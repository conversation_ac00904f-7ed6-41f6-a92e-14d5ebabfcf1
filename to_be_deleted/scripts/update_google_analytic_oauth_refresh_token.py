import os
import sys


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    import requests
    from urllib.parse import unquote
    from app.config import config
    from app.caches.report import GoogleAnalyticRefreshTokenCache
    oauth_config = config['GOOGLE_ANALYTIC_OAUTH_CONFIG']

    oauth_code_config = {
        'response_type': 'code',
        'access_type': 'offline',
        'prompt': 'consent',
        'grant_type': 'authorization_code',
    }

    # 构建授权链接
    # prompt和access_type参考https://stackoverflow.com/questions/8942340/get-refresh-token-google-api
    auth_url = f"{oauth_config['auth_uri']}?client_id={oauth_config['client_id']}&redirect_uri={oauth_config['redirect_uri']}&response_type={oauth_code_config['response_type']}&access_type={oauth_code_config['access_type']}&scope={'+'.join(oauth_config['scopes'])}&prompt={oauth_code_config['prompt']}"

    print(f'请访问以下链接进行授权：\n{auth_url}')

    auth_code = None

    while not auth_code:
        auth_code = input('请输入授权码 (code): ')
        if not auth_code:
            print('授权码不能为空，请重新输入。')

    auth_code = unquote(auth_code)
    # 打印用户输入的授权码
    print(f'你输入的授权码是：{auth_code}')

    post_body = {
        'code': auth_code,
        'client_id': oauth_config['client_id'],
        'client_secret': oauth_config['client_secret'],
        'redirect_uri': oauth_config['redirect_uri'],
        'grant_type': oauth_code_config['grant_type'],
    }
    r = requests.post(url=oauth_config['token_uri'], data=post_body)
    print(r.json())
    if refresh_token := r.json().get('refresh_token'):
        GoogleAnalyticRefreshTokenCache().set(refresh_token)
    else:
        print('get refresh_token fail!')


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
