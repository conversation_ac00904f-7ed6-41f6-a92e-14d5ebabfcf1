# -*- coding: utf-8 -*-

import os
import sys
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from itertools import chain
from typing import Dict, Tuple

import click


# select distinct table_name, column_name from information_schema.columns where table_schema = 'coinex_backend' and (column_name like '%asset%' or column_name like '%coin%');
_TABLES_WITH_ASSET = """
|abnormal_deposit_application|asset|
|abnormal_deposit_application|fee_asset|
|api_withdrawal_address|asset|
|deposit|asset|
|withdrawal|asset|
|withdrawal_address|asset|
"""


def _generate_tables(source: str, target: str):
    tables = []
    # 重新上架新市场
    # for quote in Market.TradingArea.quotes():
    #     tables.append((
    #         'market',
    #         {
    #             'name': (f'{source}{quote}', f'{target}{quote}'),
    #             'base_asset': (source, target)
    #         }
    #     ))
    for line in _TABLES_WITH_ASSET.splitlines():
        if not line:
            continue
        table, column = map(str.strip, filter(None, line.split('|')))
        tables.append((table, {column: (source, target)}))
    return tables


def _generate_count_sql(table: str, fields: Dict[str, Tuple[str, str]]) -> str:
    where = ' AND '.join(
        "`{field}` = '{old_value}'".format(field=f, old_value=o)
        for f, (o, _) in fields.items())
    return "select count(*) from `{table}` where {where};".format(
        table=table, where=where)


def _generate_patch_sql(table: str, fields: Dict[str, Tuple[str, str]]) -> str:
    update = ', '.join(
        "`{field}` = '{new_value}'".format(field=f, new_value=n)
        for f, (_, n) in fields.items())
    where = ' AND '.join(
        "`{field}` = '{old_value}'".format(field=f, old_value=o)
        for f, (o, _) in fields.items())
    return "update `{table}` set {update} where {where};".format(
        table=table, update=update, where=where)


def _update_caches(source: str, target: str):
    _ = source, target
    from app.business import update_assets, update_markets
    update_assets()
    update_markets()


@click.command()
@click.argument('source')
@click.argument('target')
@click.option('--i-know-what-i-am-doing', is_flag=True)
def main(source: str, target: str, i_know_what_i_am_doing: bool = False):
    from app.models import db
    session = db.session

    for table, fields in _generate_tables(source, target):
        count_sql = _generate_count_sql(table, fields)
        count = session.execute(count_sql).fetchone()[0]
        print(table, count)
        if count <= 0:
            continue
        patch_sql = _generate_patch_sql(table, fields)
        print('    ' + patch_sql)
        if i_know_what_i_am_doing:
            session.execute(patch_sql)
            session.commit()

    if not i_know_what_i_am_doing:
        print('Please add "--i-know-what-i-am-doing" to run it seriously.')
        return

    _update_caches(source, target)
    print(
        f'Migration done. Please run '
        f'"python deployment/scripts/convert_asset.py {source} {target} 1"'
        f' to migrate user balances.')


if __name__ == '__main__':
    from app import create_app
    app = create_app()
    with app.app_context():
        main()
