import datetime
import os
import sys

from tqdm import tqdm

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    update_asset_trade_report_schedule()
    update_daily_and_monthly_balance_report()
    update_history_asset_business_report()


def update_asset_trade_report_schedule():
    print('开始更新币种交易日报表。。。')
    update_daily_asset_trade_report()
    print('币种交易日报表更新完毕！')
    print('开始更新币种交易月报表。。。')
    update_monthly_asset_trade_report()
    print('币种交易月报表更新完毕！')
    print('---' * 20)


def update_daily_asset_trade_report():
    from app.utils import today
    from app.common import ReportType
    from app.schedules.reports.asset_business_report import update_asset_trade_report

    today_ = today()
    start = datetime.date(2022, 10, 1)
    progress_bar = tqdm(total=(today_ - start).days)
    while start < today_:
        update_asset_trade_report(start, ReportType.DAILY)
        start += datetime.timedelta(days=1)
        progress_bar.update(1)


def update_monthly_asset_trade_report():
    from app.common import ReportType
    from app.schedules.reports.asset_business_report import update_asset_trade_report
    from app.utils import this_month, next_month

    start_month = datetime.date(2022, 10, 1)
    cur_month = this_month()
    progress_bar = tqdm(total=6)

    while start_month < cur_month:
        update_asset_trade_report(start_month, ReportType.MONTHLY)
        start_month = next_month(start_month.year, start_month.month)
        progress_bar.update(1)


def update_daily_and_monthly_balance_report():
    from app.models import DailyBalanceReport
    print('开始更资产日报表。。。')
    update_balance_report(DailyBalanceReport)
    print('资产日报表更新完毕！')
    print('开始更资产月报表。。。')
    update_monthly_balance_report()
    print('资产月报表更新完毕！')
    print('---' * 20)


def update_balance_report(model):
    import copy
    from app.models.daily import db
    from pyroaring import BitMap
    print('更新中。。。')
    assets = model.query.with_entities(
        model.asset.distinct().label('asset')
    ).all()
    asset_list = [rec.asset for rec in assets]
    progress_bar = tqdm(total=len(asset_list))
    for asset in asset_list:
        history_users = set()
        records = model.query.filter(
            model.asset == asset,
            model.account_type.is_(None)
        ).order_by(model.report_date.asc()).all()
        for idx, record in enumerate(records):
            history_users_bit_map = BitMap()
            cur_uids = set(model.get_user_ids(record))
            if idx == 0:
                increase = len(cur_uids)
            else:
                increase = len(cur_uids - history_users)
            record.increase_asset_user_count = increase
            record.up_threshold_increase_asset_user_count = increase
            history_users.update(cur_uids)
            rec_history_users = copy.deepcopy(history_users)
            history_users_bit_map.update(rec_history_users)
            record.history_asset_users = history_users_bit_map.serialize()
        db.session.commit()
        progress_bar.update(1)  # 进度条


def update_monthly_balance_report():
    from app.models import MonthlyBalanceReport

    start = datetime.date(2022, 10, 1)
    _init_monthly_balance_report(start)
    update_balance_report(MonthlyBalanceReport)


def _init_monthly_balance_report(start):
    from app.models import DailyBalanceReport, MonthlyBalanceReport, db
    from app.utils import next_month
    print('初始化月报表数据。。。')
    cur_month = datetime.date(2023, 4, 1)
    while start <= cur_month:
        end = next_month(start.year, start.month)
        daily_records = DailyBalanceReport.query.filter(
            DailyBalanceReport.report_date == start).all()
        for daily_rec in daily_records:
            rec = daily_rec.to_dict()
            monthly_rec = MonthlyBalanceReport(**rec)
            monthly_rec.report_date = start
            db.session.add(monthly_rec)
        db.session.commit()
        start = end
    print('初始化月报表数据完毕！')


def update_history_asset_business_report():
    print('开始更新资产业务转换日报表。。。')
    update_history_daily_asset_business_report()
    print('资产业务转换日报表更新完毕！')
    print('开始更新资产业务转换月报表。。。')
    update_history_monthly_asset_business_report()
    print('资产业务转换月报表更新完毕！')
    print('---' * 20)


def update_history_daily_asset_business_report():
    from app.utils import today
    from app.common import ReportType
    start = datetime.date(2022, 10, 1)
    today_ = today()
    progress_bar = tqdm(total=(today_ - start).days)
    while start < today_:
        update_asset_business_report(start, ReportType.DAILY)
        progress_bar.update(1)
        start += datetime.timedelta(days=1)


def update_asset_business_report(report_date, report_type):
    from app.common import ReportType
    from app.models import MonthlyBalanceReport, \
        DailyAssetBusinessReport, DailyBalanceReport, DailyAssetTradeUserReport, \
        MonthlyAssetBusinessReport, MonthlyAssetTradeUserReport, db

    if report_type == ReportType.DAILY:
        model = DailyAssetBusinessReport
        balance_data_model = DailyBalanceReport
        trade_data_model = DailyAssetTradeUserReport
    else:
        model = MonthlyAssetBusinessReport
        balance_data_model = MonthlyBalanceReport
        trade_data_model = MonthlyAssetTradeUserReport
    balance_reports = balance_data_model.query.filter(
        balance_data_model.report_date == report_date,
        balance_data_model.account_type.is_(None),
    ).all()
    trade_reports = trade_data_model.query.filter(
        trade_data_model.report_date == report_date
    ).all()
    assets = set()
    asset_balance_increase_user_map = dict()
    up_threshold_asset_balance_increase_user_map = dict()
    for item in balance_reports:
        asset = item.asset
        assets.add(asset)
        asset_balance_increase_user_map[asset] = item.increase_asset_user_count
        up_threshold_asset_balance_increase_user_map[
            asset] = item.up_threshold_increase_asset_user_count
    increase_trade_user_map = dict()
    for item in trade_reports:
        assets.add(item.asset)
        increase_trade_user_map[item.asset] = item.increase_trade_user_count
    for asset in assets:
        record = model.query.filter(
            model.report_date == report_date,
            model.asset == asset
        ).first()
        if record:
            record.increase_trade_user_count = increase_trade_user_map.get(asset, 0)
            record.increase_asset_user_count = asset_balance_increase_user_map.get(asset,
                                                                                   0)
            record.up_threshold_increase_asset_user_count = up_threshold_asset_balance_increase_user_map.get(
                asset, 0)
    db.session.commit()


def update_history_monthly_asset_business_report():
    from app.common import ReportType
    from app.utils import this_month, next_month

    cur_month = this_month()
    start_month = datetime.date(2022, 10, 1)
    progress_bar = tqdm(total=6)

    while start_month < cur_month:
        update_asset_business_report(start_month, ReportType.MONTHLY)
        start_month = next_month(start_month.year, start_month.month)
        progress_bar.update(1)


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
