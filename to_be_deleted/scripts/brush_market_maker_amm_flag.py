import datetime
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import db

    from app.business.export.base import add_print

    @add_print
    def get_amm_markets():
        from app.caches import AmmMarketCache

        return AmmMarketCache.list_amm_markets()

    @add_print
    def brush_daily():
        from app.models import DailyMakerTradeMarketDetailReport

        model = DailyMakerTradeMarketDetailReport
        model.query.filter(
            model.report_date >= begin_date,
            model.system == model.System.SPOT,
            model.market.in_(amm_markets)
        ).update(
            {'is_amm': True},
            synchronize_session=False
        )
        db.session.commit()

    @add_print
    def brush_monthly():
        from app.models import MonthlyMakerTradeMarketDetailReport

        model = MonthlyMakerTradeMarketDetailReport
        model.query.filter(
            model.report_date >= begin_date,
            model.system == model.System.SPOT,
            model.market.in_(amm_markets)
        ).update(
            {'is_amm': True},
            synchronize_session=False
        )
        db.session.commit()

    amm_markets = get_amm_markets()
    begin_date = datetime.date(2023, 1, 1)
    brush_daily()
    brush_monthly()


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
