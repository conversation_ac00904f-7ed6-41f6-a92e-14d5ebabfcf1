import json
from copy import deepcopy

from init_base import app

from app.models import db

from app.utils import timestamp_to_date

from app.models.activity import NovicePrefectureActivity
from app.caches.activity import NoviceActivityCache


def main():
    model = NovicePrefectureActivity
    rows = model.query.filter(
        model.activity_type == model.ActivityType.PACKAGE
    )
    for idx, row in enumerate(rows):
        condition = json.loads(row.user_group_condition)
        for item in condition:
            value = item["value"]
            for i in range(1, 3):
                if isinstance(value[i], int):
                    value[i] = timestamp_to_date(value[i] / 1000 + 3600 * 8).strftime("%Y-%m-%d")
        row.user_group_condition = json.dumps(condition)
        db.session.add(row)
        if idx // 50 == 0:
            db.session.commit()
    db.session.commit()
    NoviceActivityCache.reload()


if __name__ == '__main__':
    with app.app_context():
        main()