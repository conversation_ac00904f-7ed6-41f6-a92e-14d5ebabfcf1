# -*- coding: utf-8 -*-

import os
import sys
from decimal import Decimal


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


chain = 'KDA'
asset = 'KDA'


except_address_dict = {
    "9325ad680bc666efd79e22311aad77f5465a1b32e8c9b79e90590d09edc7a232": 0,
    "6803df457850a4ed6cc55f103f088d69ca7e475f8d394ef9e56b779f5d089616": 1,
    "3a38eb174ab80a7235e2ac38de19594cca31b63502437fc14fae2c4efcecc3d2": 0,
    "da8359d5df28e7e87bb4b72d37cf57d0a424d19ddbc2aa59ec23755f9b8fe565": 1,
    "2e9ba760d7e1241fae64fb54b0ebeb354d5597f1fa33ab393ec5064e7b96c427": 0,
    "4d37cc1f5c8be41eaf3d34bea18b47394f439392b2353f98c46102d76867777e": 0,
    "23020ac77f11a638792abc5876ef28b8a060d8bb7efb25ffbc08111a9d273e43": 1,
    "c7c47b20cbff0ab5eeac26a2b7989a28088d31d51364dd7a09dab42891fae72f": 0,
    "054a493b37f7788bd273be9fdc90eea32652b4c30824831baf67adca59f82953": 3,
    "1465d211ce06614525468f0b06596e873bdf03ebac4bdda256bc3ab7e7033a0a": 0,
    "a82ed859614c3d92fc38fbbd02af776334e88b73a1b556fc4a9f59a3b1c02a20": 0,
    "d0ee6cc0c34385a122024daa659ddda75507f900a3030c81090aeee78b42e998": 0,
    "fda5dd5b3ba5b7195d94f0bd830853dd9b8f396c6ec29c9f0e17550c368c0e07": 7,
    "8a5831026eb6bc8570b15c091d193f3f9d6ac72ad36fbd54e7d0f67dfd8b2eb4": 7,
    "1ec274423ea088e6bf591e145b4a59981db8bcbf55dec1ae7ccb869591dd60e9": 7,
    "e833736e6ee0c914b62189d79be000b4685f8033681bc1c38959a4b97a41a874": 7,
    "623df0db279b7ae14001ca44f024ed41cfe9b061deb9b27fd2e5e695ab01e2bf": 7,
    "4319938a84dd1b41a7b5a3f84307acae746993dccf1681cc1b2448575306926c": 0,
    "83683272543782b9e91a250afae597a2d58ee081e6319d6e76eed8c4184b1491": 2,
    "d7c6aa1d88176f670f4993f0a4af74ca96b0f34029df42e0ae710214ca9d96e8": 0,
    "4279735cd449c05444cf7efb1522072e705b0fbe635c714aa3a912fb328842d4": 1
}


def main():
    from app.models import Deposit
    import json
    total_amount_dict = {}
    for address, chain_id in except_address_dict.items():
        dep: Deposit
        for dep in Deposit.query.filter(
                Deposit.chain == chain,
                Deposit.asset == asset,
                Deposit.type.in_([Deposit.Type.ON_CHAIN,
                                  Deposit.Type.ABNORMAL]),
                Deposit.address == address):
            attachment = dep.attachment
            if attachment is None:
                continue

            attachment = json.loads(attachment)
            to_chain_id = attachment['to_chain_id']
            if str(to_chain_id) == str(chain_id):
                amount = dep.amount
                user_id = dep.user_id
                if user_id not in total_amount_dict:
                    total_amount_dict[user_id] = Decimal()
                total_amount_dict[user_id] += amount
                print(f'{address} {chain_id} {user_id} {amount} {dep.tx_id}')
    print(f'total_amount_dict:{total_amount_dict} total_amount:{sum(total_amount_dict.values())}')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()


