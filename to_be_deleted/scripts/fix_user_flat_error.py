# -*- coding: utf-8 -*-
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def update_balance(user_id, asset, amount):
    from app.business import ServerClient
    from app.common import BalanceBusiness
    from app.models import BalanceUpdateBusiness

    c = ServerClient()
    c.add_user_balance(
        user_id=user_id,
        asset=asset,
        amount=amount,
        business=BalanceBusiness.SYSTEM,
        business_id=BalanceUpdateBusiness.new_id(user_id, asset, amount),
        detail={'remark': 'fix user flat error'}
    )


def query_user_flat_error_record(user_id, load_order_ids):
    from app.models import MarginFlatHistory

    unfinished_orders = MarginFlatHistory.query.filter(
        MarginFlatHistory.user_id == user_id,
        MarginFlatHistory.margin_loan_order_id.in_(load_order_ids),
        MarginFlatHistory.status == MarginFlatHistory.StatusType.DEDUCTED,
    ).all()
    return unfinished_orders


def fix_user_flat_error(user_id, loan_order_ids):
    records = query_user_flat_error_record(user_id, loan_order_ids)

    from app.models import db
    for record in records:
        asset = record.asset
        amount = record.amount
        print(f'fix flat error {user_id} {asset} {amount}')
        db.session.delete(record)
        # remove redundancy data
        db.session.commit()
        # add balance
        update_balance(user_id, asset, amount)


def main():
    fix_user_flat_error(2172096, [12866635, ])
    fix_user_flat_error(4534701, [13175389, ])


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
