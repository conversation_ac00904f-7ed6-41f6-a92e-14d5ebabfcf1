# -*- coding: utf-8 -*-

"""
This script simply parses all the (selected) *.po files in directory
==> app/translations
and generates a json file which looks like:
{
    "Error, latest price Error":{
        "en_US":"Error, latest price Error",
        "zh_Hans_CN":"抱歉，最新价格获取出现错误",
        "ko_KP":"물가지수를 액세스할 수 없습니다."
    },
    "Sorry, No latest price Received":{
        "en_US":"Sorry, No latest price Received",
        "zh_Hans_CN":"抱歉，无法获取最新价格",
        "ko_KP":"물가지수를 액세스할 수 없습니다."
    },
    "Future flat error":{
        "en_US":"Future flat error",
        "zh_Hans_CN":"抱歉，期货还币出现错误",
        "ko_KP":""
    }
}

possible parameter(s)
==> language(s)
    -> example:
        python deployment/scripts/read_po_file.py en_US id_ID

if no parameter, then all the languages listed in the directory mentioned above will be parsed.
"""
import os
import sys


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


import json
from babel.messages.pofile import read_po

TRANSLATION_DIR = os.path.join('app', 'translations')


def main():
    langs = sys.argv[1:]
    available_langs = list(filter(
        lambda x: os.path.isdir(os.path.join(TRANSLATION_DIR, x)), os.listdir(TRANSLATION_DIR)))
    if langs:
        for lang in langs:
            if lang not in available_langs:
                exit('Language {} does not exist, available options are {}'.format(lang, ', '.join(available_langs)))
    else:
        langs = available_langs

    # to get an English id->msg map as reference
    with open(os.path.join(TRANSLATION_DIR, 'en_US', 'LC_MESSAGES', 'messages.po'), 'rt') as f:
        catalog = read_po(f)
        eng_map = {msg.id: msg.string for msg in catalog if msg}

    # to get a Chinese id->msg map as reference
    with open(os.path.join(TRANSLATION_DIR, 'zh_Hans_CN', 'LC_MESSAGES', 'messages.po'), 'rt') as f:
        catalog = read_po(f)
        chn_map = {msg.id: msg.string for msg in catalog if msg}

    for lang in langs:
        res = dict()
        with open(os.path.join(TRANSLATION_DIR, f'{lang}', 'LC_MESSAGES', 'messages.po'), 'rt') as f:
            catalog = read_po(f)
            for msg in catalog:
                # 只包含未被翻译的
                if msg and msg.id and not msg.string:
                    eng = eng_map[msg.id] if msg.id in eng_map and eng_map[msg.id] else msg.id
                    assert eng
                    res[msg.id] = {
                        "en_US": eng,
                        "zh_Hans_CN": chn_map[msg.id] if msg.id in chn_map else "",
                        lang: msg.string
                    }
            with open(os.path.join(TRANSLATION_DIR, f'{lang}.json'), 'wt') as json_file:
                json.dump(res,
                          json_file, ensure_ascii=False, indent=4)


if __name__ == '__main__':
    main()

