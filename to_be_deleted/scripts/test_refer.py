import datetime
import os
import sys

import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())

from dateutil.relativedelta import relativedelta
from app.utils import today, today_timestamp_utc, last_month
from app.schedules.reports.user_report import update_daily_users_list_schedule
from app.business.report.refer import DailyNormalReferralReporter, \
    DailyUserNormalReferStatistics, MonthNormalReferralReporter, \
    DailyNormalReferStatisticAggregate
from app.models import ReferralHistory
from app.schedules.wallet.assets import sync_assets
from app.schedules.reports.spot_trade import update_daily_spot_trade_report
from app.schedules.perpetual import update_daily_perpetual_data_schedule, \
    update_daily_perpetual_trade_report
from app.schedules.referral import update_user_first_normal_refer_time_cache

history_end_date = today()
yesterday = history_end_date - datetime.timedelta(days=1)
history_report_st = datetime.date(2022, 1, 1)


def flush_history_day_report(_class, st, is_month=False):
    while st < history_end_date:
        if is_month:
            tmp_et = st + relativedelta(months=1)
        else:
            tmp_et = st + datetime.timedelta(days=1)
        _class().run(st, tmp_et)
        st = tmp_et


def flush_today():
    flush_history_day_report(DailyNormalReferralReporter, yesterday)
    _last_month = last_month(yesterday.year, yesterday.month)
    flush_history_day_report(MonthNormalReferralReporter, _last_month, is_month=True)
    flush_history_day_report(DailyUserNormalReferStatistics, yesterday)
    flush_history_day_report(DailyNormalReferStatisticAggregate, yesterday)


def flush_history():
    first_refer = ReferralHistory.query.order_by(
        ReferralHistory.created_at
    ).first()
    first_date = first_refer.created_at.date()
    flush_history_day_report(DailyNormalReferralReporter, history_report_st)
    flush_history_day_report(MonthNormalReferralReporter, history_report_st, is_month=True)
    flush_history_day_report(DailyUserNormalReferStatistics, first_date)
    flush_history_day_report(DailyNormalReferStatisticAggregate, yesterday)


@click.command()
@click.argument('tag', type=click.Choice(['today', 'history']), required=True)
def main(tag):
    update_user_first_normal_refer_time_cache()
    print(tag)
    if tag == "today":
        flush_today()
    else:
        flush_history()


if __name__ == '__main__':
    from app import create_app
    app = create_app()

    with app.app_context():
        main()
        # update_daily_spot_trade_report(yesterday)
        # update_daily_perpetual_trade_report(yesterday)
        # sync_assets()
        # update_daily_users_list_schedule()


