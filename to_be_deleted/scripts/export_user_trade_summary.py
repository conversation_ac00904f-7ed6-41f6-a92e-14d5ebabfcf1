import os
import sys

import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from collections import defaultdict
from decimal import Decimal
from datetime import date

from app.models import User, AssetPrice, SubAccount
from app.utils import amount_to_str, ExcelExporter, \
    upload_file, now, str_to_datetime


def get_users_data(email_list):
    query = User.query.filter(User.email.in_(email_list))
    return {r.id: r.email for r in query}


def get_sub_map(master_user_ids):
    sub_users = SubAccount.query.filter(
        SubAccount.main_user_id.in_(master_user_ids),
    )
    return {r.user_id: r.main_user_id for r in sub_users}


def get_user_trade_data(master_user_ids, trade_month):
    from app.business import TradeSummaryDB
    coin_rate_map = {}
    sub_map = get_sub_map(master_user_ids)
    all_user_ids = master_user_ids + list(sub_map.keys())

    spot_results = _get_trade_data(all_user_ids, trade_month, TradeSummaryDB.db)
    spot_fee_results = _get_trade_free_data(all_user_ids, trade_month, TradeSummaryDB.db)

    deal_volume_dict = defaultdict(Decimal)
    taker_volume_dict = defaultdict(Decimal)
    maker_volume_dict = defaultdict(Decimal)
    fee_volume_dict = defaultdict(Decimal)
    for item in spot_results:
        user_id, trade_date, money_asset, deal_volume, taker_volume, maker_volume = item
        if trade_date not in coin_rate_map:
            coin_rate_map[trade_date] = AssetPrice.get_price_map(trade_date)
        if user_id in sub_map:
            user_id = sub_map[user_id]
        coin_rate = Decimal(coin_rate_map[trade_date].get(money_asset, '0'))
        deal_volume_dict[user_id] += coin_rate * deal_volume
        taker_volume_dict[user_id] += coin_rate * taker_volume
        maker_volume_dict[user_id] += coin_rate * maker_volume

    for item in spot_fee_results:
        user_id, trade_date, asset, fee = item
        if user_id in sub_map:
            user_id = sub_map[user_id]
        coin_rate = Decimal(coin_rate_map[trade_date].get(asset, '0'))
        fee_volume_dict[user_id] += coin_rate * fee

    return deal_volume_dict, taker_volume_dict, maker_volume_dict, fee_volume_dict


def _get_trade_free_data(user_ids, trade_month, db):
    cursor = db.cursor()
    sql = "SELECT user_id, trade_date, asset, SUM(fee) FROM `user_fee_summary_{}` " \
          "WHERE user_id in {} GROUP BY user_id, trade_date, asset;".format(
        trade_month.strftime("%Y%m"), tuple(user_ids))

    cursor.execute(sql)
    return cursor.fetchall()


def _get_trade_data(user_ids, trade_month, db):
    cursor = db.cursor()
    sql = "SELECT user_id, trade_date, money_asset, SUM(deal_volume), SUM(taker_volume), SUM(maker_volume) FROM `user_trade_summary_{}` " \
          "WHERE user_id in {} GROUP BY user_id, trade_date, money_asset;".format(
        trade_month.strftime("%Y%m"), tuple(user_ids))
    cursor.execute(sql)
    return cursor.fetchall()

@click.command()
@click.argument('search_date', type=str)
def main(search_date):
    search_date = str_to_datetime(search_date)
    email_list = ['<EMAIL>', '<EMAIL>', '<EMAIL>',
                  '<EMAIL>', '<EMAIL>',
                  '<EMAIL>', '<EMAIL>',
                  '<EMAIL>', '<EMAIL>',
                  '<EMAIL>', '<EMAIL>',
                  '<EMAIL>'
                  ]
    trade_month = search_date.date()
    users_data = get_users_data(email_list)
    master_user_ids = list(users_data.keys())
    deal_volume_dict, taker_volume_dict, maker_volume_dict, fee_volume_dict = get_user_trade_data(
        master_user_ids, trade_month)
    user_data_dict = defaultdict(dict)
    for user_id, deal_volume in deal_volume_dict.items():
        user_data_dict[user_id]['deal_volume'] = deal_volume
    for user_id, taker_volume in taker_volume_dict.items():
        user_data_dict[user_id]['taker_volume'] = taker_volume
    for user_id, maker_volume in maker_volume_dict.items():
        user_data_dict[user_id]['maker_volume'] = maker_volume
    for user_id, fee in fee_volume_dict.items():
        user_data_dict[user_id]['fee'] = fee
    result_list = []
    for user_id, data in user_data_dict.items():
        data['email'] = users_data[user_id]
        data['deal_volume'] = amount_to_str(data['deal_volume'], 2)
        data['maker_volume'] = amount_to_str(data['maker_volume'], 2)
        data['taker_volume'] = amount_to_str(data['taker_volume'], 2)
        data['fee'] = amount_to_str(data['fee'], 2)
        result_list.append(data)
    
    header_data = [
        ('email', '邮箱'),
        ('deal_volume', f'{trade_month.year}年{trade_month.month}月成交额（USD）'),
        ('maker_volume', 'Maker交易额（USD）'),
        ('taker_volume', 'Taker交易额（USD）'),
        ('fee', '产生手续费'),
    ]

    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]

    streams = ExcelExporter(
        data_list=result_list, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
