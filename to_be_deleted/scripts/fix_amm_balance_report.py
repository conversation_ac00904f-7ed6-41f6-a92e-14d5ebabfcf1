import datetime
import os
import sys

from dateutil.relativedelta import relativedelta
from tqdm import tqdm


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    update_daily_balance_report()
    print('日报表更新完毕')
    update_monthly_balance_report()
    print('月报表更新完毕')


def update_daily_balance_report():
    from app.schedules.reports.daily_balance import _generate, _get_threshold_map
    from app.common import AccountBalanceType
    from app.models import db, AssetPrice, DailyBalanceReport as model

    min_amount_map = _get_threshold_map()
    end_date = datetime.datetime.utcnow().date()
    report_date = datetime.date(2023, 4, 28)
    total = (end_date - report_date).days
    process_bar = tqdm(total=total)
    while report_date <= end_date:
        price_map = AssetPrice.get_close_price_map(report_date)

        items = get_amm_records(report_date, price_map)
        _generate(items, AccountBalanceType.AMM, min_amount_map, price_map,
                  report_date, model)
        db.session.commit()
        report_date += datetime.timedelta(days=1)
        process_bar.update(1)


def update_monthly_balance_report():
    from app.schedules.reports.daily_balance import _generate, _get_threshold_map
    from app.common import AccountBalanceType
    from app.models import db, AssetPrice, MonthlyBalanceReport as model
    from app.utils import this_month

    min_amount_map = _get_threshold_map()
    report_date = datetime.date(2023, 5, 1)
    end_date = this_month()
    total = int((end_date - report_date).days/30)
    process_bar = tqdm(total=total)
    while report_date <= end_date:
        price_map = AssetPrice.get_close_price_map(report_date)
        items = get_amm_records(report_date, price_map)
        _generate(items, AccountBalanceType.AMM, min_amount_map, price_map,
                  report_date, model)
        db.session.commit()
        report_date += relativedelta(months=1)
        process_bar.update(1)


def get_amm_records(report_date, price_map):
    from app.models import UserLiquiditySlice, Market
    markets = Market.query.with_entities(
        Market.name,
        Market.base_asset,
        Market.quote_asset
    ).all()
    market_dic = dict(map(lambda item: (
        item.name, {'base_asset': item.base_asset, 'quote_asset': item.quote_asset}), markets))
    records = UserLiquiditySlice.query.filter(
        UserLiquiditySlice.date == report_date
    ).all()
    res = []
    for record in records:
        user_id = record.user_id
        market = record.market
        liquidity_usd = record.liquidity_usd
        base_asset, quote_asset = market_dic[market]['base_asset'], market_dic[market]['quote_asset']
        base_rate = price_map.get(base_asset, 0)
        quote_rate = price_map.get(quote_asset, 0)
        base_amount = liquidity_usd / 2 / base_rate if base_rate else 0
        quote_amount = liquidity_usd / 2 / quote_rate if quote_rate else 0
        res.extend([(base_amount, user_id, base_asset), (quote_amount, user_id, quote_asset)])
    return res


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
