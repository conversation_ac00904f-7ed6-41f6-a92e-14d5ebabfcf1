# generate test env config
import os
from pathlib import Path

origin_supervisor_config = """
[program:coinex_backend]
command         = bash run.sh
directory       = /var/www/coinex_backend
autostart       = true
autorestart     = true
stdout_logfile  = /var/log/coinex_backend/server.log
stderr_logfile  = /var/log/coinex_backend/server_error.log
redirect_stderr = true
stopasgroup = true
"""

origin_nginx_config = """
server {{
        listen 80;
        server_name {server_name} localhost;
        access_log /var/log/coinex_backend/www_access.log;	
        error_log /var/log/coinex_backend/www_error.log;
        keepalive_timeout 5;
        client_max_body_size 16M;
        underscores_in_headers on;

        location /res/ {{
            include uwsgi_params;
            uwsgi_pass unix:/var/www/coinex_backend/uwsgi.sock;
            add_header Cache-Control no-cache;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods *;
            add_header Access-Control-Allow-Headers *;
            add_header Access-Control-Expose-Headers 'Content-Length,Content-Range';
            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host $host;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}

        location /v1/ {{
            include uwsgi_params;
            uwsgi_pass unix:/var/www/coinex_backend/uwsgi.sock;
            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;
            uwsgi_param Host $host;
            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}

        location /internal/ {{
            include uwsgi_params;
            uwsgi_pass unix:/var/www/coinex_backend/uwsgi.sock;
            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;
            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host $host;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}

        location /swaggerui/ {{
            include uwsgi_params;
            uwsgi_pass unix:/var/www/coinex_backend/uwsgi.sock;

            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;

            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host $host;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}
}}
"""

origin_nginx_admin_config = """
server {{
        listen 80;
        server_name  {admin_server_name};

        access_log /var/log/coinex_admin_frontend/www_access.log;
        error_log /var/log/coinex_admin_frontend/www_error.log;
        keepalive_timeout 5;
        client_max_body_size 16M;
        
        location / {{
            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;
            root /var/www/coinex_admin_frontend/dist/;
        }}
        
       location /api/ {{
            rewrite ^/api/(.*)$ /admin/$1 break;
            include uwsgi_params;
            uwsgi_pass unix:/var/www/coinex_backend/uwsgi.sock;

            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;

            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host $host;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;}}
            
       location /res/ {{
            include uwsgi_params;
            uwsgi_pass unix:/var/www/coinex_backend/uwsgi.sock;

            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;

            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host $host;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}
}}
"""

backend_nginx_config = """
server {{
        listen 80;
        server_name {clone_back_host};

        access_log {clone_back_log_path}/www_access.log;
        error_log {clone_back_log_path}/www_error.log;
        keepalive_timeout 5;
        client_max_body_size 16M;
        underscores_in_headers on;

        location /res/ {{
            include uwsgi_params;
            uwsgi_pass unix:/{clone_back_path}/uwsgi.sock;
            add_header Cache-Control no-cache;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods *;
            add_header Access-Control-Allow-Headers *;
            add_header Access-Control-Expose-Headers 'Content-Length,Content-Range';

            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host $host;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}

        location /v1/ {{
            include uwsgi_params;
            uwsgi_pass unix:/{clone_back_path}/uwsgi.sock;

            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;

            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host "{clone_back_host}";
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}

        location /internal/ {{
            include uwsgi_params;
            uwsgi_pass unix:/{clone_back_path}/uwsgi.sock;

            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;

            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host $host;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}

        location /swaggerui/ {{
            include uwsgi_params;
            uwsgi_pass unix:/{clone_back_path}/uwsgi.sock;

            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;

            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host $host;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}
}}
"""

frontend_nginx_config = """
server {{
        listen 80;
        server_name {admin_server_name};

        access_log {clone_admin_log_path}/www_access.log;
        error_log {clone_admin_log_path}/www_error.log;
        keepalive_timeout 5;
        client_max_body_size 16M;
        # underscores_in_headers on;
        # server_name_in_redirect off;
        # proxy_intercept_errors on;

        location / {{
            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;
            root {clone_admin_path}/dist/;
        }}

        location /api/ {{
            rewrite ^/api/(.*)$ /admin/$1 break;
            include uwsgi_params;
            uwsgi_pass unix:/{clone_back_path}/uwsgi.sock;

            add_header Cache-Control no-cache;
            add_header X-Frame-Options SAMEORIGIN;
            add_header X-Xss-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=86400" always;

            uwsgi_param X-Forwarded-For $proxy_add_x_forwarded_for;
            uwsgi_param Host $host;
            uwsgi_param X-Real-IP $remote_addr;
            uwsgi_param X-Scheme $scheme;
        }}
}}
"""

supervisor_config = """
[program:{clone_supervisor_name}]
command         = bash test.sh
directory       = {clone_back_path}
autostart       = true
autorestart     = true
stdout_logfile  = {clone_back_log_path}/server.log
stderr_logfile  = {clone_back_log_path}/server_error.log
redirect_stderr = true
stopasgroup = true
"""

test_bash_config = """
#!/bin/bash

cd "$(dirname "$0")" || exit 1

VENV_DIR=venv
PID_FILE=uwsgi.pid


function error() {
    echo -e "\e[01;31m$1\e[0m"
    exit 1
}

if [[ ! -d "${VENV_DIR}" ]]; then
    error "Virtual environment does not exist; please run `bash setup.sh` first"
fi

function on_exit {
    if [[ -f ${PID_FILE} ]]; then
        uwsgi --stop ${PID_FILE}
        wait "$(cat ${PID_FILE})"
        rm ${PID_FILE}
    fi
    echo "Bye bye"
    exit 0
}

trap on_exit EXIT

source ${VENV_DIR}/bin/activate && uwsgi \
    --venv ${VENV_DIR} \
    --pidfile ${PID_FILE} \
    --processes 32 \
    --gevent 1000 \
    --wsgi-disable-file-wrapper \
    --log-format "[%%(ltime)] %%(addr) (%%(user)) %%(method) %%(uri) => %%(status) (%%(rsize) B 
    in %%(msecs) ms)" \
    --socket %(socket_name)s/uwsgi.sock \
    --chmod-socket=777 \
    --wsgi run:app
"""

temp_init_bash = """
#!/usr/bin/env bash
BACK_PATH=/var/www/coinex_backend_{server_name_num}/
mkdir -p ${{BACK_PATH}}
cd ${{BACK_PATH}}
git clone git@coinex_backend:viabtc/coinex_backend.git
git clone git@coinex_admin_frontend:viabtc/coinex_admin_frontend.git
cd coinex_backend
virtualenv --python=python3.11 venv
source venv/bin/activate && pip install cffi && pip install 
git+https://github.com/versionzhang/secp256k1-py
ln -s app/config/testing.py app/config/testing_release.py
cp /var/www/coinex_backend/app/config/environment.py app/config
curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11
source venv/bin/activate && pip install -r requirements.txt
cd ../coinex_admin_frontend
npm install
npm run generate
"""


def init_git_repo(clone_env_num, web_num):
    server_name_num = web_num + clone_env_num * 3
    s = temp_init_bash.format(server_name_num=server_name_num)
    p = Path("./init_clone_env.sh")
    p.write_text(s, encoding='utf-8')
    os.system("bash init_clone_env.sh")


def write_origin_env_config(server_name, admin_server_name):
    nginx_path = "/etc/nginx/sites-enabled/www.coinex.com"
    admin_nginx_path = "/etc/nginx/sites-enabled/admin.coinex.com"
    supervisor_path = f'/etc/supervisor/conf.d/coinex_backend.conf'
    os.system("mkdir -p /var/log/coinex_backend")
    path = Path(nginx_path)
    path.write_text(origin_nginx_config.format(server_name=server_name), encoding='utf-8')
    path = Path(admin_nginx_path)
    path.write_text(origin_nginx_admin_config.format(admin_server_name=admin_server_name),
                    encoding='utf-8')
    path = Path(supervisor_path)
    path.write_text(origin_supervisor_config, encoding="utf-8")


def generate_and_copy_configs(clone_env_num, web_num):
    server_name_num = web_num + clone_env_num * 3
    clone_supervisor_name = f"coinex_backend_{server_name_num}"
    clone_admin_name = f"coinex_admin_frontend_{server_name_num}"
    clone_back_host = f"test{server_name_num}.coinex.com"
    admin_server_name = f"testadmin{server_name_num}.coinex.com"
    clone_admin_log_path = f"/var/log/{clone_admin_name}"
    clone_back_log_path = f"/var/log/{clone_supervisor_name}"
    clone_back_path = f"/var/www/coinex_backend_{server_name_num}/coinex_backend"
    clone_admin_path = f"/var/www/coinex_backend_{server_name_num}/coinex_admin_frontend"
    os.system(f"mkdir -p {clone_back_log_path}")
    os.system(f"mkdir -p {clone_admin_log_path}")
    supervisor_config_string = supervisor_config.format(
        clone_back_log_path=clone_back_log_path,
        clone_back_path=clone_back_path,
        clone_supervisor_name=clone_supervisor_name
    )
    back_nginx_config_string = backend_nginx_config.format(
        clone_back_host=clone_back_host,
        clone_back_log_path=clone_back_log_path,
        clone_supervisor_name=clone_supervisor_name,
        clone_back_path=clone_back_path
    )
    admin_nginx_config_string = frontend_nginx_config.format(
        clone_back_path=clone_back_path,
        admin_server_name=admin_server_name,
        clone_admin_log_path=clone_admin_log_path,
        clone_admin_path=clone_admin_path,
        clone_supervisor_name=clone_supervisor_name
    )
    test_bash_string = test_bash_config % dict(socket_name=clone_back_path)
    supervisor_path = f'/etc/supervisor/conf.d/{clone_supervisor_name}.conf'
    back_nginx_path = f'/etc/nginx/sites-enabled/{clone_back_host}'
    front_nginx_path = f'/etc/nginx/sites-enabled/{admin_server_name}'
    bash_path = f'{clone_back_path}/test.sh'

    path = Path(supervisor_path)
    path.write_text(supervisor_config_string, encoding='utf-8')
    path = Path(back_nginx_path)
    path.write_text(back_nginx_config_string, encoding='utf-8')
    path = Path(front_nginx_path)
    path.write_text(admin_nginx_config_string, encoding='utf-8')
    path = Path(bash_path)
    path.write_text(test_bash_string, encoding='utf-8')
    alias = f"""
alias tcbf='tail -f /var/log/coinex_backend/server.log'
alias cb='cd /var/www/coinex_backend && source venv/bin/activate'    
alias cbs='cd /var/www/coinex_backend && source venv/bin/activate && python manage.py shell'
    
alias cb{server_name_num}='cd /var/www/coinex_backend_{server_name_num}/coinex_backend && source 
venv/bin/activate'
alias cbs{server_name_num}='cd /var/www/coinex_backend_{server_name_num}/coinex_backend && source 
venv/bin/activate && python manage.py shell'
alias tcbf{server_name_num}='tail -f /var/log/coinex_backend_{server_name_num}/server.log'
"""
    print(alias)
