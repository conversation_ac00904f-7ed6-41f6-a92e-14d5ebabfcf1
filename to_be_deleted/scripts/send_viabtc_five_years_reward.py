# -*- coding: utf-8 -*-
import json
import os
import sys
from datetime import datetime

import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

CET_AMOUNT = 50

VIP_LEVEL = 3

VIP_MONTHS = 3

# reward_users = """
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# V<PERSON><PERSON><PERSON><PERSON>@gmail.com
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# """
reward_users = """
<EMAIL>
<EMAIL>
"""


def read_data(data: str):
    for line in data.splitlines():
        line = line.strip()
        if not line:
            continue
        yield line


def send_vip(run):
    from app.models import User
    from app.utils.date_ import date_to_datetime
    from app.business.vip import VipHelper
    from dateutil.relativedelta import relativedelta
    expired_time = datetime.today() + relativedelta(months=VIP_MONTHS)
    for email in read_data(reward_users):
        user = User.query.filter(User.email == email).with_entities(User.id).first()
        if not user:
            print(f"not find user by email : {email}")
            continue
        if run:
            VipHelper.add_lock_level(user.id, VIP_LEVEL, date_to_datetime(expired_time))


def send_cet(run):
    from app.models import GiftHistory, Activity, db
    from app.common import BalanceBusiness
    from app.business.gift import update_gift_history_task
    for email in read_data(reward_users):
        from app.models import User
        user = User.query.filter(User.email == email).with_entities(User.id).first()
        if not user:
            print(f"not find user by email : {email}")
            continue
        db.session.add(GiftHistory(
            user_id=user.id,
            activity_id=Activity.SYSTEM_REWORD_ID,
            asset='CET',
            amount=CET_AMOUNT,
            remark='viabtc five years activity reward',
            status=GiftHistory.Status.CREATED
        ))
    if run:
        db.session.commit()
        update_gift_history_task.delay(Activity.SYSTEM_REWORD_ID, BalanceBusiness.SYSTEM.value)


@click.command()
@click.option('--i-know-what-i-am-doing', is_flag=True)
def main(i_know_what_i_am_doing):
    send_cet(i_know_what_i_am_doing)
    send_vip(i_know_what_i_am_doing)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
