import datetime
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    import datetime
    import json
    from datetime import timedelta
    from collections import defaultdict
    from app.models import db
    from app.business import PerpetualLogDB
    from app.utils.date_ import date_to_datetime, next_month
    from app.models import DailyPerpetualMarketReport, MonthlyPerpetualMarketReport

    from app.business.export.base import add_print

    @add_print
    def brush_daily(report_date):
        end_date = report_date + timedelta(days=1)
        ts = int(date_to_datetime(end_date).timestamp())
        rows = PerpetualLogDB.get_positions(
            *['user_id', 'market'],
            group_by='user_id, market',
            timestamp=ts
        )
        pos_group_by_market = defaultdict(set)
        pos_all = set()
        for user_id, market in rows:
            pos_all.add(user_id)
            pos_group_by_market[market].add(user_id)
        pos_group_by_market['ALL'] = pos_all

        daily_rows = DailyPerpetualMarketReport.query.filter(
            DailyPerpetualMarketReport.report_date == report_date
        ).all()
        for daily_row in daily_rows:
            market = daily_row.market
            daily_row.active_user_count = len(
                pos_group_by_market[market] | set(json.loads(daily_row.deal_user_list))
            )

        db.session.commit()

    @add_print
    def brush_monthly(report_date):
        end_date = next_month(report_date.year, report_date.month)
        date_ = min([datetime.date.today(), end_date])
        ts = int(date_to_datetime(date_).timestamp())
        rows = PerpetualLogDB.get_positions(
            *['user_id', 'market'],
            group_by='user_id, market',
            timestamp=ts
        )
        pos_group_by_market = defaultdict(set)
        pos_all = set()
        for user_id, market in rows:
            pos_all.add(user_id)
            pos_group_by_market[market].add(user_id)
        pos_group_by_market['ALL'] = pos_all

        monthly_rows = MonthlyPerpetualMarketReport.query.filter(
            MonthlyPerpetualMarketReport.report_date == report_date
        ).all()
        for monthly_row in monthly_rows:
            market = monthly_row.market
            monthly_row.active_user_count = len(
                pos_group_by_market[market] | set(json.loads(monthly_row.deal_user_list))
            )

        db.session.commit()

    begin, end = datetime.date(2023, 1, 1), datetime.date.today()
    cur = begin
    while cur < end:
        print(f'daily {cur}...\n')
        try:
            brush_daily(cur)
        except:
            print(f'brush_daily err: {cur}')
            pass
        cur += timedelta(days=1)

    end_month = end.replace(day=1)
    cur_month = begin.replace(day=1)
    while cur_month <= end_month:
        print(f'monthly {cur_month}...\n')
        try:
            brush_monthly(cur_month)
        except:
            print(f'brush_monthly err: {cur_month}')
            pass
        cur_month = next_month(cur_month.year, cur_month.month)


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
