# -*- coding: utf-8 -*-
import ast
import datetime
import time

import requests
from dateutil.tz import UTC
from tqdm import tqdm
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal

from dateutil.relativedelta import relativedelta
from pymysql.err import ProgrammingError

from app.assets import list_all_assets
from app.caches import MarketCache
from app.caches.kline import AssetKlineCache
from app.models import (
    PeriodType, AssetPriceKline, db, AssetPrice, CoinInformation, Market,
    AdminPermission,
)
from app.utils import now, batch_iter, current_timestamp, timestamp_to_datetime
from app.business.external_dbs import TradeLogDB
from app.business import ServerClient


def get_every_market_earliest_date():
    first_month = date(year=2017, month=12, day=1)
    current_month = now().date()
    start_month = first_month
    ts = int(now().timestamp())
    result = defaultdict(lambda: ts)
    online_markets = MarketCache.list_online_markets()
    while start_month < current_month:
        month_str = start_month.strftime("%Y%m")
        try:
            for market in online_markets:
                record = TradeLogDB.table(f'kline_history_{month_str}').select(
                    'market', '`timestamp`',
                    where=f"`market`='{market}' and t=3",
                    limit=1,
                    order_by="timestamp asc"
                )
                if len(record) > 0:
                    result[market] = min(result[market], record[0][1])
            start_month = start_month + relativedelta(months=1)
        except ProgrammingError:
            start_month = start_month + relativedelta(months=1)
            continue
    return result


def get_every_asset_earliest_date():
    markets_data = get_every_market_earliest_date()
    current_ts = int(now().timestamp())
    result = defaultdict(lambda: {'ts': current_ts, 'market': ''})
    for market, ts in markets_data.items():
        asset = MarketCache(market).dict['base_asset']
        quote_asset = MarketCache(market).dict['quote_asset']
        if result[asset]['ts'] > ts and quote_asset == 'USDT':
            result[asset] = dict(ts=ts, market=market)
    final_result = {asset: d for asset, d in result.items() if d['market'] != ''}
    return final_result


def insert_kline_records():
    asset_result = get_every_asset_earliest_date()
    settings = [
        (86400 * 365 * 10, 86400, PeriodType.DAY),
        (86400 * 30, 3600, PeriodType.HOUR),
        (86400, 60, PeriodType.MINUTE)
    ]
    ts = int(now().timestamp())
    current_ts = ts - ts % 60
    c = ServerClient()
    records = []
    progress_bar = tqdm(total=len(asset_result))
    for asset, asset_data in asset_result.items():
        progress_bar.update(1)
        asset_first_ts = asset_data["ts"]
        market = asset_data["market"]
        for setting in settings:
            period = setting[1]
            boundary = setting[0]
            start_ts = asset_first_ts - asset_first_ts % period
            while start_ts < current_ts:
                delta_ts = current_ts - start_ts
                if delta_ts <= boundary:
                    end_ts = start_ts + period
                    r = c.market_kline(market=market, start_time=start_ts, end_time=end_ts,
                                       interval=period)
                    if len(r) == 0:
                        start_ts += period
                        continue
                    # 收盘价
                    price = Decimal(r[0][2])
                    if period == 86400:
                        types = [PeriodType.DAY]
                    elif period == 3600:
                        if end_ts % 86400 == 0:
                            types = [PeriodType.DAY, PeriodType.HOUR]
                        else:
                            types = [PeriodType.HOUR]
                    elif period == 60:
                        if end_ts % 86400 == 0:
                            types = [PeriodType.DAY, PeriodType.HOUR, PeriodType.MINUTE]
                        elif end_ts % 3600 == 0:
                            types = [PeriodType.HOUR, PeriodType.MINUTE]
                        else:
                            types = [PeriodType.MINUTE]
                    else:
                        types = []
                    for p_type in types:
                        records.append(AssetPriceKline(
                            asset=asset,
                            period=p_type,
                            time=end_ts,
                            price=price
                        ))
                start_ts += period
            progress_bar.write(f"process {asset} with setting {setting}")
    progress_bar.close()
    for objs in batch_iter(records, 5000):
        db.session.bulk_save_objects(objs)
    db.session.commit()
    for record in records:
        cache = AssetKlineCache(record.asset, record.period)
        cache.save_to_cache(record)


# https://binance-docs.github.io/apidocs/spot/cn/#k
# get history kline from binance
# first GET ALL MARKET INFO
def get_can_recover_data_from_binance():
    result = get_every_asset_earliest_date()
    market_api_url = 'https://api1.binance.com/api/v3/exchangeInfo'
    r = requests.get(market_api_url)
    data = r.json()
    earliest_ts = 1420041600  # 2015.1.1
    assets_map = {v['baseAsset']: v['symbol'] for v in data['symbols']
                  if v['quoteAsset'] == 'USDT' and v['baseAsset'] in result}
    final_result = {}
    for asset, asset_data in result.items():
        if asset in assets_map:
            final_result[asset] = dict(start_ms=earliest_ts * 1000,
                                       end_ms=asset_data['ts'] * 1000,
                                       market=asset_data['market'])
    return final_result


def recover_data_from_binance():
    """从币安接口插入历史数据"""
    result = get_can_recover_data_from_binance()
    print(result)
    records = []
    progress_bar = tqdm(total=len(result))
    for asset, asset_data in result.items():
        progress_bar.update(1)
        market = asset_data['market']
        start_ms = asset_data['start_ms']
        end_ms = asset_data['end_ms']
        kline_url = f'https://api1.binance.com/api/v3/klines?symbol=' \
                    f'{market}&interval=1d&startTime={start_ms}&endTime={end_ms}'
        data = requests.get(kline_url).json()
        for kline_data in data:
            end_ts = kline_data[6] - kline_data[6] % 86400
            # 收盘价
            price = kline_data[4]
            records.append(AssetPriceKline(
                asset=asset,
                period=PeriodType.DAY,
                time=end_ts // 1000,
                price=price
            ))
        time.sleep(1)
    progress_bar.close()
    for objs in batch_iter(records, 5000):
        db.session.bulk_save_objects(objs)
    db.session.commit()
    for record in records:
        cache = AssetKlineCache(record.asset, record.period)
        cache.save_to_cache(record)


def insert_usdt_data_from_api():
    url = "https://dncapi.bqrank.net/api/coin/web-charts?code=tether&type=all&webp=1"
    kline_data = ast.literal_eval(requests.get(url).json()["value"])
    # 6天前
    end_ts = current_timestamp(to_int=True) - 86400 * 91
    exists_ts = set()
    records = []
    for v in kline_data:
        ts = v[0] // 1000
        _ts = ts - ts % 86400
        price = v[1]
        if _ts in exists_ts or _ts > end_ts:
            continue
        records.append(AssetPriceKline(
            asset="USDT",
            period=PeriodType.DAY,
            time=_ts,
            price=price
        ))
        exists_ts.add(_ts)

    max_ts = max(exists_ts)
    record_date = timestamp_to_datetime(max_ts)
    query = AssetPrice.query.filter(
        AssetPrice.date > record_date,
        AssetPrice.asset == "USDT"
    ).order_by(AssetPrice.date.asc()).all()
    current_ts = current_timestamp(to_int=True)
    save_ts = []
    for r in query:
        ts = int(r.date.timestamp())
        ts = ts - ts % 60
        if ts in save_ts:
            continue
        if ts % 86400 == 0:
            records.append(AssetPriceKline(
                asset="USDT",
                period=PeriodType.DAY,
                time=ts,
                price=r.price
            ))
        if ts % 3600 == 0:
            records.append(AssetPriceKline(
                asset="USDT",
                period=PeriodType.HOUR,
                time=ts,
                price=r.price
            ))
        if current_ts - ts <= 86400:
            records.append(AssetPriceKline(
                asset="USDT",
                period=PeriodType.MINUTE,
                time=ts,
                price=r.price
            ))
        save_ts.append(ts)
    for objs in batch_iter(records, 5000):
        db.session.bulk_save_objects(objs)
    db.session.commit()
    for record in records:
        cache = AssetKlineCache(record.asset, record.period)
        cache.save_to_cache(record)


def insert_big_coin_history_data_from_api(asset, full_asset_name):
    """
    BTC bitcoin
    ETH ethereum
    LTC litecoin
    BCH bitcoin-cash
    """
    url = f"https://dncapi.bqrank.net/api/coin/web-charts?code={full_asset_name}&type=all&webp=1"
    kline_data = ast.literal_eval(requests.get(url).json()["value"])
    exists_ts = set()
    records = []
    ts = get_every_asset_earliest_date().get(asset, {}).get("ts", 0)
    ent_ts = ts - ts % 86400
    for v in kline_data:
        ts = v[0] // 1000
        _ts = ts - ts % 86400
        price = v[1]
        if _ts in exists_ts or _ts > ent_ts:
            continue
        records.append(AssetPriceKline(
            asset=asset,
            period=PeriodType.DAY,
            time=_ts,
            price=price
        ))
        exists_ts.add(_ts)
    for objs in batch_iter(records, 5000):
        db.session.bulk_save_objects(objs)
    db.session.commit()
    for record in records:
        cache = AssetKlineCache(record.asset, record.period)
        cache.save_to_cache(record)


def insert_asset_price_history_data_to_cache():
    # 缓存：
    # 分钟k线 1天
    # 小时k线 2月
    # 天k线 1年
    ts = current_timestamp(to_int=True)
    period_type_map = {
        PeriodType.MINUTE: 86400 + 3600,
        PeriodType.HOUR: 86400 * 31 * 2,
        PeriodType.DAY: 86400 * 365 * 10,
    }
    assets = list_all_assets()
    f_time = lambda x: x - x % PeriodType.MINUTE.to_seconds()
    progress_bar = tqdm(total=len(assets))
    for asset in assets:
        for period_type in PeriodType:
            end_ts = ts - ts % period_type.to_seconds()
            start_ts = end_ts - period_type_map[period_type]
            period_seconds = period_type.to_seconds()
            points = [start_ts + v * period_seconds
                      for v in range((end_ts - start_ts) // period_seconds)]
            q = AssetPriceKline.query.filter(AssetPriceKline.asset == asset,
                                             AssetPriceKline.time >= start_ts,
                                             AssetPriceKline.time <= end_ts,
                                             AssetPriceKline.period == period_type).with_entities(
                AssetPriceKline.time,
                AssetPriceKline.price
            ).all()
            db_results = {f_time(v.time): str(v.price) for v in q}
            exists_data = {v: str(db_results[v]) for v in points if v in db_results}
            cache = AssetKlineCache(asset, period_type)
            if exists_data:
                cache.hmset(exists_data)
        progress_bar.update(1)
    progress_bar.close()


def modify_online_time_for_coin_information():
    # 修改上币时间
    q = CoinInformation.query.filter(CoinInformation.online_time.is_(None)).all()

    def find_online_time(asset):
        if asset == 'USDT':
            r = Market.query.filter(
                Market.quote_asset == "USDT"
            ).order_by(
                Market.created_at.asc()
            ).first()
            return r.created_at
        r = Market.query.filter(
            Market.base_asset == asset,
            Market.quote_asset == 'USDT'
        ).first()
        if r:
            return r.created_at

    for v in q:
        dt = find_online_time(v.code)
        if dt:
            v.online_time = dt
    db.session.commit()


def import_asset_circulation_data():
    circulation_data = [
        ['1INCH', '154414563'], ['AAVE', '12488142'], ['ACM', '2000157'], ['ADA', '31948309441'],
        ['AE', '334897613'], ['ALGO', '2771220758'], ['ALPHA', '250153035'], ['ANT', '39609523'],
        ['API3', '13847549'], ['AR', '33394701'], ['ARDR', '998999495'], ['ARRR', '177526930'],
        ['AST', '150000000'], ['ATOM', '210641905'], ['AUCTION', '2083956'], ['AUDIO', '120000000'],
        ['AUTO', '14600'], ['AVAX', '128582720'], ['AYA', '160459559'], ['BADGER', '8603194'],
        ['BAL', '6943831'], ['BAT', '1492816441'], ['BCH', '18708888'], ['BCHA', '18572921'],
        ['BCN', '184066828814'], ['BEAM', '85902120'], ['BEL ', '31500000'], ['BKK ', '60285010'],
        ['BLZ', '285934224'], ['BNB', '154532785'], ['BNT ', '178520977'], ['BOSON', '16222934'],
        ['BSV ', '18706177'], ['BTC', '18683118'], ['BTM', '1422834794'], ['BTS', '2994880000'],
        ['BTT', '659952625000'], ['CAKE', '152675144'], ['CET', '708633582'], ['CFX', '821970966'],
        ['CHZ', '5586355378'], ['CKB', '24690327882'], ['CMT', '800000000'], ['COMBO', '4542188'],
        ['COMP', '5075284'], ['COVER', '61382'], ['CREAM', '616378'], ['CRO', '25263013692'],
        ['CRV', '273116186'], ['CTXC', '249938725'], ['CVP', '23128598'], ['DAI', '3303970903'],
        ['DASH', '10078831'], ['DCR', '12822464'], ['DERO', '10512024'], ['DGB', '14219154220'],
        ['DGTX', '931035715'], ['DIA', '41430434'], ['DMD', '3588825'], ['DODO', '110551965'],
        ['DOGE', '129196123052'], ['DOT', '929528648'], ['DXD', '49305'], ['ELA', '19134897'],
        ['EMC', '47269595'], ['ENJ', '834313757'], ['EOS', '952549678'], ['EOSC', '964070125'],
        ['EPS', '58932534'], ['ERG', '31794928'], ['ETC', '116313299'], ['ETH', '115476364'],
        ['FCH', '33137216'], ['FIL', '66727184'], ['FIRO', '11756363'], ['FLOW', '33967817'],
        ['FNX', '29217626'], ['FTT', '94346958'], ['GAS', '10128375'], ['GEN', '48507939'],
        ['GHST', '40351655'], ['GLM', '1000000000'], ['GNO', '1504587'], ['GRIN', '70587240'],
        ['GRT', '1245666867'], ['GUSD', '126617825'], ['HBAR', '7916861509'], ['HC', '45071909'],
        ['HEGIC', '425355246'], ['HNS', '373178538'], ['HOPR', '130000000'],
        ['HOT', '169158310031'], ['HT', '178105407'], ['HYDRO', '70407690'], ['ICX', '597489700'],
        ['IFT', '191381257'], ['INJ', '16055554'], ['IOST', '16374175762'], ['IOTA', '2779530283'],
        ['IRIS', '977398807'], ['JRT', '29005880'], ['JST', '2260326706'], ['KAN', '8913646851'],
        ['KAVA', '58524186'], ['KDA', '115733729'], ['KEEP', '503992459'], ['KLV', '3395970980'],
        ['KMD', '125290416'], ['KNC', '205045092'], ['KP3R', '200001'], ['KSM', '8470098'],
        ['KTON', '44810'], ['KUN', '2000'], ['LAMB', '1479138020'], ['BAN', '1239970327'],
        ['WEST', '41965923'], ['ALPACA', '6618686'], ['FTM', '2545006273'], ['XCH', '515052'],
        ['RAY', '39077500'], ['SRM', '50000000'], ['FORTH', '6324505'], ['JGN', '39562500'],
        ['CHR', '429822250'], ['ANC', '58032294'], ['CHI', '46433205'], ['WAXP', '1576814349'],
        ['JULD', '385586195'], ['BRG', '8000000000'], ['SHIB', '394796000000000'],
        ['WAN', '169028581'], ['FET', '746113681'], ['TRB', '1547777'], ['CTSI', '337457354'],
        ['RLC', '80070793'], ['LBC', '572640863'], ['LIT', '18311958'], ['LINK', '419009556'],
        ['LON', '22906397'], ['LOOM', '1000000000'], ['LPT', '21164655'], ['LRC', '1223243353'],
        ['LSK', '127778762'], ['LTC', '6675241'], ['LUNA', '381347349'], ['MANA', '1578092413'],
        ['MASK', '9878121'], ['MATIC', '5038388130'], ['MIR', '54728195'], ['MKR', '995239'],
        ['MLN', '1792738'], ['NANO', '133248297'], ['NBS', '3095718947'], ['NEAR', '349452304'],
        ['NEO', '70538831'], ['NKN', '583666666'], ['NMC', '14736400'], ['NMR', '4911507'],
        ['NNB', '1769000000'], ['NRG', '39734986'], ['OCEAN', '426026837'], ['OGN', '282668411'],
        ['OKB', '60000000'], ['OLT', '454058270'], ['OMG', '140245398'], ['ONES', '11858277'],
        ['ONG', '157700000'], ['ONT', '809807991'], ['OXT', '367584352'], ['PAX', '93612452'],
        ['PERP', '21795505'], ['PGN', '7514364865'], ['PHNX', '57660270'], ['PIVX', '65180592'],
        ['PNK', '608121784'], ['POLS', '63776500'], ['POND', '736908424'], ['POWR', '439897654'],
        ['PRE', '350661436'], ['PUNDIX', '258491637'], ['QTUM', '98282573'], ['RAD', '4903705'],
        ['RARI', '24986613'], ['REEF', '11268898338'], ['REN', '997163051'], ['REP', '11000000'],
        ['REVV', '278270642'], ['RING', '469488008'], ['RLY', '133972182'], ['RNDR', '154378729'],
        ['RSR', '13159999000'], ['RVN', '8550685000'], ['SAND', '686821749'], ['SC', '47528062992'],
        ['SCRT', '69703477'], ['SEELE', '699592066'], ['SERO', '313065905'], ['SKL', '660416667'],
        ['SNT', '3470483788'], ['SNX', '114841533'], ['SOL', '270018859'], ['STAKE', '5237734'],
        ['STORJ', '255488217'], ['STX', '1087428006'], ['SUMO', '27570024'], ['SUN', '4957214'],
        ['SUSHI', '127244443'], ['SWRV', '11797773'], ['SXP', '85736975'], ['SYS', '61033508'],
        ['TARA', '240000000'], ['TKO', '108500000'], ['TLM', '124673310'], ['TORN', '561258'],
        ['TRIBE', '248364400'], ['TRTL', '98887696361'], ['TRX', '71659657369'],
        ['TUSD', '299286814'], ['ULT', '2364828294'], ['UMA', '60056046'], ['UNI', '523363302'],
        ['USDC', '10829725077'], ['USDT', '45855749841'], ['VET', '64315576989'],
        ['VSYS', '2252003662'], ['VTHO', '34266047166'], ['WAVES', '104836726'],
        ['WINGS', '99999994'], ['WNXM', '1676964'], ['WOO', '376235705'], ['XDAG', '1045137792'],
        ['XEM', '8999999999'], ['XHV', '14177541'], ['XLM', '1374036648'], ['XMR', '17882527'],
        ['XRP', '45404028640'], ['XTZ', '766422264'], ['XVG', '16444694949'], ['XVS', '9402716'],
        ['YFI', '36635'], ['YFII', '38596'], ['YUSRA', '38904443'], ['ZEC', '11549588'],
        ['ZEN', '10971238'], ['ZER', '9879233'], ['ZIL', '11167370486'], ['ZRX', '781547659']]
    q = CoinInformation.query.all()
    circulation_dict = dict(circulation_data)
    for v in q:
        if v.code in circulation_dict:
            v.circulation = circulation_dict[v.code]
    db.session.commit()


def change_admin_permission_data():
    names = [
        "运营-币种资料-币种资料搜索",
        "运营-币种资料-创建币种资料",
        "运营-币种资料-币种列表",
        "运营-币种资料-修改币种资料",
    ]
    for name in names:
        permission = AdminPermission.query.filter(
                     AdminPermission.name == name,
        ).first()
        old_endpoint = permission.endpoint
        old_rule = permission.rule
        new_endpoint = old_endpoint.replace("Operation", "Quotes")
        new_rule = old_rule.replace("operation", "quotes")
        permission.endpoint = new_endpoint
        permission.rule = new_rule
        db.session.commit()
