# -*- coding: utf-8 -*-
import os
import sys
from collections import defaultdict
from datetime import timedelta, datetime, date
from decimal import Decimal
from dateutil.relativedelta import relativedelta

import click
from sqlalchemy import func


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


from app.models import (
    db,
    AssetPrice,
    DailyDepositWithdrawalReport,
    MonthlyDepositWithdrawalReport,
    Deposit,
    Withdrawal,
    RedPacket,
    RedPacketHistory,
    DailyInnerTransferReport,
    MonthlyInnerTransferReport,
)
from app.common import ReportType, PrecisionEnum
from app.assets import list_all_assets
from app.config import config
from app.utils import amount_to_str


# noinspection DuplicatedCode
def _update_report_all_site_row_and_local_columns(start_date, end_date, report_type=ReportType.DAILY):
    # see schedules.reports.deposit_withdrawal.update_deposit_withdrawal_report
    model = DailyDepositWithdrawalReport if report_type == ReportType.DAILY else MonthlyDepositWithdrawalReport

    assets_rate = AssetPrice.get_close_price_map(start_date)
    assets = set(list_all_assets())
    deposits = Deposit.query.filter(
        start_date < Deposit.created_at,
        Deposit.created_at <= end_date,
        Deposit.type == Deposit.Type.ON_CHAIN,
        Deposit.status.in_(
            [
                Deposit.Status.CONFIRMING,
                Deposit.Status.FINISHED,
                Deposit.Status.TO_HOT,
            ]
        ),
    ).all()

    # ViaBTC内部充值
    pool_user_id = config['CLIENT_CONFIGS']['viabtc_pool']['user_id']
    viabtc_deposits = Deposit.query.filter(
        start_date < Deposit.created_at,
        Deposit.created_at <= end_date,
        Deposit.type == Deposit.Type.LOCAL,
        Deposit.sender_user_id == pool_user_id,
        Deposit.status.in_(
            [
                Deposit.Status.CONFIRMING,
                Deposit.Status.FINISHED,
                Deposit.Status.TO_HOT,
            ]
        ),
    ).all()
    withdrawals = Withdrawal.query.filter(
        start_date < Withdrawal.sent_at,
        Withdrawal.sent_at <= end_date,
        Withdrawal.type == Withdrawal.Type.ON_CHAIN,
        Withdrawal.status.in_(
            [
                Withdrawal.Status.PROCESSING,
                Withdrawal.Status.CONFIRMING,
                Withdrawal.Status.FINISHED,
            ]
        ),
    ).all()

    db.session.expunge_all()

    zero = Decimal()
    all_site_statistics_data_dict = dict(
        deposit_count=0,
        deposit_amount=zero,
        deposit_usd=zero,
        deposit_user_count=0,
        withdrawal_count=0,
        withdrawal_amount=zero,
        withdrawal_usd=zero,
        withdrawal_user_count=0,
        withdrawal_user_fee=zero,
        withdrawal_on_chain_fee=zero,
        local_deposit_count=0,
        local_deposit_amount=zero,
        local_deposit_usd=zero,
        local_deposit_user_count=0,
    )
    all_deposit_user_set, all_withdrawal_user_set, all_local_deposit_user_set = set(), set(), set()
    for asset in assets:
        # only update
        exist_row = model.query.filter(model.report_date == start_date, model.asset == asset).first()
        if not exist_row:
            continue

        asset_withdrawals = [i for i in withdrawals if i.asset == asset]
        withdrawal_user_list = set([r.user_id for r in asset_withdrawals])

        asset_price = assets_rate.get(asset, Decimal(0))
        local_asset_deposits = [i for i in viabtc_deposits if i.asset == asset]
        local_deposit_count = len(local_asset_deposits)
        local_deposit_amount = sum(r.amount for r in local_asset_deposits)
        local_deposit_usd = local_deposit_amount * asset_price
        local_deposit_user_list = set([r.user_id for r in local_asset_deposits])
        local_deposit_user_count = len(local_deposit_user_list)

        # deposit_* 加上ViaBTC充值的数据
        asset_deposits = [i for i in deposits if i.asset == asset]
        deposit_count = len(asset_deposits) + local_deposit_count
        deposit_amount = sum(r.amount for r in asset_deposits) + local_deposit_amount
        deposit_usd = deposit_amount * asset_price
        deposit_user_list = set([r.user_id for r in asset_deposits]) | local_deposit_user_list
        deposit_user_count = len(deposit_user_list)

        exist_row.local_deposit_count = local_deposit_count
        exist_row.local_deposit_amount = local_deposit_amount
        exist_row.local_deposit_usd = local_deposit_usd
        exist_row.local_deposit_user_count = local_deposit_user_count
        exist_row.deposit_count = deposit_count
        exist_row.deposit_amount = deposit_amount
        exist_row.deposit_usd = deposit_usd
        exist_row.deposit_user_count = deposit_user_count
        # 更新全站统计数据
        for key_ in all_site_statistics_data_dict:
            all_site_statistics_data_dict[key_] += getattr(exist_row, key_)
            all_deposit_user_set.update(deposit_user_list)
            all_withdrawal_user_set.update(withdrawal_user_list)
            all_local_deposit_user_set.update(local_deposit_user_list)

    all_site_statistics_data_dict["deposit_user_count"] = len(all_deposit_user_set)
    all_site_statistics_data_dict["withdrawal_user_count"] = len(all_withdrawal_user_set)
    all_site_statistics_data_dict["local_deposit_user_count"] = len(all_local_deposit_user_set)
    exist_all_site_row = model.query.filter(model.report_date == start_date, model.asset == "").first()
    if exist_all_site_row:
        for k, v in all_site_statistics_data_dict.items():
            setattr(exist_all_site_row, k, v)
    else:
        db.session.add(model(report_date=start_date, asset="", **all_site_statistics_data_dict))
    db.session.commit()


# noinspection DuplicatedCode
def import_deposit_withdrawal_report_site_data_and_local_deposit_cols(start_date: date, end_date: date) -> None:
    """ 刷 `全站数据行` 和 `新增的ViaBTC内部充值统计列` 到充提报表 """
    daily_report_dates = DailyDepositWithdrawalReport.query.with_entities(
        func.min(DailyDepositWithdrawalReport.report_date).label("min_date"),
        func.max(DailyDepositWithdrawalReport.report_date).label("min_date"),
    ).all()
    min_daily_date, max_daily_date = daily_report_dates[0]
    print(f"DailyDepositWithdrawalReport min_report_date: {min_daily_date}, max_report_date: {max_daily_date}")

    start_day = max(start_date, min_daily_date) if start_date else min_daily_date
    end_day = min(end_date, max_daily_date) if end_date else max_daily_date
    while start_day <= end_day:
        next_day = start_day + timedelta(days=1)
        _update_report_all_site_row_and_local_columns(start_day, next_day, report_type=ReportType.DAILY)
        print(f"DailyDepositWithdrawalReport {start_day} done")
        start_day = next_day

    #
    monthly_report_dates = MonthlyDepositWithdrawalReport.query.with_entities(
        func.min(MonthlyDepositWithdrawalReport.report_date).label("min_date"),
        func.max(MonthlyDepositWithdrawalReport.report_date).label("min_date"),
    ).all()
    min_monthly_date, max_monthly_date = monthly_report_dates[0]
    assert min_monthly_date.day == 1
    assert max_monthly_date.day == 1
    print(f"MonthlyDepositWithdrawalReport min_report_date: {min_monthly_date}, max_report_date: {max_monthly_date}")

    start_month = max(start_date.replace(day=1), min_monthly_date) if start_date else min_monthly_date
    end_month = min(end_date.replace(day=1), max_monthly_date) if end_date else max_monthly_date
    while start_month <= end_month:
        next_month_ = start_month + relativedelta(months=1)
        _update_report_all_site_row_and_local_columns(start_month, next_month_, report_type=ReportType.MONTHLY)
        print(f"MonthlyDepositWithdrawalReport {start_month} done")
        start_month = next_month_


# noinspection DuplicatedCode
def _update_inner_transfer_report_all_site_row(start_time, end_time, report_type=ReportType.DAILY):
    # see schedules.reports.inner_transfer.update_inner_transfer_report
    report_model = DailyInnerTransferReport if report_type == ReportType.DAILY else MonthlyInnerTransferReport
    pool_user_id = config["CLIENT_CONFIGS"]["viabtc_pool"]["user_id"]
    inner_transfer_rows = Deposit.query.filter(
        Deposit.created_at >= start_time,
        Deposit.created_at < end_time,
        Deposit.type == Deposit.Type.LOCAL,
    ).all()
    asset_inner_transfers_dict = defaultdict(list)
    viabtc_assets = set()
    for row in inner_transfer_rows:
        if row.sender_user_id != pool_user_id:
            # 内部转账 排除 ViaBTC内部充值
            asset_inner_transfers_dict[row.asset].append(row)
        else:
            viabtc_assets.add(row.asset)

    red_packet_rows = RedPacket.query.filter(
        RedPacket.effective_at >= start_time,
        RedPacket.effective_at < end_time,
    ).all()
    asset_red_packets_dict = defaultdict(list)
    for row in red_packet_rows:
        asset_red_packets_dict[row.asset].append(row)

    red_packet_history_rows = RedPacketHistory.query.filter(
        RedPacketHistory.grab_at >= start_time,
        RedPacketHistory.grab_at < end_time,
    ).all()
    asset_red_packet_histories_dict = defaultdict(list)
    for row in red_packet_history_rows:
        asset_red_packet_histories_dict[row.asset].append(row)

    db.session.expunge_all()
    assets = set(asset_inner_transfers_dict) | set(asset_red_packets_dict) | set(asset_red_packet_histories_dict)
    assets_rate = AssetPrice.get_close_price_map(start_time)
    zero = Decimal()

    # 是viabtc充值 的币种, 并且只有站内转账的数据, 设为0
    if _diff_assets := viabtc_assets - assets:
        for asset in _diff_assets:
            exist_row = report_model.query.filter(
                report_model.report_date == start_time,
                report_model.asset == asset,
            ).first()
            if exist_row:
                exist_row.inner_transfer_user_count = 0
                exist_row.inner_transfer_count = 0
                exist_row.inner_transfer_amount = zero
                exist_row.inner_transfer_usd = zero

    new_records = []
    all_site_statistics_data_dict = dict(
        inner_transfer_user_count=0,
        inner_transfer_count=0,
        inner_transfer_amount=zero,
        inner_transfer_usd=zero,
        give_red_packet_user_count=0,
        give_red_packet_count=0,
        give_red_packet_amount=zero,
        give_red_packet_usd=zero,
        receive_red_packet_user_count=0,
        receive_red_packet_count=0,
        receive_red_packet_amount=zero,
        receive_red_packet_usd=zero,
    )
    all_inner_transfer_user_set, all_give_rp_user_set, all_receive_rp_user_set = set(), set(), set()
    for asset in assets:
        # only update
        exist_row = report_model.query.filter(
            report_model.report_date == start_time,
            report_model.asset == asset,
        ).first()
        if not exist_row:
            continue

        asset_price = assets_rate.get(asset, Decimal())

        inner_transfers = asset_inner_transfers_dict[asset]
        inner_transfer_count = len(inner_transfers)
        inner_transfer_amount = sum(r.amount for r in inner_transfers)
        inner_transfer_usd = inner_transfer_amount * asset_price
        inner_transfer_user_set = {r.user_id for r in inner_transfers}
        inner_transfer_user_count = len(inner_transfer_user_set)

        red_packets = asset_red_packets_dict[asset]
        give_red_packet_user_set = {r.user_id for r in red_packets}
        give_red_packet_usd = exist_row.give_red_packet_amount * asset_price

        red_packet_histories = asset_red_packet_histories_dict[asset]
        receive_red_packet_user_set = {r.user_id for r in red_packet_histories}
        receive_red_packet_usd = exist_row.receive_red_packet_amount * asset_price

        exist_row.inner_transfer_user_count = inner_transfer_user_count
        exist_row.inner_transfer_count = inner_transfer_count
        exist_row.inner_transfer_amount = inner_transfer_amount
        exist_row.inner_transfer_usd = inner_transfer_usd
        exist_row.give_red_packet_usd = give_red_packet_usd
        exist_row.receive_red_packet_usd = receive_red_packet_usd

        # 全站数据
        for key_ in all_site_statistics_data_dict:
            all_site_statistics_data_dict[key_] += getattr(exist_row, key_)
            all_inner_transfer_user_set.update(inner_transfer_user_set)
            all_give_rp_user_set.update(give_red_packet_user_set)
            all_receive_rp_user_set.update(receive_red_packet_user_set)

    #
    if new_records:
        db.session.add_all(new_records)
    all_site_statistics_data_dict["inner_transfer_user_count"] = len(all_inner_transfer_user_set)
    all_site_statistics_data_dict["give_red_packet_user_count"] = len(all_give_rp_user_set)
    all_site_statistics_data_dict["receive_red_packet_user_count"] = len(all_receive_rp_user_set)
    decimal_keys = [
        "inner_transfer_amount",
        "inner_transfer_usd",
        "give_red_packet_amount",
        "give_red_packet_usd",
        "receive_red_packet_amount",
        "receive_red_packet_usd",
    ]
    for k in decimal_keys:
        all_site_statistics_data_dict[k] = amount_to_str(all_site_statistics_data_dict[k], PrecisionEnum.COIN_PLACES)
    all_site_record = report_model.query.filter(
        report_model.report_date == start_time,
        report_model.asset == "",
    ).first()
    if not all_site_record:
        db.session.add(report_model(report_date=start_time, asset="", **all_site_statistics_data_dict))
    else:
        for key_, val_ in all_site_statistics_data_dict.items():
            setattr(all_site_record, key_, val_)
    db.session.commit()


# noinspection DuplicatedCode
def import_inner_transfer_report_site_data(start_date: date, end_date: date) -> None:
    """ 刷 `全站数据行` 到内部转账报表 """
    daily_model = DailyInnerTransferReport
    daily_report_dates = daily_model.query.with_entities(
        func.min(daily_model.report_date).label("min_date"),
        func.max(daily_model.report_date).label("min_date"),
    ).all()
    min_daily_date, max_daily_date = daily_report_dates[0]
    print(f"{daily_model.__name__} min_report_date: {min_daily_date}, max_report_date: {max_daily_date}")

    start_day = max(start_date, min_daily_date) if start_date else min_daily_date
    end_day = min(end_date, max_daily_date) if end_date else max_daily_date
    while start_day <= end_day:
        next_day = start_day + timedelta(days=1)
        _update_inner_transfer_report_all_site_row(start_day, next_day, report_type=ReportType.DAILY)
        print(f"{daily_model.__name__} {start_day} done")
        start_day = next_day

    #
    monthly_model = MonthlyInnerTransferReport
    monthly_report_dates = monthly_model.query.with_entities(
        func.min(monthly_model.report_date).label("min_date"),
        func.max(monthly_model.report_date).label("min_date"),
    ).all()
    min_monthly_date, max_monthly_date = monthly_report_dates[0]
    assert min_monthly_date.day == 1
    assert max_monthly_date.day == 1
    print(f"{monthly_model.__name__} min_report_date: {min_monthly_date}, max_report_date: {max_monthly_date}")

    start_month = max(start_date.replace(day=1), min_monthly_date) if start_date else min_monthly_date
    end_month = min(end_date.replace(day=1), max_monthly_date) if end_date else max_monthly_date
    while start_month <= end_month:
        next_month_ = start_month + relativedelta(months=1)
        _update_inner_transfer_report_all_site_row(start_month, next_month_, report_type=ReportType.MONTHLY)
        print(f"{monthly_model.__name__} {start_month} done")
        start_month = next_month_


@click.command()
@click.option("--start_date", type=str, required=False)
@click.option("--end_date", type=str, required=False)
def main(start_date, end_date):
    start_date = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
    end_date = datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None
    import_deposit_withdrawal_report_site_data_and_local_deposit_cols(start_date, end_date)
    import_inner_transfer_report_site_data(start_date, end_date)


if __name__ == "__main__":
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
