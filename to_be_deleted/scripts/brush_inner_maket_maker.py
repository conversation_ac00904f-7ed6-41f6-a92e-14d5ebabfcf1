import os
import sys


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


def main():
    from app.models import db, User, InnerMarketMaker
    from app.business.fee import update_user_fee_task

    inner_mms = User.query.filter(
        User.user_type == User.UserType.INTERNAL_MAKER
    ).with_entities(User.id).all()
    inner_mm_ids = [i.id for i in inner_mms]
    print(f"total {len(inner_mm_ids)} internal_maker users")

    for uid in inner_mm_ids:
        # 将目前线上的内部做市商都添加到此表
        inner_mm_record: InnerMarketMaker = InnerMarketMaker.get_or_create(user_id=uid)
        inner_mm_record.status = InnerMarketMaker.StatusType.VALID
        inner_mm_record.remark = ""
        db.session.add(inner_mm_record)

        # 对于线上的内部做市商，功能上线时，批量加入现货Maker返现列表，保底等级为3级，过期时间为永不过期
        cash_back_record: MakerCashBackUser = MakerCashBackUser.get_or_create(user_id=uid)
        cash_back_record.level = 3
        cash_back_record.check_level = 1
        cash_back_record.lock_level = 3
        cash_back_record.expired_at = None
        cash_back_record.remark = ""
        cash_back_record.status = MakerCashBackUser.StatusType.PASSED
        db.session.add(cash_back_record)

    db.session.commit()
    print(f"brush InnerMarketMaker,MakerCashBackUser done")

    for uid in inner_mm_ids:
        update_user_fee_task(uid)
    print("update_user_fee done")


if __name__ == "__main__":
    from app import create_app

    with create_app().app_context():
        main()
