import os
import json
import sys

import click
from flask_babel import gettext


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


# noinspection PyProtectedMember
def brush_message(start_id: int):
    # 刷最近三月的message 到 新message表
    from typing import List
    from app.models import db, Message, MessageTitle, MessageContent
    from app.models.message import MessageOld
    from app.caches.base import BaseCacheMeta
    from app.caches import StringCache
    from app.utils import batch_iter

    _name = BaseCacheMeta._class_name_to_base_key("TempMessageIdCursorCache")
    BaseCacheMeta._base_keys.pop(_name, None)

    class TempMessageIdCursorCache(StringCache):
        ttl = 86400 * 7

        def __init__(self):
            super().__init__(None)

    if not start_id:
        start_id = TempMessageIdCursorCache().read()
        if not start_id:
            exit("请提供 start_id")
        else:
            start_id = int(start_id)

    cache = TempMessageIdCursorCache()
    valid_titles = [i.name for i in MessageTitle]
    valid_contents = [i.name for i in MessageContent]
    chunk_size = 10000
    brush_success_count = 0
    check_failed_count = 0
    while 1:
        old_msg_rows = MessageOld.query.filter(MessageOld.id > start_id).order_by(MessageOld.id.asc()).limit(chunk_size).all()
        cache.set(str(start_id), ex=cache.ttl)
        if not old_msg_rows:
            break

        start_id = old_msg_rows[-1].id
        valid_old_msg_rows: List[MessageOld] = [i for i in old_msg_rows if i.title in valid_titles and i.content in valid_contents]
        if not valid_old_msg_rows:
            continue

        new_messages = []
        for o in valid_old_msg_rows:
            if o.status == MessageOld.Status.UNREAD:
                status = Message.Status.UNREAD
            elif o.status == MessageOld.Status.READ:
                status = Message.Status.READ
            else:
                status = Message.Status.DELETED
            if o.needs_attention:
                display_type = Message.DisplayType.POPUP_WINDOW
            else:
                display_type = Message.DisplayType.TEXT
            new_msg = Message(
                created_at=o.created_at,
                updated_at=o.updated_at,
                user_id=o.user_id,
                title=MessageTitle[o.title],
                content=MessageContent[o.content],
                params=o.params,
                status=status,
                extra_info="",
                display_type=display_type,
                expired_at=None,
                channel=Message.Channel.SYSTEM,
            )
            try:
                message_to_dict(new_msg)
            except Exception as _e:
                check_failed_count += 1
                print(f"new message(old_id: {o.id}) to dict error: {_e}")
            else:
                new_messages.append(new_msg)

        for rows in batch_iter(new_messages, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()
        print(
            f"write message ID({valid_old_msg_rows[0].id}) ~ ID({valid_old_msg_rows[-1].id}) rows to message_new, "
            f"valid_rows: {len(valid_old_msg_rows)}"
        )
        brush_success_count += len(new_messages)
        if len(old_msg_rows) < chunk_size:
            break

    print(f"###brush done, brush_success_count: {brush_success_count}, check_failed_count: {check_failed_count}")


def message_to_dict(row) -> dict:
    from app.models import Message

    params = json.loads(params) if (params := row.params) else {}
    return dict(
        message_id=row.id,
        create_time=row.created_at,
        title=gettext(row.title.value, **params),
        content=gettext(row.content.value, **params),
        channel=row.channel.name,
        status="viewed" if row.status is Message.Status.READ else "not_view",
        url="",
        message_type="",
        **row.extra_info_dict,
    )


@click.command()
@click.option("--start_id", type=click.INT, default=0)
def main(start_id):
    brush_message(start_id)


if __name__ == "__main__":
    from app import create_app

    with create_app().app_context():
        main()
