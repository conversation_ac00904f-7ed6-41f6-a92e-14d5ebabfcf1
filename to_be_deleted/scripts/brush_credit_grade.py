import os
import sys
from functools import wraps

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def add_print(func):
    @wraps(func)
    def dec(*args, **kwargs):
        print(f'{func.__name__}>>>')
        ret = func(*args, **kwargs)
        print(f'{func.__name__}<<<')
        return ret

    return dec


def main():
    @add_print
    def get_inner_maker_users():
        from app.models import User

        model = User
        rows = model.query.with_entities(model.id).filter(
            model.user_type == model.UserType.INTERNAL_MAKER
        ).all()
        return {row.id for row in rows}

    @add_print
    def brush_credit_grade_super():
        from app.models import db
        from app.models import CreditUser

        model = CreditUser
        rows = model.query.all()
        for row in rows:
            if row.status is model.StatusType.DELETE:
                row.credit_grade = model.CreditGrade.NORMAL
            else:
                if row.user_id in inner_makers:
                    row.credit_grade = model.CreditGrade.SUPER
                else:
                    row.credit_grade = model.CreditGrade.NORMAL

        db.session.commit()

    inner_makers = get_inner_maker_users()
    brush_credit_grade_super()


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
