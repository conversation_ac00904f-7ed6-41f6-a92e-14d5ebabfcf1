# -*- coding: utf-8 -*-
import os
import sys
from decimal import Decimal
from collections import defaultdict
from datetime import date

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def get_refer_history(user_ids):
    from app.models import ReferralHistory

    refer_query = ReferralHistory.query.filter(
        ReferralHistory.referrer_id.in_(user_ids),
    ).with_entities(
        ReferralHistory.referrer_id,
        ReferralHistory.referree_id,
    )
    return dict(refer_query.all())


def get_trade_summary_usd(user_ids, start_date, end_date):
    from app.models import SubAccount
    from app.business.external_dbs import TradeSummaryDB, PerpetualSummaryDB
    from app.business import PriceManager
    from app.utils import batch_iter

    sub_user_map = {}
    for ids in batch_iter(user_ids, 1000):
        rows = SubAccount.query.filter(
            SubAccount.main_user_id.in_(ids)
        ).with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id
        ).all()
        sub_user_map.update(dict(rows))

    all_user_ids = set(list(sub_user_map.keys()) + user_ids)

    result = defaultdict(Decimal)
    prices = PriceManager.assets_to_usd()
    cursor = TradeSummaryDB.cursor()
    p_cursor = PerpetualSummaryDB.cursor()
    for table in TradeSummaryDB.get_user_trade_summary_tables(start_date, end_date):
        sql = f"select user_id, money_asset, sum(deal_volume) from {table} " \
              f"where trade_date >='{start_date}' and trade_date <= '{end_date}' " \
               "group by user_id, money_asset"
        cursor.execute(sql)
        rows = cursor.fetchall()
        for user_id, asset, amount in rows:
            if user_id not in all_user_ids:
                continue
            main_user_id = sub_user_map.get(user_id, user_id)
            result[main_user_id] += prices.get(asset, 0) * amount
        # 合约
        sql = f"select user_id, sum(deal_amount) from {table} " \
              f"where trade_date >='{start_date}' and trade_date <= '{end_date}' " \
               "group by user_id"
        p_cursor.execute(sql)
        rows = p_cursor.fetchall()
        for user_id, amount in rows:
            if user_id not in all_user_ids:
                continue
            main_user_id = sub_user_map.get(user_id, user_id)
            result[main_user_id] += amount
    return result


def get_user_infos(user_ids):
    from app.models import User
    from app.utils import batch_iter

    result = {}
    for ids in batch_iter(user_ids, 1000):
        rows = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(
            User.id,
            User.email
        ).all()
        result.update(dict(rows))
    return result


def main():
    from app.models import Ambassador
    from app.utils import upload_file, ExcelExporter, amount_to_str

    ambassadors = Ambassador.query.filter(Ambassador.status == Ambassador.Status.VALID)\
                            .with_entities(Ambassador.user_id).all()
    ambassadors = [x for x, in ambassadors]
    refers_map = get_refer_history(ambassadors)
    user_infos = get_user_infos(list(refers_map.keys()) + list(refers_map.values()))
    trade_map1 = get_trade_summary_usd(list(refers_map.values()), date(2020, 10, 14), date(2020, 11, 14))
    trade_map3 = get_trade_summary_usd(list(refers_map.values()), date(2020, 8, 14), date(2020, 11, 14))
    result = []
    for user_id, refer_user_id in refers_map.items():
        trade_amount1 = trade_map1.get(refer_user_id, 0)
        trade_amount3 = trade_map3.get(refer_user_id, 0)
        result.append((
            user_infos.get(user_id, user_id),
            user_infos.get(refer_user_id, refer_user_id),
            amount_to_str(trade_amount1, 2),
            amount_to_str(trade_amount3, 2)
            ))

    columns = ['a', 'b', 'c', 'd']
    headers = ['大使', '被邀请人', '最近1个月交易量(USD)', '最近3个月交易量(USD)']
    streams = ExcelExporter(
        data_list=[dict(zip(columns, x)) for x in result], fields=columns, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
