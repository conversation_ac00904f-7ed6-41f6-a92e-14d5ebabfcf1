# -*- coding: utf-8 -*-
import os
import sys

from app.models import User

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import PageVisitor
    from app.business.analytics import Scope
    from app.business.analytics import Page
    from app.business.analytics import Channel
    from datetime import datetime
    from app.business import PerpetualSummaryDB
    from app.models import Deposit
    from app.business import PriceManager
    from decimal import Decimal
    from collections import defaultdict
    from app.utils import now
    from app.utils import ExcelExporter
    from app.utils import upload_file
    header_data = [
        ('user_id', '用户id'),
        ('email', '邮箱'),
        ('created_time', '注册时间'),
        ('deposit_usd', '充值金额(USD)'),
        ('trade_amount_usd', '交易金额(USD)'),
        ('fee_usd', '手续费(USD)'),
    ]

    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]

    activity_start_time = datetime.fromtimestamp(1605571200)
    activity_end_time = datetime.fromtimestamp(1606694400)

    user_ids = PageVisitor.query.filter(
        PageVisitor.scope == Scope.PERPETUAL_ACTIVITY.value,
        PageVisitor.page == Page.PERPETUAL_ACTIVITY.value,
        PageVisitor.channel != Channel.COINEX.value
    ).with_entities(PageVisitor.user_id.distinct())
    users = User.query.filter(
        User.id.in_([x[0] for x in user_ids]),
        User.created_at >= activity_start_time,
        User.created_at < activity_end_time
    ).with_entities(
        User.id,
        User.email,
        User.created_at
    )
    user_ids = tuple([x.id for x in users])
    total_trade_amount = PerpetualSummaryDB.table(
        'user_trade_summary_202011').select(
        "user_id, sum(deal_amount)",
        where=f"trade_date >= '{activity_start_time}' and "
              f"trade_date < '{activity_end_time}' and user_id in {user_ids}",
        group_by="user_id")

    total_fee = PerpetualSummaryDB.table('user_fee_summary_202011').select(
        "user_id, sum(fee) fee_sum, asset",
        where=f"trade_date >= '{activity_start_time}' and "
              f"trade_date < '{activity_end_time}' and user_id in {user_ids}",
        group_by="user_id, asset"
    )

    deposit = Deposit.query.filter(
        Deposit.user_id.in_(user_ids),
        Deposit.created_at >= activity_start_time,
        Deposit.created_at < activity_end_time
    ).with_entities(
        Deposit.user_id,
        Deposit.amount,
        Deposit.asset
    )

    fee_map, deposit_map, trade_amount_map = defaultdict(Decimal), \
                                             defaultdict(Decimal), \
                                             defaultdict(Decimal)
    for f in total_fee:
        fee_map[f[0]] += PriceManager.asset_to_usd(f[2]) * f[1]
    for d in deposit:
        deposit_map[d.user_id] += PriceManager.asset_to_usd(d.asset) * d.amount
    for t in total_trade_amount:
        trade_amount_map[t[0]] += t[1]
    res = []
    for u in users:
        res.append(dict(
            user_id=u.id,
            email=u.email,
            created_time=u.created_at,
            deposit_usd=deposit_map[u.id],
            trade_amount_usd=trade_amount_map[u.id],
            fee_usd=fee_map[u.id]
        ))

    streams = ExcelExporter(
        data_list=res, fields=fields, headers=headers
    ).export_streams()

    file_url = upload_file(streams, 'xlsx')
    print(file_url)
    print(f'end at {now()}')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
