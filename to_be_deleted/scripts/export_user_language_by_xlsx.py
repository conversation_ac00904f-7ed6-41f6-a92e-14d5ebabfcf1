# -*- coding: utf-8 -*-
import datetime
import os
import sys

from werkzeug.datastructures import FileStorage
import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


@click.command()
@click.argument('file', type=click.File('rb'))
def main(file):
    from app.utils import ExcelExporter, upload_file
    from app.utils.importer import get_table_rows
    from app.business import UserPreferences
    from app.common import LANGUAGE_NAMES
    from app.models import User
    head_list = ['address', 'email', 'language']
    data = get_table_rows(FileStorage(file), head_list)
    email_list = [item['email'] for item in data]
    user_query_data = User.query.filter(
        User.email.in_(email_list)
    ).with_entities(User.email, User.id).all()
    email_map = {i.email: i.id for i in user_query_data}
    for row in data:
        if row['email'] in email_map:
            user_id = email_map[row['email']]
            language = UserPreferences(user_id).language
            lang_str = LANGUAGE_NAMES[language].chinese
            row['language'] = lang_str
    header_data = [
        ('address', '地址'),
        ('email', '邮箱'),
        ('language', '语言'),
    ]
    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]

    streams = ExcelExporter(
        data_list=data, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
