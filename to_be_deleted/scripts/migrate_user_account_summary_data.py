# -*- coding: utf-8 -*-
import datetime
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.business import ExchangeLogDB

    cur_timestamp = int(datetime.datetime.now().timestamp())
    for delta in range(6):
        ts = cur_timestamp - 60 * 60 * 24 * delta
        print(f'Updating at {ts}...start')
        ExchangeLogDB.sync_user_account_balance_sum(ts)
        print(f'Updating at {ts}...done')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
