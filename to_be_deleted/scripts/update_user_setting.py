# -*- coding: utf-8 -*-
import os
import sys

from sqlalchemy import func, case
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.business import UserSettings
    from app.models import UserSetting
    from app.utils import ConfigMode

    rows = UserSetting.query.filter(
        UserSetting.key == 'sub_account_transfer_to_main_disabled',
        UserSetting.value == '1'
    ).all()
    print(len(rows))
    for row in rows:
        UserSettings(row.user_id, ConfigMode.REAL_TIME).sub_account_transfer_out_disabled = True


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
