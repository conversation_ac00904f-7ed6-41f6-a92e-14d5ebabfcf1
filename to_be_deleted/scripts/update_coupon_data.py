# -*- coding: utf-8 -*-
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def update_coupon_message():
    import json
    from app.models.message import Message
    from app.common.constants import MessageContent
    from app.models import db

    update_message = Message.query.filter(
        Message.content.in_((
            MessageContent.INVESTMENT_INCREASE_RATE_COUPON_USED,
            MessageContent.CASHBACK_FEE_COUPON_USED
        ))
    ).all()
    for message in update_message:
        params = json.loads(message.params)
        new_params = {}
        for key, value in params.items():
            if key == "asset":
                new_params["value_type"] = value
            elif key == "amount":
                new_params["value"] = value
            else:
                new_params[key] = value
        message.params = json.dumps(new_params)
    db.session.commit()


def main():
    from app.models.activity import CouponApply, CouponApplyDraft, db
    from app.caches.activity import CouponCache, CouponPoolCache

    def get_draft_status(status):
        str_status = status.name
        if str_status in [CouponApplyDraft.Status.CREATED.name, CouponApplyDraft.Status.REJECTED.name]:
            return CouponApplyDraft.Status[status.name], CouponApply.Status.DRAFT
        elif str_status == CouponApplyDraft.Status.PASSED.name:
            return CouponApplyDraft.Status.PASSED, CouponApply.Status.CREATED
        else:
            return CouponApplyDraft.Status.PASSED, status

    coupon_applies = CouponApply.query.filter(
        CouponApply.origin_id == 0
    ).all()
    for apply in coupon_applies:
        draft_status, apply_status = get_draft_status(apply.status)
        apply_draft = CouponApplyDraft(
            title=apply.title,
            creat_user_id=apply.creat_user_id,
            auditor_id=apply.auditor_id,
            user_group_filter=apply.user_group_filter,
            user_group_condition=apply.user_group_condition,
            code=apply.code,
            remark=apply.remark
        )
        db.session.add(apply_draft)
        db.session.flush()
        apply_draft.status = draft_status
        apply.status = apply_status
        apply.origin_type = CouponApply.OriginType.DRAFT
        apply.origin_id = apply_draft.id
    db.session.commit()
    CouponCache.reload_all()
    pool_cache = CouponPoolCache()
    pool_cache.delete()
    pool_cache.reload()
    update_coupon_message()


if __name__ == "__main__":
    from app import create_app

    app = create_app()
    with app.app_context():
        main()