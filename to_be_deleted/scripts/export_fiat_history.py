import os
import sys

import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

@click.command()
@click.argument('start_ts', type=click.INT)
@click.argument('end_ts', type=click.INT)
def main(start_ts, end_ts):
    from app.business.export.base import UserExport
    from app.common.constants import language_cn_names
    from app.common.countries import get_country
    from app.models.fiat import FiatOrder
    from app.models.user import User
    from app.utils.export import ExcelExporter
    from app.utils.date_ import timestamp_to_date

    from app.utils.iterable import batch_iter
    from app.utils.upload import upload_file

    start_date = timestamp_to_date(start_ts)
    end_date = timestamp_to_date(end_ts)

    orders = FiatOrder.query.filter(
        FiatOrder.created_at >= start_date,
        FiatOrder.created_at < end_date,
    ).order_by(FiatOrder.id.desc()).with_entities(
        FiatOrder.created_at,
        FiatOrder.user_id,
        FiatOrder.order_type,
        FiatOrder.asset,
        FiatOrder.coin_amount,
        FiatOrder.fiat_currency,
        FiatOrder.fiat_total_amount,
        FiatOrder.third_party,
        FiatOrder.status
    ).all()
    user_ids = {order.user_id for order in orders}
    excluded_user_ids = set()
    for ids in batch_iter(user_ids, 5000):
        tmp = FiatOrder.query.filter(
            FiatOrder.created_at < start_date,
            FiatOrder.user_id.in_(ids),
        ).with_entities(
            FiatOrder.user_id.distinct().label('user_id'),
        ).all()
        excluded_user_ids |= {t.user_id for t in tmp}

    user_ids -= excluded_user_ids
    res = []

    status_map = {
        'CREATE': '已创建',
        'APPROVED': '已完成',
        'PENDING': '处理中',
        'DECLINED': '已拒绝',
        'REFUNDED': '已退款',
    }
    order_type_map = {
        'BUY': '买',
        'SELL': '卖',
    }

    lang_map = dict()
    country_map = dict()
    for ids in batch_iter(user_ids, 1000):
        tmp = UserExport.get_user_lang_mapper(user_ids)
        lang_map.update(tmp)
        country_tmp = User.query.filter(User.id.in_(ids)).with_entities(
            User.id,
            User.location_code,
        ).all()
        country_map.update(country_tmp)


    lang_cn_map = language_cn_names()

    for order in orders:
        if order.user_id not in user_ids:
            continue

        country_code = country_map.get(order.user_id)
        if country_code:
            country = get_country(country_code).cn_name
        else:
            country = ''
        res.append(dict(
            created_at=order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            user_id=order.user_id,
            order_type=order_type_map[order.order_type.name],
            asset=order.asset,
            coin_amount=order.coin_amount,
            fiat_currency=order.fiat_currency,
            fiat_total_amount=order.fiat_total_amount,
            third_party=order.third_party,
            status=status_map[order.status.name],
            lang=lang_cn_map.get(lang_map.get(order.user_id)),
            country=country
        ))

    res.sort(key=lambda x: x['user_id'])
    header_data = [
            ('user_id', '用户ID'),
            ('created_at', '创建时间'),
            ('order_type', '方向'),
            ('asset', '币种'),
            ('coin_amount', '币种数量'),
            ('fiat_currency', '使用法币'),
            ('fiat_total_amount', '法币金额'),
            ('third_party', '服务商'),
            ('status', '订单状态'),
            ('lang', '语言'),
            ('country', '国家'),
        ]
    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]

    streams = ExcelExporter(
        data_list=res, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)

if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
