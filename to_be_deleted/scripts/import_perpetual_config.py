import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from app.common import PerpetualMarketType
from app.models import PerpetualAssetIndex, db, PerpetualMarketIndex, \
    PerpetualMarketIndexDetail, PerpetualComposeIndex, \
    PerpetualMarket, PerpetualMarketLimitConfig

PERPETUAL_MARKET = {
    'BTCUSD': {
        'merge': ['0.1', '1', '10'],
        'default_merge': '0.1'
    },
    'BCHUSD': {
        'merge': ['0.01', '0.1', '1', '10'],
        'default_merge': '0.01'
    },
    'ETHUSD': {
        'merge': ['0.01', '0.1', '1', '10'],
        'default_merge': '0.01'
    },
    "LTCUSD": {
        'merge': ['0.01', '0.1', '1'],
        'default_merge': '0.01',
    },
    "BSVUSD": {
        'merge': ['0.01', '0.1', '1', "10"],
        'default_merge': '0.01',
    },
    "XRPUSD": {
        "merge": ["0.0001", "0.001", "0.01"],
        "default_merge": "0.0001",
    },
    "EOSUSD": {
        "merge": ["0.001", "0.01", "0.1"],
        "default_merge": "0.001",
    },
    "TRXUSD": {
        "merge": ["0.00001", "0.0001", "0.001"],
        "default_merge": "0.00001",
    },
    'BTCUSDT': {
        'merge': ['0.01', '0.1', '1', '10', '100'],
        'default_merge': '0.1'
    },
    'BCHUSDT': {
        'merge': ['0.01', '0.1', '1', ],
        'default_merge': '0.01'
    },
    'ETHUSDT': {
        'merge': ['0.01', '0.1', '1', '10'],
        'default_merge': '0.01'
    },
    "LTCUSDT": {
        'merge': ['0.01', '0.1', '1'],
        'default_merge': '0.01',
    },
    'default': {
        'merge': ['0.0001', '0.001', '0.01', '0.1', '1'],
        'default_merge': '1'
    }
}

ASSETS_CONFIG = [
    {
        "name": "BTC",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    },
    {
        "name": "BCH",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    },
    {
        "name": "ETH",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    },
    {
        "name": "LTC",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    },
    {
        "name": "USD",
        "show": False,
        "prec_save": 20,
        "prec_show": 4
    },
    {
        "name": "BSV",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    },
    {
        "name": "XRP",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    },
    {
        "name": "EOS",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    },
    {
        "name": "CET",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    },
    {
        "name": "TRX",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    },
    {
        "name": "USDT",
        "show": True,
        "prec_save": 20,
        "prec_show": 8
    }
]



MARKET_CONFIG = [
    {
        "name": "BTCUSD",
        "type": 2,
        "fee_prec": 5,
        "amount_prec": 0,
        "stock": {
            "name": "BTC",
            "prec": 8
        },
        "money": {
            "name": "USD",
            "prec": 1
        },
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50", "100"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100001", "100", "0.005"],
            ["1000001", "50", "0.01"],
            ["2500001", "30", "0.015"],
            ["5000001", "20", "0.02"],
            ["7500001", "15", "0.025"],
            ["10000001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "1000",
        "amount_min": "10",
        "price_size": "0.1",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"

    },
    {
        "name": "ETHUSD",
        "type": 2,
        "fee_prec": 5,
        "amount_prec": 0,
        "amount_min": "10",
        "price_size": "0.01",
        "stock": {
            "name": "ETH",
            "prec": 8
        },
        "money": {
            "name": "USD",
            "prec": 2
        },
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100001", "50", "0.01"],
            ["500001", "30", "0.015"],
            ["1000001", "20", "0.02"],
            ["2500001", "15", "0.025"],
            ["5000001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "1000",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
    },
    {
        "name": "BCHUSD",
        "type": 2,
        "fee_prec": 5,
        "amount_prec": 0,
        "amount_min": "10",
        "price_size": "0.01",
        "stock": {
            "name": "BCH",
            "prec": 8
        },
        "money": {
            "name": "USD",
            "prec": 2
        },
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100001", "50", "0.01"],
            ["250001", "30", "0.015"],
            ["500001", "20", "0.02"],
            ["1000001", "15", "0.025"],
            ["2000001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "1000",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
    },
    {
        "name": "LTCUSD",
        "type": 2,
        "fee_prec": 5,
        "amount_prec": 0,
        "amount_min": "10",
        "price_size": "0.01",
        "stock": {
            "name": "LTC",
            "prec": 8
        },
        "money": {
            "name": "USD",
            "prec": 2
        },
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100001", "50", "0.01"],
            ["250001", "30", "0.015"],
            ["500001", "20", "0.02"],
            ["1000001", "15", "0.025"],
            ["2000001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "1000",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
   },
    {
        "name": "BSVUSD",
        "type": 2,
        "fee_prec": 5,
        "amount_prec": 0,
        "amount_min": "10",
        "price_size": "0.01",
        "stock": {
            "name": "BSV",
            "prec": 8
        },
        "money": {
            "name": "USD",
            "prec": 2
        },
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100001", "50", "0.01"],
            ["250001", "30", "0.015"],
            ["500001", "20", "0.02"],
            ["750001", "15", "0.025"],
            ["1000001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "1000",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
    },
    {
        "name": "XRPUSD",
        "type": 2,
        "fee_prec": 5,
        "amount_prec": 0,
        "amount_min": "10",
        "price_size": "0.0001",
        "stock": {
            "name": "XRP",
            "prec": 8
        },
        "money": {
            "name": "USD",
            "prec": 4
        },
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100001", "50", "0.01"],
            ["250001", "30", "0.015"],
            ["500001", "20", "0.02"],
            ["750001", "15", "0.025"],
            ["1000001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "1000",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
    },
    {
        "name": "EOSUSD",
        "type": 2,
        "fee_prec": 5,
        "amount_prec": 0,
        "amount_min": "10",
        "price_size": "0.001",
        "stock": {
            "name": "EOS",
            "prec": 8
        },
        "money": {
            "name": "USD",
            "prec": 3
        },
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100001", "50", "0.01"],
            ["250001", "30", "0.015"],
            ["500001", "20", "0.02"],
            ["750001", "15", "0.025"],
            ["1000001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "1000",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
    },
    {
        "name": "TRXUSD",
        "type": 2,
        "fee_prec": 5,
        "amount_prec": 0,
        "stock": {
            "name": "TRX",
            "prec": 8
        },
        "money": {
            "name": "USD",
            "prec": 5
        },
        "price_size": "0.00001",
        "amount_min": "1",
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100001", "50", "0.01"],
            ["250001", "30", "0.015"],
            ["500001", "20", "0.02"],
            ["750001", "15", "0.025"],
            ["1000001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "1000",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
     },
     {
        "name": "BTCUSDT",
        "type": 1,
        "fee_prec": 5,
        "amount_prec": 4,
        "stock": {
            "name": "BTC",
            "prec": 8
        },
        "money": {
            "name": "USDT",
            "prec": 2
        },
        "price_size": "0.01",
        "amount_min": "0.0001",
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50", "100"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["10.0001", "100", "0.005"],
            ["50.0001", "50", "0.01"],
            ["100.0001", "30", "0.015"],
            ["250.0001", "20", "0.02"],
            ["500.0001", "15", "0.025"],
            ["1000.0001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "0.1",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
     },
     {
        "name": "ETHUSDT",
        "type": 1,
        "fee_prec": 5,
        "amount_prec": 3,
        "stock": {
            "name": "ETH",
            "prec": 8
        },
        "money": {
            "name": "USDT",
            "prec": 2
        },
        "price_size": "0.01",
        "amount_min": "0.001",
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100.001", "50", "0.01"],
            ["500.001", "30", "0.015"],
            ["1000.001", "20", "0.02"],
            ["2500.001", "15", "0.025"],
            ["5000.001", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "2",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
     },
     {
        "name": "BCHUSDT",
        "type": 1,
        "fee_prec": 5,
        "amount_prec": 2,
        "stock": {
            "name": "BCH",
            "prec": 8
        },
        "money": {
            "name": "USDT",
            "prec": 2
        },
        "price_size": "0.01",
        "amount_min": "0.01",
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["100.01", "50", "0.01"],
            ["500.01", "30", "0.015"],
            ["1000.01", "20", "0.02"],
            ["2500.01", "15", "0.025"],
            ["5000.01", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "5",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
     },
     {
        "name": "LTCUSDT",
        "type": 1,
        "fee_prec": 5,
        "amount_prec": 2,
        "stock": {
            "name": "LTC",
            "prec": 8
        },
        "money": {
            "name": "USDT",
            "prec": 2
        },
        "price_size": "0.01",
        "amount_min": "0.01",
        "multiplier": "1",
        "leverages": [
            "3", "5", "8", "10", "15", "20", "30", "50"
        ],
        "leverage_default": "3",
        "position_type_default": 2,
        "limits": [
            ["500.01", "50", "0.01"],
            ["5000.01", "30", "0.015"],
            ["10000.01", "20", "0.02"],
            ["15000.01", "15", "0.025"],
            ["20000.01", "10", "0.03"]
        ],
        "interest": "0",
        "impact_amount": "10",
        "funding_start": 0,
        "funding_interval": 8,
        "funding_rate_prec": 8,
        "liq_risk_alert": "0.7",
        "liq_order_keeptime": 60,
        "liq_price_diff": "0.02",
        "liq_price_max": "0.1"
     }
]




INDEX_CONFIG = {
    "BTCUSD": {
            "name": "BTCUSD",
            "name_show": "BTCUSD",
            "prec": 4,
            "sources": [
                {
                    "exchange": "bitstamp",
                    "trade_url": "https://www.bitstamp.net/api/v2/transactions/btcusd/?time=minute",
                    "weight": "0.25",
                    "trade_pair": "BTCUSD"
                },
                {
                    "exchange": "kraken",
                    "trade_url": "https://api.kraken.com/0/public/Trades?pair=XBTUSD",
                    "weight": "0.25",
                    "trade_pair": "XXBTZUSD"
                },
                {
                    "exchange": "coinbase",
                    "trade_url": "https://api.pro.coinbase.com/products/BTC-USD/trades?limit=20",
                    "weight": "0.25",
                    "trade_pair": "BTC-USD"
                },
                {
                    "exchange": "gemini",
                    "trade_url": "https://api.gemini.com/v1/trades/btcusd",
                    "weight": "0.25",
                    "trade_pair": "btcusd"
                }
            ]
    },
    "BCHUSD": {
           "name": "BCHUSD",
           "name_show": "BCHUSD",
           "prec": 2,
           "sources": [
               {
                   "exchange": "bitstamp",
                   "trade_url": "https://www.bitstamp.net/api/v2/transactions/bchusd/?time=hour",
                   "weight": "0.25",
                   "trade_pair": "BCHUSD"
               },
               {
                   "exchange": "coinbase",
                   "trade_url": "https://api.pro.coinbase.com/products/BCH-USD/trades?limit=20",
                   "weight": "0.25",
                   "trade_pair": "BCH-USD"
               },
               {
                   "exchange": "gemini",
                   "trade_url": "https://api.gemini.com/v1/trades/bchusd",
                   "weight": "0.25",
                   "trade_pair": "bchusd"
               },
               {
                   "exchange": "kraken",
                   "trade_url": "https://api.kraken.com/0/public/Trades?pair=BCHUSD",
                   "weight": "0.25",
                   "trade_pair": "BCHUSD"
               }
           ]
    },
    "ETHUSD": {
        "name": "ETHUSD",
        "name_show": "ETHUSD",
        "prec": 2,
        "sources": [
            {
                "exchange": "bitstamp",
                "trade_url": "https://www.bitstamp.net/api/v2/transactions/ethusd/?time=hour",
                "weight": "0.25",
                "trade_pair": "ETHUSD"
            },
            {
                "exchange": "kraken",
                "trade_url": "https://api.kraken.com/0/public/Trades?pair=XETHZUSD",
                "weight": "0.25",
                "trade_pair": "XETHZUSD"
            },
            {
                "exchange": "coinbase",
                "trade_url": "https://api.pro.coinbase.com/products/ETH-USD/trades?limit=20",
                "weight": "0.25",
                "trade_pair": "ETH-USD"
            },
            {
                "exchange": "gemini",
                "trade_url": "https://api.gemini.com/v1/trades/ethusd",
                "weight": "0.25",
                "trade_pair": "ethusd"
            }
        ]
    },
    "LTCUSD": {
        "name": "LTCUSD",
        "name_show": "LTCUSD",
        "prec": 2,
        "sources": [
             {
                "exchange": "bitstamp",
                "trade_url": "https://www.bitstamp.net/api/v2/transactions/ltcusd/?time=hour",
                "weight": "0.25",
                "trade_pair": "LTCUSD"
            },
            {
                "exchange": "kraken",
                "trade_url": "https://api.kraken.com/0/public/Trades?pair=LTCUSD",
                "weight": "0.25",
                "trade_pair": "XLTCZUSD"
            },
            {
                "exchange": "coinbase",
                "trade_url": "https://api.pro.coinbase.com/products/LTC-USD/trades?limit=20",
                "weight": "0.25",
                "trade_pair": "LTC-USD"
            },
            {
                "exchange": "gemini",
                "trade_url": "https://api.gemini.com/v1/trades/ltcusd",
                "weight": "0.25",
                "trade_pair": "ltcusd"
            }
        ]
    },
    "BSVBTC": {
        "name": "BSVBTC",
        "name_show": "BSVUSD",
        "prec": 8,
        "sources": [
            {
                "exchange": "coinex",
                "trade_url": "http://172.31.25.187:80/v1/market/deals?market=bsvbtc&limit=1",
                "weight": "0.3333",
                "trade_pair": "bsvbtc"
            },
            {
                "exchange": "huobiglobal",
                "trade_url": "https://api.huobi.pro/market/trade?symbol=bsvbtc",
                "weight": "0.3333",
                "trade_pair": "bsvbtc"
            },
            {
                "exchange": "okex",
                "trade_url": "https://www.okex.com/api/spot/v3/instruments/BSV-BTC/trades?limit=1",
                "weight": "0.3333",
                "trade_pair": "bsvbtc"
            }
        ]
    },
    "BSVUSD": {
        "show": False,
        "name": "BSVUSD",
        "compose_first_market":"BSVBTC",
        "compose_methd": 1,
        "compose_second_market":"BTCUSD",
        "prec": 2,
        "use_compose":True
    },
    "XRPBTC": {
        "name": "XRPBTC",
        "name_show": "XRPUSD",
        "prec": 8,
        "sources": [
            {
                "exchange": "binance",
                "trade_url": "https://api.binance.com/api/v1/trades?symbol=XRPBTC&limit=1",
                "weight": "0.3333",
                "trade_pair": "xrpbtc"
            },
            {
                "exchange": "huobiglobal",
                "trade_url": "https://api.huobi.pro/market/trade?symbol=xrpbtc",
                "weight": "0.3333",
                "trade_pair": "xrpbtc"
            },
            {
                "exchange": "okex",
                "trade_url": "https://www.okex.com/api/spot/v3/instruments/XRP-BTC/trades?limit=1",
                "weight": "0.3333",
                "trade_pair": "xrpbtc"
            }
        ]
    },
    "XRPUSD": {
        "show": False,
        "name": "XRPUSD",
        "compose_first_market":"XRPBTC",
        "compose_methd": 1,
        "compose_second_market":"BTCUSD",
        "prec": 4,
        "use_compose":True
    },
    "EOSBTC": {
        "name": "EOSBTC",
        "name_show": "EOSUSD",
        "prec": 8,
        "sources": [
            {
                "exchange": "coinex",
                "trade_url": "http://172.31.25.187:80/v1/market/deals?market=eosbtc&limit=1",
                "weight": "0.25",
                "trade_pair": "eosbtc"
            },
            {
                "exchange": "huobiglobal",
                "trade_url": "https://api.huobi.pro/market/trade?symbol=eosbtc",
                "weight": "0.25",
                "trade_pair": "eosbtc"
            },
            {
                "exchange": "okex",
                "trade_url": "https://www.okex.com/api/spot/v3/instruments/EOS-BTC/trades?limit=1",
                "weight": "0.25",
                "trade_pair": "eosbtc"
            },
            {
                "exchange": "binance",
                "trade_url": "https://api.binance.com/api/v1/trades?symbol=EOSBTC&limit=1",
                "weight": "0.25",
                "trade_pair": "eosbtc"
            }
        ]
    },
    "EOSUSD": {
        "show": False,
        "name": "EOSUSD",
        "compose_first_market":"EOSBTC",
        "compose_methd": 1,
        "compose_second_market":"BTCUSD",
        "prec": 3,
        "use_compose":True
    },
    "TRXBTC": {
        "name": "TRXBTC",
        "name_show": "TRXUSD",
        "prec": 10,
        "sources": [
            {
                "exchange": "coinex",
                "trade_url": "http://172.31.25.187:80/v1/market/deals?market=trxbtc&limit=1",
                "weight": "0.3333",
                "trade_pair": "trxbtc"
            },
            {
                "exchange": "huobiglobal",
                "trade_url": "https://api.huobi.pro/market/trade?symbol=trxbtc",
                "weight": "0.3333",
                "trade_pair": "trxbtc"
            },
            {
                "exchange": "binance",
                "trade_url": "https://api.binance.com/api/v1/trades?symbol=TRXBTC&limit=1",
                "weight": "0.3333",
                "trade_pair": "trxbtc"
            }
        ]
    },
    "TRXUSD": {
        "show": False,
        "name": "TRXUSD",
        "compose_first_market":"TRXBTC",
        "compose_methd": 1,
        "compose_second_market":"BTCUSD",
        "prec": 5,
        "use_compose":True
    },
    "BTCUSDT": {
        "name": "BTCUSDT",
        "name_show": "BTCUSDT",
        "prec": 8,
        "sources": [
            {
                "exchange": "coinex",
                "trade_url": "http://172.31.25.187:80/v1/market/deals?market=btcusdt&limit=1",
                "weight": "0.25",
                "trade_pair": "btcusdt"
            },
            {
                "exchange": "huobiglobal",
                "trade_url": "https://api.huobi.pro/market/trade?symbol=btcusdt",
                "weight": "0.25",
                "trade_pair": "btcusdt"
            },
            {
                "exchange": "binance",
                "trade_url": "https://api.binance.com/api/v1/trades?symbol=BTCUSDT&limit=1",
                "weight": "0.25",
                "trade_pair": "btcusdt"
            },
            {
                "exchange": "okex",
                "trade_url": "https://www.okex.com/api/spot/v3/instruments/BTC-USDT/trades?limit=1",
                "weight": "0.25",
                "trade_pair": "btcusdt"
            }
        ]
    },
    "ETHUSDT": {
        "name": "ETHUSDT",
        "name_show": "ETHUSDT",
        "prec": 8,
        "sources": [
            {
                "exchange": "coinex",
                "trade_url": "http://172.31.25.187:80/v1/market/deals?market=ethusdt&limit=1",
                "weight": "0.25",
                "trade_pair": "ethusdt"
            },
            {
                "exchange": "huobiglobal",
                "trade_url": "https://api.huobi.pro/market/trade?symbol=ethusdt",
                "weight": "0.25",
                "trade_pair": "ethusdt"
            },
            {
                "exchange": "binance",
                "trade_url": "https://api.binance.com/api/v1/trades?symbol=ETHUSDT&limit=1",
                "weight": "0.25",
                "trade_pair": "ethusdt"
            },
            {
                "exchange": "okex",
                "trade_url": "https://www.okex.com/api/spot/v3/instruments/ETH-USDT/trades?limit=1",
                "weight": "0.25",
                "trade_pair": "ethusdt"
            }
        ]
    },
    "BCHUSDT": {
        "name": "BCHUSDT",
        "name_show": "BCHUSDT",
        "prec": 8,
        "sources": [
            {
                "exchange": "coinex",
                "trade_url": "http://172.31.25.187:80/v1/market/deals?market=bchusdt&limit=1",
                "weight": "0.25",
                "trade_pair": "bchusdt"
            },
            {
                "exchange": "huobiglobal",
                "trade_url": "https://api.huobi.pro/market/trade?symbol=bchusdt",
                "weight": "0.25",
                "trade_pair": "bchusdt"
            },
            {
                "exchange": "binance",
                "trade_url": "https://api.binance.com/api/v1/trades?symbol=BCHUSDT&limit=1",
                "weight": "0.25",
                "trade_pair": "bchusdt"
            },
            {
                "exchange": "okex",
                "trade_url": "https://www.okex.com/api/spot/v3/instruments/BCH-USDT/trades?limit=1",
                "weight": "0.25",
                "trade_pair": "bchusdt"
            }
        ]
    },
    "LTCUSDT": {
        "name": "LTCUSDT",
        "name_show": "LTCUSDT",
        "prec": 8,
        "sources": [
            {
                "exchange": "coinex",
                "trade_url": "http://172.31.25.187:80/v1/market/deals?market=ltcusdt&limit=1",
                "weight": "0.25",
                "trade_pair": "ltcusdt"
            },
            {
                "exchange": "huobiglobal",
                "trade_url": "https://api.huobi.pro/market/trade?symbol=ltcusdt",
                "weight": "0.25",
                "trade_pair": "ltcusdt"
            },
            {
                "exchange": "binance",
                "trade_url": "https://api.binance.com/api/v1/trades?symbol=LTCUSDT&limit=1",
                "weight": "0.25",
                "trade_pair": "ltcusdt"
            },
            {
                "exchange": "okex",
                "trade_url": "https://www.okex.com/api/spot/v3/instruments/LTC-USDT/trades?limit=1",
                "weight": "0.25",
                "trade_pair": "ltcusdt"
            }
        ]
    }
}



def decimal_list_to_str(data):
    return ','.join(data)


def main():

    for i in ASSETS_CONFIG:
        db.session.add(
            PerpetualAssetIndex(
                name=i['name'],
                show_precision=i['prec_show'],
                save_precision=i['prec_save'],
                is_visible=i['show'],
                status=PerpetualAssetIndex.StatusType.OPEN,
            )
        )
    db.session.commit()

    for i in INDEX_CONFIG.values():
        # 普通指数
        if not i.get('use_compose'):
            record = PerpetualMarketIndex(
                    name=i['name'],
                    name_show=i['name_show'],
                    price_precision=i['prec'],
                    is_visible=True,
                    status=PerpetualMarketIndex.StatusType.OPEN,
                )
            db.session.add(record)
            db.session.flush()
            for _source in i['sources']:
                db.session.add(PerpetualMarketIndexDetail(
                    perpetual_market_index_id=record.id,
                    exchange_name=_source['exchange'],
                    market=_source['trade_pair'],
                    url=_source['trade_url'],
                    weight=_source['weight'],
                    status=PerpetualMarketIndexDetail.StatusType.PASS,
                ))
        # 合成指数
        else:
            db.session.add(PerpetualComposeIndex(
                name=i['name'],
                is_visible=i['show'],
                price_precision=i['prec'],
                first_market=i['compose_first_market'],
                second_market=i['compose_second_market'],
                compose_method=PerpetualComposeIndex.ComposeMethodType.MULTI
                if i['compose_methd'] == 1 else PerpetualComposeIndex.ComposeMethodType.DIV,
                status=PerpetualComposeIndex.StatusType.OPEN
            ))

    db.session.commit()

    for i in MARKET_CONFIG:

        config_data = PERPETUAL_MARKET.get(i['name']) or \
                      PERPETUAL_MARKET['default']
        depths = decimal_list_to_str(config_data['merge'])
        depth_default = config_data['default_merge']

        record = PerpetualMarket(
            name=i['name'],
            market_type=PerpetualMarketType.DIRECT if i['type'] == 1 else PerpetualMarketType.INVERSE,
            fee_precision=i['fee_prec'],
            amount_precision=i['amount_prec'],
            base_asset=i['stock']['name'],
            base_asset_precision=i['stock']['prec'],
            quote_asset=i['money']['name'],
            quote_asset_precision=i['money']['prec'],
            price_size=i['price_size'],
            min_order_amount=i['amount_min'],
            multiplier=i['multiplier'],
            leverages=decimal_list_to_str(i['leverages']),
            depths=depths,
            leverage_default=i['leverage_default'],
            depth_default=depth_default,
            position_type_default=PerpetualMarket.MarginType.ISOLATED if i['position_type_default'] == 1 else PerpetualMarket.MarginType.CROSS,
            impact_amount=i['impact_amount'],
            funding_start=i['funding_start'],
            funding_interval=i['funding_interval'],
            funding_rate_precision=i['funding_rate_prec'],
            liq_risk_alert=i['liq_risk_alert'],
            liq_order_keeptime=i['liq_order_keeptime'],
            diff_liq_price=i['liq_price_diff'],
            max_liq_price=i['liq_price_max'],
            interest=PerpetualMarket.DEFAULT_INTEREST,
            status=PerpetualMarket.StatusType.OPEN
        )
        db.session.add(record)
        db.session.flush()
        for _limit in i['limits']:
            db.session.add(PerpetualMarketLimitConfig(
                perpetual_market_id=record.id,
                max_amount=_limit[0],
                max_leverage=_limit[1],
                mainten_margin_rate=_limit[2],
            ))
    db.session.commit()


if __name__ == '__main__':
    from app import create_app
    app = create_app()
    with app.app_context():
        main()
