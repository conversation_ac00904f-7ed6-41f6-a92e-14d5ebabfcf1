# -*- coding: utf-8 -*-

import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from app.models import AdminPermission, db


def add_new_permission():
    admin_permission_list = [
        ('权限管理-管理员列表', '管理员列表', 'GET', '/admin/auth/admin-users'),
        ('权限管理-管理员列表', '添加管理员', 'POST', '/admin/auth/admin-users'),
        ('权限管理-管理员列表', '编辑管理员', 'POST', '/admin/auth/admin-users/[0-9]*'),
        ('权限管理-管理员列表', '管理员权限', 'GET', '/admin/auth/admin-users/[0-9]*/roles'),
        ('权限管理-管理员列表', '编辑管理员权限', 'PUT', '/admin/auth/admin-users/[0-9]*/roles'),
        ('权限管理-角色列表', '角色列表', 'GET', ' /admin/auth/roles'),
        ('权限管理-角色列表', '添加角色', 'POST', '/admin/auth/roles'),
        ('权限管理-角色列表', '编辑角色', 'PUT', '/admin/auth/roles/[0-9]*'),
        ('权限管理-角色列表', '获取角色权限', 'GET', '/admin/auth/roles/[0-9]*/permissions'),
        ('权限管理-角色列表', '设置角色权限', 'PUT', '/admin/auth/roles/[0-9]*/permissions'),
        ('权限管理-角色列表', '角色所属用户', 'GET', '/admin/auth/roles/[0-9]*/users'),
        ('权限管理-权限列表', '权限列表', 'GET', '/admin/auth/permissions'),
        ('权限管理-权限列表', '编辑权限', 'GET', '/admin/auth/permissions/[0-9]*'),
        ('权限管理-权限列表', '权限所属角色', 'GET', ' /admin/auth/permissions/[0-9]*/roles'),
    ]
    for permission in admin_permission_list:
        affiliation, name, method, route = permission

        permission_info = AdminPermission.query.filter(
            AdminPermission.method == method,
            AdminPermission.route == route
        ).first()

        if not permission_info:
            new_permission = AdminPermission()
            new_permission.affiliation = affiliation
            new_permission.name = name
            new_permission.method = method
            new_permission.route = route
            new_permission.op_user = 'system'
            db.session.add(new_permission)
        else:
            permission_info.affiliation = affiliation
            permission_info.name = name
            permission_info.method = method
            permission_info.route = route
            permission_info.op_user = 'system'
            db.session.add(permission_info)
            # print(f'{method} {route} 已存在相同权限! 请检查新增权限！')
    db.session.commit()


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        add_new_permission()
