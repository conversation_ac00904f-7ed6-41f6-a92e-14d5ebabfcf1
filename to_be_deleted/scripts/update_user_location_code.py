# -*- coding: utf-8 -*-
import os
import sys
from decimal import Decimal

import click
from sqlalchemy import or_

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def get_from_login_history(user_id):
    from app.utils.ip import GeoIP
    from app.models import LoginHistory

    rows = LoginHistory.query.filter(LoginHistory.user_id == user_id) \
                      .order_by(LoginHistory.id).all()
    for row in rows:
        if country := GeoIP(row.ip).country_code:
            return country
    return None


def get_from_login_state(user_id):
    from app.utils.ip import GeoIP
    from app.models import UserLoginState

    rows = UserLoginState.query.filter(UserLoginState.user_id == user_id) \
                         .order_by(UserLoginState.id).all()
    for row in rows:
        if country := GeoIP(row.ip).country_code:
            return country
    return None


def get_from_sub_account(user):
    from app.utils.ip import GeoIP
    from app.utils.mobile import get_mobile_country

    for sub in user.sub_accounts:
        user = sub.user
        if reg_country := GeoIP(user.registration_ip).country_code:
            return reg_country
        if mob_country := get_mobile_country(user.mobile):
            return mob_country
        if login_country := get_from_login_history(user.id):
            return login_country
        if login_country := get_from_login_state(user.id):
            return login_country
    return None


def update_user_location(user):
    from app.models import db, LoginHistory
    from app.utils.ip import GeoIP
    from app.utils.mobile import get_mobile_country
    from app.common import get_country

    location = None
    if user.is_sub_account:
        location = user.main_user.location_code
    elif (kyc := user.kyc_verification) is not None:
        location = kyc.country
    elif reg_country := GeoIP(user.registration_ip).country_code:
        location = reg_country
    elif mob_country := get_mobile_country(user.mobile):
        location = mob_country
    elif login_country := get_from_login_history(user.id):
        location = login_country
    elif login_country := get_from_login_state(user.id):
        location = login_country
    elif sub_country := get_from_sub_account(user):
        location = sub_country

    if not location:
        print(f'{user.id} {user.registration_ip} location not found')
        return None

    if not (country := get_country(location)):
        print(f'{user.id} {user.registration_ip} country not found')
        return None

    user.location_code = code = country.iso_3
    db.session.commit()
    return code


def main():
    from app.models import User

    users = User.query.filter(or_(User.location_code == None, User.location_code == '')).all()
    users.sort(key=lambda x: 1 if x.user_type == User.UserType.SUB_ACCOUNT else 0)

    total = len(users)
    success = 0
    for user in users:
        if update_user_location(user):
            success += 1

    print(f'total {total}, success {success}')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
