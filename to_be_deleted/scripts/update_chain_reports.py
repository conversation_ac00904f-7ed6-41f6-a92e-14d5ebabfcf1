import datetime
import os
import sys

from tqdm import tqdm

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    update_daily_reports()
    update_monthly_reports()


def update_daily_reports():
    from flask import current_app

    from app.common import ReportType
    from app.models import (DailyChainDepositWithdrawalReport as DailyChainModel)
    today = datetime.datetime.utcnow().date()
    last_record = DailyChainModel.query.order_by(DailyChainModel.report_date.asc()).first()
    start_date = last_record.report_date
    total = (today - start_date).days
    process_bar = tqdm(total=total)
    print('开始更新公链日报表...')
    while start_date < today:
        end_date = start_date + datetime.timedelta(days=1)
        try:
            _update_chain_deposit_withdrawal(start_date, end_date, ReportType.DAILY)
        except Exception as ex:
            import traceback
            trace_info = traceback.format_exc()
            msg = f'update_daily_chain_deposit_withdrawal_schedule {start_date} ' \
                  f'exception, ex: {str(ex)}, trace_info: {trace_info}'
            current_app.logger.error(msg)
        start_date = end_date
        process_bar.update(1)
    print('公链日报表更新完毕')


def _update_chain_deposit_withdrawal(start_date, end_date, report_type):
    from collections import defaultdict
    from decimal import Decimal
    from app.schedules.reports.deposit_withdrawal import chains_with_main_asset
    from app.business import (WalletClient)
    from app.common import ReportType
    from app.models import (db, AssetPrice, Deposit,
                            Withdrawal, DailyChainDepositWithdrawalReport as DailyChainModel,
                            MonthlyChainDepositWithdrawalReport as MonthlyChainModel)
    from app.utils.date_ import date_to_datetime
    model = DailyChainModel if report_type == ReportType.DAILY else MonthlyChainModel
    token_deposit_count, main_asset_deposit_count, deposit_user_count = \
        defaultdict(int), defaultdict(int), defaultdict(set)
    token_withdrawal_count, main_asset_withdrawal_count, withdrawal_user_count = \
        defaultdict(int), defaultdict(int), defaultdict(set)
    gas_dict, gas_usd = defaultdict(dict), defaultdict(Decimal)

    deposits = Deposit.query.filter(
        start_date <= Deposit.created_at,
        Deposit.created_at < end_date,
        Deposit.chain.isnot(None),
        Deposit.status.in_([
            Deposit.Status.CONFIRMING,
            Deposit.Status.FINISHED,
            Deposit.Status.TO_HOT])
    ).all()
    withdrawals = Withdrawal.query.filter(
        start_date <= Withdrawal.sent_at,
        Withdrawal.sent_at < end_date,
        Withdrawal.chain.isnot(None),
        Withdrawal.type == Withdrawal.Type.ON_CHAIN,
        Withdrawal.status.in_([
            Withdrawal.Status.CONFIRMING,
            Withdrawal.Status.FINISHED])
    ).all()

    client = WalletClient()
    start_ts = int(date_to_datetime(start_date).timestamp())
    end_ts = int(date_to_datetime(end_date).timestamp())
    chain_gas_collection = client.get_chain_gas_fees(start_ts, end_ts) or {}
    tx_count_collection = client.get_tx_counts(start_ts, end_ts) or {}
    deposit_chains = {x.chain for x in deposits}
    withdrawal_chains = {x.chain for x in withdrawals}
    gas_chains = {x for x in chain_gas_collection}
    tx_chains = {x for x in tx_count_collection}
    chains = deposit_chains | withdrawal_chains | gas_chains | tx_chains
    chains_main_asset, deleted_chains = chains_with_main_asset(chains)
    chains -= deleted_chains    # 已经下架的链不参与统计

    for item in deposits:
        chain, asset = item.chain, item.asset
        deposit_user_count[chain].add(item.user_id)
        if chains_main_asset.get(chain, '') == asset:
            main_asset_deposit_count[chain] += 1
        else:
            token_deposit_count[chain] += 1

    chain_fee_usd_dic = defaultdict(Decimal)
    asset_rate = AssetPrice.get_close_price_map(start_date)
    for item in withdrawals:
        chain, asset, fee_asset = item.chain, item.asset, item.fee_asset
        withdrawal_user_count[item.chain].add(item.user_id)
        chain_fee_usd_dic[chain] += asset_rate.get(fee_asset, 0) * item.fee
        if chains_main_asset.get(chain, '') == asset:
            main_asset_withdrawal_count[chain] += 1
        else:
            token_withdrawal_count[chain] += 1

    coin_rate = AssetPrice.get_close_price_map(start_date)
    for chain, asset_amount_list in chain_gas_collection.items():
        gas_asset = asset_amount_list[0].get('asset', '')   # 列表第一项是该链主币gas费的相关信息
        gas_asset_amount = asset_amount_list[0].get('amount', 0)
        gas_dict[chain] = {'gas_asset': gas_asset,
                           'gas_asset_amount': gas_asset_amount}
        for asset_item in asset_amount_list:
            asset, amount = asset_item['asset'], asset_item['amount']
            gas_usd[chain] += coin_rate.get(asset, 0) * amount

    total_token_deposit_count, total_main_asset_deposit_count, total_deposit_users = 0, 0, set()
    total_token_withdrawal_count, total_main_asset_withdrawal_count, total_withdrawal_users = 0, 0, set()
    total_gas_fee, total_tx_count, total_withdrawal_user_fee = Decimal(), 0, Decimal()
    for chain in chains:
        gas_fee = gas_usd.get(chain, 0)
        deposit_users = deposit_user_count[chain]
        token_deposit_c = token_deposit_count[chain]
        main_asset_deposit_c = main_asset_deposit_count[chain]
        withdrawal_users = withdrawal_user_count[chain]
        token_withdrawal_c = token_withdrawal_count[chain]
        main_asset_withdrawal_c = main_asset_withdrawal_count[chain]
        tx_count = tx_count_collection.get(chain, 0)
        withdrawal_user_fee = chain_fee_usd_dic.get(chain, 0)

        total_deposit_users.update(deposit_users)
        total_token_deposit_count += token_deposit_c
        total_main_asset_deposit_count += main_asset_deposit_c
        total_withdrawal_users.update(withdrawal_users)
        total_token_withdrawal_count += token_withdrawal_c
        total_main_asset_withdrawal_count += main_asset_withdrawal_c
        total_gas_fee += gas_fee
        total_tx_count += tx_count
        total_withdrawal_user_fee += withdrawal_user_fee

    all_row = model(
        report_date=start_date,
        chain='',
        deposit_user_count=len(total_deposit_users),
        token_deposit_count=total_token_deposit_count,
        main_asset_deposit_count=total_main_asset_deposit_count,
        withdrawal_user_count=len(total_withdrawal_users),
        token_withdrawal_count=total_token_withdrawal_count,
        main_asset_withdrawal_count=total_main_asset_withdrawal_count,
        gas_fee=total_gas_fee,
        gas_asset='',
        gas_asset_amount='',
        tx_count=total_tx_count,
        withdrawal_user_fee=total_withdrawal_user_fee
    )
    db.session.add(all_row)
    db.session.commit()


def update_monthly_reports():
    from app.models import (db, MonthlyChainDepositWithdrawalReport as MonthlyChainModel)
    from app.schedules.reports.deposit_withdrawal import update_monthly_chain_deposit_withdrawal_schedule
    MonthlyChainModel.query.delete()
    db.session.commit()
    print('开始更新公链月报表...')
    update_monthly_chain_deposit_withdrawal_schedule()
    print('公链月报表更新完毕')


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
