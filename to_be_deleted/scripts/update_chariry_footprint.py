import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

category_cn_to_en_map = {
    2: 1,
    6: 5,
    8: 7,
    11: 10,
}

footprints_cn_to_en_map = {
    63: 62,
    61: 60,
    50: 48,
    49: 47,
    51: 46,
    53: 45,
    54: 44,
    55: 43,
    56: 42,
    57: 41,
    58: 40,
    39: 38,
    59: 37,
    36: 35,
    33: 34,
    31: 32,
    29: 30,
    27: 28,
    25: 26,
    23: 24,
    21: 22,
    19: 20,
    17: 18,
    15: 16,
    13: 14,
    11: 12,
    9: 10,
    7: 8,
    5: 6,
    3: 4,
    2: 1
}


def main():
    from app.caches.operation import CharityFootprintCache
    old_new_category_dic = update_category()
    update_footprints(old_new_category_dic)
    CharityFootprintCache.reload()


def update_category():
    from app.models import CharityCategory, CharityFootprintCategory, db
    from app.common import Language
    en_remark_dic = dict()
    en_name_dic = dict()
    cn_name_dic = dict()
    sort_id_dic = dict()
    old_new_category_dic = dict()
    ori_records = CharityCategory.query.filter(
        CharityCategory.type == CharityCategory.Type.FOOTPRINT,
        CharityCategory.status == CharityCategory.Status.VALID
    ).all()
    for rec in ori_records:
        lang = rec.lang
        remark = rec.remark
        sort_id = rec.sort_id
        name = rec.name
        id_ = rec.id
        if lang == Language.EN_US:
            en_name_dic[id_] = name
            en_remark_dic[id_] = remark
            sort_id_dic[id_] = sort_id
        else:
            cn_name_dic[id_] = name
    for cn_id, en_id in category_cn_to_en_map.items():
        en_name = en_name_dic[en_id]
        cn_name = cn_name_dic[cn_id]
        remark = en_remark_dic[en_id]
        sort_id = sort_id_dic[en_id]
        row = CharityFootprintCategory(
            cn_name=cn_name,
            en_name=en_name,
            sort_id=sort_id,
            remark=remark,
        )
        db.session.add(row)
        db.session.flush()
        new_id = row.id
        old_new_category_dic[en_id] = new_id
    db.session.commit()
    print('慈善足迹类别更新完毕')
    return old_new_category_dic


def update_footprints(old_new_category_dic):
    from app.models import CharityFootprint

    footprints = CharityFootprint.query.filter(
        CharityFootprint.status == CharityFootprint.Status.ONLINE).all()
    display_name_dic, category_id_dic, read_count_dic = add_contents(footprints, old_new_category_dic)
    update_contents(footprints, display_name_dic, category_id_dic, read_count_dic)


def add_contents(footprints, old_new_category_dic):
    from app.common import Language
    from app.models import CharityFootprintContent, db
    display_name_dic = dict()
    category_id_dic = dict()
    read_count_dic = dict()
    for footprint in footprints:
        lang = footprint.lang
        id_ = footprint.id
        category_id = footprint.category_id
        if lang == Language.ZH_HANS_CN:
            en_id = footprints_cn_to_en_map[id_]
            display_name_dic[en_id] = footprint.name
            read_count_dic[en_id] = footprint.read_count
        else:
            en_id = id_
            new_category_id = old_new_category_dic[category_id]
            category_id_dic[id_] = new_category_id

        row = CharityFootprintContent(
            footprint_id=en_id,
            lang=footprint.lang,
            name=footprint.name,
            seo_url_keyword=footprint.seo_url_keyword,
            seo_title=footprint.seo_title,
            title=footprint.title,
            content=footprint.content,
            abstract=footprint.abstract,
            cover=footprint.cover,
        )
        db.session.add(row)
    db.session.commit()
    print('慈善足迹新表内容添加完毕')
    return display_name_dic, category_id_dic, read_count_dic


def update_contents(footprints, display_name_dic, category_id_dic, read_count_dic):
    from app.models import db, CharityFootprint
    from app.common import Language
    for footprint in footprints:
        lang = footprint.lang
        if lang == Language.ZH_HANS_CN:
            db.session.delete(footprint)
        else:
            display_name = display_name_dic[footprint.id]
            category_id = category_id_dic[footprint.id]
            read_count = read_count_dic[footprint.id]
            footprint.display_name = display_name
            footprint.category_id = category_id
            footprint.read_count += read_count
    CharityFootprint.query.filter(
        CharityFootprint.status == CharityFootprint.Status.OFFLINE).delete()
    db.session.commit()
    print('慈善足迹原表更新完毕')


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
