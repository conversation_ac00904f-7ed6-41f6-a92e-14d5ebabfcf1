import datetime
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    import datetime
    from collections import defaultdict

    from sqlalchemy import func

    from app.models import User, db, UserTradeSummary, ReferralHistory, \
        DailyChannelIncreaseUserReport, PublicityChannel, ShortLinkInfo, SubAccount
    from app.models import Daily<PERSON>ountryIncreaseUserReport, DailyPublicityChannelUserReport
    from app.business.export.base import add_print
    from app.schedules.reports.increase_user import UserFunnelReportHelper
    from app.utils import batch_iter
    from app.common import get_country

    @add_print
    def get_register_users():
        model = User
        row = model.query.with_entities(
            model.id,
            func.min(model.created_at).label('created_at'),
        ).filter(
            model.created_at >= epoch
        ).first()
        begin_id = row.id
        rows = model.query.with_entities(
            model.id,
            model.created_at
        ).filter(
            model.id >= begin_id
        ).all()
        ret = defaultdict(set)
        for row in rows:
            ret[row.created_at.date()].add(row.id)
        return ret

    def get_invitation_report_data(main_users_ids):
        refers = ReferralHistory.query.filter(
            ReferralHistory.referree_id.in_(main_users_ids),
            ReferralHistory.status == ReferralHistory.Status.VALID,
        ).all()
        refer_map = {i.referree_id: i.referral_type for i in refers}

        normal_user_ids = []
        normal_ref_user_ids = []
        ambassador_ref_user_ids = []
        for user_id in main_users_ids:
            # 自然注册
            if user_id not in refer_map:
                normal_user_ids.append(user_id)
                continue
            if refer_map[user_id] == ReferralHistory.ReferralType.AMBASSADOR:
                ambassador_ref_user_ids.append(user_id)  # 大使推荐
            # 普通推荐
            else:
                normal_ref_user_ids.append(user_id)

        channel_map = {
            DailyChannelIncreaseUserReport.ChannelType.ALL: main_users_ids,
            DailyChannelIncreaseUserReport.ChannelType.NONE: normal_user_ids,
            DailyChannelIncreaseUserReport.ChannelType.NORMAL: normal_ref_user_ids,
            DailyChannelIncreaseUserReport.ChannelType.AMBASSADOR: ambassador_ref_user_ids,
        }
        return channel_map

    def get_country_report_data(main_users_ids):
        country_map = defaultdict(set)
        country_map['ALL'] = set(main_users_ids)
        user_data = []
        for ids in batch_iter(main_users_ids, 5000):
            user_data += User.query.filter(
                User.id.in_(ids)
            ).with_entities(
                User.id,
                User.location_code
            ).all()

        for user in user_data:
            location = c.cn_name if (c := get_country(user.location_code)) else "其他"
            country_map[location].add(user.id)
        return country_map

    def get_publicity_report_data(main_users_ids):
        channel_map = defaultdict(set)
        publicity_channels = PublicityChannel.query.all()
        app_channels = []
        web_channel_dic = {}
        for channel in publicity_channels:
            if channel.platform == PublicityChannel.Platform.APP:
                app_channels.append(channel.fmt_publicity_channel_name)
            else:
                web_channel_dic.update({channel.id: channel.fmt_publicity_channel_name})
        short_links = ShortLinkInfo.query.filter(
            ShortLinkInfo.status == ShortLinkInfo.StatusType.VALID
        ).all()
        web_channels = []
        for short_link in short_links:
            channel = f'a{short_link.id}'
            web_channels.append(channel)
            pc_id = short_link.publicity_channel_id
            fmt_publicity_channel_name = web_channel_dic[pc_id]
            web_channel_dic.update({channel: fmt_publicity_channel_name})
        all_channels = app_channels + web_channels
        channel_users = []
        for uids in batch_iter(main_users_ids, 2000):
            rows = User.query.filter(
                User.channel.in_(all_channels),
                User.id.in_(uids)
            ).with_entities(
                User.id,
                User.channel,
            ).all()
            channel_users.extend(rows)
        channel_user_ids = set()
        for row in channel_users:
            user_id, channel = row
            channel_user_ids.add(user_id)
            if channel in web_channel_dic:
                channel_map[web_channel_dic[channel]].add(user_id)
            else:
                channel_map[channel].add(user_id)
        channel_map['全部'] = main_users_ids
        channel_map['其他'] = main_users_ids - channel_user_ids
        return channel_map

    def _aggregate_sub_user(user_set, sub_user_mapping):
        return {sub_user_mapping.get(i, i) for i in user_set}

    def _get_sub_account_map(user_ids):
        records = SubAccount.query.filter(
            SubAccount.user_id.in_(user_ids),
        ).all()
        return {i.user_id: i.main_user_id for i in records}

    epoch = datetime.datetime(2023, 1, 1)
    today_ = datetime.date.today()
    register_users = get_register_users()
    for date_, user_ids in register_users.items():
        print(f'Update {date_}...')
        if date_ >= today_:
            continue

        date_30 = date_ + datetime.timedelta(days=30)
        trade_users_30_days = set()
        sub_user_mapper = {}
        for ids in batch_iter(user_ids, 5000):
            rows = UserTradeSummary.query.filter(
                UserTradeSummary.report_date >= date_,
                UserTradeSummary.report_date < date_30,
                UserTradeSummary.user_id.in_(ids),
                UserTradeSummary.trade_amount > 0,
            ).with_entities(
                UserTradeSummary.user_id
            ).all()
            trade_users_30_days |= {row.user_id for row in rows}
            sub_user_mapper.update(_get_sub_account_map(ids))
        main_users_ids = _aggregate_sub_user(user_ids, sub_user_mapper)
        ReportType = UserFunnelReportHelper.ReportType
        for report_type in ReportType:
            if report_type is ReportType.COUNTRY:
                data = get_country_report_data(main_users_ids)
            elif report_type is ReportType.INVITATION:
                data = get_invitation_report_data(main_users_ids)
            else:
                data = get_publicity_report_data(main_users_ids)
            for key_type, user_list in data.items():
                if not user_list:
                    continue
                user_list = set(user_list)
                if report_type == ReportType.INVITATION:
                    row = DailyChannelIncreaseUserReport.get_or_create(report_date=date_, channel_type=key_type)
                elif report_type == ReportType.COUNTRY:
                    row = DailyCountryIncreaseUserReport.get_or_create(report_date=date_, country=key_type)
                else:
                    row = DailyPublicityChannelUserReport.get_or_create(report_date=date_, publicity_channel=key_type)
                row.trade_users_30_days = len(_aggregate_sub_user(trade_users_30_days & user_list, sub_user_mapper))
                db.session.add(row)
            db.session.commit()


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
