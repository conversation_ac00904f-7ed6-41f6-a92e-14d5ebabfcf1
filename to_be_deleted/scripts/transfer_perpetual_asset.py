# -*- coding: utf-8 -*-

from decimal import Decimal
from tqdm import tqdm
import os
import sys
import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def get_balance_users(asset, timestamp):
    from app.business.external_dbs import PerpetualLogDB

    table = PerpetualLogDB.slice_balance_table(timestamp)
    if not table:
        raise RuntimeError(f'no balance snapshot at {timestamp}')
    rows = table.select("distinct user_id",
                        where=f"asset='{asset}'",
                        group_by="user_id")
    return [x for x, in rows]


@click.command()
@click.argument('asset')
@click.option('--use-snapshot',
              help='Whether to use a snapshot. '
                   'e.g. "now", "1606752000", "2020-12-01"')
@click.option('--i-know-what-i-am-doing', is_flag=True)
def main(asset: str, i_know_what_i_am_doing: bool, use_snapshot: str = None):
    from app.business import PerpetualServerClient, perpetual_transfer_out
    from app.models import User
    from app.utils import (current_timestamp, timestamp_to_datetime,
                           str_to_datetime, datetime_to_str, quantize_amount)

    client = PerpetualServerClient()
    total_users = 0
    total_amount = Decimal()

    if use_snapshot is not None:
        if use_snapshot == 'now':
            timestamp = (now := current_timestamp(to_int=True)) - now % 3600
        elif use_snapshot.isdigit():
            timestamp = timestamp_to_datetime(int(use_snapshot))
        else:
            timestamp = str_to_datetime(use_snapshot)
        print(f'using snapshot of '
              f'{datetime_to_str(timestamp_to_datetime(timestamp))}')
        user_ids = get_balance_users(asset, timestamp)
    else:
        user_ids = User.query.with_entities(User.id).order_by(User.id).all()
        user_ids = [x for x, in user_ids]

    progress_bar = tqdm(total=len(user_ids))

    for user_id in user_ids:
        if user_id == 0:
            continue
        progress_bar.update(1)
        balance = client.get_user_balances(user_id, asset)[asset]
        if balance['frozen']:
            progress_bar.write(f'{user_id} has frozen {asset}; skipping')
            continue
        if (amount := balance['available']) <= 0:
            continue
        amount = quantize_amount(amount, 8)
        if amount <= 0:
            continue

        total_users += 1
        total_amount += amount
        progress_bar.write(f'{user_id}: {amount} {asset}')

        if not i_know_what_i_am_doing:
            continue

        if not perpetual_transfer_out(user_id, asset, amount):
            progress_bar.write(f'{user_id}: {amount} {asset} transfer_out error')

    progress_bar.close()
    print(f'deducted {total_amount:f} {asset} from {total_users} users')

    if not i_know_what_i_am_doing:
        print('This is DRY RUN mode. '
              'Please add "--i-know-what-i-am-doing" to run it seriously.')


if __name__ == '__main__':
    from app import create_app
    with create_app().app_context():
        main()
