# -*- coding: utf-8 -*-

from decimal import Decimal
import os
import sys

import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


class AirdropType:
    split = 'split'  # 奖励总量固定，用户按快照币种持仓比例瓜分奖励
    ratio = 'ratio'  # 根据快照币种持仓数量，按一定比例发放


@click.command()
@click.argument('airdrop_type', type=str)
@click.argument('slice_timestamp', type=int)
@click.argument('snapshot_asset', type=str)  # example "BTC"
@click.argument('min_amount', type=str)  # example "0.0001"
@click.argument('reward_asset', type=str)
@click.option('--ratio')
@click.option('--reward_amount', type=Decimal)
@click.option('--i-know-what-i-am-doing', is_flag=True)
def main(airdrop_type, slice_timestamp, snapshot_asset, min_amount, reward_asset,
         ratio, reward_amount, i_know_what_i_am_doing):
    from app.schedules.airdrop import calc_airdrop_by_ratio, calc_airdrop_by_split, send_airdrop
    from app.exceptions import InvalidArgument
    from app.models import Activity

    min_amount = Decimal(min_amount)
    if not getattr(AirdropType, airdrop_type, None):
        raise InvalidArgument(message='Wrong Airdrop Type')
    if airdrop_type == AirdropType.split and not reward_amount:
        raise InvalidArgument(message='Reward Amount Required')
    if airdrop_type == AirdropType.ratio and not ratio:
        raise InvalidArgument(message='Ratio Required')
    if ratio:
        ratio = Decimal(ratio)

    if airdrop_type == AirdropType.split:
        reward_map, credit_map = calc_airdrop_by_split(slice_timestamp, snapshot_asset, min_amount, reward_asset, reward_amount)
    elif airdrop_type == AirdropType.ratio:
        reward_map, credit_map = calc_airdrop_by_ratio(slice_timestamp, snapshot_asset, min_amount, reward_asset, ratio)

    if not i_know_what_i_am_doing:
        total = sum(reward_map.values())
        credit_total = sum(credit_map.values())
        print(f"需发放 {total} {reward_asset}，{len(reward_map)} 人，授信 {credit_total} {reward_asset}，{len(credit_map)} 人")
        print(f"实际需发放 {total+credit_total} {reward_asset}")
        print('Please add --i-know-what-i-am-doing to send aridrop')
    else:
        send_airdrop(reward_asset, reward_map, credit_map)
        print("Please execute to add balance:\n"
              f"update_gift_history_task.delay({Activity.SYSTEM_REWORD_ID}, 'gift', pay_from_admin=False)")


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
