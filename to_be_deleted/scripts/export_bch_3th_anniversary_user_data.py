# -*- coding: utf-8 -*-
import os
import sys

import click
from sqlalchemy import func, or_, and_

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from collections import defaultdict
from decimal import Decimal

from app.models import User, RedPacket, AssetPrice, Deposit, \
    Withdrawal
from app.utils import amount_to_str, ExcelExporter, \
    upload_file, now, str_to_datetime

user_ids_tmp = [491016, 471982, 14703, 537180, 511153, 509705, 531284, 549846,
                188807, 509701, 515196, 132952, 516198, 571550, 549429, 106836,
                532312, 326266, 635756, 664995, 541506, 237363, 548052, 455544,
                495862, 640168, 551600, 521869, 520431, 513923, 599648, 215948,
                528916, 110199, 26768, 94101, 549426, 521794, 229872, 33026,
                674082, 519541, 550810, 478316, 68925, 14683]


def get_all_user_info():
    user_query = User.query.filter(
        User.id.in_(user_ids_tmp),
    ).with_entities(
        User.email,
        User.id,
        User.user_type,
        User.created_at
    )
    return {
        v.id: {'email': v.email, 'user_type': v.user_type.value} for v in
        user_query
    }


def get_red_packet_data(user_ids, start_time, end_time):
    """
    发红包 - 退还
    """
    red_packet_query = RedPacket.query.filter(
        RedPacket.user_id.in_(user_ids),
        RedPacket.created_at >= start_time,
        RedPacket.created_at < end_time,
        RedPacket.status.in_([
            RedPacket.Status.FINISHED,
            RedPacket.Status.PASSED,
            RedPacket.Status.EXPIRED,
        ])
    ).with_entities(
        RedPacket.asset,
        RedPacket.user_id,
        func.sum(RedPacket.total_amount).label('amount')
    ).group_by(
        RedPacket.asset,
        RedPacket.user_id
    )
    red_packet_map = defaultdict(lambda: defaultdict(Decimal))
    for record in red_packet_query:
        red_packet_map[record.user_id][record.asset] = record.amount
    return red_packet_map


def get_net_deposit_usd(user_ids, start_time, end_time):
    rates_cache = AssetPrice.get_price_map(end_time)

    # 计算净充值
    # 充值
    deposit_query = Deposit.query.filter(
        Deposit.user_id.in_(user_ids),
        Deposit.status != Deposit.Status.CANCELLED,
        Deposit.updated_at >= start_time,
        Deposit.updated_at < end_time,
    ).with_entities(
        Deposit.user_id,
        Deposit.asset,
        func.sum(Deposit.amount).label('amount')
    ).group_by(
        Deposit.user_id,
        Deposit.asset,
    )
    deposit_dict = defaultdict(lambda: defaultdict(Decimal))
    for v in deposit_query:
        deposit_dict[v.user_id][v.asset] += v.amount
    # 提现
    withdraw_query = Withdrawal.query.filter(
        Withdrawal.user_id.in_(user_ids),
        or_(
            and_(
                Withdrawal.type ==
                Withdrawal.Type.ON_CHAIN,
                Withdrawal.sent_at >= start_time,
                Withdrawal.sent_at < end_time,
                Withdrawal.status == Withdrawal.Status.FINISHED
            ),
            and_(
                Withdrawal.type ==
                Withdrawal.Type.LOCAL,
                Withdrawal.updated_at >= start_time,
                Withdrawal.updated_at < end_time,
                Withdrawal.status == Withdrawal.Status.FINISHED
            )
        )
    ).with_entities(
        Withdrawal.asset,
        Withdrawal.user_id,
        func.sum(Withdrawal.amount).label('amount')
    ).group_by(
        Withdrawal.asset,
        Withdrawal.user_id
    )
    withdraw_dict = defaultdict(lambda: defaultdict(Decimal))
    for v in withdraw_query:
        withdraw_dict[v.user_id][v.asset] += v.amount

    net_deposit_dict = defaultdict(lambda: defaultdict(Decimal))
    red_packet_dict = get_red_packet_data(user_ids, start_time, end_time)
    for user_id in user_ids:
        assets = set(list(deposit_dict[user_id].keys()) +
                     list(withdraw_dict[user_id].keys()) +
                     list(red_packet_dict[user_id].keys()))
        for coin in assets:
            net_deposit_dict[user_id][coin] = deposit_dict[user_id][coin] - \
                                              withdraw_dict[user_id][coin] - \
                                              red_packet_dict[user_id][coin]

    net_deposit_usd_dict = defaultdict(Decimal)
    for user_id, value in net_deposit_dict.items():
        net_deposit_usd_dict[user_id] = sum(
            [rates_cache[coin] * amount for coin, amount in value.items()])
    return net_deposit_usd_dict


@click.command()
@click.argument('start_date', type=str)
@click.argument('end_date', type=str)
def main(start_date, end_date):
    start_at, end_at = str_to_datetime(start_date), str_to_datetime(end_date)
    print('start_at is:{}'.format(start_at))
    print('end_at is:{}'.format(end_at))
    header_data = [
        ('user_id', u'bch合约交易用户ID'),
        ('user_email', u'bch合约交易邮箱账号'),
        ('is_normal', u'是否普通用户'),
        ('deposit_usd', u'8.15～8.25现货净充值量'),
    ]

    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]

    user_infos = get_all_user_info()
    net_deposit_usd_result = get_net_deposit_usd(user_infos.keys(),
                                                 start_at,
                                                 end_at)
    result_list = []
    for user_id, user_detail in user_infos.items():
        data = dict(
            user_id=user_id,
            user_email=user_detail['email'],
            is_normal='是' if user_detail['user_type'] == 'normal' else '--',
            deposit_usd=amount_to_str(net_deposit_usd_result[user_id], 8)
        )
        result_list.append(data)

    streams = ExcelExporter(
        data_list=result_list, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)
    print(f'end at {now()}')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
