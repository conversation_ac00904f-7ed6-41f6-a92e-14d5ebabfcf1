import os
import sys
from decimal import Decimal
from functools import wraps

from app.utils import now

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def add_print(func):
    @wraps(func)
    def dec(*args, **kwargs):
        print(f'{func.__name__}>>>')
        ret = func(*args, **kwargs)
        print(f'{func.__name__}<<<')
        return ret

    return dec


def main():
    from app.models import db

    @add_print
    def brush_agent_table():
        from app.models import AmbassadorAgent

        model = AmbassadorAgent
        rows = model.query.all()
        status_mapping = {
            model.ActiveStatus.ACTIVE: model.Status.VALID,
            model.ActiveStatus.INACTIVE: model.Status.DELETED,
        }
        for row in rows:
            row.status = status_mapping[row.active_status]
            row.referral_rate = Decimal('0.05')
            row.is_appraisal = True
            if row.status is model.Status.VALID:
                row.is_appraisal = False
            row.effected_at = row.created_at
        db.session.commit()

    @add_print
    def brush_relation_to_invalid():
        from app.utils import batch_iter
        from app.models import AmbassadorAgentHistory, Ambassador, AmbassadorAgent

        user_ids = set()
        rows = Ambassador.query.with_entities(Ambassador.user_id).filter(
            Ambassador.status == Ambassador.Status.DELETED
        ).all()
        user_ids |= set(row.user_id for row in rows)

        agent_user_ids = set()
        rows = AmbassadorAgent.query.with_entities(AmbassadorAgent.user_id).filter(
            AmbassadorAgent.status == AmbassadorAgent.Status.DELETED
        ).all()
        agent_user_ids |= set(row.user_id for row in rows)

        model = AmbassadorAgentHistory
        for chunk_user_ids in batch_iter(user_ids, 1000):
            model.query.filter(
                model.ambassador_id.in_(chunk_user_ids)
            ).update({model.status: model.Status.DELETED}, synchronize_session=False)

        for chunk_agent_user_ids in batch_iter(agent_user_ids, 1000):
            model.query.filter(
                model.user_id.in_(chunk_agent_user_ids)
            ).update({model.status: model.Status.DELETED}, synchronize_session=False)
        db.session.commit()

    # @add_print
    # def brush_daily_refer_table():  # 非幂等
    #     from app.models import DailyReferTypeReport
    #
    #     model = DailyReferTypeReport
    #     rows = model.query.filter(
    #         model.type == model.Type.AMBASSADOR_AGENT
    #     ).all()
    #     for row in rows:
    #         row.agent_refer_usd += row.business_refer_usd + row.team_refer_usd
    #     db.session.commit()
    #
    # @add_print
    # def brush_monthly_refer_table():  # 非幂等
    #     from app.models import MonthlyReferTypeReport
    #
    #     model = MonthlyReferTypeReport
    #     rows = model.query.filter(
    #         model.type == model.Type.AMBASSADOR_AGENT
    #     ).all()
    #     for row in rows:
    #         row.agent_refer_usd += row.business_refer_usd + row.team_refer_usd
    #     db.session.commit()

    @add_print
    def batch_add_or_update_agent():
        from app.models import AmbassadorAgent, Ambassador

        model = Ambassador
        rows = model.query.with_entities(model.user_id).filter(
            model.status == model.Status.VALID
        ).all()
        ambassador_user_ids = [row.user_id for row in rows]

        agents = AmbassadorAgent.query.with_entities(
            AmbassadorAgent.user_id
        ).all()
        agent_user_ids = [row.user_id for row in agents]

        new_agent_user_ids = list(set(ambassador_user_ids) - set(agent_user_ids))
        pending_objs = []
        now_ = now()
        for user_id in new_agent_user_ids:
            pending_objs.append(AmbassadorAgent(
                user_id=user_id,
                status=AmbassadorAgent.Status.VALID,
                referral_rate=Decimal('0.05'),
                is_appraisal=False,
                effected_at=now_
            ))
        db.session.bulk_save_objects(pending_objs)
        db.session.commit()

        AmbassadorAgent.query.filter(
            AmbassadorAgent.user_id.in_(ambassador_user_ids)
        ).update({
            AmbassadorAgent.is_appraisal: False,
        }, synchronize_session=False)
        db.session.commit()

    @add_print
    def remove_redundant_ambassador_agent_history():
        from sqlalchemy import func
        from app.models import AmbassadorAgentHistory
        from app.utils import batch_iter

        model = AmbassadorAgentHistory
        rows = model.query.with_entities(
            model.ambassador_id,
        ).group_by(model.ambassador_id).having(
            func.count(model.ambassador_id) > 1
        ).all()

        ambassador_ids = [row.ambassador_id for row in rows]
        group_by_ambassador = {}
        for chunk_ambassador_ids in batch_iter(ambassador_ids, 1000):
            chunk_rows = model.query.filter(
                model.ambassador_id.in_(chunk_ambassador_ids)
            ).all()
            for row in chunk_rows:
                group_by_ambassador.setdefault(row.ambassador_id, []).append(row)

        to_delete_ids = []
        for ambassador_user_id, objs in group_by_ambassador.items():
            valid_obj = None
            objs.sort(key=lambda e: e.id, reverse=True)
            for obj in objs:
                if obj.status is model.Status.VALID:
                    valid_obj = obj
                    break
            if valid_obj:
                objs.remove(valid_obj)
            else:
                objs = objs[:-1]

            to_delete_ids.extend([obj.id for obj in objs])

        for chunk_ids in batch_iter(to_delete_ids, 500):
            model.query.filter(model.id.in_(chunk_ids)).delete(synchronize_session=False)
            db.session.commit()

    @add_print
    def remove_business_team_log():
        from app.models import UserStatusChangeHistory

        model = UserStatusChangeHistory
        model.query.filter(
            model.type.in_(
                [
                    model.Type.BUSINESS_USER.name,
                    model.Type.TEAM_USER.name,
                ]
            )
        ).delete(synchronize_session=False)
        db.session.commit()

    brush_agent_table()
    brush_relation_to_invalid()
    # brush_daily_refer_table()
    # brush_monthly_refer_table()
    batch_add_or_update_agent()
    remove_redundant_ambassador_agent_history()
    remove_business_team_log()


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
