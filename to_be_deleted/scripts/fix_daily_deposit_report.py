# -*- coding: utf-8 -*-
import datetime
import os
import sys
from collections import defaultdict
from decimal import Decimal

import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import AssetPrice, Deposit, DailyDepositWithdrawalReport, db

    def fix_daily_deposit_withdrawal_report(report_date):
        assets_rate = AssetPrice.get_price_map(report_date)
        start_date = report_date
        end_date = report_date + datetime.timedelta(days=1)
        deposits = Deposit.query.filter(
            start_date < Deposit.created_at,
            Deposit.created_at <= end_date,
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.status.in_([
                Deposit.Status.FINISHED,
                Deposit.Status.TO_HOT])
        ).all()

        query_data = DailyDepositWithdrawalReport.query.filter(
            DailyDepositWithdrawalReport.report_date == report_date
        ).all()
        assets = {item.asset for item in query_data}
        asset_map = {}
        for asset in assets:
            asset_price = assets_rate.get(asset, Decimal(0))
            asset_deposits = [i for i in deposits if i.asset == asset]
            deposit_count = len(asset_deposits)
            deposit_amount = sum(r.amount for r in asset_deposits)
            deposit_usd = deposit_amount * asset_price
            deposit_user_list = set([r.user_id for r in asset_deposits])
            deposit_user_count = len(deposit_user_list)

            asset_map[asset] = dict(
                deposit_count=deposit_count,
                deposit_amount=deposit_amount,
                deposit_usd=deposit_usd,
                deposit_user_count=deposit_user_count,
            )

        for item in query_data:
            if item.asset in asset_map:
                item.deposit_count = asset_map[item.asset]['deposit_count']
                item.deposit_amount = asset_map[item.asset]['deposit_amount']
                item.deposit_usd = asset_map[item.asset]['deposit_usd']
                item.deposit_user_count = asset_map[item.asset][
                    'deposit_user_count']

        db.session.commit()

    today = datetime.datetime.utcnow().date()
    last_record = DailyDepositWithdrawalReport.query.order_by(
        DailyDepositWithdrawalReport.report_date.asc()
    ).first()
    start_date = last_record.report_date
    while start_date < today:
        try:
            fix_daily_deposit_withdrawal_report(start_date)
        except Exception as e:
            print(f'日期：{start_date} 修改失败')
            db.session.rollback()

        start_date += datetime.timedelta(days=1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
