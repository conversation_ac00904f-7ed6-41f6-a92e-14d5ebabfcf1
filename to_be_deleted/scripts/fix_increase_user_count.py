# -*- coding: utf-8 -*-
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from dateutil.relativedelta import relativedelta

"""
this script fixes:
`increase_margin_user_count` field of margin report
`increase_trade_user` of perpetual trade report
`increase_investment_user` field of site investment report
`increase_trade_user_count` field of spot trade report
`increase_trade_user` field of user report
"""


def main():
    from app.models import db

    # fix_margin_report()
    fix_perpetual_trade_report()
    # fix_site_investment_report()
    # fix_spot_trade_report()
    # fix_user_report()

    db.session.commit()


def fix_margin_report():
    from app.models import DailyMarginReport, MonthlyMarginReport, MarginLoanOrder, db
    from app.utils import next_month

    def _get_increase_margin_user_count(start_time, end_time, margin_before_user_id_set):
        # 新增杠杆人数
        new_margin_user_ids = MarginLoanOrder.query.filter(
            MarginLoanOrder.created_at >= start_time,
            MarginLoanOrder.created_at < end_time
        ).with_entities(
            MarginLoanOrder.user_id.distinct().label('user_id')
        ).all()
        new_margin_user_id_set = {item.user_id for item in new_margin_user_ids}

        if not margin_before_user_id_set:
            margin_before_user_ids = MarginLoanOrder.query.filter(
                MarginLoanOrder.created_at < start_time,
            ).with_entities(
                MarginLoanOrder.user_id.distinct().label('user_id')
            ).all()
            margin_before_user_ids = [item.user_id for item in
                                      margin_before_user_ids]
            margin_before_user_id_set = set(margin_before_user_ids)
        increase_margin_user_count = len(new_margin_user_id_set -
                                         margin_before_user_id_set)
        margin_before_user_id_set |= new_margin_user_id_set
        return increase_margin_user_count

    daily_reports = DailyMarginReport.query.order_by(
        DailyMarginReport.id
    ).all()

    margin_before_user_id_set = set()
    for report in daily_reports:
        increase_margin_user_count = _get_increase_margin_user_count(
            report.report_date, report.report_date + relativedelta(days=1), margin_before_user_id_set)
        report.increase_margin_user_count = increase_margin_user_count

    monthly_reports = MonthlyMarginReport.query.order_by(
        MonthlyMarginReport.id
    ).all()

    margin_before_user_id_set = set()
    for report in monthly_reports:
        increase_margin_user_count = _get_increase_margin_user_count(
            report.report_date, next_month(report.report_date.year, report.report_date.month), margin_before_user_id_set)
        report.increase_margin_user_count = increase_margin_user_count


def fix_perpetual_trade_report():
    from app.models import DailyPerpetualTradeReport, \
        MonthlyPerpetualTradeReport, UserTradeSummary
    from app.utils import next_month

    def _get_perpetual_user_id_set(start_time, end_time):
        _query = UserTradeSummary.query
        if start_time:
            _query = _query.filter(
                UserTradeSummary.report_date >= start_time
            )
        if end_time:
            _query = _query.filter(
                UserTradeSummary.report_date < end_time
            )
        _query = _query.filter(
            UserTradeSummary.system == UserTradeSummary.System.PERPETUAL).with_entities(
            UserTradeSummary.user_id.distinct().label('user_id')
        )

        return {item.user_id for item in _query}

    daily_reports = DailyPerpetualTradeReport.query.order_by(
        DailyPerpetualTradeReport.id
    ).all()

    trade_before_user_id_set = set()
    for report in daily_reports:
        new_trade_user_id_set = _get_perpetual_user_id_set(
            report.report_date, report.report_date + relativedelta(days=1))

        if not trade_before_user_id_set:
            trade_before_user_ids = UserTradeSummary.query.filter(
                UserTradeSummary.report_date < report.report_date,
                UserTradeSummary.system == UserTradeSummary.System.PERPETUAL,
            ).with_entities(
                UserTradeSummary.user_id.distinct().label('user_id')
            ).all()
            trade_before_user_id_set = {item.user_id for item in
                                        trade_before_user_ids}

        increase_trade_user_count = len(
            new_trade_user_id_set - trade_before_user_id_set)
        trade_before_user_id_set |= new_trade_user_id_set
        report.increase_trade_user = increase_trade_user_count

    # monthly_reports = MonthlyPerpetualTradeReport.query.order_by(
    #     MonthlyPerpetualTradeReport.id
    # ).all()
    # trade_before_user_id_set = set()
    # for report in monthly_reports:
    #     new_trade_user_id_set = _get_perpetual_user_id_set(
    #         report.report_date, next_month(report.report_date.year, report.report_date.month))
    #
    #     if not trade_before_user_id_set:
    #         trade_before_user_ids = UserTradeSummary.query.filter(
    #             UserTradeSummary.report_date < report.report_date,
    #             UserTradeSummary.system == UserTradeSummary.System.PERPETUAL,
    #         ).with_entities(
    #             UserTradeSummary.user_id.distinct().label('user_id')
    #         ).all()
    #         trade_before_user_id_set = {item.user_id for item in
    #                                     trade_before_user_ids}
    #     increase_trade_user_count = len(
    #         new_trade_user_id_set - trade_before_user_id_set)
    #     trade_before_user_id_set |= new_trade_user_id_set
    #     report.increase_trade_user = increase_trade_user_count


def fix_site_investment_report():
    from app.models import DailySiteInvestmentReport, \
        MonthlySiteInvestmentReport, InvestmentBalanceHistory
    from app.utils import next_month

    def _get_investment_user_id_set(start_time, end_time):
        new_investment_user_ids = InvestmentBalanceHistory.query.filter(
                InvestmentBalanceHistory.opt_type ==
                InvestmentBalanceHistory.OptType.INTEREST,
                InvestmentBalanceHistory.status ==
                InvestmentBalanceHistory.StatusType.SUCCESS,
                InvestmentBalanceHistory.success_at >= start_time,
                InvestmentBalanceHistory.success_at < end_time,
                ).with_entities(
            InvestmentBalanceHistory.user_id.distinct().label('user_id')
        ).all()
        return {item.user_id for item in new_investment_user_ids}

    daily_reports = DailySiteInvestmentReport.query.order_by(
        DailySiteInvestmentReport.id
    ).all()

    invested_before_user_id_set = set()

    for report in daily_reports:
        new_investment_user_id_set = _get_investment_user_id_set(
            report.report_date, report.report_date + relativedelta(days=1))
        if not invested_before_user_id_set:
            invested_before_user_ids = InvestmentBalanceHistory.query.filter(
                    InvestmentBalanceHistory.opt_type ==
                    InvestmentBalanceHistory.OptType.INTEREST,
                    InvestmentBalanceHistory.status ==
                    InvestmentBalanceHistory.StatusType.SUCCESS,
                    InvestmentBalanceHistory.success_at < report.report_date,
                    ).with_entities(
                InvestmentBalanceHistory.user_id.distinct().label('user_id')
            ).all()
            invested_before_user_id_set = {item.user_id for item in invested_before_user_ids}
        increase_investment_user_count = len(
            new_investment_user_id_set - invested_before_user_id_set)

        invested_before_user_id_set |= new_investment_user_id_set
        report.increase_investment_user = increase_investment_user_count
        report.investment_user_count = len(new_investment_user_id_set)

    monthly_reports = MonthlySiteInvestmentReport.query.order_by(
        MonthlySiteInvestmentReport.id
    ).all()

    invested_before_user_id_set = set()
    for report in monthly_reports:
        new_investment_user_id_set = _get_investment_user_id_set(
            report.report_date, next_month(report.report_date.year, report.report_date.month))
        if not invested_before_user_id_set:
            invested_before_user_ids = InvestmentBalanceHistory.query.filter(
                InvestmentBalanceHistory.opt_type ==
                InvestmentBalanceHistory.OptType.INTEREST,
                InvestmentBalanceHistory.status ==
                InvestmentBalanceHistory.StatusType.SUCCESS,
                InvestmentBalanceHistory.success_at < report.report_date,
            ).with_entities(
                InvestmentBalanceHistory.user_id.distinct().label('user_id')
            ).all()
            invested_before_user_id_set = {item.user_id for item in
                                           invested_before_user_ids}
        increase_investment_user_count = len(
            new_investment_user_id_set - invested_before_user_id_set)
        invested_before_user_id_set |= new_investment_user_id_set
        report.increase_investment_user = increase_investment_user_count
        report.investment_user_count = len(new_investment_user_id_set)


def fix_spot_trade_report():

    from app.models import UserTradeSummary, DailySpotTradeReport, MonthlySpotTradeReport
    from app.utils import next_month

    def _get_trade_user_set(start_time, end_time):
        _query = UserTradeSummary.query.filter(
            UserTradeSummary.report_date >= start_time,
            UserTradeSummary.report_date < end_time,
        ).with_entities(
            UserTradeSummary.user_id.distinct().label('user_id')
        )
        _query = _query.filter(
            UserTradeSummary.system == UserTradeSummary.System.SPOT)

        return {item.user_id for item in _query}

    daily_reports = DailySpotTradeReport.query.order_by(
        DailySpotTradeReport.id
    ).all()
    traded_before_user_id_set = set()
    for report in daily_reports:
        active_trade_user_set = \
            _get_trade_user_set(report.report_date, report.report_date + relativedelta(days=1))

        if not traded_before_user_id_set:
            traded_before_user_ids = UserTradeSummary.query.filter(
                UserTradeSummary.report_date < report.report_date,
                UserTradeSummary.user_id.in_(active_trade_user_set)
            ).with_entities(
                UserTradeSummary.user_id.distinct().label('user_id')
            ).all()
            traded_before_user_id_set = {item.user_id
                                         for item in traded_before_user_ids}
        increase_trade_user_count = len(
            active_trade_user_set - traded_before_user_id_set)
        traded_before_user_id_set |= active_trade_user_set
        report.increase_trade_user_count = increase_trade_user_count

    monthly_reports = MonthlySpotTradeReport.query.order_by(
        MonthlySpotTradeReport.id
    ).all()

    traded_before_user_id_set = set()
    for report in monthly_reports:
        active_trade_user_set = \
            _get_trade_user_set(report.report_date, next_month(
                report.report_date.year, report.report_date.month))

        if not traded_before_user_id_set:
            traded_before_user_ids = UserTradeSummary.query.filter(
                UserTradeSummary.report_date < report.report_date,
                UserTradeSummary.user_id.in_(active_trade_user_set)
            ).with_entities(
                UserTradeSummary.user_id.distinct().label('user_id')
            ).all()
            traded_before_user_id_set = {item.user_id
                                         for item in traded_before_user_ids}
        increase_trade_user_count = len(
            active_trade_user_set - traded_before_user_id_set)
        traded_before_user_id_set |= active_trade_user_set
        report.increase_trade_user_count = increase_trade_user_count


def fix_user_report():
    from app.models import UserTradeSummary, DailyUserReport, MonthlyUserReport, QuarterlyUserReport
    from app.utils import next_month

    def _get_increase_user_count(start_time, end_time, traded_before_user_id_set):
        _query = UserTradeSummary.query.filter(
            UserTradeSummary.report_date >= start_time,
            UserTradeSummary.report_date < end_time,
        ).with_entities(
            UserTradeSummary.user_id.distinct().label('user_id')
        )
        active_trade_user_set = {item.user_id for item in _query}

        if not traded_before_user_id_set:
            traded_before_user_ids = UserTradeSummary.query.filter(
                UserTradeSummary.report_date < start_time,
                UserTradeSummary.user_id.in_(active_trade_user_set)
            ).with_entities(
                UserTradeSummary.user_id.distinct().label('user_id')
            ).all()
            traded_before_user_id_set = {item.user_id
                                         for item in traded_before_user_ids}
        increase_trade_user_count = len(
            active_trade_user_set - traded_before_user_id_set)
        traded_before_user_id_set |= active_trade_user_set
        return increase_trade_user_count

    daily_reports = DailyUserReport.query.order_by(
        DailyUserReport.id
    ).all()

    traded_before_user_id_set = set()
    for report in daily_reports:
        increase_user_count = _get_increase_user_count(
            report.report_date, report.report_date + relativedelta(days=1), traded_before_user_id_set)
        report.increase_trade_user = increase_user_count

    monthly_reports = MonthlyUserReport.query.order_by(
        MonthlyUserReport.id
    ).all()

    traded_before_user_id_set = set()
    for report in monthly_reports:
        increase_user_count = _get_increase_user_count(
            report.report_date, next_month(report.report_date.year, report.report_date.month), traded_before_user_id_set)
        report.increase_trade_user = increase_user_count

    quarterly_report = QuarterlyUserReport.query.order_by(
        QuarterlyUserReport.id
    ).all()

    traded_before_user_id_set = set()
    for report in quarterly_report:
        increase_user_count = _get_increase_user_count(
            report.report_date, report.report_date + relativedelta(months=3), traded_before_user_id_set)
        report.increase_trade_user = increase_user_count


def fix_site_asset_investment_report():
    from app.models import DailyInvestmentReport, \
        MonthlyInvestmentReport, InvestmentBalanceHistory
    from app.business.investment import InvestmentInterestOperation
    from app.utils import next_month

    def _get_investment_user_id_set(start_time, end_time, asset):
        new_investment_user_ids = InvestmentBalanceHistory.query.filter(
                InvestmentBalanceHistory.opt_type ==
                InvestmentBalanceHistory.OptType.INTEREST,
                InvestmentBalanceHistory.asset == asset,
                InvestmentBalanceHistory.status ==
                InvestmentBalanceHistory.StatusType.SUCCESS,
                InvestmentBalanceHistory.success_at >= start_time,
                InvestmentBalanceHistory.success_at < end_time,
                ).with_entities(
            InvestmentBalanceHistory.user_id.distinct().label('user_id')
        ).all()
        return {item.user_id for item in new_investment_user_ids}

    asset_list = InvestmentInterestOperation.get_all_assets()

    for asset in asset_list:

        daily_reports = DailyInvestmentReport.query.filter(
            DailyInvestmentReport.asset == asset,
        ).order_by(
            DailyInvestmentReport.id
        ).all()

        invested_before_user_id_set = set()

        for report in daily_reports:
            new_investment_user_id_set = _get_investment_user_id_set(
                report.report_date, report.report_date + relativedelta(days=1), asset)
            if not invested_before_user_id_set:
                invested_before_user_ids = InvestmentBalanceHistory.query.filter(
                        InvestmentBalanceHistory.opt_type ==
                        InvestmentBalanceHistory.OptType.INTEREST,
                        InvestmentBalanceHistory.asset == asset,
                        InvestmentBalanceHistory.status ==
                        InvestmentBalanceHistory.StatusType.SUCCESS,
                        InvestmentBalanceHistory.success_at < report.report_date,
                        ).with_entities(
                    InvestmentBalanceHistory.user_id.distinct().label('user_id')
                ).all()
                invested_before_user_id_set = {item.user_id for item in invested_before_user_ids}
            increase_investment_user_count = len(
                new_investment_user_id_set - invested_before_user_id_set)

            invested_before_user_id_set |= new_investment_user_id_set

            report.increase_investment_user = increase_investment_user_count
            report.user_count = len(new_investment_user_id_set)

        monthly_reports = MonthlyInvestmentReport.query.filter(
            MonthlyInvestmentReport.asset == asset,
        ).order_by(
            MonthlyInvestmentReport.id
        ).all()

        invested_before_user_id_set = set()
        for report in monthly_reports:
            new_investment_user_id_set = _get_investment_user_id_set(
                report.report_date, next_month(report.report_date.year, report.report_date.month), asset)
            if not invested_before_user_id_set:
                invested_before_user_ids = InvestmentBalanceHistory.query.filter(
                    InvestmentBalanceHistory.opt_type ==
                    InvestmentBalanceHistory.OptType.INTEREST,
                    InvestmentBalanceHistory.asset == asset,
                    InvestmentBalanceHistory.status ==
                    InvestmentBalanceHistory.StatusType.SUCCESS,
                    InvestmentBalanceHistory.success_at < report.report_date,
                ).with_entities(
                    InvestmentBalanceHistory.user_id.distinct().label('user_id')
                ).all()
                invested_before_user_id_set = {item.user_id for item in
                                               invested_before_user_ids}
            increase_investment_user_count = len(
                new_investment_user_id_set - invested_before_user_id_set)
            invested_before_user_id_set |= new_investment_user_id_set
            report.increase_investment_user = increase_investment_user_count
            report.user_count = len(new_investment_user_id_set)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
