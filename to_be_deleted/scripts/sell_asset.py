import time
from decimal import Decimal

from flask import current_app

from app.business import PriceManager, ServerClient
from app.caches import MarketCache
from app.common import OrderSideType, OrderOption
from app.business.order import Order
from app.utils import quantize_amount, amount_to_str


def sell_asset(user_id, asset, asset_balance):
    market_list = MarketCache.list_online_markets()
    market = f'{asset}USDT'
    if market not in market_list:
        current_app.logger.error(f'{market} not exists')
        return False
    
    market_cache = MarketCache(market).dict

    order_price = ServerClient().market_last(market) * Decimal('0.99')
    order_price = quantize_amount(order_price, market_cache["quote_asset_precision"])

    order_amount = asset_balance
    order_amount = min(order_amount, asset_balance)
    order_amount = quantize_amount(order_amount, market_cache["base_asset_precision"])
    # 卖单
    ServerClient().put_limit_order(
            user_id=user_id,
            market=market,
            side=OrderSideType.SELL,
            amount=amount_to_str(order_amount),
            price=amount_to_str(order_price),
            taker_fee_rate='0',
            maker_fee_rate='0',
            source=Order.OrderSourceType.SYSTEM.value,
            fee_asset=None,
            fee_discount='1',
            account_id=0,
            option=OrderOption.WITHOUT_ORDER_MIN_AMOUNT,
        )
    current_app.logger.info(f'place sell order {order_amount} {asset} at price {order_price}')
    return True


def loop_sell_asset(user_id, exclude_assets):
    prices = PriceManager.assets_to_usd()
    while True:
        ServerClient().cancel_user_all_order(user_id, -1, None)

        balances = ServerClient().get_user_balances(user_id)
        placed_order = False
        for asset, bs in balances.items():
            if asset == 'USDT' or asset in exclude_assets:
                continue
            asset_balance = bs['available']

            if prices.get(asset, 0) * asset_balance <= 0.1:
                continue

            if not sell_asset(user_id, asset, asset_balance):
                continue
            placed_order = True

        if not placed_order:
            break
        time.sleep(20)
