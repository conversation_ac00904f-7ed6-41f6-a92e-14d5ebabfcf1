import datetime
import json
from tqdm import tqdm
import os
import sys
from collections import defaultdict
from decimal import Decimal

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    update_daily_report()
    update_monthly_report()


def update_daily_report():
    from app.models import DailyReferTypeReport, db
    from app.utils.date_ import today
    print('开始更新日报表。。。')

    rec = DailyReferTypeReport.query.order_by(
        DailyReferTypeReport.report_date.asc()).first()
    start_date = rec.report_date
    today_ = today()
    progress_bar = tqdm(total=(today_ - start_date).days)

    while start_date < today_:
        records = DailyReferTypeReport.query.filter(
            DailyReferTypeReport.report_date == start_date
        ).all()
        for record in records:
            trade_user_ids = json.loads(record.trade_user_list)
            update_type_record(record, trade_user_ids)
        db.session.commit()
        start_date += datetime.timedelta(days=1)
        progress_bar.update(1)
    print('日报更新完毕！')


def update_type_record(record, user_ids):
    from app.models import UserTradeSummary
    from app.utils import batch_iter
    trade_data = []
    for _ids in batch_iter(user_ids, 1000):
        rows = UserTradeSummary.query.filter(
            UserTradeSummary.user_id.in_(_ids),
            UserTradeSummary.report_date == record.report_date,
        ).with_entities(
            UserTradeSummary.user_id,
            UserTradeSummary.system,
            UserTradeSummary.trade_amount
        ).all()
        trade_data.extend(rows)
    trade_spot_usd = trade_perpetual_usd = 0
    for item in trade_data:
        trade_amount = item.trade_amount
        if item.system == UserTradeSummary.System.SPOT:
            trade_spot_usd += trade_amount
        elif item.system == UserTradeSummary.System.PERPETUAL:
            trade_perpetual_usd += trade_amount
    record.trade_spot_usd = trade_spot_usd
    record.trade_perpetual_usd = trade_perpetual_usd


def update_monthly_report():
    from app.models import MonthlyReferTypeReport, DailyReferTypeReport, db
    from app.utils.date_ import this_month, next_month
    from sqlalchemy import func
    model = MonthlyReferTypeReport
    daily_model = DailyReferTypeReport
    rec = model.query.order_by(model.report_date.asc()).first()
    start_month = rec.report_date
    this_month_ = this_month()
    distinct_days_count = model.query.with_entities(
        func.count(func.distinct(model.report_date))
    ).scalar()
    progress_bar = tqdm(total=distinct_days_count)
    while start_month < this_month_:
        end_month = next_month(start_month.year, start_month.month)
        daily_refer_data = daily_model.query.filter(
            daily_model.report_date >= start_month,
            daily_model.report_date < end_month
        ).all()
        refer_type_map = defaultdict(lambda: {
            'trade_spot_usd': Decimal(),
            'trade_perpetual_usd': Decimal()
        })
        for item in daily_refer_data:
            type_ = item.type.name
            refer_type_map[type_]['trade_spot_usd'] += item.trade_spot_usd
            refer_type_map[type_]['trade_perpetual_usd'] += item.trade_perpetual_usd
        records = model.query.filter(
            model.report_date == start_month
        ).all()
        for record in records:
            record.trade_spot_usd = refer_type_map[record.type.name]['trade_spot_usd']
            record.trade_perpetual_usd = refer_type_map[record.type.name][
                'trade_perpetual_usd']
        db.session.commit()
        start_month = end_month
        progress_bar.update(1)
    print('月报表更新完毕')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
