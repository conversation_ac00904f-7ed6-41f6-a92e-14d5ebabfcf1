from collections import defaultdict
from datetime import datetime, timedelta
import os
import sys

from pyroaring import BitMap

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


if __name__ == '__main__':
    from app import create_app

    app = create_app()

    from app.models import InvestmentBalanceHistory, DailyInvestmentReport, db, \
        MonthlyInvestmentReport, MonthlySiteInvestmentReport, DailySiteInvestmentReport
    from app.business import ExchangeLogDB
    from app.business.report.investment import SiteInvestmentReporter
    from app.schedules.reports.monthly_investment_report import update_monthly_asset_investment_schedule, \
        update_monthly_investment_schedule

    with app.app_context():

        from app.business import ExchangeLogDB, today

        date_balance_map = defaultdict(lambda: defaultdict(set))
        print('正在读取用户历史资产快照表：user_slice_balance')
        for idx in range(ExchangeLogDB.USER_SLICE_BALANCE_TABLE_COUNT):
            table = ExchangeLogDB.user_slice_balance_table(idx)
            result = table.select("user_id, report_date, asset",
                                  where=f'account = 20000',
                                  )
            for user_id, report_date, asset in result:
                date_balance_map[asset][report_date].add(user_id)

        date_interest_map = defaultdict(lambda: defaultdict(set))
        print('正在读取用户利息收入表：InvestmentBalanceHistory')
        interest_query = InvestmentBalanceHistory.query.filter(
            InvestmentBalanceHistory.opt_type ==
            InvestmentBalanceHistory.OptType.INTEREST,
            InvestmentBalanceHistory.status == InvestmentBalanceHistory.StatusType.SUCCESS,
        ).with_entities(
            InvestmentBalanceHistory.user_id,
            InvestmentBalanceHistory.success_at,
            InvestmentBalanceHistory.created_at,
            InvestmentBalanceHistory.asset,
        ).all()

        for item in interest_query:
            success_date = item.success_at.date() if item.success_at else item.created_at.date()
            date_interest_map[item.asset][success_date].add(item.user_id)

        today_ = today()
        min_start_date = today_
        print('正在更新理财统计记录表(日报)')
        for asset, report_map in date_balance_map.items():
            total_user_set = set()
            total_interest_user_set = set()
            report_date = sorted(report_map)[0]
            if min_start_date > report_date:
                # 获取最早的快照日期
                min_start_date = report_date

            while report_date < today_:
                row = DailyInvestmentReport.get_or_create(
                    asset=asset,
                    report_date=report_date,
                )
                user_set = set(date_balance_map[asset][report_date])
                interest_user_set = set(date_interest_map[asset][report_date])
                new_user_set = user_set - total_user_set
                new_interest_user_set = interest_user_set - total_interest_user_set
                row.increase_user_bitmap = BitMap(new_user_set).serialize()
                row.increase_investment_user = len(new_user_set)
                row.site_cur_user_bitmap = BitMap(user_set).serialize()
                row.user_count = len(user_set)
                row.increase_interest_user_bitmap = BitMap(new_interest_user_set).serialize()
                row.increase_interest_user = len(new_interest_user_set)
                row.site_cur_interest_user_bitmap = BitMap(interest_user_set).serialize()
                row.interest_user_count = len(user_set)
                row.amount = row.amount if row.amount else 0
                row.usd = row.usd if row.usd else 0
                row.investment_interest_amount = row.investment_interest_amount if row.investment_interest_amount else 0
                row.investment_interest_usd = row.investment_interest_usd if row.investment_interest_usd else 0
                row.day_rate = row.day_rate if row.day_rate else 0
                row.seven_day_rate = row.seven_day_rate if row.seven_day_rate else 0
                row.interest_user_count = row.interest_user_count if row.interest_user_count else 0
                row.increase_interest_user = row.increase_interest_user if row.increase_interest_user else 0
                total_user_set.update(user_set)
                total_interest_user_set.update(interest_user_set)
                date_balance_map[asset][report_date] = BitMap(total_user_set)
                date_interest_map[asset][report_date] = BitMap(total_interest_user_set)
                row.site_history_user_bitmap = BitMap(total_user_set).serialize()
                row.site_history_interest_user_bitmap = BitMap(total_interest_user_set).serialize()
                db.session.add(row)
                report_date += timedelta(days=1)
        db.session.commit()
        print('正在清空理财全站报表并重新更新')
        DailySiteInvestmentReport.query.delete()
        start_date = min_start_date
        while start_date < today_:
            end_date = start_date + timedelta(days=1)
            SiteInvestmentReporter().run(start_date, end_date)
            start_date = end_date
        db.session.commit()
        print('正在清空理财月报表并重新更新')
        MonthlyInvestmentReport.query.delete()
        MonthlySiteInvestmentReport.query.delete()
        db.session.commit()
        update_monthly_asset_investment_schedule()
        update_monthly_investment_schedule()