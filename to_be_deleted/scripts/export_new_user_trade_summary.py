# -*- coding: utf-8 -*-
import os
import sys

import click
from sqlalchemy import and_, func
from datetime import timedelta

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from app.utils import now, ExcelExporter, upload_file, str_to_datetime
from app.models import Ambassador, ReferralHistory, User, UserTradeSummary


def get_refer_history(user_ids):
    refer_query = ReferralHistory.query.filter(
        ReferralHistory.referree_id.in_(user_ids),
    ).with_entities(
        ReferralHistory.created_at,
        ReferralHistory.referrer_id,
        ReferralHistory.referree_id,
    )
    """{被邀请用户id: 邀请人id}"""
    return {
        i.referree_id: i.referrer_id for i in refer_query
        }


def get_user_infos(start_date, end_date):
    user_query = User.query.filter(
        User.created_at >= start_date,
        User.created_at < end_date,
        and_(User.email != '',
             User.email != None,)
    ).with_entities(
        User.id,
        User.created_at,
        User.email,
    )
    return {u.id: dict(created_at=u.created_at, email=u.email) for u in user_query}


def get_ambassador(user_id):
    ambassador_query = Ambassador.query.filter(
        Ambassador.user_id.in_(user_id),
        Ambassador.status == Ambassador.Status.VALID
    ).with_entities(
        Ambassador.user_id,
    )
    return [a.user_id for a in ambassador_query]


def get_user_first_trade_time(user_ids, start_date, end_date):
    user_trade_query = UserTradeSummary.query.filter(
        UserTradeSummary.report_date >= start_date,
        UserTradeSummary.report_date < end_date,
        UserTradeSummary.user_id.in_(user_ids)
    ).with_entities(
        func.min(UserTradeSummary.report_date).label('min_created_at'),
        UserTradeSummary.user_id
    ).group_by(UserTradeSummary.user_id)

    return {u.user_id: u.min_created_at for u in user_trade_query}


def get_user_trade_amount(user_id, create_date):
    user_trade_amount = UserTradeSummary.query.filter(
        UserTradeSummary.report_date >= create_date,
        UserTradeSummary.report_date < create_date + timedelta(days=30),
        UserTradeSummary.user_id == user_id,
    ).with_entities(
        UserTradeSummary.user_id,
        func.sum(UserTradeSummary.trade_amount).label('trade_amount')
    ).first()
    return user_trade_amount.trade_amount if user_trade_amount else 0


def format_user_data(user_create_time_map, referrer_user_map, ambassador_user_ids, user_trade_map):

    records = []
    for user_id, user_data in user_create_time_map.items():
        tmp_data = dict(
            created_at=user_data['created_at'].date(),
            email=user_data['email'],
        )
        source = '-'
        if referrer_user_map.get(user_id):
            if referrer_user_map[user_id] in ambassador_user_ids:
                source = '大使推荐'
            else:
                source = '普通推荐'

        tmp_data['source'] = source

        user_created_date = user_data['created_at'].date()
        tmp_data['trade_amount'] = get_user_trade_amount(user_id,
                                                          user_created_date)

        if user_id not in user_trade_map:
            tmp_data.update({
                '1d': '否',
                '3d': '否',
                '7d': '否',
                '15d': '否',
            })

        elif user_trade_map[user_id] - user_created_date <= timedelta(days=1):
            tmp_data.update({
                '1d': '是',
                '3d': '是',
                '7d': '是',
                '15d': '是',
            })
        elif user_trade_map[user_id] - user_created_date <= timedelta(days=3):
            tmp_data.update({
                '1d': '否',
                '3d': '是',
                '7d': '是',
                '15d': '是',
            })
        elif user_trade_map[user_id] - user_created_date <= timedelta(days=7):
            tmp_data.update({
                '1d': '否',
                '3d': '否',
                '7d': '是',
                '15d': '是',
            })
        elif user_trade_map[user_id] - user_created_date <= timedelta(days=15):
            tmp_data.update({
                '1d': '否',
                '3d': '否',
                '7d': '否',
                '15d': '是',
            })
        else:
            tmp_data.update({
                '1d': '否',
                '3d': '否',
                '7d': '否',
                '15d': '否',
            })

        records.append(tmp_data)
    return records


@click.command()
@click.argument('start_date', type=str)
@click.argument('end_date', type=str)
def main(start_date, end_date):
    start_at, end_at = str_to_datetime(start_date), str_to_datetime(end_date)
    print(f'start at {now()}')
    header_data = [
        ('created_at', '用户注册时间'),
        ('email', '用户邮箱'),
        ('source', '用户来源(按推荐分类)'),
        ('1d', '注册1日内是否交易'),
        ('3d', '3天内是否交易'),
        ('7d', '7天内是否交易'),
        ('15d', '15天内是否交易'),
        ('trade_amount', '注册30天内交易额usd'),
    ]
    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]

    user_create_time_map = get_user_infos(start_at, end_at)
    referrer_user_map = get_refer_history(list(user_create_time_map.keys()))
    # 查看user是否为大使
    ambassador_user_ids = get_ambassador(list(referrer_user_map.values()))
    user_trade_map = get_user_first_trade_time(
        list(user_create_time_map.keys()), start_at, end_at)

    records = format_user_data(user_create_time_map, referrer_user_map, ambassador_user_ids, user_trade_map)

    streams = ExcelExporter(
        data_list=records, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)
    print(f'end at {now()}')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
