# -*- coding: utf-8 -*-
import os
import sys
from decimal import Decimal

import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from app.utils import now, ExcelExporter, upload_file, str_to_datetime, \
    amount_to_str
from app.models import Activity, SubAccount, BindingAddress
from werkzeug.datastructures import MultiDict


IFT_COUNT = 500000


@click.command()
@click.argument('date_', type=str)
@click.option('--update_asset', type=bool)
def main(date_, update_asset):
    from app.business import add_activity_gift, update_gift_history_task, \
        BalanceBusiness, VipHelper

    ts = int(str_to_datetime(date_).timestamp())
    print(f'ts is {ts}')

    sub_query = SubAccount.query.filter(
        SubAccount.status == SubAccount.Status.VALID
    ).with_entities(
        SubAccount.user_id,
        SubAccount.main_user_id
    )
    user_with_sub_user_dict = MultiDict(
        [(v.main_user_id, v.user_id) for v in sub_query])
    sub_user_list = [v.user_id for v in sub_query]
    user_with_cet_dict = VipHelper.list_cet_snap_shot_balance(ts)
    balance_user_ids = list(user_with_cet_dict.keys())

    binding_query = BindingAddress.query.filter(
        BindingAddress.status == BindingAddress.StatusType.PASS,
        BindingAddress.binding_time < now(),
    ).with_entities(
        BindingAddress.user_id.distinct().label('user_id')
    )
    binding_users = [v.user_id for v in binding_query]

    user_cet_amount_map = dict()

    for user_id in set(
            balance_user_ids + binding_users):
        if user_id in sub_user_list:
            continue
        user_id_with_sub_users = [user_id] + user_with_sub_user_dict.getlist(
                user_id)
        in_site_cet_amount = sum(
                [user_with_cet_dict.get(_u, Decimal()) for _u in
                 user_id_with_sub_users]
                )
        if user_id in binding_users:
            try:
                out_site_cet_amount = VipHelper.get_out_site_cet(user_id, ts)
            except Exception:
                out_site_cet_amount = Decimal()
        else:
            out_site_cet_amount = Decimal()
        cet_amount = in_site_cet_amount + out_site_cet_amount
        if cet_amount >= 10000:
            user_cet_amount_map[user_id] = cet_amount

    print(f'len of user_cet_amount_map is {len(user_cet_amount_map)}')

    result_list = [{
        'user_id': user_id,
        'amount': amount,
        'asset': 'CET',
    } for user_id, amount in user_cet_amount_map.items()]

    header_data = [
        ('user_id', '用户ID'),
        ('asset', 'CET'),
        ('amount', '数量'),
    ]
    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]

    streams = ExcelExporter(
        data_list=result_list, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)

    if update_asset:

        activity_id = Activity.SYSTEM_REWORD_ID
        asset_amount = amount_to_str(IFT_COUNT / len(user_cet_amount_map), 8)

        for user_id in user_cet_amount_map.keys():
            add_activity_gift(
                user_id=user_id,
                activity_id=activity_id,
                gift_asset='IFT',
                amount=asset_amount,
                remark='IFT空投奖励发放')
        update_gift_history_task(
            Activity.SYSTEM_REWORD_ID, BalanceBusiness.SYSTEM.value)
        print('send success!')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
