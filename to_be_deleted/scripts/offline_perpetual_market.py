# -*- coding: utf-8 -*-

import os
import sys
from decimal import Decimal

import click
from tqdm import tqdm

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def check_perpetual_user_frozen_assets(asset):
    from app.business import PerpetualLogDB
    ts = PerpetualLogDB.get_slice_history_timestamp()
    table = PerpetualLogDB.table(f"slice_balance_{ts}")
    print(f"当前读取的备份表为{table.name}")
    rows = table.select(
        'count(*)',
        where=f'`balance` > 0 and asset = "{asset}" and type in (2,3)'
    )
    print(f"当前资产{asset}还有{rows[0][0]}有冻结或者锁定资产")
    if rows[0][0] > 0:
        return False
    return True


def process_market_offline(asset, execute):
    from app.business.perpetual import perpetual_transfer_out
    from app.business import PerpetualLogDB
    from app.business import PerpetualServerClient
    from app.utils import quantize_amount
    ts = PerpetualLogDB.get_slice_history_timestamp()
    table = PerpetualLogDB.table(f"slice_balance_{ts}")
    print(f"当前读取的备份表为{table.name}")
    rows = table.select(
        'user_id',
        where=f'`balance` > 0 and asset="{asset}" and type = 1'
    )
    user_ids = [v[0] for v in rows if v[0] != 0]
    print(f"当前处理的用户数为{len(user_ids)}")
    c = PerpetualServerClient()
    progress_bar = tqdm(total=len(user_ids))
    for user_id in user_ids:
        r = c.get_user_balances(user_id, asset)
        progress_bar.update(1)
        for asset, d in r.items():
            if Decimal(d["frozen"]) > Decimal():
                print(f"用户{user_id}存在冻结资产{asset} {d}")
                continue
            if not execute:
                continue
            r = c.get_user_balances(user_id, asset)
            if Decimal(d["available"]) > Decimal():
                amount = quantize_amount(d["available"], 8)
                perpetual_transfer_out(user_id, asset, amount)
    progress_bar.close()
    print("finish process")


@click.command()
@click.argument('name')
@click.argument('command')
@click.option('--execute', is_flag=True)
def main(name, command, execute):
    commands = ["check", "process"]
    if command not in commands:
        raise ValueError("command error")
    if command == "check":
        check_perpetual_user_frozen_assets(name)
    if command == "process":
        process_market_offline(name, execute)


if __name__ == '__main__':
    from app import create_app
    with create_app().app_context():
        main()
