# -*- coding: utf-8 -*-
import os
import sys
import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


def import_monthly_ambassador_report_delta_count_col() -> None:
    """ 刷 `当月新增的推荐人数` 到 大使月报 """
    from collections import defaultdict
    from datetime import date

    from app.models import db, Ambassador, ReferralHistory, MonthlyAmbassadorReport
    from app.utils import today, batch_iter

    ambassadors = Ambassador.query.all()
    ambassador_dict = {i.user_id: i for i in ambassadors}

    _today = today()
    cur_month = date(_today.year, _today.month, 1)

    all_report_amb_user_ids = [
        r for r, in MonthlyAmbassadorReport.query.with_entities(MonthlyAmbassadorReport.user_id.distinct()).all()
    ]
    all_report_amb_user_ids.sort()
    for chunk_amb_user_id in batch_iter(all_report_amb_user_ids, 50):
        ref_rows = ReferralHistory.query.filter(
            ReferralHistory.referrer_id.in_(chunk_amb_user_id),
        ).all()

        amb_month_delta_ref_count_map = defaultdict(lambda: defaultdict(int))
        cur_month_delta_ref_count_map = defaultdict(int)
        for ref in ref_rows:
            month = date(ref.created_at.year, ref.created_at.month, 1)
            amb_month_delta_ref_count_map[month][ref.referrer_id] += 1

            ambassador_info = ambassador_dict[ref.referrer_id]
            if (
                ref.effected_at >= ambassador_info.effected_at
                and ref.status == ReferralHistory.Status.VALID
                and ambassador_info.status == Ambassador.Status.VALID
                and ref.effected_at.date() >= cur_month
            ):
                cur_month_delta_ref_count_map[ref.referrer_id] += 1

        report_rows = MonthlyAmbassadorReport.query.filter(MonthlyAmbassadorReport.user_id.in_(chunk_amb_user_id)).all()
        for report_row in report_rows:
            if report_row.report_date != cur_month:
                delta_ref_count = amb_month_delta_ref_count_map[report_row.report_date][report_row.user_id]
            else:
                delta_ref_count = cur_month_delta_ref_count_map[report_row.user_id]
            if delta_ref_count > report_row.referral_count:
                delta_ref_count = report_row.referral_count
            report_row.delta_referral_count = delta_ref_count
        db.session.commit()
        print(f"chunk {len(chunk_amb_user_id)} Ambassador done")


@click.command()
def main():
    import_monthly_ambassador_report_delta_count_col()


if __name__ == "__main__":
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
