# -*- coding: utf-8 -*-
import os
import sys
from typing import List
from decimal import Decimal
from collections import defaultdict

import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


def generate_exchange_new_fee_record():
    from app.models import db
    from app.models.exchange import AssetExchangeOrder, AssetExchangeOrderTransferHistory, AssetExchangeOrderFee

    finished_orders: List[AssetExchangeOrder] = AssetExchangeOrder.query.filter(
        AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED,
        AssetExchangeOrder.result != AssetExchangeOrder.Result.FAILED,  # 兑换失败没手续费
    ).all()
    print(f"finished_exchange_orders: {len(finished_orders)}")
    order_map = {i.id: i for i in finished_orders}
    order_ids = list(order_map.keys())
    old_fee_rows: List[AssetExchangeOrderTransferHistory] = AssetExchangeOrderTransferHistory.query.filter(
        AssetExchangeOrderTransferHistory.exchange_order_id.in_(order_ids),
        AssetExchangeOrderTransferHistory.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_FEE,
    ).all()
    print(f"old_fee_rows: {len(old_fee_rows)}")
    for old_fee_row in old_fee_rows:
        assert old_fee_row.status == AssetExchangeOrderTransferHistory.Status.FINISHED

    for old_fee_row in old_fee_rows:
        order = order_map[old_fee_row.exchange_order_id]
        new_fee = AssetExchangeOrderFee.get_or_create(exchange_order_id=old_fee_row.exchange_order_id)
        new_fee.user_id = order.user_id
        new_fee.asset = old_fee_row.asset
        new_fee.amount = old_fee_row.amount
        new_fee.status = AssetExchangeOrderFee.Status.FINISHED
        new_fee.finished_at = old_fee_row.deducted_at or old_fee_row.updated_at
        db.session.add(new_fee)
        print(f"exchange_order_id:{old_fee_row.exchange_order_id} add new_fee_row")
    db.session.commit()
    print("session commit. done")


def deduct_sys_user_fee():

    from app.models import BalanceUpdateBusiness
    from app.models.exchange import AssetExchangeOrder, AssetExchangeOrderTransferHistory
    from app.business import ServerClient, SPOT_ACCOUNT_ID, BalanceBusiness
    from app.utils import amount_to_str

    finished_orders: List[AssetExchangeOrder] = AssetExchangeOrder.query.filter(
        AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED,
        AssetExchangeOrder.result != AssetExchangeOrder.Result.FAILED,  # 兑换失败没手续费
    ).all()
    order_map = {i.id: i for i in finished_orders}
    order_ids = list(order_map.keys())
    old_fee_rows: List[AssetExchangeOrderTransferHistory] = AssetExchangeOrderTransferHistory.query.filter(
        AssetExchangeOrderTransferHistory.exchange_order_id.in_(order_ids),
        AssetExchangeOrderTransferHistory.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_FEE,
    ).all()
    sys_user_fee_map = defaultdict(lambda: defaultdict(Decimal))
    for old_fee in old_fee_rows:
        order = order_map[old_fee.exchange_order_id]
        sys_user_id = order.sys_user_id
        sys_user_fee_map[sys_user_id][old_fee.asset] += old_fee.amount
    print(f"total deduct sys_user_ids: {list(sys_user_fee_map.keys())}")

    client = ServerClient()
    for sys_user_id, fee_map in sys_user_fee_map.items():
        for asset, amount in fee_map.items():
            print(f"deduct {sys_user_id} {amount_to_str(amount)} {asset}")

            balance_update_business_id = BalanceUpdateBusiness.new_id(
                sys_user_id,
                asset,
                -amount,
            )
            try:
                client.add_user_balance(
                    user_id=sys_user_id,
                    asset=asset,
                    amount=-amount,
                    business=BalanceBusiness.SYSTEM,
                    business_id=balance_update_business_id,
                    detail={"remark": "deduct 4days exchange order fee"},
                    account_id=SPOT_ACCOUNT_ID,
                )
            except Exception as _e:
                print(print(f"    deduct {sys_user_id} {amount_to_str(amount)} {asset} error: {_e}\n"))


@click.command()
@click.argument("command", required=True)
def main(command):
    if command == "generate":
        generate_exchange_new_fee_record()
    elif command == "deduct":
        deduct_sys_user_fee()
    else:
        print("unknown command")


if __name__ == "__main__":
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
