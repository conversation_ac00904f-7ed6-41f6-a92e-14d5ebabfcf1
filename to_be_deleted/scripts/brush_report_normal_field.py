import datetime
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    import datetime
    from decimal import Decimal
    from app.models import db, DailyCoinTrade, DailySpotTradeMarketReport, DailySpotTradeCoinReport
    from collections import defaultdict

    from app.caches import MarketCache

    from app.business.export.base import add_print
    from app.business.market_maker import MarketMakerHelper
    from app.business import TradeSummaryDB
    from app.utils import next_month

    @add_print
    def brush_monthly_coin_trade(start_month, end_month):
        from app.models import MonthlyCoinTrade

        normal_fee_usd_map, normal_deal_volume = defaultdict(Decimal), defaultdict(Decimal)
        trade_fee_map = defaultdict(Decimal)
        query = DailyCoinTrade.query.filter(
            DailyCoinTrade.report_date >= start_month,
            DailyCoinTrade.report_date < end_month
        )
        assets = set()
        for item in query.all():
            normal_deal_volume[item.trading_area] += item.normal_deal_volume
            normal_fee_usd_map[item.trading_area] += item.normal_fee_usd
            trade_fee_map[item.trading_area] += item.fee_usd
            assets.add(item.trading_area)
        model = MonthlyCoinTrade
        for asset in assets:
            row = model.get_or_create(start_month, asset)
            if not row.id:
                continue
            row.fee_usd = trade_fee_map[asset]
            normal_deal_rate = (normal_deal_volume[asset] / (row.deal_volume * 2)) if row.deal_volume else Decimal()
            normal_fee_usd_rate = (normal_fee_usd_map[asset] / row.fee_usd) if row.fee_usd else Decimal()
            row.normal_deal_volume = normal_deal_volume[asset]
            row.normal_fee_usd = normal_fee_usd_map[asset]
            row.normal_deal_rate = normal_deal_rate
            row.normal_fee_usd_rate = normal_fee_usd_rate
        db.session.commit()

    @add_print
    def brush_daily_coin_trade(report_date):
        normal_user_deal_volumes = defaultdict(Decimal)
        user_trade_summary_list = list_user_trade_summary(report_date, fields='user_id, money_asset, deal_volume')
        assets = set()
        for item in user_trade_summary_list:
            user_id, money_asset, deal_volume = item
            if deal_volume <= 0:
                continue
            if user_id in maker_ids:
                continue
            normal_user_deal_volumes[money_asset] += deal_volume
            assets.add(money_asset)
        user_fee_data = TradeSummaryDB.daily_trade_fee_list(report_date)
        normal_user_deal_fees = defaultdict(Decimal)
        trade_fee_map = defaultdict(Decimal)
        price_rates = get_price_rates(report_date)
        market_map = dict()
        markets = {item['market'] for item in user_fee_data}
        for market in markets:
            market_map[market] = MarketCache(market).dict
        for fee_ in user_fee_data:
            market_data = market_map.get(fee_['market'])
            if not market_data:
                print(f'{fee_["market"]} not found.')
                continue
            asset_ = market_data['quote_asset']
            trade_fee_map[asset_] += fee_['fee'] * price_rates.get(fee_['asset'], 0)
            if fee_['user_id'] not in maker_ids:
                normal_user_deal_fees[asset_] += fee_['fee'] * price_rates.get(fee_['asset'], 0)
                assets.add(asset_)

        model = DailyCoinTrade
        for asset in assets:
            row = model.get_or_create(report_date, asset)
            if not row.id:  #
                continue
            normal_volume = normal_user_deal_volumes[asset]
            normal_deal_rate = (normal_volume / (row.deal_volume * 2)) if row.deal_volume else Decimal()
            normal_fee_usd = normal_user_deal_fees[asset]
            fee_usd = trade_fee_map[asset]
            normal_fee_usd_rate = (normal_fee_usd / fee_usd) if fee_usd else Decimal()
            row.normal_deal_volume = normal_volume
            row.normal_deal_rate = normal_deal_rate
            row.normal_fee_usd = normal_fee_usd
            row.normal_fee_usd_rate = normal_fee_usd_rate
            row.fee_usd = fee_usd
        db.session.commit()

    @add_print
    def brush_monthly_market_trade(start_month, end_month):
        from app.models import MonthlySpotTradeMarketReport

        normal_fee_usd_map, normal_deal_volume = defaultdict(Decimal), defaultdict(Decimal)
        query = DailySpotTradeMarketReport.query.filter(
            DailySpotTradeMarketReport.report_date >= start_month,
            DailySpotTradeMarketReport.report_date < end_month
        )
        markets = set()
        for item in query.all():
            normal_deal_volume[item.market] += item.normal_deal_volume
            normal_fee_usd_map[item.market] += item.normal_fee_usd
            markets.add(item.market)
        model = MonthlySpotTradeMarketReport
        for market in markets:
            row = model.get_or_create(start_month, market)
            if not row.id:
                continue
            normal_deal_rate = (normal_deal_volume[market] / (row.trade_volume * 2)) if row.trade_volume else Decimal()
            normal_fee_usd_rate = (normal_fee_usd_map[market] / row.fee_usd) if row.fee_usd else Decimal()
            row.normal_deal_rate = normal_deal_rate
            row.normal_fee_usd_rate = normal_fee_usd_rate
            row.normal_deal_volume = normal_deal_volume[market]
            row.normal_fee_usd = normal_fee_usd_map[market]
        db.session.commit()

    @add_print
    def brush_daily_market_trade(report_date):
        normal_user_deal_volumes = defaultdict(Decimal)
        user_trade_summary_list = list_user_trade_summary(report_date, fields='user_id, market, deal_volume')
        markets = set()
        for item in user_trade_summary_list:
            user_id, market, deal_volume = item
            if deal_volume <= 0:
                continue
            if user_id in maker_ids:
                continue
            normal_user_deal_volumes[market] += deal_volume
            markets.add(market)
        user_fee_data = TradeSummaryDB.daily_trade_fee_list(report_date)
        normal_user_deal_fees = defaultdict(Decimal)
        trade_fee_map = defaultdict(Decimal)
        price_rates = get_price_rates(report_date)
        for fee_ in user_fee_data:
            trade_fee_map[fee_['market']] += fee_['fee'] * price_rates.get(fee_['asset'], 0)
            if fee_['user_id'] not in maker_ids:
                normal_user_deal_fees[fee_['market']] += fee_['fee'] * price_rates.get(fee_['asset'], 0)
                markets.add(fee_['market'])

        model = DailySpotTradeMarketReport
        for market in markets:
            row = model.get_or_create(report_date, market)
            if not row.id:  #
                continue
            normal_volume = normal_user_deal_volumes[market]
            normal_deal_rate = (normal_volume / (row.trade_volume * 2)) if row.trade_volume else Decimal()
            normal_fee_usd = normal_user_deal_fees[market]
            fee_usd = trade_fee_map[market]
            normal_fee_usd_rate = (normal_fee_usd / fee_usd) if fee_usd else Decimal()
            row.normal_deal_volume = normal_volume
            row.normal_deal_rate = normal_deal_rate
            row.normal_fee_usd = normal_fee_usd
            row.normal_fee_usd_rate = normal_fee_usd_rate
        db.session.commit()

    @add_print
    def brush_monthly_spot_coin_trade(start_month, end_month):
        from app.models import MonthlySpotTradeCoinReport

        normal_fee_usd_map, normal_deal_volume = defaultdict(Decimal), defaultdict(Decimal)
        query = DailySpotTradeCoinReport.query.filter(
            DailySpotTradeCoinReport.report_date >= start_month,
            DailySpotTradeCoinReport.report_date < end_month
        )
        assets = set()
        for item in query.all():
            normal_deal_volume[item.coin] += item.normal_deal_volume_usd
            normal_fee_usd_map[item.coin] += item.normal_fee_usd
            assets.add(item.coin)
        model = MonthlySpotTradeCoinReport
        for asset in assets:
            row = model.get_or_create(start_month, asset)
            if not row.id:
                continue
            normal_deal_rate = (normal_deal_volume[asset] / (row.trade_usd * 2)) if row.trade_usd else Decimal()
            normal_fee_usd_rate = (normal_fee_usd_map[asset] / row.fee_usd) if row.fee_usd else Decimal()
            row.normal_deal_rate = normal_deal_rate
            row.normal_fee_usd_rate = normal_fee_usd_rate
            row.normal_deal_volume_usd = normal_deal_volume[asset]
            row.normal_fee_usd = normal_fee_usd_map[asset]
        db.session.commit()

    @add_print
    def brush_daily_spot_coin_trade(report_date):
        normal_user_deal_volumes = defaultdict(Decimal)
        user_trade_summary_list = list_user_trade_summary(report_date, fields='user_id, stock_asset, money_asset, deal_volume')
        assets = set()
        price_rates = get_price_rates(report_date)
        for item in user_trade_summary_list:
            user_id, stock_asset, money_asset, deal_volume = item
            if deal_volume <= 0:
                continue
            if user_id in maker_ids:
                continue
            normal_user_deal_volumes[stock_asset] += deal_volume * price_rates.get(money_asset, 0)
            assets.add(stock_asset)
        user_fee_data = TradeSummaryDB.daily_trade_fee_list(report_date)
        market_map = dict()
        markets = {item['market'] for item in user_fee_data}
        for market in markets:
            market_map[market] = MarketCache(market).dict
        normal_user_deal_fees = defaultdict(Decimal)
        trade_fee_map = defaultdict(Decimal)
        for fee_ in user_fee_data:
            market_data = market_map.get(fee_['market'])
            if not market_data:
                print(f'{fee_["market"]} not found.')
                continue
            asset_ = market_data['base_asset']
            trade_fee_map[asset_] += fee_['fee'] * price_rates.get(fee_['asset'], 0)
            if fee_['user_id'] not in maker_ids:
                normal_user_deal_fees[asset_] += fee_['fee'] * price_rates.get(fee_['asset'], 0)
                assets.add(asset_)

        model = DailySpotTradeCoinReport
        for asset in assets:
            row = model.get_or_create(report_date, asset)
            if not row.id:  #
                continue
            normal_volume_usd = normal_user_deal_volumes[asset]
            normal_deal_rate = (normal_volume_usd / (row.trade_usd * 2)) if row.trade_usd else Decimal()
            normal_fee_usd = normal_user_deal_fees[asset]
            fee_usd = trade_fee_map[asset]
            normal_fee_usd_rate = (normal_fee_usd / fee_usd) if fee_usd else Decimal()
            row.normal_deal_volume_usd = normal_volume_usd
            row.normal_deal_rate = normal_deal_rate
            row.normal_fee_usd = normal_fee_usd
            row.normal_fee_usd_rate = normal_fee_usd_rate
        db.session.commit()

    def list_user_trade_summary(date, fields: str):
        date_str = date.strftime('%Y-%m-%d')
        month_str = date.strftime('%Y%m')
        sql = "SELECT {} " \
              "FROM user_trade_summary_{} " \
              "WHERE trade_date = '{}' ".format(fields, month_str, date_str)
        trade_summary_db = TradeSummaryDB.db
        cursor = trade_summary_db.cursor()
        cursor.execute(sql)
        return cursor.fetchall()

    def get_price_rates(report_date):
        from app.models import AssetPrice

        if report_date not in range_price_rates:
            price_rate = AssetPrice.get_close_price_map(report_date)
            range_price_rates[report_date] = price_rate
        return range_price_rates[report_date]

    maker_ids = MarketMakerHelper.list_all_maker_ids()
    range_price_rates = {}
    begin_date = datetime.date(2023, 1, 1)
    end_date = datetime.date.today()
    cur_date = begin_date
    while cur_date < end_date:
        print(f'{cur_date}...')
        brush_daily_coin_trade(cur_date)
        cur_date += datetime.timedelta(days=1)

    end_month = datetime.date(end_date.year, end_date.month, 1)
    cur_month = datetime.date(begin_date.year, begin_date.month, 1)
    while cur_month < end_month:
        print(f'{cur_month}...')
        _end_month = next_month(cur_month.year, cur_month.month, 1)
        brush_monthly_coin_trade(cur_month, _end_month)
        cur_month = _end_month

    cur_date = begin_date
    while cur_date < end_date:
        print(f'{cur_date}...')
        brush_daily_market_trade(cur_date)
        cur_date += datetime.timedelta(days=1)

    end_month = datetime.date(end_date.year, end_date.month, 1)
    cur_month = datetime.date(begin_date.year, begin_date.month, 1)
    while cur_month < end_month:
        print(f'{cur_month}...')
        _end_month = next_month(cur_month.year, cur_month.month, 1)
        brush_monthly_market_trade(cur_month, _end_month)
        cur_month = _end_month

    cur_date = begin_date
    while cur_date < end_date:
        print(f'{cur_date}...')
        brush_daily_spot_coin_trade(cur_date)
        cur_date += datetime.timedelta(days=1)

    end_month = datetime.date(end_date.year, end_date.month, 1)
    cur_month = datetime.date(begin_date.year, begin_date.month, 1)
    while cur_month < end_month:
        print(f'{cur_month}...')
        _end_month = next_month(cur_month.year, cur_month.month, 1)
        brush_monthly_spot_coin_trade(cur_month, _end_month)
        cur_month = _end_month


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
