import json
import os
import sys
from collections import defaultdict

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import db, NotificationBar

    model = NotificationBar
    rows = model.query.all()
    for row in rows:
        trigger_page_params = row.trigger_page_params or ''
        if trigger_page_params:
            trigger_page_params = json.loads(trigger_page_params)

        trigger_pages = [
            dict(
                trigger_page=row.trigger_page.name,
                trigger_page_params=trigger_page_params,
            )
        ]
        row.trigger_pages = json.dumps(trigger_pages)

    db.session.commit()

    # TODO: remove [model.trigger_page, model.trigger_page_params] after all the old data has been cleaned up


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
