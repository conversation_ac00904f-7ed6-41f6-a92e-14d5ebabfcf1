import time
from decimal import Decimal

from flask import current_app
from werkzeug.datastructures import MultiDict

from app.models import UserLiquidity
from app.business.amm import remove_liquidity
from app.business.margin.transfer import MarginTransferOperation
from app.models import PoolBalanceFix
from app.business import ServerClient, PriceManager, PerpetualServerClient, perpetual_transfer_out
from app.business.margin.helper import MarginUserAccountInfo, MarginAccountHelper
from app.business.margin.repayment import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>per
from app.models import MarginLoanOrder
from app.utils import quantize_amount


def get_process_user_ids() -> set:
    user_ids = set()
    records = PoolBalanceFix.query.all()
    for r in records:
        if r.remain_amount > Decimal('0'):
            user_ids.add(r.user_id)
    return user_ids


def cancel_user_all_orders(user_id):
    c = ServerClient()
    c.cancel_user_all_order(user_id, -1, None)
    p_client = PerpetualServerClient()
    p_client.cancel_batch_orders(user_id, None, [])
    return True


def force_flat_margin(user_ids):
    if MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id.in_(user_ids),
            MarginLoanOrder.status.in_(
                [MarginLoanOrder.StatusType.BURST])).first():
        current_app.logger.warning("is liquidation, break")
        return

    records = MarginLoanOrder.query.filter(
        MarginLoanOrder.user_id.in_(user_ids),
        MarginLoanOrder.status.in_([
            MarginLoanOrder.StatusType.PASS,
            ])
    ).all()
    total_order_count = len(records)
    user_order_mapping = MultiDict([
        (v.user_id, v)
        for v in records
    ])
    current_app.logger.info(f"total user count {len(user_order_mapping)}")
    current_app.logger.info(f"total loan order count {total_order_count}")
    # force flat margin loan order
    for user_id, order_list in user_order_mapping.lists():
        force_tool = MarginForceFlatHelper(user_id)
        for order in order_list:
            force_tool.add_order(order)
        force_tool.force_flat()
    time.sleep(20)
    current_app.logger.info("start margin force flat")


def check_margin_balance(user_id, only_frozen=True):
    r = ServerClient().get_user_accounts_balances(user_id)
    margin_accounts = MarginUserAccountInfo(user_id).all_account_info
    for account, balances in r.items():
        account = int(account)
        if account <= 0 or account not in margin_accounts:
            continue
        for asset, bs in balances.items():
            av = bs["available"]
            frozen = bs['frozen']
            if frozen > Decimal('0'):
                current_app.logger.warning(f"{user_id} has frozen margin balance")
                return False
            if only_frozen:
                continue
            if av > Decimal("0"):
                current_app.logger.warning(f"{user_id} has available margin balance")
                return False
        return True


def transfer_margin_balance(user_id):
    margin_accounts = MarginUserAccountInfo(user_id).all_account_info

    prices = PriceManager.assets_to_usd()
    r = ServerClient().get_user_accounts_balances(user_id)
    for account, balances in r.items():
        account = int(account)
        if account <= 0 or account not in margin_accounts:
            continue
        for asset, bs in balances.items():
            av = bs['available']
            av = quantize_amount(av, 8)
            if prices.get(asset, 0) * av < 1:
                continue

            h = MarginAccountHelper(user_id, account)
            avs = h.calculate_transfer_out_max_amount()
            if asset == h.account_detail['sell_asset_type']:
                at = 'sell_type'
            elif asset == h.account_detail['buy_asset_type']:
                at = 'buy_type'
            else:
                raise ValueError

            av = avs[at]
            if prices.get(asset, 0) * av < 1:
                continue

            print(f'transfer margin {account} {user_id} {av} {asset}')
            op = MarginTransferOperation(user_id, account, 0, asset, av)
            op.transfer()


def force_flat_perpetual(user_id):
    from app.business import PerpetualServerClient
    from app.business.fee import DEFAULT_MIN_CONTRACT_TAKER_FEE, \
        DEFAULT_MIN_CONTRACT_MAKER_FEE
    current_app.logger.info(f"start process {user_id} perpetual position")
    while True:
        p_client = PerpetualServerClient()

        def get_position_mapping(_user_id):
            all_positions = p_client.position_pending(_user_id, None)
            _position_mapping = {
                v["market"]: v["position_id"]
                for v in all_positions
            }
            return _position_mapping

        position_mapping = get_position_mapping(user_id)
        if not position_mapping:
            current_app.logger.info(f"finish flat {user_id}")
            return True

        try:
            current_app.logger.info(f"try to flat {user_id} perpetual position")
            p_client.position_close_all(
                user_id=user_id,
                taker_fee_rate=str(DEFAULT_MIN_CONTRACT_TAKER_FEE),
                maker_fee_rate=str(DEFAULT_MIN_CONTRACT_MAKER_FEE),
                source='system'
            )
        except Exception as e:
            pass
        time.sleep(10)


def transfer_perpetual_balance(user_id):
    prices = PriceManager.assets_to_usd()
    r = PerpetualServerClient().get_user_balances(user_id)
    for asset, bs in r.items():
        av = bs['transfer']
        av = quantize_amount(av, 8)
        if prices.get(asset, 0) * av < 1:
            continue
        print(f'transfer perpetual {user_id} {av} {asset}')
        perpetual_transfer_out(user_id, asset, av)


def check_perpetual_balance(user_id, only_frozen: bool = True):
    perpetual_balances = PerpetualServerClient().get_user_balances(user_id)
    for asset, balance in perpetual_balances.items():
        # total = balance['balance_total']
        # available = balance['available']
        frozen = balance['frozen']
        transfer = balance['transfer']
        if frozen > Decimal('0'):
            current_app.logger.warning(f"{user_id} has frozen perpetual balance {frozen}")
            return False
        if only_frozen:
            continue
        if transfer > Decimal('0'):
            current_app.logger.warning(f"{user_id} has transfer perpetual balance {transfer}")
            return False
    return True


def remove_amm_liquidity(user_id):
    records = UserLiquidity.query.filter(UserLiquidity.user_id == user_id, UserLiquidity.liquidity > 0).all()
    for row in records:
        remove_liquidity(row.market, row.user_id)
    time.sleep(6)


def check_amm_liquidity(user_id):
    records = UserLiquidity.query.filter(UserLiquidity.user_id == user_id, UserLiquidity.liquidity > 0).all()
    if len(records) > 0:
        current_app.logger.warning(f"{user_id} amm balance not finished")


"""
# 执行步骤
user_ids = get_process_user_ids()
# 先进行一轮撤单
for user_id in user_ids:
    cancel_user_all_orders(user_id)

# 处理杠杆平仓
force_flat_margin(user_ids)

# 处理合约平仓
for user_id in user_ids:
    force_flat_perpetual(user_id)
    
# 处理amm资产
for user_id in user_ids:
    remove_amm_liquidity(user_id)

# 检查AMM资产
for user_id in user_ids:
    check_amm_liquidity(user_id)

# 确认无误执行划转操作
for user_id in user_ids:
    transfer_margin_balance(user_id)

for user_id in user_ids:
    transfer_perpetual_balance(user_id)

# 检查杠杆资产
for user_id in user_ids:
    check_margin_balance(user_id, False)
# 检查合约资产
for user_id in user_ids:
    check_perpetual_balance(user_id, False)
"""
