import os
import sys
from collections import defaultdict
from decimal import Decimal
import logging
import requests
from flask import current_app
import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


StableTradingArea = ['USDT', 'USDC']
UnStableTradingArea = ['BTC', 'BCH']
StablePrecisionStep = [(Decimal('30'), 2), (Decimal('0.3'), 4), (Decimal('0.003'), 6),
                           (Decimal('0.00003'), 8), (Decimal('0.0000003'), 10)]
UnStablePrecisionStep = [(Decimal('0.00003'), 8), (Decimal('0.0000003'), 10)]

LOG_FORMAT = '%(asctime)s %(levelname)s %(name)s: %(message)s'
logging.basicConfig(filename='./update_depth_and_precision.log',
                    level=logging.INFO, format=LOG_FORMAT)


@click.command()
@click.argument('export', type=bool)
@click.argument('update', type=bool)
@click.argument('market-type')
@click.option('--ignore-markets')
def main(export, update, market_type, ignore_markets=''):
    """调整价格精度
    参数 export -- 是否导出深度及精度的变更 可选True or False ；
    参数 update -- 是否将变更直接更新数据库 可选True or False ；
    参数 market_type -- 操作的市场类型 可选 SPOT or PERPETUAL or ALL；
    可选参数ignore-markets -- 添加不希望更新的markets,多个市场以","分隔（中间不能带空格）
    命令举例：
    python deployment/scripts/update_market_depth_and_precision.py True
     False ALL --ignore-markets BTCUSDT,CETUSDT
    """

    if ignore_markets:
        ignore_markets = ignore_markets.upper().split(',')
    else:
        ignore_markets = []
    if market_type == 'SPOT':
        update_spot_market(export, update, ignore_markets)

    elif market_type == 'PERPETUAL':
        update_perpetual_market(export, update, ignore_markets)
    elif market_type == 'ALL':
        update_spot_market(export, update, ignore_markets)
        update_perpetual_market(export, update, ignore_markets)
    else:
        msg = '请输入要更新的市场种类！现货-->SPOT;合约-->PERPETUAL;现货+合约-->ALL'
        raise Exception(msg)


def update_spot_market(export: bool = True, update: bool = False, ignore_markets: list = None):
    """更新现货市场"""

    res = defaultdict(dict)
    market_price_dic = get_spot_market_price()
    for market, price in market_price_dic.items():
        if ignore_markets and market in ignore_markets:
            continue
        quote_asset_precision = get_quote_precision(market, price)
        if not quote_asset_precision:
            continue
        base_asset_precision = 2 if quote_asset_precision == 12 else 8
        quantize_price = price.quantize(Decimal(10) ** - quote_asset_precision)
        significant_digits, depths = get_significant_digits_and_depths(quantize_price)
        default_depth = get_default_depth(significant_digits, depths)
        res[market].update({'quote_asset_precision': quote_asset_precision,
                            'base_asset_precision': base_asset_precision,
                            'depths': ','.join(depths),
                            'default_depth': default_depth
                            })
    update_spot_db_or_export(res, market_price_dic, export, update)
    update_index_prec(export, update)


def get_spot_market_price():
    """获取现货市场markt-最新成交价price映射"""
    spot_url = 'https://api.coinex.com/v1/market/ticker/all'
    ret = requests.get(spot_url).json()
    if ret['code'] != 0:
        msg = '未能获取现货市场价格数据'
        logging.error(msg)
        raise Exception(msg)
    tickers = ret['data']['ticker']
    res = dict()
    for market, val in tickers.items():
        res[market] = Decimal(val['last'])
    return res


def get_quote_precision(market, price):
    """获取现货市场定价币种价格精度"""
    from app.models import Market

    market_model = Market.query.filter(Market.name == market).first()
    if not market_model:
        return None
    trading_area = market_model.trading_area.name
    if trading_area in StableTradingArea:
        precision_steps = StablePrecisionStep
    elif trading_area in UnStableTradingArea:
        precision_steps = UnStablePrecisionStep
    else:
        msg = '{} 未知的交易区！'.format(trading_area)
        logging.error(msg)
        raise Exception(msg)
    precision = 12
    for step in precision_steps:
        if price >= step[0]:
            precision = step[1]
            break
    return precision


def get_significant_digits_and_depths(price: Decimal):
    """
    获取市场有效数字位数及深度列表
    Usage:
    >>> t = Decimal("0.00013796")
    >>> t.as_tuple()
    >>> DecimalTuple(sign=0, digits=(1, 3, 7, 9, 6), exponent=-8)
    """
    from app.utils import amount_to_str

    d = price.as_tuple()
    exponent = d.exponent
    digits = d.digits
    significant_digits = len(digits)
    if significant_digits == 1:     # 只有一位有效数字
        depth = amount_to_str(Decimal(10) ** exponent)
        return significant_digits, [depth]
    depths = []
    for i in range(significant_digits-1):
        e = exponent + i
        depth = amount_to_str(Decimal(10) ** e)
        depths.append(depth)
    return significant_digits, depths


def get_default_depth(significant_digits, depths):
    """获取默认深度"""
    if significant_digits >= 5:
        return depths[-4]
    else:
        return depths[0]


def update_spot_db_or_export(res, market_price_dic, export, update):
    """更新或导出现货市场 Markt表"""
    from app.models import db, Market
    from app.business import update_markets
    from app.utils import amount_to_str

    markets = list(res.keys())
    records = Market.query.filter(Market.name.in_(markets)).all()
    changes = []
    msg_list = []
    for record in records:
        market = record.name
        new_data = res[market]
        for k, v in new_data.items():
            change = {}
            original_data = getattr(record, k)
            if k == 'depths':
                original_data, v = _reorder_depths(original_data), _reorder_depths(v)
            if original_data != v:
                change.update({'market': market,
                               'price': amount_to_str(market_price_dic[market]),
                               'field': k,
                               'original_data': original_data,
                               'new_data': v
                               })
                changes.append(change)
                msg = '现货市场：{},原数据：{}={},修改后数据：{}={}'.format(
                    market, k, original_data, k, v)
                msg_list.append(msg)
                setattr(record, k, v)
    if export:
        if not changes:
            print('所有现货市场没有需要更新的内容')
        else:
            remark = '现货市场变更的导出数据：\n'
            export_data(changes, remark)
    if update:
        db.session.commit()
        logging.info('\n'.join(msg_list))
        try:
            update_markets()
        except Exception as e:
            current_app.logger.error(f'现货市场更新完毕，更新cache或server接口调用时存在异常,msg：{e}')


def _reorder_depths(depths):
    depth_lis = depths.split(',')
    depths_list = [i.strip() for i in depth_lis]
    depths_list.sort(key=lambda x: Decimal(x))
    return ','.join(depths_list)


def update_index_prec(export, update):
    """更新或导出 现货指数价格精度 MarginIndex表及ComposeIndex表，
    通知server更新配置"""
    from app.caches import IndexPriceCache
    from app.models import MarginIndex, ComposeIndex, db
    from app.business import ServerClient
    from app.utils import amount_to_str

    cache = IndexPriceCache()
    market_idx_price_dic = cache.get_all_price()
    markets = list(market_idx_price_dic.keys())
    records = MarginIndex.query.filter(
        MarginIndex.market_name.in_(markets)).all()
    changes = []
    msg_list = []
    for record in records:
        market = record.market_name
        idx_price = market_idx_price_dic[market]
        original_data = record.price_precision
        new_prec = get_quote_precision(market, idx_price)
        if new_prec != original_data:
            changes.append({'market': market,
                            'price': amount_to_str(idx_price),
                            'field': 'price_precision',
                            'original_data': original_data,
                            'new_data': new_prec
                            })
            msg = '现货市场{} 指数价格精度：原数据：price_precision={},修改后数据：price_precision={}'.format(
                market, original_data, new_prec)
            msg_list.append(msg)
            record.price_precision = new_prec
    compose_records = ComposeIndex.query.filter(ComposeIndex.name.in_(markets)).all()
    for rec in compose_records: # 如有合成指数价格，需更新合成指数价格表
        market = rec.name
        idx_price = market_idx_price_dic[market]
        original_data = rec.price_precision
        new_prec = get_quote_precision(market, idx_price)
        if new_prec != original_data:
            changes.append({'market': market,
                            'price': amount_to_str(idx_price),
                            'field': 'price_precision',
                            'original_data': original_data,
                            'new_data': new_prec
                            })
            msg = '现货市场{} 指数价格精度：原数据：price_precision={},修改后数据：price_precision={}'.format(
                market, original_data, new_prec)
            msg_list.append(msg)
            rec.price_precision = new_prec
    if export:
        if not changes:
            print('所有现货市场指数价格没有需要更新的内容')
        else:
            remark = '现货市场指数价格精度变更的导出数据：\n'
            export_data(changes, remark)
    if update:
        db.session.commit()
        logging.info('\n'.join(msg_list))
        try:
            client = ServerClient(current_app.logger)
            client.update_index()
        except Exception as e:
            current_app.logger.error(f'现货指数价格更新完毕，调用server接口时异常，msg：{e}')


def update_perpetual_market(export: bool = True, update: bool = False, ignore_markets: list = None):
    """更新合约市场"""

    res = defaultdict(dict)
    market_price_dic, market_index_price_dic = get_perpetual_market_price()
    for market, price in market_price_dic.items():
        if ignore_markets and market in ignore_markets:
            continue
        quote_asset_precision = get_perpetual_quote_precision(price)
        if not quote_asset_precision:
            continue
        base_asset_precision = 2 if quote_asset_precision == 12 else 8
        quantize_price = price.quantize(Decimal(10) ** - quote_asset_precision)
        significant_digits, depths = get_significant_digits_and_depths(quantize_price)
        depth_default = get_default_depth(significant_digits, depths)
        res[market].update({'quote_asset_precision': quote_asset_precision,
                            'base_asset_precision': base_asset_precision,
                            'price_size': Decimal(depths[0]),
                            'depths': ','.join(depths),
                            'depth_default': Decimal(depth_default)  # 此字段名以及字段类型合约跟现货不一样
                            })
    update_perpetual_db_or_export(res, market_price_dic, export, update)
    update_perpetual_index_prec(market_index_price_dic, export, update)


def get_perpetual_market_price():
    """获取合约市场-price映射及指数价格-price映射"""
    perpetual_url = 'https://api.coinex.com/perpetual/v1/market/ticker/all'
    ret = requests.get(perpetual_url).json()
    if ret['code'] != 0:
        msg = '未能获取合约市场价格数据'
        logging.error(msg)
        raise Exception(msg)
    tickers = ret['data']['ticker']
    market_price = dict()
    market_index_price = dict()
    for market, val in tickers.items():
        if '_' in market:   # 带 "_SIGNPRICE" 和 "_INDEXPRICE" 的是标记价格和指数价格
            market_lis = market.split('_')
            if market_lis[1] == 'INDEXPRICE':
                market_index_price[market_lis[0]] = Decimal(val['last'])
        else:
            market_price[market] = Decimal(val['last'])
    return market_price, market_index_price


def get_perpetual_quote_precision(price):
    """获取合约市场价格精度"""
    precision = 12
    for step in StablePrecisionStep:
        if price >= step[0]:
            precision = step[1]
            break
    return precision


def update_perpetual_db_or_export(res, market_price_dic, export, update):
    """更新或导出合约市场 PerpetualMarket表"""
    from app.models import db, PerpetualMarket
    from app.utils import amount_to_str

    markets = list(res.keys())
    records = PerpetualMarket.query.filter(PerpetualMarket.name.in_(markets)).all()
    changes = []
    msg_list = []
    for record in records:
        market = record.name
        new_data = res[market]
        for k, v in new_data.items():
            change = {}
            original_data = getattr(record, k)
            if original_data != v:
                change.update({'market': market,
                               'price': amount_to_str(market_price_dic[market]),
                               'field': k,
                               'original_data': original_data,
                               'new_data': v
                               })
                changes.append(change)
                msg = '合约市场：{}，原数据：{}={}，修改后数据：{}={}'.format(
                    market, k, original_data, k, v)
                msg_list.append(msg)
                setattr(record, k, v)
    if export:
        if not changes:
            print('所有合约市场没有需要更新的内容')
        else:
            remark = '合约市场变更的导出数据：\n'
            export_data(changes, remark)
    if update:
        db.session.commit()
        logging.info('\n'.join(msg_list))
        update_perpetual_cache()


def update_perpetual_cache():
    """更新合约市场缓存及通知server更新配置"""
    from app.caches import PerpetualMarketCache
    from app.business import PerpetualServerClient

    PerpetualMarketCache().reload()
    try:
        client = PerpetualServerClient(current_app.logger)
        client.update_market()
    except Exception as e:
        current_app.logger.error(f'合约市场数据更新完成后，调用server更新接口存在异常！msg: {e}')


def update_perpetual_index_prec(market_idx_price_dic, export, update):
    """更新或导出 合约市场指数价格精度PerpetualMarketIndex, PerpetualComposeIndex表，
    通知server更新配置"""
    from app.models import PerpetualMarketIndex, PerpetualComposeIndex, db
    from app.business import PerpetualServerClient
    from app.utils import amount_to_str

    markets = list(market_idx_price_dic.keys())
    records = PerpetualMarketIndex.query.filter(
        PerpetualMarketIndex.name.in_(markets)).all()
    changes = []
    msg_list = []
    for record in records:
        market = record.name
        idx_price = market_idx_price_dic[market]
        original_data = record.price_precision
        new_prec = get_perpetual_quote_precision(idx_price)
        if new_prec != original_data:
            changes.append({'market': market,
                            'price': amount_to_str(idx_price),
                            'field': 'price_precision',
                            'original_data': original_data,
                            'new_data': new_prec
                            })
            msg = '合约市场{} 指数价格精度：原数据：price_precision={},修改后数据：price_precision={}'.format(
                market, original_data, new_prec)
            msg_list.append(msg)
            record.price_precision = new_prec
    compose_records = PerpetualComposeIndex.query.filter(PerpetualComposeIndex.name.in_(markets)).all()
    for rec in compose_records:  # 如有合成指数价格，需更新合成指数价格表
        market = rec.name
        idx_price = market_idx_price_dic[market]
        original_data = rec.price_precision
        new_prec = get_perpetual_quote_precision(idx_price)
        if new_prec != original_data:
            changes.append({'market': market,
                            'price': amount_to_str(idx_price),
                            'field': 'price_precision',
                            'original_data': original_data,
                            'new_data': new_prec
                            })
            msg = '合约市场{} 指数价格精度：原数据：price_precision={},修改后数据：price_precision={}'.format(
                market, original_data, new_prec)
            msg_list.append(msg)
            rec.price_precision = new_prec
    if export:
        if not changes:
            print('所有合约市场指数价格没有需要更新的内容')
        else:
            remark = '合约市场指数价格精度变更的导出数据：\n'
            export_data(changes, remark)
    if update:
        db.session.commit()
        logging.info('\n'.join(msg_list))
        try:
            client = PerpetualServerClient(current_app.logger)
            client.update_index()
        except Exception as e:
            current_app.logger.error(f'合约指数价格精度更新完毕，调用server接口存在异常，msg：{e}')


def export_data(data_lis, remark):
    """导出数据的修改"""
    from app.utils import ExcelExporter, upload_file

    headers = ['市场', '价格', '字段', '原内容', '新内容']
    fields = ['market', 'price', 'field', 'original_data', 'new_data']
    data_streams = ExcelExporter(data_list=data_lis, headers=headers, fields=fields).export_streams()
    file_url = upload_file(data_streams, 'xlsx')
    print('{}{}'.format(remark, file_url))
    print('-'*50)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
