import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import db
    from app.models.wallet import DepositWithdrawalPopupWindowContent
    from app.common.constants import Language
    from app.utils import batch_iter

    records = DepositWithdrawalPopupWindowContent.query.with_entities(
        DepositWithdrawalPopupWindowContent.popup_window_id,
        DepositWithdrawalPopupWindowContent.lang,
    ).all()

    id2langs = {}
    for _id, lang in records:
        id2langs.setdefault(_id, set()).add(lang)

    pending_languages = {
        Language.FA_IR,
        Language.KO_KP,
        Language.ID_ID,
        Language.TR_TR,
        Language.VI_VN,
        Language.AR_AE,
        Language.FR_FR,
        Language.DE_DE,
        Language.PT_PT,
    }
    pending_records = []
    for _id, langs in id2langs.items():
        new_langs = pending_languages - langs
        for lang in new_langs:
            pending_records.append(
                DepositWithdrawalPopupWindowContent(
                    popup_window_id=_id,
                    lang=lang,
                    url='',
                    title='',
                    content='',
                )
            )
    for chunk_records in batch_iter(pending_records, 1000):
        db.session.bulk_save_objects(chunk_records)
        db.session.flush()
    if pending_records:
        db.session.commit()


if __name__ == '__main__':
    from app import create_app, Language

    with create_app().app_context():
        main()
