# -*- coding: utf-8 -*-
import os
import sys

from sqlalchemy import func


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from dateutil.tz import UTC
from datetime import datetime
from app.utils import now, ExcelExporter, upload_file
from app.models import ReferralHistory, User, Deposit


def main():
    start_time = datetime(2020, 9, 22, 6, 0, tzinfo=UTC)
    end_time = datetime(2020, 9, 29, 10, 18, tzinfo=UTC)
    deposit_user_list = Deposit.query.filter(
        Deposit.asset == 'AVAX',
        Deposit.updated_at >= start_time,
        Deposit.updated_at < end_time,
        Deposit.type.in_([Deposit.Type.ON_CHAIN,
                          Deposit.Type.ABNORMAL]),
        Deposit.status.in_([
            Deposit.Status.FINISHED,
            Deposit.Status.TO_HOT]),
    ).with_entities(
        Deposit.user_id,
        Deposit.asset,
        func.sum(Deposit.amount).label('amount')
    ).group_by(
        Deposit.user_id,
    ).all()
    deposit_user_map = {user.user_id: user.amount for user in deposit_user_list}
    user_list = User.query.filter(
        User.id.in_(list(deposit_user_map.keys())),
        User.created_at >= start_time,
        User.created_at < end_time,
    ).with_entities(
        User.id,
    )
    user_ids = [user.id for user in user_list]

    print('len of user_ids is:{}'.format(len(user_ids)))

    result_list = []
    for user_id in user_ids:
        referral = ReferralHistory.query.filter(ReferralHistory.referree_id == user_id).first()
        if referral:
            result_list.append(
                {
                    'referree_id': user_id,
                    'referree_email': referral.referree.email,
                    'referree_ip': referral.referree.registration_ip,
                    'referrer_id': referral.referrer_id,
                    'referrer_email': referral.referrer.email,
                    'referrer_ip': referral.referrer.registration_ip,
                    'amount': deposit_user_map[user_id],
                }
            )
        # 不存在邀请关系的用户
        else:
            user_data = User.query.filter(User.id == user_id).first()
            result_list.append(
                {
                    'referree_id': user_id,
                    'referree_email': user_data.email,
                    'referree_ip': user_data.registration_ip,
                    'referrer_id': '',
                    'referrer_email': '',
                    'referrer_ip': '',
                    'amount': deposit_user_map[user_id],
                }
            )

    header_data = [
        ('referree_id', '被邀请用户id'),
        ('referree_email', '被邀请人注册邮箱'),
        ('referree_ip', '被邀请人注册ip'),
        ('amount', '被邀请人充值AVAX的数量'),
        ('referrer_id', '邀请人id'),
        ('referrer_email', '邀请人邮箱'),
        ('referrer_ip', '邀请人注册ip'),
    ]
    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]
    streams = ExcelExporter(
        data_list=result_list, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
