# -*- coding: utf-8 -*-
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


def main():
    from app.models import db
    from app.models.daily import DailyAmmMarketReport
    from app.utils import quantize_amount

    last_id = 0
    limit = 5000
    while True:
        rows = DailyAmmMarketReport.query.filter(
            DailyAmmMarketReport.id > last_id,
        ).order_by(DailyAmmMarketReport.id).limit(limit).all()
        if rows:
            print(f"brush rows {last_id} ~ {rows[-1].id} ...")
            last_id = rows[-1].id

        for row in rows:
            row: DailyAmmMarketReport
            if not row.fee_refunded:
                continue
            if not row.refunded_fee_base_amount:
                row.refunded_fee_base_amount = quantize_amount(row.fee_base_amount * row.fee_refund_rate, 8)
            if not row.refunded_fee_quote_amount:
                row.refunded_fee_quote_amount = quantize_amount(row.fee_quote_amount * row.fee_refund_rate, 8)
            if not row.refunded_fee_usd:
                row.refunded_fee_usd = quantize_amount(row.fee_usd * row.fee_refund_rate, 8)
        db.session.commit()

        if len(rows) != limit:
            break

    print("brush done")


if __name__ == "__main__":
    from app import create_app

    with create_app().app_context():
        main()
