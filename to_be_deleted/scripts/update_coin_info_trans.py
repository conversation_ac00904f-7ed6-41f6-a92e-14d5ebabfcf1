import json
import os
import sys

from tqdm import tqdm


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import CoinInformationTrans, db
    from app.utils import batch_iter

    print('开始更新币种描述...')
    coin_trans_records = CoinInformationTrans.query.all()
    process_bar = tqdm(total=len(coin_trans_records))
    for trans_recs in batch_iter(coin_trans_records, 200):
        for trans_rec in trans_recs:
            introduces = trans_rec.introduces
            if not introduces:
                continue
            fmt_introduces = add_rich_text(introduces)
            trans_rec.introduces = fmt_introduces
        db.session.commit()
        process_bar.update(200)
    print('更新完毕！')


def add_rich_text(introduces):
    introduces_json = json.loads(introduces)
    for introduce in introduces_json:
        content = introduce.get('content', '')
        rich_content = _fmt_content(content)
        introduce['rich_content'] = rich_content
    return json.dumps(introduces_json)


def trans_to_rich_text(introduces):
    introduces_json = json.loads(introduces)
    for introduce in introduces_json:
        content = introduce['content'] or ''
        fmt_content = _fmt_content(content)
        introduce['content'] = fmt_content
    return json.dumps(introduces_json)


def _fmt_content(content):
    if content:
        fmt_content = f'<div class="ql-editor"><p>{content}</p></div>'
    else:
        fmt_content = f'<div class="ql-editor">{content}</div>'
    return fmt_content


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
