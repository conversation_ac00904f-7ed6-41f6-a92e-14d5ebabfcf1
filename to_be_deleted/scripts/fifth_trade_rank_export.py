
from datetime import timedelta
import os
import sys
import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

TRADE_ACTIVITY_ID = 8


def main_export():
    from collections import defaultdict
    from datetime import datetime, timedelta
    from decimal import Decimal
    import json
    from app.business.export.base import ExportHelper
    from app.business.external_dbs import PerpetualSummaryDB
    from app.business.prices import PriceManager
    from app.models.daily import DailyPerpetualMarketReport, DailyUserReport
    from app.models.operation import TradeRankActivity, TradeRankActivityJoinUser
    from sqlalchemy import func
    from pyroaring import BitMap
    from app.models.summary import UserTradeSummary

    from app.models.user import SubAccount, UserBusinessRecord
    from app.models.wallet import AssetPrice
    from app.utils.amount import amount_to_str, quantize_amount
    from app.utils.date_ import date_to_datetime
    from app.utils.iterable import batch_iter

    activity = TradeRankActivity.query.get(TRADE_ACTIVITY_ID)
    start_date = activity.started_at
    end_date = activity.ended_at

    markets = json.loads(activity.markets)

    market_reports = DailyPerpetualMarketReport.query.filter(
        DailyPerpetualMarketReport.report_date >= start_date,
        DailyPerpetualMarketReport.report_date <= end_date,
        DailyPerpetualMarketReport.market.in_(markets)
    ).group_by(DailyPerpetualMarketReport.report_date).with_entities(
        DailyPerpetualMarketReport.report_date,
        func.sum(DailyPerpetualMarketReport.deal_amount),
    ).all()

    # 全站合约交易量
    system_perpetual_usd_map = dict(market_reports)

    # 以下两个值只需要取开始结束值
    # 合约功能渗透率 全站合约用户数/全站交易用户数
    # 合约活跃渗透率 活动期间合约用户数/全站活跃用户数
    business_records = UserBusinessRecord.query.filter(
        UserBusinessRecord.report_at >= date_to_datetime(start_date),
        UserBusinessRecord.report_at <= date_to_datetime(end_date),
        UserBusinessRecord.business.in_((UserBusinessRecord.Business.PERPETUAL_TRADE, 
                                        UserBusinessRecord.Business.SPOT_TRADE)),
    ).all()
    daily_reports = DailyUserReport.query.filter(
        DailyUserReport.report_date.in_((start_date, end_date)),
    ).with_entities(
        DailyUserReport.report_date,
        DailyUserReport.active_user
    ).all()
    daily_report_map = dict(daily_reports)

    start_perpetual_user_ids, start_trade_user_ids = set(), set()
    end_perpetual_user_ids, end_trade_user_ids = set(), set()
    acitivity_perpetual_user_set = set()
    for item in business_records:
        ids_ = set(BitMap.deserialize(item.history_user_bit_map))
        if item.report_at.date() == start_date:
            if item.business == UserBusinessRecord.Business.PERPETUAL_TRADE:
                start_perpetual_user_ids |= ids_
            start_trade_user_ids |= ids_
        if item.report_at.date() == end_date:
            if item.business == UserBusinessRecord.Business.PERPETUAL_TRADE:
                end_perpetual_user_ids |= ids_
            end_trade_user_ids |= ids_

    if start_trade_user_ids:
        start_perpetual_infiltration_rate = len(start_perpetual_user_ids) / len(start_trade_user_ids)
    else:
        start_perpetual_infiltration_rate = 0
    if end_trade_user_ids:
        end_perpetual_infiltration_rate = len(end_perpetual_user_ids) / len(end_trade_user_ids)
    else:
        end_perpetual_infiltration_rate = 0

    business_records = UserBusinessRecord.query.filter(
        UserBusinessRecord.report_at >= datetime(2022, 12, 9),
        UserBusinessRecord.report_at <= end_date,
        UserBusinessRecord.business == UserBusinessRecord.Business.PERPETUAL_TRADE,
    ).with_entities(
        UserBusinessRecord.report_at,
        UserBusinessRecord.history_user_bit_map,
    ).all()
    start_perpetual_user_ids, end_perpetual_user_ids = set(), set()
    for item in business_records:
        if datetime(2022, 12, 9).date() <= item.report_at.date() <= datetime(2022, 12, 23).date():
            start_perpetual_user_ids |= set(BitMap.deserialize(item.history_user_bit_map))
        if datetime(2022, 12, 24).date() <= item.report_at.date() <= end_date.date():
            end_perpetual_user_ids |= set(BitMap.deserialize(item.history_user_bit_map))

    start_avg_active_user_count = DailyUserReport.query.filter(
        DailyUserReport.report_date >= datetime(2022, 12, 9),
        DailyUserReport.report_date <= datetime(2022, 12, 23),
    ).with_entities(func.avg(DailyUserReport.active_user)).scalar() or 0

    end_avg_active_user_count = DailyUserReport.query.filter(
        DailyUserReport.report_date >= start_date,
        DailyUserReport.report_date <= end_date,
    ).with_entities(func.avg(DailyUserReport.active_user)).scalar() or 0

    if start_avg_active_user_count:
        start_perpetual_active_rate = len(start_perpetual_user_ids) / start_avg_active_user_count
    else:
        start_perpetual_active_rate = 0
    if end_avg_active_user_count:
        end_perpetual_active_rate = len(end_perpetual_user_ids) / end_avg_active_user_count
    else:
        end_perpetual_active_rate = 0

    join_users = TradeRankActivityJoinUser.query.filter(
        TradeRankActivityJoinUser.trade_activity_id == TRADE_ACTIVITY_ID,
    ).with_entities(
        TradeRankActivityJoinUser.created_at,
        TradeRankActivityJoinUser.user_id,
    )
    join_user_map = defaultdict(set)
    for item in join_users: 
        join_user_map[item.created_at.date()].add(item.user_id)
    all_join_user_ids = {item.user_id for item in join_users}

    activity_new_user_count_map = dict()
    no_perpetual_user_ids = set()
    for date_, user_ids in join_user_map.items():
        pre_date = date_ - timedelta(days=1)
        items = UserBusinessRecord.query.filter(
            UserBusinessRecord.report_at == date_to_datetime(pre_date),
            UserBusinessRecord.business.in_((UserBusinessRecord.Business.PERPETUAL_TRADE, 
                                        UserBusinessRecord.Business.SPOT_TRADE))
        ).with_entities(
            UserBusinessRecord.business,
            UserBusinessRecord.history_user_bit_map,
        )
        trade_user_ids = set()
        for i in items:
            ids_ = set(BitMap.deserialize(i.history_user_bit_map))
            trade_user_ids |= ids_
            if i.business == UserBusinessRecord.Business.PERPETUAL_TRADE:
                no_perpetual_user_ids |= user_ids - ids_
        activity_new_user_count_map[date_] = len(user_ids - trade_user_ids)

    new_perpetual_user_count_map = defaultdict(int)
    for ids_ in batch_iter(no_perpetual_user_ids, 5000):
        summary = UserTradeSummary.query.filter(
            UserTradeSummary.user_id.in_(ids_),
            UserTradeSummary.system == UserTradeSummary.System.PERPETUAL,
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date <= end_date,
        ).with_entities(
            UserTradeSummary.report_date,
            func.count(UserTradeSummary.user_id.distinct()),
        )
        tmp = dict(summary)
        for k, v in tmp.items():
            new_perpetual_user_count_map[k] += v

    sub_account_map = dict()
    for ids_ in batch_iter(all_join_user_ids, 5000):
        tmp = SubAccount.query.filter(
            SubAccount.main_user_id.in_(ids_),
        ).with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id
        ).all()
        sub_account_map.update(dict(tmp))

    curr_date = start_date
    daily_trade_usd_map = defaultdict(Decimal)
    user_trade_usd_map = defaultdict(lambda: defaultdict(Decimal))
    while curr_date <= end_date:
        start_ts = int(date_to_datetime(curr_date).timestamp())
        end_ts = int(date_to_datetime(curr_date + timedelta(days=1)).timestamp())
        summaries = PerpetualSummaryDB.get_trade_summary(start_ts, end_ts)
        price_map = AssetPrice.get_close_price_map(curr_date)
        for item in summaries:
            if item['asset'] == 'USD' or \
                (item['user_id'] not in join_user_map or item['user_id'] not in sub_account_map):
                continue
            user_id = sub_account_map.get(item['user_id'], item['user_id'])
            usd = item['deal_amount'] * price_map.get(item['asset'], 0)
            daily_trade_usd_map[curr_date] += usd
            user_trade_usd_map[user_id][curr_date] += usd
        curr_date += timedelta(days=1)

    min_amount_count_map = dict()
    for user_id, trade_map in user_trade_usd_map.items():
        curr_date = start_date
        tmp_usd = 0
        target_date = None
        while curr_date <= end_date:
            tmp_usd += trade_map.get(curr_date, 0)
            if tmp_usd >= activity.least_trade_amount:
                target_date = curr_date
                break
            curr_date += timedelta(days=1)
        if target_date:
            min_amount_count_map[target_date] += 1

    def _percent(value):
        return f'{quantize_amount(value, 4) * 100}%'

    data = {
        '渗透率': {
            'header_mapper': {
                'start_infiltration_rate': '起始合约功能渗透率',
                'end_infiltration_rate': '结束合约功能渗透率',
                'start_perpetual_active_rate': '起始合约活跃渗透率',
                'end_perpetual_active_rate': '结束合约活跃渗透率',
            },
            'data': [{
                'start_infiltration_rate': _percent(start_perpetual_infiltration_rate),
                'end_infiltration_rate': _percent(end_perpetual_infiltration_rate),
                'start_perpetual_active_rate': _percent(start_perpetual_active_rate),
                'end_perpetual_active_rate': _percent(end_perpetual_active_rate),
            }]
        }
    }

    curr_date = start_date
    while curr_date <= end_date:

        header_data = [
            ('system_perpetual_usd', '全站合约交易量'),
            ('join_user_count', '报名人数'),
            ('min_amount_user_count', '交易达标人数（达标率）'),
            ('activity_new_user_count', '活动新用户数'),
            ('perpetual_new_user_count', '活动转化新用户'),
            ('user_trade_usd', '活动用户交易量'),
        ]
        header_mapper = dict(header_data)
        date_str = curr_date.strftime('%Y-%m-%d')
        d = [{
            'system_perpetual_usd': amount_to_str(system_perpetual_usd_map.get(curr_date, 0), 2),
            'join_user_count': len(join_user_map.get(curr_date, set())),
            'min_amount_user_count': min_amount_count_map.get(curr_date, 0),
            'activity_new_user_count': activity_new_user_count_map.get(curr_date, 0),
            'perpetual_new_user_count': new_perpetual_user_count_map.get(curr_date, 0),
            'user_trade_usd': amount_to_str(daily_trade_usd_map.get(curr_date, 0), 2),
        }]
        data[date_str] = {
            'header_mapper': header_mapper,
            'data': d
        }
        curr_date += timedelta(days=1)
    r = ExportHelper.write_excel_by_sheets(data)
    print(r)


def track_data():
    from app.models.operation import TradeRankActivity
    from app.models.user import UserBusinessRecord
    from app.utils.date_ import date_to_datetime
    from app.utils import batch_iter
    from app.models import UserTradeFeeSummary, UserTradeSummary
    from sqlalchemy import func
    from pyroaring import BitMap

    activity = TradeRankActivity.query.get(TRADE_ACTIVITY_ID)
    start_date = activity.started_at
    end_date = activity.ended_at

    new_perpetual_user_ids = set()
    records = UserBusinessRecord.query.filter(
        UserBusinessRecord.report_at >= date_to_datetime(start_date),
        UserBusinessRecord.report_at <= date_to_datetime(end_date),
        UserBusinessRecord.business == UserBusinessRecord.Business.PERPETUAL_TRADE,
    ).with_entities(UserBusinessRecord.new_user_bit_map).all()

    for item in records:
        new_perpetual_user_ids |= set(BitMap.deserialize(item.new_user_bit_map))
    new_begin = end_date + timedelta(days=1)
    new_end = new_begin + timedelta(days=30)
    total_fee_usd = total_trade_user_count = 0
    for ids in batch_iter(new_perpetual_user_ids, 5000):
        usd = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.user_id.in_(ids),
            UserTradeFeeSummary.report_date >= new_begin,
            UserTradeFeeSummary.report_date <= new_end,
        ).with_entities(
            func.sum(UserTradeFeeSummary.trade_fee_amount)
        ).scalar() or 0
        total_fee_usd += usd
        count = UserTradeSummary.query.filter(
            UserTradeSummary.user_id.in_(ids),
            UserTradeSummary.report_date >= new_begin,
            UserTradeSummary.report_date <= new_end,
        ).with_entities(
            func.count(UserTradeSummary.user_id.distinct())
        ).scalar() or 0
        total_trade_user_count += count
    print(f'合约新用户30天手续费收入: {total_fee_usd}')
    print(f'合约新用户30天交易人数: {total_trade_user_count}')


@click.command()
@click.argument('action')
def main(action):
    if action == 'main':
        main_export()
    else:
        track_data()


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
