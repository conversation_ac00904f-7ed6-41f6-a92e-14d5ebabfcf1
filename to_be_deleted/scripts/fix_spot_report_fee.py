import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from collections import defaultdict
from datetime import date, datetime, timedelta
from decimal import Decimal
from app.models import AssetPrice, DailySpotTradeMarketReport, db, \
    DailySpotTradeCoinReport, MonthlySpotTradeMarketReport, \
    MonthlySpotTradeCoinReport
from app.utils import next_month


def fix_daily_spot_fee(report_date):
    from app.business import TradeSummaryDB
    coin_rate = AssetPrice.get_close_price_map(report_date)
    user_fee_data = TradeSummaryDB.daily_trade_fee_list(report_date)
    market_fee_map = defaultdict(Decimal)
    asset_fee_map = defaultdict(Decimal)
    for fee_ in user_fee_data:
        market_fee_map[fee_['market']] += fee_['fee'] * coin_rate.get(
            fee_['asset'], 0)
        asset_fee_map[fee_['asset']] += fee_['fee'] * coin_rate.get(
            fee_['asset'], 0)
    # 现货市场
    market_record_list = DailySpotTradeMarketReport.query.filter(
        DailySpotTradeMarketReport.report_date == report_date).all()
    for record in market_record_list:
        record.fee_usd = market_fee_map[record.market]
        db.session.add(record)
    # 现货币种
    asset_record_list = DailySpotTradeCoinReport.query.filter(
        DailySpotTradeCoinReport.report_date == report_date).all()
    for record in asset_record_list:
        record.fee_usd = asset_fee_map[record.coin]
        db.session.add(record)
    db.session.commit()


def fix_monthly_spot_market_fee(report_date):
    start_month = report_date
    end_month = next_month(start_month.year, start_month.month)
    query = DailySpotTradeMarketReport.query.filter(
        DailySpotTradeMarketReport.report_date >= start_month,
        DailySpotTradeMarketReport.report_date < end_month
    )
    fee_usd_map = defaultdict(Decimal)
    for item in query:
        fee_usd_map[item.market] += item.fee_usd

    record_list = MonthlySpotTradeMarketReport.query.filter(
        MonthlySpotTradeMarketReport.report_date == start_month
    ).all()

    for record in record_list:
        record.fee_usd = fee_usd_map[record.market]
        db.session.add(record)
    db.session.commit()


def fix_monthly_spot_asset_fee(report_date):
    start_month = report_date
    end_month = next_month(start_month.year, start_month.month)
    query = DailySpotTradeCoinReport.query.filter(
        DailySpotTradeCoinReport.report_date >= start_month,
        DailySpotTradeCoinReport.report_date < end_month
    )
    fee_usd_map = defaultdict(Decimal)
    for item in query:
        fee_usd_map[item.coin] += item.fee_usd

    record_list = MonthlySpotTradeCoinReport.query.filter(
        MonthlySpotTradeCoinReport.report_date == start_month
    ).all()

    for record in record_list:
        record.fee_usd = fee_usd_map[record.coin]
        db.session.add(record)
    db.session.commit()


def main():
    start_date = date(2019, 5, 10)  # 日报从5月10号的现货数据开始修
    start_month = date(2019, 5, 1)  # 月报从5月1号的现货月报修
    today = datetime.utcnow().date()

    while start_date < today:
        fix_daily_spot_fee(start_date)
        start_date += timedelta(days=1)

    while start_month < today:
        fix_monthly_spot_market_fee(start_month)
        fix_monthly_spot_asset_fee(start_month)
        start_month = next_month(start_month.year, start_month.month)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()

