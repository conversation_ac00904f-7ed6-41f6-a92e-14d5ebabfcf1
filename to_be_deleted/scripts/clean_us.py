
import time
import os
import sys
import json

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


ip_libs = None

def saferpc(fn, *args, **kwargs):
    for _ in range(20):
        try:
            return fn(*args, **kwargs)
        except:
            print(f'warning: {fn.__name__} failed, retry...')
            time.sleep(5)
    else:
        raise RuntimeError(f'{fn.__name__} failed')


def get_data():
    from app.models import User, KycVerification

    last = 0
    users = set()
    while True:
        rows = User.query.filter(User.id > last) \
            .filter(User.user_type != User.UserType.SUB_ACCOUNT) \
            .order_by(User.id).with_entities(User.id, User.location_code) \
            .limit(10000).all()
        for row in rows:
            if row.location_code == 'US' or row.location_code == 'USA':
                users.add(row.id)
                continue

        if len(rows) < 10000:
            break
        last = rows[-1].id

    kycs = set()
    last = 0
    while True:
        rows = KycVerification.query.filter(KycVerification.id > last) \
            .order_by(KycVerification.id).with_entities(KycVerification.id, KycVerification.user_id, KycVerification.country, KycVerification.status) \
            .limit(10000).all()
        for row in rows:
            if row.status != KycVerification.Status.PASSED:
                continue
            if row.country == 'US' or row.country == 'USA':
                kycs.add(row.user_id)

        if len(rows) < 10000:
            break
        last = rows[-1].id

    return users, kycs


def prepare_ip_libs():
    from app.models import User
    from app.utils import GeoIP
    from app.common import get_country

    blacklist = ('中国', '香港', '台湾', '美国', 'china', 'hong kong', 'taiwan', 'america', 'united states')
    last = 1 << 32
    result = []
    for _ in range(10):
        r = User.query.filter(User.id < last) \
                .order_by(User.id.desc()) \
                .with_entities(User.registration_ip, User.registration_location) \
                .limit(10000).all()
        if not r:
            break
        last = r[-1][0]

        for ip, location in r:
            if not location:
                continue
            location = location.lower()
            if any(x in location for x in blacklist):
                continue
            country = get_country(GeoIP(ip).country_code)
            if not country:
                continue
            code = country.iso_3
            if code in ('CHN', 'HKG', 'TWN', 'USA'):
                continue
            result.append((ip, location, code))
    return result


def pick_ip():
    import random

    i = random.randint(0, len(ip_libs) - 1)
    return ip_libs[i]


def clear_login_history(user_id):
    from app.models import LoginHistory, db

    LoginHistory.query.filter(LoginHistory.user_id == user_id).delete()
    db.session.commit()


def clear_reg_ip(user_id):
    from app.models import User, db

    ip, loc, code = pick_ip()

    user = User.query.get(user_id)
    user.registration_ip = ip
    user.registration_location = loc
    user.location_code = code
    db.session.commit()


def get_user_balance(user_id):
    from app.business import ServerClient, PerpetualServerClient
    from app.business import PriceManager
    from app.business.amm import get_user_amm_assets

    prices = PriceManager.assets_to_usd()

    r = saferpc(ServerClient().get_user_accounts_balances, user_id)
    spot = 0
    for _, vs in r.items():
        for asset, v in vs.items():
            total = sum(v.values(), 0)
            spot += prices.get(asset, 0) * total

    r = saferpc(PerpetualServerClient().get_user_balances, user_id)
    perpetual = 0
    for asset, v in r.items():
        total = sum(v.values(), 0)
        perpetual += prices.get(asset, 0) * total

    amm = 0
    r = get_user_amm_assets(user_id)
    for asset, v in r.items():
        amm += prices.get(asset, 0) * v

    return spot + perpetual + amm


def get_user_balance_with_sub(user_id):
    from app.models import SubAccount

    rows = SubAccount.query.filter(SubAccount.main_user_id == user_id).all()
    user_ids = [user_id] + [x.user_id for x in rows]
    return sum(get_user_balance(x) for x in user_ids)


def clear_mobile(user_id):
    from app.models import User, db
    from app.business import UserSettings
    from app.utils import amount_to_str

    user = User.query.get(user_id)
    if user.mobile_country_code != 1:
        return
    # 只有手机号且资产大于1000U的用户关闭提现
    balance = get_user_balance_with_sub(user_id)
    if balance > 1000 and not user.totp_auth_key:
        UserSettings(user_id).withdrawals_disabled_by_admin = True
        print('forbidden', user_id, user.email or '', amount_to_str(balance), sep=',')

    user.mobile_country_code = None
    user.mobile_num = None
    db.session.commit()


def set_clear(user_id):
    from app.models import ClearedUser, db
    # 把未 清退/仅提现 的用户处理成 清退
    row = ClearedUser.query.filter(ClearedUser.user_id == user_id).first()
    if not row:
        row = ClearedUser(
            user_id=user_id,
            status=ClearedUser.Status.FORBIDDEN,
            valid=True,
            remark='us clear'
        )
        db.session.add(row)
        db.session.commit()
    else:
        if not row.valid:
            row.status = ClearedUser.Status.FORBIDDEN
            row.valid = True
            row.remark = 'us clear'
            db.session.commit()

def check_clear(user_id):
    from app.models import ClearedUser
    row = ClearedUser.query.filter(ClearedUser.user_id == user_id).first()
    return row and row.valid


def export():
    users, kycs = get_data()
    with open('us_user.json', 'w') as f:
        json.dump(list(users), f)
    with open('us_kyc.json', 'w') as f:
        json.dump(list(kycs), f)


def process_normal():
    from app.business.user import update_user_location
    # 处理美国地区非美国kyc的用户信息
    with open('us_user.json', 'r') as f:
        users = json.load(f)
    with open('us_kyc.json', 'r') as f:
        kycs = json.load(f)

    users = set(users) - set(kycs)

    global ip_libs
    ip_libs = prepare_ip_libs()
    count = 0
    total = len(users)
    for user_id in users:
        count += 1
        if count % 100 == 0:
            print(count, '/', total, sep='')
        clear_login_history(user_id)
        clear_reg_ip(user_id)
        clear_mobile(user_id)
        update_user_location(user_id)

def process_us():
    # 清退美国kyc的正常用户
    with open('us_kyc.json', 'r') as f:
        kycs = json.load(f)

    for user_id in kycs:
        if not check_clear(user_id):
            print(user_id)


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        process_us()
