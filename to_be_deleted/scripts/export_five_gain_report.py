from datetime import date, timedelta
from typing import Dict

from init_base import app

from app.business.export.base import UserExport
from app.models import User
from app.utils import group_by, datetime_to_str, timestamp_to_datetime, ExcelExporter, upload_file, timestamp_to_date
from app.models.activity import FifthAnniversaryChallenge


def get_user_time_dict() -> Dict[int, date]:
    model = FifthAnniversaryChallenge
    query = model.query.filter().with_entities(
        model.user_id,
        model.created_at
    ).all()
    ret = {i.user_id: i.created_at for i in query}
    return ret


def main():
    export_headers = {
        "date": "领取日期",
        "user_id": "用户id",
        "created_at": "注册时间",
        "deal_usd": "领取后7天内交易额"
     }
    user_time_dict = get_user_time_dict()
    query = User.query.filter(User.id.in_(list(user_time_dict.keys()))).with_entities(
        User.id,
        User.created_at
    )
    user_map = {i.id: i.created_at for i in query}
    export_data = []
    user_time_list = list(user_time_dict.items())
    user_time_list.sort(key=lambda x: x[1])
    for group, data_list in group_by(lambda x: x[1], user_time_list).items():
        for data in data_list:
            user_id, _date = data
            st = user_time_dict[user_id]
            et = st + timedelta(days=7)
            deal_map = UserExport.get_user_trade_amount(st, et, [user_id])
            tmp_dict = {
                "user_id": user_id,
                "date": _date.strftime("%Y-%m-%d"),
                "created_at": datetime_to_str(user_map[user_id]),
                "deal_usd": deal_map.get(user_id, 0)
            }
            export_data.append(tmp_dict)

    export = ExcelExporter(
        data_list=export_data,
        fields=list(export_headers.keys()),
        headers=list(export_headers.values())
    )
    streams = export.export_streams()
    file_url = upload_file(streams, "xlsx")
    print(file_url)


if __name__ == "__main__":

    with app.app_context():
        main()