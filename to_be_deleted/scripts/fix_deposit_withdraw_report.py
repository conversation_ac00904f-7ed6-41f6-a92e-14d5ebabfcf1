# -*- coding: utf-8 -*-
import datetime
import os
import sys
from collections import defaultdict
from decimal import Decimal

import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import DailyDepositWithdrawalReport, db
    from app.schedules.reports.deposit_withdrawal import update_daily_deposit_withdrawal_report
    
    start = datetime.date(2020, 8, 26)
    end = datetime.date(2020, 9, 23)
    delta = datetime.timedelta(days=1)

    while start <= end:
        DailyDepositWithdrawalReport.query.filter(
            DailyDepositWithdrawalReport.report_date == start
        ).update({
            DailyDepositWithdrawalReport.report_date: start - delta
        }, synchronize_session=False)
        db.session.commit()
        start += delta

    update_daily_deposit_withdrawal_report(end)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
