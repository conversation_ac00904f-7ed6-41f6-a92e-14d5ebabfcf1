# -*- coding: utf-8 -*-

import os
import sys
import json
import time
from collections import defaultdict
from decimal import Decimal

import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def deduct_balance():
    from app.models import BalanceUpdateBusiness, db
    from app.models.operation import PoolBalanceFix
    from app.business import ServerClient
    from app.common import BalanceBusiness
    from app.utils import amount_to_str

    c = ServerClient()
    rows = PoolBalanceFix.query.all()
    for row in rows:
        user_id = row.user_id
        asset = row.asset
        remain_amount = row.remain_amount
        if remain_amount <= 0:
            continue
        r = c.get_user_balances(user_id, asset)
        if r[asset]['frozen'] > 0:  # should not happen
            c.cancel_user_all_order(user_id, -1, None)
            time.sleep(1)
        r = c.get_user_balances(user_id, asset)
        available = r[asset]['available']
        if available <= 0:
            continue
        amount = min(row.remain_amount, available)

        print(f'deduct {user_id} {amount} {asset}')
        balance_update_business_id = BalanceUpdateBusiness.new_id(
            user_id,
            asset,
            -amount
        )
        c.add_user_balance(
            user_id=user_id,
            asset=asset,
            business=BalanceBusiness.SYSTEM,
            business_id=balance_update_business_id,
            amount=amount_to_str(-amount),
            detail={'remark': 'revert pool deposit'},
            account_id=0,
        )
        row.deducted_amount += amount
        db.session.commit()


def place_order(assets):
    from app.models.operation import PoolBalanceFix
    from app.business import ServerClient
    from app.utils import group_by

    sys.path.append('deployment/scripts')
    from fix_pool_balance2 import buyback_user_asset

    records = PoolBalanceFix.query.all()

    user_loan_assets = {}
    for user_id, rows in group_by(lambda x: x.user_id, records).items():
        user_loan_assets[user_id] = {x.asset for x in rows}

    c = ServerClient()
    for row in records:
        user_id = row.user_id
        asset = row.asset
        remain_amount = row.remain_amount
        if remain_amount <= 0:
            continue
        if asset not in assets:
            continue

        available = c.get_user_balances(user_id, asset)[asset]['available']
        want_amount = remain_amount - available
        if want_amount <= 0:
            continue

        buyback_user_asset(user_id, asset, want_amount, user_loan_assets[user_id])


def build_pool_balance():
    from app.models import ViaBTCPoolOrder, PoolBalanceFix, db
    from app.utils import group_by

    whitelist = [683374, 706295, 531643]    # 套保、质押、兑换账号
    tagret_user_ids = {2681859, 3338756, 3338759, 1604105, 2301963, 1904652, 1227790, 1495054, 1070608, 2877459, 1228820, 1233949, 3338787, 3153956, 3327525, 1276967, 2314279, 3338798, 433198, 2467887, 3338802, 3338805, 3316278, 3046455, 2436150, 1277495, 734778, 1921083, 2455613, 740418, 1083971, 3321924, 3258951, 1379912, 2582602, 2329677, 1129550, 2612303, 2685522, 1407572, 1677908, 1719894, 649306, 2212442, 2085470, 1112158, 1833570, 2616420, 3164777, 1272941, 1384049, 546421, 3246198, 739446, 1089660, 2503807, 1071235, 3337859, 2707077, 3100806, 555655, 1907338, 2405003, 26768, 1765009, 2350226, 1976978, 1098389, 2211478, 1504921, 1959578, 1333403, 1202848, 542880, 2557602, 2450595, 742564, 2474659, 1106601, 3201198, 702128, 1840305, 728242, 3075760, 2358964, 3271861, 1928888, 1710265, 1410234, 1995451, 3093696, 1186500, 3338437, 2314441, 3038409, 3202251, 3040460, 1813196, 1131724, 3240656, 3014352, 2221780, 3251925, 2473686, 1135830, 3314391, 1901273, 3053270, 1208535, 1244380, 719580, 3117791, 2374367, 2495201, 1721058, 2225891, 725223, 2307304, 1488617, 1286889, 2515690, 1112812, 1741031, 3150062, 2286831, 3338480, 2449646, 541423, 1151220, 498933, 1794805, 1489142, 1251572, 1327353, 2289914, 1446651, 3070708, 1333492, 3144958, 1353471, 1114367, 1371394, 1110279, 1483529, 1309968, 1159957, 1482005, 1769750, 1174296, 3338522, 2547483, 1210650, 1417501, 3279138, 2609955, 3115817, 552234, 1095978, 2988844, 1166635, 2790188, 1267503, 3249384, 745772, 3216179, 2065205, 3062582, 2551610, 2722618, 1574716, 2997565, 3338046, 1265474, 3338563, 3338565, 3272006, 1895239, 3256648, 3289929, 710474, 696651, 2685772, 1156944, 2376530, 2434390, 2996568, 2479962, 1901404, 635741, 1722717, 2493790, 1302368, 1834332, 3265381, 742245, 5992, 2197353, 1545578, 681836, 2618220, 1993071, 3234673, 2454901, 1202550, 2194294, 2418550, 3338107, 1778046, 1308032, 3338625, 1733507, 3338627, 1865092, 1648517, 3333000, 3338633, 3338632, 1079694, 3338641, 2340244, 1088406, 1251735, 2156443, 3028896, 1581987, 1223075, 3338661, 2257830, 1328555, 3228588, 3338156, 1153451, 2513324, 1607086, 1967539, 1664952, 1240504, 1182651, 2728892, 2519484, 1989567, 1290177, 2199490, 3338693, 747462, 1509318, 2091977, 3338701, 2883021, 1120207, 1215949, 1083345, 3338709, 2934229, 2169816, 1080281, 2573784, 3338203, 1201116, 1272795, 2526171, 274399, 3018720, 3338721, 1545697, 1881056, 1968613, 3191782, 3338727, 1769451, 2613229, 1424877, 1230831, 3258353, 1565682, 3075570, 2584564, 3338744, 3338236, 1614333, 2655230}

    records = ViaBTCPoolOrder.query.filter(
        ViaBTCPoolOrder.created_at >= '2022-01-25 15:00:00',
        ViaBTCPoolOrder.created_at <= '2022-01-26 12:00:00'
    ).all()

    for user_id, rows in group_by(lambda x: x.user_id, records).items():
        if user_id in whitelist:
            continue
        if user_id not in tagret_user_ids:
            continue
        assets = defaultdict(lambda : dict(amount=Decimal(), pool_user_ids=set()))
        for row in rows:
            assert row.status == ViaBTCPoolOrder.Status.FINISHED
            assets[row.asset]['amount'] += row.amount
            pool_user_id = json.loads(row.remark)['external_user_id']
            assets[row.asset]['pool_user_ids'].add(pool_user_id)
        for asset, data in assets.items():
            db.session.add(PoolBalanceFix(
                user_id=user_id,
                asset=asset,
                amount=data['amount'],
                pool_user_ids=json.dumps(list(data['pool_user_ids']))
            ))
    db.session.commit()


def add_cold_wallet_history():
    from sqlalchemy import func
    from app.assets import asset_to_default_chain
    from app.models import ColdWalletHistory, PoolBalanceFix

    rows = PoolBalanceFix.query.group_by(PoolBalanceFix.asset).with_entities(
        PoolBalanceFix.asset, func.sum(PoolBalanceFix.deducted_amount)
    ).all()
    for asset, amount in rows:
        if amount <= 0:
            continue
        chain = 'ERC20' if asset in ('USDT', 'ETH') else asset_to_default_chain(asset),
        # 这里流水类型只能是POOL，因为钱包分离后，只读取web侧的矿池钱包流水。
        ColdWalletHistory.add_history(asset, chain, -amount, 'fix pool deposit', ColdWalletHistory.Type.POOL)
        # todo: 执行后检查冷钱包流水余额为0


def export():
    from app.models import PoolBalanceFix
    from app.utils import amount_to_str

    records = PoolBalanceFix.query.all()
    with open('pool_balance_fix.csv', 'w') as fp:
        fp.write('CoinEx用户ID,矿池用户ID,币种,需扣减,实际扣减\n')
        for row in records:
            pool_user_ids = ';'.join(str(x) for x in json.loads(row.pool_user_ids))
            line = "%s,%s,%s,%s,%s\n" % (row.user_id, pool_user_ids, row.asset, amount_to_str(row.amount), amount_to_str(row.deducted_amount))
            fp.write(line)


@click.command()
@click.argument('command', required=True)
@click.argument("assets", required=False)
def main(command, assets=''):
    if command == 'build':
        build_pool_balance()
    elif command == 'deduct':
        deduct_balance()
    elif command == 'order':
        place_order(assets.split(','))
    elif command == 'cold':
        add_cold_wallet_history()
    elif command == 'export':
        export()
    else:
        print('invalid command')


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
