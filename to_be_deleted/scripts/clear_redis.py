# -*- coding: utf-8 -*-

from pprint import pprint
import os
import sys
import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def clear(redis, pattern, delete: bool):
    keys = list(redis.scan_iter(pattern, 100))
    if len(keys) > 0:
        print(f"clear '{pattern}' keys {len(keys)}")
        print('================keyinfo==============')
        pprint(sorted(map(lambda x: x.decode(), keys)))
    else:
        print('not found keys')
    if delete:
        for i in range(0, len(keys), 1000):
            redis.delete(*keys[i:i+1000])
        print(f"finish clear redis keys with redis pattern '{pattern}'")


@click.command()
@click.argument('key_pattern')
@click.option('--i-know-what-i-am-doing', is_flag=True)
def main(key_pattern: str, i_know_what_i_am_doing: bool):
    from app.caches.base import _BaseCache
    redis = _BaseCache.redis
    clear(redis, key_pattern, i_know_what_i_am_doing)


if __name__ == '__main__':
    from app import create_app
    with create_app().app_context():
        main()
