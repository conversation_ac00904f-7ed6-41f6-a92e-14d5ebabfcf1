import datetime
import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import db

    from app.business.export.base import add_print

    @add_print
    def brush():
        from app.models import AssetAccumulatedDepositConfig

        if AssetAccumulatedDepositConfig.query.first():
            return

        configs = [
            dict(
                id=1,
                rank_min=1,
                rank_max=5,
                deposit_threshold=3000,
                user_deposit_threshold=1000,
                notice_count_threshold=2,
                notice_amount_threshold=2,
                limit_count_threshold=2,
                limit_amount_threshold=2,
            ),
            dict(
                id=2,
                rank_min=6,
                rank_max=20,
                deposit_threshold=2000,
                user_deposit_threshold=1000,
                notice_count_threshold=3,
                notice_amount_threshold=3,
                limit_count_threshold=3,
                limit_amount_threshold=3,
            ),
            dict(
                id=3,
                rank_min=21,
                rank_max=50000000,
                deposit_threshold=1000,
                user_deposit_threshold=1000,
                notice_count_threshold=5,
                notice_amount_threshold=5,
                limit_count_threshold=5,
                limit_amount_threshold=5,
            ),
        ]
        for config in configs:
            db.session.add(AssetAccumulatedDepositConfig(**config))
        db.session.commit()

    brush()


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
