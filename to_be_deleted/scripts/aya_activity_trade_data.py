# -*- coding: utf-8 -*-
import os
import sys
from functools import lru_cache
from typing import NamedTuple

from sqlalchemy import or_, and_, func


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from decimal import Decimal
from collections import defaultdict
from datetime import datetime
from app.models import User, Withdrawal
from app.utils.date_ import timestamp_to_datetime
from app.utils import ExcelExporter, amount_to_str, upload_file

# 2020-10-20 08:00:00 UTC +8
start_time = **********
# 2020-10-27 08:00:00 UTC +8
end_time = **********


markets = ['AYAUSDT', 'AYABTC']

UserInfo = NamedTuple('UserInfo',
                      [('email', str),
                       ('is_sub_account', bool),
                       ('created_at', datetime),
                       ])

check_asset = 'AYA'


@lru_cache()
def get_user_trade_infos():
    from app.business import TradeSummaryDB
    result = []
    for market in markets:
        result.extend(TradeSummaryDB.get_trade_summary(start_time, end_time, market, check_asset))
    return result


@lru_cache()
def get_all_user_ids():
    user_trade_details = get_user_trade_infos()
    user_ids = set()
    for record in user_trade_details:
        user_ids.add(int(record['user_id']))
    return user_ids


@lru_cache()
def get_user_trade_amount_data():
    trade_amount_dict = defaultdict(Decimal)
    user_trade_details = get_user_trade_infos()
    for record in user_trade_details:
        trade_amount_dict[int(record['user_id'])] += Decimal(record['deal_amount'])
    return trade_amount_dict


@lru_cache()
def get_user_net_amount_data():
    net_amount_dict = defaultdict(Decimal)
    user_trade_details = get_user_trade_infos()
    for record in user_trade_details:
        net_amount_dict[int(record['user_id'])] += Decimal(record['buy_amount'])
        net_amount_dict[int(record['user_id'])] -= Decimal(record['sell_amount'])

    user_ids = net_amount_dict.keys()
    withdrawal_data = Withdrawal.query.filter(
        Withdrawal.user_id.in_(user_ids),
        Withdrawal.asset == check_asset,
        Withdrawal.sent_at >= timestamp_to_datetime(start_time),
        Withdrawal.sent_at < timestamp_to_datetime(end_time),
        or_(
            and_(
                Withdrawal.type == Withdrawal.Type.ON_CHAIN,
                Withdrawal.status.in_([
                    Withdrawal.Status.PROCESSING,
                    Withdrawal.Status.CONFIRMING,
                    Withdrawal.Status.FINISHED])
            ),
            and_(
                Withdrawal.type == Withdrawal.Type.LOCAL,
                Withdrawal.status == Withdrawal.Status.FINISHED
            )
        )
    ).group_by(
        Withdrawal.user_id
    ).with_entities(
        Withdrawal.user_id,
        func.sum(Withdrawal.amount).label('amount')
    ).all()
    for user_id, amount in withdrawal_data:
        net_amount_dict[user_id] -= amount
    return net_amount_dict


def get_user_infos(user_ids):
    user_query = User.query.filter(
        User.id.in_(user_ids),
    ).with_entities(
        User.id,
        User.email,
        User.created_at,
        User.user_type
    )
    return {u.id: UserInfo(
                is_sub_account=u.user_type == User.UserType.SUB_ACCOUNT,
                email=u.email,
                created_at=u.created_at)
            for u in user_query}


def get_user_id_asset_data(user_ids, asset, report_date):
    from app.business import ExchangeLogDB
    asset_dict = defaultdict(Decimal)
    for user_id in user_ids:
        idx = ExchangeLogDB.user_slice_balance_hash(user_id)
        table = ExchangeLogDB.user_slice_balance_table(idx)
        result = table.select(
            "balance",
            where=f"user_id={user_id} and asset='{asset}' and " \
                  f"account=0 and report_date='{report_date}' limit 1")
        if result:
            asset_dict[user_id] = Decimal(result[0][0])
    return asset_dict


def get_user_id_total_usd_data(user_ids, report_date):
    from app.business import ExchangeLogDB, PriceManager
    asset_dict = defaultdict(Decimal)
    for user_id in user_ids:
        idx = ExchangeLogDB.user_slice_balance_hash(user_id)
        table = ExchangeLogDB.user_slice_balance_table(idx)
        result = table.select(
            "asset", "balance",
            where=f"user_id={user_id} and "\
                  f"account=0 and report_date='{report_date}'")
        if result:
            for r in result:
                asset_dict[user_id] += Decimal(r[1]) * PriceManager.asset_to_usd(r[0])
    return asset_dict


def check_is_first_trade(user_ids):
    from app.business import ServerClient, BalanceBusiness
    check_result = defaultdict(bool)
    c = ServerClient()
    for user_id in user_ids:
        r = c.get_user_balance_history(user_id=user_id, asset=check_asset,
                                       account_id=0,
                                       business=BalanceBusiness.TRADING,
                                       start_time=start_time,
                                       end_time=end_time,
                                       page=1,
                                       limit=10000
                                       )
        parse_r = r.as_dict()
        if len(parse_r['data']) > 0:
            aya_first_time = int(parse_r['data'][-1]['time'])
            if aya_first_time < start_time:
                continue
            t = c.get_user_balance_history(user_id=user_id, asset='',
                                           account_id=0,
                                           business=BalanceBusiness.TRADING,
                                           start_time=start_time,
                                           end_time=aya_first_time,
                                           page=1,
                                           limit=1
                                           )
            if not t:
                check_result[user_id] = True
    return check_result


def main():
    header_data = [
        ('user_id', '用户id'),
        ('email', '邮箱'),
        ('created_at', '注册时间'),
        ('is_sub_account', '是否子账户'),
        ('trade_amount', 'AYA交易量'),
        ('net_amount', 'AYA净买入量'),
        ('first_is_AYA', '第一笔交易是否为AYA交易'),
        ('total_usd', '活动结束账户资产总额'),
        ('end_first_day', '活动结束后第1天账户持有AYA余额'),
        ('end_third_day', '活动结束后第3天账户持有AYA余额'),
        ('end_seven_day', '活动结束后第7天账户持有AYA余额'),
    ]
    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]
    result_list = []
    all_user_ids = get_all_user_ids()
    f = lambda x: amount_to_str(x, 8) if isinstance(x, Decimal) else str(x)
    # 2020-10-28 08:00:00
    first_day = **********
    # 2020-10-30 08:00:00
    third_day = **********
    # 2020-11-02 08:00:00
    seven_day = **********
    # 2020-11-03 08:00:00
    current_day = **********

    first_day_data = get_user_id_asset_data(all_user_ids, check_asset,
                                            timestamp_to_datetime(first_day).date())
    third_day_data = get_user_id_asset_data(all_user_ids, check_asset,
                                            timestamp_to_datetime(third_day).date())
    seven_day_data = get_user_id_asset_data(all_user_ids, check_asset,
                                            timestamp_to_datetime(seven_day).date())
    total_usd_data = get_user_id_total_usd_data(all_user_ids, timestamp_to_datetime(current_day).date())
    first_check_data = check_is_first_trade(all_user_ids)
    for user_id in all_user_ids:
        result_list.append(
            dict(
                user_id=f(user_id),
                email=f(get_user_infos(all_user_ids)[user_id].email),
                created_at=f(get_user_infos(all_user_ids)[user_id].created_at),
                is_sub_account=f(get_user_infos(all_user_ids)[user_id].is_sub_account),
                trade_amount=f(get_user_trade_amount_data()[user_id]),
                net_amount=f(get_user_net_amount_data()[user_id]),
                first_is_AYA='是' if first_check_data[user_id] else '否',
                total_usd=f(total_usd_data[user_id]),
                end_first_day=f(first_day_data[user_id]),
                end_third_day=f(third_day_data[user_id]),
                end_seven_day=f(seven_day_data[user_id])
            )
        )
    streams = ExcelExporter(
        data_list=result_list, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
