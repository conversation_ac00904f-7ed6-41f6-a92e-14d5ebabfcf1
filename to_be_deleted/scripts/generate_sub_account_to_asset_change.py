# -*- coding: utf-8 -*-
import os
import re
import sys

import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


from app.models import (
    db,
    User,
    SubAccount,
)  # noqa: E402
from app.models.exchange import AssetExchangeSysUser  # noqa: E402


def generate_sub_account(main_user_id: int, nums: int):
    subs = SubAccount.query.filter(SubAccount.main_user_id == main_user_id).all()
    subs = User.query.filter(User.id.in_([i.user_id for i in subs]))
    pattern = re.compile(r"swap_(\d+)")
    sub_idx_list = [int(pattern.fullmatch(i.name).groups()[0]) for i in subs if pattern.fullmatch(i.name)]
    if sub_idx_list:
        start_idx = max(sub_idx_list) + 1
    else:
        start_idx = 1

    parent = User.query.get(main_user_id)
    parent_pref = UserPreferences(main_user_id)
    for i in range(nums):
        idx = i + start_idx
        sub_user = User(
            login_password_hash=parent.login_password_hash,
            login_password_level=parent.login_password_level,
            login_password_updated_at=parent.login_password_updated_at,
            registration_ip=parent.registration_ip,
            registration_location=parent.registration_location,
            name=f"swap_{idx}",
            user_type=User.UserType.SUB_ACCOUNT,
        )
        db.session.add(sub_user)
        db.session.flush()
        sub_account = SubAccount(
            user_id=sub_user.id,
            main_user_id=main_user_id,
            remark="asset swap system user",
        )
        db.session.add(sub_account)
        exc_sys_user = AssetExchangeSysUser(user_id=sub_user.id)
        db.session.add(exc_sys_user)
        db.session.commit()

        sub_pref = UserPreferences(sub_user.id)
        sub_pref.language = parent_pref.language
        sub_pref.timezone_offset = parent_pref.timezone_offset
        print(f"生成 {sub_user.name}")


@click.command()
@click.option("--main_user_id", type=click.INT)
@click.option("--nums", type=click.INT)
def main(main_user_id, nums):
    generate_sub_account(main_user_id, nums)


if __name__ == "__main__":
    from app import create_app

    app = create_app()
    with app.app_context():
        from app.business import UserPreferences

        main()
