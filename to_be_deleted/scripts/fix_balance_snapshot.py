# -*- coding: utf-8 -*-

from decimal import Decimal
from datetime import date, timedelta
import os
import sys
from itertools import chain
import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


start = date(2020, 8, 26)
end = date(2020, 9, 14)


def update_slice_log(day: date):
    from app.business import ExchangeLogDB

    table = ExchangeLogDB.user_slice_balance_log_table()
    where = f"`report_date`='{day}' and `table`='user_slice_balance'"
    log = table.select(where=where)
    if log:
        table.update(where, status='finish')
    else:
        table.insert(day, 'user_slice_balance', 'finish', '')
        table.flush()

def is_slice_synchronized(day):
    from app.business import ExchangeLogDB

    log = ExchangeLogDB.user_slice_balance_log_table().select(
        where=f"`report_date`='{day}' "
        "and `table`='user_slice_balance' "
        "and `status`='finish'"
    )
    return True if log else False


def sync_slice_balance_from_snapshot(day: date):
    from app.business import ExchangeLogDB
    from app.utils.date_ import date_to_datetime

    if is_slice_synchronized(day):
        print(f'{day} slice balance has been synchronized')
        return

    timestamp = int(date_to_datetime(day).timestamp())
    table = ExchangeLogDB.user_balance_table(timestamp)
    if not table.exists() or not table.select(limit=1):
        print(f'{table.name} not exists')
        return
    tables = [ExchangeLogDB.user_slice_balance_table(i)
              for i in range(ExchangeLogDB.USER_SLICE_BALANCE_TABLE_COUNT)]
    exists = {}
    print(f'select {table.name} ...')
    records = table.select('user_id', 'account', 'asset', \
                           'balance', 'locked_balance', 'balance_usd', \
                           'margin_due_amount', 'margin_due_amount_usd')
    count = 0
    for row in records:
        if count % 10000 == 0:
            print(f'{count}/{len(records)}')
        count += 1
        user_id = row[0]
        index = ExchangeLogDB.user_slice_balance_hash(user_id)
        table = tables[index]
        if (ex := exists.get(user_id)) is None:
            exists[user_id] = ex = bool(table.select(where=f"report_date='{day}' and user_id={user_id}", limit=1))
        if ex:
            continue

        table.insert(day, *row)

    [t.flush() for t in tables]
    update_slice_log(day)
    print(f"sync {day} slice balance success")


def sync_slice_balance_from_interpolation(day: date):
    from app.business import ExchangeLogDB

    if is_slice_synchronized(day):
        print(f'{day} slice balance has been synchronized')
        return
    before_day = day - timedelta(days=1)
    after_day = day + timedelta(days=1)
    if not is_slice_synchronized(before_day) or not is_slice_synchronized(after_day):
        print(f'{day} befor or after slice balance not exists')
        return 

    tables = [ExchangeLogDB.user_slice_balance_table(i)
              for i in range(ExchangeLogDB.USER_SLICE_BALANCE_TABLE_COUNT)]

    for table in tables:
        fileds = ['user_id', 'account', 'asset',
                  'balance', 'lock_balance', 'market_value', 
                  'margin_unflat_amount', 'margin_unflat_amount_usd']
        before = table.select(*fileds, where=f"report_date='{before_day}'")
        after = table.select(*fileds, where=f"report_date='{after_day}'")

        before = {x[:3]: x[3:] for x in before}
        after = {x[:3]: x[3:] for x in after}

        exists = {}

        for key in before.keys() & after.keys():
            user_id = key[0]
            if (ex := exists.get(user_id)) is None:
                exists[user_id] = ex = bool(table.select(where=f"report_date='{day}' and user_id={user_id}", limit=1))
            if ex:
                continue
            value = [(x + y) / 2 for x, y in zip(before[key], after[key])]
            table.insert(day, *key, *value)

        for key, value in chain(
            [x for x in before.items() if x[0] not in after],
            [x for x in after.items() if x[0] not in before]
        ):
            user_id = key[0]
            if (ex := exists.get(user_id)) is None:
                exists[user_id] = ex = bool(table.select(where=f"report_date='{day}' and user_id={user_id}", limit=1))
            if ex:
                continue
            value = [x / 2 for x in value]
            table.insert(day, *key, *value)

    [t.flush() for t in tables]
    update_slice_log(day)
    print(f"sync {day} slice balance success")


def sync_slice_balance_from_interpolation2(day1: date, day2: date):
    from app.business import ExchangeLogDB

    if is_slice_synchronized(day1) or is_slice_synchronized(day2):
        print(f'{day1} or {day2} slice balance has been synchronized')
        return
    before_day = day1 - timedelta(days=1)
    after_day = day2 + timedelta(days=1)
    if not is_slice_synchronized(before_day) or not is_slice_synchronized(after_day):
        print(f'{day1} or {day2} befor or after slice balance not exists')
        return 

    def sync(day, n):
        tables = [ExchangeLogDB.user_slice_balance_table(i)
                for i in range(ExchangeLogDB.USER_SLICE_BALANCE_TABLE_COUNT)]

        for table in tables:
            fileds = ['user_id', 'account', 'asset',
                    'balance', 'lock_balance', 'market_value', 
                    'margin_unflat_amount', 'margin_unflat_amount_usd']
            before = table.select(*fileds, where=f"report_date='{before_day}'")
            after = table.select(*fileds, where=f"report_date='{after_day}'")

            before = {x[:3]: x[3:] for x in before}
            after = {x[:3]: x[3:] for x in after}

            exists = {}

            for key in before.keys() & after.keys():
                user_id = key[0]
                if (ex := exists.get(user_id)) is None:
                    exists[user_id] = ex = bool(table.select(where=f"report_date='{day}' and user_id={user_id}", limit=1))
                if ex:
                    continue
                value = [(y - x) * n / 3 + x for x, y in zip(before[key], after[key])]
                table.insert(day, *key, *value)

            for key, value in [x for x in before.items() if x[0] not in after]:
                user_id = key[0]
                if (ex := exists.get(user_id)) is None:
                    exists[user_id] = ex = bool(table.select(where=f"report_date='{day}' and user_id={user_id}", limit=1))
                if ex:
                    continue
                value = [- x * n / 3 + x for x in value]
                table.insert(day, *key, *value)

            for key, value in [x for x in after.items() if x[0] not in before]:
                user_id = key[0]
                if (ex := exists.get(user_id)) is None:
                    exists[user_id] = ex = bool(table.select(where=f"report_date='{day}' and user_id={user_id}", limit=1))
                if ex:
                    continue
                value = [x * n / 3 for x in value]
                table.insert(day, *key, *value)

        [t.flush() for t in tables]
        update_slice_log(day)
        print(f"sync {day} slice balance success")

    sync(day1, 1)
    sync(day2, 2)

@click.command()
@click.argument("op", type=click.Choice(['sync', 'fix', 'fix2']), required=True)
@click.argument("day", required=False)
def main(op, day=None):
    from app.utils import str_to_datetime

    if op == 'sync':
        day = start
        while day <= end:
            sync_slice_balance_from_snapshot(day)
            day += timedelta(days=1)
    elif op == 'fix' and day:
        day = str_to_datetime(day).date()
        sync_slice_balance_from_interpolation(day)
    elif op == 'fix2':
        day1 = str_to_datetime(day).date()
        day2 = day1 + timedelta(days=1)
        sync_slice_balance_from_interpolation2(day1, day2)


if __name__ == '__main__':
    from app import create_app
    with create_app().app_context():
        main()
