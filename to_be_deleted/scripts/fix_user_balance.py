# -*- coding: utf-8 -*-
import datetime
import os
import sys
from collections import defaultdict
from decimal import Decimal

import click

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def update_balance(user_id, asset, amount):
    from app.business import ServerClient
    from app.common import BalanceBusiness
    from app.models import BalanceUpdateBusiness

    c = ServerClient()
    c.add_user_balance(
        user_id=user_id,
        asset=asset,
        amount=amount,
        business=BalanceBusiness.SYSTEM,
        business_id=BalanceUpdateBusiness.new_id(user_id, asset, amount),
        detail={'remark': 'fix margin liquidation'}
    )


def main():
    from app.config import config

    user_id = 636237
    asset = 'BTT'
    amount = '-600000'

    update_balance(user_id, asset, amount)
    update_balance(config['OFFICIAL_ADMIN_USER_ID'], asset, amount[1:])


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
