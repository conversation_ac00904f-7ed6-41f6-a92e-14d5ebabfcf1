import json
import os
import sys
from functools import wraps

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def add_print(func):
    @wraps(func)
    def dec(*args, **kwargs):
        print(f'{func.__name__}>>>')
        ret = func(*args, **kwargs)
        print(f'{func.__name__}<<<')
        return ret

    return dec


def main():
    from app.models import db

    @add_print
    def brush_trigger_pages():
        from app.models import PopupWindow

        model = PopupWindow
        rows = model.query.all()
        for row in rows:
            trigger_pages = [
                dict(
                    trigger_page=model.TriggerPage.HOME.name,
                    param_type='',
                    trigger_page_params='',
                )
            ]
            row.trigger_pages = json.dumps(trigger_pages)
        db.session.commit()

    brush_trigger_pages()


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
