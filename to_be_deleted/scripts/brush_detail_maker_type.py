import os
import sys

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import db
    from app.utils import batch_iter

    from app.models import DailyMakerTradeDetailReport, User

    model = DailyMakerTradeDetailReport
    objs = model.query.all()
    user_ids = {obj.user_id for obj in objs}

    user_model = User
    user_type_mapping = {}
    for chunk_ids in batch_iter(user_ids, 1000):
        users = user_model.query.with_entities(user_model.id, user_model.user_type).filter(
            user_model.id.in_(chunk_ids)
        ).all()
        user_type_mapping.update(dict(users))

    for obj in objs:
        maker_type = model.MakerType.OUTER
        user_type = user_type_mapping.get(obj.user_id)
        if user_type and user_type is user_model.UserType.INTERNAL_MAKER:
            maker_type = model.MakerType.INNER

        obj.maker_type = maker_type

    db.session.commit()


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
