import os
import sys
from collections import defaultdict
from functools import wraps
import time

from openpyxl import load_workbook

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from app.utils import batch_iter, ExcelExporter, upload_file
from app.utils.date_ import today_timestamp_utc
from app.schedules.reports.daily_balance import _get_threshold_map
from app.business.user_tag.helper import get_all_user_ids
from app.business import UserPreferences
from app.common import get_country, language_cn_names
from app.models import User

asset_cate_map = {
    '主流资产': ['CET', 'SHIB', 'USDT', 'ADA', 'BTC', 'DOGE', 'ETH', 'BABYDOGE', 'XRP', 'TRX', 'SOL'],
    '次主流资产': ['BNB', 'DOT', 'VET', 'MANA', 'MATIC', 'ETHW', 'BTT', 'ERG', 'ICP', 'DYDX', 'SLP', 'LUNC', 'WIN',
              'ETC', 'LINK', 'KDA', 'FTM', 'LTC', 'NFT', 'FIL', 'BCH', 'CAKE', 'SAND', 'LUNA', 'CELR', 'EOS', 'AVAX',
              'GALA', 'XLM', 'XEC', 'CHZ', 'HOT', 'ALGO', 'XTZ', 'STARL', 'ALICE', 'UNI', 'AXS', 'SUSHI', 'ZIL'],
    '长尾资产': ['TLM', 'CRO', 'BLOK', 'PIT', 'ATOM', 'ONE', 'DODO', 'THETA', 'ROSE', 'RVN', 'ELON', 'RACA', 'XCH',
             'IOTA', 'ENJ', 'SXP', 'GRT', 'FTT', '1INCH', 'NEAR', 'FLUX', 'LC', 'TRTL', 'ARRR', 'KISHU', 'NEO',
             'HBAR', 'XEM', 'ATLAS', 'RTM', 'REEF', 'BRG', 'AUDIO', 'USDC', 'OMG', 'KAVA', 'VRA', 'QTUM', 'XMR',
             'LBC', 'ONT', 'TOMO', 'BAT', 'AAVE', 'KLV', 'FLOW', 'HNT', 'KAS', 'APE', 'SC', 'SNX', 'DASH', 'IOST',
             'KIN', 'IOTX', 'DGB', 'OOE', 'DENT', 'CSPR', 'C98', 'ALPHA', 'PSI', 'OGV', 'EGLD', 'WAVES', 'CRV',
             'BSV', 'COMP', 'LRC', 'BAN', 'PHX', 'CHR', 'VTHO', 'TON', 'SOS', 'XVG', 'OCEAN', 'REN', 'MINA', 'EPX',
             'PUNDIX', 'COTI', 'ZEC', 'PGN', 'O3', 'BRISE', 'XDAG', 'MBOX', 'OGN', 'SYS', 'FLOKI', 'SPICE', 'CKB',
             'HYDRO', 'VLX', 'TARA', 'GMT', 'ASTROC', 'OLT', 'MARS', 'CFX', 'BAND', 'HTR', 'SCRT', 'BAKE', 'FIRO',
             'ANC', 'JASMY', 'XVS', 'YOOSHI', 'NNB', 'TVK', 'AYA', 'KSM', 'SLIM', 'ATA', 'ANKR', 'VINU', 'INJ',
             'MIR', 'AR', 'RLC', 'DGTX', 'SKL', 'UMA', 'CTXC', 'ICX', 'NMC', 'DC', 'RSR', 'CLV', 'RNDR', 'UTK',
             'BEAM', 'FITFI', 'FIS', 'EFI', 'ONES', 'ZRX', 'DON', 'POLS', 'VTC', 'SFP', 'RAD', 'LOOM', 'MASK',
             'DAR', 'GOLD', 'POND', 'ELA', 'GST', 'KAN', 'STORJ', 'XPR', 'USTC', 'BLZ', 'T', 'XNO', 'SOLO', 'BCN',
             'SRM', 'KMD', 'ZEN', 'WOO', 'STX', 'BFT', 'RUNE', 'GHST', 'CTK', 'IMX', 'LIT', 'KP3R', 'HNS', 'ORN',
             'CEL', 'LEVER', 'ILV', 'LSK', 'QRDO', 'DIA', 'MKR', 'CTSI', 'CUBE', 'SEELE', 'ZER', 'SGB', 'BOSON',
             'KNC', 'EMC', 'KAI', 'WAXP', 'AMP', 'IRIS', 'JULD', 'DERO', 'WAN', 'WEVE', 'HT', 'HOPR', 'ACM', 'SUN',
             'NFTB', 'BEL', 'WILD', 'ENS', 'XHV', 'COS', 'ARPA', 'GRIN', 'THG', 'AVA', 'DCR', 'ANT', 'BOBA', 'SRK',
             'TKO', 'FCH', 'TRB', 'JST', 'CQT', 'CVC', 'API3', 'NKN', 'FORTH', 'TORN', 'CHI', 'JEWEL', 'KLAY', 'HIVE',
             'HERO', 'LPT', 'STC', 'ETN', 'YFI', 'FET', 'RARI', 'DAI', 'OP', 'RARE', 'CMT', 'TUS', 'LAT', 'REP',
             'CREAM', 'STMX', 'QNT', 'CHESS', 'LOOKS', 'YGG', 'HARD', 'SUMO', 'HIGH', 'OXD', 'PRISM', 'NIM', 'MOVR',
             'IFT', 'UOS', 'AE', 'BSTN', 'SKILL', 'BUSD', 'GODS', 'POLIS', 'ONG', 'PEOPLE', 'BAL', 'ALPACA', 'GTC',
             'XAI', 'BNT', 'CFG', 'TFUEL', 'POLY', 'REVV', 'REQ', 'DUSK', 'VITE', 'POWR', 'SNFT', 'LAZIO', 'PORTO',
             'BADGER', 'SBR', 'ASTRO', 'DPET', 'GARI', 'XYM', 'DOCK', 'TOWER', 'PHNX', 'GAS', 'SPELL', 'PYR', 'GNO',
             'GLM', 'ARDR', 'CERE', 'PMON', 'BTS', 'MTL', 'PHA', 'VGX', 'XYO', 'PRE', 'TRIBE', 'GLMR', 'OKB', 'YFII',
             'NBS', 'CULT', 'SNT', 'JRT', 'VSYS', 'YUSRA', 'MONA', 'OXT', 'AUTO', 'APT', 'RAY', 'UNIM', 'COVAL', 'QI',
             'LIKE', 'JOE', 'NULS', 'SCREAM', 'BETA', 'DESO', 'RLY', 'SLC', 'BOND', 'QKC', 'SUNDAE', 'VOLT', 'FIDA',
             'CRA', 'ERN', 'BRD', 'GENE', 'ACH', 'COMBO', 'SUPER', 'WING', 'WRLD', 'SPS', 'JUSTICE', 'INST', 'TOWN',
             'ASTR', 'AGLD', 'BICO', 'BKK', 'MDX', 'FARM', 'PSP', 'FRONT', 'CELO', 'LDO', 'GAL', 'BIT', 'DDX', 'BONE',
             'SWEAT', 'NRG', 'LUNR', 'RING', 'NOTE', 'TT', 'ANML', 'CWS', 'OXEN', 'BTRST', 'BCMC', 'ACA', 'THC',
             'ROUTE', 'UNFI', 'CVX', 'IQ', 'LINA', 'JGN', 'RBW', 'STEEM', 'NABOX', 'ALI', 'AST', 'CWAR', 'GOG', 'CVP',
             'CRU', 'OSMO', 'HBB', 'OOKI', 'TRU', 'QUICK', 'RAIDER', 'FSN', 'KAR', 'ALCX', 'PERP', 'BRWL', 'HMT',
             'ORION', 'WEST', 'BNX', 'XED', 'BTM', 'MULTI', 'MINE', 'XDC', 'DFL', 'MPLX', 'BSW', 'SFL', 'PORT', 'VEGA',
             'SANTOS', 'FIO', 'PNK', 'NFTD', 'MLN', 'NEXO', 'UMEE', 'MC', 'DEXT', 'CUDOS', 'PIVX', 'KTON', 'WEMIX',
             'EVMOS', 'TRI', 'BITCI', 'NCT', 'LQTY', 'BABY', 'BUNNY', 'RON', 'DERC', 'PAXG', 'LITH', 'SLRS', 'STRAX',
             'GRS', 'HFT', 'STARLY', 'POKT', 'AUCTION', 'AURY', 'PLA', 'HEGIC', 'NMR', 'PLY', 'LOKA', 'ARK', 'EQX',
             'BURGER', 'TRAC', 'RMRK', 'BZZ', 'FXS', 'LON', 'POSI', 'LUMI', 'ALEPH', 'FX', 'REAL', 'QSP', 'SIS', 'RENA',
             'BOO', 'EPIK', 'TAMA', 'KRL', 'ZOON', 'XAVA', 'HEC', 'TIME', 'VOXEL', 'MNGO', 'TONE', 'XCN', 'STG', 'SDN',
             'SPIRIT', 'RAIL', 'ALBT', 'NUM', 'MAGIC', 'IDEX', 'SSS', 'ELF', 'SUKU', 'GEL', 'GFI', 'REF', 'DOSE',
             'AURORA', 'WHALE', 'DUST', 'KUB', 'TOKE', 'BIFI', 'MILK', 'EXFI', 'NGL', 'ALPINE', 'GMX', 'FODL', 'AIOZ',
             'BBS', 'FPFT', 'NYM', 'DNT', 'PENDLE', 'PTP', 'GMEE', 'THOR', 'DFYN', 'ADX', 'DERI', 'TSUKA', 'PRO',
             'DXD', 'PSTAKE', 'DOME', 'ZBC', 'CEEK', 'TNS', 'RBN', 'NSBT', 'DEP', 'PUSH', 'POLC', 'WNXM', 'OPUL',
             'ORCA', 'ASTO', 'ROOK', 'XYZ', 'ZIG', 'RIDE', 'MCO2', 'PRIMATE', 'BEETS', 'COW', 'ORB', 'ANGLE', 'SD',
             'MPL', 'RSS3', 'METIS', 'DVI', 'GF', 'SSV', 'MOB', 'OVR', 'XPRT', 'EDEN', 'INDI', 'HOP', 'CRPT',
             'SYLO', 'EWT', 'BEND', 'SYNR', 'MUSE', 'GNS', 'DVF', 'PNG', 'EUL', 'FOX', 'SFUND', 'TLOS', 'AART',
             'WNCG', 'XDEFI', 'YDF', 'CLY', 'MFT', 'KEY', 'DREP', 'RIF', 'CTX', 'SLND', 'SYN', 'INV', 'RPL', 'DG',
             'OCT', 'EBEN', 'PSY', 'WBTC', 'CANTO', 'DHT', 'STPT', 'BREED', 'WAXL', 'STRP', 'LYXE', 'FLX', 'PROM',
             'EPS', 'MEME', 'STAKE', 'RAMP']
}


def calc_time(func):
    @wraps(func)
    def dec(*args, **kwargs):
        print(f'>>>{func.__name__}')
        start = time.time()
        ret = func(*args, **kwargs)
        print(f'use time {time.time() - start}<<<')
        return ret

    return dec


def gen_asset_cate_map():
    wb = load_workbook("./deployment/scripts/2022.11.25资产情况.xlsx")
    ws = wb[wb.sheetnames[0]]
    asset_map = defaultdict(list)
    for row in ws.iter_rows(min_row=3):
        if row:
            val, cate = row[1].value, row[2].value
            asset_map[cate].append(val)
    print(asset_map)
    return asset_map


def get_asset_cate_map():
    asset_map = dict()
    for k, v in asset_cate_map.items():
        for asset in v:
            asset_map[asset] = k
    return asset_map


@calc_time
def main():
    from app.business import ExchangeLogDB

    model = User
    user_rows = model.query.filter(
        model.user_type != model.UserType.SUB_ACCOUNT
    ).with_entities(
        model.id,
        model.location_code
    )
    user_map = {i.id: i for i in user_rows}

    asset_map = get_asset_cate_map()
    min_amount_map = _get_threshold_map()

    ts = today_timestamp_utc()
    user_data = defaultdict(lambda: defaultdict(set))
    table = ExchangeLogDB.user_account_balance_table(ts)
    for chunk_user_ids in batch_iter(user_map.keys(), 1000):
        records = table.select(
            'user_id',
            'asset',
            'SUM(balance) as total_balance',
            where=f"user_id in ({','.join(map(str, chunk_user_ids))})",
            group_by='asset, user_id',
        )
        for record in records:
            user_id, asset, balance = record
            if asset in min_amount_map and asset in asset_map:
                if balance > min_amount_map[asset]:
                    cate = asset_map[asset]
                    user_data[user_id][cate].add(asset)

    to_excel_data = []
    lang_map = language_cn_names()
    for k, v in user_data.items():
        if v["长尾资产"]:
            tmp = dict()
            tmp["用户id"] = k
            for field in ["主流资产", "次主流资产", "长尾资产"]:
                tmp[field] = '、'.join(v[field])
            tmp["国家"] = c.cn_name if (c := get_country(user_map[k].location_code)) else "其他"
            tmp["语言"] = lang_map.get(UserPreferences(k).language, "/")
            to_excel_data.append(tmp)
    to_excel_data.sort(key=lambda x: x["用户id"])
    to_excel(to_excel_data)


def to_excel(data):
    one_data = data[0]
    export = ExcelExporter(
        data_list=list(data),
        fields=list(one_data.keys()),
        headers=list(one_data.keys()),
    )
    streams = export.export_streams()
    file_url = upload_file(streams, "xlsx")
    print(file_url)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        # gen_asset_cate_map()
        main()
