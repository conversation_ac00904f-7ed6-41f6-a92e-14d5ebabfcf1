import os
import sys
from functools import wraps

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from datetime import timedelta, date

    from app.models import db
    from app.models import DailyPerpetualInsuranceReport, DailyMarginFundReport
    from app.business.export.base import add_print

    @add_print
    def brush_perp_date():
        model = DailyPerpetualInsuranceReport
        rows = model.query.all()
        index = 0
        for row in rows:
            row.report_date -= timedelta(days=1)
            index += 1
            if index >= 5000:
                db.session.commit()
                index = 0

        db.session.commit()

    @add_print
    def handle_margin_date():
        model = DailyMarginFundReport
        model.query.filter(
            model.report_date >= date.today()
        ).delete()
        db.session.commit()

    brush_perp_date()

    handle_margin_date()


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
