# -*- coding: utf-8 -*-

import os
import sys
import time
import datetime
from tqdm import tqdm

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def insert_user_last_activeness():
    from app.models import UserActivenessHistory, UserLastActiveness, db

    exist_user_ids = set()
    today = datetime.datetime.utcnow().date()
    last_date = today - datetime.timedelta(days=1)
    progress_bar = tqdm(total=720)
    start = 1
    end = 720
    # 只统计最近两年的活跃数据
    while start <= end:
        active_user_ids = UserActivenessHistory.filter_active_users(last_date, last_date)
        for user_id in active_user_ids:
            if user_id in exist_user_ids:
                continue
            db.session.add(UserLastActiveness(user_id=user_id, last_active_date=last_date))
            exist_user_ids.add(user_id)
        last_date = last_date - datetime.timedelta(days=1)
        start += 1
        progress_bar.update(1)
        time.sleep(0.05)
        db.session.commit()


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        insert_user_last_activeness()
