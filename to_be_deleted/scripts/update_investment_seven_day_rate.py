# -*- coding: utf-8 -*-
import os
import sys
from collections import defaultdict
from decimal import Decimal

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.models import db, DailyInvestmentReport
    from app.utils import quantize_amount

    results = DailyInvestmentReport.query.order_by(
        DailyInvestmentReport.report_date.asc()
    ).all()

    asset_invest_map = defaultdict(list)

    for item in results:
        asset_invest_map[item.asset].append(item)

    for asset, invest_data in asset_invest_map.items():
        tmp_list = []
        for i in invest_data:
            if len(tmp_list) == 7:
                tmp_list.pop(0)
            tmp_list.append(i)

            rate_list = [data.day_rate for data in tmp_list]

            seven_day_rate = quantize_amount(sum(rate_list) * 365 / l, 6) if (l := len(rate_list)) > 0 else Decimal()
            i.seven_day_rate = seven_day_rate
    db.session.commit()


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
