import os
import sys
import datetime
from functools import partial

from sqlalchemy import func

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from collections import defaultdict
from decimal import Decimal
from datetime import date

from app.models import User, AssetPrice, Deposit, UserActivenessHistory, \
    UserTradeSummary
from app.business import filter_active_users
from app.utils import ExcelExporter, upload_file


def get_lbc_trade_summary(start_date, end_date):
    from app.business import TradeSummaryDB
    AssetPrice.get_price_map()
    db = TradeSummaryDB
    select_fields = ('user_id', 'trade_date', 'deal_amount')
    where = f' stock_asset = "LBC" '
    query_data = []
    for table_name in TradeSummaryDB.get_user_trade_summary_tables(start_date, end_date):
        print(f'get_lbc_trade_summary table:{table_name}')
        s = db.table(f'{table_name}').select(
            *select_fields,
            where=where,
        )
        query_data.extend(list(dict(zip(select_fields, r)) for r in s))

    lbc_price_map = defaultdict(Decimal)
    lbc_user_trade_summary_map = defaultdict(Decimal)
    for item in query_data:
        if item['trade_date'] in lbc_price_map:
            price = lbc_price_map[item['trade_date']]
        else:
            price = AssetPrice.query.filter(
                AssetPrice.asset == 'LBC',
                AssetPrice.date >= item['trade_date'],
            ).first().price
            lbc_price_map[item['trade_date']] = price
        lbc_user_trade_summary_map[item['user_id']] += price * item['deal_amount']

    return lbc_user_trade_summary_map


def get_user_trade_summary(start_date, end_date, user_ids):
    from app.business import TradeSummaryDB
    db = TradeSummaryDB
    select_fields = ('user_id', 'trade_date', 'stock_asset')
    search_user_ids = tuple(user_ids)
    query_data = []
    for table_name in TradeSummaryDB.get_user_trade_summary_tables(start_date, end_date):
        print(f'get_user_trade_summary table:{table_name}')
        where = f'user_id IN {search_user_ids}'
        if len(search_user_ids) == 1:
            where = f'user_id = {search_user_ids[0]}'
        if not search_user_ids:
            break
        s = db.table(f'{table_name}').select(
            *select_fields,
            where=where,
        )
        q = list(dict(zip(select_fields, r)) for r in s)
        query_user_ids = [user['user_id'] for user in q]
        query_data.extend(q)
        search_user_ids = tuple([user_id for user_id in search_user_ids if
                                 user_id not in query_user_ids])

    user_map = {}
    for user in query_data:
        if user['user_id'] not in user_map:
            user_map[user['user_id']] = {
                'first_trade_asset': user['stock_asset'],
                'trade_date': user['trade_date'],
            }
    return user_map


def get_user_active(start_date, end_date, user_ids):
    set_user_ids = set(user_ids)
    user_active_map = {}
    while start_date < end_date:
        active_user_list = filter_active_users(start_date, start_date)
        # 取交集少占用些内存
        user_active_map[start_date] = set(active_user_list) & set_user_ids
        start_date = start_date + datetime.timedelta(days=1)
    return user_active_map


def get_user_trade_map(user_ids):
    user_trade_map = defaultdict(set)
    query = UserTradeSummary.query.with_entities(
        UserTradeSummary.report_date, UserTradeSummary.user_id)
    for item in query:
        if item.user_id in user_ids:
            user_trade_map[item.report_date].add(item.user_id)

    return user_trade_map


def get_all_user_summary(user_ids):
    all_user_summary_map = defaultdict(Decimal)
    batch_size = 5000
    total_page = len(user_ids) / batch_size
    for i in range(int(total_page) + 1):
        start = batch_size * i
        _user_ids = user_ids[start: start + batch_size]
        user_query = UserTradeSummary.query.group_by(
            UserTradeSummary.user_id).with_entities(
            UserTradeSummary.user_id,
            func.sum(UserTradeSummary.trade_amount).label('trade_amount')
        ).filter(UserTradeSummary.user_id.in_(_user_ids)).all()
        for user in user_query:
            all_user_summary_map[user.user_id] = user.trade_amount

    return all_user_summary_map


def get_user_created_date(user_ids):
    user_created_date_map = dict()
    batch_size = 5000
    total_page = len(user_ids) / batch_size
    for i in range(int(total_page) + 1):
        start = batch_size * i
        _user_ids = user_ids[start: start + batch_size]
        user_query = User.query.filter(User.id.in_(_user_ids)).with_entities(
            User.id, User.created_at).all()
        for user in user_query:
            user_created_date_map[user.id] = user.created_at

    return user_created_date_map


def format_user_first_trade_data(dict_map, user_data, active_key):
    user_id = user_data['user_id']
    first_trade_date = user_data['first_trade_date']
    first_trade_asset = user_data['first_trade_asset']
    if first_trade_asset == 'LBC':
        user_data['7d_' + active_key] = '否'
        user_data['30d_' + active_key] = '否'
        user_data['90d_' + active_key] = '否'
        after_90d_date = first_trade_date + datetime.timedelta(days=90)
        after_30d_date = first_trade_date + datetime.timedelta(days=30)
        after_7d_date = first_trade_date + datetime.timedelta(days=7)
        date_list = [k for k, v in dict_map.items() if user_id in v]
        if date_list:  # 兼容活跃表或交易表中没有数据的用户
            max_date = max(date_list)
            if max_date >= after_90d_date:
                user_data['7d_' + active_key] = '是'
                user_data['30d_' + active_key] = '是'
                user_data['90d_' + active_key] = '是'
            elif max_date >= after_30d_date:
                user_data['7d_' + active_key] = '是'
                user_data['30d_' + active_key] = '是'
                user_data['90d_' + active_key] = '否'
            elif max_date >= after_7d_date:
                user_data['7d_' + active_key] = '是'
                user_data['30d_' + active_key] = '否'
                user_data['90d_' + active_key] = '否'
    else:
        user_data['7d_' + active_key] = '-'
        user_data['30d_' + active_key] = '-'
        user_data['90d_' + active_key] = '-'


def get_user_balance_map(query_date, user_ids):
    from app.business import TradeLogDB, PerpetualLogDB, PriceManager, \
        date_to_datetime, quantize_amount
    timestamp = date_to_datetime(query_date).timestamp()
    user_all_balance_usd_map = defaultdict(Decimal)
    user_lbc_balance_map = defaultdict(Decimal)
    TradeLogDB.get_user_balances(timestamp)
    prices = PriceManager.assets_to_usd()
    quantize = partial(quantize_amount, decimals=16, precision=34)

    for (user_id,
         asset,
         account,
         balance) in TradeLogDB.get_user_balances(timestamp):
        if user_id not in user_ids:
            continue
        asset_price = prices.get(asset, Decimal()) or Decimal()
        if asset == 'LBC':
            user_lbc_balance_map[user_id] += balance
        user_all_balance_usd_map[user_id] += balance * asset_price

    for (user_id, asset, balance) in PerpetualLogDB.get_user_balances(
            timestamp):
        if user_id not in user_ids:
            continue
        asset_to_usd = prices.get(asset, Decimal()) or Decimal()
        balance_usd = quantize(balance * asset_to_usd)
        user_all_balance_usd_map[user_id] += balance_usd

    return user_all_balance_usd_map, user_lbc_balance_map


def get_user_coin_deposit():
    coin_deposit = Deposit.query.filter(
        Deposit.status.in_([
            Deposit.Status.FINISHED,
            Deposit.Status.TO_HOT]),
        Deposit.asset == 'LBC',
    ).with_entities(
        Deposit.user_id.distinct().label('user_id'),
    ).all()
    return coin_deposit


def get_first_deposit_asset(user_ids):
    user_first_deposit_asset_map = dict()
    batch_size = 5000
    total_page = len(user_ids) / batch_size
    for i in range(int(total_page) + 1):
        start = batch_size * i
        _user_ids = user_ids[start: start + batch_size]
        user_query = Deposit.query.filter(
            Deposit.status.in_(
                [Deposit.Status.FINISHED, Deposit.Status.TO_HOT]),
            Deposit.user_id.in_(_user_ids)
        ).with_entities(
            Deposit.user_id, Deposit.created_at, Deposit.asset).all()
        for item in user_query:
            if item.user_id not in user_first_deposit_asset_map:
                user_first_deposit_asset_map[item.user_id] = {
                    'asset': item.asset,
                    'created_at': item.created_at,
                }
            else:
                if item.created_at < user_first_deposit_asset_map[item.user_id]['created_at']:
                    user_first_deposit_asset_map[item.user_id]['created_at'] = item.created_at
                    user_first_deposit_asset_map[item.user_id]['asset'] = item.asset

    return user_first_deposit_asset_map


def main():
    start_date = date(2019, 12, 1)  # LBC上架时间
    end_date = date(2020, 10, 31)
    print('get_user_coin_deposit')
    coin_deposit = get_user_coin_deposit()
    deposit_user_list = [item.user_id for item in coin_deposit]
    print('get_lbc_trade_summary')
    lbc_user_trade_summary_map = get_lbc_trade_summary(start_date, end_date)
    lbc_trade_user_list = list(lbc_user_trade_summary_map)
    all_user_list = list(set(deposit_user_list + lbc_trade_user_list))
    print('get_first_deposit_asset')
    user_first_deposit_asset_map = get_first_deposit_asset(all_user_list)
    print('get_user_trade_summary')
    user_map = get_user_trade_summary(start_date, end_date, all_user_list)
    print('get_user_active')
    user_active_map = get_user_active(start_date, end_date, all_user_list)
    print('get_user_balance_map')
    user_all_balance_usd_map, user_lbc_balance_map = get_user_balance_map(
        end_date, all_user_list)
    print('get_user_trade_map')
    user_trade_map = get_user_trade_map(all_user_list)
    print('get_all_user_summary')
    all_user_summary_map = get_all_user_summary(all_user_list)
    print('get_user_created_date')
    user_created_date_map = get_user_created_date(all_user_list)
    records = []
    for user_id in all_user_list:
        print(f'format user: {user_id} data')
        format_data = dict()
        format_data['user_id'] = user_id
        format_data['created_at'] = user_created_date_map[user_id] if user_id in user_created_date_map else '-'
        format_data['is_deposit_lbc'] = '是' if user_id in deposit_user_list else '否'
        format_data['is_trade_lbc'] = '是' if user_id in lbc_trade_user_list else '否'
        format_data['first_trade_asset'] = user_map[user_id]['first_trade_asset'] if user_id in user_map else '-'
        format_data['first_deposit_asset'] = user_first_deposit_asset_map[user_id]['asset'] if user_id in user_first_deposit_asset_map else '-'
        format_data['first_trade_date'] = user_map[user_id]['trade_date'] if user_id in user_map else date.min  # 兼容没有交易过的用户
        format_user_first_trade_data(user_trade_map, format_data, 'trade')
        format_user_first_trade_data(user_active_map, format_data, 'active')
        format_data['lbc_balance'] = user_lbc_balance_map[user_id]
        format_data['balance'] = user_all_balance_usd_map[user_id]
        format_data['lbc_trade_value'] = lbc_user_trade_summary_map[user_id]
        format_data['all_trade_value'] = all_user_summary_map[user_id]
        records.append(format_data)

    header_data = [
        ('user_id', '曾经持有过lbc的账户id'),
        ('created_at', '账户注册日期'),
        ('is_deposit_lbc', '是否充值过lbc'),
        ('first_deposit_asset', '首次充值币种'),
        ('is_trade_lbc', '是否交易过lbc'),
        ('first_trade_asset', '首次交易币种'),
        ('7d_active', '首次交易lbc7日后是否活跃'),
        ('30d_active', '首次交易lbc30日后是否活跃'),
        ('90d_active', '首次交易lbc90日后是否活跃'),
        ('7d_trade', '首次交易lbc7日后是否交易'),
        ('30d_trade', '首次交易lbc30日后是否交易'),
        ('90d_trade', '首次交易lbc90日后是否交易'),
        ('lbc_balance', '账户当前余额（仅lbc）'),
        ('balance', '账户当前余额（USD 所有币种）'),
        ('lbc_trade_value', '总交易量（usd，仅lbc）'),
        ('all_trade_value', '总交易量（usd，全部币种）'),
    ]
    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]
    streams = ExcelExporter(
        data_list=records, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
