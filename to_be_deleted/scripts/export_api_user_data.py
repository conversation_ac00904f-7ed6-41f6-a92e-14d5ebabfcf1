# -*- coding: utf-8 -*-
import os
import sys
import time
from tqdm import tqdm

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    from app.business import PerpetualHistoryDB, TradeHistoryDB
    from app.business.risk_control.withdrawal import BalanceManager
    from app.models import ApiAuth, AssetPrice, User, SubAccount
    from app.utils import ExcelExporter, upload_file
    spot_order_history_db = TradeHistoryDB()
    perpetual_order_history_db = PerpetualHistoryDB()
    spot_api_user_ids = set()
    perpetual_api_user_ids = set()
    temp_user_ids = list(range(500))  # 取500刚好覆盖到所有的表
    table_name = 'order_history'
    spot_order_tables = spot_order_history_db.users_to_dbs_and_tables(temp_user_ids, table_name)
    perpetual_order_tables = perpetual_order_history_db.users_to_dbs_and_tables(temp_user_ids, table_name)
    sub_map = {i.user_id: i.main_user_id for i in SubAccount.query.all()}
    # 目前23542个用户
    api_user_ids = {sub_map.get(i.user_id, i.user_id) for i in ApiAuth.query.with_entities(ApiAuth.user_id.distinct().label('user_id')).all()}
    progress_bar = tqdm(total=len(temp_user_ids) * 2 + len(api_user_ids))

    field = ('user_id',)
    columns = ("distinct user_id",)
    for db_conf in spot_order_tables:
        db_name = db_conf[0]
        where = "source like '%api%'"
        for table_ in db_conf[1].keys():
            records = db_name.table(table_).select(
                *columns,
                where=where,
            )
            list_ = list(dict(zip(field, record)) for record in records)
            progress_bar.update(1)
            if list_:
                # 只统计主账号
                spot_api_user_ids.update([sub_map.get(i['user_id'], i['user_id']) for i in list_])
    for db_conf in perpetual_order_tables:
        db_name = db_conf[0]
        where = "source = 'API'"
        for table_ in db_conf[1].keys():
            records = db_name.table(table_).select(
                *columns,
                where=where,
            )
            list_ = list(dict(zip(field, record)) for record in records)
            progress_bar.update(1)
            if list_:
                perpetual_api_user_ids.update([sub_map.get(i['user_id'], i['user_id']) for i in list_])
    records = []
    price_map = AssetPrice.get_price_map()
    for user_id in api_user_ids:
        bool_spot_api = True if user_id in spot_api_user_ids else False
        bool_perpetual_api = True if user_id in perpetual_api_user_ids else False
        user_data = User.query.get(user_id)
        sub_user_ids = [i.user_id for i in SubAccount.query.filter(
            SubAccount.main_user_id == user_id, SubAccount.status == SubAccount.Status.VALID).all()]
        bm = BalanceManager(user_id, sub_user_ids, price_map)
        current_balance = bm.get_current_balance_usd()
        records.append({
            'user_id': user_id,
            'email': user_data.email,
            'mobile': user_data.mobile,
            'current_balance': current_balance,
            'bool_spot_api': bool_spot_api,
            'bool_perpetual_api': bool_perpetual_api,
        })
        time.sleep(0.05)
        progress_bar.update(1)

    header_data = [
        ('user_id', 'ID'),
        ('email', '邮箱'),
        ('mobile', '手机'),
        ('current_balance', '总资产（USD）'),
        ('bool_spot_api', '是否通过api下现货单'),
        ('bool_perpetual_api', '是否通过api下合约单'),
    ]
    fields = [r[0] for r in header_data]
    headers = [r[1] for r in header_data]

    streams = ExcelExporter(
        data_list=records, fields=fields, headers=headers
    ).export_streams()
    file_url = upload_file(streams, 'xlsx')
    print(file_url)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
