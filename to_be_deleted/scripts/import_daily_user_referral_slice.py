# -*- coding: utf-8 -*-
import os
import sys
from datetime import timedelta
from decimal import Decimal

import click


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


from sqlalchemy import func
from app.models import db, AssetPrice, ReferralAssetHistory, DailyUserReferralSlice, ReferralHistory
from app.utils.date_ import today
from app.utils import quantize_amount


def _insert_or_update_daily_user_referral_slice(query_time):
    # 老邀请数据只会有一个邀请码, 只group by referrer_id 就好
    ref_count_query = (
        ReferralHistory.query.filter(
            ReferralHistory.created_at >= query_time,
            ReferralHistory.created_at < query_time + timedelta(days=1),
        )
        .group_by(
            ReferralHistory.referrer_id,
        )
        .with_entities(
            ReferralHistory.referrer_id,
            func.count("*"),
        )
        .all()
    )
    ref_count_map = {user_id: count for user_id, count in ref_count_query}

    ref_assets = (
        ReferralAssetHistory.query.filter(ReferralAssetHistory.date == query_time)
        .group_by(ReferralAssetHistory.user_id, ReferralAssetHistory.asset)
        .with_entities(
            ReferralAssetHistory.user_id,
            ReferralAssetHistory.asset,
            func.sum(ReferralAssetHistory.amount).label("amount"),
        )
        .all()
    )

    prices = AssetPrice.get_price_map(query_time)
    new_slices = []
    for row in ref_assets:
        user_id = row.user_id
        usd = quantize_amount(row.amount * prices.get(row.asset, Decimal()), 2)
        ref_slice = DailyUserReferralSlice.query.filter(
            DailyUserReferralSlice.user_id == user_id, DailyUserReferralSlice.date == query_time
        ).first()
        if ref_slice:
            # update
            ref_slice.referral_count = ref_count_map.get(user_id, 0)
            ref_slice.amount_usd = usd
        else:
            new_slices.append(
                DailyUserReferralSlice(
                    date=query_time,
                    user_id=user_id,
                    referral_id=None,  # 只写入汇总数据
                    referral_count=ref_count_map.get(user_id, 0),
                    amount_usd=usd,
                )
            )

    db.session.add_all(new_slices)
    db.session.commit()
    print("{} DailyUserReferralSlice 已导入 {}条".format(query_time, len(ref_assets)))


def import_daily_user_referral_slice_data(days=30):
    """ 刷历史推荐数据到推广数据分析表中，默认刷最近30天的数据 """
    today_ = today()
    start_dt = today_ - timedelta(days=days + 1)
    end_dt = today_ - timedelta(days=1)
    while start_dt <= end_dt:
        _insert_or_update_daily_user_referral_slice(start_dt)
        start_dt += timedelta(days=1)


@click.command()
@click.option("--i-know-what-i-am-doing", is_flag=True)
@click.argument("days", type=click.INT, default=30)
def main(i_know_what_i_am_doing, days):
    if i_know_what_i_am_doing:
        import_daily_user_referral_slice_data(days)
    else:
        print("add --i-know-what-i-am-doing to run")


if __name__ == "__main__":
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
