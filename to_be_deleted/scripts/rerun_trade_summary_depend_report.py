# -*- coding: utf-8 -*-
import os
import sys
import traceback


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir("../../")
sys.path.append(os.getcwd())


# noinspection PyBroadException
def main():
    from app.business.report.refer import DailyAmbassadorReferralReporter
    from app.schedules.reports.ambassador_report import update_daily_ambassador_detail_report_schedule
    from app.schedules.reports.country_user_data_report import (
        update_daily_country_user_data_schedule,
        update_daily_language_user_data_schedule,
    )
    from app.schedules.reports.maker_report import (
        update_daily_maker_detail_report_schedule,
        update_daily_maker_report_schedule,
    )
    from app.schedules.reports.refer_report import update_daily_refer_type_report_schedule
    from app.schedules.reports.spot_trade import update_daily_spot_trade_schedule
    from app.schedules.reports.user_report import update_daily_users_list_schedule
    from app.schedules.perpetual import update_daily_perpetual_trade_report_schedule

    report_name_func_map = {
        "大使报表-日报": DailyAmbassadorReferralReporter().dispatch,
        "大使返佣-日报": update_daily_ambassador_detail_report_schedule,
        "国家用户统计-日报": update_daily_country_user_data_schedule,
        "国家报表-语言分布-日报": update_daily_language_user_data_schedule,
        "做市商交易记录-日报": update_daily_maker_detail_report_schedule,
        "做市报表-合约/现货-日报": update_daily_maker_report_schedule,
        "现货全站交易-日报": update_daily_spot_trade_schedule,
        "用户报表-日报": update_daily_users_list_schedule,
        "返佣报表-日报": update_daily_refer_type_report_schedule,
        "合约报表-日报": update_daily_perpetual_trade_report_schedule,
    }
    for name, func in report_name_func_map.items():
        try:
            func()
            print(f"<{name}> 生成成功")
        except Exception as _e:
            print(f"<{name}> 生成失败:")
            traceback.print_exc()


if __name__ == "__main__":
    """ 如果UserTradeSummary数据没生成，则重新生成依赖UserTradeSummary的报表 """
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
