# 持仓均价计算与实现文档

## 需求概述
- 在现货持仓页面，展示各币种的“持仓均价”字段
- 当资产变化时，更新“持仓均价”（仅在增加时更新，减少时不更新，且需要判断更新的场景）
- 提供给用户修改“持仓均价”的入口

## 计算公式
```math
更新持仓均价 = (增量 * 价格 + （当前持仓量 * 持仓均价）) / (当前持仓量 + 增量)
```
---

## 一、实现概述
### 1. 通过kafka消息推送通知“资产更新”事件（转账、充值、等等）以及“成交”事件（现货、兑换成交）
### 2. 在消费者进程中更新持仓均价，更新db
### 3. 用户的各个账户（包含现货、合约、杠杆、理财、借贷、AMM账户）都有对应的持仓均价记录。数据维度：“用户ID-账户类型-币种” 

## 一、具体实现
### 1. Model
```python
"""
class AccountBalanceType(Enum):
    SPOT = 'spot'
    MARGIN = 'margin'
    INVESTMENT = 'investment'
    PERPETUAL = 'perpetual'
    AMM = 'amm'
    PLEDGE = 'pledge'
    STAKING = 'staking'
"""

class AssetCost(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('user_id', 'account_type', 'asset', name="user_id_account_type_asset_uniq"),
    )

    user_id = db.Column(db.Integer, nullable=False)  # no foreign key maybe
    account_type = db.Column(db.StringEnum(AccountBalanceType), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
```

### 2. 消息推送
- 2.1 资产更新事件
    - 资产更新（增加）时，包含充值、划转、转账、奖励等等业务场景
    - 调用 server 的资产变更接口：`asset.update`, `asset.update_batch`时，上报事件：
    ```python
    def add_user_balance(self, user_id: int, asset: str, amount: AmountType,
                         business: Union[str, BalanceBusiness],
                         business_id: int, detail: Dict[str, Any] = None,
                         *, account_id: int = SPOT_ACCOUNT_ID) -> bool:
        if not amount:
            return False
        if isinstance(business, BalanceBusiness):
            business = business.value
        try:
            self.do_request('asset.update', user_id, account_id, asset,
                            business, business_id, str(amount), detail or {})
        except self.BadResponse as e:
            # error handling
            pass
        # send KAFKA message here.
        message = {
            'user_id': user_id,
            'account_id': account_id,
            'asset': asset,
            'business': business,
            'business_id': business_id,
            'amount': str(amount),
            'detail': detail or {},
        }
        
        producer.send_message(ProducerTopics.BALANCE_UPDATE, message)
        return True
    ```
- 2.2 成交事件(由server推送)
    - 增加app/consumer/kafka_balance_update.py 消费资产更新事件
    - app/consumer/kafka_spot_deals.py中增加一个handler消费成交事件

### 3. 消费逻辑：
3.1 判断此资产更新事件属于哪个账户:
    - 资产更新事件：**根据account_id和business判断资产更新属于哪个账户**
    - 成交事件：根据account_id区分现货、杠杆。
3.2 计算公式：

   ```math
   持仓均价 = (增量 * 价格 + (当前持仓量 * 持仓均价)) / (当前持仓量 + 增量)
   ```
   - 确定以什么价格更新持仓均价：
    - 1. **其他账户带价转入**：取其他账户的持仓均价为增量的价格来更新均价，**适用于划转相关的场景**
    - 2. **按 0 成本更新**：增量均价为 0，更新后均价降低，**适用于奖励、返佣、红包、理财收益类的场景**
    - 3. **按成交价或市场价更新**：**适用于交易、充值转账、借币类场景**
    - 4. **不更新**: 资产减少时（如划出、卖出、提现、还币等），**都不更新持仓均价**，**合约成交也不更新均价**
---
3.3 按账户分类讨论：
#### 现货账户
1. 划入：带价转入
2. 交易（含兑换）：按成交价更新
3. 转账（含充值）：按市场价更新
4. 奖励（含活动奖励、返佣等）：0 成本更新
5. 质押理财收益：0 成本更新

#### 杠杆账户

1. 划入：带价划入
2. 交易：按成交价更新
3. 借币：按市场价更新

#### 合约账户

1. 划入：带价划入
2. 盈利、亏损：不更新持仓均价

#### 借贷账户

1. 增加质押币种：带价划入
2. 还币：不更新

#### 理财账户

1. 划入：带价划入
2. 利息：0 成本更新

#### 质押账户

1. 质押：带价划入
2. 赎回：不更新

#### AMM 账户

1. 注入流动性：带价划入
2. 提取流动性：不更新

> **备注：** AMM 注入流动性是给系统账户加资产，这种场景可以根据 business_id 取到业务表 id，从而得到 user_id。（提取流动性也一样）

#### 子账号

1. 主账号划入：子账号持仓均价更新（可能是现货或合约账户）
2. 子账号划出：主账号持仓均价更新（可能是现货或合约账户）
3. 其他场景和现货账户一致

---
---
### 4. 接口改动：
4.1 获取持仓均价: GET
- 参数:
    - 币种列表：
- 返回值:
```
[
    {
        "asset": "CET",
        "price": "123"
    },
    {
        "asset": "BTC",
        "price": "1234"
    }
]
```

4.2 持仓均价修改：POST