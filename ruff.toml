# Rules: https://beta.ruff.rs/docs/rules/

# The minimum Python version
target-version = "py311"

exclude = [
    # No need checking dirs
    "data",
    "deployment",
    "migrations",
    "scripts",
    "tests",
]

# Decrease the maximum line length to 140 characters.
line-length = 140

lint.select = [
    "E",   # pycodestyle
    "F",   # pyflakes
]

lint.ignore = [
    "F541",     # f-string without any placeholders
    "E731",     # Do not assign a `lambda` expression, use a `def`
]

[lint.per-file-ignores]
"__init__.py" = [
    "F401",     # unused-import
    "F403",     # undefined-local-with-import-star
    "F405"      # undefined-local-with-import-star-usage
]
"app/config/testing.py" = ["E501"]   # line-too-long
"manage.py" = ["E402"]
"run.py" = [
    "E402",  # module-import-not-at-top-of-file
    "E702",  # multiple-statements-on-one-line-semicolon
]
"run_admin.py" = ["E402", "E702"]
"run_celery.py" = ["E402", "E702"]
"run_celery_exporter.py" = ["E402", "E702"]
"run_spot_ws_client.py" = ["E402", "E702"]
"run_perpetual_ws_client.py" = ["E402", "E702"]
"run_kline_booster.py" = ["E402", "E702"]
"run_kafka_perpetual_deals.py" = ["E402", "E702"]