name: master_protection
on:
  pull_request:
    branches:
      - master
    types: [synchronize, opened, reopened, edited]

jobs:
  check_target:
    name: check_target
    runs-on: ubuntu-latest
    steps:
      - name: Get branch names
        id: branch-name
        uses: tj-actions/branch-names@v5.2

      - name: Check target branch not master expect hot-fix
        if: steps.branch-name.outputs.base_ref_branch == 'master'
        run: |
          echo "current branch is ${{ steps.branch-name.outputs.current_branch }}"
          echo "target branch is ${{ steps.branch-name.outputs.base_ref_branch }}"

          if ([ ${{ steps.branch-name.outputs.current_branch }} == 'release' ] ||
                [[ ${{ steps.branch-name.outputs.current_branch }} =~ hotfix* ]]); then
            exit 0;
          fi

          exit 1;
