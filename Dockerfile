# FROM: 必须以 FROM 命令开始，初始化构建阶段，设置为后续命令的 基础镜像。
# 使用和测试环境相同系统的基础镜像,减少系统和依赖库冲突问题
# AS: 第一阶段构建完的镜像别名，方便后续其他阶段引用，别名规定完全小写
# 第一阶段：构建系统和必备的软件
FROM ubuntu:20.04 AS base
# WORKDIR: 设置容器内部设置工作目录，后续命令会在此目录下执行
WORKDIR /coinex_backend

# ENV: 设置容器内环境变量，持久保持在容器中，可以在容器中用 env 命令查看
ENV PY_VERSION=3.11
# ubuntu apt update 忽略时区
ENV DEBIAN_FRONTEND=noninteractive
# 启动 flask debug 模式
ENV FLASK_ENV=development

# RUN: 执行任何命令并存储结果
# 替换 apt 源，加速下载
# ppa:deadsnakes/ppa 为 python 的下载源
RUN sed -i s/archive.ubuntu.com/mirrors.aliyun.com/g /etc/apt/sources.list \
    && sed -i s/security.ubuntu.com/mirrors.aliyun.com/g /etc/apt/sources.list \
    && sed -i s/ports.ubuntu.com/mirrors.aliyun.com/g /etc/apt/sources.list \
    && apt update \
    && apt -y install software-properties-common \
    && apt-add-repository ppa:deadsnakes/ppa \
    && apt install -y --no-install-recommends python${PY_VERSION} python3-pip python${PY_VERSION}-distutils \
    && apt clean;


# 第二阶段构建：以第一阶段的镜像作为基础镜像，
# 安装 python 第三方库和对应的系统依赖库，
FROM base AS build
WORKDIR /coinex_backend

RUN apt install -y --no-install-recommends libpython${PY_VERSION}-dev python3-cffi \
    build-essential libssl-dev libffi-dev automake libtool pkg-config libsecp256k1-dev wget git curl\
    && apt clean \
    && rm -rf /tmp/* /var/lib/apt/* /var/cache/* /var/log/*;

RUN echo "Downloading GeoLite2-City..." \
    && wget "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-City&license_key=5qG98ZfdBUAEwSYT&suffix=tar.gz" -O GeoLite2-City.tar.gz \
    && tar -xzf GeoLite2-City.tar.gz \
    && directory=`ls | xargs -d ' ' | grep GeoLite2-City_* | grep -v tar.gz` \
    && mkdir -p ./data/geoip/ \
    && mv $directory/GeoLite2-City.mmdb data/geoip/ \
    && rm -r $directory GeoLite2-City.tar.gz;

# COPY: 从宿主机复制文件或目录，并将它们添加到容器的文件系统中
COPY ./requirements.txt ./requirements.txt
RUN chmod 777 .

RUN python3 -m pip install pip
RUN ln -sf /usr/bin/python3.11 /usr/bin/python3
RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11

# 设置 pip 源，安装 requirements.txt 中依赖库
RUN python3 -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pip -U \
    && python3 -m pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    && python3 -m pip install -r requirements.txt;

# mac 环境部署需要
RUN python3 -m pip install setuptools wheel
RUN python3 -m pip install cffi==1.15.1 cryptography~=40.0.2


# 第三阶段构建: 构建的最后一个阶段会作为最终镜像保存下来，丢弃其他阶段的镜像
# 使用 docker build 命令中的 --rm=false 可以保存其他阶段的镜像。
# 以第一阶段的镜像作为基础镜像
FROM base AS final
WORKDIR /coinex_backend

# 从第二阶段的镜像中复制安装完成的 python 第三方依赖库到本阶段
COPY --from=build /usr/local /usr/local
# 从第二阶段的镜像中复制下载的文件到本阶段
COPY --from=build /coinex_backend /coinex_backend

# EXPOSE: 声明打算暴露的端口
# 在 docker run 命令中使用 -P 参数会映射到宿主机的随机端口
EXPOSE 5000

RUN ln -sf /usr/bin/python3.11 /usr/bin/python3
# CMD: 定义基于此映像启动容器后运行的默认程序。只能存在一个 CMD，当存在多个时，只考虑最后一个 CMD 命令。
# host需要绑定在 0.0.0.0 ，localhost 不能跨容器访问
CMD ["/usr/local/bin/flask", "run", "-h", "0.0.0.0"]







