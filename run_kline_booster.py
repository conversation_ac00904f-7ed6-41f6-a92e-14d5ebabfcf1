import gevent.monkey; gevent.monkey.patch_all()

import sys
from app import create_app


def main():
    from app.consumer.kline import run_spot_booster, run_perpetual_booster

    biz = ""
    if len(sys.argv) == 2:
        biz = sys.argv[1]
    if biz == "spot":
        run_spot_booster()
    elif biz == "perpetual":
        run_perpetual_booster()
    else:
        print("Usage: python run_kline_booster.py [spot|perpetual]")


if __name__ == '__main__':

    app = create_app()
    with app.app_context():
        main()
