# -*- coding: utf-8 -*-

# noinspection PyPep8
# import gevent.monkey; gevent.monkey.patch_all() move to patch_celery.py
from billiard.pool import Pool, ResultHandler, RUN, CoroStop
# noinspection PyUnresolvedReferences
from app import create_app, celery  # noqa: F401


class _ResultHandler(ResultHandler):
    """
    celery worker的主进程启动时会新建一条匿名管道，然后fork子进程，子进程会关闭管道的读端，保留写端。
    接着主进程启动新线程poll轮询管道的读端，这套机制用于子进程向主进程的单向通信。
    当某个子进程被系统杀死，主进程会立即fork生成一个新的子进程。因poll轮询在另外的线程执行，fork生成的新的子进程不会拥有该线程。(fork产生的子进程只会保留当前线程)
    使用gevent的monkey patch后，线程会被替换为协程，主进程的poll轮询并非在另外的线程上执行。
    因此fork新产生的子进程也会复制poll轮询状态，但子进程又会关闭管道的读端，导致poll抛出无效的文件描述错误，使子进程崩溃退出。
    子进程不应该去poll轮询管道的读端，此处替换掉默认实现，捕获该异常，使子进程的poll轮询正常退出。
    """

    def _is_fd_closed(self, error):
        return str(error).startswith('invalid file descriptor')

    def body(self):
        try:
            while self._state == RUN:
                try:
                    for _ in self._process_result(1.0):  # blocking
                        pass
                except CoroStop:
                    break
                except ValueError as e:
                    if self._is_fd_closed(e):
                        break
                    raise
        finally:
            try:
                self.finish_at_shutdown()
            except ValueError as e:
                if not self._is_fd_closed(e):
                    raise

Pool.ResultHandler = _ResultHandler

app = create_app()
