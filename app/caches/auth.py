# -*- coding: utf-8 -*-

import json
import time
from typing import Tuple, Union, Optional, List, Dict
from ..common import EmailCodeType, MobileCodeType, TwoFAType, LOGIN_TOKEN_SECURITY_DURATION
from ..utils import current_timestamp, exhaust
from ..utils.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ..models import ApiAuth
from ..config import config
from .base import <PERSON><PERSON><PERSON>, HashCache, SetCache, BytesCache, ListCache
from .shadow import DBShadowCache
from .auth_util import new_auth_token, validate_auth_token, get_signature, sha1


class UserLoginTokenCache(HashCache):

    DEFAULT_TTL = 86400 * 30
    DEFAULT_RENEW_DURATION = 86400

    def __init__(self, user_id: int):
        self._user_id = user_id
        super().__init__(f'{user_id}')

    @property
    def user_id(self) -> int:
        return self._user_id

    def add_token(self, token: str, ttl: int = DEFAULT_TTL):
        self._clear_outdated()
        self.hset(token, str(current_timestamp() + ttl))
        _LoginTokenOwnerCache(token).set_user(self._user_id, ttl)
        _LoginTokenRenewCache(token).set_renewed(self.DEFAULT_RENEW_DURATION)

    def del_token(self, token: str):
        self.hdel(token)
        _LoginTokenOwnerCache(token).delete()
        _LoginTokenRenewCache(token).delete()

    def clear_tokens(self, exclude_tokens: List = None) -> List[str]:
        """ exclude_tokens: 用于保留当前登入态的token """
        tokens = list(self.value)
        del_tokens = []
        for token in tokens:
            if exclude_tokens and token in exclude_tokens:
                continue
            self.del_token(token)
            del_tokens.append(token)
        return del_tokens

    @classmethod
    def from_token(cls, token: str) -> Optional['UserLoginTokenCache']:
        user_id = _LoginTokenOwnerCache(token).get_user()
        if user_id is None:
            return None
        cache = cls(user_id)
        expires_at = cache.hget(token)
        if not expires_at or float(expires_at) < current_timestamp():
            return None
        return cache

    def _clear_outdated(self):
        now = current_timestamp()
        exhaust(map(self.hdel,
                    [k for k, v in self.value.items() if float(v) < now]))

    def renew(self, token: str) -> int:
        if _LoginTokenRenewCache(token).set_renewed(
            self.DEFAULT_RENEW_DURATION) is not True:
            return 0
        if not (expires_at := self.hget(token)):
            return 0
        expires_at = current_timestamp() + self.DEFAULT_TTL
        self.hset(token, str(expires_at))
        _LoginTokenOwnerCache(token).expireat(int(expires_at))
        return int(expires_at)


class _LoginTokenOwnerCache(StringCache):

    def __init__(self, token: str):
        super().__init__(token)

    def get_user(self) -> Optional[int]:
        value = self.value
        return int(value) if value else None

    def set_user(self, user_id: int, ttl: int):
        self.set(str(user_id), ex=ttl)


class _LoginTokenRenewCache(StringCache):

    def __init__(self, token: str):
        super().__init__(token)

    def set_renewed(self, duration: int) -> Optional[bool]:
        return self.set('', ex=duration, nx=True)


class UserLoginTokenAccessCache(StringCache):
    """ token访问状态的缓存 """
    def __init__(self, token: str):
        super().__init__(token)

    def access(self):
        self.set('1', ex=LOGIN_TOKEN_SECURITY_DURATION)


class UserOperationTokenCache(StringCache):

    ttl = 30 * 60

    def __init__(self, token: str):
        super().__init__(token)

    def get_user(self) -> Optional[int]:
        value = self.value
        if value is None:
            return None
        user_id = value.split(':')[0]
        return int(user_id)

    def get_user_with_2fa_type(self) -> Tuple[Optional[int], Optional[TwoFAType]]:
        value = self.value
        if value is None:
            return None, None
        user_id, tfa_type = value.split(':')
        return int(user_id), TwoFAType(tfa_type)

    def set_user(self, user_id: int, tfa_type: TwoFAType):
        v = f'{user_id}:{tfa_type.value}'
        self.set(v, ex=self.ttl)


class UserLoginOperationTokenCache(StringCache):

    ttl = 600

    def __init__(self, token: str):
        super().__init__(token)
    
    def get_user(self) -> Optional[int]:
        value = self.value
        return int(value) if value else None

    def set_user(self, user_id: int):
        self.set(str(user_id), ex=self.ttl)


class UserLoginVerifyCache(StringCache):

    def __init__(self, token: str):
        super().__init__(token)

    def get_data(self) -> Optional[dict]:
        value = self.value
        return json.loads(value) if value else None

    def set_data(self, data: dict, ttl: int = 60 * 30):
        self.set(json.dumps(data), ex=ttl)


class UserLoginQRCodeCache(StringCache):

    ttl = 180
    
    def __init__(self, token: str):
        super().__init__(token)
    
    def get_data(self) -> Optional[dict]:
        value = self.value
        return json.loads(value) if value else None

    def set_data(self, data: dict):
        self.set(json.dumps(data), ex=self.ttl)


class UserLoginQRCodeOperateTokenCache(StringCache):

    ttl = 180

    def __init__(self, token: str):
        super().__init__(token)
    
    def set_data(self, data: str):
        self.set(data, ex=self.ttl)

class ThirdPartyAccountOperationTokenCache(StringCache):

    ttl = 300

    def __init__(self, token: str):
        super().__init__(token)

class ThirdPartyAccountAuthTokenCache(StringCache):

    ttl = 300

    def __init__(self, name: str, token: str):
        super().__init__(f'{name.lower()}:{token}')

class WebAuthnChallengeCache(BytesCache):

    ttl = 300

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def set_data(self, challenge: bytes, host_url: str):
        data = b''.join((len(challenge).to_bytes(1, 'big'), challenge, host_url.encode()))
        self.set(data, ex=self.ttl)

    def get_data(self) -> Tuple[Optional[bytes], Optional[str]]:
        r = self.read()
        if not r:
            return None, None
        i = r[0] + 1
        challenge = r[1:i]
        host_url = r[i:]
        return challenge, host_url.decode()


class EmailCodeCache(HashCache):

    ttl = 600

    def __init__(self, email: str, code_type: EmailCodeType):
        pk = f'{email}:{code_type.value}'
        super().__init__(pk)

    def set_code(self, code: str):
        ex = current_timestamp(to_int=True) + self.ttl
        self.hset(code, ex)
        self.expireat(ex)

    def verify_code(self, code: str) -> bool:
        now = current_timestamp(to_int=True)
        codes = self.hgetall()
        for _code, ex in codes.items():
            if int(ex) < now:
                continue
            if _code == code:
                return True
        return False


class OauthClientCodeUserCache(StringCache):
    ttl = 300

    def __init__(self, code: str, client: str):
        pk = f'{client}:{code}'
        super().__init__(pk)


class MobileCodeCache(HashCache):

    ttl = 600

    def __init__(self, full_mobile: str, code_type: MobileCodeType):
        pk = f'{full_mobile}:{code_type.value}'
        super().__init__(pk)

    def set_code(self, code: str):
        ex = current_timestamp(to_int=True) + self.ttl
        self.hset(code, ex)
        self.expireat(ex)

    def verify_code(self, code: str) -> bool:
        if config['BYPASS_2FA']:
            return True
        now = current_timestamp(to_int=True)
        codes = self.hgetall()
        for _code, ex in codes.items():
            if int(ex) < now:
                continue
            if _code == code:
                return True
        return False


class EmailCodeTokenCache(StringCache):

    def __init__(self, token: str):
        super().__init__(token)

    def get_user(self) -> Optional[int]:
        return self.get_user_and_email()[0]

    def get_email(self) -> Optional[str]:
        return self.get_user_and_email()[1]

    def get_user_and_email(self) -> Tuple[Optional[int], Optional[str]]:
        if not (value := self.value):
            return None, None
        user_id, email = value.split(':', 1)
        return int(user_id), email

    def set_user_and_email(self, user_id: int, email: str, ttl: int = 1800):
        self.set(f'{user_id}:{email}', ex=ttl)


class OauthCodeTokenCache(StringCache):

    def __init__(self, token: str):
        super().__init__(token)

    def get_user(self) -> Optional[int]:
        return self.get_user_and_client()[0]

    def get_client(self) -> Optional[str]:
        return self.get_user_and_client()[1]

    def get_user_and_client(self) -> Tuple[Optional[int], Optional[str]]:
        if not (value := self.value):
            return None, None
        user_id, client_id = value.split(':', 1)
        return int(user_id), client_id

    def set_user_and_client_id(self, user_id: int, client_id: str, ttl: int = 86400*3):
        self.set(f'{user_id}:{client_id}', ex=ttl)


class TotpRecentAuthCache(StringCache):

    def __init__(self, auth_key: str):
        super().__init__(auth_key)

    def set_code(self, code: str, ttl: int = 60 * 30):
        self.set(code, ex=ttl)

    def is_code_recently_used(self, code: str) -> bool:
        return self.value == code


class TotpKeyCache(StringCache):

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def set_key(self, totp_auth_key: str, ttl: int = 60 * 30):
        self.set(totp_auth_key, ex=ttl)


class GeetestCache(StringCache):

    def __init__(self, challenge: str):
        super().__init__(challenge)

    def get_data(self) -> Optional[Tuple[Union[int, str], int]]:
        value = self.value
        if not value:
            return None
        user_id, status = value.split(':')
        if user_id.isdigit():
            user_id = int(user_id)
        return user_id, int(status)

    def set_data(self, user_id: Union[int, str], status: int):
        self.set(f'{user_id}:{status}', ex=60 * 60)


class AdminUserPermissionCache(StringCache):

    def __init__(self, user_id: int):
        self.user_id = user_id
        super().__init__(str(user_id))

    def reload(self):
        from app.business.auth import update_permission_cache

        update_permission_cache([self.user_id])

    def read_aside(self) -> list:
        if not self.value:
            self.reload()

        if self.value:
            return json.loads(self.value)
        return []


class ApiAuthCache(DBShadowCache):

    model = ApiAuth
    pk_fields = 'access_id',
    ttl = 86400

    def __init__(self, access_id: str):
        self._access_id = access_id
        super().__init__(access_id)

    @classmethod
    def delete_by_user(cls, user_id: int):
        for access_id, in ApiAuth.query \
                .filter(ApiAuth.user_id == user_id) \
                .with_entities(ApiAuth.access_id):
            cls(access_id).delete()


class SmsAuthCache(SetCache):
    """缓存最近使用过的token，防止重放"""

    window = 120
    ttl = window * 3 # 至少2倍

    def __init__(self, timestamp: int):
        super().__init__(str(timestamp))
    
    @classmethod
    def new_auth_token(cls) -> str:
        return new_auth_token()
    
    @classmethod
    def validate_auth_token(cls, auth_token: str) -> bool:
        t = validate_auth_token(auth_token)
        if t < 0:
            return False
        now = int(time.time())
        if abs(now - t) > cls.window:
            return False
        t -= t % cls.window
        cache = cls(t)
        if cache.sismember(auth_token):
            return False
        cache.sadd(auth_token)
        cache.expireat(t + cls.ttl)
        return True

    @classmethod
    def self_check_signature(cls, signature: str) -> bool:
        """校验signature的大小写是否正确，参考ETH地址"""
        h = sha1(bytes.fromhex(signature)).hex()
        if len(h) != len(signature):
            raise ValueError
        s = ''
        for i in range(len(signature)):
            if int(h[i], 16) >= 8:
                s += signature[i].upper()
            else:
                s += signature[i].lower()
        return s == signature

    @classmethod
    def get_signature(cls, auth_token: str, code_type: str) -> str:
        return get_signature(auth_token, code_type)

    @classmethod
    def validate_signature(cls, auth_token: str, code_type: str, signature: str) -> bool:
        return cls.validate_auth_token(auth_token) and \
            signature.lower() == get_signature(auth_token, code_type)


class UserLoginTokenDeleteCache(ListCache):
    """
    用户Token待删除队列
    """
    MAX_LENGTH = 100000  # 队列长度限制

    def __init__(self):
        super().__init__(None)

    @classmethod
    def encode(
            cls, token: str) -> bytes:
        data = {
            "token": token,
        }
        return json.dumps(data, cls=JsonEncoder).encode("utf-8")

    @classmethod
    def decode(cls, value: bytes) -> Dict:
        data = json.loads(value.decode("utf-8"))
        return data

    def add(self, token: str):
        if not token:
            return

        self.rpush(self.encode(token))
        self.ltrim(-self.MAX_LENGTH, -1)

    def lrange_all(self) -> List[Dict]:
        start, n = 0, 1000
        data = []
        while True:
            item = self.lrange_raw(start, start + n - 1)
            for value in item:
                try:
                    data.append(self.decode(value))
                except ValueError:
                    continue
            if len(item) < n:
                return data
            start += 1000

    def remove(self, n: int) -> bool:
        """裁剪掉列表中的前n项"""
        return self.ltrim(n, -1)
