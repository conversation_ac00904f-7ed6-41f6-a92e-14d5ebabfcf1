# -*- coding: utf-8 -*-
import json
from enum import Enum
from decimal import Decimal
from datetime import datetime
from typing import List, Union, Optional

from redis.crc import key_slot

from ..utils import current_timestamp, batch_iter
from .base import Hash<PERSON>ache, HyperLogLogCache, SortedSetCache, StringCache


class TimedHashCache(HashCache):
    default_interval: int = 3600

    def __init__(self, pk: str, *, interval: int = None):
        super().__init__(pk)
        self._interval = interval or self.default_interval

    def count(self) -> int:
        self._clear_outdated()
        return self.hlen()

    def list_values(self) -> List[str]:
        self._clear_outdated()
        return list(self.value.values())

    def add_value(self, value: str = '',
                  date: Union[float, Decimal, datetime] = None) -> str:
        self._clear_outdated()
        return self._add_value(value, date)

    def add_value_if_fewer_than(self, count: int, value: str = '',
                                time: Union[float, int, datetime] = None
                                ) -> bool:
        if self.count() >= count:
            return False
        self._add_value(value, time)
        return True

    def _add_value(self, value, time):
        if time is None:
            time = current_timestamp()
        elif isinstance(time, datetime):
            time = time.timestamp()

        self.hset(str(time), value)
        self.expire(self._interval)

    def _clear_outdated(self):
        start = current_timestamp() - self._interval
        keys = [x for x in self.hkeys() if float(x) < start]
        if keys:
            self.hdel(*keys)


class FrequencyCache(TimedHashCache):

    def __init__(self, key: str, interval: int):
        super().__init__(key, interval=interval)


class LoginFailureCache(TimedHashCache):

    def __init__(self, account: str, *, ttl: int = None):
        super().__init__(account, interval=ttl)


class ResetWithdrawPasswordFailureCache(TimedHashCache):

    def __init__(self, user_id: str, *, ttl: int = None):
        super().__init__(user_id, interval=ttl)


class ResetTradePasswordFailureCache(TimedHashCache):

    def __init__(self, user_id: str, *, ttl: int = None):
        super().__init__(user_id, interval=ttl)

class ValidateTradePasswordFrequencyCache(StringCache):

    TTL = 30 * 60

    def __init__(self, user_id: str):
        super().__init__(user_id)

class CBoxCodeFailureCache(TimedHashCache):

    def __init__(self, user_id: int, ttl: int = None):
        super().__init__(str(user_id), interval=ttl)


class EmailSendCountCache(HashCache):
    """ 邮件发送次数缓存，每5分钟最多只能发送100封邮件 """

    TTL = 3600 * 48
    COUNT = 100
    INTERVAL = 300

    def __init__(self, ts: Optional[int] = None):
        if not ts:
            ts = current_timestamp(to_int=True)
        ts = ts - ts % self.INTERVAL
        super().__init__(str(ts))

    def hincrby_if_fewer_than(self, email: str) -> bool:
        cur_count = int(self.hget(email) or 0)
        if cur_count >= self.COUNT:
            return False

        self.hincrby(email, 1)
        self.expire(self.TTL)
        return True


class EmailCountryCountCache(HyperLogLogCache):

    def __init__(self, country_code: str, ts: int):
        super().__init__(f'{country_code}:{str(ts)}')


class SmsSendHourCountCache(HashCache):
    """ 短信发送次数缓存(按小时记录, 用于每小时检查最近24H的短信告警) """

    TTL = 3600 * 48
    INTERVAL = 3600  # 按小时

    def __init__(self, ts: Optional[int] = None):
        if not ts:
            ts = current_timestamp(to_int=True)
        ts = ts - ts % self.INTERVAL
        super().__init__(str(ts))

    def incr(self, country_code: int, mobile: str) -> bool:
        key_ = f"{country_code}:{mobile}"
        self.hincrby(key_, 1)
        self.expire(self.TTL)
        return True


class SmsCountryCountCache(HyperLogLogCache):
    """ 短信发送人数缓存（按月记录，用于统计各国家每月短信的发送人数） """

    def __init__(self, country_code: str, ts: int):
        super().__init__(f'{country_code}:{str(ts)}')

class SmsCountryResultCache(HashCache):
    """ 短信发送结果缓存 (按15分钟记录, 用于判断渠道是否可用及告警)"""

    TTL = 3600 * 48
    INTERVAL = 60 * 15

    class Type:
        SUCCESS = 'success'
        FAIL = 'fail'

    def __init__(self, provider: str, ts: int = None):
        if not ts:
            ts = current_timestamp(to_int=True)
            ts = ts - ts % self.INTERVAL
        super().__init__(f'{provider}:{str(ts)}')

    def set_success(self, country_code: int):
        self.hincrby(f'{country_code}:{self.Type.SUCCESS}', 1)
    
    def set_fail(self, country_code: int):
        self.hincrby(f'{country_code}:{self.Type.FAIL}', 1)


class SmsCountryResultSetCache(HyperLogLogCache):
    """ 短信发送人数缓存（默认按15分钟记录，可以指定按日或月记录，用于统计每日、月有多少用户，用于统计各渠道短信的发送人数） """

    INTERVAL = 15 * 60 # 15min
    TTL_MAP = {
        'quarter': 3600 * 48,
        'daily': 3600 * 24 * 45,
        'monthly': 3600 * 24 * 7,
    }

    class Type:
        QUARTER = 'quarter'
        DAILY = 'daily'
        MONTHLY = 'monthly'

    def __init__(self, provider: str, ts: int = 0, _type: str = Type.QUARTER):
        if not ts:
            ts = current_timestamp(to_int=True)
            ts = ts - ts % self.INTERVAL
        self._type = _type
        super().__init__(f'{{{provider}}}:{_type}:{str(ts)}')

    def add(self, mobile: str) -> int:
        ttl = self.TTL_MAP.get(self._type, 3600 * 48)
        self.expire(ttl)
        return self.pfadd(mobile)

    def merge(self, keys: List[str]):
        self.expire(self.TTL_MAP.get(self._type, 3600 * 48))
        
        cur_slot = self._extract_slot(self._key)
        tmp_key = self._key + ':tmp'
        
        # 分组处理：slot 一致的 key 先收集，最后统一 merge
        same_slot_keys = []
        for key in keys:
            target_slot = self._extract_slot(key)
            if cur_slot == target_slot:
                same_slot_keys.append(key)
            else:
                # 对于不同 slot 的 key，单独处理
                self.redis.delete(tmp_key)
                value = self.redis.dump(key)
                if value:
                    self.redis.restore(tmp_key, 5 * 60, value)
                    self.pfmerge(tmp_key)
        
        # 统一 merge 相同 slot 的 keys
        if same_slot_keys:
            self.pfmerge(*same_slot_keys)
        return

    def _extract_slot(self, key: str) -> str:
        """直接使用redis的源码计算当前的slot
        """
        key = key.encode("utf-8")
        return key_slot(key)


class SmsSendControlCache(TimedHashCache):

    def __init__(self, key: str, interval: int):
        super().__init__(key, interval=interval)


class SmsBlackListCache(SortedSetCache):
    TTL = 86400 * 3

    def __init__(self, key: str):
        super().__init__(key)

    def add(self, value: str):
        n = current_timestamp(to_int=True)
        self.zadd({value: n}, nx=True)
        self.expire(self.TTL)
        self.zrem_range_by_score(0, n - self.TTL)

    def has(self, value: str) -> bool:
        v = self.zscore(value)
        if v is None:
            return False
        return current_timestamp(to_int=True) - v < self.TTL


class SmsUserCache(StringCache):

    TTL = 300

    def __init__(self, full_mobile_num: str):
        super().__init__(f"+{full_mobile_num}")

class SmsSendResultCache(StringCache):

    TTL = 120

    def __init__(self, full_mobile_num: str, message_id: str):
        super().__init__(f"+{full_mobile_num}:{message_id}")
    


class MutilTimedHashCache(HashCache):
    """
     批量限频缓存
     eg：
     {
        "user_id1": [timestamp1, timestamp2, ...],
     }
    """
    batch_limit = 1000
    default_interval: int = 86400

    def __init__(self, pk: str, *, interval: int = None):
        super().__init__(pk)
        self._interval = interval or self.default_interval

    def batch_set_stamps(self, bus_ids: List[str], timestamp: float):
        old_data = dict(self.hmget_with_keys(bus_ids))
        start = timestamp - self._interval
        new_data = {}
        for k, v in old_data.items():
            stamps = json.loads(v)
            stamps = self._filter_valid_stamps(start, stamps)
            stamps.append(timestamp)
            new_data[k] = json.dumps(stamps)
        new_ids = set(bus_ids) - set(old_data.keys())
        new_data.update({k: json.dumps([timestamp]) for k in new_ids})
        self.hmset(new_data)

    def batch_set(self, data):
        self.hmset({k: json.dumps(v) for k, v in data.items()})

    @staticmethod
    def _filter_valid_stamps(start_stamp, stamps):
        return [i for i in stamps if i >= start_stamp]

    def _handle_batch_data(self, batch_data):
        ret = {}
        start = current_timestamp() - self._interval
        for k, v in batch_data.items():
            stamps = json.loads(v)
            stamps = self._filter_valid_stamps(start, stamps)
            ret[k] = stamps
        return ret

    def yield_batch_data(self):
        keys = self.hkeys()
        for batch_key in batch_iter(keys, self.batch_limit):
            batch_data = dict(self.hmget_with_keys(batch_key))
            tmp_dict = self._handle_batch_data(batch_data)
            yield tmp_dict


class MonitorControlCache(TimedHashCache):
    default_interval: int = 3600 * 24
    default_limit: int = 1

    def __init__(self, asset: str, *, ttl: int = None, limit: int = None):
        self.limit = limit or self.default_limit
        super().__init__(asset, interval=ttl or self.default_interval)

    def can_monitor(self):
        return self.add_value_if_fewer_than(self.limit)


class MonitorControlACache(MonitorControlCache):

    def __init__(self, asset: str, *, ttl: int = None, limit: int = None):
        super().__init__(asset, ttl=ttl, limit=limit)


class MonitorControlBCache(MonitorControlCache):

    def __init__(self, asset: str, *, ttl: int = None, limit: int = None):
        super().__init__(asset, ttl=ttl, limit=limit)


class MonitorControlBusOnlineCoinRiskByMarketCache(MonitorControlCache):

    def __init__(self, market: str, *, ttl: int = None, limit: int = None):
        super().__init__(market, ttl=ttl, limit=limit)


class AuditWithdrawalsBlockedMonitorCache(HashCache):
    """因资产不平风控导致的币种提现卡待审核监控提醒记录缓存"""

    INTERVAL = 3600 * 24

    class NoticeType(Enum):
        SLACK = 'slack'
        MOBILE = 'mobile'

    def __init__(self, asset: str, notice_type: NoticeType):
        super().__init__(f'{asset}:{notice_type.value}')

    def _clear_outdated(self):
        start = current_timestamp() - self.INTERVAL
        keys = [x for x in self.hkeys() if float(x) < start]
        if keys:
            self.hdel(*keys)

    def get_ts(self, ts: Optional[int] = None) -> str:
        if not ts:
            ts = current_timestamp(to_int=True)
        ts = ts - ts % self.INTERVAL
        return str(ts)

    def get(self, ts: Optional[int] = None) -> int:
        self._clear_outdated()
        data = self.hget(self.get_ts(ts))
        if data:
            return int(data)
        return 0

    def set(self, number: int, ts: Optional[int] = None):
        self._clear_outdated()
        self.hset(self.get_ts(ts), str(number))


class SmsSendCountAbnormalCache(HashCache):
    """短信发送异常监控提醒记录缓存"""

    INTERVAL = 3600 * 24

    def __init__(self, country: str):
        super().__init__(country)

    def _clear_outdated(self):
        start = current_timestamp() - self.INTERVAL
        keys = [x for x in self.hkeys() if float(x) < start]
        if keys:
            self.hdel(*keys)

    def get_ts(self, ts: Optional[int] = None) -> str:
        if not ts:
            ts = current_timestamp(to_int=True)
        ts = ts - ts % self.INTERVAL
        return str(ts)

    def get(self, ts: Optional[int] = None) -> int:
        self._clear_outdated()
        data = self.hget(self.get_ts(ts))
        if data:
            return int(data)
        return 0

    def set(self, number: int, ts: Optional[int] = None):
        self._clear_outdated()
        self.hset(self.get_ts(ts), str(number))


class SmsSendFailAlertCache(StringCache):

    def __init__(self, country_code: int):
        super().__init__(str(country_code))
