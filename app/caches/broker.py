# -*- coding: utf-8 -*-
from . import TimedHashCache, StringCache


class BrokerReportExportLimitCache(TimedHashCache):
    # 经纪商导出限频
    default_interval: int = 86400
    count_limit = 10

    def __init__(self, key: str, interval: int = None):
        super().__init__(key, interval=interval)


class BrokerReportLastUpdateTimeCache(StringCache):
    # 经纪商报表上次更新时间

    def __init__(self):
        super().__init__(None)
