# -*- coding: utf-8 -*-
import json
from decimal import Decimal
from typing import Dict, Iterable

from flask import current_app

from .base import Hash<PERSON>ache, SetCache, StringCache
from .flow_control import TimedHashCache
from ..common import FIXED_ASSET_PRICES

from ..utils import current_timestamp, quantize_amount
from ..utils.parser import JsonEncoder


class _BasePricesCache(HashCache):

    def __init__(self):
        super().__init__(None)

    def get_price(self, asset: str) -> Decimal:
        return Decimal(self.value.get(asset, 0))

    def get_prices(self, assets: Iterable[str] = None) -> Dict[str, Decimal]:
        values: Dict[str, str] = self.value
        if assets is None:
            assets = values
        return {asset: Decimal(values.get(asset, 0)) for asset in assets}


class AssetUSDPricesCache(_BasePricesCache):
    pass


class AssetUSDPriceHistoryCache(TimedHashCache):

    def __init__(self, asset: str):
        super().__init__(asset, interval=3600)


class FiatUSDPricesCache(_BasePricesCache):
    pass


class InvisibleAssetsCache(SetCache):

    def __init__(self):
        super().__init__(None)


class SpotMarketRealTimePriceStampCache(StringCache):
    """
    实时市场价格时间戳，涨跌幅
    """

    def __init__(self):
        super().__init__(None)


class MarketRealTimePriceMixin:
    TimeCache = None
    TTL = 30

    @classmethod
    def check_stamp(cls):
        time_stamp = cls.TimeCache().get()
        # 30 s内没更新返回 False
        if time_stamp and current_timestamp() - float(time_stamp) < cls.TTL:
            return True
        current_app.logger.warning("实时市场价格缓存未更新")
        return False


class SpotMarketRealTimePriceCache(HashCache, MarketRealTimePriceMixin):
    """
    实时市场价格，涨跌幅
    """
    TimeCache = SpotMarketRealTimePriceStampCache

    def __init__(self):
        super().__init__(None)

    def read(self) -> Dict[str, tuple[Decimal, Decimal]] | Dict:
        ret = dict()
        for k, v in super().read().items():
            ret[k] = tuple(Decimal(i) for i in json.loads(v))
        return ret


class PerpetualMarketRealTimePriceStampCache(StringCache):
    """
    实时合约市场价格时间戳，涨跌幅
    """

    def __init__(self):
        super().__init__(None)


class PerpetualMarketRealTimePriceCache(HashCache, MarketRealTimePriceMixin):
    """
    实时合约市场价格，涨跌幅
    """
    TimeCache = PerpetualMarketRealTimePriceStampCache

    def __init__(self):
        super().__init__(None)


class AssetRealTimePriceCache(HashCache):
    """
    实时资产价格
    """
    _DEFAULT_PRECISION = 12

    def __init__(self):
        super().__init__(None)

    def get_price(self, asset: str) -> Decimal | None:
        if (fixed := FIXED_ASSET_PRICES.get(asset)) is not None:
            return fixed
        if SpotMarketRealTimePriceCache.check_stamp():
            if val := self.hget(asset):
                return self._quantize(Decimal(val))
        return None

    def get_prices(self, assets: Iterable[str] = None) -> Dict[str, Decimal]:
        if SpotMarketRealTimePriceCache.check_stamp():
            values: Dict[str, str] = self.value
            if assets is None:
                assets = values
            return {k: self._quantize(Decimal(v)) for k, v in values.items() if k in assets}
        else:
            return dict()

    def _quantize(self, value: Decimal) -> Decimal:
        return quantize_amount(value, self._DEFAULT_PRECISION)

    def get_asset_real_time_prices(self, assets=None):
        from app.business import PriceManager
        prices = self.get_prices(assets)
        if assets:
            other_assets = set(assets) - set(prices.keys())
            if other_assets:
                current_app.logger.info(f"实时价格缓存缺少币种 {other_assets}")
                prices.update(PriceManager.assets_to_usd(other_assets))
        else:
            old_prices = PriceManager.assets_to_usd()
            lack_set = set(old_prices) - set(prices)
            if lack_set:
                current_app.logger.info(f"实时价格缓存缺少币种 {lack_set}")
            prices.update({k: v for k, v in old_prices.items() if k in lack_set})
        return prices

    def get_asset_real_time_price(self, asset):
        from app.business import PriceManager
        price = self.get_price(asset)
        if price is None:
            current_app.logger.info(f"实时价格缓存缺少币种 {asset}")
            price = PriceManager.asset_to_usd(asset)
        return price

    def get_asset_by_sort_prices(self, reverse=False):
        prices = self.get_asset_real_time_prices()
        data = sorted(prices.items(), key=lambda x: Decimal(x[1]), reverse=reverse)
        return [i[0] for i in data]


class AssetRealTimeRateCache(HashCache):
    """
        实时资产涨跌幅
    """

    def __init__(self):
        super().__init__(None)

    def get_rate(self, asset: str) -> Decimal | None:
        if SpotMarketRealTimePriceCache.check_stamp():
            if val := self.hget(asset):
                return Decimal(val)
        return None

    def get_rates(self, assets: Iterable[str] = None) -> Dict[str, Decimal]:
        if SpotMarketRealTimePriceCache.check_stamp():
            values: Dict[str, str] = self.value
            if assets is None:
                assets = values
            return {k: Decimal(v) for k, v in values.items() if k in assets}
        else:
            return {}

    def get_asset_by_sort_rates(self, reverse=False):
        prices = self.get_asset_real_time_rates()
        data = sorted(prices.items(), key=lambda x: Decimal(x[1]), reverse=reverse)
        return [i[0] for i in data]

    def get_asset_real_time_rates(self, assets=None):
        from app.caches.kline import AssetRecentChangeRateCache
        old_rate_cache = AssetRecentChangeRateCache()
        rates = self.get_rates(assets)
        if assets:
            other_assets = set(assets) - set(rates.keys())
            if other_assets:
                current_app.logger.info(f"实时涨跌幅缓存缺少币种 {other_assets}")
                rates.update(old_rate_cache.get_rates(list(other_assets)))
        else:
            old_rates = old_rate_cache.get_rates()
            lack_set = set(old_rates) - set(rates)
            if lack_set:
                current_app.logger.info(f"实时涨跌幅缓存缺少币种 {lack_set}")
            rates.update({k: v for k, v in old_rates.items() if k not in rates})
        return rates

    def get_asset_real_time_rate(self, asset):
        from app.caches.kline import AssetRecentChangeRateCache
        rate = self.get_rate(asset)
        if rate is None:
            current_app.logger.info(f"实时涨跌幅缓存缺少币种 {asset}")
            rate = AssetRecentChangeRateCache().get_rate(asset)
        return rate


class CMCAssetRateInfoCache(HashCache):
    """ CMC币种USD汇率信息
    币种asset 可能会匹配到多个
    """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        from app.business.clients.quotes import CMCAPIClient

        asset_rates_map = CMCAPIClient().get_asset_usd_prices()
        data = {str(k): json.dumps(v, cls=JsonEncoder) for k, v in asset_rates_map.items()}
        cls().save(data)

    def get_single_asset_price(self, asset: str):
        """ 仅匹配一个时，返回价格 """
        data = self.hget(asset)
        if data:
            rates = json.loads(data)
            if len(rates) == 1:
                return Decimal(rates[0]['usd_price'])
        return None
