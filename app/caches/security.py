# -*- coding: utf-8 -*-
from enum import IntEnum
from typing import <PERSON><PERSON>, Optional

from app.caches import String<PERSON><PERSON>, HashCache, TimedHashCache


class SecurityQuestionCache(StringCache):
    """
    安全问题缓存
    """
    def __init__(self, user_id: int):
        super().__init__(str(user_id))


class SecurityOperationCache(HashCache):
    """
    用户重置方式/token缓存
    {
        "user_id": "123",
        "reset_method": "ANSWER" / "CUSTOMER"
        "balance_usd": "123.45",
        "flow_case": "NORMAL" / "PASSWORD_FORGET" / "TWO_FA_FORGET"
    }
    """
    def __init__(self, token: str):
        super().__init__(token)

    def get_user_info(self) -> Tuple[Optional[int], Optional[str]]:
        res = self.read()
        if not res:
            return None, None
        reset_method = res.get('reset_method')
        return int(res['user_id']), reset_method

    def set_reset_method(self, reset_method: str):
        return self.hset('reset_method', reset_method)

    def expire(self, seconds: int = 1800):
        super().expire(seconds)


class SecurityStatisticsCache(StringCache):
    
    class TimeRange(IntEnum):
        ONE_DAY = 1
        SEVEN_DAYS = 7
        THIRTY_DAYS = 30
        NINETY_DAYS = 90

    def __init__(self, time_range: TimeRange):
        super().__init__(f'{time_range}')


class SecurityResetFrequencyCache(TimedHashCache):

    default_interval: int = 86400
    count_limit = 5

    def __init__(self, user_id: int):
        super().__init__(f'{user_id}')


class UnfreezeAccountFrequencyCache(TimedHashCache):

    default_interval: int = 86400
    count_limit = 5

    def __init__(self, user_id: int):
        super().__init__(f'{user_id}')
