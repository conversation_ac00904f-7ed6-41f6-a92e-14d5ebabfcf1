# -*- coding: utf-8 -*-
from typing import List

from app.caches.base import Hash<PERSON>ache, StringCache
from app.caches import TimedHashCache


class RefinitivCountryCache(HashCache):
    """ 路孚特 支持的country { "CHN": "China" } """
    def __init__(self):
        super().__init__(None)


class RefinitivNationalityCache(HashCache):
    """ 路孚特 支持的nationality """
    def __init__(self):
        super().__init__(None)


class DowjonesCountryCache(HashCache):
    """ 道琼斯 支持的country { "CHN": "China" } """
    def __init__(self):
        super().__init__(None)


class DowjonesAccessTokenCache(StringCache):

    def __init__(self):
        super().__init__(None)


class RiskScreenAuditorsCache(TimedHashCache):
    """ 风险筛查-审核人缓存 """

    def __init__(self, case_id: int):
        super().__init__(str(case_id), interval=15)

    @property
    def auditors(self) -> List[int]:
        return list(map(int, self.list_values()))

    def add_auditor(self, user_id: int):
        self.add_value(str(user_id))
