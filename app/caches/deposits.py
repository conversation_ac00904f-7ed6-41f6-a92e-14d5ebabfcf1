# -*- coding: utf-8 -*-

import json
from collections import defaultdict
from itertools import chain as chain_iter
from typing import Dict, List

from flask_babel import gettext

from .base import Hash<PERSON>ache, StringCache, SetCache
from ..assets import get_asset_chain_config, asset_to_chains
from ..common import Language, DepositWithdrawalDisableReason
from ..models.wallet import (
    DepositWithdrawalPopupWindow,
    DepositWithdrawalPopupWindowContent,
)
from ..models.system import AppJumpList
from ..utils import now
from ..utils.parser import JsonEncoder


def _get_jump_mapping(jump_ids):
    jump_mapping = {}
    if jump_ids:
        jump_rows = AppJumpList.query.with_entities(
            AppJumpList.id,
            AppJumpList.jump_data,
        ).filter(
            AppJumpList.id.in_(jump_ids)
        ).all()
        for jump_row in jump_rows:
            jump_mapping[jump_row.id] = jump_row.jump_data

    return jump_mapping


def build_deposit_withdrawal_data(rows: List[DepositWithdrawalPopupWindow]):
    model = DepositWithdrawalPopupWindow
    content_model = DepositWithdrawalPopupWindowContent
    window_dict = defaultdict(list)
    jump_ids = []
    for row in rows:
        window_dict[row.platform].append(row)
        if row.jump_page_enabled:
            jump_ids.append(row.jump_id)
    jump_mapping = _get_jump_mapping(jump_ids)

    contents = content_model.query.filter(
        content_model.popup_window_id.in_([x.id for x in rows])
    ).all()
    content_dict = {(x.popup_window_id, x.lang): x for x in contents}

    result = defaultdict(list)
    for platform in (model.Platform.WEB, model.Platform.APP):
        for window in chain_iter(window_dict[platform], window_dict[model.Platform.ALL]):
            for lang in content_model.AVAILABLE_LANGS:
                if not (c := content_dict.get((window.id, lang))):
                    continue
                result[(platform, lang)].append(
                    dict(
                        id=window.id,
                        asset=window.asset,
                        chain=window.chain,
                        platform=window.platform,
                        trigger_page=window.trigger_page,
                        frequency=window.frequency,
                        filter_type=window.filter_type.name,
                        offline_type=window.offline_type.name,
                        sort_id=window.sort_id,
                        title=c.title,
                        content=c.content,
                        jump_id=window.jump_id,
                        jump_page_enabled=window.jump_page_enabled,
                        jump_type=window.jump_type.name if window.jump_type else '',
                        url=jump_mapping.get(window.jump_id) or '',
                    )
                )

    return result


class _DepositWithdrawalPopupWindowCache(HashCache):

    ttl = 86400
    model = DepositWithdrawalPopupWindow
    content_model = DepositWithdrawalPopupWindowContent
    trigger_page: model.TriggerPage = None

    def __init__(self, platform: model.Platform, lang: Language):
        platform = platform.name
        lang = lang.name
        super().__init__(f"{platform}:{lang}")

    @classmethod
    def _asset_chain_pk(cls, asset: str, chain_: str) -> str:
        return f"{chain_}-{asset}"

    # noinspection DuplicatedCode
    @classmethod
    def reload(cls):
        _now = now()
        rows = cls.model.query.filter(
            cls.model.status == cls.model.Status.VALID,
            cls.model.trigger_page == cls.trigger_page,
            cls.model.started_at < _now,
            cls.model.ended_at > _now,
        ).all()

        result = build_deposit_withdrawal_data(rows)

        old_platform_lang_keys = {(x, y) for x in cls.model.Platform for y in Language}
        for platform_lang, windows in result.items():
            windows.sort(key=lambda x: x["sort_id"], reverse=True)
            old_platform_lang_keys.remove(platform_lang)
            cache = cls(*platform_lang)
            _old_keys = cache.hkeys()
            #
            asset_chain_trigger_windows_dict = defaultdict(list)
            for w in windows:
                key_ = cls._asset_chain_pk(w["asset"], w["chain"])
                asset_chain_trigger_windows_dict[key_].append(w)
            dump_data_dict = {k: json.dumps(v, cls=JsonEncoder) for k, v in asset_chain_trigger_windows_dict.items()}
            cache.hmset(dump_data_dict)
            cache.expire(cls.ttl)
            _del_keys = {x for x in _old_keys if x not in dump_data_dict}
            if _del_keys:
                cache.hdel(*_del_keys)

        for platform_lang in old_platform_lang_keys:
            cls(*platform_lang).delete()

        cls._set_up_user_cache(rows)

    @classmethod
    def _set_up_user_cache(cls, rows):
        for window in rows:
            if window.filter_type == cls.model.FilterType.FILTERS and window.users:
                users = json.loads(window.users)
                cache = DepositWithdrawalPopupWindowUsersCache(cls.trigger_page, window.id)
                cache.value = users
                cache.expire(cls.ttl)

    def get_by_asset_chain(self, asset: str, chain_: str, user_id: int = None) -> List[Dict]:
        key_ = self._asset_chain_pk(asset, chain_)
        all_asset_key_ = self._asset_chain_pk("", chain_)
        result = []
        if data_list := self.hmget([key_, all_asset_key_]):
            for data in data_list:
                if not data:
                    continue
                items = json.loads(data) or []
                for item in items:
                    trigger_page = self.model.TriggerPage(item['trigger_page'])
                    if item['filter_type'] == self.model.FilterType.NONE.name:
                        result.append(item)
                    elif user_id and DepositWithdrawalPopupWindowUsersCache(trigger_page, item['id']).sismember(user_id):
                        result.append(item)
        result.sort(key=lambda x: x["sort_id"], reverse=True)
        return result


class DepositPopupWindowCache(_DepositWithdrawalPopupWindowCache):
    """ 充值弹窗缓存 """

    trigger_page = DepositWithdrawalPopupWindow.TriggerPage.DEPOSIT


class WithdrawalPopupWindowCache(_DepositWithdrawalPopupWindowCache):
    """ 提现弹窗缓存 """

    trigger_page = DepositWithdrawalPopupWindow.TriggerPage.WITHDRAWAL


class OfflinePopupWindowCache(HashCache):
    model = DepositWithdrawalPopupWindow
    content_model = DepositWithdrawalPopupWindowContent
    ttl = 86400

    def __init__(self):
        super().__init__(None)

    @classmethod
    def unique(cls, platform, lang):
        return f"{platform.name}-{lang.name}"

    @classmethod
    def reload(cls):
        _now = now()
        rows = cls.model.query.filter(
            cls.model.status == cls.model.Status.VALID,
            cls.model.started_at <= _now,
            cls.model.ended_at >= _now,
            cls.model.offline_type.in_([cls.model.OfflineType.DEPOSIT, cls.model.OfflineType.WITHDRAWAL])
        ).all()

        result = build_deposit_withdrawal_data(rows)
        if result:
            cls().save({cls.unique(*k): json.dumps(v, cls=JsonEncoder) for k, v in result.items()})
            cls().expire(cls.ttl)

    @classmethod
    def get_user_offline_asset_info(cls, user_id, platform: model.Platform, lang: Language):
        _key = cls.unique(platform, lang)
        data = cls().hget(_key)
        if not data:
            return []
        items = json.loads(data)
        result = []
        for item in items:
            trigger_page = cls.model.TriggerPage(item['trigger_page'])
            if item['filter_type'] == cls.model.FilterType.NONE.name:
                result.append(item)
            elif user_id and DepositWithdrawalPopupWindowUsersCache(trigger_page, item['id']).sismember(user_id):
                result.append(item)
        return result


class DepositWithdrawalPopupWindowUsersCache(SetCache):

    def __init__(self, trigger_page: DepositWithdrawalPopupWindow.TriggerPage, id_: int):
        super().__init__(f'{trigger_page.name}-{id_}')


class DepositMaintainInfoCache(StringCache):
    """ Admin 充值关闭信息缓存 """

    def __init__(self, asset: str, chain_: str):
        pk = f"{chain_}-{asset}"
        super().__init__(pk)


class WithdrawalMaintainInfoCache(DepositMaintainInfoCache):
    """ 提现关闭信息缓存 """
    pass


class DepositMaintainSubscriberCache(SetCache):
    """ 充值关闭信息-订阅人 """

    def __init__(self, asset: str, chain_: str):
        pk = f"{chain_}-{asset}"
        super().__init__(pk)

    def add_subscriber(self, user_id: int):
        self.sadd(str(user_id))

    def del_subscriber(self, user_id: int):
        self.srem(str(user_id))

    def has_subscriber(self, user_id: int) -> bool:
        return self.sismember(str(user_id))


class WithdrawalMaintainSubscriberCache(DepositMaintainSubscriberCache):
    """ 提现关闭信息-订阅人 """
    pass


class AssetMaintainCache(HashCache):
    """批量充提维护信息"""
    def __init__(self):
        super().__init__(None)

    @classmethod
    def read_aside(cls):

        def _get_reason(_reason_mapping, _reason_enum):
            reason = gettext(_reason_enum.value, **_reason_mapping['reason_params'])
            return {
                'asset': _reason_mapping['asset'],
                'chain': _reason_mapping['chain'],
                'url': _reason_mapping['url'],
                'disable_at': _reason_mapping['disable_at'],
                'reason': reason,
            }

        ret = {}
        data = cls().read()
        for key, value in data.items():
            mapping = json.loads(value)
            d_reasons = []
            for reason_mapping in mapping['deposit_disabled_reasons']:
                reason_enum = DepositWithdrawalDisableReason[reason_mapping["reason_enum_name"]]
                d_reasons.append(_get_reason(reason_mapping, reason_enum))
            w_reasons = []
            for reason_mapping in mapping['withdrawal_disabled_reasons']:
                reason_enum = DepositWithdrawalDisableReason[reason_mapping["reason_enum_name"]]
                w_reasons.append(_get_reason(reason_mapping, reason_enum))
            ret[key] = {
                'deposit_disabled_reasons': d_reasons,
                'withdrawal_disabled_reasons': w_reasons,
            }
        return ret

    @classmethod
    def reload(cls):
        ret = {}
        asset_chains = asset_to_chains()
        for asset, chains in asset_chains.items():
            deposit_disabled, withdrawal_disabled = True, True
            deposit_disabled_reasons, withdrawal_disabled_reasons = [], []
            for chain in chains:
                ac_conf = get_asset_chain_config(asset, chain)
                if ac_conf.deposits_all_enabled:
                    deposit_disabled = False
                else:
                    reason = cls.get_deposit_maintain_info(ac_conf, asset, chain)
                    deposit_disabled_reasons.append(reason)
                if deposit_disabled is False:
                    deposit_disabled_reasons = []

                if ac_conf.withdrawals_all_enabled:
                    withdrawal_disabled = False
                else:
                    reason = cls.get_withdrawal_maintain_info(ac_conf, asset, chain)
                    withdrawal_disabled_reasons.append(reason)
                if withdrawal_disabled is False:
                    withdrawal_disabled_reasons = []

            # 当所有充值关闭或所有提现关闭时
            if deposit_disabled or withdrawal_disabled:
                ret.update({
                    asset: {
                        'deposit_disabled_reasons': deposit_disabled_reasons,
                        'withdrawal_disabled_reasons': withdrawal_disabled_reasons,
                    }
                })
        for key, value in ret.items():
            ret[key] = json.dumps(value, cls=JsonEncoder)
        cache = cls()
        del_keys = set(cache.hkeys()) - set(ret.keys())
        if del_keys:
            cache.hdel(*del_keys)
        if ret:
            cache.hmset(ret)

    @classmethod
    def get_deposit_maintain_info(cls, ac_conf, asset, chain):
        disable_at = ac_conf.deposits_updated_at_display or int(now().timestamp())
        if ac_conf.deposit_suspend_by_delisting:
            reason = DepositWithdrawalDisableReason.DEPOSIT_SUSPENDED_OFFLINE_ASSET
        else:
            reason = DepositWithdrawalDisableReason.DEPOSIT_SUSPENDED_WALLET_MAINTENANCE
        maintain_info = dict(
            asset=asset,
            chain=chain,
            url="",
            disable_at=disable_at,
            reason=reason.name,
        )
        cache = DepositMaintainInfoCache(asset, chain)
        if info := cache.read():
            maintain_info.update({k: v for k, v in json.loads(info).items() if k in ["url", "reason"]})
        reason_enum = DepositWithdrawalDisableReason[maintain_info["reason"]]
        params = {}
        if reason_enum == DepositWithdrawalDisableReason.DEPOSIT_SUSPENDED_OFFLINE_ASSET:
            params = {"asset": asset, "chain": chain}
        maintain_info["reason"] = gettext(reason_enum.value, **params)
        maintain_info["reason_enum_name"] = reason_enum.name
        maintain_info["reason_params"] = params
        return maintain_info

    @classmethod
    def get_withdrawal_maintain_info(cls, ac_conf, asset, chain):
        disable_at = int(ac_conf.withdrawals_updated_at.timestamp()) if ac_conf.withdrawals_updated_at else int(
            now().timestamp())
        if ac_conf.withdrawal_suspend_by_delisting:
            reason = DepositWithdrawalDisableReason.WITHDRAWAL_SUSPENDED_OFFLINE_ASSET
        else:
            reason = DepositWithdrawalDisableReason.WITHDRAWAL_SUSPENDED_WALLET_MAINTENANCE
        maintain_info = dict(
            asset=asset,
            chain=chain,
            url="",
            disable_at=disable_at,
            reason=reason.name,
        )
        cache = WithdrawalMaintainInfoCache(asset, chain)
        if info := cache.read():
            maintain_info.update({k: v for k, v in json.loads(info).items() if k in ["url", "reason"]})
        reason_enum = DepositWithdrawalDisableReason[maintain_info["reason"]]
        params = {}
        if reason_enum == DepositWithdrawalDisableReason.WITHDRAWAL_SUSPENDED_OFFLINE_ASSET:
            params = {"asset": asset, "chain": chain}
        maintain_info["reason"] = gettext(reason_enum.value, **params)
        maintain_info["reason_enum_name"] = reason_enum.name
        maintain_info["reason_params"] = params
        return maintain_info
