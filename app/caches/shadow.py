# -*- coding: utf-8 -*-

from __future__ import annotations
from enum import Enum
from importlib import import_module
from typing import Tuple, Dict, Type, Any, Optional
from sqlalchemy import func, Enum as SQLEnum
from ..models import db, row_to_dict, get_primary_key
from ..utils import msgpack_encode, msgpack_decode, NamedObject
from .base import BytesCache


_EMPTY = NamedObject('Empty')


def _get_model_enums(model: db.Model):
    return {col.name: col.type.enum_class
            for col in model.__table__.columns
            if isinstance(col.type, (SQLEnum, db.StringEnum))}


class _DBShadowMeta(type):

    def __new__(mcs, name, bases, dct):
        cls = super().__new__(mcs, name, bases, dct)
        model = getattr(cls, 'model', None)
        if model is None:
            if any(isinstance(b, mcs) for b in bases):
                raise AttributeError(f'{name}.model is not defined')
        else:
            pk_fields_attr = 'pk_fields'
            pk_fields = getattr(cls, pk_fields_attr, None)
            columns = frozenset(col.name for col in model.__table__.columns)

            if not pk_fields:
                pk_fields = get_primary_key(model),
                setattr(cls, pk_fields_attr, pk_fields)
            elif not isinstance(pk_fields, tuple):
                raise TypeError(f'{name}.{pk_fields_attr} must be a tuple')
            elif set(pk_fields).difference(columns):
                raise ValueError(
                    f'{name}.{pk_fields_attr} contains invalid fields')

            enums = _get_model_enums(model)
            for prop_name in getattr(cls, 'prop_fields', ()):
                if not isinstance(prop := getattr(model, prop_name), property):
                    raise TypeError(f'{name}.{prop_name} must be a property')
                ret_type = prop.fget.__annotations__.get('return')
                if isinstance(ret_type, str):
                    for owner in model, import_module(model.__module__):
                        if (_r := getattr(owner, ret_type, None)) is not None:
                            ret_type = _r
                            break
                if isinstance(ret_type, type) and issubclass(ret_type, Enum):
                    enums[prop_name] = ret_type

            cls._model_columns = columns
            cls._model_enums = enums

        return cls


class DBShadow:

    def __init__(self, cache: DBShadowCache):
        self._cache = cache

    def __repr__(self):
        return f'<{type(self).__name__} of {self._cache!r}>'

    def __getattr__(self, key: str):
        cache = self._cache
        try:
            return cache.dict[key]
        except KeyError:
            if cache.has_attr(key):  # might be due to migrations
                cache.refresh()
                return cache.dict[key]
            raise AttributeError(key)


class DBShadowCache(BytesCache,
                    metaclass=type('_BaseModelMeta',
                                   (_DBShadowMeta, type(BytesCache)),
                                   {})):

    """
    Usage:
    >>> class SomeModel(db.Model):
    >>>     id = db.Column(db.Integer(), primary_key=True)
    >>>     a = db.Column(db.String())
    >>>     @property
    >>>     def a_times_two(self):
    >>>         return self.a * 2

    >>> class SomeShadowCache(DBShadowCache):
    >>>     model = SomeModel
    >>>     pk_fields = 'a',  # (optional) defaults to ('id',) in this case
    >>>     prop_fields = 'a_times_two',  # (optional)

    >>> row = SomeModel.query.get(...)
    >>> x = SomeShadowCache(row.a)  # with primary key fields defined
    >>> x.dict  # {'id': row.id, 'a': row.a, 'a_times_two': row.a_times_two}
    >>> shadow = x.shadow
    >>> getattr(shadow, 'id', None)  # x.id
    >>> getattr(shadow, 'a', None)  # x.a
    >>> getattr(shadow, 'a_times_two', None)  # x.a_times_two

    Notes:
    1. `pk_fields` is a tuple of strings consisting of fields through which
        a cache instance shall be fetched, defaulting to the primary key of the
        model typically the 'id' field.
    2. Each enum field is dumped as its name ignoring the value and converted
        back to an enum automatically when loaded.  Since we may not know the
        type of each property you must specify it through its annotations if
        you intend to include it in `prop_fields`.
    3. `DBShadowCache.dict` does not tolerate `KeyError`s for fields not loaded
        which might be due to migrations or newly defined properties, while
        `DBShadowCache.shadow` automatically refreshes the cache in such an
        occasion.
    """

    model: Type[db.Model]
    ttl: int = 3600
    pk_fields: Tuple[str, ...]  # defaults to model's primary key
    prop_fields: Tuple[str, ...] = ()
    not_found_exc_class = None
    _model_columns: frozenset
    _model_enums: Dict[str, Type[Enum]]

    def __init__(self, *pk: Any):
        if len(pk) != len(self.pk_fields):
            model_name = self.model.__name__
            raise ValueError(
                f'invalid primary keys: {pk!r} (expected '
                f'{", ".join(f"{model_name}.{f}" for f in self.pk_fields)})')

        super().__init__(':'.join(map(str, pk)))
        self._pk_orig = pk
        value = self.get()
        self._dict = self._load_data(value) if value else None

    @property
    def dict(self) -> Optional[Dict[str, Any]]:
        if self._dict is None:
            self.refresh()
        return self._dict

    @property
    def object(self) -> Optional[db.Model]:
        model = self.model
        fields = self.pk_fields
        binary = func.binary
        filters = {f: (binary(v) if isinstance(v, str) else v)
                   for f, v in zip(fields, self._pk_orig)}
        return model.query.filter_by(**filters).first()

    @object.setter
    def object(self, row: db.Model):
        if not isinstance(row, self.model):
            raise TypeError(
                f'`row` must be an instance of model {self.model.__name__!r}')

        dict_ = row_to_dict(row, with_hook=False)

        for field in self.prop_fields:
            if isinstance(value := getattr(row, field), Enum):
                value = value.name
            dict_[field] = value

        value = msgpack_encode(dict_)
        self.set(value, ex=self.ttl)
        self._dict = self._load_data(value)

    @property
    def shadow(self):
        return DBShadow(self)

    @classmethod
    def _load_data(cls, value: bytes) -> Dict[str, Any]:
        dict_ = msgpack_decode(value)
        for field, enum in cls._model_enums.items():
            if (value := dict_.get(field)) is None:
                continue
            dict_[field] = getattr(enum, value)
        return dict_

    @classmethod
    def has_attr(cls, name: str) -> bool:
        return name in cls._model_columns or name in cls.prop_fields

    def refresh(self):
        if (row := self.object) is None:
            if (exc_class := self.not_found_exc_class) is None:
                raise RuntimeError(
                    f'{self.model.__name__}{self._pk_orig} does not exist')
            raise exc_class(*self._pk_orig)
        self.object = row
