# -*- coding: utf-8 -*-

from typing import List
from ..models import CountrySmsSetting
from .shadow import DBShadowCache


class CountrySmsSettingCache(DBShadowCache):

    model = CountrySmsSetting
    pk_fields = 'country_code',
    ttl = 3600

    def __init__(self, country_code: int):
        super().__init__(str(country_code))

    @property
    def sms_providers(self) -> List[str]:
        # noinspection PyBroadException
        try:
            return (providers.split(',')
                    if (providers := self.dict['sms_providers'])
                    else [])
        except Exception:
            return []
