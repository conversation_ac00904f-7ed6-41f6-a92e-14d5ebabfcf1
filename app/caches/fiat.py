from collections import defaultdict
import json
from decimal import Decimal
from typing import Dict

from .base import Hash<PERSON><PERSON>, StringCache
from ..business.fiat.base import SupportType, get_fiat_partners, PartnerActivity
from ..models import FiatPartnerActivity
from ..utils import now
from ..utils.parser import JsonEncoder


class FiatQuoteCache(StringCache):
    ttl = 300

    def __init__(self, partner: str, user_id: int, quote_id: str):
        pk = f'{partner.lower()}:{user_id}:{quote_id}'
        super().__init__(pk)


class FiatPriceCache(HashCache):

    def __init__(self, partner_name):
        self._partner = partner_name
        super(FiatPriceCache, self).__init__(partner_name)

    def get_price(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        price = self.hget(f"{support_type.value}-{asset}-{fiat}")
        if not price:
            raise KeyError(f"Fiat Price Cache not find key {self._partner}:{support_type.value}-{asset}-{fiat}")
        return Decimal(price)

    def get_all_prices(self) -> Dict[SupportType, Dict[str, Decimal]]:
        data = self.hgetall()
        result = defaultdict(lambda: defaultdict(Decimal))
        for k, v in data.items():
            support_type, asset, fiat = k.split("-")
            v = Decimal(v)
            if v <= 0:
                continue
            result[SupportType(support_type)][(asset, fiat)] = v
        return result

class FiatActivityCache(HashCache):

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        model = FiatPartnerActivity
        _now = now()
        rows = model.query.filter(
            model.status == model.Status.VALID,
            model.begin_at <= _now,
            model.end_at > _now,
        ).all()

        result = {}
        for row in rows:
            d = dict(
                activity_name=row.name,
                activity_icon=PartnerActivity[row.name].icon,
                activity_is_top=row.is_top,
                activity_sort_id=row.sort_id,
                activity_value=row.activity_value,
            )
            result[row.partner] = json.dumps(d, cls=JsonEncoder)
        old_keys = get_fiat_partners()
        if remove_keys := set(old_keys) - set(result.keys()):
            cls().hdel(*remove_keys)
        if result:
            cls().hmset(result)

    @classmethod
    def get_data(cls):
        data = cls().hgetall()
        if data:
            ret = {}
            for partner, _value in data.items():
                activity = json.loads(_value)
                name, value = activity["activity_name"], activity["activity_value"]
                activity["description"] = PartnerActivity[name].get_description(partner.title(), value)
                ret[partner] = activity
        else:
            ret = {}
        return ret


class FiatPartnerMemoCache(StringCache):

    def __init__(self, partner: str):
        pk = f'{partner.lower()}'
        super().__init__(pk)

    def read_aside(self):
        value = self.read()
        if not value:
            return {}
        ret = json.loads(value)
        return ret


class FiatPartnerOfflineMemoCache(StringCache):

    def __init__(self, partner: str):
        pk = f'{partner.lower()}'
        super().__init__(pk)

    def read_aside(self):
        value = self.read()
        if not value:
            return {}
        ret = json.loads(value)
        return ret

class FiatPriceDeviationAlertCache(StringCache):

    ttl = 86400 * 30

    def __init__(self, partner: str, support_type: str, asset: str, fiat: str):
        pk = f'{partner.lower()}-{support_type}-{asset}-{fiat}'
        super().__init__(pk)


class FiatAuthTokenCache(StringCache):
    """
    法币服务商token缓存 (只有BTCDirect用到)
    """

    ttl = 1800

    def __init__(self, partner: str):
        pk = f'{partner.lower()}'
        super().__init__(pk)