#!/usr/bin/python
# -*- coding: utf-8 -*-
from typing import Type
from typing import Dict
from typing import List

from decimal import Decimal

from bson import ObjectId

from app.common.constants import Language

from app.utils.iterable import batch_iter

from app.caches.base import HashCache


class _InvertedIndexData(object):
    POW_8 = 10 ** 8

    @classmethod
    def key(cls) -> str:
        raise NotImplementedError

    @classmethod
    def decimal_to_bytes(cls, d: Decimal) -> bytes:
        return int(d * cls.POW_8).to_bytes(length=4, byteorder='big')

    @classmethod
    def bytes_to_decimal(cls, value: bytes) -> Decimal:
        return Decimal(int.from_bytes(value, byteorder='big')) / cls.POW_8

    @classmethod
    def str_to_bytes(cls, s: str) -> bytes:
        return bytes(s, "UTF-8")

    @classmethod
    def str_from_bytes(cls, value: bytes) -> str:
        return value.decode("UTF-8")

    @classmethod
    def encode(cls, data: Dict) -> bytes:
        raise NotImplementedError

    @classmethod
    def decode(cls, value: bytes) -> Dict:
        raise NotImplementedError

    @classmethod
    def filter(cls, item: Dict, **kwargs) -> bool:
        raise NotImplementedError


class InsightInvertedIndexData(_InvertedIndexData):

    @classmethod
    def key(cls) -> str:
        return 'insight'

    @classmethod
    def enum_to_index(cls, enum_list: List, item) -> int:
        for index, enum_item in enumerate(enum_list):
            if item == enum_item:
                return index
        return 0

    @classmethod
    def index_to_enum(cls, enum_list: List, index: int):
        if index >= len(enum_list):
            return enum_list[0]
        return enum_list[index]

    @classmethod
    def encode(cls, data: Dict) -> bytes:
        search_types, article_types = cls.get_enum_types()
        return b''.join([
            cls.enum_to_index(
                article_types,
                data['article_type']
            ).to_bytes(length=1, byteorder='big'),
            data['insight_id'].binary,
            data['publish_at'].to_bytes(length=4, byteorder='big'),
            cls.decimal_to_bytes(data['tf_idf']),
            cls.enum_to_index(
                search_types,
                data['search_type']
            ).to_bytes(length=1, byteorder='big')
        ])

    @classmethod
    def decode(cls, value: bytes) -> Dict:
        search_types, article_types = cls.get_enum_types()
        return {
            'article_type': cls.index_to_enum(
                article_types, int.from_bytes(value[:1], byteorder='big')
            ),
            'insight_id': ObjectId(value[1:13]),
            'publish_at': int.from_bytes(value[13:17], byteorder='big'),
            'tf_idf': cls.bytes_to_decimal(value[17:21]),
            'search_type': cls.index_to_enum(search_types, int.from_bytes(value[22:], byteorder='big')),
        }

    @classmethod
    def get_enum_types(cls):
        from app.models.mongo.insight import (
            CoinExInsightContentMySQL as CoinExInsightContent,
            ArticleType,
        )

        search_types = [search_type for search_type in CoinExInsightContent.SearchType]
        article_types = [type_ for type_ in ArticleType]
        return search_types, article_types

    @classmethod
    def filter(
            cls,
            item: Dict,
            article_type=None,
    ) -> bool:
        if article_type:
            if item['article_type'] != article_type:
                return False
        return True


class CopyTraderInvertedIndexData(_InvertedIndexData):

    @classmethod
    def key(cls) -> str:
        return 'copy_trader'

    @classmethod
    def encode(cls, data: Dict) -> bytes:
        return b''.join([
            cls.str_to_bytes(data['trader_id']),
            cls.decimal_to_bytes(data['tf_idf']),
        ])

    @classmethod
    def decode(cls, value: bytes) -> Dict:
        return {
            'trader_id': cls.str_from_bytes(value[0:8]),  # 长度和CopyTraderUser.gen_new_trader_id一致
            'tf_idf': cls.bytes_to_decimal(value[8:12]),
        }

    @classmethod
    def filter(cls, item: Dict, filter_trader_ids: set[int] = None) -> bool:
        if filter_trader_ids:
            if item['trader_id'] not in filter_trader_ids:
                return False
        return True


class AcademyArticleInvertedIndexData(_InvertedIndexData):

    @classmethod
    def key(cls) -> str:
        return 'academy_article'

    @classmethod
    def encode(cls, data: Dict) -> bytes:
        return b''.join([
            data['article_id'].to_bytes(length=4, byteorder='big'),  # 4 bytes max=4294967295
            cls.decimal_to_bytes(data['tf_idf']),
        ])

    @classmethod
    def decode(cls, value: bytes) -> Dict:
        return {
            'article_id': int.from_bytes(value[:4], byteorder='big'),
            'tf_idf': cls.bytes_to_decimal(value[4:8]),
        }

    @classmethod
    def filter(cls, item: Dict, article_ids: set[int] = None) -> bool:
        if article_ids:
            if item['article_id'] not in article_ids:
                return False
        return True


class InvertedIndexCache(HashCache):
    """倒排索引缓存基类"""

    STEP = 23  # value中的数组每个item的长度

    def __init__(self, data_cls: Type[_InvertedIndexData], lang: Language):
        self.data_cls = data_cls
        self.lang = lang
        super().__init__(f'{data_cls.key()}:{lang.name}')

    @classmethod
    def calculate_idf(cls, total: int, count: int) -> Decimal:
        return Decimal.log10(Decimal(total + 1) / Decimal(count + 1))

    def data_to_bytes(self, data: List[Dict]) -> bytes:
        bytes_list = []
        for item in data:
            bytes_list.append(self.data_cls.encode(item))
        return b''.join(bytes_list)

    def update(self, index: Dict[str, List[Dict]]):
        for item in batch_iter(index.items(), n=2000):
            self.hmset({word: self.data_to_bytes(data) for (word, data) in item})

    def save(self, index: Dict[str, List[Dict]]):
        del_keys = set(self.hkeys()) - set(index.keys())
        self.update(index)
        if len(del_keys) > 0:
            self.hdel(*del_keys)

    def bytes_to_data(self, value: bytes) -> List[Dict]:
        data = []
        for index in range(int(len(value) / self.STEP)):
            item = value[index * self.STEP: (index + 1) * self.STEP]
            if len(item) != self.STEP:
                break
            data.append(self.data_cls.decode(item))
        return data

    def get(self, words: List[str], **kwargs) -> Dict[str, List]:
        return {
            word: [item for item in self.bytes_to_data(value) if self.data_cls.filter(item, **kwargs)]
            for word, value in self.hmget_with_keys_raw(list(set(words)))
        }


class InsightInvertedIndexCache(InvertedIndexCache):

    STEP = 22  # value中的数组每个item的长度


class CopyTraderInvertedIndexCache(InvertedIndexCache):

    STEP = 12  # value中的数组每个item的长度


class AcademyArticleInvertedIndexCache(InvertedIndexCache):

    STEP = 8  # value中的数组每个item的长度
