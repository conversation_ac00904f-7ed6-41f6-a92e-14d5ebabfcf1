# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from datetime import date
from decimal import Decimal
from operator import or_
from typing import Set, Union, Iterable, List, Dict, Optional, Tuple

from sqlalchemy import func, and_

from app.models.staking import StakingAccount
from app.models import User, db, UserPreference

from ..common import TradeBusinessType
from ..exceptions import InvalidArgument
from ..models import OnlyWithdrawalWhitelistUser, InvestmentAccount, SubAccount, \
    SubAccountManagerRelation, ClearedUser, UserSetting, MarginLoanOrder, CountrySmsRiskConfig
from .base import StringCache, SetCache, HashCache, HyperLogLogCache, ListCache
from ..utils import current_timestamp, quantize_amount, batch_iter, group_by, timestamp_to_date
from ..utils.parser import JsonEncoder


class SubAccountInfoCache(StringCache):
    """ 子账号信息缓存 """

    TTL = 86400

    def __init__(self, sub_user_id: int):
        self.sub_user_id = sub_user_id
        super().__init__(str(sub_user_id))

    def info(self) -> Dict:
        data = self.read()
        if data is None:
            data = self.reload()
        else:
            data = json.loads(data)
        return data

    def reload(self) -> Dict:
        data = self.format_data()
        if data:
            self.set(json.dumps(data, cls=JsonEncoder), ex=self.TTL)
        else:
            self.set(json.dumps({}, cls=JsonEncoder), ex=300)  # 避免非法sub_user_id的穿透
        return data

    def format_data(self) -> Dict:
        sub_account = SubAccount.query.filter(
            SubAccount.user_id == self.sub_user_id,
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
        ).first()
        if not sub_account or sub_account.status != SubAccount.Status.VALID:
            return {}

        manage_relations = SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.user_id == self.sub_user_id,
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).with_entities(SubAccountManagerRelation.manager_id).all()

        data = {
            "main_user_id": sub_account.main_user_id,
            "manage_user_ids": [r.manager_id for r in manage_relations],
            "permissions": [i.name for i in sub_account.enum_permissions],
        }
        return data


class SubAccountBalanceCache(HashCache):
    """ 子账号资产市值, {"sub_user_id1": "usd1", "ts": "timestamp"} """

    TS_KEY = "ts"  # 存更新的时间
    TTL = 3600 * 24 * 30

    def __init__(self, main_user_id: int):
        self.main_user_id = main_user_id
        super().__init__(str(main_user_id))

    def get_balance_map_and_ts(self, sub_user_ids: Optional[List[int]] = None) -> Tuple[Dict[int, Decimal], int]:
        if sub_user_ids:
            keys_ = [str(u) for u in sub_user_ids]
            keys_.append(self.TS_KEY)
            data = dict(self.hmget_with_keys(keys_))
        else:
            data = self.hgetall()
        balance_map = {int(k): Decimal(v) for k, v in data.items() if k != self.TS_KEY}
        ts = int(data.get(self.TS_KEY, 0))
        return balance_map, ts

    def update_ts(self, ts: int):
        self.hset(self.TS_KEY, str(ts))

    def get_ts(self) -> int:
        ts = self.hget(self.TS_KEY)
        return int(ts) if ts else 0

    def refresh_many(self, sub_user_ids: List[int]):
        sub_accounts = SubAccount.query.filter(
            SubAccount.main_user_id == self.main_user_id,
            SubAccount.user_id.in_(sub_user_ids),
            SubAccount.is_visible.is_(True),
        ).all()
        sub_user_ids = [i.user_id for i in sub_accounts]
        if sub_user_ids:
            balance_map = self.query_balances(sub_user_ids)
            save_data = {str(user_id): str(usd) for user_id, usd in balance_map.items()}
            self.hmset(save_data)
            self.expire(self.TTL)
            return balance_map

    def reload(self):
        sub_accounts = SubAccount.query.filter(
            SubAccount.main_user_id == self.main_user_id,
            SubAccount.is_visible.is_(True),
        ).all()
        if not sub_accounts:
            self.delete()
            return
        sub_user_ids = [i.user_id for i in sub_accounts]
        balance_map = self.query_balances(sub_user_ids)
        save_data = {str(user_id): str(usd) for user_id, usd in balance_map.items()}
        save_data[self.TS_KEY] = str(current_timestamp(to_int=True))
        self.save(save_data)
        self.expire(self.TTL)
        return balance_map

    @classmethod
    def query_balances(cls, sub_user_ids: List[int]) -> Dict[int, Decimal]:
        """ 子账号全部资金账户的资产（汇总现货、杠杆、合约、理财、AMM） """
        from app.business import PriceManager

        asset_rates = PriceManager.assets_to_usd()
        perpetual_usd_map = cls.query_perpetual_usd(sub_user_ids, asset_rates)  # raise err: too busy | funding time
        spot_usd_map, margin_usd_map, investment_usd_map = cls.query_spot_margin_investment_usd(sub_user_ids,
                                                                                                asset_rates)
        margin_unflat_usd_map = cls.query_margin_unflat_usd(sub_user_ids, asset_rates)
        amm_usd_map = cls.query_amm_usd(sub_user_ids, asset_rates)

        total_usd_map = {}
        zero = Decimal()
        for user_id in sub_user_ids:
            spot_usd = spot_usd_map.get(user_id, zero)
            margin_usd = margin_usd_map.get(user_id, zero)
            margin_unflat_usd = margin_unflat_usd_map.get(user_id, zero)
            inv_usd = investment_usd_map.get(user_id, zero)
            perpetual_usd = perpetual_usd_map.get(user_id, zero)
            amm_usd = amm_usd_map.get(user_id, zero)
            total_usd_map[user_id] = spot_usd + margin_usd - margin_unflat_usd + inv_usd + perpetual_usd + amm_usd
        return total_usd_map

    @classmethod
    def query_spot_margin_investment_usd(cls, user_ids: List[int], asset_rates: Dict[str, Decimal]):
        # 现货、理财、杠杆资产市值
        from app.business import ServerClient, SPOT_ACCOUNT_ID

        client = ServerClient()
        spot_usd_map = {}
        margin_usd_map = {}
        investment_usd_map = {}
        zero = Decimal()
        for user_id in user_ids:
            accounts_balances_map = client.get_user_accounts_balances(user_id)
            for account_id, assets in accounts_balances_map.items():
                account_id = int(account_id)
                usd = zero
                for asset, balances in assets.items():
                    rate = asset_rates.get(asset, Decimal())
                    _total_amount = quantize_amount(balances["available"] + balances["frozen"], 8)
                    usd += _total_amount * rate
                usd = quantize_amount(usd, 8)
                if account_id == SPOT_ACCOUNT_ID:
                    spot_usd_map[user_id] = usd + spot_usd_map.get(user_id, zero)
                elif account_id == InvestmentAccount.ACCOUNT_ID:
                    investment_usd_map[user_id] = usd + investment_usd_map.get(user_id, zero)
                elif SPOT_ACCOUNT_ID < account_id < InvestmentAccount.ACCOUNT_ID:
                    margin_usd_map[user_id] = usd + margin_usd_map.get(user_id, zero)
                elif account_id == StakingAccount.ACCOUNT_ID:
                    investment_usd_map[user_id] = usd + investment_usd_map.get(user_id, zero)
        return spot_usd_map, margin_usd_map, investment_usd_map

    @classmethod
    def query_margin_unflat_usd(cls, user_ids: List[int], asset_rates: Dict[str, Decimal]) -> Dict[int, Decimal]:
        zero = Decimal()
        margin_unflat_usd_map = {}
        margin_loans = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id.in_(user_ids),
            MarginLoanOrder.status == MarginLoanOrder.StatusType.PASS,
        ).group_by(
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset,
        ).with_entities(
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset,
            func.sum(MarginLoanOrder.unflat_amount + MarginLoanOrder.interest_amount).label('loan_amount'),
        ).all()
        for r in margin_loans:
            usd = quantize_amount(r.loan_amount * asset_rates.get(r.asset, zero), 8)
            margin_unflat_usd_map[r.user_id] = usd + margin_unflat_usd_map.get(r.user_id, zero)
        return margin_unflat_usd_map

    @classmethod
    def query_perpetual_usd(cls, user_ids: List[int], asset_rates: Dict[str, Decimal]) -> Dict[int, Decimal]:
        # 合约资产市值
        from app.business import PerpetualServerClient, PerpetualMarketType
        from app.caches import PerpetualMarketCache

        p_client = PerpetualServerClient()
        p_market_list = PerpetualMarketCache().get_market_list()

        p_asset_to_markets = defaultdict(list)
        p_market_data = PerpetualMarketCache().hgetall()
        for p_market, _info in p_market_data.items():
            # PerpetualMarketCache.balance_asset_to_markets
            _info = json.loads(_info)
            if _info['type'] == PerpetualMarketType.DIRECT:
                p_asset_to_markets[_info['money']].append(p_market)
            else:
                p_asset_to_markets[_info['stock']].append(p_market)

        perpetual_usd_map = {}
        for user_id in user_ids:
            perpetual_usd = Decimal()
            perpetual_balances = p_client.get_user_balances(user_id)
            position_market_map = {i["market"]: i for i in p_client.position_pending(user_id)}
            for asset, balance in perpetual_balances.items():
                equity = balance["balance_total"]
                for _market in p_asset_to_markets[asset]:
                    if _market in p_market_list:
                        position_data = position_market_map.get(_market, defaultdict(Decimal))
                        equity += Decimal(position_data["margin_amount"])
                        equity += Decimal(position_data["profit_unreal"])
                equity = quantize_amount(equity, 8)
                perpetual_usd += equity * asset_rates.get(asset, Decimal())
            perpetual_usd = quantize_amount(perpetual_usd, 8)
            perpetual_usd_map[user_id] = perpetual_usd
        return perpetual_usd_map

    @classmethod
    def query_amm_usd(cls, user_ids: List[int], asset_rates: Dict[str, Decimal]) -> Dict[int, Decimal]:
        # amm资产市值
        from app.business.amm import batch_get_user_amm_assets

        amm_usd_map = {}
        user_amm_assets_map = batch_get_user_amm_assets(user_ids)
        for user_id in user_ids:
            amm_usd = Decimal()
            amm_asset_map = user_amm_assets_map.get(user_id, {})
            for asset, amount in amm_asset_map.items():
                amm_usd += asset_rates.get(asset, Decimal()) * amount
            amm_usd = quantize_amount(amm_usd, 8)
            amm_usd_map[user_id] = amm_usd
        return amm_usd_map


class SubAccountSwitchCache(HashCache):
    """ 子账号被切换的时间, {"sub_user_id1": "timestamp1"} """

    def __init__(self, main_user_id: int):
        self.main_user_id = main_user_id
        super().__init__(str(main_user_id))

    def add_switch_user(self, sub_user_id: int):
        self.hset(str(sub_user_id), str(current_timestamp(to_int=True)))

    def get_switch_users(self) -> Dict:
        data = self.hgetall()
        return {int(user_id): int(ts) for user_id, ts in data.items()}


class UserActivenessCache(SetCache):

    def __init__(self, date_: Union[date, int]):
        if isinstance(date_, int):
            date_ = date.fromordinal(date_)
        super().__init__(date_.strftime('%Y%m%d'))

    def get_users(self) -> Set[int]:
        return {int(s) for s in self.smembers()}

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))

    def add_user(self, *user_id: int):
        self.sadd(*[str(x) for x in user_id])

    def del_user(self, user_id: int):
        self.srem(str(user_id))


class UserRealTimeActivenessCache(SetCache):
    """每5分钟一个缓存，记录和查找实时的用户在线状态"""
    TTL = 60 * 15

    def __init__(self, timestamp: int):
        timestamp -= timestamp % 300
        super().__init__(str(timestamp))

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))

    def add_user(self, *user_id: int):
        self.sadd(*[str(x) for x in user_id])
        self.expire(self.TTL)


class UserOnlineCache(HyperLogLogCache):
    TTL = 60 * 10
    SLOT = "{slot}"

    def __init__(self, platform: str, ts: int):
        # 集群模式key的含有{}的情况会计算{}中的crc16|64的值。如果用{prefix}做前缀会分配到一个slot。
        ts -= ts % 60
        super().__init__(f"{self.SLOT}{platform}:{ts}")

    def set_one(self, *user_id: int):
        self.pfadd(*[str(x) for x in user_id])
        self.expire(self.TTL)


class UserActiveMemCache:
    """在内存中记录活跃用户一段时间，稍后刷到缓存"""

    def __new__(cls, *args, **kwargs):
        if not hasattr(cls, '_instance'):
            cls._instance = super().__new__(cls, *args, **kwargs)
        return cls._instance

    def __init__(self):
        if getattr(self, '_initialized', False):
            return
        self._initialized = True
        self.flush_interval: int = 15
        self.last_flush: int = 0
        self.data: dict[str, set] = {}

    def add(self, user_id: int, platform: str):
        if not (s := self.data.get(platform)):
            s = self.data[platform] = set()
        s.add(user_id)
        self.flush()

    def flush(self):
        ts = current_timestamp(to_int=True)
        if not self.last_flush:
            self.last_flush = ts
            return
        if ts - self.last_flush >= self.flush_interval:
            # 在io操作前重制self.data，确保协程安全。
            data = self.data
            self.data = {}
            self.last_flush = ts
            self._flush(ts, data)

    def _flush(self, ts: int, data: dict[str, set]):
        users = set().union(*data.values())
        UserActivenessCache(timestamp_to_date(ts)).add_user(*users)
        UserRealTimeActivenessCache(ts).add_user(*users)
        for platform, users in data.items():
            UserOnlineCache(platform, ts).set_one(*users)


class TelegramBindingTokenCache(StringCache):
    ttl = 1800

    def __init__(self, token):
        super().__init__(token)


class ApiExpirationCache(StringCache):

    def __init__(self, api_auth_id: int, delta_: int):
        super().__init__(f'{api_auth_id}:{delta_}')
        self.ttl = delta_ * 60 * 60

    def gen(self):
        self.set('1', ex=self.ttl)


class UserVisitPermissionCache(HashCache):
    FORBIDDEN_VALUE = "F"  # forbidden 清退
    ONLY_WITHDRAWAL_VALUE = "L"  # limit 仅提现
    ONLY_WITHDRAWAL_WHITELIST_VALUE = "W"  # white 白名单

    def __init__(self):
        super().__init__(None)

    @classmethod
    def verify_value_type(cls, value_type: str, raise_error: bool = True):
        if value_type not in (cls.FORBIDDEN_VALUE,
                              cls.ONLY_WITHDRAWAL_VALUE,
                              cls.ONLY_WITHDRAWAL_WHITELIST_VALUE):
            if raise_error:
                raise InvalidArgument
            return False
        return True

    def add_users(self, user_ids: Iterable[int], value_type: str):
        self.verify_value_type(value_type)
        self.hmset({str(_uid): value_type for _uid in user_ids})

    def del_users(self, user_ids: Iterable[int], value_type: str):
        self.verify_value_type(value_type)
        if not user_ids:
            return

        result = self.hmget_with_keys(list(map(str, user_ids)))
        filter_result = {
            str(_uid): _value for (_uid, _value) in result if _value == value_type
        }
        # 删除用户
        if filter_result:
            self.hdel(*filter_result.keys())

    def check_user_permission(self, user_id: int, value_types: List[str]) -> bool:
        for _value_type in value_types:
            self.verify_value_type(_value_type)
        user_permission_type = self.hget(str(user_id))
        if not user_permission_type:
            return False
        if user_permission_type in value_types:
            return True
        return False

    def check_users_permission(self, user_ids: List[int], value_types: List[str]) -> Set[int]:
        if not user_ids:
            return set()
        for _value_type in value_types:
            self.verify_value_type(_value_type)
        results = self.hmget_with_keys(list(map(str, user_ids)))
        result_user_ids = set()
        for result in results:
            user_id, _type = result
            if _type in value_types:
                result_user_ids.add(int(user_id))
        return result_user_ids

    def get_user_permission(self, user_id: int):
        user_permission_type = self.hget(str(user_id))
        if not user_permission_type:
            return None
        if self.verify_value_type(user_permission_type, False):
            return user_permission_type
        return None

    @classmethod
    def reload_all(cls):
        q = ClearedUser.query.filter(
            ClearedUser.valid.is_(True)
        ).with_entities(ClearedUser.user_id, ClearedUser.status).all()
        w_q = OnlyWithdrawalWhitelistUser.query.filter(
            OnlyWithdrawalWhitelistUser.status == OnlyWithdrawalWhitelistUser.Status.VALID
        ).with_entities(OnlyWithdrawalWhitelistUser.user_id).all()

        saving_data = {}
        for v in q:
            v: ClearedUser
            if v.status == ClearedUser.Status.FORBIDDEN:
                saving_data[v.user_id] = cls.FORBIDDEN_VALUE
            elif v.status == ClearedUser.Status.WITHDRAWAL_ONLY:
                saving_data[v.user_id] = cls.ONLY_WITHDRAWAL_VALUE
        for v in w_q:
            saving_data[v.user_id] = cls.ONLY_WITHDRAWAL_WHITELIST_VALUE

        cache = cls()
        old_keys = set(cache.hkeys())
        new_keys = set(str(key) for key in saving_data.keys())
        del_keys = old_keys - new_keys
        all_user_ids = list(saving_data.keys())
        del_user_ids = list(del_keys)
        for _u_ids in batch_iter(all_user_ids, 10000):
            _data = {
                _uid: saving_data[_uid]
                for _uid in _u_ids
            }
            cache.hmset(_data)

        for _u_ids in batch_iter(del_user_ids, 10000):
            cache.hdel(*_u_ids)


class UserClassIdentityCache(HashCache):

    def __init__(self, user_id: int, business_type: TradeBusinessType):
        super().__init__(f'{user_id}:{business_type.name}')


class UserSpecialFeeCache(HashCache):

    def __init__(self, user_id: int):
        # key: business_type-trade_type-market
        super().__init__(f'{user_id}')


class AbnormalUserCache(SetCache):

    def __init__(self):
        super().__init__(None)

    @classmethod
    def get_clear_users(cls):
        return {
            u_id for u_id, in ClearedUser.query.filter(
                ClearedUser.valid.is_(True)
            ).with_entities(
                ClearedUser.user_id
            ).all()
        }

    @classmethod
    def get_login_disabled_users(cls):
        # sql:9s
        from app.business import UserSettings
        field = UserSettings.login_disabled_by_admin
        r = UserSetting.query.filter(
            UserSetting.key == field.name,
            UserSetting.status == UserSetting.Status.VALID,
            UserSetting.value == field.db_value(True)
        ).with_entities(UserSetting.user_id)
        return {x for x, in r}

    @classmethod
    def get_anti_fraud_users(cls):
        from ..business import AntiFraudClient
        client = AntiFraudClient()
        data = client.get_risk_user(export=True)
        return {i['user_id'] for i in data['items']}

    @classmethod
    def reload(cls):
        cache = cls()
        disabled_users = cls.get_login_disabled_users()
        clear_users = cls.get_clear_users()
        anti_fraud_users = cls.get_anti_fraud_users()
        cache.value = disabled_users | clear_users | anti_fraud_users

    def get_users(self) -> Set[int]:
        return {int(s) for s in self.smembers()}

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))


class AdminEditUserEmailCache(StringCache):

    def __init__(self, user_id: int):
        super().__init__(str(user_id))


class NormalReferralRewardsCache(StringCache):
    """ 前端昨日普通推荐返佣 """

    def __init__(self):
        super().__init__(None)


class UserBalanceSumCache(StringCache):
    """用户资产总额缓存"""

    ttl = 600

    def __init__(self, user_id: int):
        super().__init__(str(user_id))


class CountrySmsRiskCodeCache(HashCache):

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        model = CountrySmsRiskConfig
        close_rows = model.query.filter(model.status == model.Status.CLOSE).all()
        data = {i.mobile_country_code: i.days for i in close_rows}
        cls().save(data)
        return data

    @classmethod
    def get_risk_days(cls, mobile_country_code: int) -> int:
        days = cls().hget(str(mobile_country_code))
        return int(days) if days else None


class ThirdPartyAccountConfigCache(StringCache):
    """
    第三方账号配置缓存
    """
    TTL = 86400

    def __init__(self, name: str):
        super().__init__(name)


class UserStateUpdateCache(ListCache):
    """
    用户状态更新缓存
    """
    MAX_LENGTH = 100000  # 队列长度限制

    def __init__(self):
        super().__init__(None)

    @classmethod
    def encode(
            cls, user_id: int, token_dict: dict, pref_dict: dict) -> bytes:
        data = {
            "user_id": user_id,
            "token_dict": token_dict,
            "pref_dict": pref_dict,
        }
        return json.dumps(data, cls=JsonEncoder).encode("utf-8")

    @classmethod
    def decode(cls, value: bytes) -> Dict:
        data = json.loads(value.decode("utf-8"))
        return data

    def add(self, user_id: int, token_dict: dict, pref_dict: dict):
        if not user_id:
            return

        if not token_dict and not pref_dict:
            return

        self.rpush(self.encode(user_id, token_dict, pref_dict))
        self.ltrim(-self.MAX_LENGTH, -1)

    def lrange_all(self) -> List[Dict]:
        start, n = 0, 1000
        data = []
        while True:
            item = self.lrange_raw(start, start + n - 1)
            for value in item:
                try:
                    data.append(self.decode(value))
                except ValueError:
                    continue
            if len(item) < n:
                return data
            start += 1000

    def remove(self, n: int) -> bool:
        """裁剪掉列表中的前n项"""
        return self.ltrim(n, -1)


class HadPoppedEditNameUserCache(SetCache):

    def __init__(self):
        super().__init__(None)

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))

    def add_user(self, user_id: int):
        self.sadd(str(user_id))


class NameBlockWordsCache(SetCache):

    def __init__(self, block_type: str):
        super().__init__(block_type)

    def has_word(self, word: str) -> bool:
        return self.sismember(word)

    @classmethod
    def reload(cls):
        from app.models.operation import NameBlockWords, LangNameBlockWords
        account_name_words = {
            i.word.lower() for i in NameBlockWords.query.filter(
                NameBlockWords.block_type == NameBlockWords.BlockType.ACCOUNT_NAME
            ).with_entities(
                NameBlockWords.word
            ).all()
        }
        cls(NameBlockWords.BlockType.ACCOUNT_NAME.name).value = account_name_words
        user_name_words = {
            i.lang_word.lower() for i in LangNameBlockWords.query.with_entities(
                LangNameBlockWords.lang_word
            ).all()
        }
        cls(NameBlockWords.BlockType.USER_NAME.name).value = user_name_words


class InnerMakersCache(StringCache):
    """缓存内部做市商id，只包含主账户"""

    def __init__(self):
        super().__init__(None)

    def get_inner_makers(self) -> list[int]:
        if not (v := self.read()):
            return self.reload()
        return [int(v) for v in v.split(',')]

    def reload(self) -> list[int]:
        result = []
        last = 0
        while True:
            rows = User.query.filter(User.id > last).with_entities(User.id, User.user_type) \
                .order_by(User.id).limit(100000).all()
            if not rows:
                break
            for row in rows:
                if row.user_type == User.UserType.INTERNAL_MAKER:
                    result.append(row.id)
            last = rows[-1].id
        self.set(','.join(map(str, result)), ex=86400 * 2)
        return result


class UserConfigKeyCache(SetCache):
    """ UserPreference，UserSettings key维度缓存"""

    model_field_val_map = {
        UserSetting: {
            "login_disabled_by_admin": "1",
            "withdrawals_disabled_by_admin": "1",
        },
        UserPreference: {
            "allows_announcement_emails": "0",
            "allows_activity_emails": "0",
            "allows_blog_emails": "0",
            "app_information_notice": "0",
            "allows_announcement_app": "0",
            "allows_activity_app": "0",
            "allows_blog_app": "0",
        },
    }

    def __init__(self, model: db.Model, field_name: str, val):
        self.model = model
        self.field_name = field_name
        self.val = val
        super().__init__(f"{model.__name__}:{field_name}:{val}")

    def reload(self):
        user_ids = self.get_field_value_mysql(self.model, self.field_name, self.val)
        self.save({str(user_id) for user_id in user_ids})
        return user_ids

    def add(self, user_id: int):
        if self.has_cache():
            self.sadd(str(user_id))

    def rem(self, user_id: int):
        self.srem(str(user_id))

    def has_cache(self):
        return (self.field_name, self.val) in self.model_field_val_map[self.model].items()

    def get_field_value_user_ids(self):
        if self.has_cache():
            ret = {int(s) for s in self.smembers()}
            if not ret:
                ret = self.reload()
        else:
            ret = self.get_field_value_mysql(self.model, self.field_name, self.val)
        return ret

    @classmethod
    def update_model_field_val_cache(cls):
        for model, field_value_map in cls.model_field_val_map.items():
            group_fields = group_by(lambda x: x[1], field_value_map.items())
            sql_filter = []
            for val, fields in group_fields.items():
                sql_filter.append(
                    and_(
                        model.key.in_([i[0] for i in fields]),
                        model.status == model.Status.VALID,
                        model.value == fields[0][1]
                    )
                )
            query = model.query
            if len(sql_filter) == 1:
                rows = query.filter(sql_filter[0])
            else:
                rows = query.filter(or_(*sql_filter))
            set_dict = defaultdict(set)

            for row in rows:
                set_dict[row.key].add(row.user_id)

            for _field, _set in set_dict.items():
                cls(model, _field, field_value_map[_field]).save({str(i) for i in _set})

    @classmethod
    def get_field_value_mysql(cls, model, field_name, val):
        rows = model.query.filter(
            model.key == field_name,
            model.status == model.Status.VALID,
            model.value == val
        ).with_entities(
            model.user_id
        )
        return {row.user_id for row in rows}

    @classmethod
    def get_login_disabled_by_admin_ids(cls, filter_users: Iterable[int] = None):
        """禁止登录的用户"""
        disabled_users = cls(
            UserSetting,
            "login_disabled_by_admin",
            "1"
        ).get_field_value_user_ids()
        if filter_users:
            return set(filter_users) & disabled_users
        return disabled_users


class SubMainUserCache(HashCache):
    """子账号与主账号之间的映射缓存"""

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        sub_main_mapper = {
            uid: mid for uid, mid in SubAccount.query.filter(
                SubAccount.status == SubAccount.Status.VALID,
                SubAccount.type.in_([SubAccount.Type.NORMAL, SubAccount.Type.STRATEGY])
            ).with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        }
        cls().save(sub_main_mapper)

    @classmethod
    def get_sub_main_mapper(cls):
        cache = cls()
        return {int(k): int(v) for k, v in cache.hgetall().items()}

    @classmethod
    def add_sub_user(cls, sub_user_id: int, main_user_id: int):
        cache = cls()
        cache.hset(sub_user_id, main_user_id)



