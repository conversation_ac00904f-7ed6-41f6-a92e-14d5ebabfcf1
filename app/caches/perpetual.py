# -*- coding: utf-8 -*-
import json
import re
import time
from decimal import Decimal
from collections import defaultdict
from functools import cached_property
from typing import List, Dict, Any, Union

from app.caches import StringCache, HashCache, SetCache
from app.utils import amount_to_str, amount_to_int
from app.common import PerpetualMarketType, OrderSideType, FUNDING_INTERVAL_TRIGGER_DYNAMIC_MINUTES
from app.models import PerpetualMarket, PerpetualAssetIndex
from app.exceptions import InvalidArgument, InvalidMarket
from app.utils.parser import JsonEncoder
_RE_MARKET_CODE = re.compile(r'[A-Za-z0-9_]{1,32}')


class PerpetualMarketCache(HashCache):

    MAX_STOP_ORDER_COUNT = 100

    def __init__(self):
        super().__init__(None)

    def reload(self):
        from app.business import PerpetualServerClient

        client = PerpetualServerClient()
        try:
            result = client.market_list()
        except client.BadResponse:
            result = client.market_list()
        # market merge config.
        markets = PerpetualMarket.query.with_entities(
            PerpetualMarket.name, PerpetualMarket.depths,
            PerpetualMarket.depth_default, PerpetualMarket.leverage_default,
            PerpetualMarket.position_type_default,
            PerpetualMarket.status,
            PerpetualMarket.default_funding_interval,
            PerpetualMarket.dynamic_funding_start,
        ).all()
        markets = {x.name: x for x in markets}
        offline_markets = {k for k, v in markets.items()
                           if v.status == PerpetualMarket.StatusType.CLOSE}
        online_markets = []
        for item in result:
            # 下架市场不返回
            if item['name'] in offline_markets:
                continue
            market = markets[item['name']]
            # 升序排列
            item['merge'] = list(map(amount_to_str, sorted(map(Decimal, market.depths.split(',')))))
            item['leverage_default'] = amount_to_int(market.leverage_default, 0)
            item['default_merge'] = amount_to_str(market.depth_default)
            item['position_type_default'] = market.position_type_default.value
            item['funding']['default_interval'] = market.default_funding_interval * 60 * 60
            item['funding']['dynamic_start'] = market.dynamic_funding_start
            item['offline_visible'] = False
            online_markets.append(item)
        self.value = {item['name']: json.dumps(item) for item in online_markets}

    def read_aside(self) -> Dict[str, Dict[str, Any]]:
        dict_obj = {}
        for market, data_str in self.value.items():
            dict_obj[market] = json.loads(data_str)
        return dict_obj

    def get_market_info(self, market_type) -> Dict[str, Any]:
        market_info = self.hget(market_type)
        return json.loads(market_info) if market_info else {}

    @classmethod
    def is_market_exists(cls, market: str) -> bool:
        return bool(cls().hget(market))

    def get_market_list(self) -> List[str]:
        return self.hkeys()

    @classmethod
    def get_balance_asset(cls, market: Union[str, dict]) -> str:
        """结算币种，余额、保证金、盈亏等单位"""
        if isinstance(market, str):
            market = cls().get_market_info(market)
            if not market:
                raise InvalidArgument
        if market['type'] == PerpetualMarketType.INVERSE:
            return market['stock']
        else:
            return market['money']

    @classmethod
    def get_amount_asset(cls, market: Union[str, dict]) -> str:
        """交易币种，爆仓数量、成交量等单位"""
        if isinstance(market, str):
            market = cls().get_market_info(market)
            if not market:
                raise InvalidArgument
        if market['type'] == PerpetualMarketType.INVERSE:
            return market['money']
        else:
            return market['stock']

    @classmethod
    def get_fee_asset(cls, market: Union[str, dict]) -> str:
        try:
            asset = cls.get_balance_asset(market)
            return asset
        except InvalidArgument:
            return ''

    @classmethod
    def balance_asset_to_markets(cls, asset: str) -> List[str]:
        result = []
        data = cls().hgetall()
        for market, info in data.items():
            info = json.loads(info)
            if info['type'] == PerpetualMarketType.DIRECT:
                if info['money'] == asset:
                    result.append(market)
            else:
                if info['stock'] == asset:
                    result.append(market)
        return result

    @classmethod
    def check_stop_order_count(cls, user_id: int, market: str) -> bool:
        from app.business.clients import PerpetualServerClient
        c = PerpetualServerClient()
        r = c.pending_stop(user_id, market=market, page=1,
                           limit=cls.MAX_STOP_ORDER_COUNT, side=0)
        if r["total"] < cls.MAX_STOP_ORDER_COUNT:
            return True
        return False

    @classmethod
    def get_position_type(cls, market):
        if isinstance(market, str):
            market = cls().get_market_info(market)
            if not market:
                raise InvalidArgument
        return market['type']


class PerpetualCoinTypeCache(StringCache):

    def __init__(self):
        super().__init__(None)

    # noinspection PyMethodMayBeStatic
    def reload(self):
        from app.business import PerpetualServerClient, AssetComparator

        client = PerpetualServerClient()
        try:
            result = client.asset_list()
        except client.RPCBadResponse:
            result = client.asset_list()

        asset_result = PerpetualAssetIndex.query.filter(
            PerpetualAssetIndex.status == PerpetualAssetIndex.StatusType.OPEN
        ).with_entities(
            PerpetualAssetIndex.name
        ).all()
        perpetual_asset_set = set([i.name for i in asset_result])
        assets = [item['name'] for item in result if item['name']
                  in perpetual_asset_set]
        assets.sort(key=lambda x: AssetComparator(x))
        self.value = json.dumps(assets)

    def read_aside(self) -> List[str]:
        if not self.value:
            self.reload()

        return json.loads(self.value)


class PerpetualLiquidationWarningCache(StringCache):
    ttl = 60 * 60

    def __init__(self, market: str, user_id: int, risk_rate: str):
        super().__init__(f'{market}:{user_id}:{risk_rate}')

    def gen(self):
        self.value = '1'
        self.expire(self.ttl)


class PerpetualAdlNoticeCache(StringCache):
    ttl = 60 * 60 * 3

    def __init__(self, market: str, user_id: int):
        super().__init__(f'{market}:{user_id}')

    def gen(self):
        self.value = '1'
        self.expire(self.ttl)


class PerpetualOfflineMarketCache(HashCache):

    def __init__(self):
        super().__init__(None)

    def reload(self):
        from app.business.fee_constant import DEFAULT_CONTRACT_TAKER_FEE, DEFAULT_CONTRACT_MAKER_FEE

        offline_market_query = PerpetualMarket.query.filter(
            PerpetualMarket.status == PerpetualMarket.StatusType.CLOSE
        ).all()

        offline = [dict(
            default_maker_fee_rate=amount_to_str(DEFAULT_CONTRACT_MAKER_FEE),
            default_taker_fee_rate=amount_to_str(DEFAULT_CONTRACT_TAKER_FEE),
            leverage_default=amount_to_int(market.leverage_default, 0),
            position_type_default=market.position_type_default.value,
            merge=market.depths.split(','),
            default_merge=amount_to_str(market.depth_default),
            name=market.name,
            type=market.market_type.value,
            leverages=list(market.leverages.split(',')),
            stock=market.base_asset,
            money=market.quote_asset,
            fee_prec=market.fee_precision,
            stock_prec=market.base_asset_precision,
            money_prec=market.quote_asset_precision,
            amount_prec=market.amount_precision,
            amount_min=amount_to_str(market.min_order_amount),
            multiplier=amount_to_str(market.multiplier),
            tick_size=amount_to_str(market.price_size),
            available=False,
            offline_visible=market.offline_visible,
        ) for market in offline_market_query]
        self.value = {item['name']: json.dumps(item) for item in offline}

    def read_aside(self) -> Dict[str, Dict[str, Any]]:
        if not self.value:
            self.reload()

        dict_obj = {}
        for market, data_str in self.value.items():
            dict_obj[market] = json.loads(data_str)
        return dict_obj

    @classmethod
    def get_balance_asset(cls, market: Union[str, dict]) -> str:
        if isinstance(market, str):
            market = cls().get_market_info(market)
            if not market:
                raise InvalidArgument
        if market['type'] == PerpetualMarketType.INVERSE:
            return market['stock']
        else:
            return market['money']

    @classmethod
    def get_amount_asset(cls, market: Union[str, dict]) -> str:
        if isinstance(market, str):
            market = cls().get_market_info(market)
            if not market:
                raise InvalidArgument
        if market['type'] == PerpetualMarketType.INVERSE:
            return market['money']
        else:
            return market['stock']

    def get_market_info(self, market_type) -> Dict[str, Any]:
        market_info = self.hget(market_type)
        if not market_info:
            self.reload()

        market_info = self.hget(market_type)
        return json.loads(market_info) if market_info else {}

    @classmethod
    def get_position_type(cls, market):
        if isinstance(market, str):
            market = cls().get_market_info(market)
            if not market:
                raise InvalidArgument
        return market['type']

    def get_market_list(self) -> List[str]:
        return self.hkeys()


class PerpetualMarketStatisticsCache(StringCache):

    def __init__(self):
        super().__init__(None)


class PerpetualMarketDataCache(HashCache):

    def __init__(self):
        super().__init__(None)

    @cached_property
    def market_data(self):
        if not self.value:
            return {}

        dict_obj = {}
        for market, data_str in self.value.items():
            dict_obj[market] = json.loads(data_str)
        return dict_obj


class PerpetualMonthlyMarketChangeRateCache(HashCache):

    def __init__(self):
        super().__init__(None)

    def read_aside(self):
        return self.value


class PerpetualUserAnalysisCache(StringCache):
    
    def __init__(self):
        super().__init__(None)


class TodayPerpetualLiquidationCache(HashCache):
    ttl = 86400 * 2
    LAST_RECORD_ID = 'last_record_id'

    def __init__(self, date_timestamp: int):
        self.today_timestamp = date_timestamp
        self.tomorrow_timestamp = date_timestamp + 86400
        super().__init__(str(date_timestamp))

    def get_data(self, market: str):
        data = self.get_ori_data(market)
        if not data:
            data = {
                'burst_amount': Decimal(),
                'short_burst_amount': Decimal(),
                'long_burst_amount': Decimal()
            }
        return data

    def read_aside(self):
        ret = self.read()
        if ret is None:
            return {}
        return {k: json.loads(v) for k, v in ret.items() if k != self.LAST_RECORD_ID}

    def get_ori_data(self, market: str):
        val = self.hget(market)
        if val:
            val_dic = json.loads(val)
            for k, v in val_dic.items():
                val_dic[k] = Decimal(v)
            return val_dic
        return val

    def reload(self, last_record_id):
        market_burst_dic, new_id = self._aggregate_data_from_db(last_record_id)
        for market, burst_dic in market_burst_dic.items():
            ori_data: Dict = self.get_data(market)
            data = {
                'burst_amount': ori_data['burst_amount'] + burst_dic['burst_amount'],
                'short_burst_amount': ori_data['short_burst_amount'] + burst_dic['short_burst_amount'],
                'long_burst_amount': ori_data['long_burst_amount'] + burst_dic['long_burst_amount']
            }
            self.hset(market, json.dumps(data, cls=JsonEncoder))
        self.hset(self.LAST_RECORD_ID, new_id)
        self.expire(self.ttl)

    def get_last_id(self):
        """获取缓存的最后一条记录id，如没有记录，获取当天原始数据库里的第一条记录id，并减一，
        以便更新缓存时，从这条记录开始更新"""
        from app.business import PerpetualHistoryDB

        rec_id = self.hget(self.LAST_RECORD_ID)
        if rec_id:
            return int(rec_id)
        id_ = PerpetualHistoryDB.get_first_rec_id(self.today_timestamp) - 1
        return id_

    def _aggregate_data_from_db(self, rec_id):
        from app.business import PerpetualHistoryDB
        res = defaultdict(lambda: {
            'short_burst_amount': Decimal(),
            'long_burst_amount': Decimal(),
            'burst_amount': Decimal()
        })
        last_id = rec_id
        records = PerpetualHistoryDB.get_liq_finished_order_by_id(rec_id)
        for record in records:
            create_time = record['create_time']
            if self.today_timestamp <= create_time < self.tomorrow_timestamp:
                side = record['side']
                amount = record['amount'] - record['left']
                market = record['market']
                if side == OrderSideType.BUY:  # 买入平空
                    res[market]['short_burst_amount'] += amount
                else:  # 卖出平多
                    res[market]['long_burst_amount'] += amount
                res[market]['burst_amount'] += amount
                last_id = record['id']

        return res, last_id


class PerpetualAvgPremiumCache(HashCache):

    def __init__(self):
        super().__init__(None)

    @staticmethod
    def _get_latest_avg_premium(market_list: List[str]):
        from app.business import PerpetualSysHistoryDB

        start_time = int(time.time()) - 3600
        select_fields = ['time', 'market', 'premium']
        markets_str = '","'.join(market_list)
        where = f'time >= {start_time} AND market in ("{markets_str}")'
        records = PerpetualSysHistoryDB.table('premium_history').select(
            *select_fields,
            where=where,
            order_by='time'
        )

        premium_dic = defaultdict(list)
        for timestamp, market, premium in records:
            premium_dic[market].append(premium)

        avg_premium_dic = defaultdict(Decimal)
        for market in market_list:
            if not premium_dic[market]:
                continue
            avg_premium_dic[market] = sum(premium_dic[market]) / len(premium_dic[market])

        return avg_premium_dic

    def reload(self):
        markets = PerpetualMarket.query.filter(PerpetualMarket.status == PerpetualMarket.StatusType.OPEN).all()
        market_list = [market.name for market in markets]
        latest_avg_premium_dic = self._get_latest_avg_premium(market_list)
        ts = int(time.time())
        market_data_dic = {k: json.loads(v) for k, v in self.hmget_with_keys(market_list)}
        for market in markets:
            market_data = market_data_dic.get(market.name, {})
            avg_premium_count = market_data.get('avg_premium_count', 0)
            continuous_abnormal_count = market_data.get('continuous_abnormal_count', 0)

            if avg_premium_count < FUNDING_INTERVAL_TRIGGER_DYNAMIC_MINUTES:
                avg_premium_count += 1
            latest_avg_premium = latest_avg_premium_dic[market.name]
            if latest_avg_premium > market.funding_max or latest_avg_premium < market.funding_min:
                continuous_abnormal_count += 1
            else:
                continuous_abnormal_count = 0
            market_data_dic[market.name] = {
                'avg_premium_count': avg_premium_count,
                'continuous_abnormal_count': continuous_abnormal_count,
                'latest_avg_premium_time': ts,
            }

        ret = {str(k): json.dumps(v, cls=JsonEncoder) for k, v in market_data_dic.items()}
        self.save(ret)
        return market_data_dic

    def get_market_data(self, market):
        if r := self.hget(market):
            return json.loads(r)
        return {}


class PropTradingUsersCache(StringCache):

    ttl = 86400 * 3

    def __init__(self):
        super().__init__(None)


class _PropTradingMarketCache(HashCache):

    def __init__(self):
        super().__init__(None)


class PropTradingOpUsersCache(SetCache):

    ttl = 86400

    def __init__(self, market: str):
        super().__init__(market)

    @classmethod
    def update(cls, data: dict):
        """data: server数据库的slice_history.prop_user_list字段
        {"common": [], "blacklist": {"ETHUSDT": [], ...}, "BTCUSDT": [], ...}
        """
        common = data.pop('common', set())
        blacklist = data.pop('blacklist', {})

        vs = {k: 1 for k in data.keys()} | {k: 0 for k in blacklist.keys()}
        _cache = _PropTradingMarketCache()
        _cache.save(vs)
        _cache.expire(cls.ttl)

        cache = cls('common')
        cache.save(common)
        cache.expire(cls.ttl)
        for k, v in data.items():
            cache = cls(k)
            cache.save(v)
            cache.expire(cls.ttl)
        for k, v in blacklist.items():
            cache = cls(k)
            cache.save(v)
            cache.expire(cls.ttl)

    @classmethod
    def has(cls, market: str, user_id: int) -> bool:
        v = _PropTradingMarketCache().hget(market)
        if v is None:
            return False
        user_id = str(user_id)
        # 如果是正常市场，需要判断市场及common集合，黑名单市场只需要判断市场集合。
        if int(v):
            return cls(market).sismember(user_id) or cls('common').sismember(user_id)
        else:
            return cls(market).sismember(user_id)


class UserPerpetualAssetsCache(HashCache):

    ttl = 60

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_data(self):
        value = self.hgetall()
        if not value:
            return {}
        return {k: Decimal(v) for k, v in value.items()}

    def set_data(self, data: Dict[str, Decimal]):
        self.hmset({k: str(v) for k, v in data.items()})
        self.expire(self.ttl)


class UserPreferenceFrontendCache(StringCache):
    """
    用户合约偏好缓存(仅用来前端展示)
    - settle_switch: 结算开关
    - had_future_trade: 是否有合约交易
    """

    ttl = 60

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_data(self):
        value = self.get()
        if not value:
            return {}
        return json.loads(value)

    def set_data(self, data: Dict[str, Any]):
        self.set(json.dumps(data))
        self.expire(self.ttl)


class UserDirectPerpetualProfitRealHourCache(HashCache):
    """按小时统计和记录用户正向合约交易盈亏数据"""

    ttl = 60 * 60 * 24 * 2

    def __init__(self, timestamp: int):
        """timestamp表示这一小时的开始时间"""
        super().__init__(str(timestamp - timestamp % (60 * 60)))

    def set_data(self, data: dict[int, Decimal]):
        self.hmset({str(user_id): amount_to_str(profit_real_usd) for user_id, profit_real_usd in data.items()})
        self.expire(self.ttl)

    def all(self) -> dict[int, Decimal]:
        result = self.hgetall()
        if not result:
            return {}
        data = {}
        for user_id_str, profit_real_usd_str in result.items():
            data[int(user_id_str)] = Decimal(profit_real_usd_str)
        return data


class BaseDirectPerpetualProfitRealRankCache(StringCache):
    """正向合约交易盈利前50账号缓存基类"""

    def __init__(self):
        super().__init__(None)

    def set_data(self, data: list[dict]):
        """
        {
            'rank': 1,
            'real_username': 'name',
            'real_avatar': '',
            'username': 'name',
            'avatar': '',
            'profit_real_usd': '1000000',
            'user_id': 1234
        }
        """
        for item in data:
            item['profit_real_usd'] = str(item.get('profit_real_usd', Decimal()))
        self.set(json.dumps(data))


class DirectPerpetualProfitRealMonthRankCache(BaseDirectPerpetualProfitRealRankCache):
    """近30天正向合约交易盈利前50账号"""
    pass


class DirectPerpetualProfitRealYearRankCache(BaseDirectPerpetualProfitRealRankCache):
    """近365天正向合约交易盈利前50账号"""
    pass


def normalise_perpetual_market(market: str) -> str:
    if not market or not _RE_MARKET_CODE.fullmatch(market):
        raise InvalidMarket
    return market.upper()


def has_perpetual_market(market: str, allow_offline: bool = False) -> bool:
    market = check_special_market(market)
    if allow_offline:
        return PerpetualMarketCache().hexists(market) \
               or PerpetualOfflineMarketCache().hexists(market)
    else:
        return PerpetualMarketCache().hexists(market)


def check_special_market(market):
    special_mark = ('SIGNPRICE', 'INDEXPRICE')
    market_split = market.split('_')
    if len(market_split) > 1:
        if market_split[1] in special_mark:
            market = market_split[0]
    return market
