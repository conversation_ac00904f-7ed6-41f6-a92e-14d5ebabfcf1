# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from decimal import Decimal

from app.caches.base import Hyper<PERSON><PERSON><PERSON>og<PERSON><PERSON>, HashCache
from . import TimedHashCache, StringCache
from ..common.push import PushBroadcastLimitGroup, BroadcastGroupCountLimitConfigDic, \
    BroadcastGroupFrequencyLimitConfigDic, BroadcastGlobalLimitCountDic
from ..models import UserPreference, UserFavoriteAsset
from ..models.base import read_only_session
from ..utils import today_timestamp_utc, batch_iter, current_timestamp


class PushLimitAbstract:
    """push推送限制接口抽象类"""

    def can_push(self, *args, **kwargs):
        raise NotImplementedError

    def set_pushed(self, *args, **kwargs):
        raise NotImplementedError


class PushIntervalLimitBaseCache(PushLimitAbstract, TimedHashCache):
    """按时间间隔限制推送的缓存基类"""

    default_interval: int
    count_limit: int

    def __init__(self, key: str = None, interval: int = None):
        interval = interval or self.default_interval
        super().__init__(key, interval=interval)

    def can_push(self) -> bool:
        return self.count() < self.count_limit

    def set_pushed(self, timestamp: int = None):
        if not timestamp:
            timestamp = current_timestamp()
        self.add_value('', timestamp)

    def pushed_times(self):
        return self.count()

    def can_push_times(self):
        pushed_times = self.pushed_times()
        return self.count_limit - pushed_times if self.count_limit >= pushed_times else 0


class SpotOrderNormalDealLimitCache(TimedHashCache):
    # 币币部分/全部成交推送限频
    default_interval: int = 60
    count_limit = 20

    def __init__(self, key: str, interval: int = None):
        super().__init__(key, interval=interval)


class SpotStopOrderLimitCache(TimedHashCache):
    # 币币计划限价/市价推送限频
    default_interval: int = 86400
    count_limit = 30

    def __init__(self, key: str, interval: int = None):
        super().__init__(key, interval=interval)


class PerpetualOrderNormalDealLimitCache(TimedHashCache):
    # 合约部分/全部成交推送限频
    default_interval: int = 60
    count_limit = 20

    def __init__(self, key: str, interval: int = None):
        super().__init__(key, interval=interval)


class PerpetualStopOrderLimitCache(TimedHashCache):
    # 合约计划限价/市价推送限频
    default_interval: int = 86400
    count_limit = 30

    def __init__(self, key: str, interval: int = None):
        super().__init__(key, interval=interval)


class AppSubscriptionNoticeLimitCache(PushIntervalLimitBaseCache):
    # app用户订阅类通知限频
    default_interval: int = 86400
    count_limit = 30


class AppMarketPriceNoticeLimitCache(TimedHashCache):
    # app价格提醒
    default_interval: int = 86400
    count_limit = 30

    def __init__(self, key: str, interval: int = None):
        super().__init__(key, interval=interval)


class AppMarketPriceNoticeIntervalLimitCache(PushIntervalLimitBaseCache):
    # 推送频率为 always 的价格提醒，单个订阅币种24小时内最多推送10次
    default_interval = 86400
    count_limit = 10

    def __init__(self, notice_id: int):
        super().__init__(str(notice_id))


class FirstWithdrawalCheckCache(TimedHashCache):
    # 首次提现确认提醒
    default_interval: int = 86400
    count_limit = 1

    def __init__(self, key: str, interval: int = None):
        super().__init__(key, interval=interval)


class PopularMarketPriceNoticeCache(StringCache):
    # 热门市场涨跌幅推送

    def __init__(self, key: str):
        super().__init__(key)


class MarketPriceNoticeCache(PushLimitAbstract, StringCache):
    # 市场涨跌幅推送（用于用户持仓和自选币种推送的限制）
    ttl = 86400

    def __init__(self, market: str, type_: str):
        super().__init__(f'{market}:{type_}')

    def set_pushed(self, level: str):
        self.set(level, ex=self.ttl)

    def can_push(self, level):
        if self.exists():
            cache_rate = Decimal(self.read())
            if level <= cache_rate:
                return False
            return True
        else:
            return True


class MarketRateLevelNoticeCache(PushLimitAbstract, StringCache):
    # 市场涨跌幅档位推送
    # ttl = 86400   #  去除过期时间限制

    def __init__(self, key: str):
        pk = f'{key}'
        super().__init__(pk)

    def set_pushed(self, level: str):
        self.set(level)

    def can_push(self, level: str):
        ret = self.read()
        if not ret:
            return True
        return ret != level

    def get_level(self) -> Decimal:
        ret = self.read()
        return Decimal(ret) if ret else Decimal()


class AssetUserHoldFavoriteBaseCache(StringCache):
    # 用户持仓、自选币种缓存基类

    def __init__(self):
        super().__init__(None)

    def read(self) -> dict:
        data = super().read()
        if not data:
            return self.reload()
        tmp = json.loads(data)
        ret = {k: set(v) for k, v in tmp.items()}
        return ret

    def get_query_key(self):
        raise NotImplementedError

    @staticmethod
    def get_asset_user_dic(user_ids):
        raise NotImplementedError

    def reload(self):
        query_key = self.get_query_key()
        # sql:28s 在不改动表结构设计的前提下，暂时只能使用读库来降低性能消耗
        with read_only_session() as ro_session:
            user_tags = ro_session.query(UserPreference).filter(  # key没有索引，目前查询一次大概20s
                UserPreference.key == query_key,
                UserPreference.value.is_(True),
                UserPreference.status == UserPreference.Status.VALID,
            ).with_entities(UserPreference.key, UserPreference.user_id).all()
            user_ids = [i.user_id for i in user_tags]
            asset_user_dic = self.get_asset_user_dic(user_ids)
            to_save_data = {k: list(v) for k, v in asset_user_dic.items()}
            self.set(json.dumps(to_save_data))
            return asset_user_dic


class AssetUserHoldCache(AssetUserHoldFavoriteBaseCache):
    """用户持仓币种缓存"""

    def get_query_key(self):
        from app.business.user import UserPreferences
        return UserPreferences.app_holding_asset_notice.name

    @staticmethod
    def get_asset_user_dic(user_ids):
        from app.business.external_dbs import ExchangeLogDB

        holding_asset_dict = defaultdict(set)
        account_user_record = []
        ts = today_timestamp_utc()
        table = ExchangeLogDB.user_account_balance_table(ts)
        if not table.exists():
            ts -= 86400
            table = ExchangeLogDB.user_account_balance_table(ts)
        for chunk_user_ids in batch_iter(user_ids, 5000):
            chunk_records = table.select(
                'user_id',
                'asset',
                'SUM(balance) as total_balance',
                where=f"user_id in ({','.join(map(str, chunk_user_ids))})",
                group_by='asset, user_id',
                having='`total_balance` > 100'
            )
            account_user_record.extend(chunk_records)
        for row in account_user_record:
            holding_asset_dict[row[1]].add(row[0])
        return holding_asset_dict


class AssetUserFavoriteCache(AssetUserHoldFavoriteBaseCache):

    def get_query_key(self):
        from app.business.user import UserPreferences
        return UserPreferences.app_favorite_asset_notice.name

    @staticmethod
    def get_asset_user_dic(user_ids):
        favorite_asset_dict = defaultdict(set)
        favorite_asset_record = []
        model = UserFavoriteAsset
        for chunk_user_ids in batch_iter(user_ids, 5000):
            chunk_favorite_asset_record = model.query.filter(
                model.user_id.in_(chunk_user_ids),
                model.status == model.StatusType.PASSED,
            ).with_entities(
                model.asset,
                model.user_id,
            ).all()
            favorite_asset_record.extend(chunk_favorite_asset_record)
        for favorite_ in favorite_asset_record:
            favorite_asset_dict[favorite_.asset].add(favorite_.user_id)
        return favorite_asset_dict


class AppAutoPushReadCache(HyperLogLogCache):
    # app自动推送已读数量
    ttl = 86400

    def __init__(self, id_):
        super().__init__(str(id_))


class StrategyPushReadCache(HashCache):
    # 策略推送已读数量
    ttl = 86400 * 5
    def __init__(self):
        super().__init__(None)

    def add(self, history_id: int):
        self.hincrby(str(history_id), 1)
        self.expire(self.ttl)

    def get_recs(self) -> dict:
        recs = self.hgetall()
        if not recs:
            return {}
        return {int(history_id): int(count) for history_id, count in recs.items()}

    def clear(self):
        self.delete()


class CommonGroupBroadcastCountLimitCache(PushIntervalLimitBaseCache):
    """广播一般消息组数量限制缓存"""

    default_interval: int = BroadcastGroupCountLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['interval']
    count_limit = BroadcastGroupCountLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['count']


class CommonGroupBroadcastFrequencyLimitCache(PushIntervalLimitBaseCache):
    """广播一般消息组频率限制缓存"""

    default_interval: int = BroadcastGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['interval']
    count_limit = BroadcastGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['count']


class NoticeGroupBroadcastCountLimitCache(PushIntervalLimitBaseCache):
    """广播提醒消息组数量限制缓存"""

    default_interval: int = BroadcastGroupCountLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['interval']
    count_limit = BroadcastGroupCountLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['count']


class NoticeGroupBroadcastFrequencyLimitCache(PushIntervalLimitBaseCache):
    """广播提醒消息组频率限制缓存"""

    default_interval: int = BroadcastGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['interval']
    count_limit = BroadcastGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['count']


class GlobalBroadcastLimitCache(PushIntervalLimitBaseCache):
    """广播消息组全局数量限制缓存"""
    default_interval: int = BroadcastGlobalLimitCountDic['interval']
    count_limit: int = BroadcastGlobalLimitCountDic['count']


class NewAssetPriceRiseNoticeCache(PushLimitAbstract, StringCache):
    # 市场涨跌幅档位推送
    ttl = 86400

    def __init__(self, asset: str):
        super().__init__(asset)

    def set_pushed(self):
        self.set('', ex=self.ttl)

    def can_push(self):
        return not self.exists()

