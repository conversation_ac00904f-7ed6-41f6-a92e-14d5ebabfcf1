import json
from collections import defaultdict

from flask_babel import gettext as _, force_locale

from app.business.equity_center.helper import EquityCenterService
from .base import HashCache, SetCache
from .. import Language

from ..models.mission_center import MissionPlan, Mission
from ..utils.parser import JsonEncoder


class MissionCache(HashCache):
    """任务缓存"""

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        from app.business.mission_center.mission import MissionBiz
        mission_query = Mission.query.join(
            MissionPlan, MissionPlan.id == Mission.plan_id
        ).filter(
            MissionPlan.status.in_([
                MissionPlan.Status.EFFECTIVE,
                MissionPlan.Status.STOPPED,
                MissionPlan.Status.FINISHED
            ])
        ).with_entities(
            Mission.id,
            Mission.mission_condition,
            Mission.logic_params,
            Mission.equity_id,
            Mission.sequence,
            Mission.deadline_days,
            MissionPlan.scene_type
        ).all()
        equity_ids = {i.equity_id for i in mission_query}
        equity_basic_info_mapper = EquityCenterService.batch_query_equity_basic_info(equity_ids)
        cache = cls()
        for mission in mission_query:
            equity_data = equity_basic_info_mapper.get(mission.equity_id, {})
            mission_data = dict(
                mission_id=mission.id,
                mission_condition=mission.mission_condition.name,
                logic_params=mission.logic_params,
                sequence=mission.sequence,
                deadline_days=mission.deadline_days,
                scene_type=mission.scene_type.name,
                equity_id=mission.equity_id,
                reward=dict(
                    reward_type=equity_data["type"].name,
                    value=equity_data["cost_amount"],
                    value_type=equity_data["cost_asset"],
                    cashback_asset=equity_data['cashback_asset'],
                ),
                mission_extra=MissionBiz.get_mission_extra_by_condition(mission.mission_condition),
            )
            cache.hset(mission.id, json.dumps(mission_data, cls=JsonEncoder))

    @classmethod
    def get_cache_data_by_ids(cls, mission_ids: list):
        if not mission_ids:
            return {}
        return {k: json.loads(v) for k, v in cls().hmget_with_keys(mission_ids)}

    @classmethod
    def get_all_cache_data(cls):
        return {k: json.loads(v) for k, v in cls().hgetall().items()}

    @classmethod
    def get_data_by_id(cls, mission_id: int):
        cache = cls()
        value = cache.hget(str(mission_id))
        if not value:
            return {}
        return json.loads(value)

    @classmethod
    def get_mission_by_reward_type(cls, reward_type: str):
        cache = cls()
        value = cache.hgetall()
        if not value:
            return []
        result = []
        for k, v in value.items():
            data = json.loads(v)
            if data['reward']['reward_type'] != reward_type:
                continue
            result.append(k)
        return result


class MissionContentCache(HashCache):
    """任务投放内容缓存"""

    REFER_FINISHED_TEXT = _("活动参与人数已满")

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        from ..business.mission_center.plan import MissionPlanBiz
        # normal
        plan_mapper = {
            plan_id: lang_content for plan_id, lang_content in MissionPlan.query.filter(
                MissionPlan.status == MissionPlan.Status.EFFECTIVE,
            ).with_entities(
                MissionPlan.id,
                MissionPlan.deliver_content
            ).all()
        }
        # special
        finished_display_plans = MissionPlanBiz.query_finished_display_plans()
        finished_content_mapper = defaultdict(dict)
        for plan_id in finished_display_plans:
            for lang in Language:
                with force_locale(lang.value):
                    finished_content_mapper[plan_id][lang.name] = _(cls.REFER_FINISHED_TEXT)

        cache = cls()
        update_plan_keys = plan_mapper.keys()
        current_keys = cache.hkeys()
        for k, data in plan_mapper.items():
            cache.hset(k, json.dumps(data, cls=JsonEncoder))
        for k, data in finished_content_mapper.items():
            cache.hset(k, json.dumps(data, cls=JsonEncoder))
        update_keys = list(update_plan_keys) + list(finished_display_plans)
        delete_keys = set(current_keys) - set(map(str, update_keys))
        if delete_keys:
            cache.hdel(*delete_keys)

    @classmethod
    def get_lang_data(cls, plan_id: int, lang: Language) -> str:
        cache = cls()
        lang_data = cache.hget(str(plan_id))
        if not lang_data:
            return ""
        return json.loads(lang_data).get(lang.name, "")

    @classmethod
    def delete_plan_data(cls, plan_id: int) -> None:
        cache = cls()
        cache.hdel(str(plan_id))

    @classmethod
    def update_one(cls, plan_id: int, deliver_content: dict):
        cls().hset(str(plan_id), json.dumps(deliver_content, cls=JsonEncoder))


class SendNoticeCache(SetCache):
    """任务中心已发放消息缓存"""

    def __init__(self, notice_type: str):
        super().__init__(notice_type)

    def get_ids(self) -> set[int]:
        return {int(s) for s in self.smembers()}

    def has_id(self, user_mission_id: int) -> bool:
        return self.sismember(str(user_mission_id))

    def add_id(self, user_mission_id: int):
        self.sadd(str(user_mission_id))

    def add_ids(self, user_mission_ids: list):
        self.sadd(*[str(i) for i in user_mission_ids])

    def delete_ids(self, user_mission_ids: list):
        self.srem(*[str(i) for i in user_mission_ids])
