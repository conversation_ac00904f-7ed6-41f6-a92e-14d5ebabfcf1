# -*- coding: utf-8 -*-

import json
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from collections import defaultdict
from typing import Dict, Union

from sqlalchemy import func

from . import TimedHashCache
from .kline import AssetInformationCache
from ..assets import (
    asset_to_chains, get_asset, get_asset_chain_config,
    get_asset_config, get_chain, list_all_chains, list_all_assets, list_pre_assets,
)
from ..models import CoinInformation, Deposit, Withdrawal, DailyChainDepositWithdrawalReport
from ..utils import (AWSBucketPublic, now, today_timestamp_utc, amount_to_str)
from ..utils.parser import JsonEncoder
from .base import HashCache, StringCache


class AssetsViewCache(StringCache):

    INNER_CHAINS = {"PRE_MARKET", "_NULL"}  # 内部链：某些页面不展示这些链、链的币种

    def __init__(self, is_withdrawal_privileged: bool):
        pk = None
        if is_withdrawal_privileged:
            pk = 'wp'
        super().__init__(pk)

    @classmethod
    def reload(cls):
        cls.reload_with_wp(is_withdrawal_privileged=False)
        cls.reload_with_wp(is_withdrawal_privileged=True)

    @classmethod
    def reload_with_wp(cls, is_withdrawal_privileged) -> Dict:
        from app.caches.spot import ExchangeAvailableAssetsCache

        assets = []
        logo_rows = CoinInformation.query.filter(
            CoinInformation.status == CoinInformation.Status.VALID
        ).with_entities(
            CoinInformation.code,
            CoinInformation.icon,
            CoinInformation.thumbnail_icon,
        ).all()
        logos = {i.code: i.icon for i in logo_rows}
        thumbnail_logo_map = {i.code: i.thumbnail_icon or i.icon for i in logo_rows}

        coin_info = CoinInformation.query.filter(
            CoinInformation.status == CoinInformation.Status.VALID
        ).with_entities(
            CoinInformation.code, CoinInformation.is_st
        ).all()
        st_map = {i.code: i.is_st for i in coin_info}

        pre_assets = list_pre_assets()

        exchange_asset_data = ExchangeAvailableAssetsCache().read()
        exchange_assets = set(json.loads(exchange_asset_data)) if exchange_asset_data else set()
        visible_chains = set()  # 一个链下面如果没有可见的币，不展示这个链

        for asset, chains in asset_to_chains().items():
            d_enabled = w_enabled = visible = False
            for chain in chains:
                ac_conf = get_asset_chain_config(asset, chain)
                if ac_conf.deposits_all_enabled:
                    d_enabled = True
                if cls.withdrawals_enabled(ac_conf, is_withdrawal_privileged):
                    w_enabled = True
                if ac_conf.is_visible:
                    visible = True

            if visible:
                _asset = get_asset(asset)
                asset_cfg = get_asset_config(asset)
                magnification = json.loads(_asset.magnification_info) if _asset.magnification_info else None
                chain_is_inner = set(chains).issubset(cls.INNER_CHAINS)
                asset_info = dict(
                    code=asset,
                    name=_asset.name,
                    logo=AWSBucketPublic.get_file_url(logos[asset]) if asset in logos else '',
                    thumbnail_logo=AWSBucketPublic.get_file_url(thumbnail_logo_map[asset]) if asset in thumbnail_logo_map else '',
                    local_transfers_enabled=asset_cfg.local_transfers_enabled,
                    is_privacy=asset_cfg.is_privacy,
                    deposits_enabled=d_enabled,
                    withdrawals_enabled=w_enabled,
                    is_st=st_map.get(asset, False),
                    is_pre=asset in pre_assets,
                    is_inner=chain_is_inner,  # 是内部链的币种
                    exchange_enabled=asset in exchange_assets,
                )
                if magnification:
                    asset_info['magnification'] = magnification
                assets.append(asset_info)
                visible_chains.update(chains)

        chains = []
        for chain in list_all_chains():
            if chain not in visible_chains:
                continue
            c = get_chain(chain)
            chains.append(dict(
                chain=chain,
                name=c.display_name,
                network_name=c.network_name
            ))

        result = dict(
            assets=assets,
            chains=chains
        )
        cls(is_withdrawal_privileged).set(json.dumps(result, cls=JsonEncoder))
        return result

    @classmethod
    def withdrawals_enabled(cls, ac_conf, is_withdrawal_privileged):
        if is_withdrawal_privileged:
            return ac_conf.withdrawals_all_enabled_to_privileged_users
        return ac_conf.withdrawals_all_enabled


class AssetConfigsViewCache(HashCache):

    def __init__(self, is_withdrawal_privileged: bool):
        pk = None
        if is_withdrawal_privileged:
            pk = 'wp'
        super().__init__(pk)

    @classmethod
    def reload(cls):
        cls.reload_with_wp(is_withdrawal_privileged=False)
        cls.reload_with_wp(is_withdrawal_privileged=True)

    @classmethod
    def reload_with_wp(cls, is_withdrawal_privileged) -> Dict:
        configs = {}
        for asset, chains in asset_to_chains().items():
            confs = []
            for chain in chains:
                c = get_chain(chain)
                ac_conf = get_asset_chain_config(asset, chain)
                if not ac_conf.is_visible:
                    continue
                conf = dict(
                    chain=chain,
                    chain_name=c.display_name,
                    network_name=c.network_name,
                    deposits_enabled=ac_conf.deposits_all_enabled,
                    withdrawals_enabled=AssetsViewCache.withdrawals_enabled(ac_conf, is_withdrawal_privileged),
                    deposits_visible=ac_conf.deposits_visible,
                    withdrawals_visible=ac_conf.withdrawals_visible,
                    withdrawal_precision=ac_conf.withdrawal_precision,
                    withdrawal_fee=ac_conf.withdrawal_fee,
                    identity=ac_conf.identity,
                    explorer_asset_url=ac_conf.explorer_asset_url,
                    min_deposit_amount=ac_conf.min_deposit_amount,
                    min_withdrawal_amount=ac_conf.min_withdrawal_amount,
                    safe_confirmations=ac_conf.safe_confirmations,
                    irreversible_confirmations=ac_conf.irreversible_confirmations,
                    deposits_delay_minutes=cls.calc_deposits_delay_minutes(ac_conf),
                    supported_by_via_wallet=ac_conf.supported_by_via_wallet,
                    deflation_rate=ac_conf.deflation_rate,
                    supports_memo=ac_conf.supports_memo,
                    require_memo_for_deposits=ac_conf.requires_memo_for_deposits,
                    memo_name=ac_conf.memo_name or ''
                )
                confs.append(conf)

            configs[asset] = confs

        mapping = {x: json.dumps(y, cls=JsonEncoder) for x, y in configs.items()}
        cls(is_withdrawal_privileged).save(mapping)
        return configs

    @classmethod
    def calc_deposits_delay_minutes(cls, ac_conf):
        minute = ac_conf.deposits_delay_minutes  # 区块高度/扫描落后时间
        if minute <= 15:
            return 0
        minute += (ac_conf.block_time * ac_conf.safe_confirmations) // 60  # 充值入账时间
        s, r = divmod(minute, 30)
        if r != 0:
            s += 1
        return min(30 * s, 24 * 60)


class APIV2AssetsViewCache(HashCache):

    def __init__(self, is_withdrawal_privileged: bool):
        pk = None
        if is_withdrawal_privileged:
            pk = 'wp'
        super().__init__(pk)

    @classmethod
    def reload(cls):
        cls.reload_with_wp(is_withdrawal_privileged=False)
        cls.reload_with_wp(is_withdrawal_privileged=True)

    @classmethod
    def reload_with_wp(cls, is_withdrawal_privileged: bool) -> dict:
        assets_view_cache_str = AssetsViewCache(is_withdrawal_privileged).read()
        assets_data = json.loads(assets_view_cache_str).get("assets", [])
        assets_mapping_data = {
            _data["code"]: _data
            for _data in assets_data
        }
        asset_chain_data = {
            _asset: json.loads(_details)
            for _asset, _details in AssetConfigsViewCache(is_withdrawal_privileged).hgetall().items()
        }
        result = dict()
        all_assets = list_all_assets()
        for _asset in all_assets:
            _to_be_parsed_chain_data = asset_chain_data.get(_asset, [])
            _to_be_parsed_asset_data = assets_mapping_data.get(_asset, {})
            if not _to_be_parsed_chain_data or not _to_be_parsed_asset_data:
                continue

            chain_data = cls._get_asset_chain_info(_to_be_parsed_chain_data)
            asset_data = cls._get_asset_info(_to_be_parsed_asset_data)
            result[_asset] = dict(asset=asset_data, chains=chain_data)

        mapping = {x: json.dumps(y, cls=JsonEncoder) for x, y in result.items()}
        cls(is_withdrawal_privileged).save(mapping)
        return result

    @classmethod
    def _convert_asset_data(cls, _origin_data: dict) -> dict:
        result = {}
        if not _origin_data:
            return result

        result["ccy"] = _origin_data["code"]
        result["deposit_enabled"] = _origin_data["deposits_enabled"]
        result["withdraw_enabled"] = _origin_data["withdrawals_enabled"]
        result["inter_transfer_enabled"] = _origin_data["local_transfers_enabled"]
        result["is_st"] = _origin_data["is_st"]
        return result

    @classmethod
    def _convert_chain_data(cls, _origin_data: dict) -> dict:
        result = {}
        if not _origin_data or _origin_data["chain"] in AssetsViewCache.INNER_CHAINS:
            return result
        result["chain"] = _origin_data["chain"]
        result["min_deposit_amount"] = _origin_data["min_deposit_amount"]
        result["min_withdraw_amount"] = _origin_data["min_withdrawal_amount"]
        result["deposit_enabled"] = _origin_data["deposits_enabled"]
        result["withdraw_enabled"] = _origin_data["withdrawals_enabled"]
        result["deposit_delay_minutes"] = _origin_data["deposits_delay_minutes"]
        result["safe_confirmations"] = _origin_data["safe_confirmations"]
        result["irreversible_confirmations"] = _origin_data["irreversible_confirmations"]
        result["deflation_rate"] = _origin_data["deflation_rate"]
        result["withdrawal_fee"] = _origin_data["withdrawal_fee"]
        result["withdrawal_precision"] = _origin_data["withdrawal_precision"]
        result["memo"] = _origin_data["memo_name"]
        result["is_memo_required_for_deposit"] = _origin_data["require_memo_for_deposits"]
        result["explorer_asset_url"] = _origin_data["explorer_asset_url"]
        return result

    @classmethod
    def _get_asset_info(cls, _asset_data: dict):
        return cls._convert_asset_data(_asset_data)

    @classmethod
    def _get_asset_chain_info(cls, _chain_details: list):
        chain_data_list = [cls._convert_chain_data(_chain_data) for _chain_data in _chain_details]
        return [data for data in chain_data_list if data]


class APIV2AssetsChainViewCache(HashCache):

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        assets = list_all_assets()

        data = AssetInformationCache().get_assets_data(assets=assets)
        res, cache = {}, cls()
        for item in data:
            chain_info = []

            for c in asset_to_chains(asset=item["short_name"]):
                chain = get_chain(c)
                ac_conf = get_asset_chain_config(item["short_name"], c)
                chain_info.append({
                    "chain_name": chain.name,
                    "identity": ac_conf.identity,
                    "explorer_url": ac_conf.explorer_asset_url,
                })
            entry = {
                "short_name": item["short_name"],
                "full_name": item["full_name"],
                "website_url": item["website_url"],
                "white_paper_url": item["white_paper_url"],
                "chain_info": chain_info,
            }
            res[item["short_name"]] = json.dumps(entry, cls=JsonEncoder)
                
        cache.save(res)


class UserDailyWithdrawnAmountCache(StringCache):

    DECIMALS = 8
    ttl = 86400

    def __init__(self, user_id: int):
        self.today_timestamp = today_timestamp_utc()
        key = f'{user_id}:{self.today_timestamp}'
        super().__init__(key)

    def get_withdrawn_amount(self) -> Decimal:
        ret = self.read()
        return Decimal(ret) if ret else Decimal()

    def add_amount(self, asset: str, amount: Decimal, at: datetime = None):
        from ..business import PriceManager
        if at is not None and at.timestamp() < self.today_timestamp:
            return
        rate = PriceManager.asset_to_usd(asset)
        usd = amount * rate
        ret = self.read()
        withdrawal_usd = Decimal(ret) if ret else Decimal()
        all_withdrawal_usd = withdrawal_usd + usd if withdrawal_usd + usd >= 0 else Decimal()
        self.set(amount_to_str(all_withdrawal_usd, self.DECIMALS), ex=self.ttl)


class User30DaysWithdrawalUsdCache(TimedHashCache):
    ttl = 30 * 86400
    DECIMALS = 8

    def __init__(self, user_id):
        super().__init__(user_id, interval=self.ttl)
        self.today_timestamp = today_timestamp_utc()

    def add_amount(self, asset: str, amount: Decimal, at: datetime = None):
        from ..business import PriceManager
        rate = PriceManager.asset_to_usd(asset)
        usd = amount * rate
        if at is None:
            ts = self.today_timestamp
        else:
            ts = int(at.timestamp())
            ts -= ts % 86400
        ret = self.hget(str(ts))
        withdrawal_usd = Decimal(ret) if ret else Decimal()
        all_withdrawal_usd = withdrawal_usd + usd if withdrawal_usd + usd >= 0 else Decimal()
        self.add_value(amount_to_str(all_withdrawal_usd, self.DECIMALS), ts)

    def get_all_amount(self) -> Decimal:
        return Decimal(sum([Decimal(amount) for amount in self.list_values()]))

    def _clear_outdated(self):
        start = self.today_timestamp - self.ttl
        keys = [x for x in self.hkeys() if int(x) < start]
        if keys:
            self.hdel(*keys)


class UserDailyDepositAmountCache(StringCache):

    DECIMALS = 8
    ttl = 86400

    def __init__(self, user_id: int):
        self.today_timestamp = today_timestamp_utc()
        key = f'{user_id}:{self.today_timestamp}'
        super().__init__(key)

    def get_deposit_usd(self) -> Decimal:
        ret = self.read()
        return Decimal(ret) if ret else Decimal()

    def add_amount(self, asset: str, amount: Decimal, at: datetime = None):
        from ..business import PriceManager
        if at is not None and at.timestamp() < self.today_timestamp:
            return
        rate = PriceManager.asset_to_usd(asset)
        usd = amount * rate
        ret = self.read()
        cache_usd = Decimal(ret) if ret else Decimal()
        total_usd = cache_usd + usd if cache_usd + usd >= 0 else Decimal()
        self.set(amount_to_str(total_usd, self.DECIMALS), ex=self.ttl)



class DepositCursorCache(StringCache):

    def __init__(self):
        super().__init__(None)


class DepositUpdateCache(StringCache):

    def __init__(self, key: str):
        super().__init__(key)


class WithdrawalApproverCache(StringCache):

    def __init__(self, user_id: int, key: str):
        pk = f"{user_id}:{key}"
        super().__init__(pk)


class WalletBroadcastCache(StringCache):
    MAX_LIMIT = 10

    TTL = 60

    DEPOSIT_STATUS_MAPPING = {
        Deposit.Status.TO_HOT: Deposit.Status.FINISHED,
        Deposit.Status.EXCEPTION: Deposit.Status.FINISHED
    }

    WITHDRAWAL_STATUS_MAPPING = {
        Withdrawal.Status.CONFIRMING: Withdrawal.Status.FINISHED,
        Withdrawal.Status.CANCELLATION_FAILED: Withdrawal.Status.CANCELLED,
        Withdrawal.Status.FAILED: Withdrawal.Status.PROCESSING
    }

    def __init__(self, user_id: int):
        self._user_id = user_id
        super().__init__(str(user_id))

    def _get_status_deposit(self, start_time, end_time, status):
        deposits = Deposit.query.filter(
            Deposit.user_id == self._user_id,
            Deposit.created_at >= start_time,
            Deposit.created_at < end_time,
            Deposit.status.in_(status)
        ).with_entities(
            Deposit.id,
            Deposit.type,
            Deposit.asset,
            Deposit.chain,
            Deposit.amount,
            Deposit.status,
            Deposit.confirmations,
            Deposit.created_at
        ).all()
        return deposits or []

    def _get_status_withdrawals(self, start_time, end_time, status):
        withdrawals = Withdrawal.query.filter(
            Withdrawal.user_id == self._user_id,
            Withdrawal.created_at >= start_time,
            Withdrawal.created_at < end_time,
            Withdrawal.status.in_(status)  # 产品需求将 PROCESSING 列为已完成类型
        ).with_entities(
            Withdrawal.id,
            Withdrawal.type,
            Withdrawal.asset,
            Withdrawal.chain,
            Withdrawal.amount,
            Withdrawal.status,
            Withdrawal.created_at,
            Withdrawal.confirmations
        ).all()
        return withdrawals or []

    def reload(self):
        from ..business.deposit_audit import DepositAuditBusiness

        now_ = now()
        finished_start_time = now_ - timedelta(hours=24)
        pending_start_time = now_ - timedelta(days=7)
        end_time = now_
        has_pending_record = True

        deposits = self._get_status_deposit(
            start_time=pending_start_time,
            end_time=end_time,
            status=[Deposit.Status.PROCESSING, Deposit.Status.CONFIRMING]
        )
        # 产品需求将 PROCESSING 列为已完成类型
        withdrawals = self._get_status_withdrawals(
            start_time=pending_start_time,
            end_time=end_time,
            status=[Withdrawal.Status.CREATED, Withdrawal.Status.AUDIT_REQUIRED, Withdrawal.Status.AUDITED]
        )
        if not deposits and not withdrawals:
            has_pending_record = False
        deposits += self._get_status_deposit(
            start_time=finished_start_time,
            end_time=end_time,
            status=[Deposit.Status.FINISHED, Deposit.Status.CANCELLED]
        )
        withdrawals += self._get_status_withdrawals(
            start_time=finished_start_time,
            end_time=end_time,
            status=[Withdrawal.Status.FINISHED, Withdrawal.Status.CANCELLED, Withdrawal.Status.FAILED,
                    Withdrawal.Status.PROCESSING, Withdrawal.Status.CONFIRMING]
        )
        pending_status_set = {
            self._get_broadcast_status(status).name for status in [
                Deposit.Status.PROCESSING, Deposit.Status.CONFIRMING,
                Withdrawal.Status.CREATED, Withdrawal.Status.AUDIT_REQUIRED,
                Withdrawal.Status.AUDITED
            ]
        }
        deposit_audits = DepositAuditBusiness.get_deposit_audits(deposits)
        data = []
        asset_config_mapper = {}
        type_objects_mapper = {
            "deposit": (deposits, deposit_audits),
            "withdrawal": (withdrawals, {})
        }
        for key, dw_tuple in type_objects_mapper.items():
            audits = dw_tuple[1]
            for obj in dw_tuple[0]:
                if obj.asset not in asset_config_mapper and obj.chain is not None and has_pending_record:
                    asset_config_mapper[(obj.asset, obj.chain)] = get_asset_chain_config(obj.asset, obj.chain)
                as_conf = asset_config_mapper.get((obj.asset, obj.chain))
                data.append({
                    "type": key,
                    "id": obj.id,
                    "method": obj.type.name,
                    "audit": audits.get(obj.id, {}),
                    "asset": obj.asset,
                    "amount": obj.amount,
                    "status": self._get_broadcast_status(obj.status).name,
                    "confirmations": min(obj.confirmations, as_conf.irreversible_confirmations) if as_conf else obj.confirmations,
                    "freeze_confirmations": as_conf.safe_confirmations if as_conf else 0,
                    "unfreeze_confirmations": as_conf.irreversible_confirmations if as_conf else 0,
                    "created_at": obj.created_at
                })

        data.sort(key=lambda x: (x['status'] in pending_status_set, x["created_at"]), reverse=True)
        self.set(json.dumps(data[0: self.MAX_LIMIT], cls=JsonEncoder), ex=self.TTL)

    def _get_broadcast_status(self, status: Union[Deposit.Status, Withdrawal.Status]) -> Union[Deposit.Status, Withdrawal.Status]:
        if isinstance(status, Deposit.Status):
            return self.DEPOSIT_STATUS_MAPPING.get(status, status)
        return self.WITHDRAWAL_STATUS_MAPPING.get(status, status)


class CustomWithdrawalAssetPriceCache(HashCache):
    """ 用于计算，币种-链 自定义提现币种的手续费数目。存币种汇率，每小时更新一次 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        from app.business import PriceManager

        prices = PriceManager.assets_to_usd()
        mapping = {_asset: amount_to_str(_price) for _asset, _price in prices.items()}
        cls().save(mapping)


class GasAddressAmountCache(HashCache):
    """统计每日打给gas地址的金额(USD)"""

    def __init__(self, day: int = None):
        if day is None:
            day = today_timestamp_utc()
        super().__init__(str(day))

    def get_amount(self, chain: str) -> Decimal:
        if not (v := self.hget(chain)):
            return Decimal()
        return Decimal(v) / 100

    def add_amount(self, chain: str, amount: Decimal):
        amount = int(amount * 100)
        self.hincrby(chain, amount)
        self.expire(86400 * 2)

class TmpAddressAmountCache(HashCache):
    """统计每日打给临时地址的金额(USD)"""

    def __init__(self, day: int = None):
        if day is None:
            day = today_timestamp_utc()
        super().__init__(str(day))

    def get_amount(self, chain: str) -> Decimal:
        if not (v := self.hget(chain)):
            return Decimal()
        return Decimal(v) / 100

    def add_amount(self, chain: str, amount: Decimal):
        amount = int(amount * 100)
        self.hincrby(chain, amount)
        self.expire(86400 * 2)


class GasAddressThresholdCache(StringCache):
    """统计历史每日gas的金额，给出限制阈值"""

    MIN = Decimal(5000)
    DEFAULT = Decimal(10000)
    MAX = Decimal(30000)

    def __init__(self, chain: str):
        self.chain = chain
        super().__init__(chain)

    def get_limit(self) -> Decimal:
        v = self.get()
        if v is None:
            v = self._get_last_avg_gas()
            self.set(amount_to_str(v), ex=86400)
        return self._normalise_limit(Decimal(v))

    def _get_last_avg_gas(self):
        d = (now() - timedelta(days=7)).date()
        return DailyChainDepositWithdrawalReport.query.filter(
            DailyChainDepositWithdrawalReport.chain == self.chain,
            DailyChainDepositWithdrawalReport.report_date >= d
        ).with_entities(
            func.avg(DailyChainDepositWithdrawalReport.gas_fee)
        ).scalar() or Decimal()

    def _normalise_limit(self, v):
        # 允许历史gas的2倍阈值或最小限制，但不能超出最大限制
        # 如果统计不到历史gas，则返回默认值
        if not v:
            return self.DEFAULT
        v *= 2
        if v < self.MIN:
            return self.MIN
        if v > self.MAX:
            return self.MAX
        return v


class ChainAssetPendingWithdrawalAmountCache(HashCache):
    """ 内部钱包API 提现待打币种数量 缓存 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        from app.business.utils import yield_query_records_by_time_range

        now_ = now()
        start_dt = now_ - timedelta(days=30)
        query = yield_query_records_by_time_range(
            table=Withdrawal,
            start_time=start_dt,
            end_time=now_,
            select_fields=(
                Withdrawal.id,
                Withdrawal.type,
                Withdrawal.status,
                Withdrawal.chain,
                Withdrawal.asset,
                Withdrawal.amount,
            ),
            limit=10000,
        )
        chain_asset_amount_map = defaultdict(Decimal)
        chain_asset_ids_map = defaultdict(list)
        for row in query:
            if row.type == Withdrawal.Type.ON_CHAIN and row.status in [
                Withdrawal.Status.CREATED,
                Withdrawal.Status.AUDIT_REQUIRED,
                Withdrawal.Status.AUDITED,
            ]:
                chain_asset_amount_map[(row.chain, row.asset)] += row.amount
                chain_asset_ids_map[(row.chain, row.asset)].append(row.id)

        dump_data = {}
        for k, v in chain_asset_amount_map.items():
            d = {
                "total_amount": amount_to_str(v),
                "ids": chain_asset_ids_map[k],
            }
            dump_data[":".join(k)] = json.dumps(d)
        cls().save(dump_data)
