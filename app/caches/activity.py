# -*- coding: utf-8 -*-
import datetime
import json
import math
import random
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from enum import Enum
from itertools import chain
from typing import Optional, Dict, List, Union, Set, Any

from flask import current_app
from flask_babel import force_locale, gettext
from pyroaring import BitMap
from sqlalchemy import func

from app.caches.perpetual import PerpetualMarketCache
from app.caches.spot import MarketCache
from app.common.constants import PerpetualMarketType, NoviceTradeType
from app.models.daily import DailyInvestmentReport
from app.models.investment import InvestmentAccount
from app.models.operation import TradeRankActivity, CalendarActivity, CalendarContent, \
    DiscountActivity, DiscountActivityDetail, DiscountActivityCondition, ActivityCondition, \
    IeoActivityCondition, \
    DepositBonusActivity, DepositBonusActivityContent, DepositBonusActivityConfig, \
    DepositBonusActivityCondition, AppStartPage, AppStartPageContent
from . import TimedHashCache

from .base import String<PERSON>ache, SetCache, Hash<PERSON>ache, ListCache, BitsCache, BytesCache
from app.caches.prices import AssetRealTimePriceCache, AssetRealTimeRateCache
from .kline import AssetTagsCache
from ..common import Language
from ..exceptions import DataNotReady
from ..models import (
    PopupWindow,
    PopupWindowContent,
    DepositActivity,
    MiningActivity,
    MiningActivityDetail,
    MiningTradeConfig,
    MiningPendingConfig,
    MiningPledgeConfig,
    MiningAmmConfig,
    MiningTradeModeHistory,
    MiningPendingModeHistory,
    MiningUserPledgeInfo,
    MiningPendingWeightConfig, Banner, BannerContent, AirdropActivity, AirdropActivityReward,
    AirdropActivityDetail, AirdropActivityQuestionBank,
    AirdropActivityCondition, CoinInformation, CoinExplorer, IeoActivity, IeoActivityDetail,
    IeoAssetInformation, IeoActivityOrder, UserTradeSummary, LoginRelationHistory, NewAsset, CoinInformationTrans,
    AssetTag, ReferralHistory
)
from ..models.activity import CouponPool, UserCoupon, Coupon, CouponApply, CouponApplyDraft
from ..models.activity import PerpetualSpecial, PerpetualSpecialActivity, \
    PerpetualSpecialConnect, NovicePrefectureActivity, NoviceActivityContent, CouponRisk, \
    CoinHalvingActivity, CoinHalvingActivityContent, \
    LaunchMiningProject, LaunchMiningPool
from ..models.system import AppJumpList
from ..utils import now, today, datetime_to_time, quantize_amount, amount_to_str, datetime_to_str, \
    timestamp_to_datetime, str_to_datetime, batch_iter, today_datetime, AWSBucketPublic
from ..utils.parser import JsonEncoder


class ReferralRankCache(StringCache):
    """
    推荐返佣排行
    """

    def __init__(self):
        super().__init__(None)


class AmbassadorRankCache(StringCache):
    """
    大使邀请排行
    """

    def __init__(self):
        super().__init__(None)


class BannerCache(StringCache):
    """弹窗缓存"""

    def __init__(self, platform: Banner.Platform, lang: Language):
        platform = platform.name
        lang = lang.name
        super().__init__(f"{platform}:{lang}")

    @classmethod
    def reload(cls):
        _now = now()
        rows = Banner.query.filter(
            Banner.status == Banner.Status.VALID,
            Banner.started_at <= _now,
            Banner.ended_at > _now,
            Banner.affect_homepage.is_(True),
        ).all()
        banners = defaultdict(list)
        for row in rows:
            banners[row.platform].append(row)

        contents = BannerContent.query.filter(
            BannerContent.owner_id.in_([x.id for x in rows]),
            BannerContent.file_key != ''
        ).all()
        contents = {(x.owner_id, x.lang): x for x in contents}

        result = defaultdict(list)
        for platform in Banner.Platform:
            for banner in banners[platform]:
                for lang in Banner.AVAILABLE_LANGS:
                    if not (c := contents.get((banner.id, lang))):
                        continue
                    url = c.url or banner.url
                    param = Banner.build_legacy_params(platform, url) if url else {}  # 取新的url
                    result[(platform, lang)].append(dict(
                        sort_id=banner.sort_id,
                        banner_id=banner.id,
                        name=banner.name,
                        platform=banner.platform.value,
                        url=url,
                        param=param,  # 废弃字段，兼容保留
                        img_src=(img_src := c.img_src),
                        img_src_big=img_src,
                    ))
        old_keys = {(x, y) for x in Banner.Platform for y in Language}
        for k, v in result.items():
            v.sort(key=lambda x: x['sort_id'], reverse=True)
            old_keys.remove(k)
            cls(*k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            cls(*k).delete()


class PopupWindowCache(StringCache):
    """弹窗缓存"""

    ttl = 86400

    def __init__(self, platform: PopupWindow.Platform, lang: Language):
        platform = platform.name
        lang = lang.name
        super().__init__(f"{platform}:{lang}")

    @classmethod
    def reload(cls):
        _now = now()
        model = PopupWindow
        rows = model.query.filter(
            model.status == model.Status.VALID,
            model.started_at < _now,
            model.ended_at > _now
        ).all()

        jump_ids = []
        windows = defaultdict(list)
        for row in rows:
            windows[row.platform].append(row)
            if row.jump_page_enabled:
                jump_ids.append(row.jump_id)
        jump_mapping = cls._get_jump_mapping(jump_ids)

        contents = PopupWindowContent.query.filter(
            PopupWindowContent.popup_window_id.in_([x.id for x in rows])
        ).all()
        contents = {(x.popup_window_id, x.lang): x for x in contents}

        result = defaultdict(list)
        for platform in (model.Platform.WEB, model.Platform.APP):
            for window in chain(windows[platform], windows[model.Platform.ALL]):
                for lang in model.AVAILABLE_LANGS:
                    if not (c := contents.get((window.id, lang))):
                        continue

                    trigger_pages = window.get_trigger_pages()
                    result[(platform, lang)].append(dict(
                        id=window.id,
                        platform=window.platform.name,
                        trigger_pages=trigger_pages,
                        frequency=window.frequency.name,
                        filter_type=window.filter_type.name,
                        sort_id=window.sort_id,
                        started_at=datetime_to_time(window.started_at),
                        ended_at=datetime_to_time(window.ended_at),
                        jump_id=window.jump_id,
                        jump_page_enabled=window.jump_page_enabled,
                        jump_type=window.jump_type.name if window.jump_type else '',
                        jump_url=c.url or jump_mapping.get(window.jump_id) or '',
                        title=c.title,
                        content=c.content,
                        pop_position=window.pop_position.name,
                        content_style=window.content_style.name,
                        images=c.images,
                        summary=c.summary,
                    ))

        old_keys = {(x, y) for x in model.Platform for y in Language}
        for k, v in result.items():
            v.sort(key=lambda x: x['sort_id'], reverse=True)
            old_keys.remove(k)
            PopupWindowCache(*k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            PopupWindowCache(*k).delete()

        for window in rows:
            if window.filter_type == model.FilterType.FILTERS and window.user_bitmap:
                cache = PopupWindowUsersByteCache(window.id)
                cache.set(window.user_bitmap, ex=cls.ttl)

    @classmethod
    def _get_jump_mapping(cls, jump_ids):
        model = AppJumpList
        jump_mapping = {}
        if jump_ids:
            jump_rows = model.query.with_entities(
                model.id,
                model.jump_data,
            ).filter(
                model.id.in_(jump_ids)
            ).all()
            for jump_row in jump_rows:
                jump_mapping[jump_row.id] = jump_row.jump_data

        return jump_mapping

    def filter_by_user(self, user_id: int = None) -> List[Dict]:
        if not (v := self.read()):
            return []
        result = []
        v = json.loads(v)
        for item in v:
            if item['filter_type'] == PopupWindow.FilterType.NONE.name:
                result.append(item)
            elif user_id and PopupWindowUsersByteCache(item['id']).sismember(user_id):
                result.append(item)
        return result


class PopupWindowUsersByteCache(BytesCache):

    def __init__(self, id_: int):
        super().__init__(str(id_))

    def sismember(self, user_id):
        values = BitMap.deserialize(self.read())
        return user_id in values


class AppStartPageCache(StringCache):
    """APP启动页缓存"""

    ttl = 86400

    def __init__(self, platform: AppStartPage.Platform, lang: Language):
        platform = platform.name
        lang = lang.name
        super().__init__(f"{platform}:{lang}")

    @classmethod
    def reload(cls):
        _now = now()
        model = AppStartPage
        rows = model.query.filter(
            model.status == model.Status.VALID,
            model.started_at < _now,
            model.ended_at > _now
        ).all()

        windows = defaultdict(list)
        for row in rows:
            windows[row.platform].append(row)

        contents = AppStartPageContent.query.filter(
            AppStartPageContent.app_start_page_id.in_([x.id for x in rows])
        ).all()
        contents = {(x.app_start_page_id, x.lang): x for x in contents}

        result = defaultdict(list)
        for platform in (model.Platform.ANDROID, model.Platform.IOS):
            for window in chain(windows[platform], windows[model.Platform.ALL]):
                for lang in model.AVAILABLE_LANGS:
                    if not (c := contents.get((window.id, lang))):
                        continue
                    result[(platform, lang)].append(dict(
                        id=window.id,
                        return_url=window.return_url,
                        show_time=window.show_time,
                        frequency=window.frequency.name,
                        filter_type=window.filter_type.name,
                        sort_id=window.sort_id,
                        img_src=c.images[0] if c.images else '',
                    ))

        old_keys = {(x, y) for x in model.Platform for y in Language}
        for k, v in result.items():
            v.sort(key=lambda x: x['sort_id'], reverse=True)
            old_keys.remove(k)
            AppStartPageCache(*k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            AppStartPageCache(*k).delete()

        for window in rows:
            if window.filter_type == model.FilterType.FILTERS and window.user_bitmap:
                cache = AppStartPageUsersByteCache(window.id)
                cache.set(window.user_bitmap, ex=cls.ttl)

    def filter_by_user(self, user_id: int = None) -> List[Dict]:
        if not (v := self.read()):
            return []
        result = []
        v = json.loads(v)
        for item in v:
            if item['filter_type'] == AppStartPage.FilterType.NONE.name:
                result.append(item)
            elif user_id and AppStartPageUsersByteCache(item['id']).sismember(user_id):
                result.append(item)
        return result


class AppStartPageUsersByteCache(BytesCache):

    def __init__(self, id_: int):
        super().__init__(str(id_))

    def sismember(self, user_id):
        values = BitMap.deserialize(self.read())
        return user_id in values


class ActivityViewCache(StringCache):
    """活动列表API视图缓存"""

    WEB = 'web'
    APP = 'app'
    RANK_TYPES = (DepositActivity.Type.RANK, DepositActivity.Type.PARTITION)

    def __init__(self, platform: str):
        if platform not in (self.WEB, self.APP):
            raise ValueError(f"invalid platform '{platform}'")
        super().__init__(platform)

    @classmethod
    def _dump_trade_activity(cls, row: TradeRankActivity):
        markets = []
        if row.markets:
            markets = json.loads(row.markets)
        else:
            if row.market_type == TradeRankActivity.MarketType.ALL:
                if row.type == TradeRankActivity.Type.SPOT_TRADE:
                    markets = MarketCache.list_online_markets()
                else:
                    markets = PerpetualMarketCache().get_market_list()
            elif row.market_type == TradeRankActivity.MarketType.DIRECT_PERPETUAL:
                infos = PerpetualMarketCache().read_aside()
                markets = [k for k, v in infos.items() if v['type'] == PerpetualMarketType.DIRECT]
            else:
                infos = PerpetualMarketCache().read_aside()
                markets = [k for k, v in infos.items() if v['type'] == PerpetualMarketType.INVERSE]

        return {
            'trade_activity_id': row.id,
            'gift_asset': row.gift_asset,
            'gift_amount': row.gift_amount,
            'market': markets,
            'start_time': row.started_at,
            'end_time': row.ended_at,
            'type': row.type.name,
            'status': row.status.name,
        }

    @classmethod
    def _dump_deposit_activity(cls, row: DepositActivity):
        return {
            'deposit_activity_id': row.id,
            'deposit_coin': row.deposit_asset,
            'name': row.name,
            'gift_coin': row.gift_asset,
            'total_amount': row.total_amount,
            'least_amount': row.least_amount,
            'gift_type': row.type,
            'start_time': row.started_at,
            'end_time': row.ended_at,
            'zendesk_url': row.zendesk_url or '',
            'join_type': 'all',
            'status': row.status,
            'is_rank': row.type in cls.RANK_TYPES
        }

    @classmethod
    def _dump_airdrop_activity(cls, row: AirdropActivity) -> Dict:
        return {
            'airdrop_activity_id': row.id,
            'start_time': row.start_time,
            'end_time': row.end_time,
            'status': row.status.name,
        }

    @classmethod
    def _dump_discount_activity(cls, row: DiscountActivity) -> Dict:
        return {
            'discount_activity_id': row.id,
            'start_time': row.start_time,
            'end_time': row.end_time,
            'status': row.status.name,
        }

    @classmethod
    def _dump_pledge_activity_list(cls, rows: List[MiningActivity]) -> List[Dict]:
        """ 当一个(质押)币种有多个质押活动时, 只返回一个优先级最高的活动 """
        asset_activities_map = defaultdict(list)
        configs = MiningPledgeConfig.query.filter(MiningPledgeConfig.mining_activity_id.in_([i.id for i in rows])).all()
        config_map = {i.mining_activity_id: i for i in configs}
        for row in rows:
            config = config_map.get(row.id)
            if not config:
                continue

            asset_activities_map[config.pledge_asset].append(
                {
                    "mining_activity_id": row.id,
                    "mining_asset": config.pledge_asset,
                }
            )

        _today = today()
        row_map = {row.id: row for row in rows}

        def _sorter(r):
            act = row_map[r["mining_activity_id"]]
            if _today < act.start_date:
                return 1, -datetime_to_time(act.start_date), -act.id
            if act.start_date <= _today < act.end_date:
                return 0, -datetime_to_time(act.start_date), -act.id
            return 2, -datetime_to_time(act.end_date), -act.id

        result = []
        for asset_activities in asset_activities_map.values():
            act_nums = len(asset_activities)
            if act_nums == 1:
                result.append(asset_activities[0])
            elif act_nums > 1:
                asset_activities.sort(key=_sorter)
                result.append(asset_activities[0])
        return result

    @classmethod
    def _dump_amm_activity_list(cls, rows: List[MiningActivity]) -> List[Dict]:
        """ 当一个AMM市场有多个做市活动时, 只返回一个优先级最高的活动, 但是挖矿收益率是所有活动的累加 """
        from app.business import PriceManager
        from app.business.activity.mining import PledgeAmmMiningHelper

        market_activities_map = defaultdict(list)
        for row in rows:
            total_liq_usd = MiningActivityAmmCache(row.id).get_total_liq_usd()
            if total_liq_usd > 0:
                cfg = MiningAmmConfig.query.filter(MiningAmmConfig.mining_activity_id == row.id).first()
                # amm挖矿收益率 = 每日产量市值 * 365 / 参与活动用户的总流动性市值 * 100%
                asset_rate = PriceManager.asset_to_usd(row.mining_asset)
                mining_asset_daily_usd = cfg.hourly_output * 24 * asset_rate
                mining_profit_rate = quantize_amount(mining_asset_daily_usd * 365 / total_liq_usd, 4)
            else:
                mining_profit_rate = Decimal()

            markets = PledgeAmmMiningHelper.get_amm_activity_markets(row)
            for market in markets:
                market_activities_map[market].append(
                    {
                        "mining_activity_id": row.id,
                        "mining_market": market,
                        "mining_profit_rate": mining_profit_rate,
                    }
                )

        _today = today()
        row_map = {row.id: row for row in rows}

        def _sorter(r):
            """
            1. 优先跳转进行中的活动
            2. 如果有相同状态的活动，则跳转至开始时间更晚的活动。
            3. 如果开始时间一致，则优先跳转至活动ID更大的活动。
            """
            act = row_map[r["mining_activity_id"]]
            if _today < act.start_date:
                return 1, -datetime_to_time(act.start_date), -act.id
            if act.start_date <= _today < act.end_date:
                return 0, -datetime_to_time(act.start_date), -act.id
            return 2, -datetime_to_time(act.end_date), -act.id

        result = []
        for market_activities in market_activities_map.values():
            act_nums = len(market_activities)
            if act_nums == 1:
                result.append(market_activities[0])
            elif act_nums > 1:
                market_activities.sort(key=_sorter)
                first = market_activities[0]
                first["mining_profit_rate"] = sum(i["mining_profit_rate"] for i in market_activities)
                result.append(first)
        return result

    @classmethod
    def reload(cls):
        # 交易排名活动展示未开始和进行中的
        trade_activities = TradeRankActivity.query.filter(
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
            TradeRankActivity.ended_at > now()
        ).order_by(TradeRankActivity.started_at.desc()).all()

        # web端返回进行中&7天内结束的排行类活动 和 进行中的其他充值类型活动
        end_time = now() - timedelta(days=7)
        possible_deposit_activities = DepositActivity.query.filter(
            DepositActivity.status.in_([DepositActivity.Status.PASSED,
                                        DepositActivity.Status.FINISHED]),
            DepositActivity.ended_at >= end_time
        ).order_by(DepositActivity.started_at.desc()).all()
        web_deposit_activities = []
        app_deposit_activities = []
        _now = now()
        for activity in possible_deposit_activities:
            if activity.type in cls.RANK_TYPES:
                web_deposit_activities.append(activity)
                app_deposit_activities.append(activity)
                continue
            # app端兼容，只返回进行中&7天内结束的排行活动
            if activity.status == DepositActivity.Status.PASSED and activity.ended_at > _now:
                web_deposit_activities.append(activity)

        # 未开始和进行中的质押和做市挖矿活动
        pledge_mining_activity = []
        amm_mining_activity = []
        mining_activities = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.end_date > _now.date(),
            MiningActivity.mining_mode.in_([MiningActivity.MiningMode.PLEDGE, MiningActivity.MiningMode.AMM])
        ).all()
        for act in mining_activities:
            if act.mining_mode == MiningActivity.MiningMode.PLEDGE:
                pledge_mining_activity.append(act)
            elif act.mining_mode == MiningActivity.MiningMode.AMM:
                amm_mining_activity.append(act)

        airdrop_activities = AirdropActivity.query.filter(
            AirdropActivity.status == AirdropActivity.StatusType.ONLINE,
            AirdropActivity.end_time > now()
        ).all()

        discount_activities = DiscountActivity.query.filter(
            DiscountActivity.status == DiscountActivity.StatusType.ONLINE,
            DiscountActivity.end_time > now()
        ).all()

        trade_activities = [cls._dump_trade_activity(x) for x in trade_activities]
        web_deposit_activities = [cls._dump_deposit_activity(x) for x in web_deposit_activities]
        app_deposit_activities = [cls._dump_deposit_activity(x) for x in app_deposit_activities]
        pledge_mining_activity = cls._dump_pledge_activity_list(pledge_mining_activity)
        amm_mining_activity = cls._dump_amm_activity_list(amm_mining_activity)
        airdrop_activities = [cls._dump_airdrop_activity(x) for x in airdrop_activities]
        discount_activities = [cls._dump_discount_activity(x) for x in discount_activities]

        web_data = {
            'trade_activity': trade_activities,
            'deposit_activity': web_deposit_activities,
            'pledge_mining_activity': pledge_mining_activity,
            'amm_mining_activity': amm_mining_activity,
            'airdrop_activity': airdrop_activities,
            'dibs_activities': discount_activities,
        }
        cls(cls.WEB).set(json.dumps(web_data, cls=JsonEncoder))

        app_data = {
            'trade_activity': trade_activities,
            'deposit_activity': app_deposit_activities,
            'pledge_mining_activity': pledge_mining_activity,
            'amm_mining_activity': amm_mining_activity,
            'airdrop_activity': airdrop_activities,
            'dibs_activities': discount_activities,
        }
        cls(cls.APP).set(json.dumps(app_data, cls=JsonEncoder))


class MiningActivityCache(StringCache):
    """挖矿活动首页展示的活动列表"""

    def __init__(self, mode: Optional[Union[str, MiningActivity.MiningMode]], lang: Union[str, Language]):
        if isinstance(lang, Language):
            lang = lang.value
        if mode is None:
            mode = 'all'
        elif isinstance(mode, MiningActivity.MiningMode):
            mode = mode.value
        super().__init__(f'{mode}:{lang}')

    @classmethod
    def reload(cls):
        _today = today()
        rows = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE
        ).all()
        grouped = defaultdict(list)
        for row in rows:
            grouped[row.mining_mode].append(row)

        def sorter(act: MiningActivity):
            if _today < act.start_date:
                return (1, -datetime_to_time(act.start_date), -act.id)
            if act.start_date <= _today < act.end_date:
                return (0, -datetime_to_time(act.start_date), -act.id)
            return (2, -datetime_to_time(act.end_date), -act.id)

        old_keys = {(mode, lang) for mode in MiningActivity.MiningMode for lang in MiningActivityDetail.AVAILABLE_LANGS}
        old_keys |= {(None, lang) for lang in MiningActivityDetail.AVAILABLE_LANGS}
        for mode, rows in grouped.items():
            rows.sort(key=sorter)
            for lang, data in cls.format_activity(rows):
                cls(mode, lang).set(json.dumps(data, cls=JsonEncoder))
                old_keys.remove((mode, lang))

        home_acts = sum((x[:3] for x in grouped.values()), [])
        for lang, data in cls.format_activity(home_acts):
            cls(None, lang).set(json.dumps(data, cls=JsonEncoder))
            old_keys.remove((None, lang))

        for mode, lang in old_keys:
            cls(mode, lang).delete()

    @classmethod
    def format_activity(cls, activity_list):
        result = []
        total_outputs = cls.get_total_output(activity_list)
        for row in activity_list:
            if not (total_output := total_outputs.get(row.id)):
                continue
            data = {
                'id': row.id,
                'join_user_type': row.join_user_type.name,
                'mining_mode': row.mining_mode.name,
                'active_status': row.active_status.name,
                'gift_asset': row.mining_asset,
                'duration': row.duration,
                'start_date': row.start_date,
                'end_date': row.end_date,
                'total_output': total_output
            }
            result.append(data)

        act_ids = [x['id'] for x in result]
        details = MiningActivityDetail.query.filter(
            MiningActivityDetail.mining_activity_id.in_(act_ids)
        ).all()
        contents = {}
        for row in details:
            key = (row.mining_activity_id, row.lang)
            contents[key] = row

        for lang in MiningActivityDetail.AVAILABLE_LANGS:
            for row in result:
                key = (row['id'], lang)
                if not (c := contents.get(key)):
                    continue
                row['title'] = c.title
                row['cover_url'] = c.cover_url
            yield lang, result

    @classmethod
    def get_total_output(cls, activity_list: List[MiningActivity]) -> Dict[int, Decimal]:
        """ 获取挖矿活动的总产量 """
        grouped = defaultdict(list)
        for act in activity_list:
            grouped[act.mining_mode].append(act)

        result = {}
        for mode, model in (
                (MiningActivity.MiningMode.TRADE, MiningTradeConfig),
                (MiningActivity.MiningMode.PENDING, MiningPendingConfig),
                (MiningActivity.MiningMode.PLEDGE, MiningPledgeConfig),
                (MiningActivity.MiningMode.AMM, MiningAmmConfig),
        ):
            if not (acts := grouped.get(mode)):
                continue
            cfgs = model.query.filter(model.mining_activity_id.in_([x.id for x in acts])).all()
            cfgs = {x.mining_activity_id: x for x in cfgs}
            for act in acts:
                cfg = cfgs[act.id]
                if v := getattr(cfg, 'daily_output', None):
                    total_output = v * act.duration
                elif v := getattr(cfg, 'hourly_output', None):
                    total_output = v * 24 * act.duration
                else:
                    continue
                result[act.id] = total_output
        return result


class MiningActivityDetailCache(HashCache):
    """ 挖矿活动详情信息缓存 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def format_key(cls, activity_id: int, lang: Language):
        return "{}_{}".format(activity_id, lang.value)

    @classmethod
    def get(cls, activity_id: int, lang: Language) -> Optional[str]:
        key_ = cls.format_key(activity_id, lang)
        return cls().hget(key_)

    @classmethod
    def reload_one(cls, activity_id):
        row = MiningActivity.query.get(activity_id)
        cache_detail_data_map = {}
        for lang, detail_data in cls._format_detail_data(row):
            key_ = cls.format_key(row.id, lang)
            cache_detail_data_map[key_] = json.dumps(detail_data, cls=JsonEncoder)

        instance = cls()
        old_keys = {cls.format_key(row.id, lang) for lang in MiningActivityDetail.AVAILABLE_LANGS}
        if not cache_detail_data_map:
            instance.hdel(*old_keys)
            return
        instance.hmset(cache_detail_data_map)
        # 删除过期的数据
        del_keys = [i for i in old_keys if i not in cache_detail_data_map]
        if del_keys:
            instance.hdel(*del_keys)

    @classmethod
    def reload(cls):
        rows = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
        ).all()
        cache_detail_data_map = {}
        for row in rows:
            for lang, detail_data in cls._format_detail_data(row):
                key_ = cls.format_key(row.id, lang)
                cache_detail_data_map[key_] = json.dumps(detail_data, cls=JsonEncoder)

        instance = cls()
        if not cache_detail_data_map:
            instance.delete()
            return
        old_keys = instance.hkeys()
        instance.hmset(cache_detail_data_map)
        # 删除过期的数据
        del_keys = [i for i in old_keys if i not in cache_detail_data_map]
        if del_keys:
            instance.hdel(*del_keys)

    @classmethod
    def _get_total_mining_amount(cls, activity: MiningActivity) -> Decimal:
        zero = Decimal()
        today_ = today()
        if activity.end_date <= today_:
            return zero
        if activity.mining_mode == MiningActivity.MiningMode.TRADE:
            mining_amount = MiningTradeModeHistory.query.filter(
                MiningTradeModeHistory.mining_activity_id == activity.id,
                MiningTradeModeHistory.history_at >= today_
            ).with_entities(
                func.sum(MiningTradeModeHistory.trade_usd)
            ).scalar()
            return mining_amount or zero
        if activity.mining_mode == MiningActivity.MiningMode.PENDING:
            mining_amount = MiningPendingModeHistory.query.filter(
                MiningPendingModeHistory.mining_activity_id == activity.id,
                MiningPendingModeHistory.history_at >= today_
            ).with_entities(
                func.sum(MiningPendingModeHistory.score)
            ).scalar()
            return mining_amount or zero
        if activity.mining_mode == MiningActivity.MiningMode.PLEDGE:
            mining_amount = MiningUserPledgeInfo.query.filter(
                MiningUserPledgeInfo.mining_activity_id == activity.id,
            ).with_entities(
                func.sum(MiningUserPledgeInfo.pledge_amount)
            ).scalar()
            return mining_amount or zero
        if activity.mining_mode == MiningActivity.MiningMode.AMM:
            # 详情这里也读缓存
            return MiningActivityAmmCache(activity.id).get_total_liq_usd()
        return zero

    @classmethod
    def _format_detail_data(cls, activity):
        """ 构建挖矿活动的详情信息 """
        from app.business import PriceManager

        activity_id = activity.id
        total_mining_amount = cls._get_total_mining_amount(activity)
        result_markets = activity.markets.split(";") if activity.markets else []  # 空数组代表全部市场
        res = dict(
            gift_asset=activity.mining_asset,
            markets=result_markets,
            mining_mode=activity.mining_mode.name,
            total_mining_amount=total_mining_amount,
            join_user_type=activity.join_user_type.name,
            start_date=activity.start_date,
            end_date=activity.end_date,
            active_status=activity.active_status.name
        )

        today_ = today()
        finished = activity.end_date <= today_  # 活动已结束, 利率显示--

        # build config
        configs = dict()
        zero = Decimal()
        if activity.mining_mode == MiningActivity.MiningMode.AMM:
            config: MiningAmmConfig = MiningAmmConfig.query.filter(
                MiningAmmConfig.mining_activity_id == activity_id
            ).first()
            if finished:
                profit_rate = None
            else:
                # 挖矿收益率 = 每日产量市值 * 365 / 参与活动用户的总流动性市值 * 100%
                if total_mining_amount > 0:
                    # 做市的total_mining_amount 已经是usd了
                    asset_rate = PriceManager.asset_to_usd(activity.mining_asset)
                    mining_asset_daily_usd = asset_rate * config.hourly_output * 24
                    profit_rate = quantize_amount(mining_asset_daily_usd * 365 / total_mining_amount, 4)
                else:
                    profit_rate = zero
            res["amm_profit_rate"] = zero  # AMM市场年利率, 不需要了, 兼容处理
            res["total_output"] = config.hourly_output * Decimal("24") * activity.duration
            configs["hourly_output"] = config.hourly_output
            configs["user_max_liquidity_usd"] = config.user_max_liquidity_usd
        elif activity.mining_mode == MiningActivity.MiningMode.PLEDGE:
            config: MiningPledgeConfig = MiningPledgeConfig.query.filter(
                MiningPledgeConfig.mining_activity_id == activity_id
            ).first()
            asset_rates = PriceManager.assets_to_usd([activity.mining_asset, config.pledge_asset])
            total_mining_amount_usd = quantize_amount(total_mining_amount * asset_rates[config.pledge_asset], 2)
            res["total_mining_amount_usd"] = total_mining_amount_usd  # 已质押市值
            res["total_output"] = config.hourly_output * Decimal("24") * activity.duration
            configs["hourly_output"] = config.hourly_output
            configs["pledge_asset"] = config.pledge_asset
            configs["user_max_pledge_amount"] = config.user_max_pledge_amount
            configs["user_min_pledge_amount"] = config.user_min_pledge_amount
            profit_rate = None
            if not finished and total_mining_amount > 0:
                mining_asset_daily_usd = asset_rates[activity.mining_asset] * config.hourly_output * 24
                total_mining_usd = total_mining_amount * asset_rates[config.pledge_asset]
                if total_mining_usd > 0:
                    profit_rate = quantize_amount(mining_asset_daily_usd * 365 / total_mining_usd, 4)
        else:
            profit_rate = None
            if activity.mining_mode == MiningActivity.MiningMode.PENDING:
                weight_configs = MiningPendingWeightConfig.query.filter(
                    MiningPendingWeightConfig.mining_activity_id == activity_id
                ).all()
                weights = []
                config = MiningPendingConfig.query.filter(MiningPendingConfig.mining_activity_id == activity_id).first()
                for c in weight_configs:
                    c: MiningPendingWeightConfig
                    weights.append(dict(min_delta=c.min_delta, max_delta=c.max_delta, weight=c.weight))
                configs["weights"] = weights
            else:
                config = MiningTradeConfig.query.filter(MiningTradeConfig.mining_activity_id == activity_id).first()
                configs["trade_mode"] = config.trade_mode.name if config.trade_mode else None
            res["total_output"] = config.daily_output * activity.duration
            configs["daily_output"] = config.daily_output
            configs["user_daily_max_output"] = config.user_daily_max_output
            configs["business_type"] = config.business_type.name

        res["profit_rate"] = profit_rate
        res["configs"] = configs

        details = MiningActivityDetail.query.filter(
            MiningActivityDetail.mining_activity_id == activity.id,
        ).all()
        contents = {row.lang: row for row in details}
        for lang in MiningActivityDetail.AVAILABLE_LANGS:
            if not (c := contents.get(lang)):
                continue
            res['title'] = c.title
            res['cover_url'] = c.cover_url
            yield lang, res


class MiningActivityProfitPredictCache(HashCache):
    """交易挖矿、挂单挖矿当日用户收益预估"""

    ttl = 86400

    def __init__(self, activity_id: int):
        super().__init__(str(activity_id))

    def get_predict(self, user_id) -> Optional[Dict[str, str]]:
        if r := self.hget(str(user_id)):
            return json.loads(r)
        return None

    @classmethod
    def reload(cls):
        today_ = today()
        activities = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.mining_mode.in_((
                MiningActivity.MiningMode.TRADE,
                MiningActivity.MiningMode.PENDING
            )),
            MiningActivity.start_date <= today_,
            MiningActivity.end_date > today_
        ).all()
        for activity in activities:
            ret = cls._get_predict(activity, today_)
            ret = {str(k): json.dumps(v, cls=JsonEncoder) for k, v in ret.items()}
            cache = cls(activity.id)
            if not ret:
                cache.delete()
                continue
            old_keys = cache.hkeys()
            cache.hmset(ret)
            cache.expire(cls.ttl)
            del_keys = {x for x in old_keys if x not in ret}
            if del_keys:
                cache.hdel(*del_keys)

    @classmethod
    def _get_predict(cls, activity, today_) -> Dict[int, Dict]:
        from ..business.activity.mining import get_user_gift_data_with_limit, get_user_gift_data_without_limit

        if activity.mining_mode == MiningActivity.MiningMode.TRADE:
            history = MiningTradeModeHistory.query.filter(
                MiningTradeModeHistory.history_at >= today_,
                MiningTradeModeHistory.mining_activity_id == activity.id,
            ).group_by(MiningTradeModeHistory.user_id).with_entities(
                MiningTradeModeHistory.user_id, func.sum(MiningTradeModeHistory.trade_usd).label("total")
            ).all()
            config = MiningTradeConfig.query.filter(MiningTradeConfig.mining_activity_id == activity.id).first()
        elif activity.mining_mode == MiningActivity.MiningMode.PENDING:
            history = MiningPendingModeHistory.query.filter(
                MiningPendingModeHistory.history_at >= today_,
                MiningPendingModeHistory.mining_activity_id == activity.id,
            ).group_by(MiningPendingModeHistory.user_id).with_entities(
                MiningPendingModeHistory.user_id, func.sum(MiningPendingModeHistory.score).label("total")
            ).all()
            config = MiningPendingConfig.query.filter(MiningPendingConfig.mining_activity_id == activity.id).first()
        else:
            raise ValueError

        history = dict(history)
        if config.user_daily_max_output:
            ret = get_user_gift_data_with_limit(history, config.daily_output, config.user_daily_max_output)
        else:
            ret = get_user_gift_data_without_limit(history, config.daily_output)
        result = {k: dict(mining_amount=v) for k, v in history.items()}
        for k, v in ret.items():
            result[k]['daily_gift_amount'] = v
        return result


class MiningActivityUpdateTimeCache(HashCache):
    """ 交易挖矿、挂单挖矿 挖矿数据最后更新时间 """

    def __init__(self):
        super().__init__(None)

    def get_update_time(self, id_: int) -> Optional[int]:
        return int(v) if (v := self.hget(str(id_))) else None

    def set_update_time(self, id_: int, timestamp: int):
        self.hset(str(id_), str(timestamp))


class MiningActivityAmmCache(HashCache):
    """ 做市挖矿的用户流动性市值缓存 """

    ttl = 86400

    def __init__(self, activity_id: int):
        super().__init__(str(activity_id))

    def get_user_liq_usd(self, user_id: int) -> Decimal:
        """ 获取用户的流动性市值 """
        if r := self.hget(str(user_id)):
            return Decimal(r)
        return Decimal()

    def get_total_liq_usd(self) -> Decimal:
        """ 获取活动的总流动性市值 """
        if r := self.hget("ALL"):
            return Decimal(r)
        return Decimal()

    def get_mining_user_amm_info_map(self) -> Dict[int, Decimal]:
        """ 获取做市挖矿活动的所有用户的有效流动性市值 """
        liq_map = self.hgetall()
        liq_map.pop("ALL", None)
        liq_map = {int(user_id): Decimal(liq) for user_id, liq in liq_map.items()}
        return liq_map

    @classmethod
    def reload(cls):
        from app.business.activity.mining import PledgeAmmMiningHelper

        today_ = today()
        # 不限定开始时间
        activities = MiningActivity.query.filter(
            MiningActivity.status == MiningActivity.StatusType.ONLINE,
            MiningActivity.mining_mode == MiningActivity.MiningMode.AMM,
            MiningActivity.end_date > today_,
        ).all()
        for activity in activities:
            liq_usd_data = PledgeAmmMiningHelper.get_realtime_mining_user_amm_info_map(activity)
            cache = cls(activity.id)
            if not liq_usd_data:
                cache.delete()
                continue

            dump_liq_usd_data = {str(user_id): amount_to_str(liq_usd) for user_id, liq_usd in liq_usd_data.items()}
            dump_liq_usd_data["ALL"] = amount_to_str(sum(liq_usd_data.values()))
            old_keys = cache.hkeys()
            cache.hmset(dump_liq_usd_data)
            cache.expire(cls.ttl)
            del_keys = {x for x in old_keys if x not in dump_liq_usd_data}
            if del_keys:
                cache.hdel(*del_keys)


class CalendarActivityCache(StringCache):
    """活动日历展示列表"""

    @classmethod
    def reload(cls):
        now_ = now()
        rows = CalendarActivity.query.filter(
            CalendarActivity.status == CalendarActivity.StatusType.VALID,
            CalendarActivity.online_time < now_,
            CalendarActivity.offline_time > now_,
        ).all()
        _activity_list = []
        for row in rows:
            _activity_list.append(row)

        def sorter(act: CalendarActivity):
            if now_ < act.start_time:
                return (2, -act.end_time.timestamp(), -act.id)
            elif act.start_time <= now_ < act.end_time:
                return (0, -act.end_time.timestamp(), -act.id)
            else:
                return (3, -act.end_time.timestamp(), -act.id)

        old_keys = {lang.value for lang in CalendarContent.AVAILABLE_LANGS}
        _activity_list.sort(key=sorter)
        for lang, data in cls.format_activity(_activity_list):
            key_ = lang.value
            cls(key_).set(json.dumps(data, cls=JsonEncoder))
            old_keys.remove(key_)

        for mode, lang in old_keys:
            cls(mode, key_).delete()

    @classmethod
    def format_activity(cls, activity_list):

        result = []
        for row in activity_list:
            data = {
                'id': row.id,
                'start_time': row.start_time,
                'end_time': row.end_time,
                'online_time': row.online_time,
                'offline_time': row.offline_time,
                'activity_type': row.activity_type.name,
                'active_status': row.active_status.name,
                'url': row.url,
            }
            result.append(data)

        act_ids = [x['id'] for x in result]
        details = CalendarContent.query.filter(
            CalendarContent.owner_id.in_(act_ids)
        ).all()
        contents = {}
        for row in details:
            key = (row.owner_id, row.lang)
            contents[key] = row

        for lang in CalendarContent.AVAILABLE_LANGS:
            lang_result = []
            for row in result:
                key = (row['id'], lang)
                if not (c := contents.get(key)) or not c.title:
                    continue

                row['title'] = c.title
                row['url'] = c.url or row['url']
                lang_result.append(row)
            yield lang, lang_result


class AirdropActivityCache(StringCache):
    """空投活动首页展示的活动列表"""

    @classmethod
    def reload(cls):
        now_ = now()
        rows = AirdropActivity.query.filter(
            AirdropActivity.status.in_(
                [AirdropActivity.StatusType.ONLINE, AirdropActivity.StatusType.FINISHED])
        ).all()
        _activity_list = []
        for row in rows:
            _activity_list.append(row)

        def sorter(act: AirdropActivity):
            if now_ < act.start_time:
                return (2, -act.end_time.timestamp(), -act.id)
            elif act.start_time <= now_ < act.end_time:
                return (0, -act.end_time.timestamp(), -act.id)
            else:
                # 因为类型不同，结束后需要判断类型再排序
                if act.airdrop_mode == AirdropActivity.AirdropMode.RANDOM and \
                        act.status == AirdropActivity.StatusType.ONLINE:
                    return (1, -act.end_time.timestamp(), -act.id)
                else:
                    return (3, -act.end_time.timestamp(), -act.id)

        old_keys = {lang.value for lang in AirdropActivityDetail.AVAILABLE_LANGS}
        _activity_list.sort(key=sorter)
        for lang, data in cls.format_activity(_activity_list):
            key_ = lang.value
            cls(key_).set(json.dumps(data, cls=JsonEncoder))
            old_keys.remove(key_)

        for mode, lang in old_keys:
            cls(mode, key_).delete()

    @classmethod
    def get_air_amount(cls, row: AirdropActivity):
        from app.business.activity.airdrop import get_airdrop_activity_rewards
        """
        临时解决方案:
        当空投发放VIP升级券，展示逻辑和其他空投有所差异，为了临时适配活动
        临时使用硬编码的方式，将发放VIP升级券的空投的amount 返回调整成 0 （其他类型必填），
        方便前端识别并调整展示逻辑。待后续产品调整空投逻辑，恢复原本数据展示。
        """
        _, coupon_rewards = get_airdrop_activity_rewards(row.id)
        return cls.get_amount_by_coupon_rewards(coupon_rewards, row.amount)

    @classmethod
    def get_amount_by_coupon_rewards(cls, coupon_rewards, amount):
        if len(coupon_rewards) == 1 and coupon_rewards[0]['coupon_type'] == Coupon.CouponType.VIP_UPGRADE.name:
            return Decimal()
        return amount


    @classmethod
    def format_activity(cls, activity_list):
        activity_ids = [i.id for i in activity_list]
        activity_reward_type_map = cls._get_activity_rewards_map(activity_ids)

        asset_list = {i.asset for i in activity_list}
        coin_info_map = {i.code: i for i in CoinInformation.query.filter(
            CoinInformation.code.in_(asset_list)
        )}
        result = []
        for row in activity_list:
            data = {
                'id': row.id,
                'start_time': row.start_time,
                'end_time': row.end_time,
                'airdrop_mode': row.airdrop_mode.name,
                'asset': row.asset,
                'icon': AWSBucketPublic.get_file_url(coin_info_map[row.asset].icon),
                'name': coin_info_map[row.asset].name,
                'total_amount': row.total_amount,
                'total_count': row.total_count,
                'duration': row.duration,
                'amount': cls.get_air_amount(row),
                'is_active': row.is_active,
                'status': row.status.name,
                'label_type': row.label_type.name,
                'estimate_type': row.estimate_type.name,
                'reward_type': activity_reward_type_map.get(row.id),
            }
            result.append(data)

        act_ids = [x['id'] for x in result]
        details = AirdropActivityDetail.query.filter(
            AirdropActivityDetail.airdrop_activity_id.in_(act_ids)
        ).all()
        contents = {}
        for row in details:
            key = (row.airdrop_activity_id, row.lang)
            contents[key] = row

        for lang in AirdropActivityDetail.AVAILABLE_LANGS:
            for row in result:
                key = (row['id'], lang)
                if not (c := contents.get(key)):
                    continue
                row['summary'] = c.summary
                row['cover_url'] = c.cover_url
                row['title'] = c.title
            yield lang, result

    @classmethod
    def _get_activity_rewards_map(cls, activity_ids):
        rewards = AirdropActivityReward.query.filter(
            AirdropActivityReward.airdrop_activity_id.in_(activity_ids)
        ).with_entities(
            AirdropActivityReward.airdrop_activity_id,
            AirdropActivityReward.type,
        ).all()
        activity_rewards_map = defaultdict(lambda: {
            'asset_reward_count': int(),
            'coupon_reward_count': int(),
        })
        for r in rewards:
            if r.type == AirdropActivityReward.Type.COUPON:
                activity_rewards_map[r.airdrop_activity_id]['coupon_reward_count'] += 1
            else:
                activity_rewards_map[r.airdrop_activity_id]['asset_reward_count'] += 1
        activity_reward_type_map = {}
        for ac_id, rewards_data in activity_rewards_map.items():
            if rewards_data['coupon_reward_count'] and rewards_data['asset_reward_count']:
                reward_type = 'Mix'
            elif rewards_data['asset_reward_count']:
                reward_type = 'Asset'
            else:
                reward_type = 'Coupon'
            activity_reward_type_map[ac_id] = reward_type
        return activity_reward_type_map


class AirdropActivityDetailCache(HashCache):
    """ 空投活动详情信息缓存 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def format_key(cls, activity_id: int, lang: Language):
        return "{}_{}".format(activity_id, lang.value)

    @classmethod
    def get(cls, activity_id: int, lang: Language) -> Optional[str]:
        key_ = cls.format_key(activity_id, lang)
        return cls().hget(key_)

    @classmethod
    def reload(cls):
        rows = AirdropActivity.query.filter(
            AirdropActivity.status.in_(
                [AirdropActivity.StatusType.ONLINE, AirdropActivity.StatusType.FINISHED])
        ).all()
        cache_detail_data_map = {}
        for row in rows:
            for lang, detail_data in cls._format_detail_data(row):
                key_ = cls.format_key(row.id, lang)
                cache_detail_data_map[key_] = json.dumps(detail_data, cls=JsonEncoder)

        instance = cls()
        if not cache_detail_data_map:
            instance.delete()
            return
        old_keys = instance.hkeys()
        instance.hmset(cache_detail_data_map)
        # 删除过期的数据
        del_keys = [i for i in old_keys if i not in cache_detail_data_map]
        if del_keys:
            instance.hdel(*del_keys)

    @classmethod
    def _get_title(cls, label_type: AirdropActivity.LabelType, detail: AirdropActivityDetail,
                   coin_info: CoinInformation):
        if label_type == AirdropActivity.LabelType.ASSET:
            return coin_info.name
        return detail.title

    @classmethod
    def _format_detail_data(cls, activity: AirdropActivity):
        """ 构建空投活动的详情信息 """
        from app.business.activity.airdrop import get_airdrop_activity_rewards

        coin_info = CoinInformation.query.filter(
            CoinInformation.code == activity.asset
        ).first()
        explorer_query = CoinExplorer.query.filter(
            CoinExplorer.status == CoinExplorer.Status.VALID,
            CoinExplorer.coin_info_id == coin_info.id,
        ).all()
        res = dict(
            asset=activity.asset,
            airdrop_mode=activity.airdrop_mode.name,
            start_time=activity.start_time,
            end_time=activity.end_time,
            more_reward_url=activity.more_reward_url,
            status=activity.status.name,
            amount=activity.amount,
            total_amount=activity.total_amount,
            total_count=activity.total_count,
            is_active=activity.is_active,
            icon=AWSBucketPublic.get_file_url(coin_info.icon),
            name=coin_info.name,
            white_paper_url=coin_info.white_paper,
            website_url=coin_info.official_website,
            browser_url=coin_info.explorer,
            src_url=coin_info.source_code,
            social_url=coin_info.social_url,
            assets=activity.assets,
            info_url=activity.info_url,
            label_type=activity.label_type.name,
            estimate_type=activity.estimate_type.name,
            explorers=[dict(name=i.name, url=i.url) for i in explorer_query],
        )

        asset_rewards, coupon_rewards = get_airdrop_activity_rewards(activity.id)
        res['asset_rewards'] = asset_rewards
        res['coupon_rewards'] = coupon_rewards
        res['coupon_can_fail'] = cls.check_receive_coupon_can_fail(coupon_rewards)

        conditions = AirdropActivityCondition.query.filter(
            AirdropActivityCondition.airdrop_activity_id == activity.id
        ).all()
        res['conditions'] = {i.key: i.value for i in conditions}
        res['conditions'].update(json.loads(res['conditions'].pop(ActivityCondition.ConditionKeys.HOLDING.value)))
        res['conditions'].update(json.loads(res['conditions'].pop(ActivityCondition.ConditionKeys.BALANCE_VALUE.value)))
        res['conditions'].update(json.loads(res['conditions'].pop(ActivityCondition.ConditionKeys.TRADE_VALUE.value)))
        if (registered_key := ActivityCondition.ConditionKeys.REGISTERED_VALUE.value) in res['conditions']:
            res['conditions'].update(json.loads(res['conditions'].pop(registered_key)))
        if (used_key := ActivityCondition.ConditionKeys.USED_VALUE.value) in res['conditions']:
            value = res['conditions'].pop(used_key)
            res['conditions'][used_key] = value.split(",") if value else []

        details = AirdropActivityDetail.query.filter(
            AirdropActivityDetail.airdrop_activity_id == activity.id,
        ).all()
        contents = {row.lang: row for row in details}
        for lang in AirdropActivityDetail.AVAILABLE_LANGS:
            if not (c := contents.get(lang)):
                continue
            res['title'] = cls._get_title(activity.label_type, c, coin_info)
            res['summary'] = c.summary
            res['cover_url'] = c.cover_url
            res['introductions'] = c.introductions
            res['video_url'] = c.video_url
            yield lang, res

    @classmethod
    def check_receive_coupon_can_fail(cls, coupon_rewards):
        from app.business.coupon.base import get_coupon_service

        for r in coupon_rewards:
            coupon_server = get_coupon_service(r["coupon_type"])
            if coupon_server.is_auto_use:  # 自动使用的卡券不可以领取多张
                return True
        return False


class AirdropActivityQuestionBankCache(HashCache):
    """ 空投活动题库缓存 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def format_key(cls, activity_id: int, lang: Language):
        return "{}_{}".format(activity_id, lang.value)

    @classmethod
    def get(cls, activity_id: int, lang: Language) -> Optional[str]:
        key_ = cls.format_key(activity_id, lang)
        return cls().hget(key_)

    @classmethod
    def reload(cls):
        rows = AirdropActivity.query.filter(
            AirdropActivity.status == AirdropActivity.StatusType.ONLINE,
        ).all()
        cache_detail_data_map = {}
        for row in rows:
            for lang, detail_data in cls._format_detail_data(row):
                key_ = cls.format_key(row.id, lang)
                cache_detail_data_map[key_] = json.dumps(detail_data, cls=JsonEncoder)

        instance = cls()
        if not cache_detail_data_map:
            instance.delete()
            return
        old_keys = instance.hkeys()
        instance.hmset(cache_detail_data_map)
        # 删除过期的数据
        del_keys = [i for i in old_keys if i not in cache_detail_data_map]
        if del_keys:
            instance.hdel(*del_keys)

    @classmethod
    def _format_detail_data(cls, activity):
        res = defaultdict(list)
        details = AirdropActivityQuestionBank.query.filter(
            AirdropActivityQuestionBank.airdrop_activity_id == activity.id,
            AirdropActivityQuestionBank.status == AirdropActivityQuestionBank.StatusType.PASSED,
        ).all()
        for row in details:
            res[row.lang.value].append(
                dict(question=row.question,
                     options=row.options,
                     answer=row.answer,
                     number=row.id,
                     answer_analysis=row.answer_analysis,
                     )
            )
        for lang, data in res.items():
            yield Language(lang), res[lang]


class AirdropActivityUserHasAnsweredCache(SetCache):
    """ 空投活动用户完成答题缓存 """

    def __init__(self, activity_id):
        super().__init__(str(activity_id))

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))

    def add_user(self, user_id: int):
        self.sadd(str(user_id))


class AirdropActivityLotteryCache(HashCache):
    """ 空投活动抽奖号自增缓存 """

    def __init__(self):
        super().__init__(None)


class AirdropActivityReceivedCountCache(HashCache):
    """ 空投活动限时活动已领取人数缓存 """

    def __init__(self):
        super().__init__(None)


class DiscountActivityCache(StringCache):
    """Dibs活动首页展示的活动列表"""

    @classmethod
    def reload(cls):
        rows = DiscountActivity.query.filter(
            DiscountActivity.status.in_(
                [DiscountActivity.StatusType.ONLINE, DiscountActivity.StatusType.FINISHED])
        ).all()
        _activity_list = []
        for row in rows:
            _activity_list.append(row)

        old_keys = {lang.value for lang in DiscountActivityDetail.AVAILABLE_LANGS}
        for lang, data in cls.format_activity(_activity_list):
            key_ = lang.value
            cls(key_).set(json.dumps(data, cls=JsonEncoder))
            old_keys.remove(key_)

        for mode, lang in old_keys:
            cls(mode, key_).delete()

    @classmethod
    def format_activity(cls, activity_list):

        asset_list = {i.asset for i in activity_list}
        coin_info_map = {i.code: i for i in CoinInformation.query.filter(
            CoinInformation.code.in_(asset_list)
        )}

        result = []
        for row in activity_list:
            data = {
                'id': row.id,
                'start_time': row.start_time,
                'end_time': row.end_time,
                'asset': row.asset,
                'name': coin_info_map[row.asset].name,
                'total_amount': row.total_amount,
                'total_count': row.total_count,
                'amount': row.amount,
                'status': row.status.name,
            }
            result.append(data)

        act_ids = [x['id'] for x in result]
        details = DiscountActivityDetail.query.filter(
            DiscountActivityDetail.discount_activity_id.in_(act_ids)
        ).all()
        contents = {}
        for row in details:
            key = (row.discount_activity_id, row.lang)
            contents[key] = row

        for lang in DiscountActivityDetail.AVAILABLE_LANGS:
            for row in result:
                key = (row['id'], lang)
                if not (c := contents.get(key)):
                    continue
                row['title'] = c.title
            yield lang, result


class DiscountActivityDetailCache(HashCache):
    """ Dibs活动详情信息缓存 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def format_key(cls, activity_id: int, lang: Language):
        return "{}_{}".format(activity_id, lang.value)

    @classmethod
    def get(cls, activity_id: int, lang: Language) -> Optional[str]:
        key_ = cls.format_key(activity_id, lang)
        return cls().hget(key_)

    @classmethod
    def reload(cls):
        rows = DiscountActivity.query.filter(
            DiscountActivity.status.in_(
                [DiscountActivity.StatusType.ONLINE, DiscountActivity.StatusType.FINISHED])
        ).all()
        cache_detail_data_map = {}
        for row in rows:
            for lang, detail_data in cls._format_detail_data(row):
                key_ = cls.format_key(row.id, lang)
                cache_detail_data_map[key_] = json.dumps(detail_data, cls=JsonEncoder)

        instance = cls()
        if not cache_detail_data_map:
            instance.delete()
            return
        old_keys = instance.hkeys()
        instance.hmset(cache_detail_data_map)
        # 删除过期的数据
        del_keys = [i for i in old_keys if i not in cache_detail_data_map]
        if del_keys:
            instance.hdel(*del_keys)

    @classmethod
    def _get_title(cls, detail: AirdropActivityDetail):
        return detail.title

    @classmethod
    def _format_detail_data(cls, activity: DiscountActivity):
        """ 构建活动的详情信息 """
        res = dict(
            asset=activity.asset,
            start_time=activity.start_time,
            end_time=activity.end_time,
            status=activity.status.name,
            amount=activity.amount,
            total_amount=activity.total_amount,
            total_count=activity.total_count,
            price=activity.price,
            discount_price=activity.discount_price,
            pay_amount=activity.pay_amount,
            discount=activity.discount_type.value,
            pay_asset=DiscountActivity.PAY_ASSET,
            market=activity.market,
        )

        conditions = DiscountActivityCondition.query.filter(
            DiscountActivityCondition.discount_activity_id == activity.id
        ).all()
        res['conditions'] = {i.key: i.value for i in conditions}
        res['conditions'].update(json.loads(res['conditions'].pop(ActivityCondition.ConditionKeys.HOLDING.value)))
        res['conditions'].update(json.loads(res['conditions'].pop(ActivityCondition.ConditionKeys.BALANCE_VALUE.value)))
        res['conditions'].update(json.loads(res['conditions'].pop(ActivityCondition.ConditionKeys.TRADE_VALUE.value)))
        if (registered_key := ActivityCondition.ConditionKeys.REGISTERED_VALUE.value) in res['conditions']:
            res['conditions'].update(json.loads(res['conditions'].pop(registered_key)))
        if (used_key := ActivityCondition.ConditionKeys.USED_VALUE.value) in res['conditions']:
            value = res['conditions'].pop(used_key)
            res['conditions'][used_key] = value.split(",") if value else []

        details = DiscountActivityDetail.query.filter(
            DiscountActivityDetail.discount_activity_id == activity.id,
        ).all()
        contents = {row.lang: row for row in details}
        for lang in DiscountActivityDetail.AVAILABLE_LANGS:
            if not (c := contents.get(lang)):
                continue
            res['title'] = cls._get_title(c)
            yield lang, res


class DiscountActivityLotteryCache(HashCache):
    """ Dibs活动抽奖号自增缓存 """

    def __init__(self):
        super().__init__(None)


class UserActivityTradeValueHourCache(HashCache):
    TTL = 3600

    def __init__(self, st: datetime.datetime, et: datetime.datetime, trade_type_range: list):
        key = f"{st.timestamp()}-{et.timestamp()}-{'-'.join(trade_type_range)}"
        super().__init__(key)

    def read_by_user(self, user_id: int):
        return self.hget(str(user_id))

    def set_by_user(self, user_id: int, value: Decimal):
        self.hset(str(user_id), str(value))
        self.expire(self.TTL)


class NewAssetRecentCache(StringCache):
    """新币专区最近上线缓存"""

    def read(self) -> list:

        data = super().read()
        if not data:
            return []
        data = json.loads(data)
        assets = [i["code"] for i in data]
        prices = AssetRealTimePriceCache().get_asset_real_time_prices(assets)
        rate_cache = AssetRealTimeRateCache().get_asset_real_time_rates(assets)
        for item in data:
            asset = item["code"]
            if price_usd := prices.get(asset):
                item["price_usd"] = price_usd
            if rate := rate_cache.get(asset):
                item["change_rate"] = rate
        return data

    @classmethod
    def get_new_coin_rows(cls) -> list[CoinInformation]:
        now_ = now()
        start_time = now_ - timedelta(days=90)
        rows = CoinInformation.query.filter(
            CoinInformation.online_time >= start_time,
            CoinInformation.online_time < now_,
            CoinInformation.status == CoinInformation.Status.VALID,
        ).order_by(CoinInformation.online_time.desc()).all()
        return rows

    @classmethod
    def reload(cls):
        from app.business.kline import get_all_asset_quotes_data
        assets_data = get_all_asset_quotes_data()
        rows = cls.get_new_coin_rows()

        _online_coin_list = []
        for row in rows:
            coin_code = row.code
            asset_data = assets_data.get(coin_code)
            if not asset_data:
                continue
            _online_coin_list.append(dict(
                **asset_data,
                rank=asset_data['circulation_usd_rank'],
                code=coin_code,
            ))
        old_keys = {lang.value for lang in CoinInformation.AVAILABLE_LANGS}
        assets_tag_trans = AssetTagsCache().read()
        exclude_tag_ids = AssetTag.get_auto_tag_ids()  # 最近上线币种的标签列表排除杠杆和amm标签
        for lang in CoinInformation.AVAILABLE_LANGS:
            for online_coin in _online_coin_list:
                coin_code = online_coin['code']
                trans_trans_ = json.loads(assets_tag_trans.get(coin_code, '[]'))
                online_coin['tags'] = [i['trans'].get(lang.value) for i in
                                       trans_trans_ if i['id'] not in exclude_tag_ids]  # 最近上线币种的标签列表排除杠杆和amm标签
            data = _online_coin_list
            cls(lang.value).set(json.dumps(data, cls=JsonEncoder))
            old_keys.remove(lang.value)

        for lang in old_keys:
            cls(lang).delete()


class NewAssetSoonCache(StringCache):
    """新币专区即将上线缓存"""

    @classmethod
    def reload(cls):
        now_ = now()
        new_assets_query = NewAsset.query.filter(NewAsset.status == NewAsset.StatusType.VALID).all()
        new_asset = {i.asset for i in new_assets_query}

        rows = CoinInformation.query.filter(
            CoinInformation.code.in_(new_asset),
            CoinInformation.status == CoinInformation.Status.VALID
        ).order_by(CoinInformation.online_time.asc()).all()

        coin_map = {i.id: i for i in rows}
        # 产品要求没有上架时间的币种也要展示
        _new_coin_ids = [i.id for i in rows if not i.online_time or i.online_time > now_]
        info_trans = CoinInformationTrans.query.filter(
            CoinInformationTrans.coin_information_id.in_(_new_coin_ids)
        ).all()
        info_trans_map = defaultdict(dict)
        for item in info_trans:
            info_trans_map[item.coin_information_id][item.lang.value] = item.description

        old_keys = {lang.value for lang in CoinInformation.AVAILABLE_LANGS}
        for lang in CoinInformation.AVAILABLE_LANGS:
            _new_coin_list = []
            for coin_id in _new_coin_ids:
                _new_coin_list.append(dict(
                    code=coin_map[coin_id].code,
                    id=coin_id,
                    online_time=coin_map[coin_id].online_time,
                    full_name=coin_map[coin_id].name,
                    description=info_trans_map[coin_id].get(lang.value, ''),
                    logo=CoinInformation.get_icon_url(coin_map[coin_id].icon),
                    thumbnail_logo=CoinInformation.get_thumbnail_icon_url(coin_map[coin_id].thumbnail_icon, coin_map[coin_id].icon),
                ))
            has_time_list = sorted([i for i in _new_coin_list if i['online_time']], key=lambda x: x['online_time'])
            # 没有上架时间的排后面
            has_time_list.extend(sorted([i for i in _new_coin_list if not i['online_time']], key=lambda x: x['id']))
            cls(lang.value).set(json.dumps(has_time_list, cls=JsonEncoder))
            old_keys.remove(lang.value)

        for lang in old_keys:
            cls(lang).delete()


class ToOnlineAssetCache(StringCache):
    """竞价中，倒计时币种"""

    def __init__(self, lang: Language):
        super().__init__(lang.value)

    @classmethod
    def reload(cls):
        from app.business.kline import get_to_online_asset_list
        data = get_to_online_asset_list()
        assets_tag_trans = AssetTagsCache().read()
        exclude_tag_ids = AssetTag.get_auto_tag_ids()
        for lang in CoinInformation.AVAILABLE_LANGS:
            for item in data:
                coin_code = item['asset']
                trans_trans_ = json.loads(assets_tag_trans.get(coin_code, '[]'))
                # 最近上线币种的标签列表排除杠杆和amm标签
                item['tags'] = [i['trans'].get(lang.value) for i in
                                trans_trans_ if i['id'] not in exclude_tag_ids]
            cls(lang).set(json.dumps(data, cls=JsonEncoder))

    def read_data(self):
        data = self.read()
        if not data:
            return []
        return json.loads(data)


class IeoActivityCache(StringCache):
    """投资活动首页展示的活动列表"""

    @classmethod
    def reload(cls):
        now_ = now()
        rows = IeoActivity.query.filter(
            IeoActivity.status.in_([IeoActivity.StatusType.ONLINE, IeoActivity.StatusType.FINISHED]),
            IeoActivity.ready_time < now_,
        ).all()
        _activity_list = []
        for row in rows:
            _activity_list.append(row)

        def sorter(act: IeoActivity):
            if now_ < act.start_time:
                return (1, -act.ready_time.timestamp(), -act.id)
            if act.start_time <= now_ < act.end_time:
                return (0, -act.ready_time.timestamp(), -act.id)
            if act.status == IeoActivity.StatusType.FINISHED:
                return (3, -act.ready_time.timestamp(), -act.id)
            return (2, -act.end_time.timestamp(), -act.id)

        old_keys = {lang.value for lang in IeoActivityDetail.AVAILABLE_LANGS}
        _activity_list.sort(key=sorter)

        for lang, data in cls.format_activity(_activity_list):
            key_ = lang.value
            cls(key_).set(json.dumps(data, cls=JsonEncoder))
            old_keys.remove(key_)

        for mode, lang in old_keys:
            cls(mode, key_).delete()

    @classmethod
    def format_activity(cls, activity_list):

        activity_ids = [i.id for i in activity_list]
        asset_info_map = {i.ieo_activity_id: i for i in IeoAssetInformation.query.filter(
            IeoAssetInformation.ieo_activity_id.in_(activity_ids)).all()}
        result = []
        for row in activity_list:
            data = {
                'id': row.id,
                'start_time': row.start_time,
                'end_time': row.end_time,
                'subscribe_asset': row.subscribe_asset,
                'subscribe_total_amount': row.subscribe_total_amount,
                'subscribe_total_count': row.subscribe_total_count,
                'max_subscribe_count': row.max_subscribe_count,
                'subscribe_amount': row.subscribe_amount,
                'pledge_asset': row.pledge_asset,
                'pledge_amount': row.pledge_amount,
                'pay_asset': row.pay_asset,
                'pay_amount': row.pay_amount,
                'cover_url': row.cover_url,
                'duration': row.duration,
                'is_active': row.is_active,
                'active_status': row.active_status.name,
                'status': row.status.name,
                'asset_icon': AWSBucketPublic.get_file_url(asset_info_map[row.id].icon),
                'asset_name': asset_info_map[row.id].name,
            }
            result.append(data)

        act_ids = [x['id'] for x in result]
        details = IeoActivityDetail.query.filter(
            IeoActivityDetail.ieo_activity_id.in_(act_ids)
        ).all()
        contents = {}
        for row in details:
            key = (row.ieo_activity_id, row.lang)
            contents[key] = row

        for lang in IeoActivityDetail.AVAILABLE_LANGS:
            for row in result:
                key = (row['id'], lang)
                if not (c := contents.get(key)):
                    continue
                row['summary'] = c.summary
                row['introduction'] = c.introduction
                row['report_url'] = c.report_url
            yield lang, result


class IeoActivityDetailCache(HashCache):
    """ 投资活动详情信息缓存 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def format_key(cls, activity_id: int, lang: Language):
        return "{}_{}".format(activity_id, lang.value)

    @classmethod
    def get(cls, activity_id: int, lang: Language) -> Optional[str]:
        key_ = cls.format_key(activity_id, lang)
        return cls().hget(key_)

    @classmethod
    def reload(cls):
        now_ = now()
        rows = IeoActivity.query.filter(
            IeoActivity.status.in_([IeoActivity.StatusType.ONLINE, IeoActivity.StatusType.FINISHED]),
            IeoActivity.ready_time < now_,
        ).all()
        cache_detail_data_map = {}
        for row in rows:
            for lang, detail_data in cls._format_detail_data(row):
                key_ = cls.format_key(row.id, lang)
                cache_detail_data_map[key_] = json.dumps(detail_data, cls=JsonEncoder)

        instance = cls()
        if not cache_detail_data_map:
            instance.delete()
            return
        old_keys = instance.hkeys()
        instance.hmset(cache_detail_data_map)
        # 删除过期的数据
        del_keys = [i for i in old_keys if i not in cache_detail_data_map]
        if del_keys:
            instance.hdel(*del_keys)

    @classmethod
    def _format_detail_data(cls, activity):
        asset_info = IeoAssetInformation.query.filter(
            IeoAssetInformation.ieo_activity_id == activity.id
        ).first()
        order_list = IeoActivityOrder.query.filter(
            IeoActivityOrder.ieo_activity_id == activity.id,
            IeoActivityOrder.status.in_([IeoActivityOrder.StatusType.VALID, IeoActivityOrder.StatusType.FINISHED]),
        ).all()
        cur_subscribe_count = Decimal(sum([order.subscribe_count for order in order_list]))
        cur_pledge_amount = cur_subscribe_count * activity.pledge_amount
        has_subscribe_total_amount = cur_subscribe_count * activity.subscribe_amount
        subscribe_ratio = quantize_amount(cur_subscribe_count / activity.subscribe_total_count, 4)
        lottery_ratio = quantize_amount(activity.subscribe_total_count / cur_subscribe_count,
                                        4) if cur_subscribe_count else 0
        res = dict(
            start_time=activity.start_time,
            end_time=activity.end_time,
            subscribe_asset=activity.subscribe_asset,
            subscribe_total_amount=activity.subscribe_total_amount,
            subscribe_total_count=activity.subscribe_total_count,
            cur_subscribe_count=cur_subscribe_count,
            subscribe_amount=activity.subscribe_amount,
            max_subscribe_count=activity.max_subscribe_count,
            pledge_asset=activity.pledge_asset,
            pledge_amount=activity.pledge_amount,
            pay_asset=activity.pay_asset,
            pay_amount=activity.pay_amount,
            cur_pledge_amount=cur_pledge_amount,
            has_subscribe_total_amount=has_subscribe_total_amount,
            lottery_ratio=min(lottery_ratio, 1),  # 兼容已申购份数<总份数的中签率>100%的情况
            subscribe_ratio=subscribe_ratio,
            black_country_list=json.loads(activity.black_country_list),
            cover_url=activity.cover_url,
            duration=activity.duration,
            active_status=activity.active_status.name,
            is_active=activity.is_active,
            asset_icon=AWSBucketPublic.get_file_url(asset_info.icon) if asset_info.icon else '',
            asset_project_icon=AWSBucketPublic.get_file_url(
                asset_info.project_icon) if asset_info.project_icon else '',
            asset_name=asset_info.name,
            white_paper_url=asset_info.white_paper,
            website_url=asset_info.official_website,
            browser_url=asset_info.explorer,
            src_url=asset_info.source_code,
            social_url=asset_info.social_url,
            rule_detail_url=asset_info.rule_detail_url,
            rule_url=asset_info.rule_url,
            issued_time=asset_info.issued_time,
            total_supply=asset_info.total_supply,
            circulation=asset_info.circulation,
            total_supply_value=asset_info.total_supply_value,
            explorers=json.loads(asset_info.explorer_list),
        )

        conditions = IeoActivityCondition.query.filter(
            IeoActivityCondition.ieo_activity_id == activity.id,
            IeoActivityCondition.key.in_([
                ActivityCondition.ConditionKeys.VIP.value,
                ActivityCondition.ConditionKeys.KYC.value, ])
        ).all()
        res['conditions'] = {i.key: i.value for i in conditions}

        details = IeoActivityDetail.query.filter(
            IeoActivityDetail.ieo_activity_id == activity.id,
        ).all()
        contents = {row.lang: row for row in details}
        for lang in IeoActivityDetail.AVAILABLE_LANGS:
            if not (c := contents.get(lang)):
                continue
            res['summary'] = c.summary
            res['introduction'] = c.introduction
            res['report_url'] = c.report_url
            yield lang, res


class IeoActivityUserSignedContractCache(SetCache):
    """ 投资活动用户签署协议缓存 """

    def __init__(self, activity_id):
        super().__init__(str(activity_id))

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))

    def add_user(self, user_id: int):
        self.sadd(str(user_id))


class IeoActivityLotteryCache(HashCache):
    """ 投资活动抽奖号自增缓存 """

    def __init__(self):
        super().__init__(None)


class SendCouponNoticeCache(SetCache):
    """卡劵已发放消息缓存"""

    def __init__(self, pool_id: int, send_type: str, send_time: str):
        super().__init__(f"{send_time}:{send_type}:{pool_id}")

    def get_users(self) -> Set[int]:
        return {int(s) for s in self.smembers()}

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))

    def add_user(self, user_id: int):
        self.sadd(str(user_id))

    def add_users(self, user_ids: list):
        self.sadd(*[str(i) for i in user_ids])


class FriendGiftCouponCache(HashCache):
    """好有礼用户过期时间缓存"""

    def __init__(self, pool_id: int):
        super().__init__(str(pool_id))

    def set_user_expire_time(self, user_id, expire_time: datetime):
        self.hset(str(user_id), datetime_to_time(expire_time))

    def set_uses_expire_days(self, user_ids, expire_days: int):
        expire_time = now() + timedelta(days=expire_days)
        add_user_ids = set(user_ids) - set(self.hkeys())
        self.hmset({user_id: datetime_to_time(expire_time) for user_id in add_user_ids})

    def get_expire_time(self, user_id):
        str_time = self.hget(str(user_id))
        if not str_time:
            return None
        return timestamp_to_datetime(int(str_time))

    def get_pool_expire_time(self):
        expire_mapper = self.hgetall()
        if not expire_mapper:
            return None
        return max(map(lambda x: timestamp_to_datetime(int(x)), expire_mapper.values()))


class FriendGiftMulLimitCouponCache(HashCache):
    """好有礼-多人邀请用户限制缓存"""

    def __init__(self, pool_id: int):
        super().__init__(str(pool_id))

    def add(self, referrer_id: int):
        self.hincrby(str(referrer_id), 1)


class AdminFileUploadChunk(StringCache):
    """admin文件上传切片"""

    def __init__(self, _key):
        super().__init__(f'{_key}')


class CouponCache(HashCache):
    """卡券详情缓存"""

    def __init__(self):
        super().__init__(None)

    @classmethod
    def _dump_coupon_to_dict(cls, coupons):
        from ..business.coupon.utils import CouponTool
        coupon_type_mapper = defaultdict(list)
        for c in coupons:
            coupon_type_mapper[c.coupon_type].append(c.id)
        coupon_detail_mapper = CouponTool.dump_coupon_data(coupon_type_mapper)
        coupon_mapper = {}
        for coupon in coupons:  # type: Coupon
            coupon_mapper[coupon.id] = dict(
                id=coupon.id,
                coupon_type=coupon.coupon_type.name,
                value=amount_to_str(coupon.value),
                value_type=coupon.value_type,
                status=coupon.status.name,
                receivable_days=coupon.receivable_days,
                activation_days=coupon.activation_days,
                usable_days=coupon.usable_days,
                remark=coupon.remark,
                extra=coupon_detail_mapper.get(coupon.id, {})
            )
        return coupon_mapper

    @classmethod
    def reload_all(cls):
        coupons = Coupon.query.all()
        coupon_mapper = cls._dump_coupon_to_dict(coupons)
        cache = cls()
        current_coupon_ids = {int(i) for i in cache.hkeys()}
        delete_coupons_ids = current_coupon_ids - set(coupon_mapper.keys())
        if delete_coupons_ids:
            cache.hdel(*delete_coupons_ids)
        if coupon_mapper:
            cache.hmset({id_: json.dumps(v) for id_, v in coupon_mapper.items()})

    @classmethod
    def update_one(cls, coupon):
        cache = cls()
        coupon_mapper = cls._dump_coupon_to_dict([coupon])
        if value := coupon_mapper.get(coupon.id):
            cache.hset(coupon.id, json.dumps(value))

    @classmethod
    def get_coupon_info(cls, coupon_id: int):
        cache = cls()
        return json.loads(cache.hget(str(coupon_id)))

    @classmethod
    def get_coupon_mapper(cls, coupon_ids=None, have_close=True):
        cache = cls()
        if not cache.hlen():
            return {}
        coupon_mapper = {}
        for id_, value in cls().hgetall().items():
            coupon_id = int(id_)
            json_value = json.loads(value)
            if not have_close and json_value['status'] == Coupon.CouponStatus.CLOSE.name:
                continue
            if coupon_ids and coupon_id not in coupon_ids:
                continue
            coupon_mapper[coupon_id] = json_value

        return coupon_mapper

    @classmethod
    def get_coupon_type_mapper(cls):
        return {int(k): Coupon.CouponType[v['coupon_type']] for k, v in cls.get_coupon_mapper().items()}

    @classmethod
    def get_coupon_type(cls, coupon_id):
        cache = cls()
        coupon_data = cache.hget(str(coupon_id))
        if not coupon_data:
            return None
        json_data = json.loads(coupon_data)
        return Coupon.CouponType[json_data['coupon_type']]

    @classmethod
    def get_coupon_type_ids(cls, coupon_type):
        coupon_type_ids_mapper = defaultdict(list)
        for coupon_id, _type in cls.get_coupon_type_mapper().items():
            coupon_type_ids_mapper[_type].append(coupon_id)
        return coupon_type_ids_mapper[coupon_type]


class CouponPoolCache(HashCache):
    """卡劵池详情缓存"""

    def __init__(self):
        super().__init__(None)

    @classmethod
    def _get_pool_status(cls, pool: CouponPool):
        from app.business.coupon.pool import CouponPoolStatus
        if pool.send_count >= pool.total_count:
            return CouponPoolStatus.ENDING.value
        if pool.status in [CouponPool.Status.VALID, CouponPool.Status.PAUSED]:
            return CouponPoolStatus.AVAILABLE.value

    def get_pool_data(self, pool_id: int):
        data = self.hget(str(pool_id))
        if data:
            return json.loads(data)
        return None

    def set_pool_data(self, pool_id: int, pool_data: Dict[str, Any]):
        self.hset(str(pool_id), json.dumps(pool_data))

    def set_pools(self, pool_mapper: Dict[int, Any]):
        self.hmset({str(_id): json.dumps(data) for _id, data in pool_mapper.items()})

    @classmethod
    def get_cache_pools(cls):
        cache = cls()
        if cache.hlen() == 0:
            return []
        return [json.loads(item) for _, item in cache.hgetall().items()]

    @classmethod
    def remove_pool(cls, pool_id: int):
        cache = cls()
        cache.hdel(str(pool_id))

    @classmethod
    def _not_show_dynamic_user_type(cls):
        return [
            CouponApply.DynamicUser.FIVE_ACTIVITY.name,
            CouponApply.DynamicUser.SIX_ACTIVITY_GIFT.name,
            CouponApply.DynamicUser.PERPETUAL_BENEFITS.name,
            CouponApply.DynamicUser.NOVICE_PREFECTURE.name,
            CouponApply.DynamicUser.AIRDROP_ACTIVITY.name,
        ]

    @classmethod
    def _need_check_qualification_user_type(cls):
        return [CouponApply.SendUserType.DYNAMIC.name, CouponApply.SendUserType.SOME.name]

    @classmethod
    def reload(cls):
        from app.business.coupon.utils import CouponTool
        cache = cls()
        query = CouponPool.query.join(Coupon).filter(
            CouponPool.status == CouponPool.Status.VALID
        ).with_entities(
            Coupon.coupon_type,
            Coupon.value_type,
            Coupon.value,
            Coupon.id.label("coupon_id"),
            Coupon.usable_days,
            Coupon.receivable_days,
            Coupon.activation_days,
            CouponPool.apply_coupon_id,
            CouponPool.id,
            CouponPool.send_count,
            CouponPool.total_count,
            CouponPool.send_user_type,
            CouponPool.expired_at,
            CouponPool.status,
            CouponPool.dynamic_user_type
        ).all()
        applies_ids = [i.apply_coupon_id for i in query]
        applies_query = CouponApply.query.filter(
            CouponApply.id.in_(applies_ids)
        ).with_entities(
            CouponApply.id,
            CouponApply.source,
            CouponApply.origin_id,
        ).all()
        apply_source_mapper = {x.id: x.source for x in applies_query}
        apply_to_origins = {x.id: x.origin_id for x in applies_query}
        apply_drafts = CouponApplyDraft.query.with_entities(
            CouponApplyDraft.id,
            CouponApplyDraft.invitees_limit,
        ).filter(
            CouponApplyDraft.id.in_(apply_to_origins.values())
        ).all()
        apply_drafts = dict(apply_drafts)
        coupon_type_mapper = defaultdict(list)
        for item in query:
            coupon_type_mapper[item.coupon_type].append(item.coupon_id)
        coupon_detail_mapper = CouponTool.dump_coupon_data(coupon_type_mapper)
        pool_mapper = {}
        for pool in query:
            source = apply_source_mapper[pool.apply_coupon_id]
            # 兑换券没有展示逻辑
            if source == CouponApply.Source.EXCHANGE:
                continue
            invitees_limit = apply_drafts[apply_to_origins[pool.apply_coupon_id]]
            pool_mapper[pool.id] = {
                "id": pool.id,
                "coupon_id": pool.coupon_id,
                "coupon_type": pool.coupon_type.name,
                "value_type": pool.value_type,
                "value": CouponTool.value_display(pool.coupon_type, pool.value),
                "status": cls._get_pool_status(pool),
                "send_user_type": pool.send_user_type.name,
                "apply_coupon_id": pool.apply_coupon_id,
                "total_count": pool.total_count,
                "expired_at": datetime_to_time(pool.expired_at),
                "receivable_days": pool.receivable_days,
                "active_use_days": pool.usable_days,
                "activation_days": pool.activation_days,
                "dynamic_user_type": pool.dynamic_user_type.name if pool.dynamic_user_type else None,
                "invitees_limit": invitees_limit,
                "apply_source": source.name,
                "extra": coupon_detail_mapper.get(pool.coupon_id),
            }
        if cache.exists():
            old_keys = {int(i) for i in cache.hkeys()}
            del_keys = old_keys - set(pool_mapper.keys())
            if del_keys:
                cache.hdel(*del_keys)
        if pool_mapper:
            cache.hmset({id_: json.dumps(v) for id_, v in pool_mapper.items()})

    @classmethod
    def update_one(cls, pool_id):
        cache = cls()
        cache_pool = cache.get_pool_data(pool_id)
        if not cache_pool:
            return
        pool: CouponPool = CouponPool.query.get(pool_id)
        if pool.get_expired_at() < now():
            cache.remove_pool(pool_id)
            return
        if pool.send_count >= pool.total_count:
            cache_pool["status"] = cls._get_pool_status(pool)
            cache.set_pool_data(pool_id, cache_pool)

    @classmethod
    def update_user_pool_data(cls, pool_data, user_id) -> Dict[str, Any]:
        from app.business.coupon.base import CouponPoolStatus

        dynamic_ref_gift_names = [i.name for i in CouponApply.DYNAMIC_USER_REFERRAL_GIFTS]
        if pool_data['dynamic_user_type'] not in dynamic_ref_gift_names:
            return pool_data
        if expired_at := FriendGiftCouponCache(int(pool_data['id'])).get_expire_time(user_id):
            pool_data["expired_at"] = datetime_to_time(expired_at)
        if pool_data['dynamic_user_type'] == CouponApply.DynamicUser.REFERRAL_GIFT_MUL.name:
            # 使用魔法
            referral = ReferralHistory.query.with_entities(
                ReferralHistory.referrer_id
            ).filter(
                ReferralHistory.referree_id == user_id
            ).first()
            # 必有 referral
            referrer_id = referral.referrer_id
            limit = FriendGiftMulLimitCouponCache(int(pool_data['id'])).hget(str(referrer_id))
            if limit and int(limit) >= pool_data['invitees_limit']:
                pool_data['status'] = CouponPoolStatus.ENDING.value
        return pool_data


class DeliveryUserCouponCache(HashCache):
    """ 直接发放的用户卡券的信息缓存，用于弹窗 """

    def __init__(self, pool_id: int):
        super().__init__(str(pool_id))
        self.pool_id = pool_id

    def get_user_coupon_data(self, user_id: int) -> Dict:
        data = self.hget(str(user_id))
        if data:
            return json.loads(data)
        return {}

    @classmethod
    def reload_by_pool(cls, pool_id: int):
        rows = UserCoupon.query.filter(
            UserCoupon.pool_id == pool_id,
        ).all()
        if not rows:
            return

        coupon: Coupon = Coupon.query.get(rows[0].coupon_id)
        data_map = cls.format_inv_user_coupon_data(rows)
        ttl = coupon.activation_days * 86400  # 取激活时间
        cache = cls(pool_id)
        cache.hmset(data_map)
        cache.expire(ttl)
        return data_map

    @classmethod
    def format_inv_user_coupon_data(cls, rows: List[UserCoupon]) -> Dict[str, str]:
        data_map = {}
        for r in rows:
            data = {
                "user_coupon_id": r.id,
                "activation_expired_at": datetime_to_time(r.activation_expired_at),
                "usable_expired_at": datetime_to_time(r.usable_expired_at) if r.usable_expired_at else None
            }
            data_map[str(r.user_id)] = json.dumps(data)
        return data_map


class AvailableCouponPoolUserCache(SetCache):
    """推荐卡劵"""

    UPDATE_LIMIT = 1000

    def __init__(self, pool_id: int):
        super().__init__(str(pool_id))

    @classmethod
    def _get_obtained_pool_user_mapper(cls, pool_ids: Set[int]) -> Dict[int, Set[int]]:
        pool_user_mapper = defaultdict(set)
        obtained_coupons = UserCoupon.query.filter(
            UserCoupon.pool_id.in_(pool_ids)
        ).with_entities(
            UserCoupon.user_id,
            UserCoupon.pool_id
        ).all()
        for user_coupon in obtained_coupons:
            pool_user_mapper[user_coupon.pool_id].add(user_coupon.user_id)
        return pool_user_mapper

    @classmethod
    def reload(cls):
        valid_pool_query = CouponPool.query.filter(
            CouponPool.status == CouponPool.Status.VALID
        ).all()
        pool_ids = {i.id for i in valid_pool_query}
        pool_user_mapper = cls._get_obtained_pool_user_mapper(pool_ids)
        apply_sources = CouponApply.query.filter(
            CouponApply.id.in_([v.apply_coupon_id for v in valid_pool_query])
        ).with_entities(CouponApply.id, CouponApply.source).all()
        apply_source_map = dict(apply_sources)
        for pool in valid_pool_query:  # type: CouponPool
            source = apply_source_map[pool.apply_coupon_id]
            if source in (CouponApply.Source.SYSTEM, CouponApply.Source.DELIVERY) and \
                    pool.send_user_type == CouponApply.SendUserType.ALL:
                continue
            if source == CouponApply.Source.DELIVERY:
                obtained_user_ids = set(CouponPopupReadCache(pool.id).read())
            else:
                obtained_user_ids = pool_user_mapper.get(pool.id, set())
            add_user_ids = set(pool.get_send_user_ids()) - obtained_user_ids
            cache = cls(pool.id)
            current_user_ids = cache.get_users()
            add_cache_user_ids = add_user_ids - current_user_ids
            del_user_ids = current_user_ids - add_user_ids
            if del_user_ids:
                cache.batch_del_users(del_user_ids)
            if add_cache_user_ids:
                cache.batch_add_users(add_cache_user_ids)

    def batch_add_users(self, user_ids: set[int]):
        for ids in batch_iter(user_ids, self.UPDATE_LIMIT):
            self.sadd(*(str(_id) for _id in ids))

    def batch_del_users(self, user_ids: set[int]):
        for ids in batch_iter(user_ids, self.UPDATE_LIMIT):
            self.srem(*(str(_id) for _id in ids))

    def has_user(self, user_id: int):
        return self.sismember(str(user_id))

    def add_user(self, user_id: int):
        self.sadd(str(user_id))

    def del_user(self, user_id: int):
        self.srem(str(user_id))

    def get_users(self) -> Set[int]:
        return {int(s) for s in self.smembers()}


class PushAvailablePoolCache(HashCache):
    TTL = 600

    def __init__(self):
        super().__init__(None)

    def set_pool_data(self, user_id: int, push_pools: Dict[str, Dict[str, Any]]):
        if not push_pools:
            self.delete_one(user_id)
            return
        self.hset(str(user_id), json.dumps(push_pools))
        self.expire(self.TTL)

    def get_pool_data(self, user_id: int):
        value = self.hget(str(user_id))
        return json.loads(value) if value else []

    @classmethod
    def delete_one(cls, user_id: int):
        cls().hdel(str(user_id))

    @classmethod
    def delete_all(cls):
        cls().delete()


class CouponPopupReadCache(SetCache):
    """卡劵以查看缓存"""

    def __init__(self, pool_id):
        super().__init__(str(pool_id))

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))

    def add_user(self, user_id: int):
        self.sadd(str(user_id))


class UserUsingCouponTypeCache(SetCache):
    """用户拥有使用中的卡劵缓存"""

    def __init__(self, coupon_type: str):
        super().__init__(coupon_type)

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))

    def get_users(self) -> Set[int]:
        return {int(s) for s in self.smembers()}

    @classmethod
    def reload(cls):
        using_coupons = UserCoupon.query.filter(
            UserCoupon.status.in_((
                UserCoupon.Status.CREATED, UserCoupon.Status.ACTIVE,
                UserCoupon.Status.TO_BE_RECYCLED, UserCoupon.Status.TO_BE_GIVEN))
        ).with_entities(
            UserCoupon.coupon_id,
            UserCoupon.user_id
        ).all()
        coupon_ids = {i for i, _ in using_coupons}
        coupon_mapper = {
            id_: type_ for id_, type_ in
            Coupon.query.filter(Coupon.id.in_(coupon_ids)).with_entities(
                Coupon.id,
                Coupon.coupon_type
            ).all()
        }
        coupon_uses_mapper = defaultdict(set)
        for coupon_id, user_id in using_coupons:
            coupon_uses_mapper[coupon_mapper[coupon_id]].add(user_id)
        for coupon_type, user_ids in coupon_uses_mapper.items():
            cache = cls(coupon_type.name)
            cache_users = cache.get_users()
            add_cache_user_ids = user_ids - cache_users
            del_user_ids = cache_users - user_ids
            if del_user_ids:
                cache.srem(*(str(_id) for _id in del_user_ids))
            if add_cache_user_ids:
                cache.sadd(*(str(_id) for _id in add_cache_user_ids))


class RandomCouponValueCache(ListCache):
    """交易赠金券 随机面额缓存"""

    def __init__(self, pool_id: int):
        super().__init__(str(pool_id))

    @classmethod
    def add_hundred(cls, value: Decimal):
        return quantize_amount(value, 2) * Decimal(100)

    @classmethod
    def reduce_hundred(cls, value: int):
        return Decimal(value) / Decimal(100)

    @classmethod
    def _get_random_value_list(cls, value: Decimal, total: int):
        # 计算随机面额, 只适用于卡券面额是两位小数（业务侧已经限制）。
        value = cls.add_hundred(value)
        total_balance = value * total
        min_value = math.ceil(value * Decimal(0.5))
        max_value = math.floor(value * Decimal(1.5))
        balance_per_person = [min_value] * total
        total_balance -= min_value * total
        while total_balance > 0:
            index = random.randint(0, total - 1)
            if balance_per_person[index] < max_value:
                balance_per_person[index] += 1
                total_balance -= 1
        coupon_value_list = [amount_to_str(cls.reduce_hundred(i)) for i in balance_per_person]
        return coupon_value_list

    def reload(self, coupon_value: Decimal, send_total: int):
        coupon_value_list = self._get_random_value_list(coupon_value, send_total)
        self.value = coupon_value_list

    def get_random_value(self):
        value = self.lpop()
        if not value:
            self.delete()
        return Decimal(value) or Decimal()


class FifthAnniversaryHighFiveCache(StringCache):
    """ 五周年账单击掌次数 """

    def __init__(self):
        super().__init__(None)


class FifthAnniversaryBillStatisticsCache(StringCache):
    """ 五周年账单统计"""

    def __init__(self):
        super().__init__(None)

    def reload(self):
        result = {}
        investment_asset_count = InvestmentAccount.query.filter(
            InvestmentAccount.status == InvestmentAccount.StatusType.OPEN,
        ).with_entities(func.count('*')).scalar() or 0
        result['investment_asset_count'] = investment_asset_count

        max_usdt_rate = DailyInvestmentReport.query.filter(
            DailyInvestmentReport.asset == 'USDT',
        ).with_entities(func.max(DailyInvestmentReport.seven_day_rate)).scalar() or 0
        result['max_usdt_rate'] = amount_to_str(max_usdt_rate, 4)
        self.save(json.dumps(result))


class ActivateReportCache(HashCache):

    def __init__(self):
        super().__init__(None)


class FifthAnniversaryThankLetterCache(BitsCache):
    """ 5周年-用户感谢信已读状态 """

    def __init__(self):
        super().__init__(None)


class SixthAnniversaryCompleteCache(BitsCache):
    """ 6周年-闯关完成状态 """

    def __init__(self):
        super().__init__(None)


class FifthBoxRemainNumCache(StringCache):
    """ 盲盒 剩余总数 """

    def __init__(self, box_type: str):
        super().__init__(box_type)


class FifthBoxRewardRemainNumCache(HashCache):
    """ 盲盒-批次 奖品剩余数 """

    def __init__(self, box_type: str, batch_num: int):
        # data:  {奖品类型: 剩余数目}
        super().__init__(f"{box_type}-{batch_num}")

    def get_remain_data(self) -> Dict[str, int]:
        data = self.hgetall()
        return {k: int(v) for k, v in data.items()}


class FifthUserBoxRewardCache(HashCache):
    """ 用户盲盒奖励缓存 """

    def __init__(self):
        super().__init__(None)

    def set_user_reward(self, user_id: int, box_type: str, reward: str):
        data = f"{box_type}:{reward}"
        self.hset(str(user_id), data)

    def get_user_reward(self, user_id: int) -> tuple:
        data = self.hget(str(user_id))
        if not data:
            return None, None

        box_type, reward = data.split(":")
        return box_type, reward

    def get_all_user_reward(self):
        reward_map = self.hgetall()
        return {int(k): v.split(":") for k, v in reward_map.items()}


class FifthBroadcastCache(StringCache):
    """ 开箱机会区、中奖区 轮播缓存 """

    def __init__(self, type_: str, lang: str):
        # type_: {"qualify", "reward"}
        super().__init__(f"{type_}-{lang}")


class FifthRiskEventCache(ListCache):
    """ 触发风控的事件信息 缓存 """

    def __init__(self, event_type: str, date_: datetime.date):
        key_ = f"{event_type}-{date_.strftime('%Y%m%d')}"
        super().__init__(key_)


class FifthDealHunterUserCache(SetCache):
    """ 5周年-羊毛党用户 """

    def __init__(self):
        super().__init__(None)


class FifthRiskUserCache(HashCache):
    """ 5周年-风险用户无法开启盲盒 """

    def __init__(self):
        super().__init__(None)


class FifthBoxOpenDeviceCache(HashCache):
    """ 设备id-盲盒领取数 缓存 """

    def __init__(self):
        super().__init__(None)

    def get_count(self, device_id: str) -> int:
        val = self.hget(device_id)
        return int(val) if val else 0


class FifthUserFeeCache(HashCache):
    """ 5周年-用户手续费 """

    def __init__(self):
        super().__init__(None)

    def get_user_fee(self, user_id: int) -> Decimal:
        val = self.hget(str(user_id))
        return Decimal(val) if val else Decimal()


class FifthBoxOpenIpCache(HashCache):
    """ IP-盲盒领取数 缓存 """

    def __init__(self):
        super().__init__(None)

    def get_count(self, ip: str) -> int:
        val = self.hget(ip)
        return int(val) if val else 0


class SixthRewardRemainNumCache(HashCache):
    """ 六周年奖品剩余数 """

    def __init__(self):
        super().__init__(None)

    def get_remain_data(self) -> Dict[str, int]:
        data = self.hgetall()
        return {k: int(v) for k, v in data.items()}

    def get_reward_remain_num(self, reward_type: str) -> int:
        remain_num = self.hget(reward_type) or 0
        return int(remain_num)

    def reduce_reward_remain_num(self, reward_type: str) -> int:
        return self.hincrby(reward_type, -1)


class SixthCouponPoolCache(HashCache):
    """ 六周年卡券池配置缓存 """

    def __init__(self):
        super().__init__(None)

    def set_pool_id_by_coupon_type(self, coupon_type: str, pool_id: int):
        self.hset(coupon_type, pool_id)

    def get_pool_id_by_coupon_type(self, coupon_type: str) -> int:
        remain_num = self.hget(coupon_type) or 0
        return int(remain_num)

    def get_all_pool_data(self) -> Dict[str, int]:
        data = self.hgetall()
        return {k: int(v) for k, v in data.items()}


class SeventhRewardRemainNumCache(HashCache):
    """ 七周年奖品剩余数 """

    def __init__(self):
        super().__init__(None)

    def get_remain_data(self) -> Dict[str, int]:
        data = self.hgetall()
        return {k: int(v) for k, v in data.items()}

    def get_reward_remain_num(self, reward_type: str) -> int:
        remain_num = self.hget(reward_type) or 0
        return int(remain_num)

    def reduce_reward_remain_num(self, reward_type: str) -> int:
        return self.hincrby(reward_type, -1)


class SeventhCouponPoolCache(HashCache):
    """ 七周年卡券池配置缓存 """

    def __init__(self):
        super().__init__(None)

    def set_pool_id_by_coupon_type(self, coupon_type: str, pool_id: int):
        self.hset(coupon_type, str(pool_id))

    def get_pool_id_by_coupon_type(self, coupon_type: str) -> int:
        remain_num = self.hget(coupon_type) or 0
        return int(remain_num)

    def get_all_pool_data(self) -> Dict[str, int]:
        data = self.hgetall()
        return {k: int(v) for k, v in data.items()}


class DynamicUserCouponCache(SetCache):
    """卡券动态用户缓存"""

    def __init__(self, dynamic_type: str, coupon_type: str, coupon_apply_id=None):
        key_ = f"{dynamic_type}_{coupon_type}"
        if coupon_apply_id:
            key_ = f"{dynamic_type}_{coupon_type}:{coupon_apply_id}"
        super().__init__(key_)

    def get_users(self) -> Set[int]:
        return {int(s) for s in self.smembers()}

    def add_users(self, user_ids: list):
        self.sadd(*[str(i) for i in user_ids])

    def set_users(self, user_ids: list):
        self.save({str(i) for i in user_ids})


class FiveActivityReceivedCountCache(HashCache):
    """五周年活动用户已经领取卡券的数量"""

    def __init__(self):
        super().__init__(None)

    def incr(self, user_id: int):
        self.hincrby(str(user_id), 1)


class FiveActivityUserReceivedCache(SetCache):
    """五周年用户已领取卡券缓存"""

    class LimitType(Enum):
        IP = "ip"
        USER = "user"

    def __init__(self, coupon_type: Union[Coupon.CouponType, str], limit_type: LimitType):
        if isinstance(coupon_type, Coupon.CouponType):
            coupon_type = coupon_type.name
        super().__init__(f"{coupon_type}:{limit_type.name}")

    def has_value(self, value) -> bool:
        return self.sismember(str(value))

    def add_value(self, value):
        self.sadd(str(value))


class FiveActivitySendCouponTimingCache(SetCache):
    START_DATE = datetime.date(2022, 12, 24)
    END_DATE = datetime.date(2022, 12, 30)
    ONCE_COUNT = 238

    """五周年大挑战卡券发放时间缓存"""

    def __init__(self, date_: datetime.date):
        super().__init__(str(date_))

    def set_dates(self, date_list: List[datetime.datetime]):
        self.sadd(*[datetime_to_str(i) for i in date_list])

    def del_date(self, date_: datetime.datetime):
        self.srem(datetime_to_str(date_))

    def get_send_at(self):
        date_list = [str_to_datetime(v) for v in self.smembers()]
        if not date_list:
            self.delete()
            return
        return min(date_list)

    def get_pool_expire_time(self):
        date_list = [str_to_datetime(v) for v in self.smembers()]
        if not date_list:
            return
        return max(date_list)

    @classmethod
    def reload(cls):
        current_date = cls.START_DATE
        while current_date <= cls.END_DATE:
            cache = cls(current_date)
            cache.delete()
            hours = random.sample(range(0, 24), 3)
            timing = [datetime.datetime(current_date.year, current_date.month, current_date.day, h) for h in hours]
            cache.set_dates(timing)
            current_date += timedelta(days=1)


class PerpetualSpecialSummaryDataCache(StringCache):
    """合约专题页数据缓存"""

    def __init__(self):
        super().__init__(None)


class PerpetualActivityBannerCache(StringCache):
    """合约活动数据缓存"""

    def __init__(self, lang: Language):
        super().__init__(lang.value)

    @classmethod
    def delete_all_cache(cls):
        for lang in PerpetualSpecial.AVAILABLE_LANGS:
            cls(lang).delete()

    @classmethod
    def get_static_file_url(cls, file_key: str):
        if not file_key:
            return ""
        return AWSBucketPublic.get_file_url(file_key)

    @classmethod
    def reload(cls):
        perpetual_special = PerpetualSpecial.query.filter(
            PerpetualSpecial.status == PerpetualSpecial.Status.ONLINE
        ).first()
        if not perpetual_special:
            cls.delete_all_cache()
            return
        activities = PerpetualSpecialConnect.query.join(
            PerpetualSpecialActivity
        ).filter(
            PerpetualSpecialActivity.owner_id == perpetual_special.id
        ).with_entities(
            PerpetualSpecialActivity.daylight_file_key,
            PerpetualSpecialActivity.night_file_key,
            PerpetualSpecialActivity.rtl_daylight_file_key,
            PerpetualSpecialActivity.rtl_night_file_key,
            PerpetualSpecialActivity.jump_id,
            PerpetualSpecialActivity.id,
            PerpetualSpecialConnect.lang,
            PerpetualSpecialConnect.title,
            PerpetualSpecialConnect.subtitle
        ).all()
        lang_activity_mapper = defaultdict(list)
        jump_mapping = {}
        for jump_row in AppJumpList.query.with_entities(
                AppJumpList.id,
                AppJumpList.jump_data,
        ).filter(
            AppJumpList.id.in_({a.jump_id for a in activities})
        ).all():
            jump_mapping[jump_row.id] = jump_row.jump_data

        for activity in activities:
            lang_activity_mapper[activity.lang].append(dict(
                id=activity.id,
                title=activity.title,
                subtitle=activity.subtitle,
                return_url=jump_mapping.get(activity.jump_id, ""),
                daylight_file_url=cls.get_static_file_url(activity.daylight_file_key),
                night_file_url=cls.get_static_file_url(activity.night_file_key),
                rtl_daylight_file_url=cls.get_static_file_url(activity.rtl_daylight_file_key),
                rtl_night_file_url=cls.get_static_file_url(activity.rtl_night_file_key)
            ))
        for lang, activities in lang_activity_mapper.items():
            cls(lang).save(json.dumps(activities))


class PerpetualSpecialUsersCache(SetCache):
    """合约专题用户群组缓存"""

    def __init__(self, benefit_id: int):
        super().__init__(str(benefit_id))

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))

    def set_users(self, user_ids: list):
        self.save({str(i) for i in user_ids})


class LuckyDrawDateCache(SetCache):
    """ 抽奖每日缓存人数缓存 """

    def __init__(self, benefit_id, date_: Union[None, datetime.date]):
        key = f"{benefit_id}"
        if date_:
            key = f"{benefit_id}:{date_.strftime('%Y%m%d')}"
        super().__init__(key)

    def get_users(self) -> Set[int]:
        return {int(s) for s in self.smembers()}

    def add_user(self, user_id: int):
        self.sadd(str(user_id))

    def has_user(self, user_id: int) -> bool:
        return self.sismember(str(user_id))


class PerpetualSpecialVisitCache(StringCache):
    """合约专题活动访问缓存"""

    def __init__(self, date_: datetime.date, activity_id: int):
        date_str = date_.strftime("%Y%m%d")
        pk = f"{date_str}-{activity_id}"
        super().__init__(pk)


class NoviceActivityCache(StringCache):
    """新手专区活动缓存"""

    def __init__(self, lang: Language):
        super().__init__(lang.value)

    @classmethod
    def delete_all_cache(cls):
        for lang in NovicePrefectureActivity.AVAILABLE_LANGS:
            cls(lang).delete()

    @classmethod
    def reload(cls):
        from ..business.user_group import build_package_task_content
        from app.business.report.novice import NoviceTaskConditionReportStatistic

        model = NovicePrefectureActivity
        novice = model.query.filter(
            model.display_status == model.DisplayStatus.ONLINE
        ).first()
        if not novice:
            cls.delete_all_cache()
            return
        activities = model.query.join(
            NoviceActivityContent
        ).filter(
            NoviceActivityContent.owner_id == novice.id
        ).with_entities(
            model.coupon_apply_id,
            model.notice_url,
            model.id,
            model.activity_type,
            model.start_at,
            model.end_at,
            model.user_group_condition,
            model.task_group_condition,
            model.display_status,
            model.status,
            NoviceActivityContent.title,
            NoviceActivityContent.lang,
        ).all()
        for activity in activities:
            lang = activity.lang
            with force_locale(lang.value):
                task_group_condition = build_package_task_content(
                    NoviceTaskConditionReportStatistic.get_task_group_condition(novice)
                )
                data = dict(
                    id=activity.id,
                    title=activity.title,
                    notice_url=activity.notice_url,
                    activity_type=activity.activity_type.name,
                    coupon_apply_id=activity.coupon_apply_id,
                    start_at=datetime_to_time(activity.start_at),
                    end_at=datetime_to_time(activity.end_at),
                    user_group_condition=activity.user_group_condition,
                    task_group_condition=task_group_condition,
                    display_status=activity.display_status.name,
                    status=activity.status.name
                )
                if activity.activity_type == model.ActivityType.COUPON:
                    data["virtual_activity_data"] = cls.build_virtual_activity_data(activity.coupon_apply_id)
                cls(lang).save(json.dumps(data, cls=JsonEncoder))

    @classmethod
    def build_virtual_activity_data(cls, coupon_apply_id):
        from ..business.coupon.base import CouponPoolStatus
        from ..business.coupon.utils import CouponTool
        # 仅供前端展示使用
        row = CouponApply.query.join(
            Coupon, CouponApply.coupon_id == Coupon.id).filter(
            CouponApply.id == coupon_apply_id,
        ).with_entities(
            Coupon.coupon_type,
            Coupon.value_type,
            Coupon.value,
            Coupon.id.label("coupon_id"),
            Coupon.usable_days,
            Coupon.receivable_days,
            Coupon.activation_days,
            CouponApply.total_count,
            CouponApply.send_user_type,
            CouponApply.dynamic_user_type,
            CouponApply.id,
            CouponApply.source,
            CouponApply.send_at
        ).first()
        coupon_type_mapper = {
            row.coupon_type: [row.coupon_id]
        }
        coupon_detail_mapper = CouponTool.dump_coupon_data(coupon_type_mapper)
        return {
            "id": None,
            "name": gettext(row.coupon_type.value),
            "coupon_id": row.coupon_id,
            "coupon_type": row.coupon_type.name,
            "value_type": gettext(row.value_type),
            "value": CouponTool.value_display(row.coupon_type, row.value),
            "status": CouponPoolStatus.AVAILABLE.value,
            "send_user_type": row.send_user_type.name,
            "apply_coupon_id": row.id,
            "total_count": row.total_count,
            "expired_at": datetime_to_time(row.send_at + timedelta(days=row.receivable_days)),
            "receivable_days": row.receivable_days,
            "active_use_days": row.usable_days,
            "activation_days": row.activation_days,
            "dynamic_user_type": row.dynamic_user_type.name if row.dynamic_user_type else None,
            "apply_source": row.source.name,
            "extra": coupon_detail_mapper.get(row.coupon_id)
        }


class NoviceUserCache(StringCache):
    """新手专区-用户"""

    TTL = 60 * 5

    def __init__(self, user_id):
        super().__init__(str(user_id))


class NoviceUserTradeBase(HashCache):
    TTL = 3600 * 3

    def read_one_user(self, user_id):
        cache_data = self.hget(str(user_id))
        if not cache_data:
            return {}
        return json.loads(cache_data)

    def read_all_user(self):
        cache_data = self.hgetall()
        if not cache_data:
            return {}
        return {int(k): json.loads(v) for k, v in cache_data.items()}


class NoviceUserTradeCache(NoviceUserTradeBase):
    """新手专区-新用户交易额(每小时更新)"""

    def __init__(self, novice_id):
        super().__init__(novice_id)

    def reload(self):
        from ..schedules.account import calc_user_summary
        from ..business.activity.novice import get_novice_user_ids
        from ..business.trade import get_novice_user_deposit, get_user_business_trade_summary, \
            get_user_exchange_trade_data
        _today = today()
        novice = NovicePrefectureActivity.query.get(self._pk)
        end_hour = now().replace(minute=0, second=0)
        end_ts = int(min(today_datetime().timestamp(), novice.end_at.timestamp()))
        if end_hour > novice.end_at:
            return
        user_ids = get_novice_user_ids(novice)
        start_datetime = novice.start_at
        start_day = start_datetime.date()
        # 获取昨天的 summary
        user_trade_map = defaultdict(lambda: defaultdict(Decimal))
        for batch_ids in batch_iter(user_ids, 1000):
            batch_map = get_user_business_trade_summary(batch_ids, start_day, _today)
            user_trade_map.update(batch_map)

            # 获取 server 今日的 summary
            s_model = UserTradeSummary
            for system in s_model.System:
                try:
                    user_balance = calc_user_summary(_today, system, batch_ids, end_ts)
                except DataNotReady as e:
                    current_app.logger.exception(f"novice coupon {system.name} data no ready: {e}")
                    user_balance = {}
                for user_id, info in user_balance.items():
                    user_trade_map[user_id][system.name] += info['trade_amount']

            # 获取 exchange 最新的小时的 数据
            exchange_map = get_user_exchange_trade_data(batch_ids, start_datetime, end_hour)
            for user_id, exchange_amount in exchange_map.items():
                user_trade_map[user_id][s_model.System.SPOT.name] += exchange_amount

            for user_id in user_trade_map.keys():
                _tmp = user_trade_map[user_id]
                _tmp[NoviceTradeType.SPOT_PERPETUAL.name] = \
                    _tmp[NoviceTradeType.SPOT.name] + _tmp[NoviceTradeType.PERPETUAL.name]

            # 获取 deposit 最新的小时的 数据
            deposit_map = get_novice_user_deposit(batch_ids, start_datetime, end_hour)
            for user_id, deposit_amount in deposit_map.items():
                user_trade_map[user_id][NoviceTradeType.DEPOSIT.name] = deposit_amount

        to_save_data = {k: json.dumps(v, cls=JsonEncoder) for k, v in user_trade_map.items()}
        if to_save_data:
            self.save(to_save_data)
            self.expire(self.TTL)


class NovicePackageUserCache(NoviceUserTradeBase):
    """新手礼包-新用户当日最新小时交易额(每小时更新)"""

    TTL = 3600 * 3

    def __init__(self, novice_id):
        super().__init__(novice_id)

    def get_old_data(self):
        old_data = self.read_all_user()
        for old_id, old_map in old_data.items():
            for k, amount in old_map.items():
                old_map[k] = Decimal(amount)
        return old_data

    def reload(self):
        from ..schedules.account import calc_user_summary
        from ..business.user_group import PackageOperateType
        from ..business.activity.novice import get_novice_user_ids
        from ..business.trade import get_novice_user_deposit, get_user_exchange_trade_data

        novice = NovicePrefectureActivity.query.get(self._pk)
        end_hour = now().replace(minute=0, second=0)
        end_ts = int(min(today_datetime().timestamp(), novice.end_at.timestamp()))
        start_dt = end_hour.replace(hour=0)
        if end_hour > novice.end_at:
            return
        user_ids = get_novice_user_ids(novice)
        user_trade_map = defaultdict(lambda: defaultdict(Decimal))

        old_data = self.get_old_data()
        for batch_ids in batch_iter(user_ids, 1000):
            # 获取 deposit 最新的小时的 数据
            deposit_map = get_novice_user_deposit(batch_ids, start_dt, end_hour)
            for user_id, deposit_amount in deposit_map.items():
                user_trade_map[user_id][PackageOperateType.DEPOSIT.name] = deposit_amount

            # 获取 server 最新的小时的 summary
            s_model = UserTradeSummary
            for system in s_model.System:
                sys_name = system.name
                try:
                    user_balance = calc_user_summary(start_dt.date(), system, batch_ids, end_ts)
                    for user_id, info in user_balance.items():
                        user_trade_map[user_id][sys_name] += info['trade_amount']
                    # 获取 exchange 最新的小时的 数据
                    if sys_name == s_model.System.SPOT.name:
                        exchange_map = get_user_exchange_trade_data(batch_ids, start_dt, end_hour)
                        for user_id, exchange_amount in exchange_map.items():
                            user_trade_map[user_id][sys_name] += exchange_amount
                except DataNotReady as e:
                    current_app.logger.exception(f"novice package {sys_name} data no ready: {e}")
                    # 如果为 0 点，且发生异常，则清空数据统计
                    if end_hour.hour == 0:
                        continue
                    for user_id in batch_ids:
                        if user_id in old_data and sys_name in old_data[user_id]:
                            user_trade_map[user_id][sys_name] = old_data[user_id][sys_name]

        to_save_data = {k: json.dumps(v, cls=JsonEncoder) for k, v in user_trade_map.items()}
        if to_save_data:
            self.save(to_save_data)
            self.expire(self.TTL)


class NoviceRiskUserCache(SetCache):
    TTL = 86400 * 7

    def __init__(self, novice_id: int):
        self.novice_id = novice_id
        super().__init__(str(novice_id))

    def reload(self):
        from ..business.activity.novice import get_novice_source_user_ids
        model = NovicePrefectureActivity
        novice = model.query.get(self.novice_id)
        user_ids = get_novice_source_user_ids(novice)
        risk_user_ids = LoginRelationHistory.query_duplicate_device_users(user_ids)
        self.save({str(i) for i in risk_user_ids})
        return risk_user_ids

    def get_user_ids(self) -> Set[int]:
        return {int(i) for i in self.smembers()}


class NovicePackageEmailCache(SetCache):
    TTL = 86400 * 7

    def __init__(self, id_: int):
        super().__init__(str(id_))

    def add_user_ids(self, user_id):
        self.sadd(user_id)
        self.expire(self.TTL)


class CouponRiskCache(TimedHashCache):
    RECEIVED_LIMIT_MAPPER = {
        CouponRisk.LimitType.IP: 3,
        CouponRisk.LimitType.DEVICE_ID: 3
    }

    def __init__(self, apply_id: int, limit_type: CouponRisk.LimitType, key: str, interval: int = None):
        self._limit_type = limit_type
        key = f"{limit_type.name}:{apply_id}:{key}"
        super().__init__(key, interval=interval)

    def get_limit_count(self):
        return self.RECEIVED_LIMIT_MAPPER[self._limit_type]


class CouponRiskWhiteCache(SetCache):
    class RiskWhiteType(Enum):
        IP = "ip"
        DEVICE_ID = "device_id"
        USER_ID = "user_id"

    def __init__(self, white_type: str, apply_id: int = None):
        key = white_type
        if apply_id:
            key = f"{white_type}:{apply_id}"
        super().__init__(key)

    def add_value(self, value: Union[int, str]):
        self.sadd(str(value))

    def has_value(self, value: Union[int, str]) -> bool:
        return self.sismember(str(value))

    def del_value(self, value: Union[int, str]):
        self.srem(str(value))


class CouponDistributionExcludeUserCache(SetCache):
    """卡券智能投放-客群数据剔除"""

    EXCLUDE_DAYS = 45

    def __init__(self, coupon_type: Coupon.CouponType):
        super().__init__(coupon_type.name)

    @classmethod
    def reload(cls):
        start_at = now() - timedelta(days=cls.EXCLUDE_DAYS)
        exclude_pools = CouponPool.query.filter(
            CouponPool.created_at > start_at
        ).all()

        coupon_ids = {p.coupon_id for p in exclude_pools}
        coupon_type_mapper = {i: t for i, t in Coupon.query.filter(
            Coupon.id.in_(coupon_ids)
        ).with_entities(
            Coupon.id,
            Coupon.coupon_type
        )}
        exclude_type_users = defaultdict(set)
        for pool in exclude_pools:
            exclude_type_users[coupon_type_mapper[pool.coupon_id]] |= set(pool.get_send_user_ids())
        for coupon_type, users in exclude_type_users.items():
            cls(coupon_type).value = users

    def get_users(self) -> Set[int]:
        return {int(s) for s in self.smembers()}


class AmbassadorActivityNoticeCache(SetCache):

    def __init__(self):
        super().__init__(None)

    def is_noticed(self, activity_id: int) -> bool:
        return self.sismember(str(activity_id))

    def add(self, activity_id: int):
        self.sadd(str(activity_id))


class AmbassadorActivitySecondNoticeCache(SetCache):

    def __init__(self):
        super().__init__(None)

    def is_noticed(self, activity_id: int) -> bool:
        return self.sismember(str(activity_id))

    def add(self, activity_id: int):
        self.sadd(str(activity_id))


class AmbassadorActivityThirdNoticeCache(SetCache):

    def __init__(self, flag: str):
        super().__init__(f'{flag}')

    def is_noticed(self, activity_id: int) -> bool:
        return self.sismember(str(activity_id))

    def add(self, activity_id: int):
        self.sadd(str(activity_id))


class LaunchProjectCache(ListCache):
    """ LaunchPool挖矿项目 """
    MAX_SIZE = 1000

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        projects: list[LaunchMiningProject] = LaunchMiningProject.query.filter(
            LaunchMiningProject.status.in_(
                [LaunchMiningProject.Status.ONLINE, LaunchMiningProject.Status.CLOSED]
            ),
        ).order_by(LaunchMiningProject.start_time.desc()).limit(cls.MAX_SIZE).all()
        project_ids = [i.id for i in projects]
        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id.in_(project_ids),
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        pcj_pools_map: dict[int, list[LaunchMiningPool]] = defaultdict(list)
        for p in pools:
            pcj_pools_map[p.project_id].append(p)

        q_assets = {i.reward_asset for i in projects}
        q_assets.update({i.stake_asset for i in pools})
        coin_info_rows = CoinInformation.query.filter(
            CoinInformation.code.in_(q_assets),
        ).with_entities(
            CoinInformation.code,
            CoinInformation.icon,
            CoinInformation.official_website,
            CoinInformation.white_paper,
            CoinInformation.report_url,
        ).all()
        q_coin_info_map = {i.code: i for i in coin_info_rows}

        result = []
        for pcj in projects:
            pcj_pools = pcj_pools_map[pcj.id]
            if not pcj_pools:
                continue
            assets = [pcj.reward_asset]
            assets.extend([p.stake_asset for p in pcj_pools])
            coin_info_map = {i: q_coin_info_map[i] for i in assets}
            item = cls.format_item(pcj, pcj_pools, coin_info_map)
            result.append(item)

        list_dump_data = []
        detail_dump_data = {}
        for i in result:
            di = json.dumps(i, cls=JsonEncoder)
            list_dump_data.append(di)
            detail_dump_data[i['project_id']] = di

        reward_assets = set()
        reward_assets_data = []
        for i in result:
            if i["reward_asset"] not in reward_assets:
                reward_assets_data.append(
                    {
                        "reward_asset": i["reward_asset"],
                        "logo": i["logo"],
                    }
                )
                reward_assets.add(i["reward_asset"])

        cls().save(list_dump_data)
        LaunchProjectDetailCache().save(detail_dump_data)
        LaunchProjectRewardAssetsCache().save(json.dumps(reward_assets_data, cls=JsonEncoder))

    @classmethod
    def format_item(
        cls,
        project: LaunchMiningProject,
        pools: list[LaunchMiningPool],
        coin_info_map: dict[str, CoinInformation],
    ) -> dict:
        pool_items = []
        for p in pools:
            coin_info = coin_info_map[p.stake_asset]
            points = project.reward_settle_hour_points
            hourly_reward_amount = p.reward_total_amount / len(points)
            today_reward_amount = hourly_reward_amount * 24
            pool_items.append(
                {
                    "stake_asset": p.stake_asset,
                    "user_min_stake_amount": p.user_min_stake_amount,
                    "user_max_stake_amount": p.user_max_stake_amount,
                    "reward_distribute_amount": p.reward_distribute_amount,
                    "reward_total_amount": p.reward_total_amount,
                    "total_stake_amount": max(p.locked_stake_amount or p.stake_amount, Decimal()),
                    "hourly_reward_amount": quantize_amount(hourly_reward_amount, 8),
                    "today_reward_amount": quantize_amount(today_reward_amount, 8),
                    "reward_mode": p.reward_mode,
                    "apr": p.apr,
                    "pool_id": p.id,
                    "logo": AWSBucketPublic.get_file_url(coin_info.icon) if coin_info.icon else '',
                }
            )
        r_coin_info = coin_info_map[project.reward_asset]
        return dict(
            project_id=project.id,
            name=project.name,
            reward_asset=project.reward_asset,
            reward_total_amount=project.reward_total_amount,
            status=project.status.name,
            active_status=project.active_status.name,
            start_time=int(project.start_time.timestamp()),
            end_time=int(project.end_time.timestamp()),
            logo=AWSBucketPublic.get_file_url(r_coin_info.icon) if r_coin_info.icon else '',
            website_url=r_coin_info.official_website or "",
            white_paper_url=r_coin_info.white_paper or "",
            report_url=r_coin_info.report_url or "",
            rule_url=project.rule_url,
            pools=pool_items,
        )

    def paginate(self, page: int, limit: int) -> dict:
        start, end = (page - 1) * limit, page * limit - 1
        total = self.llen()
        data = self.lrange(start, end)
        data = [json.loads(i) for i in data]
        return dict(
            has_next=end < total,
            curr_page=page,
            count=len(data),
            data=data,
            total=total,
            total_page=math.ceil(total / limit)
        )


class LaunchProjectDetailCache(HashCache):
    """ LaunchPool挖矿单个项目信息 """

    def __init__(self):
        super().__init__(None)


class LaunchProjectRewardAssetsCache(StringCache):
    """ LaunchPool 奖励币种列表 """

    def __init__(self):
        super().__init__(None)


class LaunchProjectLastRewardCache(HashCache):
    """ LaunchPool 用户奖励数缓存 """
    TTL = 86400 * 7

    def __init__(self, project_id: int):
        super().__init__(str(project_id))

    def save_user_rewards(self, user_id: int, pool_reward_map: dict[int, Decimal]):
        self.hset(str(user_id), json.dumps(pool_reward_map, cls=JsonEncoder))
        self.expire(self.TTL)

    def get_user_rewards(self, user_id: int) -> dict[int, Decimal]:
        d = self.hget(str(user_id))
        if d:
            d = json.loads(d)
            return {int(k): Decimal(v) for k, v in d.items()}
        return {}


class CoinHalvingActivityCache(StringCache):
    """ 币种减半活动banner """

    def __init__(self, lang: Language):
        super().__init__(f"{lang.name}")

    @classmethod
    def reload(cls):
        model = CoinHalvingActivity
        _now = now()
        banners: List[model] = model.query.filter(
            model.status == model.Status.VALID,
            model.begin_at <= _now,
            model.end_at > _now,
        ).order_by(model.id.desc()).limit(100).all()

        content_model = CoinHalvingActivityContent
        contents = content_model.query.filter(
            content_model.owner_id.in_([x.id for x in banners]),
            content_model.file_key != '',
        ).all()
        content_map = {(x.owner_id, x.lang): x for x in contents}

        lang_banners_map = defaultdict(list)
        for banner in banners:
            en_c = content_map.get((banner.id, Language.EN_US))  # 默认英语的
            for lang in Language:
                c = content_map.get((banner.id, lang), en_c)
                if not c:
                    continue
                _info = cls.format_item(banner, c)
                lang_banners_map[lang].append(_info)

        old_keys = {x for x in Language}
        for k, banner_info in lang_banners_map.items():
            old_keys.remove(k)
            cls(k).set(json.dumps(banner_info, cls=JsonEncoder))
        for k in old_keys:
            cls(k).delete()

    @classmethod
    def format_item(cls, banner: CoinHalvingActivity, content: CoinHalvingActivityContent) -> dict:
        return dict(
            banner_id=banner.id,
            url=content.url or banner.url,
            img_src=content.img_src,
        )


class DepositBonusActivityCache(StringCache):
    """充值福利活动首页展示的活动列表"""

    def __init__(self, lang: Language):
        super().__init__(f"{lang.name}")

    @classmethod
    def reload(cls):
        now_ = now()
        model = DepositBonusActivity
        rows = model.query.filter(
            model.status.in_(
                [
                    model.StatusType.ONLINE,
                    model.StatusType.FINISHED
                ]
            )
        ).all()

        def sorter(act: DepositBonusActivity):
            if act.start_time <= now_ < act.end_time:
                return 0, -act.end_time.timestamp(), -act.id
            else:
                return 1, -act.end_time.timestamp(), -act.id

        old_keys = {lang for lang in Language}
        rows.sort(key=sorter)
        for lang, data in cls.format_activity(rows):
            cls(lang).set(json.dumps(data, cls=JsonEncoder))
            old_keys.remove(lang)
        for lang in old_keys:
            cls(lang).delete()

    @classmethod
    def format_activity(cls, activity_list):
        activity_ids = [i.id for i in activity_list]
        sum_gift_assets, sum_gift_coupons = DepositBonusActivityConfig.get_configs_by_sum_gift(activity_ids)
        #
        asset_list = {i.asset for i in activity_list}
        coin_info_map = {i.code: i for i in CoinInformation.query.filter(
            CoinInformation.code.in_(asset_list)
        )}
        result = []
        for row in activity_list:
            deposit_bonus_id = row.id
            gift_assets = []
            for asset, amount in sum_gift_assets[deposit_bonus_id].items():
                gift_assets.append(dict(
                    asset=asset,
                    amount=amount,
                ))
            gift_coupons = []
            for apply_id, amount in sum_gift_coupons[deposit_bonus_id].items():
                gift_coupons.append(dict(
                    apply_id=apply_id,
                    amount=amount,
                ))
            data = {
                'id': row.id,
                'start_time': row.start_time,
                'end_time': row.end_time,
                'asset': row.asset,
                'icon': AWSBucketPublic.get_file_url(coin_info_map[row.asset].icon),
                'gift_assets': gift_assets,
                'gift_coupons': gift_coupons,
                'status': row.status.name,
                'cover_url': row.cover_url,
            }
            result.append(data)

        c_model = DepositBonusActivityContent
        c_rows = c_model.query.filter(
            c_model.deposit_bonus_id.in_(activity_ids)
        ).all()
        contents = {(row.deposit_bonus_id, row.lang): row for row in c_rows}
        for lang in Language:
            for row in result:
                key = (row['id'], lang)
                if not (c := contents.get(key)):
                    continue
                row['title'] = c.title
            yield lang, result


class DepositBonusActivityDetailCache(HashCache):
    """ 充值福利活动详情信息缓存 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        model = DepositBonusActivity
        rows = model.query.filter(
            model.status.in_(
                [model.StatusType.ONLINE, model.StatusType.FINISHED])
        ).all()
        ret = {}
        for row in rows:
            for lang, detail_data in cls._format_detail_data(row):
                key_ = cls.format_key(row.id, lang)
                ret[key_] = json.dumps(detail_data, cls=JsonEncoder)

        instance = cls()
        if not ret:
            instance.delete()
            return
        old_keys = instance.hkeys()
        instance.hmset(ret)
        # 删除过期的数据
        del_keys = [i for i in old_keys if i not in ret]
        if del_keys:
            instance.hdel(*del_keys)

    @classmethod
    def _format_detail_data(cls, activity: DepositBonusActivity):
        from app.business.activity.airdrop import get_airdrop_coupon_details

        coin_info = CoinInformation.query.filter(
            CoinInformation.code == activity.asset
        ).first()
        model = DepositBonusActivityConfig
        rows = model.query.filter(
            model.deposit_bonus_id == activity.id
        ).order_by(model.id.asc()).all()
        activity_configs = []
        apply_ids = {apply_id for row in rows for apply_id, _ in row.get_gift_coupon_applys().items()}
        coupon_details = {}
        if apply_ids:
            coupon_details = get_airdrop_coupon_details(apply_ids)
        for idx, row in enumerate(rows, start=1):
            activity_configs.append({
                'idx': idx,
                'id': row.id,
                'mode': row.mode.name,
                'flag': row.flag.name,
                'gift_rules': row.cached_gift_rules,
                'conditions': cls._get_conditions(row.id),
            })
        ret = dict(
            id=activity.id,
            icon=AWSBucketPublic.get_file_url(coin_info.icon),
            asset=activity.asset,
            chains=activity.get_chains(),
            threshold=activity.threshold,
            start_time=activity.start_time,
            end_time=activity.end_time,
            zendesk_url=activity.zendesk_url,
            status=activity.status.name,

            activity_configs=activity_configs,
            coupon_details=coupon_details,
        )
        c_model = DepositBonusActivityContent
        c_rows = c_model.query.filter(
            c_model.deposit_bonus_id == activity.id,
        ).all()
        contents = {row.lang: row for row in c_rows}
        for lang in Language:
            if not (c := contents.get(lang)):
                continue
            ret['title'] = c.title
            yield lang, ret

    @classmethod
    def _get_conditions(cls, cfg_id: int):
        model = DepositBonusActivityCondition
        rows = model.query.filter(
            model.activity_config_id == cfg_id
        ).all()
        conditions = {row.key: row.value for row in rows}
        conditions.update(json.loads(conditions.pop(ActivityCondition.ConditionKeys.HOLDING.value)))
        conditions.update(json.loads(conditions.pop(ActivityCondition.ConditionKeys.BALANCE_VALUE.value)))
        conditions.update(json.loads(conditions.pop(ActivityCondition.ConditionKeys.TRADE_VALUE.value)))
        if (registered_key := ActivityCondition.ConditionKeys.REGISTERED_VALUE.value) in conditions:
            conditions.update(json.loads(conditions.pop(registered_key)))
        if (used_key := ActivityCondition.ConditionKeys.USED_VALUE.value) in conditions:
            value = conditions.pop(used_key)
            conditions[used_key] = value.split(",") if value else []
        return conditions

    @classmethod
    def format_key(cls, activity_id: int, lang: Language):
        return "{}_{}".format(activity_id, lang.value)

    @classmethod
    def get(cls, activity_id: int, lang: Language) -> Optional[str]:
        key_ = cls.format_key(activity_id, lang)
        return cls().hget(key_)


class DepositBonusActivityBannerCache(StringCache):
    """充值福利活动触达 banner 信息"""

    def __init__(self, lang: Language):
        super().__init__(f"{lang.name}")

    @classmethod
    def reload(cls):
        now_ = now()
        model = DepositBonusActivity
        rows = model.query.with_entities(
            model.id,
            model.asset,
            model.start_time,
            model.end_time,
        ).filter(
            model.status == model.StatusType.ONLINE,
            model.start_time <= now_,
            model.end_time > now_,
        ).all()
        c_model = DepositBonusActivityContent
        c_rows = c_model.query.filter(
            c_model.deposit_bonus_id.in_([row.id for row in rows])
        ).all()
        contents = {}
        for c_row in c_rows:
            contents[(c_row.deposit_bonus_id, c_row.lang)] = c_row.title
        old_keys = {lang for lang in Language}
        ret = defaultdict(list)
        for row in rows:
            for lang in Language:
                key = (row.id, lang)
                if not (title := contents.get(key)):
                    continue
                ret[lang].append({
                    'id': row.id,
                    'asset': row.asset,
                    'start_time': int(row.start_time.timestamp()),
                    'end_time': int(row.end_time.timestamp()),
                    'title': title
                })

        for lang, items in ret.items():
            cls(lang).set(json.dumps(items, cls=JsonEncoder))
            old_keys.remove(lang)
        for lang in old_keys:
            cls(lang).delete()


class CouponWarningCache(StringCache):

    TTL = 60 * 60 * 24

    class WarningType(Enum):
        THRESHOLD = 'threshold'  # 资金低于阈值
        OWE = 'owe'  # 资金不足

    def __init__(self, warning_type: WarningType):
        super().__init__(warning_type.name)

    def gen(self):
        self.value = '1'
        self.expire(self.TTL)



