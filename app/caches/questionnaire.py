import json

from app import Language
from app.caches import Hash<PERSON>ache, StringCache
from app.exceptions import RecordNotFound
from app.models.mongo.questionnaire import QuestionnaireMySQL, QuestionnaireContentMySQL
from app.utils.parser import JsonEncoder


class QuestionnaireCache(HashCache):
    model = QuestionnaireMySQL
    ttl = 86400 * 10

    def __init__(self, questionnaire_uid):
        self.questionnaire_uid = str(questionnaire_uid)
        super().__init__(f"{self.questionnaire_uid}")

    def reload(self):
        questionnaire = QuestionnaireMySQL.query.filter_by(
            uid=self.questionnaire_uid).with_entities(
                QuestionnaireMySQL.submit_limit,
                QuestionnaireMySQL.start_time,
                QuestionnaireMySQL.end_time
            ).first()
        if questionnaire is None:
            raise RecordNotFound

        data = {}
        completed_langs = []
        for questionnaire_content in QuestionnaireContentMySQL.query.filter_by(questionnaire_uid=self.questionnaire_uid).all():
            questionnaire_content_dict = questionnaire_content.to_dict(enum_to_name=True)
            questionnaire_content_dict.pop("id")
            questionnaire_content_dict.pop("created_at")
            questionnaire_content_dict.pop("updated_at")
            questionnaire_content_dict['questions'] = [
                {"question": q}
                for q in questionnaire_content_dict.get('questions', [])
            ]

            questionnaire_content_dict['submit_limit'] = questionnaire.submit_limit.name
            questionnaire_content_dict['start_time'] = questionnaire.start_time
            questionnaire_content_dict['end_time'] = questionnaire.end_time
            questionnaire_content_dict['completed_langs'] = completed_langs
            if questionnaire_content_dict.get('completed'):
                completed_langs.append(Language[questionnaire_content_dict['lang']].value)

            data[questionnaire_content.lang.name] = questionnaire_content_dict

        for lang, qc_dict in data.items():
            data[lang] = json.dumps(qc_dict, cls=JsonEncoder)

        self.save_by_ttl(data)

    def read_by_lang(self, lang):
        data = self.hget(lang.name)
        return json.loads(data) if data else {}

    def save_by_ttl(self, data):
        self.save(data)
        self.expire(self.ttl)


class QuestionnaireViewCountCache(StringCache):
    def __init__(self, questionnaire_uid, lang):
        super().__init__(f"{questionnaire_uid}_{lang}")


class QuestionnaireLoginViewCountCache(StringCache):
    def __init__(self, questionnaire_uid, lang):
        super().__init__(f"{questionnaire_uid}_{lang}")
