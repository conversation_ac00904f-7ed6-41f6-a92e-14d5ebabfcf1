# -*- coding: utf-8 -*-
from __future__ import annotations

import json
import time
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from itertools import chain
from operator import and_
from typing import List, Optional, Dict, Set
from sqlalchemy import func

from werkzeug.datastructures import MultiDict

from .base import StringCache, BitsCache, ListCache, HashCache, HyperLogLogCache, SetCache
from .flow_control import TimedHashCache
from .shadow import DBShadowCache
from ..common import Language, IncreaseEvent, ProductEvent, LastPushReadType, Media
from ..models import File, ShortLinkInfo, User, CharityActivity, CharityActivityContent, \
    CharityBanner, \
    CharityBannerContent, MarketingBanner, MarketingBannerContent, \
    PageInset, PageInsetContent, \
    AssetCirculationHistory, BlogDateReadCount, Blog, \
    CharityVideo, CharityCategory, CharityFootprint, PerpetualActivity, \
    PerpetualActivityContent, TipBar, TipBarContent, ReferralActivityBanner, \
    ReferralActivityBannerContent, \
    DynamicFalling, CharityFootprintContent, CharityFootprintCategory, NewInsetConfig, P2pActivity, P2pActivityBanner, \
    FooterConfig, CBoxPromotion, CBoxPromotionContent, CBoxThemeCategory, CBoxTheme, AssetMaintain

from ..models import Portrait
from ..models import StartPage, StartPageContent, EmailPush, NewerGuide, db, \
    NotificationBar, NotificationBarContent
from ..models.system import AppQuickEntranceList, AppQuickEntranceTrans, AppJumpList, AppActivate, \
    AppActivateContent, \
    NewAppActivate, NewAppActivateContent
from ..utils import now, datetime_to_time, current_timestamp, amount_to_str, today, AWSBucketPublic
from ..utils.parser import JsonEncoder


class AppQuickEntranceListCache(StringCache):
    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        records = AppQuickEntranceList.query.filter(
            AppQuickEntranceList.status == AppQuickEntranceList.Status.OPEN
        ).order_by(
            AppQuickEntranceList.sort_id
        ).all()
        trans = AppQuickEntranceTrans.query.all()
        entry_trans = MultiDict([(v.quick_entrance_id, (v.lang.value, v.name)) for v in trans])
        jump_dict = {v.id: v for v in AppJumpList.query.all()}
        result = [
            {
                "id": v.id,
                "trans": dict(entry_trans.getlist(v.id)),
                "sort_id": v.sort_id,
                "api_field": v.api_field,
                "jump_type": v.jump_type.value,
                "jump_uri": jump_dict[v.jump_id].jump_data,
                "light_pic_url": File.query.get(v.light_pic_id).static_url,
                "dark_pic_url": File.query.get(v.dark_pic_id).static_url,
                "entrance_type": v.entrance_type.name,
                "version": v.version,
                "is_default": v.is_default,
                "platforms": v.platform_list,
            } for v in records
        ]
        result.sort(key=lambda x: x["sort_id"])
        cls().set(json.dumps(result, cls=JsonEncoder))
        return result

    def read_by_version(self, version=None):
        records = super().read()
        if not records:
            return []
        records = json.loads(records)
        if version:
            return [i for i in records if i["version"] == version]
        else:
            return records


class AppActivateCache(StringCache):
    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        model = AppActivate
        content_model = AppActivateContent
        _now = now()
        records = model.query.filter(
            model.status == model.Status.VALID,
            and_(model.begin_at <= _now, model.end_at >= _now)
        ).order_by(
            model.sort_id
        ).all()
        trans = content_model.query.all()
        content_dict = defaultdict(dict)
        for tran in trans:
            content_dict[tran.activate_id][tran.lang.value] = {
                "title": tran.title,
                "sub_title": tran.sub_title
            }
        jump_dict = {v.id: v for v in AppJumpList.query.all()}
        result = [
            {
                "activate_type": v.activate_type.name,
                "begin_at": v.begin_at,
                "end_at": v.end_at,
                "trans": content_dict.get(v.id),
                "jump_type": v.jump_type.value if v.jump_type else "",
                "jump_uri": jump_dict[v.jump_id].jump_data if v.jump_type and v.jump_id else "",
                "light_pic_url": File.query.get(v.light_pic_id).static_url,
                "dark_pic_url": File.query.get(v.dark_pic_id).static_url,
                "sort_id": v.sort_id,
                "platforms": v.platform_list,
            } for v in records
        ]
        result.sort(key=lambda x: x["sort_id"])
        cls().set(json.dumps(result, cls=JsonEncoder))
        return result

    def read_by_type(self, activate_type=None):
        records = super().read()
        if not records:
            return []
        records = json.loads(records)
        if activate_type:
            return [i for i in records if i["activate_type"] == activate_type.name]
        else:
            return records


class NewAppActivateCache(StringCache):
    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        model = NewAppActivate
        content_model = NewAppActivateContent
        _now = now()
        records = model.query.filter(
            model.status == model.Status.VALID,
            and_(model.begin_at <= _now, model.end_at >= _now)
        ).order_by(
            model.sort_id
        ).all()
        trans = content_model.query.all()
        content_dict = defaultdict(dict)
        for tran in trans:
            content_dict[tran.activate_id][tran.lang.value] = {
                "title": tran.title,
                "sub_title": tran.sub_title,
                "light_pic_url": tran.light_pic_url,
                "dark_pic_url": tran.dark_pic_url,
            }
        jump_dict = {v.id: v for v in AppJumpList.query.all()}
        result = [
            {
                "id": v.id,
                "activate_type": v.activate_type.name,
                "content_style": v.content_style.name,
                "display_name": v.display_name,
                "begin_at": v.begin_at,
                "end_at": v.end_at,
                "jump_type": v.jump_type.value if v.jump_type else "",
                "jump_uri": jump_dict[v.jump_id].jump_data if v.jump_type and v.jump_id else "",
                "light_pic_url": v.light_pic_url,
                "dark_pic_url": v.dark_pic_url,
                "rtl_light_pic_url": v.rtl_light_pic_url,
                "rtl_dark_pic_url": v.rtl_dark_pic_url,
                "sort_id": v.sort_id,
                "platforms": v.platform_list,
                "trans": content_dict.get(v.id, {}),
            } for v in records
        ]
        result.sort(key=lambda x: x["sort_id"])
        cls().set(json.dumps(result, cls=JsonEncoder))
        return result

    def read_by_type(self, activate_type):
        records = super().read()
        if not records:
            return []
        records = json.loads(records)
        return [i for i in records if i["activate_type"] == activate_type.name]


class EmailPushCache(DBShadowCache):
    model = EmailPush
    ttl = 30

    Status = EmailPush.Status

    def __init__(self, id_: int):
        super().__init__(id_)

    @property
    def status(self):
        return self.dict['status']


class KunAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class KunAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class WooAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class WooAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class FnxAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class FnxAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class ChiAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class ChiAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class WestAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class WestAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class BanAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class BanAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class BrgAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class BrgAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class VlxAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class VlxAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class SrkAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class SrkAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class StcAirdropCache(StringCache):
    def __init__(self):
        super().__init__(None)


class StcAirDropConditionCache(BitsCache):
    def __init__(self, condition: str):
        """
        condition: "cet_position", "has_trade"
        """
        super().__init__(condition)


class AnnouncementCache(ListCache):

    def __init__(self, lang: str):
        super().__init__(lang)

    def get_top_announcements(self, limit: int = None) -> List[dict]:
        if limit is None:
            limit = -1
        else:
            limit -= 1
        r = self.lrange(0, limit)
        return [json.loads(x) for x in r]

    def save_announcements(self, announcements: List[dict]):
        if len(announcements) > 0:
            self.redis.lpush(self._key,
                             *[json.dumps(x) for x in announcements][::-1])
            self.redis.ltrim(self._key, 0, len(announcements) - 1)
        else:
            self.delete()


class AnnouncementCategoryCache(StringCache):
    """ zendesk公告类别（一级和二级） """
    def __init__(self, lang: str):
        super().__init__(lang)


class HelpCenterCache(ListCache):

    def __init__(self, lang: str):
        super().__init__(lang)

    def save_announcements(self, announcements: List[dict]):
        if len(announcements) > 0:
            self.redis.lpush(self._key,
                             *[json.dumps(x) for x in announcements][::-1])
            self.redis.ltrim(self._key, 0, len(announcements) - 1)
        else:
            self.delete()


class AnnouncementSectionArticleCache(ListCache):
    """ zendesk公告-二级类别-文章 """

    MAX_NUM = 200

    def __init__(self, lang: str, section_id: int):
        super().__init__(f"{lang}_{section_id}")

    def get_page_announcements(self, page: int, limit: int) -> List[dict]:
        start = max((page - 1) * limit, 0)
        end = start + limit - 1
        r = self.lrange(start, end)
        return [json.loads(x) for x in r]

    def save_announcements(self, announcements: List[dict]):
        if len(announcements) > 0:
            self.redis.lpush(self._key, *[json.dumps(x, cls=JsonEncoder) for x in announcements][::-1])
            self.redis.ltrim(self._key, 0, len(announcements) - 1)
        else:
            self.delete()


class HelpCenterCategoryCache(StringCache):
    """ zendesk帮助中心类别（一级和二级） """
    def __init__(self, lang: str):
        super().__init__(lang)


class HelpCenterSectionArticleCache(ListCache):
    """ zendesk帮助中心-二级类别-文章 """

    MAX_NUM = 200

    def __init__(self, lang: str, section_id: int):
        super().__init__(f"{lang}_{section_id}")

    def get_page_announcements(self, page: int, limit: int) -> List[dict]:
        start = max((page - 1) * limit, 0)
        end = start + limit - 1
        r = self.lrange(start, end)
        return [json.loads(x) for x in r]

    def save_announcements(self, announcements: List[dict]):
        if len(announcements) > 0:
            self.redis.lpush(self._key, *[json.dumps(x, cls=JsonEncoder) for x in announcements][::-1])
            self.redis.ltrim(self._key, 0, len(announcements) - 1)
        else:
            self.delete()


class TelegramPushedAnnouncementCache(ListCache):
    LANGS = [
        Language.EN_US,
        Language.ZH_HANT_HK,
        Language.AR_AE,
        Language.RU_KZ,
        Language.TR_TR,
        Language.JA_JP,
        Language.KO_KP,
        Language.VI_VN,
        Language.ID_ID,
        Language.TH_TH,
        Language.ES_ES,
        Language.PT_PT,
        Language.DE_DE,
        Language.FR_FR,
    ]

    def __init__(self, lang: str):
        super().__init__(lang)


class LotteryPrizeCache(StringCache):

    def __init__(self, prize_id: int):
        """
        only store left prize count
        """
        super().__init__(pk=str(prize_id))


class KYCAuditorsCache(TimedHashCache):

    def __init__(self, id_: int, institution_type: User.KYCType = User.KYCType.INDIVIDUAL):
        key = f'{id_}'
        if institution_type is User.KYCType.INSTITUTION:
            key = f'institution:{id_}'
        super().__init__(key, interval=15)

    @property
    def auditors(self) -> List[int]:
        return list(map(int, self.list_values()))

    def add_auditor(self, user_id: int):
        self.add_value(str(user_id))


class AssignAuditorsCache(HashCache):

    def read(self) -> Dict[str, list]:
        data = super().read()
        return {k: json.loads(v) for k, v in data.items()}

    def get_auditor_data(self, auditor_id: int) -> list:
        auditor_id = str(auditor_id)
        if r := self.hget(auditor_id):
            return json.loads(r)
        return []

    def assign(self, auditor_id: int, ids: List[int]):
        auditor_id = str(auditor_id)
        data = self.read()
        data[auditor_id] = list(set(data.get(auditor_id, [])) | set(ids))
        for aid, ids_ in data.items():
            if aid == auditor_id:
                continue
            data[aid] = list(set(ids_) - set(ids))
        new_data = {k: json.dumps(v) for k, v in data.items() if v}
        self.save(new_data)

    def finish(self, bus_id: int):
        data = self.read()
        for auditor_id, ids in data.items():
            data[auditor_id] = [id_ for id_ in ids if id_ != bus_id]
        new_data = {k: json.dumps(v) for k, v in data.items() if v}
        self.save(new_data)

    def batch_finish(self, bus_ids: Set[int]):
        data = self.read()
        bus_ids = set(bus_ids)
        for auditor_id, ids in data.items():
            data[auditor_id] = [id_ for id_ in ids if id_ not in bus_ids]
        new_data = {k: json.dumps(v) for k, v in data.items() if v}
        self.save(new_data)



class KYCAssignAuditorsCache(AssignAuditorsCache):

    def __init__(self):
        super().__init__(None)


class KYCProAssignAuditorsCache(AssignAuditorsCache):

    def __init__(self):
        super().__init__(None)


class LivenessAssignAuditorsCache(AssignAuditorsCache):

    def __init__(self):
        super().__init__(None)


class SecurityResetApplicationAssignAuditorsCache(AssignAuditorsCache):

    def __init__(self):
        super().__init__(None)


class AbnormalDepositApplicationAssignAuditorsCache(AssignAuditorsCache):

    def __init__(self):
        super().__init__(None)


class RiskScreenOldKycCursor(StringCache):
    """ 老KYC用户进行风险筛查 游标 """
    ttl = 3600 * 24 * 2

    def __init__(self):
        super().__init__(None)


class RiskScreenOldKycUserCache(TimedHashCache):
    """ 老KYC用户进行风险筛查 不再发KYC通过邮件 """

    def __init__(self):
        super().__init__("", interval=3600 * 24 * 7)

    @property
    def user_ids(self) -> List[int]:
        return list(map(int, self.list_values()))

    def add_user_id(self, user_id: int):
        self.add_value(str(user_id))


class ShortLinkInfoCache(HashCache):
    """
    短链接信息缓存
    { short_link.short_url : short_link_info_dict_str }
    """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def _format_key(cls, short_link: ShortLinkInfo) -> str:
        return short_link.short_url

    @classmethod
    def _format_short_link(cls, short_link: ShortLinkInfo) -> dict:
        return {
            "id": short_link.id,
            "original_url": short_link.original_url,
        }

    @classmethod
    def reload(cls):
        rows = ShortLinkInfo.query.filter(
            ShortLinkInfo.status == ShortLinkInfo.StatusType.VALID,
        ).all()
        cache_data_map = {}
        for row in rows:
            key_ = cls._format_key(row)
            data_ = cls._format_short_link(row)
            cache_data_map[key_] = json.dumps(data_, cls=JsonEncoder)

        instance = cls()
        if not cache_data_map:
            instance.delete()
            return
        old_keys = instance.hkeys()
        instance.hmset(cache_data_map)
        del_keys = [i for i in old_keys if i not in cache_data_map]
        if del_keys:
            instance.hdel(*del_keys)

    @classmethod
    def reload_one(cls, short_link_id: int):
        row = ShortLinkInfo.query.get(short_link_id)
        cache_data_map = {cls._format_key(row): json.dumps(cls._format_short_link(row), cls=JsonEncoder)}
        instance = cls()
        instance.hmset(cache_data_map)

    @classmethod
    def reload_many(cls, short_link_ids: list[int]):
        rows = ShortLinkInfo.query.filter(ShortLinkInfo.id.in_(short_link_ids)).all()
        cache_data_map = {
            cls._format_key(row): json.dumps(cls._format_short_link(row), cls=JsonEncoder)
            for row in rows
        }
        instance = cls()
        instance.hmset(cache_data_map)

    @classmethod
    def delete_many(cls, short_link_ids: List[int]):
        rows = ShortLinkInfo.query.filter(
            ShortLinkInfo.id.in_(short_link_ids),
        ).all()
        keys_ = [cls._format_key(row) for row in rows]
        instance = cls()
        instance.hdel(*keys_)

    @classmethod
    def get(cls, short_url: str) -> Optional[dict]:
        if data := cls().hget(short_url):
            return json.loads(data)
        return None


class ShortLinkPVCache(HashCache):
    """
    短链接-每日访问次数缓存
    { short_link.id : count }
    """

    ttl = 86400 * 3

    def __init__(self, date_: date):
        date_str = date_.strftime("%Y%m%d")
        super().__init__(date_str)

    def add(self, short_link_id: int) -> None:
        field_key = str(short_link_id)
        self.hincrby(field_key, 1)
        self.expire(self.ttl)


class ShortLinkNextPVPageCache(HashCache):
    """
    短链接-新打开页面的次数, 通过前端上报
    { short_link.short_url : count }
    """

    ttl = 86400 * 3

    def __init__(self, date_: date):
        date_str = date_.strftime("%Y%m%d")
        super().__init__(date_str)

    def add(self, short_url: str) -> None:
        self.hincrby(short_url, 1)
        self.expire(self.ttl)


class ShortLinkUVCache(HyperLogLogCache):
    """ 短链接-每日访问用户数缓存 """

    ttl = 86400 * 3

    def __init__(self, date_: date, short_link_id: int):
        date_str = date_.strftime("%Y%m%d")
        pk = f"{date_str}-{short_link_id}"
        super().__init__(pk)

    def add(self, user_id: int) -> None:
        self.pfadd(user_id)
        self.expire(self.ttl)


class ShortLinkIPCache(HyperLogLogCache):
    """ 短链接-每日访问IP数缓存 """

    ttl = 86400 * 3

    def __init__(self, date_: date, short_link_id: int):
        date_str = date_.strftime("%Y%m%d")
        pk = f"{date_str}-{short_link_id}"
        super().__init__(pk)

    def add(self, ip: str) -> None:
        self.pfadd(ip)
        self.expire(self.ttl)


class ShortLinkDeviceCache(HyperLogLogCache):
    """ 短链接-每日访问设备数缓存 """

    ttl = 86400 * 3

    def __init__(self, date_: date, short_link_id: int):
        date_str = date_.strftime("%Y%m%d")
        pk = f"{date_str}-{short_link_id}"
        super().__init__(pk)

    def add(self, device_id: str) -> None:
        self.pfadd(device_id)
        self.expire(self.ttl)


class ShortLinkUserSetCache(SetCache):
    """ 短链接-每日用户访问集合缓存 """

    ttl = 86400 * 2

    def __init__(self, date_: date, short_link_id: int):
        date_str = date_.strftime("%Y%m%d")
        pk = f"{date_str}-{short_link_id}"
        super().__init__(pk)


class StartPageCache(StringCache):
    """
    启动页-语言配置缓存(等新APP启动页上线后删除)
    """
    model = StartPage

    def __init__(self, lang: Language):
        super().__init__(lang.name)

    @classmethod
    def reload(cls):
        now_ = now()
        start_page = cls.model.query.filter(
            cls.model.start_at <= now_,
            cls.model.end_at > now_
        ).first()
        if not start_page:
            for lang in StartPage.AVAILABLE_LANGS:
                cls(lang).delete()
            return
        rows = StartPageContent.query.filter(
            StartPageContent.page_id == start_page.id
        ).all()
        lang_data_mapper = {
            i.lang.name: i.img_src for i in rows
        }
        for lang in StartPage.AVAILABLE_LANGS:
            cache = cls(lang)
            if lang.name not in lang_data_mapper.keys():
                lang = Language.DEFAULT
            cache.set(json.dumps(dict(
                return_url=start_page.return_url,
                show_time=start_page.show_time,
                img_src=lang_data_mapper.get(lang.name, "")
            ), cls=JsonEncoder))


class RiskUserNoticeCache(StringCache):
    # 风控信息每增加10条告警一次
    def __init__(self):
        super().__init__(None)


class SecurityResetNoticeCache(StringCache):
    # 重置安全工具每增加100条告警一次
    def __init__(self):
        super().__init__(None)


class AuditedWithdrawalCountCache(HashCache):
    MIN_ID = 'min_id'
    ID_TS = 'id_ts'
    COUNT = 'count'
    COUNT_TS = 'count_ts'
    SLACK_NOTICE_COUNT = 'slack_notice_count'
    SLACK_NOTICE_TS = 'slack_notice_ts'
    MOBILE_NOTICE_TS = 'mobile_notice_ts'

    """
    {
        "min_id": xxxx,
        "id_ts": xxxx,
        "count_ts": xxxx,
        "count": xxxx,
        "mobile_notice_ts": xxxx,
        "slack_notice_count": xxxx,
        "slack_notice_ts": xxxx
    }
    """
    # 已审核
    def __init__(self):
        super().__init__(None)

    def get_int_data(self, field: str) -> int:
        if value := self.hget(field):
            return int(value)
        return 0

    def get_last_slack_ts(self):
        return self.get_int_data(self.SLACK_NOTICE_TS)

    def get_last_mobile_ts(self):
        return self.get_int_data(self.MOBILE_NOTICE_TS)

    def set_id_ts(self, _id: int):
        return self.hmset(
            {
                self.MIN_ID: str(_id),
                self.ID_TS: str(current_timestamp(to_int=True))
            }
        )

    def get_id_ts(self) -> int:
        return self.get_int_data(self.ID_TS)

    def set_count(self, count: int):
        return self.hmset(
            {
                self.COUNT: str(count),
                self.COUNT_TS: str(current_timestamp(to_int=True))
            }
        )

    def set_slack_notice_count(self, count: int):
        return self.hmset(
            {
                self.SLACK_NOTICE_COUNT: str(count),
                self.SLACK_NOTICE_TS: str(current_timestamp(to_int=True))
            }
        )


class AuditRequiredWithdrawalCountCache(HashCache):

    MIN_ID = 'min_id'
    ID_TS = 'id_ts'
    COUNT = 'count'
    COUNT_TS = 'count_ts'
    SLACK_NOTICE_COUNT = 'slack_notice_count'
    SLACK_NOTICE_TS = 'slack_notice_ts'
    MOBILE_NOTICE_TS = 'mobile_notice_ts'

    """
    {
        "min_id": xxxx,
        "id_ts": xxxx,
        "count_ts": xxxx,
        "count": xxxx,
        "mobile_notice_ts": xxxx,
        "slack_notice_count": xxxx,
        "slack_notice_ts": xxxx
    }
    """
    # 已审核
    def __init__(self):
        super().__init__(None)

    def get_int_data(self, field: str) -> int:
        if value := self.hget(field):
            return int(value)
        return 0

    def get_last_slack_ts(self):
        return self.get_int_data(self.SLACK_NOTICE_TS)

    def get_last_mobile_ts(self):
        return self.get_int_data(self.MOBILE_NOTICE_TS)

    def set_id_ts(self, _id: int):
        return self.hmset(
            {
                self.MIN_ID: str(_id),
                self.ID_TS: str(current_timestamp(to_int=True))
            }
        )

    def get_id_ts(self) -> int:
        return self.get_int_data(self.ID_TS)

    def set_count(self, count: int):
        return self.hmset(
            {
                self.COUNT: str(count),
                self.COUNT_TS: str(current_timestamp(to_int=True))
            }
        )

    def set_slack_notice_count(self, count: int):
        return self.hmset(
            {
                self.SLACK_NOTICE_COUNT: str(count),
                self.SLACK_NOTICE_TS: str(current_timestamp(to_int=True))
            }
        )


class KycNoticeCache(StringCache):
    # KYC待审核数每增加100条告警一次
    def __init__(self):
        super().__init__(None)


class RiskScreenNoticeCache(StringCache):
    # 风险筛查待审核数每增加500条告警一次
    def __init__(self):
        super().__init__(None)


class CreditRiskNoticeCache(StringCache):
    """ 授信用户风险提示缓存（用于N小时只提示一次） """

    ttl = 3600 * 6

    def __init__(self, notice_type: str, user_id: int):
        key_ = f"{user_id}-{notice_type}"
        super().__init__(key_)


class EmailPushReadCache(HyperLogLogCache):
    """邮件推送-邮件打开数"""

    def __init__(self, email_push_id: int):
        pk = f"{email_push_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class EmailPushReadHourTimestampCache(HyperLogLogCache):
    """每小时邮件打开数（用户去重）"""

    def __init__(self, hour_ts: int, email_push_id: int):
        pk = f"{hour_ts}-{email_push_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class PushUrlUserClickCache(HyperLogLogCache):
    """推送-永久用户访问数缓存"""

    def __init__(self, url_id: int):
        pk = f"{url_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class PushUrlUserClickHourTimestampCache(HyperLogLogCache):
    """推送-每小时用户访问数缓存（用户去重）"""

    def __init__(self, hour_ts: int, url_id: int):
        pk = f"{hour_ts}-{url_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class EmailPushedUserCache(HyperLogLogCache):
    """邮件推送-已推送用户"""
    ttl = 30 * 86400

    def __init__(self, app_push_id: int):
        pk = f"{app_push_id}"
        super().__init__(pk)
        # 添加一个不存在用户，以使过期能够生效
        self.add("0")
        self.expire(self.ttl)

    def add(self, user_id: str) -> int:
        return int(self.pfadd(user_id))


class AppPushReadCache(HyperLogLogCache):
    """APP推送-推送打开数"""
    ttl = 3 * 86400

    def __init__(self, app_push_id: int):
        pk = f"{app_push_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class AppPushReadHourTimestampCache(HyperLogLogCache):
    """每小时APP推送打开数（用户去重）"""

    def __init__(self, hour_ts: int, app_push_id: int):
        pk = f"{hour_ts}-{app_push_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class AppPushAvailableCache(SetCache):
    event = None

    def __init__(self, platform):
        super().__init__(platform)

    def add_device(self, device_id: str):
        self.sadd(device_id)

    def del_device(self, device_id: str):
        self.srem(device_id)

    def get_count(self):
        return self.scard()


class AppPushAvailableUserCache(HashCache):
    event = None

    def __init__(self, platform):
        super().__init__(platform)

    def _add_device(self, user_id: int, device_id: str) -> bool:
        value, new_value = self.hget(user_id), None
        if not value:
            new_value = device_id
        elif device_id not in value:
            # device_id not exists
            new_value = f"{value},{device_id}"
        else:
            # device_id exists, do nothing
            pass

        if new_value:
            self.hset(user_id, new_value)
            return True
        return False

    def _del_device(self, user_id: int, device_id: str) -> bool:
        value, new_value = self.hget(user_id), None
        if not value:
            # not exits, do nothing
            pass
        elif device_id in value:
            # device_id exists, remove it
            new_value = ','.join([i for i in value.split(',') if i!= device_id])
        else:
            # device_id not exists, do nothing
            pass
        if new_value is not None and new_value != value:
            self.hset(user_id, new_value or "")
            return True
        return False

    def add_device(self, user_id: int, device_id: str) -> bool:
        return self._add_device(user_id, device_id)

    def del_device(self, user_id: int, device_id: str) -> bool:
        return self._del_device(user_id, device_id)

    def get_count(self):
        res = self.hval()
        return sum(1 for item in res if item)


class AppNotificationAvailableCache(AppPushAvailableCache):
    """打开app push开关的设备"""
    event = IncreaseEvent.PUSH_PERMISSION_ENABLE_COUNT


class GesturePwdAvaiableCache(AppPushAvailableCache):
    """手势密码开启设备数"""
    event = ProductEvent.GESTURE_PASSWORD_ENABLE_COUNT


class FingerPwdAvaiableCache(AppPushAvailableCache):
    """指纹密码开启设备数"""
    event = ProductEvent.FINGER_PASSWORD_ENABLE_COUNT


class PushDepositWithdrawalAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-充值提现"""
    event = ProductEvent.PUSH_DEPOSIT_WITHDRAWAL_ENABLE_COUNT


class PushSmartTrackFavAssetAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-智能盯盘-自选币种"""
    event = ProductEvent.PUSH_SMART_TRACK_FAVORITE_ASSET_COUNT


class PushSmartTrackHoldAssetAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-智能盯盘-持仓币种"""
    event = ProductEvent.PUSH_SMART_TRACK_HOLD_ASSET_COUNT


class PushSpotLimitOrderAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-交易订单-币币交易-限价订单通知"""
    event = ProductEvent.PUSH_SPOT_LIMIT_ORDER_COUNT


class PushSpotStopOrderAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-交易订单-币币交易-计划委托通知"""
    event = ProductEvent.PUSH_SPOT_STOP_ORDER_COUNT


class PushPerpetualLimitOrderAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-交易订单-合约交易-限价订单通知"""
    event = ProductEvent.PUSH_PERPETUAL_LIMIT_ORDER_COUNT


class PushPerpetualStopOrderAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-交易订单-合约交易-计划委托通知"""
    event = ProductEvent.PUSH_PERPETUAL_STOP_ORDER_COUNT


class PushPerpetualTPAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-交易订单-合约交易-止盈止损通知"""
    event = ProductEvent.PUSH_PERPETUAL_TAKE_PROFIT_COUNT


class PushOperationNewAssetAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-运营动态-上新公告"""
    event = ProductEvent.PUSH_OPERATION_NEW_ASSET_COUNT


class PushOperationWellfareAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-运营动态-福利活动"""
    event = ProductEvent.PUSH_OPERATION_WELLFARE_COUNT


class PushOperationBlogAvaiableCache(AppPushAvailableCache):
    """PUSH开启设备数-运营动态-博客"""
    event = ProductEvent.PUSH_OPERATION_BLOG_COUNT


class OpenHistoryOrderCache(AppPushAvailableCache):
    """K线显示历史委托开启人数"""
    event = ProductEvent.OPEN_HISTORY_ORDER


event_cache_mapping = {i.event.value: i for i in AppPushAvailableCache.__subclasses__()}


class GesturePwdAvaiableUserCache(AppPushAvailableUserCache):
    """手势密码开启设备数"""
    event = ProductEvent.GESTURE_PASSWORD_ENABLE_USER_COUNT


class FingerPwdAvaiableUserCache(AppPushAvailableUserCache):
    """指纹密码开启设备数"""
    event = ProductEvent.FINGER_PASSWORD_ENABLE_USER_COUNT


class FaceIDAvaiableUserCache(AppPushAvailableUserCache):
    """面容ID开启设备数"""
    event = ProductEvent.FACI_ID_PASSWORD_ENABLE_USER_COUNT


class KlineBuySellRecordUserCache(AppPushAvailableUserCache):
    """K线图表设置买卖记录点（跟用户账户）"""
    event = ProductEvent.KLINE_BUY_SELL_RECORD_ENABLE_COUNT


event_user_cache_mapping = {i.event.value: i for i in AppPushAvailableUserCache.__subclasses__()}


class PopupPageViewCache(HyperLogLogCache):
    """弹窗推送"""

    def __init__(self, app_push_id: int):
        pk = f"{app_push_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class PopupPageViewHourTimestampCache(HyperLogLogCache):
    """每小时弹窗流量量"""

    def __init__(self, hour_ts: int, popup_id: int):
        pk = f"{hour_ts}-{popup_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class PopupClickCountCache(HyperLogLogCache):
    """弹窗推送"""

    def __init__(self, popup_id: int):
        pk = f"{popup_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class PopupClickCountHourTimestampCache(HyperLogLogCache):
    """每小时弹窗点击量"""

    def __init__(self, hour_ts: int, app_push_id: int):
        pk = f"{hour_ts}-{app_push_id}"
        super().__init__(pk)

    def add(self, token: str) -> int:
        return int(self.pfadd(token))


class MessagePushReadCountCache(HyperLogLogCache):
    """ 站内信推送 已读缓存 """

    def __init__(self, msg_push_id: int):
        pk = f"{msg_push_id}"
        super().__init__(pk)

    def add(self, user_id: int) -> int:
        return int(self.pfadd(str(user_id)))


class MessagePushReadCountHourTimestampCache(HyperLogLogCache):
    """ 站内信推送 每小时已读缓存 """

    def __init__(self, hour_ts: int, msg_push_id: int):
        pk = f"{hour_ts}-{msg_push_id}"
        super().__init__(pk)

    def add(self, user_id: int) -> int:
        return int(self.pfadd(str(user_id)))


class MessagePushClickCountCache(MessagePushReadCountCache):
    """ 站内信推送 跳转链接点击缓存 """
    pass


class MessagePushClickCountHourTimestampCache(MessagePushReadCountHourTimestampCache):
    """ 站内信推送 跳转链接每小时点击缓存 """
    pass


class PushStatisticCache(HyperLogLogCache):
    """ 推送统计 """

    def __init__(self, push_key, id_, hour_ts=None):
        if hour_ts:
            pk = f"{push_key}:{id_}-{hour_ts}"
        else:
            pk = f"{push_key}:{id_}"
        super().__init__(pk)


class PushStatisticTimeStampSetCache(SetCache):
    """ 有数据时间段集合缓存 """

    def __init__(self, push_key, id_):
        pk = f"{push_key}:{id_}"
        super().__init__(pk)


class MessagePushContentCache(HashCache):
    """ 站内信推送内容缓存 """

    TTL = 86400 * 100  # 站内信保留3个月

    def __init__(self, msg_push_id: int):
        pk = f"{msg_push_id}"
        super().__init__(pk)

    def save_contents(self, lang_content_map: Dict[str, Dict]):
        # data: {lang.name: {'title': '123', 'content': '123'}}
        data_map = {lang: json.dumps(v, cls=JsonEncoder) for lang, v in lang_content_map.items()}
        self.hmset(data_map)
        self.expire(self.TTL)

    def get_content(self, lang: str) -> Dict:
        data = self.hget(lang)
        return json.loads(data) if data else {}


class AbnormalDepositApplicationNoticeCache(StringCache):
    """ 资产自助找回申请待审核数每增加100条告警一次 """

    def __init__(self):
        super().__init__(None)


class AbnormalDepositApplicationAdditionNoticeCache(HashCache):
    """ 资产自助找回申请-待补充资料-提醒 保存时间、是否再次提醒 """

    TTL = 86400 * 15

    def __init__(self):
        super().__init__(None)

    @classmethod
    def add_many(cls, application_ids: List[int]) -> int:
        cache = cls()
        keys_ = {str(i) for i in application_ids}
        exist_keys = cache.hkeys()
        diff_keys = keys_ - set(exist_keys)
        if diff_keys:
            default_data = json.dumps(
                {
                    "time": int(now().timestamp()),
                    "has_re_notice": 0,
                },
                cls=JsonEncoder,
            )
            cache.hmset({k: default_data for k in diff_keys})
            cache.expire(cls.TTL)
        return len(diff_keys)


class NewerGuideCache(StringCache):
    def __init__(self, lang: str):
        super().__init__(lang)

    @classmethod
    def reload(cls):
        rows = NewerGuide.query.filter(NewerGuide.status == NewerGuide.Status.VALID).all()
        res = defaultdict(list)
        for row in rows:
            res[row.lang.name].append(row.to_dict(enum_to_name=True))
        for lang in NewerGuide.AVAILABLE_LANGS:
            lang_items = defaultdict(list)
            ori_data = res.get(lang.name)
            if not ori_data:
                continue
            for item in ori_data:
                lang_items[item.get('category')].append(item)
            for k, v in lang_items.items():
                if not v or len(v) > 6 or len(v) < 3:
                    v = []
                else:
                    v = sorted(v, key=lambda x: x['sort_id'])
                lang_items[k] = v
            cls(lang.name).set(json.dumps(lang_items, cls=JsonEncoder))


class BlogDisplayCache(StringCache):
    def __init__(self, lang: str):
        super().__init__(lang)

    MIN_BLOG_NUM = 3  # 正常状态下的博客数量小于3个的语种不展示

    @classmethod
    def reload(cls):
        blog_lang_dict = defaultdict(list)
        blog_rows = Blog.query.filter(
            Blog.status == Blog.Status.VALID
        ).order_by(Blog.published_at.desc()).all()
        for row in blog_rows:
            tmp = row.to_dict(enum_to_name=True)
            tmp['keywords'] = tmp.get('keywords').split(',') if tmp.get('keywords') else ''
            blog_lang_dict[row.lang.name].append(tmp)
        for lang in Blog.AVAILABLE_LANGS:
            v = blog_lang_dict.get(lang.name)
            if not v or len(v) < cls.MIN_BLOG_NUM:
                v = []
            else:
                v = v[:cls.MIN_BLOG_NUM]
            cls(lang.name).set(json.dumps(v, cls=JsonEncoder))


class PopularBlogCache(StringCache):
    def __init__(self, lang: str):
        super().__init__(lang)

    MIN_BLOG_NUM = 5  # 正常状态下的有阅读数的博客数量小于5个的语种不展示

    @classmethod
    def reload(cls):
        today_date = today()
        yesterday_date = today_date - timedelta(days=1)
        start_date = yesterday_date - timedelta(days=15)
        read_count_query = BlogDateReadCount.query.filter(
            BlogDateReadCount.report_date >= start_date
        ).group_by(
            BlogDateReadCount.blog_id
        ).with_entities(
            BlogDateReadCount.blog_id,
            func.sum(BlogDateReadCount.read_count).label(
                "read_count"
            ),
        )

        blog_read_map = {i.blog_id: i.read_count for i in read_count_query.all()}
        blog_ids = list(blog_read_map.keys())

        blog_query = Blog.query.filter(Blog.id.in_(blog_ids), Blog.status == Blog.Status.VALID).all()

        blog_lang_map = defaultdict(list)
        for row in blog_query:
            blog_lang_map[row.lang.value].append(dict(
                read_count=int(blog_read_map[row.id]),
                title=row.title,
                seo_url_keyword=row.seo_url_keyword,
                id=row.id,
                published_at=int(row.published_at.timestamp()),
            ))
        for lang in Blog.AVAILABLE_LANGS:
            v = blog_lang_map.get(lang.value)
            if not v or len(v) < cls.MIN_BLOG_NUM:
                v = []
            else:
                v = sorted(v, key=lambda x: x['read_count'], reverse=True)[:cls.MIN_BLOG_NUM]
            cls(lang.value).set(json.dumps(v, cls=JsonEncoder))


class CurrentPortraitHashCache(HashCache):
    """当前头像url缓存"""

    def __init__(self):
        super().__init__(None)

    def read(self) -> Dict[str, dict]:
        data = super().read()
        return {k: json.loads(v) for k, v in data.items()}

    def reload(self):
        model = Portrait
        portrait_list = model.get_current()
        if not portrait_list:
            data = {
                model.Platform.ALL.name: {
                    "file_key": "",
                    "file_url": "",
                    "night_url": "",
                    "day_border_url": "",
                    "night_border_url": "",
                }
            }
        else:
            data = defaultdict(dict)
            for portrait in portrait_list:
                platform = portrait.platform.name
                data[platform]['file_key'] = portrait.file_key
                for key, url_name in model.IMAGE_KEY_MAP.items():
                    data[platform][url_name] = ""
                    if filed_key := getattr(portrait, key, ""):
                        data[platform][url_name] = AWSBucketPublic.get_file_url(filed_key)
        data = {k: json.dumps(v) for k, v in data.items()}
        self.save(data)

    def get_default_portrait(self):
        from app.api.common import get_request_platform
        cache_dict = self.read()
        if not cache_dict:
            return ""
        if Portrait.Platform.ALL.name in cache_dict:
            return cache_dict[Portrait.Platform.ALL.name].get("file_key", "")
        platform = Portrait.Platform.WEB
        try:
            if get_request_platform().is_mobile():
                platform = Portrait.Platform.APP
        except Exception:
            pass
        return cache_dict.get(platform.name, {}).get("file_key", "")


class NotificationBarCache(StringCache):
    """通知栏缓存"""

    model = NotificationBar
    ttl = 60 * 60 * 24

    def __init__(self, platform: NotificationBar.Platform, lang: Language):
        platform = platform.name
        lang = lang.name
        super(NotificationBarCache, self).__init__(f"{platform}:{lang}")

    @classmethod
    def reload(cls):
        _now = now()
        cls._update_record_status(_now)
        cls._reload_cache(_now)

    @classmethod
    def _update_record_status(cls, _now):
        model = cls.model
        model.query.filter(
            model.status == model.Status.AUDITED,
            model.end_at < _now,
        ).update({model.status: model.Status.FINISHED})

        db.session.commit()

    @classmethod
    def _reload_cache(cls, _now):
        model = cls.model
        rows = model.query.filter(
            model.status == model.Status.AUDITED,
            model.begin_at < _now,
            model.end_at > _now,
        ).all()

        bars = defaultdict(list)
        bar_ids = []
        jump_ids = []
        for row in rows:
            bars[row.platform].append(row)
            bar_ids.append(row.id)
            if row.jump_page_enabled:
                jump_ids.append(row.jump_id)
        jump_mapping = cls._get_jump_mapping(jump_ids)

        contents = NotificationBarContent.query.filter(NotificationBarContent.notification_bar_id.in_(bar_ids)).all()
        contents_mapping = {(content.notification_bar_id, content.lang): content for content in contents}
        result = defaultdict(list)
        for platform in (model.Platform.WEB, model.Platform.APP):
            for bar in chain(bars[platform], bars[model.Platform.ALL]):
                for lang in Language:
                    if not (c := contents_mapping.get((bar.id, lang))):
                        continue

                    trigger_page = trigger_page_params = None
                    trigger_pages = bar.get_trigger_pages()
                    for trigger_page_map in trigger_pages:  # 兼容老版本 APP
                        trigger_page = trigger_page_map['trigger_page']
                        trigger_page_params = trigger_page_map['trigger_page_params']
                        break

                    result[(platform, lang)].append(dict(
                        id=bar.id,
                        platform=bar.platform.name,
                        trigger_pages=trigger_pages,
                        trigger_page=trigger_page,
                        trigger_page_params=trigger_page_params,
                        begin_at=datetime_to_time(bar.begin_at),
                        end_at=datetime_to_time(bar.end_at),
                        jump_id=bar.jump_id,
                        jump_page_enabled=bar.jump_page_enabled,
                        jump_type=bar.jump_type.name if bar.jump_type else '',
                        jump_url=jump_mapping.get(bar.jump_id) or '',
                        title=c.title,
                        content=c.content,
                        summary=c.summary,
                    ))

        old_keys = {(platform, lang) for platform in model.Platform for lang in Language}
        for k, v in result.items():
            v.sort(key=lambda item: item['id'], reverse=True)
            old_keys.remove(k)
            cls(*k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            cls(*k).delete()

    @classmethod
    def _get_jump_mapping(cls, jump_ids):
        jump_mapping = {}
        if jump_ids:
            jump_rows = AppJumpList.query.with_entities(
                AppJumpList.id,
                AppJumpList.jump_data,
            ).filter(
                AppJumpList.id.in_(jump_ids)
            ).all()
            for jump_row in jump_rows:
                jump_mapping[jump_row.id] = jump_row.jump_data

        return jump_mapping


class TipBarCache(StringCache):
    """提示条缓存"""

    ttl = 86400

    def __init__(self, platform: TipBar.Platform, lang: Language):
        platform = platform.name
        lang = lang.name
        super().__init__(f"{platform}:{lang}")

    @classmethod
    def reload(cls):
        _now = now()
        cls._update_record_status(_now)
        cls._reload_cache(_now)

    @classmethod
    def _update_record_status(cls, _now):
        from app.business.risk_control.base import RiskControlGroupConfig
        from app.schedules.statistics.market_monitor.base import send_basis_rate_no_trigger_alert, send_basis_rate_conflict_alert, \
            check_auto_tip_bar_conflict
        from app.schedules.statistics.market_monitor.margin_monitor import get_margin_basis_rate_trigger_count_map
        from app.schedules.statistics.market_monitor.perpetual_monitor import get_perpetual_basis_rate_trigger_count_map

        model = TipBar
        rows = model.query.filter(
            model.status == model.Status.AUDITED,
            model.ended_at < _now,
        ).all()
        margin_risk_config = RiskControlGroupConfig().margin_basis_rate_auto_tip
        margin_tip_duration_hours = int(margin_risk_config['margin_tip_duration_hours'])
        margin_trigger_count_map = get_margin_basis_rate_trigger_count_map(60)

        perpetual_risk_config = RiskControlGroupConfig().perpetual_basis_rate_auto_tip
        perpetual_tip_duration_hours = int(perpetual_risk_config['perpetual_tip_duration_hours'])
        perpetual_trigger_count_map = get_perpetual_basis_rate_trigger_count_map(60)

        not_trigger_alert_market_set = set()
        conflict_alert_market_set = set()
        for row in rows:
            if row.type == model.Type.PRICE_DEVIATION:
                trigger_pages = json.loads(row.trigger_pages)
                if not trigger_pages:
                    continue
                trigger_page = trigger_pages[0]['trigger_page']
                market = trigger_pages[0]['trigger_page_params'][0]
                if trigger_page == model.TriggerPage.SPOT_MARKET.name:
                    new_ended_at = row.ended_at + timedelta(hours=margin_tip_duration_hours)
                    if margin_trigger_count_map.get(market, 0) >= 1:
                        conflict = check_auto_tip_bar_conflict(row.id, market, model.TriggerPage.SPOT_MARKET,
                                                               row.started_at, new_ended_at)
                        if conflict:
                            row.status = model.Status.FINISHED
                            conflict_alert_market_set.add(market)
                        else:
                            row.ended_at = new_ended_at
                    else:
                        row.status = model.Status.FINISHED
                        not_trigger_alert_market_set.add(market)
                elif trigger_page == model.TriggerPage.PERPETUAL_MARKET.name:
                    new_ended_at = row.ended_at + timedelta(hours=perpetual_tip_duration_hours)
                    if perpetual_trigger_count_map.get(market, 0) >= 1:
                        conflict = check_auto_tip_bar_conflict(row.id, market, model.TriggerPage.PERPETUAL_MARKET,
                                                               row.started_at, new_ended_at)
                        if conflict:
                            row.status = model.Status.FINISHED
                            conflict_alert_market_set.add(market)
                        else:
                            row.ended_at = new_ended_at
                    else:
                        row.status = model.Status.FINISHED
                        not_trigger_alert_market_set.add(market)
            else:
                row.status = model.Status.FINISHED

        db.session.commit()
        send_basis_rate_no_trigger_alert(not_trigger_alert_market_set)
        send_basis_rate_conflict_alert(conflict_alert_market_set)

    @classmethod
    def _reload_cache(cls, _now):
        model = TipBar
        rows = model.query.filter(
            model.status == model.Status.AUDITED,
            model.started_at < _now,
            model.ended_at > _now
        ).all()

        jump_ids = []
        windows = defaultdict(list)
        for row in rows:
            windows[row.platform].append(row)
            if row.jump_page_enabled:
                jump_ids.append(row.jump_id)
        jump_mapping = cls._get_jump_mapping(jump_ids)
        # sql:19s
        contents = TipBarContent.query.filter(
            TipBarContent.tip_bar_id.in_([x.id for x in rows])
        ).all()
        contents = {(x.tip_bar_id, x.lang): x for x in contents}

        result = defaultdict(list)
        for platform in (model.Platform.WEB, model.Platform.APP):
            for window in chain(windows[platform], windows[model.Platform.ALL]):
                for lang in model.AVAILABLE_LANGS:
                    if not (c := contents.get((window.id, lang))):
                        continue

                    trigger_pages = window.get_trigger_pages()
                    result[(platform, lang)].append(dict(
                        id=window.id,
                        platform=window.platform.name,
                        trigger_pages=trigger_pages,
                        filter_type=window.filter_type.name,
                        started_at=datetime_to_time(window.started_at),
                        ended_at=datetime_to_time(window.ended_at),
                        jump_id=window.jump_id,
                        jump_page_enabled=window.jump_page_enabled,
                        jump_type=window.jump_type.name if window.jump_type else '',
                        jump_url=jump_mapping.get(window.jump_id) or '',
                        content=c.content,
                    ))

        old_keys = {(x, y) for x in model.Platform for y in Language}
        for k, v in result.items():
            old_keys.remove(k)
            TipBarCache(*k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            TipBarCache(*k).delete()

        for window in rows:
            if window.filter_type == model.FilterType.FILTERS and window.users:
                users = json.loads(window.users)
                cache = TipBarUsersCache(window.id)
                cache.value = users
                cache.expire(cls.ttl)

    @classmethod
    def _get_jump_mapping(cls, jump_ids):
        model = AppJumpList
        jump_mapping = {}
        if jump_ids:
            jump_rows = model.query.with_entities(
                model.id,
                model.jump_data,
            ).filter(
                model.id.in_(jump_ids)
            ).all()
            for jump_row in jump_rows:
                jump_mapping[jump_row.id] = jump_row.jump_data

        return jump_mapping

    def filter_by_user(self, user_id: int = None) -> List[Dict]:
        if not (v := self.read()):
            return []
        result = []
        v = json.loads(v)
        for item in v:
            if item['filter_type'] == TipBar.FilterType.NONE.name:
                result.append(item)
            elif user_id and TipBarUsersCache(item['id']).sismember(user_id):
                result.append(item)
        return result


class TipBarUsersCache(SetCache):

    def __init__(self, id_: int):
        super().__init__(str(id_))


class AssetMaintainConfigCache(StringCache):
    """币种维护缓存"""

    ttl = 86400

    def __init__(self, platform: AssetMaintain.Platform):
        platform = platform.name
        super().__init__(f"{platform}")

    @classmethod
    def reload(cls):
        _now = now()
        cls._update_record_status(_now)
        cls._reload_cache(_now)

    @classmethod
    def _update_record_status(cls, _now):
        model = AssetMaintain
        rows = model.query.filter(
            model.status == model.Status.AUDITED,
            model.ended_at < _now,
        ).all()
        for row in rows:
            row.status = model.Status.FINISHED
        db.session.commit()

    @classmethod
    def _reload_cache(cls, _now):
        model = AssetMaintain
        rows = model.query.filter(
            model.status == model.Status.AUDITED,
            model.started_at < _now,
            model.ended_at > _now
        ).all()

        jump_ids = []
        windows = defaultdict(list)
        for row in rows:
            windows[row.platform].append(row)
            if row.jump_page_enabled:
                jump_ids.append(row.jump_id)
        jump_mapping = cls._get_jump_mapping(jump_ids)
        result = defaultdict(list)
        for platform in (model.Platform.WEB, model.Platform.APP):
            for window in chain(windows[platform], windows[model.Platform.ALL]):
                if not window.should_tag_notify:
                    continue
                tag_pages = window.get_tag_pages()
                result[platform].append(dict(
                    id=window.id,
                    asset=window.asset,
                    platform=window.platform.name,
                    started_at=datetime_to_time(window.started_at),
                    ended_at=datetime_to_time(window.ended_at),
                    jump_id=window.jump_id,
                    jump_page_enabled=window.jump_page_enabled,
                    jump_type=window.jump_type.name if window.jump_type else '',
                    jump_url=jump_mapping.get(window.jump_id) or '',
                    tag_pages=tag_pages,
                    maintain_type=window.maintain_type.name,
                ))
        old_keys = {x for x in model.Platform}
        for k, v in result.items():
            old_keys.remove(k)
            cls(k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            cls(k).delete()

    @classmethod
    def _get_jump_mapping(cls, jump_ids):
        model = AppJumpList
        jump_mapping = {}
        if jump_ids:
            jump_rows = model.query.with_entities(
                model.id,
                model.jump_data,
            ).filter(
                model.id.in_(jump_ids)
            ).all()
            for jump_row in jump_rows:
                jump_mapping[jump_row.id] = jump_row.jump_data

        return jump_mapping


class CharityBannerCache(StringCache):
    TTL = 10 * 60

    def __init__(self, lang: Language) -> None:
        super().__init__(lang.value)

    @classmethod
    def reload(cls):
        lang_banner_mapper = defaultdict(list)
        records = CharityBanner.query.join(CharityBannerContent).filter(
            CharityBanner.status == CharityBanner.Status.ONLINE
        ).order_by(
            CharityBanner.sort_id
        ).with_entities(
            CharityBanner.id,
            CharityBanner.sort_id,
            CharityBanner.name,
            CharityBannerContent.lang,
            CharityBannerContent.file_key
        ).all()
        for record in records:
            lang_banner_mapper[record.lang].append(dict(
                banner_id=record.id,
                sort_id=record.sort_id,
                name=record.name,
                img_src=AWSBucketPublic.get_file_url(record.file_key)
            ))
        for lang, banners in lang_banner_mapper.items():
            cls(lang).set(json.dumps(banners[0:CharityBanner.MAX_BANNER_COUNT]), ex=cls.TTL)


class CharityActivitiesCache(StringCache):
    TTL = 10 * 60

    def __init__(self, lang: Language) -> None:
        super().__init__(lang.value)

    @classmethod
    def reload(cls):
        lang_activities_mapper = defaultdict(list)
        records = CharityActivity.query.join(CharityActivityContent).filter(
            CharityActivity.status == CharityActivity.Status.ONLINE
        ).order_by(
            CharityActivity.sort_id
        ).with_entities(
            CharityActivity.id,
            CharityActivity.sort_id,
            CharityActivityContent.title,
            CharityActivityContent.desc,
            CharityActivityContent.lang,
            CharityActivityContent.file_key
        ).all()
        for record in records:
            lang_activities_mapper[record.lang].append(dict(
                activity_id=record.id,
                sort_id=record.sort_id,
                title=record.title,
                desc=record.desc,
                img_src=AWSBucketPublic.get_file_url(record.file_key)
            ))
        for lang, activities in lang_activities_mapper.items():
            if len(activities) < CharityActivity.MIN_ACTIVITY_COUNT:
                continue
            cls(lang).set(json.dumps(activities), ex=cls.TTL)


class CharityVideoCache(StringCache):
    TTL = 10 * 60

    def __init__(self, lang: Language) -> None:
        super().__init__(lang.value)

    @classmethod
    def reload(cls):
        mapper = defaultdict(list)
        records = CharityVideo.query.filter(
            CharityVideo.status == CharityVideo.Status.ONLINE
        ).order_by(
            CharityVideo.sort_id.desc()
        ).with_entities(
            CharityVideo.id,
            CharityVideo.category_id,
            CharityVideo.sort_id,
            CharityVideo.name,
            CharityVideo.lang,
            CharityVideo.title,
            CharityVideo.url,
            CharityVideo.file_key,
            CharityVideo.created_at,
        ).all()
        category_lang_mapping = cls.get_category_lang_mapping()
        for record in records:
            category_mapping = category_lang_mapping.get(record.lang, {})
            category = category_mapping.get(record.category_id)
            mapper[record.lang].append(dict(
                id=record.id,
                created_at=int(record.created_at.timestamp()),
                category_id=record.category_id,
                category=category.name if category else '',
                sort_id=record.sort_id,
                title=record.title,
                url=record.url,
                img_src=AWSBucketPublic.get_file_url(record.file_key)
            ))
        for lang, items in mapper.items():
            cls(lang).set(json.dumps(items), ex=cls.TTL)

    @classmethod
    def get_category_lang_mapping(cls):
        mapping = {}
        for row in CharityCategory.query.filter(
                CharityCategory.type == CharityCategory.Type.ACTIVITY_VIDEO,
                CharityCategory.status == CharityCategory.Status.VALID,
        ).all():
            mapping.setdefault(row.lang, {}).update({row.id: row})
        return mapping


class CharityFootprintCache(StringCache):
    TTL = 10 * 60

    def __init__(self, lang: Language) -> None:
        super().__init__(lang.value)

    @classmethod
    def reload(cls):
        mapper = defaultdict(list)
        records = CharityFootprint.query.join(CharityFootprintContent).filter(
            CharityFootprint.status == CharityFootprint.Status.ONLINE
        ).with_entities(
            CharityFootprint.id,
            CharityFootprint.category_id,
            CharityFootprint.sort_id,
            CharityFootprint.created_at,
            CharityFootprint.is_use_blog,
            CharityFootprintContent.lang,
            CharityFootprintContent.blog_id,
            CharityFootprintContent.seo_url_keyword,
            CharityFootprintContent.seo_title,
            CharityFootprintContent.title,
            CharityFootprintContent.abstract,
            CharityFootprintContent.cover,
            CharityFootprintContent.content,
        ).order_by(
            CharityFootprint.sort_id.desc()
        ).all()
        category_lang_mapping = cls.get_category_lang_mapping()
        for record in records:
            category_id = record.category_id
            lang = record.lang
            cover_url = '' if not record.cover else AWSBucketPublic.get_file_url(record.cover)
            category_mapping = category_lang_mapping.get(category_id, {})
            if lang == Language.ZH_HANS_CN:
                category = category_mapping.get('cn_name', '')
            elif lang == Language.EN_US:
                category = category_mapping.get('en_name', '')
            else:
                category = ''
            mapper[lang].append(dict(
                id=record.id,
                category_id=category_id,
                category=category,
                sort_id=record.sort_id,
                seo_url_keyword=record.seo_url_keyword,
                seo_title=record.seo_title,
                blog_id=record.blog_id if record.is_use_blog else None,
                title=record.title,
                abstract=record.abstract,
                cover=cover_url,
                # content=record.content,   # 减小内存占用
                created_at=int(record.created_at.timestamp()),
            ))
        for lang, items in mapper.items():
            cls(lang).set(json.dumps(items), ex=cls.TTL)

    @classmethod
    def get_category_lang_mapping(cls):
        mapping = {}
        records = CharityFootprintCategory.query.filter(
            CharityFootprintCategory.status == CharityFootprintCategory.Status.VALID).all()
        for record in records:
            mapping[record.id] = {'cn_name': record.cn_name, 'en_name': record.en_name}
        return mapping


class MarketBannerCodeCache(HashCache):
    """市场营销-营销banner code缓存"""
    def __init__(self, lang: Language):
        lang = lang.name
        super().__init__(f"{lang}")


class MarketBannerCache(StringCache):
    """市场营销-默认banner 缓存"""

    def __init__(self, lang: Language):
        lang = lang.name
        super().__init__(f"{lang}")

    @classmethod
    def reload(cls):
        model = MarketingBanner
        _now = now()
        banners: List[MarketingBanner] = model.query.filter(
            model.status == model.Status.VALID,
            model.begin_at <= _now,
            model.end_at > _now,
            model.code.is_not(None),
        ).order_by(model.sort_id.asc()).all()

        content_model = MarketingBannerContent
        contents = content_model.query.filter(
            content_model.owner_id.in_([x.id for x in banners]),
            content_model.file_key != ''
        ).all()
        content_map = {(x.owner_id, x.lang): x for x in contents}

        default_banners = []
        marketing_banners = []
        for banner in banners:
            if banner.type == model.Type.DEFAULT:
                default_banners.append(banner)
            elif banner.type == model.Type.MARKETING:
                marketing_banners.append(banner)

        lang_default_banner_map = {}
        lang_code_banner_map = defaultdict(dict)
        for banner in default_banners:
            for lang in Language:
                if not (c := content_map.get((banner.id, lang))):
                    continue
                _info = cls.format_item(banner, c)
                lang_default_banner_map[lang] = _info
                lang_code_banner_map[lang][banner.code] = _info

        for banner in marketing_banners:
            if c := content_map.get((banner.id, banner.lang)):
                _info = cls.format_item(banner, c)
                # 营销banner 不同语言都用相同的banner
                for lang in Language:
                    lang_code_banner_map[lang][banner.code] = _info

        old_keys = {x for x in Language}
        for k, banner_info in lang_default_banner_map.items():
            old_keys.remove(k)
            cls(k).set(json.dumps(banner_info, cls=JsonEncoder))
        for k in old_keys:
            cls(k).delete()

        old_keys = {x for x in Language}
        for k, code_banner_map in lang_code_banner_map.items():
            old_keys.remove(k)
            _save_data = {k: json.dumps(v, cls=JsonEncoder) for k, v in code_banner_map.items()}
            MarketBannerCodeCache(k).save(_save_data)
        for k in old_keys:
            MarketBannerCodeCache(k).delete()

    @classmethod
    def format_item(cls, banner: MarketingBanner, content: Optional[MarketingBannerContent] = None) -> dict:
        return dict(
            banner_id=banner.id,
            url=banner.url,
            refer_code=banner.refer_code,
            img_src=content.img_src if content else "",
        )


class PageInsetCache(HashCache):
    """页面插画图片缓存, data: {lang: img_url} """

    def __init__(self, page: PageInset.Page):
        super().__init__(f"{page.name}")

    @classmethod
    def reload(cls):
        _now = now()
        model = PageInset
        page_inset_rows: List[PageInset] = model.query.filter(
            model.status == model.Status.VALID,
            model.begin_at <= _now,
            model.end_at > _now,
        ).all()

        content_model = PageInsetContent
        contents: List[PageInsetContent] = content_model.query.filter(
            content_model.owner_id.in_([x.id for x in page_inset_rows]),
            content_model.file_key != ''
        ).all()
        content_map = {(x.owner_id, x.lang): x for x in contents}

        page_lang_img_dict = defaultdict(dict)  # { page_enum: {lang.name: img_url } }
        for img_row in page_inset_rows:
            for lang in Language:
                c = content_map.get((img_row.id, lang))
                if c and (img_src := c.img_src):
                    page_lang_img_dict[img_row.page][lang.name] = img_src

        for page_t in PageInset.Page:
            cache = cls(page_t)
            lang_img_dict = page_lang_img_dict[page_t]
            if lang_img_dict:
                cache.save(lang_img_dict)
            else:
                cache.delete()


class AssetCirculationHistoryCache(HashCache):
    """ 日加密货币总流通市值 { daily_ts : usd } """

    model = AssetCirculationHistory

    def __init__(self):
        super().__init__(None)

    def reload(self):
        one_day = 60 * 60 * 24
        now_ts = current_timestamp(to_int=True)
        today_ts = now_ts - now_ts % one_day
        epoch_ts = today_ts - one_day * 365
        del_keys = []
        if self.hlen() == 0:
            update_at_ts = epoch_ts
        else:
            keys = [int(key) for key in self.hkeys()]
            keys.sort()
            update_at_ts = keys[-1]
            cache_begin_ts = keys[0]
            _ts = cache_begin_ts
            while _ts < epoch_ts:
                del_keys.append(_ts)
                _ts += one_day

        model = self.model
        rows = model.query.with_entities(
            model.time,  # 不考虑币种缺失
            func.sum(model.circulation_usd).label('circulation_usd')
        ).filter(
            model.time >= update_at_ts
        ).group_by(
            model.time
        ).all()

        mapping = {}
        for row in sorted(rows, key=lambda r: r.time):
            daily_ts = row.time - row.time % one_day
            mapping.update({daily_ts: row.circulation_usd})

        ret = {}
        ts = update_at_ts
        while ts <= today_ts:
            circulation_usd = mapping.get(ts)
            if circulation_usd is None:
                if ts == update_at_ts:
                    if self.hlen() == 0:
                        circulation_usd = 0
                    else:
                        circulation_usd = Decimal(self.hget(str(ts)) or '0')
                else:
                    last_ts = ts - one_day
                    circulation_usd = mapping.get(last_ts) or 0
            mapping.update({ts: circulation_usd})
            ret.update({str(ts): amount_to_str(circulation_usd, 2)})
            ts += one_day

        self.hmset(mapping=ret)
        del_keys = {str(x) for x in del_keys}
        if del_keys:
            self.hdel(*del_keys)


class SNSConfCache(StringCache):
    SORTS = {
        Media.EMAIL.value: 1,
        Media.TELEGRAM.value: 2,
        Media.TWITTER.value: 3,
        Media.FACEBOOK.value: 4,
        Media.MEDIUM.value: 5,
        Media.REDDIT.value: 6,
        Media.INSTAGRAM.value: 7,
        Media.YOUTUBE.value: 8,
        Media.DISCORD.value: 9,
        Media.TIKTOK.value: 10,
        Media.LINE.value: 11,
        Media.NAVERBLOG.value: 12,
        Media.VK.value: 13,
        Media.NOTE.value: 14,
    }

    def __init__(self, business: FooterConfig.Business, lang: Language):
        super().__init__(f'{business.name}:{lang.name}')

    @classmethod
    def reload(cls):
        model = FooterConfig
        rows = model.query.with_entities(
            model.business,
            model.media,
            model.display_positions,
            model.lang,
            model.url,
        ).filter(
            model.status == model.Status.VALID
        ).all()
        result = defaultdict(list)
        for row in rows:
            dps = json.loads(row.display_positions) if row.display_positions else []
            result[(row.business, row.lang)].append(dict(
                media=row.media.value,
                display_positions=dps or [],
                url=row.url,
            ))
        old_keys = {(x, y) for x in model.Business for y in Language}
        for k, v in result.items():
            old_keys.remove(k)
            v.sort(key=lambda d: cls.SORTS.get(d['media'], 999))
            cls(*k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            cls(*k).delete()


class TradeRankActivityNoticeCache(HyperLogLogCache):

    def __init__(self, trade_activity_id: int):
        super().__init__(trade_activity_id)


class UserRelationAsyncExportCache(SetCache):
    """ IP/设备账号关联查询 异步导出，存要查询的用户ids """

    TTL = 3600 * 2

    def __init__(self, exporter_id: int):
        super().__init__(str(exporter_id))

    def add_user_ids(self, user_ids: List[int]) -> int:
        val = self.sadd(*[str(i) for i in user_ids])
        self.expire(self.TTL)
        return val


class InsetConfigCache(StringCache):
    """运营-插画缓存"""
    model = NewInsetConfig

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        now_ = now()
        model = cls.model
        rows = model.query.filter(
            model.begin_at <= now_,
            model.end_at >= now_,
            model.status == model.Status.VALID
        ).all()
        result = []
        for row in rows:
            result.append(dict(
                id=row.id,
                begin_at=datetime_to_time(row.begin_at),
                end_at=datetime_to_time(row.end_at),
                url=row.url,
                content_style=row.content_style.name,
            ))
        cls().save(json.dumps(result, cls=JsonEncoder))


class PerpetualActivityCache(HashCache):

    def __init__(self):
        super().__init__(None)

    def reload(self):
        model = PerpetualActivity
        content_model = PerpetualActivityContent
        now_ = now()
        records = model.query.filter(
            model.begin_at <= now_,
            model.end_at >= now_,
            model.status == model.Status.VALID
        ).all()
        activity_mapping = dict()
        activity_ids = []
        jump_ids = set()
        pic_ids = set()
        for record in records:
            id_ = record.id
            activity_ids.append(id_)
            activity_mapping[id_] = record
            pic_ids.update({record.light_pic_id, record.dark_pic_id})
            if record.jump_id:
                jump_ids.add(record.jump_id)

        contents = content_model.query.filter(
            content_model.activate_id.in_(activity_ids)
        ).order_by(content_model.activate_id.desc()).all()
        jump_mapping = self.get_jump_mapping(jump_ids)
        pic_mapping = self.get_pic_mapping(pic_ids)

        res = defaultdict(list)
        for content in contents:
            activate_id = content.activate_id
            activity = activity_mapping[activate_id]
            lang = content.lang.name
            item = {
                'begin_at': datetime_to_time(activity.begin_at),
                'end_at': datetime_to_time(activity.end_at),
                'jump_uri': jump_mapping.get(activity.jump_id),
                'light_pic_url': pic_mapping[activity.light_pic_id],
                'dark_pic_url': pic_mapping[activity.dark_pic_id],
                'title': content.title,
                'sub_title': content.sub_title,
            }
            res[lang].append(item)
        dumped_mapping = {k: json.dumps(v) for k, v in res.items()}
        self.save(dumped_mapping)

    @staticmethod
    def get_jump_mapping(jump_ids):
        jump_records = AppJumpList.query.filter(
            AppJumpList.id.in_(jump_ids)
        ).with_entities(
            AppJumpList.id,
            AppJumpList.jump_data
        ).all()
        return {i.id: i.jump_data for i in jump_records}

    @staticmethod
    def get_pic_mapping(pic_ids):
        pic_records = File.query.filter(
            File.id.in_(pic_ids)
        ).all()
        return {i.id: i.static_url for i in pic_records}

    def get_contents(self, lang: str):
        ret = self.hget(lang)
        if not ret:
            return []
        else:
            return json.loads(ret)


class RiskUserCheckNoticeCache(StringCache):
    def __init__(self):
        super().__init__(None)


class RiskUserPermissionCheckNoticeCache(StringCache):
    """ 风控用户待审核-按权限 告警通知-防干扰 """
    TTL = 3600 * 2

    def __init__(self, permission_key: str):
        super().__init__(permission_key)

    def gen(self, count: int):
        self.set(str(count), ex=self.TTL)


class ReferralActivityBannerCache(StringCache):
    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        model = ReferralActivityBanner
        content_model = ReferralActivityBannerContent
        _now = now()
        records = model.query.filter(
            model.status == model.Status.VALID,
            model.begin_at <= _now,
            model.end_at >= _now
        ).order_by(
            model.sort_id
        ).all()
        trans = content_model.query.all()
        content_dict = defaultdict(dict)
        for tran in trans:
            content_dict[tran.activity_id][tran.lang.value] = {
                "title": tran.title,
                "sub_title": tran.sub_title,
                "copywriting": tran.copywriting,
            }
        jump_dict = {v.id: v for v in AppJumpList.query.all()}
        result = [
            {
                "id": v.id,
                "display_name": v.display_name,
                "begin_at": v.begin_at,
                "end_at": v.end_at,
                "web_jump_type": v.web_jump_type.name if v.web_jump_type else "",
                "app_jump_type": v.app_jump_type.name if v.app_jump_type else "",
                "web_jump_uri": jump_dict[v.web_jump_id].jump_data if v.web_jump_type and v.web_jump_id else "",
                "app_jump_uri": jump_dict[v.app_jump_id].jump_data if v.app_jump_type and v.app_jump_id else "",
                "light_pic_url": v.light_pic_url,
                "dark_pic_url": v.dark_pic_url,
                "sort_id": v.sort_id,
                "trans": content_dict.get(v.id),
            } for v in records
        ]
        result.sort(key=lambda x: x["sort_id"])
        cls().set(json.dumps(result, cls=JsonEncoder))
        return result

    def get_ret_by(self, platform: str):
        records = super().read()
        if not records:
            return []
        records = json.loads(records)
        result = []
        for item in records:
            temp_item = dict(
                id=item['id'],
                display_name=item['display_name'],
                begin_at=item['begin_at'],
                end_at=item['end_at'],
                sort_id=item['sort_id'],
                trans=item['trans'],
                light_pic_url=item['light_pic_url'],
                dark_pic_url=item['dark_pic_url'],
            )
            if platform == 'WEB':
                temp_item.update(
                    jump_type=item['web_jump_type'],
                    jump_uri=item['web_jump_uri'],

                )
            else:
                temp_item.update(
                    jump_type=item['app_jump_type'],
                    jump_uri=item['app_jump_uri'],
                )
            result.append(temp_item)
        return result


class LastPushReadTimeCache(HashCache):
    """用户最后一次打开推送/邮件时间"""

    def __init__(self, push_type: LastPushReadType):
        super().__init__(push_type.name)

    def set_time(self, user_id):
        value = str(int(time.time()))
        self.hset(user_id, value)


class DynamicFallingCache(StringCache):
    """app 动效飘落缓存"""

    model = DynamicFalling

    def __init__(self, platform: DynamicFalling.Platform):
        platform = platform.name
        super(DynamicFallingCache, self).__init__(f"{platform}")

    @classmethod
    def reload(cls):
        _now = now()
        model = cls.model
        rows = model.query.with_entities(
            model.id,
            model.platform,
            model.type,
            model.light_file_id,
            model.dark_file_id,
        ).filter(
            model.status == model.Status.VALID,
            model.begin_at < _now,
            model.end_at > _now,
        ).order_by(
            model.id.asc()
        ).all()

        objs = defaultdict(list)
        file_ids = []
        for row in rows:
            objs[row.platform].append(row)
            if row.light_file_id:
                file_ids.append(row.light_file_id)
            if row.dark_file_id:
                file_ids.append(row.dark_file_id)

        file_map = {}
        for file in File.query.filter(File.id.in_(file_ids)).all():
            file_map[file.id] = file.static_url

        ret = defaultdict(dict)
        for platform in (model.Platform.ANDROID, model.Platform.IOS, model.Platform.WEB):
            _objs = objs[platform]
            if platform in (model.Platform.ANDROID, model.Platform.IOS):
                _objs = chain(_objs, objs[model.Platform.ALL])

            for obj in _objs:
                if obj.type == model.Type.DynamicFalling:
                    ret[platform]["dynamic_falling_light"] = file_map.get(obj.light_file_id, "")
                    ret[platform]["dynamic_falling_dark"] = file_map.get(obj.dark_file_id, "")
                else:
                    ret[platform]["dynamic_logo_light"] = file_map.get(obj.light_file_id, "")
                    ret[platform]["dynamic_logo_dark"] = file_map.get(obj.dark_file_id, "")

        old_keys = {x for x in model.Platform}
        for k, v in ret.items():
            old_keys.remove(k)
            cls(k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            cls(k).delete()


class AccumulatedWithdrawalNoticeCache(StringCache):
    """ 币种累计提现监控-防干扰，最近N时间内有通知时，不再触发 """
    TTL = 3600 * 12

    def __init__(self, asset: str):
        super().__init__(asset)

    def gen(self):
        self.value = '1'
        self.expire(self.TTL)


class UserAccumulatedWithdrawalNoticeCache(StringCache):
    """ 用户币种累计提现监控-防干扰，最近N时间内有触发过，不再触发 """
    TTL = 3600 * 24

    def __init__(self, user_id: int, asset: str):
        super().__init__(f"{user_id}:{asset}")

    def gen(self):
        self.value = '1'
        self.expire(self.TTL)


class UserAccumulatedAssetDepositNoticeCache(StringCache):
    """ 用户币种累计充值监控-防干扰，最近N时间内有触发过，不再触发 """
    TTL = 3600 * 24 * 7

    def __init__(self, user_id: int, asset: str):
        super().__init__(f"{user_id}:{asset}")

    def check_ignore(self, ts: int, ignore_seconds: int) -> bool:
        value = self.read()
        if not value:
            return False
        if ts - int(value) <= ignore_seconds:
            return True
        return False

    def gen(self, ts: int):
        self.value = ts
        self.expire(self.TTL)


class AcademyTagToArticleCache(HashCache):

    def __init__(self):
        super().__init__(None)

    def read_aside(self):
        data = self.read()
        ret = {}
        for k, v in data.items():
            ret[int(k)] = json.loads(v)
        return ret


class AcademyCoinToArticleCache(HashCache):

    def __init__(self):
        super().__init__(None)

    def read_aside(self, asset: str) -> list:
        data = self.hget(asset)
        if not data:
            return []
        return json.loads(data)


class AcademyHomepageCache(StringCache):

    def __init__(self, lang: Language):
        super(AcademyHomepageCache, self).__init__(f"{lang.name}")

    @classmethod
    def read_aside(cls, lang: Language) -> dict:
        cache = cls(lang)
        data = cache.read()
        if data:
            json_data = json.loads(data)
            return json_data
        return {}


class LastAcademyArticlesCache(StringCache):

    def __init__(self, lang: Language):
        super(LastAcademyArticlesCache, self).__init__(f"{lang.name}")

    @classmethod
    def read_aside(cls, lang: Language) -> list:
        cache = cls(lang)
        data = cache.read()
        if data:
            json_data = json.loads(data)
            return json_data
        return []


class AcademyArticleIndexCache(StringCache):
    """
    按语言的文章索引（web）
    - 作为依赖数据，主要是用作：
        - 相关文章推荐
        - web 列表导航栏
        - 获取某语言前 x 条文章
    """

    def __init__(self, lang: Language):
        super(AcademyArticleIndexCache, self).__init__(f"{lang.name}")

    @classmethod
    def read_aside(cls, lang: Language) -> list:
        cache = cls(lang)
        data = cache.read()
        if data:
            json_data = json.loads(data)
            return json_data
        return []


class AcademyNavCache(StringCache):

    def __init__(self, lang: Language):
        super(AcademyNavCache, self).__init__(f"{lang.name}")

    @classmethod
    def read_aside(cls, lang: Language) -> dict:
        cache = cls(lang)
        data = cache.read()
        if data:
            json_data = json.loads(data)
            return json_data
        return {}


class AcademyGlossaryCache(StringCache):

    def __init__(self, lang: Language):
        super(AcademyGlossaryCache, self).__init__(f"{lang.name}")

    @classmethod
    def read_aside(cls, lang: Language) -> dict:
        cache = cls(lang)
        data = cache.read()
        if data:
            json_data = json.loads(data)
            return json_data
        return {}


class P2pActivityBannerCache(HashCache):
    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        _now = now()
        records = P2pActivity.query.filter(
            P2pActivity.status == P2pActivity.Status.ONLINE,
            P2pActivity.start_at <= _now,
            P2pActivity.end_at >= _now
        ).order_by(
            P2pActivity.sort_id
        ).all()
        activity_ids = {i.id for i in records}
        banners = P2pActivityBanner.query.filter(
            P2pActivityBanner.owner_id.in_(activity_ids)
        ).all()
        banner_dict = defaultdict(dict)
        for banner in banners:
            banner_dict[banner.owner_id][banner.lang.value] = {
                "daylight_file_url": AWSBucketPublic.get_file_url(banner.daylight_file_key),
                "night_file_url": AWSBucketPublic.get_file_url(banner.night_file_key),
            }
        jump_ids = {i.jump_id for i in records}
        jump_dict = {
            v.id: v for v in AppJumpList.query.filter(
                AppJumpList.id.in_(jump_ids)
            ).all()
        }
        lang_banner_dict = defaultdict(list)
        for activity in records:
            activity_banner = banner_dict[activity.id]
            for lang, banner_data in activity_banner.items():
                lang_banner_dict[lang].append({
                    "id": activity.id,
                    "sort_id": activity.sort_id,
                    "content_type": activity.content_type.name,
                    "display_to_merchant": activity.display_to_merchant,
                    "platform": activity.platform.name,
                    "fiats": activity.fiats.split(',') if activity.fiats else [],
                    "jump_url": jump_dict[activity.jump_id].jump_data if activity.jump_id else "",
                    "daylight_file_url": banner_data['daylight_file_url'],
                    "night_file_url": banner_data['daylight_file_url']
                })
        cache = cls()
        current_lang = cache.hkeys()
        update_lang = lang_banner_dict.keys()
        delete_lang = set(current_lang) - set(update_lang)
        for lang, data in lang_banner_dict.items():
            cache.hset(lang, json.dumps(data, cls=JsonEncoder))
        if delete_lang:
            cache.hdel(*list(delete_lang))

    def get_lang_banner(self, lang: str):
        lang_data = self.hget(lang)
        if lang != Language.DEFAULT.value and not lang_data:
            lang_data = self.hget(Language.DEFAULT.value)
        if not lang_data:
            return []
        return json.loads(lang_data)


class CBoxPromotionCache(StringCache):
    model = CBoxPromotion

    ttl = 60 * 60 * 24

    def __init__(self, platform: CBoxPromotion.Platform, lang: Language):
        platform = platform.name
        lang = lang.name
        super().__init__(f"{platform}:{lang}")

    @classmethod
    def reload(cls):
        _now = now()
        cls.expire(_now)
        cls._reload_cache(_now)

    @classmethod
    def expire(cls, _now):
        model = cls.model
        model.query.filter(
            model.status != model.Status.DELETED,
            model.end_at < _now,
        ).update({model.status: model.Status.OFFLINE})

        db.session.commit()

    @classmethod
    def _reload_cache(cls, _now):
        model = cls.model
        rows = model.query.filter(
            model.status == model.Status.ONLINE,
            model.begin_at < _now,
            model.end_at > _now,
        ).order_by(model.id.desc()).all()
        promotion_ids = [i.id for i in rows]
        contents = CBoxPromotionContent.query.filter(CBoxPromotionContent.promotion_id.in_(promotion_ids)).all()
        contents_mapping = {(content.promotion_id, content.lang): content for content in contents}
        result = defaultdict(list)
        for row in rows:
            platforms = cls.fmt_platform(row.platform)
            for lang in Language:
                if not (c := contents_mapping.get((row.id, lang))):
                    continue
                for platform in platforms:
                    result[(platform, lang)].append(dict(
                        id=row.id,
                        title=c.title,
                        content=c.content,
                    ))
        old_keys = {(platform, lang) for platform in model.Platform for lang in Language}
        for k, v in result.items():
            v.sort(key=lambda item: item['id'], reverse=True)
            old_keys.remove(k)
            cls(*k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            cls(*k).delete()

    @classmethod
    def fmt_platform(cls, platform):
        j_platform = json.loads(platform)
        return [getattr(cls.model.Platform, i) for i in j_platform]


class CBoxThemeCache(StringCache):
    model = CBoxThemeCategory
    NOW_VERSION = 2

    def __init__(self, lang: Language):
        super().__init__(lang.name)

    @classmethod
    def reload(cls):
        _now = now()
        cls.expire(_now)
        cls._reload_cache(_now)

    @classmethod
    def expire(cls, _now):
        model = cls.model
        model.query.filter(
            model.status != model.Status.DELETED,
            model.end_at < _now,
        ).update({model.status: model.Status.OFFLINE})
        db.session.commit()

    def read_themes(self, version):
        data = self.read()
        if not data:
            return []
        items = [i for i in json.loads(data) if i['version'] == version]
        return items

    @classmethod
    def _reload_cache(cls, _now):
        model = cls.model
        rows = model.query.filter(
            model.status == model.Status.ONLINE,
            model.begin_at < _now,
            model.end_at > _now,
        ).order_by(model.rank.desc()).all()
        theme_category_ids = [i.id for i in rows]
        contents = CBoxTheme.query.filter(
            CBoxTheme.category_id.in_(theme_category_ids),
            CBoxTheme.status == CBoxTheme.Status.VALID,
        ).all()
        contents_mapping = {(content.category_id, content.lang): content for content in contents}
        result = defaultdict(list)
        for row in rows:
            for lang in Language:
                if not (c := contents_mapping.get((row.id, lang))):
                    continue
                cover_img_src = CBoxTheme.img_src(c.cover_file_key)
                thumbnail_img_src = CBoxTheme.img_src(c.thumbnail_file_key)
                receive_img_src = CBoxTheme.img_src(c.receive_file_key)
                result[lang].append({
                    'theme_id': c.id,
                    'cover_img_src': cover_img_src,
                    'thumbnail_img_src': thumbnail_img_src,
                    'receive_img_src': receive_img_src,
                    'style': c.style.name,
                    'version': row.version,
                })
        old_keys = {lang for lang in Language}
        for k, v in result.items():
            old_keys.remove(k)
            cls(k).set(json.dumps(v, cls=JsonEncoder))
        for k in old_keys:
            cls(k).delete()


class AssetAbnormalIssuanceCache(HashCache):
    """
    钱包侧币种异常增发数据缓存
    {
        "his_amount": "100", # 最近 24h 最小值，触发告警时会更新此值
        "his_amounts": [(ts, "100"), (ts2, "200")],  # 最近 24h 记录的发行量
    }
    """
    TTL = 3600 * 24  # 钱包侧：如果币种24h 新增数量都未变化，则会移除

    def __init__(self, asset: str, chain_: str):
        super().__init__(f'{asset}:{chain_}')

    def update(self, cur_amount: Decimal):
        """告警前需记录每次请求的发行量数据，并更新一次近 24h 最小历史发行量"""
        current_ts = current_timestamp(to_int=True)
        value = self.read()
        his_amounts = value.get('his_amounts')
        if not his_amounts:
            his_amounts = []
        else:
            his_amounts = json.loads(his_amounts)

        item = [current_ts, cur_amount]
        his_amounts.append(item)
        outdated_ts = current_ts - self.TTL
        his_amounts = [t for t in his_amounts if int(t[0]) > outdated_ts]
        his_amount = min([Decimal(t[1]) for t in his_amounts])
        data = {
            'his_amount': amount_to_str(his_amount),
            'his_amounts': json.dumps(his_amounts, cls=JsonEncoder),
        }
        self.hmset(data)
        self.expire(self.TTL)

    def remove_his_amount(self, cur_amount: Decimal):
        """告警后，需更新一次最近 24h 最小值"""
        value = self.read()
        his_amounts = value.get('his_amounts')
        if not his_amounts:
            his_amounts = []
        else:
            his_amounts = json.loads(his_amounts)
        # 删除小于此次告警时发行量的历史值
        his_amounts = [t for t in his_amounts if Decimal(t[1]) >= cur_amount]
        data = {
            'his_amount': amount_to_str(cur_amount),
            'his_amounts': json.dumps(his_amounts, cls=JsonEncoder),
        }
        self.hmset(data)

    def read_his_amount(self) -> Decimal:
        value = self.read()
        if not value:
            return Decimal()
        return Decimal(value['his_amount'])

