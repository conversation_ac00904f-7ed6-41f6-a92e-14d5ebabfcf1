#!/usr/bin/python
# -*- coding: utf-8 -*-

from .base import Hash<PERSON><PERSON>, StringCache


class UserAccountPLDataCache(HashCache):

    """用户账户盈亏数据分析缓存"""
    def __init__(self, user_id: int, account_type: str):
        super().__init__(f"{user_id}:{account_type}")


class UserAccountDealCache(StringCache):

    """用户账户交易数据缓存"""
    def __init__(self, user_id: int, account_type: str):
        super().__init__(f"{user_id}:{account_type}")


class BalanceHistoryLastIdCache(HashCache):

    def __init__(self, type_: str, db_index: int):
        super().__init__(f"{type_}:{db_index}")


class BalanceHistorySyncCache(HashCache):

    def __init__(self, type_: str, user_id: int, account_type: str):

        # type_: spot/perpetual
        super().__init__(f"{type_}:{user_id}:{account_type}")
