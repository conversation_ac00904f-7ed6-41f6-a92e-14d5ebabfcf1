# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from sqlalchemy import func

from app.utils import now, amount_to_str, quantize_amount, today
from app.models.activity import Coupon, CouponDailyBalanceHistory
from .base import HashCache


class Investment7DaysEARCache(HashCache):
    """七日年化"""
    def __init__(self):
        super().__init__(None)

    def reload(self):
        data = self.get_data()
        if data:
            self.hmset({asset: amount_to_str(value, 6)
                        for asset, value in data.items()})

    @classmethod
    def get_data(cls):
        from app.models.daily import DailyInvestmentReport
        start = now() - timedelta(days=8)
        query = DailyInvestmentReport.query.filter(
            DailyInvestmentReport.report_date > start,
        )
        result = defaultdict(lambda:
                             defaultdict(lambda:
                                         dict(
                                             day_rate=Decimal(),
                                             interest_amount=Decimal()
                                              )
                                         ))
        for record in query:
            result[record.asset][record.report_date]["day_rate"] = record.day_rate
            result[record.asset][record.report_date]["interest_amount"] = record.investment_interest_amount
        final_result = dict()
        for asset, _d in result.items():
            total_interest_amount = Decimal()
            total_amount = Decimal()
            for _, _d1 in _d.items():
                day_rate = _d1["day_rate"]
                total_interest_amount += _d1["interest_amount"]
                if day_rate > Decimal():
                    total_amount += _d1["interest_amount"] / _d1["day_rate"]
            if total_amount > Decimal():
                final_result[asset] = quantize_amount(total_interest_amount * 365 / total_amount, 6)
            else:
                final_result[asset] = Decimal()

        return final_result

    def read_data(self):
        result = self.hgetall()
        return {asset: Decimal(rate) for asset, rate in result.items()}

    def get_rate(self, asset: str, default: Decimal = None) -> Decimal | None:
        if not (rate := self.hget(asset)):
            return default
        return Decimal(rate)


class InvestmentYesterdayARRCache(HashCache):
    """昨日年化"""
    def __init__(self):
        super().__init__(None)

    def reload(self):
        data = self.get_data()
        self.save(data)

    @classmethod
    def get_data(cls):
        from app.models.daily import DailyInvestmentReport
        yesterday = now().date() - timedelta(days=1)
        q = DailyInvestmentReport.query.filter(
            DailyInvestmentReport.report_date == yesterday
        ).all()
        return {v.asset: amount_to_str(v.day_rate * 365, 6) for v in q}

    def read_data(self):
        result = self.hgetall()
        return {asset: Decimal(rate) for asset, rate in result.items()}


class InvestmentDayRateCache(HashCache):
    def __init__(self):
        super().__init__(None)

    @classmethod
    def get_data(cls):
        from app.business.investment import InvestmentInterestOperation
        tool = InvestmentInterestOperation()
        result = tool.get_current_day_rates()
        return result

    def reload(self):
        data = self.get_data()
        self.hmset({asset: amount_to_str(value, 6) for asset, value in data.items()})

    def read_data(self):
        return self.read()


class InvestmentIncTotalIncomeCache(HashCache):
    """ 用户理财加息-总收益 """
    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_user_income(self) -> dict:
        data = self.hgetall()
        return {k: Decimal(v) for k, v in data.items()}

    @classmethod
    def reload(cls):
        rows = CouponDailyBalanceHistory.query.filter(
            CouponDailyBalanceHistory.coupon_type == Coupon.CouponType.INVESTMENT_INCREASE_RATE,
        ).group_by(
            CouponDailyBalanceHistory.user_id,
            CouponDailyBalanceHistory.asset,
        ).with_entities(
            CouponDailyBalanceHistory.user_id,
            CouponDailyBalanceHistory.asset,
            func.sum(CouponDailyBalanceHistory.amount).label('total_amount'),
        ).all()

        user_asset_income_map = defaultdict(lambda: defaultdict(Decimal))
        for r in rows:
            user_asset_income_map[r.user_id][r.asset] = r.total_amount

        for user_id, asset_income_map in user_asset_income_map.items():
            _cache = cls(user_id)
            _data = {str(k): amount_to_str(v) for k, v in asset_income_map.items()}
            _cache.save(_data)


class InvestmentIncYesterdayIncomeCache(HashCache):

    ttl = 86400

    """ 用户理财加息-昨日收益 """
    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_user_income(self) -> dict:
        data = self.hgetall()
        return {k: Decimal(v) for k, v in data.items()}

    @classmethod
    def reload(cls, inc_date: date = None):
        if not inc_date:
            inc_date = today()

        rows = CouponDailyBalanceHistory.query.filter(
            CouponDailyBalanceHistory.date == inc_date,
            CouponDailyBalanceHistory.coupon_type == Coupon.CouponType.INVESTMENT_INCREASE_RATE,
        ).group_by(
            CouponDailyBalanceHistory.user_id,
            CouponDailyBalanceHistory.asset,
        ).with_entities(
            CouponDailyBalanceHistory.user_id,
            CouponDailyBalanceHistory.asset,
            func.sum(CouponDailyBalanceHistory.amount).label('total_amount'),
        ).all()

        user_asset_income_map = defaultdict(lambda: defaultdict(Decimal))
        for r in rows:
            user_asset_income_map[r.user_id][r.asset] = r.total_amount

        for user_id, asset_income_map in user_asset_income_map.items():
            _cache = cls(user_id)
            _data = {str(k): amount_to_str(v) for k, v in asset_income_map.items()}
            _cache.save(_data)
            _cache.expire(cls.ttl)  # 一天过期，避免昨日加息收益一直存在
