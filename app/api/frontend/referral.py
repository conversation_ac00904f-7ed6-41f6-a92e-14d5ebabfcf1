# -*- coding: utf-8 -*-
import datetime
from enum import Enum
import json
import random
from decimal import Decimal
from datetime import timedel<PERSON>, date
from collections import defaultdict
from functools import partial
import string
from typing import List, Set

from flask import g, send_file
from sqlalchemy import func
from webargs import fields as wa_fields
from flask_restx import fields as fx_fields
from flask_babel import gettext as _, force_locale
from app.api.common.decorators import limit_ip_frequency
from app.exceptions.user import Duplicate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BindR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UserNotAmbassador

from app.models.referral import (AmbassadorPackageBatch, AmbassadorType, PackageSettlementHistory,
                                 ReferralCode, DailyUserReferralSlice, UserAmbassadorPackage)
from app.models.user import OperationLog
from app.utils.format import safe_div

from ... import config
from ...business import ReferralBusiness, UserPreferences, cached, mem_cached
from ...business.coupon import CouponTool
from ...business.referral import AmbassadorBusiness, AmbassadorAgentBusiness, ReferralRepository, ViaBtcPoolAmbBusiness
from ...business.ambassador import AmbDashboardHelper
from ...business.bus_referral import BusRelationUserQuerier
from ...business.lock import CacheLock, LockKeys
from ...business.email import send_ambassador_application_to_agent
from ...caches import AmbassadorRankCache, ReferralRankCache
from ...caches.operation import ReferralActivityBannerCache
from ...caches.user import NormalReferralRewardsCache
from ...common import Language, ReportType, PrecisionEnum, ReferralType, CAN_BIND_REFERRER_DAYS
from ...exceptions import ReferralCodeDoesNotExist, AlreadyBindReferrer, BindReferrerTimeout
from ...models import (
    Ambassador,
    AmbassadorAgentAssetDetail,
    AmbassadorAgentHistory,
    AmbassadorApplication,
    AmbassadorStar,
    Referral,
    ReferralAssetHistory,
    ReferralHistory,
    ReferralPicture,
    ReferralAssetSummary,
    ReferralCodeAssetDetail,
    MonthlyReferralCodeAssetDetail,
    ReferralCopyWriting,
    ReferralCopyWritingTranslation,
    User,
    db,
    AmbassadorGuide,
    AmbassadorGuideTranslation,
    AmbassadorStatistics,
    DailyAmbassadorAgentAssetReport, SignOffUser, Deposit, UserTradeSummary, UserExchangeSummary,
    DailyUserReferralDetailReport, DailyAmbassadorReferralDetailReport, DailyBusinessAmbassadorReferralReport,
    UserExtra, AmbassadorReferralHistoryExtra, AmbassadorDashboard,
    AmbassadorDashboardDailyRefer, AmbassadorDashboardMonthlyRefer,
    AmbassadorDashboardDailyReferDetail, AmbassadorDashboardMonthlyReferDetail, AmbassadorDashboardTradeMarket
)
from ...models.activity import AmbassadorActivity, AmbassadorActivityContent, CouponPool, UserCoupon, CouponApplyDraft
from ...models.monthly import MonthlyAmbassadorReport, MonthlyAmbassadorAgentReport, MonthlyAmbassadorAgentAssetReport, \
    MonthlyUserReferralDetailReport, MonthlyAmbassadorReferralDetailReport, MonthlyBusinessAmbassadorReferralReport
from ...utils import query_to_page, today, amount_to_str, datetime_to_str, \
    timestamp_to_datetime, ExcelExporter, batch_iter, export_xlsx, AWSBucketPrivate
from ...utils.date_ import date_to_datetime, now, datetime_to_time, timestamp_to_date, this_month

from ..common import Namespace, Resource, require_login, respond_with_code, get_request_platform, get_request_language, \
    response_replace_host_url
from ..common.fields import (
    AmountField,
    EnumField,
    EnumMarshalField,
    FileField,
    LimitField,
    PageField,
    TimestampMarshalField,
    PositiveDecimalField,
    BoolField,
    TimestampField,
)
from ...exceptions import InvalidArgument, ReferralCodeInvalid
from app.schedules.referral import audit_viabtc_pool_ambassador_application_task
from app.schedules.ambassador import update_amb_apl_last_invalid_reason_schedule


ns = Namespace('Referral')


@ns.route("/config")
@respond_with_code
class ReferralConfigResource(Resource):

    @classmethod
    def get(cls):
        """ 推荐比例的配置信息 """
        result = {
            "vip_level_rate": [{"level": k, "rate": v} for k, v in ReferralBusiness.VIP_LEVEL_RATE_MAP.items()],
            "ambassador_level_rate": [
                {"level": k.name, "rate": v} for k, v in ReferralBusiness.AMBASSADOR_LEVEL_RATE_MAP.items()
            ],
        }
        return result


@ns.route('/user-rewards')
@respond_with_code
class UserReferralRewardsResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            type=EnumField(ReferralType, missing=ReferralType.REFERRAL),
        )
    )
    def get(cls, **kwargs):
        if kwargs['type'] is ReferralType.AMBASSADOR:
            return []
        data = NormalReferralRewardsCache().read()
        if not data:
            return []
        ret = json.loads(data)
        random.shuffle(ret)
        return ret


@ns.route('/basic')
@respond_with_code
class ReferralBasicResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """
        获取用户邀请返佣的基础信息
        其他接口太重，获取的信息过多
        """
        refer_code = ReferralBusiness.get_referral_by_user(g.user.id)
        return dict(
            referral_code=refer_code.code
        )


@ns.route('/info')
@respond_with_code
class ReferralInfoResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            type=EnumField(ReferralType),
        )
    )
    def get(cls, **kwargs):
        """推荐信息"""
        user_id = g.user.id
        query = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.user_id == user_id,
        )
        type_ = kwargs.get('type')

        referree_ids = ReferralHistoryResource.get_referree_ids(type_, user_id)
        amb_referral_rates = vip_referral_rates = None
        if type_:
            if type_ is ReferralType.REFERRAL:
                query = query.filter(
                    ReferralAssetHistory.type == ReferralAssetHistory.Type.REFERRAL
                )
                vip_referral_rates = ReferralBusiness.get_vip_referral_rate(user_id)
                is_amb = AmbassadorBusiness.get_ambassador(user_id, status=None)
                referral_rates = vip_referral_rates
                if is_amb:
                    amb_referral_rates = ReferralBusiness.get_ambassador_referral_rate(user_id, status=None)
            else:
                query = query.filter(
                    ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR
                )
                is_amb = ReferralBusiness.is_valid_ambassador(user_id)
                if is_amb:
                    referral_rates = ReferralBusiness.get_ambassador_referral_rate(user_id)
                else:
                    referral_rates = ReferralBusiness.get_vip_referral_rate(user_id)
        else:
            query = query.filter(
                ReferralAssetHistory.type.in_((
                    ReferralAssetHistory.Type.REFERRAL,
                    ReferralAssetHistory.Type.AMBASSADOR
                ))
            )
            referral_rates = ReferralBusiness.get_referral_rate(user_id)
        _today = today()
        _yesterday = _today - timedelta(days=1)
        _yesterday_rows = query.group_by(
            ReferralAssetHistory.asset
        ).with_entities(
            ReferralAssetHistory.asset,
            func.sum(ReferralAssetHistory.amount),
        ).filter(
            ReferralAssetHistory.date == _yesterday
        ).all()
        referral_count = len(referree_ids)
        deal_user_ids = cls.get_deal_user_ids(user_id, referree_ids, type_)
        deal_count = len(deal_user_ids)
        rows = query.group_by(
            ReferralAssetHistory.asset
        ).with_entities(
            ReferralAssetHistory.asset,
            func.sum(ReferralAssetHistory.amount),
            func.sum(ReferralAssetHistory.spot_amount),
            func.sum(ReferralAssetHistory.perpetual_amount),
        ).all()

        refer_code = ReferralBusiness.get_referral_by_user(user_id)

        return dict(
            referral_code=refer_code.code,
            share_rate=refer_code.rate,
            referral_code_url="{}/register?refer_code={}".format(config['SITE_URL'], refer_code.code),
            referral_code_remark=refer_code.remark,
            referral_count=referral_count,
            deal_count=deal_count,
            deal_amount=cls.get_referral_trade_amount(deal_user_ids),
            referral_rate=max(referral_rates.values()),
            is_special_rate=ReferralBusiness.is_special_rate(user_id),
            ambassador_referral_rate=max(amb_referral_rates.values()) if amb_referral_rates else None,
            normal_referral_rate=max(vip_referral_rates.values()) if vip_referral_rates else None,
            referral_rewards=[dict(
                asset=asset,
                amount=amount,
                spot_amount=spot_amount or Decimal(),
                perpetual_amount=perp_amount or Decimal(),
            ) for asset, amount, spot_amount, perp_amount in rows],
            yesterday_referral_rewards=[dict(
                asset=asset,
                amount=amount,
            ) for asset, amount in _yesterday_rows],
        )

    @classmethod
    def get_deal_user_ids(cls, user_id: int, referree_user_ids: Set[int], type_: ReferralType = None) -> Set[int]:
        # 返回交易用户ids
        @cached(60 * 5)
        def _get_deal_user_ids(user_id_, type_):
            model = ReferralAssetSummary
            query = model.query.with_entities(
                model.referree_id
            ).filter(
                model.user_id == user_id_,
            )
            if type_ is ReferralType.REFERRAL:
                query = query.filter(model.type == ReferralAssetHistory.Type.REFERRAL)
            elif type_ is ReferralType.AMBASSADOR:
                query = query.filter(model.type == ReferralAssetHistory.Type.AMBASSADOR)
            return {row.referree_id for row in query.all()}
        return set(_get_deal_user_ids(user_id, type_)) & referree_user_ids

    @classmethod
    def get_referral_trade_amount(cls, referree_ids: list | set) -> Decimal:
        ret = Decimal()
        for chunk_ids in batch_iter(referree_ids, 1000):
            user_trades = ReferralHistoryResource.get_user_trades(chunk_ids)
            for value in user_trades.values():
                ret += value['spot'] + value['perpetual']
        return ret


def _validate_referral_code(code: str):
    allowed_chars = string.ascii_letters + string.digits
    return 3 <= len(code) <= 9 and all([c in allowed_chars for c in code])


@ns.route("/code")
@respond_with_code
class ReferralCodeInfoResource(Resource):

    @classmethod
    @limit_ip_frequency(15, 600)
    @ns.use_kwargs(
        dict(
            referral_code=wa_fields.String(required=True)
        )
    )
    def get(cls, **kwargs):
        if not Referral.query.filter(Referral.code == kwargs['referral_code']).first():
            raise ReferralCodeDoesNotExist


@ns.route("/codes")
@respond_with_code
class ReferralCodesResource(Resource):

    MAX_CODE_NUM = 20

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            type=EnumField(ReferralType),
        )
    )
    def get(cls, **kwargs):
        """ 推荐链接列表|推荐邀请码列表 """
        user_id = g.user.id
        type_ = kwargs.get('type')
        referree_ids = ReferralHistoryResource.get_referree_ids(type_, user_id)
        if type_:
            if type_ is ReferralType.REFERRAL:
                referral_rates = ReferralBusiness.get_vip_referral_rate(user_id)
            else:
                is_amb = ReferralBusiness.is_valid_ambassador(user_id)
                if is_amb:
                    referral_rates = ReferralBusiness.get_ambassador_referral_rate(user_id)
                else:
                    referral_rates = ReferralBusiness.get_vip_referral_rate(user_id)
        else:
            referral_rates = ReferralBusiness.get_referral_rate(user_id)
        referral_rate = max(referral_rates.values())
        referral_codes = Referral.query.filter(Referral.user_id == user_id).order_by(Referral.id.desc()).all()

        referral_count_dict = defaultdict(int)
        deal_count_dict = defaultdict(int)
        referral_rows = (
            ReferralHistory.query.filter(
                ReferralHistory.referrer_id == user_id,
            )
            .with_entities(
                ReferralHistory.referral_id,
                ReferralHistory.referree_id,
            )
            .all()
        )
        deal_user_ids = ReferralInfoResource.get_deal_user_ids(user_id, referree_ids, type_)
        for referral_id, referree_id in referral_rows:
            if referree_id not in referree_ids:
                continue  # 内存过滤
            referral_count_dict[referral_id] += 1
            if referree_id in deal_user_ids:
                deal_count_dict[referral_id] += 1

        codes = [
            dict(
                referral_code=i.code,
                is_default=i.is_default,
                share_rate=i.rate,
                referral_code_url="{}/register?refer_code={}".format(config['SITE_URL'], i.code),
                referral_code_remark=i.remark,
                referral_count=referral_count_dict.get(i.id, 0),
                deal_count=deal_count_dict.get(i.id, 0),
                referral_rate=referral_rate,
            )
            for i in referral_codes
        ]
        return {
            "max_count": cls.MAX_CODE_NUM,
            "total_count": len(codes),
            "codes": codes,
        }

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            remark=wa_fields.String(missing="", validate=lambda x: len(x) <= 256),
            rate=PositiveDecimalField(
                required=True, allow_zero=True, validate=lambda x: x % Decimal("0.01") == 0 and Decimal('0.5') <= x <= 1
            ),  # 返还给自己的比例，2位精度
            code=wa_fields.String(validate=_validate_referral_code),
            is_default=BoolField(missing=False),
        )
    )
    def post(cls, **kwargs):
        """ 生成推荐链接 """
        user_id = g.user.id
        is_default = kwargs["is_default"]
        with CacheLock(LockKeys.new_user_referral(), wait=True):
            db.session.rollback()
            code_count = Referral.query.filter(Referral.user_id == user_id).with_entities(func.count("*")).scalar() or 0
            if code_count >= cls.MAX_CODE_NUM:
                raise InvalidArgument

            if is_default:
                Referral.query.filter(Referral.user_id == user_id).update(
                    {Referral.is_default: False}, synchronize_session=False
                )
            if code:= kwargs.get('code'):
                if not AmbassadorBusiness.get_ambassador(user_id) \
                    and not BusRelationUserQuerier.get_bus_ambassador(user_id, need_valid=True):
                    raise InvalidArgument
                code = code.lower()
                if ReferralCode.query.filter(ReferralCode.code == code).first():
                    raise DuplicateReferralCode
                db.session.add(ReferralCode(
                    code=code,
                    status=ReferralCode.Status.DELETED
                ))
            row = db.session_add_and_commit(
                Referral(
                    user_id=user_id,
                    code=code or ReferralBusiness.new_refer_code(),
                    rate=kwargs["rate"],
                    remark=kwargs["remark"],
                    is_default=is_default,
                )
            )
            return {"code": row.code}

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            code=wa_fields.String(required=True),
            remark=wa_fields.String,
            rate=PositiveDecimalField(allow_zero=True, validate=lambda x: x % Decimal("0.01") == 0 and Decimal("0.01") <= x <= 1),
            is_default=BoolField,
        )
    )
    def put(cls, **kwargs):
        """ 编辑推荐链接｜将推荐链接设为默认 """
        user_id = g.user.id
        code = kwargs["code"]
        referral_code = Referral.query.filter(Referral.user_id == user_id, Referral.code == code).first()
        if not referral_code:
            raise InvalidArgument

        if (remark := kwargs.get("remark")) is not None:
            referral_code.remark = remark
        if (rate := kwargs.get("rate")) is not None:
            referral_code.rate = rate
        if (is_default := kwargs.get("is_default")) is not None:
            if is_default:
                # 当前code设为默认，取消以前的默认code
                old_default_code = Referral.query.filter(
                    Referral.user_id == user_id, Referral.code != code, Referral.is_default.is_(True)
                ).all()
                for i in old_default_code:
                    i.is_default = False
            else:
                # 当前code取消默认，设置第一个code为默认
                if referral_code.is_default:
                    first_code = Referral.query.filter(Referral.user_id == user_id).order_by(Referral.id.asc()).first()
                    first_code.is_default = True
            referral_code.is_default = is_default
        db.session.commit()


@ns.route("/copywritings")
@respond_with_code
class ReferralCopyWritingsResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 推荐文案列表 """
        query = (
            ReferralCopyWriting.query.filter(ReferralCopyWriting.status == ReferralCopyWriting.Status.VALID)
            .order_by(ReferralCopyWriting.rank.asc())
            .all()
        )
        tran_query = ReferralCopyWritingTranslation.query.filter(
            ReferralCopyWritingTranslation.status == ReferralCopyWritingTranslation.Status.VALID
        ).all()

        tran_dict = defaultdict(dict)  # { copy_writing_id: {language.value: content } }
        for t in tran_query:
            tran_dict[t.copy_writing_id][t.lang.value] = t.content

        def _get_content(_copy_writing_id):
            _tans = tran_dict[_copy_writing_id]
            _content = _tans.get(g.lang) or _tans.get(Language.EN_US.value) or ""
            return _content

        copy_writings = [{"id": v.id, "rank": v.rank, "content": _get_content(v.id)} for v in query]
        copy_writings.sort(key=lambda x: x["rank"])
        return copy_writings


@ns.route("/series")
@respond_with_code
class ReferralSeriesResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            code=wa_fields.String,  # 邀请码
            points=wa_fields.Integer(required=True, validate=lambda x: 0 < x <= 180),  # 点数｜最近n天数据
            type=EnumField(ReferralType, required=False),
        )
    )
    def get(cls, **kwargs):
        """ 推广数据分析-返佣曲线(用户每日返佣数据表格) """
        to_day_ = today()
        user_id = g.user.id
        start_date = to_day_ - timedelta(days=kwargs["points"])
        referral_id = None  # 默认查汇总数据
        if code := kwargs.get("code"):
            referral_code = Referral.query.filter(Referral.user_id == user_id, Referral.code == code).first()
            if not referral_code:
                raise InvalidArgument
            referral_id = referral_code.id

        model = DailyUserReferralSlice
        q = (
            model.query.filter(
                model.date >= start_date,
                model.user_id == user_id,
                model.referral_id == referral_id,
            )
            .order_by(model.date.asc())
            .all()
        )
        # [时间 推广人数 返佣金额USD]
        return [[i.date, i.referral_count, i.amount_usd] for i in q]


@ns.route('/history')
@respond_with_code
class ReferralHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
            code=wa_fields.String,
            search_keyword=wa_fields.String,
            type=EnumField(ReferralType),
            start_date=TimestampField(),
            end_date=TimestampField(),
            has_trade=wa_fields.Boolean,
            is_valid=wa_fields.Boolean,
            is_coupon_gift=wa_fields.Boolean(missing=False),
            export=wa_fields.Integer(missing=0)
        )
    )
    def get(cls, **kwargs):
        """ 推荐历史|推荐记录 """
        user_id = g.user.id
        page, limit, export = kwargs['page'], kwargs['limit'], kwargs['export']
        type_ = kwargs.get('type')
        referree_ids = cls.get_referree_ids(type_, user_id)
        cg_info = {}
        is_coupon_gift = kwargs.get('is_coupon_gift')
        if is_coupon_gift:
            cg_info = cls.get_coupon_gift_referree_info(user_id, referree_ids)
            referree_ids = set(cg_info.keys())
        query = ReferralHistory.query.filter(
            ReferralHistory.referrer_id == user_id,
            ReferralHistory.referree_id.in_(referree_ids)
        ).order_by(ReferralHistory.id.desc())
        if search_keyword := kwargs.get("search_keyword"):
            referee_id = User.full_match_user(search_keyword)
            query = query.filter(
                ReferralHistory.referree_id == referee_id
            )
        if code := kwargs.get("code"):
            referral_code = Referral.query.filter(Referral.user_id == user_id, Referral.code == code).first()
            if not referral_code:
                raise InvalidArgument("invalid code")
            query = query.filter(ReferralHistory.referral_id == referral_code.id)
        # 邀请用户注册时间筛选
        if start_date := kwargs.get("start_date"):
            query = query.filter(ReferralHistory.created_at >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(ReferralHistory.created_at <= end_date)
        if (has_trade := kwargs.get("has_trade")) is not None:
            filter_user_ids = cls.get_filter_user_ids(user_id, referree_ids, has_trade, type_)
            query = query.filter(ReferralHistory.referree_id.in_(filter_user_ids))
        if (is_valid := kwargs.get("is_valid")) is not None:
            if is_valid:
                status = ReferralHistory.Status.VALID
            else:
                status = ReferralHistory.Status.EXPIRED
            query = query.filter(ReferralHistory.status == status)

        pagination = query.paginate(
            page if not export else 1,
            limit if not export else config['EXPORT_ITEM_MAX_COUNT'],
            error_out=False)
        referree_ids = [i.referree_id for i in pagination.items]
        sign_off_users = []
        summary_rows = []
        user_deposits = set()
        user_trades = {}
        for chunk_referree_ids in batch_iter(referree_ids, 1000):
            sign_off_users.extend(cls._get_sign_off_users(referree_ids=chunk_referree_ids))
            summary_rows.extend(cls._get_summary_rows(user_id, chunk_referree_ids))
            user_deposits |= cls._get_deposited_user_ids(referree_ids=chunk_referree_ids)
            user_trades.update(cls.get_user_trades(referree_ids=chunk_referree_ids))
        sign_off_mapping = {row.user_id: row for row in sign_off_users}
        summary_info_map = defaultdict(list)
        for i in summary_rows:
            summary_info_map[i.referree_id].append(i)

        amb = Ambassador.query.filter(
            Ambassador.user_id == user_id,
            Ambassador.status == Ambassador.Status.VALID,
        ).with_entities(Ambassador.effected_at).first()
        if amb:
            amb_effected_at = amb.effected_at
        else:
            bus_amb = BusRelationUserQuerier.get_bus_ambassador(user_id, need_valid=True)
            amb_effected_at = bus_amb.effected_at if bus_amb else None

        flat_items = []
        is_amb = ReferralBusiness.is_valid_ambassador(user_id)
        for i in pagination.items:
            status = i.status.name
            asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.REFERRAL]
            if not type_:
                if amb_effected_at and amb_effected_at < i.effected_at:
                    # 推荐类型
                    referral_type_ = "AMBASSADOR"
                else:
                    referral_type_ = "REFERRAL"
            else:
                if type_ is ReferralType.REFERRAL:
                    referral_type_ = "REFERRAL"
                else:
                    referral_type_ = "AMBASSADOR"
                    asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR]
                    if not is_amb:
                        status = ReferralHistory.Status.EXPIRED.name

            if sign_off_user := sign_off_mapping.get(i.referree_id):
                account = sign_off_user.hidden_email
            else:
                account = i.referree.hidden_name
            flat_item = {
                "time": int(i.created_at.timestamp()),
                "account": account,
                "status": status,
                "has_deals": False,
                "has_deposits": i.referree_id in user_deposits,
                "type": referral_type_,
                "rewards": [
                    {
                        "asset": asset,
                        "spot_amount": Decimal(),
                        "perpetual_amount": Decimal(),
                    }
                ],
                'deals': {
                    'spot': Decimal(),
                    'perpetual': Decimal(),
                }
            }
            deals = user_trades.get(i.referree_id)
            if deals:
                flat_item['deals'] = deals
            summary_infos = summary_info_map.get(i.referree_id)
            if summary_infos:
                if type_:
                    if type_ is ReferralType.REFERRAL:
                        type_enum = ReferralAssetHistory.Type.REFERRAL
                    else:
                        type_enum = ReferralAssetHistory.Type.AMBASSADOR
                    type_infos = [info for info in summary_infos if info.type == type_enum]
                else:
                    type_infos = summary_infos
                flat_item["has_deals"] = False
                if type_infos:
                    rewards = [
                        dict(
                            asset=info.asset,
                            spot_amount=info.spot_amount,
                            perpetual_amount=info.perpetual_amount,
                        )
                        for info in type_infos
                    ]
                    flat_item["has_deals"] = True
                    flat_item["rewards"] = rewards
            if is_coupon_gift:
                user_coupon_info = cg_info.get(i.referree_id)
                flat_item['coupon_created_at'] = None
                flat_item['coupon_info'] = {}
                if user_coupon_info:
                    flat_item['coupon_created_at'] = user_coupon_info['created_at']
                    flat_item['coupon_info'] = {
                        'value': user_coupon_info['value'],
                        'value_type': user_coupon_info['value_type'],
                        'coupon_type': user_coupon_info['coupon_type'],
                    }
            flat_items.append(flat_item)

        if export:
            pref = UserPreferences(user_id)
            lang = get_request_language()
            with force_locale(lang.value):
                if not is_coupon_gift:
                    fields = ['account', 'created_at_str', 'has_deposits', 'has_deals', 'spot_deals', 'spot_amount_str',
                              'perpetual_deals', 'perpetual_amount_str', 'status']
                    headers = [_('账户'), _('注册时间'), _('是否充值'), _('是否交易'), _("现货交易额"), _('现货返佣'),
                               _("合约交易额"), _('合约返佣'), _('状态')]
                else:
                    fields = ['account', 'created_at_str', 'coupon_created_at', 'coupon_name', 'has_deposits',
                              'has_deals', 'spot_deals',
                              'spot_amount_str', 'perpetual_deals', 'perpetual_amount_str', 'status']
                    headers = [_('账户'), _('注册时间'), _('领取时间'), _('卡券名称'), _('是否充值'), _('是否交易'),
                               _("现货交易额"), _('现货返佣'), _("合约交易额"), _('合约返佣'), _('状态')]
                dt_to_str = partial(datetime_to_str, offset_minutes=pref.timezone_offset)
                for h in flat_items:
                    reward = h["rewards"][0]
                    h['created_at_str'] = dt_to_str(timestamp_to_datetime(h['time']))
                    h['spot_amount_str'] = f'{amount_to_str(reward["spot_amount"])} {reward["asset"]}'
                    h['perpetual_amount_str'] = f'{amount_to_str(reward["perpetual_amount"])} {reward["asset"]}'
                    h['spot_deals'] = f'{amount_to_str(h["deals"]["spot"])}'
                    h['perpetual_deals'] = f'{amount_to_str(h["deals"]["perpetual"])}'
                    if is_coupon_gift:
                        if h['coupon_created_at']:
                            h['coupon_created_at'] = dt_to_str(timestamp_to_datetime(h['coupon_created_at']))
                        h['coupon_name'] = ''
                        if coupon_info := h['coupon_info']:
                            h['coupon_name'] = f'{coupon_info["value"]}{coupon_info["value_type"]} {coupon_info["coupon_type"]}'
                stream = ExcelExporter(
                    data_list=flat_items,
                    headers=headers,
                    fields=fields,
                ).export_streams()

                return send_file(
                    stream,
                    download_name='referral_history.xlsx',
                    as_attachment=True
                )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(flat_items),
            data=flat_items,
            total=pagination.total,
            total_page=pagination.pages,
        )

    @classmethod
    def _check(cls, kwargs):
        type_ = kwargs.get('type')
        # web 与 app 筛选项不一致，暂时不处理 web
        if (_ := kwargs.get("has_trade")) is not None:
            if type_ is ReferralType.AMBASSADOR:
                raise InvalidArgument
        if (_ := kwargs.get("is_valid")) is not None:
            if type_ is ReferralType.AMBASSADOR:
                raise InvalidArgument

    @classmethod
    def get_referree_ids(cls, type_, user_id) -> Set[int]:

        @cached(60 * 5)
        def _get_referree_ids(type_, user_id):
            query = ReferralAssetSummary.query.with_entities(
                ReferralAssetSummary.referree_id
            ).filter(
                ReferralAssetSummary.user_id == user_id,
            )
            trade_users = {row.referree_id for row in query.all()}
            referral_query = ReferralHistory.query.with_entities(
                ReferralHistory.referree_id
            ).filter(
                ReferralHistory.referrer_id == user_id
            )
            _extra_users = set()
            if type_:
                if type_ is ReferralType.REFERRAL:
                    type_enum = ReferralAssetHistory.Type.REFERRAL
                    referral_type = ReferralHistory.ReferralType.NORMAL
                else:
                    type_enum = ReferralAssetHistory.Type.AMBASSADOR
                    referral_type = ReferralHistory.ReferralType.AMBASSADOR
                query = query.filter(
                    ReferralAssetSummary.type == type_enum
                )
                # Before 2022-10-18
                referral_query2 = referral_query.filter(
                    ReferralHistory.referral_type.is_(None)
                )
                history_users = {row.referree_id for row in referral_query2.all()}
                no_trade_users = history_users - trade_users  # 全部划为普通邀请关系
                if type_ is ReferralType.REFERRAL:
                    _extra_users = no_trade_users

                # ReferralHistory.referral_type created at 2022-10-18
                # 避免遗漏最近新邀请未交易的用户
                referral_query = referral_query.filter(
                    ReferralHistory.referral_type == referral_type
                )

            referree_ids = {row.referree_id for row in query.all()}
            referral_type_users = {row.referree_id for row in referral_query.all()}
            calc_referree_ids = referree_ids | _extra_users | referral_type_users
            return calc_referree_ids - {user_id}


        @cached(60 * 5)
        def _get_referree_ids_new(type_, user_id):
            history_query = ReferralHistory.query.filter(
                ReferralHistory.referrer_id == user_id
            ).with_entities(
                ReferralHistory.referree_id
            )
            if type_ == ReferralType.REFERRAL:
                history_query = history_query.filter(
                    ReferralHistory.referral_type.in_((None, ReferralHistory.ReferralType.NORMAL))
                )
            elif type_ == ReferralType.AMBASSADOR:
                history_query = history_query.filter(
                    ReferralHistory.referral_type == ReferralHistory.ReferralType.AMBASSADOR
                )
            summary_query = ReferralAssetSummary.query.filter(
                ReferralAssetSummary.user_id == user_id,
            ).with_entities(
                ReferralAssetSummary.referree_id
            )
            summary_user_ids = set()
            if type_ and type_ == ReferralType.AMBASSADOR:
                summary_type = ReferralAssetHistory.Type.AMBASSADOR
                summary_query = summary_query.filter(
                    ReferralAssetSummary.type == summary_type
                )
                summary_user_ids = {item.referree_id for item in summary_query.all()}
            referree_ids = {item.referree_id for item in history_query.all()} | summary_user_ids
            return referree_ids - {user_id}

        return set(_get_referree_ids_new(type_, user_id))

    @classmethod
    def get_coupon_gift_referree_info(cls, user_id, referree_user_ids: Set[int]) -> dict:

        @cached(60 * 5)
        def _get_coupon_gift_user_info(user_id, ) -> dict:
            valid_ret, invalid_ret = CouponTool.get_friend_gift_coupon_and_apply_list()
            results = valid_ret + invalid_ret
            apply_ids = set()
            coupon_map = {}
            for coupon, apply in results:
                if user_id not in apply.cached_user_ids:
                    continue
                apply_ids.add(apply.id)
                coupon_map[coupon.id] = coupon
            if not apply_ids:
                return {}
            pools = CouponPool.query.filter(
                CouponPool.apply_coupon_id.in_(apply_ids),
            ).all()
            pool_ids = [pool.id for pool in pools]
            if not pool_ids:
                return {}
            user_coupons = UserCoupon.query.with_entities(
                UserCoupon.user_id,
                UserCoupon.created_at,
                UserCoupon.coupon_id,
            ).filter(
                UserCoupon.pool_id.in_(pool_ids),
                UserCoupon.user_id.in_(referree_user_ids)
            ).all()
            ret = {}
            for user_coupon in user_coupons:
                coupon = coupon_map[user_coupon.coupon_id]
                ret.update({
                    user_coupon.user_id: {
                        'user_id': user_coupon.user_id,
                        'created_at': datetime_to_time(user_coupon.created_at),
                        'value': amount_to_str(coupon.value),
                        'value_type': coupon.value_type,
                        'coupon_type': coupon.coupon_type.name,
                    }
                })
            return ret

        return {int(uid): v for uid, v in _get_coupon_gift_user_info(user_id).items()}

    @classmethod
    def get_filter_user_ids(
            cls, user_id: int,
            referree_user_ids: Set[int],
            has_trade: bool = None,
            type_: ReferralType = None
    ) -> Set[int]:
        if has_trade is None:
            return referree_user_ids

        deal_user_ids = ReferralInfoResource.get_deal_user_ids(user_id, referree_user_ids, type_)
        if has_trade is True:
            return deal_user_ids
        return referree_user_ids - deal_user_ids

    @classmethod
    def _get_sign_off_users(cls, referree_ids: list):
        return SignOffUser.query.filter(
            SignOffUser.user_id.in_(referree_ids)
        ).all()

    @classmethod
    def _get_summary_rows(cls, user_id: int, referree_ids: list):
        return ReferralAssetSummary.query.filter(
            ReferralAssetSummary.user_id == user_id,
            ReferralAssetSummary.referree_id.in_(referree_ids),
        ).all()

    @classmethod
    def _get_deposited_user_ids(cls, referree_ids: list) -> set:
        deposits = Deposit.query.with_entities(
            Deposit.user_id
        ).filter(
            Deposit.user_id.in_(referree_ids),
            Deposit.status.in_([
                Deposit.Status.FINISHED,
                Deposit.Status.TO_HOT,
            ])
        ).group_by(
            Deposit.user_id
        ).all()
        return {row.user_id for row in deposits}

    @classmethod
    def get_user_trades(cls, referree_ids: list) -> dict:
        model = UserTradeSummary
        ret = defaultdict(lambda: {'spot': Decimal(), 'perpetual': Decimal()})
        rows = model.query.with_entities(
            model.user_id,
            model.system,
            func.sum(model.trade_amount).label('trade_amount'),
        ).filter(
            model.user_id.in_(referree_ids),
        ).group_by(
            model.user_id,
            model.system,
        ).all()
        for row in rows:
            if row.system is model.System.SPOT:
                ret[row.user_id]['spot'] += row.trade_amount
            else:
                ret[row.user_id]['perpetual'] += row.trade_amount
        ex_model = UserExchangeSummary
        ex_rows = ex_model.query.with_entities(
            ex_model.user_id,
            func.sum(ex_model.trade_amount).label('trade_amount'),
        ).filter(
            ex_model.user_id.in_(referree_ids)
        ).group_by(
            ex_model.user_id
        ).all()
        for row in ex_rows:
            ret[row.user_id]['spot'] += row.trade_amount
        return ret


    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            code=wa_fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 绑定邀请人 """
        user: User = g.user
        code = kwargs['code'].lower()
        referral = Referral.query.filter(Referral.code == code).first()
        if referral is None:
            raise ReferralCodeDoesNotExist
        if user.referrer is not None:
            raise AlreadyBindReferrer
        if user.created_at < now() - timedelta(days=CAN_BIND_REFERRER_DAYS):
            raise BindReferrerTimeout
        ref_user = User.query.get(referral.user_id)
        if user.created_at <= ref_user.created_at:
            raise BindReferrerYounger
        if user.id == ref_user.id:
            raise InvalidArgument('referrer error')

        is_ambassador = ReferralBusiness.is_valid_ambassador(referral.user_id)
        db.session_add_and_commit(ReferralHistory(
            referral_id=referral.id,
            referrer_id=referral.user_id,
            referree_id=user.id,
            referral_type=ReferralHistory.ReferralType.AMBASSADOR if is_ambassador else ReferralHistory.ReferralType.NORMAL
        ))
        OperationLog.add(user.id, 
                         OperationLog.Operation.BIND_REFERRER, 
                         f'用户手动绑定邀请人，邀请人ID为{referral.user_id}', get_request_platform())


@ns.route('/asset/history')
@respond_with_code
class ReferralAssetHistoryResource(Resource):

    marshal_fields = {
        "time": TimestampMarshalField(attribute="date"),
        "asset": fx_fields.String,
        "amount": AmountField(attribute=lambda x: x.spot_amount + x.perpetual_amount),
        "spot_amount": AmountField,
        "perpetual_amount": AmountField,
        "spot_fee_usd": AmountField,
        "perpetual_fee_usd": AmountField,
    }

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
            code=wa_fields.String,
            start_date=TimestampField(to_date=True),
            end_date=TimestampField(to_date=True),
            report_type=EnumField(ReportType, default=ReportType.DAILY),
            type=EnumField(ReferralType),
            export=wa_fields.Integer(missing=0),
        )
    )
    def get(cls, **kwargs):
        """ 推荐返佣历史|返佣记录 """
        user_id = g.user.id
        account = g.user.hidden_complete_name
        code = kwargs.get("code")
        type_ = kwargs.get("type")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        type_enum = None
        if kwargs.get("report_type") == ReportType.MONTHLY:
            model = MonthlyReferralCodeAssetDetail
            query = model.query.filter(
                model.user_id == user_id,
            ).order_by(model.date.desc())
            if type_:
                if type_ is ReferralType.REFERRAL:
                    type_enum = model.Type.REFERRAL
                else:
                    type_enum = model.Type.AMBASSADOR
        else:
            model = ReferralCodeAssetDetail
            query = model.query.filter(
                model.user_id == user_id,
            ).order_by(model.date.desc())
            if type_:
                if type_ is ReferralType.REFERRAL:
                    type_enum = ReferralAssetHistory.Type.REFERRAL
                else:
                    type_enum = ReferralAssetHistory.Type.AMBASSADOR
        if type_enum:
            query = query.filter(model.type == type_enum)
        if code:
            referral_code = Referral.query.filter(Referral.user_id == user_id, Referral.code == code).first()
            if not referral_code:
                raise InvalidArgument
            query = query.filter(model.referral_id == referral_code.id)
        else:
            query = query.filter(model.referral_id.is_(None))
        if start_date:
            query = query.filter(model.date >= start_date)
        if end_date:
            query = query.filter(model.date <= end_date)

        is_daily = kwargs.get("report_type") != ReportType.MONTHLY
        if not kwargs["export"]:
            page_data = query_to_page(query, kwargs["page"], kwargs["limit"], cls.marshal_fields)
            dates = [timestamp_to_date(x['time']) for x in page_data['data']]
            deal_amounts = cls._get_user_trade_amount_by(user_id, dates, is_daily)
            for item in page_data['data']:
                deal_amount = deal_amounts[(item['asset'], timestamp_to_date(item['time']))]
                item['deal_amount'] = deal_amount
                item['account'] = account
            return page_data

        data_list = query.paginate(1, config["EXPORT_ITEM_MAX_COUNT"], error_out=False).items
        file_name = now().strftime("%Y%m%d-refer-asset-history")
        pref = UserPreferences(user_id)
        with force_locale(pref.language.value):
            fields = ["time", "account", "deal_amount", "fee_usd", "spot_fee_usd", "perpetual_fee_usd", "amount"]
            headers = [_("发放时间"), _('账户'), _('累计邀请交易量'), _("总手续费"), _("现货手续费"), _("合约手续费"), _("返佣金额")]
            fmt = "%Y-%m-%d"
            if kwargs.get("report_type") == ReportType.MONTHLY:
                fmt = "%Y-%m"
            dt_to_str = partial(datetime_to_str, offset_minutes=pref.timezone_offset, fmt=fmt)
            export_items = []
            dates = [x.date for x in data_list]
            deal_amounts = cls._get_user_trade_amount_by(user_id, dates, is_daily)
            for d in data_list:
                deal_amount = deal_amounts[(d.asset, d.date)]
                item = {
                    "account": account,
                    "amount": f"{amount_to_str(d.spot_amount + d.perpetual_amount)} {d.asset}",
                    "deal_amount": f"{amount_to_str(deal_amount)} USD",
                    "fee_usd": f"{amount_to_str(d.spot_fee_usd + d.perpetual_fee_usd)} USD",
                    "spot_fee_usd": f"{amount_to_str(d.spot_fee_usd)} USD",
                    "perpetual_fee_usd": f"{amount_to_str(d.perpetual_fee_usd)} USD",
                    "time": dt_to_str(date_to_datetime(d.date)),
                }
                export_items.append(item)
            stream = ExcelExporter(
                data_list=export_items,
                headers=headers,
                fields=fields,
            ).export_streams()

            return send_file(
                stream,
                download_name=f"{file_name}.xlsx",
                as_attachment=True,
            )

    @classmethod
    def _get_user_trade_amount_by(cls, user_id, dates: list, is_daily=True) -> dict:
        """
        # PS. 以下 model 所获取的字段相同，且定义字段相同
        # 对于当前返佣逻辑互斥，一个邀请人对应的：今天要么全是普通返佣，要么全是平台大使返佣，要么是商务大使返佣
        # 历史返佣数据非互斥（邀请人同时存在普通返佣 + 平台大使返佣），历史原因 model 统计存在相同交易量

        """
        ret = defaultdict(Decimal)
        if not dates:
            return ret
        query_start, query_end = min(dates), max(dates)
        c_daily_models = [
            DailyUserReferralDetailReport
        ]
        c_monthly_models = [
            MonthlyUserReferralDetailReport,
        ]
        u_daily_models = [
            DailyAmbassadorReferralDetailReport,
            DailyBusinessAmbassadorReferralReport,
        ]
        u_monthly_models = [
            MonthlyAmbassadorReferralDetailReport,
            MonthlyBusinessAmbassadorReferralReport,
        ]
        c_models = c_daily_models
        u_models = u_daily_models
        if not is_daily:
            query_start = query_start.replace(day=1)
            query_end = query_end.replace(day=1)
            c_models = c_monthly_models
            u_models = u_monthly_models

        for model in c_models:
            rows = model.query.with_entities(
                model.report_date,
                model.spot_trade_usd,
                model.perpetual_trade_usd
            ).filter(
                model.user_id == user_id,
                model.report_date >= query_start,
                model.report_date <= query_end,
            ).all()
            for row in rows:
                ret[('CET', row.report_date)] += row.spot_trade_usd + row.perpetual_trade_usd
        for model in u_models:
            rows = model.query.with_entities(
                model.report_date,
                model.spot_trade_usd,
                model.perpetual_trade_usd
            ).filter(
                model.user_id == user_id,
                model.report_date >= query_start,
                model.report_date <= query_end,
            ).all()
            for row in rows:
                ret[('USDT', row.report_date)] += row.spot_trade_usd + row.perpetual_trade_usd
        return ret


@ns.route('/rank')
@respond_with_code
class ReferralRankResource(Resource):

    @classmethod
    def get(cls):
        """推荐排行"""
        value = ReferralRankCache().read()
        if not value:
            return []
        value = json.loads(value)
        return [dict(
            account=User.query.get(x['user_id']).hidden_complete_name,
            asset=x['asset'],
            amount=x['amount'],
            rank=x['rank']
        ) for x in value]


@ns.route('/pictures')
@respond_with_code
class ReferralPicturesResource(Resource):
    model = ReferralPicture

    @classmethod
    @ns.use_kwargs(dict(
        version=wa_fields.String(missing=1),
    ))
    def get(cls, **kwargs):
        """推荐图片"""
        records = cls.model.query.filter(
            cls.model.status == cls.model.Status.VALID,
            cls.model.version == kwargs['version']
        ).order_by(ReferralPicture.id.desc()).all()
        result = defaultdict(list)
        for item in records:
            result[item.lang.value].append(item.file.static_url)
        return result


@ns.route('/ambassador/application')
@respond_with_code
class AmbassadorApplicationResource(Resource):

    POST_SCHEMA = dict(
        name=wa_fields.String(required=True),
        head=wa_fields.String,
        mobile_num=wa_fields.String(required=True),
        city=wa_fields.String(required=True),
        profession=wa_fields.String(required=True),
        proficient_langs=wa_fields.String(required=True),
        community_scale=EnumField(AmbassadorApplication.CommunityScale,
                                  default=AmbassadorApplication.CommunityScale.BELOW_200),
        reason=EnumField(AmbassadorApplication.Reason, required=True),
        plan=wa_fields.String(required=True),
        plan_detail=wa_fields.String(required=True),
        wechat=wa_fields.String,
        telegram=wa_fields.String,
        referral_code=wa_fields.String
    )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(POST_SCHEMA)
    def post(cls, **kwargs):
        """大使申请"""
        user_id = g.user.id
        if code := kwargs.get('referral_code'):
            if not (row := Referral.query.filter(Referral.code == code).first()):
                raise ReferralCodeDoesNotExist
            if row.user_id == user_id:
                raise InvalidArgument(message="Ambassador agent cannot be yourself")
            agent = AmbassadorAgentBusiness.get_agent(row.user_id)
            if not agent:
                raise ReferralCodeInvalid
            if not AmbassadorBusiness.can_apply_ambassador(user_id, agent_user_id=row.user_id):
                raise InvalidArgument(message="Already have bounden ambassador agent")

            kwargs['referral_code'] = row.code

        ambassador = Ambassador.query.filter(
            Ambassador.user_id == user_id,
            Ambassador.status == Ambassador.Status.VALID
        ).first()
        is_amb = bool(ambassador) or BusRelationUserQuerier.get_bus_ambassador(user_id, need_valid=True)
        if is_amb:
            raise InvalidArgument(
                message=_("你已经是CoinEx大使"))

        row = AmbassadorApplication.query.filter(
            AmbassadorApplication.user_id == user_id,
            AmbassadorApplication.type == AmbassadorApplication.Type.AMBASSADOR,
            AmbassadorApplication.status == AmbassadorApplication.Status.CREATED
        ).first()
        if row:
            raise InvalidArgument(message=_("你已提交申请，请等待审核"))

        is_viabtc_pool_amb = ViaBtcPoolAmbBusiness.is_viabtc_pool_amb(g.user.email)
        row = AmbassadorApplication(
            user_id=user_id,
            type=AmbassadorApplication.Type.AMBASSADOR,
            **kwargs
        )
        db.session_add_and_commit(row)
        if code:
            send_ambassador_application_to_agent.delay(
                'ambassador_application_submit', g.user.id, agent.user.id,
                kwargs.get('telegram'))
        if is_viabtc_pool_amb:
            audit_viabtc_pool_ambassador_application_task.delay(g.user.id)
        update_amb_apl_last_invalid_reason_schedule.delay()
        return {}

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """大使申请的校验信息，给前端用"""
        user_id = g.user.id
        row = AmbassadorBusiness.get_ambassador(user_id, status=None)
        is_first_apply = not row
        code = AmbassadorBusiness.get_ambassador_agent_code(user_id)

        # 返回最近一次申请大使填写的内容
        old_apply: AmbassadorApplication = AmbassadorApplication.query.filter(
            AmbassadorApplication.user_id == user_id,
            AmbassadorApplication.type == AmbassadorApplication.Type.AMBASSADOR,
        ).order_by(AmbassadorApplication.id.desc()).first()
        if old_apply:
            old_apply_info = {
                "name": old_apply.name,
                "head": old_apply.head,
                "head_url": AWSBucketPrivate.get_file_url(old_apply.head) if old_apply.head else "",
                "mobile_num": old_apply.mobile_num,
                "telegram": old_apply.telegram,
                "city": old_apply.city,
                "profession": old_apply.profession,
                "proficient_langs": old_apply.proficient_langs,
                "community_scale": old_apply.community_scale.name,
                "reason": old_apply.reason.name,
                "plan": old_apply.plan,
                "plan_detail": old_apply.plan_detail,
            }
        else:
            old_apply_info = {}

        return dict(
            is_first_apply=is_first_apply,
            ambassador_agent_code=code,
            old_apply_info=old_apply_info,
        )


@ns.route('/ambassador/info')
@respond_with_code
class AmbassadorInfoResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """大使信息"""
        return cls.get_amb_info(g.user.id)

    @classmethod
    def get_amb_info(cls, user_id: int) -> dict:
        row = AmbassadorBusiness.get_ambassador(user_id, status=None)
        if row and row.status == Ambassador.Status.VALID:
            bus_amb = None
        else:
            bus_amb = BusRelationUserQuerier.get_bus_ambassador(user_id, need_valid=False)
        if not row and not bus_amb:
            return dict(
                is_ambassador=False,
                has_been_ambassador=False,
                continent='',
                level="",
                level_name="",
                type=""
            )

        nor_amb_info = dict(
            continent=row.continent if row else "",
            level=row.level.name if row else "",
            level_name=_(Ambassador.LEVEL_NAMES[row.level]) if row else "",
            type=Ambassador.Type.NORMAL.name,
        )
        bus_amb_info = dict(
            continent="",
            level="",
            level_name="",
            type=Ambassador.Type.BUSINESS.name,
        )

        is_nor_amb = True if row and row.status is Ambassador.Status.VALID else False
        is_bus_amb = True if bus_amb and bus_amb.is_valid else False
        is_ambassador = is_nor_amb or is_bus_amb
        if is_bus_amb:
            info = bus_amb_info
        elif is_nor_amb:
            info = nor_amb_info
        elif row and bus_amb:
            # 普通大使、商务大使都失效了
            if row.effected_at <= bus_amb.effected_at:
                # 最近失效的是商务大使
                info = bus_amb_info
            else:
                info = nor_amb_info
        elif row:
            info = nor_amb_info
        else:
            info = bus_amb_info

        return dict(
            is_ambassador=is_ambassador,
            has_been_ambassador=True,
            **info,
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            continent=EnumField(AmbassadorStar.Continent, required=True),
        )
    )
    def patch(cls, **kwargs):
        """大使点亮大洲"""
        continent = kwargs['continent']
        user_id = g.user.id
        ambassador = ReferralBusiness.get_normal_ambassador(user_id)
        if not ambassador:
            raise InvalidArgument

        ambassador.continent = continent.name
        db.session.commit()


@ns.route('/ambassador/asset/history')
@respond_with_code
class AmbassadorAssetHistoryResource(Resource):

    marshal_fields = {
        'report_date': fx_fields.String(attribute=lambda x: x.report_date.strftime('%Y-%m')),
        'referral_count': fx_fields.Integer,
        'referral_amount': AmountField,
        'asset': fx_fields.String,
        'deal_amount': AmountField,
        'deal_user_count': fx_fields.Integer,
        'delta_referral_trade_count': fx_fields.Integer,
        'level': EnumMarshalField(Ambassador.Level, output_field_lower=False),
        'level_name': fx_fields.String(attribute=lambda x: _(Ambassador.LEVEL_NAMES[x.level])),
    }

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """大使月度返佣历史"""
        query = MonthlyAmbassadorReport.query.filter(
            MonthlyAmbassadorReport.user_id == g.user.id
        ).order_by(MonthlyAmbassadorReport.report_date.desc())
        return query_to_page(query, kwargs['page'], kwargs['limit'], cls.marshal_fields)


@ns.route("/ambassador/guides")
@respond_with_code
class AmbassadorGuidesResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 大使攻略列表 """
        pagination = (
            AmbassadorGuide.query.filter(AmbassadorGuide.status == AmbassadorGuide.Status.VALID)
            .order_by(AmbassadorGuide.rank.desc())
            .paginate(kwargs["page"], kwargs["limit"], error_out=False)
        )
        guides = pagination.items
        guide_ids = [i.id for i in guides]
        tran_query = AmbassadorGuideTranslation.query.filter(AmbassadorGuideTranslation.guide_id.in_(guide_ids)).all()
        guide_tran_dict = defaultdict(dict)  # { guide_id: {language_str: trans_ojb } }
        for t in tran_query:
            guide_tran_dict[t.guide_id][t.lang] = t

        # noinspection PyBroadException
        try:
            g_lang_name = Language(g.lang).name
        except Exception:
            g_lang_name = Language.EN_US.name

        def _get_detail_dict(_guide_id):
            _trans = guide_tran_dict[_guide_id]
            _tran = _trans.get(g_lang_name) or _trans.get(Language.EN_US.name)
            return {
                "title": _tran.title if _tran else "",
                "url": _tran.url if _tran else "",
            }

        guides = [
            {
                "id": v.id,
                "time": v.created_at,
                **_get_detail_dict(v.id),
            }
            for v in guides
        ]
        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(guides),
            data=guides,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route('/ambassador/agent/info')
@respond_with_code
class AmbassadorAgentInfoResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """大使代理信息"""
        empty_res = dict(
            is_ambassador_agent=False,
            referral_code="",
            referral_count=0,
            valid_referral_count=0,
            referral_user_count=0,
            asset=ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR_AGENT],
            referral_amount=Decimal(),
            referral_rate=Decimal(),
        )
        bus_user = BusRelationUserQuerier.get_bus_user(g.user.id, need_valid=True)
        if bus_user:
            return empty_res
        row = AmbassadorAgentBusiness.get_agent(g.user.id)
        if not row:
            return empty_res

        code = ReferralBusiness.get_code_by_user(g.user.id)
        count = AmbassadorAgentHistory.query.filter(
            AmbassadorAgentHistory.user_id == g.user.id,
        ).with_entities(func.count('*')).scalar() or 0

        valid_count = AmbassadorAgentHistory.query.filter(
            AmbassadorAgentHistory.user_id == g.user.id,
            AmbassadorAgentHistory.status == AmbassadorAgentHistory.Status.VALID,
        ).with_entities(func.count('*')).scalar() or 0

        agent_report = MonthlyAmbassadorAgentReport.query.filter(
            MonthlyAmbassadorAgentReport.user_id == g.user.id,
        ).order_by(MonthlyAmbassadorAgentReport.report_date.desc()).first()
        referral_user_count = agent_report.referral_user_count if agent_report else 0

        amount = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.user_id == g.user.id,
            ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR_AGENT
        ).with_entities(func.sum(ReferralAssetHistory.amount)).scalar() or 0

        return dict(
            is_ambassador_agent=True,
            referral_code=code,
            referral_count=count,
            valid_referral_count=valid_count,
            referral_user_count=referral_user_count,
            asset=ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR_AGENT],
            referral_amount=amount,
            status=row.status.name,
            referral_rate=row.referral_rate
        )


@ns.route('/ambassador/agent/history')
@respond_with_code
class AmbassadorAgentHistoryResource(Resource):
    export_headers = (
        {"field": "time", Language.ZH_HANS_CN: "生效时间", Language.EN_US: "Effective Time"},
        {"field": "account", Language.ZH_HANS_CN: "账户", Language.EN_US: "Account"},
        {"field": "level", Language.ZH_HANS_CN: "大使最新等级", Language.EN_US: "Latest Rank"},
        {"field": "referral_user_count", Language.ZH_HANS_CN: "推荐注册用户数", Language.EN_US: "Registered Users"},
        {"field": "deal_user_count", Language.ZH_HANS_CN: "推荐交易用户数", Language.EN_US: "Referred Traders"},
        {"field": "referral_amount", Language.ZH_HANS_CN: "累计返佣（USDT）", Language.EN_US: "Cumulative Reward (USDT)"},
        {"field": "status", Language.ZH_HANS_CN: "返佣状态", Language.EN_US: "Referral Status"},
    )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
        status=EnumField(AmbassadorAgentHistory.Status),
        export=wa_fields.Boolean(default=False),
    ))
    def get(cls, **kwargs):
        """大使代理邀请历史"""
        query = AmbassadorAgentHistory.query.filter(
            AmbassadorAgentHistory.user_id == g.user.id,
        ).order_by(AmbassadorAgentHistory.id.desc())

        if kwargs.get('status'):
            query = query.filter(AmbassadorAgentHistory.status == kwargs['status'])
        if kwargs.get("export"):
            rows = query.all()
            ambassador_user_ids = [row.ambassador_id for row in rows]

            histories = (
                AmbassadorAgentAssetDetail.query.filter(
                    AmbassadorAgentAssetDetail.user_id == g.user.id,
                ).group_by(
                    AmbassadorAgentAssetDetail.ambassador_id
                ).with_entities(
                    AmbassadorAgentAssetDetail.ambassador_id,
                    func.sum(AmbassadorAgentAssetDetail.amount)
                ).all()
            )
            histories = dict(histories)

            referral_user_count_dict = {}
            deal_user_count_dict = {}
            amb_statistics_map = {
                i.user_id: i for i in
                AmbassadorStatistics.query.filter(AmbassadorStatistics.user_id.in_(ambassador_user_ids)).all()
            }
            for amb_id, amb_stat in amb_statistics_map.items():
                referral_user_count_dict[amb_id] = amb_stat.cur_valid_refer_count
                deal_user_count_dict[amb_id] = amb_stat.refer_deal_count

            user_emails = fetch_user_hidden_accounts(ambassador_user_ids)
            ambassadors = Ambassador.query.filter(
                Ambassador.user_id.in_(ambassador_user_ids),
                Ambassador.status == Ambassador.Status.VALID,
            ).all()
            ambassador_mapping = {ambassador.user_id: ambassador for ambassador in ambassadors}

            statuses = {
                AmbassadorAgentHistory.Status.VALID: 'VALID',
                AmbassadorAgentHistory.Status.DELETED: 'INVALID',
            }
            items = []
            for row in rows:
                ambassador = ambassador_mapping.get(row.ambassador_id)
                if ambassador:
                    level = ambassador.level.name
                else:
                    level = 'INVALID'
                item = dict(
                    time=row.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    account=user_emails.get(row.ambassador_id),
                    asset=ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR_AGENT],
                    referral_amount=amount_to_str(
                        histories.get(row.ambassador_id, Decimal()), PrecisionEnum.COIN_PLACES
                    ),
                    referral_user_count=referral_user_count_dict.get(row.ambassador_id, 0),
                    deal_user_count=deal_user_count_dict.get(row.ambassador_id, 0),
                    status=statuses.get(row.status),
                    level=level,
                )
                items.append(item)
            return export_xlsx(
                filename='ambassador_agent_history.xlsx',
                data_list=items,
                export_headers=cls.export_headers
            )

        else:
            pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
            rows = pagination.items
            ambassador_user_ids = [x.ambassador_id for x in rows]

            histories = (
                AmbassadorAgentAssetDetail.query.filter(
                    AmbassadorAgentAssetDetail.user_id == g.user.id,
                    AmbassadorAgentAssetDetail.ambassador_id.in_(ambassador_user_ids),
                )
                .group_by(AmbassadorAgentAssetDetail.ambassador_id)
                .with_entities(AmbassadorAgentAssetDetail.ambassador_id,
                               func.sum(AmbassadorAgentAssetDetail.amount))
                .all()
            )
            histories = dict(histories)

            referral_user_count_dict = {}
            deal_user_count_dict = {}
            amb_statistics_map = {
                i.user_id: i for i in AmbassadorStatistics.query.filter(AmbassadorStatistics.user_id.in_(ambassador_user_ids)).all()
            }
            for amb_id, amb_stat in amb_statistics_map.items():
                referral_user_count_dict[amb_id] = amb_stat.cur_valid_refer_count
                deal_user_count_dict[amb_id] = amb_stat.refer_deal_count

            user_emails = fetch_user_hidden_accounts(ambassador_user_ids)
            ambassadors = Ambassador.query.filter(
                Ambassador.user_id.in_(ambassador_user_ids),
                Ambassador.status == Ambassador.Status.VALID,
            ).all()
            ambassador_mapping = {ambassador.user_id: ambassador for ambassador in ambassadors}
            items = [
                dict(
                    time=x.created_at,
                    account=user_emails[x.ambassador_id],
                    asset=ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR_AGENT],
                    referral_amount=histories.get(x.ambassador_id, Decimal()),
                    referral_user_count=referral_user_count_dict.get(x.ambassador_id, 0),
                    deal_user_count=deal_user_count_dict.get(x.ambassador_id, 0),
                    status=x.status.name,
                    level=ambassador_mapping.get(x.ambassador_id).level.name if ambassador_mapping.get(x.ambassador_id) else None,
                )
                for x in rows
            ]

            return dict(
                has_next=pagination.has_next,
                curr_page=pagination.page,
                count=len(items),
                data=items,
                total=pagination.total,
                total_page=pagination.pages
            )


@ns.route('/ambassador/agent/asset/history')
@respond_with_code
class AmbassadorAgentAssetHistoryResource(Resource):
    export_headers = (
        {"field": "time", Language.ZH_HANS_CN: "时间", Language.EN_US: "Time"},
        {"field": "account", Language.ZH_HANS_CN: "账户", Language.EN_US: "Account"},
        {"field": "deal_user_count", Language.ZH_HANS_CN: "交易用户", Language.EN_US: "Traded Referees"},
        {"field": "fee_amount", Language.ZH_HANS_CN: "手续费（USD）", Language.EN_US: "Fee(USD)"},
        {"field": "referral_amount", Language.ZH_HANS_CN: "返佣（USDT）", Language.EN_US: "Cumulative Reward(USDT)"},
    )

    marshal_fields = {
        'user_id': fx_fields.Integer,
        'time': TimestampMarshalField(attribute='report_date'),
        'deal_user_count': fx_fields.Integer(),
        'fee_amount': AmountField,
        "amount": AmountField(attribute="referral_amount")
    }

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
            report_type=EnumField(ReportType, default=ReportType.DAILY),
            export=wa_fields.Boolean(default=False),
        )
    )
    def get(cls, **kwargs):
        """大使代理返佣历史"""
        if kwargs.get("report_type") == ReportType.MONTHLY:
            model = MonthlyAmbassadorAgentAssetReport
        else:
            model = DailyAmbassadorAgentAssetReport

        query = model.query.filter(
            model.agent_id == g.user.id,
        ).order_by(model.report_date.desc())

        ambassador_user_ids = []
        asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR_AGENT]
        if kwargs.get("export"):
            rows = query.all()
            for row in rows:
                ambassador_user_ids.append(row.user_id)

            items = []
            user_accounts = fetch_user_hidden_accounts(ambassador_user_ids)
            for row in rows:
                items.append(dict(
                    time=row.report_date.strftime("%Y-%m-%d"),
                    deal_user_count=row.deal_user_count,
                    referral_amount=amount_to_str(row.referral_amount, PrecisionEnum.COIN_PLACES),
                    fee_amount=amount_to_str(row.fee_amount, PrecisionEnum.COIN_PLACES),
                    account=user_accounts.get(row.user_id),
                ))
            return export_xlsx(
                filename='referral_history.xlsx',
                data_list=items,
                export_headers=cls.export_headers
            )
        else:
            response = query_to_page(query, kwargs["page"], kwargs["limit"], cls.marshal_fields)
            for item in response['data']:
                ambassador_user_ids.append(item["user_id"])

            user_accounts = fetch_user_hidden_accounts(ambassador_user_ids)
            for item in response['data']:
                item["account"] = user_accounts.get(item["user_id"])
                item['asset'] = asset
            return response


def fetch_user_hidden_accounts(user_ids):
    user_emails = {}
    for chunk_user_ids in batch_iter(user_ids, 1000):
        users = User.query.filter(User.id.in_(chunk_user_ids)).all()
        user_emails.update({user.id: user.hidden_name for user in users})
    return user_emails


@ns.route('/ambassador/rank')
@respond_with_code
class StarRankResource(Resource):

    @classmethod
    def get(cls):
        """大使排名"""
        value = AmbassadorRankCache().read()
        if not value:
            return []
        value = json.loads(value)
        return [dict(
            account=User.query.get(x['user_id']).hidden_name,
            referral_count=x['referral_count'],
            rank=x['rank']
        ) for x in value]


@ns.route('/ambassador/star')
@respond_with_code
class StarResource(Resource):

    class IntroField(fx_fields.String):

        def format(self, value: AmbassadorStar):
            if g.lang == Language.ZH_HANS_CN.value:
                return value.intro_cn
            elif g.lang == Language.ZH_HANT_HK.value:
                return value.intro_tc
            return value.intro_en

    marshal_fields = {
        'user_name': fx_fields.String(attribute='name'),
        'intro': IntroField(attribute=lambda x: x),
        'avatar': FileField(attribute='file_id'),
        'continent': fx_fields.String,
        'country': fx_fields.String,
    }

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """优秀大使"""
        query = AmbassadorStar.query.order_by(AmbassadorStar.sort_id)
        return query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)


@ns.route('/ambassador/activity')
@respond_with_code
class ActivityResource(Resource):

    @classmethod
    def get(cls):
        """大使最新活动"""
        lang = g.lang or Language.DEFAULT.value
        return cls._get_ret(lang)

    @classmethod
    @mem_cached(120)
    def _get_ret(cls, lang):
        model = AmbassadorActivity
        row = model.query.with_entities(
            model.id,
            model.url
        ).filter(
            model.status == model.Status.OPEN
        ).order_by(
            model.id.desc()
        ).first()
        if not row:
            return {}

        content_row = AmbassadorActivityContent.query.filter(
            AmbassadorActivityContent.activity_id == row.id,
            AmbassadorActivityContent.lang == Language(lang)
        ).first()
        if not content_row:
            en_us_row = AmbassadorActivityContent.query.filter(
                AmbassadorActivityContent.activity_id == row.id,
                AmbassadorActivityContent.lang == Language.EN_US
            ).first()
            if en_us_row:
                content = en_us_row.content
            else:
                content = ''
        else:
            content = content_row.content
        return dict(
            content=content,
            url=row.url
        )


@ns.route('/activity/banners')
@respond_with_code
class ReferralActivityBannersResource(Resource):

    @classmethod
    @response_replace_host_url
    def get(cls):
        req_platform = get_request_platform()
        if req_platform.is_mobile():
            platform = 'APP'
        else:
            platform = 'WEB'
        lang = get_request_language()
        return cls._get(platform, lang.value)

    @classmethod
    @mem_cached(60 * 2)
    def _get(cls, platform: str, lang: str) -> List:
        ret = ReferralActivityBannerCache().get_ret_by(platform)
        result = []
        for item in ret:
            if lang not in item['trans']:
                continue
            result.append(item)
        return result


@ns.route('/ambassador/coupon-gift-entrance')
@respond_with_code
class AmbassadorCouponGiftResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """大使主页-邀请礼入口"""
        return cls._get(g.user.id)

    @classmethod
    @mem_cached(60 * 2)
    def _get(cls, user_id: int) -> dict:
        info = AmbassadorInfoResource.get_amb_info(user_id)
        if not info['is_ambassador']:
            return dict(show=False)
        coupon_and_apply_list, _ = CouponTool.get_friend_gift_coupon_and_apply_list()
        if not coupon_and_apply_list:
            return dict(show=False)
        apply_list = [t[1] for t in coupon_and_apply_list]
        apply_map = {apply.id: apply for apply in apply_list}
        apply_ids = list(apply_map.keys())
        pools = CouponPool.query.with_entities(
            CouponPool.apply_coupon_id
        ).filter(
            CouponPool.apply_coupon_id.in_(apply_ids),
            CouponPool.send_count < CouponPool.total_count,
            CouponPool.status == CouponPool.Status.VALID
        ).all()
        valid_apply_ids = [pool.apply_coupon_id for pool in pools]
        for apply_id in valid_apply_ids:
            if user_id in apply_map[apply_id].cached_user_ids:
                return dict(show=True)
        return dict(show=False)


@ns.route('/coupon-gift/myself-entrance')
@respond_with_code
class MyselfEntranceCouponGiftResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            type=EnumField(ReferralType, missing=ReferralType.AMBASSADOR),
        )
    )
    def get(cls, **kwargs):
        """返佣-我的邀请礼入口"""
        type_ = kwargs['type']
        if type_ is ReferralType.AMBASSADOR:
            return cls._ambassador_has_coupon_gift(g.user.id)
        else:
            return cls._normal_has_coupon_gift(g.user.id)

    @classmethod
    @mem_cached(60 * 2)
    def _ambassador_has_coupon_gift(cls, user_id: int) -> dict:
        info = AmbassadorInfoResource.get_amb_info(user_id)
        if not info['is_ambassador']:
            return dict(show=False)
        return dict(show=CouponTool.has_friend_gift(user_id))

    @classmethod
    @mem_cached(60 * 2)
    def _normal_has_coupon_gift(cls, user_id: int) -> dict:
        return dict(show=CouponTool.has_friend_gift(user_id))


@ns.route('/coupon-gift/myself')
@respond_with_code
class MyselfCouponGiftResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            type=EnumField(ReferralType, missing=ReferralType.AMBASSADOR),
            is_valid=wa_fields.Boolean(missing=True),
        )
    )
    def get(cls, **kwargs):
        """返佣-我的邀请礼"""
        is_valid = kwargs['is_valid']
        user_id = g.user.id
        return cls._get_user_coupon_gift(user_id, is_valid)

    @classmethod
    @mem_cached(60 * 2)
    def _get_user_coupon_gift(cls, user_id: int, is_valid: bool) -> list:
        valid_ret, invalid_ret = CouponTool.get_friend_gift_coupon_and_apply_list()
        results = valid_ret if is_valid else invalid_ret
        apply_to_coupon = {apply.id: coupon for (coupon, apply) in results}
        apply_ids = list(apply_to_coupon.keys())
        pools = CouponPool.query.filter(
            CouponPool.apply_coupon_id.in_(apply_ids),
        ).all()
        apply_to_pool = {pool.apply_coupon_id: pool for pool in pools}
        origin_ids = {apply.origin_id for (coupon, apply) in results}
        apply_drafts = CouponApplyDraft.query.with_entities(
            CouponApplyDraft.id,
            CouponApplyDraft.invitees_limit,
        ).filter(
            CouponApplyDraft.id.in_(origin_ids)
        ).all()
        apply_drafts = dict(apply_drafts)
        ret = []
        for coupon, apply in results:
            if user_id not in apply.cached_user_ids:
                continue
            item = {
                'value': amount_to_str(coupon.value),
                'value_type': coupon.value_type,
                'coupon_type': coupon.coupon_type.name,
                'receivable_days': coupon.receivable_days,

                'send_at': datetime_to_time(apply.send_at),
                'send_count': 0,
                'total_count': 0,
                'pool_send_count': 0,
                'pool_total_count': 0,
            }
            apply_id = apply.id
            pool = apply_to_pool.get(apply_id)
            if pool:
                item.update({
                    'pool_send_count': pool.send_count,
                    'pool_total_count': pool.total_count,
                })
            invitees_limit: CouponApplyDraft | None = apply_drafts.get(apply.origin_id)
            if invitees_limit:  # 历史数据默认为 0，前端做特殊处理
                item.update({
                    'send_count': cls._get_send_count(user_id, pool),
                    'total_count': invitees_limit,
                })

            ret.append(item)
        ret.sort(key=lambda x: x['send_at'], reverse=True)
        return ret

    @classmethod
    def _get_send_count(cls, user_id: int, pool: CouponPool = None):
        if not pool:
            return 0
        referree_ids = ReferralHistoryResource.get_referree_ids(None, user_id)
        return UserCoupon.query.with_entities(
            func.count('*')
        ).filter(
            UserCoupon.user_id.in_(referree_ids),
            UserCoupon.pool_id == pool.id,
            UserCoupon.coupon_id == pool.coupon_id,
        ).scalar() or 0


@ns.route('/ambassador/dashboard/summary')
@respond_with_code
class AmbDashboardSummaryResource(Resource):
    @classmethod
    def require_is_amb(cls, user_id: int):
        if AmbassadorBusiness.get_ambassador(user_id, status=None):
            return
        if BusRelationUserQuerier.get_bus_ambassador(user_id, need_valid=False):
            return
        raise InvalidArgument

    @classmethod
    def load_and_merge_asset_dict(cls, *dict_str_list) -> list[dict]:
        dict_list = [json.loads(i) for i in dict_str_list if i]
        merge_dict = AmbDashboardHelper.merge_and_sum_dict(*dict_list)
        res = [{"asset": k, "amount": v} for k, v in merge_dict.items()]
        return res

    @classmethod
    def get_month_data(cls, user_id: int, month_date: date) -> dict:
        row: AmbassadorDashboardMonthlyRefer = AmbassadorDashboardMonthlyRefer.query.filter(
            AmbassadorDashboardMonthlyRefer.user_id == user_id,
            AmbassadorDashboardMonthlyRefer.report_date == month_date,
        ).with_entities(
            AmbassadorDashboardMonthlyRefer.spot_trade_usd,
            AmbassadorDashboardMonthlyRefer.perpetual_trade_usd,
            AmbassadorDashboardMonthlyRefer.spot_refer_fee_usd,
            AmbassadorDashboardMonthlyRefer.perpetual_refer_fee_usd,
            AmbassadorDashboardMonthlyRefer.spot_refer_amounts,
            AmbassadorDashboardMonthlyRefer.perpetual_refer_amounts,
        ).first()
        month_data = {
            "month_trade_usd": row.spot_trade_usd + row.perpetual_trade_usd if row else Decimal(),
            "month_refer_fee_usd": row.spot_refer_fee_usd + row.perpetual_refer_fee_usd if row else Decimal(),
            "month_refer_amounts": cls.load_and_merge_asset_dict(row.spot_refer_amounts, row.perpetual_refer_amounts) if row else [],
        }
        return month_data

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """大使看板-数据总览"""
        user_id = g.user.id
        cls.require_is_amb(user_id)
        board: AmbassadorDashboard = AmbassadorDashboard.query.filter(
            AmbassadorDashboard.user_id == user_id,
        ).first()
        result = {
            "refer_user_count": board.refer_user_count if board else 0,
            "deposit_user_count": board.deposit_user_count if board else 0,
            "trade_user_count": board.trade_user_count if board else 0,
            "trade_usd": board.spot_trade_usd + board.perpetual_trade_usd if board else Decimal(),
            "refer_fee_usd": board.spot_refer_fee_usd + board.perpetual_refer_fee_usd if board else Decimal(),
            "refer_amounts": cls.load_and_merge_asset_dict(board.spot_refer_amounts, board.perpetual_refer_amounts) if board else [],
            "last_update_at": max(board.last_update_at, board.updated_at) if board else None,
        }
        month_data = cls.get_month_data(user_id, this_month())
        result.update(month_data)
        return result


@ns.route('/ambassador/dashboard/user-series')
@respond_with_code
class AmbDashboardUserDataSeriesResource(Resource):

    @classmethod
    def parse_start_end_time(cls, time_type):
        _today = today()
        if time_type == '7d':
            start_time, end_time = _today - timedelta(days=7), _today
        elif time_type == '30d':
            start_time, end_time = _today - timedelta(days=30), _today
        elif time_type == '90d':
            start_time, end_time = _today - timedelta(days=90), _today
        elif time_type == '365d':
            start_time, end_time = _today - timedelta(days=365), _today
        else:
            raise InvalidArgument
        return start_time, end_time

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            time_type=EnumField(enum=['7d', '30d', '90d', '365d'], required=True)
        )
    )
    def get(cls, **kwargs):
        """大使看板-周期内用户数据"""
        user_id = g.user.id
        AmbDashboardSummaryResource.require_is_amb(user_id)

        time_type = kwargs['time_type']
        start_time, end_time = cls.parse_start_end_time(time_type)

        model = AmbassadorDashboardDailyRefer
        rows: list[model] = model.query.filter(
            model.user_id == user_id,
            model.report_date >= start_time,
            model.report_date < end_time,
        ).order_by(
            model.report_date.asc()
        ).with_entities(
            model.report_date,
            model.inc_refer_user_count,
            model.deposit_user_count,
            model.trade_user_count,
        ).all()
        return [[i.report_date, i.inc_refer_user_count, i.deposit_user_count, i.trade_user_count] for i in rows]


@ns.route('/ambassador/dashboard/trade-series')
@respond_with_code
class AmbDashboardTradeDataSeriesResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            time_type=EnumField(enum=['7d', '30d', '90d', '365d'], required=True)
        )
    )
    def get(cls, **kwargs):
        """大使看板-周期内用户数据"""
        user_id = g.user.id
        AmbDashboardSummaryResource.require_is_amb(user_id)

        time_type = kwargs['time_type']
        start_time, end_time = AmbDashboardUserDataSeriesResource.parse_start_end_time(time_type)

        model = AmbassadorDashboardDailyRefer
        rows: list[model] = model.query.filter(
            model.user_id == user_id,
            model.report_date >= start_time,
            model.report_date < end_time,
        ).order_by(
            model.report_date.asc()
        ).with_entities(
            model.report_date,
            model.spot_trade_usd,
            model.perpetual_trade_usd,
            model.spot_refer_fee_usd,
            model.perpetual_refer_fee_usd,
            model.spot_refer_usd,
            model.perpetual_refer_usd,
        ).all()
        return [
            [
                i.report_date,
                i.spot_trade_usd + i.perpetual_trade_usd,
                i.spot_refer_fee_usd + i.perpetual_refer_fee_usd,
                i.spot_refer_usd + i.perpetual_refer_usd,
            ]
            for i in rows
        ]


@ns.route('/ambassador/dashboard/refer-history')
@respond_with_code
class AmbDashboardReferralHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
            code=wa_fields.String,
            account_name=wa_fields.String,
            start_date=TimestampField(),
            end_date=TimestampField(),
            export=wa_fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """大使看板-邀请记录"""
        user_id = g.user.id
        AmbDashboardSummaryResource.require_is_amb(user_id)

        page, limit, export = kwargs['page'], kwargs['limit'], kwargs['export']

        query = ReferralHistory.query.filter(
            ReferralHistory.referrer_id == user_id,
        ).order_by(ReferralHistory.id.desc())
        if code := kwargs.get("code"):
            ref_code = Referral.query.filter(
                Referral.user_id == user_id,
                Referral.code == code,
            ).with_entities(Referral.id).first()
            if not ref_code:
                raise InvalidArgument
            query = query.filter(ReferralHistory.referral_id == ref_code.id)
        if account_name := kwargs.get("account_name"):
            extra = UserExtra.query.filter(
                UserExtra.account_name == account_name,
            ).with_entities(UserExtra.user_id).first()
            if not extra:
                return dict(
                    has_next=False,
                    curr_page=1,
                    count=0,
                    data=[],
                    total=0,
                    total_page=1,
                )
            query = query.filter(ReferralHistory.referree_id == extra.user_id)
        if start_date := kwargs.get("start_date"):
            query = query.filter(ReferralHistory.created_at >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(ReferralHistory.created_at <= end_date)

        pagination = query.paginate(
            page if not export else 1,
            limit if not export else config['EXPORT_ITEM_MAX_COUNT'],
            error_out=False,
        )

        his_rows = pagination.items
        ee_ids = [i.referree_id for i in his_rows]
        ee_ref_extra_map = {}
        ee_user_extra_map = {}
        for chunk_ee_ids in batch_iter(ee_ids, 2000):
            ch_ee_ref_extra_rows = AmbassadorReferralHistoryExtra.query.filter(
                AmbassadorReferralHistoryExtra.referree_id.in_(chunk_ee_ids),
            ).with_entities(
                AmbassadorReferralHistoryExtra.referree_id,
                AmbassadorReferralHistoryExtra.first_deposit_at,
                AmbassadorReferralHistoryExtra.first_deposit_usd,
                AmbassadorReferralHistoryExtra.first_trade_at,
                AmbassadorReferralHistoryExtra.first_trade_usd,
                AmbassadorReferralHistoryExtra.deposit_usd,
                AmbassadorReferralHistoryExtra.spot_trade_usd,
                AmbassadorReferralHistoryExtra.perpetual_trade_usd,
                AmbassadorReferralHistoryExtra.spot_refer_fee_usd,
                AmbassadorReferralHistoryExtra.perpetual_refer_fee_usd,
                AmbassadorReferralHistoryExtra.spot_refer_amounts,
                AmbassadorReferralHistoryExtra.perpetual_refer_amounts,
            ).all()
            ee_ref_extra_map.update({i.referree_id: i for i in ch_ee_ref_extra_rows})
            ch_ee_user_extra_rows = UserExtra.query.filter(
                UserExtra.user_id.in_(chunk_ee_ids),
            ).with_entities(
                UserExtra.user_id,
                UserExtra.account_name,
            ).all()
            ee_user_extra_map.update({i.user_id: i for i in ch_ee_user_extra_rows})

        res_items = []
        for i in his_rows:
            ee_ref_extra: AmbassadorReferralHistoryExtra = ee_ref_extra_map.get(i.referree_id)
            ee_user_extra: UserExtra = ee_user_extra_map.get(i.referree_id)
            r_item = {
                "account_name": ee_user_extra.account_name if ee_user_extra else "",
                "time": int(i.created_at.timestamp()),
                "status": i.status.name,
                "first_deposit_at": ee_ref_extra.first_deposit_at.timestamp() if ee_ref_extra and ee_ref_extra.first_deposit_at else None,
                "first_deposit_usd": ee_ref_extra.first_deposit_usd if ee_ref_extra and ee_ref_extra.first_deposit_usd else Decimal(),
                "deposit_usd": ee_ref_extra.deposit_usd if ee_ref_extra else Decimal(),
                "first_trade_at": ee_ref_extra.first_trade_at.timestamp() if ee_ref_extra and ee_ref_extra.first_trade_at else None,
                "first_trade_usd": ee_ref_extra.first_trade_usd if ee_ref_extra and ee_ref_extra.first_trade_usd else Decimal(),
                "spot_trade_usd": ee_ref_extra.spot_trade_usd if ee_ref_extra else Decimal(),
                "perpetual_trade_usd": ee_ref_extra.perpetual_trade_usd if ee_ref_extra else Decimal(),
                "spot_refer_fee_usd": ee_ref_extra.spot_refer_fee_usd if ee_ref_extra else Decimal(),
                "perpetual_refer_fee_usd": ee_ref_extra.perpetual_refer_fee_usd if ee_ref_extra else Decimal(),
                "spot_refer_amounts": AmbDashboardSummaryResource.load_and_merge_asset_dict(ee_ref_extra.spot_refer_amounts)
                if ee_ref_extra else [],
                "perpetual_refer_amounts": AmbDashboardSummaryResource.load_and_merge_asset_dict(ee_ref_extra.perpetual_refer_amounts)
                if ee_ref_extra else [],
            }
            res_items.append(r_item)

        if export:
            pref = UserPreferences(user_id)
            lang = get_request_language()
            with force_locale(lang.value):
                fields = [
                    'account_name', 'time', 'first_deposit_at', 'first_deposit_usd', 'deposit_usd', 'first_trade_at',
                    'first_trade_usd', 'spot_trade_usd', 'perpetual_trade_usd', 'spot_refer_fee_usd', 'perpetual_refer_fee_usd',
                    'spot_refer_amounts', 'perpetual_refer_amounts', 'status',
                ]
                headers = [
                    _('账户名'), _('绑定时间'), _('首次入金时间'), _('首次入金金额（USD）'), _('总入金金额（USD）'), _('首次交易时间'),
                    _("首次交易金额（USD）"), _('总现货交易金额（USD）'), _("总合约交易金额（USD）"),
                    _('总现货手续费（USD）'), _('总合约手续费（USD）'),
                    _("总现货返佣"), _('总合约返佣'), _("状态"),
                ]
                dt_to_str = partial(datetime_to_str, offset_minutes=pref.timezone_offset)
                for h in res_items:
                    h['account_name'] = f"@{h['account_name']}"
                    h['time'] = dt_to_str(timestamp_to_datetime(h['time']))
                    h['first_deposit_at'] = dt_to_str(timestamp_to_datetime(h['first_deposit_at'])) if h['first_deposit_at'] else ''
                    h['first_deposit_usd'] = amount_to_str(h["first_deposit_usd"]) if h["first_deposit_at"] else ''
                    h['deposit_usd'] = amount_to_str(h["deposit_usd"])
                    h['first_trade_at'] = dt_to_str(timestamp_to_datetime(h['first_trade_at'])) if h['first_trade_at'] else ''
                    h['first_trade_usd'] = amount_to_str(h["first_trade_usd"]) if h["first_trade_at"] else ''
                    h['spot_trade_usd'] = amount_to_str(h["spot_trade_usd"])
                    h['perpetual_trade_usd'] = amount_to_str(h["perpetual_trade_usd"])
                    h['spot_refer_fee_usd'] = amount_to_str(h["spot_refer_fee_usd"])
                    h['perpetual_refer_fee_usd'] = amount_to_str(h["perpetual_refer_fee_usd"])
                    h['spot_refer_amounts'] = '\n'.join([f'{amount_to_str(i["amount"])} {i["asset"]}' for i in h['spot_refer_amounts']])
                    h['perpetual_refer_amounts'] = '\n'.join([f'{amount_to_str(i["amount"])} {i["asset"]}'
                                                              for i in h['perpetual_refer_amounts']])
                    h['status'] = _("生效中") if h['status'] == ReferralHistory.Status.VALID.name else _("已失效")
                stream = ExcelExporter(
                    data_list=res_items,
                    headers=headers,
                    fields=fields,
                ).export_streams()

                return send_file(
                    stream,
                    download_name='ambassador_dashboard_referral_history.xlsx',
                    as_attachment=True,
                )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(res_items),
            data=res_items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route('/ambassador/dashboard/asset-history')
@respond_with_code
class AmbDashboardAssetHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, default=ReportType.DAILY),
            start_date=TimestampField(),
            end_date=TimestampField(),
            page=PageField,
            limit=LimitField,
            export=wa_fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """大使看板-返佣记录"""
        user_id = g.user.id
        AmbDashboardSummaryResource.require_is_amb(user_id)

        page, limit, export = kwargs['page'], kwargs['limit'], kwargs.get('export')
        is_monthly = kwargs.get("report_type") == ReportType.MONTHLY
        if is_monthly:
            model = AmbassadorDashboardMonthlyRefer
        else:
            model = AmbassadorDashboardDailyRefer

        query = model.query.filter(
            model.user_id == user_id,
            model.trade_user_count > 0,
        ).order_by(model.report_date.desc())
        if start_date := kwargs.get("start_date"):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(model.report_date <= end_date)

        pagination = query.paginate(
            page if not export else 1,
            limit if not export else config['EXPORT_ITEM_MAX_COUNT'],
            error_out=False,
        )
        rows = pagination.items

        res_items = []
        for row in rows:
            row: AmbassadorDashboardDailyRefer
            r_item = {
                "time": row.report_date,
                "trade_user_count": row.trade_user_count,
                "spot_trade_usd": row.spot_trade_usd,
                "perpetual_trade_usd": row.perpetual_trade_usd,
                "spot_refer_fee_usd": row.spot_refer_fee_usd,
                "perpetual_refer_fee_usd": row.perpetual_refer_fee_usd,
                "refer_amounts": AmbDashboardSummaryResource.load_and_merge_asset_dict(row.spot_refer_amounts, row.perpetual_refer_amounts),
            }
            res_items.append(r_item)

        if kwargs.get("export"):
            lang = get_request_language()
            with force_locale(lang.value):
                fields = [
                    'time', 'refer_amounts', 'trade_user_count', 'spot_trade_usd', 'perpetual_trade_usd', 'total_trade_usd',
                    'spot_refer_fee_usd', 'perpetual_refer_fee_usd', 'total_refer_fee_usd',
                ]
                headers = [
                    _('日期'), _('总返佣金额'), _('交易用户数'), _('现货交易额（USD）'), _('合约交易额（USD）'), _('总交易额（USD）'),
                    _("现货手续费（USD）"), _('合约手续费（USD）'), _("总手续费（USD）"),
                ]
                for r in res_items:
                    r['time'] = r['time'].strftime("%Y-%m-%d")
                    r['total_trade_usd'] = amount_to_str(r["spot_trade_usd"] + r["perpetual_trade_usd"])
                    r['spot_trade_usd'] = amount_to_str(r["spot_trade_usd"])
                    r['perpetual_trade_usd'] = amount_to_str(r["perpetual_trade_usd"])
                    r['total_refer_fee_usd'] = amount_to_str(r["spot_refer_fee_usd"] + r["perpetual_refer_fee_usd"])
                    r['spot_refer_fee_usd'] = amount_to_str(r["spot_refer_fee_usd"])
                    r['perpetual_refer_fee_usd'] = amount_to_str(r["perpetual_refer_fee_usd"])
                    r['refer_amounts'] = '\n'.join([f'{amount_to_str(i["amount"])} {i["asset"]}' for i in r['refer_amounts']])
                stream = ExcelExporter(
                    data_list=res_items,
                    headers=headers,
                    fields=fields,
                ).export_streams()

                return send_file(
                    stream,
                    download_name='ambassador_dashboard_referral_asset_history.xlsx',
                    as_attachment=True,
                )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(res_items),
            data=res_items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route('/ambassador/dashboard/asset-detail')
@respond_with_code
class AmbDashboardAssetDetailResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, default=ReportType.DAILY),
            start_date=TimestampField(),
            end_date=TimestampField(),
            account_name=wa_fields.String,
            page=PageField,
            limit=LimitField,
            export=wa_fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """大使看板-返佣明细"""
        user_id = g.user.id
        AmbDashboardSummaryResource.require_is_amb(user_id)

        page, limit, export = kwargs['page'], kwargs['limit'], kwargs['export']
        is_monthly = kwargs.get("report_type") == ReportType.MONTHLY
        if is_monthly:
            model = AmbassadorDashboardMonthlyReferDetail
        else:
            model = AmbassadorDashboardDailyReferDetail

        query = model.query.filter(
            model.user_id == user_id,
        ).order_by(model.report_date.desc())
        if start_date := kwargs.get("start_date"):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(model.report_date <= end_date)
        if account_name := kwargs.get("account_name"):
            extra = UserExtra.query.filter(
                UserExtra.account_name == account_name,
            ).with_entities(UserExtra.user_id).first()
            if not extra:
                return dict(
                    has_next=False,
                    curr_page=1,
                    count=0,
                    data=[],
                    total=0,
                    total_page=1,
                )
            query = query.filter(model.referree_id == extra.user_id)

        pagination = query.paginate(
            page if not export else 1,
            limit if not export else config['EXPORT_ITEM_MAX_COUNT'],
            error_out=False,
        )
        rows = pagination.items

        ee_ids = [i.referree_id for i in rows]
        ee_user_extra_map = {}
        for chunk_ee_ids in batch_iter(ee_ids, 2000):
            ch_ee_user_extra_rows = UserExtra.query.filter(
                UserExtra.user_id.in_(chunk_ee_ids),
            ).with_entities(
                UserExtra.user_id,
                UserExtra.account_name,
            ).all()
            ee_user_extra_map.update({i.user_id: i for i in ch_ee_user_extra_rows})

        res_items = []
        for row in rows:
            row: AmbassadorDashboardDailyReferDetail
            ee_user_extra: UserExtra = ee_user_extra_map.get(row.referree_id)
            if is_monthly:
                row: AmbassadorDashboardMonthlyReferDetail
                refer_amounts = AmbDashboardSummaryResource.load_and_merge_asset_dict(row.spot_refer_amounts, row.perpetual_refer_amounts)
            else:
                refer_amounts = [{"asset": row.refer_asset, "amount": row.spot_refer_amount + row.perpetual_refer_amount}]

            r_item = {
                "time": row.report_date,
                "account_name": ee_user_extra.account_name if ee_user_extra else "",
                "spot_trade_usd": row.spot_trade_usd,
                "perpetual_trade_usd": row.perpetual_trade_usd,
                "spot_refer_fee_usd": row.spot_refer_fee_usd,
                "perpetual_refer_fee_usd": row.perpetual_refer_fee_usd,
                "refer_amounts": refer_amounts,
            }
            res_items.append(r_item)

        if kwargs.get("export"):
            lang = get_request_language()
            with force_locale(lang.value):
                fields = [
                    'time', 'account_name', 'refer_amounts', 'spot_trade_usd', 'perpetual_trade_usd', 'total_trade_usd',
                    'spot_refer_fee_usd', 'perpetual_refer_fee_usd', 'total_refer_fee_usd',
                ]
                headers = [
                    _('日期'), _('账户名'), _('总返佣金额'), _('现货交易额（USD）'), _('合约交易额（USD）'), _('总交易额（USD）'),
                    _("现货手续费（USD）"), _('合约手续费（USD）'), _("总手续费（USD）"),
                ]
                for r in res_items:
                    r['time'] = r['time'].strftime("%Y-%m-%d")
                    r['account_name'] = f"@{r['account_name']}"
                    r['total_trade_usd'] = amount_to_str(r["spot_trade_usd"] + r["perpetual_trade_usd"])
                    r['total_refer_fee_usd'] = amount_to_str(r["spot_refer_fee_usd"] + r["perpetual_refer_fee_usd"])
                    r['spot_trade_usd'] = amount_to_str(r["spot_trade_usd"])
                    r['spot_refer_fee_usd'] = amount_to_str(r["spot_refer_fee_usd"])
                    r['perpetual_trade_usd'] = amount_to_str(r["perpetual_trade_usd"])
                    r['perpetual_refer_fee_usd'] = amount_to_str(r["perpetual_refer_fee_usd"])
                    r['refer_amounts'] = '\n'.join([f'{amount_to_str(i["amount"])} {i["asset"]}' for i in r['refer_amounts']])
                stream = ExcelExporter(
                    data_list=res_items,
                    headers=headers,
                    fields=fields,
                ).export_streams()

                return send_file(
                    stream,
                    download_name='ambassador_dashboard_referral_asset_detail.xlsx',
                    as_attachment=True,
                )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(res_items),
            data=res_items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route('/ambassador/dashboard/market-data')
@respond_with_code
class AmbDashboardMarketDataResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            time_type=EnumField(enum=['7d', '30d', '90d', '365d'], required=True),
        )
    )
    def get(cls, **kwargs):
        """大使看板-市场交易数据"""
        user_id = g.user.id
        AmbDashboardSummaryResource.require_is_amb(user_id)

        model = AmbassadorDashboardTradeMarket
        time_type_map = {
            '7d': model.TimeRangeEnum.DAY7,
            '30d': model.TimeRangeEnum.DAY30,
            '90d': model.TimeRangeEnum.DAY90,
            '365d': model.TimeRangeEnum.DAY365,
        }
        range_type = time_type_map.get(kwargs['time_type'])
        if not range_type:
            raise InvalidArgument

        row: model = model.query.filter(
            model.user_id == user_id,
            model.time_range == range_type,
        ).with_entities(
            model.market_data,
        ).first()

        data = json.loads(row.market_data) if row.market_data else {}
        result = {
            "spot_trade_usd": data.get("spot_trade_usd", []),
            "spot_user_count": data.get("spot_user_count", []),
            "perpetual_trade_usd": data.get("perpetual_trade_usd", []),
            "perpetual_user_count": data.get("perpetual_user_count", []),
        }
        return result



@ns.route('/ambassador/packages')
@respond_with_code
class AmbassadorPackageResource(Resource):
    class Statuses(Enum):
        ALL = 'all'
        RUNNING = 'running'
        FINISHED = 'finished'

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField(),
            limit=LimitField(missing=10),
            status=EnumField(Statuses, required=True)
        )
    )
    def get(cls, **kwargs):
        """大使激励包-列表"""
        page, limit = kwargs['page'], kwargs['limit']
        user_id = g.user.id
        query = UserAmbassadorPackage.query.filter(
            UserAmbassadorPackage.user_id == user_id,
        ).order_by(UserAmbassadorPackage.id.desc())
        if kwargs['status'] == cls.Statuses.RUNNING:
            statuses = [UserAmbassadorPackage.Status.ACTIVE, UserAmbassadorPackage.Status.PENDING]
        elif kwargs['status'] == cls.Statuses.FINISHED:
            statuses = [UserAmbassadorPackage.Status.STOPPED, UserAmbassadorPackage.Status.FINISHED, 
                        UserAmbassadorPackage.Status.INVALIDATED]
        elif kwargs['status'] == cls.Statuses.ALL:
            statuses = [UserAmbassadorPackage.Status.ACTIVE, UserAmbassadorPackage.Status.PENDING,
                        UserAmbassadorPackage.Status.STOPPED, UserAmbassadorPackage.Status.FINISHED, 
                        UserAmbassadorPackage.Status.INVALIDATED]
        else:
            raise InvalidArgument
        items = query.filter(UserAmbassadorPackage.status.in_(statuses)).all()

        batch_ids = [item.batch_id for item in items]
        batches = AmbassadorPackageBatch.query.filter(
            AmbassadorPackageBatch.id.in_(batch_ids)
        ).all()
        batch_dict = {batch.id: batch for batch in batches}
        filtered_items = cls.filter_items(items, batch_dict)
        records = filtered_items[(page-1)*limit: page*limit]
        has_next = True if page * limit < len(filtered_items) else False
        res = []
        for item in records:
            batch = batch_dict[item.batch_id]
            total_period = batch.periods
            total_amount = batch.package_amount
            current_period = item.current_period
            each_period_amount = safe_div(batch.package_amount, batch.periods)
            released_periods = item.released_periods
            if item.status in (UserAmbassadorPackage.Status.STOPPED,
                               UserAmbassadorPackage.Status.FINISHED,
                               UserAmbassadorPackage.Status.INVALIDATED,
                               ):
                remain_amount = Decimal()
                remain_period = 0
            else:
                remain_amount = amount_to_str(total_amount - released_periods * each_period_amount, 2)
                remain_period = batch.periods - released_periods
            res.append({
                'id': item.id,
                'batch_id': item.batch_id,
                'batch_periods': total_period,
                'package_amount': amount_to_str(batch.package_amount, 2),   
                'asset': batch.asset,
                'release_time': batch.actural_release_time,  # 激励包发放时间（用于生成日期标题）
                'valid_days': batch.valid_days,
                'refer_trade_users': batch.refer_trade_users,   # 激励包批次要求的邀请交易用户数
                'refer_trade_amount': amount_to_str(batch.refer_trade_amount, 2),   # 激励包批次要求的邀请交易额
                'claim_time': item.claim_time,
                'status': item.status.name,
                'current_period': current_period,
                'remain_period': remain_period,
                'remain_amount': remain_amount,   # 剩余激励包金额
                'total_released_amount': amount_to_str(item.total_released_amount, 2),   # 已发放的激励包金额
                'each_period_amount': amount_to_str(each_period_amount, 2),   # 本期激励包金额
                'current_refer_users': item.current_refer_users,   # 当期统计的邀请交易用户数
                'current_refer_amount': amount_to_str(item.current_refer_amount, 2),   # 当期统计的邀请交易额
                'current_period_start_time': item.current_period_start_time,   # 当期统计开始时间
                'current_period_end_time': item.current_period_end_time,   # 当期统计结束时间
                'current_package_release_at': item.current_package_release_at,   # 当期激励包发放时间
            })
        
        return dict(
            has_next=has_next,
            curr_page=page,
            count=len(res),
            data=res,
            total=len(filtered_items),
            is_ambassador=ReferralRepository.is_ambassador(user_id),
        )

    @classmethod
    def filter_items(cls, items, batch_dict):
        res = []
        for item in items:
            batch = batch_dict[item.batch_id]
            status = item.status
            if status == UserAmbassadorPackage.Status.PENDING:
                expired_time = batch.actural_release_time + datetime.timedelta(days=batch.valid_days)
                if expired_time < now():    # 激励包已过期，不显示
                    continue
            if status == UserAmbassadorPackage.Status.STOPPED and not item.claim_time:
                continue
            res.append(item)
        return res


@ns.route('/ambassador/package/<int:package_id>')
@respond_with_code
class AmbassadorPackageDetailResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls, package_id):
        """大使激励包-领取"""
        user_id = g.user.id
        with CacheLock(LockKeys.ambassador_package(package_id), wait=False):
            db.session.rollback()
            normal_ambs, business_ambs = ReferralRepository.get_ambassador_ids([user_id])
            if user_id not in normal_ambs and user_id not in business_ambs:
                raise UserNotAmbassador
            package = UserAmbassadorPackage.query.filter(
                UserAmbassadorPackage.id == package_id,
                UserAmbassadorPackage.user_id == user_id,
            ).first()
            if not package or package.status != UserAmbassadorPackage.Status.PENDING:
                raise InvalidArgument
            batch = AmbassadorPackageBatch.query.filter(
                AmbassadorPackageBatch.id == package.batch_id,
                AmbassadorPackageBatch.status == AmbassadorPackageBatch.Status.RELEASED,
            ).first()
            if not batch:
                raise InvalidArgument
            expired_time = batch.actural_release_time + datetime.timedelta(days=batch.valid_days)
            if expired_time < now():
                raise InvalidArgument(message=_('激励包已过期，无法领取'))
            current_period_start_time = batch.actural_release_time.date()
            current_period_end_time = package.first_release_time - datetime.timedelta(days=1)
            package.current_period_start_time = current_period_start_time
            package.current_period_end_time = current_period_end_time
            package.current_package_release_at = package.first_release_time
            package.current_period_daily_stat_done_at = batch.actural_release_time  # 激励包更新时间取激励包批次实际发放时间
            package.claim_time = now()
            package.current_period = 1
            package.status = UserAmbassadorPackage.Status.ACTIVE
            if user_id in normal_ambs:
                package.ambassador_type = AmbassadorType.NORMAL
            else:
                package.ambassador_type = AmbassadorType.BUSINESS
            db.session.commit()
            return dict(first_release_time=package.first_release_time)


@ns.route('/ambassador/package/settle-names')
@respond_with_code
class AmbassadorPackageNamesResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, **kwargs):
        """大使激励包-名称列表"""
        user_id = g.user.id
        packages = UserAmbassadorPackage.query.filter(
            UserAmbassadorPackage.user_id == user_id,
            UserAmbassadorPackage.status.in_(
                [UserAmbassadorPackage.Status.STOPPED,
                 UserAmbassadorPackage.Status.FINISHED,
                 UserAmbassadorPackage.Status.INVALIDATED,
                 UserAmbassadorPackage.Status.ACTIVE,
                 ]
            ),
        ).with_entities(
            UserAmbassadorPackage.batch_id,
            UserAmbassadorPackage.status,
            UserAmbassadorPackage.claim_time,
        ).all()
        batch_ids = set()
        for item in packages:
            if item.status == UserAmbassadorPackage.Status.STOPPED and not item.claim_time:
                continue
            batch_ids.add(item.batch_id)
                
        batches = AmbassadorPackageBatch.query.filter(
            AmbassadorPackageBatch.id.in_(batch_ids),
        ).with_entities(
            AmbassadorPackageBatch.actural_release_time,
        ).order_by(AmbassadorPackageBatch.actural_release_time.desc())
        res = []
        for batch in batches:
            actural_release_time = batch.actural_release_time
            if not actural_release_time:
                continue
            name = actural_release_time.strftime("%Y%m%d")
            if name in res:
                continue
            res.append(name)
        return dict(
            names=res,
        )

        

@ns.route('/ambassador/package/settlement/records')
@respond_with_code
class AmbassadorPackageSettlementResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField(),
            limit=LimitField(missing=10),
            name=wa_fields.String(required=False),
            start_time=TimestampField(required=False),
            end_time=TimestampField(required=False)
        )
    )
    def get(cls, **kwargs):
        """大使激励包-结算记录"""
        user_id = g.user.id
        query = PackageSettlementHistory.query.filter(
            PackageSettlementHistory.user_id == user_id,
        ).order_by(PackageSettlementHistory.id.desc())
        if batch_name := kwargs.get('name'):
            query = query.filter(PackageSettlementHistory.batch_name == batch_name)
        if start_time := kwargs.get('start_time'):
            query = query.filter(PackageSettlementHistory.settlement_time >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(PackageSettlementHistory.settlement_time <= end_time)
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        total = pagination.total
        items = pagination.items
        res = []
        for item in items:
            res.append({
                'batch_name': item.batch_name,
                'settlement_time': item.settlement_time,
                'period': item.period,
                'refer_users': item.refer_users,
                'refer_amount': amount_to_str(item.refer_amount, 2),
                'is_ambassador': item.is_ambassador,
                'is_settled': item.is_settled,
                'asset': item.asset,
                'settled_amount': amount_to_str(item.settled_amount, 2),
            })
        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(res),
            data=res,
            total=total,
        )
    