from typing import Union

from webargs import fields as wa_fields
from flask import g

from ..common import Namespace, Resource, respond_with_code, require_login
from ...models import File

ns = Namespace('File')
url_prefix = '/file'


def max_length(size=30):
    def validate(s: Union[str, list]):
        return len(s) <= size

    return validate


@ns.route('/urls')
@respond_with_code
class FileUrlsResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        file_ids=wa_fields.List(wa_fields.Integer(), required=True, validate=max_length(30)),
    ))
    def get(cls, **kwargs):
        files = File.query.filter(
            File.user_id == g.user.id,
            File.id.in_(kwargs['file_ids'])
        ).all()
        return {file.id: file.private_url for file in files}
