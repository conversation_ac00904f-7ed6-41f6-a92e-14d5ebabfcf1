# -*- coding: utf-8 -*-
from collections import named<PERSON>ple
from decimal import Decimal
from typing import Tuple, Optional, Dict

from marshmallow import Schema

from app.common.events import PerpetualEvent

from flask import g, current_app
from flask_restx import fields, marshal
from webargs import fields as wa_fields

from app.business import (
    PerpetualServerClient, <PERSON>acheLock, LockKeys, PerpetualHistoryDB,
    ORDER_BOTH_SIDE, position_target_map, TakeProfitStopLossPriceType,
)
from app.business.order import VerifyPriceTool, Order
from app.business.clients.biz_monitor import biz_monitor
from app.business.fee_constant import (
    DEFAULT_MIN_CONTRACT_TAKER_FEE,
    DEFAULT_MIN_CONTRACT_MAKER_FEE,
)
from app.business.fee import FeeFetcher
from app.business.user import require_user_not_only_withdrawal, require_user_kyc
from app.caches import PerpetualMarketCache, PropTradingOpUsersCache
from app.common import (
    TradeType, OrderBusinessType,
    OrderSideType, OrderType, OrderStatusType, StopOrderStatusIntType,
    Language,
    OrderIntType, PERPETUAL_ONLY_MAKER_VALUE, PERPETUAL_HIDE_VALUE,
    PerpetualMarketType,
    TradeBusinessType, StopOrderIntType, PrecisionEnum, PERPETUAL_ALL_MARKET_TYPE,
    SubAccountPermission, PositionDealIntType, StopOrderType,
)
from app.exceptions import InvalidArgument, StopOrderAmountLimitExceeded, PerpetualResponseCode, \
    TwoFactorAuthenticationRequired, PerpetualOrderExceptionMap
from app.utils import offset_to_page, amount_to_str, export_xlsx, batch_iter
from app.config import config
from ...common import Resource, Namespace, respond_with_code, ex_fields, \
    require_login, success
from ...common.decorators import trade_permission_validate, require_user_permission, \
    copy_trading_sub_user_setter, require_trade_password
from ...common.fields import PositiveDecimalField, IntEnumField, EnumField
from ...common.request import RequestPlatform, require_ip_not_only_withdrawal
from flask_babel import gettext as _

ns = Namespace('Perpetual')


def get_fee_asset_and_rate(user_id: int) -> Tuple[Optional[str], Optional[str]]:
    return None, None


def fetch_cal_fee(user_id: int, market: str) -> Dict[TradeType, Decimal]:
    """
    获取用户在合约市场的费率
    """
    fee_result = FeeFetcher(user_id).fetch(TradeBusinessType.PERPETUAL, market)
    return {
        TradeType.MAKER:
            max(fee_result[TradeType.MAKER],
                DEFAULT_MIN_CONTRACT_MAKER_FEE),
        TradeType.TAKER:
            max(fee_result[TradeType.TAKER],
                DEFAULT_MIN_CONTRACT_TAKER_FEE),
    }


def get_user_position_data(user_id: int, market: str, position_id: int) -> Dict:
    s = PerpetualServerClient()
    result = s.position_pending(user_id, market)
    if len(result) != 1:
        raise InvalidArgument
    if result[0]["position_id"] != position_id:
        raise InvalidArgument
    return result[0]


def get_user_close_order_side(user_id: int, market: str, position_id: int) -> OrderSideType:
    r = get_user_position_data(user_id, market, position_id)
    position_side = OrderSideType(r["side"])
    return OrderSideType.SELL if position_side == OrderSideType.BUY else OrderSideType.BUY


def get_user_close_market_order_amount(user_id: int, market: str, position_id: int) -> Decimal:
    r = get_user_position_data(user_id, market, position_id)
    return Decimal(r["close_left"])


CancelOrderData = namedtuple('MarketCoinData',
                             ['list_method', 'cancel_batch_method',
                              'cancel_market_method', 'cancel_one_method'])

LimitCancelOrderData = CancelOrderData(
    list_method='pending_order',
    cancel_batch_method='cancel_batch_orders',
    cancel_one_method='cancel_order',
    cancel_market_method='cancel_all'
)

StopLimitCancelOrderData = CancelOrderData(
    list_method='pending_stop',
    cancel_batch_method='cancel_batch_stop_orders',
    cancel_one_method='cancel_stop',
    cancel_market_method='cancel_stop_all'
)


class CancelOrderTool(object):
    MAX_CANCEL_ORDER_COUNT = 1000
    ORDER_TYPE_DATA_MAPPING = {
        "limit": LimitCancelOrderData,
        "stop_limit": StopLimitCancelOrderData
    }

    def __init__(self, order_type: str, user_id: int, market: Optional[str],
                 side: Optional[OrderSideType]):
        self.order_type = order_type
        self.order_type_data = self.ORDER_TYPE_DATA_MAPPING[order_type]
        self.user_id = user_id
        self.market = market
        self.client = PerpetualServerClient()
        self.side = side or ORDER_BOTH_SIDE

    def get_all_orders_data(self):
        result = getattr(self.client, self.order_type_data.list_method)(
            user_id=self.user_id,
            market=self.market,
            page=1,
            limit=self.MAX_CANCEL_ORDER_COUNT,
            side=self.side)
        return [v['order_id'] for v in result["records"]]

    def cancel_all(self):
        if self.market is None:
            if self.side == ORDER_BOTH_SIDE:
                getattr(self.client, self.order_type_data.cancel_batch_method)(
                    user_id=self.user_id, market=self.market, order_ids=[]
                )
            else:
                be_cancelled_order_ids = self.get_all_orders_data()
                for _ids in batch_iter(be_cancelled_order_ids, 100):
                    getattr(self.client, self.order_type_data.cancel_batch_method)(
                        self.user_id, self.market, _ids)
        else:
            getattr(self.client, self.order_type_data.cancel_market_method)(
                user_id=self.user_id, market=self.market, side=self.side
            )

    def cancel_one(self, order_id):
        if not self.market:
            raise InvalidArgument
        getattr(self.client, self.order_type_data.cancel_one_method)(user_id=self.user_id,
                                                                     market=self.market,
                                                                     order_id=order_id)


class StopLossTakeProfitSchema(Schema):
    stop_loss_type = wa_fields.Integer(validate=lambda x: x in (TakeProfitStopLossPriceType.DEAL_PRICE,
                                                                TakeProfitStopLossPriceType.SIGN_PRICE))
    stop_loss_price = PositiveDecimalField()
    take_profit_type = wa_fields.Integer(validate=lambda x: x in (TakeProfitStopLossPriceType.DEAL_PRICE,
                                                                  TakeProfitStopLossPriceType.SIGN_PRICE))
    take_profit_price = PositiveDecimalField()


@ns.route('/limit')
class LimitOrderResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            side=wa_fields.String(
                required=True,
                validate=lambda x: x in [v.name.lower() for v in OrderSideType]
            ),
            amount=PositiveDecimalField(
                required=True,
            ),
            price=PositiveDecimalField(
                required=True,
            ),
            effect_type=wa_fields.Integer(
                required=True,
                validate=lambda x: x in (1, 2, 3, 4)
            ),
            hide=wa_fields.Boolean(missing=False, default=False, description='是否隐藏委托'),
            stop_loss_take_profit=wa_fields.Nested(StopLossTakeProfitSchema, missing=None)
        )
    )
    @require_user_permission(sub_account_permissions=[SubAccountPermission.PERPETUAL])
    @trade_permission_validate(is_spot=False)
    @copy_trading_sub_user_setter(require_running=True, check_args=["market"], check_count=True)
    @require_trade_password
    def put(cls, **kwargs):
        """
        限价下单
        """
        require_ip_not_only_withdrawal()
        require_user_not_only_withdrawal(g.user)
        require_user_kyc(g.user)
        user_id = g.user.id
        side = OrderSideType[kwargs['side'].upper()]
        market = kwargs['market']
        effect_type = kwargs['effect_type']
        hide = kwargs['hide']
        with CacheLock(
                LockKeys.perpetual_put_limit(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            if market_info['type'] == PerpetualMarketType.INVERSE.value:
                if kwargs['amount'] % 1 != 0:
                    raise InvalidArgument
            prec = int(market_info['money_prec'])
            price = amount_to_str(
                kwargs['price'], prec)
            tool = VerifyPriceTool(
                market,
                OrderBusinessType.PERPETUAL_BUSINESS_TYPE,
                OrderType.LIMIT_ORDER_TYPE,
                side,
                Decimal(kwargs['amount']),
                kwargs['price'],
                Decimal(),
            )
            tool.validate(g.user.main_user_type)

            fee_rate = fetch_cal_fee(user_id, market)
            fee_asset, fee_deduction_rate = get_fee_asset_and_rate(user_id)
            client = PerpetualServerClient(current_app.logger)
            option_effect_type = effect_type
            if effect_type == 4:
                if hide:
                    option_effect_type = ((
                                                      PERPETUAL_ONLY_MAKER_VALUE |
                                                      PERPETUAL_HIDE_VALUE) << 32) + 1
                else:
                    option_effect_type = (PERPETUAL_ONLY_MAKER_VALUE << 32) + 1
            else:
                if hide:
                    option_effect_type = (PERPETUAL_HIDE_VALUE << 32) + effect_type
            if stop_loss_take_profit := kwargs['stop_loss_take_profit']:
                if 'stop_loss_price' in stop_loss_take_profit:
                    stop_loss_take_profit['stop_loss_price'] = amount_to_str(stop_loss_take_profit['stop_loss_price'], prec)
                if 'take_profit_price' in stop_loss_take_profit:
                    stop_loss_take_profit['take_profit_price'] = amount_to_str(stop_loss_take_profit['take_profit_price'], prec)
            client.put_limit(
                user_id=user_id,
                market=market,
                side=side,
                amount=str(kwargs['amount']),
                price=price,
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8),
                source=RequestPlatform.from_request().value,
                fee_asset=fee_asset,
                fee_discount=fee_deduction_rate,
                effect_type=option_effect_type,
                stop_loss_take_profit=stop_loss_take_profit
            )

            cls.report_order_event(PerpetualEvent.LIMIT_ORDER_COUNT, user_id)

        return success(message=_("下单成功"))

    @classmethod
    def report_order_event(cls, event: PerpetualEvent, user_id: int):
        biz_monitor.increase_counter(
            PerpetualEvent.ORDER_COUNT,
            with_source=True
        )
        biz_monitor.increase_uniq_counter(
            PerpetualEvent.ORDER_NUM,
            value=[user_id],
            with_source=True
        )
        biz_monitor.increase_counter(
            event,
            with_source=True
        )

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(allow_none=True, missing=None),
        order_id=wa_fields.Integer(missing=''),
        type=IntEnumField(enum=OrderSideType,
                          example='sell',
                          description='order type, "sell" or "buy"',
                          missing=ORDER_BOTH_SIDE
                          )
    ))
    @copy_trading_sub_user_setter(require_running=False)
    @respond_with_code
    def delete(cls, **kwargs):
        """
        撤销普通成交
        """
        market = kwargs['market']
        order_id = kwargs['order_id']
        if market is not None and market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument
        side = kwargs.get('type', ORDER_BOTH_SIDE)
        user_id = g.user.id
        with CacheLock(
                LockKeys.perpetual_cancel_order(user_id),
                wait=False
        ):
            t = CancelOrderTool('limit', user_id, market, side)
            if order_id:
                return t.cancel_one(order_id)
            else:
                return t.cancel_all()

    marshal_fields = {
        'order_id': fields.Integer,
        'create_time': fields.Float,
        'side': fields.Integer,
        'effect_type': fields.Integer,
        'type': fields.Integer,
        'amount': fields.String,
        'price': fields.String,
        'value': ex_fields.PerpetualAmountField(
            attribute=lambda x: (x['market'], x['amount'], x['price'])),
        'left': fields.String,
        'fee_asset': fields.String,
        'deal_asset_fee': fields.String(
            attribute=lambda x: amount_to_str(x['deal_asset_fee'], 8)
        ),
        'deal_profit': fields.String(
            attribute=lambda x: amount_to_str(
                Decimal(x['deal_profit']) - Decimal(x['deal_fee'])
                if not x['fee_asset'] else Decimal(x['deal_profit']), 8)
        ),
        'market': fields.String,
        'already_deal': fields.String(
            attribute=lambda x: amount_to_str(
                Decimal(x['amount']) - Decimal(x['left']), 8)
        ),
        'average_price': ex_fields.PerpetualAveragePriceField(
            attribute=lambda x: (x['market'], x['amount'], x['left'], x['deal_stock'])
        ),
        'stop_loss_price': ex_fields.PriceField,
        'take_profit_price': ex_fields.PriceField,
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(missing=None),
        side=wa_fields.String(missing='',
                              validate=lambda x: x in ('', 'buy', 'sell')),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
    ))
    @copy_trading_sub_user_setter(require_running=False)
    @respond_with_code
    def get(cls, **kwargs):
        """
        获取普通成交订单信息
        """
        market = kwargs['market'] or None
        if market and not PerpetualMarketCache.is_market_exists(market):
            return dict(
                has_next=False,
                curr_page=1,
                count=0,
                data=[],
                total=0,
            )
        side = kwargs['side']
        side = OrderSideType[side.upper()] if side else 0
        client = PerpetualServerClient(current_app.logger)
        result = client.pending_order(
            g.user.id, market, side, kwargs['page'], kwargs['limit'])
        result['records'] = marshal(result['records'], cls.marshal_fields)
        return offset_to_page(result)


@ns.route('/market')
class MarketOrderResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            side=wa_fields.String(
                required=True,
                validate=lambda x: x in [v.name.lower() for v in OrderSideType]
            ),
            amount=PositiveDecimalField(
                required=True,
            ),
            stop_loss_take_profit=wa_fields.Nested(StopLossTakeProfitSchema, missing=None)
        )
    )
    @require_user_permission(sub_account_permissions=[SubAccountPermission.PERPETUAL])
    @trade_permission_validate(is_spot=False)
    @copy_trading_sub_user_setter(require_running=True, check_args=["market"], check_count=True)
    @require_trade_password
    def put(cls, **kwargs):
        """
        市价下单
        """
        require_ip_not_only_withdrawal()
        require_user_not_only_withdrawal(g.user)
        require_user_kyc(g.user)
        user_id = g.user.id
        side = OrderSideType[kwargs['side'].upper()]
        market = kwargs['market']
        with CacheLock(
                LockKeys.perpetual_put_market(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            if market_info['type'] == PerpetualMarketType.INVERSE.value:
                if kwargs['amount'] % 1 != 0:
                    raise InvalidArgument
            tool = VerifyPriceTool(market=market,
                                   business_type=OrderBusinessType.PERPETUAL_BUSINESS_TYPE,
                                   order_type=OrderType.MARKET_ORDER_TYPE,
                                   sell_or_buy=side,
                                   amount=kwargs['amount'],
                                   price=Decimal('0'),
                                   stop_price=Decimal('0')
                                   )
            tool.validate(g.user.main_user_type)
            fee_rate = fetch_cal_fee(user_id, market)
            fee_asset, fee_deduction_rate = get_fee_asset_and_rate(user_id)
            client = PerpetualServerClient(current_app.logger)
            if stop_loss_take_profit := kwargs['stop_loss_take_profit']:
                prec = int(market_info['money_prec'])
                if 'stop_loss_price' in stop_loss_take_profit:
                    stop_loss_take_profit['stop_loss_price'] = amount_to_str(stop_loss_take_profit['stop_loss_price'], prec)
                if 'take_profit_price' in stop_loss_take_profit:
                    stop_loss_take_profit['take_profit_price'] = amount_to_str(stop_loss_take_profit['take_profit_price'], prec)
            client.put_market(
                user_id=user_id,
                market=market,
                side=side,
                amount=str(kwargs['amount']),
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                source=RequestPlatform.from_request().value,
                fee_asset=fee_asset,
                fee_discount=fee_deduction_rate,
                stop_loss_take_profit=stop_loss_take_profit
            )
            LimitOrderResource.report_order_event(PerpetualEvent.MARKET_ORDER_COUNT, user_id)
        return success(message=_("下单成功"))


@ns.route('/close')
class CloseOrderResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            position_id=wa_fields.Integer(
                required=True,
                validate=lambda x: x > 0
            ),
            amount=PositiveDecimalField(
                required=True,
            ),
            price=PositiveDecimalField(
                required=True,
            ),
            effect_type=wa_fields.Integer(
                required=True,
                validate=lambda x: x in (1, 2, 3)
            )
        )
    )
    @trade_permission_validate(is_spot=False, is_closing_or_reducing_position=True)
    @copy_trading_sub_user_setter(require_running=False)
    @require_trade_password
    def put(cls, **kwargs):
        """
        限价平仓(目前web端在使用，后期考虑移除)
        """
        user_id = g.user.id
        market = kwargs['market']
        with CacheLock(
                LockKeys.perpetual_put_limit_close(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            if market_info['type'] == PerpetualMarketType.INVERSE.value:
                if kwargs['amount'] % 1 != 0:
                    raise InvalidArgument
            price = amount_to_str(
                kwargs['price'], int(market_info['money_prec']))
            side = get_user_close_order_side(user_id, market, kwargs["position_id"])
            tool = VerifyPriceTool(
                market,
                OrderBusinessType.PERPETUAL_BUSINESS_TYPE,
                OrderType.LIMIT_ORDER_TYPE,
                side,
                Decimal(kwargs['amount']),
                kwargs['price'],
                Decimal(),
            )
            tool.validate(g.user.main_user_type)
            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)
            client.limit_close(
                user_id=user_id,
                market=market,
                position_id=kwargs['position_id'],
                amount=str(kwargs['amount']),
                price=price,
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8),
                source=RequestPlatform.from_request().value,
                effect_type=kwargs['effect_type']
            )
        biz_monitor.increase_counter(PerpetualEvent.CLOSE_ORDER_COUNT)
        return success(message=_("下单成功"))

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            position_id=wa_fields.Integer(
                required=True,
                validate=lambda x: x > 0
            ),
        )
    )
    @trade_permission_validate(is_spot=False, is_closing_or_reducing_position=True)
    @copy_trading_sub_user_setter(require_running=False)
    def post(cls, **kwargs):
        """
        市价全平(兼容旧版本，未来可移除)
        """
        user_id = g.user.id
        market = kwargs['market']
        with CacheLock(
                LockKeys.perpetual_put_limit_close(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)
            client.market_close(
                user_id=user_id,
                market=market,
                amount='0',
                position_id=kwargs['position_id'],
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                source=RequestPlatform.from_request().value,
            )
        biz_monitor.increase_counter(PerpetualEvent.CLOSE_ORDER_COUNT)
        return success(message=_("下单成功"))


@ns.route('/market/close')
class MarketCloseOrderResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            position_id=wa_fields.Integer(
                required=True,
                validate=lambda x: x > 0
            ),
            amount=PositiveDecimalField(
                required=True,
            )
        )
    )
    @trade_permission_validate(is_spot=False, is_closing_or_reducing_position=True)
    @copy_trading_sub_user_setter(require_running=False)
    @require_trade_password
    def put(cls, **kwargs):
        """
        自定义市价平仓(目前web端在使用，后期考虑移除)
        """
        user_id = g.user.id
        market = kwargs['market']
        with CacheLock(
                LockKeys.perpetual_put_market(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            if market_info['type'] == PerpetualMarketType.INVERSE.value:
                if kwargs['amount'] % 1 != 0:
                    raise InvalidArgument
            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)
            try:
                client.market_close(
                    user_id=user_id,
                    market=market,
                    position_id=kwargs['position_id'],
                    amount=str(kwargs['amount']),
                    taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                    source=RequestPlatform.from_request().value
                )
                biz_monitor.increase_counter(PerpetualEvent.CLOSE_ORDER_COUNT)
            except Exception as e:
                if (e.code == PerpetualResponseCode.CONTRACT_PRICE_LESS_LIQUIDATION_PRICE or
                        e.code == PerpetualResponseCode.CONTRACT_PRICE_HIGHER_LIQUIDATION_PRICE):
                    e.message_template = _('市场波动剧烈，无法执行市价平仓，请稍后再试')
                    raise e
        return success(message=_("下单成功"))

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            position_id=wa_fields.Integer(
                required=True,
                validate=lambda x: x > 0
            )
        )
    )
    @trade_permission_validate(is_spot=False, is_closing_or_reducing_position=True)
    @copy_trading_sub_user_setter(require_running=False)
    def post(cls, **kwargs):
        """
        一键全平(目前web端在使用，后期考虑移除)
        """
        user_id = g.user.id
        market = kwargs['market']
        with CacheLock(
                LockKeys.perpetual_put_limit_close(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            fee_rate = fetch_cal_fee(user_id, market)
            client = PerpetualServerClient(current_app.logger)
            client.market_close_all(
                user_id=user_id,
                market=market,
                position_id=kwargs['position_id'],
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8),
                source=RequestPlatform.from_request().value
            )
        biz_monitor.increase_counter(PerpetualEvent.CLOSE_ORDER_COUNT)
        return success(message=_("下单成功"))


@ns.route('/stop')
class StopOrderResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            side=wa_fields.String(
                required=True,
                validate=lambda x: x in [v.name.lower() for v in OrderSideType]
            ),
            stop_type=wa_fields.Integer(
                required=True,
                validate=lambda x: x in (1, 2, 3)
            ),
            amount=PositiveDecimalField(
                required=True,
            ),
            stop_price=PositiveDecimalField(
                required=True,
            ),
            price=PositiveDecimalField(
                required=True,
            ),
            effect_type=wa_fields.Integer(
                required=True,
                validate=lambda x: x in (1, 2, 3, 4)
            ),
            hide=wa_fields.Boolean(missing=False, default=False, description='是否隐藏委托'),
            stop_loss_take_profit=wa_fields.Nested(StopLossTakeProfitSchema, missing=None)
        )
    )
    @require_user_permission(sub_account_permissions=[SubAccountPermission.PERPETUAL])
    @trade_permission_validate(is_spot=False)
    @copy_trading_sub_user_setter(require_running=True, check_args=["market"], check_count=True)
    @require_trade_password
    def put(cls, **kwargs):
        """
        计划限价
        """
        require_ip_not_only_withdrawal()
        require_user_not_only_withdrawal(g.user)
        require_user_kyc(g.user)
        user_id = g.user.id
        market = kwargs['market']
        effect_type = kwargs['effect_type']
        side = OrderSideType[kwargs['side'].upper()]
        hide = kwargs['hide']
        with CacheLock(
                LockKeys.perpetual_put_stop_limit(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            if market_info['type'] == PerpetualMarketType.INVERSE.value:
                if kwargs['amount'] % 1 != 0:
                    raise InvalidArgument
            if not PerpetualMarketCache.check_stop_order_count(user_id, market):
                raise StopOrderAmountLimitExceeded(
                    max_count=PerpetualMarketCache.MAX_STOP_ORDER_COUNT)
            prec = int(market_info['money_prec'])
            price = amount_to_str(
                kwargs['price'], prec)
            stop_price = amount_to_str(
                kwargs['stop_price'], prec)

            tool = VerifyPriceTool(
                market,
                OrderBusinessType.PERPETUAL_BUSINESS_TYPE,
                OrderType.STOP_LIMIT_ORDER_TYPE,
                side,
                Decimal(kwargs['amount']),
                price,
                stop_price,
            )
            tool.validate(g.user.main_user_type)

            fee_rate = fetch_cal_fee(user_id, market)
            fee_asset, fee_deduction_rate = get_fee_asset_and_rate(user_id)
            client = PerpetualServerClient(current_app.logger)
            option_effect_type = effect_type
            if effect_type == 4:
                if hide:
                    option_effect_type = ((
                                                      PERPETUAL_ONLY_MAKER_VALUE |
                                                      PERPETUAL_HIDE_VALUE) << 32) + 1
                else:
                    option_effect_type = (PERPETUAL_ONLY_MAKER_VALUE << 32) + 1
            else:
                if hide:
                    option_effect_type = (PERPETUAL_HIDE_VALUE << 32) + effect_type
            if stop_loss_take_profit := kwargs['stop_loss_take_profit']:
                if 'stop_loss_price' in stop_loss_take_profit:
                    stop_loss_take_profit['stop_loss_price'] = amount_to_str(stop_loss_take_profit['stop_loss_price'], prec)
                if 'take_profit_price' in stop_loss_take_profit:
                    stop_loss_take_profit['take_profit_price'] = amount_to_str(stop_loss_take_profit['take_profit_price'], prec)
            client.put_stop_limit(
                user_id=user_id,
                market=market,
                side=side,
                stop_type=kwargs['stop_type'],
                amount=str(kwargs['amount']),
                stop_price=stop_price,
                price=price,
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8),
                source=RequestPlatform.from_request().value,
                fee_asset=fee_asset,
                fee_discount=fee_deduction_rate,
                effect_type=option_effect_type,
                stop_loss_take_profit=stop_loss_take_profit
            )
            LimitOrderResource.report_order_event(PerpetualEvent.STOP_LIMIT_ORDER_COUNT, user_id)
        return success(message=_("下单成功"))

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            side=wa_fields.String(
                required=True,
                validate=lambda x: x in [v.name.lower() for v in OrderSideType]
            ),
            stop_type=wa_fields.Integer(
                required=True,
                validate=lambda x: x in (1, 2, 3)
            ),
            amount=PositiveDecimalField(
                required=True,
            ),
            stop_price=PositiveDecimalField(
                required=True,
            ),
            stop_loss_take_profit=wa_fields.Nested(StopLossTakeProfitSchema, missing=None)
        )
    )
    @require_user_permission(sub_account_permissions=[SubAccountPermission.PERPETUAL])
    @trade_permission_validate(is_spot=False)
    @copy_trading_sub_user_setter(require_running=True, check_args=["market"], check_count=True)
    def post(cls, **kwargs):
        """
        计划市价
        """
        require_ip_not_only_withdrawal()
        require_user_not_only_withdrawal(g.user)
        require_user_kyc(g.user)
        user_id = g.user.id
        market = kwargs['market']
        side = OrderSideType[kwargs['side'].upper()]
        with CacheLock(
                LockKeys.perpetual_put_stop_limit(user_id, market),
                wait=False
        ):
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                raise InvalidArgument
            if market_info['type'] == PerpetualMarketType.INVERSE.value:
                if kwargs['amount'] % 1 != 0:
                    raise InvalidArgument
            if not PerpetualMarketCache.check_stop_order_count(user_id, market):
                raise StopOrderAmountLimitExceeded(
                    max_count=PerpetualMarketCache.MAX_STOP_ORDER_COUNT)
            prec = int(market_info['money_prec'])
            stop_price = amount_to_str(
                kwargs['stop_price'], prec)
            fee_rate = fetch_cal_fee(user_id, market)
            fee_asset, fee_deduction_rate = get_fee_asset_and_rate(user_id)
            if stop_loss_take_profit := kwargs['stop_loss_take_profit']:
                if 'stop_loss_price' in stop_loss_take_profit:
                    stop_loss_take_profit['stop_loss_price'] = amount_to_str(stop_loss_take_profit['stop_loss_price'], prec)
                if 'take_profit_price' in stop_loss_take_profit:
                    stop_loss_take_profit['take_profit_price'] = amount_to_str(stop_loss_take_profit['take_profit_price'], prec)
            client = PerpetualServerClient(current_app.logger)
            client.put_stop_market(
                user_id=user_id,
                market=market,
                side=side,
                stop_type=kwargs['stop_type'],
                amount=str(kwargs['amount']),
                stop_price=stop_price,
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                maker_fee_rate=amount_to_str(fee_rate[TradeType.MAKER], 8),
                source=RequestPlatform.from_request().value,
                fee_asset=fee_asset,
                fee_discount=fee_deduction_rate,
                stop_loss_take_profit=stop_loss_take_profit
            )
        LimitOrderResource.report_order_event(PerpetualEvent.STOP_MARKET_ORDER_COUNT, user_id)
        return success(message=_("下单成功"))

    marshal_fields = {
        'amount': ex_fields.AmountField,
        'client_id': fields.String,
        'create_time': fields.String,
        'effect_type': fields.Integer,
        'target': fields.Integer,
        'fee_asset': fields.String,
        'fee_discount': fields.String,
        'maker_fee': ex_fields.AmountField,
        'market': fields.String,
        'order_id': fields.Integer,
        'price': ex_fields.PriceField,
        'side': fields.Integer,
        'source': fields.String,
        'state': fields.Integer,
        'stop_price': ex_fields.PriceField,
        'stop_type': fields.Integer,
        'taker_fee': ex_fields.AmountField,
        'type': fields.Integer,
        'update_time': fields.String,
        'stop_loss_price': ex_fields.PriceField,
        'take_profit_price': ex_fields.PriceField,
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(default=None, missing=None),
        side=wa_fields.String(missing='',
                              validate=lambda x: x in ('', 'buy', 'sell')),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
    ))
    @copy_trading_sub_user_setter(require_running=False)
    @respond_with_code
    def get(cls, **kwargs):
        """
        获取计划委托订单信息
        """
        market = kwargs['market'] or None
        if market and not PerpetualMarketCache.is_market_exists(market):
            return dict(
                has_next=False,
                curr_page=1,
                count=0,
                data=[],
                total=0,
            )
        side = kwargs['side']
        side = OrderSideType[side.upper()] if side else 0
        client = PerpetualServerClient(current_app.logger)
        result = client.pending_stop(
            g.user.id, market, side, kwargs['page'], kwargs['limit'])
        result['records'] = marshal(result['records'], cls.marshal_fields)
        return offset_to_page(result)

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(allow_none=True, missing=None),
        order_id=wa_fields.Integer(missing=''),
        type=IntEnumField(enum=OrderSideType,
                          example='sell',
                          description='order type, "sell" or "buy"',
                          missing=ORDER_BOTH_SIDE
                          )
    ))
    @copy_trading_sub_user_setter(require_running=False)
    @respond_with_code
    def delete(cls, **kwargs):
        """
        撤销计划委托订单
        """
        market = kwargs['market']
        order_id = kwargs['order_id']
        if market is not None and market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument
        user_id = g.user.id
        side = kwargs.get("type", ORDER_BOTH_SIDE)
        with CacheLock(
                LockKeys.perpetual_cancel_stop_order(user_id),
                wait=False
        ):
            t = CancelOrderTool('stop_limit', user_id, market, side)
            if order_id:
                return t.cancel_one(order_id)
            else:
                return t.cancel_all()


@ns.route('/modify')
class ModifyOrderResource(Resource):

    @classmethod
    @require_login
    @require_user_permission(sub_account_permissions=[SubAccountPermission.PERPETUAL])
    @trade_permission_validate(is_spot=False)
    @copy_trading_sub_user_setter(require_running=True)
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            order_id=wa_fields.Integer(required=True, validate=lambda x: x > 0),
            amount=PositiveDecimalField(required=True),
            price=PositiveDecimalField(required=True),
        )
    )
    def post(cls, **kwargs):
        """
        修改订单
        """
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        user_id = g.user.id
        market = kwargs['market']
        order_id = kwargs['order_id']
        client = PerpetualServerClient(current_app.logger)
        detail_result = client.pending_order_detail(
            market=market,
            order_id=order_id,
        )
        if not detail_result:
            raise PerpetualOrderExceptionMap[PerpetualResponseCode.CONTRACT_ORDER_ID_INVALID]
        if int(detail_result.get('user_id', 0)) != g.user.id:
            raise PerpetualOrderExceptionMap[PerpetualResponseCode.CONTRACT_USER_ID_ERROR]
        market_info = PerpetualMarketCache().get_market_info(market)
        if not market_info:
            raise InvalidArgument
        if market_info['type'] == PerpetualMarketType.INVERSE.value:
            if kwargs['amount'] % 1 != 0:
                raise InvalidArgument
        tool = VerifyPriceTool(
            market,
            OrderBusinessType.PERPETUAL_BUSINESS_TYPE,
            OrderType.LIMIT_ORDER_TYPE,
            OrderSideType(int(detail_result['side'])),
            Decimal(kwargs['amount']),
            kwargs['price'],
            Decimal(),
        )
        tool.validate(g.user.main_user_type)
        prec = int(market_info['money_prec'])
        price = amount_to_str(
            kwargs['price'], prec)
        client.order_modify(
            user_id=user_id,
            market=market,
            order_id=order_id,
            amount=str(kwargs['amount']),
            price=price,
        )
        return success(message=_("下单成功"))


@ns.route('/stop/modify')
class ModifyOrderStopResource(Resource):

    @classmethod
    @require_login
    @require_user_permission(sub_account_permissions=[SubAccountPermission.PERPETUAL])
    @trade_permission_validate(is_spot=False)
    @copy_trading_sub_user_setter(require_running=True)
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            order_id=wa_fields.Integer(required=True, validate=lambda x: x > 0),
            amount=PositiveDecimalField(required=True),
            price=PositiveDecimalField(required=True, allow_zero=True),
            stop_price=PositiveDecimalField(required=True),
        )
    )
    def post(cls, **kwargs):
        """
        修改计划订单
        """
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        user_id = g.user.id
        market = kwargs['market']
        order_id = kwargs['order_id']
        client = PerpetualServerClient(current_app.logger)
        detail_result = client.pending_order_stop_detail(
            market=market,
            order_id=order_id,
        )
        if not detail_result:
            raise PerpetualOrderExceptionMap[PerpetualResponseCode.CONTRACT_ORDER_ID_INVALID]
        if int(detail_result.get('user_id', 0)) != g.user.id:
            raise PerpetualOrderExceptionMap[PerpetualResponseCode.CONTRACT_USER_ID_ERROR]
        market_info = PerpetualMarketCache().get_market_info(market)
        if not market_info:
            raise InvalidArgument
        if market_info['type'] == PerpetualMarketType.INVERSE.value:
            if kwargs['amount'] % 1 != 0:
                raise InvalidArgument
        order_type = OrderType.STOP_LIMIT_ORDER_TYPE if int(
            detail_result['type']) == OrderIntType.LIMIT.value else OrderType.STOP_MARKET_ORDER_TYPE
        if order_type == OrderType.STOP_LIMIT_ORDER_TYPE:
            tool = VerifyPriceTool(
                market,
                OrderBusinessType.PERPETUAL_BUSINESS_TYPE,
                order_type,
                OrderSideType(int(detail_result['side'])),
                Decimal(kwargs['amount']),
                kwargs['price'],
                kwargs['stop_price'],
            )
            tool.validate(g.user.main_user_type)
        prec = int(market_info['money_prec'])
        price = amount_to_str(kwargs['price'], prec) if kwargs['price'] else ''
        stop_price = amount_to_str(
            kwargs['stop_price'], prec)
        client.order_modify_stop(
            user_id=user_id,
            market=market,
            order_id=order_id,
            amount=str(kwargs['amount']),
            price=price,
            stop_price=stop_price,
        )
        return success(message=_("下单成功"))


@ns.route('/finished')
@respond_with_code
class FinishedOrderResource(Resource):
    export_fields = {
        'create_time': ex_fields.LocalDateTimeStr,
        'market': fields.String,
        'type': ex_fields.EnumMarshalField(OrderIntType),
        'side': ex_fields.EnumMarshalField(OrderSideType),
        'amount': ex_fields.AmountField,
        'price': ex_fields.PriceField,
        'already_deal': fields.String(
            attribute=lambda x: amount_to_str(
                Decimal(x['amount']) - Decimal(x['left']), 8)),
        'value': ex_fields.AmountField(attribute='deal_stock'),
        'status': fields.String(
            attribute=lambda x: OrderStatusType.DONE.value
            if x['left'] == '0' else OrderStatusType.PART_DEAL.value
        )
    }

    marshal_fields = {
        'order_id': fields.Integer,
        'create_time': fields.Integer,
        'market': fields.String,
        'type': fields.Integer,
        'side': fields.Integer,
        'target': fields.Integer,
        'type_detail': fields.String(attribute=lambda x: _('市价')
        if x['type'] == OrderIntType.MARKET else _('限价')),
        'amount': ex_fields.AmountField,
        'price': ex_fields.PriceField,
        'already_deal': fields.String(
            attribute=lambda x: amount_to_str(
                Decimal(x['amount']) - Decimal(x['left']), 8)
        ),
        'average_price': ex_fields.PerpetualAveragePriceField(
            attribute=lambda x: (x['market'], x['amount'], x['left'], x['deal_stock'])
        ),
        'value': ex_fields.AmountField(attribute='deal_stock'),
        'effect_type_name': Order.PerpetualOrderEffectField(attribute='effect_type'),
        'leverage': fields.String, 'effect_type': fields.Integer,
        'fee_asset': fields.String, 'deal_fee': fields.String,
        'deal_asset_fee': ex_fields.AmountField,
        'deal_profit': ex_fields.AmountField(
            attribute=lambda x: Decimal(x['deal_profit']) - Decimal(x['deal_fee'])
        ),
        "is_system": fields.Integer(
            attribute=lambda x: int(x['source'] == 'sys')
        ),
        'status': ex_fields.PerpetualOrderStatusField(attribute=lambda x: x)
    }

    export_headers = (
        {"field": "create_time", Language.ZH_HANS_CN: "时间", Language.EN_US: "Time"},
        {"field": "market", Language.ZH_HANS_CN: "合约", Language.EN_US: "Contract"},
        {"field": "type", Language.ZH_HANS_CN: "类型", Language.EN_US: "Type"},
        {"field": "side", Language.ZH_HANS_CN: "方向", Language.EN_US: "Side"},
        {"field": "price", Language.ZH_HANS_CN: "委托价", Language.EN_US: "Price"},
        {"field": "amount", Language.ZH_HANS_CN: "委托量", Language.EN_US: "Amount"},
        {"field": "already_deal", Language.ZH_HANS_CN: "成交量", Language.EN_US: "Executed"},
        {"field": "value", Language.ZH_HANS_CN: "成交额", Language.EN_US: "Exec.Value"},
        # Done (done, 全部成交-All Executed）（!done, 部分成交-Party Executed）
        {"field": "status", Language.ZH_HANS_CN: "成交状态", Language.EN_US: "Execution Status"},
    )

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(missing=''),
        side=wa_fields.String(missing='',
                              validate=lambda x: x in ('', 'buy', 'sell')),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
        stop_order_id=wa_fields.Integer(missing=None),
        export=wa_fields.Integer(missing=0),
        market_type=EnumField(PerpetualMarketType),
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """
        历史普通委托
        """
        client = PerpetualServerClient(current_app.logger)
        market = kwargs['market']
        side = OrderSideType[kwargs['side'].upper()].value \
            if kwargs['side'] else 0
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        page = kwargs['page']
        limit = kwargs['limit']
        stop_order_id = kwargs.get("stop_order_id", None)
        market_type = kwargs.get('market_type')
        market_type = market_type.value if market_type else PERPETUAL_ALL_MARKET_TYPE
        if kwargs['export']:
            records = client.order_finished(
                g.user.id,
                market,
                side,
                start_time,
                end_time,
                stop_order_id=stop_order_id,
                limit=config['EXPORT_ITEM_MAX_COUNT'],
                market_type=market_type,
            )
            return export_xlsx(
                filename='perpetual_finished_order_history',
                data_list=marshal(records, cls.export_fields),
                export_headers=cls.export_headers
            )
        result = client.order_finished(
            g.user.id,
            market,
            side,
            start_time,
            end_time,
            stop_order_id,
            page,
            limit,
            market_type=market_type,
        ).as_dict()
        result['data'] = marshal(result['data'], cls.marshal_fields)
        return result


def get_stop_type(x):
    if x['target'] in [PositionDealIntType.TYPE_MARKET_CLOSE_ALL, PositionDealIntType.TYPE_CLOSE_ALL]:
        return '--'
    try:
        stop = StopOrderType(x['stop_type'])
    except TypeError:
        return '--'
    return stop.name.lower()


def get_price(x):
    if x['type'] == StopOrderIntType.STOP_LIMIT:
        return ex_fields.PriceField().format(x['price'])
    return '市价'


def get_stop_price(x):
    if x['target'] in [PositionDealIntType.TYPE_MARKET_CLOSE_ALL, PositionDealIntType.TYPE_CLOSE_ALL]:
        return '--'

    prefix = ''
    if x['state']:
        if x['state'] == 1:
            prefix = '≤'
        else:
            prefix = '≥'
    o = ex_fields.PriceField().format(x['stop_price'])
    return f'{prefix} {o}'


@ns.route('/finished/stop')
@respond_with_code
class FinishedStopOrderResource(Resource):
    export_fields = {
        'create_time': ex_fields.LocalDateTimeStr,
        'market': fields.String,
        'side': ex_fields.EnumMarshalField(OrderSideType),
        'amount': ex_fields.AmountField,
        'stop_price': fields.String(attribute=lambda x: get_stop_price(x)),
        'price': fields.String(attribute=lambda x: get_price(x)),
        'status': fields.String(attribute=lambda x: StopOrderStatusIntType(
            x['status']).name.lower()),
        'type': fields.String(attribute=lambda x: _(position_target_map[x['target']])
        if x['target'] in position_target_map else _('计划市价') if x['type'] == StopOrderIntType.STOP_MARKET else _(
            '计划限价')),
        'stop_type': fields.String(attribute=lambda x: get_stop_type(x)),
    }

    marshal_fields = {
        'order_id': fields.Integer,
        'create_time': fields.Integer,
        'market': fields.String,
        'amount': fields.String,
        'stop_price': fields.String,
        'price': fields.String,
        'state': fields.Integer,
        'status': fields.Integer,
        'stop_type': fields.Integer,
        'type': fields.Integer,
        'target': fields.Integer,
        'side': fields.Integer,
        'type_detail': fields.String(attribute=lambda x: _(position_target_map[x['target']])
        if x['target'] in position_target_map else _('计划市价') if x['type'] == StopOrderIntType.STOP_MARKET else _('计划限价'))
    }

    export_headers = (
        {"field": "create_time", Language.ZH_HANS_CN: "时间", Language.EN_US: "Time"},
        {"field": "market", Language.ZH_HANS_CN: "合约", Language.EN_US: "Contract"},
        {"field": "type", Language.ZH_HANS_CN: "类型", Language.EN_US: "Type"},
        {"field": "side", Language.ZH_HANS_CN: "方向", Language.EN_US: "Side"},
        {"field": "price", Language.ZH_HANS_CN: "委托价", Language.EN_US: "Price"},
        {"field": "amount", Language.ZH_HANS_CN: "委托量", Language.EN_US: "Amount"},
        {"field": "stop_price", Language.ZH_HANS_CN: "触发价", Language.EN_US: "Stop"},
        {"field": "stop_type", Language.ZH_HANS_CN: "触发类型", Language.EN_US: "Tigger types"},
        {"field": "status", Language.ZH_HANS_CN: "委托状态", Language.EN_US: "Order Status"},
    )

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(missing=''),
        side=wa_fields.String(missing='',
                              validate=lambda x: x in ('', 'buy', 'sell')),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
        export=wa_fields.Integer(missing=0),
        market_type=EnumField(PerpetualMarketType),
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """
        历史计划委托
        """
        client = PerpetualServerClient(current_app.logger)
        market = kwargs['market']
        side = OrderSideType[kwargs['side'].upper()].value \
            if kwargs['side'] else 0
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        page = kwargs['page']
        limit = kwargs['limit']
        market_type = kwargs.get('market_type')
        market_type = market_type.value if market_type else PERPETUAL_ALL_MARKET_TYPE
        if kwargs['export']:
            records = client.finished_stop(
                g.user.id,
                market,
                side,
                start_time,
                end_time,
                limit=config['EXPORT_ITEM_MAX_COUNT'],
                market_type=market_type,
            )
            return export_xlsx(
                filename='perpetual_finished_order_history',
                data_list=marshal(records, cls.export_fields),
                export_headers=cls.export_headers
            )
        result = client.finished_stop(
            g.user.id,
            market,
            side,
            start_time,
            end_time,
            page,
            limit,
            market_type=market_type,
        ).as_dict()
        result['data'] = marshal(result['data'], cls.marshal_fields)
        return result


@ns.route('/deals')
@respond_with_code
class OrderDealsResource(Resource):
    marshal_fields = {
        'create_time': fields.Integer(attribute='time'),
        'amount': fields.String,
        'side': fields.Integer,
        'deal_stock': ex_fields.AmountField,
        'deal_fee': ex_fields.AmountField,
        'fee_asset': fields.String,
        'deal_profit': ex_fields.DealProfitField(
            attribute=lambda x: (
                x['deal_profit'], x['deal_fee'], x['fee_asset'])
        ),
        'id': fields.Integer,
        'price': fields.String,
        'fee_rate': fields.String(attribute='fee_real_rate'),
        'role': fields.String(attribute=lambda x: Order.TradeRoleType(int(x['role'])).name.lower()),
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        order_id=wa_fields.Integer(required=True, validate=lambda x: x > 0),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
    ))
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, **kwargs):
        """
        成交明细
        """
        client = PerpetualServerClient(current_app.logger)
        result = client.order_deals(
            user_id=g.user.id,
            order_id=kwargs['order_id'],
            page=kwargs['page'],
            limit=kwargs['limit']
        )
        result['records'] = marshal(result['records'], cls.marshal_fields)
        return offset_to_page(result)


@ns.route('/liqing')
@respond_with_code
class OrderLiqingResource(Resource):
    marshal_fields = {
        'create_time': fields.Integer,
        'amount': fields.String,
        'price': fields.String,
        'side': fields.Integer
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.Str(required=True),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
    ))
    def get(cls, **kwargs):
        """
        市场信息-强平订单（未成交）
        """
        client = PerpetualServerClient(current_app.logger)
        result = client.pending_order(
            user_id=0,
            market=kwargs['market'],
            side=0,
            page=kwargs['page'],
            limit=kwargs['limit']
        )
        result['records'] = marshal(result['records'], cls.marshal_fields)
        return offset_to_page(result)


@ns.route('/finished/liquidation')
@respond_with_code
class OrderFinishedLiquidationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.Str(required=True),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0)
    ))
    def get(cls, **kwargs):
        """
        市场信息-强平订单（最近7天/30天已成交）
        """
        market = kwargs['market']
        page = kwargs['page']
        limit = kwargs['limit']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        if market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument

        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_liq_finished_order(
            market, start_time, end_time, offset, limit + 1
        )
        res = []
        market_type = PerpetualMarketCache().get_market_info(market).get('type')
        for record in records:
            real_amount = record['amount'] - record['left']
            deal_stock = record['deal_stock']
            if real_amount == 0 or deal_stock == 0:   # 如果有一个为0，则直接取记录的price字段
                price = record['price']
            else:
                if market_type == PerpetualMarketType.INVERSE:    # 反向合约
                    price = real_amount / deal_stock
                else:
                    price = deal_stock / real_amount
            item = {
                'create_time': int(record['create_time']),
                'amount': amount_to_str(real_amount, PrecisionEnum.COIN_PLACES),
                'price': amount_to_str(price, PrecisionEnum.PRICE_PLACES),
                'side': record['side']
            }
            res.append(item)
        result = {
            'limit': limit + 1,
            'offset': offset,
            'records': res
        }
        return offset_to_page(result)


@ns.route('/stop-order-summary/<int:order_id>')
@respond_with_code
class StopOrderSummaryResource(Resource):
    marshal_fields = {
        'order_id': fields.Integer,
        'create_time': fields.Integer,
        'amount': fields.String,
        'total_deal_stock': fields.String,
        'total_deal_amount': fields.String(
            attribute=lambda x: amount_to_str(
                Decimal(x['total_deal_amount']), 8)
        ),
        'asset_fee': fields.String,
        'fee_asset': fields.String,
        'fee_discount': fields.String,
        'avg_limit_price': fields.String,
        'avg_deal_price': ex_fields.PerpetualAveragePriceField(
            attribute=lambda x: (x['market'], x['amount'], x['left'], x['total_deal_stock'])),
        'market': fields.String,
        'side': ex_fields.EnumMarshalField(OrderSideType),
    }

    @classmethod
    @require_login
    @copy_trading_sub_user_setter(require_running=False)
    def get(cls, order_id):
        user_id = g.user.id
        client = PerpetualServerClient()
        detail_result = client.finished_stop_order_detail(
            user_id=user_id,
            order_id=order_id,
        )
        market = detail_result["market"]
        market_type = PerpetualMarketCache().get_market_info(market).get('type', 1)
        orders = client.order_finished(
            user_id,
            market,
            start_time=0,
            end_time=0,
            page=1,
            limit=50,
            side=0,
            stop_order_id=order_id,
            market_type=market_type
        )
        total_deal_stock = sum([Decimal(v["deal_stock"]) for v in orders])
        total_deal_amount = sum([Decimal(v['amount']) - Decimal(v['left']) for v in orders])

        left = Decimal(detail_result['amount'])
        if len(orders) > 0:
            left = Decimal(orders[0]["left"])
        detail_result.update(
            dict(
                left=left,
                total_deal_stock=total_deal_stock,
                total_deal_amount=total_deal_amount,
                avg_limit_price=amount_to_str(
                    sum([Decimal(v["price"]) for v in orders]) / len(orders), PrecisionEnum.PRICE_PLACES)
                if len(orders) > 0 else '0'
            )
        )
        return marshal(detail_result, cls.marshal_fields)


@ns.route('/motd')
@respond_with_code
class MergeOrderToDepthResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.Str(required=True)
    ))
    def get(cls, **kwargs):
        """自营交易用户的挂单会在深度中隐藏(相当于冰山委托)，
        但是他们自己要能在深度中看到自己的挂单，该接口指示客户端合并挂单到深度"""
        v = PropTradingOpUsersCache.has(kwargs['market'], g.user.id)
        return {'show': v}
