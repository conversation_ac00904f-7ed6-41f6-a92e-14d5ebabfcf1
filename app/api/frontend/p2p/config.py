from decimal import Decimal

from flask import g
from flask_babel import force_locale
from marshmallow import fields

from app import Language
from app.api.common import Namespace, Resource, respond_with_code, get_request_user, get_request_ip, require_login
from app.business import PriceManager
from app.business.p2p.config import p2p_setting
from app.business.p2p.message import ImMixin
from app.business.p2p.pay_channel import PayChannelBus
from app.business.p2p.utils import P2pUtils
from app.caches.p2p import CountryFiatCache, CountrySuggestPayChannelCache, \
    FiatPayChannelCache, P2pFiatLimitCache, P2pAssetConfigCache
from app.caches.question import QuestionGroupCache
from app.common import Currency, get_country
from app.exceptions.p2p import P2pExceptionMap, P2pExceptionCode
from app.models.mongo.p2p.pay_channel import P2pPayChannelMySQL
from app.models.mongo.p2p.config import P2pFiatConfigMySQL
from app.utils import GeoIP

ns = Namespace('config')

"""
p2p 配置接口数据返回。
1. 全局支付渠道
2. 各种登录态的用户返回的默认配置的支付渠道
"""


@ns.route("")
@respond_with_code
class P2pConfigResource(Resource):
    STABLE_ASSET = {"USDT", "USDC"}
    ASSET_SORT = ["USDT", "USDC", "BTC", "ETH"]

    @classmethod
    def get_country_by_ip(cls):
        ip = get_request_ip()
        _ip_info = GeoIP(ip)
        if not (ip_country_code := _ip_info.country_code):
            return ""
        country = get_country(ip_country_code)
        return country.iso_3

    @classmethod
    def get_user_last_trade_fiat(cls, user):
        """获取用户最后一次p2p交易订单的法币"""
        if not user:
            return ""
        return P2pUtils.get_last_p2p_fiat(user.id)

    @classmethod
    def get_fiat_by_country_code(cls, country_code: str):
        if country_code and (fiat := CountryFiatCache().read_one_country(country_code)):
            return fiat
        return ""

    @classmethod
    def get_pay_channel_by_country_code(cls, country_code: str):
        if not country_code:
            return {}
        return CountrySuggestPayChannelCache().read_one_country(country_code)

    @classmethod
    def get_fiat_precision(cls):
        fiat_config_mapper = P2pFiatLimitCache.get_all()
        return {k: v['precision'] for k, v in fiat_config_mapper.items()}

    @classmethod
    def get(cls):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        user = get_request_user(allow_sub_account=False)
        if not user:
            country_code = cls.get_country_by_ip()
        else:
            country_code = user.kyc_country or user.location_code

        pay_channel_mapper = PayChannelBus(lang).get_all()
        normal_pay_channels = []
        country_pay_channels = []
        for k, v in pay_channel_mapper.items():
            v["id"] = k
            if v["config_type"] == P2pPayChannelMySQL.ConfigType.NORMAL.name:
                normal_pay_channels.append(v)
            else:
                country_pay_channels.append(v)

        fiat_pay_channels = FiatPayChannelCache().get_all()
        area_recommend_cache_data = cls.get_pay_channel_by_country_code(country_code)
        area_fiat = cls.get_fiat_by_country_code(country_code)
        recommend_fiat = P2pUtils.get_fiat_by_country_code(country_code)
        default_fiat = cls.get_user_last_trade_fiat(user) or recommend_fiat or Currency.USD.name
        asset_cache = P2pAssetConfigCache()
        precision_map = {i["asset"]: i["precision"] for i in asset_cache.get_many().values()}
        assets = sorted(
            asset_cache.get_active_assets(),
            key=lambda x: cls.ASSET_SORT.index(x) if x in cls.ASSET_SORT else len(precision_map)
        )
        return dict(
            normal_pay_channels=normal_pay_channels,
            country_pay_channels=country_pay_channels,
            fiat_pay_channels=fiat_pay_channels,  # 法币-支付
            area_recommend_channels=area_recommend_cache_data.get(area_fiat, []),  # 推荐 地区-支付
            area_fiat_recommend_channels=area_recommend_cache_data,  # 推荐 地区-法币-支付
            fiats=P2pUtils.get_valid_fiats(),  # 地区 - 法币
            invalid_fiats=P2pUtils.get_invalid_fiats(),  # 已下架的法币
            fiat_precision=cls.get_fiat_precision(),  # 法币-精度
            assets=assets,
            asset_precision=precision_map,
            payment_timeliness=p2p_setting.get_pay_timout_list_display(),
            default_fiat=default_fiat,
            recommend_fiat=recommend_fiat,
            merchant_asset_direction_limit=p2p_setting.merchant_asset_direction_limit,
            daily_user_cancel_order_limit=p2p_setting.daily_user_cancel_order_limit,
            daily_merchant_cancel_order_limit=p2p_setting.daily_merchant_cancel_order_limit,
            user_max_active_order_limit=p2p_setting.user_max_active_order_limit,
            merchant_max_active_order_limit=p2p_setting.merchant_max_active_order_limit,
            merchant_max_active_adv_limit=p2p_setting.merchant_max_active_adv_limit,
            order_confirm_timeout=p2p_setting.order_confirm_timeout,
            merchant_fiats=P2pUtils.get_merchant_p2p_fiats(user.kyc_country if user else "")
        )


@ns.route("/advertising-limit")
@respond_with_code
class P2pConfigAdvertisingResource(Resource):
    BASE_ASSET = "USDT"

    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String(required=True),
        asset=fields.String(required=True),
        limit_unit=fields.String()
    ))
    def get(cls, **kwargs):
        fiat, asset = kwargs["fiat"], kwargs["asset"]
        limit_unit = kwargs.get('limit_unit')
        fiat_config = P2pFiatLimitCache.get_fiat_info(fiat)
        if not fiat_config:
            raise P2pExceptionMap[P2pExceptionCode.FIAT_NOT_SUPPORTED]
        asset_limit_info = P2pAssetConfigCache.get_asset_info(asset)
        if not asset_limit_info:
            raise P2pExceptionMap[P2pExceptionCode.ASSET_NOT_SUPPORTED]

        rate = P2pUtils.get_p2p_asset_to_fiat_rate(asset, fiat)
        config_min_limit = Decimal(fiat_config['min_limit'])  # USDT
        config_max_limit = Decimal(fiat_config['max_limit'])
        asset_rate = PriceManager.asset_to_asset(P2pFiatConfigMySQL.DEFAULT_LIMIT_UNIT, asset)
        unit_min_limit = config_min_limit * asset_rate
        unit_max_limit = config_max_limit * asset_rate
        if limit_unit == asset:
            min_limit = P2pUtils.fmt_base_amount(limit_unit, unit_min_limit)
            max_limit = P2pUtils.fmt_base_amount(limit_unit, unit_max_limit)
        elif limit_unit == fiat and not rate:
            min_limit = unit_min_limit
            max_limit = unit_max_limit
        else:
            min_limit = P2pFiatLimitCache.format_limit_amount(unit_min_limit * rate)
            max_limit = P2pFiatLimitCache.format_limit_amount(unit_max_limit * rate)

        return dict(
            rate=rate,
            asset_precision=asset_limit_info['precision'],
            fiat_precision=fiat_config['precision'],
            stock_limit=dict(
                min_limit=asset_limit_info['min_limit'],
                max_limit=asset_limit_info['max_limit']
            ),
            limit_settings=dict(
                limit_unit=limit_unit or fiat,
                min_limit=min_limit,
                max_limit=max_limit
            ),
            price_limit_settings=dict(
                is_price_limit=fiat_config['is_price_limit'],
                min_price=fiat_config.get('min_price', "0"),
                max_price=fiat_config.get('max_price', "0"),
            ),
        )


@ns.route("/im-message")
@respond_with_code
class ImMessageResource(Resource):

    @classmethod
    def get(cls):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        with force_locale(lang.value):
            data = ImMixin.get_message_mapper()
        return data


@ns.route("/question")
@respond_with_code
class P2pQuestionResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, **kwargs):
        if not (group_id := p2p_setting.question_group_id):
            return {}
        questions = QuestionGroupCache(group_id).read_by_lang(Language(g.lang))
        return questions
