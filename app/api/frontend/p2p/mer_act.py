import math
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from gettext import gettext as _

from flask import g
from marshmallow import fields
from sqlalchemy import func
from flask_restx import fields as fx_fields
from app import Language
from app.api.common import Namespace, respond_with_code, Resource, require_login
from app.api.common.decorators import require_p2p_permission
from app.api.common.fields import TimestampField, PageField, LimitField, EnumMarshalField, TimestampMarshalField, \
    EnumField
from app.business.p2p.mer_act import P2pMerActBiz
from app.business.p2p.message import P2pMerActApplySuccess
from app.business.p2p.permission import P2pPermissionManager
from app.caches.p2p import P2pMerActCache
from app.common import P2pBusinessType
from app.exceptions import OperationNotAllowed, InvalidArgument
from app.exceptions.p2p import P2pPermissionError
from app.models import P2pUser, db, GiftHistory
from app.models.mongo.p2p import UserPayChannelMySQL
from app.models.mongo.p2p.mer_act import P2pMerActMySQL as P2pMerAct
from app.models.p2p_mer_act import P2pMerActUserReward, P2pMerActUser, HourP2pMerActPoint, P2pMerActRewardHistory
from app.utils import now, today_datetime, max_length_validator, amount_to_str, query_to_page, quantize_amount, \
    AWSBucketPublic
from app.utils.offset_to_page import list_to_page

ns = Namespace("p2p activity")
url_prefix = '/mer-act'


@ns.route("/list")
@respond_with_code
class P2pMerActListResource(Resource):
    model = P2pMerAct

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField(missing=5),
    ))
    def get(cls, **kwargs):
        lang = Language(g.lang)
        page, limit = kwargs['page'], kwargs['limit']
        query = cls.model.query.filter(
            cls.model.status != cls.model.Status.PENDING
        ).filter(
            cls.model.start_at < cls.model.end_at
        ).order_by(cls.model.end_at.desc(), cls.model.act_id.desc())
        
        paginate = query.paginate(page, limit, error_out=False)
        objs = paginate.items
        total = paginate.total
        items = []
        for item in objs:
            lang_data = item.lang_map.get(lang.name) or item.lang_map.get(Language.DEFAULT.name)
            image_key = item.image_key
            file_url = AWSBucketPublic.get_file_url(image_key) if image_key else ""
            items.append(dict(
                act_id=item.act_id,
                name=lang_data.get("display_name"),
                desc=lang_data.get("desc"),
                file_url=file_url,
                fiat=item.fiat,
                reward_asset=item.reward_asset,
                all_reward=item.get_all_reward(),
                start_at=item.start_at,
                end_at=item.end_at,
                status=item.status.name,
            ))

        has_next = total > page * limit
        return dict(
            has_next=has_next,
            curr_page=page,
            count=len(items),
            data=items,
            total=total,
            total_page=math.ceil(total / limit)
        )


@ns.route("/user")
@respond_with_code
class P2pMerActUserResource(Resource):
    """参与记录"""
    model = P2pMerActUser
    r_model = P2pMerActUserReward
    a_model = P2pMerAct

    marshal_fields = {
        'act_id': fx_fields.Integer,
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        user_id = g.user.id
        rows = cls.model.query.filter(
            cls.model.user_id == user_id,
            cls.model.status == cls.model.Status.PASSED
        ).order_by(cls.model.id.desc()).all()

        # 查找奖励
        rewards = cls.r_model.query.filter(
            cls.r_model.user_id == user_id,
        ).all()
        rewards_map = {(i.act_id, i.side): i for i in rewards}
        act_rows = cls.a_model.query.filter(
            cls.a_model.act_id.in_([i.act_id for i in rows])
        ).all()
        lang = Language(g.lang)
        act_map = {i.act_id: i for i in act_rows}
        items = []
        for item in rows:
            act_id = item.act_id
            act = act_map[act_id]
            lang_item = act.lang_map.get(lang.name) or act.lang_map.get(Language.DEFAULT.name)
            for side in act.get_valid_side():
                reward = rewards_map.get((act_id, side))
                items.append(dict(
                    act_id=act_id,
                    name=lang_item["display_name"],
                    fiat=act.fiat,
                    side=side.name,
                    asset=act.reward_asset,
                    amount=amount_to_str(reward.lock_amount + reward.release_amount, 2) if reward else 0,
                ))
        ret = list_to_page(items, kwargs['page'], kwargs['limit'])
        return ret


@ns.route("/<int:act_id>")
@respond_with_code
class P2pMerActResource(Resource):
    """活动详情"""
    model = P2pMerActUserReward

    @classmethod
    def get(cls, act_id, **kwargs):
        lang = Language(g.lang)
        data = P2pMerActCache(act_id).read_by_lang(lang)
        if not data:
            raise InvalidArgument(message=_("活动不存在"))
        return data


@ns.route("/user/<int:act_id>")
@respond_with_code
class P2pMerActUserDetailResource(Resource):
    """单个活动最新小时积分, 累计奖励"""
    model = P2pMerActUserReward

    @classmethod
    @require_login
    def get(cls, act_id, **kwargs):
        user_id = g.user.id
        reward_asset = P2pMerAct.query.filter(P2pMerAct.act_id == act_id).with_entities(P2pMerAct.reward_asset).scalar()
        
        reward = cls.model.query.filter(
            cls.model.act_id == act_id,
            cls.model.user_id == user_id,
        ).group_by(
            cls.model.user_id
        ).with_entities(
            cls.model.asset,
            func.sum(cls.model.lock_amount).label("lock_amount"),
            func.sum(cls.model.release_amount).label("release_amount"),
        ).first()
        lock_amount = quantize_amount(reward.lock_amount, 2) if reward else Decimal()
        release_amount = quantize_amount(reward.release_amount, 2) if reward else Decimal()
        u_model = P2pMerActUser
        user_row = u_model.query.filter(
            u_model.act_id == act_id,
            u_model.user_id == user_id,
        ).first()
        ret = dict(
            user_status=user_row.status.name if user_row else '',
            reward_asset=reward_asset,
            lock_amount=lock_amount,
            release_amount=release_amount,
            all_amount=amount_to_str(lock_amount + release_amount, 2)
        )
        ret.update(cls.add_rank_and_point(user_id, act_id))
        return ret

    @classmethod
    def add_rank_and_point(cls, user_id, act_id):
        data = dict()
        for side in P2pBusinessType:
            point_map = P2pMerActBiz.get_sum_point_map(act_id, today_datetime(), now(), side)
            point = point_map.get(user_id, Decimal())
            data[f"{side.value}_point"] = point
            if not point:
                data[f"{side.value}_rank"] = 0
            sort_list = sorted(point_map.items(), key=lambda x: x[1], reverse=True)
            rank = 0
            for idx, (tmp_user_id, point) in enumerate(sort_list, 1):
                if tmp_user_id == user_id:
                    rank = idx
                    break
            data[f"{side.value}_rank"] = rank
        return data

    @classmethod
    def get_today_last_point(cls, act_id, side):
        today_ = today_datetime()
        point = cls.model.query.filter(
            cls.model.act_id == act_id,
            cls.model.side == side,
            cls.model.user_id == g.user.id,
            cls.model.report_hour >= today_,
            cls.model.report_hour < today_ + timedelta(days=1),
        ).order_by(cls.model.report_hour.desc()).with_entities(
            cls.model.total_point
        ).scalar() or Decimal()
        return point


@ns.route("/point/list/<int:act_id>")
@respond_with_code
class P2pMerActPointListResource(Resource):
    """买卖积分列表"""
    model = HourP2pMerActPoint

    marshal_fields = {
        'user_id': fx_fields.Integer,
        'point': fx_fields.String(
            attribute=lambda x: amount_to_str(x.point, 2)),
    }

    @classmethod
    @ns.use_kwargs(dict(
        date=TimestampField(),
        side=EnumField(P2pBusinessType, required=True),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, act_id, **kwargs):
        act = P2pMerAct.query.filter(P2pMerAct.act_id == act_id).first()
        ret = dict()
        p_model = P2pUser
        side = kwargs["side"]
        reward = getattr(act, f"{side.value}_reward")
        if not reward:
            ret[f"point_list"] = []
            return
        date_dt = kwargs.get(f"date") or P2pMerActBiz.get_act_newest_day(act)
        query = cls.get_date_point_rows(act_id, side, date_dt)
        ret = query_to_page(query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        # 获取奖励
        r_model = P2pMerActRewardHistory
        reward_rows = r_model.query.filter(
            r_model.act_id == act_id,
            r_model.side == side,
            r_model.reward_date == date_dt.date(),
        ).with_entities(
            r_model.user_id,
            r_model.amount,
        ).all()
        reward_map = {i.user_id: i for i in reward_rows}

        user_ids = [i['user_id'] for i in ret['data']]
        user_rows = p_model.query.filter(
            p_model.user_id.in_(user_ids)
        ).with_entities(p_model.user_id, p_model.nickname).all()
        user_map = {i.user_id: i.nickname for i in user_rows}
        for item in ret['data']:
            reward = reward_map.get(item["user_id"])
            item["asset"] = act.reward_asset
            item["amount"] = amount_to_str(reward.amount, 2) \
                if reward else Decimal()
            item["nickname"] = user_map.get(item["user_id"])
        return ret

    @classmethod
    def get_date_point_rows(cls, act_id, side, dt):
        rows = cls.model.query.filter(
            cls.model.side == side,
            cls.model.act_id == act_id,
            cls.model.report_hour >= dt,
            cls.model.report_hour < dt + timedelta(days=1)
        ).group_by(
            cls.model.user_id
        ).having(
            func.sum(cls.model.point) > 0
        ).with_entities(
            func.sum(cls.model.point).label("point"),
            cls.model.user_id
        ).order_by(
            func.sum(cls.model.point).desc()
        )
        return rows


@ns.route("/user/point/<int:act_id>")
@respond_with_code
class P2pMerActUserPointResource(Resource):
    """每小时积分"""
    model = HourP2pMerActPoint

    marshal_fields = {
        'report_hour': TimestampMarshalField,
        'fiat': fx_fields.String,
        'side': EnumMarshalField(P2pBusinessType, output_field_lower=False),
        'point_type': EnumMarshalField(model.PointType, output_field_lower=False),
        'point': fx_fields.String(
            attribute=lambda x: amount_to_str(x.point, 2),
        ),
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, act_id, **kwargs):
        user_id = g.user.id
        query = cls.model.query.filter(
            cls.model.act_id == act_id,
            cls.model.user_id == user_id,
        ).order_by(cls.model.report_hour.desc(), cls.model.updated_at.desc())
        return query_to_page(query, kwargs['page'], kwargs['limit'], cls.marshal_fields)


@ns.route("/user/reward/<int:act_id>")
@respond_with_code
class P2pMerActUserRewardResource(Resource):
    """用户奖励记录"""
    model = P2pMerActRewardHistory

    marshal_fields = {
        'reward_date': TimestampMarshalField,
        'side': EnumMarshalField(P2pBusinessType, output_field_lower=False),
        'asset': fx_fields.String,
        'amount': fx_fields.String(
            attribute=lambda x: amount_to_str(x.amount, 2),
        ),
        'rank': fx_fields.Integer,
        'point': fx_fields.String(
            attribute=lambda x: amount_to_str(x.point, 2),
        ),
        'gift_id': fx_fields.Integer,
        "release_date": fx_fields.Integer(
            attribute=lambda x: int((x.created_at + timedelta(days=x.reward_lock_day)).timestamp())),
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, act_id, **kwargs):
        fiat = P2pMerAct.query.filter(P2pMerAct.act_id == act_id).with_entities(P2pMerAct.fiat).scalar()
        user_id = g.user.id
        query = cls.model.query.filter(
            cls.model.act_id == act_id,
            cls.model.user_id == user_id,
            cls.model.release_status == cls.model.ReleaseStatus.RELEASED
        ).order_by(cls.model.id.desc())
        g_model = GiftHistory
        result = query_to_page(query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        gift_ids = [i.get("gift_id") for i in result['data']]
        gift_rows = g_model.query.filter(
            g_model.id.in_(gift_ids)
        ).with_entities(g_model.id, g_model.status)
        gift_map = {i.id: i.status for i in gift_rows}
        for item in result['data']:
            item['fiat'] = fiat
            item['reward_status'] = gift_map.get(item['gift_id']).name
        return result


@ns.route("/user/apply/<int:act_id>")
@respond_with_code
class P2pMerActUserApplyResource(Resource):
    """活动报名"""
    model = P2pMerActUser

    @classmethod
    @require_p2p_permission(is_valid_mer=True)
    @ns.use_kwargs(dict(
        intro=fields.String(required=True, validate=max_length_validator(200)),
        other_link=fields.String(validate=max_length_validator(200)),
        image_keys=fields.List(fields.String()),
        pdf_keys=fields.List(fields.String()),
    ))
    def post(cls, act_id, **kwargs):
        user_id = g.user.id
        if not P2pPermissionManager.check_all_permission(user_id):
            raise P2pPermissionError
        if cls.model.query.filter(
                cls.model.act_id == act_id,
                cls.model.user_id == user_id,
        ).first():
            raise OperationNotAllowed(message=_("已报名"))
        act = P2pMerAct.query.filter(
            P2pMerAct.act_id == act_id, 
            P2pMerAct.status == P2pMerAct.Status.ONLINE
        ).first() 
        if not act:
            raise InvalidArgument(message=_("活动不存在"))
        if not (act.apply_start_at <= now() <= act.apply_end_at):
            raise OperationNotAllowed(message=_("不在活动报名时间内"))
        # 检查商家是否有符合的 pay_channel_id
        user_pay_channels = UserPayChannelMySQL.get_user_all_channel(user_id)
        channel_ids = set(i.pay_channel_id for i in user_pay_channels)
        if not (set(act.pay_channel_ids) & channel_ids):
            raise OperationNotAllowed(message=_("没有符合活动的支付渠道"))

        row = P2pMerActUser(
            act_id=act_id,
            user_id=user_id,
            intro=kwargs['intro'],
            other_link=kwargs.get('other_link'),
            images=kwargs.get('image_keys', []),
            pdfs=kwargs.get('pdf_keys', []),
        )
        db.session_add_and_commit(row)
        P2pMerActApplySuccess(act_id).send_message(user_id)
