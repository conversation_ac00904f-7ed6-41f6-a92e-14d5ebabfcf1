from flask import g
from marshmallow import fields

from app.api.common import Namespace
from app.api.common import Resource
from app.api.common import respond_with_code
from app.api.common.fields import <PERSON>Field
from app.api.common.fields import LimitField
from app.api.common.decorators import require_p2p_permission

from app.business.p2p.relation import P2pRelationManager

ns = Namespace('relation')


@ns.route("/<string:biz_user_id>")
@respond_with_code
class P2pRelationResource(Resource):

    @classmethod
    @require_p2p_permission
    def get(cls, biz_user_id):
        return P2pRelationManager(g.user.id).get_relation_data(biz_user_id)


@ns.route("/block")
@respond_with_code
class P2pRelationBlockListResource(Resource):

    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField(missing=5),
    ))
    def get(cls, **kwargs):
        return P2pRelationManager(g.user.id).get_block_list(kwargs['page'], kwargs['limit'])


@ns.route("/follow")
@respond_with_code
class P2pRelationFollowListResource(Resource):

    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField(missing=5),
    ))
    def get(cls, **kwargs):
        return P2pRelationManager(g.user.id).get_follow_list(kwargs['page'], kwargs['limit'])


@ns.route("/block/<string:biz_user_id>")
@respond_with_code
class P2pRelationBlockUpdateResource(Resource):

    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        is_block=fields.Boolean(required=True),
    ))
    def post(cls, biz_user_id, **kwargs):
        is_block = P2pRelationManager(g.user.id).update_block_relation(biz_user_id, kwargs['is_block'])
        return dict(
            is_block=is_block,
        )


@ns.route("/follow/<string:biz_user_id>")
@respond_with_code
class P2pRelationFollowUpdateResource(Resource):

    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        is_follow=fields.Boolean(required=True),
    ))
    def post(cls, biz_user_id, **kwargs):
        is_follow = P2pRelationManager(g.user.id).update_follow_relation(biz_user_id, kwargs['is_follow'])
        return dict(
            is_follow=is_follow,
        )
