from datetime import datetime
from operator import and_

from flask import g, current_app
from sqlalchemy import or_
from webargs import fields
from flask_restx import fields as fx_fields, marshal

from app import Language
from app.api.common import respond_with_code, Namespace, Resource, require_login, require_2fa, ex_fields
from app.api.common.decorators import require_p2p_permission, lock_request, limit_user_frequency
from app.api.common.fields import PageField, LimitField, EnumField, TimestampField
from app.business.clients.im import ImServerClient
from app.business.p2p.order import P2pOrderBiz
from app.business.p2p.order_complaint import P2pOrderComplaintBiz, P2pComplaintRecordBiz
from app.business.p2p.order_factor import P2pOrderFactor
from app.business.p2p.utils import P2pUtils, export_amount
from app.business.security import check_withdraw_password_by_api
from app.caches.p2p import UserUnreadComplaintCache
from app.common import P2pBusinessType, MobileCodeType, P2pAmountType, P2pOrderUserType
from app.exceptions import InvalidArgument, RecordNotFound, TwoFactorAuthenticationRequired
from app.models import P2pUser
from app.models.base import Query, db
from app.models.p2p import P2pOrder, P2pOrderComplaint, P2pOrderComplaintRecord
from app.utils import export_xlsx

ns = Namespace("order")


class P2pOrderBase(Resource):
    model = P2pOrder
    c_model = P2pOrderComplaint

    @classmethod
    def get_orders_query(cls, user_id, kwargs: dict, query=None) -> Query:
        """过滤订单"""
        query = query or cls.model.query
        if user_type := kwargs.get("user_type"):
            if user_type == P2pOrderUserType.CUSTOMER:
                query = query.filter(cls.model.customer_id == user_id)
            else:
                query = query.filter(cls.model.merchant_id == user_id)
        customer_statement = [cls.model.customer_id == user_id]
        merchant_statement = [cls.model.merchant_id == user_id]
        biz_user_id = kwargs.get("opponent_user_id")
        opponent_user_id = P2pUtils.change_biz_user_id(biz_user_id) if biz_user_id else None
        if opponent_user_id:
            customer_statement.append(cls.model.merchant_id == opponent_user_id)
            merchant_statement.append(cls.model.customer_id == opponent_user_id)
        if side := kwargs.get("side"):
            # 商家端可以同时看到作为用户和商家的订单，但是方向是相反的
            customer_statement.append(cls.model.side == side)
            merchant_statement.append(cls.model.side == P2pBusinessType.reverse(side))
        query = query.filter(
            or_(
                and_(*customer_statement) if len(customer_statement) > 1 else customer_statement[0],
                and_(*merchant_statement) if len(merchant_statement) > 1 else merchant_statement[0]
            )
        )
        if statuses := kwargs.get("statuses"):
            query = query.filter(cls.model.status.in_(statuses))
        if fiat_list := kwargs.get("fiat"):
            query = query.filter(cls.model.quote.in_(fiat_list))
        if assets := kwargs.get("assets"):
            query = query.filter(cls.model.base.in_(assets))
        if order_id := kwargs.get("order_id"):
            query = query.filter(cls.model.order_id.contains(order_id))
        if start_time := kwargs.get("start_time"):
            query = query.filter(cls.model.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            query = query.filter(cls.model.created_at < end_time)
        return query

    @classmethod
    def get_user_order(cls, user_id: int, order_id: str) -> P2pOrder:
        order = cls.model.get_user_query(user_id).filter(
            cls.model.order_id == order_id
        ).first()
        if not order:
            raise InvalidArgument("订单不存在")
        return order

    @classmethod
    def get_merchant_order(cls, user_id: int, order_id: str):
        return cls.model.query.filter(
            cls.model.merchant_id == user_id,
            cls.model.order_id == order_id
        ).first()

    @classmethod
    def get_and_check_order(cls, user_id: int, order_id: str) -> P2pOrder:
        order = cls.get_user_order(user_id, order_id)
        return order

    @classmethod
    def get_active_order_count(cls, user_id: int) -> int:
        return P2pOrderBiz.get_user_active_order_count(user_id)

    @classmethod
    def format_order_list(cls, user_id, page_orders):
        return P2pOrderFactor.format_many_order(user_id, page_orders)

    @classmethod
    def get_im_user_id(cls, user_id):
        return P2pUser.get_by_user_id(user_id).biz_user_id


@ns.route("")
@respond_with_code
class P2pOrderResource(P2pOrderBase):

    @classmethod
    @require_p2p_permission(is_trading=True, is_kyc=True)
    @lock_request(with_user=True)
    @ns.use_kwargs(dict(
        adv_id=fields.String(required=True),
        price=fields.Decimal(required=True),
        base=fields.String(),
        quote=fields.String(),
        base_amount=fields.Decimal(required=True, validate=lambda v: v > 0),
        quote_amount=fields.Decimal(required=True, validate=lambda v: v > 0),
        amount_type=EnumField(P2pAmountType, required=True),
        user_pay_channel_id=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """创建订单"""
        user = g.user
        if not user.has_2fa:
            raise TwoFactorAuthenticationRequired
        user_id = user.id
        order = P2pOrderFactor.create(
            user_id,
            kwargs["adv_id"],
            kwargs["price"],
            base_amount=kwargs["base_amount"],
            quote_amount=kwargs["quote_amount"],
            amount_type=kwargs["amount_type"],
            user_pay_channel_id=kwargs["user_pay_channel_id"],
            base=kwargs.get("base"),
            quote=kwargs.get("quote"),
        )
        return order.order_id


@ns.route("/list")
@respond_with_code
class P2pOrderListResource(P2pOrderBase):

    export_marshal_fields = dict(
        order_id=fx_fields.Raw,
        created_at=ex_fields.LocalDateTimeStr(attribute=lambda x: int(x["created_at"].timestamp())),
        base=fx_fields.Raw,
        side=fx_fields.Raw,
        price=fx_fields.String(attribute=lambda x: f"{export_amount(x['price'])} {x['quote']}"),
        quote_amount=fx_fields.String(attribute=lambda x: f"{export_amount(x['quote_amount'])} {x['quote']}"),
        base_amount=fx_fields.String(attribute=lambda x: f"{export_amount(x['base_amount'])} {x['base']}"),
        kyc_name=fx_fields.String(attribute=lambda x: x["opponent_user_info"]["kyc_name"]),
        channel_name=fx_fields.String(attribute=lambda x: x["user_pay_channel"]["name"]),
        status=fx_fields.Raw,
    )

    export_headers = (
        {"field": "order_id", Language.ZH_HANS_CN: "订单编号", Language.EN_US: "Order ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间", Language.EN_US: "Time Created"},
        {"field": "side", Language.ZH_HANS_CN: "订单方向", Language.EN_US: "Order Direction"},
        {"field": "base", Language.ZH_HANS_CN: "币种", Language.EN_US: "Coins"},
        {"field": "price", Language.ZH_HANS_CN: "价格", Language.EN_US: "Price"},
        {"field": "quote_amount", Language.ZH_HANS_CN: "总额", Language.EN_US: "Total Value"},
        {"field": "base_amount", Language.ZH_HANS_CN: "数量", Language.EN_US: "Amount"},
        {"field": "kyc_name", Language.ZH_HANS_CN: "实名", Language.EN_US: "Real Name"},
        {"field": "channel_name", Language.ZH_HANS_CN: "支付方式", Language.EN_US: "Payment Method"},
        {"field": "status", Language.ZH_HANS_CN: "状态", Language.EN_US: "Status"},
    )

    @classmethod
    @require_p2p_permission()
    @ns.use_kwargs(dict(
        opponent_user_id=fields.String(),
        user_type=EnumField(P2pOrderUserType),
        statuses=fields.List(EnumField(P2pOrder.Status)),
        side=EnumField(P2pBusinessType),
        fiat=fields.List(fields.String),
        assets=fields.List(fields.String),
        order_id=fields.String(),
        start_time=TimestampField(),
        end_time=TimestampField(),
        has_unread=fields.Boolean,
        page=PageField,
        limit=LimitField,
        export=fields.Boolean
    ))
    def post(cls, **kwargs):
        """ 获取P2p订单列表 """
        user_id = g.user.id
        query = cls.get_orders_query(user_id, kwargs)
        session_ids = cls.filter_unread_session_ids(user_id)
        unread_query = query.filter(cls.model.session_id.in_(session_ids))
        unread_count = unread_query.count()
        if kwargs.get("has_unread"):
            query = unread_query
        query = query.order_by(cls.model.id.desc())
        if kwargs.get("export"):
            rows = query.all()
            orders = cls.format_order_list(user_id, rows)
            export_data = marshal(orders, cls.export_marshal_fields)
            file_name = datetime.utcnow().strftime('%Y%m%d-p2p-order-history')
            return export_xlsx(file_name, export_data, cls.export_headers)

        paginate = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        page_orders = paginate.items
        session_ids = set(session_ids)
        if page_orders:
            order_id_map = {i.order_id: i.id for i in page_orders}
            complaint_rows = cls.c_model.query.filter(cls.c_model.order_id.in_(list(order_id_map.values()))).all()
            complaint_map = {i.order_id: i.complaint_status for i in complaint_rows}
            page_orders = cls.format_order_list(user_id, page_orders)
            unread_complaint_set = UserUnreadComplaintCache(user_id).get_unread_complaint_set()
            for order in page_orders:
                order_id = order["order_id"]
                order["unread_msg"] = order["session_id"] in session_ids
                complaint_status = complaint_map.get(order_id_map[order_id])
                if complaint_status:
                    order["complaint_status"] = complaint_status.name
                    order["unread_complaint"] = order["complaint_id"] in unread_complaint_set
        return {
            "orders": page_orders,
            "total": paginate.total,
            "unread_order_count": unread_count,
        }

    @classmethod
    def filter_unread_session_ids(cls, user_id) -> list[P2pOrder]:
        im_user_id = cls.get_im_user_id(user_id)
        try:
            seqs = ImServerClient().get_order_unread_seqs(im_user_id)
            session_ids = [i["convo_id"] for i in seqs]
            return session_ids
        except Exception as e:
            current_app.logger.error(f"get_unread_order_session_ids error: {e}")
            return []


@ns.route("/<string:order_id>")
@respond_with_code
class P2pOrderDetailResource(P2pOrderBase):

    @classmethod
    @require_p2p_permission
    def get(cls, order_id, **kwargs):
        """获取订单详情"""
        user_id = g.user.id
        order = cls.get_and_check_order(user_id, order_id)
        new_order = P2pOrderFactor.format_one_order(user_id, order)
        if order.complaint_id:
            complaint = P2pOrderComplaintBase.format_complaint(
                P2pOrderComplaint.query.get(order.complaint_id)
            )
            new_order["complaint"] = complaint
        return new_order


@ns.route("/<string:order_id>/confirm")
@respond_with_code
class P2pOrderConfirmResource(P2pOrderBase):

    @classmethod
    @require_p2p_permission(is_merchant=True, is_trading=True)
    def put(cls, order_id, **kwargs):
        """ 确认订单 """
        user_id = g.user.id
        order = cls.get_merchant_order(user_id, order_id)
        if not order:
            raise RecordNotFound
        P2pOrderFactor(order).confirm(user_id)
        return


@ns.route("/<string:order_id>/pay")
@respond_with_code
class P2pOrderPayResource(P2pOrderBase):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        file_keys=fields.List(fields.String(), validate=lambda x: len(x) <= 3 and all(x), missing=[])
    ))
    def put(cls, order_id, **kwargs):
        """ 订单付款 """
        user_id = g.user.id
        order = cls.get_and_check_order(user_id, order_id)
        P2pOrderFactor(order).pay(user_id, kwargs["file_keys"])


@ns.route("/<string:order_id>/finish")
@respond_with_code
class P2pOrderFinishResource(P2pOrderBase):

    @classmethod
    @require_p2p_permission(is_trading=True)
    @require_2fa(MobileCodeType.P2P_FINISH, allow_sub_account=False)
    @ns.use_kwargs(dict(
        withdraw_password=fields.String(),  # 提现密码
    ))
    def put(cls, order_id, **kwargs):
        """ 完成订单(放币) """
        user = g.user
        check_withdraw_password_by_api(user, kwargs.get('withdraw_password'))
        user_id = user.id
        order = cls.get_and_check_order(user_id, order_id)
        P2pOrderFactor(order).to_finish(user_id)


@ns.route("/<string:order_id>/cancel")
@respond_with_code
class P2pOrderCancelResource(P2pOrderBase):

    @classmethod
    @require_p2p_permission()
    @ns.use_kwargs(dict(
        reason=EnumField(P2pOrder.CancelReason)
    ))
    def put(cls, order_id, **kwargs):
        """取消订单"""
        user_id = g.user.id
        order = cls.get_and_check_order(user_id, order_id)
        P2pOrderFactor(order).user_cancel(reason=kwargs.get("reason"), user_id=user_id)


@ns.route("/active_count")
@respond_with_code
class P2pOrderActiveOrderCountResource(P2pOrderBase):

    @classmethod
    @require_p2p_permission
    def get(cls, **kwargs):
        """ 获取进行中的订单数量 申诉数量"""
        user_id = g.user.id
        order_count = cls.get_active_order_count(user_id)
        complaint_count = cls.get_active_complaint_count(user_id)
        return {
            "order_count": order_count,
            "complaint_count": complaint_count,
            "has_unread_complaint": UserUnreadComplaintCache(user_id).has_unread()
        }

    @classmethod
    def get_active_complaint_count(cls, user_id: int) -> int:
        model = P2pOrderComplaint
        query = model.query.filter(
            model.complaint_status.in_([
                model.Status.CREATED,
                model.Status.PENDING,
            ]),
            or_(
                model.plaintiff_id == user_id,
                model.defendant_id == user_id
            )
        )
        return query.count()


@ns.route("/status")
@respond_with_code
class P2pOrderStatusResource(P2pOrderBase):

    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        order_ids=fields.List(fields.String, required=True)
    ))
    def post(cls, **kwargs):
        """ 获取订单状态（前端轮询使用） """
        user_id = g.user.id
        orders = cls.model.get_user_query(user_id).filter(
            cls.model.order_id.in_(kwargs["order_ids"]),
        ).with_entities(
            cls.model.order_id,
            cls.model.status,
            cls.model.id,
            cls.model.complaint_id
        ).all()
        order_ids = [i.id for i in orders]
        complaint_rows = cls.c_model.query.filter(cls.c_model.order_id.in_(order_ids)).all()
        complaint_map = {i.order_id: i.complaint_status for i in complaint_rows}
        unread_complaint_set = UserUnreadComplaintCache(user_id).get_unread_complaint_set()
        ret = []
        for i in orders:
            tmp = {
                "order_id": i.order_id,
                "status": i.status.name,
            }
            if status := complaint_map.get(i.id):
                tmp["complaint_status"] = status.name
                tmp["unread_complaint"] = i.complaint_id in unread_complaint_set
            ret.append(tmp)
        return ret


class P2pOrderComplaintBase(Resource):
    model = P2pOrderComplaint
    order_model = P2pOrder

    @classmethod
    def format_complaint(cls, complaint: P2pOrderComplaint) -> dict:
        complaint_dict = P2pOrderComplaintBiz.format_complaint(complaint)
        pop_fields = ["remark", "updated_at", "audit_id"]
        for field in pop_fields:
            complaint_dict.pop(field, "")
        biz_id_map = P2pUtils.get_biz_user_id_map([complaint.plaintiff_id, complaint.defendant_id])
        complaint_dict["plaintiff_id"] = biz_id_map[complaint.plaintiff_id]
        complaint_dict["defendant_id"] = biz_id_map[complaint.defendant_id]
        return complaint_dict

    @classmethod
    def format_record(cls, record: P2pOrderComplaintRecord) -> dict:
        record_dict = P2pComplaintRecordBiz.format_record(record)
        pop_fields = ["updated_at", "id"]
        for field in pop_fields:
            record_dict.pop(field, "")
        biz_id_map = P2pUtils.get_biz_user_id_map([record.user_id, record.to_user_id])
        record_dict["user_id"] = biz_id_map.get(record.user_id)
        record_dict["to_user_id"] = biz_id_map.get(record.to_user_id)
        return record_dict


@ns.route("/complaint/list")
@respond_with_code
class P2pOrderComplaintListResource(P2pOrderComplaintBase):
    model = P2pOrderComplaint
    order_model = P2pOrder

    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        order_id=fields.String(),
        user_type=EnumField(P2pOrderUserType),
        statuses=fields.List(EnumField(P2pOrderComplaint.Status)),
        side=EnumField(P2pBusinessType),
        fiat=fields.List(fields.String),
        assets=fields.List(fields.String),
        start_time=TimestampField(),
        end_time=TimestampField(),
        page=PageField,
        limit=LimitField,
    ))
    def post(cls, **kwargs):
        """ 获取申诉列表 """
        user_id = g.user.id
        query = db.session.query(cls.model, cls.order_model).join(
            cls.model,
            cls.model.order_id == cls.order_model.id
        ).filter(or_(
            cls.model.plaintiff_id == user_id,
            cls.model.defendant_id == user_id
        ))
        if status := kwargs.get("statuses"):
            query = query.filter(cls.model.complaint_status.in_(status))
        if user_type := kwargs.get("user_type"):
            if user_type == P2pOrderUserType.CUSTOMER:
                query = query.filter(cls.order_model.customer_id == user_id)
            else:
                query = query.filter(cls.order_model.merchant_id == user_id)
        if order_id := kwargs.get("order_id"):
            query = query.filter(cls.order_model.order_id.contains(order_id))
        if start_time := kwargs.get("start_time"):
            query = query.filter(cls.order_model.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            query = query.filter(cls.order_model.created_at < end_time)
        if side := kwargs.get("side"):
            query = query.filter(or_(
                and_(cls.order_model.customer_id == user_id, cls.order_model.side == side),
                and_(cls.order_model.merchant_id == user_id, cls.order_model.side == P2pBusinessType.reverse(side))
            ))
        if fiat_list := kwargs.get("fiat"):
            query = query.filter(cls.order_model.quote.in_(fiat_list))
        if assets := kwargs.get("assets"):
            query = query.filter(cls.order_model.base.in_(assets))
        query = query.order_by(cls.model.id.desc())
        paginate = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)

        items = paginate.items
        complaint_list = [i[0] for i in items]
        order_list = [i[1] for i in items]
        new_complaint_list = []
        if order_list:
            order_id_map = {i.id: i.order_id for i in order_list}
            new_order_list = P2pOrderBase.format_order_list(user_id, order_list)
            order_map = {i["order_id"]: i for i in new_order_list}
            complaint_pop_fields = ["id", "updated_at", "remark", "order_id"]
            unread_id_set = UserUnreadComplaintCache(user_id).get_unread_complaint_set()
            for complaint in complaint_list:
                new_complaint = cls.format_complaint(complaint)
                for field in complaint_pop_fields:
                    new_complaint.pop(field, "")
                new_complaint["unread_complaint"] = complaint.id in unread_id_set
                new_complaint["complaint_created_at"] = complaint.created_at
                new_complaint.update(order_map[order_id_map[complaint.order_id]])
                new_complaint_list.append(new_complaint)

        # 获取未读数量
        return dict(
            total=paginate.total,
            complaints=new_complaint_list,
        )

    @classmethod
    def format_data(cls, data):
        return data.to_dict(enum_to_name=True)


@ns.route("/<string:order_id>/complaint")
@respond_with_code
class P2pOrderComplaintDetailResource(P2pOrderComplaintBase):
    model = P2pOrderComplaint
    order_model = P2pOrder

    @classmethod
    @require_p2p_permission()
    @limit_user_frequency(1, 60)
    @ns.use_kwargs(dict(
        desc=fields.String(required=True),
        reason=EnumField(P2pOrderComplaint.Reason),
        image_keys=fields.List(fields.String),
        video_keys=fields.List(fields.String),
    ))
    def post(cls, **kwargs):
        """ 创建订单申诉 """
        user_id = g.user.id
        reason = kwargs.get("reason")
        order = P2pOrderBase.get_user_order(user_id, kwargs["order_id"])
        if order.complaint_id:
            complaint = cls.get_user_complaint(user_id, order.id)
            P2pOrderComplaintBiz(complaint).reopen(
                user_id, order, kwargs["desc"], kwargs.get("image_keys"), kwargs.get("video_keys"))
        else:
            if not reason:
                raise InvalidArgument("need reason")
            P2pOrderComplaintBiz.create_complaint(
                user_id, order, reason, kwargs["desc"],
                kwargs.get("image_keys"), kwargs.get("video_keys"))

    @classmethod
    @require_p2p_permission
    def get(cls, order_id, **kwargs):
        """ 获取申诉详情 """
        user_id = g.user.id
        order = cls.order_model.query.filter(cls.order_model.order_id == order_id).first()
        if not order:
            raise RecordNotFound
        complaint = cls.get_user_complaint(user_id, order.id)
        if not complaint:
            raise RecordNotFound
        complaint_dict = cls.format_complaint(complaint)
        record_rows = P2pComplaintRecordBiz(complaint).ger_complaint_record(user_id)
        complaint_dict["records"] = [cls.format_record(i) for i in record_rows]
        if record_rows:
            P2pComplaintRecordBiz(complaint).update_newest_anchor(user_id)
        return {
            "complaint": complaint_dict,
            "order": P2pOrderFactor.format_one_order(user_id, order)
        }

    @classmethod
    @require_p2p_permission()
    @ns.use_kwargs(dict(
        desc=fields.String(required=True),
        image_keys=fields.List(fields.String),
        video_keys=fields.List(fields.String),
    ))
    def put(cls, order_id, **kwargs):
        """ 增加申诉内容 """
        user_id = g.user.id
        order = cls.order_model.query.filter(cls.order_model.order_id == order_id).first()
        if not order:
            raise RecordNotFound
        complaint = cls.get_user_complaint(user_id, order.id)
        if not complaint:
            raise RecordNotFound
        P2pOrderComplaintBiz(complaint).user_update(
            user_id,
            kwargs["desc"],
            kwargs.get("image_keys"),
            kwargs.get("video_keys"),
        )

    @classmethod
    def get_user_complaint(cls, user_id: int, order_id: int) -> P2pOrderComplaint:
        """获取申诉"""
        complaint = cls.model.query.filter(
            or_(
                cls.model.plaintiff_id == user_id,
                cls.model.defendant_id == user_id
            ),
            cls.model.order_id == order_id
        ).first()
        return complaint



@ns.route("/<string:order_id>/complaint/cancel")
@respond_with_code
class P2pOrderComplaintResource(Resource):
    model = P2pOrderComplaint
    order_model = P2pOrder

    @classmethod
    @require_p2p_permission()
    def put(cls, order_id, **kwargs):
        """ 取消申诉 """
        user_id = g.user.id
        order = cls.order_model.query.filter(cls.order_model.order_id == order_id).first()
        if not order:
            raise RecordNotFound
        complaint = cls.model.query.filter(
            cls.model.plaintiff_id == user_id,
            cls.model.order_id == order.id
        ).first()
        if not complaint:
            raise RecordNotFound
        P2pOrderComplaintBiz(complaint).cancel(user_id)
