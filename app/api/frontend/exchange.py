# -*- coding: utf-8 -*-
import json
from decimal import Decimal, ROUND_DOW<PERSON>
from typing import List, Dict

from flask import g, current_app
from flask_babel import gettext

from app.caches.system import MarketMaintainCache
from app.common import PrecisionEnum, OrderSideType
from app.models import db, Market
from app.models.exchange import AssetExchangeOrder, AssetExchangeSysUser, SysAssetExchangeOrder, ExchangeFeeType
from app.api.common import Namespace, Resource, require_login, respond_with_code, json_string_success, \
    require_ip_not_only_withdrawal, lock_request
from app.api.common.decorators import require_trade_password, trade_permission_validate
from app.api.common.fields import LimitField, PageField, PositiveDecimalField, AssetField, EnumField, BoolField
from app.business import (
    ServerClient, SPOT_ACCOUNT_ID, PriceManager, SiteSettings, BusinessSettings,
    LockKeys, CacheLock, mem_cached,
)
from app.business.user import require_user_not_only_withdrawal, require_user_kyc
from app.business.exchange import MID_ASSETS, process_exchange_order_task
from app.caches import MarketCache
from app.caches.spot import (
    ExchangeAssetCache,
    ExchangeVisibleAssetsCache,
    ExchangeMarketCache,
    ExchangeRankCache,
    ExchangePreviewPathCache,
    ExchangeTargetAssetRankCache,
    ExchangeAvailableAssetsCache,
    ExchangeTargetAssetCache,
)
from app.exceptions import (
    ErrorWithResponseCode,
    InvalidArgument,
    InsufficientBalance,
    OrderPlacementForbidden,
    OrderExceptionMap,
    OrderException,
    TwoFactorAuthenticationRequired,
)
from app.assets.asset import get_asset_config, is_pre_asset
from app.utils import quantize_amount


ns = Namespace("Asset-Exchange")


class ExchangePathSorter:

    @classmethod
    def sort(cls, source_asset: str, source_amount: Decimal, target_asset: str, exchange_paths: List[List[str]]) -> List[List[str]]:
        """
        对兑换路径进行排序，按`获得目标币种数`倒叙，`获得目标币种数`相同时优先直接兑换
        """
        if len(exchange_paths) <= 1:
            return exchange_paths

        res = []
        for exchange_path in exchange_paths:
            try:
                target_amount = cls.estimate_target_amount(source_asset, source_amount, target_asset, exchange_path)
            except:  # noqa
                target_amount = Decimal()
            res.append([exchange_path, target_amount])

        res.sort(key=lambda x: (x[1], -len(x[0])), reverse=True)
        return [i[0] for i in res]

    @classmethod
    def estimate_target_amount(cls, source_asset: str, source_amount: Decimal, target_asset: str, exchange_path: list[str]) -> Decimal:
        if len(exchange_path) == 1:
            market = exchange_path[0]
            target_deal_amount, target_fee_amount = cls._estimate_market_target_amount(source_asset, source_amount, market)
            res_target_amount = quantize_amount(target_deal_amount, 8) - quantize_amount(target_fee_amount, 8)
            res_target_amount = max(res_target_amount, Decimal(0))
            return res_target_amount
        elif len(exchange_path) == 2:
            first_market = exchange_path[0]
            target1_deal_amount, target1_fee_amount = cls._estimate_market_target_amount(source_asset, source_amount, first_market)
            target1_amount = quantize_amount(target1_deal_amount, 8) - quantize_amount(target1_fee_amount, 8)
            target1_amount = max(target1_amount, Decimal(0))
            second_market = exchange_path[1]
            second_market_info = MarketCache(second_market).dict
            if target_asset == second_market_info["base_asset"]:
                source_asset2 = second_market_info['quote_asset']
            elif target_asset == second_market_info["quote_asset"]:
                source_asset2 = second_market_info["base_asset"]
            else:
                raise ValueError(f"invalid second_market:{second_market} target:{target_asset}")
            target2_deal_amount, target2_fee_amount = cls._estimate_market_target_amount(source_asset2, target1_amount, second_market)
            res_target_amount = quantize_amount(target2_deal_amount, 8) - quantize_amount(target2_fee_amount, 8)
            res_target_amount = max(res_target_amount, Decimal(0))
            return res_target_amount
        else:
            raise ValueError(f"invalid path: {exchange_path}")

    @classmethod
    def _estimate_market_target_amount(cls, source_asset: str, source_amount: Decimal, market: str) -> tuple[Decimal, Decimal]:
        market_info = MarketCache(market).dict
        base_asset = market_info["base_asset"]
        quote_asset = market_info["quote_asset"]
        if source_asset == base_asset:
            side = OrderSideType.SELL
        elif source_asset == quote_asset:
            side = OrderSideType.BUY
        else:
            raise ValueError(f"invalid market {market} {source_asset}")

        depths = market_info["depths"]
        min_merge, max_merge = Decimal(market_info["default_depth"]), Decimal(max(depths))
        order_book_merges = []
        start_merge = min_merge
        while start_merge <= max_merge:
            order_book_merges.append(start_merge)
            start_merge *= 10

        client = ServerClient()
        last_target_amount = Decimal('0')
        taker_fee_rate = market_info['taker_fee_rate']
        for _merge in order_book_merges:
            depth = client.market_order_depth(market=market, limit=20, interval=str(_merge))
            target_amount = Decimal('0')
            remain_source_amount = source_amount
            orders = depth["bids"] if side == OrderSideType.SELL else depth["asks"]
            for item in orders:
                # item: [price, amount]
                if side == OrderSideType.SELL:
                    # 卖出：失去BTC 得到USDT
                    _deal_amount = min(remain_source_amount, Decimal(item[1]))
                    remain_source_amount -= _deal_amount
                    target_amount += _deal_amount * Decimal(item[0])
                else:
                    # 买入：失去USDT 得到BTC
                    _deal_amount = min(remain_source_amount, Decimal(item[0]) * Decimal(item[1]))
                    remain_source_amount -= _deal_amount
                    target_amount += _deal_amount / Decimal(item[0])
                assert remain_source_amount >= 0
                if remain_source_amount == 0:
                    break
            if remain_source_amount == 0:
                return target_amount, target_amount * taker_fee_rate
            if _merge == order_book_merges[-1]:
                last_target_amount = target_amount
        return last_target_amount, last_target_amount * taker_fee_rate


@ns.route("/visible-assets")
@respond_with_code
class AssetExchangeVisibleAssetsResource(Resource):
    @classmethod
    def get(cls):
        """ 返回可见的兑换币种列表（下拉列表） """
        cache = ExchangeVisibleAssetsCache()
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route("/available-assets")
@respond_with_code
class AssetExchangeAvailableAssetsResource(Resource):
    @classmethod
    def get(cls):
        """ 返回支持的兑换币种数组列表，不包括目标币种、最大兑换市值 """
        cache = ExchangeAvailableAssetsCache()
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route("/assets")
@respond_with_code
class AssetExchangeAssetsResource(Resource):
    @classmethod
    @mem_cached(120)
    def get(cls):
        """ 支持的兑换币种列表，已废弃，兼容老版本APP保留 """
        cache = ExchangeAssetCache()
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route("/target-assets")
@respond_with_code
class ExchangeTargetAssetsResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            source_asset=AssetField(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 根据源币种 获取支持兑换的 目标币种列表 """
        return cls._get(kwargs["source_asset"])

    @classmethod
    @mem_cached(120)
    def _get(cls, source_asset: str):
        cache = ExchangeTargetAssetCache()
        if data := cache.hget(source_asset):
            return json_string_success(data)
        return []


@ns.route("/path")
@respond_with_code
class AssetExchangePathResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            source_asset=AssetField(required=True),
            target_asset=AssetField(required=True),
            restricted=BoolField(missing=True),
        )
    )
    def get(cls, **kwargs):
        """ 获取兑换路径 """
        source_asset = kwargs["source_asset"]
        target_asset = kwargs["target_asset"]
        if source_asset == target_asset:
            raise InvalidArgument(message=AssetExchangeOrdersResource.NOT_SUPPORTED_ASSET_PAIR_MSG)

        exchange_markets_list = AssetExchangeOrdersResource.build_exchange_path_list(source_asset, target_asset)
        exchange_market_info_map = ExchangeMarketCache().read()
        if kwargs.get("restricted") is False:
            # 不强制检查深度，返回兑换市场路径，用于计算参考价格
            def _markets_deep_enough(_markets):
                for _m in _markets:
                    if _m not in exchange_market_info_map:
                        return False
                return True

            if exchange_markets_list:
                for markets in exchange_markets_list:
                    # 优先返回深度足够的
                    if _markets_deep_enough(markets):
                        return markets
                return exchange_markets_list[0]
            else:
                raise InvalidArgument(message=AssetExchangeOrdersResource.DEEP_NOT_ENOUGH_MSG)

        disabled_markets = BusinessSettings.disabled_exchange_markets

        def _validate_markets(_markets):
            for _m in _markets:
                if _m in disabled_markets or _m not in exchange_market_info_map:
                    return False
            return True

        for markets in exchange_markets_list:
            if _validate_markets(markets):
                return markets

        raise InvalidArgument(message=AssetExchangeOrdersResource.DEEP_NOT_ENOUGH_MSG)


@ns.route("/preview")
@respond_with_code
class AssetExchangeOrderPreviewResource(Resource):
    @classmethod
    @require_login
    @trade_permission_validate(is_spot=True, account_id=0)
    @ns.use_kwargs(
        dict(
            source_asset=AssetField(required=True),
            target_asset=AssetField(required=True),
            source_asset_amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 预览兑换订单路径（提前计算兑换路径） """
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        require_ip_not_only_withdrawal()
        if not SiteSettings.exchange_order_enabled:
            raise InvalidArgument(message=AssetExchangeOrdersResource.NOT_SUPPORTED_MSG)

        source_asset = kwargs["source_asset"]
        target_asset = kwargs["target_asset"]
        source_asset_amount = kwargs["source_asset_amount"]
        if source_asset == target_asset:
            raise InvalidArgument(message=AssetExchangeOrdersResource.NOT_SUPPORTED_ASSET_PAIR_MSG)
        if ServerClient().get_protect_status()['status']:
            raise InvalidArgument(message=AssetExchangeOrdersResource.NOT_SUPPORTED_MSG)
        if is_pre_asset(source_asset) or is_pre_asset(target_asset):
            raise InvalidArgument(message=AssetExchangeOrdersResource.NOT_SUPPORTED_MSG)

        exchange_markets_list = AssetExchangeOrdersResource.build_exchange_path_list(source_asset, target_asset)
        validated_exc_paths = AssetExchangeOrdersResource.get_validated_exchange_path(
            source_asset, source_asset_amount, exchange_markets_list,
        )
        sorted_validated_exc_paths = ExchangePathSorter.sort(source_asset, source_asset_amount, target_asset, validated_exc_paths)
        validated_exchange_markets = sorted_validated_exc_paths[0]
        ExchangePreviewPathCache(source_asset, target_asset, source_asset_amount).save_path(validated_exchange_markets)
        return {
            "markets": validated_exchange_markets,
        }


@ns.route("/orders")
@respond_with_code
class AssetExchangeOrdersResource(Resource):

    NOT_SUPPORTED_MSG = gettext("暂不支持兑换")
    NOT_SUPPORTED_ASSET_PAIR_MSG = gettext("该币对暂不支持兑换")
    DEEP_NOT_ENOUGH_MSG = gettext("该币对深度不足，暂不支持兑换")

    @classmethod
    def build_exchange_path_list(cls, source_asset: str, target_asset: str) -> List[List[str]]:
        # 构建所有可以用来兑换的市场列表（包括直接兑换、跨市场兑换）
        market_list = MarketCache.list_online_markets()

        result = []
        concat_market1 = f"{source_asset}{target_asset}"
        if concat_market1 in market_list:
            result.append([concat_market1])
        concat_market2 = f"{target_asset}{source_asset}"
        if concat_market2 in market_list:
            result.append([concat_market2])

        for mid_asset in MID_ASSETS:
            if source_asset == mid_asset or target_asset == mid_asset:
                continue
            m1 = f"{source_asset}{mid_asset}"
            m2 = f"{target_asset}{mid_asset}"
            if m1 in market_list and m2 in market_list:
                result.append([m1, m2])

        if not result:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_ASSET_PAIR_MSG)
        return result

    @classmethod
    def get_validated_exchange_path(
        cls,
        source_asset: str,
        source_asset_amount: Decimal,
        exchange_markets_list: List[List[str]],
        validate_all: bool = True,
    ) -> List[List[str]]:
        source_asset_price = PriceManager.asset_to_usd(source_asset)

        validated_exchange_paths = []
        disabled_markets = BusinessSettings.disabled_exchange_markets
        exchange_market_info_map = ExchangeMarketCache().read()
        market_maintains = MarketMaintainCache.get_market_maintains()
        last_err = None
        for idx, exchange_markets_ in enumerate(exchange_markets_list):
            try:
                cls.validate_(
                    disabled_markets,
                    exchange_market_info_map,
                    exchange_markets_,
                    source_asset,
                    source_asset_amount,
                    source_asset_price,
                    market_maintains,
                )
            except ErrorWithResponseCode as _e:
                last_err = _e
                continue
            else:
                validated_exchange_paths.append(exchange_markets_)
                if not validate_all:
                    break

        if not validated_exchange_paths:
            if last_err:
                raise last_err
            raise InvalidArgument(message=cls.NOT_SUPPORTED_ASSET_PAIR_MSG)

        # last check balance
        client = ServerClient(current_app.logger)
        balance_dict = client.get_user_balances(
            user_id=g.user.id,
            asset=source_asset,
            account_id=SPOT_ACCOUNT_ID,
        )
        source_available = balance_dict.get(source_asset, {}).get("available", Decimal("0"))
        if source_available < source_asset_amount:
            raise InsufficientBalance

        return validated_exchange_paths

    @classmethod
    def validate_(
        cls,
        disabled_markets: List[str],
        exchange_market_info_map: Dict[str, str],
        exchange_markets_: List[str],
        source_asset: str,
        source_asset_amount: Decimal,
        source_asset_price: Decimal,
        market_maintains: dict,
    ):
        max_exchange_usds = []
        for market in exchange_markets_:
            if market not in exchange_market_info_map:
                raise InvalidArgument(message=cls.DEEP_NOT_ENOUGH_MSG)
            if market in disabled_markets:
                raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)
            if _ := market_maintains.get(market):
                raise InvalidArgument(
                    message=gettext(
                        "%(market)s市场暂停交易中，无法发起兑换",
                        market=market,
                    )
                )
            usd = Decimal(json.loads(exchange_market_info_map[market])["max_exchange_usd"])
            max_exchange_usds.append(usd)

        # check amount
        max_exchange_usd = min(max_exchange_usds) * Decimal("1.05")
        max_exchange_amount = quantize_amount(max_exchange_usd / source_asset_price, 8)
        if source_asset_amount > max_exchange_amount:
            raise InvalidArgument(
                message=gettext(
                    "超过最大兑换数量 %(max_exchange_amount)s %(asset)s",
                    max_exchange_amount=max_exchange_amount,
                    asset=source_asset,
                )
            )
        market1_cache = MarketCache(exchange_markets_[0])
        if not market1_cache.check_order_permission(g.user.main_user_type):
            raise OrderPlacementForbidden
        if market1_cache.dict["status"] != Market.Status.ONLINE:
            # 集合竞价中不能下市价单，可能会导致兑换不能正常进行
            raise InvalidArgument(message=cls.NOT_SUPPORTED_ASSET_PAIR_MSG)
        if len(exchange_markets_) == 2:
            market2_cache = MarketCache(exchange_markets_[1])
            if not market2_cache.check_order_permission(g.user.main_user_type):
                raise OrderPlacementForbidden
            if market2_cache.dict["status"] != Market.Status.ONLINE:
                raise InvalidArgument(message=cls.NOT_SUPPORTED_ASSET_PAIR_MSG)
        market_info = market1_cache.dict
        if source_asset == market_info["base_asset"]:
            asset_precision = market_info["base_asset_precision"]
        elif source_asset == market_info["quote_asset"]:
            asset_precision = market_info["quote_asset_precision"]
        else:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_ASSET_PAIR_MSG)
        source_asset_amount = quantize_amount(source_asset_amount, asset_precision)
        min_order_amount = Decimal("1") if source_asset == "USDT" else get_asset_config(source_asset).min_order_amount
        if source_asset_amount < min_order_amount:
            raise OrderExceptionMap[OrderException.LESS_THAN_LEAST_AMOUNT]

    @classmethod
    @require_login
    @lock_request(with_user=True)
    @trade_permission_validate(is_spot=True, account_id=0)
    @require_trade_password
    @ns.use_kwargs(
        dict(
            source_asset=AssetField(required=True),
            target_asset=AssetField(required=True),
            source_asset_amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 提交兑换订单 """
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        exc_order = cls.new_order(g.user, kwargs)
        process_exchange_order_task.delay(exc_order.id)
        return {"id": exc_order.id}

    @classmethod
    def new_order(cls, user, kwargs: dict) -> AssetExchangeOrder:
        require_ip_not_only_withdrawal()
        if not SiteSettings.exchange_order_enabled:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)
        require_user_not_only_withdrawal(user)
        require_user_kyc(user)

        source_asset = kwargs["source_asset"]
        target_asset = kwargs["target_asset"]
        source_asset_amount = kwargs["source_asset_amount"]
        if source_asset == target_asset:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_ASSET_PAIR_MSG)
        if ServerClient().get_protect_status()['status']:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)
        if is_pre_asset(source_asset) or is_pre_asset(target_asset):
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)

        exchange_markets_list = cls.build_exchange_path_list(source_asset, target_asset)
        validated_exc_paths = []
        prev_cached_path = ExchangePreviewPathCache(source_asset, target_asset, source_asset_amount).get_path()
        if prev_cached_path and prev_cached_path in exchange_markets_list:
            # 优先使用之前预览过的兑换路径
            validated_exc_paths = cls.get_validated_exchange_path(source_asset, source_asset_amount, [prev_cached_path])
        if not validated_exc_paths:
            validated_exc_paths = cls.get_validated_exchange_path(source_asset, source_asset_amount, exchange_markets_list)
        sorted_validated_exc_paths = ExchangePathSorter.sort(source_asset, source_asset_amount, target_asset, validated_exc_paths)
        validated_exchange_markets = sorted_validated_exc_paths[0]
        if not prev_cached_path:
            ExchangePreviewPathCache(source_asset, target_asset, source_asset_amount).save_path(validated_exchange_markets)

        market_info = MarketCache(validated_exchange_markets[0]).dict
        if source_asset == market_info["base_asset"]:
            asset_precision = market_info["base_asset_precision"]
        else:
            asset_precision = market_info["quote_asset_precision"]
        source_asset_amount = quantize_amount(source_asset_amount, asset_precision)

        user_id = user.id
        with CacheLock(key=LockKeys.exchange_order_sys_user(), wait=False):
            db.session.rollback()

            sys_user: AssetExchangeSysUser = (
                AssetExchangeSysUser.query.filter(AssetExchangeSysUser.status == AssetExchangeSysUser.Status.USABLE)
                .order_by(AssetExchangeSysUser.id.asc())
                .first()
            )
            if not sys_user:
                raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)

            sys_user.status = AssetExchangeSysUser.Status.UNUSABLE
            exc_order = AssetExchangeOrder(
                user_id=user_id,
                sys_user_id=sys_user.user_id,
                exchange_path=json.dumps(validated_exchange_markets),
                source_asset=source_asset,
                source_asset_amount=source_asset_amount,
                target_asset=target_asset,
            )
            db.session.add(exc_order)
            db.session.commit()
        return exc_order

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            source_asset=AssetField,
            target_asset=AssetField,
            status=EnumField(["PROCESSING", "FINISHED"]),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 兑换订单列表 """
        user_id = g.user.id
        q = AssetExchangeOrder.query.filter(
            AssetExchangeOrder.user_id == user_id,
        ).order_by(AssetExchangeOrder.id.desc())
        if source_asset := kwargs.get("source_asset", ""):
            if source_asset := source_asset.strip():
                q = q.filter(AssetExchangeOrder.source_asset == source_asset)
        if target_asset := kwargs.get("target_asset", ""):
            if target_asset := target_asset.strip():
                q = q.filter(AssetExchangeOrder.target_asset == target_asset)
        finished_statues = [AssetExchangeOrder.Status.FAILED, AssetExchangeOrder.Status.FINISHED]
        if status := kwargs.get("status"):
            if status == "PROCESSING":
                q = q.filter(AssetExchangeOrder.status.not_in(finished_statues))
            else:
                q = q.filter(AssetExchangeOrder.status.in_(finished_statues))

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows = pagination.items
        items = []
        for r in rows:
            if r.status in finished_statues:
                if r.status == AssetExchangeOrder.Status.FINISHED and r.target_asset_exchanged_amount == Decimal():
                    # 未成交
                    status = "FAILED"
                else:
                    status = r.status.name
            else:
                status = "PROCESSING"
            items.append(
                dict(
                    id=r.id,
                    time=int(r.created_at.timestamp()),
                    source_asset=r.source_asset,
                    source_asset_amount=r.source_asset_amount,
                    source_asset_exchanged_amount=r.source_asset_exchanged_amount,
                    target_asset=r.target_asset,
                    target_asset_exchanged_amount=r.target_asset_exchanged_amount,
                    status=status,
                    result=r.result.name if r.result else None,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/orders/<int:order_id>")
@respond_with_code
class AssetExchangeOrderDetailResource(Resource):
    @classmethod
    @require_login
    def get(cls, order_id):
        """ 兑换订单明细 """
        user_id = g.user.id
        order: AssetExchangeOrder = AssetExchangeOrder.query.filter(
            AssetExchangeOrder.user_id == user_id,
            AssetExchangeOrder.id == order_id,
        ).first()
        if not order:
            raise InvalidArgument

        if not (order.status == AssetExchangeOrder.Status.FINISHED and order.result and order.result != AssetExchangeOrder.Result.FAILED):
            # 不是成功兑换 不展示明细
            return {**cls.order_to_dict(order), "details": []}

        sys_orders: List[SysAssetExchangeOrder] = (
            SysAssetExchangeOrder.query.filter(
                SysAssetExchangeOrder.exchange_order_id == order_id,
                SysAssetExchangeOrder.type == SysAssetExchangeOrder.Type.EXCHANGE,  # 不展示买回的系统兑换单
            )
            .order_by(SysAssetExchangeOrder.id.asc())
            .all()
        )
        markets = {i.market for i in sys_orders}
        market_info_map = {m: MarketCache(m).dict for m in markets}

        details = []
        for so in sys_orders:
            market_info = market_info_map[so.market]
            side = OrderSideType.SELL if so.target_asset == market_info["quote_asset"] else OrderSideType.BUY
            if so.result == SysAssetExchangeOrder.Result.ALL:
                # 完全兑换时返回源币种数目，exchanged_amount可能存在最小精度的差值
                source_asset_exchanged_amount = so.source_asset_amount
            else:
                source_asset_exchanged_amount = so.source_asset_exchanged_amount
            if so.fee_type == ExchangeFeeType.SERVER:
                # 成交数 不包含收的手续费
                target_asset_exchanged_amount = so.target_asset_exchanged_amount + so.fee_amount
            else:
                target_asset_exchanged_amount = so.target_asset_exchanged_amount
            detail = {
                "time": int(so.updated_at.timestamp()),
                "market": so.market,
                "side": side.name,
                "source_asset": so.source_asset,
                "source_asset_exchanged_amount": source_asset_exchanged_amount,
                "target_asset": so.target_asset,
                "target_asset_exchanged_amount": target_asset_exchanged_amount,
                "fee_asset": so.fee_asset,
                "fee_amount": so.fee_amount,
            }
            details.append(detail)

        return {
            **cls.order_to_dict(order),
            "details": details,
        }

    @classmethod
    def order_to_dict(cls, order: AssetExchangeOrder) -> Dict:
        finished_statues = [AssetExchangeOrder.Status.FAILED, AssetExchangeOrder.Status.FINISHED]
        if order.status in finished_statues:
            if order.status == AssetExchangeOrder.Status.FINISHED and order.target_asset_exchanged_amount == Decimal():
                # 未成交
                status = "FAILED"
            else:
                status = order.status.name
        else:
            status = "PROCESSING"
        return dict(
            id=order.id,
            time=int(order.created_at.timestamp()),
            source_asset=order.source_asset,
            source_asset_amount=order.source_asset_amount,
            source_asset_exchanged_amount=order.source_asset_exchanged_amount,
            target_asset=order.target_asset,
            target_asset_exchanged_amount=order.target_asset_exchanged_amount,
            status=status,
            result=order.result.name if order.result else None,
        )


@ns.route("/rank")
@respond_with_code
class AssetExchangeRankResource(Resource):
    @classmethod
    @mem_cached(120)
    def get(cls):
        """ 热门兑换榜 """
        cache = ExchangeRankCache()
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route("/rank/assets")
@respond_with_code
class AssetExchangeAssetRankResource(Resource):
    @classmethod
    @mem_cached(120)
    def get(cls):
        """ 热门目标币种榜 """
        cache = ExchangeTargetAssetRankCache()
        if data := cache.read():
            return json_string_success(data)
        return []
