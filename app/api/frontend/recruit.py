from flask import g

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField
from app.exceptions import InvalidArgument
from app.models.recruit import Job, JobContent

ns = Namespace('Recruitment')


class JobMixin:
    langs = JobContent.AVAILABLE_LANGS

    @classmethod
    def _get_job_content_mapping(cls, ids):
        model = JobContent
        rows = model.query.with_entities(
            model.job_id,
            model.lang,
            model.title,
            model.content,
        ).filter(
            model.job_id.in_(ids)
        ).all()

        ret = {}
        for row in rows:
            ret.setdefault(row.job_id, {}).update({
                row.lang.name: {
                    'title': row.title,
                    'content': row.content,
                }
            })
        return ret

    @classmethod
    def _get_lang(cls):
        lang = Language(g.lang)
        if lang not in JobContent.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        return lang


@ns.route('/jobs')
@respond_with_code
class JobsResource(JobMixin, Resource):
    model = Job

    @classmethod
    @ns.use_kwargs(dict(
        job_type=EnumField(model.JobType, required=False)
    ))
    def get(cls, **kwargs):
        """首页-CoinEx 招聘-职位列表"""
        query = cls.get_query_by(params=kwargs)
        records = query.all()

        ids = {e.id for e in records}
        job_content_mapping = cls._get_job_content_mapping(ids)

        lang = cls._get_lang()

        items = {}
        for item in records:
            hans = job_content_mapping.get(item.id, {}).get(lang.name) or {}
            title = hans.get('title') or ''
            tmp = item.to_dict(enum_to_name=True)
            tmp.update(title=title)
            items.setdefault(item.job_type.name, []).append(tmp)

        return dict(
            items=items,
        )

    @classmethod
    def get_query_by(cls, params):
        model = cls.model
        query = model.query.filter(
            model.status == model.Status.VALID,
        ).order_by(
            model.id.desc()
        )
        if job_type := params.get('job_type'):
            query = query.filter(model.job_type == job_type)

        return query


@ns.route('/job/<int:id_>')
@respond_with_code
class JobResource(JobMixin, Resource):
    model = Job

    @classmethod
    def get(cls, id_):
        obj = cls._get_obj(id_)
        ret = obj.to_dict(enum_to_name=True)
        lang = cls._get_lang()

        contents_mapping = cls._get_job_content_mapping(ids=[id_])
        lang_to_contents = contents_mapping.get(obj.id) or {}
        contents = lang_to_contents.get(lang.name) or {}
        ret.update(dict(
            job_title=contents.get('title') or '',
            job_content=contents.get('content') or '',
        ))
        return ret

    @classmethod
    def _get_obj(cls, id_):
        obj = cls.model.query.get(id_)
        if not obj:
            raise InvalidArgument
        if obj.status is cls.model.Status.DELETED:
            raise InvalidArgument
        return obj
