# -*- coding: utf-8 -*-
import datetime
import time
from decimal import Decimal

from flask import g
from webargs import fields as wa_fields

from app.api.common.request import get_request_platform
from app.business.clients.server import ServerClient

from ..common import (Namespace, Resource, respond_with_code, require_login)
from ..common.fields import TimestampField, EnumField
from ...business import (
    UserPreferences, cached,
)
from ...business.account_pl import (
    format_deal_snapshot_data, get_account_deal_value_cache_data, get_deal_snapshot_data, \
        get_snapshot_data_new, get_snapshot_sum_profit_data, TOTAL_ACCOUNT, get_snapshot_data,
    format_profit_snapshot_data, check_today_data_ready, RealtimeAccountProfitLossProcessor, \
        get_account_pl_cache_data,
)
from ...business.profit_loss import MarketProfitLossAnalyzer, UserDealAnalyzer
from ...caches import MarginAccountNameCache
from ...caches.profit_loss import UserAccountPLDataCache
from ...common import AccountBalanceType
from ...exceptions import InvalidArgument, ConfirmationRequired, DataNotReady
from ...models import db
from ...utils import today, current_timestamp

url_prefix = '/profit-loss'

ns = Namespace('Profit-Loss')


@ns.route('/market')
@respond_with_code
class MarketDataResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
       dict(
           market=wa_fields.String(validate=lambda x: x.isupper(), required=True),
           margin=wa_fields.Bool(default=False, missing=False)
       )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        db.session.close()
        market, margin = kwargs['market'], kwargs['margin']
        if margin:
            if market not in MarginAccountNameCache.list_online_markets().values():
                raise InvalidArgument
        p = MarketProfitLossAnalyzer(user_id, market, margin)
        return p.analyze()


@ns.route('/deal')
@respond_with_code
class DealDataResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(validate=lambda x: x.isupper(), required=True),
            start_time=TimestampField(required=True),
            end_time=TimestampField(required=True),
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        db.session.close()
        market = kwargs['market']
        start, end = int(kwargs['start_time'].timestamp()), int(kwargs['end_time'].timestamp())
        p = UserDealAnalyzer(user_id, market, start, end)
        return p.analyze()


@ns.route('/account')
@respond_with_code
class AccountProfitLossResource(Resource):


    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            account_type=EnumField(AccountBalanceType, enum_by_value=False),
            start_time=TimestampField(required=True, to_date=True),
            end_time=TimestampField(required=True, to_date=True),
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise ConfirmationRequired
        today_date = datetime.datetime.utcnow().date()
        start_date = kwargs["start_time"]
        end_date = kwargs["end_time"]
        account_type = TOTAL_ACCOUNT if not kwargs.get("account_type") else kwargs["account_type"]
        account_type_str = account_type.name if isinstance(account_type, AccountBalanceType) \
            else account_type
        if account_type_str == AccountBalanceType.PLEDGE.name:
            raise InvalidArgument
        if start_date > end_date:
            raise InvalidArgument
        if end_date > today_date:
            end_date = today_date
        if end_date == today_date:
            include_realtime_data = True
        else:
            include_realtime_data = False
        if include_realtime_data:
            if not check_today_data_ready():
                end_date = today_date - datetime.timedelta(days=1)
                include_realtime_data = False
                if start_date > end_date:
                    raise DataNotReady
        platform = get_request_platform()
        # TODO: app支持质押理财后，改为按版本判断
        if platform.is_web():
            snapshot_func = get_snapshot_data_new
        else:
            snapshot_func = get_snapshot_data
        snap_result = format_profit_snapshot_data(
            start_date,
            snapshot_func(user_id, start_date, end_date, account_type))
        if include_realtime_data:
            realtime_data = {
                "balance_usd": snap_result[-1]["balance_usd"] if len(snap_result) > 0 else Decimal(),
                "profit_usd": Decimal(),
                "profit_rate": Decimal(),
                "total_profit_usd": snap_result[-1]["total_profit_usd"] if len(snap_result) > 0
                else Decimal(),
                "report_date": today()
            }
            cache_data = get_account_pl_cache_data(user_id, account_type_str)
            realtime_data.update(cache_data)
            if "profit_usd" in cache_data:
                realtime_data["total_profit_usd"] += Decimal(cache_data["profit_usd"])
            snap_result.append(realtime_data)
        return snap_result


@ns.route('/account/realtime')
@respond_with_code
class AccountRealtimeProfitLossResource(Resource):

    @classmethod
    @cached(5*60)
    def get_user_realtime_usd(cls, user_id: int, account_type_str: str):
        return RealtimeAccountProfitLossProcessor.get_real_time_account_type_usd(
            user_id,
            account_type_str)

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            account_type=EnumField(AccountBalanceType, enum_by_value=False),
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise InvalidArgument
        account_type = TOTAL_ACCOUNT if not kwargs.get("account_type") else kwargs["account_type"]
        account_type_str = account_type.name if isinstance(account_type, AccountBalanceType) \
            else account_type
        if account_type_str == AccountBalanceType.PLEDGE.name:
            raise InvalidArgument
        
        has_balance_history = bool(ServerClient().get_user_balance_history(user_id, page=1, limit=1))

        if not check_today_data_ready():
            raise DataNotReady(
                data=dict(
                    balance_usd=cls.get_user_realtime_usd(user_id, account_type_str),
                    has_balance_history=has_balance_history
                )
            )
        db.session.close()
        max_wait = 2
        t0 = current_timestamp()
        cache_data = get_account_pl_cache_data(user_id, account_type_str)
        while not {"profit_rate", "profit_usd"} & set(cache_data):
            t1 = current_timestamp()
            if t1 - t0 >= max_wait:
                break
            time.sleep(0.2)
            cache_data = UserAccountPLDataCache(user_id, account_type_str).hgetall()
        if not cache_data:
            raise DataNotReady(
                data=dict(
                    balance_usd=cls.get_user_realtime_usd(user_id, account_type_str),
                    has_balance_history=has_balance_history
                )
            )

        realtime_data = {
            "balance_usd": Decimal(),
            # "profit_usd": Decimal(),
            # "profit_rate": Decimal(),
        }
        # noinspection PyTypeChecker
        realtime_data.update(cache_data)
        realtime_data['has_balance_history'] = has_balance_history
        return realtime_data


@ns.route('/account/summary')
@respond_with_code
class AccountSummaryResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            start_time=TimestampField(required=True, to_date=True),
            end_time=TimestampField(to_date=True, required=True)
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise ConfirmationRequired
        today_date = datetime.datetime.utcnow().date()
        end_date = kwargs["end_time"]
        start_date = kwargs["start_time"]
        if start_date > end_date:
            raise InvalidArgument
        if end_date > today_date:
            end_date = today_date
        if end_date == today_date:
            include_realtime_data = True
        else:
            include_realtime_data = False
        if not check_today_data_ready():
            end_date = today_date - datetime.timedelta(days=1)
            include_realtime_data = False
            if end_date < start_date:
                raise DataNotReady

        show_account_types = [i for i in AccountBalanceType if i not in (AccountBalanceType.PLEDGE,
                                                                         AccountBalanceType.STAKING)]
        snap_result = {
            v.name: Decimal()
            for v in show_account_types
        }
        snap_result.update(get_snapshot_sum_profit_data(user_id, start_date, end_date))

        if (staking_type:= AccountBalanceType.STAKING.name) in snap_result:
            snap_result[AccountBalanceType.INVESTMENT.name] += snap_result[staking_type]
            del snap_result[staking_type]
        if include_realtime_data:
            for account_type in show_account_types:
                cache_data = get_account_pl_cache_data(user_id, account_type.name)
                profit_usd = cache_data.get("profit_usd")
                snap_result[account_type.name] += Decimal(profit_usd) if profit_usd else Decimal()
        return snap_result


@ns.route('/account/asset/percent')
@respond_with_code
class AccountAssetPercentResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            account_type=EnumField(AccountBalanceType, enum_by_value=False),
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise InvalidArgument
        account_type = TOTAL_ACCOUNT if not kwargs.get("account_type") else kwargs["account_type"]
        if account_type == AccountBalanceType.PLEDGE:
            raise InvalidArgument

        return RealtimeAccountProfitLossProcessor.get_user_asset_percent_data(user_id, account_type)


@ns.route('/account/deal')
@respond_with_code
class AccountDealValueResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            account_type=EnumField(AccountBalanceType, enum_by_value=False),
            start_time=TimestampField(required=True, to_date=True),
            end_time=TimestampField(required=True, to_date=True),
        )
    )
    def get(cls, **kwargs):
        """
        盈亏分析-交易额曲线
        """
        start_date = kwargs["start_time"]
        end_date = kwargs["end_time"]
        account_type = kwargs.get("account_type")

        if account_type and account_type not in (
            AccountBalanceType.SPOT,
            AccountBalanceType.MARGIN,
            AccountBalanceType.PERPETUAL,
        ):
            raise InvalidArgument
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise ConfirmationRequired

        result = get_deal_snapshot_data(user_id, start_date, end_date, account_type)
        format_result = format_deal_snapshot_data(start_date, end_date, result)
        
        today_ = today()
        if end_date == today_:
            include_realtime_data = True
        else:
            include_realtime_data = False
        if not check_today_data_ready():
            end_date = today_ - datetime.timedelta(days=1)
            include_realtime_data = False
            if end_date < start_date:
                raise DataNotReady
        
        if include_realtime_data:
            if account_type:
                account_type_str = account_type.name
            else:
                account_type_str = TOTAL_ACCOUNT
            cache_data = get_account_deal_value_cache_data(user_id, account_type_str)
            if cache_data:
                cache_data["report_date"] = today_
                format_result.append(cache_data)
        return dict(
            data=format_result,
        )