# -*- coding: utf-8 -*-

import decimal
import json
from decimal import Decimal
from enum import Enum

from flask import g
from flask_babel import gettext
from sqlalchemy import func
from webargs import fields

from app.common import PrecisionEnum
from app.models import db
from app.models.wallet import AbnormalDepositApplication
from app.assets import get_asset_chain_config
from app.api.common import (
    Namespace,
    Resource,
    require_login,
    respond_with_code,
    lock_request,
)
from app.api.common.fields import (
    LimitField,
    PageField,
    PositiveDecimalField,
)
from app.business import WalletClient
from app.business.abnormal_deposit import AbnormalDepositBusiness
from app.exceptions import InvalidArgument, AssetNotFound, InvalidAssetCode, WithdrawalsSuspended
from app.utils import now, amount_to_str, quantize_amount


ns = Namespace("Abnormal-Deposit-Application")
url_prefix = "/abnormal-deposit"


@ns.route("/application/config")
@respond_with_code
class AbnormalDepositApplicationConfigResource(Resource):
    @classmethod
    @ns.use_kwargs(dict())
    def get(cls):
        """ 异常充值申请-配置信息 """
        # 已废弃
        return {
            "fee_asset": AbnormalDepositApplication.SERVICE_FEE_ASSET,
            "fee_amount": amount_to_str(AbnormalDepositApplication.SERVICE_FEE_AMOUNT, 8),
        }


@ns.route("/application")
@respond_with_code
class AbnormalDepositApplicationResource(Resource):
    class ErrorMsg:
        NOT_COINEX_ADDRESS = gettext("你填写的地址不是你的CoinEx地址，请确认是否输入正确。")
        INFO_MISMATCHED = gettext("你填写的交易ID（TxID）与填写充值接收地址不一致，请确认是否输入正确。")
        TX_NOT_FOUND = gettext("无法在区块上找到该笔充值记录，请检查交易ID（TxID）或公链类型后重新提交。")
        PROCESSING_APPLICATION = gettext("该笔交易已经申请过，可在下方 申请记录 中查看，请勿重复申请。")
        TX_REPEAT = gettext("该笔交易已经申请过，请勿重复申请")
        #
        CREDITED = gettext("无需找回，可在充值记录中查看该笔充值。")
        NOT_YOUR_DEPOSIT = gettext("该笔充值不是你本人的充值。")
        DEPOSITS_SUSPENDED = gettext("该币种/公链暂停充值，请恢复充值后再提交。")
        TX_IMMATURE = gettext("该笔充值在区块链上还未转账成功，请耐心等待。")
        SENDER_BLACKLISTED = gettext("该笔充值无法入账，请联系客服。")
        PLEASE_CONTACT_CS = gettext("该笔充值无法入账，请联系客服。")

    class Status(Enum):
        CREDITED = "已到账"
        NOT_CREDITABLE = "无法到账"
        PLEASE_WAIT = "请等待"
        PROOF_REQUIRED = "需提供相关证明"
        PLEASE_CONTACT_CS = "请联系客服"
        ASSESSMENT_PENDING = "待评估"

    class Reason(Enum):
        ALREADY_CREDITED = "已到账"
        RECOVERED = "漏扫并找回"
        PENDING = "待入账"

        NOT_DEPOSIT = "非充值"
        NOT_YOUR_DEPOSIT = "非用户充值"
        DEPOSITS_SUSPENDED = "充值暂停"
        TX_IMMATURE = "交易未成熟"
        SENDER_BLACKLISTED = "发送方在黑名单中"
        AMOUNT_TOO_SMALL = "数额太小"
        WRONG_MEMO = "Memo 错误"
        RECIPIENT_IS_HOT_WALLET = "充值到热钱包"
        WRONG_CHAIN_OF_ADDRESS = "充值到其他链的地址"
        DEPOSIT_EXPIRED = "该笔充值已超期"

        NOT_LISTED = "未上架"
        UNSUPPORTED = "未支持"
        UNIDENTIFIED = "未知"

    # 不需要找回的 reason: 返回的msg
    NOT_NEED_RECOVERY_REASON_TO_ERROR_MSG = {
        Reason.ALREADY_CREDITED.name: ErrorMsg.CREDITED,
        Reason.RECOVERED.name: ErrorMsg.CREDITED,
        Reason.PENDING.name: ErrorMsg.CREDITED,
        Reason.AMOUNT_TOO_SMALL.name: ErrorMsg.CREDITED,
        Reason.NOT_DEPOSIT.name: ErrorMsg.NOT_YOUR_DEPOSIT,
        Reason.NOT_YOUR_DEPOSIT.name: ErrorMsg.NOT_YOUR_DEPOSIT,
        Reason.DEPOSITS_SUSPENDED.name: ErrorMsg.DEPOSITS_SUSPENDED,
        Reason.TX_IMMATURE.name: ErrorMsg.TX_IMMATURE,
        Reason.SENDER_BLACKLISTED.name: ErrorMsg.SENDER_BLACKLISTED,
    }

    PASSED_STATUES = [
        Status.PROOF_REQUIRED.name,
        Status.ASSESSMENT_PENDING.name,
    ]

    @classmethod
    def process_validation_result(cls, validation_result) -> AbnormalDepositApplication.Type:
        if validation_result and validation_result.get("not_implemented"):
            if not validation_result.get("is_address_ours"):
                raise InvalidArgument(message=cls.ErrorMsg.NOT_COINEX_ADDRESS)
            return AbnormalDepositApplication.Type.UNKNOWN

        if not validation_result or not validation_result.get("successful"):
            raise InvalidArgument(message=cls.ErrorMsg.TX_NOT_FOUND)

        reason = validation_result["reason"]
        status = validation_result["status"]
        type_ = AbnormalDepositApplication.Type.UNKNOWN
        if reason in cls.NOT_NEED_RECOVERY_REASON_TO_ERROR_MSG:
            raise InvalidArgument(message=cls.NOT_NEED_RECOVERY_REASON_TO_ERROR_MSG[reason])
        if reason == cls.Reason.DEPOSIT_EXPIRED.name:
            raise InvalidArgument(message=cls.ErrorMsg.PLEASE_CONTACT_CS)
        elif status not in cls.PASSED_STATUES:
            raise InvalidArgument(message=cls.ErrorMsg.PLEASE_CONTACT_CS)
        elif reason == cls.Reason.WRONG_MEMO.name:
            type_ = AbnormalDepositApplication.Type.WRONG_MEMO
        elif reason == cls.Reason.RECIPIENT_IS_HOT_WALLET.name:
            type_ = AbnormalDepositApplication.Type.RECIPIENT_IS_HOT_WALLET
        elif reason == cls.Reason.WRONG_CHAIN_OF_ADDRESS.name:
            type_ = AbnormalDepositApplication.Type.WRONG_CHAIN_OF_ADDRESS
        elif reason in [
            cls.Reason.UNSUPPORTED.name,
            cls.Reason.UNIDENTIFIED.name,
            cls.Reason.NOT_LISTED.name,
        ]:
            type_ = AbnormalDepositApplication.Type.UNKNOWN
        return type_

    @classmethod
    def get_remark_by_result(cls, validation_result) -> str:
        if validation_result and validation_result.get("not_implemented"):
            return "未校验"
        return ""

    @classmethod
    def require_tx_id_not_duplicate(cls, user_id: int, tx_id: str):
        # 只判断tx_id是否重复
        exist_row: AbnormalDepositApplication = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.tx_id == tx_id,
            AbnormalDepositApplication.status.not_in(
                [
                    AbnormalDepositApplication.Status.CANCELLED,
                    AbnormalDepositApplication.Status.REJECTED,
                ]
            ),
        ).first()
        if exist_row:
            # 除了已取消和审核失败，不允许重复提交
            if exist_row.user_id == user_id:
                raise InvalidArgument(message=cls.ErrorMsg.PROCESSING_APPLICATION)
            raise InvalidArgument(message=cls.ErrorMsg.TX_REPEAT)

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True),
            chain=fields.String(required=True),
            address=fields.String(required=True),
            amount=PositiveDecimalField(
                required=True,
                places=PrecisionEnum.COIN_PLACES,
                rounding=decimal.ROUND_DOWN,
            ),
            tx_id=fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 异常充值申请-提交 """
        tx_id = kwargs["tx_id"].strip()
        for c in [" ", "\n"]:
            # 有些链的tx_id有特殊的字符，不好用re统一判断，比如HBAR
            if c in tx_id:
                raise InvalidArgument(message=cls.ErrorMsg.TX_NOT_FOUND)
        user_id = g.user.id

        asset = kwargs["asset"].strip()
        chain = kwargs["chain"].strip()
        address = kwargs["address"]
        address = address.strip()
        amount = kwargs["amount"]

        try:
            response = WalletClient().validate_and_analysis_deposit_recovery(
                user_id=user_id,
                asset=asset,
                chain=chain,
                address=address,
                memo="",
                amount=amount,
                tx_id=tx_id,
            )
        except Exception as _e:
            if isinstance(_e, InvalidArgument):
                raise InvalidArgument(message=cls.ErrorMsg.INFO_MISMATCHED)
            raise
        resp_asset = response.get("asset") or ""  # 链上的币种, 未上架时wallet返回None
        resp_chain = response.get("chain") or ""

        real_asset = resp_asset or asset  # 非空时 用钱包返回的; 为空时 用用户的
        real_chain = resp_chain or chain
        try:
            ac_conf = get_asset_chain_config(real_asset, real_chain)
            if ac_conf.is_visible and not ac_conf.deposits_all_enabled:
                raise InvalidArgument(message=cls.ErrorMsg.DEPOSITS_SUSPENDED)
        except (AssetNotFound, InvalidAssetCode):
            # 可能是充错链、允许特殊字符
            pass

        remark = cls.get_remark_by_result(response)
        type_ = cls.process_validation_result(response)
        resp_amount = response.get("amount") or Decimal()
        resp_tx_id = response.get("tx_id")
        resp_address = response.get("address") or address

        cls.require_tx_id_not_duplicate(user_id, tx_id)
        # recheck wallet response.tx_id
        if resp_tx_id and resp_tx_id != tx_id:
            cls.require_tx_id_not_duplicate(user_id, resp_tx_id)

        asset_rate = AbnormalDepositBusiness.get_asset_rate(real_asset)
        total_usd = quantize_amount(asset_rate * resp_amount, 8)
        if asset_rate and type_ in AbnormalDepositApplication.NEW_NEED_SERVICE_FEE_TYPES:
            expect_fee_asset = real_asset
            expect_fee_amount = AbnormalDepositBusiness.calc_expect_fee_amount(asset_rate, resp_amount)
        else:
            expect_fee_asset = expect_fee_amount = None

        application = AbnormalDepositApplication(
            user_id=user_id,
            asset=real_asset,
            chain=real_chain,
            address=resp_address,
            amount=amount,  # 用户填的数目
            tx_amount=amount_to_str(resp_amount, 8),  # 链上的数量
            tx_id=resp_tx_id or tx_id,
            type=type_,
            remark=remark,
            total_usd=total_usd,
            asset_rate=asset_rate,
            expect_fee_asset=expect_fee_asset,
            expect_fee_amount=expect_fee_amount,
        )
        #
        if type_ == AbnormalDepositApplication.Type.UNKNOWN:
            additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.UNKNOWN
        elif type_ in AbnormalDepositApplication.NEED_ADDITIONAL_INFO_TYPES:
            additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.REQUIRED
        else:
            additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.NOT_REQUIRED
            application.status = AbnormalDepositApplication.Status.AUDIT_REQUIRED  # 不需要补充资料，待初审
        #
        if type_ in AbnormalDepositApplication.WALLET_ABNORMAL_DEPOSIT_TYPES:
            wallet_type = AbnormalDepositApplication.WalletType.ABNORMAL_DEPOSIT
        elif type_ in AbnormalDepositApplication.WALLET_DEPOSIT_RECOVERY_TYPES:
            wallet_type = AbnormalDepositApplication.WalletType.DEPOSIT_RECOVERY
        else:
            wallet_type = AbnormalDepositApplication.WalletType.UNKNOWN
        application.additional_info_status = additional_info_status
        application.wallet_type = wallet_type
        db.session_add_and_commit(application)
        return {
            "id": application.id,
            "type": type_.name,
        }

    @classmethod
    def get_frontend_status(cls, row: AbnormalDepositApplication) -> str:
        status_class = AbnormalDepositApplication.Status
        if row.status in [status_class.REJECTED, status_class.CANCELLED, status_class.FINISHED]:
            return row.status.name
        elif row.status in [
            status_class.CHECKED,
            status_class.PROCESSING,
            status_class.ASSET_CHANGE_PROCESSING,
            status_class.ASSET_CHANGE_DEDUCTED,
            status_class.FAILED,
        ]:
            return "PROCESSING"
        elif row.status == status_class.AUDIT_REQUIRED:
            if row.additional_info_status == AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED:
                # 补充资料审核中
                return "ADDITIONAL_INFO_AUDIT_REQUIRED"
        elif row.status == status_class.CREATED:
            if row.additional_info_status == AbnormalDepositApplication.AdditionalInfoStatus.REQUIRED:
                # 待补充资料
                return "ADDITIONAL_INFO_REQUIRED"
            elif row.additional_info_status in [
                AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE,
                AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE_CR,
            ]:
                # 待额外补充资料
                return "ADDITIONAL_INFO_REQUIRE_MORE"

        # 其他情况都当成 审核中
        return "AUDIT_REQUIRED"

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(page=PageField, limit=LimitField))
    def get(cls, **kwargs):
        """ 异常充值申请-列表 """
        query = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.user_id == g.user.id,
        ).order_by(AbnormalDepositApplication.id.desc())
        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows = pagination.items

        items = [
            dict(
                id=i.id,
                time=i.created_at,
                asset=i.asset,
                amount=i.amount,
                status=cls.get_frontend_status(i),
            )
            for i in rows
        ]

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/application/processing/total")
@respond_with_code
class AbnormalDepositApplicationProcTotalResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 异常充值申请-处理中的数目 """
        status_class = AbnormalDepositApplication.Status
        processing_count = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.user_id == g.user.id,
            AbnormalDepositApplication.status.not_in(
                [status_class.REJECTED, status_class.CANCELLED, status_class.FINISHED]
            ),
        ).with_entities(
            func.count(1)
        ).scalar() or 0
        return {
            "total": processing_count,
        }


@ns.route("/application/<int:application_id>")
@respond_with_code
class AbnormalDepositApplicationDetailResource(Resource):

    class ErrorMsg:
        INCOMPLETE_INFO = gettext("补充资料不全")

    CANCELLABLE_STATUS = [
        AbnormalDepositApplication.Status.CREATED,
        AbnormalDepositApplication.Status.AUDIT_REQUIRED,
        AbnormalDepositApplication.Status.CHECK_REQUIRED,
        AbnormalDepositApplication.Status.DOUBLE_CHECK_REQUIRED,
    ]

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, application_id):
        """ 异常充值申请-详情 """
        user_id = g.user.id
        row: AbnormalDepositApplication = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.id == application_id,
            AbnormalDepositApplication.user_id == user_id,
        ).first()
        if not row:
            raise InvalidArgument

        if row.status != AbnormalDepositApplication.Status.REJECTED:
            rejection_reason_str = ""
        else:
            rejection_reason_str = row.rejection_reason_str
        if row.is_need_fee and row.status not in [
            AbnormalDepositApplication.Status.CREATED,
            AbnormalDepositApplication.Status.AUDIT_REQUIRED,
            AbnormalDepositApplication.Status.CHECK_REQUIRED,
            AbnormalDepositApplication.Status.DOUBLE_CHECK_REQUIRED,
            AbnormalDepositApplication.Status.CANCELLED,
            AbnormalDepositApplication.Status.REJECTED,
        ]:
            fee_asset = row.fee_asset or row.expect_fee_asset
            fee_amount = row.fee_amount or row.expect_fee_amount
            if not fee_asset:
                fee_amount = None
        else:
            fee_asset = ""
            fee_amount = None
        if row.status != AbnormalDepositApplication.Status.CREATED:
            append_additional_info_reason_str = ""
        else:
            append_additional_info_reason_str = row.rejection_reason_str
        cancellable = row.status in cls.CANCELLABLE_STATUS
        detail_info = dict(
            id=row.id,
            time=row.created_at,
            asset=row.asset,
            chain=row.chain,
            address=row.address,
            amount=row.amount,
            tx_id=row.tx_id,
            status=AbnormalDepositApplicationResource.get_frontend_status(row),
            fee_asset=fee_asset,
            fee_amount=fee_amount,
            cancellable=cancellable,
            processed_at=row.processed_at,
            type=row.type.name,
            rejection_reason=rejection_reason_str,
            refund_tx_id=row.refund_tx_id,
            refund_tx_url=WalletClient().get_explorer_tx_url(row.chain, row.refund_tx_id) if row.refund_tx_id else "",
            append_additional_info_reason=append_additional_info_reason_str,
        )
        return detail_info

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            transfer_out_screenshots=fields.List(fields.String, required=False),  # 转账凭证图片-数组
            send_address_screenshots=fields.List(fields.String, required=False),  # 转出地址凭证图片-数组
            merge_screenshots=fields.List(fields.String, required=False),   # 两笔交易在同一页面的转账凭证
            refund_address_screenshots=fields.List(fields.String, required=False),  # 退款地址凭证图片-数组
            refund_address=fields.String(required=False),
            refund_memo=fields.String(required=False),
        )
    )
    def put(cls, application_id, **kwargs):
        """ 异常充值申请-提交补充资料 """
        user_id = g.user.id
        row = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.id == application_id,
            AbnormalDepositApplication.user_id == user_id,
        ).first()
        if not row:
            raise InvalidArgument

        if row.type == AbnormalDepositApplication.Type.PENDING_DEPOSIT:
            raise InvalidArgument
        # 待提交资料：status=CREATED，additional_info_status=REQUIRED
        if row.status != AbnormalDepositApplication.Status.CREATED:
            raise InvalidArgument
        if row.additional_info_status != AbnormalDepositApplication.AdditionalInfoStatus.REQUIRED:
            raise InvalidArgument

        # check params
        max_img_num = 2
        transfer_out_screenshots = kwargs.get("transfer_out_screenshots") or []
        if len(transfer_out_screenshots) > max_img_num:
            raise InvalidArgument
        send_address_screenshots = kwargs.get("send_address_screenshots") or []
        if len(send_address_screenshots) > max_img_num:
            raise InvalidArgument
        merge_screenshots = kwargs.get("merge_screenshots") or []
        if len(merge_screenshots) > max_img_num:
            raise InvalidArgument
        refund_address_screenshots = kwargs.get("refund_address_screenshots") or []
        if len(refund_address_screenshots) > max_img_num:
            raise InvalidArgument
        refund_address = kwargs.get("refund_address")
        if refund_address:
            refund_address = refund_address.strip()
        refund_memo = kwargs.get("refund_memo")

        if row.type == AbnormalDepositApplication.Type.WRONG_MEMO:
            if not transfer_out_screenshots:
                raise InvalidArgument(message=cls.ErrorMsg.INCOMPLETE_INFO)
            if not send_address_screenshots:
                raise InvalidArgument(message=cls.ErrorMsg.INCOMPLETE_INFO)
            if not merge_screenshots:
                raise InvalidArgument(message=cls.ErrorMsg.INCOMPLETE_INFO)
            additional_info = {
                "transfer_out_screenshots": transfer_out_screenshots,
                "send_address_screenshots": send_address_screenshots,
                "merge_screenshots": merge_screenshots,
            }

        elif row.type in [
            AbnormalDepositApplication.Type.RECIPIENT_IS_HOT_WALLET,
            AbnormalDepositApplication.Type.WRONG_CHAIN_OF_ADDRESS,
            AbnormalDepositApplication.Type.WRONG_CHAIN_WITH_RECORDABLE,
        ]:
            if not transfer_out_screenshots:
                raise InvalidArgument(message=cls.ErrorMsg.INCOMPLETE_INFO)
            additional_info = {
                "transfer_out_screenshots": transfer_out_screenshots,
            }

        elif row.type in [
            AbnormalDepositApplication.Type.WRONG_ASSET,
            AbnormalDepositApplication.Type.WRONG_CHAIN_WITHOUT_RECORDABLE,
        ]:
            if not transfer_out_screenshots:
                raise InvalidArgument(message=cls.ErrorMsg.INCOMPLETE_INFO)
            if not refund_address_screenshots:
                raise InvalidArgument(message=cls.ErrorMsg.INCOMPLETE_INFO)
            if not refund_address:
                raise InvalidArgument(message=cls.ErrorMsg.INCOMPLETE_INFO)
            row.refund_address = refund_address
            if refund_memo:
                row.refund_memo = refund_memo
            # 退回地址不能是CoinEx地址
            try:
                is_valid, is_deposit_address = WalletClient().is_coinex_address(
                    chain=row.chain, address=refund_address, memo=refund_memo or ""
                )
            except WithdrawalsSuspended:
                raise InvalidArgument(message=gettext("该币种/公链暂停提现，请恢复后再提交资料"))
            if not is_valid:
                raise InvalidArgument(message="address is invalid")
            if is_deposit_address:
                raise InvalidArgument(message=gettext("不能提交CoinEx地址"))

            additional_info = {
                "transfer_out_screenshots": transfer_out_screenshots,
                "refund_address_screenshots": refund_address_screenshots,
            }
        else:
            raise InvalidArgument

        row.status = AbnormalDepositApplication.Status.AUDIT_REQUIRED
        row.additional_info = json.dumps(additional_info)
        row.additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED
        row.additional_info_at = now()
        additional_info_at_info = row.get_additional_info_at_info()
        additional_info_at_info.append(int(row.additional_info_at.timestamp()))
        row.additional_info_at_info = json.dumps(additional_info_at_info)
        db.session.commit()
        return {}

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            append_screenshots=fields.List(fields.String, required=False),
        )
    )
    def patch(cls, application_id, **kwargs):
        """ 异常充值申请-提交额外补充资料 """
        user_id = g.user.id
        row = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.id == application_id,
            AbnormalDepositApplication.user_id == user_id,
        ).first()
        if not row:
            raise InvalidArgument

        if row.type == AbnormalDepositApplication.Type.PENDING_DEPOSIT:
            raise InvalidArgument
        # 待补充提交资料：status=CREATED，additional_info_status=REQUIRE_MORE
        # or
        # 待补充提交资料：status=CREATED，additional_info_status=REQUIRE_MORE_CR
        if row.status != AbnormalDepositApplication.Status.CREATED:
            raise InvalidArgument
        if row.additional_info_status not in [
            AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE,
            AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE_CR,
        ]:
            raise InvalidArgument

        # check params
        max_img_num = 2
        append_screenshots = kwargs.get("append_screenshots") or []
        if len(append_screenshots) > max_img_num:
            raise InvalidArgument
        if not append_screenshots:
            raise InvalidArgument(message=cls.ErrorMsg.INCOMPLETE_INFO)

        additional_info = json.loads(row.additional_info) if row.additional_info else {}
        info_append_screenshots = additional_info.get("append_screenshots", [])
        info_append_screenshots.append(append_screenshots)
        additional_info["append_screenshots"] = info_append_screenshots

        status = AbnormalDepositApplication.Status.AUDIT_REQUIRED
        additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED
        if row.additional_info_status == AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE_CR:
            status = AbnormalDepositApplication.Status.CHECK_REQUIRED
            additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED_CR
        row.status = status
        row.additional_info = json.dumps(additional_info)
        row.additional_info_status = additional_info_status
        row.additional_info_at = now()
        additional_info_at_info = row.get_additional_info_at_info()
        additional_info_at_info.append(int(row.additional_info_at.timestamp()))
        row.additional_info_at_info = json.dumps(additional_info_at_info)
        row.rejection_reason = ''
        row.is_custom_reason = False
        db.session.commit()
        return {}

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict())
    def delete(cls, application_id):
        """ 异常充值申请-取消申请 """
        user_id = g.user.id
        row = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.id == application_id,
            AbnormalDepositApplication.user_id == user_id,
        ).first()
        if not row:
            raise InvalidArgument

        if row.status not in cls.CANCELLABLE_STATUS:
            raise InvalidArgument

        row.status = AbnormalDepositApplication.Status.CANCELLED
        row.processed_at = now()
        db.session.commit()
        return {}
