# -*- coding: utf-8 -*-
import json
import os
from collections import defaultdict
from decimal import Decimal
from functools import partial
from flask_restx import fields as fx_fields
from webargs import fields as wa_fields

from flask import g, send_file, request
from flask_babel import force_locale
from flask_babel import gettext as _


from app import config
from app.business import UserPreferences, timedelta
from app.caches.broker import BrokerReportExportLimitCache, BrokerReportLastUpdateTimeCache
from app.common import ReportType, PrecisionEnum
from app.models import db, MaskUser, User
from app.models.broker import BrokerType, BrokerApplication, Broker, DailyBrokerAssetReport, \
    MonthlyBrokerAssetReport, DailyBrokerUserAssetReport, MonthlyBrokerUserAssetReport
from app.api.common import Namespace, Resource, require_login, respond_with_code, lock_request, limit_user_frequency
from app.api.common.fields import EnumField, PageField, LimitField, TimestampMarshalField, AmountField, TimestampField
from app.exceptions import (
    InvalidArgument, FileTooBig, ServiceUnavailable,
)
from app.utils import max_length_validator, query_to_page, now, datetime_to_str, amount_to_str, ExcelExporter, \
    batch_iter, AWSBucketPrivate
from app.utils.date_ import date_to_datetime, yesterday, today, next_month

ns = Namespace("Broker")


@ns.route("/application")
@respond_with_code
class BrokerApplyResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @limit_user_frequency(count=10, interval=86400)
    @ns.use_kwargs(
        dict(
            name=wa_fields.String(required=True, validate=max_length_validator(64)),
            contact_name=wa_fields.String(required=True, validate=max_length_validator(64)),
            contact_info=wa_fields.String(required=True, validate=max_length_validator(128)),
            city=wa_fields.String(required=True, validate=max_length_validator(128)),
            type=EnumField(BrokerType, required=True),
            contact_type=EnumField(BrokerApplication.ContactType, required=True),
            introduction=wa_fields.String(required=True),
            official_website=wa_fields.String(missing="", validate=max_length_validator(256)),
            user_scale=EnumField(BrokerApplication.UserScale, required=True),
            trade_scale=EnumField(BrokerApplication.TradeScale, required=True),
            extra_info=wa_fields.String(missing="", validate=max_length_validator(256)),
            extra_data=wa_fields.List(wa_fields.String, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 提交经纪商申请 """
        user = g.user
        broker: Broker = Broker.query.filter(
            Broker.user_id == user.id,
            Broker.status == Broker.Status.VALID,
        ).first()
        if broker:
            raise InvalidArgument(message=_("您已经是经纪商"))

        extra_images = list(set(kwargs["extra_data"]))
        if len(extra_images) > 10:
            raise InvalidArgument
        extra_data = {"extra_images": extra_images}

        row = BrokerApplication.get_or_create(user_id=user.id)
        row.type = BrokerType.API
        row.applied_at = now()
        row.name = kwargs["name"]
        row.city = kwargs.get("city") or ""
        row.contact_name = kwargs["contact_name"]
        row.contact_info = kwargs["contact_info"]
        row.official_website = kwargs.get("official_website") or ""
        row.introduction = kwargs["introduction"]
        row.user_scale = kwargs["user_scale"]
        row.contact_type = kwargs["contact_type"]
        row.trade_scale = kwargs["trade_scale"]
        row.extra_info = kwargs["extra_info"]
        row.extra_data = json.dumps(extra_data),
        db.session_add_and_commit(row)


@ns.route("/info")
@respond_with_code
class BrokerInfoResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """经纪商信息"""
        user = g.user
        broker: Broker = Broker.query.filter(
            Broker.user_id == user.id,
            Broker.status == Broker.Status.VALID,
        ).first()
        if not broker:
            return dict(
                is_exists=False,
                broker_id="",
                rate=0,
                deal_user_count=0,
                total_trade_amount=0,
                total_referral_amount=0,
            )

        report_list = DailyBrokerAssetReport.query.filter(
            DailyBrokerAssetReport.user_id == user.id,
        ).all()

        deal_user_set = set()
        total_trade_amount = Decimal()
        total_referral_amount = Decimal()

        for row in report_list:
            total_trade_amount += row.spot_trade_usd
            total_trade_amount += row.perpetual_trade_usd
            total_referral_amount += row.amount
            deal_user_set.update(row.get_deal_user_ids())

        return dict(
            is_exists=True,
            broker_id=broker.broker_id,
            name=broker.name,
            rate=broker.rate,
            deal_user_count=len(deal_user_set),
            total_trade_amount=total_trade_amount,
            total_referral_amount=total_referral_amount,
        )


@ns.route("/series")
@respond_with_code
class BrokerSeriesResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            points=wa_fields.Integer(required=True, validate=lambda x: 0 < x <= 365),  # 点数｜最近n天数据
        )
    )
    @require_login(allow_sub_account=False)
    def get(cls, **kwargs):
        """ 推广数据分析-经纪商返佣曲线(用户每日返佣数据表格) """
        user = g.user
        broker: Broker = Broker.query.filter(
            Broker.user_id == user.id,
            Broker.status == Broker.Status.VALID,
        ).first()
        if not broker:
            return

        referral_history = DailyBrokerAssetReport.query.filter(
            DailyBrokerAssetReport.user_id == user.id,
            DailyBrokerAssetReport.date >= today() - timedelta(days=kwargs['points'])
        ).order_by(DailyBrokerAssetReport.id.desc()).all()

        series = [[i.date, i.deal_user_count,
                   i.spot_trade_usd+i.perpetual_trade_usd,
                   i.spot_fee_usd+i.perpetual_fee_usd,
                   i.amount] for i in referral_history]
        summary = defaultdict(Decimal)
        user_list = set()
        for i in referral_history:
            summary['total_trade_amount'] += i.spot_trade_usd
            summary['total_trade_amount'] += i.perpetual_trade_usd
            summary['total_fee_amount'] += i.spot_fee_usd
            summary['total_fee_amount'] += i.perpetual_fee_usd
            summary['total_referral_amount'] += i.amount
            user_list.update(i.get_deal_user_ids())
        summary['total_user_count'] = len(user_list)

        last_update_time = yesterday()
        if last_update_time_cache := BrokerReportLastUpdateTimeCache().get():
            last_update_time = int(last_update_time_cache)

        return dict(
            series=series,
            summary=summary,
            last_update_time=last_update_time,
        )


@ns.route("/reports")
@respond_with_code
class BrokerReportResource(Resource):
    
    @classmethod
    def limit_export_notice(cls, user_id):
        notice_cache = BrokerReportExportLimitCache(user_id)
        if notice_cache.count() < notice_cache.count_limit:
            notice_cache.add_value()
            return False
        return True

    marshal_fields = {
        "time": TimestampMarshalField(attribute="date"),
        "asset": fx_fields.String,
        "amount": AmountField,
        "spot_fee_usd": AmountField,
        "perpetual_fee_usd": AmountField,
        "total_fee_usd": AmountField(attribute=lambda x: x.spot_fee_usd+x.perpetual_fee_usd),
        "spot_trade_usd": AmountField,
        "perpetual_trade_usd": AmountField,
        "total_trade_usd": AmountField(attribute=lambda x: x.spot_trade_usd+x.perpetual_trade_usd),
        "deal_user_count": fx_fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
            code=wa_fields.String,
            report_type=EnumField(ReportType, default=ReportType.DAILY),
            export=wa_fields.Integer(missing=0),
        )
    )
    @require_login(allow_sub_account=False)
    def get(cls, **kwargs):
        """ 经纪商返佣历史|返佣记录 """
        user_id = g.user.id
        if kwargs.get("report_type") == ReportType.MONTHLY:
            model = MonthlyBrokerAssetReport
            query = model.query.filter(
                model.user_id == user_id,
            ).order_by(model.date.desc())
        else:
            model = DailyBrokerAssetReport
            query = model.query.filter(
                model.user_id == user_id,
            ).order_by(model.date.desc())

        if not kwargs["export"]:
            return query_to_page(query, kwargs["page"], kwargs["limit"], cls.marshal_fields)
        
        if cls.limit_export_notice(user_id):
            raise InvalidArgument(message=_("已超过每日导出次数%(num)s次", num=10))

        data_list = query.paginate(1, config["EXPORT_ITEM_MAX_COUNT"], error_out=False).items
        file_name = now().strftime("%Y%m%d-refer-asset-history")
        pref = UserPreferences(user_id)
        with force_locale(pref.language.value):

            fields = ["time", "deal_user_count", "spot_trade_usd", "perpetual_trade_usd",
                      "total_trade_usd", "spot_fee_usd", "perpetual_fee_usd", "total_fee_usd", "amount"]

            headers = [_("时间"), _("总交易用户"), _("现货交易额"), _("合约交易额"),
                       _("总交易额"), _("现货手续费"), _("合约手续费"), _("总手续费"), _("返佣金额")]
            dt_to_str = partial(datetime_to_str, offset_minutes=pref.timezone_offset, fmt="%Y-%m-%d")
            export_items = []
            for d in data_list:
                item = {
                    "amount": f"{amount_to_str(d.amount)} {d.asset}",
                    "total_fee_usd": f"{amount_to_str(d.spot_fee_usd + d.perpetual_fee_usd)} USD",
                    "spot_fee_usd": f"{amount_to_str(d.spot_fee_usd)} USD",
                    "perpetual_fee_usd": f"{amount_to_str(d.perpetual_fee_usd)} USD",
                    "total_trade_usd": f"{amount_to_str(d.spot_trade_usd + d.perpetual_trade_usd)} USD",
                    "spot_trade_usd": f"{amount_to_str(d.spot_trade_usd)} USD",
                    "perpetual_trade_usd": f"{amount_to_str(d.perpetual_trade_usd)} USD",
                    "time": dt_to_str(date_to_datetime(d.date)),
                    "deal_user_count": d.deal_user_count,
                }
                export_items.append(item)
            stream = ExcelExporter(
                data_list=export_items,
                headers=headers,
                fields=fields,
            ).export_streams()

            return send_file(
                stream,
                download_name=f"{file_name}.xlsx",
                as_attachment=True,
            )


@ns.route("/reports/detail")
@respond_with_code
class BrokerReportDetailResource(Resource):
    marshal_fields = {
        "time": TimestampMarshalField(attribute="date"),
        "user_id": fx_fields.Integer,
        "asset": fx_fields.String,
        "amount": AmountField(attribute=lambda x: amount_to_str(x.amount, PrecisionEnum.COIN_PLACES)),
        "spot_trade_usd": AmountField(attribute=lambda x: amount_to_str(x.spot_trade_usd, PrecisionEnum.CASH_PLACES)),
        "perpetual_trade_usd": AmountField(
            attribute=lambda x: amount_to_str(x.perpetual_trade_usd, PrecisionEnum.CASH_PLACES)),
        "total_trade_usd": AmountField(
            attribute=lambda x: amount_to_str(x.spot_trade_usd + x.perpetual_trade_usd, PrecisionEnum.CASH_PLACES)),
        "spot_fee_usd": AmountField(attribute=lambda x: amount_to_str(x.spot_fee_usd, PrecisionEnum.CASH_PLACES)),
        "perpetual_fee_usd": AmountField(
            attribute=lambda x: amount_to_str(x.perpetual_fee_usd, PrecisionEnum.CASH_PLACES)),
        "total_fee_usd": AmountField(
            attribute=lambda x: amount_to_str(x.spot_fee_usd + x.perpetual_fee_usd, PrecisionEnum.CASH_PLACES)),
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            report_date=TimestampField(to_date=True, required=True),
            report_type=EnumField(ReportType, default=ReportType.DAILY),
            page=PageField,
            limit=LimitField,
        )
    )
    @require_login(allow_sub_account=False)
    def get(cls, **kwargs):
        """ 明细-经纪商返佣历史|返佣记录 """
        user_id = g.user.id
        model = DailyBrokerUserAssetReport
        report_date = kwargs['report_date']
        if kwargs.get("report_type") == ReportType.MONTHLY:
            next_month_date = next_month(report_date.year, report_date.month)
            query = model.query.filter(
                model.broker_user_id == user_id,
                model.date >= report_date,
                model.date < next_month_date,
            )
        else:
            query = model.query.filter(
                model.broker_user_id == user_id,
                model.date == report_date,
            )
        query = query.order_by(model.date.desc())
        ret = query_to_page(query, kwargs["page"], kwargs["limit"], cls.marshal_fields)
        referree_ids = {x['user_id'] for x in ret['data']}
        mask_user_mapping = cls._get_or_create_mask_user_mapping(referree_ids)
        hidden_accounts = cls._get_user_hidden_accounts(referree_ids)
        for item in ret['data']:
            user_id = item.pop('user_id')
            mask_id = mask_user_mapping[user_id]
            hidden_account = hidden_accounts[user_id]
            item['account'] = f'{mask_id}({hidden_account})'
        return ret

    @classmethod
    def _get_or_create_mask_user_mapping(cls, user_ids):
        mask_user_mapping = MaskUser.get_mask_user_mapping(user_ids)
        to_creates = set(user_ids) - set(mask_user_mapping.keys())
        for user_id in to_creates:
            new_mask = MaskUser.new_mask_user(user_id)
            mask_user_mapping.update({user_id: new_mask.mask_id})
        return mask_user_mapping

    @classmethod
    def _get_user_hidden_accounts(cls, user_ids):
        user_emails = {}
        for chunk_user_ids in batch_iter(user_ids, 1000):
            users = User.query.filter(User.id.in_(chunk_user_ids)).all()
            user_emails.update({user.id: user.hidden_complete_name for user in users})
        return user_emails


@ns.route("/reports/referral-detail")
@respond_with_code
class BrokerReportReferralDetailResource(Resource):
    marshal_fields = {
        "time": TimestampMarshalField(attribute="date"),
        "asset": fx_fields.String,
        "amount": AmountField(attribute=lambda x: amount_to_str(x.amount, PrecisionEnum.COIN_PLACES)),
        "spot_trade_usd": AmountField(attribute=lambda x: amount_to_str(x.spot_trade_usd, PrecisionEnum.CASH_PLACES)),
        "perpetual_trade_usd": AmountField(
            attribute=lambda x: amount_to_str(x.perpetual_trade_usd, PrecisionEnum.CASH_PLACES)),
        "total_trade_usd": AmountField(
            attribute=lambda x: amount_to_str(x.spot_trade_usd + x.perpetual_trade_usd, PrecisionEnum.CASH_PLACES)),
        "spot_fee_usd": AmountField(attribute=lambda x: amount_to_str(x.spot_fee_usd, PrecisionEnum.CASH_PLACES)),
        "perpetual_fee_usd": AmountField(
            attribute=lambda x: amount_to_str(x.perpetual_fee_usd, PrecisionEnum.CASH_PLACES)),
        "total_fee_usd": AmountField(
            attribute=lambda x: amount_to_str(x.spot_fee_usd + x.perpetual_fee_usd, PrecisionEnum.CASH_PLACES)),
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.String(required=True),
        )
    )
    @require_login(allow_sub_account=False)
    def get(cls, **kwargs):
        """ 经纪商返佣历史|返佣记录-用户月度明细 """
        broker_user_id = g.user.id
        model = MonthlyBrokerUserAssetReport
        mask_id = kwargs['user_id']
        user_id = cls._get_user_id_by(mask_id)
        if not user_id:
            raise InvalidArgument
        query = model.query.filter(
            model.broker_user_id == broker_user_id,
            model.user_id == user_id,
        ).order_by(model.date.desc())
        ret = query_to_page(query, 1, 12, cls.marshal_fields)
        return ret

    @classmethod
    def _get_user_id_by(cls, mask_id: str) -> int | None:
        model = MaskUser
        row = model.query.with_entities(
            model.user_id
        ).filter(
            model.mask_id == mask_id
        ).first()
        return row.user_id if row else None


@ns.route('/upload/file')
@respond_with_code
class FileUploadResource(Resource):

    @classmethod
    @require_login
    def post(cls):
        """
        经纪商图片/文件上传
        """
        file_ = request.files.get('my_file')
        if not file_:
            raise InvalidArgument

        ext = os.path.splitext(file_.filename)[1].lower()

        if ext not in {'.png', '.jpeg', '.jpg', ".pdf", ".doc", ".docx"}:
            raise InvalidArgument
        if int(request.headers['CONTENT_LENGTH']) > 10 * 1024 * 1024:
            raise FileTooBig

        file_type = ext.split('.')[1]
        file_key = AWSBucketPrivate.new_file_key(suffix=file_type)
        if not AWSBucketPrivate.put_file(
                file_key, file_):
            raise ServiceUnavailable
        url = AWSBucketPrivate.get_file_url(file_key)
        return dict(file_url=url, file_key=file_key)