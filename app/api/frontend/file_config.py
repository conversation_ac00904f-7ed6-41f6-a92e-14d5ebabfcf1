# -*- coding: utf-8 -*-

"""该文件的API已不再维护，App兼容保留"""

from webargs import fields as wa_fields

from ...caches import AssetFileConfigViewCache
from ..common import Namespace, Resource, respond_with_code, json_string_success
from ...business import mem_cached
from ...exceptions import InvalidArgument


ns = Namespace('File Config')
url_prefix = '/file_config'


@ns.route('/json')
@respond_with_code
class JsonFileResource(Resource):

    CONFIGS = {
        'coinex_pc_tag_asset':  AssetFileConfigViewCache.PC,
        'coinex_mobile_tag_asset': AssetFileConfigViewCache.MOBILE
    }

    @classmethod
    @ns.use_kwargs(dict(
        project=wa_fields.String(required=True),
        name=wa_fields.String(required=True)
    ))
    def get(cls, **kwargs):
        project = kwargs['project']
        name = kwargs['name']
        if not (key := cls.CONFIGS.get(f"{project}_{name}")):
            raise InvalidArgument
        return cls._get(key)

    @classmethod
    @mem_cached(120)
    def _get(cls, name):
        cache = AssetFileConfigViewCache(name)
        if data := cache.read():
            return json_string_success(data)
        return cache.reload()[name]
