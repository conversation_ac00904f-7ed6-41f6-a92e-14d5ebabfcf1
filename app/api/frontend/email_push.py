# -*- coding: utf-8 -*-
from webargs import fields

from ..common import Namespace, Resource, respond_with_code
from ...business.push_statistic import EmailPushReadCountStatistic

url_prefix = '/email_push'
ns = Namespace('Email Push')


# noinspection PyUnresolvedReferences
@ns.route('/count/<int:id_>')
@respond_with_code
class EmailPushCountResource(Resource):
    """已废弃，兼容保留"""

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String,
    ))
    def get(cls, id_, **kwargs):
        token = kwargs.get('token')
        if token:
            EmailPushReadCountStatistic.add_to_cache(id_, token)
