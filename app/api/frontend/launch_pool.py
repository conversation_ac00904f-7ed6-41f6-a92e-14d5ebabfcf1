# -*- coding: utf-8 -*-
import json
from decimal import Decimal, ROUND_<PERSON>OW<PERSON>
from typing import List

from flask import g
from flask_babel import gettext
from marshmallow import fields as mm_fields
from sqlalchemy import or_

from app.models import db, User, CoinInformation, CoinInformationTrans
from app.models.activity import (
    LaunchMiningProject,
    LaunchMiningPool,
    LaunchMiningPoolUserInfo,
    LaunchMiningPoolOperateHistory,
)
from app.exceptions import InvalidArgument, NoKycQualifications
from app.common import Language, PrecisionEnum
from app.business import CacheLock, LockKeys, UserPreferences
from app.business.activity.launch_pool import LaunchMiningOp
from app.business.user import UserRepository, require_user_not_only_withdrawal
from app.caches.activity import (
    LaunchProjectCache, LaunchProjectDetailCache, LaunchProjectLastRewardCache,
    LaunchProjectRewardAssetsCache,
)
from app.api.common import (
    Resource,
    Namespace,
    respond_with_code,
    require_login,
    lock_request,
    get_request_user,
    json_string_success,
)
from app.api.common.fields import <PERSON>itField, PageField, PositiveDecimalField, EnumField
from app.utils import current_timestamp


ns = Namespace("LaunchPool")
url_prefix = '/launchpool'


@ns.route("/projects")
@respond_with_code
class LaunchPoolProjectsResource(Resource):
    @classmethod
    def fill_extra_fields(cls, pcj_items: list[dict], lang: Language):
        """ 池子总质押数需要实时 """
        if not pcj_items:
            return

        reward_assets = {p["reward_asset"] for p in pcj_items}
        coin_desc_rows = CoinInformationTrans.query.join(
            CoinInformation,
            CoinInformationTrans.coin_information_id == CoinInformation.id,
        ).filter(
            CoinInformation.code.in_(reward_assets),
            CoinInformationTrans.lang == lang,
        ).with_entities(
            CoinInformation.code,
            CoinInformationTrans.description,
        ).all()
        reward_asset_desc_map = dict(coin_desc_rows)

        cur_ts = current_timestamp(to_int=True)
        pool_ids = {j["pool_id"] for i in pcj_items for j in i["pools"] if int(i['end_time']) > cur_ts}
        pool_rows: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.id.in_(pool_ids),
        ).with_entities(
            LaunchMiningPool.id,
            LaunchMiningPool.stake_amount,
        ).all()
        pool_stake_amount_map = dict(pool_rows)
        for i in pcj_items:
            i["description"] = reward_asset_desc_map.get(i["reward_asset"], "")
            if int(i['end_time']) > cur_ts:
                for p in i["pools"]:
                    if p["pool_id"] not in pool_stake_amount_map:
                        continue
                    p["total_stake_amount"] = max(pool_stake_amount_map.get(p["pool_id"], Decimal()), Decimal())

    @classmethod
    @ns.use_kwargs(
        dict(
            scope=EnumField(['ALL', 'SELF'], missing='ALL'),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ LaunchPool 项目列表 """
        page, limit = kwargs["page"], kwargs["limit"]
        lang = Language(g.lang)
        scope = kwargs['scope']
        is_self = scope == 'SELF'
        if not is_self:
            data = LaunchProjectCache().paginate(page, limit)
            cls.fill_extra_fields(data["data"], lang)
            return data

        user = get_request_user(allow_none=True)
        if not user:
            raise InvalidArgument

        q = LaunchMiningPoolUserInfo.query.join(
            LaunchMiningProject,
            LaunchMiningPoolUserInfo.project_id == LaunchMiningProject.id,
        ).filter(
            LaunchMiningPoolUserInfo.user_id == user.id,
            LaunchMiningProject.status.in_(
                [LaunchMiningProject.Status.ONLINE, LaunchMiningProject.Status.CLOSED]
            ),
        ).order_by(
            LaunchMiningPoolUserInfo.project_id.desc()
        ).with_entities(
            LaunchMiningPoolUserInfo.project_id.distinct().label('project_id'),
        )
        pagination = q.paginate(page, limit, error_out=False)
        rows: List[LaunchMiningPoolOperateHistory] = pagination.items
        if rows:
            project_ids = [str(i.project_id) for i in rows]
            project_infos = LaunchProjectDetailCache().hmget(project_ids)
            project_items = [json.loads(i) for i in project_infos if i]
            cls.fill_extra_fields(project_items, lang)
        else:
            project_items = []
        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(project_items),
            data=project_items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/project/<int:id_>")
@respond_with_code
class LaunchPoolProjectDetailResource(Resource):
    @classmethod
    def get(cls, id_):
        """ LaunchPool 项目详情 """
        data = LaunchProjectDetailCache().hget(str(id_))
        if not data:
            raise InvalidArgument
        lang = Language(g.lang)
        info = json.loads(data)
        LaunchPoolProjectsResource.fill_extra_fields([info], lang)
        return info


@ns.route("/reward-assets")
@respond_with_code
class LaunchPoolRewardAssetsResource(Resource):
    @classmethod
    def get(cls):
        """ LaunchPool 奖励币种列表 """
        cache = LaunchProjectRewardAssetsCache()
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route("/opening-agreement")
@respond_with_code
class UserOpeningAgreementResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 用户挖矿协议状态 """
        user = g.user
        result = {
            "opening_agreement": UserPreferences(user.id).opening_launch_pool_mining,
        }
        return result

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        """ 用户同意挖矿协议 """
        user = g.user
        pref = UserPreferences(user.id)
        if not pref.opening_launch_pool_mining:
            pref.opening_launch_pool_mining = True


@ns.route("/user-info")
@respond_with_code
class LaunchPoolUserInfoResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            project_id=mm_fields.Integer(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 用户参与信息 """
        user = g.user
        user_id = user.id
        project_id = kwargs["project_id"]

        project: LaunchMiningProject = LaunchMiningProject.query.filter(
            LaunchMiningProject.id == project_id,
        ).first()
        if not project:
            raise InvalidArgument

        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == project_id,
        ).all()
        user_info_rows: list[LaunchMiningPoolUserInfo] = LaunchMiningPoolUserInfo.query.filter(
            LaunchMiningPoolUserInfo.project_id == project_id,
            LaunchMiningPoolUserInfo.user_id == user_id,
        ).all()
        user_pool_info_map = {i.pool_id: i for i in user_info_rows}

        reward_cache = LaunchProjectLastRewardCache(project_id)
        if user_pool_info_map and project.is_running:
            pool_last_reward_map = reward_cache.get_user_rewards(user_id)
        else:
            pool_last_reward_map = {}

        zero = Decimal('0')
        pool_cur_reward_map = {}
        user_pool_info_list = []
        for pool in pools:
            info_row = user_pool_info_map.get(pool.id)
            if info_row:
                stake_amount = info_row.stake_amount
                reward_amount = info_row.total_reward_amount
                pool_cur_reward_map[pool.id] = reward_amount
            else:
                stake_amount = reward_amount = zero
            if project.is_running:
                last_reward_amount = pool_last_reward_map.get(pool.id, zero)
            else:
                last_reward_amount = reward_amount
            info = {
                'stake_asset': pool.stake_asset,
                'stake_amount': stake_amount,
                'reward_asset': pool.reward_asset,
                'reward_amount': reward_amount,
                'last_reward_amount': last_reward_amount
            }
            user_pool_info_list.append(info)
        result = {
            "opening_agreement": UserPreferences(user.id).opening_launch_pool_mining,
            "is_black_user": UserRepository.is_abnormal_user(user_id),
            "is_clear_user": LaunchMiningOp.is_clear_user(user_id),
            "is_join": bool(user_pool_info_map),
            "pools": user_pool_info_list,
        }
        if pool_cur_reward_map and project.is_running:
            reward_cache.save_user_rewards(user_id, pool_cur_reward_map)
        return result


@ns.route("/stake")
@respond_with_code
class LaunchPoolStakeOperateResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            pool_id=mm_fields.Integer(required=True),
            amount=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 用户存入质押币 """
        user = g.user
        if user.kyc_status != User.KYCStatus.PASSED:
            raise NoKycQualifications
        if LaunchMiningOp.is_clear_user(user.id):
            raise InvalidArgument(message=gettext("你的账号处于清退状态，无法参与挖矿。如需更多帮助，可提交工单咨询。"))
        if UserRepository.is_abnormal_user(user.id):
            raise InvalidArgument(message=gettext("该账号异常，无法参与挖矿，请提交工单处理。"))
        require_user_not_only_withdrawal(user)

        pool_id = kwargs["pool_id"]
        amount = kwargs["amount"]
        pool: LaunchMiningPool = LaunchMiningPool.query.filter(
            LaunchMiningPool.id == pool_id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).first()
        if not pool:
            raise InvalidArgument
        if amount < pool.user_min_stake_amount:
            raise InvalidArgument
        project: LaunchMiningProject = LaunchMiningProject.query.filter(
            LaunchMiningProject.id == pool.project_id,
        ).first()
        if not project:
            raise InvalidArgument
        if not project.can_staking:
            raise InvalidArgument

        with CacheLock(key=LockKeys.launch_pool(pool_id), wait=False):
            db.session.rollback()
            if not project.can_staking:
                raise InvalidArgument
            LaunchMiningOp.stake(project, pool, user.id, amount)

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            pool_id=mm_fields.Integer(required=True),
            amount=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 用户取出质押币 """
        user = g.user
        pool_id = kwargs["pool_id"]
        amount = kwargs["amount"]
        pool: LaunchMiningPool = LaunchMiningPool.query.filter(
            LaunchMiningPool.id == pool_id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).first()
        if not pool:
            raise InvalidArgument
        project: LaunchMiningProject = LaunchMiningProject.query.filter(
            LaunchMiningProject.id == pool.project_id,
        ).first()
        if not project:
            raise InvalidArgument

        with CacheLock(key=LockKeys.launch_pool(pool_id), wait=False):
            db.session.rollback()
            if project.status != LaunchMiningProject.Status.ONLINE:
                raise InvalidArgument
            LaunchMiningOp.retrieve(project, pool, user.id, amount, True)


@ns.route("/reward-history")
@respond_with_code
class LaunchPoolRewardHistoryResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            reward_asset=mm_fields.String,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 历史收益记录 """
        user = g.user

        q = LaunchMiningPoolUserInfo.query.filter(
            LaunchMiningPoolUserInfo.user_id == user.id,
            LaunchMiningPoolUserInfo.reward_status == LaunchMiningPoolUserInfo.RewardStatus.FINISHED,
        ).order_by(LaunchMiningPoolUserInfo.id.desc())
        if reward_asset := kwargs.get("reward_asset"):
            q = q.filter(LaunchMiningPoolUserInfo.reward_asset == reward_asset)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[LaunchMiningPoolUserInfo] = pagination.items

        pool_ids = [i.pool_id for i in rows]
        pools = LaunchMiningPool.query.filter(
            LaunchMiningPool.id.in_(pool_ids),
        ).all()
        pool_map = {i.id: i for i in pools}

        pcj_ids = {i.project_id for i in rows}
        pcj_rows = LaunchMiningProject.query.filter(
            LaunchMiningProject.id.in_(pcj_ids),
        ).all()
        project_map = {i.id: i for i in pcj_rows}

        items = []
        for r in rows:
            pool = pool_map[r.pool_id]
            project = project_map[r.project_id]
            end_time = project.close_time or project.end_time
            items.append(
                dict(
                    project_name=project.name,
                    reward_asset=pool.reward_asset,
                    reward_amount=r.total_reward_amount,
                    stake_asset=pool.stake_asset,
                    stake_amount=r.stake_amount,
                    end_time=int(end_time.timestamp()),
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/operate-history")
@respond_with_code
class LaunchPoolOperateHistoryResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            reward_asset=mm_fields.String,
            type=EnumField(LaunchMiningPoolOperateHistory.Type),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 挖矿操作记录 """
        user = g.user

        q = LaunchMiningPoolOperateHistory.query.filter(
            LaunchMiningPoolOperateHistory.user_id == user.id,
            or_(
                LaunchMiningPoolOperateHistory.status == LaunchMiningPoolOperateHistory.Status.FINISHED,
                # REWARD_SETTLEMENT可能存在失败记录需要展示
                LaunchMiningPoolOperateHistory.type == LaunchMiningPoolOperateHistory.Type.REWARD_SETTLEMENT,
            )
        ).order_by(LaunchMiningPoolOperateHistory.time.desc())
        if reward_asset := kwargs.get("reward_asset"):
            q = q.filter(LaunchMiningPoolOperateHistory.reward_asset == reward_asset)
        if type_ := kwargs.get("type"):
            q = q.filter(LaunchMiningPoolOperateHistory.type == type_)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[LaunchMiningPoolOperateHistory] = pagination.items

        project_ids = {i.project_id for i in rows}
        projects = LaunchMiningProject.query.filter(
            LaunchMiningProject.id.in_(project_ids),
        ).all()
        project_map = {i.id: i for i in projects}

        items = []
        for r in rows:
            project = project_map[r.project_id]
            items.append(
                dict(
                    project_name=project.name,
                    type=r.type.name,
                    asset=r.asset,
                    amount=r.amount,
                    time=int(r.time.timestamp()),
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )
