from flask import g
from flask_babel import gettext
from webargs import fields as wa_fields

from app.api.common import Namespace, respond_with_code, Resource, get_request_user
from app.api.common.fields import <PERSON><PERSON>ield, LimitField, EnumField
from app.api.common.request import is_old_app_request
from app.business.mission_center.mission import UserMissionBiz, MissionUtils
from app.models.equity_center import EquityType
from app.models.mission_center import UserMission, SceneType, MissionCondition
from app.exceptions import InvalidArgument
from app.utils import amount_to_str

ns = Namespace("Mission Center")
url_prefix = "/reward-center"


class MissionTasksMixin:

    @classmethod
    def _sort(cls, mission_data: dict) -> tuple:
        """
        排序规则:
        a.根据任务状态排序，从上往下依次为：进行中>结算中>已完成>已过期；
        b.每个状态下的新手任务根据后台配置顺序排序。
        :param mission_data:
        :return:
        """
        scene_score = 1 if mission_data['scene_type'] != SceneType.NEWBIE.name else 0
        status_score = 0
        match mission_data['status']:
            case UserMission.Status.PENDING.name:
                status_score = 0
            case UserMission.Status.SETTLING.name:
                status_score = 1
            case UserMission.Status.FINISHED.name:
                status_score = 2
            case UserMission.Status.EXPIRED.name:
                status_score = 3
            case _:
                scene_score = 5
        return scene_score, status_score, mission_data['sequence']


@ns.route("/tasks")
@respond_with_code
class MissionTasksResource(Resource, MissionTasksMixin):
    
    NEW_MISSION_CONDITION_TYPES = [
        MissionCondition.COPY_TRADING_ONCE.name,
        MissionCondition.DEMO_TRADING_ONCE.name,
        MissionCondition.PERPETUAL_AMOUNT.name,
    ]
    
    @classmethod
    def filter_app_user_missions(cls, user_mission_data: list[dict]) -> list[dict]:
        if not is_old_app_request(3470, 90):  # app兼容老设备展示任务数据
            return user_mission_data
        return [
            mission for mission in user_mission_data
            if mission['mission_type'] not in cls.NEW_MISSION_CONDITION_TYPES
        ]

    @classmethod
    @ns.use_kwargs(dict(
        channel=wa_fields.String,
        refer_code=wa_fields.String,
        device_id=wa_fields.String,
        status=EnumField(UserMission.Status),
        page=PageField(missing=1),
        limit=LimitField(missing=10),
    ))
    def get(cls, **kwargs):
        page, limit = kwargs['page'], kwargs['limit']
        user = get_request_user(allow_none=True)
        if not user and MissionUtils.check_device_registered(kwargs.get("device_id")):
            return dict(
                total=0,
                curr_page=1,
                has_next=False,
                count=0,
                data=[]
            )
        user_params = MissionUtils.get_user_params(kwargs, g.get('user_tz_offset'))
        user_mission_data = UserMissionBiz.get_user_missions(user, user_params, kwargs.get("status"))
        filter_user_mission = cls.filter_app_user_missions(user_mission_data)
        items = sorted(filter_user_mission, key=cls._sort)[(page - 1) * limit: page * limit]
        total = len(filter_user_mission)
        return dict(
            total=total,
            curr_page=page,
            has_next=page * limit < total,
            count=len(items),
            data=items,
        )


@ns.route("/tasks/<int:id_>")
@respond_with_code
class MissionInfoResource(Resource):

    @classmethod
    def format_mission_rule_and_reward_desc(cls, mission_info: dict) -> tuple[str, str]:
        mission_type = mission_info['mission_type']
        reward_type = mission_info['reward']['reward_type']
        if mission_info['mission_type'] == MissionCondition.DEPOSIT_AMOUNT.name:
            mission_rule = gettext(
                "注册 %(deadline_days)s 天内完成%(mission_type)s，且累积金额 ≥ %(total)s %(unit)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "1. 请务必在规定时间内完成任务，超出活动时间的入金将无法计入有效金额；\n"
                "2. 链上充值、P2P买币、第三方买币均为有效入金，不包括站内转账和C-Box转账。",
                deadline_days=mission_info['deadline_days'],
                mission_type=gettext(MissionCondition[mission_type].value),
                total=amount_to_str(mission_info['progress']['total']),
                unit=mission_info['progress']['unit'],
                value=amount_to_str(mission_info['reward']['value']),
                value_type=mission_info['reward']['value_type'],
                reward_type=gettext(EquityType[reward_type].value),
            )
        elif mission_info['mission_type'] == MissionCondition.SPOT_AMOUNT.name:
            mission_rule = gettext(
                "注册 %(deadline_days)s 天内完成%(mission_type)s，且累积金额 ≥ %(total)s %(unit)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "1. 请务必在规定时间内完成任务，超出活动时间的币币交易将无法计入有效交易额；\n"
                "2. 兑换交易、现货交易、杠杆交易、策略交易均为有效币币交易；\n"
                "3. 子账户无法独立参与任务，交易金额将合并计入主账户。",
                deadline_days=mission_info['deadline_days'],
                mission_type=gettext(MissionCondition[mission_type].value),
                total=amount_to_str(mission_info['progress']['total']),
                unit=mission_info['progress']['unit'],
                value=amount_to_str(mission_info['reward']['value']),
                value_type=mission_info['reward']['value_type'],
                reward_type=gettext(EquityType[reward_type].value),
            )
        elif mission_info['mission_type'] == MissionCondition.PERPETUAL_AMOUNT.name:
            mission_rule = gettext(
                "注册 %(deadline_days)s 天内完成%(mission_type)s，且累积金额 ≥ %(total)s %(unit)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "1. 请务必在规定时间内完成任务，超出活动时间的合约交易将无法计入有效交易额；\n"
                "2. 正向合约和反向合约均为有效合约交易；\n"
                "3. 子账户无法独立参与任务，交易金额将合并计入主账户。",
                deadline_days=mission_info['deadline_days'],
                mission_type=gettext(MissionCondition[mission_type].value),
                total=amount_to_str(mission_info['progress']['total']),
                unit=mission_info['progress']['unit'],
                value=amount_to_str(mission_info['reward']['value']),
                value_type=mission_info['reward']['value_type'],
                reward_type=gettext(EquityType[reward_type].value),
            )
        elif mission_info['mission_type'] == MissionCondition.COPY_TRADING_ONCE.name:
            mission_rule = gettext(
                "注册 %(deadline_days)s 天内完成一次%(mission_type)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "请务必在规定时间内完成任务，超出活动时间的跟单交易将不再统计。",
                deadline_days=mission_info['deadline_days'],
                mission_type=gettext(MissionCondition[mission_type].value),
                value=amount_to_str(mission_info['reward']['value']),
                value_type=mission_info['reward']['value_type'],
                reward_type=gettext(EquityType[reward_type].value),
            )
        elif mission_info['mission_type'] == MissionCondition.DEMO_TRADING_ONCE.name:
            mission_rule = gettext(
                "注册 %(deadline_days)s 天内完成一次%(mission_type)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "请务必在规定时间内完成任务，超出活动时间的模拟交易将不再统计。",
                deadline_days=mission_info['deadline_days'],
                mission_type=gettext(MissionCondition[mission_type].value),
                value=amount_to_str(mission_info['reward']['value']),
                value_type=mission_info['reward']['value_type'],
                reward_type=gettext(EquityType[reward_type].value),
            )
        else:
            mission_rule = ""

        reward_info = mission_info['reward']
        if reward_info['reward_type'] == EquityType.CASHBACK.name:
            reward_desc = gettext(
                "手续费返现奖励将在任务达标后发放并立即生效。\n"
                "请在有效期内根据规则使用权益，并获得手续费返现。\n"
                "请在「我的奖励」页面查看详情。\n\n"
                "CoinEx禁止作弊行为，如批量注册小号等，一经发现将取消其获奖资格。"
            )
        elif reward_info['reward_type'] == EquityType.AIRDROP.name:
            reward_desc = gettext(
                "空投奖励将在任务达标后发放至现货账户。\n"
                "请在「我的奖励」页面查看详情。\n\n"
                "CoinEx禁止作弊行为，如批量注册小号等，一经发现将取消其获奖资格。"
            )
        else:
            reward_desc = ""
        return mission_rule, reward_desc

    @classmethod
    def get(cls, id_):
        """
        任务详情
        :param id_: 用户登录的时候是 user_mission_id 未登录是 mission_id
        :return:
        """
        user = get_request_user(allow_none=True)
        data = UserMissionBiz.get_user_mission_info(user, id_)
        if not data:
            raise InvalidArgument
        mission_rule, reward_desc = cls.format_mission_rule_and_reward_desc(data)
        data['mission_rule'] = mission_rule
        data['reward_desc'] = reward_desc
        return data


@ns.route("/banner")
@respond_with_code
class MissionBannerResource(Resource, MissionTasksMixin):

    @classmethod
    @ns.use_kwargs(dict(
        channel=wa_fields.String,
        refer_code=wa_fields.String,
        device_id=wa_fields.String,
    ))
    def get(cls, **kwargs):
        user = get_request_user(allow_none=True)
        if not user and MissionUtils.check_device_registered(kwargs.get("device_id")):
            return []
        user_params = MissionUtils.get_user_params(kwargs, g.get('user_tz_offset'))
        user_mission_data = UserMissionBiz.get_user_missions(user, user_params, UserMission.Status.PENDING)
        data = sorted(user_mission_data, key=cls._sort)
        return data
