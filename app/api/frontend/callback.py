# -*- coding: utf-8 -*-
from enum import Enum
import hashlib
from hmac import HMAC
from hashlib import sha256
import hmac
import json
from app.api.common.fields import Enum<PERSON>ield

from flask import make_response, request, current_app
from marshmallow import EXCLUDE, Schema, fields
from app.business.user import UserPreferences
from app.caches.flow_control import Sms<PERSON>ser<PERSON>ache, SmsSendResultCache
from app.exceptions.basic import InvalidArgument
from app.utils.date_ import current_timestamp
from app.utils.sms import SMSProvider

from ..common import Namespace, Resource, respond_with_code
from ...business.fiat import PaxfulClient, XanPoolClient
from ...business.kyc import send_sumsub_result_rechange_email_task, SumsubClient
from ...config import config
from ...caches import TelegramBindingTokenCache
from ...models import UserBindingAccount, FiatOrder, db, \
    KycVerificationHistory, KycVerification
from ...business import TelegramBot<PERSON>lient, <PERSON>ache<PERSON><PERSON>, <PERSON><PERSON><PERSON>s
from ...exceptions import AccountBoundByAnOther
from ...utils import now

ns = Namespace('Callback')


@ns.route('/telegram/<token>')
class TelegramWebhookResource(Resource):

    _BOUND_SUCCESS = ("Your CoinEx account has successfully "
                      "been bound with telegram account.")
    _BOUND_ERROR = ("This telegram account has been bound "
                    "with another CoinEx account.")

    @classmethod
    def post(cls, token):
        success = make_response('', 200)
        if token != config['TELEGRAM_BOT']['token']:
            return make_response('', 404)
        if not (update := request.get_json(silent=True)):
            return success
        command = {}
        if not cls.try_parse_command(update, command):
            return success
        if command['cmd'] != '/start':
            return success
        cache = TelegramBindingTokenCache(command['arg'])
        if not (user_id := cache.read()):
            return success
        user_id = int(user_id)
        from_ = command['from']
        if not (user_name := from_.get('user_name')):
            user_name = '{} {}'.format(from_.get('first_name', ''),
                                       from_.get('last_name', '')
                              ).strip()
        try:
            UserBindingAccount.add_or_update(
                user_id=user_id,
                account_type=UserBindingAccount.AccountType.TELEGRAM,
                account_id=str(from_['id']),
                account_name=user_name
            )
        except AccountBoundByAnOther:
            reply = cls._BOUND_ERROR
        else:
            reply = cls._BOUND_SUCCESS

        try:
            TelegramBotClient().send_message(from_['id'], reply)
        except Exception as e:
            current_app.logger.info('telegram bound reply error: %s', e)
        return success


    @classmethod
    def try_parse_command(cls, update, command):
        """
        see https://core.telegram.org/bots/api#update
        """
        if not (message := update.get('message')):
            return False
        if not (from_ := message.get('from')):
            return False
        if from_['is_bot']:
            return False
        if not (text := message.get('text')):
            return False
        if not (entities := message.get('entities')):
            return False
        entity = entities[0]
        if entity['type'] != 'bot_command':
            return False
        offset, length = entity['offset'], entity['length']
        cmd, arg = text[offset:length], text[length+1:]
        command.update({
            'cmd': cmd,
            'arg': arg,
            'from': from_
        })
        return True


@ns.route('/paxful')
class PaxfulCallBack(Resource):

    status_mapping = {
        "SUCCESS": FiatOrder.StatusType.APPROVED,
        "CANCELLED": FiatOrder.StatusType.DECLINED,
        "EXPIRED": FiatOrder.StatusType.DECLINED
    }

    @classmethod
    def post(cls):
        track_id = request.form.get("track_id", None)
        status = request.form.get("status", None)
        client = PaxfulClient()
        if not track_id:
            return
        fiat_order: FiatOrder = FiatOrder.query.filter(
            FiatOrder.third_party == client.name,
            FiatOrder.payment_id == track_id
        ).first()
        if not fiat_order:
            return
        if status not in cls.status_mapping.keys():
            raise ValueError("Paxful CallBack Status Error")
        status = cls.status_mapping[status]
        fiat_order.status = status
        if status == FiatOrder.StatusType.APPROVED:
            fiat_order.approved_at = now()
        db.session.commit()


@ns.route("/xanpool")
class XanPoolCallBack(Resource):
    status_mapping = {
        "pending": FiatOrder.StatusType.PENDING,
        "completed": FiatOrder.StatusType.APPROVED,
        "fiat_received": FiatOrder.StatusType.PENDING,
        "payout_failed": FiatOrder.StatusType.DECLINED,
        "refunded": FiatOrder.StatusType.REFUNDED,
    }

    @classmethod
    def post(cls):
        payload = request.get_json(silent=True).get("payload")
        if not payload:
            return
        client = XanPoolClient()
        fiat_order: FiatOrder = FiatOrder.query.filter(
            FiatOrder.third_party == client.name,
            FiatOrder.payment_id == payload.get("partnerData")
        ).first()
        if not fiat_order:
            return
        if status := payload.get("status"):
            if sys_status := cls.status_mapping.get(status):
                fiat_order.status = sys_status
                if sys_status == FiatOrder.StatusType.APPROVED:
                    fiat_order.approved_at = now()
        if fiat := payload.get("fiat"):
            fiat_order.fiat_total_amount = fiat
        if total := payload.get("total"):
            fiat_order.coin_amount = total
        db.session.commit()


@ns.route("/onmeta")
class OnMetaCallBack(Resource):
    status_mapping = {
        "fiatPending": FiatOrder.StatusType.PENDING,
        "orderReceived": FiatOrder.StatusType.PENDING,
        "InProgress": FiatOrder.StatusType.PENDING,
        "fiatReceived": FiatOrder.StatusType.PENDING,
        "transferred": FiatOrder.StatusType.PENDING,
        "cryptoReceived": FiatOrder.StatusType.PENDING,
        "completed": FiatOrder.StatusType.APPROVED,
        "payoutSuccess": FiatOrder.StatusType.APPROVED,
        "expired": FiatOrder.StatusType.DECLINED,
    }

    class RequestSchema(Schema):
        fiat = fields.Decimal(required=True)
        receiverWalletAddress = fields.String()
        senderWalletAddress = fields.String()
        buyTokenSymbol = fields.String()
        sellTokenSymbol = fields.String()
        orderId = fields.String(required=True)
        currency = fields.String(required=True)
        status = fields.String(required=True)
        txnHash = fields.String(required=True)
        transferredAmount = fields.Decimal()
        tokensDeducted = fields.Decimal()
        metaData = fields.Dict(required=True)
        eventType = EnumField(['onramp', 'offramp'], required=True)

    @staticmethod
    def _validate_signature() -> bool:
        signature = request.headers.get('X-Onmeta-Signature')
        if not signature:
            return False
        data = request.json
        s = HMAC(config['ONMETA_CONFIG']['client_secret'].encode(), 
                 json.dumps(data).encode(), sha256).hexdigest()
        return s == signature

    @classmethod
    def post(cls):
        # if not cls._validate_signature():
        #     raise InvalidArgument(message="invalid signature")
        payload = request.json
        if not payload:
            raise InvalidArgument(message="invalid payload")
        sch = cls.RequestSchema()
        try:
            data = sch.load(payload, unknown=EXCLUDE)
        except Exception:
            current_app.logger.error(f"invalid Onmeta payload.")
            return
        if data['status'] not in cls.status_mapping or cls.status_mapping[data['status']] not in (
                FiatOrder.StatusType.APPROVED, FiatOrder.StatusType.DECLINED):
            return
        order_info = data['metaData']['submeta1']
        order_info = json.loads(order_info)
        order_id = order_info['order_id']
        order: FiatOrder = FiatOrder.query.filter(FiatOrder.order_id == order_id).first()
        if not order:
            return
        order.status = cls.status_mapping[data['status']]
        order.asset = data.get('buyTokenSymbol') or data.get('sellTokenSymbol') or order.asset  
        order.fiat_currency = data['currency'].upper()
        order.deposit_address = data.get('receiverWalletAddress') or data.get('senderWalletAddress')
        order.tx_id = data['txnHash']
        order.order_type = FiatOrder.OrderType.BUY \
            if data['eventType'] == 'onramp' else FiatOrder.OrderType.SELL
        order.coin_amount = data['transferredAmount'] \
            if order.order_type == FiatOrder.OrderType.BUY else data['tokensDeducted']
        order.fiat_total_amount = data['fiat']
        if order.status == FiatOrder.StatusType.APPROVED:
            order.approved_at = now()
        order.updated_at = now()
        db.session.commit()


@ns.route('/sumsub')
@respond_with_code
class SumsubCallBack(Resource):

    @staticmethod
    def _validate_signature() -> bool:
        signature = request.headers.get('x-payload-digest')
        if not signature:
            return False
        data = request.data
        s = HMAC(
            config["SUMSUB_CONFIG"]["webhook_secret_key"].encode(),
            data,
            sha256
        ).hexdigest()
        return s == signature

    @classmethod
    def post(cls):
        """
        接收Sumsub回调结果
        """
        if not cls._validate_signature():
            raise InvalidArgument(message="invalid signature")

        payload = request.get_json(silent=True)
        if not payload:
            return
        applicant_id = payload.get('applicantId')
        if not applicant_id:
            return
        if payload.get('type', '') != 'applicantReviewed':
            return
        review_status = payload.get('reviewStatus', '')
        review_result = payload.get('reviewResult', {})
        review_answer = review_result.get('reviewAnswer', '')
        if review_status != 'completed' or review_answer != 'RED':
            return

        history = KycVerificationHistory.query.filter(
            KycVerificationHistory.transaction_id == applicant_id
        ).order_by(KycVerificationHistory.id.desc()).first()
        if not history:
            return
        kyc_id = json.loads(history.detail).get('kyc_id')
        if not kyc_id:
            return
        kyc = KycVerification.query.get(kyc_id)
        if not kyc:
            return
        if kyc.status == KycVerification.Status.PASSED:
            with CacheLock(LockKeys.kyc_operation(kyc.user_id), wait=False):
                db.session.rollback()
                reject_labels = review_result.get('rejectLabels', [])
                reject_reasons = []
                for label in reject_labels:
                    reason = SumsubClient.reason_redirect_map.get(label, {}).get('user_reason')
                    if reason:
                        reject_reasons.append(reason.name)
                reject_reasons = list(set(reject_reasons))
                reason_str = ','.join(reject_reasons) or None
                history.rejection_reason = reason_str
                reject_type = review_result.get('reviewRejectType')  # RETRY FINAL
                if reject_type == 'RETRY':
                    history.reject_type = KycVerificationHistory.RejectType.RETRY
                else:
                    history.reject_type = KycVerificationHistory.RejectType.FINAL
                history.status = KycVerificationHistory.Status.RECHANGED
                db.session.commit()
                send_sumsub_result_rechange_email_task.delay(history.id)


@ns.route('/whatsapp')
@respond_with_code
class WhatsappMessageCallbackResource(Resource):

    class Status(Enum):
        SENT = 'sent'
        DELIVERED = 'delivered'
        FAILED = 'failed'
    
    @staticmethod
    def _validate_signature() -> bool:
        ycloud_sig = request.headers['Ycloud-Signature']
        parts = ycloud_sig.split(',')
        result = {}
        for p in parts:
            key, value = p.split('=')
            result[key] = value
        sig, ts = result['s'], result['t']
        body = request.get_json(silent=True)
        body = json.dumps(body, separators=(',', ':'))
        signed_payload = f'{ts}.{body}'
        expected = hmac.new(
        key=config['WHATSAPP_MESSAGE']['webhook_secret'].encode(),
        msg=signed_payload.encode(),
        digestmod=hashlib.sha256
        ).hexdigest()

        return expected == sig

    @classmethod
    def post(cls):
        """
        WhatsApp: 接收第三方回调结果
        """
        if not cls._validate_signature():
            raise InvalidArgument(message="invalid signature")
        payload = request.get_json(silent=True)
        if not payload:
            return
        type_ = payload.get('type')
        if type_ != 'whatsapp.message.updated':
            return
        content = payload['whatsappMessage']

        status = content['status']
        if not (status:= getattr(cls.Status, status.upper(), None)):
            return
        to_number = content['to']
        to_number = to_number.lstrip("+")
        SmsSendResultCache(to_number, content['id']).set(status.name.lower(), ex=SmsSendResultCache.TTL)
        cache = SmsUserCache(to_number)
        if user_id:= cache.read():
            user_id = int(user_id)
        else:
            return
        _accounts = UserPreferences(user_id).third_party_message_accounts
        if status == cls.Status.FAILED:
            if SMSProvider.WHATSAPP.name in _accounts:
                _accounts.remove(SMSProvider.WHATSAPP.name)
        elif status in (cls.Status.DELIVERED, cls.Status.SENT):
            _accounts.add(SMSProvider.WHATSAPP.name)
        else:
            pass


@ns.route('/telegram')
@respond_with_code
class TelegramMessageCallbackResource(Resource):

    class Status(Enum):
        SENT = 'sent'
    
    @staticmethod
    def _validate_signature() -> bool:
        ts = request.headers['X-Request-Timestamp']
        sig = request.headers['X-Request-Signature']

        body = request.get_json(silent=True)
        body = json.dumps(body, separators=(',', ':'))
        body = str(ts) + '\n' + body
        api_key = config['TELEGRAM_GATEWAY_API']['api_key']
        secret_key = hashlib.sha256(api_key.encode()).digest()
        expected = hmac.new(secret_key, body.encode(), hashlib.sha256).hexdigest()
        return expected == sig

    @classmethod
    def post(cls):
        """
        Telegram: 接收第三方回调结果
        """
        ts = request.headers['X-Request-Timestamp']
        current_ts = current_timestamp(to_int=True)
        if current_ts - int(ts) > 3600:
            return
        if not cls._validate_signature():
            raise InvalidArgument(message="invalid signature")

        payload = request.get_json(silent=True)
        if not payload:
            return
        delivery_status = payload.get('delivery_status')
        if not delivery_status:
            return
        if not (status:= getattr(cls.Status, delivery_status['status'].upper(), None)):
            return
        if status != cls.Status.SENT:
            return
        to_number = payload['phone_number']
        to_number = to_number.lstrip("+")
        
        cache = SmsUserCache(to_number)
        if user_id:= cache.read():
            user_id = int(user_id)
        else:
            return
        UserPreferences(user_id).third_party_message_accounts.add(SMSProvider.TELEGRAM.name)