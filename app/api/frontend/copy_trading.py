# -*- coding: utf-8 -*-
import json
from decimal import Decimal, ROUND_DOWN
from datetime import timed<PERSON><PERSON>, date
from collections import defaultdict

from flask import g, current_app
from flask_babel import gettext
from marshmallow import fields as mm_fields
from sqlalchemy import func, case

from app.common import PrecisionEnum, PerpetualMarketType, TradeType, Language
from app.models import db, User
from app.models.activity import Coupon, UserCoupon
from app.models.copy_trading import (
    TimeRangeEnum, CopyTraderUser, UserFavoriteCopyTrader, CopyTraderProfitShareDetail,
    CopyTraderHistory, CopyTransferHistory, CopyFollowerHistory, CopyTraderStatistics,
    DailyCopyTraderStatistics, DailyCopyTraderMarketStatistics, CopyFollowerStatistics,
)
from app.exceptions import (
    InvalidArgument, TransferNotAllowed, TwoFactorAuthenticationRequired, TransferOutNotAllowed,
    InsufficientBalance, FollowInactiveCopyTraderError, FollowFullCopyTraderError, PerpetualResponseCode,
    CopyTradingForbiddenError, CopyTraderNotExists, CopyTraderCopyAmountChanged,
    UsingCouponLimit, UsingCouponExpired, UsingCouponInvalid,
)
from app.api.common import Namespace, Resource, require_login, respond_with_code, require_ip_not_only_withdrawal
from app.api.common.decorators import trade_permission_validate, lock_request
from app.api.common.request import RequestPlatform, get_request_user
from app.api.common.fields import (
    LimitField, PageField, PositiveDecimalField, TimestampField, EnumField, BoolField,
)
from app.business import (
    CacheLock, LockKeys, cached, ServerClient, PerpetualServerClient, PriceManager,
    SiteSettings, UserSettings,
)
from app.business.user import require_user_not_only_withdrawal, require_user_kyc, UserRepository
from app.business.account import AccountTransferLogHelper
from app.business.copy_trading.base import CopyTradingSettings, CopyRelationQuerier
from app.business.copy_trading.trader import CopyTraderApplyManager, CopyTraderManager, SUPPORT_LEVERAGES
from app.business.copy_trading.transfer import CopyTransferHelper
from app.business.copy_trading.follower import CopyFollowerManager, TraderFavoriteHelper, retry_or_stop_zero_fund_follow_his
from app.business.copy_trading.message import send_trader_add_margin_notice
from app.business.copy_trading.position import query_users_time_range_finish_positions
from app.business.copy_trading.statistics import CopyStatistician
from app.business.sub_account import update_sub_account_balance_cache_task
from app.business.coupon.user_coupon import CopyTradingExperienceFeeService
from app.caches import PerpetualMarketCache, PerpetualCoinTypeCache
from app.caches.activity import CouponPopupReadCache
from app.utils import offset_to_page, quantize_amount, amount_to_str
from app.utils.text import max_length_validator
from app.utils.date_ import date_to_datetime, today
from app.schedules.copy_trading import finish_copy_trading_follower_status


ns = Namespace("Copy-Trading")
url_prefix = '/copy-trading'

"""
/public/    公开接口
/trader/    带单人视角相关的接口
/follower/  跟单人视角相关的接口
"""


NICKNAME_SEARCH_LIMIT = 5000  # 昵称搜索最多结果个数
MAX_SERIES_DAYS = 300  # 曲线最多返回300天的数
TIME_RANGE_DAY_MAP = {
    TimeRangeEnum.DAY7: 7,
    TimeRangeEnum.DAY30: 30,
    TimeRangeEnum.DAY90: 90,
    TimeRangeEnum.ALL: 300,
}


class CopyApiMixin:

    @classmethod
    def get_account_info_mapper(cls, user_ids: list[int], has_key=False):
        account_info_mapper = UserRepository.get_user_account_info_mapper(user_ids)
        data = {}
        for k, v in account_info_mapper.items():
            base_dict = dict(
                nickname=v['name'],
                account_name=v['account_name'],
                avatar=v['avatar'],
            )
            if has_key:
                base_dict['avatar_key'] = v['avatar_key']
            data[k] = base_dict
        return data

    @classmethod
    def get_trader_info_map(cls, trader_user_ids: list[int]) -> dict[int, dict]:
        trader_info_map = {}
        if trader_user_ids:
            trader_rows = CopyTraderUser.query.filter(
                CopyTraderUser.user_id.in_(trader_user_ids),
            ).with_entities(
                CopyTraderUser.user_id,
                CopyTraderUser.trader_id,
            ).all()
            account_info_mapper = cls.get_account_info_mapper(trader_user_ids)
            for r in trader_rows:
                trader_info_map[r.user_id] = {
                    "user_id": r.user_id,
                    "trader_id": r.trader_id,
                    **account_info_mapper[r.user_id]
                }
        return trader_info_map

    @classmethod
    def get_traders_trade_days(cls, trader_user_ids: list[int]) -> dict[int, dict]:
        statics_rows = CopyTraderStatistics.query.filter(
            CopyTraderStatistics.user_id.in_(trader_user_ids),
            CopyTraderStatistics.time_range == TimeRangeEnum.ALL,
        ).with_entities(
            CopyTraderStatistics.user_id,
            CopyTraderStatistics.trade_days,
        ).all()
        return dict(statics_rows)

    @classmethod
    def get_traders_trade_info(
        cls,
        trader_user_ids: list[int],
        time_range: TimeRangeEnum,
        include_summary: bool = False,
        summary_time_range: TimeRangeEnum = TimeRangeEnum.ALL,
    ) -> dict[int, dict]:
        """ 带单人列表的相关统计信息 """
        if not trader_user_ids:
            return {}
        if time_range not in TIME_RANGE_DAY_MAP:
            raise InvalidArgument

        days = TIME_RANGE_DAY_MAP[time_range]
        start_dt = today() - timedelta(days=days + 1)

        if include_summary:
            statics_rows = CopyTraderStatistics.query.filter(
                CopyTraderStatistics.user_id.in_(trader_user_ids),
                CopyTraderStatistics.time_range == summary_time_range,
            ).with_entities(
                CopyTraderStatistics.user_id,
                CopyTraderStatistics.aum,
                CopyTraderStatistics.mdd,
                CopyTraderStatistics.trade_days,
                CopyTraderStatistics.profit_rate,
                CopyTraderStatistics.winning_rate,
                CopyTraderStatistics.profit_amount,
                CopyTraderStatistics.total_profit_amount,
            ).all()
            statics_map = {i.user_id: i for i in statics_rows}
        else:
            statics_map = {}

        daily_statics_rows = DailyCopyTraderStatistics.query.filter(
            DailyCopyTraderStatistics.user_id.in_(trader_user_ids),
            DailyCopyTraderStatistics.date >= start_dt,
        ).with_entities(
            DailyCopyTraderStatistics.user_id,
            DailyCopyTraderStatistics.date,
            DailyCopyTraderStatistics.profit_rate,
        ).all()
        daily_statics_map = defaultdict(list)
        for r in daily_statics_rows:
            daily_statics_map[r.user_id].append(r)

        trade_info_map = {}
        for trader_id in trader_user_ids:
            daily_statics: list[DailyCopyTraderStatistics] = daily_statics_map[trader_id]
            daily_statics.sort(key=lambda x: x.date)
            info = {
                "profit_rate_series": [[i.date, i.profit_rate] for i in daily_statics],
            }
            if include_summary:
                statics: CopyTraderStatistics = statics_map.get(trader_id)
                info.update({
                    "aum": statics.aum if statics else Decimal(0),
                    "mdd": statics.mdd if statics else Decimal(0),
                    "winning_rate": statics.winning_rate if statics else Decimal(0),
                    "trade_days": statics.trade_days if statics else 0,
                    "profit_rate": statics.profit_rate if statics else Decimal(0),
                    "profit_amount": statics.profit_amount if statics else Decimal(0),
                    "total_profit_amount": statics.total_profit_amount if statics else Decimal(0),
                })
            trade_info_map[trader_id] = info
        return trade_info_map

    @classmethod
    def get_trader_trade_detail_info(
        cls,
        trader_user_id: int,
        time_range: TimeRangeEnum = TimeRangeEnum.DAY30.DAY30,
    ) -> dict:
        """ 带单人详情的相关统计信息 """
        statics = CopyTraderStatistics.query.filter(
            CopyTraderStatistics.user_id == trader_user_id,
            CopyTraderStatistics.time_range == TimeRangeEnum.ALL,
        ).with_entities(
            CopyTraderStatistics.trade_days,
            CopyTraderStatistics.aum,
            CopyTraderStatistics.mdd,
            CopyTraderStatistics.last_trade_at,
            CopyTraderStatistics.profit_rate,
            CopyTraderStatistics.profit_amount,
        ).first()
        days = TIME_RANGE_DAY_MAP[time_range]
        start_dt = today() - timedelta(days=days + 1)
        daily_statics = DailyCopyTraderStatistics.query.filter(
            DailyCopyTraderStatistics.user_id == trader_user_id,
            DailyCopyTraderStatistics.date >= start_dt,
        ).with_entities(
            DailyCopyTraderStatistics.date,
            DailyCopyTraderStatistics.profit_rate,
        ).all()
        profit_rate_series = [[i.date, i.profit_rate] for i in daily_statics]
        return {
            "trade_days": statics.trade_days if statics else 0,
            "aum": statics.aum if statics else Decimal(0),
            "mdd": statics.mdd if statics else Decimal(0),
            "profit_rate": statics.profit_rate if statics else Decimal(0),
            "profit_amount": statics.profit_amount if statics else Decimal(0),
            "last_trade_at": int(statics.last_trade_at.timestamp()) if statics and statics.last_trade_at else None,
            "profit_rate_series": profit_rate_series,
        }

    @classmethod
    def get_user_favorite_trader_ids(cls, user_id: int, trader_ids: list[int]) -> set[int]:
        """ 获取用户关注的带单人ids """
        if not trader_ids:
            return set()
        fav_rows = UserFavoriteCopyTrader.query.filter(
            UserFavoriteCopyTrader.user_id == user_id,
            UserFavoriteCopyTrader.copy_trader_user_id.in_(trader_ids),
            UserFavoriteCopyTrader.status == UserFavoriteCopyTrader.Status.VALID,
        ).with_entities(
            UserFavoriteCopyTrader.copy_trader_user_id,
        ).all()
        return {i.copy_trader_user_id for i in fav_rows}

    @classmethod
    def get_user_following_trader_ids(cls, user_id: int, trader_ids: list[int] = None) -> set[int]:
        """ 获取用户在跟单中的带单人ids """
        model = CopyFollowerHistory
        q = model.query.filter(
            model.user_id == user_id,
            model.status.in_([model.Status.FOLLOWING, model.Status.ENDING]),
        )
        if trader_ids:
            q = q.filter(model.copy_trader_user_id.in_(trader_ids))
        follow_rows = q.with_entities(
            model.copy_trader_user_id,
        ).all()
        return {i.copy_trader_user_id for i in follow_rows}

    @classmethod
    def get_users_pending_position_profits(
        cls,
        user_ids: list[int],
        unit_asset: str,
    ) -> tuple[dict[int, Decimal], dict[int, Decimal]]:
        """ 获取用户当前仓位的盈亏数 """
        if not user_ids:
            return {}, {}
        markets = CopyTradingSettings.markets
        if not markets:
            return {}, {}
        p_market_data = PerpetualMarketCache().hmget_with_keys(markets)
        market_asset_map = {}
        for p_market, _info in p_market_data:
            _info = json.loads(_info)
            if _info['type'] == PerpetualMarketType.DIRECT:
                market_asset_map[p_market] = _info['money']
            else:
                market_asset_map[p_market] = _info['stock']

        client = PerpetualServerClient()
        user_profit_real_amount_map = defaultdict(lambda: defaultdict(Decimal))
        user_profit_unreal_amount_map = defaultdict(lambda: defaultdict(Decimal))
        assets_ = set()
        for user_id in user_ids:
            pos_list = client.position_pending(user_id)
            for p in pos_list:
                asset_ = market_asset_map[p["market"]]
                user_profit_real_amount_map[user_id][asset_] += Decimal(p["profit_real"])
                user_profit_unreal_amount_map[user_id][asset_] += Decimal(p["profit_unreal"])
                assets_.add(asset_)

        asset_rates = PriceManager.assets_to_usd(assets_)
        asset_rates["USD"] = Decimal("1")
        user_profit_real_map = defaultdict(Decimal)
        for user_id, unreal_amounts in user_profit_real_amount_map.items():
            for _asset, _amount in unreal_amounts.items():
                if _asset != unit_asset:
                    unit_amount = _amount * asset_rates[_asset] / asset_rates[unit_asset]
                else:
                    unit_amount = _amount
                user_profit_real_map[user_id] += quantize_amount(unit_amount, 8)

        user_profit_unreal_map = defaultdict(Decimal)
        for user_id, unreal_amounts in user_profit_unreal_amount_map.items():
            for _asset, _amount in unreal_amounts.items():
                if _asset != unit_asset:
                    unit_amount = _amount * asset_rates[_asset] / asset_rates[unit_asset]
                else:
                    unit_amount = _amount
                user_profit_unreal_map[user_id] += quantize_amount(unit_amount, 8)
        return user_profit_unreal_map, user_profit_real_map


class CopyCouponMixin:
    @classmethod
    def get_and_check_ct_experience_fee_coupon(
        cls,
        user_id: int,
        sub_user_id: int,
        user_coupon_id: int,
    ) -> tuple[UserCoupon, Coupon]:
        """ 检查待使用的合约跟单体验金 """
        user_coupon: UserCoupon = UserCoupon.query.filter(
            UserCoupon.id == user_coupon_id,
            UserCoupon.user_id == user_id,
        ).first()
        if not user_coupon:
            raise InvalidArgument
        if user_coupon.status == UserCoupon.Status.EXPIRED:
            raise UsingCouponExpired
        if user_coupon.status == UserCoupon.Status.INVALID:
            raise UsingCouponInvalid
        if user_coupon.status != UserCoupon.Status.CREATED:
            raise InvalidArgument(message=gettext("卡券已经被激活使用，不可重复激活使用。"))
        coupon: Coupon = Coupon.query.filter(Coupon.id == user_coupon.coupon_id).first()
        if not coupon:
            raise InvalidArgument
        if coupon.coupon_type != Coupon.CouponType.COPY_TRADING_EXPERIENCE_FEE:
            raise InvalidArgument
        if sub_user_id and CopyTradingExperienceFeeService.check_sub_using_coupon(user_id, sub_user_id):
            raise UsingCouponLimit
        return user_coupon, coupon

    @classmethod
    def follower_use_ct_experience_fee_coupon(
        cls,
        user_coupon: UserCoupon,
        coupon: Coupon,
        follow_his: CopyFollowerHistory,
    ):
        """ 跟单人使用的合约跟单体验金 """
        if not SiteSettings.perpetual_transfers_enabled:
            raise TransferNotAllowed
        with CacheLock(LockKeys.copy_follower_sub(follow_his.sub_user_id)):
            db.session.rollback()
            if follow_his.status != CopyFollowerHistory.Status.FOLLOWING:
                raise InvalidArgument

            params = {"sub_user_id": follow_his.sub_user_id, "history_id": follow_his.id}
            CopyTradingExperienceFeeService.use(user_coupon=user_coupon, coupon=coupon, auto_commit=True, **params)
            follow_his.fund_amount += user_coupon.coupon_value
            db.session.commit()
        CouponPopupReadCache(user_coupon.pool_id).add_user(user_coupon.user_id)

    @classmethod
    def trader_use_ct_experience_fee_coupon(
        cls,
        user_coupon: UserCoupon,
        coupon: Coupon,
        trade_his: CopyTraderHistory,
    ):
        """ 带单人使用的合约跟单体验金 """
        if trade_his.status != CopyTraderHistory.Status.RUNNING:
            raise InvalidArgument
        if not SiteSettings.perpetual_transfers_enabled:
            raise TransferNotAllowed

        params = {"sub_user_id": trade_his.sub_user_id, "history_id": trade_his.id}
        CopyTradingExperienceFeeService.use(user_coupon=user_coupon, coupon=coupon, auto_commit=True, **params)
        CouponPopupReadCache(user_coupon.pool_id).add_user(user_coupon.user_id)


@ns.route("/markets")
@respond_with_code
class CopyTradingMarketsResource(Resource):

    @classmethod
    def get(cls):
        """ 支持跟单的市场列表 """
        return list(CopyTradingSettings.markets)


@ns.route("/configs")
@respond_with_code
class CopyTradingConfigsResource(Resource):

    @classmethod
    def get(cls):
        """ 跟单修改的配置 """
        return {
            "min_trade_amount": CopyTradingSettings.min_trade_amount,
            "max_trade_amount": CopyTradingSettings.max_trade_amount,
            "min_copy_amount": CopyTradingSettings.min_copy_amount,
            "max_copy_amount": CopyTradingSettings.max_copy_amount,
            "min_profit_share_rate": CopyTradingSettings.min_profit_share_rate,
            "max_profit_share_rate": CopyTradingSettings.max_profit_share_rate,
            "support_leverages": SUPPORT_LEVERAGES,
        }


@ns.route("/public/traders")
@respond_with_code
class PublicTradersResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            time_range=EnumField(TimeRangeEnum, missing=TimeRangeEnum.DAY30),
            data_type=EnumField(["profit_rate", "profit_amount", "aum", "follower_num", "mdd", "winning_rate"],
                                missing="profit_amount"),
            hide_full=BoolField,
            nickname=mm_fields.String,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 所有交易员 """
        lang = Language(g.lang)
        langs = [lang] if lang == Language.EN_US else [lang, Language.EN_US]

        model = CopyTraderUser
        time_range = kwargs["time_range"]
        data_type = kwargs["data_type"]
        page, limit = kwargs["page"], kwargs["limit"]

        hide_full = kwargs.get("hide_full")
        user = get_request_user(allow_none=True)
        is_active_trader = False
        if user:
            _cur_trader = CopyTraderManager.get_trader(user.id)
            is_active_trader = _cur_trader and _cur_trader.is_active
        following_trader_ids = None

        q = model.query.join(
            CopyTraderStatistics, model.user_id == CopyTraderStatistics.user_id,
        ).filter(
            model.status == model.Status.ACTIVE,
            CopyTraderStatistics.time_range == time_range,
        )
        if nickname := kwargs.get("nickname"):
            # fixme CopyTraderUser目前不到1000条记录
            q = q.filter(model.nickname.contains(nickname))
        else:
            # 搜索功能不受语区限制，可以搜到所有语区的交易员
            # 不搜索时，不展示不活跃的交易员
            q = q.filter(model.language.in_(langs))
            q = q.filter(model.display_priority > model.INACTIVE_DISPLAY_PRIORITY)
        if hide_full:
            q = q.filter(model.cur_follower_num < model.max_follower_num)
            if user and not is_active_trader:
                following_trader_ids = CopyApiMixin.get_user_following_trader_ids(user.id)
                q = q.filter(model.user_id.not_in(following_trader_ids))

        # 交易笔数=0的交易员，于所有筛选条件下，都是排序在有数据的交易员后面(需求id 86eq7wcax)
        sort_conditions = [case(
            (CopyTraderStatistics.profit_amount == 0 and CopyTraderStatistics.trade_count == 0, 1),
            else_=0,
        )]

        if data_type == "follower_num":
            sort_conditions.append(model.cur_follower_num.desc())
        elif data_type == "profit_rate":
            sort_conditions.append(CopyTraderStatistics.profit_rate.desc())
        elif data_type == "profit_amount":
            sort_conditions.append(CopyTraderStatistics.profit_amount.desc())
        elif data_type == "aum":
            sort_conditions.append(CopyTraderStatistics.aum.desc())
        elif data_type == "mdd":
            sort_conditions.append(CopyTraderStatistics.mdd.asc())
        elif data_type == "winning_rate":
            sort_conditions.append(CopyTraderStatistics.winning_rate.desc())

        q = q.order_by(*sort_conditions)

        pagination = q.with_entities(
            CopyTraderUser.user_id,
            CopyTraderUser.trader_id,
            CopyTraderUser.cur_follower_num,
            CopyTraderUser.max_follower_num,
            CopyTraderUser.profit_share_rate,
            CopyTraderUser.status,
            CopyTraderUser.last_started_at,
            CopyTraderUser.last_finished_at,
            CopyTraderStatistics.profit_rate,
            CopyTraderStatistics.trade_count,
            CopyTraderStatistics.profit_amount,
            CopyTraderStatistics.total_profit_amount,
            CopyTraderStatistics.aum,
            CopyTraderStatistics.mdd,
            CopyTraderStatistics.trade_days,
            CopyTraderStatistics.winning_rate,
        ).paginate(page, limit, error_out=False)

        rows: list[model] = pagination.items
        trader_user_ids = [i.user_id for i in rows]
        trade_info_map = CopyApiMixin.get_traders_trade_info(trader_user_ids, time_range)

        if time_range != TimeRangeEnum.ALL:
            traders_trade_days = CopyApiMixin.get_traders_trade_days(trader_user_ids)
        else:
            traders_trade_days = {}  # 上面已经查出来了

        if user and trader_user_ids:
            favorite_trader_ids = CopyApiMixin.get_user_favorite_trader_ids(user.id, trader_user_ids)
            if following_trader_ids is None and not is_active_trader:
                following_trader_ids = CopyApiMixin.get_user_following_trader_ids(user.id)
        else:
            favorite_trader_ids = following_trader_ids = set()
        if following_trader_ids is None:
            following_trader_ids = set()

        items = []
        account_info_mapper = CopyApiMixin.get_account_info_mapper(trader_user_ids)
        for r in rows:
            trader_uid = r.user_id
            trade_info = trade_info_map[trader_uid]
            profit_rate_series = trade_info.pop("profit_rate_series", [])
            profit_rate_series = [i for i in profit_rate_series if i[0] >= r.last_started_at.date()]
            trade_days = traders_trade_days.get(trader_uid, r.trade_days)
            items.append(
                dict(
                    trader_id=r.trader_id,
                    **account_info_mapper[trader_uid],
                    status=r.status.name,
                    cur_follower_num=r.cur_follower_num,
                    winning_rate=r.winning_rate,
                    max_follower_num=r.max_follower_num,
                    profit_share_rate=r.profit_share_rate,
                    is_favorite=trader_uid in favorite_trader_ids,
                    is_follow=trader_uid in following_trader_ids,
                    profit_rate=r.profit_rate,
                    trade_count=r.trade_count,
                    profit_amount=r.profit_amount,
                    total_profit_amount=r.total_profit_amount,
                    aum=r.aum,
                    mdd=r.mdd,
                    trade_days=trade_days,
                    profit_rate_series=profit_rate_series,
                    **trade_info,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/public/trader-detail")
@respond_with_code
class PublicTraderDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 带单人详情 """
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists

        trader_user_id = trader.user_id
        user = get_request_user(allow_none=True)
        if user:
            favorite_trader_ids = CopyApiMixin.get_user_favorite_trader_ids(user.id, [trader_user_id])
            following_trader_ids = CopyApiMixin.get_user_following_trader_ids(user.id, [trader_user_id])
        else:
            favorite_trader_ids = following_trader_ids = set()

        trader_trade_info = CopyApiMixin.get_trader_trade_detail_info(trader.user_id)
        profit_rate_series = trader_trade_info.pop("profit_rate_series", [])
        profit_rate_series = [i for i in profit_rate_series if i[0] >= trader.last_started_at.date()]
        account_info = CopyApiMixin.get_account_info_mapper([trader_user_id], has_key=True)[trader_user_id]
        detail_info = dict(
            trader_id=trader_id,
            nickname=account_info['nickname'],
            account_name=account_info['account_name'],
            avatar=account_info['avatar_key'],
            status=trader.status.name,
            avatar_url=account_info['avatar'],
            introduction=trader.introduction,
            min_copy_amount=trader.min_copy_amount,
            max_copy_amount=trader.max_copy_amount,
            cur_follower_num=trader.cur_follower_num,
            max_follower_num=trader.max_follower_num,
            profit_share_rate=trader.profit_share_rate,
            last_finished_at=int(trader.last_finished_at.timestamp()) if trader.last_finished_at else None,
            is_favorite=trader_user_id in favorite_trader_ids,
            is_follow=trader_user_id in following_trader_ids,
            profit_rate_series=profit_rate_series,
            **trader_trade_info,
        )
        return detail_info


@ns.route("/public/followers")
@respond_with_code
class PublicTraderFollowersResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 带单人-跟单人列表（当前跟单中的） """
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists

        page, limit = kwargs["page"], kwargs["limit"]
        trade_his = CopyTraderManager.try_get_last_active_trade_his(trader.user_id)
        if not trade_his:
            return dict(
                has_next=False,
                curr_page=1,
                count=0,
                data=[],
                total=0,
                total_page=1,
            )

        model = CopyFollowerHistory
        q = model.query.filter(
            model.trader_history_id == trade_his.id,
            model.status != model.Status.FINISHED,
        ).with_entities(
            model.user_id,
            model.fund_amount,
            model.total_profit_amount,
        )
        pagination = q.order_by(model.id.desc()).paginate(page, limit, error_out=False)

        rows: list[model] = pagination.items
        follower_ids = [i.user_id for i in rows]
        follower_users = User.query.filter(User.id.in_(follower_ids)).all()
        follower_user_map = {i.id: i for i in follower_users}

        items = []
        for r in rows:
            follower_user: User = follower_user_map[r.user_id]
            d = dict(
                follower_name=follower_user.hidden_complete_name,
                fund_amount=r.fund_amount,
                total_copy_earn_amount=r.total_profit_amount,
            )
            items.append(d)

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/public/current-position")
@respond_with_code
class PublicTraderCurrentPositionResource(Resource):

    @classmethod
    def format_cur_positions(cls, pos_list: list[dict]) -> list[dict]:
        res = []
        for pos in pos_list:
            d = {
                'position_id': pos['position_id'],
                'create_time': int(pos['create_time']),
                'market': pos['market'],
                'type': pos['type'],
                'leverage': pos['leverage'],
                'side': pos['side'],
                'amount': pos['amount'],
                'open_price': pos['open_price'],
                'liq_price': pos['liq_price'],
                'profit_unreal': pos['profit_unreal'],
                'profit_real': pos['profit_real'],
                'margin_amount': pos['margin_amount'],
            }
            res.append(d)
        return res

    @classmethod
    def format_fin_positions(cls, pos_list: list[dict]) -> list[dict]:
        res = []
        for pos in pos_list:
            d = {
                'position_id': pos['position_id'],
                'create_time': int(pos['create_time']),
                'update_time': int(pos['update_time']),
                'market': pos['market'],
                'type': pos['type'],
                'side': pos['side'],
                'leverage': pos['leverage'],
                'amount_max': pos['amount_max'],
                'amount_max_margin': pos['amount_max_margin'],
                'open_val_max': pos['open_val_max'],
                'profit_real': pos['profit_real'],
                'open_price': pos['open_price'],
                'first_price': pos['first_price'],
                'latest_price': pos['latest_price'],
            }
            res.append(d)
        return res

    @classmethod
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
            market=mm_fields.String(missing=None),
        )
    )
    def get(cls, **kwargs):
        """ 带单人-当前带单的仓位 """
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists
        trader_user_id = trader.user_id
        trade_his = CopyTraderManager.try_get_last_active_trade_his(trader_user_id)
        if not trade_his:
            return []

        run_sub_id = trade_his.sub_user_id
        market = kwargs['market']
        if market and market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument
        client = PerpetualServerClient()
        pos_list = client.position_pending(run_sub_id, kwargs['market'])
        res = cls.format_cur_positions(pos_list)
        res.sort(key=lambda x: x['create_time'], reverse=True)
        return res


@ns.route("/public/finished-position")
@respond_with_code
class PublicTraderFinishedPositionResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
            market=mm_fields.String(missing=''),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 带单人-历史带单的仓位 """
        page = kwargs['page']
        limit = kwargs['limit']
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists
        trader_user_id = trader.user_id
        trade_his = CopyTraderManager.get_last_trade_his(trader_user_id)
        if not trade_his:
            _d = {"page": page, "limit": limit, "records": []}
            return offset_to_page(_d)

        market = kwargs['market']
        if market and market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument

        run_sub_id = trade_his.sub_user_id
        client = PerpetualServerClient()
        result = client.position_finished(run_sub_id, market, page=page, limit=limit)
        result['records'] = PublicTraderCurrentPositionResource.format_fin_positions(result['records'])
        return offset_to_page(result)


@ns.route("/public/trade-data")
@respond_with_code
class PublicTraderTradeDataResource(Resource):

    @classmethod
    @cached(300)
    def get_realtime_st(cls, user_id: int) -> dict:
        fav_count = UserFavoriteCopyTrader.query.filter(
            UserFavoriteCopyTrader.copy_trader_user_id == user_id,
            UserFavoriteCopyTrader.status == UserFavoriteCopyTrader.Status.VALID,
        ).with_entities(func.count()).scalar() or 0
        total_follower_count = CopyFollowerHistory.query.filter(
            CopyFollowerHistory.copy_trader_user_id == user_id,
        ).with_entities(func.count(func.distinct(CopyFollowerHistory.user_id))).scalar() or 0
        return {"favorite_count": fav_count, "total_follower_count": total_follower_count}

    @classmethod
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 带单人-统计数据-数据总览 """
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists

        trader_user_id = trader.user_id
        st: CopyTraderStatistics = CopyTraderStatistics.query.filter(
            CopyTraderStatistics.user_id == trader_user_id,
            CopyTraderStatistics.time_range == TimeRangeEnum.ALL,
        ).first()
        real_st = cls.get_realtime_st(trader_user_id)
        return dict(
            last_trade_at=int(st.last_trade_at.timestamp()) if st and st.last_trade_at else None,
            trade_days=st.trade_days if st else 0,
            trade_count=st.trade_count if st else 0,
            margin_amount=st.margin_amount if st else Decimal(0),
            equity=st.equity if st else Decimal(0),
            profit_amount=st.profit_amount if st else Decimal(0),
            profit_rate=st.profit_rate if st else Decimal(0),
            winning_rate=st.winning_rate if st else Decimal(0),
            follower_profit_amount=st.follower_profit_amount if st else Decimal(0),
            total_profit_amount=st.total_profit_amount if st else Decimal(0),
            profit_share_amount=st.profit_share_amount if st else Decimal(0),
            mdd=st.mdd if st else Decimal(0),
            profit_count=st.profit_count if st else 0,
            loss_count=st.loss_count if st else 0,
            total_follower_count=real_st["total_follower_count"],
            favorite_count=real_st["favorite_count"],
            max_follower_num=trader.max_follower_num,
            cur_follower_num=trader.cur_follower_num,
        )


@ns.route("/public/profit-series")
@respond_with_code
class PublicTraderProfitSeriesResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
            time_range=EnumField(TimeRangeEnum, missing=TimeRangeEnum.DAY30),
        )
    )
    def get(cls, **kwargs):
        """ 带单人-统计数据-盈亏数据曲线 """
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists

        days = TIME_RANGE_DAY_MAP[kwargs['time_range']]
        start_dt = today() - timedelta(days + 1)
        start_dt = max(start_dt, trader.last_started_at.date())

        rows: list[DailyCopyTraderStatistics] = DailyCopyTraderStatistics.query.filter(
            DailyCopyTraderStatistics.user_id == trader.user_id,
            DailyCopyTraderStatistics.date >= start_dt,
        ).with_entities(
            DailyCopyTraderStatistics.date,
            DailyCopyTraderStatistics.profit_amount,
            DailyCopyTraderStatistics.delta_pending_profit,
            DailyCopyTraderStatistics.follower_profit_amount,
            DailyCopyTraderStatistics.delta_follower_pending_profit,
            DailyCopyTraderStatistics.total_profit_amount,
        ).all()
        res = [
            [
                date_to_datetime(r.date),
                r.profit_amount + r.delta_pending_profit,
                r.follower_profit_amount + r.delta_follower_pending_profit,
                r.total_profit_amount + r.delta_pending_profit + r.delta_follower_pending_profit,
            ]
            for r in rows
        ]
        res.sort(key=lambda x: x[0])
        return res


@ns.route("/public/aum-series")
@respond_with_code
class PublicTraderAumSeriesResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
            time_range=EnumField(TimeRangeEnum, missing=TimeRangeEnum.DAY30),
        )
    )
    def get(cls, **kwargs):
        """ 带单人-统计数据-资产规模曲线 """
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists

        days = TIME_RANGE_DAY_MAP[kwargs['time_range']]
        start_dt = today() - timedelta(days + 1)
        start_dt = max(start_dt, trader.last_started_at.date())

        rows: list[DailyCopyTraderStatistics] = DailyCopyTraderStatistics.query.filter(
            DailyCopyTraderStatistics.user_id == trader.user_id,
            DailyCopyTraderStatistics.date >= start_dt,
        ).with_entities(
            DailyCopyTraderStatistics.date,
            DailyCopyTraderStatistics.aum,
        ).all()
        res = [[date_to_datetime(r.date), r.aum] for r in rows]
        res.sort(key=lambda x: x[0])
        return res


@ns.route("/public/market-percent")
@respond_with_code
class PublicTraderMarketPercentResource(Resource):
    @classmethod
    @cached(300)
    def get_(cls, user_id: int, start_dt: date) -> list:
        model = DailyCopyTraderMarketStatistics
        rows: list[model] = model.query.filter(
            model.user_id == user_id,
            model.date >= start_dt,
        ).group_by(
            model.market,
        ).with_entities(
            model.market,
            func.sum(model.trade_count).label('trade_count'),
            func.sum(model.profit_amount).label('profit_amount'),
        ).all()
        return [
            {
                "market": r.market,
                "trade_count": r.trade_count,
                "profit_amount": r.profit_amount
            } for r in rows
        ]

    @classmethod
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
            time_range=EnumField(TimeRangeEnum, missing=TimeRangeEnum.DAY30),
        )
    )
    def get(cls, **kwargs):
        """ 带单人-统计数据-合约交易偏好 """
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists

        trader_user_id = trader.user_id
        days = TIME_RANGE_DAY_MAP[kwargs['time_range']]
        start_dt = today() - timedelta(days + 1)
        start_dt = max(start_dt, trader.last_started_at.date())
        return cls.get_(trader_user_id, start_dt)


@ns.route("/user-info")
@respond_with_code
class CopyUserInfoResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 合约跟单交易的用户信息 """
        user = g.user
        user_id = user.id
        trader_status = trader_id = trader_sub_user_id = None
        trader = CopyTraderManager.get_trader(user_id)
        if trader:
            trader_status = trader.status.name
            trader_id = trader.trader_id
            trade_his = CopyRelationQuerier.get_last_trade_his(user_id)
            if trade_his.status == CopyTraderHistory.Status.ENDING:
                trader_status = trade_his.status.name
            trader_sub_user_id = trade_his.sub_user_id
        if CopyFollowerManager.has_active_follow_his(user_id):
            is_active_follower = True
        else:
            is_active_follower = False
        return {
            "trader_id": trader_id,
            "trader_status": trader_status,
            "trader_sub_user_id": trader_sub_user_id,
            "is_active_follower": is_active_follower,
        }


@ns.route("/trader/application")
@respond_with_code
class TraderApplicationResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 获取带单人申请信息 """
        user = g.user
        apl_manager = CopyTraderApplyManager(user)
        apl = apl_manager.get_last_application(user.id)

        old_apply_info = {}
        if apl:
            old_apply_info = {
                "nickname": apl.nickname,
                "account_name": apl.account_name or user.extra.display_account_name,
                "avatar": CopyTraderUser.get_avatar_url(apl.avatar),
                "introduction": apl.introduction,
                "contact": apl.contact,
            }
            social_data = json.loads(apl.social_data) if apl.social_data else {}
            for sf in apl_manager.SOCIAL_KEYS:
                old_apply_info[sf] = social_data.get(sf, "")

        conditions, condition_details = apl_manager.get_condition_status()
        data = {
            "has_pending_application": CopyTraderApplyManager.has_pending_application(user.id),
            "conditions": conditions,
            "condition_details": condition_details,
        }
        # APP 不兼容 空字典的数据结构
        if old_apply_info:
            data['old_application_info'] = old_apply_info
        return data

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            nickname=mm_fields.String(required=True, validate=max_length_validator(40)),
            contact=mm_fields.String(required=True, validate=max_length_validator(256)),
            account_name=mm_fields.String(validate=max_length_validator(20)),
            avatar=mm_fields.String(validate=max_length_validator(512)),
            introduction=mm_fields.String(validate=max_length_validator(300)),
            telegram=mm_fields.String(validate=max_length_validator(64)),
            facebook=mm_fields.String(validate=max_length_validator(64)),
            twitter=mm_fields.String(validate=max_length_validator(64)),
            reddit=mm_fields.String(validate=max_length_validator(64)),
            medium=mm_fields.String(validate=max_length_validator(64)),
            discord=mm_fields.String(validate=max_length_validator(64)),
        )
    )
    def post(cls, **kwargs):
        """ 提交带单人申请 """
        user = g.user
        user_id = user.id
        trader = CopyTraderManager.get_trader(user_id)
        if trader and not trader.is_inactive:
            raise InvalidArgument(message='already a trader')
        apl_manager = CopyTraderApplyManager(user)
        if apl_manager.has_pending_application(user_id):
            raise InvalidArgument(message='duplicate submission')
        apl_manager.check_inactive_interval_days(user_id)
        if CopyRelationQuerier.is_risk_user(user_id):
            raise CopyTradingForbiddenError
        if not apl_manager.is_pass_all_condition():
            raise InvalidArgument(message='conditions not met')

        with CacheLock(LockKeys.copy_trader(user_id), wait=False):
            db.session.rollback()
            if apl_manager.has_pending_application(user_id):
                raise InvalidArgument(message='duplicate submission')
            apl = apl_manager.new_application(kwargs)
            apl_manager.audit_pass_application(apl)


@ns.route("/trader/profile")
@respond_with_code
class TraderProfileResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            avatar=mm_fields.String(validate=max_length_validator(512)),
            nickname=mm_fields.String(validate=max_length_validator(40)),
            account_name=mm_fields.String(validate=max_length_validator(20)),
            introduction=mm_fields.String(validate=max_length_validator(300)),
        )
    )
    def put(cls, **kwargs):
        """ 带单人-修改个人信息 """
        user = g.user
        user_id = user.id
        trader = CopyTraderManager.get_trader(user_id)
        if not trader or not trader.is_active:
            raise InvalidArgument
        CopyTraderManager.edit_profile_info(trader, kwargs)


@ns.route("/trader/settings")
@respond_with_code
class TraderSettingsResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            profit_share_rate=PositiveDecimalField(
                places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, allow_zero=True, required=False),
            max_copy_amount=PositiveDecimalField(
                places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=False),
            min_copy_amount=PositiveDecimalField(
                places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=False),
        )
    )
    def put(cls, **kwargs):
        """ 带单人-修改带单参数设定 """
        user = g.user
        user_id = user.id
        if not kwargs:
            raise InvalidArgument
        trader = CopyTraderManager.get_trader(user_id)
        if not trader or not trader.is_active:
            raise InvalidArgument

        new_settings = {}
        if "max_copy_amount" in kwargs and "min_copy_amount" in kwargs:
            max_copy_amount = kwargs["max_copy_amount"]
            min_copy_amount = kwargs["min_copy_amount"]
            if min_copy_amount > max_copy_amount:
                raise InvalidArgument(message=gettext("请调整跟单金额后重新提交"))

            site_min_copy_amount = CopyTradingSettings.min_copy_amount
            site_max_copy_amount = CopyTradingSettings.max_copy_amount
            if not (site_min_copy_amount <= max_copy_amount <= site_max_copy_amount):
                raise InvalidArgument
            if not (site_min_copy_amount <= min_copy_amount <= site_max_copy_amount):
                raise InvalidArgument

            new_settings["max_copy_amount"] = max_copy_amount
            new_settings["min_copy_amount"] = min_copy_amount

        if "profit_share_rate" in kwargs:
            ps_rate = kwargs["profit_share_rate"]
            if not (CopyTradingSettings.min_profit_share_rate <= ps_rate <= CopyTradingSettings.max_profit_share_rate):
                raise InvalidArgument
            new_settings["profit_share_rate"] = ps_rate

        if new_settings:
            CopyTraderManager.edit_settings(trader, new_settings)


@ns.route("/trader/stop")
@respond_with_code
class TraderStopTradeResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        """ 带单人-停止带单 """
        user = g.user
        user_id = user.id
        trader = CopyTraderManager.get_trader(user_id)
        if not trader:
            raise CopyTraderNotExists
        if not trader.is_active:
            raise InvalidArgument
        with CacheLock(LockKeys.copy_trader(user_id), wait=False):
            db.session.rollback()
            trade_his = CopyTraderManager.try_get_last_active_trade_his(trader.user_id)
            if not trade_his:
                raise InvalidArgument
            if trade_his.status == CopyTraderHistory.Status.ENDING:
                raise InvalidArgument(message=gettext("系统正处理你的结束带单请求，禁止操作"))
            CopyTraderManager.stop_trade(trader)


@ns.route('/trader/transfer')
@respond_with_code
class TraderTransferResource(Resource):
    @classmethod
    def get_trader_remain_transfer_in_amount(cls, trader_user_id: int, trader_sub_id: int, asset: str) -> Decimal:
        # 剩余可转入 = min(最大带单资金 - 带单子帐号_账户盈亏权益，主帐号现货剩余可用)
        # 带单子帐号只会有USDT，所以直接用余额算账户权益：账户余额 + 保证金余额 + 未实现盈亏
        sub_balance = PerpetualServerClient().get_user_balances(user_id=trader_sub_id, asset=asset)[asset]
        equity = sub_balance['balance_total'] + sub_balance['margin'] + sub_balance['profit_unreal']
        equity = quantize_amount(equity, 8)
        remain_tran_in_amount = CopyTradingSettings.max_trade_amount - equity
        zero = Decimal()
        if remain_tran_in_amount <= zero:
            return zero

        m_balance = ServerClient().get_user_balances(user_id=trader_user_id, asset=asset)[asset]
        available = m_balance["available"]
        remain_tran_in_amount = max(min(remain_tran_in_amount, available), zero)
        return quantize_amount(remain_tran_in_amount, 8)

    @classmethod
    def get_trader_sub_can_transfer_out_amount(cls, trader_user_id: int, trader_sub_id: int, asset: str) -> Decimal:
        """ 获取带单子帐号的合约可转出数 """
        using_coupon_balance = CopyTradingExperienceFeeService.get_sub_using_coupon_balance(
            trader_user_id, trader_sub_id, asset,
        )
        balances = PerpetualServerClient().get_user_balances(trader_sub_id, asset)
        transfer_amount = quantize_amount(Decimal(balances.get(asset, {}).get("transfer", "0")), 8)
        can_transfer_out_amount = transfer_amount - using_coupon_balance
        can_transfer_out_amount = max(can_transfer_out_amount, Decimal(0))
        return can_transfer_out_amount

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            type=EnumField(["IN", "OUT"], required=True),
        )
    )
    def get(cls, **kwargs):
        """ 带单人-获取可划转数 """
        user: User = g.user
        user_id = user.id

        trader = CopyTraderManager.get_trader(user_id)
        if not trader:
            raise CopyTraderNotExists
        trade_his = CopyTraderManager.try_get_last_active_trade_his(trader.user_id)
        if not trade_his:
            return dict(transfer_amount=Decimal())

        asset = "USDT"
        transfer_type = kwargs["type"]
        if transfer_type == "IN":
            # 带单人主账号 --> 带单子帐号
            transfer_amount = cls.get_trader_remain_transfer_in_amount(user_id, trade_his.sub_user_id, asset)
        else:
            # 带单子帐号 --> 带单人主账号，获取带单子帐号的合约可转出数
            transfer_amount = cls.get_trader_sub_can_transfer_out_amount(user_id, trade_his.sub_user_id, asset)
        return dict(transfer_amount=transfer_amount)

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            type=EnumField(["IN", "OUT"], required=True),
            amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 带单人-划转资产（带单人 <--> 带单子帐号） """
        user: User = g.user
        user_id = user.id
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired

        transfer_type = kwargs["type"]
        asset = "USDT"
        amount = kwargs["amount"]
        if transfer_type == "IN":
            require_ip_not_only_withdrawal()
            require_user_not_only_withdrawal(user)
            require_user_kyc(user)
            if asset in SiteSettings.forbidden_perpetual_transfer_in_assets:
                raise TransferNotAllowed
        if not SiteSettings.perpetual_transfers_enabled:
            raise TransferNotAllowed

        if asset not in set(PerpetualCoinTypeCache().read_aside()):
            raise InvalidArgument

        trader = CopyTraderManager.get_trader(user_id)
        if not trader:
            raise CopyTraderNotExists
        trader_sub_id = CopyTraderManager.get_trader_run_sub_id(user_id)
        if not trader_sub_id:
            raise InvalidArgument

        trade_his = CopyTraderManager.try_get_last_active_trade_his(trader.user_id)
        if not trade_his:
            raise InvalidArgument
        if trade_his.status != CopyTraderHistory.Status.RUNNING:
            raise InvalidArgument
        if trade_his.sub_user_id != trader_sub_id:
            raise InvalidArgument

        if transfer_type == "IN":
            transfer_type = CopyTransferHistory.Type.TRADER_TRANSFER_IN
            transfer_amount = cls.get_trader_remain_transfer_in_amount(user_id, trader_sub_id, asset)
            if amount > transfer_amount:
                raise InvalidArgument(message=gettext("超过最大可跟单金额划入"))
        else:
            from_user_id = trader_sub_id
            transfer_type = CopyTransferHistory.Type.TRADER_TRANSFER_OUT
            transfer_amount = cls.get_trader_sub_can_transfer_out_amount(user_id, trader_sub_id, asset)
            if transfer_amount < amount:
                raise InsufficientBalance
            if not UserSettings(from_user_id).perpetual_transfer_out_enabled:
                raise TransferOutNotAllowed

        with CacheLock(LockKeys.copy_trader_transfer(user_id)):
            db.session.rollback()
            if trade_his.status != CopyTraderHistory.Status.RUNNING:
                raise InvalidArgument
            tran_row = CopyTransferHelper.transfer(
                main_user_id=user_id,
                sub_id=trader_sub_id,
                history_id=trade_his.id,
                transfer_type=transfer_type,
                asset=asset,
                amount=amount,
            )
            AccountTransferLogHelper.add_log_by_transfer(tran_row)
        if transfer_type == CopyTransferHistory.Type.TRADER_TRANSFER_IN:
            send_trader_add_margin_notice.delay(user_id, amount)
        update_sub_account_balance_cache_task.delay(user_id, [trader_sub_id])
        CopyStatistician.update_trade_his_equity(trade_his)
        return {}


@ns.route('/trader/transfer-history')
@respond_with_code
class TraderTransferHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        trade_history_id=mm_fields.Integer,
        start_date=TimestampField(to_date=True),
        end_date=TimestampField(to_date=True),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """ 带单人-划转历史 """
        user = g.user
        main_user_id = user.id
        trade_history_id = kwargs.get('trade_history_id')
        if trade_history_id:
            if not CopyTraderHistory.query.filter(
                CopyTraderHistory.id == trade_history_id,
                CopyTraderHistory.user_id == main_user_id,
            ).with_entities(CopyTraderHistory.id).first():
                raise InvalidArgument

        query = CopyTransferHistory.query.filter(
            CopyTransferHistory.main_user_id == main_user_id,
            CopyTransferHistory.type.in_(CopyTransferHistory.TRADER_TYPES),
            CopyTransferHistory.status == CopyTransferHistory.Status.FINISHED,
        ).order_by(CopyTransferHistory.id.desc())
        if trade_history_id:
            query = query.filter(CopyTransferHistory.history_id == trade_history_id)
        if start_date := kwargs.get('start_date'):
            query = query.filter(CopyTransferHistory.created_at >= start_date)
        if end_date := kwargs.get('end_date'):
            _next_end_dt = end_date + timedelta(days=1)
            query = query.filter(CopyTransferHistory.created_at < _next_end_dt)

        page, limit = kwargs["page"], kwargs["limit"]
        pagination = query.paginate(page, limit, error_out=False)

        record = pagination.items
        items = []
        for i in record:
            if i.type == CopyTransferHistory.Type.TRADER_TRANSFER_IN:
                type_ = "IN"
            elif i.type == CopyTransferHistory.Type.TRADER_TRANSFER_OUT:
                type_ = "OUT"
            elif i.type == CopyTransferHistory.Type.TRADER_COUPON_USE:
                type_ = "COUPON_USE"
            elif i.type == CopyTransferHistory.Type.TRADER_COUPON_RECYCLE:
                type_ = "COUPON_RECYCLE"
            else:
                type_ = ""
            item = {
                "created_at": int(i.created_at.timestamp()),
                "asset": i.asset,
                "amount": i.amount,
                "type": type_,
            }
            items.append(item)

        return dict(
            data=items,
            curr_page=pagination.page,
            has_next=pagination.has_next,
            count=len(items),
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route('/trader/coupon')
@respond_with_code
class TraderCouponResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            user_coupon_id=mm_fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 带单人-使用卡券 """
        user: User = g.user
        user_id = user.id

        trader = CopyTraderManager.get_trader(user_id)
        if not trader:
            raise CopyTraderNotExists
        trader_sub_id = CopyTraderManager.get_trader_run_sub_id(user_id)
        if not trader_sub_id:
            raise InvalidArgument

        trade_his = CopyTraderManager.try_get_last_active_trade_his(trader.user_id)
        if not trade_his:
            raise InvalidArgument
        if trade_his.status != CopyTraderHistory.Status.RUNNING:
            raise InvalidArgument
        if trade_his.sub_user_id != trader_sub_id:
            raise InvalidArgument

        user_coupon_id = kwargs["user_coupon_id"]
        with CacheLock(LockKeys.copy_trader(user_id), wait=False):
            db.session.rollback()
            user_coupon, coupon = CopyCouponMixin.get_and_check_ct_experience_fee_coupon(
                user_id=user_id,
                sub_user_id=trader_sub_id,
                user_coupon_id=user_coupon_id,
            )
            CopyCouponMixin.trader_use_ct_experience_fee_coupon(user_coupon, coupon, trade_his)
        return {}


@ns.route("/trader/profit-share-history")
@respond_with_code
class TraderProfitShareHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 带单人-分润记录 """
        user = g.user
        user_id = user.id
        model = CopyTraderProfitShareDetail
        q = model.query.filter(
            model.copy_trader_user_id == user_id,
            model.status == model.Status.FINISHED,
            model.amount > 0,
        )
        page, limit = kwargs["page"], kwargs["limit"]
        pagination = q.order_by(model.id.desc()).paginate(page, limit, error_out=False)

        rows: list[model] = pagination.items
        follower_ids = [i.user_id for i in rows]
        follower_users = User.query.filter(User.id.in_(follower_ids)).all()
        follower_user_map = {i.id: i for i in follower_users}

        items = []
        for r in rows:
            follower_user: User = follower_user_map[r.user_id]
            items.append(
                dict(
                    created_at=int(r.created_at.timestamp()),
                    asset=r.asset,
                    amount=r.amount,
                    follower_name=follower_user.hidden_complete_name,
                    profit_share_rate=r.profit_share_rate,
                    profit_share_type=r.profit_share_type.name,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/trader/profit-share-summary")
@respond_with_code
class TraderProfitShareSummaryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, **kwargs):
        """ 带单人-分润汇总信息 """
        trader_user_id = g.user.id
        trader = CopyTraderManager.get_trader(trader_user_id)
        if not trader:
            raise CopyTraderNotExists

        trade_his = CopyTraderManager.try_get_last_active_trade_his(trader.user_id)
        if not trade_his:
            expected_profit_share_amount = Decimal()
        else:
            model = CopyFollowerHistory
            rows = model.query.filter(
                model.status != model.Status.FINISHED,
                model.trader_history_id == trade_his.id,
            ).with_entities(
                model.expected_profit_share_amount,
            ).all()
            expected_profit_share_amount = sum([i.expected_profit_share_amount for i in rows])

        return {
            "expected_profit_share_amount": expected_profit_share_amount,
        }


@ns.route("/trader/followers")
@respond_with_code
class TraderFollowersResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 带单人-跟单人列表（带单人自身视角） """
        page, limit = kwargs["page"], kwargs["limit"]
        trader_user_id = g.user.id
        trader = CopyTraderManager.get_trader(trader_user_id)
        if not trader:
            raise CopyTraderNotExists

        trade_his = CopyTraderManager.try_get_last_active_trade_his(trader.user_id)
        if not trade_his:
            return dict(
                has_next=False,
                curr_page=1,
                data=[],
                count=0,
                total=0,
                total_page=1,
            )

        model = CopyFollowerHistory
        q = model.query.filter(
            model.status != model.Status.FINISHED,
            model.trader_history_id == trade_his.id,
        ).with_entities(
            model.user_id,
            model.fund_amount,
            model.total_profit_amount,
            model.expected_profit_share_amount,
            model.last_profit_shared_at,
        )

        pagination = q.order_by(model.id.desc()).paginate(page, limit, error_out=False)
        rows: list[model] = pagination.items
        follower_ids = [i.user_id for i in rows]
        follower_users = User.query.filter(User.id.in_(follower_ids)).all()
        follower_user_map = {i.id: i for i in follower_users}

        items = []
        for r in rows:
            follower_user: User = follower_user_map[r.user_id]
            next_profit_shared_at = model.calc_next_profit_shared_at(r.last_profit_shared_at)
            next_profit_shared_at = int(next_profit_shared_at.timestamp())
            d = dict(
                follower_name=follower_user.hidden_complete_name,
                fund_amount=r.fund_amount,
                total_copy_earn_amount=r.total_profit_amount,
                expected_profit_share_amount=r.expected_profit_share_amount,
                next_profit_shared_at=next_profit_shared_at,
            )
            items.append(d)

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/follower/favorite")
@respond_with_code
class FollowerFavoriteTradersResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            nickname=mm_fields.String,
            hide_full=BoolField,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 我关注的交易员 """
        model = UserFavoriteCopyTrader
        user = g.user
        hide_full = kwargs.get("hide_full")
        _cur_trader = CopyTraderManager.get_trader(user.id)
        is_active_trader = _cur_trader and _cur_trader.is_active
        if hide_full and is_active_trader:
            # 交易员，无法跟单，勾选后不展示数据
            return dict(
                has_next=False,
                curr_page=1,
                count=0,
                data=[],
                total=0,
                total_page=1,
            )

        if not is_active_trader:
            following_trader_ids = CopyApiMixin.get_user_following_trader_ids(user.id)
        else:
            following_trader_ids = set()

        q = model.query.join(
            CopyTraderUser, CopyTraderUser.user_id == model.copy_trader_user_id,
        ).filter(
            model.user_id == user.id,
            model.status == model.Status.VALID,
            CopyTraderUser.status == CopyTraderUser.Status.ACTIVE,
        )
        if nickname := kwargs.get("nickname"):
            q = q.filter(CopyTraderUser.nickname.contains(nickname))
        if hide_full:
            q = q.filter(
                CopyTraderUser.cur_follower_num < CopyTraderUser.max_follower_num,
                CopyTraderUser.user_id.not_in(following_trader_ids),
            )

        page, limit = kwargs["page"], kwargs["limit"]
        q = q.order_by(model.updated_at.desc())
        pagination = q.with_entities(
            CopyTraderUser.user_id.label("trader_user_id"),
            CopyTraderUser.trader_id,
            CopyTraderUser.status,
            CopyTraderUser.cur_follower_num,
            CopyTraderUser.max_follower_num,
            CopyTraderUser.profit_share_rate,
            CopyTraderUser.last_started_at,
            CopyTraderUser.last_finished_at,
        ).paginate(page, limit, error_out=False)

        rows: list[model] = pagination.items
        trader_user_ids = [i.trader_user_id for i in rows]

        trade_info_map = CopyApiMixin.get_traders_trade_info(trader_user_ids, TimeRangeEnum.DAY30, True, TimeRangeEnum.DAY30)
        account_info_mapper = CopyApiMixin.get_account_info_mapper(trader_user_ids)
        items = []
        for r in rows:
            trader_user_id = r.trader_user_id
            trade_info = trade_info_map[trader_user_id]
            account_info = account_info_mapper[trader_user_id]
            profit_rate_series = trade_info.pop("profit_rate_series", [])
            profit_rate_series = [i for i in profit_rate_series if i[0] >= r.last_started_at.date()]
            items.append(
                dict(
                    trader_id=r.trader_id,
                    **account_info,
                    status=r.status.name,
                    cur_follower_num=r.cur_follower_num,
                    max_follower_num=r.max_follower_num,
                    profit_share_rate=r.profit_share_rate,
                    is_favorite=True,
                    is_follow=trader_user_id in following_trader_ids,
                    profit_rate_series=profit_rate_series,
                    **trade_info,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 关注交易员 """
        user = g.user
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists
        if user.id == trader.user_id:
            raise InvalidArgument
        with CacheLock(LockKeys.copy_trader_favorite(user.id), wait=False):
            db.session.rollback()
            TraderFavoriteHelper.add_favorite(user.id, trader.user_id)

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 取消关注交易员 """
        user = g.user
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists
        TraderFavoriteHelper.cancel_favorite(user.id, trader.user_id)


@ns.route("/follower/start")
@respond_with_code
class FollowerStartFollowResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
            margin_type=EnumField(CopyFollowerHistory.MarginType),
            leverage_type=EnumField(CopyFollowerHistory.LeverageType),
            leverage=mm_fields.Integer(missing=None, validate=lambda x: x in SUPPORT_LEVERAGES),
            add_margin_amount=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
            user_coupon_id=mm_fields.Integer,
            profit_trigger_rate=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
            loss_trigger_rate=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
        )
    )
    def post(cls, **kwargs):
        """ 跟单人-开始跟单 """
        follow_user = g.user

        require_ip_not_only_withdrawal()
        require_user_not_only_withdrawal(follow_user)
        require_user_kyc(follow_user)
        if not SiteSettings.perpetual_transfers_enabled:
            raise TransferNotAllowed

        # 兼容旧版本APP
        if 'margin_type' in kwargs and kwargs['margin_type'] != CopyFollowerHistory.MarginType.NONE:
            raise InvalidArgument(message=gettext("仅支持跟随交易员保证金模式，请重试"))
        if 'leverage_type' in kwargs and kwargs['leverage_type'] != CopyFollowerHistory.LeverageType.NONE:
            raise InvalidArgument(message=gettext("仅支持跟随交易员杠杆倍数，请重试"))
        if kwargs.get('profit_trigger_rate') or kwargs.get('loss_trigger_rate'):
            raise InvalidArgument(message=gettext("当前不支持设置止盈或止损，请清空后重试"))

        f_trader = CopyTraderManager.get_trader(follow_user.id)
        if f_trader and f_trader.is_active:
            raise InvalidArgument(message=gettext("交易员不支持跟单"))

        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader:
            raise CopyTraderNotExists
        if not trader.is_active:
            raise FollowInactiveCopyTraderError
        if trader.is_full:
            raise FollowFullCopyTraderError
        if follow_user.id == trader.user_id:
            raise InvalidArgument(message=gettext("交易员不支持跟单"))
        add_margin_amount = kwargs.get("add_margin_amount")
        if add_margin_amount:
            if add_margin_amount < trader.min_copy_amount:
                raise CopyTraderCopyAmountChanged
            if add_margin_amount > trader.max_copy_amount:
                raise CopyTraderCopyAmountChanged
            cls.check_add_margin_amount(follow_user, add_margin_amount)
        user_coupon_id = kwargs.get("user_coupon_id")
        if not add_margin_amount and not user_coupon_id:
            raise InvalidArgument
        if add_margin_amount and user_coupon_id:
            raise InvalidArgument
        user_coupon = coupon = None
        if user_coupon_id:
            user_coupon, coupon = CopyCouponMixin.get_and_check_ct_experience_fee_coupon(
                user_id=follow_user.id,
                sub_user_id=0,  # 提交跟单时，不知道子帐号
                user_coupon_id=user_coupon_id,
            )

        if CopyFollowerManager.has_trader_active_follow_his(trader.user_id, follow_user.id):
            raise InvalidArgument
        if CopyRelationQuerier.is_risk_user(follow_user.id):
            raise CopyTradingForbiddenError

        trader_user_id = trader.user_id
        follow_settings = dict(kwargs)
        with CacheLock(LockKeys.copy_trader(trader_user_id), wait=False), \
                CacheLock(LockKeys.copy_follower(follow_user.id), wait=False):
            db.session.rollback()
            follow_his = CopyFollowerManager.start_follow(trader, follow_user, follow_settings)
        # 增加保证金，单独事务
        if add_margin_amount:
            try:
                cls.do_add_margin_amount(follow_user, follow_his, add_margin_amount)
            except Exception as _e:  # noqa
                current_app.logger.error(f"start_follow_add_margin_err: {follow_user.id} {follow_his.id} {follow_his.sub_user_id} {_e!r}")
                retry_or_stop_zero_fund_follow_his(follow_his.id)
                if follow_his.status != CopyFollowerHistory.Status.FOLLOWING:
                    raise _e
        if user_coupon and coupon:
            CopyCouponMixin.follower_use_ct_experience_fee_coupon(user_coupon, coupon, follow_his)

        account_info = CopyApiMixin.get_account_info_mapper([trader_user_id])[trader_user_id]

        result = {
            "trader_id": trader.trader_id,
            "trader_nickname": account_info['nickname'],
            "trader_account_name": account_info['account_name'],
            "trader_avatar": account_info['avatar'],
            "profit_share_rate": trader.profit_share_rate,
            "id": follow_his.id,
            "profit_trigger_rate": follow_his.profit_trigger_rate,
            "loss_trigger_rate": follow_his.loss_trigger_rate,
            "margin_type": follow_his.margin_type.name,
            "leverage": follow_his.leverage,
            "leverage_type": follow_his.leverage_type.name,
            "status": follow_his.status.name,
            "asset": "USDT",
            "sub_user_id": follow_his.sub_user_id,
            "profit_unreal": Decimal(),
            "fund_amount": follow_his.fund_amount,
            "profit_amount": follow_his.total_profit_amount,
            "expected_profit_share_amount": follow_his.expected_profit_share_amount,
            "created_at": int(follow_his.created_at.timestamp()),
        }
        return result

    @classmethod
    def check_add_margin_amount(cls, user: User, amount: Decimal):
        if not SiteSettings.perpetual_transfers_enabled:
            raise TransferNotAllowed

        asset = "USDT"
        if asset in SiteSettings.forbidden_perpetual_transfer_in_assets:
            raise TransferNotAllowed
        if asset not in set(PerpetualCoinTypeCache().read_aside()):
            raise InvalidArgument

        user_id = user.id
        balance = ServerClient().get_user_balances(user_id, asset)[asset]
        if balance["available"] < amount:
            raise InsufficientBalance

    @classmethod
    def do_add_margin_amount(cls, user: User, follow_his: CopyFollowerHistory, amount: Decimal):
        # 跟单人增加保证金，从主帐号 转入 跟单子帐号，无法主动转出
        if follow_his.status != CopyFollowerHistory.Status.FOLLOWING:
            raise InvalidArgument

        with CacheLock(LockKeys.copy_follower_sub(follow_his.sub_user_id)):
            db.session.rollback()
            CopyFollowerManager.add_margin_amount(follow_his, amount)
        update_sub_account_balance_cache_task.delay(user.id, [follow_his.sub_user_id])


@ns.route("/follower/stop-preview")
@respond_with_code
class FollowerStopFollowPreviewResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 跟单人-停止跟单预览 """
        follow_user = g.user
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader or not trader.is_active:
            raise InvalidArgument
        trader_user_id = trader.user_id
        follow_his = CopyFollowerManager.get_last_active_follow_his(trader_user_id, follow_user.id)
        if not follow_his:
            raise InvalidArgument
        data = cls.get_profit_data(follow_his, trader)
        return {
            "profit_amount": data[0],
            "expected_profit_share_amount": data[1],
        }

    @classmethod
    def get_profit_data(cls, follow_his: CopyFollowerHistory, trader: CopyTraderUser) -> tuple[Decimal, Decimal]:

        @cached(60 * 5)
        def _calc_trader_profit_amount(follow_his_id) -> tuple[Decimal, Decimal]:  # noqa follow_his_id 用于缓存key
            finish_positions_query = query_users_time_range_finish_positions(
                {follow_his.sub_user_id},
                follow_his.last_profit_shared_at,
                None,
                created_at=follow_his.created_at,
                fin_columns=['profit_real'],
            )
            fol_profit_amount = sum([i['profit_real'] for i in finish_positions_query])
            profit_unreal_map, profit_real_map = CopyApiMixin.get_users_pending_position_profits(
                [follow_his.sub_user_id], unit_asset='USDT',
            )
            fol_profit_amount += sum(profit_unreal_map.values())
            fol_profit_amount += sum(profit_real_map.values())
            fol_profit_amount = quantize_amount(fol_profit_amount, PrecisionEnum.COIN_PLACES)
            trader_profit_amount = fol_profit_amount * trader.profit_share_rate if fol_profit_amount > 0 else Decimal()
            trader_profit_amount = quantize_amount(trader_profit_amount, PrecisionEnum.COIN_PLACES)
            return fol_profit_amount, trader_profit_amount

        res = _calc_trader_profit_amount(follow_his.id)
        return Decimal(res[0]), Decimal(res[1])


@ns.route("/follower/stop")
@respond_with_code
class FollowerStopFollowResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 跟单人-停止跟单 """
        follow_user = g.user
        trader_id = kwargs["trader_id"]
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader or not trader.is_active:
            raise InvalidArgument
        trader_user_id = trader.user_id
        follow_his = CopyFollowerManager.get_last_active_follow_his(trader_user_id, follow_user.id)
        if not follow_his:
            raise InvalidArgument
        with CacheLock(LockKeys.copy_follower_sub(follow_his.sub_user_id), wait=False):
            db.session.rollback()
            if follow_his.status != CopyFollowerHistory.Status.FOLLOWING:
                raise InvalidArgument
            CopyFollowerManager.stop_follow(follow_his)
        finish_copy_trading_follower_status.delay(follow_his.id)


@ns.route("/follower/settings")
@respond_with_code
class FollowerSettingsResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            trader_id=mm_fields.String(required=True),
            margin_type=EnumField(CopyFollowerHistory.MarginType),
            leverage_type=EnumField(CopyFollowerHistory.LeverageType),
            add_margin_amount=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
            user_coupon_id=mm_fields.Integer,
            profit_trigger_rate=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
            loss_trigger_rate=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN),
        )
    )
    def put(cls, **kwargs):
        """ 跟单人-修改跟单参数设定 """
        follow_user = g.user
        trader_id = kwargs["trader_id"]
        add_margin_amount = kwargs.get('add_margin_amount', 0)
        user_coupon_id = kwargs.get("user_coupon_id")
        trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
        if not trader or not trader.is_active:
            raise InvalidArgument

        # 兼容旧版本APP
        if 'margin_type' in kwargs and kwargs['margin_type'] != CopyFollowerHistory.MarginType.NONE:
            raise InvalidArgument(message=gettext("仅支持跟随交易员保证金模式，请重试"))
        if 'leverage_type' in kwargs and kwargs['leverage_type'] != CopyFollowerHistory.LeverageType.NONE:
            raise InvalidArgument(message=gettext("仅支持跟随交易员杠杆倍数，请重试"))
        if kwargs.get('profit_trigger_rate') or kwargs.get('loss_trigger_rate'):
            raise InvalidArgument(message=gettext("当前不支持设置止盈或止损，请清空后重试"))

        trader_user_id = trader.user_id
        with CacheLock(LockKeys.copy_follower(follow_user.id), wait=False):
            db.session.rollback()
            follow_his = CopyFollowerManager.get_last_active_follow_his(trader_user_id, follow_user.id)
            if not follow_his:
                raise InvalidArgument
            if follow_his.status != CopyFollowerHistory.Status.FOLLOWING:
                raise InvalidArgument
            if add_margin_amount > 0 and add_margin_amount + follow_his.fund_amount > trader.max_copy_amount:
                raise CopyTraderCopyAmountChanged
            user_coupon = coupon = None
            if user_coupon_id:
                user_coupon, coupon = CopyCouponMixin.get_and_check_ct_experience_fee_coupon(
                    user_id=follow_user.id,
                    sub_user_id=follow_his.sub_user_id,
                    user_coupon_id=user_coupon_id,
                )

            follow_his = CopyFollowerManager.edit_follow_settings(follow_his, kwargs)

            # 增加保证金，单独事务
            if add_margin_amount > 0:
                FollowerStartFollowResource.check_add_margin_amount(follow_user, add_margin_amount)
                FollowerStartFollowResource.do_add_margin_amount(follow_user, follow_his, add_margin_amount)
            if user_coupon and coupon:
                CopyCouponMixin.follower_use_ct_experience_fee_coupon(user_coupon, coupon, follow_his)


@ns.route('/follower/margin/adjust')
@respond_with_code
class FollowerAdjustPositionMarginResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        market=mm_fields.String(required=True),
        follow_history_id=mm_fields.Integer(required=True),
        amount=PositiveDecimalField(required=True),
    ))
    def post(cls, **kwargs):
        """ 跟单人-增加保证金（仓位是跟单子帐号的）"""
        market = kwargs['market']
        follow_history_id = kwargs['follow_history_id']
        amount = kwargs['amount']

        follower_id = g.user.id
        model = CopyFollowerHistory
        follow_his: model = model.query.filter(
            model.user_id == follower_id,
            model.status != model.Status.FINISHED,
            model.id == follow_history_id,
        ).first()
        if not follow_his:
            raise InvalidArgument
        if market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument

        margin_type = 1  # 1: add margin, 2: sub margin
        sub_user_id = follow_his.sub_user_id
        with CacheLock(LockKeys.perpetual_adjust_margin(sub_user_id, market), wait=False):
            client = PerpetualServerClient()
            try:
                client.adjust_margin(
                    user_id=sub_user_id,
                    market=market,
                    margin_type=margin_type,
                    amount=amount_to_str(amount, 8),
                )
            except client.BadResponse as e:
                if e.code == PerpetualResponseCode.CONTRACT_BALANCE_NOT_ENOUGH:
                    raise InsufficientBalance
        return {}


@ns.route('/follower/transfer-history')
@respond_with_code
class FollowerTransferHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        follow_history_id=mm_fields.Integer(required=True),
        start_date=TimestampField(to_date=True),
        end_date=TimestampField(to_date=True),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """ 跟单人-划转历史 """
        user = g.user
        main_user_id = user.id
        follow_history_id = kwargs["follow_history_id"]
        if not CopyFollowerHistory.query.filter(
            CopyFollowerHistory.id == follow_history_id,
            CopyFollowerHistory.user_id == main_user_id,
        ).with_entities(CopyFollowerHistory.id).first():
            raise InvalidArgument

        query = CopyTransferHistory.query.filter(
            CopyTransferHistory.main_user_id == main_user_id,
            CopyTransferHistory.history_id == follow_history_id,
            CopyTransferHistory.status == CopyTransferHistory.Status.FINISHED,
            CopyTransferHistory.type.in_(CopyTransferHistory.FOLLOWER_TYPES),
        ).order_by(CopyTransferHistory.id.desc())
        if end_date := kwargs.get('end_date'):
            _next_end_dt = end_date + timedelta(days=1)
            query = query.filter(CopyTransferHistory.created_at < _next_end_dt)
        if start_date := kwargs.get('start_date'):
            query = query.filter(CopyTransferHistory.created_at >= start_date)

        page, limit = kwargs["page"], kwargs["limit"]
        pagination = query.paginate(page, limit, error_out=False)

        record = pagination.items
        items = []
        for i in record:
            if i.type == CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN:
                type_ = "IN"
            elif i.type == CopyTransferHistory.Type.FOLLOWER_TRANSFER_OUT:
                type_ = "OUT"
            elif i.type == CopyTransferHistory.Type.FOLLOWER_COUPON_USE:
                type_ = "COUPON_USE"
            elif i.type == CopyTransferHistory.Type.FOLLOWER_COUPON_RECYCLE:
                type_ = "COUPON_RECYCLE"
            else:
                type_ = ""
            item = {
                "created_at": int(i.created_at.timestamp()),
                "type": type_,
                "asset": i.asset,
                "amount": i.amount,
            }
            items.append(item)

        return dict(
            data=items,
            curr_page=pagination.page,
            has_next=pagination.has_next,
            count=len(items),
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/follower/follow-data")
@respond_with_code
class FollowerFollowDataResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 跟单人-跟单数据统计 """
        follow_user = g.user
        follower_id = follow_user.id

        rows: list[CopyFollowerHistory] = CopyFollowerHistory.query.filter(
            CopyFollowerHistory.user_id == follower_id,
            CopyFollowerHistory.status != CopyFollowerHistory.Status.FINISHED,
        ).all()
        unit_asset = "USDT"
        sub_ids = [r.sub_user_id for r in rows]
        try:
            profit_unreal_map, _ = CopyApiMixin.get_users_pending_position_profits(sub_ids, unit_asset=unit_asset)
        except:  # noqa
            profit_unreal_map = {}

        st_row: CopyFollowerStatistics = CopyFollowerStatistics.query.filter(
            CopyFollowerStatistics.user_id == follower_id,
        ).first()
        result = {
            "profit_amount": st_row.profit_amount if st_row else Decimal(),
            "profit_unreal": Decimal(sum(profit_unreal_map.values())),  # 当前跟单-未实现盈亏的汇总
        }
        return result


@ns.route("/follower/current-follow")
@respond_with_code
class FollowerCurrentFollowResource(Resource):
    @classmethod
    def get_user_available_amounts(cls, user_ids: list[int]) -> dict[int, Decimal]:
        if not user_ids:
            return {}
        asset = "USDT"
        try:
            user_balance_map = PerpetualServerClient().get_users_balances(user_ids=user_ids, asset=asset)
            user_available_map = {u: quantize_amount(d[asset]['available'], 8) for u, d in user_balance_map.items()}
            return user_available_map
        except:  # noqa
            return {}

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 跟单人-当前跟单 """
        follow_user = g.user
        follower_id = follow_user.id
        rows: list[CopyFollowerHistory] = CopyFollowerHistory.query.filter(
            CopyFollowerHistory.user_id == follower_id,
            CopyFollowerHistory.status != CopyFollowerHistory.Status.FINISHED,
        ).all()
        items = []

        trader_user_ids = [r.copy_trader_user_id for r in rows]
        trader_info_map = CopyApiMixin.get_trader_info_map(trader_user_ids)

        unit_asset = "USDT"
        sub_ids = [r.sub_user_id for r in rows]
        profit_unreal_map, _ = CopyApiMixin.get_users_pending_position_profits(sub_ids, unit_asset=unit_asset)
        available_amount_map = cls.get_user_available_amounts(sub_ids)
        for r in rows:
            trader_ = trader_info_map.get(r.copy_trader_user_id, {})
            d = {
                "id": r.id,
                "trader_id": trader_.get("trader_id", ""),
                "trader_nickname": trader_.get("nickname", ""),
                "trader_account_name": trader_.get("account_name", ""),
                "trader_avatar": trader_.get("avatar", ""),
                "fund_amount": r.fund_amount,
                "available_amount": available_amount_map.get(r.sub_user_id, Decimal()),
                "asset": unit_asset,
                "status": r.status.name,
                "sub_user_id": r.sub_user_id,
                "profit_amount": r.total_profit_amount,
                "profit_unreal": profit_unreal_map.get(r.sub_user_id, Decimal()),
                "expected_profit_share_amount": r.expected_profit_share_amount,
                "created_at": int(r.created_at.timestamp()),
            }
            items.append(d)
        return items


@ns.route("/follower/finished-follow")
@respond_with_code
class FollowerFinishedFollowResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 跟单人-历史跟单 """
        follow_user = g.user
        follower_id = follow_user.id
        model = CopyFollowerHistory
        q = model.query.filter(
            model.user_id == follower_id,
            model.status == model.Status.FINISHED,
            model.fund_amount > 0,
        )
        page, limit = kwargs["page"], kwargs["limit"]
        pagination = q.order_by(model.finished_at.desc()).paginate(page, limit, error_out=False)
        rows: list[model] = pagination.items

        trader_user_ids = [r.copy_trader_user_id for r in rows]
        trader_info_map = CopyApiMixin.get_trader_info_map(trader_user_ids)

        fol_his_ids = [i.id for i in rows]
        finish_share_rows = CopyTraderProfitShareDetail.query.filter(
            CopyTraderProfitShareDetail.follow_history_id.in_(fol_his_ids),
            CopyTraderProfitShareDetail.profit_share_type !=
            CopyTraderProfitShareDetail.ProfitShareType.PERIODIC_SETTLEMENT,
        ).with_entities(
            CopyTraderProfitShareDetail.follow_history_id,
            CopyTraderProfitShareDetail.profit_share_type,
        ).all()
        finish_type_map = {i.follow_history_id: i.profit_share_type for i in finish_share_rows}

        items = []
        for r in rows:
            trader_ = trader_info_map.get(r.copy_trader_user_id)
            finish_type = finish_type_map.get(r.id)
            d = {
                "id": r.id,
                "trader_id": trader_.get("trader_id", ""),
                "trader_nickname": trader_.get("nickname", ""),
                "trader_account_name": trader_.get("account_name", ""),
                "trader_avatar": trader_.get("avatar", ""),
                "fund_amount": r.fund_amount,
                "asset": "USDT",
                "profit_amount": r.total_profit_amount,
                "created_at": int(r.created_at.timestamp()),
                "finished_at": int(r.finished_at.timestamp()) if r.finished_at else None,
                "finished_type": finish_type.name if r.finished_at and finish_type else None,
            }
            items.append(d)
        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/follower/follow-detail")
@respond_with_code
class FollowerFollowDetailResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            id=mm_fields.Integer(validate=lambda x: x > 0),
            trader_id=mm_fields.String,  # 传交易员ID，获取最新的跟单记录
        )
    )
    def get(cls, **kwargs):
        """ 跟单人-跟单详情 """
        his_id = kwargs.get("id")
        trader_id = kwargs.get("trader_id")
        if not his_id and not trader_id:
            raise InvalidArgument

        follow_user = g.user
        follower_id = follow_user.id
        model = CopyFollowerHistory
        if his_id:
            follow_his: model = model.query.filter(
                model.user_id == follower_id,
                model.id == his_id,
            ).first()
            if not follow_his:
                raise InvalidArgument
            trader = CopyTraderManager.get_trader(follow_his.copy_trader_user_id)
            if not trader:
                raise CopyTraderNotExists
        else:
            trader = CopyTraderManager.get_trader_by_trader_id(trader_id)
            if not trader:
                raise CopyTraderNotExists
            follow_his: model = model.query.filter(
                model.user_id == follower_id,
                model.copy_trader_user_id == trader.user_id,
            ).order_by(model.id.desc()).first()
            if not follow_his:
                raise InvalidArgument

        if follow_his.status == model.Status.FOLLOWING:
            cp_user_coupon = CopyTradingExperienceFeeService.query_sub_using_coupon(
                follow_his.user_id, follow_his.sub_user_id,
            )
            active_user_coupon_id = cp_user_coupon.user_coupon_id if cp_user_coupon else None
        else:
            active_user_coupon_id = None

        if follow_his.status != model.Status.FINISHED:
            is_follow = True
        else:
            is_follow = CopyFollowerManager.has_trader_active_follow_his(trader.user_id, follow_user.id)

        usdt = "USDT"
        sub_user_id = None
        available_amount = profit_unreal = Decimal()
        if follow_his.status != model.Status.FINISHED:
            sub_user_id = follow_his.sub_user_id
            available_amount_map = FollowerCurrentFollowResource.get_user_available_amounts([sub_user_id])
            available_amount = available_amount_map.get(sub_user_id, Decimal())
            profit_unreal_map, _ = CopyApiMixin.get_users_pending_position_profits([sub_user_id], unit_asset=usdt)
            profit_unreal = profit_unreal_map.get(sub_user_id, Decimal())
            finish_type = None
        else:
            finish_share_row = CopyTraderProfitShareDetail.query.filter(
                CopyTraderProfitShareDetail.follow_history_id == follow_his.id,
                CopyTraderProfitShareDetail.profit_share_type !=
                CopyTraderProfitShareDetail.ProfitShareType.PERIODIC_SETTLEMENT,
            ).with_entities(
                CopyTraderProfitShareDetail.profit_share_type,
            ).first()
            finish_type = finish_share_row.profit_share_type.name if finish_share_row else None

        account_info = CopyApiMixin.get_account_info_mapper([trader.user_id])[trader.user_id]
        result = {
            "trader_id": trader.trader_id,
            "trader_nickname": account_info['nickname'],
            "trader_account_name": account_info['account_name'],
            "trader_avatar": account_info['avatar'],
            "profit_share_rate": trader.profit_share_rate,
            "trader_status": trader.status.name,
            "is_follow": is_follow,
            "id": follow_his.id,
            "active_user_coupon_id": active_user_coupon_id,
            "profit_trigger_rate": follow_his.profit_trigger_rate,
            "loss_trigger_rate": follow_his.loss_trigger_rate,
            "margin_type": follow_his.margin_type.name,
            "leverage": follow_his.leverage,
            "leverage_type": follow_his.leverage_type.name,
            "status": follow_his.status.name,
            "asset": usdt,
            "sub_user_id": sub_user_id,
            "available_amount": available_amount,
            "profit_unreal": profit_unreal,
            "fund_amount": follow_his.fund_amount,
            "profit_amount": follow_his.total_profit_amount,
            "expected_profit_share_amount": follow_his.expected_profit_share_amount,
            "created_at": int(follow_his.created_at.timestamp()),
            "finished_at": int(follow_his.finished_at.timestamp()) if follow_his.finished_at else None,
            "finished_type": finish_type,
        }
        return result


@ns.route("/follower/current-position")
@respond_with_code
class FollowerCurrentPositionResource(Resource):
    @classmethod
    def get_active_follow_his(cls, follower_id: int, follow_id: int) -> CopyFollowerHistory:
        model = CopyFollowerHistory
        follow_his: model = model.query.filter(
            model.user_id == follower_id,
            model.status != model.Status.FINISHED,
            model.id == follow_id,
        ).first()
        if not follow_his:
            raise InvalidArgument
        return follow_his

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            follow_history_id=mm_fields.Integer(required=True, validate=lambda x: x > 0),
            market=mm_fields.String(missing=None),
        )
    )
    def get(cls, **kwargs):
        """ 跟单人-跟单详情-当前仓位 """
        follow_user = g.user
        follower_id = follow_user.id
        follow_his = cls.get_active_follow_his(follower_id, kwargs["follow_history_id"])

        run_sub_id = follow_his.sub_user_id
        market = kwargs['market']
        if market and market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument

        client = PerpetualServerClient()
        pos_list = client.position_pending(run_sub_id, kwargs['market'])
        return PublicTraderCurrentPositionResource.format_cur_positions(pos_list)


@ns.route("/follower/finished-position")
@respond_with_code
class FollowerFinishedPositionResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            follow_history_id=mm_fields.Integer(required=True, validate=lambda x: x > 0),
            market=mm_fields.String(missing=''),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 跟单人-跟单详情-历史仓位 """
        # 注：跟单人-跟单详情-当前仓位，前端是直接从WebSocket取数据
        follow_user = g.user
        follower_id = follow_user.id
        model = CopyFollowerHistory
        follow_his: model = model.query.filter(
            model.user_id == follower_id,
            model.id == kwargs["follow_history_id"],
        ).first()
        if not follow_his:
            raise InvalidArgument

        run_sub_id = follow_his.sub_user_id
        market = kwargs['market']
        if market and market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument

        page, limit = kwargs["page"], kwargs["limit"]
        start_ts = int(follow_his.created_at.timestamp())
        end_ts = int(follow_his.finished_at.timestamp()) if follow_his.finished_at else 0
        client = PerpetualServerClient()
        result = client.position_finished(
            user_id=run_sub_id,
            market=market,
            start_time=start_ts,
            end_time=end_ts,
            page=page,
            limit=limit,
        )
        result['records'] = PublicTraderCurrentPositionResource.format_fin_positions(result['records'])
        return offset_to_page(result)


@ns.route('/follower/close-position')
@respond_with_code
class FollowerMarketClosePositionResource(Resource):
    @classmethod
    def get_active_follow_his(cls, follower_id: int, follow_id: int) -> CopyFollowerHistory:
        model = CopyFollowerHistory
        follow_his: model = model.query.filter(
            model.user_id == follower_id,
            model.status != model.Status.FINISHED,
            model.id == follow_id,
        ).first()
        if not follow_his:
            raise InvalidArgument
        return follow_his

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            follow_history_id=mm_fields.Integer(required=True, validate=lambda x: x > 0),
            market=mm_fields.String(required=True),
        )
    )
    @trade_permission_validate(is_spot=False, is_closing_or_reducing_position=True)
    def post(cls, **kwargs):
        """ 跟单人-跟单详情-当前仓位 一键平仓 """
        from app.api.frontend.perpetual.order import fetch_cal_fee

        follow_user = g.user
        follower_id = follow_user.id
        follow_his = cls.get_active_follow_his(follower_id, kwargs["follow_history_id"])

        client = PerpetualServerClient()
        run_sub_id = follow_his.sub_user_id
        market = kwargs['market']
        pos_list = client.position_pending(run_sub_id, market)
        if not pos_list:
            raise InvalidArgument
        position_id = pos_list[0]["position_id"]

        market_info = PerpetualMarketCache().get_market_info(market)
        if not market_info:
            raise InvalidArgument
        with CacheLock(LockKeys.perpetual_put_market(run_sub_id, market), wait=False):
            db.session.rollback()
            fee_rate = fetch_cal_fee(follower_id, market)  # 主帐号的
            client.market_close(
                user_id=run_sub_id,
                market=market,
                position_id=position_id,
                amount='0',  # 0 代表全部平仓,
                taker_fee_rate=amount_to_str(fee_rate[TradeType.TAKER], 8),
                source=RequestPlatform.from_request().value
            )


@ns.route("/follower/profit-share-history")
@respond_with_code
class FollowerProfitShareHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            follow_history_id=mm_fields.Integer(validate=lambda x: x > 0),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 跟单人-跟单详情-分润记录 """
        follow_user = g.user
        follower_id = follow_user.id
        follow_his_id = kwargs["follow_history_id"]

        model = CopyTraderProfitShareDetail
        q = model.query.filter(
            model.user_id == follower_id,
            model.follow_history_id == follow_his_id,
            model.status == model.Status.FINISHED,
            model.amount > 0,
        )
        page, limit = kwargs["page"], kwargs["limit"]
        pagination = q.order_by(model.id.desc()).paginate(page, limit, error_out=False)

        rows: list[model] = pagination.items
        items = []
        for r in rows:
            items.append(
                dict(
                    created_at=int(r.created_at.timestamp()),
                    asset=r.asset,
                    profit_amount=r.profit_amount,
                    amount=r.amount,
                    profit_share_rate=r.profit_share_rate,
                    profit_share_type=r.profit_share_type.name,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )
