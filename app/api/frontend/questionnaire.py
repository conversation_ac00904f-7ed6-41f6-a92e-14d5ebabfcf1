import os

from flask import g, request, current_app
from flask_babel import gettext as _
from marshmallow import Schema, fields as mm_fields

from app import Language
from app.api.common import Namespace, Resource, respond_with_code, get_request_user, require_login, \
     limit_user_frequency
from app.api.common.fields import <PERSON><PERSON><PERSON><PERSON>
from app.business.questionnaire import Questionnaire<PERSON>anage, QuestionnaireResponseManage
from app.caches.questionnaire import (QuestionnaireCache, QuestionnaireViewCountCache, QuestionnaireLoginViewCountCache)
from app.exceptions import InvalidArgument, FileTooBig, FileTooSmall, ServiceUnavailable, \
    RecordNotFound, QuestionnaireNotFound
from app.models import db
from app.models.mongo.questionnaire import (QuestionnaireMySQL, QuestionnaireResponseMySQL, 
                                          QuestionnaireAnswerMySQL, QuestionType)
from app.utils import AWSBucketPublic, AWSBucketPrivate, now

ns = Namespace('Questionnaire')


@ns.route("/<string:q_id>")
@respond_with_code
class QuestionnaireResource(Resource):

    class QuestionnaireAnswerSchema(Schema):
        question_uid = mm_fields.String(required=True)
        answer = mm_fields.Dict(required=True)

    @classmethod
    def get(cls, q_id, **kwargs):
        """获取问卷"""
        lang = Language(g.lang)

        questionnaire = QuestionnaireCache(q_id).read_by_lang(lang)

        if not questionnaire:
            raise RecordNotFound(message="unknown questionnaire")

        if not questionnaire.get('completed'):
            raise QuestionnaireNotFound({"completed_langs": questionnaire.get('completed_langs', [])})

        if web_file_key := questionnaire.get("header_image_web"):
            questionnaire["header_image_web"] = AWSBucketPublic.get_file_url(web_file_key)
        if h5_file_key := questionnaire.get("header_image_h5"):
            questionnaire["header_image_h5"] = AWSBucketPublic.get_file_url(h5_file_key)

        user = get_request_user(allow_sub_account=False)

        if (user and questionnaire['submit_limit'] == QuestionnaireMySQL.SubmitLimit.ONCE.name and
                QuestionnaireResponseManage.check_user_has_once_submit_response(q_id, user.id)):
            answer_map = QuestionnaireResponseManage.get_user_answer_map(q_id, user.id)

            for q in questionnaire['questions']:
                answer = answer_map.get(q['question']['uid'])
                if answer is not None:
                    if q['question']['type'] == QuestionType.UPLOAD_FILE.name and answer.get('file_key'):
                        q['answer'] = {"file_url": AWSBucketPrivate.get_file_url(answer['file_key'])}
                    else:
                        q['answer'] = answer
            questionnaire['has_submit'] = True

        if questionnaire['start_time'] < now().timestamp() < questionnaire['end_time']:
            try:
                QuestionnaireViewCountCache(q_id, lang.value).incr()
                if user:
                    QuestionnaireLoginViewCountCache(q_id, lang.value).incr()
            except Exception as e:
                current_app.logger.error(e)
        return questionnaire

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        time=mm_fields.Integer(required=True),
        platform=EnumField(QuestionnaireResponseMySQL.Platform, required=True),
        answers=mm_fields.List(mm_fields.Nested(QuestionnaireAnswerSchema()), required=True)
    ))
    @limit_user_frequency(5, 300)
    def post(cls, q_id, **kwargs):
        """提交问卷"""
        user = g.user
        lang = Language(g.lang)
        questionnaire = QuestionnaireManage.get_questionnaire_by_id(
            q_id, ["submit_limit", "start_time", "end_time", "deleted"]
        )

        if questionnaire.deleted:
            raise InvalidArgument(message=_("问卷已结束，无法提交"))

        if QuestionnaireManage.get_status(questionnaire) == QuestionnaireMySQL.Status.PENDING:
            raise InvalidArgument(message=_("问卷未开始，无法提交"))
        elif QuestionnaireManage.get_status(questionnaire) == QuestionnaireMySQL.Status.TERMINATION:
            raise InvalidArgument(message=_("问卷已结束，无法提交"))

        if questionnaire.submit_limit == QuestionnaireMySQL.SubmitLimit.ONCE:
            if QuestionnaireResponseManage.check_user_has_once_submit_response(q_id, user.id):
                raise InvalidArgument(message=_("问卷已提交，请勿重复操作"))
            questionnaire_response_uid = QuestionnaireResponseManage.OnceSubmitResponseUid
        else:
            questionnaire_response_uid = QuestionnaireResponseManage.new_response_uid()

        questionnaire_response = QuestionnaireResponseMySQL(
            user_id=user.id,
            questionnaire_uid=q_id,
            uid=questionnaire_response_uid,
            time=kwargs['time'],
            platform=kwargs['platform'],
            lang=lang,
            country=user.location_code,
        )

        questionnaire_content = QuestionnaireManage.get_questionnaire_content_by_lang(q_id, lang)
        if not questionnaire_content.completed:
            raise InvalidArgument(message="invalid questionnaire")  # 提交了未完成的问卷
        question_map = {}
        required_question = set()
        for q in questionnaire_content.questions:
            question_map[q["uid"]] = q
            if q.get('required'):
                required_question.add(q["uid"])
        submitted_question = set()
        answers = []
        for answer in kwargs['answers']:
            question_uid = answer['question_uid']
            if question_uid in submitted_question:
                raise InvalidArgument(message="duplicate question")  # 问题重复提交

            question = question_map.get(question_uid)
            if not question:
                raise InvalidArgument(message="invalid question uid")  # 无效的问题uid

            try:
                answer = QuestionnaireResponseManage.validate_answer(question, answer['answer'])
            except InvalidArgument as e:
                raise InvalidArgument(message=f"invalid answer format: {e.message}")  # 答案格式异常

            if question_uid in required_question:
                required_question.remove(question_uid)
            answers.append((question_uid, question['type'], answer))

        if required_question:
            raise InvalidArgument(message="miss required question")  # 需回答必答问题

        for question_uid, question_type, answer in answers:
            qa = QuestionnaireAnswerMySQL.query.filter_by(
                questionnaire_uid=q_id,
                user_id=user.id,
                response_uid=questionnaire_response.uid,
                question_uid=question_uid
            ).first()

            if not qa:
                qa = QuestionnaireAnswerMySQL(
                    questionnaire_uid=q_id,
                    user_id=user.id,
                    response_uid=questionnaire_response.uid,
                    question_uid=question_uid,
                    question_type=QuestionType[question_type],
                    answer=answer,
                )
                db.session.add(qa)
            else:
                qa.answer = answer

        # 后保存response，防止出现问题数据
        db.session.add(questionnaire_response)
        db.session.commit()


@ns.route("/upload")
@respond_with_code
class QuestionnaireUploadResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(20, 600)
    def post(cls, **kwargs):
        """问卷上传文件"""
        questionnaire_uid = request.form.get('questionnaire_uid')
        question_uid = request.form.get('question_uid')

        if not questionnaire_uid or not question_uid:
            raise InvalidArgument(message="miss uid")

        if not (file := request.files.get('file')):
            raise InvalidArgument(message="miss file")

        lang = Language(g.lang)
        questionnaire = QuestionnaireCache(questionnaire_uid).read_by_lang(lang)
        allowed_ext = set()
        for q in questionnaire.get('questions', []):
            question = q['question']
            if question['uid'] == question_uid:
                if question['type'] != QuestionType.UPLOAD_FILE.name:
                    raise InvalidArgument
                for allow_type in question.get('type_limit', []):
                    if allow_type == QuestionnaireManage.FileType.PNG.name:
                        allowed_ext.add('.png')
                    elif allow_type == QuestionnaireManage.FileType.JPG.name:
                        allowed_ext.add('.jpeg')
                        allowed_ext.add('.jpg')
                break
        else:
            raise InvalidArgument(message="unknown question")

        _, ext = os.path.splitext(file.filename)
        ext = ext.lower() if ext else None
        if ext not in allowed_ext:
            raise InvalidArgument(message="invalid ext")

        if (size := int(request.headers['CONTENT_LENGTH'])) > question.get('size_limit', 0):
            raise FileTooBig

        if size < 1024 * 5:
            raise FileTooSmall

        file_type = ext.split('.')[1]
        file_key = AWSBucketPrivate.new_file_key(suffix=file_type)
        if not AWSBucketPrivate.put_file(file_key, file):
            raise ServiceUnavailable
        url = AWSBucketPrivate.get_file_url(file_key)

        return dict(
            questionnaire_uid=questionnaire_uid,
            question_uid=question_uid,
            file_url=url,
            file_key=file_key
        )
