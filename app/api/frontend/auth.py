#!/usr/bin/env python3
import json
from enum import Enum
from typing import List, <PERSON>tional, <PERSON><PERSON>

from flask import g, current_app, request
from flask_babel import gettext as _
from flask_api.exceptions import AuthenticationFailed
from webargs import fields
from app.api.common.decorators import verify_user_permission
from app.business.oauth import get_oauth_client, get_oauth_client_names
from app.business.security import SecuritySettingType, update_security_statistics
from app.business.user import update_user_two_fa_type
from app.caches.auth import ThirdPartyAccountOperationTokenCache
from app.exceptions.basic import NotSupportedByApp
from app.exceptions.denied import OperationDenied
from app.models.mongo.sms import MobileMessageRecordMySQL
from ... import config

from ...business import UserPreferences, CountrySettings
from ...caches import EmailCodeTokenCache, UserOperationTokenCache, SmsAuthCache
from ...common import OPERATION_TOKEN_SIZE, EmailCodeType, MobileCodeType, get_country, TwoFAType
from ...exceptions import (<PERSON>ailNotAllowed, EmailAlreadyExists, EmailDoesNotExist,
                           InvalidArgument, NotSupportedByCountryInOnlyWithdrawal, UserEmailRepeat)
from ...models import User, UserWebAuthn, db, OperationLog
from ...utils import (
    is_disposable_email, new_hex_token, get_mobile_country, max_length_validator,
)
from ..common import (
    Namespace, EmailCodeValidator, MobileCodeValidator,
    Resource, TotpCodeValidator, get_request_user, get_request_auth_user,
    require_geetest, require_login, require_security_reset_token,
    require_login_operation_token, respond_with_code, get_request_platform, json_string_success, require_2fa,
    require_email_code,
)
from ..common.fields import EnumField, EmailField
from ..common.request import get_request_ip, is_old_app_request, get_request_host_url, \
    require_email_exists, require_email_not_exists
from ..common.auth import sign_qrcode_url, WebAuthn
from ...business.email import send_email_registered_notice_email


ns = Namespace('Auth')


@ns.route('/email/code')
@respond_with_code
class EmailCodeResource(Resource):

    class Require(Enum):
        EMAIL_ARG = 'email_arg'
        EMAIL_EXISTS = 'email_exists'
        EMAIL_NOT_EXISTS = 'email_not_exists'
        LOGIN = 'login'
        MAIN_ACCOUNT = 'main_account'
        GEETEST = 'geetest'

    requires = {
        Require.EMAIL_ARG: [
            EmailCodeType.SIGN_IN,
            EmailCodeType.REGISTRATION,
            EmailCodeType.EMAIL_BINDING,
            EmailCodeType.EMAIL_RESET,
            EmailCodeType.RESET_2FA,
            EmailCodeType.LOGIN_PASSWORD_RESET,
            EmailCodeType.LOGIN_PASSWORD_SET,
            EmailCodeType.NON_LOGIN_PASSWORD_RESET,
            EmailCodeType.NON_LOGIN_EMAIL_RESET,
            EmailCodeType.NON_LOGIN_BIND_THIRD_PARTY_ACCOUNT,
            EmailCodeType.SEND_C_BOX,
            EmailCodeType.UNFREEZE_ACCOUNT,
        ],
        Require.EMAIL_EXISTS: [ # Require.EMAIL_ARG时，必须设置EMAIL_EXISTS/EMAIL_NOT_EXISTS其中一个
            EmailCodeType.SIGN_IN,
            EmailCodeType.LOGIN_PASSWORD_RESET,
            EmailCodeType.NON_LOGIN_PASSWORD_RESET,
            EmailCodeType.NON_LOGIN_BIND_THIRD_PARTY_ACCOUNT,
            EmailCodeType.LOGIN_PASSWORD_SET,
            EmailCodeType.RESET_2FA,
            EmailCodeType.SEND_C_BOX,
            EmailCodeType.UNFREEZE_ACCOUNT,
        ],
        Require.EMAIL_NOT_EXISTS: [
            EmailCodeType.REGISTRATION,
            EmailCodeType.EMAIL_BINDING,
            EmailCodeType.EMAIL_RESET,
            EmailCodeType.NON_LOGIN_EMAIL_RESET,
        ],
        Require.MAIN_ACCOUNT: [
            EmailCodeType.EMAIL_BINDING,
            EmailCodeType.EMAIL_RESET,
            EmailCodeType.EMAIL_EDIT,
            EmailCodeType.MOBILE_BINDING,
            EmailCodeType.MOBILE_EDIT,
            EmailCodeType.MOBILE_UNBIND,
            EmailCodeType.TOTP_BINDING,
            EmailCodeType.TOTP_EDIT,
            EmailCodeType.TOTP_UNBIND,
            EmailCodeType.API_WITHDRAWAL_ADDRESS_ADDITION,
            EmailCodeType.ANTI_PHISHING_CODE_ADD,
            EmailCodeType.ANTI_PHISHING_CODE_EDIT,
            EmailCodeType.SIGN_OFF,
            EmailCodeType.SEND_C_BOX,
            EmailCodeType.ADD_WITHDRAW_PASSWORD,
            EmailCodeType.EDIT_WITHDRAW_PASSWORD,
            EmailCodeType.RESET_WITHDRAW_PASSWORD,
            EmailCodeType.LOGIN_PASSWORD_RESET,
            EmailCodeType.LOGIN_PASSWORD_SET,
            EmailCodeType.BIND_WEBAUTHN,
            EmailCodeType.UNBIND_WEBAUTHN,
            EmailCodeType.TRADING_PASSWORD_EDIT,
            EmailCodeType.TRADING_PASSWORD_ADDITION,
            EmailCodeType.TRADING_PASSWORD_REMOVE,
            EmailCodeType.BIND_THIRD_PARTY_ACCOUNT
        ],
        Require.GEETEST: [
            EmailCodeType.NON_LOGIN_PASSWORD_RESET,
            EmailCodeType.REGISTRATION,
            EmailCodeType.NON_LOGIN_EMAIL_RESET,
            EmailCodeType.RESET_2FA,
            EmailCodeType.UNFREEZE_ACCOUNT,
            EmailCodeType.NON_LOGIN_BIND_THIRD_PARTY_ACCOUNT
        ],
    }

    @classmethod
    def check_requires(cls, email: Optional[str], code_type: EmailCodeType,
                       reqs: List[Require] = None) -> Tuple[Optional[User], str]:
        _email = _user = None
        if not reqs:
            reqs = cls.Require
        for key in reqs:
            if code_type not in cls.requires.get(key, []):
                continue
            if key == cls.Require.EMAIL_ARG:
                if not email:
                    raise InvalidArgument
                _email = email
            elif key == cls.Require.EMAIL_EXISTS:
                require_email_exists(email)
            elif key == cls.Require.EMAIL_NOT_EXISTS:
                if is_disposable_email(email):
                    raise EmailNotAllowed
                # 重置邮箱：是否和当前用户的邮箱一致
                user = get_request_user()
                if code_type in (EmailCodeType.EMAIL_RESET, EmailCodeType.NON_LOGIN_EMAIL_RESET) and user:
                    if email.lower() == user.main_user_email.lower():
                        raise UserEmailRepeat
                require_email_not_exists(email)
            elif key == cls.Require.MAIN_ACCOUNT:
                _switch_user = get_request_user(allow_none=False)
                _user = g.auth_user
                if _switch_user.id != _user.id:
                    raise InvalidArgument
            elif key == cls.Require.GEETEST:
                require_geetest(lambda: None)()

        _email = _email or (_user and _user.main_user_email)
        if not _email:
            raise InvalidArgument
        if _user:
            g.lang = UserPreferences(_user.id).language.value
        return _user, _email

    @classmethod
    @ns.use_kwargs(dict(
        email=EmailField,
        code_type=EnumField(EmailCodeType, enum_by_value=True, required=True),
    ))
    def get(cls, **kwargs):
        email = kwargs.get('email')
        code_type = kwargs['code_type']
        if is_old_app_request(3260, 40) and code_type == EmailCodeType.LOGIN_PASSWORD_RESET:
            raise NotSupportedByApp
        try:
            _user, email = cls.check_requires(email, code_type)
        except EmailDoesNotExist:   # 这些情况不发送邮件
            return
        except EmailAlreadyExists:
            send_email_registered_notice_email.delay(email)
            return
        # 非登录态检查用户访问权限
        if code_type in (EmailCodeType.NON_LOGIN_PASSWORD_RESET, ):
            u = _user or User.query.filter(User.email == email).first()
            if u:
                try:
                    verify_user_permission(u.id)
                except AuthenticationFailed:
                    raise OperationDenied
        EmailCodeValidator(email, code_type).send()


    @classmethod
    @ns.use_kwargs(dict(
        email=EmailField,
        validate_code=fields.String(required=True),
        code_type=EnumField(EmailCodeType, enum_by_value=True, required=True),
    ))
    def post(cls, **kwargs):
        validate_code = kwargs['validate_code']
        code_type = kwargs['code_type']
        email = kwargs.get('email')

        user, email = cls.check_requires(email, code_type,
                                         [cls.Require.EMAIL_ARG, cls.Require.MAIN_ACCOUNT])
        EmailCodeValidator(email, code_type).validate(validate_code)
        token = new_hex_token(OPERATION_TOKEN_SIZE)
        user_id = user.id if user else 0

        if code_type in [EmailCodeType.MOBILE_BINDING, EmailCodeType.TOTP_BINDING,
                         EmailCodeType.LOGIN_PASSWORD_RESET,
                         EmailCodeType.NON_LOGIN_PASSWORD_RESET
                         ]:  # 兼容2fa只绑定通行密钥但是旧版app操作的用户
            if is_old_app_request(3340, 62):  # app兼容老设备仅绑定通讯密钥用户
                if code_type == EmailCodeType.NON_LOGIN_PASSWORD_RESET:
                    user = User.query.filter(
                        User.email == email
                    ).first()
                if bool(user.web_authn_list) and not bool(user.mobile or user.totp_auth_key):
                    raise InvalidArgument(message=_('由于你绑定了通行密钥，APP端暂不支持验证，请到WEB端设置其他2FA'))
        
        EmailCodeTokenCache(token).set_user_and_email(user_id, email)
        return dict(
            email_code_token=token
        )


@ns.route('/mobile/token')
@respond_with_code
class MobileTokenResource(Resource):

    @classmethod
    def get(cls):
        token = SmsAuthCache.new_auth_token()
        return {'token': token}


@ns.route('/webauthn/list')
@respond_with_code
class WebAuthnList(Resource):

    @classmethod
    @require_login
    def get(cls):
        user: User = g.user
        authn_list = [dict(
            name=i.name,
            user_id=i.user_id,
            credential_id=i.credential_id,
            created_at=i.created_at,
            last_used_at=i.last_used_at,
            transports=i.transports,
        ) for i in user.web_authn_list]
        return authn_list


@ns.route('/webauthn/registration')
@respond_with_code
class WebAuthnRegistration(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        authenticator_attachment=fields.String,
    ))
    def get(cls, **kwargs):
        user: User = g.user
        host_url = WebAuthResource.get_allow_url(user)
        platform = WebAuthn.get_request_platform_source()
        op = WebAuthn.get_registration_option(user,
                                              host_url,
                                              platform,
                                              kwargs.get('authenticator_attachment', 'PLATFORM'),
                                              )
        options_json = json.loads(op)
        options_json["extensions"] = {
            "credProps": True,
        }
        return json_string_success(json.dumps(options_json))

    @classmethod
    @require_email_code
    @ns.use_kwargs(dict(
        credential=fields.Dict(required=True)
    ))
    def post(cls, **kwargs):
        user: User = g.user
        if user.has_2fa:
            require_2fa(MobileCodeType.BIND_WEBAUTHN)(lambda: None)()
        _ = WebAuthResource.get_allow_url(user)
        credential = kwargs['credential']
        platform = WebAuthn.get_request_platform_source()
        auth = WebAuthn.verify_registration(user, credential,
                                            platform,
                                            )
        auth_row = user.set_webauthn(auth)
        OperationLog.add(user.id, OperationLog.Operation.ADD_WEBAUTHN, auth_row.name, get_request_platform())


@ns.route('/webauthn/authentication')
@respond_with_code
class WebAuthResource(Resource):

    class KeyType(Enum):  # 非登录态场景用于各个业务token获取密钥配置
        NO_LOGIN = "not_login"  # 登录时进行2fa验证
        NO_LOGIN_RESET_BY_EMAIL = "non_login_reset_by_email"  # 重置密码验证邮箱
        NO_LOGIN_RESET_BY_SECURITY = "non_login_reset_by_security"  # 重置安全工具验证
        NO_LOGIN_BIND_THIRD_PARTY_ACCOUNT = "non_login_bind_third_party_account"  # 绑定第三方账号

    @classmethod
    def get_user_by_kwarg(cls, kwargs: dict) -> User:
        req_key = kwargs.get('key')
        if req_key == cls.KeyType.NO_LOGIN:
            require_login_operation_token(lambda: None)()
            _user = g.user
        elif req_key == cls.KeyType.NO_LOGIN_RESET_BY_EMAIL:
            cache = EmailCodeTokenCache(kwargs.get('email_code_token'))
            email = cache.get_email()
            if not email:
                raise InvalidArgument
            _user = User.query.filter(User.email == email).first()
            if not _user or _user.is_sub_account:
                raise InvalidArgument
        elif req_key == cls.KeyType.NO_LOGIN_RESET_BY_SECURITY:
            require_security_reset_token(lambda: None)()
            _user = g.auth_user
        elif req_key == cls.KeyType.NO_LOGIN_BIND_THIRD_PARTY_ACCOUNT:
            headers = request.headers
            token = (headers.get('OPERATION_TOKEN')
                 or headers.get('OPERATE_TOKEN')
                 or headers.get('Operate-Token'))
            cache = ThirdPartyAccountOperationTokenCache(token)
            if _id:= cache.read():
                _user = User.query.get(int(_id))
            else:
                raise InvalidArgument
        else:
            _switch_user = get_request_user(allow_none=False)
            _user = g.auth_user
        return _user

    @classmethod
    def get_allow_url(cls, user: User) -> str:
        host_url = get_request_host_url()
        site_url = config['SITE_URL']
        if get_request_platform().is_mobile():
            # 针对app多域名情况，统一当做一个域名处理
            host_url = site_url
        else:
            if WebAuthn._root_domain(host_url) != WebAuthn._root_domain(site_url):
                if user.has_2fa and not bool(user.web_authn_list):
                    raise InvalidArgument(message=_(f"该域名无支持的通行密钥，请选择其他2FA验证或使用支持通行密钥的域名进行验证。"))
                else:
                    raise InvalidArgument(message=_(f"该域名无支持的通行密钥，请使用支持通行密钥的域名进行验证。"))
        return host_url

    @classmethod
    @ns.use_kwargs(dict(
        key=EnumField(KeyType, enum_by_value=True),
        email_code_token=fields.String(),
    ))
    def get(cls, **kwargs):
        """通行密钥鉴权"""
        _user = cls.get_user_by_kwarg(kwargs)
        host_url = cls.get_allow_url(_user)
        platform = WebAuthn.get_request_platform_source()
        op = WebAuthn.get_authentication_option(_user,
                                                host_url,
                                                platform,
                                                )
        return json_string_success(op)

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        credential=fields.Dict(required=True)
    ))
    def post(cls, **kwargs):
        user = g.user
        _ = WebAuthResource.get_allow_url(user)
        credential = kwargs['credential']
        platform = WebAuthn.get_request_platform_source()
        WebAuthn.verify_authentication(user, credential, platform)
        UserPreferences(user.id).two_fa_type = TwoFAType.WEBAUTHN
        token = new_hex_token(OPERATION_TOKEN_SIZE)
        UserOperationTokenCache(token).set_user(user.id, TwoFAType.WEBAUTHN)
        update_security_statistics([user.id], SecuritySettingType.WEBAUTHN)
        return dict(
            operate_token=token
        )


@ns.route('/webauthn/rename')
@respond_with_code
class WebAuthRenameResource(Resource):

    @classmethod
    @require_login()
    @ns.use_kwargs(dict(
        name=fields.String(required=True, validate=max_length_validator(50)),
        credential_id=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        user = g.user
        auth = UserWebAuthn.get_auth_by_user_id_credential_id(user.id, kwargs['credential_id'])
        auth.name = kwargs['name']
        db.session.commit()
        OperationLog.add(user.id, OperationLog.Operation.EDIT_WEBAUTHN_NAME, auth.name, get_request_platform())


@ns.route('/webauthn/unbind')
@respond_with_code
class WebAuthUnbindResource(Resource):

    @classmethod
    @require_email_code
    @require_2fa(MobileCodeType.UNBIND_WEBAUTHN)
    @ns.use_kwargs(dict(
        credential_id=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        #  有可能出现删除的同时鉴权成功，但鉴权本身大概率代表本人，所以似乎可以容忍？
        user = g.user
        auth = UserWebAuthn.get_auth_by_user_id_credential_id(user.id, kwargs['credential_id'])
        auth.status = UserWebAuthn.Status.DELETED
        db.session.commit()
        user.suspend_withdrawal_after_security_reset()
        update_user_two_fa_type(user)
        OperationLog.add(user.id, OperationLog.Operation.DELETE_WEBAUTHN, auth.name, get_request_platform())


@ns.route('/mobile/code')
@respond_with_code
class MobileCodeResource(Resource):

    class Require(Enum):
        MOBILE_ARG = 'mobile_arg'
        LOGIN = 'login'
        MAIN_ACCOUNT = 'main_account'
        LOGIN_OPERATION_TOKEN = 'login_operation_token'
        SECURITY_RESET_TOKEN = 'security_reset_token'
        GEETEST = 'geetest'

    requires = {
        Require.MOBILE_ARG: [
            MobileCodeType.SECURITY_RESET,
            MobileCodeType.MOBILE_BINDING,
            MobileCodeType.MOBILE_RESET,
            MobileCodeType.NON_LOGIN_PASSWORD_RESET,
            MobileCodeType.NON_LOGIN_BIND_THIRD_PARTY_ACCOUNT
        ],
        Require.LOGIN: [
            MobileCodeType.API_AUTH_ADDITION,
            MobileCodeType.API_AUTH_EDIT,
            MobileCodeType.API_AUTH_QUERY,
            MobileCodeType.API_AUTH_DELETION,
            MobileCodeType.API_AUTH_EXTEND,
            MobileCodeType.ORDER_PLACEMENT,
        ],
        Require.MAIN_ACCOUNT: [
            MobileCodeType.LOGIN_PASSWORD_EDIT,
            MobileCodeType.LOGIN_PASSWORD_SET,
            MobileCodeType.MOBILE_BINDING,
            MobileCodeType.MOBILE_EDIT,
            MobileCodeType.MOBILE_RESET,
            MobileCodeType.MOBILE_UNBIND,
            MobileCodeType.TOTP_BINDING,
            MobileCodeType.TOTP_EDIT,
            MobileCodeType.TOTP_UNBIND,
            MobileCodeType.EMAIL_BINDING,
            MobileCodeType.EMAIL_EDIT,
            MobileCodeType.WITHDRAWAL_ADDRESS_ADDITION,
            MobileCodeType.API_WITHDRAWAL_ADDRESS_ADDITION,
            MobileCodeType.WITHDRAWAL_APPLICATION,
            MobileCodeType.TRADING_PASSWORD_ADDITION,
            MobileCodeType.TRADING_PASSWORD_EDIT,
            MobileCodeType.VIP_PURCHASE,
            MobileCodeType.FLAT_COIN,
            MobileCodeType.SUB_ACCOUNT_REGISTRATION,
            MobileCodeType.SUB_ACCOUNT_PASSWORD_RESET,
            MobileCodeType.SUB_ACCOUNT_DELETION,
            MobileCodeType.SUB_ACCOUNT_BIND_MANAGER,
            MobileCodeType.SEND_C_BOX,
            MobileCodeType.ADD_WITHDRAWAL_APPROVER,
            MobileCodeType.DELETE_WITHDRAWAL_APPROVER,
            MobileCodeType.SIGN_OFF,
            MobileCodeType.ADD_WITHDRAW_PASSWORD,
            MobileCodeType.EDIT_WITHDRAW_PASSWORD,
            MobileCodeType.RESET_WITHDRAW_PASSWORD,
            MobileCodeType.BIND_WEBAUTHN,
            MobileCodeType.UNBIND_WEBAUTHN,
            MobileCodeType.P2P_FINISH,
            MobileCodeType.P2P_ADD_USER_PAY_CHANNEL,
            MobileCodeType.P2P_EDIT_USER_PAY_CHANNEL,
            MobileCodeType.P2P_DELETE_USER_PAY_CHANNEL,
            MobileCodeType.TRADING_PASSWORD_ADDITION,
            MobileCodeType.TRADING_PASSWORD_EDIT,
            MobileCodeType.TRADING_PASSWORD_REMOVE,
            MobileCodeType.BIND_THIRD_PARTY_ACCOUNT
        ],
        Require.LOGIN_OPERATION_TOKEN: [
            MobileCodeType.LOGIN_WITH_OPERATION_TOKEN
        ],
        Require.SECURITY_RESET_TOKEN: [
            MobileCodeType.SECURITY_RESET
        ],
        Require.GEETEST: [
            MobileCodeType.MOBILE_BINDING,
            MobileCodeType.SECURITY_RESET,
            MobileCodeType.NON_LOGIN_PASSWORD_RESET,
            MobileCodeType.NON_LOGIN_BIND_THIRD_PARTY_ACCOUNT
        ],
    }

    @classmethod
    def check_requires(cls, country_code: Optional[int], mobile: Optional[str],
                       code_type: MobileCodeType, reqs: List[Require] = None) -> Tuple[Optional[User], int, str, bool]:
        _country_code = _mobile = _user = None
        if not reqs:
            reqs = cls.Require
        silent = False
        for key in reqs:
            if code_type not in cls.requires.get(key, []):
                continue
            if key == cls.Require.MOBILE_ARG:
                if not country_code or not mobile:
                    raise InvalidArgument
                _country_code = country_code
                _mobile = mobile
            elif key == cls.Require.LOGIN:
                _user = get_request_auth_user(allow_none=False)
            elif key == cls.Require.MAIN_ACCOUNT:
                _switch_user = get_request_user(allow_none=False)
                _user = g.auth_user
                if _switch_user.id != _user.id:
                    raise InvalidArgument
            elif key == cls.Require.LOGIN_OPERATION_TOKEN:
                require_login_operation_token(lambda: None)()
                _user = g.auth_user
            elif key == cls.Require.SECURITY_RESET_TOKEN:
                require_security_reset_token(lambda: None)()
                if mobile and country_code:
                    param_mobile = f'+{country_code}{mobile}'
                    silent = param_mobile != g.user.mobile
                _user = g.auth_user
            elif key == cls.Require.GEETEST:
                platform = get_request_platform()
                if not platform.is_mobile():
                    require_geetest(lambda: None)()

        _country_code = _country_code or (_user and _user.mobile_country_code)
        _mobile = _mobile or (_user and _user.mobile_num)
        if not _country_code or not _mobile:
            raise InvalidArgument
        return _user, _country_code, _mobile, silent

    @classmethod
    @ns.use_kwargs(dict(
        country_code=fields.Integer,
        mobile=fields.String,
        code_type=EnumField(MobileCodeType, enum_by_value=True, required=True),
        token=fields.String,
        sig=fields.String
    ))
    def get(cls, **kwargs):
        country_code = kwargs.get('country_code')
        mobile = kwargs.get('mobile')
        code_type = kwargs['code_type']
        token = kwargs.get('token')
        sig = kwargs.get('sig')
        if not is_old_app_request(3210, 16):
            if not token or not sig:
                raise InvalidArgument
        if token and not SmsAuthCache.validate_signature(token, code_type.value, sig):
            raise InvalidArgument
        # 针对web平台专门增加校验，应对短信刷量
        if get_request_platform().is_web():
            if not SmsAuthCache.self_check_signature(sig):
                current_app.logger.warning('ignore send sms code: %s, %s', country_code, code_type)
                return

        user, country_code, mobile, silent = cls.check_requires(country_code, mobile, code_type)
        
        full_phone_number = f'+{country_code}{mobile}'
        region_code = get_mobile_country(full_phone_number)
        country = get_country(region_code)
        if code_type in (MobileCodeType.MOBILE_BINDING, MobileCodeType.MOBILE_RESET):
            if country and not CountrySettings(country.iso_3).allow_binding_mobile:
                raise NotSupportedByCountryInOnlyWithdrawal
        if user:
            g.lang = UserPreferences(user.id).language.value
        if silent:
            return
        if not user:
            user = User.query.filter(User.mobile_country_code == country_code,
                                     User.mobile_num == mobile).first()
        validator = MobileCodeValidator(country_code, mobile, code_type, user)
        if validator.send() is not None:
            db.session.add(MobileMessageRecordMySQL(
                user_id=user.id if user else None,
                mobile_country_code=str(country_code),
                mobile_num=mobile,
                country=country.iso_3 if country else '',
                business=code_type.name,
                user_registered_at=user.created_at if user else None,
                has_user_binded_mobile=bool(user and user.mobile),
                ip_address=get_request_ip(),
            ))
            db.session.commit()

        user_ids = [user.id] if user else []
        update_security_statistics(user_ids, SecuritySettingType.MOBILE)
        return dict(provider=validator.get_estimated_sms_provider_name())

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        validate_code=fields.String(required=True),
        code_type=EnumField(MobileCodeType, enum_by_value=True, required=True)
    ))
    def post(cls, **kwargs):
        validate_code = kwargs['validate_code']
        code_type = kwargs['code_type']
        # 该接口用于二次验证，因此只允许登录态
        user, country_code, mobile, _ = cls.check_requires(None, None, code_type,
                                                        [cls.Require.LOGIN, cls.Require.MAIN_ACCOUNT])
        MobileCodeValidator(country_code, mobile, code_type).validate(validate_code)
        UserPreferences(user.id).two_fa_type = TwoFAType.MOBILE
        token = new_hex_token(OPERATION_TOKEN_SIZE)
        UserOperationTokenCache(token).set_user(user.id, TwoFAType.MOBILE)
        return dict(
            operate_token=token
        )


@ns.route('/totp/code')
@respond_with_code
class TotpCodeResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        validate_code=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        user: User = g.auth_user
        TotpCodeValidator(user).validate(kwargs['validate_code'])
        UserPreferences(user.id).two_fa_type = TwoFAType.TOTP
        operation_token = new_hex_token(OPERATION_TOKEN_SIZE)
        UserOperationTokenCache(operation_token).set_user(user.id, TwoFAType.TOTP)
        update_security_statistics([user.id], SecuritySettingType.TOTP)
        return dict(
            operate_token=operation_token
        )


@ns.route('/qrcode/sign')
@respond_with_code
class QrCodeSignResource(Resource):

    @classmethod
    def get(cls):
        url = get_request_host_url()
        sig = sign_qrcode_url(url)
        return {'s': sig}


@ns.route('/<string:name>')
@respond_with_code
class ThirdPartyAuthURLResource(Resource):

    @classmethod
    def get(cls, name):
        name = name.lower()
        if name not in get_oauth_client_names():
            raise InvalidArgument
        client = get_oauth_client(name)
        return dict(url=client.get_auth_url())
