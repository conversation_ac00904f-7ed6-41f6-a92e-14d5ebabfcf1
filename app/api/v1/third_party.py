# -*- coding: utf-8 -*-

from decimal import Decimal
import json
from flask import request
import requests

from webargs import fields
from app import config
from app.business.clients.server import ServerClient
from app.business.external_dbs import PerpetualHistoryDB
from app.caches.spot import MarketCache, MarketStatusCache
from app.common.constants import PerpetualMarketType
from app.models.user import VipUser
from app.utils import offset_to_page
from app.utils.amount import amount_to_str

from ..common import Namespace, Resource, respond_with_code
from ...business import (PerpetualServerClient, PerpetualMarketCache, PriceManager)
from ...caches import PerpetualMarketStatisticsCache
from ...exceptions import InvalidArgument, ServiceUnavailable
from ...models import PerpetualMarket
from ...models.spot import SystemAssetLiability
from ...utils import current_timestamp, strip_non_alpha_num, datetime_to_time


ns = Namespace('Third Party')
url_prefix = '/third_party'

SPOT_DEPTH_VALUES = [1, 5, 10, 20, 30, 50]


# 暂时使用v1接口，后面替换成其他实现
def get_all_market_tickers() -> dict[str, dict]:
    r = requests.get('https://api.coinex.com/v1/market/ticker/all', timeout=9)
    if r.status_code != 200:
        raise ServiceUnavailable
    r = r.json()
    if r['code'] != 0:
        raise ServiceUnavailable
    return r['data']['ticker']


@ns.route('/perpetual/info')
@respond_with_code
class PerpetualInfoResource(Resource):

    @classmethod
    def get(cls):
        """
        CMC: 合约市场信息
        """
        data = PerpetualMarketStatisticsCache().read()
        if not data:
            return
        return json.loads(data)


@ns.route('/perpetual/list')
@respond_with_code
class PerpetualListResource(Resource):

    @classmethod
    def get(cls):
        """
        CoinGecko: 合约市场信息
        """
        data = PerpetualMarketStatisticsCache().read()
        if not data:
            return
        ticker_list = json.loads(data)
        # 做一些字段处理
        for item in ticker_list:
            item['target_currency'] = item['quote_currency']
            item['target_volume'] = item['quote_volume']
            item['index_currency'] = item['quote_currency']
            del item['quote_currency']
            del item['quote_volume']
        return ticker_list


@ns.route('/perpetual/orderbook')
@respond_with_code
class PerpetualMarketDepthResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(required=True),
        limit=fields.Integer(missing=100)
    ))
    def get(cls, **kwargs):
        market_name = strip_non_alpha_num(kwargs['market'])
        market = PerpetualMarket.query.filter(PerpetualMarket.name == market_name).first()
        if not market:
            raise InvalidArgument
        market_info = PerpetualMarketCache().get_market_info(market_name)
        if not market_info:
            return
        client = PerpetualServerClient()
        depth = client.market_order_depth(market=market_name,
                                          limit=kwargs['limit'], interval='0')
        return dict(
            ticker_id=f'{market_info["stock"]}-{market_info["money"]}',
            timestamp=current_timestamp(to_int=True) * 1000,
            bids=depth['bids'],
            asks=depth['asks']
        )


@ns.route('/perpetual/liquidation')
@respond_with_code
class PerpetualLiquidationResource(Resource):


    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(required=True),
        start_time=fields.Integer(missing=0),
        end_time=fields.Integer(missing=0),
        page=fields.Integer(missing=1),
        limit=fields.Integer(validate=lambda x: 0 <= x <= 500, missing=500)
    ))
    def get(cls, **kwargs):
        market_name = strip_non_alpha_num(kwargs['market'])
        market_info = PerpetualMarketCache().get_market_info(market_name)
        if not market_info:
            raise InvalidArgument
        page, limit = kwargs['page'], kwargs['limit']
        start_time, end_time = kwargs['start_time'], kwargs['end_time']
        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_liq_finished_order(
            market_name, start_time, end_time, offset, limit + 1
        )
        res = []
        market_type = market_info.get('type')
        for record in records:
            real_amount = record['amount'] - record['left']
            deal_stock = record['deal_stock']
            if real_amount == 0 or deal_stock == 0:   # 如果有一个为0，则直接取记录的price字段
                price = record['price']
            else:
                if market_type == PerpetualMarketType.INVERSE:    # 反向合约
                    price = real_amount / deal_stock
                else:
                    price = deal_stock / real_amount
            item = {
                'create_time': int(record['create_time']),
                'amount': amount_to_str(real_amount, 8),
                'price': amount_to_str(price, 8),
                'side': record['side']
            }
            res.append(item)
        result = {
            'limit': limit + 1,
            'offset': offset,
            'records': res
        }
        return offset_to_page(result)


@ns.route('/spot/summary')
@respond_with_code
class SpotMarketSummaryResource(Resource):

    @classmethod
    def get(cls):
        """
        CMC: 现货市场汇总信息
        """
        market_info_map = MarketCache.online_markets_detail()
        all_ticker_map = get_all_market_tickers()
        cache_data = {name: json.loads(detail)
                      for name, detail in MarketStatusCache().hgetall().items()}
        tickers = []
        for market, info in market_info_map.items():
            if market not in cache_data or market not in all_ticker_map:
                continue
            cache_info, ticker_info = cache_data[market], all_ticker_map[market]
            if Decimal(ticker_info['open']) == 0:
                price_change_percent = '0'
            else:
                price_change_percent = amount_to_str(
                    (Decimal(ticker_info['last']) - Decimal(ticker_info['open']))
                    / Decimal(ticker_info['open']), 8)

            tickers.append(dict(
                trading_pairs=f'{info["base_asset"]}_{info["quote_asset"]}',
                base_currency=info['base_asset'],
                quote_currency=info['quote_asset'],
                last_price=ticker_info['last'],
                lowest_ask=ticker_info['buy'],
                highest_bid=ticker_info['sell'],
                base_volume=cache_info['vol'],
                quote_volume=cache_info['deal'],
                price_change_percent_24h=price_change_percent,
                highest_price_24h=ticker_info['high'],
                lowest_price_24h=ticker_info['low'],
            ))
        return tickers


@ns.route('/spot/ticker')
@respond_with_code
class SpotTickerResource(Resource):

    @classmethod
    def get(cls):
        """
        CMC: 现货市场交易数据
        """
        res = {}
        cache_data = {name: json.loads(detail)
                      for name, detail in MarketStatusCache().hgetall().items()}
        all_ticker_map = get_all_market_tickers()
        market_info_map = MarketCache.online_markets_detail()
        for market, info in market_info_map.items():
            if market not in all_ticker_map or market not in cache_data:
                continue
            ticker_info, cache_info = all_ticker_map[market], cache_data[market]
            res[f'{info["base_asset"]}_{info["quote_asset"]}'] = \
                dict(
                    last_price=ticker_info['last'],
                    base_volume=cache_info['vol'],
                    quote_volume=cache_info['deal'],
            )
        return res


@ns.route('/spot/orderbook')
@respond_with_code
class SpotOrderBookResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market_pair=fields.String(required=True),
        depth=fields.Integer(validate=lambda x: x in SPOT_DEPTH_VALUES,
                             missing=SPOT_DEPTH_VALUES[-1],
                             error_messages={"validator_failed":
                                             f"Invalid depth, possible values: {SPOT_DEPTH_VALUES}"}
                             )

    ))
    def get(cls, **kwargs):
        """
        CMC: 现货市场订单簿
        """
        market_pair = kwargs['market_pair']
        market = strip_non_alpha_num(market_pair)
        if market not in MarketCache.list_online_markets():
            raise InvalidArgument
        market_info = MarketCache(market).dict
        client = ServerClient()
        depths = client.market_order_depth(market=market,
                                           limit=kwargs['depth'],
                                           interval=str(min(market_info['depths'])))
        return dict(
            timestamp=depths.get('time', int(current_timestamp() * 1000)),
            asks=depths['asks'],
            bids=depths['bids']
        )


@ns.route('/spot/trades')
@respond_with_code
class SpotMarketTradesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market_pair=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        """
        CMC: 现货交易订单信息
        """
        market_pair = kwargs['market_pair']
        market = strip_non_alpha_num(market_pair)
        if market not in MarketCache.list_online_markets():
            raise InvalidArgument

        market_info = MarketCache(market).dict
        quote_precision = market_info['quote_asset_precision']
        client = ServerClient()
        r = client.market_deals(market=market, limit=1000, last_id=0)
        res = []
        for item in r:
            res.append(dict(
                trade_id=item['id'],
                price=item['price'],
                base_volume=item['amount'],
                quote_volume=amount_to_str(Decimal(item['amount']) * Decimal(item['price']),
                                           quote_precision),
                timestamp=int(item['time']) * 1000,
                type=item['type']
            ))
        return res


@ns.route('/spot/pairs')
@respond_with_code
class SpotMarketPairsResource(Resource):

    @classmethod
    def get(cls):
        """
        CoinGecko: 现货交易对币种信息
        """
        market_info_map = MarketCache.online_markets_detail()
        res = []
        for v in market_info_map.values():
            res.append(dict(
                ticker_id=f'{v["base_asset"]}_{v["quote_asset"]}',
                base=v['base_asset'],
                target=v['quote_asset'],
            ))
        return res


@ns.route('/spot/market_info')
@respond_with_code
class SpotMarketInfoResource(Resource):

    @classmethod
    def get(cls):
        """
        CoinGecko: 现货市场汇总信息
        """
        market_info_map = MarketCache.online_markets_detail()
        cache_data = {name: json.loads(detail)
                      for name, detail in MarketStatusCache().hgetall().items()}
        all_ticker_map = get_all_market_tickers()
        tickers = []
        for market, info in market_info_map.items():
            if market not in all_ticker_map or market not in cache_data:
                continue
            ticker_info, cache_info = all_ticker_map[market], cache_data[market]
            tickers.append(dict(
                ticker_id=f'{info["base_asset"]}_{info["quote_asset"]}',
                base_currency=info['base_asset'],
                target_currency=info['quote_asset'],
                last_price=ticker_info['last'],
                base_volume=cache_info['vol'],
                target_volume=cache_info['deal'],
                bid=ticker_info['sell'],
                ask=ticker_info['buy'],
                high=ticker_info['high'],
                low=ticker_info['low'],
            ))
        return tickers


@ns.route('/spot/orders')
@respond_with_code
class SpotOrdersResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ticker_id=fields.String(required=True),
        depth=fields.Integer(validate=lambda x: x in SPOT_DEPTH_VALUES,
                             missing=SPOT_DEPTH_VALUES[-1],
                             error_messages={"validator_failed":
                                             f"Invalid depth, possible values: {SPOT_DEPTH_VALUES}"}
                             )
    ))
    def get(cls, **kwargs):
        """
        CoinGecko: 现货市场订单簿
        """
        market_pair = kwargs['ticker_id']
        market = strip_non_alpha_num(market_pair)
        if market not in MarketCache.list_online_markets():
            raise InvalidArgument
        market_info = MarketCache(market).dict
        client = ServerClient()
        depths = client.market_order_depth(market=market,
                                           limit=kwargs['depth'],
                                           interval=str(min(market_info['depths'])))
        return dict(
            timestamp=depths.get('time', int(current_timestamp() * 1000)),
            ticker_id=f'{market_info["base_asset"]}_{market_info["quote_asset"]}',
            asks=depths['asks'],
            bids=depths['bids']
        )


@ns.route('/spot/historical_trades')
@respond_with_code
class SpotMarketHistoricalTradesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ticker_id=fields.String(required=True),
        limit=fields.Integer(validate=lambda x: 0 <= x <= 500, missing=500)
    ))
    def get(cls, **kwargs):
        """
        CoinGecko: 现货历史订单信息
        """
        market_pair = kwargs['ticker_id']
        market = strip_non_alpha_num(market_pair)
        if market not in MarketCache.list_online_markets():
            raise InvalidArgument

        market_info = MarketCache(market).dict
        quote_precision = market_info['quote_asset_precision']
        client = ServerClient()
        limit = kwargs['limit']

        r = client.market_deals(market=market, limit=limit, last_id=0)
        res = {
            'buy': [],
            'sell': [],
        }
        for item in r:
            res[item['type']].append(dict(
                trade_id=item['id'],
                price=item['price'],
                base_volume=item['amount'],
                target_volume=amount_to_str(Decimal(item['amount']) * Decimal(item['price']),
                                            quote_precision),
                trade_timestamp=int(item['time']) * 1000,
                type=item['type']
            ))
        return res


@ns.route('/zendesk/user')
class ZendeskUserInfoResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        external_id=fields.String)
    )
    def get(cls, **kwargs):
        token = request.headers.get("Authorization", "")
        ret_value = dict(
            can_use_manual_service=0,
            level=0
        )
        if token != config['ZENDESK_BOT_CONFIG']['token']:
            return ret_value
        user_id: str = kwargs.get("external_id")
        if not user_id:
            return ret_value
        prefix = config['ZENDESK_BOT_CONFIG']['id_prefix']
        user_id = user_id.lstrip(prefix)
        if not str(user_id).isdigit():
            return ret_value
        user_id: int = int(user_id)
        record = VipUser.query.filter(
            VipUser.user_id == user_id,
            VipUser.status == VipUser.StatusType.PASS
        ).first()
        can_use_manual_service = bool(record and record.level >= 4)
        return dict(
            can_use_manual_service=int(can_use_manual_service),
            level=record.level if record else 0
        )


@ns.route('/wallet/balance')
@respond_with_code
class WalletBalanceResource(Resource):

    @classmethod
    def get(cls):
        """
        CoinGlass: BTC钱包余额
        """
        interval = 1800
        asset = 'BTC'
        limit = 86400 / interval * 30 + 100
        rows = SystemAssetLiability.query.filter(
            SystemAssetLiability.asset == asset
            ).order_by(SystemAssetLiability.id.desc()) \
            .with_entities(SystemAssetLiability.created_at, SystemAssetLiability.hot_wallet,
                           SystemAssetLiability.deposit_wallet, SystemAssetLiability.cold_wallet) \
            .limit(limit).all()
        if not rows:
            return {
                'asset': asset,
                'balance': '0',
                'change_1h': '0',
                'change_24h': '0',
                'change_7d': '0',
                'change_30d': '0'
            }
        curr = rows[0]

        ts = datetime_to_time(curr.created_at)
        ts -= ts % interval
        _1h = ts - 3600
        _24h = ts - 86400
        _7d = ts - 86400 * 7
        _30d = ts - 86400 * 30
        _1h_row = None
        _24h_row = None
        _7d_row = None
        _30d_row = None

        for row in rows[::-1]:
            _t = datetime_to_time(row.created_at)
            if not _30d_row:
                if _t >= _30d:
                    _30d_row = row
            if not _7d_row:
                if _t >= _7d:
                    _7d_row = row
            if not _24h_row:
                if _t >= _24h:
                    _24h_row = row
            if not _1h_row:
                if _t >= _1h:
                    _1h_row = row

        curr_balance = curr.hot_wallet + curr.deposit_wallet + curr.cold_wallet
        result = {
            'asset': asset,
            'balance': curr_balance,
            'change_1h': curr_balance - (_1h_row.hot_wallet + _1h_row.deposit_wallet + _1h_row.cold_wallet) if _1h_row else '0',
            'change_24h': curr_balance - (_24h_row.hot_wallet + _24h_row.deposit_wallet + _24h_row.cold_wallet) if _24h_row else '0',
            'change_7d': curr_balance - (_7d_row.hot_wallet + _7d_row.deposit_wallet + _7d_row.cold_wallet) if _7d_row else '0',
            'change_30d': curr_balance - (_30d_row.hot_wallet + _30d_row.deposit_wallet + _30d_row.cold_wallet) if _30d_row else '0'
        }
        return result


@ns.route('/currency/rate')
@respond_with_code
class CurrencyRateResource(Resource):

    ALLOWED_PAIRS = {('USD', 'USDT')}

    @classmethod
    @ns.use_kwargs(dict(
        from_asset=fields.String(required=True),
        to_asset=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        from_asset, to_asset = kwargs['from_asset'], kwargs['to_asset']
        if (from_asset,  to_asset) not in cls.ALLOWED_PAIRS:
            raise InvalidArgument
        return dict(
            rate=PriceManager.convert_price(from_asset, 1, to_asset)
        )