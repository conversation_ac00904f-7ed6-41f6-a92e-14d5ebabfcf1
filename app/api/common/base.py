# -*- coding: utf-8 -*-
import json
from traceback import format_exc

from flask import make_response, jsonify, request, Response, current_app
from flask_restx import (Api as _Api, Resource as _Resource,
                         Namespace as _Namespace)
from flask_api.exceptions import APIException
from webargs.flaskparser import use_kwargs, parser
from marshmallow import fields as mm_fields
from marshmallow.utils import EXCLUDE
from werkzeug.exceptions import HTTPException

from .request import get_request_info
from ...business.api_v2 import convert_response_code
from ...exceptions import ErrorWithResponseCode, InvalidArgument, \
    ServiceUnavailable
from ...models import db, row_to_dict
from ...utils.parser import JsonEncoder as _JsonEncoder, dict2schema
from .responses import failure


# noinspection PyUnusedLocal
@parser.error_handler
def handle_request_parsing_error(err, req, schema,
                                 *, error_status_code, error_headers):
    raise InvalidArgument(err.messages)


class Api(_Api):

    # disable swagger
    def _register_apidoc(self, app):
        pass

    def _register_specs(self, app):
        pass

    def _register_doc(self, app):
        pass

    def handle_error(self, e):
        if 'multipart/form-data' in (request.content_type or ''):
            # 使用uwsgi_socket时，如果请求中的文件数据（multipart/form-data）不被读取就返回，
            # 会导致socket悬挂在读取状态，uWSGI 提前断开连接
            _ = request.files

        if isinstance(e, APIException):
            return make_response(jsonify(dict(
                message=e.detail
            )), e.status_code)
        if isinstance(e, ErrorWithResponseCode):
            return Response(json.dumps(
                failure(e.response_code, e.message, e.data),
                cls=ApiJsonEncoder
            ), mimetype='application/json')
        elif isinstance(e, HTTPException):
            return Response(json.dumps(
                failure(e.code, e.description),
                cls=ApiJsonEncoder
            ), status=e.code, mimetype='application/json')
        else:
            req = get_request_info()
            msg = ' '.join([f'{k}: {v}' for k, v in req.items()])
            current_app.logger.error(f"{msg} {format_exc()}", extra={'method': req['method'], 'path': req['path']})
            return Response(json.dumps(
                failure(ServiceUnavailable.response_code,
                        ServiceUnavailable.message_template,
                        {}),
                cls=ApiJsonEncoder
            ), status=500, mimetype='application/json')


class ApiV2Api(_Api):
    def handle_error(self, e):
        if isinstance(e, APIException):
            return make_response(jsonify(dict(
                message=e.detail
            )), e.status_code)
        if isinstance(e, ErrorWithResponseCode):
            return Response(json.dumps(
                failure(convert_response_code(e.response_code), e.message, e.data),
                cls=ApiJsonEncoder
            ), mimetype='application/json')
        elif isinstance(e, HTTPException):
            return Response(json.dumps(
                failure(e.code, e.description),
                cls=ApiJsonEncoder
            ), status=e.code, mimetype='application/json')
        else:
            req = get_request_info()
            msg = ' '.join([f'{k}: {v}' for k, v in req.items()])
            current_app.logger.error(f"{msg} {format_exc()}", extra={'method': req['method'], 'path': req['path']})
            return Response(json.dumps(
                failure(convert_response_code(ServiceUnavailable.response_code),
                        ServiceUnavailable.message_template,
                        {}),
                cls=ApiJsonEncoder
            ), status=500, mimetype='application/json')


Resource = _Resource


class Namespace(_Namespace):

    _METHODS_WITH_BODY = frozenset(['post', 'put', 'patch'])

    def use_kwargs(self, fields):
        def validate_field(_f):
            if isinstance(_f, mm_fields.Field):
                return _f
            if isinstance(_f, type) and issubclass(_f, mm_fields.Field):
                return _f()
            raise TypeError(f'invalid field: {_f}')

        fields = {key: validate_field(field) for key, field in fields.items()}

        def dec(func):
            if func.__name__ in self._METHODS_WITH_BODY:
                location = 'json'
            else:
                location = 'query'

            argmap = dict2schema(fields, schema_class=parser.schema_class)(unknown=EXCLUDE)
            return use_kwargs(argmap, location=location)(func)

        return dec


class ApiJsonEncoder(_JsonEncoder):
    def default(self, obj):
        if isinstance(obj, db.Model):
            return row_to_dict(obj, enum_to_name=True)
        return super().default(obj)
