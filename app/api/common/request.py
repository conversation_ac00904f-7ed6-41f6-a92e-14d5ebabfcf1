# -*- coding: utf-8 -*-

from enum import Enum
from itertools import chain as chain_iter
from datetime import tzinfo, timedelta, timezone
from decimal import Decimal
from json import loads as json_loads
from typing import Callable, Optional, Union, Any

from flask import request, g, current_app
from flask_api.exceptions import AuthenticationFailed
from user_agents.parsers import parse as parse_user_agent, UserAgent


from app.exceptions import SubAccountNotAllowed, AuthenticationTimeout, WebauthnVerificationFailed
from app.exceptions.legacy import AccessIdDoesNotExist
from app.models.system import LocalAreasBlock
from ...caches.admin import (
    AdminUserOperationTokenCache, AdminUserWebAuthnOperationTokenCache,
    AdminWebAuthnOperationType,
)

from ...caches.system import SystemMaintainManagerCache
from ...exceptions import (
    InvalidPlatform, InvalidMobileVerificationCode,
    InvalidTotpVerificationCode, TwoFactorAuthenticationFailed,
    EmailCodeVerificationFailed, ServerInMaintainMode,
    EmailAlreadyExists, EmailDoesNotExist, NotSupportedByCountryInOnlyWithdrawal
)
from ...common import Language, TwoFAType, CAPTCHA_VALIDATION_LIMIT, MobileCodeType
from .validators import MobileCodeValidator, TotpCodeValidator, WebauthnCredentialValidator
from ...models import User
from ...caches import EmailCodeTokenCache, UserOperationTokenCache
from ...utils import current_timestamp, GeoIP
from ...business import UserPreferences, CountrySettings
from ...business.clients import monitor_client


class RequestPlatform(Enum):

    WEB = 'web'
    IOS = 'iOS'
    IOS_APP_STORE = 'iOS_appstore'
    ANDROID = 'Android'
    ANDROID_GOOGLE_PLAY = 'Android_GooglePlay'
    # 原先的APP也被下架了，重新添加两个字段区分上架
    IOS_LITE = 'iOSLite'
    IOS_LITE_APP_STORE = 'iOSLite_appstore'

    def is_web(self):
        return self is self.WEB

    def is_ios(self):
        return self in [self.IOS, self.IOS_APP_STORE, self.IOS_LITE, self.IOS_LITE_APP_STORE]

    def is_android(self):
        return self in [self.ANDROID, self.ANDROID_GOOGLE_PLAY]

    def is_mobile(self):
        return self.is_ios() or self.is_android()

    @classmethod
    def app_list(cls):
        return [cls.IOS, cls.IOS_APP_STORE, cls.IOS_LITE, cls.IOS_LITE_APP_STORE, cls.ANDROID, cls.ANDROID_GOOGLE_PLAY]

    @classmethod
    def from_request(cls) -> 'RequestPlatform':
        platform = request.headers.get('PLATFORM', '')
        if platform:
            try:
                platform = RequestPlatform(platform)
            except ValueError:
                raise InvalidPlatform(platform)
        else:
            platform = cls.WEB
        return platform


def get_request_info() -> dict:
    return dict(
        path=request.path,
        method=request.method,
        ip=get_request_ip(),
        platform=get_request_platform().name,
        build=get_request_build(),
        language=get_request_language().name,
        args=dict(request.args)
    )


def get_request_ip() -> str:
    headers = request.headers
    if 'Cf-Connecting-Ip' in headers:     # 来自CloudFlare
        return headers['Cf-Connecting-IP']
    if 'CloudFront-Viewer-Address' in headers:     # 来自AWS CloudFront
        return headers['CloudFront-Viewer-Address'].split(':')[0]
    if 'X-Real-IP' in headers:            # 来自nginx设置
        return headers['X-Real-IP']
    if 'X-Forwarded-For' in headers:      # 来自api_gateway等设置
        return headers['X-Forwarded-For'].split(',')[0]
    return request.remote_addr


def get_request_platform() -> RequestPlatform:
    return RequestPlatform.from_request()


def get_request_build() -> int:
    return int(request.headers.get('BUILD', 0))


def get_request_version() -> str:
    return request.headers.get('version', '')


def get_request_host_url():
    return request.host_url


def get_request_language(use_default_val: bool = True) -> Union[Language, None]:
    for lang in chain_iter(request.accept_languages.values(),
                           [request.cookies.get('lang')]):
        if not lang:
            continue
        try:
            lang = Language(lang)
        except ValueError:
            continue
        break
    else:
        if use_default_val:
            lang = Language.DEFAULT
        else:
            lang = None
    return lang


def get_request_timezone() -> Optional[tzinfo]:
    if (tz := request.headers.get('timezone')) is None:
        return None
    return timezone(timedelta(minutes=int(Decimal(tz) * 60)))


def get_request_user_agent() -> str:
    return request.headers.get('User-Agent', '')


def get_request_user_agent_parsed() -> UserAgent:
    return parse_user_agent(get_request_user_agent())


def get_request_user(allow_none=True, allow_sub_account=True) -> Optional[User]:
    from .decorators import require_login

    try:
        return require_login(lambda: g.user, allow_sub_account=allow_sub_account)()
    except (AuthenticationFailed, SubAccountNotAllowed):
        if not allow_none:
            raise
    return None


def get_api_request_user(allow_none=True, allow_sub_account=True) -> Optional[User]:
    from .decorators import require_api_v2_auth

    try:
        return require_api_v2_auth(lambda: g.user, allow_sub_account=allow_sub_account)()
    except (AccessIdDoesNotExist, SubAccountNotAllowed):
        if not allow_none:
            raise
    return None


def get_request_auth_user(allow_none=True) -> Optional[User]:
    from .decorators import require_login

    try:
        return require_login(lambda: g.auth_user)()
    except AuthenticationFailed:
        if not allow_none:
            raise
    return None


def get_admin_request_user(allow_none=True) -> Optional[User]:
    from .decorators import require_admin_login
    try:
        return require_admin_login(lambda: g.user)()
    except AuthenticationFailed:
        if not allow_none:
            raise
    return None


def try_verify_request_mobile_code(user: User, code_type: MobileCodeType) -> Union[bool, Callable]:
    """尝试验证验证码， 如果没有验证码传参，则不校验"""
    if not user.mobile:
        return False
    # 支持在header或body中传递 Sms-Captcha: {"validate_code": "xxxxxx"} 或 Sms-Captcha: xxxxxx
    code = None
    headers = request.headers
    if (value := headers.get('SMS_CAPTCHA') or headers.get('Sms-Captcha')):
        if value.isdigit():
            code = value
        else:
            try:
                value = json_loads(value)
            except ValueError:
                raise InvalidMobileVerificationCode
            code = value.get('validate_code')
    elif (request_json := request.get_json(silent=True)) and (value := request_json.get('sms_captcha')):
        # 这里不能直接判断 request.json，flask 2.1 之后，如果没有 body 会报错
        if isinstance(value, (int, str)):
            code = value
        elif isinstance(value, dict):
            code = value.get('validate_code')
    else:
        return False

    if not code or not isinstance(code, (int, str)):
        raise InvalidMobileVerificationCode
    if isinstance(code, int):
        code = str(code)
    elif not code.isdigit():
        raise InvalidMobileVerificationCode

    callback = MobileCodeValidator(
            user.mobile_country_code,
            user.mobile_num,
            code_type
        ).validate(code, delete=False)
    UserPreferences(user.id).two_fa_type = TwoFAType.MOBILE
    return callback


def verify_request_mobile_code(user: User, code_type: MobileCodeType) -> Callable:
    cb = try_verify_request_mobile_code(user, code_type)
    if not cb:
        raise InvalidMobileVerificationCode
    return cb


def try_verify_request_totp_code(user: User) -> Union[bool, Callable]:
    from app.business.security import SecuritySettingType, update_security_statistics
    if not user.totp_auth_key:
        return False

    code = None
    headers = request.headers
    if (value := headers.get('TOTP_CAPTCHA') or headers.get('Totp-Captcha')):
        if value.isdigit():
            code = value
        else:
            try:
                value = json_loads(value)
            except ValueError:
                raise InvalidTotpVerificationCode
            code = value.get('validate_code')
    elif (request_json := request.get_json(silent=True)) and (value := request_json.get('totp_captcha')):
        if isinstance(value, (int, str)):
            code = value
        elif isinstance(value, dict):
            code = value.get('validate_code')
    else:
        return False

    if not code or not isinstance(code, (int, str)):
        raise InvalidTotpVerificationCode
    if isinstance(code, int):
        code = str(code)
    elif not code.isdigit():
        raise InvalidTotpVerificationCode

    callback = TotpCodeValidator(user).validate(code, delete=False)
    UserPreferences(user.id).two_fa_type = TwoFAType.TOTP
    update_security_statistics([user.id], SecuritySettingType.TOTP)
    return callback


def verify_request_totp_code(user: User) -> Callable:
    cb = try_verify_request_totp_code(user)
    if not cb:
        raise InvalidTotpVerificationCode
    return cb


def try_verify_request_webauthn_credential(user: User) -> Union[bool, Callable]:
    from app.business.security import SecuritySettingType, update_security_statistics
    if not user.web_authn_list:
        return False
    headers = request.headers
    if value := headers.get('Webauthn-Captcha'):
        try:
            value = json_loads(value)
        except ValueError:
            raise WebauthnVerificationFailed
    elif (request_json := request.get_json(silent=True)) and (value := request_json.get('webauthn_captcha')):
        if not isinstance(value, dict):
            raise WebauthnVerificationFailed
    else:
        return False
    WebauthnCredentialValidator(user).validate(value)
    UserPreferences(user.id).two_fa_type = TwoFAType.WEBAUTHN
    update_security_statistics([user.id], SecuritySettingType.WEBAUTHN)
    def cb():
        return
    return cb


def verify_request_operation_token(user: User, tfa_type: TwoFAType = None) -> Callable:
    headers = request.headers
    token = (headers.get('OPERATION_TOKEN') or headers.get('OPERATE_TOKEN')
             or headers.get('Operate-Token'))
    if token:
        try:
            int(token, 16)
        except ValueError:
            try:
                token = json_loads(token)
            except ValueError:
                raise TwoFactorAuthenticationFailed
    elif not (json := request.get_json(silent=True)):
        raise TwoFactorAuthenticationFailed
    elif not (token := json.get('operate_token')):
        raise TwoFactorAuthenticationFailed

    _require_frequency_limit('verify_request_operation_token', user.id)
    cache = UserOperationTokenCache(token)
    if tfa_type is None:
        if cache.get_user() != user.id:
            raise TwoFactorAuthenticationFailed
    else:
        user_id, _tfa_type = cache.get_user_with_2fa_type()
        if user_id != user.id or _tfa_type != tfa_type:
            raise TwoFactorAuthenticationFailed
    def cb():
        cache.delete()
    return cb


def verify_admin_request_operation_token() -> Callable:
    headers = request.headers
    _token = (headers.get('OPERATION_TOKEN') or headers.get('OPERATE_TOKEN')
             or headers.get('Operate-Token'))
    _type = (headers.get('OPERATION_TYPE') or headers.get('OPERATE_TYPE')
             or headers.get('Operate-Type'))
    if not _token or not _type:
        if not (json_data := request.get_json(silent=True)):
            raise AuthenticationTimeout

        _token = json_data.get('operation_token')
        _type = json_data.get('operation_type')

    if not _token or not _type:
        raise AuthenticationTimeout
    _cache_cls = AdminUserOperationTokenCache
    try:
        _type_enum = AdminWebAuthnOperationType[_type]
    except ValueError:
        raise AuthenticationTimeout
    _cache = AdminUserOperationTokenCache(_token, _type_enum)
    user_id = _cache.get_user()
    if user_id is None:
        raise AuthenticationTimeout
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise AuthenticationTimeout
    g.operation_user = user
    g.operation_type = _type_enum

    def cb():
        _cache.delete()
    return cb


def verify_admin_webauthn_operation_token(auth_user: User) -> Callable:
    headers = request.headers
    _token = (headers.get('WEBAUTHN_TOKEN') or headers.get('WebAuthn-Token'))
    _type = (headers.get('OPERATION_TYPE') or headers.get('OPERATE_TYPE')
             or headers.get('Operate-Type'))
    if not _token or not _type:
        if not (json_data := request.get_json(silent=True)):
            raise AuthenticationTimeout

        _token = json_data.get('webauthn_token')
        _type = json_data.get('operation_type')

    if not _token or not _type:
        raise AuthenticationTimeout
    _cache_cls = AdminUserWebAuthnOperationTokenCache
    try:
        _type_enum = AdminWebAuthnOperationType[_type]
    except ValueError:
        raise AuthenticationTimeout
    _cache = _cache_cls(_token, _type_enum)
    user_id = _cache.get_user()
    if user_id is None:
        raise AuthenticationTimeout
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise AuthenticationTimeout
    if auth_user.id != user.id:
        raise AuthenticationTimeout
    def cb():
        _cache.delete()
    return cb


def verify_request_email_token(user: User) -> Callable:
    if not user.main_user_email:
        raise EmailCodeVerificationFailed

    headers = request.headers
    token = headers.get('EMAIL_CODE_TOKEN') or headers.get('Email-Code-Token')
    if token:
        try:
            int(token, 16)
        except ValueError:
            try:
                token = json_loads(token)
            except ValueError:
                raise EmailCodeVerificationFailed
    elif not (json := request.get_json(silent=True)):
        raise EmailCodeVerificationFailed
    elif not (token := json.get('email_code_token')):
        raise EmailCodeVerificationFailed

    _require_frequency_limit('verify_request_email_token', user.id)
    cache = EmailCodeTokenCache(token)
    if cache.get_user() != user.id:
        raise EmailCodeVerificationFailed

    def cb():
        cache.delete()
    return cb


def _require_frequency_limit(key: str, arg: Union[str, Any]):
    from app.api.common.decorators import limit_frequency

    deco = limit_frequency(*CAPTCHA_VALIDATION_LIMIT, key, str(arg))
    deco(lambda: True)()


def require_email_exists(email: str) -> User:
    """used for any email validation"""
    if not email:
        raise EmailDoesNotExist
    _require_frequency_limit('verify_request_email', get_request_ip())
    monitor_client.increase('web', 'check_email_exists')
    if not (user := User.query.filter(User.email == email).first()):
        raise EmailDoesNotExist
    return user


def require_email_not_exists(email: str):
    """used for any email validation"""
    if not email:
        return
    _require_frequency_limit('verify_request_email', get_request_ip())
    monitor_client.increase('web', 'check_email_exists')
    if User.query.filter(User.email == email).first():
        raise EmailAlreadyExists


def require_ip_not_only_withdrawal():
    _ip_info = GeoIP(get_request_ip())
    ip_country_code = _ip_info.country_code
    if ip_country_code:
        country_settings = None
        # noinspection PyBroadException
        try:
            country_settings = CountrySettings(ip_country_code)
        except Exception:
            current_app.logger.warning(f"failed to get country settings for {ip_country_code}")
        if country_settings and country_settings.only_withdrawal:
            raise NotSupportedByCountryInOnlyWithdrawal
        
        block_record = LocalAreasBlock.query.filter(
            LocalAreasBlock.country_code == ip_country_code,
            LocalAreasBlock.state_code == _ip_info.state_code,
            LocalAreasBlock.type == LocalAreasBlock.Type.WITHDRAWAL_ONLY,
            LocalAreasBlock.status == LocalAreasBlock.Status.VALID
        ).first()
        if block_record:
            raise NotSupportedByCountryInOnlyWithdrawal


def check_maintain_mode(raise_exception=True):
    cache = SystemMaintainManagerCache()
    data = cache.saving_data
    if data and data['on']:
        start_time = data['start_time']
        end_time = data['end_time']
        current_ts = current_timestamp(to_int=True)
        if start_time <= current_ts <= end_time or (end_time == 0 and current_ts >= start_time):
            if not raise_exception:
                return True
            raise ServerInMaintainMode(data=dict(
                start_time=start_time,
                end_time=end_time,
                url=data['url'],
                protect_duration=data['protect_duration']
            ))
    return False


read_only_module = [
    "frontend.KlineAi",
    "frontend.Support",
    "frontend.MarketMaker",
    "frontend.Margin",
    "frontend.Blog",
    "frontend.CBOX",
    "frontend.Abnormal-Deposit-Application",
    "frontend.Activities",
    "frontend.Broker",
    "frontend.Account",
    "frontend.Wallet",
    "frontend.Order",
    "frontend.Safety",
    "frontend.User",
    "frontend.Market",
    "frontend.Message",
    "frontend.Profit-Loss",
    "frontend.Projects",
    # v2 api
    "v2.Margin",
]


read_only_except_endpoints = [
    "frontend.User_login_qr_code_resource",
    "frontend.Message_messages_resource",
    "frontend.Message_message_resource",
    "frontend.Message_mobile_code_resource",
    "frontend.Wallet_assets_resource",
    "frontend.User_user_resource",
    "frontend.Activities_seventh_anniversary_reward_receive_resource",
    "frontend.Account_share_pop_resource",
    "frontend.User_non_sensitive_user_info_resource",
    "frontend.User_withdraw_password_reset_resource",
]


def _is_request_in_read_only_module():
    if not request or not request.endpoint:
        return False

    for prefix in read_only_module:
        if request.endpoint.startswith(prefix):
            return True
    return False

def _is_request_in_read_only_except_endpoints():
    if not request or not request.endpoint:
        return False

    for endpoint in read_only_except_endpoints:
        if request.endpoint == endpoint:
            return True
    return False

def auto_read_only():
    # only support read_only mode for `GET` reqeuest which that take no effects with database
    if request.method != "GET":
        return
    
    func = current_app.view_functions.get(request.endpoint)
    if not getattr(func, "read_only", False) and (not _is_request_in_read_only_module()):
        return
    
    if _is_request_in_read_only_except_endpoints():
        return
    
    g.read_only = True


def is_old_app_request(android_build: int, ios_build: int) -> bool:
    platform = get_request_platform()
    build = get_request_build()
    # 处理ios两个app的兼容，临时解决办法。后面CoinEx Pro build号增长超过500时注意跟进处理。
    # CoinEx App: 500 < build < 600
    # CoinEx Pro App: 0 < build < 500
    if ios_build < 500:
        ios_build += 600
    if platform.is_ios() and build < 500:
        build += 600
    if (platform.is_android() and build < android_build) or \
            (platform.is_ios() and build < ios_build):
        return True
    return False
