# -*- coding: utf-8 -*-
from typing import Union, Optional, Callable, Any, Literal

from flask import g
from flask_babel import gettext, force_locale

from app.business.user import UserPreferences
from app.caches.flow_control import SmsUserCache
from app.utils.sms import SMS<PERSON>rovider

from .auth import WebAuthn
from ...business.risk_control.base import RiskControlGroupConfig
from ...business.risk_control.sms import check_country_sms_risk
from ...common import EmailCodeType, MobileCodeType, CAPTCHA_SEND_LIMIT, SMS_MOBILE_BINDING_SEND_LIMIT,\
    CAPTCHA_VALIDATION_LIMIT
from ...caches import (TotpRecentAuthCache, EmailCodeCache, MobileCodeCache,
                       SmsSendControlCache, SmsBlackListCache)
from ...business import (send_sms, send_verification_code_email)
from ...business.risk_control import execute_sms_risk_control_task
from ...business.clients import monitor_client
from ...utils import new_verification_code, verify_totp_code, get_sms_lang
from ...models import User
from ...exceptions import (EmailCodeVerificationFailed, InvalidMobileVerificationCode,
                           InvalidTotpVerificationCode, VerificationCodeHasBeenUsed,
                           FrequencyExceeded)


def _require_frequency_limit(count: int, interval: int, key: str, arg: Union[str, Any]):
    from app.api.common.decorators import limit_frequency

    deco = limit_frequency(count, interval, key, str(arg))
    deco(lambda: True)()


def _require_ip_limit(count: int, interval: int, key: str):
    from app.api.common.request import get_request_ip

    _require_frequency_limit(count, interval, key, get_request_ip())


def _require_user_limit(count: int, interval: int, key: str):
    if user := g.get('auth_user'):
        _require_frequency_limit(count, interval, key, user.id)


def _require_user_sms_risk_control(count: int, interval: int, code_type: str):
    """触发限频进入风控审核"""
    if not (user := g.get('auth_user')):
        return
    cache = SmsSendControlCache(str(user.id), interval)
    if not cache.add_value_if_fewer_than(count, code_type):
        execute_sms_risk_control_task.delay(user.id, count, interval)
        raise FrequencyExceeded


def _require_sms_blacklist(count: int, interval: int, key: str, t: Literal['ip', 'mobile'], value: str):
    """触发限频进入黑名单"""
    cache = SmsBlackListCache(t)
    if cache.has(value):
        raise FrequencyExceeded
    try:
        _require_frequency_limit(count, interval, key, value)
    except FrequencyExceeded:
        cache.add(value)
        raise


class TotpCodeValidator:

    def __init__(self, auth: Union[str, User]) -> None:
        if isinstance(auth, User):
            self._auth_key = auth.get_totp_auth_key()
        else:
            self._auth_key = auth

    @property
    def totp_auth_key(self):
        return self._auth_key

    def validate(self, code: str, *, delete: bool = True) -> Optional[Callable]:
        if not self._auth_key:
            raise InvalidTotpVerificationCode
        _require_frequency_limit(*CAPTCHA_VALIDATION_LIMIT, 'validate_totp_code', self.totp_auth_key)
        _require_user_limit(*CAPTCHA_VALIDATION_LIMIT, 'validate_totp_code')
        recent_totp_cache = TotpRecentAuthCache(self._auth_key)
        if recent_totp_cache.is_code_recently_used(code):
            raise VerificationCodeHasBeenUsed

        if not verify_totp_code(self._auth_key, code):
            raise InvalidTotpVerificationCode
        if delete:
            recent_totp_cache.set_code(code)
        else:
            def cb():
                recent_totp_cache.set_code(code)
            return cb


class WebauthnCredentialValidator:

    def __init__(self, user) -> None:
        self.user = user

    def validate(self, credential: dict) -> Optional[Callable]:
        platform = WebAuthn.get_request_platform_source()
        WebAuthn.verify_authentication(self.user, credential, platform)


class EmailCodeValidator:

    def __init__(self, email: str, code_type: EmailCodeType) -> None:
        self.email = email
        self.code_type = code_type

    def send(self) -> str:
        _require_frequency_limit(*CAPTCHA_SEND_LIMIT, 'send_email_code', self.email)
        _require_user_limit(*CAPTCHA_SEND_LIMIT, 'send_email_code')
        _require_ip_limit(*CAPTCHA_SEND_LIMIT, 'send_email_code')
        code = new_verification_code()
        EmailCodeCache(self.email, self.code_type).set_code(code)

        if (user := g.get('auth_user')) and user.email == self.email:
            name = user.name_displayed
        else:
            name = self.email
        
        send_verification_code_email.delay(
            self.email, self.code_type.value, name, code, g.lang)
        return code

    def validate(self, code: str, *, delete: bool = True) -> Optional[Callable]:
        _require_frequency_limit(*CAPTCHA_VALIDATION_LIMIT, 'validate_email_code', self.email)
        _require_user_limit(*CAPTCHA_VALIDATION_LIMIT, 'validate_email_code')
        cache = EmailCodeCache(self.email, self.code_type)
        if not cache.verify_code(code):
            raise EmailCodeVerificationFailed
        if delete:
            cache.delete()
        else:
            def cb():
                cache.delete()
            return cb


class MobileCodeValidator:

    MOBILE_CODE_TEXTS = {
        MobileCodeType.LOGIN_WITH_OPERATION_TOKEN: gettext(
            '验证码：%(code)s，你正在使用本手机号登录，该码30分钟内有效。'),
        MobileCodeType.LOGIN_PASSWORD_EDIT: gettext(
            '验证码：%(code)s，你正在修改登录密码，该码30分钟内有效。'),
        MobileCodeType.LOGIN_PASSWORD_SET: gettext(
            '验证码：%(code)s，你正在设置登录密码，该码30分钟内有效。'),
        MobileCodeType.NON_LOGIN_PASSWORD_RESET: gettext(
            '验证码：%(code)s，你正在修改登录密码，该码30分钟内有效。'),
        MobileCodeType.MOBILE_BINDING: gettext(
            '验证码：%(code)s，你正在绑定手机，该码30分钟内有效。'),
        MobileCodeType.MOBILE_EDIT: gettext(
            '验证码：%(code)s，你正在修改绑定手机，该码30分钟内有效。'),
        MobileCodeType.MOBILE_RESET: gettext(
            '验证码：%(code)s，你正在修改绑定手机，该码30分钟内有效。'),
        MobileCodeType.MOBILE_UNBIND: gettext(
            '验证码：%(code)s，你正在解绑手机，该码30分钟内有效。'),
        MobileCodeType.TOTP_BINDING: gettext(
            '验证码：%(code)s，你正在绑定谷歌验证器，该码30分钟内有效。'),
        MobileCodeType.TOTP_EDIT: gettext(
            '验证码：%(code)s，你正在修改谷歌验证器，该码30分钟内有效。'),
        MobileCodeType.TOTP_UNBIND: gettext(
            '验证码：%(code)s，你正在解绑谷歌验证器，该码30分钟内有效。'),
        MobileCodeType.EMAIL_BINDING: gettext(
            '验证码：%(code)s，你正在绑定邮箱，该码30分钟内有效。'),
        MobileCodeType.EMAIL_EDIT: gettext(
            '验证码：%(code)s，你正在修改邮箱，该码30分钟内有效。'),
        MobileCodeType.SECURITY_RESET: gettext(
            '验证码：%(code)s，你正在重置安全项，该码30分钟内有效。'),
        MobileCodeType.FLAT_COIN: gettext(
            '验证码：%(code)s，你正在进行还币操作，该码30分钟内有效。'),
        MobileCodeType.WITHDRAWAL_ADDRESS_ADDITION: gettext(
            '验证码：%(code)s，你正在申请增加提现钱包地址，该码30分钟有效。'),
        MobileCodeType.API_WITHDRAWAL_ADDRESS_ADDITION: gettext(
            '验证码：%(code)s，你正在新建白名单地址，该码30分钟内有效。'),
        MobileCodeType.WITHDRAWAL_APPLICATION: gettext(
            '验证码：%(code)s，你正在申请提现，该码30分钟内有效。'),
        MobileCodeType.API_AUTH_ADDITION: gettext(
            '验证码：%(code)s，你正在申请API密钥，该码30分钟内有效。'),
        MobileCodeType.API_AUTH_EDIT: gettext(
            '验证码：%(code)s，你正在修改API密钥，该码30分钟内有效。'),
        MobileCodeType.API_AUTH_QUERY: gettext(
            '验证码：%(code)s，你正在查看API密钥，该码30分钟内有效。'),
        MobileCodeType.API_AUTH_DELETION: gettext(
            '验证码：%(code)s，你正在删除API密钥，该码30分钟内有效。'),
        MobileCodeType.API_AUTH_EXTEND: gettext(
            '验证码：%(code)s，你正在续期API密钥，该码30分钟内有效。'),
        MobileCodeType.TRADING_PASSWORD_ADDITION: gettext(
            '验证码：%(code)s，你正在设置交易密码，该码30分钟内有效。'),
        MobileCodeType.TRADING_PASSWORD_EDIT: gettext(
            '验证码：%(code)s，你正在修改交易密码，该码30分钟内有效。'),
        MobileCodeType.TRADING_PASSWORD_REMOVE: gettext(
            '验证码：%(code)s，你正在关闭交易密码，该码30分钟内有效。'),
        MobileCodeType.ORDER_PLACEMENT: gettext(
            '验证码：%(code)s，你正在进行下单操作，该码30分钟内有效。'),
        MobileCodeType.VIP_PURCHASE: gettext(
            '验证码：%(code)s，你正在购买VIP，该码30分钟内有效。'),
        MobileCodeType.SUB_ACCOUNT_REGISTRATION: gettext(
            '验证码： %(code)s，你正在注册子账号，该码30分钟内有效。'),
        MobileCodeType.SUB_ACCOUNT_PASSWORD_RESET: gettext(
            '验证码： %(code)s，你正在修改子账号密码，该码30分钟内有效。'),
        MobileCodeType.SUB_ACCOUNT_DELETION: gettext(
            '验证码： %(code)s，你正在删除子账号，该码30分钟内有效。'),
        MobileCodeType.SUB_ACCOUNT_BIND_MANAGER: gettext(
            '验证码： %(code)s，你正在添加子账号授权，该码30分钟内有效。'),
        MobileCodeType.SEND_C_BOX: gettext(
            '验证码： %(code)s，你正在发C-Box，该码30分钟内有效。'
        ),
        MobileCodeType.ADD_WITHDRAWAL_APPROVER: gettext(
            '验证码： %(code)s, 你正在添加提现审核人，该码30分钟内有效。'
        ),
        MobileCodeType.DELETE_WITHDRAWAL_APPROVER: gettext(
            '验证码： %(code)s, 您正在删除提现审核人，该码30分钟内有效。'
        ),
        MobileCodeType.SIGN_OFF: gettext(
            '验证码：%(code)s，你正在注销账号，该码30分钟内有效。'
        ),
        MobileCodeType.ADD_WITHDRAW_PASSWORD: gettext(
            '验证码： %(code)s，你正在添加提现密码，该码30分钟内有效。'),
        MobileCodeType.EDIT_WITHDRAW_PASSWORD: gettext(
            '验证码： %(code)s，你正在修改提现密码，该码30分钟内有效。'),
        MobileCodeType.RESET_WITHDRAW_PASSWORD: gettext(
            '验证码： %(code)s，你正在重置提现密码，该码30分钟内有效。'),
        MobileCodeType.BIND_WEBAUTHN: gettext(
            '验证码： %(code)s，你正在创建通行密钥，该码30分钟内有效。'),
        MobileCodeType.UNBIND_WEBAUTHN: gettext(
            '验证码： %(code)s，你正在删除通行密钥，该码30分钟内有效。'),
        MobileCodeType.RESET_WEBAUTHN: gettext(
            '验证码： %(code)s，你正在重置通行密钥，该码30分钟内有效。'),
        MobileCodeType.P2P_FINISH: gettext(
            '验证码： %(code)s，你正在进行p2p放币，该码30分钟内有效。'),
        MobileCodeType.P2P_ADD_USER_PAY_CHANNEL: gettext(
            '验证码： %(code)s，你正在增加收款方式，该码30分钟内有效。'
        ),
        MobileCodeType.P2P_EDIT_USER_PAY_CHANNEL: gettext(
            '验证码： %(code)s，你正在修改收款方式，该码30分钟内有效。'
        ),
        MobileCodeType.P2P_DELETE_USER_PAY_CHANNEL: gettext(
            '验证码： %(code)s，你正在删除收款方式，该码30分钟内有效。'
        ),
        MobileCodeType.NON_LOGIN_BIND_THIRD_PARTY_ACCOUNT: gettext(
            '验证码： %(code)s，你正在绑定第三方账号，该码30分钟内有效。'
        ),
        MobileCodeType.BIND_THIRD_PARTY_ACCOUNT: gettext(
            '验证码： %(code)s，你正在绑定第三方账号，该码30分钟内有效。'
        )
    }

    def __init__(self, country_code: int, mobile: str, code_type: MobileCodeType, user: User = None) -> None:
        self.user = user
        self.country_code = country_code
        self.mobile = mobile
        self.code_type = code_type
        self.full_mobile = f'+{self.country_code}{self.mobile}'

    _SPECIAL_SMS_PROVIDERS = (SMSProvider.TELEGRAM, SMSProvider.WHATSAPP)

    @classmethod
    def get_send_text(cls, country_code: int, lang: str, code_type: MobileCodeType) -> str:
        # 伊朗用户使用固定的短信渠道商以及固定的短信模板
        if country_code == 98:
            return 'کد تایید: %(code)s، این کد در طی ۳۰ دقیقه موثر است.'
        with force_locale(lang):
            sms_text = gettext(cls.MOBILE_CODE_TEXTS[code_type])
        return sms_text
    
    def select_providers(self, sms_lang):
        """
        返回值:
        sms_providers: 短信渠道列表, 按此顺序发送验证码
        special_providers: 特殊渠道列表
        """

        from app.business.sms import SMSSender
        all_providers = SMSSender.select_providers(self.country_code, sms_lang)
        pref = None
        third_party_message_accounts = []
        if self.user:
            pref = UserPreferences(self.user.id)

        if pref:
            third_party_message_accounts = [SMSProvider[item] for item in pref.third_party_message_accounts]
        
        # special_providers 只包含优先使用的，及不确定可发送的特殊渠道
        special_providers = []
        for p in all_providers:
            if p not in self._SPECIAL_SMS_PROVIDERS:
                break
            if p not in third_party_message_accounts:
                special_providers.append(p)
        
        # sms_providers只包含普通渠道，及确定可发送的特殊渠道
        sms_providers = [p for p in all_providers if p not in special_providers]
        
        if pref and pref.only_allow_sms_verification:
            sms_providers = [item for item in all_providers if item not in self._SPECIAL_SMS_PROVIDERS]
            special_providers = []
        
        # if set(third_party_message_accounts) & set(all_providers):
        #     return sms_providers, []
        return sms_providers, special_providers

    def send(self) -> str | None:
        _SMS_SEND_LIMIT = RiskControlGroupConfig().get_sms_limit()
        _require_user_limit(*CAPTCHA_SEND_LIMIT, 'send_mobile_code')
        _require_user_sms_risk_control(*_SMS_SEND_LIMIT, self.code_type.value)  # 风控规则优先，避免不被触发
        _require_frequency_limit(*CAPTCHA_SEND_LIMIT, 'send_mobile_code', self.full_mobile)
        _require_sms_blacklist(*_SMS_SEND_LIMIT, 'send_mobile_code-rule2', 'mobile', self.full_mobile)
        _require_ip_limit(*CAPTCHA_SEND_LIMIT, 'send_mobile_code')
        _require_ip_limit(*_SMS_SEND_LIMIT, 'send_mobile_code-rule2')
        # 容易被刷的接口，限制更严格
        if self.code_type in (MobileCodeType.MOBILE_BINDING, MobileCodeType.MOBILE_EDIT, MobileCodeType.MOBILE_RESET):
            _require_user_limit(*SMS_MOBILE_BINDING_SEND_LIMIT, 'send_mobile_code_binding')

        # 检查国家维度禁止下发短信操作
        user = self.user
        if user and check_country_sms_risk(user, self.country_code):
            return None
        
        code = new_verification_code()
        MobileCodeCache(self.full_mobile, self.code_type).set_code(code)
        sms_lang = get_sms_lang(self.country_code, g.lang)
        sms_text = self.get_send_text(self.country_code, sms_lang, self.code_type)

        user_id = user.id if user else None
        if user:
            SmsUserCache(f'{self.country_code}{self.mobile}').set(user_id, ex=SmsUserCache.TTL)
        
        # 部分运营商如telegram,whatsapp发送验证码，不支持自定义文案，通过extra参数传入
        extra = {'verification_code': code, 'lang': sms_lang}
        providers, special_providers = self.select_providers(sms_lang)
        if providers:
            send_sms.delay(self.country_code, self.mobile, sms_text % {'code': code}, sms_lang, 
                           [p.name for p in providers],
                           extra=extra)
        if special_providers:
            send_sms.delay(self.country_code, self.mobile, sms_text % {'code': code}, sms_lang, 
                           [p.name for p in special_providers],
                           extra=extra)
        monitor_client.increase('web', 'sms_send', labels={'sms_business': self.code_type.value})
        return code

    def validate(self, code: str, *, delete: bool = True) -> Optional[Callable]:
        _require_frequency_limit(*CAPTCHA_VALIDATION_LIMIT, 'validate_mobile_code', self.full_mobile)
        _require_user_limit(*CAPTCHA_VALIDATION_LIMIT, 'validate_mobile_code')
        cache = MobileCodeCache(self.full_mobile, self.code_type)
        if not cache.verify_code(code):
            raise InvalidMobileVerificationCode
        if delete:
            cache.delete()
        else:
            def cb():
                cache.delete()
            return cb
    
    def get_estimated_sms_provider_name(self) -> str:
        # 如预计发送渠道是特殊渠道则返回渠道名称，否则返回空字符串
        sms_lang = get_sms_lang(self.country_code, g.lang)
        provider = ""
        sms_providers, _ = self.select_providers(sms_lang)
        if sms_providers and sms_providers[0] in self._SPECIAL_SMS_PROVIDERS:
            provider = sms_providers[0].value
        return provider
