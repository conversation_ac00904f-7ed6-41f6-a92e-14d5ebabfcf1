from functools import partial

import json
from flask import current_app, Response
from webargs import fields, ValidationError
from app import Language
from app.api.common import Namespace
from app.api.common import Resource
from app.api.common import respond_with_code
from app.api.common.fields import EnumField
from app.business.clients.ai_translate import TermsEnum, AITranslateClient
from app.models import db
from app.models.mongo.translation import TranslationTaskMySQL

ns = Namespace('Ai-Translation')


def typing_one_of(value, typings):
    for typing in typings:
        if isinstance(value, typing):
            return value
    raise ValidationError(f'Field type should be one of {typings}.')




@ns.route('/sync')
@respond_with_code
class SyncTranslationResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        source=EnumField(Language, required=True),
        target=fields.Raw(required=True, validate=partial(typing_one_of, typings=[str, list])),
        content=fields.Raw(required=True, validate=partial(typing_one_of, typings=[str, dict, list])),
        term=EnumField(TermsEnum, required=False), business=EnumField(TranslationTaskMySQL.Business, required=False),
    ))
    def post(cls, **kwargs):
        """同步翻译"""
        source = kwargs['source']
        target = kwargs['target']
        content = kwargs['content']
        term = kwargs.get('term')
        business = kwargs.get('business')
        translator = AITranslateClient(terms=term)

        return translator.translate(content, source, target, business=business)


@ns.route('/async')
@respond_with_code
class AsyncTranslationResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        source=EnumField(Language, required=True),
        target=fields.Raw(required=True, validate=partial(typing_one_of, typings=[str, list])),
        content=fields.Raw(required=True, validate=partial(typing_one_of, typings=[str, dict, list])),
        business=EnumField(TranslationTaskMySQL.Business, required=True),
        business_id=fields.String(required=False),
        business_info=fields.String(required=False),
        term=EnumField(TermsEnum, required=False),
    ))
    def post(cls, **kwargs):
        """异步翻译"""
        source = kwargs['source']
        target = kwargs['target']
        content = kwargs['content']
        business = kwargs['business']
        business_id = kwargs.get('business_id')
        business_info = kwargs.get('business_info')
        term = kwargs.get('term')

        translator = AITranslateClient(terms=term, business=business)

        return translator.translate_async(
            content, source, target,
            business_id=business_id,
            business_info=business_info,
            business=business
        )


@ns.route('/tasks')
@respond_with_code
class TranslationTaskResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        business=EnumField(TranslationTaskMySQL.Business, required=True),
        business_id=fields.Raw(required=True, validate=partial(typing_one_of, typings=[str, list])),
        task_id=fields.String,
        business_info=fields.String(required=False),
        with_content=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """获取翻译结果"""
        business = kwargs['business']
        business_id = kwargs.get('business_id')
        business_info = kwargs.get('business_info')
        task_id = kwargs.get('task_id')
        if ',' in business_id:
            business_id = business_id.split(',')

        tasks = TranslationTaskMySQL.get_business_tasks(business, business_id, business_info, task_id)

        result = []
        for task in tasks:
            data = \
            {
                'id': str(task.id),
                'task_id': task.task_id,
                'source': task.source,
                'target': task.target,
                'status': task.status,
                'created_at': task.created_at,
                'updated_at': task.updated_at,
                'business_info': task.business_info,
                'business_id': task.business_id,
            }

            if kwargs['with_content']:
                try:
                    content = json.loads(task.content)
                except Exception:
                    content = task.content
                data['content'] = content
            result.append(data)
        return result

    @ns.use_kwargs(dict(
        business=EnumField(TranslationTaskMySQL.Business, required=True),
        business_id=fields.String(required=True),
        target_langs=fields.String,
        business_info=fields.String(required=False),
    ))
    def delete(cls, **kwargs):
        """删除翻译结果（软删除）"""
        business = kwargs['business']
        business_id = kwargs['business_id']
        business_info = kwargs.get('business_info')

        query = TranslationTaskMySQL.query
        
        business_ids = business_id.split(',')


        if len(business_ids) == 1:
            query = query.filter(TranslationTaskMySQL.business_id == business_id)
        else:
            query = query.filter(TranslationTaskMySQL.business_id.in_(business_ids))
        query = query.filter(TranslationTaskMySQL.business == business)

        if langs:= kwargs.get('target_langs'):
            langs = langs.split(',')
            query = query.filter(TranslationTaskMySQL.target.in_(langs))
        
        if business_info is not None:
            query = query.filter(TranslationTaskMySQL.business_info == business_info)
        
        # 执行软删除
        affected_rows = query.update({TranslationTaskMySQL.deleted: True}, synchronize_session=False)
        db.session.commit()
        
        current_app.logger.warning(f"Delete translation task: {affected_rows}")
        return 'success'


@ns.route('/task/<string:id>')
@respond_with_code
class TranslationTaskDetailResource(Resource):
    @classmethod
    def get(cls, id: str):
        """获取详细翻译结果"""
        task =  TranslationTaskMySQL.query.filter_by(mongo_id=id).first()
        return {
            'id': str(task.id),
            'task_id': task.task_id,
            'source': task.source,
            'target': task.target,
            'status': task.status,
            'created_at': task.created_at,
            'updated_at': task.updated_at,
            'input_tokens': task.input_tokens,
            'output_tokens': task.output_tokens,
            'business_info': task.business_info,
            'business_id': task.business_id,
            'business': task.business,
            'content': task.content,
        }


@ns.route('/stream')
@respond_with_code
class StreamTranslateResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        target=fields.Raw(required=True, validate=partial(typing_one_of, typings=[str, Language, list])),
        content=fields.Raw(required=True, validate=partial(typing_one_of, typings=[str, dict, list])),
    ))
    def post(cls, **kwargs):
        """系统-AI任务查询-流式翻译"""

        target = kwargs['target']
        content = kwargs['content']

        translator = AITranslateClient()
        return Response(
            translator.translate_sse(content, target),
            mimetype='text/event-stream',
            headers={
                'Transfer-Encoding': 'chunked',  # 显示指定分块传输
                'Cache-Control': 'no-cache, no-transform',  # 禁止任何加速器缓存
                'X-Accel-Buffering': 'no',  # 禁用 Nginx 缓冲
                'Connection': 'keep-alive', # Added to ensure connection stays open
            }
        )
