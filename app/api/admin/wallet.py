# -*- coding: utf-8 -*-
from urllib import parse

import requests
from flask import g, request, make_response

from app.config import config
from ..common import Resource, Namespace
from ...business.clients.wallet import jwt_token_create

ns = Namespace('Wallet')

@ns.route('/<path:path>', methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
class RelayResource(Resource):

    HOP_BY_HOP_HEADERS = ('Host', 'Connection', 'Transfer-Encoding', 'Content-Encoding',
                          'Content-Length', 'Authorization', 'Cookie', 'Set-Cookie')

    session = requests.Session()

    @classmethod
    def get_file(cls, file_name='file'):
        """
        获取单个文件
        ：return：
        """
        file_obj = request.files.get(file_name)
        if not file_obj:
            return {}
        return {file_name: (file_obj.filename, file_obj, file_obj.content_type)}

    def dispatch_request(self, path, *args, **kwargs):
        meth = getattr(self.session, request.method.lower())
        headers = self.remove_hop_by_hop_headers(request.headers)
        headers['User-Id'] = str(g.user.id)
        headers['Email'] = g.user.email or ''
        headers['token'] = jwt_token_create(config['CLIENT_CONFIGS']['wallet']['salt'], exp=60)
        url = parse.urljoin(config["CLIENT_CONFIGS"]["wallet"]["url"], '/admin/' + path)
        if query_string := request.full_path.split('?', 1)[1]:
            url = f'{url}?{query_string}'
        files = self.get_file('batch-upload')
        if files:
            r = meth(url, headers=self.remove_headers(headers, ['Content-Type']), data=request.data, files = files)
        else:
            r = meth(url, headers=headers, data=request.data)
        return make_response(r.content, r.status_code, self.remove_hop_by_hop_headers(r.headers))

    @classmethod
    def remove_hop_by_hop_headers(cls, headers):
        build_new_headers = {}
        for name, value in headers.items():
            if name not in cls.HOP_BY_HOP_HEADERS:
                build_new_headers[name] = value
        return build_new_headers

    @classmethod
    def remove_headers(cls, headers, remove_names):
        build_new_headers = {}
        for name, value in headers.items():
            if name not in remove_names:
                build_new_headers[name] = value
        return build_new_headers

