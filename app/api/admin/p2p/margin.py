from flask import g
from marshmallow import fields
from sqlalchemy import or_

from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.decorators import require_admin_webauth_token
from app.api.common.fields import <PERSON>umField, TimestampField, PageField, LimitField
from app.business.auth import get_admin_user_name_map
from app.business.lock import <PERSON>acheLock, LockKeys
from app.business.p2p.config import p2p_setting
from app.business.p2p.margin import P2pUserMarginHistoryBiz, update_require_margin
from app.business.p2p.message import send_p2p_margin_change_message
from app.business.p2p.user import update_merchant_margin_status, no_margin_merchant_operate
from app.business.user import get_user_kyc_country_map
from app.common import get_code_to_cn_name
from app.exceptions import InvalidArgument
from app.models import P2pMarginCountry, db, P2pMarginHistory, P2pUserMargin, P2pMerchant, User, KycVerification, \
    P2pUserMarginHistory
from app.models.mongo.op_log import OPNamespaceObjectOperation
from app.models.mongo.op_log import OPNamespaceObjectFiat
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog
from app.models.p2p import P2pOrder
from app.models.user import SignOffUser

ns = Namespace('Admin P2P Margin Operations')
url_prefix = '/margin'


@ns.route('/country')
@respond_with_code
class P2pMarginCountryResource(Resource):
    model = P2pMarginCountry

    @classmethod
    @ns.use_kwargs(dict(
        country_code=fields.String(),
    ))
    def get(cls, **kwargs):
        """p2p配置-p2p地区商家保证金配置-查看"""
        query = cls.model.query
        if code := kwargs.get('country_code'):
            query = query.filter(cls.model.country_code == code)
        data = query.all()
        return dict(
            items=[i.to_dict(enum_to_name=True) for i in data],
            countries=get_code_to_cn_name()
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        amount=fields.Decimal(required=True),
    ))
    def put(cls, **kwargs):
        """p2p配置-p2p地区商家保证金配置-修改"""
        id_ = kwargs['id']
        amount = kwargs['amount']
        row = cls.model.query.get(id_)
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)
        old_amount = row.amount
        if old_amount == amount:
            return
        row.amount = amount
        # 批量修改商家应缴保证金
        user_ids = cls.batch_update_merchant_margin(row.country_code, amount)
        db.session.commit()
        for user_id in user_ids:
            send_p2p_margin_change_message.delay(
                user_id, str(amount), str(old_amount))

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pCountryMargin,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    def batch_update_merchant_margin(cls, country_code, amount):
        # 获取对应kyc的商家
        u_model = P2pUserMargin

        m_rows = P2pMerchant.query.all()
        mer_map = {i.user_id: i for i in m_rows}
        user_country_map = get_user_kyc_country_map(
            list(mer_map.keys()), country_code)
        user_ids = list(user_country_map.keys())

        u_rows = u_model.query.filter(
            u_model.user_id.in_(user_ids)
        ).all()

        for margin in u_rows:
            user_id = margin.user_id
            update_require_margin(
                margin, amount, P2pUserMargin.GraceSource.COUNTRY)
            mer_row = mer_map[user_id]
            update_merchant_margin_status(mer_row, margin.margin_pass)
        return user_ids


@ns.route('/user')
@respond_with_code
class P2pUserMarginResource(Resource):
    model = P2pUserMargin

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(),
        margin_type=EnumField(P2pUserMargin.MarginType),
        email=fields.String(),
    ))
    def get(cls, **kwargs):
        """p2p配置-商家保证金配置-用户"""
        query = cls.model.query
        if margin_type := kwargs.get('margin_type'):
            query = query.filter(cls.model.margin_type == margin_type)
        if user_id := kwargs.get('user_id'):
            query = query.filter(cls.model.user_id == user_id)
        if email := kwargs.get('email'):
            user_row = User.query.filter(User.email == email).first()
            if not user_row:
                raise InvalidArgument(message="该邮箱未注册")
            query = query.filter(cls.model.user_id == user_row.id)
        data = query.all()
        user_ids = [i.user_id for i in data]

        email_map = {i.id: i.email for i in User.query.filter(
            User.id.in_(user_ids)).all()}

        k_model = KycVerification
        kyc_map = {i.user_id: i.country for i in k_model.query.filter(
            k_model.user_id.in_(user_ids),
            k_model.status == k_model.Status.PASSED
        )}

        p_model = P2pMarginCountry
        country_margin_map = {i.country_code: i.amount for i in p_model.query.filter(
            p_model.country_code.in_(kyc_map.values())
        )}

        items = []
        for row in data:
            country_code = kyc_map.get(row.user_id)
            item = row.to_dict(enum_to_name=True)
            item['email'] = email_map.get(row.user_id)
            item['country_code'] = country_code
            item['country_amount'] = country_margin_map.get(
                country_code, p2p_setting.p2p_margin_amount)
            items.append(item)

        return dict(
            items=items,
            countries=get_code_to_cn_name()
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        require_margin=fields.Decimal(required=True),
    ))
    def post(cls, **kwargs):
        """p2p-商家保证金配置-创建个人保证金记录"""
        user_id = kwargs['user_id']
        row = P2pUserMargin.get_or_create(user_id=user_id)
        row.margin_type = P2pUserMargin.MarginType.PERSON
        row.require_margin = kwargs.get('require_margin')
        db.session_add_and_commit(row)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pUserMargin,
            detail=row.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        margin_type=EnumField(P2pUserMargin.MarginType, required=True),
        require_margin=fields.Decimal(),
    ))
    def patch(cls, user_id, **kwargs):
        """p2p-商家保证金配置-应缴保证金修改"""
        row: P2pUserMargin = cls.model.query.filter(
            cls.model.user_id == user_id,
        ).first()
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)
        margin_type = kwargs['margin_type']
        row.margin_type = margin_type
        source_amount = row.require_margin
        if margin_type == cls.model.MarginType.COUNTRY:
            country = get_user_kyc_country_map([user_id]).get(user_id)
            if not country:
                raise InvalidArgument(message="用户没有 kyc 国家，无法删除个人保证金")
            amount = P2pMarginCountry.get_country_margin(country)
            update_require_margin(
                row, amount, P2pUserMargin.GraceSource.COUNTRY)
        else:
            if (amount := kwargs.get('require_margin')) is None:
                raise InvalidArgument(message="缺少个人保证金额度")
            update_require_margin(
                row, amount, P2pUserMargin.GraceSource.PERSON)
        # 更新商家状态
        ret = cls.update_mer_margin_status(user_id, row)
        db.session.commit()
        if ret and not row.margin_pass:
            no_margin_merchant_operate(user_id, row.grace_deadline)
        if amount != source_amount:
            send_p2p_margin_change_message.delay(
                user_id, str(amount), str(source_amount))

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pUserMargin,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )

    @classmethod
    def update_mer_margin_status(cls, user_id, margin_row):
        model = P2pMerchant
        mer_row = model.query.filter(model.user_id == user_id).first()
        if not mer_row:
            return
        ret = update_merchant_margin_status(mer_row, margin_row.margin_pass)
        return ret


@ns.route('/sys_history')
@respond_with_code
class P2pMarginHistoryResource(Resource):
    model = P2pMarginHistory

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(model.Type),
        user_id=fields.Integer(),
        start_at=TimestampField(is_ms=True),
        end_at=TimestampField(is_ms=True),
        page=PageField(missing=1),
        limit=LimitField(missing=10)
    ))
    def get(cls, **kwargs):
        """p2p交易-商家保证金收支表-查看"""
        query = cls.model.query.filter(
            cls.model.status != cls.model.Status.FAIL,
        )
        if type_ := kwargs.get('type'):
            query = query.filter(cls.model.type == type_)
        if user_id := kwargs.get('user_id'):
            query = query.filter(
                or_(
                    cls.model.from_id == user_id,
                    cls.model.to_id == user_id,
                )
            )
        if start_at := kwargs.get('start_at'):
            query = query.filter(cls.model.created_at >= start_at)
        if end_at := kwargs.get('end_at'):
            query = query.filter(cls.model.created_at <= end_at)
        data = query.order_by(
            cls.model.created_at.desc()
        ).paginate(kwargs['page'], kwargs['limit'], False)
        user_ids = set()
        for i in data.items:
            user_ids.add(i.from_id)
            user_ids.add(i.to_id)

        u_model = User
        users = u_model.query.filter(
            u_model.id.in_(user_ids)
        ).with_entities(
            u_model.id,
            u_model.name,
            u_model.email,
        )
        email_map = {i.id: i.email for i in users}
        name_map = {i.id: i.name for i in users}

        items = []
        for i in data.items:
            item = i.to_dict(enum_to_name=True)
            item["from_email"] = email_map.get(i.from_id)
            item["to_email"] = email_map.get(i.to_id)
            item["mer_name"] = name_map.get(
                i.from_id) if i.type == cls.model.Type.PAYMENT else name_map.get(i.to_id)
            items.append(item)

        return dict(
            items=items,
            total=data.total,
            types=cls.model.Type,
            statuses=cls.model.Status,
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True, validate=lambda x: len(x) < 256),
    ))
    def patch(cls, **kwargs):
        row = cls.model.query.get(kwargs['id'])
        if not row:
            raise InvalidArgument
        old_remark = row.remark
        row.remark = kwargs['remark']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PMarginHistory,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=row.remark),
        )
        return


@ns.route('/user_history')
@respond_with_code
class P2pUserMarginHistoryResource(Resource):
    model = P2pUserMarginHistory

    @classmethod
    @ns.use_kwargs(dict(
        biz_type=EnumField(model.BizType,),
        user_id=fields.Integer(),
        to_user_id=fields.Integer(),
        order_id=fields.String(),
        start_at=TimestampField(is_ms=True),
        end_at=TimestampField(is_ms=True),
        status=EnumField(model.Status),
        page=PageField(missing=1),
        limit=LimitField(missing=10)
    ))
    def get(cls, **kwargs):
        """p2p-商家保证金收支表-查看"""
        query = cls.model.query.order_by(
            cls.model.created_at.desc()
        )
        if biz_type := kwargs.get('biz_type'):
            query = query.filter(cls.model.biz_type == biz_type)
        if user_id := kwargs.get('user_id'):
            query = query.filter(cls.model.user_id == user_id)
        if to_user_id := kwargs.get('to_user_id'):
            query = query.filter(cls.model.to_user_id == to_user_id)
        if order_id := kwargs.get('order_id'):
            real_id = P2pOrder.get_real_id(order_id)
            query = query.filter(cls.model.p2p_order_id == real_id)
        if start_at := kwargs.get('start_at'):
            query = query.filter(cls.model.created_at >= start_at)
        if end_at := kwargs.get('end_at'):
            query = query.filter(cls.model.created_at <= end_at)
        if status := kwargs.get('status'):
            query = query.filter(cls.model.status == status)
        data = query.paginate(kwargs['page'], kwargs['limit'], False)

        user_ids = set()
        for i in data.items:
            user_ids.add(i.user_id)
            user_ids.add(i.to_user_id)
        users = User.query.filter(
            User.id.in_(user_ids)
        ).all()

        nick_name_map = {i.id: i.nickname for i in users}
        email_map = {i.id: i.email for i in users}
        audit_name_map = get_admin_user_name_map([i.audit_user_id for i in data.items if i.audit_user_id])
        p2p_order_map = {i.id: i.order_id for i in P2pOrder.query.filter(
            P2pOrder.id.in_([i.p2p_order_id for i in data.items if i.p2p_order_id])
        ).all()}

        ret = []
        for item in data.items:
            amount = item.amount
            item_dict = item.to_dict(enum_to_name=True)
            item_dict['user_email'] = email_map.get(item.user_id, item.user_id)
            item_dict['user_name'] = nick_name_map.get(item.user_id, item.user_id)
            item_dict['to_user_email'] = email_map.get(
                item.to_user_id, item.to_user_id)
            item_dict['audit_name'] = audit_name_map.get(item.audit_user_id, "--")
            item_dict['p2p_show_id'] = p2p_order_map.get(item.p2p_order_id)
            item_dict['amount'] = amount if item.biz_type in cls.model.payment_types() else -amount
            ret.append(item_dict)

        return dict(
            items=ret,
            total=data.total,
            extra=dict(
                biz_types=cls.model.BizType,
                statuses=cls.model.Status,
                admin_biz_types={i.name: i.value for i in cls.model.admin_biz_types()},
                deduct_types={i.name: i.value for i in cls.model.deduct_types()},
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        biz_type=EnumField(model.BizType, required=True,
                           validate=lambda x: x in P2pUserMarginHistory.admin_biz_types()),
        user_email=fields.String(required=True),
        to_user_email=fields.String(),
        order_id=fields.String(),
        amount=fields.Decimal(required=True, validate=lambda x: x > 0),
        biz_remark=fields.String(validate=lambda x: len(x) < 512),
    ))
    def post(cls, **kwargs):
        """p2p-商家保证金收支表-创建"""
        user_id = cls.get_user_id_by_email(kwargs["user_email"])
        biz_type = kwargs['biz_type']
        if biz_type in cls.model.refund_types() and SignOffUser.is_signoff_user(user_id):
            raise InvalidArgument(message="商家账号已注销，无法退回现货账户")
        if biz_type == cls.model.BizType.EXCESS_REFUND:
            P2pUserMarginHistoryBiz.pending_excess_refund(
                user_id, kwargs['amount'], kwargs['biz_remark'])
        else:
            to_user_id = cls.get_user_id_by_email(kwargs.get('to_user_email'))
            if user_id == to_user_id:
                raise InvalidArgument(message="商家不能和用户相同")
            biz_remark = kwargs.get('biz_remark', "")
            order_id = kwargs.get('order_id')
            if order_id:
                order_id = P2pOrder.get_real_id(order_id)
            P2pUserMarginHistoryBiz.pending_deduct_for_user(
                user_id, to_user_id, kwargs['amount'], biz_type, biz_remark, order_id)

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True, validate=lambda x: len(x) < 256),
    ))
    def patch(cls, **kwargs):
        """p2p-商家保证金收支表-编辑备注"""
        row = cls.model.query.get(kwargs['id'])
        if not row:
            raise InvalidArgument
        row.remark = kwargs['remark']
        db.session.commit()

    @classmethod
    def get_user_id_by_email(cls, email):
        """通过邮箱获取用户ID"""
        if not email:
            raise InvalidArgument(message="缺少邮箱信息")
        user = User.query.filter(User.email == email).first()
        if not user:
            raise InvalidArgument(message="邮箱未注册")
        return user.id


@ns.route('/user_history/audit/<int:id_>')
@respond_with_code
class P2pUserMarginAuditHistoryResource(Resource):
    model = P2pUserMarginHistory

    @classmethod
    @require_admin_webauth_token
    def patch(cls, id_, **kwargs):
        """p2p-商家保证金收支表-审核"""
        his_row = cls.model.query.get(id_)
        if his_row.status != cls.model.Status.PENDING:
            raise InvalidArgument(message="记录状态不为待审核")
        if his_row.biz_type not in cls.model.admin_biz_types():
            raise InvalidArgument(message="该业务类型由系统审核")
        user_id = his_row.user_id
        user_margin = P2pUserMarginHistoryBiz.process_margin_trans(user_id, id_, g.user.id)
        # 更新商家保证金状态
        m_model = P2pMerchant
        mer = m_model.query.filter(m_model.user_id == user_id).first()
        ret = update_merchant_margin_status(mer, user_margin)
        db.session.commit()
        if ret and not not user_margin.margin_pass:
            no_margin_merchant_operate(user_id, user_margin.grace_deadline)


@ns.route('/user_history/cancel/<int:id_>')
@respond_with_code
class P2pUserMarginCancelHistoryResource(Resource):
    model = P2pUserMarginHistory

    @classmethod
    def patch(cls, id_, **kwargs):
        """p2p-商家保证金收支表-取消"""
        with CacheLock(LockKeys.p2p_margin_status(id_)):
            his_row = cls.model.query.get(id_)
            if his_row.status != cls.model.Status.PENDING:
                raise InvalidArgument(message="记录状态不为待审核")
            his_row.status = cls.model.Status.FAIL
            db.session.commit()
            return
