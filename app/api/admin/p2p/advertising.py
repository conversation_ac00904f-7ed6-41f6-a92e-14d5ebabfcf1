from enum import Enum

from marshmallow import fields

from flask import g

from app import Language
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import EnumField, LimitField, PageField, TimestampField
from app.business.p2p.advertising import P2pAdvertisingBiz, P2pAdvertisingManger
from app.business.p2p.utils import P2pUtils
from app.common import P2pBusinessType, get_country_code_cn_name_dic
from app.exceptions import RecordNotFound, InvalidArgument
from app.models import db, User, KycVerification
from app.models.mongo.p2p.advertising import P2pAdvertisingMySQL, P2pAdvertisingChangeLogMySQL, AutoOfflineAdvReason
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectFiat
from app.utils import batch_iter, format_percent, export_xlsx

ns = Namespace('P2p - advertising')


class AdminAdvTypeEnum(Enum):
    SELL = "卖出"
    BUY = "买入"


class AdminStatusEnum(Enum):
    OFFLINE = "未生效"
    ONLINE = "生效中"


@ns.route("")
@respond_with_code
class P2pAdvertisingResource(Resource):

    @classmethod
    def get_user_email_dic(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = User.query.filter(
                User.id.in_(ids)
            ).all()
            res.update({user.id: user.email for user in users})
        return res

    @classmethod
    def get_user_kyc_country_dict(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            res.update({
                u: c for u, c in
                KycVerification.query.filter(
                    KycVerification.user_id.in_(ids),
                    KycVerification.status == KycVerification.Status.PASSED
                ).with_entities(
                    KycVerification.user_id,
                    KycVerification.country
                ).all()
            })
        return res

    @classmethod
    def get_adv_all_number(cls):
        return {i.adv_number for i in P2pAdvertisingMySQL.query.with_entities(P2pAdvertisingMySQL.adv_number).all()}

    @classmethod
    def p2p_advertising_fiat_summary(cls):
        """p2p-广告列表-法币分布"""
        total = P2pAdvertisingMySQL.query.count()
        from sqlalchemy import func
        fiat_counts = db.session.query(
            P2pAdvertisingMySQL.quote,
            func.count(P2pAdvertisingMySQL.id).label('count')
        ).group_by(P2pAdvertisingMySQL.quote).all()
        
        fiat_count_list = [{"_id": fiat, "count": count} for fiat, count in fiat_counts]
        header_fiat = sorted(fiat_count_list, key=lambda x: -x['count'])[0:5]
        header_fiat_counts = sum([i["count"] for i in header_fiat])
        header_fiat.append({
            "_id": "其他",
            "count": total - header_fiat_counts
        })
        return [
            {
                "fiat": i["_id"],
                "rate": format_percent(i["count"] / total if total else 0)
            } for i in header_fiat
        ]

    export_headers = (
        {'field': 'adv_number', Language.ZH_HANS_CN: '广告ID'},
        {'field': 'user_email', Language.ZH_HANS_CN: '商家邮箱'},
        {'field': 'created_at', Language.ZH_HANS_CN: '创建时间'},
        {'field': 'updated_at', Language.ZH_HANS_CN: '更新时间'},
        {'field': 'adv_type', Language.ZH_HANS_CN: '广告方向'},
        {'field': 'base', Language.ZH_HANS_CN: '数字货币'},
        {'field': 'quote', Language.ZH_HANS_CN: '法币'},
        {'field': 'price', Language.ZH_HANS_CN: '价格'},
        {'field': 'country_code', Language.ZH_HANS_CN: 'kyc地区'},
        {'field': 'stocks_quantity', Language.ZH_HANS_CN: '数量'},
        {'field': 'min_limit', Language.ZH_HANS_CN: '限额'},
        {'field': 'status', Language.ZH_HANS_CN: '广告状态'},
    )

    @classmethod
    def get_export_data(cls, query_all):
        items = []
        user_ids = {i.user_id for i in query_all}
        country_name_mapper = get_country_code_cn_name_dic()
        country_code_mapper = cls.get_user_kyc_country_dict(user_ids)
        user_email_mapper = cls.get_user_email_dic(user_ids)
        for item in query_all:
            country_code = country_code_mapper.get(item.user_id)
            items.append(dict(
                adv_number=item.adv_number,
                user_email=user_email_mapper[item.user_id],
                created_at=item.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                updated_at=item.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                adv_type=item.adv_type.value,
                price=item.price,
                base=item.base,
                quote=item.quote,
                country_code=country_name_mapper.get("country_code") if country_code else "",
                stocks_quantity=item.stocks_quantity,
                min_limit=f"{item.min_limit}~{item.max_limit} {item.quote}",
                status=item.status.value,
            ))
        return items

    class SortTypeEnum(Enum):
        ASCENDING = "ascending"
        DESCENDING = "descending"

    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String,
        adv_type=EnumField(P2pBusinessType),
        status=EnumField(P2pAdvertisingMySQL.Status),
        asset=fields.String,
        adv_number=fields.String,
        user_id=fields.Integer,
        start_at=TimestampField(is_ms=True),
        end_at=TimestampField(is_ms=True),
        page=PageField(missing=1),
        limit=LimitField(missing=15),
        export=fields.Boolean(missing=False),
        sort_by=fields.String,
        sort_type=EnumField(SortTypeEnum, enum_by_value=True)
    ))
    def get(cls, **kwargs):
        """p2p-广告列表-获取列表"""
        page, limit = kwargs['page'], kwargs['limit']
        query = P2pAdvertisingMySQL.query
        if fiat := kwargs.get("fiat"):
            query = query.filter(P2pAdvertisingMySQL.quote == fiat)
        if asset := kwargs.get("asset"):
            query = query.filter(P2pAdvertisingMySQL.base == asset)
        if status := kwargs.get("status"):
            query = query.filter(P2pAdvertisingMySQL.status == status.value)
        if adv_type := kwargs.get("adv_type"):
            query = query.filter(P2pAdvertisingMySQL.adv_type == adv_type.value)
        if adv_number := kwargs.get("adv_number"):
            query = query.filter(P2pAdvertisingMySQL.adv_number == adv_number)
        if user_id := kwargs.get("user_id"):
            query = query.filter(P2pAdvertisingMySQL.user_id == user_id)
        if start_at := kwargs.get("start_at"):
            query = query.filter(P2pAdvertisingMySQL.updated_at >= start_at)
        if end_at := kwargs.get("end_at"):
            query = query.filter(P2pAdvertisingMySQL.updated_at <= end_at)
        if (sort_by := kwargs.get("sort_by")) and (sort_type := kwargs.get('sort_type')):
            if sort_type == cls.SortTypeEnum.ASCENDING:
                query = query.order_by(getattr(P2pAdvertisingMySQL, sort_by).asc())
            else:
                query = query.order_by(getattr(P2pAdvertisingMySQL, sort_by).desc())
        else:
            query = query.order_by(P2pAdvertisingMySQL.id.desc())
        
        total = query.count()
        
        if kwargs.get("export"):
            query_all = query.all()
            items = cls.get_export_data(query_all)
            return export_xlsx(
                filename='advertising_list',
                data_list=items,
                export_headers=cls.export_headers,
            )
        else:
            query = query.offset((page - 1) * limit).limit(limit)
            
        user_ids = {i.user_id for i in query}
        email_mapper = cls.get_user_email_dic(user_ids)
        kyc_country_mapper = cls.get_user_kyc_country_dict(user_ids)
        items = []
        for item in query:
            items.append(dict(
                **item.to_dict(enum_to_name=True),
                user_email=email_mapper.get(item.user_id),
                kyc_country=kyc_country_mapper.get(item.user_id),
            ))
        return dict(
            total=total,
            items=items,
            extra=dict(
                assets=P2pUtils.get_p2p_assets(),
                fiats=P2pUtils.get_all_p2p_fiats(),
                statuses=AdminStatusEnum,
                adv_types=AdminAdvTypeEnum,
                id_numbers=cls.get_adv_all_number(),
                fiat_summary=cls.p2p_advertising_fiat_summary(),
                country_code_mapper=get_country_code_cn_name_dic()
            ),
        )


@ns.route("/<string:adv_id>")
@respond_with_code
class P2pAdvertisingDetailResource(Resource):

    @classmethod
    def get(cls, adv_id: str):
        """p2p-广告列表-获取详情"""
        adv = P2pAdvertisingMySQL.query.filter_by(mongo_id=adv_id).first()
        if not adv:
            raise RecordNotFound
        info_dict = adv.to_dict(enum_to_name=True)
        user = User.query.get(adv.user_id)
        new_data = P2pAdvertisingBiz.gen_adv_channel_info(adv)
        change_logs = P2pAdvertisingChangeLogMySQL.get_adv_change_logs(adv.mongo_id)
        return dict(
            **info_dict,
            real_stocks_quantity=adv.real_stocks_quantity,
            user_email=user.email,
            user_avatar=user.extra.admin_avatar_url,
            pay_channel_info=new_data,
            statuses=AdminStatusEnum,
            adv_types=AdminAdvTypeEnum,
            change_logs=change_logs
        )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(P2pAdvertisingMySQL.Status, missing=P2pAdvertisingMySQL.Status.OFFLINE)
    ))
    def put(cls, adv_id: str, **kwargs):
        """p2p-广告列表-下架广告"""
        status = kwargs.get("status", P2pAdvertisingMySQL.Status.OFFLINE)
        adv = P2pAdvertisingMySQL.query.filter_by(mongo_id=adv_id).first()
        if not adv:
            raise RecordNotFound
        if adv.status != P2pAdvertisingMySQL.Status.ONLINE or status != P2pAdvertisingMySQL.Status.OFFLINE:
            raise InvalidArgument(message="禁止该操作")
        adv.status = status
        db.session.commit()
        if status == P2pAdvertisingMySQL.Status.OFFLINE:
            P2pAdvertisingManger(adv_id).auto_offline(AutoOfflineAdvReason.OFFLINE_BY_ADMIN)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PAdvertising,
            detail=dict(adv_number=adv.adv_number, id=str(adv.id)),
            target_user_id=adv.user_id,
        )
