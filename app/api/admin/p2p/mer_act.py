from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from flask import g, current_app
from marshmallow import fields

from app import Language
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import EnumField, LimitField, PageField, TimestampField
from app.assets import list_all_assets
from app.business import gift_history_to_revoke, gift_history_real_freeze, gift_history_unfreeze
from app.business.clients.p2p_fair_price import FairPriceBiz, MerActFairPriceBiz
from app.business.p2p.config import p2p_mer_act_settings
from app.business.p2p.mer_act import P2pMerActBiz
from app.business.p2p.message import P2pMerActAuditSuccess, P2pMerActAuditFail, P2pMerActAuditCancel, \
    P2pMerActRewardFreeze, P2pMerActRewardCancel, P2pMerActRewardSuccess
from app.business.p2p.utils import P2pUtils
from app.business.user import UserRepository
from app.caches import func
from app.caches.p2p import FiatPayChannelCache, PayChannelCache, P2pMerActCache
from app.common import P2pBusinessType, P2pMerActRewardType, language_name_cn_names
from app.exceptions import RecordNotFound, InvalidArgument
from app.models import User, P2pUser, db, \
    KycVerification, Activity, GiftHistory, P2pMerchant, LockedAssetBalance, P2pFiatFairPrice
from app.models.mongo.op_log import OPNamespaceObjectOperation, AdminOperationLogMySQL as AdminOperationLog
from app.models.mongo.p2p.mer_act import P2pMerActMySQL as P2pMerAct, ConfigModel
from app.models.p2p_mer_act import P2pMerActUser, P2pMerActRewardHistory, P2pMerActUserReward, HourP2pMerActPoint
from app.utils import AWSBucketPrivate, now, export_xlsx, current_timestamp, AWSBucketPublic
from app.utils.date_ import dt_to_today

ns = Namespace('P2p Merchant Activity')
url_prefix = '/mer-act'


class P2pMerActBase(Resource):
    model = P2pMerAct
    act_kwargs = dict(
        # P2pMerAct 的所有字段都作为参数
        name=fields.String(required=True),
        fiat=fields.String(required=True),
        start_at=TimestampField(is_ms=True, required=True),
        end_at=TimestampField(is_ms=True, required=True),
        apply_start_at=TimestampField(is_ms=True, required=True),
        apply_end_at=TimestampField(is_ms=True, required=True),
        valid_start_hour=fields.Integer(required=True, validate=lambda x: 0 <= x <= 23),
        valid_end_hour=fields.Integer(required=True, validate=lambda x: 1 <= x <= 24),
        reward_type=EnumField(P2pMerActRewardType, required=True),
        reward_asset=fields.String(required=True),
        pay_channel_ids=fields.List(fields.String(), required=True),
        max_limit=fields.Decimal(),
        min_limit=fields.Decimal(),
        buy_reward=fields.Decimal(),
        sell_reward=fields.Decimal(),
        reward_lock_day=fields.Integer(required=True),
        lang_map=fields.Dict(required=True),
        completion_rate_list=fields.List(fields.Dict, required=True),
        rank_rate_list=fields.List(fields.Dict, required=True),
        image_key=fields.String(),
    )

    @classmethod
    def get_by_act_id(cls, act_id):
        obj = cls.model.query.filter(cls.model.act_id == act_id).first()
        if not obj:
            raise RecordNotFound
        return obj


@ns.route("/list")
@respond_with_code
class P2pMerActList(P2pMerActBase):
    model = P2pMerAct

    @classmethod
    @ns.use_kwargs(dict(
        act_id=fields.Integer(),
        name=fields.String(),
        fiat=fields.String(),
        status=EnumField(model.Status),
        start_at=TimestampField(is_ms=True, to_utc=True),
        end_at=TimestampField(is_ms=True, to_utc=True),
        page=PageField(),
        limit=LimitField(),
    ))
    def get(cls, **kwargs):
        """p2p商家活动-活动管理-列表"""
        query = cls.model.query
        if act_id := kwargs.get('act_id'):
            query = query.filter(cls.model.act_id == act_id)
        if name := kwargs.get('name'):
            query = query.filter(cls.model.name.ilike(f'%{name}%'))
        if fiat := kwargs.get('fiat'):
            query = query.filter(cls.model.fiat == fiat)
        if start_at := kwargs.get('start_at'):
            query = query.filter(cls.model.start_at >= start_at)
        if end_at := kwargs.get('end_at'):
            query = query.filter(cls.model.end_at <= end_at)
        if status := kwargs.get('status'):
            query = query.filter(cls.model.status == status.name)
        page, limit = kwargs['page'], kwargs['limit']
        query = query.order_by(cls.model.act_id.desc())
        paginate = query.paginate(page, limit, error_out=False)
        rows = paginate.items
        items = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            act_days = row.get_act_days()
            item["id"] = item["mongo_id"]
            item["buy_reward"] = row.buy_reward * act_days if row.buy_reward else Decimal()
            item["sell_reward"] = row.sell_reward * act_days if row.sell_reward else Decimal()
            items.append(item)
        return dict(
            items=items,
            total=paginate.total,
            extra=dict(
                fiats=P2pUtils.get_valid_fiats(),
                id_name_map=cls.get_id_name_map(),
                reward_types=P2pMerActRewardType,
                statuses=cls.model.Status,
            )
        )

    @classmethod
    @ns.use_kwargs(P2pMerActBase.act_kwargs)
    def post(cls, **kwargs):
        """p2p商家活动-活动管理-创建活动"""
        row = cls.model.query.order_by(cls.model.act_id.desc()).first()
        new_act_id = row.act_id + 1 if row else 1
        
        # 处理Config类型的字段
        completion_rate_list = kwargs.pop("completion_rate_list", [])
        rank_rate_list = kwargs.pop("rank_rate_list", [])
        
        activity = Activity.get_or_create(name=f"p2p_mer_act_{new_act_id}")
        db.session_add_and_commit(activity)
        
        obj = P2pMerAct(
            act_id=new_act_id,
            activity_id=activity.id,
            **kwargs
        )
        
        # 处理JSON字段
        obj.completion_rate_list = [
            ConfigModel(
                rank_min=float(config["rank_min"]),
                rank_max=float(config["rank_max"]),
                rank_amount=float(config["rank_amount"])
            ) for config in completion_rate_list
        ]
        
        obj.rank_rate_list = [
            ConfigModel(
                rank_min=float(config["rank_min"]),
                rank_max=float(config["rank_max"]),
                rank_amount=float(config["rank_amount"])
            ) for config in rank_rate_list
        ]
        
        db.session_add_and_commit(obj)
        P2pMerActCache(obj.act_id).reload()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pMerAct,
            detail=kwargs,
        )
        return dict(act_id=new_act_id)

    @classmethod
    def get_id_name_map(cls):
        rows = cls.model.query.with_entities(cls.model.act_id, cls.model.name).order_by(cls.model.act_id.desc()).all()
        return {row.act_id: row.name for row in rows}

    @classmethod
    def get_all_act(cls, field_list):
        query = cls.model.query.order_by(cls.model.act_id.desc())
        return query.all()


@ns.route("/<int:act_id>")
@respond_with_code
class P2pMerActDetail(P2pMerActBase):
    model = P2pMerAct

    @classmethod
    def get(cls, act_id, **kwargs):
        """p2p商家活动-活动管理-活动详情"""
        item = {}
        if act_id != 0:
            item = cls.get_by_act_id(act_id).to_dict(enum_to_name=True)
            image_key = item.get("image_key")
            item["file_url"] = AWSBucketPublic.get_file_url(image_key) if image_key else ""
        return dict(
            item=item,
            extra=cls.get_extra(),
        )

    @classmethod
    @ns.use_kwargs(P2pMerActBase.act_kwargs)
    def put(cls, act_id, **kwargs):
        """p2p商家活动-活动管理-活动编辑"""
        obj = cls.get_by_act_id(act_id)
        old_obj = obj.to_dict(enum_to_name=True)
        
        # 处理Config类型的字段
        completion_rate_list = kwargs.pop("completion_rate_list", [])
        rank_rate_list = kwargs.pop("rank_rate_list", [])
        
        # 更新普通字段
        for k, v in kwargs.items():
            setattr(obj, k, v)
        
        # 处理JSON字段
        obj.completion_rate_list = [
            ConfigModel(
                rank_min=float(config["rank_min"]),
                rank_max=float(config["rank_max"]),
                rank_amount=float(config["rank_amount"])
            ) for config in completion_rate_list
        ]
        
        obj.rank_rate_list = [
            ConfigModel(
                rank_min=float(config["rank_min"]),
                rank_max=float(config["rank_max"]),
                rank_amount=float(config["rank_amount"])
            ) for config in rank_rate_list
        ]
        
        db.session.commit()
        P2pMerActCache(act_id).reload()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pMerAct,
            old_data=old_obj,
            new_data=obj.to_dict(enum_to_name=True),
        )
        return obj.act_id

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(model.Status, required=True)
    ))
    def patch(cls, act_id, **kwargs):
        """p2p商家活动-活动管理-状态编辑"""
        obj = cls.get_by_act_id(act_id)
        old_data = obj.to_dict()
        new_status = kwargs['status']
        if new_status == cls.model.Status.EARLY_OFFLINE:
            obj.early_offline()
        else:
            obj.status = new_status
            db.session.commit()
        P2pMerActCache(act_id).reload()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pMerAct,
            old_data=old_data,
            new_data=obj.to_dict(),
        )
        return obj.act_id

    @classmethod
    def get_extra(cls):
        return dict(
            reward_types=P2pMerActRewardType,
            reward_assets=list_all_assets(),
            fiats=P2pUtils.get_valid_fiats(),
            fiat_pay_channels=FiatPayChannelCache().get_all(),
            languages=language_name_cn_names(),
            pay_channels={k: v["name"] for k, v in PayChannelCache().get_all(Language.ZH_HANS_CN).items()}
        )


class P2pMerActUserBase(Resource):
    base_kwargs = dict(
        act_id=fields.Integer(),
        user_id=fields.Integer(),
        page=PageField(),
        limit=LimitField(),
    )

    @classmethod
    def get_user_id_by_email(cls, email):
        model = User
        rows = model.query.filter(model.email == email).with_entities(model.id)
        return [i.user_id for i in rows]

    @classmethod
    def base_search(cls, model, query, kwargs):
        if act_id := kwargs.get('act_id'):
            query = query.filter(model.act_id == act_id)
        if user_id := kwargs.get('user_id'):
            query = query.filter(model.user_id == user_id)
        return query

    @classmethod
    def get_kyc_country(cls, user_ids):
        model = KycVerification
        user_kyc = model.query.filter(
            model.user_id.in_(user_ids)
        ).with_entities(model.user_id, model.country)
        ret = {row.user_id: UserRepository.get_user_country_cn_name(row.country) for row in user_kyc}
        return ret

    @classmethod
    def get_p2p_name_map(cls, user_ids):
        model = P2pUser
        rows = model.query.filter(
            model.user_id.in_(user_ids)
        ).with_entities(model.user_id, model.nickname)
        return {row.user_id: row.nickname for row in rows}

    @classmethod
    def get_users_email(cls, user_ids):
        user = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email)
        return {row.id: row.email for row in user}

    @classmethod
    def add_users_info(cls, rows):
        user_ids = [row.user_id for row in rows]
        # 增加用户kyc地区
        user_kyc_country_dict = cls.get_kyc_country(user_ids)
        # 增加p2p名称
        user_p2p_map = P2pUser.get_users_map(*user_ids)
        email_amp = cls.get_users_email(user_ids)
        merchant_map = {i.user_id: i.created_at for i in P2pMerchant.query.filter(P2pMerchant.user_id.in_(user_ids))}
        ret = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            p2p_row = user_p2p_map[row.user_id]
            item['country'] = user_kyc_country_dict.get(row.user_id)
            item['nickname'] = p2p_row.nickname
            item['email'] = email_amp.get(row.user_id)
            item['p2p_id'] = p2p_row.merchant_id
            item['p2p_at'] = merchant_map[row.user_id]
            item['images'] = [AWSBucketPrivate.get_file_url(i) for i in row.images]
            item['pdfs'] = [AWSBucketPrivate.get_file_url(i) for i in row.pdfs]
            ret.append(item)
        return ret

    @classmethod
    def get_last_act_id(cls):
        row = P2pMerAct.query.order_by(P2pMerAct.act_id.desc()).first()
        return row.act_id if row else None

    @classmethod
    def get_user_extra(cls):
        return dict(
            id_name_map=P2pMerActList.get_id_name_map(),
            sides={
                P2pBusinessType.BUY.name: "买币区",
                P2pBusinessType.SELL.name: "卖币区",
            }
        )


@ns.route("/point/rank")
@respond_with_code
class P2pMerActPointRankResource(P2pMerActUserBase):
    """积分排行榜"""
    model = HourP2pMerActPoint

    @classmethod
    @ns.use_kwargs(dict(
        report_date=TimestampField(is_ms=True, to_utc=True),
        act_id=fields.Integer(),
        side=EnumField(P2pBusinessType, default=P2pBusinessType.BUY),
        user_id=fields.Integer(),
        email=fields.String(),
    ))
    def get(cls, **kwargs):
        """p2p商家活动-活动管理-积分排行榜"""
        if not kwargs.get("act_id"):
            kwargs["act_id"] = cls.get_last_act_id()
        act = P2pMerAct.query.filter(P2pMerAct.act_id == kwargs["act_id"]).first()
        report_date = kwargs.get("report_date") or P2pMerActBiz.get_act_newest_day(act)
        query = cls.base_search(cls.model, cls.model.query, kwargs)
        query = query.filter(
            cls.model.side == kwargs['side'],
            cls.model.report_hour >= report_date,
            cls.model.report_hour < report_date + timedelta(days=1)
        )
        point_rows = cls.get_point_rows(query)

        user_ids = [i.user_id for i in point_rows]
        user_p2p_name_map = cls.get_p2p_name_map(user_ids)

        u_model = User
        email_map = u_model.query.filter(u_model.id.in_(user_ids)).with_entities(u_model.id, u_model.email).all()
        email_map = {i.id: i.email for i in email_map}
        ret = []
        for rank, row in enumerate(point_rows, 1):
            ret.append(dict(
                nickname=user_p2p_name_map.get(row.user_id),
                email=email_map.get(row.user_id, row.user_id),
                point=row.sum_point,
                user_id=row.user_id,
                act_name=act.name,
                rank=rank,
                side=row.side.name,
                report_date=report_date,
                act_id=kwargs["act_id"],
            ))

        return dict(
            items=ret,
            act_id=kwargs["act_id"],
            report_date=report_date,
            extra=cls.get_user_extra()
        )

    @classmethod
    def get_point_rows(cls, query):
        rows = query.group_by(cls.model.user_id).with_entities(
            func.sum(cls.model.point).label("sum_point"),
            cls.model.user_id,
            cls.model.side
        ).order_by(
            func.sum(cls.model.point).desc()
        ).all()
        return rows


# 奖励记录
@ns.route("/user/reward/list")
@respond_with_code
class P2pMerActRewardList(P2pMerActUserBase):
    model = P2pMerActUser

    @classmethod
    @ns.use_kwargs(P2pMerActUserBase.base_kwargs)
    def get(cls, **kwargs):
        """p2p商家活动-积分奖励记录-列表"""
        if not kwargs.get("act_id"):
            kwargs["act_id"] = cls.get_last_act_id()
        query = cls.base_search(cls.model, cls.model.query, kwargs)
        query = query.filter(
            cls.model.status.in_([cls.model.Status.PASSED, cls.model.Status.CANCELLED])
        )
        paginate = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        tmp_items = paginate.items
        user_ids = [i.user_id for i in paginate.items]

        r_model = P2pMerActUserReward
        reward_rows = r_model.query.filter(
            r_model.act_id == kwargs["act_id"],
            r_model.user_id.in_(user_ids)
        ).group_by(
            r_model.user_id,
        ).with_entities(
            r_model.user_id,
            func.sum(r_model.lock_amount).label("sum_lock_amount"),
            func.sum(r_model.release_amount).label("sum_release_amount"),
        ).all()
        reward_map = {i.user_id: i for i in reward_rows}

        act_rows = P2pMerActList.get_all_act(['act_id', 'name', 'reward_type', 'reward_asset'])
        act_map = {i.act_id: i for i in act_rows}
        user_p2p_name_map = cls.get_p2p_name_map(user_ids)
        items = []
        for row in tmp_items:
            reward = reward_map.get(row.user_id)
            lock_amount = reward.sum_lock_amount if reward else Decimal()
            release_amount = reward.sum_release_amount if reward else Decimal()
            act = act_map[row.act_id]
            item = dict(
                user_id=row.user_id,
                act_id=row.act_id,
                reward_type=act.reward_type,
                asset=act.reward_asset,
                nickname=user_p2p_name_map.get(row.user_id),
                lock_amount=lock_amount,
                release_amount=release_amount,
                sum_amount=lock_amount + release_amount,
            )
            items.append(item)
        return dict(
            items=items,
            total=paginate.total,
            act_id=kwargs["act_id"],
            extra=dict(
                id_name_map={row.act_id: row.name for row in act_rows},
                reward_type_map={row.act_id: row.reward_type for row in act_rows},
            )
        )


@ns.route("/user/point/list")
@respond_with_code
class P2pMerActPointList(P2pMerActUserBase):
    model = HourP2pMerActPoint

    @classmethod
    @ns.use_kwargs(dict(
        **P2pMerActUserBase.base_kwargs,
        fiat=fields.String(),
        side=EnumField(P2pBusinessType),
        report_date=TimestampField(is_ms=True, to_utc=True),
    ))
    def get(cls, **kwargs):
        """p2p商家活动-积分明细-列表"""
        if not kwargs.get("act_id"):
            kwargs["act_id"] = cls.get_last_act_id()
        query = cls.base_search(cls.model, cls.model.query, kwargs)
        for field in ['fiat', 'side']:
            if value := kwargs.get(field):
                query = query.filter(getattr(cls.model, field) == value)
        if report_date := kwargs.get('report_date'):
            query = query.filter(cls.model.report_hour >= report_date,
                                 cls.model.report_hour < report_date + timedelta(days=1))
        query = query.order_by(cls.model.report_hour.desc(), cls.model.updated_at.desc())
        paginate = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        user_ids = [i.user_id for i in paginate.items]
        user_p2p_name_map = cls.get_p2p_name_map(user_ids)
        items = []
        for row in paginate.items:
            item = row.to_dict(enum_to_name=True)
            item['nickname'] = user_p2p_name_map.get(row.user_id)
            items.append(item)
        return dict(
            items=items,
            total=paginate.total,
            act_id=kwargs["act_id"],
            extra={
                **cls.get_user_extra(),
                'point_types': cls.model.PointType
            }
        )


# 奖励记录
@ns.route("/user/reward/detail")
@respond_with_code
class P2pMerActRewardDetail(P2pMerActUserBase):
    model = P2pMerActRewardHistory
    g_model = GiftHistory

    LOCK_STATUSES = {
        g_model.Status.CREATED.name: "待解锁",
        g_model.Status.LOCKED.name: "待解锁",
        g_model.Status.FROZEN.name: "待解锁",
        g_model.Status.REAL_FROZEN.name: "已冻结",
        g_model.Status.TO_REVOKE.name: "奖励撤销中",
        g_model.Status.REVOKED.name: "奖励撤销",
        g_model.Status.FINISHED.name: "已解锁",
        g_model.Status.CANCELLED.name: "未发放",
    }

    @classmethod
    @ns.use_kwargs(dict(
        **P2pMerActUserBase.base_kwargs,
        reward_date=TimestampField(is_ms=True, to_utc=True),
    ))
    def get(cls, **kwargs):
        """p2p商家活动-奖励明细-列表"""
        query = cls.base_search(cls.model, cls.model.query, kwargs)
        if reward_date := kwargs.get('reward_date'):
            query = query.filter_by(reward_date=reward_date)
        query = query.order_by(cls.model.id.desc())
        paginate = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        page_items = paginate.items
        user_map = P2pUser.get_users_map(*[i.user_id for i in page_items])
        act = P2pMerAct.query.filter(P2pMerAct.act_id == kwargs["act_id"]).with_entities(P2pMerAct.fiat).first()
        items = []
        gift_ids = [i.gift_id for i in page_items]
        gift_statsu_map = {i.id: i.status.name for i in cls.g_model.query.filter(cls.g_model.id.in_(gift_ids))}
        l_model = LockedAssetBalance
        lock_rows = LockedAssetBalance.query.filter(
            l_model.business == LockedAssetBalance.Business.GIFT,
            l_model.business_id.in_(gift_ids)
        ).with_entities(l_model.business_id, l_model.unlocked_at).all()
        lock_map = {i.business_id: i.unlocked_at for i in lock_rows}
        for row in page_items:
            item = row.to_dict(enum_to_name=True)
            uid = row.user_id
            gift_id = row.gift_id
            item.update(dict(
                fiat=act.fiat,
                nickname=user_map[uid].nickname,
                lock_status=gift_statsu_map.get(row.gift_id, cls.g_model.Status.CANCELLED.name),
                merchant_status="正常" if row.merchant_status == P2pMerchant.Status.ACTIVE else "异常",
                unlocked_at=lock_map.get(gift_id),
            ))
            items.append(item)

        return dict(
            items=items,
            total=paginate.total,
            extra={
                **cls.get_user_extra(),
                "release_statuses": cls.model.ReleaseStatus,
                "lock_statuses": cls.LOCK_STATUSES,
                "perm_statuses": cls.model.PermStatus,
            }
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        status=EnumField(GiftHistory.Status, required=True),
    ))
    def patch(cls, **kwargs):
        """p2p商家活动-奖励记录-状态编辑"""
        row = cls.model.query.get(kwargs['id'])
        if not row:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)
        status = kwargs['status']
        if status == cls.g_model.Status.REAL_FROZEN:
            gift_history_real_freeze(row.gift_id)
        elif status == cls.g_model.Status.FINISHED:
            gift_history_unfreeze(row.gift_id)
        elif status == cls.g_model.Status.REVOKED:
            ret = gift_history_to_revoke(row.gift_id)
            if not ret:
                raise InvalidArgument(message="临近解锁时间(5分钟内), 奖励可能已经在发放，无法撤销")
        cls.send_message(row, kwargs['status'])

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pMerAct,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    def send_message(cls, row: model, status):
        match status:
            case cls.g_model.Status.REAL_FROZEN:
                P2pMerActRewardFreeze(row.act_id).send_message(row.user_id)
            case cls.g_model.Status.REVOKED:
                P2pMerActRewardCancel(row.act_id).send_message(row.user_id)
            case cls.g_model.Status.FINISHED:
                P2pMerActRewardSuccess(row.act_id).send_message(
                    row.user_id, reward_date=row.reward_date.strftime("%Y-%m-%d"))


@ns.route("/user/list")
@respond_with_code
class P2pMerActUserList(P2pMerActUserBase):
    model = P2pMerActUser

    @classmethod
    @ns.use_kwargs(dict(
        **P2pMerActUserBase.base_kwargs,
        status=EnumField(model.Status),
    ))
    def get(cls, **kwargs):
        """p2p商家活动-报名用户-列表"""
        query = cls.base_search(cls.model, cls.model.query, kwargs)
        if status := kwargs.get('status'):
            query = query.filter(cls.model.status == status)
        query = query.order_by(cls.model.id.desc())
        paginate = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        items = cls.add_users_info(paginate.items)
        return dict(
            items=items,
            total=paginate.total,
            extra={
                **cls.get_user_extra(),
                "statuses": cls.model.Status,
            },
        )


@ns.route("/user/next")
@respond_with_code
class P2pMerActUserNextList(P2pMerActUserBase):
    model = P2pMerActUser

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        act_id=fields.Integer(required=True),
    ))
    def get(cls, **kwargs):
        """p2p商家活动-报名用户-下一个审核"""
        rows = cls.model.query.filter(
            cls.model.status == cls.model.Status.CREATED,
        ).order_by(cls.model.act_id).with_entities(cls.model.id).all()
        if not rows:
            return dict(next_id=None)
        ids = [i.id for i in rows]
        cur_id = kwargs["id"]
        if cur_id in ids:
            idx = (ids.index(cur_id) + 1) % len(ids)
            next_id = ids[idx]
        else:
            next_id = ids[0]
        return dict(
            next_id=next_id,
        )


@ns.route("/user/<int:id_>")
@respond_with_code
class P2pMerActUserDetail(P2pMerActUserBase):
    model = P2pMerActUser

    @classmethod
    def get_by_id(cls, id_):
        user = cls.model.query.get(id_)
        if not user:
            raise RecordNotFound
        return user

    @classmethod
    def get(cls, id_, **kwargs):
        """p2p商家活动-报名用户-审核详情"""
        user = cls.get_by_id(id_)
        item = cls.add_users_info([user])[0]
        act = P2pMerAct.query.filter(P2pMerAct.act_id == item["act_id"]).with_entities(P2pMerAct.name).first()
        item["act_name"] = act.name if act else ""
        return dict(
            item=item,
            statuses=cls.model.Status,
        )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(model.Status),
        remark=fields.String(validate=lambda x: len(x) <= 200),
    ))
    def patch(cls, id_, **kwargs):
        """p2p商家活动-报名用户-修改状态"""
        row = cls.get_by_id(id_)
        old_data = row.to_dict()

        if status := kwargs.get('status'):
            row.status = status
            if status == cls.model.Status.CANCELLED:
                # 积分清零
                cls.clear_user_point(row)
                # 回收奖励
                cls.revoke_user_reward(row)
            elif status == cls.model.Status.PASSED:
                row.pass_at = now()
            db.session.commit()
            cls.send_message(row, status)

        if remark := kwargs.get('remark'):
            row.remark = remark
            db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pMerActUser,
            old_data=old_data,
            new_data=row.to_dict(),
        )

    @classmethod
    def send_message(cls, row: model, status):
        match status:
            case cls.model.Status.PASSED:
                P2pMerActAuditSuccess(row.act_id).send_message(row.user_id)
            case cls.model.Status.REJECTED:
                P2pMerActAuditFail(row.act_id).send_message(row.user_id)
            case cls.model.Status.CANCELLED:
                P2pMerActAuditCancel(row.act_id).send_message(row.user_id)

    @classmethod
    def clear_user_point(cls, row):
        act: P2pMerAct = P2pMerActBase.get_by_act_id(row.act_id)
        hour = now().replace(minute=0, second=0, microsecond=0)
        for side in act.get_valid_side():
            P2pMerActBiz.save_point_history(
                act, hour, side, {row.user_id: Decimal()}, HourP2pMerActPoint.PointType.REVOKE
            )
        db.session.commit()

    @classmethod
    def revoke_user_reward(cls, row):
        # 获取用户所有未解锁奖励
        model = P2pMerActRewardHistory
        g_model = GiftHistory
        rows = model.query.join(
            g_model, model.gift_id == g_model.id
        ).filter(
            model.user_id == row.user_id,
            model.act_id == row.act_id,
            g_model.status.in_([g_model.Status.LOCKED, g_model.Status.REAL_FROZEN])
        ).with_entities(
            model.gift_id
        )
        gift_ids = [i.gift_id for i in rows]
        for gift_id in gift_ids:
            try:
                gift_history_to_revoke(gift_id)
            except Exception as e:
                current_app.logger.error(f"mer act user: {row.user_id} revoke gift_id: {gift_id} reward error: {e}")
        if gift_ids:
            P2pMerActRewardCancel(row.act_id).send_message(row.user_id)


# 活动配置
@ns.route("/config")
@respond_with_code
class P2pMerActConfig(Resource):

    @classmethod
    def get(cls):
        """p2p商家活动-查看活动配置"""
        items = {i["name"]: i["value"] for i in p2p_mer_act_settings.fields_and_values_json}
        return items


# 公允价格模块
@ns.route("/fair-price")
@respond_with_code
class P2pMerActFairPrice(Resource):
    model = P2pFiatFairPrice

    @classmethod
    def get(cls):
        """p2p商家活动-查看公允价格-列表"""
        rows = cls.model.query.all()
        return dict(
            items=[row.to_dict(enum_to_name=True) for row in rows],
            price_types=cls.model.PriceType,
            fiats=P2pUtils.get_valid_fiats(),
        )

    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """p2p商家活动-创建公允价格"""
        fiat = kwargs['fiat']
        if cls.model.query.filter(cls.model.fiat == fiat).first():
            raise InvalidArgument(message="当前法币已添加")
        obj = MerActFairPriceBiz.handle_mer_fair_price([fiat])
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pMerActFair,
            detail=obj.to_dict(),
        )

        return {'price': obj.price}

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        manual_price=fields.Decimal,
        rule=fields.Dict(keys=EnumField(P2pFiatFairPrice.VERSION_1_RULE.keys()), values=fields.String()),
        price_type=EnumField(model.PriceType),
    ))
    def put(cls, **kwargs):
        """p2p商家活动-修改公允价格"""
        if rule := kwargs.get('rule'):
            cls.check_rule(rule)
        row = cls.model.query.get(kwargs.pop('id'))
        if not row:
            raise RecordNotFound
        old_data = row.to_dict()
        need_snap = cls.check_need_snap(row, kwargs)
        for k, v in kwargs.items():
            setattr(row, k, v)
        if need_snap:
            FairPriceBiz.save_fair_snapshot(row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pMerActFair,
            old_data=old_data,
            new_data=row.to_dict(),
        )

    @classmethod
    def check_rule(cls, rule):
        for v in rule.values():
            try:
                Decimal(v)
            except Exception:
                raise InvalidArgument(message="区间值必须为数字")

    @classmethod
    def check_need_snap(cls, row, kwargs):
        for field in ['manual_price', 'rule', 'price_type']:
            if (value := kwargs.get(field)) and value != getattr(row, field):
                return True


# 公允价格模块
@ns.route("/user-point-export")
@respond_with_code
class UserPointExport(Resource):
    export_headers = (
        {"field": "minuter", Language.ZH_HANS_CN: "时间"},
        {"field": "act_id", Language.ZH_HANS_CN: "活动ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "adv_number", Language.ZH_HANS_CN: "广告ID"},
        {"field": "side", Language.ZH_HANS_CN: "广告方向"},
        {"field": "rank", Language.ZH_HANS_CN: "广告排名"},
        {"field": "adv_price", Language.ZH_HANS_CN: "广告价格"},
        {"field": "price", Language.ZH_HANS_CN: "公允价格"},
        {"field": "rule", Language.ZH_HANS_CN: "范围规则"},
        {"field": "price_type", Language.ZH_HANS_CN: "公允价格类型"},
        {"field": "point_type", Language.ZH_HANS_CN: "积分类型"},
        {"field": "point", Language.ZH_HANS_CN: "积分(保留4位小数，不足0.0001为0)"},
        {"field": "sort_factor", Language.ZH_HANS_CN: "排序因子(保留8位) = max(0, (1 - 排名 / 100))"},
        {"field": "offset_percent", Language.ZH_HANS_CN: "偏离百分比(保留8位) = abs(广告价格 - 公允价格) / 公允价格"},
        {"field": "tmp_point", Language.ZH_HANS_CN: "积分计算公式 排序因子 * max(0, 1 - 偏离百分比) * 100"},
        {"field": "finish_rate", Language.ZH_HANS_CN: "完单率"},
        {"field": "day_rate", Language.ZH_HANS_CN: "完单率奖励比例"},
        {"field": "order_info", Language.ZH_HANS_CN: "每日膨胀计算公式"},
        {"field": "error", Language.ZH_HANS_CN: "无效原因"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        act_id=fields.Integer(required=True),
        user_id=fields.Integer(required=True),
        start_at=TimestampField(),
        end_at=TimestampField(),
        side=EnumField(P2pBusinessType),
    ))
    def get(cls, **kwargs):
        """p2p商家活动-导出用户每分钟积分详情"""
        act = P2pMerAct.query.filter(P2pMerAct.act_id == kwargs["act_id"]).first()
        all_items = P2pMerActBiz.export_users_act_point(
            act,
            [kwargs["user_id"]],
            kwargs.get("start_at"),
            kwargs.get("end_at"),
            kwargs.get("side")
        )

        return export_xlsx(
            filename=f'point-{kwargs["user_id"]}-{kwargs["act_id"]}-{current_timestamp(to_int=True)}',
            data_list=all_items,
            export_headers=cls.export_headers,
        )


# 公允价格模块
@ns.route("/user-reward-export")
@respond_with_code
class UserRewardExport(Resource):
    export_headers = (
        {"field": "reward_date", Language.ZH_HANS_CN: "时间"},
        {"field": "act_id", Language.ZH_HANS_CN: "活动ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "side", Language.ZH_HANS_CN: "广告方向"},
        {"field": "rank", Language.ZH_HANS_CN: "当日积分排名"},
        {"field": "point", Language.ZH_HANS_CN: "当日积分"},
        {"field": "asset", Language.ZH_HANS_CN: "奖励币种"},
        {"field": "amount", Language.ZH_HANS_CN: "币种数量"},
        {"field": "rank_rate", Language.ZH_HANS_CN: "奖励比例"},
        {"field": "error", Language.ZH_HANS_CN: "错误原因"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        act_id=fields.Integer(required=True),
    ))
    def get(cls, **kwargs):
        """p2p商家活动-导出活动奖励详情"""
        act = P2pMerAct.query.filter(P2pMerAct.act_id == kwargs["act_id"]).first()
        st = dt_to_today(act.start_at)
        all_items = P2pMerActBiz.export_user_reward_history(act, st)

        return export_xlsx(
            filename=f'reward-{kwargs["act_id"]}-{current_timestamp(to_int=True)}',
            data_list=all_items,
            export_headers=cls.export_headers,
        )
