import json
from collections import defaultdict
from enum import Enum

from flask import g, request
from marshmallow import fields
from sqlalchemy import or_

from app import Language
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.decorators import check_p2p_site_setting
from app.api.common.fields import <PERSON>umField, LimitField, PageField, TimestampField
from app.business.clients.im import ImServerClient, ImContentType
from app.business.p2p.order import P2pOrderFileBus
from app.business.p2p.order_complaint import P2pOrderComplaintBiz, P2pComplaintRecordBiz
from app.business.p2p.order_factor import P2pOrderFactor
from app.business.p2p.utils import P2pUtils, export_amount
from app.common import P2pBusinessType
from app.exceptions import InvalidArgument
from app.models import User, P2pOrder, P2pOrderComplaint, \
    P2pOrderComplaintRecord, db, P2p<PERSON><PERSON>, <PERSON>rEx<PERSON>, P2p<PERSON>lusNRule, P2p<PERSON>ser<PERSON>lusNRecord, P2p<PERSON><PERSON><PERSON>tReply, \
    P2pOrderComplaintAnchor
from app.models.mongo.p2p.advertising import P2pAdvertisingMySQL
from app.models.mongo.p2p.order import P2pOrderCreateSnapMySQL
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectFiat
from app.models.wallet import WithdrawalRestrictedFund, WithdrawalRestrictedFundChangeLog
from app.utils import export_xlsx, datetime_to_str, timestamp_to_datetime, now, AWSBucketPrivate
from app.utils.importer import get_table_rows

ns = Namespace('P2p - order')


class P2pOrderAdminBase(Resource):
    model = P2pOrder
    c_model = P2pOrderComplaint

    class AdminSideEnum(Enum):
        BUY = "买入"
        SELL = "卖出"

    @classmethod
    def get_user_email_dic(cls, user_ids):
        res = dict()
        users = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email).all()
        res.update({user.id: user.email for user in users})
        return res

    @classmethod
    def get_user_avatar(cls, user_ids):
        return {
            ue.user_id: ue.admin_avatar_url for ue in UserExtra.query.filter(
                UserExtra.user_id.in_(user_ids)
            ).all()
        }

    @classmethod
    def get_enum_info(cls):
        return dict(
            statuses={i.name: i.value for i in cls.model.Status},
            sides={i.name: i.value for i in cls.AdminSideEnum},
            cancel_reasons={i.name: i.value for i in cls.model.CancelReason},
            cancel_types={i.name: i.value for i in cls.model.CancelType},
            fiats=P2pUtils.get_all_p2p_fiats(),
            assets_list=P2pUtils.get_p2p_assets(),
        )


@ns.route("")
@respond_with_code
class P2pOrderResource(P2pOrderAdminBase):
    export_headers = (
        {"field": "order_id", Language.ZH_HANS_CN: "订单ID"},
        {"field": "adv_number", Language.ZH_HANS_CN: "广告ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "base", Language.ZH_HANS_CN: "币种"},
        {"field": "quote", Language.ZH_HANS_CN: "法币"},
        {"field": "side", Language.ZH_HANS_CN: "订单发起方向"},
        {"field": "customer_email", Language.ZH_HANS_CN: "邮箱(交易者)"},
        {"field": "merchant_email", Language.ZH_HANS_CN: "邮箱(商家)"},
        {"field": "quote_amount", Language.ZH_HANS_CN: "订单金额"},
        {"field": "asset_amount", Language.ZH_HANS_CN: "数字资产数量"},
        {"field": "price", Language.ZH_HANS_CN: "价格"},
        {"field": "status", Language.ZH_HANS_CN: "订单状态"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        order_id=fields.Integer,
        side=EnumField(P2pBusinessType),
        status=EnumField(P2pOrder.Status),
        base=fields.String,
        quote=fields.String,
        adv_id=fields.String,
        adv_number=fields.String,
        email=fields.String,
        user_id=fields.Integer,
        user_id2=fields.Integer,
        start_at=TimestampField(is_ms=True),
        end_at=TimestampField(is_ms=True),
        page=PageField,
        limit=LimitField,
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """p2p-订单-获取列表"""
        query = cls.model.query
        ret = {
            "total": 0,
            "items": [],
            "extra": cls.get_enum_info()
        }

        user_id = kwargs.get("user_id")
        user_id2 = kwargs.get("user_id2")

        if user_id and user_id2:
            query = cls.model.get_two_user_query(user_id, user_id2)
        elif tmp_id := (user_id or user_id2):
            query = cls.model.get_user_query(tmp_id)

        for field in ["base", "quote", "status", "order_id", "side", "adv_id"]:
            if val := kwargs.get(field):
                query = query.filter(getattr(cls.model, field) == val)
        if adv_number := kwargs.get("adv_number"):
            adv = P2pAdvertisingMySQL.query.filter_by(adv_number=adv_number).first()
            if not adv:
                return ret
            query = query.filter(cls.model.adv_id == adv.mongo_id)
        if start_at := kwargs.get("start_at"):
            query = query.filter(cls.model.created_at >= start_at)
        if end_at := kwargs.get("end_at"):
            query = query.filter(cls.model.created_at < end_at)
        query = query.order_by(cls.model.id.desc())
        if kwargs.get("export"):
            rows = query.all()
            return export_xlsx(filename='p2p_order',
                               data_list=cls.format_export(rows),
                               export_headers=cls.export_headers)

        paginate = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        items, total = paginate.items, paginate.total
        user_ids = set()
        for item in items:
            user_ids.update((item.customer_id, item.merchant_id))
        email_mapper = cls.get_user_email_dic(user_ids)
        adv_num_map = cls.get_adv_num_map([i.adv_id for i in items])
        new_items = []
        for item in items:
            tmp = item.to_dict(enum_to_name=True)
            tmp["adv_number"] = adv_num_map[item.adv_id]
            tmp["customer_email"] = email_mapper.get(item.customer_id)
            tmp["merchant_email"] = email_mapper.get(item.merchant_id)
            new_items.append(tmp)
        ret.update(
            total=total,
            items=new_items
        )
        return ret

    @classmethod
    def format_export(cls, rows):
        user_ids = set()
        adv_ids = set()
        for row in rows:
            user_ids.update((row.customer_id, row.merchant_id))
            adv_ids.add(row.adv_id)
        email_mapper = cls.get_user_email_dic(user_ids)
        adv_num_map = cls.get_adv_num_map(adv_ids)
        items = []
        for row in rows:
            item = row.to_dict()
            item["status"] = row.status.value
            item["adv_number"] = adv_num_map[row.adv_id]
            item["side"] = "买入" if row.side == P2pBusinessType.BUY else "卖出"
            item["quote_amount"] = f"{row.quote_amount} {row.quote}"
            item["asset_amount"] = f"{row.base_amount} {row.base}"
            item["price"] = f"{row.price} {row.quote}"
            item["customer_email"] = email_mapper.get(row.customer_id)
            item["merchant_email"] = email_mapper.get(row.merchant_id)
            item["created_at"] = datetime_to_str(row.created_at)
            items.append(item)
        return items

    @classmethod
    def get_adv_num_map(cls, adv_ids):
        adv_num_map = {adv.mongo_id: adv.adv_number
                       for adv in P2pAdvertisingMySQL.query.filter(P2pAdvertisingMySQL.mongo_id.in_(adv_ids)).all()}
        return adv_num_map


@ns.route("/<int:order_id>")
@respond_with_code
class P2pOrderDetailResource(P2pOrderAdminBase):

    @classmethod
    def get(cls, order_id, **kwargs):
        """p2p-订单-获取订单详情"""
        order: P2pOrder = cls.model.query.get(order_id)
        new_order = P2pOrderFactor.format_one_order(g.user.id, order)
        email_mapper = cls.get_user_email_dic({order.customer_id, order.merchant_id})
        if order.cert_file_ids:
            cert_file_urls, _ = P2pOrderFileBus.get_p2p_file_urls(json.loads(order.cert_file_ids))
        else:
            cert_file_urls = []
        new_order.update(
            cert_file_urls=cert_file_urls,
            source_customer_id=order.customer_id,
            source_merchant_id=order.merchant_id,
            t_plus_n=cls.get_t_plus_n(order),
            admin_remark=order.admin_remark,
            **cls.add_complaint_statistic(order.customer_id, order.merchant_id)
        )
        im_user_map = P2pOrderFactor.get_im_user_map(order)
        snap_data = P2pOrderCreateSnapMySQL.get_by_order_id(order.id)
        email_mapper.update({v: email_mapper[k] for k, v in im_user_map.items()})
        ret = dict(
            order=new_order,
            adv=snap_data.adv,
            im_msgs=cls.get_im_msg(order),
            extra=cls.get_enum_info(),
            avatar_map=cls.get_user_avatar([order.customer_id, order.merchant_id]),
            email_map=email_mapper,
            im_user_map=im_user_map,
        )
        return ret

    @classmethod
    @ns.use_kwargs(dict(
        admin_remark=fields.String(required=True),
    ))
    def patch(cls, order_id, **kwargs):
        """p2p-订单-修改订单信息"""
        order: P2pOrder = cls.model.query.get(order_id)
        if not order:
            raise InvalidArgument(message="订单不存在")
        old_remark = order.admin_remark
        order.admin_remark = kwargs["admin_remark"]
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2POrder,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=order.admin_remark),
            target_user_id=order.customer_id,
        )

    @classmethod
    def get_t_plus_n(cls, order):
        now_ = now()
        restricted_fund = WithdrawalRestrictedFund.query.filter(
            WithdrawalRestrictedFund.biz_type == WithdrawalRestrictedFund.BizType.P2P,
            WithdrawalRestrictedFund.biz_id == order.id,
            WithdrawalRestrictedFund.status == WithdrawalRestrictedFund.StatusType.NORMAL,
            WithdrawalRestrictedFund.expired_at >= now_
        ).first()
        if not restricted_fund:
            return {}

        change_logs = []
        rule_ids = set()
        logs = WithdrawalRestrictedFundChangeLog.query.filter(
                WithdrawalRestrictedFundChangeLog.record_id == restricted_fund.id
        ).order_by(WithdrawalRestrictedFundChangeLog.id.desc()).all()
        for log in logs:
            log: WithdrawalRestrictedFundChangeLog
            old = log.old_data or {}
            new = log.new_data
            if old:
                rule_ids.add(old['t_plus_n_rule_id'])
            rule_ids.add(new['t_plus_n_rule_id'])
            change_logs.append(dict(
                change_time=log.created_at,
                old_rule_id=old['t_plus_n_rule_id'] if old else None,
                old_t_plus_n_days=old['t_plus_n_days'] if old else None,
                old_expired_at=timestamp_to_datetime(old['expired_at']) if old else None,
                new_rule_id=new['t_plus_n_rule_id'],
                new_t_plus_n_days=new['t_plus_n_days'],
                new_expired_at=timestamp_to_datetime(new['expired_at']),
            ))
        rules = P2pTPlusNRule.query.filter(
            P2pTPlusNRule.id.in_(rule_ids)
        ).with_entities(
            P2pTPlusNRule.id,
            P2pTPlusNRule.name,
        ).all()
        rule_name_mapper = {r.id: r.name for r in rules}
        rule_name_mapper.update({
            P2pUserTPlusNRecord.GLOBAL_SETTING_RULE_ID: P2pUserTPlusNRecord.GLOBAL_SETTING_RULE_NAME,
            P2pUserTPlusNRecord.USER_SETTING_RULE_ID: P2pUserTPlusNRecord.USER_SETTING_RULE_NAME,
        })
        for r in change_logs:
            r['old_rule_name'] = rule_name_mapper.get(r['old_rule_id'], '')
            r['new_rule_name'] = rule_name_mapper.get(r['new_rule_id'], '')
        t_plus_n_days = change_logs[0]['new_t_plus_n_days']
        return {
            't_plus_n_days': t_plus_n_days,
            'expired_at': restricted_fund.expired_at,
            'change_logs': change_logs
        }

    @classmethod
    def get_im_msg(cls, order):
        cus_im_id = P2pUser.get_by_user_id(order.customer_id).biz_user_id
        msgs = ImServerClient().get_order_im_msg(cus_im_id, order.session_id)
        now_order_id = ""
        show_msgs = []
        for msg in msgs:
            content = msg["content"]
            match ImContentType(msg["content_type"]):
                case ImContentType.IMAGE:
                    content = json.loads(content)
                    content = AWSBucketPrivate.get_file_url(content["pic"]["key"])
                    show_msgs.append(msg)
                case ImContentType.VIDEO:
                    content = json.loads(content)
                    if _key := content["vid"].get("key"):
                        content = AWSBucketPrivate.get_file_url(_key)
                        show_msgs.append(msg)
                case ImContentType.ORDER_CARD:
                    content = json.loads(content)
                    now_order_id = content['order_id']
                    content = f"新增订单状态：待接单"
                    show_msgs.append(msg)
                case ImContentType.TEXT_NOTICE:
                    content = json.loads(content)
                    if content["message_enum"] == P2pOrder.Status.CONFIRMED.name:
                        content = f"新增订单状态：已接单"
                        show_msgs.append(msg)
                case ImContentType.TEXT:
                    show_msgs.append(msg)
                case ImContentType.SAY_HI:
                    show_msgs.append(msg)
            msg["content"] = content
            msg["order_id"] = now_order_id

        show_ids = {i["order_id"] for i in show_msgs}
        new_id_map = {i.order_id: i.id for i in cls.model.query.filter(
            cls.model.order_id.in_(show_ids)
        ).with_entities(
            cls.model.order_id, cls.model.id
        ).all()}
        for msg in show_msgs:
            msg["id"] = new_id_map.get(msg["order_id"])

        return show_msgs

    @classmethod
    def add_complaint_statistic(cls, user_id, mer_id):
        ids = [user_id, mer_id]
        rows = cls.c_model.query.filter(
            or_(
                cls.c_model.plaintiff_id.in_(ids),
                cls.c_model.defendant_id.in_(ids),
            )
        ).with_entities(cls.c_model.plaintiff_id, cls.c_model.defendant_id).all()
        tmp = defaultdict(int)
        for row in rows:
            if row.plaintiff_id == user_id:
                tmp["user_plaintiff_count"] += 1
            if row.defendant_id == user_id:
                tmp["user_defendant_count"] += 1
            if row.plaintiff_id == mer_id:
                tmp["mer_plaintiff_count"] += 1
            if row.defendant_id == mer_id:
                tmp["mer_defendant_count"] += 1
        return tmp


class P2pOrderComplaintAdminBase(Resource):
    model = P2pOrderComplaint
    order_model = P2pOrder
    r_model = P2pOrderComplaintRecord

    @classmethod
    def get_enum_info(cls):
        return dict(
            order_statuses={i.name: i.value for i in cls.order_model.Status},
            complaint_statuses={i.name: i.value for i in cls.model.Status},
            complaint_reasons={i.name: i.value for i in cls.model.Reason},
            audit_operation_map={i.name: i.value for i in cls.r_model.AuditOperation},
            winner_types={i.name: i.value for i in cls.r_model.WinnerType},
            fiats=P2pUtils.get_all_p2p_fiats(),
        )


@ns.route("/complaint/list")
@respond_with_code
class P2pOrderComplaintResource(P2pOrderComplaintAdminBase):
    export_headers = (
        {"field": "order_id", Language.ZH_HANS_CN: "订单编号"},
        {"field": "status", Language.ZH_HANS_CN: "订单状态"},
        {"field": "complaint_created_at", Language.ZH_HANS_CN: "提交申诉时间"},
        {"field": "complaint_updated_at", Language.ZH_HANS_CN: "申诉完成时间"},
        {"field": "complaint_reason", Language.ZH_HANS_CN: "申诉原因"},
        {"field": "quote", Language.ZH_HANS_CN: "法币"},
        {"field": "side", Language.ZH_HANS_CN: "申诉发起方"},
        {"field": "plaintiff_email", Language.ZH_HANS_CN: "发起方邮箱"},
        {"field": "defendant_email", Language.ZH_HANS_CN: "被申诉方邮箱"},
        {"field": "quote_amount", Language.ZH_HANS_CN: "订单金额"},
        {"field": "base_amount", Language.ZH_HANS_CN: "数字资产数量"},
        {"field": "complaint_status", Language.ZH_HANS_CN: "处理状态"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        order_id=fields.Integer,
        complaint_statuses=fields.List(EnumField(P2pOrderComplaint.Status), required=True),
        quote=fields.String,
        user_id=fields.Integer,
        complaint_reason=EnumField(P2pOrderComplaint.Reason),
        created_start_at=TimestampField(is_ms=True),
        created_end_at=TimestampField(is_ms=True),
        finished_at=TimestampField(is_ms=True),
        is_unread=fields.Boolean(missing=False),
        page=PageField,
        limit=LimitField,
        export=fields.Boolean(missing=False),
    ))
    def post(cls, **kwargs):
        """p2p-订单-获取申诉列表"""
        query = db.session.query(cls.model, cls.order_model).join(
            cls.model,
            cls.model.order_id == cls.order_model.id
        )
        ret = {
            "total": 0,
            "items": [],
            "extra": cls.get_enum_info()
        }
        if user_id := kwargs.get("user_id"):
            query = query.filter(
                or_(cls.model.plaintiff_id == user_id, cls.model.defendant_id == user_id)
            )

        if order_id := kwargs.get("order_id"):
            query = query.filter(cls.order_model.order_id == order_id)
        if quote := kwargs.get("quote"):
            query = query.filter(cls.order_model.quote == quote)
        if complaint_statuses := kwargs.get("complaint_statuses"):
            query = query.filter(cls.model.complaint_status.in_(complaint_statuses))
        if complaint_reason := kwargs.get("complaint_reason"):
            query = query.filter(cls.model.complaint_reason == complaint_reason)
        if created_start_at := kwargs.get("created_start_at"):
            query = query.filter(cls.model.created_at >= created_start_at)
        if created_end_at := kwargs.get("created_end_at"):
            query = query.filter(cls.model.created_at <= created_end_at)
        if finished_at := kwargs.get("finished_at"):
            query = query.filter(
                cls.model.updated_at >= finished_at,
                cls.model.complaint_status == cls.model.Status.FINISHED
            )
        unread_ids = P2pOrderComplaintBiz.get_unread_complaint_ids(P2pOrderComplaintAnchor.ADMIN_ID)
        if kwargs.get("is_unread"):
            query = query.filter(cls.model.id.in_(unread_ids))
        # 待处理的订单正序，历史订单倒序
        if {cls.model.Status.CREATED, cls.model.Status.PENDING} & set(complaint_statuses):
            query = query.order_by(cls.model.id)
        else:
            query = query.order_by(cls.model.id.desc())
        if export := kwargs.get("export"):
            items = query.all()
        else:
            paginate = query.paginate(kwargs["page"], kwargs["limit"])
            items = paginate.items
        if not items:
            return ret
        complaint_list = [i[0] for i in items]
        order_list = [i[1] for i in items]
        user_ids = set()
        for i in complaint_list:
            user_ids.update((i.plaintiff_id, i.defendant_id))
        email_mapper = P2pOrderAdminBase.get_user_email_dic(user_ids)
        order_id_map = {i.id: i for i in order_list}
        new_complaint_list = []
        
        for complaint in complaint_list:
            order = order_id_map[complaint.order_id]
            new_complaint = P2pOrderComplaintBiz.format_complaint(complaint)
            new_complaint["plaintiff_email"] = email_mapper.get(complaint.plaintiff_id)
            new_complaint["defendant_email"] = email_mapper.get(complaint.defendant_id)
            new_complaint.update(order.to_dict(enum_to_name=True))
            new_complaint["complaint_created_at"] = complaint.created_at
            new_complaint["complaint_updated_at"] = complaint.updated_at
            new_complaint["side"] = cls.format_complaint_user(complaint, order)
            new_complaint["is_unread"] = complaint.id in unread_ids
            new_complaint_list.append(new_complaint)

        if export:
            return export_xlsx(filename='p2p_order_complaint',
                               data_list=cls.format_export(complaint_list, order_list, email_mapper),
                               export_headers=cls.export_headers)
        else:
            ret.update(
                total=paginate.total,
                items=new_complaint_list
            )
            return ret

    @classmethod
    def format_export(cls, complaint_list, order_list, email_mapper):
        order_id_map = {i.id: i for i in order_list}
        items = []
        for complaint in complaint_list:
            item = {}
            order = order_id_map[complaint.order_id]
            item["order_id"] = order.order_id
            item["status"] = order.status.value
            item["complaint_created_at"] = datetime_to_str(complaint.created_at)
            item["complaint_updated_at"] = datetime_to_str(complaint.updated_at) \
                if complaint.complaint_status in (cls.model.Status.FINISHED, cls.model.Status.CANCELED) else "--"
            item["complaint_reason"] = complaint.complaint_reason.value
            item["quote"] = order.quote
            item["side"] = cls.format_complaint_user(complaint, order)
            item["plaintiff_email"] = email_mapper.get(complaint.plaintiff_id)
            item["defendant_email"] = email_mapper.get(complaint.defendant_id)
            item["quote_amount"] = export_amount(order.quote_amount)
            item["base_amount"] = f"{export_amount(order.base_amount)} {order.base}"
            item["complaint_status"] = complaint.complaint_status.value
            item["remark"] = complaint.remark
            items.append(item)
        return items

    @classmethod
    def format_complaint_user(cls, complaint, order):
        is_plaintiff_customer = complaint.plaintiff_id == order.customer_id
        if order.side == P2pBusinessType.BUY:
            return "买方（交易者）" if is_plaintiff_customer else "卖方（商家）"
        else:
            return "卖方（交易者）" if is_plaintiff_customer else "买方（商家）"


@ns.route("/<int:complaint_id>/complaint")
@respond_with_code
class P2pOrderComplaintDetailResource(P2pOrderComplaintAdminBase):

    @classmethod
    def get(cls, complaint_id, **kwargs):
        """p2p-订单-获取申诉详情"""
        complaint_row = cls.model.query.get(complaint_id)
        record_rows = cls.r_model.query.filter(
            cls.r_model.complaint_id == complaint_row.id
        ).order_by(cls.r_model.id).all()
        fac = P2pOrderComplaintBiz
        complaint = fac.format_complaint(complaint_row)
        records = [P2pComplaintRecordBiz.format_record(i) for i in record_rows]
        audit_ids = {i.user_id for i in record_rows if i.user_type == cls.r_model.UserType.AUDIT}
        email_map = P2pOrderAdminBase.get_user_email_dic(audit_ids)

        for record in records:
            record["audit_email"] = email_map.get(record["user_id"])
        complaint["record"] = records
        return {
            **complaint,
            "extra": cls.get_enum_info(),
            "quick_reply": cls.get_complaint_reply()
        }

    @classmethod
    def get_complaint_reply(cls):
        model = P2pComplaintReply
        rows = model.query.order_by(model.sort_id).all()
        return [i.to_dict() for i in rows]

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(P2pOrderComplaint.Status),
        winner=EnumField(P2pOrderComplaintRecord.WinnerType),
        msg=fields.String,
        to_user_id=fields.Integer,
        remark=fields.String,
        operate=EnumField(P2pOrderComplaintRecord.AuditOperation),
        image_keys=fields.List(fields.String),
        video_keys=fields.List(fields.String),
    ))
    def put(cls, complaint_id, **kwargs):
        """p2p-订单-修改申诉"""
        complaint = P2pOrderComplaint.query.get(complaint_id)
        old_data = complaint.to_dict(enum_to_name=True)
        user_id = g.user.id
        if user_id in [complaint.plaintiff_id, complaint.defendant_id]:
            raise InvalidArgument(message="客服不能是申诉相关人员")
        STATUS = cls.model.Status
        biz = P2pOrderComplaintBiz(complaint)
        if to_user_id := kwargs.get("to_user_id"):
            msg, image_keys, video_keys = kwargs.get("msg"), kwargs.get("image_keys"), kwargs.get("video_keys")
            if not any([msg, image_keys, video_keys]):
                return
            biz.audit_msg_update(
                user_id,
                to_user_id=to_user_id,
                msg=kwargs.get("msg"),
                image_keys=image_keys,
                video_keys=video_keys,
            )
        if status := kwargs.get("status"):
            if status in [STATUS.CREATED, STATUS.CANCELED]:
                raise InvalidArgument(message=f"状态不允许变更为{status.value}")
            if status == STATUS.PENDING:
                biz.pending(user_id)
            if status == STATUS.FINISHED:
                if not (winner := kwargs.get("winner")):
                    raise InvalidArgument(message=f"请选择处理结果")
                biz.finish(user_id, winner)
        if (remark := kwargs.get("remark")) is not None:
            complaint.remark = remark
        if operate := kwargs.get("operate"):
            check_p2p_site_setting()
            biz.execute_operation(user_id, operate)
        biz.record_bus.update_newest_anchor(P2pOrderComplaintAnchor.ADMIN_ID)

        AdminOperationLog.new_edit(
            user_id=user_id,
            ns_obj=OPNamespaceObjectFiat.P2PComplaintOP,
            old_data=old_data,
            new_data=complaint.to_dict(enum_to_name=True),
        )


@ns.route("/complaint/reply")
@respond_with_code
class P2pOrderComplaintReplyResource(P2pOrderComplaintAdminBase):
    model = P2pComplaintReply

    export_headers = (
        {"field": "reply_type", Language.ZH_HANS_CN: "类型"},
        {"field": "title", Language.ZH_HANS_CN: "场景"},
        {"field": "content", Language.ZH_HANS_CN: "内容"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """p2p-申诉-查看申诉话术"""
        rows = cls.model.query.all()
        items = [i.to_dict() for i in rows]
        if kwargs.get("export"):
            return export_xlsx(filename='p2p_order_complaint_reply',
                               data_list=items,
                               export_headers=cls.export_headers)
        return dict(
            items=items
        )

    @classmethod
    def post(cls, **kwargs):
        """p2p-申诉-上传申诉话术"""
        file_ = request.files.get('file')
        file_columns = [i['field'] for i in cls.export_headers]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)

        if not rows:
            raise InvalidArgument(message="内容不能为空")

        cls.model.query.delete()
        replays = []
        for idx, row in enumerate(rows):
            reply = P2pComplaintReply(**row, sort_id=idx)
            db.session.add(reply)
            replays.append(reply)
        db.session.commit()

        for replay in replays:
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectFiat.P2PComplaintReply,
                detail=replay.to_dict(enum_to_name=True),
            )
