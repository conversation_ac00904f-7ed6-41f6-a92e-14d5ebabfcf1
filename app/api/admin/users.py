# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from copy import deepcopy
from datetime import datetime, timedelta, date
from decimal import Decimal
from enum import Enum
from functools import partial, reduce
from typing import Iterable, Dict, List

from dateutil.tz import tz
from flask import current_app, g
from flask_api.exceptions import NotFound
from flask_babel import gettext as _
from flask_restx import fields as fx_fields
from flask_restx import marshal
from pyroaring import BitMap
from sqlalchemy import func, or_, select, and_
from webargs import fields

from app.api.common.decorators import require_admin_webauth_token, check_p2p_site_setting
from app.api.common.request import require_email_not_exists
from app.business.auth import get_admin_user_name_map, get_special_conf_create_operators
from app.business.user import get_user_kyc_country_map
from app.business.balance.series import get_user_staking_balance_series
from app.business.external_dbs import TradeHistoryDB
from app.business.p2p.utils import P2pUtils
from app.business.security import reset_security_info
from app.business.statistic import (
    get_user_fee_statistic,
    get_user_summary_statistic, get_user_deal_order,
    get_user_asset, get_user_perpetual_summary_statistic,
    get_user_perpetual_fee_statistic, user_margin_asset,
)
from app.business.user_group import UserGroupValidator
from app.common import (
    MessageTitle, MessageContent, MessageWebLink,
    list_country_codes_3, language_cn_names, LANGUAGE_NAMES
    )
from app.common.constants import (
    StopOrderStatusIntType, TradeIntType, PERPETUAL_ALL_MARKET_TYPE, ADMIN_EXPORT_LIMIT, PerpetualOrderOption,
    P2pBusinessType, ShareUserTag
)
from app.exceptions.user import UnbindThirdPartyAccountNotAllowed
from app.models.admin_tag import AdminTagUser, AdminTagCategory
from app.models.risk_control import RiskUser
from app.models.amm import LiquiditySlice, UserLiquiditySlice, UserLiquidityProfit, AmmMarket, \
    UserLiquidityIncomeSummary
from app.models.daily import DailyStakingReport
from app.models.margin import MarginLiquidationOrder
from app.models.referral import Ambassador
from app.models.security import SecurityResetApplication
from app.models.staking import StakingAccount, StakingUserSummary
from app.models.user import (ClearedUser, SignOffUser, LoginRelationHistory, ApiTradingWhiteList, \
                             BusinessSystemUserRecord, ThirdPartyAccount, TaxExportHistory, P2pMerchant, P2pUser,
                             UserExtra, UserPreference,
                             UserSetting as UserSettingModel, MarketPriceNotice)
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser, \
    BaseOPNamespaceObjectMeta, OPNamespaceObjectFiat
from app.utils.ip import GeoIP
from app.utils.net import validate_email
from .p2p.advertising import AdminStatusEnum, AdminAdvTypeEnum
from ..common import (
    Resource, Namespace, respond_with_code,
    require_super_admin,
)
from ..common import ex_fields
from ..common.fields import MarketField, TimestampField, TimestampMarshalField, EnumField, \
    PageField, LimitField, EnumMarshalField, AmountField, PositiveDecimalField, \
    DateField, AssetField, ChainField
from ...assets import list_all_assets, list_all_chains, has_asset, asset_to_chains
from ...business import (
    UserSettings, UserPreferences, PerpetualServerClient,
    PerpetualHistoryDB, CacheLock, LockKeys, PriceManager, ServerClient,
    LockAssetHelper, UpdateAssetHelper,
    get_user_daily_withdrawal_limit, get_30_days_withdrawal_limit, SPOT_ACCOUNT_ID,
    ReferralBusiness, WalletClient, KYCInstitutionBusiness,
    position_target_map, get_user_using_coupon_balance, RiskScreenBusiness,
)
from ...business import cached
from ...business.account_pl import RealtimeAccountProfitLossProcessor
from ...business.balance import (
    get_suggest_user_balance_series_points,
    get_user_spot_balance_series,
    get_user_investment_balance_series,
    get_user_perpetual_balance_series,
    get_user_margin_balance_series,
    get_user_balance_sum_series,
    get_user_spot_balance_sum_series,
    get_user_amm_balance_sum_series,
    get_user_sub_account_sum_series,
)
from ...business.balance.helper import UserTotalBalanceHelper
from ...business.email import send_edit_security_notice_email, send_reset_security_notice_email
from ...business.export.tax_data import export_tax_data
from ...business.fee import FeeFetcher, UserFeeParser, update_user_fee_task
from ...business.investment import InvestmentUserAccountsHelper
from ...business.margin.helper import MarginHelper
from ...business.market_maker import MarketMakerHelper
from ...business.order import Order
from ...business.p2p.pay_channel import UserPayChannelBus
from ...business.p2p.permission import P2pPermissionManager, update_user_p2p_permission_task
from ...business.p2p.relation import P2pRelationManager
from ...business.pledge.helper import (
    is_pledge_account,
    get_user_pledge_unflat_amount_dict,
    get_pledge_loan_asset_account_id_dict,
    get_pledge_account_id_assets_dict,
)
from ...business.summary import get_trade_users
from ...business.user import get_user_accounts_balances_dict, get_user_remark, get_users_remark_map, \
    process_user_api_permission, UserRepository
from ...business.user_status import MarketMakerChangeType
from ...business.utils import PerpetualMarketComparator
from ...business.vip import VIP_LEVEL_DICT, VipHelper
from ...caches import (
    PerpetualMarketCache, MarketCache, MarginAccountNameCache,
    PerpetualOfflineMarketCache, UserLoginTokenCache,
)
from ...caches.amm import LongLiveLiquidityPoolAmountCache
from ...caches.operation import UserRelationAsyncExportCache
from ...caches.user import UserVisitPermissionCache, AdminEditUserEmailCache
from ...common import (
    PositionSide, OrderSideType, OrderIntType, position_deal_type_map,
    StopOrderType, BalanceBusiness,
    TradeBusinessType, TradeType, OrderType, OrderBusinessType,
    StopOrderIntType, get_country, TwoFAType, list_country_codes_3_admin,
    AccountBalanceType, PerpetualMarketType, Language, PrecisionEnum,
    position_margin_type_map, SettleSwitch
)
from ...exceptions import (
    InvalidArgument, ForbidTrading, OrderExceptionMap,
    OrderException, AssetNotFound,
)
from ...models import (
    User, Deposit, Withdrawal, SubAccount, KycVerification, KycVerificationPro,
    InvestmentAccount, InvestmentBalanceHistory, Market,
    OperationLog, WithdrawalAddress,
    VipUser, UserMarginDayRate, MarketMaker, db, ReferralHistory, Referral,
    BigCustomer, LockedAssetBalance, UpdateAssetBalance,
    LoginHistory, UserLoginState, MarginAccount, UserBindingAccount,
    UserLiquidity, MarginLoanOrder, ApiAuth, UserStatusChangeHistory,
    SecurityToolHistory, PublicityChannel,
    UserSpecialConfigChangeLog, ApiWithdrawalAddress,
    UserBusinessRecord, ShortLinkInfo, PerpetualMarket,
    SubAccountManagerRelation, WithdrawalApprover, DailyAssetExchangeSiteReport,
    Message, KolApply, KolUser, CreditUser, P2pMerchantDailyTradeSummary, P2pUserTradeSummary, AccountTransferLog,
    P2pUserMargin, P2pUserTPlusNRecord
)
from ...models.mongo import AutoOfflineAdvReason
from ...models.mongo.p2p.pay_channel import UserPayChannelMySQL, P2pPayChannelMySQL
from ...models.mongo.p2p.advertising import P2pAdvertisingMySQL
from ...models.wallet import AdminDisableWithdrawalAddressRecord
from ...schedules.account import check_asset_lock_schedule
from ...schedules.operation import user_relation_async_export_task
from ...utils import (
    validate_mobile, amount_to_str, quantize_amount, batch_iter, offset_to_page, now, query_to_page,
    export_xlsx, today, datetime_to_str, format_percent, current_timestamp, ConfigMode
)
from ...utils.date_ import last_month_range, datetime_to_utc8_str, timestamp_to_datetime
from ...utils.export import export_xlsx_with_sheet
from ...utils.helper import Struct

ns = Namespace('Users')


def get_all_markets():
    return Market.query.with_entities(
            Market.name,
            Market.base_asset,
            Market.quote_asset,
        ).all()


@ns.route('/common')
@respond_with_code
class UserCommonResource(Resource):

    @classmethod
    def get(cls):
        return dict(
            user_types=UsersResource.USER_TYPES_DISPLAY,
            vip_levels=VipHelper.get_vip_level2desc(),
            countries={code: get_country(code).cn_name
                       for code in list_country_codes_3_admin()}
        )


@ns.route('')
@respond_with_code
class UsersResource(Resource):
    USER_TYPES = {
        User.UserType.NORMAL: '普通用户',
        User.UserType.INTERNAL_MAKER: '内部做市商',
        User.UserType.EXTERNAL_MAKER: '外部做市商',
        User.UserType.EXTERNAL_SPOT_MAKER: '现货做市商',
        User.UserType.EXTERNAL_CONTRACT_MAKER: '合约做市商',
        User.UserType.EXTERNAL: '机构账号',
        User.UserType.SUB_ACCOUNT: '子账户'
    }
    USER_TYPES_DISPLAY = {k.name: v for k, v in USER_TYPES.items()}

    @classmethod
    @ns.use_kwargs(dict(
        user_type=EnumField(User.UserType),
        keyword=fields.String(),
        location_code=fields.String,
        channel=fields.String,
        start_time=fields.Integer,
        end_time=fields.Integer,
        vip_level=fields.Integer,
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50),
    ))
    def get(cls, **kwargs):
        """用户列表"""
        keyword_results = None
        key_word_is_email = False
        query = User.query.join(
            VipUser, User.id == VipUser.user_id, isouter=True
        )
        query = query.with_entities(User, VipUser.level)
        if (user_type := kwargs.get('user_type')) is not None:
            query = query.filter(User.user_type == user_type)
        if keyword := kwargs.get('keyword', '').strip():
            keyword_results = User.search_for_users(keyword)
            query = query.filter(User.id.in_(keyword_results))
            if validate_email(keyword):
                key_word_is_email = True
        if location_code := kwargs.get('location_code'):
            query = query.filter(User.location_code == location_code)
        if channel := kwargs.get('channel'):
            query = query.filter(User.channel == channel)
        if start_time := kwargs.get('start_time'):
            query = query.filter(
                User.created_at >= timestamp_to_datetime(start_time))
        if end_time := kwargs.get('end_time'):
            query = query.filter(
                User.created_at <= timestamp_to_datetime(end_time))
        if (vip_level := kwargs.get('vip_level')) is not None:
            if vip_level == 0:
                query = query.filter(
                    or_(
                        VipUser.level == 0,
                        VipUser.level.is_(None)
                    )
                )
            else:
                query = query.filter(VipUser.level == vip_level)

        user_ids = set(keyword_results) if keyword_results else set()
        if key_word_is_email:
            sign_off_query = or_(
                SignOffUser.email == keyword,
                SignOffUser.user_id.in_(user_ids)
            )
        else:
            sign_off_query = SignOffUser.user_id.in_(user_ids)
        sign_off_records = SignOffUser.query.filter(sign_off_query).with_entities(
            SignOffUser.user_id).all()
        sign_off_users = {i.user_id for i in sign_off_records}
        email_only_in_sign_off_users = sign_off_users - user_ids  # 用户注销后email已从User表删除，仅从SignOffUser可查

        records = query.order_by(User.id.desc()).infinite_paginate(kwargs['page'], kwargs['limit'])
        items = records.items
        res = []
        for (user, vip_level) in items:
            user_id = user.id
            if user_id in sign_off_users:
                email = '账号已注销'
                is_sign_off = True
            else:
                email = user.email
                is_sign_off = False

            device, device_id = cls.get_user_device_and_id(user_id)
            item = dict(
                id=user_id,
                created_at=user.created_at,
                email=email,
                mobile=user.mobile,
                name=user.name,
                user_type=user.user_type.name,
                vip_level=vip_level if vip_level is not None else 0,
                has_2fa=user.has_2fa,
                referrer_id=r.id if (r := user.referrer) else None,
                channel=user.channel,
                location=(get_country(user.location_code).cn_name
                          if user.location_code
                          else ''),
                device=device,
                device_id=device_id,
                is_sign_off=is_sign_off
            )
            res.append(item)

        if email_only_in_sign_off_users:
            users = User.query.filter(User.id.in_(email_only_in_sign_off_users)).all()
            for user in users:
                user_id = user.id
                email = '账号已注销'
                device, device_id = cls.get_user_device_and_id(user_id)
                item = dict(
                    id=user_id,
                    created_at=user.created_at,
                    email=email,
                    mobile=user.mobile,
                    name=user.name,
                    user_type=user.user_type.name,
                    vip_level=0,
                    has_2fa=None,
                    referrer_id=None,
                    channel=user.channel,
                    location=(get_country(user.location_code).cn_name
                              if user.location_code
                              else ''),
                    device=device,
                    device_id=device_id,
                    is_sign_off=True
                )
                res.append(item)
        return dict(
            items=res,
            has_next=records.has_next,
            user_types=cls.USER_TYPES_DISPLAY,
            vip_levels=VipHelper.get_vip_level2desc(),
            channels=cls.get_channels(),
            countries={code: get_country(code).cn_name
                       for code in list_country_codes_3_admin()}
        )

    @classmethod
    @cached(3600)
    def get_channels(cls):
        channels = User.query.with_entities(func.distinct(User.channel)).all()
        return [x for x, in channels if x]

    @classmethod
    def get_user_device_and_id(cls, user_id):
        histroy = LoginRelationHistory.query.filter(
            LoginRelationHistory.user_id == user_id
        ).order_by(LoginRelationHistory.id.desc()).first()
        if not histroy:
            return '', ''
        return histroy.device, histroy.device_id


@ns.route('/search')
@respond_with_code
class UserSearchResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_type=fields.String(),
        keyword=fields.String()
    ))
    def get(cls, **kwargs):
        """用户搜索"""
        key_word_is_email = False
        query = User.query
        if user_type := kwargs.get('user_type'):
            user_types = {i.strip() for i in user_type.split(',')}
            query = query.filter(User.user_type.in_(user_types))
        if keyword := kwargs.get('keyword', '').strip():
            keyword_results = User.search_for_users(keyword)
            query = query.filter(User.id.in_(keyword_results))
            if validate_email(keyword):
                key_word_is_email = True
        user_ids = {user.id for user in query.all()}
        if key_word_is_email:
            sign_off_query = or_(
                SignOffUser.email == keyword,
                SignOffUser.user_id.in_(user_ids)
            )
        else:
            sign_off_query = SignOffUser.user_id.in_(user_ids)
        sign_off_records = SignOffUser.query.filter(sign_off_query).with_entities(SignOffUser.user_id).all()
        sign_off_users = {i.user_id for i in sign_off_records}
        all_query_users = user_ids | sign_off_users
        records = User.query.filter(User.id.in_(all_query_users)).all()  # 这次查询无需再加user_type，因user_id已经是通过user_type筛选出来的
        res = []

        for user in records:
            user_id = user.id
            is_sign_off = True if user_id in sign_off_users else False
            item = dict(
                id=user_id,
                email=user.email,
                created_at=user.created_at,
                name=user.name,
                mobile=user.mobile,
                is_sign_off=is_sign_off,
                nickname=user.nickname,
            )
            res.append(item)
        return dict(items=res)


# noinspection PyUnresolvedReferences
@ns.route('/<int:id_>')
@respond_with_code
class UserBasicsResource(Resource):

    @classmethod
    def get(cls, id_):
        """用户-基本信息"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Basics,
            target_user_id=id_,
        )

        return cls.user_to_dict(user)

    @classmethod
    @ns.use_kwargs(dict(
        kyc_status=EnumField(User.KYCStatus),
        kyc_pro_status=EnumField(User.KycProStatus),
        kyc_name=fields.String,
        telegram=fields.String,
        reset_withdraw_password=fields.Boolean,
    ))
    def patch(cls, id_, **kwargs):
        """用户-基本信息修改"""
        admin_user_id = g.user.id
        user = User.query.get(id_)
        if user is None:
            raise NotFound

        old_data = user.to_dict(enum_to_name=True)

        if (kyc_status := kwargs.get('kyc_status')) is not None:
            if kyc_status not in (User.KYCStatus.PASSED, User.KYCStatus.NONE):
                raise InvalidArgument(
                    message=f'invalid KYC status: {kyc_status!r}')
            if user.kyc_type == user.KYCType.INSTITUTION:
                raise InvalidArgument(message='机构认证不允许此处修改')
            if KycVerification.query.filter(KycVerification.user_id == id_).first():
                raise InvalidArgument(message='请在KYC列表或风险筛查列表中进行操作')
            user.kyc_status = kyc_status
            RiskScreenBusiness.cancel_screen_request(user.id)
            if kyc_status is User.KYCStatus.NONE:
                KycVerification.query \
                    .filter(KycVerification.user_id == id_) \
                    .update(dict(status=KycVerification.Status.REJECTED))

        if (kyc_pro_status := kwargs.get("kyc_pro_status")) is not None:
            if KycVerificationPro.query.filter(KycVerificationPro.user_id == id_).first():
                raise InvalidArgument(message='请在高级KYC列表中进行操作')
            if kyc_pro_status not in (User.KycProStatus.PASSED, User.KycProStatus.NONE):
                raise InvalidArgument(message=f'invalid KYC status: {kyc_pro_status!r}')
            if kyc_pro_status is User.KycProStatus.NONE:
                KycVerificationPro.query \
                    .filter(KycVerificationPro.user_id == id_) \
                    .update(dict(status=KycVerificationPro.Status.REJECTED))
            else:
                user: User
                pro_record = KycVerificationPro(
                    user_id=user.id,
                    status=KycVerificationPro.Status.PASSED,
                    service_type=KycVerificationPro.ServiceType.MANUAL_AUDIT,
                    country=user.kyc_country or user.location_code,
                    pro_name=user.kyc_full_name or "",
                    address_file_ids="",
                    address='',
                    auditor_id=admin_user_id,
                    audited_at=now()
                )
                db.session.add(pro_record)
        if kwargs.get('telegram') is not None:
            UserBindingAccount.reset_binding_account(id_, UserBindingAccount.AccountType.TELEGRAM)

        if kwargs.get('reset_withdraw_password') is not None:
            user.extra.reset_withdraw_password()
            cls.send_reset_withdrawal_notice(user.id)

        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Basics,
            old_data=old_data,
            new_data=user.to_dict(enum_to_name=True),
            target_user_id=id_,
        )
        return cls.user_to_dict(user)

    @classmethod
    def send_reset_withdrawal_notice(cls, user_id):
        model = SecurityResetApplication
        send_reset_security_notice_email.delay(
            user_id,
            model.ResetType.WITHDRAW_PASSWORD.name,
            model.StatusType.PASSED.name,
            None
        )
        title = MessageTitle.RESET_WITHDRAW_PASSWORD_RESET_SUCCESS.name
        content = MessageContent.SECURITY_RESET_WITHDRAW_PASSWORD_SUCCESS.name
        db.session.add(Message(
            user_id=user_id,
            title=title,
            content=content,
            params=json.dumps({
                'reset_type': model.ResetType.WITHDRAW_PASSWORD.value
            }),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.ACCOUNT_SECURITY_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.ACCOUNT_SECURITY,
        ))

    @classmethod
    def user_to_dict(cls, user: User) -> dict:
        from app.api.admin.p2p.config import P2pTPlusNUserRecordResource

        id_ = user.id
        settings = UserSettings(id_).fields_and_values_json
        for item in settings:
            if item['name'] == UserSettings.daily_withdrawal_limit.name:
                item['value'] = get_user_daily_withdrawal_limit(user)['limit_usd']
                if item['valid_interval'] and item['valid_interval'][1] < current_timestamp(to_int=True):
                    item['valid_interval'] = None
            if item['name'] == UserSettings.withdrawal_limit_30_days.name:
                item['value'] = get_30_days_withdrawal_limit(user)['limit_usd']
                if item['valid_interval'] and item['valid_interval'][1] < current_timestamp(to_int=True):
                    item['valid_interval'] = None
        # convert account id to market name
        items = [item for item in settings if item['name'] in (
            UserSettings.forbidden_margin_accounts.name,
            UserSettings.forbidden_margin_flat.name,
            UserSettings.only_allowed_margin_accounts_by_risk_control.name,
            UserSettings.only_allowed_margin_accounts_by_credit.name,
        )]
        values = cls._accounts_to_markets(*[item['value'] for item in items])
        for i, v in zip(items, values):
            i['value'] = v
        settings.append({
            'name': RiskUser.Permission.BALANCE_OUT_DISABLED.name.lower(),
            'type': 'bool',
            'desc': '风控提现受限',
            'editable': True,
            'meta': {'admin_editable': False, 'remark': ''},
            'value': RiskUser.test(id_, RiskUser.Permission.BALANCE_OUT_DISABLED),
            'valid_interval': None,
            'default_value': False,
        })

        u_pref = UserPreferences(id_)
        prefs = u_pref.fields_and_values_json
        for pf in prefs:
            if pf["name"] == "withdrawal_fee_asset" and not pf["value"]:
                pf["value"] = "提现币种"  # 没设置，默认显示`提现币种`
        settle_switch_dic = cls._get_user_perpetual_settle_switch(id_)
        prefs.append(settle_switch_dic)

        kyc_type = user.kyc_type
        institution_kyc_status = {}
        if kyc_type == user.KYCType.INSTITUTION:
            institution_kyc_status = KYCInstitutionBusiness(user).get_kyc_status()

        risk_screen_status = RiskScreenBusiness.get_user_risk_screen_status(id_)
        manage_users = []
        if user.is_sub_account:
            relations = SubAccountManagerRelation.query.filter(
                SubAccountManagerRelation.user_id == user.id,
                SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
            ).all()
            manage_users = [i.manager_id for i in relations]

        cleared_user = ClearedUser.query.filter(ClearedUser.user_id == id_,
                                                ClearedUser.valid.is_(True)).first()
        publicity_channel = cls.get_publicity_channel(user)
        referrer_id = user.referrer.id if user.referrer else None
        referral_type = cls.get_refer_type(referrer_id, id_) if referrer_id else '无邀请人'
        withdrawal_approvers = WithdrawalApprover.query.filter(
            WithdrawalApprover.user_id == user.id,
            WithdrawalApprover.is_self.is_(False),
            WithdrawalApprover.status.in_([WithdrawalApprover.Status.VALID, WithdrawalApprover.Status.DELETING]),
        ).all()
        third_party_records = ThirdPartyAccount.query.filter(
            ThirdPartyAccount.user_id == user.id
        ).order_by(ThirdPartyAccount.id).with_entities(
            ThirdPartyAccount.created_at,
            ThirdPartyAccount.source,
            ThirdPartyAccount.name,
            ThirdPartyAccount.status,
        ).all()
        sign_up_type = 'normal'
        if third_party_records and abs(int(third_party_records[0].created_at.timestamp()) - int(user.created_at.timestamp())) < 5:
            # 这种情况认为是通过第三方账号注册
            sign_up_type = third_party_records[0].source.value
        third_account_map = {item.source: item for item in third_party_records if item.status == ThirdPartyAccount.Status.VALID}
        google_account = third_account_map.get(ThirdPartyAccount.Source.GOOGLE, None)
        apple_account = third_account_map.get(ThirdPartyAccount.Source.APPLE, None)

        email = user.email
        is_sign_off = False
        audit_info = None
        if email is None:
            rec = SignOffUser.query.filter(SignOffUser.user_id == id_).first()
            if rec:
                is_sign_off = True
                email = '账号已注销'
        else:
            cache = AdminEditUserEmailCache(id_)
            if cache.value:
                audit_info = json.loads(cache.value)
                email = f'{email} 修改为 {audit_info["email"]}'
        last_active_at = UserRepository.get_user_last_active_at(id_)
        last_kyc = KycVerification.query.filter(
            KycVerification.user_id == id_
        ).order_by(KycVerification.id.desc()).first()
        last_kyc_pro = KycVerificationPro.query.filter(
            KycVerificationPro.user_id == id_
        ).order_by(KycVerificationPro.id.desc()).first()
        user_extra = user.extra

        kyc_country_code = user.kyc_country
        kyc_country = get_country(kyc_country_code)
        kyc_country_name = kyc_country.cn_name if kyc_country else kyc_country_code
        withdrawal_approval_rec = AdminDisableWithdrawalAddressRecord.query.filter(
            AdminDisableWithdrawalAddressRecord.user_id == id_,
            AdminDisableWithdrawalAddressRecord.status == AdminDisableWithdrawalAddressRecord.Status.CREATED
        ).first()
        if withdrawal_approval_rec:
            withdrawal_approval_delete_accounts = json.loads(withdrawal_approval_rec.accounts)
            admin_id = withdrawal_approval_rec.created_by
            withdrawal_approval_operator = get_admin_user_name_map([admin_id])[admin_id]
        else:
            withdrawal_approval_delete_accounts = None
            withdrawal_approval_operator = None

        t_plus_n = None
        t_plus_n_record = P2pUserTPlusNRecord.query.filter(
            P2pUserTPlusNRecord.user_id == id_
        ).first()
        if t_plus_n_record:
            rule_name_mapper = P2pTPlusNUserRecordResource.get_rule_name_dic()
            match_rule_ids = P2pTPlusNUserRecordResource.get_real_match_rule_ids(t_plus_n_record)
            t_plus_n_match_rules = [rule_name_mapper[rule_id] for rule_id in match_rule_ids if rule_name_mapper.get(rule_id, '')]
            t_plus_n = {
                'match_rules': '、'.join(t_plus_n_match_rules),
                'effect_rule': rule_name_mapper.get(t_plus_n_record.real_rule, ''),
                'real_days': t_plus_n_record.real_days,
            }


        tag_user_query = AdminTagUser.query.filter(
            AdminTagUser.user_id == id_,
        ).with_entities(
            AdminTagUser.tag_id,
            AdminTagUser.status,
        ).all()

        tag_category_query = AdminTagCategory.query.filter(
            AdminTagCategory.id.in_([i.tag_id for i in tag_user_query])
        ).with_entities(
            AdminTagCategory.id,
            AdminTagCategory.name,
            AdminTagCategory.remark,
        ).all()
        tag_category_map = {i.id: dict(name=i.name, remark=i.remark) for i in tag_category_query}
        tag_list = [dict(
            key=tag_category_map[i.tag_id]['name'],
            remark=tag_category_map[i.tag_id]['remark'],
            value=True if i.status == AdminTagUser.Status.PASSED else False,
                         ) for i in tag_user_query]
        tag_list.sort(key=lambda x: x["value"], reverse=True)

        return dict(
            id=id_,
            name_displayed=user.name_displayed,
            nickname=user.nickname,
            account_name=f"@{user.extra.display_account_name}",
            avatar=user_extra.admin_avatar_url,
            email=email,
            mobile_country_code=user.mobile_country_code,
            mobile=user.mobile,
            mobile_num=user.mobile_num,
            user_type=user.user_type.name,
            sub_account_count=user.sub_accounts.count(),
            created_at=user.created_at,
            has_totp=bool(user.totp_auth_key),
            has_webauthn=bool(user.web_authn_list),
            has_withdraw_password=user.extra.has_withdraw_password,
            kyc_type=kyc_type,
            kyc_institution=institution_kyc_status,
            kyc_status=user.kyc_status.name,
            kyc_pro_status=user.kyc_pro_status.name if user.kyc_pro_status else User.KycProStatus.NONE.name,
            kyc_country=kyc_country_code,
            kyc_country_name=kyc_country_name,
            kyc_name=user.kyc_full_name,
            kyc_id_number=user.kyc_id_number,
            kyc_passed_at=cls._get_kyc_passed_at(user),
            kyc_last_passed_at=last_kyc.last_passed_at if last_kyc else None,
            last_kyc_id=last_kyc.id if last_kyc else None,
            last_kyc_pro_id=last_kyc_pro.id if last_kyc_pro else None,
            risk_screen_status=risk_screen_status if not risk_screen_status else risk_screen_status.value,
            registration_ip=user.registration_ip,
            registration_location=user.registration_location,
            login_password_level=user.login_password_level.name,
            login_password_updated_at=user.login_password_updated_at,
            is_sub_account=user.is_sub_account,
            ambassador_display=ReferralBusiness.get_ambassador_admin_display(user.id),
            main_account=(0
                          if (sub_account_ref := user.sub_account_ref) is None
                          else sub_account_ref.main_user_id),
            publicity_channel=publicity_channel,
            referral_type=referral_type,
            referrer_id=referrer_id,
            referree_count=user.referee_count,
            location_code=user.location_code,
            location=(country.cn_name
                      if (country := get_country(user.location_code))
                      else ''),
            settings=settings,
            preferences=prefs,
            channel=user.channel,
            remark=get_user_remark(user),
            auth_api_count=ApiAuth.query.filter(
                ApiAuth.user_id == id_, ApiAuth.status == ApiAuth.Status.VALID,
            ).with_entities(func.count("*")).scalar() or 0,
            telegram=(account.account_name
                      if (account := UserBindingAccount.get_binding_account(id_,
                                                                            UserBindingAccount.AccountType.TELEGRAM))
                      else ''
                      ),
            tag_list=tag_list,
            cleared_user_status=cleared_user.status.value if cleared_user else '',
            manage_users=manage_users,
            withdrawal_approvers=withdrawal_approvers,
            google_account_name=google_account.name if google_account else '',
            apple_account_name=apple_account.name if apple_account else '',
            sign_up_type=sign_up_type,
            is_sign_off=is_sign_off,
            last_active_at=last_active_at,
            webauthn_list=cls.get_webauthn_list(user),
            opening_web_login_ip_locking=u_pref.opening_web_login_ip_locking,
            opening_web_security_login_duration=u_pref.opening_web_security_login_duration,
            audit_info=audit_info,
            opening_trade_password=UserPreferences(user.main_user.id).opening_trade_password,
            has_withdrawal_approval_audit=bool(withdrawal_approval_rec),
            withdrawal_approval_delete_accounts=withdrawal_approval_delete_accounts,
            withdrawal_approval_operator=withdrawal_approval_operator,
            withdrawal_approval_rec_id=withdrawal_approval_rec.id if withdrawal_approval_rec else None,
            t_plus_n=t_plus_n,
            app_face_id_status=cls.get_app_pwd_status(id_, OperationLog.Operation.UPDATE_APP_FACE_ID_STATUS),
            app_finger_pwd_status=cls.get_app_pwd_status(id_, OperationLog.Operation.UPDATE_APP_FINGER_PWD_STATUS),
            app_gesture_pwd_status=cls.get_app_pwd_status(id_, OperationLog.Operation.UPDATE_APP_GESTURE_PWD_STATUS),
        )

    @classmethod
    def _accounts_to_markets(cls, *args):
        accounts = reduce(lambda x, y: x | y, args)
        if not accounts:
            return [[] for _ in range(len(args))]
        markets = MarginAccount.query.filter(
            MarginAccount.id.in_(accounts)
        ).with_entities(MarginAccount.id, MarginAccount.name).all()
        markets = dict(markets)
        return [[markets.get(item, 'NONE') for item in account] for account in args]

    @classmethod
    def _get_user_perpetual_settle_switch(cls, user_id):
        pref = PerpetualServerClient().get_settle_switch(user_id)
        value = pref['settle_switch']
        settle_switch_dic = {
            'name': 'settle_switch',
            'value': '开启' if value == SettleSwitch.OPEN else '关闭',
            'desc': '浮盈结算'
        }
        return settle_switch_dic

    @classmethod
    def _get_kyc_passed_at(cls, user):
        """KYC 个人认证通过时间(自动审核的时间不准确)"""
        if user.kyc_status is not user.KYCStatus.PASSED:
            return None
        kyc = user.kyc_verification
        if not kyc or kyc.status is not kyc.Status.PASSED:
            return None
        return kyc.last_passed_at

    @classmethod
    def get_publicity_channel(cls, user):
        publicity_channel = '无推广渠道'
        channel = user.channel
        if not channel:
            return publicity_channel
        publicity_channels = PublicityChannel.get_all_pub_name_map()
        if channel in publicity_channels:
            publicity_channel = channel
        else:
            channel_lis = user.channel.split('a')
            if len(channel_lis) > 1 and channel_lis[1].isdigit():
                short_link_rec = ShortLinkInfo.query.get(int(channel_lis[1]))
                if short_link_rec:
                    publicity_channel = short_link_rec.publicity_channel_name
        return publicity_channel

    @classmethod
    def get_refer_type(cls, referrer_id: int, user_id: int):
        rec = ReferralHistory.query.filter(
            ReferralHistory.referrer_id == referrer_id,
            ReferralHistory.referree_id == user_id).first()
        referral_type = '大使推荐' if rec.referral_type == ReferralHistory.ReferralType.AMBASSADOR else '普通推荐'
        return referral_type

    @classmethod
    def get_webauthn_list(cls, user: User) -> list:
        user_webauthn_list = []
        for _v in user.web_authn_list:
            user_webauthn_list.append(
                dict(
                    id=_v.id,
                    name=_v.name,
                    created_at=_v.created_at,
                    last_used_at=_v.last_used_at or _v.created_at,
                )
            )
        return user_webauthn_list

    @classmethod
    def get_app_pwd_status(cls, user_id, op_type):
        if op_type not in [
            OperationLog.Operation.UPDATE_APP_FACE_ID_STATUS,
            OperationLog.Operation.UPDATE_APP_FINGER_PWD_STATUS,
            OperationLog.Operation.UPDATE_APP_GESTURE_PWD_STATUS,
        ]:
            raise InvalidArgument("invalid optype")

        last_app_pwd_status_op_log = OperationLog.query.filter(
            OperationLog.user_id==user_id,
            OperationLog.operation==op_type.name,
        ).with_entities(
            OperationLog.user_id,
            OperationLog.operation,
            OperationLog.platform,
            OperationLog.detail,
            OperationLog.created_at,
        ).order_by(OperationLog.id.desc()).first()

        if not last_app_pwd_status_op_log:
            return ""

        detail = json.loads(last_app_pwd_status_op_log.detail)
        app_version = detail.get("app_version", "unknown")
        status = detail.get("status", "unknown")
        device_id = detail.get("device_id", "unknown")
        op_time = datetime_to_utc8_str(last_app_pwd_status_op_log.created_at)
        return f"{last_app_pwd_status_op_log.platform}-{app_version}-{status}-{op_time}-{device_id}"


@ns.route('/<int:id_>/mobile')
@respond_with_code
class UserMobileResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        mobile=fields.String(allow_none=True),
        mobile_country_code=fields.String(allow_none=True),
    ))
    def patch(cls, id_, **kwargs):
        """用户-手机号修改"""
        user = User.query.get(id_)

        old_data = user.to_dict(enum_to_name=True)

        pref = UserPreferences(id_)
        admin_user_id = g.user.id
        if mobile := kwargs.get('mobile'):
            if user.is_sub_account:
                raise InvalidArgument(message='子账号不允许此处修改')
            if not (country_code := kwargs.get('mobile_country_code')):
                raise InvalidArgument
            if not validate_mobile(f'{country_code}{mobile}'):
                raise InvalidArgument(message=f'invalid mobile: {mobile!r}')
            mob_cc, mob_no = country_code, mobile
            if User.query \
                    .filter(User.mobile_country_code == mob_cc,
                            User.mobile_num == mob_no) \
                    .first() is not None:
                raise InvalidArgument(message=f'mobile used by another user')
            mobile_before = user.mobile
            user.set_mobile(mob_cc, mob_no)
            SecurityToolHistory.add(
                user.id,
                SecurityToolHistory.OpType.CHANGE_MOBILE,
                SecurityToolHistory.OpRole.ADMIN,
                user.email,
                account_before=mobile_before,
                admin_user_id=admin_user_id
            )
            if pref.two_fa_type is TwoFAType.NONE:
                pref.two_fa_type = TwoFAType.MOBILE
        else:
            # 解绑手机
            reset_security_info(user, SecurityResetApplication.ResetType.MOBILE, SecurityToolHistory.OpRole.ADMIN)

        tokens = UserLoginTokenCache(user.id).clear_tokens()
        UserLoginState.clear_tokens(tokens)
        send_edit_security_notice_email.delay('mobile', id_)
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Mobile,
            old_data=old_data,
            new_data=user.to_dict(enum_to_name=True),
            target_user_id=id_,
        )
        return dict(name_displayed=user.name_displayed, mobile=user.mobile)


@ns.route('/<int:id_>/email')
@respond_with_code
class UserEmailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        email=fields.String(required=True, validate=validate_email),
    ))
    def patch(cls, id_, **kwargs):
        """用户-邮箱修改"""
        user = User.query.get(id_)
        if user.is_sub_account:
            raise InvalidArgument(message='子账号不允许此处修改')
        admin_user_id = g.user.id
        email = kwargs['email']
        require_email_not_exists(email)
        cache = AdminEditUserEmailCache(id_)
        name_map = get_admin_user_name_map([admin_user_id])
        detail = {
            'email': email,
            'origin_email': user.email,
            'op_user': admin_user_id,
            'op_username': name_map.get(admin_user_id, admin_user_id)
        }
        cache.save(json.dumps(detail))
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Email,
            old_data=dict(email=user.email),
            new_data=dict(email=email),
            target_user_id=id_,
        )
        resp = {
            'email': f'{user.email} 修改为 {email}',
            'audit_info': detail,
        }
        return dict(name_displayed=user.name_displayed, **resp)

    @classmethod
    @ns.use_kwargs(dict(
        email=fields.String(required=True, validate=validate_email),
        status=EnumField(enum=['PASSED', 'REJECTED', ], required=True),
    ))
    def put(cls, id_, **kwargs):
        """用户-邮箱修改审核"""
        user = User.query.get(id_)
        if user.is_sub_account:
            raise InvalidArgument(message='子账号不允许此处修改')
        admin_user_id = g.user.id
        email = kwargs['email']
        if kwargs['status'] == 'PASSED':
            require_email_not_exists(email)
            reset_security_info(
                user,
                SecurityResetApplication.ResetType.EMAIL,
                SecurityToolHistory.OpRole.ADMIN,
                email,
                admin_user_id=admin_user_id
            )
            send_edit_security_notice_email.delay('email', id_)
        cache = AdminEditUserEmailCache(id_)
        origin_email = ''
        if cache.value:
            audit_info = json.loads(cache.value)
            origin_email = audit_info['origin_email']
        AdminOperationLog.new_audit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Email,
            detail={'email': {'old': origin_email, 'new': kwargs['email']}, 'status': kwargs['status']},
            target_user_id=id_,
        )
        cache.delete()
        resp_email = email if kwargs['status'] == 'PASSED' else origin_email
        resp = {
            'email': resp_email,
            'audit_info': None,
        }
        return dict(name_displayed=user.name_displayed, **resp)


@ns.route('/<int:id_>/totp')
@respond_with_code
class UserTotpResource(Resource):

    @classmethod
    def send_reset_totp_notice(cls, user_id):
        model = SecurityResetApplication
        send_reset_security_notice_email.delay(
            user_id,
            model.ResetType.TOTP.name,
            model.StatusType.PASSED.name,
            None
        )
        title = MessageTitle.RESET_TOTP_PASS.name
        content = MessageContent.RESET_TOTP_PASS.name
        db.session.add(Message(
            user_id=user_id,
            title=title,
            content=content,
            params=json.dumps({
                'reset_type': model.ResetType.TOTP.value
            }),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.ACCOUNT_SECURITY_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.ACCOUNT_SECURITY,
        ))

    @classmethod
    def delete(cls, id_):
        """用户-解绑TOTP"""
        user = User.query.get(id_)
        if user.is_sub_account:
            raise InvalidArgument(message='子账号不允许此处修改')
        admin_user_id = g.user.id
        reset_security_info(
            user,
            SecurityResetApplication.ResetType.TOTP,
            SecurityToolHistory.OpRole.ADMIN,
            admin_user_id=admin_user_id
        )
        cls.send_reset_totp_notice(user.id)
        AdminOperationLog.new_delete(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.TOTP,
            target_user_id=id_,
        )
        return dict(name_displayed=user.name_displayed, has_totp=False)


@ns.route('/<int:id_>/webauthn')
@respond_with_code
class UserWebauthnResource(Resource):


    @classmethod
    def delete(cls, id_):
        """用户-解绑通行密钥"""
        user = User.query.get(id_)
        if user.is_sub_account:
            raise InvalidArgument(message='子账号不允许此处修改')
        admin_user_id = g.user.id
        reset_security_info(
            user,
            SecurityResetApplication.ResetType.WEBAUTHN,
            SecurityToolHistory.OpRole.ADMIN,
            admin_user_id=admin_user_id
        )
        AdminOperationLog.new_delete(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Webauthn,
            target_user_id=id_,
        )
        return dict(name_displayed=user.name_displayed, has_webauthn=False)


@ns.route('/<int:id_>/remark')
@respond_with_code
class UserRemarkResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String(missing="", allow_none=True),
    ))
    def patch(cls, id_, **kwargs):
        """用户-备注修改"""
        user = User.query.get(id_)
        old_data = user.to_dict(enum_to_name=True)
        user.remark = kwargs["remark"] or ""
        db.session.commit()
        admin_user_id = g.user.id
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Remark,
            old_data=old_data,
            new_data=user.to_dict(enum_to_name=True),
            target_user_id=id_,
        )
        return dict(name_displayed=user.name_displayed, **kwargs)


@ns.route('/<int:id_>/withdrawal-approvers')
@respond_with_code
class UserWithdrawalApproversResource(Resource):

    @classmethod
    def delete(cls, id_, **kwargs):
        """用户-删除提现审核人"""
        rec = AdminDisableWithdrawalAddressRecord.query.filter(
            AdminDisableWithdrawalAddressRecord.user_id == id_,
            AdminDisableWithdrawalAddressRecord.status == AdminDisableWithdrawalAddressRecord.Status.CREATED
        ).first()
        if rec:
            raise InvalidArgument(message='存在尚未审核的请求!')
        approvers = WithdrawalApprover.query.filter(
            WithdrawalApprover.user_id == id_,
            WithdrawalApprover.is_self.is_(False),
            WithdrawalApprover.status.in_([WithdrawalApprover.Status.VALID, WithdrawalApprover.Status.DELETING]),
        ).with_entities(
            WithdrawalApprover.account
        ).all()
        accounts = [i.account for i in approvers]
        row = AdminDisableWithdrawalAddressRecord(
            user_id=id_,
            accounts=json.dumps(accounts),
            created_by=g.user.id,
        )
        db.session_add_and_commit(row)
        admin_user_id = g.user.id
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.WithdrawalApprover,
            new_data={'delete_email': accounts},
            target_user_id=id_,
        )
        return dict(
            withdrawal_approval_rec_id=row.id,
            withdrawal_approval_operator=get_admin_user_name_map([admin_user_id])[admin_user_id]
        )


@ns.route('/<int:id_>/withdrawal-approvals-delete')
@respond_with_code
class WithdrawalApprovalsDeleteResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        accounts=fields.List(fields.String, required=True),
    ))
    def put(cls, id_: int, **kwargs):
        """用户详情-提现多人审核-删除提现地址"""
        rec = AdminDisableWithdrawalAddressRecord.query.filter(
            AdminDisableWithdrawalAddressRecord.user_id == id_,
            AdminDisableWithdrawalAddressRecord.status == AdminDisableWithdrawalAddressRecord.Status.CREATED
        ).first()
        if rec:
            raise InvalidArgument(message='存在尚未审核的请求!')
        row = AdminDisableWithdrawalAddressRecord(
            user_id=id_,
            accounts=json.dumps(kwargs['accounts']),
            created_by=g.user.id,
        )
        db.session_add_and_commit(row)

        admin_user_id = g.user.id
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.WithdrawalApprover,
            new_data={'delete_email': kwargs['accounts']},
            target_user_id=id_,
        )
        return dict(
            withdrawal_approval_rec_id=row.id,
            withdrawal_approval_operator=get_admin_user_name_map([admin_user_id])[admin_user_id]
        )


@ns.route('/<int:id_>/withdrawal-approval-audit/pass')
@respond_with_code
class WithdrawalApprovalAuditPassResource(Resource):

    @classmethod
    def put(cls, id_: int):
        """用户详情-审核删除多人提现地址-审核通过"""
        rec = AdminDisableWithdrawalAddressRecord.query.get(id_)
        if not rec or rec.status != AdminDisableWithdrawalAddressRecord.Status.CREATED:
            raise InvalidArgument(message='审核申请不存在或已被审核！')

        auditor = g.user.id
        creator = rec.created_by
        if auditor == creator:
            raise InvalidArgument(message='提交人与审核人不能为同一人！')

        user_id = rec.user_id
        accounts = json.loads(rec.accounts)
        WithdrawalApprover.query.filter(
            WithdrawalApprover.user_id == user_id,
            WithdrawalApprover.is_self.is_(False),
            WithdrawalApprover.account.in_(accounts),
        ).update({'status': WithdrawalApprover.Status.DELETED}, synchronize_session=False)

        rec.status = AdminDisableWithdrawalAddressRecord.Status.PASSED
        rec.auditor = auditor
        db.session.commit()

        detail = dict(
            delete_email=accounts,
            status='PASSED',
        )
        AdminOperationLog.new_audit(
            user_id=auditor,
            ns_obj=OPNamespaceObjectUser.WithdrawalApprover,
            detail=detail,
            target_user_id=user_id,
        )

        self_approver = User.query.get(user_id)
        for account in accounts:
            # 这里审核人数量最多5个，不做批量增加
            SecurityToolHistory.add(
                user_id,
                SecurityToolHistory.OpType.DELETE_WITHDRAWAL_APPROVER,
                SecurityToolHistory.OpRole.ADMIN,
                self_approver.email,
                admin_user_id=auditor,
                withdrawal_approver_email=account,
            )
        return dict(
            deleted_accounts=accounts
        )


@ns.route('/<int:id_>/withdrawal-approval-audit/reject')
@respond_with_code
class WithdrawalApprovalAuditRejectResource(Resource):

    @classmethod
    def put(cls, id_: int):
        """用户详情-审核删除多人提现地址-审核拒绝"""
        rec = AdminDisableWithdrawalAddressRecord.query.get(id_)
        if not rec or rec.status != AdminDisableWithdrawalAddressRecord.Status.CREATED:
            raise InvalidArgument(message='审核申请不存在或已被审核！')
        auditor = g.user.id
        creator = rec.created_by
        user_id = rec.user_id
        accounts = json.loads(rec.accounts)
        if auditor == creator:
            raise InvalidArgument(message='提交人与审核人不能为同一人！')
        rec.status = AdminDisableWithdrawalAddressRecord.Status.REJECTED
        rec.auditor = auditor
        db.session.commit()
        detail = dict(
            delete_email=accounts,
            status='REJECTED',
        )
        AdminOperationLog.new_audit(
            user_id=auditor,
            ns_obj=OPNamespaceObjectUser.WithdrawalApprover,
            detail=detail,
            target_user_id=user_id,
        )


@ns.route('/<int:id_>/avatar')
@respond_with_code
class UserResetAvatarResource(Resource):

    @classmethod
    def patch(cls, id_):
        """用户-重置用户头像"""
        user = User.query.get(id_)
        admin_user_id = g.user.id

        old_data = {
            "avatar": user.extra.avatar
        }
        default_avatar = UserRepository.get_system_default_avatar()
        UserRepository.update_user_avatar(user, default_avatar, is_admin=True)
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.ResetAvatar,
            old_data=old_data,
            new_data={"avatar": default_avatar},
            target_user_id=id_
        )


@ns.route('/avatars')
@respond_with_code
class UserAvatarListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_ids=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        """用户-头像信息列表"""
        user_ids = kwargs['user_ids']
        user_ids = user_ids.split(',')
        user_ids = [int(id_) for id_ in user_ids]
        records = UserExtra.query.filter(
            UserExtra.user_id.in_(user_ids)
        ).all()
        res = []
        for item in records:
            item: UserExtra
            res.append(dict(
                user_id=item.user_id,
                avatar=item.admin_avatar_url
            ))
        return dict(
            items=res
        )


@ns.route('/<int:id_>/settings/<field>')
@respond_with_code
class UserSettingFieldResource(Resource):
    DAILY_WITHDRAWAL_LIMIT = 'daily_withdrawal_limit'
    FIELD_TO_SP_OP_TYPE = {
        DAILY_WITHDRAWAL_LIMIT: UserSpecialConfigChangeLog.SpecialConfigType.DAILY_WITHDRAWAL_LIMIT,
    }

    @classmethod
    def get(cls, id_, field):
        """用户详情-设置列表"""
        settings = UserSettings(id_)
        try:
            return settings.get_field_and_value_json(field)
        except AttributeError:
            raise InvalidArgument(message=f'field {field!r} does not exist')

    @classmethod
    def _check_p2p_permission_params(cls, user_id: str, field: str, value=None):
        if field not in P2pPermissionManager.P2P_ALL_PERMISSIONS:
            return
        p2p_user = P2pUser.query.filter(P2pUser.user_id == user_id).first()
        if not p2p_user:
            raise InvalidArgument(message=f"current user not open p2p function")
        if field == UserSettings.p2p_t_plus_n_days.name:
            if value is not None:
                value = int(value)
            if value is not None and (value > 10 or value < 0):
                raise InvalidArgument("请输入正确的数量")

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True),
        valid_from=TimestampField,
        valid_till=TimestampField,
        remark=fields.String,
    ))
    def put(cls, id_, field, value, **kwargs):
        """用户详情-设置修改"""
        admin_user_id = g.user.id
        settings = UserSettings(id_, mode=ConfigMode.REAL_TIME)
        old_value = getattr(settings, field)
        try:
            p = getattr(UserSettings, field)
            if p.meta.get('admin_editable') is False:
                raise InvalidArgument(
                    message=f'field {field!r} does not allow editing')
        except AttributeError:
            raise InvalidArgument(message=f'field {field!r} does not exist')
        sp_change_details = cls.get_sp_change_details(settings, field, value, kwargs)
        cls._check_p2p_permission_params(id_, field, value)
        if field in P2pPermissionManager.P2P_ALL_PERMISSIONS:
            cls.update_p2p_permission(id_, field, value)
        else:
            try:
                settings.set(field, value,
                             valid_from=kwargs.get('valid_from'),
                             valid_till=kwargs.get('valid_till'))
            except (AttributeError, ValueError) as e:
                raise InvalidArgument(message=f'{e!r}')
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Setting,
            old_data={field: old_value},
            new_data={field: getattr(settings, field)},
            target_user_id=id_,
        )
        field_rec = settings.get_field_record(field)
        cls.add_special_config_log_when(
            sp_change_details, user_id=id_, remark=kwargs.get('remark'),
            op_id=field_rec.id)
        return UserBasicsResource.user_to_dict(User.query.get(id_))

    @classmethod
    def update_p2p_permission(cls, user_id, permission, value):
        check_p2p_site_setting()
        update_user_p2p_permission_task(user_id, permission, value)

    @classmethod
    def delete_p2p_permission(cls, settings, user_id, permission):
        check_p2p_site_setting()
        default_value = settings.get_field_and_value_json(permission)['default_value']
        update_user_p2p_permission_task(user_id, permission, default_value)

    @classmethod
    def delete(cls, id_, field):
        """用户详情-设置删除"""
        admin_user_id = g.user.id
        settings = UserSettings(id_)
        try:
            p = getattr(UserSettings, field)
            if p.meta.get('admin_editable') is False:
                raise InvalidArgument(message=f'field {field!r} not allow edit')
        except AttributeError:
            raise InvalidArgument(message=f'field {field!r} does not exist')
        sp_change_details = cls.get_sp_change_details(settings, field)
        cls._check_p2p_permission_params(id_, field)
        if field in P2pPermissionManager.P2P_ALL_PERMISSIONS:
            cls.delete_p2p_permission(settings, id_, field)
        else:
            try:
                delattr(settings, field)
            except (AttributeError, ValueError) as e:
                raise InvalidArgument(message=f'{e!r}')
        AdminOperationLog.new_delete(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Setting,
            detail={'field': field},
            target_user_id=id_,
        )
        field_rec = settings.get_field_record(field)
        cls.add_special_config_log_when(
            sp_change_details, user_id=id_, op_id=field_rec.id)
        return dict(
            value=getattr(UserSettings(id_), field)
        )

    @classmethod
    def get_sp_change_details(cls, obj, field, value=None, kwargs=None):
        if field not in cls.FIELD_TO_SP_OP_TYPE.keys():
            return {}

        op_type = UserSpecialConfigChangeLog.OpType.CREATE
        if obj.get(field) is not None:
            op_type = UserSpecialConfigChangeLog.OpType.UPDATE

        kwargs = kwargs or {}
        if not kwargs:
            kwargs = {'valid_till': obj.get_valid_interval(field)}
            op_type = UserSpecialConfigChangeLog.OpType.DELETE

        detail = {'detail': '', 'key': field, 'op_type': op_type}
        if field == cls.DAILY_WITHDRAWAL_LIMIT:
            expired_time = None
            valid_till = kwargs.get("valid_till")  # 这里接口返回值不是统一的？
            if isinstance(valid_till, datetime):
                valid_till = (None, valid_till.timestamp())
            if valid_till and valid_till[-1]:
                expired_time = datetime.fromtimestamp(valid_till[-1])
                expired_time = datetime_to_str(expired_time, offset_minutes=60 * 8)
            expired_time = expired_time or '永不过期'
            value = value or obj.get(field)
            detail.update(detail=f'提现额度为{value}USD，到期时间为{expired_time}')
        return detail

    @classmethod
    def add_special_config_log_when(
            cls, record_detail: dict, user_id: int, remark: str = '', op_id: int = None):
        if not record_detail:
            return

        UserSpecialConfigChangeLog.add(
            user_id=user_id,
            config_type=cls.FIELD_TO_SP_OP_TYPE.get(record_detail.get('key')),
            op_type=record_detail['op_type'],
            admin_user_id=g.user.id,
            change_detail=record_detail.get('detail'),
            change_remark=remark,
            op_id=op_id
        )


@ns.route('/<int:id_>/withdrawal-limit')
@respond_with_code
class UserWithdrawalLimitResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        daily_withdrawal_limit=fields.Decimal(allow_none=True),
        withdrawal_limit_30_days=fields.Decimal(allow_none=True),
        valid_till=TimestampField,
        remark=fields.String,
    ))
    def put(cls, id_, **kwargs):
        """用户-特殊配置-提现额度配置"""
        daily_withdrawal_limit, withdrawal_limit_30_days = (
            kwargs['daily_withdrawal_limit'], kwargs['withdrawal_limit_30_days'])
        user = User.query.get(id_)
        # 因KYC用户没有30天提现额度的限制，因此特殊提现额度只对未KYC用户设置
        if user.kyc_status == User.KYCStatus.PASSED and withdrawal_limit_30_days:
            raise InvalidArgument(message='特殊额度设置仅针对未KYC用户')
        admin_user_id = g.user.id
        settings = UserSettings(id_, mode=ConfigMode.REAL_TIME)
        ori_daily_limit, ori_limit_30_days = settings.daily_withdrawal_limit, settings.withdrawal_limit_30_days
        ori_data = dict(daily_withdrawal_limit=ori_daily_limit,
                        withdrawal_limit_30_days=ori_limit_30_days)
        new_data = dict(daily_withdrawal_limit=daily_withdrawal_limit,
                        withdrawal_limit_30_days=withdrawal_limit_30_days)
        if daily_withdrawal_limit:
            settings.set('daily_withdrawal_limit', daily_withdrawal_limit,
                         valid_from=kwargs.get('valid_from'),
                         valid_till=kwargs.get('valid_till'))
        else:
            delattr(settings, 'daily_withdrawal_limit')
        if withdrawal_limit_30_days:
            settings.set('withdrawal_limit_30_days', withdrawal_limit_30_days,
                         valid_from=kwargs.get('valid_from'),
                         valid_till=kwargs.get('valid_till'))
        else:
            delattr(settings, 'withdrawal_limit_30_days')
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.SpecialWithdrawalLimit,
            old_data=ori_data,
            new_data=new_data,
            target_user_id=id_,
        )
        expired_time = None
        valid_till = kwargs.get("valid_till")
        if isinstance(valid_till, datetime):
            valid_till = (None, valid_till.timestamp())
        if valid_till and valid_till[-1]:
            expired_time = datetime.fromtimestamp(valid_till[-1])
            expired_time = datetime_to_str(expired_time, offset_minutes=60 * 8)
        expired_time = expired_time or '永不过期'
        if not ori_daily_limit and not ori_limit_30_days:
            op_type = UserSpecialConfigChangeLog.OpType.CREATE
        else:
            op_type = UserSpecialConfigChangeLog.OpType.UPDATE
        detail = (f'24小时提现额度为{daily_withdrawal_limit or ""}USD，30天提现额度为{withdrawal_limit_30_days or ""}USD,'
                  f'到期时间为{expired_time}')
        UserSpecialConfigChangeLog.add(
            user_id=id_,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.DAILY_WITHDRAWAL_LIMIT,
            op_type=op_type,
            admin_user_id=admin_user_id,
            change_detail=detail,
            change_remark=kwargs.get('remark'),
        )

    @classmethod
    def delete(cls, id_):
        """用户-特殊配置-删除提现额度配置"""
        admin_user_id = g.user.id
        settings = UserSettings(id_)
        daily_withdrawal_limit, withdrawal_limit_30_days = settings.daily_withdrawal_limit, settings.withdrawal_limit_30_days

        delattr(settings, 'daily_withdrawal_limit')
        delattr(settings, 'withdrawal_limit_30_days')
        AdminOperationLog.new_delete(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.SpecialWithdrawalLimit,
            detail={'field': ['daily_withdrawal_limit', 'withdrawal_limit_30_days']},
            target_user_id=id_,
        )
        expired_time = '永不过期'
        detail = f'24小时提现额度为{daily_withdrawal_limit}USD，30天提现额度为{withdrawal_limit_30_days}USD到期时间为{expired_time}'

        UserSpecialConfigChangeLog.add(
            user_id=id_,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.DAILY_WITHDRAWAL_LIMIT,
            op_type=UserSpecialConfigChangeLog.OpType.DELETE,
            admin_user_id=admin_user_id,
            change_detail=detail,
        )


@ns.route('/sp-config/withdrawal-limit')
@respond_with_code
class UserWithdrawalLimitSpecialConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user=fields.String,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """用户-特殊配置-提现额度配置列表"""
        model = UserSpecialConfigChangeLog
        query = model.query.filter(
            model.config_type == model.SpecialConfigType.DAILY_WITHDRAWAL_LIMIT.value,
            model.op_type == model.OpType.CREATE.value
        ).order_by(model.id.desc())
        if user := kwargs.get('user'):
            u_ids = User.search_for_users(user)
            query = query.filter(
                model.user_id.in_(u_ids)
            )
        records = query.all()
        user_ids = {i.user_id for i in records}
        query_fields = ('daily_withdrawal_limit', 'withdrawal_limit_30_days')
        user_settings = UserSettingModel.query.filter(
            UserSettingModel.user_id.in_(user_ids),
            UserSettingModel.key.in_(query_fields),
            UserSettingModel.status == UserSettingModel.Status.VALID
        ).all()
        valid_user_ids = set()
        user_withdrawal_dic = defaultdict(lambda: {
            'daily_withdrawal_limit': None,
            'withdrawal_limit_30_days': None,
            'valid_from': None,
            'valid_till': None,
        })

        for row in user_settings:
            user_id = row.user_id
            valid_user_ids.add(user_id)
            if row.key == 'daily_withdrawal_limit':
                user_withdrawal_dic[user_id]['daily_withdrawal_limit'] = row.value
            else:
                user_withdrawal_dic[user_id]['withdrawal_limit_30_days'] = row.value
            user_withdrawal_dic[user_id]['valid_from'] = row.valid_from
            user_withdrawal_dic[user_id]['valid_till'] = row.valid_till
        added_sp_conf_user_ids = set()
        valid_sp_conf_records = []
        for record in records:
            u_id = record.user_id
            if u_id in valid_user_ids and u_id not in added_sp_conf_user_ids:   # 取最新的一条添加记录
                valid_sp_conf_records.append(record)
                added_sp_conf_user_ids.add(u_id)
        page, limit = kwargs['page'], kwargs['limit']
        sp_conf_rows = valid_sp_conf_records[(page-1)*limit: page*limit]
        admin_user_ids = {i.admin_user_id for i in sp_conf_rows}
        sp_conf_user_ids = {i.user_id for i in sp_conf_rows}
        user_detail_dic = UserRepository.get_user_info_map(admin_user_ids | sp_conf_user_ids)
        res = []
        for row in sp_conf_rows:
            operator_id = row.admin_user_id
            operator = user_detail_dic[operator_id].name
            user_id = row.user_id
            email = user_detail_dic[user_id].email
            mobile = user_detail_dic[user_id].mobile
            name = user_detail_dic[user_id].name
            withdrawal_dic = user_withdrawal_dic[user_id]
            item = {
                'operator_id': operator_id,
                'operator': operator,
                'user_id': user_id,
                'email': email,
                'mobile': mobile,
                'name': name,
                'remark': row.change_remark,
            }
            item.update(withdrawal_dic)
            res.append(item)
        return dict(
            items=res,
            total=len(valid_sp_conf_records)
        )


@ns.route('/<int:id_>/preferences/<field>')
@respond_with_code
class UserPreferenceFieldResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True),
        valid_from=TimestampField,
        valid_till=TimestampField,
        remark=fields.String,
    ))
    def put(cls, id_, field, value, **kwargs):
        """用户详情-偏好修改"""
        if field not in [
            "opening_web_login_ip_locking",
            "opening_web_security_login_duration",
        ]:
            raise InvalidArgument(message=f'暂不支持修改 {field!r}')

        admin_user_id = g.user.id
        pref = UserPreferences(id_)

        old_value = getattr(pref, field)

        try:
            pref.set(
                field,
                value,
                valid_from=kwargs.get('valid_from'),
                valid_till=kwargs.get('valid_till'),
            )
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Preference,
            old_data={field: old_value},
            new_data={field: getattr(pref, field)},
            target_user_id=id_,
        )
        return dict(
            value=getattr(pref, field)
        )


@ns.route('/<int:id_>/spot-assets')
@respond_with_code
class UserSpotInfoResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        filter_small_assets=fields.Boolean(missing=False),
        hide_deposit_withdrawal=fields.Boolean(missing=True),
        hide_offline_assets=fields.Boolean(missing=True)
    ))
    def get(cls, id_, **kwargs):
        """用户详情-现货账户-现货资产"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        user_assets = get_user_asset(user.id, kwargs['filter_small_assets'], kwargs['hide_offline_assets'])
        user_assets.sort(key=lambda x: x['market_value_usd'], reverse=True)

        assets = [item['asset'] for item in user_assets]
        withdraw_records, deposit_records = cls._get_deposit_withdrawal_records(
            user.id, kwargs.get('hide_deposit_withdrawal', True))
        account_balances = get_user_accounts_balances_dict(user.id, included_assets=assets)
        balance = account_balances.get(SPOT_ACCOUNT_ID, 0)

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpotAssets,
            detail=kwargs,
            target_user_id=id_,
        )

        return dict(
            user_assets=user_assets,
            deposit_records=deposit_records,
            withdraw_records=withdraw_records,
            asset_update_types=UpdateAssetBalance.Type,
            balance=balance,
        )

    @classmethod
    def _get_deposit_withdrawal_records(cls, user_id, hide: bool = True):
        withdraw_records, deposit_records = {}, {}
        if hide:
            return withdraw_records, deposit_records
        coin_withdraw = Withdrawal.query.filter(
            Withdrawal.user_id == user_id,
            Withdrawal.status.in_([
                Withdrawal.Status.FINISHED,
                Withdrawal.Status.AUDIT_REQUIRED,
                Withdrawal.Status.AUDITED,
                Withdrawal.Status.CONFIRMING,
            ])
        ).with_entities(
            Withdrawal.asset,
            func.sum(Withdrawal.amount)
        ).group_by(Withdrawal.asset).all()
        for record in coin_withdraw:
            withdraw_records[record[0]] = record[1]

        coin_deposit = Deposit.query.filter(
            Deposit.user_id == user_id,
            Deposit.status.in_([
                Deposit.Status.FINISHED,
                Deposit.Status.TO_HOT])
        ).with_entities(
            Deposit.asset,
            func.sum(Deposit.amount)
        ).group_by(Deposit.asset).all()
        for record in coin_deposit:
            deposit_records[record[0]] = record[1]
        return withdraw_records, deposit_records


@ns.route('/<int:id_>/spot-assets-preview')
@respond_with_code
class UserSpotAssetPreviewResource(Resource):

    @classmethod
    def get(cls, id_):
        """用户详情-资产总览-资产总览"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound
        daily_withdrawal_limit = get_user_daily_withdrawal_limit(user)
        _30_days_withdrawal_limit = get_30_days_withdrawal_limit(user)
        user_rate = dict(
            daily_withdrawal_limit_usd=daily_withdrawal_limit['limit_usd'],
            daily_withddrawn_usd=daily_withdrawal_limit['withdrawn_usd'],
            kyc_status=_30_days_withdrawal_limit['kyc_status'],
            _30_days_withdrawal_limit_usd=_30_days_withdrawal_limit['limit_usd'],
            _30_days_withdrawn_usd=_30_days_withdrawal_limit['withdrawn_usd'],
        )
        account_balance_sum = cls.get_account_balance_sum(id_, True)
        nor_sub_accounts_bl = copy_trading_sub_accounts_bl = Decimal()
        for sub in user.sub_accounts:
            sub_bl = cls.get_account_balance_sum(sub.user_id)
            if sub.type in [SubAccount.Type.COPY_TRADER, SubAccount.Type.COPY_FOLLOWER]:
                copy_trading_sub_accounts_bl += sub_bl['all']
            else:
                nor_sub_accounts_bl += sub_bl['all']
        account_balance_sum['sub_accounts'] = nor_sub_accounts_bl
        account_balance_sum['copy_trading_sub_accounts'] = copy_trading_sub_accounts_bl
        account_balance_sum['all'] += nor_sub_accounts_bl + copy_trading_sub_accounts_bl
        credit = CreditUser.query.with_entities(
            CreditUser.unfinished_credit_balance
        ).filter(
            CreditUser.user_id == id_
        ).first()
        unfinished_credit_total_usd = Decimal()
        if credit:
            unfinished_credit_total_usd = credit.unfinished_credit_balance
        account_balance_sum['unfinished_credit_total_usd'] = amount_to_str(unfinished_credit_total_usd,
                                                                           PrecisionEnum.CASH_PLACES)

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AssetsPreview,
            target_user_id=id_,
        )

        return dict(
            user_rate=user_rate,
            account_balance_sum=account_balance_sum,
        )

    @classmethod
    def get_account_balance_sum(cls, user_id, is_main_user=False):
        result = defaultdict(Decimal)
        asset_price_map = PriceManager.assets_to_usd()
        account_balances = get_user_accounts_balances_dict(user_id)
        result[AccountBalanceType.SPOT.value] = account_balances.get(SPOT_ACCOUNT_ID, 0)
        result[AccountBalanceType.INVESTMENT.value] = account_balances.get(InvestmentAccount.ACCOUNT_ID, 0)
        result[AccountBalanceType.INVESTMENT.value] += account_balances.get(StakingAccount.ACCOUNT_ID, 0)
        result[AccountBalanceType.MARGIN.value] = sum(v for k, v in account_balances.items() \
                                                      if SPOT_ACCOUNT_ID < k < InvestmentAccount.ACCOUNT_ID)
        result[AccountBalanceType.PLEDGE.value] = sum(v for k, v in account_balances.items() if is_pledge_account(k))
        if is_main_user:  # 子账号不用AMM，PLEDGE
            res = AmmMarketResource.get_user_amm_market_record(user_id)
            total_usd = sum([i['liquidity_usd'] for i in res])
            result[AccountBalanceType.AMM.value] = total_usd

            pledge_unflat_amount_dict = get_user_pledge_unflat_amount_dict(user_id)
            for _asset, _unflat_amount in pledge_unflat_amount_dict.items():
                result[AccountBalanceType.PLEDGE.value] -= _unflat_amount * asset_price_map.get(_asset, 0)

        loan_amount = Decimal()
        margin_loans = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id == user_id,
            MarginLoanOrder.status == MarginLoanOrder.StatusType.PASS
        ).with_entities(
            MarginLoanOrder.asset,
            func.sum(
                MarginLoanOrder.unflat_amount + MarginLoanOrder.interest_amount
            ).label('loan_amount')
        ).group_by(
            MarginLoanOrder.asset
        ).all()
        for loan in margin_loans:
            loan_amount += loan.loan_amount * asset_price_map.get(loan.asset, 0)
        result[AccountBalanceType.MARGIN.value] -= loan_amount

        perpetual_usd = 0
        client = PerpetualServerClient()
        perpetual_balances = client.get_user_balances(user_id)
        position_market_map = {i['market']: i for i in client.position_pending(user_id)}
        market_list = PerpetualMarketCache().get_market_list()
        for asset, balance in perpetual_balances.items():
            equity = balance['balance_total']
            for _market in PerpetualMarketCache.balance_asset_to_markets(asset):
                if _market in market_list:
                    position_data = position_market_map.get(_market, defaultdict(Decimal))
                    equity += Decimal(position_data['margin_amount'])
                    equity += Decimal(position_data['profit_unreal'])
            perpetual_usd += equity * asset_price_map.get(asset, 0)
        result[AccountBalanceType.PERPETUAL.value] = perpetual_usd
        result['all'] = sum(result.values())
        for k, v in result.items():
            result[k] = quantize_amount(v, 2)
        return result


@ns.route('/<int:id_>/spot-deposits')
@respond_with_code
class UserSpotDepositsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(),
        chain=ChainField(),
        type=EnumField(Deposit.Type),
        start=TimestampField(),
        end=TimestampField(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, id_, **kwargs):
        """用户详情-现货账户-充值记录"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        asset = kwargs.get('asset')
        chain = kwargs.get('chain')
        if asset and not has_asset(asset, chain):
            raise AssetNotFound(asset, chain)

        query = Deposit.query.filter(Deposit.user_id == id_)
        if chain:
            query = query.filter(Deposit.chain == chain)
        if asset:
            query = query.filter(Deposit.asset == asset)
        if (type_ := kwargs.get('type')) is not None:
            query = query.filter(Deposit.type == type_)
        if start := kwargs.get('start'):
            query = query.filter(Deposit.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(Deposit.created_at < end)
        records = query.order_by(Deposit.id.desc()).paginate(kwargs['page'], kwargs['limit'])

        asset_price_map = PriceManager.assets_to_usd()
        wallet_client = WalletClient()
        addrs = [(x.chain, x.address) for x in records.items]
        txs = [(x.chain, x.tx_id) for x in records.items]
        addrs_url = wallet_client.get_explorer_addresses_url(addrs)
        txs_url = wallet_client.get_explorer_txs_url(txs)

        items = []
        total_usd = Decimal()
        for index, v in enumerate(records.items):
            usd = quantize_amount(asset_price_map.get(v.asset, Decimal()) * v.amount, 8)
            total_usd += usd
            d = dict(
                id=v.id,
                created_at=v.created_at,
                updated_at=v.updated_at,
                wallet_deposit_id=v.wallet_deposit_id,
                user_id=v.user_id,
                type=v.type.name,
                asset=v.asset,
                chain=v.chain,
                address=v.address,
                amount=v.amount,
                memo=v.memo,
                attachment=v.attachment,
                sender_user_id=v.sender_user_id,
                tx_id=v.tx_id,
                vout=v.vout,
                block_height=v.block_height,
                confirmations=v.confirmations,
                confirmed_at=v.confirmed_at,
                status=v.status.name,
                address_url=addrs_url[index],
                tx_url=txs_url[index],
                usd=usd,
            )
            items.append(d)

        return dict(
            total=records.total,
            items=items,
            total_usd=total_usd,
            extra=dict(
                assets=asset_to_chains(),
                types=Deposit.Type,
                statuses=Deposit.Status,
            )
        )


@ns.route('/<int:id_>/spot-withdrawals')
@respond_with_code
class UserSpotWithdrawalsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(),
        chain=ChainField(),
        type=EnumField(Withdrawal.Type),
        start=TimestampField(),
        end=TimestampField(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, id_, **kwargs):
        """用户详情-现货账户-提现记录"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        asset = kwargs.get('asset')
        chain = kwargs.get('chain')
        if asset and not has_asset(asset, chain):
            raise AssetNotFound(asset, chain)

        query = Withdrawal.query.filter(Withdrawal.user_id == id_)
        if chain:
            query = query.filter(Withdrawal.chain == chain)
        if asset:
            query = query.filter(Withdrawal.asset == asset)
        if (type_ := kwargs.get('type')) is not None:
            query = query.filter(Withdrawal.type == type_)
        if start := kwargs.get('start'):
            query = query.filter(Withdrawal.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(Withdrawal.created_at < end)
        records = query.order_by(Withdrawal.id.desc()).paginate(kwargs['page'], kwargs['limit'])

        prices = PriceManager.assets_to_usd()
        return dict(
            total=records.total,
            items=[
                dict(
                    id=v.id,
                    created_at=v.created_at,
                    updated_at=v.updated_at,
                    user_id=v.user_id,
                    type=v.type.name,
                    asset=v.asset,
                    chain=v.chain,
                    address=v.address,
                    amount=v.amount,
                    amount_usd=quantize_amount(prices.get(v.asset, 0) * v.amount, 2),
                    fee=v.fee,
                    fee_asset=v.fee_asset,
                    memo=v.memo,
                    attachment=v.attachment,
                    recipient_user_id=v.recipient_user_id,
                    approved_by_user_at=v.approved_by_user_at,
                    sent_at=v.sent_at,
                    tx_id=v.tx_id,
                    confirmations=v.confirmations,
                    status=v.status.name,
                )
                for index, v in enumerate(records.items)
            ],
            extra=dict(
                assets=asset_to_chains(),
                types=Withdrawal.Type,
                statuses=Withdrawal.Status,
            )
        )


@ns.route('/<int:id_>/spot-balance-history')
@respond_with_code
class UserSpotBalanceHistoryResource(Resource):

    ACCOUNT_TYPE_DICT = {
        AccountTransferLog.AccountType.MARGIN.name: '杠杆账户',
        AccountTransferLog.AccountType.INVESTMENT.name: '理财账户',
        AccountTransferLog.AccountType.PERPETUAL.name: '合约账户',
    }

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(),
        account_type=EnumField(AccountTransferLog.AccountType),
        start_time=TimestampField(),
        end_time=TimestampField(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, id_, **kwargs):
        """用户详情-现货账户-划转记录"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        query = AccountTransferLog.query.filter(
            AccountTransferLog.user_id == id_,
        ).order_by(AccountTransferLog.created_at.desc())
        if account_type := kwargs.get("account_type"):
            all_account_types = [account_type]
        else:
            all_account_types = [
                AccountTransferLog.AccountType.PERPETUAL,
                AccountTransferLog.AccountType.MARGIN,
                AccountTransferLog.AccountType.INVESTMENT,
            ]
        query = query.filter(
            or_(
                and_(
                    AccountTransferLog.source_account_type == AccountTransferLog.AccountType.SPOT,
                    AccountTransferLog.target_account_type.in_(all_account_types),
                ),
                and_(
                    AccountTransferLog.source_account_type.in_(all_account_types),
                    AccountTransferLog.target_account_type == AccountTransferLog.AccountType.SPOT,
                ),
            ),
        )
        if asset := kwargs.get('asset'):
            query = query.filter(AccountTransferLog.asset == asset)
        if start_time := kwargs.get('start_time'):
            query = query.filter(AccountTransferLog.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(AccountTransferLog.created_at < end_time)

        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        items = []
        for record in pagination.items:
            items.append(
                {
                    "time": int(record.created_at.timestamp()),
                    "asset": record.asset,
                    "amount": record.amount,
                    "from": record.source_account_type.value,
                    "to": record.target_account_type.value,
                }
            )
        return dict(
            items=items,
            total=pagination.total,
            extra=dict(
                account_type_dict=cls.ACCOUNT_TYPE_DICT,
                coin_list=list_all_assets(),
            )
        )


@ns.route('/<int:id_>/perpetual-info')
@respond_with_code
class UserPerpetualInfoResource(Resource):

    @classmethod
    def get(cls, id_):
        """用户详情-永续信息"""
        result = []
        client = PerpetualServerClient()
        balance_data = client.get_user_balances(user_id=id_)
        market_list = PerpetualMarketCache().get_market_list()
        position_market_map = {i['market']: i for i in client.position_pending(id_)}
        for asset, balance in balance_data.items():
            # equity：账户权益 = 账户余额 + 保证金余额 + 未实现盈亏
            equity = balance['balance_total']
            for _market in PerpetualMarketCache.balance_asset_to_markets(asset):
                if _market in market_list:
                    position_data = position_market_map.get(_market, defaultdict(Decimal))
                    equity += Decimal(position_data['margin_amount'])
                    equity += Decimal(position_data['profit_unreal'])

            using_coupon_balance = get_user_using_coupon_balance(id_, asset)
            transfer_amount = balance['transfer'] - using_coupon_balance
            balance_temp = dict(
                asset=asset,
                balance_total=amount_to_str(balance['balance_total'], 8),
                equity=amount_to_str(equity, 8),
                margin=amount_to_str(balance['margin'], 8),
                available=amount_to_str(balance['available'], 8),
                frozen=amount_to_str(balance['frozen'], 8),
                transfer=amount_to_str(transfer_amount, 8),
            )
            result.append(balance_temp)
        result = sorted(result, key=lambda x: Decimal(x['equity']),
                        reverse=True)

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.PerpetualAssets,
            target_user_id=id_,
        )

        return result


@ns.route('/<int:id_>/current-position')
@respond_with_code
class UserPerpetualPositionResource(Resource):

    @classmethod
    def _get_position_val(cls, x):
        client = PerpetualServerClient()
        res = client.get_market_status(x['market'], 86400)
        info = PerpetualMarketCache().get_market_info(x['market'])
        if sign_price := Decimal(res.get('sign_price', '0')):
            if info['type'] == PerpetualMarketType.INVERSE.value:
                return Decimal(x['amount']) / sign_price
            else:
                return Decimal(x['amount']) * sign_price
        return '-'

    @classmethod
    def _get_stop_loss_val(cls, x):
        stop_loss_price = x.get('stop_loss_price', '0')
        if not Decimal(stop_loss_price):
            return '/'
        if x['side'] == PositionSide.LONG:
            return f'≤{stop_loss_price}'
        else:
            return f'≥{stop_loss_price}'

    @classmethod
    def _get_take_profit_val(cls, x):
        take_profit_price = x.get('take_profit_price', '0')
        if not Decimal(take_profit_price):
            return '/'
        if x['side'] == PositionSide.SHORT:
            return f'≤{take_profit_price}'
        else:
            return f'≥{take_profit_price}'

    marshal_fields = {
        'position_id': fx_fields.Integer,
        'position_type': fx_fields.Integer(
            attribute=lambda x: PerpetualMarketCache.get_position_type(x['market'])),
        'adl_sort': fx_fields.Integer,
        'market': fx_fields.String,
        'side': ex_fields.EnumMarshalField(
            PositionSide, output_field_lower=False),
        'leverage': fx_fields.String,
        'amount': ex_fields.AmountField,
        'close_left': ex_fields.AmountField,
        'open_price': ex_fields.PriceField,
        'settle_price': ex_fields.PriceField,
        'position_val': ex_fields.AmountField(
            attribute=lambda
                x: UserPerpetualPositionResource._get_position_val(x)
        ),
        'liq_price': ex_fields.LiqPriceField,
        'profit_unreal': ex_fields.AmountField,
        'profit_real': ex_fields.AmountField,
        'deal_asset_fee': ex_fields.AmountField,
        'fee_asset': fx_fields.String,
        'base_asset': fx_fields.String(
            attribute=lambda x: PerpetualMarketCache.get_balance_asset(x['market'])),
        'amount_asset': fx_fields.String(
            attribute=lambda x: PerpetualMarketCache.get_amount_asset(
                x['market'])
        ),
        'balance_asset': fx_fields.String(
            attribute=lambda x: PerpetualMarketCache.get_balance_asset(
                x['market'])
        ),
        'take_profit': fx_fields.String(attribute=lambda x: UserPerpetualPositionResource._get_take_profit_val(x)),
        'stop_loss': fx_fields.String(attribute=lambda x: UserPerpetualPositionResource._get_stop_loss_val(x))
    }

    @classmethod
    def get(cls, id_):
        """用户详情-永续-当前持仓信息"""
        client = PerpetualServerClient()
        records = client.position_pending(id_)
        records = marshal(records, cls.marshal_fields)
        market_info_map = PerpetualMarketCache().read_aside()
        total_profit_amount = 0
        prices = PriceManager.assets_to_usd()
        for item in records:
            info = market_info_map[item['market']]
            money_prec = info['money_prec']
            for k in ('open_price', 'settle_price', 'liq_price'):
                # noinspection PyBroadException
                try:
                    item[k] = quantize_amount(item[k], money_prec)
                except Exception:
                    pass
            profit_real = Decimal(item['profit_real'])
            profit_unreal = Decimal(item['profit_unreal'])
            if info['type'] == PerpetualMarketType.INVERSE:
                total_profit_amount += (profit_real+profit_unreal) * prices.get(info['stock'], 0)
            else:
                total_profit_amount += (profit_real+profit_unreal) * prices.get(info['money'], 0)

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.PerpetualOrders,
            target_user_id=id_,
        )

        return dict(
            items=records,
            market_types=PerpetualMarketType,
            market_info=market_info_map,
            total_profit_amount=amount_to_str(total_profit_amount, PrecisionEnum.COIN_PLACES),
        )


_FINISH_TYPE_MAP = {
    1: '正常',
    2: '强平',
    3: '自动减仓',
    4: '限价',
    5: '市价',
    6: '一键平仓',
    7: '止盈',
    8: '止损',
    9: '系统平仓'
}


@ns.route('/<int:id_>/history-position')
@respond_with_code
class UserHistoryPositionResource(Resource):
    marshal_fields = {
        'position_id': fx_fields.Integer,
        'create_time': fx_fields.Float,
        'update_time': fx_fields.Float,
        'market': fx_fields.String,
        'side': ex_fields.EnumMarshalField(
            PositionSide, output_field_lower=False),
        'amount_max': ex_fields.AmountField,
        'open_price': ex_fields.PriceField,
        'open_val_max': ex_fields.AmountField,
        'profit_real': ex_fields.AmountField,
        'deal_asset_fee': ex_fields.AmountField,
        'fee_asset': fx_fields.String,
        'finish_type': fx_fields.String(attribute=lambda x: _FINISH_TYPE_MAP[x['finish_type']])
    }

    export_marshal_fields = {
        'create_time': fx_fields.String(attribute=lambda x: \
            timestamp_to_datetime(x['create_time']).strftime('%Y-%m-%d %H:%M:%S')),
        'update_time': fx_fields.String(attribute=lambda x: \
            timestamp_to_datetime(x['update_time']).strftime('%Y-%m-%d %H:%M:%S')),
        'market': fx_fields.String,
        'side': fx_fields.String(attribute=lambda x: '多仓' if x['side'] == PositionSide.LONG else '空仓'),
        'open_price': ex_fields.PriceField,
        'finish_type': fx_fields.String(attribute=lambda x: _FINISH_TYPE_MAP[x['finish_type']]),
        'amount_max': ex_fields.AmountField,
        'open_val_max': ex_fields.AmountField,
        'profit_real': ex_fields.AmountField,
        'deal_asset_fee': ex_fields.AmountField,
        'fee_asset': fx_fields.String,
    }

    export_headers = (
        {"field": "create_time", Language.ZH_HANS_CN: "开仓时间"},
        {"field": "update_time", Language.ZH_HANS_CN: "平仓时间"},
        {"field": "market", Language.ZH_HANS_CN: "合约"},
        {"field": "side", Language.ZH_HANS_CN: "类型"},
        {"field": "open_price", Language.ZH_HANS_CN: "开仓均价"},
        {"field": "finish_type", Language.ZH_HANS_CN: "平仓类型"},
        {"field": "amount_max", Language.ZH_HANS_CN: "历史最高数量"},
        {"field": "open_val_max", Language.ZH_HANS_CN: "历史最高价值"},
        {"field": "profit_real", Language.ZH_HANS_CN: "已实现盈亏"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(required=True),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        start_time=fields.Integer(missing=0),
        end_time=fields.Integer(missing=0),
        export=fields.Boolean(missing=False),
        finish_type=fields.Integer(required=False, allow_none=True),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续-历史仓位"""
        market = kwargs['market']
        page, limit = kwargs['page'], kwargs['limit']
        offset = (page - 1) * limit
        start_time = int(kwargs['start_time'] / 1000)
        end_time = int(kwargs['end_time'] / 1000)
        finish_type = kwargs.get('finish_type')
        conds = []
        if start_time:
            conds.append(f'create_time>={start_time}')
        if end_time:
            conds.append(f'create_time<{end_time}')
        if market:
            conds.append(f'market="{market}"')
        if finish_type:
            conds.append(f'finish_type="{finish_type}"')
        cond = ' AND '.join(conds)
        if kwargs['export']:
            history = PerpetualHistoryDB.get_position_history([id_], cond=cond)
            return export_xlsx(
                filename="history-positions",
                data_list=marshal(history, cls.export_marshal_fields),
                export_headers=cls.export_headers,
            )
        records = PerpetualHistoryDB.get_position_history(
            [id_], cond=cond, offset=offset, limit=limit + 1)

        positions = PerpetualHistoryDB.get_position_history(
            [id_], cond=cond, offset=0, limit=2000)
        profit_usd = Decimal()
        prices = PriceManager.assets_to_usd()
        prices["USD"] = Decimal("1")
        markets_info = PerpetualMarketCache().read_aside()
        markets_info.update(PerpetualOfflineMarketCache().read_aside())
        if len(positions) > 0:
            market_profit_details = defaultdict(Decimal)
            for position in positions:
                _market = position["market"]
                if _market not in markets_info:
                    continue
                _profit = Decimal(position["profit_real"])
                market_profit_details[_market] += _profit
            for _market, _total in market_profit_details.items():
                _market_info = markets_info[_market]
                _market_type = _market_info["type"]
                if _market_type == PerpetualMarketType.DIRECT.value:
                    _asset = _market_info["money"]
                else:
                    _asset = _market_info["stock"]
                profit_usd += _total * prices.get(_asset, Decimal())

        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': marshal(records, cls.marshal_fields),
        }
        for item in result['records']:
            _market = item["market"]
            if _market not in markets_info:
                continue
            market_info = markets_info[_market]
            _market_type = market_info['type']

            if _market_type == PerpetualMarketType.INVERSE.value:
                amount_asset = market_info['money']
                balance_asset = market_info['stock']
            else:
                amount_asset = market_info['stock']
                balance_asset = market_info['money']

            item['amount_asset'] = amount_asset
            item['balance_asset'] = balance_asset
            item['position_type'] = _market_type
            item['open_price'] = quantize_amount(item['open_price'], market_info['money_prec'])
        return dict(
            market_info=markets_info,
            market_types=PerpetualMarketType,
            profit_usd=amount_to_str(profit_usd, PrecisionEnum.COIN_PLACES),
            items=offset_to_page(result),
            finish_types=_FINISH_TYPE_MAP
        )


@ns.route('/<int:id_>/position-detail-export')
@respond_with_code
class PositionDetailExportResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        position_id=fields.Integer,
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续-仓位明细-导出"""
        position_id = kwargs['position_id']
        market_info = PerpetualMarketCache().read_aside()
        market_info.update(PerpetualOfflineMarketCache().read_aside())
        sheets = {}
        sheets.update(cls._get_position_deal_detail(id_, position_id, market_info))
        sheets.update(cls._get_position_margin_detail(id_, position_id, market_info))
        sheets.update(cls._get_position_settle_detail(id_, position_id, market_info))
        sheets.update(cls._get_position_funding_detail(id_, position_id, market_info))
        sheets.update(cls._get_position_stop_loss_take_profit_detail(id_, position_id))
        return export_xlsx_with_sheet(sheets, filename='position-detail', temporary=False)

    @classmethod
    def _get_position_deal_detail(cls, user_id, position_id, market_info):
        marshal_fields = {
            'create_time': fx_fields.String(
                attribute=lambda x: datetime_to_str(timestamp_to_datetime(x['time']), offset_minutes=60 * 8)),
            'type': fx_fields.String(
                attribute=lambda x: position_deal_type_map[x['deal_type']]),
            'leverage': fx_fields.String(
                attribute=lambda x: amount_to_str(x['leverage'], 2)),
            'change': ex_fields.AmountField(attribute='amount'),
            'position_amount': ex_fields.AmountField,
            'price': fx_fields.String(
                attribute=lambda x: amount_to_str(x['price'], PrecisionEnum.PRICE_PLACES)),
            'settle_price': ex_fields.PriceField,
            'deal_margin': ex_fields.AmountField(
                attribute=lambda x: x['deal_margin'] if x['deal_type'] in (
                    1, 2)
                else -x['deal_margin']
            ),
            'margin_amount': ex_fields.AmountField,
            'profit_real': ex_fields.DealProfitField(
                attribute=lambda x: (
                    x['deal_profit'], x['deal_fee'], x['fee_asset'])
            ),
            'liq_price': ex_fields.LiqPriceField,
            'order_id': fx_fields.Integer,

            'market': fx_fields.String,
        }
        records = PerpetualHistoryDB.get_deal_history(
            user_id, position_id, limit=ADMIN_EXPORT_LIMIT
        )
        c_asset = u_asset = 'USDT'
        if records:
            c_asset, u_asset = cls._get_display_asset_by(records[0]['market'], market_info)
        records = marshal(records, marshal_fields)
        for item in records:
            info = market_info[item['market']]
            money_prec = info['money_prec']
            item['price'] = quantize_amount(item['price'], money_prec)
            for k in ('liq_price', 'settle_price'):
                # noinspection PyBroadException
                try:
                    item[k] = quantize_amount(item[k], money_prec)
                except Exception:
                    pass
        return {
            '加减仓': {
                'header_mapper': {
                    'create_time': '时间',
                    'type': '类型',
                    'leverage': '杠杆倍数',
                    'change': f'调整数量({u_asset})',
                    'position_amount': f'当前仓位数量({u_asset})',
                    'price': '成交价格',
                    'settle_price': '结算价格',
                    'deal_margin': f'保证金变化({c_asset})',
                    'margin_amount': f'保证金余额({c_asset})',
                    'profit_real': f'已实现盈亏({c_asset})',
                    'liq_price': '强平价格',
                    'order_id': '普通委托订单ID',
                },
                'data': records
            }
        }

    @classmethod
    def _get_position_margin_detail(cls, user_id, position_id, market_info):
        marshal_fields = {
            'create_time': fx_fields.String(
                attribute=lambda x: datetime_to_str(timestamp_to_datetime(x['time']), offset_minutes=60 * 8)),
            'type': fx_fields.String(attribute=lambda x: position_margin_type_map[x['type']][x['position_type']]
            if x['type'] in (4, 5) else position_margin_type_map[x['type']]),
            'leverage': fx_fields.String,
            'settle_price': ex_fields.PriceField,
            'deal_margin': ex_fields.AmountField(
                attribute=lambda x: x['margin_change']
            ),
            'margin_amount': ex_fields.AmountField,
            'liq_price': ex_fields.LiqPriceField,
        }
        records = PerpetualHistoryDB.get_position_margin_history(
            user_id, position_id, limit=ADMIN_EXPORT_LIMIT)
        c_asset = 'USDT'
        if records:
            c_asset, _ = cls._get_display_asset_by(records[0]['market'], market_info)
        ret = marshal(records, marshal_fields)
        return {
            '仓位调整': {
                'header_mapper': {
                    'create_time': '时间',
                    'type': '类型',
                    'leverage': '杠杆倍数',
                    'settle_price': '结算价格',
                    'deal_margin': f'保证金变化({c_asset})',
                    'margin_amount': f'保证金余额({c_asset})',
                    'liq_price': '强平价格',
                },
                'data': ret
            }
        }

    @classmethod
    def _get_position_settle_detail(cls, user_id, position_id, market_info):
        marshal_fields = {
            'create_time': fx_fields.String(
                attribute=lambda x: datetime_to_str(timestamp_to_datetime(x['time']), offset_minutes=60 * 8)),
            'type': fx_fields.String(attribute=lambda x: position_margin_type_map[x['type']][x['position_type']]),
            'leverage': fx_fields.String,
            'settle_price': ex_fields.PriceField,
            'deal_margin': ex_fields.AmountField(
                attribute=lambda x: x['margin_change']
            ),
        }
        records = PerpetualHistoryDB.get_settle_history(
            user_id, position_id, limit=ADMIN_EXPORT_LIMIT)
        c_asset = 'USDT'
        if records:
            c_asset, _ = cls._get_display_asset_by(records[0]['market'], market_info)
        ret = marshal(records, marshal_fields)
        return {
            '结算记录': {
                'header_mapper': {
                    'create_time': '时间',
                    'type': '类型',
                    'leverage': '杠杆倍数',
                    'settle_price': '结算价格',
                    'deal_margin': f'结算资金({c_asset})',
                },
                'data': ret
            }
        }

    @classmethod
    def _get_position_funding_detail(cls, user_id, position_id, market_info):
        marshal_fields = {
            'create_time': fx_fields.String(
                attribute=lambda x: datetime_to_str(timestamp_to_datetime(x['time']), offset_minutes=60 * 8)),
            'funding': ex_fields.AmountField,
            'funding_rate': ex_fields.AmountField,
            'liq_price': ex_fields.LiqPriceField,
        }
        records = PerpetualHistoryDB.get_position_funding_history(
            user_id, position_id=position_id, limit=ADMIN_EXPORT_LIMIT)
        c_asset = 'USDT'
        if records:
            c_asset, _ = cls._get_display_asset_by(records[0]['market'], market_info)
        ret = marshal(records, marshal_fields)
        return {
            '资金费用': {
                'header_mapper': {
                    'create_time': '时间',
                    'funding': f'金额({c_asset})',
                    'funding_rate': '资金费率(理论)',
                    'liq_price': '强平价格',
                },
                'data': ret
            }
        }

    @classmethod
    def _get_position_stop_loss_take_profit_detail(cls, user_id, position_id):

        def fmt_stop_price(x):
            stop_price = amount_to_str(x["stop_price"], 12)
            if x['stop_price'] == 0:
                return f'{stop_price}'
            if x['state'] == 1:
                return f'≤{stop_price}'
            else:
                return f'≥ {stop_price}'

        type_map = {1: "止盈", 2: "止损"}
        stop_type_map = {1: "最新成交", 2: "标记价格", 3: "标记价格"}
        marshal_fields = {
            'create_time': fx_fields.String(
                attribute=lambda x: datetime_to_str(timestamp_to_datetime(x['time']), offset_minutes=60 * 8)),
            'type': fx_fields.String(attribute=lambda x: type_map[x['type']]),
            'stop_price': ex_fields.String(attribute=lambda x: fmt_stop_price(x)),
            'stop_type': fx_fields.String(attribute=lambda x: stop_type_map[x['stop_type']]),
            'amount': ex_fields.AmountField,
        }
        records = PerpetualHistoryDB.get_position_stop_loss_take_profit_history(
            user_id, position_id, limit=ADMIN_EXPORT_LIMIT)
        ret = marshal(records, marshal_fields)
        return {
            '止盈止损': {
                'header_mapper': {
                    'create_time': '时间',
                    'type': '设置类型',
                    'stop_price': '设置价格',
                    'stop_type': '价格类型',
                    'amount': '仓位数量',
                },
                'data': ret
            }
        }

    @classmethod
    def _get_display_asset_by(cls, market, market_info):
        market = market_info[market]
        if market['type'] == PerpetualMarketType.DIRECT:
            c, u = market['money'], market['stock']
        else:
            c, u = market['stock'], '张'
        return c, u


@ns.route('/<int:id_>/position-deal-detail')
@respond_with_code
class PositionDealDetailResource(Resource):
    marshal_fields = {
        'market': fx_fields.String,
        'type': fx_fields.String(
            attribute=lambda x: position_deal_type_map[x['deal_type']]),
        'margin_amount': ex_fields.AmountField,
        'change': ex_fields.AmountField(attribute='amount'),
        'position_amount': ex_fields.AmountField,
        'create_time': fx_fields.Float(attribute='time'),
        'deal_fee': fx_fields.String,
        'profit_real': ex_fields.DealProfitField(
            attribute=lambda x: (
                x['deal_profit'], x['deal_fee'], x['fee_asset'])
        ),
        'deal_margin': ex_fields.AmountField(
            attribute=lambda x: x['deal_margin'] if x['deal_type'] in (
                1, 2)
            else -x['deal_margin']
        ),
        'leverage': fx_fields.String(
            attribute=lambda x: amount_to_str(x['leverage'], 2)),
        'price': fx_fields.String(
            attribute=lambda x: amount_to_str(x['price'], PrecisionEnum.PRICE_PLACES)),
        'deal_id': fx_fields.Integer(attribute='id'),
        'fee_asset': fx_fields.String,
        'order_id': fx_fields.Integer,
        'liq_price': ex_fields.LiqPriceField,
        'settle_price': ex_fields.PriceField,
    }

    @classmethod
    @ns.use_kwargs(dict(
        position_id=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续-仓位明细-加减仓"""
        page = kwargs['page']
        limit = kwargs['limit']
        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_deal_history(
            id_, kwargs['position_id'], offset, limit + 1
        )
        records = marshal(records, cls.marshal_fields)
        market_info = PerpetualMarketCache().read_aside()
        market_info.update(PerpetualOfflineMarketCache().read_aside())
        for item in records:
            info = market_info[item['market']]
            money_prec = info['money_prec']
            item['price'] = quantize_amount(item['price'], money_prec)
            for k in ('liq_price', 'settle_price'):
                # noinspection PyBroadException
                try:
                    item[k] = quantize_amount(item[k], money_prec)
                except Exception:
                    pass
        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': records
        }
        return offset_to_page(result)


@ns.route('/<int:id_>/position-margin-detail')
@respond_with_code
class PositionMarginResource(Resource):
    marshal_fields = {
        'type': fx_fields.String(attribute=lambda x: position_margin_type_map[x['type']][x['position_type']]
        if x['type'] in (4, 5) else position_margin_type_map[x['type']]),
        'leverage': fx_fields.String,
        'margin_amount': ex_fields.AmountField,
        'change': fx_fields.String(attribute=lambda x: '-'),
        'create_time': fx_fields.Float(attribute='time'),
        'profit_real': fx_fields.String(attribute=lambda x: '0'),
        'liq_price': ex_fields.LiqPriceField,
        'deal_margin': ex_fields.AmountField(
            attribute=lambda x: x['margin_change']
        ),
        'settle_price': ex_fields.PriceField,
    }

    @classmethod
    @ns.use_kwargs(dict(
        position_id=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续-仓位明细-追加/减少保证金"""
        page = kwargs['page']
        limit = kwargs['limit']
        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_position_margin_history(
            id_, kwargs['position_id'], offset, limit + 1)
        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': marshal(records, cls.marshal_fields)
        }
        return offset_to_page(result)


@ns.route('/<int:id_>/position-settle-detail')
@respond_with_code
class SettleDetailResource(Resource):
    marshal_fields = {
        'type': fx_fields.String(attribute=lambda x: position_margin_type_map[x['type']][x['position_type']]),
        'create_time': fx_fields.Integer(attribute='time'),
        'leverage': fx_fields.String,
        'deal_margin': ex_fields.AmountField(
            attribute=lambda x: x['margin_change']
        ),
        'settle_price': ex_fields.PriceField
    }

    @classmethod
    @ns.use_kwargs(dict(
        position_id=fields.Integer(
            required=True,
        ),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续-仓位明细-结算记录"""
        page = kwargs['page']
        limit = kwargs['limit']
        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_settle_history(
            id_, kwargs['position_id'], offset, limit + 1)
        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': marshal(records, cls.marshal_fields)
        }
        return offset_to_page(result)


@ns.route('/<int:id_>/position-funding-detail')
@respond_with_code
class FundingDetailResource(Resource):
    marshal_fields = {
        'funding_rate': ex_fields.AmountField,
        'funding': ex_fields.AmountField,
        'liq_price': ex_fields.LiqPriceField,
        'create_time': fx_fields.Float(attribute='time'),
    }

    @classmethod
    @ns.use_kwargs(dict(
        position_id=fields.Integer,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续-仓位明细-资金费用"""
        page = kwargs['page']
        limit = kwargs['limit']
        offset = (page - 1) * limit
        records = PerpetualHistoryDB.get_position_funding_history(
            id_, position_id=kwargs['position_id'], offset=offset, limit=limit + 1
        )
        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': marshal(records, cls.marshal_fields)
        }
        return offset_to_page(result)


@ns.route("/<int:id_>/position-stop-loss-take-profit-history")
@respond_with_code
class PositionStopLossTakeProfitHistoryResource(Resource):
    type_map = {1: "止盈", 2: "止损"}
    stop_type_map = {1: "最新成交", 2: "标记价格", 3: "标记价格"}

    @classmethod
    @ns.use_kwargs(
        dict(
            position_id=fields.Integer,
            page=ex_fields.PageField(unlimited=True),
            limit=ex_fields.LimitField,
        )
    )
    def get(cls, id_, **kwargs):
        """用户详情-永续-仓位明细-止盈止损"""
        position_id = kwargs["position_id"]
        page = kwargs["page"]
        limit = kwargs["limit"]

        client = PerpetualServerClient()
        result = client.position_stop_loss_take_profit_history(id_, position_id, page, limit)
        for r in result["records"]:
            r["type_str"] = cls.type_map.get(r["type"], r["type"])
            r["stop_type_str"] = cls.stop_type_map.get(r["stop_type"], r["stop_type"])
        return offset_to_page(result)


@ns.route('/<int:id_>/perpetual-pending-orders')
@respond_with_code
class UserPerpetualPendingOrderResource(Resource):
    base_marshal_fields = {
        'order_id': fx_fields.Integer,
        'create_time': fx_fields.Float,
        'market': fx_fields.String,
        'source': fx_fields.String,
        'side': ex_fields.EnumMarshalField(OrderSideType, output_field_lower=False),
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'stop_loss_price': ex_fields.PriceField,
        'take_profit_price': ex_fields.PriceField,
    }

    normal_fields = deepcopy(base_marshal_fields)
    normal_fields.update(
        dict(
            deal_amount=ex_fields.AmountField(
                attribute=lambda x: Decimal(x['amount']) - Decimal(x['left'])
            ),
            average_price=ex_fields.PerpetualAveragePriceField(
                attribute=lambda x: (x['market'], x['amount'], x['left'], x['deal_stock'])
            ),
            order_type=ex_fields.EnumMarshalField(
                OrderIntType, output_field_lower=False, attribute='type'
            ),
            option=fx_fields.Integer,
            reduce_only=fx_fields.Boolean(
                attribute=lambda x: bool(x['option'] & PerpetualOrderOption.REDUCE_ONLY)
            )
        )
    )

    stop_fields = deepcopy(base_marshal_fields)
    stop_fields.update(
        dict(
            stop_price=ex_fields.AmountField,
            state=fx_fields.Integer,
            stop_type=ex_fields.EnumMarshalField(StopOrderType),
            order_type=ex_fields.EnumMarshalField(
                StopOrderIntType, output_field_lower=False, attribute='type'
            )
        )
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            type=ex_fields.EnumField(enum=['normal', 'stop'], required=True),
            market=fields.String,
            side=ex_fields.EnumField(OrderSideType),
            page=ex_fields.PageField(unlimited=True),
            limit=ex_fields.LimitField,
        )
    )
    def get(cls, id_, **kwargs):
        """用户详情-永续-当前委托"""
        market = kwargs.get('market') or None
        side = kwargs.get('side') or 0
        page, limit = kwargs['page'], kwargs['limit']
        client = PerpetualServerClient()
        if kwargs['type'] == 'normal':
            result = client.pending_order(id_, market, side, page, limit)
            result['records'] = marshal(result['records'], cls.normal_fields)
        else:
            result = client.pending_stop(id_, market, side, page, limit)
            result['records'] = marshal(result['records'], cls.stop_fields)

        markets = list(set(r['market'] for r in result['records']))
        market_asset_map = dict()
        for m in markets:
            amount_asset = PerpetualMarketCache.get_amount_asset(m)
            balance_asset = PerpetualMarketCache.get_balance_asset(m)
            market_asset_map[m] = dict(
                amount_asset=amount_asset,
                balance_asset=balance_asset
            )
        for r in result['records']:
            r['amount_asset'] = market_asset_map[r['market']]['amount_asset']
            r['balance_asset'] = market_asset_map[r['market']]['balance_asset']
        return dict(
            market_types=PerpetualMarketType,
            items=offset_to_page(result)
        )

    @classmethod
    @ns.use_kwargs(dict(
        type=ex_fields.EnumField(enum=['normal', 'stop'], required=True),
        market=fields.String(required=True),
        order_id=fields.Integer(required=True)
    ))
    def delete(cls, id_, **kwargs):
        """用户详情-永续-撤销订单"""
        """
        撤销订单
        """
        market = kwargs['market']
        order_id = kwargs['order_id']
        if market not in PerpetualMarketCache().get_market_list():
            raise InvalidArgument
        client = PerpetualServerClient(current_app.logger)
        if kwargs['type'] == 'normal':
            with CacheLock(
                    LockKeys.perpetual_cancel_order(id_),
                    wait=False
            ):
                client.cancel_order(id_, market, order_id)
        else:
            with CacheLock(
                    LockKeys.perpetual_cancel_stop_order(id_),
                    wait=False
            ):
                client.cancel_stop(id_, market, order_id)


@ns.route('/<int:id_>/perpetual-pending-orders/batch')
@respond_with_code
class UserPerpetualPendingOrderBatchDeleteResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        type=ex_fields.EnumField(enum=['normal', 'stop'], required=True)
    ))
    def delete(cls, id_, **kwargs):
        """
        用户详情-永续-批量撤销订单
        """
        client = PerpetualServerClient(current_app.logger)
        markets = PerpetualMarketCache().get_market_list()
        for market in markets:
            if kwargs['type'] == 'normal':
                with CacheLock(
                        LockKeys.perpetual_cancel_order(id_),
                        wait=False
                ):
                    client.cancel_all(id_, market)
            else:
                with CacheLock(
                        LockKeys.perpetual_cancel_stop_order(id_),
                        wait=False
                ):
                    client.cancel_stop_all(id_, market)


@ns.route('/<int:id_>/perpetual-history-orders')
@respond_with_code
class UserPerpetualFinishedOrderResource(Resource):
    marshal_fields = {
        'order_id': fx_fields.Integer,
        'create_time': fx_fields.Integer,
        'stop_id': fx_fields.Integer,
        'client_id': fx_fields.String,
        'market': fx_fields.String,
        'source': fx_fields.String,
        'option': fx_fields.Integer,
        'effect_type': fx_fields.Integer,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'option_str': fx_fields.String(attribute=lambda x: OrderHistoryMixin.format_perpetual_option_str(x)),
        'side': fx_fields.String(attribute=lambda x: Order.OrderSideType(
            int(x['side'])).name.lower()),
        'order_type': ex_fields.EnumMarshalField(
            OrderIntType, output_field_lower=False, attribute='type'),
        'deal_amount': ex_fields.AmountField(
            attribute=lambda x: Decimal(x['amount']) - Decimal(
                x['left'])),
        'deal_price': ex_fields.PriceField(
            attribute=lambda x: (Decimal(x['amount']) - Decimal(
                x['left'])
                                 ) / Decimal(x['deal_stock'])
        ),
        'deal_value': ex_fields.AmountField(
            attribute='deal_stock'
        ),
        'status': ex_fields.PerpetualOrderStatusField(attribute=lambda x: x),
        'source_type': ex_fields.String(attribute=lambda x: '系统' if x['source'] == 'sys' else '用户')
    }

    export_marshal_fields = {
        'order_id': fx_fields.Integer,
        'create_time': fx_fields.String(attribute=lambda x:
        timestamp_to_datetime(x['create_time']).strftime('%Y-%m-%d %H:%M:%S')),
        'market': fx_fields.String,
        'order_type': fx_fields.String(attribute=lambda x: '市价' \
            if x['type'] == OrderIntType.MARKET else '限价'),
        'side': fx_fields.String(attribute=lambda x: '买入' \
            if x['side'] == OrderSideType.BUY else '卖出'),
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'deal_amount': ex_fields.AmountField(
            attribute=lambda x: Decimal(x['amount']) - Decimal(
                x['left'])),
        'deal_price': ex_fields.PerpetualAveragePriceField(
            attribute=lambda x: (x['market'], x['amount'], x['left'], x['deal_stock'])
        ),
        'deal_value': ex_fields.AmountField(
            attribute='deal_stock'
        ),
        'source': fx_fields.String,
        'option_str': fx_fields.String(attribute=lambda x: OrderHistoryMixin.format_perpetual_option_str(x)),
        'stop_id': fx_fields.String(attribute=lambda x: x['stop_id'] if x['stop_id'] else '--'),
        'client_id': fx_fields.String(attribute=lambda x: x['client_id'] if x['client_id'] else '--'),
    }

    export_headers = (
        {"field": "order_id", Language.ZH_HANS_CN: "订单ID"},
        {"field": "create_time", Language.ZH_HANS_CN: "委托时间"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "order_type", Language.ZH_HANS_CN: "委托类型"},
        {"field": "side", Language.ZH_HANS_CN: "方向"},
        {"field": "price", Language.ZH_HANS_CN: "委托价"},
        {"field": "amount", Language.ZH_HANS_CN: "委托数量"},
        {"field": "deal_amount", Language.ZH_HANS_CN: "成交数量"},
        {"field": "deal_price", Language.ZH_HANS_CN: "成交均价"},
        {"field": "deal_value", Language.ZH_HANS_CN: "成交额"},
        {"field": "source", Language.ZH_HANS_CN: "委托人"},
        {"field": "stop_id", Language.ZH_HANS_CN: "计划委托"},
        {"field": "client_id", Language.ZH_HANS_CN: "client_id"},
        {"field": "option_str", Language.ZH_HANS_CN: "高级设置"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        side=ex_fields.EnumField(OrderSideType),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        start_time=fields.Integer(missing=0),
        end_time=fields.Integer(missing=0),
        stop_order_id=fields.Integer(),
        order_id=fields.Integer(),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续-历史普通委托"""
        market = kwargs['market']
        side = kwargs.get('side') or 0
        order_id = kwargs.get('order_id') or 0
        stop_order_id = kwargs.get('stop_order_id') or 0
        page, limit = kwargs['page'], kwargs['limit']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']

        if kwargs['export']:
            fields = ['order_id', 'create_time', 'market', 'type', 'side',
                      'price', 'amount', 'left', 'deal_stock', 'source', 'stop_id', 'client_id']
            conds = []
            if start_time:
                conds.append(f'create_time>={start_time}')
            if end_time:
                conds.append(f'create_time<{end_time}')
            if market:
                conds.append(f'market="{market}"')
            if order_id:
                conds.append(f'order_id={order_id}')
            if side:
                conds.append(f'side={side}')
            cond = ' AND '.join(conds)
            history = PerpetualHistoryDB.get_order_history([id_],
                                                           fields=fields,
                                                           cond=cond,
                                                           table_type='order_history')
            return export_xlsx(
                filename="perpetual-history-orders",
                data_list=marshal(history, cls.export_marshal_fields),
                export_headers=cls.export_headers,
            )

        client = PerpetualServerClient()
        if not order_id:
            result = client.order_finished(
                id_, market, side, start_time, end_time, stop_order_id, page, limit,
                PERPETUAL_ALL_MARKET_TYPE)
        else:
            order = client.finished_order_detail(id_, order_id)
            result = [order] if order else []

        format_result = marshal(result, cls.marshal_fields)
        markets = list(set(r['market'] for r in format_result))
        market_asset_map = dict()
        online_market_list = set(PerpetualMarketCache().get_market_list())
        for m in markets:
            amount_asset = (
                PerpetualMarketCache.get_amount_asset(
                    m) if m in online_market_list else PerpetualOfflineMarketCache.get_amount_asset(m)
            )
            balance_asset = (
                PerpetualMarketCache.get_balance_asset(
                    m) if m in online_market_list else PerpetualOfflineMarketCache.get_balance_asset(m)
            )
            market_asset_map[m] = dict(
                amount_asset=amount_asset,
                balance_asset=balance_asset
            )

        all_market_type_map = cls.get_all_market_type_map(markets)
        result_map = {i["order_id"]: i for i in result}
        for r in format_result:
            r['amount_asset'] = market_asset_map[r['market']]['amount_asset']
            r['balance_asset'] = market_asset_map[r['market']]['balance_asset']
            # admin 用户 -> 合约订单历史委托 能查看下架市场的成交均价
            r["average_price"] = cls.get_average_price(
                all_market_type_map[r['market']],
                result_map[r["order_id"]],
            )
        return dict(
            result=format_result,
            has_next=result.has_next if not order_id else False,
            curr_page=result.page if not order_id else 1,
        )

    @classmethod
    def get_all_market_type_map(cls, names):
        model = PerpetualMarket
        ret = model.query.filter(model.name.in_(names)).with_entities(model.name, model.market_type)
        return {i.name: i.market_type for i in ret}

    @classmethod
    def get_average_price(cls, market_type, item):
        amount, left, deal_stock = item['amount'], item['left'], item['deal_stock']
        if market_type == PerpetualMarketType.DIRECT.value:
            if not (deal_amount := (Decimal(amount) - Decimal(left))):
                return '0'
            return amount_to_str(Decimal(deal_stock) / deal_amount, PrecisionEnum.PRICE_PLACES)
        else:
            if not Decimal(deal_stock):
                return '0'
            return amount_to_str(
                (Decimal(amount) - Decimal(left)) / Decimal(deal_stock), PrecisionEnum.PRICE_PLACES)


@ns.route('/<int:id_>/perpetual-position-profit')
@respond_with_code
class UserPerpetualPositionProfitResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        start_time=fields.Integer(missing=0),
        end_time=fields.Integer(missing=0),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续-盈亏统计"""
        markets_info = PerpetualMarketCache().read_aside()
        markets_info.update(PerpetualOfflineMarketCache().read_aside())
        offline_market_list = set(PerpetualOfflineMarketCache().get_market_list())

        start_time = int(kwargs['start_time'] / 1000)
        end_time = int(kwargs['end_time'] / 1000)
        conds = []
        if start_time:
            conds.append(f'create_time>={start_time}')
        if end_time:
            conds.append(f'create_time<{end_time}')
        cond = ' AND '.join(conds)
        positions = PerpetualHistoryDB.get_position_history(
            [id_], cond=cond, offset=0, limit=2000)
        market_profit_details = defaultdict(
            lambda: {
                "win_count": 0,
                "total_count": 0,
                "status": 1,  # 上架/下架
                "total_profit": Decimal(),
                "roi": Decimal(),
                "amount_max_margin": Decimal(),
                "profit_asset": ""
            }
        )
        for position in positions:
            _market = position["market"]
            if _market not in markets_info:
                continue
            _market_info = markets_info[_market]
            _market_type = _market_info['type']
            _profit = Decimal(position["profit_real"])
            amount_max_margin = Decimal(position["amount_max_margin"])
            market_profit_details[_market]["total_count"] += 1
            market_profit_details[_market]["total_profit"] += _profit
            if _profit > 0:
                market_profit_details[_market]["win_count"] += 1
            market_profit_details[_market]["amount_max_margin"] += amount_max_margin

            if _market_type == PerpetualMarketType.DIRECT.value:
                _asset = _market_info["money"]
            else:
                _asset = _market_info["stock"]

            market_profit_details[_market]["profit_asset"] = _asset
        all_infos = list(markets_info.values())
        all_infos.sort(key=lambda x: PerpetualMarketComparator(x['stock'], x['money']))
        return_data = []

        def _get_roi(x) -> str:
            if Decimal(x['amount_max_margin']) == 0:
                return '0%'
            return format_percent(Decimal(x['total_profit']) / Decimal(x['amount_max_margin']))

        prices = PriceManager.assets_to_usd()
        prices["USD"] = Decimal('1')

        def _get_usd(x, _field: str) -> Decimal:
            return Decimal(x[_field]) * prices.get(x["profit_asset"], Decimal())

        for _market_info in all_infos:
            _market = _market_info["name"]
            if _market not in market_profit_details:
                continue

            return_data.append(
                market_profit_details[_market] |
                {
                    "market": _market,
                    "status": 1 if _market not in offline_market_list else 0,
                    "roi": _get_roi(market_profit_details[_market]),
                    "total_profit_usd": _get_usd(market_profit_details[_market], "total_profit"),
                    "amount_max_margin_usd": _get_usd(market_profit_details[_market], "amount_max_margin")
                }
            )
        all_sum_data = {
            "market": "<ALL>",
            "win_count": 0,
            "total_count": 0,
            "status": 1,
            "total_profit": Decimal(),
            "total_profit_usd": Decimal(),
            "roi": Decimal(),
            "amount_max_margin": Decimal(),
            "amount_max_margin_usd": Decimal(),
            "profit_asset": "USD"
        }
        for _field in ["win_count", "total_count", "total_profit_usd", "total_profit_usd", "amount_max_margin_usd"]:
            all_sum_data[_field] = sum([v[_field] for v in return_data])

        _total_profit_usd = all_sum_data["total_profit_usd"]
        _amount_max_margin_usd = all_sum_data["amount_max_margin_usd"]
        _roi = format_percent(_total_profit_usd / _amount_max_margin_usd) \
            if _amount_max_margin_usd > 0 else "0%"
        all_sum_data["roi"] = _roi
        return_data.insert(0, all_sum_data)
        return return_data


_stop_order_types = {
    StopOrderIntType.STOP_LIMIT: '限价',
    StopOrderIntType.STOP_MARKET: '市价'
}


@ns.route('/<int:id_>/perpetual-history-stop-orders')
@respond_with_code
class UserPerpetualHistoryStopOrderResource(Resource):
    marshal_fields = {
        'order_id': fx_fields.Integer,
        'create_time': fx_fields.Integer,
        'update_time': fx_fields.Integer,
        'market': fx_fields.String,
        'amount': fx_fields.String(attribute=lambda x: amount_to_str(x['amount'], 8)),
        'source': fx_fields.String,
        'option': fx_fields.Integer,
        'stop_price': Order.PriceFields(attribute=lambda x: (x['stop_price'], x['market'])),
        'price': Order.PriceFields(attribute=lambda x: (x['price'], x['market'])),
        'status': fx_fields.Integer,
        'deal_status': fx_fields.String(),
        'stop_type': ex_fields.EnumMarshalField(StopOrderType),
        'state': fx_fields.Integer,
        'deal_type': fx_fields.String(attribute=lambda x: position_target_map[int(x['target'])]
        if int(x['target']) != 0 else _stop_order_types[int(x['type'])]),
        'side': fx_fields.String(attribute=lambda x: Order.OrderSideType(
            int(x['side'])).name.lower()),
        'order_type': ex_fields.EnumMarshalField(
            OrderIntType, output_field_lower=False, attribute='type')
    }

    export_marshal_fields = {
        'order_id': fx_fields.Integer,
        'create_time': fx_fields.String(attribute=lambda x:
        timestamp_to_datetime(x['create_time']).strftime('%Y-%m-%d %H:%M:%S')),
        'update_time': fx_fields.String(attribute=lambda x:
        timestamp_to_datetime(x['update_time']).strftime('%Y-%m-%d %H:%M:%S')),
        'market': fx_fields.String,
        'order_type': fx_fields.String(attribute=lambda x: position_target_map[int(x['target'])]
        if int(x['target']) != 0 else _stop_order_types[int(x['type'])]),
        'side': fx_fields.String(attribute=lambda x: '买入' if x['side'] == Order.OrderSideType.BUY else '卖出'),
        'price': Order.PriceFields(attribute=lambda x: (x['price'], x['market'])),
        'amount': fx_fields.String(attribute=lambda x: amount_to_str(x['amount'], 8)),
        'stop_price': Order.PriceFields(attribute=lambda x: (x['stop_price'], x['market'])),
        'status': fx_fields.String(
            attribute=lambda x: '已委托' if x['status'] == StopOrderStatusIntType.ACTIVE else '委托失败'),
        'deal_status': fx_fields.String(),
        # 'source': fx_fields.String,
        # 'stop_type': ex_fields.EnumMarshalField(StopOrderType),
        # 'deal_type': fx_fields.String(attribute=lambda x: position_target_map[int(x['target'])]
        # if int(x['target']) != 0 else _stop_order_types[int(x['type'])]),
    }

    export_headers = (
        {"field": "order_id", Language.ZH_HANS_CN: "订单ID"},
        {"field": "create_time", Language.ZH_HANS_CN: "委托时间"},
        {"field": "update_time", Language.ZH_HANS_CN: "触发时间"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "order_type", Language.ZH_HANS_CN: "委托类型"},
        {"field": "side", Language.ZH_HANS_CN: "方向"},
        {"field": "price", Language.ZH_HANS_CN: "委托价"},
        {"field": "amount", Language.ZH_HANS_CN: "委托数量"},
        {"field": "stop_price", Language.ZH_HANS_CN: "触发价"},
        {"field": "status", Language.ZH_HANS_CN: "委托状态"},
        {"field": "deal_status", Language.ZH_HANS_CN: "成交状态"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        side=ex_fields.EnumField(OrderSideType),
        status=ex_fields.EnumField(enum=StopOrderStatusIntType, missing=0),
        start_time=fields.Integer(missing=0),
        end_time=fields.Integer(missing=0),
        order_id=fields.Integer(),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续-历史计划委托"""
        market = kwargs['market']
        side = kwargs.get('side') or 0
        order_id = kwargs.get('order_id') or 0
        status = kwargs['status']
        page, limit = kwargs['page'], kwargs['limit']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        if kwargs['export']:
            fields = ['order_id', 'create_time', 'update_time', 'market', 'type', 'side', 'stop_price',
                      'price', 'amount', 'status', 'source', 'target']
            conds = []
            if start_time:
                conds.append(f'create_time >= {start_time}')
            if end_time:
                conds.append(f'create_time < {end_time}')
            if market:
                conds.append(f'market="{market}"')
            if order_id:
                conds.append(f'order_id={order_id}')
            if side:
                conds.append(f'side={side}')
            cond = ' AND '.join(conds)

            history = PerpetualHistoryDB.get_order_history([id_],
                                                           fields=fields,
                                                           cond=cond,
                                                           table_type='stop_history')
            cls._update_result(user_id=id_, result=history)

            return export_xlsx(
                filename="perpetual-history-stop-orders",
                data_list=marshal(history, cls.export_marshal_fields),
                export_headers=cls.export_headers,
            )

        client = PerpetualServerClient()

        if not order_id:
            result = client.finished_stop(
                id_, market, side, start_time, end_time, page, limit, status,
                PERPETUAL_ALL_MARKET_TYPE)
        else:
            order = client.finished_stop_order_detail(
                user_id=id_,
                order_id=order_id,
            )
            result = [order] if order else []

        cls._update_result(user_id=id_, result=result)

        format_result = marshal(result, cls.marshal_fields)
        markets = list(set(r['market'] for r in format_result))
        market_asset_map = dict()
        online_market_list = set(PerpetualMarketCache().get_market_list())
        for m in markets:
            amount_asset = (
                PerpetualMarketCache.get_amount_asset(
                    m) if m in online_market_list else PerpetualOfflineMarketCache.get_amount_asset(m)
            )
            balance_asset = (
                PerpetualMarketCache.get_balance_asset(
                    m) if m in online_market_list else PerpetualOfflineMarketCache.get_balance_asset(m)
            )
            market_asset_map[m] = dict(
                amount_asset=amount_asset,
                balance_asset=balance_asset
            )
        for r in format_result:
            r['amount_asset'] = market_asset_map[r['market']]['amount_asset']
            r['balance_asset'] = market_asset_map[r['market']]['balance_asset']
        return dict(
            result=format_result,
            has_next=result.has_next if not order_id else False,
            page=result.page if not order_id else 1,
        )

    @classmethod
    def _update_result(cls, user_id, result):
        if not result:
            return
        stop_ids_str = ','.join(map(str, [x['order_id'] for x in result]))
        order_history = PerpetualHistoryDB.get_order_history(
            [user_id],
            fields=['stop_id', 'amount', 'left'],
            cond=f'stop_id in ({stop_ids_str})',
            table_type='order_history')
        stop_id_mapping = {}
        for x in order_history:
            stop_id_mapping.setdefault(x['stop_id'], []).append(x)
        for x in result:
            stop_id = x['order_id']
            mappings = stop_id_mapping.get(stop_id)
            if not mappings:
                x['deal_status'] = '未成交'
            else:
                x['deal_status'] = '部分成交'
                for mapping in mappings:
                    if mapping['left'] == 0:
                        x['deal_status'] = '完全成交'
                        break


@ns.route('/<int:id_>/deposit-info')
@respond_with_code
class UserDepositInfoResource(Resource):

    export_headers = (
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "address", Language.ZH_HANS_CN: "充值地址"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        chain=fields.String(),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        export=fields.Boolean(missing=False),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-充值信息"""
        chain = kwargs.get('chain')
        page = kwargs['page']
        limit = kwargs['limit']
        user_id = id_

        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        if kwargs['export']:
            r = WalletClient().get_deposit_addresses(user_id, chain, 1, 1000)
            for item in r:
                item['created_at'] = datetime_to_utc8_str(timestamp_to_datetime(item['created_at']))
                item['updated_at'] = datetime_to_utc8_str(timestamp_to_datetime(item['updated_at']))
            return export_xlsx(
                filename='deposit_info',
                data_list=r,
                export_headers=cls.export_headers,
            )

        r = WalletClient().get_deposit_addresses(user_id, chain, page, limit)

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.DepositInfo,
            detail=kwargs,
            target_user_id=id_,
        )

        return dict(
            record_list=r,
            total=r.total,
            assets=list_all_assets(),
        )


@ns.route('/<int:id_>/withdrawal-info')
@respond_with_code
class UserWithdrawalInfoResource(Resource):

    export_headers = (
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "address", Language.ZH_HANS_CN: "提现地址"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        chain=fields.String(),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        export=fields.Boolean(missing=False),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-提现信息"""
        chain = kwargs.get('chain')
        page = kwargs['page']
        limit = kwargs['limit']
        user_id = id_

        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        withdraw_addresses = WithdrawalAddress.query.filter(
            WithdrawalAddress.user_id == user_id,
        ).order_by(WithdrawalAddress.id.desc())
        if chain:
            withdraw_addresses = withdraw_addresses.filter(
                WithdrawalAddress.chain == chain,
            )

        if kwargs['export']:
            record_list = []
            for item in list(withdraw_addresses.limit(ADMIN_EXPORT_LIMIT).all()):
                record = item.to_dict()
                record['created_at'] = datetime_to_utc8_str(record['created_at'])
                record['updated_at'] = datetime_to_utc8_str(record['updated_at'])
                record['status'] = record['status'].value
                record_list.append(record)
            return export_xlsx(
                filename='withdrawal_info',
                data_list=record_list,
                export_headers=cls.export_headers,
            )

        pagination = withdraw_addresses.paginate(page, limit)
        record_list = [item.to_dict() for item in pagination.items]

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.WithdrawalInfo,
            detail=kwargs,
            target_user_id=id_,
        )

        return dict(
            record_list=record_list,
            total=pagination.total,
            chains=list_all_chains()
        )


@ns.route('/<int:id_>/operation-logs')
@respond_with_code
class UserOperationLogsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, id_, **kwargs):
        """用户详情-操作记录"""
        page, limit = kwargs['page'], kwargs['limit']
        query = OperationLog.query.filter(OperationLog.user_id == id_).order_by(
            OperationLog.id.desc())
        records = query.paginate(page, limit)
        ops = {x.name: x.value for x in OperationLog.Operation}
        items = [{
            'id': x.id,
            'created_at': x.created_at,
            'user_id': x.user_id,
            'operation': x.operation,
            'operation_name': ops.get(x.operation, x.operation),
            'platform': x.platform,
            'detail': x.detail
        } for x in records.items]

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.OperationHistory,
            detail=kwargs,
            target_user_id=id_,
        )

        return {
            'items': items,
            'total': records.total
        }


@ns.route('/<int:id_>/admin-operation-logs')
@respond_with_code
class AdminOperationLogsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        op_type=fields.String(),    # 空=ALL、QUERY=查询、EDIT=除查询以外的其他类型
        started_at=TimestampField,
        ended_at=TimestampField,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
    ))
    def get(cls, id_, **kwargs):
        """用户详情-客服记录"""
        from app.api.admin.op_log import AdminOperationLogResource

        query = AdminOperationLog.query.filter(AdminOperationLog.target_user_id == id_)

        op_type = kwargs.get('op_type')
        if op_type:
            if op_type == AdminOperationLog.Operation.QUERY.name:
                query = query.filter(AdminOperationLog.operation == AdminOperationLog.Operation.QUERY)
            else:
                query = query.filter(AdminOperationLog.operation != AdminOperationLog.Operation.QUERY)

        started_at, ended_at = kwargs.get('started_at'), kwargs.get('ended_at')
        if started_at and ended_at:
            if started_at >= ended_at:
                raise InvalidArgument(message='开始时间必须小于结束时间')
        if started_at:
            query = query.filter(AdminOperationLog.created_at >= started_at)
        if ended_at:
            query = query.filter(AdminOperationLog.created_at <= ended_at)

        total = query.count()

        page, limit = kwargs['page'], kwargs['limit']
        offset = (page - 1) * limit
        res = query.order_by(AdminOperationLog.created_at.desc()).offset(offset).limit(limit).all()

        user_ids = [item.user_id for item in res if item.user_id]
        user_name_map = get_admin_user_name_map(user_ids)

        namespace_desc_map = {namespace.name: namespace.desc for namespace in BaseOPNamespaceObjectMeta.namespaces()}
        object_desc_map = {
            namespace.name: {
                ns_obj.object.name: ns_obj.object.desc
                for ns_obj in BaseOPNamespaceObjectMeta.objects(namespace)
            } for namespace in BaseOPNamespaceObjectMeta.namespaces()
        }

        items = [
            dict(
                id=item.id,
                created_at=item.created_at,
                namespace=namespace_desc_map.get(item.namespace, item.namespace),
                object=object_desc_map.get(item.namespace, {}).get(item.object, item.object),
                operation=AdminOperationLogResource.OPERATION_NAME_MAP.get(item.operation.name, item.operation.value),
                detail=item.show_detail(),
                user=dict(
                    user_id=item.user_id,
                    name_or_email=user_name_map.get(item.user_id, str(item.user_id)),
                ),
            ) for item in res
        ]
        return {
            'items': items,
            'total': total,
        }


@ns.route('/<int:id_>/login-history')
@respond_with_code
class UserLoginHistoryResource(Resource):

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "登陆时间"},
        {"field": "location", Language.ZH_HANS_CN: "登陆地点"},
        {"field": "ip", Language.ZH_HANS_CN: "登陆IP"},
        {"field": "user_agent_human", Language.ZH_HANS_CN: "设备信息"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "fail_reason_str", Language.ZH_HANS_CN: "失败原因"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
        export=fields.Boolean(missing=False),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-登录记录"""
        page, limit = kwargs['page'], kwargs['limit']
        sign_in_logs = LoginHistory.query.filter(
            LoginHistory.user_id == id_,
        ).order_by(LoginHistory.id.desc())

        reason_map = {i.name: i.value for i in LoginHistory.FailReason}

        if kwargs['export']:
            record_list = []
            for item in list(sign_in_logs.limit(ADMIN_EXPORT_LIMIT).all()):
                record = item.to_dict()
                record['location'] = GeoIP(record['ip'], lang=Language.EN_US.value).location
                record['user_agent_human'] = item.user_agent_for_admin
                record['fail_reason_str'] = reason_map.get(record['fail_reason'])
                record['created_at'] = datetime_to_utc8_str(record['created_at'])
                record['status'] = '成功' if record['successful'] else '失败'
                record_list.append(record)
            return export_xlsx(
                filename='login_history',
                data_list=record_list,
                export_headers=cls.export_headers,
            )

        pagination = sign_in_logs.paginate(page, limit, error_out=False)
        record_list = []
        for item in pagination.items:
            record = item.to_dict()
            record['location'] = GeoIP(record['ip'], lang=Language.EN_US.value).location
            record['user_agent_human'] = item.user_agent_for_admin
            record['fail_reason_str'] = reason_map.get(record['fail_reason'])
            record_list.append(record)

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.LoginHistory,
            detail=kwargs,
            target_user_id=id_,
        )


        return dict(
            record_list=record_list,
            total=pagination.total,
            page=pagination.page,
        )


# noinspection PyUnresolvedReferences
@ns.route('/<int:id_>/sub_accounts')
@respond_with_code
class UserSubAccountsResource(Resource):

    @classmethod
    def get(cls, id_):
        """用户详情-子账号"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound
        return [dict(
            id=account.id,
            created_at=account.created_at,
            user_id=account.user_id,
            user_name=account.user.name,
            status=account.status
        ) for account in user.sub_accounts]


@ns.route('/<int:id_>/assets')
@respond_with_code
class UserAssetsResource(Resource):

    @classmethod
    def get(cls, id_):
        """用户详情-杠杆资产"""
        QUOTE_ASSETS = ('USDT', 'USDC', 'BTC')

        user: User = User.query.get(id_)
        if user is None:
            raise NotFound
        user_id = user.id
        all_assets = list_all_assets()
        markets = []
        for asset in all_assets:
            for q in QUOTE_ASSETS:
                if asset == q:
                    continue
                markets.append(f'{asset}{q}')
        record_list, record_total_assets = user_margin_asset(user_id)
        asset_list, no_asset_list = [], []
        for item in record_list:
            if item.get('net_usd', 0) != 0:
                asset_list.append(item)
            else:
                no_asset_list.append(item)
            if not item['rate']:
                item['rate'] = "-"
            if not item['burst_price']:
                item['burst_price'] = "-"
        asset_records = []
        asset_record_map = {item['market_type']: item for item in asset_list}
        for m in markets:
            if m not in asset_record_map:
                continue
            asset_records.append(asset_record_map[m])

        no_asset_records = []
        no_asset_record_map = {item['market_type']: item for item in no_asset_list}
        for m in markets:
            if m not in no_asset_record_map:
                continue
            no_asset_records.append(no_asset_record_map[m])

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.MarginAssets,
            target_user_id=id_,
        )

        return dict(
            user=user,
            record_list=asset_records + no_asset_records,
            record_total_assets=[record_total_assets],
        )

    @classmethod
    @ns.use_kwargs(dict(
        can_loan=fields.Integer(),
        remark=fields.String(missing=''),
    ))
    def patch(cls, id_, **kwargs):
        """用户详情-借币权限配置"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound
        if 'can_loan' in kwargs:
            can_loan = kwargs['can_loan']
            UserSettings(id_).margin_loan_enabled = bool(can_loan)
            if not user.is_sub_account:
                sub_accounts = SubAccount.query.filter(
                    SubAccount.main_user_id == id_,
                ).all()
                for sub_account in sub_accounts:
                    UserSettings(sub_account.user_id
                                 ).margin_loan_enabled = bool(can_loan)
            # todo AdminOperLog表还没有迁移，暂时不做更新
        return


@ns.route('/<int:id_>/deals')
@respond_with_code
class UserDealsResource(Resource):
    export_marshal_fields = {
        'deal_id': fx_fields.Integer,
        'order_id': fx_fields.Integer,
        'time': fx_fields.String(attribute=lambda x: \
            timestamp_to_datetime(x['time']).strftime('%Y-%m-%d %H:%M:%S')),
        'market': fx_fields.String(attribute=lambda x: x['market'] + ("(现货)" if x['account'] == 0 else "(杠杆)")),
        'side': fx_fields.String(attribute=lambda x: '买入' if x['side'] == OrderSideType.BUY else '卖出'),
        'price': Order.PriceFields(attribute=lambda x: (x['price'], x['market'])),
        'amount': fx_fields.String(attribute=lambda x: amount_to_str(x['amount'], 8)),
        'fee': fx_fields.String(attribute=lambda x: amount_to_str(x['fee'], 8) + f' {x["fee_asset"]}'),
        'role': fx_fields.String(attribute=lambda x: 'Maker' if x['role'] == TradeIntType.MAKER else 'Taker'),
        'total': fx_fields.String(attribute=lambda x: amount_to_str(x['price'] * x['amount'], 8)),
    }

    export_headers = (
        {"field": "deal_id", Language.ZH_HANS_CN: "交易ID"},
        {"field": "order_id", Language.ZH_HANS_CN: "订单ID"},
        {"field": "time", Language.ZH_HANS_CN: "成交时间"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "side", Language.ZH_HANS_CN: "方向"},
        {"field": "price", Language.ZH_HANS_CN: "成交价"},
        {"field": "amount", Language.ZH_HANS_CN: "成交量"},
        {"field": "total", Language.ZH_HANS_CN: "成交额"},
        {"field": "fee", Language.ZH_HANS_CN: "手续费"},
        {"field": "role", Language.ZH_HANS_CN: "成交类型"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        market_type=fields.String(missing=''),
        side=fields.Integer(missing=0),
        page=fields.Integer(missing=1),
        limit=ex_fields.LimitField,
        start_time=fields.Integer(missing=0),
        end_time=fields.Integer(missing=0),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, id_, **kwargs):
        """用户详情-币币订单"""
        market = kwargs['market']
        market_type = kwargs['market_type']
        side = kwargs['side']
        page = kwargs['page']
        limit = kwargs['limit']
        page = 1 if page <= 0 else page
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']

        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        if kwargs['export']:
            conds = []
            if start_time:
                conds.append(f'time >= {start_time}')
            if end_time:
                conds.append(f'time < {end_time}')
            if market:
                if market == 'spot':
                    conds.append('account = 0')
                else:
                    conds.append('account > 0')
            if market_type:
                conds.append(f'market = "{market_type}"')
            if side:
                conds.append(f'side = {side}')
            cond = ' AND '.join(conds)
            fields = ('deal_id', 'order_id', 'market', 'time', 'side',
                      'price', 'amount', 'fee', 'role', 'account', 'fee_asset')
            history = TradeHistoryDB.get_users_history([id_], fields, cond, table_type='user_deal_history')
            return export_xlsx(
                filename="spot-deals",
                data_list=marshal(history, cls.export_marshal_fields),
                export_headers=cls.export_headers,
            )

        records, has_next, market_type_list, page = get_user_deal_order(
            user.id, start_time, end_time, market, market_type, side, page, limit)

        return dict(
            records=records,
            has_next=has_next,
            curr_page=page,
            market_type_list=market_type_list,
        )


@ns.route('/<int:id_>/contract-deals')
@respond_with_code
class ContractDealsResource(Resource):
    export_marshal_fields = {
        'deal_id': fx_fields.Integer,
        'order_id': fx_fields.Integer,
        'time': fx_fields.String(attribute=lambda x: \
            timestamp_to_datetime(x['time']).strftime('%Y-%m-%d %H:%M:%S')),
        'market': fx_fields.String,
        'side': fx_fields.String(attribute=lambda x: '买入' if x['side'] == OrderSideType.BUY else '卖出'),
        'price': Order.PriceFields(attribute=lambda x: (x['price'], x['market'])),
        'amount': fx_fields.String(attribute=lambda x: amount_to_str(x['amount'], 8)),
        'fee': fx_fields.String(attribute=lambda x: amount_to_str(x['deal_fee'], 8) + f' {x["fee_asset"]}'),
        'role': fx_fields.String(attribute=lambda x: 'Maker' if x['role'] == TradeIntType.MAKER else 'Taker'),
        'deal_stock': fx_fields.String(attribute=lambda x: amount_to_str(x['deal_stock'], 8)),
    }

    export_headers = (
        {"field": "deal_id", Language.ZH_HANS_CN: "交易ID"},
        {"field": "order_id", Language.ZH_HANS_CN: "订单ID"},
        {"field": "time", Language.ZH_HANS_CN: "成交时间"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "side", Language.ZH_HANS_CN: "方向"},
        {"field": "price", Language.ZH_HANS_CN: "成交价"},
        {"field": "amount", Language.ZH_HANS_CN: "成交量"},
        {"field": "fee", Language.ZH_HANS_CN: "手续费"},
        {"field": "role", Language.ZH_HANS_CN: "成交类型"},
        {"field": "deal_stock", Language.ZH_HANS_CN: "成交额"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        side=fields.Integer(missing=0),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=100),
        start_time=fields.Integer(missing=0),
        end_time=fields.Integer(missing=0),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续成交记录"""
        market = kwargs['market']
        side = kwargs['side']
        limit = kwargs['limit']
        page = kwargs['page']
        page = 1 if page <= 0 else page
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']

        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        if kwargs['export']:
            conds = []
            if start_time:
                conds.append(f'time >= {start_time}')
            if end_time:
                conds.append(f'time < {end_time}')
            if market:
                conds.append(f'market = "{market}"')
            if side:
                conds.append(f'side = {side}')
            cond = ' AND '.join(conds)
            fields = ('deal_id', 'order_id', 'market', 'time', 'side', 'price',
                      'amount', 'deal_fee', 'fee_asset', 'role', 'deal_stock')
            history = PerpetualHistoryDB.get_users_deals([id_], fields, cond)
            return export_xlsx(
                filename="perpetual-deals",
                data_list=marshal(history, cls.export_marshal_fields),
                export_headers=cls.export_headers,
            )

        market_server_client = PerpetualServerClient()

        perpetual_dict_map = PerpetualMarketCache().read_aside()
        offline_perpetual_dict_map = PerpetualOfflineMarketCache().read_aside()
        results = market_server_client.user_deals(
            user_id=id_,
            market=market,
            side=side,
            start_time=start_time,
            end_time=end_time,
            page=page,
            limit=limit,
        )
        records = []
        if results:
            for item in results:
                # market might not exists
                market_exists = item['market'] in perpetual_dict_map and \
                                perpetual_dict_map[item['market']]
                info = perpetual_dict_map.get(item['market']) or offline_perpetual_dict_map[item['market']]
                data = {
                    "deal_user": item['deal_user_id'],
                    "position_id": item["position_id"],
                    "position_amount": item["position_amount"],
                    "fee": amount_to_str(item['deal_fee'], 8),
                    "fee_asset": ((item['fee_asset'] or
                                   perpetual_dict_map[item['market']]['stock'])
                                  if perpetual_dict_map[item['market']]['type'] ==
                                     PerpetualMarketType.INVERSE.value
                                  else (item['fee_asset']
                                        or perpetual_dict_map[item['market']]['money']))
                    if market_exists else '',
                    "open_price": item["open_price"],
                    "deal_profit": item["deal_profit"],
                    "order_id": item['order_id'],
                    "price": amount_to_str(item['price'], info['money_prec']),
                    "side": item['side'],
                    "amount": item['amount'],
                    "deal_type": item['deal_type'],
                    "role": 'Maker' if item['role'] == 1 else 'Taker',
                    "time": item['time'],
                    "deal_fee": item['deal_fee'],
                    "id": item['id'],
                    "market": item['market'],
                    "deal_stock": item['deal_stock'],
                    'total': quantize_amount(item['deal_stock'], 8),
                    'amount_asset': PerpetualMarketCache.get_amount_asset(item['market']) if market_exists else '',
                    'balance_asset': PerpetualMarketCache.get_balance_asset(item['market']) if market_exists else ''
                }
                records.append(data)

        return dict(
            records=records,
            has_next=results.has_next,
            curr_page=results.page,
        )


@ns.route('/<int:id_>/contract-funding')
@respond_with_code
class ContractFundingResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=20),
        start_time=fields.Integer(missing=0),

        end_time=fields.Integer(missing=0),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续资金费用"""
        market = kwargs['market']
        limit = kwargs['limit']
        page = kwargs['page']
        page = 1 if page <= 0 else page
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']

        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        market_server_client = PerpetualServerClient()

        results = market_server_client.position_funding(
            user_id=id_,
            market=market,
            start_time=start_time,
            end_time=end_time,
            page=page,
            limit=limit,
        )
        market_info_map = PerpetualMarketCache().read_aside()
        markets = list(set(r['market'] for r in results))
        market_asset_map = dict()
        for m in markets:
            amount_asset = PerpetualMarketCache.get_amount_asset(m)
            balance_asset = PerpetualMarketCache.get_balance_asset(m)
            market_asset_map[m] = dict(
                amount_asset=amount_asset,
                balance_asset=balance_asset
            )
        for r in results:
            r['amount_asset'] = market_asset_map[r['market']]['amount_asset']
            r['balance_asset'] = market_asset_map[r['market']]['balance_asset']
        for result in results:
            if market_info_map[result['market']]['type'] == \
                    PerpetualMarketType.INVERSE.value:
                actual_rate = Decimal(result['funding']) / (
                        Decimal(result['amount']) / Decimal(result['price']))
            else:
                actual_rate = Decimal(result['funding']) / (
                        Decimal(result['amount']) * Decimal(result['price']))
            if result['side'] != 1:
                actual_rate = -actual_rate
            result['actual_rate'] = amount_to_str(actual_rate, 8)
            if market_info_map[result['market']]['type'] == \
                    PerpetualMarketType.INVERSE.value:
                result['total_price'] = amount_to_str(
                    Decimal(result['amount']) / Decimal(result['price']), 8)
            else:
                result['total_price'] = amount_to_str(
                    Decimal(result['amount']) * Decimal(result['price']), 8)
            result['profit_real'] = amount_to_str(result['funding'], 8)
            result['amount_asset'] = market_asset_map[result['market']]['amount_asset']
            result['balance_asset'] = market_asset_map[result['market']][
                'balance_asset']
        return dict(
            records=results,
            has_next=results.has_next,
            curr_page=results.page,
        )


@ns.route('/<int:id_>/statistic')
@respond_with_code
class StatisticResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        coin_type=fields.String(missing=''),
        market_type=fields.String(missing=''),
        start_time=DateField(to_date=True),
        end_time=DateField(to_date=True),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-币币成交统计"""
        coin_type = kwargs.get('coin_type', '')
        market_type = kwargs.get('market_type', '')
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']

        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        market_types = []
        market_sell_buy_asset_type = {}
        all_markets = get_all_markets()
        show_market_types = []

        all_assets = set()
        for item in all_markets:
            market_sell_buy_asset_type[item.name] = {
                'sell_asset_type': item.base_asset,
                'buy_asset_type': item.quote_asset
            }
            all_assets.add(item.base_asset)

        if coin_type and not market_type:
            for item in market_sell_buy_asset_type:
                if coin_type == \
                        market_sell_buy_asset_type[item]['sell_asset_type']:
                    market_types.append(item)
            show_market_types = market_types

        elif market_type:
            market_types.append(market_type)
            if coin_type:
                for item in market_sell_buy_asset_type:
                    if coin_type == \
                            market_sell_buy_asset_type[item][
                                'sell_asset_type']:
                        show_market_types.append(item)

        elif not coin_type and not market_type:
            market_types.append(market_sell_buy_asset_type.keys())

        records = get_user_summary_statistic(
            id_, start_time, end_time, coin_type, market_type)

        total_deal_volume_to_usd = 0
        total_fee_to_usd = 0

        record_dict = defaultdict(dict)
        market_fee_map = dict()
        for record in records:

            market, deal_count, deal_amount, deal_volume, buy_count, \
                buy_amount, sell_count, sell_amount = record

            deal_amount = Decimal(
                Order.BasePrecisionFields().format((deal_amount, market))
            )
            deal_volume = Decimal(
                Order.QuotePrecisionFields().format((deal_volume, market))
            )
            sell_amount = Decimal(
                Order.BasePrecisionFields().format((sell_amount, market))
            )
            buy_amount = Decimal(
                Order.BasePrecisionFields().format((buy_amount, market))
            )
            if market not in market_sell_buy_asset_type:
                continue
            market_record = record_dict[market]
            market_record['market'] = market
            market_record['coin_type'] = \
                market_sell_buy_asset_type[market]['sell_asset_type']
            market_record['deal_count'] = \
                market_record.get('deal_count', Decimal('0')) + deal_count
            market_record['deal_volume'] = \
                market_record.get('deal_volume', Decimal('0')) + deal_volume
            market_record['deal_amount'] = \
                market_record.get('deal_amount', Decimal('0')) + deal_amount
            market_record['buy_count'] = \
                market_record.get('buy_count', Decimal('0')) + buy_count
            market_record['buy_amount'] = \
                market_record.get('buy_amount', Decimal('0')) + buy_amount
            market_record['sell_count'] = \
                market_record.get('sell_count', Decimal('0')) + sell_count
            market_record['sell_amount'] = \
                market_record.get('sell_amount', Decimal('0')) + sell_amount
            market_record['price_coin'] = \
                market_sell_buy_asset_type[market]['buy_asset_type']
            if market not in market_fee_map:
                fee = get_user_fee_statistic(id_, market, start_time, end_time)
                market_fee_map[market] = fee

            fee = market_fee_map[market]
            market_record['fee'] = amount_to_str(fee, 10)
            deal_volume_usd = Decimal(deal_volume) * PriceManager.asset_to_usd(market_record['price_coin'])

            if 'deal_volume_usd' not in market_record:
                market_record['deal_volume_usd'] = 0
            market_record['deal_volume_usd'] += deal_volume_usd
            total_deal_volume_to_usd += deal_volume_usd

        record_list = [record for _, record in record_dict.items()]
        for item in record_list:
            item['deal_volume_usd'] = amount_to_str(item['deal_volume_usd'], PrecisionEnum.CASH_PLACES)
        total_fee_to_usd = sum(market_fee_map.values())
        return dict(
            record_list=record_list,
            market_types=show_market_types,
            coin_types=all_assets,
            total_deal_volume_to_usd=amount_to_str(total_deal_volume_to_usd, 10),
            total_fee_to_usd=amount_to_str(total_fee_to_usd, 10),
        )


@ns.route('/<int:id_>/perpetual-statistic')
@respond_with_code
class PerpetualStatisticResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(missing=''),
        start_time=DateField(to_date=True),
        end_time=DateField(to_date=True),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-永续成交统计"""
        asset = kwargs['asset']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']

        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        records = get_user_perpetual_summary_statistic(
            id_, start_time, end_time, asset)

        record_dict = defaultdict(dict)
        online_market_list = set(PerpetualMarketCache().get_market_list())
        for record in records:
            _cache = PerpetualMarketCache if record['market'] in online_market_list else PerpetualOfflineMarketCache
            market_info = _cache(
            ).get_market_info(record['market'])
            market_record = record_dict[record['market']]
            market_record['market'] = record['market']
            market_record['amount_asset'] = _cache.get_amount_asset(record['market'])
            market_record['balance_asset'] = _cache.get_balance_asset(record['market'])

            market_record['deal_count'] = \
                market_record.get('deal_count', Decimal('0')) + record["deal_count"]
            market_record['deal_amount'] = \
                market_record.get('deal_amount', Decimal('0')) + record["deal_amount"]
            market_record['buy_count'] = \
                market_record.get('buy_count', Decimal('0')) + record["buy_count"]
            market_record['buy_amount'] = \
                market_record.get('buy_amount', Decimal('0')) + record["buy_amount"]
            market_record['buy_volume'] = \
                market_record.get('buy_volume', Decimal('0')) + record["buy_volume"]
            market_record['sell_count'] = \
                market_record.get('sell_count', Decimal('0')) + record["sell_count"]
            market_record['sell_amount'] = \
                market_record.get('sell_amount', Decimal('0')) + record["sell_amount"]
            market_record['sell_volume'] = \
                market_record.get('sell_volume', Decimal('0')) + record["sell_volume"]
            market_record['deal_amount_usd'] = \
                market_record.get('deal_amount_usd', Decimal('0')) + record["deal_amount_usd"]
            market_record['market_type'] = market_info['type']
            total_deal_volume = Decimal(
                market_record.get('deal_volume', 0)) + record["deal_volume"]
            market_record['deal_volume'] = amount_to_str(total_deal_volume, 10)
            fee = get_user_perpetual_fee_statistic(
                id_, record['market'], start_time, end_time)
            market_record['fee'] = fee

        record_list = [record for _, record in record_dict.items()]

        total_deal_volume_to_usd = sum([i['deal_amount_usd'] for i in record_list])
        total_fee_to_usd = sum([i['fee'] for i in record_list])

        return dict(
            record_list=record_list,
            total_deal_volume_to_usd=amount_to_str(total_deal_volume_to_usd, 10),
            total_fee_to_usd=amount_to_str(total_fee_to_usd, 10),
            market_types=PerpetualMarketType
        )


@ns.route('/<int:id_>/balance-history')
@respond_with_code
class BalanceHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        market_type=fields.String(),
        pledge_account_id=fields.String(),
        asset=fields.String(missing=''),
        business=fields.DelimitedList(fields.String()),
        sub_type=fields.String(missing=''),
        start_time=fields.Integer(missing=0),
        end_time=fields.Integer(missing=0),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=100),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-资金流水"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        market = kwargs['market'] \
            if kwargs['market'] else TradeBusinessType.SPOT.value
        market_type = kwargs.get('market_type')
        asset = kwargs['asset']
        businesses = kwargs.get('business', [])
        start_time = kwargs['start_time'] \
            if kwargs['start_time'] <= kwargs['end_time'] else 0
        end_time = kwargs['end_time']
        page = kwargs['page']
        limit = kwargs['limit']
        account_id = 0
        all_markets = get_all_markets()
        coin_list = list({item.base_asset for item in all_markets})

        margin_markets = list(MarginAccountNameCache.list_all_markets().values())
        pledge_loan_asset_account_id_dict = get_pledge_loan_asset_account_id_dict()

        if market == 'margin':
            if market_type and market_type != '':
                account_id = MarginHelper.get_account_id_by_market_type(market_type)
            else:
                market_type = list(MarginAccountNameCache.list_online_markets().values())[0]
            coin_type_set = set()
            coin_type_set.update(
                [MarketCache(market_type).dict['base_asset']])
            coin_type_set.update(
                [MarketCache(market_type).dict['quote_asset']])
            coin_list = list(coin_type_set)

        if market == 'investment':
            if kwargs['sub_type'] == 'investment':
                account_id = InvestmentAccount.ACCOUNT_ID
                items = InvestmentAccount.query.filter().with_entities(InvestmentAccount.asset).all()
                coin_list = [item.asset for item in items]
            else:
                account_id = StakingAccount.ACCOUNT_ID
                items = StakingAccount.query.filter().all()
                coin_list = [item.asset for item in items]
        balance_server_client = ServerClient(current_app.logger)
        perpetual_client = PerpetualServerClient(current_app.logger)
        spot_types = BalanceBusiness.build_operation(AccountBalanceType.SPOT)
        margin_types = BalanceBusiness.build_operation(AccountBalanceType.MARGIN)
        investment_types = BalanceBusiness.build_operation(AccountBalanceType.INVESTMENT)
        pledge_types = BalanceBusiness.build_operation(AccountBalanceType.PLEDGE)
        staking_types = BalanceBusiness.build_operation(AccountBalanceType.STAKING)

        def validate_business(_market: str, _business: str):
            if _market == 'perpetual':
                return True
            match _market:
                case 'spot':
                    return _business in [v.value for v in spot_types]
                case 'margin':
                    return _business in [v.value for v in margin_types]
                case 'investment':
                    return _business in [v.value for v in list(investment_types) + list(staking_types)]
                case 'pledge':
                    return _business in [v.value for v in pledge_types]
            return False

        if not all([validate_business(market, _v) for _v in businesses]):
            business = []
        else:
            business = businesses

        if market == 'perpetual':
            # 合约暂时不支持传多个
            if len(businesses) > 0:
                business = businesses[0]
                businesses = [businesses[0]]
            else:
                business = ''
                businesses = []

        if len(businesses) > 10:
            raise InvalidArgument(message=f"暂不支持超过10个类型")

        if market == 'pledge':
            pledge_account_id = kwargs.get('pledge_account_id')
            if pledge_account_id and str(pledge_account_id).isdigit():
                account_id = int(pledge_account_id)
            else:
                account_id = list(pledge_loan_asset_account_id_dict.values())[0]
            coin_list = get_pledge_account_id_assets_dict(all_assets=True)[account_id]

        if market == 'perpetual':
            result = perpetual_client.query_history_all(
                user_id=id_, asset=asset, business=business,
                start_time=start_time, end_time=end_time, limit=limit,
                page=page)
        else:
            result = balance_server_client.get_user_balance_history(
                user_id=id_, asset=asset, business=business,
                start_time=start_time, end_time=end_time, limit=limit,
                page=page, account_id=int(account_id))

        market_str_map = {
            'spot': '现货账户',
            'margin': '杠杆账户',
            'investment': '理财账户',
            'perpetual': '合约账户',
            'pledge': '借贷账户',
        }

        business_str_map = {
            'spot': BalanceBusiness.build_trans_map(spot_types),
            'margin': BalanceBusiness.build_trans_map(margin_types),
            'investment': BalanceBusiness.build_trans_map(investment_types),
            'pledge': BalanceBusiness.build_trans_map(pledge_types),
            'staking': BalanceBusiness.build_trans_map(staking_types),
            'perpetual': {
                'clearing_funding': '资金费用结算',
                'clearing_close': '平仓结算',
                'fee': '手续费',
                'funding': '资金费用',
                'trade': '交易',
                'contract_transfer_in': '转入',
                'contract_transfer_out': '转出',
                'coupon': '合约体验金',
                'coupon_recycle': '回收合约体验金',
                'ct_experience_fee': '合约跟单体验金',
                'ct_experience_fee_recycle': '回收合约跟单体验金',
                'settle': '结算盈亏',
                "broker_referral": "经纪商返佣",
            }
        }

        record_list = []
        for record in result:
            asset_display = record['asset']
            if 'f' in record['detail']:
                business_display = BalanceBusiness.TRADING_FEE.value
            else:
                business_display = record['business']

            detail = record['detail']
            if float(record['change']) > 0:
                change_display = ' +{change}'.format(change=record['change'])
            else:
                change_display = ' {change}'.format(change=record['change'])
            record_list.append(
                {
                    'time': record['time'],
                    'business': business_display,
                    'asset': asset_display,
                    'change': change_display,
                    'balance': amount_to_str(record['balance'], 8),
                    'detail': detail,
                }
            )

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BalanceHistory,
            detail=kwargs,
            target_user_id=id_,
        )

        return dict(
            record_list=record_list,
            market=market,
            market_str=market_str_map[market],
            market_type=market_type,
            market_str_map=market_str_map,
            business_str_map=business_str_map,
            margin_markets=margin_markets,
            coin_list=coin_list,
            asset=asset,
            business=businesses,
            pledge_loan_asset_account_ids=pledge_loan_asset_account_id_dict,
            has_next=result.has_next,
            curr_page=page,
            extra=dict(
                tax_account_map={
                    TaxExportHistory.Account.ALL.name: '全部',
                    TaxExportHistory.Account.SPOT.name: '现货账户',
                    TaxExportHistory.Account.MARGIN.name: '杠杆账户',
                    TaxExportHistory.Account.FUTURE.name: '合约账户',
                    TaxExportHistory.Account.INVESTMENT.name: '理财账户',
                }
            ),
        )


@ns.route('/<int:id_>/tax-history-export')
@respond_with_code
class TaxHistoryExportResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        start_time=fields.Integer(missing=0),
        end_time=fields.Integer(missing=0),
        asset=fields.String(missing='ALL'),
        hide_transfer=fields.Boolean(missing=False),
        account=EnumField(TaxExportHistory.Account),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-报税数据导出"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound
        with CacheLock(LockKeys.tax_export(id_), wait=False):
            db.session.rollback()
            record_id = cls._add_record(id_, **kwargs)
            export_tax_data.delay(record_id, False)
        return {}

    @classmethod
    def _add_record(cls, user_id, **kwargs):
        cls._check(**kwargs)
        rec = TaxExportHistory(
            user_id=user_id,
            status=TaxExportHistory.Status.PENDING,
            start_time=kwargs['start_time'],
            end_time=kwargs['end_time'],
            account=kwargs['account'],
            asset=kwargs['asset'],
            hide_transfer=kwargs['hide_transfer'],
        )
        db.session.add(rec)
        db.session.commit()
        return rec.id

    @classmethod
    def _check(cls, **kwargs):
        start_time, end_time = kwargs['start_time'], kwargs['end_time']
        if start_time > end_time:
            raise InvalidArgument
        asset = kwargs['asset']
        if asset != 'ALL':
            if not has_asset(asset):
                raise AssetNotFound(asset)


@ns.route('/<int:id_>/tax/export/history')
@respond_with_code
class TaxExportHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField(missing=20)
    ))
    def get(cls, id_, **kwargs):
        """用户详情-报税数据导出历史"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound
        page, limit = kwargs.get('page'), kwargs.get('limit')
        ret = cls._get_export_history(id_, page, limit)
        ret['user_register_at'] = user.created_at
        return ret

    @classmethod
    def _get_export_history(cls, user_id, page, limit):
        query = TaxExportHistory.query.filter(
            TaxExportHistory.user_id == user_id
        ).order_by(TaxExportHistory.id.desc())
        total = query.count()
        data = []
        pagination = query.paginate(page, limit, error_out=False)
        now_ = now()
        for record in pagination.items:
            status = record.status
            if status == TaxExportHistory.Status.FINISHED:
                finished_at = record.finished_at
                if now_ > finished_at + timedelta(days=TaxExportHistory.EXPIRE_DAYS):
                    status = TaxExportHistory.Status.EXPIRED
            item = {
                'id': record.id,
                'created_at': record.created_at.timestamp(),
                'start_time': record.start_time,
                'end_time': record.end_time,
                'status': status.name,
                'file_url': record.file_url,
            }
            data.append(item)
        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            data=data,
            total=total,
        )


@ns.route('/<int:id_>/invest-assets')
@respond_with_code
class InvestHistoryResource(Resource):

    @classmethod
    def get(cls, id_):
        """用户详情-理财资产"""
        user: User = User.query.get(id_)
        if user is None:
            raise NotFound

        account_info = InvestmentUserAccountsHelper(id_).get_accounts_info()

        record_list = []
        total_balance = Decimal(0)

        db_r = InvestmentBalanceHistory.query.filter(
            InvestmentBalanceHistory.user_id == id_,
            InvestmentBalanceHistory.status ==
            InvestmentBalanceHistory.StatusType.SUCCESS,
        ).with_entities(
            InvestmentBalanceHistory.asset,
            func.sum(InvestmentBalanceHistory.amount).label("all_balance"),
        ).group_by(
            InvestmentBalanceHistory.asset
        )
        invest_balances = ServerClient().get_user_balances(
            user_id=id_,
            account_id=InvestmentAccount.ACCOUNT_ID
        )
        user_asset_result = defaultdict(Decimal)
        for asset, balances in invest_balances.items():
            if available := balances.get("available", Decimal()):
                user_asset_result[asset] = \
                    quantize_amount(available, PrecisionEnum.COIN_PLACES)

        for i in db_r:
            if user_asset_result[i.asset] == 0:
                continue

            data = {
                "asset": account_info[i.asset]["coin_type"],
                "balance": user_asset_result[i.asset],
                "yday_income": account_info[i.asset].get("yday_income",
                                                         "0"),
                "all_income": account_info[i.asset].get("all_income", "0"),
                "day_rate": Decimal(
                    account_info[i.asset]["day_rate"] * 100),
                "seventh_pa": Decimal(
                    account_info[i.asset]["7th_pa"] * 100),
            }
            record_list.append(data)
            total_balance += PriceManager.asset_to_usd(i.asset) * user_asset_result[i.asset]
            total_balance = quantize_amount(total_balance, PrecisionEnum.CASH_PLACES)

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.InvestAssets,
            target_user_id=id_,
        )

        return dict(
            record_list=record_list,
            total_volume={"total_volume": total_balance},
        )


@ns.route('/<int:id_>/staking-assets')
@respond_with_code
class StakingAssetResource(Resource):

    @classmethod
    def get(cls, id_):
        """用户详情-链上质押资产"""
        user: User = User.query.get(id_)
        client = ServerClient()
        balances = client.get_user_balances(user.id, account_id=StakingAccount.ACCOUNT_ID)
        balance_map = dict()
        for k, v in balances.items():
            balance_map[k] = v.get('available', 0)
        summaries = StakingUserSummary.query.filter(
            StakingUserSummary.user_id == id_,
        ).all()
        summary_map = {item.asset: item for item in summaries}
        price_map = PriceManager.assets_to_usd()

        last_report = DailyStakingReport.query.order_by(DailyStakingReport.report_date.desc()).limit(1).first()
        reports = DailyStakingReport.query.filter(
            DailyStakingReport.report_date == last_report.report_date
        ).with_entities(
            DailyStakingReport.asset,
            DailyStakingReport.income_rate,
        ).all()
        income_rate_map = dict(reports)
        total_usd = 0
        for k, v in balance_map.items():
            total_usd += v * price_map.get(k, 0)
        accounts = StakingAccount.query.filter(StakingAccount.status == StakingAccount.Status.OPEN).all()
        result = []
        for account in accounts:
            summary = summary_map.get(account.asset)
            daily_income = 0
            if summary and summary.last_reward_at and summary.last_reward_at.date() == today():
                daily_income = summary.daily_reward
            result.append(dict(
                asset=account.asset,
                amount=balance_map.get(account.asset, 0),
                unstaking_amount=summary.unstaking_amount if summary else 0,
                total_income=summary.total_reward if summary else 0,
                daily_income=daily_income,
                income_rate=income_rate_map.get(account.asset, 0),
            ))
        return dict(
            record_list=result,
            total_volume={"total_volume": quantize_amount(total_usd, PrecisionEnum.CASH_PLACES)},
        )


@ns.route('/<int:id_>/stop-orders')
@respond_with_code
class UserStopOrderResource(Resource):
    marshal_fields = {
        'account_id': fx_fields.Integer(attribute='account'),
        'order_id': fx_fields.Integer(attribute='id'),
        'source': fx_fields.String(attribute='source'),
        'create_time': fx_fields.Integer(attribute='ctime'),
        'amount': fx_fields.String,
        'price': Order.PriceFields(
            attribute=lambda x: (x['price'], x['market'])),
        'stop_price': Order.PriceFields(
            attribute=lambda x: (x['stop_price'], x['market'])),
        'taker_fee': fx_fields.String,
        'maker_fee': fx_fields.String,
        'state': fx_fields.Integer,
        'fee_asset': fx_fields.String,
        'fee_discount': fx_fields.String,
        'market': fx_fields.String,
        'order_type': fx_fields.String(
            attribute=lambda x: Order.NormalOrderType(
                int(x['type'])).name.lower()),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(
            int(x['side'])).name.lower()),
        'market_type': fx_fields.String(
            attribute=lambda x: 'spot' if x['account'] == 0 else 'margin'),
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(),
        side=fields.Integer(missing=0),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        account_id=fields.Integer(missing=-1),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-币币-计划委托"""
        market = kwargs.get('market')
        side = kwargs['side']
        account_id = kwargs['account_id']
        page, limit = kwargs['page'], kwargs['limit']
        client = ServerClient()
        result = client.user_pending_stop_orders(
            user_id=id_,
            market=market,
            side=side,
            page=page,
            limit=limit,
            account_id=account_id,
        )

        result = result.as_dict()

        result['data'] = marshal(result['data'], cls.marshal_fields)
        result['market_list'] = MarketCache.list_online_markets()

        return result


@ns.route('/<int:id_>/pending-orders')
@respond_with_code
class UserPendingOrderResource(Resource):
    base_marshal_fields = {
        'order_id': fx_fields.Integer(attribute='id'),
        'source': fx_fields.String(attribute='source'),
        'account_id': fx_fields.Integer(attribute='account'),
        'create_time': fx_fields.Integer(attribute='ctime'),
        'market': fx_fields.String,
        'order_type': ex_fields.EnumMarshalField(
            OrderIntType,
            attribute='type'
        ),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(
            int(x['side'])).name.lower()),
        'price': Order.PriceFields(attribute=lambda x: (x['price'], x['market'])),
        'amount': ex_fields.AmountField,
        'market_type': fx_fields.String(
            attribute=lambda x: 'spot' if x['account'] == 0 else 'margin'),
        'option': fx_fields.Integer,
    }

    normal_fields = deepcopy(base_marshal_fields)
    normal_fields.update(dict(
        deal_volume=Order.QuotePrecisionFields(
            attribute=lambda x: (Decimal(x['deal_money']), x['market'])),
        deal_amount=ex_fields.AmountField(
            attribute=lambda x: Decimal(x['amount']) - Decimal(
                x['left'])),
        average_price=Order.AvgPriceFields(
            attribute=lambda x: (
                Decimal(x['deal_stock']), Decimal(x['deal_money']), x['market']))
    ))

    stop_fields = deepcopy(base_marshal_fields)
    stop_fields.update(dict(
        stop_price=ex_fields.AmountField,
        stop_type=ex_fields.EnumMarshalField(StopOrderType)
    ))

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(),
        side=fields.Integer(missing=0),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        account_id=fields.Integer(missing=-1),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-币币-当前委托"""
        market = kwargs.get('market')
        side = kwargs['side']
        account_id = kwargs['account_id']
        page, limit = kwargs['page'], kwargs['limit']
        client = ServerClient()
        result = client.user_pending_orders(
            user_id=id_,
            market=market,
            side=side,
            page=page,
            limit=limit,
            account_id=account_id,
        )

        result = result.as_dict()

        result['data'] = marshal(result['data'], cls.normal_fields)
        result['market_list'] = MarketCache.list_online_markets()

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpotOrders,
            detail=kwargs,
            target_user_id=id_,
        )

        return result


@ns.route('/<int:id_>/pending-orders/<int:order_id>')
@respond_with_code
class UserPendingOrderDeleteResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(required=True),
    ))
    def delete(cls, id_, order_id, **kwargs):
        """用户详情-币币-撤销普通委托"""
        user_id = id_
        market = kwargs['market']
        market_cache = MarketCache(market).dict
        if market_cache["status"] not in (
                Market.Status.ONLINE, Market.Status.BIDDING):
            raise ForbidTrading
        if market_cache["status"] == Market.Status.BIDDING and \
                market_cache["bidding_matching_started_at"] < now():
            raise OrderExceptionMap[OrderException.BIDDING_STATUS]
        log_client = ServerClient(current_app.logger)
        log_client.cancel_user_order(user_id=user_id, market=market,
                                     order_id=order_id)


@ns.route('/<int:id_>/stop-orders/<int:order_id>')
@respond_with_code
class UserStopOrderDeleteResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(required=True),
    ))
    def delete(cls, id_, order_id, **kwargs):
        """用户详情-币币-撤销计划委托"""
        user_id = id_
        market = kwargs['market']
        market_cache = MarketCache(market).dict
        if market_cache["status"] not in (
                Market.Status.ONLINE, Market.Status.BIDDING):
            raise ForbidTrading
        log_client = ServerClient(current_app.logger)
        log_client.cancel_user_stop_order(user_id=user_id,
                                          market=market,
                                          order_id=order_id)


@ns.route('/<int:id_>/stop-orders/batch')
@respond_with_code
class UserStopOrderBatchDeleteResource(Resource):

    @classmethod
    def delete(cls, id_):
        """用户详情-币币-批量撤销计划委托单"""
        user_id = id_
        log_client = ServerClient(current_app.logger)
        markets = MarketCache.list_online_markets()
        for market in markets:
            log_client.cancel_user_all_stop_order(
                user_id=user_id, account_id=0, market=market)


@ns.route('/<int:id_>/pending-orders/batch')
@respond_with_code
class UserPendingOrderBatchDeleteResource(Resource):

    @classmethod
    def delete(cls, id_):
        """用户详情-币币-批量撤销普通委托单"""
        user_id = id_
        log_client = ServerClient(current_app.logger)
        markets = MarketCache.list_online_markets()
        for market in markets:
            log_client.cancel_user_all_order(
                user_id=user_id, account_id=0, market=market)


class OrderHistoryMixin:
    @classmethod
    def format_spot_option_str(cls, row):
        OPTION_IMMEDIATED_OR_CANCEL = 0x8
        OPTION_FILL_OR_KILL = 0x10
        OPTION_HIDDEN = 0x20
        OPTION_MAKER_ONLY = 0x80

        option_list = []
        if row['option'] & OPTION_IMMEDIATED_OR_CANCEL:
            option_list.append('IOC')
        elif row['option'] & OPTION_FILL_OR_KILL:
            option_list.append('FOK')
        else:
            option_list.append('AL')

        is_marker_only = row['option'] & OPTION_MAKER_ONLY
        is_hide = row['option'] & OPTION_HIDDEN
        option_list.append('M' if is_marker_only else '--')
        option_list.append('开启' if is_hide else '关闭')
        return ','.join(option_list)

    @classmethod
    def format_perpetual_option_str(cls, row):
        effect_type_map = {
            1: 'AL',
            2: 'IOC',
            3: 'FOK'
        }
        OPTION_MAKER_ONLY = 0x1
        OPTION_HIDDEN = 0x2
        option_list = []
        option_list.append(effect_type_map.get(row['effect_type'], '--'))
        is_marker_only = row['option'] & OPTION_MAKER_ONLY
        is_hide = row['option'] & OPTION_HIDDEN
        option_list.append('M' if is_marker_only else '--')
        option_list.append('开启' if is_hide else '关闭')
        return ','.join(option_list)


@ns.route('/<int:id_>/pending-history-orders')
@respond_with_code
class UserPendingHistoryOrderResource(Resource):
    marshal_fields = {
        'account_id': fx_fields.Integer(attribute='account'),
        'order_id': fx_fields.Integer(attribute='id'),
        'client_id': fx_fields.String,
        'create_time': fx_fields.Integer(attribute='ctime'),
        'amount': fx_fields.String,
        'price': Order.PriceFields(
            attribute=lambda x: (x['price'], x['market'])),
        'deal_amount': fx_fields.String(attribute='deal_stock'),
        'stock_fee': fx_fields.String,
        'money_fee': fx_fields.String,
        'deal_money': Order.PriceFields(
            attribute=lambda x: (x['deal_money'], x['market'])),
        'asset_fee': fx_fields.String,
        'fee_asset': fx_fields.String,
        'source': fx_fields.String,
        'fee_discount': fx_fields.String,
        'option_str': fx_fields.String(attribute=lambda x: OrderHistoryMixin.format_spot_option_str(x)),
        'avg_price': Order.AvgPriceFields(
            attribute=lambda x: (
                Decimal(x['deal_stock']), Decimal(x['deal_money']),
                x['market'])),
        'market': fx_fields.String,
        'order_type': fx_fields.String(
            attribute=lambda x: Order.NormalOrderType(
                int(x['type'])).name.lower()),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(
            int(x['side'])).name.lower()),
        'status': Order.StatusFields(attribute=lambda x: (x, x['type'], False)),
        'option': fx_fields.Integer,
        'market_type': fx_fields.String(
            attribute=lambda x: 'spot' if x['account'] == 0 else 'margin'),
        'source_type': fx_fields.String(attribute=lambda x: '系统' if x['source'] == 'system' else '用户')
    }

    export_marshal_fields = {
        'id': fx_fields.Integer(attribute='order_id'),
        'client_id': fx_fields.String(attribute='client_id'),
        'create_time': fx_fields.String(attribute=lambda x: \
            timestamp_to_datetime(x['create_time']).strftime('%Y-%m-%d %H:%M:%S')),
        'market': fx_fields.String,
        'order_type': fx_fields.String(
            attribute=lambda x: '市价单' + (
                ('(杠杆)' if x['account'] < InvestmentAccount.ACCOUNT_ID else '(质押)') if x['account'] != 0 else '') \
                if x['t'] == OrderIntType.MARKET else '限价单' + (
                ('(杠杆)' if x['account'] < InvestmentAccount.ACCOUNT_ID else '(质押)') if x['account'] != 0 else '')),
        'type': fx_fields.String(attribute=lambda x: '买入' if x['side'] == OrderSideType.BUY else '卖出'),
        'price': Order.PriceFields(attribute=lambda x: (x['price'], x['market'])),
        'amount': fx_fields.String(attribute=lambda x: amount_to_str(x['amount'], 8)),
        'deal_amount': fx_fields.String(attribute=lambda x: amount_to_str(x['deal_stock'], 8)),
        'deal_money': Order.PriceFields(
            attribute=lambda x: (x['deal_money'], x['market'])),
        'avg_price': fx_fields.String(attribute=lambda x: f'{amount_to_str(x["deal_money"] / x["deal_stock"], 8)}'),
        'status': Order.StatusFields(attribute=lambda x: (x, x['t'], False)),
        'option_str': fx_fields.String(attribute=lambda x: OrderHistoryMixin.format_spot_option_str(x)),
        'source': fx_fields.String,
    }

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "订单ID"},
        {"field": "client_id", Language.ZH_HANS_CN: "client_id"},
        {"field": "create_time", Language.ZH_HANS_CN: "委托时间"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "order_type", Language.ZH_HANS_CN: "委托类型"},
        {"field": "type", Language.ZH_HANS_CN: "方向"},
        {"field": "price", Language.ZH_HANS_CN: "委托价"},
        {"field": "amount", Language.ZH_HANS_CN: "委托数量"},
        {"field": "deal_amount", Language.ZH_HANS_CN: "成交数量"},
        {"field": "avg_price", Language.ZH_HANS_CN: "成交均价"},
        {"field": "deal_money", Language.ZH_HANS_CN: "成交额"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "source", Language.ZH_HANS_CN: "委托人"},
        {"field": "option_str", Language.ZH_HANS_CN: "高级设置"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        page=fields.Integer(missing=1),
        side=fields.Integer(missing=0),
        limit=fields.Integer(missing=50),
        start_time=TimestampField(),
        end_time=TimestampField(),
        account_id=fields.Integer(missing=-1),
        order_id=fields.Integer(missing=0),
        stop_order_id=fields.Integer(),
        margin_liquidation_history_id=fields.Integer(missing=0),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-币币-历史委托"""
        market = kwargs['market']
        account_id = kwargs['account_id']
        side = kwargs['side']
        order_id = kwargs['order_id']
        margin_liquidation_history_id = kwargs['margin_liquidation_history_id']
        page, limit = kwargs['page'], kwargs['limit']
        start_time = kwargs.get('start_time')
        if not start_time:
            start_time = datetime(2018, 1, 1)
        end_time = kwargs.get('end_time', datetime.now())
        stop_order_id = kwargs.get('stop_order_id', None)
        start_time = TimestampMarshalField().format(start_time)
        end_time = TimestampMarshalField().format(end_time)

        if kwargs['export']:
            cond = f' create_time>={start_time} AND create_time<{end_time}'
            if account_id == 0:
                cond += f' AND account={account_id}'
            elif account_id == -2:
                cond += ' AND account > 0'
            if market:
                cond += f' AND market="{market}"'
            if side:
                cond += f' AND side={side}'
            history = TradeHistoryDB.get_users_history([id_],
                                                       fields=['id', 'order_id', 'create_time',
                                                               'user_id', 'market', 't', 'side',
                                                               'price', 'amount', 'deal_stock',
                                                               'deal_money', 'money_fee', 'stock_fee', 'source',
                                                               'account', 'option'],
                                                       cond=cond,
                                                       table_type='order_history')
            return export_xlsx(
                filename="spot-history-orders",
                data_list=marshal(history, cls.export_marshal_fields),
                export_headers=cls.export_headers,
            )
        client = ServerClient()
        if not order_id and not margin_liquidation_history_id:
            result = client.user_finished_orders(
                user_id=id_,
                market=market,
                start_time=start_time,
                end_time=end_time,
                side=side,
                page=page,
                limit=limit,
                account_id=account_id,
                stop_order_id=stop_order_id
            )

            result = result.as_dict()
        elif order_id:
            result = dict()
            order = client.finished_order_detail(id_, order_id)
            if not order:
                result['data'] = []
            else:
                result['data'] = [order]
        elif margin_liquidation_history_id:
            order_ids = MarginLiquidationOrder.query.filter(
                MarginLiquidationOrder.margin_liquidation_history_id == margin_liquidation_history_id
            ).with_entities(
                MarginLiquidationOrder.order_id
            ).all()
            order_ids = [item.order_id for item in order_ids]
            result = dict()
            result['data'] = []
            for order_id in order_ids:
                order = client.finished_order_detail(id_, order_id)
                if order:
                    result['data'].append(order)
        else:
            pass
        result['data'] = marshal(result['data'], cls.marshal_fields)
        all_markets = get_all_markets()
        result['market_list'] = [item.name for item in all_markets]

        for data in result['data']:
            data['avg_price'] = amount_to_str(data['avg_price'], PrecisionEnum.PRICE_PLACES)

        return result


@ns.route('/<int:id_>/order-detail/<int:order_id>')
@respond_with_code
class UserOrderDetailResource(Resource):
    enums = {
        'part_deal': '部分成交',
        'not_deal': '未成交',
        'done': '全部成交',
        'cancel': '已撤销'
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        market_type=fields.String(missing=''),
        limit=fields.Integer(missing=10),
        page=fields.Integer(missing=1),
    ))
    def get(cls, id_, order_id, **kwargs):
        """用户详情-币币-订单明细"""
        market = kwargs['market']
        market_type = kwargs['market_type']
        page = kwargs['page']
        limit = kwargs['limit']

        is_pending = True
        client = ServerClient()
        detail_result = {}
        if market_type in MarketCache.list_online_markets():
            detail_result = client.pending_order_detail(
                market=market_type,
                order_id=order_id,
            )
        if not detail_result:
            detail_result = client.finished_order_detail(
                user_id=id_,
                order_id=order_id,
            )
            is_pending = False

        if not detail_result:
            raise OrderExceptionMap[OrderException.ORDER_NOT_FOUND]

        order_data = deepcopy(detail_result)
        order_data['amount_asset'] = \
            Order.AmountAssetFields().format((order_data['market'],
                                              order_data['side'],
                                              order_data['type'],
                                              order_data['option']))
        order_data['type'] = 'sell' if detail_result['side'] == 1 else 'buy'
        order_data['order_type'] = OrderType.LIMIT_ORDER_TYPE.value \
            if detail_result['type'] == Order.TradeRoleType.MAKER.value \
            else OrderType.MARKET_ORDER_TYPE.value

        order_data['avg_price'] = Order.get_avg_price(
            (Decimal(detail_result['deal_stock']),
             Decimal(detail_result['deal_money']),
             detail_result['market']))
        order_data['is_discount'] = Decimal(order_data["fee_discount"]) < 1 and order_data['fee_asset']
        status_dic = marshal(detail_result,
                             {'status': Order.StatusFields(attribute=lambda x: (x, x['type'], is_pending))},
                             skip_none=True)
        order_data.update(status_dic)
        if order_data['is_discount']:
            order_data['taker_fee'] = max(
                Decimal(),
                Decimal(order_data['taker_fee']
                        ) * Decimal(order_data['fee_discount'])
            )
            order_data['maker_fee'] = max(
                Decimal(),
                Decimal(order_data['maker_fee']
                        ) * Decimal(order_data['fee_discount'])
            )
        if order_data.get('fee_asset'):
            order_data['asset_fee'] = amount_to_str(order_data['asset_fee'], 8)

        order_data['stock_fee'] = amount_to_str(order_data['stock_fee'], 8)
        order_data['money_fee'] = amount_to_str(order_data['money_fee'], 8)
        account_id = order_data["account"]
        if market == TradeBusinessType.SPOT.value:
            item = MarketCache(order_data['market']).dict
            order_data['sell_asset_type'] = item['base_asset']
            order_data['buy_asset_type'] = item['quote_asset']
        elif market == OrderBusinessType.MARGIN_BUSINESS_TYPE.value:
            item = MarketCache(order_data['market']).dict
            order_data['sell_asset_type'] = item['base_asset']
            order_data['buy_asset_type'] = item['quote_asset']
        user: User = User.query.get(order_data['user'])
        if user is None:
            raise NotFound
        deals_result = client.user_order_deals(user_id=id_,
                                               order_id=order_id,
                                               page=page,
                                               limit=limit,
                                               account_id=account_id)
        return dict(
            deals_result=deals_result,
            order_data=order_data,
            pre_page=deals_result.page > 1,
            next_page=deals_result.has_next,
            order_status_type=cls.enums
        )


@ns.route('/<int:id_>/stop-history-orders')
@respond_with_code
class UserStopHistoryOrderResource(Resource):
    marshal_fields = {
        'account_id': fx_fields.Integer(attribute='account'),
        'order_id': fx_fields.Integer(attribute='id'),
        'create_time': fx_fields.Integer(attribute='ctime'),
        'trigger_time': fx_fields.Integer(attribute='ftime'),
        'amount': fx_fields.String,
        'price': Order.PriceFields(
            attribute=lambda x: (x['price'], x['market'])),
        'stop_price': Order.PriceFields(
            attribute=lambda x: (x['stop_price'], x['market'])),
        'fee_asset': fx_fields.String,
        'fee_discount': fx_fields.String,
        'source': fx_fields.String,
        'market': fx_fields.String,
        'option': fx_fields.Integer,
        'option_str': fx_fields.String(attribute=lambda x: OrderHistoryMixin.format_spot_option_str(x)),
        'order_type': fx_fields.String(
            attribute=lambda x: Order.NormalOrderType(
                int(x['type'])).name.lower()),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(
            int(x['side'])).name.lower()),
        'status': Order.StopStatusFields(attribute=lambda x: x['status']),
        'market_type': fx_fields.String(
            attribute=lambda x: 'spot' if x['account'] == 0 else 'margin'),
        'state': fx_fields.Integer(attribute='direction'),
    }

    export_marshal_fields = {
        'order_id': fx_fields.Integer,
        'option_str': fx_fields.String(attribute=lambda x: OrderHistoryMixin.format_spot_option_str(x)),
        'create_time': fx_fields.String(attribute=lambda x: \
            timestamp_to_datetime(x['create_time']).strftime('%Y-%m-%d %H:%M:%S')),
        'finish_time': fx_fields.String(attribute=lambda x: \
            timestamp_to_datetime(x['finish_time']).strftime('%Y-%m-%d %H:%M:%S')),
        'market': fx_fields.String,
        'order_type': fx_fields.String(attribute=lambda x: \
            '市价单' if x['t'] == OrderIntType.MARKET else '限价单'),
        'type': fx_fields.String(attribute=lambda x: '买入' if x['side'] == OrderSideType.BUY else '卖出'),
        'price': Order.PriceFields(attribute=lambda x: (x['price'], x['market'])),
        'amount': fx_fields.String(lambda x: amount_to_str(x['amount'], 8)),
        'stop_price': fx_fields.String(
            attribute=lambda x: ('≤' if x['direction'] == 1 else '≥') + amount_to_str(x['stop_price'], 8)),
        'status': fx_fields.String(attribute=lambda x: {
            StopOrderStatusIntType.ACTIVE: '已委托',
            StopOrderStatusIntType.CANCEL: '已撤销',
            StopOrderStatusIntType.FAIL: '余额不足，委托失败',
        }[x['status']]),
    }

    export_headers = (
        {"field": "order_id", Language.ZH_HANS_CN: "订单ID"},
        {"field": "create_time", Language.ZH_HANS_CN: "委托时间"},
        {"field": "finish_time", Language.ZH_HANS_CN: "触发时间"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "order_type", Language.ZH_HANS_CN: "委托类型"},
        {"field": "amount", Language.ZH_HANS_CN: "委托数量"},
        {"field": "type", Language.ZH_HANS_CN: "方向"},
        {"field": "price", Language.ZH_HANS_CN: "委托价"},
        {"field": "stop_price", Language.ZH_HANS_CN: "触发价"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "option_str", Language.ZH_HANS_CN: "高级设置"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        start_time=TimestampField(),
        end_time=TimestampField(),
        account_id=fields.Integer(missing=-1),
        side=fields.Integer(missing=0),
        order_id=fields.Integer(missing=0),
        status=ex_fields.EnumField(enum=StopOrderStatusIntType, missing=0),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, id_, **kwargs):
        """用户详情-币币-历史计划委托"""
        market = kwargs['market']
        account_id = kwargs['account_id']
        side = kwargs['side']
        order_id = kwargs['order_id']
        status = kwargs['status']
        page, limit = kwargs['page'], kwargs['limit']
        start_time = kwargs.get('start_time')
        if not start_time:
            start_time = datetime(2018, 1, 1)
        end_time = kwargs.get('end_time')
        if not end_time or end_time < start_time:
            end_time = datetime.now()
        start_time = TimestampMarshalField().format(start_time)
        end_time = TimestampMarshalField().format(end_time)

        if kwargs['export']:
            cond = f' create_time>={start_time} AND create_time<{end_time}'
            if market:
                cond += f' AND market="{market}"'
            if order_id != 0:
                cond += f' AND order_id={order_id}'
            if side:
                cond += f' AND side={side}'
            if account_id == 0:
                cond += f' AND account={account_id}'
            elif account_id == -2:
                cond += ' AND account > 0'
            history = TradeHistoryDB.get_users_history([id_],
                                                       fields=['id', 'order_id', 'create_time', 'finish_time',
                                                               'stop_price',
                                                               'user_id', 'market', 't', 'side',
                                                               'price', 'amount', 'status', 'direction'],
                                                       cond=cond,
                                                       table_type='stop_history')
            return export_xlsx(
                filename="spot-history-stop-orders",
                data_list=marshal(history, cls.export_marshal_fields),
                export_headers=cls.export_headers,
            )

        client = ServerClient()
        if not order_id:
            result = client.user_finished_stop_orders(
                user_id=id_,
                market=market,
                start_time=start_time,
                end_time=end_time,
                side=side,
                page=page,
                limit=limit,
                account_id=account_id,
                status=status
            )

            result = result.as_dict()
        else:
            result = dict()
            order = client.finished_stop_order_detail(id_, order_id)
            if not order:
                result['data'] = []
            else:
                result['data'] = [order]

        result['data'] = marshal(result['data'], cls.marshal_fields)
        all_markets = get_all_markets()
        result['market_list'] = [item.name for item in all_markets]

        return result


@ns.route('/user-level/')
@respond_with_code
class UserLevelResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    )
    )
    def get(cls, **kwargs):
        """用户详情-用户等级"""
        user_id = kwargs["id"]
        user = User.query.filter(
            User.id == user_id
        ).first()
        if not user:
            raise InvalidArgument(message=f'{user_id}不存在')
        t = FeeFetcher(user_id)
        parser = UserFeeParser(user_id)
        spot_fee = parser.get_settle_fee(TradeBusinessType.SPOT)
        p = UserPreferences(user_id=user_id).cet_discount_enabled
        perpetual_fee = parser.get_settle_fee(TradeBusinessType.PERPETUAL)
        vip = VipUser.query.filter(VipUser.user_id == user_id,
                                   VipUser.status ==
                                   VipUser.StatusType.PASS).first()
        sub_user = SubAccount.query.filter(
            SubAccount.user_id == user_id,
            SubAccount.status == SubAccount.Status.VALID,
        ).first()
        q = UserMarginDayRate.query.filter(
            UserMarginDayRate.user_id == user_id,
            UserMarginDayRate.status == UserMarginDayRate.StatusType.PASS,
        ).first()
        if not q:
            # 某个账号设置了特殊杠杆日息, 子账号也享受相应的特殊杠杆日息
            if sub_user:
                q = UserMarginDayRate.query.filter(
                    UserMarginDayRate.user_id == sub_user.main_user_id,
                    UserMarginDayRate.status == UserMarginDayRate.StatusType.PASS,
                ).first()

        dt_format = partial(datetime_to_str, offset_minutes=60 * 8)  # to utc+8 str
        spot_fee_data = [
            {
                "label": "Maker费率",
                "value": spot_fee[TradeType.MAKER],
            },
            {
                "label": "Taker费率",
                "value": spot_fee[TradeType.TAKER],
            },
            {
                "label": "普通用户AMM市场费率",
                "value": "跟随市场配置；非稳定币市场Maker和Taker默认都为0.003，稳定币市场Maker和Taker默认都为0.001"
            },
            {
                "label": "做市商AMM市场费率",
                "value": "Maker都为0；Taker跟随市场配置，和普通用户费率一致"
            },
            {"label": "开启CET抵扣", "value": "已开启" if p else "未开启"},
            {
                "label": "VIP杠杆日息",
                "value": "",  # Admin跳转，不展示
            },
            {
                "label": "特殊配置杠杆日息",
                "editable": 1,
                "value": q.fee if q else None,
            },
            {
                "label": "特殊配置杠杆日息到期时间",
                "value": dt_format(q.expired_time) if q and q.expired_time else None,
            },
        ]

        all_config_data = t.fetch_all_special_market_fee()
        fee_config_data_dict = {
            TradeBusinessType.SPOT: [],
            TradeBusinessType.PERPETUAL: []
        }

        for key, value in all_config_data.items():
            business_type, trade_type, market = key
            fee_config_data_dict[business_type].append(
                {
                    "market_name": market,
                    "trade_type": trade_type,
                    "fee": value["fee"],
                    "expired_time": timestamp_to_datetime(value["expired_time"])
                }
            )

        perpetual_fee_data = [
            {
                "label": "Maker费率",
                "value": perpetual_fee[TradeType.MAKER],
            },
            {
                "label": "Taker费率",
                "value": perpetual_fee[TradeType.TAKER],
            },
            {
                "label": "开启CET抵扣",
                "value": "已开启" if p else "未开启",
            },
        ]
        binding_cet_data = VipHelper.get_binding_cet_data(user_id)
        lv = vip.level if vip else 0
        vip_desc_dict = cls._get_vip_desc_dict(user_id, lv)
        user_type_level_data = [
            {
                "label": "账号类型",
                "value": user.user_type.name,
                "editable": True if not user.is_sub_account else False,
            },
            {
                "label": "VIP等级",
                "value": lv,
                "extra": {**vip_desc_dict}
            },
            {
                "label": "VIP保底等级",
                "value": vip.lock_level if vip else 0,
                "editable": True,
            },
            {
                "label": "VIP考核等级",
                "value": vip.real_level if vip else 0,
            },
            {
                "label": "VIP保底等级过期时间",
                "value": dt_format(vip.expired_time) if vip and vip.expired_time else None,
            },
            {
                "label": "站外地址CET余额",
                "value": binding_cet_data,
            },
        ]

        amm_markets = {
            v.name
            for v in
            AmmMarket.query.filter(AmmMarket.status == AmmMarket.Status.ONLINE).with_entities(
                AmmMarket.name
            ).all()
        }

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Level,
            target_user_id=user.id,
        )

        return dict(
            perpetual_fee_data=perpetual_fee_data,
            spot_fee_data=spot_fee_data,
            amm_markets=amm_markets,
            spot_market_fee_config_data=fee_config_data_dict[TradeBusinessType.SPOT],
            perpetual_market_fee_config_data
            =fee_config_data_dict[TradeBusinessType.PERPETUAL],
            user_type_level_data=user_type_level_data,
            vip_levels=sorted(VIP_LEVEL_DICT.keys()),
            binding_cet_data=binding_cet_data
        )

    @classmethod
    def _get_vip_desc_dict(cls, user_id: int, lv: int) -> dict:
        snapshot = VipHelper.get_vip_snapshot_data(user_id, fetch_current=False)
        result = VipHelper.get_vip_appraisal_result_desc(user_id)
        ret = {
            'result': result,
            'cet': f'CET持仓量 {snapshot["cet_amount"]}',
            'spot': f'近30天现货交易量 {snapshot["spot_trade_usd"]}',
            'perp': f'近30天合约交易量 {snapshot["perpetual_trade_usd"]}',
            'pos': f'资产总值(USD) {snapshot["position_usd"]}',
            'cet_red': 0,
            'spot_red': 0,
            'perp_red': 0,
            'pos_red': 0,
        }
        rule = VIP_LEVEL_DICT.get(lv)
        if not rule:
            return ret
        spec_rule = rule['rule']
        if Decimal(snapshot["cet_amount"]) >= spec_rule['cet_amount']:
            ret.update(cet_red=1)
        if Decimal(snapshot["spot_trade_usd"]) >= spec_rule['spot_usd']:
            ret.update(spot_red=1)
        if Decimal(snapshot["perpetual_trade_usd"]) >= spec_rule['perpetual_usd']:
            ret.update(perp_red=1)
        if Decimal(snapshot["position_usd"]) >= spec_rule['position_usd']:
            ret.update(pos_red=1)
        return ret


@ns.route('/user-type')
@respond_with_code
class UserTypeResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            user_type=ex_fields.EnumField(
                User.UserType,
                required=True
            )
        )
    )
    def post(cls, **kwargs):
        """用户详情-修改账号类型"""
        from app.api.admin.market_maker import InnerMarketMakerResource

        admin_user_id = g.user.id
        user_id = kwargs["user_id"]
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(message=f'{user_id}不存在')
        if user.is_sub_account:
            raise InvalidArgument(message='子账号不允许设置账户类型')
        prev_user_type = user.user_type
        user_type = kwargs["user_type"]

        spot_market_maker_helper = MarketMakerHelper(
            MarketMaker.MakerType.SPOT)
        perpetual_market_maker_helper = MarketMakerHelper(
            MarketMaker.MakerType.PERPETUAL)
        spot_level = spot_market_maker_helper.get_level(user_id)
        perpetual_level = perpetual_market_maker_helper.get_level(user_id)
        if user_type in [
            User.UserType.NORMAL,
            User.UserType.EXTERNAL
        ]:
            spot_market_maker_helper.delete(user_id, update_fee=False)
            perpetual_market_maker_helper.delete(user_id, update_fee=False)
            InnerMarketMakerResource.do_delete(admin_user_id, user_id)

        elif user_type == User.UserType.EXTERNAL_SPOT_MAKER:
            InnerMarketMakerResource.do_delete(admin_user_id, user_id)
            spot_market_maker_helper.add(
                user_id,
                1,
                False,
                update_fee=False,
            )
            perpetual_market_maker_helper.delete(user_id, update_fee=False)
        elif user_type == User.UserType.EXTERNAL_CONTRACT_MAKER:
            InnerMarketMakerResource.do_delete(admin_user_id, user_id)
            perpetual_market_maker_helper.add(
                user_id,
                1,
                False,
                update_fee=False,
            )
            spot_market_maker_helper.delete(user_id, update_fee=False)
        elif user_type == User.UserType.EXTERNAL_MAKER:
            InnerMarketMakerResource.do_delete(admin_user_id, user_id)
            perpetual_market_maker_helper.add(
                user_id,
                1,
                False,
                update_fee=False,
            )
            spot_market_maker_helper.add(
                user_id,
                1,
                False,
                update_fee=False,
            )
        elif user_type == User.UserType.INTERNAL_MAKER:
            spot_market_maker_helper.delete(user_id, update_fee=False)
            perpetual_market_maker_helper.delete(user_id, update_fee=False)
            InnerMarketMakerResource.do_add(admin_user_id, user_id)
        else:
            raise InvalidArgument(message='用户类型非法')
        update_user_fee_task(user_id)

        # 成为做市商
        if prev_user_type == User.UserType.EXTERNAL_MAKER:
            prev_user_type = (User.UserType.EXTERNAL_SPOT_MAKER, User.UserType.EXTERNAL_CONTRACT_MAKER)
        else:
            prev_user_type = (prev_user_type,)
        if user_type == User.UserType.EXTERNAL_MAKER:
            user_type = (User.UserType.EXTERNAL_SPOT_MAKER, User.UserType.EXTERNAL_CONTRACT_MAKER)
        else:
            user_type = (user_type,)
        operation_map = defaultdict(list)
        for _type in prev_user_type:
            if _type not in user_type:
                operation_map['remove'].append(_type)
        for _type in user_type:
            if _type not in prev_user_type:
                operation_map['add'].append(_type)
        for _type in operation_map['add']:
            db.session.add(UserStatusChangeHistory(
                user_id=user_id,
                type=_type.name,
                action=MarketMakerChangeType.ADD.name,
                admin_user_id=admin_user_id,
                detail=json.dumps(dict(
                    maker_type=_type.name,
                    old_level=None,
                    new_level=1
                ))
            ))
        for _type in operation_map['remove']:
            db.session.add(UserStatusChangeHistory(
                user_id=user_id,
                type=_type.name,
                action=MarketMakerChangeType.REMOVE.name,
                admin_user_id=admin_user_id,
                detail=json.dumps(dict(
                    maker_type=_type.name,
                    old_level=spot_level if _type == User.UserType.EXTERNAL_SPOT_MAKER
                    else perpetual_level if _type == User.UserType.EXTERNAL_CONTRACT_MAKER else None,
                    new_level=None
                ))
            ))
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.Type,
            old_data=dict(user_type=prev_user_type),
            new_data=dict(user_type=user_type),
            target_user_id=user_id,
        )


@ns.route('/margin-day-rate')
@respond_with_code
class UserMarginDayRateResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            day_rate=ex_fields.PositiveDecimalField(
                required=True
            ),
            expired_time=ex_fields.TimestampField(
                is_ms=True
            )
        )
    )
    def post(cls, **kwargs):
        """用户详情-杠杆日息"""
        admin_user_id = g.user.id
        body = Struct(**kwargs)
        user_id = kwargs["user_id"]
        user = User.query.filter(
            User.id == user_id
        ).first()
        if not user:
            raise InvalidArgument(f'{user_id}不存在')
        q = UserMarginDayRate.query.filter(
            UserMarginDayRate.user_id == user_id
        ).first()
        if q:
            old_data = q.to_dict(enum_to_name=True)
            q.status = UserMarginDayRate.StatusType.PASS
            q.expired_time = body.expired_time
            q.fee = body.day_rate
            AdminOperationLog.new_edit(
                user_id=admin_user_id,
                ns_obj=OPNamespaceObjectUser.MarginDayRate,
                old_data=old_data,
                new_data=q.to_dict(enum_to_name=True),
                target_user_id=user_id,
            )
        else:
            record = UserMarginDayRate(
                status=UserMarginDayRate.StatusType.PASS,
                user_id=user_id,
                fee=body.day_rate,
                expired_time=body.expired_time
            )
            db.session.add(record)
            AdminOperationLog.new_add(
                user_id=admin_user_id,
                ns_obj=OPNamespaceObjectUser.MarginDayRate,
                detail={'day_rate': body.day_rate, 'expired_time': body.expired_time},
                target_user_id=user_id,
            )
        db.session.commit()


@ns.route('/referral')
@respond_with_code
class ReferralResource(Resource):

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "邀请时间"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "referrer_id", Language.ZH_HANS_CN: "邀请人ID"},
        {"field": "channel", Language.ZH_HANS_CN: "分享来源"},
        {"field": "referrer_email", Language.ZH_HANS_CN: "邀请人邮箱"},
        {"field": "referree_id", Language.ZH_HANS_CN: "被邀请人ID"},
        {"field": "referree_email", Language.ZH_HANS_CN: "被邀请人邮箱"},
        {"field": "referral_code", Language.ZH_HANS_CN: "邀请码"},
        {"field": "has_spot_trade", Language.ZH_HANS_CN: "是否有过币币交易"},
        {"field": "has_perpetual_trade", Language.ZH_HANS_CN: "是否有过合约交易"},
        {"field": "has_deposit", Language.ZH_HANS_CN: "是否有过充值"},
        {"field": "referree_location", Language.ZH_HANS_CN: "地区"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        export=fields.Integer,
        referral_code=fields.String,
        page=ex_fields.PageField(unlimited=True),
        status=EnumField(ReferralHistory.Status),
        limit=ex_fields.LimitField,
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
    ))
    def get(cls, **kwargs):
        """
        邀请列表
        """
        query = ReferralHistory.query.order_by(ReferralHistory.id.desc())
        if user_id := kwargs.get('user_id'):
            query = query.filter(ReferralHistory.referrer_id == user_id)
        if status := kwargs.get('status'):
            query = query.filter(ReferralHistory.status == status)
        if start_date := kwargs.get('start_date'):
            query = query.filter(ReferralHistory.created_at >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(ReferralHistory.created_at <= end_date)

        if referral_code := kwargs.get('referral_code'):
            referral_info: Referral = Referral.query.filter(
                Referral.code == referral_code
            ).first()
            if not referral_info:
                raise InvalidArgument(message='邀请码不存在')
            query = query.filter(
                ReferralHistory.referrer_id == referral_info.user_id)

        businesses = [
            UserBusinessRecord.Business.SPOT_TRADE,
            UserBusinessRecord.Business.PERPETUAL_TRADE,
            UserBusinessRecord.Business.DEPOSIT
        ]
        total = 0
        if kwargs.get('export'):
            records = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            paginate = query.paginate(kwargs['page'], kwargs['limit'])
            records = paginate.items
            total = paginate.total

        referee_ids = [item.referree_id for item in records]
        referee_id_bit_map = BitMap()
        referee_id_bit_map.update(referee_ids)

        business_record_map = dict()
        for business in businesses:
            record = UserBusinessRecord.query.filter(
                UserBusinessRecord.business == business
            ).order_by(UserBusinessRecord.report_at.desc()).first()
            business_record_map[business] = record
        exchange_site_report = DailyAssetExchangeSiteReport.query.order_by(
            DailyAssetExchangeSiteReport.report_date.desc()
        ).with_entities(DailyAssetExchangeSiteReport.history_user_bitmap).first()
        spot_user_bm: BitMap = BitMap.deserialize(exchange_site_report.history_user_bitmap)
        if spot_record := business_record_map[UserBusinessRecord.Business.SPOT_TRADE]:
            spot_user_bm = spot_user_bm.union(BitMap.deserialize(spot_record.history_user_bit_map))
        spot_user_ids = set(spot_user_bm.intersection(referee_id_bit_map))
        perpetual_user_ids, deposit_user_ids = set(), set()
        if perpetual_record := business_record_map[UserBusinessRecord.Business.PERPETUAL_TRADE]:
            perpetual_user_ids = set(
                BitMap.deserialize(perpetual_record.history_user_bit_map).intersection(referee_id_bit_map))
        if deposit_record := business_record_map[UserBusinessRecord.Business.DEPOSIT]:
            deposit_user_ids = set(
                BitMap.deserialize(deposit_record.history_user_bit_map).intersection(referee_id_bit_map))

        user_info_map, referral_map = cls.get_user_referral_map(records)
        channel_map = ShareUserTag.get_channel_name_map()
        ret = []
        for i in records:
            tmp = i.to_dict()
            info = user_info_map[i.referree_id]
            location_code = info["location_code"]
            country = get_country(location_code)
            tmp.update(
                referrer_email=user_info_map[i.referrer_id]["email"],
                referree_email=info["email"],
                referree_location=country.cn_name if country else "",
                created_at=i.created_at,
                referral_code=referral_map[i.referral_id]["code"],
                has_spot_trade='是' if i.referree_id in spot_user_ids else '否',
                has_perpetual_trade='是' if i.referree_id in perpetual_user_ids else '否',
                has_deposit='是' if i.referree_id in deposit_user_ids else '否',
                status=i.status.value,
                channel=channel_map.get(info["channel"], '/'),
            )
            ret.append(tmp)

        if kwargs.get('export'):
            for r in ret:
                r["created_at"] = datetime_to_str(r["created_at"])
            return export_xlsx(
                filename='referral_list',
                data_list=ret,
                export_headers=cls.export_headers
            )

        ret = dict(
            data=ret,
            total=total,
            extra=dict(
                status_dict={
                    ReferralHistory.Status.VALID.name: '生效中',
                    ReferralHistory.Status.EXPIRED.name: '已过期',
                }
            )
        )

        return ret

    @staticmethod
    def get_user_referral_map(records):
        user_id_set, referral_id_set = set(), set()
        for record in records:
            user_id_set.add(record.referrer_id)
            user_id_set.add(record.referree_id)
            referral_id_set.add(record.referral_id)

        user_info_map = {}
        model = User
        for user_ids in batch_iter(list(user_id_set), 1000):
            cursor = model.query.filter(
                model.id.in_(user_ids)
            ).with_entities(
                model.id,
                model.email,
                model.location_code,
                model.channel
            ).all()
            for i in cursor:
                user_info_map[i.id] = {
                    "email": i.email,
                    "location_code": i.location_code,
                    "channel": i.channel
                }

        referral_map = {}
        for user_ids in batch_iter(list(referral_id_set), 1000):
            cursor = Referral.query.filter(Referral.id.in_(user_ids)).with_entities(Referral.id,
                                                                                    Referral.code).all()
            for i in cursor:
                referral_map[i.id] = {
                    "code": i.code,
                }
        return user_info_map, referral_map


@ns.route('/referral-codes')
@respond_with_code
class ReferralCodesResource(Resource):
    TYPES = [i.name for i in Ambassador.LEVEL_NAMES] + ['NORMAL', 'ALL_AMBASSADOR']

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        referral_code=fields.String,
        type=EnumField(TYPES),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
    ))
    def get(cls, **kwargs):
        """
        邀请码列表
        """
        query = Referral.query.order_by(Referral.id.desc())
        if type_ := kwargs.get('type'):
            q = Ambassador.query.filter(
                Ambassador.status == Ambassador.Status.VALID
            )
            if type_ in [i.name for i in Ambassador.Level]:
                ambs = q.all()
                user_ids = [i.user_id for i in ambs if i.level == Ambassador.Level[type_]]
                query = query.filter(Referral.user_id.in_(user_ids))
            elif type_ == 'ALL_AMBASSADOR':
                user_ids = q.with_entities(Ambassador.user_id).all()
                user_ids = [i.user_id for i in user_ids]
                query = query.filter(Referral.user_id.in_(user_ids))
            else:
                user_ids = q.with_entities(Ambassador.user_id).all()
                user_ids = [i.user_id for i in user_ids]
                query = query.filter(Referral.user_id.notin_(user_ids))
        if user_id := kwargs.get('user_id'):
            query = query.filter(Referral.user_id == user_id)
        if referral_code := kwargs.get('referral_code'):
            query = query.filter(Referral.code == referral_code)
        pagination = query.paginate(kwargs['page'], kwargs['limit'])
        user_ids = [i.user_id for i in pagination.items]
        user_emails = User.query.filter(User.id.in_(user_ids)).with_entities(User.id,
                                                                             User.email).all()
        user_email_map = dict(user_emails)
        ambassadors = Ambassador.query.filter(
            Ambassador.user_id.in_(user_ids),
            Ambassador.status == Ambassador.Status.VALID
        ).all()
        ambassador_map = dict()
        for i in ambassadors:
            ambassador_map[i.user_id] = _(Ambassador.LEVEL_NAMES[i.level])

        record_ids = [i.id for i in pagination.items]
        history = ReferralHistory.query.filter(
            ReferralHistory.referral_id.in_(record_ids)
        ).with_entities(
            ReferralHistory.referral_id,
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
            ReferralHistory.status,
        )
        referree_ids = [i.referree_id for i in history]
        trade_user_ids = get_trade_users(referree_ids)
        valid_referral_count_map, invalid_referral_count_map = defaultdict(int), defaultdict(int)
        trade_count_map = defaultdict(int)
        for item in history:
            if item.status == ReferralHistory.Status.VALID:
                valid_referral_count_map[item.referral_id] += 1
            else:
                invalid_referral_count_map[item.referral_id] += 1
            if item.referree_id in trade_user_ids:
                trade_count_map[item.referral_id] += 1
        result = []
        for item in pagination.items:
            result.append(dict(
                user_id=item.user_id,
                email=user_email_map[item.user_id],
                level=ambassador_map.get(item.user_id, '普通'),
                code=item.code,
                referral_count=valid_referral_count_map[item.id] + invalid_referral_count_map[item.id],
                trade_count=trade_count_map[item.id],
                valid_referral_count=valid_referral_count_map[item.id],
                invalid_referral_count=invalid_referral_count_map[item.id],
                rate=1 - item.rate
            ))
        return dict(
            items=result,
            total=pagination.total,
            types={
                **{k.name: v for k, v in Ambassador.LEVEL_NAMES.items()},
                'ALL_AMBASSADOR': '所有大使',
                'NORMAL': '普通'
            }
        )


@ns.route('/big-customer/info')
@respond_with_code
class BigCustomerInfoResource(Resource):

    @classmethod
    def get(cls):
        """用户-大客户信息"""
        return dict(
            sort=dict(
                position='持仓数', market_value='持仓市值', trade_amount='成交量'),
            attention=BigCustomer.Attention,
            type=BigCustomer.Type,
            user_types={t.name: t.value for t in User.UserType}
        )


@ns.route('/big-customer')
@respond_with_code
class BigCustomerListResource(Resource):
    marshal_fields = {
        'id': fx_fields.Integer,
        'user_id': fx_fields.Integer,
        'type': EnumMarshalField(
            BigCustomer.Type, output_field_lower=False),
        'attention': EnumMarshalField(
            BigCustomer.Attention, output_field_lower=False),
        'user_type': fx_fields.String(attribute=lambda x: x.user.user_type.name),
        'email': fx_fields.String(attribute=lambda x: x.user.email),
        'mobile': fx_fields.String(attribute=lambda x: x.user.mobile),
        'thirty_avg_cet_amount': AmountField,
        'thirty_avg_market_value': AmountField,
        'thirty_trade_amount': AmountField,
        'remark': fx_fields.String,
    }

    @classmethod
    @ns.use_kwargs(dict(
        sort=EnumField(['position', 'market_value', 'trade_amount'],
                       required=True),
        attention=EnumField(BigCustomer.Attention),
        type=EnumField(BigCustomer.Type),
        user_id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """
        大客户列表
        """
        # 已弃用，使用新表BigUserCustomer
        sort = kwargs['sort']
        query = BigCustomer.query
        if sort == 'position':
            query = query.order_by(BigCustomer.thirty_avg_cet_amount.desc())
        elif sort == 'market_value':
            query = query.order_by(BigCustomer.market_value.desc())
        else:
            query = query.order_by(BigCustomer.thirty_trade_amount.desc())
        if type_ := kwargs.get('type'):
            query = query.filter(BigCustomer.type == type_)
        if attention := kwargs.get('attention'):
            query = query.filter(BigCustomer.attention == attention)
        if user_id := kwargs.get('user_id'):
            query = query.filter(BigCustomer.user_id == user_id)
        sum_result = query.with_entities(
            func.sum(BigCustomer.cet_balance).label('total_cet_amount'),
            func.sum(BigCustomer.market_value).label('total_market_value'),
            func.sum(BigCustomer.thirty_trade_amount).label(
                'total_trade_amount'),
            func.max(BigCustomer.updated_at).label('updated_at'),
        ).first()
        result = query_to_page(query, kwargs['page'], kwargs['limit'],
                               cls.marshal_fields)
        result.update(dict(sum=sum_result))
        return result


# noinspection PyUnresolvedReferences
@ns.route('/big-customer/<int:id_>')
@respond_with_code
class BigCustomerResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        attention=EnumField(BigCustomer.Attention),
        type=EnumField(BigCustomer.Type),
        remark=fields.String
    ))
    def patch(cls, id_, **kwargs):
        """
        大客户信息修改
        """
        big_customer: BigCustomer = BigCustomer.query.get(id_)
        if not big_customer:
            raise InvalidArgument(message='大客户不存在')
        if attention := kwargs.get('attention'):
            big_customer.attention = attention
        if type_ := kwargs.get('type'):
            big_customer.type = type_
        if (remark := kwargs.get('remark')) is not None:
            big_customer.remark = remark
        db.session.commit()


# noinspection PyUnresolvedReferences
@ns.route('/<int:id_>/asset-lock')
@respond_with_code
class UserAssetLockResource(Resource):

    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        asset=fields.String(required=True),
        amount=PositiveDecimalField(required=True),
        lock_type=EnumField(['lock', 'unlock'], required=True),
        remark=fields.String(required=True)
    ))
    def post(cls, id_, **kwargs):
        """用户详情-资产锁定"""
        asset = kwargs['asset']
        amount = kwargs['amount']
        lock_type = kwargs['lock_type']
        if lock_type == 'lock':
            LockAssetHelper.check_valid_amount_for_balance_lock(
                id_, asset, amount)
        else:
            LockAssetHelper.check_valid_amount_for_balance_unlock(
                id_, asset, amount)

        record = LockedAssetBalance(
            user_id=id_,
            created_by=g.user.id,
            asset=asset,
            amount=amount,
            locked_at=now() if lock_type == 'lock' else None,
            unlocked_at=now() if lock_type == 'unlock' else None,
            remark=kwargs['remark'],
            business=LockedAssetBalance.Business.ADMIN,
            business_id=0,
            lock_type=LockedAssetBalance.OpType.LOCK,
            lock_business=BalanceBusiness.SYSTEM,
            unlock_business=BalanceBusiness.SYSTEM,
        )
        db.session_add_and_flush(record)
        record.business_id = record.id
        db.session.commit()
        check_asset_lock_schedule()


# noinspection PyUnresolvedReferences
@ns.route('/<int:id_>/asset-update')
@respond_with_code
class UserAssetUpdateResource(Resource):

    @classmethod
    @require_super_admin
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        asset=fields.String(required=True),
        amount=fields.Decimal(required=True),
        remark=fields.String(required=True),
        type=ex_fields.EnumField(UpdateAssetBalance.Type, required=True)
    ))
    def post(cls, id_, **kwargs):
        """用户详情-资产变更"""
        asset = kwargs['asset']
        amount = kwargs['amount']
        UpdateAssetHelper.check_valid_amount_for_balance_update(
            id_, asset, amount)

        record = UpdateAssetBalance(
            user_id=id_,
            created_by=g.user.id,
            audit_id=g.user.id,
            asset=asset,
            amount=amount,
            remark=kwargs['remark'],
            status=UpdateAssetBalance.Status.AUDITED,
            type=kwargs['type']
        )
        db.session.add(record)
        db.session.commit()


class AssetSeriesValidateMixin:

    @classmethod
    def validate_date_from(cls, kwargs):
        start_date, end_date = kwargs.get('start_date'), kwargs.get('end_date')
        if start_date and not end_date:
            raise InvalidArgument(message='请输入结束日期')
        if end_date and not start_date:
            raise InvalidArgument(message='请输入开始日期')
        if start_date and end_date and start_date > end_date:
            raise InvalidArgument(message='开始日期大于结束日期')
        return start_date, end_date

    @classmethod
    def get_query_start_end_date_by(cls, points, start_date, end_date):
        point_start_date = today() - timedelta(days=points)
        if start_date and end_date:
            if start_date < point_start_date:
                start_date = point_start_date
            if end_date > today():
                end_date = today()
            if start_date > end_date:
                end_date = start_date
        else:
            start_date, end_date = point_start_date, None
        return start_date, end_date


@ns.route('/<int:user_id>/asset-series')
@respond_with_code
class AssetSeriesResource(AssetSeriesValidateMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        account_type=EnumField(AccountBalanceType, required=True),
        asset=fields.String,
        market=fields.String,
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
    ))
    def get(cls, user_id, **kwargs):
        """
        用户详情-资产持仓历史
        """
        start_date, end_date = cls.validate_date_from(kwargs)
        user = User.query.get(user_id)
        account_type = kwargs['account_type']
        asset = kwargs.get('asset')
        market = kwargs.get('market')
        if account_type == AccountBalanceType.MARGIN:
            if not market:
                raise InvalidArgument
            if not (account := MarginAccount.query.filter(MarginAccount.name == market).first()):
                raise InvalidArgument
            account_id = account.id
        else:
            if not asset:
                raise InvalidArgument
        points = get_suggest_user_balance_series_points(user, max_points=365)
        start_date, end_date = cls.get_query_start_end_date_by(points, start_date, end_date)
        if account_type == AccountBalanceType.SPOT:
            return get_user_spot_balance_series(user_id, asset, start_date, end_date=end_date)
        if account_type == AccountBalanceType.INVESTMENT:
            return get_user_investment_balance_series(user_id, asset, start_date, end_date=end_date)
        if account_type == AccountBalanceType.PERPETUAL:
            return get_user_perpetual_balance_series(user_id, asset, start_date, end_date=end_date)
        if account_type == AccountBalanceType.MARGIN:
            return get_user_margin_balance_series(user_id, account_id, start_date, end_date=end_date)
        if account_type == AccountBalanceType.STAKING:
            return get_user_staking_balance_series(user_id, asset, start_date, end_date=end_date)
        raise InvalidArgument


@ns.route('/<int:user_id>/asset-sum-series')
@respond_with_code
class AssetSumSeriesResource(AssetSeriesValidateMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        account_type=EnumField(['SPOT', 'AMM', 'SUB_ACCOUNT']),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
    ))
    def get(cls, user_id, **kwargs):
        """
        用户详情-总资产持仓历史
        """
        start_date, end_date = cls.validate_date_from(kwargs)
        user = User.query.get(user_id)
        account_type = kwargs.get('account_type')
        points = get_suggest_user_balance_series_points(user, account_type=account_type, max_points=365)
        start_date, end_date = cls.get_query_start_end_date_by(points, start_date, end_date)
        if not account_type:
            return get_user_balance_sum_series(user_id, start_date, end_date=end_date)
        if account_type == 'SPOT':
            return get_user_spot_balance_sum_series(user_id, start_date, end_date=end_date)
        if account_type == 'AMM':
            return get_user_amm_balance_sum_series(user_id, start_date, end_date=end_date)
        if account_type == 'SUB_ACCOUNT':
            return get_user_sub_account_sum_series(user_id, start_date, end_date=end_date)
        raise InvalidArgument


@ns.route('/<int:user_id>/amm-market')
@respond_with_code
class AmmMarketResource(Resource):

    @classmethod
    def get_user_amm_market_record(cls, user_id):
        records = UserLiquidity.query.filter(
            UserLiquidity.user_id == user_id,
            UserLiquidity.liquidity > 0
        ).all()
        res = []
        total_usd = Decimal()
        market_list = [v.market for v in records]
        if len(market_list) > 0:
            cache_data = {market: json.loads(value) for (market, value) in
                          LongLiveLiquidityPoolAmountCache().hmget_with_keys(market_list)}
        else:
            cache_data = {}
        prices = PriceManager.assets_to_usd()
        for record in records:
            if record.market not in cache_data:
                continue
            _market_cache_data = cache_data[record.market]
            base_asset = _market_cache_data['base_asset']
            quote_asset = _market_cache_data['quote_asset']
            pool_liquidity = Decimal(_market_cache_data['liquidity'])
            pool_base_amount = Decimal(_market_cache_data[base_asset])
            pool_quote_amount = Decimal(_market_cache_data[quote_asset])
            if pool_liquidity > 0:
                share = record.liquidity/pool_liquidity
            else:
                share = Decimal()
            base_amount = quantize_amount(pool_base_amount * share, 8)
            quote_amount = quantize_amount(pool_quote_amount * share, 8)
            usd = base_amount * prices.get(base_asset, 0) \
                  + quote_amount * prices.get(quote_asset, 0)
            liq_usd = quantize_amount(usd, 8)
            total_usd += liq_usd
            res.append(dict(
                market=record.market,
                base_amount=base_amount,
                quote_amount=quote_amount,
                liquidity=record.liquidity,
                share=share,
                base_asset=base_asset,
                quote_asset=quote_asset,
                liquidity_usd=quantize_amount(liq_usd, 2)
            ))
        return res

    @classmethod
    def get(cls, user_id):
        """
        用户详情-做市账户
        """
        res = cls.get_user_amm_market_record(user_id)
        total_usd = sum([i['liquidity_usd'] for i in res])
        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AMMAssets,
            target_user_id=user_id,
        )

        return dict(
            total_usd=quantize_amount(total_usd, 2),
            items=res
        )


@ns.route('/<int:user_id>/amm-incomes')
@respond_with_code
class AmmIncomeHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=MarketField,
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField,
        limit=LimitField
    ))
    def get(cls, user_id, **kwargs):
        """
        用户详情-AMM收益记录
        """
        query = UserLiquiditySlice.query.filter(
            UserLiquiditySlice.user_id == user_id,
        ).order_by(UserLiquiditySlice.id.desc())
        if market := kwargs.get('market'):
            query = query.filter(UserLiquiditySlice.market == market)
        if start_date := kwargs.get('start_date'):
            query = query.filter(UserLiquiditySlice.date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(UserLiquiditySlice.date <= end_date)
        page, limit = kwargs['page'], kwargs['limit']
        pagination = query.paginate(page, limit)
        if not pagination.items:
            return dict(
                items=[],
                total=0
            )
        markets = {item.market for item in pagination.items}
        max_date = max(item.date for item in pagination.items)
        min_date = min(item.date for item in pagination.items)
        slices = LiquiditySlice.query.filter(
            LiquiditySlice.market.in_(markets),
            LiquiditySlice.date >= min_date,
            LiquiditySlice.date <= max_date
        ).all()
        slice_map = {(item.market, item.date): item for item in slices}
        profits = UserLiquidityProfit.query.filter(
            UserLiquidityProfit.user_id == user_id,
            or_(
                *[and_(
                    UserLiquidityProfit.market == x.market,
                    UserLiquidityProfit.date == x.date,
                ) for x in pagination.items]
            ),
            UserLiquidityProfit.date >= min_date,
            UserLiquidityProfit.date <= max_date,
        ).all()
        profit_map = {(x.date, x.market): x for x in profits}
        result = []
        market_query = Market.query.with_entities(
            Market.name,
            Market.base_asset,
            Market.quote_asset,
        ).all()
        market_info_map = {i.name: dict(
            base_asset=i.base_asset,
            quote_asset=i.quote_asset,
        ) for i in market_query}
        for item in pagination.items:
            slice = slice_map.get((item.market, item.date))
            if not slice or item.market not in market_info_map:
                continue
            rate = item.liquidity / slice.liquidity
            if user_profit := profit_map.get((item.date, item.market)):
                fee_usd = quantize_amount(user_profit.fee_usd, PrecisionEnum.CASH_PLACES)
                fee_base_amount = quantize_amount(user_profit.fee_base_amount, PrecisionEnum.COIN_PLACES)
                fee_quote_amount = quantize_amount(user_profit.fee_quote_amount, PrecisionEnum.COIN_PLACES)
            else:  # 兼容历史数据无UserLiquidityProfit
                fee_usd = quantize_amount(slice.fee_usd * rate, PrecisionEnum.CASH_PLACES)
                fee_base_amount = quantize_amount(slice.fee_base_amount * rate, PrecisionEnum.COIN_PLACES)
                fee_quote_amount = quantize_amount(slice.fee_quote_amount * rate, PrecisionEnum.COIN_PLACES)
            result.append(dict(
                market=item.market,
                date=item.date,
                base_asset=market_info_map[item.market]['base_asset'],
                quote_asset=market_info_map[item.market]['quote_asset'],
                liquidity_usd=quantize_amount(item.liquidity_usd, PrecisionEnum.CASH_PLACES),
                base_amount=quantize_amount(slice.base_amount * rate, PrecisionEnum.COIN_PLACES),
                quote_amount=quantize_amount(slice.quote_amount * rate, PrecisionEnum.COIN_PLACES),
                fee_usd=fee_usd,
                fee_base_amount=fee_base_amount,
                fee_quote_amount=fee_quote_amount,
            ))
        return dict(
            total=pagination.total,
            items=result
        )


@ns.route('/<int:user_id>/all-amm-incomes')
@respond_with_code
class AllAmmIncomeHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField,
        limit=LimitField
    ))
    def get(cls, user_id, **kwargs):
        """
        用户详情-AMM全部收益记录
        """
        query = UserLiquidityIncomeSummary.query.filter(
            UserLiquidityIncomeSummary.user_id == user_id,
        ).order_by(UserLiquidityIncomeSummary.date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(UserLiquidityIncomeSummary.date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(UserLiquidityIncomeSummary.date <= end_date)
        page, limit = kwargs['page'], kwargs['limit']
        pagination = query.paginate(page, limit)
        return dict(
            total=pagination.total,
            items=pagination.items,
        )


@ns.route("/auth-api")
@respond_with_code
class UserAuthAPIResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            withdrawals_enabled=fields.Boolean,
            trading_enabled=fields.Boolean,
            expire_status=EnumField(["EXPIRED", "VALID"]),
            export=fields.Boolean,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 用户->API用户列表 """
        # 已删除的auth-api不展示
        api_query = ApiAuth.query.filter(ApiAuth.status == ApiAuth.Status.VALID).order_by(ApiAuth.id.desc())
        if (user_id := kwargs.get("user_id")) is not None:
            api_query = api_query.filter(ApiAuth.user_id == user_id)
        if (withdrawals_enabled := kwargs.get("withdrawals_enabled")) is not None:
            api_query = api_query.filter(ApiAuth.withdrawals_enabled == withdrawals_enabled)
        if (trading_enabled := kwargs.get("trading_enabled")) is not None:
            api_query = api_query.filter(ApiAuth.trading_enabled == trading_enabled)
        if expire_status := kwargs.get("expire_status"):
            if expire_status == "EXPIRED":
                api_query = api_query.filter(ApiAuth.expired_at <= now())
            else:
                api_query = api_query.filter(or_(ApiAuth.expired_at > now(), ApiAuth.expired_at.is_(None)))

        export = kwargs.get("export")
        if export:
            records = api_query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(records)
        else:
            pagination = api_query.paginate(kwargs["page"], kwargs["limit"])
            records = pagination.items
            total = pagination.total

        user_ids = {i.user_id for i in records}
        user_email_map = {}
        chunk_size = 200
        for chunk_user_ids in batch_iter(user_ids, chunk_size):
            if chunk_user_ids:
                user_query = User.query.filter(User.id.in_(chunk_user_ids)).with_entities(User.id, User.email).all()
                user_email_map.update({i.id: i.email for i in user_query})

        result_items = [
            {
                "id": i.id,
                "user_id": i.user_id,
                "email": user_email_map.get(i.user_id, ""),
                "created_at": i.created_at,
                "expired_at": i.expired_at,
                "expired_status": "EXPIRED" if i.is_expired else "VALID",
                "withdrawals_enabled": i.withdrawals_enabled,
                "trading_enabled": i.trading_enabled,
            }
            for i in records
        ]

        user_count = (
                ApiAuth.query.filter(ApiAuth.status == ApiAuth.Status.VALID)
                .with_entities(func.count(ApiAuth.user_id.distinct()))
                .scalar()
                or 0
        )

        if export:
            export_headers = (
                {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
                {"field": "email", Language.ZH_HANS_CN: "邮箱"},
                {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
                {"field": "expired_at", Language.ZH_HANS_CN: "过期时间"},
                {"field": "permission", Language.ZH_HANS_CN: "权限"},
                {"field": "expired_status", Language.ZH_HANS_CN: "状态"},
            )
            for i in result_items:
                permissions = []
                if i["withdrawals_enabled"]:
                    permissions.append("可提现")
                if i["trading_enabled"]:
                    permissions.append("可交易")
                i["permission"] = "&".join(permissions) if permissions else "只读"
                i["created_at"] = i["created_at"].strftime("%Y-%m-%dT%H:%M:%S")
                i["expired_at"] = i["expired_at"].strftime("%Y-%m-%dT%H:%M:%S") if i["expired_at"] else "永不过期"
            return export_xlsx(
                filename="user_auth_api_list",
                data_list=result_items,
                export_headers=export_headers,
            )

        return dict(
            user_count=user_count,
            items=result_items,
            total=total,
            extra=dict(
                expire_status_dict={
                    "VALID": "生效中",
                    "EXPIRED": "已失效",
                }
            ),
        )


@ns.route('/withdrawal-whitelist')
@respond_with_code
class APIUsersResource(Resource):

    export_headers = (
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "address", Language.ZH_HANS_CN: "地址"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(),
        chain=fields.String(),
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """用户-API-提现白名单列表"""
        query = ApiWithdrawalAddress.query.filter(
            ApiWithdrawalAddress.status == ApiWithdrawalAddress.Status.VALID
        )
        if user_id := kwargs.get("user_id"):
            query = query.filter(
                ApiWithdrawalAddress.user_id == user_id
            )
        if chain := kwargs.get('chain'):
            query = query.filter(
                ApiWithdrawalAddress.chain == chain
            )
        query = query.order_by(
            ApiWithdrawalAddress.id.desc()
        )

        if kwargs['export']:
            record_list = []
            for item in list(query.limit(ADMIN_EXPORT_LIMIT).all()):
                record = item.to_dict()
                record['created_at'] = datetime_to_utc8_str(record['created_at'])
                record_list.append(record)
            return export_xlsx(
                filename='withdrawal_whitelist',
                data_list=record_list,
                export_headers=cls.export_headers,
            )

        records = query.paginate(kwargs['page'], kwargs['limit'])

        user_ids = [item.user_id for item in records.items]

        user_email_mapper = {
            user_id: email for user_id, email in
            User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email).all()
        }
        wallet_client = WalletClient()
        addrs = [(x.chain, x.address) for x in records.items]
        addrs_url = wallet_client.get_explorer_addresses_url(addrs)
        return dict(
            items=[{
                "email": user_email_mapper[i.user_id],
                "addr_url": addrs_url[index],
                **i.to_dict()} for index, i in enumerate(records.items)],
            total=records.total,
            chains=list_all_chains()
        )


@ns.route('/spot-trading-whitelist')
@respond_with_code
class APISpotTradingWhiteListResource(Resource):

    export_headers = (
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "account_type", Language.ZH_HANS_CN: "账号类型"},
        {"field": "allowed_markets", Language.ZH_HANS_CN: "可交易市场"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(),
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """用户-API-现货交易用户白名单列表"""
        query = ApiTradingWhiteList.query.filter(
            ApiTradingWhiteList.status == ApiTradingWhiteList.Status.VALID
        )
        if user_id := kwargs.get("user_id"):
            query = query.filter(
                ApiTradingWhiteList.user_id == user_id
            )
        query = query.order_by(
            ApiTradingWhiteList.id.desc()
        )
        if kwargs['export']:
            records = query.limit(ADMIN_EXPORT_LIMIT).all()
            user_ids = [item.user_id for item in records]
        else:
            records = query.paginate(kwargs['page'], kwargs['limit'])
            user_ids = [item.user_id for item in records.items]

        user_email_mapper, user_account_type_mapper = {}, {}
        for res in User.query.filter(User.id.in_(user_ids)).with_entities(
            User.id, User.email, User.user_type
        ).all():
            user_email_mapper[res.id] = res.email
            user_account_type_mapper[res.id] = res.user_type.value

        if kwargs['export']:
            record_list = []
            for item in list(query.limit(ADMIN_EXPORT_LIMIT).all()):
                record = item.to_dict()
                record['email'] = user_email_mapper[item.user_id]
                record['account_type'] = user_account_type_mapper[item.user_id]
                record['created_at'] = datetime_to_utc8_str(record['created_at'])
                record_list.append(record)
            return export_xlsx(
                filename='spot_trading_whitelist',
                data_list=record_list,
                export_headers=cls.export_headers,
            )
        return dict(
            items=[{
                "email": user_email_mapper[i.user_id],
                "account_type": user_account_type_mapper[i.user_id],
                **i.to_dict()} for _, i in enumerate(records.items)],
            online_markets=MarketCache.list_online_markets(),
            total=records.total,
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        markets=fields.List(fields.String, required=True),
        remark=fields.String(missing='')
    ))
    def post(cls, **kwargs):
        """用户-API-新建现货交易用户白名单"""
        user_id = kwargs['user_id']
        user = User.query.get(user_id)
        if user.is_sub_account:
            raise InvalidArgument
        allowed_markets = kwargs["markets"]
        if not allowed_markets:
            raise InvalidArgument(message="白名单不能为空")

        record = ApiTradingWhiteList.query.filter_by(user_id=user_id).first()
        if record:
            raise InvalidArgument(message="白名单已经存在")

        model = ApiTradingWhiteList
        row: ApiTradingWhiteList = model.get_or_create(user_id=user_id)
        row.market_type = ApiTradingWhiteList.Type.SPOT
        row.markets = allowed_markets
        row.status = model.Status.VALID
        row.remark = kwargs["remark"]
        db.session.add(row)
        db.session.commit()

        UserSpecialConfigChangeLog.add(
            user_id=user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.API_SPOT_TRADING_WHITELIST,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail="新建现货交易用户白名单",
            change_remark=kwargs['remark'],
            op_id=user.id
        )

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.APISpotTradingWhitelist,
            detail=kwargs,
            target_user_id=user_id,
        )
        return


@ns.route("/spot-trading-whitelist/<int:id_>")
@respond_with_code
class APISpotTradingWhiteListDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        markets=fields.List(fields.String, required=True),
        remark=fields.String(missing='')
    ))
    def put(cls, id_, **kwargs):
        """用户-API-修改现货交易用户白名单"""
        record = ApiTradingWhiteList.query.get(id_)
        if not record:
            raise InvalidArgument(message="白名单不存在")
        old_data = record.to_dict(enum_to_name=True)

        allowed_markets = kwargs["markets"]
        if not allowed_markets:
            raise InvalidArgument(message="白名单不能为空")

        record.markets = allowed_markets
        record.remark = kwargs["remark"]
        db.session.add(record)
        db.session.commit()

        UserSpecialConfigChangeLog.add(
            user_id=record.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.API_SPOT_TRADING_WHITELIST,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail="修改现货交易用户白名单",
            change_remark=kwargs['remark'],
            op_id=record.user_id
        )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.APISpotTradingWhitelist,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
            target_user_id=record.user_id,
        )
        return

    @classmethod
    def delete(cls, id_):
        """用户-API-删除现货交易用户白名单"""
        record = ApiTradingWhiteList.query.get(id_)
        if not record:
            raise InvalidArgument(message="白名单不存在")
        detail = record.to_dict(enum_to_name=True)
        db.session.delete(record)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.APISpotTradingWhitelist,
            detail=detail,
            target_user_id=record.user_id,
        )


@ns.route("/user-groups")
@respond_with_code
class CouponUserGroupConfigResource(Resource):

    @classmethod
    def get(cls):
        """用户-用户组配置"""
        return UserGroupValidator.get_filter_configs()


@ns.route("/cleared-users")
@respond_with_code
class ClearedUsersResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        status=EnumField(ClearedUser.Status),
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """用户-清退用户列表"""
        query = ClearedUser.query.filter(ClearedUser.valid.is_(True)).order_by(ClearedUser.id.desc())
        if user_id := kwargs.get('user_id'):
            query = query.filter(ClearedUser.user_id == user_id)
        if kwargs.get('status'):
            query = query.filter(ClearedUser.status == kwargs['status'])
        records = query.paginate(kwargs['page'], kwargs['limit'])
        user_ids = [item.user_id for item in records.items]
        users = User.query.filter(User.id.in_(user_ids)).all()
        user_map = {user.id: user for user in users}
        res = []
        for item in records.items:
            u = user_map[item.user_id]
            res.append(dict(
                user_id=item.user_id,
                name=u.name_displayed,
                email=u.email,
                mobile=u.mobile,
                status=item.status.name,
                remark=item.remark
            ))
        return dict(
            total=records.total,
            items=res,
            statuses=ClearedUser.Status
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        status=EnumField(ClearedUser.Status, required=True),
        remark=fields.String(missing='')
    ))
    def post(cls, **kwargs):
        """用户-新增清退用户"""
        user_id = kwargs['user_id']
        user = User.query.get(user_id)
        if user.is_sub_account:
            raise InvalidArgument
        status = kwargs['status']
        cleared_user = ClearedUser.get_or_create(user_id=user_id)
        cleared_user.status = status
        cleared_user.remark = kwargs['remark']
        cleared_user.valid = True
        db.session_add_and_commit(cleared_user)
        _cache_cls = UserVisitPermissionCache
        if status == ClearedUser.Status.FORBIDDEN:
            cache_value = _cache_cls.FORBIDDEN_VALUE
        else:
            cache_value = _cache_cls.ONLY_WITHDRAWAL_VALUE
        _cache_cls().add_users([user_id], cache_value)
        process_user_api_permission(user_id)
        if status == ClearedUser.Status.FORBIDDEN:
            change_detail = "添加清退用户"
            conf_type = UserSpecialConfigChangeLog.SpecialConfigType.CLEARED_USER
        else:
            change_detail = "添加仅提现用户"
            conf_type = UserSpecialConfigChangeLog.SpecialConfigType.ONLY_WITHDRAWAL

        UserSpecialConfigChangeLog.add(
            user_id=user_id,
            config_type=conf_type,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail=change_detail,
            change_remark=kwargs['remark'],
            op_id=cleared_user.id
        )
        return dict(
            user_id=user_id,
            name=user.name_displayed,
            email=user.email,
            mobile=user.mobile,
            status=cleared_user.status.name,
            remark=cleared_user.remark
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        remark=fields.String(missing=''),
    ))
    def patch(cls, **kwargs):
        """用户-修改清退用户"""
        cleared_user = ClearedUser.query.filter(
            ClearedUser.user_id == kwargs['user_id'],
            ClearedUser.valid.is_(True)).first()
        if not cleared_user:
            raise InvalidArgument
        cleared_user.remark = kwargs['remark']
        db.session.commit()
        status = cleared_user.status
        if status == ClearedUser.Status.FORBIDDEN:
            change_detail = "修改清退用户备注"
            conf_type = UserSpecialConfigChangeLog.SpecialConfigType.CLEARED_USER
        else:
            change_detail = "修改仅提现用户备注"
            conf_type = UserSpecialConfigChangeLog.SpecialConfigType.ONLY_WITHDRAWAL

        UserSpecialConfigChangeLog.add(
            user_id=cleared_user.user_id,
            config_type=conf_type,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail=change_detail,
            change_remark=kwargs['remark'],
            op_id=cleared_user.id
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        remark=fields.String(missing='')
    ))
    def delete(cls, **kwargs):
        """用户-删除清退用户"""
        cleared_user = ClearedUser.query.filter(
            ClearedUser.user_id == kwargs['user_id'],
            ClearedUser.valid.is_(True)).first()
        if not cleared_user:
            raise InvalidArgument
        cleared_user.valid = False
        db.session.commit()
        status = cleared_user.status
        _cache_cls = UserVisitPermissionCache
        if status == ClearedUser.Status.FORBIDDEN:
            change_detail = "删除清退用户"
            conf_type = UserSpecialConfigChangeLog.SpecialConfigType.CLEARED_USER
            cache_value = _cache_cls.FORBIDDEN_VALUE
        else:
            change_detail = "删除仅提现用户"
            conf_type = UserSpecialConfigChangeLog.SpecialConfigType.ONLY_WITHDRAWAL
            cache_value = _cache_cls.ONLY_WITHDRAWAL_VALUE

        _cache_cls().del_users([cleared_user.user_id], cache_value)

        UserSpecialConfigChangeLog.add(
            user_id=cleared_user.user_id,
            config_type=conf_type,
            op_type=UserSpecialConfigChangeLog.OpType.DELETE,
            admin_user_id=g.user.id,
            change_detail=change_detail,
            change_remark=kwargs["remark"],
            op_id=cleared_user.id
        )


@ns.route("/sign-off-users")
@respond_with_code
class UserSignOffHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user=fields.String,
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField,
        limit=LimitField,
        with_assets=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """用户-注销用户列表"""
        query = SignOffUser.query.order_by(SignOffUser.id.desc())
        if user_ := kwargs.get("user"):
            if user_.isdigit():
                user_id = int(user_)
                query = query.filter(SignOffUser.user_id == user_id)
            else:
                query = query.filter(SignOffUser.email == user_)
        if start_time := kwargs.get('start_time'):
            query = query.filter(SignOffUser.updated_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(SignOffUser.updated_at <= end_time)
        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        items = pagination.items
        records = []
        for item in items:
            record = item.to_dict()
            records.append(record)
        res = dict(
            items=records,
            total=pagination.total,
        )
        if kwargs['with_assets']:
            if not user_id:
                raise InvalidArgument
            if res['items'][0]['has_asset_cleared_off']:
                client = ServerClient()
                history = client.get_user_balance_history(
                    user_id=user_id, asset='', business=BalanceBusiness.SIGNED_OFF_USER_TO_ADMIN_TRANSFER,
                    start_time=0, end_time=0,
                    page=1)
                res['assets'] = {item['asset']: abs(Decimal(item['change'])) for item in history}
                return res

            sub_user_ids = SubAccount.query.filter(
                SubAccount.main_user_id == user_id
            ).with_entities(SubAccount.user_id).all()
            sub_user_ids = [item.user_id for item in sub_user_ids]

            asset_map = defaultdict(Decimal)
            for user_id in sub_user_ids + [user_id, ]:
                tmp = RealtimeAccountProfitLossProcessor.get_all_assets(user_id)
                for k, v in tmp.items():
                    asset_map[k] += v
            res['assets'] = {k: v for k, v in asset_map.items() if v > 0}
        return res


@ns.route("/relations")
@respond_with_code
class UserRelationListResource(Resource):
    @classmethod
    def get_same_ip_device_data(cls, user_ids: Iterable[int]):
        records = LoginRelationHistory.query.filter(
            LoginRelationHistory.user_id.in_(user_ids)
        ).order_by(LoginRelationHistory.id.asc()).all()
        register_ip_mapping = dict()
        login_ip_mapping = defaultdict(set)
        device_id_mapping = defaultdict(set)
        last_device_mapping = {}
        last_device_id_mapping = {}
        last_login_ip_mapping = {}
        same_login_ip_user_map = defaultdict(set)
        same_device_ip_user_map = defaultdict(set)
        same_reg_ip_count_map = {}

        for v in records:
            v: LoginRelationHistory
            if v.is_registration:
                register_ip_mapping[v.user_id] = v.ip
            login_ip_mapping[v.user_id].add(v.ip)
            if v.device_id:
                device_id_mapping[v.user_id].add(v.device_id)
                last_device_id_mapping[v.user_id] = v.device_id
            last_device_mapping[v.user_id] = v.device
            last_login_ip_mapping[v.user_id] = v.ip
        all_ips = reduce(lambda x, y: x | y, login_ip_mapping.values()) \
            if login_ip_mapping else set()
        app_device_ids = reduce(lambda x, y: x | y, device_id_mapping.values()) \
            if device_id_mapping else set()
        _q = LoginRelationHistory.query.filter(
            LoginRelationHistory.ip.in_(register_ip_mapping.values()),
            LoginRelationHistory.is_registration.is_(True)
        ).with_entities(
            func.count(LoginRelationHistory.user_id.distinct()).label("count"),
            LoginRelationHistory.ip
        ).group_by(LoginRelationHistory.ip).all()

        for _v in _q:
            same_reg_ip_count_map[_v.ip] = _v.count - 1

        def calculate_relations(holders: Dict):
            types_user_mapping = defaultdict(set)
            for _user_id, user_holders in holders.items():
                for _user_holder in user_holders:
                    types_user_mapping[_user_holder].add(_user_id)

            relations = defaultdict(int)
            for _user_id, user_holders in holders.items():
                added = set()
                for _user_holder in user_holders:
                    for _holder_user_id in types_user_mapping[_user_holder]:
                        if _holder_user_id != _user_id and _holder_user_id not in added:
                            added.add(_holder_user_id)
                            relations[_user_id] += 1
            return relations

        _q = LoginRelationHistory.query.filter(
            LoginRelationHistory.ip.in_(all_ips)
        ).with_entities(
            LoginRelationHistory.user_id,
            LoginRelationHistory.ip
        ).group_by(LoginRelationHistory.ip, LoginRelationHistory.user_id).all()

        for _v in _q:
            same_login_ip_user_map[_v.user_id].add(_v.ip)
        same_login_ip_count_map = calculate_relations(same_login_ip_user_map)

        _q = LoginRelationHistory.query.filter(
            LoginRelationHistory.device_id.in_(app_device_ids)
        ).with_entities(
            LoginRelationHistory.user_id,
            LoginRelationHistory.device_id
        ).group_by(LoginRelationHistory.device_id, LoginRelationHistory.user_id).all()
        for _v in _q:
            same_device_ip_user_map[_v.user_id].add(_v.device_id)
        same_device_id_count_map = calculate_relations(same_device_ip_user_map)

        login_data = defaultdict(
            lambda: dict(
                req_ip_count=0,
                login_ip_count=0,
                device_id_count=0
            )
        )

        for _uid in user_ids:
            login_data[_uid]["req_ip_count"] = 0

        for _uid, _ip in register_ip_mapping.items():
            login_data[_uid]["req_ip_count"] = same_reg_ip_count_map[_ip]
        for _uid in user_ids:
            login_data[_uid]["login_ip_count"] = same_login_ip_count_map[_uid]
            login_data[_uid]["device_id_count"] = same_device_id_count_map[_uid]

        return last_device_mapping, last_device_id_mapping, last_login_ip_mapping, login_data

    @classmethod
    def get_referral_data(cls, user_ids: Iterable[int]):
        query = ReferralHistory.query.filter(
            ReferralHistory.referree_id.in_(user_ids)
        ).with_entities(ReferralHistory.referrer_id,
                        ReferralHistory.referree_id).all()
        all_referrer_ids = {v.referrer_id for v in query}
        mapping = {v.referree_id: v.referrer_id for v in query}
        q = ReferralHistory.query.filter(
            ReferralHistory.referrer_id.in_(
                all_referrer_ids
            )
        ).with_entities(
            func.count(ReferralHistory.id).label("count"),
            ReferralHistory.referrer_id
        ).group_by(ReferralHistory.referrer_id).all()
        tmp_data = {v.referrer_id: v.count for v in q}
        result = {}
        for _referree_id, _referrer_id in mapping.items():
            result[_referree_id] = tmp_data[_referrer_id] - 1
        return mapping, result

    @classmethod
    def get_local_transfer_data(cls, user_ids: Iterable[int]):
        query = Withdrawal.query.filter(
            Withdrawal.user_id.in_(user_ids),
            Withdrawal.type == Withdrawal.Type.LOCAL,
            Withdrawal.status == Withdrawal.Status.FINISHED
        ).with_entities(Withdrawal.user_id, Withdrawal.recipient_user_id).all()
        relation_user_data = defaultdict(set)
        result = defaultdict(lambda: dict(
            user_count=0,
            count=0,
        ))
        for _uid in user_ids:
            result[_uid]["user_count"] = 0
        for _v in query:
            relation_user_data[_v.user_id].add(_v.recipient_user_id)
            result[_v.user_id]["count"] += 1
        for _uid, _uids in relation_user_data.items():
            result[_uid]["user_count"] = len(_uids)
        return result

    @classmethod
    def get_same_withdrawal_address_data(cls, user_ids: Iterable[int]):
        # 2022-01-01 08:00:00  (UTC+8)
        dt = timestamp_to_datetime(1640995200)
        q = Withdrawal.query.filter(
            Withdrawal.status == Withdrawal.Status.FINISHED,
            Withdrawal.user_id.in_(user_ids),
            Withdrawal.type == Withdrawal.Type.ON_CHAIN,
            Withdrawal.created_at >= dt
        ).with_entities(
            Withdrawal.user_id,
            Withdrawal.address
        ).order_by(Withdrawal.id.asc()).all()
        user_address_data = defaultdict(set)
        last_address_data = dict()
        for _v in q:
            user_address_data[_v.user_id].add(_v.address)
            last_address_data[_v.user_id] = _v.address
        all_addresses = reduce(lambda x, y: x | y, user_address_data.values()) \
            if user_address_data else set()
        if not all_addresses:
            return {}, {}
        q = Withdrawal.query.filter(
            Withdrawal.created_at >= dt,
            Withdrawal.address.in_(all_addresses),
            Withdrawal.type == Withdrawal.Type.ON_CHAIN,
            Withdrawal.status.in_([
                Withdrawal.Status.FINISHED,
            ])).with_entities(
            Withdrawal.user_id,
            Withdrawal.address
        ).group_by(Withdrawal.user_id, Withdrawal.address).all()
        address_user_result = defaultdict(set)
        user_relation_result = defaultdict(set)
        final_result = defaultdict(int)
        for _v in q:
            address_user_result[_v.address].add(_v.user_id)

        for uid in user_ids:
            addresses = user_address_data[uid]
            for address in addresses:
                user_relation_result[uid] |= address_user_result[address]
        for _uid, _uids in user_relation_result.items():
            final_result[_uid] = len(_uids) - 1
        return last_address_data, final_result

    @classmethod
    def format_records(cls, users: List[User], is_export: bool = False) -> List[Dict]:
        ts = current_timestamp(to_int=True)
        user_map = {v.id: v for v in users}
        uids = user_map.keys()
        last_device_mapping, last_device_id_mapping, last_login_ip_mapping, ip_device_data \
            = cls.get_same_ip_device_data(uids)
        refer_mapping, referral_data = cls.get_referral_data(uids)
        local_transfer_data = cls.get_local_transfer_data(uids)
        last_address_data, withdrawal_address_data = cls.get_same_withdrawal_address_data(uids)
        balance_usd_data = UserTotalBalanceHelper.get_users_total_balance(ts, uids)
        records = [dict(id=v.id, user_id=v.id, email=v.email, reg_ip=v.registration_ip, created_at=v.created_at)
                   for v in users]
        for record in records:
            _uid = record["id"]
            user_pref = UserPreferences(_uid)
            record["lang"] = LANGUAGE_NAMES[user_pref.language].chinese
            record["location_code"] = user_map.get(_uid).location_code
            location = get_country(record["location_code"]).cn_name if record["location_code"] else ''
            record["location"] =  f"{record['location_code']} ({location})"
            record["total_usd"] = balance_usd_data.get(_uid, 0)
            record["last_login_ip"] = last_login_ip_mapping.get(_uid, "")
            record["last_device"] = last_device_mapping.get(_uid, "")
            record["last_device_id"] = last_device_id_mapping.get(_uid, "")
            record["referral_id"] = refer_mapping.get(_uid, None)
            record["last_address"] = last_address_data.get(_uid, "")
            record["referral_data"] = referral_data.get(_uid, 0)
            record["withdrawal_address_data"] = withdrawal_address_data.get(_uid, 0)
            _u_ip_device_data = ip_device_data[_uid]
            _u_local_transfer_data = local_transfer_data[_uid]
            if not is_export:
                record["ip_device_data"] = _u_ip_device_data
                record["local_transfer_data"] = _u_local_transfer_data
            else:
                record["created_at"] = record["created_at"].strftime("%Y-%m-%dT%H:%M:%S")
                record["req_ip_count"] = _u_ip_device_data["req_ip_count"]
                record["login_ip_count"] = _u_ip_device_data["login_ip_count"]
                record["device_id_count"] = _u_ip_device_data["device_id_count"]
                record["local_transfer_count"] = _u_local_transfer_data["count"]
                record["local_transfer_user_count"] = _u_local_transfer_data["user_count"]
        return records

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        location_code=fields.String,
        language=fields.String,
        page=PageField,
        limit=LimitField(max_limit=100),
    ))
    def get(cls, **kwargs):
        """用户-IP/设备ID/提现地址查询"""
        return cls.do_get_query(**kwargs)

    @classmethod
    def do_get_query(cls, **kwargs):
        query = User.query.filter(User.user_type.notin_(
            [User.UserType.SUB_ACCOUNT])).order_by(User.id.desc())
        if _user_id := kwargs.get("user_id"):
            query = query.filter(User.id == _user_id)
        if start_time := kwargs.get('start_time'):
            query = query.filter(User.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(User.created_at <= end_time)
        if not start_time and not end_time:
            # created_at 没有索引，默认最多返回100条数据
            if not kwargs["limit"]:
                kwargs["limit"] = 100
        if location_code := kwargs.get('location_code'):
            query = query.filter(User.location_code == location_code)
        if language := kwargs.get("language"):
            query = query.join(
                UserPreference,
                UserPreference.user_id == User.id,
                isouter=True
            ).filter(
                UserPreference.key == UserPreferences.language.name,
                UserPreference.value == language
            )
        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        items = pagination.items
        records = cls.format_records(items)
        return dict(
            items=records,
            countries={
                code: get_country(code).cn_name for code in list_country_codes_3_admin()
            },
            langs = {lang.name: lang_value for lang, lang_value in language_cn_names().items()},
            total=pagination.total,
        )

    @classmethod
    @ns.use_kwargs(dict(
        batch_user_ids=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """用户-IP/设备ID/提现地址-批量查询"""
        max_num = 200
        query = User.query.filter(
            User.user_type.notin_([User.UserType.SUB_ACCOUNT]),
        ).order_by(User.id.desc())
        user_id_string = kwargs["batch_user_ids"]
        try:
            # 请求头太长时 uwsgi会返回502 （buffer-size默认4096）
            user_ids = list({int(v) for v in user_id_string.split(",")})[:max_num]
        except ValueError:
            raise InvalidArgument
        query = query.filter(User.id.in_(user_ids))
        items = query.all()
        records = cls.format_records(items)
        return dict(
            items=records,
            total=len(records),
        )


@ns.route("/relations-1day")
@respond_with_code
class UserRelation1dayListResource(UserRelationListResource):
    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        location_code=fields.String,
        language=fields.String,
        page=PageField,
        limit=LimitField(max_limit=100),
    ))
    def get(cls, **kwargs):
        """用户-IP/设备ID/提现地址查询（当天）"""
        if 'user_id' in kwargs:
            # 搜索用户允许不在当天内
            kwargs.pop('start_time', None)
            kwargs.pop('end_time', None)
        else:
            start_time = kwargs.get('start_time')
            if start_time:
                min_start_time = now() - timedelta(hours=24, minutes=10)
                if start_time <= min_start_time:
                    raise InvalidArgument(message="时间超过一天，请重新选择时间范围")
        return cls.do_get_query(**kwargs)


@ns.route("/relations/async-export")
@respond_with_code
class UserRelationAsyncExportResource(Resource):
    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "location", Language.ZH_HANS_CN: "国家"},
        {"field": "lang", Language.ZH_HANS_CN: "语言"},
        {"field": "total_usd", Language.ZH_HANS_CN: "资产折合"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "reg_ip", Language.ZH_HANS_CN: "注册IP"},
        {"field": "created_at", Language.ZH_HANS_CN: "注册时间"},
        {"field": "req_ip_count", Language.ZH_HANS_CN: "同注册IP账号数"},
        {"field": "last_login_ip", Language.ZH_HANS_CN: "最近登录IP"},
        {"field": "login_ip_count", Language.ZH_HANS_CN: "同登录IP账号数"},
        {"field": "last_device", Language.ZH_HANS_CN: "最近访问设备"},
        {"field": "last_device_id", Language.ZH_HANS_CN: "最近设备ID"},
        {"field": "device_id_count", Language.ZH_HANS_CN: "同设备ID账号数"},
        {"field": "referral_id", Language.ZH_HANS_CN: "邀请人用户ID"},
        {"field": "referral_data", Language.ZH_HANS_CN: "相同邀请人用户数"},
        {"field": "local_transfer_count", Language.ZH_HANS_CN: "站内转账次数"},
        {"field": "local_transfer_user_count", Language.ZH_HANS_CN: "站内转账关联用户数"},
        {"field": "last_address", Language.ZH_HANS_CN: "最后一次提现地址"},
        {"field": "withdrawal_address_data", Language.ZH_HANS_CN: "同提现地址用户数"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        batch_user_ids=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """用户-IP/设备ID/提现地址-异步导出"""
        user_id_string = kwargs["batch_user_ids"]
        try:
            user_ids = list({int(v) for v in user_id_string.split(",")})
        except ValueError:
            raise InvalidArgument

        async_max_num = 200000
        user_nums = len(user_ids)
        if user_nums > async_max_num:
            raise InvalidArgument(message=f"异步导出用户数不能超过{async_max_num}")
        cls.do_async_export(user_ids)
        return dict(
            items=[],
            total=user_nums,
        )

    @classmethod
    def do_async_export(cls, user_ids: List[int]):
        user = g.user
        cache = UserRelationAsyncExportCache(user.id)
        if cache.exists():
            raise InvalidArgument(message=f"存在进行中的导出未完成")

        cache.add_user_ids(user_ids)
        user_relation_async_export_task.delay(user.id)


@ns.route("/<int:id_>/relation")
@respond_with_code
class UserRelationsResource(Resource):

    @classmethod
    def get_same_reg_ip_user_ids(cls, ip: str, user_id: int):
        q = LoginRelationHistory.query.filter(
            LoginRelationHistory.ip == ip,
            LoginRelationHistory.is_registration.is_(True)
        ).with_entities(LoginRelationHistory.user_id).all()
        return {v.user_id for v in q} - {user_id}

    @classmethod
    def get_same_login_ip_user_ids(cls, user_id: int):
        q = LoginRelationHistory.query.filter(
            LoginRelationHistory.user_id == user_id,
        ).with_entities(LoginRelationHistory.ip).all()
        all_ips = {v.ip for v in q if v.ip}
        q = LoginRelationHistory.query.filter(
            LoginRelationHistory.ip.in_(all_ips)
        ).with_entities(LoginRelationHistory.user_id.distinct().label("uid")).all()
        return {v.uid for v in q} - {user_id}

    @classmethod
    def get_same_device_id_user_ids(cls, user_id: int):
        q = LoginRelationHistory.query.filter(
            LoginRelationHistory.user_id == user_id,
        ).with_entities(LoginRelationHistory.device_id).all()
        all_device_ids = {v.device_id for v in q if v.device_id}
        q = LoginRelationHistory.query.filter(
            LoginRelationHistory.device_id.in_(all_device_ids)
        ).with_entities(LoginRelationHistory.user_id).all()
        return {v.user_id for v in q} - {user_id}

    @classmethod
    def get_same_referral_user_ids(cls, user_id: int):
        query = ReferralHistory.query.filter(
            ReferralHistory.referree_id == user_id,
        ).with_entities(ReferralHistory.referrer_id).first()
        if not query:
            return set()
        referrer_id = query.referrer_id
        q = ReferralHistory.query.filter(
            ReferralHistory.referrer_id == referrer_id
        ).with_entities(ReferralHistory.referree_id).all()
        return {v.referree_id for v in q} - {user_id}

    @classmethod
    def get_local_transfer_user_ids(cls, user_id: int):
        q = Withdrawal.query.filter(
            Withdrawal.user_id == user_id,
            Withdrawal.type == Withdrawal.Type.LOCAL,
            Withdrawal.status == Withdrawal.Status.FINISHED
        ).with_entities(Withdrawal.recipient_user_id).all()
        if not q:
            return set()
        return {v.recipient_user_id for v in q} - {user_id}

    @classmethod
    def get_same_withdrawal_address_user_ids(cls, user_id: int):
        dt = timestamp_to_datetime(1640995200)
        q = Withdrawal.query.filter(
            Withdrawal.status == Withdrawal.Status.FINISHED,
            Withdrawal.user_id == user_id,
            Withdrawal.type == Withdrawal.Type.ON_CHAIN,
            Withdrawal.created_at >= dt
        ).with_entities(
            Withdrawal.address
        ).all()
        addresses = {v.address for v in q}
        if not addresses:
            return set()
        q = Withdrawal.query.filter(
            Withdrawal.created_at >= dt,
            Withdrawal.address.in_(addresses),
            Withdrawal.type == Withdrawal.Type.ON_CHAIN,
            Withdrawal.status.in_([
                Withdrawal.Status.FINISHED,
                Withdrawal.Status.CONFIRMING])
        ).with_entities(
            Withdrawal.user_id.distinct().label('uid')
        ).all()
        return {v.uid for v in q} - {user_id}

    @classmethod
    def get_last_withdrawal_addresses(cls, user_ids: Iterable[int]):
        if not user_ids:
            return {}
        sub_query = select(Withdrawal.query.with_entities(
            func.max(Withdrawal.id)
        ).filter(Withdrawal.user_id.in_(user_ids),
                 Withdrawal.type == Withdrawal.Type.ON_CHAIN
                 ).group_by(Withdrawal.user_id).subquery())
        q = Withdrawal.query.filter(
            Withdrawal.id.in_(sub_query)
        ).with_entities(Withdrawal.user_id, Withdrawal.address)
        return {v.user_id: v.address for v in q}

    @classmethod
    def get_basic_user_info(cls, user_ids: Iterable[int]):
        q = User.query.filter(User.id.in_(user_ids)).with_entities(
            User.id,
            User.email,
            User.created_at
        ).all()
        return [
            dict(
                user_id=v.id,
                email=v.email,
                created_at=v.created_at
            )
            for v in q
        ]

    class RelationType(Enum):
        SAME_REG_IP = "注册IP相同"
        SAME_LOGIN_IP = "登录IP相同"
        SAME_DEVICE_ID = "设备ID相同"
        SAME_REFERRAL_ID = "邀请人相同"
        LOCAL_TRANSFER = "站内转账相关"
        SAME_WITHDRAWAL_ADDRESS = "提现地址相同"

    class SortType(Enum):
        CREATED_AT = "注册时间"
        BALANCE_USD = "总资产市值"
        DEPOSIT_USD = "链上充值总市值"
        WITHDRAWAL_USD = "链上提现总市值"

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        search_user_id=fields.Integer,
        relation_type=ex_fields.EnumField(enum=RelationType),
        sort_type=ex_fields.EnumField(enum=SortType, missing=SortType.CREATED_AT),
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField,
        limit=LimitField(max_limit=100),
    ))
    def get(cls, **kwargs):
        """用户-关联用户详情"""

        user = User.query.get(kwargs["user_id"])
        if not user:
            raise InvalidArgument
        user_info = dict(user_id=user.id, email=user.email, user_type=user.user_type)
        if user.user_type == User.UserType.SUB_ACCOUNT:
            return dict(
                sort_types=cls.SortType,
                relation_types=cls.RelationType,
                user_info=user_info,
                items=[],
                total=0
            )

        user_id = user.id
        sort_type = kwargs["sort_type"]

        reg_user_ids = cls.get_same_reg_ip_user_ids(user.registration_ip, user.id)
        login_ip_user_ids = cls.get_same_login_ip_user_ids(user_id)
        device_id_user_ids = cls.get_same_device_id_user_ids(user_id)
        referral_user_ids = cls.get_same_referral_user_ids(user_id)
        local_transfer_user_ids = cls.get_local_transfer_user_ids(user_id)
        withdrawal_address_user_ids = cls.get_same_withdrawal_address_user_ids(user_id)

        relation_type_mapping = {
            cls.RelationType.SAME_REG_IP: reg_user_ids,
            cls.RelationType.SAME_LOGIN_IP: login_ip_user_ids,
            cls.RelationType.SAME_DEVICE_ID: device_id_user_ids,
            cls.RelationType.SAME_REFERRAL_ID: referral_user_ids,
            cls.RelationType.LOCAL_TRANSFER: local_transfer_user_ids,
            cls.RelationType.SAME_WITHDRAWAL_ADDRESS: withdrawal_address_user_ids,
        }
        user_relation_data = defaultdict(list)

        all_user_ids = reduce(lambda x, y: x | y, relation_type_mapping.values())

        for user_id in all_user_ids:
            for relation_type in cls.RelationType:
                if user_id in relation_type_mapping[relation_type]:
                    user_relation_data[user_id].append(relation_type)
        if relation_type := kwargs.get('relation_type'):
            all_user_ids = relation_type_mapping[relation_type]

        if search_user_id := kwargs.get("search_user_id"):
            all_user_ids = {search_user_id} & all_user_ids

        all_user_ids = sorted(list(all_user_ids), reverse=True)

        q = User.query.filter(User.id.in_(all_user_ids))
        if start_time := kwargs.get('start_time'):
            q = q.filter(
                User.created_at >= start_time,
            )
        if end_time := kwargs.get('end_time'):
            q = q.filter(
                User.created_at <= end_time,
            )

        all_user_ids = {v.id for v in q.with_entities(User.id)}

        withdrawal_tmp = Withdrawal.query.filter(
            Withdrawal.user_id.in_(all_user_ids),
            Withdrawal.status.in_((
                Withdrawal.Status.CONFIRMING,
                Withdrawal.Status.PROCESSING,
                Withdrawal.Status.FINISHED
            )),
            Withdrawal.type == Withdrawal.Type.ON_CHAIN
        ).order_by(Withdrawal.id.desc()).with_entities(
            Withdrawal.user_id,
            Withdrawal.amount,
            Withdrawal.asset,
        ).all()
        withdrawal_list_map = defaultdict(lambda: defaultdict(Decimal))
        deposit_list_map = defaultdict(lambda: defaultdict(Decimal))
        for w in withdrawal_tmp:
            withdrawal_list_map[w.user_id][w.asset] += w.amount

        deposit_tmp = Deposit.query.filter(
            Deposit.user_id.in_(all_user_ids),
            Deposit.status.in_((
                Deposit.Status.CONFIRMING,
                Deposit.Status.PROCESSING,
                Deposit.Status.TO_HOT,
                Deposit.Status.FINISHED
            )),
            Deposit.type == Deposit.Type.ON_CHAIN
        ).order_by(Deposit.id.desc()).with_entities(
            Deposit.user_id,
            Deposit.amount,
            Deposit.asset
        ).all()
        for d in deposit_tmp:
            deposit_list_map[d.user_id][d.asset] += d.amount
        user_balance_data = defaultdict(Decimal)
        withdrawal_usd_data = defaultdict(Decimal)
        deposit_usd_data = defaultdict(Decimal)
        for _uid in all_user_ids:
            user_balance_data[_uid] = Decimal()
            withdrawal_usd_data[_uid] = Decimal()
            deposit_usd_data[_uid] = Decimal()
        prices = PriceManager.assets_to_usd()
        for _uid, _data in deposit_list_map.items():
            for _asset, _amount in _data.items():
                _usd = _amount * prices.get(_asset, Decimal())
                deposit_usd_data[_uid] += _usd
        for _uid, _data in withdrawal_list_map.items():
            for _asset, _amount in _data.items():
                _usd = _amount * prices.get(_asset, Decimal())
                withdrawal_usd_data[_uid] += _usd
        items = cls.get_basic_user_info(all_user_ids)
        last_addresses = cls.get_last_withdrawal_addresses(all_user_ids)
        tool = UserTotalBalanceHelper(all_user_ids)
        user_balance_data = tool.get_user_balances()

        remark_map = get_users_remark_map([v["user_id"] for v in items])
        for item in items:
            uid = item["user_id"]
            item["last_address"] = last_addresses.get(uid, "")
            item["withdrawal_usd"] = quantize_amount(withdrawal_usd_data[uid], PrecisionEnum.CASH_PLACES)
            item["deposit_usd"] = quantize_amount(deposit_usd_data[uid], PrecisionEnum.CASH_PLACES)
            item["balance_usd"] = quantize_amount(
                user_balance_data.get(uid, Decimal()), PrecisionEnum.CASH_PLACES)
            item["relation"] = ",".join([v.value for v in user_relation_data[uid]])
            item["remark"] = remark_map.get(uid, "")

        items.sort(key=lambda x: x[sort_type.name.lower()], reverse=True)

        page, limit = kwargs["page"], kwargs["limit"]

        return dict(
            sort_types=cls.SortType,
            relation_types=cls.RelationType,
            user_info=user_info,
            items=items[(page - 1) * limit: page * limit],
            total=len(items),
        )


@ns.route("/third-party")
@respond_with_code
class ThirdPartyAccountResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        source=EnumField(ThirdPartyAccount.Source, required=True)
    ))
    def delete(cls, **kwargs):
        """解绑第三方账号"""
        user_id = kwargs["user_id"]
        user = User.query.get(user_id)
        if not user:
            raise InvalidArgument
        if not user.login_password_hash:
            raise UnbindThirdPartyAccountNotAllowed
        account = ThirdPartyAccount.query.filter(
            ThirdPartyAccount.source == kwargs["source"],
            ThirdPartyAccount.user_id == user_id,
            ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
        ).first()
        if not account:
            raise InvalidArgument
        account.status = ThirdPartyAccount.Status.DELETED
        db.session.commit()
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.ThirdPartyAccount,
            detail=kwargs,
            target_user_id=user_id,
        )
        return {
            f"{kwargs['source'].name.lower()}_account_name": "",
        }


@ns.route("/business-user")
@respond_with_code
class BusinessSystemUsersResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            page=PageField(unlimited=True),
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 用户-特殊配置-业务账号配置列表 """
        model = BusinessSystemUserRecord
        query = model.query.filter(model.status == model.Status.VALID)
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        records = query.order_by(model.id.desc()).paginate(kwargs["page"], kwargs["limit"])
        record_ids = [i.id for i in records.items]
        operator_id_dict, operator_name_dict = get_special_conf_create_operators(
            record_ids, UserSpecialConfigChangeLog.SpecialConfigType.BUSINESS_ACCOUNT)
        items = []
        for row in records.items:
            user = row.user
            item = row.to_dict()
            item["user_name"] = user.communication_name
            item.update(
                operator=operator_name_dict.get(row.id),
                operator_id=operator_id_dict.get(row.id)
            )
            items.append(item)

        return dict(
            items=items,
            total=records.total,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-业务账号配置-新增 """
        user_id = kwargs["user_id"]
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(message=f"用户{user_id}不存在")
        if user.sub_account_ref:
            raise InvalidArgument(message=f"用户{user_id}是子账号")

        model = BusinessSystemUserRecord
        row: BusinessSystemUserRecord = model.get_or_create(user_id=user_id)
        row.status = model.Status.VALID
        row.remark = kwargs["remark"]
        db.session.add(row)
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.BUSINESS_ACCOUNT,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail=f'新增业务账号',
            change_remark=kwargs['remark'],
            op_id=row.id
        )

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusinessSystemUser,
            detail=kwargs,
            target_user_id=user_id,
        )
        return row

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            remark=fields.String(missing=""),
        )
    )
    def put(cls, **kwargs):
        """ 用户-特殊配置-业务账号配置-编辑 """
        model = BusinessSystemUserRecord
        user_id = kwargs["user_id"]
        row: BusinessSystemUserRecord = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
        ).first()
        if row is None:
            raise InvalidArgument(message=f"用户{user_id}子账号上限配置不存在")
        old_data = row.to_dict(enum_to_name=True)

        if (remark := kwargs.get("remark")) is not None:
            row.remark = remark
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.BUSINESS_ACCOUNT,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail=f'修改备注',
            change_remark=kwargs['remark'],
            op_id=row.id
        )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BusinessSystemUser,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )
        return row

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 用户-特殊配置-业务账号配置-删除 """
        model = BusinessSystemUserRecord
        user_id = kwargs["user_id"]
        row: BusinessSystemUserRecord = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
        ).first()
        if row is not None:
            row.status = model.Status.DELETED
            db.session.commit()
            UserSpecialConfigChangeLog.add(
                user_id=user_id,
                config_type=UserSpecialConfigChangeLog.SpecialConfigType.BUSINESS_ACCOUNT,
                op_type=UserSpecialConfigChangeLog.OpType.DELETE,
                admin_user_id=g.user.id,
                change_detail=f'删除业务账号',
                op_id=row.id
            )

            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.BusinessSystemUser,
                detail=row.to_dict(enum_to_name=True),
                target_user_id=row.user_id,
            )
        return {}


@ns.route("/kol-user")
@respond_with_code
class KolUserListResource(Resource):
    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "用户邮箱"},
        {"field": "account_platforms", Language.ZH_HANS_CN: "账号平台"},
        {"field": "location", Language.ZH_HANS_CN: "用户地区"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    def dump_account_platforms(cls, account_platforms):
        return ",".join([
            KolApply.AccountPlatform[i].value for i in account_platforms.split(",")
        ])

    @classmethod
    def get_export_data(cls, items, countries):
        data = []
        for item in items:
            data.append(dict(
                user_id=item.user_id,
                email=item.email,
                account_platforms=cls.dump_account_platforms(item.account_platforms),
                location=countries.get(item.location_code, "/"),
                remark=item.remark,
                status=item.status.value,
            ))
        return data

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        account_platform=EnumField(KolApply.AccountPlatform, missing=""),
        status=EnumField(KolUser.Status),
        location=fields.String(missing=""),
        page=PageField,
        limit=LimitField,
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """用户-KOL列表-列表详情"""
        page, limit = kwargs["page"], kwargs['limit']
        kol_query = KolUser.query.select_from(KolUser).join(User)
        if user_id := kwargs.get("user_id"):
            kol_query = kol_query.filter(KolUser.user_id == user_id)
        if status := kwargs.get("status"):
            kol_query = kol_query.filter(KolUser.status == status)
        if location := kwargs.get("location"):
            kol_query = kol_query.filter(User.location_code == location)
        if account_platform := kwargs.get("account_platform"):
            kol_query = kol_query.filter(KolUser.account_platforms.contains(account_platform.name))
        kol_query = kol_query.with_entities(
            KolUser.id,
            KolUser.user_id,
            User.email,
            KolUser.account_platforms,
            User.location_code,
            KolUser.remark,
            KolUser.status,
        )
        countries = {code: get_country(code).cn_name for code in list_country_codes_3_admin()}
        if kwargs['export']:
            items = kol_query.limit(ADMIN_EXPORT_LIMIT).all()
            records = cls.get_export_data(items, countries)
            return export_xlsx(
                filename='kol_users',
                data_list=records,
                export_headers=cls.export_headers
            )
        paginate = kol_query.paginate(page, limit)
        return dict(
            total=paginate.total,
            items=[{
                "id": i.id,
                "user_id": i.user_id,
                "email": i.email,
                "account_platforms": cls.dump_account_platforms(i.account_platforms),
                "location_code": i.location_code,
                "remark": i.remark,
                "status": i.status.name,
            } for i in paginate.items],
            countries=countries,
            account_platforms={i.name: i.value for i in KolApply.AccountPlatform},
            statues={i.name: i.value for i in KolUser.Status},
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        account_platforms=fields.List(fields.String, required=True),
        status=EnumField(KolUser.Status, required=True),
        remark=fields.String(missing=""),
    ))
    def post(cls, **kwargs):
        """用户-KOL列表-添加KOL用户"""
        user_id = kwargs['user_id']
        str_account_platforms = ",".join(kwargs["account_platforms"])
        kol_user = KolUser.query.filter(
            KolUser.user_id == user_id
        ).first()
        if kol_user:
            raise InvalidArgument(message="当前用户已经是KOL用户了")
        kol_user = KolUser(
            user_id=user_id,
            account_platforms=str_account_platforms,
            remark=kwargs.get("remark"),
            status=kwargs["status"],
        )
        db.session_add_and_commit(kol_user)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.KolUser,
            detail=kwargs,
        )

        return dict(id=kol_user.id)


@ns.route("/kol-user/<int:id_>")
@respond_with_code
class KolUserDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        account_platforms=fields.List(fields.String, required=True),
        status=EnumField(KolUser.Status, required=True),
        remark=fields.String(missing=""),
    ))
    def put(cls, id_, **kwargs):
        """用户-KOL列表-修改KOL用户"""
        kol_user = KolUser.query.get(id_)
        if not kol_user:
            raise InvalidArgument
        old_data = kol_user.to_dict(enum_to_name=True)
        kol_user.user_id = kwargs['user_id']
        str_account_platforms = ",".join(kwargs["account_platforms"])
        kol_user.account_platforms = str_account_platforms
        kol_user.remark = kwargs.get("remark")
        kol_user.status = kwargs["status"]
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.KolUser,
            old_data=old_data,
            new_data=kol_user.to_dict(enum_to_name=True),
        )

        return dict(id=kol_user.id)

    @classmethod
    def delete(cls, id_):
        """用户-KOL列表-删除KOL用户"""
        kol_user = KolUser.query.get(id_)
        if not kol_user:
            raise InvalidArgument
        data = kol_user.to_dict(enum_to_name=True)
        db.session.delete(kol_user)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.KolUser,
            detail=data,
        )


@ns.route("/kol-apply")
@respond_with_code
class KolApplyListResource(Resource):
    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "用户邮箱"},
        {"field": "created_at", Language.ZH_HANS_CN: "申请时间"},
        {"field": "account_platform", Language.ZH_HANS_CN: "账号平台"},
        {"field": "fans_range", Language.ZH_HANS_CN: "账号粉丝量"},
        {"field": "location", Language.ZH_HANS_CN: "国家"},
        {"field": "country_and_city", Language.ZH_HANS_CN: "申请时填写地区"},
        {"field": "account_link", Language.ZH_HANS_CN: "账号链接"},
        {"field": "expected_salary", Language.ZH_HANS_CN: "账号粉丝量"},
        {"field": "update_frequency", Language.ZH_HANS_CN: "账号更新频率"},
        {"field": "account_created_at", Language.ZH_HANS_CN: "账号成立时间"},
        {"field": "country_and_city", Language.ZH_HANS_CN: "所在国家及城市"},
        {"field": "other_contact", Language.ZH_HANS_CN: "其他联系方式"},
        {"field": "expected_salary", Language.ZH_HANS_CN: "期待月薪"},
        {"field": "expectations", Language.ZH_HANS_CN: "期望或目标"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    def get_export_data(cls, items, countries):
        data = []
        for item in items:
            data.append(dict(
                user_id=item.user_id,
                email=item.email,
                account_platform=item.account_platform.value,
                fans_range=item.fans_range.value,
                other_contact=item.other_contact,
                location=countries.get(item.location_code, "/"),
                country_and_city=item.country_and_city,
                created_at=datetime_to_str(item.created_at),
                account_link=item.account_link,
                expected_salary=item.expected_salary,
                update_frequency=item.update_frequency.value,
                account_created_at=datetime_to_str(item.account_created_at),
                expectations=item.expectations,
                remark=item.remark
            ))
        return data

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        account_platform=EnumField(KolApply.AccountPlatform, missing=""),
        location=fields.String(missing=""),
        page=PageField,
        limit=LimitField,
        start=TimestampField(),
        end=TimestampField(),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """用户-KOL申请-列表详情"""
        page, limit = kwargs["page"], kwargs['limit']
        apply_query = KolApply.query.select_from(KolApply).join(User)
        if user_id := kwargs.get("user_id"):
            apply_query = apply_query.filter(KolApply.user_id == user_id)
        if location := kwargs.get("location"):
            apply_query = apply_query.filter(User.location_code == location)
        if account_platform := kwargs.get("account_platform"):
            apply_query = apply_query.filter(KolApply.account_platform == account_platform)
        if start := kwargs.get('start'):
            apply_query = apply_query.filter(KolApply.created_at >= start)
        if end := kwargs.get('end'):
            apply_query = apply_query.filter(KolApply.created_at <= end)
        apply_query = apply_query.with_entities(
            KolApply.id,
            KolApply.user_id,
            KolApply.created_at,
            KolApply.account_platform,
            KolApply.fans_range,
            KolApply.expected_salary,
            KolApply.account_link,
            KolApply.average_views,
            KolApply.update_frequency,
            KolApply.account_created_at,
            KolApply.country_and_city,
            KolApply.other_contact,
            KolApply.expectations,
            KolApply.remark,
            User.location_code,
            User.email
        )
        countries = {code: get_country(code).cn_name for code in list_country_codes_3_admin()}
        if kwargs['export']:
            items = apply_query.limit(ADMIN_EXPORT_LIMIT).all()
            records = cls.get_export_data(items, countries)
            return export_xlsx(
                filename='kol_apply_users',
                data_list=records,
                export_headers=cls.export_headers
            )
        paginate = apply_query.paginate(page, limit)
        return dict(
            total=paginate.total,
            items=[{
                "id": i.id,
                "user_id": i.user_id,
                "email": i.email,
                "account_platform": i.account_platform.name,
                "account_link": i.account_link,
                "created_at": i.created_at,
                "fans_range": i.fans_range.name,
                "expected_salary": i.expected_salary,
                "average_views": i.average_views,
                "update_frequency": i.update_frequency.name,
                "account_created_at": i.account_created_at,
                "country_and_city": i.country_and_city,
                "location_code": i.location_code,
                "other_contact": i.other_contact,
                "expectations": i.expectations,
                "remark": i.remark
            } for i in paginate.items],
            countries=countries,
            account_platforms={i.name: i.value for i in KolApply.AccountPlatform},
            fans_ranges={i.name: i.value for i in KolApply.FansRange},
            update_frequencys={i.name: i.value for i in KolApply.UpdateFrequency},
        )


@ns.route("/kol-apply/<int:apply_id>")
@respond_with_code
class KolApplyDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String(missing="")
    ))
    def put(cls, apply_id, **kwargs):
        """用户-KOL申请-修改详情"""
        remark = kwargs['remark']
        kol_apply = KolApply.query.get(apply_id)
        if remark and remark != kol_apply.remark:
            old_data = kol_apply.to_dict(enum_to_name=True)

            kol_apply.remark = remark
            db.session.commit()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.KolApply,
                old_data=old_data,
                new_data=kol_apply.to_dict(enum_to_name=True),
                target_user_id=kol_apply.user_id,
            )


class P2pMerchantMixin:

    statues = {
        P2pMerchant.Status.ACTIVE.value: "正常",
        P2pMerchant.Status.INACTIVE.value: "关闭",
        P2pMerchant.Status.CANCELED.value: "取消",
        P2pMerchant.Status.CANCELING.value: "保证金赎回中",
        P2pMerchant.Status.FORBID.value: "禁止"
    }
    shop_statues = {P2pMerchant.ShopStatus.OPEN.value: "接单中", P2pMerchant.ShopStatus.CLOSED.value: "休息中"}
    countries = {code: get_country(code).cn_name for code in list_country_codes_3()}

    @classmethod
    def get_user_mapper(cls, user_ids: set[int]):
        return {u.id: u for u in User.query.filter(
            User.id.in_(user_ids)
        ).all()}

    @classmethod
    def get_user_admin_avatar(cls, user_ids: set[int]):
        return {
            u.user_id: u.admin_avatar_url for u in UserExtra.query.filter(
                UserExtra.user_id.in_(user_ids)
            ).all()
        }

    @classmethod
    def get_merchant_trade_info(cls, user_ids: set[int]):
        user_summary_mapper = {
            p.user_id: {
                "acceptance_count": p.acceptance_count,
                "acceptance_rate": p.acceptance_rate,
            } for p in P2pUserTradeSummary.query.filter(
                P2pUserTradeSummary.user_id.in_(user_ids),
            ).all()
        }
        daily_merchant_summary = {
            i.user_id: {
                "deal_count": i.total_deal_count or 0,
                "total_order_count": i.total_order_count or 0
            } for i in
            P2pMerchantDailyTradeSummary.query.filter(
                P2pMerchantDailyTradeSummary.user_id.in_(user_ids),
            ).group_by(
                P2pMerchantDailyTradeSummary.user_id
            ).with_entities(
                P2pMerchantDailyTradeSummary.user_id,
                func.max(P2pMerchantDailyTradeSummary.total_deal_count).label("total_deal_count"),
                func.max(P2pMerchantDailyTradeSummary.total_order_count).label("total_order_count")
            ).all()
        }
        data = {}
        for user_id in user_ids:
            user_summary = user_summary_mapper.get(user_id, {})
            merchant_summary = daily_merchant_summary.get(user_id, {})
            acceptance_count = user_summary.get("acceptance_count", 0)
            completion_rate = merchant_summary.get("deal_count", 0) / acceptance_count if acceptance_count else 0
            data[user_id] = {
                **user_summary,
                **merchant_summary,
                "completion_rate": completion_rate
            }
        return data

    @classmethod
    def get_p2p_user_nickname(cls, user_ids: set[int]):

        return {i: n for i, n in P2pUser.query.filter(
            P2pUser.user_id.in_(user_ids)
        ).with_entities(
            P2pUser.user_id,
            P2pUser.nickname
        ).all()}

    @classmethod
    def _get_user_time_range_trade_summary(
            cls,
            user_ids: set[int],
            start_date: datetime.date = None,
            end_date: datetime.date = None
    ):
        query = P2pMerchantDailyTradeSummary.query.filter(
            P2pMerchantDailyTradeSummary.user_id.in_(user_ids)
        )
        if start_date:
            query = query.filter(
                P2pMerchantDailyTradeSummary.report_date >= start_date,
            )
        if end_date:
            query = query.filter(
                P2pMerchantDailyTradeSummary.report_date <= end_date
            )
        query = query.group_by(
            P2pMerchantDailyTradeSummary.user_id
        ).with_entities(
            P2pMerchantDailyTradeSummary.user_id,
            func.sum(P2pMerchantDailyTradeSummary.trade_amount_usd).label("trade_usd"),
        ).all()
        return {
            i.user_id: quantize_amount(i.trade_usd, 2) for i in query
        }

    @classmethod
    def get_user_trade_info(cls, user_ids: set[int]) -> tuple[dict, dict]:
        _today = today()
        last_start_date, last_end_date = last_month_range(_today)
        last_mapper = cls._get_user_time_range_trade_summary(user_ids, last_start_date, last_end_date)
        start_date = date(_today.year, _today.month, 1)
        current_mapper = cls._get_user_time_range_trade_summary(user_ids, start_date, _today)
        return last_mapper, current_mapper


@ns.route("/p2p-merchant")
@respond_with_code
class P2pMerchantResource(Resource, P2pMerchantMixin):

    @classmethod
    def get_merchant_location_distribution(cls, countries):
        location_count_list = [
            {
                "location": location,
                "count": count
            } for location, count in P2pMerchant.query.select_from(P2pMerchant).join(
                KycVerification, P2pMerchant.user_id == KycVerification.user_id, isouter=True
            ).filter(
                KycVerification.status == KycVerification.Status.PASSED
            ).group_by(
                KycVerification.country
            ).with_entities(
                KycVerification.country,
                func.count(P2pMerchant.user_id)
            ).all()
            if location
        ]
        header_location = sorted(location_count_list, key=lambda x: -x["count"])[0:5]
        total_count = P2pMerchant.query.count()
        other_count = total_count - sum(i['count'] for i in header_location)
        all_data = header_location + [{
            "location": "OTHER",
            "count": other_count
        }]
        countries['OTHER'] = "其他"
        return [{
            "location": countries[data["location"]],
            "rate": format_percent(data['count'] / total_count)
        } for data in all_data]

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "avatar", Language.ZH_HANS_CN: "头像"},
        {"field": "user_email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "nickname", Language.ZH_HANS_CN: "广告商名称"},
        {"field": "kyc_country", Language.ZH_HANS_CN: "kyc地区"},
        {"field": "shop_status", Language.ZH_HANS_CN: "店铺状态"},
        {"field": "current_monthly_trade_amount", Language.ZH_HANS_CN: "当月成交量(USD)"},
        {"field": "last_monthly_trade_amount", Language.ZH_HANS_CN: "上月成交量(USD)"},
        {"field": "deal_amount", Language.ZH_HANS_CN: "总成交额(USD)"},
        {"field": "acceptance_count", Language.ZH_HANS_CN: "接单总数"},
        {"field": "acceptance_rate", Language.ZH_HANS_CN: "接单率"},
        {"field": "deal_count", Language.ZH_HANS_CN: "成交订单数"},
        {"field": "completion_rate", Language.ZH_HANS_CN: "成交率"},
        {"field": "status", Language.ZH_HANS_CN: "身份状态"},
        {"field": "require_margin", Language.ZH_HANS_CN: "应缴保证金(USDT)"},
        {"field": "paid_margin", Language.ZH_HANS_CN: "实缴保证金(USDT)"},
        {"field": "created_at", Language.ZH_HANS_CN: "广告商开通时间"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "contact", Language.ZH_HANS_CN: "联系方式"},
        {"field": "self_introduction", Language.ZH_HANS_CN: "自我介绍"},
    )

    @classmethod
    def dump_export_data(cls, items, countries, statues, shop_statues):
        res = []
        for i in items:
            i['created_at'] = i['created_at'].astimezone(tz.gettz("UTC+8")).strftime("%Y-%m-%d %H:%M:%S")
            i['status'] = statues[i['status'].value]
            i['kyc_country'] = countries.get(i['kyc_country'])
            i['shop_status'] = shop_statues[i['shop_status'].value]
            i['completion_rate'] = format_percent(i.get('completion_rate', Decimal()))
            i['acceptance_rate'] = format_percent(i.get('acceptance_rate', Decimal()))
            res.append(i)
        return res

    @classmethod
    def get_margin_map(cls, user_ids):
        model = P2pUserMargin
        rows = model.query.filter(model.user_id.in_(user_ids)).all()
        return {
            row.user_id: {
                "require_margin": row.require_margin,
                "paid_margin": row.paid_margin,
                "grace_deadline": row.grace_deadline,
            } for row in rows
        }

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        name=fields.String,
        location=fields.String(missing=""),
        shop_status=EnumField(P2pMerchant.ShopStatus, enum_by_value=True),
        status=EnumField(P2pMerchant.Status, enum_by_value=True),
        page=PageField(missing=1),
        limit=LimitField(missing=15),
        export=fields.Boolean()
    ))
    def get(cls, **kwargs):
        """用户-P2P-商家列表"""
        page, limit = kwargs["page"], kwargs['limit']
        query = P2pMerchant.query
        if user_id := kwargs.get("user_id"):
            query = query.filter(P2pMerchant.user_id == user_id)
        if shop_status := kwargs.get("shop_status"):
            query = query.filter(P2pMerchant.shop_status == shop_status)
        if status := kwargs.get('status'):
            query = query.filter(P2pMerchant.status == status)
        query = query.order_by(P2pMerchant.id.desc())

        kyc_location = kwargs.get("location")
        export = kwargs.get('export')
        if not kyc_location and not export:
            paginate = query.paginate(page, limit)
            data = paginate.items
            total = paginate.total
            user_ids = {i.user_id for i in data}
        else:
            left, right = (page - 1) * limit, page * limit
            all_data = query.all()
            user_ids = [i.user_id for i in all_data]
            if kyc_location:
                kyc_map = get_user_kyc_country_map(user_ids, kyc_location)
                user_ids = list(kyc_map.keys())
            total = len(user_ids)
            if not export:
                user_ids = set(user_ids[left:right])
            data = [i for i in all_data if i.user_id in user_ids]

        user_mapper = cls.get_user_mapper(user_ids)
        last_mapper, current_mapper = cls.get_user_trade_info(user_ids)
        merchant_trade_mapper = cls.get_merchant_trade_info(user_ids)
        kyc_mapper = get_user_kyc_country_map(user_ids)
        total_deal_mapper = cls._get_user_time_range_trade_summary(user_ids)
        user_avatar_mapper = cls.get_user_admin_avatar(user_ids)
        margin_map = cls.get_margin_map(user_ids)
        items = []
        for item in data:
            user_id = item.user_id
            user = user_mapper.get(user_id)
            if not user:
                continue
            merchant_trade = merchant_trade_mapper.get(user_id, {})
            items.append({
                **merchant_trade,
                "nickname": user.nickname,
                "avatar": user_avatar_mapper.get(user_id, ""),
                "kyc_country": kyc_mapper.get(user_id, ""),
                "user_id": user_id,
                "user_email": user.email,
                "current_monthly_trade_amount": current_mapper.get(user_id, 0),
                "last_monthly_trade_amount": last_mapper.get(user_id, 0),
                "deal_amount": total_deal_mapper.get(user_id, 0),
                **margin_map.get(user_id, {}),
                **item.to_dict()
            })

        if export:
            res = cls.dump_export_data(items, cls.countries, cls.statues, cls.shop_statues)
            return export_xlsx(
                filename='p2p_merchant',
                data_list=res,
                export_headers=cls.export_headers
            )
        return dict(
            items=items,
            total=total,
            countries=cls.countries,
            statues=cls.statues,
            shop_statues=cls.shop_statues,
            location_distribution=cls.get_merchant_location_distribution(cls.countries)
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        remark=fields.String
    ))
    def put(cls, **kwargs):
        """用户-P2P-修改商家备注"""
        merchant = P2pMerchant.query.filter(P2pMerchant.user_id == kwargs['user_id']).first()
        if not merchant:
            raise InvalidArgument
        old_remark = merchant.remark
        merchant.remark = kwargs.get("remark", "")
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PMerchant,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=merchant.remark),
            target_user_id=merchant.user_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        status=EnumField(P2pMerchant.Status, enum_by_value=True, required=True)
    ))
    def patch(cls, **kwargs):
        """用户-p2p-修改商家状态"""
        user_id = kwargs['user_id']
        with CacheLock(LockKeys.update_p2p_user(user_id)):
            merchant = P2pMerchant.query.filter(P2pMerchant.user_id == user_id).first()
            if not merchant:
                raise InvalidArgument
            old_status = merchant.status
            new_status = kwargs['status']
            cls.check_new_status(merchant.status, new_status)
            merchant.status = kwargs['status']
            db.session.commit()
            if merchant.status == P2pMerchant.Status.INACTIVE:
                P2pUtils.offline_advertising_by_merchant(merchant.user_id, reason=AutoOfflineAdvReason.FROZEN_MERCHANT)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PMerchant,
            old_data=dict(status=old_status),
            new_data=dict(status=new_status),
            target_user_id=merchant.user_id,
        )

    @classmethod
    def check_new_status(cls, old_status, new_status):
        s = P2pMerchant.Status
        if old_status == P2pMerchant.Status.CANCELED:
            raise InvalidArgument(message="商家已取消身份，无法操作")
        status_map = {
            s.ACTIVE: s.INACTIVE,
            s.INACTIVE: s.ACTIVE,
            s.CANCELING: s.FORBID,
            s.FORBID: s.CANCELING,
        }
        if status_map.get(old_status) != new_status:
            raise InvalidArgument(message="商家状态错误，无法冻结/解冻")


@ns.route("/p2p-merchant/<int:user_id>")
@respond_with_code
class P2pMerchantDetailResource(Resource, P2pMerchantMixin):

    @classmethod
    def get_merchant_adv(cls, user_id):
        # 使用SQLAlchemy查询
        adv_list = P2pAdvertisingMySQL.query.filter_by(
            user_id=user_id
        ).with_entities(
            P2pAdvertisingMySQL.mongo_id,
            P2pAdvertisingMySQL.adv_number,
            P2pAdvertisingMySQL.adv_type,
            P2pAdvertisingMySQL.base,
            P2pAdvertisingMySQL.quote,
            P2pAdvertisingMySQL.price,
            P2pAdvertisingMySQL.status,
            P2pAdvertisingMySQL.updated_at
        ).all()
        
        data = [
            {
                "id": adv.mongo_id,
                "adv_number": adv.adv_number,
                "adv_type": adv.adv_type.name,
                "base": adv.base,
                "quote": adv.quote,
                "price": adv.price,
                "status": adv.status.name,
                "updated_at": adv.updated_at
            } for adv in adv_list
        ]

        def sort_adv(item):
            adv_type = item['adv_type']
            if adv_type == P2pBusinessType.BUY.name:
                return 1, item['price']
            return -1, -item['price']

        sort_data = sorted(data, key=sort_adv)
        return sort_data

    @classmethod
    def get_merchant_online_data(cls, user_id):
        trade_data: P2pMerchantDailyTradeSummary = P2pMerchantDailyTradeSummary.query.filter(
            P2pMerchantDailyTradeSummary.user_id == user_id
        ).order_by(P2pMerchantDailyTradeSummary.report_date.desc()).first()
        if not trade_data:
            return {}
        today_ = today()
        current_start_date = today_.replace(day=1)
        current_monthly_query = P2pMerchantDailyTradeSummary.query.filter(
            P2pMerchantDailyTradeSummary.user_id == user_id,
            P2pMerchantDailyTradeSummary.report_date >= current_start_date,
            P2pMerchantDailyTradeSummary.report_date <= today_
        ).with_entities(
            P2pMerchantDailyTradeSummary.online_time,
            P2pMerchantDailyTradeSummary.new_deal_customer_ids,
            P2pMerchantDailyTradeSummary.daily_merchant_cancel_count,
            P2pMerchantDailyTradeSummary.daily_acceptance_count
        ).all()
        monthly_valid_days = 0
        monthly_acceptance_count = 0
        monthly_merchant_cancel_count = 0
        monthly_bitmap = BitMap()
        for online_time, new_deal_customer_ids, \
                daily_merchant_cancel_count, daily_acceptance_count in current_monthly_query:
            monthly_valid_days += 1 if online_time >= P2pMerchantDailyTradeSummary.ONLINE_VALID_SECONDS else 0
            if new_deal_customer_ids:
                monthly_bitmap = monthly_bitmap.union(BitMap.deserialize(new_deal_customer_ids))
            monthly_acceptance_count += daily_acceptance_count or 0
            monthly_merchant_cancel_count += daily_merchant_cancel_count or 0

        return dict(
            online_time=trade_data.online_time,
            total_online_time=trade_data.total_online_time,
            avg_online_time=trade_data.avg_online_time,
            monthly_valid_days=monthly_valid_days,
            total_valid_days=trade_data.total_valid_days,
            monthly_customer_count=len(monthly_bitmap),
            monthly_acceptance_count=monthly_acceptance_count,
            monthly_acceptance_rate=quantize_amount(
                Decimal(monthly_acceptance_count / (monthly_acceptance_count + monthly_merchant_cancel_count)), 4
            ) if monthly_acceptance_count else 0,
            total_customer_count=len(set(BitMap.deserialize(trade_data.all_deal_customer_ids))) if trade_data.all_deal_customer_ids else 0
        )

    @classmethod
    def get_pay_channels(cls, user_id: int):
        user_channels = UserPayChannelMySQL.get_user_all_channel(user_id)
        channel_ids = [i.pay_channel_id for i in user_channels]
        channels = P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.mongo_id.in_(channel_ids)).all()
        channels_map = {str(i.mongo_id): i.to_dict(enum_to_name=True) for i in channels}
        res = []
        for item in user_channels:
            cid = item.pay_channel_id
            if not (base_info := channels_map.get(str(cid))):
                continue
            if new_item := UserPayChannelBus.format_channel(base_info, item.to_dict(enum_to_name=True), ''):
                values = []
                for v in new_item.get('form', []):
                    values.append(f"{v['field_name']}: {v['value']}")
                res.append({
                    'name': new_item['name'],
                    'value': '; '.join(values)
                })
        if not res:
            res.append({'name': '--', 'value': '--'})
        return res

    @classmethod
    def get(cls, user_id: int):
        """用户-P2P-查看商家详情"""
        merchant = P2pMerchant.query.filter(
            P2pMerchant.user_id == user_id
        ).first()
        if not merchant:
            raise InvalidArgument
        user: User = User.query.get(merchant.user_id)
        user_id = user.id
        user_ids = {user_id}
        last_mapper, current_mapper = cls.get_user_trade_info(user_ids)
        merchant_trade_mapper = cls.get_merchant_trade_info(user_ids)
        kyc_mapper = get_user_kyc_country_map(user_ids)
        total_deal_mapper = cls._get_user_time_range_trade_summary(user_ids)
        online_data = cls.get_merchant_online_data(user_id)
        kyc_country = cls.countries.get(kyc_mapper.get(user_id, ""))

        m_model = P2pUserMargin
        margin_row = m_model.query.filter(
            m_model.user_id == user_id
        ).first()

        p2p_relation_manager = P2pRelationManager(user_id)
        all_block_target_data = p2p_relation_manager.all_block_target_data()
        all_follow_target_data = p2p_relation_manager.all_follow_target_data()
        relation_user_ids = list(all_block_target_data.keys()) + list(all_follow_target_data.keys())
        relation_user_map = {
            user.id: dict(
                email=user.email,
                nickname=user.nickname,
            ) for user in User.query.filter(
                User.id.in_(relation_user_ids),
            ).all()
        }
        is_merchant_map = {
            p2p_user.user_id: p2p_user.is_merchant for p2p_user in P2pUser.query.filter(
                P2pUser.user_id.in_(relation_user_ids),
            ).all()
        }

        return dict(
            user_info=dict(
                email=user.email,
                nick_name=user.nickname,
                kyc_country=kyc_country,
                require_margin=margin_row.require_margin,
                paid_margin=margin_row.paid_margin,
                margin_type=margin_row.margin_type.name,
                grace_deadline=margin_row.grace_deadline,
                block_target_count=len(all_block_target_data),
                follow_target_count=len(all_follow_target_data),
                **merchant.to_dict(),
            ),
            trade_info=dict(
                **merchant_trade_mapper.get(user_id, {}),
                current_monthly_trade_amount=current_mapper.get(user_id, 0),
                last_monthly_trade_amount=last_mapper.get(user_id, 0),
                deal_amount=total_deal_mapper.get(user_id, 0),
                **online_data
            ),
            adv_list=cls.get_merchant_adv(user_id),
            pay_channels=cls.get_pay_channels(user_id),
            all_block_relation_list=cls._get_relation_data(all_block_target_data, relation_user_map, is_merchant_map),
            all_follow_relation_list=cls._get_relation_data(all_follow_target_data, relation_user_map, is_merchant_map),
            extr=dict(
                adv_status=AdminStatusEnum,
                adv_types=AdminAdvTypeEnum,
                user_status=cls.statues,
                shop_status=cls.shop_statues,
            )
        )

    @classmethod
    def _get_relation_data(
            cls,
            all_relation_data: dict[int, datetime],
            relation_user_map: dict[int, dict],
            is_merchant_map: dict[int, bool],
    ):
        all_relation_target_list = []
        for user_id, created_at in all_relation_data.items():
            relation_user_basic = relation_user_map.get(user_id, {})
            all_relation_target_list.append(dict(
                user_id=user_id,
                email=relation_user_basic.get('email', ''),
                nickname=relation_user_basic.get('nickname', ''),
                is_merchant=is_merchant_map.get(user_id, False),
                created_at=created_at,
            ))
        all_relation_target_list.sort(key=lambda x: x['created_at'], reverse=True)
        return all_relation_target_list


# 新增查看用户价格提醒的接口
@ns.route('/<int:id_>/price-notice')
@respond_with_code
class UserPriceNoticeResource(Resource):

    @classmethod
    def get(cls, id_):
        """查询用户价格提醒设置"""
        query = MarketPriceNotice.query.filter(
            MarketPriceNotice.user_id == id_,
            MarketPriceNotice.status == MarketPriceNotice.Status.VALID
        ).order_by(MarketPriceNotice.id.desc())

        spot_markets = set(MarketCache.list_online_markets())
        perpetual_markets = set(PerpetualMarketCache().get_market_list())
        new_rows = []
        for row in query:
            if row.trade_type == MarketPriceNotice.TradeType.SPOT and row.market in spot_markets:
                new_rows.append(row)
            elif row.trade_type == MarketPriceNotice.TradeType.PERPETUAL and row.market in perpetual_markets:
                new_rows.append(row)

        return [dict(
            id=row.id,
            market=row.market,
            value=amount_to_str(row.value),
            state=row.state.name,
            ttl_type=row.ttl_type.name,
            trade_type=row.trade_type.name,
            rule=row.rule.name,
            direction=MarketPriceNotice.RULE_MAP[row.rule.name]['direction'],
            notice_type=MarketPriceNotice.RULE_MAP[row.rule.name]['notice_type'],
        ) for row in new_rows]
