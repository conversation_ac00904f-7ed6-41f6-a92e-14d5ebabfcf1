# -*- coding: utf-8 -*-
from collections import defaultdict

from flask import g, request
from sqlalchemy import func, or_
from webargs import fields

from app import Language
from app.assets import list_all_chains, list_all_assets
from app.business import get_admin_user_name_map, WalletClient
from app.business.kyt import DepositRiskAssessmentManager, WithdrawalPreScreeningManager
from app.common import ADMIN_EXPORT_LIMIT, get_code_to_cn_name
from app.exceptions import InvalidArgument, RecordNotFound
from app.models import db, User
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import PageField, LimitField, EnumField, ChainField, TimestampField
from app.models.kyt import KYT<PERSON>hitelistUser, DepositSenderRiskAssessmentWhitelist, KytChainAssessor, \
    BlacklistedDepositSender, DepositSenderRiskAssessment, UserWithdrawalAddressPreScreening
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectWallet
from app.utils import RESTClient, export_xlsx, batch_iter
from app.utils.importer import get_table_rows

ns = Namespace('KYT')


@ns.route('/withdrawal-pre-screening')
@respond_with_code
class WithdrawalPreScreeningResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        asset=fields.String,
        chain=fields.String,
        address=fields.String,
        category_id=fields.Integer(allow_none=True),
        begin_at=TimestampField(is_ms=True),
        end_at=TimestampField(is_ms=True),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """KYT - 提现地址风险筛查记录-列表"""
        model = UserWithdrawalAddressPreScreening
        query = model.query
        if user_id := kwargs.get('user_id'):
            query = query.filter(model.user_id == user_id)
        if asset := kwargs.get('asset'):
            query = query.filter(model.asset == asset)
        if chain := kwargs.get('chain'):
            query = query.filter(model.chain == chain)
        if address := kwargs.get('address'):
            query = query.filter(model.address == address)
        if (category_id := kwargs.get('category_id')) is not None:
            if category_id == 0:
                query = query.filter(model.category_id.is_(None))
            else:
                query = query.filter(model.category_id == category_id)
        if begin_at := kwargs.get('begin_at'):
            query = query.filter(model.created_at >= begin_at)
        if end_at := kwargs.get('end_at'):
            query = query.filter(model.created_at <= end_at)
        records = query.order_by(
            model.id.desc()
        ).paginate(kwargs['page'], kwargs['limit'])
        user_ids = [x.user_id for x in records.items]
        user_infos = cls._get_user_infos(user_ids)
        ret = []
        for row in records.items:
            item = row.to_dict(enum_to_name=True)
            user_info = user_infos[row.user_id]
            item['user_email'] = user_info.get('email', row.user_id)
            item['country'] = user_info.get('country', '-')
            ret.append(item)
        psm = WithdrawalPreScreeningManager
        categories = psm.list_all_categories()
        categories = {0: 'low risk'} | categories
        return dict(
            items=ret,
            total=records.total,
            extra=dict(
                assets=list_all_assets(),
                chains=list_all_chains(),
                risk_agreements=UserWithdrawalAddressPreScreening.RiskAgreement,
                categories=categories,
                configurable_categories=psm.list_configurable_categories(),
                selected_category_ids=psm.list_monitored_category_ids(),
            )
        )

    @classmethod
    def _get_user_infos(cls, user_ids: list[int]) -> dict:
        ret = defaultdict(dict)
        code2cn = get_code_to_cn_name()
        for ids_ in batch_iter(user_ids, 1000):
            rows = User.query.with_entities(
                User.id,
                User.email,
                User.location_code,
            ).filter(
                User.id.in_(ids_)
            ).all()
            for row in rows:
                ret[row.id] = {
                    'email': row.email,
                    'location_code': row.location_code,
                    'country': code2cn.get(row.location_code, '-'),
                }
        return ret


@ns.route('/withdrawal-pre-screening/<int:id_>')
@respond_with_code
class WithdrawalPreScreeningDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String(required=True)
    ))
    def patch(cls, id_, **kwargs):
        """KYT - 提现地址风险筛查记录-备注"""
        model = UserWithdrawalAddressPreScreening
        row = model.query.get(id_)
        if row is None:
            raise RecordNotFound
        row.remark = kwargs['remark']
        db.session.commit()
        return {}


@ns.route('/deposit-sender-risk-assessments')
@respond_with_code
class DepositSenderRiskAssessmentsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ids=fields.DelimitedList(fields.Integer()),
        deposit_id=fields.Integer(),
        chain=ChainField,
        assess_object=fields.String,
        status=EnumField(DepositSenderRiskAssessment.Status),
        assessor=fields.String,
        audit_status=EnumField(DepositSenderRiskAssessment.AuditStatus),
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """Web KYT 评估 - 查看列表"""
        query = DepositSenderRiskAssessment.query
        if ids := kwargs.get('ids'):
            query = query.filter(DepositSenderRiskAssessment.id.in_(ids))
        if deposit_id := kwargs.get('deposit_id'):
            query = query.filter(DepositSenderRiskAssessment.deposit_id == deposit_id)
        if chain := kwargs.get('chain'):
            query = query.filter_by(chain=chain)
            if assess_object := kwargs.get('assess_object'):
                query = cls._filter_rows_by_assess_object(
                    query, chain, assess_object)
        if (status := kwargs.get('status')) is not None:
            query = query.filter_by(status=status)
            if (audit_status := kwargs.get('audit_status')) is not None:
                query = query.filter_by(audit_status=audit_status)
        if assessor := kwargs.get('assessor'):
            query = query.filter_by(assessor=assessor)

        records = query \
            .order_by(DepositSenderRiskAssessment.id.desc()) \
            .paginate(kwargs['page'], kwargs['limit'])
        items = records.items
        whitelisted = cls._filter_whitelisted_rows(items)
        ret = []
        wallet_client = WalletClient()
        name_map = get_admin_user_name_map([x.auditor for x in items if x.auditor])
        for item in items:
            is_in_whitelist = item.id in whitelisted
            tmp = cls.explain_row(item, is_in_whitelist, wallet_client, name_map)
            ret.append(tmp)
        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.KYTRiskAssessment,
            detail=kwargs,
        )
        return dict(
            total=records.total,
            items=ret,
            extra=dict(
                chains=list_all_chains(),
                statuses=DepositSenderRiskAssessment.Status,
                audit_statuses=DepositSenderRiskAssessment.AuditStatus,
                assessors=[assessor.name for assessor in DepositRiskAssessmentManager.list_assessors()],
                reasons=DepositSenderRiskAssessment.Reason,
            )
        )

    @classmethod
    def _filter_rows_by_assess_object(
            cls, query: db.Query, chain: str, assess_object: str) -> db.Query:
        splitter = DepositSenderRiskAssessment.SPLITTER
        binary = func.binary
        # 对于 chain 配置的服务商可能变化，此时寻找 chain 的服务商没有意义
        result = assess_object.split(splitter)
        # full match
        if len(result) >= 2:
            return query.filter_by(assessed_object=binary(assess_object))
        result = result[0]
        search_objects = [
            f'{result}{splitter}',
            f'{splitter}{result}',
            f'{result}',
        ]
        return query.filter(
            or_(
                func.left(
                    DepositSenderRiskAssessment.assessed_object,
                    func.length(search_objects[0]),
                ) == binary(search_objects[0]),
                func.right(
                    DepositSenderRiskAssessment.assessed_object,
                    func.length(search_objects[1]),
                ) == binary(search_objects[1]),
                DepositSenderRiskAssessment.assessed_object == binary(search_objects[2]),
            )
        )

    @classmethod
    def explain_row(
            cls,
            row: DepositSenderRiskAssessment,
            is_in_whitelist: bool = None,
            wallet_client: WalletClient = None,
            name_map: dict = None
    ) -> dict:
        wallet_client = wallet_client or WalletClient()
        name_map = name_map or {}
        r_dict = row.to_dict(enum_to_name=True)
        if row.assess_object_type is row.AssessObjectType.ADDRESS:
            r_dict.update({
                'assess_object_url': wallet_client.get_explorer_address_url(row.chain, row.assessed_object),
                'is_address_type': True,
            })
        else:
            tx_id = row.assessed_object.split(row.SPLITTER)[0]
            r_dict.update({
                'assess_object_url': wallet_client.get_explorer_tx_url(row.chain, tx_id),
                'is_address_type': False,
            })
        assessor = row.assessor
        details = row.details
        m = DepositRiskAssessmentManager
        r_dict.update(
            details_previewed=m.preview_assessment_details(assessor, details),
            details_explained=m.explain_assessment_details(assessor, details)
        )
        if is_in_whitelist is None:
            is_in_whitelist = row.id in cls._filter_whitelisted_rows([row])
        r_dict['is_in_whitelist'] = is_in_whitelist
        auditor_name = name_map.get(row.auditor, row.auditor)
        r_dict['auditor_name'] = auditor_name
        return r_dict

    @classmethod
    def _filter_whitelisted_rows(cls,
                                rows: list[DepositSenderRiskAssessment]
                                ) -> set[int]:
        ac2row = {(row.chain, row.assessed_object): row.id for row in rows}
        return {
            ac2row[ac] for ac
            in DepositRiskAssessmentManager.filter_senders_in_whitelist(ac2row)
        }


@ns.route('/whitelist-user')
@respond_with_code
class WhiteListUsersResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """KYT - 充值风险评估白名单用户-列表"""
        model = KYTWhitelistUser
        query = model.query.filter(model.status == model.Status.VALID)
        if user_id := kwargs.get('user_id'):
            query = query.filter(model.user_id == user_id)
        records = query.order_by(
            model.id.desc()
        ).paginate(kwargs['page'], kwargs['limit'])
        operators = [x.operator for x in records.items if x.operator]
        user_ids = [x.user_id for x in records.items]
        admin_name_map = get_admin_user_name_map(operators + user_ids)
        ret = []
        for row in records.items:
            item = row.to_dict()
            item['user_email'] = admin_name_map.get(row.user_id, row.user_id)
            item.update(
                {
                    "operator": row.operator,
                    "operator_email": admin_name_map.get(row.operator, row.operator),
                }
            )
            ret.append(item)
        return dict(
            items=ret,
            total=records.total
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_ids=fields.List(fields.Integer, required=True),
        remark=fields.String(missing='')
    ))
    def post(cls, **kwargs):
        """KYT - 充值风险评估白名单用户-添加"""
        user_ids = list(set(kwargs['user_ids']))
        model = KYTWhitelistUser
        pending_objs = []
        for user_id in user_ids:
            row = model.get_or_create(user_id=user_id)
            if row.status is model.Status.VALID:
                raise InvalidArgument(message=f'user_id: {user_id} already exists!')
            row.status = model.Status.VALID
            row.remark = kwargs['remark']
            row.operator = g.user.id
            pending_objs.append(row)
        db.session.add_all(pending_objs)
        db.session.commit()

        for obj in pending_objs:
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.KYTWhitelistUser,
                detail=dict(
                    id=obj.id,
                    user_id=obj.user_id,
                    remark=obj.remark,
                    operator=g.user.id
                ),
            )
        return {}


# noinspection PyUnresolvedReferences
@ns.route('/whitelist-user/<int:id_>')
@respond_with_code
class WhiteListUserResource(Resource):

    @classmethod
    def delete(cls, id_):
        """KYT - 充值风险评估白名单用户-删除"""
        model = KYTWhitelistUser
        row: model = model.query.filter(
            model.id == id_,
            model.status == model.Status.VALID
        ).first()
        if row is not None:
            row.status = model.Status.DELETED
            row.operator = g.user.id
            db.session.commit()
            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.KYTWhitelistUser,
                detail=dict(
                    id=row.id,
                    user_id=row.user_id,
                    operator=g.user.id
                ),
            )
        return {}


@ns.route('/deposit-sender-risk-assessment-whitelist')
@respond_with_code
class DepositSenderRiskAssessmentWhitelistResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        chain=ChainField,
        address=fields.String,
        status=EnumField(DepositSenderRiskAssessmentWhitelist.Status),
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """KYT 充值风险评估白名单 from 地址 - 查看列表"""
        model = DepositSenderRiskAssessmentWhitelist
        query = model.query.filter(model.status == model.Status.VALID)
        if chain := kwargs.get('chain'):
            query = query.filter_by(chain=chain)
            if address := kwargs.get('address'):  # 这里应该无需 normalise_address
                query = query.filter_by(address=address)
        if status := kwargs.get('status'):
            query = query.filter_by(status=status)

        records = query.order_by(model.id.desc()).paginate(kwargs['page'], kwargs['limit'])
        name_map = get_admin_user_name_map([x.operator for x in records.items])
        items = []
        wa_client = WalletClient()
        for row in records.items:
            item = row.to_dict()
            explorer_address_url = wa_client.get_explorer_address_url(row.chain, row.address)
            item['address_url'] = explorer_address_url
            item['operator_name'] = name_map.get(row.operator, row.operator)
            items.append(item)

        return dict(
            total=records.total,
            items=items,
            extra=dict(
                chains=list_all_chains(),
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        chain=ChainField(required=True),
        addresses=fields.List(fields.String(), required=True),
        reason=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """KYT 充值风险评估白名单 from 地址 - 添加"""
        chain = kwargs['chain']
        addresses = kwargs['addresses']
        reason = kwargs['reason']
        return cls.update_whitelist_addresses(chain, addresses, reason)

    @classmethod
    def update_whitelist_addresses(cls, chain: str, addresses: list[str], reason: str):
        if not cls.is_allowed(chain):
            raise InvalidArgument(message=f'chain {chain} is not allowed')

        cls.validate_addresses([(chain, a) for a in addresses])

        model = DepositSenderRiskAssessmentWhitelist
        rows = model.query.filter(
            model.chain == chain,
            model.address.in_([func.binary(address) for address in addresses])
        ).all()
        normalised_addresses = WalletClient().normalise_addresses([(chain, address) for address in addresses])
        # TODO: raise error if result's bool(address) is False
        to_creates = {(c, a) for c, a in normalised_addresses.items() if a} - {(row.chain, row.address) for row in rows}
        for c, a in to_creates:
            obj = model(
                chain=c,
                address=a,
                reason=reason,
                operator=g.user.id,
            )
            db.session.add(obj)
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.KYTFromAddressWhitelist,
                detail=dict(
                    id=obj.id,
                    chain=obj.chain,
                    address=obj.address,
                    reason=obj.reason,
                    operator=g.user.id
                ),
            )
        for row in rows:
            old_data = row.to_dict(enum_to_name=True)
            row.status = model.Status.VALID
            row.reason = reason
            row.operator = g.user.id
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.KYTFromAddressWhitelist,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )
        db.session.commit()

    @classmethod
    def is_allowed(cls, chain) -> bool:
        # 这里按理说不应该校验 chain 是否配置 KYT assessor
        # 也不用关心 assessor 是否支持地址
        model = KytChainAssessor
        record: model = model.query.filter(
            model.chain == chain
        ).first()
        if not record or not record.assessor:
            return False
        return True

    @classmethod
    def validate_addresses(cls, addresses: list[tuple[str, str]]):
        invalid = []
        client = WalletClient()
        for chain, address in addresses:
            valid, _, _ = client.validate_address_info(chain, address)
            if not valid:
                invalid.append((chain, address))
        if invalid:
            raise InvalidArgument(
                message=f'the following addresses are invalid: '
                        f'{", ".join(f"{a}({c})" for c, a in invalid)}')


@ns.route('/deposit-sender-risk-assessment-whitelist/<int:id_>')
@respond_with_code
class DepositSenderRiskAssessmentWhitelistItemResource(Resource):

    @classmethod
    def delete(cls, id_):
        """KYT 充值风险评估白名单 from 地址 - 删除单条"""
        row = cls._get_row(id_)
        model = DepositSenderRiskAssessmentWhitelist
        row.status = model.Status.DELETED
        db.session.commit()
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.KYTFromAddressWhitelist,
            detail=dict(id=id_, chain=row.chain, address=row.address),
        )


    @classmethod
    def _get_row(cls, id_):
        model = DepositSenderRiskAssessmentWhitelist
        row = model.query.get(id_)
        if row is None or row.status is not model.Status.VALID:
            raise RecordNotFound
        return row


@ns.route('/blacklisted-deposit-senders')
@respond_with_code
class BlacklistedDepositSendersResource(Resource):
    export_headers = (
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "address", Language.ZH_HANS_CN: "地址"},
        {"field": "operator_name", Language.ZH_HANS_CN: "操作人"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        chain=ChainField,
        dynamic=fields.Boolean(missing=False),
        address=fields.String,
        page=PageField,
        limit=LimitField,
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """Web 充值黑名单 - 获取列表"""
        model = BlacklistedDepositSender
        extra = dict(
            chains=list_all_chains(),
        )
        query = model.query.filter(model.status == model.Status.VALID)
        if chain := kwargs.get('chain'):
            dynamic = kwargs.get('dynamic')
            if dynamic:
                sndrs = list(cls.get_dynamic_blacklisted_deposit_senders(chain))
                return dict(
                    items=[dict(
                        chain=chain,
                        address=a,
                        is_dynamic=True,
                        remark='动态生成, 不可编辑'
                    ) for a in sndrs],
                    total=len(sndrs),
                    extra=extra
                )
            query = query.filter(model.chain == chain)
        if address := kwargs.get('address'):
            query = query.filter(model.address == address)

        query = query.order_by(model.id.desc())
        if kwargs.get('export'):
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            records = query.paginate(kwargs['page'], kwargs['limit'])
            items = records.items
        name_map = get_admin_user_name_map([x.operator for x in items])
        ret = []
        for row in items:
            item = row.to_dict()
            explorer_address_url = WalletClient().get_explorer_address_url(row.chain, row.address)
            item['address_url'] = explorer_address_url
            item['operator_name'] = name_map.get(row.operator, row.operator)
            ret.append(item)
        if kwargs.get('export'):
            return export_xlsx(
                filename='blacklisted_deposit_senders_xlsx',
                data_list=ret,
                export_headers=cls.export_headers
            )
        return dict(
            items=ret,
            total=records.total,
            extra=extra
        )

    @classmethod
    def get_dynamic_blacklisted_deposit_senders(cls, chain) -> tuple:
        dynamic_chains = {'BSC': 'bsc', 'ERC20': 'eth', 'BTC': 'btc'}
        if chain not in dynamic_chains:
            return ()
        resp_key = dynamic_chains[chain]
        res = RESTClient('https://hackscan.hackbounty.io/public').get('hack-address.json')
        try:
            return res['0221'][resp_key]
        except KeyError:
            return ()

    @classmethod
    @ns.use_kwargs(dict(
        chain=ChainField(required=True),
        address=fields.String(required=True),
        remark=fields.String(missing='')
    ))
    def post(cls, **kwargs):
        """Web 充值黑名单 - 新增"""
        chain = kwargs['chain']
        address = kwargs['address']
        address = DepositRiskAssessmentManager.normalise_address(chain, address)
        model = BlacklistedDepositSender
        row: model = model.query.filter(
            model.chain == chain,
            model.address == func.binary(address)
        ).first()
        if row is None:
            row = model(chain=chain, address=address)
            db.session.add(row)
        row.operator = g.user.id
        row.status = model.Status.VALID
        row.remark = kwargs.get('remark')
        db.session.commit()
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.KYTFromAddressBlacklist,
            detail=dict(
                id=row.id,
                chain=row.chain,
                address=row.address,
                remark=row.remark,
                operator=g.user.id
            ),
        )
        return row

    @classmethod
    @ns.use_kwargs(dict(
        ids=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """Web 充值黑名单 - 批量删除"""
        ids = [int(x) for x in kwargs['ids'].strip().split(',')]
        if not ids:
            return
        model = BlacklistedDepositSender
        rows = model.query.filter(model.id.in_(ids)).all()
        for row in rows:
            row.status = model.Status.DELETED
        db.session.commit()
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.KYTFromAddressBlacklist,
            detail=dict(ids=ids, addresses=[(x.chain, x.address) for x in rows]),
        )


@ns.route('/blacklisted-deposit-senders/<int:id_>')
@respond_with_code
class BlacklistedDepositSenderResource(Resource):

    @classmethod
    def get(cls, id_):
        """Web 充值黑名单 - 获取单个"""
        return cls._get_row(id_)

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String
    ))
    def patch(cls, id_, **kwargs):
        """Web 充值黑名单 - 修改单个"""
        row: BlacklistedDepositSender = cls._get_row(id_)
        old_data = row.to_dict(enum_to_name=True)
        if (remark := kwargs.get('remark')) is not None:
            row.remark = remark
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.KYTFromAddressBlacklist,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return row

    @classmethod
    def _get_row(cls, id_):
        row: BlacklistedDepositSender = BlacklistedDepositSender.query.get(id_)
        if row is None or row.status is not BlacklistedDepositSender.Status.VALID:
            raise RecordNotFound
        return row

    @classmethod
    def delete(cls, id_):
        """Web 充值黑名单 - 删除单个"""
        row: BlacklistedDepositSender = BlacklistedDepositSender.query.get(id_)
        if row is None:
            raise RecordNotFound
        row.status = BlacklistedDepositSender.Status.DELETED
        db.session.commit()
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.KYTFromAddressBlacklist,
            detail=dict(id=id_, chain=row.chain, address=row.address),
        )


@ns.route('/blacklisted-deposit-senders/batch-upload')
@respond_with_code
class BatchUploadBlacklistedDepositSenderResource(Resource):

    @classmethod
    def post(cls):
        """Web 充值黑名单 - 批量上传"""
        if not (file := request.files.get('file')):
            raise InvalidArgument
        file_columns = ["chain", "address", "operator", "remark"]
        rows = get_table_rows(file, file_columns)
        data = []
        chains = list_all_chains()
        for row in rows:
            chain = row['chain']
            address = row['address']
            if not chain or not address:
                raise InvalidArgument(message='chain or address is required')
            if chain not in chains:
                raise InvalidArgument(message='chain does not exist')
            address = DepositRiskAssessmentManager.normalise_address(chain, address)
            remark = row.get('remark') or ''
            data.append({'chain': chain, 'address': address, 'remark': remark})

        model = BlacklistedDepositSender
        log_data = []
        for d in data:
            chain, address = d['chain'], d['address']
            row: model = model.query.filter(
                model.chain == chain,
                model.address == func.binary(address)
            ).first()
            if row is None:
                old_data = None
                row = model(chain=chain, address=address)
                db.session.add(row)
            else:
                old_data = row.to_dict(enum_to_name=True)
            row.operator = g.user.id
            row.status = model.Status.VALID
            row.remark = d['remark']
            log_data.append((old_data, row))
        db.session.commit()
        for old_data, row in log_data:
            AdminOperationLog.new_add_or_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.KYTFromAddressBlacklist,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )


KytSettings = DepositRiskAssessmentManager.Settings


@ns.route('/settings')
@respond_with_code
class KytSettingsAdminResource(Resource):

    @classmethod
    def get(cls):
        """Web KYT 设置 - 查看所有"""
        return KytSettings.fields_and_values_json


@ns.route('/settings/<field>')
@respond_with_code
class ChainConfigManagementAdminResource(Resource):

    @classmethod
    def get(cls, field):
        """Web KYT 设置 - 查看单项"""
        try:
            return KytSettings.get_field_and_value_json(field)
        except AttributeError:
            raise InvalidArgument(message=f'field {field!r} does not exist')

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True)
    ))
    def put(cls, field, **kwargs):
        """Web KYT 设置 - 修改单项"""
        value = kwargs['value']
        try:
            old_data = {field: getattr(KytSettings, field)}
            setattr(KytSettings, field, value)
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.KYTSettings,
                old_data=old_data,
                new_data={field: getattr(KytSettings, field)},
            )
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        return dict(
            value=getattr(KytSettings, field)
        )

    @classmethod
    def delete(cls, field):
        """KYT 设置 - 重置单项"""
        try:
            old_data = {field: getattr(KytSettings, field)}
            delattr(KytSettings, field)
            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.KYTSettings,
                detail={
                    'old_data': old_data,
                    'new_data': {field: getattr(KytSettings, field)},
                },
            )
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        return dict(
            value=getattr(KytSettings, field)
        )


@ns.route('/chain-assessors')
@respond_with_code
class ChainAssessorsAdminResource(Resource):

    @classmethod
    def get(cls):
        """Web KYT 服务商 - 查看列表 """
        result = [
            {
                'chain': item.chain,
                'assessor': item.assessor,
            }
            for item in KytChainAssessor.query.filter(
                KytChainAssessor.assessor.isnot(None)
            ).order_by(KytChainAssessor.chain)
        ]
        configured_chains = {item['chain'] for item in result}
        all_chains = list_all_chains()
        result.extend([
            {
                'chain': chain,
                'assessor': None,
            }
            for chain in sorted(set(all_chains) - configured_chains)
        ])
        return dict(
            items=result,
            chains=all_chains,
            assessors=[
                assessor.name
                for assessor in DepositRiskAssessmentManager.list_assessors()
            ],
        )


@ns.route('/chain-assessors/<chain>')
@respond_with_code
class SingleChainAssessorAdminResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        assessor_name=fields.String,
    ))
    def put(cls, chain, **kwargs):
        """Web KYT 服务商 - 修改链配置"""
        if chain not in list_all_chains():
            raise InvalidArgument(message=f'chain {chain!r} does not exist')
        assessors = [
            item.name
            for item in DepositRiskAssessmentManager.list_assessors()
        ]
        if assessor_name := kwargs.get('assessor_name'):
            if assessor_name not in assessors:
                raise InvalidArgument(
                    message=f'assessor {assessor_name!r} does not exist')
            assessor = DepositRiskAssessmentManager.get_assessor(assessor_name)
            if not assessor.is_chain_supported(chain):
                raise InvalidArgument(
                    message=f'该服务商 {assessor_name} 不支持这条公链 {chain}')
        chain_assessor: KytChainAssessor = KytChainAssessor.query.filter(
            KytChainAssessor.chain == chain
        ).first()
        if chain_assessor is None:
            old_data = None
            chain_assessor = KytChainAssessor(
                chain=chain,
            )
            db.session.add(chain_assessor)
        else:
            old_data = chain_assessor.to_dict(enum_to_name=True)
        chain_assessor.assessor = assessor_name if assessor_name else None
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.KYTChainAssessor,
            old_data=old_data,
            new_data=chain_assessor.to_dict(enum_to_name=True),
        )
