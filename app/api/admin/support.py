import io
from collections import defaultdict

import requests
from flask import request, g, send_file
from webargs import fields

from app import Language
from app.api.common import respond_with_code, Namespace, Resource
from app.api.common.fields import <PERSON><PERSON>ield, LimitField
from app.business.auth import get_admin_user_name_map
from app.business.user import UserRepository
from app.business.activity.anti_fraud import AntiFraudHelper
from app.business.clients.anti_fraud import AntiFraudClient
from app.exceptions import InvalidArgument, ServiceUnavailable
from app.models import db, UserEmailCheck, User, UserEmailCheckDetail, UserMaskIdTransfer, MaskUser
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from app.utils import validate_email, batch_iter, export_xlsx, AWSBucketPrivate, \
    ExcelExporter
from app.utils.importer import get_table_rows

ns = Namespace('User Support')


@ns.route('/user-check/template')
@respond_with_code
class UserEmailCheckTemplateResource(Resource):

    export_headers = (
        {'field': 'email', Language.ZH_HANS_CN: '邮箱', Language.EN_US: 'Email'},
        {'field': 'activity_id', Language.ZH_HANS_CN: '活动 ID(选填，用activity_id)', Language.EN_US: 'Activity ID'}
    )

    @classmethod
    def get(cls):
        return export_xlsx(
            filename='user-email-check-template',
            data_list=[],
            export_headers=cls.export_headers
        )


@ns.route('/user-check')
@respond_with_code
class UserCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(missing=1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """用户-用户验证-列表展示"""
        query = UserEmailCheck.query.order_by(UserEmailCheck.id.desc())
        page = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        user_ids = {obj.admin_user_id for obj in page.items}
        name_map = get_admin_user_name_map(user_ids)

        items = []
        for item in page.items:
            tmp = item.to_dict()
            tmp['operator'] = name_map.get(item.admin_user_id) or '--'
            items.append(tmp)
        return dict(items=items, total=page.total,)

    @classmethod
    def post(cls):
        """用户-用户验证-批量上传"""
        if not (title := request.form.get('title')):
            raise InvalidArgument

        file_ = request.files.get('batch-upload')
        file_columns = ["email", "activity_id"]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)

        validated_rows = []
        for idx, row in enumerate(rows):
            email = str(row['email'])
            if not validate_email(email):
                raise InvalidArgument(
                    message=f'文件内第{idx + 2}行有非法的email格式或email长度超过64，请修改后重新提交！')

            activity_id = row.get('activity_id')
            if activity_id and (not str(activity_id).isnumeric() or int(activity_id) <= 0):
                raise InvalidArgument(
                    message=f'文件内第{idx + 2}行有非法的activity_id，请修改后重新提交！')

            validated_rows.append(dict(email=email, activity_id=int(activity_id) if activity_id else None))

        user_emails = {}
        for chunk_emails in batch_iter([item["email"] for item in validated_rows], 500):
            chunk_user_emails = User.query.with_entities(User.id, User.email).filter(
                User.email.in_([email.lower() for email in chunk_emails])
            ).all()
            for user in chunk_user_emails:
                email = user.email
                if email:
                    email = email.lower()
                user_emails.update({email: user.id})

        # 批量查询是否羊毛党    
        client, anti_fraud_users = AntiFraudClient(), defaultdict(bool)
        for chunk_user_ids in batch_iter([user_id for _, user_id in user_emails.items()], 100):
            chunk_anti_fraud_users = client.batch_get_risk_users(user_ids=chunk_user_ids)
            for item in chunk_anti_fraud_users["items"]:
                anti_fraud_users[item["user_id"]] = True

        # 批量查询anti_fraud_data
        anti_fraud_data_map = AntiFraudHelper.batch_get_anti_fraud_data(
            [item["activity_id"] for item in validated_rows if item["activity_id"]],
        )
            
        detail = []
        cleard_users = UserRepository.get_cleared_user_ids(list(user_emails.values()))
        for item in validated_rows:
            email = item['email']
            activity_id = item['activity_id']
            
            if email.lower() in user_emails:
                user_id = user_emails[email.lower()]
                is_cleared = True if user_id in cleard_users else False
                
                # 添加anti_fraud_data
                anti_fraud_data = {}
                if activity_id and anti_fraud_data_map.get(int(activity_id)):
                    anti_fraud_data = anti_fraud_data_map[int(activity_id)].get(user_id, {})
                
                detail.append({
                    'email': email,
                    'is_coinex': True,
                    'user_id': user_id,
                    'is_cleared': is_cleared,
                    'is_fraud': anti_fraud_users.get(user_id, False),
                    'activity_id': activity_id,
                    'anti_fraud_data': anti_fraud_data
                })
            else:
                detail.append({
                    'email': email, 
                    'is_coinex': False, 
                    'is_cleared': None,
                    'is_fraud': False,
                    'activity_id': activity_id,
                    'anti_fraud_data': {}
                })

        obj = UserEmailCheck(title=title, admin_user_id=g.user.id)
        db.session.add(obj)
        db.session.commit()
        detail_pending_objs = []
        for kwargs in detail:
            kwargs.update(ref_id=obj.id)
            detail_pending_objs.append(UserEmailCheckDetail(**kwargs))

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.UserCheck,
                detail=kwargs,
                target_user_id=kwargs.get('user_id'),
            )

        db.session.add_all(detail_pending_objs)
        db.session.commit()
        return dict(count=len(rows))

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def delete(cls, **kwargs):
        """用户-用户验证-删除"""
        id_ = kwargs['id']
        obj = UserEmailCheck.query.get(id_)
        if not obj:
            raise InvalidArgument
        detail = dict(id=id_, title=obj.title)

        cascade_query = UserEmailCheckDetail.query.filter(UserEmailCheckDetail.ref_id == id_)
        cascade_query.delete(synchronize_session=False)
        db.session.delete(obj)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.UserCheck,
            detail=detail,
        )


@ns.route('/user-check/export')
@respond_with_code
class UserCheckExportResource(Resource):
   
    HEADERS = (
        {'field': 'email', Language.ZH_HANS_CN: '邮箱'},
        {'field': 'is_coinex', Language.ZH_HANS_CN: '是否为 CoinEx 用户'},
        {'field': 'is_cleared', Language.ZH_HANS_CN: '是否为清退用户'},
        {'field': 'user_id', Language.ZH_HANS_CN: '用户 ID'},
        {'field': 'is_fraud', Language.ZH_HANS_CN: '是否为黑名单用户'},
    )

    ADDITIONAL_FRAUD_HEADERS = (
        {'field': 'email', Language.ZH_HANS_CN: '邮箱'},
        {'field': 'is_coinex', Language.ZH_HANS_CN: '是否为 CoinEx 用户'},
        {'field': 'is_cleared', Language.ZH_HANS_CN: '是否为清退用户'},
        {'field': 'user_id', Language.ZH_HANS_CN: '用户 ID'},
        {'field': 'is_fraud', Language.ZH_HANS_CN: '是否为黑名单用户'},
        {'field': 'activity_id', Language.ZH_HANS_CN: '活动 ID'},
        {'field': 'anti_fraud_score', Language.ZH_HANS_CN: '羊毛得分'},
        {'field': 'anti_fraud_detail', Language.ZH_HANS_CN: '得分详情'},
        {'field': 'similar_emails', Language.ZH_HANS_CN: '相似邮箱'},
   )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """用户-用户验证-导出"""
        id_ = kwargs['id']
        obj = UserEmailCheck.query.get(id_)
        if not obj:
            raise InvalidArgument

        cascade_query = UserEmailCheckDetail.query.filter(UserEmailCheckDetail.ref_id == id_).order_by(
            UserEmailCheckDetail.id.asc()
        )
        has_activity, export_data = False, []
        for obj in cascade_query.all():
            is_coinex = f'是' if obj.is_coinex else '否'
            if obj.is_cleared:
                is_cleared = '是'
            elif obj.is_cleared is False:
                is_cleared = '否'
            else:
                is_cleared = None

            if obj.activity_id:
                has_activity = True

            data = {
                'email': obj.email,
                'user_id': obj.user_id,
                'is_coinex': is_coinex,
                'is_cleared': is_cleared,
                'is_fraud': f'是' if obj.is_fraud else '否',
                'activity_id': obj.activity_id,
            }
            if (anti_fraud_data := obj.load_anti_fraud_data()):
                data.update(anti_fraud_data)

            export_data.append(data)

        if has_activity:
            headers = cls.ADDITIONAL_FRAUD_HEADERS
        else: 
            headers = cls.HEADERS
        return export_xlsx('user_check-export', export_data, headers)


@ns.route('/user-check/mask-id-transfer/template')
@respond_with_code
class UserMaskIdTransferTemplateResource(Resource):

    export_headers = (
        {'field': 'CUID', Language.ZH_HANS_CN: 'CUID'},
    )

    @classmethod
    def get(cls):
        return export_xlsx(
            filename='user-cuid-template',
            data_list=[],
            export_headers=cls.export_headers
        )


@ns.route('/user-check/mask-id-transfer')
@respond_with_code
class UserMaskIdTransferResource(Resource):
    model = UserMaskIdTransfer

    export_headers = (
        {'field': 'CUID', Language.ZH_HANS_CN: 'CUID'},
        {'field': 'USER_ID', Language.ZH_HANS_CN: 'USER_ID'},
    )

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(missing=1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """用户-用户验证-CUID列表"""
        query = cls.model.query.order_by(cls.model.id.desc())
        page = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        user_ids = {obj.admin_user_id for obj in page.items}
        name_map = get_admin_user_name_map(user_ids)

        items = []
        for item in page.items:
            tmp = item.to_dict()
            tmp['operator'] = name_map.get(item.admin_user_id) or '--'
            items.append(tmp)
        return dict(items=items, total=page.total)

    @classmethod
    def post(cls):
        """用户-用户验证-CUID上传"""
        if not (title := request.form.get('title')):
            raise InvalidArgument

        file_ = request.files.get('file')
        file_columns = ["CUID"]
        try:
            rows = get_table_rows(file_, file_columns, parse_str=True)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)

        mask_ids = list(set(i["CUID"] for i in rows))
        model = MaskUser
        mask_rows = model.query.filter(
            model.mask_id.in_(mask_ids)
        ).with_entities(
            model.user_id,
            model.mask_id,
        ).all()
        if not mask_rows:
            raise InvalidArgument(message="CUID 映射结果为空")

        mask_user_map = {i.mask_id: i.user_id for i in mask_rows}
        data = [{
            "CUID": row["CUID"],
            "USER_ID": mask_user_map.get(row["CUID"], "")
        } for row in rows]
        header = list(data[0].keys())
        export = ExcelExporter(
            data_list=list(data),
            fields=header,
            headers=header,
        )
        streams = export.export_streams()

        file_type = file_.filename.split(".")[-1].lower()
        file_key = AWSBucketPrivate.new_file_key(suffix=file_type)
        if not AWSBucketPrivate.put_file(file_key, streams):
            raise ServiceUnavailable

        row = UserMaskIdTransfer(
            title=title,
            admin_user_id=g.user.id,
            file_key=file_key
        )
        db.session_add_and_commit(row)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.MaskIdTransfer,
            detail=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def delete(cls, **kwargs):
        """用户-用户验证-CUID删除"""
        id_ = kwargs['id']
        obj = cls.model.query.get(id_)
        if not obj:
            raise InvalidArgument
        detail = obj.to_dict(enum_to_name=True)
        db.session.delete(obj)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.MaskIdTransfer,
            detail=detail,
        )


@ns.route('/user-check/mask-id-transfer/export')
@respond_with_code
class UserMaskIdTransferExportResource(Resource):
    model = UserMaskIdTransfer

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """用户-用户验证-CUID导出"""
        id_ = kwargs['id']
        obj = cls.model.query.get(id_)
        if not obj:
            raise InvalidArgument

        url = AWSBucketPrivate.get_file_url(obj.file_key)
        resp = requests.get(url)
        return send_file(
            io.BytesIO(resp.content),
            download_name=f'user-cuid-result.xlsx',
            as_attachment=True,
        )
