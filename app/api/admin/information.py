import json
import requests

from urllib import parse

from webargs import fields

from flask import g
from flask import request
from flask import make_response

from app import config

from app.api.common import Resource
from app.api.common import Namespace
from app.api.common import respond_with_code
from app.api.common.fields import <PERSON>um<PERSON>ield
from app.api.common.fields import <PERSON>tamp<PERSON>ield

from app.common import Language

from app.models import db
from app.models import AppPush
from app.models import AppPushContent

from app.business.push_statistic import UserTagGroupBiz
from app.business.push_statistic import app_push_user_statistic

from app.utils import g_map
from app.utils import RESTClient
from app.utils.net import get_url_base
from app.utils.push import AppPagePath
from app.utils.translate import GoogleTranslate

from app.exceptions import InvalidArgument

ns = Namespace('Information')

url_base = get_url_base(config['CLIENT_CONFIGS']['information_internal']['url'])
_client = RESTClient(url_base)


def _res(res: dict) -> dict:
    if res['code'] == 0:
        return res['data']
    raise InvalidArgument(message=res['message'])


def get_res(url: str, params: dict = None) -> dict:
    if params is None:
        params = {}
    res = _client.get(url, **params)
    return _res(res)


@ns.route('/push')
@respond_with_code
class PushResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.String(required=True),
        name=fields.String(required=True),
        push_type=EnumField(AppPush.PushType, default=AppPush.PushType.INFORMATION),
        push_time=TimestampField(required=True),
        remark=fields.String(required=False),
        ttl=fields.Integer(required=False),
    ))
    def post(cls, **kwargs):
        """资讯-推送PUSH"""
        information_id = kwargs['id']

        information_data = get_res(f'/admin/information/push/{information_id}')
        if len(information_data['contents']) == 0:
            raise InvalidArgument(message='information is empty')

        if information_data['information_type'] == 'news':
            jump_id = config['INFORMATION_PUSH']['news_jump_id']
            url = AppPagePath.QUICK_NEWS_DETAIL.value.format(id=information_id)
        else:
            jump_id = config['INFORMATION_PUSH']['article_jump_id']
            url = AppPagePath.ARTICLE_DETAIL.value.format(id=information_id)

        name = kwargs['name']
        push_type = kwargs['push_type']
        push_time = kwargs['push_time']
        remark = kwargs.get('remark') or ''
        ttl = kwargs.get('ttl') or None

        if name == '':
            raise InvalidArgument(message='推送名称不能为空')

        groups = config['INFORMATION_PUSH']['group_id']
        if isinstance(groups, int):
            groups = [groups]
        groups = UserTagGroupBiz.filter_tag_group_ids(ids=groups)
        groups = json.dumps(groups) if groups else ''

        app_push = db.session_add_and_flush(AppPush(
            created_by=g.user.id,
            name=name,
            push_type=push_type,
            push_time=push_time,
            remark=remark,
            user_type=AppPush.UserType.TARGET_USER,
            ttl=ttl,
            groups=groups,
            jump_id=jump_id,
            status=AppPush.Status.CREATED,
        ))

        for information_content in information_data['contents']:
            db.session.add(AppPushContent(
                app_push_id=app_push.id,
                lang=Language(information_content['lang']),
                title=information_content['title'],
                url=url,
                content=information_content['abstract']
            ))

        db.session.commit()

        app_push_user_statistic.delay(app_push.id)

        return dict(
            push_id=app_push.id,
        )


@ns.route('/translate')
@respond_with_code
class TranslateResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        source_lang=fields.String(required=True),
        target_langs=fields.List(fields.String(required=True)),
        title=fields.String(required=True),
        abstract=fields.String(missing=''),
        content=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """资讯-翻译"""
        source_lang = Language[kwargs['source_lang'].upper()]
        target_langs = [Language[item.upper()] for item in kwargs['target_langs']]
        title = kwargs['title']
        abstract = kwargs.get('abstract') or ''
        content = kwargs['content']

        api_key = config['GOOGLE_TRANSLATION_CONFIG']['api_key']

        data = []
        length = len(target_langs)
        for result in g_map(
                cls.translate,
                [api_key] * length,
                [source_lang] * length,
                target_langs,
                [title] * length,
                [abstract] * length,
                [content] * length,
                size=length
        ):
            data.append(result)

        return dict(
            data=data,
        )

    @classmethod
    def translate(
            cls, api_key: str, source_lang: Language, target_lang: Language,
            title: str, abstract: str, content: str
    ):
        translator = GoogleTranslate(api_key, source_lang, target_lang)
        translation_title = translator.translate(title)
        translation_abstract = translator.translate(abstract) if abstract else ''
        if '<' in content and '</' in content and '>' in content:
            translation_content = translator.translate_html(content)
        else:
            translation_content = translator.translate(content)
        return {
            'lang': target_lang.value,
            'title': translation_title,
            'abstract': translation_abstract,
            'content': translation_content,
        }


@ns.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
class RelayResource(Resource):
    HOP_BY_HOP_HEADERS = (
        'Host', 'Connection', 'Transfer-Encoding', 'Content-Encoding',
        'Content-Length', 'Authorization', 'Cookie', 'Set-Cookie',
    )

    session = requests.Session()

    def dispatch_request(self, path, *args, **kwargs):
        meth = getattr(self.session, request.method.lower())
        headers = self.remove_hop_by_hop_headers(request.headers)
        headers['User-Id'] = str(g.user.id)
        url = parse.urljoin(url_base, '/admin/information/' + path)
        if query_string := request.full_path.split('?', 1)[1]:
            url = f'{url}?{query_string}'
        r = meth(url, headers=headers, data=request.data)
        return make_response(r.content, r.status_code, self.remove_hop_by_hop_headers(r.headers))

    @classmethod
    def remove_hop_by_hop_headers(cls, headers):
        build_new_headers = {}
        for name, value in headers.items():
            if name not in cls.HOP_BY_HOP_HEADERS:
                build_new_headers[name] = value
        return build_new_headers
