# -*- coding: utf-8 -*-
from webargs import fields
from collections import defaultdict
from decimal import Decimal
from datetime import timed<PERSON><PERSON>

from flask import g, send_file

from app.common.constants import ADMIN_EXPORT_LIMIT, Language, BusinessParty
from app.models import db, SubAccount, User
from app.models.user import UserBizTag
from app.models.equity_center import (
    EquityType, EquityBaseInfo, UserEquity, UserCashbackEquity, EquitySendApply,
    UserCashbackEquityHistory, UserCashbackEquityTransferHistory, UserAirdropEquity, UserAirdropEquityHistory,
    UserCashbackSettlementHistory, UserCashbackTradeFeeHistory
)
from app.exceptions import InvalidArgument
from app.business import get_admin_user_name_map
from app.business.equity_center.helper import EquitySettings, EquityCenterService
from app.business.mission_center.mission import MissionBiz
from app.business.push_statistic import UserTagGroupBiz, EquitySendApplyUserParser
from app.assets import list_all_assets
from app.api.common import Namespace, Resource, respond_with_code, lock_request
from app.api.common.fields import (
    LimitField,
    PageField,
    EnumField,
    TimestampField,
    PositiveDecimalField,
)
from app.api.common.decorators import require_admin_webauth_token
from app.utils import amount_to_str, export_xlsx, datetime_to_utc8_str, batch_iter, now, ExcelExporter


ns = Namespace("Equity-Center-Admin")


class MissionEquityQuery:
    @classmethod
    def query_user_eq_info_by_bus_ids(cls, bus_type: UserEquity.BusinessType, bus_ids: list[str]) -> dict[int, dict]:
        user_eq_rows = UserEquity.query.filter(
            UserEquity.business_type == bus_type,
            UserEquity.business_id.in_(bus_ids),
        ).with_entities(
            UserEquity.id,
            UserEquity.business_id,
            UserEquity.type,
        ).all()
        air_user_eq_ids = [i.id for i in user_eq_rows if i.type == EquityType.AIRDROP]
        air_eq_row_map = {}
        if air_user_eq_ids:
            air_eq_rows = UserAirdropEquity.query.filter(
                UserAirdropEquity.user_equity_id.in_(air_user_eq_ids),
            ).all()
            air_eq_row_map = {i.user_equity_id: i for i in air_eq_rows}
        cb_user_eq_ids = [i.id for i in user_eq_rows if i.type == EquityType.CASHBACK]
        cb_eq_row_map = {}
        if cb_user_eq_ids:
            cb_eq_rows = UserCashbackEquity.query.filter(
                UserCashbackEquity.user_equity_id.in_(cb_user_eq_ids),
            ).all()
            cb_eq_row_map = {i.user_equity_id: i for i in cb_eq_rows}

        result = dict()
        for eq_row in user_eq_rows:
            item = {
                "type": eq_row.type
            }
            if eq_row.type == EquityType.AIRDROP:
                eq_detail: UserAirdropEquity = air_eq_row_map[eq_row.id]
                item["cost_asset"] = eq_detail.airdrop_asset
                item["cost_amount"] = eq_detail.airdrop_amount
            elif eq_row.type == EquityType.CASHBACK:
                eq_detail: UserCashbackEquity = cb_eq_row_map[eq_row.id]
                item["cost_asset"] = eq_detail.cost_asset
                item["cost_amount"] = eq_detail.cost_amount
                item["used_cost_amount"] = eq_detail.used_cost_amount
            result[eq_row.business_id] = item
        return result


@ns.route("/settings")
@respond_with_code
class EquitySettingsResource(Resource):

    @classmethod
    def get(cls):
        """权益中心-获取全局配置"""
        return {
            "items": EquitySettings.fields_and_values_json,
        }

    @classmethod
    @ns.use_kwargs(dict(
        settings=fields.Dict(required=True)
    ))
    def post(cls, **kwargs):
        """权益中心-修改全局配置"""
        settings = kwargs["settings"]
        for key, value in settings.items():
            try:
                setattr(EquitySettings, key, value)
            except (AttributeError, ValueError) as e:
                raise InvalidArgument(message=f'{e!r}')


@ns.route('/settings/<field>')
@respond_with_code
class EquitySettingsManagementResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True)
    ))
    def put(cls, field, **kwargs):
        """权益中心-编辑全局配置"""
        value = kwargs['value']

        try:
            setattr(EquitySettings, field, value)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')
        new_value = getattr(EquitySettings, field)

        return dict(
            value=new_value,
        )

    @classmethod
    def delete(cls, field):
        """权益中心-重置全局配置"""
        try:
            delattr(EquitySettings, field)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')
        new_value = getattr(EquitySettings, field)

        return dict(
            value=new_value,
        )


@ns.route("/cashback/list")
@respond_with_code
class CashbackEquityListResource(Resource):
    equity_type = EquityType.CASHBACK
    model = EquityBaseInfo

    @classmethod
    @ns.use_kwargs(
        dict(
            equity_id=fields.Integer(),
            status=EnumField(model.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-返现权益列表"""
        model = cls.model
        q = model.query.filter(
            model.type == cls.equity_type,
        ).order_by(model.id.desc())
        if equity_id := kwargs.get("equity_id"):
            q = q.filter(model.id == equity_id)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows: list[model] = pagination.items

        creators = [i.creator for i in rows]
        creator_name_map = get_admin_user_name_map(creators)

        items = []
        extra_fields = ["cashback_scope", "cashback_asset", "effective_days"]
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            extra_data = r.extra_data
            item.update(extra_data)
            for fie in extra_fields:
                if fie not in item:
                    item[fie] = ""
            item["extra_data"] = extra_data
            item["creator_name"] = creator_name_map.get(r.creator)
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                equity_type_dict=EquityType,
                status_dict=EquityBaseInfo.Status,
                cashback_scope_dict=UserCashbackEquity.CashbackScope,
                all_assets=['CET'],  # 目前线上只支持配CET
                all_equity_dict=EquityCenterService.get_all_equity_dict(),
            ),
        )

    @classmethod
    @lock_request()
    @ns.use_kwargs(
        dict(
            cashback_scope=EnumField(UserCashbackEquity.CashbackScope, required=True),
            cashback_asset=fields.String(required=True),
            cost_asset=fields.String(required=True),
            cost_amount=PositiveDecimalField(required=True),
            effective_days=fields.Integer(required=True, validate=lambda x: 1 <= x <= 60),
            remark=fields.String,
        )
    )
    def post(cls, **kwargs):
        """权益中心-返现权益-创建"""
        cashback_asset = kwargs["cashback_asset"]
        if cashback_asset not in list_all_assets():
            raise InvalidArgument(f"返现币种{cashback_asset}不存在")
        cost_asset = kwargs["cost_asset"]
        if cost_asset != "USDT":
            raise InvalidArgument(f"返现价值币种当前只支持USDT")

        cost_amount = kwargs["cost_amount"]
        extra_data = dict(
            cashback_scope=kwargs["cashback_scope"].name,
            cashback_asset=cashback_asset,
            effective_days=kwargs["effective_days"],
        )

        exist_base_eqs: list[EquityBaseInfo] = EquityBaseInfo.query.filter(
            EquityBaseInfo.type == cls.equity_type,
            EquityBaseInfo.cost_asset == cost_asset,
            EquityBaseInfo.cost_amount == cost_amount,
        ).all()
        for exist_base_eq in exist_base_eqs:
            if exist_base_eq.extra_data == extra_data:
                raise InvalidArgument(f"已存在重复权益数据 权益ID：{exist_base_eq.id}")

        base_eq = EquityBaseInfo(
            type=cls.equity_type,
            creator=g.user.id,
            remark=kwargs.get("remark", ""),
            cost_asset=cost_asset,
            cost_amount=cost_amount,
            extra_data=extra_data,
        )
        db.session.add(base_eq)
        db.session.commit()

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            status=EnumField(model.Status, required=True),
        )
    )
    def patch(cls, **kwargs):
        """权益中心-返现权益-禁用|启用"""
        model = cls.model
        id_ = kwargs["id"]
        row: model = model.query.filter(model.id == id_).first()
        if not row:
            raise InvalidArgument(message=f"返现权益{id_}不存在")

        row.status = kwargs['status']
        db.session.add(row)
        db.session.commit()


@ns.route("/cashback/user-equity")
@respond_with_code
class UserCashbackEquityListResource(Resource):
    equity_type = EquityType.CASHBACK

    export_headers = (
        {"field": "user_equity_id", Language.ZH_HANS_CN: "发放ID"},
        {"field": "business_type", Language.ZH_HANS_CN: "权益类型"},
        {"field": "plan_id", Language.ZH_HANS_CN: "推送ID"},
        {"field": "mission_id", Language.ZH_HANS_CN: "任务ID"},
        {"field": "equity_id", Language.ZH_HANS_CN: "权益ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "发放时间(UTC+8)"},
        {"field": "cost_amount", Language.ZH_HANS_CN: "返现价值"},
        {"field": "used_cost_amount", Language.ZH_HANS_CN: "累计返现额度"},
        {"field": "cashback_amount", Language.ZH_HANS_CN: "累计返现额度"},
        {"field": "status", Language.ZH_HANS_CN: "使用权益状态"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间(UTC+8)"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            status=EnumField(UserCashbackEquity.Status),
            equity_id=fields.Integer,
            business_type=EnumField(UserEquity.BusinessType),
            start_time=TimestampField,
            end_time=TimestampField,
            page=PageField,
            limit=LimitField,
            export=fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """权益中心-权益发放-返现权益发放列表"""

        q = UserCashbackEquity.query.join(
            UserEquity,
            UserCashbackEquity.user_equity_id == UserEquity.id,
        ).order_by(UserCashbackEquity.id.desc())
        if user_id := kwargs.get("user_id"):
            q = q.filter(UserCashbackEquity.user_id == user_id)
        if status := kwargs.get("status"):
            q = q.filter(UserCashbackEquity.status == status)
        else:
            q = q.filter(UserCashbackEquity.status != UserCashbackEquity.Status.FAILED)
        if equity_id := kwargs.get("equity_id"):
            q = q.filter(UserEquity.equity_id == equity_id)
        if business_type := kwargs.get("business_type"):
            q = q.filter(UserEquity.business_type == business_type)
        if start_time := kwargs.get("start_time"):
            q = q.filter(UserEquity.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            q = q.filter(UserEquity.created_at <= end_time)

        cols = [
            UserCashbackEquity.created_at,
            UserCashbackEquity.updated_at,
            UserCashbackEquity.id,
            UserCashbackEquity.user_id,
            UserCashbackEquity.user_equity_id,
            UserCashbackEquity.start_time,
            UserCashbackEquity.end_time,
            UserCashbackEquity.status,
            UserCashbackEquity.cost_asset,
            UserCashbackEquity.cost_amount,
            UserCashbackEquity.cashback_scope,
            UserCashbackEquity.cashback_asset,
            UserCashbackEquity.cashback_amount,
            UserCashbackEquity.used_cost_amount,
            UserCashbackEquity.last_cashback_at,
            UserEquity.equity_id,
            UserEquity.type,
            UserEquity.business_id,
            UserEquity.business_type,
            UserEquity.status.label("base_status"),
            UserEquity.finished_at,
        ]
        q = q.with_entities(*cols)

        is_export = kwargs.get("export")
        if is_export:
            rows = q.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(rows)
        else:
            pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
            total = pagination.total
            rows = pagination.items

        user_equity_ids = {i.user_equity_id for i in rows}
        pending_tran_rows = UserCashbackEquityTransferHistory.query.filter(
            UserCashbackEquityTransferHistory.user_equity_id.in_(user_equity_ids),
            UserCashbackEquityTransferHistory.status != UserCashbackEquityTransferHistory.Status.FINISHED,
        ).with_entities(
            UserCashbackEquityTransferHistory.id,
            UserCashbackEquityTransferHistory.user_equity_id,
            UserCashbackEquityTransferHistory.his_ids,
        ).all()
        eq_pending_tran_rows_map = defaultdict(list)
        pending_detail_ids = set()
        for r in pending_tran_rows:
            eq_pending_tran_rows_map[r.user_equity_id].append(r)
            pending_detail_ids.update(r.his_ids)
        detail_rows = []
        for ch_d_ids in batch_iter(pending_detail_ids, 5000):
            ch_detail_rows = UserCashbackEquityHistory.query.filter(
                UserCashbackEquityHistory.id.in_(ch_d_ids),
            ).all()
            detail_rows.extend(ch_detail_rows)
        detail_row_map = {i.id: i for i in detail_rows}

        eq_pending_cashback_amount_map = defaultdict(Decimal)
        eq_pending_cost_amount_map = defaultdict(Decimal)
        for r in pending_tran_rows:
            for did in r.his_ids:
                pd = detail_row_map.get(did)
                if pd:
                    eq_pending_cashback_amount_map[r.id] += pd.delta_cashback_amount
                    eq_pending_cost_amount_map[r.id] += pd.delta_cost_amount

        mis_biz_ids = {i.business_id for i in rows if i.business_type == UserEquity.BusinessType.MISSION}
        mis_biz_info_map = MissionBiz.query_user_mission_info(list(mis_biz_ids))

        enum_cols = ['type', 'status', 'base_status', 'business_type', 'cashback_scope']
        items = []
        for i in rows:
            d = dict([k, getattr(i, k)] for k in i._fields)  # noqa
            for c in enum_cols:
                d[c] = d[c].name
            d['plan_id'] = mis_biz_info_map.get(i.business_id, {}).get('plan_id', 0)
            d['mission_id'] = mis_biz_info_map.get(i.business_id, {}).get('mission_id', 0)
            eq_pending_tran_rows = eq_pending_tran_rows_map[i.user_equity_id]
            pending_cashback_amount = sum([eq_pending_cashback_amount_map[i.id] for i in eq_pending_tran_rows])
            d['pending_cashback_amount'] = pending_cashback_amount
            d['real_cashback_amount'] = d['cashback_amount'] - pending_cashback_amount
            pending_cost_amount = sum([eq_pending_cost_amount_map[i.id] for i in eq_pending_tran_rows])
            d['pending_cost_amount'] = pending_cost_amount
            d['real_cost_amount'] = d['used_cost_amount'] - pending_cost_amount
            items.append(d)

        if is_export:
            for d in items:
                d["created_at"] = datetime_to_utc8_str(d["created_at"])
                d["updated_at"] = datetime_to_utc8_str(d["updated_at"])
                d["cost_amount"] = f'{amount_to_str(d["cost_amount"])} {d["cost_asset"]}'
                d["used_cost_amount"] = f'{amount_to_str(d["used_cost_amount"] - d["pending_cost_amount"])} {d["cost_asset"]}'
                d["cashback_amount"] = f'{amount_to_str(d["cashback_amount"] - d["pending_cashback_amount"])} {d["cashback_asset"]}'
                d["business_type"] = UserEquity.BusinessType[d["business_type"]].value
                d["status"] = UserCashbackEquity.Status[d["status"]].value

            return export_xlsx(
                filename="user-cashback-equity-list",
                data_list=items,
                export_headers=cls.export_headers,
            )

        status_dict = {i.name: i.value for i in UserCashbackEquity.Status if i != UserCashbackEquity.Status.FAILED}
        return dict(
            items=items,
            total=total,
            extra=dict(
                business_type_dict=UserEquity.BusinessType,
                status_dict=status_dict,
                all_equity_dict=EquityCenterService.get_all_equity_dict(),
                cashback_scope_dict=UserCashbackEquity.CashbackScope,
            ),
        )


@ns.route("/cashback/user-equity-history")
@respond_with_code
class UserCashbackEquityHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            user_equity_id=fields.Integer,
            status=EnumField(UserCashbackEquityTransferHistory.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-权益发放-返现权益发放明细"""
        q = UserCashbackEquityTransferHistory.query.order_by(UserCashbackEquityTransferHistory.id.desc())
        if user_equity_id := kwargs.get("user_equity_id"):
            q = q.filter(UserCashbackEquityTransferHistory.user_equity_id == user_equity_id)
        if status := kwargs.get("status"):
            q = q.filter(UserCashbackEquityTransferHistory.status == status)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        user_equity_ids = {i.user_equity_id for i in rows}
        user_eqs = UserEquity.query.filter(
            UserEquity.id.in_(user_equity_ids),
        ).all()
        user_eq_map = {i.id: i for i in user_eqs}

        detail_ids = set()
        for r in rows:
            detail_ids.update(r.his_ids)
        pending_tran_rows = UserCashbackEquityTransferHistory.query.filter(
            UserCashbackEquityTransferHistory.user_equity_id.in_(user_equity_ids),
            UserCashbackEquityTransferHistory.status != UserCashbackEquityTransferHistory.Status.FINISHED,
        ).with_entities(
            UserCashbackEquityTransferHistory.id,
            UserCashbackEquityTransferHistory.user_equity_id,
            UserCashbackEquityTransferHistory.his_ids,
        ).all()
        eq_pending_tran_rows_map = defaultdict(list)
        pending_detail_ids = set()
        for r in pending_tran_rows:
            eq_pending_tran_rows_map[r.user_equity_id].append(r)
            pending_detail_ids.update(r.his_ids)

        q_details = detail_ids | pending_detail_ids
        detail_rows = UserCashbackEquityHistory.query.filter(
            UserCashbackEquityHistory.id.in_(q_details),
        ).all()
        detail_row_map = {i.id: i for i in detail_rows}

        eq_pending_cashback_amount_map = defaultdict(Decimal)
        eq_pending_cost_amount_map = defaultdict(Decimal)
        for r in pending_tran_rows:
            for did in r.his_ids:
                pd = detail_row_map.get(did)
                if pd:
                    eq_pending_cashback_amount_map[r.id] += pd.delta_cashback_amount
                    eq_pending_cost_amount_map[r.id] += pd.delta_cost_amount

        items = []
        for i in rows:
            d = i.to_dict(enum_to_name=True)
            user_eq = user_eq_map.get(i.user_equity_id)
            d['equity_id'] = user_eq.equity_id if user_eq else 0

            d['cashback_asset'] = ''
            d['cost_asset'] = ''
            d['settle_at'] = i.created_at
            d['delta_cashback_amount'] = 0
            d['delta_cost_amount'] = 0
            d['total_cashback_amount'] = 0
            d['total_used_cost_amount'] = 0
            d['details'] = []
            for detail_id in i.his_ids:
                detail_r: UserCashbackEquityHistory = detail_row_map.get(detail_id)
                if detail_r:
                    d['details'].append(detail_r.to_dict(enum_to_name=True))
                    d['cashback_asset'] = detail_r.cashback_asset
                    d['cost_asset'] = detail_r.cost_asset
                    d['settle_at'] = detail_r.settle_at
                    d['delta_cashback_amount'] += detail_r.delta_cashback_amount
                    d['delta_cost_amount'] += detail_r.delta_cost_amount
                    d['total_used_cost_amount'] = max(detail_r.total_used_cost_amount, d['total_used_cost_amount'])
                    d['total_cashback_amount'] = max(detail_r.total_cashback_amount, d['total_cashback_amount'])
            eq_pending_tran_rows = eq_pending_tran_rows_map[i.user_equity_id]
            ago_pending_rows = [p for p in eq_pending_tran_rows if p.id <= i.id]
            pending_cashback_amount = sum([eq_pending_cashback_amount_map[i.id] for i in ago_pending_rows])
            d['pending_cashback_amount'] = pending_cashback_amount
            d['real_cashback_amount'] = d['total_cashback_amount'] - pending_cashback_amount
            pending_cost_amount = sum([eq_pending_cost_amount_map[i.id] for i in ago_pending_rows])
            d['pending_cost_amount'] = pending_cost_amount
            d['real_cost_amount'] = d['total_used_cost_amount'] - pending_cost_amount
            items.append(d)

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict=UserCashbackEquityTransferHistory.Status,
            ),
        )


@ns.route("/relation-history")
@respond_with_code
class EquityRelationHisResource(Resource):

    RELATION_MAP = {
        "user_airdrop_equity": UserAirdropEquity,
        "user_airdrop_equity_his": UserAirdropEquityHistory,
        "cashback_equity_his": UserCashbackEquityHistory,
        "cashback_settlement_his": UserCashbackSettlementHistory,
        "cashback_trade_fee_his": UserCashbackTradeFeeHistory,
        "user_biz_tag": UserBizTag,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            history_type=EnumField(RELATION_MAP),
            start=TimestampField,
            end=TimestampField,
            row_id=fields.Integer,
            user_id=fields.Integer,
            equity_id=fields.Integer,
            main_user_id=fields.Integer,
            user_equity_id=fields.Integer,
            trade_business_id=fields.Integer,
            settle_his_id=fields.Integer,
            settlement_status=EnumField(UserCashbackSettlementHistory.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-相关记录"""
        history_type = kwargs["history_type"]
        model = cls.RELATION_MAP[history_type]
        q = model.query.order_by(model.id.desc())
        if row_id := kwargs.get("row_id"):
            q = q.filter(model.id == row_id)
        if user_id := kwargs.get("user_id"):
            if model == UserCashbackTradeFeeHistory:
                q = q.filter(model.trade_user_id == user_id)
            else:
                q = q.filter(model.user_id == user_id)
        if start_time := kwargs.get("start"):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            q = q.filter(model.created_at <= end_time)
        # special filter
        if model == UserAirdropEquity or model == UserAirdropEquityHistory or model == UserCashbackEquityHistory:
            if user_equity_id := kwargs.get("user_equity_id"):
                q = q.filter(model.user_equity_id == user_equity_id)
            if equity_id := kwargs.get("equity_id"):
                q = q.join(
                    UserEquity,
                    UserEquity.equity_id == equity_id,
                )
        if model == UserCashbackSettlementHistory:
            if settlement_status := kwargs.get("settlement_status"):
                q = q.filter(model.status == settlement_status)
        if model == UserCashbackTradeFeeHistory:
            if trade_business_id := kwargs.get("trade_business_id"):
                q = q.filter(model.trade_business_id == trade_business_id)
            if (settle_his_id := kwargs.get("settle_his_id")) is not None:
                if settle_his_id:
                    q = q.filter(model.settle_his_id == settle_his_id)
                else:
                    q = q.filter(model.settle_his_id.is_(None))
            if main_user_id := kwargs.get("main_user_id"):
                subs = SubAccount.query.filter(
                    SubAccount.main_user_id == main_user_id,
                ).with_entities(SubAccount.user_id).all()
                _q_u_ids = {i.user_id for i in subs} | {main_user_id}
                q = q.filter(model.trade_user_id.in_(_q_u_ids))
        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        total = pagination.total
        rows: list[model] = pagination.items

        items = []
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                settlement_status_dict=UserCashbackSettlementHistory.Status,
                biz_tag_dict=UserBizTag.BizTag,
                biz_tag_source_dict=UserBizTag.Source,
            ),
        )


@ns.route("/send-apply/list")
@respond_with_code
class EquitySendApplyListResource(Resource):

    @classmethod
    def get_all_apply_title_dict(cls) -> dict[int, str]:
        rows: list[EquitySendApply] = EquitySendApply.query.order_by(EquitySendApply.id.desc()).with_entities(
            EquitySendApply.id,
            EquitySendApply.send_type,
            EquitySendApply.title
        ).all()
        return {i.id: f"{i.id} {i.send_type.value} {i.title}" for i in rows}

    @classmethod
    def get_equity_type_eq_info_dict(cls) -> dict[str, dict[int, str]]:
        model = EquityBaseInfo
        rows = model.query.filter(
            model.status == model.Status.OPEN,
        ).with_entities(
            model.id,
            model.type,
            model.cost_asset,
            model.cost_amount,
            model.extra_data,
        ).all()
        equity_type_eq_info_dict = defaultdict(dict)
        for row in rows:
            row: model
            eq_type = row.type
            if eq_type == EquityType.AIRDROP:
                desc = f'{amount_to_str(row.cost_amount)} {row.cost_asset}({row.id})'
            elif eq_type == EquityType.CASHBACK:
                cashback_scope_str = UserCashbackEquity.CashbackScope[row.extra_data["cashback_scope"]].value
                desc = f'{amount_to_str(row.cost_amount)} {row.cost_asset} {cashback_scope_str}({row.id})'
            else:
                continue
            equity_type_eq_info_dict[eq_type.name][row.id] = desc
        return equity_type_eq_info_dict

    @classmethod
    def format_equity_desc(cls, row: EquityBaseInfo):
        if not row:
            return "-"
        if row.type == EquityType.AIRDROP:
            return f"{row.type.value} ({amount_to_str(row.cost_amount)} {row.cost_asset})"
        else:
            return f"{row.type.value} (ID：{row.id})"

    @classmethod
    @ns.use_kwargs(
        dict(
            equity_type=EnumField(EquityType),
            apply_id=fields.Integer,
            equity_id=fields.Integer,
            title=fields.String,
            status=EnumField(EquitySendApply.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-权益发放列表"""
        model = EquitySendApply
        q = model.query.order_by(model.id.desc())
        if apply_id := kwargs.get("apply_id"):
            q = q.filter(model.id == apply_id)
        if equity_id := kwargs.get("equity_id"):
            q = q.filter(model.equity_id == equity_id)
        if equity_type := kwargs.get("equity_type"):
            q = q.filter(model.equity_type == equity_type)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        if title := kwargs.get("title"):
            q = q.filter(model.title.contains(title))

        cols = [
            model.id,
            model.created_at,
            model.updated_at,
            model.send_type,
            model.business_party,
            model.title,
            model.equity_id,
            model.equity_type,
            model.total_send_count,
            model.success_send_count,
            model.status,
            model.send_at,
            model.send_finished_at,
            model.creator,
            model.remark,
            model.group_ids,
        ]
        q = q.with_entities(*cols)
        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows: list[model] = pagination.items

        creators = [i.creator for i in rows]
        creator_name_map = get_admin_user_name_map(creators)

        equity_ids = {i.equity_id for i in rows}
        eq_base_info_rows = EquityBaseInfo.query.filter(
            EquityBaseInfo.id.in_(equity_ids),
        ).with_entities(
            EquityBaseInfo.id,
            EquityBaseInfo.type,
            EquityBaseInfo.cost_asset,
            EquityBaseInfo.cost_amount,
            EquityBaseInfo.extra_data,
        ).all()
        equity_basic_info_map = {i.id: i for i in eq_base_info_rows}

        tag_group_ids = set()
        for item in rows:
            tag_group_ids |= set(item.group_ids)
        tag_groups_map = UserTagGroupBiz.get_tag_group_info_dict(tag_group_ids)

        items = []
        for r in rows:
            item = {c.name: getattr(r, c.name) for c in cols}
            item["send_type"] = item["send_type"].name
            item["business_party"] = item["business_party"].name
            item["equity_type"] = item["equity_type"].name
            item["status"] = item["status"].name
            item["creator_name"] = creator_name_map.get(r.creator)
            eq_basic_info = equity_basic_info_map.get(r.equity_id)
            item["cost_asset"] = eq_basic_info.cost_asset if eq_basic_info else ''
            item["cost_amount"] = eq_basic_info.cost_amount if eq_basic_info else Decimal()
            item["equity_desc"] = cls.format_equity_desc(eq_basic_info)
            tag_groups = []
            for group_id in r.group_ids:
                tag_group = tag_groups_map.get(group_id)
                if tag_group:
                    tag_groups.append(tag_group)
            item["tag_groups"] = tag_groups
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                equity_type_dict=EquityType,
                status_dict=model.Status,
                send_type_dict=model.SendType,
                business_party_dict=BusinessParty,
                equity_type_eq_info_dict=cls.get_equity_type_eq_info_dict(),
                apply_title_dict=cls.get_all_apply_title_dict(),
            ),
        )

    @classmethod
    @lock_request()
    @ns.use_kwargs(
        dict(
            title=fields.String(required=True),
            business_party=EnumField(enum=BusinessParty, required=True),
            send_type=EnumField(enum=EquitySendApply.SendType, required=True),
            send_at=TimestampField(is_ms=True, required=True),
            total_send_count=fields.Integer(required=False, validate=lambda x: x > 0),
            equity_type=EnumField(enum=EquityType, required=True),
            equity_id=fields.Integer(required=False),  # 空投时不传id，传空投币种和数目
            airdrop_asset=fields.String,
            airdrop_amount=fields.Integer(required=False, validate=lambda x: x > 0),
            groups=fields.List(fields.Integer),
            remark=fields.String,
        )
    )
    def post(cls, **kwargs):
        """权益中心-权益发放-创建"""
        if kwargs["send_at"] < now():
            raise InvalidArgument(message="发放时间不正确，发放时间应晚于当前时间")

        admin_user_id = g.user.id
        eq_info = cls.get_or_create_equity_base_info(admin_user_id, kwargs)

        groups = kwargs.pop("groups") or ""
        group_ids = UserTagGroupBiz.filter_tag_group_ids(ids=groups) if groups else []
        if not group_ids:
            raise InvalidArgument(message="发放客群为空")

        total_send_count = kwargs.get('total_send_count') or 0
        apply = EquitySendApply(
            send_type=kwargs["send_type"],
            business_party=kwargs["business_party"],
            title=kwargs["title"],
            equity_id=eq_info.id,
            equity_type=eq_info.type,
            total_send_count=total_send_count,
            send_at=kwargs["send_at"],
            creator=admin_user_id,
            remark=kwargs.get("remark") or "",
            group_ids=group_ids,
        )
        group_user_ids, _ = EquitySendApplyUserParser(apply).parse()
        apply.set_group_user_ids(group_user_ids)
        if not total_send_count:
            apply.total_send_count = len(group_user_ids)

        db.session.add(apply)
        db.session.commit()

    @classmethod
    def get_or_create_equity_base_info(cls, admin_user_id: int, kwargs: dict) -> EquityBaseInfo:
        equity_type = kwargs['equity_type']
        if equity_type == EquityType.CASHBACK:
            equity_id = kwargs.get('equity_id')
            if not equity_id:
                raise InvalidArgument(message="缺少返现权益ID")
        else:
            airdrop_asset = kwargs.get('airdrop_asset')
            airdrop_amount = kwargs.get('airdrop_amount')
            if not airdrop_asset or not airdrop_amount:
                raise InvalidArgument(message="缺少空投币种或空投数目")
            if airdrop_asset not in list_all_assets():
                raise InvalidArgument(f"空投币种{airdrop_asset}不存在")
            equity_id = EquityCenterService.get_or_create_airdrop_base_equity(
                airdrop_asset=airdrop_asset,
                airdrop_amount=airdrop_amount,
                creator=admin_user_id,
            )
        eq_info: EquityBaseInfo = EquityBaseInfo.query.get(equity_id)
        if not eq_info or eq_info.status == EquityBaseInfo.Status.CLOSE:
            raise InvalidArgument(message="未找到权益或者权益状态不允许使用")
        return eq_info


@ns.route("/send-apply/<int:apply_id>")  # noqa
@respond_with_code
class EquitySendApplyDetailResource(Resource):

    @classmethod
    def get_apply(cls, apply_id: int) -> EquitySendApply:
        apply: EquitySendApply = EquitySendApply.query.get(apply_id)
        if not apply:
            raise InvalidArgument(message=f"未找到权益发放{apply_id}")
        return apply

    @classmethod
    @ns.use_kwargs(
        dict(
            title=fields.String(required=True),
            business_party=EnumField(enum=BusinessParty, required=True),
            send_type=EnumField(enum=EquitySendApply.SendType, required=True),
            send_at=TimestampField(is_ms=True, required=True),
            equity_type=EnumField(enum=EquityType, required=True),
            equity_id=fields.Integer(required=False),  # 空投时不传id，传空投币种和数目
            airdrop_asset=fields.String,
            airdrop_amount=fields.Integer(required=False, validate=lambda x: x > 0),
            total_send_count=fields.Integer(required=False, validate=lambda x: x > 0),
            groups=fields.List(fields.Integer),
            remark=fields.String,
            resubmit_wait_audit=fields.Boolean,
        )
    )
    def put(cls, apply_id, **kwargs):
        """权益中心-权益发放-修改发放"""
        if kwargs["send_at"] < now():
            raise InvalidArgument(message="发放时间不正确，发放时间应晚于当前时间")
        apply = cls.get_apply(apply_id)

        if apply.status not in [EquitySendApply.Status.CREATED, EquitySendApply.Status.REJECTED]:
            raise InvalidArgument(message="当前状态不允许修改")

        if kwargs.get('resubmit_wait_audit') and apply.status == EquitySendApply.Status.REJECTED:
            # 重新提交审核
            cls.change_apply_status(apply, EquitySendApply.Status.CREATED)

        admin_user_id = g.user.id
        eq_info = EquitySendApplyListResource.get_or_create_equity_base_info(admin_user_id, kwargs)

        groups = kwargs.pop("groups") or ""
        group_ids = UserTagGroupBiz.filter_tag_group_ids(ids=groups) if groups else []
        if not group_ids:
            raise InvalidArgument(message="发放客群为空")

        total_send_count = kwargs.get('total_send_count') or 0
        apply.send_type = kwargs["send_type"]
        apply.business_party = kwargs["business_party"]
        apply.title = kwargs["title"]
        apply.equity_id = eq_info.id
        apply.equity_type = eq_info.type
        apply.total_send_count = total_send_count
        apply.send_at = kwargs["send_at"]
        apply.remark = kwargs.get("remark") or ""
        apply.group_ids = group_ids
        group_user_ids, _ = EquitySendApplyUserParser(apply).parse()
        apply.set_group_user_ids(group_user_ids)
        if not total_send_count:
            apply.total_send_count = len(group_user_ids)
        db.session.commit()

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(enum=EquitySendApply.Status, required=True),
        )
    )
    def patch(cls, apply_id, **kwargs):
        """运营-权益发放-更改权益发放状态（拒绝、禁用）"""
        apply = cls.get_apply(apply_id)
        new_status = kwargs['status']
        cls.change_apply_status(apply, new_status)

    @classmethod
    def change_apply_status(cls, apply: EquitySendApply, new_status: EquitySendApply.Status):
        status_enum = EquitySendApply.Status
        allow_status_flows = {
            # (current_status, next_status)
            (status_enum.CREATED, status_enum.PASSED),  # 审核通过
            (status_enum.CREATED, status_enum.REJECTED),  # 审核拒绝
            (status_enum.REJECTED, status_enum.CREATED),  # 重新提交审核
            (status_enum.PASSED, status_enum.DISABLED),  # 禁用
        }

        old_status = apply.status
        flow = (old_status, new_status)
        if flow not in allow_status_flows:
            raise InvalidArgument(message=f"不支持 {old_status.value} -> {new_status.value} 的状态修改")
        apply.status = new_status
        db.session.commit()


@ns.route("/send-apply/<int:apply_id>/audit")
@respond_with_code
class EquitySendApplyAuditResource(Resource):

    @classmethod
    @require_admin_webauth_token
    def post(cls, apply_id):
        """运营-权益发放-审核权益发放"""
        admin_user_id = g.user.id
        apply = EquitySendApplyDetailResource.get_apply(apply_id)
        if apply.status != EquitySendApply.Status.CREATED:
            raise InvalidArgument(message="状态不是待审核")
        if apply.creator == admin_user_id:
            raise InvalidArgument(message="申请人和审核人不能是同一个人")
        if apply.send_at + timedelta(minutes=2) < now():
            # 审核时间不能晚于发放时间超过2分钟，如果晚于，就把状态更新为审核未通过
            EquitySendApplyDetailResource.change_apply_status(apply, EquitySendApply.Status.REJECTED)
            db.session.commit()
            raise InvalidArgument(message="审核已超时，请修改发放时间重新提交")

        EquitySendApplyDetailResource.change_apply_status(apply, EquitySendApply.Status.PASSED)
        db.session.commit()


@ns.route('/send-apply/<int:apply_id>/user-download')
@respond_with_code
class EquitySendApplyUserResource(Resource):

    @classmethod
    def get(cls, apply_id):
        """运营-权益发放-权益发放用户下载"""
        apply = EquitySendApplyDetailResource.get_apply(apply_id)
        data = cls.get_data(apply)
        fields_ = headers = ["id", "email"]
        stream = ExcelExporter(
            data_list=data,
            fields=fields_,
            headers=headers
        ).export_streams()
        return send_file(
            stream,
            download_name=f'权益发放{apply_id}客群信息.xlsx',
            as_attachment=True
        )

    @classmethod
    def get_data(cls, row: EquitySendApply) -> list:
        user_ids = row.cached_group_user_ids
        items = []
        for chunk_ids in batch_iter(user_ids, 2000):
            chunk_objs = User.query.with_entities(
                User.id,
                User.email,
            ).filter(
                User.id.in_(chunk_ids),
            ).all()
            items.extend(chunk_objs)
        return [{'id': obj.id, 'email': obj.email} for obj in items]
