# -*- coding: utf-8 -*-
import io
import json
from decimal import Decimal
from webargs import fields
from openpyxl import Workbook
from flask import send_file

from app.api.common import Namespace, respond_with_code, Resource
from app.assets import list_all_assets
from app.business import AssetComparator

from app.caches.admin import RealTimeAssetAdequacyExternalCache, RealTimeAssetAdequacyCache

from app.common import PrecisionEnum
from app.utils import amount_to_str, format_percent

ns = Namespace('Asset Adequacy')

COIN_PLACES = PrecisionEnum.COIN_PLACES.value


@ns.route('')
@respond_with_code
class AssetAdequacyResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """统计-资产统计-资金充足率统计"""
        main_cache = RealTimeAssetAdequacyCache()
        sub_cache = RealTimeAssetAdequacyExternalCache()
        ret = main_cache.read()
        if not ret:
            return dict(
                records=[],
                assets=list_all_assets()
            )
        sub_data = sub_cache.read()
        data = json.loads(ret['data'])
        records = []
        records_mapping = {}
        for mapping in data:
            quantification_balance = sub_data.get(mapping['asset'], 0)
            wallet_available = 0
            wallet_available += Decimal(mapping['hot_wallet'])
            wallet_available += Decimal(mapping['cold_wallet'])
            wallet_available += Decimal(mapping['deposit_wallet'])
            wallet_available += Decimal(mapping['staking'])

            available = 0
            available += Decimal(mapping['hot_wallet'])
            available += Decimal(mapping['cold_wallet'])
            available += Decimal(mapping['deposit_wallet'])
            available += Decimal(mapping['staking'])
            available += Decimal(mapping['margin_unflat'])
            available += Decimal(mapping['credit_unflat'])
            available += Decimal(mapping['pledge_unflat'])
            available += Decimal(quantification_balance)
            total = Decimal(mapping['balance'])
            if total != Decimal():
                wallet_ratio_num = amount_to_str(wallet_available / total)
                wallet_ratio = format_percent(wallet_available / total)
                ratio_num = amount_to_str(available / total)
                ratio = format_percent(available / total)
            else:
                wallet_ratio = '/'
                wallet_ratio_num = '0'
                ratio = '/'
                ratio_num = '0'
            item = {
                'asset': mapping['asset'],
                'hot_wallet': amount_to_str(mapping['hot_wallet'], COIN_PLACES),
                'cold_wallet': amount_to_str(mapping['cold_wallet'], COIN_PLACES),
                'deposit_wallet': amount_to_str(mapping['deposit_wallet'], COIN_PLACES),
                'staking': amount_to_str(mapping['staking'], COIN_PLACES),
                'margin_unflat': amount_to_str(mapping['margin_unflat'], COIN_PLACES),
                'credit_unflat': amount_to_str(mapping['credit_unflat'], COIN_PLACES),
                'pledge_unflat': amount_to_str(mapping['pledge_unflat'], COIN_PLACES),
                'balance': amount_to_str(mapping['balance'], COIN_PLACES),
                'balance_usd': amount_to_str(mapping['balance_usd'], COIN_PLACES),
                'quantification_balance': amount_to_str(quantification_balance, COIN_PLACES),
                'ratio': ratio,
                'ratio_num': ratio_num,
                'wallet_ratio': wallet_ratio,
                'wallet_ratio_num': wallet_ratio_num,
            }
            records.append(item)
            records_mapping.update({
                mapping['asset']: item
            })

        if asset := kwargs.get('asset'):
            item = records_mapping.get(asset)
            ret = [item] if item else []
        else:
            ret = records
        ret.sort(key=lambda x: AssetComparator(x['asset']))
        if kwargs['export']:
            return cls.do_export_xlsx(
                data_list=ret,
            )
        return dict(
            records=ret,
            assets=list_all_assets()
        )

    @classmethod
    def do_export_xlsx(cls, data_list):
        wb = Workbook()
        ws = wb.create_sheet(title='资金充足率统计')
        cls._write_header(ws)
        for data in data_list:
            ws.append(
                [
                    data['asset'],
                    data['hot_wallet'],
                    data['cold_wallet'],
                    data['deposit_wallet'],
                    data['margin_unflat'],
                    data['credit_unflat'],
                    data['pledge_unflat'],
                    data['quantification_balance'],
                    data['balance'],
                    data['balance_usd'],
                    data['ratio'],
                    data['wallet_ratio'],
                ]
            )
        del wb['Sheet']
        stream = io.BytesIO()
        wb.save(stream)
        stream.seek(0)
        return send_file(
            stream,
            download_name=f'资金充足率统计.xlsx',
            as_attachment=True
        )

    @classmethod
    def _write_header(cls, ws):
        mapping = {
            '币种': (1, 1, 3, 1),
            '平台可用资产': (1, 2, 1, 8),
            '热钱包': (2, 2, 3, 2),
            '冷钱包': (2, 3, 3, 3),
            '充值钱包': (2, 4, 3, 4),
            '借贷未还': (2, 5, 2, 7),
            '杠杆': (3, 5, 3, 5),
            '授信': (3, 6, 3, 6),
            '借贷': (3, 7, 3, 7),
            '量化站外资产': (2, 8, 3, 8),
            '外部用户资产': (1, 9, 3, 9),
            '外部用户资产市值': (1, 10, 3, 10),
            '资金充足率': (1, 11, 3, 11),
            '钱包资产充足率': (1, 12, 3, 12),
        }
        for title, (start_row, start_column, end_row, end_column) in mapping.items():
            ws.merge_cells(
                start_row=start_row,
                start_column=start_column,
                end_row=end_row,
                end_column=end_column
            )
            ws.cell(start_row, start_column, title)
