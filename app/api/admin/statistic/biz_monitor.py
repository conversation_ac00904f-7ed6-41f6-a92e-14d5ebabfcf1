# -*- coding: utf-8 -*-
import json
import re
from collections import defaultdict
from enum import Enum
from datetime import timedelta


from app.business.clients.biz_monitor import BizMonitorClient
from app.common.constants import Language
from app.common.events import EventDataType
from app.utils import now, today_datetime
from app.utils.export import export_xlsx
from app.utils.importer import get_table_rows
from flask import g, request
from sqlalchemy import func
from webargs import fields

from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import DateField, EnumField, LimitField, PageField, TimestampField
from app.exceptions.basic import InvalidArgument
from app.models.authority import AdminUser
from app.models.base import db
from app.models.biz_monitor import EventChangeHistory, MonitorEvent, MonitorEventGroup, EventTag, UserEventCollection, \
    UserEventBrowsingHistory
from app.models.user import User


ns = Namespace('biz-monitor')


RE_EVENT_TAG = re.compile(r'^[A-Za-z0-9_]+$')


def format_event_tag_name(tag_name):
    if RE_EVENT_TAG.fullmatch(tag_name):
        return tag_name.upper()
    return tag_name


@ns.route('/event')
@respond_with_code
class MonitorEventResource(Resource):

    @classmethod
    def get_admin_user_display(cls, admin_user_ids):
        admin_name_mapper = {
            admin.user_id: admin.name
            for admin in AdminUser.query.filter(AdminUser.user_id.in_(admin_user_ids)).all()
        }
        admin_email_mapper = {
            i: e for i, e in User.query.filter(User.id.in_(admin_user_ids)).with_entities(User.id, User.email).all()
        }
        admin_email_mapper.update(admin_name_mapper)
        return admin_email_mapper

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer,
        type_id=fields.Integer,
        name=fields.String,
        status=EnumField(MonitorEvent.Status, default=MonitorEvent.Status.VALID),
        created_time=DateField(to_date=True),
        page=PageField(missing=1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """数据监控-事件管理-事件列表"""
        page, limit = kwargs['page'], kwargs['limit']
        query = MonitorEvent.query.join(MonitorEventGroup)
        if id := kwargs.get("id"):
            query = query.filter(
                MonitorEvent.id == id
            )
        if type_id := kwargs.get("type_id"):
            query = query.filter(
                MonitorEventGroup.id == type_id
            )
        if name := kwargs.get("name"):
            query = query.filter(
                MonitorEvent.name.contains(name)
            )
        if status := kwargs.get("status"):
            query = query.filter(
                MonitorEvent.status == status
            )
        if created_time := kwargs.get("created_time"):
            query = query.filter(
                func.date_format(MonitorEvent.created_at, "%Y-%m-%d") == created_time
            )

        paginate = query.order_by(
            MonitorEvent.id
        ).with_entities(
            MonitorEvent.id,
            MonitorEventGroup.name.label("type_name"),
            MonitorEventGroup.id.label("type_id"),
            MonitorEvent.name,
            MonitorEvent.status,
            MonitorEvent.data_type,
            MonitorEvent.desc,
            MonitorEvent.created_at,
        ).paginate(page, limit)

        return dict(
            items=[{
                "id": item.id,
                "name": item.name,
                "type_name": item.type_name,
                "type_id": item.type_id,
                "status": item.status,
                "desc": item.desc,
                "data_type": item.data_type.name,
                "created_at": item.created_at
            } for item in paginate.items],
            total=paginate.total,
            envent_types={item.id: item.name for item in MonitorEventGroup.query.filter(
                MonitorEventGroup.status == MonitorEventGroup.Status.VALID).all()},
            all_status={item.name: item.value for item in MonitorEvent.Status if item != MonitorEvent.Status.CLEARED},
        )

    @classmethod
    @ns.use_kwargs(dict(
        type_id=fields.Integer(required=True),
        name=fields.String(required=True),
        data_type=EnumField(EventDataType, required=True),
        desc=fields.String(missing=""),
        time_range=EnumField(MonitorEvent.TimeRange, missing=MonitorEvent.TimeRange.TIME_UNIT)
    ))
    def post(cls, **kwargs):
        """数据监控-事件管理-事件列表-新增"""
        data_type, time_range = kwargs["data_type"], kwargs["time_range"]
        if data_type == EventDataType.GAUGE and time_range == MonitorEvent.TimeRange.LATEST_DAYS:
            raise InvalidArgument(message='时间范围选择最近一段时间时，事件类型不能为瞬时值！')
        db.session.add(MonitorEvent(
            type_id=kwargs['type_id'],
            name=kwargs['name'],
            data_type=kwargs['data_type'],
            desc=kwargs['desc'],
            time_range=kwargs['time_range'],
        ))
        db.session.commit()


@ns.route("/event/batch")
@respond_with_code
class MonitorEventBatchResource(Resource):
    EVENT_TYPE_MAPPER = {
        "次数": EventDataType.COUNTER,
        "去重次数": EventDataType.UNIQ_COUNTER,
        "瞬间值": EventDataType.GAUGE
    }

    @classmethod
    def _format_data_type(cls, idx, data_type_str):
        event_data_type = cls.EVENT_TYPE_MAPPER.get(data_type_str)
        if not event_data_type:
            raise InvalidArgument(message=f"第{idx + 2}行 数据类型 {data_type_str} 未找到")
        return event_data_type

    @classmethod
    def _format_row_event_data(cls, rows, columns):
        for row in rows:
            for column in columns:
                if column == 'time_range':
                    if not row.get(column):
                        row[column] = MonitorEvent.TimeRange.TIME_UNIT.value
                if not row[column]:
                    continue
                if column == "tags":
                    row[column] = str(row[column]).replace("、", ",")
                row[column] = str(row[column]).strip()

        return rows

    @classmethod
    def post(cls):
        """数据监控-事件管理-事件列表-批量新增"""
        admin_user_id = g.user.id
        file = request.files.get("batch-upload")
        if not file:
            raise InvalidArgument("请上传文件")
        file_columns = ["event_type", "name", "data_type", "tags", "desc", "time_range"]
        # 已经过滤表头
        rows = get_table_rows(file, file_columns)
        if rows is None:
            raise InvalidArgument(message="文件格式错误,请检查文件模板")
        rows = cls._format_row_event_data(rows, ["event_type", "name", "data_type", "tags", "time_range"])
        event_types = {row["event_type"] for row in rows}
        event_type_mapper = {
            item.name: item for item in MonitorEventGroup.query.filter(MonitorEventGroup.name.in_(event_types)).all()
        }
        not_event_types = event_types - set(event_type_mapper.keys())
        for name in not_event_types:
            event_type = MonitorEventGroup(
                name=name,
                admin_user_id=admin_user_id
            )
            db.session.add(event_type)
            event_type_mapper[name] = event_type
        db.session.flush()
        tags = {format_event_tag_name(tag_name) for row in rows if row["tags"] for tag_name in row['tags'].split(",")}
        exist_tags = {name for name, in EventTag.query.with_entities(EventTag.name)}
        add_tag_names = list(tags - exist_tags)
        if add_tag_names:
            raise InvalidArgument(message=f"标签: {', '.join(add_tag_names)} 不存在 请创建后重试")
        events = []
        for idx,  row in enumerate(rows):
            data_type, time_range = cls._format_data_type(idx, row["data_type"]), row["time_range"]
            if data_type == EventDataType.GAUGE and time_range == MonitorEvent.TimeRange.LATEST_DAYS.value:
                raise InvalidArgument(message=f'批量上传数据错误！第{idx + 2}行 时间范围选择最近一段时间时，事件类型不能为瞬时值！')
            event = MonitorEvent.get_or_create(
                type_id=event_type_mapper[row["event_type"]].id,
                name=row["name"]
            )
            event.data_type = data_type
            event.desc = row["desc"]
            try:
                event.time_range = MonitorEvent.TimeRange(row["time_range"])
            except Exception:
                raise InvalidArgument(message=f'未识别的时间范围！第{idx + 2}行 可选项： 最近一段时间 或 每分钟/小时/天')
            events.append(event)
        db.session.add_all(events)
        db.session.commit()

        return dict(
            count=len(events)
        )


@ns.route('/event/<int:id_>')
@respond_with_code
class MonitorEventDetailResource(Resource):

    @classmethod
    def get(cls, id_):
        """数据监控-事件管理-事件列表-详情"""
        event = MonitorEvent.query.get(id_)
        if not event:
            raise InvalidArgument
        change_history = EventChangeHistory.query.filter(
            EventChangeHistory.event_id == event.id
        ).all()
        event_type_ids = set()
        for i in change_history:
            if i.key != EventChangeHistory.ChangeKey.EVENT_TYPE:
                continue
            detail = json.loads(i.detail)
            if old_value := detail["old_value"]:
                event_type_ids.add(int(old_value))
            if new_value := detail["new_value"]:
                event_type_ids.add(int(new_value))

        event_type_name_mapper = {
            id_: name for id_, name in MonitorEventGroup.query.filter(
                MonitorEventGroup.id.in_(event_type_ids)
            ).with_entities(
                MonitorEventGroup.id,
                MonitorEventGroup.name
            ).all()
        }
        admin_user_ids = [item.admin_user_id for item in change_history]
        change_items = []
        admin_name_mapper = MonitorEventResource.get_admin_user_display(admin_user_ids)
        for change in change_history:
            detail = json.loads(change.detail)
            old_value = detail.get("old_value")
            new_value = detail.get("new_value")
            if change.key == EventChangeHistory.ChangeKey.EVENT_TYPE:
                old_value = event_type_name_mapper.get(old_value)
                new_value = event_type_name_mapper.get(new_value)
            change_items.append({
                "id": change.id,
                "admin_user": admin_name_mapper.get(change.admin_user_id),
                "created_at": change.created_at,
                "key": change.key.value,
                "old_value": old_value,
                "new_value": new_value,
            })
        client = BizMonitorClient()
        tag_ids = client.get_metric_tags(event.id)
        tag_items = {
            tag_id: tag_name for tag_id, tag_name in EventTag.query.filter(EventTag.id.in_(tag_ids)).with_entities(
                EventTag.id,
                EventTag.name
            )
        }
        return dict(
            event_detail={
                "id": event.id,
                "data_type": event.data_type.name,
                "type_id": event.type_id,
                "name": event.name,
                "status": event.status.value,
                "desc": event.desc,
                "time_range": event.time_range.name,
            },
            change_history=change_items,
            tag_items={id_: tag_items.get(id_) for id_ in tag_ids}
        )

    @classmethod
    @ns.use_kwargs(dict(
        type_id=fields.Integer(required=True),
        name=fields.String(required=True),
        desc=fields.String(),
        time_range=EnumField(MonitorEvent.TimeRange, required=True)
    ))
    def put(cls, id_, **kwargs):
        """数据监控-事件管理-事件列表-修改"""
        event = MonitorEvent.query.get(id_)
        admin_user_id = g.user.id
        if not event:
            raise InvalidArgument
        data_type, time_range = event.data_type, kwargs["time_range"]
        if data_type == EventDataType.GAUGE and time_range == MonitorEvent.TimeRange.LATEST_DAYS:
            raise InvalidArgument(message='时间范围选择最近一段时间时，事件类型不能为瞬时值！')
        change_history = []
        if kwargs["type_id"] != event.type_id:
            change_history.append(EventChangeHistory(
                event_id=event.id,
                admin_user_id=admin_user_id,
                key=EventChangeHistory.ChangeKey.EVENT_TYPE,
                detail=json.dumps({"old_value": event.type_id, "new_value": kwargs["type_id"]}),
            ))
            event.type_id = kwargs["type_id"]
        if kwargs['name'] != event.name:
            change_history.append(EventChangeHistory(
                event_id=event.id,
                admin_user_id=admin_user_id,
                key=EventChangeHistory.ChangeKey.EVENT_NAME,
                detail=json.dumps({"old_value": event.name, "new_value": kwargs['name']}),
            ))
            event.name = kwargs['name']
        if (desc := kwargs.get("desc")) and desc != event.desc:
            change_history.append(EventChangeHistory(
                event_id=event.id,
                admin_user_id=admin_user_id,
                key=EventChangeHistory.ChangeKey.EVENT_DESC,
                detail=json.dumps({"old_value": event.desc, "new_value": desc}),
            ))
            event.desc = desc
        if kwargs['time_range'] != event.time_range:
            change_history.append(EventChangeHistory(
                event_id=event.id,
                admin_user_id=admin_user_id,
                key=EventChangeHistory.ChangeKey.EVENT_TIME_RANGE,
                detail=json.dumps({"old_value": event.time_range.value, "new_value": kwargs['time_range'].value}),
            ))
            event.time_range = kwargs['time_range']
        db.session.add_all(change_history)
        db.session.commit()

    @classmethod
    def delete(cls, id_):
        """数据监控-事件管理-事件列表-删除"""
        event = MonitorEvent.query.get(id_)
        if not event:
            raise InvalidArgument
        if event.status != MonitorEvent.Status.VALID:
            raise InvalidArgument
        db.session.add(EventChangeHistory(
            event_id=event.id,
            admin_user_id=g.user.id,
            key=EventChangeHistory.ChangeKey.EVENT_STATUS,
            detail=json.dumps({"old_value": event.status.value, "new_value": MonitorEvent.Status.DELETED.value}),
        ))
        event.status = MonitorEvent.Status.DELETED
        db.session.commit()


@ns.route('/event/<int:id_>/collection')
@respond_with_code
class MonitorEventCollection(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        is_del=fields.Boolean(default=False)
    ))
    def put(cls, id_, **kwargs):
        """数据监控-事件管理-收藏事件"""
        admin_user_id = g.user.id
        if kwargs["is_del"]:
            status = UserEventCollection.Status.DELETED
        else:
            status = UserEventCollection.Status.VALID
        collection = UserEventCollection.get_or_create(
            admin_user_id=admin_user_id,
            event_id=id_,
            auto_commit=True
        )
        collection.status = status
        db.session.commit()


@ns.route('/event/group')
@respond_with_code
class MonitorEventGroupResource(Resource):

    class GroupType(Enum):
        ALL = "全部"
        COLLECTION = "我的收藏"
        BROWSING = "最近浏览"

    @classmethod
    @ns.use_kwargs(dict(
        group_type=EnumField(GroupType, missing=GroupType.ALL)
    ))
    def get(cls, **kwargs):
        """数据游览-事件管理-事件分组-列表"""
        group_type = kwargs["group_type"]
        user_id = g.user.id
        model_type_query = None
        if group_type == cls.GroupType.BROWSING:
            model_type_query = UserEventBrowsingHistory.query.filter(
                UserEventBrowsingHistory.status == UserEventBrowsingHistory.Status.VALID,
                UserEventBrowsingHistory.admin_user_id == user_id,
                UserEventBrowsingHistory.created_at >= now() - timedelta(days=30)
            ).order_by(
                UserEventBrowsingHistory.id.desc()
            ).with_entities(
                UserEventBrowsingHistory.event_id
            ).all()

        elif group_type == cls.GroupType.COLLECTION:
            model_type_query = UserEventCollection.query.filter(
                UserEventCollection.admin_user_id == user_id,
                UserEventCollection.status == UserEventCollection.Status.VALID,
            ).with_entities(
                UserEventCollection.event_id.distinct()
            ).all()

        event_query = MonitorEvent.query.filter(
            MonitorEvent.status == MonitorEvent.Status.VALID
        )
        event_type_mapper = {
            item.id: item.name for item in MonitorEventGroup.query.all()
        }
        if group_type != cls.GroupType.ALL and not model_type_query:
            return {}

        if model_type_query:
            event_ids = [i for i, in model_type_query]
            event_query = event_query.filter(
                MonitorEvent.id.in_(event_ids)
            )
        rows = event_query.with_entities(
            MonitorEvent.name,
            MonitorEvent.type_id,
            MonitorEvent.id,
        )
        result = []
        if group_type == cls.GroupType.BROWSING:
            rows = sorted(rows, key=lambda x: [i for i, in model_type_query].index(x.id))
            for row in rows:
                result.append({
                    "label": f"{event_type_mapper[row.type_id]}--{row.name}",
                    "value": row.id
                })
            return result

        group_events = defaultdict(list)
        for row in rows:
            group_events[event_type_mapper[row.type_id]].append(row)
        for type_name, events in group_events.items():
            result.append({
                "label": type_name,
                "children": [
                    {"label": event.name, "value": event.id} for event in events
                ]
            })
        return dict(
            items=result,
            interval_config=MonitorEventChartResource.INTERVAL_CONFIG_MAPPER
        )


@ns.route('/event/chart')
@respond_with_code
class MonitorEventChartResource(Resource):

    class Interval(Enum):
        MINUTE = "minute"
        HOUR = "hour"
        DAY = "day"

    INTERVAL_CONFIG_MAPPER = {
        Interval.MINUTE.name: {
            "unit": "H",
            "select": [6, 24, 72],
            "default": 24
        },
        Interval.HOUR.name: {
            "unit": "D",
            "select": [6, 30, 90],
            "default": 30
        },
        Interval.DAY.name: {
            "unit": "Y",
            "select": [1, 2, 3, 5],
            "default": 1
        }
    }

    @classmethod
    def _add_browsing_history(cls, event_id, user_id):
        db.session_add_and_commit(UserEventBrowsingHistory(
            event_id=event_id,
            admin_user_id=user_id
        ))

    @classmethod
    def get_interval_time(cls, interval, interval_range):
        match interval:
            case cls.Interval.MINUTE:
                return timedelta(hours=interval_range)
            case cls.Interval.HOUR:
                return timedelta(days=interval_range)
            case cls.Interval.DAY:
                return timedelta(days=365 * interval_range)

    @classmethod
    @ns.use_kwargs(dict(
        event_id=fields.Integer(required=True),
        tags=fields.List(fields.Integer, missing=[0]),
        interval=EnumField(Interval, defult=Interval.MINUTE),
        interval_range=fields.Integer(missing=0, allow_none=True),
        start_time=TimestampField(allow_none=True),
        end_time=TimestampField(allow_none=True),
        page=fields.Integer,
        page_size=fields.Integer,
    ))
    def post(cls, **kwargs):
        """数据游览-事件管理-图表数据"""
        event_id = kwargs["event_id"]
        event = MonitorEvent.query.get(event_id)
        event_type = MonitorEventGroup.query.get(event.type_id)
        client = BizMonitorClient()
        tag_ids = client.get_metric_tags(event.id)
        tag_name_mapper = {
            tag_id: tag_name for tag_id, tag_name in EventTag.query.filter(EventTag.id.in_(tag_ids)).with_entities(
                EventTag.id,
                EventTag.name
            )
        }
        tag_items = {id_: tag_name_mapper.get(id_) for id_ in tag_ids}
        tag_items.update({0: "全部"})

        if event.time_range == MonitorEvent.TimeRange.TIME_UNIT:
            metrics_data = cls.get_time_unit_data(client, event, kwargs)
            event_detail_data = cls.get_event_detail_data(metrics_data, kwargs['tags'])
        else:
            metrics_data = cls.get_latest_days_data(client, event, kwargs, tag_items)
            event_detail_data = []

        cls._add_browsing_history(event_id, g.user.id)
        return dict(
            chart_data=metrics_data,
            event_detail={
                "event_type_name": event_type.name,
                "data_type": event.data_type.name,
                "name": event.name,
                "id": event.id,
                "desc": event.desc,
                "time_range": event.time_range.name,
                "is_star": bool(UserEventCollection.query.filter(
                    UserEventCollection.event_id == event.id,
                    UserEventCollection.admin_user_id == g.user.id,
                    UserEventCollection.status == UserEventCollection.Status.VALID
                ).first())
            },
            event_detail_data=event_detail_data,
            tag_items=tag_items
        )

    @classmethod
    def get_time_unit_data(cls, client, event, kwargs):
        event_id = event.id
        interval, tags = kwargs["interval"], kwargs["tags"]
        interval_range = kwargs['interval_range'] or cls.INTERVAL_CONFIG_MAPPER[interval.name]['default']
        _now = today_datetime() - timedelta(hours=8)
        end_time = _now
        start_time = _now - cls.get_interval_time(interval, interval_range)
        if params_start_time := kwargs.get("start_time"):
            start_time = params_start_time
        if params_end_time := kwargs.get("end_time"):
            end_time = params_end_time

        metrics_data = client.get_metrics_chart_data(
            start_time=int(start_time.timestamp()),
            end_time=int(end_time.timestamp()),
            metric_id=event_id,
            metric_type=event.data_type.value,
            period=interval.value,
            label_ids=tags
        )
        return metrics_data

    @classmethod
    def get_event_detail_data(cls, metrics_data, tags):
        time_mapper = defaultdict(dict)
        for id_, data in metrics_data.items():
            for time, value in data:
                if time not in time_mapper:
                    time_mapper[time] = {i: 0 for i in tags}
                time_mapper[time].update({id_: value})
        event_detail_data = [{
            "time": time,
            **tag_data
        } for time, tag_data in time_mapper.items()]
        return event_detail_data

    @classmethod
    def get_latest_days_data(cls, client, event, kwargs, tag_items):
        ret = client.get_rw_metric_data(
            metric_id=event.id,
            metric_type=event.data_type.value,
            label_ids=kwargs["tags"]
        )
        field_map = {
                1: 'one_day',
                7: 'seven_days',
                30: 'thirty_days',
                90: 'ninety_days',
        }
        res = []
        for tag_id, item_lis in ret.items():
            item = dict()
            for interval, val in item_lis:
                field = field_map.get(interval)
                if not field:
                    continue
                item[field] = val
            item['tag'] = tag_items[tag_id]
            res.append(item)
        return res


@ns.route('/event/type')
@respond_with_code
class MonitorEventTypeResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(missing=1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """数据监控-事件管理-事件类型列表"""
        page, limit = kwargs['page'], kwargs['limit']
        paginate = MonitorEventGroup.query.filter(
            MonitorEventGroup.status == MonitorEventGroup.Status.VALID
        ).paginate(page, limit)
        items = []
        admin_user_ids = [item.admin_user_id for item in paginate.items]
        admin_name_mapper = MonitorEventResource.get_admin_user_display(admin_user_ids)
        type_ids = [item.id for item in paginate.items]
        event_count_mapper = {
            type_id: event_count for type_id, event_count in MonitorEvent.query.filter(
                MonitorEvent.type_id.in_(type_ids)
            ).group_by(
                MonitorEvent.type_id
            ).with_entities(
                MonitorEvent.type_id,
                func.count(MonitorEvent.id).label("event_count")
            ).all()
        }
        for item in paginate.items:
            items.append({
                **item.to_dict(),
                "admin_user": admin_name_mapper.get(item.admin_user_id),
                "event_count": event_count_mapper.get(item.id, 0),
            })

        return dict(
            items=items,
            total=paginate.total,
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        remark=fields.String(),
    ))
    def post(cls, **kwargs):
        """数据监控-事件管理-事件类型列表-新建"""
        db.session.add(MonitorEventGroup(
            name=kwargs['name'],
            remark=kwargs.get('remark'),
            admin_user_id=g.user.id,
        ))
        db.session.commit()


@ns.route('/event/type/<int:id_>')
@respond_with_code
class MonitorEventTypeDetailResource(Resource):

    @classmethod
    def delete(cls, id_):
        """数据监控-事件管理-事件类型列表-删除"""
        event_group = MonitorEventGroup.query.get(id_)
        if not event_group:
            raise InvalidArgument
        event_group.status = MonitorEventGroup.Status.DELETED
        db.session.commit()


@ns.route("/event/tag")
@respond_with_code
class MonitorEventTagResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(missing=1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """数据监控-事件标签-事件标签-列表"""
        page, limit = kwargs['page'], kwargs['limit']
        paginate = EventTag.query.paginate(page, limit)
        admin_user_ids = [item.admin_user_id for item in paginate.items]
        admin_name_mapper = MonitorEventResource.get_admin_user_display(admin_user_ids)
        items = []
        for item in paginate.items:
            item_dict = item.to_dict()
            items.append({
                **item_dict,
                "admin_user": admin_name_mapper[item.admin_user_id]
            })

        return dict(
            total=paginate.total,
            items=items
        )

    @classmethod
    def check_event_tag_name_exists(cls, event_tag_name):
        if EventTag.query.filter(
            EventTag.name == func.binary(event_tag_name)
        ).first():
            raise InvalidArgument(message=f"{event_tag_name} 已存在")

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """数据监控-事件标签-事件标签-新增"""
        admin_user_id = g.user.id
        tag_name = format_event_tag_name(kwargs["name"])
        cls.check_event_tag_name_exists(tag_name)
        event_tag = EventTag(
            name=tag_name,
            admin_user_id=admin_user_id
        )
        db.session.add(event_tag)
        db.session.commit()


@ns.route("/event/tag/<int:id_>")
@respond_with_code
class EventTagDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True)
    ))
    def put(cls, id_, **kwargs):
        """数据监控-事件标签-事件标签-修改"""
        event_tag = EventTag.query.get(id_)
        if not event_tag:
            raise InvalidArgument
        if (name := kwargs["name"]) and name != event_tag.name:
            tag_name = format_event_tag_name(name)
            MonitorEventTagResource.check_event_tag_name_exists(tag_name)
            event_tag.name = tag_name
        db.session.commit()


@ns.route('/event/template')
@respond_with_code
class MonitorEventDataTemplateResource(Resource):

    export_headers = (
        {'field': 'event_type', Language.ZH_HANS_CN: '事件类别'},
        {'field': 'event_name', Language.ZH_HANS_CN: '事件名称'},
        {'field': 'data_type', Language.ZH_HANS_CN: '数据类型'},
        {'field': 'tags', Language.ZH_HANS_CN: '标签名'},
        {'field': 'desc', Language.ZH_HANS_CN: '事件描述'},
        {'field': 'time_range', Language.ZH_HANS_CN: '时间范围'}
    )

    @classmethod
    def get(cls):
        """数据监控-事件管理-事件模版"""
        return export_xlsx(
            filename='event-data-template',
            data_list=[],
            export_headers=cls.export_headers
        )


@ns.route('/event/status/batch-update')
@respond_with_code
class MonitorEventStatusBatchUpdateResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ids=fields.List(fields.Integer, required=True),
        update_status=EnumField(MonitorEvent.Status, required=True),
    ))
    def put(cls, **kwargs):
        """数据监控-事件管理-事件列表-批量更新状态"""
        update_status = kwargs['update_status']
        if update_status not in {
            MonitorEvent.Status.VALID,
            MonitorEvent.Status.DELETED,
        }:
            raise InvalidArgument('invalid update_status')

        # 修改为VALID时需确保当前状态为PENDING, 修改为DELETE时需确保当前状态为VALID或PENDING
        current_statuses = [MonitorEvent.Status.VALID, MonitorEvent.Status.PENDING]
        if update_status == MonitorEvent.Status.VALID:
            current_statuses = [MonitorEvent.Status.PENDING]

        for event in MonitorEvent.query.filter(
            MonitorEvent.id.in_(kwargs['ids']),
            MonitorEvent.status.in_(current_statuses),
        ).all():
            db.session.add(EventChangeHistory(
                event_id=event.id,
                admin_user_id=g.user.id,
                key=EventChangeHistory.ChangeKey.EVENT_STATUS,
                detail=json.dumps({'old_value': event.status.value, 'new_value': update_status.value}),
            ))
            event.status = update_status

        db.session.commit()
