# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from itertools import chain

from flask import g

from app.api.common import Namespace, respond_with_code, Resource, fields

from webargs import fields as wa_fields

from app.assets import list_all_assets
from app.business.utils import AssetComparator
from app.common.constants import AccountBalanceType

from app.business.vip import VipHelper
from app.business.balance.helper import  UserTopBalanceRankHelper
from app.exceptions import InvalidArgument
from app.models import InvestmentAccount, User
from app.models.pledge import PledgeAsset
from app.caches import MarketCache, PerpetualCoinTypeCache
from app.caches.margin import MarginAccountNameCache
from app.models.staking import StakingAccount
from app.utils import current_timestamp, today_timestamp_utc
from app.utils.date_ import date_to_datetime
from app.utils.helper import Struct
from app.schedules.reports.admin_async_download import async_send_asset_balances_email

ns = Namespace('Balance-Rank')


@ns.route('')
@respond_with_code
class UserBalanceRankResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            account_balance_type=fields.EnumField(AccountBalanceType),
            asset=fields.AssetField(),
            search_keyword=wa_fields.String(),
            date=wa_fields.Date("%Y-%m-%d"),
            page=fields.PageField(max_page=3000),
            limit=fields.LimitField(missing=100),
        )
    )
    def get(cls, **kwargs):
        """统计-资产排名"""
        params = Struct(**kwargs)
        if params.date:
            ts = int(date_to_datetime(params.date).timestamp())
        else:
            ts = current_timestamp(to_int=True)
        search_keyword = kwargs.get("search_keyword", "")
        user_ids = None
        filter_amm_account = False
        # 用户的AMM账户资产同时也在AMM系统账户的现货资产里，防止重复计算
        if not params.account_balance_type or params.account_balance_type == AccountBalanceType.SPOT:
            filter_amm_account = True
        if search_keyword:
            user_ids = User.search_for_users(search_keyword)

        yesterday_ts = ts - 86400

        asset_sorter = lambda x: sorted(x, key=lambda _asset: AssetComparator(_asset))
        margin_markets = MarginAccountNameCache.list_all_markets().values()
        spot_assets = list_all_assets()
        margin_assets = list(set(
            chain(*[((cache := MarketCache(market).dict)['base_asset'],
                     cache['quote_asset']) for market in margin_markets])))
        investment_assets = [v.asset for v in InvestmentAccount.query.with_entities(InvestmentAccount.asset)]
        staking_assets = [v.asset for v in StakingAccount.query.with_entities(StakingAccount.asset)]
        perpetual_assets = PerpetualCoinTypeCache().read_aside()
        pledge_assets = [v.asset for v in PledgeAsset.query.with_entities(PledgeAsset.asset)]
        account_balance_types = {
            AccountBalanceType.SPOT.name: '现货',
            AccountBalanceType.MARGIN.name: '杠杆',
            AccountBalanceType.INVESTMENT.name: '理财',
            AccountBalanceType.STAKING.name: '质押',
            AccountBalanceType.PERPETUAL.name: '合约',
            AccountBalanceType.AMM.name: '做市',
            AccountBalanceType.PLEDGE.name: '借贷',
        }
        assets_mapping = {
            AccountBalanceType.SPOT.name: asset_sorter(spot_assets),
            AccountBalanceType.MARGIN.name: asset_sorter(margin_assets),
            AccountBalanceType.INVESTMENT.name: asset_sorter(investment_assets),
            AccountBalanceType.STAKING.name: asset_sorter(staking_assets),
            AccountBalanceType.PERPETUAL.name: asset_sorter(perpetual_assets),
            AccountBalanceType.AMM.name: asset_sorter(spot_assets),
            AccountBalanceType.PLEDGE.name: asset_sorter(pledge_assets),
        }

        rank_data = UserTopBalanceRankHelper.get_user_rank_data(ts, params.account_balance_type, params.asset,
                                                                user_ids, params.page, params.limit)
        total_balance, total_user_usd = UserTopBalanceRankHelper.get_total_usd(
                        ts, params.account_balance_type, params.asset)
        query_user_ids = [v[0] for v in rank_data] + user_ids if user_ids \
                else [v[0] for v in rank_data]

        yesterday_rank_data = UserTopBalanceRankHelper.get_user_rank_data(
                    yesterday_ts, params.account_balance_type, params.asset,
                    query_user_ids, None, None)

        if filter_amm_account:
            total_user_usd -= UserTopBalanceRankHelper.get_total_usd(
                ts, AccountBalanceType.AMM, params.asset)[1]

        user_info_dict = {
            v.id: dict(email=v.main_user_email,
                        name=v.name,
                        is_sub_account=v.is_sub_account
                        ) for v in User.query.filter(User.id.in_(query_user_ids))
        }
        rank = (params.page - 1) * params.limit
        temp_table_data = []
        query_user_ids = []
        pie_chart_data = []
        head_user_total_data = Decimal()
        for (user_id, balance, total_usd) in rank_data:
            rank += 1
            query_user_ids.append(user_id)
            percent = total_usd / total_user_usd if total_user_usd else Decimal()
            temp_table_data.append(
                dict(
                    rank=rank,
                    user_id=user_id,
                    total_usd=total_usd,
                    percent=percent,
                    total_asset=balance,
                )
            )
            if percent > 0.001 and rank <= 20:
                head_user_total_data += total_usd
                pie_chart_data.append([user_id, total_usd])
        final_result = []
        pie_chart_data = [
            [user_info_dict.get(user_id, {}).get('email', ''), total_data] for
            user_id, total_data in pie_chart_data
        ]
        pie_chart_data.append(["other", total_user_usd - head_user_total_data])
        yesterday_data = defaultdict(Decimal)
        for (user_id, balance, total_yesterday_usd) in yesterday_rank_data:
            yesterday_data[user_id] = (balance, total_yesterday_usd)
        for data in temp_table_data:
            yesterday_row = yesterday_data.get(data['user_id'])
            final_result.append(
                dict(
                    data,
                    asset_change=data['total_asset'] - yesterday_row[0] if yesterday_row else '--',
                    usd_change=data['total_usd'] - yesterday_row[1] if yesterday_row else '--',
                    email=user_info_dict.get(data['user_id'], {}).get('email', ''),
                    name=user_info_dict.get(data['user_id'], {}).get('name', ''),
                    is_sub_account=user_info_dict.get(data['user_id'], {}).get('is_sub_account', False),
                )
            )
        if params.asset and params.asset == 'CET':
            for data in final_result:
                data['binding_cet'] = VipHelper.get_binding_cet_data(data['user_id'])
        
        total_user_count = UserTopBalanceRankHelper.get_total_user_count(ts, params.account_balance_type, params.asset)

        return dict(
            account_balance_types=account_balance_types,
            assets=assets_mapping,
            total=min(total_user_count, len(user_ids)) if user_ids else total_user_count,  # 当按用户搜索时，直接返回用户的数量
            items=final_result,
            total_usd=total_user_usd,
            asset_sum=total_balance,
            pie_chart_data=pie_chart_data
        )


@ns.route('/export')
@respond_with_code
class UserBalanceRankExportResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.AssetField(required=True),
            date=wa_fields.Date("%Y-%m-%d")
        )
    )
    def get(cls, **kwargs):
        """统计-资产排名-币种资产全量导出"""
        params = Struct(**kwargs)
        if params.date:
            ts = int(date_to_datetime(params.date).timestamp())
        else:
            ts = today_timestamp_utc()
        asset = params.asset
        if asset not in list_all_assets():
            raise InvalidArgument
        async_send_asset_balances_email.delay(g.user.id, params.asset, ts)
