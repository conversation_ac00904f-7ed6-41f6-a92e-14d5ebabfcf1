# -*- coding: utf-8 -*-

from webargs import fields as wa_fields

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import <PERSON>umField, PageField, LimitField, DateField
from app.common import ReportType, ADMIN_EXPORT_LIMIT
from app.models import (
    DailyAutoInvestSiteReport,
    MonthlyAutoInvestSiteReport, QuarterlyAutoInvestSiteReport,
)
from app.utils import export_xlsx

ns = Namespace("Auto Invest")


@ns.route("/site-report")
@respond_with_code
class AutoInvestSiteReportResource(Resource):

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "success_order_count", Language.ZH_HANS_CN: "成交订单笔数"},
        {"field": "fail_order_count", Language.ZH_HANS_CN: "失败订单笔数"},
        {"field": "manual_order_count", Language.ZH_HANS_CN: "立即定投次数"},
        {"field": "total_plan_count", Language.ZH_HANS_CN: "创建策略数"},
        {"field": "user_plan_count", Language.ZH_HANS_CN: "创建策略人数"},
        {"field": "running_plan_count", Language.ZH_HANS_CN: "运行策略数"},
        {"field": "user_order_count", Language.ZH_HANS_CN: "成交订单人数"},
        {"field": "running_user_plan_count", Language.ZH_HANS_CN: "运行策略人数"},
        {"field": "new_user_plan_count", Language.ZH_HANS_CN: "新增策略人数"},
        {"field": "total_trade_usd", Language.ZH_HANS_CN: "成交额"},
        {"field": "total_fee_usd", Language.ZH_HANS_CN: "手续费"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            order=EnumField(["report_date", "deal_usd"], missing="report_date"),
            page=PageField(unlimited=True),
            limit=LimitField(missing=50),
            export=wa_fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """ 报表-定投报表-全站报表 """
        if kwargs["report_type"] == ReportType.DAILY:
            model = DailyAutoInvestSiteReport
        elif kwargs["report_type"] == ReportType.MONTHLY:
            model = MonthlyAutoInvestSiteReport
        else:
            model = QuarterlyAutoInvestSiteReport

        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get("start_date"):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(model.report_date <= end_date)

        query = query.with_entities(
            model.report_date,
            model.success_order_count,
            model.fail_order_count,
            model.manual_order_count,
            model.total_plan_count,
            model.user_plan_count,
            model.running_plan_count,
            model.user_order_count,
            model.running_user_plan_count,
            model.new_user_plan_count,
            model.total_trade_usd,
            model.total_fee_usd,
        )
        total = 0
        if kwargs['export']:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = query.paginate(kwargs['page'], kwargs["limit"], error_out=False)
            items = pagination.items
            total = pagination.total
        result = []
        for i in items:
            result.append(
                {
                    "report_date": i.report_date,
                    "success_order_count": i.success_order_count,
                    "fail_order_count": i.fail_order_count,
                    "manual_order_count": i.manual_order_count,
                    "total_plan_count": i.total_plan_count,
                    "user_plan_count": i.user_plan_count,
                    "running_plan_count": i.running_plan_count,
                    "user_order_count": i.user_order_count,
                    "running_user_plan_count": i.running_user_plan_count,
                    "new_user_plan_count": i.new_user_plan_count,
                    "total_trade_usd": i.total_trade_usd,
                    "total_fee_usd": i.total_fee_usd,
                }
            )

        if kwargs['export']:
            return export_xlsx(
                filename='auto_invest_report',
                data_list=result,
                export_headers=cls.export_headers
            )

        return dict(
            data=result,
            total=total,
        )
