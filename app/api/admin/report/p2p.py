# -*- coding: utf-8 -*-
import copy

from marshmallow import fields

from app import Language
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import <PERSON>umField, PageField, LimitField, DateField
from app.business.p2p.report import P2pTradeQueryBiz
from app.common import ReportType
from app.models import MonthlyMerchantReport
from app.models.daily import (
    DailyP2pTradeReport, DailyMerchantReport
)
from app.utils import export_xlsx

ns = Namespace('Report - P2P')

P2P_EXPORT_HEADERS = [
    {"field": "report_date", Language.ZH_HANS_CN: "日期"},
    {"field": "put_user_count", Language.ZH_HANS_CN: "下单人数"},
    {"field": "deal_user_count", Language.ZH_HANS_CN: "成交人数"},
    {"field": "new_user_count", Language.ZH_HANS_CN: "下单新用户数"},
    {"field": "active_merchant_count", Language.ZH_HANS_CN: "活跃商家数"},
    {"field": "deal_merchant_count", Language.ZH_HANS_CN: "成交商家数"},
    {"field": "put_order_count", Language.ZH_HANS_CN: "下单笔数"},
    {"field": "accept_order_count", Language.ZH_HANS_CN: "接单笔数"},
    {"field": "deal_order_count", Language.ZH_HANS_CN: "成交笔数"},
    {"field": "appeal_order_count", Language.ZH_HANS_CN: "申诉笔数"},
    {"field": "deal_volume_usd", Language.ZH_HANS_CN: "成交市值USD"},
]


def get_export_headers(key_type):
    base_headers = copy.deepcopy(P2P_EXPORT_HEADERS)
    if key_type == DailyP2pTradeReport.KeyType.ASSET:
        base_headers.insert(
            1,
            {"field": "key", Language.ZH_HANS_CN: "币种"},
        )
        base_headers.insert(
            len(base_headers) - 1,
            {"field": "deal_amount", Language.ZH_HANS_CN: "成交数量"},
        )
    elif key_type == DailyP2pTradeReport.KeyType.FIAT:
        base_headers.insert(
            1,
            {"field": "key", Language.ZH_HANS_CN: "法币"},
        )
        base_headers.insert(
            len(base_headers) - 1,
            {"field": "deal_amount", Language.ZH_HANS_CN: "成交法币数量"},
        )
    return base_headers


@ns.route('')
@respond_with_code
class P2pReportResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, missing=ReportType.DAILY),
        key_type=EnumField(DailyP2pTradeReport.KeyType, missing=DailyP2pTradeReport.KeyType.ALL),
        key=fields.String(missing=None),
        page=PageField(),
        limit=LimitField(),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """报表-p2p交易报表-获取列表"""
        page, limit = kwargs["page"], kwargs["limit"]
        report_type, key_type = kwargs["report_type"], kwargs["key_type"]
        biz = P2pTradeQueryBiz(report_type, key_type, page, limit)
        paginate = biz.query_by_key(
            key=kwargs['key'],
            start_date=kwargs.get('start_date'),
            end_date=kwargs.get('end_date'),
            export=kwargs.get('export')
        )
        if kwargs.get('export'):
            base_headers = get_export_headers(key_type)
            return export_xlsx(
                filename="p2p_trade_report",
                data_list=biz.dump_export_data(paginate),
                export_headers=tuple(base_headers),
            )

        return dict(
            total=paginate.total,
            items=biz.dump_list(paginate.items),
            keys=[k for k in biz.get_keys() if k != "ALL"]
        )


@ns.route('/detail')
@respond_with_code
class P2pReportDetailResource(Resource):
    SORT_KEYS = {
        "put_user_count": "下单人数",
        "deal_user_count": "成交人数",
        "new_user_count": "下单新用户数",
        "active_merchant_count": "活跃商家数",
        "deal_merchant_count": "成交商家数",
        "put_order_count": "下单笔数",
        "accept_order_count": "接单笔数",
        "deal_order_count": "成交笔数",
        "appeal_order_count": "申诉笔数",
        "deal_amount": "成交量",
        "deal_volume_usd": "成交市值",
    }

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, missing=ReportType.DAILY),
        key_type=EnumField(DailyP2pTradeReport.KeyType, missing=DailyP2pTradeReport.KeyType.ALL),
        report_date=DateField(required=True, to_date=True),
        sort_by=EnumField(SORT_KEYS.keys(), missing="deal_volume_usd"),
        page=PageField(),
        limit=LimitField(),
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """报表-p2p交易报表-报表详情"""
        page, limit = kwargs["page"], kwargs["limit"]
        report_type, key_type = kwargs["report_type"], kwargs["key_type"]
        report_date = kwargs['report_date']
        biz = P2pTradeQueryBiz(report_type, key_type, page, limit)
        paginate = biz.query_by_date(
            sort_by=kwargs['sort_by'],
            report_date=report_date,
            export=kwargs.get('export')
        )
        if kwargs.get('export'):
            base_headers = get_export_headers(key_type)
            return export_xlsx(
                filename="p2p_trade_detail_report",
                data_list=biz.dump_export_data(paginate),
                export_headers=tuple(base_headers),
            )
        return dict(
            total=paginate.total,
            items=biz.dump_list(paginate.items),
            columns=cls.SORT_KEYS,
        )


@ns.route("/merchant")
@respond_with_code
class P2pMerchantReportResource(Resource):

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "merchant_count", Language.ZH_HANS_CN: "商家总数量"},
        {"field": "new_merchant_count", Language.ZH_HANS_CN: "新增商家数量"},
        {"field": "active_merchant_count", Language.ZH_HANS_CN: "活跃商家数量"},
        {"field": "deal_merchant_count", Language.ZH_HANS_CN: "成交商家数量"},
        {"field": "fiat_amount", Language.ZH_HANS_CN: "法币数量"},
        {"field": "adv_count", Language.ZH_HANS_CN: "广告数量"},
        {"field": "pay_channel_count", Language.ZH_HANS_CN: "支付方式数量"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, missing=ReportType.DAILY),
        page=PageField(),
        limit=LimitField(),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """报表-p2p交易报表-商家报表"""
        page, limit = kwargs["page"], kwargs["limit"]
        report_type = kwargs["report_type"]
        if report_type == ReportType.DAILY:
            model = DailyMerchantReport
        else:
            model = MonthlyMerchantReport
        query = model.query
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)
        query = query.order_by(model.report_date.desc())
        if kwargs.get('export'):
            return export_xlsx(
                filename="p2p_trade_detail_report",
                data_list=[i.to_dict() for i in query.all()],
                export_headers=cls.export_headers,
            )
        paginate = query.paginate(page=page, per_page=limit, error_out=False)
        return dict(
            total=paginate.total,
            items=[i.to_dict() for i in paginate.items],
        )
