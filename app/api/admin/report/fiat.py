# -*- coding: utf-8 -*-

from marshmallow import fields as mm_fields

from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField, PageField, LimitField
from app.business.fiat import get_fiat_partners
from app.common import ReportType
from app.models.daily import (
    DailyAssetFiatOrderReport, DailySiteFiatOrderReport, DailyThirdPartyFiatOrderReport,
)
from app.models.monthly import (
    MonthlyAssetFiatOrderReport, MonthlySiteFiatOrderReport, MonthlyThirdPartyFiatOrderReport,
)
from app.utils.helper import Struct

ns = Namespace('Report - Fiat')


@ns.route('/site-fiat-order-report')
@respond_with_code
class SiteFiatOrderReportResource(Resource):

    TYPE_DICT = {
        "ALL": "全部",
        "BUY": "入金",
        "SELL": "出金",
    }

    @classmethod
    @ns.use_kwargs(dict(
        is_third_party=mm_fields.Boolean(missing=False),
        third_party=mm_fields.String(allow_none=True),
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        type=EnumField(TYPE_DICT, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(max_limit=3000)
    ))
    def get(cls, **kwargs):
        """报表-法币报表-全站法币报表"""
        params = Struct(**kwargs)
        if params.report_type == ReportType.DAILY:
            if params.is_third_party:
                model = DailyThirdPartyFiatOrderReport
            else:
                model = DailySiteFiatOrderReport
        else:
            if params.is_third_party:
                model = MonthlyThirdPartyFiatOrderReport
            else:
                model = MonthlySiteFiatOrderReport

        q = model.query.order_by(
            model.report_date.desc(),
        )
        if params.start_date:
            q = q.filter(
                model.report_date >= params.start_date.date()
            )
        if params.end_date:
            q = q.filter(
                model.report_date <= params.end_date.date()
            )
        if params.is_third_party:
            q = q.filter(
                # 产品要求申请笔数大于0才展示
                model.apply_count > 0
            )
        if params.third_party:
            q = q.filter(
                model.third_party == params.third_party,
            )
        if params.type:
            q = q.filter(
                model.report_type == params.type,
            )

        records = q.paginate(params.page, params.limit, error_out=False)

        items = []
        for v in records.items:
            _dict = dict(
                id=v.id,
                report_date=v.report_date,
                apply_user_count=v.apply_user_count,
                deal_user_count=v.deal_user_count,
                apply_count=v.apply_count,
                deal_count=v.deal_count,
                deal_usd=v.deal_usd,
                user_count=v.user_count,
                new_user_count=v.new_user_count,
            )
            if params.is_third_party:
                _dict["third_party"] = v.third_party
            items.append(_dict)

        return dict(
            total=q.count(),
            items=items,
            third_party_list=get_fiat_partners(),
            extra=dict(types=cls.TYPE_DICT),
        )


@ns.route("/site-fiat-order-detail-report")
@respond_with_code
class SiteFiatOrderDetailReportResource(Resource):

    SORT_KEYS = {
        "apply_user_count": "申请人数",
        "deal_user_count": "成交人数",
        "apply_count": "申请人数",
        "deal_count": "成交笔数",
        "deal_usd": "成交市值",
        "user_count": "使用人数",
        "new_user_count": "新增人数"
    }

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        is_third_party=mm_fields.Boolean(missing=False),
        type=EnumField(SiteFiatOrderReportResource.TYPE_DICT, required=True),
        report_date=mm_fields.DateTime(format="%Y-%m-%d"),
        sort_by=EnumField(SORT_KEYS.keys(), missing="deal_usd"),
        page=PageField(unlimited=True),
        limit=LimitField(max_limit=3000)
    ))
    def get(cls, **kwargs):
        """报表-法币报表-法币服务商报表详情"""
        params = Struct(**kwargs)
        if params.report_type == ReportType.DAILY:
            model = DailyThirdPartyFiatOrderReport
        else:
            model = MonthlyThirdPartyFiatOrderReport

        q = model.query.filter(
            model.report_date == params.report_date,
        )

        if params.is_third_party:
            q = q.filter(
                # 产品要求申请笔数大于0才展示
                model.apply_count > 0
            )
        if params.type:
            q = q.filter(
                model.report_type == params.type,
            )

        q = q.order_by(
            getattr(model, params.sort_by).desc()
        )
        records = q.paginate(params.page, params.limit, error_out=False)
        items = [
                dict(
                    id=v.id,
                    report_date=v.report_date,
                    apply_user_count=v.apply_user_count,
                    deal_user_count=v.deal_user_count,
                    apply_count=v.apply_count,
                    deal_count=v.deal_count,
                    deal_usd=v.deal_usd,
                    user_count=v.user_count,
                    new_user_count=v.new_user_count,
                    third_party=v.third_party,
                )
                for v in records.items
        ]
        return dict(
            total=q.count(),
            items=items,
            sort_keys=cls.SORT_KEYS,
        )


@ns.route('/asset-fiat-order-report')
@respond_with_code
class AssetFiatOrderReportResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        asset=mm_fields.String(),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-法币报表-币种法币报表"""
        params = Struct(**kwargs)
        assets = [
            v.asset for v in
            DailyAssetFiatOrderReport.query.with_entities(
                DailyAssetFiatOrderReport.asset.distinct().label('asset')
            )
        ]
        if params.report_type == ReportType.DAILY:
            q = DailyAssetFiatOrderReport.query.order_by(
                DailyAssetFiatOrderReport.report_date.desc(),
            )
            if params.asset:
                q = q.filter(
                    DailyAssetFiatOrderReport.asset == params.asset
                )
            if params.start_date:
                q = q.filter(
                    DailyAssetFiatOrderReport.report_date >=
                    params.start_date.date()
                )
            if params.end_date:
                q = q.filter(
                    DailyAssetFiatOrderReport.report_date <=
                    params.end_date.date()
                )
        else:
            q = MonthlyAssetFiatOrderReport.query.order_by(
                MonthlyAssetFiatOrderReport.report_date.desc()
            )
            if params.asset:
                q = q.filter(
                    MonthlyAssetFiatOrderReport.asset == params.asset
                )
            if params.start_date:
                q = q.filter(
                    MonthlyAssetFiatOrderReport.report_date >=
                    params.start_date.date()
                )
            if params.end_date:
                q = q.filter(
                    MonthlyAssetFiatOrderReport.report_date <=
                    params.end_date.date()
                )
        records = q.paginate(params.page, params.limit, error_out=False)

        items = [
            dict(
                asset=v.asset,
                report_date=v.report_date,
                apply_user_count=v.apply_user_count,
                deal_user_count=v.deal_user_count,
                apply_count=v.apply_count,
                deal_count=v.deal_count,
                deal_usd=v.deal_usd,
            )
            for v in records.items
        ]

        return dict(
            total=q.count(),
            items=items,
            assets=assets
        )


@ns.route('/asset-fiat-order-report-detail')
@respond_with_code
class AssetFiatOrderReportDetail(Resource):

    SORT_KEYS = {
        "apply_user_count": "申请人数",
        "deal_user_count": "成交人数",
        "apply_count": "使用人数",
        "deal_count": "成交笔数",
        "deal_usd": "成交市值",
    }

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        report_date=mm_fields.DateTime(format="%Y-%m-%d"),
        sort_by=EnumField(SORT_KEYS.keys(), missing="deal_usd"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-法币报表-币种法币详情报表"""
        params = Struct(**kwargs)
        if params.report_type == ReportType.DAILY:
            model = DailyAssetFiatOrderReport
        else:
            model = MonthlyAssetFiatOrderReport

        q = model.query.filter(
            model.report_date == params.report_date,
        )
        q = q.order_by(
            getattr(model, params.sort_by).desc()
        )
        records = q.paginate(params.page, params.limit, error_out=False)
        items = [
            v.to_dict()
            for v in records.items
        ]
        return dict(
            total=q.count(),
            items=items,
            sort_keys=cls.SORT_KEYS,
        )
