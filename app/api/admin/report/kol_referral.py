from datetime import datetime

from flask import g, request
from webargs import fields

from app import Language
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import LimitField, PageField
from app.business.auth import get_admin_user_name_map
from app.exceptions import InvalidArgument
from app.models import KolReferralExport, Referral, User, db
from app.schedules.reports.admin_async_download import \
    async_download_kol_referral_report
from app.utils import batch_iter, export_xlsx, validate_email
from app.utils.importer import get_table_rows

ns = Namespace("KOL Referral")


@ns.route("/template")
@respond_with_code
class KOLReferralTemplateResource(Resource):

    export_headers = (
        {"field": "email", Language.ZH_HANS_CN: "邮箱", Language.EN_US: "Email"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID", Language.EN_US: "UserID"},
        {
            "field": "referral_code",
            Language.ZH_HANS_CN: "邀请码",
            Language.EN_US: "ReferralCode",
        },
    )

    @classmethod
    def get(cls):
        """用户-邀请返佣-KOL邀请数据导出-下载模板"""
        return export_xlsx(
            filename="kol-referral-template",
            data_list=[],
            export_headers=cls.export_headers,
        )


@ns.route("")
@respond_with_code
class KOLReferralResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(page=PageField(missing=1), limit=LimitField(missing=50)))
    def get(cls, **kwargs):
        """用户-邀请返佣-KOL邀请数据导出-列表展示"""
        query = KolReferralExport.query.filter(
            KolReferralExport.status != KolReferralExport.Status.DELETED,
        ).order_by(KolReferralExport.id.desc())
        page = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        user_ids = {obj.admin_user_id for obj in page.items}
        name_map = get_admin_user_name_map(user_ids)

        items = []
        status_map = {
            KolReferralExport.Status.PROCESSING: "待生成",
            KolReferralExport.Status.FINISHED: "已生成",
            KolReferralExport.Status.DELETED: "已删除",
        }
        type_map = {item: item.value for item in KolReferralExport.Type}
        for item in page.items:
            tmp = item.to_dict()
            del tmp["user_ids"]
            tmp["status"] = status_map.get(item.status, "--")
            tmp["type"] = type_map.get(item.type, "新用户") # 旧数据为空，认为是新用户
            tmp["operator"] = name_map.get(item.admin_user_id) or "--"
            items.append(tmp)
        return dict(
            items=items,
            total=page.total,
        )

    def _get_time_from_form(field_name: str) -> datetime | None:
        time_str = request.form.get(field_name, None)
        if not time_str:
            return None

        try:
            _time = datetime.strptime(time_str, "%Y-%m-%d")
            return _time
        except ValueError:
            return None

    @classmethod
    def post(cls):
        """用户-邀请返佣-KOL邀请数据导出-批量上传"""
        if not (title := request.form.get("title")):
            raise InvalidArgument

        start_date = cls._get_time_from_form("start_date")
        if not start_date:
            raise InvalidArgument("开始时间不能为空")
        end_date = cls._get_time_from_form("end_date")
        if not end_date:
            raise InvalidArgument("结束时间不能为空")

        type_ = request.form.get("type")
        if not type_:
            raise InvalidArgument("用户要求不能为空")

        file_ = request.files.get("batch-upload")
        file_columns = ["email", "user_id", "refer_code"]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, "message", "文件解析失败，请重新生成并上传")
            raise InvalidArgument(message=msg)

        # 添加合法的邮箱
        input_emails, input_user_ids, input_refer_codes = [], [], []
        for idx, row in enumerate(rows):
            if email := row["email"]:
                if not validate_email(str(email)):
                    raise InvalidArgument(
                        message=f"文件内第{idx + 2}行有非法的email格式或email长度超过64，请修改后重新提交！"
                    )
                input_emails.append(str(email))

            if user_id := row["user_id"]:
                try:
                    input_user_ids.append(int(user_id))
                except ValueError:
                    raise InvalidArgument(
                        message=f"文件内第{idx + 2}行有非法的user_id，请修改后重新提交！"
                    )
            if refer_code := row["refer_code"]:
                input_refer_codes.append(str(refer_code))

        user_ids = []
        for chunk_emails in batch_iter(input_emails, 500):
            chunk_res = (
                User.query.with_entities(User.id, User.email)
                .filter(User.email.in_([email.lower() for email in chunk_emails]))
                .all()
            )
            sorted_res = sorted(
                chunk_res,
                key=lambda x: (
                    chunk_emails.index(x[1]) if x[1] in chunk_emails else float("inf")
                ),
            )
            for user_id, _ in sorted_res:
                user_ids.append(user_id)

        # 添加合法的用户ID
        for _chunk_user_ids in batch_iter(input_user_ids, 500):
            chunk_res = (
                User.query.with_entities(User.id)
                .filter(User.id.in_(_chunk_user_ids))
                .all()
            )
            sorted_res = sorted(
                chunk_res,
                key=lambda x: (
                    _chunk_user_ids.index(x[0])
                    if x[0] in _chunk_user_ids
                    else float("inf")
                ),
            )
            for (user_id,) in sorted_res:
                user_ids.append(user_id)

        # 添加合法的 referral_code
        for chunk_refer_codes in batch_iter(input_refer_codes, 500):
            chunk_res = (
                Referral.query.with_entities(Referral.user_id, Referral.code)
                .filter(Referral.code.in_(chunk_refer_codes))
                .all()
            )
            sorted_res = sorted(
                chunk_res,
                key=lambda x: (
                    chunk_refer_codes.index(x[1])
                    if x[1] in chunk_refer_codes
                    else float("inf")
                ),
            )

            for user_id, _ in sorted_res:
                user_ids.append(user_id)

        if not user_ids:
            return dict(count=0)

        kol_referral_report = KolReferralExport(
            title=title,
            start_time=start_date,
            end_time=end_date,
            type=KolReferralExport.Type[type_],
            status=KolReferralExport.Status.PROCESSING,
            admin_user_id=g.user.id,
        )
        kol_referral_report.set_user_ids(user_ids=user_ids)
        db.session_add_and_commit(kol_referral_report)
        return dict(count=len(rows))

    @classmethod
    @ns.use_kwargs(dict(id=fields.Integer(required=True)))
    def delete(cls, **kwargs):
        """用户-邀请返佣-KOL邀请数据导出-删除"""
        id_ = kwargs["id"]
        obj = KolReferralExport.query.get(id_)
        if not obj:
            raise InvalidArgument

        obj.status = KolReferralExport.Status.DELETED
        db.session.commit()


@ns.route("/download")
@respond_with_code
class KOLReferralReportResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(id=fields.Integer(required=True)))
    def get(cls, **kwargs):
        """用户-邀请返佣-KOL邀请数据导出-下载数据"""
        id_ = kwargs["id"]
        obj = KolReferralExport.query.get(id_)
        if not obj:
            raise InvalidArgument

        if obj.status != KolReferralExport.Status.FINISHED:
            raise InvalidArgument("请等待数据生成后再下载")

        user_emails = (
            User.query.filter(User.id == obj.admin_user_id)
            .with_entities(User.id, User.email)
            .all()
        )
        user_email_dict = dict(user_emails)

        if user_email_dict:
            async_download_kol_referral_report.delay(
                user_email_dict[obj.admin_user_id],
                obj.id,
            )
