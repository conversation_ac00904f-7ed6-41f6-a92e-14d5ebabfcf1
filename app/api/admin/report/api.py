import datetime
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from enum import Enum

from marshmallow import fields as mm_fields
from sqlalchemy import func

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField, DateField, PageField, LimitField, TimestampField
from app.business.clients.api_gateway import ApiGatewayClient
from app.common import ADMIN_EXPORT_LIMIT, TradeBusinessType
from app.schedules.reports.api_access_rank import ApiStaticReport
from app.models import User, UserApiFrequencyLimitRecord, DailyApiAccessRankReport, UserTradeSummary, \
    UserTradeFeeSummary, SubAccount, UserLongLimiterRecord
from app.models.api_resource import ApiResource, HourApiResource, DailyApiResource, \
    ApiRequest, HourApiRequest, DailyApiRequest
from app.utils import now, current_timestamp, batch_iter, export_xlsx, today, format_percent, datetime_to_time, \
    amount_to_str
from app.utils.date_ import date_to_datetime

ns = Namespace('Report - Api')


@ns.route('')
@respond_with_code
class ApiRealTimeResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        api_type=EnumField(UserApiFrequencyLimitRecord.ApiGroups, missing=UserApiFrequencyLimitRecord.ApiGroups.ORDER),
        user_id=mm_fields.Integer(missing=None)
    ))
    def get(cls, **kwargs):
        """统计-API统计-实时访问统计"""
        api_type, user_id = kwargs["api_type"], None
        if user_id := kwargs.get("user_id"):
            user_id = user_id
        api_client = ApiGatewayClient()
        data = api_client.req_trending(api_type, user_id)
        return {
            "items": data,
            "api_types": UserApiFrequencyLimitRecord.ApiGroups,
            "timestamp": now().timestamp()
        }


@ns.route('/history')
@respond_with_code
class ApiHistoryResource(Resource):
    SYSTEM_USER_ID = 0

    class Interval(Enum):
        MINUTE = "minute"
        HOUR = "hour"
        DAY = "day"

    INTERVAL_DAYS_MODEL_MAPPER = {
        Interval.MINUTE: (1, ApiRequest),
        Interval.HOUR: (30, HourApiRequest),
        Interval.DAY: (365, DailyApiRequest)
    }

    @classmethod
    @ns.use_kwargs(dict(
        api_type=EnumField(UserApiFrequencyLimitRecord.ApiGroups, missing=UserApiFrequencyLimitRecord.ApiGroups.ORDER),
        user_id=mm_fields.Integer(missing=None),
        start_time=TimestampField(allow_none=True),
        end_time=TimestampField(allow_none=True),
        interval=EnumField(Interval, defult=Interval.MINUTE),
    ))
    def get(cls, **kwargs):
        """统计-API统计-历史访问统计"""
        api_type, interval = kwargs['api_type'], kwargs['interval']
        user_id = kwargs.get("user_id") or cls.SYSTEM_USER_ID
        interval_days, model = cls.INTERVAL_DAYS_MODEL_MAPPER[interval]
        end_time = now()
        start_time = end_time - timedelta(days=interval_days)
        if params_start_time := kwargs.get("start_time"):
            start_time = params_start_time
        if params_end_time := kwargs.get("end_time"):
            end_time = params_end_time

        query = model.query.filter(
            model.time >= start_time,
            model.time < end_time,
            model.group == api_type.name,
            model.user_id == user_id,
        ).order_by(model.time).all()
        data = []
        for row in query:
            data.append([row.time.timestamp(), row.req_count])

        return {
            "items": data,
            "api_types": UserApiFrequencyLimitRecord.ApiGroups,
        }


@ns.route("/rank")
@respond_with_code
class ApiRankResource(Resource):
    HourType = [1, 6, 12, 24]

    @classmethod
    @ns.use_kwargs(dict(
        hour_type=EnumField(enum=list(map(str, HourType)), missing="24"),
        api_type=EnumField(UserApiFrequencyLimitRecord.ApiGroups, missing=UserApiFrequencyLimitRecord.ApiGroups.ORDER),
    ))
    def get(cls, **kwargs):
        """统计-API统计-访问排名统计"""
        hour = int(kwargs["hour_type"])
        api_client = ApiGatewayClient()
        data = api_client.req_top_n(kwargs["api_type"], hour)
        user_ids = [i for i, _ in data]
        sub_mapper = {sid: mid for mid, sid in SubAccount.query.filter(
            SubAccount.user_id.in_(user_ids)
        ).with_entities(
            SubAccount.main_user_id,
            SubAccount.user_id
        ).all()}
        main_user_ids = set(sub_mapper.values()) | (set(user_ids) - set(sub_mapper.keys()))
        user_query = User.query.filter(
            User.id.in_(main_user_ids)
        ).with_entities(
            User.id,
            User.email
        ).all()
        user_email_mapper = {_id: email for _id, email in user_query}
        items = []
        timestamp = current_timestamp(to_int=True)
        sec = (hour * 3600 - (3600 - timestamp % 3600))
        for index, (_id, count) in enumerate(data):
            main_user_id = sub_mapper.get(_id, _id)
            items.append({
                "rank": index + 1,
                "user_id": _id,
                "main_user_id": main_user_id,
                "email": user_email_mapper.get(main_user_id),
                "count": count,
                "rate": count // sec if sec else 0
            })
        return {
            "items": items,
            "hour_types": {str(i): f"最近{i}H" for i in cls.HourType},
            "api_types": UserApiFrequencyLimitRecord.ApiGroups,
            "timestamp": timestamp
        }


@ns.route("/access-rank")
@respond_with_code
class ApiAccessRankResource(Resource):

    USER_TYPES = {
        User.UserType.NORMAL: '普通用户',
        User.UserType.INTERNAL_MAKER: '内部做市商',
        User.UserType.EXTERNAL_MAKER: '外部做市商',
        User.UserType.EXTERNAL_SPOT_MAKER: '现货做市商',
        User.UserType.EXTERNAL_CONTRACT_MAKER: '合约做市商',
        User.UserType.EXTERNAL: '机构账号',
        User.UserType.SUB_ACCOUNT: '子账户'
    }

    export_headers = (
        {"field": "rank", Language.ZH_HANS_CN: "排名"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "count", Language.ZH_HANS_CN: "访问次数"},
        {"field": "rate", Language.ZH_HANS_CN: "访问频率"},
        {"field": "deal_amount", Language.ZH_HANS_CN: "总交易额"},
        {"field": "deal_rate", Language.ZH_HANS_CN: "总交易额占比"},
        {"field": "maker_amount", Language.ZH_HANS_CN: "maker交易额"},
        {"field": "maker_rate", Language.ZH_HANS_CN: "maker交易额占比"},
        {"field": "taker_amount", Language.ZH_HANS_CN: "taker交易额"},
        {"field": "taker_rate", Language.ZH_HANS_CN: "taker交易额占比"},
        {"field": "resource_efficiency", Language.ZH_HANS_CN: "资源效率"},
        {"field": "total_trade_fee", Language.ZH_HANS_CN: "总手续费"},
        {"field": "trade_fee_rate", Language.ZH_HANS_CN: "手续费占比"},
        {"field": "avg_active_time", Language.ZH_HANS_CN: "订单平均存活时间"},
        {"field": "accounts", Language.ZH_HANS_CN: "子账号数"},
        {"field": "active_accounts", Language.ZH_HANS_CN: "活跃子账号数"},
        {"field": "user_type", Language.ZH_HANS_CN: "帐号类型"}
    )

    @classmethod
    def get_site_trade_data(cls, start_date, end_data, system):
        report_trade_mapper = defaultdict(dict)
        group_query = UserTradeSummary.query.filter(
            UserTradeSummary.system == UserTradeSummary.System[system.name],
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date <= end_data
        ).group_by(
            UserTradeSummary.report_date
        ).with_entities(
            UserTradeSummary.report_date,
            func.sum(UserTradeSummary.trade_amount),
            func.sum(UserTradeSummary.maker_amount),
            func.sum(UserTradeSummary.taker_amount)
        ).all()
        for report_date, site_trade_amount, site_maker_amount, site_taker_amount in group_query:
            report_trade_mapper[report_date] = {
                "site_trade_amount": site_trade_amount,
                "site_maker_amount": site_maker_amount,
                "site_taker_amount": site_taker_amount
            }
        return report_trade_mapper

    @classmethod
    def get_site_trade_fee(cls,  start_date, end_data, system):
        group_query = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.system == UserTradeFeeSummary.System[system.name],
            UserTradeFeeSummary.report_date >= start_date,
            UserTradeFeeSummary.report_date <= end_data
        ).group_by(
            UserTradeFeeSummary.report_date
        ).with_entities(
            UserTradeFeeSummary.report_date,
            func.sum(UserTradeFeeSummary.trade_fee_amount),
        ).all()
        return {date_: fee for date_, fee in group_query}

    @classmethod
    def _get_trade_format_percent(cls, user_data, site_data, key):
        if not (key_data := site_data.get(key)):
            return "0%"
        return format_percent(user_data / key_data, 2)

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer,
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        system=EnumField(DailyApiAccessRankReport.System, missing=DailyApiAccessRankReport.System.PERPETUAL),
        limit=LimitField(missing=50),
        sort_name=EnumField([
            'report_date', 'count', 'taker_amount', 'maker_amount', 'deal_amount',
            'resource_efficiency', 'effective_rate', 'saturation_rate',
            'total_trade_fee', 'avg_active_time', 'accounts', 'active_accounts'
        ], missing='count'),
        export=mm_fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """统计-API统计-资源占用数据报表"""
        system = kwargs['system']
        query = DailyApiAccessRankReport.query.filter(
            DailyApiAccessRankReport.system == system,
        )
        sort_name = kwargs['sort_name']
        page, limit = kwargs['page'], kwargs['limit']
        if user_id := kwargs.get('user_id'):
            query = query.filter(DailyApiAccessRankReport.user_id == user_id)
        elif not kwargs.get('start_date') and not kwargs.get('end_date'):
            yesterday = today() - timedelta(days=1)
            query = query.filter(DailyApiAccessRankReport.report_date == yesterday)
        else:
            if start_date := kwargs.get('start_date'):
                query = query.filter(
                    DailyApiAccessRankReport.report_date >= start_date)
            if end_date := kwargs.get('end_date'):
                query = query.filter(
                    DailyApiAccessRankReport.report_date <= end_date)

        export = kwargs.get("export")
        if export:
            records = query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(records)
        else:
            page_rows = query.paginate(page, limit, error_out=False)
            total = page_rows.total
            records = page_rows.items

        user_ids = {r.user_id for r in records}
        report_dates = {i.report_date for i in records}
        site_trade_data, site_fee_data = {}, {}
        if report_dates:
            site_trade_data = cls.get_site_trade_data(min(report_dates), max(report_dates), system)
            site_fee_data = cls.get_site_trade_fee(min(report_dates), max(report_dates), system)

        user_email_map = {}
        user_remark_map = {}
        user_type_map = {}
        for batch_user_ids in batch_iter(user_ids, 1000):
            users = (
                User.query.filter(
                    User.id.in_(batch_user_ids)).with_entities(
                    User.email,
                    User.remark,
                    User.id,
                    User.user_type,
                ).all()
            )
            user_email_map.update({u.id: u.email for u in users})
            user_remark_map.update({u.id: u.remark for u in users})
            user_type_map.update({u.id: cls.USER_TYPES[u.user_type] for u in users})

        res = []
        for item in records:
            site_trade = site_trade_data.get(item.report_date, {})
            site_fee = site_fee_data.get(item.report_date, Decimal())
            res.append(dict(
                user_id=item.user_id,
                email=user_email_map[item.user_id],
                remark=user_remark_map.get(item.user_id, ''),
                report_date=item.report_date,
                count=item.count,
                rate=item.rate,
                deal_amount=item.deal_amount,
                deal_rate=cls._get_trade_format_percent(item.deal_amount, site_trade, "site_trade_amount"),
                maker_amount=item.maker_amount,
                maker_rate=cls._get_trade_format_percent(item.maker_amount, site_trade, "site_maker_amount"),
                taker_amount=item.taker_amount,
                taker_rate=cls._get_trade_format_percent(item.taker_amount, site_trade, "site_taker_amount"),
                resource_efficiency=item.resource_efficiency,
                total_trade_fee=amount_to_str(item.total_trade_fee, 2),
                trade_fee_rate=format_percent(item.total_trade_fee / site_fee, 2) if site_fee else "0%",
                avg_active_time=item.avg_active_time,
                rank=item.rank,
                accounts=item.accounts,
                active_accounts=item.active_accounts,
                user_type=user_type_map[item.user_id],
                effective_rate=item.effective_rate,
                saturation_rate=item.saturation_rate,
            ))
        res.sort(
            key=lambda x: x[sort_name], reverse=True)

        if export:
            for i in res:
                i["report_date"] = i["report_date"].strftime("%Y-%m")
            return export_xlsx(
                filename="api_access_rank_list",
                data_list=res,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=res,
        )


class ApiSaturationChartMixin:
    SYSTEM_USER_ID = 0
    SYSTEM_PERPETUAL_MAX_REQ_BY_SECOND = 9000
    SYSTEM_SPOT_MAX_REQ_BY_SECOND = 12000

    class Interval(Enum):
        MINUTE = "minute"
        HOUR = "hour"
        DAY = "day"

    INTERVAL_DAYS_MODEL_MAPPER = {
        Interval.MINUTE: (1, ApiResource),
        Interval.HOUR: (30, HourApiResource),
        Interval.DAY: (365, DailyApiResource)
    }

    @classmethod
    def get_limiter_by_interval(cls, interval: Interval, rate_second):
        if interval == interval.MINUTE:
            return rate_second * 60
        elif interval == interval.HOUR:
            return rate_second * 60 * 60
        else:
            return rate_second * 60 * 60 * 24


@ns.route("/saturation/chart")
@respond_with_code
class ApiSaturationChartResource(ApiSaturationChartMixin, Resource):
    GROUPS = [
        UserApiFrequencyLimitRecord.ApiGroups.ORDER,
        UserApiFrequencyLimitRecord.ApiGroups.ORDERS,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDER,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDERS
    ]

    @classmethod
    def get_user_api_saturation(cls, model, user_id, group, start_time, end_time):
        return {m.time: m for m in model.query.filter(
            model.time < end_time,
            model.time >= start_time,
            model.user_id == user_id,
            model.group == group.name,
        ).with_entities(
            model.time,
            model.req_count,
            model.put_order_count,
            model.rel_count,
            model.active_total_time
        ).order_by(
            model.time
        ).all()}

    @classmethod
    def get_user_frequency_limiter(cls, user_id, group=UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDER):
        user_record = UserLongLimiterRecord.query.filter(
            UserLongLimiterRecord.user_id == user_id,
            UserLongLimiterRecord.group == group,
            UserLongLimiterRecord.status == UserLongLimiterRecord.Status.VALID,
        ).first()
        if not user_record:
            user_record = UserLongLimiterRecord.query.filter(
                UserLongLimiterRecord.user_id == 0,
                UserLongLimiterRecord.group == group,
                UserLongLimiterRecord.status == UserLongLimiterRecord.Status.VALID,
            ).first()
        return user_record

    @classmethod
    def get_sys_limiter_count(cls, group: UserApiFrequencyLimitRecord.ApiGroups):
        if group.name.startswith("PERPETUAL"):
            return cls.SYSTEM_PERPETUAL_MAX_REQ_BY_SECOND
        return cls.SYSTEM_SPOT_MAX_REQ_BY_SECOND

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(),
        group_type=EnumField(UserApiFrequencyLimitRecord.ApiGroups, missing=UserApiFrequencyLimitRecord.ApiGroups.ORDER),
        start_time=TimestampField(allow_none=True),
        end_time=TimestampField(allow_none=True),
        interval=EnumField(ApiSaturationChartMixin.Interval, defult=ApiSaturationChartMixin.Interval.MINUTE),
    ))
    def get(cls, **kwargs):
        """统计-API统计-资源占用数据-用户饱和度"""
        group_type, interval = kwargs['group_type'], kwargs['interval']
        _now = now()
        interval_days, model = cls.INTERVAL_DAYS_MODEL_MAPPER[interval]
        end_time = _now
        start_time = _now - timedelta(days=interval_days)
        if params_start_time := kwargs.get("start_time"):
            start_time = params_start_time
        if params_end_time := kwargs.get("end_time"):
            end_time = params_end_time

        user_date_mapper = {}
        if user_id := kwargs.get("user_id"):
            user_limiter_count = 1000
            limiter_config = cls.get_user_frequency_limiter(user_id, group_type)
            if limiter_config:
                user_limiter_count = limiter_config.limit_total_count / (limiter_config.long_period_hour * 60 * 60)
            user_limiter_interval_count = cls.get_limiter_by_interval(interval, user_limiter_count)
            user_date_mapper = cls.get_user_api_saturation(model, user_id, group_type, start_time, end_time)
        system_max_req = cls.get_limiter_by_interval(interval, cls.get_sys_limiter_count(group_type))
        system_data_mapper = cls.get_user_api_saturation(model, cls.SYSTEM_USER_ID, group_type, start_time, end_time)
        all_timing = sorted(list(set(user_date_mapper.keys()) | set(system_data_mapper.keys())))
        data_mapper = []
        timings, user_data, system_data, user_using_data = [], [], [], []
        for t in all_timing:
            ts = datetime_to_time(t) * 1000
            s_data = system_data_mapper.get(t)
            u_data = user_date_mapper.get(t)

            user_rate = system_rate = using_rate = 0
            if u_data and s_data and s_data.req_count and user_id:
                using_rate = float(amount_to_str((u_data.req_count / s_data.req_count) * 100, 2))
                user_rate = float(amount_to_str((u_data.req_count / user_limiter_interval_count) * 100, 2))
            if s_data:
                system_rate = float(amount_to_str((s_data.req_count / system_max_req) * 100, 2))

            timings.append(ts)
            user_data.append(user_rate)
            system_data.append(system_rate)
            user_using_data.append(using_rate)
            base_dict = {
                "time": ts,
                "system_rate": system_rate,
            }
            if user_id:
                base_dict.update(**{
                    "user_rate": user_rate,
                    "using_rate": using_rate
                })
            data_mapper.append(base_dict)

        series_data = [{
            "name": "系统饱和度",
            "data": system_data,
            "type": "spline",
        }]
        y_axis = [{
            "labels": {
                "format": '{value}%',
            },
            "title": {
                "text": '饱和度'
            }
        }]
        if user_id:
            y_axis += [
                {
                    "title": {
                        "text": '用户资源占比',
                    },
                    "labels": {
                        "format": '{value}%',
                    },
                    "opposite": True
                }
            ]
            series_data += [
                {
                    "name": "用户饱和度",
                    "data": user_data,
                    "type": "spline",
                },
                {
                    "name": "用户资源使用占比",
                    "data": user_using_data,
                    "type": "spline",
                    "yAxis": 1
                }
            ]

        extr = dict(user_id=user_id, email=User.query.get(user_id).email) if user_id else {}
        return dict(
            detail_data=data_mapper,
            api_types={g.name: g.value for g in ApiSaturationChartResource.GROUPS},
            extr=extr,
            chart_data={
                "xAxis": timings,
                "series_data": series_data,
                "yAxis": y_axis
            }
        )


@ns.route("/saturation/sys/chart")
@respond_with_code
class ApiSysSaturationChartResource(ApiSaturationChartMixin, Resource):

    class TradeType(Enum):
        SPOT = "现货"
        PERPETUAL = "合约"

    class SystemAPIType(Enum):
        PUT_ORDER = "下单"
        CANCEL_ORDER = "撤单"
        QUERY_ORDER = "查单"
        SYSTEM = "总饱和度"

    SYSTEM_TYPE_GROUP_SYSTEM_LIMIT_MAPPER = {
        TradeType.SPOT: {
            SystemAPIType.PUT_ORDER: ([
                UserApiFrequencyLimitRecord.ApiGroups.ORDER,
                UserApiFrequencyLimitRecord.ApiGroups.ORDERS,
            ], 6000),
            SystemAPIType.CANCEL_ORDER: ([
                UserApiFrequencyLimitRecord.ApiGroups.CANCEL,
                UserApiFrequencyLimitRecord.ApiGroups.CANCELS,
            ], 12000),
            SystemAPIType.QUERY_ORDER: ([
                UserApiFrequencyLimitRecord.ApiGroups.QUERY_ORDER,
                UserApiFrequencyLimitRecord.ApiGroups.QUERY_ORDER_HISTORY
            ], 60000),
            SystemAPIType.SYSTEM: ([
                i for i in ApiStaticReport.API_GROUP_WEIGHT_MAP.keys() if not i.name.startswith("PERPETUAL")
            ], 6000 + 12000 * 0.5 + 60000 * 0.1)
        },
        TradeType.PERPETUAL: {
            SystemAPIType.PUT_ORDER: ([
                UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDER,
                UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDERS,
            ], 3000),
            SystemAPIType.CANCEL_ORDER: ([
                UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_CANCEL,
                UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_CANCELS,
            ], 6000),
            SystemAPIType.QUERY_ORDER: ([
                UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_QUERY_ORDER,
                UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_QUERY_ORDER_HISTORY
            ], 30000),
            SystemAPIType.SYSTEM: ([
                i for i in ApiStaticReport.API_GROUP_WEIGHT_MAP.keys() if i.name.startswith("PERPETUAL")
            ], 3000 + 6000 * 0.5 + 30000 * 0.1)
        }
    }

    @classmethod
    def get_weight_user_api_saturation(cls, group_type, model, user_id, groups, start_time, end_time):
        res = defaultdict(Decimal)
        q = model.query.filter(
            model.time < end_time,
            model.time >= start_time,
            model.user_id == user_id,
            model.group.in_([i.name for i in groups])
        ).with_entities(
            model.time,
            model.group,
            model.req_count,
        ).all()
        for row in q:
            # 只有系统数据才有权重
            weight = ApiStaticReport.API_GROUP_WEIGHT_MAP.get(UserApiFrequencyLimitRecord.ApiGroups[row.group], 0) \
                if group_type == cls.SystemAPIType.SYSTEM else 1
            res[row.time] += row.req_count * weight
        return res

    @classmethod
    def _get_groups_and_system_max_req(cls, trade_type, group_type):
        return cls.SYSTEM_TYPE_GROUP_SYSTEM_LIMIT_MAPPER[trade_type][group_type]

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(),
        trade_type=EnumField(TradeType, missing=TradeType.SPOT),
        group_type=EnumField(SystemAPIType, missing=SystemAPIType.SYSTEM),
        start_time=TimestampField(allow_none=True),
        end_time=TimestampField(allow_none=True),
        interval=EnumField(ApiSaturationChartMixin.Interval, defult=ApiSaturationChartMixin.Interval.MINUTE),
    ))
    def get(cls, **kwargs):
        """统计-API统计-资源占用数据-系统饱和度"""
        trade_type, group_type, interval = kwargs['trade_type'], kwargs['group_type'], kwargs['interval']
        _now = now()
        interval_days, model = cls.INTERVAL_DAYS_MODEL_MAPPER[interval]
        end_time = _now
        start_time = _now - timedelta(days=interval_days)
        if params_start_time := kwargs.get("start_time"):
            start_time = params_start_time
        if params_end_time := kwargs.get("end_time"):
            end_time = params_end_time

        groups, max_req = cls._get_groups_and_system_max_req(trade_type, group_type)
        system_max_req = cls.get_limiter_by_interval(interval, max_req)
        system_data_mapper = cls.get_weight_user_api_saturation(
            group_type,
            model,
            cls.SYSTEM_USER_ID,
            groups,
            start_time,
            end_time
        )
        all_timing = sorted(list(set(system_data_mapper.keys())))
        data_mapper = []
        timings, system_data = [], []
        for t in all_timing:
            ts = datetime_to_time(t) * 1000
            weight_req_count = system_data_mapper.get(t)
            system_rate = 0
            if weight_req_count:
                system_rate = float(amount_to_str((weight_req_count / Decimal(system_max_req)) * 100, 2))

            timings.append(ts)
            system_data.append(system_rate)
            base_dict = {
                "time": ts,
                "system_rate": system_rate,
            }
            data_mapper.append(base_dict)

        series_data = [{
            "name": f"{group_type.value}系统饱和度",
            "data": system_data,
            "type": "spline",
        }]
        y_axis = [{
            "labels": {
                "format": '{value}%',
            },
            "title": {
                "text": '饱和度'
            }
        }]

        return dict(
            detail_data=data_mapper,
            api_types=cls.SystemAPIType,
            chart_data={
                "xAxis": timings,
                "series_data": series_data,
                "yAxis": y_axis
            }
        )


@ns.route("/req-quality/chart")
@respond_with_code
class ApiReqQualityChartResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(),
        trade_type=EnumField(TradeBusinessType, missing=TradeBusinessType.SPOT),
        start_time=TimestampField(allow_none=True),
        end_time=TimestampField(allow_none=True),
        interval=EnumField(
            ApiSaturationChartResource.Interval,
            defult=ApiSaturationChartResource.Interval.MINUTE
        ),
    ))
    def get(cls, **kwargs):
        """统计-API统计-资源占用数据-请求质量"""
        trade_type,  interval = kwargs["trade_type"],  kwargs['interval']
        if trade_type == TradeBusinessType.SPOT:
            group_type = UserApiFrequencyLimitRecord.ApiGroups.ORDER
        else:
            group_type = UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDER
        _now = now()
        interval_days, model = ApiSaturationChartResource.INTERVAL_DAYS_MODEL_MAPPER[interval]
        end_time = _now
        start_time = _now - timedelta(days=interval_days)
        if params_start_time := kwargs.get("start_time"):
            start_time = params_start_time
        if params_end_time := kwargs.get("end_time"):
            end_time = params_end_time

        start_time = max(start_time, date_to_datetime(datetime.date(2023, 8, 22)))
        user_date_mapper = {}
        if user_id := kwargs.get("user_id"):
            user_date_mapper = ApiSaturationChartResource.get_user_api_saturation(model, user_id, group_type, start_time, end_time)
        system_data_mapper = ApiSaturationChartResource.get_user_api_saturation(
            model,
            ApiSaturationChartResource.SYSTEM_USER_ID,
            group_type,
            start_time,
            end_time
        )
        all_timing = sorted(list(set(user_date_mapper.keys()) | set(system_data_mapper.keys())))
        data_mapper = []
        timings, user_rel_rate_list, system_rel_rate_list, \
            user_rel_count_list, user_req_count_list = [], [], [], [], []
        for t in all_timing:
            ts = datetime_to_time(t) * 1000
            s_data = system_data_mapper.get(t)
            u_data = user_date_mapper.get(t)
            req_user_count = rel_user_count = rel_user_rate = rel_system_rate = 0
            if u_data and u_data.put_order_count:
                rel_user_count = u_data.rel_count
                req_user_count = u_data.put_order_count
                rel_user_rate = float(amount_to_str((rel_user_count / u_data.put_order_count) * 100, 2))
            if s_data and s_data.put_order_count:
                rel_system_rate = float(amount_to_str((s_data.rel_count / s_data.put_order_count) * 100, 2))

            base_dict = {
                "time": ts,
                "rel_system_rate": rel_system_rate
            }
            if user_id:
                base_dict.update(**{
                    "req_user_count": req_user_count,
                    "rel_user_count": rel_user_count,
                    "rel_user_rate": rel_user_rate,
                })

            data_mapper.append(base_dict)
            timings.append(ts)
            user_req_count_list.append(req_user_count)
            user_rel_count_list.append(rel_user_count)
            user_rel_rate_list.append(rel_user_rate)
            system_rel_rate_list.append(rel_system_rate)

        series_data = [
            {
                "name": "系统有效成交率",
                "data": system_rel_rate_list,
                "type": "spline"
            }
        ]
        y_axis = [
            {
                "title": {
                    "text": '有效成交率',
                },
                "labels": {
                    "format": '{value}%',
                }
            }
        ]
        if user_id:
            y_axis += [
                {
                    "gridLineWidth": 0,
                    "title": {
                        "text": "订单数",
                    },
                    "labels": {
                        "format": '{value}',
                    },
                    "opposite": True
                },
                {
                    "gridLineWidth": 0,
                    "title": {
                        "text": "有效成交",
                    },
                    "labels": {
                        "format": '{value}',
                    },
                    "opposite": True
                },
            ]
            series_data += [
                {
                    "name": "用户有效成交率",
                    "data": user_rel_rate_list,
                    "type": "spline",
                },
                {
                    "name": "用户总订单数",
                    "data": user_req_count_list,
                    "type": "spline",
                    "yAxis": 1
                },
                {
                    "name": "用户有效成交数",
                    "data": user_rel_count_list,
                    "type": "spline",
                    "yAxis": 2
                },

            ]
        extr = dict(user_id=user_id, email=User.query.get(user_id).email) if user_id else {}
        return dict(
            detail_data=data_mapper,
            api_types={g.name: g.value for g in ApiSaturationChartResource.GROUPS},
            extr=extr,
            chart_data={
                "xAxis": timings,
                "series_data": series_data,
                "yAxis": y_axis
            }
        )
