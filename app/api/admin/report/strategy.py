# -*- coding: utf-8 -*-
from sqlalchemy import desc
from webargs import fields as wa_fields

from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField, PageField, LimitField, DateField
from app.common import ReportType, Language, ADMIN_EXPORT_LIMIT
from app.models import (
    DailySpotGridSiteReport,
    MonthlySpotGridSiteReport, QuarterlySpotGridSiteReport,
)
from app.utils import export_xlsx


ns = Namespace("Strategy Report")


@ns.route("/spot-grid/site-report")
@respond_with_code
class SpotGridSiteReportResource(Resource):

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "total_deal_usd", Language.ZH_HANS_CN: "成交额"},
        {"field": "total_fee_usd", Language.ZH_HANS_CN: "手续费"},
        {"field": "grid_profit_usd", Language.ZH_HANS_CN: "网格利润市值"},
        {"field": "total_sty_usd", Language.ZH_HANS_CN: "策略持仓总市值"},
        {"field": "running_sty_count", Language.ZH_HANS_CN: "运行策略数"},
        {"field": "running_user_count", Language.ZH_HANS_CN: "运行策略人数"},
        {"field": "inc_sty_count", Language.ZH_HANS_CN: "创建策略数"},
        {"field": "inc_user_count", Language.ZH_HANS_CN: "创建策略人数"},
        {"field": "new_user_count", Language.ZH_HANS_CN: "新增策略人数"},
        {"field": "deal_order_count", Language.ZH_HANS_CN: "成交订单笔数"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            order=EnumField(["report_date", "total_deal_usd"], missing="report_date"),
            page=PageField(unlimited=True),
            limit=LimitField(missing=50),
            export=wa_fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """ 报表-现货报表-现货网格报表 """
        order = kwargs["order"]
        page = kwargs["page"]
        limit = kwargs["limit"]
        export = kwargs["export"]
        if kwargs["report_type"] == ReportType.DAILY:
            model = DailySpotGridSiteReport
        elif kwargs["report_type"] == ReportType.MONTHLY:
            model = MonthlySpotGridSiteReport
        else:
            model = QuarterlySpotGridSiteReport

        query = model.query
        if start_date := kwargs.get("start_date"):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(model.report_date <= end_date)

        query = query.with_entities(
            model.report_date,
            model.total_deal_usd,
            model.total_fee_usd,
            model.grid_profit_usd,
            model.total_sty_usd,
            model.running_sty_count,
            model.running_user_count,
            model.inc_sty_count,
            model.inc_user_count,
            model.new_user_count,
            model.deal_order_count,
        ).order_by(desc(order))
        
        total = 0
        if export:
            rows = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = query.paginate(page, limit, error_out=False)
            rows = pagination.items
            total = pagination.total
        items = []
        for i in rows:
            items.append(
                {
                    "report_date": i.report_date,
                    "total_deal_usd": i.total_deal_usd,
                    "total_fee_usd": i.total_fee_usd,
                    "grid_profit_usd": i.grid_profit_usd,
                    "total_sty_usd": i.total_sty_usd,
                    "running_sty_count": i.running_sty_count,
                    "running_user_count": i.running_user_count,
                    "inc_sty_count": i.inc_sty_count,
                    "inc_user_count": i.inc_user_count,
                    "new_user_count": i.new_user_count,
                    "deal_order_count": i.deal_order_count,
                }
            )

        if export:
            for i in items:
                i["report_date"] = i["report_date"].strftime("%Y-%m-%d")
            return export_xlsx(
                filename='spot_grid_report',
                data_list=items,
                export_headers=cls.export_headers,
            )
        return dict(
            items=items,
            total=total,
        )
