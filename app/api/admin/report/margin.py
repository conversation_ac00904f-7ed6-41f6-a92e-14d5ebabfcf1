import datetime
import json
from collections import defaultdict
from decimal import Decimal

from sqlalchemy import func, desc
from webargs import fields
from flask import g

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import <PERSON>umField, PageField, LimitField, DateField
from app.assets import list_all_assets
from app.caches import MarginA<PERSON>unt<PERSON>ameCache
from app.common import ReportType, ADMIN_EXPORT_LIMIT
from app.exceptions import InvalidArgument
from app.models import DailyMarginReport, MonthlyMarginReport, \
    DailyMarginAssetReport, MonthlyMarginAssetReport, DailyMarginMarketReport, \
    MonthlyMarginMarketReport, MarginAssetRule, DailySpotMarginBurstReport, \
    MonthlySpotMarginBurstReport, DailyMarginFundReport, \
    MonthlyMarginFundReport
from app.utils import amount_to_str, quantize_amount, export_xlsx, format_percent, datetime_to_time
from app.schedules.reports.admin_async_download import async_download_margin_asset_report

ns = Namespace('Report - Margin')


@ns.route('/deals')
@respond_with_code
class DailyMarginDealsResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "market_count", Language.ZH_HANS_CN: "市场数"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户数"},
        {"field": "increase_margin_user_count", Language.ZH_HANS_CN: "新增杠杆用户"},
        {"field": "loan_user_count", Language.ZH_HANS_CN: "借币用户数"},
        {"field": "loan_order_count", Language.ZH_HANS_CN: "借币笔数"},
        {"field": "loan_usd", Language.ZH_HANS_CN: "借币市值(USD)"},
        {"field": "flat_user_count", Language.ZH_HANS_CN: "还币用户数"},
        {"field": "flat_order_count", Language.ZH_HANS_CN: "还币笔数"},
        {"field": "flat_usd", Language.ZH_HANS_CN: "还币市值(USD)"},
        {"field": "interest_real_usd", Language.ZH_HANS_CN: "利息收入(USD)"},
        {"field": "interest_fund_usd", Language.ZH_HANS_CN: "基金收入(USD)"},
        {"field": "average_loan_balance", Language.ZH_HANS_CN: "平均借贷余额市值(USD)"},
        {"field": "average_interest_rate", Language.ZH_HANS_CN: "平均利率"},
        {"field": "real_leverage", Language.ZH_HANS_CN: "实际杠杆倍数"},

    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """报表-杠杆-全站"""

        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyMarginReport
        else:
            model = MonthlyMarginReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)

        total = 0
        page = 1
        if kwargs['export']:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = query.paginate(kwargs['page'], limit, error_out=False)
            items = pagination.items
            total = pagination.total
            page = pagination.page
        records = []
        for item in items:
            report_date = item.report_date.strftime('%Y-%m-%d')
            record = {
                "report_date": report_date,
                "loan_order_count": amount_to_str(item.loan_order_count, 2),
                "flat_order_count": amount_to_str(item.flat_order_count, 2),
                "interest_amount": amount_to_str(item.interest_amount, 2),
                "loan_usd": amount_to_str(item.loan_usd, 2),
                "flat_usd": amount_to_str(item.flat_usd, 2),
                "interest_usd": amount_to_str(item.interest_usd, 2),
                "market_count": item.market_count,
                "increase_margin_user_count": item.increase_margin_user_count,
                "active_user_count": item.active_user_count,
                "loan_user_count": item.loan_user_count,
                "flat_user_count": item.flat_user_count,
                "average_loan_balance": amount_to_str(item.average_loan_balance, 2),
                "average_interest_rate": amount_to_str(item.average_interest_rate, 8),
                "real_leverage": amount_to_str(item.real_leverage, 2),
            }
            records.append(record)

        if kwargs['export']:
            return export_xlsx(
                filename='margin_site_report',
                data_list=cls.get_export_data(records),
                export_headers=cls.export_headers
            )

        return dict(
            records=records,
            total=total,
            page=page,
        )

    @classmethod
    def get_export_data(cls, records):
        for record in records:
            record['interest_real_usd'] = amount_to_str(Decimal(float(record['interest_usd']) * 0.7), 2)
            record['interest_fund_usd'] = amount_to_str(Decimal(float(record['interest_usd']) * 0.3), 2)
            record['average_interest_rate'] = format_percent(record['average_interest_rate'], 4)
            record['real_leverage'] = amount_to_str(record['real_leverage'], 2)
        return records


@ns.route('/assets')
@respond_with_code
class DailyMarginAssetResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户数"},
        {"field": "loan_user_count", Language.ZH_HANS_CN: "借币用户数"},
        {"field": "loan_order_count", Language.ZH_HANS_CN: "借币笔数"},
        {"field": "loan_usd", Language.ZH_HANS_CN: "借币市值(USD)"},
        {"field": "flat_user_count", Language.ZH_HANS_CN: "还币用户数"},
        {"field": "flat_order_count", Language.ZH_HANS_CN: "还币笔数"},
        {"field": "flat_usd", Language.ZH_HANS_CN: "还币市值(USD)"},
        {"field": "interest_real_usd", Language.ZH_HANS_CN: "利息收入(USD)"},
        {"field": "interest_fund_usd", Language.ZH_HANS_CN: "基金收入(USD)"},
        {"field": "average_loan_balance_amount", Language.ZH_HANS_CN: "平均借贷余额市值(USD)"},
        {"field": "average_interest_rate", Language.ZH_HANS_CN: "平均利率"},

    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        asset=fields.String,
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """报表-杠杆-币种"""

        limit = kwargs['limit']
        asset_list = [item.asset for item in MarginAssetRule.query.all()]

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyMarginAssetReport
        else:
            model = MonthlyMarginAssetReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)
        if asset := kwargs.get('asset'):
            query = query.filter(model.asset == asset)

        total = 0
        page = 1
        if kwargs['export']:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = query.paginate(kwargs['page'], limit, error_out=False)
            items = pagination.items
            total = pagination.total
            page = pagination.page
        records = []
        for item in items:
            record = {
                "report_date": item.report_date,
                "asset": item.asset,
                "loan_order_count": amount_to_str(item.loan_order_count, 2),
                "flat_order_count": amount_to_str(item.flat_order_count, 2),
                "interest_amount": amount_to_str(item.interest_amount, 2),
                "loan_usd": amount_to_str(item.loan_usd, 2),
                "flat_usd": amount_to_str(item.flat_usd, 2),
                "interest_usd": amount_to_str(item.interest_usd, 2),
                "active_user_count": item.active_user_count,
                "loan_user_count": item.loan_user_count,
                "flat_user_count": item.flat_user_count,
                "average_loan_balance_amount": amount_to_str(item.average_loan_balance_amount, 8),
                "average_loan_balance_usd": amount_to_str(item.average_loan_balance_usd, 2),
                "average_interest_rate": item.average_interest_rate,
            }
            records.append(record)

        if kwargs['export']:
            return export_xlsx(
                filename='margin_coin_report',
                data_list=cls.get_export_data(records),
                export_headers=cls.export_headers
            )

        return dict(
            records=records,
            total=total,
            page=page,
            asset_list=asset_list,
        )

    @classmethod
    def get_export_data(cls, records):
        for record in records:
            record['interest_real_usd'] = amount_to_str(Decimal(float(record['interest_usd']) * 0.7), 2)
            record['interest_fund_usd'] = amount_to_str(Decimal(float(record['interest_usd']) * 0.3), 2)
            record['average_interest_rate'] = format_percent(record['average_interest_rate'], 4)
        return records


@ns.route('/assets-async-download')
@respond_with_code
class DailyMarginAssetAsyncDownloadResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_time=DateField(required=True),
        end_time=DateField(required=True),
    ))
    def get(cls, **kwargs):
        """报表-杠杆-币种-异步下载"""

        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_margin_asset_report.delay(
            email=g.user.email,
            is_daily=kwargs['report_type'] is ReportType.DAILY,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )


@ns.route("/asset-detail")
@respond_with_code
class DailyMarginAssetDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            dict(
                report_type=EnumField(ReportType, required=True),
                report_date=DateField(to_date=True),
                order=EnumField(
                    [
                        "active_user_count",
                        "loan_user_count",
                        "loan_order_count",
                        "loan_usd",
                        "flat_user_count",
                        "flat_order_count",
                        "flat_usd",
                        "interest_usd",
                        "interest_fund_usd",  # 基金收入
                        "average_loan_balance",
                        "average_interest_rate",
                    ],
                ),
                page=PageField,
                limit=LimitField,
            )
        )
    )
    def get(cls, **kwargs):
        """ 报表-杠杆报表-币种详情 """
        page = kwargs["page"]
        limit = kwargs["limit"]
        report_type = kwargs["report_type"]
        if report_type == ReportType.DAILY:
            model = DailyMarginAssetReport
        else:
            model = MonthlyMarginAssetReport

        report_date: datetime = kwargs.get("report_date")
        if not report_date:
            last_item = model.query.order_by(model.report_date.desc()).first()
            if not last_item:
                return dict(
                    records=[],
                    total=0,
                    page=1,
                )
            report_date = last_item.report_date
        if report_type == ReportType.MONTHLY:
            report_date = report_date.replace(day=1)

        order_field = kwargs.get("order")
        if not order_field:
            order_field = "interest_usd"
        if order_field == "interest_fund_usd":
            order_field = "interest_usd"
        pagination = (
            model.query.filter(model.report_date == report_date).order_by(desc(order_field)).paginate(page, limit)
        )

        records = []
        for item in pagination.items:
            record = {
                "report_date": item.report_date,
                "asset": item.asset,
                "loan_order_count": amount_to_str(item.loan_order_count, 2),
                "flat_order_count": amount_to_str(item.flat_order_count, 2),
                "interest_amount": amount_to_str(item.interest_amount, 2),
                "loan_usd": amount_to_str(item.loan_usd, 2),
                "flat_usd": amount_to_str(item.flat_usd, 2),
                "interest_usd": amount_to_str(item.interest_usd, 2),
                "active_user_count": item.active_user_count,
                "loan_user_count": item.loan_user_count,
                "flat_user_count": item.flat_user_count,
                "average_loan_balance_amount": amount_to_str(item.average_loan_balance_amount, 8),
                "average_loan_balance_usd": amount_to_str(item.average_loan_balance_usd, 2),
                "average_interest_rate": item.average_interest_rate,
            }
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
        )


@ns.route('/markets')
@respond_with_code
class DailyMarginMarketResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            market=fields.String,
            order=EnumField(
                [
                    "report_date",
                    "active_user_count",
                    "loan_user_count",
                    "loan_usd",
                    "interest_usd",
                ],
            ),
            page=PageField(unlimited=True),
            limit=LimitField(missing=50),
        )
    )
    def get(cls, **kwargs):
        """报表-杠杆-市场"""

        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyMarginMarketReport
        else:
            model = MonthlyMarginMarketReport
        query = model.query
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)
        if market := kwargs.get('market'):
            query = query.filter(model.market == market)

        order_field = kwargs.get("order")
        if not order_field:
            order_field = "report_date"
        query = query.order_by(desc(order_field))
        pagination = query.paginate(page, limit, error_out=False)
        records = []
        for item in pagination.items:
            record = {
                "report_date": item.report_date,
                "market": item.market,
                "loan_order_count": amount_to_str(item.loan_order_count, 2),
                "flat_order_count": amount_to_str(item.flat_order_count, 2),
                "interest_amount": amount_to_str(item.interest_amount, 2),
                "loan_usd": amount_to_str(item.loan_usd, 2),
                "flat_usd": amount_to_str(item.flat_usd, 2),
                "interest_usd": amount_to_str(item.interest_usd, 2),
                "active_user_count": item.active_user_count,
                "loan_user_count": item.loan_user_count,
                "flat_user_count": item.flat_user_count,
                "average_loan_balance": amount_to_str(item.average_loan_balance, 2),
                "average_interest_rate": item.average_interest_rate,
                "real_leverage": amount_to_str(item.real_leverage, 2),
            }
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
            market_list=list(
                MarginAccountNameCache.list_online_markets().values()),
        )


@ns.route("/market-detail")
@respond_with_code
class DailyMarginMarketDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, required=True),
            report_date=DateField(to_date=True),
            order=EnumField(
                [
                    "active_user_count",
                    "loan_user_count",
                    "loan_order_count",
                    "loan_usd",
                    "flat_user_count",
                    "flat_order_count",
                    "flat_usd",
                    "interest_usd",
                    "interest_fund_usd",  # 基金收入
                    "average_loan_balance",
                    "average_interest_rate",
                ],
            ),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 报表-杠杆报表-市场详情 """
        page = kwargs["page"]
        limit = kwargs["limit"]
        report_type = kwargs["report_type"]
        if report_type == ReportType.DAILY:
            model = DailyMarginMarketReport
        else:
            model = MonthlyMarginMarketReport

        report_date: datetime = kwargs.get("report_date")
        if not report_date:
            last_item = model.query.order_by(model.report_date.desc()).first()
            if not last_item:
                return dict(
                    records=[],
                    total=0,
                    page=1,
                )
            report_date = last_item.report_date
        if report_type == ReportType.MONTHLY:
            report_date = report_date.replace(day=1)

        order_field = kwargs.get("order")
        if not order_field:
            order_field = "interest_usd"
        if order_field == "interest_fund_usd":
            order_field = "interest_usd"
        pagination = (
            model.query.filter(model.report_date == report_date).order_by(desc(order_field)).paginate(page, limit)
        )

        records = []
        for item in pagination.items:
            record = {
                "report_date": item.report_date,
                "market": item.market,
                "loan_order_count": amount_to_str(item.loan_order_count, 2),
                "flat_order_count": amount_to_str(item.flat_order_count, 2),
                "interest_amount": amount_to_str(item.interest_amount, 2),
                "loan_usd": amount_to_str(item.loan_usd, 2),
                "flat_usd": amount_to_str(item.flat_usd, 2),
                "interest_usd": amount_to_str(item.interest_usd, 2),
                "active_user_count": item.active_user_count,
                "loan_user_count": item.loan_user_count,
                "flat_user_count": item.flat_user_count,
                "average_loan_balance": amount_to_str(item.average_loan_balance, 2),
                "average_interest_rate": item.average_interest_rate,
                "real_leverage": amount_to_str(item.real_leverage, 2),
            }
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
        )


@ns.route('/liquidation')
@respond_with_code
class MarginLiquidationResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        market=fields.String,
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-杠杆-爆仓"""

        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailySpotMarginBurstReport
        else:
            model = MonthlySpotMarginBurstReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)
        if market := kwargs.get('market'):
            query = query.filter(model.market == market)

        pagination = query.paginate(page, limit, error_out=False)
        records = []
        for item in pagination.items:
            record = item.to_dict()
            record['report_date'] = item.report_date.strftime('%Y-%m-%d')
            record['burst_usd'] = burst_usd = item.base_asset_burst_usd + item.quote_asset_burst_usd
            record['cross_liq_usd'] = cross_liq_usd = item.base_asset_cross_liq_usd + item.quote_asset_cross_liq_usd
            record['fund_usd'] = fund_usd = item.base_asset_fund_usd + item.quote_asset_fund_usd
            record['cross_liq_usd_ratio'] = amount_to_str(cross_liq_usd / burst_usd * 100, 2) + "%" if burst_usd else "0%"
            record['cross_liq_count_ratio'] = amount_to_str(
                item.cross_liq_count / item.burst_count * 100, 2
            ) + "%" if item.burst_count else "0%"
            record['fund_usd_ratio'] = amount_to_str(fund_usd / cross_liq_usd * 100, 2) + "%" if cross_liq_usd else "0%"
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
            market_list=list(
                MarginAccountNameCache.list_online_markets().values()),
        )


@ns.route('/liquidation-summary')
@respond_with_code
class MarginLiquidationSummaryResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-杠杆-爆仓聚合统计"""

        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailySpotMarginBurstReport
        else:
            model = MonthlySpotMarginBurstReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)

        query = query.group_by(
            model.report_date,
        ).with_entities(
            model.report_date,
            func.sum(model.base_asset_liq_fee_usd).label("base_asset_liq_fee_usd"),
            func.sum(model.quote_asset_liq_fee_usd).label("quote_asset_liq_fee_usd"),
            func.sum(model.base_asset_burst_usd).label('base_asset_burst_usd'),
            func.sum(model.quote_asset_burst_usd).label('quote_asset_burst_usd'),
            func.sum(model.base_asset_fund_usd).label('base_asset_fund_usd'),
            func.sum(model.quote_asset_fund_usd).label('quote_asset_fund_usd'),
            func.sum(model.base_asset_cross_liq_usd).label('base_asset_cross_liq_usd'),
            func.sum(model.quote_asset_cross_liq_usd).label('quote_asset_cross_liq_usd'),
            func.sum(model.burst_count).label('burst_count'),
            func.sum(model.cross_liq_count).label('cross_liq_count'),
        )

        pagination = query.paginate(page, limit, error_out=False)
        pagination_result = pagination.items

        user_count_query = model.query.filter(model.report_date.in_(
            [i.report_date for i in pagination_result])).all()

        user_set_date_map = defaultdict(set)

        for item in user_count_query:
            user_set_date_map[item.report_date].update(
                json.loads(item.user_list))

        records = []
        for item in pagination_result:
            burst_usd = item.base_asset_burst_usd + item.quote_asset_burst_usd
            cross_liq_usd = item.base_asset_cross_liq_usd + item.quote_asset_cross_liq_usd
            fund_usd = item.base_asset_fund_usd + item.quote_asset_fund_usd
            cross_liq_usd_ratio = amount_to_str(cross_liq_usd / burst_usd * 100, 2) + "%" if burst_usd else "0%"
            cross_liq_count_ratio = amount_to_str(item.cross_liq_count / item.burst_count * 100, 2) + "%" if item.burst_count else "0%"
            fund_usd_ratio = amount_to_str(fund_usd / cross_liq_usd * 100, 2) + "%" if cross_liq_usd else "0%"

            record = dict(
                burst_usd=burst_usd,
                fund_usd=fund_usd,
                cross_liq_usd=cross_liq_usd,
                liq_fee_usd=item.base_asset_liq_fee_usd + item.quote_asset_liq_fee_usd,
                market='全部',
                report_date=item.report_date.strftime('%Y-%m-%d'),
                user_count=len(user_set_date_map[item.report_date]),
                burst_count=item.burst_count,
                cross_liq_count=item.cross_liq_count,
                cross_liq_usd_ratio=cross_liq_usd_ratio,
                cross_liq_count_ratio=cross_liq_count_ratio,
                fund_usd_ratio=fund_usd_ratio,
            )
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
            market_list=list(
                MarginAccountNameCache.list_online_markets().values()),
        )


@ns.route('/margin-fund')
@respond_with_code
class MarginFundReportResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField,
        end_date=DateField,
        page=PageField(unlimited=True),
        asset=fields.String,
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-杠杆-保险基金"""
        model = DailyMarginFundReport if kwargs['report_type'] == ReportType.DAILY else MonthlyMarginFundReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)
        if asset := kwargs.get('asset'):
            query = query.filter(model.asset == asset)
        records = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        res = []
        for item in records.items:
            report_date = item.report_date.strftime('%Y-%m-%d')
            res.append(dict(
                report_date=report_date,
                asset=item.asset,
                interest_fund=quantize_amount(item.interest_fund, 8),
                liquidation_fund=quantize_amount(item.liquidation_fund, 8),
                liquidation=quantize_amount(item.liquidation, 8),
                amount=quantize_amount(item.amount, 8),
                balance=quantize_amount(item.balance, 8),
                transfer=quantize_amount(abs(item.transfer), 8),

                real_income=quantize_amount(item.interest_fund + item.liquidation_fund, 8),
                real_cost=quantize_amount(item.liquidation + item.transfer, 8),
                real_change_amount=quantize_amount(
                    item.interest_fund + item.liquidation_fund +
                    item.liquidation + item.transfer, 8),
                real_balance=quantize_amount(item.real_balance, 8),
            ))
        return dict(
            total=records.total,
            items=res,
            assets=list_all_assets()
        )
