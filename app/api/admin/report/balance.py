# -*- coding: utf-8 -*-
from collections import defaultdict
from itertools import chain
from datetime import timedelta
from enum import Enum
from flask import g

from webargs import fields

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField, PageField, LimitField, DateField
from app.assets import list_all_assets
from app.common import AccountBalanceType, ReportType, language_cn_names
from app.exceptions import InvalidArgument
from app.models import DailyBalanceReport
from app.models.daily import DailyAssetBusinessReport, DailyAssetUserVisitReport, DailyAssetExposureReport
from app.models.monthly import MonthlyAssetBusinessReport
from app.utils import quantize_amount, amount_to_str
from app.utils.date_ import last_month, datetime_to_time
from app.utils.format import format_percent
from app.schedules.reports.admin_async_download import async_download_balance_visit_report, \
    async_download_balance_asset_report, async_download_balance_exposure_report

ns = Namespace('Report - Balance')


@ns.route('')
@respond_with_code
class UserBalanceResource(Resource):
    class Range(Enum):
        ONE_DAY = 1
        SEVEN_DAYS = 7
        THIRTY_DAYS = 30

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        account_type=EnumField(AccountBalanceType),
        asset=fields.String(required=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """报表-资产报表"""

        asset = kwargs['asset'] or ""  # 空字符串：全部币种
        account_type = kwargs.get('account_type')
        query = DailyBalanceReport.query.filter(
            DailyBalanceReport.asset == asset
        )
        if account_type is None:
            query = query.filter(DailyBalanceReport.account_type.is_(None))
        else:
            query = query.filter(DailyBalanceReport.account_type == account_type)
        if start_date := kwargs.get('start_date'):
            query = query.filter(DailyBalanceReport.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(DailyBalanceReport.report_date <= end_date)
        query = query.order_by(DailyBalanceReport.report_date.desc()).with_entities(
            DailyBalanceReport.id,
            DailyBalanceReport.report_date,
            DailyBalanceReport.count_0,
            DailyBalanceReport.count_1,
            DailyBalanceReport.count_2,
            DailyBalanceReport.count_3,
            DailyBalanceReport.count_4,
            DailyBalanceReport.count_5,
            DailyBalanceReport.count_6,
            DailyBalanceReport.count_7,
            DailyBalanceReport.threshold,
            DailyBalanceReport.asset,
            DailyBalanceReport.total_amount,
            DailyBalanceReport.total_usd,
            DailyBalanceReport.user_count,
            DailyBalanceReport.account_type,
        )
        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        records = pagination.items
        res = []
        for row in records:
            item = {
                "id": row.id,
                "report_date": row.report_date,
                "count_0": row.count_0,
                "count_1": row.count_1,
                "count_2": row.count_2,
                "count_3": row.count_3,
                "count_4": row.count_4,
                "count_5": row.count_5,
                "count_6": row.count_6,
                "count_7": row.count_7,
                "threshold": row.threshold,
                "asset": row.asset,
                "account_type": row.account_type,
                "total_usd": quantize_amount(row.total_usd, 2),
                "total_amount": quantize_amount(row.total_amount, 8),
                "total_count": row.user_count,
            }
            user_count = sum([
                row.count_1,
                row.count_2,
                row.count_3,
                row.count_4,
                row.count_5,
                row.count_6,
                row.count_7,
            ])
            item.update(user_count=user_count)
            res.append(item)
        assets = list_all_assets()
        assets.insert(0, "")
        return dict(
            total=pagination.total,
            items=res,
            assets=assets,
            account_types={
                "SPOT": "现货",
                "MARGIN": "杠杆",
                "INVESTMENT": "理财",
                "STAKING": "质押",
                "PERPETUAL": "合约",
                "AMM": "AMM",
                "PLEDGE": "借贷",
            },
            threshold=res[0]['threshold'] if res else 0,
            last_update_time=res[0]['report_date'] if res else None,
        )


@ns.route('/async-download')
@respond_with_code
class UserBalanceAsyncDownloadResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        start_time=DateField(required=True),
        end_time=DateField(required=True),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-异步下载"""

        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_balance_asset_report.delay(
            email=g.user.email,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )


@ns.route('/business')
@respond_with_code
class AssetBusinessTransitionReportResource(Resource):

    @staticmethod
    def _get_prev_date(date, report_type):
        if report_type == ReportType.DAILY:
            prev_date = date - timedelta(days=1)
        else:
            prev_date = last_month(date.year, date.month)
        return prev_date

    @staticmethod
    def _get_relative_ratio(prev, curr):
        """环比"""
        if prev == 0:
            return '0%'
        return format_percent((curr - prev) / prev)

    @staticmethod
    def _get_ratio(curr, total):
        """占比"""
        if total == 0:
            return '0%'
        return format_percent(curr / total)

    ORDER_TYPES = {
        'report_date': '日期',
        'user_count': '全部用户数',
        'asset_user_count': '资产用户数',
        'total_usd': '持仓总市值（USD）',
        'spot_user_count': '币币用户数',
        'exchange_user_count': '兑换用户数',
        'perpetual_user_count': '合约用户数',
        'margin_user_count': '杠杆用户数',
        'investment_user_count': '理财用户数',
        'amm_user_count': 'AMM用户数',
    }

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        asset=fields.String,
        order_type=fields.String(missing='report_date'),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-业务转化报表"""
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyAssetBusinessReport
        else:
            model = MonthlyAssetBusinessReport
        query = model.query
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)
        if asset := kwargs.get('asset'):
            if asset.upper() == 'ALL':
                asset = ''
            query = query.filter(model.asset == asset)
        query = query.order_by(getattr(model, kwargs['order_type']).desc(), model.id.asc())
        pagination = query.paginate(kwargs['page'], kwargs['limit'])
        items = pagination.items
        if not items:
            return dict(total=0, items=[], order_types=cls.ORDER_TYPES)
        min_date = min(item.report_date for item in items)

        prev_date = cls._get_prev_date(min_date, kwargs['report_type'])
        prev_records = model.query.filter(
            model.report_date == prev_date,
        ).all()
        record_map = defaultdict(lambda: defaultdict(dict))
        for record in chain(prev_records, items):
            record_map[record.report_date][record.asset] = record
        dates = {item.report_date for item in items}
        total_records = model.query.filter(
            model.report_date.in_(dates),
            model.asset == '',
        ).all()
        total_record_map = {record.report_date: record for record in total_records}
        result = []
        for item in items:
            item = item.to_dict()
            date_ = item['report_date']
            total_record = total_record_map[date_]
            prev_date = cls._get_prev_date(item['report_date'], kwargs['report_type'])
            prev_record = record_map[prev_date][item['asset']]
            prev_spot_user_count = prev_record.spot_user_count if prev_record else 0
            prev_perpetual_user_count = prev_record.perpetual_user_count if prev_record else 0
            prev_exchange_user_count = prev_record.exchange_user_count if prev_record else 0
            prev_margin_user_count = prev_record.margin_user_count if prev_record else 0
            prev_investment_user_count = prev_record.investment_user_count if prev_record else 0
            prev_staking_user_count = prev_record.staking_user_count if prev_record else 0
            prev_amm_user_count = prev_record.amm_user_count if prev_record else 0
            prev_asset_user_count = prev_record.asset_user_count if prev_record else 0
            prev_total_usd = prev_record.total_usd if prev_record else 0
            result.append(dict(
                asset_user_ratio=cls._get_ratio(item['asset_user_count'], total_record.asset_user_count),
                total_usd_ratio=cls._get_ratio(item['total_usd'], total_record.total_usd),
                asset_user_relative_ratio=cls._get_relative_ratio(prev_asset_user_count, item['asset_user_count']),
                total_usd_relative_ratio=cls._get_relative_ratio(prev_total_usd, item['total_usd']),
                spot_user_ratio=cls._get_ratio(item['spot_user_count'], total_record.spot_user_count),
                spot_user_relative_ratio=cls._get_relative_ratio(prev_spot_user_count, item['spot_user_count']),
                perpetual_user_ratio=cls._get_ratio(item['perpetual_user_count'], total_record.perpetual_user_count),
                perpetual_user_relative_ratio=cls._get_relative_ratio(prev_perpetual_user_count,
                                                                      item['perpetual_user_count']),
                exchange_user_ratio=cls._get_ratio(item['exchange_user_count'], total_record.exchange_user_count),
                exchange_user_relative_ratio=cls._get_relative_ratio(prev_exchange_user_count,
                                                                     item['exchange_user_count']),
                margin_user_ratio=cls._get_ratio(item['margin_user_count'], total_record.margin_user_count),
                margin_user_relative_ratio=cls._get_relative_ratio(prev_margin_user_count, item['margin_user_count']),
                investment_user_ratio=cls._get_ratio(item['investment_user_count'], total_record.investment_user_count),
                investment_user_relative_ratio=cls._get_relative_ratio(prev_investment_user_count,
                                                                       item['investment_user_count']),
                staking_user_ratio=cls._get_ratio(item['staking_user_count'], total_record.staking_user_count),
                staking_user_relative_ratio=cls._get_relative_ratio(prev_staking_user_count,
                                                                       item['staking_user_count']),
                amm_user_ratio=cls._get_ratio(item['amm_user_count'], total_record.amm_user_count),
                amm_user_relative_ratio=cls._get_relative_ratio(prev_amm_user_count, item['amm_user_count']),
                increase_asset_user_ratio=cls._get_ratio(item['up_threshold_increase_asset_user_count'],
                                                         item['increase_asset_user_count']),
                **item,
            ))
        return dict(
            total=pagination.total,
            items=result,
            order_types=cls.ORDER_TYPES,
        )


@ns.route('/visit')
@respond_with_code
class AssetUserVisitReportResource(Resource):

    @staticmethod
    def _get_prev_date(date, report_type):
        if report_type == ReportType.DAILY:
            prev_date = date - timedelta(days=1)
        else:
            prev_date = last_month(date.year, date.month)
        return prev_date

    @staticmethod
    def _get_ratio(curr, total):
        """占比"""
        if total == 0:
            return 0
        return format_percent(curr / total)

    ORDER_TYPES = {
        'report_date': '日期',
        'favorite_count': '行情自选人数',
        'spot_follow_count': '币币自选人数',
        'perpetual_follow_count': '合约自选人数',
        'asset_click_count': '币种搜索点击人数',
        'spot_click_count': '币币搜索点击人数',
        'perpetual_click_count': '合约搜索点击人数',
        'asset_search_count': 'app币种搜索点击人数',
        'spot_search_count': 'app币币搜索点击人数',
        'perpetual_search_count': 'app合约搜索点击人数',
    }

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        user_type=EnumField(DailyAssetUserVisitReport.UserType),
        lang=EnumField(Language),
        duration=fields.Integer,
        asset=fields.String,
        order_type=fields.String(missing='report_date'),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-用户关注报表"""

        model = DailyAssetUserVisitReport
        query = model.query
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)
        if duration := kwargs.get('duration'):
            query = query.filter(model.duration == duration)
        if asset := kwargs.get('asset'):  # asset == '' 无需展示
            query = query.filter(model.asset == asset)
        query = query.filter(model.asset != '')
        user_type = kwargs.get('user_type')
        user_type_str = user_type.name if user_type else ''
        query = query.filter(model.user_type == user_type_str)
        lang = kwargs.get('lang')
        lang_str = lang.name if lang else ''
        query = query.filter(model.lang == lang_str)

        query = query.order_by(getattr(model, kwargs['order_type']).desc())

        pagination = query.paginate(kwargs['page'], kwargs['limit'])
        items = pagination.items
        langs = {lang.name: lang_value for lang, lang_value in language_cn_names().items()}
        if not items:
            return dict(total=0, items=[], order_types=cls.ORDER_TYPES, user_types=model.UserType,
                        langs=langs)
        min_date = min(item.report_date for item in items)

        prev_date = cls._get_prev_date(min_date, kwargs['report_type'])
        prev_records = model.query.filter(
            model.report_date == prev_date,
        ).all()
        record_map = defaultdict(lambda: defaultdict(dict))
        for record in chain(prev_records, items):
            record_map[record.report_date][record.asset] = record
        result = []
        for item in items:
            item = item.to_dict()
            result.append(item)

        return dict(
            total=pagination.total,
            items=result,
            order_types=cls.ORDER_TYPES,
            user_types=model.UserType,
            langs=langs,
        )


@ns.route('/visit-async-download')
@respond_with_code
class AssetUserVisitReportAsyncDownloadResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_time=DateField(required=True),
        end_time=DateField(required=True),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-用户关注报表-异步下载"""
        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_balance_visit_report.delay(
            email=g.user.email,
            is_daily=kwargs['report_type'] is ReportType.DAILY,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )


@ns.route('/exposure')
@respond_with_code
class AssetUserExposureReportResource(Resource):
    ORDER_TYPES = {
        'report_date': '日期',
        'asset': '币种',
        'duration': '币种上线时长',
        'view_count': '币种浏览用户数',
        'detail_view_count': '币种详情页浏览用户数',
        'spot_view_count': '币币交易页浏览用户数',
        'perpetual_view_count': '合约交易页浏览用户数',
        'normal_trade_count': '交易普通用户数',
        'normal_spot_trade_count': '币币普通交易用户数',
        'normal_spot_trade_usd': '币币普通用户交易额',
        'normal_perpetual_trade_count': '合约普通交易用户数',
        'normal_perpetual_trade_usd': '合约普通用户交易额',
    }

    @classmethod
    @ns.use_kwargs(dict(
        user_type=EnumField(DailyAssetExposureReport.UserType),
        lang=EnumField(Language),
        duration=fields.Integer,
        asset=fields.String,
        order_type=fields.String(missing='report_date'),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-币种曝光报表"""

        model = DailyAssetExposureReport
        query = model.query
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)
        if duration := kwargs.get('duration'):
            query = query.filter(model.duration == duration)
        if asset := kwargs.get('asset'):  # asset == '' 无需展示
            query = query.filter(model.asset == asset)
        query = query.filter(model.asset != '')
        user_type = kwargs.get('user_type')
        user_type_str = user_type.name if user_type else ''
        query = query.filter(model.user_type == user_type_str)
        lang = kwargs.get('lang')
        lang_str = lang.name if lang else ''
        query = query.filter(model.lang == lang_str)

        query = query.order_by(getattr(model, kwargs['order_type']).desc())

        pagination = query.paginate(kwargs['page'], kwargs['limit'])
        items = pagination.items
        langs = {lang.name: lang_value for lang, lang_value in language_cn_names().items()}
        if not items:
            return dict(total=0, items=[], user_types=model.UserType, langs=langs)

        result = []
        for item in items:
            item = item.to_dict()
            item['normal_spot_trade_usd'] = amount_to_str(item['normal_spot_trade_usd'], 2)
            item['normal_perpetual_trade_usd'] = amount_to_str(item['normal_perpetual_trade_usd'], 2)
            result.append(item)

        return dict(
            total=pagination.total,
            items=result,
            user_types=model.UserType,
            langs=langs,
            order_types=cls.ORDER_TYPES,
        )


@ns.route('/exposure-async-download')
@respond_with_code
class AssetUserExposureReportAsyncDownloadResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        start_time=DateField(required=True),
        end_time=DateField(required=True),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-币种曝光报表-异步下载"""
        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_balance_exposure_report.delay(
            email=g.user.email,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )
