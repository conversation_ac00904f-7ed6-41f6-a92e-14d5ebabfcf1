# -*- coding: utf-8 -*-
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField, PageField, LimitField, DateField
from app.common import ReportType
from app.models import (
    DailyBrokerReport,
    MonthlyBrokerReport,
)

ns = Namespace("Broker")


@ns.route("/site-report")
@respond_with_code
class AutoInvestSiteReportResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            order=EnumField(["report_date", "deal_usd"], missing="report_date"),
            page=PageField(unlimited=True),
            limit=LimitField(missing=50),
        )
    )
    def get(cls, **kwargs):
        """ 报表-返佣报表-经纪商返佣报表 """
        if kwargs["report_type"] == ReportType.DAILY:
            model = DailyBrokerReport
        else:
            model = MonthlyBrokerReport

        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get("start_date"):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(model.report_date <= end_date)

        query = query.with_entities(
            model.report_date,
            model.broker_count,
            model.trade_user_count,
            model.spot_trade_usd,
            model.perpetual_trade_usd,
            model.spot_fee_usd,
            model.perpetual_fee_usd,
            model.refer_usd,
        )
        pagination = query.paginate(kwargs['page'], kwargs["limit"], error_out=False)
        items = pagination.items
        total = pagination.total
        result = []
        for i in items:
            result.append(
                {
                    "report_date": i.report_date,
                    "broker_count": i.broker_count,
                    "trade_user_count": i.trade_user_count,
                    "spot_trade_usd": i.spot_trade_usd,
                    "total_trade_usd": i.spot_trade_usd+i.perpetual_trade_usd,
                    "perpetual_trade_usd": i.perpetual_trade_usd,
                    "spot_fee_usd": i.spot_fee_usd,
                    "perpetual_fee_usd": i.perpetual_fee_usd,
                    "total_fee_usd": i.spot_fee_usd+i.perpetual_fee_usd,
                    "refer_usd": i.refer_usd,
                }
            )

        return dict(
            data=result,
            total=total,
        )
