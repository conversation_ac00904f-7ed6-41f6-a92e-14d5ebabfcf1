# -*- coding: utf-8 -*-
from enum import Enum
import json

from marshmallow import fields as mm_fields

from app.api.admin.margin.utils import Pagination
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField, PageField, LimitField
from app.caches.report import AppLoginLangIntervalStaticCache
from app.common import language_name_cn_names
from app.utils import MobilePusher, format_percent

ns = Namespace('Report - App')


@ns.route('/app-report')
@respond_with_code
class AppReportResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """统计-APP统计报表"""
        records = MobilePusher().get_push_report_slice_day()['records'][::-1]
        for item in records:
            item['report_date'] = item['name']

        return dict(
            total=len(records),
            items=records,
        )


@ns.route('/app-report-group')
@respond_with_code
class AppReportGroupResource(Resource):

    class TimeRange(Enum):

        ONE_DAY = '1d'
        SEVEN_DAYS = '7d'
        THIRTY_DAYS = '30d'
        NINETY_DAYS = '90d'

    @classmethod
    @ns.use_kwargs(dict(
        day=mm_fields.Date(),
        time_range=EnumField(TimeRange, enum_by_value=True, required=True),
        order_by=mm_fields.String,
        group_by=mm_fields.String,
        dim_or=mm_fields.String,
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """统计-APP维度统计"""
        kwargs['time_range'] = kwargs['time_range'].value
        if kwargs.get('day'):
            kwargs['day'] = kwargs['day'].strftime('%Y-%m-%d')
        page = kwargs.pop('page')
        limit = kwargs.pop('limit')

        resp = MobilePusher().get_push_report_group_by(**kwargs)
        records = resp['records'][::-1] if resp['records'] else []
        update_time = resp.get('update_time', '')

        if not records:
            return dict(total=0, items=records, update_time=update_time)
        # 因为聚合查找和展示原因，需要过滤掉total_device=0的数据
        records = [i for i in records if not i['total_device'] == 0]
        cal_list = ['active_device', 'active_user', 'new_device',
                    'new_user', 'total_device', 'total_user']
        sum_dict = {'name': 'ALL'}
        for col in cal_list:
            sum_dict[col] = sum([i[col] for i in records])
        total = len(records)
        for item in records:
            for cal_col in cal_list:
                item[f'perc_{cal_col}'] = item[cal_col] / sum_dict[cal_col] if sum_dict[cal_col] != 0 else 0
        result = Pagination(page, limit, total, records, need_slice=True)
        for cal_col in cal_list:
            sum_dict[f'perc_{cal_col}'] = 1
        result.items.insert(0, sum_dict)
        return dict(
            total=result.total,
            items=result.items,
            update_time=update_time,
        )


@ns.route('/app-login-users-lang')
@respond_with_code
class AppLoginUsersLangResource(Resource):

    class TimeRange(Enum):

        ONE_DAY = 1
        SEVEN_DAYS = 7
        THIRTY_DAYS = 30
        NINETY_DAYS = 90
        HALF_YEAY = 180
        ONE_YEAR = 365
        ALL = -1

    @classmethod
    @ns.use_kwargs(dict(
        time_range=EnumField(TimeRange, required=True),
    ))
    def get(cls, **kwargs):
        """APP统计-语区分布"""
        cache = AppLoginLangIntervalStaticCache()
        ret = cache.hget(kwargs['time_range'].value)
        if not ret:
            return {}
        res = json.loads(ret)
        items = res['items']
        items.sort(key=lambda x: x['count'], reverse=True)
        all_count = items[0]['count']
        for item in items:
            val = item['count']/all_count if all_count else 0
            lang_percent = format_percent(val)
            item['lang_percent'] = lang_percent

        return {
                'data': items,
                'updated_at': res['updated_at'],
                'langs': language_name_cn_names()
                }
