# -*- coding: utf-8 -*-
from collections import defaultdict
import re
from typing import Type
from flask import request, g
from sqlalchemy import or_
from webargs import fields as wa_fields
from flask_restx import fields as fx_fields

from app.exceptions.admin import SuperAdminRequire
from app.common.constants import Language
from ..common import (
    Resource, Namespace,
    respond_with_code
)
from ..common.decorators import require_admin_operation_token
from ..common.fields import (
    EnumField, PageField, LimitField,
    EnumMarshalField, TimestampMarshalField,
)
from app.utils import export_xlsx
from ...business.auth import get_admin_user_name_map, is_super_user, update_role_permission_cache, \
    update_permission_cache, get_admin_user_email_and_name_map
from ...business.email import send_verification_code_email
from ...caches import EmailCodeCache, EmailCodeTokenCache
from ...caches.admin import (AdminUserLoginTokenCache, AdminUserOperationTokenCache,
                             AdminWebAuthnOperationType)
from ...common import EmailCodeType, OPERATION_TOKEN_SIZE
from ...exceptions import (
    InvalidArgument,
    EmailCodeVerificationFailed,
)
from ...models import AdminUser, User, db, AdminRole, AdminUserRole, \
    AdminPermission, AdminRolePermission
from app.models.authority import AdminUserWebAuthn
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectSystem
from ...utils import query_to_page, new_verification_code, new_hex_token

ns = Namespace('Admin - Auth')


@ns.route('/admin-users')
@respond_with_code
class AdminUserList(Resource):

    @classmethod
    def get(cls):
        """系统-权限-管理员列表"""
        keyword = request.args.get('keyword')
        status = request.args.get('status')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 100))
        query = AdminUser.query
        if status:
            query = query.filter(AdminUser.status == status)
        if keyword:
            if keyword.isdigit():
                query = query.filter(AdminUser.user_id == keyword)
            elif '@' in keyword:
                user_id = User.full_match_user(keyword)
                query = query.filter(AdminUser.user_id == user_id)
            else:
                query = query.filter(
                    or_(
                        AdminUser.name.contains(keyword),
                        AdminUser.department.contains(keyword),
                        AdminUser.position.contains(keyword)
                    )
                )
        records = query.paginate(page, limit, error_out=False)
        name_map = get_admin_user_name_map([int(item.op_user)
                                            for item in records.items if item.op_user.isdigit()])
        q = AdminUserWebAuthn.query.filter(
            AdminUserWebAuthn.user_id.in_([v.user_id for v in records.items]),
            AdminUserWebAuthn.status == AdminUserWebAuthn.Status.VALID
        ).with_entities(AdminUserWebAuthn.user_id,
                        AdminUserWebAuthn.id,
                        AdminUserWebAuthn.name).all()
        user_webauthn_list = defaultdict(list)
        for _v in q:
            user_webauthn_list[_v.user_id].append(
                dict(name=_v.name,
                     user_id=_v.user_id,
                     id=_v.id)
            )
        return dict(
            items=[
                {
                    'id': r.id,
                    'user_id': r.user_id,
                    'email': r.user.main_user_email,
                    'name': r.name,
                    'binding_webauthn': r.user_id in user_webauthn_list.keys(),
                    'webauthn_list': list([dict(**v, admin_record_id=r.id)
                                           for v in
                                           user_webauthn_list[r.user_id]]),
                    'department': r.department,
                    'position': r.position,
                    'remark': r.remark,
                    'status': r.status,
                    'op_user': r.op_user,
                    'op_user_name': name_map.get(int(r.op_user)) if r.op_user.isdigit() else '系统',
                    'updated_at': r.updated_at
                } for r in records.items
            ],
            total=records.total,
            extra=dict(
                statuses=AdminUser.Status,
                roles=[{'key': r.id, 'label': r.name} for r in
                       AdminRole.query.filter(
                           AdminRole.status == AdminRole.Status.PASSED
                       ).all()]
            )
        )

    @classmethod
    def post(cls):
        """系统-权限-添加管理员"""
        data = request.get_json(silent=True)
        account = data['account'].strip()
        name = data['name'].strip()
        department = data['department'].strip()
        position = data['position'].strip()
        remark = data.get('remark') or ''
        if not all((account, department, position)):
            raise InvalidArgument(message='不能为空')

        user = User.query.filter(User.email == account).first()
        if not user:
            raise InvalidArgument(message='用户不存在')

        if AdminUser.query.filter(
            AdminUser.user_id == user.id,
        ).first():
            raise InvalidArgument(message='用户已存在')

        db.session.add(AdminUser(
            user_id=user.id,
            name=name,
            department=department,
            position=position,
            remark=remark,
            op_user=g.user.id
        ))
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminUser,
            detail=dict(account=account, name=name, department=department, position=position),
            target_user_id=user.id,
        )


def check_id(_id: int, model_cls: Type[db.Model]) -> db.Model:
    info = model_cls.query.filter(
        model_cls.id == _id
    ).first()
    if not info:
        raise InvalidArgument(message='id不存在')
    return info


# noinspection PyUnresolvedReferences
@ns.route('/admin-users/<int:admin_record_id>')
@respond_with_code
class EditAdminUser(Resource):

    @classmethod
    def put(cls, admin_record_id):
        """系统-权限-编辑管理员"""
        user_info = check_id(admin_record_id, AdminUser)
        old_data = user_info.to_dict(enum_to_name=True)
        data = request.get_json(silent=True)
        user_info.name = data['name'].strip()
        user_info.department = data['department'].strip()
        user_info.position = data['position'].strip()
        user_info.remark = data.get('remark') or ''
        user_info.op_user = g.user.id
        if data['status'] is True:
            user_info.status = AdminUser.Status.PASSED
        elif data['status'] is False:
            user_info.status = AdminUser.Status.DELETED
        db.session.commit()
        # update_menus([user_info.user_id])
        update_permission_cache([user_info.user_id])

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminUser,
            old_data=old_data,
            new_data=user_info.to_dict(enum_to_name=True),
            target_user_id=user_info.user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            webauthn_id=wa_fields.Integer(required=True)
        )
    )
    def patch(cls, admin_record_id: int, **kwargs):
        """系统-权限-删除通行密钥"""
        user_info = check_id(admin_record_id, AdminUser)
        user_id = user_info.user_id
        webauthn = AdminUserWebAuthn.query.filter(
            AdminUserWebAuthn.user_id == user_id,
            AdminUserWebAuthn.id == kwargs['webauthn_id'],
            AdminUserWebAuthn.status == AdminUserWebAuthn.Status.VALID).first()
        if not webauthn:
            raise InvalidArgument
        webauthn.status = AdminUserWebAuthn.Status.DELETED
        db.session_add_and_commit(webauthn)
        count = AdminUserWebAuthn.query.filter(
            AdminUserWebAuthn.user_id == user_id,
            AdminUserWebAuthn.status == AdminUserWebAuthn.Status.VALID).count()
        if count == 0:
            AdminUserLoginTokenCache(user_id).clear_tokens()
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminUserWebauthn,
            detail=dict(id=webauthn.id, name=webauthn.name),
            target_user_id=user_id,
        )


# noinspection PyUnresolvedReferences
@ns.route('/admin-users/<int:admin_user_id>/roles')
@respond_with_code
class EditAdminUserRole(Resource):

    @classmethod
    def get(cls, admin_user_id):
        """系统-权限-管理员角色列表"""
        user = check_id(admin_user_id, AdminUser)
        records = AdminUserRole.query.select_from(
            AdminUserRole
        ).join(AdminRole, AdminUserRole.admin_role_id == AdminRole.id).filter(
            AdminUserRole.user_id == user.user_id,
            AdminRole.status == AdminRole.Status.PASSED
        ).with_entities(
            AdminRole.id.label('id'),
        )
        return [r.id for r in records]

    @classmethod
    def put(cls, admin_user_id):
        """系统-权限-添加管理员角色"""
        user = check_id(admin_user_id, AdminUser)
        old_roles = {str(role.admin_role_id) for role in AdminUserRole.query.filter(
            AdminUserRole.user_id == user.user_id
        ).all()}
        AdminUserRole.query.filter(
            AdminUserRole.user_id == user.user_id
        ).delete()
        db.session.commit()
        all_role_ids = set([r.id for r in AdminRole.query.filter(
            AdminRole.status == AdminRole.Status.PASSED
        )])
        data_list = request.get_json(silent=True)
        if not isinstance(data_list, list):
            raise InvalidArgument(message='数据格式错误')
        role_list = set()
        for role_id in data_list:
            if role_id not in all_role_ids:
                continue
            db.session.add(
                AdminUserRole(
                    user_id=user.user_id,
                    admin_role_id=role_id,
                    op_user=g.user.id
                )
            )
            role_list.add(str(role_id))
        db.session.commit()
        # update_menus([user.id])
        update_permission_cache([user.user_id])

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminUserRoles,
            detail=dict(
                delete_roles=';'.join(old_roles - role_list),
                new_roles=';'.join(role_list - old_roles),
            ),
            target_user_id=user.user_id,
        )


@ns.route('/roles')
@respond_with_code
class AdminRoleList(Resource):

    @classmethod
    def get(cls):
        """系统-权限-角色列表"""
        keyword = request.args.get('keyword')
        status = request.args.get('status')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 100))
        query = AdminRole.query
        if status:
            query = query.filter(AdminRole.status == status)
        if keyword:
            query = query.filter(AdminRole.name == keyword)
        records = query.paginate(page, limit, error_out=False)
        name_map = get_admin_user_name_map({int(item.op_user)
                                            for item in records.items if item.op_user.isdigit()})

        # 克隆角色用
        role_dict = {v.id: v.name for v in
                     AdminRole.query.filter(
                         AdminRole.status == AdminRole.Status.PASSED
                     ).order_by(
                AdminRole.id.desc()
            ).limit(100).with_entities(AdminRole.id, AdminRole.name).all()}

        return dict(
            items=[
                {
                    'id': r.id,
                    'name': r.name,
                    'remark': r.remark,
                    'status': r.status,
                    'op_user': r.op_user,
                    'op_user_name': name_map.get(int(r.op_user)) if r.op_user.isdigit() else '系统',
                    'updated_at': r.updated_at
                } for r in records.items
            ],
            total=records.total,
            extra=dict(
                statuses=AdminRole.Status,
                permissions=[
                    {'key': r.id, 'label': r.display_name}
                    for r in AdminPermission.query.filter(
                        AdminPermission.status ==
                        AdminPermission.Status.PASSED,
                    ).all()],
                role_dict=role_dict
            )
        )

    @classmethod
    def post(cls):
        """系统-权限-添加角色"""
        data = request.get_json(silent=True)
        name = data['name'].strip()
        remark = data.get('remark') or ''

        if AdminRole.query.filter(
                AdminRole.name == name
        ).first():
            raise InvalidArgument(message='role 已存在')

        db.session.add(AdminRole(
            name=name,
            remark=remark,
            op_user=g.user.id
        ))
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminRole,
            detail=dict(name=name),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            role_ids=wa_fields.List(wa_fields.String, required=True),
            name=wa_fields.String(required=True),
            remark=wa_fields.String(default='', missing=''),
        )
    )
    def patch(cls, **kwargs):
        """系统-权限-克隆角色"""
        name = kwargs['name']
        remark = kwargs.get('remark', '') or ''
        role_ids = kwargs['role_ids']

        if AdminRole.query.filter(
                AdminRole.name == name
        ).first():
            raise InvalidArgument(message='role 已存在')

        all_permission_ids = set([r.id for r in AdminPermission.query.filter(
            AdminPermission.status == AdminPermission.Status.PASSED
        ).with_entities(AdminPermission.id).all()])

        q = AdminRolePermission.query.filter(
            AdminRolePermission.admin_role_id.in_(role_ids),
            AdminRolePermission.status == AdminRolePermission.Status.PASSED).with_entities(
            AdminRolePermission.admin_permission_id
        ).all()
        permission_ids = {v.admin_permission_id for v in q}

        admin_auth_permissions = \
            AdminPermission.query.filter(AdminPermission.id.in_(permission_ids),
                                         AdminPermission.rule.like('/admin/auth%'),
                                         AdminPermission.status == AdminPermission.Status.PASSED).all()
        admin_auth_permission_ids = {item.id for item in admin_auth_permissions}

        if admin_auth_permission_ids:
            raise SuperAdminRequire(message=f'克隆角色中包含管理员相关权限，不支持克隆, 管理员相关权限需要创建角色后单独添加')

        role = AdminRole(
            name=name,
            remark=remark,
            op_user=g.user.id
        )
        db.session.add(role)
        db.session.flush()

        new_permission_ids = set()
        for permission_id in permission_ids:
            if permission_id not in all_permission_ids:
                continue
            db.session.add(
                AdminRolePermission(
                    admin_role_id=role.id,
                    admin_permission_id=permission_id,
                )
            )
            new_permission_ids.add(str(permission_id))
        db.session.commit()
        # update_menus()
        update_role_permission_cache(role.id)
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminRole,
            detail=dict(name=name, remark=remark, role_ids=role_ids),
        )
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminRolePermissions,
            special_data=dict(
                role_id=role.id,
                clone_role_ids=role_ids,
                permision_ids=';'.join(new_permission_ids),
            )
        )


@ns.route('/roles/<int:role_id>')
@respond_with_code
class EditAdminRole(Resource):

    @classmethod
    def put(cls, role_id):
        """系统-权限-修改角色"""
        role_info = check_id(role_id, AdminRole)
        old_data = role_info.to_dict(enum_to_name=True)
        data = request.get_json(silent=True)
        role_info.name = data['name']
        role_info.remark = data.get('remark') or ''
        role_info.op_user = g.user.id
        if data['status'] is True:
            role_info.status = AdminRole.Status.PASSED
            AdminUserRole.query.filter(
                AdminUserRole.admin_role_id == role_id
            ).update(
                {'status': AdminUserRole.Status.PASSED},
                synchronize_session=False
            )
        elif data['status'] is False:
            role_info.status = AdminRole.Status.DELETED
            AdminUserRole.query.filter(
                AdminUserRole.admin_role_id == role_id
            ).update(
                {'status': AdminUserRole.Status.DELETED},
                synchronize_session=False
            )
        db.session.commit()
        # update_menus()
        update_role_permission_cache(role_id)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminRole,
            old_data=old_data,
            new_data=role_info.to_dict(enum_to_name=True),
        )


# noinspection PyUnresolvedReferences
@ns.route('/roles/<int:role_id>/permissions')
@respond_with_code
class EditAdminRolePermission(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            export=wa_fields.Boolean(missing=False)
        )
    )
    def get(cls, role_id, **kwargs):
        """系统-权限-角色权限列表"""
        records = AdminRolePermission.query.select_from(AdminRolePermission).join(
            AdminPermission, AdminRolePermission.admin_permission_id == AdminPermission.id
        ).filter(
            AdminRolePermission.admin_role_id == role_id,
            AdminPermission.status == AdminPermission.Status.PASSED
        ).with_entities(
            AdminPermission.id.label('id'),
        )
        export = kwargs.get('export', False)
        if not export:
            return [r.id for r in records]
        else:
            role_name = AdminRole.query.get(role_id).name
            file_name = f"role_permissions-{role_id}-{role_name}.xlsx"
            export_headers = (
                {"field": "permission_id", Language.ZH_HANS_CN: "权限ID"},
                {"field": "permission_name", Language.ZH_HANS_CN: "权限名称"},
            )

            def remove_illegal_chars(text):
                # 移除控制字符（\x00-\x1F）
                return re.sub(r'[\x00-\x1F\x7F]', '', text)

            permissions = {
                r.id: {'permission_id': r.id, 'permission_name': remove_illegal_chars(r.display_name)}
                for r in AdminPermission.query.filter(
                    AdminPermission.status ==
                    AdminPermission.Status.PASSED,
                ).all()
            }
            export_data = list()
            for r in records:
                if r.id in permissions:
                    export_data.append(
                        permissions[r.id]
                    )
            return export_xlsx(
                filename=file_name,
                data_list=export_data,
                export_headers=export_headers
            )

    @classmethod
    def put(cls, role_id):
        """系统-权限-编辑角色"""
        check_id(role_id, AdminRole)
        
        all_permission_ids = set([r.id for r in AdminPermission.query.filter(
            AdminPermission.status == AdminPermission.Status.PASSED
        )])
        data_list = request.get_json(silent=True)
        if not isinstance(data_list, list):
            raise InvalidArgument(message='数据格式错误')
        
        admin_auth_permissions = \
            AdminPermission.query.filter(AdminPermission.id.in_(data_list),
                                         AdminPermission.rule.like('/admin/auth%'),
                                         AdminPermission.status == AdminPermission.Status.PASSED).all()
        admin_auth_permission_ids = {item.id for item in admin_auth_permissions}
        
        if not is_super_user(g.user.id) and admin_auth_permission_ids \
            and not AdminRolePermission.query.filter(
                    AdminRolePermission.admin_role_id == role_id,
                    AdminRolePermission.admin_permission_id.in_(admin_auth_permission_ids),
                    AdminRolePermission.status == AdminRolePermission.Status.PASSED
                ).with_entities(AdminRolePermission.admin_permission_id).first():
            raise SuperAdminRequire

        old_permission_ids = {str(permission.admin_permission_id) for permission in AdminRolePermission.query.filter(
            AdminRolePermission.admin_role_id == role_id
        ).all()}
        AdminRolePermission.query.filter(
            AdminRolePermission.admin_role_id == role_id
        ).delete()
        db.session.commit()
        new_permission_ids = set()
        for permission_id in data_list:
            if permission_id not in all_permission_ids:
                continue
            db.session.add(
                AdminRolePermission(
                    admin_role_id=role_id,
                    admin_permission_id=permission_id,
                )
            )
            new_permission_ids.add(str(permission_id))
        db.session.commit()
        # update_menus()
        update_role_permission_cache(role_id)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminRolePermissions,
            special_data=dict(
                role_id=role_id,
                delete_ids=';'.join(old_permission_ids - new_permission_ids),
                add_ids=';'.join(new_permission_ids - old_permission_ids),
            ),
            only_special_data=True,
        )


# noinspection PyUnresolvedReferences
@ns.route('/roles/<int:role_id>/users')
@respond_with_code
class GetRoleUser(Resource):

    @classmethod
    def get(cls, role_id):
        """系统-权限-角色用户列表"""
        records = AdminUserRole.query.filter(
            AdminUserRole.admin_role_id == role_id,
            AdminUserRole.status == AdminUserRole.Status.PASSED
        ).with_entities(AdminUserRole.user_id).all()
        u_ids = [v.user_id for v in records]
        email_map = get_admin_user_email_and_name_map(u_ids)
        return [f"{email_map.get(u_id, '')[0]} ({email_map.get(u_id, '')[1]}) ({u_id})" for u_id in u_ids]


@ns.route('/permissions')
@respond_with_code
class AdminPermissionList(Resource):

    marshal_fields = {
        'id': fx_fields.Integer,
        'name': fx_fields.String,
        'endpoint': fx_fields.String,
        'rule': fx_fields.String,
        'method': EnumMarshalField(
            AdminPermission.Method, output_field_lower=False),
        'app': EnumMarshalField(AdminPermission.App, output_field_name=False),
        'status': EnumMarshalField(
            AdminPermission.Status, output_field_lower=False),
        'op_user': fx_fields.Integer,
        'updated_at': TimestampMarshalField
    }

    @classmethod
    @ns.use_kwargs(dict(
        keyword=wa_fields.String,
        status=EnumField(AdminPermission.Status),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """系统-权限-权限列表"""
        query = AdminPermission.query.order_by(AdminPermission.id.desc())
        if status := kwargs.get('status'):
            query = query.filter(AdminPermission.status == status)
        if keyword := kwargs.get('keyword'):
            query = query.filter(
                or_(
                    AdminPermission.name.contains(keyword),
                    AdminPermission.endpoint.contains(keyword),
                    AdminPermission.rule.contains(keyword)
                )
            )
        result = query_to_page(query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        op_user_ids = {item['op_user'] for item in result['data']}
        name_map = get_admin_user_name_map(op_user_ids)
        for item in result['data']:
            item['op_user_name'] = name_map.get(item['op_user'])
        return result


# noinspection PyUnresolvedReferences
@ns.route('/permissions/<int:permission_id>')
@respond_with_code
class EditAdminPermission(Resource):

    @classmethod
    def put(cls, permission_id):
        """系统-权限-编辑权限"""
        permission = check_id(permission_id, AdminPermission)
        old_data = permission.to_dict(enum_to_name=True)
        data = request.get_json(silent=True)
        permission.name = data['name'].strip()
        permission.op_user = g.user.id
        if data['status'] is True:
            permission.status = AdminPermission.Status.PASSED
        elif data['status'] is False:
            permission.status = AdminPermission.Status.DELETED
        db.session.commit()
        # update_menus()
        update_permission_cache()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AdminPermission,
            old_data=old_data,
            new_data=permission.to_dict(enum_to_name=True),
        )


# noinspection PyUnresolvedReferences
@ns.route('/permissions/<int:permission_id>/roles')
@respond_with_code
class AdminPermissionRoles(Resource):

    @classmethod
    def get(cls, permission_id):
        """系统-权限-权限所属角色"""
        records = AdminRolePermission.query.join(
            AdminRolePermission.role
        ).filter(
            AdminRole.status == AdminRole.Status.PASSED,
            AdminRolePermission.admin_permission_id == permission_id,
        ).with_entities(
            AdminRole.name
        ).all()
        return [r.name for r in records]


@ns.route('/email/code')
@respond_with_code
class EmailCodeResource(Resource):

    @classmethod
    @require_admin_operation_token
    def get(cls):
        user = g.operation_user
        email = user.email
        if not email:
            raise InvalidArgument
        code = new_verification_code()
        code_type = EmailCodeType.BINDING_ADMIN_WEBAUTHN
        EmailCodeCache(email, code_type).set_code(code)
        send_verification_code_email.delay(
            email, code_type.value, email, code, g.lang)

    @classmethod
    @require_admin_operation_token(delete_cache=True)
    @ns.use_kwargs(dict(
        validate_code=wa_fields.String(required=True)
    ))
    def post(cls, **kwargs):
        validate_code = kwargs['validate_code']
        try:
            operation_type = g.operation_type
        except ValueError:
            raise InvalidArgument
        mapping = {
            AdminWebAuthnOperationType.BINDING_WEBAUTHN: EmailCodeType.BINDING_ADMIN_WEBAUTHN
        }
        _cache_cls = AdminUserOperationTokenCache
        if operation_type not in mapping:
            raise InvalidArgument
        code_type = mapping[operation_type]
        user = g.operation_user
        email = user.email
        if not email:
            raise InvalidArgument
        cache = EmailCodeCache(email, code_type)
        if not cache.verify_code(validate_code):
            raise EmailCodeVerificationFailed
        cache.delete()
        token = new_hex_token(OPERATION_TOKEN_SIZE)
        user_id = user.id
        EmailCodeTokenCache(token).set_user_and_email(user_id, email)
        if operation_type == AdminWebAuthnOperationType.BINDING_WEBAUTHN:
            _binding_token = new_hex_token(OPERATION_TOKEN_SIZE)
            _cache_cls(_binding_token, AdminWebAuthnOperationType.BINDING_WEBAUTHN).set_user(user_id)
            return dict(
                email_code_token=token,
                binding_token=_binding_token
            )
        return dict(
            email_code_token=token
        )
