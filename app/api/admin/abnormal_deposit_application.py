# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from decimal import Decimal, ROUND_DOWN
from datetime import timed<PERSON><PERSON>, datetime
from typing import List, Optional

from flask import g
from sqlalchemy import or_, and_, func
from webargs import fields

from app.api.admin.operation.operation import AssignAuditorMixin
from app.business.abnormal_deposit import AbnormalDepositBusiness
from app.business.auth import get_admin_user_name_map
from app.caches.operation import AbnormalDepositApplicationAssignAuditorsCache

from app.common.constants import MessageTitle, MessageContent, MessageWebLink, PrecisionEnum, Language, BalanceBusiness, \
    ADMIN_EXPORT_LIMIT
from app.models import db, User, Message
from app.models.wallet import (
    Deposit,
    AbnormalDepositApplication,
    AbnormalDepositApplicationChangelog,
    AbnormalDepositApplicationTransferHistory,
)
from app.exceptions import InvalidArgument
from app.config import config
from app.utils import batch_iter, amount_to_str, datetime_to_str, AWSBucketPrivate
from app.business import ServerClient, SPOT_ACCOUNT_ID, WalletClient, CacheLock, LockKeys, PriceManager
from app.business.user import UserPreferences, get_user_remark
from app.business.email import send_abnormal_deposit_application_fee_email, send_abnormal_deposit_application_passed_email
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import LimitField, PageField, EnumField, PositiveDecimalField, TimestampField
from app.assets import asset_to_chains, has_chain, list_all_assets
from app.assets.asset import try_get_asset_chain_config
from app.utils import now, export_xlsx, quantize_amount


ns = Namespace("Abnormal-Deposit-Application-Admin")


@ns.route("/list")
@respond_with_code
class AbnormalDepositApplicationsResource(Resource, AssignAuditorMixin):
    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "订单ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "申请时间"},
        {"field": "processed_at", Language.ZH_HANS_CN: "提交资料时间"},
        {"field": "checked_at", Language.ZH_HANS_CN: "复审时间"},
        {"field": "processed_at", Language.ZH_HANS_CN: "完成时间"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "tx_amount", Language.ZH_HANS_CN: "链上数量"},
        {"field": "amount", Language.ZH_HANS_CN: "数量"},
        {"field": "chain", Language.ZH_HANS_CN: "公链"},
        {"field": "tx_id", Language.ZH_HANS_CN: "交易ID"},
        {"field": "type", Language.ZH_HANS_CN: "类型"},
        {"field": "admin_status", Language.ZH_HANS_CN: "状态"},
        {"field": "total_usd", Language.ZH_HANS_CN: "币种价值"},
        {"field": "show_fee_amount", Language.ZH_HANS_CN: "手续费"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    # admin筛选、展示的状态
    ADMIN_STATUS_DICT = AbnormalDepositBusiness.ADMIN_STATUS_DICT

    SORT_NAME_DICT = {
        AbnormalDepositApplication.id.name: "申请时间",
        AbnormalDepositApplication.additional_info_at.name: "提交资料时间",
        AbnormalDepositApplication.audited_at.name: "初审时间",
        AbnormalDepositApplication.checked_at.name: "复审时间",
        AbnormalDepositApplication.double_checked_at.name: "三审时间",
        AbnormalDepositApplication.processed_at.name: "完成时间",
    }

    @classmethod
    def get_admin_status(cls, row: AbnormalDepositApplication) -> str:
        return AbnormalDepositBusiness.get_admin_status(row)

    @classmethod
    def admin_status_to_filter(cls, admin_status: str):
        return AbnormalDepositBusiness.admin_status_to_filter(admin_status)

    @classmethod
    def get_need_audit_admin_status(cls) -> set:
        return AbnormalDepositBusiness.NEED_AUDIT_STATUSES

    @classmethod
    def get_need_audit_filter(cls):
        return or_(
            and_(cls.admin_status_to_filter("AUDIT_REQUIRED")),
            and_(cls.admin_status_to_filter("ADDITIONAL_INFO_AUDIT_REQUIRED")),
            and_(cls.admin_status_to_filter("CHECK_REQUIRED")),
            and_(cls.admin_status_to_filter("DOUBLE_CHECK_REQUIRED")),
            and_(cls.admin_status_to_filter("CREATED")),
        )

    @classmethod
    def get_need_additional_info_at(cls, row_id: int) -> Optional[datetime]:
        # 获取变为待补充资料时间，UNKNOWN变为NEED_ADDITIONAL_INFO_TYPES的记录作为时间
        change_logs = AbnormalDepositApplicationChangelog.query.filter(
            AbnormalDepositApplicationChangelog.application_id == row_id,
            AbnormalDepositApplicationChangelog.change_type == AbnormalDepositApplicationChangelog.ChangeType.TYPE.name,
        ).order_by(AbnormalDepositApplicationChangelog.id.desc()).all()
        need_additional_info_types = [i.name for i in AbnormalDepositApplication.NEED_ADDITIONAL_INFO_TYPES]
        need_additional_info_at = None
        for row in change_logs:
            detail = json.loads(row.detail)
            if (detail['old'] == AbnormalDepositApplication.Type.UNKNOWN.name
                    and detail['new'] in need_additional_info_types):
                need_additional_info_at = row.created_at
                break
        return need_additional_info_at

    @classmethod
    @ns.use_kwargs(
        dict(
            start_time=TimestampField(is_ms=True),
            end_time=TimestampField(is_ms=True),
            user_id=fields.Integer,
            id=fields.Integer,
            asset=fields.String,
            chain=fields.String,
            tx_id=fields.String,
            type=EnumField(AbnormalDepositApplication.Type),
            status=EnumField(ADMIN_STATUS_DICT.keys()),
            sort_name=EnumField(SORT_NAME_DICT.keys()),
            sort_type=EnumField(["asc", "desc"], missing="desc"),
            is_high_total_usd=fields.Boolean,  # 高币种价值
            auditor_id=fields.Integer,
            next=fields.Boolean,  # 下一条
            cur_id=fields.Integer,  # 排除下一条id
            export=fields.Boolean,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 运营>充值找回>订单管理-列表 """
        auditor_map = {}
        auditor_ids_map = AbnormalDepositApplicationAssignAuditorsCache().read()
        for auditor_id, ids in auditor_ids_map.items():
            for id_ in ids:
                auditor_map[id_] = int(auditor_id)

        model = AbnormalDepositApplication
        query = model.query.filter()
        if sort_name := kwargs.get("sort_name"):
            sort_col = getattr(model, sort_name)
        else:
            sort_col = model.id
        if kwargs.get("sort_type") == "asc":
            query = query.order_by(sort_col.asc())
        else:
            query = query.order_by(sort_col.desc())
        if start_time := kwargs.get("start_time"):
            query = query.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            query = query.filter(model.created_at <= end_time)
        if status := kwargs.get("status"):
            query = query.filter(cls.admin_status_to_filter(status))
        for field in ["user_id", "id", "asset", "chain", "tx_id", "type"]:
            if val := kwargs.get(field):
                query = query.filter(getattr(model, field) == val)
        if (is_high_total_usd := kwargs.get("is_high_total_usd")) is not None:
            if is_high_total_usd:
                query = query.filter(model.total_usd >= 10000)
            else:
                query = query.filter(model.total_usd < 10000)
        if (auditor_id := kwargs.get('auditor_id')) is not None:
            if auditor_id == 0:
                to_audit_ids = {r.id for r in model.query.filter(
                    cls.get_need_audit_filter()
                ).with_entities(
                    model.id
                ).all()}
                assigned_ids = set()
                for ids in auditor_ids_map.values():
                    assigned_ids |= set(ids)
                to_audit_ids -= assigned_ids
            else:
                to_audit_ids = auditor_ids_map.get(str(auditor_id), [])
            query = query.filter(
                model.id.in_(to_audit_ids)
            )

        is_next = kwargs.get("next")
        is_export = kwargs.get("export")
        total = 0
        if is_next:
            # 下一条待审核：补充资料审核中、初审中、复审中、三审中、待定
            if not kwargs.get("status"):
                query = query.filter(
                    cls.get_need_audit_filter()
                )
            admin_user_id = g.user.id
            ids = auditor_ids_map.get(str(admin_user_id), [])
            query = query.filter(model.id.in_(ids))
            tmp_rows = query.all()
            row = cls.get_next_row(kwargs['cur_id'], tmp_rows, sort_name)
            rows = [row] if row else []
        else:
            if is_export:
                rows = query.limit(ADMIN_EXPORT_LIMIT).all()
                total = len(rows)
            else:
                pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
                total = pagination.total
                rows = pagination.items

        application_assets = [
            i for i, in AbnormalDepositApplication.query.with_entities(AbnormalDepositApplication.asset.distinct()).all()
        ]
        asset_chains_dict = asset_to_chains()
        chain_set = set()
        asset_set = set()
        asset_set.update(application_assets)
        for asset, chains in asset_chains_dict.items():
            asset_set.add(asset)
            for c in chains:
                if c != asset:
                    chain_set.add(c)

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        is_take_fee_map = AbnormalDepositBusiness.batch_get_is_take_fee(rows)
        client = WalletClient()
        txs = [(row.chain, row.tx_id) for row in rows]
        tx_urls = client.get_explorer_txs_url(txs)
        items = []
        auditor_ids = []
        for idx, row in enumerate(rows):
            row: AbnormalDepositApplication
            d = row.to_dict(enum_to_name=True)
            d["show_fee_asset"] = row.fee_asset or row.expect_fee_asset
            d["show_fee_amount"] = row.fee_amount or row.expect_fee_amount
            d["admin_status"] = cls.get_admin_status(row)
            d["email"] = user_email_map.get(row.user_id, "")
            d["tx_url"] = tx_urls[idx]
            d["auditor_id"] = row.checker_id or auditor_map.get(row.id)
            d["is_need_fee"] = row.is_need_fee
            d["is_take_fee"] = is_take_fee_map.get(row.id, False)
            auditor_ids.append(d["auditor_id"])
            items.append(d)

        auditor_name_map = get_admin_user_name_map(auditor_ids)
        for item in items:
            item['auditor_name'] = auditor_name_map.get(item['auditor_id'], '--')

        if not is_export:
            audit_filter = None
            audit_statuses = cls.get_need_audit_admin_status()
            if status := kwargs.get('status'):
                if status in audit_statuses:
                    audit_filter = cls.admin_status_to_filter(status)
            else:
                audit_filter = cls.get_need_audit_filter()
            audit_required_ids = set()
            if audit_filter is not None:
                audit_required_ids = {i.id for i in AbnormalDepositApplication.query.filter(
                    audit_filter
                ).with_entities(
                    AbnormalDepositApplication.id
                ).all()}
            return dict(
                items=items,
                total=total,
                extra=dict(
                    type_dict={i.name: i.value for i in AbnormalDepositApplication.Type},
                    admin_status_dict=cls.ADMIN_STATUS_DICT,
                    sort_name_dict=cls.SORT_NAME_DICT,
                    asset_list=list(asset_set),
                    chain_list=list(chain_set),
                    auditor_list=cls.get_auditor_list(auditor_ids_map, audit_required_ids),
                ),
            )
        else:
            for i in items:
                i["created_at"] = i["created_at"].strftime("%Y-%m-%d %H:%M:%S")
                i["checked_at"] = i["checked_at"].strftime("%Y-%m-%d %H:%M:%S") \
                    if i["checked_at"] else "/"
                i["additional_info_at"] = i["additional_info_at"].strftime("%Y-%m-%d %H:%M:%S") \
                    if i["additional_info_at"] else "/"

                if i["status"] in ["REJECTED", "CANCELLED", "FINISHED"]:
                    i["processed_at"] = i["processed_at"].strftime("%Y-%m-%d %H:%M:%S") if i["processed_at"] else "/"
                else:
                    i["processed_at"] = "/"
                i["admin_status"] = cls.ADMIN_STATUS_DICT[i["admin_status"]]
                i["type"] = AbnormalDepositApplication.Type[i["type"]].value
            return export_xlsx(
                filename="abnormal-deposit-application-list",
                data_list=items,
                export_headers=cls.export_headers,
            )

    @classmethod
    def get_next_row(cls, cur_id, rows, sort_name):
        if not rows:
            return
        model = AbnormalDepositApplication
        cur = model.query.get(cur_id)
        # 产品要求顺序： 先排有值的数据升序，排序列为None的数据按ID升序，到达最大值后下一条为最小值
        has_val_rows = sorted([i for i in rows if getattr(i, sort_name)], key=lambda x: getattr(x, sort_name))
        no_val_rows = sorted([i for i in rows if not getattr(i, sort_name)], key=lambda x: getattr(x, 'id'))
        sort_rows = has_val_rows + no_val_rows
        ids = [i.id for i in sort_rows]
        if cur.id in ids:
            idx = (ids.index(cur.id) + 1) % len(ids)
            row = sort_rows[idx]
        else:
            row = sort_rows[0]
        return row

    @classmethod
    def get_row(cls, row_id: int) -> AbnormalDepositApplication:
        row = AbnormalDepositApplication.query.filter(AbnormalDepositApplication.id == row_id).first()
        if not row:
            raise InvalidArgument(message=f"找回记录{row_id}不存在")

        return row

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            remark=fields.String(required=False),
            jump_url=fields.String(required=False),
            asset=fields.String(required=False),
            chain=fields.String(required=False),
            asset_rate=PositiveDecimalField(required=False),
            tx_amount=PositiveDecimalField(
                required=False,
                places=PrecisionEnum.COIN_PLACES,
                rounding=ROUND_DOWN,
            ),
        )
    )
    def patch(cls, **kwargs):
        """ 运营>充值找回>订单管理-编辑备注、审核页面-编辑币种、数目、公链 """
        row = cls.get_row(kwargs["id"])
        change_values = []
        new_remark = kwargs.get("remark", '')
        if row.remark != new_remark:
            change_values.append([f"备注'{row.remark}'", f"备注'{new_remark}'"])
            row.remark = new_remark
        new_jump_url = kwargs.get("jump_url", '')
        if row.jump_url != new_jump_url:
            change_values.append([f"跳转链接'{row.jump_url}'", f"跳转链接'{new_jump_url}'"])
            row.jump_url = new_jump_url
        ignore_is_manual = False
        pending_audit_statuses = [
            AbnormalDepositApplication.Status.CREATED,
            AbnormalDepositApplication.Status.AUDIT_REQUIRED,
            AbnormalDepositApplication.Status.CHECK_REQUIRED,
            AbnormalDepositApplication.Status.DOUBLE_CHECK_REQUIRED,
        ]
        if asset := kwargs.get("asset"):
            if row.status not in pending_audit_statuses:
                raise InvalidArgument(message=f"当前状态不允许修改币种")
            if row.asset != asset:
                change_values.append([f"币种'{row.asset}'", f"币种'{asset}'"])
                new_total_usd = quantize_amount(row.tx_amount * PriceManager.asset_to_usd(asset), 8)
                if new_total_usd != row.total_usd:
                    change_values.append([f"市值'{amount_to_str(row.total_usd)}'", f"市值'{amount_to_str(new_total_usd)}'"])
                    row.total_usd = new_total_usd
                kwargs['asset_rate'] = AbnormalDepositBusiness.get_asset_rate(asset)
                ignore_is_manual = True  # 修改币种时 不受【编辑过手续费，锁定手续费】的限制
            row.asset = asset

        if chain := kwargs.get("chain"):
            if not has_chain(chain):
                raise InvalidArgument(message=f"链不存在")
            if row.chain != chain:
                change_values.append([f"链'{row.chain}'", f"链'{chain}'"])
            row.chain = chain
        if tx_amount := kwargs.get("tx_amount"):
            if row.status not in pending_audit_statuses:
                raise InvalidArgument(message=f"当前状态不允许修改实际数量")
            if row.tx_amount != tx_amount:
                change_values.append([f"实际数量'{amount_to_str(row.tx_amount)}'", f"实际数量'{amount_to_str(tx_amount)}'"])

            if row.asset_rate:
                new_total_usd = quantize_amount(row.asset_rate * tx_amount, 8)
            elif row.tx_amount:
                new_total_usd = quantize_amount(row.total_usd / row.tx_amount * tx_amount, 8)
            else:
                new_total_usd = Decimal()
            if row.total_usd != new_total_usd:
                change_values.append([f"市值'{amount_to_str(row.total_usd)}'", f"市值'{amount_to_str(new_total_usd)}'"])

            row.tx_amount = tx_amount
            row.total_usd = new_total_usd
            AbnormalDepositBusiness.update_expect_fee_amount(row, admin_user_id=g.user.id)
        if (asset_rate := kwargs.get("asset_rate")) is not None:
            if row.status not in pending_audit_statuses:
                raise InvalidArgument(message=f"当前状态不允许修改币种汇率")
            if row.asset_rate != asset_rate:
                change_values.append(
                    [
                        f"币种汇率'{amount_to_str(row.asset_rate) if row.asset_rate is not None else '-'}'",
                        f"币种汇率'{amount_to_str(asset_rate)}'"
                    ]
                )

            if row.tx_amount:
                new_total_usd = quantize_amount(asset_rate * row.tx_amount, 8)
            else:
                new_total_usd = Decimal()
            if row.total_usd != new_total_usd:
                change_values.append([f"市值'{amount_to_str(row.total_usd)}'", f"市值'{amount_to_str(new_total_usd)}'"])

            row.asset_rate = asset_rate
            row.total_usd = new_total_usd
            AbnormalDepositBusiness.update_expect_fee_amount(row, admin_user_id=g.user.id, ignore_is_manual=ignore_is_manual)

        for old_value, new_value in change_values:
            AbnormalDepositApplicationChangelog.add(
                application_id=row.id,
                user_id=row.user_id,
                admin_user_id=g.user.id,
                change_type=AbnormalDepositApplicationChangelog.ChangeType.DETAIL.name,
                old_value=old_value,
                new_value=new_value,
            )
        db.session.commit()


@ns.route("/detail/<int:application_id>")
@respond_with_code
class AbnormalDepositApplicationDetailResource(Resource):

    @classmethod
    def get_category_reasons(cls) -> list[dict]:
        reason_em = AbnormalDepositApplication.RejectionReason
        category_reasons_map = {
            "第三方截图相关": [
                reason_em.NOT_UPLOAD_BLOCK_BROWSER_QUERY_SCREENSHOT,
                reason_em.UPLOAD_COMPLETED_THIRD_PARTY_SNAPSHOT,
                reason_em.THIRD_PARTY_TRANSFER_OUT_SCREENSHOT_MISMATCHED,
                reason_em.THIRD_PARTY_WITHDRAWAL_SNAPSHOT_FROM_EMAIL,
                reason_em.THIRD_PARTY_WITHDRAWAL_SNAPSHOT_FROM_COINEX,
                reason_em.THIRD_PARTY_WITHDRAWAL_SNAPSHOT_UNRELATED,
                reason_em.THIRD_PARTY_WITHDRAWAL_SNAPSHOT_BLURRY,
                reason_em.THIRD_PARTY_WITHDRAWAL_SNAPSHOT_INCOMPLETE,
            ],
            "充值验证相关": [
                reason_em.DEPOSIT_TRANSFER_OUT_SCREENSHOT_MISMATCHED,
                reason_em.NEED_PROVIDE_CANNOT_FILL_MEMO_CERTIFICATE,
                reason_em.NEED_PROVIDE_SCREENSHOT_CONTAINING_TWO_WITHDRAWALS,
                reason_em.NOT_COMPLETED_DEPOSIT_SPECIFIED_AMOUNT_VERIFICATION,
                reason_em.DEPOSIT_VERIFICATION_INVALID_BEFORE_APPLY,
                reason_em.DEPOSIT_VERIFICATION_FROM_SAME_PLATFORM,
                reason_em.DEPOSIT_VERIFICATION_FROM_USER_COINEX_ACCOUNT,
                reason_em.DEPOSIT_VERIFICATION_FROM_USER_COINEX_WITHDRAWAL,
            ],
            "退回地址相关": [
                reason_em.REFUND_ADDRESS_FORMAT_MISMATCHED,
                reason_em.NEED_PROVIDE_NON_COINEX_RECEIVING_ADDRESS,
                reason_em.REFUND_ADDRESS_SCREENSHOT_MISMATCHED,
                reason_em.NEED_PROVIDE_SAME_CHAIN_RECEIVING_ADDRESS,
                reason_em.NEED_PROVIDE_RECEIVING_PLATFORM_ADDRESS,
            ],
            "充值/提现退回热钱包及录屏相关": [
                reason_em.THIRD_PARTY_REFUND_SCREENSHOT_MISMATCHED,
                reason_em.THIRD_PARTY_CUSTOMER_COMMUNICATE_SCREENSHOT_MISMATCHED,
                reason_em.SCREEN_WITHOUT_FACE_BY_ANOTHER_DEVICE,
                reason_em.SCREEN_BLURRY,
                reason_em.SCREEN_MISSING_TIME,
                reason_em.SCREEN_MISSING_TX_ON_CHAIN_INFO,
                reason_em.SCREEN_MISSING_REFUND_PROOF,
                reason_em.SCREEN_MISSING_CUSTOMER_COMMUNICATE,
            ],
            "其他充值相关": [
                reason_em.ALREADY_RECORDED,
                reason_em.NEED_WAIT,
                reason_em.DEPOSIT_MADE_BEFORE_ADDRESS,
                reason_em.RETRIEVE_ADDRESS_NOT_GENERATE,
                reason_em.TOTAL_USD_UNKNOWN,
                reason_em.TOTAL_USD_TOO_SMALL,
                reason_em.DEPOSIT_TO_INCORRECT_ADDRESS,
            ],
            "其他": [
                reason_em.IMAGE_HAS_BEEN_EDITED,
                reason_em.ADDITIONAL_INFO_NOT_PROVIDED_FOR_A_LONG_TIME,
                reason_em.ADDITIONAL_INFO_UNQUALIFIED,
                reason_em.NEED_PROVIDE_PROOF,
            ],
        }
        result = []
        for ct, rs in category_reasons_map.items():
            item = {
                "category": ct,
                "reasons": [dict(name=i.name, value=i.value) for i in rs],
            }
            result.append(item)
        return result

    @classmethod
    def get(cls, application_id):
        """ 运营>充值找回>审核详情 """

        row = AbnormalDepositApplicationsResource.get_row(application_id)
        detail = row.to_dict(enum_to_name=True)
        user_ids = {row.user_id}
        if row.auditor_id:
            user_ids.add(row.auditor_id)
        if row.checker_id:
            user_ids.add(row.checker_id)
        if row.double_checker_id:
            user_ids.add(row.double_checker_id)
        users = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email).all()
        user_email_map = dict(users)
        user_name_map = get_admin_user_name_map(user_ids)
        detail["show_fee_asset"] = row.fee_asset or row.expect_fee_asset
        detail["show_fee_amount"] = row.fee_amount or row.expect_fee_amount
        detail["asset_list"] = list_all_assets()
        detail["email"] = user_email_map.get(row.user_id, "")
        detail["user_remark"] = get_user_remark(User.query.get(row.user_id))
        detail["auditor_email"] = user_email_map.get(row.auditor_id, "")
        detail["auditor_name"] = user_name_map.get(row.auditor_id, "")
        detail["checker_email"] = user_email_map.get(row.checker_id, "")
        detail["checker_name"] = user_name_map.get(row.checker_id, "")
        detail["double_checker_email"] = user_email_map.get(row.double_checker_id, "")
        detail["double_checker_name"] = user_name_map.get(row.double_checker_id, "")
        detail["admin_status"] = AbnormalDepositApplicationsResource.get_admin_status(row)
        detail["is_need_double_check"] = AbnormalDepositBusiness.is_need_double_check(row)
        detail["is_need_fee"] = row.is_need_fee
        is_take_fee_map = AbnormalDepositBusiness.batch_get_is_take_fee([row])
        detail["is_take_fee"] = is_take_fee_map.get(row.id, False)
        client = WalletClient()
        detail["tx_url"] = client.get_explorer_tx_url(row.chain, row.tx_id)
        detail["refund_tx_url"] = client.get_explorer_tx_url(row.chain, row.refund_tx_id) if row.refund_tx_id else ""
        detail["address_url"] = client.get_explorer_address_url(row.chain, row.address) if row.address else ""
        detail["refund_address_url"] = (
            client.get_explorer_address_url(row.chain, row.refund_address) if row.refund_address else ""
        )

        image_keys = [
            "transfer_out_screenshot",
            "send_address_screenshot",
            "refund_address_screenshot",
        ]
        additional_info = json.loads(row.additional_info) if row.additional_info else {}
        for k in image_keys:
            nk = f"{k}_url"
            detail[nk] = AWSBucketPrivate.get_file_url(s3_key) if (s3_key := additional_info.get(k)) else ""

        transfer_out_screenshots = additional_info.get("transfer_out_screenshots", [])
        if (t1 := additional_info.get('transfer_out_screenshot')) and t1 not in transfer_out_screenshots:
            transfer_out_screenshots.append(t1)
        transfer_out_screenshot_urls = []
        for k in transfer_out_screenshots:
            if k:
                transfer_out_screenshot_urls.append(AWSBucketPrivate.get_file_url(k))
        detail["transfer_out_screenshot_urls"] = transfer_out_screenshot_urls

        send_address_screenshots = additional_info.get("send_address_screenshots", [])
        if (s1 := additional_info.get('send_address_screenshot')) and s1 not in send_address_screenshots:
            send_address_screenshots.append(s1)
        send_address_screenshot_urls = []
        for k in send_address_screenshots:
            if k:
                send_address_screenshot_urls.append(AWSBucketPrivate.get_file_url(k))
        detail["send_address_screenshot_urls"] = send_address_screenshot_urls

        merge_screenshots = additional_info.get("merge_screenshots", [])
        merge_screenshot_urls = []
        for k in merge_screenshots:
            if k:
                merge_screenshot_urls.append(AWSBucketPrivate.get_file_url(k))
        detail["merge_screenshot_urls"] = merge_screenshot_urls

        refund_address_screenshots = additional_info.get("refund_address_screenshots", [])
        if (r1 := additional_info.get('refund_address_screenshot')) and r1 not in refund_address_screenshots:
            refund_address_screenshots.append(r1)
        refund_address_screenshot_urls = []
        for k in refund_address_screenshots:
            if k:
                refund_address_screenshot_urls.append(AWSBucketPrivate.get_file_url(k))
        detail["refund_address_screenshot_urls"] = refund_address_screenshot_urls

        append_screenshots = additional_info.get("append_screenshots", [])
        append_screenshot_urls = []
        for k in append_screenshots:
            if not k:
                continue
            tmp = []
            for v in k:
                if v:
                    tmp.append(AWSBucketPrivate.get_file_url(v))
            if tmp:
                append_screenshot_urls.append(tmp)
        detail["append_screenshot_urls"] = append_screenshot_urls

        detail["rejection_reason"] = row.rejection_reason_list if not row.is_custom_reason else []
        detail["rejection_reason_str"] = row.rejection_reason_str
        detail["history_rejection_reason"] = row.get_history_rejection_reason()
        detail["additional_info_at_info"] = row.get_additional_info_at_info()
        type_enum = AbnormalDepositApplication.Type
        reason_enum = AbnormalDepositApplication.RejectionReason

        detail["type_dict"] = {i.name: i.value for i in type_enum}
        detail["reason_dict"] = {i.name: i.value for i in reason_enum}
        detail["admin_status_dict"] = AbnormalDepositApplicationsResource.ADMIN_STATUS_DICT
        detail["category_reasons"] = cls.get_category_reasons()

        # 交易ID总申请次数
        tx_apply_count = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.tx_id == row.tx_id,
        ).with_entities(
            func.count(AbnormalDepositApplication.id)
        ).scalar() or 0
        detail['tx_apply_count'] = tx_apply_count
        # 当前账户交易ID申请次数
        user_tx_apply_count = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.tx_id == row.tx_id,
            AbnormalDepositApplication.user_id == row.user_id
        ).with_entities(
            func.count(AbnormalDepositApplication.id)
        ).scalar() or 0
        detail['user_tx_apply_count'] = user_tx_apply_count
        # 当前账户&交易ID 首次申请时间
        first_tx_id_row = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.user_id == row.user_id,
            AbnormalDepositApplication.tx_id == row.tx_id,
        ).first()
        detail['user_tx_first_apply_time'] = first_tx_id_row.created_at
        # 交易ID 首次申请时间
        first_tx_id_row = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.tx_id == row.tx_id,
        ).first()
        detail['tx_first_apply_time'] = first_tx_id_row.created_at

        detail['need_additional_info_at'] = AbnormalDepositApplicationsResource.get_need_additional_info_at(application_id)
        user = User.query.get(row.user_id)
        detail["user_info"] = dict(
            id=user.id,
            created_at=user.created_at,
            registration_ip=user.registration_ip,
            registration_location=user.registration_location,
            email=user.email,
            mobile=user.mobile,
            name=user.name,
            user_type=user.user_type,
            kyc_status=user.kyc_status,
        )

        return detail


@ns.route("/edit-type/<int:application_id>")
@respond_with_code
class AbnormalDepositApplicationEditTypeResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            type=EnumField(AbnormalDepositApplication.Type, required=True),
        )
    )
    def post(cls, application_id, **kwargs):
        """ 运营>充值找回>审核详情-设置类型 """
        from app.schedules.abnormal_deposit_application import notice_abnormal_deposit_application_additional_info

        row = AbnormalDepositApplicationsResource.get_row(application_id)

        if row.status not in [
            AbnormalDepositApplication.Status.CREATED,
            AbnormalDepositApplication.Status.AUDIT_REQUIRED,
        ]:
            raise InvalidArgument(message=f"状态不是 CREATED 或 AUDIT_REQUIRED")

        type_ = kwargs["type"]
        if type_ == AbnormalDepositApplication.Type.UNKNOWN:
            raise InvalidArgument(message=f"不允许修改类型为 {AbnormalDepositApplication.Type.UNKNOWN.value}")

        old_type = row.type
        row.type = type_
        is_send_notice = False
        if type_ in AbnormalDepositApplication.NEED_ADDITIONAL_INFO_TYPES:
            # 需要补充信息，状态不变。等用户提交补充资料后再 -> 待审核
            row.additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.REQUIRED
            row.status = AbnormalDepositApplication.Status.CREATED  # AUDIT_REQUIRED -> CREATED
            is_send_notice = True
        else:
            # 不属于需要补充信息，状态->待审核
            row.additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.NOT_REQUIRED
            row.status = AbnormalDepositApplication.Status.AUDIT_REQUIRED
        #
        if type_ in AbnormalDepositApplication.WALLET_ABNORMAL_DEPOSIT_TYPES:
            wallet_type = AbnormalDepositApplication.WalletType.ABNORMAL_DEPOSIT
        elif type_ in AbnormalDepositApplication.WALLET_DEPOSIT_RECOVERY_TYPES:
            wallet_type = AbnormalDepositApplication.WalletType.DEPOSIT_RECOVERY
        else:
            wallet_type = AbnormalDepositApplication.WalletType.UNKNOWN
        row.wallet_type = wallet_type

        if row.is_new and row.is_need_fee:
            AbnormalDepositBusiness.update_expect_fee_amount(row, admin_user_id=g.user.id)

        AbnormalDepositApplicationChangelog.add(
            application_id=application_id,
            user_id=row.user_id,
            admin_user_id=g.user.id,
            change_type=AbnormalDepositApplicationChangelog.ChangeType.TYPE.name,
            old_value=old_type.name,
            new_value=type_.name,
        )
        db.session.commit()

        if is_send_notice:
            notice_abnormal_deposit_application_additional_info(row)


@ns.route("/edit-expect-fee")
@respond_with_code
class AbnormalDepositApplyEditExpectFeeResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            expect_fee_amount=PositiveDecimalField(required=True, allow_zero=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>充值找回>审核详情-编辑手续费 """

        apply_id = kwargs['id']
        expect_fee_amount = kwargs["expect_fee_amount"]
        row = AbnormalDepositApplicationsResource.get_row(apply_id)
        if not row.is_new:
            raise InvalidArgument(message=f"只允许编辑新找回记录的手续费")
        if row.status not in [
            AbnormalDepositApplication.Status.CREATED,
            AbnormalDepositApplication.Status.AUDIT_REQUIRED,
            AbnormalDepositApplication.Status.CHECK_REQUIRED,
            AbnormalDepositApplication.Status.DOUBLE_CHECK_REQUIRED,
        ]:
            raise InvalidArgument(message=f"当前状态不允许修改手续费")
        if not row.is_need_fee:
            raise InvalidArgument(message=f"当前找回记录的类型不需要手续费")
        if expect_fee_amount > row.tx_amount:
            raise InvalidArgument(message=f"手续费数目大于找回数目")

        old_expect_fee_asset = row.expect_fee_asset
        old_expect_fee_amount = row.expect_fee_amount
        row.expect_fee_amount = expect_fee_amount
        row.expect_fee_asset = row.asset
        row.expect_fee_is_manual = True

        old_expect_fee_amount_str = amount_to_str(old_expect_fee_amount) if old_expect_fee_amount is not None else '-'
        expect_fee_amount_str = amount_to_str(expect_fee_amount) if expect_fee_amount is not None else '-'
        AbnormalDepositApplicationChangelog.add(
            application_id=apply_id,
            user_id=row.user_id,
            admin_user_id=g.user.id,
            change_type=AbnormalDepositApplicationChangelog.ChangeType.DETAIL.name,
            old_value=f"手续费{old_expect_fee_amount_str} {old_expect_fee_asset or '-'}",
            new_value=f"手续费{expect_fee_amount_str} {row.expect_fee_asset}",
        )
        db.session.commit()


@ns.route("/audit/<int:application_id>")
@respond_with_code
class AbnormalDepositApplicationAuditResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            audit_status=EnumField(["REJECTED", "PASSED"], required=True),  # 审核是否通过
            rejection_reason=fields.List(EnumField(AbnormalDepositApplication.RejectionReason)),
            is_custom_reason=fields.Boolean(missing=False),
            custom_reject_reason=fields.String,
        )
    )
    def post(cls, application_id, **kwargs):
        # 编辑备注、设置类型、初审、复审操作 分为多个权限
        """ 运营>充值找回>审核详情-初审 """
        from app.schedules.abnormal_deposit_application import notice_abnormal_deposit_application_rejected

        audit_status = kwargs["audit_status"]
        is_rejected = audit_status == "REJECTED"
        rejection_reason = kwargs.get("rejection_reason")
        is_custom_reason = bool(kwargs.get("is_custom_reason"))
        custom_reject_reason = kwargs.get("custom_reject_reason")
        if is_rejected:
            if is_custom_reason:
                if not custom_reject_reason:
                    raise InvalidArgument(message="缺少自定义的拒绝原因")
            else:
                if not rejection_reason:
                    raise InvalidArgument(message="请选择拒绝原因")

        now_ = now()
        admin_user_id = g.user.id
        row = AbnormalDepositApplicationsResource.get_row(application_id)
        old_status = row.status
        if not is_rejected:
            # 审核拒绝，不判断
            if row.type == AbnormalDepositApplication.Type.UNKNOWN:
                raise InvalidArgument(message="找回类型是待定，无法审核通过，请先设置找回类型")
            if (
                row.is_need_additional_info
                and row.additional_info_status != AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED
            ):
                raise InvalidArgument(message=f"还没提供补充资料，无法审核通过")
            if row.status != AbnormalDepositApplication.Status.AUDIT_REQUIRED:
                raise InvalidArgument(message=f"初审只能操作待初审待记录")
            if row.wallet_type == AbnormalDepositApplication.WalletType.UNKNOWN:
                raise InvalidArgument(message=f"wallet_type是UNKNOWN，无法审核")
            if not row.asset:
                raise InvalidArgument(message="asset为空，请修改币种")
            if not row.tx_amount:
                raise InvalidArgument(message="tx_amount为0")

        if is_rejected:
            row.status = new_status = AbnormalDepositApplication.Status.REJECTED
            cls.set_rejection_reason(row, is_custom_reason, custom_reject_reason, rejection_reason)
        else:
            AbnormalDepositBusiness.update_expect_fee_amount(row, admin_user_id=g.user.id)  # 审核通过时需要计算一次手续费
            row.status = new_status = AbnormalDepositApplication.Status.CHECK_REQUIRED

        row.auditor_id = admin_user_id
        row.audited_at = now_
        row.processed_at = now_
        AbnormalDepositApplicationChangelog.add(
            application_id=application_id,
            user_id=row.user_id,
            admin_user_id=g.user.id,
            change_type=AbnormalDepositApplicationChangelog.ChangeType.STATUS.name,
            old_value=old_status.name,
            new_value=new_status.name,
        )
        db.session.commit()
        AbnormalDepositApplicationAssignAuditorsCache().finish(row.id)

        if is_rejected:
            notice_abnormal_deposit_application_rejected(row)

    @classmethod
    def set_rejection_reason(cls, row, is_custom_reason, custom_reject_reason, rejection_reason):
        history_rejection_reason = row.get_history_rejection_reason()
        if is_custom_reason:
            rejection_reason = custom_reject_reason
            history_rejection_reason.append(rejection_reason)
        else:
            rejection_reason_value = ';'.join([i.value for i in rejection_reason])
            rejection_reason = ','.join([i.name for i in rejection_reason])
            history_rejection_reason.append(rejection_reason_value)
        row.is_custom_reason = is_custom_reason
        row.rejection_reason = rejection_reason
        row.history_rejection_reason = json.dumps(history_rejection_reason) if history_rejection_reason else ''


@ns.route("/check/<int:application_id>")
@respond_with_code
class AbnormalDepositApplicationCheckResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            audit_status=EnumField(["REJECTED", "PASSED"], required=True),  # 审核是否通过
            rejection_reason=fields.List(EnumField(AbnormalDepositApplication.RejectionReason)),
            is_custom_reason=fields.Boolean(missing=False),
            custom_reject_reason=fields.String,
        )
    )
    def post(cls, application_id, **kwargs):
        """" 运营>充值找回>审核详情-复审（小于1W刀） """
        row = AbnormalDepositApplicationsResource.get_row(application_id)
        if row.total_usd and row.total_usd > Decimal("10000"):
            raise InvalidArgument(message="找回资产超过1W刀，无法复审，需要终审")
        return cls.do_check(application_id, **kwargs)

    @classmethod
    def do_check(cls, application_id, **kwargs):
        from app.schedules.abnormal_deposit_application import (
            process_audited_abnormal_deposit_application,
            notice_abnormal_deposit_application_rejected,
        )

        audit_status = kwargs["audit_status"]
        is_rejected = audit_status == "REJECTED"
        rejection_reason = kwargs.get("rejection_reason")
        is_custom_reason = bool(kwargs.get("is_custom_reason"))
        custom_reject_reason = kwargs.get("custom_reject_reason")
        if is_rejected:
            if is_custom_reason:
                if not custom_reject_reason:
                    raise InvalidArgument(message="缺少自定义的拒绝原因")
            else:
                if not rejection_reason:
                    raise InvalidArgument(message="请选择拒绝原因")

        now_ = now()
        admin_user_id = g.user.id

        with CacheLock(LockKeys.abnormal_deposit_application(application_id)):
            # 扣款加锁
            db.session.rollback()

            row = AbnormalDepositApplicationsResource.get_row(application_id)
            old_status = row.status
            if not is_rejected:
                if row.type == AbnormalDepositApplication.Type.UNKNOWN:
                    raise InvalidArgument(message="找回类型是待定，无法审核通过，请先设置找回类型")
                if (
                    row.is_need_additional_info
                    and row.additional_info_status != AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED
                ):
                    raise InvalidArgument(message=f"还没提供补充资料，无法审核通过")
                if not (
                    row.status == AbnormalDepositApplication.Status.CHECK_REQUIRED
                    or (not AbnormalDepositBusiness.is_need_double_check(row)
                        and row.status == AbnormalDepositApplication.Status.FEE_ASSET_CHANGE_DEDUCTED)
                ):
                    raise InvalidArgument(message=f"复审只能操作待复审记录")
            else:
                if old_status == AbnormalDepositApplication.Status.REJECTED:
                    raise InvalidArgument(message="已经审核不通过")

            if admin_user_id == row.auditor_id:
                raise InvalidArgument(message="初审、复核的操作人不能为同一个人")
            if row.wallet_type == AbnormalDepositApplication.WalletType.UNKNOWN:
                raise InvalidArgument(message=f"wallet_type是UNKNOWN，无法审核")
            if not row.asset:
                raise InvalidArgument(message="asset为空，请修改币种")
            if not row.tx_amount:
                raise InvalidArgument(message="tx_amount为0")

            if row.is_need_fee and not is_rejected and not AbnormalDepositBusiness.is_need_double_check(row) and not row.is_new:
                history = cls.do_abnormal_deposit_application_fee_asset_changing(row)  # db session will commit
                if not row.fee_amount or not row.fee_asset or history.status != AbnormalDepositApplicationTransferHistory.Status.FINISHED:
                    raise InvalidArgument(message=f"历史找回记录{row.id}手续费扣除失败")

            if is_rejected:
                row.status = new_status = AbnormalDepositApplication.Status.REJECTED
                AbnormalDepositApplicationAuditResource.set_rejection_reason(
                    row, is_custom_reason, custom_reject_reason, rejection_reason)
            else:
                if AbnormalDepositBusiness.is_need_double_check(row):
                    row.status = new_status = AbnormalDepositApplication.Status.DOUBLE_CHECK_REQUIRED
                else:
                    row.status = new_status = AbnormalDepositApplication.Status.CHECKED

            row.checker_id = admin_user_id
            row.checked_at = now_
            row.processed_at = now_
            AbnormalDepositApplicationChangelog.add(
                application_id=application_id,
                user_id=row.user_id,
                admin_user_id=g.user.id,
                change_type=AbnormalDepositApplicationChangelog.ChangeType.STATUS.name,
                old_value=old_status.name,
                new_value=new_status.name,
            )
            db.session.commit()

        AbnormalDepositApplicationAssignAuditorsCache().finish(row.id)
        if is_rejected:
            notice_abnormal_deposit_application_rejected(row)
        elif new_status == AbnormalDepositApplication.Status.CHECKED:
            process_audited_abnormal_deposit_application.delay(application_id)
            send_abnormal_deposit_application_passed_email.delay(application_id)

    @classmethod
    def do_abnormal_deposit_application_fee_asset_changing(
            cls, row: AbnormalDepositApplication
    ) -> AbnormalDepositApplicationTransferHistory:
        # 手续费资产变更：扣减用户账户USDT，给CoinEx财务账户加USDT（要在cache_lock下调用）
        assert not row.is_new
        client = ServerClient()
        fee_amount = AbnormalDepositApplication.SERVICE_FEE_AMOUNT
        fee_asset = AbnormalDepositApplication.SERVICE_FEE_ASSET
        application_id = row.id

        user_id = row.user_id
        finance_user_id = config["FINANCE_USER_ID"]

        history = AbnormalDepositApplicationTransferHistory.query.filter(
            AbnormalDepositApplicationTransferHistory.application_id == application_id,
            AbnormalDepositApplicationTransferHistory.type == AbnormalDepositApplicationTransferHistory.Type.FEE,
        ).first()
        if not history:
            history = AbnormalDepositApplicationTransferHistory(
                application_id=application_id,
                type=AbnormalDepositApplicationTransferHistory.Type.FEE,
                from_user_id=user_id,
                to_user_id=finance_user_id,
                asset=fee_asset,
                amount=fee_amount,
            )
            db.session.add(history)
            db.session.commit()
        business_id = history.get_business_id()
        balance_remark = f"fee for abnormal deposit application {application_id} history_id:{history.id}"

        # 扣减用户资产
        if row.status in [AbnormalDepositApplication.Status.CHECK_REQUIRED, AbnormalDepositApplication.Status.DOUBLE_CHECK_REQUIRED]:
            deduct_result = client.asset_query_business(
                user_id=user_id,
                asset=fee_asset,
                business=BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
                business_id=business_id,
            )
            if deduct_result:
                # server已扣减资产 但是web没更新
                row.status = AbnormalDepositApplication.Status.FEE_ASSET_CHANGE_DEDUCTED
                history.status = AbnormalDepositApplicationTransferHistory.Status.DEDUCTED
                history.deducted_at = now()
                db.session.commit()
            else:
                # 尝试扣款
                user_balance_result = client.get_user_balances(user_id, fee_asset, account_id=SPOT_ACCOUNT_ID)
                available = Decimal(user_balance_result.get(fee_asset, {}).get("available", Decimal()))
                if available < fee_amount:
                    raise InvalidArgument(message=f"用户手续费不足（{fee_amount}{fee_asset}），无法扣减手续费")

                try:
                    client.add_user_balance(
                        user_id=user_id,
                        asset=fee_asset,
                        amount=amount_to_str(-fee_amount),
                        business=BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
                        business_id=business_id,
                        detail={"remark": balance_remark},
                        account_id=SPOT_ACCOUNT_ID,
                    )
                except client.BadResponse as _e:
                    raise InvalidArgument(message=f"row:{application_id} 扣减用户账户手续费失败：{_e}")
                else:
                    row.status = AbnormalDepositApplication.Status.FEE_ASSET_CHANGE_DEDUCTED
                    history.status = AbnormalDepositApplicationTransferHistory.Status.DEDUCTED
                    history.deducted_at = now()
                    db.session.commit()

        # 增加财务资产
        if row.status == AbnormalDepositApplication.Status.FEE_ASSET_CHANGE_DEDUCTED:
            add_result = client.asset_query_business(
                user_id=finance_user_id,
                asset=fee_asset,
                business=BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
                business_id=business_id,
            )
            if add_result:
                # 扣费流程已完成
                row.fee_asset = fee_asset
                row.fee_amount = fee_amount
                history.status = AbnormalDepositApplicationTransferHistory.Status.FINISHED
                history.finished_at = now()
                db.session.commit()
            else:
                try:
                    client.add_user_balance(
                        user_id=finance_user_id,
                        asset=fee_asset,
                        amount=amount_to_str(fee_amount, 8),
                        business=BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
                        business_id=business_id,
                        detail={"remark": balance_remark},
                        account_id=SPOT_ACCOUNT_ID,
                    )
                except client.BadResponse as _e:
                    raise InvalidArgument(message=f"row:{application_id} 扣减用户手续费资产成功，增加财务手续费资产失败：{_e}")
                else:
                    # 扣费流程已完成
                    row.fee_asset = fee_asset
                    row.fee_amount = fee_amount
                    history.status = AbnormalDepositApplicationTransferHistory.Status.FINISHED
                    history.finished_at = now()
                    db.session.commit()
        return history


@ns.route("/super-check/<int:application_id>")
@respond_with_code
class AbnormalDepositApplicationSuperCheckResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            audit_status=EnumField(["REJECTED", "PASSED"], required=True),  # 审核是否通过
            rejection_reason=fields.List(EnumField(AbnormalDepositApplication.RejectionReason)),
            is_custom_reason=fields.Boolean(missing=False),
            custom_reject_reason=fields.String,
        )
    )
    def post(cls, application_id, **kwargs):
        """" 运营>充值找回>审核详情-复审（大于1W刀） """
        # 小于1W刀 和 大于1W刀 的审核权限需要分开
        return AbnormalDepositApplicationCheckResource.do_check(application_id, **kwargs)


@ns.route("/double-check/<int:application_id>")
@respond_with_code
class AbnormalDepositApplicationDoubleCheckResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            audit_status=EnumField(["REJECTED", "PASSED"], required=True),  # 审核是否通过
            rejection_reason=fields.List(EnumField(AbnormalDepositApplication.RejectionReason)),
            is_custom_reason=fields.Boolean(missing=False),
            custom_reject_reason=fields.String,
        )
    )
    def post(cls, application_id, **kwargs):
        """" 运营>充值找回>审核详情-三审 """
        row = AbnormalDepositApplicationsResource.get_row(application_id)
        if not AbnormalDepositBusiness.is_need_double_check(row):
            raise InvalidArgument(message="该笔找回不需要三审")
        return cls.do_double_check(application_id, **kwargs)

    @classmethod
    def do_double_check(cls, application_id, **kwargs):
        from app.schedules.abnormal_deposit_application import (
            process_audited_abnormal_deposit_application,
            notice_abnormal_deposit_application_rejected,
        )

        audit_status = kwargs["audit_status"]
        is_rejected = audit_status == "REJECTED"
        rejection_reason = kwargs.get("rejection_reason")
        is_custom_reason = bool(kwargs.get("is_custom_reason"))
        custom_reject_reason = kwargs.get("custom_reject_reason")
        if is_rejected:
            if is_custom_reason:
                if not custom_reject_reason:
                    raise InvalidArgument(message="缺少自定义的拒绝原因")
            else:
                if not rejection_reason:
                    raise InvalidArgument(message="请选择拒绝原因")

        now_ = now()
        admin_user_id = g.user.id

        with CacheLock(LockKeys.abnormal_deposit_application(application_id)):
            # 扣款加锁
            db.session.rollback()

            row = AbnormalDepositApplicationsResource.get_row(application_id)
            old_status = row.status
            if not is_rejected:
                if row.type == AbnormalDepositApplication.Type.UNKNOWN:
                    raise InvalidArgument(message="找回类型是待定，无法审核通过，请先设置找回类型")
                if (
                    row.is_need_additional_info
                    and row.additional_info_status != AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED
                ):
                    raise InvalidArgument(message=f"还没提供补充资料，无法审核通过")
                if not (
                        row.status == AbnormalDepositApplication.Status.DOUBLE_CHECK_REQUIRED
                        or (AbnormalDepositBusiness.is_need_double_check(row)
                            and row.status == AbnormalDepositApplication.Status.FEE_ASSET_CHANGE_DEDUCTED)
                ):
                    raise InvalidArgument(message=f"三审只能操作待三审记录")
            else:
                if old_status == AbnormalDepositApplication.Status.REJECTED:
                    raise InvalidArgument(message="已经审核不通过")

            if admin_user_id in [row.auditor_id, row.checker_id]:
                raise InvalidArgument(message="初审、复审、三审的操作人不能为同一个人")
            if row.wallet_type == AbnormalDepositApplication.WalletType.UNKNOWN:
                raise InvalidArgument(message=f"wallet_type是UNKNOWN，无法审核")
            if not row.asset:
                raise InvalidArgument(message="asset为空，请修改币种")
            if not row.tx_amount:
                raise InvalidArgument(message="tx_amount为0")

            if row.is_need_fee and not is_rejected and not row.is_new:
                history = AbnormalDepositApplicationCheckResource.do_abnormal_deposit_application_fee_asset_changing(
                    row)  # db session will commit
                if not row.fee_amount or not row.fee_asset or history.status != AbnormalDepositApplicationTransferHistory.Status.FINISHED:
                    raise InvalidArgument(message="手续费扣除失败")

            if is_rejected:
                row.status = new_status = AbnormalDepositApplication.Status.REJECTED
                AbnormalDepositApplicationAuditResource.set_rejection_reason(
                    row, is_custom_reason, custom_reject_reason, rejection_reason)
            else:
                row.status = new_status = AbnormalDepositApplication.Status.CHECKED

            row.double_checker_id = admin_user_id
            row.double_checked_at = now_
            row.processed_at = now_
            AbnormalDepositApplicationChangelog.add(
                application_id=application_id,
                user_id=row.user_id,
                admin_user_id=g.user.id,
                change_type=AbnormalDepositApplicationChangelog.ChangeType.STATUS.name,
                old_value=old_status.name,
                new_value=new_status.name,
            )
            db.session.commit()

        AbnormalDepositApplicationAssignAuditorsCache().finish(row.id)
        if is_rejected:
            notice_abnormal_deposit_application_rejected(row)
        else:
            process_audited_abnormal_deposit_application.delay(application_id)
            send_abnormal_deposit_application_passed_email.delay(application_id)


@ns.route("/retry-asset-change/<int:application_id>")
@respond_with_code
class AbnormalDepositApplicationRetryAssetChangeResource(Resource):
    @classmethod
    @ns.use_kwargs(dict())
    def post(cls, application_id):
        """ 运营>充值找回>审核详情-资产变更重试 """
        from app.business.abnormal_deposit import AbnormalDepositBusiness
        from app.schedules.abnormal_deposit_application import do_abnormal_deposit_application_asset_changing

        row = AbnormalDepositApplicationsResource.get_row(application_id)
        status_class = AbnormalDepositApplication.Status
        if row.status not in [
            status_class.ASSET_CHANGE_PROCESSING,
            status_class.ASSET_CHANGE_DEDUCTED,
        ]:
            raise InvalidArgument(message=f"状态不是资产变更：{row.status.name}")

        try:
            if row.is_new:
                AbnormalDepositBusiness.do_asset_change_by_new_apply(row)
            else:
                do_abnormal_deposit_application_asset_changing(application_id)
        except Exception as _e:
            raise InvalidArgument(message=f"{_e!r}")


@ns.route("/batch-retry-asset-change")
@respond_with_code
class AbnormalDepositApplicationBatchRetryAssetChangeResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            application_ids=fields.List(fields.Integer, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>充值找回>批量资产变更重试 """
        from app.schedules.abnormal_deposit_application import do_abnormal_deposit_application_asset_changing
        application_ids = kwargs["application_ids"]
        apply_rows: List[AbnormalDepositApplication] = []
        for chunk_ids in batch_iter(application_ids, 2000):
            chunk_rows = AbnormalDepositApplication.query.filter(
                AbnormalDepositApplication.id.in_(chunk_ids),
            ).all()
            apply_rows.extend([i for i in chunk_rows])
        for r in apply_rows:
            if r.status not in [
                AbnormalDepositApplication.Status.ASSET_CHANGE_PROCESSING,
                AbnormalDepositApplication.Status.ASSET_CHANGE_DEDUCTED,
            ]:
                raise InvalidArgument(message=f"只有状态为资产变更失败的记录才支持重试 {r.id}")
        for r in apply_rows:
            if r.is_new:
                continue
            do_abnormal_deposit_application_asset_changing.delay(r.id)


@ns.route('/auditors')
@respond_with_code
class AbnormalDepositApplicationAssignAuditorsResource(Resource, AssignAuditorMixin):

    @classmethod
    @ns.use_kwargs(dict(
        auditor_id=fields.Integer(required=True),
        ids=fields.List(fields.Integer, required=True),
    ))
    def post(cls, **kwargs):
        """运营>充值找回>批量分配审核人"""
        auditor_id = kwargs["auditor_id"]
        if auditor_id not in cls.get_auditor_map():
            raise InvalidArgument(message='请选择正确的审核人')
        ids = set(kwargs["ids"])
        if not ids:
            return
        ids = [r.id for r in AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.id.in_(ids),
            AbnormalDepositApplicationsResource.get_need_audit_filter()
        ).with_entities(
            AbnormalDepositApplication.id
        ).all()]
        if not ids:
            return
        AbnormalDepositApplicationAssignAuditorsCache().assign(auditor_id, ids)


@ns.route("/change-log")
@respond_with_code
class AbnormalDepositApplicationChangeLogResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            start_time=TimestampField(is_ms=True),
            end_time=TimestampField(is_ms=True),
            user_id=fields.Integer,
            admin_user_id=fields.Integer,
            application_id=fields.Integer,
            change_type=EnumField(AbnormalDepositApplicationChangelog.ChangeType),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 运营>充值找回>变更记录-列表 """
        query = AbnormalDepositApplicationChangelog.query.filter().order_by(AbnormalDepositApplicationChangelog.id.desc())
        if user_id := kwargs.get("user_id"):
            query = query.filter(AbnormalDepositApplicationChangelog.user_id == user_id)
        if application_id := kwargs.get("application_id"):
            query = query.filter(AbnormalDepositApplicationChangelog.application_id == application_id)
        if start_time := kwargs.get("start_time"):
            query = query.filter(AbnormalDepositApplicationChangelog.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            query = query.filter(AbnormalDepositApplicationChangelog.created_at <= end_time)
        if change_type := kwargs.get("change_type"):
            query = query.filter(AbnormalDepositApplicationChangelog.change_type == change_type.name)
        if admin_user_id := kwargs.get('admin_user_id'):
            query = query.filter(AbnormalDepositApplicationChangelog.admin_user_id == admin_user_id)

        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows = pagination.items

        user_ids = {i.user_id for i in rows}
        user_ids.update({i.admin_user_id for i in rows if i.admin_user_id})
        name_map = get_admin_user_name_map(user_ids)
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        items = []
        for row in rows:
            d = row.to_dict(enum_to_name=True)
            d["email"] = user_email_map.get(row.user_id)
            d["admin_user_email"] = user_email_map.get(row.admin_user_id) if row.admin_user_id else ""
            d["admin_name"] = name_map.get(row.admin_user_id) or ""
            detail = json.loads(row.detail)
            if row.change_type == AbnormalDepositApplicationChangelog.ChangeType.TYPE.name:
                old = AbnormalDepositApplication.Type[detail["old"]].value
                new = AbnormalDepositApplication.Type[detail["new"]].value
            elif row.change_type == AbnormalDepositApplicationChangelog.ChangeType.STATUS.name:
                try:
                    old = AbnormalDepositApplication.Status[detail["old"]].value
                except:  # noqa
                    old = detail["old"]
                try:
                    new = AbnormalDepositApplication.Status[detail["new"]].value
                except:  # noqa
                    new = detail["new"]
            else:
                old = detail["old"]
                new = detail["new"]
            d["detail_str"] = f"{old} 变为 {new}"
            items.append(d)

        return dict(
            items=items,
            total=pagination.total,
            extra=dict(
                change_type_dict={i.name: i.value for i in AbnormalDepositApplicationChangelog.ChangeType},
            ),
        )


@ns.route("/auto-reject")
@respond_with_code
class AbnormalDepositApplicationAutoRejectResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            application_ids=fields.List(fields.Integer, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>充值找回>批量自动拒绝 """
        from app.schedules.abnormal_deposit_application import notice_abnormal_deposit_application_rejected

        def _can_reject(row_: AbnormalDepositApplication) -> bool:
            if row_.status in [
                AbnormalDepositApplication.Status.CREATED,
                AbnormalDepositApplication.Status.AUDIT_REQUIRED,
                AbnormalDepositApplication.Status.CHECK_REQUIRED,
                AbnormalDepositApplication.Status.DOUBLE_CHECK_REQUIRED,
            ]:
                return True
            return False

        def _filter_deposit(row_: Deposit) -> bool:
            if row_.type == Deposit.Type.ON_CHAIN and row_.status not in [
                Deposit.Status.CANCELLED,
            ]:
                return True
            return False

        def _amount_is_eq(_apply_row: AbnormalDepositApplication, _deposit_row: Deposit) -> bool:
            if _apply_row.amount == _deposit_row.amount or _apply_row.tx_amount == _deposit_row.amount:
                return True
            cfg_key = (_apply_row.asset, _apply_row.chain)
            if cfg_key in cfg_map:
                cfg = cfg_map.get(cfg_key)
            else:
                cfg = try_get_asset_chain_config(*cfg_key)
                cfg_map[cfg_key] = cfg
            if cfg and cfg.deflation_rate:
                _rate = Decimal(1) - cfg.deflation_rate
                _amount1 = _apply_row.amount * _rate
                _tx_amount1 = _apply_row.tx_amount * _rate
                return _amount1 == _deposit_row.amount or _tx_amount1 == _deposit_row.amount
            return False

        apply_rows: List[AbnormalDepositApplication] = []
        application_ids = kwargs["application_ids"]
        for chunk_ids in batch_iter(application_ids, 500):
            chunk_rows = AbnormalDepositApplication.query.filter(AbnormalDepositApplication.id.in_(chunk_ids)).all()
            apply_rows.extend([i for i in chunk_rows if _can_reject(i)])

        pk_apply_rows_map = defaultdict(list)
        for apply_row in apply_rows:
            pk = apply_row.tx_id
            pk_apply_rows_map[pk].append(apply_row)

        tx_ids = {i.tx_id for i in apply_rows if i.tx_id}
        deposit_rows: List[Deposit] = []
        for chunk_tx_ids in batch_iter(tx_ids, 100):
            chunk_rows = Deposit.query.filter(Deposit.tx_id.in_(chunk_tx_ids)).all()
            deposit_rows.extend([i for i in chunk_rows if _filter_deposit(i)])

        cfg_map = {}
        apply_reject_reason_map = {}
        for deposit_row in deposit_rows:
            pk = deposit_row.tx_id
            # 只要交易ID对应的交易有生成充值记录或者已入账，就把这笔订单拒绝掉
            for apply_row in pk_apply_rows_map[pk]:
                apply_row: AbnormalDepositApplication
                if (
                        apply_row.user_id == deposit_row.user_id and
                        apply_row.address.lower() == deposit_row.address.lower() and
                        _amount_is_eq(apply_row, deposit_row)
                ):
                    # tx_id、用户、地址、数目都相同，才拒绝
                    apply_reject_reason_map[apply_row.id] = AbnormalDepositApplication.RejectionReason.ALREADY_RECORDED

        apply_map = {i.id: i for i in apply_rows}
        for row_id, reason in apply_reject_reason_map.items():
            row = apply_map[row_id]
            row.status = AbnormalDepositApplication.Status.REJECTED
            row.is_custom_reason = False
            row.rejection_reason = reason.name
            history_rejection_reason = row.get_history_rejection_reason()
            history_rejection_reason.append(reason.value)
            row.history_rejection_reason = json.dumps(history_rejection_reason)
        db.session.commit()

        AbnormalDepositApplicationAssignAuditorsCache().batch_finish(set(apply_reject_reason_map.keys()))

        for row_id in apply_reject_reason_map:
            notice_abnormal_deposit_application_rejected(apply_map[row_id])
        return list(apply_reject_reason_map)


@ns.route("/send-notice")
@respond_with_code
class AbnormalDepositApplicationSendNoticeResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            application_id=fields.Integer(required=True),
            notice_type=EnumField(["NOTICE_FEE", "NOTICE_FILE"], default="NOTICE_FEE"),
            rejection_reason=fields.List(EnumField(AbnormalDepositApplication.RejectionReason)),
            is_custom_reason=fields.Boolean(missing=False),
            custom_reject_reason=fields.String,
        )
    )
    def post(cls, **kwargs):
        """ 运营>充值找回>提醒补充手续费 """
        from app.schedules.abnormal_deposit_application import notice_abnormal_deposit_application_additional_info

        notice_type = kwargs["notice_type"]
        application_id = kwargs["application_id"]
        if notice_type == "NOTICE_FEE":
            row: AbnormalDepositApplication = AbnormalDepositApplication.query.get(application_id)
            if row.status != AbnormalDepositApplication.Status.CHECK_REQUIRED:
                raise InvalidArgument(message="当前状态不是待复审，不能提醒补充手续费")
            if not row.is_need_fee:
                raise InvalidArgument(message="当前类型不需要手续费")
            cls.notice_abnormal_deposit_application_fee(row)
        elif notice_type == "NOTICE_FILE":
            rejection_reason = kwargs.get("rejection_reason")
            is_custom_reason = bool(kwargs.get("is_custom_reason"))
            custom_reject_reason = kwargs.get("custom_reject_reason")
            if is_custom_reason:
                if not custom_reject_reason:
                    raise InvalidArgument(message="缺少自定义的拒绝原因")
            else:
                if not rejection_reason:
                    raise InvalidArgument(message="请选择拒绝原因")
            row: AbnormalDepositApplication = AbnormalDepositApplication.query.get(application_id)
            if (row.status not in  [
                AbnormalDepositApplication.Status.AUDIT_REQUIRED,
                AbnormalDepositApplication.Status.CHECK_REQUIRED,
            ] or row.additional_info_status not in [
                AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED,
                AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED_CR,
            ]):
                raise InvalidArgument(message="当前状态不是补充资料审核中，不能提醒补充资料")
            additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE
            if row.status == AbnormalDepositApplication.Status.CHECK_REQUIRED:
                additional_info_status = AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE_CR
            old_status = row.status
            row.status = AbnormalDepositApplication.Status.CREATED
            row.additional_info_status = additional_info_status
            history_rejection_reason = row.get_history_rejection_reason()
            if is_custom_reason:
                rejection_reason = custom_reject_reason
                history_rejection_reason.append(rejection_reason)
            else:
                rejection_reason = ','.join([i.name for i in rejection_reason])
                rejection_reason_value = ';'.join([i.value for i in kwargs['rejection_reason']])
                history_rejection_reason.append(rejection_reason_value)
            row.rejection_reason = rejection_reason
            row.is_custom_reason = is_custom_reason
            row.history_rejection_reason = json.dumps(history_rejection_reason) if history_rejection_reason else ''
            admin_status = AbnormalDepositBusiness.get_admin_status(row)
            AbnormalDepositApplicationChangelog.add(
                application_id=application_id,
                user_id=row.user_id,
                admin_user_id=g.user.id,
                change_type=AbnormalDepositApplicationChangelog.ChangeType.STATUS.name,
                old_value=old_status.name,
                new_value=AbnormalDepositBusiness.ADMIN_STATUS_DICT[admin_status],
            )
            db.session.commit()
            notice_abnormal_deposit_application_additional_info(row)

    @classmethod
    def notice_abnormal_deposit_application_fee(cls, row: AbnormalDepositApplication):
        """ 发送提醒补充手续费通知 """
        pref = UserPreferences(row.user_id)
        message_popup_expired_at = now() + timedelta(days=7)
        msg = Message(
            user_id=row.user_id,
            title=MessageTitle.ABNORMAL_DEPOSIT_APPLICATION_FEE.name,
            content=MessageContent.ABNORMAL_DEPOSIT_APPLICATION_FEE.name,
            params=json.dumps(
                dict(
                    time=datetime_to_str(row.created_at, pref.timezone_offset),
                    amount=amount_to_str(row.tx_amount, PrecisionEnum.COIN_PLACES),
                    asset=row.asset,
                    fee_amount=amount_to_str(AbnormalDepositApplication.SERVICE_FEE_AMOUNT),
                    fee_asset=AbnormalDepositApplication.SERVICE_FEE_ASSET,
                )
            ),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.SPOT_ASSET_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            expired_at=message_popup_expired_at,
            channel=Message.Channel.DEPOSIT_WITHDRAWAL,
        )
        db.session_add_and_commit(msg)
        send_abnormal_deposit_application_fee_email(row.id)
