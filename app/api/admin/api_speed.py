from marshmallow import fields as mm_fields
from sqlalchemy import or_
from flask import g

from app.models import <PERSON>r, db, UserApiFrequencyLimitRecord, UserApiSpeedRecord, SubAccount
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from app.utils.net import validate_email
from ..common import Resource, Namespace, respond_with_code
from ..common.fields import EnumField, LimitField, PageField
from ...exceptions import InvalidArgument

ns = Namespace('API Speed')

url_prefix = '/api-speed'


@ns.route('/config')
@respond_with_code
class UserApiSpeedConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        search_keyword=mm_fields.String,
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """
        API 加速配置列表
        """
        page, limit = kwargs['page'], kwargs['limit']
        search_keyword = kwargs.get("search_keyword")
        if search_keyword and len(search_keyword) < 6 and not search_keyword.isdigit():
            return []
        u_q = User.query
        query = UserApiSpeedRecord.query.filter(
            UserApiSpeedRecord.status == UserApiSpeedRecord.Status.VALID)
        # 按用户筛选
        if search_keyword:
            if search_keyword.isdigit():
                u_q = u_q.filter(
                    or_(User.id == search_keyword,
                        User.mobile == search_keyword))
            elif validate_email(search_keyword):
                u_q = u_q.filter(User.email == search_keyword)
            else:
                u_q = u_q.filter(User.email.contains(search_keyword))
            users = u_q.with_entities(User.id).all()
            query_user_ids = [item.id for item in users]
            sub_mapper = {
                sid: mid for mid, sid in
                SubAccount.query.filter(
                    SubAccount.user_id.in_(query_user_ids)
                ).with_entities(
                    SubAccount.main_user_id,
                    SubAccount.user_id
                ).all()
            }
            user_ids = set(query_user_ids) | set(sub_mapper.keys())
            main_user_ids = set(query_user_ids) | set(sub_mapper.values())
            query = query.filter(
                UserApiSpeedRecord.user_id.in_(user_ids))
            items = query.all()
        # 执行分页
        else:
            pagination = query.paginate(page, limit, error_out=False)
            items = pagination.items
            user_ids = [item.user_id for item in items]
            sub_mapper = {
                sid: mid for mid, sid in SubAccount.query.filter(
                    SubAccount.user_id.in_(user_ids)
                ).with_entities(
                    SubAccount.main_user_id,
                    SubAccount.user_id
                ).all()}
            main_user_ids = set(sub_mapper.values()) | (set(user_ids) - set(sub_mapper.keys()))
        user_emails = User.query.filter(
            User.id.in_(main_user_ids)
        ).with_entities(
            User.id, User.email
        ).all()
        user_email_map = dict(user_emails)

        res = []
        for item in items:
            item = item.to_dict()
            main_user_id = sub_mapper.get(item["user_id"], item["user_id"])
            item['email'] = user_email_map.get(main_user_id)
            res.append(item)
        return dict(items=res,
                    total=query.count(),
                    api_groups=UserApiFrequencyLimitRecord.ApiGroups)

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(required=True, validate=lambda x: x >= 0),
        group=EnumField(UserApiFrequencyLimitRecord.ApiGroups),
        ms=mm_fields.Integer(required=True, validate=lambda x: x >= 0),
        remark=mm_fields.String(missing='')
    ))
    def post(cls, **kwargs):
        """
        创建 API 加速配置
        """
        group = kwargs['group']
        record = UserApiSpeedRecord.query.filter(
            UserApiSpeedRecord.user_id == kwargs['user_id'],
            UserApiSpeedRecord.group == group,
            UserApiSpeedRecord.status == UserApiSpeedRecord.Status.VALID
        ).first()
        
        if record:
            raise InvalidArgument(message="该用户已经有了该分组的API加速配置")
            
        record = UserApiSpeedRecord(
            user_id=kwargs['user_id'],
            group=group,
            status=UserApiSpeedRecord.Status.VALID
        )
        for k, v in kwargs.items():
            setattr(record, k, v)
        db.session_add_and_commit(record)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.APISpeed,
            detail=kwargs,
            target_user_id=record.user_id,
        )


@ns.route('/config/<int:id_>')
@respond_with_code
class EditUserApiSpeedConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ms=mm_fields.Integer(required=True, validate=lambda x: x >= 0),
        remark=mm_fields.String(missing='')
    ))
    def put(cls, id_, **kwargs):
        """
        修改 API 加速配置
        """
        record = UserApiSpeedRecord.query.get(id_)
        old_data = record.to_dict(enum_to_name=True)
        for k, v in kwargs.items():
            setattr(record, k, v)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.APISpeed,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
            target_user_id=record.user_id,
        )

    @classmethod
    def delete(cls, id_):
        """
        删除 API 加速配置
        """
        record = UserApiSpeedRecord.query.get(id_)
        record.status = UserApiSpeedRecord.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.APISpeed,
            detail=record.to_dict(enum_to_name=True),
            target_user_id=record.user_id,
        )
