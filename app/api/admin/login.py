# -*- coding: utf-8 -*-
import json

from flask import current_app, g
from webargs import fields as wa_fields

from ..common import Resource, Namespace, respond_with_code, get_request_host_url
from ..common.auth import WebAuthn, WebAuthnSource
from ..common.decorators import require_admin_email_code, require_admin_operation_token
from ..common.fields import <PERSON>ailField
from ...business import UserSettings, is_super_user, get_user_menus
from ...caches.admin import (
    AdminUserOperationTokenCache,
    AdminUserLoginTokenCache, AdminUserWebAuthnOperationTokenCache, AdminWebAuthnOperationType,
)
from ...caches.admin import AdminLoginFailureCache
from ...common import LOGIN_FAILURE_LIMIT, OPERATION_TOKEN_SIZE, \
    LOGIN_TOKEN_SIZE
from ..common.responses import json_string_success
from ...exceptions import (
    InvalidUsernameOrPassword, FrequencyExceeded,
    TwoFactorA<PERSON>entica<PERSON>Required,
    Two<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Login<PERSON>orbidden, InvalidArgument,
)
from ...models import User, AdminUser
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from ...utils import validate_email, new_hex_token


ns = Namespace('Login')


@ns.route('')
@respond_with_code
class UserLoginResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        email=EmailField(required=True),
        login_password=wa_fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """登录"""
        email = kwargs['email'].strip()
        if not validate_email(email):
            raise InvalidUsernameOrPassword

        failure_limit, failure_ttl = LOGIN_FAILURE_LIMIT
        failure_cache = AdminLoginFailureCache(email, ttl=failure_ttl)
        if failure_cache.count() >= failure_limit:
            raise FrequencyExceeded

        user = User.query.filter_by(email=email).first()
        if user is None:
            failure_cache.add_value()
            raise InvalidUsernameOrPassword

        if user.id not in current_app.config['SUPER_ADMIN_USER']:
            if not AdminUser.query.filter(
                AdminUser.user_id == user.id,
                AdminUser.status == AdminUser.Status.PASSED
            ).first():
                raise LoginForbidden

        if not user.check_login_password(kwargs['login_password']):
            failure_cache.add_value()
            raise InvalidUsernameOrPassword

        user_id = user.id
        if not UserSettings(user_id).login_enabled:
            raise TwoFactorAuthenticationRequired
        # admin强制webauthn二次验证
        need_binding_webauthn = True
        if user.admin_web_authn_list:
            need_binding_webauthn = False

        operation_token = new_hex_token(OPERATION_TOKEN_SIZE)
        _cache_cls = AdminUserOperationTokenCache
        _cache_cls(operation_token).set_user(user_id)
        if need_binding_webauthn:
            _binding_token = new_hex_token(OPERATION_TOKEN_SIZE)
            _cache_cls(_binding_token, AdminWebAuthnOperationType.BINDING_WEBAUTHN).set_user(user_id)
        else:
            _binding_token = ''

        AdminOperationLog.new_login(
            user_id=user.id,
            ns_obj=OPNamespaceObjectUser.LoginAdmin,
            detail=dict(email=email),
        )

        return dict(
            email=user.main_user_email,
            operation_token=operation_token,
            need_binding_webauthn=need_binding_webauthn,
            binding_token=_binding_token
        )


@ns.route('/2fa')
@respond_with_code
class Login2FAResource(Resource):

    @classmethod
    @require_admin_operation_token(delete_cache=True)
    @ns.use_kwargs(
        dict(
            webauthn_token=wa_fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """登录-2FA"""
        user = g.operation_user
        webauthn_token = kwargs["webauthn_token"]
        cache = AdminUserWebAuthnOperationTokenCache(webauthn_token)
        user_id = cache.get_user()
        if user_id != user.id:
            failure_limit, failure_ttl = LOGIN_FAILURE_LIMIT
            failure_cache = AdminLoginFailureCache(user.main_user_email,
                                                   ttl=failure_ttl)
            if failure_cache.count() >= failure_limit:
                raise FrequencyExceeded
            raise TwoFactorAuthenticationFailed

        token = new_hex_token(LOGIN_TOKEN_SIZE)
        AdminUserLoginTokenCache(user_id).add_token(token)
        cache.delete()
        return dict(
            token=token,
            admin_user_menus=get_user_menus(user_id),
            is_super_admin_user=is_super_user(user_id),
            username=user.name_displayed
        )


@ns.route('/logout')
@respond_with_code
class LogoutResource(Resource):

    @classmethod
    def post(cls):
        """登出"""
        AdminUserLoginTokenCache(g.user.id).del_token(g.admin_token)


@ns.route('/webauthn/registration')
@respond_with_code
class WebAuthnRegistration(Resource):

    @classmethod
    @require_admin_operation_token
    @require_admin_email_code
    def get(cls):
        user: User = g.operation_user
        # 登录逻辑只允许绑定一个
        if user.admin_web_authn_list:
            raise InvalidArgument
        op = WebAuthn.get_registration_option(user,
                                              get_request_host_url(),
                                              WebAuthnSource.ADMIN
                                              )
        options_json = json.loads(op)

        # Add in credProps extension
        options_json["extensions"] = {
            "credProps": True,
        }
        return json_string_success(json.dumps(options_json))

    @classmethod
    @require_admin_operation_token(delete_cache=True)
    @require_admin_email_code(delete_cache=True)
    @ns.use_kwargs(dict(
        credential=wa_fields.Dict(required=True)
    ))
    def post(cls, **kwargs):
        user: User = g.operation_user
        # 登录逻辑只允许绑定一个
        if user.admin_web_authn_list:
            raise InvalidArgument
        credential = kwargs['credential']
        auth = WebAuthn.verify_registration(user, credential,
                                            WebAuthnSource.ADMIN)
        user.set_admin_webauthn(auth)


@ns.route('/webauthn/authentication')
@respond_with_code
class WebAuthnAuthentication(Resource):

    @classmethod
    @require_admin_operation_token
    def get(cls):
        """通行密钥鉴权"""
        user = g.operation_user
        if user is None:
            raise TwoFactorAuthenticationFailed
        if not user.admin_web_authn_list:
            raise InvalidArgument
        op = WebAuthn.get_authentication_option(user,
                                                get_request_host_url(),
                                                WebAuthnSource.ADMIN)
        return json_string_success(op)

    @classmethod
    @require_admin_operation_token
    @ns.use_kwargs(dict(
        credential=wa_fields.Dict(required=True)
    ))
    def post(cls, **kwargs):
        user = g.operation_user
        if not user.admin_web_authn_list:
            raise InvalidArgument
        credential = kwargs['credential']
        token = WebAuthn.verify_admin_authentication(user,
                                                     credential)
        return {'operation_token': token}
