# -*- coding: utf-8 -*-

from typing import Optional, Literal

import pyroscope
import threading

import gevent
from flask import g, request, current_app
from werkzeug.routing import Map, MapAdapter, Rule, RequestRedirect
from werkzeug.exceptions import NotFound

from ...business import has_url_permission, is_super_user, get_url_permission, is_super_wallet_user, CacheLock, LockKeys
from ...common import Language
from ..common.request import get_admin_request_user
from ...exceptions import AdminRequire, ServiceTimeout
from ...models.authority import AdminPermission
from ...utils import classproperty, remove_prefix, current_timestamp


no_admin_resource_list = [
    "/admin/system/ping",
    # 登录相关接口
    "/admin/login/2fa",
    "/admin/login",
    "/admin/auth/email/code",
    "/admin/login/webauthn/authentication",
    "/admin/login/webauthn/registration",
    "/admin/webauthn/authentication",
    "/admin/webauthn/registration",
]

admin_white_resource_list = [
    ('/admin/webauthn', ['GET', 'PUT']),
    ('/admin/webauthn/pre-auth', ['GET']),
    ('/admin/users/common', ['GET']),
    ('/admin/login/logout', ['POST']),
    ('/admin/spot/info', ['GET']),
    ('/admin/perpetual/markets', ['GET']),
    ('/admin/report/income/info', ['GET']),
    ('/admin/operation/red-packet-info', ['GET']),
    ('/admin/system/asset-prices', ['GET']),
    ('/admin/spot/asset-rates', ['GET']),
    ('/admin/asset/asset-update/template', ['GET']),
    ('/admin/asset/salary-pay/template', ['GET']),
    ('/admin/asset/lock/template', ['GET']),
    ('/admin/activity/mining/template', ['GET']),
    ('/admin/operation/email-push/template', ['GET']),
    ('/admin/support/user-check/template', ['GET']),
    ('/admin/support/user-check/mask-id-transfer/template', ['GET']),
    ('/admin/statistic/biz-monitor/event/template/admin/spot/market-help-me',
     ['GET']),
    ('/admin/upload/image', ['POST']),
    ('/admin/users/search', ['GET']),
    ('/admin/wallet/permissions/mine', ['GET']),
    ('/admin/wallet/permissions', ['POST']),
    ('/admin/asset/withdrawals/explorer-txs-url', ['POST']),  # 获取浏览器交易id的url
    ('/admin/asset/withdrawals/explorer-addresses-url', ['POST']),  # 获取浏览器地址的url
    ('/admin/operation/title-subtitle/upload', ['POST']),
    ('/admin/operation/title-subtitle/template', ['GET']),
    ('/admin/ai-translation/sync', ['POST']),
    ('/admin/ai-translation/async', ['GET', 'POST']),
    ('/admin/ai-translation/task/<string:id>', ['GET']),
    ('/admin/ai-translation/tasks', ['GET', 'DELETE']),
    ('/admin/ai-translation/stream', ['POST']),
    ('/admin/ai-analytics/tasks/search', ['GET']),
    ('/admin/ai-analytics/costs', ['GET']),
    ('/admin/bus-amb/bus-ambassadors/template', ['GET']),
    ('/admin/system/lang/cn-names', ['GET']),
]


class BaseRouter:

    _url_map: MapAdapter = None
    _updated_at: int = 0

    @classproperty
    def _path_prefix(cls) -> str:
        raise NotImplementedError

    @classproperty
    def _app(cls) -> AdminPermission.App:
        raise NotImplementedError

    @classproperty
    def url_map(cls) -> MapAdapter:
        if cls._url_map is None:
            cls.maybe_update()
        return cls._url_map

    @classmethod
    def maybe_update(cls) -> bool:
        ts = current_timestamp(to_int=True)
        if cls._url_map is None or ts - cls._updated_at > 3600:
            cls._url_map = cls._build_url_map()
            cls._updated_at = ts
            return True
        return False

    @classmethod
    def _build_url_map(cls) -> MapAdapter:
        rows = AdminPermission.query.filter(
            AdminPermission.app == cls._app,
            AdminPermission.status == AdminPermission.Status.PASSED
        ).with_entities(
            AdminPermission.rule,
            AdminPermission.endpoint,
        ).distinct().all()
        url_rules = [Rule(rule, endpoint=endpoint) for rule, endpoint in rows]
        url_map = Map(url_rules)
        return url_map.bind('')

    @classmethod
    def match(cls, path: str) -> Optional[Rule]:
        if not path.startswith(cls._path_prefix):
            return None
        path = '/admin/' + remove_prefix(path, cls._path_prefix)
        for _ in range(2):
            try:
                rule, _ = cls.url_map.match(path, return_rule=True)
                return rule
            except RequestRedirect:
                raise NotFound  # url不完全匹配，处理重定向比较麻烦，直接404
            except NotFound:
                if cls.maybe_update():  # 404，可能有新增的rule，尝试刷新
                    continue
                raise


class WalletRouter(BaseRouter):

    @classproperty
    def _path_prefix(cls) -> str:
        return '/admin/wallet/'

    @classproperty
    def _app(cls) -> AdminPermission.App:
        return AdminPermission.App.WALLET


class InformationRouter(BaseRouter):

    @classproperty
    def _path_prefix(cls) -> str:
        return '/admin/information/'

    @classproperty
    def _app(cls) -> AdminPermission.App:
        return AdminPermission.App.INFORMATION

    @classmethod
    def match(cls, path: str) -> Optional[Rule]:
        if not path.startswith(cls._path_prefix):
            return None
        if path in {
            '/admin/information/push',
            '/admin/information/translate',
        }:
            return None
        for _ in range(2):
            try:
                rule, _ = cls.url_map.match(path, return_rule=True)
                return rule
            except RequestRedirect:
                raise NotFound  # url不完全匹配，处理重定向比较麻烦，直接404
            except NotFound:
                if cls.maybe_update():  # 404，可能有新增的rule，尝试刷新
                    continue
                raise


class CommentRouter(BaseRouter):

    @classproperty
    def _path_prefix(cls) -> str:
        return '/admin/comment/'

    @classproperty
    def _app(cls) -> AdminPermission.App:
        return AdminPermission.App.COMMENT

    @classmethod
    def match(cls, path: str) -> Optional[Rule]:
        if not path.startswith(cls._path_prefix):
            return None
        for _ in range(2):
            try:
                rule, _ = cls.url_map.match(path, return_rule=True)
                return rule
            except RequestRedirect:
                raise NotFound  # url不完全匹配，处理重定向比较麻烦，直接404
            except NotFound:
                if cls.maybe_update():  # 404，可能有新增的rule，尝试刷新
                    continue
                raise

def is_export_request() -> tuple[bool, Literal['', 'path', 'args']]:
    if request.method != 'GET':
        return False, ''
    if 'async' in request.path:
        return False, ''
    if 'download' in request.path or 'export' in request.path:
        return True, 'path'
    exp = request.args.get('export')
    if exp and exp != '0' and exp.lower() != 'false':
        return True, 'args'
    return False, ''

def set_export_timeout(reason: Literal['path', 'args']):
    func = current_app.view_functions[request.endpoint]
    # 避免重复装饰，会导致锁重入，请求失败
    if func.__name__ == 'view_function_with_timeout':
        return
    # 通过参数控制的导出行为，如果不是导出请求则不进行处理
    if reason == 'args':
        def view_function_with_timeout(*args, **kwargs):
            exp = request.args.get('export')
            if exp and exp != '0' and exp.lower() != 'false':
                with CacheLock(LockKeys.admin_export(), ttl=300, wait=False):
                    with gevent.Timeout(90, ServiceTimeout):
                        return func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
    else:
        def view_function_with_timeout(*args, **kwargs):
            with CacheLock(LockKeys.admin_export(), ttl=300, wait=False):
                with gevent.Timeout(90, ServiceTimeout):
                    return func(*args, **kwargs)
    current_app.view_functions[request.endpoint] = view_function_with_timeout

def before_request():
    g.lang = Language.ZH_HANS_CN.value
    if request.routing_exception is not None:
        raise request.routing_exception
    if request.url_rule.rule in no_admin_resource_list:
        return
    user = get_admin_request_user(allow_none=False)
    for _v in admin_white_resource_list:
        url, white_methods = _v
        if request.url_rule.rule == url and request.method in white_methods:
            return
        # 钱包的路由都是 /admin/wallet/<path:path>，所以需要用path检查
        if request.path == url and request.method in white_methods:
            return

    if rule := WalletRouter.match(request.path):
        app = AdminPermission.App.WALLET
    elif rule := InformationRouter.match(request.path):
        app = AdminPermission.App.INFORMATION
    elif rule := CommentRouter.match(request.path):
        app = AdminPermission.App.COMMENT
    else:
        rule = request.url_rule
        app = AdminPermission.App.WEB

    current_app.logger.warning(
        f"【admin_op】user:{user.id} visit:{app.name}-{request.method}-{request.path}"
    )
    pyroscope.lib.add_thread_tag(threading.get_ident(), "path".encode("UTF-8"), request.path.encode("UTF-8"))

    if is_super_user(user.id):
        return
    if app == AdminPermission.App.WALLET and is_super_wallet_user(user.id):
        return
    if not has_url_permission(user.id, rule.rule, request.method, app.name):
        if permission := get_url_permission(rule.rule, request.method, app.name):
            raise AdminRequire(dict(
                name=permission.display_name,
                rule=permission.rule,
                method=permission.method
            ))
        raise AdminRequire
    is_export, reason = is_export_request()
    if is_export:
        set_export_timeout(reason)
    
def after_request(resp):
    pyroscope.lib.remove_thread_tag(threading.get_ident(), "path".encode("UTF-8"), request.path.encode("UTF-8"))
    return resp
