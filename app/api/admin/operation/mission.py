from datetime import <PERSON><PERSON><PERSON>
from enum import Enum
from typing import Dict

from flask import g
from marshmallow import Schema, EXCLUDE
from sqlalchemy import func
from webargs import fields

from app import Language, config
from app.api.common import Resource, Namespace, respond_with_code
from app.api.common.fields import TimestampField, EnumField, PageField, LimitField
from app.business import get_admin_user_name_map
from app.business.equity_center.helper import EquityCenterService
from app.business.mission_center.mission import MissionBiz
from app.business.mission_center.plan import MissionPlanBiz
from app.caches.mission import MissionCache
from app.common import language_name_cn_names, get_country, list_country_codes_3_admin, ADMIN_EXPORT_LIMIT, BusinessParty
from app.exceptions import RecordNotFound, InvalidArgument
from app.models import db, User, LoginRelationHistory
from app.models.equity_center import EquityType, UserEquity
from app.models.mission_center import SceneType, LogicTemplate, MissionCondition, MissionPlan, MissionPlanUserGroup, \
    Mission, UserMission, DailyMissionStatistics
from app.utils import validate_email, export_xlsx, amount_to_str, datetime_to_utc8_str, now
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation

ns = Namespace('Mission')


class MissionSchema(Schema):
    id = fields.Integer(allow_none=True)
    equity_id = fields.Integer(allow_none=True)
    asset = fields.String(allow_none=True)
    amount = fields.Decimal(allow_none=True)
    sequence = fields.Integer(required=True)
    deadline_days = fields.Integer(required=True)
    reward_type = fields.String(required=True)
    mission_condition = EnumField(MissionCondition, required=True)
    logic_params = fields.Dict(keys=fields.String, values=fields.Raw, required=True)

    class Meta:
        UNKNOWN = EXCLUDE


class ChannelTypes(Enum):
    NORMAL = '普通注册'
    REFERER = '邀请注册'
    POOL = '矿池注册'


LOGIC_TEMPLATE_CHANNEL_MAPPER = {
    LogicTemplate.CHANNEL_ID_EQ: ChannelTypes.POOL,
    LogicTemplate.REFERER_ID_IN: ChannelTypes.REFERER,
    LogicTemplate.REGISTRATION_AREA_IN: ChannelTypes.NORMAL
}

CHANNEL_LOGIC_MAPPER = {
    channel: temp for temp, channel in LOGIC_TEMPLATE_CHANNEL_MAPPER.items()
}


class MissionResourceMixin:

    CREATED_UPDATED_PARAMS = dict(
        name=fields.String(required=True),
        channel=EnumField(ChannelTypes, required=True),
        scene_type=EnumField(SceneType, required=True),
        start_at=TimestampField(required=True, is_ms=True),
        end_at=TimestampField(is_ms=True, allow_none=True),
        total=fields.Integer(allow_none=True),
        deliver_content=fields.Dict(keys=EnumField(
            [i.name for i in Language]), values=fields.String),
        logic_params=fields.Dict(keys=EnumField(
            [i.name for i in LogicTemplate]), values=fields.Raw, required=True),
        missions=fields.Nested(MissionSchema, many=True, required=True),
        business_party=EnumField(BusinessParty, required=True)
    )

    @classmethod
    def update_params(cls, params: dict):
        channel = params['channel']
        logic_params = params['logic_params']
        now_ = now()
        if (start_at := params['start_at']) < now_:
            raise InvalidArgument(message="开始时间不能小于当前时间")
        if (end_at := params.get("end_at")) and end_at < start_at:
            raise InvalidArgument(message="结束时间不能小于开始时间")
        match channel:
            case ChannelTypes.REFERER:
                logic_value: str = logic_params.get(LogicTemplate.REFERER_ID_IN.name)
                if not logic_value:
                    raise InvalidArgument(message=f"{channel.value} params value error: {logic_value}")
                logic_list = logic_value.strip().replace("，", ",").split(",")
                id_set, email_set = set(), set()
                for id_or_email in logic_list:
                    id_or_email = id_or_email.strip()
                    if id_or_email.isdigit():
                        id_set.add(int(id_or_email))
                        continue
                    if not validate_email(id_or_email):
                        raise InvalidArgument(message=f"{channel.value} 参数错误, {id_or_email} 并不是有效的用户邮箱")
                    email_set.add(id_or_email)
                email_user_mapper = {
                    e: i
                    for i, e in User.query.filter(
                        User.email.in_(email_set)
                    ).with_entities(
                        User.id,
                        User.email
                    ).all()
                }
                user_ids = {i.id for i in User.query.filter(User.id.in_(id_set)).with_entities(User.id).all()}
                not_email = email_set - set(email_user_mapper.keys())
                not_user_ids = id_set - user_ids
                if not_email:
                    raise InvalidArgument(
                        message=f"{channel.value} 参数错误, 无效用户邮箱: {','.join(not_email)}"
                    )
                if not_user_ids:
                    raise InvalidArgument(
                        message=f"{channel.value} 参数错误, 无效用户ID{','.join((map(str, not_user_ids)))}"
                    )
                logic_params[LogicTemplate.REFERER_ID_IN.name] = logic_list
            case ChannelTypes.POOL:
                logic_params[LogicTemplate.CHANNEL_ID_EQ.name] = MissionPlanBiz.POOL_CHANNEL
            case ChannelTypes.NORMAL:
                if not logic_params:
                    logic_params[LogicTemplate.REGISTRATION_AREA_IN.name] = []

        params["logic_params"] = logic_params
        params["logic_template"] = CHANNEL_LOGIC_MAPPER[channel]
        return params

    @classmethod
    def get_all_plan_name_mapper(cls):
        return {
            i.id: f"{i.id} {i.scene_type.value} {i.name}" for i in MissionPlan.query.filter(
                MissionPlan.status != MissionPlan.Status.DELETED
            ).order_by(MissionPlan.id.desc()).with_entities(
                MissionPlan.id,
                MissionPlan.name,
                MissionPlan.scene_type
            ).all()
        }

    @classmethod
    def get_plan_name_mapper(cls, plan_ids: set[int]):
        return {
            i.id: f"{i.name}" for i in MissionPlan.query.filter(
                MissionPlan.id.in_(plan_ids)
            ).with_entities(
                MissionPlan.id,
                MissionPlan.name
            ).all()
        }

    @classmethod
    def get_reward_name_by_cache_data(cls, cache_data: dict):
        if not cache_data:
            return ""
        reward_data = cache_data['reward']
        match reward_type := reward_data['reward_type']:
            case EquityType.AIRDROP.name:
                return f"{EquityType[reward_type].value} ({reward_data['value_type']})"
            case EquityType.CASHBACK.name:
                return f"{EquityType[reward_type].value} (ID: {cache_data['equity_id']})"
            case _:
                return ""

    @classmethod
    def get_mission_name_mapper(cls):
        return {
            k: f'{k}  {MissionBiz.build_title(v)}' for k, v in
            MissionCache.get_all_cache_data().items()
        }


@ns.route('')
@respond_with_code
class MissionPlanListResource(Resource, MissionResourceMixin):

    @classmethod
    @ns.use_kwargs(dict(
        plan_id=fields.Integer(allow_none=True),
        status=EnumField(MissionPlan.Status),
        channel=EnumField(ChannelTypes),
        page=PageField(missing=-1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-任务中心-新手任务推送列表"""
        page, limit = kwargs['page'], kwargs['limit']
        query = MissionPlan.query.filter(
            MissionPlan.status != MissionPlan.Status.DELETED
        )
        if plan_id := kwargs.get('plan_id'):
            query = query.filter(MissionPlan.id == plan_id)
        if status := kwargs.get('status'):
            statuses = [status, MissionPlan.Status.PASSED] if status == MissionPlan.Status.EFFECTIVE else [status]
            query = query.filter(
                MissionPlan.status.in_(statuses)
            )
        if channel := kwargs.get('channel'):
            plan_ids = {
                mpg.plan_id for mpg in MissionPlanUserGroup.query.filter(
                    MissionPlanUserGroup.scene_type == SceneType.NEWBIE,
                    MissionPlanUserGroup.logic_template == CHANNEL_LOGIC_MAPPER[channel]
                ).with_entities(
                    MissionPlanUserGroup.plan_id
                ).all()
            }
            query = query.filter(
                MissionPlan.id.in_(plan_ids)
            )
        paginate = query.order_by(
            MissionPlan.id.desc()
        ).with_entities(
            MissionPlan.id,
            MissionPlan.name,
            MissionPlan.status,
            MissionPlan.scene_type,
            MissionPlan.total,
            MissionPlan.created_by,
            MissionPlan.business_party,
            MissionPlan.updated_at
        ).paginate(page, limit, error_out=False)
        plan_ids = [i.id for i in paginate.items]
        mission_count_mapper = {
            pid: count for pid, count in
            Mission.query.filter(
                Mission.plan_id.in_(plan_ids),
            ).group_by(
                Mission.plan_id
            ).with_entities(
                Mission.plan_id,
                func.count(Mission.id)
            )
        }
        success_current_count_mapper = {
            pid: count for pid, count in UserMission.query.filter(
                UserMission.plan_id.in_(plan_ids),
                UserMission.status != UserMission.Status.FAILED
            ).group_by(
                UserMission.plan_id
            ).with_entities(
                UserMission.plan_id,
                func.count(UserMission.user_id.distinct())
            ).all()
        }
        failed_current_count_mapper = {
            pid: count for pid, count in UserMission.query.filter(
                UserMission.plan_id.in_(plan_ids),
                UserMission.status == UserMission.Status.FAILED
            ).group_by(
                UserMission.plan_id
            ).with_entities(
                UserMission.plan_id,
                func.count(UserMission.user_id.distinct())
            ).all()
        }
        created_bys = [i.created_by for i in paginate.items]
        user_name_mapper = get_admin_user_name_map(created_bys)
        logic_template_mapper = {
            pid: template for pid, template in
            MissionPlanUserGroup.query.filter(
                MissionPlanUserGroup.plan_id.in_(plan_ids)
            ).with_entities(
                MissionPlanUserGroup.plan_id,
                MissionPlanUserGroup.logic_template
            ).all()
        }
        items = []
        for item in paginate.items:
            logic_template = logic_template_mapper.get(item.id)
            if not logic_template:
                continue
            success_current_count = success_current_count_mapper.get(item.id, 0)
            failed_current_count = failed_current_count_mapper.get(item.id, 0)
            current_count = success_current_count + failed_current_count
            items.append(dict(
                id=item.id,
                scene_type=item.scene_type.value,
                name=item.name,
                total=item.total if item.total else "无上限",
                current_count=current_count,
                success_count=success_current_count,
                failed_count=failed_current_count,
                mission_count=mission_count_mapper.get(item.id, 0),
                status=item.status.name if item.status != MissionPlan.Status.PASSED else MissionPlan.Status.EFFECTIVE.name,
                created_by=item.created_by,
                updated_at=item.updated_at,
                created_by_name=user_name_mapper.get(item.created_by),
                channel=LOGIC_TEMPLATE_CHANNEL_MAPPER[logic_template].value,
                business_party=item.business_party.value
            ))

        return dict(
            total=paginate.total,
            items=items,
            channels=ChannelTypes,
            statuses={s.name: s.value for s in MissionPlan.Status
                      if s not in [MissionPlan.Status.PASSED, MissionPlan.Status.DELETED]},
            plan_id_mapper=cls.get_all_plan_name_mapper(),
        )

    @classmethod
    @ns.use_kwargs(MissionResourceMixin.CREATED_UPDATED_PARAMS)
    def post(cls, **kwargs):
        """运营-任务中心-创建新手任务推送"""
        user_id = g.user.id
        params = cls.update_params(kwargs)
        plan_id = MissionPlanBiz.create(params, user_id)
        AdminOperationLog.new_add(
            user_id=user_id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            detail=kwargs,
        )
        return dict(id=plan_id)


@ns.route('/<int:id_>')
@respond_with_code
class MissionPlanDetailResource(Resource, MissionResourceMixin):

    @classmethod
    def get(cls, id_):
        """运营-任务中心-获取新手任务推送详情"""
        base_dict = dict(
            scene_types=SceneType,
            languages=language_name_cn_names(),
            conditions=MissionCondition,
            channels=ChannelTypes,
            countries={code: get_country(
                code).cn_name for code in list_country_codes_3_admin()},
            rewards=EquityCenterService.get_all_equity_dict(),
            business_parties=BusinessParty
        )
        if not id_:
            return base_dict
        g_usr_id = g.user.id
        mission_plain_dict = MissionPlanBiz.get_info(id_)
        can_review_users = config['CAN_REVIEW_MISSION_PLAN_USERS']
        mission_plain_dict['channel'] = LOGIC_TEMPLATE_CHANNEL_MAPPER[mission_plain_dict['logic_template']].name
        mission_plain_dict['is_self_record'] = g_usr_id == mission_plain_dict['created_by']
        mission_plain_dict['can_review'] = g_usr_id in can_review_users if can_review_users else True
        mission_plain_dict.update(base_dict)

        return mission_plain_dict

    @classmethod
    @ns.use_kwargs(MissionResourceMixin.CREATED_UPDATED_PARAMS)
    def put(cls, id_, **kwargs):
        """运营-任务中心-修改新手任务推送详情"""
        old_plan = MissionPlan.query.get(id_)
        if not old_plan:
            raise RecordNotFound
        old_data = old_plan.to_dict(enum_to_name=True)
        params = cls.update_params(kwargs)
        plan = MissionPlanBiz.update(id_, params, g.user.id)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            old_data=old_data,
            new_data=plan.to_dict(enum_to_name=True),
            special_data=dict(plan_id=id_),
        )
        return dict(id=id_)

    @classmethod
    @ns.use_kwargs(MissionResourceMixin.CREATED_UPDATED_PARAMS)
    def patch(cls, id_, **kwargs):
        """运营-任务中心-新手任务-提交审核"""
        params = cls.update_params(kwargs)
        admin_user_id = g.user.id
        if not id_:
            id_ = MissionPlanBiz.create(params, admin_user_id)
        else:
            MissionPlanBiz.update(id_, params, admin_user_id)
        MissionPlanBiz.submit(id_)
        AdminOperationLog.new_audit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            detail=dict(id=id_, name=kwargs['name']),
        )
        return dict(id=id_)

    @classmethod
    def delete(cls, id_):
        """运营-任务中心-新手任务-删除推送"""
        MissionPlanBiz.delete(id_)
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            detail=dict(id=id_),
        )


@ns.route('/<int:id_>/review')
@respond_with_code
class MissionPlanReviewResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(MissionPlan.Status, required=True)
    ))
    def post(cls, id_, **kwargs):
        """运营-任务中心-新手任务-审核通过/拒绝审核"""
        status = kwargs["status"]
        plan: MissionPlan = MissionPlan.query.get(id_)
        if not plan:
            raise RecordNotFound
        old_data = plan.to_dict(enum_to_name=True)
        if plan.status != MissionPlan.Status.PENDING:
            raise InvalidArgument
        if status not in [MissionPlan.Status.PASSED, MissionPlan.Status.REJECTED]:
            raise InvalidArgument
        if status == MissionPlan.Status.PASSED:
            if plan.start_at + timedelta(minutes=2) <= now():
                MissionPlanBiz.rejected(id_)
                raise InvalidArgument(
                    message="审核已超时，请修改推送时间重新提交", data=dict(id=id_))
            else:
                MissionPlanBiz.audit_pass(id_)
        elif status == MissionPlan.Status.REJECTED:
            MissionPlanBiz.rejected(id_)
        plan.auditor_by = g.user.id
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            old_data=old_data,
            new_data=plan.to_dict(enum_to_name=True),
            special_data=dict(plan_id=id_),
        )
        return dict(id=id_)


@ns.route('/<int:id_>/stop')
@respond_with_code
class MissionPlanStopResource(Resource):

    @classmethod
    def post(cls, id_, **kwargs):
        """运营-任务中心-新手任务-暂停推送"""
        MissionPlanBiz.stop(id_)
        AdminOperationLog.new_stop(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            detail=dict(id=id_),
        )


@ns.route("/<int:id_>/update-content")
@respond_with_code
class MissionPlanUpdateContentResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        deliver_content=fields.Dict(keys=EnumField(
            [i.name for i in Language]), values=fields.String, required=True)
    ))
    def put(cls, id_, **kwargs):
        """运营-任务中心-新手任务-更新推送内容"""
        old_plan = MissionPlan.query.get(id_)
        if not old_plan:
            raise RecordNotFound
        deliver_content = kwargs["deliver_content"]
        old_deliver_content = old_plan.deliver_content
        plan = MissionPlanBiz.update_deliver_content(id_, deliver_content)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            old_data=old_deliver_content,
            new_data=deliver_content,
            special_data=dict(plan_id=id_),
        )
        return dict(id=plan.id)


@ns.route('/user-mission')
@respond_with_code
class UserMissionInfoResource(Resource, MissionResourceMixin):
    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "推送ID"},
        {"field": "plan_name", Language.ZH_HANS_CN: "推送名称"},
        {"field": "mission_id", Language.ZH_HANS_CN: "任务ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "push_status", Language.ZH_HANS_CN: "推送状态"},
        {"field": "created_at", Language.ZH_HANS_CN: "推送时间"},
        {"field": "mission_condition", Language.ZH_HANS_CN: "任务类型"},
        {"field": "status", Language.ZH_HANS_CN: "任务进行状态"},
        {"field": "completed_at", Language.ZH_HANS_CN: "达标时间"},
        {"field": "reward_name", Language.ZH_HANS_CN: "任务奖励"},
        {"field": "reward_status", Language.ZH_HANS_CN: "奖励发放状态"},
        {"field": "reward_amount", Language.ZH_HANS_CN: "应发奖励"},
        {"field": "reward_real_amount", Language.ZH_HANS_CN: "实发奖励"},
        {"field": "reward_created_at", Language.ZH_HANS_CN: "发放时间"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
        {"field": "fail_reason", Language.ZH_HANS_CN: "失败原因"},
    )

    class MissionPushStatus(Enum):
        FAILED = '推送失败'
        SUCCEEDED = '推送成功'

    class MissionRewardStatus(Enum):
        NOT_REWARDED = "未发放"
        NORMAL_REWARDED = "正常发放"
        ABNORMAL_REWARDED = "异常发放"

    @classmethod
    def get_reward_status(cls, user_mission_status: UserMission.Status, equity_status: UserEquity.Status):
        if user_mission_status != UserMission.Status.FINISHED:
            return cls.MissionRewardStatus.NOT_REWARDED
        if equity_status in EquityCenterService.FAILED_STATUS:
            return cls.MissionRewardStatus.ABNORMAL_REWARDED
        return cls.MissionRewardStatus.NORMAL_REWARDED

    @classmethod
    def get_push_status(cls, user_mission_status: UserMission.Status):
        if user_mission_status == UserMission.Status.FAILED:
            return cls.MissionPushStatus.FAILED
        return cls.MissionPushStatus.SUCCEEDED

    @classmethod
    def get_row_waring_flag(cls, reward_info: dict):
        if not reward_info:
            return False
        if reward_info['status'] in EquityCenterService.FAILED_STATUS:
            return True
        return reward_info['cost_amount'] != reward_info['real_amount']

    @classmethod
    def get_fail_reason(cls, equity_status):
        if equity_status == EquityCenterService.UserEquityStatus.FAILED:
            return UserMission.FailReason.RISK_USER
        if equity_status == EquityCenterService.UserEquityStatus.CREATED:
            return UserMission.FailReason.SETTLING_ERROR
        return None

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        plan_id=fields.Integer,
        mission_id=fields.Integer,
        status=EnumField(UserMission.Status),
        push_status=EnumField(MissionPushStatus),
        reward_type=EnumField(EquityType),
        reward_status=EnumField(MissionRewardStatus),
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """运营-任务中心-新手任务明细列表"""
        page, limit = kwargs['page'], kwargs['limit']
        query = UserMission.query
        if user_id := kwargs.get('user_id'):
            query = query.filter(UserMission.user_id == user_id)
        if plan_id := kwargs.get('plan_id'):
            query = query.filter(UserMission.plan_id == plan_id)
        if mission_id := kwargs.get('mission_id'):
            query = query.filter(UserMission.mission_id == mission_id)
        if status := kwargs.get('status'):
            query = query.filter(UserMission.status == status)
        if push_status := kwargs.get('push_status'):
            if push_status == cls.MissionPushStatus.FAILED:
                query = query.filter(
                    UserMission.status == UserMission.Status.FAILED
                )
            else:
                query = query.filter(
                    UserMission.status != UserMission.Status.FAILED
                )
        if reward_type := kwargs.get('reward_type'):
            mission_ids = MissionCache.get_mission_by_reward_type(reward_type.name)
            query = query.filter(
                UserMission.mission_id.in_(mission_ids)
            )
        if reward_status := kwargs.get('reward_status'):
            match reward_status:
                case cls.MissionRewardStatus.NOT_REWARDED:
                    query = query.filter(
                        UserMission.status != UserMission.Status.FINISHED
                    )
                case cls.MissionRewardStatus.NORMAL_REWARDED:
                    biz_ids = EquityCenterService.query_biz_id_by_biz_type_status(
                        EquityCenterService.BizTypes.MISSION,
                        EquityCenterService.UserEquityStatus.FAILED
                    )
                    query = query.filter(
                        UserMission.id.notin_(biz_ids),
                        UserMission.status == UserMission.Status.FINISHED
                    )
                case cls.MissionRewardStatus.ABNORMAL_REWARDED:
                    failed_biz_ids = EquityCenterService.query_biz_id_by_biz_type_status(
                        EquityCenterService.BizTypes.MISSION,
                        EquityCenterService.UserEquityStatus.FAILED
                    )
                    created_biz_ids = EquityCenterService.query_biz_id_by_biz_type_status(
                        EquityCenterService.BizTypes.MISSION,
                        EquityCenterService.UserEquityStatus.CREATED
                    )
                    query = query.filter(
                        UserMission.id.in_(failed_biz_ids | created_biz_ids)
                    )

        query = query.order_by(
            UserMission.plan_id.desc(), UserMission.id.desc()
        )
        if kwargs.get('export'):
            paginate = query.paginate(page, ADMIN_EXPORT_LIMIT, error_out=False)
        else:
            paginate = query.paginate(page, limit, error_out=False)
        biz_ids = {i.id for i in paginate.items}
        plan_ids = {i.plan_id for i in paginate.items}
        mission_ids = {i.mission_id for i in paginate.items}
        mission_info_mapper = MissionCache.get_cache_data_by_ids(list(mission_ids))
        reward_info_mapper = EquityCenterService.batch_query_user_eq_info(EquityCenterService.BizTypes.MISSION, biz_ids)
        plan_name_mapper = cls.get_plan_name_mapper(plan_ids)
        items = []
        for item in paginate.items:
            reward_info = reward_info_mapper.get(item.id, {})
            waring_flag = cls.get_row_waring_flag(reward_info)
            origin_reward_status = reward_info.get("status")
            new_status = cls.get_reward_status(item.status, origin_reward_status)
            reward_info['origin_status'] = origin_reward_status.name if origin_reward_status else None
            reward_info['status'] = new_status
            mission_info = mission_info_mapper.get(item.mission_id, {})
            reward_info.update(**mission_info.get("reward", {}))
            items.append(dict(
                id=item.id,
                plan_name=plan_name_mapper.get(item.plan_id),
                user_id=item.user_id,
                plan_id=item.plan_id,
                equity_id=mission_info['equity_id'],
                mission_id=item.mission_id,
                status=item.status.value,
                push_status=cls.get_push_status(item.status).value,
                reward_name=cls.get_reward_name_by_cache_data(mission_info),
                reward=reward_info,
                created_at=item.used_at if item.used_at != UserMission.MIN_UTC_DATETIME else item.created_at,
                updated_at=item.last_updated_at or item.updated_at,
                completed_at=item.completed_at,
                mission_condition=item.mission_condition.value,
                fail_reason=cls.get_fail_reason(origin_reward_status),
                waring_flag=waring_flag,
            ))
        if kwargs.get('export'):
            export_items = []
            for item in items:
                reward = item['reward']
                reward_created_at = reward.get('created_at')
                export_items.append({
                    "id": item['plan_id'],
                    "user_id": item["user_id"],
                    "plan_name": item["plan_name"],
                    "mission_id": item["mission_id"],
                    "status": "--" if item["status"] == '已失败' else item["status"],
                    "push_status": item["push_status"],
                    "reward_name": item["reward_name"],
                    "reward_status": reward.get("status").value,
                    "reward_amount": f'{amount_to_str(reward.get("cost_amount", 0))} {reward["value_type"]}',
                    "reward_real_amount": f'{amount_to_str(reward.get("real_amount", 0))} {reward["value_type"]}',
                    "reward_created_at": datetime_to_utc8_str(reward_created_at) if reward_created_at else None,
                    "updated_at": datetime_to_utc8_str(item["updated_at"]),
                    "completed_at": datetime_to_utc8_str(item["completed_at"]) if item["completed_at"] else "",
                    "mission_condition": item["mission_condition"],
                    "created_at": datetime_to_utc8_str(item['created_at']),
                    "fail_reason": item["fail_reason"].value if item["fail_reason"] else ""
                })
            return export_xlsx(
                filename='user_mission_list',
                data_list=export_items,
                export_headers=cls.export_headers
            )
        return dict(
            total=paginate.total,
            items=items,
            extra_data=dict(
                statuses={u.name: u.value for u in UserMission.Status if u != UserMission.Status.FAILED},
                reward_types=EquityType,
                reward_statuses=cls.MissionRewardStatus,
                push_status=cls.MissionPushStatus,
                plan_id_mapper=cls.get_all_plan_name_mapper(),
                mission_id_mapper=cls.get_mission_name_mapper()
            )
        )


@ns.route('/failed-users')
@respond_with_code
class MissionFailedUsersResource(Resource):
    
    @classmethod
    def get_user_email_dict(cls, user_ids: set[int]) -> Dict[int, str]:
        user_query = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(
            User.id,
            User.email
        ).all()
        return {i.id: i.email for i in user_query}
    
    @classmethod
    @ns.use_kwargs(dict(
        plan_id=fields.Integer(required=True),
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        user_id=fields.Integer,
    ))
    def get(cls, **kwargs):
        """运营-任务中心-失败用户列表"""
        plan_id = kwargs['plan_id']
        plan = MissionPlan.query.get(plan_id)
        if not plan:
            raise RecordNotFound
        if plan.scene_type != SceneType.NEWBIE:
            raise InvalidArgument(message="推送类型错误")
        query = UserMission.query.filter(
            UserMission.plan_id == plan_id,
            UserMission.status == UserMission.Status.FAILED
        )
        if user_id := kwargs.get('user_id'):
            query = query.filter(UserMission.user_id == user_id)

        query = query.group_by(
            UserMission.user_id
        ).with_entities(
            UserMission.user_id,
            func.min(UserMission.created_at).label('created_at'),
        )
        paginate = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        failed_users = paginate.items
        user_ids = {i.user_id for i in failed_users}
        user_device_mapper = LoginRelationHistory.query_duplicate_device_users(user_ids, is_get_device_mapper=True)
        user_email_mapper = cls.get_user_email_dict(user_ids)
        return dict(
            total=paginate.total,
            items=[
                dict(
                    user_id=user_id,
                    email=user_email_mapper.get(user_id),
                    created_at=created_at,
                    plan_id=plan_id,
                    title=plan.name,
                    fail_reason="设备相同",  # 现阶段 任务 失败只有这一个原因。
                    device_id=user_device_mapper.get(user_id)
                ) for user_id, created_at in failed_users
            ]
        )


@ns.route('/statistics')
@respond_with_code
class MissionStatisticsResource(Resource, MissionResourceMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "统计日期"},
        {"field": "plan_id", Language.ZH_HANS_CN: "推送ID"},
        {"field": "plan_name", Language.ZH_HANS_CN: "推送名称"},
        {"field": "mission_id", Language.ZH_HANS_CN: "任务ID"},
        {"field": "mission_condition", Language.ZH_HANS_CN: "任务类型"},
        {"field": "delivery_count", Language.ZH_HANS_CN: "推送成功用户数"},
        {"field": "completion_count", Language.ZH_HANS_CN: "达标用户数"},
        {"field": "finished_count", Language.ZH_HANS_CN: "已发奖用户数"},
        {"field": "reward_type", Language.ZH_HANS_CN: "任务奖励"},
        {"field": "reward_amount", Language.ZH_HANS_CN: "应发奖励"},
        {"field": "real_reward_amount", Language.ZH_HANS_CN: "实发奖励"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        date=fields.Date,
        plan_id=fields.Integer,
        mission_id=fields.Integer,
        reward_type=EnumField(EquityType),
        limit=LimitField(missing=50),
        page=PageField(missing=1),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """运营-任务中心-新手任务统计列表"""
        page, limit = kwargs['page'], kwargs['limit']
        query = DailyMissionStatistics.query
        if report_date := kwargs.get('date'):
            query = query.filter(DailyMissionStatistics.report_date == report_date)
        if plan_id := kwargs.get('plan_id'):
            query = query.filter(DailyMissionStatistics.plan_id == plan_id)
        if mission_id := kwargs.get('mission_id'):
            query = query.filter(DailyMissionStatistics.mission_id == mission_id)
        if reward_type := kwargs.get('reward_type'):
            mission_ids = MissionCache.get_mission_by_reward_type(reward_type.name)
            query = query.filter(
                DailyMissionStatistics.mission_id.in_(mission_ids)
            )
        query = query.order_by(
            DailyMissionStatistics.report_date.desc(),
            DailyMissionStatistics.plan_id.desc(),
            DailyMissionStatistics.mission_id.desc()
        )
        if kwargs.get('export'):
            paginate = query.paginate(page, ADMIN_EXPORT_LIMIT, error_out=False)
        else:
            paginate = query.paginate(page, limit, error_out=False)
        items = []
        mission_ids = {i.mission_id for i in paginate.items}
        plan_ids = {i.plan_id for i in paginate.items}
        mission_info_mapper = MissionCache.get_cache_data_by_ids(list(mission_ids))
        plan_name_mapper = cls.get_plan_name_mapper(plan_ids)
        for item in paginate.items:
            mission_info = mission_info_mapper[item.mission_id]
            items.append(dict(
                report_date=item.report_date,
                plan_id=item.plan_id,
                mission_id=item.mission_id,
                plan_name=plan_name_mapper.get(item.plan_id),
                mission_condition=MissionCondition[mission_info['mission_condition']].value,
                reward_name=cls.get_reward_name_by_cache_data(mission_info),
                reward=mission_info['reward'],
                delivery_count=item.delivery_count,
                completion_count=item.completion_count,
                finished_count=item.finished_count,
                real_reward_amount=item.real_reward_amount,
                reward_amount=item.reward_amount,
                updated_at=item.updated_at,
                waring_flag=item.real_reward_amount != item.reward_amount,
            ))
        if kwargs.get('export'):
            export_items = []
            for item in items:
                reward = item['reward']
                export_items.append({
                    "report_date": item["report_date"].strftime("%Y-%m-%d"),
                    "plan_id": item["plan_id"],
                    "plan_name": item["plan_name"],
                    "mission_id": item["mission_id"],
                    "mission_condition": item["mission_condition"],
                    "reward_type": item["reward_name"],
                    "delivery_count": item["delivery_count"],
                    "completion_count": item["completion_count"],
                    "finished_count": item["finished_count"],
                    "real_reward_amount": f'{amount_to_str(item["real_reward_amount"])} {reward["value_type"]}',
                    "reward_amount": f'{amount_to_str(item["reward_amount"])} {reward["value_type"]}',
                    "updated_at": datetime_to_utc8_str(item["updated_at"])
                })
            return export_xlsx(
                filename='mission_statistics',
                data_list=export_items,
                export_headers=cls.export_headers
            )
        return dict(
            total=paginate.total,
            items=items,
            extra_data=dict(
                reward_types=EquityType,
                plan_id_mapper=cls.get_all_plan_name_mapper(),
                mission_id_mapper=cls.get_mission_name_mapper()
            )
        )
