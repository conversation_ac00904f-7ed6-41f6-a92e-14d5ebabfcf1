from marshmallow import Schema
from webargs import fields
from sqlalchemy import func

from flask import g

from app import Language
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import EnumField
from app.business import <PERSON>ache<PERSON>ock, LockKeys
from app.common import language_cn_names
from app.exceptions import InvalidArgument, RecordNotFound
from app.models import CharityBanner, db, CharityBannerContent, CharityDonationData, \
    CharityActivity, \
    CharityActivityContent, CharityVideo, CharityCategory, CharityFootprint, \
    CharityFootprintCategory, \
    CharityFootprintContent, row_to_dict, Blog, BlogCategory
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.utils import AWSBucketPublic, format_seo_url_keyword

ns = Namespace('Operation - Charity')


@ns.route('/banner')
@respond_with_code
class CharityBannerResource(Resource):
    LANGUAGES_MAPPER = {
        Language.EN_US: '英语',
        Language.ZH_HANS_CN: ' 简体中文',
    }

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(CharityBanner.Status, missing=""),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=10)
    ))
    def get(cls, **kwargs):
        """慈善-获取banner"""
        query = CharityBanner.query
        if status := kwargs["status"]:
            query = query.filter(CharityBanner.status == status)
        query = query.order_by(CharityBanner.sort_id)
        pagination = query.paginate(kwargs["page"], kwargs["limit"])
        return dict(
            total=pagination.total,
            status={k.name: k.value for k in CharityBanner.Status},
            items=[item.to_dict(enum_to_name=False) for item in pagination.items],
            langs={k.name: v for k, v in cls.LANGUAGES_MAPPER.items()}
        )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(CharityBanner.Status, required=True),
        name=fields.String(required=True),
        contents=fields.Dict(required=True),
    ))
    def post(cls, **kwargs):
        """慈善-新增banner"""
        banner = CharityBanner(
            name=kwargs["name"],
            status=kwargs["status"],
            sort_id=cls.get_sort_id()
        )
        db.session.add(banner)
        db.session.flush()
        banner_contents = []
        for lang, file_data in kwargs['contents'].items():
            if not (file_key := file_data.get('file_key')):
                continue
            banner_contents.append(CharityBannerContent(
                owner_id=banner.id,
                lang=Language[lang],
                file_key=file_key,
            ))
        db.session.add_all(banner_contents)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityBanner,
            detail=kwargs,
        )

    @classmethod
    def get_sort_id(cls) -> int:
        return (CharityBanner.query.with_entities(
            func.max(CharityBanner.sort_id).label('max_sort_id')  # type: ignore
        ).scalar() or 0) + 1


@ns.route('/banner/<int:id_>')
@respond_with_code
class CharityBannerDetailResource(Resource):

    @classmethod
    def get(cls, id_):
        """慈善-获取banner详情"""
        if not (banner := CharityBanner.query.get(id_)):
            raise InvalidArgument
        contents = CharityBannerContent.query.filter(CharityBannerContent.owner_id == id_).all()
        return dict(
            id=banner.id,
            name=banner.name,
            status=banner.status.name,
            contents={
                i.lang.name: {
                    "file_key": i.file_key,
                    "file_url": AWSBucketPublic.get_file_url(i.file_key)
                } for i in contents
            }
        )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(CharityBanner.Status, required=True),
        name=fields.String(required=True),
        contents=fields.Dict(required=True),
    ))
    def put(cls, id_, **kwargs):
        """慈善-修改banner"""
        if not (banner := CharityBanner.query.get(id_)):
            raise InvalidArgument
        old_data = banner.to_dict(enum_to_name=True)
        banner.name = kwargs['name']
        banner.status = kwargs['status']
        exist_lang = {lang for lang, in CharityBannerContent.query.filter(
            CharityBannerContent.owner_id == id_
        ).with_entities(CharityBannerContent.lang).all()}
        update_lang = set()
        for lang, data in kwargs['contents'].items():
            lang = Language[lang]
            if not (file_key := data.get('file_key')):
                continue
            update_lang.add(lang)
            content = CharityBannerContent.query.filter(
                CharityBannerContent.owner_id == id_,
                CharityBannerContent.lang == lang
            ).first()
            if not content:
                content = CharityBannerContent(
                    owner_id=id_,
                    lang=lang,
                    file_key=file_key
                )
                db.session.add(content)
            content.file_key = file_key
        del_lang = exist_lang - update_lang
        CharityBannerContent.query.filter(
            CharityBannerContent.lang.in_(del_lang)
        ).delete()
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityBanner,
            old_data=old_data,
            new_data=banner.to_dict(enum_to_name=True),
            special_data=dict(contents=kwargs['contents']),
        )

    @classmethod
    def delete(cls, id_):
        """慈善-删除banner"""
        if (banner_obj := CharityBanner.query.get(id_)) is None:
            raise InvalidArgument
        name = banner_obj.name
        CharityBannerContent.query.filter(
            CharityBannerContent.owner_id == id_).delete()
        db.session.delete(banner_obj)
        db.session.commit()
        cls.reorder_all()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityBanner,
            detail=dict(id=id_, name=name),
        )

    @staticmethod
    def reorder_all():
        records = CharityBanner.query.order_by(
            CharityBanner.sort_id
        ).all()
        rank = 0
        for record in records:
            rank += 1
            record.sort_id = rank
        db.session.commit()


@ns.route('/banner/<int:id_>/sort')
@respond_with_code
class CharityBannerSortResource(Resource):

    @classmethod
    def get_by_sort_id(cls, sort_id) -> CharityBanner:
        return CharityBanner.query.filter(
            CharityBanner.sort_id == sort_id
        ).first()

    @classmethod
    @ns.use_kwargs(dict(
        up=fields.Boolean(required=True)
    ))
    def put(cls, id_, **kwargs):
        """慈善-排序banner"""
        if not (banner := CharityBanner.query.get(id_)):
            raise InvalidArgument
        old_data = banner.to_dict(enum_to_name=True)

        if kwargs['up']:
            if banner.sort_id == 1:
                raise InvalidArgument(message='已经是第一')
            record = cls.get_by_sort_id(banner.sort_id - 1)
        else:
            last_record: CharityBanner = CharityBanner.query.order_by(
                CharityBanner.sort_id.desc()
            ).first()
            if banner.sort_id == last_record.sort_id:
                raise InvalidArgument(message='已经是最后')
            record = cls.get_by_sort_id(banner.sort_id + 1)
        banner.sort_id, record.sort_id = \
            record.sort_id, banner.sort_id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityBanner,
            old_data=old_data,
            new_data=banner.to_dict(enum_to_name=True),
        )


@ns.route('/donation')
@respond_with_code
class CharityDonationResource(Resource):

    @classmethod
    def get(cls):
        """慈善-获取捐赠数据"""

        record = CharityDonationData.query.all()
        result = {i: dict(
            id=None,
            data_type=i.name,
            data_type_value=i.value,
            data_value=None
        ) for i in CharityDonationData.DataType}
        for item in record:
            result[item.data_type] = dict(
                id=item.id,
                data_type=item.data_type.name,
                data_type_value=item.data_type.value,
                data_value=item.data_value
            )

        return list(result.values())

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(allow_none=True),
        data_type=EnumField(CharityDonationData.DataType, required=True),
        data_value=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """慈善-修改捐赠数据"""
        old_data = None
        if id_ := kwargs.get("id"):
            data = CharityDonationData.query.get(id_)
            old_data = data.to_dict(enum_to_name=True)
        else:
            data = CharityDonationData()
            db.session.add(data)
        if not data:
            raise InvalidArgument
        data.data_type = kwargs['data_type']
        data.data_value = kwargs['data_value']
        db.session.commit()

        AdminOperationLog.new_add_or_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityDonation,
            old_data=old_data,
            new_data=data.to_dict(enum_to_name=True),
        )


@ns.route('/activities')
@respond_with_code
class CharityActivitiesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(CharityActivity.Status, default=""),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=10)
    ))
    def get(cls, **kwargs):
        """慈善-获取活动"""
        query = CharityActivity.query.order_by(CharityActivity.sort_id)
        if status := kwargs.get("status"):
            query = query.filter(
                CharityActivity.status == status
            )
        pagination = query.paginate(kwargs["page"], kwargs["limit"])
        return dict(
            total=pagination.total,
            status={k.name: k.value for k in CharityActivity.Status},
            items=[i.to_dict() for i in pagination.items],
            langs={k.name: v for k, v in CharityBannerResource.LANGUAGES_MAPPER.items()}
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        status=EnumField(CharityActivity.Status, required=True),
        contents=fields.Dict(required=True),
    ))
    def post(cls, **kwargs):
        """慈善-增加活动"""
        activity = CharityActivity(
            name=kwargs['name'],
            status=kwargs["status"],
            sort_id=0,  # sort_id起始值为1，这里给一个小于起始值的值，方便重新排序
        )
        db.session.add(activity)
        db.session.flush()
        activity_contents = []
        for lang, data in kwargs['contents'].items():
            if not (file_key := data.get('file_key')):
                continue
            activity_contents.append(CharityActivityContent(
                owner_id=activity.id,
                lang=Language[lang],
                file_key=file_key,
                title=data['title'],
                desc=data.get('desc')
            ))
        db.session.add_all(activity_contents)
        db.session.commit()
        CharityActivitiesDetailResource.reorder_all()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityActivity,
            detail=kwargs,
        )


@ns.route('/activities/<int:id_>')
@respond_with_code
class CharityActivitiesDetailResource(Resource):

    @classmethod
    def get(cls, id_):
        """慈善-获取活动信息"""
        if not (activity := CharityActivity.query.get(id_)):
            raise InvalidArgument
        contents = CharityActivityContent.query.filter(CharityActivityContent.owner_id == id_).all()
        return dict(
            id=activity.id,
            name=activity.name,
            status=activity.status.name,
            contents={
                i.lang.name: {
                    "title": i.title,
                    "desc": i.desc,
                    "file_key": i.file_key,
                    "file_url": AWSBucketPublic.get_file_url(i.file_key)
                } for i in contents
            }
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        status=EnumField(CharityActivity.Status, required=True),
        contents=fields.Dict(required=True),
    ))
    def put(cls, id_, **kwargs):
        """慈善-修改活动"""
        if not (activity := CharityActivity.query.get(id_)):
            raise InvalidArgument
        old_data = activity.to_dict(enum_to_name=True)
        activity.status = kwargs['status']
        activity.name = kwargs['name']
        exist_lang = {lang for lang, in CharityActivityContent.query.filter(
            CharityActivityContent.owner_id == id_
        ).with_entities(CharityActivityContent.lang).all()}
        update_lang = set()
        for lang, data in kwargs['contents'].items():
            lang = Language[lang]
            if not (file_key := data.get('file_key')):
                continue
            update_lang.add(lang)
            content = CharityActivityContent.query.filter(
                CharityActivityContent.owner_id == id_,
                CharityActivityContent.lang == lang
            ).first()
            if not content:
                content = CharityActivityContent(
                    owner_id=id_,
                    lang=lang
                )
                db.session.add(content)
            old_content_data = row_to_dict(content, enum_to_name=True)
            content.file_key = file_key
            if desc := data.get("desc"):
                content.desc = desc
            if title := data.get("title"):
                content.title = title

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.CharityActivity,
                old_data=old_content_data,
                new_data=row_to_dict(content, enum_to_name=True),
                special_data=dict(lang=lang.name),
            )
        del_lang = exist_lang - update_lang
        CharityActivityContent.query.filter(
            CharityActivityContent.lang.in_(del_lang)
        ).delete()
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityActivity,
            old_data=old_data,
            new_data=activity.to_dict(enum_to_name=True),
            special_data=dict(contents=kwargs['contents']),
        )

    @classmethod
    def delete(cls, id_):
        """慈善-删除活动"""
        if (activity := CharityActivity.query.get(id_)) is None:
            raise InvalidArgument
        name = activity.name
        CharityActivityContent.query.filter(
            CharityActivityContent.owner_id == id_).delete()
        db.session.delete(activity)
        db.session.commit()
        cls.reorder_all()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityActivity,
            detail=dict(id=id_, name=name),
        )

    @staticmethod
    def reorder_all():
        records = CharityActivity.query.order_by(
            CharityActivity.sort_id
        ).all()
        rank = 0
        for record in records:
            rank += 1
            record.sort_id = rank
        db.session.commit()


@ns.route('/activities/<int:id_>/sort')
@respond_with_code
class CharityActivitiesSortResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        up=fields.Boolean(required=True)
    ))
    def put(cls, id_, **kwargs):
        """慈善-排序活动"""
        if not (activity := CharityActivity.query.get(id_)):
            raise InvalidArgument
        old_data = activity.to_dict(enum_to_name=True)

        if kwargs['up']:
            if activity.sort_id == 1:
                raise InvalidArgument(message='已经是第一')
            record = cls.get_by_sort_id(activity.sort_id - 1)
        else:
            last_record: CharityActivity = CharityActivity.query.order_by(
                CharityActivity.sort_id.desc()
            ).first()
            if activity.sort_id == last_record.sort_id:
                raise InvalidArgument(message='已经是最后')
            record = cls.get_by_sort_id(activity.sort_id + 1)
        activity.sort_id, record.sort_id = \
            record.sort_id, activity.sort_id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityActivity,
            old_data=old_data,
            new_data=activity.to_dict(enum_to_name=True),
        )

    @classmethod
    def get_by_sort_id(cls, sort_id) -> CharityActivity:
        return CharityActivity.query.filter(
            CharityActivity.sort_id == sort_id
        ).first()


class CharityMixin:
    model: db.Model
    CHARITY_BLOG_CATEGORY_LIST = ['CoinEx Charity', 'CoinEx慈善']  # 仅用于admin展示慈善相关博客

    class TransSchema(Schema):
        lang = EnumField(Language, required=True)
        name = fields.String(required=True)
        title = fields.String(required=True)
        content = fields.String(required=True)
        abstract = fields.String(required=True)
        cover = fields.String(required=True)
        seo_url_keyword = fields.String(required=True)
        seo_title = fields.String(required=True)
        blog_id = fields.Integer(allow_none=True)

    @classmethod
    def get_category_mapping(cls, type_):
        model = CharityCategory
        rows = model.query.with_entities(
            model.id,
            model.name,
            model.lang,
        ).filter(
            model.type == type_,
            model.status == model.Status.VALID
        ).all()
        lang_mapping, mapping = {}, {}
        for row in rows:
            lang_mapping.setdefault(row.lang.name, {}).update({row.id: row.name})
            mapping.update({row.id: row.name})
        return lang_mapping, mapping

    @classmethod
    def get_footprint_category_mapping(cls):
        model = CharityFootprintCategory
        rows = model.query.filter(
            model.status == model.Status.VALID
        ).with_entities(
            model.id,
            model.cn_name
        ).all()
        return dict(rows)

    @classmethod
    def get_charity_blog_list(cls):
        category_result = BlogCategory.query.filter(
            BlogCategory.remark.in_(cls.CHARITY_BLOG_CATEGORY_LIST),
            BlogCategory.lang.in_([Language.EN_US, Language.ZH_HANS_CN]),
            BlogCategory.status == BlogCategory.Status.VALID
        ).with_entities(
            BlogCategory.id,
            BlogCategory.remark,
        ).all()
        category_map = {item.id: item.remark for item in category_result}
        category_ids = list(category_map.keys())
        blueprint_blog_rows = Blog.query.filter(
            Blog.category_id.in_(category_ids),
            Blog.status == Blog.Status.VALID,
        ).with_entities(
            Blog.id,
            Blog.category_id,
            Blog.remark,
            Blog.title,
            Blog.abstract,
            Blog.seo_title,
            Blog.lang,
        ).order_by(Blog.id.desc()).all()
        blueprint_blogs = [{"id": i.id,
                            "desc": f"{i.id} {category_map.get(i.category_id, '')} {i.remark}",
                            'title': i.title,
                            'abstract': i.abstract,
                            'remark': i.remark,
                            'seo_title': i.seo_title,
                            'lang': i.lang.name,
                            } for i in blueprint_blog_rows]
        return blueprint_blogs

    @classmethod
    def get_sort_id(cls, lang=None) -> int:
        model = cls.model
        query = model.query.with_entities(
            func.max(model.sort_id).label('max_sort_id')  # type: ignore
        )
        if lang:
            query = query.filter(model.lang == lang)
        return (query.scalar() or 0) + 1

    @classmethod
    def get_exchange_sort_id(cls, up, sort_id, lang=None):
        model = cls.model
        if up:
            query = model.query.filter(
                model.sort_id > sort_id
            ).order_by(model.sort_id.asc())
        else:
            query = model.query.filter(
                model.sort_id < sort_id
            ).order_by(model.sort_id.desc())
        if lang:
            query = query.filter(model.lang == lang)
        row = query.first()
        if not row:
            raise InvalidArgument(message='无需排序')
        return row

    @classmethod
    def do_drag_sort_action(cls, lock_key, kwargs):
        model = cls.model
        with CacheLock(LockKeys.edit_operation_sort(lock_key), wait=False):
            old_data = kwargs['old_data']
            old_index = kwargs['old_index']
            new_index = kwargs['new_index']
            edit_banner = model.query.get(old_data[old_index]["id"])
            swap_banner = model.query.get(old_data[new_index]["id"])
            if not edit_banner or not swap_banner:
                raise RecordNotFound
            swap_banner_sort_id = swap_banner.sort_id
            edit_banner_sort_id = edit_banner.sort_id
            if edit_banner_sort_id > swap_banner_sort_id:
                # 表示进行下移
                model.query.filter(
                    model.sort_id >= swap_banner_sort_id,
                    model.sort_id < edit_banner_sort_id
                ).update(
                    {model.sort_id: model.sort_id + 1},
                    synchronize_session=False
                )
                edit_banner.sort_id = swap_banner_sort_id
                db.session.commit()
            else:
                # 表示进行上移
                model.query.filter(
                    model.sort_id > edit_banner_sort_id,
                    model.sort_id <= swap_banner_sort_id
                ).update(
                    {model.sort_id: model.sort_id - 1},
                    synchronize_session=False
                )
                edit_banner.sort_id = swap_banner_sort_id
                db.session.commit()
            return {}

    @classmethod
    def get_category_id(cls, category_id):
        if category_id == -1:  # 类别：无
            return None
        return category_id


@ns.route('/video')
@respond_with_code
class CharityVideoResource(CharityMixin, Resource):
    model = CharityVideo

    @classmethod
    @ns.use_kwargs(dict(
        lang=EnumField(Language, missing=Language.ZH_HANS_CN),
        category_id=fields.Integer(missing=0),
        status=EnumField(CharityVideo.Status),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50),
    ))
    def get(cls, **kwargs):
        """慈善-获取 video"""
        page = kwargs['page']
        limit = kwargs['limit']
        lang = kwargs['lang']
        category_id = kwargs['category_id']
        model = cls.model
        query = model.query.filter(model.lang == lang)
        if category_id:
            if category_id == -1:
                query = query.filter(model.category_id.is_(None))
            else:
                query = query.filter(model.category_id == category_id)
        if status := kwargs.get('status'):
            query = query.filter(model.status == status)
        query = query.order_by(model.sort_id.desc())
        records = query.paginate(page, limit)
        items = records.items
        ret = []
        cate_lang_mapping, cate_mapping = cls.get_category_mapping(CharityCategory.Type.ACTIVITY_VIDEO)
        for item in items:
            tmp_item = item.to_dict(enum_to_name=True)
            tmp_item['category'] = cate_mapping.get(item.category_id, '无')
            ret.append(tmp_item)
        return dict(
            total=records.total,
            items=ret,
            category_map=cate_mapping,
            category_lang_map=cate_lang_mapping,
            status_map=model.Status,
            lang_map={k.name: v for k, v in language_cn_names().items() if k in model.AVAILABLE_LANGS}
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        category_id=fields.Integer(required=True),
        status=EnumField(CharityVideo.Status, missing=CharityVideo.Status.OFFLINE),
        lang=EnumField(Language, required=True),
        title=fields.String(required=True),
        url=fields.String(required=True),
        file_key=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """慈善-新增 video"""
        name = kwargs['name']
        category_id = cls.get_category_id(kwargs['category_id'])
        status = kwargs['status']
        lang = kwargs['lang']
        title = kwargs['title']
        url = kwargs['url']
        file_key = kwargs['file_key']

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityVideo,
            detail=kwargs,
        )

        return db.session_add_and_commit(CharityVideo(
            name=name,
            category_id=category_id,
            status=status,
            title=title,
            url=url,
            file_key=file_key,
            lang=lang,
            sort_id=cls.get_sort_id(),
        ))


@ns.route('/video/<int:id_>')
@respond_with_code
class CharityVideoDetailResource(CharityMixin, Resource):
    model = CharityVideo

    @classmethod
    def get(cls, id_):
        """慈善-获取 video 详情"""
        lang_names = language_cn_names()
        cate_lang_mapping, cate_mapping = cls.get_category_mapping(CharityCategory.Type.ACTIVITY_VIDEO)
        model = CharityVideo
        extra = dict(
            languages={e.name: lang_names[e] for e in model.AVAILABLE_LANGS},
            status_map=model.Status,
            category_lang_map=cate_lang_mapping,
            lang_map={k.name: v for k, v in language_cn_names().items() if k in model.AVAILABLE_LANGS},
        )
        if not id_:
            return dict(
                extra=extra,
                category_map=cate_mapping,
            )

        row = cls.get_row(id_)
        res = row.to_dict(enum_to_name=True)
        res['cover_url'] = row.cover_url
        res['extra'] = extra
        res['category_id'] = str(row.category_id) if row.category_id else None
        res['category_map'] = cate_mapping
        return res

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        category_id=fields.Integer(required=True),
        status=EnumField(CharityVideo.Status, missing=CharityVideo.Status.OFFLINE),
        lang=EnumField(Language, required=True),
        title=fields.String(required=True),
        url=fields.String(required=True),
        file_key=fields.String(required=True),
    ))
    def put(cls, id_, **kwargs):
        """慈善-修改 video"""
        row = cls.get_row(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.name = kwargs['name']
        row.category_id = cls.get_category_id(kwargs['category_id'])
        row.status = kwargs['status']
        row.lang = kwargs['lang']
        row.title = kwargs['title']
        row.url = kwargs['url']
        row.file_key = kwargs['file_key']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityVideo,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

        return row

    @classmethod
    def delete(cls, id_):
        """慈善-删除 video"""
        row = cls.get_row(id_)
        row.status = CharityVideo.Status.OFFLINE
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityVideo,
            detail=dict(id=id_),
        )

    @classmethod
    def get_row(cls, id_):
        row = CharityVideo.query.get(id_)
        if not row:
            raise InvalidArgument
        return row


@ns.route('/video/<int:id_>/sort')
@respond_with_code
class CharityVideoSortResource(CharityMixin, Resource):
    model = CharityVideo

    @classmethod
    @ns.use_kwargs(dict(
        up=fields.Boolean(required=True)
    ))
    def put(cls, id_, **kwargs):
        """慈善-排序 video"""
        if not (row := CharityVideo.query.get(id_)):
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)
        record = cls.get_exchange_sort_id(kwargs['up'], row.sort_id, row.lang)
        row.sort_id, record.sort_id = record.sort_id, row.sort_id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityVideo,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/video/sorts')
@respond_with_code
class CharityVideoSortsResource(CharityMixin, Resource):
    model = CharityVideo

    @classmethod
    @ns.use_kwargs(
        dict(
            old_data=fields.Raw(required=True),
            old_index=fields.Integer(required=True),
            new_index=fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """慈善-拖拽排序 video"""
        return cls.do_drag_sort_action(lock_key='charity_video', kwargs=kwargs)


@ns.route('/category')
@respond_with_code
class CategoryResource(CharityMixin, Resource):
    model = CharityCategory

    @classmethod
    @ns.use_kwargs(dict(
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50),
        lang=EnumField(Language, required=True),
        type=EnumField(CharityCategory.Type, required=True),
    ))
    def get(cls, **kwargs):
        """慈善-类别管理"""
        page = kwargs['page']
        limit = kwargs['limit']
        lang = kwargs['lang']

        model = CharityCategory
        records = model.query.filter(
            model.status == model.Status.VALID,
            model.lang == lang,
            model.type == kwargs['type'],
        ).order_by(
            model.sort_id.desc()
        ).paginate(page, limit)

        record_items = list(records.items)

        return dict(
            total=records.total,
            items=record_items,
            lang_map={k.name: v for k, v in language_cn_names().items() if k in model.AVAILABLE_LANGS}
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        remark=fields.String(required=True),
        lang=EnumField(Language, required=True),
        type=EnumField(CharityCategory.Type, required=True),
    ))
    def post(cls, **kwargs):
        """慈善-添加类别"""
        name = kwargs['name']
        lang = kwargs['lang']
        remark = kwargs['remark']

        model = CharityCategory
        row = model(
            name=name,
            sort_id=cls.get_sort_id(),
            remark=remark,
            lang=lang,
            type=kwargs['type'],
        )
        db.session_add_and_commit(row)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityCategory,
            detail=kwargs,
        )


@ns.route('/category/<int:id_>')
@respond_with_code
class CategoryDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        remark=fields.String(required=True),
    ))
    def put(cls, id_, **kwargs):
        """慈善-修改类别"""
        name = kwargs['name']
        remark = kwargs['remark']
        row = cls.get_row(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.name = name
        row.remark = remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityCategory,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    def delete(cls, id_):
        """慈善-删除类别"""
        row = cls.get_row(id_)
        row.status = CharityCategory.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityCategory,
            detail=dict(id=id_, name=row.name),
        )

    @classmethod
    def get_row(cls, id_):
        row = CharityCategory.query.get(id_)
        if not row:
            raise RecordNotFound
        return row


@ns.route('/category/<int:id_>/sort_id')
@respond_with_code
class CategorySortResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        sort_type=fields.String(required=True),
    ))
    def put(cls, id_, **kwargs):
        """慈善-类别管理-移动"""
        sort_type = kwargs['sort_type']
        model = CharityCategory
        row = model.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        lang = row.lang
        if sort_type == 'up':
            other = model.query.filter(
                model.status == model.Status.VALID,
                model.sort_id > row.sort_id,
                model.lang == lang,
                model.type == row.type,
            ).order_by(model.sort_id.asc()).first()
        else:
            other = model.query.filter(
                model.status == model.Status.VALID,
                model.sort_id < row.sort_id,
                model.lang == lang,
                model.type == row.type,
            ).order_by(model.sort_id.desc()).first()
        if other is not None:
            row.sort_id, other.sort_id = other.sort_id, row.sort_id
            db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityCategory,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/footprint/category')
@respond_with_code
class FootPrintCategoryResource(CharityMixin, Resource):
    model = CharityFootprintCategory

    @classmethod
    @ns.use_kwargs(dict(
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50),
    ))
    def get(cls, **kwargs):
        """慈善-慈善足迹类别管理"""
        page = kwargs['page']
        limit = kwargs['limit']

        records = cls.model.query.filter(
            cls.model.status == cls.model.Status.VALID,
        ).order_by(
            cls.model.sort_id.desc()
        ).paginate(page, limit)

        record_items = list(records.items)

        return dict(
            total=records.total,
            items=record_items,
        )

    @classmethod
    @ns.use_kwargs(dict(
        cn_name=fields.String(required=True),
        en_name=fields.String(required=True),
        remark=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """慈善-添加慈善足迹类别"""
        cn_name = kwargs['cn_name']
        en_name = kwargs['en_name']
        remark = kwargs['remark']

        row = cls.model(
            cn_name=cn_name,
            en_name=en_name,
            sort_id=cls.get_sort_id(),
            remark=remark,
        )
        db.session_add_and_commit(row)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityFootPrintCategory,
            detail=kwargs,
        )


@ns.route('/footprint/category/<int:id_>')
@respond_with_code
class FootPrintCategoryDetailResource(Resource):

    model = CharityFootprintCategory

    @classmethod
    @ns.use_kwargs(dict(
        cn_name=fields.String(required=True),
        en_name=fields.String(required=True),
        remark=fields.String(required=True),
    ))
    def put(cls, id_, **kwargs):
        """慈善-修改慈善足迹类别"""
        cn_name = kwargs['cn_name']
        en_name = kwargs['en_name']
        remark = kwargs['remark']
        row = cls.get_row(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.cn_name = cn_name
        row.en_name = en_name
        row.remark = remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityFootPrintCategory,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    def delete(cls, id_):
        """慈善-删除慈善足迹类别"""
        row = cls.get_row(id_)
        row.status = cls.model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityFootPrintCategory,
            detail=dict(id=id_, cn_name=row.cn_name),
        )

    @classmethod
    def get_row(cls, id_):
        row = cls.model.query.get(id_)
        if not row:
            raise RecordNotFound
        return row


@ns.route('/footprint/category/<int:id_>/sort_id')
@respond_with_code
class FootPrintCategorySortResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        sort_type=fields.String(required=True),
    ))
    def put(cls, id_, **kwargs):
        """慈善-慈善足迹类别管理-移动"""
        sort_type = kwargs['sort_type']
        model = CharityFootprintCategory
        row = model.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        if sort_type == 'up':
            other = model.query.filter(
                model.status == model.Status.VALID,
                model.sort_id > row.sort_id,
            ).order_by(model.sort_id.asc()).first()
        else:
            other = model.query.filter(
                model.status == model.Status.VALID,
                model.sort_id < row.sort_id,
            ).order_by(model.sort_id.desc()).first()
        if other is not None:
            row.sort_id, other.sort_id = other.sort_id, row.sort_id
            db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityFootPrintCategory,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/footprints')
@respond_with_code
class FootPrintResource(CharityMixin, Resource):
    model = CharityFootprint

    @classmethod
    @ns.use_kwargs(dict(
        category_id=fields.Integer(missing=0),
        status=EnumField(CharityFootprint.Status),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50),
    ))
    def get(cls, **kwargs):
        """慈善-足迹"""
        page = kwargs['page']
        limit = kwargs['limit']
        category_id = kwargs['category_id']
        model = CharityFootprint
        query = model.query.order_by(model.sort_id.desc())
        if category_id:
            if category_id == -1:
                query = query.filter(model.category_id.is_(None))
            else:
                query = query.filter(model.category_id == category_id)
        if status := kwargs.get('status'):
            query = query.filter(model.status == status)
        records = query.paginate(page, limit)
        items = records.items
        ret = []
        cate_mapping = cls.get_footprint_category_mapping()
        for item in items:
            tmp_item = item.to_dict(enum_to_name=True)
            tmp_item['category'] = cate_mapping.get(item.category_id, '无')
            ret.append(tmp_item)
        return dict(
            total=records.total,
            items=ret,
            category_map=cate_mapping,
            status_map=model.Status,
        )

    @classmethod
    @ns.use_kwargs(dict(
        category_id=fields.Integer(required=True),
        is_use_blog=fields.Boolean(required=True),
        status=EnumField(CharityFootprint.Status, missing=CharityFootprint.Status.OFFLINE),
        translations=fields.Nested(CharityMixin.TransSchema, many=True, required=True),
    ))
    def post(cls, **kwargs):
        """慈善-创建足迹"""
        category_id = cls.get_category_id(kwargs['category_id'])
        status = kwargs['status']
        is_use_blog = kwargs['is_use_blog']
        translations = kwargs['translations']
        cn_trans = list(filter(lambda x: x['lang'] == Language.ZH_HANS_CN, translations))[0]
        display_name = cn_trans['name']
        footprint = CharityFootprint(
            category_id=category_id,
            status=status,
            is_use_blog=is_use_blog,
            display_name=display_name,
            sort_id=cls.get_sort_id()
        )
        db.session.add(footprint)
        db.session.flush()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityFootPrint,
            detail=footprint.to_dict(enum_to_name=True),
        )

        for tran in translations:
            lang = tran['lang']
            name = tran['name']
            title = tran['title']
            content = tran['content']
            abstract = tran['abstract']
            cover = tran['cover']
            blog_id = tran.get('blog_id', None)
            seo_url_keyword = format_seo_url_keyword(tran['seo_url_keyword'])
            seo_title = tran['seo_title']
            content = CharityFootprintContent(
                footprint_id=footprint.id,
                lang=lang,
                name=name,
                seo_url_keyword=seo_url_keyword,
                seo_title=seo_title,
                title=title,
                blog_id=blog_id,
                content=content,
                abstract=abstract,
                cover=cover,
            )
            db.session.add(content)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.CharityFootPrintContent,
                detail=content.to_dict(enum_to_name=True),
            )
        db.session.commit()
        return footprint


@ns.route('/footprint/<int:id_>')
@respond_with_code
class FootprintDetailResource(CharityMixin, Resource):
    model = CharityFootprint

    @classmethod
    def get(cls, id_):
        """慈善-足迹详情"""
        model = CharityFootprint
        cate_mapping = cls.get_footprint_category_mapping()
        charity_blog_list = cls.get_charity_blog_list()
        extra = dict(
            languages=[v.name for v in model.AVAILABLE_LANGS],
            charity_blog_list=charity_blog_list,
            status_map=model.Status,
            lang_map={k.name: v for k, v in language_cn_names().items() if k in model.AVAILABLE_LANGS},
        )
        if not id_:
            return dict(
                extra=extra,
                category_map=cate_mapping,
            )
        row = cls.get_row(id_)
        content_records = CharityFootprintContent.query.filter(
            CharityFootprintContent.footprint_id == id_
        ).all()
        translations = []
        for rec in content_records:
            item = {
                'lang': rec.lang.name,
                'name': rec.name,
                'title': rec.title,
                'content': rec.content,
                'abstract': rec.abstract,
                'cover': rec.cover,
                'blog_id': rec.blog_id,
                'seo_url_keyword': rec.seo_url_keyword,
                'seo_title': rec.seo_title,
                'cover_url': rec.cover_url,
            }
            translations.append(item)
        res = row.to_dict(enum_to_name=True)
        res['translations'] = translations
        res['extra'] = extra
        res['category_id'] = str(row.category_id) if row.category_id else None
        res['category_map'] = cate_mapping
        return res

    @classmethod
    @ns.use_kwargs(dict(
        category_id=fields.Integer(required=True),
        is_use_blog=fields.Boolean(required=True),
        status=EnumField(CharityFootprint.Status, missing=CharityFootprint.Status.OFFLINE),
        translations=fields.Nested(CharityMixin.TransSchema, many=True, required=True),
    ))
    def patch(cls, id_, **kwargs):
        """慈善-足迹编辑"""
        row = cls.get_row(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.category_id = cls.get_category_id(kwargs['category_id'])
        row.status = kwargs['status']
        row.is_use_blog = kwargs['is_use_blog']
        translations = kwargs['translations']
        trans_dic = {tran['lang']: tran for tran in translations}
        display_name = trans_dic[Language.ZH_HANS_CN]['name']
        row.display_name = display_name
        contents = CharityFootprintContent.query.filter(
            CharityFootprintContent.footprint_id == id_
        ).all()
        for item in contents:
            tran = trans_dic.get(item.lang)
            if not tran:
                continue
            tran_old_data = item.to_dict(enum_to_name=True)
            item.name = tran['name']
            item.seo_url_keyword = format_seo_url_keyword(tran['seo_url_keyword'])
            item.seo_title = tran['seo_title']
            item.title = tran['title']
            item.blog_id = tran.get('blog_id', None)
            item.content = tran['content']
            item.abstract = tran['abstract']
            item.cover = tran['cover']

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.CharityFootPrintContent,
                old_data=tran_old_data,
                new_data=item.to_dict(enum_to_name=True),
                special_data=dict(lang=item.lang.name),
            )
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityFootPrint,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return row

    @classmethod
    def delete(cls, id_):
        """慈善-足迹下架"""
        row = cls.get_row(id_)
        row.status = CharityFootprint.Status.OFFLINE
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityFootPrint,
            detail=dict(id=id_, display_name=row.display_name),
        )

    @classmethod
    def get_row(cls, id_):
        row = CharityFootprint.query.get(id_)
        if not row:
            raise InvalidArgument
        return row

    @classmethod
    def put(cls, id_):
        """慈善-足迹上架"""
        row = cls.get_row(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.status = CharityFootprint.Status.ONLINE
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityFootPrint,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/footprint/<int:id_>/sort')
@respond_with_code
class FootprintSortResource(CharityMixin, Resource):
    model = CharityFootprint

    @classmethod
    @ns.use_kwargs(dict(
        up=fields.Boolean(required=True)
    ))
    def put(cls, id_, **kwargs):
        """慈善-足迹排序"""
        if not (row := CharityFootprint.query.get(id_)):
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)
        record = cls.get_exchange_sort_id(kwargs['up'], row.sort_id)
        row.sort_id, record.sort_id = record.sort_id, row.sort_id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CharityFootPrint,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/footprint/sorts')
@respond_with_code
class CharityFootprintSortResource(CharityMixin, Resource):
    model = CharityFootprint

    @classmethod
    @ns.use_kwargs(
        dict(
            old_data=fields.Raw(required=True),
            old_index=fields.Integer(required=True),
            new_index=fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """慈善-足迹拖拽排序"""
        return cls.do_drag_sort_action(lock_key='charity_footprint', kwargs=kwargs)
