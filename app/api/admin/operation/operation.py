# -*- coding: utf-8 -*-

import os
import json
from copy import deepcopy
from functools import partial
import hashlib
import math
from collections import defaultdict, OrderedDict
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from typing import Callable, Dict, List, Optional, Tuple, Union

from marshmallow import Schema, EXCLUDE
from pyroaring import BitMap

from app import config
from app.api.admin.op_log import AdminOperationLogResource
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.decorators import require_admin_webauth_token
from app.api.common.fields import (AmountField, AssetField, BoolField, CustomIntegerField,
                                   DateField, DateMarshalField, EnumField,
                                   EnumMarshalField, IconField,
                                   JsonMarshalField, LimitField, PageField, PerpetualMarketField,
                                   PositiveDecimalField, TimestampField,
                                   TimestampMarshalField)
from app.api.common.request import require_email_not_exists
from app.api.admin.utils import UploadFileHelper
from app.business.push_statistic import email_push_user_statistic, \
    PopupPageViewStatistic, PopupClickCountStatistic, UserTagGroupBiz, PopupWindowUserParser, FILTER_CONFIGS, \
    ExpressionConfig, EmailPushReadCountStatistic, \
    EmailPushClickCountStatistic, WebPopupPageViewStatistic, AndroidPopupPageViewStatistic, IosPopupPageViewStatistic, \
    WebPopupClickCountStatistic, AndroidPopupClickCountStatistic, IosPopupClickCountStatistic
from app.business.security import SecurityQuestionBusiness
from app.assets import list_all_assets
from app.business import (
    CacheLock, LockKeys,
    UserPreferences, VipHelper,
    send_deposit_activity_gift_task,
    send_kyc_result_email, send_trade_activity_gift_task,
    send_channel_reward_activity_gift_task,
    update_deposit_activity_rank_task,
    update_user_location, BusinessSettings, ServerClient,
)
from app.business.activity.deposit import generator_rule_description
from app.business.activity.trade import TradeActivityBusiness
from app.business.activity.anti_fraud import AntiFraudHelper
from app.business.auth import get_admin_user_name_map
from app.business.email import (EmailSender,
                                send_coin_application_rejected_notice_email,
                                send_ieo_application_rejected_notice_email,
                                send_kyc_institution_result_email, send_kyc_pro_result,
                                send_reset_security_notice_email, send_liveness_check_email_task)
from app.business.kyc import KycBusiness, KycProBusiness, LivenessCheckBusiness
from app.business.prices import PriceManager
from app.business.red_packet.constants import COIN_PLACES, validate_c_box_code
from app.business.risk_control.withdrawal import BalanceManager
from app.business.risk_screen import RiskScreenBusiness
from app.business.security import SecurityResetMethod, reset_security_info, SecurityBusiness
from app.business.user import UserRepository, UnfreezeAccountHelper, get_user_remark
from app.caches import (KYCAuditorsCache, MarketCache, PerpetualMarketCache,
                        RedPacketOverallCache, MarginAccountIdCache)
from app.caches.admin import ZendeskArticleCache
from app.caches.amm import AmmMarketCache
from app.caches.operation import CBoxPromotionCache, CBoxThemeCache, KYCAssignAuditorsCache, KYCProAssignAuditorsCache, \
    SecurityResetApplicationAssignAuditorsCache, LivenessAssignAuditorsCache
from app.caches.user import AbnormalUserCache
from app.caches.activity import AdminFileUploadChunk
from app.common import (LANGUAGE_NAMES, Language, get_country, language_cn_names,
                        list_country_codes_3_admin)
from app.common.constants import language_name_cn_names, ADMIN_EXPORT_LIMIT
from app.common.countries import search_for_countries
from app.exceptions import (InvalidArgument, OperationDenied, RecordNotFound,
                            ServiceUnavailable, FileTooBig)
from app.models import (Activity, ActivityBlackList,
                        AppVersion, AppVersionLangDetails, CoinApplicationFiles,
                        CoinListingApplication, Deposit, DepositActivity,
                        DepositActivityRank, DepositActivityRule, EmailPush,
                        EmailPushContent, EmailPushUnsubscription, File,
                        GiftHistory, IeoApplicationFiles,
                        IeoListingApplication, InstitutionCompany,
                        InstitutionDirector, KYCInstitution, KycVerification,
                        KycVerificationHistory, KycVerificationStatistics,
                        NotificationBar,
                        NotificationBarContent, OperationTemplate,
                        OperationTemplateContent, PopupWindow,
                        PopupWindowContent, PushUrlConfig, RedPacket,
                        RedPacketHistory, ReferralPicture,
                        SubAccount, TradeRankActivityUserInfo,
                        TradeRankActivity, User, UserSpecialConfigChangeLog,
                        UserTradeSummary, VipUser, KycCountryConfig,
                        AssetMarketOfflineEmailPush, db, row_to_dict, CBoxCode,
                        CBoxOnlyNewUserConf, CBoxTheme, RedPacketWhiteListUsers, CBoxThemeCategory, KycVerificationPro,
                        KycVerificationProHistory, AntiFraudScore, LivenessCheckHistory,
                        CBoxPromotion, CBoxPromotionContent, AdminUserRole, AdminUser, UserExtra)
from app.models.media import Video
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectSystem
from app.models.mongo.op_log import OPNamespaceObjectOperation, OPNamespaceObjectRisk
from app.models.operation import (Portrait,
                                  TradeRankActivityDetail,
                                  TradeRankActivityJoinUser, TradeRankActivityStatistics, ChannelRewardActivity,
                                  ChannelRewardHistory, ChannelRewardPlatform
                                  )
from app.models.security import (SecurityResetAnswerHistory,
                                 SecurityResetApplication, SecurityResetApplicationChangelog, SecurityResetFile,
                                 SecurityToolHistory, UnfreezeOperationLog)
from app.models.system import VerifyChannel
from app.models.user_tag import UserTagGroup
from app.schedules.kyc import process_risk_screen_request_task
from app.schedules.operation import update_offline_asset_market_push_status, \
    update_zendesk_article_schedule
from app.utils import (AWSBucketPublic, AWSBucketPrivate, ExcelExporter, amount_to_str, batch_iter,
                       compact_json_dumps, exhaust, now,
                       query_to_page, validate_email, validate_url, export_xlsx, safe_percent_format)
from app.utils.amount import quantize_amount
from app.utils.date_ import current_timestamp, datetime_to_str, datetime_to_utc8_str, \
    timestamp_to_datetime
from app.utils.export import remove_illegal_excel_chars
from app.utils.helper import Struct
from app.utils.importer import get_table_rows
from app.utils.iterable import iter_by_id, group_by
from app.utils.offset_to_page import list_to_page
from app.utils.parser import JsonEncoder
from flask import g, request, send_file
from flask_babel import gettext, force_locale
from flask_restx import fields as fx_fields
from sqlalchemy import and_, func, or_
from webargs import fields
from werkzeug.utils import secure_filename

ns = Namespace('Operation - Operation')
url_prefix = ""


@ns.route('/push/url')
@respond_with_code
class PushUrlResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        urls=fields.List(fields.Dict, required=True),
        business_id=fields.Integer(required=True),
        business=EnumField(PushUrlConfig.Business, missing=PushUrlConfig.Business.EMAIL_PUSH),
    ))
    def put(cls, **kwargs):
        """运营-触达管理-生成渲染 url"""
        business = kwargs['business']
        business_id = kwargs['business_id']
        pending_urls = []
        updating_urls = []
        for url_mapping in kwargs['urls']:
            url = url_mapping.get('url')
            if not url:
                raise InvalidArgument(message='Invalid url')

            remark = url_mapping.get('remark') or ''
            url_id = url_mapping.get('id')
            if url_id is not None:
                updating_urls.append(dict(id=url_id, url=url, remark=remark))
            else:
                pending_urls.append(dict(url=url, remark=remark))

        pending_objs = []
        for url_kw in pending_urls:
            pending_objs.append(
                PushUrlConfig(
                    business=business,
                    **url_kw
                )
            )

        updating_url_ids = {kw['id'] for kw in updating_urls}
        objs = PushUrlConfig.query.filter(PushUrlConfig.id.in_(updating_url_ids)).all()
        mapping = {obj.id: obj for obj in objs}
        updating_objs = []
        for updating_url_kw in updating_urls:
            obj = mapping.get(updating_url_kw['id'])
            if not obj:
                pending_objs.append(
                    PushUrlConfig(
                        business=business,
                        url=updating_url_kw['url'],
                        remark=updating_url_kw['remark'],
                    )
                )
            else:
                obj.url = updating_url_kw['url']
                obj.remark = updating_url_kw['remark']
                updating_objs.append(obj)

        db.session.add_all(pending_objs)
        db.session.commit()
        return {
            obj.url: {
                'id': obj.id,
                'target_url': obj.get_target_url(business_id)
            } for obj in pending_objs + updating_objs
        }


def fetch_user_emails(user_ids: set) -> dict:
    user_emails = User.query.with_entities(
        User.id,
        User.email,
    ).filter(User.id.in_(user_ids)).all()
    return dict(user_emails)


class EmailPushMixin:

    @classmethod
    def filter_url_ids(cls, url_ids) -> list:
        model = PushUrlConfig
        urls = model.query.with_entities(model.id).filter(model.id.in_(url_ids)).all()
        return [url.id for url in urls]

    @classmethod
    def trim_row(cls, row: EmailPush) -> dict:
        row_dict = row.to_dict(enum_to_name=True)  # 序列化用不到，返回时置为空字符
        cls.trim_row_dict(row_dict)
        return row_dict

    @classmethod
    def trim_row_dict(cls, row_dict: dict):
        """序列化处理字节类型（mysql blob 类型）"""
        row_dict.pop('user_ids', None)


@ns.route('/email-pushes')
@respond_with_code
class EmailPushesResource(EmailPushMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String,
        status=EnumField(EmailPush.Status),
        start_time=TimestampField(),
        end_time=TimestampField(),
        keyword=fields.String,
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-触达管理-邮件推送"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)
        records = query.paginate(params.page, params.limit)
        items = records.items
        create_user_ids = {item.created_by for item in items}
        name_map = get_admin_user_name_map(create_user_ids)
        pushes = []
        for row in items:
            r_dict = row_to_dict(row, enum_to_name=True)
            r_dict['created_user_email'] = name_map.get(row.created_by) or '-'
            r_dict['user_read_rate'] = row.user_read_rate(EmailPushReadCountStatistic.count(row.id))
            r_dict['user_unsubscribe_rate'] = row.user_unsubscribe_rate
            r_dict['user_click_rate'] = row.user_click_rate(EmailPushClickCountStatistic.count(row.id))

            cls.trim_row_dict(row_dict=r_dict)

            can_del = True
            if row.status in (EmailPush.Status.PROCESSING, EmailPush.Status.FINISHED):
                can_del = False
            r_dict['can_del'] = can_del
            pushes.append(r_dict)

        return dict(
            total=records.total,
            items=pushes,
            extra=dict(
                push_types=EmailPush.PushType,
                statuses=EmailPush.Status
            )
        )

    @classmethod
    def get_query_by(cls, params):
        model = EmailPush
        query = model.query.filter(
            model.status != model.Status.DELETED,
            model.type == model.Type.NORMAL_PUSH
        ).order_by(model.id.desc())
        if params.name:
            query = query.filter(model.name.contains(params.name))
        if params.status:
            query = query.filter(model.status == params.status)
        if params.start_time:
            query = query.filter(model.push_time >= params.start_time)
        if params.end_time:
            query = query.filter(model.push_time <= params.end_time)
        if params.keyword:
            user_ids = User.search_for_users(params.keyword)
            query = query.filter(model.created_by.in_(user_ids))

        return query

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        push_type=EnumField(EmailPush.PushType),
        push_scope_type=EnumField(EmailPush.PushScopeType, missing=EmailPush.PushScopeType.EXCLUDE_UNSUBSCRIBE),
        push_time_type=EnumField(EmailPush.PushTimeType),
        push_time=TimestampField(required=True),
        remark=fields.String(required=False),
        user_type=EnumField(EmailPush.UserType),
        groups=fields.List(fields.Integer, required=True),
        whitelist_enabled=fields.Boolean(missing=False),
        user_whitelist=fields.String(required=False),
        url_ids=fields.List(fields.Integer, required=False),
        status=EnumField(EmailPush.Status),
    ))
    def post(cls, **kwargs):
        """运营-触达管理-邮件推送-创建"""
        status = kwargs.get('status')
        if status not in (EmailPush.Status.DRAFT, EmailPush.Status.CREATED):
            raise OperationDenied(message=f'invalid status {status!r}')

        if kwargs['push_type'] is not EmailPush.PushType.SYSTEM:  # reset if others
            kwargs['push_scope_type'] = EmailPush.PushScopeType.EXCLUDE_UNSUBSCRIBE
        user_whitelist = kwargs.pop('user_whitelist', None) or ''
        url_ids = kwargs.pop('url_ids', None) or ''
        groups = kwargs.pop('groups') or ''
        if groups:
            groups = UserTagGroupBiz.filter_tag_group_ids(ids=groups)
            groups = json.dumps(groups) if groups else ''
        if url_ids:
            url_ids = cls.filter_url_ids(url_ids)
            url_ids = json.dumps(url_ids) if url_ids else ''

        obj = db.session_add_and_commit(EmailPush(
            created_by=g.user.id,
            user_whitelist=user_whitelist,
            urls=url_ids,
            groups=groups,
            system_template_kwargs='',
            **kwargs
        ))
        email_push_user_statistic.delay(obj.id)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.EmailPush,
            detail=kwargs,
        )
        return cls.trim_row(row=obj)


@ns.route('/email-pushes/<int:id_>')
@respond_with_code
class EmailPushResource(EmailPushMixin, Resource):

    @classmethod
    def get(cls, id_):
        """运营-触达管理-邮件推送详情"""
        lang_names = language_cn_names()
        extra = dict(
            push_types=EmailPush.PushType,
            push_time_types=EmailPush.PushTimeType,
            push_scope_types=EmailPush.PushScopeType,
            user_types=EmailPush.UserType,
            statuses=EmailPush.Status,
            languages={e.name: lang_names[e]
                       for e in EmailPushContent.AVAILABLE_LANGS}
        )
        if not id_:
            return dict(
                extra=extra
            )
        row = EmailPush.query.get(id_)
        if row is None:
            raise RecordNotFound

        res = row.to_dict(enum_to_name=True)
        res['user_whitelist'] = row.user_whitelist
        res['user_filters'] = row.cached_user_filters
        res['urls_info'] = cls.get_urls_info(row.cached_url_ids, id_)
        res['user_unsubscribe_rate'] = row.user_unsubscribe_rate

        read_count = EmailPushReadCountStatistic.count(id_)
        res["user_read_count"] = read_count
        res['user_read_rate'] = row.user_read_rate(read_count)

        click_count = EmailPushClickCountStatistic.count(id_)
        res["user_click_count"] = click_count
        res['user_click_rate'] = row.user_click_rate(click_count)

        cls.trim_row_dict(row_dict=res)

        user_ids = {row.created_by}
        if row.audited_by:
            user_ids.add(row.audited_by)
        name_map = get_admin_user_name_map(user_ids)
        created_user_email = name_map.get(row.created_by) or '-'
        audited_user_email = name_map.get(row.audited_by) or '-'
        res['created_user_email'] = created_user_email
        res['audited_user_email'] = audited_user_email
        extra.update(series_data=cls.get_series_data(row))
        extra.update(tag_groups=UserTagGroupBiz.get_tag_group_info(row.get_groups()))
        res['extra'] = extra
        return res

    @classmethod
    def get_urls_info(cls, url_ids, push_id):
        model = PushUrlConfig
        urls = model.query.filter(model.id.in_(url_ids)).all()
        urls_info = []
        for url in urls:
            urls_info.append(
                dict(
                    id=url.id,
                    url=url.url,
                    target_url=url.get_target_url(business_id=push_id),
                    click_count=EmailPushClickCountStatistic.count(url.id),
                    remark=url.remark,
                )
            )
        return urls_info

    @classmethod
    def get_series_data(cls, email_push):
        read_statistic = EmailPushReadCountStatistic
        click_statistic = EmailPushClickCountStatistic
        read_count_series_data = read_statistic.get_series_data(email_push)
        click_count_series_data = click_statistic.get_series_data(email_push)
        ret = defaultdict(list)
        _hour_field = f"_{read_statistic.HOUR_SERIES_NUM}h_series"
        _day_field = f"_{read_statistic.DAY_SERIES_NUM}d_series"
        for field, data_list in read_count_series_data.items():
            for idx, data in enumerate(data_list):
                ret[field].append({
                    "key": data["key"],
                    "value": {
                        "read_count": data["value"][read_statistic.REPORT_FIELD],
                        "click_count": click_count_series_data[field][idx]["value"][click_statistic.REPORT_FIELD],
                    }
                })

        return ret

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        push_type=EnumField(EmailPush.PushType),
        push_scope_type=EnumField(EmailPush.PushScopeType, missing=EmailPush.PushScopeType.EXCLUDE_UNSUBSCRIBE),
        push_time_type=EnumField(EmailPush.PushTimeType),
        push_time=TimestampField(required=True),
        remark=fields.String(required=False),
        user_type=EnumField(EmailPush.UserType),
        groups=fields.List(fields.Integer, required=True),
        whitelist_enabled=fields.Boolean(missing=False),
        user_whitelist=fields.String(required=False),
        url_ids=fields.List(fields.Integer, required=False),
        status=EnumField(EmailPush.Status),
    ))
    def patch(cls, id_, **kwargs):
        """运营-触达管理-邮件推送-编辑"""
        row = EmailPush.query.get(id_)
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)

        status = kwargs.get('status')
        if status not in (EmailPush.Status.DRAFT, EmailPush.Status.CREATED):
            raise OperationDenied(message=f'invalid status {status!r}')
        if row.status in (EmailPush.Status.PROCESSING, EmailPush.Status.FINISHED):
            raise OperationDenied(message='cannot edit a processing/finished item')

        if kwargs['push_type'] is not EmailPush.PushType.SYSTEM:  # reset if others
            kwargs['push_scope_type'] = EmailPush.PushScopeType.EXCLUDE_UNSUBSCRIBE
        user_whitelist = kwargs.pop('user_whitelist', None) or ''
        groups = kwargs.pop('groups') or ''
        if groups:
            groups = UserTagGroupBiz.filter_tag_group_ids(ids=groups)
            groups = json.dumps(groups) if groups else ''
        url_ids = kwargs.pop('url_ids', None) or ''
        if url_ids:
            url_ids = cls.filter_url_ids(url_ids)
            url_ids = json.dumps(url_ids) if url_ids else ''

        row.name = kwargs['name']
        row.push_type = kwargs['push_type']
        row.push_scope_type = kwargs['push_scope_type']
        row.push_time_type = kwargs['push_time_type']
        row.push_time = kwargs['push_time']
        row.remark = kwargs['remark']
        row.user_type = kwargs['user_type']
        row.groups = groups
        row.whitelist_enabled = kwargs['whitelist_enabled']
        row.user_whitelist = user_whitelist
        row.urls = url_ids
        row.status = kwargs['status']
        row.updated_at = now()
        db.session.commit()

        email_push_user_statistic.delay(row.id)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.EmailPush,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return cls.trim_row(row)

    @classmethod
    def delete(cls, id_):
        """运营-触达管理-邮件推送-删除"""
        row: EmailPush = EmailPush.query.get(id_)
        if row is None:
            raise RecordNotFound
        statuses = EmailPush.Status
        if row.status in (statuses.PROCESSING, statuses.FINISHED):
            raise OperationDenied(message='cannot delete a processing/finished item')
        row.status = statuses.DELETED
        row.updated_at = now()
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.EmailPush,
            detail=dict(id=id_, name=row.name),
        )
        return {}


@ns.route('/email-pushes/<int:id_>/user-download')
@respond_with_code
class EmailPushUserResource(Resource):

    @classmethod
    def get(cls, id_):
        """运营-触达管理-邮件推送用户下载"""
        row = EmailPush.query.get(id_)
        if row is None:
            raise RecordNotFound

        data = cls.get_data(row)
        fields = headers = ["id", "email"]
        stream = ExcelExporter(
            data_list=data,
            fields=fields,
            headers=headers
        ).export_streams()
        return send_file(
            stream,
            download_name=f'推送{row.id}客群信息.xlsx',
            as_attachment=True
        )

    @classmethod
    def get_data(cls, row) -> list:
        user_ids = row.cached_user_ids
        model = User
        objs = []
        for chunk_ids in batch_iter(user_ids, 2000):
            chunk_objs = model.query.with_entities(
                model.id, model.email
            ).filter(
                model.id.in_(chunk_ids)
            ).all()
            objs.extend(chunk_objs)
        return [{'id': obj.id, 'email': obj.email} for obj in objs]


@ns.route('/email-pushes/<int:id_>/urls')
@respond_with_code
class EmailPushUrlsResource(EmailPushMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        url_ids=fields.List(fields.Integer, required=True),
    ))
    def patch(cls, id_, **kwargs):
        """运营-触达管理-邮件推送-保存 url_ids"""
        row: EmailPush = EmailPush.query.get(id_)
        if row is None:
            raise RecordNotFound

        statuses = EmailPush.Status
        if row.status in (statuses.PROCESSING, statuses.FINISHED):
            raise OperationDenied(message='processing/finished status item can not be edit')

        url_ids = kwargs.pop('url_ids', None) or ''
        if url_ids:
            url_ids = cls.filter_url_ids(url_ids)
            url_ids = json.dumps(url_ids) if url_ids else ''
        row.urls = url_ids
        db.session.commit()
        return cls.trim_row(row)


@ns.route('/email-pushes/<int:id_>/created')
@respond_with_code
class EmailPushDraftResource(EmailPushMixin, Resource):

    @classmethod
    def patch(cls, id_):
        """运营-触达管理-邮件推送-提交审核"""
        row: EmailPush = EmailPush.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        statuses = EmailPush.Status
        if row.status is not statuses.DRAFT:
            raise OperationDenied(message='only draft status item can be edit')

        row.status = statuses.CREATED
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.EmailPush,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return cls.trim_row(row)


@ns.route('/email-pushes/<int:id_>/audit')
@respond_with_code
class EmailPushAuditResource(EmailPushMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(EmailPush.Status, required=True),
        auditor_remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-触达管理-邮件推送-审核"""
        params = Struct(**kwargs)
        user_id: int = g.user.id
        row: EmailPush = EmailPush.query.get(id_)
        if row is None:
            raise RecordNotFound

        statuses = EmailPush.Status
        if row.status is not statuses.CREATED:
            raise OperationDenied(message='only created status item can be audit')

        if params.status not in (statuses.AUDITED, statuses.REJECTED):
            raise OperationDenied(message=f'invalid status {params.status!r}')

        if user_id == row.created_by:
            raise OperationDenied(message='内容审核人不得与内容创建人重复！')

        if row.push_time_type is EmailPush.PushTimeType.REALTIME:
            row.push_time = now()
        row.status = params.status
        row.audited_by = user_id
        row.audited_at = now()
        row.auditor_remark = params.auditor_remark or ''
        db.session.commit()

        kwargs['id'] = id_
        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.EmailPush,
            detail=kwargs,
        )
        return cls.trim_row(row)


# noinspection PyUnresolvedReferences
@ns.route('/email-pushes/<int:id_>/langs/<lang>')
@respond_with_code
class EmailPushContentResource(Resource):

    @classmethod
    def get(cls, id_, lang):
        """运营-邮件推送-获取邮件推送内容"""
        row = cls._get_row(id_, lang)
        if row is None:
            return dict(
                title='',
                content=''
            )
        return dict(
            title=row.title,
            content=row.content
        )

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String(required=True),
        content=fields.String(required=True)
    ))
    def put(cls, id_, lang, **kwargs):
        """运营-邮件推送-编辑邮件推送内容"""
        title = kwargs['title']
        content = kwargs['content']
        row = cls._get_row(id_, lang)
        if row is None:
            row = EmailPushContent(
                email_push_id=id_,
                lang=lang,
                title=title,
                content=content,
            )
            db.session.add(row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.EmailPushContent,
                detail=dict(
                    email_push_id=id_,
                    lang=lang,
                    title=title,
                    content=content,
                ),
            )
        else:
            old_data = dict(
                title=row.title,
                content=row.content,
            )
            row.title = title
            row.content = content
            row.updated_at = now()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.EmailPushContent,
                old_data=old_data,
                new_data=dict(
                    title=row.title,
                    content=row.content,
                ),
                special_data=dict(
                    email_push_id=id_,
                    lang=lang,
                ),
            )
        db.session.commit()
        return row

    @classmethod
    def _get_row(cls, id_: int, lang: str) -> Optional[EmailPushContent]:
        return EmailPushContent.query \
            .filter(EmailPushContent.email_push_id == id_,
                    EmailPushContent.lang == getattr(Language, lang, '')) \
            .first()


@ns.route('/email-pushes/<int:id_>/test')
@respond_with_code
class EmailPushContentTestResource(Resource):

    @classmethod
    def get(cls, id_):
        """邮件测试-检查"""
        model = EmailPushContent
        langs = model.query.with_entities(
            model.lang
        ).filter(
            model.email_push_id == id_
        ).all()
        langs = {e.lang for e in langs}
        if not langs:
            raise InvalidArgument(message='触达内容不能为空，请保存！')

    @classmethod
    @ns.use_kwargs(dict(
        accounts=fields.List(fields.Dict, required=True),
        push_type=EnumField(EmailPush.PushType, missing=EmailPush.PushType.SYSTEM)
    ))
    def post(cls, id_, **kwargs):
        """运营-触达管理-内容邮件测试"""
        accounts = kwargs['accounts']
        push_type = kwargs['push_type']
        is_system = False
        if push_type is EmailPush.PushType.SYSTEM:
            is_system = True
        if not accounts:
            raise InvalidArgument
        accounts = [account.get('account').strip() for account in accounts if account.get('account')]
        for account in accounts:
            if not validate_email(email=account):
                raise InvalidArgument(message=f'Invalid email {account!r}')

        lang2content_mapping = cls.get_lang2content_mapping(email_push_id=id_)
        if not lang2content_mapping:
            raise InvalidArgument(message='No content matching')

        users = User.query.with_entities(
            User.id,
            User.email,
        ).filter(User.email.in_(accounts)).all()
        missing = []
        for user in users:
            user_pref = UserPreferences(user.id)
            lang = user_pref.language
            content_mapping = lang2content_mapping.get(lang)
            if not content_mapping:
                missing.append(f'{user.email}:{lang}')
                continue
            content = content_mapping['content']
            title = content_mapping['title']
            if not content or not title:
                continue
            if not cls.user_push_enabled(user_pref, push_type):
                continue
            cls.send_push_email_test(id_, content, user.email, lang.value, title, is_system)

        return missing

    @classmethod
    def get_lang2content_mapping(cls, email_push_id):
        lang_to_content_mapping = {}
        model = EmailPushContent
        contents = model.query.filter(
            model.email_push_id == email_push_id
        ).all()
        if not contents:
            return lang_to_content_mapping

        for content in contents:
            lang_to_content_mapping[content.lang] = dict(title=content.title, content=content.content)
        return lang_to_content_mapping

    @classmethod
    def user_push_enabled(cls, user_pref, push_type) -> bool:
        if push_type is EmailPush.PushType.SYSTEM:
            announcement_enabled = user_pref.allows_announcement_emails
            activity_enabled = user_pref.allows_activity_emails
            blog_enabled = user_pref.allows_blog_emails
            return announcement_enabled or activity_enabled or blog_enabled

        if push_type is EmailPush.PushType.ANNOUNCEMENT:
            return user_pref.allows_announcement_emails

        if push_type is EmailPush.PushType.ACTIVITY:
            return user_pref.allows_activity_emails

        if push_type is EmailPush.PushType.BLOG:
            return user_pref.allows_blog_emails

        return True

    @classmethod
    def send_push_email_test(cls, push_id: int, content: str, email: str, lang: str, title: str, is_system=False):
        support_unsubscribe = True
        if is_system:
            support_unsubscribe = False
        subject = title if title.startswith('【CoinEx】') else f'【CoinEx】{title}'
        EmailSender.send_from_template(
            'notice',
            'announcement_notice',
            dict(
                site_url=config['SITE_URL'],
                title=title,
                content=content,
                manager_id=push_id,
                unsubscribe_uri='TOKEN',
                user_token='TOKEN',
                support_unsubscribe=support_unsubscribe
            ),
            email,
            lang,
            subject=subject,
        )


@ns.route('/email-push-unsubscriptions')
@respond_with_code
class EmailPushUnsubscriptionsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-邮件推送-获取邮件退订列表"""
        page = kwargs['page']
        limit = kwargs['limit']

        records = EmailPushUnsubscription.query \
            .filter(EmailPushUnsubscription.status
                    == EmailPushUnsubscription.Status.FINISHED) \
            .order_by(EmailPushUnsubscription.finished_at.desc()) \
            .paginate(page, limit)

        user_id_to_emails = dict(
            User.query
            .filter(User.id.in_({r.user_id for r in records.items}))
            .with_entities(User.id, User.email)
        )

        unsubs = []
        row: EmailPushUnsubscription
        for row in records.items:
            r_dict = row_to_dict(row, enum_to_name=True)
            r_dict.pop('token')
            r_dict['user_email'] = user_id_to_emails.get(row.user_id, '')
            unsubs.append(r_dict)

        return dict(
            total=records.total,
            items=unsubs
        )


@ns.route('/email-push/filter-configs')
@respond_with_code
class EmailPushFilterConfigsResource(Resource):

    @classmethod
    def get(cls, **kwargs):
        """运营-邮件推送-筛选用户(仅用作历史记录展示)"""

        def format_expr(_expr: ExpressionConfig):
            ops.update({
                _o.value: dict(
                    operator_type=_expr.operator_type.value,
                    value_type=_expr.value_type.__name__,
                    value_options=_expr.value_options,
                    key_options=_expr.key_options
                ) for _o in _expr.operators
            })

        options = {}
        for fil_type, fil_exprs in FILTER_CONFIGS.items():
            ops = {}
            exhaust(map(format_expr, fil_exprs))
            options[fil_type.name] = dict(
                desc=fil_type.value,
                operators=ops
            )
        return options


@ns.route('/email-push/template')
@respond_with_code
class EmailPushTemplateResource(Resource):

    @classmethod
    def get(cls):
        headers = ["id", "email"]
        stream = ExcelExporter(
            data_list=[],
            headers=headers
        ).export_streams()
        return send_file(
            stream,
            download_name='推送导入模板.xlsx',
            as_attachment=True
        )


@ns.route('/email-push/imports')
@respond_with_code
class EmailPushImportsResource(Resource):

    @classmethod
    def post(cls):
        """运营-导入用户"""
        if not (file := request.files.get('file')):
            raise InvalidArgument
        rows = get_table_rows(file, ['id', "email"])
        items = set()
        ids = set()
        emails = set()
        for row in rows:
            if row["id"] and isinstance(row["id"], str) and row["id"].strip():
                ids.add(int(row["id"]))
            elif row["id"] and isinstance(row["id"], int):
                ids.add(row["id"])
            elif row["email"] and isinstance(row["email"], str) and row["email"].strip():
                emails.add(row["email"])
            else:
                raise InvalidArgument(message=f"用户{row}不存在")

        for _ids in batch_iter(ids, 1000):
            ret = User.query.filter(User.id.in_(_ids)).with_entities(User.id).all()
            ret = {x for x, in ret}
            if len(ret) != len(_ids):
                row = (set(_ids) - ret).pop()
                raise InvalidArgument(message=f"用户{row}不存在")
            items |= ret

        for _emails in batch_iter(emails, 1000):
            _lower_emails = {_email.lower() for _email in _emails}
            result = User.query.filter(User.email.in_(_lower_emails)).with_entities(User.id, User.email).all()
            ret = {x.lower() for _, x in result}
            if len(ret) != len(_lower_emails):
                row = (set(_lower_emails) - ret).pop()
                raise InvalidArgument(message=f"用户{row}不存在")
            items |= {x for x, _ in result}

        return dict(
            total=len(items),
            items=items
        )


def _check_asset_markets(keys: List[str],
                         type_: AssetMarketOfflineEmailPush.Type):
    types = AssetMarketOfflineEmailPush.Type
    if type_ in (types.ASSET, types.PLEDGE_ASSET, types.PLEDGE_LOAN_ASSET):
        for k in keys:
            try:
                AssetField().deserialize(k)
            except Exception:
                raise InvalidArgument
    elif type_ == types.PERPETUAL_MARKET:
        for k in keys:
            try:
                PerpetualMarketField().deserialize(k)
            except Exception:
                raise InvalidArgument
    elif type_ == types.MARGIN_MARKET:
        markets = MarginAccountIdCache.list_all_markets().values()
        if not set(markets).issuperset(set(keys)):
            raise InvalidArgument
    else:
        markets = AmmMarketCache.list_amm_markets()
        if not set(markets).issuperset(set(keys)):
            raise InvalidArgument


@ns.route('/asset-offline-push')
@respond_with_code
class AssetOfflineEmailPushResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String,
        status=EnumField(EmailPush.Status),
        keyword=fields.String,
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-触达管理-下架资产邮件推送"""
        params = Struct(**kwargs)
        query = AssetMarketOfflineEmailPush.query.order_by(AssetMarketOfflineEmailPush.id.desc())
        if params.status or params.name or params.keyword:
            query = query.join(
                EmailPush,
                EmailPush.id == AssetMarketOfflineEmailPush.push_id
            )
            if params.name:
                query = query.filter(EmailPush.name.contains(params.name))
            if params.status:
                query = query.filter(EmailPush.status == params.status)
            if params.keyword:
                user_ids = User.search_for_users(params.keyword)
                query = query.filter(EmailPush.created_by.in_(user_ids))

        records = query.paginate(params.page, params.limit)
        items = records.items
        item_map = {item.push_id: item for item in items}
        push_ids = [r.push_id for r in items]
        pushes = EmailPush.query.filter(EmailPush.id.in_(push_ids)).all()
        pushes.sort(key=lambda x: x.created_at, reverse=True)
        create_user_ids = {item.created_by for item in pushes}
        name_map = get_admin_user_name_map(create_user_ids)
        res = []
        for row in pushes:
            r_dict = row_to_dict(row, enum_to_name=True)
            del r_dict['user_ids']
            r_dict['created_user_email'] = name_map.get(row.created_by) or '-'
            r_dict['user_read_rate'] = row.user_read_rate(row.user_read_count)
            r_dict['user_unsubscribe_rate'] = row.user_unsubscribe_rate
            r_dict['user_click_rate'] = row.user_click_rate(row.user_click_count)
            can_del = True
            if row.status in (EmailPush.Status.PROCESSING,
                              EmailPush.Status.FINISHED,
                              EmailPush.Status.DELETED):
                can_del = False
            r_dict['can_del'] = can_del
            item = item_map[row.id]
            r_dict['push_id'] = r_dict['id']
            r_dict['id'] = item.id
            r_dict['started_at'] = item.started_at
            r_dict['ended_at'] = item.ended_at
            res.append(r_dict)

        return dict(
            total=records.total,
            items=res,
            extra=dict(
                push_types=EmailPush.PushType,
                statuses=EmailPush.Status
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        push_scope_type=EnumField(EmailPush.PushScopeType, missing=EmailPush.PushScopeType.EXCLUDE_UNSUBSCRIBE),
        remark=fields.String(required=False),
        whitelist_enabled=fields.Boolean(missing=False),
        user_whitelist=fields.String(required=False),
        url_ids=fields.List(fields.Integer, required=False),
        status=EnumField(EmailPush.Status),
        key=fields.List(fields.String, required=True),
        type=EnumField(AssetMarketOfflineEmailPush.Type, required=True),
        started_at=TimestampField(required=True),
        ended_at=TimestampField(required=True),
    ))
    def post(cls, **kwargs):
        """运营-触达管理-下架资产邮件推送-创建"""
        status = kwargs.get('status')
        if status not in (EmailPush.Status.DRAFT, EmailPush.Status.CREATED):
            raise OperationDenied(message=f'invalid status {status!r}')
        _check_asset_markets(kwargs['key'], kwargs['type'])
        user_whitelist = kwargs.pop('user_whitelist', None) or ''
        url_ids = kwargs.pop('url_ids', None) or ''
        if url_ids:
            url_ids = cls.filter_url_ids(url_ids)
            url_ids = json.dumps(url_ids) if url_ids else ''

        params = dict(kwargs)
        for k in ('key', 'type', 'started_at', 'ended_at'):
            del params[k]
        email_push = EmailPush(
            created_by=g.user.id,
            user_whitelist=user_whitelist,
            urls=url_ids,
            push_type=EmailPush.PushType.SYSTEM,
            push_time=max(now(), kwargs['started_at']),
            push_time_type=EmailPush.PushTimeType.TIMING,
            user_type=EmailPush.UserType.TARGET_USER,
            type=EmailPush.Type.OFFLINE_ASSET,
            **params
        )
        db.session_add_and_commit(email_push)
        record = AssetMarketOfflineEmailPush(
            push_id=email_push.id,
            key=json.dumps(kwargs['key']),
            type=kwargs['type'],
            started_at=kwargs['started_at'],
            ended_at=kwargs['ended_at']
        )
        db.session_add_and_commit(record)
        update_offline_asset_market_push_status.delay(record.id, refresh=True, force=True)
        res = email_push.to_dict(enum_to_name=True)

        res.update(record.to_dict(enum_to_name=True))
        del res['user_ids']

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetOfflineEmailPush,
            detail=res,
        )
        return res


@ns.route('/asset-offline-push/<int:id_>')
@respond_with_code
class AssetOfflineEmailPushDetailResource(EmailPushMixin, Resource):

    @classmethod
    def get(cls, id_):
        """运营-触达管理-下架资产邮件推送-详情"""
        if not id_:
            return dict(types=AssetMarketOfflineEmailPush.Type, details={})
        record = AssetMarketOfflineEmailPush.query.get(id_)
        details = record.to_dict(enum_to_name=True)
        del details['user_ids']
        return dict(
            details=details,
            types=AssetMarketOfflineEmailPush.Type
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        push_scope_type=EnumField(EmailPush.PushScopeType, missing=EmailPush.PushScopeType.EXCLUDE_UNSUBSCRIBE),
        started_at=TimestampField(required=True),
        ended_at=TimestampField(required=True),
        key=fields.List(fields.String, required=True),
        type=EnumField(AssetMarketOfflineEmailPush.Type, required=True),
        remark=fields.String(required=False),
        whitelist_enabled=fields.Boolean(missing=False),
        user_whitelist=fields.String(required=False),
        url_ids=fields.List(fields.Integer, required=False),
        status=EnumField(EmailPush.Status),
    ))
    def patch(cls, id_, **kwargs):
        """运营-触达管理-下架资产邮件推送-编辑"""

        record = AssetMarketOfflineEmailPush.query.get(id_)
        if not record:
            raise InvalidArgument
        old_data = record.to_dict(enum_to_name=True)
        _check_asset_markets(kwargs['key'], kwargs['type'])
        record.started_at = kwargs['started_at']
        record.ended_at = kwargs['ended_at']
        record.key = json.dumps(kwargs['key'])
        record.type = kwargs['type']
        row = EmailPush.query.get(record.push_id)
        status = kwargs.get('status')
        if status not in (EmailPush.Status.DRAFT, EmailPush.Status.CREATED):
            raise OperationDenied(message=f'invalid status {status!r}')
        if row.status in (EmailPush.Status.PROCESSING, EmailPush.Status.FINISHED):
            raise OperationDenied(message='cannot edit a processing/finished item')
        old_data.update(row.to_dict(enum_to_name=True))
        del old_data['user_ids']

        user_whitelist = kwargs.pop('user_whitelist', None) or ''
        url_ids = kwargs.pop('url_ids', None) or ''
        if url_ids:
            url_ids = cls.filter_url_ids(url_ids)
            url_ids = json.dumps(url_ids) if url_ids else ''

        row.name = kwargs['name']
        row.push_scope_type = kwargs['push_scope_type']
        row.remark = kwargs['remark']
        row.whitelist_enabled = kwargs['whitelist_enabled']
        row.user_whitelist = user_whitelist
        row.urls = url_ids
        row.status = kwargs['status']
        row.type = EmailPush.Type.OFFLINE_ASSET
        row.updated_at = now()
        db.session.commit()

        update_offline_asset_market_push_status.delay(record.id, refresh=True)
        res = row.to_dict(enum_to_name=True)
        res.update(record.to_dict(enum_to_name=True))
        del res['user_ids']

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetOfflineEmailPush,
            old_data=old_data,
            new_data=res,
        )
        return res


@ns.route('/asset-offline-push/<int:id_>/user-download')
@respond_with_code
class AssetOfflineEmailPushUserResource(Resource):

    @classmethod
    def get(cls, id_):
        """运营-触达管理-下架资产邮件推送-用户下载"""
        row = AssetMarketOfflineEmailPush.query.get(id_)
        if row is None:
            raise RecordNotFound

        user_ids = row.cached_user_ids
        data = []
        for ids in batch_iter(user_ids, 1000):
            tmp = User.query.filter(
                User.id.in_(ids)
            ).with_entities(User.id, User.email).all()
            data.extend([dict(id=item.id, email=item.email) for item in tmp])
        _fields = headers = ["id", "email"]
        stream = ExcelExporter(
            data_list=data,
            fields=_fields,
            headers=headers
        ).export_streams()
        return send_file(
            stream,
            download_name=f'推送{row.id}客群信息.xlsx',
            as_attachment=True
        )


@ns.route("/email-push/batch-upload")
@respond_with_code
class EmailPushUploadResource(Resource):
    class Business(Enum):
        RISK_SCREEN = '风险筛查'

    @classmethod
    def post(cls):
        """运营-触达管理-邮件推送-上传文件生成邮件记录"""
        if not (title := request.form.get('title')):
            raise InvalidArgument
        if not (business := request.form.get('business')):
            raise InvalidArgument
        business = cls.Business[business]
        file_ = request.files.get('batch-upload')
        file_columns = ["email", "from_year", "to_year", "date"]
        try:
            rows = get_table_rows(file_, file_columns, parse_str=True)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)
        row_mapping = {row['email'].lower().strip(): row for row in rows if row['email'].strip()}
        # 目前一批最多上千个
        user_emails = cls._get_user_emails(emails=set(row_mapping.keys()))
        template_kwargs = {}
        for email, kw in row_mapping.items():
            user_id = user_emails.get(email)
            if not user_id:
                continue
            template_kwargs[user_id] = kw
        if not template_kwargs:
            raise InvalidArgument(message='上传文件未匹配到有效用户，请检查！')
        ret = {
            'business': business.name,
            'template_kwargs': template_kwargs
        }
        cls._save_email_push(
            system_template_kwargs=ret,
            title=title,
            business=business,
            user_ids=list(template_kwargs.keys()),
        )
        return dict(count=len(template_kwargs))

    @classmethod
    def _get_user_emails(cls, emails) -> dict[str, int]:
        model = User
        rows = model.query.with_entities(
            model.email,
            model.id,
        ).filter(
            model.email.in_(emails)
        ).all()
        return dict(rows)

    @classmethod
    def _save_email_push(cls, system_template_kwargs, title, business, user_ids: list):
        """这里是按 system_template_kwargs 参数来指定模板发送，所以不需要创建多语言内容"""
        name = title or business.value
        model = UserTagGroup
        row = model(
            name=name,
            group_type=model.GroupType.IMPORT,
            rules='',
            remark='系统创建请勿修改',
        )
        row.set_user_ids(user_ids)
        row.user_count = len(user_ids)
        row.calc_status = model.CalcStatus.FINISHED
        row.last_updated_at = now()
        db.session.add(row)
        db.session.flush()
        group_id = row.id
        data = dict(
            name=name,
            push_type=EmailPush.PushType.SYSTEM,
            push_time_type=EmailPush.PushTimeType.REALTIME,
            push_scope_type=EmailPush.PushScopeType.INCLUDE_UNSUBSCRIBE,  # 这里需要包含退订用户
            push_time=now(),
            remark='系统创建请勿修改',
            user_type=EmailPush.UserType.TARGET_USER,
            groups=json.dumps([group_id]),
            status=EmailPush.Status.AUDITED,
            system_template_kwargs=json.dumps(system_template_kwargs, cls=JsonEncoder)
        )
        row = EmailPush(**data)
        row.user_count = len(user_ids)
        row.subscribe_count = len(user_ids)
        row.set_user_ids(user_ids)
        db.session.add(row)
        db.session.commit()
        return row


@ns.route('/coin-application-list')
@respond_with_code
class CoinApplicationListResource(Resource):

    @classmethod
    def _list_attachments(cls, id_):
        records = CoinApplicationFiles.query.filter(
            CoinApplicationFiles.coin_listing_application_id == id_,
            CoinApplicationFiles.status == CoinApplicationFiles.Status.VALID
        ).all()
        return [dict(
            id=r.file_id,
            name=r.name,
            file_type=r.file_type.name,
            file_url=File.query.get(r.file_id).static_url
        ) for r in records]

    class AttachmentsField(fx_fields.Raw):

        def format(self, value):
            return CoinApplicationListResource._list_attachments(value)

    marshal_fields = {
        'id': fx_fields.Integer,
        'code': fx_fields.String,
        'name': fx_fields.String,
        'icon': fx_fields.Nested(dict(
            file_key=fx_fields.String(attribute='icon'),
            file_url=IconField(attribute='icon'),
        ), attribute=lambda x: x),
        'type': EnumMarshalField(
            CoinListingApplication.Type, output_field_lower=False),
        'issued_date': DateMarshalField,
        'issued_price_data': JsonMarshalField(default=[]),
        'total_supply': fx_fields.String,
        'total_circulate': fx_fields.String,
        'usd_circulate': fx_fields.String,
        'official_website': fx_fields.String,
        'white_paper': fx_fields.String,
        'source_code': fx_fields.String,
        'explorer': fx_fields.String,
        'intro_en': fx_fields.String,
        'intro_cn': fx_fields.String,
        'fundraising': fx_fields.String,
        'token_distribution': fx_fields.String,
        'telegram': fx_fields.String,
        'facebook': fx_fields.String,
        'twitter': fx_fields.String,
        'reddit': fx_fields.String,
        'medium': fx_fields.String,
        'discord': fx_fields.String,
        'linkedin_or_cv': fx_fields.String,
        'applicant_name': fx_fields.String,
        'applicant_role': fx_fields.String,
        'applicant_email': fx_fields.String,
        'applicant_mobile': fx_fields.String,
        'is_first': fx_fields.Boolean,
        'is_securities': fx_fields.Boolean,
        'status': EnumMarshalField(
            CoinListingApplication.Status, output_field_lower=False),
        'user_id': fx_fields.Integer(attribute='created_by'),
        'attachments': AttachmentsField(attribute=lambda x: x.id),
        'created_at': TimestampMarshalField,
        'updated_at': TimestampMarshalField,
    }

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(CoinListingApplication.Type),
        status=EnumField(CoinListingApplication.Status),
        is_first=BoolField,
        id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-币种申请-币种申请列表"""
        query = CoinListingApplication.query
        if type_ := kwargs.get('type'):
            query = query.filter(CoinListingApplication.type == type_)
        if status := kwargs.get('status'):
            query = query.filter(CoinListingApplication.status == status)
        if (is_first := kwargs.get('is_first')) is not None:
            query = query.filter(CoinListingApplication.is_first == is_first)
        if coin_id := kwargs.get('id'):
            query = query.filter(CoinListingApplication.id == coin_id)
        query = query.order_by(
            CoinListingApplication.id.desc()
        )
        return query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)

    @classmethod
    @ns.use_kwargs(dict(
        ids=fields.String(required=True),
        status=EnumField([
            CoinListingApplication.Status.REJECTED.name,
            CoinListingApplication.Status.AUDITED.name,
        ], required=True)
    ))
    def patch(cls, **kwargs):
        """运营-币种申请-批量审核"""
        ids_str = kwargs["ids"]
        new_status = CoinListingApplication.Status[kwargs['status']]
        _ids = list(map(int, ids_str.split(',')))
        for _id in _ids:
            obj = CoinListingApplication.query.get(_id)
            old_status = obj.status
            if old_status != CoinListingApplication.Status.CREATED:
                # 只有申请中的才能批量审核
                continue
            obj.updated_by = g.user.id
            obj.status = new_status
            db.session.commit()
            if old_status == CoinListingApplication.Status.CREATED \
                    and new_status == CoinListingApplication.Status.REJECTED:
                send_coin_application_rejected_notice_email.delay(
                    obj.created_by,
                    obj.applicant_email,
                    obj.name
                )
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CoinApplication,
            special_data=dict(
                ids=_ids,
                new_status=new_status.name,
                op='批量审核',
            ),
        )


@ns.route('/coin-application')
@respond_with_code
class CoinApplicationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        keyword=fields.Raw(required=True)
    ))
    def get(cls, **kwargs):
        """运营-币种申请-币种申请搜索"""
        keyword = kwargs['keyword']
        records = CoinListingApplication.query.filter(
            or_(
                CoinListingApplication.id == keyword,
                CoinListingApplication.code.contains(keyword),
                CoinListingApplication.name.contains(keyword),
            )
        ).with_entities(
            CoinListingApplication.id,
            CoinListingApplication.code,
            CoinListingApplication.name
        ).all()
        return [dict(id=r.id, code=r.code, name=r.name) for r in records]


# noinspection PyUnresolvedReferences
@ns.route('/coin-application/<int:id_>')
@respond_with_code
class CoinApplicationDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(CoinListingApplication.Status, required=True)
    ))
    def patch(cls, id_, **kwargs):
        """运营-币种申请-修改币种申请状态"""
        c: CoinListingApplication = CoinListingApplication.query.get(id_)
        if not c:
            raise InvalidArgument
        old_data = c.to_dict(enum_to_name=True)
        c.updated_by = g.user.id
        old_status = c.status
        new_status = kwargs['status']
        c.status = new_status
        db.session.commit()
        if old_status == CoinListingApplication.Status.CREATED \
                and new_status == CoinListingApplication.Status.REJECTED:
            send_coin_application_rejected_notice_email.delay(
                c.created_by,
                c.applicant_email,
                c.name
            )
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CoinApplication,
            old_data=old_data,
            new_data=c.to_dict(enum_to_name=True),
        )


@ns.route('/ieo-application-list')
@respond_with_code
class IeoApplicationListResource(Resource):

    @classmethod
    def _list_attachments(cls, id_):
        records = IeoApplicationFiles.query.filter(
            IeoApplicationFiles.ieo_listing_application_id == id_,
            IeoApplicationFiles.status == IeoApplicationFiles.Status.VALID
        ).all()
        return [dict(
            id=r.file_id,
            name=r.name,
            file_type=r.file_type.name,
            file_url=File.query.get(r.file_id).static_url
        ) for r in records]

    class AttachmentsField(fx_fields.Raw):

        def format(self, value):
            return IeoApplicationListResource._list_attachments(value)

    marshal_fields = {
        'id': fx_fields.Integer,
        'code': fx_fields.String,
        'name': fx_fields.String,
        'icon': fx_fields.Nested(dict(
            file_key=fx_fields.String(attribute='icon'),
            file_url=IconField(attribute='icon'),
        ), attribute=lambda x: x),
        'type': EnumMarshalField(
            IeoListingApplication.Type, output_field_lower=False),
        'issued_date': DateMarshalField,
        'total_supply': fx_fields.String,
        'total_circulate': fx_fields.String,
        'usd_circulate': fx_fields.String,
        'official_website': fx_fields.String,
        'white_paper': fx_fields.String,
        'source_code': fx_fields.String,
        'explorer': fx_fields.String,
        'intro_en': fx_fields.String,
        'intro_cn': fx_fields.String,
        'fundraising': fx_fields.String,
        'token_distribution': fx_fields.String,
        'telegram': fx_fields.String,
        'facebook': fx_fields.String,
        'twitter': fx_fields.String,
        'reddit': fx_fields.String,
        'medium': fx_fields.String,
        'discord': fx_fields.String,
        'linkedin_or_cv': fx_fields.String,
        'applicant_name': fx_fields.String,
        'applicant_role': fx_fields.String,
        'applicant_email': fx_fields.String,
        'applicant_telegram': fx_fields.String,
        'is_first': fx_fields.Boolean,
        'is_securities': fx_fields.Boolean,
        'status': EnumMarshalField(
            IeoListingApplication.Status, output_field_lower=False),
        'user_id': fx_fields.Integer(attribute='created_by'),
        'attachments': AttachmentsField(attribute=lambda x: x.id),
        'created_at': TimestampMarshalField,
        'updated_at': TimestampMarshalField,
        'issued_price_data': JsonMarshalField(default=[]),
        'avg_price_data': JsonMarshalField(default=[]),
        'discount': fx_fields.String,
        'is_locked': fx_fields.String,
        'unlock_condition': fx_fields.String,
        'product_url': fx_fields.String,
        'test_code': fx_fields.String,
        'support_chain_url': fx_fields.String,
    }

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(IeoListingApplication.Type),
        status=EnumField(IeoListingApplication.Status),
        is_first=BoolField,
        id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-币种申请-融资申请列表"""
        query = IeoListingApplication.query
        if type_ := kwargs.get('type'):
            query = query.filter(IeoListingApplication.type == type_)
        if status := kwargs.get('status'):
            query = query.filter(IeoListingApplication.status == status)
        if (is_first := kwargs.get('is_first')) is not None:
            query = query.filter(IeoListingApplication.is_first == is_first)
        if coin_id := kwargs.get('id'):
            query = query.filter(IeoListingApplication.id == coin_id)
        query = query.order_by(
            IeoListingApplication.id.desc()
        )
        return query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)


@ns.route('/ieo-application-list/<int:id_>')
@respond_with_code
class IeoApplicationDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(IeoListingApplication.Status, required=True)
    ))
    def patch(cls, id_, **kwargs):
        """运营-融资申请-修改融资申请状态"""
        c: IeoListingApplication = IeoListingApplication.query.get(id_)
        if not c:
            raise InvalidArgument
        old_data = c.to_dict(enum_to_name=True)
        c.updated_by = g.user.id
        old_status = c.status
        new_status = kwargs['status']
        c.status = new_status
        db.session.commit()
        if old_status == IeoListingApplication.Status.CREATED \
                and new_status == IeoListingApplication.Status.REJECTED:
            send_ieo_application_rejected_notice_email(
                c.created_by,
                c.applicant_email,
                c.name
            )
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CoinApplicationIEO,
            old_data=old_data,
            new_data=c.to_dict(enum_to_name=True),
        )


@ns.route('/ieo-application')
@respond_with_code
class IeoApplicationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        keyword=fields.Raw(required=True)
    ))
    def get(cls, **kwargs):
        """运营-融资申请-融资申请搜索"""
        keyword = kwargs['keyword']
        records = IeoListingApplication.query.filter(
            or_(
                IeoListingApplication.id == keyword,
                IeoListingApplication.code.contains(keyword),
                IeoListingApplication.name.contains(keyword),
            )
        ).with_entities(
            IeoListingApplication.id,
            IeoListingApplication.code,
            IeoListingApplication.name
        ).all()
        return [dict(id=r.id, code=r.code, name=r.name) for r in records]


@ns.route('/c-box/themes')
@respond_with_code
class CBoxThemesResource(Resource):
    GENERAL_LEFT_SIDE_LANG = 'EN_US'
    GENERAL_RIGHT_SIDE_LANG = 'AR_AE'

    @classmethod
    @ns.use_kwargs(
        dict(
            category_id=fields.Integer(required=True)
        )
    )
    def get(cls, **kwargs):
        """运营-cbox-主题列表"""
        category_id = kwargs['category_id']
        languages = language_name_cn_names()
        extra = {'content_languages': languages, 'all_languages': languages}
        if category_id == 0:
            return dict(extra=extra)
        category = CBoxThemeCategory.query.get(category_id)
        if not category:
            raise InvalidArgument(message='主题不存在！')

        res = category.to_dict(enum_to_name=True)
        langs = json.loads(res['languages'])
        res['languages'] = langs
        res['display_status'] = CBoxThemeCategoryResource.get_display_status(category)
        if langs:
            content_languages = extra['content_languages']
            extra['content_languages'] = {lang: cn_name for lang, cn_name in content_languages.items()
                                          if lang in res['languages']}
        res['extra'] = extra

        records = CBoxTheme.query.filter(
            CBoxTheme.status == CBoxTheme.Status.VALID,
            CBoxTheme.category_id == category_id
        ).all()
        if res['type'] == CBoxThemeCategory.Type.GENERAL.name:
            if not records:
                res['left_side_lang_cover_img_src'] = None
                res['right_side_lang_cover_img_src'] = None
                res['left_side_lang_thumbnail_img_src'] = None
                res['right_side_lang_thumbnail_img_src'] = None
                res['left_side_lang_receive_img_src'] = None
                res['right_side_lang_receive_img_src'] = None
                res['general_style'] = None
            else:
                lang_ret = dict()
                for record in records:
                    item = record.to_dict(enum_to_name=True)
                    item['cover_img_src'] = CBoxTheme.img_src(record.cover_file_key)
                    item['thumbnail_img_src'] = CBoxTheme.img_src(record.thumbnail_file_key)
                    item['receive_img_src'] = CBoxTheme.img_src(record.receive_file_key)
                    lang_ret[item['lang']] = item
                res['left_side_lang_cover_img_src'] = lang_ret[cls.GENERAL_LEFT_SIDE_LANG]['cover_img_src']
                res['left_side_lang_cover_file_key'] = lang_ret[cls.GENERAL_LEFT_SIDE_LANG]['cover_file_key']
                res['right_side_lang_cover_img_src'] = lang_ret[cls.GENERAL_RIGHT_SIDE_LANG][
                    'cover_img_src']
                res['right_side_lang_cover_file_key'] = lang_ret[cls.GENERAL_RIGHT_SIDE_LANG][
                    'cover_file_key']
                res['left_side_lang_thumbnail_img_src'] = lang_ret[cls.GENERAL_LEFT_SIDE_LANG][
                    'thumbnail_img_src']
                res['left_side_lang_thumbnail_file_key'] = lang_ret[cls.GENERAL_LEFT_SIDE_LANG][
                    'thumbnail_file_key']
                res['right_side_lang_thumbnail_img_src'] = lang_ret[cls.GENERAL_RIGHT_SIDE_LANG][
                    'thumbnail_img_src']
                res['right_side_lang_thumbnail_file_key'] = lang_ret[cls.GENERAL_RIGHT_SIDE_LANG][
                    'thumbnail_file_key']

                res['left_side_lang_receive_img_src'] = lang_ret[cls.GENERAL_LEFT_SIDE_LANG][
                    'receive_img_src']
                res['left_side_lang_receive_file_key'] = lang_ret[cls.GENERAL_LEFT_SIDE_LANG][
                    'receive_file_key']
                res['right_side_lang_receive_img_src'] = lang_ret[cls.GENERAL_RIGHT_SIDE_LANG][
                    'receive_img_src']
                res['right_side_lang_receive_file_key'] = lang_ret[cls.GENERAL_RIGHT_SIDE_LANG][
                    'receive_file_key']

                res['general_style'] = lang_ret[cls.GENERAL_LEFT_SIDE_LANG]['style']
        else:

            lang_ret = dict()

            if not records:
                for lang in langs:
                    lang_ret[lang] = dict(
                        cover_img_src=None,
                        cover_file_key=None,
                        thumbnail_img_src=None,
                        thumbnail_file_key=None,
                        receive_img_src=None,
                        receive_file_key=None,
                        lang=lang,
                        style=None,
                    )
            else:
                record_dic = {rec.lang.name: rec for rec in records}
                for lang in langs:
                    record = record_dic[lang]
                    item = record.to_dict(enum_to_name=True)
                    item['cover_img_src'] = CBoxTheme.img_src(record.cover_file_key)
                    item['thumbnail_img_src'] = CBoxTheme.img_src(record.thumbnail_file_key)
                    item['receive_img_src'] = CBoxTheme.img_src(record.receive_file_key)
                    lang_ret[lang] = item
            res['lang_items'] = lang_ret
        return res

    @classmethod
    @ns.use_kwargs(dict(
        cover_file_key=fields.String(required=True),
        thumbnail_file_key=fields.String(required=True),
        receive_file_key=fields.String(required=True),
        style=EnumField(CBoxTheme.Style, required=True),
        lang=EnumField(Language, required=True),
        category_id=fields.Integer(required=True),
    ))
    def post(cls, **kwargs):
        """运营-cbox-新增主题"""
        category_id = kwargs['category_id']
        category = CBoxThemeCategory.query.get(category_id)
        if not category:
            raise InvalidArgument(message='主题不存在！')
        theme = CBoxTheme.query.filter(
            CBoxTheme.category_id == category_id,
            CBoxTheme.lang == kwargs['lang']
        ).first()
        if theme:
            theme.cover_file_key = kwargs['cover_file_key']
            theme.thumbnail_file_key = kwargs['thumbnail_file_key']
            theme.receive_file_key = kwargs['receive_file_key']
            theme.style = kwargs['style']
            theme.status = CBoxTheme.Status.VALID
        else:
            theme = CBoxTheme(
                cover_file_key=kwargs['cover_file_key'],
                thumbnail_file_key=kwargs['thumbnail_file_key'],
                receive_file_key=kwargs['receive_file_key'],
                lang=kwargs['lang'],
                style=kwargs['style'],
                category_id=category_id
            )
            db.session.add(theme)
        db.session.commit()
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxTheme,
            detail=kwargs,
        )
        CBoxThemeCache.reload()


@ns.route('/c-box/theme/<int:id_>')
@respond_with_code
class CBoxThemeResource(Resource):

    @classmethod
    def delete(cls, id_):
        """运营-cbox-删除主题"""
        theme = CBoxTheme.query.get(id_)
        if not theme:
            raise RecordNotFound
        theme.status = CBoxTheme.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxTheme,
            detail=dict(id=id_, category_id=theme.category_id, lang=theme.lang),
        )
        CBoxThemeCache.reload()


@ns.route('/c-box-theme-category')
@respond_with_code
class CBoxThemeCategoryResource(Resource):
    model = CBoxThemeCategory

    class DisplayStatus(Enum):
        DRAFT = '待上架'
        OFFLINE = '已下架'
        PENDING = '待展示'
        RUNNING = '展示中'

    deserialize_status_mapping = {
        DisplayStatus.DRAFT: CBoxThemeCategory.Status.OFFLINE,
        DisplayStatus.PENDING: CBoxThemeCategory.Status.ONLINE,
        DisplayStatus.RUNNING: CBoxThemeCategory.Status.ONLINE,
        DisplayStatus.OFFLINE: CBoxThemeCategory.Status.OFFLINE,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            category_id=fields.Integer(required=True),
            title=fields.String(required=True),
            begin_at=fields.DateTime(required=True),
            end_at=fields.DateTime(required=True),
            type=EnumField(CBoxThemeCategory.Type, required=True),
            languages=fields.List(fields.String(), required=True),
        )
    )
    def put(cls, **kwargs):
        """运营-cbox-主题类型编辑"""
        params = Struct(**kwargs)
        category_id, title = params.category_id, params.title
        if params.type == cls.model.Type.GENERAL:
            languages = compact_json_dumps([])
        else:
            languages = compact_json_dumps(params.languages)
        if category_id != 0:
            category = cls.model.query.get(category_id)
            if not category:
                raise InvalidArgument(message='不存在的主题！')
            old_data = category.to_dict(enum_to_name=True)
            category.title = params.title
            category.begin_at = params.begin_at
            category.end_at = params.end_at
            category.type = params.type
            category.languages = languages
            cls.handle_themes(params, old_data)
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.CBoxTheme,
                old_data=old_data,
                new_data=category.to_dict(enum_to_name=True),
            )
        else:
            largest_rank_rec = cls.model.query.with_entities(
                cls.model.rank).order_by(cls.model.rank.desc()).first()
            largest_rank = largest_rank_rec.rank if largest_rank_rec else 0
            category = cls.model(
                rank=largest_rank + 1,
                title=params.title,
                begin_at=params.begin_at,
                end_at=params.end_at,
                type=params.type,
                languages=languages,
                status=cls.model.Status.OFFLINE
            )
            db.session.add(category)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.CBoxTheme,
                detail=dict(title=title),
            )
        db.session.commit()
        CBoxThemeCache.reload()
        return category.to_dict(enum_to_name=True)

    @classmethod
    def handle_themes(cls, params, old_data):
        new_type, old_type = params.type, old_data['type']
        new_langs, old_langs = set(params.languages), set(json.loads(old_data['languages']))
        if old_type == new_type.name:
            if new_type == cls.model.Type.GENERAL:
                return
            del_langs = old_langs - new_langs
            if not del_langs:
                return
            CBoxTheme.query.filter(
                CBoxTheme.category_id == params.category_id,
                CBoxTheme.lang.in_(del_langs)
            ).update({'status': CBoxTheme.Status.DELETED}, synchronize_session=False)
            db.session.commit()
        else:
            CBoxTheme.query.filter(
                CBoxTheme.category_id == params.category_id,
            ).update({'status': CBoxTheme.Status.DELETED}, synchronize_session=False)
            db.session.commit()

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
        title=fields.String,
        languages=fields.String,
    ))
    def get(cls, **kwargs):
        """运营-cbox-主题类型列表"""
        model = cls.model
        params = Struct(**kwargs)
        query = model.query.filter(
            model.status != model.Status.DELETED,
        ).order_by(model.rank.desc())
        if title := params.title:
            query = query.filter(model.title == title)
        titles = model.query.filter(
            model.status != model.Status.DELETED
        ).order_by(
            model.id.desc()
        ).with_entities(
            model.title
        ).all()
        theme_titles = [i.title for i in titles]
        languages = language_name_cn_names()
        languages['全部'] = '全部'
        langs = set(json.loads(params.languages)) if params.languages else []
        if langs:
            ret = query.all()
            records = []
            for row in ret:
                r_langs = set(json.loads(row.languages))
                if row.type == model.Type.GENERAL:
                    records.append(row)
                elif r_langs & langs:
                    records.append(row)
            page, limit = params.page, params.limit
            items = records[(page - 1) * limit: page * limit]
            return dict(
                total=len(records),
                items=cls.fmt_items(items, languages),
                extra=dict(
                    theme_titles=theme_titles,
                    languages=languages,
                )
            )
        else:
            ret = query.paginate(params.page, params.limit)
            items = ret.items

            return dict(
                total=ret.total,
                items=cls.fmt_items(items, languages),
                extra=dict(
                    theme_titles=theme_titles,
                    languages=languages,
                )
            )

    @classmethod
    def fmt_items(cls, rows, languages):
        model = CBoxThemeCategory
        res = []
        for row in rows:
            item = row.to_dict()
            if row.type == model.Type.GENERAL:
                item['langs_display'] = '全部'
            else:
                langs = json.loads(row.languages)
                item['langs_display'] = ','.join([languages[i] for i in langs])
            item['display_status'] = cls.get_display_status(row)
            res.append(item)
        return res

    @classmethod
    def get_display_status(cls, row):
        now_ = now()
        begin_at = row.begin_at
        end_at = row.end_at
        if row.status == cls.model.Status.OFFLINE:
            if end_at < now_:
                display_status = cls.DisplayStatus.OFFLINE.value
            else:
                display_status = cls.DisplayStatus.DRAFT.value
        else:
            if now_ < begin_at:
                display_status = cls.DisplayStatus.PENDING.value
            elif begin_at <= now_ <= end_at:
                display_status = cls.DisplayStatus.RUNNING.value
            else:
                display_status = cls.DisplayStatus.OFFLINE.value
        return display_status

    @classmethod
    @ns.use_kwargs(
        dict(
            category_id=fields.Integer(required=True),
            status=EnumField(CBoxThemeCategory.Status, required=True)
        )
    )
    def patch(cls, **kwargs):
        """运营-cbox-下架/上架主题类型"""
        category_id, status = kwargs['category_id'], kwargs['status']
        category = CBoxThemeCategory.query.get(category_id)
        if not category:
            raise InvalidArgument(message='主题不存在！')
        if status == CBoxThemeCategory.Status.ONLINE:
            theme_record = CBoxTheme.query.filter(
                CBoxTheme.category_id == category_id,
                CBoxTheme.status == CBoxTheme.Status.VALID
            ).first()
            if not theme_record:
                raise InvalidArgument(message='您暂未配置主题，请配置后点击上架')
        old_data = category.to_dict(enum_to_name=True)
        category.status = status
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxTheme,
            old_data=old_data,
            new_data=category.to_dict(enum_to_name=True),
            special_data=dict(title=category.title),
        )
        CBoxThemeCache.reload()

    @classmethod
    @ns.use_kwargs(
        dict(
            category_id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """运营-cbox-删除主题类型"""
        category_id = kwargs['category_id']
        row = cls.model.query.get(category_id)
        if not row:
            raise InvalidArgument(message='活动不存在！')
        row.status = cls.model.Status.DELETED
        db.session.commit()
        CBoxThemeCache.reload()


@ns.route('/c-box-theme-category-rank-up/<int:category_id>')
@respond_with_code
class CBoxThemeCategoryRankResource(Resource):

    @classmethod
    def patch(cls, category_id):
        """运营-cbox-上移主题类型排名"""
        category = CBoxThemeCategory.query.filter(
            CBoxThemeCategory.id == category_id,
            CBoxThemeCategory.status != CBoxThemeCategory.Status.DELETED
        ).first()
        if not category:
            raise InvalidArgument(message='主题不存在')
        old_data = category.to_dict(enum_to_name=True)
        upper_rank_category = CBoxThemeCategory.query.filter(
            CBoxThemeCategory.rank > category.rank,
            CBoxThemeCategory.status != CBoxThemeCategory.Status.DELETED
        ).order_by(CBoxThemeCategory.rank.asc()).first()
        if not upper_rank_category:
            return
        upper_rank_category.rank, category.rank = category.rank, upper_rank_category.rank
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxTheme,
            old_data=old_data,
            new_data=category.to_dict(enum_to_name=True),
            special_data=dict(title=category.title),
        )
        CBoxThemeCache.reload()


@ns.route('/c-box/conf/only_new_user')
@respond_with_code
class CBoxConfsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True)
    ))
    def post(cls, **kwargs):
        """运营-cbox-新增配置"""
        user_id = kwargs['user_id']
        user = User.query.get(user_id)
        if user.is_sub_account:
            raise InvalidArgument(message='该配置不能添加子账户！')
        conf = CBoxOnlyNewUserConf.query.filter(
            CBoxOnlyNewUserConf.user_id == user_id
        ).first()
        if not conf:
            db.session_add_and_commit(
                CBoxOnlyNewUserConf(
                    user_id=user_id
                )
            )

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.CBoxOnlyNewUserConfig,
                detail=kwargs,
            )
            return
        if conf.status == CBoxOnlyNewUserConf.Status.VALID:
            raise InvalidArgument(message='该用户已经存在！')
        old_data = conf.to_dict(enum_to_name=True)
        conf.status = CBoxOnlyNewUserConf.Status.VALID
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxOnlyNewUserConfig,
            old_data=old_data,
            new_data=conf.to_dict(enum_to_name=True),
            special_data=dict(user_id=conf.user_id),
        )

        return

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-cbox-查询配置"""
        query = CBoxOnlyNewUserConf.query.filter(
            CBoxOnlyNewUserConf.status == CBoxOnlyNewUserConf.Status.VALID
        ).order_by(CBoxOnlyNewUserConf.id.desc())
        if user_id := kwargs.get('user_id'):
            query = query.filter(CBoxOnlyNewUserConf.user_id == user_id)
        pagination = query.paginate(kwargs['page'], kwargs['limit'])
        items = pagination.items
        user_ids = [i.user_id for i in items]
        id_email_map = UserRepository.get_users_id_email_map(user_ids)
        res = []
        for item in items:
            item = item.to_dict()
            item['email'] = id_email_map[item['user_id']]
            res.append(item)
        return dict(
            curr_page=pagination.page,
            has_next=pagination.has_next,
            per_page=pagination.per_page,
            total_page=pagination.pages,
            data=res,
            total=pagination.total,
        )


@ns.route('/c-box/conf/only_new_user/<int:id_>')
@respond_with_code
class CBoxConfResource(Resource):

    @classmethod
    def delete(cls, id_):
        """运营-cbox-删除配置"""
        conf = CBoxOnlyNewUserConf.query.get(id_)
        if not conf:
            raise InvalidArgument(message='记录不存在或已被删除！')
        conf.status = CBoxOnlyNewUserConf.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxOnlyNewUserConfig,
            detail=dict(user_id=conf.user_id),
        )

        return {}


@ns.route('/c-box/code')
@respond_with_code
class CBoxCodesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        code=fields.String,
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-cbox-获取口令列表"""
        query = CBoxCode.query.filter(
            CBoxCode.status == CBoxCode.Status.CREATED,
            CBoxCode.is_conf.is_(True)
        ).order_by(CBoxCode.id.desc())
        if code := kwargs.get('code'):
            query = query.filter(CBoxCode.code == code.upper())
        pagination = query.paginate(kwargs['page'], kwargs['limit'])
        items = pagination.items
        user_ids = [i.user_id for i in items]
        id_email_map = UserRepository.get_users_id_email_map(user_ids)
        res = []
        for item in items:
            item = item.to_dict()
            item['email'] = id_email_map[item['user_id']]
            res.append(item)
        return dict(
            curr_page=pagination.page,
            has_next=pagination.has_next,
            per_page=pagination.per_page,
            total_page=pagination.pages,
            data=res,
            total=pagination.total,
        )

    @classmethod
    @ns.use_kwargs(dict(
        code=fields.String(required=True, validate=validate_c_box_code,
                           error_messages={"validator_failed": "口令格式不合法！"}),
        user_id=fields.Integer(required=True)
    ))
    def post(cls, **kwargs):
        """运营-cbox-为指定用户新增口令"""
        code, user_id = kwargs['code'].upper(), kwargs['user_id']
        user = User.query.get(user_id)
        if user.is_sub_account:
            raise InvalidArgument(message='该配置禁止添加子账号！')
        c_code = CBoxCode.query.filter(CBoxCode.code == code).first()
        if c_code:
            raise InvalidArgument(message='该口令已被使用或删除，无法新增！')
        db.session_add_and_commit(CBoxCode(
            code=code,
            user_id=user_id,
            is_conf=True
        ))

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxCode,
            detail=kwargs,
        )

        return {}


@ns.route('/c-box/code/<int:id_>')
@respond_with_code
class CBoxCodeResource(Resource):

    @classmethod
    def get(cls, id_):
        """运营-cbox-口令详情"""
        c_code = CBoxCode.query.get(id_)
        if not c_code:
            raise InvalidArgument(message='该口令不存在！')
        return dict(code=c_code.code)

    @classmethod
    def delete(cls, id_):
        """运营-cbox-删除口令"""
        c_code = CBoxCode.query.get(id_)
        if not c_code:
            raise InvalidArgument(message='该口令不存在！')
        c_code.status = CBoxCode.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxCode,
            detail=dict(id=id_, user_id=c_code.user_id, code=c_code.code),
        )

        return {}


@ns.route('/c-box/white-list')
@respond_with_code
class CBoxWhiteListResource(Resource):
    model = RedPacketWhiteListUsers

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=False, allow_none=True)
    ))
    def get(cls, **kwargs):
        """运营-cbox-特殊额度配置列表"""
        query = cls.model.query.filter(
            cls.model.status == cls.model.Status.ACTIVE
        ).order_by(cls.model.id.desc())
        if user_id := kwargs.get('user_id'):
            query = query.filter(cls.model.user_id == user_id)
        user_ids = [i.user_id for i in query]
        id_email_map = UserRepository.get_users_id_email_map(user_ids)
        res = []
        for record in query:
            item = record.to_dict()
            item['email'] = id_email_map[record.user_id]
            res.append(item)
        return res

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        daily_send_amount_limit=PositiveDecimalField(required=True),
        remark=fields.String(required=False, missing='', allow_none=True)
    ))
    def post(cls, **kwargs):
        """运营-cbox-新增特殊额度配置"""
        user = User.query.get(kwargs['user_id'])
        if user.is_sub_account:
            raise InvalidArgument(message='该配置禁止添加子账号！')
        rec = cls.model.query.filter(
            cls.model.user_id == kwargs['user_id'],
        ).first()
        if rec:
            if rec.status == cls.model.Status.ACTIVE:
                raise InvalidArgument(message='请勿重复添加！')
            rec.status = cls.model.Status.ACTIVE
            list(map(lambda item: setattr(rec, item[0], item[1]), kwargs.items()))
        else:
            rec = cls.model(**kwargs)
            db.session.add(rec)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxPacket,
            detail=kwargs,
        )


@ns.route('/c-box/white-list/<int:id_>')
@respond_with_code
class CBoxWhiteListDetailResource(Resource):
    model = RedPacketWhiteListUsers

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        daily_send_amount_limit=PositiveDecimalField(required=True),
        remark=fields.String(required=False, missing='')
    ))
    def patch(cls, id_, **kwargs):
        """运营-cbox-编辑特殊额度配置"""
        user = User.query.get(kwargs['user_id'])
        if user.is_sub_account:
            raise InvalidArgument(message='该配置禁止添加子账号！')
        rec = cls.model.query.get(id_)
        if not rec or rec.status == cls.model.Status.DELETED:
            raise InvalidArgument(message='记录不存在或已被删除！')
        old_data = rec.to_dict(enum_to_name=True)
        list(map(lambda item: setattr(rec, item[0], item[1]), kwargs.items()))
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxPacket,
            old_data=old_data,
            new_data=rec.to_dict(enum_to_name=True),
        )

    @classmethod
    def delete(cls, id_):
        """运营-cbox-删除特殊额度配置"""
        rec = cls.model.query.get(id_)
        if not rec or rec.status == cls.model.Status.DELETED:
            raise InvalidArgument(message='记录不存在或已被删除！')
        rec.status = cls.model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CBoxPacket,
            detail=dict(id=id_, user_id=rec.user_id),
        )


@ns.route('/red-packet-info')
@respond_with_code
class RedPacketInfoResource(Resource):

    @classmethod
    def get(cls):
        """运营-红包-红包币种"""
        result = RedPacketOverallCache().read_aside()
        result.pop(RedPacketOverallCache.KEY_TIMESTAMP)
        return dict(
            asset_list=sorted(list(result.keys())),
            red_packet_statuses=RedPacket.STATUS_MAP,
            red_packet_types=RedPacket.PACKET_TYPE_MAP,
            history_statuses=RedPacketHistory.STATUS_MAP,
            red_packet_receive_types=RedPacket.PACKET_RECEIVE_TYPE_MAP,
            red_packet_receive_statuses=RedPacketHistory.STATUS_FILTER_MAP
        )


@ns.route('/red-packet-overall')
@respond_with_code
class RedPacketOverallResource(Resource):

    @classmethod
    def get(cls):
        """运营-红包-红包总览"""
        result = RedPacketOverallCache().read_aside()
        timestamp = result.pop(RedPacketOverallCache.KEY_TIMESTAMP)
        records = []
        for asset in sorted(list(result.keys())):
            records.append(result[asset])
        return dict(timestamp=timestamp, records=records)


@ns.route('/red-packets')
@respond_with_code
class RedPacketListResource(Resource):
    export_headers = (
        {"field": "red_packet_id", Language.ZH_HANS_CN: "C-Box ID"},
        {"field": "name", Language.ZH_HANS_CN: "用户名称"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "receive_type", Language.ZH_HANS_CN: "C-Box类型"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "type", Language.ZH_HANS_CN: "金额类型"},
        {"field": "valid_days", Language.ZH_HANS_CN: "有效期(天)"},
        {"field": "received_count_total", Language.ZH_HANS_CN: "已领数量/总数"},
        {"field": "received_amount_total", Language.ZH_HANS_CN: "已领金额/总金额"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
    )
    NOT_EXISTED_CODE_ID = 0

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        type=EnumField(RedPacket.PacketType),
        receive_type=EnumField(RedPacket.ReceiveType),
        code=fields.String,
        status=EnumField(RedPacket.Status),
        user_id=fields.Integer,
        red_packet_id=fields.Raw,
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField(unlimited=True),
        limit=LimitField,
        export=fields.Boolean(missing=False)

    ))
    def get(cls, **kwargs):
        """运营-红包-红包记录"""
        query = RedPacket.query.order_by(RedPacket.id.desc())
        if asset := kwargs.get('asset'):
            query = query.filter(RedPacket.asset == asset)
        if packet_type := kwargs.get('type'):
            query = query.filter(RedPacket.packet_type == packet_type)
        if receive_type := kwargs.get('receive_type'):
            query = query.filter(RedPacket.receive_type == receive_type)
        if code := kwargs.get('code'):
            c_code = CBoxCode.query.filter(CBoxCode.code == code).first()
            if c_code:
                query = query.filter(RedPacket.code_id == c_code.id)
            else:
                query = query.filter(RedPacket.code_id == cls.NOT_EXISTED_CODE_ID)
        if status := kwargs.get('status'):
            query = query.filter(RedPacket.status == status)
        else:
            query = query.filter(RedPacket.status.notin_([
                RedPacket.Status.DEDUCTED,
                RedPacket.Status.RISK,
                RedPacket.Status.RISK_CANCELLED
            ]))
        if user_id := kwargs.get('user_id'):
            query = query.filter(RedPacket.user_id == user_id)
        if red_packet_id := kwargs.get('red_packet_id'):
            query = query.filter(RedPacket.id == red_packet_id)
        if start_time := kwargs.get('start_time'):
            query = query.filter(RedPacket.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(RedPacket.created_at < end_time)
        if not kwargs.get('export'):
            page, limit = kwargs['page'], kwargs['limit']
            pagination = query.paginate(page, limit, error_out=False)
            items = pagination.items
            records = cls.get_records(items)
            return dict(
                has_next=pagination.has_next,
                curr_page=pagination.page,
                count=len(records),
                data=records,
                total=pagination.total,
                total_page=math.ceil(pagination.total / limit)
            )
        else:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
            records = cls.get_records(items)
            cls.fmt_records(records)
            return export_xlsx(filename='c-box-send-records',
                               data_list=records,
                               export_headers=cls.export_headers)

    @classmethod
    def get_records(cls, items):
        red_packet_ids = [r.id for r in items]
        history_records_map = dict()
        for ids in batch_iter(red_packet_ids, 1000):
            history_records = RedPacketHistory.query.filter(
                RedPacketHistory.red_packet_id.in_(ids),
                RedPacketHistory.grab_at.isnot(None)
            ).with_entities(
                RedPacketHistory.red_packet_id,
                func.count().label('count'),
                func.sum(RedPacketHistory.amount).label('amount')
            ).group_by(RedPacketHistory.red_packet_id)
            batch_res = {r[0]: [r[1], r[2]] for r in history_records}
            history_records_map.update(batch_res)

        c_box_user_ids = [r.user_id for r in items]
        user_records_map = UserRepository.get_user_info_map(c_box_user_ids)
        records = []
        for item in items:
            history = history_records_map.get(item.id, [0, 0])
            user: User = user_records_map[item.user_id]
            record = {
                'red_packet_id': item.id,
                'name': user.name_displayed if user else item.user_name,
                'user_id': user.id if user else None,
                'email': item.user_name,
                'asset': item.asset,
                'type': item.packet_type.name,
                'code_id': item.code_id if item.code_id else None,
                'receive_type': item.receive_type.name,
                'received_count': history[0],
                'total_count': item.count,
                'received_amount': amount_to_str(history[1], COIN_PLACES),
                'total_amount': amount_to_str(item.total_amount, COIN_PLACES),
                'status': item.status.name,
                'created_at': item.created_at.timestamp(),
                'updated_at': item.updated_at.timestamp(),
                'valid_days': item.valid_days,
            }
            records.append(record)
        return records

    @classmethod
    def fmt_records(cls, records):
        for record in records:
            record['type'] = RedPacket.PACKET_TYPE_MAP[record['type']]
            record['receive_type'] = RedPacket.PACKET_RECEIVE_TYPE_MAP[
                record['receive_type']]
            record['status'] = RedPacket.STATUS_MAP.get(record['status'], '')
            record['created_at'] = datetime_to_utc8_str(
                timestamp_to_datetime(record['created_at']))
            record['updated_at'] = datetime_to_utc8_str(
                timestamp_to_datetime(record['updated_at']))
            record[
                'received_count_total'] = f'{record["received_count"]}/{record["total_count"]}'
            record[
                'received_amount_total'] = f'{record["received_amount"]}/{record["total_amount"]}'


@ns.route('/red-packet-detail')
@respond_with_code
class RedPacketDetailResource(Resource):
    export_headers = (
        {"field": "red_packet_id", Language.ZH_HANS_CN: "C-Box ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "amount", Language.ZH_HANS_CN: "领取C-Box金额"},
        {"field": "email_status", Language.ZH_HANS_CN: "邮箱状态"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "grab_at", Language.ZH_HANS_CN: "领取时间"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        red_packet_id=fields.Integer(allow_none=True),
        asset=fields.String,
        receive_email=fields.String,
        receive_status=EnumField(RedPacketHistory.Status),
        export=fields.Boolean(missing=False),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-红包-红包详情"""
        query = RedPacketHistory.query.filter(
            RedPacketHistory.grab_at.isnot(None)
        ).order_by(RedPacketHistory.grab_at.desc())
        if red_packet_id := kwargs.get('red_packet_id'):
            query = query.filter(
                RedPacketHistory.red_packet_id == red_packet_id
            )
        if asset := kwargs.get('asset'):
            query = query.filter(
                RedPacketHistory.asset == asset
            )
        if email := kwargs.get('receive_email'):
            query = query.filter(
                RedPacketHistory.email == email
            )
        if receive_status := kwargs.get('receive_status'):
            query = query.filter(
                RedPacketHistory.status == receive_status
            )
        else:
            query = query.filter(
                RedPacketHistory.status.in_(
                    (
                        RedPacketHistory.Status.UNREGISTER,
                        RedPacketHistory.Status.EXPIRED,
                        RedPacketHistory.Status.FINISHED,
                        RedPacketHistory.Status.FAILED,
                    ))
            )
        if not kwargs.get('export'):
            page, limit = kwargs['page'], kwargs['limit']
            pagination = query.paginate(page, limit, error_out=False)
            items = pagination.items
            records = cls.get_records(items)
            return dict(
                has_next=pagination.has_next,
                curr_page=pagination.page,
                count=len(records),
                data=records,
                total=pagination.total,
                total_page=math.ceil(pagination.total / limit)
            )
        else:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
            records = cls.get_records(items)
            cls.fmt_records(records)
            return export_xlsx(filename='c-box-receive-records',
                               data_list=records,
                               export_headers=cls.export_headers)

    @classmethod
    def get_records(cls, items):
        records = []
        for item in items:
            status = item.status
            email_status = '未注册' if status != RedPacketHistory.Status.FINISHED else '已注册'
            record = {
                'red_packet_id': item.red_packet_id,
                'user_id': item.user_id,
                'email': item.email,
                'asset': item.asset,
                'amount': amount_to_str(item.amount, COIN_PLACES),
                'status': status.name,
                'email_status': email_status,
                'grab_at': item.grab_at.timestamp(),
            }
            records.append(record)
        return records

    @classmethod
    def fmt_records(cls, records):
        for record in records:
            record['status'] = RedPacketHistory.STATUS_MAP.get(record['status'], '')
            record['grab_at'] = datetime_to_utc8_str(
                timestamp_to_datetime(record['grab_at']))


class CBoxPromotionMixin:
    model = CBoxPromotion

    class DisplayStatus(Enum):
        DRAFT = '待上架'
        PENDING = '待展示'
        RUNNING = '展示中'
        OFFLINE = '已下架'

    deserialize_status_mapping = {
        DisplayStatus.DRAFT: model.Status.OFFLINE,
        DisplayStatus.PENDING: model.Status.ONLINE,
        DisplayStatus.RUNNING: model.Status.ONLINE,
        DisplayStatus.OFFLINE: model.Status.OFFLINE,
    }

    @classmethod
    def get_display_status(cls, row):
        now_ = now()
        begin_at = row.begin_at
        end_at = row.end_at
        if row.status == cls.model.Status.OFFLINE:
            if end_at < now_:
                display_status = cls.DisplayStatus.OFFLINE.value
            else:
                display_status = cls.DisplayStatus.DRAFT.value
        else:
            if now_ < begin_at:
                display_status = cls.DisplayStatus.PENDING.value
            elif begin_at <= now_ <= end_at:
                display_status = cls.DisplayStatus.RUNNING.value
            else:
                display_status = cls.DisplayStatus.OFFLINE.value
        return display_status


@ns.route('/c-box/promotion/list')
@respond_with_code
class CBoxPromotionResource(Resource, CBoxPromotionMixin):

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String,
        status=EnumField(CBoxPromotionMixin.DisplayStatus),
        languages=fields.String,
        start_time=TimestampField(),
        end_time=TimestampField(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50),
    ))
    def get(cls, **kwargs):
        """运营-cbox-活动区列表"""
        model = cls.model
        params = Struct(**kwargs)
        query = model.query.filter(
            model.status != model.Status.DELETED
        ).order_by(model.id.desc())
        if name := params.name:
            query = query.filter(model.name == name)
        if platform := params.platform:
            query = query.filter(model.platform == platform)

        if status := params.status:
            status = cls.deserialize_status_mapping.get(status)
            now_ = now()
            if status:
                query = query.filter(model.status == status)
                if params.status in (cls.DisplayStatus.PENDING, cls.DisplayStatus.DRAFT):
                    query = query.filter(model.begin_at > now_)
                elif params.status == cls.DisplayStatus.RUNNING:
                    query = query.filter(model.end_at >= now_,
                                         model.begin_at <= now_)
                elif params.status == cls.DisplayStatus.OFFLINE:
                    query = query.filter(model.end_at < now_)

        if params.start_time:
            query = query.filter(model.begin_at >= params.start_time)
        if params.end_time:
            query = query.filter(model.end_at <= params.end_time)

        names = model.query.filter(
            model.status != model.Status.DELETED
        ).order_by(
            model.id.desc()
        ).with_entities(
            model.name
        ).all()
        promotion_names = [i.name for i in names]
        languages = language_name_cn_names()
        languages['全部'] = '全部'
        langs = set(json.loads(params.languages)) if params.languages else []
        if langs:
            ret = query.all()
            records = []
            for row in ret:
                r_langs = set(json.loads(row.languages))
                if row.type == model.Type.GENERAL:
                    records.append(row)
                elif r_langs & langs:
                    records.append(row)
            page, limit = params.page, params.limit
            items = records[(page - 1) * limit: page * limit]
            return dict(
                total=len(records),
                items=cls.fmt_items(items, languages),
                extra=dict(
                    platforms=model.Platform,
                    statuses=cls.DisplayStatus,
                    promotion_names=promotion_names,
                    languages=languages,
                )
            )
        else:
            ret = query.paginate(params.page, params.limit)
            items = ret.items

            return dict(
                total=ret.total,
                items=cls.fmt_items(items, languages),
                extra=dict(
                    platforms=model.Platform,
                    statuses=cls.DisplayStatus,
                    promotion_names=promotion_names,
                    languages=languages,
                )
            )

    @classmethod
    def fmt_items(cls, rows, languages):
        res = []
        for row in rows:
            item = row.to_dict()
            platform = json.loads(row.platform)
            item['platform'] = ','.join([getattr(cls.model.Platform, i).value for i in platform])
            item['display_status'] = cls.get_display_status(row)
            if row.type == cls.model.Type.GENERAL:
                item['langs_display'] = '全部'
            else:
                langs = json.loads(row.languages)
                item['langs_display'] = ','.join([languages[i] for i in langs])
            res.append(item)
        return res

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True, validate=lambda s: len(s) <= 256),
        platform=fields.List(fields.String(), required=True),
        begin_at=fields.DateTime(required=True),
        end_at=fields.DateTime(required=True),
        type=EnumField(CBoxPromotion.Type, required=True),
        languages=fields.List(fields.String(), required=True),
    ))
    def post(cls, **kwargs):
        """运营-cbox-新建活动区"""
        params = Struct(**kwargs)
        row = db.session_add_and_commit(
            cls.model(
                name=params.name,
                platform=compact_json_dumps(params.platform),
                begin_at=params.begin_at,
                end_at=params.end_at,
                type=params.type,
                languages=compact_json_dumps(params.languages),
            )
        )
        CBoxPromotionCache.reload()
        return row.to_dict(enum_to_name=True)


@ns.route('/c-box/promotion/<int:id_>')
@respond_with_code
class CBoxPromotionDetailResource(Resource, CBoxPromotionMixin):

    @classmethod
    def get(cls, id_):
        """运营-cbox-活动区详情"""
        extra = dict(
            platforms=cls.model.Platform,
            content_languages=language_name_cn_names(),
            all_languages=language_name_cn_names(),
        )
        if not id_:
            return dict(
                extra=extra
            )
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        res = row.to_dict(enum_to_name=True)
        languages = json.loads(res['languages'])
        platform = json.loads(res['platform'])
        res['languages'] = languages
        res['platform'] = platform
        res['display_status'] = cls.get_display_status(row)
        if languages:
            content_languages = extra['content_languages']
            extra['content_languages'] = {lang: cn_name for lang, cn_name in content_languages.items()
                                          if lang in res['languages']}
        res['extra'] = extra
        return res

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True, validate=lambda s: len(s) <= 256),
        platform=fields.List(fields.String(), required=True),
        begin_at=fields.DateTime(required=True),
        end_at=fields.DateTime(required=True),
        type=EnumField(CBoxPromotion.Type, required=True),
        languages=fields.List(fields.String, required=True),
    ))
    def put(cls, id_, **kwargs):
        """运营-cbox-活动区-编辑"""
        row = cls.model.query.get(id_)
        params = Struct(**kwargs)
        if not row:
            raise InvalidArgument
        row.name = params.name
        row.platform = compact_json_dumps(params.platform)
        row.begin_at = params.begin_at
        row.end_at = params.end_at
        row.type = params.type
        row.languages = compact_json_dumps(params.languages)
        db.session.commit()
        CBoxPromotionCache.reload()
        row_dict = row.to_dict(enum_to_name=True)
        return row_dict

    @classmethod
    def delete(cls, id_):
        """运营-cbox-活动区-删除"""
        row = cls.model.query.get(id_)
        if not row:
            raise InvalidArgument(message='活动不存在！')
        row.status = cls.model.Status.DELETED
        db.session.commit()
        CBoxPromotionCache.reload()


@ns.route('/c-box/promotion/<int:id_>/online-offline')
@respond_with_code
class CBoxPromotionOnOffLineResource(Resource):
    model = CBoxPromotion

    @classmethod
    def patch(cls, id_):
        """运营-cbox-活动区-上架"""
        row = cls.model.query.get(id_)
        if not row:
            raise InvalidArgument(message='活动不存在！')
        row.status = cls.model.Status.ONLINE
        db.session.commit()
        CBoxPromotionCache.reload()

    @classmethod
    def delete(cls, id_):
        """运营-cbox-活动区-下架"""
        row = cls.model.query.get(id_)
        if not row:
            raise InvalidArgument(message='活动不存在！')
        row.status = cls.model.Status.OFFLINE
        db.session.commit()
        CBoxPromotionCache.reload()


@ns.route('/c-box/promotion/<int:id_>/langs/<lang>')
@respond_with_code
class CBoxPromotionContentResource(Resource):
    model = CBoxPromotionContent

    @classmethod
    def get(cls, id_, lang):
        """运营-cbox-活动区-获取活动区内容"""
        row = cls._get_row(id_, lang)
        if row is None:
            return dict(
                title='',
                content=''
            )
        return dict(
            title=row.title,
            content=row.content,
        )

    @classmethod
    def _get_row(cls, id_: int, lang: str):
        model = cls.model
        return model.query.filter(
            model.promotion_id == id_,
            model.lang == getattr(Language, lang, '')
        ).first()

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String(required=True),
        content=fields.String(required=True)
    ))
    def put(cls, id_, lang, **kwargs):
        """运营-cbox-活动区-编辑内容"""
        title = kwargs['title'] or ''
        content = kwargs['content'] or ''
        if title == '':
            raise InvalidArgument(message=f"{lang}标题或者内容不能为空")
        row = cls._get_row(id_, lang)
        model = cls.model
        if row is None:
            row = model(
                promotion_id=id_,
                lang=lang,
                title=title,
                content=content
            )
            db.session.add(row)
        else:
            row.title = title
            row.content = content
        db.session.commit()
        return row


class AssignAuditorMixin:

    @classmethod
    def get_auditor_map(cls):
        admin_role_ids = config['KYC_AUDITOR_ROLES']
        records = AdminUserRole.query.filter(
            AdminUserRole.admin_role_id.in_(admin_role_ids),
            AdminUserRole.status == AdminUserRole.Status.PASSED
        ).with_entities(AdminUserRole.user_id).all()
        user_ids = {r.user_id for r in records}
        res = {r.user_id: r.name for r in AdminUser.query.filter(
            AdminUser.user_id.in_(user_ids),
            AdminUser.status == AdminUser.Status.PASSED
        ).all()}
        return res

    @classmethod
    def get_auditor_list(cls, auditor_ids_map: dict, audit_required_ids: set):
        assigned_ids = set()
        auditor_list = []
        for auditor_id, auditor_name in cls.get_auditor_map().items():
            ids = set(auditor_ids_map.get(str(auditor_id), [])) & audit_required_ids
            auditor_list.append({'auditor_id': auditor_id, 'name': auditor_name, 'count': len(ids)})
            assigned_ids |= set(ids)
        auditor_list.sort(key=lambda x: x["count"], reverse=True)
        unassigned_ids = audit_required_ids - assigned_ids
        auditor_list.insert(0, {'auditor_id': 0, 'name': '未分配', 'count': len(unassigned_ids)})
        return auditor_list


class KYCResourceMixin(AssignAuditorMixin):

    STATUS_NAMES = dict(
        AUDIT_REQUIRED='待人工审核',
        PASSED='已通过',
        REJECTED='已拒绝',
        DUPLICATE_VERIFICATION='重复提交',
        CANCELLED='已取消',
        SCREENING="风险筛查中",
    )

    AUDIT_TYPES = dict(
        admin="人工审核",
        audit_required='人工二次审核',
        third_party="文件验证自动审核",
        non_doc_third_party="非文件验证自动审核",
        submit_on_pass="重新认证人工审核"
    )

    @classmethod
    def get_query_sql(cls, auditor_ids_map: dict = None, **kwargs):
        query = KycVerification.query
        if country := kwargs.get('country'):
            if (country_info := get_country(country)) is None:
                raise InvalidArgument
            query = query.filter(KycVerification.country == country_info.iso_3)
        if (status := kwargs.get('status')) is not None:
            query = query.filter(KycVerification.status == status)
        if (id_type := kwargs.get('id_type')) is not None:
            query = query.filter(KycVerification.id_type == id_type)
        if user := kwargs.get('user', '').strip():
            query = query.filter(
                KycVerification.user_id.in_(User.search_for_users(user)))
        if id_number := kwargs.get('id_number', '').strip():
            query = query.filter(KycVerification.id_number == id_number)
        if start := kwargs.get('start'):
            query = query.filter(KycVerification.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(KycVerification.created_at < end)
        if platform := kwargs.get('platform'):
            query = query.filter(KycVerification.platform == platform)
        if service_type := kwargs.get('service_type'):
            query = query.filter(KycVerification.service_type == service_type)

        if audit_type := kwargs.get('audit_type'):
            if audit_type == 'admin':
                query = query.filter(
                    or_(
                        and_(
                            KycVerification.rejection_reason.is_(None),
                            KycVerification.custom_rejection_reason.is_(None),
                            KycVerification.status == KycVerification.Status.AUDIT_REQUIRED
                        ),
                        KycVerification.auditor_id.isnot(None)
                    )
                )
            elif audit_type == 'third_party':
                query = query.filter(
                    KycVerification.status != KycVerification.Status.AUDIT_REQUIRED,
                    KycVerification.auditor_id.is_(None),
                    KycVerification.doc_type == KycVerification.DocType.DOC
                )
            elif audit_type == 'non_doc_third_party':
                query = query.filter(
                    KycVerification.status != KycVerification.Status.AUDIT_REQUIRED,
                    KycVerification.auditor_id.is_(None),
                    KycVerification.doc_type == KycVerification.DocType.NON_DOC
                )
            elif audit_type == 'audit_required':
                query = query.filter(
                    or_(
                        KycVerification.rejection_reason.isnot(None),
                        KycVerification.custom_rejection_reason.isnot(None)
                    ),
                    KycVerification.status == KycVerification.Status.AUDIT_REQUIRED)
            else:
                query = query.filter(
                    KycVerification.submit_type == KycVerification.SubmitType.SUBMIT_ON_PASS,
                )
        if (auditor_id := kwargs.get('auditor_id')) is not None:
            if auditor_id == 0:
                kyc_ids = {r.id for r in KycVerification.query.filter(
                    KycVerification.status == KycVerification.Status.AUDIT_REQUIRED
                ).with_entities(
                    KycVerification.id
                ).all()}
                assigned_ids = set()
                for ids in auditor_ids_map.values():
                    assigned_ids |= set(ids)
                kyc_ids -= assigned_ids
            else:
                kyc_ids = auditor_ids_map.get(str(auditor_id), [])
            query = query.filter(
                KycVerification.id.in_(kyc_ids)
            )
        return query

    @classmethod
    def get_audit_type(cls, item: KycVerification):
        if item.status == KycVerification.Status.CREATED or (
                item.status != KycVerification.Status.AUDIT_REQUIRED and not item.auditor_id
        ):
            if item.doc_type == KycVerification.DocType.NON_DOC:
                return 'non_doc_third_party'
            else:
                return 'third_party'
        elif item.submit_type == KycVerification.SubmitType.SUBMIT_ON_PASS:
            return 'submit_on_pass'
        elif item.status == KycVerification.Status.AUDIT_REQUIRED and item.get_reject_reason():
            return 'audit_required'
        else:
            return 'admin'


@ns.route('/kyc')
@respond_with_code
class KYCListResource(KYCResourceMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        country=fields.String,
        status=EnumField(KycVerification.Status),
        audit_type=EnumField(['admin', 'third_party', 'non_doc_third_party', 'audit_required', 'submit_on_pass']),
        id_type=EnumField(KycVerification.IDType),
        user=fields.String,
        id_number=fields.String,
        start=TimestampField(),
        end=TimestampField(),
        platform=fields.String,
        service_type=EnumField(KycVerification.ServiceType),
        page=PageField(unlimited=True),
        limit=LimitField,
        start_id=fields.Integer(required=False),
        auditor_id=fields.Integer,
    ))
    def get(cls, **kwargs):
        """运营-KYC-KYC列表"""
        kyc_auditor_map = {}
        auditor_ids_map = KYCAssignAuditorsCache().read()
        for auditor_id, kyc_ids in auditor_ids_map.items():
            for kyc_id in kyc_ids:
                kyc_auditor_map[kyc_id] = int(auditor_id)

        page, limit = kwargs['page'], kwargs['limit']
        if start_id := kwargs.get('start_id'):
            admin_user_id = g.user.id
            kyc_ids = auditor_ids_map.get(str(admin_user_id), [])
            kyc_ids.sort()
            if start_id in set(kyc_ids):
                next_index = (kyc_ids.index(start_id) + 1) % len(kyc_ids)
            else:
                next_index = 0
            query = KycVerification.query.filter(
                KycVerification.id.in_(kyc_ids[next_index: next_index + 1])
            )
            paginate = query.paginate(1, 1, error_out=False)
        else:
            query = cls.get_query_sql(auditor_ids_map, **kwargs)
            query = query.order_by(KycVerification.id.desc())
            paginate = query.paginate(page, limit, error_out=False)

        items = [dict(
            id=item.id,
            created_at=item.created_at,
            audited_at=item.audited_at,
            platform=item.platform,
            service_type=item.service_type.value if item.service_type else '',
            user_id=item.user_id,
            user_email=item.user.email,
            user_name=item.user.name_displayed,
            country=item.country,
            nationality=item.nationality,
            id_type=gettext(item.id_type.value),
            id_number=item.id_number,
            full_name=item.full_name,
            gender=item.gender,
            date_of_birth=item.date_of_birth,
            doc_expire_date_type=item.doc_expire_date_type.name if item.doc_expire_date_type else None,
            doc_expire_date=item.doc_expire_date,
            status=item.status.name,
            audit_type=cls.get_audit_type(item),
            operable=True,
            auditor_id=item.auditor_id if item.status != KycVerification.Status.AUDIT_REQUIRED else kyc_auditor_map.get(
                item.id),
            remark=item.remark,
        ) for item in paginate.items]
        auditor_ids = [item['auditor_id'] for item in items if item['auditor_id']]
        name_map = get_admin_user_name_map(auditor_ids)
        for item in items:
            item['auditor_name'] = name_map.get(item['auditor_id'], '--')

        audit_statuses = {KycVerification.Status.AUDIT_REQUIRED}
        if status := kwargs.get('status'):
            audit_statuses &= {status}
        audit_required_ids = set()
        if audit_statuses:
            audit_required_ids = {i.id for i in KycVerification.query.filter(
                KycVerification.status.in_(audit_statuses)
            ).with_entities(
                KycVerification.id
            ).all()}
        return dict(
            items=items,
            total=paginate.total,
            extra=dict(
                countries={code: get_country(code).cn_name
                           for code in list_country_codes_3_admin()},
                statuses=cls.STATUS_NAMES,
                audit_types=cls.AUDIT_TYPES,
                id_types={item.name: gettext(item.value) for item in KycVerification.IDType},
                platforms=[KycVerification.PlatForm.WEB, KycVerification.PlatForm.APP],
                statistics=cls.get_statistics(kwargs),
                service_types=KycVerification.ServiceType,
                auditor_list=cls.get_auditor_list(auditor_ids_map, audit_required_ids),
            )
        )

    @classmethod
    def get_statistics(cls, kwargs):
        statistics_query = KycVerificationStatistics.query
        if start := kwargs.get('start'):
            statistics_query = statistics_query.filter(
                KycVerificationStatistics.report_date > start.date())
        if end := kwargs.get('end'):
            statistics_query = statistics_query.filter(
                KycVerificationStatistics.report_date <= end.date())
        if service_type := kwargs.get('service_type'):
            statistics_query = statistics_query.filter(
                KycVerificationStatistics.service_type == service_type)
        else:
            statistics_query = statistics_query.filter(
                KycVerificationStatistics.service_type.is_(None))
        statistics_fields = ('admin_count',
                             'admin_passed_count',
                             'third_party_passed_count',
                             'third_party_count',
                             'second_audited_count')
        stats = statistics_query.with_entities(
            *[func.sum(getattr(KycVerificationStatistics, item)).label(item) for item in statistics_fields]
        ).first()
        return {item: getattr(stats, item) or 0 for item in statistics_fields}


@ns.route('/kyc/export')
@respond_with_code
class KYCListExportResource(KYCResourceMixin, Resource):
    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "audited_at", Language.ZH_HANS_CN: "审核时间"},
        {"field": "platform", Language.ZH_HANS_CN: "终端"},
        {"field": "service_type", Language.ZH_HANS_CN: "机构"},
        {"field": "user_email", Language.ZH_HANS_CN: "用户"},
        {"field": "country", Language.ZH_HANS_CN: "国籍"},
        {"field": "full_name", Language.ZH_HANS_CN: "姓名"},
        {"field": "gender", Language.ZH_HANS_CN: "性别"},
        {"field": "date_of_birth", Language.ZH_HANS_CN: "出生日期"},
        {"field": "id_type", Language.ZH_HANS_CN: "证件类型"},
        {"field": "id_number", Language.ZH_HANS_CN: "证件号码"},
        {"field": "audit_type", Language.ZH_HANS_CN: "审核类型"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "rejection_reason", Language.ZH_HANS_CN: "拒绝原因"},
        {"field": "third_reject_reasons", Language.ZH_HANS_CN: "第三方拒绝原因"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        country=fields.String,
        status=EnumField(KycVerification.Status),
        audit_type=EnumField(['admin', 'third_party', 'non_doc_third_party', 'audit_required']),
        id_type=EnumField(KycVerification.IDType),
        user=fields.String,
        id_number=fields.String,
        start=TimestampField(),
        end=TimestampField(),
        platform=fields.String,
        service_type=EnumField(KycVerification.ServiceType),
        order=EnumField(['asc', 'desc'], missing='desc'),
    ))
    def get(cls, **kwargs):
        """运营-KYC-KYC个人列表导出"""
        records = cls.get_records(**kwargs)
        user_ids = [i.user_id for i in records]
        user_email_dic = cls.get_user_email_dic(user_ids)
        countries = {code: get_country(code).cn_name
                     for code in list_country_codes_3_admin()}
        history_ids = [item.history_id for item in records if item.history_id]
        histories = KycVerificationHistory.query.filter(
            KycVerificationHistory.id.in_(history_ids)
        ).all()
        third_reject_reason_mapper = {}
        for h in histories:
            detail = json.loads(h.detail)
            kyc_id = detail.get('kyc_id')
            if kyc_id:
                reason_raw = detail.get('reason_raw', [])
                third_reject_reason_mapper[kyc_id] = KycBusiness.get_third_reject_reason_desc(h.service_type, reason_raw)

        res = [dict(
            id=item.id,
            created_at=item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            audited_at=item.audited_at.strftime('%Y-%m-%d %H:%M:%S') if item.audited_at else '',
            platform=item.platform,
            service_type=item.service_type.value if item.service_type else '',
            user_email=user_email_dic[item.user_id],
            country=countries.get(item.country, ''),
            id_type=item.id_type.value,
            id_number=item.id_number,
            full_name=item.full_name,
            gender=getattr(KycVerification.Gender, item.gender).value if item.gender else '',
            date_of_birth=item.date_of_birth.strftime('%Y-%m-%d') if item.date_of_birth else '',
            status=cls.STATUS_NAMES[item.status.name],
            audit_type=cls.AUDIT_TYPES[cls.get_audit_type(item)],
            rejection_reason=item.get_reject_reason(translate=False),
            third_reject_reasons='；'.join(third_reject_reason_mapper.get(item.id, []))
        ) for item in records]
        return export_xlsx(
            filename="kyc_verification_list",
            data_list=res,
            export_headers=cls.export_headers,
        )

    @classmethod
    def get_records(cls, **kwargs):
        query = cls.get_query_sql(**kwargs)
        res = []
        for items in iter_by_id(query, KycVerification):
            res.extend(items)
        return res

    @classmethod
    def get_user_email_dic(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = User.query.filter(
                User.id.in_(ids)
            ).with_entities(
                User.id,
                User.email
            ).all()
            res.update({user.id: user.email for user in users})
        return res


# noinspection PyUnresolvedReferences
@ns.route('/kyc/<int:id_>')
@respond_with_code
class KYCResource(Resource):
    AUDIT_TYPES = dict(
        admin="人工审核",
        audit_required="人工二次审核",
        third_party="文件验证自动审核",
        non_doc_third_party="非文件验证自动审核",
    )

    @classmethod
    def get_supported_types(cls, country, type_=User.KYCType.INDIVIDUAL.name):
        id_types = KycBusiness.get_all_country_id_types(kyc_type=type_)
        id_type_map = dict()
        for item in id_types:
            types_ = [i['idType'] for i in item['idTypes']]
            id_type_map[item['countryCode']] = types_
        supported_types = id_type_map.get(country, [])
        return supported_types

    @classmethod
    def get(cls, id_):
        """运营-KYC-查看单条kyc记录"""
        row: KycVerification = KycVerification.query.get(id_)
        if row is None:
            raise RecordNotFound
        data = cls._row_to_dict(row)
        return data

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String,
        id_number=fields.String,
        id_type=EnumField(KycVerification.IDType),
        country=fields.String,
        nationality=fields.String(allow_none=True),
        gender=EnumField(KycVerification.Gender),
        date_of_birth=DateField(to_date=True, allow_none=True),
        doc_expire_date=DateField(to_date=True, allow_none=True),
        status=EnumField(KycVerification.Status),
        rejection_reason=fields.List(EnumField(KycVerification.RejectionReason)),
        custom_reject_reason=fields.String,
        is_custom_reason=fields.Boolean(missing=False),
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-KYC-编辑kyc"""
        admin_user_id = g.user.id
        user: User = g.user
        row: KycVerification = KycVerification.query.get(id_)
        user_id = row.user_id
        if row is None:
            raise RecordNotFound
        if (r := kwargs.get("custom_reject_reason")) and len(r) > 4096:
            raise InvalidArgument(message="自定义拒绝原因长度不能超过4096")

        old_data = row.to_dict(enum_to_name=True)

        detail = dict(kwargs)
        detail['kyc_id'] = row.id
        if row.rejection_reason:
            detail['history_rejection_reason'] = [KycVerification.RejectionReason[i].value
                                                  for i in row.rejection_reason.split(',')]
        detail['history_status'] = row.status.value

        editted_risk_screen_fields = False
        if name := kwargs.get('name'):
            editted_risk_screen_fields |= name != row.full_name
            row.name = name
        if id_number := kwargs.get('id_number'):
            dup_record = KycVerification.query.filter(KycVerification.status.in_(
                (KycVerification.Status.PASSED,
                 KycVerification.Status.CREATED,
                 KycVerification.Status.AUDIT_REQUIRED,
                 KycVerification.Status.SCREENING)),
                KycVerification.id_number == id_number).first()
            if dup_record and dup_record.id != row.id:
                raise InvalidArgument(message="已有相同证件号码的KYC记录")
            row.id_number = id_number
        if id_type := kwargs.get('id_type'):
            row.id_type = id_type
        if country := kwargs.get('country'):
            editted_risk_screen_fields |= country != row.country
            row.country = country

        if gender := kwargs.get("gender"):
            editted_risk_screen_fields |= gender != row.gender
            row.gender = gender.name
        if date_of_birth := kwargs.get("date_of_birth"):
            editted_risk_screen_fields |= date_of_birth != row.date_of_birth
            row.date_of_birth = date_of_birth
        if doc_expire_date := kwargs.get("doc_expire_date"):
            row.doc_expire_date = doc_expire_date
        statuses = KycVerification.Status
        screen_request = None
        if editted_risk_screen_fields:
            screen_request = RiskScreenBusiness.new_screen_request_from_kyc(row, session_add=True)
        if (status := kwargs.get('status')) is not None:
            # 只允许审核操作 最新一条
            last_kyc = (
                KycVerification.query.filter(
                    KycVerification.user_id == user_id,
                ).order_by(KycVerification.id.desc()).first()
            )
            if last_kyc.id != row.id:
                raise InvalidArgument(message=f"已有新提交的KYC {last_kyc.id}，不允许再操作旧KYC记录")

            kyc_institution = KYCInstitution.query.filter(
                KYCInstitution.user_id == user_id,
                KYCInstitution.status.in_([KYCInstitution.Status.CREATED, KYCInstitution.Status.PASSED])
            ).first()

            if kyc_institution:
                raise InvalidArgument(
                    message=f"已经有 {kyc_institution.status.value} 机构kyc 审核记录, 请先处理机构kyc。")

            # 审核通过 进入筛查
            if status == statuses.PASSED:
                status = statuses.SCREENING

                # 不允许有多条通过的kyc
                exist_kyc = KycVerification.query.filter(
                    KycVerification.user_id == user_id,
                    KycVerification.id != row.id,
                    KycVerification.status.in_([
                        KycVerification.Status.SCREENING, KycVerification.Status.PASSED
                    ])
                ).first()
                if exist_kyc:
                    raise InvalidArgument(message=f"已有通过KYC记录 {exist_kyc.id}，不能再通过另外的审核记录")

            row.status = status
            if status is statuses.SCREENING:
                row.auditor_id = user.id
                row.audited_at = now()
                row.user.kyc_status = User.KYCStatus.PROCESSING
                if not screen_request:
                    screen_request = RiskScreenBusiness.new_screen_request_from_kyc(row, session_add=True)
            elif status is statuses.REJECTED:
                if kwargs.get('is_custom_reason'):
                    if (rej_reason := kwargs.get('custom_reject_reason')) is None:
                        raise InvalidArgument(
                            message='`custom_reject_reason` is not provided')
                    row.is_custom_reason = True
                    row.custom_rejection_reason = rej_reason

                else:
                    if (rej_reason := kwargs.get('rejection_reason')) is None:
                        raise InvalidArgument(
                            message='`rejection_reason` is not provided')
                    rej_reason = ','.join([r.name for r in rej_reason])
                    row.rejection_reason = rej_reason
                    row.is_custom_reason = False
                row.auditor_id = user.id
                row.audited_at = now()
                row.user.kyc_status = User.KYCStatus.FAILED
                RiskScreenBusiness.cancel_screen_request(user_id)
            else:
                raise InvalidArgument(message=f'invalid status: {status!r}')
        if not status:
            row.nationality = kwargs.get("nationality")

        if remark := kwargs.get("remark"):
            row.remark = remark
        db.session.commit()

        if status is not None:
            update_user_location(user_id)
            if screen_request:
                # KYC通过，进入风险筛查流程
                process_risk_screen_request_task.delay(screen_request.id)
            elif status is statuses.REJECTED:
                send_kyc_result_email.delay(row.id)

        if kwargs.get('status') is not None:
            KYCAssignAuditorsCache().finish(id_)
            # status is not None时表示审核, 否则表示编辑
            AdminOperationLog.new_audit(
                user_id=admin_user_id,
                ns_obj=OPNamespaceObjectRisk.Kyc,
                detail=detail,
                target_user_id=user_id,
            )
        else:
            AdminOperationLog.new_edit(
                user_id=admin_user_id,
                ns_obj=OPNamespaceObjectRisk.Kyc,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
                target_user_id=user_id,
            )
        row = KycVerification.query.get(id_)
        return cls._row_to_dict(row)

    @classmethod
    def _row_to_dict(cls, row: KycVerification):
        if row.status == KycVerification.Status.AUDIT_REQUIRED:
            if row.auditor_id or not row.get_reject_reason():
                audit_type = 'admin'
            else:
                audit_type = 'audit_required'
        else:
            if row.auditor_id:
                audit_type = 'admin'
            else:
                if row.doc_type == KycVerification.DocType.NON_DOC:
                    audit_type = 'non_doc_third_party'
                else:
                    audit_type = 'third_party'
        # last_pass_record 上一次通过的记录，即为类型为SUBMIT_ON_PASS的前一条记录
        last_pass_record = None
        if row.submit_type == KycVerification.SubmitType.SUBMIT_ON_PASS:
            submit_on_pass_record = KycVerification.query.filter(
                KycVerification.user_id == row.user_id,
                KycVerification.submit_type == KycVerification.SubmitType.SUBMIT_ON_PASS
            ).order_by(KycVerification.id.asc()).first()
            if submit_on_pass_record:
                last_pass_record = KycVerification.query.filter(
                    KycVerification.user_id == row.user_id,
                    KycVerification.id < submit_on_pass_record.id
                ).order_by(KycVerification.id.desc()).first()
        last_record = KycVerification.query.filter(KycVerification.id < row.id,
                                                   KycVerification.user_id == row.user_id
                                                   ).order_by(KycVerification.id.desc()).first()
        history = KycVerificationHistory.query.filter(
            KycVerificationHistory.created_at > row.created_at - timedelta(seconds=KycBusiness.PROCESS_EXPIRE_TIME),
            KycVerificationHistory.user_id == row.user_id
        ).all()
        id_type = id_number = doc_name = ''
        third_reject_reasons = []
        for item in history:
            presets = json.loads(item.detail)
            if kyc_id := presets.get('kyc_id'):
                if kyc_id == row.id:
                    id_type, id_number = presets['id_type'], presets['id_number']
                    doc_name = presets.get('doc_name', '')
                    reason_raw = presets.get('reason_raw', [])
                    third_reject_reasons = KycBusiness.get_third_reject_reason_desc(item.service_type, reason_raw)
                    break
        auditor_ids, reject_reasons, status_names, audit_times = [], [], [], []
        status_map = {item.value: item.name for item in KycVerification.Status}
        for log in AdminOperationLog.query.filter(
                AdminOperationLog.target_user_id==row.user_id,
                AdminOperationLog.namespace==OPNamespaceObjectRisk.Kyc.namespace.name,
                AdminOperationLog.object==OPNamespaceObjectRisk.Kyc.object.name,
                AdminOperationLog.operation==AdminOperationLog.Operation.AUDIT,
                AdminOperationLog.created_at>=row.created_at,
        ).all():
            log: AdminOperationLog
            _detail = json.loads(log.detail)
            if _detail.get('kyc_id') != row.id:
                continue
            current_status = _detail.get('status')
            if not current_status:
                continue
            auditor_ids.append(log.user_id)
            status_names.append(status_map[current_status])
            audit_times.append(log.created_at)
            if current_status == KycVerification.Status.REJECTED.value:
                if r := _detail.get('custom_rejection_reason'):
                    _reason = r
                elif r := _detail.get('custom_reject_reason'):  # 之前是这个字段
                    _reason = r
                else:
                    _reason = _detail.get('rejection_reason')
                reject_reasons.append(_reason)
            else:
                reject_reasons.append('--')
        auditor_map = get_admin_user_name_map(auditor_ids)
        reason_map = {item.name: gettext(item.value) for item in KycVerification.RejectionReason}
        data = row.to_dict(enum_to_name=True)
        auditors = [{id_: auditor_map[id_]} for id_ in auditor_ids]
        if not auditors:
            auditors = [{row.auditor.id: row.auditor.name_displayed}, ] if row.auditor else []
        country = get_country(row.country)
        nationality_country = get_country(row.nationality)
        data.update(
            rejection_reason=row.rejection_reason.split(',') if row.rejection_reason else [],
            last_rejection_reason=last_record.get_reject_reason() if last_record else [],
            country_name=country.cn_name if country else row.country,
            nationality_name=nationality_country.cn_name if nationality_country else row.nationality,
            status_name=KYCListResource.STATUS_NAMES.get(row.status.name, ''),
            status_names=[KYCListResource.STATUS_NAMES.get(item, '') for item in status_names],
            audit_times=audit_times,
            full_name=row.full_name,
            user_name=row.user.name_displayed,
            user_email=row.user.email,
            gender=row.gender,
            date_of_birth=row.date_of_birth.strftime("%Y-%m-%d") if row.date_of_birth else None,
            doc_expire_date_type=row.doc_expire_date_type.name if row.doc_expire_date_type else None,
            doc_expire_date=row.doc_expire_date.strftime("%Y-%m-%d") if row.doc_expire_date else None,
            audit_type=audit_type,
            auditor_name=(auditor.name_displayed
                          if (auditor := row.auditor) is not None
                          else ''),
            front_img_url=(file.private_url
                           if (file := row.front_img_file) is not None
                           else ''),
            back_img_url=(file.private_url
                          if (file := row.back_img_file) is not None
                          else ''),
            face_img_url=(file.private_url
                          if (file := row.face_img_file) is not None
                          else ''),
            rejection_reasons=reason_map,
            category_reasons=cls.get_category_reasons(),
            preset_id_type=id_type or row.id_type.name,
            preset_id_number=id_number or row.id_number,
            audit_types=cls.AUDIT_TYPES,
            reason_list=reject_reasons,
            last_pass_record_id=last_pass_record.id if last_pass_record else None,
            auditors=auditors,
            countries={code: get_country(code).cn_name for code in list_country_codes_3_admin()},
            supported_types=cls.get_supported_types(row.country),
            gender_dict={i.name: i.value for i in KycVerification.Gender},
            id_type_dict={i.name: i.value for i in KycVerification.IDType},
            doc_expire_date_type_dict={i.name: i.value for i in KycVerification.ExpireDateType},
            user_remark=get_user_remark(row.user),
            last_passed_at=row.last_passed_at if row.last_passed_at else None,
            doc_name=doc_name,
            third_reject_reasons=third_reject_reasons
        )
        return data

    @classmethod
    def get_category_reasons(cls) -> list[dict]:
        reason_em = KycVerification.RejectionReason
        category_reasons_map = {
            "高频使用": [
                reason_em.DOCUMENT_MISSING,
                reason_em.DOCUMENT_UNSUPPORTED,
                reason_em.SELFIE_IS_SCREEN_PAPER_VIDEO,
                reason_em.FACE_ABSENT,
                reason_em.NOT_MATCHED_WITH_LAST_SUBMIT,
                reason_em.AGE_NOT_ALLOWED,
                reason_em.COMPANY_COUNTRY_DOES_NOT_SUPPORT,
                reason_em.ID_ISSUED_BY_COUNTRY_RESIDENCE,
                reason_em.SHOW_FACE_WITH_ID,
                reason_em.TICKET_CONTACTED,
            ],
            "证件照": [
                reason_em.DOCUMENT_EXPIRED,
                reason_em.ID_IS_SAMPLE,
                reason_em.PHOTO_MANIPULATED,
                reason_em.PUNCHED_DOCUMENT,
                reason_em.DIGITAL_COPY,
                reason_em.NOT_READABLE_DOCUMENT,
                reason_em.HIDDEN_PART_DOCUMENT,
                reason_em.DAMAGED_DOCUMENT,
                reason_em.NO_DOCUMENT,
                reason_em.MISSING_FRONT,
                reason_em.MISSING_BACK,
                reason_em.MISSING_PAGE,
                reason_em.MISSING_SIGNATURE,
                reason_em.CAMERA_BLACK_WHITE,
                reason_em.INVALID_WATERMARK,
                reason_em.MANUAL_REJECTION,
                reason_em.UPLOAD_FAILED,
            ],
            "持证人和手持声明书": [
                reason_em.INVALID_TIME_FORMAT,
                reason_em.BLURRY_ANNOUNCEMENT,
                reason_em.HANDWRITTEN_NOTE_MISMATCHED,
                reason_em.PHOTO_MISMATCHED,
                reason_em.AGE_MISMATCHED,
                reason_em.ID_USED_AS_SELFIE,
            ],
            "信息填写": [
                reason_em.NATIONALITY_ERROR,
                reason_em.INFO_MISMATCHED,
                reason_em.PRESET_ID_UNMATCHED,
            ],
            "人脸识别": [
                reason_em.AGE_DIFFERENCE_TOO_BIG,
                reason_em.FACE_NOT_FULLY_VISIBLE,
                reason_em.SELFIE_BAD_QUALITY,
                reason_em.BLACK_AND_WHITE,
                reason_em.LIVENESS_FAILED,
                reason_em.FACE_TOO_CLOSE,
                reason_em.FACE_TOO_SMALL,
                reason_em.FACE_NOT_FOUND,
            ],
            "Sumsub": [
                reason_em.DOCUMENT_MANIPULATED,
                reason_em.FAKE,
                reason_em.ADDITIONAL_DOCUMENT_REQUIRED,
                reason_em.VIDEO_FAILED,
                reason_em.UNSUPPORTED_VIDEO_LANGUAGE,
                reason_em.CREDIBILITY_PROBLEM,
                reason_em.SERVICE_TIME_OUT,
                reason_em.FRAUDSTER,
                reason_em.DOCUMENT_EXISTED,
            ],
            "风险筛查": [
                reason_em.REGULATORY_ENFORCEMENT,
            ],
            "机构KYC": [
                reason_em.COMPANY_ADDR_DOES_NOT_EXISTS,
                reason_em.COMPANY_INFO_UNABLE_TO_CONFIRM,
                reason_em.COMPANY_BEHAVIOR_ILLEGAL,
                reason_em.INSTITUTION_INFO_WRONG,
                reason_em.INSTITUTION_UPLOAD_FILE_FUZZY,
                reason_em.INSTITUTION_UPLOAD_FILE_IS_COPY,
                reason_em.INSTITUTION_UPLOAD_FILE_WRONG,
                reason_em.COMPANY_NOT_BENEFICIARIES,
                reason_em.COMPANY_NOT_REPRESENTATIVES,
                reason_em.COMPANY_NOT_STRUCTURE,
                reason_em.EXPERIENCE_REQUIREMENT_MISMATCH,
                reason_em.THIRD_PARTY_INVOLVED,
                reason_em.WRONG_ID_NUMBER,
                reason_em.BAD_PROOF_OF_ADDRESS,
                reason_em.BAD_PROOF_OF_PAYMENT,
                reason_em.WRONG_ADDRESS,
                reason_em.INCOMPATIBLE_LANGUAGE,
            ],
        }
        result = []
        for ct, rs in category_reasons_map.items():
            item = {
                "category": ct,
                "reasons": [dict(name=i.name, value=i.value) for i in rs],
            }
            result.append(item)
        return result


@ns.route('/kyc/auditors')
@respond_with_code
class KycVerificationAssignAuditorsResource(KYCResourceMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        auditor_id=fields.Integer(required=True),
        ids=fields.List(fields.Integer, required=True),
    ))
    def post(cls, **kwargs):
        """风控-KYC-个人初级认证-批量分配审核人"""
        auditor_id = kwargs["auditor_id"]
        if auditor_id not in cls.get_auditor_map():
            raise InvalidArgument(message='请选择正确的审核人')
        ids = set(kwargs["ids"])
        if not ids:
            return
        ids = [r.id for r in KycVerification.query.filter(
            KycVerification.id.in_(ids),
            KycVerification.status == KycVerification.Status.AUDIT_REQUIRED
        ).with_entities(
            KycVerification.id
        ).all()]
        if not ids:
            return
        KYCAssignAuditorsCache().assign(auditor_id, ids)


@ns.route('/kyc/history')
@respond_with_code
class KYCHistoryListResource(Resource):
    STATUS_NAMES = dict(
        CREATED='待第三方审核',
        AUDIT_REQUIRED='待人工审核',
        PASSED='已通过',
        REJECTED='已拒绝',
        DUPLICATE_VERIFICATION='重复提交',
        CANCELLED='已取消'
    )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(KycVerificationHistory.Status),
        reason=EnumField(KycVerification.RejectionReason),
        service_type=EnumField(KycVerification.ServiceType),
        doc_type=EnumField(KycVerification.DocType),
        transaction_id=fields.String,
        user=fields.String,
        start=TimestampField(),
        end=TimestampField(),
        page=PageField(unlimited=True),
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """运营-KYC-第三方审核记录"""
        query = KycVerificationHistory.query
        page, limit = kwargs['page'], kwargs['limit']
        if (status := kwargs.get('status')) is not None:
            if status in (KycVerificationHistory.Status.FAILED,
                          KycVerificationHistory.Status.CANCELLED):
                query = query.filter(KycVerificationHistory.status.in_((
                    KycVerificationHistory.Status.FAILED,
                    KycVerificationHistory.Status.CANCELLED)))
            else:
                query = query.filter(KycVerificationHistory.status == status)
        if user := kwargs.get('user', '').strip():
            query = query.filter(
                KycVerificationHistory.user_id.in_(User.search_for_users(user)))
        if start := kwargs.get('start'):
            query = query.filter(KycVerificationHistory.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(KycVerificationHistory.created_at < end)
        if reason := kwargs.get('reason'):
            query = query.filter(KycVerificationHistory.rejection_reason.like(f"%{reason.name}%"))
        if service_type := kwargs.get('service_type'):
            query = query.filter(KycVerificationHistory.service_type == service_type)
        if doc_type := kwargs.get('doc_type'):
            query = query.filter(KycVerificationHistory.doc_type == doc_type)
        if transaction_id := kwargs.get('transaction_id'):
            query = query.filter(KycVerificationHistory.transaction_id == transaction_id)
        query = query.order_by(KycVerificationHistory.id.desc())
        records = query.paginate(page, limit, error_out=False)
        res = []
        user_ids = {item.user_id for item in records.items}
        users = User.query.filter(User.id.in_(user_ids)).all()
        user_map = {u.id: u for u in users}
        for item in records.items:
            item: KycVerificationHistory
            detail = json.loads(item.detail)
            user = user_map[item.user_id]
            country = get_country(detail['country'])
            full_name = detail.get('name') or f"{detail['first_name']} {detail['last_name']}"
            res.append(dict(
                id=item.id,
                created_at=item.created_at,
                country=country.cn_name if country else None,
                full_name=full_name,
                transaction_id=item.transaction_id,
                id_number=detail['id_number'],
                id_type=detail['id_type'],
                gender=detail.get("gender"),
                date_of_birth=detail.get("date_of_birth"),
                doc_expire_date_type=detail.get("doc_expire_date_type"),
                doc_expire_date=detail.get("doc_expire_date"),
                status=KycVerificationHistory.Status.CANCELLED.name
                if item.status == KycVerificationHistory.Status.FAILED else item.status.name,
                user_id=item.user_id,
                user_email=user.email,
                user_name=user.name_displayed,
                service_type=item.service_type,
                doc_type=item.doc_type.name if item.doc_type else '',
                kyc_id=detail.get('kyc_id'),
                rejection_reason=item.get_reject_reason()
            ))
        return dict(
            items=res,
            total=records.total,
            extra=dict(
                statuses={
                    KycVerificationHistory.Status.CREATED.name: '待审核',
                    KycVerificationHistory.Status.FINISHED.name: '已完成',
                    KycVerificationHistory.Status.CANCELLED.name: '已取消',
                    KycVerificationHistory.Status.RECHANGED.name: '再次修改',
                },
                id_types={item.name: gettext(item.value) for item in
                          KycVerification.IDType},
                reasons={item.name: gettext(item.value) for item in
                         KycBusiness.get_third_party_rejection_reason()},
                service_types=KycVerification.ServiceType,
                doc_types=KycVerification.DocType,
            )
        )


@ns.route('/kyc/country-configs')
@respond_with_code
class KYCCountryConfigsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
        country=fields.String,
        service_type=EnumField(KycVerification.ServiceType),
        pro_service_type=EnumField(KycVerificationPro.ServiceType),
        pro_supported=BoolField(allow_none=True),
    ))
    def get(cls, **kwargs):
        """运营-KYC-配置表"""
        query = KycCountryConfig.query
        if country := kwargs.get('country'):
            countries = search_for_countries(country)
            codes = [c.iso_3 for c in countries]
            query = query.filter(KycCountryConfig.country.in_(codes))
        if service_type := kwargs.get('service_type'):
            query = query.filter(KycCountryConfig.service_type == service_type)
        if pro_service_type := kwargs.get('pro_service_type'):
            query = query.filter(KycCountryConfig.pro_service_type == pro_service_type)
        if (pro_supported := kwargs.get('pro_supported')) is not None:
            query = query.filter(KycCountryConfig.pro_supported == pro_supported)
        query = query.order_by(KycCountryConfig.country)
        records = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)

        res = []
        for item in records.items:
            audit_detail = f"文件认证 {item.supported_id_types}"
            if item.non_doc_id_types:
                audit_detail += f"\n非文件认证 {item.non_doc_id_types}"
            res.append(dict(
                id=item.id,
                country=item.country,
                country_name=get_country(item.country).cn_name,
                service_type=item.service_type.name,
                is_audit_required=item.is_audit_required,
                audit_detail=audit_detail,
                pro_supported=item.pro_supported,
                pro_service_type=item.pro_service_type.name,
                pro_is_audit_required=item.pro_is_audit_required,
                pro_audit_detail='地址认证',
            ))
        return dict(
            total=records.total,
            items=res,
            service_types=KycVerification.ServiceType,
            pro_service_types=KycVerificationPro.ServiceType,
        )

    @classmethod
    @ns.use_kwargs(dict(
        country=fields.String(required=True),
        service_type=EnumField(KycVerification.ServiceType, required=True),
        is_audit_required=fields.Boolean(required=True),
        pro_supported=fields.Boolean(required=True),
        pro_service_type=EnumField(KycVerificationPro.ServiceType, required=True),
        pro_is_audit_required=fields.Boolean(required=True),
    ))
    def patch(cls, **kwargs):
        """运营-KYC-修改配置"""
        record = KycCountryConfig.query.filter(
            KycCountryConfig.country == kwargs['country']).first()
        if not record:
            raise InvalidArgument
        old_data = record.to_dict(enum_to_name=True)
        record.service_type = kwargs['service_type']
        record.is_audit_required = kwargs['is_audit_required']
        record.pro_supported = kwargs['pro_supported']
        record.pro_service_type = kwargs['pro_service_type']
        record.pro_is_audit_required = kwargs['pro_is_audit_required']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.KycCountryConfig,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
            special_data=dict(country=record.country),
        )


@ns.route('/kyc-institution')
@respond_with_code
class KYCInstitutionListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        country=fields.String,
        status=EnumField(KYCInstitution.Status),
        keyword=fields.String,
        beneficiary_id_number=fields.String,
        start=TimestampField(),
        end=TimestampField(),
        page=PageField(unlimited=True),
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """运营-KYC-机构认证列表"""
        params = Struct(**kwargs)
        model = KYCInstitution
        query = model.query
        if status := params.status:
            query = query.filter(model.status == status)
        if start := params.start:
            query = query.filter(model.created_at >= start)
        if end := params.end:
            query = query.filter(model.created_at <= end)
        if keyword := params.keyword:
            user_ids = User.search_for_users(keyword)
            query = query.filter(model.user_id.in_(user_ids))

        institution_ids = None
        if location_code := params.country:
            objs = InstitutionCompany.query.with_entities(
                InstitutionCompany.institution_id
            ).filter(
                InstitutionCompany.location_code == location_code
            ).all()
            institution_ids = {obj.institution_id for obj in objs}

        if beneficiary_id_number := params.beneficiary_id_number:
            objs = InstitutionDirector.query.with_entities(
                InstitutionDirector.institution_id
            ).filter(
                InstitutionDirector.is_beneficiary.is_(True),
                InstitutionDirector.id_number == beneficiary_id_number,
            ).all()
            institution_ids_from_director = {obj.institution_id for obj in objs}
            if institution_ids is None:
                institution_ids = institution_ids_from_director
            else:
                institution_ids &= institution_ids_from_director

        if institution_ids is not None:
            query = query.filter(model.id.in_(institution_ids))

        institutions = query.order_by(model.id.desc())
        page = institutions.paginate(params.page, params.limit)
        page_institution_ids = [item.id for item in page.items]

        companies = cls.fetch_companies(page_institution_ids)
        beneficiary_directors = cls.fetch_beneficiary_directors(page_institution_ids)
        user2email = cls.fetch_user2email_by([item.user_id for item in page.items])
        records = []
        for item in page.items:
            company = companies.get(item.id)
            beneficiary_director = beneficiary_directors.get(item.id)
            records.append(
                dict(
                    id=item.id,
                    created_at=item.created_at,
                    user_id=item.user_id,
                    status=item.status,
                    remark=item.remark,
                    user_email=user2email.get(item.user_id) or '--',
                    company_name=company.name if company else '--',
                    company_country=company.location_code if company else '--',
                    beneficiary_name=beneficiary_director.name if beneficiary_director else '--',
                    beneficiary_id_type=beneficiary_director.id_type if beneficiary_director else '--',
                    beneficiary_id_number=beneficiary_director.id_number if beneficiary_director else '--',
                )
            )

        return dict(
            items=records,
            total=page.total,
            extra=dict(
                countries={code: get_country(code).cn_name for code in list_country_codes_3_admin()},
                statuses={item.name: item.value for item in KYCInstitution.Status},
                id_types={item.name: gettext(item.value) for item in InstitutionDirector.IDType},
            ),
        )

    @classmethod
    def fetch_companies(cls, institution_ids):
        companies = InstitutionCompany.query.filter(
            InstitutionCompany.institution_id.in_(institution_ids)
        ).all()
        return {company.institution_id: company for company in companies}

    @classmethod
    def fetch_beneficiary_directors(cls, institution_ids):
        directors = InstitutionDirector.query.filter(
            InstitutionDirector.is_beneficiary.is_(True),
            InstitutionDirector.institution_id.in_(institution_ids)
        ).all()
        return {director.institution_id: director for director in directors}

    @classmethod
    def fetch_user2email_by(cls, user_ids: list) -> dict:
        users = User.query.filter(
            User.id.in_(set(user_ids))
        ).with_entities(User.id, User.email).all()
        return {user.id: user.email for user in users}


@ns.route('/kyc-institution/<int:id_>')
@respond_with_code
class KYCInstitutionResource(Resource):

    @classmethod
    def get(cls, id_):
        """运营-KYC-机构认证详情"""
        row: KYCInstitution = KYCInstitution.query.get(id_)
        if row is None:
            raise RecordNotFound

        applicant_info = cls.fetch_applicant_info(row.user_id)
        beneficiary_info, non_beneficiary_info = cls.fetch_director_info(row.id)
        company_info = cls.fetch_company_info(row.id)
        reject_reasons = {item.name: gettext(item.value) for item in KycVerification.RejectionReason}
        auditor_name = ''
        if row.auditor:
            user_name_map = get_admin_user_name_map([row.auditor])
            auditor_name = user_name_map.get(row.auditor)
        user = User.query.get(row.user_id)
        detail = dict(
            created_at=row.created_at,
            status=row.status,
            auditor=row.auditor or '--',
            auditor_name=auditor_name,
            reject_reason=row.rejection_reason.name if row.rejection_reason else '',
            custom_reject_reason=row.custom_rejection_reason,
            is_custom_reason=row.is_custom_reason,
            remark=row.remark,
            user_remark=get_user_remark(user),
            applicant_info=applicant_info,
            beneficiary_info=beneficiary_info,
            non_beneficiary_info=non_beneficiary_info,
            company_info=company_info,
            extra=dict(
                reject_reasons=reject_reasons,
                id_type_map={item.name: gettext(item.value) for item in InstitutionDirector.IDType},
            ),
        )
        return detail

    @classmethod
    def fetch_applicant_info(cls, user_id) -> dict:
        user = User.query.get(user_id)
        vip_user = VipUser.query.with_entities(
            VipUser.level
        ).filter(
            VipUser.user_id == user.id
        ).all()
        if vip_user:
            level = vip_user[0].level
        else:
            level = 0
        vip_level = VipHelper.get_vip_level2desc()[level]

        sub_users = SubAccount.query.with_entities(SubAccount.user_id).filter(SubAccount.main_user_id == user_id).all()
        user_ids = {sub_user.user_id for sub_user in sub_users}
        user_ids.add(user_id)
        last_thirty_days_date = (now() - timedelta(days=30)).date()
        user_trades = UserTradeSummary.query.with_entities(
            UserTradeSummary.trade_amount,
        ).filter(
            UserTradeSummary.user_id.in_(user_ids),
            UserTradeSummary.report_date >= last_thirty_days_date,
        ).all()
        last_30_days_trade_usd = sum(user_trade.trade_amount for user_trade in user_trades)

        applicant_info = dict(
            user_id=user.id,
            user_email=user.email,
            vip_level=vip_level,
            last_30_days_trade_usd=amount_to_str(last_30_days_trade_usd, 2),
        )
        return applicant_info

    @classmethod
    def fetch_director_info(cls, institution_id) -> Tuple[dict, list]:
        model = InstitutionDirector
        directors = model.query.filter(
            model.institution_id == institution_id,
        ).all()
        beneficiary = None
        non_beneficiaries = []
        for director in directors:
            if director.is_beneficiary:
                beneficiary = director
            else:
                non_beneficiaries.append(director)

        return cls._get_beneficiary_info(beneficiary), cls._get_non_beneficiary_info(non_beneficiaries)

    @classmethod
    def _get_beneficiary_info(cls, beneficiary) -> dict:
        beneficiary_info = {}
        if beneficiary:
            beneficiary_info = dict(
                beneficiary_name=beneficiary.name,
                beneficiary_country='',
                beneficiary_id_type=beneficiary.id_type,
                beneficiary_id_number=beneficiary.id_number,
                beneficiary_shareholding_ratio=beneficiary.shareholding_ratio_display,
            )
            if country := get_country(beneficiary.location_code):
                beneficiary_info.update(beneficiary_country=country.cn_name)
                beneficiary_info.update(supported_types=KYCResource.get_supported_types(country.iso_3,
                                                                                        type_=User.KYCType.INSTITUTION.name))
            file_ids = {
                beneficiary.face_img_file_id,
                beneficiary.identity_front_file_id,
            }
            if beneficiary.identity_back_file_id:
                file_ids.add(beneficiary.identity_back_file_id)
            files = cls.fetch_files(file_ids)
            file = files.get(beneficiary.face_img_file_id)
            face_img_file_url = file.private_url if file else ''
            beneficiary_info.update(face_img_file_url=face_img_file_url)

            file = files.get(beneficiary.identity_front_file_id)
            identity_front_file_url = file.private_url if file else ''
            beneficiary_info.update(identity_front_file_url=identity_front_file_url)

            file = files.get(beneficiary.identity_back_file_id)
            identity_back_file_url = file.private_url if file else ''
            beneficiary_info.update(identity_back_file_url=identity_back_file_url)

        return beneficiary_info

    @classmethod
    def _get_non_beneficiary_info(cls, non_beneficiaries) -> list:
        file_ids = set()
        for director in non_beneficiaries:
            file_ids.add(director.identity_front_file_id)
            file_ids.add(director.face_img_file_id)
            if director.identity_back_file_id:
                file_ids.add(director.identity_back_file_id)
        files = cls.fetch_files(file_ids)

        non_beneficiary_info = []
        for director in non_beneficiaries:
            director_info = dict(
                director_name=director.name,
                director_country='',
                director_id_type=director.id_type,
                director_id_number=director.id_number,
            )
            if country := get_country(director.location_code):
                director_info.update(director_country=country.cn_name)
                director_info.update(supported_types=KYCResource.get_supported_types(country.iso_3,
                                                                                     type_=User.KYCType.INSTITUTION.name))

            file = files.get(director.face_img_file_id)
            face_img_file_url = file.private_url if file else ''
            director_info.update(face_img_file_url=face_img_file_url)

            file = files.get(director.identity_front_file_id)
            identity_front_file_url = file.private_url if file else ''
            director_info.update(identity_front_file_url=identity_front_file_url)

            file = files.get(director.identity_back_file_id)
            identity_back_file_url = file.private_url if file else ''
            director_info.update(identity_back_file_url=identity_back_file_url)

            non_beneficiary_info.append(director_info)

        return non_beneficiary_info

    @classmethod
    def fetch_company_info(cls, institution_id) -> dict:
        model = InstitutionCompany
        company = model.query.filter(
            model.institution_id == institution_id,
        ).first()
        company_info = {}
        if company:
            certificate_file_ids = company.get_certificate_file_ids()
            structure_file_ids = company.get_structure_file_ids()
            constitution_file_ids = company.get_constitution_file_ids()
            roster_file_ids = company.get_roster_file_ids()
            authorization_letter_file_ids = company.get_authorization_letter_file_ids()
            statement_file_ids = company.get_statement_file_ids()
            coi_file_ids = company.get_coi_file_ids()
            other_file_ids = company.get_other_file_ids()
            file_ids = {
                *certificate_file_ids,
                *structure_file_ids,
                *constitution_file_ids,
                *roster_file_ids,
                *authorization_letter_file_ids,
                *statement_file_ids,
            }
            if other_file_ids:
                file_ids.update(set(other_file_ids))
            if coi_file_ids:
                file_ids.update(set(coi_file_ids))
            files = cls.fetch_files(file_ids)

            country = get_country(company.location_code).cn_name
            company_info.update(country=f'{country}（{company.location_code}）')
            company_info.update(name=company.name)
            company_info.update(nature_of_business=company.nature_of_business)
            company_info.update(register_code=company.register_code)
            company_info.update(funding_source=company.funding_source)
            company_info.update(register_address=company.register_address)
            company_info.update(government_url=company.government_url)
            company_info.update(apply_reason=company.apply_reason or '--')
            company_info.update(contact=company.contact)

            def get_file_urls(ids):
                ids = ids or []
                urls = []
                for id_ in ids:
                    file = files.get(id_)
                    if file:
                        urls.append(file.private_url)
                return urls

            company_info.update(certificate_file_urls=get_file_urls(certificate_file_ids))

            company_info.update(structure_file_urls=get_file_urls(structure_file_ids))

            company_info.update(constitution_file_urls=get_file_urls(constitution_file_ids))

            company_info.update(roster_file_urls=get_file_urls(roster_file_ids))

            company_info.update(authorization_letter_urls=get_file_urls(authorization_letter_file_ids))

            company_info.update(statement_file_urls=get_file_urls(statement_file_ids))

            company_info.update(other_file_urls=get_file_urls(other_file_ids))

            company_info.update(coi_file_urls=get_file_urls(coi_file_ids))

        return company_info

    @classmethod
    def fetch_files(cls, file_ids):
        if not file_ids:
            return {}
        files = File.query.filter(File.id.in_(file_ids)).all()
        return {file.id: file for file in files}

    @classmethod
    @ns.use_kwargs(dict(
        reject_reason=EnumField(KycVerification.RejectionReason),
        is_custom_reason=fields.Boolean(missing=False),
        custom_reject_reason=fields.String,
        status=EnumField(KYCInstitution.Status, required=True),
    ))
    def put(cls, id_, **kwargs):
        """运营-KYC-机构认证审核"""
        row: KYCInstitution = KYCInstitution.query.get(id_)
        if row is None:
            raise RecordNotFound
        row_user = User.query.get(row.user_id)
        latest_institution = KYCInstitution.query.filter(
            KYCInstitution.user_id == row_user.id,
        ).order_by(
            KYCInstitution.id.desc(),
        ).first()
        if row.id < latest_institution.id:
            raise InvalidArgument(message='用户已提交新纪录，请返回列表查看')
        params = Struct(**kwargs)
        if params.status is KYCInstitution.Status.PASSED:
            row_user.kyc_status = User.KYCStatus.PASSED
        else:
            row_user.kyc_status = User.KYCStatus.FAILED
            if params.is_custom_reason:
                if not params.custom_reject_reason:
                    raise InvalidArgument(message='自定义拒绝原因未填写')
                row.is_custom_reason = True
                row.custom_rejection_reason = params.custom_reject_reason
            else:
                if not params.reject_reason:
                    raise InvalidArgument(message='拒绝原因未选择')
                row.rejection_reason = params.reject_reason
                row.is_custom_reason = False

        status = params.status

        row.status = status
        row.auditor = g.user.id
        db.session.commit()
        update_user_location(row_user.id)
        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.KycInstitution,
            detail=dict(
                kyc_id=row.id,
                is_institution=True,
                status=f'{row.status}->{status}'
            ),
            target_user_id=row.user_id,
        )

        send_kyc_institution_result_email.delay(row.id)


@ns.route('/kyc-institution/<int:id_>/remark')
@respond_with_code
class KYCInstitutionRemarkResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-KYC-机构认证备注"""
        row: KYCInstitution = KYCInstitution.query.get(id_)
        if row is None:
            raise RecordNotFound

        remark = kwargs.get('remark') or ''
        old_remark = row.remark
        row.remark = remark
        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.KycInstitution,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=remark),
            target_user_id=row.user_id,
        )


@ns.route('/kyc-institution/<int:id_>/auditors')
@respond_with_code
class KYCInstitutionAuditorsResource(Resource):

    @classmethod
    def post(cls, id_):
        """运营-KYC 机构认证-更新当前编辑者"""
        row: KYCInstitution = KYCInstitution.query.get(id_)
        if row is None:
            raise RecordNotFound
        cache = KYCAuditorsCache(id_, institution_type=User.KYCType.INSTITUTION)
        cache.add_auditor(auditor := g.user.id)
        return dict(
            status=row.status,
            other_auditors=dict(
                User.query
                .filter(User.id.in_([a for a in cache.auditors
                                     if a != auditor]))
                .with_entities(User.id, User.email))
        )


@ns.route('/activities/info')
@respond_with_code
class ActivitiesInfoResource(Resource):

    @classmethod
    def get(cls):
        """运营-活动-活动信息"""
        return Activity.query.order_by(Activity.id).all()


@ns.route('/activities/gift-history')
@respond_with_code
class GiftHistoryResource(Resource):
    g_model = GiftHistory

    marshal_fields = {
        'id': fx_fields.Integer,
        'activity_id': fx_fields.Integer,
        'created_at': TimestampMarshalField,
        'user_id': fx_fields.Integer,
        'asset': fx_fields.String,
        'amount': AmountField,
        'remark': fx_fields.String,
        'status': EnumMarshalField(g_model.Status, output_field_lower=False)
    }

    GIFT_STATUS = {
        g_model.Status.CREATED.name: "进行中",
        g_model.Status.LOCKED.name: "待解锁",
        g_model.Status.FROZEN.name: "已冻结",
        g_model.Status.REAL_FROZEN.name: "延长冻结",
        g_model.Status.TO_REVOKE.name: "奖励撤销中",
        g_model.Status.REVOKED.name: "已撤回",
        g_model.Status.FINISHED.name: "已完成",
        g_model.Status.CANCELLED.name: "已取消",
    }

    @classmethod
    @ns.use_kwargs(dict(
        activity_id=CustomIntegerField,
        asset=fields.String,
        status=EnumField(GiftHistory.Status),
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        user_id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-活动-送币历史"""
        query = GiftHistory.query.order_by(GiftHistory.id.desc())
        if activity_id := kwargs.get('activity_id'):
            activity: Activity = Activity.query.get(activity_id)
            if not activity:
                raise InvalidArgument(message='活动不存在')
            query = query.filter(GiftHistory.activity_id == activity.id)
        if asset := kwargs.get('asset'):
            query = query.filter(GiftHistory.asset == asset)
        if status := kwargs.get('status'):
            query = query.filter(GiftHistory.status == status)
        if user_id := kwargs.get('user_id'):
            query = query.filter(GiftHistory.user_id == user_id)
        if start_time := kwargs.get('start_time'):
            query = query.filter(GiftHistory.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(GiftHistory.created_at < end_time)
        records = query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        user_ids = [item['user_id'] for item in records['data']]
        activity_ids = [item['activity_id'] for item in records['data']]
        user_account_name_map = ChannelRewardActivityJoinUsersResource.get_user_account_name_map(user_ids)
        activities = dict()
        for chunk_ids in batch_iter(activity_ids, 1000):
            rows = Activity.query.with_entities(
                Activity.id,
                Activity.name
            ).filter(
                Activity.id.in_(chunk_ids)
            ).all()
            activities.update(dict(rows))
        user_email_map = dict()
        for ids_ in batch_iter(user_ids, 1000):
            user_email = User.query.filter(
                User.id.in_(ids_)
            ).with_entities(User.id, User.email).all()
            user_email_map.update(dict(user_email))
        for item in records['data']:
            item['email'] = user_email_map[item['user_id']]
            activity_name = activities.get(item['activity_id'], '')
            item['activity'] = f"{item['activity_id']} {activity_name}"
            item['account_name'] = user_account_name_map.get(item['user_id'], '-')
        extra = dict()
        if user_id := kwargs.get('user_id'):
            gift_history = GiftHistory.query.filter(
                GiftHistory.user_id == user_id,
                GiftHistory.status == GiftHistory.Status.FINISHED,
            ).with_entities(
                GiftHistory.activity_id.distinct().label('activity_id')
            ).all()
            activity_ids = {i.activity_id for i in gift_history}
            channel_activity_list = ChannelRewardActivity.query.filter(
                ChannelRewardActivity.activity_id.in_(activity_ids)
            ).with_entities(
                ChannelRewardActivity.activity_id
            ).all()
            channel_activity_ids = {i.activity_id for i in channel_activity_list}
            total_receive_gift_count = len(activity_ids)
            referral_gift_count = 1 if Activity.REFERRAL_ID in activity_ids else 0  # 参与过邀请返佣
            receive_channel_count = len(channel_activity_ids)
            other_gift_count = total_receive_gift_count - referral_gift_count - receive_channel_count
            extra['total_receive_gift_count'] = total_receive_gift_count
            extra['referral_gift_count'] = referral_gift_count
            extra['receive_channel_count'] = receive_channel_count
            extra['other_gift_count'] = other_gift_count
        extra["statuses"] = cls.GIFT_STATUS
        records['extra'] = extra
        return records


def check_rule(kwargs):
    type_: DepositActivity.Type = kwargs['type']
    rule_data: Union[dict, list] = kwargs['rule_data']
    rule_map: Dict[DepositActivity.Type, Dict[str, Callable]] = {
        DepositActivity.Type.FIX: {
            "fix_threshold": Decimal,
            "fix_gift_amount": Decimal,
        },
        DepositActivity.Type.RANK: {
            "rank_min": int,
            "rank_max": int,
            "rank_amount": Decimal,
        },
        DepositActivity.Type.PARTITION: {
            "partition_enable": bool,
        },
        DepositActivity.Type.RATE: {
            "rate_gift": Decimal,
            "rate_upper_limit": Decimal
        }
    }

    rule_dict = rule_map[type_]
    if type_ != DepositActivity.Type.RANK:
        for k, v in rule_dict.items():
            if k not in rule_data:
                raise InvalidArgument(message="规则相关的配置项有问题")
            # noinspection PyBroadException
            try:
                if v(rule_data[k]) <= Decimal():
                    raise InvalidArgument(message=f"{k}不能小于等于0")
            except Exception:
                raise InvalidArgument(message='配置参数错误')
        return {k: v(rule_data[k]) for k, v in rule_dict.items()}
    else:
        if len(rule_data) <= 0:
            raise InvalidArgument(message='排名配置未填写')
        for e in rule_data:
            for key in rule_dict.keys():
                if key not in e:
                    raise InvalidArgument(message='规则相关的配置项有问题')
                if Decimal(e[key]) <= 0:
                    raise InvalidArgument(message=f"{key}不能小于等于0")
        rule_total_amount = sum(
            [(int(v["rank_max"]) - int(v["rank_min"]) + 1) * Decimal(v["rank_amount"])
             for v in rule_data]
        )
        total_amount = Decimal(kwargs["total_amount"])
        if total_amount < rule_total_amount:
            raise InvalidArgument(message='排名的送出数高于送币总数')
        return [{k: v(e[k]) for k, v in rule_dict.items()} for e in rule_data]


@ns.route('/activities/deposit')
@respond_with_code
class DepositListResource(Resource):
    marshal_fields = {
        'id': fx_fields.Integer,
        'activity_id': fx_fields.Integer,
        'name': fx_fields.String,
        'zendesk_url': fx_fields.String,
        'deposit_asset': fx_fields.String,
        'gift_asset': fx_fields.String,
        'type': EnumMarshalField(
            DepositActivity.Type, output_field_lower=False),
        'remark_format': fx_fields.String(
            attribute=lambda x: generator_rule_description(x)),
        'rule_data': JsonMarshalField(
            attribute=lambda x: DepositActivityRule.query.filter(
                DepositActivityRule.deposit_activity_id == x.id
            ).first().rule_data
        ),
        'left_amount': AmountField(attribute=lambda x: x.get_left_amount()),
        'total_amount': AmountField,
        'least_amount': AmountField,
        'start_time': TimestampMarshalField(attribute='started_at'),
        'end_time': TimestampMarshalField(attribute='ended_at'),
        'status': EnumMarshalField(
            DepositActivity.Status, output_field_lower=False),
    }

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-活动-充值送币活动列表"""
        query = DepositActivity.query.order_by(DepositActivity.id.desc())
        return query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        zendesk_url=fields.String(missing=None),
        deposit_asset=fields.String(
            required=True,
            validate=lambda x: x in list_all_assets()),
        gift_asset=fields.String(
            required=True,
            validate=lambda x: x in list_all_assets()),
        type=EnumField(DepositActivity.Type, required=True),
        rule_data=fields.Raw(required=True),
        least_amount=PositiveDecimalField(allow_zero=True, allow_none=True),
        total_amount=PositiveDecimalField(required=True),
        start_time=TimestampField(is_ms=True, required=True),
        end_time=TimestampField(is_ms=True, required=True),
    ))
    def post(cls, **kwargs):
        """运营-活动-创建充值送币活动"""
        if kwargs['end_time'] < kwargs['start_time']:
            raise InvalidArgument(message='活动结束时间必须大于开始时间')
        rule_data = check_rule(kwargs)

        activity = Activity()
        activity.name = kwargs['name']
        db.session.add(activity)
        db.session.commit()

        record = DepositActivity(
            activity_id=activity.id,
            zendesk_url=kwargs['zendesk_url'],
            name=kwargs['name'],
            deposit_asset=kwargs['deposit_asset'],
            gift_asset=kwargs['gift_asset'],
            type=kwargs['type'],
            total_amount=kwargs['total_amount'],
            least_amount=kwargs['least_amount'] or Decimal(),
            started_at=kwargs['start_time'],
            ended_at=kwargs['end_time'],
            status=DepositActivity.Status.SUSPENDED,
            updated_by=g.user.name_displayed
        )
        db.session.add(record)
        db.session.commit()
        record_rule = DepositActivityRule(
            deposit_activity_id=record.id,
            rule_data=json.dumps(rule_data, cls=JsonEncoder)
        )
        db.session.add(record_rule)
        db.session.commit()
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositActivity,
            detail=kwargs,
        )


# noinspection PyUnresolvedReferences
@ns.route('/activities/deposit/<int:id_>')
@respond_with_code
class DepositActivityResource(Resource):

    @classmethod
    def get(cls, id_):
        """运营-活动-充值活动统计信息"""
        record: DepositActivity = DepositActivity.query.get(id_)
        if not record:
            raise InvalidArgument(message='记录不存在')
        rank_query = DepositActivityRank.query.filter(
            DepositActivityRank.activity_id == record.activity_id
        )
        total_rank_count = rank_query.count()
        latest_rank: DepositActivityRank = rank_query.order_by(
            DepositActivityRank.rank_at.desc()
        ).first()
        latest_rank_at = latest_rank.rank_at if latest_rank else 0
        deposit_data = Deposit.query.filter(
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.asset == record.deposit_asset,
            or_(
                Deposit.status == Deposit.Status.CONFIRMING,
                Deposit.status == Deposit.Status.FINISHED,
                Deposit.status == Deposit.Status.TO_HOT
            ),
            Deposit.created_at >= record.started_at,
            Deposit.created_at < record.ended_at
        ).with_entities(
            func.sum(Deposit.amount).label('deposit_amount')
        ).first()
        total_deposit_amount = deposit_data.deposit_amount \
            if deposit_data.deposit_amount else Decimal()

        query = DepositActivity.query.filter(
            DepositActivity.type.in_([DepositActivity.Type.RANK,
                                      DepositActivity.Type.PARTITION]),
            DepositActivity.started_at <= now(),
            DepositActivity.status != DepositActivity.Status.DELETED
        ).order_by(
            DepositActivity.id
        )
        activities = {v.id: f'{v.id} {v.name}' for v in query}
        return dict(
            extra=dict(activities=activities),
            total_rank_count=total_rank_count,
            total_deposit_amount=total_deposit_amount,
            latest_rank_time=latest_rank_at,
            info=record
        )

    @classmethod
    @ns.use_kwargs(dict(
        zendesk_url=fields.String(missing=None),
        deposit_asset=fields.String(
            required=True,
            validate=lambda x: x in list_all_assets()),
        gift_asset=fields.String(
            required=True,
            validate=lambda x: x in list_all_assets()),
        type=EnumField(DepositActivity.Type, required=True),
        rule_data=fields.Raw(required=True),
        least_amount=PositiveDecimalField(allow_zero=True, allow_none=True),
        total_amount=PositiveDecimalField(required=True),
        start_time=TimestampField(is_ms=True, required=True),
        end_time=TimestampField(is_ms=True, required=True),
    ))
    def put(cls, id_, **kwargs):
        """运营-活动-编辑充值送币活动"""
        if kwargs['end_time'] < kwargs['start_time']:
            raise InvalidArgument(message='活动结束时间必须大于开始时间')
        record: DepositActivity = DepositActivity.query.get(id_)
        if not record:
            raise InvalidArgument(message='记录不存在')
        old_data = record.to_dict(enum_to_name=True)
        rule_data = check_rule(kwargs)

        record.zendesk_url = kwargs['zendesk_url']
        record.least_amount = kwargs['least_amount'] or Decimal()
        if deposit_asset := kwargs.get('deposit_asset'):
            record.deposit_asset = deposit_asset
        if gift_asset := kwargs.get('gift_asset'):
            record.gift_asset = gift_asset
        if type_ := kwargs.get('type'):
            record.type = type_
        if total_amount := kwargs.get('total_amount'):
            record.total_amount = total_amount
        if start_time := kwargs.get('start_time'):
            record.started_at = start_time
        if end_time := kwargs.get('end_time'):
            record.ended_at = end_time
        DepositActivityRule.query.filter(
            DepositActivityRule.deposit_activity_id == record.id
        ).update(
            dict(rule_data=json.dumps(rule_data, cls=JsonEncoder)),
            synchronize_session=False
        )
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositActivity,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
            special_data=dict(rule_data=rule_data),
        )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(DepositActivity.Status, required=True),
    ))
    def patch(cls, id_, **kwargs):
        """运营-活动-编辑充值送币状态"""
        record: DepositActivity = DepositActivity.query.get(id_)
        if not record:
            raise InvalidArgument(message='记录不存在')
        old_data = record.to_dict(enum_to_name=True)
        record.status = kwargs['status']
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositActivity,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )


# noinspection PyUnresolvedReferences
@ns.route('/activities/deposit-send-gift/<int:id_>')
@respond_with_code
class DepositSendGiftResource(Resource):

    @classmethod
    def post(cls, id_):
        """运营-活动-充值送币派奖"""
        record: DepositActivity = DepositActivity.query.filter(
            DepositActivity.id == id_,
            DepositActivity.status == DepositActivity.Status.PASSED,
            DepositActivity.type.in_([
                DepositActivity.Type.RANK, DepositActivity.Type.PARTITION]),
            DepositActivity.ended_at <= now()
        ).first()
        if not record:
            raise InvalidArgument(message='充值送币活动暂时无法进行派奖')
        with CacheLock(LockKeys.activity_deposit_send_gift(record.id),
                       wait=False):
            update_deposit_activity_rank_task(id_)
            query = DepositActivityRank.query.filter(
                DepositActivityRank.activity_id == record.activity_id
            ).order_by(
                DepositActivityRank.rank
            )
            record.status = DepositActivity.Status.FINISHED
            db.session.commit()
            for v in query:
                send_deposit_activity_gift_task.delay(id_, v.user_id)
        AdminOperationLog.new_send(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositActivity,
            detail=dict(id=id_),
        )


@ns.route('/activities/deposit-rank')
@respond_with_code
class DepositRankResource(Resource):
    marshal_fields = {
        'rank': fx_fields.Integer,
        'user_id': fx_fields.Integer,
        'deposit_amount': AmountField,
        'gift_amount': AmountField,
        'rank_at': TimestampMarshalField
    }

    @classmethod
    @ns.use_kwargs(dict(
        deposit_activity_id=fields.Integer(required=True),
        user_id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-活动-充值送币排行"""
        activity: DepositActivity = DepositActivity.query.filter(
            DepositActivity.id == kwargs['deposit_activity_id'],
            DepositActivity.type.in_([DepositActivity.Type.RANK,
                                      DepositActivity.Type.PARTITION]),
            DepositActivity.status != DepositActivity.Status.DELETED
        ).first()
        if not activity:
            raise InvalidArgument(message='活动不存在')
        rule: DepositActivityRule = DepositActivityRule.query.filter(
            DepositActivityRule.deposit_activity_id == activity.id
        ).first()
        if not rule:
            raise InvalidArgument(message='活动规则不存在')
        query = DepositActivityRank.query.filter(
            DepositActivityRank.activity_id == activity.activity_id
        ).order_by(
            DepositActivityRank.rank
        )
        if user_id := kwargs.get('user_id'):
            query = query.filter(DepositActivityRank.user_id == user_id)
        records = query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        user_ids = [item['user_id'] for item in records['data']]
        user_email_map = dict()
        for ids_ in batch_iter(user_ids, 1000):
            user_email = User.query.filter(
                User.id.in_(ids_)
            ).with_entities(User.id, User.email).all()
            user_email_map.update(dict(user_email))
        for item in records['data']:
            item['email'] = user_email_map[item['user_id']]
        return records


def check_trade_rule(kwargs):
    rule_data = kwargs['gift_rules']
    rule_dict = {
        "rank_min": int,
        "rank_max": int,
        "rank_amount": Decimal,
    }
    if len(rule_data) == 0:
        return []
    if 'rank_max' not in rule_data[0]:
        raise InvalidArgument
    last_rank_max = rule_data[0]['rank_max']
    for i, e in enumerate(rule_data):
        for key in rule_dict.keys():
            if key not in e:
                raise InvalidArgument(message='规则相关的配置项有问题')
            if not e[key] or Decimal(e[key]) <= 0:
                raise InvalidArgument(message=f"{key}不能为空或小于等于0")
        if i == 0:
            if e['rank_min'] != 1:
                raise InvalidArgument(message='规则相关的配置项有问题')
        if e['rank_min'] > e['rank_max']:
            raise InvalidArgument(message='规则相关的配置项有问题')
        if i > 0 and e['rank_min'] <= last_rank_max:
            raise InvalidArgument(message='规则相关的配置项有问题')
        last_rank_max = e['rank_max']

    rule_total_amount = sum(
        [(int(v["rank_max"]) - int(v["rank_min"]) + 1) * Decimal(v["rank_amount"]) for v in rule_data]
    )
    total_amount = Decimal(kwargs["gift_amount"])
    if total_amount < rule_total_amount:
        raise InvalidArgument(message='排名的送出数高于送币总数')
    max_split_reward_amount = kwargs.get('max_split_reward_amount')
    left_amount = total_amount - rule_total_amount
    if max_split_reward_amount:
        amount_ = min(left_amount, Decimal(rule_data[-1]['rank_amount']))
        if max_split_reward_amount > amount_:
            raise InvalidArgument(message=f'瓜分奖励须小于{amount_}')

    return [{k: v(e[k]) for k, v in rule_dict.items()} for e in rule_data]


@ns.route('/activities/trade/list')
@respond_with_code
class TradeRankActivityListResource(Resource):
    marshal_fields = {
        'id': fx_fields.Integer,
        'activity_id': fx_fields.Integer,
        'markets': JsonMarshalField,
        'exclude_markets': JsonMarshalField,
        'market_type': EnumMarshalField(
            TradeRankActivity.MarketType, output_field_lower=False),
        'gift_asset': fx_fields.String,
        'announce_url': fx_fields.String,
        'type': EnumMarshalField(
            TradeRankActivity.Type, output_field_lower=False),
        'gift_rules': JsonMarshalField,
        'left_amount': AmountField(attribute=lambda x: x.get_left_amount()),
        'gift_amount': AmountField,
        'least_trade_amount': AmountField,
        'max_split_reward_amount': AmountField,
        'start_time': TimestampMarshalField(attribute='started_at'),
        'end_time': TimestampMarshalField(attribute='ended_at'),
        'user_group_condition': JsonMarshalField,
        'cover': fx_fields.String,
        'cover_url': fx_fields.String(attribute=lambda x: AWSBucketPublic.get_file_url(x.cover)),
        'state': fx_fields.String,
        'status': EnumMarshalField(
            TradeRankActivity.Status, output_field_lower=False),
        'funding_source': EnumMarshalField(
            TradeRankActivity.FundingSource, output_field_lower=False),
    }

    STATES = {
        'PENDING': '未开始',
        'ONGOING': '进行中',
        'ENDED': '已结束'
    }

    ACTIVITY_TYPES = {
        TradeRankActivity.Type.SPOT_TRADE.name: '币币交易量排名',
        TradeRankActivity.Type.SPOT_NET_BUY.name: '币币净买入排名',
        TradeRankActivity.Type.PERPETUAL_TRADE.name: '合约交易量排名',
        TradeRankActivity.Type.PERPETUAL_INCOME.name: '合约收益额排名',
        TradeRankActivity.Type.PERPETUAL_INCOME_RATE.name: '合约收益率排名',
    }

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(TradeRankActivity.Type),
        gift_asset=fields.String,
        state=EnumField(list(STATES.keys())),
        status=EnumField(TradeRankActivity.Status),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-活动-交易送币活动列表"""
        query = TradeRankActivity.query.order_by(TradeRankActivity.id.desc())
        if state := kwargs.get('state'):
            now_ = now()
            if state == 'PENDING':
                query = query.filter(TradeRankActivity.started_at > now_)
            elif state == 'ONGOING':
                query = query.filter(and_(TradeRankActivity.started_at <= now_,
                                          TradeRankActivity.ended_at > now_))
            else:
                query = query.filter(TradeRankActivity.ended_at < now_)
        if gift_asset := kwargs.get('gift_asset'):
            query = query.filter(TradeRankActivity.gift_asset == gift_asset)
        if status := kwargs.get('status'):
            if status == TradeRankActivity.Status.ONLINE:
                query = query.filter(TradeRankActivity.status.in_(
                    [TradeRankActivity.Status.ONLINE, TradeRankActivity.Status.FINISHED]
                ))
            else:
                query = query.filter(TradeRankActivity.status == status)
        if type_ := kwargs.get('type'):
            query = query.filter(TradeRankActivity.type == type_)

        items = query_to_page(query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        ids = [item['id'] for item in items['data']]
        details = TradeRankActivityDetail.query.filter(
            TradeRankActivityDetail.trade_activity_id.in_(ids)
        ).all()

        join_users = TradeRankActivityJoinUser.query.filter(
            TradeRankActivityJoinUser.trade_activity_id.in_(ids)
        ).group_by(
            TradeRankActivityJoinUser.trade_activity_id
        ).with_entities(
            TradeRankActivityJoinUser.trade_activity_id,
            func.count('*')
        ).all()
        join_user_count_map = dict(join_users)

        stats = TradeRankActivityStatistics.query.filter(
            TradeRankActivityStatistics.trade_activity_id.in_(ids),
        ).with_entities(
            TradeRankActivityStatistics.trade_activity_id,
            TradeRankActivityStatistics.qualified_user_count,
            TradeRankActivityStatistics.trade_user_count
        ).all()
        stats_map = {item[0]: item[1] for item in stats}
        trade_user_count_map = {item[0]: item[2] for item in stats}

        detail_map = dict()
        for item in details:
            if item.trade_activity_id not in detail_map:
                detail_map[item.trade_activity_id] = dict()

            detail_map[item.trade_activity_id][item.lang.name] = item.title
        now_ts = current_timestamp(to_int=True)
        for item in items['data']:
            item['join_user_count'] = join_user_count_map.get(item['id'], 0)
            item['trade_user_count'] = trade_user_count_map.get(item['id'], 0)
            item['qualified_user_count'] = stats_map.get(item['id'], 0)
            item['titles'] = detail_map.get(item['id'])
            if item['titles']:
                item['title'] = item['titles'].get(Language.ZH_HANS_CN.name)
            if item['start_time'] > now_ts:
                item['state'] = cls.STATES['PENDING']
            elif item['start_time'] <= now_ts and item['end_time'] >= now_ts:
                item['state'] = cls.STATES['ONGOING']
            else:
                item['state'] = cls.STATES['ENDED']
        return dict(
            types=cls.ACTIVITY_TYPES,
            market_type=TradeRankActivity.MarketType,
            statuses={
                TradeRankActivity.Status.ONLINE.name: '已上架',
                TradeRankActivity.Status.PENDING.name: '未上架',
                TradeRankActivity.Status.DELETED.name: '已下架',
            },
            states=cls.STATES,
            langs={lang.name: ln.chinese for lang, ln in LANGUAGE_NAMES.items()},
            funding_sources=TradeRankActivity.FundingSource,
            items=items,
        )

    @classmethod
    @ns.use_kwargs(dict(
        markets=fields.List(fields.String, missing=None),
        exclude_markets=fields.List(fields.String, missing=None),
        market_type=EnumField(TradeRankActivity.MarketType, allow_none=True),
        gift_asset=fields.String(
            required=True),
        type=EnumField(TradeRankActivity.Type, required=True),
        gift_rules=fields.Raw(required=True),
        least_trade_amount=PositiveDecimalField(allow_zero=True, allow_none=True),
        announce_url=fields.String(),
        max_split_reward_amount=PositiveDecimalField,
        gift_amount=PositiveDecimalField(required=True),
        start_time=TimestampField(is_ms=True, required=True),
        end_time=TimestampField(is_ms=True, required=True),
        user_group_condition=fields.String(required=True),
        titles=fields.Dict(keys=EnumField([i.name for i in Language]), values=fields.String, required=True),
        cover=fields.String(required=True),
        funding_source=EnumField(TradeRankActivity.FundingSource, required=True),
    ))
    def post(cls, **kwargs):
        """运营-活动-创建交易送币活动"""
        if kwargs['end_time'] < kwargs['start_time']:
            raise InvalidArgument(message='活动结束时间必须大于开始时间')
        if kwargs['start_time'] < now() + timedelta(days=-3):
            raise InvalidArgument(message='活动开始时间应为3天内')
        rule_data = check_trade_rule(kwargs)
        markets = kwargs['markets']
        exclude_markets = kwargs['exclude_markets']
        if markets and exclude_markets:
            raise InvalidArgument(message='已经选择了活动市场不能再添加被排除市场！')
        # 币币净买入量要求所有市场交易币种一样
        if kwargs['type'] == TradeRankActivity.Type.SPOT_NET_BUY:
            if not markets:
                raise InvalidArgument
            market_data = MarketCache(markets[0]).dict
            activity_asset = market_data['base_asset']
            for market in markets[1:]:
                market_data = MarketCache(market).dict
                if market_data['base_asset'] != activity_asset:
                    raise InvalidArgument(message='市场选择错误')
        activity = Activity()
        activity.name = f'{kwargs["titles"][Language.ZH_HANS_CN.name]}_{current_timestamp(to_int=True)}'
        db.session.add(activity)
        db.session.commit()

        record = TradeRankActivity(
            activity_id=activity.id,
            type=kwargs['type'],
            gift_asset=kwargs['gift_asset'],
            markets=compact_json_dumps(markets) if markets else '',
            exclude_markets=compact_json_dumps(exclude_markets) if exclude_markets else '',
            market_type=kwargs.get('market_type'),
            gift_rules=json.dumps(rule_data, cls=JsonEncoder),
            gift_amount=kwargs['gift_amount'],
            left_gift_amount=kwargs['gift_amount'],
            announce_url=kwargs.get('announce_url', ''),
            least_trade_amount=kwargs['least_trade_amount'] or Decimal(),
            max_split_reward_amount=kwargs.get('max_split_reward_amount'),
            started_at=kwargs['start_time'],
            ended_at=kwargs['end_time'],
            cover=kwargs['cover'],
            status=TradeRankActivity.Status.PENDING,
            user_group_condition=kwargs['user_group_condition'],
            updated_by=g.user.id,
            funding_source=kwargs['funding_source'],
        )
        db.session.add(record)
        db.session.commit()
        for lang in Language:
            title = kwargs['titles'].get(lang.name)
            if not title:
                title = kwargs['titles'][Language.EN_US.name]
            detail = TradeRankActivityDetail(
                trade_activity_id=record.id,
                lang=lang,
                title=title,
            )
            db.session.add(detail)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TradeActivity,
            detail=kwargs,
        )


class ChannelActivityMixin:

    @classmethod
    def get_langs(cls):
        langs = OrderedDict()
        langs['Global'] = 'Global'
        langs.update({lang.name: ln.chinese for lang, ln in LANGUAGE_NAMES.items()})
        return langs

    @classmethod
    def get_platforms(cls):
        platforms = {i.name: i.value for i in ChannelRewardActivity.Platform}
        recs = ChannelRewardPlatform.query.all()
        platforms.update({i.value: i.value for i in recs})
        return platforms


@ns.route('/activities/channel/list')
@respond_with_code
class ChannelActivityListResource(Resource, ChannelActivityMixin):
    STATES = {
        'PENDING': '未开始',
        'ONGOING': '进行中',
        'UPLOAD_PENDING': '待上传',
        'AUDIT_REQUIRED': '待审核',
        'ENDED': '待发奖',
        'FINISHED': '已结束',
    }

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "name", Language.ZH_HANS_CN: "活动名称"},
        {"field": "languages_str", Language.ZH_HANS_CN: "活动语区"},
        {"field": "platforms_str", Language.ZH_HANS_CN: "平台类型"},
        {"field": "assets", Language.ZH_HANS_CN: "奖励币种"},
        {"field": "user_count_list", Language.ZH_HANS_CN: "有效获奖人数/总获奖人数"},
        {"field": "user_amount_list", Language.ZH_HANS_CN: "实际发放/发放总数量"},
        {"field": "state", Language.ZH_HANS_CN: "状态"},
        {"field": "started_at_str", Language.ZH_HANS_CN: "开始时间"},
        {"field": "ended_at_str", Language.ZH_HANS_CN: "结束时间"},
        {"field": "creator_name", Language.ZH_HANS_CN: "创建人"},
        {"field": "auditor_name", Language.ZH_HANS_CN: "审核人"},
        {"field": "rewarder_name", Language.ZH_HANS_CN: "发放人"},
        {"field": "reward_at_str", Language.ZH_HANS_CN: "发奖时间"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        languages=fields.String,
        platforms=fields.String,
        assets=fields.String,
        started_at=TimestampField(is_ms=True, to_hour=True),
        ended_at=TimestampField(is_ms=True, to_hour=True),
        state=EnumField(list(STATES.keys())),
        page=PageField(unlimited=True),
        limit=LimitField,
        export=fields.Boolean(missing=False),
        activity_url=fields.String(allow_none=True),
    ))
    def get(cls, **kwargs):
        """运营-活动-渠道奖励活动列表"""
        languages = set(json.loads(kwargs['languages']))
        platforms = set(json.loads(kwargs['platforms']))
        assets = set(json.loads(kwargs['assets']))
        query = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.status != ChannelRewardActivity.Status.DELETED,
        ).order_by(ChannelRewardActivity.id.desc())
        if state := kwargs.get('state'):
            now_ = now()
            if state == 'PENDING':
                query = query.filter(
                    ChannelRewardActivity.status == ChannelRewardActivity.Status.ONLINE,
                    ChannelRewardActivity.started_at > now_
                )
            elif state == 'ONGOING':
                query = query.filter(
                    ChannelRewardActivity.status == ChannelRewardActivity.Status.ONLINE,
                    and_(
                        ChannelRewardActivity.started_at <= now_,
                        ChannelRewardActivity.ended_at > now_
                    )
                )
            elif state == 'UPLOAD_PENDING':
                query = query.filter(
                    ChannelRewardActivity.status == ChannelRewardActivity.Status.ONLINE,
                    ChannelRewardActivity.ended_at < now_,
                    ChannelRewardActivity.report_at.is_(None)
                )
            elif state == 'AUDIT_REQUIRED':
                query = query.filter(
                    ChannelRewardActivity.status == ChannelRewardActivity.Status.ONLINE,
                    ChannelRewardActivity.ended_at < now_,
                    ChannelRewardActivity.report_at.isnot(None)
                )
            elif state == 'ENDED':
                query = query.filter(ChannelRewardActivity.status == ChannelRewardActivity.Status.AUDITED)
            elif state == 'FINISHED':
                query = query.filter(ChannelRewardActivity.status == ChannelRewardActivity.Status.FINISHED)

        if started_at := kwargs.get('started_at'):
            query = query.filter(ChannelRewardActivity.started_at >= started_at)
        if ended_at := kwargs.get('ended_at'):
            query = query.filter(ChannelRewardActivity.ended_at < ended_at)
        if activity_url := kwargs.get('activity_url'):
            query = query.filter(ChannelRewardActivity.activity_url == activity_url)
        items = query.all()

        if languages:
            items = [i for i in items if set(json.loads(i.languages)) & languages]
        if platforms:
            items = [i for i in items if set(json.loads(i.platforms)) & platforms]
        if assets:
            items = [i for i in items if {i['asset'] for i in json.loads(i.gift_rules)} & assets]

        ids = [item.id for item in items]

        join_users = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(ids),
            ChannelRewardHistory.status != ChannelRewardHistory.Status.DELETED,
        ).group_by(
            ChannelRewardHistory.channel_reward_activity_id,
            ChannelRewardHistory.asset,
            ChannelRewardHistory.status,
        ).with_entities(
            ChannelRewardHistory.channel_reward_activity_id,
            ChannelRewardHistory.asset,
            ChannelRewardHistory.status,
            func.count(ChannelRewardHistory.id).label('user_count'),
            func.sum(ChannelRewardHistory.amount).label('amount'),
        ).all()
        join_user_count_map = defaultdict(lambda: defaultdict(dict))
        join_user_amount_map = defaultdict(lambda: defaultdict(dict))
        total_user_count = valid_user_count = 0
        asset_data = defaultdict(Decimal)

        for row in join_users:
            status = row.status
            count = row.user_count
            amount = row.amount
            join_user_count_map[row.channel_reward_activity_id][row.asset][status] = count
            join_user_amount_map[row.channel_reward_activity_id][row.asset][status] = amount
            total_user_count += count
            if status == ChannelRewardHistory.Status.VALID:
                valid_user_count += count
                asset_data[row.asset] += amount

        reported_list = [i.channel_reward_activity_id for i in ChannelRewardHistory.query.filter(
            ChannelRewardHistory.status != ChannelRewardHistory.Status.DELETED
        ).with_entities(
            ChannelRewardHistory.channel_reward_activity_id.distinct().label('channel_reward_activity_id')
        ).all()]
        activities = {i.id: i.name for i in ChannelRewardActivity.query.filter(
            ChannelRewardActivity.status == ChannelRewardActivity.Status.ONLINE,
        )}

        result = []
        op_user_ids = set()
        for i in items:
            if i.creator:
                op_user_ids.add(i.creator)
            if i.auditor:
                op_user_ids.add(i.auditor)
            if i.rewarder:
                op_user_ids.add(i.rewarder)
        admin_user_name_map = get_admin_user_name_map(list(op_user_ids))
        langs = cls.get_langs()
        push_msg_langs = {lang.name: ln.chinese for lang, ln in LANGUAGE_NAMES.items()}
        platforms = cls.get_platforms()
        now_ = now()
        for row in items:
            item = row.to_dict(enum_to_name=True)
            gift_rules = json.loads(item['gift_rules'])
            gift_rule_map = {i['asset']: i['amount'] for i in gift_rules}
            assets = [i['asset'] for i in gift_rules]
            user_count_data = join_user_count_map[item['id']]
            user_amount_data = join_user_amount_map[item['id']]
            user_count_list = []
            user_amount_list = []
            for asset in assets:
                qualified_user_count = user_count_data[asset].get(ChannelRewardHistory.Status.VALID, 0)
                join_user_count = sum(user_count_data[asset].values())
                user_gift_amount = user_amount_data[asset].get(ChannelRewardHistory.Status.VALID, 0)
                user_count_list.append(dict(
                    qualified_user_count=qualified_user_count,
                    join_user_count=join_user_count))
                user_amount_list.append(dict(
                    user_gift_amount=amount_to_str(user_gift_amount),
                    user_total_amount=gift_rule_map[asset],
                    asset=asset,
                ))
            item['user_count_list'] = '\n'.join(
                [f"{i['qualified_user_count']}/{i['join_user_count']}" for i in user_count_list])
            item['user_amount_list'] = '\n'.join(
                [f"{i['user_gift_amount']}/{i['user_total_amount']} {i['asset']}" for i in user_amount_list]
            )
            item['languages'] = json.loads(item['languages'])
            item['platforms'] = json.loads(item['platforms'])
            item['started_at_str'] = item['started_at'].strftime("%Y-%m-%d %H:%M:%S")
            item['ended_at_str'] = item['ended_at'].strftime("%Y-%m-%d %H:%M:%S")
            item['reward_at_str'] = item['reward_at'].strftime("%Y-%m-%d %H:%M:%S") if item['reward_at'] else '-'
            item['languages_str'] = ','.join([langs[i] for i in item['languages']])
            item['platforms_str'] = ','.join([platforms[i] for i in item['platforms']])
            item['gift_rules'] = gift_rules
            item['creator_name'] = admin_user_name_map.get(item['creator'], '-')
            item['auditor_name'] = admin_user_name_map.get(item['auditor'], '-')
            item['rewarder_name'] = admin_user_name_map.get(item['rewarder'], '-')
            item['assets'] = ','.join(assets)
            item['amounts'] = [i['amount'] for i in item['gift_rules']]
            if item['status'] == ChannelRewardActivity.Status.FINISHED.name:
                item['state'] = cls.STATES['FINISHED']
            if item['status'] == ChannelRewardActivity.Status.AUDITED.name:
                item['state'] = cls.STATES['ENDED']
            if item['status'] == ChannelRewardActivity.Status.ONLINE.name:
                if item['started_at'] > now_:
                    item['state'] = cls.STATES['PENDING']
                elif item['started_at'] <= now_ <= item['ended_at']:
                    item['state'] = cls.STATES['ONGOING']
                elif item['report_at'] is None:
                    item['state'] = cls.STATES['UPLOAD_PENDING']
                else:
                    item['state'] = cls.STATES['AUDIT_REQUIRED']
            result.append(item)

        if kwargs.get('export'):
            return export_xlsx(filename='channel-activities-list',
                               data_list=result,
                               export_headers=cls.export_headers)

        return dict(
            platforms=platforms,
            reward_types=ChannelRewardActivity.RewardType,
            message_types=ChannelRewardActivity.MessageType,
            states=cls.STATES,
            langs=langs,
            push_msg_langs=push_msg_langs,
            reported_list=reported_list,
            market_admin_user_id=config['MARKET_ADMIN_USER_ID'],
            activities=activities,
            items=list_to_page(result, kwargs['page'], kwargs['limit']),
            total_user_count=total_user_count,
            qualified_user_count=valid_user_count,
            asset_list=asset_data,
            total=len(result),
        )

    @classmethod
    @ns.use_kwargs(dict(
        languages=fields.List(fields.String, missing=None),
        platforms=fields.List(fields.String, missing=None),
        name=fields.String(required=True),
        activity_url=fields.String(missing=''),
        reward_url=fields.String(missing=''),
        remark=fields.String(missing=''),
        message_temp_id=fields.Integer(missing=0),
        reward_type=EnumField(ChannelRewardActivity.RewardType, required=True),
        message_type=EnumField(ChannelRewardActivity.MessageType, required=True),
        gift_rules=fields.Raw(required=True),
        started_at=TimestampField(is_ms=True, to_hour=True, required=True),
        ended_at=TimestampField(is_ms=True, to_hour=True, required=True),
    ))
    def post(cls, **kwargs):
        """运营-活动-渠道奖励送币活动"""
        if kwargs['ended_at'] < kwargs['started_at']:
            raise InvalidArgument(message='活动结束时间必须大于开始时间')
        gift_rules = kwargs['gift_rules']
        languages = kwargs['languages']
        platforms = kwargs['platforms']
        activity = Activity()
        activity.name = f'{kwargs["name"]}_{current_timestamp(to_int=True)}'
        db.session.add(activity)
        db.session.flush()

        record = ChannelRewardActivity(
            activity_id=activity.id,
            reward_type=kwargs['reward_type'],
            message_type=kwargs['message_type'],
            message_temp_id=kwargs['message_temp_id'],
            activity_url=kwargs['activity_url'],
            reward_url=kwargs['reward_url'],
            remark=kwargs['remark'],
            name=kwargs['name'],
            languages=compact_json_dumps(languages),
            platforms=compact_json_dumps(platforms),
            gift_rules=json.dumps(gift_rules, cls=JsonEncoder),
            started_at=kwargs['started_at'],
            ended_at=kwargs['ended_at'],
            status=ChannelRewardActivity.Status.ONLINE,
            updated_by=g.user.id,
            creator=g.user.id,
        )
        db.session.add(record)
        db.session.commit()

        kwargs['activity_id'] = activity.id
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ChannelActivity,
            detail=kwargs,
        )


@ns.route('/activities/channel-reward/<int:id_>')
@respond_with_code
class ChannelRewardActivityDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        languages=fields.List(fields.String, missing=None),
        platforms=fields.List(fields.String, missing=None),
        name=fields.String(required=True),
        reward_type=EnumField(ChannelRewardActivity.RewardType, required=True),
        message_type=EnumField(ChannelRewardActivity.MessageType, required=True),
        gift_rules=fields.Raw(required=True),
        activity_url=fields.String(missing=''),
        reward_url=fields.String(missing=''),
        remark=fields.String(missing=''),
        message_temp_id=fields.Integer(missing=0),
        started_at=TimestampField(is_ms=True, to_hour=True, required=True),
        ended_at=TimestampField(is_ms=True, to_hour=True, required=True),
    ))
    def put(cls, id_, **kwargs):
        """运营-活动-编辑渠道奖励送币活动"""
        if kwargs['ended_at'] < kwargs['started_at']:
            raise InvalidArgument(message='活动结束时间必须大于开始时间')
        gift_rules = kwargs['gift_rules']
        languages = kwargs['languages']
        platforms = kwargs['platforms']
        record = ChannelRewardActivity.query.get(id_)
        old_data = record.to_dict(enum_to_name=True)
        record.reward_type = kwargs['reward_type']
        record.message_type = kwargs['message_type']
        record.name = kwargs['name']
        record.message_temp_id = kwargs['message_temp_id']
        record.activity_url = kwargs['activity_url']
        record.reward_url = kwargs['reward_url']
        record.remark = kwargs['remark']
        record.languages = compact_json_dumps(languages)
        record.platforms = compact_json_dumps(platforms)
        record.gift_rules = json.dumps(gift_rules, cls=JsonEncoder)
        record.started_at = kwargs['started_at']
        record.ended_at = kwargs['ended_at']
        record.updated_by = g.user.id
        db.session.add(record)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ChannelActivity,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )

    @classmethod
    def patch(cls, id_, **kwargs):
        """运营-活动-编辑渠道奖励状态"""
        record: ChannelRewardActivity = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id == id_,
            ChannelRewardActivity.creator == g.user.id,
            ChannelRewardActivity.status.in_([
                ChannelRewardActivity.Status.ONLINE
            ])
        ).first()
        if not record:
            raise InvalidArgument(message='不支持删除其他人创建的记录')
        old_data = record.to_dict(enum_to_name=True)
        record.status = ChannelRewardActivity.Status.DELETED
        record.updated_by = g.user.id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ChannelActivity,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )

    @classmethod
    def delete(cls, id_):
        """运营-活动-删除渠道奖励活动"""
        record: ChannelRewardActivity = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id == id_,
            ChannelRewardActivity.status != ChannelRewardActivity.Status.DELETED,
        ).first()
        if not record:
            raise InvalidArgument(message='记录不存在')
        if record.status == ChannelRewardActivity.Status.FINISHED:
            raise InvalidArgument(message='渠道奖励活动已完成，无法删除')
        record.status = ChannelRewardActivity.Status.DELETED
        record.updated_by = g.user.id
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ChannelActivity,
            detail=dict(id=id_),
        )


@ns.route('/activities/channel-reward/users')
@respond_with_code
class ChannelRewardActivityJoinUsersResource(Resource, ChannelActivityMixin):

    @classmethod
    def get_user_account_name_map(cls, user_ids: list[int]) -> dict[int, str]:
        res = dict()
        for ids_ in batch_iter(user_ids, 1000):
            for ue in UserExtra.query.filter(
                    UserExtra.user_id.in_(ids_),
            ).all():
                res[ue.user_id] = ue.account_name
        return res

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        assets=fields.String,
        channel_reward_activity_id=fields.Integer,
        status=EnumField(ChannelRewardHistory.Status),
        page=PageField(unlimited=True),
        limit=LimitField(max_limit=3000),
        languages=fields.String,
        reward_start_time=TimestampField(is_ms=True),
        reward_end_time=TimestampField(is_ms=True),
        activity_url=fields.String(allow_none=True),
    ))
    def get(cls, **kwargs):
        """
        活动-渠道活动-参与用户
        """
        languages = json.loads(kwargs.get('languages', '[]'))
        if languages:
            return cls.get_language_result(kwargs)
        else:
            return cls.get_activity_result(kwargs)

    @classmethod
    def get_language_result(cls, kwargs):
        activity_query = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.status != ChannelRewardActivity.Status.DELETED
        )
        if activity_id := kwargs.get('channel_reward_activity_id'):
            activity_query = activity_query.filter(
                ChannelRewardActivity.id == activity_id
            )
        if reward_start_time := kwargs.get('reward_start_time'):
            activity_query = activity_query.filter(
                ChannelRewardActivity.reward_at >= reward_start_time,
            )
        if reward_end_time := kwargs.get('reward_end_time'):
            activity_query = activity_query.filter(
                ChannelRewardActivity.reward_at <= reward_end_time,
            )
        if activity_url := kwargs.get('activity_url'):
            activity_query = activity_query.filter(ChannelRewardActivity.activity_url == activity_url)
        recs = activity_query.order_by(ChannelRewardActivity.id.desc()).all()
        activity_dic = dict()
        activity_ids = []
        query_languages = set(json.loads(kwargs['languages']))
        for activity in recs:
            langs = set(json.loads(activity.languages))
            if langs & query_languages:
                activity_ids.append(activity.id)
                activity_dic[activity.id] = activity
        query = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(activity_ids),
            ChannelRewardHistory.status != ChannelRewardHistory.Status.DELETED
        ).order_by(ChannelRewardHistory.channel_reward_activity_id.desc(), ChannelRewardHistory.id.desc())
        if assets := json.loads(kwargs.get('assets', '[]')):
            query = query.filter(ChannelRewardHistory.asset.in_(assets))
        if user_id := kwargs.get('user_id'):
            query = query.filter(ChannelRewardHistory.user_id == user_id)
        if status := kwargs.get('status'):
            query = query.filter(ChannelRewardHistory.status == status)
        records = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        rewarders = {activity.rewarder for activity in activity_dic.values() if
                     activity.status == ChannelRewardActivity.Status.FINISHED}
        admin_user_name_map = get_admin_user_name_map(rewarders)

        history_list = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(activity_ids),
            ChannelRewardHistory.status != ChannelRewardHistory.Status.DELETED
        ).all()

        frequently_and_mul_lang_rewarders = set()
        total_user_count = 0
        valid_user_count = 0
        asset_data = defaultdict(Decimal)
        for row in history_list:
            remark = row.remark
            if ChannelRewardHistory.FREQUENTLY_GIFT_USERS in remark or ChannelRewardHistory.MULTI_LANG_GIFT_USERS in remark:
                frequently_and_mul_lang_rewarders.add(row.user_id)
            asset_data[row.asset] += row.amount
            total_user_count += 1
            if row.status == ChannelRewardHistory.Status.VALID:
                valid_user_count += 1

        user_ids = [i.user_id for i in records.items]
        user_account_name_map = cls.get_user_account_name_map(user_ids)
        finished_activity_list = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.status == ChannelRewardActivity.Status.FINISHED
        ).all()
        finished_activity_ids = [i.id for i in finished_activity_list]
        user_reward_list = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(finished_activity_ids),
            ChannelRewardHistory.user_id.in_(user_ids),
            ChannelRewardHistory.status == ChannelRewardHistory.Status.VALID
        ).all()
        user_reward_activity_map = defaultdict(set)
        for row in user_reward_list:
            user_reward_activity_map[row.user_id].add(row.channel_reward_activity_id)
        res = []
        for item in records.items:
            item = item.to_dict()
            activity = activity_dic[item['channel_reward_activity_id']]
            rewarder_name = admin_user_name_map.get(activity.rewarder)
            item.update({
                'rewarder_name': rewarder_name,
                'rewarder': activity.rewarder,
                'activity_name': activity.name,
                'received_channel_count': len(user_reward_activity_map[item['user_id']]),
                'activity_url': activity.activity_url,
                'reward_at': activity.reward_at,
                'account_name': user_account_name_map.get(item['user_id'], '-'),
            })
            res.append(item)
        return dict(
            items=res,
            total=records.total,
            total_count=total_user_count,
            qualified_count=valid_user_count,
            frequently_and_mul_lang_rewarders_count=len(frequently_and_mul_lang_rewarders),
            activities={i.id: i.name for i in ChannelRewardActivity.query},
            asset_list=asset_data,
            is_language_query=True,
            is_single_activity=False,
            langs=cls.get_langs(),
            channel_reward_activity_id=activity_id
        )

    @classmethod
    def get_activity_result(cls, kwargs):
        id_ = kwargs.get('channel_reward_activity_id')
        if not id_:
            return cls.get_multi_activity_query_res(kwargs)
        else:
            return cls.get_single_activity_query_res(kwargs)

    @classmethod
    def get_single_activity_query_res(cls, kwargs):
        id_ = kwargs['channel_reward_activity_id']
        activity: ChannelRewardActivity = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id == id_,
        ).first()

        if not activity:
            raise InvalidArgument(message='活动不存在')
        if activity_url := kwargs.get('activity_url'):
            if activity.activity_url != activity_url:
                return dict(
                    is_single_activity=True,
                    is_language_query=False,
                    items=[],
                    total=0,
                    total_count=0,
                    qualified_count=0,
                    activities={i.id: i.name for i in ChannelRewardActivity.query},
                    asset_list=[],
                    channel_reward_activity_id=activity.id,
                    can_gift=False,
                    can_audit=False,
                    status=activity.status.name,
                    langs=cls.get_langs(),
                )
        query = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id == activity.id,
            ChannelRewardHistory.status != ChannelRewardHistory.Status.DELETED,
        )
        if assets := json.loads(kwargs.get('assets', '[]')):
            query = query.filter(ChannelRewardHistory.asset.in_(assets))
        if user_id := kwargs.get('user_id'):
            query = query.filter(ChannelRewardHistory.user_id == user_id)
        if status := kwargs.get('status'):
            query = query.filter(ChannelRewardHistory.status == status)
        records = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        res = []
        rewarder_name = rewarder = None
        if activity.status is ChannelRewardActivity.Status.FINISHED:
            admin_user_name_map = get_admin_user_name_map([activity.rewarder])
            rewarder_name = admin_user_name_map.get(activity.rewarder, '-')
            rewarder = activity.rewarder
        user_ids = [i.user_id for i in records.items]
        user_account_name_map = cls.get_user_account_name_map(user_ids)
        finished_activity_list = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.status == ChannelRewardActivity.Status.FINISHED
        ).all()
        finished_activity_ids = [i.id for i in finished_activity_list]
        user_reward_list = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(finished_activity_ids),
            ChannelRewardHistory.user_id.in_(user_ids),
            ChannelRewardHistory.status == ChannelRewardHistory.Status.VALID
        ).all()
        user_reward_activity_map = defaultdict(set)
        for row in user_reward_list:
            user_reward_activity_map[row.user_id].add(row.channel_reward_activity_id)
        for item in records.items:
            item = item.to_dict()
            item.update({
                'rewarder_name': rewarder_name,
                'rewarder': rewarder,
                'activity_name': activity.name,
                'received_channel_count': len(user_reward_activity_map[item['user_id']]),
                'activity_url': activity.activity_url,
                'reward_at': activity.reward_at,
                'account_name': user_account_name_map.get(item['user_id'], '-'),
            })
            res.append(item)

        can_gift = True if activity.status == ChannelRewardActivity.Status.AUDITED else False
        if activity.status == ChannelRewardActivity.Status.ONLINE and activity.ended_at < now() and activity.is_checked:
            can_audit = True
        else:
            can_audit = False

        history_list = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id == activity.id,
            ChannelRewardHistory.status.in_([ChannelRewardHistory.Status.VALID,
                                             ChannelRewardHistory.Status.INVALID, ]),
        ).all()
        total_user_count = 0
        valid_user_count = 0
        asset_data = defaultdict(Decimal)
        for row in history_list:
            asset_data[row.asset] += row.amount
            total_user_count += 1
            if row.status == ChannelRewardHistory.Status.VALID:
                valid_user_count += 1

        return dict(
            is_single_activity=True,
            is_language_query=False,
            items=res,
            total=records.total,
            total_count=total_user_count,
            qualified_count=valid_user_count,
            activities={i.id: i.name for i in ChannelRewardActivity.query},
            asset_list=asset_data,
            channel_reward_activity_id=activity.id,
            can_gift=can_gift,
            can_audit=can_audit,
            status=activity.status.name,
            langs=cls.get_langs(),
        )

    @classmethod
    def get_multi_activity_query_res(cls, kwargs):
        activity_query = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.status != ChannelRewardActivity.Status.DELETED,
        )
        if reward_start_time := kwargs.get('reward_start_time'):
            activity_query = activity_query.filter(
                ChannelRewardActivity.reward_at >= reward_start_time,
            )
        if reward_end_time := kwargs.get('reward_end_time'):
            activity_query = activity_query.filter(
                ChannelRewardActivity.reward_at <= reward_end_time,
            )
        if activity_url := kwargs.get('activity_url'):
            activity_query = activity_query.filter(ChannelRewardActivity.activity_url == activity_url)
        recs = activity_query.order_by(ChannelRewardActivity.id.desc()).all()
        if not recs:
            return dict(
                is_single_activity=False,
                is_language_query=False,
                can_gift=False,
                can_audit=False,
                activities={i.id: i.name for i in ChannelRewardActivity.query},
                langs=cls.get_langs()
            )
        activity_dic = dict()
        activity_ids = []
        for activity in recs:
            activity_ids.append(activity.id)
            activity_dic[activity.id] = activity

        query = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(activity_ids),
            ChannelRewardHistory.status != ChannelRewardHistory.Status.DELETED
        ).order_by(ChannelRewardHistory.channel_reward_activity_id.desc(), ChannelRewardHistory.id.desc())
        if assets := json.loads(kwargs.get('assets', '[]')):
            query = query.filter(ChannelRewardHistory.asset.in_(assets))
        if user_id := kwargs.get('user_id'):
            query = query.filter(ChannelRewardHistory.user_id == user_id)
        if status := kwargs.get('status'):
            query = query.filter(ChannelRewardHistory.status == status)
        records = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        rewarders = {activity.rewarder for activity in activity_dic.values() if
                     activity.status == ChannelRewardActivity.Status.FINISHED}
        admin_user_name_map = get_admin_user_name_map(rewarders)
        user_ids = [i.user_id for i in records.items]
        user_account_name_map = cls.get_user_account_name_map(user_ids)
        finished_activity_list = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.status == ChannelRewardActivity.Status.FINISHED
        ).all()
        finished_activity_ids = [i.id for i in finished_activity_list]
        user_reward_list = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(finished_activity_ids),
            ChannelRewardHistory.user_id.in_(user_ids),
            ChannelRewardHistory.status == ChannelRewardHistory.Status.VALID
        ).all()
        user_reward_activity_map = defaultdict(set)
        for row in user_reward_list:
            user_reward_activity_map[row.user_id].add(row.channel_reward_activity_id)
        res = []
        for item in records.items:
            item = item.to_dict()
            activity = activity_dic[item['channel_reward_activity_id']]
            rewarder_name = admin_user_name_map.get(activity.rewarder)
            item.update({
                'rewarder_name': rewarder_name,
                'rewarder': activity.rewarder,
                'activity_name': activity.name,
                'received_channel_count': len(user_reward_activity_map[item['user_id']]),
                'activity_url': activity.activity_url,
                'reward_at': activity.reward_at,
                'account_name': user_account_name_map.get(item['user_id'], '-'),
            })
            res.append(item)
        return dict(
            items=res,
            total=records.total,
            is_single_activity=False,
            is_language_query=False,
            can_gift=False,
            can_audit=False,
            activities={i.id: i.name for i in ChannelRewardActivity.query},
            langs=cls.get_langs()
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer,
        channel_reward_activity_id=fields.Integer,
        status=EnumField(ChannelRewardHistory.Status),
        remark=fields.String(missing=''),
    ))
    def put(cls, **kwargs):
        """
        活动-渠道活动-编辑用户状态
        """
        row = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.id == kwargs['id'],
            ChannelRewardHistory.channel_reward_activity_id == kwargs['channel_reward_activity_id'],
            ChannelRewardHistory.status != ChannelRewardHistory.Status.DELETED,
        ).first()

        if not row:
            raise InvalidArgument(message='用户记录不存在')

        if not row.user_id and kwargs['status'] == ChannelRewardHistory.Status.VALID:
            raise InvalidArgument(message='非Coinex无法修改状态')

        old_data = row.to_dict(enum_to_name=True)

        activity = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id == row.channel_reward_activity_id
        ).first()
        if not activity:
            raise InvalidArgument(message='活动不存在')

        if activity.status in [ChannelRewardActivity.Status.AUDITED, ChannelRewardActivity.Status.FINISHED]:
            if row.status != kwargs['status']:
                raise InvalidArgument(message='活动已审核无法修改状态')

        row.remark = kwargs['remark']
        row.status = kwargs['status']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ChannelActivityUser,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )


@ns.route('/activities/channel-reward/platform')
@respond_with_code
class ActivityChannelPlatformResource(Resource, ChannelActivityMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            value=fields.String(required=True)
        )
    )
    def post(cls, **kwargs):
        """活动-渠道活动-新增平台"""
        val = kwargs['value']
        if ChannelRewardPlatform.query.filter(
                ChannelRewardPlatform.value == val
        ).first():
            raise InvalidArgument(message='该平台已存在')
        db.session_add_and_commit(
            ChannelRewardPlatform(
                value=val
            )
        )
        return cls.get_platforms()


@ns.route('/activities/channel-reward/template')
@respond_with_code
class ChannelRewardActivityTemplateResource(Resource):
    export_headers = (
        {'field': 'email', Language.ZH_HANS_CN: '邮箱', Language.EN_US: 'Email'},
        {'field': 'user_id', Language.ZH_HANS_CN: 'UID', Language.EN_US: 'UID'},
        {'field': 'account_name', Language.ZH_HANS_CN: '账户名', Language.EN_US: 'Account Name'},
        {'field': 'asset', Language.ZH_HANS_CN: '币种', Language.EN_US: 'Asset'},
        {'field': 'amount', Language.ZH_HANS_CN: '数量', Language.EN_US: 'Amount'},
        {'field': 'activity_name', Language.ZH_HANS_CN: '活动名称（小语种）', Language.EN_US: 'Activity Name'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注', Language.EN_US: 'Remark'},
    )

    @classmethod
    def get(cls):
        """
        活动-渠道活动-模板下载
        """
        return export_xlsx(
            filename='channel-reward-activity-update-template',
            data_list=[],
            export_headers=cls.export_headers
        )


@ns.route('/activities/channel-reward/<int:channel_reward_activity_id>/batch')
@respond_with_code
class ChannelRewardActivityBatchResource(Resource):

    @classmethod
    def check_email(cls, email: str):
        if not validate_email(email):
            raise InvalidArgument(message=f'邮箱格式错误, email: {email}')

    @classmethod
    @require_admin_webauth_token
    def post(cls, channel_reward_activity_id):
        """
        活动-渠道奖励-活动用户批量上传
        """
        if not (file := request.files.get('file')):
            raise InvalidArgument
        activity = ChannelRewardActivity.query.get(channel_reward_activity_id)
        if not activity:
            raise InvalidArgument(message="活动不存在")
        if activity.status == ChannelRewardActivity.Status.AUDITED:
            raise InvalidArgument(message="活动已审核")
        if activity.status == ChannelRewardActivity.Status.FINISHED:
            raise InvalidArgument(message="活动已结束")
        if activity.started_at >= now():
            raise InvalidArgument(message="活动未开始")

        rows = get_table_rows(file, ['email', 'user_id', 'account_name', "asset", "amount", "activity_name", "remark"])
        email_row_map = {}
        names_row_map = {}
        user_ids = []
        for idx, row in enumerate(rows):
            email = row['email'].lower() if row['email'] else None
            user_id = row['user_id']
            account_name = row['account_name']
            if not any([email, user_id, account_name]):
                raise InvalidArgument(message=f'第 {idx+2} 行账户名/UID/邮箱至少填入一个')
            if email:
                cls.check_email(email)
                email_row_map[email] = idx
            elif user_id:
                user_ids.append(user_id)
            else:
                names_row_map[account_name] = idx

        user_email_map = {}
        if email_row_map:
            query = User.query.filter(
                User.email.in_(list(email_row_map.keys()))
            ).with_entities(User.id, User.email).all()
            # 补充 user_id
            for item in query:
                _id = item.id
                email = item.email.lower() if item.email else ""
                user_email_map[_id] = email
                rows[email_row_map[email]]["user_id"] = _id

        if names_row_map:
            e_query = UserExtra.query.filter(
                UserExtra.account_name.in_(list(names_row_map.keys()))
            ).with_entities(UserExtra.user_id, UserExtra.account_name).all()
            user_ids.extend([i.user_id for i in e_query])
            # 补充 user_id
            for item in e_query:
                user_id, account_name = item.user_id, item.account_name
                rows[names_row_map[account_name]]["user_id"] = user_id
                user_ids.append(user_id)

        if user_ids:
            query = User.query.filter(
                User.id.in_(user_ids)
            ).with_entities(User.id, User.email).all()
            user_email_map.update({i.id: i.email.lower() if i.email else "" for i in query})

        user_asset_map = defaultdict(lambda: defaultdict(Decimal))
        fail_row_list = []
        for row in rows:
            email = row['email']
            user_id = row['user_id']
            asset = row['asset']
            account_name = row['account_name']
            if user_id not in user_email_map:
                fail_row_list.append(row)
                continue
            amount = quantize_amount(row['amount'], 8)
            err_str = f'用户邮箱: {email}, UID: {user_id} 账户名: {account_name}, 币种: {asset}, 数量: {amount}'
            if not user_email_map[user_id]:
                raise InvalidArgument(message=f'{err_str} 邮箱不存在')
            if amount <= 0:
                raise InvalidArgument(message=f'{err_str} 小于等于0')
            if user_id in user_asset_map[asset]:
                raise InvalidArgument(message=f'{err_str} 存在多条记录')
            user_asset_map[asset][user_id] = amount
        gift_rule_map = {i['asset']: Decimal(i['amount']) for i in json.loads(activity.gift_rules)}
        for asset, user_amount in user_asset_map.items():
            if asset not in gift_rule_map:
                raise InvalidArgument(message=f'币种:{asset}不属于活动奖励币种')
            if sum(user_amount.values()) > gift_rule_map[asset]:
                raise InvalidArgument(message=f'币种:{asset}奖励数量超过活动币种奖励数量{gift_rule_map[asset]}')
        total = 0
        ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id == channel_reward_activity_id
        ).update(
            {'status': ChannelRewardHistory.Status.DELETED},
            synchronize_session=False
        )
        activity.report_at = now()
        activity.is_checked = False
        activity.business_name = rows[0]['activity_name']
        db.session.flush()
        for row in rows:
            user_id = row['user_id']
            if user_id in user_email_map:
                db.session.add(
                    ChannelRewardHistory(
                        channel_reward_activity_id=channel_reward_activity_id,
                        user_id=user_id,
                        email=user_email_map[user_id],
                        asset=row['asset'],
                        amount=row['amount'],
                        status=ChannelRewardHistory.Status.VALID,
                    )
                )
            elif row['email']:
                db.session.add(
                    ChannelRewardHistory(
                        channel_reward_activity_id=channel_reward_activity_id,
                        email=row['email'],
                        asset=row['asset'],
                        amount=row['amount'],
                        status=ChannelRewardHistory.Status.INVALID,
                        remark='非Coinex用户',
                    )
                )
            total += 1
        db.session.commit()

        return dict(
            total=total,
        )


class ChannelRewardMixin:

    @classmethod
    def query_reward_asset_amount(cls, activity_id):
        model = ChannelRewardHistory
        rows = model.query.filter(
            model.channel_reward_activity_id == activity_id,
            model.status == model.Status.VALID,
        ).with_entities(
            model.asset,
            func.sum(model.amount).label('amount'),
        ).group_by(
            model.asset,
        ).all()
        return rows

    @classmethod
    def get_reward_asset_amount_map(cls, activity_id):
        rows = cls.query_reward_asset_amount(activity_id)
        asset_amount_map = defaultdict(Decimal)
        for row in rows:
            asset_amount_map[row.asset] = row.amount

        return asset_amount_map


@ns.route('/activities/channel-reward/<int:id_>/audit')
@respond_with_code
class ChannelRewardAuditResource(ChannelRewardMixin, Resource):

    @classmethod
    @require_admin_webauth_token
    def post(cls, id_):
        """运营-活动-渠道活动审核"""
        record: ChannelRewardActivity = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id == id_,
            ChannelRewardActivity.status == ChannelRewardActivity.Status.ONLINE,
        ).first()
        if not record:
            raise InvalidArgument(message='活动不存在')
        if record.ended_at > now():
            raise InvalidArgument(message='活动未结束')
        if record.report_at is None:
            raise InvalidArgument(message='请选择审核中的活动')
        if not record.is_checked:
            raise InvalidArgument(message='请先完成筛选异常用户')

        asset_amount_map = cls.get_reward_asset_amount_map(id_)
        gift_rule_map = {i['asset']: Decimal(i['amount']) for i in json.loads(record.gift_rules)}
        for asset, amount in asset_amount_map.items():
            if asset not in gift_rule_map:
                raise InvalidArgument(message=f'币种:{asset}不属于活动奖励币种')
            if amount > gift_rule_map[asset]:
                raise InvalidArgument(message=f'币种:{asset}奖励数量超过活动币种奖励数量{gift_rule_map[asset]}')

        record.status = ChannelRewardActivity.Status.AUDITED
        record.auditor = g.user.id
        db.session.commit()


class CheckRewardUserMixin:
    FREQUENTLY_GIFT_USER_THRESHOLD = 5
    MULTI_LANG_AREA_GIFT_USER_THRESHOLD = 3
    TRADE_AMOUNT_THRESHOLD = Decimal('100')

    @classmethod
    def get_frequently_gift_users(cls, activity_id) -> set:
        activity = ChannelRewardActivity.query.get(activity_id)
        same_lang_area_activities = cls.get_same_lang_area_activities(activity)
        if not same_lang_area_activities:
            return set()
        return cls._get_frequently_gift_users(same_lang_area_activities)

    @classmethod
    def get_same_lang_area_activities(cls, activity) -> list:
        exclude_activity_types = {
            ChannelRewardActivity.Platform.KOL_Cooperation_Cost.name,
            ChannelRewardActivity.Platform.PublicRelationsMaintenance.name,
            ChannelRewardActivity.Platform.PartnerRelationshipMaintenance.name,
            ChannelRewardActivity.Platform.MediaCooperation.name,
            ChannelRewardActivity.Platform.OfflineDelivery.name,
        }
        ended_at = now()
        languages = set(json.loads(activity.languages))
        exclude_global_languages = languages - {'Global'}
        if len(exclude_global_languages) >= 2 or len(exclude_global_languages) == 0:
            return []

        start_at = ended_at - timedelta(days=90)
        rows = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id != activity.id,
            ChannelRewardActivity.status == ChannelRewardActivity.Status.FINISHED,
            ChannelRewardActivity.started_at >= start_at,
            ChannelRewardActivity.started_at <= ended_at,
            ChannelRewardActivity.is_checked.is_(True)
        ).all()
        res = []
        for row in rows:
            platforms = set(json.loads(row.platforms))
            if not platforms - exclude_activity_types:
                continue
            lang_set = set(json.loads(row.languages))
            lang_set -= {'Global'}
            if not lang_set & exclude_global_languages:
                continue
            res.append(row.id)
        return res

    @classmethod
    def _get_frequently_gift_users(cls, activities) -> set:
        recs = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(activities),
            ChannelRewardHistory.status == ChannelRewardHistory.Status.VALID,
        ).all()
        res = defaultdict(int)
        for rec in recs:
            res[rec.email] += 1
        return {email for email, count in res.items() if count >= cls.FREQUENTLY_GIFT_USER_THRESHOLD}

    @classmethod
    def get_multi_lang_gift_users(cls, activity_id):
        activity = ChannelRewardActivity.query.get(activity_id)
        mul_lang_area_activities = cls._get_multi_lang_area_activities(activity)
        if not mul_lang_area_activities:
            return set()
        activity_langs = set(json.loads(activity.languages)) - {'Global'}
        return cls._get_multi_lang_area_gift_users(activity_langs, mul_lang_area_activities)

    @classmethod
    def _get_multi_lang_area_activities(cls, activity) -> list:
        ended_at = now()
        languages = set(json.loads(activity.languages))
        exclude_global_languages = languages - {'Global'}
        if len(exclude_global_languages) >= 2 or len(exclude_global_languages) == 0:
            return []
        start_at = ended_at - timedelta(days=90)

        rows = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id != activity.id,
            ChannelRewardActivity.status == ChannelRewardActivity.Status.FINISHED,
            ChannelRewardActivity.started_at >= start_at,
            ChannelRewardActivity.started_at <= ended_at,
            ChannelRewardActivity.is_checked.is_(True)
        ).all()
        res = []
        for row in rows:
            lang_set = set(json.loads(row.languages))
            lang_set -= {'Global'}
            if len(lang_set) >= 2 or (lang_set - exclude_global_languages):
                res.append(row)
        return res

    @classmethod
    def _get_multi_lang_area_gift_users(cls, activity_langs, activities) -> set:
        activity_dic = {i.id: i for i in activities}
        activity_ids = list(activity_dic.keys())
        recs = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(activity_ids),
            ChannelRewardHistory.status == ChannelRewardHistory.Status.VALID,
        ).all()
        res = defaultdict(set)
        for rec in recs:
            activity_id = rec.channel_reward_activity_id
            activity = activity_dic[activity_id]
            lang_set = set(json.loads(activity.languages))
            lang_set -= ({'Global'} | activity_langs)
            if len(lang_set) >= 2:
                res[rec.user_id].add(lang_set.pop())  # 有多个语区时，取其中的一个即可
            else:
                res[rec.user_id].update(lang_set)
        user_ids = {user_id for user_id, langs in res.items() if len(langs) >= cls.MULTI_LANG_AREA_GIFT_USER_THRESHOLD}
        filtered_user_ids = cls.filter_by_trade_usd(user_ids)
        return filtered_user_ids

    @classmethod
    def filter_by_trade_usd(cls, user_ids: set):
        if not user_ids:
            return user_ids
        rec = UserTradeSummary.query.order_by(
            UserTradeSummary.report_date.desc()
        ).with_entities(
            UserTradeSummary.report_date
        ).first()
        report_date = rec.report_date
        start_date = report_date - timedelta(days=30)
        ret = UserTradeSummary.query.filter(
            UserTradeSummary.user_id.in_(user_ids),
            UserTradeSummary.report_date <= report_date,
            UserTradeSummary.report_date > start_date
        ).with_entities(
            UserTradeSummary.user_id,
            UserTradeSummary.system,
            func.sum(UserTradeSummary.trade_amount)
        ).group_by(
            UserTradeSummary.user_id,
            UserTradeSummary.system
        ).all()
        over_threshold_users = set()
        for rec in ret:
            user_id, system, amount = rec
            if amount >= cls.TRADE_AMOUNT_THRESHOLD:
                over_threshold_users.add(user_id)
        return user_ids - over_threshold_users


@ns.route('/activities/channel-reward/<int:id_>/gift')
@respond_with_code
class ChannelRewardGiftResource(Resource, CheckRewardUserMixin, ChannelRewardMixin):

    @classmethod
    @require_admin_webauth_token
    def post(cls, id_):
        """运营-活动-渠道奖励派奖"""
        record: ChannelRewardActivity = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id == id_,
        ).first()
        if not record:
            raise InvalidArgument(message='活动不存在')
        if record.ended_at > now():
            raise InvalidArgument(message='活动未结束')
        if not record.is_checked:
            raise InvalidArgument(message='请先完成筛选异常用户')
        if record.status != ChannelRewardActivity.Status.AUDITED:
            raise InvalidArgument(message='活动未完成审核')

        asset_amount_query = cls.query_reward_asset_amount(id_)
        admin_market_balance = ServerClient().get_user_balances(config['MARKET_ADMIN_USER_ID'])
        for row in asset_amount_query:
            asset = row.asset
            total_amount = row.amount
            balance = admin_market_balance[asset]
            if balance["available"] < total_amount:
                raise InvalidArgument(message=f"Admin市场账户余额不足，币种：{asset},"
                                              f"余额：{balance['available']},"
                                              f"活动派奖待扣除数量:{total_amount}")
        record.status = ChannelRewardActivity.Status.FINISHED
        record.rewarder = g.user.id
        record.updated_by = g.user.id
        record.reward_at = now()
        db.session.commit()
        send_channel_reward_activity_gift_task.delay(record.id)

    @classmethod
    def patch(cls, id_):
        """运营-活动-渠道奖励校验用户"""
        record: ChannelRewardActivity = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id == id_,
            ChannelRewardActivity.status == ChannelRewardActivity.Status.ONLINE,
        ).first()
        if not record:
            raise InvalidArgument(message='活动不存在')
        clear_users_ids = AbnormalUserCache.get_clear_users()
        anti_user_ids = AbnormalUserCache.get_anti_fraud_users()
        frequently_gift_users = cls.get_frequently_gift_users(record.id)
        multi_lang_gift_users = cls.get_multi_lang_gift_users(record.id)
        history_query = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id == record.id,
            ChannelRewardHistory.status == ChannelRewardHistory.Status.VALID,
        )
        for row in history_query:
            reason = []
            if row.email in frequently_gift_users:
                reason.append(ChannelRewardHistory.FREQUENTLY_GIFT_USERS)
            if row.user_id in multi_lang_gift_users:
                reason.append(ChannelRewardHistory.MULTI_LANG_GIFT_USERS)
            if row.user_id in clear_users_ids:
                reason.append('清退用户')
            if row.user_id in anti_user_ids:
                reason.append('羊毛党黑名单')
            if reason:
                row.remark = ','.join(reason)
                row.status = ChannelRewardHistory.Status.INVALID
        record.is_checked = True
        db.session.commit()


@ns.route('/activities/channel-reward/gift/batch')
@respond_with_code
class ChannelRewardGiftBatchResource(Resource, ChannelRewardMixin, CheckRewardUserMixin):

    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        ids=fields.List(fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """运营-活动-渠道活动批量派奖"""
        ids = set(kwargs["ids"])
        activities = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id.in_(ids),
            ChannelRewardActivity.ended_at <= now()
        ).all()
        if len(ids) != len(activities):
            raise InvalidArgument(message=f'请选择已结束活动')

        deduct_amount_dic = defaultdict(Decimal)
        for record in activities:
            if not record.is_checked:
                raise InvalidArgument(message=f'【{record.name}】活动未完成筛选异常用户')
            if record.status == ChannelRewardActivity.Status.FINISHED:
                raise InvalidArgument(message=f'【{record.name}】活动已完成奖励发放，请勿重新执行')
            if record.status != ChannelRewardActivity.Status.AUDITED:
                raise InvalidArgument(message=f'【{record.name}】活动未完成审核')

            asset_amount_query = cls.query_reward_asset_amount(record.id)
            for row in asset_amount_query:
                deduct_amount_dic[row.asset] += row.amount

            record.status = ChannelRewardActivity.Status.FINISHED
            record.rewarder = g.user.id
            record.reward_at = now()

        admin_market_balance = ServerClient().get_user_balances(config['MARKET_ADMIN_USER_ID'])
        for asset, amount in deduct_amount_dic.items():
            balance = admin_market_balance[asset]
            if balance["available"] < amount:
                raise InvalidArgument(message=f"Admin市场账户余额不足，币种：{asset},"
                                              f"余额：{balance['available']},"
                                              f"活动派奖待扣除数量:{amount}")

        db.session.commit()
        for record in activities:
            send_channel_reward_activity_gift_task.delay(record.id)

    @classmethod
    @ns.use_kwargs(dict(
        ids=fields.List(fields.Integer, required=True)
    ))
    def patch(cls, **kwargs):
        """运营-活动-渠道活动批量校验用户"""
        ids = set(kwargs["ids"])
        activities = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id.in_(ids),
            ChannelRewardActivity.status == ChannelRewardActivity.Status.ONLINE,
        ).order_by(ChannelRewardActivity.id.asc()).all()
        if len(ids) != len(activities):
            raise InvalidArgument(message=f'请选择未审核活动')

        for row in activities:
            row.is_checked = True

        clear_users_ids = AbnormalUserCache.get_clear_users()
        anti_user_ids = AbnormalUserCache.get_anti_fraud_users()
        ordered_ids = [i.id for i in activities]
        activity_frequently_gift_users = cls.get_frequently_gift_users_dic(ordered_ids)
        activity_multi_lang_gift_users = cls.get_multi_lang_gift_users_dic(ids)
        history_query = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(ids),
            ChannelRewardHistory.status == ChannelRewardHistory.Status.VALID,
        )
        for row in history_query:
            reason = []
            activity_id = row.channel_reward_activity_id
            frequently_gift_users = activity_frequently_gift_users.get(activity_id, set())
            multi_lang_gift_users = activity_multi_lang_gift_users.get(activity_id, set())
            if row.email in frequently_gift_users:
                reason.append(ChannelRewardHistory.FREQUENTLY_GIFT_USERS)
            if row.user_id in multi_lang_gift_users:
                reason.append(ChannelRewardHistory.MULTI_LANG_GIFT_USERS)
            if row.user_id in clear_users_ids:
                reason.append('清退用户')
            if row.user_id in anti_user_ids:
                reason.append('羊毛党黑名单')
            if reason:
                row.remark = ','.join(reason)
                row.status = ChannelRewardHistory.Status.INVALID

        db.session.commit()

    @classmethod
    def get_frequently_gift_users_dic(cls, activity_ids) -> dict:

        ret = defaultdict(set)
        for activity_id in activity_ids:
            frequently_gift_users = cls.get_frequently_gift_users(activity_id)
            ret[activity_id] = frequently_gift_users
        return ret

    @classmethod
    def get_multi_lang_gift_users_dic(cls, activity_ids) -> dict:
        ret = defaultdict(set)
        for activity_id in activity_ids:
            multi_lang_gift_users = cls.get_multi_lang_gift_users(activity_id)
            ret[activity_id] = multi_lang_gift_users
        return ret


@ns.route('/activities/channel-reward/audit/batch')
@respond_with_code
class ChannelRewardAuditBatchResource(ChannelRewardMixin, Resource):
    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        ids=fields.List(fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """运营-活动-渠道活动批量审核"""
        ids = set(kwargs["ids"])
        activities = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id.in_(ids),
            ChannelRewardActivity.status == ChannelRewardActivity.Status.ONLINE,
            ChannelRewardActivity.ended_at <= now()
        ).all()
        if len(ids) != len(activities):
            raise InvalidArgument(message=f'请选择待审核活动')

        for record in activities:
            if record.report_at is None:
                raise InvalidArgument(message=f'【{record.name}】请选择审核中的活动')
            if not record.is_checked:
                raise InvalidArgument(message=f'【{record.name}】活动未完成筛选异常用户')

            asset_amount_map = cls.get_reward_asset_amount_map(record.id)
            gift_rule_map = {i['asset']: Decimal(i['amount']) for i in json.loads(record.gift_rules)}
            for asset, amount in asset_amount_map.items():
                if asset not in gift_rule_map:
                    raise InvalidArgument(message=f'币种:{asset}不属于【{record.name}】活动奖励币种')
                if amount > gift_rule_map[asset]:
                    raise InvalidArgument(
                        message=f'币种:{asset}奖励数量超过【{record.name}】活动币种奖励数量{gift_rule_map[asset]}')

            record.status = ChannelRewardActivity.Status.AUDITED
            record.auditor = g.user.id
        db.session.commit()


@ns.route('/activities/channel-reward/export/batch')
@respond_with_code
class ChannelRewardExportBatchResource(ChannelRewardMixin, Resource):
    STATES = {
        'PENDING': '未开始',
        'ONGOING': '进行中',
        'UPLOAD_PENDING': '待上传',
        'AUDIT_REQUIRED': '待审核',
        'ENDED': '待发奖',
        'FINISHED': '已结束',
    }

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "name", Language.ZH_HANS_CN: "活动名称"},
        {"field": "languages_str", Language.ZH_HANS_CN: "活动语区"},
        {"field": "platforms_str", Language.ZH_HANS_CN: "平台类型"},
        {"field": "asset", Language.ZH_HANS_CN: "奖励币种"},
        {"field": "user_gift_amount", Language.ZH_HANS_CN: "实际发放数量"},
        {"field": "state", Language.ZH_HANS_CN: "状态"},
        {"field": "creator_name", Language.ZH_HANS_CN: "创建人"},
        {"field": "auditor_name", Language.ZH_HANS_CN: "审核人"},
        {"field": "rewarder_name", Language.ZH_HANS_CN: "发奖人"},
        {"field": "reward_at_str", Language.ZH_HANS_CN: "发放时间"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        ids=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        """运营-活动-渠道活动批量导出"""
        ids = json.loads(kwargs["ids"])
        query = ChannelRewardActivity.query.filter(
            ChannelRewardActivity.id.in_(ids),
        ).order_by(ChannelRewardActivity.id.desc())
        items = query.all()
        ids = [item.id for item in items]
        join_users = ChannelRewardHistory.query.filter(
            ChannelRewardHistory.channel_reward_activity_id.in_(ids),
            ChannelRewardHistory.status != ChannelRewardHistory.Status.DELETED,
        ).group_by(
            ChannelRewardHistory.channel_reward_activity_id,
            ChannelRewardHistory.asset,
            ChannelRewardHistory.status,
        ).with_entities(
            ChannelRewardHistory.channel_reward_activity_id,
            ChannelRewardHistory.asset,
            ChannelRewardHistory.status,
            func.count(ChannelRewardHistory.id).label('user_count'),
            func.sum(ChannelRewardHistory.amount).label('amount'),
        ).all()
        join_user_count_map = defaultdict(lambda: defaultdict(dict))
        join_user_amount_map = defaultdict(lambda: defaultdict(dict))

        for row in join_users:
            join_user_count_map[row.channel_reward_activity_id][row.asset][row.status] = row.user_count
            join_user_amount_map[row.channel_reward_activity_id][row.asset][row.status] = row.amount

        result = []
        op_user_ids = set()
        for i in items:
            if i.creator:
                op_user_ids.add(i.creator)
            if i.auditor:
                op_user_ids.add(i.auditor)
            if i.rewarder:
                op_user_ids.add(i.rewarder)
        admin_user_name_map = get_admin_user_name_map(list(op_user_ids))
        langs = {lang.name: ln.chinese for lang, ln in LANGUAGE_NAMES.items()}
        now_ = now()
        for row in items:
            item = row.to_dict(enum_to_name=True)
            gift_rules = json.loads(item['gift_rules'])
            assets = [i['asset'] for i in gift_rules]
            user_amount_data = join_user_amount_map[item['id']]
            item['languages'] = json.loads(item['languages'])
            item['platforms'] = json.loads(item['platforms'])
            item['reward_at_str'] = item['reward_at'].strftime("%Y-%m-%d %H:%M:%S") if item['reward_at'] else '-'
            item['languages_str'] = ','.join([langs[i] for i in item['languages']])
            item['platforms_str'] = ','.join([ChannelRewardActivity.Platform[i].value for i in item['platforms']])
            item['creator_name'] = admin_user_name_map.get(item['creator'], '-')
            item['auditor_name'] = admin_user_name_map.get(item['auditor'], '-')
            item['rewarder_name'] = admin_user_name_map.get(item['rewarder'], '-')
            if item['status'] == ChannelRewardActivity.Status.FINISHED.name:
                item['state'] = cls.STATES['FINISHED']
            if item['status'] == ChannelRewardActivity.Status.AUDITED.name:
                item['state'] = cls.STATES['ENDED']
            if item['status'] == ChannelRewardActivity.Status.ONLINE.name:
                if item['started_at'] > now_:
                    item['state'] = cls.STATES['PENDING']
                elif item['started_at'] <= now_ <= item['ended_at']:
                    item['state'] = cls.STATES['ONGOING']
                elif item['report_at'] is None:
                    item['state'] = cls.STATES['UPLOAD_PENDING']
                else:
                    item['state'] = cls.STATES['AUDIT_REQUIRED']
            for asset in assets:
                item = deepcopy(item)
                user_gift_amount = user_amount_data[asset].get(ChannelRewardHistory.Status.VALID, 0)
                item['asset'] = asset
                item['user_gift_amount'] = amount_to_str(user_gift_amount)
                result.append(item)

        return export_xlsx(filename='channel-activities-list',
                           data_list=result,
                           export_headers=cls.export_headers)


# noinspection PyUnresolvedReferences
@ns.route('/activities/trade/<int:id_>')
@respond_with_code
class TradeRankActivityResource(Resource):

    @classmethod
    def get(cls, id_):
        """运营-活动-交易送币活动统计信息"""
        record: TradeRankActivity = TradeRankActivity.query.get(id_)
        if not record:
            raise InvalidArgument(message='记录不存在')
        rank_query = TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.activity_id == record.activity_id
        )
        total_rank_count = rank_query.count()
        latest_rank: TradeRankActivityUserInfo = rank_query.order_by(
            TradeRankActivityUserInfo.rank_at.desc()
        ).first()
        latest_rank_at = latest_rank.rank_at if latest_rank else 0

        trade_value_query = rank_query.with_entities(
            func.sum(TradeRankActivityUserInfo.trade_value).label('amount')
        ).first()
        trade_value = trade_value_query.amount \
            if trade_value_query.amount else Decimal()

        query = TradeRankActivity.query.filter(
            TradeRankActivity.started_at <= now(),
            TradeRankActivity.status != TradeRankActivity.Status.DELETED
        ).order_by(
            TradeRankActivity.id
        )
        activities = {v.id: f'{v.id} {v.name}' for v in query}
        return dict(
            extra=dict(activities=activities),
            total_rank_count=total_rank_count,
            latest_rank_time=latest_rank_at,
            trade_value=amount_to_str(trade_value, 8),
            info=record
        )

    @classmethod
    @ns.use_kwargs(dict(
        gift_asset=fields.String(
            required=True,
            validate=lambda x: x in list_all_assets()),
        markets=fields.List(fields.String, missing=None),
        exclude_markets=fields.List(fields.String, missing=None),
        market_type=EnumField(TradeRankActivity.MarketType, allow_none=True),
        type=EnumField(TradeRankActivity.Type, required=True),
        gift_rules=fields.Raw(required=True),
        announce_url=fields.String(),
        least_trade_amount=PositiveDecimalField(allow_zero=True, allow_none=True),
        max_split_reward_amount=PositiveDecimalField,
        gift_amount=PositiveDecimalField(required=True),
        start_time=TimestampField(is_ms=True, required=True),
        end_time=TimestampField(is_ms=True, required=True),
        titles=fields.Dict(keys=EnumField([i.name for i in Language]), values=fields.String, required=True),
        user_group_condition=fields.String(required=True),
        cover=fields.String(missing=None),
        funding_source=EnumField(TradeRankActivity.FundingSource, required=True),
    ))
    def put(cls, id_, **kwargs):
        """运营-活动-编辑交易送币活动"""
        if kwargs['end_time'] < kwargs['start_time']:
            raise InvalidArgument(message='活动结束时间必须大于开始时间')
        record: TradeRankActivity = TradeRankActivity.query.get(id_)
        if not record:
            raise InvalidArgument(message='记录不存在')
        markets = kwargs['markets']
        exclude_markets = kwargs['exclude_markets']
        if markets and exclude_markets:
            raise InvalidArgument(message='已经选择了活动市场不能再添加被排除市场！')
        old_data = record.to_dict(enum_to_name=True)
        rule_data = check_trade_rule(kwargs)
        record.least_trade_amount = kwargs['least_trade_amount'] or Decimal()
        record.announce_url = kwargs.get('announce_url', '')
        record.max_split_reward_amount = kwargs.get('max_split_reward_amount')
        record.gift_rules = json.dumps(rule_data, cls=JsonEncoder)
        record.type = kwargs['type']
        record.funding_source = kwargs['funding_source']

        if record.type == TradeRankActivity.Type.SPOT_NET_BUY:
            if not markets:
                raise InvalidArgument
            market_data = MarketCache(markets[0]).dict
            activity_asset = market_data['base_asset']
            for market in markets[1:]:
                market_data = MarketCache(market).dict
                if market_data['base_asset'] != activity_asset:
                    raise InvalidArgument(message='市场选择错误')
        record.markets = compact_json_dumps(markets) if markets else ''
        record.exclude_markets = compact_json_dumps(exclude_markets) if exclude_markets else ''
        record.market_type = kwargs.get('market_type')
        record.gift_asset = kwargs['gift_asset']
        record.gift_amount = kwargs['gift_amount']
        record.left_gift_amount = kwargs['gift_amount']
        record.started_at = kwargs['start_time']
        record.ended_at = kwargs['end_time']
        record.user_group_condition = kwargs['user_group_condition']
        record.updated_by = g.user.id
        if cover := kwargs.get('cover'):
            record.cover = cover
        for lang in Language:
            detail = TradeRankActivityDetail.get_or_create(
                trade_activity_id=record.id,
                lang=lang
            )
            title = kwargs['titles'].get(lang.name)
            if not title:
                title = kwargs['titles'][Language.EN_US.name]
            detail.title = title
            db.session.add(detail)

        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TradeActivity,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(TradeRankActivity.Status, required=True),
    ))
    def patch(cls, id_, **kwargs):
        """运营-活动-编辑交易送币状态"""
        record: TradeRankActivity = TradeRankActivity.query.get(id_)
        if not record:
            raise InvalidArgument(message='记录不存在')
        old_data = record.to_dict(enum_to_name=True)
        record.status = kwargs['status']
        record.updated_by = g.user.id
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TradeActivity,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
        )


@ns.route('/activities/trade/title/template')
@respond_with_code
class ActivityTradeTemplateResource(Resource):
    export_headers = (
        {'field': 'display', Language.ZH_HANS_CN: '语言'},
        {'field': 'lang', Language.ZH_HANS_CN: 'lang'},
        {'field': 'title', Language.ZH_HANS_CN: '活动标题'},
    )

    @classmethod
    def get(cls):
        """运营-活动-交易送币活动-活动标题模板"""
        available_lang_dict = language_cn_names()
        languages = [{"lang": k.name, "display": v, "title": ''} for k, v in available_lang_dict.items()]
        return export_xlsx(
            filename='template',
            data_list=languages,
            export_headers=cls.export_headers
        )


@ns.route('/activities/trade/title/template-upload')
@respond_with_code
class ActivityTradeUploadResource(Resource):

    @classmethod
    def post(cls, **kwargs):
        """运营-活动-交易送币活动-导入活动标题模板"""
        file_ = request.files.get('batch-upload')
        file_columns = ["display", "lang", "title"]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)
        return rows


@ns.route('/activities/trade-rank')
@respond_with_code
class TradeRankResource(Resource):
    marshal_fields = {
        'rank': fx_fields.Integer,
        'user_id': fx_fields.Integer,
        'trade_value': AmountField,
        'gift_amount': AmountField,
        'rank_at': TimestampMarshalField
    }

    @classmethod
    @ns.use_kwargs(dict(
        trade_activity_id=fields.Integer(required=True),
        user_id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-活动-交易送币排行"""
        activity: TradeRankActivity = TradeRankActivity.query.filter(
            TradeRankActivity.id == kwargs['trade_activity_id'],
            TradeRankActivity.status != TradeRankActivity.Status.DELETED
        ).first()
        if not activity:
            raise InvalidArgument(message='活动不存在')
        query = TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.activity_id == activity.activity_id
        ).order_by(
            TradeRankActivityUserInfo.rank
        )
        if user_id := kwargs.get('user_id'):
            query = query.filter(TradeRankActivityUserInfo.user_id == user_id)
        records = query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        user_ids = [item['user_id'] for item in records['data']]

        black_list = ActivityBlackList.query.filter(
            ActivityBlackList.user_id.in_(user_ids),
            ActivityBlackList.activity_id == activity.id,
            ActivityBlackList.status == ActivityBlackList.Status.PASSED
        ).all()
        black_user_list = {i.user_id for i in black_list}
        user_email_map = dict()
        for ids_ in batch_iter(user_ids, 1000):
            user_email = User.query.filter(
                User.id.in_(ids_)
            ).with_entities(User.id, User.email).all()
            user_email_map.update(dict(user_email))
        for item in records['data']:
            item['email'] = user_email_map[item['user_id']]
            item['in_black_list'] = item['user_id'] in black_user_list
        return records

    @classmethod
    @ns.use_kwargs(dict(
        trade_activity_id=fields.Integer(required=True),
        user_id=fields.Integer(required=True),
        status=EnumField(ActivityBlackList.Status, missing=ActivityBlackList.Status.PASSED),
        remark=fields.String,
    ))
    def put(cls, **kwargs):
        """运营-活动-交易送币排行-添加/移除黑名单"""
        activity = TradeRankActivity.query.filter(
            TradeRankActivity.id == kwargs['trade_activity_id'],
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
        ).first()
        if not activity:
            raise InvalidArgument(message='只能对进行中的活动进行操作')

        b = TradeActivityBusiness(activity.id)
        b.set_black_list_user(kwargs['user_id'],
                              kwargs['status'],
                              kwargs.get('remark'))
        if activity.ended_at <= now():
            b.update_rankings()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TradeRank,
            special_data=dict(activity_id=activity.id, remark=kwargs.get('remark', ''), status=kwargs['status']),
            target_user_id=kwargs['user_id'],
        )


@ns.route('/activities/trade/users')
@respond_with_code
class TradeRankActivityJoinUsersResource(Resource):
    class UserStatus(Enum):
        PASSED = '有效'
        DELETED = '失效'

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        trade_activity_id=fields.Integer(allow_none=True),
        status=EnumField(UserStatus),
        page=PageField(unlimited=True),
        limit=LimitField(max_limit=3000),
        channel=fields.String(allow_none=True)
    ))
    def get(cls, **kwargs):
        """
        活动-交易排名-参与用户
        """
        is_single_activity_query = cls.check_by_kwargs(kwargs)
        if is_single_activity_query:
            ret = cls.get_single_activity_query_res(kwargs)
        else:
            ret = cls.get_multi_activity_query_res(kwargs)
        titles = TradeRankActivity.query.join(TradeRankActivityDetail).filter(
            TradeRankActivity.id == TradeRankActivityDetail.trade_activity_id,
            TradeRankActivityDetail.lang == Language.ZH_HANS_CN
        ).with_entities(
            TradeRankActivity.id,
            TradeRankActivityDetail.title
        ).order_by(TradeRankActivity.id.desc()).all()
        title_map = []
        for item in titles:
            title_map.append((item.id, item.title))
        channel_recs = TradeRankActivityJoinUser.query.filter(
            TradeRankActivityJoinUser.channel.isnot(None)
        ).with_entities(
            TradeRankActivityJoinUser.channel.distinct()
        ).all()
        activity_channels = [i[0] for i in channel_recs]
        ret.update(activities=title_map, activity_channels=activity_channels, activity_map=dict(title_map))
        return ret

    @classmethod
    def get_single_activity_query_res(cls, kwargs):
        id_ = kwargs.get('trade_activity_id')
        if id_:
            activity: TradeRankActivity = TradeRankActivity.query.filter(
                TradeRankActivity.id == id_,
            ).first()
            if not activity:
                raise InvalidArgument(message='活动不存在')
        else:
            activity = TradeRankActivity.query.filter(
                TradeRankActivity.status.in_([TradeRankActivity.Status.ONLINE,
                                              TradeRankActivity.Status.FINISHED]),
            ).order_by(TradeRankActivity.id.desc()).first()
            if not activity:
                raise InvalidArgument(message="无活动")

        black_list = ActivityBlackList.query.filter(
            ActivityBlackList.activity_id == activity.activity_id,
        ).with_entities(ActivityBlackList.user_id,
                        ActivityBlackList.status,
                        ActivityBlackList.remark).all()
        black_user_ids = {i.user_id for i in black_list if i.status == ActivityBlackList.Status.PASSED}
        black_user_remark_map = {i.user_id: i.remark for i in black_list}
        query = TradeRankActivityJoinUser.query.filter(
            TradeRankActivityJoinUser.trade_activity_id == activity.id,
        ).outerjoin(TradeRankActivityUserInfo,
                    and_(TradeRankActivityJoinUser.user_id == TradeRankActivityUserInfo.user_id,
                         TradeRankActivityJoinUser.trade_activity_id == TradeRankActivityUserInfo.trade_activity_id)
                    ).order_by(-TradeRankActivityUserInfo.rank.desc(),
                               TradeRankActivityJoinUser.user_id)
        total_count = query.count()
        if user_id := kwargs.get('user_id'):
            query = query.filter(TradeRankActivityJoinUser.user_id == user_id)
        if status := kwargs.get('status'):
            if status == cls.UserStatus.DELETED:
                query = query.filter(TradeRankActivityJoinUser.user_id.in_(black_user_ids))
            else:
                query = query.filter(TradeRankActivityJoinUser.user_id.notin_(black_user_ids))
        if channel := kwargs.get('channel'):
            query = query.filter(TradeRankActivityJoinUser.channel == channel)

        records = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        user_ids = [item.user_id for item in records.items]
        user_email_map = UserRepository.get_users_id_email_map(user_ids)
        user_info = TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.trade_activity_id == activity.id,
            TradeRankActivityUserInfo.user_id.in_(user_ids)
        ).with_entities(TradeRankActivityUserInfo.user_id,
                        TradeRankActivityUserInfo.report_at,
                        TradeRankActivityUserInfo.target_value,
                        TradeRankActivityUserInfo.trade_amount,
                        TradeRankActivityUserInfo.gift_amount,
                        TradeRankActivityUserInfo.fee_usd,
                        TradeRankActivityUserInfo.market_fee_usd,
                        TradeRankActivityUserInfo.rank,
                        TradeRankActivityUserInfo.self_deal_amount,
                        ).all()
        user_info_map = {item.user_id: item for item in user_info}

        statistics_records = TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.trade_activity_id == activity.id,
            TradeRankActivityUserInfo.user_id.notin_(black_user_ids)
        ).with_entities(
            TradeRankActivityUserInfo.rank,
            TradeRankActivityUserInfo.trade_amount,
            TradeRankActivityUserInfo.fee_usd,
            TradeRankActivityUserInfo.market_fee_usd,
        )
        rule_data = json.loads(activity.gift_rules)
        max_rank = rule_data[-1]['rank_max']
        qualified_user_count = len([item for item in statistics_records
                                    if item.trade_amount >= activity.least_trade_amount])
        trade_count = len([item.trade_amount for item in statistics_records if item.trade_amount > 0])
        sum_amount = sum(item.trade_amount for item in statistics_records)
        qualified_sum_amount = sum(item.trade_amount for item in statistics_records \
                                   if item.trade_amount >= activity.least_trade_amount)
        rank_reward_sum_amount = sum(item.trade_amount for item in statistics_records \
                                     if item.rank and item.rank <= max_rank)
        total_fee_usd = sum(item.fee_usd for item in statistics_records)
        market_fee_usd = sum(item.market_fee_usd for item in statistics_records)
        total_fee_usd = quantize_amount(total_fee_usd, 2)
        res = []
        for item in records.items:
            item = item.to_dict()
            item['email'] = user_email_map[item['user_id']]
            item['in_black_list'] = item['user_id'] in black_user_ids
            item['remark'] = black_user_remark_map.get(item['user_id'])

            info = user_info_map.get(item['user_id'])
            item['target_value'] = 0
            item['trade_amount'] = 0
            item['fee_usd'] = 0
            item['rank'] = None
            item['self_deal_amount'] = 0
            if info:
                item['target_value'] = info.target_value
                item['trade_amount'] = info.trade_amount
                item['gift_amount'] = info.gift_amount
                item['fee_usd'] = info.fee_usd
                item['market_fee_usd'] = info.market_fee_usd
                item['rank'] = info.rank
                item['self_deal_amount'] = info.self_deal_amount
            res.append(item)

        updated_at = None
        if user_info_map:
            updated_at = list(user_info_map.values())[0].report_at
        if activity.status == TradeRankActivity.Status.ONLINE and activity.ended_at < now():
            can_gift = True
        else:
            can_gift = False
        anti_fraud_data = AntiFraudHelper.get_anti_fraud_data(AntiFraudScore.ActivityType.TRADE_RANK,
                                                              activity.activity_id)
        for item in res:
            if anti_fraud_data is None:
                item['anti_fraud_data'] = None
                item['anti_fraud_score'] = "--"
                item['anti_fraud_similar_emails'] = '--'
            else:
                item['anti_fraud_data'] = anti_fraud_data.get(item['user_id'], {})
                item['anti_fraud_score'] = item['anti_fraud_data'].get('score', 0)
                item['anti_fraud_similar_emails'] = len(item['anti_fraud_data'].get('similar_emails', {}))
        return dict(
            items=res,
            total=records.total,
            activity_type=activity.type.name,
            gift_asset=activity.gift_asset,
            total_count=total_count,
            trade_count=trade_count,
            qualified_count=qualified_user_count,
            sum_amount=sum_amount,
            qualified_sum_amount=qualified_sum_amount,
            rank_reward_sum_amount=rank_reward_sum_amount,
            updated_at=updated_at,
            trade_asset=TradeActivityBusiness(activity.id).get_trade_asset(),
            trade_activity_id=activity.id,
            total_fee_usd=total_fee_usd,
            market_fee_usd=market_fee_usd,
            can_gift=can_gift,
            is_checked=activity.is_checked,
            is_single_activity=True,
            activity_status=activity.status.name,
        )

    @classmethod
    def get_multi_activity_query_res(cls, kwargs):
        user_id, status, channel = kwargs.get('user_id'), kwargs.get('status'), kwargs.get('channel')
        if status and (not user_id and not channel):
            raise InvalidArgument(message='不指定活动id时，查询参数必须包含用户或渠道')

        query = TradeRankActivityJoinUser.query.outerjoin(
            TradeRankActivityUserInfo, and_(
                TradeRankActivityJoinUser.user_id == TradeRankActivityUserInfo.user_id,
                TradeRankActivityJoinUser.trade_activity_id == TradeRankActivityUserInfo.trade_activity_id)
        ).order_by(TradeRankActivityJoinUser.trade_activity_id.desc(),
                   -TradeRankActivityUserInfo.rank.desc())
        if channel:
            query = query.filter(TradeRankActivityJoinUser.channel == channel)
        if user_id:
            query = query.filter(TradeRankActivityJoinUser.user_id == user_id)
        activity_rows_lis = group_by(lambda x: x.trade_activity_id, query)
        trade_rank_activities = TradeRankActivity.query.filter(
            TradeRankActivity.id.in_(list(activity_rows_lis))
        ).with_entities(
            TradeRankActivity.id,
            TradeRankActivity.activity_id
        ).all()
        trade_rank_id_dic = dict(trade_rank_activities)
        records = []
        total_users = set()
        for trade_rank_activity_id, rows in activity_rows_lis.items():
            activity_id = trade_rank_id_dic[trade_rank_activity_id]
            black_list = ActivityBlackList.query.filter(
                ActivityBlackList.activity_id == activity_id,
            ).with_entities(ActivityBlackList.user_id,
                            ActivityBlackList.status,
                            ActivityBlackList.remark).all()
            black_user_ids = {i.user_id for i in black_list if i.status == ActivityBlackList.Status.PASSED}
            black_user_remark_map = {i.user_id: i.remark for i in black_list}
            user_ids = {i.user_id for i in rows}
            user_info = TradeRankActivityUserInfo.query.filter(
                TradeRankActivityUserInfo.trade_activity_id == trade_rank_activity_id,
                TradeRankActivityUserInfo.user_id.in_(user_ids)
            ).with_entities(TradeRankActivityUserInfo.user_id,
                            TradeRankActivityUserInfo.report_at,
                            TradeRankActivityUserInfo.target_value,
                            TradeRankActivityUserInfo.trade_amount,
                            TradeRankActivityUserInfo.gift_amount,
                            TradeRankActivityUserInfo.fee_usd,
                            TradeRankActivityUserInfo.market_fee_usd,
                            TradeRankActivityUserInfo.rank,
                            TradeRankActivityUserInfo.self_deal_amount,
                            ).all()
            user_info_map = {item.user_id: item for item in user_info}
            for row in rows:
                user_id = row.user_id
                if status and not cls.user_status_valid(status, user_id, black_user_ids):  # 状态在这里筛选
                    continue
                total_users.add(user_id)
                item = row.to_dict()
                item['in_black_list'] = item['user_id'] in black_user_ids
                item['remark'] = black_user_remark_map.get(item['user_id'])

                info = user_info_map.get(item['user_id'])
                item['target_value'] = 0
                item['trade_amount'] = 0
                item['fee_usd'] = 0
                item['rank'] = None
                item['self_deal_amount'] = 0
                if info:
                    item['target_value'] = info.target_value
                    item['trade_amount'] = info.trade_amount
                    item['gift_amount'] = info.gift_amount
                    item['fee_usd'] = info.fee_usd
                    item['market_fee_usd'] = info.market_fee_usd
                    item['rank'] = info.rank
                    item['self_deal_amount'] = info.self_deal_amount
                records.append(item)
        user_email_map = UserRepository.get_users_id_email_map(total_users)
        page, limit = kwargs['page'], kwargs['limit']
        res = records[(page - 1) * limit: page * limit]
        for item in res:
            item['email'] = user_email_map.get(item['user_id'])
        return dict(
            items=res,
            total=len(records),
            activity_type='',
            gift_asset='',
            total_count='',
            qualified_count='',
            sum_amount='',
            qualified_sum_amount='',
            rank_reward_sum_amount='',
            updated_at='',
            trade_asset='',
            trade_activity_id=None,
            total_fee_usd='',
            market_fee_usd='',
            can_gift=False,
            is_checked=False,
            is_single_activity=False,
            activity_status=None,
        )

    @classmethod
    def check_by_kwargs(cls, kwargs):
        id_ = kwargs.get('trade_activity_id')
        if id_:
            return True
        user_id, status, channel = kwargs.get('user_id'), kwargs.get('status'), kwargs.get('channel')
        if not any((user_id, status, channel)):
            return True
        else:
            return False

    @classmethod
    def user_status_valid(cls, status, user_id, black_user_ids):
        if status == cls.UserStatus.DELETED:
            if user_id not in black_user_ids:
                return False
        else:
            if user_id in black_user_ids:
                return False
        return True


@ns.route('/activities/trade/template')
@respond_with_code
class TradeRankActivityTemplateResource(Resource):
    export_headers = (
        {'field': 'user_id', Language.ZH_HANS_CN: '用户ID', Language.EN_US: 'User ID'},
        {'field': 'email', Language.ZH_HANS_CN: '邮箱',
         Language.EN_US: 'Email'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注',
         Language.EN_US: 'Remark'},
        {'field': 'status', Language.ZH_HANS_CN: '状态',
         Language.EN_US: 'Status'},
    )

    @classmethod
    def get(cls):
        """
        活动-交易排名-模板下载
        """
        return export_xlsx(
            filename='trade-rank-activity-update-template',
            data_list=[],
            export_headers=cls.export_headers
        )


@ns.route('/activities/trade/<int:trade_activity_id>/batch')
@respond_with_code
class TradeRankActivityBatchResource(Resource):
    VALID_STATUS = ['有效', '无效']

    @classmethod
    def post(cls, trade_activity_id):
        """
        活动-交易排名-黑名单用户批量上传
        """
        # 此接口已改为名单批量上传（支持黑名单批量上传和批量取消黑名单用户）
        if not (file := request.files.get('file')):
            raise InvalidArgument
        rows = get_table_rows(file, ['id', "email", "remark", "status"])
        if not rows:
            return dict(
            add_black_user_count=0,
            remove_black_user_count=0,
        )
        black_rows, white_rows = [], []
        for idx, row in enumerate(rows):
            status = row['status']
            if not status:
                raise InvalidArgument(message=f'第{idx+2}行未填写状态！')
            if status not in cls.VALID_STATUS:
                raise InvalidArgument(message=f'状态字段只能填写 有效 或 无效')
            if status == '有效':
                white_rows.append(row)
            else:
                black_rows.append(row)
        add_black_users = cls.batch_upload_users(True, black_rows, trade_activity_id)
        remove_black_users = cls.batch_upload_users(False, white_rows, trade_activity_id)
        b = TradeActivityBusiness(trade_activity_id)
        if b.activity.ended_at <= now():
            b.update_rankings()
        return dict(
            add_black_users=add_black_users,
            remove_black_users=remove_black_users,
        )

    @classmethod
    def batch_upload_users(cls, is_black, rows, trade_activity_id):
        if not rows:
            return 0
        emails = [row['email'] for row in rows]
        user_ids = User.query.filter(
            User.email.in_(emails)
        ).with_entities(User.id).all()
        user_ids = [user_id for user_id, in user_ids]
        user_ids = set(user_ids) | {row['id'] for row in rows}
        user_ids = [i for i in user_ids if i]
        remark_map = {row['id']: row['remark'] for row in rows}

        join_user_ids = TradeRankActivityJoinUser.query.filter(
            TradeRankActivityJoinUser.trade_activity_id == trade_activity_id,
            TradeRankActivityJoinUser.user_id.in_(user_ids)
        ).with_entities(TradeRankActivityJoinUser.user_id).all()
        join_user_ids = [item.user_id for item in join_user_ids]
        if not_activity_user_ids := (set(user_ids) - set(join_user_ids)):
            raise InvalidArgument(message=f'{not_activity_user_ids}不是本次活动报名用户')

        b = TradeActivityBusiness(trade_activity_id)
        prev_black_list_count = ActivityBlackList.query.filter(
            ActivityBlackList.activity_id == b.activity.activity_id,
            ActivityBlackList.status == ActivityBlackList.Status.PASSED,
            ActivityBlackList.user_id.in_(user_ids)
        ).with_entities(
            func.count('*')
        ).scalar() or 0
        for user_id in user_ids:
            if is_black:
                status = ActivityBlackList.Status.PASSED
            else:
                status = ActivityBlackList.Status.DELETED
            b.set_black_list_user(
                user_id=user_id,
                status=status,
                remark=remark_map.get(user_id),
                commit=False
            )
        db.session.commit()
        if is_black:
            return len(user_ids) - prev_black_list_count
        else:
            return prev_black_list_count


@ns.route('/activities/trade/<int:trade_activity_id>/batch-update-users')
@respond_with_code
class BatchUpdateUsersResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(ActivityBlackList.Status, required=True),
            ids=fields.String(required=True),
            remark=fields.String,
        )
    )
    def post(cls, trade_activity_id, **kwargs):
        """运营-活动-交易排名活动-批量修改用户状态"""
        status, user_id_str, remark = kwargs['status'], kwargs['ids'], kwargs.get('remark')
        user_ids = [int(i) for i in user_id_str.split(',')]
        record: TradeRankActivity = TradeRankActivity.query.filter(
            TradeRankActivity.id == trade_activity_id,
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
        ).first()
        if not record:
            raise InvalidArgument(message='活动不存在或当前状态不支持批量修改！')
        b = TradeActivityBusiness(trade_activity_id)
        for user_id in user_ids:
            b.set_black_list_user(
                user_id=user_id,
                status=status,
                remark=remark,
                commit=False
            )
        db.session.commit()
        if b.activity.ended_at <= now():
            b.update_rankings()
        return dict()


# noinspection PyUnresolvedReferences
@ns.route('/activities/trade/<int:id_>/gift')
@respond_with_code
class TradeSendGiftResource(Resource):

    @classmethod
    def _check_balance(cls, pay_user_id: int, amount: Decimal, asset: str):
        balance = ServerClient().get_user_balances(pay_user_id, asset)[asset]
        if balance["available"] < amount:
            raise InvalidArgument(
                message=f"账号余额不足，请充值后重试。"
                        f"币种：{asset}, 余额：{amount_to_str(balance['available'], 2)}, 待扣除数量:{amount}。"
            )

    @classmethod
    def post(cls, id_):
        """运营-活动-交易送币派奖"""
        record: TradeRankActivity = TradeRankActivity.query.filter(
            TradeRankActivity.id == id_,
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
            TradeRankActivity.ended_at <= now()
        ).first()
        if not record:
            raise InvalidArgument(message='活动不存在')
        if not record.is_checked:
            raise InvalidArgument(message='未完成清退用户排除，不能派奖！')
        with CacheLock(LockKeys.activity_trade_send_gift(record.id),
                       wait=False):
            TradeActivityBusiness(record.id).update_user_values()
            query = TradeRankActivityUserInfo.query.filter(
                TradeRankActivityUserInfo.trade_activity_id == record.id,
                TradeRankActivityUserInfo.rank.isnot(None),
                TradeRankActivityUserInfo.gift_amount > 0
            ).order_by(
                TradeRankActivityUserInfo.rank
            )
            total_gift_amount = Decimal(sum([v.gift_amount for v in query]))
            if record.type in [TradeRankActivity.Type.SPOT_TRADE, TradeRankActivity.Type.SPOT_NET_BUY]:
                pay_user_id = config["SPOT_TRADE_RANK_ADMIN_USER_ID"]
            else:
                pay_user_id = config['PERPETUAL_TRADE_RANK_ADMIN_USER_ID']
            cls._check_balance(pay_user_id, total_gift_amount, record.gift_asset)

            record.status = TradeRankActivity.Status.FINISHED
            db.session.commit()
            for v in query:
                send_trade_activity_gift_task.delay(id_, v.user_id)
        AdminOperationLog.new_send(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TradeActivity,
            detail=dict(id=id_),
        )

    @classmethod
    def patch(cls, id_):
        """运营-活动-交易送币排除清退用户"""
        record: TradeRankActivity = TradeRankActivity.query.filter(
            TradeRankActivity.id == id_,
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
            TradeRankActivity.ended_at <= now()
        ).first()
        if not record:
            raise InvalidArgument(message='活动不存在')
        handler = TradeActivityBusiness(id_)
        user_ids = handler.get_activity_cleared_users()
        handler.set_cleared_users(user_ids)
        record.is_checked = True
        db.session.commit()
        handler.update_rankings()


@ns.route('/activities/trade/statistics')
@respond_with_code
class TradeStatisticsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(unlimited=True),
        limit=LimitField(max_limit=3000)
    ))
    def get(cls, **kwargs):
        """交易排名-活动统计"""
        query = TradeRankActivityStatistics.query.order_by(
            TradeRankActivityStatistics.trade_activity_id.desc()
        )
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        report_at = None
        if pagination.items:
            report_at = max(item.report_at for item in pagination.items)
        ids = [item.trade_activity_id for item in pagination.items]
        titles = TradeRankActivityDetail.query.filter(
            TradeRankActivityDetail.trade_activity_id.in_(ids),
            TradeRankActivityDetail.lang == Language.ZH_HANS_CN
        ).with_entities(
            TradeRankActivityDetail.trade_activity_id,
            TradeRankActivityDetail.title
        ).all()
        title_map = dict(titles)
        items = []
        for item in pagination.items:
            item = item.to_dict()
            item['title'] = title_map.get(item['trade_activity_id'], '')
            items.append(item)

        return dict(
            total=pagination.total,
            items=items,
            report_at=report_at
        )


@ns.route('/referral-pictures')
@respond_with_code
class ReferralPictureListResource(Resource):
    model = ReferralPicture
    marshal_fields = {
        'id': fx_fields.Integer,
        'name': fx_fields.String(attribute=lambda x: x.file.name),
        'url': fx_fields.String(attribute=lambda x: x.file.static_url),
    }

    @classmethod
    @ns.use_kwargs(dict(
        lang=EnumField(model.Language, required=True),
        version=fields.Integer(missing=1),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-推荐图片-推荐图片列表"""
        query = cls.model.query.filter(
            cls.model.lang == kwargs['lang'],
            cls.model.status == cls.model.Status.VALID,
            cls.model.version == kwargs['version']
        ).order_by(cls.model.id.desc())
        items = query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        available_langs = [item for item in cls.model.Language]
        all_langs = language_cn_names()
        return dict(
            items=items,
            lang_map={k.name: v for k, v in all_langs.items() if k in available_langs}
        )


@ns.route('/referral-picture')
@respond_with_code
class ReferralPictureResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        lang=EnumField(ReferralPicture.Language, required=True),
        file_ids=fields.List(fields.Integer, required=True),
        version=fields.Integer(missing=ReferralPicture.NOW_VERSION),
    ))
    def post(cls, **kwargs):
        """运营-推荐图片-添加推荐图片"""
        for file_id in kwargs['file_ids']:
            if not File.query.get(file_id):
                raise InvalidArgument(message='file id不存在')
        for file_id in kwargs['file_ids']:
            record = ReferralPicture(
                file_id=file_id,
                lang=kwargs['lang'],
                version=kwargs['version'],
            )
            db.session.add(record)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.ReferralPicture,
                detail=dict(file_id=file_id, lang=record.lang),
            )
        db.session.commit()


# noinspection PyUnresolvedReferences
@ns.route('/referral-picture/<int:id_>')
@respond_with_code
class ReferralPictureDetailResource(Resource):

    @classmethod
    def delete(cls, id_):
        """运营-推荐图片-删除推荐图片"""
        record: ReferralPicture = ReferralPicture.query.get(id_)
        if not record:
            raise InvalidArgument(message='id不存在')
        db.session.delete(record)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ReferralPicture,
            detail=dict(id=id_, file_id=record.file_id, lang=record.lang),
        )


@ns.route('/app-versions')
@respond_with_code
class AppVersionsResource(Resource):
    UPDATE_TYPES = {
        AppVersion.UpdateType.FORCE.name: '强制升级',
        AppVersion.UpdateType.NOTICE.name: '提示升级',
        AppVersion.UpdateType.NONE.name: '不要求升级',
        AppVersion.UpdateType.ONLY_ONCE.name: '只提醒一次',
    }

    @classmethod
    @ns.use_kwargs(dict(
        platform=EnumField(AppVersion.Platform),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-App版本-列表"""
        query = AppVersion.query \
            .filter(AppVersion.status == AppVersion.Status.VALID)
        if (platform := kwargs.get('platform')) is not None:
            query = query.filter(AppVersion.platform == platform)
        records = query \
            .order_by(AppVersion.id.desc()) \
            .paginate(kwargs['page'], kwargs['limit'])

        descs = dict(
            AppVersionLangDetails.query.filter(
                AppVersionLangDetails.owner_id.in_([v.id for v in records.items]),
                AppVersionLangDetails.lang == Language.ZH_HANS_CN
            ).with_entities(
                AppVersionLangDetails.owner_id,
                AppVersionLangDetails.desc
            )
        )

        items = []
        row: AppVersion
        for row in records.items:
            r_dict = row.to_dict(enum_to_name=True)
            r_dict['desc'] = descs.get(row.id, '')
            items.append(r_dict)

        return dict(
            items=items,
            total=records.total,
            extra=dict(
                platforms=AppVersion.Platform,
                update_types=cls.UPDATE_TYPES,
                support_app_update=BusinessSettings.support_app_update
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        platform=EnumField(AppVersion.Platform, required=True),
        version=fields.String(required=True),
        build=fields.Integer(required=True),
        update_type=EnumField(AppVersion.UpdateType, required=True),
        file_url=fields.String(missing='')
    ))
    def post(cls, **kwargs):
        """运营-App版本--新建app版本"""
        platform = kwargs['platform']
        version = kwargs['version']

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.APPVersion,
            detail=kwargs,
        )

        return db.session_add_and_commit(AppVersion(
            platform=platform,
            version=version,
            build=kwargs['build'],
            update_type=kwargs['update_type'],
            file_url=kwargs['file_url']
        ))

    @classmethod
    @ns.use_kwargs(dict(
        support_app_update=fields.Boolean(required=True),
    ))
    def patch(cls, **kwargs):
        """运营-App版本-是否支持APP内升级"""
        BusinessSettings.support_app_update = kwargs["support_app_update"]


# noinspection PyUnresolvedReferences
@ns.route('/app-versions/<int:id_>')
@respond_with_code
class AppVersionResource(Resource):

    @classmethod
    def get(cls, id_):
        """运营-App版本-获取单条app版本"""

        lang_names = language_cn_names()
        extra = dict(
            languages={e.name: lang_names[e]
                       for e in AppVersion.AVAILABLE_LANGS},
            platforms=AppVersion.Platform,
            update_types=AppVersionsResource.UPDATE_TYPES,
            latest_app_desc=cls._get_latest_app_desc(cur_id=id_),
        )
        if not id_:
            return dict(
                extra=extra
            )

        row: AppVersion = AppVersion.query.get(id_)
        if row is None:
            raise RecordNotFound
        result = row.to_dict(enum_to_name=True)
        result.update(
            extra=extra
        )
        return result

    @classmethod
    def _get_latest_app_desc(cls, cur_id: int) -> dict:
        model = AppVersion
        rows = model.query.with_entities(
            model.id
        ).filter(
            model.id != cur_id
        ).order_by(
            model.id.desc()
        ).limit(10).all()
        app_ids = {row.id for row in rows}
        if not app_ids:
            return {}

        detail_model = AppVersionLangDetails
        detail_rows = detail_model.query.with_entities(
            detail_model.owner_id,
            detail_model.lang,
            detail_model.desc,
        ).filter(
            detail_model.owner_id.in_(app_ids)
        ).order_by(
            detail_model.owner_id.desc()
        ).all()
        latest_app_desc = defaultdict(dict)
        for detail_row in detail_rows:
            latest_app_desc[detail_row.owner_id][detail_row.lang.name] = detail_row.desc
        return latest_app_desc

    @classmethod
    @ns.use_kwargs(dict(
        version=fields.String,
        build=fields.Integer,
        update_type=EnumField(AppVersion.UpdateType),
        file_url=fields.String
    ))
    def patch(cls, id_, **kwargs):
        """运营-App版本-修改app版本"""
        row: AppVersion = AppVersion.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)
        if version := kwargs.get('version'):
            row.version = version
        if (build := kwargs.get('build')) is not None:
            row.build = build
        if (update_type := kwargs.get('update_type')) is not None:
            row.update_type = update_type
        if (file_url := kwargs.get('file_url')) and file_url != row.file_url:
            if not validate_url(file_url):
                raise InvalidArgument(
                    message=f'invalid file url: {file_url!r}')
            row.file_id = None
            row.file_md5 = None
            row.file_url = file_url
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.APPVersion,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

        return row

    @classmethod
    def delete(cls, id_):
        """运营-App版本-删除app版本"""
        row: AppVersion = AppVersion.query.get(id_)
        if row is None:
            raise RecordNotFound
        row.status = AppVersion.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.APPVersion,
            detail=dict(id=id_, platform=row.platform, version=row.version, build=row.build),
        )


@ns.route('/app-versions/<int:id_>/remark')
@respond_with_code
class AppVersionRemarkResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-App版本-修改备注"""
        row: AppVersion = AppVersion.query.get(id_)
        if row is None:
            raise RecordNotFound

        old_remark = row.remark
        row.remark = kwargs['remark']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.APPVersion,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=row.remark),
        )

        return row


# noinspection PyUnresolvedReferences
@ns.route('/app-versions/<int:id_>/file')
@respond_with_code
class AppVersionFileResource(Resource):

    CHUNK_DIR = '/tmp/chunks/'
    CHUNK_SIZE_LIMIT = 10 * 1024 * 1024  # MB
    TOTAL_SIZE_LIMIT = 500 * 1024 * 1024  # MB

    @classmethod
    def post(cls, id_):
        """运营-App版本-上传app文件"""
        row: AppVersion = AppVersion.query.get(id_)
        if row is None:
            raise RecordNotFound

        file = request.files.get('file')
        if not file:
            raise InvalidArgument(message="文件未上传")

        filename = request.form.get('name')
        chunk_number = int(request.form.get('chunkNumber'))
        total_chunks = int(request.form.get('totalChunks'))
        chunk_md5 = request.form.get('chunkMd5')
        file_md5 = request.form.get('fileMd5')
        total_size = int(request.form.get('size'))
        chunk_size = int(request.form.get('chunkSize'))

        format_file_name, format_file_type = os.path.splitext(filename)
        file_type = format_file_type.strip('.')
        md5_file_name = hashlib.md5(format_file_name.encode('utf-8')).hexdigest()

        if file_type not in {'apk'}:
            raise InvalidArgument(message="仅支持apk的文件上传")
        if chunk_size > cls.CHUNK_SIZE_LIMIT or total_size > cls.TOTAL_SIZE_LIMIT:
            raise FileTooBig

        chunk_content = file.read()
        file_helper = UploadFileHelper(cls.CHUNK_DIR)
        if not file_helper.is_match_md5(chunk_content, chunk_md5):
            raise InvalidArgument(message="文件MD5不一致，请重新上传")
        # 暂时不支持同文件名下的多并发上传
        file_cache = AdminFileUploadChunk(md5_file_name)
        if file_cache.exists() and total_chunks > int(file_cache.get()) > chunk_number:
            # 暂停/断开后继续上传
            return dict(is_completed=False, chunkNumber=int(file_cache.get())+1)

        chunk_filename = f'{md5_file_name}.{file_type}'
        file_helper.write_chunk_file(chunk_filename, chunk_content, chunk_number)
        file_cache.set(chunk_number, ex=86400)

        if chunk_number == total_chunks:  # 表示上传到了最后一个
            file_helper.merge_file(chunk_filename, total_chunks)
            file_content = file_helper.read_file(chunk_filename)
            if not file_helper.is_match_md5(file_content, file_md5):
                raise InvalidArgument(message="文件MD5不一致，请重新上传")

            filename = secure_filename(filename)
            file_key = AWSBucketPublic.new_file_key(key=f'coinex_mobile/{filename}')
            if not AWSBucketPublic.put_file(file_key, file_content):
                raise ServiceUnavailable
            url = AWSBucketPublic.get_file_url(file_key)

            file_row = db.session_add_and_commit(File(
                user_id=g.user.id,
                bucket=AWSBucketPublic.bucket_name,
                key=file_key,
                name=filename,
                size=total_size,
                provider=File.Provider.AWS,
                acl=File.ACLType.PUBLIC_READ
            ))

            file_md5 = hashlib.md5(file_content).hexdigest()
            row.file_id = file_row.id
            row.file_md5 = file_md5
            row.file_url = url
            db.session.commit()

            return dict(
                file_id=file_row.id,
                file_md5=file_md5,
                file_url=url,
                is_completed=True,
            )

        return dict(
            is_completed=False,
            chunkNumber=chunk_number+1,
        )


# noinspection PyUnresolvedReferences
@ns.route('/app-versions/<int:id_>/langs/<lang>')
@respond_with_code
class AppVersionsDescResource(Resource):

    @classmethod
    def get(cls, id_, lang):
        """运营-App版本-获取app版本描述"""
        row = cls.get_row(id_, lang)
        if row is None:
            raise RecordNotFound
        return dict(
            desc=row.desc
        )

    @classmethod
    @ns.use_kwargs(dict(
        desc=fields.String(required=True)
    ))
    def put(cls, id_, lang, **kwargs):
        """运营-App版本-编辑app版本描述"""
        if (row := cls.get_row(id_, lang)) is None:
            row = AppVersionLangDetails(
                owner_id=id_,
                lang=lang
            )
            db.session.add(row)
        old_data = dict(desc=row.desc)
        row.desc = kwargs['desc']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.APPVersion,
            old_data=old_data,
            new_data=dict(desc=row.desc),
            special_data=dict(owner_id=id_, lang=lang),
        )

        return row

    @classmethod
    def get_row(cls, id_, lang) -> Optional[AppVersionLangDetails]:
        if not isinstance((lang := getattr(Language, lang, None)), Language):
            raise InvalidArgument
        return AppVersionLangDetails.query \
            .filter(AppVersionLangDetails.owner_id == id_,
                    AppVersionLangDetails.lang == lang) \
            .first()


class PopupWindowMixin:

    @classmethod
    def get_status_values(cls, start_t, end_t, now_t):
        if start_t > now_t:
            status = 'pending'
        elif end_t < now_t:
            status = 'offline'
        else:
            status = 'online'
        return status

    @classmethod
    def validate_params_and_business(cls, params, id_=None):
        cls.validate_params(params)
        # cls.validate_business(params, id_)

    @classmethod
    def validate_params(cls, params):
        if params['started_at'] > params['ended_at']:
            raise InvalidArgument(message='结束时间小于开始时间')
        if params['ended_at'] < now():
            raise InvalidArgument(message='结束时间小于当前时间')

    @classmethod
    def validate_business(cls, params, id_):
        start, end = params['started_at'], params['ended_at']

        # 产品要求 如果位置为右下角，同一个时间段，同一个页面只支持配置一个弹窗
        if params["pop_position"] == PopupWindow.PopPosition.CORNER:
            rows = PopupWindow.query.filter(
                PopupWindow.status == PopupWindow.Status.VALID,
                PopupWindow.ended_at >= max(start, now()),  # 过滤掉已过期的活动
                PopupWindow.started_at <= end,  # 过滤掉 开始时间小于本次结束时间 的活动
                PopupWindow.pop_position == PopupWindow.PopPosition.CORNER,
            ).with_entities(
                PopupWindow.trigger_pages,
                PopupWindow.id,
                PopupWindow.started_at,
                PopupWindow.ended_at,
            )
            # 过滤自身活动
            if id_:
                rows = rows.filter(PopupWindow.id != id_)
            if (platform := params["platform"]) != PopupWindow.Platform.ALL:
                rows = rows.filter(PopupWindow.platform.in_([PopupWindow.Platform.ALL, platform]))
            params_page_set = {i["trigger_page"] for i in params["trigger_pages"]}
            for row in rows:
                trigger_pages = json.loads(row["trigger_pages"])
                if not trigger_pages:
                    continue
                row_page_set = {i.get("trigger_page") for i in trigger_pages}
                # 全部页包含所有页
                if any([
                    PopupWindow.TriggerPage.ALL.name in params_page_set,
                    PopupWindow.TriggerPage.ALL.name in row_page_set,
                    params_page_set & row_page_set
                ]):
                    dt_format = partial(datetime_to_str, offset_minutes=60 * 8)  # to utc+8 str
                    raise InvalidArgument(
                        message=f'显示位置为右下角的弹窗已存在; id: {row.id} 开始时间 {dt_format(row.started_at)} '
                                f'结束时间 {dt_format(row.ended_at)}; 请修改 活动时间 不与之重叠'
                    )

    @classmethod
    def trim_trigger_pages(cls, trigger_pages: list):
        pages = []
        model: PopupWindow = cls.model
        for trigger_page_map in trigger_pages:
            trigger_page = trigger_page_map.get('trigger_page')
            param_type = trigger_page_map.get('param_type')
            page_op = trigger_page_map.get('page_op')
            trigger_page_params = trigger_page_map.get('trigger_page_params') or ''
            if trigger_page in [
                model.TriggerPage.HOME.name,
                model.TriggerPage.ALL.name,
                model.TriggerPage.ASSETS.name,
                model.TriggerPage.FIAT.name,
                model.TriggerPage.QUOTES.name,
            ]:
                trigger_page_params = ''
                param_type = ''
                page_op = ''
            elif trigger_page == model.TriggerPage.ASSET_DATA.name:
                param_type = model.TriggerPageParamType.ASSET.name
            else:
                pass
            pages.append({
                'trigger_page': trigger_page,
                'param_type': param_type,
                'page_op': page_op,
                'trigger_page_params': trigger_page_params,
            })

        pages = pages or ''
        if pages:
            pages = json.dumps(pages)
        return pages

    @classmethod
    def get_assets_and_markets(cls):
        perpetual_markets = PerpetualMarketCache().get_market_list()
        perpetual_assets = []
        for m in perpetual_markets:
            asset = PerpetualMarketCache.get_amount_asset(m)
            perpetual_assets.append(asset)
        markets_detail = MarketCache.online_markets_detail()
        margin_markets_mapping = MarginAccountIdCache.list_online_markets()
        markets = list(markets_detail.keys())
        margin_markets = list(margin_markets_mapping.values())
        margin_assets = []
        for margin_market in margin_markets:
            market = markets_detail.get(margin_market)
            if not market:
                continue
            margin_assets.append(market['base_asset'])

        assets = list_all_assets()
        margin_assets = list(set(margin_assets))
        margin_assets.sort(key=lambda asset: assets.index(asset))
        return markets, margin_markets, assets, margin_assets, perpetual_markets, list(set(perpetual_assets))

    @classmethod
    def update_group_data(cls, row, group_ids):
        groups = UserTagGroupBiz.filter_tag_group_ids(ids=group_ids)
        _, users = PopupWindowUserParser(row).parse()
        row.user_bitmap = BitMap(users).serialize()
        row.target_user_number = len(users)
        row.groups = json.dumps(groups)
        return row


@ns.route('/popup-windows')
@respond_with_code
class PopupWindowsResource(PopupWindowMixin, Resource):
    model = PopupWindow
    STATUSES = dict(
        pending='待上架',
        online='上架中',
        offline='已下架'
    )

    FREQUENCIES = {
        model.Frequency.ONCE.value: '一次',
        model.Frequency.EVERY_DAY.value: '每天',
        model.Frequency.EVERY_TIME.value: '每次'
    }

    TRIGGER_PAGES = {
        page.name: page.value for page in model.TriggerPage
    }

    PLATFORMS = {
        p.name: p.value for p in model.Platform
    }

    FILTER_TYPES = {
        model.FilterType.FILTERS.value: '定向用户',
        model.FilterType.NONE.value: '全部用户'
    }

    CONTENT_STYLE = {
        model.ContentStyle.TEXT.value: '纯文字',
        model.ContentStyle.ARTICLE.value: '图文结合',
        model.ContentStyle.IMAGE.value: '纯图片',
    }

    POP_POSITION = {
        model.PopPosition.CENTER.value: '正中间',
        model.PopPosition.CORNER.value: '右下角/顶部'
    }

    extra = dict(
        statuses=STATUSES,
        frequencies=FREQUENCIES,
        trigger_pages=TRIGGER_PAGES,
        param_types=model.TriggerPageParamType,
        page_ops=model.TriggerPageOp,
        platforms=PLATFORMS,
        filter_types=FILTER_TYPES,
        jump_types=model.JumpType,
        content_style=CONTENT_STYLE,
        pop_position=POP_POSITION,
        langs={x.name: y for x, y in language_cn_names().items() if x in PopupWindow.AVAILABLE_LANGS}
    )

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
        status=EnumField(list(STATUSES)),
        platform=EnumField(model.Platform),
        name=fields.String,
    ))
    def get(cls, **kwargs):
        """运营-弹窗管理"""
        model = cls.model
        _now = now()
        query = model.query.filter(model.status == model.Status.VALID)
        if status := kwargs.get('status'):
            if status == 'pending':
                query = query.filter(model.started_at > _now)
            elif status == 'online':
                query = query.filter(model.started_at <= _now, model.ended_at >= _now)
            else:
                query = query.filter(model.ended_at < _now)
        if platform := kwargs.get('platform'):
            query = query.filter(model.platform == platform)
        if name := kwargs.get('name'):
            query = query.filter(model.name.contains(name))
        records = query.order_by(model.sort_id.desc()).paginate(kwargs['page'], kwargs['limit'])
        result = []
        for x in records.items:
            status = cls.get_status_values(x.started_at, x.ended_at, _now)
            result.append(dict(
                id=x.id,
                name=x.name,
                started_at=x.started_at,
                ended_at=x.ended_at,
                platform=x.platform.name,
                trigger_pages=x.get_trigger_pages(),
                frequency=x.frequency,
                target_user_number=('-' if x.filter_type == model.FilterType.NONE else x.target_user_number),
                status=status,
                page_view=x.page_view,
                click_rate=f'{(x.click_count / x.page_view) * 100:.2f}%' if x.page_view else 0,
            ))

        markets, margin_markets, assets, margin_assets, perpetual_markets, perpetual_assets = cls.get_assets_and_markets()
        return dict(
            items=result,
            total=records.total,
            extra={
                'markets': markets,
                'margin_markets': margin_markets,
                'assets': assets,
                'margin_assets': margin_assets,
                'perpetual_markets': perpetual_markets,
                'perpetual_assets': perpetual_assets,
                **cls.extra
            }
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        started_at=TimestampField(required=True),
        ended_at=TimestampField(required=True),
        platform=EnumField(model.Platform, required=True),
        trigger_pages=fields.List(fields.Dict(required=True), required=True),
        frequency=EnumField(model.Frequency, enum_by_value=True, required=True),
        filter_type=EnumField(model.FilterType, enum_by_value=True, required=True),
        groups=fields.List(fields.Integer),
        whitelist_enabled=fields.Boolean(missing=False),
        user_whitelist=fields.String(required=False),
        jump_page_enabled=fields.Boolean(missing=False),
        jump_type=EnumField(model.JumpType),
        jump_id=fields.Integer,
        pop_position=EnumField(model.PopPosition, enum_by_value=True, required=True),
        content_style=EnumField(model.ContentStyle, enum_by_value=True, required=True),
    ))
    def post(cls, **kwargs):
        """运营-弹窗管理-新增弹窗"""
        model = cls.model
        cls.validate_params_and_business(kwargs)

        sort_id = model.query.filter(
            model.status == model.Status.VALID
        ).with_entities(func.max(model.sort_id)).scalar() or 0
        row = model(
            name=kwargs['name'],
            started_at=kwargs['started_at'],
            ended_at=kwargs['ended_at'],
            platform=kwargs['platform'],
            trigger_pages=cls.trim_trigger_pages(kwargs['trigger_pages']),
            frequency=kwargs['frequency'],
            filter_type=kwargs['filter_type'],
            whitelist_enabled=kwargs['whitelist_enabled'],
            user_whitelist=kwargs.get('user_whitelist') or '',
            jump_page_enabled=kwargs['jump_page_enabled'],
            jump_type=kwargs.get('jump_type'),
            jump_id=kwargs.get('jump_id'),
            sort_id=sort_id + 1,
            pop_position=kwargs.get('pop_position'),
            content_style=kwargs.get('content_style'),
        )
        filter_type = row.filter_type = kwargs['filter_type']
        if filter_type == model.FilterType.FILTERS:
            if not (groups := kwargs.get('groups')):
                raise InvalidArgument
            cls.update_group_data(row, groups)

        obj = db.session_add_and_commit(row)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.PopupWindows,
            detail=kwargs,
        )
        return {"id": obj.id}


@ns.route('/popup-windows/<int:id_>')
@respond_with_code
class PopupWindowResource(PopupWindowMixin, Resource):
    model = PopupWindow

    @classmethod
    def get(cls, id_):
        """运营-弹窗管理-弹窗详情"""
        markets, margin_markets, assets, margin_assets, perpetual_markets, perpetual_assets = cls.get_assets_and_markets()
        extra = {
            'markets': markets,
            'margin_markets': margin_markets,
            'assets': assets,
            'margin_assets': margin_assets,
            'perpetual_markets': perpetual_markets,
            'perpetual_assets': perpetual_assets,
            **PopupWindowsResource.extra
        }
        if id_ == 0:
            return dict(extra=extra)

        row = PopupWindow.query.get(id_)
        if not row:
            raise InvalidArgument
        rows = PopupWindowContent.query.filter(PopupWindowContent.popup_window_id == id_).all()
        rows = {x.lang.name: x for x in rows}
        contents = {}
        has_content = False
        for lang in PopupWindow.AVAILABLE_LANGS:
            lang = lang.name
            r = rows.get(lang)
            if r:
                contents[lang] = dict(
                    lang=lang,
                    title=r.title,
                    content=r.content,
                    url=r.url,
                    images=r.images or [],
                    summary=r.summary
                )
                has_content = True
            else:
                contents[lang] = dict(
                    lang=lang,
                    title='',
                    content='',
                    url='',
                    images=[],
                    summary='',
                )
        extra.update(tag_groups=UserTagGroupBiz.get_tag_group_info(row.get_groups()))
        return dict(
            record=dict(
                id=row.id,
                name=row.name,
                started_at=row.started_at,
                ended_at=row.ended_at,
                created_at=row.created_at,
                platform=row.platform.name,
                trigger_pages=row.get_trigger_pages(),
                jump_page_enabled=row.jump_page_enabled,
                jump_type=row.jump_type.name if row.jump_type else None,
                jump_id=row.jump_id,
                whitelist_enabled=row.whitelist_enabled,
                user_whitelist=row.user_whitelist,
                frequency=row.frequency,
                filter_type=row.filter_type,
                filters=json.loads(row.filters) if row.filters else None,
                target_user_number=('-' if row.filter_type == PopupWindow.FilterType.NONE
                                    else row.target_user_number),
                content_style=row.content_style,
                pop_position=row.pop_position,
                status=cls.get_status_values(row.started_at, row.ended_at, now()),
                has_content=has_content,
            ),
            contents=contents,
            extra=extra,
            statistic=cls.get_statistic_data(row),
        )

    @classmethod
    def get_statistic_data(cls, row):
        data = {}

        cls_map = {
            "ALL": (PopupPageViewStatistic, PopupClickCountStatistic),
            "WEB": (WebPopupPageViewStatistic, WebPopupClickCountStatistic),
            "ANDROID": (AndroidPopupPageViewStatistic, AndroidPopupClickCountStatistic),
            "IOS": (IosPopupPageViewStatistic, IosPopupClickCountStatistic),
        }
        for plat, (page_cls, click_cls) in cls_map.items():
            page_count = page_cls.count(row.id)
            click_count = click_cls.count(row.id)
            data[plat] = {
                "platform": plat,
                "page_view": page_count,
                "click_count": click_count,
                "click_rate": safe_percent_format(click_count, page_count),
                "series_data": cls._get_series_data(page_cls, click_cls, row)
            }
        andi, ios = data["ANDROID"], data["IOS"]
        app_page_view = andi["page_view"] + ios["page_view"]
        app_click_count = andi["click_count"] + ios["click_count"]
        tmp_key = 'APP'
        data[tmp_key] = {
            "platform": tmp_key,
            "page_view": app_page_view,
            "click_count": app_click_count,
            "click_rate": safe_percent_format(app_click_count, app_page_view),
            "series_data": cls._get_app_series_data(row)
        }
        return data

    @classmethod
    def _get_series_data(cls, page_cls, click_cls, row):
        series_data = page_cls.get_series_data(row)
        click_count_data = click_cls.get_series_data(row)
        for delta, data_list in series_data.items():
            for idx, data in enumerate(data_list):
                data["value"].update(click_count_data[delta][idx]["value"])
        return series_data

    @classmethod
    def _get_app_series_data(cls, row):
        andi_page_data = AndroidPopupPageViewStatistic.get_series_data(row)
        andi_click_data = AndroidPopupClickCountStatistic.get_series_data(row)
        ios_page_data = IosPopupPageViewStatistic.get_series_data(row)
        ios_click_data = IosPopupClickCountStatistic.get_series_data(row)
        for delta, data_list in andi_page_data.items():
            for idx, data in enumerate(data_list):
                data["value"].update(andi_click_data[delta][idx]["value"])
                data["value"]["page_view"] += ios_page_data[delta][idx]["value"]["page_view"]
                data["value"]["click_count"] += ios_click_data[delta][idx]["value"]["click_count"]
        return andi_page_data

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        started_at=TimestampField(required=True),
        ended_at=TimestampField(required=True),
        platform=EnumField(model.Platform, required=True),
        trigger_pages=fields.List(fields.Dict(required=True), required=True),
        frequency=EnumField(model.Frequency, enum_by_value=True, required=True),
        filter_type=EnumField(model.FilterType, enum_by_value=True, required=True),
        groups=fields.List(fields.Integer),
        whitelist_enabled=fields.Boolean(missing=False),
        user_whitelist=fields.String(required=False),
        jump_page_enabled=fields.Boolean(missing=False),
        jump_type=EnumField(model.JumpType),
        jump_id=fields.Integer,
        content_style=EnumField(model.ContentStyle, enum_by_value=True, required=True),
        pop_position=EnumField(model.PopPosition, enum_by_value=True, required=True)
    ))
    def patch(cls, id_, **kwargs):
        """运营-弹窗管理-编辑弹窗"""
        cls.validate_params_and_business(kwargs, id_)
        row = PopupWindow.query.get(id_)
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)

        row.name = kwargs['name']
        row.started_at = kwargs['started_at']
        row.ended_at = kwargs['ended_at']
        row.platform = kwargs['platform']
        row.frequency = kwargs['frequency']
        row.whitelist_enabled = kwargs['whitelist_enabled']
        row.user_whitelist = kwargs.get('user_whitelist') or ''
        row.jump_page_enabled = kwargs['jump_page_enabled']
        row.jump_type = kwargs.get('jump_type')
        row.jump_id = kwargs.get('jump_id')
        row.content_style = kwargs.get('content_style')
        row.pop_position = kwargs.get('pop_position')
        row.trigger_pages = cls.trim_trigger_pages(kwargs['trigger_pages'])
        if filter_type := kwargs.get('filter_type'):
            row.filter_type = filter_type
            row.users = None
            row.target_user_number = 0
            if filter_type == PopupWindow.FilterType.FILTERS:
                if not (groups := kwargs.get('groups')):
                    raise InvalidArgument
                cls.update_group_data(row, groups)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.PopupWindows,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    def delete(cls, id_):
        """运营-弹窗管理-删除弹窗"""
        row = PopupWindow.query.get(id_)
        if not row:
            raise InvalidArgument
        row.status = PopupWindow.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.PopupWindows,
            detail=dict(id=id_, name=row.name),
        )
        return {}


@ns.route('/popup-windows/<int:id_>/sort')
@respond_with_code
class PopupWindowSortResource(Resource):

    @classmethod
    def put(cls, id_):
        """运营-弹窗管理-编辑排序"""
        row = PopupWindow.query.get(id_)
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)
        other = PopupWindow.query.filter(
            PopupWindow.status == PopupWindow.Status.VALID,
            PopupWindow.sort_id > row.sort_id
        ).order_by(PopupWindow.sort_id.desc()).first()
        if other:
            row.sort_id, other.sort_id = other.sort_id, row.sort_id
            db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.PopupWindows,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return {}


@ns.route('/popup-windows/<int:id_>/offline')
@respond_with_code
class PopupWindowOfflineResource(Resource):

    @classmethod
    def put(cls, id_):
        """运营-弹窗管理-停止活动"""
        row = PopupWindow.query.get(id_)
        if not row:
            raise InvalidArgument
        row.ended_at = now()
        db.session.commit()

        AdminOperationLog.new_stop(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.PopupWindows,
            detail=dict(id=id_, name=row.name),
        )
        return {}


@ns.route('/popup-windows/<int:id_>/langs/<lang>')
@respond_with_code
class PopupWindowContentResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String(missing=''),
        content=fields.String(missing=''),
        url=fields.String(missing=''),
        images=fields.List(fields.String, missing=[], allow_none=True),
        content_style=EnumField(PopupWindow.ContentStyle, enum_by_value=True, required=True),
        pop_position=EnumField(PopupWindow.PopPosition, enum_by_value=True, required=True),
        summary=fields.String(missing=''),
    ))
    def put(cls, id_, lang, **kwargs):
        """运营-弹窗管理-编辑弹窗内容"""
        row = PopupWindow.query.get(id_)
        if not row:
            raise InvalidArgument
        if lang not in [e.name for e in PopupWindow.AVAILABLE_LANGS]:
            raise InvalidArgument

        content_style = kwargs['content_style']
        pop_position = kwargs['pop_position']
        if content_style == PopupWindow.ContentStyle.IMAGE:
            cls.check_image(kwargs, lang)
        elif pop_position == PopupWindow.PopPosition.CORNER and content_style == PopupWindow.ContentStyle.TEXT:
            cls.check_title(kwargs, lang)
        else:
            cls.check_title_and_content(kwargs, lang)

        row = PopupWindowContent.query.filter(
            PopupWindowContent.popup_window_id == id_,
            PopupWindowContent.lang == lang
        ).first()
        if row:
            old_data = row_to_dict(row, enum_to_name=True)
            row.title = kwargs['title']
            row.content = kwargs['content']
            row.images = kwargs['images']
            row.url = kwargs['url']
            row.summary = kwargs['summary']

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.PopupWindowsContent,
                old_data=old_data,
                new_data=row_to_dict(row, enum_to_name=True),
                special_data=dict(popup_window_id=id_, lang=lang),
            )
        else:
            row = PopupWindowContent(
                popup_window_id=id_,
                lang=lang,
                title=kwargs['title'],
                url=kwargs['url'],
                content=kwargs['content'],
                images=kwargs['images'],
                summary=kwargs['summary']
            )
            db.session.add(row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.PopupWindowsContent,
                detail=row_to_dict(row, enum_to_name=True),
            )
        db.session.commit()

    @classmethod
    def check_title_and_content(cls, kwargs, lang):
        if not kwargs['title'] or not kwargs['content']:
            raise InvalidArgument(message=f"图文结合样式 {lang}标题或者内容不能为空")

    @classmethod
    def check_title(cls, kwargs, lang):
        if not kwargs['title']:
            raise InvalidArgument(message=f"样式为右下角 + 纯文字 {lang} 标题不能为空")

    @classmethod
    def check_image(cls, kwargs, lang):
        if not kwargs['images']:
            raise InvalidArgument(message=f"纯图片样式 {lang}图片列表不能为空")


class Reset2FAMixin(AssignAuditorMixin):

    class BalanceChoice(Enum):
        GTE_1W = '大于等于1W刀'
        LT_1W = '小于1W刀'
        GTE_200 = '大于等于200刀'
        LT_200 = '小于200刀'
        LT_1 = '小于1刀'
        EQ_0 = '0资产'  # ＜0.01刀则视为0资产

    @classmethod
    def get_query(cls, kwargs, business='', auditor_ids_map: dict = None):
        if not business:
            query = SecurityResetApplication.query.filter(
                SecurityResetApplication.reset_type.in_(SecurityResetApplication.ADMIN_SECURITY_RESET_LIST))
        else:
            query = SecurityResetApplication.query.filter(
                SecurityResetApplication.reset_type.in_(SecurityResetApplication.ADMIN_UNFREEZE_RESET_LIST))

        if kyc_status := kwargs.get('kyc_status'):
            query = query.join(
                User,
                SecurityResetApplication.user_id == User.id,
                isouter=True,
            ).filter(
                User.kyc_status == kyc_status
            )
        if status := kwargs.get('status'):
            query = query.filter(
                SecurityResetApplication.status == status
            )
        if identity_type := kwargs.get('identity_type'):
            query = query.filter(
                SecurityResetApplication.identity_type == identity_type
            )
        if flow := kwargs.get('flow'):
            query = query.filter(
                SecurityResetApplication.flow == flow
            )
        if data_type := kwargs.get('data_type'):
            query = query.filter(
                SecurityResetApplication.data_type == data_type
            )
        if balance_choice := kwargs.get('balance_choice'):
            if balance_choice is cls.BalanceChoice.GTE_1W:
                query = query.filter(
                    SecurityResetApplication.balance_usd >= Decimal('10000')
                )
            elif balance_choice is cls.BalanceChoice.LT_1W:
                query = query.filter(
                    SecurityResetApplication.balance_usd < Decimal('10000')
                )
            elif balance_choice is cls.BalanceChoice.GTE_200:
                query = query.filter(
                    SecurityResetApplication.balance_usd >= Decimal('200')
                )
            elif balance_choice is cls.BalanceChoice.LT_200:
                query = query.filter(
                    SecurityResetApplication.balance_usd < Decimal('200')
                )
            elif balance_choice is cls.BalanceChoice.LT_1:
                query = query.filter(
                    SecurityResetApplication.balance_usd < Decimal('1')
                )
            elif balance_choice is cls.BalanceChoice.EQ_0:
                query = query.filter(
                    SecurityResetApplication.balance_usd < Decimal('0.01')
                )
        if type_ := kwargs.get('type'):
            query = query.filter(
                SecurityResetApplication.reset_type == type_
            )
        pass_type = kwargs.get('pass_type')
        if pass_type:
            if pass_type == SecurityResetMethod.ANSWER:
                # 系统审核
                query = query.filter(
                    or_(
                        and_(
                            SecurityResetApplication.status == SecurityResetApplication.StatusType.LIVENESS_CHECK_REQUIRE,
                            SecurityResetApplication.balance_usd < SecurityResetApplication.LIVENESS_AUDIT_BALANCE_USD_THRESHOLD
                        ),
                        and_(
                            SecurityResetApplication.status.in_([
                                SecurityResetApplication.StatusType.PASSED, SecurityResetApplication.StatusType.REJECTED
                            ]),
                            SecurityResetApplication.auditor_id.is_(None),
                            SecurityResetApplication.checker_id.is_(None),
                        )
                    ),
                )
            else:
                # 人工审核
                query = query.filter(
                    or_(
                        SecurityResetApplication.status.in_([
                            SecurityResetApplication.StatusType.CREATED, SecurityResetApplication.StatusType.AUDITED,
                        ]),
                        and_(
                            SecurityResetApplication.status == SecurityResetApplication.StatusType.LIVENESS_CHECK_REQUIRE,
                            SecurityResetApplication.balance_usd >= SecurityResetApplication.LIVENESS_AUDIT_BALANCE_USD_THRESHOLD
                        ),
                        and_(
                            SecurityResetApplication.status.in_([
                                SecurityResetApplication.StatusType.PASSED, SecurityResetApplication.StatusType.REJECTED
                            ]),
                            or_(
                                SecurityResetApplication.auditor_id.is_not(None),
                                SecurityResetApplication.checker_id.is_not(None)
                            )
                        )
                    )
                )
        if start_date := kwargs.get('start_date'):
            query = query.filter(
                SecurityResetApplication.created_at >= start_date
            )
        if end_date := kwargs.get('end_date'):
            query = query.filter(
                SecurityResetApplication.created_at < end_date
            )
        if keyword := kwargs.get('keyword', '').strip():
            keyword_results = User.search_for_users(keyword)
            query = query.filter(SecurityResetApplication.user_id.in_(keyword_results))
        if (auditor_id := kwargs.get('auditor_id')) is not None:
            if auditor_id == 0:
                application_ids = {r.id for r in SecurityResetApplication.query.filter(
                    SecurityResetApplication.status.in_(
                        [SecurityResetApplication.StatusType.CREATED, SecurityResetApplication.StatusType.AUDITED]
                    )
                ).with_entities(
                    SecurityResetApplication.id
                ).all()}
                assigned_ids = set()
                for ids in auditor_ids_map.values():
                    assigned_ids |= set(ids)
                application_ids -= assigned_ids
            else:
                application_ids = auditor_ids_map.get(str(auditor_id), [])
            query = query.filter(
                SecurityResetApplication.id.in_(application_ids)
            )
        if new_email := kwargs.get('new_email'):
            query = query.filter(SecurityResetApplication.new_email == new_email)

        return cls.get_sort_query(query, kwargs, kwargs.get('to_next_field', None))

    @classmethod
    def get_sort_query(cls, query, kwargs, to_next_field=None):
        order, order_type = kwargs.get("order"), kwargs.get("order_type")
        # created_at 和 id 相关联，id 有排序可以利用索引
        if order == 'created_at':
            order = 'id'
        query = query.order_by(getattr(getattr(SecurityResetApplication, order), order_type)())

        if to_next_field:
            field = getattr(SecurityResetApplication, order)
            if order_type == "desc":
                query = query.filter(field < to_next_field)
            else:
                query = query.filter(field > to_next_field)

        return query

    @classmethod
    def get_reject_count(cls, row, reset_types) -> int:
        model = SecurityResetApplication
        base_query = model.query.filter(model.reset_type.in_(reset_types))
        pre_succeed = base_query.with_entities(
            model.id
        ).filter(
            model.id < row.id,
            model.user_id == row.user_id,
            model.status == model.StatusType.PASSED,
        ).order_by(
            model.id.desc()
        ).first()
        query = base_query.with_entities(
            func.count('*')
        ).filter(
            model.id < row.id,
            model.user_id == row.user_id,
            model.status == model.StatusType.REJECTED,
        )
        if pre_succeed:
            query = query.filter(model.id > pre_succeed.id)
        return query.scalar() or 0

    @classmethod
    def get_record_by_id(cls, record_id, business='') -> SecurityResetApplication:
        model = SecurityResetApplication
        if not business:
            query = model.query.filter(
                model.reset_type.in_(model.ADMIN_SECURITY_RESET_LIST))
        else:
            query = model.query.filter(
                model.reset_type.in_(model.ADMIN_UNFREEZE_RESET_LIST))
        record = query.filter(model.id == record_id).first()
        if not record:
            raise RecordNotFound

        return record

    @classmethod
    def get_last_rejected_reason(cls, user_id: int, application_id: int, reset_types: List):
        record = SecurityResetApplication.query.filter(
            SecurityResetApplication.id < application_id,
            SecurityResetApplication.user_id == user_id,
            SecurityResetApplication.reset_type.in_(reset_types),
            SecurityResetApplication.status == SecurityResetApplication.StatusType.REJECTED,
        ).order_by(SecurityResetApplication.id.desc()).first()
        return record.get_reject_reason() if record else ''


@ns.route('/2fa-reset')
@respond_with_code
class Reset2FAResource(Resource, Reset2FAMixin):
    reset_type_map = dict(
        TOTP="重置TOTP",
        MOBILE="重置手机",
        EMAIL="重置邮箱",
        WEBAUTHN="重置通行密钥",
        WITHDRAW_PASSWORD="重置提现密码"
    )

    reset_status_map = dict(
        LIVENESS_CHECK_REQUIRE="待生物识别验证",
        CREATED="待初审",
        AUDITED="待复审",
        PASSED="已重置",
        REJECTED="已拒绝"
    )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(SecurityResetApplication.StatusType),
        balance_choice=EnumField(Reset2FAMixin.BalanceChoice),
        type=EnumField(SecurityResetApplication.ResetType),
        flow=EnumField(SecurityResetApplication.FlowCase),
        data_type=fields.String,
        kyc_status=EnumField(User.KYCStatus),
        pass_type=EnumField(SecurityResetMethod),
        new_email=fields.String,
        start_date=TimestampField(is_ms=True),
        end_date=TimestampField(is_ms=True),
        keyword=fields.String,
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        order=EnumField(["created_at"], missing="id"),
        order_type=EnumField(["asc", "desc"], missing='desc'),
        auditor_id=fields.Integer,
    ))
    def get(cls, **kwargs):
        """
        运营-2FA重置管理
        """
        auditor_map = {}
        auditor_ids_map = SecurityResetApplicationAssignAuditorsCache().read()
        for auditor_id, kyc_ids in auditor_ids_map.items():
            for kyc_id in kyc_ids:
                auditor_map[kyc_id] = int(auditor_id)
        page, limit = kwargs['page'], kwargs['limit']
        query = cls.get_query(kwargs, auditor_ids_map=auditor_ids_map)
        records = query.paginate(page, limit, error_out=False)
        res = []
        items = records.items
        ids, user_ids = list(), set()
        for item in items:
            ids.append(item.id)
            user_ids.add(item.user_id)
        liveness_rows = LivenessCheckHistory.query.filter(
            LivenessCheckHistory.business == LivenessCheckHistory.Business.RESET_SECURITY,
            LivenessCheckHistory.business_id.in_(ids)
        ).with_entities(
            LivenessCheckHistory.id,
            LivenessCheckHistory.transaction_id,
            LivenessCheckHistory.business_id,
            LivenessCheckHistory.status,
        ).all()
        liveness_map = {i.business_id: dict(
            id=i.id,
            transaction_id=i.transaction_id,
            status=i.status,
        ) for i in liveness_rows}
        kyc_rows = KycVerification.query.filter(
            KycVerification.user_id.in_(user_ids),
            KycVerification.status == KycVerification.Status.PASSED
        ).with_entities(KycVerification.user_id, KycVerification.id, KycVerification.created_at).all()
        kyc_created_at_map = {row.user_id: row.created_at for row in kyc_rows}
        kyc_id_map = {row.user_id: row.id for row in kyc_rows}
        user_info = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(User.id, User.email, User.kyc_status).all()
        user_info_map = dict()
        for u in user_info:
            user_info_map[u.id] = dict(email=u.email, kyc_status=u.kyc_status.name)
        for item in items:
            item: SecurityResetApplication

            if item.status == SecurityResetApplication.StatusType.LIVENESS_CHECK_REQUIRE:
                if item.balance_usd >= SecurityResetApplication.LIVENESS_AUDIT_BALANCE_USD_THRESHOLD:
                    reset_method = "人工审核"
                else:
                    reset_method = "系统重置"
            elif item.status in [SecurityResetApplication.StatusType.PASSED, SecurityResetApplication.StatusType.REJECTED]:
                reset_method = "人工审核" if item.auditor_id or item.checker_id else "系统重置"
            else:
                # 待审核的
                reset_method = "人工审核"
            res.append(dict(
                id=item.id,
                created_at=item.created_at,
                audited_at=item.audited_at,
                checked_at=item.checked_at,
                user_id=item.user_id,
                email=user_info_map[item.user_id]['email'],
                kyc_status=user_info_map[item.user_id]['kyc_status'],
                reset_type=cls.reset_type_map[item.reset_type.name],
                flow=item.flow.name,
                data_type=item.data_type,
                rejection_reason=item.rejection_reason,
                is_custom_reason=item.is_custom_reason,
                remark=item.remark,
                status=item.status.name,
                kyc_id=kyc_id_map.get(item.user_id),
                kyc_created_at=kyc_created_at_map.get(item.user_id),
                reset_method=reset_method,
                balance_usd=amount_to_str(item.balance_usd, 2),
                auditor_id=item.checker_id if item.status not in [
                    SecurityResetApplication.StatusType.CREATED, SecurityResetApplication.StatusType.AUDITED
                ] else auditor_map.get(item.id),
                liveness_id=liveness_map[item.id]['id'] if liveness_map.get(item.id) else None,
                liveness_transaction_id=liveness_map[item.id]['transaction_id'] if liveness_map.get(item.id) else None,
                liveness_status=liveness_map[item.id]['status'].name if liveness_map.get(item.id) else '',
            ))
        auditor_ids = [item['auditor_id'] for item in res if item['auditor_id']]
        name_map = get_admin_user_name_map(auditor_ids)
        for item in res:
            item['auditor_name'] = name_map.get(item['auditor_id'], '--')
        audit_statuses = {SecurityResetApplication.StatusType.CREATED, SecurityResetApplication.StatusType.AUDITED}
        if status := kwargs.get('status'):
            audit_statuses &= {status}
        audit_required_ids = set()
        if audit_statuses:
            audit_required_ids = {i.id for i in SecurityResetApplication.query.filter(
                SecurityResetApplication.status.in_(audit_statuses)
            ).with_entities(
                SecurityResetApplication.id
            ).all()}
        return dict(
            auditor_list=cls.get_auditor_list(auditor_ids_map, audit_required_ids),
            statuses=cls.reset_status_map,
            types=cls.reset_type_map,
            flows=SecurityResetApplication.FlowCase,
            data_types=SecurityResetFile.FILE_DESC_LIST,
            kyc_statuses={
                User.KYCStatus.PASSED.name: "已实名",
                User.KYCStatus.NONE.name: "未实名",
                User.KYCStatus.FAILED.name: "失败",
                User.KYCStatus.PROCESSING.name: "处理中"
            },
            pass_types={
                SecurityResetMethod.CUSTOMER.name: "人工审核",
                SecurityResetMethod.ANSWER.name: "系统重置"
            },
            reset_types=cls.reset_type_map,
            balance_choices=cls.BalanceChoice,
            liveness_statuses=LivenessCheckHistory.Status,
            items=res,
            total=records.total
        )

    @staticmethod
    def _audit(result: bool,
               application: SecurityResetApplication,
               reason: str,
               is_custom_reason: bool,
               next_status: SecurityResetApplication.StatusType):
        user_id = application.user_id
        user = User.query.get(user_id)
        application.status = next_status
        # 重置失败
        if next_status == SecurityResetApplication.StatusType.REJECTED:
            if is_custom_reason:
                application.is_custom_reason = is_custom_reason
                application.custom_reason = reason
            else:
                application.reason = reason
            db.session.commit()
            SecurityBusiness.security_reset_reject_notice(application)
        # 重置成功
        elif next_status == SecurityResetApplication.StatusType.PASSED:
            if application.reset_type == SecurityResetApplication.ResetType.EMAIL:
                require_email_not_exists(application.new_email)
            if application.data_type == SecurityResetFile.LIVENESS_CHECK:
                op_role = SecurityToolHistory.OpRole.LIVENESS
            else:
                op_role = SecurityToolHistory.OpRole.AUDIT
            reset_security_info(user, application.reset_type, op_role,
                                application.new_email)
            db.session.commit()
            SecurityBusiness.security_reset_pass_notice(application)
        else:
            # 待复审
            db.session.commit()
        SecurityResetApplicationAssignAuditorsCache().finish(application.id)

    @classmethod
    @ns.use_kwargs(dict(
        result=fields.Boolean(required=True),
        application_id=fields.Integer(required=True),
        reason=fields.String,
        is_custom_reason=fields.Boolean(missing=False),
    ))
    def patch(cls, **kwargs):
        """
        运营-2FA重置管理-审核
        """
        result = kwargs['result']
        reason = kwargs.get('reason')
        if not result and not reason:
            raise InvalidArgument(message="拒绝申请必须注明原因")
        application = SecurityResetApplication.query.filter(
            SecurityResetApplication.id == kwargs['application_id']
        ).first()
        if application.status != SecurityResetApplication.StatusType.CREATED:
            raise InvalidArgument(message=f"操作失败，此申请状态为 {application.status.name}")

        if result:
            if application.balance_usd < SecurityResetApplication.SECURITY_AUDIT_REQUIRED_BALANCE_LIMIT:
                next_status = SecurityResetApplication.StatusType.PASSED
            else:
                next_status = SecurityResetApplication.StatusType.AUDITED
        else:
            next_status = SecurityResetApplication.StatusType.REJECTED
        application.auditor_id = g.user.id
        application.audited_at = now()
        cls._audit(result, application, reason, kwargs['is_custom_reason'], next_status)

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.Reset2FA,
            detail=dict(id=application.id, next_status=next_status.name),
            target_user_id=application.user_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        result=fields.Boolean(required=True),
        application_id=fields.Integer(required=True),
        reason=fields.String,
        is_custom_reason=fields.Boolean(missing=False),
    ))
    def put(cls, **kwargs):
        """
        运营-2FA重置管理-复审
        """
        result = kwargs['result']
        reason = kwargs.get('reason')
        if not result and not reason:
            raise InvalidArgument(message="拒绝申请必须注明原因")
        application = SecurityResetApplication.query.filter(
            SecurityResetApplication.id == kwargs['application_id']
        ).first()
        if application.status != SecurityResetApplication.StatusType.AUDITED:
            raise InvalidArgument(message=f"操作失败，此申请状态为 {application.status.name}")
        if g.user.id == application.auditor_id:
            raise InvalidArgument(message="初审和复审不能为同一人")
        if result:
            next_status = SecurityResetApplication.StatusType.PASSED
        else:
            next_status = SecurityResetApplication.StatusType.REJECTED
        application.checker_id = g.user.id
        application.checked_at = now()
        cls._audit(result, application, reason, kwargs['is_custom_reason'], next_status)

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.Reset2FA,
            detail=dict(id=application.id, next_status=next_status.name),
            target_user_id=application.user_id,
        )


@ns.route('/2fa-reset/auditors')
@respond_with_code
class Reset2FAAssignAuditorsResource(Resource, Reset2FAMixin):

    @classmethod
    @ns.use_kwargs(dict(
        auditor_id=fields.Integer(required=True),
        ids=fields.List(fields.Integer, required=True),
    ))
    def post(cls, **kwargs):
        """运营-2FA重置管理-批量分配审核人"""
        auditor_id = kwargs["auditor_id"]
        if auditor_id not in cls.get_auditor_map():
            raise InvalidArgument(message='请选择正确的审核人')
        ids = set(kwargs["ids"])
        if not ids:
            return
        ids = [r.id for r in SecurityResetApplication.query.filter(
            SecurityResetApplication.id.in_(ids),
            SecurityResetApplication.status.in_(
                [SecurityResetApplication.StatusType.CREATED, SecurityResetApplication.StatusType.AUDITED]
            )
        ).with_entities(
            SecurityResetApplication.id
        ).all()]
        if not ids:
            return
        SecurityResetApplicationAssignAuditorsCache().assign(auditor_id, ids)


@ns.route('/2fa-reset/<int:id_>/remark')
@respond_with_code
class Reset2FARemarkResource(Resource, Reset2FAMixin):

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-2FA重置管理-修改备注"""
        row: SecurityResetApplication = cls.get_record_by_id(id_)
        old_remark = row.remark
        row.remark = kwargs['remark']
        SecurityResetApplicationChangelog.add(
            application_id=row.id,
            user_id=row.user_id,
            admin_user_id=g.user.id,
            change_type=SecurityResetApplicationChangelog.ChangeType.REMARK,
            old_value=old_remark,
            new_value=row.remark,
            is_commit=False,
        )
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.Reset2FA,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=row.remark),
            target_user_id=row.user_id,
        )
        return row


@ns.route('/2fa-reset/<int:id_>/jump_url')
@respond_with_code
class Reset2FAJumpUrlResource(Resource, Reset2FAMixin):

    @classmethod
    @ns.use_kwargs(dict(
        jump_url=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-2FA重置管理-修改跳转链接"""
        row: SecurityResetApplication = cls.get_record_by_id(id_)
        old_jump_url = row.jump_url
        row.jump_url = kwargs['jump_url']
        SecurityResetApplicationChangelog.add(
            application_id=row.id,
            user_id=row.user_id,
            admin_user_id=g.user.id,
            change_type=SecurityResetApplicationChangelog.ChangeType.JUMP_URL,
            old_value=old_jump_url,
            new_value=row.jump_url,
            is_commit=False,
        )
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.Reset2FA,
            old_data=dict(jump_url=old_jump_url),
            new_data=dict(jump_url=row.jump_url),
            target_user_id=row.user_id,
        )
        return row


@ns.route("/2fa-reset/change-log")
@respond_with_code
class SecurityResetApplyChangeLogResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            start_time=TimestampField(is_ms=True),
            end_time=TimestampField(is_ms=True),
            user_id=fields.Integer,
            admin_user_id=fields.Integer,
            application_id=fields.Integer,
            change_type=EnumField(SecurityResetApplicationChangelog.ChangeType),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """风控-安全工具-安全工具重置审核-变更记录"""
        model = SecurityResetApplicationChangelog
        query = model.query.order_by(model.id.desc())
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if application_id := kwargs.get("application_id"):
            query = query.filter(model.application_id == application_id)
        if start_time := kwargs.get("start_time"):
            query = query.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            query = query.filter(model.created_at <= end_time)
        if change_type := kwargs.get("change_type"):
            query = query.filter(model.change_type == change_type.name)
        if admin_user_id := kwargs.get('admin_user_id'):
            query = query.filter(model.admin_user_id == admin_user_id)

        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows = pagination.items

        admin_user_name_map = get_admin_user_name_map({i.admin_user_id for i in rows if i.admin_user_id})
        user_ids = {i.user_id for i in rows}
        user_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email, User.name).all()
            user_map.update({i.id: i for i in chunk_users})

        items = []
        for row in rows:
            row: model
            d = row.to_dict(enum_to_name=True)
            _user = user_map.get(row.user_id)
            d["email"] = _user.email if _user else ""
            d["user_name"] = _user.name if _user else ""
            d["admin_user_name"] = admin_user_name_map.get(row.admin_user_id) or ""
            old = row.detail["old"]
            new = row.detail["new"]
            d["detail_str"] = f"{row.change_type.value} {repr(old)} 变为 {repr(new)}"
            items.append(d)

        return dict(
            items=items,
            total=pagination.total,
            extra=dict(
                change_type_dict={i.name: i.value for i in model.ChangeType},
            ),
        )


@ns.route('/2fa-reset/detail')
@respond_with_code
class Reset2FADetailResource(Resource, Reset2FAMixin):
    NEXT_QUERY_SCHEMA = dict(
        is_next=fields.Boolean,
        status=EnumField(SecurityResetApplication.StatusType),
        balance_choice=EnumField(Reset2FAMixin.BalanceChoice),
        type=EnumField(SecurityResetApplication.ResetType),
        kyc_status=EnumField(User.KYCStatus),
        pass_type=EnumField(SecurityResetMethod),
        start_date=TimestampField(is_ms=True),
        end_date=TimestampField(is_ms=True),
        keyword=fields.String,
        order=EnumField(["created_at"], missing="id"),
        order_type=EnumField(["asc", "desc"], missing='desc'),
    )
    DETAIL_SCHEMA = dict(
        application_id=fields.Integer(required=True),
    )

    @classmethod
    def get_category_reasons(cls) -> list[dict]:
        reason_em = SecurityResetApplication.Reason
        category_reasons_map = {
            "高频使用": [
                reason_em.INVALID_SNAPSHOT,
                reason_em.INVALID_THIRD_PARTY_WITHDRAWAL_DEPOSIT_SNAPSHOT,
                reason_em.INVALID_SNAPSHOT_AND_THP_WD_SNAPSHOT,
                reason_em.FACE_SHOWN_ANNOUNCEMENT_AND_ID_COMPLIANT,
            ],
            "邮件截图相关": [
                reason_em.EMAIL_ADDR_NOT_FULLY_SHOW,
                reason_em.EMAIL_ADDR_UNMATCHED,
                reason_em.INVALID_HISTORY_AND_THIRD_SNAPSHOT,
                reason_em.BLURRY_COINEX_EMAIL_SNAPSHOT,
            ],
            "第三方截图相关": [
                reason_em.BLOCK_CHAIN_SNAPSHOT,
                reason_em.THIRD_PARTY_SNAPSHOT_FROM_EMAIL,
                reason_em.THIRD_PARTY_SNAPSHOT_KEY_MSG_LOSS,
                reason_em.OBTAIN_VIABTC_WITHDRAWAL_DEPOSIT,
                reason_em.THIRD_PARTY_SNAPSHOT_FROM_OWN_COINEX_ACCOUNT,
                reason_em.SNAPSHOT_AND_THP_WD_SNAPSHOT_UNMATCHED,
                reason_em.BLURRY_THIRD_PARTY_SNAPSHOT,
                reason_em.INCOMPLETE_WITHDRAWAL_DEPOSIT_SNAPSHOT,
            ],
            "证件/手持照/人脸相关": [
                reason_em.INVALID_KYC_INFORMATION,
                reason_em.BLURRY_IDENTIFICATION,
                reason_em.UNSUPPORT_ID_TYPE,
                reason_em.ID_EXPIRED,
                reason_em.ID_DAMAGED,
                reason_em.NO_ORIGINAL_ID,
                reason_em.INVALID_TIME_FORMAT,
                reason_em.BLURRY_ANNOUNCEMENT,
                reason_em.ANNOUNCEMENT_UNMATCHED,
                reason_em.IDENTIFICATION_UNMATCHED,
                reason_em.PHOTO_FROM_SNAPSHOT,
                reason_em.BLURRY_VIDEO,
            ],
            "第三方录屏相关": [
                reason_em.VIDEO_WITHDRAWAL_DEPOSIT_UNMATCHED,
                reason_em.VIDEO_WITHDRAWAL_DEPOSIT_FROM_COUNTERPARTY,
                reason_em.VIDEO_FROM_ANOTHER_DEVICE,
                reason_em.VIDEO_NO_FACE_SHOWN,
                reason_em.VIDEO_OPEN_COUNTERPARTY,
                reason_em.PICTURE_TAMPERED,
            ],
            "其他通用": [
                reason_em.TICKET_CONTACTED,
            ],
            "生物识别验证": [
                reason_em.LIVENESS_BAD_FACE_COMPARISON,
                reason_em.LIVENESS_SELFIE_LIVENESS,
                reason_em.LIVENESS_WATERMARK,
                reason_em.LIVENESS_WITH_PHONE,
                reason_em.LIVENESS_MANY_PEOPLE,
                reason_em.LIVENESS_FAKE,
                reason_em.LIVENESS_FORCED,
                reason_em.LIVENESS_SELFIE_MISMATCH,
                reason_em.LIVENESS_SELFIE_WITH_ID,
                reason_em.LIVENESS_VIDEO_SELFIE,
                reason_em.LIVENESS_OTHER,
            ],
        }
        result = []
        for ct, rs in category_reasons_map.items():
            item = {
                "category": ct,
                "reasons": [dict(name=i.name, value=i.value) for i in rs],
            }
            result.append(item)
        return result

    @classmethod
    @ns.use_kwargs(dict(
        **DETAIL_SCHEMA,
        **NEXT_QUERY_SCHEMA
    ))
    def get(cls, **kwargs):
        """
        运营-2FA重置管理-详情页面
        """
        if kwargs.get("is_next"):
            application = cls.get_next(g.user.id, kwargs['application_id'], kwargs)
        else:
            application = cls.get_record_by_id(kwargs['application_id'])
        user_id = application.user_id
        user = User.query.get(user_id)
        kyc: KycVerification = KycVerification.query.filter(
            KycVerification.user_id == user_id,
            KycVerification.status == KycVerification.Status.PASSED
        ).first()
        files = SecurityResetFile.query.filter(
            SecurityResetFile.application_id == application.id
        ).all()
        file_map = dict()
        required_file_types = []
        for file in files:
            file: SecurityResetFile
            file_type = file.file_type.value
            required_file_types.append(file_type)
            file_map[file_type] = AWSBucketPrivate.get_file_url(file.file_key)
        if kyc:
            file_map['kyc_front_img_file'] = kyc.front_img_file.private_url if kyc.front_img_file else None
            file_map['kyc_back_img_file'] = kyc.back_img_file.private_url if kyc.back_img_file else None
            file_map['kyc_face_img_file'] = kyc.face_img_file.private_url if kyc.face_img_file else None
        liveness: LivenessCheckHistory = LivenessCheckHistory.query.filter(
            LivenessCheckHistory.business == LivenessCheckHistory.Business.RESET_SECURITY,
            LivenessCheckHistory.business_id == application.id
        ).first()
        if liveness and liveness.face_img_file_id:
            file_map['liveness_face_img_file'] = liveness.face_img_file_url

        enums = dict(
            kyc_statuses={
                User.KYCStatus.PASSED.name: "已实名",
                User.KYCStatus.NONE.name: "未实名",
                User.KYCStatus.FAILED.name: "失败",
                User.KYCStatus.PROCESSING.name: "处理中"
            },
            id_types=KycVerification.IDType,
            category_reasons=cls.get_category_reasons(),
            statuses=SecurityResetApplication.StatusType,
            balance_choices=cls.BalanceChoice,
            liveness_statuses=LivenessCheckHistory.Status,
        )

        # 近期提交次数
        started_at = application.created_at - timedelta(hours=48)
        recent_submit_count = SecurityResetApplication.query.filter(
            SecurityResetApplication.user_id == user_id,
            SecurityResetApplication.reset_type.in_(SecurityResetApplication.ADMIN_SECURITY_RESET_LIST),
            SecurityResetApplication.created_at >= started_at,
            SecurityResetApplication.created_at <= application.created_at,
            or_(SecurityResetApplication.status == SecurityResetApplication.StatusType.CREATED,
                SecurityResetApplication.auditor_id.isnot(None))
        ).with_entities(func.count('*')).scalar() or 0
        reject_count = cls.get_reject_count(application, SecurityResetApplication.ADMIN_SECURITY_RESET_LIST)
        last_reject_reason = cls.get_last_rejected_reason(
            application.user_id, application.id, SecurityResetApplication.ADMIN_SECURITY_RESET_LIST
        ) if reject_count > 0 else '/'

        price_map = PriceManager.assets_to_usd()
        current_balance_usd = BalanceManager(user_id, [], price_map).get_current_balance_usd()
        author_name_map = get_admin_user_name_map([application.auditor_id, application.checker_id])
        kyc_first_submit_at = kyc_first_pass_at = None
        kyc_submit_on_pass_record = KycVerification.query.filter(
            KycVerification.user_id == user_id,
            KycVerification.submit_type == KycVerification.SubmitType.SUBMIT_ON_PASS
        ).order_by(KycVerification.id.asc()).first()
        if r := kyc_submit_on_pass_record:
            last_pass_record = KycVerification.query.filter(
                KycVerification.id < r.id,
                KycVerification.user_id == user_id,
            ).order_by(KycVerification.id.desc()).first()
            if r := last_pass_record:
                kyc_first_submit_at = r.created_at
                kyc_first_pass_at = r.audited_at or r.created_at

        return dict(
            application_id=application.id,
            created_at=application.created_at,
            audited_at=application.audited_at,
            kyc_id=kyc.id if kyc else None,
            kyc_created_at=kyc.created_at if kyc else None,
            kyc_updated_at=kyc.updated_at if kyc else None,
            kyc_last_passed_at=kyc.last_passed_at if kyc else None,
            kyc_first_submit_at=kyc_first_submit_at,
            kyc_first_pass_at=kyc_first_pass_at,
            user_id=user.id,
            auditor_id=application.auditor_id,
            author_email=None if not application.auditor_id else User.query.get(application.auditor_id).email,
            author_name=author_name_map.get(application.auditor_id),
            checker_id=application.checker_id,
            checker_email=None if not application.checker_id else User.query.get(application.checker_id).email,
            checker_name=author_name_map.get(application.checker_id),
            checked_at=application.checked_at,
            email=user.email,
            mobile=user.mobile,
            balance=amount_to_str(application.balance_usd, 2),
            current_balance=amount_to_str(current_balance_usd, 2),
            reason_str=application.get_reject_reason(),
            totp_status=bool(user.totp_auth_key),
            kyc_status=user.kyc_status.name,
            country=user.kyc_country,
            id_type=kyc.id_type.name if kyc else None,
            name=kyc.full_name if kyc else None,
            id_num=kyc.id_number if kyc else None,
            enums=enums,
            required_file_types=required_file_types,
            reset_type=gettext(application.reset_type.value),
            flow=application.flow.value,
            data_type=application.data_type,
            ip=application.ip,
            platform=application.platform,
            recent_submit_count=recent_submit_count,
            status=application.status.name,
            is_custom_reason=application.is_custom_reason,
            remark=application.remark,
            jump_url=application.jump_url,
            user_remark=get_user_remark(user),
            reject_count=reject_count or '/',
            last_reject_reason=last_reject_reason,
            liveness_id=liveness.id if liveness else None,
            liveness_transaction_id=liveness.transaction_id if liveness else None,
            liveness_status=liveness.status.name if liveness else '',
            liveness_reject_reason=liveness.get_reject_reason(translate=False) if liveness else '',
            user_info=dict(
                id=user.id,
                created_at=user.created_at,
                registration_ip=user.registration_ip,
                registration_location=user.registration_location,
                email=user.email,
                new_email=application.new_email,
                mobile=user.mobile,
                name=user.name,
                user_type=user.user_type,
                kyc_status=user.kyc_status
            ),
            **file_map
        )

    @classmethod
    def get_next(cls, user_id, application_id, kwargs):
        ids = SecurityResetApplicationAssignAuditorsCache().get_auditor_data(user_id)
        kyc_status = kwargs.get('kyc_status')
        balance_choice = kwargs.get('balance_choice')
        if kyc_status or balance_choice:
            query = SecurityResetApplication.query.filter(SecurityResetApplication.id.in_(ids))
            if kyc_status := kwargs.get('kyc_status'):
                query = query.join(
                    User,
                    SecurityResetApplication.user_id == User.id,
                    isouter=True,
                ).filter(
                    User.kyc_status == kyc_status
                )
            if balance_choice := kwargs.get('balance_choice'):
                if balance_choice is cls.BalanceChoice.GTE_1W:
                    query = query.filter(
                        SecurityResetApplication.balance_usd >= Decimal('10000')
                    )
                elif balance_choice is cls.BalanceChoice.LT_1W:
                    query = query.filter(
                        SecurityResetApplication.balance_usd < Decimal('10000')
                    )
                elif balance_choice is cls.BalanceChoice.GTE_200:
                    query = query.filter(
                        SecurityResetApplication.balance_usd >= Decimal('200')
                    )
                elif balance_choice is cls.BalanceChoice.LT_200:
                    query = query.filter(
                        SecurityResetApplication.balance_usd < Decimal('200')
                    )
                elif balance_choice is cls.BalanceChoice.LT_1:
                    query = query.filter(
                        SecurityResetApplication.balance_usd < Decimal('1')
                    )
                elif balance_choice is cls.BalanceChoice.EQ_0:
                    query = query.filter(
                        SecurityResetApplication.balance_usd < Decimal('0.01')
                    )
            rows = query.with_entities(
                SecurityResetApplication.id
            ).order_by(
                SecurityResetApplication.id
            ).all()
            ids = [i.id for i in rows]
        ids.sort()
        if application_id in set(ids):
            next_index = (ids.index(application_id) + 1) % len(ids)
        else:
            next_index = 0
        row = SecurityResetApplication.query.filter(
            SecurityResetApplication.id.in_(ids[next_index: next_index + 1])
        ).first()
        if not row:
            raise InvalidArgument(message='没有下一个')
        return row


@ns.route('/2fa-reset/answer-history')
@respond_with_code
class Reset2FAQuestionHistoryResource(Resource):
    reset_type_map = dict(
        TOTP="重置TOTP",
        MOBILE="重置手机",
        EMAIL="重置邮箱",
        WEBAUTHN="重置通行密钥",
    )

    reset_status_map = dict(
        SUCCEEDED="成功",
        FAILED="失败"
    )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(SecurityResetAnswerHistory.Status),
        kyc_status=EnumField(User.KYCStatus),
        type=EnumField(SecurityResetApplication.ResetType),
        start_date=TimestampField(is_ms=True),
        end_date=TimestampField(is_ms=True),
        keyword=fields.String,
        page=PageField(missing=1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """
        运营-2FA重置管理-自动审核记录
        """
        page, limit = kwargs['page'], kwargs['limit']
        query = SecurityResetAnswerHistory.query.order_by(SecurityResetAnswerHistory.id.desc())
        if status := kwargs.get('status'):
            query = query.filter(
                SecurityResetAnswerHistory.status == status
            )
        if kyc_status := kwargs.get('kyc_status'):
            query = query.join(
                User,
                SecurityResetAnswerHistory.user_id == User.id,
                isouter=True,
            ).filter(
                User.kyc_status == kyc_status
            )
        if type_ := kwargs.get('type'):
            query = query.filter(
                SecurityResetAnswerHistory.reset_type == type_
            )
        if start_date := kwargs.get('start_date'):
            query = query.filter(
                SecurityResetAnswerHistory.created_at >= start_date
            )
        if end_date := kwargs.get('end_date'):
            query = query.filter(
                SecurityResetAnswerHistory.created_at < end_date
            )
        remaining_count = '-'  # 剩余答题次数，只有查询单个用户时才有意义
        if keyword := kwargs.get('keyword', '').strip():
            keyword_results = User.search_for_users(keyword)
            query = query.filter(SecurityResetAnswerHistory.user_id.in_(keyword_results))
            if len(keyword_results) == 1:
                failure_attempt_count = SecurityQuestionBusiness.get_failure_attempt_count(keyword_results[0])
                remaining_count = SecurityQuestionBusiness.MAX_ALLOWED_ATTEMPTS - failure_attempt_count
        records = query.paginate(page, limit, error_out=False)
        total_user_count, total_count = query.with_entities(
            func.count(SecurityResetAnswerHistory.user_id.distinct()),
            func.count()
        ).first()
        success_count = query.filter(
            SecurityResetAnswerHistory.status == SecurityResetAnswerHistory.Status.SUCCEEDED
        ).with_entities(
            func.count()
        ).scalar() or 0
        items = records.items
        user_ids = [item.user_id for item in items]
        user_info = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(User.id, User.email, User.kyc_status).all()

        kyc_rows = KycVerification.query.filter(
            KycVerification.user_id.in_(user_ids),
            KycVerification.status == KycVerification.Status.PASSED
        ).with_entities(KycVerification.user_id, KycVerification.id).all()
        kyc_id_map = {row.user_id: row.id for row in kyc_rows}

        user_info_map = {
            user.id: {
                "email": user.email,
                "kyc_status": user.kyc_status.name,
            }
            for user in user_info
        }

        res = []
        for item in items:
            item: SecurityResetAnswerHistory
            res.append(
                dict(
                    id=item.id,
                    created_at=item.created_at,
                    user_id=item.user_id,
                    email=user_info_map.get(item.user_id, {}).get('email'),
                    reset_type=cls.reset_type_map[item.reset_type.name],
                    status=cls.reset_status_map[item.status.name],
                    kyc_status=user_info_map.get(item.user_id, {}).get('kyc_status'),
                    kyc_id=kyc_id_map.get(item.user_id),
                )
            )
        return dict(
            statuses=cls.reset_status_map,
            types=cls.reset_type_map,
            kyc_statuses={
                User.KYCStatus.PASSED.name: "已实名",
                User.KYCStatus.NONE.name: "未实名",
                User.KYCStatus.FAILED.name: "失败",
                User.KYCStatus.PROCESSING.name: "处理中"
            },
            items=res,
            total=records.total,
            statistics=dict(
                success_count=success_count,
                fail_count=total_count - success_count,
                user_count=total_user_count,
                answer_count=total_count,
                remaining_count=remaining_count,
            )
        )


@ns.route('/verify-channel')
@respond_with_code
class VerifyChannelResource(Resource):
    class ContentsSchema(Schema):
        val = fields.String(required=True)

        class Meta:
            UNKNOWN = EXCLUDE

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "type", Language.ZH_HANS_CN: "类型"},
        {"field": "name", Language.ZH_HANS_CN: "名称"},
        {"field": "content", Language.ZH_HANS_CN: "内容"},
        {"field": "update_at", Language.ZH_HANS_CN: "最后编辑时间"},
        {"field": "edit_by_email", Language.ZH_HANS_CN: "操作人"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(VerifyChannel.ChannelType),
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        user_id=fields.Integer,
        name=fields.String,
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """
        运营-官方渠道校验-渠道列表
        """
        page, limit = kwargs['page'], kwargs['limit']
        query = VerifyChannel.query.order_by(VerifyChannel.id.desc())
        if type_ := kwargs.get("type"):
            query = query.filter(VerifyChannel.channel_type == type_)
        if user_id:= kwargs.get("user_id"):
            query = query.filter(VerifyChannel.edit_by == user_id)

        if name := kwargs.get("name"):
            query = query.filter(VerifyChannel.name.like(f"%{name}%"))

        if kwargs['export']:
            records = query.all()
            items, total = records, len(records)
        else:
            records = query.paginate(page, limit)
            items, total = records.items, records.total
        res = []
        name_map = get_admin_user_name_map({item.edit_by for item in items})
        for item in items:
            res.append(dict(
                id=item.id,
                type=item.channel_type.name,
                name=item.name,
                content=item.content,
                update_at=item.updated_at,
                edit_by=item.edit_by,
                edit_by_email=name_map.get(item.edit_by)
            ))
        if kwargs['export']:
            for item in res:
                item['update_at'] = item['update_at'].strftime('%Y-%m-%d %H:%M:%S')
                item['type'] = VerifyChannel.ChannelType[item['type']].value

            return export_xlsx(
                filename='user_report',
                data_list=res,
                export_headers=cls.export_headers
            )
        return dict(
            total=total,
            items=res,
            types={item.name: gettext(item.value) for item in VerifyChannel.ChannelType},
        )

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(VerifyChannel.ChannelType, required=True),
        name=fields.String(required=True),
        contents=fields.Nested(ContentsSchema, many=True, required=True)
    ))
    def post(cls, **kwargs):
        """
        运营-官方渠道校验-新增渠道
        """
        contents = kwargs['contents']
        channel_type = kwargs['type']
        for item in contents:
            content = item['val']
            if channel_type == VerifyChannel.ChannelType.DOMAIN:
                content = content.lower()
            elif channel_type == VerifyChannel.ChannelType.EMAIL and not validate_email(content):
                raise InvalidArgument(message="邮箱格式错误")
            channel = VerifyChannel(
                name=kwargs["name"],
                channel_type=channel_type,
                content=content.strip(),
                edit_by=g.user.id
            )
            db.session.add(channel)
            db.session.commit()

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.VerifyChannel,
                detail=channel.to_dict(enum_to_name=True),
            )


@ns.route('/verify-channel/batch-upload')
@respond_with_code
class VerifyChannelBatchUploadResource(Resource):

    @classmethod
    def post(cls):
        """
        运营-官方渠道校验-批量上传
        """
        file_ = request.files.get('file')
        file_columns = ["channel_type", "name", "content"]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)

        for item in rows:
            content = item['content']
            channel_type = item['channel_type']
            name = item['name']
            if not content or not channel_type or not name:
                raise InvalidArgument(message="类型、名称、内容为必填项")
            if channel_type not in (t.value for t in VerifyChannel.ChannelType):
                raise InvalidArgument(message=f"{channel_type} 不是合法的类型")
            channel_type = VerifyChannel.ChannelType(channel_type)
            content = str(content)
            if channel_type == VerifyChannel.ChannelType.DOMAIN:
                content = content.lower()
            elif channel_type == VerifyChannel.ChannelType.EMAIL and not validate_email(content):
                raise InvalidArgument(message="邮箱格式错误")
            channel = VerifyChannel(
                name=name,
                channel_type=channel_type,
                content=content.strip(),
                edit_by=g.user.id
            )
            db.session.add(channel)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.VerifyChannel,
                detail=dict(name=name, channel_type=channel_type, content=content.strip()),
            )
        db.session.commit()
        return dict(
            total=len(rows)
        )


@ns.route("/verify-channel/<int:id_>")
@respond_with_code
class VerifyChannelDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(VerifyChannel.ChannelType, required=True),
        name=fields.String(required=True),
        content=fields.String(required=True)
    ))
    def put(cls, id_, **kwargs):
        """
        运营-官方渠道校验-修改渠道
        """
        channel: VerifyChannel = VerifyChannel.query.get(id_)
        if not channel:
            raise InvalidArgument
        old_data = channel.to_dict(enum_to_name=True)
        channel_type = kwargs["type"]
        content = kwargs['content']
        if channel_type == VerifyChannel.ChannelType.DOMAIN:
            content = content.lower()
        if channel_type == VerifyChannel.ChannelType.EMAIL and not validate_email(content):
            raise InvalidArgument(message="邮箱格式错误")
        channel.channel_type = channel_type
        channel.name = kwargs["name"]
        channel.content = content.strip()
        channel.updated_at = now()
        channel.edit_by = g.user.id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.VerifyChannel,
            old_data=old_data,
            new_data=channel.to_dict(enum_to_name=True),
        )

    @classmethod
    def delete(cls, id_):
        """
        运营-官方渠道校验-删除渠道
        """
        channel: VerifyChannel = VerifyChannel.query.get(id_)
        if not channel:
            raise InvalidArgument
        db.session.delete(channel)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.VerifyChannel,
            detail=dict(id=id_),
        )


@ns.route('/zendesk-article-update-record')
class ZendeskArticleUpdateRecordResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        last_id=fields.String(),
        first_id=fields.String(),
        limit=LimitField(missing=100),
    ))
    def get(cls, **kwargs):
        """
        运营-查看Zendesk手动拉取记录
        """
        # 仅查看zendesk的记录，与操作记录分开权限
        kwargs['namespace'] = '运营'
        kwargs['object'] = 'Zendesk文章拉取更新'
        return AdminOperationLogResource.get(**kwargs)



@ns.route('/zendesk-article-update')
@respond_with_code
class ZendeskArticleUpdateResource(Resource):

    @classmethod
    def get(cls):
        """
        运营-查看Zendesk手动拉取记录最近更新时间
        """
        cache_data = ZendeskArticleCache().hgetall()

        timestamp_ = cache_data.get('timestamp')

        return dict(timestamp=int(timestamp_) if timestamp_ else None)

    @classmethod
    def post(cls):
        """
        运营-手动拉取更新Zendesk文章
        """
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Zendesk_Article_Update,
            detail=dict(msg='提交Zendesk文章更新任务'),
        )
        update_zendesk_article_schedule.delay()


@ns.route('/security-tools-history')
@respond_with_code
class SecurityHistoryToolHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            search_keyword=fields.String,
            history_email_or_mobile=fields.String,
            change_type=EnumField(SecurityToolHistory.OpType),
            start_date=fields.Date,
            end_date=fields.Date,
            page=PageField(unlimited=True, missing=1),
            limit=LimitField(missing=50),
            op_role=EnumField(SecurityToolHistory.OpRole),
        )
    )
    def get(cls, **kwargs):
        """
        安全工具变更记录
        """
        params = Struct(**kwargs)
        query = cls.get_query_by(params)
        pages = query.paginate(params.page, params.limit, error_out=False)
        admin_user_ids = {record.admin_user_id for record in pages.items if record.admin_user_id}
        user_ids = {record.user_id for record in pages.items}
        name_map = get_admin_user_name_map(list(admin_user_ids | user_ids))
        results = []
        for record in pages.items:
            op_role = record.op_role
            if op_role == SecurityToolHistory.OpRole.ADMIN.value and record.admin_user_id:
                operator = name_map.get(record.admin_user_id, '/')
            else:
                operator = '/'
            results.append(
                dict(
                    created_at=record.created_at,
                    user_id=record.user_id,
                    email=record.account,
                    withdrawal_approver_email=record.withdrawal_approver_email or '--',
                    email_or_mobile_before=record.account_before or '--',
                    change_type=record.op_type,
                    operator=operator,
                    op_role=op_role,
                    admin_user_id=record.admin_user_id,
                )
            )
        return dict(
            items=results,
            total=pages.total,
            change_types={op_type.name: op_type.value for op_type in SecurityToolHistory.OpType},
            op_roles={i.name: i.value for i in SecurityToolHistory.OpRole}
        )

    @classmethod
    def get_query_by(cls, params):
        query = SecurityToolHistory.query.order_by(SecurityToolHistory.id.desc())
        if params.change_type:
            query = query.filter(SecurityToolHistory.op_type == params.change_type.value)
        if params.start_date:
            query = query.filter(
                SecurityToolHistory.created_at >= params.start_date
            )
        if params.end_date:
            query = query.filter(
                SecurityToolHistory.created_at <= params.end_date + timedelta(days=1)
            )
        if params.history_email_or_mobile:
            history_email_or_mobile = params.history_email_or_mobile.strip()
            query = query.filter(SecurityToolHistory.account_before == history_email_or_mobile)

        if params.search_keyword:
            search_keyword = params.search_keyword.strip()
            user_ids = User.search_for_users(search_keyword)
            query = query.filter(SecurityToolHistory.user_id.in_(user_ids))

        if op_role := params.op_role:
            query = query.filter(SecurityToolHistory.op_role == op_role.value)

        return query

    @classmethod
    def fetch_user2email_by(cls, user_ids) -> dict:
        return fetch_user2email_by(user_ids)


@ns.route('/sp-config-change-log')
@respond_with_code
class UserSpConfigChangeLogResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            search_keyword=fields.String,
            config_type=EnumField(UserSpecialConfigChangeLog.SpecialConfigType),
            op_type=EnumField(UserSpecialConfigChangeLog.OpType),
            start_date=TimestampField(is_ms=True),
            end_date=TimestampField(is_ms=True),
            page=PageField(unlimited=True, missing=1),
            limit=LimitField(missing=50),
        )
    )
    def get(cls, **kwargs):
        """
        用户特殊配置-变更记录
        """
        params = Struct(**kwargs)
        query = cls.get_query_by(params)
        pages = query.paginate(params.page, params.limit, error_out=False)
        user_ids = set()
        for record in pages.items:
            user_ids.add(record.user_id)
            user_ids.add(record.admin_user_id)
        user2email = cls.fetch_user2email_by(user_ids)
        name_map = get_admin_user_name_map(user_ids)
        results = []
        for record in pages.items:
            results.append(
                dict(
                    created_at=record.created_at,
                    user_id=record.user_id,
                    change_detail=record.change_detail,
                    change_remark=record.change_remark or '--',
                    email=user2email.get(record.user_id) or '--',
                    config_type=record.config_type,
                    op_type=record.op_type,
                    operator=name_map.get(record.admin_user_id) or '--',
                    admin_user_id=record.admin_user_id,
                )
            )
        return dict(
            items=results,
            total=pages.total,
            config_types={e.name: e.value for e in UserSpecialConfigChangeLog.SpecialConfigType},
            op_types={op_type.name: op_type.value for op_type in UserSpecialConfigChangeLog.OpType}
        )

    @classmethod
    def get_query_by(cls, params):
        query = UserSpecialConfigChangeLog.query.order_by(UserSpecialConfigChangeLog.id.desc())
        if params.config_type:
            query = query.filter(UserSpecialConfigChangeLog.config_type == params.config_type)
        if params.op_type:
            query = query.filter(UserSpecialConfigChangeLog.op_type == params.op_type)
        if params.start_date:
            query = query.filter(
                UserSpecialConfigChangeLog.created_at >= params.start_date
            )
        if params.end_date:
            query = query.filter(
                UserSpecialConfigChangeLog.created_at <= params.end_date
            )

        if params.search_keyword:
            search_keyword = params.search_keyword.strip()
            user_ids = User.search_for_users(search_keyword)
            query = query.filter(UserSpecialConfigChangeLog.user_id.in_(user_ids))

        return query

    @classmethod
    def fetch_user2email_by(cls, user_ids) -> dict:
        return fetch_user2email_by(user_ids)


def fetch_user2email_by(user_ids) -> dict:
    # Current email.
    users = User.query.filter(
        User.id.in_(user_ids)
    ).with_entities(User.id, User.email).all()
    return {user.id: user.email for user in users}


@ns.route('/portrait-list')
@respond_with_code
class PortraitListResource(Resource):
    """头像列表"""
    model = Portrait
    VALIDITY_DICT = {
        model.Validity.PENDING.value: '待上架',
        model.Validity.ONLINE.value: '上架中',
        model.Validity.OFFLINE.value: '已下架'
    }

    PARAM_VALIDITY = 'validity'
    PARAM_PAGE = 'page'
    PARAM_LIMIT = 'limit'

    @classmethod
    @ns.use_kwargs({
        PARAM_VALIDITY: EnumField([e.value for e in model.Validity], missing=None),
        PARAM_PAGE: fields.Integer(missing=1),
        PARAM_LIMIT: fields.Integer(missing=50)
    })
    def get(cls, **kwargs):
        """运营-App配置-头像配置-头像列表"""
        page: int = kwargs[cls.PARAM_PAGE]
        limit: int = kwargs[cls.PARAM_LIMIT]
        validity: str = kwargs[cls.PARAM_VALIDITY]

        records = cls._query_by_validity(validity, page, limit)
        user_ids = {item.updated_by for item in records.items}
        name_map = get_admin_user_name_map(user_ids)
        items = []
        item: cls.model
        for item in records.items:
            i_dict = item.to_dict(enum_to_name=True)
            for key, url in cls.model.IMAGE_KEY_MAP.items():
                i_dict[url] = ""
                if filed_key := getattr(item, key, ""):
                    i_dict[url] = AWSBucketPublic.get_file_url(filed_key)  # admin前端展示需要这个额外的字段
            i_dict['updated_by_user_email'] = name_map.get(item.updated_by)

            items.append(i_dict)

        return dict(
            total=records.total,
            items=items,
            extra=dict(
                validity_map=cls.VALIDITY_DICT,
                platform_map={i.name: i.value for i in cls.model.Platform}
            )
        )

    @classmethod
    def _query_by_validity(cls, validity: str, page: int, limit: int):
        _now: datetime = now()
        query = cls.model.query.filter(cls.model.status == cls.model.Status.VALID)
        if validity:
            if validity == cls.model.Validity.PENDING.value:
                query = query.filter(cls.model.started_at > _now)
            elif validity == cls.model.Validity.ONLINE.value:
                query = query.filter(cls.model.started_at <= _now,
                                     cls.model.ended_at > _now)
            else:
                query = query.filter(cls.model.ended_at <= _now)

        records = query.order_by(cls.model.started_at.desc()).paginate(page, limit)
        return records


@ns.route('/portrait')
@respond_with_code
class PortraitResource(Resource):
    """单个头像"""
    model = Portrait
    PARAM_ID = 'id'

    params_dict = {
        'name': fields.String(required=True),
        'started_at': TimestampField(required=True),
        'ended_at': TimestampField(required=True),
        'file_key': fields.String(required=True),
        'night_key': fields.String(required=True),
        'day_border_key': fields.String(missing="", allow_none=True),
        'night_border_key': fields.String(missing="", allow_none=True),
        'platform': EnumField(model.Platform, required=True)
    }

    @classmethod
    @ns.use_kwargs({
        PARAM_ID: fields.Integer(required=True)
    })
    def get(cls, **kwargs):
        id_: int = kwargs.get(cls.PARAM_ID)
        portrait = cls.model.query.get(id_)
        if portrait is None:
            raise RecordNotFound
        return portrait

    @classmethod
    @ns.use_kwargs({
        PARAM_ID: fields.Integer(required=True)
    })
    def delete(cls, **kwargs):
        """运营-App配置-头像配置-删除头像"""
        id_: int = kwargs.get(cls.PARAM_ID)

        portrait = cls.model.query.get(id_)
        if portrait is None:
            raise RecordNotFound
        portrait.status = cls.model.Status.DELETED

        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Portrait,
            detail=dict(id=id_, name=portrait.name),
        )

    @classmethod
    @ns.use_kwargs({
        PARAM_ID: fields.Integer(required=True),
        **params_dict
    })
    def put(cls, **kwargs):
        """运营-App配置-头像配置-编辑头像"""
        params = Struct(**kwargs)
        portrait = cls.model.query.get(params.id)
        if portrait is None:
            raise RecordNotFound
        old_data = portrait.to_dict(enum_to_name=True)

        cls._check_validity(params.started_at, params.ended_at, params.platform, params.id)

        portrait.updated_by = g.user.id
        for field in cls.params_dict.keys():
            setattr(portrait, field, kwargs.get(field))

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Portrait,
            old_data=old_data,
            new_data=portrait.to_dict(enum_to_name=True),
        )

        return db.session.commit()

    @classmethod
    @ns.use_kwargs(params_dict)
    def post(cls, **kwargs):
        """运营-App配置-头像配置-创建头像"""
        params = Struct(**kwargs)
        cls._check_validity(params.started_at, params.ended_at, params.platform)

        portrait = Portrait(
            **kwargs,
            updated_by=g.user.id,
        )

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Portrait,
            detail=kwargs,
        )
        return db.session_add_and_commit(portrait)

    @classmethod
    def _check_validity(cls, start: datetime, end: datetime, platform, id_: int = None):
        now_: datetime = now()
        if start < now_:
            raise InvalidArgument(message='开始时间不能早于当前时间')

        if start >= end:
            raise InvalidArgument(message='开始时间不能晚于结束时间')

        # T检查开始结束时间是否与已有时间重叠
        query = cls.model.query.filter(cls.model.status == cls.model.Status.VALID)
        query = query.filter(
            cls.model.started_at <= end,
            cls.model.ended_at >= start,
        )
        if platform != cls.model.Platform.ALL:
            query = query.filter(
                cls.model.platform.in_(
                    [cls.model.Platform.ALL, platform]
                )
            )
        if id_ is not None:
            query = query.filter(cls.model.id != id_)
        count = query.with_entities(func.count()).scalar()
        if count > 0:
            raise InvalidArgument(message='相同平台开始结束时间不可与已有记录时间重叠')


class NotificationBarMixin:

    @classmethod
    def validate(cls, params):
        model = cls.model

        if params.status not in (model.Status.DRAFT, model.Status.CREATED):
            raise OperationDenied(message=f'invalid status {params.status!r}')

        mapping = cls._get_platform_to_trigger_pages()
        trigger_pages = mapping.get(params.platform) or ()
        trigger_pages = [page.name for page in trigger_pages]
        for trigger_page_map in params.trigger_pages:
            if trigger_page_map.get('trigger_page') not in trigger_pages:
                raise InvalidArgument(message=f'trigger_page type error')

        if params.begin_at > params.end_at:
            raise InvalidArgument

        if params.begin_at < now():
            raise InvalidArgument(message='消息开始时间不得早于当前时间')

        # Do not check params startswith jump_

    @classmethod
    def _get_platform_to_trigger_pages(cls):
        model = cls.model
        return {
            model.Platform.APP: (
                model.TriggerPage.QUOTES,
                model.TriggerPage.ASSET_DATA,
                model.TriggerPage.SPOT_MARKET,
                model.TriggerPage.PERPETUAL_MARKET,
                model.TriggerPage.ACCOUNT_ASSET,
            ),
            model.Platform.WEB: (
                model.TriggerPage.QUOTES,
                model.TriggerPage.FIAT,
                model.TriggerPage.ASSET_DATA,
                model.TriggerPage.SPOT_MARKET,
                model.TriggerPage.PERPETUAL_MARKET,
                model.TriggerPage.ACCOUNT_ASSET,
            ),
            model.Platform.ALL: (
                model.TriggerPage.QUOTES,
                model.TriggerPage.ASSET_DATA,
                model.TriggerPage.SPOT_MARKET,
                model.TriggerPage.PERPETUAL_MARKET,
            ),
        }

    @classmethod
    def trim_trigger_pages(cls, trigger_pages: list):
        pages = []
        model = cls.model
        for trigger_page_map in trigger_pages:
            trigger_page = trigger_page_map.get('trigger_page')
            trigger_page_params = trigger_page_map.get('trigger_page_params') or ''
            if trigger_page not in (
                    model.TriggerPage.ASSET_DATA.name,
                    model.TriggerPage.SPOT_MARKET.name,
                    model.TriggerPage.PERPETUAL_MARKET.name,
            ):
                trigger_page_params = ''
            pages.append({
                'trigger_page': trigger_page,
                'trigger_page_params': trigger_page_params,
            })

        pages = pages or ''
        if pages:
            pages = json.dumps(pages)
        return pages


@ns.route('/notification-bars')
@respond_with_code
class NotificationBars(NotificationBarMixin, Resource):
    """运营-触达管理-通知栏列表"""

    model = NotificationBar

    class DisplayStatus(Enum):
        DRAFT = '未提交'
        CREATED = '待审核'
        REJECTED = '未通过'
        PENDING = '待通知'
        RUNNING = '通知中'
        FINISHED = '已结束'
        FAILED = '推送失败'

    deserialize_status_mapping = {
        DisplayStatus.DRAFT: model.Status.DRAFT,
        DisplayStatus.CREATED: model.Status.CREATED,
        DisplayStatus.REJECTED: model.Status.REJECTED,
        DisplayStatus.PENDING: model.Status.AUDITED,
        DisplayStatus.RUNNING: model.Status.AUDITED,
        DisplayStatus.FINISHED: model.Status.FINISHED,
        DisplayStatus.FAILED: model.Status.FAILED,
    }
    serialize_status_mapping = {
        model.Status.DRAFT: DisplayStatus.DRAFT,
        model.Status.CREATED: DisplayStatus.CREATED,
        model.Status.REJECTED: DisplayStatus.REJECTED,
        model.Status.AUDITED: DisplayStatus.PENDING,
        model.Status.FINISHED: DisplayStatus.FINISHED,
        model.Status.FAILED: DisplayStatus.FAILED,
    }

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String,
        platform=EnumField(model.Platform),
        trigger_page=EnumField(model.TriggerPage),
        status=EnumField(DisplayStatus),
        start_time=TimestampField(),
        end_time=TimestampField(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """触达管理-通知栏-列表"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)

        records = query.paginate(params.page, params.limit)
        items = records.items
        create_user_ids = {item.created_by for item in items}
        name_map = get_admin_user_name_map(create_user_ids)

        now_ = now()
        model = cls.model
        bars = []
        for row in items:
            row_dict = row.to_dict(enum_to_name=True)
            row_dict['trigger_pages'] = row.get_trigger_pages()
            row_dict['created_user_email'] = name_map.get(row.created_by) or '-'
            row_dict['status'] = row.status.value

            status = cls.serialize_status_mapping[row.status]
            row_dict['status'] = status.name
            can_del, can_stop = True, False
            if row.status is model.Status.FINISHED:
                can_del = False
            elif row.status is model.Status.AUDITED:
                if row.begin_at <= now_:
                    row_dict['status'] = cls.DisplayStatus.RUNNING.name
                    can_stop = True
                else:
                    row_dict['status'] = cls.DisplayStatus.PENDING.name

            row_dict['can_del'] = can_del
            row_dict['can_stop'] = can_stop
            bars.append(row_dict)

        return dict(
            total=records.total,
            items=bars,
            extra=dict(
                platforms=model.Platform,
                trigger_pages=model.TriggerPage,
                statuses=cls.DisplayStatus,
            )
        )

    @classmethod
    def get_query_by(cls, params):
        model = cls.model
        query = model.query.filter(
            model.status != model.Status.DELETED
        ).order_by(model.id.desc())

        if name := params.name:
            query = query.filter(model.name.contains(name))
        if platform := params.platform:
            query = query.filter(model.platform == platform)
        if trigger_page := params.trigger_page:
            query = query.filter(model.trigger_pages.contains(trigger_page.name))
        if status := params.status:
            status = cls.deserialize_status_mapping.get(status)
            if status:
                query = query.filter(model.status == status)
                if params.status == cls.DisplayStatus.PENDING:
                    query = query.filter(model.begin_at > now())
                elif params.status == cls.DisplayStatus.RUNNING:
                    query = query.filter(model.begin_at < now())
        if params.start_time:
            query = query.filter(model.begin_at >= params.start_time)
        if params.end_time:
            query = query.filter(model.end_at <= params.end_time)

        return query

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True, validate=lambda s: len(s) <= 256),
        platform=EnumField(model.Platform, required=True),
        trigger_pages=fields.List(fields.Dict(required=True), required=True),
        begin_at=fields.DateTime(required=True),
        end_at=fields.DateTime(required=True),
        jump_page_enabled=fields.Boolean(missing=False),
        jump_type=EnumField(model.JumpType),
        jump_id=fields.Integer,
        status=EnumField(model.Status),
        remark=fields.String,
    ))
    def post(cls, **kwargs):
        """运营-触达管理-通知栏-新建"""
        params = Struct(**kwargs)
        cls.validate(params)

        trigger_pages = cls.trim_trigger_pages(params.trigger_pages)

        model = cls.model
        row = db.session_add_and_commit(model(
            name=params.name,
            platform=params.platform,
            trigger_pages=trigger_pages,
            jump_page_enabled=params.jump_page_enabled,
            jump_type=params.jump_type,
            jump_id=params.jump_id,
            begin_at=params.begin_at,
            end_at=params.end_at,
            remark=params.remark or '',
            status=params.status,
            created_by=g.user.id,
        ))
        row_dict = row.to_dict(enum_to_name=True)
        row_dict['trigger_pages'] = row.get_trigger_pages()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NotificationBar,
            detail=kwargs,
        )
        return row_dict


@ns.route('/notification-bar/<int:id_>')
@respond_with_code
class NotificationBarResource(NotificationBarMixin, Resource):
    model = NotificationBar

    @classmethod
    def get(cls, id_):
        """运营-通知栏-详情"""
        lang_names = language_cn_names()
        extra = dict(
            platforms=cls.model.Platform,
            trigger_pages=cls.model.TriggerPage,
            jump_types=cls.model.JumpType,
            statuses=cls.model.Status,
            spot_markets=MarketCache.list_online_markets(),
            perpetual_markets=PerpetualMarketCache().get_market_list(),
            assets=list_all_assets(),
            languages={e.name: lang_names[e] for e in EmailPushContent.AVAILABLE_LANGS}
        )
        if not id_:
            return dict(
                extra=extra
            )
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        res = row.to_dict(enum_to_name=True)

        user_ids = {row.created_by}
        if row.audited_by:
            user_ids.add(row.audited_by)
        name_map = get_admin_user_name_map(user_ids)
        created_user_email = name_map.get(row.created_by) or '-'
        audited_user_email = name_map.get(row.audited_by) or '-'
        res['created_user_email'] = created_user_email
        res['audited_user_email'] = audited_user_email
        res['trigger_pages'] = row.get_trigger_pages()
        res['extra'] = extra
        return res

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True, validate=lambda s: len(s) <= 256),
        platform=EnumField(model.Platform, required=True),
        trigger_pages=fields.List(fields.Dict(required=True), required=True),
        begin_at=fields.DateTime(required=True),
        end_at=fields.DateTime(required=True),
        jump_page_enabled=fields.Boolean(missing=False),
        jump_type=EnumField(model.JumpType),
        jump_id=fields.Integer,
        status=EnumField(model.Status),
        remark=fields.String,
    ))
    def put(cls, id_, **kwargs):
        """运营-触达管理-通知栏-编辑"""
        row = cls.model.query.get(id_)
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)

        params = Struct(**kwargs)
        if params.status not in (cls.model.Status.DRAFT, cls.model.Status.CREATED):
            raise OperationDenied(message=f'invalid status {params.status!r}')

        if row.status is cls.model.Status.FINISHED:
            raise OperationDenied(message='cannot edit a finished item')

        if row.status is cls.model.Status.AUDITED:
            if row.begin_at <= now():
                raise OperationDenied(message='cannot edit a running item')

        cls.validate(params)

        trigger_pages = cls.trim_trigger_pages(params.trigger_pages)

        row.name = params.name
        row.platform = params.platform
        row.trigger_pages = trigger_pages
        row.begin_at = params.begin_at
        row.end_at = params.end_at
        row.jump_page_enabled = params.jump_page_enabled
        row.jump_type = params.jump_type
        row.jump_id = params.jump_id
        row.status = params.status
        row.remark = params.remark or ''
        db.session.commit()

        row_dict = row.to_dict(enum_to_name=True)
        row_dict['trigger_pages'] = row.get_trigger_pages()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NotificationBar,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return row_dict

    @classmethod
    def delete(cls, id_):
        """运营-触达管理-通知栏-删除"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        if row.status is cls.model.Status.FINISHED:
            raise OperationDenied(message='cannot delete a finished item')

        row.status = cls.model.Status.DELETED
        row.updated_at = now()
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NotificationBar,
            detail=dict(id=id_, name=row.name),
        )
        return {}


@ns.route('/notification-bar/<int:id_>/created')
@respond_with_code
class NotificationBarCreated(Resource):
    model = NotificationBar

    @classmethod
    def patch(cls, id_):
        """运营-触达管理-通知栏-提交审核"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        if row.status is not cls.model.Status.DRAFT:
            raise OperationDenied(message='only draft status item can be edit')

        if NotificationBarContent.query.filter(
                NotificationBarContent.notification_bar_id == id_
        ).count() == 0:
            raise OperationDenied(message='触达内容未配置,不可提交审核')
        row.status = cls.model.Status.CREATED
        db.session.commit()
        row.trigger_pages = row.get_trigger_pages()

        return row


@ns.route('/notification-bar/<int:id_>/audit')
@respond_with_code
class NotificationBarAudit(Resource):
    model = NotificationBar

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(model.Status, required=True),
        auditor_remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-触达管理-通知栏-审核"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        params = Struct(**kwargs)
        if params.status not in (cls.model.Status.AUDITED, cls.model.Status.REJECTED):
            raise OperationDenied(message=f'invalid status {params.status!r}')

        if row.status is not cls.model.Status.CREATED:
            raise OperationDenied(message='only created status item can be audit')

        user_id: int = g.user.id
        row.status = params.status
        row.audited_by = user_id
        row.audited_at = now()
        row.auditor_remark = params.auditor_remark or ''

        if row.audited_at > row.begin_at:
            row.status = cls.model.Status.FAILED
            row.remark = "audit time gt than the record's begin at"
        db.session.commit()

        row_dict = row.to_dict(enum_to_name=True)
        if row.status is cls.model.Status.AUDITED:
            if row.begin_at <= now():
                row_dict['status'] = 'RUNNING'
            else:
                row_dict['status'] = 'PENDING'

        row_dict['trigger_pages'] = row.get_trigger_pages()

        kwargs['id'] = id_
        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NotificationBar,
            detail=kwargs,
        )
        return row_dict


@ns.route('/notification-bar/<int:id_>/offline')
@respond_with_code
class NotificationBarOffline(Resource):
    model = NotificationBar

    @classmethod
    def patch(cls, id_):
        """运营-触达管理-通知栏-通知中提前结束"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        if row.status is not cls.model.Status.AUDITED:
            raise OperationDenied(message='only running status item can be stopped')
        now_ = now()
        if row.begin_at > now_:
            raise OperationDenied(message='only running status item can be stopped')

        row.status = cls.model.Status.FINISHED
        row.end_at = now_
        db.session.commit()

        row_dict = row.to_dict(enum_to_name=True)
        row_dict['trigger_pages'] = row.get_trigger_pages()

        AdminOperationLog.new_stop(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NotificationBar,
            detail=dict(id=id_, name=row.name),
        )
        return row_dict


@ns.route('/notification-bar/duplicates')
@respond_with_code
class NotificationBarDuplicates(Resource):
    model = NotificationBar

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer,
        platform=EnumField(model.Platform, required=True),
        trigger_pages=fields.String(required=True),
        begin_at=fields.DateTime(required=True),
        end_at=fields.DateTime(required=True),
    ))
    def get(cls, **kwargs):
        """运营-触达管理-通知栏-时间范围重复数量"""
        id_ = kwargs.get('id')
        begin_at = kwargs['begin_at']
        end_at = kwargs['end_at']
        platform = kwargs['platform']
        trigger_pages = json.loads(kwargs['trigger_pages'])

        ret = {}
        for trigger_page_map in trigger_pages:
            trigger_page = trigger_page_map['trigger_page']
            query = cls.model.query.filter(
                cls.model.trigger_pages.contains(trigger_page),
                cls.model.status.in_(
                    [
                        cls.model.Status.DRAFT,
                        cls.model.Status.CREATED,
                        cls.model.Status.AUDITED,
                    ]
                ),
                cls.model.begin_at < end_at,
                cls.model.end_at > begin_at,
            )
            if id_:
                query = query.filter(cls.model.id != id_)

            if platform == cls.model.Platform.APP:
                platforms = (cls.model.Platform.APP, cls.model.Platform.ALL)
                duplicates_count = query.filter(cls.model.platform.in_(platforms)).count()
            elif platform == cls.model.Platform.WEB:
                platforms = (cls.model.Platform.WEB, cls.model.Platform.ALL)
                duplicates_count = query.filter(cls.model.platform.in_(platforms)).count()
            else:
                platforms = (cls.model.Platform.APP, cls.model.Platform.ALL)
                app_duplicates_count = query.filter(cls.model.platform.in_(platforms)).count()
                platforms = (cls.model.Platform.WEB, cls.model.Platform.ALL)
                web_duplicates_count = query.filter(cls.model.platform.in_(platforms)).count()
                duplicates_count = max([app_duplicates_count, web_duplicates_count])

            ret[trigger_page] = duplicates_count
        return ret


@ns.route('/notification-bar/<int:id_>/langs/<lang>')
@respond_with_code
class NotificationBarContentResource(Resource):
    model = NotificationBarContent

    @classmethod
    def get(cls, id_, lang):
        """运营-通知栏-获取通知栏内容"""
        row = cls._get_row(id_, lang)
        if row is None:
            return dict(
                title='',
                content=''
            )
        return dict(
            title=row.title,
            content=row.content,
            summary=row.summary,
        )

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String(required=True),
        summary=fields.String(required=True),
        content=fields.String(required=True)
    ))
    def put(cls, id_, lang, **kwargs):
        """运营-通知栏-编辑内容"""
        title = kwargs['title'] or ''
        summary = kwargs['summary'] or ''
        content = kwargs['content'] or ''
        if title == '' or content == '' or summary == '':
            raise InvalidArgument(message=f"{lang}标题或者摘要或者内容不能为空")

        row = cls._get_row(id_, lang)
        model = cls.model
        if row is None:
            row = model(
                notification_bar_id=id_,
                lang=lang,
                title=title,
                summary=summary,
                content=content
            )
            db.session.add(row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.NotificationBarContent,
                detail=row_to_dict(row, enum_to_name=True),
            )
        else:
            old_data = row_to_dict(row, enum_to_name=True)
            row.title = title
            row.summary = summary
            row.content = content

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.NotificationBarContent,
                old_data=old_data,
                new_data=row_to_dict(row, enum_to_name=True),
                special_data=dict(notification_bar_id=id_, lang=lang),
            )
        db.session.commit()
        return row

    @classmethod
    def _get_row(cls, id_: int, lang: str):
        model = cls.model
        return model.query.filter(
            model.notification_bar_id == id_,
            model.lang == getattr(Language, lang, '')
        ).first()


@ns.route('/templates')
@respond_with_code
class OperationTemplates(Resource):
    model = OperationTemplate

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String,
        enabled=fields.Boolean,
        start_time=TimestampField(),
        end_time=TimestampField(),
        business=EnumField(model.Business, required=True),
        keyword=fields.String,
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营管理-模板列表"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)
        records = query.paginate(params.page, params.limit, error_out=False)
        items = records.items
        create_user_ids = {item.created_by for item in items if item.created_by}
        name_map = get_admin_user_name_map(create_user_ids)
        ret = []
        for row in items:
            r_dict = row_to_dict(row, enum_to_name=True)
            r_dict['created_user_email'] = name_map.get(row.created_by) or '-'

            ret.append(r_dict)
        return dict(
            total=records.total,
            items=ret,
        )

    @classmethod
    def get_query_by(cls, params):
        model = cls.model
        query = model.query.filter(
            model.status == model.Status.VALID,
            model.business == params.business
        )
        if params.title:
            query = query.filter(model.title.contains(params.title))
        if params.enabled is not None:
            query = query.filter(model.enabled == params.enabled)
        if params.start_time:
            query = query.filter(model.created_at >= params.start_time)
        if params.end_time:
            query = query.filter(model.created_at <= params.end_time)
        if params.keyword:
            user_ids = User.search_for_users(params.keyword)
            query = query.filter(model.created_by.in_(user_ids))

        query = query.order_by(model.id.desc())
        return query

    @classmethod
    @ns.use_kwargs(dict(
        business=EnumField(model.Business, required=True),
        title=fields.String(required=True),
        remark=fields.String,
    ))
    def post(cls, **kwargs):
        """运营管理-模板新建"""
        params = Struct(**kwargs)
        obj = cls.model(
            title=params.title,
            business=params.business,
            created_by=g.user.id,
            remark=params.remark or '',
        )
        db.session.add(obj)
        db.session.commit()

        kwargs['id'] = obj.id
        kwargs['business'] = params.business.value
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Template,
            detail=kwargs,
        )
        return obj


@ns.route('/template/<int:id_>/on_off')
@respond_with_code
class OperationTemplateOnOffResource(Resource):
    model = OperationTemplate

    @classmethod
    def put(cls, id_):
        """运营管理-模板启用开关"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)
        row.enabled = not row.enabled
        row.updated_at = now()
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Template,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            special_data=dict(business=row.business.value),
        )
        return row


@ns.route('/template/<int:id_>')
@respond_with_code
class OperationTemplateResource(Resource):
    model = OperationTemplate

    @classmethod
    def get(cls, id_):
        """运营管理-模板详情"""
        lang_names = language_cn_names()
        languages = {e.name: lang_names[e] for e in Language}
        row = cls.model.query.get(id_)
        if not row:
            return dict(extra=dict(languages=languages))

        return dict(
            id=row.id,
            title=row.title,
            remark=row.remark,
            extra=dict(languages=languages)
        )

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String(required=True),
        remark=fields.String,
    ))
    def put(cls, id_, **kwargs):
        """运营管理-模板编辑"""
        row = cls._get_row(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.title = kwargs['title']
        row.remark = kwargs.get('remark') or ''
        row.updated_at = now()
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Template,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            special_data=dict(business=row.business.value),
        )
        return row

    @classmethod
    def delete(cls, id_):
        """运营管理-模板删除"""
        row = cls._get_row(id_)
        row.status = cls.model.Status.DELETED
        row.updated_at = now()
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Template,
            detail=dict(id=id_, business=row.business.value),
        )
        return {}

    @classmethod
    def _get_row(cls, id_):
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        return row


@ns.route('/template-content/<int:id_>/langs/<lang>')
@respond_with_code
class OperationTemplateContentResource(Resource):
    model = OperationTemplateContent

    @classmethod
    def get(cls, id_, lang):
        """运营管理-模板内容"""
        row = cls._get_row(id_, lang)
        if row is None:
            return dict(
                title='',
                content='',
                summary='',
                url='',
            )
        return dict(
            title=row.title,
            content=row.content,
            summary=row.summary,
            url=row.url,
        )

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String(required=True),
        content=fields.String(required=True),
        summary=fields.String(required=False),
        url=fields.String(required=False),
    ))
    def put(cls, id_, lang, **kwargs):
        """运营管理-模板内容编辑"""
        title = kwargs['title'] or ''
        content = kwargs['content'] or ''
        summary = kwargs.get('summary', '')
        url = kwargs.get('url', '')

        row = cls._get_row(id_, lang)
        if row is None:
            row = cls.model(
                template_id=id_,
                lang=lang,
                title=title,
                content=content,
                summary=summary,
                url=url,
            )
            db.session.add(row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.TemplateContent,
                detail=row_to_dict(row, enum_to_name=True),
            )
        else:
            old_data = row_to_dict(row, enum_to_name=True)
            row.title = title
            row.content = content
            row.summary = summary
            row.url = url
            row.updated_at = now()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.TemplateContent,
                old_data=old_data,
                new_data=row_to_dict(row, enum_to_name=True),
                special_data=dict(lang=lang),
            )
        db.session.commit()
        return row

    @classmethod
    def _get_row(cls, id_: int, lang: str):
        model = cls.model
        return model.query.filter(
            model.template_id == id_,
            model.lang == getattr(Language, lang, '')
        ).first()


@ns.route('/videos')
@respond_with_code
class VideosResource(Resource):
    """运营-视频文件列表
        这里包含了用户上传的视频
    """
    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """运营-视频文件列表"""
        params = Struct(**kwargs)
        query = File.query.filter(
            File.mime_type == File.MimeType.VIDEO
        ).order_by(File.id.desc())

        records = query.paginate(params.page, params.limit)
        user_ids = {v.user_id for v in records.items}
        name_map = get_admin_user_name_map(user_ids)
        items = []
        for item in records.items:
            tmp = dict(
                id=item.id,
                created_at=item.created_at,
                name=item.name,
                user_id=item.user_id,
                operator=name_map.get(item.user_id, ""),
                size=item.size,
                url=item.static_url,
            )
            items.append(tmp)

        return dict(
            total=records.total,
            items=items,
        )


@ns.route('/media/videos')
@respond_with_code
class MediaVideoResource(Resource):
    """运营-视频文件上传管理列表"""
    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """运营-视频文件列表"""
        params = Struct(**kwargs)
        query = Video.query.order_by(File.id.desc())

        records = query.paginate(params.page, params.limit)
        user_ids = {v.user_id for v in records.items}
        name_map = get_admin_user_name_map(user_ids)
        items = []
        for item in records.items:
            tmp = dict(
                id=item.id,
                created_at=item.created_at,
                name=item.name,
                user_id=item.user_id,
                operator=name_map.get(item.user_id, ""),
                size=item.size,
                url=item.static_url,
            )
            items.append(tmp)

        return dict(
            total=records.total,
            items=items,
        )


@ns.route('/title-subtitle/template')
@respond_with_code
class TitleSubtitleTemplateResource(Resource):
    export_headers = (
        {'field': 'display', Language.ZH_HANS_CN: '语言'},
        {'field': 'lang', Language.ZH_HANS_CN: 'lang'},
        {'field': 'title', Language.ZH_HANS_CN: '活动主标题'},
        {'field': 'subtitle', Language.ZH_HANS_CN: '活动副标题'},
    )

    @classmethod
    def get_available_lang_dict(cls):
        names = language_cn_names()
        available_lang_dict = {}
        for lang in Language:
            available_lang_dict[lang] = names.get(lang, lang.name)
        return available_lang_dict

    @classmethod
    def get(cls):
        """运营-主副标题形式-下载模板"""
        available_lang_dict = cls.get_available_lang_dict()
        languages = [{"lang": k.value, "display": v, "title": '', "subtitle": ''} for k, v in
                     available_lang_dict.items()]
        return export_xlsx(
            filename='template',
            data_list=languages,
            export_headers=cls.export_headers
        )


@ns.route("/title-subtitle/upload")
@respond_with_code
class TitleSubtitleUploadResource(Resource):

    @classmethod
    def post(cls):
        """运营-主副标题形式-上传文件"""
        file_ = request.files.get('file')
        file_columns = ["display", "lang", "title", "subtitle"]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)
        return rows


@ns.route('/unfreeze')
@respond_with_code
class UnfreezeUserListResource(Resource, Reset2FAMixin):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(SecurityResetApplication.StatusType),
        identity_type=EnumField(SecurityResetApplication.IdentityType),
        balance_choice=EnumField(Reset2FAMixin.BalanceChoice),
        kyc_status=EnumField(User.KYCStatus),
        pass_type=EnumField(SecurityResetMethod),
        start_date=TimestampField(is_ms=True),
        end_date=TimestampField(is_ms=True),
        keyword=fields.String,
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        order=EnumField(["created_at"], missing="id"),
        order_type=EnumField(["asc", "desc"], missing='desc'),
    ))
    def get(cls, **kwargs):
        """
        风控-自助解冻管理
        """
        page, limit = kwargs['page'], kwargs['limit']
        query = cls.get_query(kwargs, business='unfreeze')
        records = query.paginate(page, limit, error_out=False)
        res = []
        items = records.items
        ids, user_ids = list(), set()
        for item in items:
            ids.append(item.id)
            user_ids.add(item.user_id)
        kyc_rows = KycVerification.query.filter(
            KycVerification.user_id.in_(user_ids),
            KycVerification.status == KycVerification.Status.PASSED
        ).with_entities(KycVerification.user_id, KycVerification.id, KycVerification.created_at).all()
        kyc_created_at_map = {row.user_id: row.created_at for row in kyc_rows}
        kyc_id_map = {row.user_id: row.id for row in kyc_rows}
        user_info = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(User.id, User.email, User.kyc_status).all()
        user_info_map = dict()
        for u in user_info:
            user_info_map[u.id] = dict(email=u.email, kyc_status=u.kyc_status.name)
        liveness_rows = LivenessCheckHistory.query.filter(
            LivenessCheckHistory.business == LivenessCheckHistory.Business.UNFREEZE_ACCOUNT,
            LivenessCheckHistory.business_id.in_(ids)
        ).all()
        liveness_map = {i.business_id: dict(
            id=i.id,
            transaction_id=i.transaction_id,
            status=i.status,
            reject_reason=i.get_reject_reason(translate=False)
        ) for i in liveness_rows}
        for item in items:
            item: SecurityResetApplication
            res.append(dict(
                id=item.id,
                created_at=item.created_at,
                audited_at=item.audited_at,
                checked_at=item.checked_at,
                user_id=item.user_id,
                email=user_info_map[item.user_id]['email'],
                kyc_status=user_info_map[item.user_id]['kyc_status'],
                rejection_reason=item.rejection_reason,
                is_custom_reason=item.is_custom_reason,
                remark=item.remark,
                status=item.status.name,
                liveness_id=liveness_map[item.id]['id'] if liveness_map.get(item.id) else None,
                liveness_transaction_id=liveness_map[item.id]['transaction_id'] if liveness_map.get(item.id) else None,
                liveness_status=liveness_map[item.id]['status'].name if liveness_map.get(item.id) else '',
                liveness_reject_reason=liveness_map.get(item.id, {}).get('reject_reason', ''),
                identity_type=item.identity_type.name,
                kyc_id=kyc_id_map.get(item.user_id),
                kyc_created_at=kyc_created_at_map.get(item.user_id),
                reset_method=SecurityResetApplication.ResetType.UNFREEZE_ACCOUNT,
                balance_usd=amount_to_str(item.balance_usd, 2)
            ))
        return dict(
            statuses=SecurityResetApplication.StatusType,
            liveness_statuses=LivenessCheckHistory.Status,
            kyc_statuses={
                User.KYCStatus.PASSED.name: "已实名",
                User.KYCStatus.NONE.name: "未实名",
                User.KYCStatus.FAILED.name: "失败",
                User.KYCStatus.PROCESSING.name: "处理中"
            },
            pass_types={
                SecurityResetMethod.CUSTOMER.name: "人工审核",
                SecurityResetMethod.ANSWER.name: "系统重置"
            },
            identity_types=SecurityResetApplication.IdentityType,
            balance_choices=cls.BalanceChoice,
            items=res,
            total=records.total
        )


@ns.route('/unfreeze/<int:id_>/audit')
@respond_with_code
class UnfreezeUserAuditResource(Resource, Reset2FAMixin):

    @classmethod
    def patch(cls, id_):
        """
        风控-自助解冻管理-初核通过
        """
        status = SecurityResetApplication.StatusType.AUDITED
        application = cls.get_record_by_id(id_, 'unfreeze')
        if application.status != SecurityResetApplication.StatusType.CREATED:
            raise InvalidArgument(message=f"操作失败，此申请状态为 {application.status.name}")
        ori_status = application.status.value
        user_id = application.user_id
        application.status = status
        application.auditor_id = g.user.id
        application.audited_at = now()
        db.session.commit()
        if application.balance_usd < SecurityResetApplication.UNFREEZE_AUDIT_REQUIRED_BALANCE_LIMIT:
            application.status = SecurityResetApplication.StatusType.PASSED
            db.session.commit()
            UnfreezeAccountHelper.unfreeze_account(user_id)
            send_reset_security_notice_email.delay(
                application.user_id, application.reset_type.name, application.status.name, None)
        new_status = application.status.value
        detail = f'从【{ori_status}】变更为【{new_status}】'
        UnfreezeOperationLog.add(user_id, UnfreezeOperationLog.Type.STATUS, detail, g.user.id, id_)

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.UnfreezeAccount,
            detail=dict(application_id=id_, detail=detail),
            target_user_id=application.user_id,
        )


@ns.route('/unfreeze/<int:id_>/pass')
@respond_with_code
class UnfreezeUserPassResource(Resource, Reset2FAMixin):
    @classmethod
    def patch(cls, id_):
        """
        风控-自助解冻管理-复核通过
        """
        status = SecurityResetApplication.StatusType.PASSED
        application = cls.get_record_by_id(id_, 'unfreeze')
        if application.status != SecurityResetApplication.StatusType.AUDITED:
            raise InvalidArgument(message=f"操作失败，此申请状态为 {application.status.name}")
        ori_status = application.status.value

        user_id = application.user_id
        application.status = status
        application.checker_id = g.user.id
        application.checked_at = now()
        db.session.commit()
        UnfreezeAccountHelper.unfreeze_account(user_id)
        send_reset_security_notice_email.delay(
            application.user_id, application.reset_type.name, application.status.name, None)
        new_status = application.status.value
        detail = f'从【{ori_status}】变更为【{new_status}】'
        UnfreezeOperationLog.add(user_id, UnfreezeOperationLog.Type.STATUS, detail, g.user.id, id_)

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.UnfreezeAccount,
            detail=dict(application_id=id_, detail=detail),
            target_user_id=application.user_id,
        )


@ns.route('/unfreeze/<int:id_>/reject')
@respond_with_code
class UnfreezeUserRejectResource(Resource, Reset2FAMixin):
    @classmethod
    @ns.use_kwargs(dict(
        reason=fields.List(EnumField(SecurityResetApplication.Reason)),
        is_custom_reason=fields.Boolean(missing=False),
        custom_reject_reason=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """
        风控-自助解冻管理-拒绝
        """
        status = SecurityResetApplication.StatusType.REJECTED
        reason = kwargs.get('reason')
        is_custom_reason = kwargs.get('is_custom_reason')
        custom_reject_reason = kwargs.get("custom_reject_reason")
        if is_custom_reason:
            if not custom_reject_reason:
                raise InvalidArgument(message="拒绝申请必须注明原因")
            if len(custom_reject_reason) > 4096:
                raise InvalidArgument(message="自定义拒绝原因长度不能超过4096")
        else:
            if not reason:
                raise InvalidArgument(message="拒绝申请必须注明原因")

        application = cls.get_record_by_id(id_, 'unfreeze')
        if application.status in [SecurityResetApplication.StatusType.PASSED,
                                  SecurityResetApplication.StatusType.REJECTED,
                                  ]:
            raise InvalidArgument(message=f"操作失败，此申请状态为 {application.status.name}")
        ori_status = application.status.value
        user_id = application.user_id
        if application.status == SecurityResetApplication.StatusType.CREATED:
            application.auditor_id = g.user.id
            application.audited_at = now()
        else:
            application.checker_id = g.user.id
            application.checked_at = now()
        application.status = SecurityResetApplication.StatusType.REJECTED

        if is_custom_reason:
            application.is_custom_reason = is_custom_reason
            application.custom_reason = custom_reject_reason
        else:
            application.reason = ','.join([i.name for i in reason])
        db.session.commit()
        pref = UserPreferences(user_id)
        with force_locale(pref.language.value):
            reason_text = application.get_reject_reason()
        send_reset_security_notice_email.delay(
            application.user_id, application.reset_type.name, status.name, reason_text)
        new_status = application.status.value
        detail = f'从【{ori_status}】变更为【{new_status}】'
        UnfreezeOperationLog.add(user_id, UnfreezeOperationLog.Type.STATUS, detail, g.user.id, id_)

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.UnfreezeAccount,
            detail=dict(application_id=id_, detail=detail),
            target_user_id=application.user_id,
        )


@ns.route('/unfreeze/<int:id_>/remark')
@respond_with_code
class UnfreezeUserRemarkResource(Resource, Reset2FAMixin):

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """风控-自助解冻管理-修改备注"""
        row: SecurityResetApplication = cls.get_record_by_id(id_, 'unfreeze')
        if row is None:
            raise RecordNotFound
        ori_remark = row.remark
        row.remark = kwargs['remark']
        db.session.commit()
        new_remark = row.remark
        detail = f'从【{ori_remark}】变更为【{new_remark}】'
        UnfreezeOperationLog.add(row.user_id, UnfreezeOperationLog.Type.REMARK, detail, g.user.id, id_)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.UnfreezeAccount,
            old_data=dict(remark=ori_remark),
            new_data=dict(remark=new_remark),
            target_user_id=row.user_id,
        )

        return row


@ns.route('/unfreeze/detail')
@respond_with_code
class UnfreezeUserDetailResource(Resource, Reset2FAMixin):
    NEXT_QUERY_SCHEMA = dict(
        status=EnumField(SecurityResetApplication.StatusType),
        balance_choice=EnumField(Reset2FAMixin.BalanceChoice),
        kyc_status=EnumField(User.KYCStatus),
        pass_type=EnumField(SecurityResetMethod),
        start_date=TimestampField(is_ms=True),
        end_date=TimestampField(is_ms=True),
        keyword=fields.String,
        order=EnumField(["created_at"], missing="id"),
        order_type=EnumField(["asc", "desc"], missing='asc'),
    )
    DETAIL_SCHEMA = dict(
        application_id=fields.Integer(required=True),
    )

    @classmethod
    def get_category_reasons(cls) -> list[dict]:
        reason_em = SecurityResetApplication.Reason
        category_reasons_map = {
            "人脸/手持相关": [
                reason_em.UNFREEZE_INVALID_KYC_INFORMATION,
                reason_em.UNFREEZE_FACE_SHOWN_ANNOUNCEMENT_AND_ID_COMPLIANT,
                reason_em.UNFREEZE_IDENTIFICATION_UNMATCHED,
                reason_em.UNFREEZE_ANNOUNCEMENT_UNMATCHED,
                reason_em.UNFREEZE_INVALID_TIME_FORMAT,
                reason_em.UNFREEZE_BLURRY_ANNOUNCEMENT,
            ],
            "证件相关": [
                reason_em.UNFREEZE_BLURRY_IDENTIFICATION,
                reason_em.UNFREEZE_UNSUPPORT_ID_TYPE,
                reason_em.UNFREEZE_PICTURE_TAMPERED,
                reason_em.UNFREEZE_ID_DAMAGED,
                reason_em.UNFREEZE_NO_ORIGINAL_ID,
                reason_em.UNFREEZE_PHOTO_FROM_SNAPSHOT,
                reason_em.UNFREEZE_IDENTIFICATION_UNMATCHED,
                reason_em.UNFREEZE_ID_EXPIRED,
            ],
            "第三方截图相关": [
                reason_em.UNFREEZE_INVALID_THIRD_PARTY_WITHDRAWAL_DEPOSIT_SNAPSHOT,
                reason_em.UNFREEZE_THIRD_PARTY_SNAPSHOT_KEY_MSG_LOSS,
                reason_em.UNFREEZE_THIRD_PARTY_SNAPSHOT_FROM_EMAIL,
                reason_em.UNFREEZE_BLOCK_CHAIN_SNAPSHOT,
                reason_em.UNFREEZE_THIRD_PARTY_SNAPSHOT_FROM_OWN_COINEX_ACCOUNT,
                reason_em.UNFREEZE_SNAPSHOT_AND_THP_WD_SNAPSHOT_UNMATCHED,
                reason_em.UNFREEZE_OBTAIN_VIABTC_WITHDRAWAL_DEPOSIT,
            ],
            "生物识别验证": [
                reason_em.LIVENESS_BAD_FACE_COMPARISON,
                reason_em.LIVENESS_SELFIE_LIVENESS,
                reason_em.LIVENESS_WATERMARK,
                reason_em.LIVENESS_WITH_PHONE,
                reason_em.LIVENESS_MANY_PEOPLE,
                reason_em.LIVENESS_FAKE,
                reason_em.LIVENESS_FORCED,
                reason_em.LIVENESS_SELFIE_MISMATCH,
                reason_em.LIVENESS_SELFIE_WITH_ID,
                reason_em.LIVENESS_VIDEO_SELFIE,
                reason_em.LIVENESS_OTHER,
            ],
        }
        result = []
        for ct, rs in category_reasons_map.items():
            item = {
                "category": ct,
                "reasons": [dict(name=i.name, value=i.value) for i in rs],
            }
            result.append(item)
        return result

    @classmethod
    @ns.use_kwargs(dict(
        **DETAIL_SCHEMA,
        **NEXT_QUERY_SCHEMA
    ))
    def get(cls, **kwargs):
        """
        风控-自助解冻管理-详情页面
        """
        application = cls.get_record_by_id(kwargs['application_id'], 'unfreeze')
        user_id = application.user_id
        user = User.query.get(user_id)
        kyc: KycVerification = KycVerification.query.filter(
            KycVerification.user_id == user_id,
            KycVerification.status == KycVerification.Status.PASSED
        ).first()
        files = SecurityResetFile.query.filter(
            SecurityResetFile.application_id == application.id
        ).all()
        file_map = dict()
        required_file_types = []
        for file in files:
            file: SecurityResetFile
            required_file_types.append(file.file_type.value)
            file_map[file.file_type.value] = AWSBucketPrivate.get_file_url(file.file_key)
        if kyc:
            file_map['kyc_front_img_file'] = kyc.front_img_file.private_url if kyc.front_img_file else None
            file_map['kyc_back_img_file'] = kyc.back_img_file.private_url if kyc.back_img_file else None
            file_map['kyc_face_img_file'] = kyc.face_img_file.private_url if kyc.face_img_file else None
        liveness: LivenessCheckHistory = LivenessCheckHistory.query.filter(
            LivenessCheckHistory.business == LivenessCheckHistory.Business.UNFREEZE_ACCOUNT,
            LivenessCheckHistory.business_id == application.id
        ).first()
        if liveness and liveness.face_img_file_id:
            file_map['liveness_face_img_file'] = liveness.face_img_file_url
        
        enums = dict(
            kyc_statuses={
                User.KYCStatus.PASSED.name: "已实名",
                User.KYCStatus.NONE.name: "未实名",
                User.KYCStatus.FAILED.name: "失败",
                User.KYCStatus.PROCESSING.name: "处理中"
            },
            id_types=KycVerification.IDType,
            category_reasons=cls.get_category_reasons(),
            statuses=SecurityResetApplication.StatusType,
            identity_types=SecurityResetApplication.IdentityType,
            liveness_statuses=LivenessCheckHistory.Status,
        )
        # 查找下一个待审核的申请
        next_application = cls.get_next(kwargs)

        price_map = PriceManager.assets_to_usd()
        current_balance_usd = BalanceManager(user_id, [], price_map).get_current_balance_usd()
        author_name_map = get_admin_user_name_map([application.auditor_id])
        reject_count = cls.get_reject_count(application, SecurityResetApplication.ADMIN_UNFREEZE_RESET_LIST)
        last_reason = cls.get_last_rejected_reason(
            application.user_id, application.id, SecurityResetApplication.ADMIN_UNFREEZE_RESET_LIST
        ) if reject_count > 0 else '/'
        return dict(
            application_id=kwargs['application_id'],
            created_at=application.created_at,
            audited_at=application.audited_at,
            checked_at=application.checked_at,
            next_application_id=next_application.id if next_application else None,
            kyc_id=kyc.id if kyc else None,
            kyc_created_at=kyc.created_at if kyc else None,
            kyc_updated_at=kyc.updated_at if kyc else None,
            kyc_last_passed_at=kyc.last_passed_at if kyc else None,
            user_id=user.id,
            auditor_id=application.auditor_id,
            checker_id=application.checker_id,
            author_email=None if not application.auditor_id else User.query.get(application.auditor_id).email,
            author_name=author_name_map.get(application.auditor_id),
            checker_email=None if not application.checker_id else User.query.get(application.checker_id).email,
            checker_name=author_name_map.get(application.checker_id),
            email=user.email,
            mobile=user.mobile,
            balance=amount_to_str(application.balance_usd, 2),
            current_balance=amount_to_str(current_balance_usd, 2),
            reason_str=application.get_reject_reason(),
            last_reason=last_reason,
            totp_status=bool(user.totp_auth_key),
            kyc_status=user.kyc_status.name,
            country=user.kyc_country,
            id_type=kyc.id_type.name if kyc else None,
            name=kyc.full_name if kyc else None,
            id_num=kyc.id_number if kyc else None,
            enums=enums,
            required_file_types=required_file_types,
            reset_type=application.reset_type.value,
            ip=application.ip,
            platform=application.platform,
            status=application.status.name,
            identity_type=application.identity_type.name,
            is_custom_reason=application.is_custom_reason,
            remark=application.remark,
            user_remark=get_user_remark(user),
            reject_count=reject_count or '/',
            liveness_id=liveness.id if liveness else None,
            liveness_transaction_id=liveness.transaction_id if liveness else None,
            liveness_status=liveness.status.name if liveness else '',
            liveness_reject_reason=liveness.get_reject_reason(translate=False) if liveness else '',
            user_info=dict(
                id=user.id,
                created_at=user.created_at,
                registration_ip=user.registration_ip,
                registration_location=user.registration_location,
                email=user.email,
                mobile=user.mobile,
                name=user.name,
                user_type=user.user_type,
                kyc_status=user.kyc_status
            ),
            **file_map
        )

    @classmethod
    def get_next(cls, kwargs):
        next_kwargs = kwargs.copy()
        next_kwargs.update(to_next_field=kwargs['application_id'])
        next_query = cls.get_query(next_kwargs, business='unfreeze')
        return next_query.first()


@ns.route('/unfreeze-log')
@respond_with_code
class UnfreezeLogResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        search_keyword=fields.String,
        start_date=TimestampField(is_ms=True),
        end_date=TimestampField(is_ms=True),
        operate_type=EnumField(UnfreezeOperationLog.Type),
        op_id=fields.Integer(allow_none=True),
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """风控-自助解冻-变更记录"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)
        pages = query.paginate(params.page, params.limit, error_out=False)
        user_ids = set()
        for record in pages.items:
            user_ids.add(record.user_id)
            user_ids.add(record.admin_user_id)
        user2email = UserRepository.get_users_id_email_map(user_ids)
        name_map = get_admin_user_name_map(user_ids)
        results = []
        for record in pages.items:
            results.append(
                dict(
                    created_at=record.created_at,
                    user_id=record.user_id,
                    detail=record.detail,
                    email=user2email.get(record.user_id) or '--',
                    operate_type=record.operate_type.value,
                    admin_user_id=record.admin_user_id,
                    operator=name_map.get(record.admin_user_id) or '--',
                    op_id=record.op_id,
                )
            )
        return dict(
            items=results,
            total=pages.total,
            operate_types={op_type.name: op_type.value for op_type in UnfreezeOperationLog.Type}
        )

    @classmethod
    def get_query_by(cls, params):
        query = UnfreezeOperationLog.query.order_by(UnfreezeOperationLog.id.desc())
        if params.operate_type:
            query = query.filter(UnfreezeOperationLog.operate_type == params.operate_type)
        if params.op_id:
            query = query.filter(UnfreezeOperationLog.op_id == params.op_id)
        if params.start_date:
            query = query.filter(
                UnfreezeOperationLog.created_at >= params.start_date
            )
        if params.end_date:
            query = query.filter(
                UnfreezeOperationLog.created_at <= params.end_date
            )

        if params.search_keyword:
            search_keyword = params.search_keyword.strip()
            user_ids = User.search_for_users(search_keyword)
            query = query.filter(UnfreezeOperationLog.user_id.in_(user_ids))

        return query


class KycVerificationProMixin(AssignAuditorMixin):

    AUDIT_TYPES = dict(
        third_party="自动审核",
        admin="人工审核",
        audit_required='人工二次审核',
    )

    @classmethod
    def get_audit_type(cls, item: KycVerificationPro) -> str:
        if (item.service_type != KycVerificationPro.ServiceType.MANUAL_AUDIT
                and item.status != KycVerificationPro.Status.AUDIT_REQUIRED and not item.auditor_id):
            audit_type = 'third_party'
        elif (item.service_type != KycVerificationPro.ServiceType.MANUAL_AUDIT
              and item.status == KycVerificationPro.Status.AUDIT_REQUIRED):
            audit_type = 'audit_required'
        else:
            audit_type = 'admin'
        return audit_type


@ns.route("/kyc-pro")
@respond_with_code
class KycVerificationProResource(Resource, KycVerificationProMixin):
    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "audited_at", Language.ZH_HANS_CN: "审核时间"},
        {"field": "platform", Language.ZH_HANS_CN: "终端"},
        {"field": "user_email", Language.ZH_HANS_CN: "用户"},
        {"field": "country", Language.ZH_HANS_CN: "初级认证居住国家"},
        {"field": "address", Language.ZH_HANS_CN: "居住地"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "audit_type", Language.ZH_HANS_CN: "审核类型"},
        {"field": "auditor_name", Language.ZH_HANS_CN: "审核人"},
        {"field": "rejection_reason", Language.ZH_HANS_CN: "拒绝原因"},
        {"field": "third_reject_reasons", Language.ZH_HANS_CN: "第三方拒绝原因"},
    )

    @classmethod
    def get_user_email_dic(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = User.query.filter(
                User.id.in_(ids)
            ).with_entities(
                User.id,
                User.email
            ).all()
            res.update({user.id: user.email for user in users})
        return res

    @classmethod
    def get_user_country_dic(cls, user_ids):
        res = dict()
        for u_ids in batch_iter(user_ids, 2000):
            kyc_countries = KycVerification.query.filter(
                KycVerification.user_id.in_(u_ids),
                KycVerification.status == KycVerification.Status.PASSED
            ).with_entities(
                KycVerification.user_id,
                KycVerification.country
            ).all()
            res.update({item.user_id: item.country for item in kyc_countries})
        return res

    @classmethod
    def get_query_sql(cls, kwargs, auditor_ids_map: dict = None):
        query = KycVerificationPro.query
        if user_id := kwargs.get("user_id"):
            query = query.filter(
                KycVerificationPro.user_id == user_id
            )
        if country := kwargs.get("country"):
            # 初级kyc认证的用户
            query = query.join(
                KycVerification, KycVerificationPro.user_id == KycVerification.user_id
            ).filter(
                KycVerification.country == country,
                KycVerification.status == KycVerification.Status.PASSED
            )
        if status := kwargs.get("status"):
            query = query.filter(
                KycVerificationPro.status == status
            )
        if platform := kwargs.get("platform"):
            query = query.filter(
                KycVerificationPro.platform == platform
            )
        if start := kwargs.get("start_at"):
            query = query.filter(
                KycVerificationPro.created_at > start
            )
        if end := kwargs.get("end_at"):
            query = query.filter(
                KycVerificationPro.created_at <= end
            )
        if audit_type := kwargs.get('audit_type'):
            if audit_type == 'admin':
                query = query.filter(
                    or_(
                        and_(
                            KycVerificationPro.service_type == KycVerificationPro.ServiceType.MANUAL_AUDIT,
                            KycVerificationPro.status == KycVerificationPro.Status.AUDIT_REQUIRED
                        ),
                        KycVerificationPro.auditor_id.isnot(None)
                    ),
                )
            elif audit_type == 'third_party':
                query = query.filter(
                    KycVerificationPro.service_type != KycVerificationPro.ServiceType.MANUAL_AUDIT,
                    KycVerificationPro.status != KycVerificationPro.Status.AUDIT_REQUIRED,
                    KycVerificationPro.auditor_id.is_(None),
                )
            elif audit_type == 'audit_required':
                query = query.filter(
                    KycVerificationPro.service_type != KycVerificationPro.ServiceType.MANUAL_AUDIT,
                    KycVerificationPro.status == KycVerificationPro.Status.AUDIT_REQUIRED,
                )
        if (auditor_id := kwargs.get('auditor_id')) is not None:
            if auditor_id == 0:
                kyc_pro_ids = {r.id for r in KycVerificationPro.query.filter(
                    KycVerificationPro.status == KycVerificationPro.Status.AUDIT_REQUIRED
                ).with_entities(
                    KycVerificationPro.id
                ).all()}
                assigned_ids = set()
                for ids in auditor_ids_map.values():
                    assigned_ids |= set(ids)
                kyc_pro_ids -= assigned_ids
            else:
                kyc_pro_ids = auditor_ids_map.get(str(auditor_id), [])
            query = query.filter(
                KycVerificationPro.id.in_(kyc_pro_ids)
            )

        query = query.order_by(KycVerificationPro.id.desc())
        return query

    @classmethod
    def query_data(cls, kwargs):
        auditor_map = {}
        auditor_ids_map = KYCProAssignAuditorsCache().read()
        for auditor_id, kyc_ids in auditor_ids_map.items():
            for kyc_id in kyc_ids:
                auditor_map[kyc_id] = int(auditor_id)
        query = cls.get_query_sql(kwargs, auditor_ids_map)
        if kwargs['export']:
            records = query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(records)
        elif start_id := kwargs.get('start_id'):
            admin_user_id = g.user.id
            kyc_ids = auditor_ids_map.get(str(admin_user_id), [])
            kyc_ids.sort()
            if start_id in set(kyc_ids):
                next_index = (kyc_ids.index(start_id) + 1) % len(kyc_ids)
            else:
                next_index = 0
            query = KycVerificationPro.query.filter(
                KycVerificationPro.id.in_(kyc_ids[next_index: next_index + 1])
            )
            pagination = query.paginate(1, 1, error_out=False)
            total = pagination.total
            records = pagination.items
        else:
            page, limit = kwargs['page'], kwargs['limit']
            pagination = query.paginate(page, limit, error_out=False)
            total = pagination.total
            records = pagination.items
        user_ids = []
        for i in records:
            user_ids.append(i.user_id)
        email_mapper = cls.get_user_email_dic(user_ids)
        kyc_country_map = cls.get_user_country_dic(user_ids)

        items = []
        for item in records:
            items.append(dict(
                id=item.id,
                user_id=item.user_id,
                user_email=email_mapper.get(item.user_id, ""),
                status=item.status.name,
                country=kyc_country_map.get(item.user_id, ""),
                address=item.address,
                auditor_id=item.auditor_id if item.status != KycVerificationPro.Status.AUDIT_REQUIRED else auditor_map.get(item.id),
                audited_at=item.audited_at,
                created_at=item.created_at,
                platform=item.platform.name if item.platform else "",
                remark=item.remark,
                audit_type=cls.get_audit_type(item),
                rejection_reason=item.get_reject_reason(translate=False),
                history_id=item.history_id,
            ))
        history_ids = [item['history_id'] for item in items if item['history_id']]
        histories = KycVerificationProHistory.query.filter(
            KycVerificationProHistory.id.in_(history_ids)
        ).all()
        third_reject_reason_mapper = {}
        for h in histories:
            detail = json.loads(h.detail)
            kyc_pro_id = detail.get('kyc_pro_id')
            if kyc_pro_id:
                reason_raw = detail.get('reason_raw', [])
                third_reject_reason_mapper[kyc_pro_id] = KycProBusiness.get_third_reject_reason_desc(h.service_type, reason_raw)

        auditor_ids = [item['auditor_id'] for item in items if item['auditor_id']]
        name_map = get_admin_user_name_map(auditor_ids)
        for item in items:
            item['auditor_name'] = name_map.get(item['auditor_id'], '--')

        if kwargs['export']:
            for _item in items:
                _item['status'] = KycVerificationPro.Status[_item['status']].value
                _item['created_at'] = _item['created_at'].strftime("%Y-%m-%d %H:%M:%S")
                _item['audited_at'] = _item['audited_at'].strftime("%Y-%m-%d %H:%M:%S") if _item['audited_at'] else ''
                _item['third_reject_reasons'] = '；'.join(third_reject_reason_mapper.get(_item['id'], []))
                _item['audit_type'] = cls.AUDIT_TYPES.get(_item['audit_type'])

            remove_illegal_excel_chars(items, fields={"address", })
            return export_xlsx(
                filename="kyc_verification_pro_list",
                data_list=items,
                export_headers=cls.export_headers,
            )

        audit_statuses = {KycVerificationPro.Status.AUDIT_REQUIRED}
        if status := kwargs.get('status'):
            audit_statuses &= {status}
        audit_required_ids = set()
        if audit_statuses:
            audit_required_ids = {i.id for i in KycVerificationPro.query.filter(
                KycVerificationPro.status.in_(audit_statuses)
            ).with_entities(
                KycVerificationPro.id
            ).all()}
        return dict(
            items=items,
            total=total,
            extra=dict(
                countries={code: get_country(code).cn_name
                           for code in list_country_codes_3_admin()},
                statuses={i.name: i.value for i in KycVerificationPro.Status},
                audit_types=cls.AUDIT_TYPES,
                platforms=KycVerificationPro.Platform,
                auditor_list=cls.get_auditor_list(auditor_ids_map, audit_required_ids),
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        country=fields.String,
        status=EnumField(KycVerificationPro.Status),
        audit_type=EnumField(['admin', 'third_party', 'audit_required']),
        user_id=fields.Integer,
        platform=EnumField(KycVerificationPro.Platform),
        start_at=TimestampField(is_ms=True),
        end_at=TimestampField(is_ms=True),
        start_id=fields.Integer,
        auditor_id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField,
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """运营-KYC-个人高级认证列表"""
        return cls.query_data(kwargs)


@ns.route("/kyc-pro/download")
@respond_with_code
class KycVerificationProDownloadResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        country=fields.String,
        status=EnumField(KycVerificationPro.Status),
        user_id=fields.Integer,
        start_at=TimestampField(is_ms=True),
        end_at=TimestampField(is_ms=True),
        page=PageField(unlimited=True),
        limit=LimitField,
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """运营-KYC-个人高级认证下载"""
        return KycVerificationProResource.query_data(kwargs)


@ns.route('/kyc-pro/auditors')
@respond_with_code
class KycVerificationProAssignAuditorsResource(KYCResourceMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        auditor_id=fields.Integer(required=True),
        ids=fields.List(fields.Integer, required=True),
    ))
    def post(cls, **kwargs):
        """风控-KYC-个人高级认证-批量分配审核人"""
        auditor_id = kwargs["auditor_id"]
        if auditor_id not in cls.get_auditor_map():
            raise InvalidArgument(message='请选择正确的审核人')
        ids = set(kwargs["ids"])
        if not ids:
            return
        ids = [r.id for r in KycVerificationPro.query.filter(
            KycVerificationPro.id.in_(ids),
            KycVerificationPro.status == KycVerificationPro.Status.AUDIT_REQUIRED
        ).with_entities(
            KycVerificationPro.id
        ).all()]
        if not ids:
            return
        KYCProAssignAuditorsCache().assign(auditor_id, ids)


@ns.route("/kyc-pro/settings")
@respond_with_code
class KycVerificationProSettingsResource(Resource):

    @classmethod
    def get(cls):
        """运营-KYC-个人高级认证-配置查询"""
        return {
            'third_max_count': BusinessSettings.kyc_pro_third_max_count
        }

    @classmethod
    @ns.use_kwargs(dict(
        third_max_count=fields.Integer(required=True),
    ))
    def patch(cls, **kwargs):
        """运营-KYC-个人高级认证-配置修改"""
        old_value = BusinessSettings.kyc_pro_third_max_count
        new_value = kwargs["third_max_count"]
        BusinessSettings.kyc_pro_third_max_count = new_value
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.BusinessSettings,
            old_data={'kyc_pro_third_max_count': old_value},
            new_data={'kyc_pro_third_max_count': new_value},
        )


@ns.route("/kyc-pro/<int:kyc_pro_id>")
@respond_with_code
class KycVerificationProDetailResource(Resource, KycVerificationProMixin):

    @classmethod
    def kyc_por_to_dict(cls, kyc_pro: KycVerificationPro):
        user: User = User.query.get(kyc_pro.user_id)
        kyc_pro_map = kyc_pro.to_dict()
        parsed_info = kyc_pro_map.pop('parsed_info')
        parsed_info = json.loads(parsed_info) if parsed_info else {}
        kyc_pro_map['parsed_address'] = parsed_info.get('address', '')
        kyc_pro_map['parsed_name'] = parsed_info.get('full_name', '')
        kyc_pro_map['rejection_reason'] = kyc_pro.rejection_reason.split(',') if kyc_pro.rejection_reason else []

        auditor_ids, reject_reasons, status_names, audit_times = [], [], [], []
        for log in AdminOperationLog.query.filter(
                AdminOperationLog.target_user_id==kyc_pro.user_id,
                AdminOperationLog.namespace==OPNamespaceObjectRisk.KycPro.namespace.name,
                AdminOperationLog.object==OPNamespaceObjectRisk.KycPro.object.name,
                AdminOperationLog.operation==AdminOperationLog.Operation.AUDIT,
                AdminOperationLog.created_at>=kyc_pro.created_at,
        ).all():
            log: AdminOperationLog
            _detail = json.loads(log.detail)
            if _detail.get('kyc_pro_id') == kyc_pro.id:
                current_status = _detail.get('status')
                if current_status:
                    auditor_ids.append(log.user_id)
                    status_names.append(current_status)
                    audit_times.append(log.created_at)
                    if current_status == KycVerificationPro.Status.REJECTED.value:
                        if r := _detail.get('custom_rejection_reason'):
                            _reason = r
                        elif r := _detail.get('custom_reject_reason'):  # 之前是这个字段
                            _reason = r
                        else:
                            _reason = _detail.get('rejection_reason')
                        reject_reasons.append(_reason)
                    else:
                        reject_reasons.append('--')
        auditor_map = get_admin_user_name_map(auditor_ids)
        reason_map = {item.name: gettext(item.value) for item in KycVerificationPro.RejectionReason}
        auditors = [{id_: auditor_map[id_]} for id_ in auditor_ids]
        if not auditors:
            auditor_map = get_admin_user_name_map([kyc_pro.auditor_id])
            auditors = [{kyc_pro.auditor_id: auditor_map[kyc_pro.auditor_id]}, ] if kyc_pro.auditor_id else []

        auditor_name_map = get_admin_user_name_map([kyc_pro.auditor_id])
        kyc = user.kyc_verification

        last_record = KycVerificationPro.query.filter(
            KycVerificationPro.user_id == kyc_pro.user_id,
            KycVerificationPro.id < kyc_pro.id,
        ).order_by(KycVerificationPro.id.desc()).first()

        third_reject_reasons = []
        if kyc_pro.service_type != KycVerificationPro.ServiceType.MANUAL_AUDIT:
            histories = KycVerificationProHistory.query.filter(
                KycVerificationProHistory.created_at >= kyc_pro.created_at,
                KycVerificationProHistory.user_id == kyc_pro.user_id
            ).all()
            for item in histories:
                detail = json.loads(item.detail)
                if kyc_pro_id := detail.get('kyc_pro_id'):
                    if kyc_pro_id == kyc_pro.id:
                        reason_raw = detail.get('reason_raw', [])
                        third_reject_reasons = KycProBusiness.get_third_reject_reason_desc(item.service_type, reason_raw)
                        break

        return dict(
            **kyc_pro_map,
            last_rejection_reason=last_record.get_reject_reason() if last_record else [],
            is_custom_reason=bool(kyc_pro.custom_rejection_reason),
            auditor_name=auditor_name_map.get(kyc_pro.auditor_id, ""),
            file_urls=kyc_pro.address_file_urls,
            user_name=user.email,
            user_remark=get_user_remark(user),
            kyc=KYCResource._row_to_dict(kyc) if kyc else {
                "id_type_dict": {i.name: i.value for i in KycVerification.IDType}
            },
            audit_type=cls.get_audit_type(kyc_pro),
            audit_types=cls.AUDIT_TYPES,
            status_names=status_names,
            audit_times=audit_times,
            auditors=auditors,
            rejection_reasons=reason_map,
            reason_list=reject_reasons,
            third_reject_reasons=third_reject_reasons,
            extr=dict(
                reason_list={item.name: gettext(item.value) for item in KycVerificationPro.RejectionReason},
                countries={code: get_country(code).cn_name for code in list_country_codes_3_admin()},
            )
        )

    @classmethod
    def get(cls, kyc_pro_id):
        """kyc高级认证-认证详情"""
        kyc_pro = KycVerificationPro.query.get(kyc_pro_id)
        if not kyc_pro:
            raise InvalidArgument
        return cls.kyc_por_to_dict(kyc_pro)

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(KycVerificationPro.Status),
        rejection_reason=fields.List(EnumField(KycVerificationPro.RejectionReason)),
        custom_rejection_reason=fields.String
    ))
    def patch(cls, kyc_pro_id, **kwargs):
        """kyc高级认证-编辑审核"""
        status = kwargs.get('status')
        kyc_pro: KycVerificationPro = KycVerificationPro.query.get(kyc_pro_id)
        if not kyc_pro:
            raise RecordNotFound
        if kyc_pro.status == KycVerificationPro.Status.CANCELLED:
            raise InvalidArgument(message="已有新提交的KYC，不允许再操作旧KYC记录")
        with CacheLock(LockKeys.kyc_pro_operation(kyc_pro.user_id), wait=False):
            detail = dict(kwargs)
            detail['kyc_pro_id'] = kyc_pro.id
            if kyc_pro.rejection_reason:
                detail['history_rejection_reason'] = [KycVerificationPro.RejectionReason[i].value
                                                      for i in kyc_pro.rejection_reason.split(',')]
            detail['history_status'] = kyc_pro.status.value
            user: User = User.query.get(kyc_pro.user_id)
            custom_rejection_reason = kwargs.get("custom_rejection_reason")
            if not user:
                raise InvalidArgument(message="未找到该用户")
            if status == KycVerificationPro.Status.PASSED:
                if user.kyc_status != User.KYCStatus.PASSED:
                    raise InvalidArgument(message="用户初级kyc未通过。")
                kyc_pro.status = KycVerificationPro.Status.PASSED
            elif status == KycVerificationPro.Status.REJECTED:
                if not (rejection_reason := kwargs.get("rejection_reason")) and \
                        not custom_rejection_reason:
                    raise InvalidArgument(message="未填写或者选择拒绝原因")
                kyc_pro.status = KycVerificationPro.Status.REJECTED
                if rejection_reason:
                    kyc_pro.rejection_reason = ','.join([r.name for r in rejection_reason])
                    kyc_pro.custom_rejection_reason = None
                if custom_rejection_reason:
                    kyc_pro.custom_rejection_reason = custom_rejection_reason
                    kyc_pro.rejection_reason = None
            else:
                if status is not None:
                    raise InvalidArgument(message=f"状态异常: {status.name}")
            if status is not None:
                kyc_pro.auditor_id = g.user.id
                kyc_pro.audited_at = now()
            db.session.commit()
            if status is not None:
                KYCProAssignAuditorsCache().finish(kyc_pro_id)
            AdminOperationLog.new_audit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.KycPro,
                detail=detail,
                target_user_id=kyc_pro.user_id,
            )
            send_kyc_pro_result.delay(kyc_pro.id)
            return cls.kyc_por_to_dict(kyc_pro)

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String
    ))
    def put(cls, kyc_pro_id, **kwargs):
        """kyc高级认证-修改备注"""
        kyc_pro: KycVerificationPro = KycVerificationPro.query.get(kyc_pro_id)
        if not kyc_pro:
            raise RecordNotFound
        old_remark = kyc_pro.remark
        kyc_pro.remark = kwargs.get('remark', '')
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.KycPro,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=kyc_pro.remark),
            target_user_id=kyc_pro.user_id,
        )


@ns.route('/kyc-pro/history')
@respond_with_code
class KYCProHistoryListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(KycVerificationProHistory.Status),
        reason=EnumField(KycVerificationPro.RejectionReason),
        service_type=EnumField(KycVerificationPro.ServiceType),
        transaction_id=fields.String,
        user=fields.String,
        start=TimestampField(),
        end=TimestampField(),
        page=PageField(unlimited=True),
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """运营-KYC-高级认证第三方审核记录"""
        query = KycVerificationProHistory.query
        page, limit = kwargs['page'], kwargs['limit']
        if (status := kwargs.get('status')) is not None:
            if status in (KycVerificationProHistory.Status.FAILED, KycVerificationProHistory.Status.CANCELLED):
                query = query.filter(
                    KycVerificationProHistory.status.in_((
                        KycVerificationProHistory.Status.FAILED,
                        KycVerificationProHistory.Status.CANCELLED
                    ))
                )
            else:
                query = query.filter(KycVerificationProHistory.status == status)
        if user := kwargs.get('user', '').strip():
            query = query.filter(
                KycVerificationProHistory.user_id.in_(User.search_for_users(user)))
        if start := kwargs.get('start'):
            query = query.filter(KycVerificationProHistory.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(KycVerificationProHistory.created_at < end)
        if reason := kwargs.get('reason'):
            query = query.filter(KycVerificationProHistory.rejection_reason.like(f'%{reason.name}%'))
        if service_type := kwargs.get('service_type'):
            query = query.filter(KycVerificationProHistory.service_type == service_type)
        if transaction_id := kwargs.get('transaction_id'):
            query = query.filter(KycVerificationProHistory.transaction_id == transaction_id)
        query = query.order_by(KycVerificationProHistory.id.desc())
        records = query.paginate(page, limit, error_out=False)
        res = []
        user_ids = {item.user_id for item in records.items}
        users = User.query.filter(User.id.in_(user_ids)).all()
        user_map = {u.id: u for u in users}
        for item in records.items:
            item: KycVerificationProHistory
            detail = json.loads(item.detail)
            user = user_map[item.user_id]
            country = get_country(detail['country'])
            full_name = detail.get('full_name') or f"{detail['first_name']} {detail['last_name']}"
            res.append(dict(
                id=item.id,
                created_at=item.created_at,
                country=country.cn_name if country else None,
                full_name=full_name,
                address=detail['address'],
                transaction_id=item.transaction_id,
                status=KycVerificationProHistory.Status.CANCELLED.name
                if item.status == KycVerificationProHistory.Status.FAILED else item.status.name,
                user_id=item.user_id,
                user_email=user.email,
                user_name=user.name_displayed,
                service_type=item.service_type,
                kyc_pro_id=detail.get('kyc_pro_id'),
                rejection_reason=item.get_reject_reason()
            ))
        return dict(
            items=res,
            total=records.total,
            extra=dict(
                statuses={
                    KycVerificationProHistory.Status.CREATED.name: '待审核',
                    KycVerificationProHistory.Status.FINISHED.name: '已完成',
                    KycVerificationProHistory.Status.CANCELLED.name: '已取消'
                },
                reasons={item.name: gettext(item.value) for item in KycProBusiness.get_third_party_rejection_reason()},
                service_types=KycVerificationPro.ServiceType
            )
        )


@ns.route('/liveness-check/history')
@respond_with_code
class LivenessCheckHistoryResource(AssignAuditorMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(LivenessCheckHistory.Status),
        business=EnumField(LivenessCheckHistory.Business),
        audit_type=EnumField(LivenessCheckHistory.AuditType),
        transaction_id=fields.String,
        start_id=fields.Integer,
        auditor_id=fields.Integer,
        user=fields.String,
        start=TimestampField(),
        end=TimestampField(),
        page=PageField(unlimited=True),
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """生物识别认证-认证记录"""
        auditor_map = {}
        auditor_ids_map = LivenessAssignAuditorsCache().read()
        for auditor_id, liveness_ids in auditor_ids_map.items():
            for liveness_id in liveness_ids:
                auditor_map[liveness_id] = int(auditor_id)

        query = LivenessCheckHistory.query
        page, limit = kwargs['page'], kwargs['limit']
        if (status := kwargs.get('status')) is not None:
            query = query.filter(LivenessCheckHistory.status == status)
        if business := kwargs.get('business'):
            query = query.filter(LivenessCheckHistory.business == business)
        if user := kwargs.get('user', '').strip():
            query = query.filter(LivenessCheckHistory.user_id.in_(User.search_for_users(user)))
        if start := kwargs.get('start'):
            query = query.filter(LivenessCheckHistory.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(LivenessCheckHistory.created_at < end)
        if transaction_id := kwargs.get('transaction_id'):
            query = query.filter(LivenessCheckHistory.transaction_id == transaction_id)
        if audit_type := kwargs.get('audit_type'):
            if audit_type == LivenessCheckHistory.AuditType.ADMIN:
                query = query.filter(
                    or_(
                        LivenessCheckHistory.status == LivenessCheckHistory.Status.MANUAL_AUDIT_REQUIRED,
                        LivenessCheckHistory.auditor_id.isnot(None)
                    ),
                )
            elif audit_type == LivenessCheckHistory.AuditType.THIRD_PARTY:
                query = query.filter(
                    LivenessCheckHistory.status.in_([LivenessCheckHistory.Status.PASSED, LivenessCheckHistory.Status.REJECTED]),
                    LivenessCheckHistory.auditor_id.is_(None),
                )
        if (auditor_id := kwargs.get('auditor_id')) is not None:
            if auditor_id == 0:
                liveness_ids = {r.id for r in LivenessCheckHistory.query.filter(
                    LivenessCheckHistory.status == LivenessCheckHistory.Status.MANUAL_AUDIT_REQUIRED
                ).with_entities(
                    LivenessCheckHistory.id
                ).all()}
                assigned_ids = set()
                for ids in auditor_ids_map.values():
                    assigned_ids |= set(ids)
                liveness_ids -= assigned_ids
            else:
                liveness_ids = auditor_ids_map.get(str(auditor_id), [])
            query = query.filter(
                LivenessCheckHistory.id.in_(liveness_ids)
            )

        query = query.order_by(LivenessCheckHistory.id.desc())
        if start_id := kwargs.get('start_id'):
            admin_user_id = g.user.id
            liveness_ids = auditor_ids_map.get(str(admin_user_id), [])
            liveness_ids.sort()
            if start_id in set(liveness_ids):
                next_index = (liveness_ids.index(start_id) + 1) % len(liveness_ids)
            else:
                next_index = 0
            query = LivenessCheckHistory.query.filter(
                LivenessCheckHistory.id.in_(liveness_ids[next_index: next_index + 1])
            )
            pagination = query.paginate(1, 1, error_out=False)
            total = pagination.total
            records = pagination.items
        else:
            pagination = query.paginate(page, limit, error_out=False)
            total = pagination.total
            records = pagination.items

        res = []
        user_ids, auditor_ids = set(), set(auditor_map.values())
        for item in records:
            user_ids.add(item.user_id)
            auditor_ids.add(item.auditor_id)
        users = User.query.filter(User.id.in_(user_ids)).all()
        user_map = {u.id: u for u in users}
        admin_name_map = get_admin_user_name_map(auditor_ids)
        for item in records:
            item: LivenessCheckHistory
            user = user_map[item.user_id]
            auditor_id = item.auditor_id if item.status != LivenessCheckHistory.Status.MANUAL_AUDIT_REQUIRED else auditor_map.get(
                item.id)
            res.append(dict(
                id=item.id,
                created_at=item.created_at,
                transaction_id=item.transaction_id,
                business=item.business.name,
                status=item.status.name,
                user_id=item.user_id,
                user_email=user.email,
                user_name=user.name_displayed,
                kyc_id=item.kyc_id,
                third_reason=item.get_third_reject_reason(translate=False),
                admin_reason=item.get_admin_reject_reason(translate=False),
                audit_type=item.audit_type.name if item.audit_type else '',
                auditor_id=auditor_id,
                auditor_name=admin_name_map.get(auditor_id, '--'),
                email=item.email,
                remark=item.remark,
            ))
        audit_statuses = {LivenessCheckHistory.Status.MANUAL_AUDIT_REQUIRED}
        if status := kwargs.get('status'):
            audit_statuses &= {status}
        audit_required_ids = set()
        if audit_statuses:
            audit_required_ids = {i.id for i in LivenessCheckHistory.query.filter(
                LivenessCheckHistory.status.in_(audit_statuses)
            ).with_entities(
                LivenessCheckHistory.id
            ).all()}
        return dict(
            items=res,
            total=total,
            extra=dict(
                businesses=LivenessCheckHistory.Business,
                statuses=LivenessCheckHistory.Status,
                reasons={item.name: gettext(item.value) for item in KycBusiness.get_third_party_rejection_reason()},
                audit_types=LivenessCheckHistory.AuditType,
                auditor_list=cls.get_auditor_list(auditor_ids_map, audit_required_ids),
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        email=fields.String(required=True, validate=validate_email),
    ))
    def post(cls, **kwargs):
        """生物识别认证-发送认证邮件"""
        user_id = kwargs['user_id']
        email = kwargs['email']
        old_history = LivenessCheckHistory.query.filter(
            LivenessCheckHistory.user_id == user_id,
            LivenessCheckHistory.business == LivenessCheckHistory.Business.MANUAL,
            LivenessCheckHistory.status == LivenessCheckHistory.Status.CREATED
        ).first()
        if old_history:
            raise InvalidArgument(message='用户当前有待验证的记录')
        kyc = LivenessCheckBusiness.upload_kyc_data(user_id)
        action_id = LivenessCheckBusiness.get_action_id()
        row = LivenessCheckHistory(
            user_id=user_id,
            kyc_id=kyc.id,
            action_id=action_id,
            business=LivenessCheckHistory.Business.MANUAL,
            email=email,
        )
        db.session.add(row)
        db.session.commit()
        send_liveness_check_email_task.delay(row.id, email)
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.LivenessCheck,
            detail=row.to_dict(enum_to_name=True),
        )


@ns.route("/liveness-check/<int:id_>")
@respond_with_code
class LivenessCheckDetailResource(Resource):

    @classmethod
    def liveness_to_dict(cls, history: LivenessCheckHistory):
        history_dict = history.to_dict()
        history_dict['admin_reason'] = history.admin_reason.split(',') if history.admin_reason else []
        user: User = User.query.get(history.user_id)
        kyc: KycVerification = KycVerification.query.get(history.kyc_id)
        admin_user_name_map = get_admin_user_name_map([history.auditor_id])
        return dict(
            **history_dict,
            audit_type=history.audit_type.value if history.audit_type else '',
            third_reject_reason=history.get_third_reject_reason(translate=False),
            admin_reject_reason=history.get_admin_reject_reason(translate=False),
            is_custom_reason=bool(history.custom_admin_reason),
            face_img_file_url=history.face_img_file_url,
            user_email=user.email,
            auditor_name=admin_user_name_map.get(history.auditor_id),
            kyc=KYCResource._row_to_dict(kyc),
            extra=dict(
                audit_type_dict=LivenessCheckHistory.AuditType,
                status_dict=LivenessCheckHistory.Status,
                reason_dict=LivenessCheckHistory.Reason,
                country_dict={code: get_country(code).cn_name for code in list_country_codes_3_admin()},
            )
        )

    @classmethod
    def get(cls, id_):
        """生物识别认证-认证详情"""
        history = LivenessCheckHistory.query.get(id_)
        if not history:
            raise InvalidArgument
        return cls.liveness_to_dict(history)

    @classmethod
    @ns.use_kwargs(dict(
        admin_reason=fields.List(EnumField(LivenessCheckHistory.Reason)),
        is_custom_reason=fields.Boolean(missing=False),
        custom_admin_reason=fields.String,
        status=EnumField(LivenessCheckHistory.Status, required=True),
    ))
    def put(cls, id_, **kwargs):
        """生物识别验证-审核"""
        status = kwargs["status"]
        is_rejected = status == LivenessCheckHistory.Status.REJECTED
        admin_reason = kwargs.get("admin_reason")
        is_custom_reason = bool(kwargs.get("is_custom_reason"))
        custom_admin_reason = kwargs.get("custom_admin_reason")
        if is_rejected:
            if is_custom_reason:
                if not custom_admin_reason:
                    raise InvalidArgument(message="缺少自定义的拒绝原因")
            else:
                if not admin_reason:
                    raise InvalidArgument(message="请选择拒绝原因")

        row: LivenessCheckHistory = LivenessCheckHistory.query.get(id_)
        if row is None:
            raise RecordNotFound
        if row.status != LivenessCheckHistory.Status.MANUAL_AUDIT_REQUIRED:
            raise InvalidArgument(message='验证记录状态必须是待人工审核')
        old_status = row.status
        row.status = status
        if is_rejected:
            if is_custom_reason:
                row.custom_admin_reason = custom_admin_reason
            else:
                row.admin_reason = ','.join([i.name for i in admin_reason])
        row.auditor_id = g.user.id
        row.audited_at = now()
        LivenessCheckBusiness.update_business(row, g.user.id)
        db.session.commit()
        LivenessAssignAuditorsCache().finish(id_)

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.LivenessCheck,
            detail=dict(
                liveness_history_id=row.id,
                status=f'{old_status}->{status}'
            ),
            target_user_id=row.user_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String(required=True),
    ))
    def patch(cls, id_, **kwargs):
        """生物识别认证-修改备注"""
        history = LivenessCheckHistory.query.get(id_)
        if not history:
            raise InvalidArgument
        old_remark = history.remark
        history.remark = kwargs['remark']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.LivenessCheck,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=history.remark),
        )


@ns.route('/liveness-check/auditors')
@respond_with_code
class LivenessCheckAssignAuditorsResource(AssignAuditorMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        auditor_id=fields.Integer(required=True),
        ids=fields.List(fields.Integer, required=True),
    ))
    def post(cls, **kwargs):
        """风控-KYC-生物识别验证-批量分配审核人"""
        auditor_id = kwargs["auditor_id"]
        if auditor_id not in cls.get_auditor_map():
            raise InvalidArgument(message='请选择正确的审核人')
        ids = set(kwargs["ids"])
        if not ids:
            return
        ids = [r.id for r in LivenessCheckHistory.query.filter(
            LivenessCheckHistory.id.in_(ids),
            LivenessCheckHistory.status == LivenessCheckHistory.Status.MANUAL_AUDIT_REQUIRED
        ).with_entities(
            LivenessCheckHistory.id
        ).all()]
        if not ids:
            return

        LivenessAssignAuditorsCache().assign(auditor_id, ids)
