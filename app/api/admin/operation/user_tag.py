import json
from collections import defaultdict

from flask import g, send_file, request
from sqlalchemy import func
from webargs import fields

from app import Language
from app.api.common import (Namespace, Resource, respond_with_code)
from app.api.common.fields import TimestampField, EnumField
from app.schedules.user_tag import user_tag_group_statistic, GroupPortraitDataMixin, send_group_portrait_to_research_team
from app.business.user_tag import get_not_supported_tags, TagReader
from app.business.auth import get_admin_user_name_map
from app.exceptions import InvalidArgument, RecordNotFound
from app.models import db, User, UserExtra
from app.models.user_tag import (
    UserTagInfo, UserTagCategory, UserTagGroup,
    OperatorType, UserTag, UserTagGroupPortrait, AllowType,
)
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.utils import ExcelExporter, batch_iter, now, export_xlsx
from app.utils.helper import Struct
from app.utils.importer import get_table_rows

ns = Namespace('Operation - UserTag')


class UserTagMixin:

    @classmethod
    def get_tag_categories(cls):
        model = UserTagCategory
        rows = model.query.filter(
            model.status == model.Status.PASSED
        ).all()
        return {row.id: row for row in rows}


@ns.route('/list')
@respond_with_code
class UserTagListResource(UserTagMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        tag=EnumField(UserTag),
        category_id=fields.Integer,
        begin_at=TimestampField(),
        end_at=TimestampField(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-用户标签-列表"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)
        records = query.paginate(params.page, params.limit)
        items = records.items
        ret = []
        tag_categories = cls.get_tag_categories()
        for row in items:
            tag_category = tag_categories.get(row.category_id) or '-'
            row_dict = row.to_dict(enum_to_name=True)
            row_dict['tag_category'] = tag_category.name
            ret.append(row_dict)

        return dict(
            total=records.total,
            items=ret,
            extra=dict(
                user_tags=UserTag,
                tag_categories={x.id: x.name for (_, x) in tag_categories.items()},
            )
        )

    @classmethod
    def get_query_by(cls, params):
        model = UserTagInfo
        query = model.query.filter(
            model.status == model.Status.PASSED
        ).order_by(model.id.desc())
        if params.tag:
            query = query.filter(model.tag == params.tag)
        if params.category_id:
            query = query.filter(model.category_id == params.category_id)
        if params.begin_at:
            query = query.filter(model.created_at >= params.begin_at)
        if params.end_at:
            query = query.filter(model.created_at <= params.end_at)

        return query


@ns.route('/<int:id_>')
@respond_with_code
class UserTagDetailResource(UserTagMixin, Resource):
    model = UserTagInfo

    @classmethod
    def get(cls, id_):
        """运营-用户标签-详情"""
        row = cls._get_row(id_)
        return cls._to_ret(row)

    @classmethod
    @ns.use_kwargs(dict(
        category_id=fields.Integer(required=True),
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-用户标签-编辑"""

        params = Struct(**kwargs)
        cls._validate(params)

        row = cls._get_row(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.category_id = params.category_id
        row.remark = params.remark
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.UserTag,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            special_data=dict(tag=row.tag)
        )

        return cls._to_ret(row)

    @classmethod
    def _to_ret(cls, row):
        tag_categories = cls.get_tag_categories()
        ret = row.to_dict(enum_to_name=True)
        tag_category = tag_categories.get(row.category_id)
        ret['tag_category'] = tag_category.name if tag_category else '-'
        extra = dict(
            user_tags=UserTag,
            tag_categories={x.id: x.name for (_, x) in tag_categories.items()},
        )
        ret['extra'] = extra
        return ret

    @classmethod
    def _validate(cls, params):
        model = UserTagCategory
        row = model.query.get(params.category_id)
        if not row:
            raise InvalidArgument(message=f'{params.category_id}无效的标签类别')

    @classmethod
    def delete(cls, id_):
        """运营-用户标签-删除"""

        row = cls._get_row(id_)
        row.status = cls.model.Status.DELETED  # 仅 admin 不展示，并不影响此标签的统计任务
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.UserTag,
            detail=dict(id=id_, tag=row.tag),
        )

        return {}

    @classmethod
    def _get_row(cls, id_):
        row = cls.model.query.get(id_)
        if not row:
            raise InvalidArgument(message=f'{id_}标签不存在')
        return row


@ns.route('/categories')
@respond_with_code
class UserTagCategoriesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-用户标签-类别管理"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)
        records = query.paginate(params.page, params.limit)
        items = records.items
        ret = []
        tag_count_mapping = cls._get_tag_count()
        user_ids = {item.created_by for item in items}
        name_map = get_admin_user_name_map(user_ids)
        for row in items:
            tag_count = tag_count_mapping.get(row.id) or 0
            email = name_map.get(row.created_by) or '-'
            row_dict = row.to_dict(enum_to_name=True)
            row_dict['created_user_email'] = email
            row_dict['tag_count'] = tag_count
            ret.append(row_dict)

        return dict(
            total=records.total,
            items=ret,
        )

    @classmethod
    def get_query_by(cls, params):
        model = UserTagCategory
        query = model.query.filter(
            model.status == model.Status.PASSED
        ).order_by(model.id.desc())
        return query

    @classmethod
    def _get_tag_count(cls):
        model = UserTagInfo
        rows = model.query.with_entities(
            model.category_id,
            func.count('*').label('tag_count')
        ).filter(
            model.status == model.Status.PASSED
        ).group_by(
            model.category_id
        ).all()
        return {row.category_id: row.tag_count for row in rows}


@ns.route('/category/<int:id_>')
@respond_with_code
class UserTagCategoryDetailResource(Resource):
    model = UserTagCategory

    @classmethod
    def get(cls, id_):
        """运营-用户标签-类别管理-详情"""
        row = cls._get_row(id_)
        return row.to_dict(enum_to_name=True)

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-用户标签-类别管理-编辑/创建"""

        params = Struct(**kwargs)
        params.id_ = id_

        if id_ == 0:
            cls._validate(params)
            row = cls.model(
                name=params.name,
                remark=params.remark,
                created_by=g.user.id
            )
            db.session.add(row)
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.UserTagCategory,
                detail=kwargs,
            )
        else:
            cls._validate(params, True)
            row = cls._get_row(id_)
            old_data = row.to_dict(enum_to_name=True)
            row.name = params.name
            row.remark = params.remark
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.UserTagCategory,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )
        db.session.commit()
        return row.to_dict(enum_to_name=True)

    @classmethod
    def _validate(cls, params, edit=False):
        model = cls.model
        query = model.query.filter(
            model.name == params.name
        )
        if edit:
            query = query.filter(model.id != params.id_)
        row = query.first()
        if row:
            raise InvalidArgument(message=f'{params.name}类别名称已存在')

    @classmethod
    def delete(cls, id_):
        """运营-用户标签-类别管理-删除"""

        row = cls._get_row(id_)
        row.status = cls.model.Status.DELETED
        db.session.commit()
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.UserTagCategory,
            detail=dict(id=id_, name=row.name),
        )
        return {}

    @classmethod
    def _get_row(cls, id_):
        model = cls.model
        row = model.query.get(id_)
        if not row:
            raise InvalidArgument
        if row.status is model.Status.DELETED:
            raise InvalidArgument(message=f'{id_}标签类别已被删除')
        return row


def fetch_user_emails(user_ids: set) -> dict:
    user_emails = User.query.with_entities(
        User.id,
        User.name,
    ).filter(User.id.in_(user_ids)).all()
    return dict(user_emails)


@ns.route('/group-list')
@respond_with_code
class UserTagGroupListResource(Resource):
    model = UserTagGroup

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String,
        group_type=EnumField(model.GroupType),
        keyword=fields.String,
        begin_at=TimestampField(),
        end_at=TimestampField(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-用户标签-分群列表"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)
        records = query.paginate(params.page, params.limit, error_out=False)
        items = records.items
        ret = []
        created_users = {item.created_by for item in items if item.created_by}
        name_map = get_admin_user_name_map(created_users)
        for row in items:
            row_dict = row.to_dict(enum_to_name=True)
            row_dict['created_user_email'] = name_map.get(row.created_by) or '-'
            row_dict.pop("user_ids", None)
            tag_names = []
            for d in TagReader.parse_rules(row.get_rules()):
                rules = d["rules"]
                for dd in rules:
                    if not dd:  # test 脏数据
                        continue
                    tag_names.append(dd['tag'].name)
            row_dict['tags'] = tag_names
            ret.append(row_dict)

        return dict(
            total=records.total,
            items=ret,
            extra=dict(
                group_types=cls.model.GroupType,
                calc_statuses=cls.model.CalcStatus,
            )
        )

    @classmethod
    def get_query_by(cls, params):
        model = cls.model
        query = model.query.filter(
            model.status == model.Status.PASSED
        ).order_by(model.id.desc())
        if params.name:
            query = query.filter(model.name.contains(params.name))
        if params.group_type:
            query = query.filter(model.group_type == params.group_type)
        if params.keyword:
            user_ids = User.search_for_users(params.keyword)
            query = query.filter(model.created_by.in_(user_ids))
        if params.begin_at:
            query = query.filter(model.created_at >= params.begin_at)
        if params.end_at:
            query = query.filter(model.created_at <= params.end_at)

        return query


@ns.route('/group/<int:id_>/user-download')
@respond_with_code
class UserTagGroupUserResource(Resource):
    model = UserTagGroup

    @classmethod
    def get(cls, id_):
        """运营-用户标签-分群用户下载"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        data = cls.get_data(row)
        fields = headers = ["id", "account_name"]
        stream = ExcelExporter(
            data_list=data,
            fields=fields,
            headers=headers
        ).export_streams()
        return send_file(
            stream,
            download_name=f'{row.id}-{row.name}.xlsx',
            as_attachment=True
        )

    @classmethod
    def get_data(cls, row) -> list:
        user_ids = row.get_user_ids()
        model = UserExtra
        objs = []
        for chunk_ids in batch_iter(user_ids, 2000):
            chunk_objs = model.query.with_entities(
                model.user_id,
                model.account_name,
            ).filter(
                model.user_id.in_(chunk_ids)
            ).all()
            objs.extend(chunk_objs)
        ret = []
        exists = set()
        for obj in objs:
            exists.add(obj.user_id)
            pending_obj = model(user_id=obj.user_id, account_name=obj.account_name)
            ret.append({
                'id': pending_obj.user_id,
                'account_name': pending_obj.display_account_name,
            })
        for uid in set(user_ids) - exists:
            pending_obj = model(user_id=uid)
            ret.append({
                'id': pending_obj.user_id,
                'account_name': pending_obj.display_account_name,
            })
        return ret


@ns.route('/group/<int:id_>')
@respond_with_code
class UserTagGroupDetailResource(Resource):
    model = UserTagGroup

    @classmethod
    def get(cls, id_):
        """运营-用户标签-分群详情"""
        if id_ == 0:
            return {'extra': cls._get_extra_data()}
        else:
            row = cls._get_row(id_)
            return cls._to_ret(row)

    @classmethod
    def post(cls, id_):
        """运营-用户标签-用户分群复制"""
        origin_data = UserTagGroup.query.get(id_)
        row = UserTagGroup(
            name=origin_data.name,
            group_type=origin_data.group_type,
            rules=origin_data.rules,
            user_ids=origin_data.user_ids,
            user_count=origin_data.user_count,
            last_updated_at=origin_data.last_updated_at,
            status=origin_data.status,
            calc_status=origin_data.calc_status,
            remark=origin_data.remark,
            created_by=g.user.id
        )
        db.session.add(row)
        db.session.flush()
        row.name = f"{row.name}_{row.id}"
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.UserTagGroup,
            detail=dict(
                id=row.id,
                name=row.name,
                group_type=row.group_type,
                rules=row.rules,
                remark=row.remark,
            ),
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        group_type=EnumField(model.GroupType, required=True),
        rules=fields.List(fields.Raw()),
        import_users=fields.String,
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-用户标签-分群编辑/创建"""

        params = Struct(**kwargs)
        cls._validate(params)
        rules = params.rules
        if rules:
            db_rules = TagReader.get_rules_from_component(rules)
            db_rules = json.dumps(db_rules)
        else:
            db_rules = ''
        if id_ == 0:
            row = cls.model(
                name=params.name,
                group_type=params.group_type,
                rules=db_rules,
                remark=params.remark,
                created_by=g.user.id
            )
            db.session.add(row)
            db.session.commit()

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.UserTagGroup,
                detail=dict(
                    id=row.id,
                    name=row.name,
                    group_type=row.group_type,
                    rules=row.rules,
                    remark=row.remark,
                ),
            )
        else:
            row = cls._get_row(id_)
            old_data = row.to_dict(enum_to_name=True)
            row.name = params.name
            row.group_type = params.group_type
            row.rules = db_rules
            row.remark = params.remark

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.UserTagGroup,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )

        if row.group_type is cls.model.GroupType.IMPORT:
            user_ids = [int(user_id) for user_id in params.import_users.split(',')]
            row.set_user_ids(user_ids)
            row.user_count = len(user_ids)
            row.calc_status = cls.model.CalcStatus.FINISHED
            row.last_updated_at = now()
        if row.group_type is cls.model.GroupType.RULE:
            row.calc_status = cls.model.CalcStatus.CREATED
            row.set_user_ids([])
            user_tag_group_statistic.delay(row.id)
            # user_tag_group_statistic(row.id)
        db.session.commit()
        return cls._to_ret(row)

    @classmethod
    def _to_ret(cls, row):
        ret = row.to_dict(enum_to_name=True)
        ret['user_ids'] = row.get_user_ids()
        ret['user_ids'] = ','.join([str(uid) for uid in ret['user_ids']])
        if row.group_type is cls.model.GroupType.RULE:
            ret['rules'] = TagReader.build_component_rules(json.loads(ret["rules"])) if ret["rules"] else []
        ret['extra'] = cls._get_extra_data()
        return ret

    @classmethod
    def _get_extra_data(cls):
        extra = dict(
            group_types=cls.model.GroupType,
            calc_statuses=cls.model.CalcStatus,
            op_types=OperatorType,
            user_tags=UserTag,
        )
        categories = UserTagCategory.query.with_entities(
            UserTagCategory.id,
            UserTagCategory.name,
        ).filter(
            UserTagCategory.status == UserTagCategory.Status.PASSED
        ).all()
        categories = {obj.id: obj for obj in categories}

        tags = UserTagInfo.query.with_entities(
            UserTagInfo.category_id,
            UserTagInfo.tag,
        ).filter(
            UserTagInfo.status == UserTagInfo.Status.PASSED
        ).all()
        not_supported_tags = get_not_supported_tags()
        cate_to_tag = {}
        for tag in tags:
            # 不返回不支持的tag
            if tag.tag in not_supported_tags:
                continue
            tag_prop = TagReader.get_tag_property(tag.tag)
            if not tag_prop:
                continue
            allow_types = tag_prop.get_allow_types()
            if AllowType.GROUP not in allow_types:
                continue
            cate = categories.get(tag.category_id)
            tag_info = dict(
                id=tag.tag.name,
                label=tag.tag.value,
                disabled=True,
                value_type=tag_prop.raw_type.name,
                ops=[_op.value for _op in tag_prop.get_supported_operations()],
                options=tag_prop.get_options()
            )
            cate_to_tag.setdefault(cate, []).append(tag_info)
        items = []
        for cate, children in cate_to_tag.items():
            items.append(dict(
                id=cate.id if cate else 0,
                label=cate.name if cate else '-',
                disabled=True,
                children=children
            ))

        extra.update(dict(tag_data=items))
        extra.update(dict(tag_props=TagReader.get_tag_property_mapping()))
        return extra

    @classmethod
    def _validate(cls, params):
        if params.group_type is cls.model.GroupType.RULE:
            if not params.rules:
                raise InvalidArgument(message='规则创建下，条件不能为空')

            params.import_users = ''
            TagReader.validate_group_rules(params.rules)
        else:
            if not params.import_users:
                raise InvalidArgument(message='导入创建下，用户不能为空')
            params.rules = None

    @classmethod
    def delete(cls, id_):
        """运营-用户标签-分群删除"""

        row = cls._get_row(id_)
        row.status = cls.model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.UserTagGroup,
            detail=dict(id=id_, name=row.name),
        )
        return {}

    @classmethod
    def _get_row(cls, id_):
        model = cls.model
        row = model.query.get(id_)
        if not row:
            raise InvalidArgument
        if row.status is model.Status.DELETED:
            raise InvalidArgument(message=f'{id_}已被删除')
        return row


@ns.route('/group/<int:id_>/refreshed')
@respond_with_code
class UserTagGroupRefreshResource(Resource):
    model = UserTagGroup

    @classmethod
    def patch(cls, id_):
        """运营-用户标签-分群用户更新"""
        row = cls._get_row(id_)
        if row.group_type is cls.model.GroupType.RULE:
            row.calc_status = cls.model.CalcStatus.CREATED
            row.set_user_ids([])
            db.session.commit()
            user_tag_group_statistic.delay(row.id)
        return {}

    @classmethod
    def _get_row(cls, id_):
        model = cls.model
        row = model.query.get(id_)
        if not row:
            raise InvalidArgument
        if row.status is model.Status.DELETED:
            raise InvalidArgument(message=f'{id_}已被删除')
        return row


@ns.route('/group/batch-template')
@respond_with_code
class UserTagGroupBatchTemplateResource(Resource):

    @classmethod
    def get(cls):
        """运营-用户标签-用户分群-多分群导入模板下载"""
        headers = ["分群名称", "id", "email"]
        stream = ExcelExporter(
            data_list=[],
            headers=headers
        ).export_streams()
        return send_file(
            stream,
            download_name='多分群导入模板.xlsx',
            as_attachment=True
        )


@ns.route('/group/batch-upload')
@respond_with_code
class UserTagGroupBatchUploadResource(Resource):
    model = UserTagGroup

    @classmethod
    def post(cls):
        """运营-用户标签-用户分群-创建多条分群"""
        if not (file := request.files.get('file')):
            raise InvalidArgument
        rows = get_table_rows(file, ["分群名称", "id", "email"])
        if not rows:
            raise InvalidArgument(message="请填写有效ID或邮箱后再上传")
        ids = set()
        emails = set()
        for row in rows:
            if not row['分群名称'] or not str(row["分群名称"]).strip():
                raise InvalidArgument(message=f"{row}分群名不存在")
            if row["id"] and isinstance(row["id"], str) and row["id"].strip():
                if not row["id"].isdigit():
                    raise InvalidArgument(message=f"用户{row}不存在")
                ids.add(int(row["id"]))
            elif row["id"] and isinstance(row["id"], int):
                ids.add(row["id"])
            elif row["email"] and isinstance(row["email"], str) and row["email"].strip():
                emails.add(row["email"])
            else:
                raise InvalidArgument(message=f"用户{row}不存在")

        for _ids in batch_iter(ids, 2000):
            ret = User.query.filter(User.id.in_(_ids)).with_entities(User.id).all()
            ret = {x for x, in ret}
            if len(ret) != len(_ids):
                row = (set(_ids) - ret).pop()
                raise InvalidArgument(message=f"用户{row}不存在")

        email_id_mapper = {}
        for _emails in batch_iter(emails, 2000):
            _lower_emails = {_email.lower() for _email in _emails}
            result = User.query.filter(User.email.in_(_lower_emails)).with_entities(User.id, User.email).all()
            ret = {email.lower(): id_ for id_, email in result}
            if len(ret) != len(_lower_emails):
                row = (set(_lower_emails) - set(ret.keys())).pop()
                raise InvalidArgument(message=f"用户{row}不存在")
            email_id_mapper.update(ret)

        group_ids_mapper = defaultdict(set)
        for row in rows:
            group_name = str(row['分群名称']).strip()
            if row["id"] and isinstance(row["id"], str) and row["id"].strip():
                group_ids_mapper[group_name].add(int(row["id"]))
            elif row["id"] and isinstance(row["id"], int):
                group_ids_mapper[group_name].add(row["id"])
            elif row["email"] and isinstance(row["email"], str) and row["email"].strip():
                email = row["email"].lower()
                group_ids_mapper[group_name].add(email_id_mapper[email])

        new_groups = []
        for name, user_ids in group_ids_mapper.items():
            row = cls.model(
                name=name,
                group_type=cls.model.GroupType.IMPORT,
                created_by=g.user.id
            )
            row.set_user_ids(list(user_ids))
            row.user_count = len(user_ids)
            row.calc_status = cls.model.CalcStatus.FINISHED
            row.last_updated_at = now()
            new_groups.append(row)
        db.session.bulk_save_objects(new_groups)
        db.session.commit()

        for row in new_groups:
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.UserTagGroup,
                detail=dict(
                    id=row.id,
                    name=row.name,
                    group_type=row.group_type,
                    rules=row.rules,
                    remark=row.remark,
                ),
            )

        return dict(
            total=len(new_groups)
        )


@ns.route('/group-portraits')
@respond_with_code
class GroupPortraitsResource(Resource):
    model = UserTagGroupPortrait

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String,
        keyword=fields.String,
        begin_at=TimestampField(),
        end_at=TimestampField(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-用户标签-群体画像列表"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)
        records = query.paginate(params.page, params.limit)
        items = records.items
        ret = []
        created_users = {item.created_by for item in items}
        name_map = get_admin_user_name_map(created_users)
        for row in items:
            email = name_map.get(row.created_by) or '-'
            row_dict = row.to_dict(enum_to_name=True)
            row_dict['created_user_email'] = email
            row_dict['tag_ids'] = row.get_tag_ids()
            row_dict['tag_group_ids'] = row.get_tag_group_ids()
            row_dict['tag_count'] = len(row_dict['tag_ids'])
            row_dict['tag_group_count'] = len(row_dict['tag_group_ids'])
            ret.append(row_dict)

        return dict(
            total=records.total,
            items=ret,
            extra=dict(
                statuses=cls.model.Status,
            )
        )

    @classmethod
    def get_query_by(cls, params):
        model = cls.model
        query = model.query.filter(
            model.status == model.Status.PASSED
        ).order_by(model.id.desc())
        if params.name:
            query = query.filter(model.name.contains(params.name))
        if params.keyword:
            user_ids = User.search_for_users(params.keyword)
            query = query.filter(model.created_by.in_(user_ids))
        if params.begin_at:
            query = query.filter(model.created_at >= params.begin_at)
        if params.end_at:
            query = query.filter(model.created_at <= params.end_at)

        return query


@ns.route('/group-portraits/<int:id_>')
@respond_with_code
class GroupPortraitDetailResource(Resource):
    model = UserTagGroupPortrait

    @classmethod
    def get(cls, id_):
        """运营-用户标签-群体画像详情"""
        if id_ == 0:
            return {'extra': cls._get_extra_data()}
        else:
            row = cls._get_row(id_)
            return cls._to_ret(row)

    @classmethod
    def post(cls, id_):
        """运营-用户标签-群体画像复制"""
        origin_data = UserTagGroupPortrait.query.get(id_)
        row = UserTagGroupPortrait(
            name=origin_data.name,
            tag_ids=origin_data.tag_ids,
            tag_group_ids=origin_data.tag_group_ids,
            last_updated_at=origin_data.last_updated_at,
            remark=origin_data.remark,
            status=origin_data.status,
            created_by=g.user.id
        )
        db.session.add(row)
        db.session.flush()
        row.name = f"{row.name}_{row.id}"
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.UserTagGroupPortrait,
            detail=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        tag_ids=fields.List(fields.Integer(required=True), required=True),
        tag_group_ids=fields.List(fields.Integer(required=True), required=True),
        remark=fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """运营-用户标签-群体画像编辑/创建"""
        params = Struct(**kwargs)
        cls._validate(params)

        tag_group_ids = []
        for gid in params.tag_group_ids:
            if gid not in tag_group_ids:
                tag_group_ids.append(gid)
        if id_ == 0:
            row = cls.model(
                name=params.name,
                tag_ids=json.dumps(params.tag_ids),
                tag_group_ids=json.dumps(tag_group_ids),
                remark=params.remark,
                created_by=g.user.id
            )
            db.session.add(row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.UserTagGroupPortrait,
                detail=row.to_dict(enum_to_name=True),
            )
        else:
            row = cls._get_row(id_)
            old_data = row.to_dict(enum_to_name=True)
            row.name = params.name
            row.tag_ids = json.dumps(params.tag_ids)
            row.tag_group_ids = json.dumps(tag_group_ids)
            row.remark = params.remark

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.UserTagGroupPortrait,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )

        db.session.commit()
        return cls._to_ret(row)

    @classmethod
    def _to_ret(cls, row):
        ret = row.to_dict(enum_to_name=True)
        ret['tag_ids'] = row.get_tag_ids()
        ret['tag_group_ids'] = row.get_tag_group_ids()
        ret['extra'] = cls._get_extra_data(ret['tag_group_ids'])
        return ret

    @classmethod
    def _get_extra_data(cls, tag_group_ids=None):
        extra = dict(
            user_tags=UserTag,
            group_types=UserTagGroup.GroupType,
        )

        categories = UserTagCategory.query.with_entities(
            UserTagCategory.id,
            UserTagCategory.name,
        ).filter(
            UserTagCategory.status == UserTagCategory.Status.PASSED
        ).all()
        categories = {obj.id: obj.name for obj in categories}

        tags = UserTagInfo.query.with_entities(
            UserTagInfo.id,
            UserTagInfo.category_id,
            UserTagInfo.tag,
        ).filter(
            UserTagInfo.status == UserTagInfo.Status.PASSED
        ).all()
        tag_mapping = {}
        cate_to_tag = {}
        not_supported_tags = get_not_supported_tags()
        for tag in tags:
            tag_mapping[tag.id] = tag.tag.name
            if tag.tag in not_supported_tags:
                continue
            tag_prop = TagReader.get_tag_property(tag.tag)
            if not tag_prop:
                continue
            allow_types = tag_prop.get_allow_types()
            if AllowType.PORTRAIT not in allow_types:
                continue
            cate_name = categories.get(tag.category_id) or '-'
            tag_info = dict(
                id=tag.tag.name,
                label=tag.tag.value,
                disabled=True,
            )
            cate_to_tag.setdefault(cate_name, []).append(tag_info)

        items = []
        for cate, children in cate_to_tag.items():
            items.append(dict(
                id=cate,
                label=cate,
                disabled=True,
                children=children
            ))

        if tag_group_ids:
            groups = UserTagGroup.query.with_entities(
                UserTagGroup.id,
                UserTagGroup.name,
                UserTagGroup.user_count,
            ).filter(
                UserTagGroup.status == UserTagGroup.Status.PASSED,
                UserTagGroup.id.in_(tag_group_ids)
            ).all()
        else:
            groups = []
        tag_groups = [{'id': group.id, 'name': group.name, 'user_count': group.user_count} for group in groups]
        extra.update(tag_data=items)
        extra.update(tags=tag_mapping)
        extra.update(tag_groups=tag_groups)
        return extra

    @classmethod
    def _validate(cls, params):
        model1 = UserTagGroup
        rows1 = model1.query.filter(
            model1.id.in_(params.tag_group_ids),
            model1.status == model1.Status.PASSED
        ).all()
        if len(params.tag_group_ids) == 0:
            raise InvalidArgument(message='请选择一个用户分群')
        if len(params.tag_group_ids) > 1:
            raise InvalidArgument(message='当前仅支持选择一个用户分群')
        if len(rows1) != len(params.tag_group_ids):
            raise InvalidArgument(message='请检查用户分群是否存在')

        model2 = UserTagInfo
        rows2 = model2.query.filter(
            model2.id.in_(params.tag_ids),
            model2.status == model2.Status.PASSED
        ).all()
        if len(rows2) != len(params.tag_ids):
            raise InvalidArgument(message='请检查用户标签是否存在')

    @classmethod
    def delete(cls, id_):
        """运营-用户标签-群体画像删除"""

        row = cls._get_row(id_)
        row.status = cls.model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.UserTagGroupPortrait,
            detail=dict(id=id_, name=row.name),
        )
        return {}

    @classmethod
    def _get_row(cls, id_):
        model = cls.model
        row = model.query.get(id_)
        if not row:
            raise InvalidArgument
        if row.status is model.Status.DELETED:
            raise InvalidArgument(message=f'{id_}已被删除')
        return row


@ns.route('/group-portraits/<int:id_>/preview')
@respond_with_code
class GroupPortraitDataPreviewResource(GroupPortraitDataMixin, Resource):
    model = UserTagGroupPortrait

    @classmethod
    def get(cls, id_):
        """运营-用户标签-群体画像数据预览"""
        row = cls._get_row(id_)
        tag_ids = row.get_tag_ids()
        tags = list(cls.get_tags(tag_ids).values())
        tag_names = [tag.name for tag in tags]
        tag_group_ids = row.get_tag_group_ids()
        user_ids = cls._get_tag_group_users(tag_group_ids)
        user_data = TagReader.get_user_tag_data_by(tag_names, user_ids[:100])
        return {'items': cls._get_ret(user_ids[:100], user_data, tags), 'user_count': len(user_ids)}


@ns.route('/group-portraits/<int:id_>/downloads/sync-include-email')
@respond_with_code
class GroupPortraitDataDownloadResource(GroupPortraitDataMixin, Resource):
    model = UserTagGroupPortrait
    DOWNLOAD_LIMIT = 200000
    EXCLUDE_TAGS = []
    INCLUDE_TAGS = [UserTag.USER_EMAIL]

    @classmethod
    def get(cls, id_):
        """运营-用户标签-群体画像数据下载-普通下载（包含邮箱）"""
        row = cls._get_row(id_)
        tag_ids = row.get_tag_ids()
        tags = list(cls.get_tags(tag_ids).values())
        tags = [t for t in tags if t not in cls.EXCLUDE_TAGS]
        tags = set(tags) | set(cls.INCLUDE_TAGS)
        tags = list(tags)
        tag_names = [tag.name for tag in tags]
        tag_group_ids = row.get_tag_group_ids()
        user_ids = cls._get_tag_group_users(tag_group_ids)
        if len(user_ids) > cls.DOWNLOAD_LIMIT:
            raise InvalidArgument(message=f'大于 20W 用户数禁止下载（当前用户数：{len(user_ids)}）')
        user_data = TagReader.get_user_tag_data_by(tag_names, user_ids)
        return export_xlsx(
            filename=f'群体画像数据-{row.name}',
            data_list=cls._get_ret(user_ids, user_data, tags),
            export_headers=cls._get_export_header([tag.value for tag in tags]),
        )

    @classmethod
    def _get_export_header(cls, tag_names):
        header = (
            {"field": "id", Language.ZH_HANS_CN: "ID"},
        )
        tag_header = []
        for tag_name in tag_names:
            tag_header.append(
                {
                    "field": tag_name,
                    Language.ZH_HANS_CN: tag_name
                }
            )
        header += tuple(tag_header)
        return header


@ns.route('/group-portraits/<int:id_>/downloads/sync-exclude-email')
@respond_with_code
class GroupPortraitDataDownloadExcludeEmailResource(GroupPortraitDataDownloadResource):
    EXCLUDE_TAGS = [UserTag.USER_EMAIL]
    INCLUDE_TAGS = []

    @classmethod
    def get(cls, id_):
        """运营-用户标签-群体画像数据下载-普通下载（无邮箱）"""
        return super().get(id_)


@ns.route('/group-portraits/<int:id_>/downloads/async-include-email')
@respond_with_code
class GroupPortraitDataAsyncDownloadResource(Resource):
    model = UserTagGroupPortrait

    @classmethod
    def get(cls, id_):
        """运营-用户标签-群体画像数据下载-异步下载（包含邮箱）"""
        send_group_portrait_to_research_team.delay(id_, g.user.id, include_email=True)


@ns.route('/group-portraits/<int:id_>/downloads/async-exclude-email')
@respond_with_code
class GroupPortraitDataAsyncDownloadExcludeEmailResource(Resource):

    @classmethod
    def get(cls, id_):
        """运营-用户标签-群体画像数据下载-异步下载（无邮箱）"""
        send_group_portrait_to_research_team.delay(id_, g.user.id, include_email=False)
