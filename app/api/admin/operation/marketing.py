import imghdr
from typing import Optional

from flask import g, request
from webargs import fields

from app.api.common import (Namespace, Resource, respond_with_code)
from app.api.common.fields import (EnumField, TimestampField)
from app.models import MarketingBanner, MarketingBannerContent, db, Referral
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.config import config
from app.business import CacheLock, LockKeys
from app.business.auth import get_admin_user_name_map

from app.common import Language, language_cn_names, language_name_cn_names, IMAGE_INFO
from app.exceptions import (InvalidArgument, RecordNotFound, ServiceUnavailable)
from app.utils import AWSBucketPublic, now, new_file_key


ns = Namespace('Operation Marketing')


class MarketBannerMixin:

    @classmethod
    def validate(cls, params):
        if params['begin_at'] > params['end_at']:
            raise InvalidArgument(message='开始时间不能大于结束时间')
        if refer_code := params.get("refer_code"):
            ref_row = Referral.query.filter(Referral.code == refer_code).first()
            if not ref_row:
                raise InvalidArgument(message=f'邀请码{refer_code}不存在')


@ns.route('/banners')
@respond_with_code
class MarketBannersResource(MarketBannerMixin, Resource):
    model = MarketingBanner
    STATUSES = dict(
        pending='待上架',
        online='上架',
        offline='已下架'
    )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(list(STATUSES)),
        lang=EnumField(Language),
        type=EnumField(MarketingBanner.Type),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-营销Banner-获取banners列表"""
        page = kwargs['page']
        limit = kwargs['limit']

        _now = now()
        model = cls.model
        query = model.query.filter(model.status == model.Status.VALID)
        if status := kwargs.get('status'):
            if status == 'pending':
                query = query.filter(model.begin_at > _now)
            elif status == 'online':
                query = query.filter(model.begin_at <= _now, model.end_at > _now)
            else:
                query = query.filter(model.end_at <= _now)
        if lang := kwargs.get("lang"):
            query = query.filter(model.lang == lang)
        if type_ := kwargs.get("type"):
            query = query.filter(model.type == type_)

        records = query.order_by(model.sort_id.desc()).paginate(page, limit)
        user_ids = {i.updated_by for i in records.items if i.updated_by}
        user_name_map = get_admin_user_name_map(list(user_ids))

        items = []
        item: MarketingBanner
        for item in records.items:
            i_dict = item.to_dict(enum_to_name=True)
            if item.begin_at > _now:
                status = 'pending'
            elif item.end_at <= _now:
                status = 'offline'
            else:
                status = 'online'
            i_dict['status'] = status
            i_dict['updated_user_email'] = user_name_map.get(item.updated_by, "")
            items.append(i_dict)

        return dict(
            total=records.total,
            items=items,
            extra=dict(
                statuses=cls.STATUSES,
                type_dict=cls.model.Type,
                languages=language_name_cn_names(),
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        type=EnumField(MarketingBanner.Type, required=True),
        lang=EnumField(Language, required=False),
        begin_at=TimestampField(required=True),
        end_at=TimestampField(required=True),
        url=fields.String(missing=""),
        code=fields.String(required=True),
        refer_code=fields.String(missing=""),
    ))
    def post(cls, **kwargs):
        """运营-营销Banner-创建banner"""
        model = cls.model
        cls.validate(kwargs)

        lang = kwargs['lang']
        type_ = kwargs["type"]
        if type_ == cls.model.Type.DEFAULT:
            cls.check_default_banner()
            lang = None

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MarketBanner,
            detail=kwargs,
        )

        return db.session_add_and_commit(
            model(
                name=kwargs['name'],
                type=type_,
                begin_at=kwargs['begin_at'],
                end_at=kwargs['end_at'],
                lang=lang,
                url=kwargs['url'],
                code=kwargs['code'],
                refer_code=kwargs['refer_code'],
                updated_by=g.user.id,
                sort_id=model.new_sort_id(),
            )
        )

    @classmethod
    def check_default_banner(cls):
        _now = now()
        if cls.model.query.filter(
            cls.model.type == cls.model.Type.DEFAULT,
            cls.model.status == cls.model.Status.VALID,
            cls.model.begin_at <= _now,
            cls.model.end_at > _now,
        ).first():
            raise InvalidArgument(message="已存在默认的投放营销")


# noinspection PyUnresolvedReferences
@ns.route('/banners/<int:id_>')
@respond_with_code
class MarketBannerContentResource(MarketBannerMixin, Resource):
    model = MarketingBanner

    @classmethod
    def get(cls, id_):
        """运营-营销Banner-获取单条banner"""
        lang_names = language_cn_names()
        extra = dict(
            type_dict=cls.model.Type,
            languages={e.name: lang_names[e] for e in Language},
            site_url=config['SITE_URL'],
        )
        if not id_:
            return dict(
                extra=extra
            )

        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        result = row.to_dict(enum_to_name=True)
        result.update(
            extra=extra
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        lang=EnumField(Language, required=False),
        begin_at=TimestampField(required=True),
        end_at=TimestampField(required=True),
        url=fields.String(missing=""),
        refer_code=fields.String(missing=""),
    ))
    def patch(cls, id_, **kwargs):
        """运营-营销Banner-编辑banner"""
        cls.validate(kwargs)
        row: cls.model = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        row.name = kwargs['name']
        row.begin_at = kwargs['begin_at']
        row.end_at = kwargs['end_at']
        if row.type != cls.model.Type.DEFAULT:
            row.lang = kwargs['lang']
        row.url = kwargs['url']
        row.refer_code = kwargs['refer_code']
        row.updated_at = now()
        row.updated_by = g.user.id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MarketBanner,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return row

    @classmethod
    @ns.use_kwargs(dict(
        is_offline=fields.Boolean(missing=False),  # 是否下架
    ))
    def delete(cls, id_, **kwargs):
        """运营-营销Banner-删除、下架"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        if kwargs.get("is_offline"):
            row.end_at = now()
            AdminOperationLog.new_stop(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.MarketBanner,
                detail=dict(id=id_, name=row.name),
            )
        else:
            row.status = cls.model.Status.DELETED
            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.MarketBanner,
                detail=dict(id=id_, name=row.name),
            )
        db.session_add_and_commit(row)

        return row


# noinspection PyUnresolvedReferences
@ns.route('/banners/<int:id_>/sort_id')
@respond_with_code
class MarketBannerSortIDResource(Resource):
    model = MarketingBanner

    @classmethod
    def post(cls, id_):
        """运营-营销Banner-banners上移"""
        with CacheLock(LockKeys.edit_operation_sort("market_banner"), wait=False):
            model = cls.model
            row = model.query.get(id_)
            if row is None:
                raise RecordNotFound
            old_data = row.to_dict(enum_to_name=True)
            other = model.query.filter(
                model.status == model.Status.VALID,
                model.sort_id > row.sort_id
            ).order_by(model.sort_id.asc()).first()
            if other is not None:
                row.sort_id, other.sort_id = other.sort_id, row.sort_id
                row.updated_at = other.updated_at = now()
                row.updated_by = other.updated_by = g.user.id
                db.session.commit()
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.MarketBanner,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )
            return {}


# noinspection PyUnresolvedReferences
@ns.route('/banners/<int:id_>/langs/<lang>')
@respond_with_code
class MarketBannerContentLangResource(Resource):
    model = MarketingBannerContent

    @classmethod
    def get(cls, id_, lang):
        """运营-营销Banner-获取单条banner内容"""
        row = cls.get_row(id_, lang)
        if row is None:
            raise RecordNotFound
        return dict(
            img_src=row.img_src if row is not None else ''
        )

    @classmethod
    def get_row(cls, id_, lang) -> Optional[MarketingBannerContent]:
        if not isinstance((lang := getattr(Language, lang, '')), Language):
            raise InvalidArgument

        model = cls.model
        return model.query.filter(
            model.owner_id == id_,
            model.lang == lang
        ).first()


@ns.route('/banners/<int:id_>/langs/<lang>/img')
@respond_with_code
class MarketBannerContentImageResource(Resource):
    model = MarketingBannerContent

    @classmethod
    @ns.use_kwargs(dict(
        same_as=fields.String
    ))
    def post(cls, id_, lang, **kwargs):
        """运营-营销Banner-编辑banner内容"""
        if same_as := kwargs.get('same_as'):
            row = cls._get_row(id_, same_as)
            if row is None:
                raise RecordNotFound
            file_key = row.file_key

        else:
            img = request.files.get('img')
            if not img:
                raise InvalidArgument(message='image not provided')
            if (img_type := imghdr.what(img)) not in {'png', 'jpeg'}:
                raise InvalidArgument(message='unsupported image type')

            img_info = IMAGE_INFO[img_type]
            file_key = AWSBucketPublic.new_file_key(key=f'{new_file_key()}.{img_info.ext}')
            if not AWSBucketPublic.put_file(
                    file_key,
                    img,
                    ContentType=img_info.mime_type
            ):
                raise ServiceUnavailable

        row = cls._get_row(id_, lang)
        model = cls.model
        if row is None:
            row = db.session_add_and_commit(model(
                owner_id=id_,
                lang=getattr(Language, lang),
                file_key=file_key
            ))

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.MarketBannerContent,
                detail=dict(owner_id=id_, lang=row.lang, file_key=file_key),
            )
        else:
            old_value = row.file_key
            row.file_key = file_key
            row.updated_at = now()
            db.session.commit()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.MarketBannerContent,
                old_data=dict(file_key=old_value),
                new_data=dict(file_key=file_key),
                special_data=dict(owner_id=id_, lang=row.lang),
            )

        return dict(
            img_src=row.img_src
        )

    @classmethod
    def delete(cls, id_, lang):
        """运营-营销Banner-删除banner"""
        row = cls._get_row(id_, lang)
        if row is None:
            raise RecordNotFound
        row.file_key = ''
        row.updated_at = now()
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MarketBannerContent,
            detail=dict(owner_id=id_, lang=row.lang),
        )
        return {}

    @classmethod
    def _get_row(cls, id_, lang):
        row = MarketBannerContentLangResource.get_row(id_, lang)
        return row


@ns.route('/banners/sorts')
@respond_with_code
class MarketBannerSortResource(Resource):
    model = MarketingBanner

    @classmethod
    @ns.use_kwargs(
        dict(
            old_data=fields.Raw(required=True),
            old_index=fields.Integer(required=True),
            new_index=fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """运营-营销Banner-banner拖拽排序"""
        model = cls.model
        with CacheLock(LockKeys.edit_operation_sort("banner"), wait=False):
            old_data = kwargs['old_data']
            old_index = kwargs['old_index']
            new_index = kwargs['new_index']
            edit_banner = model.query.get(old_data[old_index]["id"])
            swap_banner = model.query.get(old_data[new_index]["id"])
            if not edit_banner or not swap_banner:
                raise RecordNotFound
            swap_banner_sort_id = swap_banner.sort_id
            edit_banner_sort_id = edit_banner.sort_id
            if edit_banner_sort_id > swap_banner_sort_id:
                # 表示进行下移
                model.query.filter(
                    model.sort_id >= swap_banner_sort_id,
                    model.sort_id < edit_banner_sort_id
                ).update(
                    {model.sort_id: model.sort_id + 1},
                    synchronize_session=False
                )
                edit_banner.sort_id = swap_banner_sort_id
                db.session.commit()
            else:
                # 表示进行上移
                model.query.filter(
                    model.sort_id > edit_banner_sort_id,
                    model.sort_id <= swap_banner_sort_id
                ).update(
                    {model.sort_id: model.sort_id - 1},
                    synchronize_session=False
                )
                edit_banner.sort_id = swap_banner_sort_id
                db.session.commit()
            return {}
