# -*- coding: utf-8 -*-

from collections import defaultdict
from decimal import Decimal

from flask import g
from webargs import fields
from sqlalchemy import func
from sqlalchemy.orm import aliased

from ...models import db, SubAccount, User, SubAccountLimitRecord, \
    SubAccountManagerRelation, MarginLoanOrder, ApiAuth, UserSpecialConfigChangeLog
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from ...exceptions import InvalidArgument
from ...business import ServerClient, PerpetualServerClient, PriceManager, \
    get_special_conf_create_operators
from ...business.amm import get_user_amm_assets
from ...business.sub_account import get_sub_account_balance_map
from ...caches import SubAccountInfoCache, PerpetualMarketCache, ApiAuthCache, SubAccountBalanceCache
from ..common import Namespace, Resource, respond_with_code
from ..common.fields import <PERSON>umField, PageField, LimitField
from ...utils import quantize_amount, amount_to_str


ns = Namespace('Sub Accounts')


@ns.route('')
@respond_with_code
class SubAccountsResource(Resource):

    MainUser = aliased(User)

    STATUSES = {
        SubAccount.Status.VALID.name: '正常',
        SubAccount.Status.FROZEN.name: '已冻结',
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            main_user_id=fields.Integer,
            manage_user_id=fields.Integer,
            status=EnumField(SubAccount.Status),
            type=EnumField(SubAccount.Type),
            is_visible=fields.Boolean,
            page=PageField(unlimited=True),
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """用户-子账户列表"""
        mu_model = cls.MainUser
        query = SubAccount.query.join(mu_model, mu_model.id == SubAccount.main_user_id).join(
            User,
            User.id == SubAccount.user_id,
        )

        if main_user_id := kwargs.get("main_user_id"):
            query = query.filter(SubAccount.main_user_id == main_user_id)
        if manage_user_id := kwargs.get("manage_user_id"):
            relations = SubAccountManagerRelation.query.filter(
                SubAccountManagerRelation.manager_id == manage_user_id,
                SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
            ).all()
            sub_user_ids = [i.user_id for i in relations]
            query = query.filter(SubAccount.user_id.in_(sub_user_ids))
        if (status := kwargs.get('status')) is not None:
            query = query.filter(SubAccount.status == status)
        if (type_ := kwargs.get('type')) is not None:
            query = query.filter(SubAccount.type == type_)
        if (is_visible := kwargs.get('is_visible')) is not None:
            is_visible = bool(is_visible)
            query = query.filter(SubAccount.is_visible.is_(is_visible))

        query = query.order_by(SubAccount.id.desc()).with_entities(
            SubAccount.id,
            SubAccount.created_at,
            SubAccount.user_id,
            SubAccount.main_user_id,
            SubAccount.is_visible,
            SubAccount.permissions,
            SubAccount.type,
            SubAccount.status,
            SubAccount.admin_remark,
            SubAccount.remark,
            User.name,
            User.totp_auth_key,
            User.mobile_num,
        )

        page, limit = kwargs["page"], kwargs["limit"]
        if not main_user_id and not manage_user_id:
            records = query.paginate(page, limit)
            records_items = records.items
            total = records.total
        else:
            # 选择主账号或托管账号时按照总资产市值降序排列
            records_all = query.all()
            total = len(records_all)
            sub_account_map = {}
            all_sub_balance_map = {}
            all_main_user_ids = set()
            for item in records_all:
                sub_account_map[item.user_id] = item
                all_sub_balance_map[item.user_id] = Decimal()
                all_main_user_ids.add(item.main_user_id)
            for mid in all_main_user_ids:
                sub_balance_map, _ = SubAccountBalanceCache(mid).get_balance_map_and_ts()
                for sid, balance_usd in sub_balance_map.items():
                    if sid in sub_account_map:
                        all_sub_balance_map[sid] = balance_usd
            start, end = (page - 1) * limit, page * limit
            sub_account_user_ids = [
                user_id for user_id, _ in sorted(all_sub_balance_map.items(), key=lambda x: x[1], reverse=True)
            ][start: end]
            records_items = [sub_account_map[sid] for sid in sub_account_user_ids]

        sub_ids = set()
        main_sub_ids_map = defaultdict(list)
        for r in records_items:
            main_sub_ids_map[r.main_user_id].append(r.user_id)
            sub_ids.add(r.user_id)
        balance_map = {}
        for mid, sids in main_sub_ids_map.items():
            balance_map.update(get_sub_account_balance_map(mid, list(sids)))

        manage_relations = SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.user_id.in_(list(sub_ids)),
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).all()
        sub_manager_ids_map = defaultdict(list)
        for r in manage_relations:
            sub_manager_ids_map[r.user_id].append(r.manager_id)

        items = []
        for id_, ca, uid, mid, iv, pm, tp, st, arm, rm, un, totp, mob in records_items:
            items.append(
                dict(
                    id=id_,
                    created_at=ca,
                    user_id=uid,
                    main_user_id=mid,
                    is_visible=iv,
                    permissions=pm,
                    type=tp.name,
                    status=st.name,
                    admin_remark=arm,
                    remark=rm,
                    name=un,
                    has_2fa=bool(totp or mob),
                    balance_usd=balance_map.get(uid, Decimal()),
                    maanger_ids=sub_manager_ids_map[uid],
                )
            )

        return dict(
            items=items,
            total=total,
            extra=dict(
                statuses=cls.STATUSES,
                type_dict={
                    SubAccount.Type.NORMAL.name: "NORMAL",
                    SubAccount.Type.STRATEGY.name: "策略子账号",
                    SubAccount.Type.COPY_TRADER.name: "带单人子账号",
                    SubAccount.Type.COPY_FOLLOWER.name: "跟单人子账号",
                },
            )
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            sub_account_id=fields.Integer,
            remark=fields.String
        )
    )
    def put(cls, **kwargs):
        """用户-子账户-编辑"""
        account = SubAccount.query.get(kwargs['sub_account_id'])
        old_data = account.to_dict(enum_to_name=True)
        if (remark := kwargs.get('remark')) is not None:
            account.admin_remark = remark

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SubAccount,
                old_data=old_data,
                new_data=account.to_dict(enum_to_name=True),
                special_data=dict(sub_account_user_id=account.user_id, main_user_id=account.main_user_id),
                target_user_id=account.main_user_id,
            )
        db.session.commit()


@ns.route("/limit-config")
@respond_with_code
class SubAccountLimitResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            page=PageField(unlimited=True),
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 用户-特殊配置-子账号上限配置列表 """
        model = SubAccountLimitRecord
        query = model.query.filter(model.status == model.Status.VALID)
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        records = query.order_by(model.id.desc()).paginate(kwargs["page"], kwargs["limit"])
        record_ids = [i.id for i in records.items]
        operator_id_dict, operator_name_dict = get_special_conf_create_operators(
            record_ids, UserSpecialConfigChangeLog.SpecialConfigType.SUB_ACCOUNT_LIMIT)
        items = []
        for row in records.items:
            user = row.user
            item = row.to_dict()
            item["user_name"] = user.communication_name
            item.update(
                operator=operator_name_dict.get(row.id),
                operator_id=operator_id_dict.get(row.id)
            )
            items.append(item)

        return dict(
            items=items,
            total=records.total,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            limit_num=fields.Integer(required=True),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-子账号上限配置-新增 """
        user_id = kwargs["user_id"]
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(message=f"用户{user_id}不存在")
        if user.sub_account_ref:
            raise InvalidArgument(message=f"用户{user_id}是子账号")
        limit_num = kwargs["limit_num"]
        if limit_num > 1000:
            raise InvalidArgument(message=f"用户{user_id}子账号上限配置不能超过1000")

        model = SubAccountLimitRecord
        row: model = model.get_or_create(user_id=user_id)
        row.status = model.Status.VALID
        row.limit_num = limit_num
        row.remark = kwargs["remark"]
        db.session.add(row)
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.SUB_ACCOUNT_LIMIT,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail=f'子账户上限{limit_num}个',
            change_remark=kwargs['remark'],
            op_id=row.id
        )

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SubAccountLimit,
            detail=kwargs,
            target_user_id=user_id,
        )
        return row

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            limit_num=fields.Integer(required=True),
            remark=fields.String(missing=""),
        )
    )
    def put(cls, **kwargs):
        """ 用户-特殊配置-子账号上限配置-编辑 """
        model = SubAccountLimitRecord
        user_id = kwargs["user_id"]
        row: model = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
        ).first()
        if row is None:
            raise InvalidArgument(message=f"用户{user_id}子账号上限配置不存在")
        old_data = row.to_dict(enum_to_name=True)

        if (remark := kwargs.get("remark")) is not None:
            row.remark = remark
        if (limit_num := kwargs.get("limit_num")) is not None:
            if limit_num > 1000:
                raise InvalidArgument(message=f"用户{user_id}子账号上限配置不能超过1000")
            if limit_num > row.limit_num:
                # 改大的数据需包含已创建的子账号数
                cur_sub_num = SubAccount.query.filter(
                    SubAccount.main_user_id == user_id,
                ).with_entities(func.count()).scalar() or 0
                if limit_num <= cur_sub_num:
                    raise InvalidArgument(message=f"改大上限配置：用户{user_id}当前已创建子账号数目为：{cur_sub_num}")

            row.limit_num = limit_num
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.SUB_ACCOUNT_LIMIT,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail=f'修改子账户上限位{limit_num}个',
            change_remark=kwargs.get("remark", ''),
            op_id=row.id
        )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SubAccountLimit,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )
        return row

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 用户-特殊配置-子账号上限配置-删除 """
        model = SubAccountLimitRecord
        user_id = kwargs["user_id"]
        row: model = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID,
        ).first()
        if row is not None:
            row.status = model.Status.DELETED
            db.session.commit()
            UserSpecialConfigChangeLog.add(
                user_id=user_id,
                config_type=UserSpecialConfigChangeLog.SpecialConfigType.SUB_ACCOUNT_LIMIT,
                op_type=UserSpecialConfigChangeLog.OpType.DELETE,
                admin_user_id=g.user.id,
                change_detail=f'删除子账户上限配置',
                op_id=row.id
            )

            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SubAccountLimit,
                detail=row.to_dict(enum_to_name=True),
                target_user_id=row.user_id,
            )
        return {}


@ns.route('/update-visible')
@respond_with_code
class SubAccountUpdateVisibleResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            sub_user_id=fields.Integer(required=True),
            is_visible=fields.Boolean(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 用户-子账户列表-更新是否禁用 """
        sub_user_id = kwargs["sub_user_id"]
        is_visible = kwargs["is_visible"]
        sub_acc: SubAccount = SubAccount.query.filter(
            SubAccount.user_id == sub_user_id
        ).first()
        if not sub_acc:
            raise InvalidArgument(f"子账号{sub_user_id} 不存在")
        if sub_acc.type == SubAccount.Type.STRATEGY:
            raise InvalidArgument(f"子账号{sub_user_id} 是策略子账号")
        old_data = sub_acc.to_dict(enum_to_name=True)

        if not is_visible:
            if sub_acc.status != SubAccount.Status.FROZEN:
                raise InvalidArgument(f"子账号{sub_user_id} 客户未冻结")
            if not sub_acc.is_visible:
                raise InvalidArgument(f"子账号{sub_user_id} 当前是禁用状态")

            cls.do_check_sub_acc(sub_acc)
            cls.do_check_sub_user(sub_user_id)
            cls.set_sub_acc_not_visible(sub_acc)
        else:
            if sub_acc.is_visible:
                raise InvalidArgument(f"子账号{sub_user_id} 当前不是禁用状态")
            cls.set_sub_acc_visible(sub_acc)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SubAccount,
            old_data=old_data,
            new_data=sub_acc.to_dict(enum_to_name=True),
            special_data=dict(sub_account_user_id=sub_acc.user_id, main_user_id=sub_acc.main_user_id),
            target_user_id=sub_acc.main_user_id,
        )

    @classmethod
    def do_check_sub_acc(cls, sub_acc: SubAccount):
        """ 子账号相关检查 """
        sub_user_id = sub_acc.user_id
        relation_rows = SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.user_id == sub_user_id,
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).first()
        if relation_rows:
            raise InvalidArgument(f"子账号{sub_user_id}还有托管关系")

    @classmethod
    def do_check_sub_user(cls, sub_user_id: int):
        """ 子账号的用户的相关检查 """
        margin_loan = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id == sub_user_id,
            MarginLoanOrder.status.in_(
                [MarginLoanOrder.StatusType.PASS,
                 MarginLoanOrder.StatusType.ARREARS,
                 MarginLoanOrder.StatusType.BURST]),
            MarginLoanOrder.unflat_amount + MarginLoanOrder.interest_amount > 0
        ).first()
        if margin_loan:
            raise InvalidArgument(f"子账号{sub_user_id}还有杠杆借币订单")
        client = ServerClient()
        orders = client.user_pending_orders(sub_user_id, account_id=-1, limit=1, page=1)
        if orders:
            raise InvalidArgument(f"子账号{sub_user_id}还有现货委托订单")
        orders = client.user_pending_stop_orders(sub_user_id, account_id=-1, limit=1, page=1)
        if orders:
            raise InvalidArgument(f"子账号{sub_user_id}还有现货计划委托订单")
        perpetual_client = PerpetualServerClient()
        orders = perpetual_client.pending_order(sub_user_id, None, limit=1)['records']
        if orders:
            raise InvalidArgument(f"子账号{sub_user_id}还有合约托订单")
        orders = perpetual_client.pending_stop(sub_user_id, None, limit=1)['records']
        if orders:
            raise InvalidArgument(f"子账号{sub_user_id}还有合约计划托订单")
        positions = perpetual_client.position_pending(sub_user_id)
        if positions:
            raise InvalidArgument(f"子账号{sub_user_id}还有合约持仓")

        def assets_to_usd(_assets_) -> Decimal:
            usd_ = sum(asset_price_map.get(k, 0) * v for k, v in _assets_.items())
            return quantize_amount(usd_, 8)

        # balance
        asset_price_map = PriceManager.assets_to_usd()
        spot_assets = defaultdict(Decimal)  # 现货(币币+杠杆+理财)
        result = ServerClient().get_user_accounts_balances(sub_user_id)
        for _, assets in result.items():
            for asset, values in assets.items():
                spot_assets[asset] += values['available']
                spot_assets[asset] += values['frozen']
        spot_usd = assets_to_usd(spot_assets)

        per_assets = defaultdict(Decimal)
        result = perpetual_client.get_user_balances(sub_user_id)
        for asset, values in result.items():
            per_assets[asset] += values['available']
            per_assets[asset] += values['frozen']
            per_assets[asset] += values['margin']
        # 未实现盈亏
        for position in positions:
            asset = PerpetualMarketCache().get_balance_asset(position['market'])
            per_assets[asset] += Decimal(position['profit_unreal'])
        per_usd = assets_to_usd(per_assets)

        amm_assets = defaultdict(Decimal)
        result = get_user_amm_assets(sub_user_id)
        for asset, amount in result.items():
            amm_assets[asset] += amount
        amm_usd = assets_to_usd(amm_assets)
        if spot_usd + per_usd + amm_usd >= Decimal("1"):
            raise InvalidArgument(f"子账号{sub_user_id}资产市值需小于1U。当前现货资产：{amount_to_str(spot_usd, 2)}，"
                                  f"合约资产：{amount_to_str(per_usd, 2)}，AMM资产：{amount_to_str(amm_usd, 2)}")

    @classmethod
    def set_sub_acc_not_visible(cls, sub_acc: SubAccount):
        sub_user_id = sub_acc.user_id
        SubAccountInfoCache(sub_user_id).delete()

        del_api_access_ids = []
        sub_apis = ApiAuth.query.filter(
            ApiAuth.user_id == sub_user_id,
            ApiAuth.status == ApiAuth.Status.VALID,
        ).all()
        for _api_row in sub_apis:
            _api_row.status = ApiAuth.Status.DELETED
            del_api_access_ids.append(_api_row.access_id)

        relation_rows = SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.user_id == sub_user_id,
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).all()
        for rel_row in relation_rows:
            rel_row.status = SubAccountManagerRelation.Status.DELETED

        sub_acc.is_visible = False
        db.session.commit()

        SubAccountInfoCache(sub_user_id).delete()
        for _access_id in del_api_access_ids:
            ApiAuthCache(_access_id).delete()

    @classmethod
    def set_sub_acc_visible(cls, sub_acc: SubAccount):
        sub_user_id = sub_acc.user_id
        sub_acc.is_visible = True
        db.session.commit()
        SubAccountInfoCache(sub_user_id).delete()
