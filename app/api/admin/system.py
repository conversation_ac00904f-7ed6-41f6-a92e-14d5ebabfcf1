# -*- coding: utf-8 -*-
import datetime
from enum import Enum
import time
import json
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from functools import wraps
from typing import Union

import requests
from flask import g
from flask_babel import gettext, force_locale
from webargs import fields
from sqlalchemy import text

from app.caches.flow_control import SmsCountryResultCache
from app.common.countries import get_code_to_cn_name, list_country_codes_3
from app.utils.mobile import country_to_mobile_country_code

from ..common import (
    Resource, Namespace, respond_with_code,
)
from ..common.fields import PageField, LimitField, TimestampField, AssetField, EnumField
from ... import Language
from ...assets import list_all_assets
from ...business import SiteSettings, CountrySettings, ServerClient, PerpetualServerClient, \
    mem_cached
from ...business.prop_trading import PropTradingBusiness, update_prop_config_task
from ...business.auth import get_admin_user_name_map
from ...business.operation import close_protect_duration
from ...business.site import BusinessSettings
from ...business.strategy.grid import batch_pause_grid_strategy_by_market, batch_restart_grid_strategy_by_market
from ...caches import PerpetualMarketCache
from ...schedules.p2p.order import batch_cancel_not_confirmed_order_task
from ...caches import CountrySmsSettingCache, MarketCache
from ...caches.operation import NotificationBarCache, TipBarCache
from ...caches.system import IpWhiteListCache, SystemMaintainManagerCache, TempMaintainCache, MarketMaintainCache
from ...common import list_country_codes_3_admin, get_country, search_for_countries, \
    language_cn_names, LANGUAGE_NAMES, language_name_cn_names
from ...exceptions import InvalidArgument, RecordNotFound
from ...models import (
    CountrySmsSetting, AssetPrice, db, DailyUserReport, DailySiteFiatOrderReport,
    DailyPerpetualTradeReport, DailySpotTradeReport, TempMaintain,
    User, NotificationBar, NotificationBarContent, Market, TipBar, TipBarContent,
)
from ...models.system import LocalAreasBlock, LocalAreasBlockContent, MarketMaintain
from ...models.system import TempMaintainContent, AppJumpList, KlineBoostSetting
from ...models.prop_trading import DailyPropTradingReport, PropTradingSlice, PropTradingConfig, \
    PropTradingUser, MonthlyPropTradingReport, PropTradingStatAggregation
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, \
    OPNamespaceObjectOperation, OPNamespaceObjectSystem
from ...utils import SMSProvider, validate_ipv4_address, today, now, AWSBucketPublic, \
    amount_to_str, quantize_amount
from ...utils.date_ import convert_datetime, datetime_to_str

ns = Namespace('System')


@ns.route('/ping')
@respond_with_code
class PingResource(Resource):

    @classmethod
    def get(cls):
        """心跳"""
        return 'pong'


# noinspection PyUnresolvedReferences
@ns.route('/site-settings')
@respond_with_code
class SiteSettingsResource(Resource):

    @classmethod
    def get(cls):
        """系统-站点设置列表"""
        return SiteSettings.fields_and_values_json


# noinspection PyUnresolvedReferences
@ns.route('/site-settings/<field>')
@respond_with_code
class SiteSettingsManagementResource(Resource):

    @classmethod
    def get(cls, field):
        """系统-站点设置详情"""
        try:
            SiteSettings.get_field_and_value_json(field)
        except AttributeError:
            raise InvalidArgument(message=f'field {field!r} does not exist')

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True)
    ))
    def put(cls, field, **kwargs):
        """系统-站点设置编辑"""
        value = kwargs['value']
        old_value = getattr(SiteSettings, field)
        try:
            setattr(SiteSettings, field, value)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')
        new_value = getattr(SiteSettings, field)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.SiteSettings,
            old_data={field: old_value},
            new_data={field: new_value},
        )

        cls.sync_spot_grid_strategies(field)
        cls.sync_p2p_trade_options(field)

        return dict(
            value=new_value
        )

    @classmethod
    def delete(cls, field):
        """系统-站点设置配置删除"""
        try:
            delattr(SiteSettings, field)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.SiteSettings,
            detail=dict(field=field),
        )

        cls.sync_spot_grid_strategies(field)
        cls.sync_p2p_trade_options(field)

        return dict(
            value=getattr(SiteSettings, field)
        )

    @classmethod
    def sync_p2p_trade_options(cls, field_name: str):
        if field_name not in ["p2p_trading_enabled"]:
            return
        if SiteSettings.get(field_name):
            return
        batch_cancel_not_confirmed_order_task.delay()

    @classmethod
    def sync_spot_grid_strategies(cls, field_name: str):
        """ 全站交易状态变化时，同步到现货网格 """
        if field_name not in ["trading_enabled", "spot_trading_enabled"]:
            return
        new_val = SiteSettings.get(field_name)
        if not new_val:
            # 关闭全站交易、关闭现货交易
            batch_pause_grid_strategy_by_market.delay("")
        else:
            batch_restart_grid_strategy_by_market.delay("")


@ns.route('/protect-duration-settings')
@respond_with_code
class ProtectDurationSettingsResource(Resource):

    @classmethod
    def get(cls):
        """系统-站点设置-保护期状态列表"""
        res = [
            {
                'name': 'spot_protect_duration_enabled',
                'default_value': False,
                'desc': '进入币币保护期',
                'editable': True,
                'meta': {},
                'type': bool.__name__,
                'valid_interval': None,
                'client': ServerClient
            },
            {
                'name': 'perpetual_protect_duration_enabled',
                'default_value': False,
                'desc': '进入合约保护期',
                'editable': True,
                'meta': {},
                'type': bool.__name__,
                'valid_interval': None,
                'client': PerpetualServerClient
            }
        ]
        for item in res:
            client = item.pop('client')
            try:
                status = client().get_protect_status()['status']
                item['value'] = status
            except Exception:  # server处于更新中状态，此时为非保护期
                item['value'] = False
        return res


@ns.route('/protect-duration-settings/<field>')
@respond_with_code
class ProtectDurationSettingResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True)
    ))
    def put(cls, field, **kwargs):
        """系统-保护期设置编辑"""
        value = bool(int(kwargs['value']))
        if field == 'spot_protect_duration_enabled':
            client = ServerClient()
        elif field == 'perpetual_protect_duration_enabled':
            client = PerpetualServerClient()
        else:
            raise InvalidArgument
        old_value = client.get_protect_status()['status']
        try:
            client.update_protect_duration(value)
        except Exception as e:
            raise InvalidArgument(message=f'server保护期接口调用失败，请检查server状态！error: {e}')
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.SiteSettings,
            old_data={field: old_value},
            new_data={field: value},
        )
        return dict(
            value=value
        )

    @classmethod
    def delete(cls, field):
        """系统-保护期配置删除"""
        if field == 'spot_protect_duration_enabled':
            client = ServerClient()
        elif field == 'perpetual_protect_duration_enabled':
            client = PerpetualServerClient()
        else:
            raise InvalidArgument
        client.update_protect_duration(False)
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.SiteSettings,
            detail=dict(field=field),
        )
        status = client.get_protect_status()['status']
        return dict(value=status)


# noinspection PyUnresolvedReferences
@ns.route('/business-settings')
@respond_with_code
class BusinessSettingsResource(Resource):

    @classmethod
    def get(cls):
        """系统-业务变量配置列表"""
        return BusinessSettings.fields_and_values_json


# noinspection PyUnresolvedReferences
@ns.route('/business-settings/<field>')
@respond_with_code
class BusinessSettingsManagementResource(Resource):

    @classmethod
    def get(cls, field):
        """系统-业务变量配置详情"""
        try:
            BusinessSettings.get_field_and_value_json(field)
        except AttributeError:
            raise InvalidArgument(message=f'field {field!r} does not exist')

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True)
    ))
    def put(cls, field, **kwargs):
        """系统-业务变量配置编辑"""
        value = kwargs['value']
        old_value = getattr(BusinessSettings, field)

        if field in ['margin_interest_split_percents', 'pledge_interest_split_percents']:
            value = [Decimal(str(i)) for i in value]
            if len(value) != 3:
                raise InvalidArgument(message=f'比例数据错误 分配个数不为3')
            for v in value:
                if not (Decimal(0) <= v <= Decimal(1)):
                    raise InvalidArgument(message=f'比例错误，范围不在0～1')
            if sum(value) != Decimal(1):
                raise InvalidArgument(message=f'保险基金的比例、理财收益的比例、平台收入的比例 之和不为1')

        try:
            setattr(BusinessSettings, field, value)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')
        new_value = getattr(BusinessSettings, field)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.BusinessSettings,
            old_data={field: old_value},
            new_data={field: new_value},
        )

        return dict(
            value=new_value
        )

    @classmethod
    def delete(cls, field):
        """系统-业务变量配置删除"""
        try:
            delattr(BusinessSettings, field)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.BusinessSettings,
            detail=dict(field=field),
        )

        return dict(
            value=getattr(BusinessSettings, field)
        )


@ns.route('/country-settings')
@respond_with_code
class CountrySettingsListResource(Resource):

    MESSAGE_MAP = {
        CountrySettings.kyc_required.name: '某个国家开启后，未KYC用户使用该国家IP访问时，只能进行KYC和提现操作，其他功能都会被限制。'
                                           '但是会受到业务变量设置中的"不强制KYC的Timezone-Offset"配置影响。'
    }

    @classmethod
    @ns.use_kwargs(dict(
        keyword=fields.String,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """系统-国家配置列表"""
        if keyword := kwargs.get('keyword'):
            countries = [c.iso_3 for c in search_for_countries(keyword)]
        else:
            countries = list(list_country_codes_3_admin())
        limit = kwargs['limit']
        offset = (kwargs['page'] - 1) * limit

        result = []
        for code in countries[offset:offset + limit]:
            settings = CountrySettings(code)
            # noinspection PyProtectedMember
            result.append(dict(
                country_info=get_country(code)._asdict(),
                settings=settings.values
            ))

        return dict(
            items=result,
            total=len(countries),
            fields=[dict(
                name=name,
                desc=field.desc,
                type=field.type.__name__,
                message=cls.MESSAGE_MAP.get(name) or ""
            ) for name, field in CountrySettings.fields().items()]
        )


# noinspection PyUnresolvedReferences
@ns.route('/country-settings/<code>/<field>')
@respond_with_code
class CountrySettingsFieldResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True)
    ))
    def put(cls, code, field, **kwargs):
        """系统-编辑国家配置"""
        if not get_country(code):
            raise InvalidArgument
        value = kwargs['value']
        settings = CountrySettings(code)
        old_value = getattr(settings, field)
        try:
            setattr(settings, field, value)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')
        new_value = getattr(settings, field)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.CountrySettings,
            old_data={field: old_value},
            new_data={field: new_value},
            special_data={'code': code},
        )

        return dict(
            value=new_value
        )


@ns.route('/country-sms-settings')
@respond_with_code
class CountrySmsSettingsListResource(Resource):

    @classmethod
    def get(cls):
        """系统-sms配置"""
        items = CountrySmsSetting.query \
            .order_by(CountrySmsSetting.country_code) \
            .all()
        sms_providers = [i.value for i in SMSProvider]
        unsucessful_provider_map = defaultdict(list)
        for p in SMSProvider:
            result = SmsCountryResultCache(p.name).hgetall()
            for item in items:
                fail_count = int(result.get(f'{item.country_code}:{SmsCountryResultCache.Type.FAIL}', 0))
                success_count = int(result.get(f'{item.country_code}:{SmsCountryResultCache.Type.SUCCESS}', 0))
                if fail_count > 0 and success_count == 0:
                    unsucessful_provider_map[item.country_code].append(p.value)
        return dict(
            items=items,
            support_sms_providers=sms_providers,
            unsucessful_provider_map=unsucessful_provider_map
        )


# noinspection PyUnresolvedReferences
@ns.route('/country-sms-settings/<int:country_code>')
@respond_with_code
class CountrySmsSettingsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        sms_providers=fields.String(required=True)
    ))
    def put(cls, country_code, **kwargs):
        """系统-sms配置修改"""
        sms_providers = kwargs['sms_providers']
        for provider in sms_providers.split(','):
            try:
                SMSProvider(provider)
            except ValueError:
                raise InvalidArgument(
                    message=f'invalid sms provider: {provider!r}')

        row: CountrySmsSetting = CountrySmsSetting.query \
            .filter(CountrySmsSetting.country_code == country_code) \
            .first()
        if row is None:
            old_value = ''
            row = CountrySmsSetting(
                country_code=country_code,
                sms_providers=sms_providers
            )
            db.session.add(row)
        else:
            old_value = row.sms_providers
            row.sms_providers = sms_providers
        db.session.commit()
        CountrySmsSettingCache(country_code).delete()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.CountrySMSSettings,
            old_data=dict(sms_providers=old_value),
            new_data=dict(sms_providers=sms_providers),
            special_data=dict(country_code=country_code),
        )

        return row

    @classmethod
    def delete(cls, country_code):
        """系统-sms配置删除"""
        row: CountrySmsSetting = CountrySmsSetting.query \
            .filter(CountrySmsSetting.country_code == country_code) \
            .first()
        if row is not None:
            db.session.delete(row)
            db.session.commit()
            CountrySmsSettingCache(country_code).delete()

            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectSystem.CountrySMSSettings,
                detail=dict(country_code=country_code),
            )

        return {}


@ns.route('/country-sms-status')
@respond_with_code
class CountrySmsStatusResource(Resource):

    class Status(Enum):
        NORMAL = '正常'
        ABNORMAL = '异常'

    @classmethod
    @ns.use_kwargs(
        dict(
            country=fields.String,
            provider=EnumField(SMSProvider, required=True),
            status=EnumField(Status),
            is_open=fields.Boolean
        )
    )
    def get(cls, **kwargs):
        """系统-短信渠道状态表"""
        all_countries = list_country_codes_3()
        if country := kwargs.get('country'):
            countries = [country, ]
        else:
            countries = all_countries    
        provider = kwargs['provider']
        SmsCountryResultCache(provider.name).hgetall()
        ret = []
        for country in countries:
            mobile_code = country_to_mobile_country_code(get_country(country).iso_2)
            result = SmsCountryResultCache(provider.name).hgetall()
            fail_count = int(result.get(f'{mobile_code}:{SmsCountryResultCache.Type.FAIL}', 0))
            success_count = int(result.get(f'{mobile_code}:{SmsCountryResultCache.Type.SUCCESS}', 0))
            sms_providers = CountrySmsSettingCache(mobile_code).sms_providers
            is_open = provider.value in sms_providers
            if kwargs.get('is_open') is not None and is_open != kwargs.get('is_open'):
                continue
            status = cls.Status.ABNORMAL if fail_count > 0 and success_count == 0 else cls.Status.NORMAL
            if kwargs.get('status') is not None and status != kwargs.get('status'):
                continue
            ret.append(dict(
                country_code=country,
                status=status.name,
                is_open=is_open,
            ))
        return dict(
            items=ret,
            countries=get_code_to_cn_name(),
            providers={p.name: p.value for p in SMSProvider},
            statuses={s.name: s.value for s in cls.Status}
        )


@ns.route('/asset-prices')
@respond_with_code
class AssetPricesResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=AssetField,
            start=TimestampField,
            end=TimestampField,
            page=PageField(unlimited=True),
            limit=LimitField
        )
    )
    def get(cls, **kwargs):
        """系统-汇率"""
        query = AssetPrice.query
        if asset := kwargs.get('asset'):
            query = query.filter(AssetPrice.asset == asset)
        if start := kwargs.get('start'):
            query = query.filter(AssetPrice.date >= start)
        if end := kwargs.get('end'):
            query = query.filter(AssetPrice.date < end)
        records = query \
            .order_by(AssetPrice.id.desc()) \
            .paginate(kwargs['page'], kwargs['limit'])
        return dict(
            items=records.items,
            total=records.total,
            assets=list_all_assets()
        )


@ns.route('/ip-whitelist')
@respond_with_code
class IpWhiteListResource(Resource):
    @classmethod
    def get(cls):
        """系统-IP白名单设置"""
        ips = IpWhiteListCache().get_ip_list()
        return dict(items=[{"ip": v} for v in ips])

    @classmethod
    @ns.use_kwargs(
        dict(
            ips=fields.String(required=True)
        )
    )
    def post(cls, **kwargs):
        """系统-IP白名单编辑"""
        ips = kwargs['ips']
        split_ips = [v.strip() for v in ips.split(";") if v.strip() != '']

        for v in split_ips:
            if not validate_ipv4_address(v):
                raise InvalidArgument(message=f"错误的ip格式: {v}")
        ips_str = ";".join(split_ips)
        old_value = ';'.join(IpWhiteListCache().get_ip_list())
        IpWhiteListCache().set(ips_str)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.IPWhitelist,
            old_data=dict(ip_whitelist=old_value),
            new_data=dict(ip_whitelist=ips_str),
        )


@ns.route('/dashboard')
@respond_with_code
class DashBoardIndex(Resource):

    @classmethod
    def get(cls):
        """dashboard首页"""
        delta_days = 90
        today_date = today()
        yesterday_date = today_date - timedelta(days=1)
        ninety_days_before = today_date - timedelta(days=delta_days + 1)

        user_query = DailyUserReport.query.filter(
            DailyUserReport.report_date >= ninety_days_before
        )

        fiat_query = DailySiteFiatOrderReport.query.filter(
            DailySiteFiatOrderReport.report_date >= ninety_days_before
        )
        spot_query = DailySpotTradeReport.query.filter(
            DailySpotTradeReport.report_date >= ninety_days_before
        )
        perpetual_query = DailyPerpetualTradeReport.query.filter(
            DailyPerpetualTradeReport.report_date >= ninety_days_before
        )

        f = lambda x: x.strftime("%Y-%m-%d")

        result = defaultdict(
            lambda: {f((today_date - timedelta(days=v + 1))): Decimal() for v in range(delta_days)}
        )
        init_result = lambda key, date_str: date_str in result[key]
        keys = [
            'increase_user',
            'asset_user',
            'active_user',
            'active_trade_user',
            'fiat_usd',
            'spot_trade_usd',
            'perpetual_trade_usd'
        ]
        for key in keys:
            init_result(key, f(yesterday_date))

        for _record in user_query:
            result['increase_user'][f(_record.report_date)] = _record.increase_user
            result['asset_user'][f(_record.report_date)] = _record.asset_user
            result['active_user'][f(_record.report_date)] = _record.active_user
            result['active_trade_user'][f(_record.report_date)] = _record.active_trade_user

        for _record in fiat_query:
            result['fiat_usd'][f(_record.report_date)] = _record.deal_usd

        for _record in spot_query:
            result['spot_trade_usd'][f(_record.report_date)] = _record.trade_usd

        for _record in perpetual_query:
            result['perpetual_trade_usd'][f(_record.report_date)] = _record.trade_usd

        final_result = {}
        for key, details in result.items():
            final_result[key] = sorted([[date_str, value] for date_str, value in details.items()],
                                       key=lambda x: x[0])

        return final_result


def update_temp_maintain_cache(_func):
    @wraps(_func)
    def dec(*args, **kwargs):
        res = _func(*args, **kwargs)
        TempMaintainCache().reload()
        return res

    return dec


class TempMaintainMixin:

    @classmethod
    def _check_maintain_scope_valid(cls, scope, rows, started_at, ended_at):
        can_exists_at_same_time = {
            TempMaintain.MaintainScope.SPOT, TempMaintain.MaintainScope.PERPETUAL}
        for row in rows:
            tmp = {scope, row.scope}
            if row.started_at <= started_at <= row.ended_at and tmp != can_exists_at_same_time:
                raise InvalidArgument(message="维护时间与已有消息重合，不支持新增。")
            if row.started_at <= ended_at <= row.ended_at and tmp != can_exists_at_same_time:
                raise InvalidArgument(message="维护时间与已有消息重合，不支持新增。")

    @classmethod
    def create_all_site_maintain(cls, started_at: int, ended_at: int, url: str, protect_duration: Union[int, None]):
        cache = SystemMaintainManagerCache()
        cls._create_all_site_maintain(cache, started_at, ended_at, url, protect_duration)

    @classmethod
    def _create_all_site_maintain(cls, cache, started_at, ended_at, url, protect_duration):
        data = dict(
            on=1,
            start_time=started_at,
            end_time=ended_at,
            url=url,
            protect_duration=protect_duration or 0
        )
        cache.hmset(data)

    @classmethod
    def edit_all_site_maintain(cls, old_scope, new_scope, started_at: int, ended_at: int, url: str,
                               protect_duration: Union[int, None]):
        cache = SystemMaintainManagerCache()
        if new_scope == TempMaintain.MaintainScope.ALL_SITE:
            cls._create_all_site_maintain(cache, started_at, ended_at, url, protect_duration)
        else:
            if old_scope == TempMaintain.MaintainScope.ALL_SITE:
                cls._delete_all_site_maintain(cache)

    @classmethod
    def _delete_all_site_maintain(cls, cache):
        data = dict(
            on=0,
            start_time=0,
            end_time=0,
            url='',
            protect_duration=0
        )
        cache.hmset(data)

    @classmethod
    def delete_all_site_maintain(cls):
        cache = SystemMaintainManagerCache()
        cls._delete_all_site_maintain(cache)

    @classmethod
    def auto_add_protect_duration_notification_bar(
            cls, item, scope, maintain_ended_at, protect_duration, jump_page_enabled, jump_url,
            user_id):
        if scope in (TempMaintain.MaintainScope.ALL, TempMaintain.MaintainScope.ALL_SITE):
            page_scope_infos = [
                {'page': [
                    {'trigger_page': NotificationBar.TriggerPage.SPOT_MARKET.name,
                     'trigger_page_params': ''
                     }],
                    'scope': TempMaintain.MaintainScope.SPOT
                },
                {'page': [
                    {'trigger_page': NotificationBar.TriggerPage.PERPETUAL_MARKET.name,
                     'trigger_page_params': ''
                     }],
                    'scope': TempMaintain.MaintainScope.PERPETUAL}
            ]
        elif scope == TempMaintain.MaintainScope.SPOT:
            page_scope_infos = [
                {'page': [
                    {'trigger_page': NotificationBar.TriggerPage.SPOT_MARKET.name,
                     'trigger_page_params': ''
                     }],
                    'scope': TempMaintain.MaintainScope.SPOT
                }]
        elif scope == TempMaintain.MaintainScope.PERPETUAL:
            page_scope_infos = [
                {'page': [
                    {'trigger_page': NotificationBar.TriggerPage.PERPETUAL_MARKET.name,
                     'trigger_page_params': ''
                     }],
                    'scope': TempMaintain.MaintainScope.PERPETUAL
                }]
        else:
            page_scope_infos = []
        begin_at = maintain_ended_at
        end_at = maintain_ended_at + datetime.timedelta(minutes=protect_duration)
        res = []
        for info in page_scope_infos:
            nb_id = cls.create_new_notification(info, begin_at, end_at, jump_page_enabled,
                                                jump_url, user_id)
            res.append(nb_id)
        item.notification_ids = json.dumps(res)
        db.session.commit()
        NotificationBarCache.reload()

    @classmethod
    def create_new_notification(cls, info, begin_at, end_at, jump_page_enabled, jump_url,
                                user_id):
        nb = NotificationBar(
            name='系统升级保护期通知（自动生成）',
            platform=NotificationBar.Platform.ALL,
            trigger_pages=json.dumps(info['page']),
            begin_at=begin_at,
            end_at=end_at,
            created_by=user_id,
            audited_by=user_id,
            audited_at=now(),
            status=NotificationBar.Status.AUDITED,
            auditor_remark='系统升级保护期通知自动审核',
            remark='系统升级自动生成保护期通知'
        )
        if jump_page_enabled and jump_url:
            jump = AppJumpList.query.filter(AppJumpList.jump_data == jump_url,
                                            AppJumpList.jump_type == AppJumpList.JumpType.URL
                                            ).order_by(AppJumpList.id.desc()).first()
            if not jump:
                jump = AppJumpList(
                    remark='维护跳转自动添加',
                    jump_type=AppJumpList.JumpType.URL,
                    jump_data=jump_url
                )
                db.session.add(jump)
                db.session.flush()
            nb.jump_page_enabled = True
            nb.jump_type = NotificationBar.JumpType.URL
            nb.jump_id = jump.id
        db.session.add(nb)
        db.session.flush()
        end_at_str = datetime_to_str(end_at)
        for lang in TempMaintain.AVAILABLE_LANGS:
            with force_locale(lang.value):
                title = gettext('系统升级保护期生效中')
                if info['scope'] == TempMaintain.MaintainScope.SPOT:
                    text = gettext(
                        "预计于 %(end_at_str)s（UTC）结束，当前支持撤单、下单（仅Maker Only限价单）。",
                        end_at_str=end_at_str)
                elif info['scope'] == TempMaintain.MaintainScope.PERPETUAL:
                    text = gettext(
                        "预计于 %(end_at_str)s（UTC）结束，当前支持撤单、下单（仅Maker Only限价单）、增加或减少保证金。",
                        end_at_str=end_at_str)
                nb_content = NotificationBarContent(
                    notification_bar_id=nb.id,
                    lang=lang,
                    title=title,
                    summary=text,
                    content=text,
                )
                db.session.add(nb_content)
        return nb.id

    @classmethod
    def auto_delete_notifications(cls, item, id_str):
        ids = json.loads(id_str)
        nbs = NotificationBar.query.filter(NotificationBar.id.in_(ids)).all()
        for nb in nbs:
            nb.status = NotificationBar.Status.DELETED
        item.notification_ids = None
        db.session.commit()
        NotificationBarCache.reload()

    @classmethod
    def auto_update_notification_time(cls, maintain_stop_at, protect_duration, id_str):
        ids = json.loads(id_str)
        nbs = NotificationBar.query.filter(NotificationBar.id.in_(ids)).all()
        end_at = maintain_stop_at + datetime.timedelta(minutes=protect_duration)
        end_at_str = datetime_to_str(end_at)

        for nb in nbs:
            nb.begin_at = maintain_stop_at
            nb.end_at = end_at
            triger_info = json.loads(nb.trigger_pages)
            triger_page = triger_info[0]['trigger_page']
            contents = NotificationBarContent.query.filter(
                NotificationBarContent.notification_bar_id == nb.id
            )
            if triger_page == NotificationBar.TriggerPage.SPOT_MARKET.name:
                for content in contents:
                    with force_locale(content.lang.value):
                        text = gettext(
                            "预计于 %(end_at_str)s（UTC）结束，当前支持撤单、下单（仅Maker Only限价单）。",
                            end_at_str=end_at_str)
                        content.content = text
                        content.summary = text
            elif triger_page == NotificationBar.TriggerPage.PERPETUAL_MARKET.name:
                for content in contents:
                    with force_locale(content.lang.value):
                        text = gettext(
                            "预计于 %(end_at_str)s（UTC）结束，当前支持撤单、下单（仅Maker Only限价单）、增加或减少保证金。",
                            end_at_str=end_at_str)
                        content.content = text
                        content.summary = text
            else:
                continue
        db.session.commit()
        NotificationBarCache.reload()

    @classmethod
    def auto_update_notifications(cls, old_item, kwargs, old_data, user_id):
        old_protect_duration = old_data.get('protect_duration')
        new_protect_duration = kwargs.get('protect_duration')
        old_end_at = old_data['ended_at']
        new_end_at = kwargs['ended_at']
        if not old_protect_duration and new_protect_duration:
            cls.auto_add_protect_duration_notification_bar(
                old_item, kwargs['scope'], new_end_at, new_protect_duration,
                kwargs.get('jump_page_enabled'), kwargs.get('url'), user_id)
        elif old_protect_duration and not new_protect_duration:
            cls.auto_delete_notifications(old_item, old_data['notification_ids'])
        elif old_protect_duration and new_protect_duration:
            if old_protect_duration != new_protect_duration or old_end_at != new_end_at:
                cls.auto_update_notification_time(new_end_at, new_protect_duration, old_data['notification_ids'])
        else:
            return

    @classmethod
    def update_maintain_contents(cls, id_, scope, minute_delta, protect_duration, started_at_str):
        cls.delete_old_contents(id_)
        cls.create_new_contents(id_, scope, minute_delta, protect_duration, started_at_str)

    @classmethod
    def delete_old_contents(cls, id_):
        TempMaintainContent.query.filter(TempMaintainContent.temp_maintain_id == id_).delete()
        db.session.commit()

    @classmethod
    def create_new_contents(cls, temp_maintain_id, scope, minute_delta, protect_duration, started_at_str):
        for lang in TempMaintain.AVAILABLE_LANGS:
            with (force_locale(lang.value)):
                if protect_duration:
                    if scope == TempMaintain.MaintainScope.SPOT:
                        protect_text = gettext('升级结束后，系统将自动进入保护期，持续时间%(protect_duration)s分钟'
                                               '，期间支持撤单、下单（仅Maker Only限价单）。', protect_duration=protect_duration)
                    else:
                        protect_text =gettext(
                            '升级结束后，系统将自动进入保护期，持续时间%(protect_duration)s分钟，'
                            '期间支持撤单、下单（仅Maker Only限价单）、增加或减少保证金。', protect_duration=protect_duration)
                else:
                    protect_text = gettext('')
                if scope == TempMaintain.MaintainScope.SPOT:
                    title = gettext("币币停服升级即将开始")
                    content = gettext(
                        "币币系统升级将于%(started_at_str)s（UTC）开始，时长约%(minute_delta)s分钟，届时暂停现货、杠杆交易与兑换服务，请提前做好风险控制。",
                        started_at_str=started_at_str, minute_delta=minute_delta) + protect_text
                    new_content = gettext(
                        "币币系统升级即将开始，时长约%(minute_delta)s分钟，届时暂停现货、杠杆交易与兑换服务，请提前做好风险控制。",
                        minute_delta=minute_delta) + protect_text
                elif scope == TempMaintain.MaintainScope.PERPETUAL:
                    title = gettext("合约停服升级即将开始")
                    content = gettext(
                        "合约系统升级将于%(started_at_str)s（UTC）开始，时长约%(minute_delta)s分钟，届时暂停合约交易，请提前做好风险控制。",
                        started_at_str=started_at_str, minute_delta=minute_delta) + protect_text
                    new_content = gettext(
                        "合约系统升级即将开始，时长约%(minute_delta)s分钟，届时暂停合约交易，请提前做好风险控制。",
                                                                  minute_delta=minute_delta) + protect_text
                elif scope == TempMaintain.MaintainScope.ALL:
                    title = gettext("币币及合约停服升级即将开始")
                    content = gettext(
                        "币币及合约系统升级将于%(started_at_str)s（UTC）开始，时长约%(minute_delta)s分钟，届时暂停现货、杠杆、合约交易与资产划转及兑换服务，请提前做好风险控制。",
                                                            started_at_str=started_at_str,
                                                            minute_delta=minute_delta) + protect_text
                    new_content = gettext(
                        "币币及合约系统升级即将开始，时长约%(minute_delta)s分钟，届时暂停现货、杠杆、合约交易与资产划转及兑换服务，请提前做好风险控制。",
                                                            minute_delta=minute_delta) + protect_text
                elif scope == TempMaintain.MaintainScope.ALL_SITE:
                    title = gettext("全站停服升级即将开始")
                    content = gettext(
                        "CoinEx将于%(started_at_str)s（UTC）进行全站升级，届时所有服务暂停%(minute_delta)s分钟，请提前做好风险控制。给你带来的不便，敬请谅解。",
                                                                started_at_str=started_at_str,
                                                                minute_delta=minute_delta) + protect_text
                    new_content = gettext(
                        "CoinEx即将进行全站升级，届时所有服务暂停%(minute_delta)s分钟，请提前做好风险控制。给你带来的不便，敬请谅解。",
                                                                minute_delta=minute_delta) + protect_text

                row = TempMaintainContent(
                    temp_maintain_id=temp_maintain_id,
                    lang=lang,
                    title=title,
                    content=content,
                    new_content=new_content,
                )
                db.session.add(row)
        db.session.commit()


@ns.route('/temp-maintain')
@respond_with_code
class TempMaintainResource(Resource, TempMaintainMixin):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(),
        limit=LimitField(),
        scope=EnumField(TempMaintain.MaintainScope),
        started_at=TimestampField(),
        ended_at=TimestampField(),
        keyword=fields.String,
        active_status=EnumField(TempMaintain.ActiveStatus),
    ))
    def get(cls, **kwargs):
        """系统-升级维护-临时维护记录列表"""
        page, limit = kwargs.get('page'), kwargs.get('limit')
        # 处于开启中的临时维护展示在最前面，已关闭的在后面，按id排序
        query = TempMaintain.query.filter(TempMaintain.status == TempMaintain.Status.VALID).order_by(TempMaintain.id.desc())
        if scope := kwargs.get('scope'):
            query = query.filter(TempMaintain.scope == scope)
        if keyword := kwargs.get('keyword'):
            user_ids = User.search_for_users(keyword)
            query = query.filter(TempMaintain.user_id.in_(user_ids))
        if started_at := kwargs.get('started_at'):
            query = query.filter(TempMaintain.started_at >= started_at)
        records = query.all()
        if active_status := kwargs.get('active_status'):
            now_ = now()
            rows = []
            if active_status == TempMaintain.ActiveStatus.CREATED:
                rows = [i for i in records if i.started_at > now_]
            elif active_status == TempMaintain.ActiveStatus.STARTED:
                rows = [i for i in records if i.started_at <= now_ < i.ended_at]
            elif active_status == TempMaintain.ActiveStatus.PROTECTING:
                for record in records:
                    if record.protect_duration is not None and record.ended_at < now_ <= record.ended_at + datetime.timedelta(
                            minutes=record.protect_duration):
                        rows.append(record)
            elif active_status == TempMaintain.ActiveStatus.FINISHED:
                for record in records:
                    if record.protect_duration is None and record.ended_at < now_:
                        rows.append(record)
                    elif (record.protect_duration is not None and
                          record.ended_at + datetime.timedelta(minutes=record.protect_duration) < now_):
                        rows.append(record)
        else:
            rows = records

        total = len(rows)
        paginate_items = rows[(page - 1) * limit: page * limit]
        extra = dict(
            maintain_scopes=TempMaintain.MaintainScope,
            active_statuses=TempMaintain.ActiveStatus,
        )
        name_map = get_admin_user_name_map({item.user_id for item in paginate_items})

        return dict(total=total,
                    items=[cls.fmt_data(item, name_map) for item in paginate_items],
                    extra=extra)

    @classmethod
    def fmt_data(cls, row, admin_name_map):
        ret = row.to_dict(enum_to_name=True)
        ret.update(user_email=admin_name_map.get(row.user_id),
                   active_status=row.active_status.name)
        return ret

    @classmethod
    @update_temp_maintain_cache
    @ns.use_kwargs(
        dict(
            scope=EnumField(TempMaintain.MaintainScope, required=True),
            started_at=TimestampField(required=True),
            ended_at=TimestampField(required=True),
            jump_page_enabled=fields.Boolean(required=True),
            remark=fields.String(missing='', allow_none=True),
            url=fields.String(missing='', allow_none=True),
            protect_duration=fields.Integer(allow_none=True)
        )
    )
    def post(cls, **kwargs):
        """系统-升级维护-新增临时维护记录"""
        started_at, ended_at, scope, protect_duration, user_id = (
            kwargs.get('started_at'), kwargs.get('ended_at'), kwargs['scope'], kwargs.get('protect_duration'), g.user.id)
        cls._check(started_at, ended_at, scope)
        kwargs.update(user_id=user_id)
        tm = TempMaintain(**kwargs)
        db.session.add(tm)

        if protect_duration:
            cls.auto_add_protect_duration_notification_bar(
                tm, scope, ended_at, protect_duration, kwargs['jump_page_enabled'], kwargs.get('url'), user_id)

        if scope == TempMaintain.MaintainScope.ALL_SITE:
            cls.create_all_site_maintain(int(started_at.timestamp()), int(ended_at.timestamp()), kwargs.get('url', ''),
                                         protect_duration)
        started_at_str = started_at.strftime("%Y-%m-%d %H:%M:%S")
        minute_delta = int((ended_at - started_at).total_seconds() // 60)
        cls.create_new_contents(tm.id, scope, minute_delta, protect_duration, started_at_str)

        item = tm.to_dict(enum_to_name=True)

        AdminOperationLog.new_add(
            user_id=user_id,
            ns_obj=OPNamespaceObjectOperation.TempMaintain,
            detail=kwargs,
        )
        return item

    @classmethod
    def _check(cls, started_at, ended_at, scope):
        now_ = now()
        if started_at >= ended_at:
            raise InvalidArgument(message="结束时间不能小于开始时间")
        if ended_at < now_:
            raise InvalidArgument(message="结束时间不能小于当前时间")
        if started_at < now_:
            raise InvalidArgument(message="开始时间不能小于当前时间")
        rows = TempMaintain.query.filter(
            TempMaintain.status == TempMaintain.Status.VALID,
        ).all()
        cls._check_maintain_scope_valid(scope, rows, started_at, ended_at)


@ns.route('/temp-maintain/<int:id_>')
@respond_with_code
class TempMaintainDetailResource(Resource, TempMaintainMixin):

    @classmethod
    @update_temp_maintain_cache
    @ns.use_kwargs(dict(
        scope=EnumField(TempMaintain.MaintainScope, required=True),
        started_at=TimestampField(required=True),
        ended_at=TimestampField(required=True),
        jump_page_enabled=fields.Boolean(required=True),
        remark=fields.String(missing='', allow_none=True),
        url=fields.String(missing='', allow_none=True),
        protect_duration=fields.Integer(allow_none=True)
    ))
    def put(cls, id_, **kwargs):
        """系统-升级维护-修改临时维护记录"""
        item = cls._check(id_, **kwargs)
        user_id = g.user.id
        old_data = item.to_dict(enum_to_name=True)
        for k, v in kwargs.items():
            setattr(item, k, v)
        old_scope, new_scope = getattr(TempMaintain.MaintainScope, old_data['scope']), kwargs['scope']
        old_protect_duration = old_data.get('protect_duration')
        new_protect_duration = kwargs.get('protect_duration')
        if old_scope == TempMaintain.MaintainScope.ALL_SITE or new_scope == TempMaintain.MaintainScope.ALL_SITE:
            cls.edit_all_site_maintain(old_scope, new_scope, int(kwargs['started_at'].timestamp()),
                                       int(kwargs['ended_at'].timestamp()), kwargs.get('url', ''), new_protect_duration)

        if old_protect_duration != new_protect_duration or old_data['ended_at'] != kwargs['ended_at']:
            cls.auto_update_notifications(item, kwargs, old_data, user_id)

        old_minute_delta = int((old_data['ended_at'] - old_data['started_at']).total_seconds() // 60)
        new_minute_delta = int((kwargs['ended_at'] - kwargs['started_at']).total_seconds() // 60)
        if old_minute_delta != new_minute_delta or old_protect_duration != new_protect_duration or old_scope != new_scope:
            started_at_str = kwargs['started_at'].strftime("%Y-%m-%d %H:%M:%S")
            cls.update_maintain_contents(id_, new_scope, new_minute_delta, new_protect_duration, started_at_str)

        AdminOperationLog.new_edit(
            user_id=user_id,
            ns_obj=OPNamespaceObjectOperation.TempMaintain,
            old_data=old_data,
            new_data=item.to_dict(enum_to_name=True),
        )

        return item

    @classmethod
    def _check(cls, id_, **kwargs):
        item = TempMaintain.query.get(id_)
        if not item:
            raise RecordNotFound
        if item.active_status == TempMaintain.ActiveStatus.FINISHED:
            raise InvalidArgument(message="已结束的通知无法修改")
        started_at, ended_at, scope = \
            kwargs.get('started_at'), kwargs.get('ended_at'), kwargs['scope']
        now_ = now()
        if started_at >= ended_at:
            raise InvalidArgument(message="结束时间不能小于开始时间")
        if ended_at < now_:
            raise InvalidArgument(message="结束时间不能小于当前时间")
        rows = TempMaintain.query.filter(
            TempMaintain.id != id_,
            TempMaintain.status == TempMaintain.Status.VALID,
        ).all()
        cls._check_maintain_scope_valid(scope, rows, started_at, ended_at)
        return item

    @classmethod
    @update_temp_maintain_cache
    def delete(cls, id_):
        """系统-升级维护-删除临时维护记录"""
        item = TempMaintain.query.get(id_)
        if not item:
            raise RecordNotFound
        user_id = g.user.id
        tmp = item.to_dict(enum_to_name=True)
        item.status = TempMaintain.Status.DELETED

        close_protect_duration(item.scope)    # 关闭server保护期，防止潜在的server重启后一直处于保护期中
        item.protect_duration_updated = True
        if tmp['scope'] == TempMaintain.MaintainScope.ALL_SITE.name:
            cls.delete_all_site_maintain()
        if id_str := item.notification_ids:
            cls.auto_delete_notifications(item, id_str)
        AdminOperationLog.new_delete(
            user_id=user_id,
            ns_obj=OPNamespaceObjectOperation.TempMaintain,
            detail=dict(id=id_),
        )

    @classmethod
    @update_temp_maintain_cache
    def patch(cls, id_):
        """系统-升级维护-终止临时维护"""
        stop_at = convert_datetime(now(), 'second')  # 取到秒避免刷新缓存刷新不到
        item = TempMaintain.query.get(id_)
        if not item:
            raise RecordNotFound
        user_id = g.user.id
        tmp = item.to_dict(enum_to_name=True)
        item.ended_at = stop_at
        if tmp['scope'] == TempMaintain.MaintainScope.ALL_SITE.name:
            cls.create_all_site_maintain(int(tmp['started_at'].timestamp()), int(stop_at.timestamp()), tmp['url'],
                                         tmp['protect_duration'])
        db.session.commit()
        if id_str := item.notification_ids:
            cls.auto_update_notification_time(stop_at, item.protect_duration, id_str)

        AdminOperationLog.new_stop(
            user_id=user_id,
            ns_obj=OPNamespaceObjectOperation.TempMaintain,
            detail=dict(id=id_, ended_at=stop_at),
        )

    @classmethod
    def get(cls, id_):
        """系统-升级维护-获取临时维护记录"""
        lang_names = language_cn_names()
        extra = dict(
            maintain_scopes=TempMaintain.MaintainScope,
            languages={e.name: lang_names[e]
                       for e in TempMaintain.AVAILABLE_LANGS},
        )
        if not id_:
            return dict(
                extra=extra
            )
        row = TempMaintain.query.get(id_)
        if row is None:
            raise RecordNotFound
        res = row.to_dict(enum_to_name=True)
        res['active_status'] = row.active_status.name
        res['extra'] = extra
        return res


@ns.route('/temp-maintain/<int:id_>/langs/<lang>')
@respond_with_code
class TempMaintainContentResource(Resource):

    @classmethod
    def get(cls, id_, lang):
        """运营-维护通知-获取维护通知详情"""
        row = cls._get_row(id_, lang)
        if row is None:
            return dict(
                title='',
                content=''
            )
        return dict(
            title=row.title,
            content=row.new_content or row.content
        )

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String(required=True),
        content=fields.String(required=True)
    ))
    def put(cls, id_, lang, **kwargs):
        """运营-维护通知-编辑维护通知详情"""
        title = kwargs['title']
        content = kwargs['content']

        item = TempMaintain.query.get(id_)
        if item.active_status != TempMaintain.ActiveStatus.CREATED:
            raise InvalidArgument(message="维护中/已结束的通知无法修改")

        row = cls._get_row(id_, lang)
        old_data = None
        if row is None:
            row = TempMaintainContent(
                temp_maintain_id=id_,
                lang=lang,
                title=title,
                content='',
                new_content=content,
            )
            db.session.add(row)
        else:
            old_data = row.to_dict(enum_to_name=True)
            row.title = title
            row.new_content = content
            row.updated_at = now()
        db.session.commit()

        AdminOperationLog.new_add_or_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TempMaintainContent,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

        return row

    @classmethod
    def _get_row(cls, id_: int, lang: str):
        return TempMaintainContent.query \
            .filter(TempMaintainContent.temp_maintain_id == id_,
                    TempMaintainContent.lang == getattr(Language, lang, '')) \
            .first()


class SingleMarketMaintainMixin:

    @classmethod
    def check_for_add_new_rec(cls, market, maintain_status):
        cls.check_market(market)
        maintain_rec = MarketMaintain.query.filter(
            MarketMaintain.market == market,
            MarketMaintain.status == MarketMaintain.Status.VALID
        ).first()
        if maintain_rec:
            raise InvalidArgument(message='该市场维护已存在其他维护记录！')
        if maintain_status == MarketMaintain.MaintainStatus.PROTECT_DURATION:
            raise InvalidArgument(message='只有停服状态才能修改为保护期。')

    @classmethod
    def check_market(cls, market):
        market = Market.query.filter(Market.name == market).first()
        if not market or market.status != Market.Status.ONLINE:
            raise InvalidArgument(message='市场不存在或未处于上架状态')

    @classmethod
    def check_rec_exist(cls, maintain_id):
        maintain_rec = MarketMaintain.query.get(maintain_id)
        if not maintain_rec or maintain_rec.status != MarketMaintain.Status.VALID:
            raise InvalidArgument(message='该记录不存在或已被删除！')
        return maintain_rec

    @classmethod
    def add_or_update_tip_bar_for_maintain_rec(cls, maintain_rec):
        maintain_status = maintain_rec.maintain_status
        if maintain_status == MarketMaintain.MaintainStatus.OUT_OF_SERVICE:
            # 首次或从保护期变为停服
            if not maintain_rec.tip_bar_id:
                return
            cls.delete_tip_bar(maintain_rec.tip_bar_id)
            db.session.commit()
            cls.reload_tip_bar_cache()
        elif maintain_status == MarketMaintain.MaintainStatus.PROTECT_DURATION:
            tip_bar_id = cls.add_tip_bar_for_maintain_rec(maintain_rec)
            maintain_rec.tip_bar_id = tip_bar_id
            db.session.commit()
            cls.reload_tip_bar_cache()

    @classmethod
    def add_tip_bar_for_maintain_rec(cls, maintain_rec):
        market = maintain_rec.market
        started_at = now()
        ended_at = maintain_rec.maintain_expect_end_at if (
            maintain_rec.maintain_expect_end_at) else datetime.datetime(2099, 1, 1)
        trigger_page_list = [{
                    "trigger_page": 'SPOT_MARKET',
                    "param_type": 'MARKET',
                    "page_op": 'IN',
                    "trigger_page_params": [market]
                }]
        tip_bar = TipBar(
            name=f'现货{market}停服维护自动生成',
            started_at=started_at,
            ended_at=ended_at,
            platform=TipBar.Platform.ALL,
            trigger_pages=json.dumps(trigger_page_list),
            filter_type=TipBar.FilterType.NONE,
            whitelist_enabled=False,
            user_whitelist="",
            jump_page_enabled=False,
            jump_type=None,
            jump_id=None,
            remark='系统自动生成',
            created_by=g.user.id,
            audited_by=g.user.id,
            audited_at=started_at,
            auditor_remark='系统自动审批通过',
            status=TipBar.Status.AUDITED
        )
        db.session.add(tip_bar)
        db.session.flush()
        cls.add_tip_bar_content(tip_bar.id, maintain_rec.maintain_expect_end_at)
        db.session.commit()
        return tip_bar.id

    @classmethod
    def add_tip_bar_content(cls, tip_bar_id, maintain_expect_end_at):
        for lang in TipBar.AVAILABLE_LANGS:
            with force_locale(lang.value):
                content = gettext('该市场当前为保护期，仅支持撤单、下单（仅Maker Only限价单）。')
                if maintain_expect_end_at:
                    maintain_expect_end_at_str = datetime_to_str(maintain_expect_end_at)
                    recover_time = gettext('预计恢复时间为：%(maintain_expect_end_at_str)s (UTC)'
                                           , maintain_expect_end_at_str=maintain_expect_end_at_str)
                else:
                    recover_time = gettext('')
                content += recover_time
                db.session.add(TipBarContent(
                    lang=lang,
                    tip_bar_id=tip_bar_id,
                    content=content
                ))

    @classmethod
    def delete_tip_bar(cls, tip_bar_id):
        tip_bar = TipBar.query.get(tip_bar_id)
        if not tip_bar:
            return
        tip_bar.status = TipBar.Status.DELETED

    @classmethod
    def reload_tip_bar_cache(cls):
        TipBarCache.reload()

    @classmethod
    def reload_maintain_cache(cls):
        MarketMaintainCache().reload()


@ns.route('/single-market/maintain')
@respond_with_code
class SingleMarketMaintainsResource(Resource, SingleMarketMaintainMixin):

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(required=True),
        maintain_status=EnumField(MarketMaintain.MaintainStatus, required=True),
        maintain_expect_end_at=TimestampField(allow_none=True)
    ))
    def post(cls, **kwargs):
        """系统-升级维护-新建单市场维护"""
        market = kwargs['market']
        maintain_status = kwargs['maintain_status']
        maintain_expect_end_at = kwargs.get('maintain_expect_end_at')
        cls.check_for_add_new_rec(market, maintain_status)
        maintain_rec = MarketMaintain(
            market=market,
            maintain_status=maintain_status,
            maintain_expect_end_at=maintain_expect_end_at
        )
        db.session.add(maintain_rec)
        client = ServerClient()
        client.set_market_maintain_status(market, maintain_status)
        db.session.flush()
        db.session.commit()
        cls.reload_maintain_cache()
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MarketMaintain,
            detail=kwargs,
        )

    @classmethod
    def get(cls):
        """系统-升级维护-单市场维护状态列表"""
        records = MarketMaintain.query.filter(
            MarketMaintain.status == MarketMaintain.Status.VALID
        ).order_by(MarketMaintain.id.desc()).all()
        markets = MarketCache.list_online_markets()
        res = [i.to_dict(enum_to_name=True) for i in records]
        return dict(items=res, markets=markets)


@ns.route('/single-market/maintain/<int:id_>')
@respond_with_code
class SingleMarketMaintainResource(Resource, SingleMarketMaintainMixin):

    @classmethod
    @ns.use_kwargs(dict(
        maintain_status=EnumField(MarketMaintain.MaintainStatus, required=True),
        maintain_expect_end_at=TimestampField(allow_none=True)
    ))
    def put(cls, id_, **kwargs):
        """系统-升级维护-编辑单市场维护"""
        maintain_status = kwargs['maintain_status']
        maintain_expect_end_at = kwargs.get('maintain_expect_end_at')
        maintain_rec = cls.check_rec_exist(id_)
        old_data = maintain_rec.to_dict(enum_to_name=True)
        maintain_rec.maintain_status = maintain_status
        maintain_rec.maintain_expect_end_at = maintain_expect_end_at
        client = ServerClient()
        client.set_market_maintain_status(maintain_rec.market, maintain_status)
        cls.reload_maintain_cache()
        cls.add_or_update_tip_bar_for_maintain_rec(maintain_rec)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MarketMaintain,
            old_data=old_data,
            new_data=maintain_rec.to_dict(enum_to_name=True),
        )

    @classmethod
    def delete(cls, id_):
        """系统-升级维护-删除单市场维护"""
        maintain_rec = cls.check_rec_exist(id_)
        maintain_rec.status = MarketMaintain.Status.DELETED
        client = ServerClient()
        client.set_market_maintain_status(maintain_rec.market, MarketMaintain.MaintainStatus.ON_SERVICE)
        cls.delete_tip_bar(maintain_rec.tip_bar_id)
        db.session.commit()
        cls.reload_maintain_cache()
        cls.reload_tip_bar_cache()
        AdminOperationLog.new_stop(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MarketMaintain,
            detail={'record_id': id_, 'market': maintain_rec.market},
        )


@ns.route('/local-areas-block')
@respond_with_code
class LocalAreasBlockListResource(Resource):
    model = LocalAreasBlock

    @classmethod
    @mem_cached(600)
    def get_country_state_list(cls):
        # upload from deployment/scripts/upload_country_states_json.py
        file_name = 'country_states.json'
        country_state_list_url = AWSBucketPublic.get_file_url(file_name)
        response = requests.get(country_state_list_url)
        country_state_list = []
        if response.status_code == 200:
            country_state_list = json.loads(response.text)
        return country_state_list

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(model.Type),
        country_code=fields.String(),
        state_code=fields.String(),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """系统-局部地区屏蔽表"""
        model = cls.model
        query = model.query.filter(
            model.status == model.Status.VALID,
        ).order_by(model.id.desc())
        if type_ := kwargs.get('type'):
            query = query.filter(model.type == type_)
        if country_code := kwargs.get('country_code'):
            query = query.filter(model.country_code == country_code)
        if state_code := kwargs.get('state_code'):
            query = query.filter(model.state_code == state_code)

        total = query.count()
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        records = pagination.items
        ids = [item.id for item in records]
        title_mapping = cls._get_title_mapping(ids=ids)
        items = []
        country_state_list = cls.get_country_state_list()
        country_code_state_map = {i['iso2']: dict(name=i['name'],
                         states=[dict(
                             code=j['state_code'],
                             name=j['name'],
                         ) for j in i['states']],
                         ) for i in country_state_list}
        for row in records:
            item = row.to_dict(enum_to_name=True)
            item['titles'] = title_mapping.get(item['id'])
            if item['titles']:
                item['title'] = item['titles'].get(Language.ZH_HANS_CN.name)
            states = country_code_state_map.get(item['country_code'])
            if states and states['states']:
                for i in states['states']:
                    if i['code'] == item['state_code']:
                        item['state_name'] = i['name']
                        break
            items.append(item)

        

        return dict(
            types=model.Type,
            country_code_state_map=country_code_state_map,
            langs={lang.name: ln.chinese for lang, ln in LANGUAGE_NAMES.items()
                   if lang in model.AVAILABLE_LANGS},
            items=items,
            total=total,
        )

    @classmethod
    def _get_title_mapping(cls, ids):
        model = LocalAreasBlockContent
        rows = model.query.filter(
            model.local_areas_block_id.in_(ids)
        ).all()
        ret = dict()
        for row in rows:
            if row.local_areas_block_id not in ret:
                ret[row.local_areas_block_id] = dict()
            ret[row.local_areas_block_id][row.lang.name] = row.title
        return ret

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(model.Type, required=True),
        country_code=fields.String(required=True),
        state_codes=fields.List(fields.String, required=True),
    ))
    def post(cls, **kwargs):
        """系统-局部地区屏蔽表创建"""

        model = cls.model
        for code in kwargs['state_codes']:
            record = model.get_or_create(
                country_code=kwargs['country_code'],
                state_code=code,
                type=kwargs['type']
            )
            record.status = model.Status.VALID
            db.session.add(record)
        model.query.filter(
            model.country_code == kwargs['country_code'],
            model.state_code.notin_(kwargs['state_codes']),
            model.status == model.Status.VALID,
            model.type == kwargs['type'],
        ).update(dict(status=model.Status.DELETED))
        db.session.commit()


@ns.route('/prop-trading-config')
@respond_with_code
class PropTradingConfigResource(Resource):

    SCOPES = {
        'ALL': '全部市场',
        'AREA': '交易区',
        'CUSTOM': '指定市场'
    }

    @classmethod
    def get(cls):
        """系统-自营交易-查看配置"""
        config = PropTradingBusiness.get_config()
        return {
            'sample_rate': amount_to_str(config.sample_rate * 100),
            'market_scope': config.market_scope.name,
            'markets': config.markets,
            'market_scopes': cls.SCOPES,
            'blacklist_markets': config.blacklist_markets,
            'whitelist_deal_days': config.whitelist_deal_days,
            'whitelist_deal_amount': config.whitelist_deal_amount,
            'whitelist_register_days': config.whitelist_register_days,
            'risk_deal_amount': config.risk_deal_amount,
            'risk_deal_amount_rate': amount_to_str(config.risk_deal_amount_rate * 100),
            'prop_risk_deal_amount': config.prop_risk_deal_amount,
            'prop_risk_deal_amount_rate': amount_to_str(config.prop_risk_deal_amount_rate * 100),
        }

    @classmethod
    @ns.use_kwargs(dict(
        sample_rate=fields.Decimal(),
        market_scope=EnumField(PropTradingConfig.MarketScope),
        markets=fields.String(),
        blacklist_markets=fields.String(),
        whitelist_deal_days=fields.Integer(),
        whitelist_deal_amount=fields.Decimal(),
        whitelist_register_days=fields.Integer(),
        risk_deal_amount=fields.Decimal(),
        risk_deal_amount_rate=fields.Decimal(),
        prop_risk_deal_amount_rate=fields.Decimal(),
        prop_risk_deal_amount=fields.Decimal(),
    ))
    def post(cls, **kwargs):
        """系统-自营交易-修改配置"""
        config = PropTradingBusiness.get_config()
        db.session.add(config)
        old_data = config.to_dict(enum_to_name=True)
        if sample_rate := kwargs.get('sample_rate'):
            sample_rate = quantize_amount(sample_rate / 100, 2)
            if not 0 <= sample_rate <= 1:
                raise InvalidArgument(message="采样率必须在0-100之间")
            config.sample_rate = sample_rate
        if market_scope := kwargs.get('market_scope'):
            if market_scope == PropTradingConfig.MarketScope.ALL:
                raise InvalidArgument(message="暂不支持反向合约市场")
            elif market_scope in (PropTradingConfig.MarketScope.CUSTOM, PropTradingConfig.MarketScope.AREA):
                if not (markets := kwargs.get('markets')):
                    raise InvalidArgument(message="请选择指定市场/交易区")
                try:
                    markets = json.loads(markets)
                    if not isinstance(markets, list):
                        raise ValueError
                except Exception:
                    raise InvalidArgument(message="市场/交易区格式错误")
                if market_scope == PropTradingConfig.MarketScope.CUSTOM:
                    p_markets = PerpetualMarketCache().get_market_list()
                    if not set(markets).issubset(set(p_markets)):
                        raise InvalidArgument(message="选择的市场有误")
                    if any(x.endswith('USD') for x in markets):
                        raise InvalidArgument(message="暂不支持反向合约市场")
                else:
                    for area in markets:
                        if area == 'USD':
                            raise InvalidArgument(message="暂不支持反向合约市场")
                        elif area not in ('USDT', 'USDC'):
                            raise InvalidArgument(message="选择的交易区有误")
            else:
                raise InvalidArgument
            config.market_scope = market_scope
            config.markets = json.dumps(markets)
        if blacklist_markets := kwargs.get('blacklist_markets'):
            try:
                blacklist_markets = json.loads(blacklist_markets)
                if not isinstance(blacklist_markets, list):
                    raise ValueError
            except Exception:
                raise InvalidArgument(message="黑名单市场格式错误")
            p_markets = PerpetualMarketCache().get_market_list()
            if not set(blacklist_markets).issubset(set(p_markets)):
                raise InvalidArgument(message="黑名单的市场有误")
            config.blacklist_markets = json.dumps(blacklist_markets)
        if whitelist_deal_days := kwargs.get('whitelist_deal_days'):
            if whitelist_deal_days < 0:
                raise InvalidArgument(message="白名单交易天数不能小于0")
            config.whitelist_deal_days = whitelist_deal_days
        if whitelist_deal_amount := kwargs.get('whitelist_deal_amount'):
            if whitelist_deal_amount < 0:
                raise InvalidArgument(message="白名单交易金额不能小于0")
            config.whitelist_deal_amount = whitelist_deal_amount
        if whitelist_register_days := kwargs.get('whitelist_register_days'):
            if whitelist_register_days < 0:
                raise InvalidArgument(message="白名单注册天数不能小于0")
            config.whitelist_register_days = whitelist_register_days
        if risk_deal_amount := kwargs.get('risk_deal_amount'):
            if risk_deal_amount < 0:
                raise InvalidArgument(message="成交市值不能小于0")
            config.risk_deal_amount = risk_deal_amount
        if risk_deal_amount_rate := kwargs.get('risk_deal_amount_rate'):
            risk_deal_amount_rate = quantize_amount(risk_deal_amount_rate / 100, 2)
            if not 0 <= risk_deal_amount_rate <= 1:
                raise InvalidArgument(message="成交量占比必须在0-100之间")
            config.risk_deal_amount_rate = risk_deal_amount_rate
        if prop_risk_deal_amount := kwargs.get('prop_risk_deal_amount'):
            if prop_risk_deal_amount < 0:
                raise InvalidArgument(message="成交市值不能小于0")
            config.prop_risk_deal_amount = prop_risk_deal_amount
        if prop_risk_deal_amount_rate := kwargs.get('prop_risk_deal_amount_rate'):
            prop_risk_deal_amount_rate = quantize_amount(prop_risk_deal_amount_rate / 100, 2)
            if not 0 <= prop_risk_deal_amount_rate <= 1:
                raise InvalidArgument(message="成交量占比必须在0-100之间")
            config.prop_risk_deal_amount_rate = prop_risk_deal_amount_rate
        db.session.commit()

        if sample_rate or market_scope or kwargs.get('markets') or kwargs.get('blacklist_markets'):
            update_prop_config_task.delay(True)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.PropTradingConfig,
            old_data=old_data,
            new_data=config.to_dict(enum_to_name=True),
        )


@ns.route('/prop-trading-user')
@respond_with_code
class PropTradingUserResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        status=EnumField(PropTradingUser.Status),
        reason=EnumField(PropTradingUser.Reason),
        page=PageField,
        limit=LimitField(missing=100),
    ))
    def get(cls, **kwargs):
        """系统-自营交易-查看用户"""
        query = PropTradingUser.query.order_by(PropTradingUser.id.desc())
        if user_id := kwargs.get('user_id'):
            query = query.filter(PropTradingUser.user_id == user_id)
        if status := kwargs.get('status'):
            query = query.filter(PropTradingUser.status == status)
        if reason := kwargs.get('reason'):
            query = query.filter(PropTradingUser.reason == reason)
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        items = pagination.items
        emails = User.query.filter(User.id.in_([item.user_id for item in items])) \
                           .with_entities(User.id, User.email).all()
        emails = dict(emails)
        items = [item.to_dict(enum_to_name=True) for item in items]
        for item in items:
            item['email'] = emails.get(item['user_id'], '')
        return {
            'items': items,
            'total': pagination.total,
            'statuses': {
                'W': '白名单',
                'B': '黑名单',
            },
            'reasons': {
                'MAKER': '做市商',
                'SYSTEM': '系统账户',
                'FROM_QUANT': '量化评估',
                'MANNUAL': '手动添加',
                'COPY_TRADING': '跟单用户',
                'RISK_CONTROL': '风控',
            },
        }

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
        status=EnumField(PropTradingUser.Status, required=True),
    ))
    def post(cls, **kwargs):
        """系统-自营交易-添加用户"""
        user = User.query.get(kwargs['user_id'])
        if not user:
            raise InvalidArgument(message='用户不存在')
        status = kwargs['status']
        if status == PropTradingUser.Status.B:
            reason = PropTradingUser.Reason.MANNUAL
        else:
            reason = PropTradingUser.Reason.NO
        row = PropTradingUser.query.filter(PropTradingUser.user_id == user.id).first()
        if not row:
            db.session.add(PropTradingUser(
                user_id=user.id,
                status=status,
                reason=reason,
                evaluation_time=today(),
                detail=''
            ))
        else:
            if row.status == status:
                raise InvalidArgument(message='该用户已在名单中')
            row.reason = reason
            row.status = status
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.PropTradingUser,
            detail=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )


@ns.route('/prop-trading-report')
@respond_with_code
class PropTradingReportResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            market=fields.String(),
            start=fields.Date,
            end=fields.Date,
            sort=fields.String(),
            page=PageField,
            limit=LimitField(missing=100),
            type=fields.String(missing="daily")
        )
    )
    def get(cls, **kwargs):
        """系统-自营交易-报表"""
        start = kwargs.get("start")
        end = kwargs.get("end")
        market = kwargs.get("market")
        sort = kwargs.get("sort")
        page = kwargs["page"]
        limit = kwargs["limit"]
        typ = kwargs["type"]
        if typ == "daily":
            model = DailyPropTradingReport
        elif typ == "monthly":
            model = MonthlyPropTradingReport
        else:
            raise InvalidArgument
        q = model.query
        if market:
            q = q.filter(model.market == market)
        if start:
            q = q.filter(model.report_date >= start)
        if end:
            q = q.filter(model.report_date <= end)
        if sort:
            if sort.startswith('-'):
                sort = sort[1:]
                _sort = getattr(model, sort).desc()
            else:
                _sort = getattr(model, sort)
            q = q.filter(model.market != 'ALL').order_by(_sort)
        else:
            q = q.order_by(model.report_date.desc())

        pagination = q.paginate(page, limit, error_out=False)

        total = pagination.total
        rows = pagination.items
        result = []
        for row in rows:
            result.append(
                dict(
                    report_date=row.report_date,
                    market=row.market,
                    equity=row.equity,
                    total_profit=row.total_profit,
                    day_profit=row.day_profit,
                    whitelist_user_count=row.whitelist_user_count,
                    op_user_count=row.op_user_count,
                    new_op_user_count=row.new_op_user_count,
                    deal_user_count=row.deal_user_count,
                    deal_amount=row.deal_amount,
                    deal_amount_rate=row.deal_amount_rate * 100,
                    deal_user_rate=row.deal_user_rate * 100,
                    profit_rate=row.profit_rate * 100
                )
            )

        return dict(
            total=total,
            items=result,
            markets=['ALL'] + PropTradingBusiness.get_supported_markets(include_blacklist=True),
        )


@ns.route('/prop-trading-slice')
@respond_with_code
class PropTradingDataViewResource(Resource):

    FIELDS = dict(
        equity="账户权益",
        total_profit="累计盈亏",
        position_amount="净持仓数量",
        position_abs_amount="总持仓数量",
        position_change="仓位变化",
        margin_amount="保证金数量",
        op_user_count="对手盘用户数",
        tbr_user_count="待失效用户数",
        deal_amount_10m="10分钟成交量",
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing="ALL"),
        field=fields.String(missing="equity"),
        latest=fields.String(),
        start=TimestampField(allow_none=True),
        end=TimestampField(allow_none=True),
    ))
    def get(cls, **kwargs):
        """系统-自营交易-监控"""
        market = kwargs['market']
        field = kwargs['field']
        latest = kwargs.get('latest')
        start = kwargs.get('start')
        end = kwargs.get('end')
        if field not in cls.FIELDS:
            raise InvalidArgument

        q = PropTradingSlice.query.filter(
            PropTradingSlice.market == market
        ).with_entities(
            PropTradingSlice.slice_time,
            text(field),
        ).order_by(PropTradingSlice.slice_time.desc())
        # 默认展示最近1天
        if not latest and not start and not end:
            latest = 1
        if start:
            start = int(start.timestamp())
        if end:
            end = int(end.timestamp())
        if latest:
            latest = int(latest)
            tod = int(time.time())
            if latest == 1:  # 最近24小时
                tod -= tod % 600
            else:   # 最近n天
                tod -= tod % 86400
            start = tod - 86400 * latest
            q = q.filter(PropTradingSlice.slice_time >= start)
            interval = cls.get_interval(start, tod)
        else:
            if start:
                q = q.filter(PropTradingSlice.slice_time >= start)
            if end:
                q = q.filter(PropTradingSlice.slice_time <= end)
            interval = cls.get_interval(start or 0, end or int(time.time()))

        if interval > 1:
            interval *= 600
            end = end or int(time.time())
            end -= end % 600
            mod = end % interval
            q = q.filter(PropTradingSlice.slice_time % interval == mod)

        q.with_hint(PropTradingSlice, "FORCE INDEX (market_slice_time)")
        rows = q.all()
        data = [(x, y) for x, y in rows]
        data.reverse()
        state = PropTradingSlice.query.filter(PropTradingSlice.market == market) \
                                .order_by(PropTradingSlice.slice_time.desc()).first()
        _state = {}
        if state:
            for col in PropTradingSlice.__table__.columns:
                if col.name == 'price':
                    continue
                v = getattr(state, col.name)
                if isinstance(v, Decimal):
                    v = amount_to_str(v, 0)
                _state[col.name] = v

        return dict(
            markets=['ALL'] + PropTradingBusiness.get_supported_markets(include_blacklist=True),
            fields=cls.FIELDS,
            state=_state,
            data=data,
        )

    @classmethod
    def get_interval(cls, start, end):
        period = end - start
        if period <= 2 * 86400:
            interval = 1
        elif period <= 7 * 86400:
            interval = 3
        elif period <= 14 * 86400:
            interval = 6
        elif period <= 30 * 86400:
            interval = 12
        elif period <= 60 * 86400:
            interval = 24
        else:
            interval = 144
        return interval


@ns.route('/prop-trading-stat')
@respond_with_code
class PropTradingLatestResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        sort=fields.String(),
        page=PageField,
        limit=LimitField(missing=100),
    ))
    def get(cls, **kwargs):
        """系统-自营交易-统计"""
        sort = kwargs.get("sort")
        page = kwargs["page"]
        limit = kwargs["limit"]
        slice_time  = PropTradingSlice.get_latest_slice_time_with_full_fields()
        rows = PropTradingSlice.query.filter(PropTradingSlice.slice_time == slice_time).all()
        # 避免下面修改字段误提交
        db.session.expunge_all()
        for row in rows:
            row.position_amount = quantize_amount(row.position_amount * row.price, 2)
            row.op_position_abs_amount = quantize_amount(row.op_position_abs_amount * row.price, 2)

        if sort:
            if sort.startswith('-'):
                sort = sort[1:]
                reverse = True
            else:
                reverse = False

            def percent_sorter(a, b):
                if b == 0:
                    return Decimal('inf')
                return a / b
            if sort == 'position_ls_amount_rate':
                rows.sort(key=lambda x: percent_sorter(x.op_position_long_amount, x.op_position_short_amount), reverse=reverse)
            elif sort == 'position_ls_user_count_rate':
                rows.sort(key=lambda x: percent_sorter(x.op_position_long_user_count, x.op_position_short_user_count), reverse=reverse)
            elif sort == 'market':
                rows.sort(key=lambda x: x.market, reverse=reverse)
                for x in rows:
                    if x.market == 'ALL':
                        rows.remove(x)
                        rows.insert(0, x)
                        break
            else:
                rows.sort(key=lambda x: getattr(x, sort), reverse=reverse)

        total = len(rows)
        s = (page - 1) * limit
        e = s + limit
        rows = rows[s:e]

        def percent(a, b):
            if b == 0:
                return '∞'
            return amount_to_str(a / b, 2)

        result = []
        for row in rows:
            item = dict(
                market=row.market,
                total_profit=amount_to_str(row.total_profit, 2),
                curr_position_profit=amount_to_str(row.curr_position_profit, 2),
                profit_24h=amount_to_str(row.profit_24h, 2),
                position_amount=row.position_amount,
                op_position_abs_amount=row.op_position_abs_amount,
                position_ls_amount_rate=percent(row.op_position_long_amount, row.op_position_short_amount),
                position_ls_user_count_rate=percent(row.op_position_long_user_count, row.op_position_short_user_count),
                op_user_count=row.op_user_count,
                tbr_user_count=row.tbr_user_count,
                deal_amount_24h=amount_to_str(row.deal_amount_24h, 2),
                deal_amount_rate=amount_to_str(row.deal_amount_rate * 100, 2)
            )
            result.append(item)
        return dict(
            total=total,
            items=result,
            slice_time=slice_time
        )


@ns.route('/prop-trading-user-stat')
@respond_with_code
class PropTradingUserStatResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        period=fields.Integer(required=True),
        user_id=fields.Integer(),
        position_ge=fields.Integer(),
        win_amount_ge=fields.Decimal(),
        sort=fields.String(missing='-win_amount'),
        page=PageField,
        limit=LimitField(missing=100),
    ))
    def get(cls, **kwargs):
        """系统-自营交易-对手盘统计"""
        period = kwargs['period']
        if period not in PropTradingStatAggregation.PERIODS:
            raise InvalidArgument
        query = PropTradingStatAggregation.query.filter(PropTradingStatAggregation.period == period)
        if user_id := kwargs.get('user_id'):
            query = query.filter(PropTradingStatAggregation.user_id == user_id)
        if position_ge := kwargs.get('position_ge'):
            query = query.filter(PropTradingStatAggregation.position_count >= position_ge)
        if win_amount_ge := kwargs.get('win_amount_ge'):
            query = query.filter(PropTradingStatAggregation.win_amount >= win_amount_ge)

        sort = kwargs['sort']
        if sort.startswith('-'):
            sort = sort[1:]
            reverse = True
        else:
            reverse = False
        col = getattr(PropTradingStatAggregation, sort)
        query = query.order_by(col.desc() if reverse else col.asc())

        records = query.infinite_paginate(kwargs['page'], kwargs['limit'])

        emails = User.query.filter(User.id.in_([x.user_id for x in records.items])) \
                           .with_entities(User.id, User.email).all()
        emails = dict(emails)

        update_time = records.items[0].created_at if records.items else None
        return dict(
            update_time=update_time,
            items=[dict(
                user_id=x.user_id,
                email=emails[x.user_id] or '',
                period=x.period,
                position_count=x.position_count,
                win_count=x.win_count,
                win_amount=x.win_amount,
                win_rate=x.win_rate * 100,
            ) for x in records.items],
            has_next=records.has_next,
            periods=PropTradingStatAggregation.PERIODS,
        )


@ns.route('/kline-boost-setting')
@respond_with_code
class KlineBoostSettingResource(Resource):

    @classmethod
    def get(cls):
        from app.consumer.kline.booster import Calculator

        bizs = {
            KlineBoostSetting.BusinessType.SPOT.name: '现货成交量',
            KlineBoostSetting.BusinessType.PERPETUAL.name: '合约成交量',
            KlineBoostSetting.BusinessType.PERPETUAL_POSITION.name: '合约持仓量',
        }
        rows = KlineBoostSetting.query.all()
        items = {}
        for row in rows:
            item = {
                'end_multiple': row.end_multiple,
                'increase_rate': row.increase_rate,
                'current_multiple': Calculator._calc_multiple(row.business_type)
            }
            delta = item['current_multiple'] - item['end_multiple']
            item['remain_days'] = abs(int(delta / item['increase_rate'])) if delta != 0 else 0
            items[row.business_type.name] = item

        return {
            'business_types': bizs,
            'items': items
        }

    @classmethod
    @ns.use_kwargs(dict(
        business=EnumField(KlineBoostSetting.BusinessType, required=True),
        current_multiple=fields.Decimal(required=True),
        end_multiple=fields.Decimal(required=True),
        increase_rate=fields.Decimal(required=True)
    ))
    def post(cls, **kwargs):
        business = kwargs['business']
        start_multiple = kwargs['current_multiple']
        end_multiple = kwargs['end_multiple']
        increase_rate = kwargs['increase_rate']
        if start_multiple <= 1 or end_multiple <= 1:
            raise InvalidArgument(message="刷量倍率必须大于1")
        if start_multiple < end_multiple:
            if increase_rate <= 0:
                raise InvalidArgument(message="日增长率必须大于0")
        elif start_multiple == end_multiple:
            if increase_rate != 0:
                raise InvalidArgument(message="日增长率必须为0")
        else:
            if increase_rate >= 0:
                raise InvalidArgument(message="日增长率必须小于0")

        row = KlineBoostSetting.query.filter(KlineBoostSetting.business_type == business).first()
        old_data = None
        if not row:
            db.session.add(KlineBoostSetting(
                business_type=business,
                start_day=today(),
                start_multiple=start_multiple,
                end_multiple=end_multiple,
                increase_rate=increase_rate,
            ))
        else:
            old_data = row.to_dict(enum_to_name=True)
            row.start_day = today()
            row.start_multiple = start_multiple
            row.end_multiple = end_multiple
            row.increase_rate = increase_rate
        db.session.commit()

        AdminOperationLog.new_add_or_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.KlineBoostSetting,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            special_data=dict(business=business),
        )


@ns.route('/lang/cn-names')
@respond_with_code
class LangCnNamesResource(Resource):
    @classmethod
    def get(cls):
        return language_name_cn_names()
