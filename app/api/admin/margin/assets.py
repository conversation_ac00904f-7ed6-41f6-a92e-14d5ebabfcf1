# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal

from flask import g, current_app
from webargs import fields
from app.api.common import Resource, Namespace, respond_with_code
from app.api.common.fields import EnumField, PositiveDecimalField, PageField, LimitField
from app.business import get_special_conf_create_operators
from app.business.clients import ServerClient
from app.business.margin.helper import USDT_ASSET
from app.exceptions import InvalidArgument
from app.assets import try_get_asset_config, list_all_assets
from app.models import (
    db, MarginAssetRule, InvestmentAccount, User, UserMarginAssetRule,
    UserSpecialConfigChangeLog, MarginInsurance
)
from app.caches.margin import MarginAssetRuleCache
from app.models.mongo.margin import AssetFloatingRateMySQL
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectSpot, \
    OPNamespaceObjectUser
from app.utils.helper import Struct


ns = Namespace('Margin - Assets')


@ns.route('/rules')
@respond_with_code
class MarginAssetRuleResource(Resource):
    POST_SCHEMA = dict(
        min_loan=PositiveDecimalField(required=True),
        max_loan=PositiveDecimalField(required=True),
        asset=fields.String(required=True),
        period=fields.Integer(required=True),
        basic_day_rate=PositiveDecimalField(required=True),
        status=EnumField(MarginAssetRule.StatusType, enum_by_value=True, required=True),
        invest_status=EnumField(InvestmentAccount.StatusType, enum_by_value=True)
    )

    PUT_SCHEMA = dict(
        id=fields.Integer(required=True),
        min_loan=PositiveDecimalField(),
        max_loan=PositiveDecimalField(),
        period=fields.Integer(),
        basic_day_rate=PositiveDecimalField,
        status=EnumField(MarginAssetRule.StatusType, enum_by_value=True),
        invest_status=EnumField(InvestmentAccount.StatusType, enum_by_value=True)
    )

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(missing=''),
        status=EnumField(MarginAssetRule.StatusType, missing='', enum_by_value=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """杠杆-借币配置"""
        params = Struct(**kwargs)
        rule_query = MarginAssetRule.query
        all_coins = [v.asset for v in rule_query.with_entities(
            MarginAssetRule.asset
        )]
        if params.asset:
            rule_query = rule_query.filter(
                MarginAssetRule.asset == params.asset
            )
        if params.status:
            rule_query = rule_query.filter(
                MarginAssetRule.status == params.status
            )
        records = rule_query.order_by(MarginAssetRule.id.desc()) \
            .paginate(params.page, params.limit)

        def get_invest_status(asset_list):
            q = InvestmentAccount.query.filter(
                InvestmentAccount.asset.in_(asset_list)
            ).with_entities(
                InvestmentAccount.asset,
                InvestmentAccount.status,
            )
            result = defaultdict(lambda: InvestmentAccount.StatusType.CLOSE)
            for v in q:
                result[v.asset] = v.status
            return result

        invest_status_dict = get_invest_status([v.asset for v in records.items])
        _asset = USDT_ASSET
        f_config = AssetFloatingRateMySQL.query.filter(
            AssetFloatingRateMySQL.asset == _asset
        ).first()
        f_rate = f_config.rate if f_config else None
        special_rates = [dict(
            asset=_asset,
            float_rate=f_rate,
        )]
        items = []
        for record in records.items:
            asset_cfg = try_get_asset_config(record.asset)
            d = dict(
                id=record.id,
                created_at=record.created_at,
                updated_at=record.updated_at,
                status=record.status.value,
                invest_status=invest_status_dict[record.asset],
                asset=record.asset,
                reserve_ratio=asset_cfg.reserve_ratio if asset_cfg else Decimal(),
                can_loan_amount=asset_cfg.lendable_amount if asset_cfg else Decimal(),
                min_loan=record.min_loan,
                max_loan=record.max_loan,
                period=record.period,
                basic_day_rate=record.basic_day_rate,
            )
            items.append(d)
        return dict(
            total=records.total,
            items=items,
            special_rates=special_rates,
            coin_list=all_coins,
            status_dict={MarginAssetRule.StatusType.OPEN.value: "开启",
                         MarginAssetRule.StatusType.CLOSE.value: "关闭"},
        )

    @classmethod
    def check_basic_day_rate(cls, basic_day_rate: Decimal):
        if not Decimal(0) <= basic_day_rate <= Decimal('0.01'):
            raise InvalidArgument(message=f"基础利率不在[0,0.01]")

    @classmethod
    @ns.use_kwargs(POST_SCHEMA)
    def post(cls, **kwargs):
        """杠杆-借币配置-添加借币配置"""
        data = Struct(**kwargs)
        all_coins = [v.asset for v in MarginAssetRule.query.with_entities(
            MarginAssetRule.asset
        )]
        if data.asset in all_coins:
            raise InvalidArgument(data.asset)
        cls.check_basic_day_rate(data.basic_day_rate)

        # add margin insurance_obj
        if not MarginInsurance.query.filter(MarginInsurance.asset == data.asset).first():
            insurance_obj = MarginInsurance(
                asset=data.asset,
                amount=Decimal(),
                real_amount=Decimal()
            )
            db.session.add(insurance_obj)
            # noinspection PyBroadException
            try:
                db.session.commit()
            except Exception:
                db.session.rollback()
                current_app.logger.warning(f"insurance {data.asset} exists")

        asset_rule = MarginAssetRule(
            min_loan=data.min_loan,
            max_loan=data.max_loan,
            asset=data.asset,
            period=data.period,
            basic_day_rate=data.basic_day_rate,
            status=data.status,
        )
        db.session.add(asset_rule)
        if "invest_status" in kwargs:
            record = InvestmentAccount.query.filter(
                InvestmentAccount.asset == data.asset
            ).first()
            if record:
                record.status = data.invest_status
            else:
                record = InvestmentAccount(
                    asset=data.asset,
                    status=data.invest_status
                )
                db.session.add(record)
        MarginAssetRuleCache(data.asset).refresh()
        db.session.commit()
        ServerClient().update_assets()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarginAssetRule,
            detail=kwargs,
        )

    @classmethod
    @ns.use_kwargs(PUT_SCHEMA)
    def put(cls, **kwargs):
        """杠杆-借币配置-编辑借币配置"""
        data = Struct(**kwargs)
        record_id = data.id
        asset_rule = MarginAssetRule.query.filter(
            MarginAssetRule.id == record_id
        ).first()
        if not asset_rule:
            raise InvalidArgument(f"id {record_id}")
        if data.basic_day_rate is not None:
            cls.check_basic_day_rate(data.basic_day_rate)
        old_data = asset_rule.to_dict(enum_to_name=True)
        update_fields = ['min_loan', 'max_loan', 'period', 'status', 'basic_day_rate']
        for _f in update_fields:
            if _f in kwargs:
                setattr(asset_rule, _f, kwargs[_f])
        new_data = asset_rule.to_dict(enum_to_name=True)
        if "invest_status" in kwargs:
            record = InvestmentAccount.query.filter(
                InvestmentAccount.asset == asset_rule.asset
            ).first()
            if record:
                old_data['invest_status'] = record.status
                record.status = data.invest_status
            else:
                old_data['invest_status'] = None
                record = InvestmentAccount(
                    asset=asset_rule.asset,
                    status=data.invest_status
                )
                db.session.add(record)
            new_data['invest_status'] = data.invest_status
        MarginAssetRuleCache(asset_rule.asset).refresh()
        db.session.commit()
        ServerClient().update_assets()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarginAssetRule,
            old_data=old_data,
            new_data=new_data,
        )


@ns.route('/info')
@respond_with_code
class MarginAssetInfoResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True)
        )
    )
    def get(cls, **kwargs):
        """支持特殊杠杆借币配置自动填充"""
        params = Struct(**kwargs)
        m = MarginAssetRuleCache(params.asset).dict
        return {
            'max_loan': m['max_loan'],
            'min_loan': m['min_loan']
        }


@ns.route("/user-rules")
@respond_with_code
class UserMarginAssetRuleResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String,
            user_id=fields.Integer,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 用户-特殊配置-特殊杠杆借币额度->列表 """
        rule_q = UserMarginAssetRule.query.filter(UserMarginAssetRule.status == UserMarginAssetRule.StatusType.PASS)
        if asset := kwargs.get("asset", "").strip():
            rule_q = rule_q.filter(UserMarginAssetRule.asset == asset)
        if user_id := kwargs.get("user_id"):
            rule_q = rule_q.filter(UserMarginAssetRule.user_id == user_id)

        pagination = rule_q.order_by(UserMarginAssetRule.id.desc()).paginate(kwargs["page"], kwargs["limit"])
        records = pagination.items
        user_ids = list({i.user_id for i in records})
        user_email_map = dict(
            User.query.filter(User.id.in_(user_ids))
            .with_entities(
                User.id,
                User.email,
            )
            .all()
        )
        record_ids = [i.id for i in records]
        operator_id_dict, operator_name_dict = get_special_conf_create_operators(
            record_ids,
            UserSpecialConfigChangeLog.SpecialConfigType.MARGIN_LOAN_LIMIT)
        return dict(
            items=[
                {
                    "id": i.id,
                    "user_id": i.user_id,
                    "email": user_email_map.get(i.user_id),
                    "asset": i.asset,
                    "max_loan": i.max_loan,
                    "remark": i.remark,
                    "operator": operator_name_dict.get(i.id),
                    "operator_id": operator_id_dict.get(i.id)
                }
                for i in records
            ],
            total=pagination.total,
            extra=dict(
                assets=list_all_assets(),
            )
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            asset=fields.String(required=True),
            max_loan=PositiveDecimalField(required=True),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-特殊杠杆借币额度->新增 """
        user_id = kwargs["user_id"]
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(f"{user_id}不存在")

        max_loan = kwargs["max_loan"]
        asset = kwargs["asset"]
        cls.validate_asset(asset)
        exist_rule = UserMarginAssetRule.query.filter(
            UserMarginAssetRule.user_id == user_id,
            UserMarginAssetRule.asset == asset,
        ).first()
        if exist_rule:
            old_data = exist_rule.to_dict(enum_to_name=True)
            exist_rule.status = UserMarginAssetRule.StatusType.PASS
            exist_rule.max_loan = max_loan
            exist_rule.remark = kwargs.get("remark", "")
            row = exist_rule

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SpecialMarginAssetRule,
                old_data=old_data,
                new_data=exist_rule.to_dict(enum_to_name=True),
                target_user_id=user_id,
            )
        else:
            record = UserMarginAssetRule(
                user_id=user_id,
                asset=asset,
                max_loan=max_loan,
                remark=kwargs.get("remark", ""),
                status=UserMarginAssetRule.StatusType.PASS,
            )
            db.session.add(record)
            row = record

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SpecialMarginAssetRule,
                detail=kwargs,
                target_user_id=user_id,
            )
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=row.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.MARGIN_LOAN_LIMIT,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail=row.record_detail,
            change_remark=kwargs.get('remark'),
            op_id=row.id
        )

    @classmethod
    def validate_asset(cls, asset):
        model = MarginAssetRule
        rule = model.query.filter(
            model.asset == asset,
            model.status == model.StatusType.OPEN,
        ).first()
        if not rule:
            raise InvalidArgument(message=f'ensure margin asset rule does exist and is open')

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            user_id=fields.Integer(required=True),
            asset=fields.String(required=True),
            max_loan=PositiveDecimalField(required=True),
            remark=fields.String(missing=""),
        )
    )
    def put(cls, **kwargs):
        """ 用户-特殊配置-特殊杠杆借币额度->编辑 """
        user_id = kwargs["user_id"]
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(f"用户{user_id}不存在")

        id_ = kwargs["id"]
        max_loan = kwargs["max_loan"]
        asset = kwargs["asset"]
        to_update_rule = UserMarginAssetRule.query.get(id_)
        if to_update_rule.asset != asset:
            exist_asset_rule = UserMarginAssetRule.query.filter(
                UserMarginAssetRule.user_id == user_id,
                UserMarginAssetRule.asset == asset,
            ).first()
            if exist_asset_rule and exist_asset_rule.id != id_:
                raise InvalidArgument(f"用户币种{asset}已存在特殊杠杆日息")
        old_data = to_update_rule.to_dict(enum_to_name=True)

        to_update_rule.status = UserMarginAssetRule.StatusType.PASS
        to_update_rule.asset = asset
        to_update_rule.max_loan = max_loan
        to_update_rule.remark = kwargs.get("remark", "")
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=to_update_rule.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.MARGIN_LOAN_LIMIT,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail=to_update_rule.record_detail,
            change_remark=kwargs.get('remark'),
            op_id=to_update_rule.id
        )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialMarginAssetRule,
            old_data=old_data,
            new_data=to_update_rule.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 用户-特殊配置-特殊杠杆借币额度->删除 """
        id_ = kwargs["id"]
        rule = UserMarginAssetRule.query.get(id_)
        rule.status = UserMarginAssetRule.StatusType.DELETE
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=rule.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.MARGIN_LOAN_LIMIT,
            op_type=UserSpecialConfigChangeLog.OpType.DELETE,
            admin_user_id=g.user.id,
            change_detail=rule.record_detail,
            op_id=rule.id
        )

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialMarginAssetRule,
            detail=dict(id=id_, asset=rule.asset, max_loan=rule.max_loan, remark=rule.remark),
            target_user_id=rule.user_id,
        )
