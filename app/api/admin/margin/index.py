# -*- coding: utf-8 -*-
from collections import defaultdict

from flask import current_app, g
from marshmallow import EXCLUDE, Schema
from sqlalchemy import or_
from webargs import fields
from webargs.flaskparser import use_kwargs

from ...common import Resource, Namespace, respond_with_code
from ...common.fields import EnumField, PageField, LimitField
from ....business import ServerClient
from ....caches import MarginExchangeAssetMappingCache
from ....exceptions import InvalidArgument
from ....models import MarginIndexDetail, MarginIndex, \
    db, ComposeIndex, Market
from ....models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectSpot
from ....utils import now
from ....utils.helper import Struct
from ....utils.parser import dict2schema


ns = Namespace('Margin - Index')


@ns.route('')
@respond_with_code
class MarginIndexResource(Resource):
    class DetailSchema(Schema):
        exchange_name = EnumField(MarginIndexDetail.ExchangeNameType, required=True,
                                  enum_by_value=True)
        weight = fields.Decimal(required=True)

        class Meta:
            UNKNOWN = EXCLUDE

    POST_SCHEMA = dict(
        market_name=fields.String(required=True, validate=lambda x: x.isupper()),
        price_precision=fields.Integer(required=True),
        risk_check_time=fields.Integer(required=True),
        status=EnumField(MarginIndex.StatusType, enum_by_value=True, required=True),
        details=fields.Nested(DetailSchema, many=True, required=True)
    )

    PUT_SCHEMA = dict(
        id=fields.Integer(),
        market_name=fields.String(),
        price_precision=fields.Integer(),
        risk_check_time=fields.Integer(),
        status=EnumField(MarginIndex.StatusType, enum_by_value=True),
        details=fields.Nested(DetailSchema, many=True)
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        status=EnumField(MarginIndex.StatusType, missing='', enum_by_value=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """币币-杠杆配置-普通指数配置"""
        params = Struct(**kwargs)
        index_query = MarginIndex.query
        all_markets = [v.name for v in Market.query.all()]
        if params.market:
            index_query = index_query.filter(
                MarginIndex.market_name == params.market
            )
        if params.status:
            index_query = index_query.filter(
                MarginIndex.status == params.status
            )
        records = index_query.order_by(MarginIndex.id.desc()) \
            .paginate(params.page, params.limit)

        def get_index_details(index_ids):
            q = MarginIndexDetail.query.filter(
                MarginIndexDetail.margin_index_id.in_(index_ids),
                MarginIndexDetail.status == MarginIndexDetail.StatusType.PASS)
            result = defaultdict(list)
            for v in q:
                result[v.margin_index_id].append(
                    dict(weight=v.weight,
                         exchange_name=v.exchange_name
                         )
                )
            return result

        page_index_ids = [v.id for v in records.items]
        index_detail_dict = get_index_details(page_index_ids)
        cache_mapping = {v.value: MarginExchangeAssetMappingCache(v.value)
                         for v in MarginIndexDetail.ExchangeNameType}
        result = {
            _v: _cache.hgetall()
            for _v, _cache in cache_mapping.items()
        }
        special_items = []
        for name, v in result.items():
            for old_symbol, new_symbol in v.items():
                special_items.append(
                    dict(exchange_name=name,
                         old=old_symbol,
                         new=new_symbol)
                )
        return dict(
            total=records.total,
            special_items=special_items,
            items=[dict(
                id=record.id,
                created_at=record.created_at,
                updated_at=record.updated_at,
                market_name=record.market_name,
                price_precision=int(record.price_precision),
                risk_check_time=record.risk_check_time,
                status=record.status.value,
                details=index_detail_dict[record.id],
            ) for record in records.items],
            market_list=all_markets,
            status_dict={MarginIndex.StatusType.OPEN.value: "开启",
                         MarginIndex.StatusType.CLOSE.value: "关闭"},
            exchange_names={v.value for v in MarginIndexDetail.ExchangeNameType
                            if v not in MarginIndexDetail.EXCLUDE_EXCHANGES}
        )

    # TODO: nested schema validate error.
    @classmethod
    @use_kwargs(POST_SCHEMA)
    def post(cls, **kwargs):
        """币币-杠杆配置-普通指数配置-添加普通指数配置"""
        data = Struct(**kwargs)
        all_markets = [v.market_name for v in
                       MarginIndex.query.with_entities(MarginIndex.market_name)]
        if data.market_name in all_markets:
            raise InvalidArgument(data.market_name)
        if len(data.details) == 0:
            raise InvalidArgument('index details')
        details_config = [
            MarginIndexDetail(
                exchange_name=v['exchange_name'],
                weight=v['weight'],
                status=MarginIndexDetail.StatusType.PASS
            ) for v in data.details]
        index_config = MarginIndex(
            status=data.status,
            price_precision=data.price_precision,
            risk_check_time=data.risk_check_time,
            market_name=data.market_name,
            margin_index_details=details_config
        )
        db.session.add(index_config)
        db.session.add_all(details_config)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarginIndex,
            detail=kwargs,
        )

    @classmethod
    @use_kwargs(dict2schema(PUT_SCHEMA)(unknown=EXCLUDE))
    def put(cls, **kwargs):
        """币币-杠杆配置-普通指数配置-编辑普通指数配置"""
        data = Struct(**kwargs)
        record_id = data.id
        record = MarginIndex.query.filter(
            MarginIndex.id == record_id
        ).first()
        if not record:
            raise InvalidArgument(f"id {record_id}")
        old_record_data = record.to_dict(enum_to_name=True)
        if "status" in kwargs:
            if data.status == MarginIndex.StatusType.CLOSE:
                cls.check_not_in_composite(record)
            record.status = data.status
        if "price_precision" in kwargs:
            record.price_precision = data.price_precision
        if "risk_check_time" in kwargs:
            record.risk_check_time = data.risk_check_time
        if "details" in kwargs:
            MarginIndexDetail.query.filter(
                MarginIndexDetail.margin_index_id == record_id
            ).update(
                {MarginIndexDetail.status: MarginIndexDetail.StatusType.DELETE},
                synchronize_session=False
            )
            details_config = [
                MarginIndexDetail(
                    margin_index_id=record_id,
                    exchange_name=v['exchange_name'],
                    weight=v['weight'],
                    status=MarginIndexDetail.StatusType.PASS
                ) for v in data.details]
            db.session.add_all(details_config)
        record.updated_at = now()
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarginIndex,
            old_data=old_record_data,
            new_data=record.to_dict(enum_to_name=True),
            special_data=dict(details=data.details) if data.details else None,
        )

    @classmethod
    def check_not_in_composite(cls, row: MarginIndex):
        compose_row = ComposeIndex.query.filter(
            or_(
                ComposeIndex.first_market == row.market_name,
                ComposeIndex.second_market == row.market_name,
            ),
            ComposeIndex.status == ComposeIndex.StatusType.OPEN,
        ).first()
        if compose_row:
            raise InvalidArgument(f"当前配置id:{row.id} 包含在 合成指数配置id:{compose_row.id}中")


@ns.route('/special-asset/mapping')
@respond_with_code
class MarginSpecialIndexMappingResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            exchange_name=EnumField(MarginIndexDetail.ExchangeNameType, required=True,
                                    enum_by_value=True),
            old=fields.String(required=True),
            new=fields.String(required=True)
        )
    )
    def post(cls, **kwargs):
        """杠杆-指数价格-特殊币种映射配置修改"""
        exchange_name, old, new = kwargs["exchange_name"].value, kwargs["old"], kwargs["new"]
        cache = MarginExchangeAssetMappingCache(exchange_name)
        cache.hset(old, new)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarginSpecialIndexMapping,
            detail=kwargs,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            exchange_name=EnumField(MarginIndexDetail.ExchangeNameType, required=True,
                                    enum_by_value=True),
            old=fields.String(required=True),
            new=fields.String(required=True)
        )
    )
    def delete(cls, **kwargs):
        """杠杆-指数价格-特殊币种映射配置删除"""
        exchange_name, old = kwargs["exchange_name"].value, kwargs["old"]
        cache = MarginExchangeAssetMappingCache(exchange_name)
        cache.hdel(old)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarginSpecialIndexMapping,
            detail=kwargs,
        )


@ns.route('/update-config')
@respond_with_code
class UpdateConfigResource(Resource):

    @classmethod
    def put(cls):
        """杠杆-通知更新杠杆指数配置"""
        client = ServerClient(current_app.logger)
        client.update_index()
