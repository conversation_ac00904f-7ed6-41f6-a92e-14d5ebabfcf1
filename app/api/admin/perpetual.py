# -*- coding: utf-8 -*-
import math
import json
from collections import defaultdict
from datetime import timedelta, datetime
from decimal import Decimal, DecimalException
from enum import Enum
from typing import Dict

from webargs import fields as wa_fields
from flask_babel import gettext
from flask import current_app, g
from flask_restx import fields, marshal
from marshmallow import Schema, EXCLUDE
from sqlalchemy import func, distinct

from app.business.perpetual.position import get_profit_unreal

from ..common import (Resource, Namespace,
                      respond_with_code)
from ..common import ex_fields
from ..common.fields import PageField, LimitField, EnumField, \
    TimestampMarshalField, AmountField, DateField, PositiveDecimalField
from ... import Language
from ...business import PerpetualServerClient, PriceManager
from ...business.export.perpetual import get_liquidation_records, export_liquidation_records, get_insurance_summary_data
from ...business.external_dbs import PerpetualSysHistoryDB, \
    PerpetualHistoryDB, PerpetualSummaryDB
from ...business.trade_rank import RankAdaptor
from ...caches import PerpetualMarketCache, PerpetualCoinTypeCache, PerpetualOfflineMarketCache
from ...common import OrderSideType, PositionType, PositionSide, ReportType, \
    PerpetualMarketType, position_deal_type_map, ADMIN_EXPORT_LIMIT
from ...exceptions import InvalidArgument
from ...models import User, UserTradeSummary, \
    MarketSummaryHistory, DailyPerpetualMarketReport, \
    MonthlyPerpetualTradeReport, DailyPerpetualTradeReport, \
    MonthlyPerpetualMarketReport, DailyPerpetualInsuranceReport, \
    MonthlyPerpetualInsuranceReport, UserTradeFeeSummary, db, PerpetualMarketIndex, \
    PerpetualMarketIndexDetail, PerpetualComposeIndex, PerpetualMarket, \
    PerpetualMarketLimitConfig, PerpetualAssetIndex
from ...models.spot import MarketOfflineContent
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectPerpetual
from ...business.order import Order
from ...utils import offset_to_page, current_timestamp, \
    amount_to_str, now, datetime_to_time, query_to_page, quantize_amount, export_xlsx, format_percent, datetime_to_str
from ...utils.date_ import date_to_datetime
from ...utils.helper import Struct
from ...utils.parser import JsonEncoder
from .spot import LastHour

ns = Namespace('Perpetual')


def lambda_validate_precision(x):
    return 20 >= x >= 0


def lambda_positive_int(x):
    return x >= 0


@ns.route('/markets')
@respond_with_code
class MarketData(Resource):

    @staticmethod
    def get():
        """市场信息"""
        markets_data = PerpetualMarketCache().read_aside()
        offline_markets_data = PerpetualOfflineMarketCache().read_aside()
        markets_data.update(offline_markets_data)
        market_list = sorted(list(markets_data.keys()))
        return dict(
            market_list=market_list,
            market_info=markets_data,
            asset_list=sorted(PerpetualCoinTypeCache().read_aside()),
            order_side=dict(SELL='卖出', BUY='买入'),
            position_side=dict(LONG='多仓', SHORT='空仓'),
            position_type=dict(ISOLATED_MARGIN='逐仓', CROSS_MARGIN='全仓'),
            insurance_type={1: '平台注入', 2: '穿仓垫付'},
            order_type_data=dict(LIMIT='限价', MARKET='市价',
                                 STOP_LIMIT='计划限价', STOP_MARKET='计划市价')
        )


@ns.route('/depth')
@respond_with_code
class DepthList(Resource):
    marshal_fields = {
        'create_time': fields.Integer,
        'user_id': fields.Integer,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'deal_amount': ex_fields.AmountField(
            attribute=lambda x: Decimal(x['amount']) - Decimal(x['left'])),
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        side=ex_fields.EnumField(enum=OrderSideType, required=True),
        order_type=ex_fields.EnumField(enum=['normal'], required=True),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
    ))
    def get(cls, **kwargs):
        """
        永续深度成交-深度列表
        """
        market = kwargs['market']
        side = kwargs['side']
        page = kwargs['page']
        limit = kwargs['limit']

        client = PerpetualServerClient()
        if kwargs['order_type'] == 'normal':
            result = client.query_order_book(market, side, page, limit)
        else:
            result = client.query_stop_order_book(market, side, page, limit)
        result['records'] = marshal(result['records'], cls.marshal_fields)
        return offset_to_page(result)


@ns.route('/latest-deals')
@respond_with_code
class LatestDeals(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String(
            attribute=lambda x: '卖出' if x['type'] == 'sell' else '买入'),
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        limit=ex_fields.LimitField,
    ))
    def get(cls, **kwargs):
        """
        永续深度成交-最新成交
        """
        market = kwargs['market']
        limit = kwargs['limit']

        client = PerpetualServerClient()
        result = client.query_latest_deals(market, limit)
        return marshal(result, cls.marshal_fields)


@ns.route('/realtime-net-rank')
@respond_with_code
class RealtimeNetRank(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        time_type=ex_fields.EnumField(
            enum=['15min', '1h', '6h', '24h', '72h', '7d'], missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
    ))
    def get(cls, **kwargs):
        """
        永续-交易排名-实时净买卖
        """
        market = kwargs['market']
        time_type = kwargs['time_type']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        now_ts = current_timestamp(to_int=True)
        if start_time and end_time:
            start_time = int(start_time / 1000)
            end_time = int(end_time / 1000)
        else:
            start_time, end_time = now_ts - 86400, now_ts

        if time_type == '15min':
            start_time, end_time = now_ts - 15 * 60, now_ts
            end_time = now_ts
        elif time_type == '1h':
            start_time, end_time = now_ts - 1 * 3600, now_ts
        elif time_type == '6h':
            start_time, end_time = now_ts - 6 * 3600, now_ts
        elif time_type == '24h':
            start_time, end_time = now_ts - 86400, now_ts
        elif time_type == '72h':
            start_time, end_time = now_ts - 3 * 86400, now_ts
        elif time_type == '7d':
            start_time, end_time = now_ts - 7 * 86400, now_ts

        if end_time - start_time > 7 * 86400:
            raise InvalidArgument(message='仅支持七天实时数据')

        client = PerpetualServerClient()

        result = client.query_net_rank(market, start_time, end_time)
        p = RankAdaptor('contract', 'market_rank').get_processor()
        return dict(
            rank_data=dict(
                buy=[r for r in result['buy'] if Decimal(r['net']) > 0][:200],
                sell=[r for r in result['sell'] if Decimal(r['net']) > 0][:200],
            ),
            summary_data=p.process(result),
            amount_asset=PerpetualMarketCache.get_amount_asset(market),
            balance_asset=PerpetualMarketCache.get_balance_asset(market)
        )


@ns.route('/realtime-deal-rank')
@respond_with_code
class RealtimeDealRank(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        time_type=ex_fields.EnumField(
            enum=['15min', '1h', '6h', '24h', '72h', '7d'], missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
    ))
    def get(cls, **kwargs):
        """
        永续-交易排名-实时成交量
        """
        market = kwargs['market']
        time_type = kwargs['time_type']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        now_ts = current_timestamp(to_int=True)
        if start_time and end_time:
            start_time = int(start_time / 1000)
            end_time = int(end_time / 1000)
        else:
            start_time, end_time = now_ts - 86400, now_ts

        if time_type == '15min':
            start_time, end_time = now_ts - 15 * 60, now_ts
            end_time = now_ts
        elif time_type == '1h':
            start_time, end_time = now_ts - 1 * 3600, now_ts
        elif time_type == '6h':
            start_time, end_time = now_ts - 6 * 3600, now_ts
        elif time_type == '24h':
            start_time, end_time = now_ts - 86400, now_ts
        elif time_type == '72h':
            start_time, end_time = now_ts - 3 * 86400, now_ts
        elif time_type == '7d':
            start_time, end_time = now_ts - 7 * 86400, now_ts

        if end_time - start_time > 7 * 86400:
            raise InvalidArgument(message='仅支持七天实时数据')

        client = PerpetualServerClient()
        result = client.query_amount_rank(market, start_time, end_time)
        p = RankAdaptor('contract', 'realtime_deal').get_processor()
        return dict(
            rank_data=dict(
                buy=[dict(
                    amount=quantize_amount(r['amount'], 8),
                    total_amount=quantize_amount(r['total_amount'], 8),
                    user_id=r['user_id']
                ) for r in result['buy']
                        if Decimal(r['amount']) > 0][:200],
                sell=[dict(
                    amount=quantize_amount(r['amount'], 8),
                    total_amount=quantize_amount(r['total_amount'], 8),
                    user_id=r['user_id']
                ) for r in result['sell']
                         if Decimal(r['amount']) > 0][:200],
            ),
            summary_data=p.process(result),
            amount_asset=PerpetualMarketCache.get_amount_asset(market),
            balance_asset=PerpetualMarketCache.get_balance_asset(market)
        )


@ns.route('/history-net-rank')
@respond_with_code
class HistoryNetRank(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        time_type=ex_fields.EnumField(
            enum=['7d', '30d', '90d', '180d', '365d'], missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
    ))
    def get(cls, **kwargs):
        """
        永续-交易排名-历史净买卖
        """
        market = kwargs['market']
        time_type = kwargs['time_type']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        now_ts = current_timestamp(to_int=True)
        if start_time and end_time:
            start_time = int(start_time / 1000)
            end_time = int(end_time / 1000)
        else:
            start_time, end_time = now_ts - 86400, now_ts

        if time_type == '7d':
            start_time, end_time = now_ts - 7 * 86400, now_ts
            end_time = now_ts
        elif time_type == '30d':
            start_time, end_time = now_ts - 30 * 86400, now_ts
        elif time_type == '90d':
            start_time, end_time = now_ts - 90 * 86400, now_ts
        elif time_type == '180d':
            start_time, end_time = now_ts - 180 * 86400, now_ts
        elif time_type == '365d':
            start_time, end_time = now_ts - 365 * 86400, now_ts

        if end_time - start_time > 365 * 86400:
            raise InvalidArgument(message='仅支持365天历史数据')

        records = PerpetualSummaryDB.get_trade_summary(
            start_time, end_time, market)

        buy_dict = defaultdict(dict)
        sell_dict = defaultdict(dict)
        for record in records:
            user_id = record['user_id']
            deal_amount = record['deal_amount']
            buy_amount = record['buy_amount']
            sell_amount = record['sell_amount']

            user_buy = buy_dict[user_id]
            user_buy['user_id'] = user_id
            user_buy['net'] = user_buy.get('net', 0) + (
                    buy_amount - sell_amount)
            user_buy['deal'] = user_buy.get('deal', 0) + deal_amount

            sell_buy = sell_dict[user_id]
            sell_buy['user_id'] = user_id
            sell_buy['net'] = sell_buy.get('net', 0) + (
                    sell_amount - buy_amount)
            sell_buy['deal'] = sell_buy.get('deal', 0) + deal_amount

        buy_list = [buy_value for user_id, buy_value in buy_dict.items()]
        sell_list = [sell_value for user_id, sell_value in sell_dict.items()]

        buy_list.sort(key=lambda x: x['net'], reverse=True)
        sell_list.sort(key=lambda x: x['net'], reverse=True)

        valid_buy_list = []
        valid_sell_list = []

        for buy in buy_list:
            if buy['net'] >= 0:
                valid_buy_list.append(buy)

        for sell in sell_list:
            if sell['net'] > 0:
                valid_sell_list.append(sell)

        p = RankAdaptor('contract', 'history_net').get_processor()
        return dict(
            rank_data=dict(
                buy=valid_buy_list[:200],
                sell=valid_sell_list[:200],
            ),
            summary_data=p.process(dict(
                buy=valid_buy_list,
                sell=valid_sell_list
            )),
            amount_asset=PerpetualMarketCache.get_amount_asset(market),
            balance_asset=PerpetualMarketCache.get_balance_asset(market)
        )


@ns.route('/history-deal-rank')
@respond_with_code
class HistoryDealRank(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        time_type=ex_fields.EnumField(
            enum=['7d', '30d', '90d', '180d', '365d'], missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
    ))
    def get(cls, **kwargs):
        """
        永续-交易排名-历史成交量
        """
        market = kwargs['market']
        time_type = kwargs['time_type']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        now_ts = current_timestamp(to_int=True)
        if start_time and end_time:
            start_time = int(start_time / 1000)
            end_time = int(end_time / 1000)
        else:
            start_time, end_time = now_ts - 86400, now_ts

        if time_type == '7d':
            start_time, end_time = now_ts - 7 * 86400, now_ts
            end_time = now_ts
        elif time_type == '30d':
            start_time, end_time = now_ts - 30 * 86400, now_ts
        elif time_type == '90d':
            start_time, end_time = now_ts - 90 * 86400, now_ts
        elif time_type == '180d':
            start_time, end_time = now_ts - 180 * 86400, now_ts
        elif time_type == '365d':
            start_time, end_time = now_ts - 365 * 86400, now_ts

        if end_time - start_time > 365 * 86400:
            raise InvalidArgument(message='仅支持365天历史数据')

        records = PerpetualSummaryDB.get_trade_summary(
            start_time, end_time, market)

        buy_dict = defaultdict(dict)
        sell_dict = defaultdict(dict)
        for record in records:
            user_id = record['user_id']
            deal_amount = record['deal_amount']
            buy_amount = record['buy_amount']
            sell_amount = record['sell_amount']

            user_buy = buy_dict[user_id]
            user_buy['user_id'] = user_id
            user_buy['total'] = user_buy.get('total', 0) + buy_amount
            user_buy['deal'] = user_buy.get('deal', 0) + deal_amount

            sell_buy = sell_dict[user_id]
            sell_buy['user_id'] = user_id
            sell_buy['total'] = sell_buy.get('total', 0) + sell_amount
            sell_buy['deal'] = sell_buy.get('deal', 0) + deal_amount

        buy_list = [buy_value for user_id, buy_value in buy_dict.items()]
        sell_list = [sell_value for user_id, sell_value in sell_dict.items()]

        buy_list.sort(key=lambda x: x['total'], reverse=True)
        sell_list.sort(key=lambda x: x['total'], reverse=True)

        valid_buy_list = []
        valid_sell_list = []

        for buy in buy_list:
            if buy['total'] > 0:
                valid_buy_list.append(buy)

        for sell in sell_list:
            if sell['total'] > 0:
                valid_sell_list.append(sell)

        p = RankAdaptor('contract', 'history_deal').get_processor()
        return dict(
            rank_data=dict(
                buy=valid_buy_list[:200],
                sell=valid_sell_list[:200],
            ),
            summary_data=p.process(dict(
                buy=valid_buy_list,
                sell=valid_sell_list
            )),
            amount_asset=PerpetualMarketCache.get_amount_asset(market),
            balance_asset=PerpetualMarketCache.get_balance_asset(market)
        )


@ns.route('/user-trade-summary-rank')
@respond_with_code
class UserTradeSummaryRank(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "user_type", Language.ZH_HANS_CN: "账户类型"},
        {"field": "trade_amount", Language.ZH_HANS_CN: "成交市值（USD）"},
        {"field": "trade_amount_rate", Language.ZH_HANS_CN: "成交市值全站占比"},
        {"field": "trade_fee_amount", Language.ZH_HANS_CN: "手续费市值（USD）"},
        {"field": "fee_ratio", Language.ZH_HANS_CN: "平均费率"},
        {"field": "rank", Language.ZH_HANS_CN: "排名"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        id=wa_fields.Integer(missing=None),
        start_date=wa_fields.String(required=True),
        end_date=wa_fields.String(required=True),
        page=ex_fields.PageField(unlimited=True),
        sort_type=wa_fields.String(missing='trade_amount'),
        limit=ex_fields.LimitField,
        user_type=wa_fields.String,
        export=wa_fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """永续-交易排名-用户总成交量"""
        from .users import UsersResource
        user_id = kwargs['id']
        start_date = kwargs['start_date']
        end_date = kwargs['end_date']
        page = kwargs['page']
        limit = kwargs['limit']

        query = UserTradeSummary.query.filter(
            UserTradeSummary.system == UserTradeSummary.System.PERPETUAL,
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date <= end_date,
        )
        # 强制指定索引，避免sql优化器扫全表
        daily_trade_amount_record = query.group_by(UserTradeSummary.report_date).with_entities(
            UserTradeSummary.report_date,
            func.sum(UserTradeSummary.trade_amount)
        ).with_hint(UserTradeSummary, "FORCE INDEX(report_date_system_user_id)").all()
        daily_trade_amount_map = dict(daily_trade_amount_record)
        total_amount = sum(daily_trade_amount_map.values())

        fee_query = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.system == UserTradeFeeSummary.System.PERPETUAL,
            UserTradeFeeSummary.report_date >= start_date,
            UserTradeFeeSummary.report_date <= end_date,
        )
        if user_type := kwargs.get('user_type'):
            query = query.join(User).filter(
                UserTradeSummary.user_id == User.id,
                User.user_type == user_type,
            )
            fee_query = fee_query.join(User).filter(
                UserTradeFeeSummary.user_id == User.id,
                User.user_type == user_type,
            )

        records = []
        total = 0
        if user_id:
            user = User.query.get(user_id)
            query = query.filter(
                UserTradeSummary.user_id == user_id
            ).order_by(
                UserTradeSummary.report_date.desc()
            )
            fee_query = fee_query.filter(UserTradeFeeSummary.user_id == user_id)
            user_fee = fee_query.all()

            user_fee_date_map = defaultdict(Decimal)
            for _user in user_fee:
                user_fee_date_map[_user.report_date] = _user.trade_fee_amount

            if kwargs['export']:
                user_trade_list = query.all()
            else:
                paginate = query.paginate(page, limit, error_out=False)
                user_trade_list = paginate.items
                total = paginate.total

            for item in user_trade_list:
                total_amount = daily_trade_amount_map.get(item.report_date, 0)
                records.append({
                    'report_date': item.report_date.strftime("%Y-%m-%d"),
                    'user_id': item.user_id,
                    'trade_amount': quantize_amount(item.trade_amount, 2),
                    "trade_amount_rate": amount_to_str(
                        (item.trade_amount / total_amount) * 100 if total_amount else Decimal(), 4) + '%',
                    'trade_fee_amount': quantize_amount(user_fee_date_map[item.report_date], 2),
                    'fee_ratio': amount_to_str(user_fee_date_map[item.report_date] / item.trade_amount * 100, 2) + '%',
                    'rank': item.rank,
                    'email': user.main_user_email,
                    'user_type': UsersResource.USER_TYPES[user.user_type]
                })
        else:
            user_trade_query = query.with_entities(
                func.sum(UserTradeSummary.trade_amount).label('trade_amount'),
                UserTradeSummary.user_id
            ).group_by(
                UserTradeSummary.user_id
            ).order_by(
                func.sum(UserTradeSummary.trade_amount).desc()
            )

            if kwargs['export']:
                user_trade_list = user_trade_query.all()
            else:
                paginate = user_trade_query.paginate(page, limit, error_out=False)
                user_trade_list = list(paginate.items)
                total = paginate.total

            user_ids = [item.user_id for item in user_trade_list]
            user_fee = fee_query.filter(UserTradeFeeSummary.user_id.in_(user_ids)).with_entities(
                func.sum(UserTradeFeeSummary.trade_fee_amount).label('trade_fee_amount'),
                UserTradeFeeSummary.user_id
            ).group_by(
                UserTradeFeeSummary.user_id
            ).all()
            user_fee_map = defaultdict(Decimal)
            for user in user_fee:
                user_fee_map[user.user_id] = user.trade_fee_amount
            rank = (page - 1) * limit
            user_data_map = {i.id: i for i in User.query.filter(User.id.in_(user_ids))}
            for item in user_trade_list:
                rank += 1
                records.append({
                    'report_date': '{start_date} - {end_date}'.format(
                        start_date=start_date, end_date=end_date),
                    'user_id': item.user_id,
                    'trade_amount': quantize_amount(item.trade_amount, 2),
                    "trade_amount_rate": amount_to_str(
                        (item.trade_amount / total_amount) * 100 if total_amount else Decimal(), 4) + '%',
                    'trade_fee_amount': quantize_amount(user_fee_map[item.user_id], 2),
                    'fee_ratio': (amount_to_str(user_fee_map[item.user_id] / item.trade_amount * 100
                                                if item.trade_amount else Decimal(), 2)) + '%',
                    'rank': rank,
                    'email': user_data_map[item.user_id].email,
                    'user_type': UsersResource.USER_TYPES[user_data_map[item.user_id].user_type],
                })

            if kwargs['export']:
                return export_xlsx(
                    filename='perpetual_trade_user',
                    data_list=records,
                    export_headers=cls.export_headers
                )
        if kwargs['export']:
            return export_xlsx(
                filename='perpetual_trade_user',
                data_list=records,
                export_headers=cls.export_headers
            )
        fee_result = fee_query.with_entities(
            func.sum(UserTradeFeeSummary.trade_fee_amount).label(
                'total_amount')
        ).first()
        total_amount = query.with_entities(
            func.sum(UserTradeSummary.trade_amount)
        ).scalar() or Decimal()

        total_fee_amount = fee_result.total_amount \
            if fee_result.total_amount else Decimal()
        count_query = query.with_entities(
            func.count(distinct(UserTradeSummary.user_id)).label('user_count')
        ).first()
        total_count = count_query.user_count if count_query else 0
        return dict(
            total=total,
            records=records,
            summary_data=dict(
                total_amount=quantize_amount(total_amount, 2),
                total_fee_amount=quantize_amount(total_fee_amount, 2),
                fee_ratio=(str(quantize_amount((total_fee_amount / total_amount * 100),
                                               4)) if total_amount else '0') + '%',
                total_count=total_count
            ),
            user_types={item.name: gettext(item.value) for item in User.UserType}
        )


@ns.route('/realtime-price-rank')
@respond_with_code
class RealtimePriceRank(Resource):
    class Bid(Enum):
        BID_RANK_DESC = 'bid_rank_desc'
        BID_RANK_ASC = 'bid_rank_asc'

    class Ask(Enum):
        ASK_RANK_DESC = 'ask_rank_desc'
        ASK_RANK_ASC = 'ask_rank_asc'

    @classmethod
    def get_rank(cls, rank):
        if rank in (cls.Ask.ASK_RANK_DESC, cls.Bid.BID_RANK_DESC):
            return max, True
        return min, False

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        bid_rank=EnumField(Bid, missing=Bid.BID_RANK_DESC),
        ask_rank=EnumField(Ask, missing=Ask.ASK_RANK_ASC),
        last_hour=EnumField(LastHour, missing=LastHour.LAST_1_HOUR),
        limit=ex_fields.LimitField(missing=100)
    ))
    def get(cls, **kwargs):
        """合约-交易排名-成交价排名"""
        market = kwargs['market']
        bid_rank = kwargs['bid_rank']
        ask_rank = kwargs['ask_rank']
        last_hour = kwargs['last_hour']
        limit = kwargs['limit']

        end_time = datetime_to_time(now() - last_hour.to_timedelta())
        client = PerpetualServerClient()
        if (position_type := PerpetualMarketCache().get_position_type(market)) == PerpetualMarketType.INVERSE:
            # 反向合约的成交数量即为USD
            balance_price = Decimal(1)
        else:
            balance_asset = PerpetualMarketCache().get_balance_asset(market)
            balance_price = PriceManager.asset_to_usd(balance_asset)
        # 合约server的最近成交数据默认包括自成交数据
        result = client.market_deals_ext(market=market, limit=3000, last_id=0)
        sell_trade_mapper = defaultdict(lambda: {
            "price": [],
            "amount_at_price": [],
            "amount": Decimal(),
            "amount_usd": Decimal()
        })
        buy_trade_mapper = defaultdict(lambda: {
            "price": [],
            "amount_at_price": [],
            "amount": Decimal(),
            "amount_usd": Decimal()
        })
        for item in result:
            if int(item['time']) < end_time:
                break

            price = Decimal(item['price'])
            amount = Decimal(item['amount'])
            buy_user_id = item['bid_user_id']
            sell_user_id = item['ask_user_id']
            amount_usd = price * amount * balance_price if position_type == PerpetualMarketType.DIRECT else amount

            sell_trade_mapper[sell_user_id]['price'].append(price)
            sell_trade_mapper[sell_user_id]['amount_at_price'].append(amount)
            sell_trade_mapper[sell_user_id]['amount'] += amount
            sell_trade_mapper[sell_user_id]['amount_usd'] += amount_usd
            buy_trade_mapper[buy_user_id]['price'].append(price)
            buy_trade_mapper[buy_user_id]['amount_at_price'].append(amount)
            buy_trade_mapper[buy_user_id]['amount'] += amount
            buy_trade_mapper[buy_user_id]['amount_usd'] += amount_usd

        sell_rank_data, buy_rank_data = [], []
        ask_choose, ask_reverse = cls.get_rank(ask_rank)
        for user_id, data in sorted(
                sell_trade_mapper.items(), key=lambda x: ask_choose(x[1]['price']), reverse=ask_reverse
        )[: limit]:
            price = ask_choose(data['price'])
            amount_at_price = data['amount_at_price'][data['price'].index(price)]
            amount_usd = data['amount_usd'] - buy_trade_mapper.get(user_id, {}).get('amount_usd', Decimal())
            sell_rank_data.append({
                "user_id": user_id,
                "price": price,
                "amount_at_price": amount_at_price,
                "volume_at_price": price * amount_at_price,                
                "amount": data['amount'] - buy_trade_mapper.get(user_id, {}).get('amount', Decimal()),
                "amount_usd": quantize_amount(amount_usd, 8)
            })
        bid_choose, bid_reverse = cls.get_rank(bid_rank)
        for user_id, data in sorted(
                buy_trade_mapper.items(), key=lambda x: bid_choose(x[1]['price']), reverse=bid_reverse
        )[: limit]:
            price = bid_choose(data['price'])
            amount_at_price = data['amount_at_price'][data['price'].index(price)]
            amount_usd = data['amount_usd'] - sell_trade_mapper.get(user_id, {}).get('amount_usd', Decimal())
            buy_rank_data.append({
                "user_id": user_id,                
                "price": price,
                "amount_at_price": amount_at_price,
                "volume_at_price": price * amount_at_price,                
                "amount": data['amount'] - sell_trade_mapper.get(user_id, {}).get('amount', Decimal()),
                "amount_usd": quantize_amount(amount_usd, 8)
            })

        return dict(
            sell_rank_data=sell_rank_data,
            buy_rank_data=buy_rank_data,
            market_list=sorted(PerpetualMarketCache().get_market_list()),
            last_hour_list=LastHour.get_last_hour_list(result)
        )


@ns.route('/user-trade-fee-summary-rank')
@respond_with_code
class UserTradeFeeSummaryRank(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        id=wa_fields.Integer(missing=None),
        start_date=wa_fields.String(required=True),
        end_date=wa_fields.String(required=True),
        page=ex_fields.PageField(unlimited=True),
        sort_type=wa_fields.String(missing='trade_amount'),
        limit=ex_fields.LimitField,
        user_type=wa_fields.String
    ))
    def get(cls, **kwargs):
        """永续-交易排名-用户总成交手续费排名"""
        from .users import UsersResource
        user_id = kwargs['id']
        start_date = kwargs['start_date']
        end_date = kwargs['end_date']
        page = kwargs['page']
        limit = kwargs['limit']

        query = UserTradeSummary.query.filter(
            UserTradeSummary.system == UserTradeSummary.System.PERPETUAL,
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date <= end_date,
        )
        fee_query = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.system == UserTradeFeeSummary.System.PERPETUAL,
            UserTradeFeeSummary.report_date >= start_date,
            UserTradeFeeSummary.report_date <= end_date,
        )
        if user_type := kwargs.get('user_type'):
            fee_query = fee_query.join(User).filter(
                UserTradeFeeSummary.user_id == User.id,
                User.user_type == user_type,
            )
            query = query.join(User).filter(
                UserTradeSummary.user_id == User.id,
                User.user_type == user_type,
            )

        records = []
        if user_id:
            user = User.query.get(user_id)
            fee_query = fee_query.filter(
                UserTradeFeeSummary.user_id == user_id
            ).order_by(
                UserTradeFeeSummary.report_date.desc()
            )
            query = query.filter(UserTradeSummary.user_id == user_id)
            user_trade_amount = query.all()

            user_trade_amount_date_map = {item.report_date: item.trade_amount for item in user_trade_amount}

            pagination = fee_query.paginate(page, limit, error_out=False)
            for item in pagination.items:
                records.append({
                    'report_date': item.report_date.strftime("%Y-%m-%d"),
                    'user_id': item.user_id,
                    'trade_amount': user_trade_amount_date_map[item.report_date],
                    'trade_fee_amount': item.trade_fee_amount,
                    'fee_ratio': amount_to_str(
                        item.trade_fee_amount / user_trade_amount_date_map[item.report_date] * 100, 2) + '%',
                    'rank': item.rank,
                    'email': user.main_user_email,
                    'user_type': UsersResource.USER_TYPES[user.user_type]
                })
        else:
            user_fee_query = fee_query.with_entities(
                func.sum(UserTradeFeeSummary.trade_fee_amount).label('trade_fee_amount'),
                UserTradeFeeSummary.user_id
            ).group_by(
                UserTradeFeeSummary.user_id
            ).order_by(
                func.sum(UserTradeFeeSummary.trade_fee_amount).desc()
            )

            pagination = user_fee_query.paginate(page, limit, error_out=False)

            pagination_items = list(pagination.items)
            user_ids = [item.user_id for item in pagination_items]
            user_trade_amount = query.filter(UserTradeSummary.user_id.in_(user_ids)).with_entities(
                func.sum(UserTradeSummary.trade_amount).label('trade_amount'),
                UserTradeSummary.user_id
            ).group_by(
                UserTradeSummary.user_id
            ).all()
            user_trade_amount_map = defaultdict(Decimal)
            for _user in user_trade_amount:
                user_trade_amount_map[_user.user_id] = _user.trade_amount
            rank = (page - 1) * limit
            user_data_map = {i.id: i for i in User.query.filter(User.id.in_(user_ids))}
            for item in pagination_items:
                rank += 1
                records.append({
                    'report_date': '{start_date} - {end_date}'.format(
                        start_date=start_date, end_date=end_date),
                    'user_id': item.user_id,
                    'trade_amount': user_trade_amount_map[item.user_id],
                    'trade_fee_amount': item.trade_fee_amount,
                    'fee_ratio': amount_to_str(
                        item.trade_fee_amount / user_trade_amount_map[item.user_id] * 100 if user_trade_amount_map[
                            item.user_id] else Decimal(), 2) + '%',
                    'rank': rank,
                    'email': user_data_map[item.user_id].email,
                    'user_type': UsersResource.USER_TYPES[user_data_map[item.user_id].user_type],
                })
        amount_query = query.with_entities(
            func.sum(UserTradeSummary.trade_amount).label('total_amount')
        ).first()
        fee_result = fee_query.with_entities(
            func.sum(UserTradeFeeSummary.trade_fee_amount).label(
                'total_amount')
        ).first()
        total_amount = amount_query.total_amount \
            if amount_query.total_amount else Decimal()
        total_fee_amount = fee_result.total_amount \
            if fee_result.total_amount else Decimal()
        count_query = fee_query.with_entities(
            func.count(distinct(UserTradeFeeSummary.user_id)).label(
                'user_count')
        ).first()
        total_count = count_query.user_count if count_query else 0
        return dict(
            total=pagination.total,
            records=records,
            summary_data=dict(
                total_amount=quantize_amount(total_amount, 2),
                total_fee_amount=quantize_amount(total_fee_amount, 2),
                fee_ratio=(str(quantize_amount((total_fee_amount / total_amount * 100),
                                               2)) if total_amount else '0') + '%',
                total_count=total_count
            ),
            user_types={item.name: gettext(item.value) for item in User.UserType}
        )


@ns.route('/position-list')
@respond_with_code
class PositionList(Resource):
    marshal_fields = {
        'asset': fields.String,
        'position_id': fields.Integer,
        'user_id': fields.Integer,
        'market': fields.String,
        'side': ex_fields.EnumMarshalField(
            PositionSide, output_field_lower=False),
        'leverage': fields.String,
        'type': ex_fields.EnumMarshalField(
            PositionType, output_field_lower=False),
        'amount': ex_fields.AmountField,
        'open_price': ex_fields.PriceField,
        'liq_price': ex_fields.PriceField,
        'bkr_price': ex_fields.PriceField,
        'position_margin_amount': ex_fields.AmountField,
        'mainten_margin_amount': ex_fields.AmountField,
        'profit_unreal': ex_fields.AmountField,
        'profit_real': ex_fields.AmountField,
        'deal_asset_fee': ex_fields.AmountField,
        'fee_asset': fields.String,
        'liq_risk': ex_fields.AmountField,
        'adl_sort': fields.Integer
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        side=ex_fields.EnumField(enum=PositionSide, required=True),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, **kwargs):
        """永续-永续-持仓列表"""
        market = kwargs['market']
        side = kwargs['side']
        page = kwargs['page']
        limit = kwargs['limit']

        sign_price_map: Dict[str, Decimal] = {}
        client = PerpetualServerClient()
        result = client.position_list(market, side, page, limit)
        records = result['records'][:limit]

        for item in records:
            market = item['market']
            market_info = PerpetualMarketCache().get_market_info(market)
            if market not in sign_price_map:
                res = client.get_market_status(market, 86400)
                sign_price_map[market] = Decimal(res.get('sign_price', '0'))
            sign_price = sign_price_map[market]

            item['profit_unreal'] = \
                get_profit_unreal(sign_price, item['side'], item['settle_price'],
                                  item['amount'], market_info['type'])
            item['position_margin_amount'] = \
                Decimal(item['margin_amount']) + item['profit_unreal']

        market_summary = MarketSummaryHistory.query.filter(
            MarketSummaryHistory.market_type == market
        ).order_by(
            MarketSummaryHistory.updated_at.desc()
        ).first()

        r = offset_to_page(result)
        r['data'] = marshal(records, cls.marshal_fields)
        r['market_summary'] = market_summary
        r['balance_asset'] = PerpetualMarketCache.get_balance_asset(market)
        r['amount_asset'] = PerpetualMarketCache.get_amount_asset(market)
        return r


@ns.route('/position-summary')
@respond_with_code
class PositionSummary(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
    ))
    def get(cls, **kwargs):
        """永续-永续-持仓统计"""
        market = kwargs['market']

        query = MarketSummaryHistory.query.filter(
            MarketSummaryHistory.market_type == market,
            MarketSummaryHistory.updated_at >= now() - timedelta(days=3),
            MarketSummaryHistory.updated_at < now(),
        ).order_by(
            MarketSummaryHistory.updated_at
        )
        user_data_list = []
        amount_data_list = []
        for item in query:
            ts = int(item.updated_at.timestamp()) * 1000
            user_data_list.append(
                (ts, item.position_short_users + item.position_long_users))
            amount_data_list.append((ts, item.position_short_amount))
        return dict(
            user_data_list=user_data_list,
            amount_data_list=amount_data_list
        )


@ns.route('/liquidation-records')
@respond_with_code
class LiquidationRecords(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'liq_time': fields.Float,
        'user_id': fields.Integer,
        'market': fields.String,
        'side': fields.String(
            attribute=lambda x: '买入平空' if x['side'] == 1 else '卖出平多'),
        'liq_amount': ex_fields.AmountField,
        'open_price': ex_fields.PriceField,
        'bkr_price': ex_fields.PriceField,
        'liq_price': ex_fields.PriceField,
        'average_deal_price': ex_fields.PriceField,
        'profit_real': ex_fields.AmountField,
        'insurance_change': ex_fields.AmountField,
        'position_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(missing=''),
        side=ex_fields.EnumField(missing=0, enum=PositionSide),
        id=wa_fields.Integer(missing=None),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        insurance_change=ex_fields.EnumField(PerpetualSysHistoryDB.InsuranceChange),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField(missing=100),
        export=wa_fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """永续-永续-爆仓记录"""
        if kwargs['export']:
            if kwargs['end_time'] - kwargs['start_time'] > 1000 * 86400 * 30:
                raise InvalidArgument(message='导出任务时间范围不能超过30天')
            kwargs['email'] = g.user.email
            json_kwargs = json.dumps(kwargs, cls=JsonEncoder)
            export_liquidation_records.delay(json_kwargs)
            return
        records, total = get_liquidation_records(kwargs)
        has_filter_option, can_statistic = cls.check_filters(kwargs)
        if can_statistic:
            total_amount, total_income_amount, total_expense_amount = get_insurance_summary_data(kwargs)
        elif not has_filter_option:
            total_amount = sum([i['insurance_change_value'] for i in records])
            total_income_amount = sum([i['insurance_change_value'] for i in records if i['insurance_change_value'] > 0])
            total_expense_amount = sum([i['insurance_change_value'] for i in records if i['insurance_change_value'] < 0])
        else:
            total_amount = total_income_amount = total_expense_amount = '-'
        market_info = PerpetualMarketCache().read_aside()
        market_info.update(PerpetualOfflineMarketCache().read_aside())
        items = []
        for record in records:
            item = marshal(record, cls.marshal_fields)
            info = market_info[item['market']]
            prec = info['money_prec']
            item['open_price'] = cls._fmt_price(item['open_price'], prec)
            item['average_deal_price'] = cls._fmt_price(item['average_deal_price'], prec)
            item['liq_price'] = cls._fmt_price(item['liq_price'], prec)
            item['bkr_price'] = cls._fmt_price(item['bkr_price'], prec)
            items.append(item)
        unit = 'USD'
        if market := kwargs['market']:
            online_market_list = set(PerpetualMarketCache().get_market_list())
            if market in online_market_list:
                unit = PerpetualMarketCache.get_balance_asset(market)
            else:
                unit = PerpetualOfflineMarketCache.get_balance_asset(market)
        result = {
            'data': items,
            'total': total
        }
        result.update(extra=dict(
            total_amount=f'{amount_to_str(total_amount, 8)} {unit}' if total_amount != '-' else total_amount,
            total_income_amount=f'{amount_to_str(total_income_amount, 8)} {unit}' if
            total_income_amount != '-' else total_income_amount,
            total_expense_amount=f'{amount_to_str(total_expense_amount, 8)} {unit}' if
            total_expense_amount != '-' else total_expense_amount,
        ))
        return result

    @classmethod
    def check_filters(cls, kwargs):
        has_filter_option, can_statistic = False, False
        if kwargs.get('start_time') and kwargs.get('end_time') and not kwargs.get('side'):
            can_statistic = True
        for k, v in kwargs.items():
            if k in ('page', 'limit', 'export'):
                continue
            if v:
                has_filter_option = True
        return has_filter_option, can_statistic

    @classmethod
    def _fmt_price(cls, price, prec):
        if price == '--':
            return price
        return quantize_amount(price, prec)


# noinspection PyUnresolvedReferences
@ns.route('/liquidation-records/<int:position_id>/<int:user_id>')
@respond_with_code
class LiquidationRecordsDetail(Resource):
    marshal_fields = {
        'time': fields.Float,
        'market': fields.String,
        'user_id': fields.Integer,
        'deal_user_id': fields.Integer,
        'order_id': fields.Integer,
        'deal_order_id': fields.Integer,
        'position_id': fields.Integer,
        'side': ex_fields.EnumMarshalField(
            PositionSide, output_field_lower=False),
        'role': fields.Integer,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'margin_amount': ex_fields.AmountField,
        'deal_stock': ex_fields.AmountField,
        'deal_margin': ex_fields.AmountField,
    }

    @classmethod
    def get(cls, position_id, user_id):
        """永续-永续-爆仓记录详情"""
        records = PerpetualHistoryDB.get_liquidation_detail(
            position_id, user_id
        )
        records = marshal(records, cls.marshal_fields)
        result = {
            'offset': 0,
            'limit': 101,
            'records': records
        }
        return offset_to_page(result)


@ns.route('/auto-deleveraging-records')
@respond_with_code
class AutoDeleveragingRecords(Resource):
    marshal_fields = {
        'deal_id': fields.Integer,
        'time': fields.Float,
        'user_id': fields.Integer,
        'deal_user_id': fields.Integer,
        'market': fields.String,
        'side': fields.String(
            attribute=lambda x: '空仓' if x['side'] == 1 else '多仓'),
        'leverage': ex_fields.AmountField,
        'position_amount': ex_fields.AmountField(
            attribute=lambda x: Decimal(
                x['amount'] + Decimal(x['position_amount']))),
        'amount': ex_fields.AmountField,
        'price': ex_fields.PriceField,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String,
        side=ex_fields.EnumField(PositionSide),
        id=wa_fields.Integer,
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=100)
    ))
    def get(cls, **kwargs):
        """永续-永续-自动减仓记录"""
        market = kwargs.get('market')
        user_id = kwargs.get('id')
        side = kwargs.get('side')
        start_time = int(kwargs['start_time'] / 1000)
        end_time = int(kwargs['end_time'] / 1000)
        page, limit = kwargs['page'], kwargs['limit']

        records = PerpetualSysHistoryDB.get_auto_deleveraging_records(
            start_time, end_time, user_id, market, side
        )
        total = len(records)
        records = records[(page - 1) * limit:page * limit]
        records = marshal(records, cls.marshal_fields)
        market_info = PerpetualMarketCache().read_aside()
        market_info.update(PerpetualOfflineMarketCache().read_aside())
        for item in records:
            info = market_info[item['market']]
            item['price'] = quantize_amount(item['price'], info['money_prec'])
        return dict(
            items=records,
            total=total,
        )


@ns.route('/insurance')
@respond_with_code
class Insurance(Resource):
    marshal_fields = {
        'time': fields.Integer,
        'asset': fields.String,
        'total': ex_fields.AmountField(attribute="amount"),
        'real_total': ex_fields.AmountField(attribute="real_amount")
    }

    @classmethod
    @ns.use_kwargs(dict(
        asset=wa_fields.String(missing=None),
    ))
    def get(cls, **kwargs):
        """永续-永续-保险基金"""
        asset = kwargs['asset']
        if not asset:
            market_data = PerpetualMarketCache().read_aside()
            asset_list = sorted(PerpetualMarketCache.get_balance_asset(m) \
                                for m in market_data.values())
        else:
            asset_list = [asset]
        asset_list = sorted(list(set(asset_list)))

        client = PerpetualServerClient()
        data = client.market_insurances()
        records = []
        for _v in data:
            asset = _v["asset"]
            if asset in asset_list:
                records.append(_v)
        return marshal(records, cls.marshal_fields)


@ns.route('/insurance-detail')
@respond_with_code
class InsuranceDetail(Resource):
    marshal_fields = {
        'asset': fields.String,
        'time': fields.Integer,
        'insurance_type': fields.String(
            attribute=lambda x: '平台注入' if x['type'] == 1 else '穿仓垫付'),
        'change': ex_fields.AmountField,
        'total': ex_fields.AmountField
    }

    @classmethod
    @ns.use_kwargs(dict(
        asset=wa_fields.String(missing=None),
        insurance_type=wa_fields.String(missing=None),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField(missing=100)
    ))
    def get(cls, **kwargs):
        """永续-永续-保险基金明细"""
        asset = kwargs['asset']
        insurance_type = kwargs['insurance_type']
        start_time = int(kwargs['start_time'] / 1000)
        end_time = int(kwargs['end_time'] / 1000)
        page = kwargs['page']
        limit = kwargs['limit']

        offset = (page - 1) * limit
        records = PerpetualSysHistoryDB.get_insurance_detail(
            start_time, end_time, offset, limit + 1, asset, insurance_type)
        records = marshal(records, cls.marshal_fields)
        result = {
            'offset': offset,
            'limit': limit + 1,
            'records': records
        }
        return offset_to_page(result)


# noinspection PyUnresolvedReferences
@ns.route('/pending-order-detail')
@respond_with_code
class PendingOrderDetailResource(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'create_time': fields.Integer(attribute='time'),
        'deal_type': fields.String(attribute=lambda x: position_deal_type_map.get(x['deal_type'], '')),
        'market': fields.String,
        'user_id': fields.Integer,
        'deal_user_id': fields.Integer,
        'order_id': fields.Integer,
        'deal_order_id': fields.Integer,
        'position_id': fields.Integer,
        'side': ex_fields.EnumMarshalField(OrderSideType),
        'role': ex_fields.EnumMarshalField(Order.TradeRoleType),
        'deal_fee': ex_fields.AmountField,
        'fee_asset': fields.String,  # fee_asset字段是手续费抵扣币种（合约无CET抵扣，所以都为空），不是手续费币种
        'fee_real_rate': ex_fields.AmountField,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'position_amount': ex_fields.AmountField,
        'position_type': ex_fields.EnumMarshalField(PositionType)
    }

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
        order_id=wa_fields.Integer(required=True),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField
    ))
    def get(cls, **kwargs):
        """
        永续普通委托-未完成订单详情
        """
        client = PerpetualServerClient()
        result = client.order_deals(
            kwargs['user_id'], kwargs['order_id'],
            kwargs['page'], kwargs['limit']
        )
        ret = marshal(result['records'], cls.marshal_fields)
        if ret:
            market = ret[0]['market']
            market_info = PerpetualMarketCache().get_market_info(market)
            market_type = market_info['type']
            deal_fee_asset = market_info['money'] if market_type == 1 else market_info['stock']
            for item in ret:
                item['deal_fee_asset'] = deal_fee_asset
        result['records'] = ret
        return offset_to_page(result)


@ns.route('/report-trade')
@respond_with_code
class TradeReportResource(Resource):
    marshal_fields = {
        'report_date': TimestampMarshalField,
        'trade_usd': AmountField,
        'fee_usd': AmountField,
        'deal_user_count': fields.Integer,
        'deal_count': fields.Integer,
        'avg_fee_rate': AmountField,
        'avg_mm_fee_rate': AmountField,
        'avg_normal_fee_rate': AmountField,
        'normal_fee_rate': AmountField,
        'normal_trade_rate': AmountField,
        'cet_fee_rate': AmountField,
        'increase_trade_user': AmountField,
        'position_amount_usd': AmountField,
        'real_leverage': AmountField,
        'active_user_count': fields.Integer,
        'position_user_count': fields.Integer,
        'market_count': fields.Integer,
        'api_user_count': fields.Integer,
    }

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "market_count", Language.ZH_HANS_CN: "市场数"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "全站成交额（USD）"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "总手续费收入（USD）"},
        {"field": "position_amount_usd", Language.ZH_HANS_CN: "持仓市值"},
        {"field": "position_user_count", Language.ZH_HANS_CN: "持仓人数"},
        {"field": "deal_user_count", Language.ZH_HANS_CN: "成交人数"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "合约使用人数"},
        {"field": "api_user_count", Language.ZH_HANS_CN: "API用户数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "成交笔数"},
        {"field": "increase_trade_user", Language.ZH_HANS_CN: "新增合约用户"},
        {"field": "avg_fee_rate", Language.ZH_HANS_CN: "平均费率"},
        {"field": "avg_mm_fee_rate", Language.ZH_HANS_CN: "做市商平均费率"},
        {"field": "avg_normal_fee_rate", Language.ZH_HANS_CN: "普通用户平均费率"},
        {"field": "normal_fee_rate", Language.ZH_HANS_CN: "普通手续费比例"},
        {"field": "normal_trade_rate", Language.ZH_HANS_CN: "普通成交比例"},
        {"field": "real_leverage", Language.ZH_HANS_CN: "实际杠杆倍数"},

    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField,
        export=wa_fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """报表-永续-交易报表"""
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyPerpetualTradeReport
        else:
            model = MonthlyPerpetualTradeReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)

        if kwargs['export']:
            rows = query.limit(ADMIN_EXPORT_LIMIT).all()
            return export_xlsx(
                filename='perpetual_site_report',
                data_list=cls.get_export_data(rows, kwargs['report_type']),
                export_headers=cls.export_headers
            )
        ret = query_to_page(query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        return ret

    @classmethod
    def get_export_data(cls, rows, report_type):
        records = []
        for row in rows:
            report_dt = date_to_datetime(row.report_date)
            if report_type == ReportType.DAILY:
                report_date_str = datetime_to_str(report_dt, fmt='%Y-%m-%d')
            else:
                report_date_str = datetime_to_str(report_dt, fmt='%Y-%m')
            record = row.to_dict()
            record['report_date'] = report_date_str
            record['trade_usd'] = amount_to_str(record['trade_usd'], 2)
            record['fee_usd'] = amount_to_str(record['fee_usd'], 2)
            record['position_amount_usd'] = amount_to_str(record['position_amount_usd'], 2)
            record['avg_fee_rate'] = format_percent(record['avg_fee_rate'], 4)
            record['avg_mm_fee_rate'] = format_percent(record['avg_mm_fee_rate'], 4)
            record['avg_normal_fee_rate'] = format_percent(record['avg_normal_fee_rate'], 4)
            record['normal_fee_rate'] = format_percent(record['normal_fee_rate'], 4)
            record['normal_trade_rate'] = format_percent(record['normal_trade_rate'], 4)
            records.append(record)
        return records


@ns.route('/report-market')
@respond_with_code
class MarketReportResource(Resource):
    marshal_fields = {
        'report_date': TimestampMarshalField,
        'market': fields.String,
        'deal_user_count': fields.Integer,
        'deal_count': fields.Integer,
        'active_user_count': fields.Integer,
        'deal_amount': AmountField,
        'deal_usd_percent': AmountField,
        'buy_count': fields.Integer,
        'buy_amount': AmountField,
        'sell_count': fields.Integer,
        'sell_amount': AmountField,
        'position_amount_usd': AmountField,
        'real_leverage': AmountField,
        'fee_usd': AmountField,
        'position_user_count': fields.Integer,
    }

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "position_amount_usd", Language.ZH_HANS_CN: "持仓市值"},
        {"field": "position_user_count", Language.ZH_HANS_CN: "持仓人数"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "合约使用人数"},
        {"field": "deal_user_count", Language.ZH_HANS_CN: "成交人数"},
        {"field": "deal_amount", Language.ZH_HANS_CN: "成交量（USD）"},
        {"field": "deal_usd_percent", Language.ZH_HANS_CN: "成交额占比"},
        {"field": "deal_count", Language.ZH_HANS_CN: "成交笔数"},
        {"field": "buy_count", Language.ZH_HANS_CN: "主动买入笔数"},
        {"field": "buy_amount", Language.ZH_HANS_CN: "主动买入量（USD）"},
        {"field": "sell_count", Language.ZH_HANS_CN: "主动卖出笔数"},
        {"field": "sell_amount", Language.ZH_HANS_CN: "主动卖出量（USD）"},
        {"field": "real_leverage", Language.ZH_HANS_CN: "实际杠杆倍数"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "手续费市值（USD）"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        order=EnumField(["report_date", "deal_amount", "deal_user_count", "deal_count"], missing="report_date"),
        order_type=EnumField(["desc", "asc"], missing="desc"),
        market=wa_fields.String,
        page=PageField(unlimited=True),
        limit=LimitField,
        export=wa_fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """报表-永续-市场报表"""
        start_date, end_date = kwargs.get('start_date'), kwargs.get("end_date")
        order = kwargs.get('order') or 'report_date'
        order_type = kwargs.get('order_type') or 'desc'
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyPerpetualMarketReport
        else:
            model = MonthlyPerpetualMarketReport
        query = model.query.order_by(getattr(getattr(model, order), order_type)())
        if start_date:
            query = query.filter(model.report_date >= start_date)
        if end_date:
            query = query.filter(model.report_date <= end_date)
        if market := kwargs.get('market'):
            query = query.filter(model.market == market)

        if kwargs['export']:
            rows = query.limit(ADMIN_EXPORT_LIMIT).all()
            return export_xlsx(
                filename='perpetual_market_report',
                data_list=cls.get_export_data(rows, kwargs['report_type']),
                export_headers=cls.export_headers
            )
        ret = query_to_page(query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        return ret

    @classmethod
    def get_export_data(cls, rows, report_type):
        records = []
        for row in rows:
            report_dt = date_to_datetime(row.report_date)
            if report_type == ReportType.DAILY:
                report_date_str = datetime_to_str(report_dt, fmt='%Y-%m-%d')
            else:
                report_date_str = datetime_to_str(report_dt, fmt='%Y-%m')
            record = row.to_dict()
            record['report_date'] = report_date_str
            record['position_amount_usd'] = amount_to_str(record['position_amount_usd'], 2)
            record['deal_amount'] = amount_to_str(record['deal_amount'], 2)
            record['deal_usd_percent'] = format_percent(record['deal_usd_percent'], 4)
            record['buy_amount'] = amount_to_str(record['buy_amount'], 2)
            record['sell_amount'] = amount_to_str(record['sell_amount'], 2)
            record['fee_usd'] = amount_to_str(record['fee_usd'], 2)
            records.append(record)
        return records


@ns.route("/report-market-detail")
@respond_with_code
class MarketDetailReportResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, required=True),
            report_date=DateField(to_date=True),
            order=EnumField(["deal_amount", "deal_user_count", "deal_count"], missing="deal_amount"),
            order_type=EnumField(["desc", "asc"], missing="desc"),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 报表-永续-市场详情报表 """
        page = kwargs["page"]
        limit = kwargs["limit"]
        report_type = kwargs["report_type"]
        order = kwargs.get('order') or 'deal_amount'
        order_type = kwargs.get('order_type') or 'desc'
        report_date: datetime = kwargs.get("report_date")
        if report_type == ReportType.DAILY:
            model = DailyPerpetualMarketReport
        else:
            model = MonthlyPerpetualMarketReport
        if not report_date:
            last_item = model.query.order_by(model.report_date.desc()).first()
            if not last_item:
                return dict(
                    has_next=False,
                    curr_page=1,
                    count=0,
                    data=[],
                    total=0,
                    total_page=1,
                )
            report_date = last_item.report_date
        if report_type == ReportType.MONTHLY:
            report_date = report_date.replace(day=1)

        pagination = (
            model.query.filter(model.report_date == report_date)
            .order_by(getattr(getattr(model, order), order_type)())
            .paginate(page, limit)
        )
        all_market_row = model.query.filter(model.report_date == report_date, model.market == "ALL").first()

        fmt = "%Y-%m-%d" if report_type == ReportType.DAILY else "%Y-%m"
        table_rows = []
        if all_market_row:
            table_rows.append(all_market_row)
        table_rows.extend([i for i in pagination.items if i.market != "ALL"])  # ALL固定第一条
        table_items = []
        for row in table_rows:
            item = {
                "report_date": row.report_date.strftime(fmt),
                "market": row.market,
                "deal_amount": amount_to_str(row.deal_amount, 2),
                "deal_user_count": row.deal_user_count,
                "deal_count": row.deal_count,
                "deal_usd_percent": row.deal_usd_percent,
                "buy_amount": amount_to_str(row.buy_amount, 2),
                "buy_count": row.buy_count,
                "sell_amount": amount_to_str(row.sell_amount, 2),
                "sell_count": row.sell_count,
                'position_amount_usd': amount_to_str(row.position_amount_usd, 2),
                'real_leverage': amount_to_str(row.real_leverage, 2),
                'fee_usd': amount_to_str(row.fee_usd, 2)
            }
            table_items.append(item)

        total = pagination.total
        total_page = math.ceil(total / limit)
        return dict(
            has_next=page < total_page,
            curr_page=page,
            count=len(table_items),
            data=table_items,
            total=total,
            total_page=total_page,
        )


@ns.route('/report-insurance')
@respond_with_code
class InsuranceReportResource(Resource):
    marshal_fields = {
        'report_date': TimestampMarshalField,
        'asset': fields.String,
        'increase_amount': AmountField,
        'decrease_amount': AmountField,
        'total_balance': AmountField,
        'real_increase_amount': AmountField,
        'real_decrease_amount': AmountField,
        'real_balance': AmountField,
        'transfer': AmountField(attribute=lambda x: abs(x.transfer)),
    }

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        asset=wa_fields.String,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """报表-永续-保险基金报表"""
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyPerpetualInsuranceReport
        else:
            model = MonthlyPerpetualInsuranceReport
        query = model.query.order_by(model.report_date.desc(), model.asset)
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(model.report_date <= end_date)
        if asset := kwargs.get('asset'):
            query = query.filter(model.asset == asset)
        return query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)


@ns.route('/market-index')
@respond_with_code
class MarginIndexResource(Resource):
    class DetailSchema(Schema):
        exchange_name = wa_fields.String(required=True)
        market = wa_fields.String(required=True)
        weight = wa_fields.Decimal(required=True)
        url = wa_fields.URL(required=True)

        class Meta:
            UNKNOWN = EXCLUDE

    POST_SCHEMA = dict(
        market_name=wa_fields.String(required=True, validate=lambda x: x.isupper()),
        name_show=wa_fields.String(required=True),
        risk_check_time=wa_fields.Integer(required=True),
        price_precision=wa_fields.Integer(required=True, validate=lambda_validate_precision),
        status=EnumField(PerpetualMarketIndex.StatusType, enum_by_value=True, required=True),
        details=wa_fields.Nested(DetailSchema, many=True, required=True)
    )

    PUT_SCHEMA = dict(
        id=wa_fields.Integer(),
        market_name=wa_fields.String(),
        name_show=wa_fields.String(),
        risk_check_time=wa_fields.Integer(),
        price_precision=wa_fields.Integer(validate=lambda_validate_precision),
        status=EnumField(PerpetualMarketIndex.StatusType, enum_by_value=True),
        details=wa_fields.Nested(DetailSchema, many=True)
    )

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(missing=''),
        status=EnumField(PerpetualMarketIndex.StatusType, missing='', enum_by_value=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """永续-永续配置-普通指数配置"""
        params = Struct(**kwargs)
        index_query = PerpetualMarketIndex.query
        all_markets = [v.name for v in
                       PerpetualMarketIndex.query.with_entities(PerpetualMarketIndex.name)]
        if params.market:
            index_query = index_query.filter(
                PerpetualMarketIndex.name == params.market
            )
        if params.status:
            index_query = index_query.filter(
                PerpetualMarketIndex.status == params.status
            )
        records = index_query.order_by(PerpetualMarketIndex.id.desc()) \
            .paginate(params.page, params.limit)

        def get_index_details(index_ids):
            q = PerpetualMarketIndexDetail.query.filter(
                PerpetualMarketIndexDetail.perpetual_market_index_id.in_(index_ids),
                PerpetualMarketIndexDetail.status == PerpetualMarketIndexDetail.StatusType.PASS)
            result = defaultdict(list)
            for v in q:
                result[v.perpetual_market_index_id].append(
                    dict(weight=v.weight,
                         url=v.url,
                         exchange_name=v.exchange_name,
                         market=v.market,
                         )
                )
            return result

        page_index_ids = [v.id for v in records.items]
        index_detail_dict = get_index_details(page_index_ids)
        return dict(
            total=records.total,
            items=[dict(
                id=record.id,
                created_at=record.created_at,
                updated_at=record.updated_at,
                market_name=record.name,
                name_show=record.name_show,
                risk_check_time=record.risk_check_time,
                price_precision=int(record.price_precision),
                status=record.status.value,
                details=index_detail_dict[record.id],
            ) for record in records.items],
            market_list=all_markets,
            status_dict={PerpetualMarketIndex.StatusType.OPEN.value: "开启",
                         PerpetualMarketIndex.StatusType.CLOSE.value: "关闭"},
        )

    @classmethod
    @ns.use_kwargs(POST_SCHEMA)
    def post(cls, **kwargs):
        """永续-永续配置-普通指数配置-添加普通指数配置"""
        data = Struct(**kwargs)
        all_markets = [v.name for v in
                       PerpetualMarketIndex.query.with_entities(PerpetualMarketIndex.name)]
        if data.name in all_markets:
            raise InvalidArgument(data.name)
        if len(data.details) == 0:
            raise InvalidArgument('index details')
        details_config = [
            PerpetualMarketIndexDetail(
                exchange_name=v['exchange_name'],
                market=v['market'],
                url=v['url'],
                weight=v['weight'],
                status=PerpetualMarketIndexDetail.StatusType.PASS
            ) for v in data.details]
        index_config = PerpetualMarketIndex(
            status=data.status,
            price_precision=data.price_precision,
            name=data.market_name,
            risk_check_time=data.risk_check_time,
            name_show=data.name_show,
            perpetual_market_index_detail=details_config
        )
        db.session.add(index_config)
        db.session.add_all(details_config)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.PerpetualIndex,
            detail=kwargs,
        )

    @classmethod
    @ns.use_kwargs(PUT_SCHEMA)
    def put(cls, **kwargs):
        """永续-永续配置-普通指数配置-编辑普通指数配置"""
        data = Struct(**kwargs)
        record_id = data.id
        record = PerpetualMarketIndex.query.filter(
            PerpetualMarketIndex.id == record_id
        ).first()
        if not record:
            raise InvalidArgument(f"id {record_id}")
        old_data = record.to_dict(enum_to_name=True)
        if "name_show" in kwargs:
            record.name_show = data.name_show
        if "price_precision" in kwargs:
            record.price_precision = data.price_precision
        if "status" in kwargs:
            record.status = data.status
        if "risk_check_time" in kwargs:
            record.risk_check_time = data.risk_check_time
        if "details" in kwargs:
            PerpetualMarketIndexDetail.query.filter(
                PerpetualMarketIndexDetail.perpetual_market_index_id == record_id
            ).update(
                {PerpetualMarketIndexDetail.status: PerpetualMarketIndexDetail.StatusType.DELETE},
                synchronize_session=False
            )
            details_config = [
                PerpetualMarketIndexDetail(
                    perpetual_market_index_id=record_id,
                    exchange_name=v['exchange_name'],
                    market=v['market'],
                    url=v['url'],
                    weight=v['weight'],
                    status=PerpetualMarketIndexDetail.StatusType.PASS
                ) for v in data.details]
            db.session.add_all(details_config)
        record.updated_at = now()
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.PerpetualIndex,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
            special_data=dict(details=data.details) if data.details else None,
        )


@ns.route('/compose')
@respond_with_code
class ComposeIndexResource(Resource):
    COMPOSE_OPERATOR_DICT = [
        {"label": "乘", "value": 1},
        {"label": "除", "value": 2}
    ]

    STATUS_DICT = {PerpetualComposeIndex.StatusType.OPEN.value: "开启",
                   PerpetualComposeIndex.StatusType.CLOSE.value: "关闭"}

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(),
        status=wa_fields.String(),
        page=wa_fields.Int(missing=1),
        limit=wa_fields.Int(missing=100),
    ))
    def get(cls, **kwargs):
        """永续-永续配置-合成指数配置列表"""
        name = kwargs.get('name', '').upper()
        status = kwargs.get('status', '').lower()

        market_name_set = [v.name for v in
                           PerpetualComposeIndex.query.with_entities(PerpetualComposeIndex.name)]

        index_market_name_set = [
            v.name for v in
            PerpetualMarketIndex.query.filter(
                PerpetualMarketIndex.status == PerpetualMarketIndex.StatusType.OPEN).with_entities(
                PerpetualMarketIndex.name)
        ]

        compose_market_name_set = [
            v.name for v in
            PerpetualComposeIndex.query.filter().with_entities(PerpetualComposeIndex.name)
        ]

        data = PerpetualComposeIndex.query
        if name and name in market_name_set:
            data = data.filter(PerpetualComposeIndex.name == name)
        if status:
            data = data.filter(PerpetualComposeIndex.status == status)
        records = [item.to_dict() for item in data.paginate(kwargs['page'], kwargs['limit']).items]

        return dict(
            index_market_name_set=index_market_name_set,
            compose_market_name_set=compose_market_name_set,
            page=kwargs['page'],
            count=data.count(),
            name=name,
            records=records,
            status_dict=cls.STATUS_DICT,
            operator_types=cls.COMPOSE_OPERATOR_DICT,
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        compose_method=wa_fields.Int(required=True),
        price_precision=wa_fields.Int(required=True, validate=lambda_validate_precision),
        first_market=wa_fields.String(required=True),
        second_market=wa_fields.String(required=True),
        status=wa_fields.String(required=True),
        is_visible=wa_fields.Boolean(required=True),
    ))
    def post(cls, **kwargs):
        """永续-永续配置-新建合成指数配置"""
        name = kwargs.get('name', '').upper()
        status = kwargs.get('status', '').lower()

        if PerpetualComposeIndex.query.filter(
                PerpetualComposeIndex.name == name
        ).first():
            raise InvalidArgument(message="交易对已经存在")
        if kwargs["first_market"] == kwargs["second_market"]:
            raise InvalidArgument(message="两个交易对不能相同")
        if int(kwargs["compose_method"]) not in PerpetualComposeIndex.ComposeMethodType.__members__.values():
            raise InvalidArgument(message="方法定义错误")

        index_market_name_set = [
            v.name for v in
            PerpetualMarketIndex.query.filter(
                PerpetualMarketIndex.status == PerpetualMarketIndex.StatusType.OPEN).with_entities(
                PerpetualMarketIndex.name)
        ]

        compose_market_name_set = [
            v.name for v in
            PerpetualComposeIndex.query.with_entities(PerpetualComposeIndex.name)
        ]

        if name in compose_market_name_set:
            raise InvalidArgument(message="交易对的指数价格已经配置, 请勿重复配置")

        if kwargs["first_market"] not in index_market_name_set or kwargs["second_market"] not in index_market_name_set:
            raise InvalidArgument(message="合成的两个交易对的指数价格未配置或未开启")

        compose_index_config = PerpetualComposeIndex(
            name=name,
            status=status,
            price_precision=kwargs["price_precision"],
            first_market=kwargs["first_market"],
            second_market=kwargs["second_market"],
            is_visible=kwargs["is_visible"],
            compose_method=int(kwargs["compose_method"]),
        )
        db.session.add(compose_index_config)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.PerpetualCompose,
            detail=compose_index_config.to_dict(enum_to_name=True),
        )


@ns.route('/compose/<int:index_id>')
@respond_with_code
class ComposeIndexIdResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        compose_method=wa_fields.Int(required=True),
        price_precision=wa_fields.Int(required=True, validate=lambda_validate_precision),
        first_market=wa_fields.String(required=True),
        second_market=wa_fields.String(required=True),
        status=wa_fields.String(required=True),
        is_visible=wa_fields.Boolean(required=True),
    ))
    def put(cls, index_id, **kwargs):
        """永续-永续配置-合成指数设置"""
        name = kwargs.get('name', '').upper()
        status = kwargs.get('status', '').lower()

        index_config = PerpetualComposeIndex.query.filter(PerpetualComposeIndex.id == index_id).first()
        if not index_config:
            raise InvalidArgument(message="合成指数配置不存在")
        if kwargs["first_market"] == kwargs["second_market"]:
            raise InvalidArgument(message="两个交易对不能相同")
        if int(kwargs["compose_method"]) not in PerpetualComposeIndex.ComposeMethodType.__members__.values():
            raise InvalidArgument(message="方法定义错误")
        old_data = index_config.to_dict(enum_to_name=True)

        index_market_name_set = [
            v.name for v in
            PerpetualMarketIndex.query.filter(
                PerpetualMarketIndex.status == PerpetualMarketIndex.StatusType.OPEN).with_entities(
                PerpetualMarketIndex.name)
        ]

        if kwargs["first_market"] not in index_market_name_set or kwargs["second_market"] not in index_market_name_set:
            raise InvalidArgument(message="合成的两个交易对的指数价格未配置或未开启")

        index_config.name = name
        index_config.status = status
        index_config.price_precision = kwargs["price_precision"]
        index_config.first_market = kwargs["first_market"]
        index_config.second_market = kwargs["second_market"]
        index_config.is_visible = kwargs["is_visible"]
        index_config.compose_method = int(kwargs["compose_method"])
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.PerpetualCompose,
            old_data=old_data,
            new_data=index_config.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        status=wa_fields.String(required=True),
    ))
    def patch(cls, index_id, **kwargs):
        """永续-永续配置-合成指数状态设置"""
        status = kwargs.get('status', '').lower()

        index_config = PerpetualComposeIndex.query.filter(PerpetualComposeIndex.id == index_id).first()
        if not index_config:
            raise InvalidArgument(message="合成指数配置不存在")
        old_status = index_config.status

        index_config.status = status
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.PerpetualCompose,
            old_data=dict(status=old_status),
            new_data=dict(status=index_config.status),
        )


class MarketsConfigMixin:

    FUNDING_INTERVAL_LIST = [8, 4, 2]

    @classmethod
    def get_min_mainten_margin(cls, details: list) -> Decimal:
        sort_details = sorted(details, key=lambda _x: Decimal(_x['max_amount']), reverse=False)
        return Decimal(sort_details[0]['mainten_margin_rate'])

    @classmethod
    def get_funding_min_max(cls, details: list) -> (Decimal, Decimal):
        min_mainten_margin = cls.get_min_mainten_margin(details)
        return -Decimal('0.75') * min_mainten_margin, Decimal('0.75') * min_mainten_margin


@ns.route('/markets-config')
@respond_with_code
class MarketsConfigResource(MarketsConfigMixin, Resource):

    class FundingIntervalStatusDict(Enum):
        default = '默认周期'
        dynamic = '动态周期'

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(),
        status=wa_fields.String(),
        offline_visible=wa_fields.Boolean(),
        funding_interval_status=EnumField(FundingIntervalStatusDict),
        page=wa_fields.Int(missing=1),
        limit=wa_fields.Int(missing=100),
    ))
    def get(cls, **kwargs):
        """永续-永续配置-市场配置"""
        name = kwargs.get('name', '').upper()
        status = kwargs.get('status', '').lower()
        funding_interval_status = kwargs.get('funding_interval_status')

        index_configs = PerpetualMarketIndex.query.filter(
            PerpetualMarketIndex.status == PerpetualMarketIndex.StatusType.OPEN)
        market_type_set = [v.name for v in index_configs if
                           not PerpetualMarket.query.filter(PerpetualMarket.name == v.name).first()]

        all_market_type_set = [v.name for v in PerpetualMarket.query.all()]
        trade_asset_set = [item.name for item in PerpetualAssetIndex.query.all()]
        data = PerpetualMarket.query
        if name and name in all_market_type_set:
            data = data.filter(PerpetualMarket.name == name)
        if status:
            data = data.filter(PerpetualMarket.status == status)
        if (offline_visible := kwargs.get('offline_visible')) is not None:
            data = data.filter(PerpetualMarket.status == PerpetualMarket.StatusType.CLOSE)
            if offline_visible:
                data = data.filter(PerpetualMarket.offline_visible.is_(True))
            else:
                data = data.filter(PerpetualMarket.offline_visible.is_(False))
        if funding_interval_status == cls.FundingIntervalStatusDict.default:
            data = data.filter(PerpetualMarket.funding_interval == PerpetualMarket.default_funding_interval)
        elif funding_interval_status == cls.FundingIntervalStatusDict.dynamic:
            data = data.filter(PerpetualMarket.funding_interval != PerpetualMarket.default_funding_interval)

        records = data.paginate(kwargs['page'], kwargs['limit']).items
        liquid_rate_data = PerpetualMarketLimitConfig.query.filter(
            PerpetualMarketLimitConfig.perpetual_market_id.in_(
                [account.id for account in records]),
            PerpetualMarketLimitConfig.status == PerpetualMarketLimitConfig.StatusType.PASS
        ).all()
        liquid_rate_account_id_map = defaultdict(list)
        for liquid_rate in liquid_rate_data:
            liquid_rate_account_id_map[liquid_rate.perpetual_market_id].append(liquid_rate.to_dict())

        final_result = []
        for record in records:
            format_record = record.to_dict()
            format_record['funding_limit_type'] = format_record['funding_limit_type'].name
            format_record['details'] = liquid_rate_account_id_map[format_record['id']]
            final_result.append(format_record)

        result = dict(
            market_type_set=market_type_set,
            trade_asset_set=trade_asset_set,
            all_market_type_set=all_market_type_set,
            status_dict={PerpetualMarket.StatusType.OPEN.value: "开启",
                         PerpetualMarket.StatusType.CLOSE.value: "关闭"},
            margin_dict={PerpetualMarket.MarginType.CROSS.value: "全仓",
                         PerpetualMarket.MarginType.ISOLATED.value: "逐仓"},
            funding_interval_status_dict=cls.FundingIntervalStatusDict,
            funding_interval_list=cls.FUNDING_INTERVAL_LIST,
            funding_limit_dic=PerpetualMarket.FundingLimitType,
            page=kwargs['page'],
            count=data.count(),
            market_type=name,
            records=final_result
        )

        return result

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        market_type=wa_fields.Integer(required=True),
        fee_precision=wa_fields.Int(required=True,
                                    validate=lambda_validate_precision),
        amount_precision=wa_fields.Int(required=True,
                                       validate=lambda_validate_precision),
        base_asset=wa_fields.String(required=True),
        quote_asset=wa_fields.String(required=True),
        base_asset_precision=wa_fields.Int(required=True,
                                           validate=lambda_validate_precision),
        quote_asset_precision=wa_fields.Int(required=True,
                                            validate=lambda_validate_precision),
        price_size=PositiveDecimalField(required=True),
        min_order_amount=PositiveDecimalField(required=True),
        multiplier=PositiveDecimalField(required=True),
        leverages=wa_fields.String(required=True),
        depths=wa_fields.String(required=True),
        leverage_default=PositiveDecimalField(required=True),
        depth_default=PositiveDecimalField(required=True),
        position_type_default=wa_fields.String(required=True),
        impact_amount=PositiveDecimalField(required=True),
        funding_start=wa_fields.Int(required=True, validate=lambda_positive_int),
        default_funding_interval=wa_fields.Int(required=True, validate=lambda x: x in MarketsConfigMixin.FUNDING_INTERVAL_LIST),
        funding_rate_precision=wa_fields.Int(required=True, validate=lambda_validate_precision),
        funding_max=wa_fields.Decimal(required=True, validate=lambda x: 0 < x < 1),
        funding_min=wa_fields.Decimal(required=True, validate=lambda x: -1 < x < 0),
        funding_limit_type=ex_fields.EnumField(PerpetualMarket.FundingLimitType, required=True),
        liq_risk_alert=PositiveDecimalField(required=True),
        details=wa_fields.List(wa_fields.Dict),
        status=wa_fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """永续-永续配置-添加市场配置"""
        name = kwargs.get('name', '').upper()
        data = kwargs
        details = kwargs['details']

        margin_account_data = PerpetualMarket.query.filter(
            PerpetualMarket.name == name
        ).first()
        if margin_account_data:
            raise InvalidArgument(message='当前已经有对应交易对的配置信息')
        try:
            list(map(Decimal, data['leverages'].split(',')))
        except DecimalException:
            raise InvalidArgument(f'invalid leverages: {data["leverages"]}')

        try:
            list(map(Decimal, data['depths'].split(',')))
        except DecimalException:
            raise InvalidArgument(f'invalid depths: {data["depths"]}')

        if data["funding_limit_type"] == PerpetualMarket.FundingLimitType.CALC_BY_MIN_MAINTEN_MARGIN:
            data["funding_min"], data["funding_max"] = cls.get_funding_min_max(details)

        account = PerpetualMarket(
            name=data["name"],
            market_type=PerpetualMarketType.DIRECT if data["market_type"] == 1 else PerpetualMarketType.INVERSE,
            fee_precision=data["fee_precision"],
            amount_precision=data["amount_precision"],
            base_asset=data["base_asset"],
            quote_asset=data["quote_asset"],
            base_asset_precision=data["base_asset_precision"],
            quote_asset_precision=data["quote_asset_precision"],
            price_size=data["price_size"],
            min_order_amount=data["min_order_amount"],
            multiplier=data["multiplier"],
            leverages=data["leverages"],
            depths=data["depths"],
            leverage_default=data["leverage_default"],
            depth_default=data["depth_default"],
            position_type_default=data["position_type_default"],
            impact_amount=data["impact_amount"],
            funding_start=data["funding_start"],
            dynamic_funding_start=0,
            default_funding_interval=data["default_funding_interval"],
            funding_interval=data["default_funding_interval"],
            funding_rate_precision=data["funding_rate_precision"],
            funding_max=data["funding_max"],
            funding_min=data["funding_min"],
            funding_limit_type=data["funding_limit_type"],
            liq_risk_alert=data["liq_risk_alert"],
            interest=PerpetualMarket.DEFAULT_INTEREST,
            status=data["status"],
        )
        db.session.add(account)
        db.session.flush()
        details_config = [PerpetualMarketLimitConfig(
            max_amount=v["max_amount"],
            max_leverage=v["max_leverage"],
            mainten_margin_rate=v["mainten_margin_rate"],
            perpetual_market_id=account.id,
            status=PerpetualMarketLimitConfig.StatusType.PASS
        ) for v in details]
        db.session.add_all(details_config)
        db.session.commit()
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.Market,
            detail=kwargs,
        )


@ns.route('/markets-config/<int:account_id>')
@respond_with_code
class MarketsConfigDetailResource(MarketsConfigMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        market_type=wa_fields.Integer(required=True),
        fee_precision=wa_fields.Int(required=True,
                                    validate=lambda_validate_precision),
        amount_precision=wa_fields.Int(required=True,
                                       validate=lambda_validate_precision),
        base_asset=wa_fields.String(required=True),
        quote_asset=wa_fields.String(required=True),
        base_asset_precision=wa_fields.Int(required=True,
                                           validate=lambda_validate_precision),
        quote_asset_precision=wa_fields.Int(required=True,
                                            validate=lambda_validate_precision),
        price_size=PositiveDecimalField(required=True),
        min_order_amount=PositiveDecimalField(required=True),
        multiplier=PositiveDecimalField(required=True),
        leverages=wa_fields.String(required=True),
        depths=wa_fields.String(required=True),
        leverage_default=PositiveDecimalField(required=True),
        depth_default=PositiveDecimalField(required=True),
        position_type_default=wa_fields.String(required=True),
        impact_amount=PositiveDecimalField(required=True),
        funding_start=wa_fields.Int(required=True, validate=lambda_positive_int),
        default_funding_interval=wa_fields.Int(required=True, validate=lambda x: x in MarketsConfigMixin.FUNDING_INTERVAL_LIST),
        funding_rate_precision=wa_fields.Int(required=True, validate=lambda_positive_int),
        funding_max=wa_fields.Decimal(required=True, validate=lambda x: 0 < x < 1),
        funding_min=wa_fields.Decimal(required=True, validate=lambda x: -1 < x < 0),
        funding_limit_type=ex_fields.EnumField(PerpetualMarket.FundingLimitType, required=True),
        liq_risk_alert=PositiveDecimalField(required=True),
        details=wa_fields.List(wa_fields.Dict),
        status=wa_fields.String(required=True),
        offline_visible=wa_fields.Boolean,
    ))
    def put(cls, account_id, **kwargs):
        """永续-永续配置-编辑市场配置"""
        name = kwargs.get('name', '').upper()
        data = kwargs
        details = kwargs['details']

        account_data = PerpetualMarket.query.filter(
            PerpetualMarket.id == account_id
        ).first()

        old_data = account_data.to_dict(enum_to_name=True)

        try:
            list(map(Decimal, data['leverages'].split(',')))
        except DecimalException:
            raise InvalidArgument(f'invalid leverages: {data["leverages"]}')

        try:
            list(map(Decimal, data['depths'].split(',')))
        except DecimalException:
            raise InvalidArgument(f'invalid depths: {data["depths"]}')

        new_status = data['status']
        offline_visible = bool(data.get('offline_visible'))
        if offline_visible:
            if not MarketOfflineContent.query.filter(
                MarketOfflineContent.market == name,
                MarketOfflineContent.market_type == MarketOfflineContent.MarketType.PERPETUAL,
                MarketOfflineContent.lang == Language.EN_US,
            ).first():
                raise InvalidArgument(message=f'请先配置市场的下架文案，再设置为下架可见')

        funding_interval = account_data.funding_interval
        old_default_funding_interval = account_data.default_funding_interval
        dynamic_funding_start = account_data.dynamic_funding_start
        new_default_funding_interval = data['default_funding_interval']
        if new_default_funding_interval > old_default_funding_interval:
            if funding_interval == old_default_funding_interval:
                funding_interval = new_default_funding_interval
        elif new_default_funding_interval < old_default_funding_interval:
            if funding_interval >= new_default_funding_interval:
                funding_interval = new_default_funding_interval
                dynamic_funding_start = 0

        if data["funding_limit_type"] == PerpetualMarket.FundingLimitType.CALC_BY_MIN_MAINTEN_MARGIN:
            data["funding_min"], data["funding_max"] = cls.get_funding_min_max(details)

        account_data: PerpetualMarket
        account_data.name = name
        account_data.market_type = data["market_type"]
        account_data.fee_precision = data['fee_precision']
        account_data.amount_precision = data['amount_precision']
        account_data.base_asset = data['base_asset']
        account_data.quote_asset = data['quote_asset']
        account_data.base_asset_precision = data['base_asset_precision']
        account_data.quote_asset_precision = data['quote_asset_precision']
        account_data.price_size = data['price_size']
        account_data.min_order_amount = data['min_order_amount']
        account_data.multiplier = data['multiplier']
        account_data.position_type_default = data['position_type_default']
        account_data.leverages = data['leverages']
        account_data.depths = data['depths']
        account_data.leverage_default = data['leverage_default']
        account_data.depth_default = data['depth_default']
        account_data.impact_amount = data['impact_amount']
        account_data.funding_start = data['funding_start']
        account_data.dynamic_funding_start = dynamic_funding_start
        account_data.default_funding_interval = new_default_funding_interval
        account_data.funding_interval = funding_interval
        account_data.funding_rate_precision = data['funding_rate_precision']
        account_data.funding_limit_type = data["funding_limit_type"]
        account_data.funding_max = data["funding_max"]
        account_data.funding_min = data["funding_min"]
        account_data.liq_risk_alert = data['liq_risk_alert']
        account_data.status = new_status
        account_data.offline_visible = offline_visible

        if "details" in kwargs:
            PerpetualMarketLimitConfig.query.filter(
                PerpetualMarketLimitConfig.perpetual_market_id == account_id
            ).update(
                {PerpetualMarketLimitConfig.status: PerpetualMarketLimitConfig.StatusType.DELETE},
                synchronize_session=False
            )

            details_config = [PerpetualMarketLimitConfig(
                max_amount=v["max_amount"],
                max_leverage=v["max_leverage"],
                mainten_margin_rate=v["mainten_margin_rate"],
                perpetual_market_id=account_id,
                status=PerpetualMarketLimitConfig.StatusType.PASS
            ) for v in kwargs['details']]

            db.session.add_all(details_config)

        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.Market,
            old_data=old_data,
            new_data=account_data.to_dict(enum_to_name=True),
            special_data=dict(name=name),
        )

    @classmethod
    @ns.use_kwargs(dict(
        status=wa_fields.String(required=True),
    ))
    def patch(cls, account_id, **kwargs):
        """永续-永续配置-市场配置-开关"""
        margin_account_data = PerpetualMarket.query.filter(
            PerpetualMarket.id == account_id
        ).first()
        if not margin_account_data:
            raise InvalidArgument(f"id {account_id}")
        old_data = margin_account_data.to_dict(enum_to_name=True)
        if "status" in kwargs:
            margin_account_data.status = kwargs['status']
            if kwargs['status'] == PerpetualMarket.StatusType.OPEN.value:
                margin_account_data.offline_visible = False
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.Market,
            old_data=old_data,
            new_data=margin_account_data.to_dict(enum_to_name=True),
            special_data=dict(name=margin_account_data.name),
        )


@ns.route('/assets/<int:asset_id>')
@respond_with_code
class PerpetualAssetsDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        is_visible=wa_fields.Boolean(),
        status=wa_fields.String(),
        save_precision=wa_fields.Integer(validate=lambda_positive_int),
        show_precision=wa_fields.Integer(validate=lambda_positive_int),
    ))
    def put(cls, asset_id, **kwargs):
        """永续-永续配置-编辑资产配置"""
        data = kwargs

        asset_config = PerpetualAssetIndex.query.filter(
            PerpetualAssetIndex.id == asset_id).first()
        if not asset_config:
            raise InvalidArgument(message='资产配置不存在')
        old_data = asset_config.to_dict(enum_to_name=True)

        asset_config.status = data["status"]
        if "is_visible" in data:
            asset_config.is_visible = data["is_visible"]
        if "status" in data and data["status"]:
            asset_config.status = data["status"]
        if "save_precision" in data and data["save_precision"]:
            asset_config.save_precision = data["save_precision"]
        if "show_precision" in data and data["show_precision"]:
            asset_config.show_precision = data["show_precision"]
        db.session.commit()
        PerpetualCoinTypeCache().reload()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.PerpetualAssets,
            old_data=old_data,
            new_data=asset_config.to_dict(enum_to_name=True),
        )


@ns.route('/assets')
@respond_with_code
class PerpetualAssetsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        is_visible=wa_fields.Boolean(required=True),
        status=wa_fields.String(required=True),
        name=wa_fields.String(required=True),
        save_precision=wa_fields.Integer(required=True, validate=lambda_positive_int),
        show_precision=wa_fields.Integer(required=True, validate=lambda_positive_int),
    ))
    def post(cls, **kwargs):
        """永续-永续配置-资产配置-添加资产配置"""
        name = kwargs.get('name', '').upper()
        data = kwargs

        asset_config = PerpetualAssetIndex.query.filter(
            PerpetualAssetIndex.name == name).first()
        if asset_config:
            raise InvalidArgument(message=f'{name}配置已存在')

        record = PerpetualAssetIndex()
        record.name = name
        record.save_precision = data['save_precision']
        record.show_precision = data['show_precision']
        record.status = data['status']
        record.is_visible = data['is_visible']
        db.session.add(record)

        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectPerpetual.PerpetualAssets,
            detail=record.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(),
        status=wa_fields.String(),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """永续-永续配置-资产配置列表"""
        _query = PerpetualAssetIndex.query
        if kwargs.get('status'):
            _query = _query.filter(
                PerpetualAssetIndex.status == kwargs['status'])
        if kwargs.get('name'):
            _query = _query.filter(PerpetualAssetIndex.name == kwargs['name'])

        records = _query.paginate(kwargs['page'], kwargs['limit'])
        asset_list = [item.name for item in PerpetualAssetIndex.query.all()]
        return dict(
            records=[record.to_dict() for record in records.items],
            count=_query.count(),
            status_dict={PerpetualAssetIndex.StatusType.OPEN.value: "开启",
                         PerpetualAssetIndex.StatusType.CLOSE.value: "关闭"},
            asset_list=asset_list,
        )


@ns.route('/update-config')
@respond_with_code
class UpdateConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        type=ex_fields.EnumField(
            enum=['asset', 'index', 'market']),
    ))
    def put(cls, **kwargs):
        """永续-通知更新永续配置"""
        client = PerpetualServerClient(current_app.logger)
        func_map = {
            'asset': client.update_assets,
            'index': client.update_index,
            'market': client.update_market,
        }
        type_ = kwargs['type']
        func_map[type_]()
        return
