# -*- coding: utf-8 -*-
from flask import g
from webargs import fields

from ....business import get_special_conf_create_operators
from ....exceptions import InvalidArgument, RecordNotFound
from ....models import User, WithdrawalPrivilegedUser, db, UserSpecialConfigChangeLog
from ....models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from ...common import Namespace, Resource, respond_with_code
from ...common.fields import PageField, LimitField


ns = Namespace('Withdrawal Privileged Users')


@ns.route('')
@respond_with_code
class UsersResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user=fields.String,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """特殊提现白名单列表"""
        model = WithdrawalPrivilegedUser
        query = model.query \
            .filter(model.status == model.Status.VALID)
        if user_keyword := kwargs.get('user'):
            query = query.filter(model.user_id.in_(
                User.search_for_users(user_keyword)))
        records = query \
            .order_by(model.id.desc()) \
            .paginate(kwargs['page'], kwargs['limit'])
        record_ids = [i.id for i in records.items]
        operator_id_dict, operator_name_dict = get_special_conf_create_operators(
            record_ids,
            UserSpecialConfigChangeLog.SpecialConfigType.WITHDRAWAL_PRIVILEGED_WHITELIST)
        res = []
        for row in records.items:
            item = row.to_dict()
            item.update(
                {
                    "operator": operator_name_dict.get(row.id),
                    "operator_id": operator_id_dict.get(row.id)}
            )
            res.append(item)
        return dict(
            items=res,
            total=records.total
        )

    @classmethod
    @ns.use_kwargs(dict(
        user=fields.String(required=True),
        remark=fields.String(missing='')
    ))
    def post(cls, **kwargs):
        """特殊提现白名单添加"""
        users = User.search_for_users(kwargs['user'])
        if not users:
            raise InvalidArgument(message=f'user does not exist')
        if len(users) != 1:
            raise InvalidArgument(message=f'ambiguous keyword')
        user_id = users[0]
        row: WithdrawalPrivilegedUser = WithdrawalPrivilegedUser.query \
            .filter(WithdrawalPrivilegedUser.user_id == user_id) \
            .first()
        if row is not None:
            if row.status == WithdrawalPrivilegedUser.Status.VALID:
                raise InvalidArgument
            row.status = WithdrawalPrivilegedUser.Status.VALID
        else:
            row = WithdrawalPrivilegedUser(
                user_id=user_id
            )
            db.session.add(row)
        row.remark = kwargs['remark']
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=row.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.WITHDRAWAL_PRIVILEGED_WHITELIST,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail='加入提现白名单',
            change_remark=kwargs['remark'],
            op_id=row.id
        )

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialWithdrawalPrivileged,
            detail=kwargs,
            target_user_id=user_id,
        )
        return row


# noinspection PyUnresolvedReferences
@ns.route('/<int:id_>')
@respond_with_code
class UserResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String
    ))
    def patch(cls, id_, **kwargs):
        """特殊提现白名单编辑"""
        model = WithdrawalPrivilegedUser
        row: model = model.query \
            .filter(model.id == id_,
                    model.status == model.Status.VALID) \
            .first()
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)
        if (remark := kwargs.get('remark')) is not None:
            row.remark = remark
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=row.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.WITHDRAWAL_PRIVILEGED_WHITELIST,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail='修改备注',
            change_remark=kwargs['remark'],
            op_id=row.id
        )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialWithdrawalPrivileged,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )
        return row

    @classmethod
    def delete(cls, id_):
        """特殊提现白名单删除"""
        model = WithdrawalPrivilegedUser
        row: model = model.query \
            .filter(model.id == id_,
                    model.status == model.Status.VALID) \
            .first()
        if row is not None:
            row.status = model.Status.DELETED
            db.session.commit()
            UserSpecialConfigChangeLog.add(
                user_id=row.user_id,
                config_type=UserSpecialConfigChangeLog.SpecialConfigType.WITHDRAWAL_PRIVILEGED_WHITELIST,
                op_type=UserSpecialConfigChangeLog.OpType.DELETE,
                admin_user_id=g.user.id,
                change_detail='删除提现白名单',
                op_id=row.id
            )

            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SpecialWithdrawalPrivileged,
                detail=dict(id=id_, remark=row.remark),
                target_user_id=row.user_id,
            )
        return {}
