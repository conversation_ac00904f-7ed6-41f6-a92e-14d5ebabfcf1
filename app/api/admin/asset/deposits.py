# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from typing import List, Dict
from flask import g
from flask_babel import gettext
from webargs import fields
from sqlalchemy import func, or_

from app import config
from app.common.constants import DepositWithdrawalDisableReason, ADMIN_EXPORT_LIMIT
from app.business.clients.wallet import WalletClient
from ....assets import list_all_assets, list_all_chains, has_asset, asset_to_chains, get_asset_chain_config, \
    chain_to_assets, normalise_asset_code, AssetChainConfig
from ....business import get_admin_user_name_map, CacheLock, LockKeys, PriceManager
from ....business.deposit_audit import DepositAuditBusiness
from ....business.kyt import DepositRiskAssessmentManager
from ....business.push import send_resume_deposit_withdrawal
from ....business.push_statistic import UserTagGroupBiz, DepositWithdrawalPopupWindowUserParser
from ....caches import AssetCache
from ....caches.user import UserVisitPermissionCache
from ....models import User, Deposit, db, KycVerification, File, ClearedUser, EmailToken
from ....models.kyt import DepositSenderRiskAssessment
from ....models.wallet import (
    DepositAudit,
    DepositWithdrawalPopupWindow,
    DepositWithdrawalPopupWindowContent, KYTDepositAudit, KYTDepositAuditDetail, EDDAudit, EDDAuditDetail,
)
from ....models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectWallet, OPNamespaceObjectOperation
from ....exceptions import AssetNotFound, RecordNotFound, InvalidArgument
from ....business.email import send_resume_deposit_withdrawal_notice_email, send_kyt_audit_email, send_edd_audit_email, \
    send_deposit_audit_email
from ...common import Resource, Namespace, respond_with_code
from ...common.fields import (EnumField, TimestampField, AssetField,
                              ChainField, PositiveDecimalField, PageField, LimitField)
from ....caches.deposits import (
    DepositMaintainInfoCache,
    WithdrawalMaintainInfoCache,
    DepositMaintainSubscriberCache,
    WithdrawalMaintainSubscriberCache, OfflinePopupWindowCache,
)
from ....utils import now, export_xlsx, batch_iter, amount_to_str, url_join
from ....utils.parser import JsonEncoder
from ....utils.date_ import timestamp_to_datetime, current_timestamp, datetime_to_utc8_str, datetime_to_str
from ....common import Language, LANGUAGE_NAMES, language_cn_names

ns = Namespace('Asset - Deposits')


@ns.route('')
@respond_with_code
class DepositsResource(Resource):

    status_dict = {
        'TOO_SMALL': '数额太小',
        'PROCESSING': '处理中',
        'AUDIT_REQUIRED': '待审核',
        'CONFIRMING': '确认中',
        'CANCELLED': '已取消',
        'FINISHED': '已完成',
        'TO_HOT': '已转热钱包',
        'EXCEPTION': '异常',
    }

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "wallet_deposit_id", Language.ZH_HANS_CN: "钱包"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "confirmed_at", Language.ZH_HANS_CN: "入账时间"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户"},
        {"field": "type", Language.ZH_HANS_CN: "类型"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "amount", Language.ZH_HANS_CN: "数量"},
        {"field": "address", Language.ZH_HANS_CN: "地址/站内转账发送方"},
        {"field": "memo", Language.ZH_HANS_CN: "Memo"},
        {"field": "tx_id", Language.ZH_HANS_CN: "交易 ID"},
        {"field": "confirmations", Language.ZH_HANS_CN: "确认数"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "assessment_status", Language.ZH_HANS_CN: "校验结果"},
        {"field": "audit_type", Language.ZH_HANS_CN: "待审核原因"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(),
        asset=AssetField(),
        chain=ChainField(),
        type=EnumField(Deposit.Type),
        status=EnumField(status_dict),
        audit_type=EnumField(DepositAudit.Type),
        start=TimestampField(),
        end=TimestampField(),
        user=fields.String(),
        tx_id=fields.String(),
        address=fields.String(),
        min_amount=PositiveDecimalField,
        max_amount=PositiveDecimalField,
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """充值记录"""
        asset = kwargs.get('asset')
        chain = kwargs.get('chain')
        if asset and not has_asset(asset, chain):
            raise AssetNotFound(asset, chain)

        export = kwargs['export']

        query = Deposit.query
        if id_ := kwargs.get('id'):
            query = query.filter(Deposit.id == id_)
        if chain:
            query = query.filter(Deposit.chain == chain)
        if asset:
            query = query.filter(Deposit.asset == asset)
        if (type_ := kwargs.get('type')) is not None:
            query = query.filter(Deposit.type == type_)
        if start := kwargs.get('start'):
            query = query.filter(Deposit.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(Deposit.created_at < end)
        if user := kwargs.get('user', '').strip():
            query = query.filter(
                Deposit.user_id.in_(User.search_for_users(user)))
        if tx_id := kwargs.get('tx_id'):
            query = query.filter(Deposit.tx_id == tx_id)
        if address := kwargs.get('address'):
            query = query.filter(or_(Deposit.address == address,
                                     Deposit.memo == address))
        if min_amount := kwargs.get('min_amount'):
            query = query.filter(Deposit.amount >= min_amount)
        if max_amount := kwargs.get('max_amount'):
            query = query.filter(Deposit.amount <= max_amount)
        if (status := kwargs.get('status')) is not None:
            if status == "AUDIT_REQUIRED":
                q_ids = cls.get_audit_required_deposit_ids(kwargs.get('audit_type'))
                query = query.filter(Deposit.id.in_(q_ids))
            else:
                query = query.filter(Deposit.status == Deposit.Status[status])

        if export:
            items = query.order_by(Deposit.id.desc()).limit(ADMIN_EXPORT_LIMIT).all()
            users = []
            addrs_url = []
            txs_url = []
            total = 0
        else:
            records = query \
                .order_by(Deposit.id.desc()) \
                .paginate(kwargs['page'], kwargs['limit'])
            items = records.items
            users = User.query \
                .filter(User.id.in_(set(d.user_id for d in items))) \
                .with_entities(User.id, User.email, User.name) \
                .all()
            wallet_client = WalletClient()
            addrs = [(x.chain, x.address) for x in items]
            txs = [(x.chain, x.tx_id) for x in items]
            addrs_url = wallet_client.get_explorer_addresses_url(addrs)
            txs_url = wallet_client.get_explorer_txs_url(txs)
            total = records.total

        all_dids = [i.id for i in items]
        ass_results = DepositRiskAssessmentManager.display_for_deposit_list(all_dids)
        d_ids = [i.id for i in items if i.status == Deposit.Status.PROCESSING and i.type == Deposit.Type.ON_CHAIN]
        audit_required_mapping = cls.filter_audit_required_deposit_ids(d_ids)

        def _get_status(_row: Deposit):
            if _row.id in audit_required_mapping:
                return "AUDIT_REQUIRED"
            return _row.status.name

        def _get_assessment_status_for_export(_status: str):
            if _status in [
                DepositSenderRiskAssessment.Status.APPROVED.name,
                DepositSenderRiskAssessment.Status.DECLINED.name,
            ]:
                return DepositSenderRiskAssessment.Status[_status].value
            return '-'

        data = [
            dict(
                id=v.id,
                created_at=v.created_at,
                updated_at=v.updated_at,
                wallet_deposit_id=v.wallet_deposit_id,
                user_id=v.user_id,
                type=v.type.name,
                asset=v.asset,
                chain=v.chain,
                address=v.address,
                amount=v.amount,
                memo=v.memo,
                attachment=v.attachment,
                sender_user_id=v.sender_user_id,
                tx_id=v.tx_id,
                vout=v.vout,
                block_height=v.block_height,
                confirmations=v.confirmations,
                confirmed_at=v.confirmed_at,
                status=_get_status(v),
                audit_type=audit_required_mapping[v.id].name if audit_required_mapping.get(v.id) else '',
                address_url=addrs_url[index] if not export else '',
                tx_url=txs_url[index] if not export else '',
                assessment_status=ass_results.get(v.id)
            )
            for index, v in enumerate(items)
        ]

        if export:
            for item in data:
                item['created_at'] = datetime_to_utc8_str(item['created_at'])
                item['confirmed_at'] = datetime_to_utc8_str(item['confirmed_at']) if item['confirmed_at'] else ''
                item['type'] = Deposit.Type[item['type']].value
                item['status'] = cls.status_dict[item['status']]
                item['assessment_status'] = _get_assessment_status_for_export(item['assessment_status'])
                item['audit_type'] = DepositAudit.Type[item['audit_type']].value if item['audit_type'] else ''
                item['address'] = item['address'] if item['type'] != Deposit.Type.LOCAL.value \
                    else item['sender_user_id']
            return export_xlsx(
                filename='asset_deposits',
                data_list=data,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=data,
            extra=dict(
                assets=asset_to_chains(),
                types=Deposit.Type,
                statuses=cls.status_dict,
                users={id_: dict(name=name, email=email)
                       for id_, email, name in users},
                audit_types=DepositAudit.Type,
                assessment_statuses=DepositSenderRiskAssessment.Status,
            )
        )

    @classmethod
    def get_audit_required_deposit_ids(cls, audit_type: DepositAudit.Type, limit: int = 1000) -> set[int]:
        q = DepositAudit.query.filter(
            DepositAudit.status.notin_([
                DepositAudit.Status.AUDITED,
                DepositAudit.Status.CANCELLED,
            ]),
        )
        if audit_type:
            q = q.filter(
                DepositAudit.type == audit_type
            )
        rows = q.with_entities(DepositAudit.deposit_id).limit(limit).all()
        return {i.deposit_id for i in rows}

    @classmethod
    def filter_audit_required_deposit_ids(cls, deposit_ids: list[int]) -> dict:
        if not deposit_ids:
            return {}
        rows = DepositAudit.query.filter(
            DepositAudit.deposit_id.in_(deposit_ids),
            DepositAudit.status.notin_([
                DepositAudit.Status.AUDITED,
                DepositAudit.Status.CANCELLED,
            ]),
        ).with_entities(
            DepositAudit.deposit_id,
            DepositAudit.type,
        ).all()
        return {i.deposit_id: i.type for i in rows}


@ns.route('/audit')
@respond_with_code
class DepositsAuditResource(Resource):

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "deposit_id", Language.ZH_HANS_CN: "充值ID"},
        {"field": "tx_id", Language.ZH_HANS_CN: "TXID"},
        {"field": "sender_risk_id", Language.ZH_HANS_CN: "KYT风险评估ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "user_email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "amount", Language.ZH_HANS_CN: "数量"},
        {"field": "amount_usd", Language.ZH_HANS_CN: "市值（USD）"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "audited_at", Language.ZH_HANS_CN: "审核时间"},
        {"field": "risk_level", Language.ZH_HANS_CN: "风险等级"},
        {"field": "type", Language.ZH_HANS_CN: "审核类型"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "audited_by_email", Language.ZH_HANS_CN: "操作人"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(),
        chain=ChainField(),
        status=EnumField(DepositAudit.Status),
        type=EnumField(DepositAudit.Type),
        risk_level=EnumField(EDDAudit.RiskLevel),
        keyword=fields.String,
        tx_id=fields.String,
        start_time=TimestampField,
        end_time=TimestampField,
        audited_start_time=TimestampField,
        audited_end_time=TimestampField,
        export=fields.Boolean,
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """充值审核列表"""
        query = DepositAudit.query.with_entities(
            DepositAudit, Deposit, EDDAudit
        ).join(
            Deposit, DepositAudit.deposit_id == Deposit.id
        ).outerjoin(
            EDDAudit, DepositAudit.edd_id == EDDAudit.id
        )
        if tx_id := kwargs.get('tx_id'):
            query = query.filter(Deposit.tx_id == tx_id)
        if asset := kwargs.get('asset'):
            query = query.filter(Deposit.asset == asset)
        if chain := kwargs.get('chain'):
            query = query.filter(Deposit.chain == chain)
        if status := kwargs.get('status'):
            query = query.filter(DepositAudit.status == status)
        if type_ := kwargs.get("type"):
            query = query.filter(DepositAudit.type == type_)
        user_ids = []
        if keyword := kwargs.get('keyword', '').strip():
            keyword_results = User.search_for_users(keyword)
            user_ids.extend(keyword_results)
            query = query.filter(DepositAudit.user_id.in_(keyword_results))
        if start_time := kwargs.get('start_time'):
            query = query.filter(DepositAudit.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(DepositAudit.created_at < end_time)
        if audited_start_time := kwargs.get('audited_start_time'):
            query = query.filter(DepositAudit.audited_at >= audited_start_time)
        if audited_end_time := kwargs.get('audited_end_time'):
            query = query.filter(DepositAudit.audited_at < audited_end_time)
        if risk_level := kwargs.get('risk_level'):
            query = query.filter(EDDAudit.risk_level == risk_level)

        query = query.order_by(DepositAudit.id.desc())
        export = kwargs.get("export")
        if export:
            records = query.limit(config['EXPORT_ITEM_MAX_COUNT']).all()
            total = 0
        else:
            paginate = query.paginate(kwargs['page'], kwargs['limit'])
            records = paginate.items
            total = paginate.total

        for (audit, _, _) in records:
            user_ids.append(audit.user_id)
            if audit.audited_by:
                user_ids.append(audit.audited_by)
        name_map = get_admin_user_name_map(user_ids)
        addrs, txs = [], []
        for (audit, dep, edd) in records:
            address = dep.address
            if edd and edd.address_from:
                address = edd.address_from
            addrs.append((dep.chain, address))
            txs.append((dep.chain, dep.tx_id))
        wallet_client = WalletClient()
        addrs_url = wallet_client.get_explorer_addresses_url(addrs)
        addrs_url_map = dict(zip(addrs, addrs_url))
        txs_url = wallet_client.get_explorer_txs_url(txs)

        result = []
        for i, (audit, dep, edd) in enumerate(records):
            amount_usd = amount_to_str(PriceManager.asset_to_usd(dep.asset) * dep.amount, 2)
            tmp = dict(
                audit=dict(
                    id=audit.id,
                    user_id=audit.user_id,
                    user_email=name_map.get(audit.user_id, '-'),
                    created_at=audit.created_at,
                    type=audit.type.value,
                    status=audit.status.value,
                    audited_by=audit.audited_by,
                    audited_by_email=name_map.get(audit.audited_by, '-'),
                    audited_at=audit.audited_at,
                    deposit_id=audit.deposit_id,
                    remark=audit.remark,
                ),
                dep=dict(
                    tx_id=dep.tx_id,
                    tx_url=txs_url[i],
                    asset=dep.asset,
                    chain=dep.chain,
                    amount=dep.amount,
                    amount_usd=amount_usd,
                )
            )
            if edd:
                tmp.update(edd=dict(
                    id=edd.id,
                    sender_risk_id=edd.sender_risk_id,
                    address_result=edd.address_result,
                    address_from=edd.address_from,
                    assessor=edd.assessor,
                    risk_result=edd.risk_result,
                    risk_level=edd.risk_level.value,
                    address_url=addrs_url_map.get((dep.chain, edd.address_from)),
                ))
            result.append(tmp)

        if export:
            result = cls.build_export_data(result)
            return export_xlsx(
                filename='deposits-audit.xlsx',
                data_list=result,
                export_headers=cls.export_headers
            )

        return dict(
            total=total,
            items=result,
            extra=dict(
                assets=list_all_assets(),
                chains=list_all_chains(),
                statuses=DepositAudit.Status,
                types=DepositAudit.Type,
                risk_levels=EDDAudit.RiskLevel,
            ))

    @classmethod
    def build_export_data(cls, result):
        for i in result:
            for f in ['created_at', 'audited_at']:
                i[f] = datetime_to_str(val) if (val := i['audit'].get(f)) else "-"
            for f in ['id', 'user_id', 'deposit_id', 'user_email', 'remark', 'audited_by_email', 'type', 'status']:
                i[f] = val if (val := i['audit'].get(f)) else "-"
            for f in ['tx_id', 'chain', 'asset', 'amount', 'amount_usd']:
                i[f] = i['dep'][f]
            for f in ['sender_risk_id', 'assessor']:
                i[f] = edd[f] if (edd := i.get('edd')) else "-"
            i['risk_level'] = edd['risk_level'] if (edd := i.get('edd')) else "-"
        return result

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True),
    ))
    def patch(cls, **kwargs):
        """充值审核-备注"""
        _id = kwargs['id']
        row = DepositAudit.query.filter(DepositAudit.id == _id).first()
        if not row:
            raise InvalidArgument
        old_remark = row.remark
        row.remark = kwargs['remark']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.DepositAudit,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=row.remark),
            target_user_id=row.user_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
    ))
    def put(cls, **kwargs):
        """充值审核-关联 EDD"""
        # TODO: 考虑将 EDD 与充值审核业务拆分，EDD 审核后进行业务通知
        _id = kwargs['id']
        row = DepositAudit.query.filter(DepositAudit.id == _id).first()
        if not row:
            raise InvalidArgument
        with CacheLock(LockKeys.deposit_audit(row.deposit_id)):
            db.session.rollback()
            row = DepositAudit.query.filter(DepositAudit.id == _id).first()
            if row.edd_id:
                raise InvalidArgument(message='该记录已关联 EDD！')
            if row.status in [
                DepositAudit.Status.AUDITED,
                DepositAudit.Status.FREEZING,
            ]:
                raise InvalidArgument(message='该记录已审核，不能手动关联 EDD！')
            edd = EDDAudit.get_or_create(
                deposit_id=row.deposit_id,
                user_id=row.user_id,
            )
            if edd.id:
                audit_status = EDDAudit.SYNC_STATUSES.get(edd.status)
                if audit_status:
                    row.status = audit_status
                    db.session.flush()
                DepositAuditBusiness.sync_status_by_edd(edd)
                db.session.commit()
            else:
                edd.source = EDDAudit.Source.DEPOSIT_AUDIT
                edd.status = EDDAudit.Status.INFO_REQUIRED
                row.status = EDDAudit.SYNC_STATUSES[edd.status]
                row.edd_id = edd.id
                db.session.add(edd)
                db.session.flush()
                DepositAuditBusiness.sync_status_by_edd(edd)
                db.session.commit()
                if edd.status == EDDAudit.Status.INFO_REQUIRED:  # 仅当状态是流转的状态才触发邮件：上一步同步状态时，可能被修改状态
                    send_edd_audit_email.delay(edd.id)
            AdminOperationLog.new_audit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.DepositAudit,
                detail={'audit_id': row.id, 'edd_id': edd.id},
                target_user_id=row.user_id,
            )


@ns.route('/edd-audit')
@respond_with_code
class EDDAuditResource(Resource):

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "流水ID"},
        {"field": "source", Language.ZH_HANS_CN: "来源"},
        {"field": "tx_id", Language.ZH_HANS_CN: "TXID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "user_email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "audited_at", Language.ZH_HANS_CN: "审核时间"},
        {"field": "created_by_email", Language.ZH_HANS_CN: "发起人"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "audited_by_email", Language.ZH_HANS_CN: "操作人"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        keyword=fields.String,
        source=EnumField(EDDAudit.Source),
        status=EnumField(EDDAudit.Status),
        tx_id=fields.String,
        start_time=TimestampField,
        end_time=TimestampField,
        audited_start_time=TimestampField,
        audited_end_time=TimestampField,
        export=fields.Boolean,
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """EDD 列表"""
        query = EDDAudit.query.with_entities(
            EDDAudit, Deposit
        ).outerjoin(
            Deposit, EDDAudit.deposit_id == Deposit.id
        )
        if tx_id := kwargs.get('tx_id'):
            query = query.filter(Deposit.tx_id == tx_id)
        if status := kwargs.get('status'):
            query = query.filter(EDDAudit.status == status)
        if source := kwargs.get("source"):
            query = query.filter(EDDAudit.source == source)
        user_ids = []
        if keyword := kwargs.get('keyword', '').strip():
            keyword_results = User.search_for_users(keyword)
            user_ids.extend(keyword_results)
            query = query.filter(EDDAudit.user_id.in_(keyword_results))
        if start_time := kwargs.get('start_time'):
            query = query.filter(EDDAudit.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(EDDAudit.created_at < end_time)
        if audited_start_time := kwargs.get('audited_start_time'):
            query = query.filter(EDDAudit.audited_at >= audited_start_time)
        if audited_end_time := kwargs.get('audited_end_time'):
            query = query.filter(EDDAudit.audited_at < audited_end_time)

        query = query.order_by(EDDAudit.id.desc())
        export = kwargs.get("export")
        if export:
            records = query.all()
            total = 0
        else:
            paginate = query.paginate(kwargs['page'], kwargs['limit'])
            records = paginate.items
            total = paginate.total

        for (edd, _) in records:
            user_ids.append(edd.user_id)
            if edd.audited_by:
                user_ids.append(edd.audited_by)
            if edd.created_by:
                user_ids.append(edd.created_by)
        name_map = get_admin_user_name_map(user_ids)
        addrs, txs = [], []
        edd_ids = []
        for (edd, dep) in records:
            edd_ids.append(edd.id)
            if dep:
                addrs.append((dep.chain, edd.address_from))
                txs.append((dep.chain, dep.tx_id))
        wallet_client = WalletClient()
        # addrs_url = wallet_client.get_explorer_addresses_url(addrs)
        # addrs_url_map = dict(zip(addrs, addrs_url))
        txs_url = wallet_client.get_explorer_txs_url(txs)
        txs_url_map =dict(zip(txs, txs_url))

        result = []
        edd_to_audit = cls._get_deposit_audits_by(edd_ids)
        for i, (edd, dep) in enumerate(records):
            tmp = dict(
                edd=dict(
                    id=edd.id,
                    audit_id=edd_to_audit.get(edd.id),
                    user_id=edd.user_id,
                    user_email=name_map.get(edd.user_id, '-'),
                    created_at=edd.created_at,
                    source=edd.source.value,
                    status=edd.status.value,
                    created_by=edd.created_by,
                    created_by_email=name_map.get(edd.created_by, '-'),
                    audited_by=edd.audited_by,
                    audited_by_email=name_map.get(edd.audited_by, '-'),
                    audited_at=edd.audited_at,
                    deposit_id=edd.deposit_id,
                    remark=edd.remark,
                ),
            )
            if dep:
                tmp.update(dep=dict(
                    tx_id=dep.tx_id,
                    tx_url=txs_url_map.get(dep.chain, dep.tx_id),
                ))
            result.append(tmp)

        if export:
            result = cls.build_export_data(result)
            return export_xlsx(
                filename='edd-audit.xlsx',
                data_list=result,
                export_headers=cls.export_headers
            )

        return dict(
            total=total,
            items=result,
            extra=dict(
                statuses=EDDAudit.Status,
                sources=EDDAudit.Source,
            ))

    @classmethod
    def build_export_data(cls, result):
        for i in result:
            edd = i['edd']
            for f in ['created_at', 'audited_at']:
                i[f] = datetime_to_str(val) if (val := edd.get(f)) else "-"
            for f in ['id', 'source', 'user_id', 'user_email', 'remark', 'created_by_email', 'status', 'audited_by_email']:
                i[f] = val if (val := i['edd'].get(f)) else "-"
            for f in ['tx_id']:
                i[f] = dep[f] if (dep := i.get('dep')) else "-"
        return result

    @classmethod
    def _get_deposit_audits_by(cls, edd_ids) -> dict:
        model = DepositAudit
        rows = model.query.with_entities(
            model.edd_id,
            model.id,
        ).filter(
            model.edd_id.in_(edd_ids),
        ).order_by(model.id).all()
        ret = {}
        for row in rows:
            ret[row.edd_id] = row.id
        return ret

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True),
    ))
    def post(cls, **kwargs):
        """EDD-创建 EDD 验证"""
        user_id = kwargs['user_id']
        model = EDDAudit
        row = model.query.filter(
            model.user_id == user_id,
            model.status.notin_([
                model.Status.AUDITED,
                model.Status.FREEZING,
            ]),
        ).first()
        if row:
            raise InvalidArgument(message='该用户存在处理中的EDD记录')
        row = model(
            user_id=user_id,
            source=model.Source.MANUAL,
            status=model.Status.INFO_REQUIRED,
            created_by=g.user.id,
        )
        db.session_add_and_commit(row)
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.EDDAudit,
            detail=kwargs,
            target_user_id=user_id,
        )
        send_edd_audit_email.delay(row.id)

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        remark=fields.String(required=True),
    ))
    def patch(cls, **kwargs):
        """EDD-备注"""
        _id = kwargs['id']
        row = EDDAudit.query.filter(EDDAudit.id == _id).first()
        if not row:
            raise InvalidArgument
        old_remark = row.remark
        row.remark = kwargs['remark']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.EDDAudit,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=row.remark),
            target_user_id=row.user_id,
        )


@ns.route('/edd-audit/details')
@respond_with_code
class EDDDetailResource(Resource):
    CHANGEABLE_STATUS_MAP = {
        EDDAudit.Source.DEPOSIT_AUDIT: {
            EDDAudit.Status.AUDIT_REQUIRED: [
                EDDAudit.Status.EXTRA_INFO_REQUIRED,
                EDDAudit.Status.FREEZING,
                EDDAudit.Status.AUDITED,
            ],
            EDDAudit.Status.FREEZING: [
                EDDAudit.Status.AUDITED,
            ],
            # 以下两种状态流转属于特殊情况：认为操作人已知转账情况，可以直接入账
            EDDAudit.Status.INFO_REQUIRED: [
                EDDAudit.Status.AUDITED,
            ],
            EDDAudit.Status.EXTRA_INFO_REQUIRED: [
                EDDAudit.Status.AUDITED,
            ],
        },
        EDDAudit.Source.MANUAL: {
            EDDAudit.Status.AUDIT_REQUIRED: [
                EDDAudit.Status.EXTRA_INFO_REQUIRED,
                EDDAudit.Status.AUDITED,
            ],
            EDDAudit.Status.INFO_REQUIRED: [
                EDDAudit.Status.AUDITED,
            ],
            EDDAudit.Status.EXTRA_INFO_REQUIRED: [
                EDDAudit.Status.AUDITED,
            ],
        },
    }
    REASON_STATUSES = [
        EDDAudit.Status.EXTRA_INFO_REQUIRED,
        EDDAudit.Status.FREEZING,
    ]

    @classmethod
    @ns.use_kwargs(dict(
        audit_id=fields.Integer(required=False),
        edd_id=fields.Integer(required=False),
    ))
    def get(cls, **kwargs):
        """钱包管理-web钱包管理-充值审核/EDD详情"""
        audit_id = kwargs.get('audit_id')
        edd_id = kwargs.get('edd_id')
        if not audit_id and not edd_id:
            raise InvalidArgument
        edd, audit, deposit = cls._get_related_objs(edd_id, audit_id)
        user_ids = []
        wallet_client = WalletClient()
        if audit:
            user_ids.append(audit.user_id)
            if edd:
                if edd.audited_by:
                    user_ids.append(edd.audited_by)
                if edd.created_by:
                    user_ids.append(edd.created_by)
            else:
                if audit.audited_by:
                    user_ids.append(audit.audited_by)
            name_map = get_admin_user_name_map(user_ids)
            user = User.query.get(audit.user_id)
            amount_usd = amount_to_str(PriceManager.asset_to_usd(deposit.asset) * deposit.amount, 2)
            detail = dict(
                audit_id=audit.id,
                type=audit.type.name,
                user_id=audit.user_id,
                user_email=name_map.get(audit.user_id, audit.user_id),
                created_at=audit.created_at,
                audited_by=audit.audited_by,
                audited_by_email=name_map.get(audit.audited_by, audit.audited_by),
                audited_at=audit.audited_at,
                remark=audit.remark,
                reject_reason=audit.rejection_reason,
                is_custom_reason=audit.is_custom_reason,
                custom_reject_reason=audit.custom_rejection_reason,

                kyc_status=user.kyc_status.name,
                audit_status=audit.status.name,

                chain=deposit.chain,
                asset=deposit.asset,
                tx_id=deposit.tx_id,
                amount=deposit.amount,
                amount_usd=amount_usd,
            )
            if edd:
                address = deposit.address
                if edd.address_from:
                    address = edd.address_from
                detail.update(dict(
                    edd_id=edd.id,
                    sender_risk_id=edd.sender_risk_id,
                    address_from=edd.address_from,
                    assessor=edd.assessor,
                    address_result=edd.address_result,
                    risk_result=edd.risk_result,
                    info_updated_at=edd.info_updated_at,
                    reject_reason=edd.rejection_reason,
                    is_custom_reason=edd.is_custom_reason,
                    custom_reject_reason=edd.custom_rejection_reason,
                    # remark=edd.remark,  # do not display
                    upload_url=cls._get_upload_link(edd.id),
                    file_info=cls._fetch_file_info(edd.id),
                    edd_status=edd.status.name,

                    address_url=wallet_client.get_explorer_addresses_url([(deposit.chain, address)])[0],
                    tx_url=wallet_client.get_explorer_txs_url([(deposit.chain, deposit.tx_id)])[0],
                ))
        else:
            user_ids.append(edd.user_id)
            if edd.audited_by:
                user_ids.append(edd.audited_by)
            name_map = get_admin_user_name_map(user_ids)
            user = User.query.get(edd.user_id)
            detail = dict(
                edd_id=edd.id,
                user_id=edd.user_id,
                user_email=name_map.get(edd.user_id, edd.user_id),
                created_at=edd.created_at,
                created_by=edd.created_by,
                created_by_email=name_map.get(edd.created_by, edd.created_by),
                audited_by=edd.audited_by,
                audited_by_email=name_map.get(edd.audited_by, edd.audited_by),
                audited_at=edd.audited_at,
                info_updated_at=edd.info_updated_at,
                reject_reason=edd.rejection_reason,
                is_custom_reason=edd.is_custom_reason,
                custom_reject_reason=edd.custom_rejection_reason,
                remark=edd.remark,
                edd_status=edd.status.name,
                upload_url=cls._get_upload_link(edd.id),
                file_info=cls._fetch_file_info(edd.id),

                kyc_status=user.kyc_status.name,
            )
        extra = dict(
            types=DepositAudit.Type,
            audit_statuses=DepositAudit.Status,
            edd_statuses=EDDAudit.Status,
            income_source_options=EDDAuditDetail.IncomeSource,
            deposit_source_options=EDDAuditDetail.DepositSource,
            reject_reasons={item.name: gettext(item.value) for item in KycVerification.RejectionReason},
        )
        detail.update(extra=extra)
        return detail

    @classmethod
    def _fetch_file_info(cls, edd_id: int) -> dict:
        model = EDDAuditDetail
        row = model.query.filter(
            model.edd_id == edd_id,
        ).first()
        ret = {}
        if not row:
            return ret
        income_file_ids = row.get_income_file_ids()
        deposit_file_ids = row.get_deposit_file_ids()
        id_with_photo_file_ids = row.get_id_with_photo_file_ids()
        address_file_ids = row.get_address_file_ids()
        supplement_file_ids = row.get_supplement_file_ids()
        file_ids = {
            *income_file_ids,
            *address_file_ids,
            *id_with_photo_file_ids,
        }
        if deposit_file_ids:
            file_ids.update(set(deposit_file_ids))
        if supplement_file_ids:
            file_ids.update(set(supplement_file_ids))
        files = cls._fetch_files(file_ids)
        ret.update(supplement_desc=row.supplement_desc or '-')

        def get_file_urls(ids):
            ids = ids or []
            urls = []
            for id_ in ids:
                file = files.get(id_)
                if file:
                    urls.append(file.private_url)
            return urls

        ret.update(income_file_urls=get_file_urls(income_file_ids))
        ret.update(address_file_urls=get_file_urls(address_file_ids))
        ret.update(id_with_photo_file_urls=get_file_urls(id_with_photo_file_ids))
        ret.update(deposit_file_urls=get_file_urls(deposit_file_ids))
        ret.update(supplement_file_urls=get_file_urls(supplement_file_ids))
        ret.update(dict(
            name=row.name,
            address=row.address,
            mobile=row.mobile,
            occupation=row.occupation,
            employer=row.employer,
            employer_address=row.employer_address,
            annual_income=row.annual_income,
            income_sources=row.get_income_sources(),
            income_source_options=model.IncomeSource,
            income_source_desc=row.income_source_desc,
            deposit_sources=row.get_deposit_sources(),
            deposit_source_options=model.DepositSource,
            deposit_source_desc=row.deposit_source_desc,
            has_legal_proceedings=row.has_legal_proceedings,
            legal_proceedings_desc=row.legal_proceedings_desc,
            has_pep=row.has_pep,
            pep_desc=row.pep_desc,
        ))
        return ret

    @classmethod
    def _fetch_files(cls, file_ids):
        if not file_ids:
            return {}
        files = File.query.filter(File.id.in_(file_ids)).all()
        return {file.id: file for file in files}

    @classmethod
    def _get_upload_link(cls, edd_id: int):
        site_url = config['SITE_URL']
        path = f'edd/material/{edd_id}'
        return url_join(site_url, path)

    @classmethod
    @ns.use_kwargs(dict(
        audit_id=fields.Integer(required=False),
        edd_id=fields.Integer(required=False),
        reject_reason=EnumField(KycVerification.RejectionReason, allow_none=True),
        is_custom_reason=fields.Boolean(missing=False),
        custom_reject_reason=fields.String,
        status=EnumField(EDDAudit.Status, required=True),
    ))
    def put(cls, **kwargs):
        """钱包管理-web钱包管理-充值审核/EDD详情-审核流转"""
        audit_id = kwargs.get('audit_id')
        edd_id = kwargs.get('edd_id')
        if not audit_id and not edd_id:
            raise InvalidArgument
        edd, audit, deposit = cls._get_related_objs(edd_id, audit_id)
        if audit:
            status = kwargs['status']
            audit_status = EDDAudit.STATUS_MAP[status]
            if not edd:  # 说明此笔充值审核无关联 edd 流程，可直接审核
                with CacheLock(LockKeys.deposit_audit(audit.deposit_id)):
                    db.session.rollback()
                    edd, audit, deposit = cls._get_related_objs(edd_id, audit_id)
                    changeable_status_map = {
                        DepositAudit.Status.AUDIT_REQUIRED: {
                            DepositAudit.Status.AUDITED,
                            DepositAudit.Status.FREEZING,
                        },
                        DepositAudit.Status.FREEZING: {
                            DepositAudit.Status.AUDITED,
                        }
                    }
                    if audit.status not in changeable_status_map:
                        raise InvalidArgument(message=f'current status {audit.status.name} cannot be changed')
                    changeable_statuses = changeable_status_map[audit.status]
                    if audit_status not in changeable_statuses:
                        raise InvalidArgument(
                            message=f'current status {audit.status.name} cannot be changed to {audit_status.name}')
                    detail = dict(
                        audit_id=audit.id,
                        status=f'{audit.status}->{audit_status}')
                    cls._validate_rejection_reason(kwargs)
                    cls._set_audit_rejection_reason(audit, kwargs)
                    audit.status = audit_status
                    audit.audited_by = g.user.id
                    audit.audited_at = now()
                    db.session.flush()
                    DepositAuditBusiness.sync_status_by_audit(audit.deposit_id)
                    db.session.commit()
                    if audit.status == audit_status:  # 仅当状态是流转的状态才触发邮件
                        send_deposit_audit_email.delay(audit.id)
                    AdminOperationLog.new_audit(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectWallet.DepositAudit,
                        detail=detail,
                        target_user_id=audit.user_id,
                    )
            else:
                with CacheLock(LockKeys.deposit_audit(audit.deposit_id)):
                    db.session.rollback()
                    edd, audit, deposit = cls._get_related_objs(edd_id, audit_id)
                    cls._validate_from_deposit_audit(audit, edd, kwargs)
                    cls._set_edd_rejection_reason(edd, kwargs)
                    detail = dict(
                        audit_id=audit.id,
                        edd_id=edd.id,
                        status=f'{edd.status}->{kwargs["status"]}')
                    edd.status = kwargs['status']
                    edd.audited_by = g.user.id
                    edd.audited_at = now()
                    audit.status = EDDAudit.STATUS_MAP[edd.status]
                    audit.audited_by = edd.audited_by
                    audit.audited_at = edd.audited_at
                    db.session.flush()
                    DepositAuditBusiness.sync_status_by_edd(edd)
                    db.session.commit()
                    if edd.status == kwargs['status']:  # 仅当状态是流转的状态才触发邮件
                        send_edd_audit_email.delay(edd.id)
                    AdminOperationLog.new_audit(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectWallet.DepositAudit,
                        detail=detail,
                        target_user_id=audit.user_id,
                    )
        else:  # ADMIN EDD 详情页进行审核，只能审核 EDDAudit.Source.MANUAL
            cls._validate_from_edd_audit(edd, kwargs)
            with CacheLock(LockKeys.edd_audit(edd.id)):
                db.session.rollback()
                edd = EDDAudit.query.get(edd.id)
                cls._validate_from_edd_audit(edd, kwargs)
                cls._set_edd_rejection_reason(edd, kwargs)
                detail = dict(
                    edd_id=edd.id,
                    status=f'{edd.status}->{kwargs["status"]}')

                edd.status = kwargs['status']
                edd.audited_by = g.user.id
                edd.audited_at = now()
                db.session.commit()
                send_edd_audit_email.delay(edd.id)
                AdminOperationLog.new_audit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectWallet.EDDAudit,
                    detail=detail,
                    target_user_id=edd.user_id,
                )

    @classmethod
    def _get_related_objs(cls, edd_id: int | None, audit_id: int | None):
        audit = deposit = None
        if audit_id:  # 从充值审核发起访问详情
            audit = DepositAudit.query.get(audit_id)
            if not audit:
                raise InvalidArgument
            deposit = Deposit.query.get(audit.deposit_id)
            edd = None
            if edd_id:
                edd = EDDAudit.query.get(edd_id)
                if not edd:
                    raise InvalidArgument
        else:  # 从 EDD 列表发起访问详情
            edd = EDDAudit.query.get(edd_id)
            if not edd:
                raise RecordNotFound
        return edd, audit, deposit

    @classmethod
    def _validate_from_edd_audit(cls, edd, kwargs):
        cls._validate_kwargs(edd, cls.CHANGEABLE_STATUS_MAP[EDDAudit.Source.MANUAL], kwargs)

    @classmethod
    def _validate_from_deposit_audit(cls, audit, edd, kwargs):
        cls._validate_kwargs(edd, cls.CHANGEABLE_STATUS_MAP[EDDAudit.Source.DEPOSIT_AUDIT], kwargs)
        if edd.status == EDDAudit.Status.FREEZING and kwargs['status'] == EDDAudit.Status.AUDITED:
            # edd 审核通过前，需检查多笔关联的充值审核记录状态，若有未通过，则不允许修改 edd 状态，且不允许该笔充值审核通过
            # 这属于体验优化，实际上审核是独立的，可以不考虑这个条件直接审核
            audits = DepositAudit.query.filter(
                DepositAudit.id != audit.id,
                DepositAudit.edd_id == edd.id,
                DepositAudit.status == DepositAudit.Status.FREEZING,
            ).all()
            if audits:
                raise InvalidArgument(message='禁止操作：存在多笔充值审核记录关联到 edd，且有未审核通过的记录！')

    @classmethod
    def _validate_kwargs(cls, edd, changeable_status_map, kwargs):
        if edd.status not in changeable_status_map:
            raise InvalidArgument(message=f'current status {edd.status.name} cannot be changed')
        changeable_statuses = changeable_status_map[edd.status]
        if kwargs['status'] not in changeable_statuses:
            raise InvalidArgument(message=f'current status {edd.status.name} cannot be changed to {kwargs["status"].name}')
        if kwargs['status'] in [
            EDDAudit.Status.EXTRA_INFO_REQUIRED,
            EDDAudit.Status.FREEZING,
        ]:
            if kwargs['is_custom_reason']:
                if not kwargs.get('custom_reject_reason'):
                    raise InvalidArgument(message='自定义拒绝原因未填写')
            else:
                if not kwargs.get('reject_reason'):
                    raise InvalidArgument(message='拒绝原因未选择')

    @classmethod
    def _validate_rejection_reason(cls, kwargs):
        if kwargs['status'] in [
            EDDAudit.Status.EXTRA_INFO_REQUIRED,
            EDDAudit.Status.FREEZING,
        ]:
            if kwargs['is_custom_reason']:
                if not kwargs.get('custom_reject_reason'):
                    raise InvalidArgument(message='自定义拒绝原因未填写')
            else:
                if not kwargs.get('reject_reason'):
                    raise InvalidArgument(message='拒绝原因未选择')


    @classmethod
    def _set_edd_rejection_reason(cls, edd, kwargs):
        if kwargs['status'] in cls.REASON_STATUSES:
            if kwargs['is_custom_reason']:
                edd.is_custom_reason = True
                edd.custom_rejection_reason = kwargs['custom_reject_reason']
            else:
                edd.rejection_reason = kwargs['reject_reason'].name
                edd.is_custom_reason = False

    @classmethod
    def _set_audit_rejection_reason(cls, audit, kwargs):
        if kwargs['status'] in cls.REASON_STATUSES:
            if kwargs['is_custom_reason']:
                audit.is_custom_reason = True
                audit.custom_rejection_reason = kwargs['custom_reject_reason']
            else:
                audit.rejection_reason = kwargs['reject_reason'].name
                audit.is_custom_reason = False

    @classmethod
    @ns.use_kwargs(dict(
        edd_id=fields.Integer(required=True),
        remark=fields.String(required=True),
    ))
    def patch(cls, **kwargs):
        """钱包管理-充值管理-充值风险评估备注"""
        row: EDDAudit = EDDAudit.query.get(kwargs['edd_id'])
        old_remark = row.remark
        row.remark = kwargs['remark']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.EDDAudit,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=row.remark),
            target_user_id=row.user_id,
        )


@ns.route("/deposit-withdraw-popup-windows")
@respond_with_code
class DepositWithdrawalPopupWindowsResource(Resource):

    STATUSES = dict(pending="待上架", online="上架中", offline="已下架")
    model = DepositWithdrawalPopupWindow

    @classmethod
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
            asset=AssetField(),
            chain=ChainField(),
            status=EnumField(list(STATUSES)),
            platform=EnumField(model.Platform, enum_by_value=True),
            offline_type=EnumField(model.OfflineType),
            id=fields.String(missing=''),
        )
    )
    def get(cls, **kwargs):
        """ 币种管理>充提弹窗配置-列表 """
        _now = now()
        query = cls.model.query.filter(
            cls.model.status == cls.model.Status.VALID,
        )
        if id := kwargs.get("id"):
            query = query.filter(cls.model.id == id)
        if asset := kwargs.get("asset"):
            query = query.filter(cls.model.asset == asset)
        if chain := kwargs.get("chain"):
            query = query.filter(cls.model.chain == chain)
        if offline_type := kwargs.get("offline_type"):
            query = query.filter(cls.model.offline_type == offline_type)
        if status := kwargs.get("status"):
            if status == "pending":
                query = query.filter(cls.model.started_at > _now)
            elif status == "online":
                query = query.filter(
                    cls.model.started_at <= _now,
                    cls.model.ended_at >= _now,
                )
            else:
                query = query.filter(cls.model.ended_at < _now)
        if platform := kwargs.get("platform"):
            query = query.filter(cls.model.platform == platform)

        records = query.order_by(cls.model.sort_id.desc()).paginate(kwargs["page"], kwargs["limit"])

        table_items = []
        for x in records.items:
            if x.started_at > _now:
                status = "pending"
            elif x.ended_at < _now:
                status = "offline"
            else:
                status = "online"
            row_dict = x.to_dict()
            row_dict["status"] = status
            table_items.append(row_dict)

        asset_chains_dict = asset_to_chains()
        chain_assets_dict = defaultdict(list)
        for asset, chains in asset_chains_dict.items():
            for c in chains:
                chain_assets_dict[c].append(asset)

        return dict(
            items=table_items,
            total=records.total,
            extra=dict(
                frequency_dict={
                    cls.model.Frequency.ONCE.value: "一次",
                    cls.model.Frequency.EVERY_DAY.value: "每天",
                    cls.model.Frequency.EVERY_TIME.value: "每次",
                },
                platform_dict={
                    cls.model.Platform.ALL.value: "全部",
                    cls.model.Platform.WEB.value: "WEB",
                    cls.model.Platform.APP.value: "APP",
                },
                trigger_page_dict={
                    cls.model.TriggerPage.DEPOSIT.value: "充值",
                    cls.model.TriggerPage.WITHDRAWAL.value: "提现",
                },
                offline_type_dict={i.name: i.value for i in cls.model.OfflineType},
                status_dict=cls.STATUSES,
                languages=[
                    {"lang": lang.name, "display": LANGUAGE_NAMES[lang].chinese}
                    for lang in DepositWithdrawalPopupWindowContent.AVAILABLE_LANGS
                    if lang in LANGUAGE_NAMES
                ],
                assets=asset_chains_dict,
                chain_assets_dict=chain_assets_dict,
                window_list={i.id: i.name for i in cls.model.query.filter(
                    cls.model.status == cls.model.Status.VALID
                )},
            ),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            name=fields.String(required=True),
            asset=AssetField(required=False, default=''),
            chain=ChainField(required=True),
            started_at=fields.DateTime(required=True),
            ended_at=fields.DateTime(required=True),
            platform=EnumField(model.Platform, required=True),
            filter_type=EnumField(
                model.FilterType,
                missing=model.FilterType.NONE
            ),
            whitelist_enabled=fields.Boolean(missing=False),
            user_whitelist=fields.String(required=False),
            groups=fields.List(fields.Integer),
            trigger_page=EnumField(model.TriggerPage, required=True),
            frequency=EnumField(model.Frequency, required=True),
            offline_type=EnumField(model.OfflineType, required=True),
            jump_page_enabled=fields.Boolean(missing=False),
            jump_type=EnumField(model.JumpType),
            jump_id=fields.Integer,
        )
    )
    def post(cls, **kwargs):
        """ 币种管理>充提弹窗配置-新增弹窗 """

        sort_id = (
            cls.model.query.filter(
                cls.model.status == cls.model.Status.VALID,
            )
            .with_entities(func.max(cls.model.sort_id))
            .scalar()
            or 0
        )
        asset = kwargs.get("asset") or ""  # ALL
        row = cls.model(
            name=kwargs["name"],
            chain=kwargs["chain"],
            asset=asset,
            started_at=kwargs["started_at"],
            ended_at=kwargs["ended_at"],
            platform=kwargs["platform"],
            filter_type=kwargs['filter_type'],
            whitelist_enabled=kwargs['whitelist_enabled'],
            user_whitelist=kwargs.get('user_whitelist') or '',
            trigger_page=kwargs["trigger_page"],
            frequency=kwargs["frequency"],
            offline_type=kwargs["offline_type"],
            jump_page_enabled=kwargs['jump_page_enabled'],
            jump_type=kwargs.get('jump_type'),
            jump_id=kwargs.get('jump_id'),
            sort_id=sort_id + 1,
        )
        if row.filter_type == cls.model.FilterType.FILTERS:
            if not (groups := kwargs.get('groups')):
                raise InvalidArgument
            groups = UserTagGroupBiz.filter_tag_group_ids(ids=groups)
            row.groups = json.dumps(groups)
            _, users = DepositWithdrawalPopupWindowUserParser(row).parse()
            row.users = json.dumps(list(users))
            row.target_user_number = len(users)
        db.session_add_and_commit(row)

        DepositWithdrawalPopupWindowResource.refresh_popup_window_cache(row)
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositWithdrawPopupWindows,
            detail=kwargs,
        )
        return dict(id=row.id)


@ns.route("/deposit-withdraw-popup-windows/<int:id_>")
@respond_with_code
class DepositWithdrawalPopupWindowResource(Resource):
    model = DepositWithdrawalPopupWindow

    @classmethod
    def refresh_popup_window_cache(cls, row: model) -> None:
        from app.caches.deposits import DepositPopupWindowCache, WithdrawalPopupWindowCache

        if row.trigger_page == cls.model.TriggerPage.DEPOSIT:
            DepositPopupWindowCache.reload()
        if row.trigger_page == cls.model.TriggerPage.WITHDRAWAL:
            WithdrawalPopupWindowCache.reload()
        OfflinePopupWindowCache.reload()

    @classmethod
    def get(cls, id_):
        """币种管理>充提弹窗配置-弹窗详情"""
        lang_names = language_cn_names()
        asset_chains_dict = asset_to_chains()
        chain_assets_dict = defaultdict(list)
        for asset, chains in asset_chains_dict.items():
            for c in chains:
                chain_assets_dict[c].append(asset)

        extra = dict(
            frequencies={
                cls.model.Frequency.ONCE.name: "一次",
                cls.model.Frequency.EVERY_DAY.name: "每天",
                cls.model.Frequency.EVERY_TIME.name: "每次",
            },
            platforms={
                cls.model.Platform.ALL.name: "全部",
                cls.model.Platform.WEB.name: "WEB",
                cls.model.Platform.APP.name: "APP",
            },
            trigger_pages={
                cls.model.TriggerPage.DEPOSIT.name: "充值",
                cls.model.TriggerPage.WITHDRAWAL.name: "提现",
            },
            statuses=cls.model.Status,
            assets=list_all_assets(),
            filter_types={
                cls.model.FilterType.FILTERS.name: '定向用户',
                cls.model.FilterType.NONE.name: '全部用户'
            },
            offline_type_dict={i.name: i.value for i in cls.model.OfflineType},
            asset_chains_dict=asset_chains_dict,
            chain_assets_dict=chain_assets_dict,
            languages={e.name: lang_names[e] for e in DepositWithdrawalPopupWindowContent.AVAILABLE_LANGS},
            jump_types=cls.model.JumpType,
        )
        if not id_:
            return dict(
                extra=extra
            )
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        res = row.to_dict(enum_to_name=True)
        res['target_user_number'] = ('-' if row.filter_type == cls.model.FilterType.NONE
                                     else row.target_user_number)

        extra.update(tag_groups=UserTagGroupBiz.get_tag_group_info(row.get_groups()))
        res['extra'] = extra
        return res

    @classmethod
    @ns.use_kwargs(
        dict(
            name=fields.String(required=True),
            started_at=fields.DateTime(required=True),
            ended_at=fields.DateTime(required=True),
            platform=EnumField(model.Platform, required=True),
            filter_type=EnumField(
                model.FilterType,
                missing=model.FilterType.NONE
            ),
            groups=fields.List(fields.Integer),
            whitelist_enabled=fields.Boolean(missing=False),
            user_whitelist=fields.String(required=False),
            trigger_page=EnumField(model.TriggerPage, required=True),
            frequency=EnumField(model.Frequency, required=True),
            offline_type=EnumField(model.OfflineType, required=True),
            jump_page_enabled=fields.Boolean(missing=False),
            jump_type=EnumField(model.JumpType),
            jump_id=fields.Integer,
        )
    )
    def put(cls, id_, **kwargs):
        """ 币种管理>充提弹窗配置-编辑弹窗 """
        row = cls.model.query.get(id_)
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)
        for field in {
            "name", "started_at", "ended_at", "platform",
            "trigger_page", "frequency", "offline_type",
            "whitelist_enabled", "filter_type"
        }:
            if val := kwargs.get(field):
                setattr(row, field, val)
        row.user_whitelist = kwargs.get('user_whitelist') or ''
        if row.filter_type == cls.model.FilterType.FILTERS:
            if not (groups := kwargs.get('groups')):
                raise InvalidArgument
            groups = UserTagGroupBiz.filter_tag_group_ids(ids=groups)
            row.groups = json.dumps(groups)
            _, users = DepositWithdrawalPopupWindowUserParser(row).parse()
            row.users = json.dumps(list(users))
            row.target_user_number = len(users)
        row.jump_page_enabled = kwargs['jump_page_enabled']
        row.jump_type = kwargs.get('jump_type')
        row.jump_id = kwargs.get('jump_id')

        db.session.commit()
        cls.refresh_popup_window_cache(row)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositWithdrawPopupWindows,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    def patch(cls, id_):
        """ 币种管理>充提弹窗配置-编辑排序 """
        row = cls.model.query.get(id_)
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)
        other = cls.model.query.filter(
            cls.model.status == cls.model.Status.VALID,
            cls.model.sort_id > row.sort_id,
        ).order_by(cls.model.sort_id.asc()).first()
        if other:
            row.sort_id, other.sort_id = other.sort_id, row.sort_id
            db.session.commit()
        cls.refresh_popup_window_cache(row)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositWithdrawPopupWindows,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    def delete(cls, id_):
        """ 币种管理>充提弹窗配置-删除弹窗 """
        row = cls.model.query.get(id_)
        if not row:
            raise InvalidArgument
        row.status = cls.model.Status.DELETED
        db.session.commit()
        cls.refresh_popup_window_cache(row)
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositWithdrawPopupWindows,
            detail=dict(id=id_, name=row.name),
        )
        return {}


@ns.route('/deposit-withdraw-popup-windows/<int:id_>/langs/<lang>')
@respond_with_code
class DepositWithdrawalPopupWindowLangResource(Resource):
    model = DepositWithdrawalPopupWindowContent

    @classmethod
    def get(cls, id_, lang):
        """币种管理>充提弹窗配置-编辑弹窗语言"""
        row = cls._get_row(id_, lang)
        if row is None:
            return dict(
                title='',
                content='',
            )
        return dict(
            title=row.title,
            content=row.content,
        )

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String(required=True),
        content=fields.String(required=True)
    ))
    def put(cls, id_, lang, **kwargs):
        """运营-通知栏-编辑内容"""
        title = kwargs['title'] or ''
        content = kwargs['content'] or ''
        if title == '' or content == '':
            raise InvalidArgument(message=f"{lang}标题或者内容不能为空")

        row = cls._get_row(id_, lang)
        model = cls.model
        if row is None:
            row = model(
                popup_window_id=id_,
                lang=lang,
                title=title,
                url='',
                content=content
            )
            db.session.add(row)
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.DepositWithdrawPopupWindows,
                detail=row.to_dict(enum_to_name=True),
            )
        else:
            old_data = row.to_dict(enum_to_name=True)
            row.title = title
            row.content = content
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.DepositWithdrawPopupWindows,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
                special_data=dict(lang=str(lang), popup_window_id=row.popup_window_id),
            )
        db.session.commit()
        return row

    @classmethod
    def _get_row(cls, id_: int, lang: str):
        model = cls.model
        return model.query.filter(
            model.popup_window_id == id_,
            model.lang == getattr(Language, lang, '')
        ).first()


@ns.route("/deposit-withdraw-disable-info")
@respond_with_code
class DepositWithdrawalDisableInfosResource(Resource):

    export_headers = (
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "type", Language.ZH_HANS_CN: "关闭类型"},
        {"field": "is_visible", Language.ZH_HANS_CN: "前端可见"},
        {"field": "reason", Language.ZH_HANS_CN: "原因"},
        {"field": "url", Language.ZH_HANS_CN: "公告链接"},
        {"field": "disable_at", Language.ZH_HANS_CN: "关闭时间UTC"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    order_type_dict = {
        "asset": "币种",
        "disable_at": "关闭时间",
    }

    # 钱包端的充提关闭原因
    """
    rename 就是改名或者换合约之类的
    node maintenance 就是节点维护
    risk control 就是因为风控原因关闭的
    delisting 就是这个币准备下架了
    还有 network anomaly 就是网络被攻击或者不稳定之类的
    还有 insufficient balance 就是热钱包不够币关了提现
    无法归类的就是 other
    """
    wallet_suspension_reason_dict = {
        "OTHER": "无法归类",
        "NODE_MAINTENANCE": "节点维护",
        "NETWORK_UPGRADE": "网络升级",
        "NETWORK_ANOMALY": "网络被攻击或不稳定",
        "RISK_CONTROL": "风控原因关闭",
        "RENAME_OR_IDENTITY_CHANGE": "改名或更换合约",
        "DELISTING": "准备下架",
        # withdrawal reasons
        "INSUFFICIENT_BALANCE": "热钱包余额不足",
    }

    @classmethod
    def get_maintain_infos(cls, q_asset: str, q_chain: str) -> tuple[list, list]:
        deposit_items = []
        withdrawal_items = []
        now_ = now()
        now_ts = int(now_.timestamp())
        reason_class = DepositWithdrawalDisableReason
        all_chains = AssetCache.list_all_chains()
        all_assets = AssetCache.list_all_assets()
        for asset, chains in asset_to_chains().items():
            asset = normalise_asset_code(asset)
            if q_asset and q_asset != asset:
                continue
            for chain_ in chains:
                chain_ = normalise_asset_code(chain_)
                if q_chain and q_chain != chain_:
                    continue
                if chain_ not in all_chains or asset not in all_assets:
                    continue
                ac_conf = AssetChainConfig(asset, chain_)
                if ac_conf.deposits_all_enabled and ac_conf.withdrawals_all_enabled:
                    continue
                if not ac_conf.deposits_all_enabled:
                    disable_at = int(ac_conf.deposits_updated_at.timestamp()) if ac_conf.deposits_updated_at else now_ts
                    if ac_conf.deposit_suspend_by_delisting:
                        reason = reason_class.DEPOSIT_SUSPENDED_OFFLINE_ASSET
                    else:
                        reason = reason_class.DEPOSIT_SUSPENDED_WALLET_MAINTENANCE
                    maintain_info = dict(
                        asset=asset,
                        chain=chain_,
                        url="",
                        remark=";".join([str(cls.wallet_suspension_reason_dict.get(i, i)) for i in ac_conf.deposit_suspension_reasons]),
                        valid=True,
                        disable_at=disable_at,
                        reason=reason.name,
                        is_visible=bool(ac_conf.is_visible),
                        type="DEPOSIT",
                    )
                    deposit_items.append(maintain_info)
                if not ac_conf.withdrawals_all_enabled:
                    disable_at = int(ac_conf.withdrawals_updated_at.timestamp()) if ac_conf.withdrawals_updated_at else now_ts
                    if ac_conf.withdrawal_suspend_by_delisting:
                        reason = reason_class.WITHDRAWAL_SUSPENDED_OFFLINE_ASSET
                    else:
                        reason = reason_class.WITHDRAWAL_SUSPENDED_WALLET_MAINTENANCE
                    maintain_info = dict(
                        asset=asset,
                        chain=chain_,
                        url="",
                        remark=";".join([str(cls.wallet_suspension_reason_dict.get(i, i)) for i in ac_conf.withdrawal_suspension_reasons]),
                        valid=True,
                        disable_at=disable_at,
                        reason=reason.name,
                        is_visible=bool(ac_conf.is_visible),
                        type="WITHDRAWAL",
                    )
                    withdrawal_items.append(maintain_info)
        return deposit_items, withdrawal_items

    @classmethod
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
            asset=AssetField(),
            chain=ChainField(),
            type=EnumField(["DEPOSIT", "WITHDRAWAL"]),
            is_visible=fields.Boolean,
            order_type=EnumField(list(order_type_dict.keys()), default="disable_at"),
            export=fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """ 币种管理>充提关闭列表 """
        page, limit = kwargs["page"], kwargs["limit"]
        reason_class = DepositWithdrawalDisableReason

        items = []
        q_asset = kwargs.get("asset")
        q_chain = kwargs.get("chain")
        deposit_items, withdrawal_items = cls.get_maintain_infos(q_asset, q_chain)
        if disable_type := kwargs.get("type"):
            if disable_type == "DEPOSIT":
                items.extend(deposit_items)
            else:
                items.extend(withdrawal_items)
        else:
            items.extend(deposit_items)
            items.extend(withdrawal_items)

        if kwargs.get("order_type") == "asset":
            items.sort(key=lambda x: x["asset"])
        else:
            items.sort(key=lambda x: x["disable_at"], reverse=True)

        # 是否前端可见 过滤
        if (is_visible := kwargs.get("is_visible")) is None:
            filter_records = items
        else:
            filter_records = [i for i in items if i["is_visible"] == is_visible]

        asset_responsible_dev_map = dict()
        client = WalletClient()
        asset_infos = client.get_assets()
        for info in asset_infos:
            for c in info['chains']:
                asset_responsible_dev_map[(info['code'], c['chain_name'])] = c['responsible_developer']

        # 写入admin编辑的信息
        for i in filter_records:
            asset_ = i["asset"]
            chain_ = i["chain"]
            if i["type"] == "DEPOSIT":
                cache = DepositMaintainInfoCache(asset_, chain_)
            else:
                cache = WithdrawalMaintainInfoCache(asset_, chain_)
            info = cache.read()
            if info:
                i.update({k: v for k, v in json.loads(info).items() if k in ["url", "reason", "remark"]})
            i["responsible_developer"] = asset_responsible_dev_map.get((asset_, chain_), "")
            _reason = reason_class[i["reason"]]
            params = {}
            if _reason in [
                reason_class.DEPOSIT_SUSPENDED_OFFLINE_ASSET,
                reason_class.WITHDRAWAL_SUSPENDED_OFFLINE_ASSET,
            ]:
                params = {"asset": asset_, "chain": chain_}
            i["reason_str"] = gettext(_reason.value, **params)

        if kwargs.get("export"):
            visible_dict = {True: "是", False: "否"}
            for i in filter_records:
                asset_ = i["asset"]
                chain_ = i["chain"]
                i["disable_at"] = timestamp_to_datetime(i["disable_at"]).strftime("%Y-%m-%d %H:%M:%S")
                i["type"] = "充值" if i["type"] == "DEPOSIT" else "提现"
                i["remark"] = i.get("remark", "")
                i["is_visible"] = visible_dict.get(i["is_visible"], i["is_visible"])
                _reason = reason_class[i["reason"]]
                params = {}
                if _reason in [
                    reason_class.DEPOSIT_SUSPENDED_OFFLINE_ASSET,
                    reason_class.WITHDRAWAL_SUSPENDED_OFFLINE_ASSET,
                ]:
                    params = {"asset": asset_, "chain": chain_}
                i["reason"] = gettext(_reason.value, **params)
            return export_xlsx(
                filename="deposit-withdraw-disable-info-list",
                data_list=filter_records,
                export_headers=cls.export_headers,
            )

        total = len(filter_records)
        page_items = filter_records[(page - 1) * limit: page * limit]
        asset_chains_dict = asset_to_chains()
        chain_assets_dict = defaultdict(list)
        for asset, chains in asset_chains_dict.items():
            for c in chains:
                if c != asset:
                    chain_assets_dict[c].append(asset)

        deposit_reason_dict = {
            i.name: gettext(i.value)
            for i in [
                reason_class.DEPOSIT_SUSPENDED,
                reason_class.DEPOSIT_SUSPENDED_SMART_CONTRACT_UPDATE,
                reason_class.DEPOSIT_SUSPENDED_MAINNET_UPGRADE,
                reason_class.DEPOSIT_SUSPENDED_WALLET_MAINTENANCE,
                reason_class.DEPOSIT_SUSPENDED_OFFLINE_ASSET,
            ]
        }
        withdrawal_reason_dict = {
            i.name: gettext(i.value)
            for i in [
                reason_class.WITHDRAWAL_SUSPENDED,
                reason_class.WITHDRAWAL_SUSPENDED_SMART_CONTRACT_UPDATE,
                reason_class.WITHDRAWAL_SUSPENDED_MAINNET_UPGRADE,
                reason_class.WITHDRAWAL_SUSPENDED_WALLET_MAINTENANCE,
                reason_class.WITHDRAWAL_SUSPENDED_OFFLINE_ASSET,
            ]
        }
        return dict(
            items=page_items,
            total=total,
            extra=dict(
                type_dict={
                    "DEPOSIT": "充值",
                    "WITHDRAWAL": "提现",
                },
                deposit_reason_dict=deposit_reason_dict,
                withdrawal_reason_dict=withdrawal_reason_dict,
                assets=asset_chains_dict,
                chain_assets_dict=chain_assets_dict,
                order_type_dict=cls.order_type_dict,
            ),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            # 3个标识字段
            asset=AssetField(required=True),
            chain=fields.String(required=True),
            type=EnumField(["DEPOSIT", "WITHDRAWAL"], required=True),
            #
            disable_at=TimestampField(required=True),
            reason=fields.String(required=True),
            url=fields.String(required=False),
            remark=fields.String(required=False),
        )
    )
    def put(cls, **kwargs):
        """ 币种管理>充提关闭列表-原因配置 """
        asset_ = kwargs["asset"]
        chain_ = kwargs["chain"]
        ac_conf = get_asset_chain_config(asset_, chain_)
        if kwargs["type"] == "DEPOSIT":
            if ac_conf.deposits_all_enabled:
                raise InvalidArgument(message="充值已开启，禁止修改")
            cache = DepositMaintainInfoCache(asset_, chain_)
        else:
            if ac_conf.withdrawals_all_enabled:
                raise InvalidArgument(message="提现已开启，禁止修改")
            cache = WithdrawalMaintainInfoCache(asset_, chain_)

        info = {}
        if disable_at := kwargs.get("disable_at"):
            if kwargs["type"] == "DEPOSIT":
                ac_conf.deposits_updated_at = disable_at
            else:
                ac_conf.withdrawals_updated_at = disable_at
        if reason := kwargs.get("reason"):
            info["reason"] = reason
        if (url := kwargs.get("url")) is not None:
            if len(url) >= 256:
                raise InvalidArgument(message="url字段不能超过256")
            info["url"] = url
        if (remark := kwargs.get("remark")) is not None:
            if len(remark) >= 256:
                raise InvalidArgument(message="remark字段不能超过256")
            info["remark"] = remark

        cache.set(json.dumps(info, cls=JsonEncoder))
        return {}


@ns.route("/send-deposit-withdraw-resume-email")
@respond_with_code
class SendDepositWithdrawalResumesEmail(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            asset=AssetField(required=False),  # 可为空，支持整个链上的全部币种
            chain=ChainField(required=True),
            type=EnumField(["DEPOSIT", "WITHDRAWAL"], required=True),
            confirm=fields.Boolean(),
        )
    )
    def post(cls, **kwargs):
        """ 系统>币种设置-发送充提恢复邮件 """
        asset_ = kwargs.get("asset")
        chain_ = kwargs["chain"]
        type_ = kwargs["type"]
        if not asset_:
            assets = chain_to_assets("CSC")
        else:
            assets = [asset_]

        maintain_items = [{"asset": a, "chain": chain_, "type": type_} for a in assets]
        if type_ == "DEPOSIT":
            subs_cache_class = DepositMaintainSubscriberCache
        else:
            subs_cache_class = WithdrawalMaintainSubscriberCache

        if not kwargs.get("confirm"):
            asset_subscriber_count_map = {}
            for i in maintain_items:
                subs_cache = subs_cache_class(i["asset"], i["chain"])
                asset_subscriber_count_map[i["asset"]] = subs_cache.scard()
            return {"asset_subscriber_count_map": asset_subscriber_count_map}

        cls.send_resume_deposit_withdrawal_notice(maintain_items, type_)
        return {}

    @classmethod
    def send_resume_deposit_withdrawal_notice(cls, maintain_items: List[Dict], type_: str):
        """ 充值订阅恢复、提现订阅恢复 """
        from app.assets import is_multi_chain, get_chain

        if type_ == "DEPOSIT":
            info_cache_class = DepositMaintainInfoCache
            subs_cache_class = DepositMaintainSubscriberCache
        else:
            info_cache_class = WithdrawalMaintainInfoCache
            subs_cache_class = WithdrawalMaintainSubscriberCache

        for i in maintain_items:
            asset = i["asset"]
            chain_ = i["chain"]
            subs_cache = subs_cache_class(asset, chain_)
            user_ids = [int(i) for i in subs_cache.smembers()]
            if user_ids:
                asset_chain_str = asset
                if is_multi_chain(asset):
                    chain_name = get_chain(chain_).display_name
                    if chain_name != asset:
                        asset_chain_str = f"{asset}-{chain_name}"

                for chunk_user_ids in batch_iter(user_ids, 5000):
                    send_resume_deposit_withdrawal.delay(chunk_user_ids, asset, type_, current_timestamp(to_int=True))
                    send_resume_deposit_withdrawal_notice_email.delay(
                        chunk_user_ids,
                        type_,
                        asset,
                        asset_chain_str,
                    )

            info_cache_class(asset, chain_).delete()
            subs_cache.delete()


@ns.route('/kyt/audit')
@respond_with_code
class KYTDepositsAuditResource(Resource):
    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "流水ID"},
        {"field": "sender_risk_id", Language.ZH_HANS_CN: "充值风险评估ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "address_result", Language.ZH_HANS_CN: "发送方地址"},
        {"field": "tx_id", Language.ZH_HANS_CN: "TXID"},
        {"field": "risk_level", Language.ZH_HANS_CN: "风险等级"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "amount", Language.ZH_HANS_CN: "数量"},
        {"field": "created_at", Language.ZH_HANS_CN: "评估时间"},
        {"field": "info_updated_at", Language.ZH_HANS_CN: "提交资料时间"},
        {"field": "risk_result", Language.ZH_HANS_CN: "分数及风险等级"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "operator", Language.ZH_HANS_CN: "操作人"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        keyword=fields.String,
        assess_object=fields.String,
        chain=ChainField(),
        tx_id=fields.String,
        status=EnumField(KYTDepositAudit.Status),
        risk_level=EnumField(KYTDepositAudit.RiskLevel),
        start_time=TimestampField,
        end_time=TimestampField,
        updated_start_time=TimestampField,
        updated_end_time=TimestampField,
        export=fields.Boolean,
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """钱包管理-充值管理-充值风险评估审核列表"""
        query = KYTDepositAudit.query.join(
            Deposit, KYTDepositAudit.deposit_id == Deposit.id
        ).with_entities(KYTDepositAudit, Deposit)
        user_ids = []
        if keyword := kwargs.get('keyword', '').strip():
            keyword_results = User.search_for_users(keyword)
            user_ids.extend(keyword_results)
            query = query.filter(KYTDepositAudit.user_id.in_(keyword_results))
        if assess_object := kwargs.get('assess_object'):
            query = query.filter(
                or_(
                    KYTDepositAudit.address_from == assess_object,
                    Deposit.address == assess_object,
                    Deposit.tx_id == assess_object,
                )
            )
        if chain := kwargs.get('chain'):
            query = query.filter(Deposit.chain == chain)
        if tx_id := kwargs.get('tx_id'):
            query = query.filter(Deposit.tx_id == tx_id)
        if risk_level := kwargs.get('risk_level'):
            query = query.filter(KYTDepositAudit.risk_level == risk_level)
        if status := kwargs.get('status'):
            query = query.filter(KYTDepositAudit.status == status)
        if start_time := kwargs.get('start_time'):
            query = query.filter(KYTDepositAudit.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(KYTDepositAudit.created_at < end_time)
        if updated_start_time := kwargs.get('updated_start_time'):
            query = query.filter(KYTDepositAudit.info_updated_at >= updated_start_time)
        if updated_end_time := kwargs.get('updated_end_time'):
            query = query.filter(KYTDepositAudit.info_updated_at < updated_end_time)
        query = query.order_by(KYTDepositAudit.id.desc())
        if kwargs.get('export'):
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            records = query.paginate(kwargs['page'], kwargs['limit'])
            items = records.items
        for (audit, _) in items:
            user_ids.append(audit.user_id)
            if audit.operator:
                user_ids.append(audit.operator)
        name_map = get_admin_user_name_map(user_ids)
        addrs, txs = [], []
        for (audit, dep) in items:
            addrs.append((dep.chain, audit.address_from))
            txs.append((dep.chain, dep.tx_id))
        wallet_client = WalletClient()
        addrs_url = wallet_client.get_explorer_addresses_url(addrs)
        txs_url = wallet_client.get_explorer_txs_url(txs)
        result = []
        for i, (audit, dep) in enumerate(items):
            deposit = dep
            amount_usd = amount_to_str(PriceManager.asset_to_usd(deposit.asset) * deposit.amount, 2)
            result.append(dict(
                id=audit.id,
                sender_risk_id=audit.sender_risk_id,
                user_id=audit.user_id,
                user_email=name_map.get(audit.user_id, '-'),
                asset=deposit.asset,
                chain=deposit.chain,
                address_result=audit.address_result,
                address_from=audit.address_from,
                assess_object=audit.assess_object,
                remark=audit.remark,
                amount=deposit.amount,
                amount_usd=amount_usd,
                tx_id=deposit.tx_id,
                created_at=audit.created_at,
                risk_result=audit.risk_result,
                risk_level=audit.risk_level.name,
                status=audit.status.name,
                info_updated_at=audit.info_updated_at,
                operator=audit.operator,
                operator_email=name_map.get(audit.operator, '-'),
                address_url=addrs_url[i],
                tx_url=txs_url[i],
            ))
        if kwargs.get('export'):
            data = []
            for res in result:
                data.append({
                    'id': res['id'],
                    'sender_risk_id': res['sender_risk_id'],
                    'user_id': res['user_id'],
                    'chain': res['chain'],
                    'address_result': res['address_result'],
                    'tx_id': res['tx_id'],
                    'asset': res['asset'],
                    'amount': res['amount'],
                    'created_at': datetime_to_utc8_str(res['created_at']),
                    'info_updated_at': datetime_to_utc8_str(res['info_updated_at']) if res['info_updated_at'] else None,
                    'risk_result': res['risk_result'],
                    'risk_level': res['risk_level'],
                    'status': res['status'],
                    'remark': res['remark'],
                    'operator': res['operator_email'] or res['operator'],
                })
            return export_xlsx(
                filename="kyt_deposit_audits",
                data_list=data,
                export_headers=cls.export_headers,
            )

        return dict(
            total=query.count(),
            aq_total=KYTDepositAudit.query.filter(
                KYTDepositAudit.status == KYTDepositAudit.Status.AUDIT_REQUIRED
            ).count(),
            items=result,
            extra=dict(
                chains=list_all_chains(),
                statuses=KYTDepositAudit.Status,
                risk_levels=KYTDepositAudit.RiskLevel,
            ))


@ns.route('/kyt/audit/<int:id_>')
@respond_with_code
class KYTDetailResource(Resource):
    CHANGEABLE_STATUS_MAP = {
        KYTDepositAudit.Status.AUDIT_REQUIRED: [
            KYTDepositAudit.Status.EXTRA_INFO_REQUIRED,
            KYTDepositAudit.Status.FREEZING,
            KYTDepositAudit.Status.WITHDRAWAL_ONLY,
            KYTDepositAudit.Status.AUDITED,
        ],
        KYTDepositAudit.Status.FREEZING: [
            KYTDepositAudit.Status.WITHDRAWAL_ONLY,
        ],

        # 以下两种状态流转属于特殊情况：认为操作人已知转账情况，可以直接入账
        KYTDepositAudit.Status.INFO_REQUIRED: [
            KYTDepositAudit.Status.AUDITED,
        ],
        KYTDepositAudit.Status.EXTRA_INFO_REQUIRED: [
            KYTDepositAudit.Status.AUDITED,
        ],
    }
    REASON_STATUSES = [
        KYTDepositAudit.Status.EXTRA_INFO_REQUIRED,
        KYTDepositAudit.Status.FREEZING,
    ]

    @classmethod
    def get(cls, id_):
        """钱包管理-充值管理-充值风险评估审核详情"""
        row: KYTDepositAudit = KYTDepositAudit.query.get(id_)
        if row is None:
            raise RecordNotFound
        deposit = Deposit.query.get(row.deposit_id)
        user = User.query.get(row.user_id)
        reject_reasons = {item.name: gettext(item.value) for item in KycVerification.RejectionReason}
        user_ids = [row.user_id]
        if row.operator:
            user_ids.append(row.operator)
        name_map = get_admin_user_name_map(user_ids)
        wallet_client = WalletClient()
        amount_usd = amount_to_str(PriceManager.asset_to_usd(deposit.asset) * deposit.amount, 2)
        detail = dict(
            id=row.id,
            user_id=row.user_id,
            user_email=name_map.get(row.user_id, row.user_id),
            address_from=row.address_from,
            assess_object=row.assess_object,
            address_result=row.address_result,
            created_at=row.created_at,
            operated_at=row.operated_at,
            chain=deposit.chain,
            status=row.status.name,
            asset=deposit.asset,
            risk_result=row.risk_result,
            operator=row.operator,
            operator_email=name_map.get(row.operator, row.operator),
            reject_reason=row.rejection_reason,
            is_custom_reason=row.is_custom_reason,
            custom_reject_reason=row.custom_rejection_reason,
            remark=row.remark,
            upload_url=cls._get_kyt_link(row.user_id),

            kyc_status=user.kyc_status.name,
            tx_id=deposit.tx_id,
            amount=deposit.amount,
            amount_usd=amount_usd,

            address_url=wallet_client.get_explorer_addresses_url([(deposit.chain, row.address_from)])[0],
            tx_url=wallet_client.get_explorer_txs_url([(deposit.chain, deposit.tx_id)])[0],

            file_info=cls._fetch_file_info(row.id),
            extra=dict(
                reject_reasons=reject_reasons,
                statuses=KYTDepositAudit.Status,
            ),
        )
        return detail

    @classmethod
    def _fetch_file_info(cls, kyt_id: int) -> dict:
        model = KYTDepositAuditDetail
        row = model.query.filter(
            model.kyt_id == kyt_id,
        ).first()
        ret = {}
        if row:
            source_proof_file_ids = row.get_source_proof_file_ids()
            address_file_ids = row.get_address_file_ids()
            edd_file_ids = row.get_edd_file_ids()
            supplement_file_ids = row.get_supplement_file_ids()
            file_ids = {
                *source_proof_file_ids,
                *address_file_ids,
                *edd_file_ids,
            }
            if supplement_file_ids:
                file_ids.update(set(supplement_file_ids))
            files = cls._fetch_files(file_ids)
            ret.update(supplement_desc=row.supplement_desc or '-')

            def get_file_urls(ids):
                ids = ids or []
                urls = []
                for id_ in ids:
                    file = files.get(id_)
                    if file:
                        urls.append(file.private_url)
                return urls

            ret.update(source_proof_file_urls=get_file_urls(source_proof_file_ids))
            ret.update(address_file_urls=get_file_urls(address_file_ids))
            ret.update(edd_file_urls=get_file_urls(edd_file_ids))
            ret.update(supplement_file_urls=get_file_urls(supplement_file_ids))
        return ret

    @classmethod
    def _fetch_files(cls, file_ids):
        if not file_ids:
            return {}
        files = File.query.filter(File.id.in_(file_ids)).all()
        return {file.id: file for file in files}

    @classmethod
    def _get_kyt_link(cls, user_id):
        model = EmailToken
        row = model.query.filter(
            model.user_id == user_id,
            model.email_type == model.EmailType.KYT_DEPOSIT_AUDIT
        ).order_by(
            model.id.desc()
        ).first()
        site_url = config['SITE_URL']
        kyt_page = 'kyt/material'
        token = row.token
        return url_join(site_url, kyt_page, token=token)

    @classmethod
    @ns.use_kwargs(dict(
        reject_reason=EnumField(KycVerification.RejectionReason),
        is_custom_reason=fields.Boolean(missing=False),
        custom_reject_reason=fields.String,
        status=EnumField(KYTDepositAudit.Status, required=True),
    ))
    def put(cls, id_, **kwargs):
        """钱包管理-充值管理-充值风险评估审核流转"""
        row: KYTDepositAudit = KYTDepositAudit.query.get(id_)
        cls._validate(row, kwargs)
        with CacheLock(LockKeys.kyt_deposit_audit(row.id)):
            db.session.rollback()
            row = KYTDepositAudit.query.get(id_)
            if row.status not in cls.CHANGEABLE_STATUS_MAP:
                raise InvalidArgument(message=f'current status {row.status.name} cannot be changed')
            changeable_statuses = cls.CHANGEABLE_STATUS_MAP[row.status]
            if kwargs['status'] not in changeable_statuses:
                raise InvalidArgument(
                    message=f'current status {row.status.name} cannot be changed to {kwargs["status"].name}')
            if kwargs['status'] in cls.REASON_STATUSES:
                if kwargs['is_custom_reason']:
                    row.is_custom_reason = True
                    row.custom_rejection_reason = kwargs['custom_reject_reason']
                else:
                    row.rejection_reason = kwargs['reject_reason'].name
                    row.is_custom_reason = False
            detail = dict(
                kyt_id=row.id,
                status=f'{row.status}->{kwargs["status"]}')

            row.status = kwargs['status']
            row.operator = g.user.id
            row.operated_at = now()
            cls._try_add_cleared_user(row)
            db.session.commit()
            send_kyt_audit_email.delay(row.id)
            AdminOperationLog.new_audit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.KYT,
                detail=detail,
                target_user_id=row.user_id,
            )

    @classmethod
    def _validate(cls, row, kwargs):
        if row is None:
            raise RecordNotFound
        if row.status not in cls.CHANGEABLE_STATUS_MAP:
            raise InvalidArgument(message=f'current status {row.status.name} cannot be changed')
        changeable_statuses = cls.CHANGEABLE_STATUS_MAP[row.status]
        if kwargs['status'] not in changeable_statuses:
            raise InvalidArgument(message=f'current status {row.status.name} cannot be changed to {kwargs["status"].name}')
        if kwargs['status'] in [
            KYTDepositAudit.Status.EXTRA_INFO_REQUIRED,
            KYTDepositAudit.Status.FREEZING,
        ]:
            if kwargs['is_custom_reason']:
                if not kwargs.get('custom_reject_reason'):
                    raise InvalidArgument(message='自定义拒绝原因未填写')
            else:
                if not kwargs.get('reject_reason'):
                    raise InvalidArgument(message='拒绝原因未选择')

    @classmethod
    def _try_add_cleared_user(cls, row: KYTDepositAudit):
        if row.status is not row.Status.WITHDRAWAL_ONLY:
            return
        cleared_user = ClearedUser.get_or_create(user_id=row.user_id)
        if not cleared_user.id:
            cleared_user.status = ClearedUser.Status.WITHDRAWAL_ONLY
            cleared_user.remark = '【KYT】 WITHDRAWAL_ONLY'
            db.session.add(cleared_user)
        else:
            if not cleared_user.valid:
                cleared_user.valid = True
                cleared_user.status = ClearedUser.Status.WITHDRAWAL_ONLY
                cleared_user.remark = '【KYT】 WITHDRAWAL_ONLY'
        db.session.commit()
        UserVisitPermissionCache().add_users([row.user_id], UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE)

    @classmethod
    @ns.use_kwargs(dict(
        remark=fields.String(required=True),
    ))
    def patch(cls, id_, **kwargs):
        """钱包管理-充值管理-充值风险评估备注"""
        row: KYTDepositAudit = KYTDepositAudit.query.get(id_)
        row.remark = kwargs['remark']
        db.session.commit()

