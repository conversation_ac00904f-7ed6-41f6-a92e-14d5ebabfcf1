# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal

from flask import g, current_app

from app.business.user import UserSettings
from webargs import fields
from sqlalchemy import or_

from ....assets import (asset_to_chains, has_asset, get_asset_chain_config)
from ....assets.asset import try_get_asset_chain_config
from ....models import User, Withdrawal, db, WithdrawalApprover, <PERSON><PERSON><PERSON>W<PERSON>elistUser, RiskUser, WithdrawalAddressBlacklist, AdminUser
from ....models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectRisk
from ....exceptions import InvalidArgument, OperationDenied, AssetNotFound
from ....business import PriceManager, SiteSettings
from ....business.risk_control import withdrawals_disabled_by_risk_control
from ....business.email import send_cancelled_withdrawal_notice_email
from ....business import cancel_withdrawal
from ....business import WalletClient
from ...common import Resource, Namespace, respond_with_code
from ...common.fields import <PERSON><PERSON>Field, ChainField, EnumField, TimestampField, PositiveDecimalField, PageField, LimitField
from ....common import get_country, PrecisionEnum
from ....common.constants import Language, ADMIN_EXPORT_LIMIT
from ....models.wallet import WithdrawalCancel
from ....utils import quantize_amount, batch_iter, export_xlsx
from ....utils.date_ import datetime_to_utc8_str

ns = Namespace('Asset - Withdrawals')


class WithdrawalHistoryMixin:

    @classmethod
    def get_user_map(cls, user_ids):
        users = User.query \
            .filter(User.id.in_(user_ids)) \
            .with_entities(User.id, User.email, User.name, User.location_code) \
            .all()
        return {
            id_: dict(name=name, email=email, country=c.cn_name if (c := get_country(code)) else '')
            for id_, email, name, code in users
        }

    @classmethod
    def get_admin_name_map(cls, user_ids):
        users = AdminUser.query.filter(
            AdminUser.user_id.in_(user_ids)
        ).with_entities(
            AdminUser.user_id,
            AdminUser.name
        ).all()
        return dict(users)

    @classmethod
    def get_cancel_user_name(cls, user_id: int, cancel_type: str, admin_name_map: dict, user_map: dict):
        if user_id is None:
            return ''
        if cancel_type == WithdrawalCancel.CancelType.CUSTOMER_SERVICE.name:
            return admin_name_map.get(user_id) or user_map.get(user_id, {}).get('email', '')
        elif cancel_type == WithdrawalCancel.CancelType.USER.name:
            return user_map.get(user_id, {}).get('email', '')
        else:
            return 'system'


@ns.route('')
@respond_with_code
class WithdrawalHistoryResource(WithdrawalHistoryMixin, Resource):

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "approved_by_user_at", Language.ZH_HANS_CN: "确认时间"},
        {"field": "sent_at", Language.ZH_HANS_CN: "打出时间"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户"},
        {"field": "country", Language.ZH_HANS_CN: "国家"},
        {"field": "type", Language.ZH_HANS_CN: "类型"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "amount", Language.ZH_HANS_CN: "数量"},
        {"field": "address", Language.ZH_HANS_CN: "地址"},
        {"field": "memo", Language.ZH_HANS_CN: "Memo"},
        {"field": "tx_id", Language.ZH_HANS_CN: "交易 ID"},
        {"field": "fee", Language.ZH_HANS_CN: "手续费"},
        {"field": "confirmations", Language.ZH_HANS_CN: "确认数"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "audit_type", Language.ZH_HANS_CN: "待审核原因"},
        {"field": "cancel_type", Language.ZH_HANS_CN: "取消原因"},
    )

    audit_type_dict = {
        'NORMAL': '正常',
        'SITE_RISK_CONTROL': '全站提现受限',
        'ASSET_MANUAL_AUDIT': '提现需要人工审核（币种）',
        'ASSET_RISK_CONTROL': 'WEB风控关闭提现（币种）',
        'USER_RISK_CONTROL': '用户风控提现受限',
    }

    SCHEMA = dict(
        asset=AssetField(),
        chain=ChainField(),
        type=EnumField(Withdrawal.Type),
        status=EnumField(Withdrawal.Status),
        audit_type=EnumField(audit_type_dict),
        start=TimestampField(),
        end=TimestampField(),
        id=fields.Integer,
        user=fields.String(),
        tx_id=fields.String(),
        address=fields.String(),
        min_amount=PositiveDecimalField,
        max_amount=PositiveDecimalField,
        min_amount_usd=PositiveDecimalField,
        max_amount_usd=PositiveDecimalField,
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50),
        export=fields.Boolean(missing=False),
    )

    @classmethod
    @ns.use_kwargs(SCHEMA)
    def get(cls, **kwargs):
        """提现记录列表"""
        export = kwargs['export']

        query = cls.get_query(kwargs)

        if export:
            items = query.order_by(Withdrawal.id.desc()).limit(ADMIN_EXPORT_LIMIT).all()
            total = 0
        else:
            records = query \
                .order_by(Withdrawal.id.desc()) \
                .paginate(kwargs['page'], kwargs['limit'])
            items = records.items
            total = records.total

        cancel_withdrawal_ids = [r.id for r in items if r.status == Withdrawal.Status.CANCELLED]
        cancel_type_map = cls.get_cancel_type_map(cancel_withdrawal_ids)

        user_ids = set(d.user_id for d in items)
        cancel_user_ids = {data['cancel_user_id'] for data in cancel_type_map.values() if data['cancel_user_id']}
        user_ids |= cancel_user_ids
        user_map = cls.get_user_map(user_ids)
        admin_name_map = cls.get_admin_name_map(cancel_user_ids)

        prices = PriceManager.assets_to_usd()
        audit_type_ids_map = cls.get_audit_type_ids_map([r for r in items if r.status == Withdrawal.Status.AUDIT_REQUIRED])
        audit_type_map = {}
        for audit_type_, ids_ in audit_type_ids_map.items():
            for id_ in ids_:
                audit_type_map[id_] = audit_type_

        def get_audit_type(id_, status_):
            if status_ != Withdrawal.Status.AUDIT_REQUIRED:
                return ''
            return audit_type_map.get(id_, '')

        data = [
            dict(
                id=v.id,
                created_at=v.created_at,
                updated_at=v.updated_at,
                user_id=v.user_id,
                type=v.type.name,
                asset=v.asset,
                chain=v.chain,
                address=v.address,
                amount=v.amount,
                amount_usd=quantize_amount(prices.get(v.asset, 0) * v.amount, 2),
                fee=v.fee,
                fee_asset=v.fee_asset,
                memo=v.memo,
                attachment=v.attachment,
                recipient_user_id=v.recipient_user_id,
                approved_by_user_at=v.approved_by_user_at,
                sent_at=v.sent_at,
                tx_id=v.tx_id,
                confirmations=v.confirmations,
                status=v.status.name,
                audit_type=get_audit_type(v.id, v.status),
                cancel_type=cancel_type_map.get(v.id, {}).get('cancel_type'),
                cancel_user_id=cancel_type_map.get(v.id, {}).get('cancel_user_id'),
                cancel_user_name=cls.get_cancel_user_name(
                    cancel_type_map.get(v.id, {}).get('cancel_user_id'),
                    cancel_type_map.get(v.id, {}).get('cancel_type'),
                    admin_name_map,
                    user_map
                ),
            )
            for index, v in enumerate(items)
        ]

        if export:
            for item in data:
                item['created_at'] = datetime_to_utc8_str(item['created_at'])
                item['approved_by_user_at'] = datetime_to_utc8_str(item['approved_by_user_at']) \
                    if item['approved_by_user_at'] else ''
                item['sent_at'] = datetime_to_utc8_str(item['sent_at']) \
                    if item['sent_at'] else ''
                item['type'] = Withdrawal.Type[item['type']].value
                item['status'] = Withdrawal.Status[item['status']].value
                item['audit_type'] = cls.audit_type_dict[item['audit_type']] if item['audit_type'] else ''
                item['country'] = user_map.get(item['user_id'], {'country': ''})['country']
                item['cancel_type'] = WithdrawalCancel.CancelType[item['cancel_type']].value \
                                      + item.get('cancel_user_name', '') if item['cancel_type'] else ''
            return export_xlsx(
                filename='asset_withdrawals',
                data_list=data,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=data,
            extra=dict(
                assets=asset_to_chains(),
                types=Withdrawal.Type,
                statuses=Withdrawal.Status,
                audit_types=cls.audit_type_dict,
                users=user_map,
                cancel_types=WithdrawalCancel.CancelType,
            )
        )

    @classmethod
    def get_query(cls, kwargs):
        chain = kwargs.get('chain')
        asset = kwargs.get('asset')

        if asset and not has_asset(asset, chain):
            raise AssetNotFound(asset, chain)
        if kwargs.get('min_amount_usd') or kwargs.get('max_amount_usd'):
            if not kwargs.get('asset'):
                raise InvalidArgument(message='请选择币种，再选择市值查询！')
        if kwargs.get('min_amount') or kwargs.get('max_amount'):
            if not kwargs.get('asset'):
                raise InvalidArgument(message='请选择币种，再选择数量查询！')
        query = Withdrawal.query
        if chain:
            query = query.filter(Withdrawal.chain == chain)
        if asset:
            query = query.filter(Withdrawal.asset == asset)
        if (type_ := kwargs.get('type')) is not None:
            query = query.filter(Withdrawal.type == type_)
        if start := kwargs.get('start'):
            query = query.filter(Withdrawal.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(Withdrawal.created_at < end)
        if _id := kwargs.get('id'):
            query = query.filter(Withdrawal.id == _id)
        if user := kwargs.get('user', '').strip():
            query = query.filter(
                Withdrawal.user_id.in_(User.search_for_users(user)))
        if tx_id := kwargs.get('tx_id'):
            query = query.filter(Withdrawal.tx_id == tx_id)
        if address := kwargs.get('address'):
            query = query.filter(or_(Withdrawal.address == address,
                                     Withdrawal.memo == address))
        if min_amount := kwargs.get('min_amount'):
            query = query.filter(Withdrawal.amount >= min_amount)
        if max_amount := kwargs.get('max_amount'):
            query = query.filter(Withdrawal.amount <= max_amount)
        asset_rate = PriceManager.asset_to_usd(asset) or 1
        if min_amount_usd := kwargs.get('min_amount_usd'):
            convert_amount = Decimal('1') / Decimal(asset_rate) * min_amount_usd
            convert_amount = quantize_amount(convert_amount, PrecisionEnum.COIN_PLACES)
            if convert_amount != Decimal():
                query = query.filter(Withdrawal.amount >= convert_amount)
        if max_amount_usd := kwargs.get('max_amount_usd'):
            convert_amount = Decimal('1') / Decimal(asset_rate) * max_amount_usd
            convert_amount = quantize_amount(convert_amount, PrecisionEnum.COIN_PLACES)
            if convert_amount != Decimal():
                query = query.filter(Withdrawal.amount <= convert_amount)
        status = kwargs.get('status')  # 需要放最后
        if status:
            query = query.filter(Withdrawal.status == status)
            audit_type = kwargs.get('audit_type')
            if status == Withdrawal.Status.AUDIT_REQUIRED and audit_type:
                _q_rows = query.filter(
                    Withdrawal.status == Withdrawal.Status.AUDIT_REQUIRED,
                ).with_entities(
                    Withdrawal.id,
                    Withdrawal.user_id,
                    Withdrawal.asset,
                    Withdrawal.chain,
                    Withdrawal.type
                ).all()
                audit_type_ids_map = cls.get_audit_type_ids_map(_q_rows)
                ids = audit_type_ids_map[audit_type]
                query = query.filter(Withdrawal.id.in_(ids))

            """
            线上数据分布情况，优化查询
            (339, <Status.CREATED: '待用户确认'>)
            (5, <Status.AUDIT_REQUIRED: '待审核'>)
            (9, <Status.AUDITED: '已审核'>)
            (57, <Status.PROCESSING: '处理中'>)
            (108, <Status.CONFIRMING: '确认中'>)
            (19491055, <Status.FINISHED: '已完成'>)
            (1010512, <Status.CANCELLED: '已取消'>)
            (34, <Status.FAILED: '失败'>)
            """
            if status in (
                    Withdrawal.Status.CREATED,
                    Withdrawal.Status.AUDIT_REQUIRED,
                    Withdrawal.Status.AUDITED,
                    Withdrawal.Status.PROCESSING,
                    Withdrawal.Status.CONFIRMING,
                    Withdrawal.Status.FAILED,
            ):
                ids = {v.id for v in query.with_entities(Withdrawal.id).all()}
                query = query.filter(Withdrawal.id.in_(ids))

        return query

    @classmethod
    def get_audit_type_ids_map(cls, rows: list) -> defaultdict:
        audit_type_map = defaultdict(set)
        site_disabled = not SiteSettings.withdrawals_enabled or SiteSettings.withdrawals_disabled_by_risk_control
        site_local_disabled = not SiteSettings.local_transfers_enabled
        block_user_ids = set()
        has_load_block = False
        white_list_user_ids = set()
        has_load_white_list = False
        asset_chain_config_map = {}

        def _get_asset_chain_config(asset_, chain_):
            conf_ = asset_chain_config_map.get((asset_, chain_))
            if not conf_:
                conf_ = try_get_asset_chain_config(asset_, chain_)
            return conf_

        for row in rows:
            if row.type == Withdrawal.Type.ON_CHAIN:
                if site_disabled:
                    audit_type_map['SITE_RISK_CONTROL'].add(row.id)
                    continue
                asset_chain_config = _get_asset_chain_config(row.asset, row.chain)
                if asset_chain_config and asset_chain_config.withdrawals_disabled_by_risk_control:
                    audit_type_map['ASSET_RISK_CONTROL'].add(row.id)
                    continue
            else:
                if site_local_disabled:
                    audit_type_map['SITE_RISK_CONTROL'].add(row.id)
                    continue
            if not UserSettings(row.user_id).withdrawals_enabled:
                audit_type_map['USER_RISK_CONTROL'].add(row.id)
                continue

            if not has_load_white_list:
                white_list_users = WithdrawalWhitelistUser.query.filter(
                    WithdrawalWhitelistUser.status == WithdrawalWhitelistUser.Status.VALID
                ).with_entities(WithdrawalWhitelistUser.user_id).all()
                white_list_user_ids = {r.user_id for r in white_list_users}
                has_load_white_list = True
            if row.user_id not in white_list_user_ids:
                if row.type == Withdrawal.Type.ON_CHAIN:
                    asset_chain_config = _get_asset_chain_config(row.asset, row.chain)
                    if (asset_chain_config and asset_chain_config.withdrawals_require_manual_audit
                            and row.amount >= asset_chain_config.withdrawals_manual_audit_threshold):
                        audit_type_map['ASSET_MANUAL_AUDIT'].add(row.id)
                        continue

                if not has_load_block:
                    user_ids = {i.user_id for i in rows}
                    user_ids = set(list(user_ids)[:1000])  # 限制下条数
                    block_user_ids = cls.get_withdrawals_disabled_user_ids_by_risk_control(user_ids)
                    has_load_block = True
                if row.user_id in block_user_ids:
                    audit_type_map['USER_RISK_CONTROL'].add(row.id)
                    continue
            audit_type_map['NORMAL'].add(row.id)

        return audit_type_map

    @classmethod
    def get_withdrawals_disabled_user_ids_by_risk_control(cls, user_ids: set[int]) -> set:
        """ 找出被风控卡住待审核的提现用户 """
        # see app.business.risk_control.base.withdrawals_disabled_by_risk_control
        block_user_ids = RiskUser.batch_test(user_ids, RiskUser.Permission.BALANCE_OUT_DISABLED)
        # UserCheckRequest 不判断
        return block_user_ids

    @classmethod
    def get_cancel_type_map(cls, cancel_withdrawal_ids: list):
        cancels = WithdrawalCancel.query.filter(
            WithdrawalCancel.withdrawal_id.in_(cancel_withdrawal_ids)
        ).all()
        return {c.withdrawal_id: dict(
            cancel_type=c.cancel_type.name,
            cancel_user_id=c.cancel_user_id,
        ) for c in cancels}


# noinspection PyUnresolvedReferences
@ns.route('/<int:id_>')
@respond_with_code
class WithdrawalHistoryItemResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(Withdrawal.Status, required=True),
        tx_id=fields.String(),
        tx_fee=fields.String(),
    ))
    def patch(cls, id_, **kwargs):
        """提现记录编辑"""
        row: Withdrawal = Withdrawal.query.filter(Withdrawal.id == id_).first()
        if row is None:
            raise InvalidArgument(f'record {id_!r} does not exist')

        status = kwargs['status']
        current_app.logger.warning(f'withdrawal_operate userid:{g.user.id} change withdrawal_id:{id_} to {status}')

        if status is Withdrawal.Status.CANCELLED:
            if row.status not in (Withdrawal.Status.CREATED,
                                  Withdrawal.Status.AUDIT_REQUIRED,
                                  Withdrawal.Status.AUDITED):
                raise OperationDenied
            if cancel_withdrawal(id_, cancel_type=WithdrawalCancel.CancelType.CUSTOMER_SERVICE, cancel_user_id=g.user.id):
                # 成功取消了(加了余额)才发
                send_cancelled_withdrawal_notice_email.delay(id_)
        elif status is Withdrawal.Status.AUDITED:
            if row.status != Withdrawal.Status.AUDIT_REQUIRED:
                raise OperationDenied
            # 被禁止提现/风控系统禁止提现时，不能审核通过
            if (not UserSettings(row.user_id).withdrawals_enabled
                    or withdrawals_disabled_by_risk_control(row.user_id)):
                raise OperationDenied(message='withdrawals of the user is disabled')
            if SiteSettings.withdrawals_disabled_by_risk_control:
                raise OperationDenied(message='全站触发资产负债不平风控，无法提现')
            if get_asset_chain_config(row.asset, row.chain).withdrawals_disabled_by_risk_control:
                raise OperationDenied(message='该币种触发资产负债不平风控，无法提现')
            row.status = status
            db.session.commit()
        else:
            raise OperationDenied

        return row


# noinspection PyUnresolvedReferences
@ns.route('/explorer-addresses-url')
@respond_with_code
class ExplorerAddressUrlResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        addresses=fields.Raw(required=True),
    ))
    def post(cls, **kwargs):
        """批量获取浏览器地址"""
        addresses = kwargs["addresses"]
        if not isinstance(addresses, list):
            raise InvalidArgument
        wallet_client = WalletClient()
        return wallet_client.get_explorer_addresses_url(addresses)


@ns.route('/explorer-txs-url')
@respond_with_code
class ExplorerTxsUrlResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        txs=fields.Raw(required=True),
    ))
    def post(cls, **kwargs):
        """批量获取交易id浏览器地址"""
        txs = kwargs["txs"]
        if not isinstance(txs, list):
            raise InvalidArgument
        wallet_client = WalletClient()
        return wallet_client.get_explorer_txs_url(txs)


@ns.route("/approvers")
@respond_with_code
class WithdrawalApproversResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(),
            account=fields.String(),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """提现多人审核列表"""
        query = WithdrawalApprover.query.filter(
            WithdrawalApprover.is_self.is_(False),
            WithdrawalApprover.status.in_([WithdrawalApprover.Status.VALID, WithdrawalApprover.Status.DELETING]),
        ).order_by(WithdrawalApprover.id.desc())
        if user_id := kwargs.get('user_id'):
            query = query.filter(WithdrawalApprover.user_id == user_id)
        if account := kwargs.get('account'):
            query = query.filter(WithdrawalApprover.account == account)

        pagination = query.paginate(kwargs['page'], kwargs['limit'])
        rows = pagination.items

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 2000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        items = []
        for r in rows:
            item = r.to_dict()
            item["email"] = user_email_map.get(r.user_id, "")
            items.append(item)

        return dict(
            items=items,
            total=pagination.total,
        )


@ns.route("/address-blacklist")
@respond_with_code
class WithdrawalAddressBlacklistResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            chain=fields.String,
            address=fields.String,
            memo=fields.String,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 风控-风控参数配置-提现地址黑名单-列表 """
        model = WithdrawalAddressBlacklist
        query = model.query.filter(model.status == model.Status.VALID)
        if chain := kwargs.get("chain"):
            query = query.filter(model.chain == chain)
        if address := kwargs.get("address"):
            query = query.filter(model.address == address)
        if memo := kwargs.get("memo"):
            query = query.filter(model.memo == memo)

        pagination = query.order_by(model.id.desc()).paginate(kwargs["page"], kwargs["limit"], error_out=False)
        res_items = []
        for row in pagination.items:
            item = row.to_dict(enum_to_name=True)
            res_items.append(item)

        asset_chains_dict = asset_to_chains()
        chain_set = set()
        for asset, chains in asset_chains_dict.items():
            for c in chains:
                chain_set.add(c)

        return dict(
            items=res_items,
            total=pagination.total,
            extra=dict(
                chain_list=chain_set,
            ),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            chain=fields.String(required=True),
            address=fields.String(required=True),
            memo=fields.String(missing=""),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 风控-风控参数配置-提现地址黑名单-新增 """
        row = WithdrawalAddressBlacklist(
            chain=kwargs['chain'],
            address=kwargs['address'],
            memo=kwargs.get('memo') or '',
            remark=kwargs.get('remark') or '',
        )
        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.WithdrawalAddressBlocklist,
            detail=row.to_dict(enum_to_name=True),
        )

        return row.to_dict(enum_to_name=True)

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            remark=fields.String(missing=""),
        )
    )
    def patch(cls, **kwargs):
        """ 风控-风控参数配置-提现地址黑名单-编辑备注 """
        model = WithdrawalAddressBlacklist
        row: model = model.query.get(kwargs['id'])
        old_data = row.to_dict(enum_to_name=True)
        if (remark := kwargs.get("remark")) is not None:
            row.remark = remark

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.WithdrawalAddressBlocklist,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )
        db.session.commit()
        return row.to_dict(enum_to_name=True)

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 风控-风控参数配置-提现地址黑名单-删除 """
        model = WithdrawalAddressBlacklist
        row: model = model.query.get(kwargs['id'])
        row.status = model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.WithdrawalAddressBlocklist,
            detail=row.to_dict(enum_to_name=True),
        )
        return {}


@ns.route("/cancels")
@respond_with_code
class WithdrawalCancelsResource(WithdrawalHistoryMixin, Resource):

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间"},
        {"field": "approved_by_user_at", Language.ZH_HANS_CN: "确认时间"},
        {"field": "sent_at", Language.ZH_HANS_CN: "打出时间"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户"},
        {"field": "country", Language.ZH_HANS_CN: "国家"},
        {"field": "type", Language.ZH_HANS_CN: "类型"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "chain", Language.ZH_HANS_CN: "链"},
        {"field": "amount", Language.ZH_HANS_CN: "数量"},
        {"field": "address", Language.ZH_HANS_CN: "地址"},
        {"field": "memo", Language.ZH_HANS_CN: "Memo"},
        {"field": "cancel_time", Language.ZH_HANS_CN: "取消时间"},
        {"field": "cancel_type", Language.ZH_HANS_CN: "取消原因"},
        {"field": "is_risk_control", Language.ZH_HANS_CN: "是否被风控"},
        {"field": "cancel_user_name", Language.ZH_HANS_CN: "操作人"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(),
            asset=AssetField(),
            chain=ChainField(),
            type=EnumField(Withdrawal.Type),
            user=fields.String(),
            cancel_type=EnumField(WithdrawalCancel.CancelType),
            cancel_user=fields.String(),
            address=fields.String(),
            start=TimestampField(),
            end=TimestampField(),
            page=fields.Integer(missing=1),
            limit=fields.Integer(missing=50),
            export=fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """钱包-WEB钱包管理-提现取消记录列表"""
        export = kwargs['export']
        query = cls.get_query(kwargs)
        if export:
            items = query.order_by(Withdrawal.id.desc()).limit(ADMIN_EXPORT_LIMIT).all()
            total = 0
        else:
            records = query.order_by(Withdrawal.id.desc()).paginate(kwargs['page'], kwargs['limit'])
            items = records.items
            total = records.total

        user_ids = set(d.user_id for d in items)
        cancel_user_ids = {i.cancel_user_id for i in items if i.cancel_user_id}
        user_ids |= cancel_user_ids
        user_map = cls.get_user_map(user_ids)
        admin_name_map = cls.get_admin_name_map(cancel_user_ids)
        prices = PriceManager.assets_to_usd()

        data = []
        for index, v in enumerate(items):
            cancel_type = v.cancel_type.name if v.cancel_type else None
            data.append(dict(
                id=v.id,
                created_at=v.created_at,
                updated_at=v.updated_at,
                user_id=v.user_id,
                type=v.type.name,
                asset=v.asset,
                chain=v.chain,
                address=v.address,
                amount=v.amount,
                amount_usd=quantize_amount(prices.get(v.asset, 0) * v.amount, 2),
                memo=v.memo,
                recipient_user_id=v.recipient_user_id,
                approved_by_user_at=v.approved_by_user_at,
                sent_at=v.sent_at,
                tx_id=v.tx_id,
                cancel_time=v.cancel_time,
                cancel_type=cancel_type,
                cancel_user_id=v.cancel_user_id,
                cancel_user_name=cls.get_cancel_user_name(v.cancel_user_id, cancel_type, admin_name_map, user_map),
                is_risk_control=v.is_risk_control
            ))

        if export:
            for item in data:
                item['created_at'] = datetime_to_utc8_str(item['created_at'])
                item['approved_by_user_at'] = datetime_to_utc8_str(item['approved_by_user_at']) \
                    if item['approved_by_user_at'] else ''
                item['sent_at'] = datetime_to_utc8_str(item['sent_at']) \
                    if item['sent_at'] else ''
                item['type'] = Withdrawal.Type[item['type']].value
                item['cancel_time'] = datetime_to_utc8_str(item['cancel_time']) \
                    if item['cancel_time'] else ''
                item['cancel_type'] = WithdrawalCancel.CancelType[item['cancel_type']].value \
                    if item['cancel_type'] else ''
                item['country'] = user_map.get(item['user_id'], {'country': ''})['country']
                item['is_risk_control'] = '是' if item['is_risk_control'] else '否'
            return export_xlsx(
                filename='asset_withdrawal_cancels',
                data_list=data,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=data,
            extra=dict(
                assets=asset_to_chains(),
                types=Withdrawal.Type,
                cancel_types=WithdrawalCancel.CancelType,
                users=user_map,
            )
        )

    @classmethod
    def get_query(cls, kwargs):
        chain = kwargs.get('chain')
        asset = kwargs.get('asset')

        if asset and not has_asset(asset, chain):
            raise AssetNotFound(asset, chain)
        query = Withdrawal.query.join(
            WithdrawalCancel, Withdrawal.id == WithdrawalCancel.withdrawal_id,
            isouter=True
        ).with_entities(
            Withdrawal.id,
            Withdrawal.created_at,
            Withdrawal.updated_at,
            Withdrawal.user_id,
            Withdrawal.type,
            Withdrawal.asset,
            Withdrawal.chain,
            Withdrawal.address,
            Withdrawal.amount,
            Withdrawal.memo,
            Withdrawal.recipient_user_id,
            Withdrawal.approved_by_user_at,
            Withdrawal.sent_at,
            Withdrawal.tx_id,
            WithdrawalCancel.created_at.label("cancel_time"),
            WithdrawalCancel.cancel_type,
            WithdrawalCancel.cancel_user_id,
            WithdrawalCancel.is_risk_control,
        ).filter(
            Withdrawal.status == Withdrawal.Status.CANCELLED
        )
        if chain:
            query = query.filter(Withdrawal.chain == chain)
        if asset:
            query = query.filter(Withdrawal.asset == asset)
        if (type_ := kwargs.get('type')) is not None:
            query = query.filter(Withdrawal.type == type_)
        if cancel_type := kwargs.get('cancel_type'):
            query = query.filter(WithdrawalCancel.cancel_type == cancel_type)
        if cancel_user := kwargs.get('cancel_user', '').strip():
            query = query.filter(
                WithdrawalCancel.cancel_user_id.in_(User.search_for_users(cancel_user)))
        if start := kwargs.get('start'):
            query = query.filter(Withdrawal.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(Withdrawal.created_at < end)
        if _id := kwargs.get('id'):
            query = query.filter(Withdrawal.id == _id)
        if user := kwargs.get('user', '').strip():
            query = query.filter(
                Withdrawal.user_id.in_(User.search_for_users(user)))
        if address := kwargs.get('address'):
            query = query.filter(or_(Withdrawal.address == address,
                                     Withdrawal.memo == address))

        return query
