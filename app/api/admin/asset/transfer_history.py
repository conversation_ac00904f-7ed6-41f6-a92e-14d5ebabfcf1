# -*- coding: utf-8 -*-

from webargs import fields
from ....assets import list_all_assets
from ....caches import MarginAccountIdCache
from ....models import User, MarginTransferHistory, \
    InvestmentBalanceHistory, PerpetualBalanceTransfer, \
    SubAccountAssetTransfer
from ...common import Resource, Namespace, respond_with_code
from ...common.fields import AssetField, EnumField, TimestampField

ns = Namespace('Asset - Transfer History')


@ns.route('/margin')
@respond_with_code
class TransferHistoryMarginResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField,
        status=EnumField(MarginTransferHistory.StatusType),
        transfer_type=EnumField(MarginTransferHistory.TransferType),
        start=TimestampField,
        end=TimestampField,
        user=fields.String(),
        source_id=fields.String(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """钱包-划转记录-杠杆账户"""
        query = MarginTransferHistory.query
        if user := kwargs.get('user', '').strip():
            query = query.filter(
                MarginTransferHistory.user_id.in_(User.search_for_users(user)))
        if asset := kwargs.get('asset'):
            query = query.filter(MarginTransferHistory.asset == asset)
        if (status := kwargs.get('status')) is not None:
            query = query.filter(MarginTransferHistory.status == status)
        if (transfer_type := kwargs.get('transfer_type')) is not None:
            query = query.filter(MarginTransferHistory.transfer_type == transfer_type)
        if start := kwargs.get('start'):
            query = query.filter(MarginTransferHistory.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(MarginTransferHistory.created_at < end)
        if source_id := kwargs.get('source_id'):
            query = query.filter(MarginTransferHistory.id == source_id)
        records = query \
            .order_by(MarginTransferHistory.id.desc())\
            .infinite_paginate(kwargs['page'], kwargs['limit'])

        items = records.items
        users = User.query \
            .filter(User.id.in_(set(d.user_id for d in items))) \
            .with_entities(User.id, User.email, User.name) \
            .all()

        return dict(
            has_next=records.has_next,
            items=records.items,
            extra=dict(
                statuses=MarginTransferHistory.StatusType,
                transfer_types=MarginTransferHistory.TransferType,
                assets=list_all_assets(),
                markets=MarginAccountIdCache.list_all_markets(),
                users={id_: dict(name=name, email=email)
                       for id_, email, name in users}
            )
        )


@ns.route('/perpetual')
@respond_with_code
class TransferHistoryPerpetualResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField,
        status=EnumField(PerpetualBalanceTransfer.Status),
        transfer_type=EnumField(PerpetualBalanceTransfer.TransferType),
        start=TimestampField,
        end=TimestampField,
        user=fields.String(),
        source_id=fields.String(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """钱包-划转记录-合约账户"""
        query = PerpetualBalanceTransfer.query
        if user := kwargs.get('user', '').strip():
            query = query.filter(
                PerpetualBalanceTransfer.user_id.in_(User.search_for_users(user)))
        if asset := kwargs.get('asset'):
            query = query.filter(PerpetualBalanceTransfer.coin_type == asset)
        if (status := kwargs.get('status')) is not None:
            query = query.filter(PerpetualBalanceTransfer.status == status)
        if (transfer_type := kwargs.get('transfer_type')) is not None:
            query = query.filter(PerpetualBalanceTransfer.transfer_type == transfer_type)
        if start := kwargs.get('start'):
            query = query.filter(PerpetualBalanceTransfer.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(PerpetualBalanceTransfer.created_at < end)
        if source_id := kwargs.get('source_id'):
            query = query.filter(PerpetualBalanceTransfer.id == source_id)
        records = query \
            .order_by(PerpetualBalanceTransfer.id.desc())\
            .paginate(kwargs['page'], kwargs['limit'])

        items = records.items
        users = User.query \
            .filter(User.id.in_(set(d.user_id for d in items))) \
            .with_entities(User.id, User.email, User.name) \
            .all()

        return dict(
            total=records.total,
            items=records.items,
            extra=dict(
                statuses=PerpetualBalanceTransfer.Status,
                transfer_types=PerpetualBalanceTransfer.TransferType,
                assets=list_all_assets(),
                users={id_: dict(name=name, email=email)
                       for id_, email, name in users}
            )
        )


@ns.route('/investment')
@respond_with_code
class TransferHistoryInvestmentResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField,
        status=EnumField(InvestmentBalanceHistory.StatusType),
        transfer_type=EnumField(InvestmentBalanceHistory.OptType),
        start=TimestampField,
        end=TimestampField,
        user=fields.String(),
        source_id=fields.String(),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """钱包-划转记录-理财账户"""
        query = InvestmentBalanceHistory.query
        if user := kwargs.get('user', '').strip():
            query = query.filter(
                InvestmentBalanceHistory.user_id.in_(User.search_for_users(user)))
        if asset := kwargs.get('asset'):
            query = query.filter(InvestmentBalanceHistory.asset == asset)
        if (status := kwargs.get('status')) is not None:
            query = query.filter(InvestmentBalanceHistory.status == status)
        if (transfer_type := kwargs.get('transfer_type')) is not None:
            query = query.filter(InvestmentBalanceHistory.opt_type == transfer_type)
        if start := kwargs.get('start'):
            query = query.filter(InvestmentBalanceHistory.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(InvestmentBalanceHistory.created_at < end)
        if source_id := kwargs.get('source_id'):
            query = query.filter(InvestmentBalanceHistory.id == source_id)
        records = query \
            .order_by(InvestmentBalanceHistory.id.desc())\
            .paginate(kwargs['page'], kwargs['limit'])

        items = records.items
        users = User.query \
            .filter(User.id.in_(set(d.user_id for d in items))) \
            .with_entities(User.id, User.email, User.name) \
            .all()

        return dict(
            total=records.total,
            items=records.items,
            extra=dict(
                statuses=InvestmentBalanceHistory.StatusType,
                transfer_types=InvestmentBalanceHistory.OptType,
                assets=list_all_assets(),
                users={id_: dict(name=name, email=email)
                       for id_, email, name in users}
            )
        )


@ns.route('/sub_account')
@respond_with_code
class TransferHistorySubAccountResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField,
        status=EnumField(SubAccountAssetTransfer.Status),
        start=TimestampField,
        end=TimestampField,
        main_user_id=fields.Integer(),
        source_user_id=fields.Integer(),
        target_user_id=fields.Integer(),
        source_id=fields.String(),
        source_account_type=EnumField(SubAccountAssetTransfer.AccountType),
        target_account_type=EnumField(SubAccountAssetTransfer.AccountType),
        cursor=fields.Integer(),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """钱包-划转记录-子账户"""
        query = SubAccountAssetTransfer.query
        if main_user_id := kwargs.get("main_user_id"):
            query = query.filter(SubAccountAssetTransfer.main_user_id == main_user_id)
        if source_user_id := kwargs.get("source_user_id"):
            query = query.filter(SubAccountAssetTransfer.source == source_user_id)
        if target_user_id := kwargs.get("target_user_id"):
            query = query.filter(SubAccountAssetTransfer.target == target_user_id)
        if source_account_type := kwargs.get("source_account_type"):
            query = query.filter(SubAccountAssetTransfer.source_account_type == source_account_type)
        if target_account_type := kwargs.get("target_account_type"):
            query = query.filter(SubAccountAssetTransfer.target_account_type == target_account_type)
        if asset := kwargs.get('asset'):
            query = query.filter(SubAccountAssetTransfer.asset == asset)
        if (status := kwargs.get('status')) is not None:
            query = query.filter(SubAccountAssetTransfer.status == status)
        if start := kwargs.get('start'):
            query = query.filter(SubAccountAssetTransfer.created_at >= start)
        if end := kwargs.get('end'):
            query = query.filter(SubAccountAssetTransfer.created_at < end)
        if source_id := kwargs.get('source_id'):
            query = query.filter(SubAccountAssetTransfer.id == source_id)

        records = query.cursor_paginate(kwargs.get('cursor'), kwargs['limit'])

        return dict(
            cursor=records.cursor,
            has_next=records.has_next,
            items=records.items,
            extra=dict(
                statuses=SubAccountAssetTransfer.Status,
                account_types=SubAccountAssetTransfer.AccountType,
                assets=list_all_assets(),
                users={}
            )
        )
