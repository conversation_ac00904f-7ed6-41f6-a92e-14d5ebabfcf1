# -*- coding: utf-8 -*-
from typing import List

from flask import request, g
from flask_restx import fields as fx_fields, marshal
from webargs import fields as wa_fields

from app.business.auth import get_admin_user_name_map

from ...common import (Resource, Namespace, respond_with_code)
from ...common.decorators import require_admin_webauth_token
from ...common.fields import (EnumField, TimestampMarshalField, PageField,
                              LimitField, AmountField, EnumMarshalField, TimestampField)
from ....business import UpdateAssetHelper, ServerClient, \
    is_user_has_audit_permission
from ....common import Language, BalanceBusiness, ADMIN_EXPORT_LIMIT
from ....exceptions import InvalidArgument
from ....models import SalaryPay, db, BalanceUpdateBusiness
from ....utils import export_xlsx, query_to_page, timestamp_to_datetime
from ....utils.importer import get_table_rows

ns = Namespace('Asset - Salary Pay')
url_prefix = '/salary-pay'


@ns.route('/template')
@respond_with_code
class SalaryPayTemplateResource(Resource):

    export_headers = (
        {'field': 'asset', Language.ZH_HANS_CN: '币种', Language.EN_US: 'Asset'},
        {'field': 'email_userid', Language.ZH_HANS_CN: '邮箱/ID',
         Language.EN_US: 'Email/ID'},
        {'field': 'amount', Language.ZH_HANS_CN: '操作数量',
         Language.EN_US: 'Amount'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注',
         Language.EN_US: 'Remark'}
    )

    @classmethod
    def get(cls):
        return export_xlsx(
            filename='asset-update-template',
            data_list=[],
            export_headers=cls.export_headers
        )


@ns.route('/')
@respond_with_code
class SalaryPayListResource(Resource):

    marshal_fields = {
        'id': fx_fields.Integer,
        'user_id': fx_fields.Integer,
        'create_time': TimestampMarshalField(attribute='created_at'),
        'update_time': TimestampMarshalField(attribute='updated_at'),
        'asset': fx_fields.String,
        'amount': AmountField,
        'created_by': fx_fields.Integer,
        'status': EnumMarshalField(
            SalaryPay.Status, output_field_lower=False),
        'type': EnumMarshalField(
            SalaryPay.Type, output_field_lower=False),
        'audit_id': fx_fields.Integer,
        'check_id': fx_fields.Integer,
        'remark': fx_fields.String,
    }

    EXPORT_HEADERS = (
        {'field': 'id', Language.ZH_HANS_CN: 'ID'},
        {'field': 'create_time', Language.ZH_HANS_CN: '上传时间'},
        {'field': 'update_time', Language.ZH_HANS_CN: '更新时间'},
        {'field': 'asset', Language.ZH_HANS_CN: '币种'},
        {'field': 'amount', Language.ZH_HANS_CN: '数量'},
        {'field': 'user_id', Language.ZH_HANS_CN: '用户ID'},
        {'field': 'created_by', Language.ZH_HANS_CN: '上传用户'},
        {'field': 'audit_id', Language.ZH_HANS_CN: '初审人'},
        {'field': 'check_id', Language.ZH_HANS_CN: '复审人'},
        {'field': 'status', Language.ZH_HANS_CN: '状态'},
        {'field': 'type', Language.ZH_HANS_CN: '变更类型'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注'}
    )

    @classmethod
    @ns.use_kwargs(dict(
        asset=wa_fields.String,
        status=EnumField(SalaryPay.Status),
        type=EnumField(SalaryPay.Type),
        remark=wa_fields.String,
        user_id=wa_fields.Integer,
        export=wa_fields.Boolean(missing=False),
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField(unlimited=True),
        limit=LimitField(max_limit=10000)
    ))
    def get(cls, **kwargs):
        """钱包-薪资发放-列表"""
        query = SalaryPay.query
        if asset := kwargs.get('asset'):
            query = query.filter(SalaryPay.asset == asset)
        if status := kwargs.get('status'):
            query = query.filter(SalaryPay.status == status)
        if type := kwargs.get('type'):
            query = query.filter(SalaryPay.type == type)
        if user_id := kwargs.get('user_id'):
            query = query.filter(SalaryPay.user_id == user_id)
        if remark := kwargs.get('remark'):
            query = query.filter(SalaryPay.remark == remark)
        if start_time := kwargs.get('start_time'):
            query = query.filter(SalaryPay.updated_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(SalaryPay.updated_at < end_time)
        query = query.filter(
            SalaryPay.status != SalaryPay.Status.DELETED
        ).order_by(SalaryPay.id.desc())
        if kwargs['export']:
            export_data = marshal(query.limit(ADMIN_EXPORT_LIMIT).all(), cls.marshal_fields)
            user_ids = set()
            user_ids |= {i['created_by'] for i in export_data}
            user_ids |= {i['check_id'] for i in export_data}
            user_ids |= {i['audit_id'] for i in export_data}
            name_map = get_admin_user_name_map(user_ids)
            type_map = {item.name: item.value for item in SalaryPay.Type}
            status_map = {item.name: item.value for item in SalaryPay.Status}
            for data in export_data:
                data['create_time'] = timestamp_to_datetime(data['create_time']).replace(tzinfo=None)
                data['update_time'] = timestamp_to_datetime(data['update_time']).replace(tzinfo=None)
                data['created_by'] = name_map.get(data['created_by']) or '--'
                data['audit_id'] = name_map.get(data['audit_id']) or '--'
                data['check_id'] = name_map.get(data['check_id']) or '--'
                if data['type']:
                    data['type'] = type_map[data['type']]
                data['status'] = status_map[data['status']]
            return export_xlsx('salary-pay', export_data, cls.EXPORT_HEADERS)
        items = query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        user_ids = set()
        user_ids |= {i['created_by'] for i in items['data']}
        user_ids |= {i['check_id'] for i in items['data']}
        user_ids |= {i['audit_id'] for i in items['data']}
        name_map = get_admin_user_name_map(user_ids)
        for item in items['data']:
            item['created_user_name'] = name_map.get(item['created_by'])
            item['audit_user_name'] = name_map.get(item['audit_id'])
            item['check_user_name'] = name_map.get(item['check_id'])
        return dict(
            items=items,
            types=SalaryPay.Type
        )

    @classmethod
    @require_admin_webauth_token
    def post(cls):
        """钱包-薪资发放-批量上传"""
        if not (type_ := request.form.get('type')):
            raise InvalidArgument
        if not (type_ := getattr(SalaryPay.Type, type_, None)):
            raise InvalidArgument
        file_ = request.files.get('batch-upload')
        file_columns = ["asset", "email_userid", "amount", "remark"]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)
        for row in rows:
            row['type'] = type_
        UpdateAssetHelper(rows).insert_rows(model=SalaryPay)
        return dict(count=len(rows))


@ns.route('/batch-audit')
@respond_with_code
class SalaryPayBatchAuditResource(Resource):

    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        ids=wa_fields.List(wa_fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """钱包-薪资发放-批量初审"""
        user_id = g.user.id
        if not is_user_has_audit_permission(user_id, 'SALARY_PAY_AUDIT_USERS'):
            raise InvalidArgument(message='没有审核权限')

        for update_config in SalaryPay.query.filter(
            SalaryPay.id.in_(kwargs['ids']),
        ).all():
            if update_config.status != SalaryPay.Status.CREATED:
                raise InvalidArgument(message=f'{update_config.id} 状态异常')

        SalaryPay.query.filter(
            SalaryPay.id.in_(kwargs['ids']),
            SalaryPay.status == SalaryPay.Status.CREATED,
        ).update(
            {'audit_id': user_id, 'status': SalaryPay.Status.AUDITED},
            synchronize_session=False
        )
        db.session.commit()


@ns.route('/batch-check')
@respond_with_code
class SalaryPayBatchCheckResource(Resource):

    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        ids=wa_fields.List(wa_fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """钱包-薪资发放-批量复审"""
        check_user_id = g.user.id
        if not is_user_has_audit_permission(check_user_id, 'SALARY_PAY_CHECK_USERS'):
            raise InvalidArgument(message='没有审核权限')

        records: List[SalaryPay] = SalaryPay.query.filter(
            SalaryPay.id.in_(kwargs['ids']),
        ).all()
        for update_config in records:
            if update_config.status != SalaryPay.Status.AUDITED:
                raise InvalidArgument(message=f'{update_config.id} 状态异常')
            if update_config.audit_id == check_user_id:
                raise InvalidArgument(
                    message=f'{update_config.id} 初审和复审不能为同一人')

        client = ServerClient()
        for update_config in records:
            user_id = update_config.user_id
            asset = update_config.asset
            amount = update_config.amount
            business_id = BalanceUpdateBusiness.new_id(user_id, asset, amount)
            UpdateAssetHelper.check_valid_amount_for_balance_update(
                user_id, asset, amount)
            # noinspection PyBroadException
            try:
                client.add_user_balance(
                    user_id, asset, amount, BalanceBusiness.SYSTEM, business_id)
            except Exception:
                update_config.status = SalaryPay.Status.FAILED
                db.session.commit()
                continue
            update_config.check_id = check_user_id
            update_config.status = SalaryPay.Status.FINISHED
            db.session.commit()


@ns.route('/batch-delete')
@respond_with_code
class SalaryPayBatchDeleteResource(Resource):

    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        ids=wa_fields.List(wa_fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """钱包-薪资发放-批量删除"""
        for update_config in SalaryPay.query.filter(
                SalaryPay.id.in_(kwargs['ids']),
        ).all():
            if update_config.status == SalaryPay.Status.FINISHED:
                raise InvalidArgument(message=f'{update_config.id} 状态异常')

        SalaryPay.query.filter(
            SalaryPay.id.in_(kwargs['ids']),
        ).update(
            {'status': SalaryPay.Status.DELETED},
            synchronize_session=False
        )
        db.session.commit()
