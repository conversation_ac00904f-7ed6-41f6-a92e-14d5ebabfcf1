# -*- coding: utf-8 -*-
from collections import defaultdict

from flask import g
from webargs import fields

from app import config
from app.assets.configs import AssetConfig
from app.business.alert import send_alert_notice
from app.models.staking import StakingAccount

from ....assets import list_all_assets, get_asset_config, asset_to_chains, get_asset_chain_config, list_all_chains
from ....business.asset import AssetAlertHelper
from ....business.clients.wallet import CoinexWalletClient
from ....caches.assets import CoinexWalletChainMapCache
from ....exceptions import InvalidArgument
from ....models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectSystem
from ...common import Resource, Namespace, respond_with_code
from ....schedules.wallet.assets import update_coinex_wallet_support_info, sync_assets


ns = Namespace('Asset - Assets')


@ns.route('')
@respond_with_code
class AssetsResource(Resource):

    @classmethod
    def get(cls):
        """币种列表"""
        return list_all_assets()


# noinspection PyUnresolvedReferences
@ns.route('/<asset>/configs')
@respond_with_code
class AssetChainConfigsResource(Resource):

    @staticmethod
    def _filter(asset, configs):
        r = StakingAccount.query.filter(StakingAccount.asset == asset).first()
        is_staking_asset = bool(r)
        result = []
        for item in configs:
            if item['name'] == AssetConfig.staking_bufsize.name:
                continue
            if item['name'] == AssetConfig.staking_withdraw_period.name and not is_staking_asset:
                continue
            result.append(item)
        return result

    @classmethod
    def get(cls, asset):
        """币种配置"""
        config = get_asset_config(asset)
        result = config.fields_and_values_json
        return cls._filter(asset, result)


# noinspection PyUnresolvedReferences
@ns.route('/<asset>/configs/<field>')
@respond_with_code
class AssetConfigManagementResource(Resource):

    @staticmethod
    def _alert(field, value=None):
        if field in (AssetConfig.staking_bufsize.name, ):
            send_alert_notice(f'币种设置 {field} 修改为 {value}', 
                              config["ADMIN_CONTACTS"]["web_notice"])

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True)
    ))
    def put(cls, asset, field, **kwargs):
        """币种配置单项设置"""
        value = kwargs['value']
        asset_config = get_asset_config(asset)
        old_value = getattr(asset_config, field)
        try:
            setattr(asset_config, field, value)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        new_value = getattr(asset_config, field)

        cls._alert(field, value)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AssetConfig,
            old_data={field: old_value},
            new_data={field: new_value},
            special_data=dict(asset=asset),
        )
        return dict(
            value=new_value
        )

    @classmethod
    def delete(cls, asset, field):
        """币种配置单项重置"""
        asset_config = get_asset_config(asset)
        try:
            delattr(asset_config, field)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        cls._alert(field)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AssetConfig,
            detail=dict(asset=asset, field=field),
        )
        return dict(
            value=getattr(asset_config, field)
        )


@ns.route('/to-chains')
@respond_with_code
class AssetChainsResource(Resource):

    @classmethod
    def get(cls):
        """币种-链信息列表"""
        return asset_to_chains()


@ns.route('/<asset>/chain-configs/<chain>')
@respond_with_code
class AssetChainConfigResource(Resource):
    keys = ['withdrawals_disabled_to_normal_users', 'withdrawals_require_manual_audit',
            'withdrawals_manual_audit_threshold', 'deposits_require_manual_audit', 'deposits_manual_audit_threshold',
            'withdrawals_disabled_by_asset_liability',
            'withdrawals_disabled_by_accumulate_rc',
            'deposits_disabled_by_rc_abnormal',
            'withdrawals_disabled_by_rc_abnormal',
            'deposits_disabled_by_accumulate_rc_incr',
            'deposits_disabled_by_accumulate_rc_proportion',
            ]

    @classmethod
    def get(cls, asset, chain):
        """币种-链配置信息"""
        config = get_asset_chain_config(asset, chain)
        items = config.fields_and_values_json
        ret = []
        for item in items:
            if item['name'] in cls.keys:
                ret.append(item)
        return ret


@ns.route('/<asset>/chain-configs/<chain>/<field>')
@respond_with_code
class AssetChainConfigManagementResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.Raw(required=True)
    ))
    def put(cls, asset, chain, field, **kwargs):
        """币种链配置单项设置"""
        cls._check(field)
        value = kwargs['value']
        _config = get_asset_chain_config(asset, chain)
        old_value = getattr(_config, field)
        try:
            setattr(_config, field, value)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        new_value = getattr(_config, field)

        if field in AssetAlertHelper.deposit_withdrawal_risk_alert_fields:
            AssetAlertHelper.deposit_withdrawal_risk_alert(
                asset, chain, field, old_value, bool(int(value)), '手动操作', g.user.id,
            )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AssetChainConfig,
            old_data={field: old_value},
            new_data={field: new_value},
            special_data={'asset': asset, 'chain': chain},
        )

        return dict(
            value=new_value
        )

    @classmethod
    def delete(cls, asset, chain, field):
        """币种链配置单项重置"""
        cls._check(field)
        config = get_asset_chain_config(asset, chain)
        old_value = getattr(config, field)
        try:
            delattr(config, field)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')

        value = getattr(config, field)
        if field in AssetAlertHelper.deposit_withdrawal_risk_alert_fields:
            AssetAlertHelper.deposit_withdrawal_risk_alert(
                asset, chain, field, old_value, bool(int(value)), '手动操作', g.user.id,
            )

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.AssetChainConfig,
            detail=dict(asset=asset, chain=chain, field=field),
        )

        return dict(
            value=value
        )

    @classmethod
    def _check(cls, field):
        if field not in AssetChainConfigResource.keys:
            raise InvalidArgument(message=f'{field} cannot be edited')


@ns.route('/coinex_wallet_support')
@respond_with_code
class CoinexWalletSupportMapResource(Resource):

    @classmethod
    def get(cls):
        """web钱包管理-Coinex Wallet 公链映射-查看信息"""
        web_chain = list_all_chains()
        wallet_support_map = {i["chain"]: i["support_token"] for i in CoinexWalletClient().get_chain_info()}
        exist_map = CoinexWalletChainMapCache().read()
        new_data = []
        for chain in web_chain:
            wallet_chain = exist_map.get(chain, "")
            if not wallet_chain and chain in wallet_support_map:
                wallet_chain = chain
            item = dict(
                chain=chain,
                wallet_chain=wallet_chain,
                support_token=wallet_support_map.get(wallet_chain, False)
            )
            new_data.append(item)
        new_data.sort(key=lambda x: (not x['support_token'], x['wallet_chain'] == "", x['chain']))
        return dict(
            items=new_data,
            wallet_chains=list(wallet_support_map),
            wallet_support_map=wallet_support_map
        )

    @classmethod
    @ns.use_kwargs(dict(
        chain_map=fields.Dict(keys=fields.String, values=fields.String, required=True)
    ))
    def put(cls, **kwargs):
        """web钱包管理-Coinex Wallet 公链映射-修改映射"""
        chain_map = kwargs['chain_map']
        dup_map = cls.find_duplicate_values(chain_map)
        if dup_map:
            error = ""
            for wallet, web_list in dup_map.items():
                error += f"wallet 公链 {wallet} 被重复映射，请检查 web 公链 {'，'.join(web_list)}\n"
            raise InvalidArgument(message=error)

        CoinexWalletChainMapCache().save(chain_map)
        update_coinex_wallet_support_info()
        sync_assets.delay()
        return chain_map

    @classmethod
    def find_duplicate_values(cls, chain_map):
        dup = defaultdict(list)
        for web, wallet in chain_map.items():
            dup[wallet].append(web)
        return {k: v for k, v in dup.items() if len(v) > 1 and k}
