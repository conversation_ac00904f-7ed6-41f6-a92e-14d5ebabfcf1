# -*- coding: utf-8 -*-
import json
from decimal import Decimal
from typing import List, Dict, Optional
from webargs import fields
from marshmallow import Schema, EXCLUDE

from flask import g
from sqlalchemy import or_

from app.assets import list_pre_assets
from app.models import db, User, Market
from app.models.strategy import (
    SpotGridMarket,
    UserStrategy,
    SpotGridStrategy,
    SpotGridStrategyDetail,
    SpotGridStrategyMatchOrder,
    SpotGridStrategyOrderHistory,
    SpotGridStrategyTrace,
    StrategyChangeHistory,
    StrategyTransferHistory,
)
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectSpot
from app.exceptions import InvalidArgument
from app.common import OrderSideType
from app.utils import batch_iter
from app.utils.parser import JsonEncoder
from app.business import ServerClient, get_admin_user_name_map, cached
from app.business.order import Order
from app.business.strategy.grid import (
    SPOT_ACCOUNT_ID,
    MAX_GRID_COUNT,
    SpotGridOrderQuery,
    calc_grid_pnl_range,
    get_cached_market_last_price,
    terminate_spot_grid_strategy,
    update_spot_grid_strategy_profit,
    batch_terminate_grid_strategy_by_market,
)
from app.caches import MarketCache
from app.caches.strategy import (
    SpotGridMarketDepthCache,
    SpotGridStageConfigCache,
    SpotGridMarketCache,
    MarketDayKlineCache,
    MarketMinuteClosePriceCache,
)
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import LimitField, PageField, EnumField, PositiveDecimalField, TimestampField
from app.utils import quantize_amount


ns = Namespace("Strategy-Admin")


@ns.route("/grid/spot/stage-config")
@respond_with_code
class SpotGridConfigResource(Resource):
    @classmethod
    def get_and_format_configs(cls) -> List[Dict]:
        configs = SpotGridStageConfigCache().get_sorted_configs()
        last_min_depth_usd = None
        results = []
        for stage, c in enumerate(configs, 1):
            results.append(
                {
                    "stage": stage,
                    "min_depth_usd": c[0],
                    "max_depth_usd": last_min_depth_usd,
                    "max_usd": c[1],
                }
            )
            last_min_depth_usd = c[0]
        return results

    @classmethod
    def get(cls):
        """ 币币-现货网格-档位配置 """
        return cls.get_and_format_configs()

    class ConfigSchema(Schema):
        """ 档位配置 """

        stage = fields.Int(required=False)  # for update
        min_depth_usd = PositiveDecimalField(allow_zero=False, required=True)  # 最小深度市值USD
        max_usd = PositiveDecimalField(allow_zero=False, required=True)  # 最大挂单USD

        class Meta:
            UNKNOWN = EXCLUDE

    @classmethod
    @ns.use_kwargs(
        dict(
            configs=fields.Nested(ConfigSchema, many=True, required=True),
        )
    )
    def put(cls, **kwargs):
        """ 币币-现货网格-档位配置修改 """
        from app.schedules.strategy import update_spot_grid_market_cache_schedule

        cls.update_configs(kwargs["configs"])
        update_spot_grid_market_cache_schedule()  # 修改配置后，更新下市场档位

    @classmethod
    def update_configs(cls, new_configs: List[Dict]):
        # 档位深度不能重复
        min_depth_usds = set()
        data_list = []
        old_configs = SpotGridStageConfigCache().get_sorted_configs()
        for c in new_configs:
            if c["min_depth_usd"] in min_depth_usds:
                raise InvalidArgument("min_depth_usd", f"重复的最小档位市值: {c['min_depth_usd']}")
            min_depth_usds.add(c["min_depth_usd"])

            data_list.append(
                {
                    "min_depth_usd": c["min_depth_usd"],
                    "max_usd": c["max_usd"],
                }
            )
        data_list.sort(key=lambda x: x["min_depth_usd"], reverse=True)
        SpotGridStageConfigCache().set(json.dumps(data_list, cls=JsonEncoder))

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.GridConfig,
            old_data=dict(configs=old_configs),
            new_data=dict(configs=data_list),
        )


@ns.route("/grid/spot/markets")
@respond_with_code
class SpotGridMarketsResource(Resource):
    @classmethod
    def get_all_markets(cls) -> List[str]:
        return [i.name for i in SpotGridMarket.query.with_entities(SpotGridMarket.name).all()]

    @classmethod
    def find_min_depth_usd(cls, depths: List[Decimal], depth_usd: Decimal) -> Optional[Decimal]:
        # 获得对应 深度档位 的最小深度
        depths = sorted(depths, reverse=True)
        for _depth_usd in depths:
            if depth_usd >= _depth_usd:
                return _depth_usd

    @classmethod
    @ns.use_kwargs(
        dict(
            market=fields.String,
            stage=fields.Integer,
            status=EnumField(SpotGridMarket.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 币币-现货网格-市场配置列表 """
        model = SpotGridMarket
        query = model.query
        if market := kwargs.get("market"):
            query = query.filter(model.name == market)
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)
        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        format_configs = SpotGridConfigResource.get_and_format_configs()
        min_depth_usd_config_map = {c["min_depth_usd"]: c for c in format_configs}
        min_depth_usds = list(min_depth_usd_config_map.keys())

        market_stash_info_map = {}
        stage = kwargs.get("stage")
        market_depth_map = {}
        if stage is None:
            markets = [i.name for i in rows]
            if markets:
                market_depth_map = {k: Decimal(v) for k, v in SpotGridMarketDepthCache().hmget_with_keys(markets)}
        else:
            market_depth_map = {k: Decimal(v) for k, v in SpotGridMarketDepthCache().read().items()}
        for market, depth_usd in market_depth_map.items():
            min_depth_usd = cls.find_min_depth_usd(min_depth_usds, depth_usd)
            format_config = min_depth_usd_config_map.get(min_depth_usd) or {}
            market_stash_info_map[market] = {
                "depth_usd": depth_usd,
                "min_depth_usd": min_depth_usd,
                "max_depth_usd": format_config.get("max_depth_usd"),
                "max_usd": format_config.get("max_usd"),
                "stage": format_config.get("stage", -1),
                "available": bool(format_config),
            }

        items = []
        for row in rows:
            stage_info = market_stash_info_map.get(row.name, {"stage": -1, "available": False})
            if stage and stage != stage_info.get("stage"):
                continue
            d = row.to_dict(enum_to_name=True)
            d["market"] = d["name"]
            d.update(stage_info)
            items.append(d)
        items.sort(key=lambda x: (0, x["stage"]) if x["stage"] > 0 else (1, x["stage"]))

        stage_dict = {r["stage"]: r["stage"] for r in format_configs}
        stage_dict[-1] = "无档位"

        return dict(
            items=items,
            total=total,
            extra=dict(
                all_markets=MarketCache.list_online_markets(),
                markets=cls.get_all_markets(),
                status_dict={i.name: i.value for i in model.Status},
                stage_dict=stage_dict,
            ),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            market=fields.String(required=True),
            status=EnumField(SpotGridMarket.Status),
            min_order_amount_mul=fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 币币-现货网格-新增市场配置 """
        market = kwargs["market"]
        market_info = MarketCache(market).dict
        if market_info["status"] != Market.Status.ONLINE:
            raise InvalidArgument(f"市场{market}状态未上架")
        asset = market_info['base_asset']
        if asset in list_pre_assets():
            raise InvalidArgument(message=f'币种{asset}为pre token，不可配置')
        min_order_amount_mul = kwargs["min_order_amount_mul"]
        if min_order_amount_mul <= 0:
            raise InvalidArgument(f"市场{market}单格最小下单数倍数小于1")

        row: SpotGridMarket = SpotGridMarket.get_or_create(name=market)
        row.min_order_amount_mul = min_order_amount_mul
        if status := kwargs.get("status"):
            row.status = status
        else:
            row.status = SpotGridMarket.Status.ONLINE
        db.session.add(row)
        db.session.commit()
        MarketDayKlineCache.reload_by_market(market)
        MarketMinuteClosePriceCache.reload_by_market(market)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.GridMarket,
            detail=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            market=fields.String(required=True),
            status=EnumField(SpotGridMarket.Status, required=True),
            min_order_amount_mul=fields.Integer(required=True),
        )
    )
    def put(cls, **kwargs):
        """ 币币-现货网格-市场配置修改 """
        market = kwargs["market"]
        min_order_amount_mul = kwargs["min_order_amount_mul"]
        if min_order_amount_mul <= 0:
            raise InvalidArgument(f"市场{market}单格最小下单数倍数小于1")
        row: SpotGridMarket = SpotGridMarket.query.filter(
            SpotGridMarket.name == market,
        ).first()
        if not row:
            raise InvalidArgument(f"市场{market}不存在")
        old_data = row.to_dict(enum_to_name=True)
        row.min_order_amount_mul = min_order_amount_mul
        if status := kwargs.get("status"):
            if row.status != status and status != SpotGridMarket.Status.ONLINE:
                raise InvalidArgument(f"编辑市场配置只允许修改为上架")
            row.status = status
        db.session.commit()
        if row.status == SpotGridMarket.Status.OFFLINE:
            # fast update front api markets
            SpotGridMarketCache().hdel(market)
        else:
            MarketDayKlineCache.reload_by_market(market)
            MarketMinuteClosePriceCache.reload_by_market(market)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.GridMarket,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            market=fields.String(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 币币-现货网格-市场下架 """
        market = kwargs["market"]
        row: SpotGridMarket = SpotGridMarket.query.filter(
            SpotGridMarket.name == market,
        ).first()
        if not row:
            raise InvalidArgument(f"市场{market}不存在")
        if row.status == SpotGridMarket.Status.OFFLINE:
            raise InvalidArgument(message="该网格市场已下架")

        sty_list = SpotGridStrategyBatchStopResource.get_market_running_sty_list(market)
        if sty_list:
            batch_terminate_grid_strategy_by_market.delay(market)
            raise InvalidArgument(message=f"该网格市场还存在{sty_list}个未撤销的策略，无法下架市场")

        row.status = SpotGridMarket.Status.OFFLINE
        db.session.commit()
        SpotGridMarketCache().hdel(market)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.GridMarket,
            detail=row.to_dict(enum_to_name=True),
        )


@ns.route("/grid/spot/batch-stop")
@respond_with_code
class SpotGridStrategyBatchStopResource(Resource):
    @classmethod
    def get_market_running_sty_list(cls, market: str):
        sty_list = SpotGridStrategy.query.filter(
            SpotGridStrategy.market == market,
            SpotGridStrategy.status.in_(
                [
                    SpotGridStrategy.Status.CREATED,
                    SpotGridStrategy.Status.TRANSFERRED_IN,
                    SpotGridStrategy.Status.RUNNING,
                    SpotGridStrategy.Status.PAUSED,
                ]
            )
        ).with_entities(SpotGridStrategy.id).all()
        return sty_list

    @classmethod
    @ns.use_kwargs(
        dict(
            market=fields.String(required=True),
        )
    )
    def put(cls, **kwargs):
        """币币-现货网格-市场配置-批量终止策略"""
        market = kwargs['market']
        row: SpotGridMarket = SpotGridMarket.query.filter(
            SpotGridMarket.name == market,
        ).first()
        if not row:
            raise InvalidArgument(f"市场{market}不存在")

        SpotGridMarketCache().hdel(market)
        if row.status == SpotGridMarket.Status.ONLINE:
            row.status = SpotGridMarket.Status.CLOSING
            db.session.commit()

        sty_list = cls.get_market_running_sty_list(market)
        if sty_list:
            batch_terminate_grid_strategy_by_market.delay(market)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.GridMarket,
            detail=dict(market=market, count=len(sty_list)),
        )
        return dict(
            count=len(sty_list),
        )


@ns.route("/grid/spot/list")
@respond_with_code
class SpotGridStrategyListResource(Resource):

    @classmethod
    @cached(120)
    def get_market_last_prices(cls):
        tickers = ServerClient().get_all_market_tickers()
        return {m: ticker['last'] for m, ticker in tickers.items()}

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            market=fields.String,
            status=EnumField(SpotGridStrategy.Status),
            start=TimestampField,
            end=TimestampField,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 币币-现货网格-策略列表 """
        model = SpotGridStrategy
        query = model.query.filter().order_by(model.id.desc())
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if market := kwargs.get("market"):
            query = query.filter(model.market == market)
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)
        if start_time := kwargs.get("start"):
            query = query.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            query = query.filter(model.created_at <= end_time)

        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        sty_ids = [r.strategy_id for r in rows]
        basic_sty_list = UserStrategy.query.filter(
            UserStrategy.id.in_(sty_ids),
        ).all()
        basic_sty_map = {i.id: i for i in basic_sty_list}
        sty_details = SpotGridStrategyDetail.query.filter(
            SpotGridStrategyDetail.strategy_id.in_(sty_ids),
        ).all()
        sty_detail_map = {i.strategy_id: i for i in sty_details}

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 2000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        market_last_price_map = cls.get_market_last_prices()
        items = []
        for row in rows:
            basic_sty = basic_sty_map[row.strategy_id]
            d = row.to_dict(enum_to_name=True)
            d["user_email"] = user_email_map.get(row.user_id, "")
            d["strategy_id"] = basic_sty.id
            d["started_at"] = basic_sty.started_at
            d["terminated_at"] = basic_sty.terminated_at
            d["run_user_id"] = basic_sty.run_user_id
            d["basic_status"] = basic_sty.status.name

            sty_detail = sty_detail_map[row.strategy_id]
            total_profit = sty_detail.total_profit
            last_price = Decimal(market_last_price_map.get(row.market, sty_detail.stop_price))
            cost = row.quote_amount + row.base_amount * last_price
            total_profit_rate = total_profit / cost if cost else Decimal()
            detail = sty_detail.to_dict(enum_to_name=True)
            detail["total_profit"] = total_profit
            detail["total_profit_rate"] = total_profit_rate
            d["detail"] = detail
            items.append(d)

        return dict(
            items=items,
            total=total,
            extra=dict(
                basic_status_dict={i.name: i.value for i in UserStrategy.Status},
                status_dict={i.name: i.value for i in model.Status},
                hold_asset_dict={i.name: i.value for i in model.TerminateHoldAsset},
                markets=SpotGridMarketsResource.get_all_markets(),
            ),
        )


@ns.route("/grid/spot/<int:strategy_id>")
@respond_with_code
class SpotGridStrategyDetailResource(Resource):

    @classmethod
    def get_pending_orders(cls, sty: SpotGridStrategy, run_user_id: int):
        client = ServerClient()
        orders = client.user_pending_orders(
            user_id=run_user_id,
            market=sty.market,
            account_id=0,
            page=1,
            limit=MAX_GRID_COUNT + 1,  # 不会超过最大网格数
        )
        buys, sells = [], []
        for o in orders:
            o["order_id"] = o["id"]
            o["create_time"] = int(o["ctime"])
            o["order_type"] = Order.NormalOrderType(int(o["type"])).name
            o["avg_price"] = Order.get_avg_price((Decimal(o["deal_stock"]), Decimal(o["deal_money"]), o["market"]))
            if o["side"] == Order.OrderSideType.BUY:
                buys.append(o)
            else:
                sells.append(o)

        if orders:
            sty_trace: SpotGridStrategyTrace = SpotGridStrategyTrace.query.filter(
                SpotGridStrategyTrace.strategy_id == sty.strategy_id,
            ).first()
            cur_max_order_id = max([o["id"] for o in orders])
            if cur_max_order_id > sty_trace.max_order_id:
                update_spot_grid_strategy_profit.delay(sty.strategy_id)
        return buys, sells

    @classmethod
    def get(cls, strategy_id):
        """ 币币-现货网格-策略详情 """
        basic_strategy: UserStrategy = UserStrategy.query.get(strategy_id)
        sty: SpotGridStrategy = SpotGridStrategy.query.filter(
            SpotGridStrategy.strategy_id == strategy_id,
        ).first()
        sty_detail: SpotGridStrategyDetail = SpotGridStrategyDetail.query.filter(
            SpotGridStrategyDetail.strategy_id == strategy_id,
        ).first()

        if sty.status == SpotGridStrategy.Status.RUNNING:
            pending_buys, pending_sells = cls.get_pending_orders(sty, basic_strategy.run_user_id)
        else:
            pending_buys = pending_sells = []
        market_info = MarketCache(sty.market).dict
        market_max_fee = max(market_info["maker_fee_rate"], market_info["taker_fee_rate"])
        min_pnl, max_pnl = calc_grid_pnl_range(sty.lowest_price, sty.highest_price, sty.grid_count, market_max_fee)
        total_profit = sty_detail.total_profit
        try:
            last_price = Decimal(get_cached_market_last_price(sty.market))
        except:  # noqa
            last_price = sty_detail.stop_price
        cost = (sty.quote_amount + sty.base_amount * last_price)
        total_profit_rate = quantize_amount(total_profit / cost, 6) if cost else Decimal()
        detail = sty_detail.to_dict(enum_to_name=True)
        detail["min_pnl"] = min_pnl
        detail["max_pnl"] = max_pnl
        detail["total_profit"] = total_profit
        detail["total_profit_rate"] = total_profit_rate
        detail["grid_order_avg_price"] = sty_detail.grid_order_avg_price

        zero = Decimal()
        base_asset = market_info["base_asset"]
        quote_asset = market_info["quote_asset"]
        if sty.is_modifiable():
            client = ServerClient()
            balance_dict = client.get_user_balances(user_id=basic_strategy.run_user_id, account_id=SPOT_ACCOUNT_ID)
            quote_balance = balance_dict.get(quote_asset, {})
            quote_balance_amount = quote_balance["frozen"] + quote_balance["available"] if quote_balance else zero
            base_balance = balance_dict.get(base_asset, {})
            base_balance_amount = base_balance["frozen"] + base_balance["available"] if base_balance else zero
        else:
            quote_balance_amount = base_balance_amount = zero

        trans_rows = StrategyTransferHistory.query.filter(
            StrategyTransferHistory.strategy_id == strategy_id,
        ).all()

        trace_row = SpotGridStrategyTrace.query.filter(
            SpotGridStrategyTrace.strategy_id == strategy_id,
        ).first()
        trace_info = trace_row.to_dict(enum_to_name=True) if trace_row else {}

        extra = dict(
            basic_status_dict={i.name: i.value for i in UserStrategy.Status},
            status_dict={i.name: i.value for i in SpotGridStrategy.Status},
        )

        result = {
            "strategy_id": basic_strategy.id,
            "basic_status": basic_strategy.status.name,
            "run_user_id": basic_strategy.run_user_id,
            "started_at": basic_strategy.started_at,
            "terminated_at": basic_strategy.terminated_at,
            "last_price": last_price,
            "pending_buys": pending_buys,
            "pending_sells": pending_sells,
            "base_asset": base_asset,
            "quote_asset": quote_asset,
            "base_balance_amount": base_balance_amount,
            "quote_balance_amount": quote_balance_amount,
            "info": sty.to_dict(enum_to_name=True),
            "detail": detail,
            "trace_info": trace_info,
            "transfer_rows": [i.to_dict(enum_to_name=True) for i in trans_rows],
            "extra": extra,
        }
        return result

    @classmethod
    @ns.use_kwargs(
        dict(
            hold_asset=EnumField(SpotGridStrategy.TerminateHoldAsset),
        )
    )
    def delete(cls, strategy_id, **kwargs):
        """ 币币-现货网格-终止策略 """
        grid_sty: SpotGridStrategy = SpotGridStrategy.query.filter(
            SpotGridStrategy.strategy_id == strategy_id,
        ).first()
        if not grid_sty.is_terminable():
            raise InvalidArgument("策略状态不可终止")
        hold_asset = kwargs.get("hold_asset")
        terminate_spot_grid_strategy(grid_sty.strategy_id, hold_asset=hold_asset, operator_id=g.user.id)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.GridStrategy,
            detail=grid_sty.to_dict(enum_to_name=True),
        )


@ns.route("/grid/spot/finished-orders")
@respond_with_code
class SpotGridStrategyFinishedOrdersResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            strategy_id=fields.Integer(required=True, validate=lambda x: x > 0),
            side=EnumField(['1', '2']),
            order_type=EnumField(['1', '2']),
            start=TimestampField,
            end=TimestampField,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 币币-现货网格-策略详情-历史委托 """
        strategy_id = kwargs["strategy_id"]
        basic_sty: UserStrategy = UserStrategy.query.get(strategy_id)
        sty: SpotGridStrategy = SpotGridStrategy.query.filter(
            SpotGridStrategy.strategy_id == strategy_id,
        ).first()
        o_fields = [
            "order_id", "create_time", "finish_time", "market", "side", "price", "amount",
            "deal_stock", "deal_money", "money_fee", "stock_fee", "t", "option", "source",
            "id", "client_id",
        ]
        start_time = kwargs.get("start") or basic_sty.created_at
        if start_time < basic_sty.created_at:
            start_time = basic_sty.created_at
        end_time = kwargs.get("end") or basic_sty.terminated_at or 0
        if end_time and basic_sty.terminated_at and end_time > basic_sty.terminated_at:
            end_time = basic_sty.terminated_at

        page, limit = kwargs["page"], kwargs["limit"]
        offset_limit = ((page - 1) * limit, limit)
        finished_orders = SpotGridOrderQuery.query_finished_orders(
            user_id=basic_sty.run_user_id,
            market=sty.market,
            fields=o_fields,
            start_time=start_time,
            end_time=end_time,
            side=kwargs.get("side", 0),
            type_=kwargs.get("order_type", 0),
            order_by=" order_id desc ",
            limit=offset_limit,
        )

        market_order_ids = [i["order_id"] for i in finished_orders if i["t"] == Order.NormalOrderType.MARKET]
        web_order_history_list = SpotGridStrategyOrderHistory.query.filter(
            SpotGridStrategyOrderHistory.strategy_id == strategy_id,
            SpotGridStrategyOrderHistory.order_id.in_(market_order_ids),
        ).all()
        web_order_history_map = {i.order_id: i for i in web_order_history_list}

        sell_order_ids = [i["order_id"] for i in finished_orders if i["side"] == OrderSideType.SELL]
        buy_order_ids = [i["order_id"] for i in finished_orders if i["side"] == OrderSideType.BUY]
        match_rows = SpotGridStrategyMatchOrder.query.filter(
            SpotGridStrategyMatchOrder.strategy_id == strategy_id,
            or_(
                SpotGridStrategyMatchOrder.sell_order_id.in_(sell_order_ids),
                SpotGridStrategyMatchOrder.buy_order_id.in_(buy_order_ids),
            )
        ).all()
        match_order_id_map = {}
        order_id_match_row_map = {}
        for r in match_rows:
            if r.sell_order_id:
                order_id_match_row_map[r.sell_order_id] = r
            if r.buy_order_id:
                order_id_match_row_map[r.buy_order_id] = r
            if r.sell_order_id and r.buy_order_id:
                match_order_id_map[r.sell_order_id] = r.buy_order_id
                match_order_id_map[r.buy_order_id] = r.sell_order_id

        for o in finished_orders:
            o["order_type"] = Order.NormalOrderType(int(o["t"])).name
            o["avg_price"] = Order.get_avg_price((Decimal(o["deal_stock"]), Decimal(o["deal_money"]), o["market"]))
            o["match_order_id"] = match_order_id_map.get(o["order_id"])
            match_info_row = order_id_match_row_map.get(o["order_id"])
            if match_info_row:
                match_info = match_info_row.to_dict()
                if match_info["sell_order_info"]:
                    so = json.loads(match_info["sell_order_info"])
                    so["avg_price"] = Order.get_avg_price((Decimal(so["deal_stock"]), Decimal(so["deal_money"]), so["market"]))
                    match_info["sell_order_info"] = so
                if match_info["buy_order_info"]:
                    bo = json.loads(match_info["buy_order_info"])
                    bo["avg_price"] = Order.get_avg_price((Decimal(bo["deal_stock"]), Decimal(bo["deal_money"]), bo["market"]))
                    match_info["buy_order_info"] = bo
            else:
                match_info = {}
            o["match_info"] = match_info
            web_o = web_order_history_map.get(o["order_id"])
            web_order_info = web_o.to_dict() if web_o else {}
            o["web_order_info"] = web_order_info

        return {
            "curr_page": page,
            "has_next": len(finished_orders) >= limit,
            "items": finished_orders,
        }


@ns.route("/grid/spot/web-order-history")
@respond_with_code
class SpotGridStrategyOrderHistoryResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            strategy_id=fields.Integer(required=True, validate=lambda x: x > 0),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 币币-现货网格-手续费订单 """
        strategy_id = kwargs["strategy_id"]
        q = SpotGridStrategyOrderHistory.query.filter(
            SpotGridStrategyOrderHistory.strategy_id == strategy_id,
        ).order_by(SpotGridStrategyOrderHistory.id.desc())

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows = pagination.items
        return dict(
            total=pagination.total,
            items=rows,
        )


@ns.route("/grid/spot/change-history")
@respond_with_code
class SpotGridStrategyChangeHistoryResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            strategy_id=fields.Integer(required=False, validate=lambda x: x > 0),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 币币-现货网格-策略历史 """
        q = StrategyChangeHistory.query.order_by(StrategyChangeHistory.id.desc())
        if strategy_id := kwargs.get("strategy_id"):
            q = q.filter(StrategyChangeHistory.strategy_id == strategy_id)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows = pagination.items

        if rows:
            sty_ids = {i.strategy_id for i in rows}
            sty_list = SpotGridStrategy.query.filter(
                SpotGridStrategy.strategy_id.in_(list(sty_ids)),
            ).with_entities(
                SpotGridStrategy.strategy_id,
                SpotGridStrategy.entry_price,
                SpotGridStrategy.market,
            ).all()
            sty_map = {i.strategy_id: i for i in sty_list}

            markets = {i.market for i in sty_list}
            market_map = {i: MarketCache(i).dict for i in markets}

            user_ids = {i.operator_id for i in rows if i.operator_id}
            user_name_map = get_admin_user_name_map(list(user_ids))
        else:
            sty_map = {}
            market_map = {}
            user_name_map = {}

        items = []
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            sty = sty_map[r.strategy_id]
            item["operator_name"] = user_name_map.get(r.operator_id)
            item["entry_price"] = sty.entry_price
            item["quote_asset"] = market_map[sty.market]["quote_asset"]
            if item["new_info"]:
                item["new_info"] = json.loads(item["new_info"])
            if item["old_info"]:
                item["old_info"] = json.loads(item["old_info"])
            items.append(item)

        return dict(
            total=pagination.total,
            items=items,
            extra=dict(
                op_type_dict={i.name: i.value for i in StrategyChangeHistory.OpType},
            ),
        )
