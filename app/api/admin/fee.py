# -*- coding: utf-8 -*-

from decimal import Decimal

from flask import g
from marshmallow import Schema, EXCLUDE
from sqlalchemy import or_
from webargs.flaskparser import use_kwargs

from app.api.common.decorators import require_super_admin

from ..common import Resource, Namespace, respond_with_code
from ..common.fields import (
    PageField, LimitField, EnumField, TimestampField,
    mm_fields,
    )
from ...business import get_special_conf_create_operators
from ...business.fee_constant import (
    DEFAULT_MIN_CONTRACT_MAKER_FEE,
    DEFAULT_MIN_CONTRACT_TAKER_FEE,
    )
from ...business.fee import update_user_special_fee_task
from ...common import TradeType, TradeBusinessType
from ...caches import MarketCache, PerpetualMarketCache
from ...exceptions import InvalidArgument
from ...models import (
    Market, User, UserConfigTradeFee, db, UserTradeFeeDiscount,
    UserSpecialConfigChangeLog, AmmMarket,
)
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperation<PERSON>og, OPNamespaceObjectUser
from ...utils import validate_email, quantize_amount
from ...utils.helper import Struct
from ...business.fee import update_user_fee_discount_task

ns = Namespace('ConfigFee')

url_prefix = '/fee'


def check_contract_fee_rate(trade_type: TradeType, fee: Decimal):
    if trade_type == TradeType.MAKER:
        if Decimal(fee) < DEFAULT_MIN_CONTRACT_MAKER_FEE:
            raise InvalidArgument(message='maker费率不能低于-0.00025')
    else:
        if Decimal(fee) < DEFAULT_MIN_CONTRACT_TAKER_FEE:
            raise InvalidArgument(message='taker费率不能低于0.00025')


def get_contract_market_names():
    return PerpetualMarketCache().get_market_list()


def get_spot_market_names():
    query = Market.query.filter(
        Market.status.in_(
            [
                Market.Status.PENDING,
                Market.Status.BIDDING,
                Market.Status.COUNTING_DOWN,
                Market.Status.ONLINE,
            ]
        )
    ).with_entities(
        Market.name
    )
    return [v.name for v in query]


def get_market_names():
    return ['ALL'] + get_contract_market_names() + get_spot_market_names()


@ns.route('/search-user')
@respond_with_code
class UserSearchResource(Resource):
    
    @classmethod
    @require_super_admin
    @ns.use_kwargs(dict(
           search_keyword=mm_fields.String(required=True)
            ))
    def post(cls, **kwargs):
        """用户-用户特殊费率列表-搜索用户"""
        search_keyword = kwargs.get("search_keyword", "")
        if len(search_keyword) < 6 and not search_keyword.isdigit():
            return []
        u_q = User.query
        if search_keyword.isdigit():
            u_q = u_q.filter(
                or_(User.id == search_keyword,
                    User.mobile == search_keyword))
        elif validate_email(search_keyword):
            u_q = u_q.filter(User.email == search_keyword)
        elif search_keyword:
            u_q = u_q.filter(User.email.contains(search_keyword))
        result = u_q.limit(50)
        return [{'label': '{user_id}:{email}:{mobile}'.format(
            user_id=v.id,
            email=v.email,
            mobile=v.mobile), 'value': v.id} for v in result if
                not v.is_sub_account]


@ns.route('/config')
@respond_with_code
class UserFeeConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
            page=PageField(unlimited=True),
            limit=LimitField(missing=50),
            business_type=EnumField(TradeBusinessType, enum_by_value=True),
            market_name=mm_fields.String(),
            trade_type=EnumField(TradeType, enum_by_value=True),
            start_time=TimestampField(is_ms=True),
            end_time=TimestampField(is_ms=True),
            search_keyword=mm_fields.String()
            ))
    def get(cls, **kwargs):
        """用户-用户特殊费率列表-配置列表"""
        args = Struct(**kwargs)
        all_market_names = get_market_names()
        search_keyword = args.search_keyword
        q = UserConfigTradeFee.query.filter(
                UserConfigTradeFee.status == UserConfigTradeFee.StatusType.PASS,
                ).order_by(
                UserConfigTradeFee.id.desc()
                )
        if search_keyword:
            u_q = User.query
            if search_keyword.isdigit():
                u_q = u_q.filter(
                        or_(User.id == search_keyword,
                            User.mobile == search_keyword))
            elif validate_email(search_keyword):
                u_q = u_q.filter(User.email == search_keyword)
            elif search_keyword:
                u_q = u_q.filter(User.email.contains(search_keyword))

            if u_q.count() == 1:
                user = u_q.first()
                q = q.filter(UserConfigTradeFee.user_id == user.id)
        if args.business_type:
            q = q.filter(
                    UserConfigTradeFee.business_type == args.business_type
                    )

        if args.trade_type:
            q = q.filter(
                    UserConfigTradeFee.trade_type == args.trade_type
                    )
        if args.market_name and args.market_name != 'ALL':
            q = q.filter(
                    UserConfigTradeFee.market_name == args.market_name
                    )
        if args.market_name == 'ALL':
            q = q.filter(
                    UserConfigTradeFee.market_name == ''
            )
        if args.start_time:
            q = q.filter(
                    UserConfigTradeFee.expired_time >= args.start_time,
                )
        if args.end_time:
            q = q.filter(
                    UserConfigTradeFee.expired_time >= args.end_time,
                )
        records = q.paginate(args.page, args.limit, error_out=False)

        user_ids = [item.user_id for item in records.items]
        user_dict = {
                v.id: v.email
                for v in User.query.filter(User.id.in_(
                        user_ids)).with_entities(
                        User.id, User.email
                        )
                }
        record_ids = [i.id for i in records.items]
        operator_id_dict, operator_name_dict = get_special_conf_create_operators(
            record_ids, UserSpecialConfigChangeLog.SpecialConfigType.FEE)
        amm_markets = {
            v.name
            for v in
            AmmMarket.query.filter(AmmMarket.status == AmmMarket.Status.ONLINE).with_entities(
                AmmMarket.name
            ).all()
        }
        table_data = [dict(
                id=v.id,
                user_id=v.user_id,
                user_email=user_dict.get(v.user_id, ''),
                business_type=v.business_type,
                market_name=v.market_name,
                trade_type=v.trade_type,
                fee=v.fee,
                expired_time=v.expired_time,
                updated_at=v.updated_at,
                remark=v.remark,
                operator=operator_name_dict.get(v.id),
                operator_id=operator_id_dict.get(v.id)
                ) for v in records.items]

        return dict(
                total=q.count(),
                items=table_data,
                all_market_names=all_market_names,
                spot_markets=[
                    dict(label=f"{_name} (AMM)", value=_name) if _name in amm_markets else
                    dict(label=f"{_name}", value=_name)
                    for _name in ['ALL'] + get_spot_market_names()],
                perpetual_markets=[
                    dict(label=_name, value=_name)
                    for _name in ['ALL'] + get_contract_market_names()],
                amm_markets=amm_markets,
                business_type_dict={
                        TradeBusinessType.SPOT.value: "现货",
                        TradeBusinessType.PERPETUAL.value: "合约",
                    },
                trade_type_dict={
                        TradeType.MAKER.value: "maker费率",
                        TradeType.TAKER.value: "taker费率",
                    }
                )

    class DetailSchema(Schema):
        business_type = EnumField(TradeBusinessType,
                                  enum_by_value=True, required=True)
        trade_type = EnumField(TradeType, enum_by_value=True, required=True)
        market_name = mm_fields.String(required=True)
        fee = mm_fields.Decimal(required=True)
        expired_time = TimestampField(is_ms=True, allow_none=True)

        class Meta:
            UNKNOWN = EXCLUDE

    @classmethod
    @require_super_admin
    @use_kwargs(dict(
            user_id=mm_fields.Integer(required=True),
            fee_config=mm_fields.Nested(DetailSchema, many=True, required=True),
            remark=mm_fields.String()
        ))
    def post(cls, **kwargs):
        """用户-用户特殊费率列表-添加配置"""
        body = Struct(**kwargs)
        user_id = body.user_id
        fee_config = body.fee_config
        if not fee_config:
            raise InvalidArgument(message='数据错误')
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(message="用户不存在")
        if user.is_sub_account:
            raise InvalidArgument(message="子账号无法创建特殊费率")
        if len(fee_config) == 0:
            raise InvalidArgument(message="配置不可为空")
        admin_user_id = g.user.id
        market_names = get_market_names()
        perpetual_market_names = get_contract_market_names()
        spot_market_names = get_spot_market_names()

        for index, config in enumerate(fee_config):
            # 检查重复数据
            business_type = config["business_type"]
            trade_type = config["trade_type"]
            market_name = config["market_name"]
            if index != len(fee_config) - 1:
                rest_config = fee_config[index + 1:]
                for v in rest_config:
                    if v["business_type"] == business_type and v[
                        "trade_type"] == trade_type and \
                            v["market_name"] == market_name:
                        raise InvalidArgument(message="配置重复")

        for index, config in enumerate(fee_config):
            if config["market_name"] not in market_names and \
                    config["market_name"] != "":
                raise InvalidArgument(message="交易对错误")
            if config["business_type"] != \
                    TradeBusinessType.PERPETUAL \
                    or config["trade_type"] != TradeType.MAKER:
                if config["fee"] < Decimal(0):
                    raise InvalidArgument(message="除合约maker费率外不可为负数")
            if config["business_type"] == TradeBusinessType.SPOT:
                if config["fee"] % Decimal('0.0001') != 0:
                    raise InvalidArgument(message="现货费率精度不能超过四位小数")
            if config["market_name"] != 'ALL' and config[
                "business_type"] == TradeBusinessType.PERPETUAL \
                    and \
                    config["market_name"] not in perpetual_market_names:
                raise InvalidArgument(message="交易对与业务类型不匹配")
            if config["market_name"] != 'ALL' and config[
                "business_type"] == TradeBusinessType.SPOT and \
                    config["market_name"] not in spot_market_names:
                raise InvalidArgument(message="交易对与业务类型不匹配")
            if config["market_name"] == 'ALL':
                fee_config[index]['market_name'] = ''
            if config['business_type'] == TradeBusinessType.PERPETUAL:
                check_contract_fee_rate(config['trade_type'], config['fee'])
        for index, config in enumerate(fee_config):
            q = UserConfigTradeFee.query.filter(
                    UserConfigTradeFee.user_id == user_id,
                    UserConfigTradeFee.business_type == config["business_type"],
                    UserConfigTradeFee.trade_type == config["trade_type"],
                    UserConfigTradeFee.market_name == config["market_name"].strip()
                    ).first()
            if q and q.status == UserConfigTradeFee.StatusType.PASS:
                raise InvalidArgument(message="配置重复，请检查")
            if q and q.status == UserConfigTradeFee.StatusType.DELETE:
                fee_config[index]['create'] = False
            else:
                fee_config[index]['create'] = True
        create_data = []
        for config in fee_config:
            if config["create"]:
                create_data.append(
                        UserConfigTradeFee(
                                user_id=user_id,
                                expired_time=config["expired_time"],
                                trade_type=config["trade_type"],
                                business_type=config["business_type"],
                                market_name=config["market_name"].strip(),
                                fee=Decimal(config["fee"]),
                                status=UserConfigTradeFee.StatusType.PASS,
                                remark=body.remark or ''
                                )
                        )
            else:
                expired_time = config["expired_time"]
                UserConfigTradeFee.query.filter_by(
                    user_id=user_id,
                    trade_type=config["trade_type"],
                    business_type=config["business_type"],
                    market_name=config["market_name"].strip(),
                ).update(
                    {
                        UserConfigTradeFee.status: UserConfigTradeFee.StatusType.PASS,
                        UserConfigTradeFee.expired_time: expired_time,
                        UserConfigTradeFee.fee: config["fee"],
                        UserConfigTradeFee.remark: body.remark or ''
                    },
                    synchronize_session=False
                )
        db.session.add_all(create_data)
        db.session.commit()
        update_user_special_fee_task(user_id)
        AdminOperationLog.new_add(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.SpecialFee,
            detail=fee_config,
            target_user_id=user_id,
        )
        for create_obj in create_data:
            UserSpecialConfigChangeLog.add(
                user_id=create_obj.user_id,
                config_type=UserSpecialConfigChangeLog.SpecialConfigType.FEE,
                op_type=UserSpecialConfigChangeLog.OpType.CREATE,
                admin_user_id=admin_user_id,
                change_detail=create_obj.record_detail,
                change_remark=body.remark,
                op_id=create_obj.id
            )

    @classmethod
    @require_super_admin
    @ns.use_kwargs(dict(
            id=mm_fields.Integer(required=True),
            remark=mm_fields.String(),
            fee=mm_fields.Decimal(),
            business_type=EnumField(TradeBusinessType, enum_by_value=True),
            trade_type=EnumField(TradeType, enum_by_value=True),
            expired_time=TimestampField(is_ms=True, allow_none=True)
            ))
    def put(cls, **kwargs):
        """用户-用户特殊费率列表-修改配置"""
        body = Struct(**kwargs)
        admin_user_id = g.user.id
        record = UserConfigTradeFee.query.filter(
                UserConfigTradeFee.id == body.id
                ).first()
        if not record:
            return InvalidArgument(message="记录不存在")
        old_data = record.to_dict(enum_to_name=True)
        record.expired_time = body.expired_time
        if record.business_type != TradeBusinessType.PERPETUAL\
                or record.trade_type != TradeType.MAKER:
            if body.fee < Decimal(0):
                raise InvalidArgument(message="除合约maker费率外不可为负数")
        if record.business_type == TradeBusinessType.SPOT:
            if body.fee % Decimal('0.0001') != 0:
                raise InvalidArgument(message="现货费率精度不能超过四位小数")
        elif record.business_type == TradeBusinessType.PERPETUAL:
            check_contract_fee_rate(record.trade_type, body.fee)

        record.fee = body.fee
        record.remark = body.remark or ''
        db.session.commit()
        update_user_special_fee_task(record.user_id)
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.SpecialFee,
            old_data=old_data,
            new_data=record.to_dict(enum_to_name=True),
            target_user_id=record.user_id,
        )
        UserSpecialConfigChangeLog.add(
            user_id=record.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.FEE,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=admin_user_id,
            change_detail=record.record_detail,
            change_remark=body.remark,
            op_id=record.id
        )

    @classmethod
    @require_super_admin
    @use_kwargs(dict(
            id=mm_fields.Integer(required=True),
            remark=mm_fields.String(default='', missing='', allow_none=True)
            ))
    def delete(cls, **kwargs):
        """用户-用户特殊费率列表-删除配置"""
        body = Struct(**kwargs)
        record = UserConfigTradeFee.query.filter(
                UserConfigTradeFee.id == body.id
                ).first()
        if not record:
            return InvalidArgument(message="记录不存在")
        record.status = UserConfigTradeFee.StatusType.DELETE
        db.session.commit()
        update_user_special_fee_task(record.user_id)
        old_record_data = {
            "fee": str(record.fee),
            "business_type": record.business_type.name,
            "trade_type": record.trade_type.name,
            "expired_time": str(record.expired_time)
        }
        admin_user_id = g.user.id
        AdminOperationLog.new_delete(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.SpecialFee,
            detail=old_record_data,
            target_user_id=record.user_id,
        )
        UserSpecialConfigChangeLog.add(
            user_id=record.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.FEE,
            op_type=UserSpecialConfigChangeLog.OpType.DELETE,
            admin_user_id=admin_user_id,
            change_detail=record.record_detail,
            change_remark=body.remark,
            op_id=record.id
        )


@ns.route('/discount/config')
@respond_with_code
class UserFeeDiscountConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(),
        limit=LimitField(),
        market_name=mm_fields.String(),
        start_time=TimestampField(),
        end_time=TimestampField(),
        search_keyword=mm_fields.String()
    ))
    def get(cls, **kwargs):
        """用户-CET特殊折扣配置"""
        query = UserTradeFeeDiscount.query.filter(
            UserTradeFeeDiscount.status == UserTradeFeeDiscount.StatusType.PASS
        ).order_by(UserTradeFeeDiscount.id.desc())
        if market_name := kwargs.get('market_name'):
            query = query.filter(UserTradeFeeDiscount.market_name == market_name)
        if start_time := kwargs.get('start_time'):
            query = query.filter(UserTradeFeeDiscount.expired_time >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(UserTradeFeeDiscount.expired_time <= end_time)
        if search_keyword := kwargs.get('search_keyword'):
            user_ids = User.search_for_users(search_keyword)
            query = query.filter(UserTradeFeeDiscount.user_id.in_(user_ids))

        records = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)

        user_emails = User.query.filter(
            User.id.in_({x.user_id for x in records.items})
        ).with_entities(
            User.id, User.email
        ).all()
        user_emails = dict(user_emails)
        record_ids = [i.id for i in records.items]
        operator_id_dict, operator_name_dict = get_special_conf_create_operators(
            record_ids, UserSpecialConfigChangeLog.SpecialConfigType.CET_DISCOUNT)
        items = []
        for row in records.items:
            items.append(dict(
                id=row.id,
                user_id=row.user_id,
                email=user_emails.get(row.user_id),
                market_name=row.market_name,
                discount=row.discount,
                expired_time=row.expired_time,
                remark=row.remark,
                updated_at=row.updated_at,
                operator=operator_name_dict.get(row.id),
                operator_id=operator_id_dict.get(row.id)
            ))

        return dict(
            items=items,
            total=records.total,
            market_list=['ALL'] + MarketCache.list_online_markets()
        )
    
    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(required=True),
        remark=mm_fields.String(allow_none=True),
        market_name=mm_fields.String(required=True),
        discount=mm_fields.Decimal(required=True),
        expired_time=TimestampField(is_ms=True, allow_none=True)
    ))
    def post(cls, **kwargs):
        """用户-CET特殊折扣配置-新增"""
        discount = quantize_amount(kwargs['discount'], 2)
        if not 0 < discount < 1:
            raise InvalidArgument
        user = User.query.get(kwargs['user_id'])
        if not user:
            raise InvalidArgument(message="用户不存在")
        if user.is_sub_account:
            raise InvalidArgument(message="不能设置子账户")
        row = UserTradeFeeDiscount.query.filter(
            UserTradeFeeDiscount.user_id == user.id,
            UserTradeFeeDiscount.market_name == kwargs['market_name']
        ).first()
        if row and row.status == UserTradeFeeDiscount.StatusType.PASS:
            raise InvalidArgument(message="配置已存在")
        if not row:
            row = UserTradeFeeDiscount(
                user_id=user.id,
                market_name=kwargs['market_name'],
                discount=discount,
                remark=kwargs.get('remark'),
                expired_time=kwargs['expired_time']
            )
            db.session.add(row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SpecialCETDiscount,
                detail=kwargs,
                target_user_id=user.id,
            )
        else:
            old_data = row.to_dict(enum_to_name=True)
            row.status = UserTradeFeeDiscount.StatusType.PASS
            row.discount = discount
            row.expired_time = kwargs['expired_time']
            row.remark = kwargs.get('remark')

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SpecialCETDiscount,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
                target_user_id=user.id,
            )
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=row.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.CET_DISCOUNT,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail=row.record_detail,
            change_remark=kwargs.get('remark'),
            op_id=row.id
        )
        update_user_fee_discount_task.delay(user.id)
        return {}


@ns.route('/discount/config/<int:id_>')
@respond_with_code
class UserFeeDiscountConfigDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        remark=mm_fields.String(allow_none=True),
        discount = mm_fields.Decimal(required=True),
        expired_time = TimestampField(is_ms=True, allow_none=True)
    ))
    def put(cls, id_, **kwargs):
        """用户-CET特殊折扣配置-编辑"""
        discount = quantize_amount(kwargs['discount'], 2)
        if not 0 < discount < 1:
            raise InvalidArgument
        row = UserTradeFeeDiscount.query.get(id_)
        if not row or row.status != UserTradeFeeDiscount.StatusType.PASS:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)

        row.discount = discount
        row.expired_time = kwargs['expired_time']
        row.remark = kwargs.get('remark')
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=row.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.CET_DISCOUNT,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail=row.record_detail,
            change_remark=kwargs.get('remark'),
            op_id=row.id
        )
        update_user_fee_discount_task.delay(row.user_id)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialCETDiscount,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )
        return {}

    @classmethod
    def delete(cls, id_):
        """用户-CET特殊折扣配置-删除"""
        row = UserTradeFeeDiscount.query.get(id_)
        if not row:
            raise InvalidArgument
        row.status = UserTradeFeeDiscount.StatusType.DELETE
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=row.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.CET_DISCOUNT,
            op_type=UserSpecialConfigChangeLog.OpType.DELETE,
            admin_user_id=g.user.id,
            change_detail=row.record_detail,
            op_id=row.id
        )
        update_user_fee_discount_task.delay(row.user_id)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialCETDiscount,
            detail=dict(id=id_, market_name=row.market_name),
            target_user_id=row.user_id,
        )
        return {}
