# -*- coding: utf-8 -*-

import json
from datetime import date
from decimal import Decimal

from marshmallow import fields as mm_fields
from sqlalchemy import func, case, inspect

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.decorators import require_admin_webauth_token
from app.api.common.fields import AssetField, EnumField, PageField, LimitField, DateField, PositiveDecimalField
from app.assets.asset import get_asset_config, try_get_asset_config
from app.business.prices import PriceManager
from app.caches.statistics import StakingStatisticsCache
from app.common import ReportType
from app.common.constants import PrecisionEnum
from app.models.daily import DailyStakingReport, DailyStakingSiteReport
from app.models.monthly import MonthlyStakingReport, MonthlyStakingSiteReport
from app.models.staking import StakingAccount, StakingHistory, StakingSyncRecord
from app.utils import quantize_amount, export_xlsx
from app.utils.date_ import timestamp_to_datetime

ns = Namespace('Staking')


@ns.route('/report')
@respond_with_code
class AssetStakingReportResource(Resource):
    
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "user_count", Language.ZH_HANS_CN: "质押人数"},
        {"field": "new_user_count", Language.ZH_HANS_CN: "新增质押人数"},
        {"field": "staking_amount", Language.ZH_HANS_CN: "质押数量"},
        {"field": "new_staking_amount", Language.ZH_HANS_CN: "新增数量"},
        {"field": "new_unstaking_amount", Language.ZH_HANS_CN: "赎回数量"},
        {"field": "reward_user_count", Language.ZH_HANS_CN: "质押收益人数"},
        {"field": "reward_amount", Language.ZH_HANS_CN: "平台总收益"},
        {"field": "user_reward_amount", Language.ZH_HANS_CN: "用户总收益"},
        {"field": "system_reward_amount", Language.ZH_HANS_CN: "平台收益"},
        {"field": "income_rate", Language.ZH_HANS_CN: "理论收益率(平台用户)"},
    )

    excluded_fields = (
            'id',
            'created_at',
            'income_user_bitmap',
            'user_bitmap',
            'history_income_user_bitmap',
            'history_user_bitmap',
        )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        asset=AssetField(required=True),
        export=mm_fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """
        全站质押理财报表
        """
        report_type = kwargs['report_type']
        page = kwargs['page']
        limit = kwargs['limit']
        export = kwargs['export']
        if report_type == ReportType.DAILY:
            report_cls = DailyStakingReport
        else:
            report_cls = MonthlyStakingReport
        query = report_cls.query.filter(
            report_cls.asset == kwargs['asset'],
        ).order_by(report_cls.report_date.desc())
        if start_date:= kwargs.get('start_date'):
            query = query.filter(report_cls.report_date >= start_date)
        if end_date:= kwargs.get('end_date'):
            query = query.filter(report_cls.report_date <= end_date)
        report_cls: DailyStakingReport
        all_fields = inspect(report_cls).c.keys()
        fields = [f for f in all_fields if f not in cls.excluded_fields]
        query = query.order_by(report_cls.report_date.desc()).with_entities(
            *[getattr(report_cls, field) for field in fields]
        )
        if export:
            data = []
            for record in query.all():
                data.append({k: getattr(record, k) for k in fields})
            return export_xlsx(
                filename='staking_site_report',
                data_list=data,
                export_headers=cls.export_headers
            )
        pagination = query.paginate(page, limit)
        records = pagination.items
        total = pagination.total
        data = [{k: getattr(record, k) for k in fields} for record in records]
        db_r = StakingAccount.query.filter(
                StakingAccount.status == StakingAccount.Status.OPEN
            )
        asset_list = [i.asset for i in db_r]
        return dict(
            assets=asset_list,
            total=total,
            items=data,
        )


@ns.route('/report/detail')
@respond_with_code
class AssetStakingReportDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        report_date=DateField(to_date=True, required=True),
        order=mm_fields.String(missing='user_count'),
    ))
    def get(cls, **kwargs):
        """
        质押理财报表-币种详情
        """
        order = kwargs['order']
        report_type = kwargs['report_type']
        if report_type == ReportType.DAILY:
            model = DailyStakingReport
        else:
            model = MonthlyStakingReport
        report_date = kwargs['report_date']
        if kwargs['report_type'] == ReportType.MONTHLY:
            report_date = date(report_date.year, report_date.month, 1)

        all_fields = inspect(DailyStakingReport).c.keys()
        fields = [f for f in all_fields if f not in AssetStakingReportResource.excluded_fields]
        records = model.query.filter(
            model.report_date == report_date
        ).order_by(
            getattr(model, order).desc()
        ).with_entities(
            *[getattr(model, field) for field in fields]
        ).all()
        data = [{k: getattr(record, k) for k in fields} for record in records]

        return dict(
            records=data,
        )


@ns.route('/site-report')
@respond_with_code
class SiteStakingReportResource(Resource):
    
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "user_count", Language.ZH_HANS_CN: "质押人数"},
        {"field": "new_user_count", Language.ZH_HANS_CN: "新增质押人数"},
        {"field": "staking_amount", Language.ZH_HANS_CN: "质押数量"},
        {"field": "new_staking_amount", Language.ZH_HANS_CN: "新增数量"},
        {"field": "new_unstaking_amount", Language.ZH_HANS_CN: "赎回数量"},
        {"field": "net_unstaking_amount", Language.ZH_HANS_CN: "净质押数量"},
        {"field": "user_reward_amount", Language.ZH_HANS_CN: "用户收益"},
        {"field": "system_reward_amount", Language.ZH_HANS_CN: "平台收益"},
        {"field": "reward_amount", Language.ZH_HANS_CN: "质押总收益"},        
        {"field": "income_rate", Language.ZH_HANS_CN: "平均收益率"},
    )

    excluded_fields = (
            'id',
            'created_at',
            'income_user_bitmap',
            'user_bitmap',
            'history_income_user_bitmap',
            'history_user_bitmap',
        )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=mm_fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """
        质押理财汇总报表
        """
        report_type = kwargs['report_type']
        page = kwargs['page']
        limit = kwargs['limit']
        export = kwargs['export']
        if report_type == ReportType.DAILY:
            report_cls = DailyStakingSiteReport
        else:
            report_cls = MonthlyStakingSiteReport
        query = report_cls.query.order_by(report_cls.report_date.desc())
        if start_date:= kwargs.get('start_date'):
            query = query.filter(report_cls.report_date >= start_date)
        if end_date:= kwargs.get('end_date'):
            query = query.filter(report_cls.report_date <= end_date)
        all_fields = inspect(report_cls).c.keys()
        fields = [f for f in all_fields if f not in cls.excluded_fields]
        query = query.order_by(report_cls.report_date.desc()).with_entities(
            *[getattr(report_cls, field) for field in fields]
        )
        if export:
            data = []
            for record in query.all():
                data.append({k: getattr(record, k) for k in fields})
            return export_xlsx(
                filename='staking_site_report',
                data_list=data,
                export_headers=cls.export_headers
            )
        pagination = query.paginate(page, limit)
        records = pagination.items
        total = pagination.total
        data = [{k: getattr(record, k) for k in fields} for record in records]
        return dict(
            total=total,
            items=data,
        )


@ns.route('/balance-rank')
@respond_with_code
class StakingBalanceRankResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=mm_fields.String(required=True),
        user_id=mm_fields.Integer,
        page=mm_fields.Integer(missing=1),
        limit=mm_fields.Integer(missing=100),
    ))
    def get(cls, **kwargs):
        """币币-理财-链上质押排名"""
        asset = kwargs['asset']
        user_id = kwargs.get('id')
        limit = kwargs['limit']
        amount_expression = case([
            (StakingHistory.type == StakingHistory.Type.STAKE, StakingHistory.amount),
            (StakingHistory.type == StakingHistory.Type.UNSTAKE, -StakingHistory.amount)
        ], else_=0)
        balances = StakingHistory.query.filter(
            StakingHistory.asset == asset,
            StakingHistory.status == StakingHistory.Status.FINISHED
        ).with_entities(
            StakingHistory.user_id,
            func.sum(amount_expression).label('total_amount')
        ).group_by(
            StakingHistory.user_id
        ).all()
        balances = [item for item in balances if item.total_amount > 0]
        balances.sort(key=lambda x: x.total_amount, reverse=True)
        total_balance = sum(item.total_amount for item in balances)
        total_balance = quantize_amount(total_balance, PrecisionEnum.COIN_PLACES)
        user_count = len(balances)
        rate = PriceManager.asset_to_usd(asset)
        total_usd = quantize_amount(total_balance * rate, PrecisionEnum.CASH_PLACES)
        balances = balances[:limit]

        records = []
        for idx, item in enumerate(balances, start=1):
            records.append(dict(
                rank=idx,
                user_id=item.user_id,
                balance=item.total_amount
            ))
        if user_id:= kwargs.get('user_id'):
            records = [item for item in records if item['user_id'] == user_id]
        sum_record = StakingHistory.query.filter(
            StakingHistory.asset == asset,
            StakingHistory.type == StakingHistory.Type.UNSTAKE,
            StakingHistory.status == StakingHistory.Status.QUEUED,
        ).with_entities(
            func.count(StakingHistory.user_id.distinct()).label('count'),
            func.sum(StakingHistory.amount).label('amount')
        ).first()
        unstaking_user_count = unstaking_amount = 0
        if sum_record:
            unstaking_user_count = sum_record.count or 0
            unstaking_amount = sum_record.amount or 0
        db_r = StakingAccount.query.filter(
                StakingAccount.status == StakingAccount.Status.OPEN
            )
        asset_list = [i.asset for i in db_r]
        return dict(
            assets=asset_list,
            records=records,
            total_balance=total_balance,
            total_usd=total_usd,
            user_count=user_count,
            unstaking_user_count=unstaking_user_count,
            unstaking_amount=unstaking_amount,
        )


@ns.route('/system-record')
@respond_with_code
class SystemStakingRecordResource(Resource):


    @classmethod
    @ns.use_kwargs(dict(
        asset=mm_fields.String,
        status=EnumField(StakingSyncRecord.Status),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """币币-理财-链上质押赎回记录"""
        query = StakingSyncRecord.query.order_by(StakingSyncRecord.id.desc())
        if asset:= kwargs.get('asset'):
            query = query.filter(StakingSyncRecord.asset == asset)
        if status:= kwargs.get('status'):
            query = query.filter(StakingSyncRecord.status == status)
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        data = []
        for item in pagination.items:
            data.append(dict(
                id=item.id,
                asset=item.asset,
                amount=abs(item.amount),
                type='STAKE' if item.amount > 0 else 'UNSTAKE',
                status=item.status.name,
                created_at=item.created_at,
            ))
        assets = StakingAccount.query.filter(
                StakingAccount.status == StakingAccount.Status.OPEN
        ).with_entities(StakingAccount.asset).all()
        assets = [item.asset for item in assets]
        return dict(
            items=data,
            total=pagination.total,
            status_map={item.name: item.value for item in StakingSyncRecord.Status},
            assets=assets,
        )


@ns.route('/user-record')
@respond_with_code
class UserStakingRecordResource(Resource):


    @classmethod
    @ns.use_kwargs(dict(
        asset=mm_fields.String,
        user_id=mm_fields.Integer,
        status=EnumField(StakingHistory.Status),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """币币-理财-用户质押赎回记录"""
        query = StakingHistory.query.filter(
            StakingHistory.status != StakingHistory.Status.FAILED
        ).order_by(StakingHistory.id.desc())

        if asset:= kwargs.get('asset'):
            query = query.filter(StakingHistory.asset == asset)
        if user_id:= kwargs.get('user_id'):
            query = query.filter(StakingHistory.user_id == user_id)
        if status:= kwargs.get('status'):
            query = query.filter(StakingHistory.status == status)
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        data = []
        for item in pagination.items:
            if item.status == StakingHistory.Status.FINISHED:
                status = item.status.name
            else:
                status = StakingHistory.Status.CREATED.name
            data.append(dict(
                user_id=item.user_id,
                asset=item.asset,
                amount=item.amount,
                created_at=item.created_at,
                type=item.type.name,
                status=status,
            ))
        assets = StakingAccount.query.filter(
                StakingAccount.status == StakingAccount.Status.OPEN
        ).with_entities(StakingAccount.asset).all()
        assets = [item.asset for item in assets]
        return dict(
            items=data,
            total=pagination.total,
            assets=assets,
            status_map={
                StakingHistory.Status.QUEUED.name: '处理中',
                StakingHistory.Status.FINISHED.name: '已完成',
            }
        )
    

@ns.route('/statistics')
@respond_with_code
class UserStakingStatisticsResource(Resource):
    
    @classmethod
    def get(cls):
        """统计-质押统计"""
        assets = StakingAccount.query.filter(
                StakingAccount.status == StakingAccount.Status.OPEN
        ).with_entities(StakingAccount.asset).all()
        assets = [item.asset for item in assets]
        chain_data = []
        user_data = []
        prices = PriceManager.assets_to_usd()

        for asset in assets:
            d = StakingStatisticsCache(asset).read()
            if not d:
                continue
            d = json.loads(d)

            asset_config = try_get_asset_config(asset)
            chain_data.append(
                dict(
                    asset=asset,
                    report_at=timestamp_to_datetime(d['report_at']),
                    income_rate=d["income_rate"],
                    system_staking=d["system_staking"],
                    system_staking_usd=quantize_amount(
                        Decimal(d["system_staking"]) * prices.get(asset, 0), PrecisionEnum.CASH_PLACES),
                    system_effective_amount=d["system_effective_amount"],
                    system_pending_staking=d["system_pending_staking"],
                    system_pending_unstaking=d["system_pending_unstaking"],
                    bufsize=d["bufsize"],
                    target_bufsize=asset_config.staking_bufsize if asset_config else d['target_bufsize'],
                )
            )
            user_data.append(
                dict(
                    asset=asset,
                    report_at=timestamp_to_datetime(d['report_at']),
                    amount=d['total_user_amount'],
                    user_count=d["total_user_count"],
                    effective_user_count=d['effective_user_count'],
                    effective_asset_amount=d['effective_user_amount'],
                    pending_staking_user_count=d['staking_user_count'],
                    pending_staking_asset_amount=d['user_pending_staking'],
                    pending_unstaking_user_count=d['unstaking_user_count'],
                    pending_unstaking_asset_amount=d['user_pending_unstaking'],
                ),
            )
        return dict(
            assets=assets,
            chain_data=chain_data,
            user_data=user_data,
            report_at=user_data[0]['report_at'] if user_data else None,
        )
    

@ns.route('/config')
@respond_with_code
class StakingConfigResource(Resource):

    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        target_bufsize=PositiveDecimalField(required=True)
    ))
    def post(cls, **kwargs):
        """币币-理财-超额质押目标值配置"""
        asset = kwargs['asset']
        target_bufsize = kwargs['target_bufsize']
        get_asset_config(asset).staking_bufsize = target_bufsize