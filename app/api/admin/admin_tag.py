from flask import g, request
from sqlalchemy import func
from webargs import fields as wa_fields

from app import Language
from app.api.common import Namespace, respond_with_code, Resource, fields
from app.business.admin_tag import TagType, AdminTagHelper, handle_admin_tag_task
from app.exceptions import InvalidArgument
from app.models import User, db
from app.models.admin_tag import AdminTagUser, AdminTagCategory
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from app.utils import export_xlsx, datetime_to_str
from app.utils.importer import get_table_rows

ns = Namespace("Admin-Tag")


@ns.route("/category")
@respond_with_code
class AdminTagCategoryResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            page=fields.PageField(unlimited=True),
            limit=fields.LimitField(missing=100),
            tag_id=wa_fields.Integer,
            status=fields.EnumField(AdminTagCategory.Status),
            start=fields.TimestampField,
            end=fields.TimestampField,
        )
    )
    def get(cls, **kwargs):
        """ 用户-特殊配置-用户Tag标签管理-用户标签列表 """
        tag_category_query = AdminTagCategory.query.order_by(
            AdminTagCategory.id.asc()
        )
        if status := kwargs.get('status'):
            tag_category_query = tag_category_query.filter(
                AdminTagCategory.status == status
            )
        if tag_id := kwargs.get('tag_id'):
            tag_category_query = tag_category_query.filter(
                AdminTagCategory.id == tag_id
            )
        if start := kwargs.get('start'):
            tag_category_query = tag_category_query.filter(
                AdminTagCategory.created_at >= start
            )
        if end := kwargs.get('end'):
            tag_category_query = tag_category_query.filter(
                AdminTagCategory.created_at <= end
            )
        page, limit = kwargs["page"], kwargs["limit"]
        tag_category = tag_category_query.paginate(page, limit)
        table_items = []
        tag_user_query = AdminTagUser.query.filter(
            AdminTagUser.tag_id.in_([i.id for i in tag_category.items]),
            AdminTagUser.status == AdminTagUser.Status.PASSED,
        ).group_by(
            AdminTagUser.tag_id
        ).with_entities(
            func.count(AdminTagUser.user_id).label("user_count"),
            AdminTagUser.tag_id,
        ).all()
        tag_user_count_map = {i.tag_id: i.user_count for i in tag_user_query}
        for item in tag_category.items:
            table_item = item.to_dict(enum_to_name=True)
            table_item['user_count'] = tag_user_count_map.get(item.id, 0)
            table_items.append(table_item)

        return dict(
            items=table_items,
            total=tag_category.total,
            extra=dict(
                tag_name_map={k: v[0] for k, v in AdminTagHelper.tag_detail_map.items()},
                editable_black_list=AdminTagHelper.editable_black_list,
                status_dict={
                    AdminTagCategory.Status.PASSED.name: '启用',
                    AdminTagCategory.Status.DELETED.name: '未启用',
                }
            ),
        )
    
    
@ns.route("/category/status")
@respond_with_code
class AdminTagCategoryStatusResource(Resource):


    @classmethod
    @ns.use_kwargs(
        dict(
            tag_id=wa_fields.Integer,
            status=fields.EnumField(AdminTagCategory.Status),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-用户Tag标签管理-用户Tag标签管理-编辑标签状态 """
        tag_id = kwargs['tag_id']
        status = kwargs['status']
        tag_category = AdminTagCategory.query.get(tag_id)
        if not tag_category:
            raise InvalidArgument(message="标签不存在")
        old_data = tag_category.to_dict(enum_to_name=True)
        if status == AdminTagCategory.Status.PASSED:
            tag_user_query = AdminTagUser.query.filter(
                AdminTagUser.tag_id == tag_id,
                AdminTagUser.status == AdminTagUser.Status.DELETED
            ).with_entities(
                AdminTagUser.user_id
            ).all()
            tag_user_ids = [i.user_id for i in tag_user_query]
            for user_id in tag_user_ids:
                handle_admin_tag_task(user_id, g.user.id, status.name, tag_id)
        else:
            tag_user_query = AdminTagUser.query.filter(
                AdminTagUser.tag_id == tag_id,
                AdminTagUser.status == AdminTagUser.Status.PASSED
            ).with_entities(
                AdminTagUser.user_id
            ).all()
            tag_user_ids = [i.user_id for i in tag_user_query]
            for user_id in tag_user_ids:
                handle_admin_tag_task(user_id, g.user.id, status.name, tag_id)
        tag_category.status = status
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AdminTagCategory,
            old_data=old_data,
            new_data=tag_category.to_dict(enum_to_name=True),
        )


@ns.route("/users")
@respond_with_code
class AdminTagUsersResource(Resource):
    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "name_displayed", Language.ZH_HANS_CN: "用户名"},
        {"field": "status", Language.ZH_HANS_CN: "生效状态"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "updated_at_str", Language.ZH_HANS_CN: "操作时间"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "updated_by", Language.ZH_HANS_CN: "操作人"},
    )

    @classmethod
    def get_(cls, **kwargs):
        tag_id = kwargs['tag_id'] if kwargs.get('tag_id') else TagType.INTERNAL_STAFF.value
        tag_users_query = AdminTagUser.query.filter(
            AdminTagUser.tag_id == tag_id,
        ).order_by(
            AdminTagUser.created_at.desc()
        )
        if status := kwargs.get('status'):
            tag_users_query = tag_users_query.filter(
                AdminTagUser.status == status
            )
        if user_id := kwargs.get('user_id'):
            tag_users_query = tag_users_query.filter(
                AdminTagUser.user_id == user_id
            )
        page, limit = kwargs["page"], kwargs["limit"]
        tag_users = tag_users_query.paginate(page, limit)
        table_items = []
        user_ids = [i.user_id for i in tag_users.items]
        user_id_map = {i.id: i for i in User.query.filter(
            User.id.in_(user_ids)
        ).all()}
        for item in tag_users.items:
            table_item = item.to_dict(enum_to_name=True)
            table_item['email'] = user_id_map[item.user_id].email
            table_item['name_displayed'] = user_id_map[item.user_id].name_displayed
            table_item['updated_at_str'] = datetime_to_str(table_item['updated_at'],
                                                           offset_minutes=60 * 8)
            table_items.append(table_item)

        if kwargs.get('export'):
            return export_xlsx(
                filename='admin_tag_user_list',
                data_list=table_items,
                export_headers=cls.export_headers,
            )

        return dict(
            items=table_items,
            total=tag_users.total,
            extra=dict(
                tag_name_map={k: v[0] for k, v in AdminTagHelper.tag_detail_map.items()},
                editable_black_list=AdminTagHelper.editable_black_list,
                status_dict={
                    AdminTagUser.Status.PASSED.name: '生效中',
                    AdminTagUser.Status.DELETED.name: '失效',
                }
            ),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            page=fields.PageField(unlimited=True),
            tag_id=wa_fields.Integer,
            user_id=wa_fields.Integer,
            status=fields.EnumField(AdminTagUser.Status),
            limit=fields.LimitField(missing=100),
        )
    )
    def get(cls, **kwargs):
        """ 用户-特殊配置-用户Tag标签管理-标签用户详情 """
        return cls.get_(**kwargs)

    @classmethod
    @ns.use_kwargs(
        dict(
            tag_id=wa_fields.Integer,
            user_id=wa_fields.Integer,
            remark=wa_fields.String(missing=''),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-用户Tag标签管理-标签用户详情-添加用户 """
        tag_id = kwargs['tag_id']
        tag_category = AdminTagCategory.query.get(tag_id)
        if not tag_category:
            raise InvalidArgument(message="标签不存在")
        if tag_category.status == AdminTagCategory.Status.DELETED:
            raise InvalidArgument(message="标签已失效，请生效后再操作")
        tag_user = AdminTagUser.query.filter(
            AdminTagUser.tag_id == kwargs['tag_id'],
            AdminTagUser.user_id == kwargs['user_id'],
        ).first()
        if tag_user:
            raise InvalidArgument(message="用户已存在标签中")

        handle_admin_tag_task(kwargs['user_id'], g.user.id, AdminTagUser.Status.PASSED.name, tag_id, kwargs['remark'])

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AdminTagUser,
            detail=kwargs,
            target_user_id=kwargs['user_id'],
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            tag_id=wa_fields.Integer,
            user_id=wa_fields.Integer,
            remark=wa_fields.String(missing=''),
        )
    )
    def put(cls, **kwargs):
        """ 用户-特殊配置-用户Tag标签管理-标签用户详情-编辑用户备注 """
        tag_user = AdminTagUser.query.filter(
            AdminTagUser.tag_id == kwargs['tag_id'],
            AdminTagUser.user_id == kwargs['user_id'],
        ).first()
        if not tag_user:
            raise InvalidArgument(message="用户不存在标签中")
        old_data = tag_user.to_dict(enum_to_name=True)
        tag_user.remark = kwargs['remark']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AdminTagUser,
            old_data=old_data,
            new_data=tag_user.to_dict(enum_to_name=True),
            target_user_id=tag_user.user_id,
        )


@ns.route("/users/export")
@respond_with_code
class AdminTagUsersExportResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            page=fields.PageField(unlimited=True),
            tag_id=wa_fields.Integer,
            user_id=wa_fields.Integer,
            status=fields.EnumField(AdminTagUser.Status),
            limit=fields.LimitField(missing=100),
            export=wa_fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """ 用户-特殊配置-用户Tag标签管理-标签用户详情-导出 """
        return AdminTagUsersResource.get_(**kwargs)


@ns.route("/users/status")
@respond_with_code
class AdminTagUsersStatusResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            tag_id=wa_fields.Integer,
            user_id=wa_fields.Integer,
            status=fields.EnumField(AdminTagUser.Status),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-用户Tag标签管理-标签用户详情-编辑用户状态 """
        tag_id = kwargs['tag_id']
        user_id = kwargs['user_id']
        status = kwargs['status']
        tag_category = AdminTagCategory.query.get(tag_id)
        if not tag_category:
            raise InvalidArgument(message="标签不存在")
        if tag_category.status == AdminTagCategory.Status.DELETED:
            raise InvalidArgument(message="标签已失效，请生效后再操作")
        tag_user = AdminTagUser.query.filter(
            AdminTagUser.tag_id == tag_id,
            AdminTagUser.user_id == user_id,
        ).first()
        if not tag_user:
            raise InvalidArgument(message="用户不存在标签中")
        old_data = tag_user.to_dict(enum_to_name=True)
        handle_admin_tag_task(user_id, g.user.id, status.name, tag_id)

        new_tag_user = AdminTagUser.query.filter(
            AdminTagUser.tag_id == tag_id,
            AdminTagUser.user_id == user_id,
        ).first()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AdminTagUser,
            old_data=old_data,
            new_data=new_tag_user.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )


@ns.route("/users/status/batch")
@respond_with_code
class AdminTagUsersStatusBatchResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            tag_id=wa_fields.Integer(required=True),
            ids=wa_fields.List(wa_fields.Integer, required=True),
            status=fields.EnumField(AdminTagUser.Status, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-用户Tag标签管理-标签用户详情-批量生效/失效 """
        tag_id = kwargs['tag_id']
        status = kwargs['status']
        old_status = AdminTagUser.Status.PASSED if status != AdminTagUser.Status.PASSED else AdminTagUser.Status.DELETED
        tag_category = AdminTagCategory.query.get(tag_id)
        if not tag_category:
            raise InvalidArgument(message="标签不存在")
        if tag_category.status == AdminTagCategory.Status.DELETED:
            raise InvalidArgument(message="标签已失效，请生效后再操作")
        tag_user_query = AdminTagUser.query.filter(
            AdminTagUser.tag_id == tag_id,
            AdminTagUser.user_id.in_(kwargs['ids']),
            AdminTagUser.status != status
        ).with_entities(
            AdminTagUser.user_id
        ).all()
        tag_user_ids = [i.user_id for i in tag_user_query]
        for user_id in tag_user_ids:
            handle_admin_tag_task(user_id, g.user.id, status.name, tag_id)

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.AdminTagUser,
                old_data=dict(status=old_status),
                new_data=dict(status=status),
                special_data=dict(tag_id=tag_id, name=tag_category.name),
                target_user_id=user_id,
            )


@ns.route('/users/upload/<int:tag_id>')
@respond_with_code
class AdminTagUsersBatchResource(Resource):

    @classmethod
    def post(cls, tag_id):
        """ 用户-特殊配置-用户Tag标签管理-标签用户详情-上传 """
        if not (file := request.files.get('file')):
            raise InvalidArgument
        tag_category = AdminTagCategory.query.get(tag_id)
        if not tag_category:
            raise InvalidArgument(message="标签不存在")
        if tag_category.status == AdminTagCategory.Status.DELETED:
            raise InvalidArgument(message="标签已失效，请生效后再操作")

        rows = get_table_rows(file, ["email", "remark"])
        emails = [row['email'] for row in rows]
        user_ids_query = User.query.filter(
            User.email.in_(emails)
        ).with_entities(
            User.id,
            User.email,
        ).all()
        user_ids = [i.id for i in user_ids_query]
        user_email_map = {i.id: i.email for i in user_ids_query}
        remark_map = {row['email']: row['remark'] for row in rows}
        exist_user_query = AdminTagUser.query.filter(
            AdminTagUser.user_id.in_(user_ids),
            AdminTagUser.tag_id == tag_id,
        ).with_entities(
            AdminTagUser.user_id
        ).all()
        exist_user_ids = [i.user_id for i in exist_user_query]
        if exist_user_ids:
            error_email = [user_email_map.get(i, '') for i in exist_user_ids]
            raise InvalidArgument(message=f'{error_email}已存在标签中，添加失败')
        for user_id in user_ids:
            user_email = user_email_map[user_id]
            remark = remark_map.get(user_email, None)
            handle_admin_tag_task(user_id, g.user.id, AdminTagUser.Status.PASSED.name, tag_id, remark)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.AdminTagUser,
                detail=dict(tag_id=tag_id, name=tag_category.name, remark=remark),
                target_user_id=user_id,
            )
        return dict(
            total=len(user_ids),
        )
