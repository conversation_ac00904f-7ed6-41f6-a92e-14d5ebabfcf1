# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from itertools import chain
from typing import List, Dict

from flask import g
from sqlalchemy import or_, func
from werkzeug.datastructures import MultiDict
from app.business.auth import get_admin_user_name_map

from app.common.constants import ReportType, ADMIN_EXPORT_LIMIT

from ..common import Resource, Namespace, respond_with_code
from ..common.fields import (
    EnumField, CustomIntegerField, PageField,
    LimitField, mm_fields, TimestampField, EmailField
    )
from ...business import TradeLogDB, PriceManager
from ...business.market_maker import MarketMakerHelper
from ...business.fee import UserFeeParser, update_user_fee_task
from ...business.user import UserRepository
from ...business.user_status import MarketMakerChangeType
from ...business.fee_constant import MARKET_MAKER_DICT

from ...common import TradeType, TradeBusinessType, list_country_codes_3_admin, get_country
from ...exceptions import InvalidArgument
from ...models import (
    MarketMaker, InnerMarketMaker, User, SubAccount, UserTradeSummary, db,
    DailyMakerTradeDetailReport, Language,
    UserStatusChangeHistory, MonthlyMakerTradeDetailReport, AppraisalHistory,
    MarketMakerApplication, MarketMakerReportSendEmail, DailyMakerTradeMarketDetailReport,
    MonthlyMakerTradeMarketDetailReport,
)
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from ...utils import current_timestamp, validate_email, export_xlsx, format_percent, batch_iter, AWSBucketPrivate
from ...utils.date_ import now, today, last_month_range, \
    datetime_to_time, timestamp_to_datetime, datetime_to_utc8_str
from ...utils.helper import Struct
from ...utils.parser import JsonEncoder

ns = Namespace('MarketMaker')


@ns.route('')
@respond_with_code
class MarketMakerResource(Resource):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "maker_fee_rate", Language.ZH_HANS_CN: "maker费率"},
        {"field": "taker_fee_rate", Language.ZH_HANS_CN: "taker费率"},
        {"field": "deal_amount", Language.ZH_HANS_CN: "当月成交量(USD)"},
        {"field": "last_month_deal_amount", Language.ZH_HANS_CN: "上个月成交量(USD)"},
        {"field": "last_month_ranking", Language.ZH_HANS_CN: "上月排名"},
        {"field": "level", Language.ZH_HANS_CN: "等级"},
        {"field": "lock_level", Language.ZH_HANS_CN: "保底等级"},
        {"field": "real_level", Language.ZH_HANS_CN: "考核等级"},
        {"field": "appraisal_type", Language.ZH_HANS_CN: "是否考核"},
        {"field": "expired_time", Language.ZH_HANS_CN: "保底到期时间"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    ORDER_TYPES = {
        'deal_amount': '当月成交量',
        'last_month_deal_amount': '上个月成交量'
    }

    MAKET_MAKER_TO_USER_TYPE_DIC = {
        MarketMaker.MakerType.SPOT: User.UserType.EXTERNAL_SPOT_MAKER,
        MarketMaker.MakerType.PERPETUAL: User.UserType.EXTERNAL_CONTRACT_MAKER,
        'SPOT_AND_PERPETUAL': User.UserType.EXTERNAL_MAKER,
        'NORMAL': User.UserType.NORMAL
    }

    @classmethod
    @ns.use_kwargs(
            dict(
                    search_keyword=mm_fields.String(),
                    maker_type=EnumField(MarketMaker.MakerType,
                                         enum_by_value=True, required=True),
                    level=CustomIntegerField(),
                    order=mm_fields.String(),
                    page=PageField(unlimited=True),
                    limit=LimitField(missing=50),
                    appraisal_type=EnumField(MarketMaker.AppraisalType),
                    export=mm_fields.Boolean,
                    )
            )
    def get(cls, **kwargs):
        """用户-做市商列表"""
        args = Struct(**kwargs)
        query = MarketMaker.query.join(
                User
                ).filter(
                MarketMaker.user_id == User.id,
                MarketMaker.maker_type == args.maker_type,
                MarketMaker.status == MarketMaker.StatusType.PASS).order_by(
                MarketMaker.id.desc())
        if args.appraisal_type:
            query = query.filter(MarketMaker.appraisal_type == args.appraisal_type)
        query = query.with_entities(
                MarketMaker.level,
                MarketMaker.user_id,
                User.name,
                User.email,
                User.user_type,
                MarketMaker.level,
                MarketMaker.real_level,
                MarketMaker.lock_level,
                MarketMaker.is_lock,
                MarketMaker.expired_time,
                MarketMaker.appraisal_type,
                MarketMaker.remark
                )
        export = kwargs.get("export")
        # page_rows = query.paginate(args.page, args.limit, error_out=False)
        records = query.all()

        ids = [item.user_id for item in records]
        ts = current_timestamp(to_int=True)
        sub_query = SubAccount.query.filter(
                SubAccount.main_user_id.in_(ids),
                ).with_entities(
                SubAccount.main_user_id,
                SubAccount.user_id
                )
        sub_account_map = MultiDict([(v.main_user_id, v.user_id) for v in
                                     sub_query])

        sub_user_ids = list(chain(*sub_account_map.listvalues()))
        if len(ids + sub_user_ids) > 0:
            id_str = ','.join([str(v) for v in (ids + sub_user_ids)])
            balance_table = TradeLogDB.slice_balance_table(ts)
            if not balance_table:
                # 避免当前小时取不到快照报错
                balance_table = TradeLogDB.slice_balance_table(ts-3600)
            balance_records = balance_table.select(
                    'user_id', 'asset',
                    'SUM(`balance`) `balance`',
                    where=f"user_id "
                          f"in ({id_str})",
                    group_by="`user_id`, `asset`",
                    )
            balance_dict = defaultdict(Decimal)
            asset_rate = PriceManager.assets_to_usd(PriceManager.list_asset())
            for user_id, asset, balance in balance_records:
                balance_dict[user_id] += asset_rate.get(asset, Decimal()) * \
                Decimal(balance)
        else:
            balance_dict = {}
        if args.maker_type == MarketMaker.MakerType.SPOT:
            system = UserTradeSummary.System.SPOT
        else:
            system = UserTradeSummary.System.PERPETUAL
        today_ = today()
        this_month_first_day = datetime(today_.year, today_.month, 1).date()
        trade_dict = MarketMakerHelper.list_trade_amount(
                ids,
                this_month_first_day,
                today_,
                system
                )
        dt = today()
        start_dt, end_dt = last_month_range(dt)
        last_month_trade_dict = MarketMakerHelper.list_trade_amount(ids, start_dt, end_dt, system)
        s = lambda d, uid: sum(
            [d.get(v, Decimal()) for v in [uid] + sub_account_map.getlist(uid)]
        )
        fee_fetch = lambda x, y: UserFeeParser(x).get_settle_fee(TradeBusinessType(args.maker_type.value))[y]

        user_report_send_emails_map = cls.get_report_send_emails(ids)

        table_data = [
            dict(
                user_id=v.user_id,
                name=v.name,
                user_type=v.user_type,
                email=v.email,
                taker_fee_rate=fee_fetch(v.user_id, TradeType.TAKER),
                maker_fee_rate=fee_fetch(v.user_id, TradeType.MAKER),
                cet_amount=s(balance_dict, v.user_id),
                deal_amount=s(trade_dict, v.user_id),
                last_month_deal_amount=s(last_month_trade_dict, v.user_id),
                level=v.level,
                lock_level=v.lock_level,
                real_level=v.real_level,
                expired_time=v.expired_time,
                appraisal_type=v.appraisal_type.name,
                remark=v.remark,
                report_send_emails=user_report_send_emails_map[v.user_id],
            )
            for v in records
        ]
        # 排序
        order = kwargs.get('order', 'last_month_deal_amount')
        table_data.sort(key=lambda x: x[order], reverse=True)

        if args.search_keyword:
            user_query = User.query
            users = None
            if args.search_keyword.isdigit():
                user_query = user_query.filter(or_(User.id == args.search_keyword,
                                         User.mobile == args.search_keyword))
                users = user_query.with_entities(User.id).all()
            elif validate_email(args.search_keyword):
                user_query = user_query.filter(User.email == args.search_keyword)
                users = user_query.with_entities(User.id).all()
            elif args.search_keyword:
                user_query = user_query.filter(User.email.contains(args.search_keyword))
                users = user_query.with_entities(User.id).all()
            if users:
                user_ids = {u.id for u in users}
                table_data = list(filter(lambda x: x['user_id'] in user_ids, table_data))
        if args.level:
            table_data = list(filter(lambda x: x['level'] == args.level, table_data))

        user_ids = [item['user_id'] for item in table_data]
        history = AppraisalHistory.query.filter(
            AppraisalHistory.user_id.in_(user_ids),
            AppraisalHistory.report_date == this_month_first_day,
            AppraisalHistory.business_type == (AppraisalHistory.BusinessType.SPOT \
                if args.maker_type == MarketMaker.MakerType.SPOT else AppraisalHistory.BusinessType.PERPETUAL)
        ).with_entities(AppraisalHistory.user_id, AppraisalHistory.result).all()
        ranking_map = dict()
        for item in history:
            result = json.loads(item.result)
            ranking_map[item.user_id] = result.get('ranking')
        for item in table_data:
            item['last_month_ranking'] = ranking_map.get(item['user_id'])

        if export:
            for i in table_data:
                i["expired_time"] = i["expired_time"].strftime('%Y-%m-%d %H:%M') if i["expired_time"] else i["expired_time"]
                i["appraisal_type"] = '是' if i["appraisal_type"] == 'APPRAISAL' else '否'

            return export_xlsx(
                filename="market_maker_list",
                data_list=table_data,
                export_headers=cls.export_headers,
            )
        total = len(table_data)
        table_data = table_data[(args.page - 1) * args.limit: args.page * args.limit]
        return dict(
                total=total,
                items=table_data,
                levels=sorted(MARKET_MAKER_DICT.keys()),
                balance_dict=balance_dict,
                trade_dict=trade_dict,
                maker_type_dict={'spot': '现货做市商', 'perpetual': "合约做市商"},
                orders=cls.ORDER_TYPES
                )

    @classmethod
    def get_report_send_emails(cls, user_ids: List[int]) -> Dict[int, List]:
        rows = []
        for ch_user_ids in batch_iter(user_ids, 2000):
            ch_rows = MarketMakerReportSendEmail.query.filter(
                MarketMakerReportSendEmail.user_id.in_(ch_user_ids),
                MarketMakerReportSendEmail.status == MarketMakerReportSendEmail.Status.VALID,
            ).order_by(MarketMakerReportSendEmail.updated_at.desc()).all()
            rows.extend(ch_rows)
        user_report_send_emails = defaultdict(list)
        for r in rows:
            user_report_send_emails[r.user_id].append(r.to_dict())
        return user_report_send_emails

    @classmethod
    @ns.use_kwargs(dict(
            maker_type=EnumField(MarketMaker.MakerType,
                                 enum_by_value=True,
                                 required=True),
            user_id=mm_fields.Integer(required=True),
            real_level=mm_fields.Integer(
                    validate=lambda x: x in set(MARKET_MAKER_DICT.keys()) | {0}, required=True),
            lock_level=mm_fields.Integer(
                    validate=lambda x: x in set(MARKET_MAKER_DICT.keys()) | {0}),
            remark=mm_fields.String(missing='', allow_none=True),
            expired_time=TimestampField(is_ms=True, allow_none=True),
            ))
    def post(cls, **kwargs):
        """用户-添加做市商"""
        admin_user_id = g.user.id
        body = Struct(**kwargs)
        cls._check(body)
        maker = cls._add_and_change_user_type(body)
        cls._log_add(maker, admin_user_id, kwargs)
        update_user_fee_task.delay(maker.user_id)
        return {}

    @classmethod
    def _check(cls, body):
        if not body.expired_time and body.lock_level != 0:
            raise InvalidArgument(message='配置保底等级需要填写过期时间')
        user = User.query.filter(User.id == body.user_id).first()
        if user.is_sub_account:
            raise InvalidArgument(message='不允许添加子账户！')
        if InnerMarketMaker.query.filter(
            InnerMarketMaker.user_id == body.user_id,
            InnerMarketMaker.status == InnerMarketMaker.StatusType.VALID,
        ).first():
            raise InvalidArgument(message=f'用户{body.user_id}是内部做市商, 不允许再添加外部做市商')

    @classmethod
    def _add_and_change_user_type(cls, body):
        maker = MarketMaker.query.filter(
            MarketMaker.user_id == body.user_id,
            MarketMaker.maker_type == body.maker_type
        ).first()
        if maker:
            if maker.status == MarketMaker.StatusType.PASS:
                raise InvalidArgument(message='已经存在该用户')
        else:
            maker = MarketMaker(user_id=body.user_id,
                                maker_type=body.maker_type,
                                )
            db.session.add(maker)
        maker.status = MarketMaker.StatusType.PASS
        maker.remark = body.remark or ''
        maker.real_level = body.real_level

        if body.lock_level:
            maker.is_lock = True
            maker.lock_level = body.lock_level
            maker.expired_time = body.expired_time
            maker.level = max(body.lock_level, body.real_level)
        else:
            maker.is_lock = False
            maker.lock_level = 0
            maker.expired_time = None
            maker.level = body.real_level
        user_type = cls._user_type_after_add(body.user_id, body.maker_type)
        user = User.query.get(body.user_id)
        user.user_type = user_type
        db.session.commit()
        return maker

    @classmethod
    def _user_type_after_add(cls, user_id, maker_type):
        record = MarketMaker.query.filter(
            MarketMaker.user_id == user_id,
            MarketMaker.status == MarketMaker.StatusType.PASS,
            MarketMaker.maker_type != maker_type
        ).first()
        if record:
            user_type = cls.MAKET_MAKER_TO_USER_TYPE_DIC['SPOT_AND_PERPETUAL']
        else:
            user_type = cls.MAKET_MAKER_TO_USER_TYPE_DIC[maker_type]
        return user_type

    @classmethod
    def _log_add(cls, maker, admin_user_id, kwargs):
        logs = []
        if maker.maker_type == MarketMaker.MakerType.SPOT:
            ns_obj = OPNamespaceObjectUser.SpotMarketMaker
            type_ = User.UserType.EXTERNAL_SPOT_MAKER.name
        else:
            ns_obj = OPNamespaceObjectUser.PerpetualMarketMaker
            type_ = User.UserType.EXTERNAL_CONTRACT_MAKER.name
        AdminOperationLog.new_add(
            user_id=admin_user_id,
            ns_obj=ns_obj,
            detail=kwargs,
            target_user_id=maker.user_id,
        )

        add_log = UserStatusChangeHistory(
            user_id=maker.user_id,
            type=type_,
            action=MarketMakerChangeType.ADD.name,
            detail=json.dumps(dict(
                maker_type=type_,
                old_level=None,
                new_level=maker.level,
            )),
            admin_user_id=admin_user_id
        )
        logs.append(add_log)
        if maker.is_lock:
            lock_level_log = UserStatusChangeHistory(
                user_id=maker.user_id,
                type=type_,
                action=MarketMakerChangeType.ADJUST_LOCK_LEVEL.name,
                detail=json.dumps(dict(
                    maker_type=type_,
                    old_level=None,
                    new_level=maker.lock_level,
                    expired_time=datetime_to_time(maker.expired_time)
                )),
                admin_user_id=admin_user_id
            )
            logs.append(lock_level_log)
        db.session.bulk_save_objects(logs)
        db.session.commit()

    @classmethod
    @ns.use_kwargs(dict(
        maker_type=EnumField(MarketMaker.MakerType, enum_by_value=True,
                             required=True),
        user_id=mm_fields.Integer(required=True),
        remark=mm_fields.String(allow_none=True),
    ))
    def delete(cls, **kwargs):
        """用户-删除做市商"""
        admin_user_id = g.user.id
        body = Struct(**kwargs)
        old_level = cls._delete_and_change_user_type(body)
        cls._log_delete(body, admin_user_id, old_level, kwargs)
        update_user_fee_task.delay(body.user_id)
        return {}

    @classmethod
    def _delete_and_change_user_type(cls, body):
        maker = MarketMaker.query.filter(
            MarketMaker.user_id == body.user_id,
            MarketMaker.maker_type == body.maker_type,
            MarketMaker.status == MarketMaker.StatusType.PASS
        ).first()
        if not maker:
            raise InvalidArgument('用户身份不存在')
        old_level = maker.level or 0
        maker.status = MarketMaker.StatusType.DELETE
        maker.level = 0
        maker.real_level = 0
        maker.remark = body.remark or ''
        maker.is_lock = False
        maker.lock_level = 0
        maker.expired_time = None
        user_type = cls._user_type_after_del(body.user_id, body.maker_type)
        user = User.query.get(body.user_id)
        user.user_type = user_type
        db.session.commit()
        return old_level

    @classmethod
    def _user_type_after_del(cls, user_id, maker_type):
        record = MarketMaker.query.filter(
            MarketMaker.user_id == user_id,
            MarketMaker.status == MarketMaker.StatusType.PASS,
            MarketMaker.maker_type != maker_type
        ).first()
        if record:
            user_type = cls.MAKET_MAKER_TO_USER_TYPE_DIC[record.maker_type]
        else:
            user_type = cls.MAKET_MAKER_TO_USER_TYPE_DIC['NORMAL']
        return user_type

    @classmethod
    def _log_delete(cls, body, admin_user_id, old_level, kwargs):
        logs = []
        if body.maker_type == MarketMaker.MakerType.SPOT:
            ns_obj = OPNamespaceObjectUser.SpotMarketMaker
            type_ = User.UserType.EXTERNAL_SPOT_MAKER.name
        else:
            ns_obj = OPNamespaceObjectUser.PerpetualMarketMaker
            type_ = User.UserType.EXTERNAL_CONTRACT_MAKER.name

        AdminOperationLog.new_delete(
            user_id=admin_user_id,
            ns_obj=ns_obj,
            detail=kwargs,
            target_user_id=body.user_id,
        )

        delete_log = UserStatusChangeHistory(
            user_id=body.user_id,
            type=type_,
            action=MarketMakerChangeType.REMOVE.name,
            detail=json.dumps(dict(
                maker_type=type_,
                old_level=old_level,
                new_level=None,
            )),
            admin_user_id=admin_user_id
        )
        logs.append(delete_log)
        db.session.bulk_save_objects(logs)
        db.session.commit()

    @classmethod
    @ns.use_kwargs(dict(
            maker_type=EnumField(MarketMaker.MakerType, enum_by_value=True,
                                 required=True),
            user_id=mm_fields.Integer(required=True),
            remark=mm_fields.String(allow_none=True),
            lock_level=mm_fields.Integer(
                    validate=lambda x: x in set(MARKET_MAKER_DICT.keys()) | {0}),
            expired_time=TimestampField(is_ms=True, allow_none=True),
            appraisal_type=EnumField(MarketMaker.AppraisalType, required=True)
            ))
    def patch(cls, **kwargs):
        """用户-编辑做市商"""
        admin_user_id = g.user.id
        body = Struct(**kwargs)

        if not body.expired_time and body.lock_level != 0:
            raise InvalidArgument(message='配置保底等级需要填写过期时间')
        maker = MarketMaker.query.filter(
                MarketMaker.user_id == body.user_id,
                MarketMaker.maker_type == body.maker_type
                ).first()
        old_data = maker.to_dict(enum_to_name=True)
        has_lock_level_before = bool(maker.expired_time)
        prev_lock_level = maker.lock_level
        prev_appraisal_type = maker.appraisal_type
        prev_expired_time = maker.expired_time

        maker.remark = body.remark or ''
        maker.is_lock = True
        maker.lock_level = body.lock_level
        if prev_appraisal_type != body.appraisal_type and \
             body.appraisal_type == MarketMaker.AppraisalType.NO_APPRAISAL:
            maker.real_level = 1
        if body.lock_level is not None and \
            body.expired_time and body.expired_time > now():
            maker.level = max(body.lock_level, maker.real_level)
        maker.expired_time = body.expired_time
        maker.appraisal_type = body.appraisal_type
        maker.status = MarketMaker.StatusType.PASS
        db.session.commit()

        # logging
        ns_obj = OPNamespaceObjectUser.PerpetualMarketMaker
        if body.maker_type == MarketMaker.MakerType.SPOT:
            ns_obj = OPNamespaceObjectUser.SpotMarketMaker
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=ns_obj,
            old_data=old_data,
            new_data=maker.to_dict(enum_to_name=True),
            target_user_id=body.user_id,
        )
        type_ = ''
        if body.maker_type == MarketMaker.MakerType.SPOT:
            type_ = User.UserType.EXTERNAL_SPOT_MAKER.name
        elif body.maker_type == MarketMaker.MakerType.PERPETUAL:
            type_ = User.UserType.EXTERNAL_CONTRACT_MAKER.name

        if prev_lock_level != maker.lock_level or \
            (body.expired_time and prev_expired_time != body.expired_time):
            db.session_add_and_commit(UserStatusChangeHistory(
                user_id=body.user_id,
                type=type_,
                action=MarketMakerChangeType.ADJUST_LOCK_LEVEL.name,
                detail=json.dumps(dict(
                    old_level=None if not has_lock_level_before else prev_lock_level,
                    new_level=body.lock_level,
                    expired_time=datetime_to_time(body.expired_time) if body.expired_time else None
                )),
                admin_user_id=admin_user_id
            ))
        if prev_appraisal_type != body.appraisal_type:
            db.session_add_and_commit(UserStatusChangeHistory(
                user_id=body.user_id,
                type=type_,
                action=MarketMakerChangeType.ADJUST_APPRAISAL_TYPE.name,
                detail=json.dumps(dict(
                    old_appraisal_type=prev_appraisal_type.name,
                    new_appraisal_type=body.appraisal_type.name
                )),
                admin_user_id=admin_user_id
            ))

        update_user_fee_task.delay(maker.user_id)

        return {}


@ns.route('/inner-list')
@respond_with_code
class InnerMarketMakerResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=mm_fields.Integer,
            page=PageField(unlimited=True),
            limit=LimitField(missing=50),
            export=mm_fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """用户-内部做市商-内部做市商列表"""
        query = InnerMarketMaker.query.filter(
            InnerMarketMaker.status == InnerMarketMaker.StatusType.VALID,
        ).order_by(InnerMarketMaker.created_at.desc())
        if user_id := kwargs.get("user_id"):
            query = query.filter(InnerMarketMaker.user_id == user_id)

        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total, rows = pagination.total, pagination.items

        user_ids = [i.user_id for i in rows]
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 2000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        op_user_ids = list({i.updated_by for i in rows if i.updated_by})
        admin_user_name_map = get_admin_user_name_map(op_user_ids)

        items = []
        for r in rows:
            item = r.to_dict()
            user_fee_fetcher = UserFeeParser(r.user_id)
            spot_fees = user_fee_fetcher.get_settle_fee(TradeBusinessType.SPOT)
            per_fees = user_fee_fetcher.get_settle_fee(TradeBusinessType.PERPETUAL)
            item["spot_maker_fee"] = spot_fees[TradeType.MAKER]
            item["spot_taker_fee"] = spot_fees[TradeType.TAKER]
            item["perpetual_maker_fee"] = per_fees[TradeType.MAKER]
            item["perpetual_taker_fee"] = per_fees[TradeType.TAKER]
            item["email"] = user_email_map.get(r.user_id)
            item["updated_by_name"] = admin_user_name_map.get(r.updated_by)
            items.append(item)

        return dict(
            total=total,
            items=items,
            extra={},
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(required=True),
        remark=mm_fields.String(allow_none=True, missing=''),
    ))
    def post(cls, **kwargs):
        """用户-内部做市商-添加内部做市商"""
        admin_user_id = g.user.id
        user_id = kwargs["user_id"]
        cls.do_add(admin_user_id, user_id, kwargs.get("remark"))
        update_user_fee_task(user_id)
        return {}

    @classmethod
    def do_add(cls, admin_user_id: int, user_id: int, remark: str = None):
        cls._check(user_id)
        cls._add_and_change_user_type(admin_user_id, user_id, remark)
        cls._log_add(admin_user_id, user_id, remark)

    @classmethod
    def _check(cls, user_id: int):
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(message='用户不存在')
        if user.is_sub_account:
            raise InvalidArgument(message='不允许添加子账户！')
        if MarketMaker.query.filter(
            MarketMaker.user_id == user_id,
            MarketMaker.status == MarketMaker.StatusType.PASS,
        ).first():
            raise InvalidArgument(message=f'用户{user_id}是外部做市商, 不允许再添加内部做市商')

    @classmethod
    def _add_and_change_user_type(cls, admin_user_id: int, user_id: int, remark: str = None):
        inner_maker: InnerMarketMaker = InnerMarketMaker.query.filter(
            InnerMarketMaker.user_id == user_id,
        ).first()
        if inner_maker and inner_maker.status == InnerMarketMaker.StatusType.VALID:
            raise InvalidArgument(message=f'该用户已经是内部做市商')
        if not inner_maker:
            inner_maker = InnerMarketMaker(user_id=user_id)
        inner_maker.created_at = now()  # for order_by
        inner_maker.status = InnerMarketMaker.StatusType.VALID
        inner_maker.remark = remark or ''
        inner_maker.updated_by = admin_user_id
        db.session.add(inner_maker)

        user = User.query.get(user_id)
        user.user_type = User.UserType.INTERNAL_MAKER
        db.session.commit()
        return inner_maker

    @classmethod
    def _log_add(cls, admin_user_id: int, user_id: int, remark: str = None):
        params = {"remark": remark}
        logs = []
        AdminOperationLog.new_add(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.InnerMarketMaker,
            detail=params,
            target_user_id=user_id,
        )

        user_log = UserStatusChangeHistory(
            user_id=user_id,
            type=User.UserType.INTERNAL_MAKER.name,
            action=MarketMakerChangeType.ADD.name,
            detail=json.dumps(params, cls=JsonEncoder, ensure_ascii=False),
            admin_user_id=admin_user_id
        )
        logs.append(user_log)
        db.session.bulk_save_objects(logs)
        db.session.commit()

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(required=True),
        remark=mm_fields.String(allow_none=True, missing=""),
    ))
    def put(cls, **kwargs):
        """用户-内部做市商-编辑内部做市商"""
        admin_user_id = g.user.id

        user_id = kwargs["user_id"]
        inner_maker: InnerMarketMaker = InnerMarketMaker.query.filter(
            InnerMarketMaker.user_id == user_id,
            InnerMarketMaker.status == InnerMarketMaker.StatusType.VALID,
        ).first()
        if not inner_maker:
            raise InvalidArgument(message='内部做市商不存在')

        old_data = inner_maker.to_dict(enum_to_name=True)

        inner_maker.remark = kwargs.get("remark") or ''
        inner_maker.status = InnerMarketMaker.StatusType.VALID
        inner_maker.updated_by = admin_user_id
        db.session.add(inner_maker)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.InnerMarketMaker,
            old_data=old_data,
            new_data=inner_maker.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )
        update_user_fee_task(inner_maker.user_id)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(required=True),
        remark=mm_fields.String(missing=""),
    ))
    def delete(cls, **kwargs):
        """用户-内部做市商-删除内部做市商"""
        user_id = kwargs["user_id"]
        if cls.do_delete(g.user.id, user_id, kwargs["remark"]):
            update_user_fee_task(user_id)
        return {}

    @classmethod
    def do_delete(cls, admin_user_id, user_id, remark="") -> bool:
        inner_maker = InnerMarketMaker.query.filter(
            InnerMarketMaker.user_id == user_id,
            InnerMarketMaker.status == InnerMarketMaker.StatusType.VALID,
        ).first()
        if not inner_maker:
            return False

        inner_maker.status = InnerMarketMaker.StatusType.DELETED
        inner_maker.remark = remark
        user = User.query.get(user_id)
        user.user_type = User.UserType.NORMAL
        db.session.commit()
        cls._log_delete(admin_user_id, user_id, remark)
        return True

    @classmethod
    def _log_delete(cls, admin_user_id, user_id, remark=""):
        logs = []
        AdminOperationLog.new_delete(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectUser.InnerMarketMaker,
            detail={"remark": remark},
            target_user_id=user_id,
        )

        delete_log = UserStatusChangeHistory(
            user_id=user_id,
            type=User.UserType.INTERNAL_MAKER.name,
            action=MarketMakerChangeType.REMOVE.name,
            detail=json.dumps({}),
            admin_user_id=admin_user_id,
        )
        logs.append(delete_log)
        db.session.bulk_save_objects(logs)
        db.session.commit()


@ns.route('/report-email')
@respond_with_code
class MarketMakerReportEmailResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=mm_fields.Integer(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 用户-做市商-查询做市商日报接收邮箱 """
        user_id = kwargs["user_id"]
        rows = MarketMakerReportSendEmail.query.filter(
            MarketMakerReportSendEmail.user_id == user_id,
            MarketMakerReportSendEmail.status == MarketMakerReportSendEmail.Status.VALID,
        ).order_by(MarketMakerReportSendEmail.updated_at.desc()).all()
        return [i.to_dict() for i in rows]

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=mm_fields.Integer(required=True),
            email=EmailField(required=True),
            remark=mm_fields.String(missing="", validate=lambda x: len(x) < 32),
        )
    )
    def post(cls, **kwargs):
        """ 用户-做市商-新增做市商日报接收邮箱 """
        user = User.query.get(kwargs["user_id"])
        notice_email = kwargs["email"]
        remark = kwargs.get("remark") or ""
        if notice_email == user.email:
            raise InvalidArgument(message="该邮箱为本人账号")

        cur_num = MarketMakerReportSendEmail.query.filter(
            MarketMakerReportSendEmail.user_id == user.id,
            MarketMakerReportSendEmail.status == MarketMakerReportSendEmail.Status.VALID,
        ).with_entities(func.count()).scalar() or 0
        max_num = MarketMakerReportSendEmail.MAX_NUM
        if cur_num >= max_num:
            raise InvalidArgument(message=f"最多增加{max_num}个邮箱")

        row: MarketMakerReportSendEmail = MarketMakerReportSendEmail.get_or_create(
            user_id=user.id,
            email=notice_email,
        )
        if row.id and row.status == MarketMakerReportSendEmail.Status.VALID:
            raise InvalidArgument(message="该邮箱已添加")

        row.status = MarketMakerReportSendEmail.Status.VALID
        row.remark = remark
        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.MarketMakerEmail,
            detail=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=mm_fields.Integer(required=True),
            email=EmailField(required=True),
            remark=mm_fields.String(missing="", validate=lambda x: len(x) < 32),
        )
    )
    def put(cls, **kwargs):
        """ 用户-做市商-编辑做市商日报接收邮箱 """
        id_ = kwargs["id"]
        if id_ <= 0:
            raise InvalidArgument

        cur_row = MarketMakerReportSendEmail.query.filter(
            MarketMakerReportSendEmail.id == id_,
            MarketMakerReportSendEmail.status == MarketMakerReportSendEmail.Status.VALID,
        ).first()
        if not cur_row:
            raise InvalidArgument(message="记录不存在")
        old_data = cur_row.to_dict(enum_to_name=True)

        email = kwargs["email"]
        if MarketMakerReportSendEmail.query.filter(
            MarketMakerReportSendEmail.user_id == cur_row.user_id,
            MarketMakerReportSendEmail.id != cur_row.id,
            MarketMakerReportSendEmail.email == email,
        ).first():
            raise InvalidArgument(message=f"邮箱 {email} 已存在")

        cur_row.email = kwargs["email"]
        cur_row.remark = kwargs["remark"]
        db.session.add(cur_row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.MarketMakerEmail,
            old_data=old_data,
            new_data=cur_row.to_dict(enum_to_name=True),
            target_user_id=cur_row.user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=mm_fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 用户-做市商-删除做市商日报接收邮箱 """
        id_ = kwargs["id"]
        if id_ <= 0:
            raise InvalidArgument

        row = MarketMakerReportSendEmail.query.filter(
            MarketMakerReportSendEmail.id == id_,
            MarketMakerReportSendEmail.status == MarketMakerReportSendEmail.Status.VALID,
        ).first()
        if not row:
            raise InvalidArgument

        row.status = MarketMakerReportSendEmail.Status.DELETED
        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.MarketMakerEmail,
            detail=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )


@ns.route('/change-history')
@respond_with_code
class MarketMakerChangeHistoryResource(Resource):

    change_type_map = {
        MarketMakerChangeType.ADD.name: '成为做市商',
        MarketMakerChangeType.REMOVE.name: '取消做市商',
        MarketMakerChangeType.LEVEL_CHANGE.name: '等级变动',
        MarketMakerChangeType.ADJUST_LOCK_LEVEL.name: '调整保底等级',
        MarketMakerChangeType.LOCK_LEVEL_EXPIRED.name: '保底等级到期',
        MarketMakerChangeType.ADJUST_APPRAISAL_TYPE.name: '调整是否考核'
    }

    market_maker_map = {
        User.UserType.EXTERNAL_SPOT_MAKER.name: "现货做市商",
        User.UserType.EXTERNAL_CONTRACT_MAKER.name: "合约做市商",
        User.UserType.INTERNAL_MAKER.name: "内部做市商"
    }

    market_maker_types = (
        User.UserType.INTERNAL_MAKER, User.UserType.EXTERNAL_MAKER,
        User.UserType.EXTERNAL_SPOT_MAKER, User.UserType.EXTERNAL_CONTRACT_MAKER)

    @classmethod
    @ns.use_kwargs(
            dict(
                    search_keyword=mm_fields.String,
                    maker_type=mm_fields.String(required=True),
                    change_type=EnumField(MarketMakerChangeType),
                    start_date=mm_fields.Date,
                    end_date=mm_fields.Date,
                    page=PageField(unlimited=True),
                    limit=LimitField(missing=50),
                )
            )
    def get(cls, **kwargs):
        """
        用户-做市商变更记录
        """
        page, limit = kwargs['page'], kwargs['limit']
        query = UserStatusChangeHistory.query.order_by(UserStatusChangeHistory.id.desc())
        if not (maker_type := kwargs.get('maker_type')):
            query = query.filter(
                UserStatusChangeHistory.type.in_([item.name for item in cls.market_maker_types])
            )
        else:
            query = query.filter(
                UserStatusChangeHistory.type == maker_type
            )
        if change_type := kwargs.get('change_type'):
            query = query.filter(
                UserStatusChangeHistory.action == change_type.name
            )
        if start_date := kwargs.get('start_date'):
            query = query.filter(
                UserStatusChangeHistory.created_at >= start_date
            )
        if end_date := kwargs.get('end_date'):
            query = query.filter(
                UserStatusChangeHistory.created_at <= end_date + timedelta(days=1)
            )

        if search_keyword := kwargs.get('search_keyword', '').strip():
            keyword_results = User.search_for_users(search_keyword)
            query = query.filter(
                UserStatusChangeHistory.user_id.in_(keyword_results)
            )
        records = query.paginate(page, limit, error_out=False)
        user_ids = {item.user_id for item in records.items}
        user_ids |= {item.admin_user_id for item in records.items if item.admin_user_id }
        user_emails = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(User.id, User.email).all()
        user_email_map = dict(user_emails)
        name_map = get_admin_user_name_map(user_ids)
        res = []
        for item in records.items:
            item: UserStatusChangeHistory
            details = json.loads(item.detail)
            detail_string = ''
            if expired_time := details.get('expired_time'):
                expired_time = timestamp_to_datetime(int(expired_time)).strftime('%Y-%m-%d %H:%M:%S')
            if item.action == MarketMakerChangeType.ADD.name:
                if item.type == User.UserType.INTERNAL_MAKER.name:
                    detail_string = f'成为{cls.market_maker_map[item.type]}'
                else:
                    detail_string = f'成为{cls.market_maker_map[item.type]}LV{details["new_level"]}'
            elif item.action == MarketMakerChangeType.REMOVE.name:
                if item.type == User.UserType.INTERNAL_MAKER.name:
                    detail_string = f'取消{cls.market_maker_map[item.type]}'
                else:
                    detail_string = f'取消{cls.market_maker_map[item.type]}LV{details["old_level"]}'
            elif item.action == MarketMakerChangeType.LEVEL_CHANGE.name:
                detail_string = f'LV{details["old_level"]}变为LV{details["new_level"]}'
            elif item.action == MarketMakerChangeType.ADJUST_LOCK_LEVEL.name:
                if not details['old_level'] and details['new_level'] is not None:
                    detail_string = f'设置保底等级LV{details["new_level"]}，到期时间为{expired_time}'
                elif details['old_level'] is not None \
                    and details['new_level'] is not None \
                        and details['expired_time'] is not None:
                    if details['old_level'] == details['new_level']:
                        detail_string = f'修改保底等级到期时间为{expired_time}'
                    else:
                        detail_string = f'保底等级从LV{details["old_level"]}调整到LV{details["new_level"]}, 到期时间为{expired_time}'
                elif details['expired_time'] is None:
                    detail_string = f'取消保底等级LV{details["old_level"]}'
            elif item.action == MarketMakerChangeType.LOCK_LEVEL_EXPIRED.name:
                detail_string = f'LV{details["new_level"]}保底等级到期'
            elif item.action == MarketMakerChangeType.ADJUST_APPRAISAL_TYPE.name:
                if details['new_appraisal_type'] == MarketMaker.AppraisalType.APPRAISAL.name:
                    detail_string = '不需考核变为需考核'
                else:
                    detail_string = '需考核变为不需考核'
            res.append(dict(
                created_at=item.created_at,
                user_id=item.user_id,
                email=user_email_map.get(item.user_id),
                change_type=cls.change_type_map[item.action],
                detail=detail_string,
                admin_user_id=item.admin_user_id,
                admin_email=name_map.get(item.admin_user_id) or "系统"
            ))

        if maker_type == User.UserType.INTERNAL_MAKER.name:
            change_types = {
                MarketMakerChangeType.ADD.name: '成为做市商',
                MarketMakerChangeType.REMOVE.name: '取消做市商'
            }
        else:
            change_types = cls.change_type_map

        return dict(
            items=res,
            total=records.total,
            maker_types=cls.market_maker_map,
            change_types=change_types
        )


@ns.route('/deals')
@respond_with_code
class UserTradeSummaryRank(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "maker_type", Language.ZH_HANS_CN: "账户类型"},
        {"field": "maker_amount", Language.ZH_HANS_CN: "Maker成交市值"},
        {"field": "account_maker_amount_percent", Language.ZH_HANS_CN: "Maker账户占比"},
        {"field": "maker_amount_percent", Language.ZH_HANS_CN: "Maker全站占比"},
        {"field": "taker_amount", Language.ZH_HANS_CN: "Taker成交市值"},
        {"field": "account_taker_amount_percent", Language.ZH_HANS_CN: "Taker账户占比"},
        {"field": "taker_amount_percent", Language.ZH_HANS_CN: "Taker全站占比"},
        {"field": "trade_amount", Language.ZH_HANS_CN: "成交总市值"},
        {"field": "trade_amount_percent", Language.ZH_HANS_CN: "总市值全站占比"},
        {"field": "fee_amount", Language.ZH_HANS_CN: "手续费市值"},
        {"field": "fee_amount_percent", Language.ZH_HANS_CN: "手续费全站占比"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        id=mm_fields.Integer(),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        system=mm_fields.String(required=True),
        limit=LimitField(missing=100),
        maker_type=mm_fields.String,
        report_type=EnumField(ReportType, required=True),
        export=mm_fields.Boolean,
        search_keyword=mm_fields.String(),
    ))
    def get(cls, **kwargs):
        """做市商-做市商交易记录"""
        args = Struct(**kwargs)
        if args.report_type == ReportType.DAILY:
            model = DailyMakerTradeDetailReport
        else:
            model = MonthlyMakerTradeDetailReport
        system = getattr(model.System, args.system)

        report_query = model.query.join(User).filter(
            model.user_id == User.id,
            model.system == system,
        ).order_by(model.report_date.desc())

        if args.start_date:
            report_query = report_query.filter(
                model.report_date >= args.start_date)
        if args.end_date:
            report_query = report_query.filter(
                model.report_date <= args.end_date)
        if args.maker_type:
            maker_type = getattr(model.MakerType, args.maker_type)
            report_query = report_query.filter(
                model.maker_type == maker_type,
            )
        if args.search_keyword:
            keyword_results = User.search_for_users(args.search_keyword)
            report_query = report_query.filter(
                model.user_id.in_(keyword_results)
            )

        records = []
        if kwargs.get('export'):
            result = report_query.limit(ADMIN_EXPORT_LIMIT).all()
            for item in result:
                tmp = item.to_dict()
                tmp['maker_type'] = tmp['maker_type'].value
                tmp['email'] = item.user.email
                tmp['account_maker_amount_percent'] = format_percent(tmp['maker_amount'] / tmp['trade_amount'], 4)
                tmp['account_taker_amount_percent'] = format_percent(tmp['taker_amount'] / tmp['trade_amount'], 4)
                records.append(tmp)

            return export_xlsx(
                filename='market_maker_detail_list',
                data_list=records,
                export_headers=cls.export_headers
            )
        paginate = report_query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        result = paginate.items

        for item in result:
            tmp = item.to_dict()
            tmp['maker_type'] = tmp['maker_type'].value
            tmp['email'] = item.user.email
            tmp['account_maker_amount_percent'] = tmp['maker_amount'] / tmp['trade_amount']
            tmp['account_taker_amount_percent'] = tmp['taker_amount'] / tmp['trade_amount']
            records.append(tmp)

        return dict(
            records=records,
            page=paginate.page,
            total=paginate.total,
            maker_types={
               e.name: e.value for e in model.MakerType
            },
        )


@ns.route('/market/deals')
@respond_with_code
class MarketMakerMarketDealsResource(Resource):

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "maker_amount", Language.ZH_HANS_CN: "Maker成交市值"},
        {"field": "maker_amount_percent", Language.ZH_HANS_CN: "Maker全站占比"},
        {"field": "taker_amount", Language.ZH_HANS_CN: "Taker成交市值"},
        {"field": "taker_amount_percent", Language.ZH_HANS_CN: "Taker全站占比"},
        {"field": "trade_amount", Language.ZH_HANS_CN: "总成交市值"},
        {"field": "trade_amount_percent", Language.ZH_HANS_CN: "总市值全站占比"},
        {"field": "fee_amount", Language.ZH_HANS_CN: "手续费市值"},
        {"field": "fee_amount_percent", Language.ZH_HANS_CN: "手续费全站占比"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        system=mm_fields.String(required=True),
        report_type=EnumField(ReportType, required=True),
        market=mm_fields.String(),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        user_id=mm_fields.Integer(),
        search_keyword=mm_fields.String(),
        page=PageField(unlimited=True),
        limit=LimitField(missing=100),
        export=mm_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """做市商-市场交易记录"""
        args = Struct(**kwargs)
        if args.report_type == ReportType.DAILY:
            model = DailyMakerTradeMarketDetailReport
        else:
            model = MonthlyMakerTradeMarketDetailReport
        system = getattr(model.System, args.system)
        report_query = model.query.filter(
            model.system == system,
        ).order_by(model.report_date.desc())
        if args.market:
            report_query = report_query.filter(model.market == args.market)
        if args.user_id:
            report_query = report_query.filter(model.user_id == args.user_id)
        if args.start_date:
            report_query = report_query.filter(
                model.report_date >= args.start_date)
        if args.end_date:
            report_query = report_query.filter(
                model.report_date <= args.end_date)
        if args.search_keyword:
            keyword_results = User.search_for_users(args.search_keyword)
            report_query = report_query.filter(
                model.user_id.in_(keyword_results)
            )
        records = []
        if kwargs.get('export'):
            result = report_query.limit(ADMIN_EXPORT_LIMIT).all()
            user_ids = {i.user_id for i in result}
            user_id_email_dic = UserRepository.get_users_id_email_map(user_ids)
            for item in result:
                tmp = cls._fmt_record(item, user_id_email_dic)
                records.append(tmp)
            return export_xlsx(
                filename='market_maker_market_detail_list',
                data_list=records,
                export_headers=cls.export_headers
            )
        paginate = report_query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        result = paginate.items
        user_ids = {i.user_id for i in result}
        user_id_email_dic = UserRepository.get_users_id_email_map(user_ids)
        for item in result:
            tmp = cls._fmt_record(item, user_id_email_dic)
            records.append(tmp)
        markets_query = model.query.filter(
            model.system == system
        ).with_entities(
            model.market.distinct()
        ).all()
        markets = [i[0] for i in markets_query]
        return dict(
            records=records,
            page=paginate.page,
            total=paginate.total,
            markets=markets
        )

    @classmethod
    def _fmt_record(cls, record, user_id_email_dic):
        tmp = record.to_dict()
        tmp['email'] = user_id_email_dic[tmp['user_id']]
        tmp['trade_amount_percent'] = format_percent(
            tmp['trade_amount'] / tmp['total_trade_amount'] if tmp[
                'total_trade_amount'] else 0)
        tmp['taker_amount_percent'] = format_percent(
            tmp['taker_amount'] / tmp['total_taker_amount'] if tmp[
                'total_taker_amount'] else 0)
        tmp['maker_amount_percent'] = format_percent(
            tmp['maker_amount'] / tmp['total_maker_amount'] if tmp[
                'total_maker_amount'] else 0)
        tmp['fee_amount_percent'] = format_percent(
            tmp['fee_amount'] / tmp['total_fee_amount'] if tmp['total_fee_amount'] else 0)
        return tmp


@ns.route('/application-list')
@respond_with_code
class MarketMakerApplyListResource(Resource):

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "记录ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "country_cn_name", Language.ZH_HANS_CN: "用户地区"},
        {"field": "name", Language.ZH_HANS_CN: "团队姓名"},
        {"field": "contact_name", Language.ZH_HANS_CN: "联系人"},
        {"field": "contact_info", Language.ZH_HANS_CN: "联系方式"},
        {"field": "type", Language.ZH_HANS_CN: "申请类型"},
        {"field": "created_at", Language.ZH_HANS_CN: "申请时间"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer,
        location_code=mm_fields.String,
        type=EnumField(MarketMakerApplication.Type),
        status=EnumField(MarketMakerApplication.Status),
        page=PageField(unlimited=True),
        limit=LimitField,
        export=mm_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """做市商申请列表"""
        model = MarketMakerApplication
        query = model.query.order_by(model.id.desc())
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if location_code := kwargs.get("location_code"):
            query = query.join(User).filter(
                model.user_id == User.id,
                User.location_code == location_code
            )
        if type_ := kwargs.get("type"):
            query = query.filter(model.type == type_)
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)

        export = kwargs.get("export")
        if export:
            rows = query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(rows)
        else:
            page_rows = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
            total = page_rows.total
            rows = page_rows.items

        user_map = {}
        user_ids = {i.user_id for i in rows}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(
                User.id.in_(ids_),
            ).with_entities(User.id, User.email, User.location_code).all()
            user_map.update({i.id: i for i in chunk_users})

        if export:
            records = []
            for item in rows:
                tmp = item.to_dict()
                tmp['type'] = tmp['type'].value
                user = user_map[item.user_id]
                tmp["email"] = user.email
                tmp["country_cn_name"] = c.cn_name if (c := get_country(user.location_code)) else '其他'
                tmp["created_at"] = datetime_to_utc8_str(tmp["created_at"])
                records.append(tmp)
            return export_xlsx(
                filename='market_maker_application_list',
                data_list=records,
                export_headers=cls.export_headers
            )

        items = []
        for row in rows:
            d = row.to_dict(enum_to_name=True)
            user = user_map[row.user_id]
            d["extra_info"] = json.loads(d["extra_info"])
            d.update(d["extra_info"])
            d["email"] = user.email
            d["country_cn_name"] = c.cn_name if (c := get_country(user.location_code)) else '其他'
            items.append(d)

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict={i.name: i.value for i in model.Status},
                type_dict={i.name: i.value for i in model.Type},
                source_dict={i.name: i.value for i in model.Source},
                countries={code: get_country(code).cn_name for code in list_country_codes_3_admin()}
            ),
        )


@ns.route('/application/<int:id_>')
@respond_with_code
class MarketMakerApplyDetailResource(Resource):
    @classmethod
    def get(cls, id_):
        """做市商申请详情"""
        row = MarketMakerApplication.query.get(id_)
        user = User.query.get(row.user_id)
        detail = row.to_dict(enum_to_name=True)
        detail["extra_info"] = json.loads(detail["extra_info"])
        detail.update(detail["extra_info"])

        image_keys = [
            "trade_volume_images",
        ]
        for _key in image_keys:
            nk = f"{_key}_urls"
            detail[nk] = [AWSBucketPrivate.get_file_url(s3_key) for s3_key in detail[_key]]
        detail["email"] = user.email
        return detail

    @classmethod
    @ns.use_kwargs(
        dict(
            remark=mm_fields.String,
        )
    )
    def put(cls, id_, **kwargs):
        """做市商申请编辑备注"""
        row: MarketMakerApplication = MarketMakerApplication.query.get(id_)
        old_remark = row.remark
        if (remark := kwargs.get("remark")) is not None:
            row.remark = remark

        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.MarketMakerApply,
            old_data=dict(remark=old_remark),
            new_data=dict(remark=remark),
            target_user_id=row.user_id,
        )
