# -*- coding: utf-8 -*-
import datetime
import json
from enum import Enum
from collections import defaultdict
from random import sample
from webargs import fields
from typing import Dict, List

from flask import g
from sqlalchemy import or_

from app.business.user import get_user_remark
from app.models import (
    db,
    User,
    UserRiskScreen,
    UserRiskScreenRequest,
    UserRiskScreenCase,
    UserRiskScreenCaseResult,
    DowjonesProfileInfo,
    RefinitivProfileInfo,
    KycVerification,
    KycVerificationHistory,
)
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectRisk
from app.exceptions import InvalidArgument
from app.business.risk_screen import RefinitivScreenClient, RiskScreenBusiness
from app.business.kyc import KycBusiness
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import LimitField, PageField, EnumField, TimestampField, DateField
from app.common.countries import get_country
from app.caches.risk_screen import RiskScreenAuditorsCache
from app.utils import now, today


ns = Namespace("Risk-Screen-Admin")


@ns.route("/individual/cases")
@respond_with_code
class IndividualCaseListResource(Resource):

    AUDIT_TYPE_DICT = dict(
        AUTO_PASSED="自动审核",
        MANUAL_AUDIT="人工审核",
    )

    @classmethod
    def get_iso3_country_dict(cls):
        from app.common.countries import _ISO3_TO_COUNTRY

        return {k: f"{k}  {v.cn_name}" for k, v in _ISO3_TO_COUNTRY.items()}

    @classmethod
    @ns.use_kwargs(
        dict(
            start=TimestampField,
            end=TimestampField,
            user_id=fields.Integer,
            country=fields.String,
            status=EnumField(UserRiskScreenCase.Status),
            rating=EnumField(UserRiskScreenCase.Rating),
            audit_type=EnumField(["AUTO_PASSED", "MANUAL_AUDIT"]),
            rand=fields.Boolean(missing=False),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 运营>风险筛查>个人列表 """
        model = UserRiskScreenCase
        q = model.query.filter(
            model.entity_type == model.EntityType.INDIVIDUAL.name,
        ).order_by(model.id.desc())
        if country := kwargs.get("country"):
            q = q.filter(model.country == country)
        if user_id := kwargs.get("user_id"):
            q = q.filter(model.user_id == user_id)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        if rating := kwargs.get("rating"):
            q = q.filter(model.rating == rating)
        if start := kwargs.get("start"):
            q = q.filter(model.created_at >= start)
        if end := kwargs.get("end"):
            q = q.filter(model.created_at < end)
        if audit_type := kwargs.get("audit_type"):
            if audit_type == "MANUAL_AUDIT":
                q = q.filter(
                    or_(
                        model.status == model.Status.AUDIT_REQUIRED,  # 待审核
                        model.auditor_id.isnot(None),  # 有审核人
                    )
                )
            elif audit_type == "AUTO_PASSED":
                # 自动审核已通过的（无风险results）
                q = q.filter(
                    model.auditor_id.is_(None),
                    model.status == model.Status.PASSED,
                )

        cols = [
            model.id,
            model.created_at,
            model.user_id,
            model.entity_type,
            model.case_id,
            model.rating,
            model.rating_remark,
            model.status,
            model.name,
            model.country,
            model.gender,
            model.date_of_birth,
            model.auditor_id,
            model.remark,
        ]
        q = q.with_entities(*cols)

        admin_user_id = g.user.id
        if kwargs["rand"]:
            pagination = q.paginate(kwargs["page"], 200, error_out=False)
            total = pagination.total
            rows = pagination.items
            k = min(len(rows), 10)
            rows = sample(rows, k=k)
            rand_rows = []
            for r in rows:
                # 避免多个审核账号同时审核同一条
                cache = RiskScreenAuditorsCache(r["id"])
                if [i for i in cache.auditors if i != admin_user_id]:
                    continue
                else:
                    cache.add_auditor(admin_user_id)
                    rand_rows.append(r)
                    rows = [r]
                if len(rand_rows) >= 2:
                    rows = rand_rows
                    break

        else:
            pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
            total = pagination.total
            rows = pagination.items

        user_ids = [r.user_id for r in rows]
        user_email_rows = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email).all()
        user_email_map = dict(user_email_rows)
        screen_source_row = (
            UserRiskScreenRequest.query.filter(UserRiskScreenRequest.user_id.in_(user_ids))
            .with_entities(UserRiskScreenRequest.user_id, UserRiskScreenRequest.source)
            .order_by(UserRiskScreenRequest.id.asc())
            .all()
        )
        screen_source_map = dict(screen_source_row)

        items = []
        for row in rows:
            item = {}
            for c in cols:
                val = getattr(row, c.name)
                if isinstance(val, Enum):
                    val = val.name
                item[c.name] = val
            item["user_email"] = user_email_map.get(row.user_id)
            screen_source = screen_source_map.get(row.user_id)
            item["source"] = screen_source.name if screen_source else ""
            item["remark"] = item["remark"] or (screen_source.value if screen_source else "")

            audit_type = ""
            if row.auditor_id is None and row.status == model.Status.PASSED:
                audit_type = "AUTO_PASSED"
            elif row.auditor_id or row.status == model.Status.AUDIT_REQUIRED:
                audit_type = "MANUAL_AUDIT"
            item["audit_type"] = audit_type

            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict={i.name: i.value for i in model.Status},
                rating_dict={i.name: i.value for i in model.Rating},
                gender_dict={i.name: i.value for i in model.Gender},
                source_dict={i.name: i.value for i in UserRiskScreenRequest.Source},
                country_dict=cls.get_iso3_country_dict(),
                audit_type_dict=cls.AUDIT_TYPE_DICT,
            ),
        )


@ns.route("/individual/screen-result")
@respond_with_code
class IndividualCasecreenResultResource(Resource):
    @classmethod
    def build_group_resolution_tools(cls) -> Dict:
        group_row = RefinitivScreenClient.get_group_row()
        kits = json.loads(group_row.resolution_toolkits)
        fields_ = kits["resolution_fields"]
        return {
            "resolution_status_dict": fields_["status_dict"],
            "resolution_risk_dict": fields_["risk_dict"],
            "resolution_reason_dict": fields_["reason_dict"],
            "resolution_rule_dict": kits["resolution_rules"],
        }

    @classmethod
    def build_dowjones_profile_detail(cls, profile_data: Dict) -> Dict:

        def _format_primary_name(name_detail_):
            surname_ = name_detail_.get("primary_name", {}).get("surname", "")
            name_list_ = []
            if first_name_ := name_detail_.get("primary_name", {}).get("first_name", ""):
                name_list_.append(first_name_)
            if middle_name_ := name_detail_.get("primary_name", {}).get("middle_name", ""):
                name_list_.append(middle_name_)
            f_name_list_ = []
            if surname_:
                f_name_list_.append(surname_)
            if name_list_:
                f_name_list_.append(" ".join(name_list_))
            return ", ".join(f_name_list_)

        def _format_date(date_):
            li_ = []
            if year := date_.get("year"):
                li_.append(str(year))
            month_ = date_.get("month")
            day_ = date_.get("day")
            if month_ and day_:
                li_.append(str(month_))
                li_.append(str(day_))
            return "-".join(li_)

        def _format_role(role_detail_):
            category_type_ = role_detail_.get("category_type")
            title_ = role_detail_.get("title", "") or "-"
            since_ = _format_date(role_detail_.get("since", {})) or "-"
            to_ = _format_date(role_detail_.get("to", {})) or "-"
            return "类别：" + category_type_ + "，职务：" + title_ + "，自：" + since_ + "，至：" + to_

        def _format_address(address_detail_):
            lines_ = []
            country_code_ = address_detail_.get("country", {}).get("iso_alpha3")
            country_ = get_country(country_code_) if country_code_ else ""
            if country_:
                lines_.append("国家/地区：" + country_.cn_name)
            if region_ := address_detail_.get("region"):
                lines_.append("州/地区：" + region_)
            if sub_region_ := address_detail_.get("sub_region"):
                lines_.append("次级地区：" + sub_region_)
            if city_ := address_detail_.get("city"):
                lines_.append("城市：" + city_)
            if address_ := address_detail_.get("address"):
                lines_.append("地址：" + address_)
            return "，".join(lines_)

        def _get_profile_url(profile_id_):
            return "https://eu.riskcenter.dowjones.com/search/profile?id=" + str(profile_id_)

        def _get_article_url(drn_):
            return "https://eu.riskcenter.dowjones.com/search/article?ref=" + drn_

        attributes = profile_data.get("data", {}).get("attributes", {})
        basic = attributes.get("basic", {})
        type_ = basic.get("type")
        primary_name, gender = "", ""
        date_of_births = []
        identification = {}
        nationalities = []
        if type_ == "Person":
            primary_name = _format_primary_name(basic.get("name_details", {}))
            identification = attributes.get("person", {})
            gender = identification.get("gender", "")
            birth = identification.get("date_details", {}).get("birth", [])
            for v in birth:
                date_of_births.append(_format_date(v.get("date", {})))
            citizenship = identification.get("country_territory_details", {}).get("citizenship", [])
            nationality_codes = {}
            for city_data in citizenship:
                if iso_alpha3 := city_data.get("iso_alpha3"):
                    nationality_codes[iso_alpha3] = city_data.get("code")
            nationalities = [dict(code=code, name=name) for code, name in nationality_codes.items()]
        elif type_ == "Entity":
            identification = attributes.get("entity", {})
        icon_hints = identification.get("icon_hints", [])
        keywords = [v["icon_hint"] for v in icon_hints]

        relationship = attributes.get("relationship", {})
        associates = []
        for associate in relationship.get("connection_details") or []:
            a_icon_hints = associate.get("icon_hints", [])
            a_keywords = [dict(name=v["icon_hint"], status=v["status"]) for v in a_icon_hints]  # status: Active Inactive
            associates.append(
                {
                    "profile_id": associate.get("profile_id"),
                    "profile_url": _get_profile_url(associate.get("profile_id", "")),
                    "entity_type": associate.get("type"),
                    "associate_type": associate.get("connection_type"),
                    "target_primary_name": associate.get("name_detail", {}).get("full_name"),
                    "keywords": a_keywords,
                }
            )

        further_infos = defaultdict(list)
        places_of_birth = identification.get("places_of_birth", [])
        f_places_of_birth = [_format_address(i) for i in places_of_birth]
        if f_places_of_birth:
            further_infos["出生地"].append(
                {
                    "type": "出生地",
                    "title": '',
                    "text": "\n".join(f_places_of_birth),
                }
            )
        identification_details = identification.get("identification_details", [])
        identification_details = [i.get('type', '') + "  -  " + i.get('value', '') for i in identification_details]
        if identification_details:
            further_infos["ID号码"].append(
                {
                    "type": "ID号码",
                    "title": '',
                    "text": "\n".join(identification_details),
                }
            )

        images = attributes.get("image", {}).get("image_details", [])
        image_links = [i.get('external_url') for i in images if i.get('external_url')]
        if image_links:
            further_infos["图片"].append(
                {
                    "type": "图片",
                    "title": '',
                    "text": "\n".join(image_links),
                }
            )
        watch_list = attributes.get("watchlist", {})
        role_details = watch_list.get("role_details", {})
        if role_details:
            if primary := role_details.get("primary", {}):
                text = _format_role(primary)
                further_infos["职务"].append(
                    {
                        "type": "职务",
                        "title": "主要职业",
                        "text": text,
                    }
                )
            if other := role_details.get("other", []):
                other_roles = []
                for v in other:
                    other_roles.append(_format_role(v))
                further_infos["职务"].append(
                    {
                        "type": "职务",
                        "title": "其他职务",
                        "text": "\n".join(other_roles),
                    }
                )
            if previous := role_details.get("previous", []):
                previous_roles = []
                for v in previous:
                    previous_roles.append(_format_role(v))
                further_infos["职务"].append(
                    {
                        "type": "职务",
                        "title": "简历",
                        "text": "\n".join(previous_roles),
                    }
                )

        address_details = watch_list.get("address_details", [])
        f_address_details = [_format_address(v) for v in address_details]
        if f_address_details:
            further_infos["地址"].append(
                {
                    "type": "地址",
                    "title": '',
                    "text": "\n".join(f_address_details),
                }
            )

        reported_allegation_details = watch_list.get("reported_allegation_details", [])
        f_reported_allegation_details = []
        for v in reported_allegation_details:
            case_row_details = v.get("case_row_details", [])
            lines = []
            for d in case_row_details:
                if content_type := d.get("content_type"):
                    lines.append("报道中的相关指控：" + content_type)
                reported_allegation_source_details = d.get("reported_allegation_source_details", [])
                for s in reported_allegation_source_details:
                    if s.get("drn"):
                        lines.append("资讯来源名：" + s.get("name", "")
                                     + "，来源日期：" + _format_date(s.get("date", ""))
                                     + "，资讯链接：" + _get_article_url(s.get("drn", "")))
            f_reported_allegation_details.append("\n".join(lines))
        for i, v in enumerate(f_reported_allegation_details, start=1):
            further_infos["需特别关注人物"].append(
                {
                    "type": "需特别关注人物",
                    "title": "项目" + str(i),
                    "text": v,
                }
            )

        sources = watch_list.get("sources", [])
        for s in sources:
            if s.get("external_link"):
                further_infos["资料来源链接"].append(
                    {
                        "type": "资料来源链接",
                        "title": s.get("title"),
                        "text": s.get("external_link"),
                    }
                )
            if s.get("drn"):
                further_infos["资料来源链接"].append(
                    {
                        "type": "资料来源链接",
                        "title": s.get("title"),
                        "text": _get_article_url(s.get("drn", "")),
                    }
                )
        comment_details = watch_list.get("comment_details", {})
        for k, v in comment_details.items():
            further_infos["详情内容"].append(
                {
                    "type": "详情内容",
                    "title": k,
                    "text": v,
                }
            )

        detail = {
            "primary_name": primary_name,
            "gender": gender.upper(),
            "date_of_births": date_of_births,
            "nationalities": nationalities,
            "keywords": keywords,
            "further_infos": further_infos,
            "associates": associates,
        }

        return detail

    @classmethod
    def build_refinitiv_profile_detail(cls, profile_data: Dict) -> Dict:
        entity_type = profile_data["entityType"]
        if entity_type not in ["INDIVIDUAL", "ORGANISATION"]:
            raise ValueError(f"不支持的entityType: {entity_type}")

        # same fields
        names = []
        primary_name = ""
        for name_ in profile_data["names"]:
            names.append(
                {
                    "full_name": name_["fullName"],
                    "type": name_["type"],
                }
            )
            if name_["type"] == "PRIMARY":
                primary_name = name_["fullName"]

        modified_at = None
        modification_date = profile_data["modificationDate"]
        if modification_date:
            modified_at = datetime.datetime.strptime(modification_date, "%Y-%m-%dT%H:%M:%SZ")

        detail = {
            "primary_name": primary_name,
            "names": names,
            "modified_at": modified_at,
        }
        if entity_type == "INDIVIDUAL":
            # 国籍，多个
            nationalities = []
            country_links = profile_data.get("countryLinks") or []
            for country_link in country_links:
                if country_link["type"] == "NATIONALITY":
                    _country = country_link["country"]
                    nationalities.append({"code": _country["code"], "name": _country["name"]})

            # 出生日期，多个
            date_of_births = []
            for event in profile_data.get("events") or []:
                if event["type"] == "BIRTH":
                    date_of_births.append(event["fullDate"])

            identity_documents = []
            for doc in profile_data.get("identity_documents") or []:
                if doc["type"] == "Passport":
                    number = doc["number"]
                    country = doc["locationType"]["country"]["code"]
                    identity_documents.append({"country": country, "number": number, "type": "Passport"})

            keywords = []
            for source in profile_data.get("sources") or []:
                if source["abbreviation"] not in SHOW_KEY_WORDS:
                    continue
                keywords.append(
                    {
                        "abbreviation": source["abbreviation"],
                        "name": source["name"],
                        "category": source["type"].get("category", {}).get("name", ""),
                        "description": source["name"],
                        "region_of_authority": source["regionOfAuthority"],
                    }
                )

            associates = []
            for associate in profile_data.get("associates") or []:
                associates.append(
                    {
                        "profile_id": associate["targetEntityId"],
                        "entity_type": associate["entityType"],
                        "category": associate["category"],
                        "associate_type": associate["type"],
                        "target_categories": associate["targetCategories"],
                        "target_primary_name": associate["targetPrimaryName"],
                    }
                )

            urls = []
            for link in profile_data.get("weblinks") or []:
                urls.append(link["uri"])

            further_infos = defaultdict(list)
            for info in profile_data.get("details") or []:
                type_ = info["detailType"]
                further_infos[type_].append(
                    {
                        "type": type_,
                        "title": info["title"],
                        "text": info["text"],
                    }
                )

            detail.update(
                {
                    "nationalities": nationalities,
                    "gender": profile_data["gender"],
                    "date_of_births": date_of_births,
                    "category": profile_data["category"],
                    "identity_documents": identity_documents,
                    "keywords": keywords,
                    "associates": associates,
                    "urls": urls,
                    "further_infos": further_infos,
                }
            )
        else:
            register_country = {"code": "", "name": ""}
            country_links = profile_data["countryLinks"]
            for country_link in country_links:
                if country_link["type"] == "REGISTEREDIN":
                    _country = country_link["country"]
                    register_country = {"code": _country["code"], "name": _country["name"]}
                    break

            detail.update(
                {
                    "register_country": register_country,
                }
            )

        return detail

    @classmethod
    def build_screen_result(cls, idx, screen_result_row, profile_row, third_party):
        w = screen_result_row.to_dict(enum_to_name=True)
        w["index"] = idx
        profile_meta = profile_detail = {}
        if profile_row:
            profile_meta = profile_row.to_dict()
            profile_meta.pop("detail", None)
        if third_party == UserRiskScreenRequest.ThirdParty.Refinitiv:
            if profile_row and profile_row.detail and (d := json.loads(profile_row.detail)):
                profile_detail = cls.build_refinitiv_profile_detail(d)
            w["keywords"] = []
            keywords = profile_detail.get("keywords") or []
            for k in keywords:
                w["keywords"].append(k["abbreviation"])
        else:
            if profile_row and profile_row.detail and (d := json.loads(profile_row.detail)):
                profile_detail = cls.build_dowjones_profile_detail(d)
            w["keywords"] = profile_detail.get("keywords") or []
        w["profile_meta"] = profile_meta
        w["primary_name"] = profile_detail.get("primary_name")
        w["gender"] = profile_detail.get("gender")
        w["category"] = profile_detail.get("category")
        w["date_of_births"] = profile_detail.get("date_of_births") or []
        w["identity_documents"] = profile_detail.get("identity_documents") or []
        w["nationalities"] = profile_detail.get("nationalities") or [{"code": "UNKNOWN", "name": "未知"}]
        w["further_infos"] = profile_detail.get("further_infos") or []
        w["associates"] = profile_detail.get("associates") or []

        return w

    @classmethod
    def get_doc_name(cls, kyc_verification: KycVerification) -> str:
        """从KycVerificationHistory获取第三方返回的姓名"""
        gt_created_at = kyc_verification.created_at - datetime.timedelta(seconds=KycBusiness.PROCESS_EXPIRE_TIME)
        history = KycVerificationHistory.query.filter(
            KycVerificationHistory.created_at > gt_created_at,
            KycVerificationHistory.user_id == kyc_verification.user_id,
        ).all()
        for item in history:
            presets = json.loads(item.detail)
            if presets.get('kyc_id') == kyc_verification.id:
                return presets.get('doc_name', '')
        return ''

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 运营>风险筛查>查看个人筛查详情 """
        case: UserRiskScreenCase = UserRiskScreenCase.query.get(kwargs["id"])
        screen_result_rows = (
            UserRiskScreenCaseResult.query.filter(
                UserRiskScreenCaseResult.case_id == case.case_id,
            )
            .order_by(UserRiskScreenCaseResult.match_score.desc())
            .all()
        )
        profile_ids = [r.profile_id for r in screen_result_rows]
        third_party = case.third_party
        profile_info_model = RefinitivProfileInfo if third_party == UserRiskScreenRequest.ThirdParty.Refinitiv else DowjonesProfileInfo
        profile_rows = profile_info_model.query.filter(profile_info_model.profile_id.in_(profile_ids)).all()
        profile_map = {i.profile_id: i for i in profile_rows}

        kyc_row: KycVerification = KycVerification.query.filter(
            KycVerification.user_id == case.user_id,
        ).order_by(KycVerification.id.desc()).first()
        kyc_info = {
            "id_type": "",
            "id_number": "",
            "front_img_url": "",
            "back_img_url": "",
            "doc_name": "",
        }
        if kyc_row:
            kyc_info = {
                "id_type": kyc_row.id_type.value,
                "id_number": kyc_row.id_number,
                "front_img_url": file.private_url if (file := kyc_row.front_img_file) is not None else "",
                "back_img_url": file.private_url if (file := kyc_row.back_img_file) is not None else "",
                "doc_name": cls.get_doc_name(kyc_row),
            }

        user_ids = [case.user_id]
        if case.auditor_id:
            user_ids.append(case.auditor_id)
        user_email_rows = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email).all()
        user_email_map = dict(user_email_rows)

        screen_result_list = []
        for idx, screen_result in enumerate(screen_result_rows, 1):
            profile = profile_map.get(screen_result.profile_id)
            w = cls.build_screen_result(idx, screen_result, profile, third_party)
            screen_result_list.append(w)

        status_priority_map = {
            UserRiskScreenCaseResult.MatchStatus.UNSPECIFIED.name: 1,
            UserRiskScreenCaseResult.MatchStatus.POSITIVE.name: 2,
            UserRiskScreenCaseResult.MatchStatus.POSSIBLE.name: 3,
            UserRiskScreenCaseResult.MatchStatus.FALSE.name: 4,
        }
        screen_result_list.sort(key=lambda x: status_priority_map.get(x["match_status"], 0))

        group_resolution_tools = cls.build_group_resolution_tools()
        user = User.query.get(case.user_id)
        data = {
            **case.to_dict(enum_to_name=True),
            "kyc_info": kyc_info,
            "country": case.country,
            "gender": case.gender.name if case.gender else "",
            "date_of_birth": case.date_of_birth.strftime("%Y-%m-%d") if case.date_of_birth else None,
            "user_email": user_email_map.get(case.user_id),
            "user_remark": get_user_remark(user),
            "auditor_email": user_email_map.get(case.auditor_id),
            "screen_result_list": screen_result_list,
            "custom_reject_reason": case.rejection_reason if case.is_custom_reason else "",
            "rejection_reasons": {i.name: i.value for i in UserRiskScreenCase.RejectionReason},
            "country_dict": IndividualCaseListResource.get_iso3_country_dict(),
            "rating_dict": {i.name: i.value for i in UserRiskScreenCase.Rating},
            "status_dict": {i.name: i.value for i in UserRiskScreenCase.Status},
            "gender_dict": {i.name: i.value for i in UserRiskScreenCase.Gender},
            "match_status_dict": {i.name: i.value for i in UserRiskScreenCaseResult.MatchStatus},
            "risk_dict": {i.name: i.value for i in UserRiskScreenCaseResult.Risk},
            **group_resolution_tools,
        }
        return data


@ns.route("/individual/screen-result/sync-profile")
@respond_with_code
class IndividualCasecreenSyncProfileResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>风险筛查>同步匹配风险信息 """
        case: UserRiskScreenCase = UserRiskScreenCase.query.get(kwargs["id"])
        num = RiskScreenBusiness.sync_invalid_profiles(case)
        return {"nums": num}


@ns.route("/individual/case-edit/<int:case_row_id>")
@respond_with_code
class IndividualCaseEditResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            country=fields.String,
            name=fields.String,
            gender=EnumField(UserRiskScreenCase.Gender),
            date_of_birth=DateField(to_date=True, allow_none=True),
            sync_screen_result=fields.Boolean,
            force_sync_screen_result=fields.Boolean,
            remark=fields.String,
        )
    )
    def put(cls, case_row_id, **kwargs):
        """ 运营>风险筛查>个人详情-修改个人筛查信息 """
        from app.schedules.kyc import process_risk_screen_request_task

        case: UserRiskScreenCase = UserRiskScreenCase.query.get(case_row_id)
        old_data = case.to_dict(enum_to_name=True)

        if (remark := kwargs.get("remark")) is not None:
            # 单独修改备注
            old_remark = case.remark
            case.remark = remark
            db.session.commit()
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.IndividualCase,
                old_data=dict(remark=old_remark),
                new_data=dict(remark=remark),
                target_user_id=case.user_id,
            )
            return

        sync_screen_result = kwargs.get("sync_screen_result")
        force_sync_screen_result = kwargs.get("force_sync_screen_result")
        if sync_screen_result or force_sync_screen_result:
            return cls.sync_screen_result(case, force_sync_screen_result)

        if case.status in [
            UserRiskScreenCase.Status.PASSED,
            UserRiskScreenCase.Status.RISKED,
        ]:
            raise InvalidArgument(message="状态已审核无法修改")
        if name := kwargs.get("name"):
            case.name = name
        if country := kwargs.get("country"):
            country_dict = IndividualCaseListResource.get_iso3_country_dict()
            if country not in country_dict:
                raise InvalidArgument(message="国家ISO码未找到")
            case.country = country
        case.gender = kwargs.get("gender")
        dob = kwargs.get("date_of_birth")
        if dob and dob >= today():
            raise InvalidArgument(message="出生日期不能超过今天")
        case.date_of_birth = dob

        changed = True
        if changed:
            req = UserRiskScreenRequest.query.get(case.request_id)
            old_req_data = req.to_dict(enum_to_name=True)
            info = json.loads(req.info)
            if name:
                info["name"] = name
            if country:
                info["country"] = country
            info["gender"] = case.gender.name if case.gender else None
            info["date_of_birth"] = case.date_of_birth.strftime("%Y-%m-%d") if case.date_of_birth else None
            req.info = json.dumps(info)
            req.status = UserRiskScreenRequest.Status.PROCESSING

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.IndividualCase,
                old_data=old_req_data,
                new_data=req.to_dict(enum_to_name=True),
                target_user_id=req.user_id,
            )
        db.session.commit()

        if changed:
            process_risk_screen_request_task.delay(case.request_id)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.IndividualCase,
            old_data=old_data,
            new_data=case.to_dict(enum_to_name=True),
            target_user_id=case.user_id,
        )

    @classmethod
    def sync_screen_result(cls, case: UserRiskScreenCase, force: bool):
        """ 同步重新筛查的结果 """
        from app.schedules.kyc import process_risk_screen_request_task

        is_ready = False
        # noinspection PyBroadException
        try:
            is_ready = RefinitivScreenClient.case_rescreen_result_is_ready(case)
        except Exception:
            pass
        if is_ready or force:
            req = UserRiskScreenRequest.query.get(case.request_id)
            req.status = UserRiskScreenRequest.Status.PROCESSING
            db.session.commit()
            process_risk_screen_request_task.delay(case.request_id)

        return {"is_ready": is_ready}


@ns.route("/individual/case-audit/<int:case_row_id>")
@respond_with_code
class IndividualCaseAuditResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            audit_status=EnumField(["REJECTED", "PASSED"], required=True),  # 审核是否通过
            rejection_reason=fields.String,
            is_custom_reason=fields.Boolean(missing=False),
            custom_reject_reason=fields.String,
        )
    )
    def post(cls, case_row_id, **kwargs):
        """ 运营>风险筛查>个人详情-审核操作 """
        audit_status = kwargs["audit_status"]
        is_rejected = audit_status == "REJECTED"
        rejection_reason = kwargs.get("rejection_reason")
        is_custom_reason = bool(kwargs.get("is_custom_reason"))
        custom_reject_reason = kwargs.get("custom_reject_reason")
        if is_rejected:
            if is_custom_reason:
                if not custom_reject_reason:
                    raise InvalidArgument(message="缺少自定义的拒绝原因")
            else:
                if not rejection_reason:
                    raise InvalidArgument(message="请选择拒绝原因")
                if rejection_reason not in [i.name for i in UserRiskScreenCase.RejectionReason]:
                    raise InvalidArgument(message="拒绝原因未定义")

        case: UserRiskScreenCase = UserRiskScreenCase.query.get(case_row_id)
        # 和KYC一样 允许重复审核
        # if case.status != UserRiskScreenCase.Status.AUDIT_REQUIRED:
        #     raise InvalidArgument(message=f"只能操作待审核记录")

        screen_result_rows: List[UserRiskScreenCaseResult] = UserRiskScreenCaseResult.query.filter(
            UserRiskScreenCaseResult.case_id == case.case_id,
        ).all()
        to_resolute_profiles = []
        for result in screen_result_rows:
            if not result.resolver_id and not result.match_status:
                to_resolute_profiles.append(result.profile_id)
        if to_resolute_profiles:
            raise InvalidArgument(message=f"还存在匹配风险项未处理：{to_resolute_profiles}")

        now_ = now()
        admin_user_id = g.user.id
        if is_rejected:
            case.status = UserRiskScreenCase.Status.RISKED
            rejection_reason = custom_reject_reason if is_custom_reason else rejection_reason
            case.is_custom_reason = is_custom_reason
            case.rejection_reason = rejection_reason
            user_risk_status = UserRiskScreen.Status.RISKED
        else:
            case.status = UserRiskScreenCase.Status.PASSED
            user_risk_status = UserRiskScreen.Status.PASSED
        case.auditor_id = admin_user_id
        case.audited_at = now_
        RiskScreenBusiness.update_user_risk_status(case.user_id, user_risk_status, op_user_id=g.user.id)

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.IndividualCase,
            detail=dict(id=case.id, status=case.status.name),
            target_user_id=case.user_id,
        )


@ns.route("/individual/case-auditors/<int:case_row_id>")
@respond_with_code
class IndividualCaseAuditorsResource(Resource):
    @classmethod
    @ns.use_kwargs(dict())
    def post(cls, case_row_id):
        """ 运营>风险筛查>个人详情-更新当前审核人 """
        case: UserRiskScreenCase = UserRiskScreenCase.query.get(case_row_id)
        user_id = g.user.id
        cache = RiskScreenAuditorsCache(case_row_id)
        cache.add_auditor(user_id)
        other_auditor_user_ids = [_uid for _uid in cache.auditors if _uid != user_id]
        return dict(
            status=case.status.name,
            other_auditors=dict(User.query.filter(User.id.in_(other_auditor_user_ids)).with_entities(User.id, User.email))
            if other_auditor_user_ids
            else {},
        )


@ns.route("/case-resolution")
@respond_with_code
class CaseResolutionResource(Resource):
    @classmethod
    def check_and_get_resolution_rule(cls, status_id: str, risk_id: str, reason_id: str):
        group_config = RefinitivScreenClient.get_group_config()
        kits = group_config.resolution_toolkits
        fields_ = kits["resolution_fields"]
        resolution_status_dict: Dict = fields_["status_dict"]  # {id: label}, eg: {'5jb6w1hxishw1g8t0tlouplxc': 'POSITIVE'}
        resolution_risk_dict: Dict = fields_["risk_dict"]
        resolution_reason_dict: Dict = fields_["reason_dict"]
        status_text = resolution_status_dict.get(status_id, status_id)
        risk_text = resolution_risk_dict.get(risk_id, risk_id)
        reason_text = resolution_reason_dict.get(reason_id, reason_id)

        """
        rule example
        {'reason_required': True,
         'reasons': ['5jb6w1hxishw1g8t0tlouplx5'],  # reason ids
         'remark_required': False,
         'risks': ['5jb6w1hxishw1g8t0tlouplx9',     # risk ids
                   '5jb6w1hxishw1g8t0tlouplx8',
                   '5jb6w1hxishw1g8t0tlouplxa',
                   '5jb6w1hxishw1g8t0tlouplxb']
        }
        """
        rule_map = kits["resolution_rules"]
        if status_id not in rule_map:
            raise InvalidArgument(message=f"匹配状态: {status_text} 未找到")
        rule = rule_map[status_id]
        if risk_id not in rule["risks"]:
            raise InvalidArgument(message=f"匹配状态: {status_text} 无法选择 风险水平: {risk_text}, 请重新选择")
        if reason_id not in rule["reasons"]:
            raise InvalidArgument(message=f"匹配状态: {status_text} 无法选择 原因: {reason_text}, 请重新选择")
        return status_text, risk_text, reason_text

    @classmethod
    @ns.use_kwargs(
        dict(
            case_id=fields.String(required=True),
            ids=fields.List(fields.Integer, required=True),
            status_id=fields.String(required=True),
            risk_id=fields.String(required=True),
            reason_id=fields.String(required=True),
            remark=fields.String(required=True),
        )
    )
    def put(cls, **kwargs):
        """ 运营>风险筛查>解析操作 """
        case_id = kwargs["case_id"]
        case = UserRiskScreenCase.query.filter(UserRiskScreenCase.case_id == case_id).first()
        if not case:
            raise InvalidArgument
        ids = kwargs["ids"]
        screen_result_rows: List[UserRiskScreenCaseResult] = UserRiskScreenCaseResult.query.filter(
            UserRiskScreenCaseResult.id.in_(ids),
        ).all()
        case_ids = {i.case_id for i in screen_result_rows}
        if len(case_ids) != 1 and list(case_ids)[0] != case_id:
            raise InvalidArgument(message="所选匹配结果必须只属于当前case")

        third_party = case.third_party
        status_id = kwargs["status_id"]
        risk_id = kwargs["risk_id"]
        reason_id = kwargs["reason_id"]
        remark = kwargs["remark"]
        status_text, risk_text, reason_text = cls.check_and_get_resolution_rule(status_id, risk_id, reason_id)

        now_ = now()
        to_resolute_rows = []
        for row in screen_result_rows:
            old_data = row.to_dict(enum_to_name=True)
            row.resolver_id = g.user.id
            row.resolved_at = now_
            row.resolution_remark = remark
            row.match_status = status_text
            row.risk = risk_text
            row.reason = reason_text
            row.resolution_sync_status = UserRiskScreenCaseResult.SyncStatus.NOT_SYNCED
            if row.result_id:
                to_resolute_rows.append(row)

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectRisk.IndividualCase,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
                special_data=dict(case_id=row.case_id, profile_id=row.profile_id),
                target_user_id=case.user_id,
            )

        db.session.commit()

        cls.sync_result_resolution(third_party, to_resolute_rows)

    @classmethod
    def sync_result_resolution(cls, third_party, to_resolute_rows):
        from app.business.risk_screen.refinitiv import sync_refinitiv_case_result_resolution_task

        if to_resolute_rows and third_party == UserRiskScreenRequest.ThirdParty.Refinitiv:
            sync_refinitiv_case_result_resolution_task.delay([i.id for i in to_resolute_rows])


@ns.route("/case-rating")
@respond_with_code
class CaseRatingResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            rating=EnumField(UserRiskScreenCase.Rating, required=True),
            rating_remark=fields.String(required=True),
        )
    )
    def put(cls, **kwargs):
        """ 运营>风险筛查>设置评级 """
        case: UserRiskScreenCase = UserRiskScreenCase.query.get(kwargs["id"])
        old_data = case.to_dict(enum_to_name=True)
        case.rating = kwargs["rating"]
        case.rating_remark = kwargs.get("rating_remark") or ""
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectRisk.IndividualCase,
            old_data=old_data,
            new_data=case.to_dict(enum_to_name=True),
            target_user_id=case.user_id,
        )


# 展示的关键字, 先写死, 后面可能存表
SHOW_KEY_WORDS = ['BDRQ', 'CAALERT', 'CAAMF', 'CAASC', 'CACBSA', 'CACCB', 'CACRA', 'CADA', 'CADL', 'CAEC',
                  'CAFCAA', 'CAFCNB', 'CAFIC', 'CAFSCO', 'CALPS', 'CAMFDA', 'CAMSC', 'CAN-AN', 'CANBSC',
                  'CANEMBARGO', 'CANFACFO', 'CANS', 'CANS-JVCFO', 'CANS-UN1373', 'CANSEMB', 'CANSEMBLR',
                  'CANSEMIR', 'CANSEMNIC', 'CANSEMPRC', 'CANSEMRUS', 'CANSEMRUS-FSR', 'CANSEMSS',
                  'CANSEMSYR', 'CANSEMUKR', 'CANSEMVE', 'CANSEMZ', 'CAOSC', 'CAQCMELCC', 'CARBQ', 'CARENA',
                  'CARQ', 'CASFSC', 'CASQ', 'CATMF', 'CATMX', 'CAUPAC', 'CCC', 'CES', 'CFSEU', 'CSA-ALERT',
                  'CSA-CTO', 'CSA-DP', 'FINTRAC', 'IIROC', 'NSSC', 'OSFI', 'PRPS', 'QCSF', 'RCMP', 'SECBC',
                  'TPS', 'UNSTR', 'YRPS', 'HKEX', 'HKFSC-ALERT', 'HKGAZ', 'HKMA', 'HKMA-AN', 'HKMAW', 'HKSFC',
                  'ICACHK', 'CADSG', 'MAS', 'MAS-AN', 'MAS-TSOFA', 'MASE', 'MASW', 'SGCCS', 'SGCPIB', 'SGIRAS',
                  'SGSGX', 'DISQUALIFIED DIRECTORS', 'DTI-SEC', 'FSA', 'FSA-UFI', 'FSA-UIB', 'FSA-UOF', 'IOMFSC',
                  'IOMFSC-S', 'IOMSO', 'METPOL', 'SFO', 'UK CUSTOMS', 'UKCOLP', 'UKEMBARGO', 'UKGC', 'UKHMRC-TD',
                  'UKHMT', 'UKHMT-AN', 'UKHMT-DU', 'UKHMT-IB', 'UKHMT-NDS', 'UKHO', 'UKHSE', 'UKMHPRA', 'UKNCA',
                  'UKOFCOM', 'UKSANC', 'UKTR', 'ADB', 'AFDB', 'AIIB', 'EBRD', 'ECB', 'ECC', 'EDES', 'ESMA', 'EU',
                  'EU-AN', 'EU-AQ', 'EU-BDI', 'EU-BLR', 'EU-CAR', 'EU-CYBER', 'EU-DPRK', 'EU-DRC', 'EU-EGY', 'EU-GN',
                  'EU-GNB', 'EU-HRTC', 'EU-HRVA', 'EU-IRN', 'EU-IRQ', 'EU-LBY', 'EU-MAL', 'EU-MY', 'EU-NIC', 'EU-PCW',
                  'EU-S', 'EU-SOM', 'EU-SS', 'EU-SYR', 'EU-TA', 'EU-TAF', 'EU-TCO', 'EU-TUN', 'EU-TUR', 'EU-UKR',
                  'EU-UKR2', 'EU-VE', 'EU-YEM', 'EU-ZIM', 'EUAC', 'EUEIB', 'EUEMBARGO', 'EUFSR-RUS', 'EUFSR-SYR',
                  'EUMW', 'EUMY-LTM', 'EUNCJ-TAX', 'EUTB', 'EUTB-BLR', 'EUTB-FYROM', 'EUTB-GN', 'EUTB-ICTY', 'EUTB-LBY',
                  'EUTB-MAL', 'EUTB-MD', 'EUTB-MY', 'EUTB-ZIM', 'FATF', 'FATF-SD', 'IDB', 'INTERPOL', 'ITOCF',
                  'NIB', 'PTCMVM-AJC', 'UN', 'UN-CAR', 'UN-CIAC', 'UN-DPRK', 'UN-DRC', 'UN-LBY', 'UN-MAL', 'UN-S',
                  'UN-SOM', 'UN-SS', 'UN1267', 'UN1737', 'UN1988', 'UN2140', 'UNDP', 'UNEMBARGO', 'UNIR', 'UNOPS',
                  'UNTB', 'UNTB-GNB', 'UNTB-LBY', 'UNTB-MAL', 'WORLD BANK', 'WORLDBANK-ND', 'ADOI', 'BISN',
                  'BISN-CAATSA231', 'BISN-CBW', 'BISN-EIBA', 'BISN-EO12938', 'BISN-EO13382', 'BISN-IRAN',
                  'BISN-IRSY', 'BISN-LME', 'BISN-MIS', 'BISN-NPPA', 'BISN.IR-IQ', 'BPI', 'BPI-PA', 'BPI-SDNT',
                  'BPI-SDNTK', 'BXA', 'BXAENT', 'BXAMEU', 'BXAUNVER', 'CADI', 'CAPTA-561', 'CFTC', 'CFTC-RL',
                  'CIVPEN', 'CORPCA', 'FBI', 'FDIC', 'FDIC-FB', 'FINCEN', 'FINRA', 'FSEL', 'FTO', 'HHS',
                  'HHS-CMP', 'ICE', 'IFSR', 'INKSNA', 'IRAN-HR', 'IRAN-TRA', 'IRAQ.2', 'IRAQ.3', 'IRGC',
                  'ISA', 'ITRSHRA', 'MAGNIT', 'NFA', 'NPWMD', 'NS-CAATSA-RUSSIA', 'NS-CCMC-EO13959', 'NS-CMIC-EO',
                  'NS-IRAN', 'NS-PEESA', 'NS-PLC', 'NYBB', 'NYDFS', 'NYSE', 'NYSID', 'OCCBANK', 'OCCEA', 'OFAC',
                  'OFAC-BDI', 'OFAC-BK', 'OFAC-BLR', 'OFAC-BM-EO', 'OFAC-BM-EO14014', 'OFAC-C', 'OFAC-CAATSA-IRAN',
                  'OFAC-CAATSA-RUSSIA', 'OFAC-CAR', 'OFAC-CYBER2', 'OFAC-DFR', 'OFAC-DPRK', 'OFAC-DPRK2',
                  'OFAC-DPRK3', 'OFAC-DPRK4', 'OFAC-DRC', 'OFAC-EO13622', 'OFAC-EO13645', 'OFAC-EO13846',
                  'OFAC-EO13848', 'OFAC-EO13871', 'OFAC-EO13876', 'OFAC-EO13884', 'OFAC-EO13894',
                  'OFAC-EO13902', 'OFAC-FSEIR', 'OFAC-GLOMAG', 'OFAC-HIFPAA', 'OFAC-HK-EO13936', 'OFAC-HRIT',
                  'OFAC-ICAAE', 'OFAC-ICCP', 'OFAC-IFCA', 'OFAC-IRN', 'OFAC-ISA', 'OFAC-LBN', 'OFAC-LIBYA2',
                  'OFAC-LIBYA3', 'OFAC-MALI', 'OFAC-NIC', 'OFAC-NICHRAA', 'OFAC-NK', 'OFAC-NKSPEA',
                  'OFAC-NONSDN', 'OFAC-PEESA', 'OFAC-RUSSIA-EO', 'OFAC-S', 'OFAC-SOM', 'OFAC-SS', 'OFAC-SY-CAESAR',
                  'OFAC-SYR', 'OFAC-SYR-EO', 'OFAC-TCO', 'OFAC-UKR', 'OFAC-UKR13662', 'OFAC-UKR13685',
                  'OFAC-UKR2', 'OFAC-VE', 'OFAC-VE13850', 'OFAC-YEM', 'OFAC-Z', 'OTS', 'PCAOB', 'PDGS',
                  'SDGT', 'SDNT', 'SDNTK', 'SDT', 'SDTEL', 'SEC', 'SEC-PAUSE', 'UKR-EO13662', 'US MARSHALLS',
                  'US-EO13959', 'US-EO14024-DIR1', 'US-SST', 'USATF', 'USBIS-EV', 'USCBOE', 'USCBP', 'USCBP-WRO',
                  'USCFPB', 'USCME', 'USCRAL', 'USCRL', 'USDA', 'USDEA', 'USDOD-1237', 'USDOD-1260H', 'USDOJ',
                  'USDOJ-EOIR', 'USDOL-EBSA', 'USDOL-OLMS', 'USDTC', 'USDTC-CA', 'USEMBARGO', 'USFDA', 'USFHFA',
                  'USFRB', 'USFTC', 'USHUD', 'USINL', 'USJVNKA', 'USMDOB', 'USNCUA', 'USNDAA-889', 'USPS', 'USSAG',
                  'USSGE', 'USSIGAR', 'USSLEA', 'USSPSC', 'USSS', 'USSS-PD', 'USSSR', 'USTREAS.311', 'WISCONSIN DT']
