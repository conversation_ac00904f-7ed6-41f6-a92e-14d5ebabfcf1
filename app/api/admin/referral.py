# -*- coding: utf-8 -*-
from collections import defaultdict
from enum import Enum

from flask import g
from marshmallow import Schema, EXCLUDE
from sqlalchemy import and_
from webargs import fields
from werkzeug.datastructures import MultiDict

from ...business import CacheLock, LockKeys
from ...business.auth import get_admin_user_name_map
from ...business.referral import ReferralBusiness
from ...models import db, ReferralCopyWriting, ReferralCopyWritingTranslation, ReferralActivityBanner, \
    ReferralActivityBannerContent, AppJumpList, File, User, SpecialReferreeUserRate
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation, \
    OPNamespaceObjectUser
from ...common import Language, language_cn_names, LANGUAGE_NAMES, ADMIN_EXPORT_LIMIT
from ...exceptions import InvalidArgument
from ..common import Namespace, Resource, respond_with_code
from ..common.fields import <PERSON>umField, <PERSON><PERSON>ield, <PERSON>itField, TimestampField, PositiveDecimalField
from ...utils import now, batch_iter, amount_to_str, export_xlsx
from ...utils.helper import Struct

ns = Namespace("Referral")


@ns.route("/copy-writing")
@respond_with_code
class ReferralCopyWritingResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            page=PageField(unlimited=True),
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 运营-推荐返佣管理-推荐文案-列表 """
        query = ReferralCopyWriting.query.order_by(ReferralCopyWriting.rank.asc())
        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        copy_writing_ids = [i.id for i in pagination.items]
        tran_query = ReferralCopyWritingTranslation.query.filter(
            ReferralCopyWritingTranslation.copy_writing_id.in_(copy_writing_ids)
        ).all()
        tran_dict = MultiDict(
            [
                (i.copy_writing_id, dict(content=i.content, lang=i.lang.name, copy_writing_id=i.copy_writing_id))
                for i in tran_query
            ]
        )
        items = []
        for i in pagination.items:
            translations = tran_dict.getlist(i.id)
            missing_langs = {
                                lang.name for lang in ReferralCopyWritingTranslation.LANGUAGES} - \
                            {d['lang'] for d in translations}
            for missing_lang in missing_langs:
                translations.append(dict(content='', lang=missing_lang, copy_writing_id=i.id))
            mapping = dict(
                id=i.id,
                rank=i.rank,
                remark=i.remark,
                status=i.status.name,
                translations=translations,
            )
            items.append(mapping)

        return dict(
            statuses={
                ReferralCopyWriting.Status.VALID.name: "开启",
                ReferralCopyWriting.Status.DELETED.name: "关闭",
            },
            total=pagination.total,
            items=items,
            languages=[v.name for v in ReferralCopyWritingTranslation.LANGUAGES],
            language_trans={
                lang.name: cn_name
                for lang, cn_name in language_cn_names().items()
                if lang in ReferralCopyWritingTranslation.LANGUAGES
            },
        )

    class ContentTransSchema(Schema):
        lang = EnumField(Language, required=True)
        content = fields.String(required=True)
        copy_writing_id = fields.Integer()  # when post: None

        class Meta:
            UNKNOWN = EXCLUDE

    @classmethod
    @ns.use_kwargs(
        dict(
            translations=fields.Nested(ContentTransSchema, many=True, required=True),
            remark=fields.String(required=False, missing=""),
            status=EnumField(ReferralCopyWriting.Status, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营-推荐返佣管理-推荐文案-添加文案 """
        param_trans = kwargs["translations"]
        post_languages = {v["lang"] for v in param_trans}
        if set(ReferralCopyWritingTranslation.LANGUAGES) - post_languages:
            raise InvalidArgument(message="文案翻译缺失，请检查")

        cur_max_rank_cursor = ReferralCopyWriting.query.order_by(ReferralCopyWriting.rank.desc()).first()
        if cur_max_rank_cursor:
            next_rank = cur_max_rank_cursor.rank + 1
        else:
            next_rank = 1
        param_content_trans_dict = {v["lang"]: v["content"] for v in param_trans}
        cn_content = param_content_trans_dict[Language.ZH_HANS_CN]
        copy_writing = ReferralCopyWriting(
            content=cn_content,
            rank=next_rank,
            remark=kwargs.get("remark", ""),
            status=kwargs["status"],
        )
        db.session_add_and_commit(copy_writing)

        trans_objs = [
            ReferralCopyWritingTranslation(
                copy_writing_id=copy_writing.id,
                lang=lang,
                content=content,
            )
            for lang, content in param_content_trans_dict.items()
        ]
        db.session.add_all(trans_objs)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ReferralCopyWriting,
            detail=kwargs,
        )
        return {"id": copy_writing.id}

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            translations=fields.Nested(ContentTransSchema, many=True, required=True),
            remark=fields.String(required=False, missing=""),
            status=EnumField(ReferralCopyWriting.Status, required=True),
        )
    )
    def put(cls, **kwargs):
        """ 运营-推荐返佣管理-推荐文案-编辑文案 """
        copy_writing = ReferralCopyWriting.query.filter(ReferralCopyWriting.id == kwargs["id"]).first()
        if not copy_writing:
            raise InvalidArgument(message="文案不存在")
        old_data = copy_writing.to_dict(enum_to_name=True)
        param_trans = kwargs["translations"]
        post_languages = {v["lang"] for v in param_trans}
        if set(ReferralCopyWritingTranslation.LANGUAGES) - post_languages:
            raise InvalidArgument(message="文案翻译缺失，请检查")

        param_content_trans_dict = {v["lang"]: v["content"] for v in param_trans}
        copy_writing.content = param_content_trans_dict[Language.ZH_HANS_CN]
        copy_writing.remark = kwargs.get("remark", "")
        copy_writing.status = kwargs["status"]

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ReferralCopyWriting,
            old_data=old_data,
            new_data=copy_writing.to_dict(enum_to_name=True),
        )

        trans = ReferralCopyWritingTranslation.query.filter(
            ReferralCopyWritingTranslation.copy_writing_id == copy_writing.id,
        ).all()
        db_trans_dict = {v.lang: v for v in trans}
        # update
        for lang, content in param_content_trans_dict.items():
            if lang in db_trans_dict:
                db_tran = db_trans_dict[lang]
                old_value = db_tran.content
                db_tran.content = content

                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectOperation.ReferralCopyWritingTranslation,
                    old_data=dict(content=old_value),
                    new_data=dict(content=content),
                    special_data=dict(copy_writing_id=db_tran.copy_writing_id, lang=lang),
                )
        # add new tran language
        trans_objs = []
        for lang, content in param_content_trans_dict.items():
            if lang in db_trans_dict:
                continue
            trans_objs.append(ReferralCopyWritingTranslation(
                copy_writing_id=copy_writing.id,
                lang=lang,
                content=content,
            ))

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.ReferralCopyWritingTranslation,
                detail=dict(copy_writing_id=copy_writing.id, lang=lang, content=content),
            )
        db.session.add_all(trans_objs)
        db.session.commit()

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            change=EnumField(["UP", "DOWN"], required=True),
        )
    )
    def patch(cls, **kwargs):
        """ 运营-推荐返佣管理-推荐文案-修改排序 """
        copy_writing = ReferralCopyWriting.query.filter(ReferralCopyWriting.id == kwargs["id"]).first()
        if not copy_writing:
            raise InvalidArgument(message="文案不存在")
        old_data = copy_writing.to_dict(enum_to_name=True)

        param_change = kwargs["change"]
        old_rank = copy_writing.rank
        if param_change == "UP":
            new_rank = copy_writing.rank - 1
            # rank最小为1, new_rank=0时已经是第一了不用处理
            if new_rank > 0:
                change_obj = ReferralCopyWriting.query.filter(ReferralCopyWriting.rank == new_rank).first()
                copy_writing.rank = new_rank
                change_obj.rank = old_rank
                db.session.commit()
        elif param_change == "DOWN":
            new_rank = copy_writing.rank + 1
            last_id = ReferralCopyWriting.query.order_by(ReferralCopyWriting.rank.desc()).first().id
            if last_id != copy_writing.id:
                change_obj = ReferralCopyWriting.query.filter(ReferralCopyWriting.rank == new_rank).first()
                copy_writing.rank = new_rank
                change_obj.rank = old_rank
                db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ReferralCopyWriting,
            old_data=old_data,
            new_data=copy_writing.to_dict(enum_to_name=True),
        )


class ReferralActivityBannerMixin:
    model = ReferralActivityBanner
    content_model = ReferralActivityBannerContent

    class Platform(Enum):
        WEB = 'web'
        APP = 'app'
        ALL = 'all'

    class TransSchema(Schema):
        lang = EnumField(Language, required=True)
        title = fields.String(required=True)
        sub_title = fields.String(required=True)
        copywriting = fields.String(required=False, missing='')

    class ActivateStatuses(Enum):
        pending = '待上架'
        online = '上架中'
        offline = '已下架'

    @classmethod
    def check_field(cls, kwargs, id_=None):
        if not File.query.get(kwargs[f'light_pic_id']) or not File.query.get(kwargs[f'dark_pic_id']):
            raise InvalidArgument(message="图片文件未找到")
        keys = ['web', 'app']
        for key in keys:
            if f"{key}_jump_type" in kwargs:
                jump_obj = AppJumpList.query.get(kwargs[f'{key}_jump_id'])
                if not jump_obj:
                    raise InvalidArgument(message="跳转未找到")
                if jump_obj.jump_type.value != kwargs[f'{key}_jump_type'].value:
                    raise InvalidArgument(message="跳转类型不匹配")

    @classmethod
    def format_item(cls, row: ReferralActivityBanner, trans_map, _now=None, admin_name_map=None, need_pic=None):
        row_dict = row.to_dict(enum_to_name=True)
        row_dict["translations"] = []
        exist_langs = set()
        for tran in trans_map[row.id]:
            exist_langs.add(tran['lang'])
            tmp_dict = dict()
            for field in ["lang", "title", "sub_title", "copywriting"]:
                tmp_dict[field] = tran[field]
            row_dict["translations"].append(tmp_dict)
        for missing_lang in {lang.name for lang in Language} - exist_langs:
            row_dict["translations"].append({
                'lang': missing_lang,
                'title': '',
                'sub_title': '',
                'copywriting': '',
            })
        if admin_name_map:
            row_dict["created_user_email"] = admin_name_map.get(row.created_by)
        if _now:
            row_dict["activate_status"] = cls.get_status_values(row.begin_at, row.end_at, _now)
        if need_pic:
            row_dict["light_pic_url"] = File.query.get(row.light_pic_id).static_url
            row_dict["dark_pic_url"] = File.query.get(row.dark_pic_id).static_url
        row_dict['platform'] = cls.Platform.ALL.name
        if row_dict['web_jump_id'] and row_dict['app_jump_id']:
            row_dict['platform'] = cls.Platform.ALL.name
        if row_dict['web_jump_id'] and not row_dict['app_jump_id']:
            row_dict['platform'] = cls.Platform.WEB.name
        if not row_dict['web_jump_id'] and row_dict['app_jump_id']:
            row_dict['platform'] = cls.Platform.APP.name
        return row_dict

    @classmethod
    def get_status_values(cls, start_t, end_t, now_t):
        if start_t > now_t:
            activate_status = cls.ActivateStatuses.pending.name
        elif end_t < now_t:
            activate_status = cls.ActivateStatuses.offline.name
        else:
            activate_status = cls.ActivateStatuses.online.name
        return activate_status


@ns.route("/activities")
@respond_with_code
class ReferralActivityListResource(ReferralActivityBannerMixin, Resource):
    model = ReferralActivityBanner
    content_model = ReferralActivityBannerContent

    @classmethod
    @ns.use_kwargs(dict(
        display_name=fields.String,
        begin_at=TimestampField(),
        end_at=TimestampField(),
        activate_status=EnumField(ReferralActivityBannerMixin.ActivateStatuses),
        platform=EnumField(ReferralActivityBannerMixin.Platform),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-推荐返佣管理-活动配置列表"""
        _now = now()
        params = Struct(**kwargs)
        query = cls.get_query_by(params, _now)
        records = query.paginate(params.page, params.limit)
        rows = records.items
        create_user_ids = {item.created_by for item in rows}
        row_ids = [i.id for i in rows]
        name_map = get_admin_user_name_map(create_user_ids)
        trans_query = cls.content_model.query.filter(
            cls.content_model.activity_id.in_(row_ids)
        )
        trans_map = defaultdict(list)
        for trans in trans_query:
            trans_map[trans.activity_id].append(trans.to_dict())
        items = [cls.format_item(row, trans_map, _now, name_map, need_pic=True) for row in rows]

        return dict(
            total=records.total,
            items=items,
            extra=dict(
                activate_statuses=cls.ActivateStatuses,
                platforms=cls.Platform
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        begin_at=fields.DateTime(required=True),
        end_at=fields.DateTime(required=True),
        web_jump_type=EnumField(AppJumpList.JumpType, required=False),
        app_jump_type=EnumField(AppJumpList.JumpType, required=False),
        web_jump_id=fields.Integer(required=False),
        app_jump_id=fields.Integer(required=False),
        light_pic_id=fields.Integer(required=True),
        dark_pic_id=fields.Integer(required=True),
        translations=fields.Nested(ReferralActivityBannerMixin.TransSchema, many=True, required=True),
    ))
    def post(cls, **kwargs):
        """运营-推荐返佣管理-活动配置创建"""
        cls.check_field(kwargs)
        last_record: cls.model = cls.model.query.order_by(
            cls.model.sort_id.desc()
        ).first()
        rank = last_record.sort_id if last_record else 0
        translations = kwargs.pop("translations")
        trans_dict = {v["lang"]: v["title"] for v in translations}
        if not kwargs.get("web_jump_type"):
            kwargs["web_jump_type"] = kwargs["web_jump_id"] = None
        if not kwargs.get("app_jump_type"):
            kwargs["app_jump_type"] = kwargs["app_jump_id"] = None
        with CacheLock(LockKeys.referral_activity()):
            db.session.rollback()
            display_name = trans_dict[Language.ZH_HANS_CN]
            obj = cls.model(
                **kwargs,
                created_by=g.user.id,
                display_name=display_name,
                sort_id=rank + 1
            )
            db.session.add(obj)
            db.session.commit()
            trans_objs = [
                cls.content_model(
                    activity_id=obj.id,
                    **trans
                )
                for trans in translations
            ]
            db.session.add_all(trans_objs)
            db.session.commit()

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.ReferralActivity,
                detail=kwargs,
            )
        return {"id": obj.id}

    @classmethod
    def get_query_by(cls, params, _now):
        model = cls.model
        query = model.query.filter(
            model.status == model.Status.VALID
        ).order_by(model.sort_id)
        if display_name := params.display_name:
            query = query.filter(model.display_name.contains(display_name))
        if platform := params.platform:
            if platform is cls.Platform.WEB:
                query = query.filter(
                    model.web_jump_id.isnot(None),
                    model.app_jump_id.is_(None),
                )
            elif platform is cls.Platform.APP:
                query = query.filter(
                    model.app_jump_id.isnot(None),
                    model.web_jump_id.is_(None),
                )
            else:
                query = query.filter(
                    model.app_jump_id.isnot(None),
                    model.web_jump_id.isnot(None),
                )
        if activate_status := params.activate_status:
            if activate_status == cls.ActivateStatuses.pending:
                query = query.filter(model.begin_at > _now)
            elif activate_status == cls.ActivateStatuses.online:
                query = query.filter(and_(
                    model.begin_at <= _now,
                    model.end_at >= _now)
                )
            else:
                query = query.filter(model.end_at < _now)
        if params.begin_at:
            query = query.filter(model.begin_at >= params.begin_at)
        if params.end_at:
            query = query.filter(model.end_at <= params.end_at)
        return query


@ns.route('/activity/<int:id_>')
@respond_with_code
class ReferralActivityResource(ReferralActivityBannerMixin, Resource):

    @classmethod
    def get(cls, id_):
        """运营-推荐返佣管理-活动配置详情"""
        extra = dict(
            jump_types=AppJumpList.JumpType,
            langs=[v.name for v in Language],
            lang_trans={lang.name: tran.chinese for lang, tran in LANGUAGE_NAMES.items() if lang in Language},
        )
        if id_ == 0:
            return dict(extra=extra)
        if (row := cls.model.query.get(id_)) is None:
            raise InvalidArgument
        trans_query = cls.content_model.query.filter(
            cls.content_model.activity_id == row.id
        )
        trans = {row.id: [i.to_dict(enum_to_name=True) for i in trans_query]}
        item = cls.format_item(row, trans, _now=now(), need_pic=True)
        item.update(
            extra=extra,
        )
        return item

    @classmethod
    @ns.use_kwargs(dict(
        begin_at=fields.DateTime(required=True),
        end_at=fields.DateTime(required=True),
        web_jump_type=EnumField(AppJumpList.JumpType, required=False),
        app_jump_type=EnumField(AppJumpList.JumpType, required=False),
        web_jump_id=fields.Integer(required=False),
        app_jump_id=fields.Integer(required=False),
        light_pic_id=fields.Integer(required=True),
        dark_pic_id=fields.Integer(required=True),
        translations=fields.Nested(ReferralActivityBannerMixin.TransSchema, many=True, required=True),
    ))
    def put(cls, id_, **kwargs):
        """运营-推荐返佣管理-活动配置编辑"""
        if (row := cls.model.query.get(id_)) is None:
            raise InvalidArgument
        cls.check_field(kwargs, id_)
        with CacheLock(LockKeys.referral_activity()):
            db.session.rollback()
            trans = cls.content_model.query.filter(
                cls.content_model.activity_id == row.id
            ).all()
            trans_map = {v.lang: v for v in trans}
            trans_field = ["title", "sub_title", "copywriting"]
            for t in kwargs.pop("translations"):
                if t_row := trans_map.get(t["lang"]):
                    old_data = t_row.to_dict(enum_to_name=True)
                    for t_field in trans_field:
                        setattr(t_row, t_field, t[t_field])
                    AdminOperationLog.new_edit(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectOperation.ReferralActivityContent,
                        old_data=old_data,
                        new_data=t_row.to_dict(enum_to_name=True),
                        special_data=dict(activity_id=t_row.activity_id, lang=t_row.lang)
                    )
                else:
                    new_content = cls.content_model(
                        activity_id=row.id,
                        lang=t["lang"],
                        title=t["title"],
                        sub_title=t["sub_title"],
                        copywriting=t["copywriting"],
                    )
                    db.session.add(new_content)
                    AdminOperationLog.new_add(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectOperation.ReferralActivityContent,
                        detail=new_content.to_dict(enum_to_name=True),
                    )

            put_fields = [
                "light_pic_id", "dark_pic_id",
                "begin_at", "end_at"
            ]
            old_data = row.to_dict(enum_to_name=True)
            for field in put_fields:
                setattr(row, field, kwargs[field])
            row.web_jump_type = kwargs.get("web_jump_type")
            row.app_jump_type = kwargs.get("app_jump_type")
            row.web_jump_id = kwargs.get("web_jump_id")
            row.app_jump_id = kwargs.get("app_jump_id")
            setattr(row, "display_name", trans_map[Language.ZH_HANS_CN].title)
            db.session.commit()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.ReferralActivity,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )

    @classmethod
    def delete(cls, id_):
        """运营-推荐返佣管理-活动配置删除"""
        if (row := cls.model.query.get(id_)) is None:
            raise InvalidArgument
        row.status = cls.model.Status.DELETE
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ReferralActivity,
            detail=dict(id=id_, display_name=row.display_name),
        )


@ns.route('/activity/<int:id_>/offline')
@respond_with_code
class ReferralActivityOfflineResource(ReferralActivityBannerMixin, Resource):

    @classmethod
    def patch(cls, id_):
        """运营-推荐返佣管理-活动配置下架"""
        if (row := cls.model.query.get(id_)) is None:
            raise InvalidArgument
        row.end_at = now()
        db.session.commit()

        AdminOperationLog.new_stop(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.ReferralActivity,
            detail=dict(id=id_, display_name=row.display_name),
        )


@ns.route('/activity-order/<int:id_>')
@respond_with_code
class ReferralActivityOrderResource(ReferralActivityBannerMixin, Resource):

    @classmethod
    def get_by_sort_id(cls, obj, up):
        if up:
            query = cls.model.query.filter(
                cls.model.sort_id < obj.sort_id,
            ).order_by(cls.model.sort_id.desc())
        else:
            query = cls.model.query.filter(
                cls.model.sort_id > obj.sort_id,
            ).order_by(cls.model.sort_id)
        ret = query.filter(
            cls.model.status == cls.model.Status.VALID
        ).first()
        return ret

    @classmethod
    @ns.use_kwargs(dict(
        up=fields.Boolean(required=True),
    ))
    def put(cls, id_, **kwargs):
        """运营-推荐返佣管理-活动配置排序"""
        if (obj := cls.model.query.get(id_)) is None:
            raise InvalidArgument
        old_data = obj.to_dict(enum_to_name=True)
        with CacheLock(LockKeys.referral_activity()):
            db.session.rollback()
            if up := kwargs['up']:
                if obj.sort_id == 1:
                    raise InvalidArgument(message='已经是第一')
                record = cls.get_by_sort_id(obj, up)
            else:
                last_record: cls.model = cls.model.query.order_by(
                    cls.model.sort_id.desc()
                ).first()
                if obj.sort_id == last_record.sort_id:
                    raise InvalidArgument(message='已经是最后')
                record = cls.get_by_sort_id(obj, up)
            if not record:
                raise InvalidArgument(message='已经是最前/最后')
            obj.sort_id, record.sort_id = \
                record.sort_id, obj.sort_id
            db.session.commit()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.ReferralActivity,
                old_data=old_data,
                new_data=obj.to_dict(enum_to_name=True),
            )


@ns.route("/special-referree-rates")
@respond_with_code
class SpecialReferreeRatesResource(Resource):

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "trigger_time", Language.ZH_HANS_CN: "触发时间"},
        {"field": "referree_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "referree_email", Language.ZH_HANS_CN: "用户邮箱"},
        {"field": "referrer_email", Language.ZH_HANS_CN: "邀请人邮箱"},
        {"field": "referrer_type", Language.ZH_HANS_CN: "邀请人身份"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "origin_referrer_rate", Language.ZH_HANS_CN: "邀请人返佣比例"},
        {"field": "rate", Language.ZH_HANS_CN: "新返佣比例"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            referree_id=fields.Integer,
            referrer_id=fields.Integer,
            referrer_type=EnumField(SpecialReferreeUserRate.ReferrerType),
            status=EnumField(SpecialReferreeUserRate.Status),
            min_rate=PositiveDecimalField(allow_zero=True),
            export=fields.Boolean,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 被邀请API用户列表 """
        model = SpecialReferreeUserRate
        q = model.query.order_by(model.id.desc())
        if referree_id := kwargs.get("referree_id"):
            q = q.filter(model.referree_id == referree_id)
        if referrer_id := kwargs.get("referrer_id"):
            q = q.filter(model.referrer_id == referrer_id)
        if referrer_type := kwargs.get("referrer_type"):
            q = q.filter(model.referrer_type == referrer_type)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        if (min_rate := kwargs.get("min_rate")) is not None and min_rate > 0:
            q = q.filter(model.origin_referrer_rate > min_rate)

        is_export = kwargs.get('export')
        if is_export:
            pagination = q.paginate(1, ADMIN_EXPORT_LIMIT, error_out=False)
        else:
            pagination = q.paginate(kwargs["page"], kwargs["limit"])
        rows: list[model] = pagination.items
        total = pagination.total

        user_ids = {i.referree_id for i in rows}
        user_ids.update({i.referrer_id for i in rows})
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 2000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(
                User.id,
                User.email,
            ).all()
            user_email_map.update(dict(chunk_users))

        items = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            item['referree_email'] = user_email_map.get(row.referree_id)
            item['referrer_email'] = user_email_map.get(row.referrer_id)
            items.append(item)

        if is_export:
            for d in items:
                d["origin_referrer_rate"] = f'{amount_to_str(d["origin_referrer_rate"] * 100, 4)}%'
                d["rate"] = f'{amount_to_str(d["rate"] * 100, 4)}%'
                d["trigger_time"] = d["trigger_time"].strftime("%Y-%m-%d %H:%M:%S") if d["trigger_time"] else ""
                d["referrer_type"] = model.ReferrerType[d["referrer_type"]].value
                d["status"] = model.Status[d["status"]].value

            return export_xlsx(
                filename='referral-api-user-list',
                data_list=items,
                export_headers=cls.export_headers,
            )

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict=model.Status,
                referrer_type_dict=model.ReferrerType,
            ),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            rate=PositiveDecimalField(required=True),
        )
    )
    def put(cls, **kwargs):
        """ 被邀请API用户-修改新返佣比例 """
        model = SpecialReferreeUserRate
        row: model = model.query.get(kwargs['id'])
        old_data = row.to_dict(enum_to_name=True)
        new_rate = kwargs["rate"]
        if not 0 <= new_rate <= 1:
            raise InvalidArgument(message="新返佣比例范围不在0～1")

        er_id = row.referrer_id
        er_ref_rates = ReferralBusiness.batch_get_users_referral_rate([er_id]).get(er_id)
        if not er_ref_rates:
            raise InvalidArgument(message="邀请人返佣比例查询失败")
        if new_rate > max(er_ref_rates[0], er_ref_rates[1]):
            raise InvalidArgument(message="新返佣比例不得大于邀请人返佣比例。")

        row.rate = new_rate
        row.rate_manual_edit_at = now()
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialReferreeRate,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=er_id,
        )
