# -*- coding: utf-8 -*-
import copy
import decimal
import re
import time
from datetime import timedelta
import json
from decimal import Decimal, DecimalException
from enum import Enum
from typing import List, Dict

from flask import g
from flask_babel import gettext
from flask_restx import fields, marshal
from sqlalchemy import or_, distinct, func
from collections import defaultdict
from webargs import fields as wa_fields


from ..common import Resource, Namespace, respond_with_code, \
    ex_fields
from ..common.fields import (
    PageField, LimitField, EnumField, AssetField,
    TimestampField, PositiveDecimalField,
)
from ... import Language
from ...business import ServerClient, current_app, TradeSummaryDB, User, \
    market_info_auto, cancel_market_orders, PriceManager
from ...business.market import update_amm_markets, update_markets
from ...business.amm import LiquidityService, close_liqudity_pool
from ...business.exchange import retry_exchanging_exchange_order_by_market
from ...business.trade_rank import RankAdaptor
from ...business.strategy.grid import batch_pause_grid_strategy_by_market, batch_restart_grid_strategy_by_market
from ...business.pre_trading import get_pre_asset_config
from ...caches import MarketCache, AmmMarketCache, LiquidityPoolCache
from ...caches.system import MarketMaintainCache
from ...models import UserLiquidity, LiquidityPool, LiquidityHistory, AmmMarket, MarginAccount, CoinInformation
from ...utils import current_timestamp, quantize_amount, amount_to_str, now, datetime_to_time, export_xlsx
from ...models import Market, UserTradeSummary, db, UserTradeFeeSummary, \
    row_to_dict
from ...models.spot import MarketOfflineContent
from ...models.perpetual import PerpetualMarket
from ...models.auto_invest import AutoInvestMarket
from ...models.strategy import SpotGridMarket
from ...models.mongo.op_log import (AdminOperationLogMySQL as AdminOperationLog,
                                    OPNamespaceObjectSpot, OPNamespaceObjectPerpetual)
from ...models.pledge import PledgeAsset
from ...exceptions import InvalidArgument
from ...assets import list_all_assets, has_asset, list_pre_assets
from ...common import PrecisionEnum, CommonCurrency, language_cn_names
from ...schedules.spot import cancel_market_orders_task, update_market_statuses


ns = Namespace('Spot')


@ns.route('/info')
@respond_with_code
class MarketInfoResource(Resource):

    @staticmethod
    def get():
        """市场信息"""
        pending_markets = Market.query.filter(Market.status == Market.Status.PENDING).with_entities(
            Market.name
        ).all()
        return dict(
            market_list=sorted(MarketCache.list_online_markets()),
            pending_market_lis={i.name for i in pending_markets},
            asset_list=sorted(list(list_all_assets())),
            order_side=dict(SELL='卖出', BUY='买入'),
            order_type_data=dict(LIMIT='限价', MARKET='市价',
                                 STOP_LIMIT='计划限价', STOP_MARKET='计划市价')
        )


class MarketHelp(Resource):

    @classmethod
    def check_coin_info(cls, base_asset, started_at):
        # 检查市场的币种资料是不是已经填写了上架时间，且市场的上架时间要小于币种的上架时间
        model = CoinInformation
        coin = model.query.filter(model.code == base_asset).first()
        if not coin:
            raise InvalidArgument(message=f'币种资料中不存在 {base_asset} 币种')
        if started_at:
            if not coin.online_time:
                raise InvalidArgument(message=f'请先到 {base_asset} 币种资料中填写上架时间')
            if started_at < coin.online_time:
                raise InvalidArgument(message=f'市场上架时间需大于等于 {base_asset} 币种资料的上架时间')


@ns.route('/markets')
@respond_with_code
class MarketsResource(MarketHelp):

    status_map = {
        # 'OFFLINE': '下架',
        'OFFLINE_VISIBLE': '下架可见',
        'OFFLINE_NOT_VISIBLE': '下架隐藏',
        'PENDING': '待上架',
        'BIDDING': '竞价中',
        'COUNTING_DOWN': '倒计时中',
        'ONLINE': '上架',
        'SUSPENDED': '停牌'
    }

    @classmethod
    @ns.use_kwargs(dict(
        trading_area=EnumField(Market.TradingArea),
        mode=EnumField(Market.Mode),
        status=EnumField(status_map),
        keyword=wa_fields.String,
        page=wa_fields.Integer(missing=1),
        limit=wa_fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """系统-市场配置"""
        query = Market.query
        if (trading_area := kwargs.get('trading_area')) is not None:
            query = query.filter(Market.trading_area == trading_area)
        if (mode := kwargs.get('mode')) is not None:
            query = query.filter(Market.mode == mode)
        if (status := kwargs.get('status')) is not None:
            if status == 'OFFLINE_VISIBLE':
                query = query.filter(Market.status == Market.Status.OFFLINE, Market.offline_visible.is_(True))
            elif status == 'OFFLINE_NOT_VISIBLE':
                query = query.filter(Market.status == Market.Status.OFFLINE, Market.offline_visible.is_(False))
            else:
                query = query.filter(Market.status == Market.Status[status])
        if keyword := kwargs.get('keyword'):
            query = query.filter(or_(Market.base_asset == keyword,
                                     Market.quote_asset == keyword,
                                     Market.name == keyword))

        records = query \
            .order_by(Market.id.desc()) \
            .paginate(kwargs['page'], kwargs['limit'])

        amm_query = AmmMarket.query.with_entities(
            AmmMarket.name,
            AmmMarket.allow_in_bidding,
            AmmMarket.status
        ).all()
        amm_markets = []
        bidding_allow_amm_markets = []
        for _v in amm_query:
            _v: AmmMarket
            if _v.status == AmmMarket.Status.ONLINE:
                amm_markets.append(_v.name)
                if _v.allow_in_bidding:
                    bidding_allow_amm_markets.append(_v.name)

        return dict(
            total=records.total,
            items=records.items,
            extra=dict(
                trading_areas=Market.TradingArea,
                modes=Market.Mode,
                statuses=cls.status_map,
                amm_markets=amm_markets,
                bidding_allow_amm_markets=bidding_allow_amm_markets,
                trading_area_quotes={v: [v] for v in Market.TradingArea.quotes()},
                assets=list_all_assets()
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        trading_area=EnumField(Market.TradingArea, required=True),
        mode=EnumField(Market.Mode, example=Market.Mode.NORMAL),
        default_depth=wa_fields.String(required=True, example='0.01'),
        depths=wa_fields.String(required=True,
                                example='0.1,0.01,0.001,0.0001'),
        base_asset=AssetField(required=True),
        base_asset_precision=wa_fields.Integer(required=True),
        quote_asset=AssetField(required=True),
        quote_asset_precision=wa_fields.Integer(required=True),
        taker_fee_rate=wa_fields.Decimal(required=True, example='0.002'),
        maker_fee_rate=wa_fields.Decimal(required=True, example='0.002'),
        max_price_deviation=wa_fields.Decimal(required=True,
                                              example=Decimal('0.05')),
        opening_price_asset=wa_fields.String(missing='USD', example='USD'),
        opening_price=wa_fields.Decimal(missing=Decimal()),
        started_at=TimestampField,
        ended_at=TimestampField,
        bidding_matching_started_at=TimestampField,
        bidding_ended_at=TimestampField,
        countdown_ended_at=TimestampField
    ))
    def post(cls, **kwargs):
        """系统-市场配置-添加市场"""
        base_asset = kwargs['base_asset']
        quote_asset = kwargs['quote_asset']
        name = f'{base_asset}{quote_asset}'
        if Market.query.filter(Market.name == name).first():
            raise InvalidArgument(message=f'market {name!r} already exists')

        trading_area = kwargs['trading_area']
        mode = kwargs['mode']
        base_asset_precision = kwargs['base_asset_precision']
        quote_asset_precision = kwargs['quote_asset_precision']
        default_depth = kwargs['default_depth']
        depths = kwargs['depths']
        maker_fee_rate = kwargs['maker_fee_rate']
        taker_fee_rate = kwargs['taker_fee_rate']
        max_price_deviation = kwargs['max_price_deviation']
        opening_price_asset = kwargs['opening_price_asset'].upper()
        opening_price = kwargs['opening_price']
        started_at = kwargs.get('started_at')
        ended_at = kwargs.get('ended_at')
        bidding_matching_started_at = kwargs.get('bidding_matching_started_at')
        bidding_ended_at = kwargs.get('bidding_ended_at')
        countdown_ended_at = kwargs.get('countdown_ended_at')

        cls.check_coin_info(base_asset, started_at)

        if quote_asset != trading_area.value:
            raise InvalidArgument(
                message=f'quote asset {quote_asset!r} does not equal to '
                        f'trading area {trading_area!r}')

        try:
            list(map(Decimal, depths.split(',')))
        except DecimalException:
            raise InvalidArgument(message=f'invalid depths: {depths!r}')

        if mode is not Market.Mode.COUNTDOWN:
            if opening_price <= 0:
                raise InvalidArgument(
                    message=f'invalid `opening_price`: {opening_price}')
            if not (has_asset(opening_price_asset)
                    or isinstance(getattr(CommonCurrency, opening_price_asset, None),
                                  CommonCurrency)):
                raise InvalidArgument(
                    message=f'invalid opening price asset: '
                            f'{opening_price_asset!r}')

        if mode is Market.Mode.BIDDING:
            if bidding_matching_started_at is None or bidding_ended_at is None:
                raise InvalidArgument(
                    message=f'`bidding_matching_started_at` or '
                            f'`bidding_ended_at` is empty')
        elif mode is Market.Mode.COUNTDOWN:
            if countdown_ended_at is None:
                raise InvalidArgument(message=f'`countdown_ended_at` is empty')

        if not Market.check_precision(base_asset_precision, quote_asset_precision):
            raise InvalidArgument(message='precision overflow')

        row = Market(
            name=name,
            trading_area=trading_area,
            mode=mode,
            base_asset=base_asset,
            quote_asset=quote_asset,
            default_depth=default_depth,
            depths=depths,
            base_asset_precision=base_asset_precision,
            quote_asset_precision=quote_asset_precision,
            maker_fee_rate=maker_fee_rate,
            taker_fee_rate=taker_fee_rate,
            max_price_deviation=max_price_deviation,
            opening_price_asset=opening_price_asset,
            opening_price=opening_price,
            started_at=started_at,
            ended_at=ended_at,
            bidding_matching_started_at=bidding_matching_started_at,
            bidding_ended_at=bidding_ended_at,
            countdown_ended_at=countdown_ended_at
        )
        db.session_add_and_commit(row)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.Market,
            detail=kwargs,
        )

        return row


# noinspection PyUnresolvedReferences
@ns.route('/markets/<name>')
@respond_with_code
class MarketResource(MarketHelp):

    @classmethod
    def get(cls, name):
        """系统-市场配置-市场"""
        market = Market.query \
            .filter(Market.name == name) \
            .first()
        if market is None:
            raise InvalidArgument
        return market

    @classmethod
    @ns.use_kwargs(dict(
        mode=EnumField(Market.Mode),
        default_depth=wa_fields.String(),
        depths=wa_fields.String(),
        base_asset_precision=wa_fields.Integer(),
        quote_asset_precision=wa_fields.Integer(),
        taker_fee_rate=wa_fields.Decimal(),
        maker_fee_rate=wa_fields.Decimal(),
        max_price_deviation=wa_fields.Decimal(),
        opening_price_asset=wa_fields.String(),
        opening_price=wa_fields.Decimal(),
        started_at=TimestampField(),
        ended_at=TimestampField(),
        bidding_matching_started_at=TimestampField(),
        bidding_ended_at=TimestampField(),
        countdown_ended_at=TimestampField(),
        trading_disabled=wa_fields.Boolean,
        api_spot_trading_disabled=wa_fields.Boolean,
        offline_visible=wa_fields.Boolean,
        status=EnumField(Market.Status)
    ))
    def patch(cls, name, **kwargs):
        """系统-市场配置-编辑市场"""
        row: Market = Market.query.filter(Market.name == name).first()
        if row is None:
            raise InvalidArgument(f'market {name!r} does not exists')
        old_data = row_to_dict(row, enum_to_name=True)

        mode = kwargs.get('mode')
        base_asset_precision = kwargs.get('base_asset_precision')
        quote_asset_precision = kwargs.get('quote_asset_precision')
        default_depth = kwargs.get('default_depth')
        depths = kwargs.get('depths')
        maker_fee_rate = kwargs.get('maker_fee_rate')
        taker_fee_rate = kwargs.get('taker_fee_rate')
        max_price_deviation = kwargs.get('max_price_deviation')
        opening_price_asset = kwargs.get('opening_price_asset')
        opening_price = kwargs.get('opening_price')
        started_at = kwargs.get('started_at')
        ended_at = kwargs.get('ended_at')
        bidding_matching_started_at = kwargs.get('bidding_matching_started_at')
        bidding_ended_at = kwargs.get('bidding_ended_at')
        countdown_ended_at = kwargs.get('countdown_ended_at')
        trading_disabled = kwargs.get('trading_disabled')
        api_spot_trading_disabled = kwargs.get('api_spot_trading_disabled')
        offline_visible = kwargs.get('offline_visible')
        status = kwargs.get('status')
        if status is Market.Status.PENDING:
            if row.name in MarketMaintainCache.get_market_maintains():
                raise InvalidArgument(message=f'该市场设置了单一市场停服，无法上架')
        if offline_visible:
            if not MarketOfflineContent.query.filter(
                MarketOfflineContent.market == name,
                MarketOfflineContent.market_type == MarketOfflineContent.MarketType.SPOT,
                MarketOfflineContent.lang == Language.EN_US,
            ).first():
                raise InvalidArgument(message=f'请先配置市场的下架文案，再设置为下架可见')

        if started_at:
            cls.check_coin_info(row.base_asset, started_at)

        old_market_info = {
            "trading_disabled": row.trading_disabled,
            "api_spot_trading_disabled": row.api_spot_trading_disabled,
        }

        if depths:
            try:
                list(map(Decimal, depths.split(',')))
            except DecimalException:
                raise InvalidArgument(f'invalid depths: {depths!r}')
        if (opening_price_asset
                and not (has_asset(opening_price_asset)
                         or isinstance(getattr(CommonCurrency,
                                               opening_price_asset, None),
                                       CommonCurrency))):
            raise InvalidArgument(
                f'invalid opening price asset: {opening_price_asset!r}')

        if not Market.check_precision(base_asset_precision or row.base_asset_precision,
                                      quote_asset_precision or row.quote_asset_precision):
            raise InvalidArgument('precision overflow')

        if mode is not None:
            row.mode = mode
        if base_asset_precision is not None:
            row.base_asset_precision = base_asset_precision
        if quote_asset_precision is not None:
            row.quote_asset_precision = quote_asset_precision
        if default_depth is not None:
            row.default_depth = default_depth
        if depths is not None:
            row.depths = depths
        if maker_fee_rate is not None:
            row.maker_fee_rate = maker_fee_rate
        if taker_fee_rate is not None:
            row.taker_fee_rate = taker_fee_rate
        if max_price_deviation is not None:
            row.max_price_deviation = max_price_deviation
        if opening_price_asset is not None:
            row.opening_price_asset = opening_price_asset
        if opening_price is not None:
            row.opening_price = opening_price
        if started_at is not None:
            row.started_at = started_at
        if ended_at is not None:
            cls.check_other_dependence_markets(row)
            row.ended_at = ended_at
        # 删除下架时间
        if ended_at is None:
            row.ended_at = ended_at
        if bidding_matching_started_at is not None:
            row.bidding_matching_started_at = bidding_matching_started_at
        if bidding_ended_at is not None:
            row.bidding_ended_at = bidding_ended_at
        if countdown_ended_at is not None:
            row.countdown_ended_at = countdown_ended_at
        if trading_disabled is not None:
            row.trading_disabled = trading_disabled
        if api_spot_trading_disabled is not None:
            row.api_spot_trading_disabled = api_spot_trading_disabled
        if offline_visible is not None:
            row.offline_visible = offline_visible
        if status is not None:
            statuses = Market.Status
            if row.status is statuses.OFFLINE:
                if status is not statuses.PENDING:
                    raise InvalidArgument
            if status == statuses.OFFLINE:
                cls.check_other_dependence_markets(row)
                # 三分钟之后再撤一次单,防止api未同步仍有部分订单未撤
                cancel_market_orders_task.apply_async((row.name,), countdown=180, expires=240)
            else:
                row.offline_visible = False
            row.status = status
        db.session.commit()

        MarketCache(name).refresh()
        update_markets()
        update_market_statuses.delay()
        cls.sync_spot_grid_strategies(row, old_market_info)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.Market,
            old_data=old_data,
            new_data=row_to_dict(row, enum_to_name=True),
            special_data=dict(name=name),
        )

        return row

    @classmethod
    def check_other_dependence_markets(cls, market_row: Market):
        market = market_row.name
        amm_row = AmmMarket.query.filter(AmmMarket.name == market, AmmMarket.status == AmmMarket.Status.ONLINE).first()
        if amm_row:
            raise InvalidArgument(message='{}存在AMM市场，不能下架或设置下架时间，请先将其从AMM市场下架!'.format(market))
        margin_row = MarginAccount.query.filter(
            MarginAccount.name == market, MarginAccount.status == MarginAccount.StatusType.OPEN).first()
        if margin_row:
            raise InvalidArgument(
                message='{}存在杠杆市场，不能下架或设置下架时间，请先将其从杠杆市场下架!'.format(market))
        grid_market: SpotGridMarket = SpotGridMarket.query.filter(
            SpotGridMarket.name == market,
            SpotGridMarket.status == SpotGridMarket.Status.ONLINE,
        ).first()
        if grid_market:
            raise InvalidArgument(message=f'{market}存在现货网格市场，不能下架或设置下架时间，请先将其从现货网格市场下架!')
        auto_invest_market: AutoInvestMarket = AutoInvestMarket.query.filter(
            AutoInvestMarket.market == market,
            AutoInvestMarket.status == AutoInvestMarket.Status.OPEN,
        ).first()
        if auto_invest_market:
            raise InvalidArgument(message=f'{market}存在定投市场，不能下架或设置下架时间，请先将其从定投下架!')
        pledge_asset_row: PledgeAsset = PledgeAsset.query.filter(
            PledgeAsset.asset == market_row.base_asset,
            PledgeAsset.status == PledgeAsset.Status.OPEN,
        ).first()
        if pledge_asset_row:
            # 实时质押的资产不好判断，这里只检查下质押币的状态
            raise InvalidArgument(
                message=f'{market}存在质押币种{pledge_asset_row.asset}，不能下架或设置下架时间，请先将质押币种下架!'
            )

    @classmethod
    def sync_spot_grid_strategies(cls, row: Market, old_market_info: dict):
        """ 市场可交易状态变化时，同步到现货网格 """
        if row.trading_disabled == old_market_info["trading_disabled"]:
            return
        market_name = row.name
        grid_market: SpotGridMarket = SpotGridMarket.query.filter(
            SpotGridMarket.name == market_name,
            SpotGridMarket.status == SpotGridMarket.Status.ONLINE,
        ).first()
        if not grid_market:
            return
        if row.trading_disabled:
            batch_pause_grid_strategy_by_market.delay(market_name)
        else:
            batch_restart_grid_strategy_by_market.delay(market_name)


@ns.route("/markets/offline-contents")
@respond_with_code
class MarketOfflineContentsResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            market_type=EnumField(MarketOfflineContent.MarketType, required=True),
        )
    )
    def get(cls, **kwargs):
        """系统-市场配置-获取市场下架文案"""
        market = kwargs['market']
        market_type = kwargs['market_type']
        if market_type == MarketOfflineContent.MarketType.SPOT:
            market_row: Market = Market.query.filter(Market.name == market).first()
            if not market_row:
                raise InvalidArgument(message=f'现货市场 {market!r} 不存在')
        else:
            market_row: PerpetualMarket = PerpetualMarket.query.filter(PerpetualMarket.name == market).first()
            if not market_row:
                raise InvalidArgument(message=f'合约市场 {market!r} 不存在')
        extra = dict(
            langs={
                x.name: y
                for x, y in language_cn_names().items()
                if x in Language
            },
        )

        rows = MarketOfflineContent.query.filter(
            MarketOfflineContent.market == market,
            MarketOfflineContent.market_type == market_type,
        ).all()
        lang_content_map = {x.lang.name: x for x in rows}
        contents = {}
        has_content = False
        for lang in Language:
            lang = lang.name
            r = lang_content_map.get(lang)
            if r:
                contents[lang] = dict(
                    lang=lang,
                    content=r.content,
                )
                has_content = True
            else:
                contents[lang] = dict(
                    lang=lang,
                    content="",
                )
        return dict(
            market=market,
            market_type=market_type.name,
            record=dict(
                id=market_row.id,
                status=market_row.status.name,
                has_content=has_content,
                created_by=0,
                audited_by=0,
            ),
            contents=contents,
            extra=extra,
        )


@ns.route("/markets/offline-contents/<market_type>/<int:id_>/langs/<lang>")
@respond_with_code
class MarketOfflineLangContentResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            content=wa_fields.String(missing=""),
        )
    )
    def put(cls, market_type, id_, lang, **kwargs):
        """系统-市场配置-市场下架文案-保存"""
        market_type = str(market_type).upper()
        ns_obj = OPNamespaceObjectSpot.MarketOfflineContent
        if market_type == MarketOfflineContent.MarketType.SPOT.name:
            market_row: Market = Market.query.filter(Market.id == id_).first()
            if not market_row:
                raise InvalidArgument(message=f'现货市场 {id_!r} 不存在')
            market = market_row.name
        else:
            market_row: PerpetualMarket = PerpetualMarket.query.filter(PerpetualMarket.id == id_).first()
            if not market_row:
                raise InvalidArgument(message=f'合约市场 {id_!r} 不存在')
            market = market_row.name

            ns_obj = OPNamespaceObjectPerpetual.MarketOfflineContent

        content_row: MarketOfflineContent = MarketOfflineContent.get_or_create(
            market=market,
            market_type=market_type,
            lang=lang,
        )
        old_content = content_row.content
        content_row.content = kwargs["content"]
        db.session.add(content_row)
        db.session.commit()

        if not old_content:
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=ns_obj,
                detail=content_row.to_dict(enum_to_name=True),
            )
        else:
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectSpot.MarketOfflineContent,
                old_data=dict(content=old_content),
                new_data=dict(content=content_row.content),
                special_data=dict(market=market, market_type=market_type, lang=lang),
            )


# noinspection PyUnresolvedReferences
@ns.route('/markets/<name>/orders')
@respond_with_code
class MarketOrdersResource(Resource):

    @classmethod
    def delete(cls, name):
        """市场撤单"""
        cancel_market_orders(name)
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarketOrders,
            detail=dict(name=name),
        )
        return {}


@ns.route('/markets/<name>/exhcange-orders')
@respond_with_code
class MarketExchangeOrdersResource(Resource):

    @classmethod
    def delete(cls, name):
        """现货市场-撤销兑换订单"""
        row: Market = Market.query.filter(Market.name == name).first()
        if not row:
            raise InvalidArgument(message="市场不存在")
        retry_exchanging_exchange_order_by_market(row.base_asset, row.quote_asset, name)
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.ExchangeOrders,
            detail=dict(name=name),
        )
        return {}


@ns.route('/market-help-me')
@respond_with_code
class MarketAutoGenerationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        quote_asset=AssetField(required=True),
        opening_price=wa_fields.Decimal(required=True),
        opening_price_asset=wa_fields.String(required=True)
    ))
    def get(cls, quote_asset: str, opening_price: Decimal,
            opening_price_asset: str):
        """市场配置辅助"""
        return market_info_auto(
            quote_asset, opening_price, opening_price_asset)


@ns.route('/asset-rates')
@respond_with_code
class AssetRatesResource(Resource):

    @classmethod
    def get(cls):
        """币种汇率"""
        return PriceManager.assets_to_usd()


@ns.route('/trade-depth')
@respond_with_code
class CoinTradeDepthResource(Resource):
    marshal_fields = {
        'create_time': fields.Integer(attribute='ctime'),
        'user_id': fields.Integer(attribute='user'),
        'price': ex_fields.AmountField,
        'amount': ex_fields.AmountField,
        'deal_amount': ex_fields.AmountField(
            attribute=lambda x: Decimal(
                x['amount']) - Decimal(x.get('left', 0))),
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(missing='BTCUSDT'),
        side=wa_fields.Integer(),
        is_stop=wa_fields.Integer(),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """币币-币币深度列表"""
        market = kwargs['market']
        side = kwargs.get('side')
        is_stop = kwargs.get('is_stop')
        page = kwargs.get('page')
        limit = kwargs.get('limit')

        server_cli = ServerClient()
        if is_stop:
            orders = server_cli.market_stop_book_orders(
                market=market, side=side, page=page, limit=limit)
        else:
            orders = server_cli.market_book_orders(
                market=market, side=side, page=page, limit=limit
            )
        orders = orders.as_dict()

        orders['data'] = marshal(orders['data'], cls.marshal_fields)
        orders['market_list'] = sorted(MarketCache.list_online_markets())

        return orders


@ns.route('/latest-deals')
@respond_with_code
class LatestDeals(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String(
            attribute=lambda x: '主动卖出' if x['type'] == 'sell' else '主动买入'),
        'price': ex_fields.AmountField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        last_id=wa_fields.String(missing=0),
        limit=ex_fields.LimitField,
    ))
    def get(cls, **kwargs):
        """币币-币币深度列表-币币最新成交"""
        market = kwargs['market']
        last_id = kwargs['last_id']
        limit = kwargs['limit']

        client = ServerClient()
        result = client.market_deals_ext(
            market=market, limit=limit, last_id=last_id)

        return dict(
            orders=marshal(result, cls.marshal_fields),
            market_list=sorted(MarketCache.list_online_markets())
        )


@ns.route('/realtime-net-rank')
@respond_with_code
class RealtimeNetRank(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(),
        asset=wa_fields.String(required=True),
        time_type=ex_fields.EnumField(
            enum=['15min', '1h', '6h', '24h', '72h', '7d'], missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
    ))
    def get(cls, **kwargs):
        """币币-交易排名-实时净买卖"""
        asset = kwargs['asset']
        market = kwargs.get('market')
        time_type = kwargs['time_type']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        now_ts = current_timestamp(to_int=True)
        if start_time and end_time:
            start_time = int(start_time / 1000)
            end_time = int(end_time / 1000)
        else:
            start_time, end_time = now_ts - 86400, now_ts

        if time_type == '15min':
            start_time, end_time = now_ts - 15 * 60, now_ts
            end_time = now_ts
        elif time_type == '1h':
            start_time, end_time = now_ts - 1 * 3600, now_ts
        elif time_type == '6h':
            start_time, end_time = now_ts - 6 * 3600, now_ts
        elif time_type == '24h':
            start_time, end_time = now_ts - 86400, now_ts
        elif time_type == '72h':
            start_time, end_time = now_ts - 3 * 86400, now_ts
        elif time_type == '7d':
            start_time, end_time = now_ts - 7 * 86400, now_ts

        if end_time - start_time > 7 * 86400:
            raise InvalidArgument(message='仅支持七天实时数据')

        market_list = [market] if market else \
            MarketCache.list_online_markets_by_asset(asset)

        client = ServerClient()
        result = client.trade_net_rank(
            market=market_list, start_time=start_time, end_time=end_time)

        p = RankAdaptor('spot', 'market_rank').get_processor()
        return dict(
            rank_data=dict(
                buy=[dict(
                    net=quantize_amount(r['net'], 8),
                    total=quantize_amount(r['total'], 8),
                    user_id=r['user_id']
                ) for r in result['buy'] if Decimal(
                    r['net']) > 0][:200],
                sell=[dict(
                    net=quantize_amount(r['net'], 8),
                    total=quantize_amount(r['total'], 8),
                    user_id=r['user_id']
                ) for r in result['sell'] if Decimal(
                    r['net']) > 0][:200],
            ),
            summary_data=p.process(result),
            asset_list=list_all_assets(),
            market_list=sorted(MarketCache.list_online_markets_by_asset(asset))
        )


class LastHour(Enum):
    LAST_1_HOUR = '1小时'
    LAST_2_HOUR = '2小时'
    LAST_4_HOUR = '4小时'
    LAST_6_HOUR = '6小时'
    LAST_12_HOUR = '12小时'
    LAST_24_HOUR = '24小时'

    def to_timedelta(self) -> timedelta:
        return timedelta(hours=int(self.value[:-2]))

    def to_map(self) -> Dict:
        return {'value': self.name, 'label': self.value}

    @classmethod
    def get_last_hour_list(cls, market_deals_ext_result: List[Dict]) -> List[Dict]:
        last_timestamp = int(market_deals_ext_result[-1]['time']) if len(market_deals_ext_result) > 0 else 0
        time_interval = timedelta(seconds=int(time.time()) - last_timestamp)
        last_list: List[Dict] = [cls.LAST_1_HOUR.to_map()]
        for last in [cls.LAST_2_HOUR, cls.LAST_4_HOUR, cls.LAST_6_HOUR, cls.LAST_12_HOUR, cls.LAST_24_HOUR]:
            if time_interval > last.to_timedelta():
                last_list.append(last.to_map())
            else:
                break
        return last_list


@ns.route('/realtime-price-rank')
@respond_with_code
class RealtimePriceRank(Resource):
    class Bid(Enum):
        BID_RANK_DESC = 'bid_rank_desc'
        BID_RANK_ASC = 'bid_rank_asc'

    class Ask(Enum):
        ASK_RANK_DESC = 'ask_rank_desc'
        ASK_RANK_ASC = 'ask_rank_asc'

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        bid_rank=EnumField(Bid, missing=Bid.BID_RANK_DESC),
        ask_rank=EnumField(Ask, missing=Ask.ASK_RANK_ASC),
        last_hour=EnumField(LastHour, missing=LastHour.LAST_1_HOUR),
        limit=ex_fields.LimitField(missing=100)
    ))
    def get(cls, **kwargs):
        """币币-交易排名-成交价排名"""
        market = kwargs['market']
        limit = kwargs['limit']
        bid_rank = kwargs['bid_rank']
        ask_rank = kwargs['ask_rank']
        last_hour = kwargs['last_hour']

        end_time = datetime_to_time(now() - last_hour.to_timedelta())
        client = ServerClient()
        quote_asset = MarketCache(market).dict["quote_asset"]
        asset_price = PriceManager.asset_to_usd(quote_asset)
        # 现货server的最近成交数据默认不包括自成交数据，需要添加real字段指定包括自成交数据
        result = client.market_deals_ext(market=market, limit=3000, last_id=0, real=False)
        sell_trade_mapper = defaultdict(lambda: {
            "price": [],
            "amount_at_price": [],
            "amount": Decimal(),
            "amount_usd": Decimal()
        })
        buy_trade_mapper = copy.deepcopy(sell_trade_mapper)
        for item in result:
            price = Decimal(item['price'])
            amount = Decimal(item['amount'])
            buy_user_id = item['bid_user_id']
            sell_user_id = item['ask_user_id']
            if int(item['time']) < end_time:
                break
            buy_trade_mapper[buy_user_id]['price'].append(price)
            buy_trade_mapper[buy_user_id]['amount_at_price'].append(amount)
            buy_trade_mapper[buy_user_id]['amount'] += amount
            buy_trade_mapper[buy_user_id]['amount_usd'] += price * amount * asset_price
            sell_trade_mapper[sell_user_id]['price'].append(price)
            sell_trade_mapper[sell_user_id]['amount_at_price'].append(amount)
            sell_trade_mapper[sell_user_id]['amount'] += amount
            sell_trade_mapper[sell_user_id]['amount_usd'] += price * amount * asset_price
        sell_rank_data, buy_rank_data = [], []

        bid_choose, bid_reverse = cls.get_bid_rank(bid_rank)
        for user_id, data in sorted(
                buy_trade_mapper.items(), key=lambda x: bid_choose(x[1]['price']), reverse=bid_reverse
        ):
            if len(buy_rank_data) > limit:
                break
            
            price = bid_choose(data['price'])
            amount_at_price = data['amount_at_price'][data['price'].index(price)]
            buy_rank_data.append({
                "user_id": user_id,
                "price": price,
                "amount_at_price": amount_at_price,
                "volume_at_price": price * amount_at_price,
                "amount": data['amount'] - sell_trade_mapper.get(user_id, {}).get('amount', Decimal()),
                "amount_usd": quantize_amount(
                    data['amount_usd'] - sell_trade_mapper.get(user_id, {}).get('amount_usd', Decimal()), 8
                )
            })

        ask_choose, ask_reverse = cls.get_ask_rank(ask_rank)
        for user_id, data in sorted(
                sell_trade_mapper.items(), key=lambda x: ask_choose(x[1]['price']), reverse=ask_reverse
        ):
            if len(sell_rank_data) > limit:
                break

            price = ask_choose(data['price'])
            amount_at_price = data['amount_at_price'][data['price'].index(price)]
            sell_rank_data.append({
                "user_id": user_id,
                "price": price,
                "amount_at_price": amount_at_price,
                "volume_at_price": price * amount_at_price,
                "amount": data['amount'] - buy_trade_mapper.get(user_id, {}).get('amount', Decimal()),
                "amount_usd": quantize_amount(
                    data['amount_usd'] - buy_trade_mapper.get(user_id, {}).get('amount_usd', Decimal()), 8
                )
            })

        return dict(
            sell_rank_data=sell_rank_data,
            buy_rank_data=buy_rank_data,
            market_list=sorted(MarketCache.list_online_markets()),
            last_hour_list=LastHour.get_last_hour_list(result)
        )

    @classmethod
    def get_ask_rank(cls, ask_rank):
        if ask_rank is cls.Ask.ASK_RANK_DESC:
            choose, reverse = max, True
        else:
            choose, reverse = min, False
        return choose, reverse

    @classmethod
    def get_bid_rank(cls, bid_rank):
        if bid_rank is cls.Bid.BID_RANK_DESC:
            choose, reverse = max, True
        else:
            choose, reverse = min, False
        return choose, reverse


@ns.route('/realtime-deal-rank')
@respond_with_code
class RealtimeDealRank(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(),
        asset=wa_fields.String(required=True),
        time_type=ex_fields.EnumField(
            enum=['15min', '1h', '6h', '24h', '72h', '7d'], missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
    ))
    def get(cls, **kwargs):
        """
        币币-交易排名-实时成交量
        """
        asset = kwargs['asset']
        market = kwargs.get('market')
        time_type = kwargs['time_type']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        now_ts = current_timestamp(to_int=True)
        if start_time and end_time:
            start_time = int(start_time / 1000)
            end_time = int(end_time / 1000)
        else:
            start_time, end_time = now_ts - 86400, now_ts

        if time_type == '15min':
            start_time, end_time = now_ts - 15 * 60, now_ts
            end_time = now_ts
        elif time_type == '1h':
            start_time, end_time = now_ts - 1 * 3600, now_ts
        elif time_type == '6h':
            start_time, end_time = now_ts - 6 * 3600, now_ts
        elif time_type == '24h':
            start_time, end_time = now_ts - 86400, now_ts
        elif time_type == '72h':
            start_time, end_time = now_ts - 3 * 86400, now_ts
        elif time_type == '7d':
            start_time, end_time = now_ts - 7 * 86400, now_ts

        if end_time - start_time > 7 * 86400:
            raise InvalidArgument(message='仅支持七天实时数据')

        market_list = [market] if market else \
            MarketCache.list_online_markets_by_asset(asset)

        client = ServerClient(current_app.logger)
        result = client.trade_amount_rank(
            market=market_list, start_time=start_time, end_time=end_time)
        p = RankAdaptor('spot', 'realtime_deal').get_processor()
        return dict(
            rank_data=dict(
                buy=[dict(
                    amount=quantize_amount(r['amount'], 8),
                    total_amount=quantize_amount(r['total_amount'], 8),
                    user_id=r['user_id']
                ) for r in result['buy']
                        if Decimal(r['amount']) > 0][:200],
                sell=[dict(
                    amount=quantize_amount(r['amount'], 8),
                    total_amount=quantize_amount(r['total_amount'], 8),
                    user_id=r['user_id']
                ) for r in result['sell']
                         if Decimal(r['amount']) > 0][:200],
            ),
            summary_data=p.process(result),
            asset_list=list_all_assets(),
            market_list=sorted(MarketCache.list_online_markets_by_asset(asset))
        )


@ns.route('/history-net-rank')
@respond_with_code
class HistoryNetRank(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(),
        asset=wa_fields.String(required=True),
        time_type=ex_fields.EnumField(
            enum=['within_7d', '7d', '30d', '90d', '180d', '365d'], missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
    ))
    def get(cls, **kwargs):
        """币币-交易排名-币币历史净买卖"""
        asset = kwargs['asset']
        market = kwargs.get('market')
        time_type = kwargs['time_type']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        now_ts = current_timestamp(to_int=True)
        if start_time and end_time:
            start_time = int(start_time / 1000)
            end_time = int(end_time / 1000)
        else:
            start_time, end_time = now_ts - 86400, now_ts

        if time_type == '7d':
            start_time, end_time = now_ts - 7 * 86400, now_ts
            end_time = now_ts
        elif time_type == '30d':
            start_time, end_time = now_ts - 30 * 86400, now_ts
        elif time_type == '90d':
            start_time, end_time = now_ts - 90 * 86400, now_ts
        elif time_type == '180d':
            start_time, end_time = now_ts - 180 * 86400, now_ts
        elif time_type == '365d':
            start_time, end_time = now_ts - 365 * 86400, now_ts
        elif time_type == 'within_7d':
            if end_time - start_time > 7 * 86400:
                raise InvalidArgument(message='时间范围超出7天')

        if end_time - start_time > 365 * 86400:
            raise InvalidArgument(message='仅支持365天历史数据')

        asset_markets_map = HistoryNetRank.get_asset_to_markets_map()
        if time_type == 'within_7d':
            if market:
                markets = [market]
            elif asset in asset_markets_map:
                markets = asset_markets_map[asset]
            else:
                raise InvalidArgument(message=f"币种{asset}未找到市场")
            records = cls.get_within_7d_trade_data(start_time, end_time, markets)
        else:
            records = TradeSummaryDB.get_trade_summary(start_time, end_time, market, asset)

        buy_dict = defaultdict(dict)
        sell_dict = defaultdict(dict)
        for record in records:
            user_id = record['user_id']
            deal_amount = record['deal_amount']
            buy_amount = record['buy_amount']
            sell_amount = record['sell_amount']

            user_buy = buy_dict[user_id]
            user_buy['user_id'] = user_id
            user_buy['net'] = user_buy.get('net', 0) + (
                    buy_amount - sell_amount)
            user_buy['deal'] = user_buy.get('deal', 0) + deal_amount

            sell_buy = sell_dict[user_id]
            sell_buy['user_id'] = user_id
            sell_buy['net'] = sell_buy.get('net', 0) + (
                    sell_amount - buy_amount)
            sell_buy['deal'] = sell_buy.get('deal', 0) + deal_amount

        buy_list = [buy_value for user_id, buy_value in buy_dict.items()]
        sell_list = [sell_value for user_id, sell_value in sell_dict.items()]

        buy_list.sort(key=lambda x: x['net'], reverse=True)
        sell_list.sort(key=lambda x: x['net'], reverse=True)

        valid_buy_list = []
        valid_sell_list = []

        for buy in buy_list:
            if buy['net'] >= 0:
                valid_buy_list.append(buy)

        for sell in sell_list:
            if sell['net'] > 0:
                valid_sell_list.append(sell)

        p = RankAdaptor('spot', 'history_net').get_processor()
        res = dict(
            rank_data=dict(
                buy=valid_buy_list[:200],
                sell=valid_sell_list[:200],
            ),
            summary_data=p.process(dict(
                buy=valid_buy_list,
                sell=valid_sell_list
            )),
            asset_markets_map=asset_markets_map,
            asset_list=list_all_assets(),
        )
        if time_type == 'within_7d':
            res.update(
                start_time=int(start_time * 1000),
                end_time=int(end_time * 1000)
            )
        return res

    @classmethod
    def get_within_7d_trade_data(cls, start_time, end_time, markets):
        records = TradeSummaryDB.get_trade_summary_from_detail_table(start_time, end_time, markets)
        for r in records:
            r["deal_amount"] = r["buy_amount"] + r["sell_amount"]
        return records

    @classmethod
    def get_asset_to_markets_map(cls) -> Dict[str, List]:
        asset_markets_map = defaultdict(list)
        market_map = MarketCache.online_markets_detail()
        for m, info in market_map.items():
            asset_markets_map[info["base_asset"]].append(m)
        return {k: sorted(v) for k, v in asset_markets_map.items()}


@ns.route('/history-deal-rank')
@respond_with_code
class HistoryDealRank(Resource):
    marshal_fields = {
        'id': fields.Integer,
        'time': fields.Integer,
        'type': fields.String,
        'price': ex_fields.PriceField,
        'amount': ex_fields.AmountField,
        'ask_user_id': fields.Integer,
        'bid_user_id': fields.Integer,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(),
        asset=wa_fields.String(required=True),
        time_type=ex_fields.EnumField(
            enum=['within_7d', '7d', '30d', '90d', '180d', '365d'], missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
    ))
    def get(cls, **kwargs):
        """币币-交易排名-币币历史成交量"""
        asset = kwargs['asset']
        market = kwargs.get('market')
        time_type = kwargs['time_type']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        now_ts = current_timestamp(to_int=True)
        if start_time and end_time:
            start_time = int(start_time / 1000)
            end_time = int(end_time / 1000)
        else:
            start_time, end_time = now_ts - 86400, now_ts

        if time_type == '7d':
            start_time, end_time = now_ts - 7 * 86400, now_ts
            end_time = now_ts
        elif time_type == '30d':
            start_time, end_time = now_ts - 30 * 86400, now_ts
        elif time_type == '90d':
            start_time, end_time = now_ts - 90 * 86400, now_ts
        elif time_type == '180d':
            start_time, end_time = now_ts - 180 * 86400, now_ts
        elif time_type == '365d':
            start_time, end_time = now_ts - 365 * 86400, now_ts
        elif time_type == 'within_7d':
            if end_time - start_time > 7 * 86400:
                raise InvalidArgument(message='时间范围超出7天')

        if end_time - start_time > 365 * 86400:
            raise InvalidArgument(message='仅支持365天历史数据')

        asset_markets_map = HistoryNetRank.get_asset_to_markets_map()
        if time_type == 'within_7d':
            if market:
                markets = [market]
            elif asset in asset_markets_map:
                markets = asset_markets_map[asset]
            else:
                raise InvalidArgument(message=f"币种{asset}未找到市场")
            records = HistoryNetRank.get_within_7d_trade_data(start_time, end_time, markets)
        else:
            records = TradeSummaryDB.get_trade_summary(start_time, end_time, market, asset)

        buy_dict = defaultdict(dict)
        sell_dict = defaultdict(dict)
        for record in records:
            user_id = record['user_id']
            deal_amount = record['deal_amount']
            buy_amount = record['buy_amount']
            sell_amount = record['sell_amount']

            user_buy = buy_dict[user_id]
            user_buy['user_id'] = user_id
            user_buy['total'] = user_buy.get('total', 0) + buy_amount
            user_buy['deal'] = user_buy.get('deal', 0) + deal_amount

            sell_buy = sell_dict[user_id]
            sell_buy['user_id'] = user_id
            sell_buy['total'] = sell_buy.get('total', 0) + sell_amount
            sell_buy['deal'] = sell_buy.get('deal', 0) + deal_amount

        buy_list = [buy_value for user_id, buy_value in buy_dict.items()]
        sell_list = [sell_value for user_id, sell_value in sell_dict.items()]

        buy_list.sort(key=lambda x: x['total'], reverse=True)
        sell_list.sort(key=lambda x: x['total'], reverse=True)

        valid_buy_list = []
        valid_sell_list = []

        for buy in buy_list:
            if buy['total'] > 0:
                valid_buy_list.append(buy)

        for sell in sell_list:
            if sell['total'] > 0:
                valid_sell_list.append(sell)

        p = RankAdaptor('spot', 'history_deal').get_processor()
        res = dict(
            rank_data=dict(
                buy=valid_buy_list[:200],
                sell=valid_sell_list[:200],
            ),
            summary_data=p.process(dict(
                buy=valid_buy_list,
                sell=valid_sell_list
            )),
            asset_markets_map=asset_markets_map,
            asset_list=list_all_assets(),
        )
        if time_type == 'within_7d':
            res.update(
                start_time=int(start_time * 1000),
                end_time=int(end_time * 1000)
            )
        return res


@ns.route('/user-trade-summary-rank')
@respond_with_code
class UserTradeSummaryRank(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "user_type", Language.ZH_HANS_CN: "账户类型"},
        {"field": "trade_amount", Language.ZH_HANS_CN: "成交市值（USD）"},
        {"field": "trade_amount_rate", Language.ZH_HANS_CN: "成交市值全站占比"},
        {"field": "trade_fee_amount", Language.ZH_HANS_CN: "手续费市值（USD）"},
        {"field": "fee_ratio", Language.ZH_HANS_CN: "平均费率"},
        {"field": "rank", Language.ZH_HANS_CN: "排名"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        id=wa_fields.Integer(missing=None),
        start_date=wa_fields.String(required=True),
        end_date=wa_fields.String(required=True),
        page=ex_fields.PageField(unlimited=True),
        sort_type=wa_fields.String(missing='trade_amount'),
        limit=ex_fields.LimitField,
        user_type=wa_fields.String,
        export=wa_fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """币币-交易排名-用户总成交量"""
        from .users import UsersResource
        user_id = kwargs['id']
        start_date = kwargs['start_date']
        end_date = kwargs['end_date']
        page = kwargs['page']
        limit = kwargs['limit']

        query = UserTradeSummary.query.filter(
            UserTradeSummary.system == UserTradeSummary.System.SPOT,
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date <= end_date,
        )
        # 强制指定索引，避免sql优化器扫全表
        daily_trade_amount_record = query.group_by(UserTradeSummary.report_date).with_entities(
            UserTradeSummary.report_date,
            func.sum(UserTradeSummary.trade_amount)
        ).with_hint(UserTradeSummary, "FORCE INDEX(report_date_system_user_id)").all()
        daily_trade_amount_map = dict(daily_trade_amount_record)
        total_amount = sum(daily_trade_amount_map.values())

        fee_query = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.system == UserTradeFeeSummary.System.SPOT,
            UserTradeFeeSummary.report_date >= start_date,
            UserTradeFeeSummary.report_date <= end_date,
        )
        if user_type := kwargs.get('user_type'):
            query = query.join(User).filter(
                UserTradeSummary.user_id == User.id,
                User.user_type == user_type,
            )
            fee_query = fee_query.join(User).filter(
                UserTradeFeeSummary.user_id == User.id,
                User.user_type == user_type,
            )
        records = []
        total = 0
        if user_id:
            user = User.query.get(user_id)
            query = query.filter(
                UserTradeSummary.user_id == user_id
            ).order_by(
                UserTradeSummary.report_date.desc()
            )
            fee_query = fee_query.filter(UserTradeFeeSummary.user_id == user_id)
            user_fee = fee_query.all()

            user_fee_date_map = defaultdict(Decimal)
            for _user in user_fee:
                user_fee_date_map[_user.report_date] = _user.trade_fee_amount

            if kwargs['export']:
                user_trade_list = query.all()
            else:
                paginate = query.paginate(page, limit, error_out=False)
                user_trade_list = paginate.items
                total = paginate.total
            for item in user_trade_list:
                total_amount = daily_trade_amount_map.get(item.report_date, 0)
                records.append({
                    'report_date': item.report_date.strftime("%Y-%m-%d"),
                    'user_id': item.user_id,
                    'trade_amount': quantize_amount(item.trade_amount, 2),
                    "trade_amount_rate": amount_to_str(
                        (item.trade_amount / total_amount) * 100 if total_amount else Decimal(), 4) + '%',
                    'trade_fee_amount': quantize_amount(user_fee_date_map[item.report_date], 2),
                    'fee_ratio': amount_to_str(user_fee_date_map[item.report_date] / item.trade_amount * 100, 2) + '%',
                    'rank': item.rank,
                    'email': user.main_user_email,
                    'user_type': UsersResource.USER_TYPES[user.user_type]
                })
        else:
            user_trade_query = query.with_entities(
                func.sum(UserTradeSummary.trade_amount).label('trade_amount'),
                UserTradeSummary.user_id
            ).group_by(
                UserTradeSummary.user_id
            ).order_by(
                func.sum(UserTradeSummary.trade_amount).desc()
            )

            if kwargs['export']:
                user_trade_list = user_trade_query.all()
            else:
                paginate = user_trade_query.paginate(page, limit, error_out=False)
                user_trade_list = list(paginate.items)
                total = paginate.total

            user_ids = [item.user_id for item in user_trade_list]
            user_fee = fee_query.filter(UserTradeFeeSummary.user_id.in_(user_ids)).with_entities(
                func.sum(UserTradeFeeSummary.trade_fee_amount).label('trade_fee_amount'),
                UserTradeFeeSummary.user_id
            ).group_by(
                UserTradeFeeSummary.user_id
            ).all()
            user_fee_map = defaultdict(Decimal)
            for _user in user_fee:
                user_fee_map[_user.user_id] = _user.trade_fee_amount
            rank = (page - 1) * limit
            user_data_map = {i.id: i for i in User.query.filter(User.id.in_(user_ids))}
            for item in user_trade_list:
                rank += 1
                records.append({
                    'report_date': '{start_date} - {end_date}'.format(
                        start_date=start_date, end_date=end_date),
                    'user_id': item.user_id,
                    'trade_amount': quantize_amount(item.trade_amount, 2),
                    "trade_amount_rate": amount_to_str(
                        (item.trade_amount / total_amount) * 100 if total_amount else Decimal(), 4) + '%',
                    'trade_fee_amount': quantize_amount(user_fee_map[item.user_id], 2),
                    'fee_ratio': (amount_to_str(user_fee_map[item.user_id] / item.trade_amount * 100
                                                if item.trade_amount else Decimal(), 2)) + '%',
                    'rank': rank,
                    'email': user_data_map[item.user_id].email,
                    'user_type': UsersResource.USER_TYPES[user_data_map[item.user_id].user_type],
                })

        if kwargs['export']:
            return export_xlsx(
                filename='spot_trade_user',
                data_list=records,
                export_headers=cls.export_headers
            )

        total_amount = query.with_entities(
            func.sum(UserTradeSummary.trade_amount)
        ).scalar() or Decimal()

        fee_result = fee_query.with_entities(
            func.sum(UserTradeFeeSummary.trade_fee_amount).label(
                'total_amount')
        ).first()
        total_fee_amount = fee_result.total_amount \
            if fee_result.total_amount else Decimal()
        count_query = query.with_entities(
            func.count(distinct(UserTradeSummary.user_id)).label('user_count')
        ).first()
        total_count = count_query.user_count if count_query else 0

        return dict(
            total=total,
            records=records,
            summary_data=dict(
                total_amount=quantize_amount(total_amount, 2),
                total_fee_amount=quantize_amount(total_fee_amount, 2),
                fee_ratio=(str(quantize_amount((total_fee_amount / total_amount * 100),
                                               2)) if total_amount else '0') + '%',
                total_count=total_count
            ),
            user_types={item.name: gettext(item.value) for item in User.UserType}
        )


@ns.route('/user-trade-fee-summary-rank')
@respond_with_code
class UserTradeFeeSummaryRank(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        id=wa_fields.Integer(missing=None),
        start_date=wa_fields.String(required=True),
        end_date=wa_fields.String(required=True),
        page=ex_fields.PageField(unlimited=True),
        sort_type=wa_fields.String(missing='trade_amount'),
        limit=ex_fields.LimitField,
        user_type=wa_fields.String
    ))
    def get(cls, **kwargs):
        """币币-交易排名-用户总成交手续费排名"""
        from .users import UsersResource
        user_id = kwargs['id']
        start_date = kwargs['start_date']
        end_date = kwargs['end_date']
        page = kwargs['page']
        limit = kwargs['limit']

        query = UserTradeSummary.query.filter(
            UserTradeSummary.system == UserTradeSummary.System.SPOT,
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date <= end_date,
        )
        fee_query = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.system == UserTradeFeeSummary.System.SPOT,
            UserTradeFeeSummary.report_date >= start_date,
            UserTradeFeeSummary.report_date <= end_date,
        )
        if user_type := kwargs.get('user_type'):
            fee_query = fee_query.join(User).filter(
                UserTradeFeeSummary.user_id == User.id,
                User.user_type == user_type,
            )
            query = query.join(User).filter(
                UserTradeSummary.user_id == User.id,
                User.user_type == user_type,
            )

        records = []
        if user_id:
            user = User.query.get(user_id)
            fee_query = fee_query.filter(
                UserTradeFeeSummary.user_id == user_id
            ).order_by(
                UserTradeFeeSummary.report_date.desc()
            )
            query = query.filter(UserTradeSummary.user_id == user_id)
            user_trade_amount = query.all()

            user_trade_amount_date_map = {item.report_date: item.trade_amount for item in user_trade_amount}

            pagination = fee_query.paginate(page, limit, error_out=False)
            for item in pagination.items:
                records.append({
                    'report_date': item.report_date.strftime("%Y-%m-%d"),
                    'user_id': item.user_id,
                    'trade_amount': user_trade_amount_date_map[item.report_date],
                    'trade_fee_amount': item.trade_fee_amount,
                    'fee_ratio': amount_to_str(
                        item.trade_fee_amount / user_trade_amount_date_map[item.report_date] * 100, 2) + '%',
                    'rank': item.rank,
                    'email': user.main_user_email,
                    'user_type': UsersResource.USER_TYPES[user.user_type]
                })
        else:
            user_fee_query = fee_query.with_entities(
                func.sum(UserTradeFeeSummary.trade_fee_amount).label('trade_fee_amount'),
                UserTradeFeeSummary.user_id
            ).group_by(
                UserTradeFeeSummary.user_id
            ).order_by(
                func.sum(UserTradeFeeSummary.trade_fee_amount).desc()
            )

            pagination = user_fee_query.paginate(page, limit, error_out=False)

            pagination_items = list(pagination.items)
            user_ids = [item.user_id for item in pagination_items]
            user_trade_amount = query.filter(UserTradeSummary.user_id.in_(user_ids)).with_entities(
                func.sum(UserTradeSummary.trade_amount).label('trade_amount'),
                UserTradeSummary.user_id
            ).group_by(
                UserTradeSummary.user_id
            ).all()
            user_trade_amount_map = defaultdict(Decimal)
            for _user in user_trade_amount:
                user_trade_amount_map[_user.user_id] = _user.trade_amount
            rank = (page - 1) * limit
            user_data_map = {i.id: i for i in User.query.filter(User.id.in_(user_ids))}
            for item in pagination_items:
                rank += 1
                records.append({
                    'report_date': '{start_date} - {end_date}'.format(
                        start_date=start_date, end_date=end_date),
                    'user_id': item.user_id,
                    'trade_amount': user_trade_amount_map[item.user_id],
                    'trade_fee_amount': item.trade_fee_amount,
                    'fee_ratio': amount_to_str(
                        item.trade_fee_amount / user_trade_amount_map[item.user_id] * 100 if user_trade_amount_map[
                            item.user_id] else Decimal(), 2) + '%',
                    'rank': rank,
                    'email': user_data_map[item.user_id].email,
                    'user_type': UsersResource.USER_TYPES[user_data_map[item.user_id].user_type],
                })
        amount_query = query.with_entities(
            func.sum(UserTradeSummary.trade_amount).label('total_amount')
        ).first()
        fee_result = fee_query.with_entities(
            func.sum(UserTradeFeeSummary.trade_fee_amount).label(
                'total_amount')
        ).first()
        total_amount = amount_query.total_amount \
            if amount_query.total_amount else Decimal()
        total_fee_amount = fee_result.total_amount \
            if fee_result.total_amount else Decimal()
        count_query = fee_query.with_entities(
            func.count(distinct(UserTradeFeeSummary.user_id)).label(
                'user_count')
        ).first()
        total_count = count_query.user_count if count_query else 0

        return dict(
            total=pagination.total,
            records=records,
            summary_data=dict(
                total_amount=quantize_amount(total_amount, 2),
                total_fee_amount=quantize_amount(total_fee_amount, 2),
                fee_ratio=(str(quantize_amount((total_fee_amount / total_amount * 100),
                                               2)) if total_amount else '0') + '%',
                total_count=total_count
            ),
            user_types={item.name: gettext(item.value) for item in User.UserType}
        )


@ns.route('/amm-market-config')
@respond_with_code
class AmmMarketConfig(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        trading_area=EnumField(Market.TradingArea),
        amm_type=EnumField(AmmMarket.AmmType),
        status=EnumField(AmmMarket.Status),
        keyword=wa_fields.String,
        page=wa_fields.Integer(missing=1),
        limit=wa_fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """币币-AMM-市场配置"""
        query = AmmMarket.query
        if (trading_area := kwargs.get('trading_area')) is not None:
            market_names = Market.query.filter(Market.trading_area == trading_area) \
                .with_entities(Market.name).all()
            query = query.filter(AmmMarket.name.in_([x for x, in market_names]))
        if (mode := kwargs.get('mode')) is not None:
            query = query.filter(AmmMarket.mode == mode)
        if (amm_type := kwargs.get('amm_type')) is not None:
            query = query.filter(AmmMarket.amm_type == amm_type)
        if (status := kwargs.get('status')) is not None:
            query = query.filter(AmmMarket.status == status)
        if keyword := kwargs.get('keyword'):
            # 不确定like查询是否会参数化，此处防止sql注入，即使是admin接口也不能信任。
            if len(keyword) > 12 or not re.fullmatch('[A-Za-z0-9]+', keyword):
                raise InvalidArgument
            query = query.filter(AmmMarket.name.like(f'%{keyword}%'))

        records = query.order_by(AmmMarket.id.desc()).paginate(kwargs['page'], kwargs['limit'])
        items = records.items
        markets = Market.query.filter(Market.name.in_([x.name for x in items])).all()
        markets = {x.name: x for x in markets}
        items = [row_to_dict(x, enum_to_name=True) for x in items]
        for item in items:
            item['trading_area'] = markets[item['name']].trading_area.name
            item['allow_in_bidding'] = bool(item['allow_in_bidding'])
        return dict(
            total=records.total,
            items=items,
            amm_types=AmmMarket.AmmType,
            statuses=AmmMarket.Status,
            trading_areas=Market.TradingArea,
            markets=MarketCache.list_online_markets()
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        amm_type=EnumField(AmmMarket.AmmType, required=True),
        fee_refund_rate=wa_fields.Decimal(
            places=PrecisionEnum.RATE_PLACES,
            rounding=decimal.ROUND_DOWN,
            required=True,
        ),
        delta=PositiveDecimalField(
            places=PrecisionEnum.PRICE_PLACES,
            allow_zero=True,
            rounding=decimal.ROUND_DOWN,
        ),
        initial_price=PositiveDecimalField(
            places=PrecisionEnum.PRICE_PLACES,
            allow_zero=True,
            rounding=decimal.ROUND_DOWN,
        ),
        allow_in_bidding=wa_fields.Boolean(default=False),
        min_price=PositiveDecimalField(
            places=PrecisionEnum.PRICE_PLACES,
            allow_zero=True,
            rounding=decimal.ROUND_DOWN,
        ),
        max_price=PositiveDecimalField(
            places=PrecisionEnum.PRICE_PLACES,
            allow_zero=True,
            rounding=decimal.ROUND_DOWN,
        ),
    ))
    def post(cls, **kwargs):
        """币币-AMM-添加市场"""
        if (name := kwargs['name']) not in MarketCache.list_online_markets():
            raise InvalidArgument('市场不存在')
        asset = MarketCache(name).dict['base_asset']
        if asset in list_pre_assets():
            if kwargs['amm_type'] == AmmMarket.AmmType.FINITE:
                if not (max_price := kwargs.get('max_price')):
                    raise InvalidArgument('缺少最高价')
                pre_cfg = get_pre_asset_config(asset)
                if max_price > pre_cfg.pledge_ratio:
                    raise InvalidArgument('最高价不能大于质押比例')

        row = AmmMarket.query.filter(AmmMarket.name == name).first()
        if row:
            raise InvalidArgument('市场已存在')

        fee_refund_rate = quantize_amount(kwargs['fee_refund_rate'], 4)
        if not 0 <= fee_refund_rate <= 1:
            raise InvalidArgument

        if (amm_type := kwargs['amm_type']) == AmmMarket.AmmType.FINITE:
            if not (delta := kwargs.get('delta')):
                raise InvalidArgument
            if not (initial_price := kwargs.get('initial_price')):
                raise InvalidArgument
            if not (min_price := kwargs.get('min_price')):
                raise InvalidArgument
            if not (max_price := kwargs.get('max_price')):
                raise InvalidArgument
            if delta <= 0 or initial_price <= 0 or not (0 < min_price < max_price):
                raise InvalidArgument
        else:
            delta = 0
            initial_price = 0
            min_price = 0
            max_price = 0

        row = AmmMarket()
        row.name = name
        row.amm_type = amm_type
        row.fee_refund_rate = fee_refund_rate
        row.delta = delta
        row.initial_price = initial_price
        row.min_price = min_price
        row.max_price = max_price
        row.allow_in_bidding = kwargs.get("allow_in_bidding", False)
        db.session.add(row)

        user, _ = LiquidityPool.derive_system_user(row.name)
        db.session.add(LiquidityPool(
            market=row.name,
            liquidity=0,
            system_user_id=user.id
        ))
        db.session.commit()
        update_amm_markets()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.AMMMarketConfig,
            detail=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        amm_type=EnumField(AmmMarket.AmmType, required=True),
        fee_refund_rate=wa_fields.Decimal(
            places=PrecisionEnum.RATE_PLACES,
            rounding=decimal.ROUND_DOWN,
            required=True,
        ),
        delta=PositiveDecimalField(
            places=PrecisionEnum.PRICE_PLACES,
            allow_zero=True,
            rounding=decimal.ROUND_DOWN,
        ),
        initial_price=PositiveDecimalField(
            places=PrecisionEnum.PRICE_PLACES,
            allow_zero=True,
            rounding=decimal.ROUND_DOWN,
        ),
        allow_in_bidding=wa_fields.Boolean(default=False),
        min_price=PositiveDecimalField(
            places=PrecisionEnum.PRICE_PLACES,
            allow_zero=True,
            rounding=decimal.ROUND_DOWN,
        ),
        max_price=PositiveDecimalField(
            places=PrecisionEnum.PRICE_PLACES,
            allow_zero=True,
            rounding=decimal.ROUND_DOWN,
        ),
    ))
    def put(cls, **kwargs):
        """币币-AMM-编辑市场"""
        row = AmmMarket.query.filter(AmmMarket.name == kwargs['name']).first()
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)

        asset = MarketCache(kwargs['name']).dict['base_asset']
        if asset in list_pre_assets():
            if kwargs['amm_type'] == AmmMarket.AmmType.FINITE:
                if not (max_price := kwargs.get('max_price')):
                    raise InvalidArgument('缺少最高价')
                pre_cfg = get_pre_asset_config(asset)
                if max_price > pre_cfg.pledge_ratio:
                    raise InvalidArgument('最高价不能大于质押比例')

        fee_refund_rate = quantize_amount(kwargs['fee_refund_rate'], 4)
        if not 0 <= fee_refund_rate <= 1:
            raise InvalidArgument

        if (amm_type := kwargs['amm_type']) == AmmMarket.AmmType.FINITE:
            if not (delta := kwargs.get('delta')):
                raise InvalidArgument
            if not (initial_price := kwargs.get('initial_price')):
                raise InvalidArgument
            if not (min_price := kwargs.get('min_price')):
                raise InvalidArgument
            if not (max_price := kwargs.get('max_price')):
                raise InvalidArgument
            if delta <= 0 or initial_price <= 0 or not (0 < min_price < max_price):
                raise InvalidArgument
        else:
            delta = 0
            initial_price = 0
            min_price = 0
            max_price = 0

        row.amm_type = amm_type
        row.delta = delta
        row.fee_refund_rate = fee_refund_rate
        row.initial_price = initial_price
        row.min_price = min_price
        row.max_price = max_price
        row.allow_in_bidding = kwargs.get("allow_in_bidding", False)
        db.session.commit()
        update_amm_markets()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.AMMMarketConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        status=EnumField(AmmMarket.Status, required=True)
    ))
    def patch(cls, **kwargs):
        """币币-AMM-上下架市场"""
        row = AmmMarket.query.filter(AmmMarket.name == kwargs['name']).first()
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)
        status = kwargs['status']
        if status == AmmMarket.Status.OFFLINE:
            if UserLiquidity.query.filter(
                    UserLiquidity.market == row.name,
                    UserLiquidity.liquidity > 0
            ).first():
                raise InvalidArgument(message="当前市场存在流动性，不能下架")
        row.status = kwargs['status']
        db.session.commit()
        update_amm_markets()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.AMMMarketConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True)
    ))
    def delete(cls, **kwargs):
        """币币-AMM-撤销流动性"""
        row = AmmMarket.query.filter(AmmMarket.name == kwargs['name']).first()
        if not row:
            raise InvalidArgument
        market = kwargs['name']
        close_liqudity_pool(market)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.AMMMarketConfig,
            detail=dict(name=row.name, amm_type=row.amm_type.value),
        )


@ns.route('/amm-markets')
@respond_with_code
class AMMMarketResource(Resource):
    class OrderType(Enum):
        market = '交易对'
        liquidity_usd = '流动性市值'
        deal_amount = '7日成交额'
        liquidity_count = '提供流动性用户'
        profit_rate = '年化收益率'

    @classmethod
    @ns.use_kwargs(dict(
        order=EnumField(OrderType, missing=OrderType.market),
        market=wa_fields.String,
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """币币-AMM市场列表"""
        res = []
        market = kwargs.get('market')
        order = kwargs['order']
        page = kwargs['page']
        limit = kwargs['limit']
        all_markets = AmmMarketCache.list_amm_markets()
        history = UserLiquidity.query.filter(
            UserLiquidity.liquidity > 0
        ).group_by(UserLiquidity.market).with_entities(
            UserLiquidity.market,
            func.count('*')
        ).all()
        liquidity_count_map = dict(history)  # 提供流动性用户人数
        market_data = json.loads(LiquidityPoolCache().read())
        if market:
            market_data = list(filter(lambda x: x['market'] == market, market_data))
        for item in market_data:
            res.append(dict(
                market=item['market'],
                liquidity=Decimal(item['liquidity']),
                liquidity_usd=quantize_amount(item['liquidity_usd'], 2),
                base_amount=Decimal(item['base_amount']),
                quote_amount=Decimal(item['quote_amount']),
                base_asset=item['base_asset'],
                quote_asset=item['quote_asset'],
                liquidity_count=liquidity_count_map.get(item['market'], 0),
                deal_amount=quantize_amount(item['deal_usd'], 2),
                fee_amount=quantize_amount(item['fee_usd'], 2),
                profit_rate=quantize_amount(item['profit_rate'], 4)
            ))
        total_refund_fee = sum(Decimal(item['fee_amount']) for item in res)
        total_liq_usd = sum(Decimal(item['liquidity_usd']) for item in res)
        if market:
            total_profit_rate = res[0]['profit_rate']
        else:
            if total_liq_usd > 0:
                total_profit_rate = total_refund_fee / total_liq_usd * 365 / 7
            else:
                total_profit_rate = Decimal()
        total_profit_rate = amount_to_str(total_profit_rate, 4)

        total_liquidity_count_query = UserLiquidity.query.filter(
            UserLiquidity.liquidity > 0
        ).with_entities(
            func.count(UserLiquidity.user_id.distinct())
        )
        if market:
            total_liquidity_count_query = total_liquidity_count_query.filter(
                UserLiquidity.market == market
            )
        total_liquidity_count = total_liquidity_count_query.scalar() or 0
        total_record = dict(
            market="ALL",
            liquidity_count=total_liquidity_count,
            liquidity_usd=total_liq_usd,
            deal_amount=sum(Decimal(item['deal_amount']) for item in res),
            fee_amount=total_refund_fee,
            profit_rate=total_profit_rate,
            all=True
        )
        if order != cls.OrderType.market:
            res.sort(key=lambda x: x[order.name], reverse=True)
        result = [total_record, ] + res[(page - 1) * limit: page * limit - 1]
        return dict(
            total=len(res),
            items=result,
            markets=all_markets,
            orders=cls.OrderType
        )


@ns.route('/liquidity-rank')
@respond_with_code
class LiquidityRankResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String,
        user_id=wa_fields.Integer,
        page=ex_fields.PageField(unlimited=True, missing=1),
        limit=ex_fields.LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """
        币币-用户流动性排行
        """
        query = UserLiquidity.query.filter(
            UserLiquidity.liquidity > 0
        )
        page, limit = kwargs.get('page'), kwargs.get('limit')
        all_markets = AmmMarketCache.list_amm_markets()
        market = kwargs.get('market')
        if not market:
            market = all_markets[0]
        query = query.filter(
            UserLiquidity.market == market
        )
        if user_id := kwargs.get('user_id'):
            query = query.filter(
                UserLiquidity.user_id == user_id
            )
        rows = query.all()
        user_ids = list({item.user_id for item in rows})
        users = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(User.id, User.email).all()
        user_email_map = {u.id: u.email for u in users}
        res = []
        for row in rows:
            liq = row.liquidity
            srv = LiquidityService(row.market)
            base_amount, quote_amount = srv.liquidity_to_asset_amounts(liq)
            liq_usd = srv.asset_amounts_to_usd(base_amount, quote_amount)
            if srv.pool.liquidity > 0:
                share = quantize_amount(liq / srv.pool.liquidity, 4)
            else:
                share = Decimal()
            res.append(dict(
                user_id=row.user_id,
                email=user_email_map[row.user_id],
                market=row.market,
                base_amount=base_amount,
                base_asset=srv.market['base_asset'],
                quote_amount=quote_amount,
                quote_asset=srv.market['quote_asset'],
                total_liquidity=liq,
                liquidity_usd=quantize_amount(liq_usd, 2),
                share=share
            ))
        res.sort(key=lambda x: x['share'], reverse=True)
        total = len(res)
        res = res[(page - 1) * limit: page * limit]
        rank = (page - 1) * limit + 1
        for item in res:
            item['rank'] = rank
            rank += 1

        return dict(
            total=total,
            items=res,
            markets=all_markets
        )


@ns.route('/liquidity-history')
@respond_with_code
class LiquidityHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String,
        user_id=wa_fields.Integer,
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=ex_fields.PageField(unlimited=True, missing=1),
        limit=ex_fields.LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """
        币币-用户做市记录
        """
        page, limit = kwargs.get('page'), kwargs.get('limit')
        query = LiquidityHistory.query
        if start_time := kwargs.get('start_time'):
            query = query.filter(
                LiquidityHistory.created_at >= start_time
            )
        if end_time := kwargs.get('end_time'):
            query = query.filter(
                LiquidityHistory.created_at <= end_time
            )
        if user_id := kwargs.get('user_id'):
            query = query.filter(
                LiquidityHistory.user_id == user_id
            )
        if market := kwargs.get('market'):
            query = query.filter(
                LiquidityHistory.market == market
            )
        query = query.order_by(LiquidityHistory.id.desc())
        pagination = query.paginate(page, limit, error_out=False)
        user_ids = list({item.user_id for item in pagination.items})
        users = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(User.id, User.email).all()
        user_email_map = {u.id: u.email for u in users}
        res = []

        market_asset_map = dict()
        markets = {record.market for record in pagination.items}
        for market in markets:
            srv = LiquidityService(market)
            market_asset_map[market] = dict(
                base_asset=srv.market['base_asset'],
                quote_asset=srv.market['quote_asset']
            )
        for record in pagination.items:
            res.append(dict(
                id=record.id,
                time=record.created_at,
                user_id=record.user_id,
                status=record.status,
                email=user_email_map[record.user_id],
                market=record.market,
                business=record.business.name,
                base_amount=quantize_amount(record.base_amount, 8),
                quote_amount=quantize_amount(record.quote_amount, 8),
                base_asset=market_asset_map[record.market]['base_asset'],
                quote_asset=market_asset_map[record.market]['quote_asset'],
                liquidity=record.liquidity,
                liquidity_usd=quantize_amount(record.liquidity_usd, 2),
            ))
        return dict(
            total=pagination.total,
            items=res,
            businesses={business.name: gettext(business.value)
                        for business in LiquidityHistory.Business},
            markets=AmmMarketCache.list_amm_markets()
        )
