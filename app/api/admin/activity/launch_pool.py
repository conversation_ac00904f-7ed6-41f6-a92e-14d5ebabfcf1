# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from decimal import Decimal
from enum import Enum
from sqlalchemy import func
from webargs import fields as wa_fields
from marshmallow import Schema, EXCLUDE
from flask import g

from app.api.common import (
    Namespace,
    respond_with_code,
    Resource,
    fields,
    lock_request,
)
from app.api.common.fields import PageField, LimitField, EnumField, TimestampField, PositiveDecimalField

from app.common import Language, ADMIN_EXPORT_LIMIT
from app.exceptions import InvalidArgument
from app.models import (
    db,
    User,
    Activity,
    AssetPrice,
    CoinInformation,
)
from app.models.activity import (
    LaunchMiningProject,
    LaunchMiningPool,
    LaunchMiningPoolUserInfo,
    LaunchMiningPoolOperateHistory,
)
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.business import PriceManager
from app.business.activity.launch_pool import project_pools_retrieve_all_stake, update_pcj_pool_locked_stake_amount
from app.utils import quantize_amount, amount_to_str, batch_iter, export_xlsx, now
from app.utils.date_ import convert_datetime


ns = Namespace("Admin Launch Pool Mining")


class PoolSchema(Schema):
    stake_asset = wa_fields.String(required=True)
    user_min_stake_amount = PositiveDecimalField(required=True)
    user_max_stake_amount = PositiveDecimalField(required=True)
    reward_total_amount = PositiveDecimalField(required=True)
    reward_mode = EnumField(LaunchMiningPool.RewardMode, required=True)

    class Meta:
        UNKNOWN = EXCLUDE


class LaunchProjectMixin:
    @classmethod
    def get_validated_params(cls, kwargs: dict) -> dict:
        validated_params = kwargs.copy()

        now_ = now()
        start_time = kwargs["start_time"] = convert_datetime(kwargs["start_time"], 'minute')
        end_time = kwargs["end_time"] = convert_datetime(kwargs["end_time"], 'minute')
        if start_time <= now_:
            raise InvalidArgument(message="开始时间不能小于当前时间")
        if start_time >= end_time:
            raise InvalidArgument(message="开始时间大于结束时间")
        auto_online_time = kwargs.get('auto_online_time')
        if auto_online_time and auto_online_time >= start_time:
            raise InvalidArgument(message="开始时间不得早于自动上架时间")
        forbid_hours = [0, 23]
        if start_time.hour in forbid_hours:
            raise InvalidArgument(message="一天的23:00 到次日1：00的时间不能被设置为【挖矿开始时间】")
        if end_time.hour in forbid_hours:
            raise InvalidArgument(message="一天的23:00 到次日1：00的时间不能被设置为【挖矿结束时间】")

        pools = kwargs["pools"]
        if not 1 <= len(pools) <= 3:
            raise InvalidArgument(message=f"质押代币配置项 数目为1～3条")
        if sum([p['reward_total_amount'] for p in pools]) != kwargs['reward_total_amount']:
            raise InvalidArgument(message=f"质押代币配置项的分配奖励数目之和 不等于 奖励代币总数量")
        pool_stake_assets = {i['stake_asset'] for i in pools}
        if len(pool_stake_assets) != len(pools):
            raise InvalidArgument(message=f"质押代币配置项 质押币种重复")
        for pool in pools:
            if pool['user_min_stake_amount'] > pool['user_max_stake_amount']:
                raise InvalidArgument(message=f"最小存入 大于 单用户质押数量上限")
        reward_asset = kwargs['reward_asset']
        q_assets = pool_stake_assets | {reward_asset}
        coin_rows = CoinInformation.query.filter(
            CoinInformation.code.in_(q_assets)
        ).with_entities(CoinInformation.code).all()
        db_q_assets = {i.code for i in coin_rows}
        if diff_assets := (q_assets - db_q_assets):
            raise InvalidArgument(message=f"币种{diff_assets}不存在")

        return validated_params

    @classmethod
    def new_pool_rows(cls, pcj: LaunchMiningProject, pool_data_list: list[dict]) -> list[LaunchMiningPool]:
        pool_rows = []
        for p_data in pool_data_list:
            pool: LaunchMiningPool = LaunchMiningPool.get_or_create(
                project_id=pcj.id,
                stake_asset=p_data['stake_asset'],
            )
            pool.user_min_stake_amount = p_data['user_min_stake_amount']
            pool.user_max_stake_amount = p_data['user_max_stake_amount']
            pool.reward_total_amount = p_data['reward_total_amount']
            pool.reward_mode = p_data['reward_mode']
            pool.reward_asset = pcj.reward_asset
            pool.status = LaunchMiningPool.Status.VALID
            db.session.add(pool)
            db.session.flush()
            pool_rows.append(pool)
        return pool_rows

    @classmethod
    def refresh_cache(cls):
        from app.schedules.activity import update_launch_pool_project_cache_schedule

        update_launch_pool_project_cache_schedule.delay()

    @classmethod
    def get_all_project_pool_infos(cls) -> tuple:
        pcj_rows = LaunchMiningProject.query.all()
        pool_rows = LaunchMiningPool.query.filter(
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).order_by(LaunchMiningPool.id.desc()).all()
        pcj_map = {r.id: r for r in pcj_rows}
        pcj_desc_map = {r.id: f"{r.id}-{r.name}" for r in pcj_rows}
        pool_desc_list = []
        for p in pool_rows:
            pcj = pcj_map[p.project_id]
            pd = f"{pcj.id}-{pcj.reward_asset}项目-{p.stake_asset}质押池"
            pool_desc_list.append(
                {"pool_id": p.id, "desc": pd}
            )
        return pcj_rows, pool_rows, pcj_desc_map, pool_desc_list


@ns.route("/project-list")
@respond_with_code
class LaunchMiningProjectListResource(Resource, LaunchProjectMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            reward_asset=wa_fields.String(),
            name=wa_fields.String(),
            status=EnumField(LaunchMiningProject.Status),
            active_status=EnumField(LaunchMiningProject.ActiveStatus),
            page=PageField(unlimited=True),
            limit=LimitField(missing=100),
        )
    )
    def get(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-列表 """
        model = LaunchMiningProject
        q = model.query.order_by(model.id.desc())
        if reward_asset := kwargs.get("reward_asset"):
            q = q.filter(model.reward_asset == reward_asset)
        if name := kwargs.get("name"):
            q = q.filter(model.name == name)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        else:
            q = q.filter(model.status != model.Status.DELETED)
        if active_status := kwargs.get("active_status"):
            now_ = now()
            if active_status == model.ActiveStatus.PREPARING:
                q = q.filter(model.start_time > now_)
            elif active_status == model.ActiveStatus.STARTED:
                q = q.filter(
                    model.start_time <= now_,
                    model.end_time > now_,
                )
            elif active_status == model.ActiveStatus.FINISHED:
                q = q.filter(model.end_time <= now_)

        page, limit = kwargs["page"], kwargs["limit"]
        pagination = q.paginate(page, limit)
        pcj_rows: list[LaunchMiningProject] = pagination.items
        pcj_ids = [v.id for v in pcj_rows]

        pool_rows: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id.in_(pcj_ids),
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID
        ).all()
        pcj_pools_map: dict[int, list[LaunchMiningPool]] = defaultdict(list)
        pool_max_stake_amount_map = {}
        pool_user_count_map = {}
        for p in pool_rows:
            pcj_pools_map[p.project_id].append(p)
            p_st_info = json.loads(p.statistics_info) if p.statistics_info else {}
            pool_max_stake_amount_map[p.id] = Decimal(p_st_info.get("max_stake_amount", 0))
            pool_user_count_map[p.id] = int(p_st_info.get("user_count", 0))

        reward_assets = {i.reward_asset for i in pcj_rows}
        coin_info_rows = CoinInformation.query.filter(
            CoinInformation.code.in_(reward_assets),
        ).with_entities(
            CoinInformation.code,
            CoinInformation.name,
        ).all()
        coin_full_name_map = dict(coin_info_rows)

        table_items = []
        cur_prices = PriceManager.assets_to_usd()
        dt_close_prices_map = {}
        for item in pcj_rows:
            if item.active_status == LaunchMiningProject.ActiveStatus.FINISHED or \
                    item.status == LaunchMiningProject.Status.CLOSED:
                fin_dt = (item.close_time or item.end_time).date()
                if fin_dt in dt_close_prices_map:
                    prices = dt_close_prices_map[fin_dt]
                else:
                    prices = AssetPrice.get_close_price_map(fin_dt)
                    dt_close_prices_map[fin_dt] = prices
            else:
                prices = cur_prices
            table_item = item.to_dict(enum_to_name=True)
            pjc_pools = pcj_pools_map[item.id]
            pool_items = []
            for p in pjc_pools:
                p1 = p.to_dict(enum_to_name=True)
                p1['raw_stake_amount'] = p.stake_amount
                p1['stake_amount'] = pool_max_stake_amount_map.get(p.id, p.stake_amount)
                p1['user_count'] = pool_user_count_map.get(p.id, 0)
                p1['stake_usd'] = quantize_amount(p1['stake_amount'] * prices.get(p.stake_asset, Decimal(0)), 8)
                pool_items.append(p1)
            table_item['pools'] = pool_items
            table_item['active_status'] = item.active_status.name
            table_item['name'] = coin_full_name_map.get(item.reward_asset, item.reward_asset)
            table_items.append(table_item)

        return dict(
            items=table_items,
            total=pagination.total,
            extra=dict(
                status_dict={i.name: i.value for i in model.Status},
                active_status_dict={i.name: i.value for i in model.ActiveStatus},
            ),
        )

    @classmethod
    @lock_request("admin_launch_project")
    @ns.use_kwargs(
        dict(
            name=wa_fields.String(required=True),
            start_time=TimestampField(required=True, is_ms=True),
            end_time=TimestampField(required=True, is_ms=True),
            auto_online_time=TimestampField(is_ms=True, allow_none=True),
            reward_asset=wa_fields.String(required=True),
            reward_total_amount=fields.PositiveDecimalField(required=True),
            rule_url=wa_fields.String(required=True),
            pools=wa_fields.Nested(PoolSchema, many=True, required=False),
        )
    )
    def post(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-新增Mining配置 """
        params = cls.get_validated_params(kwargs)
        reward_asset = params["reward_asset"]

        now_str = now().strftime("%Y-%m-%dT%H:%M:%S")
        p_active_name = f"launch_{reward_asset}_{now_str}"
        new_activity = Activity(name=p_active_name)
        db.session.add(new_activity)
        db.session.flush()

        pcj = LaunchMiningProject(
            name=reward_asset,
            reward_asset=reward_asset,
            reward_total_amount=params["reward_total_amount"],
            activity_id=new_activity.id,
            start_time=params["start_time"],
            end_time=params["end_time"],
            rule_url=params["rule_url"],
            auto_online_time=params.get('auto_online_time'),
        )
        db.session.add(pcj)
        db.session.flush()

        cls.new_pool_rows(pcj, params["pools"])
        db.session.commit()

        cls.refresh_cache()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MiningActivity,
            detail=kwargs,
        )

        return {"id": pcj.id}


@ns.route("/project-detail")
@respond_with_code
class LaunchMiningProjectDetailResource(Resource, LaunchProjectMixin):
    @classmethod
    def get_extra_info(cls):
        coin_info_rows = CoinInformation.query.with_entities(
            CoinInformation.code,
            CoinInformation.name,
        ).all()
        all_asset_dict = dict(coin_info_rows)
        price_dict = PriceManager.assets_to_usd()
        reward_mode_dict = {
            LaunchMiningPool.RewardMode.REAL_TIME.name: '存入即享小时收益',
            LaunchMiningPool.RewardMode.AFTER_EVENT.name: '存到活动结束享受收益',
        }
        return dict(
            all_asset_dict=all_asset_dict,
            price_dict=price_dict,
            reward_mode_dict=reward_mode_dict,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-详情页面 """
        pcj_id = kwargs.get("id")
        if not pcj_id:
            return {
                "detail": {},
                "extra": cls.get_extra_info(),
            }

        model = LaunchMiningProject
        pcj: model = model.query.filter(model.id == pcj_id).first()
        if not pcj:
            raise InvalidArgument(message="Mining活动不存在")

        pool_rows = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj_id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID
        ).all()

        detail = pcj.to_dict(enum_to_name=True)
        pool_items = []
        for p in pool_rows:
            p1 = p.to_dict(enum_to_name=True)
            if pcj.reward_total_amount:
                reward_total_percent = quantize_amount(p.reward_total_amount / pcj.reward_total_amount, 8)
            else:
                reward_total_percent = Decimal()
            p1['reward_total_percent'] = reward_total_percent
            pool_items.append(p1)
        detail['pools'] = pool_items
        return {
            "detail": detail,
            "extra": cls.get_extra_info(),
        }

    @classmethod
    @lock_request("admin_launch_project")
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
            name=wa_fields.String(required=True),
            start_time=TimestampField(required=True, is_ms=True),
            end_time=TimestampField(required=True, is_ms=True),
            auto_online_time=TimestampField(is_ms=True, allow_none=True),
            reward_asset=wa_fields.String(required=True),
            reward_total_amount=fields.PositiveDecimalField(required=True),
            rule_url=wa_fields.String(required=True),
            pools=wa_fields.Nested(PoolSchema, many=True, required=True),
        )
    )
    def put(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-修改Mining配置 """
        pcj_id = kwargs["id"]
        model = LaunchMiningProject
        pcj: model = model.query.filter(model.id == pcj_id).first()
        if not pcj:
            raise InvalidArgument(message="Ming活动不存在")
        old_data = pcj.to_dict(enum_to_name=True)
        if pcj.status in [
            model.Status.ONLINE,
            model.Status.OFFLINE,
            model.Status.CLOSED,
        ] or pcj.online_time:
            if kwargs["rule_url"] != pcj.rule_url:
                pcj.rule_url = kwargs["rule_url"]
                db.session.commit()
                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectOperation.MiningActivity,
                    old_data=old_data,
                    new_data=pcj.to_dict(enum_to_name=True),
                )
                cls.refresh_cache()
                return
            else:
                raise InvalidArgument("上架后不允许编辑（只支持修改【质押规则说明】）")

        params = cls.get_validated_params(kwargs)

        now_ = now()
        start_time = params["start_time"]
        end_time = params["end_time"]
        active_status = pcj.active_status

        if active_status in [model.ActiveStatus.PREPARING]:
            # 活动未开始
            if start_time <= now_:
                raise InvalidArgument(message="开始时间不能小于当前时间")

        if pcj.is_running or (
            active_status in [model.ActiveStatus.STARTED, model.ActiveStatus.FINISHED]
            and pcj.status == model.Status.ONLINE
        ):
            # 进行中 或 已结束
            raise InvalidArgument(message="活动开始后不可修改")

        pool_data_list = params["pools"]
        assert len(pool_data_list) > 0
        db_pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj_id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        # update or delete
        for db_pool in db_pools:
            for pool_d in pool_data_list:
                if db_pool.stake_asset == pool_d["stake_asset"]:
                    db_pool.user_min_stake_amount = pool_d["user_min_stake_amount"]
                    db_pool.user_max_stake_amount = pool_d["user_max_stake_amount"]
                    db_pool.reward_total_amount = pool_d["reward_total_amount"]
                    db_pool.reward_mode = pool_d["reward_mode"]
                    break
            else:
                db_pool.status = LaunchMiningPool.Status.DELETED
        # insert
        db_pool_assets = [i.stake_asset for i in db_pools]
        insert_pool_data_list = [c for c in pool_data_list if c["stake_asset"] not in db_pool_assets]
        if insert_pool_data_list:
            cls.new_pool_rows(pcj, insert_pool_data_list)

        pcj.status = model.Status.DRAFT
        pcj.start_time = start_time
        pcj.end_time = end_time
        pcj.reward_total_amount = kwargs["reward_total_amount"]
        pcj.rule_url = kwargs["rule_url"]
        pcj.auto_online_time = params.get('auto_online_time')
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MiningActivity,
            old_data=old_data,
            new_data=pcj.to_dict(enum_to_name=True),
        )

        cls.refresh_cache()


@ns.route("/project/submit")
@respond_with_code
class LaunchMiningProjectSubmitResource(Resource, LaunchProjectMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-提交 """
        pcj_id = kwargs["id"]
        model = LaunchMiningProject
        pcj: model = model.query.filter(model.id == pcj_id).first()
        if not pcj:
            raise InvalidArgument(message="Ming活动不存在")
        old_data = pcj.to_dict(enum_to_name=True)
        if pcj.status != model.Status.DRAFT:
            raise InvalidArgument(message="状态不是待提交，无法提交到待审核")
        pcj.status = model.Status.CREATED
        db.session.add(pcj)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MiningActivity,
            old_data=old_data,
            new_data=pcj.to_dict(enum_to_name=True),
        )


@ns.route("/project/audit")
@respond_with_code
class LaunchMiningProjectAuditResource(Resource, LaunchProjectMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-审核 """
        pcj_id = kwargs["id"]
        model = LaunchMiningProject
        pcj: model = model.query.filter(model.id == pcj_id).first()
        if not pcj:
            raise InvalidArgument(message="Ming活动不存在")
        if pcj.status != model.Status.CREATED:
            raise InvalidArgument(message="状态不是待审核，无法进行审核")
        pcj.status = model.Status.PENDING
        db.session.add(pcj)
        db.session.commit()

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MiningActivity,
            detail=dict(id=pcj_id, status=model.Status.PENDING),
        )


@ns.route("/project/online")
@respond_with_code
class LaunchMiningProjectOnlineResource(Resource, LaunchProjectMixin):

    @classmethod
    @lock_request("admin_launch_project")
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-上架 """
        pcj_id = kwargs["id"]
        model = LaunchMiningProject
        pcj: model = model.query.filter(model.id == pcj_id).first()
        if not pcj:
            raise InvalidArgument(message="Ming活动不存在")
        old_data = pcj.to_dict(enum_to_name=True)
        if pcj.status == model.Status.PENDING:
            if pcj.start_time <= now():
                raise InvalidArgument(message="开始时间小于当前时间，无法上架")
        elif pcj.status == model.Status.OFFLINE:
            if pcj.close_time:
                raise InvalidArgument(message="该活动是暂停挖矿，无法上架")
        else:
            raise InvalidArgument(message="状态不是待上架或已下架，无法进行上架")
        if not pcj.online_time:
            pcj.online_time = now()
        pcj.status = model.Status.ONLINE
        db.session.add(pcj)
        db.session.commit()
        cls.refresh_cache()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MiningActivity,
            old_data=old_data,
            new_data=pcj.to_dict(enum_to_name=True),
        )


@ns.route("/project/offline")
@respond_with_code
class LaunchMiningProjectOfflineResource(Resource, LaunchProjectMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-下架 """
        pcj_id = kwargs["id"]
        model = LaunchMiningProject
        pcj: model = model.query.filter(model.id == pcj_id).first()
        if not pcj:
            raise InvalidArgument(message="Ming活动不存在")
        old_data = pcj.to_dict(enum_to_name=True)
        if pcj.status == model.Status.ONLINE:
            if pcj.active_status != model.ActiveStatus.FINISHED:
                raise InvalidArgument(message="进行状态不是已结束，无法进行下架")
        elif pcj.status == model.Status.CLOSED:
            pass
        else:
            raise InvalidArgument(message="状态不是已上架或已暂停，无法进行下架")
        pcj.status = model.Status.OFFLINE
        db.session.add(pcj)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MiningActivity,
            old_data=old_data,
            new_data=pcj.to_dict(enum_to_name=True),
        )

        cls.refresh_cache()


@ns.route("/project/close")
@respond_with_code
class LaunchMiningProjectCloseResource(Resource, LaunchProjectMixin):

    @classmethod
    @lock_request("admin_launch_project")
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-关闭 """
        pcj_id = kwargs["id"]
        model = LaunchMiningProject
        pcj: model = model.query.filter(model.id == pcj_id).first()
        if not pcj:
            raise InvalidArgument(message="Ming活动不存在")
        old_data = pcj.to_dict(enum_to_name=True)
        if pcj.status != model.Status.ONLINE:
            raise InvalidArgument(message="状态不是已上架，无法进行关闭")
        pcj.status = model.Status.CLOSED
        pcj.close_time = now()
        db.session.add(pcj)
        db.session.commit()
        cls.refresh_cache()
        project_pools_retrieve_all_stake.delay(pcj_id)
        update_pcj_pool_locked_stake_amount.delay(pcj_id)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MiningActivity,
            old_data=old_data,
            new_data=pcj.to_dict(enum_to_name=True),
        )


@ns.route("/project/delete")
@respond_with_code
class LaunchMiningProjectDeleteResource(Resource, LaunchProjectMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-删除 """
        pcj_id = kwargs["id"]
        model = LaunchMiningProject
        pcj: model = model.query.filter(model.id == pcj_id).first()
        if not pcj:
            raise InvalidArgument(message="Ming活动不存在")
        if pcj.status in [
            model.Status.ONLINE,
            model.Status.OFFLINE,
            model.Status.CLOSED,
        ]:
            raise InvalidArgument(message="活动上架过，无法删除")
        pcj.status = model.Status.DELETED
        db.session.add(pcj)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MiningActivity,
            detail=kwargs,
        )


@ns.route("/join-user-list")
@respond_with_code
class LaunchMiningProjectJoinUsersResource(Resource, LaunchProjectMixin):
    class Order(Enum):
        mining_amount = '总挖矿量'
        signup_time = '报名时间'

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "user_email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "reward_asset", Language.ZH_HANS_CN: "Mining代币"},
        {"field": "name", Language.ZH_HANS_CN: "项目名称"},
        {"field": "stake_asset", Language.ZH_HANS_CN: "质押池"},
        {"field": "show_stake_amount", Language.ZH_HANS_CN: "质押数量"},
        {"field": "show_stake_usd", Language.ZH_HANS_CN: "质押价值"},
        {"field": "total_reward_amount", Language.ZH_HANS_CN: "奖励数量"},
        {"field": "reward_is_send", Language.ZH_HANS_CN: "是否发放"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        project_id=wa_fields.Integer,
        pool_ids=wa_fields.String,
        user_id=wa_fields.Integer,
        reward_status=EnumField(LaunchMiningPoolUserInfo.RewardStatus),
        start_time=wa_fields.DateTime,
        end_time=wa_fields.DateTime,
        export=wa_fields.Boolean(missing=False),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-用户参与明细 """
        all_project_pool_infos = cls.get_all_project_pool_infos()

        model = LaunchMiningPoolUserInfo
        q = model.query.order_by(model.id.desc())
        if pool_ids := kwargs.get('pool_ids'):
            pool_ids = [int(i) for i in pool_ids.split(',')]
            q = q.filter(model.pool_id.in_(pool_ids))
        if project_id := kwargs.get('project_id'):
            q = q.query.filter(model.project_id == project_id)
        if not project_id and not pool_ids:
            if all_project_pool_infos[0]:
                _pjc_ids = [i.id for i in all_project_pool_infos[0]]
                _pjc_ids.sort()
                q_pjc_ids = _pjc_ids[-20:]
                q = q.filter(model.project_id.in_(q_pjc_ids))
        if user_id := kwargs.get('user_id'):
            q = q.filter(model.user_id == user_id)
        if reward_status := kwargs.get('reward_status'):
            q = q.filter(model.reward_status == reward_status)
        if end_time := kwargs.get('end_time'):
            q = q.filter(model.created_at <= end_time)
        if start_time := kwargs.get('start_time'):
            q = q.filter(model.created_at >= start_time)

        is_export = kwargs['export']
        page, limit = kwargs["page"], kwargs["limit"]

        if not is_export:
            pagination = q.paginate(page, limit)
            rows: list[model] = pagination.items
            total = pagination.total
        else:
            rows = q.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(rows)

        st_rows = q.group_by(
            model.pool_id,
            model.user_id,
        ).with_entities(
            model.pool_id,
            model.user_id,
            func.sum(model.max_stake_amount).label('max_stake_amount')
        ).all()

        pool_ids = {item.pool_id for item in st_rows}
        pool_rows = LaunchMiningPool.query.filter(
            LaunchMiningPool.id.in_(pool_ids),
        ).all()
        pool_map = {p.id: p for p in pool_rows}

        reward_assets = {i.reward_asset for i in pool_rows}
        coin_info_rows = CoinInformation.query.filter(
            CoinInformation.code.in_(reward_assets),
        ).with_entities(
            CoinInformation.code,
            CoinInformation.name,
        ).all()
        coin_full_name_map = dict(coin_info_rows)

        prices = PriceManager.assets_to_usd()
        total_stake_usd = Decimal()
        st_user_ids = set()
        for st in st_rows:
            p = pool_map[st.pool_id]
            st_user_ids.add(st.user_id)
            max_stake_amount = st.max_stake_amount
            total_stake_usd += max_stake_amount * prices.get(p.stake_asset, Decimal(0))
        total_stake_usd = quantize_amount(total_stake_usd, 8)
        st_data = {
            "total_stake_user_count": len(st_user_ids),
            "total_stake_usd": total_stake_usd,
        }

        user_ids = {item.user_id for item in rows}
        user_email_map = dict()
        for ids in batch_iter(user_ids, 2000):
            users = User.query.filter(
                User.id.in_(ids)
            ).with_entities(
                User.id,
                User.email,
            ).all()
            user_email_map.update(dict(users))

        res = []
        for r in rows:
            d = r.to_dict(enum_to_name=True)
            pool: LaunchMiningPool = pool_map.get(r.pool_id)
            d['user_email'] = user_email_map.get(r.user_id, "")
            d['name'] = coin_full_name_map.get(pool.reward_asset, pool.reward_asset)
            d['reward_asset'] = pool.reward_asset
            d['stake_asset'] = pool.stake_asset
            show_stake_amount = r.stake_amount or r.max_stake_amount
            d['show_stake_amount'] = show_stake_amount
            stake_asset_price = prices.get(pool.stake_asset, Decimal(0))
            d['show_stake_usd'] = quantize_amount(show_stake_amount * stake_asset_price, 8)
            d['max_stake_usd'] = quantize_amount(r.max_stake_amount * stake_asset_price, 8)
            d['stake_usd'] = quantize_amount(r.stake_amount * stake_asset_price, 8)
            d["reward_is_send"] = "是" if r.reward_status == model.RewardStatus.FINISHED else "否"
            res.append(d)

        if is_export:
            for i in res:
                i["total_reward_amount"] = f'{amount_to_str(i["total_reward_amount"], 8)} {i["reward_asset"]}'
                i["show_stake_usd"] = f'{amount_to_str(i["stake_usd"], 8)}'
            return export_xlsx(
                filename='launch_pool_mining_join_user_list',
                data_list=res,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=res,
            statistics_data=st_data,
            extra=dict(
                pool_desc_list=all_project_pool_infos[3],
                reward_status_dict=model.RewardStatus,
            ),
        )


@ns.route("/operate-history")
@respond_with_code
class LaunchMiningProjectUserOperateHisResource(Resource, LaunchProjectMixin):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "user_email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "reward_asset", Language.ZH_HANS_CN: "Mining代币"},
        {"field": "stake_asset", Language.ZH_HANS_CN: "质押代币"},
        {"field": "type", Language.ZH_HANS_CN: "操作"},
        {"field": "amount", Language.ZH_HANS_CN: "存取数量"},
        {"field": "time", Language.ZH_HANS_CN: "操作时间"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        project_id=wa_fields.Integer,
        pool_id=wa_fields.Integer,
        pool_ids=wa_fields.String,
        user_id=wa_fields.Integer,
        type=EnumField(LaunchMiningPoolOperateHistory.Type),
        start_time=wa_fields.DateTime,
        end_time=wa_fields.DateTime,
        export=wa_fields.Boolean(missing=False),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-用户参与明细-质押流水 """
        model = LaunchMiningPoolOperateHistory
        row_types = [model.Type.STAKE, model.Type.RETRIEVE]
        all_project_pool_infos = cls.get_all_project_pool_infos()

        q = model.query.order_by(model.id.desc())
        q = q.filter(model.type.in_(row_types))
        if pool_id := kwargs.get('pool_id'):
            q = q.filter(model.pool_id == pool_id)
        if pool_ids := kwargs.get('pool_ids'):
            pool_ids = [int(i) for i in pool_ids.split(',')]
            q = q.filter(model.pool_id.in_(pool_ids))
        if project_id := kwargs.get('project_id'):
            q = q.query.filter(model.project_id == project_id)
        if user_id := kwargs.get('user_id'):
            q = q.filter(model.user_id == user_id)
        if start_time := kwargs.get('start_time'):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            q = q.filter(model.created_at <= end_time)
        if type_ := kwargs.get('type'):
            q = q.filter(model.type == type_)

        is_export = kwargs['export']
        page, limit = kwargs["page"], kwargs["limit"]

        if not is_export:
            pagination = q.paginate(page, limit)
            rows: list[model] = pagination.items
            total = pagination.total
        else:
            rows = q.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(rows)

        user_ids = {item.user_id for item in rows}
        user_email_map = dict()
        for ids in batch_iter(user_ids, 2000):
            users = User.query.filter(
                User.id.in_(ids)
            ).with_entities(
                User.id,
                User.email,
            ).all()
            user_email_map.update(dict(users))

        pool_ids = {item.pool_id for item in rows}
        pool_rows = LaunchMiningPool.query.filter(
            LaunchMiningPool.id.in_(pool_ids),
        ).all()
        pool_map = {p.id: p for p in pool_rows}
        res = []
        for r in rows:
            d = r.to_dict(enum_to_name=True)
            pool = pool_map.get(r.pool_id)
            d['user_email'] = user_email_map.get(r.user_id, "")
            d['pool_id'] = pool.id
            d['reward_asset'] = pool.reward_asset
            d['stake_asset'] = pool.stake_asset
            res.append(d)

        if kwargs['export']:
            for i in res:
                i["time"] = i["time"].strftime("%Y-%m-%d %H:%M:%S")
                i["type"] = model.Type[i["type"]].value
                i["amount"] = amount_to_str(i["amount"], 8)
            return export_xlsx(
                filename='launch_pool_mining_user_operate_history',
                data_list=res,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=res,
            extra=dict(
                pool_desc_list=all_project_pool_infos[3],
                type_dict={i.name: i.value for i in row_types},
            ),
        )


@ns.route("/reward-history")
@respond_with_code
class LaunchMiningProjectUserRewardHisResource(Resource, LaunchProjectMixin):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "user_email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "reward_asset", Language.ZH_HANS_CN: "Mining代币"},
        {"field": "stake_asset", Language.ZH_HANS_CN: "质押代币"},
        {"field": "type", Language.ZH_HANS_CN: "操作"},
        {"field": "amount", Language.ZH_HANS_CN: "数量"},
        {"field": "time", Language.ZH_HANS_CN: "操作时间"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        project_id=wa_fields.Integer,
        pool_id=wa_fields.Integer,
        user_id=wa_fields.Integer,
        type=EnumField(LaunchMiningPoolOperateHistory.Type),
        start_time=wa_fields.DateTime,
        end_time=wa_fields.DateTime,
        export=wa_fields.Boolean(missing=False),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """ 运营>活动>Ming>Ming管理-用户参与明细-收益历史 """
        model = LaunchMiningPoolOperateHistory
        rows_types = [model.Type.REWARD_SETTLEMENT, model.Type.REWARD_SEND]
        all_project_pool_infos = cls.get_all_project_pool_infos()

        q = model.query.order_by(model.id.desc())
        q = q.filter(model.type.in_(rows_types))
        if pool_id := kwargs.get('pool_id'):
            q = q.filter(model.pool_id == pool_id)
        if project_id := kwargs.get('project_id'):
            q = q.query.filter(model.project_id == project_id)
        if user_id := kwargs.get('user_id'):
            q = q.filter(model.user_id == user_id)
        if type_ := kwargs.get('type'):
            q = q.filter(model.type == type_)
        if start_time := kwargs.get('start_time'):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            q = q.filter(model.created_at <= end_time)

        is_export = kwargs['export']
        page, limit = kwargs["page"], kwargs["limit"]

        if not is_export:
            pagination = q.paginate(page, limit)
            rows: list[model] = pagination.items
            total = pagination.total
        else:
            rows = q.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(rows)

        user_ids = {item.user_id for item in rows}
        user_email_map = dict()
        for ids in batch_iter(user_ids, 2000):
            users = User.query.filter(
                User.id.in_(ids),
            ).with_entities(
                User.id,
                User.email,
            ).all()
            user_email_map.update(dict(users))

        pool_ids = {item.pool_id for item in rows}
        pool_rows = LaunchMiningPool.query.filter(
            LaunchMiningPool.id.in_(pool_ids),
        ).all()
        pool_map = {p.id: p for p in pool_rows}
        res = []
        for r in rows:
            d = r.to_dict(enum_to_name=True)
            pool = pool_map.get(r.pool_id)
            d['user_email'] = user_email_map.get(r.user_id, "")
            d['reward_asset'] = pool.reward_asset
            d['stake_asset'] = pool.stake_asset
            d['pool_id'] = pool.id
            res.append(d)

        if kwargs['export']:
            for i in res:
                i["time"] = i["time"].strftime("%Y-%m-%d %H:%M:%S")
                i["type"] = model.Type[i["type"]].value
                i["amount"] = amount_to_str(i["amount"], 8)
            return export_xlsx(
                filename='launch_pool_mining_user_reward_history',
                data_list=res,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=res,
            extra=dict(
                pool_desc_list=all_project_pool_infos[3],
                type_dict={i.name: i.value for i in rows_types},
            ),
        )
