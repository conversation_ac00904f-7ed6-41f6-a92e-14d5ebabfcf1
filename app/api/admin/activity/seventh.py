from sqlalchemy import func
from webargs import fields as wa_fields

from app.api.common import Namespace
from app.api.common import respond_with_code
from app.api.common import Resource
from app.api.common.fields import EnumField
from app.api.common.fields import <PERSON>Field
from app.api.common.fields import LimitField

from app.caches.activity import SeventhCouponPoolCache

from app.models.user import User
from app.models.activity import Coupon
from app.models.activity import CouponPool
from app.models.activity import SeventhAnniversaryUser

ns = Namespace("Seventh Anniversary - seventh")


@ns.route('/user-rewards')
@respond_with_code
class UserRewardListResource(Resource):

    @classmethod
    def get_reward_info(cls) -> dict:
        default_remain_info = {
            k: v.value for k, v in SeventhAnniversaryUser.RewardRemainNumConfig.__members__.items()
        }

        received_reward_map = {
            i.actual_reward.name: i.received_count
            for i in SeventhAnniversaryUser.query.filter(
                SeventhAnniversaryUser.status == SeventhAnniversaryUser.Status.FINISHED,
            ).group_by(
                SeventhAnniversaryUser.actual_reward,
            ).with_entities(
                SeventhAnniversaryUser.actual_reward,
                func.count('*').label('received_count')
            ).all()
        }

        result = {}
        pool_data = SeventhCouponPoolCache().get_all_pool_data()
        for reward_type in SeventhAnniversaryUser.RewardType.__members__.keys():
            total_num = default_remain_info.get(reward_type, 0)
            received_num = received_reward_map.get(reward_type, 0)
            pool_id = 0
            if reward_type == SeventhAnniversaryUser.RewardType.CASHBACK_FEE_COUPON.name:
                pool_id = pool_data.get(Coupon.CouponType.CASHBACK_FEE.name, 0)
            elif reward_type == SeventhAnniversaryUser.RewardType.INVESTMENT_INCREASE_RATE_COUPON.name:
                pool_id = pool_data.get(Coupon.CouponType.INVESTMENT_INCREASE_RATE.name, 0)
            coupon_apply_id = CouponPool.query.get(pool_id).apply_coupon_id if pool_id else 0
            result[reward_type] = dict(
                reward_type=reward_type,
                reward_type_str=SeventhAnniversaryUser.RewardType[reward_type].value,
                total_num=total_num,
                received_num=received_num,
                pool_id=pool_id,
                coupon_apply_id=coupon_apply_id,
            )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer,
        actual_reward=EnumField(SeventhAnniversaryUser.RewardType),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """ 运营-活动-七周年 """
        query = SeventhAnniversaryUser.query.filter(
            SeventhAnniversaryUser.status == SeventhAnniversaryUser.Status.FINISHED,
        ).order_by(SeventhAnniversaryUser.received_at.desc())
        if user_id := kwargs.get('user_id'):
            query = query.filter(SeventhAnniversaryUser.user_id == user_id)
        if actual_reward := kwargs.get('actual_reward'):
            query = query.filter(SeventhAnniversaryUser.actual_reward == actual_reward)
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        total = pagination.total
        rows = pagination.items

        user_email_map = {
            i.id: i.email
            for i in User.query.filter(
                User.id.in_({i.user_id for i in rows})
            ).with_entities(
                User.id, User.email,
            ).all()
        }

        items = []
        for row in rows:
            row: SeventhAnniversaryUser
            d = row.to_dict(enum_to_name=True)
            d['email'] = user_email_map[row.user_id]
            items.append(d)

        return dict(
            items=items,
            total=total,
            extra=dict(
                reward_type_dict={i.name: i.value for i in SeventhAnniversaryUser.RewardType},
                reward_info=cls.get_reward_info(),
            ),
        )
