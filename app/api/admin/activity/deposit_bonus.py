import json
from decimal import Decimal
from collections import defaultdict
from datetime import <PERSON><PERSON><PERSON>

from flask import g, request
from sqlalchemy import func, and_
from webargs import fields as wa_fields
from marshmallow import Schema, EXCLUDE

from app.api.common import (
    Namespace,
    respond_with_code,
    Resource,
    fields,
)
from app.api.common.fields import TimestampField
from app.assets import asset_to_chains
from app.business import ServerClient, CacheLock, LockKeys
from app.business.activity.airdrop import get_airdrop_coupon_details
from app.business.activity.anti_fraud import AntiFraudHelper
from app.business.activity.deposit_bonus import DepositBonusActivityBusiness
from app.business.gift import send_deposit_bonus_activity_reward_task
from app.business.coupon import CouponTool
from app.business.user import UserRepository
from app.business.lock_asset import get_user_id_from_email
from app.config import config
from app.caches.activity import DepositBonusActivityCache, DepositBonusActivityDetailCache, \
    DepositBonusActivityBannerCache

from app.common import Language, language_cn_names, ADMIN_EXPORT_LIMIT
from app.exceptions import InvalidArgument
from app.models import (
    db, Activity, User,
    CoinInformation, ActivityCondition,
    AntiFraudScore, DepositBonusActivity, DepositBonusActivityConfig,
    DepositBonusActivityCondition, DepositBonusActivityApplyUser, DepositBonusActivityUserInfo,
    DepositBonusActivityContent
)
from app.models.activity import Coupon, CouponApply, CouponPool
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.schedules.activity import update_deposit_bonus_statistic_schedule
from app.schedules.reports.admin_async_download import async_download_deposit_bonus_history_report
from app.utils import now, batch_iter, amount_to_str, export_xlsx
from app.utils.date_ import current_timestamp
from app.utils.parser import JsonEncoder
from app.utils.importer import get_table_rows

ns = Namespace("Activity - Deposit Bonus")


class DepositBonusMixin:

    @classmethod
    def _get_row(cls, id_):
        model = cls.model
        row = model.query.filter(
            model.id == id_
        ).first()
        if not row:
            raise InvalidArgument
        return row

    @classmethod
    def _get_asset_to_chains(cls):
        model = CoinInformation
        rows = model.query.with_entities(
            model.code
        ).filter(
            model.status == model.Status.VALID
        ).all()
        ret = defaultdict(list)
        for row in rows:
            ret[row.code] = asset_to_chains(row.code)
        return ret

    @classmethod
    def get_activity_configs(cls, deposit_bonus_ids: list[int]) -> dict[int, list]:
        model = DepositBonusActivityConfig
        rows = model.query.filter(
            model.deposit_bonus_id.in_(deposit_bonus_ids)
        ).order_by(model.id.asc()).all()
        ret = defaultdict(list)
        for row in rows:
            ret[row.deposit_bonus_id].append({
                'id': row.id,
                'activity_id': row.activity_id,
                'mode': row.mode.name,
                'flag': row.flag.name,
                'raw_gift_rules': row.gift_rules,
                'gift_rules': row.cached_gift_rules,
            })
        return ret

    @classmethod
    def _build_activity_config_display(cls, items: list):
        for item in items:
            gift_rules_display = []
            obj = DepositBonusActivityConfig(
                activity_id=item['activity_id'],
                gift_rules=item['raw_gift_rules'],
                mode=item['mode'],
            )
            # 对于子活动，展示汇总处理
            for asset, amount in obj.get_gift_assets().items():
                gift_rules_display.append(dict(
                    gift_type=DepositBonusActivityConfig.GiftType.ASSET.name,
                    gift_asset=asset,
                    rank_total=amount,
                    left_amount=obj.left_gift_amount(
                        Decimal(amount),
                        asset
                    ),
                ))
            for apply_id, amount in obj.get_gift_coupon_applys().items():
                coupon_info = cls.get_coupon_info(apply_ids=[int(apply_id)])
                rank_total, left_amount = amount, 0
                coupons = coupon_info.get(int(apply_id), {})
                if coupons.get('total_count'):
                    # rank_total = coupons['total_count']
                    left_amount = rank_total - coupons['send_count']
                gift_rules_display.append(dict(
                    gift_type=DepositBonusActivityConfig.GiftType.COUPON.name,
                    rank_total=rank_total,
                    left_amount=left_amount,
                ))
            item['gift_rules_display'] = gift_rules_display

    @classmethod
    def get_coupon_info(cls, apply_ids: list[int]) -> dict:
        rows = CouponApply.query.filter(
            CouponApply.id.in_(apply_ids)
        ).with_entities(
            CouponApply.id,
            CouponApply.title,
        ).all()
        pools = CouponPool.query.with_entities(
            CouponPool.id,
            CouponPool.apply_coupon_id,
            CouponPool.total_count,
            CouponPool.send_count,
        ).filter(
            CouponPool.apply_coupon_id.in_(apply_ids)
        ).all()
        apply_pools = {pool.apply_coupon_id: pool for pool in pools}
        ret = {}
        for row in rows:
            tmp = {
                row.id: {
                    'title': row.title,
                    'total_count': 0,
                    'send_count': 0,
                }
            }
            pool = apply_pools.get(row.id)
            if pool:
                tmp[row.id].update({
                    'total_count': pool.total_count,
                    'send_count': pool.send_count,
                })
            ret.update(tmp)
        return ret


@ns.route("/activity")
@respond_with_code
class DepositBonusActivityResource(DepositBonusMixin, Resource):
    model = DepositBonusActivity

    EMPTY_INTRODUCTIONS = "<div class=\"ql-editor\"></div>"

    @classmethod
    @ns.use_kwargs(
        dict(
            name=wa_fields.String,
            status=fields.EnumField(DepositBonusActivity.StatusType),
            active_status=fields.EnumField(DepositBonusActivity.ActiveStatus),
            page=fields.PageField(unlimited=True),
            limit=fields.LimitField(missing=30),
        )
    )
    def get(cls, **kwargs):
        """ 运营-活动-充值福利-列表 """
        model = cls.model
        query = model.query.filter(
            model.status != model.StatusType.DELETED
        ).order_by(model.id.desc())
        if name := kwargs.get("name"):
            query = query.filter(model.name.contains(name))
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)
        if active_status := kwargs.get("active_status"):
            now_ = now()
            if active_status is model.ActiveStatus.CREATED:
                query = query.filter(model.start_time > now_)
            elif active_status is model.ActiveStatus.STARTED:
                query = query.filter(
                    model.start_time <= now_,
                    model.end_time > now_,
                )
            elif active_status is model.ActiveStatus.FINISHED:
                query = query.filter(model.end_time <= now_)

        page, limit = kwargs["page"], kwargs["limit"]
        activities = query.paginate(page, limit)
        activity_ids = [v.id for v in activities.items]
        items = []
        activity_configs = cls.get_activity_configs(deposit_bonus_ids=activity_ids)
        apply_user_counts = cls._get_apply_user_counts(deposit_bonus_ids=activity_ids)
        new_apply_user_counts = cls._get_new_apply_user_counts(deposit_bonus_ids=activity_ids)
        thresholds = {x.id: x.threshold for x in activities.items}
        satisfied_user_counts = cls._get_satisfied_user_counts(deposit_bonus_thresholds=thresholds)
        apply_ids = set()
        for item in activities.items:
            activity_id = item.id
            config_items = activity_configs[activity_id]
            cls._build_activity_config_display(items=config_items)
            for cfg in config_items:
                for gift_rule in cfg['gift_rules']:
                    if gift_rule['gift_type'] == DepositBonusActivityConfig.GiftType.COUPON.name:
                        apply_ids.add(gift_rule['coupon_apply_id'])
            row_dict = item.to_dict(enum_to_name=True)
            row_dict.update(dict(
                activity_id=",".join([str(item["activity_id"]) for item in config_items]),
                active_status=item.active_status.name,
                config_items=config_items,
                user_count=apply_user_counts.get(activity_id, 0),
                new_user_count=new_apply_user_counts.get(activity_id, 0),
                satisfied_user_count=satisfied_user_counts.get(activity_id, 0),
            ))
            items.append(row_dict)
        coupon_details = {}
        if apply_ids:
            coupon_details = get_airdrop_coupon_details(apply_ids)
        return dict(
            items=items,
            total=activities.total,
            extra=dict(
                # asset_to_chains=cls._get_asset_to_chains(),
                statuses=model.StatusType,
                active_statuses=model.ActiveStatus,
                modes=DepositBonusActivityConfig.ActivityMode,
                flags=DepositBonusActivityConfig.Flag,
                gift_types=DepositBonusActivityConfig.GiftType,
                coupon_details=coupon_details,
                coupon_types=Coupon.CouponType,
            ),
        )

    @classmethod
    def _get_apply_user_counts(cls, deposit_bonus_ids):
        model = DepositBonusActivityApplyUser
        rows = model.query.with_entities(
            model.deposit_bonus_id,
            func.count(model.id)
        ).filter(
            model.deposit_bonus_id.in_(deposit_bonus_ids)
        ).group_by(
            model.deposit_bonus_id
        ).all()
        return dict(rows)

    @classmethod
    def _get_new_apply_user_counts(cls, deposit_bonus_ids):
        model = DepositBonusActivityUserInfo
        rows = model.query.with_entities(
            model.deposit_bonus_id,
            func.count(model.id)
        ).filter(
            model.deposit_bonus_id.in_(deposit_bonus_ids),
            model.type == model.Type.NEW,
        ).group_by(
            model.deposit_bonus_id
        ).all()
        return dict(rows)

    @classmethod
    def _get_satisfied_user_counts(cls, deposit_bonus_thresholds):
        model = DepositBonusActivityUserInfo
        rows = model.query.with_entities(
            model.deposit_bonus_id,
            model.net_amount
        ).filter(
            model.deposit_bonus_id.in_(deposit_bonus_thresholds.keys()),
        ).all()
        ret = defaultdict(Decimal)
        for row in rows:
            threshold = deposit_bonus_thresholds[row.deposit_bonus_id]
            if row.net_amount >= threshold:
                ret[row.deposit_bonus_id] += 1
        return ret

    @classmethod
    def _reload(cls):
        DepositBonusActivityCache.reload()
        DepositBonusActivityDetailCache.reload()
        DepositBonusActivityBannerCache.reload()
        update_deposit_bonus_statistic_schedule.delay()

    class ActivityConfigSchema(Schema):
        """ 子活动 """

        id = wa_fields.Integer(required=False)  # for put
        mode = fields.EnumField(DepositBonusActivityConfig.ActivityMode, required=True)
        flag = fields.EnumField(DepositBonusActivityConfig.Flag, missing=DepositBonusActivityConfig.Flag.NONE)
        gift_rules = wa_fields.List(wa_fields.Dict(), required=True)
        # conditions = wa_fields.Dict(required=True)
        VIP = fields.EnumField(ActivityCondition.VIPType, required=True)
        KYC = fields.EnumField(ActivityCondition.KYCType, required=True)
        NEED_HOLDING = fields.EnumField(ActivityCondition.NeedHoldingType,
                                        default=ActivityCondition.NeedHoldingType.NOT_LIMITED)
        ASSET_HOLDING = wa_fields.String(default='')
        TRADE_OP_TYPE = fields.EnumField(ActivityCondition.TradeValueOperateType,
                                         default=ActivityCondition.TradeValueOperateType.NOT_LIMITED)
        TRADE_BUSINESS_TYPE = fields.EnumField(ActivityCondition.TradeBusinessTypeKey,
                                               default=ActivityCondition.TradeBusinessTypeKey.REAL_TIME)
        TRADE_VALUE = wa_fields.Decimal(default=0)  # 累计交易额数值
        TRADE_TYPE_RANGE = wa_fields.List(wa_fields.String)
        TRADE_DAY_RANGE = wa_fields.Integer(default=7)  # 累计交易额时间范围
        BALANCE_OP_TYPE = fields.EnumField(ActivityCondition.BalanceValueOperateType,
                                           default=ActivityCondition.BalanceValueOperateType.NOT_LIMITED)
        BALANCE_VALUE = wa_fields.Decimal(default=0)  # 余额数值
        REGISTERED_OP_TYPE = fields.EnumField(
            ActivityCondition.RegisteredTimeType,
            default=ActivityCondition.BalanceValueOperateType.NOT_LIMITED
        )
        REGISTERED_VALUE = wa_fields.Integer()
        MARKET_MAKER = fields.EnumField(
            ActivityCondition.MarketMakerTypeKeys,
            default=ActivityCondition.MarketMakerTypeKeys.NOT_LIMITED
        )
        USED_VALUE = wa_fields.List(wa_fields.String)

        class Meta:
            UNKNOWN = EXCLUDE

    class DetailSchema(Schema):
        """ 多语言标题 """

        id = wa_fields.Integer(required=False)
        deposit_bonus_id = wa_fields.Integer(required=False)
        lang = fields.EnumField(Language, required=True)
        title = wa_fields.String(required=True)

        class Meta:
            UNKNOWN = EXCLUDE

    @classmethod
    @ns.use_kwargs(
        dict(
            start_time=TimestampField(is_ms=True, required=True),
            end_time=TimestampField(is_ms=True, required=True),
            zendesk_url=wa_fields.String(missing=''),
            asset=wa_fields.String(required=True),
            chains=wa_fields.List(wa_fields.String, required=True),
            threshold=fields.PositiveDecimalField(required=True),
            cover=wa_fields.String(required=True),

            configs=wa_fields.Nested(ActivityConfigSchema, many=True, required=True, unknown=EXCLUDE),
            details=wa_fields.Nested(DetailSchema, many=True, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营-活动-充值福利-添加活动 """
        model = cls.model
        kwargs = cls._get_validated_params(kwargs)
        details = kwargs["details"]
        details_mapping = {detail['lang']: detail for detail in details}
        cn_name = details_mapping[Language.ZH_HANS_CN]['title']
        row = model(
            name=cn_name,
            cover=kwargs['cover'],
            start_time=kwargs['start_time'],
            end_time=kwargs['end_time'],
            zendesk_url=kwargs['zendesk_url'],
            asset=kwargs["asset"],
            chains=json.dumps(kwargs["chains"]),
            threshold=kwargs["threshold"],
            status=model.StatusType.PENDING,
        )
        db.session_add_and_commit(row)

        c_model = DepositBonusActivityContent
        content_details = [
            c_model(
                deposit_bonus_id=row.id,
                lang=detail["lang"],
                title=detail['title'],
            )
            for detail in details
        ]
        db.session.bulk_save_objects(content_details)
        db.session.commit()

        cls._bulk_save_activity_configs(row.id, cn_name, kwargs['configs'])
        cls._reload()
        kwargs['id'] = row.id
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositActivity,
            detail=kwargs,
        )
        return {"id": row.id}

    @classmethod
    def _bulk_save_activity_configs(cls, deposit_bonus_id, cn_name, configs):
        # 添加子活动
        model = DepositBonusActivityConfig
        for idx, activity_config in enumerate(configs, start=1):
            activity_name = f'{cn_name}_{idx}_{current_timestamp(to_int=True)}'
            new_activity = Activity(name=activity_name)
            db.session_add_and_commit(new_activity)
            row = model(
                activity_id=new_activity.id,
                deposit_bonus_id=deposit_bonus_id,
                mode=activity_config['mode'],
                flag=activity_config['flag'],
                gift_rules=json.dumps(activity_config['gift_rules'], cls=JsonEncoder),
            )
            db.session_add_and_commit(row)
            cls._save_conditions(row.id, activity_config)

    @classmethod
    def _save_conditions(cls, activity_config_id, activity_config):
        model = DepositBonusActivityCondition
        for key in ActivityCondition.CONDITION_LIST:
            row = model.get_or_create(
                activity_config_id=activity_config_id,
                key=key,
            )
            if key in [
                ActivityCondition.ConditionKeys.VIP.value,
                ActivityCondition.ConditionKeys.KYC.value,
                ActivityCondition.ConditionKeys.MARKET_MAKER.value,
            ]:
                row.value = activity_config[key].value
            elif key == ActivityCondition.ConditionKeys.HOLDING.value:
                # 是否持有某个币种需要存两个条件
                value = json.dumps(dict(
                    NEED_HOLDING=activity_config['NEED_HOLDING'].value,
                    ASSET_HOLDING=activity_config['ASSET_HOLDING'],
                ))
                row.value = value
            elif key == ActivityCondition.ConditionKeys.BALANCE_VALUE.value:
                # 一定时间内累计交易额USD是否满足条件
                value = json.dumps(dict(
                    BALANCE_OP_TYPE=activity_config['BALANCE_OP_TYPE'].value,
                    BALANCE_VALUE=activity_config['BALANCE_VALUE'],
                ), cls=JsonEncoder)
                row.value = value
            elif key == ActivityCondition.ConditionKeys.TRADE_VALUE.value:
                # 总资产市值USD是否满足条件
                value = json.dumps(dict(
                    TRADE_OP_TYPE=activity_config['TRADE_OP_TYPE'].value,
                    TRADE_VALUE=activity_config['TRADE_VALUE'],
                    TRADE_DAY_RANGE=activity_config['TRADE_DAY_RANGE'],
                    TRADE_BUSINESS_TYPE=activity_config['TRADE_BUSINESS_TYPE'].value,
                    TRADE_TYPE_RANGE=activity_config['TRADE_TYPE_RANGE'],
                ), cls=JsonEncoder)
                row.value = value
            elif key == ActivityCondition.ConditionKeys.REGISTERED_VALUE.value:
                # 注册时间是否满足要求
                value = json.dumps(dict(
                    REGISTERED_OP_TYPE=activity_config['REGISTERED_OP_TYPE'].value,
                    REGISTERED_VALUE=activity_config.get('REGISTERED_VALUE', 0)
                ))
                row.value = value
            elif key == ActivityCondition.ConditionKeys.USED_VALUE.value:
                # 未使用过此功能
                value = ",".join(activity_config.get('USED_VALUE', []))
                row.value = value
            db.session.add(row)
        db.session.commit()

    @classmethod
    def _update_details(cls, activity_id: int, kwargs):
        details_mapping = {
            c_row.lang: c_row
            for c_row in DepositBonusActivityContent.query.filter(
                DepositBonusActivityContent.deposit_bonus_id == activity_id
            ).all()}
        for detail in kwargs['details']:
            # 语言都是必填的
            if c_row := details_mapping.get(detail['lang']):
                c_row.title = detail['title']
            else:
                db.session_add_and_commit(DepositBonusActivityContent(
                    deposit_bonus_id=activity_id,
                    lang=detail['lang'],
                    title=detail['title'],
                ))

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
            start_time=TimestampField(is_ms=True, required=True),
            end_time=TimestampField(is_ms=True, required=True),
            zendesk_url=wa_fields.String(missing=''),
            asset=wa_fields.String(required=True),
            chains=wa_fields.List(wa_fields.String, required=True),
            threshold=fields.PositiveDecimalField(required=True),
            cover=wa_fields.String(required=True),

            configs=wa_fields.Nested(ActivityConfigSchema, many=True, required=True, unknown=EXCLUDE),
            details=wa_fields.Nested(DetailSchema, many=True, required=True, unknown=EXCLUDE),
        )
    )
    def put(cls, **kwargs):
        """ 运营-活动-充值福利-编辑活动 """
        activity_id = kwargs["id"]
        row = cls._get_row(activity_id)
        if row.active_status == DepositBonusActivity.ActiveStatus.FINISHED:
            raise InvalidArgument(message='已完成的活动不能修改')

        old_data = row.to_dict(enum_to_name=True)
        kwargs = cls._get_validated_params(kwargs, edit=True)

        # zendesk_url, cover以及details修改没有限制
        row.zendesk_url = kwargs['zendesk_url']
        row.cover = kwargs['cover']
        cls._update_details(activity_id, kwargs)

        if row.active_status != DepositBonusActivity.ActiveStatus.STARTED:
            # 未开始时可以编辑所有内容，但是需要将已上架状态修改为未上架
            cls._update_activity_configs(activity_id, row.name, kwargs['configs'])
            if kwargs['start_time'] <= now():
                raise InvalidArgument(message="活动开始时间不能小于当前时间")
            row.start_time = kwargs['start_time']
            row.end_time = kwargs['end_time']
            row.asset = kwargs['asset']
            row.chains = json.dumps(kwargs['chains'])
            row.threshold = kwargs['threshold']
            if row.status in {
                cls.model.StatusType.OFFLINE,
                cls.model.StatusType.ONLINE,
            }:
                row.status = cls.model.StatusType.PENDING

        db.session.commit()
        cls._reload()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositActivity,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return {'id': row.id}

    @classmethod
    def _update_activity_configs(cls, activity_id, cn_name, configs):
        # update or delete or create
        model = DepositBonusActivityConfig
        rows = model.query.filter(
            model.deposit_bonus_id == activity_id
        ).all()
        config_mapping = {cfg.id: cfg for cfg in rows}
        updated = set()
        for idx, activity_config in enumerate(configs, start=1):
            if row := config_mapping.get(activity_config.get('id')):
                updated.add(row.id)
                row.mode = activity_config['mode']
                row.flag = activity_config['flag']
                row.gift_rules = json.dumps(activity_config['gift_rules'], cls=JsonEncoder)
                cls._save_conditions(row.id, activity_config)
            else:
                activity_name = f'{cn_name}_{idx}_{current_timestamp(to_int=True)}'
                new_activity = Activity(name=activity_name)
                db.session_add_and_commit(new_activity)
                row = model(
                    activity_id=new_activity.id,
                    deposit_bonus_id=activity_id,
                    mode=activity_config['mode'],
                    flag=activity_config['flag'],
                    gift_rules=json.dumps(activity_config['gift_rules'], cls=JsonEncoder),
                )
                db.session_add_and_commit(row)
                cls._save_conditions(row.id, activity_config)
        old_keys = set(config_mapping.keys()) - updated
        model.query.filter(
            model.id.in_(old_keys)
        ).delete()
        db.session.commit()

    @classmethod
    def _get_validated_params(cls, kwargs: dict, edit=False) -> dict:
        validated_params = kwargs.copy()
        cls._check_normal(validated_params, edit)
        cls._check_activity_configs(validated_params)
        cls._check_conditions(validated_params)
        cls._check_details(validated_params)
        return validated_params

    @classmethod
    def _check_normal(cls, kwargs, edit=False):
        if kwargs['start_time'] >= kwargs['end_time']:
            raise InvalidArgument(message='活动开始时间不能大于结束时间！')
        if not edit and kwargs['start_time'] <= now():
            raise InvalidArgument(message="活动开始时间不能小于当前时间")

    @classmethod
    def _check_activity_configs(cls, kwargs):
        configs = kwargs['configs']
        coupon_apply_ids = set()
        coupon_applys = defaultdict(int)
        for activity_config in configs:
            # 这里可能新增子活动，所以没有子活动 id
            # if edit is True and not activity_config['id']:
            #     raise InvalidArgument(message=f'子活动 id 不存在： {activity_config["id"]}')
            gift_rules = activity_config['gift_rules']
            if not gift_rules:
                raise InvalidArgument(message='活动奖励必须设置！')
            trimmed_rules = []
            apply_ids = set()
            for gift_rule in gift_rules:
                trimmed_rule = {
                    'gift_type': gift_rule['gift_type'],
                    'rank_min': int(gift_rule['rank_min']),
                    'rank_max': int(gift_rule['rank_max']),
                    'rank_limit': Decimal(gift_rule['rank_limit']),
                    'rank_total': Decimal(gift_rule['rank_total']),
                }
                if trimmed_rule['rank_max'] < trimmed_rule['rank_min']:
                    InvalidArgument(message='每段排名开始不能大于结束！')
                if trimmed_rule['rank_limit'] <= 0 or trimmed_rule['rank_total'] <= 0:
                    raise InvalidArgument(message='每段排名每人上限/活动总额不能小于 0！')
                quantity = trimmed_rule['rank_max'] - trimmed_rule['rank_min'] + 1
                if quantity * trimmed_rule['rank_limit'] > trimmed_rule['rank_total']:
                    raise InvalidArgument(message='每段排名累计奖励，不能超过该段活动总额！')
                if trimmed_rule['gift_type'] == DepositBonusActivityConfig.GiftType.ASSET.name:
                    trimmed_rule['gift_asset'] = gift_rule['gift_asset']
                    if activity_config['mode'] is DepositBonusActivityConfig.ActivityMode.PROPORTION:
                        # 前端传来的是一个百分比值: 50% -> 50
                        trimmed_rule['proportion'] = Decimal(gift_rule['proportion']) / Decimal('100')
                        if not 0 <= trimmed_rule['proportion'] <= 1:
                            raise InvalidArgument(message='按充值比例赠送时，设置范围需在 0～1！')
                elif trimmed_rule['gift_type'] == DepositBonusActivityConfig.GiftType.COUPON.name:
                    trimmed_rule['coupon_apply_id'] = int(gift_rule['coupon_apply_id'])
                    trimmed_rule['rank_total'] = int(gift_rule['rank_total'])
                    # if len(apply_ids) > 1:
                    #     raise InvalidArgument(message='一个子活动，卡券奖励最多只能配置一个！')
                    # if trimmed_rule['coupon_apply_id'] in apply_ids:
                    #     raise InvalidArgument(message='卡券奖励, 每段排名不能重复！')
                    apply_ids.add(trimmed_rule['coupon_apply_id'])
                    coupon_apply_ids |= apply_ids
                    if trimmed_rule['rank_limit'] != 1:
                        raise InvalidArgument(message='每段排名每人卡券奖励只能一张！')
                    coupon_applys[trimmed_rule['coupon_apply_id']] += trimmed_rule['rank_total']
                trimmed_rules.append(trimmed_rule)
            activity_config['gift_rules'] = trimmed_rules
            cls.__check_rank_duplicate(activity_config['gift_rules'])

        # activity_id = kwargs.get('id', 0)
        # 处理比较麻烦， 这里就不校验卡券是否已经在其他活动中使用
        _, end_time = kwargs["start_time"], kwargs["end_time"]
        applies = CouponApply.query.join(
            Coupon
        ).filter(
            CouponApply.id.in_(coupon_apply_ids)
        ).with_entities(
            CouponApply.id,
            CouponApply.send_at,
            CouponApply.coupon_id,
            CouponApply.total_count,
            Coupon.receivable_days,
        ).all()
        for row in applies:
            total = coupon_applys[row.id]
            if row.send_at > end_time or end_time + timedelta(days=7) > row.send_at + timedelta(days=row.receivable_days):
                raise InvalidArgument(
                    message="发放前保证在卡券有效（卡券发放时间≤活动结束时间<活动结束时间+7天≤卡券发放结束时间(卡券发放时间+领取有效期)）")
            if row.total_count < total:
                raise InvalidArgument(message="卡券发放数量需大于等于奖励卡券总量")

    @classmethod
    def __check_rank_duplicate(cls, gift_rules):
        for idx, gift_rule in enumerate(gift_rules, start=0):
            if gift_rule['gift_type'] == DepositBonusActivityConfig.GiftType.ASSET.name:
                continue
            for next_gift_rule in gift_rules[idx + 1:]:
                if next_gift_rule['gift_type'] == DepositBonusActivityConfig.GiftType.ASSET.name:
                    continue
                if gift_rule['coupon_apply_id'] != next_gift_rule['coupon_apply_id']:
                    continue
                if gift_rule['rank_max'] < next_gift_rule['rank_min']:
                    continue
                if gift_rule['rank_min'] > next_gift_rule['rank_max']:
                    continue
                raise InvalidArgument(message="区间排名交叉时，同一卡券发放ID无法对同一人发放2张，请关联新卡券或使区间排名不要交叉")

    @classmethod
    def _check_conditions(cls, kwargs):
        asset_holding = kwargs.get('ASSET_HOLDING')
        need_holding = kwargs.get('NEED_HOLDING')
        if not need_holding or need_holding == ActivityCondition.NeedHoldingType.NOT_LIMITED:
            if asset_holding:
                raise InvalidArgument(message="不限制不能填入币种")
        else:
            if not asset_holding:
                raise InvalidArgument(message="持有币种和是否持有条件必须同时存在")

    @classmethod
    def _check_details(cls, kwargs):
        detail_map = {i["lang"]: i for i in kwargs['details']}
        for lang in Language:
            detail = detail_map.get(lang)
            if not detail:
                raise InvalidArgument(message="语言{}必填".format(lang))
            if not detail['title']:
                raise InvalidArgument(message="语言{}的标题必填".format(lang))

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
            status=fields.EnumField(
                model.StatusType, required=True
            ),
        )
    )
    def patch(cls, **kwargs):
        """ 运营-活动-充值福利-操作上下架/删除 """
        activity_id = kwargs["id"]
        status = kwargs["status"]
        model = cls.model
        row = cls._get_row(activity_id)
        old_data = row.to_dict(enum_to_name=True)
        if status not in [
            model.StatusType.ONLINE,
            model.StatusType.OFFLINE,
            model.StatusType.DELETED,
        ]:
            raise InvalidArgument(message="只支持上架/下架/删除操作")
        if row.status is model.StatusType.FINISHED:
            raise InvalidArgument(message="活动已完成，不支持操作")
        if status is model.StatusType.DELETED:
            if row.status not in [
                model.StatusType.PENDING,
                model.StatusType.OFFLINE,
            ]:
                raise InvalidArgument(message="仅有待上架、已下架支持删除")
        row.status = status
        db.session.commit()
        cls._reload()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositActivity,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route("/activity/<int:id_>")
@respond_with_code
class DepositBonusActivityDetailResource(DepositBonusMixin, Resource):
    model = DepositBonusActivity

    conditions = {
        ActivityCondition.ConditionKeys.VIP.name: 'VIP条件',
        ActivityCondition.ConditionKeys.KYC.name: 'KYC条件',
        ActivityCondition.ConditionKeys.HOLDING.name: '是否持有币种',
        ActivityCondition.ConditionKeys.BALANCE_VALUE.name: '总资产',
        ActivityCondition.ConditionKeys.TRADE_VALUE.name: '累计交易额',
        ActivityCondition.ConditionKeys.REGISTERED_VALUE.name: '注册时间',
        ActivityCondition.ConditionKeys.USED_VALUE.name: '使用此功能',
        ActivityCondition.ConditionKeys.MARKET_MAKER.name: '做市商',
    }

    @classmethod
    def get(cls, id_):
        """运营-活动-充值福利-活动详情"""
        _asset_to_chains = cls._get_asset_to_chains()
        extra = dict(
            assets=list(_asset_to_chains.keys()),
            asset_to_chains=_asset_to_chains,
            statuses=cls.model.StatusType,
            conditions=cls.conditions,
            languages={k.name: v for k, v in language_cn_names().items()},
            modes=DepositBonusActivityConfig.ActivityMode,
            gift_types=DepositBonusActivityConfig.GiftType,
            flags=DepositBonusActivityConfig.Flag,
            not_used_dict={i.name: i.value for i in ActivityCondition.UsedType},
            trade_type_dict={i.name: i.value for i in ActivityCondition.TradeType},
            market_maker_dict={i.name: i.value for i in ActivityCondition.MarketMakerType},
            coupon_types=Coupon.CouponType,
        )
        if id_ == 0:
            return dict(
                extra=extra
            )

        activity = cls._get_row(id_)
        c_model = DepositBonusActivityContent
        c_rows = c_model.query.filter(
            c_model.deposit_bonus_id == activity.id
        ).all()
        details = {i.lang.name: i.to_dict(enum_to_name=True) for i in c_rows}
        activity_configs, coupon_details = cls._get_activity_configs(activity.id)
        extra['coupon_details'] = coupon_details
        return dict(
            activity=dict(
                id=activity.id,
                name=activity.name,
                start_time=activity.start_time,
                end_time=activity.end_time,
                cover=activity.cover,
                cover_url=activity.cover_url,
                zendesk_url=activity.zendesk_url,
                asset=activity.asset,
                chains=activity.get_chains(),
                threshold=activity.threshold,
                status=activity.status.name,
                active_status=activity.active_status.name,
                details=details,
                configs=activity_configs,
            ),
            extra=extra
        )

    @classmethod
    def _get_activity_configs(cls, activity_id):
        model = DepositBonusActivityConfig
        rows = model.query.filter(
            model.deposit_bonus_id == activity_id
        ).order_by(model.id.asc()).all()
        activity_config_ids = [row.id for row in rows]
        condition_query = DepositBonusActivityCondition.query.filter(
            DepositBonusActivityCondition.activity_config_id.in_(activity_config_ids),
        ).all()
        config_conditions = defaultdict(dict)
        for c in condition_query:
            conditions = config_conditions[c.activity_config_id]
            if c.key in [
                ActivityCondition.ConditionKeys.VIP.value,
                ActivityCondition.ConditionKeys.KYC.value,
            ]:
                key_type = getattr(ActivityCondition, f'{c.key}Type')(c.value)
                if not bool(int(key_type.value)):
                    continue
                conditions[c.key] = key_type.name
            elif c.key == ActivityCondition.ConditionKeys.MARKET_MAKER.value:
                key_type = ActivityCondition.MarketMakerTypeKeys(c.value)
                if key_type is ActivityCondition.MarketMakerTypeKeys.NOT_LIMITED:
                    continue
                conditions[c.key] = key_type.name
            elif c.key == ActivityCondition.ConditionKeys.HOLDING.value:
                holding = json.loads(c.value)
                if ((need_holding := ActivityCondition.NeedHoldingType(holding['NEED_HOLDING'])) ==
                        ActivityCondition.NeedHoldingType.NOT_LIMITED):
                    continue
                conditions['NEED_HOLDING'] = need_holding.name
                conditions['HOLDING'] = True
                conditions['ASSET_HOLDING'] = holding['ASSET_HOLDING']
            elif c.key == ActivityCondition.ConditionKeys.BALANCE_VALUE.value:
                balance_conf = json.loads(c.value)
                if ((balance_op_type := ActivityCondition.BalanceValueOperateType(balance_conf['BALANCE_OP_TYPE'])) ==
                        ActivityCondition.BalanceValueOperateType.NOT_LIMITED):
                    continue
                conditions['BALANCE_OP_TYPE'] = balance_op_type.name
                conditions['BALANCE_VALUE'] = balance_conf['BALANCE_VALUE']
            elif c.key == ActivityCondition.ConditionKeys.TRADE_VALUE.value:
                trade_conf = json.loads(c.value)
                if ((trade_op_type := ActivityCondition.TradeValueOperateType(trade_conf['TRADE_OP_TYPE'])) ==
                        ActivityCondition.TradeValueOperateType.NOT_LIMITED):
                    continue
                conditions['TRADE_OP_TYPE'] = trade_op_type.name
                conditions['TRADE_BUSINESS_TYPE'] = ActivityCondition.TradeBusinessTypeKey(
                    trade_conf['TRADE_BUSINESS_TYPE']
                ).name
                conditions['TRADE_DAY_RANGE'] = trade_conf['TRADE_DAY_RANGE']

                conditions['TRADE_VALUE'] = trade_conf['TRADE_VALUE']
                conditions['TRADE_TYPE_RANGE'] = trade_conf['TRADE_TYPE_RANGE']
            elif c.key == ActivityCondition.ConditionKeys.USED_VALUE.value:
                if not c.value:
                    continue
                conditions['USED_VALUE'] = c.value.split(",")
            elif c.key == ActivityCondition.ConditionKeys.REGISTERED_VALUE.value:
                registered_config = json.loads(c.value)
                if not registered_config['REGISTERED_VALUE']:
                    continue
                conditions["REGISTERED_VALUE"] = registered_config['REGISTERED_VALUE']
                conditions["REGISTERED_OP_TYPE"] = ActivityCondition.RegisteredTimeType(
                    registered_config['REGISTERED_OP_TYPE']
                ).name
            else:
                conditions[c.key] = c.value
        ret = []
        apply_ids = set()
        for row in rows:
            gift_rules = row.cached_gift_rules
            for gift_rule in gift_rules:
                if gift_rule['gift_type'] == DepositBonusActivityConfig.GiftType.COUPON.name:
                    apply_ids.add(gift_rule['coupon_apply_id'])
                else:
                    if row.mode is row.ActivityMode.PROPORTION:
                        gift_rule['proportion'] = gift_rule['proportion'] * Decimal('100')
            ret.append({
                'id': row.id,
                'activity_id': row.activity_id,
                'mode': row.mode.name,
                'flag': row.flag.name,
                'gift_rules': row.cached_gift_rules,
                'conditions': config_conditions[row.id]
            })
        coupon_details = {}
        if apply_ids:
            coupon_details = get_airdrop_coupon_details(apply_ids)
        return ret, coupon_details


@ns.route("/activity/statistics")
@respond_with_code
class DepositBonusActivityStatisticsResource(Resource):
    model = DepositBonusActivityUserInfo

    @classmethod
    @ns.use_kwargs(
        dict(
            activity_id=wa_fields.Integer(),
            config_id=wa_fields.Integer(),
        )
    )
    def get(cls, **kwargs):
        """ 运营-活动-充值福利-参与详情统计 """
        activity_id = kwargs['activity_id']
        if not activity_id:
            return dict(
                updated_at=now(),
                apply_total=0,
                apply_counts=0,
                deposit_user_cnt=0,
                satisfied_user_cnt=0,
                new_user_cnt=0,
                first_deposit_cnt=0,
                come_back_cnt=0,
                new_deposit_user_cnt=0,
                new_satisfied_user_cnt=0,
                first_deposit_amount='0',
                come_back_amount='0',
                deposit_amount='0',
                new_deposit_amount='0',
                asset='',
            )
        activity = DepositBonusActivityHistoryResource.get_activity(activity_id)
        query = DepositBonusActivityApplyUser.query.outerjoin(
            DepositBonusActivityUserInfo,
            and_(
                DepositBonusActivityApplyUser.deposit_bonus_id == DepositBonusActivityUserInfo.deposit_bonus_id,
                DepositBonusActivityApplyUser.activity_id == DepositBonusActivityUserInfo.activity_id,
                DepositBonusActivityApplyUser.user_id == DepositBonusActivityUserInfo.user_id,
            )
        ).filter(
            DepositBonusActivityApplyUser.deposit_bonus_id == activity.id
        ).with_entities(
            DepositBonusActivityUserInfo, DepositBonusActivityApplyUser
        )
        if config_id := kwargs.get('config_id'):
            query = query.filter(DepositBonusActivityUserInfo.activity_id == config_id)
        items = query.all()

        config_items, apply_ids = DepositBonusActivityHistoryResource.get_activity_configs([activity.id])
        applying_at_mapping = DepositBonusActivityHistoryResource.get_applying_at([activity.id])
        if config_id:
            applying_at_mapping = {k: v for k, v in applying_at_mapping.items() if k == config_id}
        apply_counts = []
        for x in config_items:
            if config_id and x['id'] != config_id:
                continue
            apply_counts.append({
                'id': x['id'],
                'name': x['name'],
                'count': len(applying_at_mapping.get(x['id'], {}))
            })
        deposit_user_cnt = satisfied_user_cnt = new_user_cnt = 0
        new_deposit_user_cnt = new_satisfied_user_cnt = 0
        first_deposit_cnt = come_back_cnt = 0
        deposit_amount = new_deposit_amount = 0
        first_deposit_amount = come_back_amount = 0
        updated_at = None
        for (user_info, apply_user) in items:
            item = {}
            if not user_info:  # 报名后还未更新数据
                item.update(DepositBonusActivityUserInfo(
                    amount=Decimal(),
                    net_amount=Decimal(),
                ).to_dict(enum_to_name=True))
            else:
                item.update(user_info.to_dict(enum_to_name=True))
            item.update(apply_user.to_dict(enum_to_name=True))
            if updated_at is None:
                updated_at = item.get('report_at')
            if item['amount'] > 0:
                deposit_user_cnt += 1
            if item['net_amount'] >= activity.threshold:
                satisfied_user_cnt += 1
            if item['type'] == DepositBonusActivityUserInfo.Type.NEW.name:
                new_user_cnt += 1
                new_deposit_amount += item['net_amount']
                if item['amount'] > 0:
                    new_deposit_user_cnt += 1
                if item['net_amount'] >= activity.threshold:
                    new_satisfied_user_cnt += 1
            if item['is_first_deposit']:
                first_deposit_cnt += 1
                first_deposit_amount += item['net_amount']
            if item['is_comeback']:
                come_back_cnt += 1
                come_back_amount += item['net_amount']
            deposit_amount += item['net_amount']
        return dict(
            updated_at=updated_at,
            apply_total=sum([len(v) for v in applying_at_mapping.values()]),
            apply_counts=apply_counts,  # 子活动人数展示：要求按固定格式命名，且按顺序展示
            deposit_user_cnt=deposit_user_cnt,
            satisfied_user_cnt=satisfied_user_cnt,
            new_user_cnt=new_user_cnt,
            first_deposit_cnt=first_deposit_cnt,
            come_back_cnt=come_back_cnt,
            new_deposit_user_cnt=new_deposit_user_cnt,
            new_satisfied_user_cnt=new_satisfied_user_cnt,
            first_deposit_amount=amount_to_str(first_deposit_amount),
            come_back_amount=amount_to_str(come_back_amount),
            deposit_amount=amount_to_str(deposit_amount),
            new_deposit_amount=amount_to_str(new_deposit_amount),
            asset=activity.asset,
        )


@ns.route("/activity/history")
@respond_with_code
class DepositBonusActivityHistoryResource(Resource):
    model = DepositBonusActivityUserInfo
    export_headers = (
        {"field": "activity_id", Language.ZH_HANS_CN: "活动ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "name", Language.ZH_HANS_CN: "报名活动"},
        {"field": "rank", Language.ZH_HANS_CN: "活动排名"},
        {"field": "net_amount", Language.ZH_HANS_CN: "累计链上净充值数量"},
        {"field": "gifts", Language.ZH_HANS_CN: "预计奖励"},
        {"field": "real_gifts", Language.ZH_HANS_CN: "实际奖励"},
        {"field": "applying_at", Language.ZH_HANS_CN: "报名时间"},
        {"field": "deposit_at", Language.ZH_HANS_CN: "达标时间"},
        {"field": "status", Language.ZH_HANS_CN: "报名状态"},
        {"field": "anti_fraud_score", Language.ZH_HANS_CN: "羊毛党得分"},
        {"field": "anti_fraud_similar_emails", Language.ZH_HANS_CN: "相似邮箱"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            activity_id=wa_fields.Integer(),
            config_id=wa_fields.Integer(missing=0),
            status=fields.EnumField(
                model.Status,
                required=False,
            ),
            user_id=wa_fields.Integer,
            page=fields.PageField(unlimited=True, missing=1),
            limit=fields.LimitField(missing=20),
        )
    )
    def get(cls, **kwargs):
        """ 运营-活动-充值福利-参与详情 """
        return cls.get_items(kwargs)

    @classmethod
    def get_items(cls, kwargs):
        extra = dict(
            activities={i.id: i.name for i in DepositBonusActivity.query.filter(
                DepositBonusActivity.status != DepositBonusActivity.StatusType.DELETED
            ).all()},
            statuses=DepositBonusActivityUserInfo.Status,
            configs={},
        )

        activity_id, user_id = kwargs.get('activity_id'), kwargs.get('user_id')
        if not activity_id and not user_id:
            return dict(
                items=[],
                total=0,
                can_gift=False,
                is_checked=True,
                activity_status=DepositBonusActivity.StatusType.FINISHED.name,
                activity_id=None,
                extra=extra,
            )

        activity = cls.get_activity(activity_id)
        activity_asset_map = cls.all_activity_asset_map()
        model = cls.model
        o_model = DepositBonusActivityApplyUser
        query = o_model.query.outerjoin(
            model,
            and_(
                o_model.deposit_bonus_id == model.deposit_bonus_id,
                o_model.activity_id == model.activity_id,
                o_model.user_id == model.user_id,
            )
        ).with_entities(
            model, o_model
        )
        page = kwargs['page']
        limit = kwargs['limit']
        if activity_id:
            query = query.filter(o_model.deposit_bonus_id == activity_id)
        if user_id:
            query = query.filter(o_model.user_id == user_id)
        if config_id := kwargs.get('config_id'):
            query = query.filter(o_model.activity_id == config_id)
        if status := kwargs.get('status'):
            query = query.filter(model.status == status)

        paginate = query.paginate(page, limit, error_out=False)
        items = paginate.items
        total = paginate.total

        activity_ids = {apply_user.deposit_bonus_id for (_, apply_user) in items}
        user_ids = [apply_user.user_id for (user_info, apply_user) in items]
        config_items, apply_ids = cls.get_activity_configs(activity_ids)
        configs = {x['id']: x for x in config_items}
        coupon_details = cls._get_coupon_details(apply_ids)
        user_emails = cls._get_user_emails(user_ids)
        applying_at_mapping = cls.get_applying_at(activity_ids)
        ret = []
        anti_fraud_cache = {}
        for (user_info, apply_user) in items:
            item = {}
            if not user_info:  # 报名后还未更新数据
                tmp = DepositBonusActivityUserInfo(
                    amount=Decimal(),
                    net_amount=Decimal(),
                )
                item.update(tmp.to_dict(enum_to_name=True))
                gifts = tmp.get_gifts()
            else:
                gifts = user_info.get_gifts()
                item.update(user_info.to_dict(enum_to_name=True))
            item.update(gifts=gifts)
            item.update(apply_user.to_dict(enum_to_name=True))
            item['asset'] = activity_asset_map.get(item['deposit_bonus_id'], '')
            item['email'] = user_emails[item['user_id']]
            item['name'] = configs[item['activity_id']]['name']
            item['gifts'] = cls._format_gifts(coupon_details, item['gifts'])
            item['applying_at'] = applying_at_mapping[item['activity_id']][item['user_id']]
            # get anti-fraud data from cache or from db
            if configs[item['activity_id']]['activity_id'] not in anti_fraud_cache:
                anti_fraud_data = AntiFraudHelper.get_anti_fraud_data(
                    AntiFraudScore.ActivityType.DEPOSIT_BONUS,
                    configs[item['activity_id']]['activity_id']
                )
                anti_fraud_cache[configs[item['activity_id']]['activity_id']] = anti_fraud_data
            anti_fraud_data = anti_fraud_cache[configs[item['activity_id']]['activity_id']]
            if anti_fraud_data is None:
                item['anti_fraud_data'] = None
                item['anti_fraud_score'] = "--"
                item['anti_fraud_similar_emails'] = 0
            else:
                item['anti_fraud_data'] = anti_fraud_data.get(item['user_id'], {})
                item['anti_fraud_score'] = item['anti_fraud_data'].get('score', 0)
                item['anti_fraud_similar_emails'] = len(item['anti_fraud_data'].get('similar_emails', {}))
            ret.append(item)
        if activity and activity.status == DepositBonusActivity.StatusType.ONLINE and activity.end_time < now():
            can_gift = True
        else:
            can_gift = False
        extra['configs'] = configs
        return dict(
            items=ret,
            total=total,
            can_gift=can_gift,
            is_checked=activity.is_checked if activity else True,
            activity_status=activity.status.name if activity else DepositBonusActivity.StatusType.FINISHED.name,
            activity_id=activity_id,
            extra=extra,
        )

    @classmethod
    def all_activity_asset_map(cls):
        return {
            item.id: item.asset
            for item in DepositBonusActivity.query.with_entities(
                DepositBonusActivity.id,
                DepositBonusActivity.asset,
            ).all()
        }

    @classmethod
    def get_activity(cls, id_):
        if not id_:
            return None
        activity = DepositBonusActivity.query.filter(
            DepositBonusActivity.id == id_,
        ).first()
        if not activity:
            raise InvalidArgument(message='活动不存在')
        return activity

    @classmethod
    def get_activity_configs(cls, activity_ids):
        model = DepositBonusActivityConfig
        rows = model.query.with_entities(
            model.id,
            model.deposit_bonus_id,
            model.activity_id,
            model.gift_rules,
        ).filter(
            model.deposit_bonus_id.in_(activity_ids),
        ).order_by(model.id.asc()).all()
        ret = []
        apply_ids = set()
        idx_map = defaultdict(int)
        for row in rows:
            idx_map[row.deposit_bonus_id] += 1
            ret.append({
                'id': row.id,
                'name': f'活动{idx_map[row.deposit_bonus_id]}',
                'activity_id': row.activity_id,
            })
            gift_rules = model(gift_rules=row.gift_rules).cached_gift_rules
            for gift_rule in gift_rules:
                if gift_rule['gift_type'] == model.GiftType.COUPON.name:
                    apply_ids.add(int(gift_rule['coupon_apply_id']))
        return ret, apply_ids

    @classmethod
    def _get_user_emails(cls, user_ids):
        user_emails = dict()
        for chunk_ids in batch_iter(user_ids, 2000):
            rows = User.query.filter(
                User.id.in_(chunk_ids)
            ).with_entities(User.id, User.email).all()
            user_emails.update(dict(rows))
        return user_emails

    @classmethod
    def _get_coupon_details(cls, apply_ids):
        query = CouponApply.query.join(Coupon).filter(
            CouponApply.id.in_(apply_ids)
        ).with_entities(
            CouponApply.id,
            Coupon.coupon_type,
            Coupon.id.label("coupon_id"),
            Coupon.value_type,
            Coupon.value,
            CouponApply.title,
            CouponApply.remark,
        ).all()
        return {item.id: {
            "apply_id": item.id,
            "coupon_type": item.coupon_type.name,
            "coupon_id": item.coupon_id,
            "value": CouponTool.value_display(item.coupon_type, item.value),
            "value_type": item.value_type,
            "title": item.title,
            "remark": item.remark,
        } for item in query}

    @classmethod
    def _format_gifts(cls, coupon_details, gifts):
        display_items = []
        for gift in gifts:
            if gift['gift_type'] == DepositBonusActivityConfig.GiftType.ASSET.name:
                display_items.append(f'{amount_to_str(gift["gift_amount"])} {gift["gift_asset"]}')
            else:
                coupon_apply_id = int(gift['coupon_apply_id'])
                coupon_info = coupon_details[coupon_apply_id]
                item = (f"{amount_to_str(coupon_info['value'])} {coupon_info['value_type']} "
                        f"{Coupon.CouponType[coupon_info['coupon_type']].value}")
                display_items.append(item)
        return '\n'.join(display_items)

    @classmethod
    def get_applying_at(cls, activity_ids):
        model = DepositBonusActivityApplyUser
        rows = model.query.with_entities(
            model.user_id,
            model.activity_id,
            model.created_at,
        ).filter(
            model.deposit_bonus_id.in_(activity_ids),
        ).all()
        ret = defaultdict(dict)
        for row in rows:
            ret[row.activity_id][row.user_id] = row.created_at
        return ret

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
        id=wa_fields.Integer(required=True),
        activity_id=wa_fields.Integer(required=True),
        status=fields.EnumField(model.Status, default=model.Status.VALID),
        remark=wa_fields.String,
    ))
    def put(cls, **kwargs):
        """运营-活动-充值福利-报名用户状态修改"""
        activity = DepositBonusActivity.query.filter(
            DepositBonusActivity.id == kwargs['id'],
            DepositBonusActivity.status == DepositBonusActivity.StatusType.ONLINE,
        ).first()
        if not activity:
            raise InvalidArgument(message='活动不存在，或必须是进行中的活动')
        model = cls.model
        detail = model.query.filter(
            model.deposit_bonus_id == kwargs['id'],
            model.activity_id == kwargs['activity_id'],
            model.user_id == kwargs['user_id'],
        ).first()
        if not detail:
            raise InvalidArgument(message='用户明细不存在!')
        detail.status = kwargs['status']
        detail.remark = kwargs.get('remark', '')
        db.session.commit()

    @classmethod
    @ns.use_kwargs(dict(
        activity_id=wa_fields.Integer(required=True),
    ))
    def patch(cls, **kwargs):
        """运营-活动-充值福利-参与详情用户校验"""
        activity_id = kwargs['activity_id']
        activity = DepositBonusActivity.query.filter(
            DepositBonusActivity.id == activity_id,
            DepositBonusActivity.status == DepositBonusActivity.StatusType.ONLINE,
        ).first()
        if not activity:
            raise InvalidArgument(message='只能对进行中活动操作!')
        abnormal_user = cls._get_abnormal_user()
        model = cls.model
        model.query.filter(
            model.deposit_bonus_id == activity_id,
            model.user_id.in_(abnormal_user)
        ).update({
            model.status: model.Status.INVALID,
            model.remark: model.RemarkType.ABNORMAL_USER.value,
        }, synchronize_session=False)
        if int(activity.end_time.timestamp()) < current_timestamp(to_int=True):
            activity.is_checked = True
        db.session.commit()

    @classmethod
    def _get_abnormal_user(cls):
        return UserRepository.get_abnormal_users()

    @classmethod
    @ns.use_kwargs(dict(
        activity_id=wa_fields.Integer(required=True),
    ))
    def post(cls, **kwargs):
        """运营-活动-充值福利-派奖"""
        activity_id = kwargs['activity_id']
        activity = DepositBonusActivity.query.filter(
            DepositBonusActivity.id == activity_id,
            DepositBonusActivity.status == DepositBonusActivity.StatusType.ONLINE,
            DepositBonusActivity.end_time <= now()
        ).first()
        if not activity:
            raise InvalidArgument(message='只能对进行中，且当前时间大于结束时间的活动进行操作！')
        if not activity.is_checked:
            raise InvalidArgument(message="未完成清退用户排除，不能派奖！")

        cls._check_balance(activity_id)

        DepositBonusActivityBusiness(activity_id).update_user_infos()

        with CacheLock(
                LockKeys.deposit_bonus_activity_send_gift(activity_id),
                wait=False
        ):
            model = cls.model
            rows = model.query.filter(
                model.deposit_bonus_id == activity_id,
                model.status == model.Status.VALID,
                model.gift_status == model.GiftStatus.CREATED,
            ).order_by(
                model.rank
            ).all()
            activity.status = DepositBonusActivity.StatusType.FINISHED
            db.session.commit()
            for v in rows:
                send_deposit_bonus_activity_reward_task.delay(activity_id, v.id, v.user_id)
        AdminOperationLog.new_send(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.DepositActivity,
            detail=dict(id=activity_id),
        )

    @classmethod
    def _check_balance(cls, activity_id: int):
        model = DepositBonusActivityConfig
        rows = model.query.filter(
            model.deposit_bonus_id == activity_id
        ).all()
        asset_mapping = defaultdict(Decimal)
        for row in rows:
            for asset, amount in row.get_gift_assets().items():
                asset_mapping[asset] += amount
        msg = ''
        for asset, amount in asset_mapping.items():
            balance = ServerClient().get_user_balances(config["DEPOSIT_BONUS_ADMIN_USER_ID"], asset)[asset]
            if balance["available"] < amount:
                if not msg:
                    msg = "账号余额不足，请充值后重试。"
                msg += f"币种：{asset}, 余额：{amount_to_str(balance['available'], 2)}, 活动设置总数量:{amount}。"
        if msg:
            raise InvalidArgument(message=msg)


@ns.route("/activity/history/async-download")
@respond_with_code
class DepositBonusActivityHistoryReportAsyncDownloadResource(Resource):
    model = DepositBonusActivityUserInfo

    @classmethod
    @ns.use_kwargs(
        dict(
            activity_id=wa_fields.Integer(required=True),
            config_id=wa_fields.Integer(missing=0),
            status=fields.EnumField(
                model.Status,
                required=False,
            ),
            user_id=wa_fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """运营-活动-充值福利-参与详情-异步下载"""
        activity_id = kwargs['activity_id']
        status = kwargs.get('status')
        status_name = status.name if status else None
        async_download_deposit_bonus_history_report.delay(
            activity_id=activity_id,
            config_id=kwargs.get('config_id', 0),
            user_id=kwargs.get('user_id', 0),
            status=status_name,
            email=g.user.email,
        )

    @classmethod
    def build_download_data(cls, activity_id: int, config_id: int, user_id: int, status: str | None):
        status = cls.model.Status[status] if status else None
        kwargs = {
            'activity_id': activity_id,
            'config_id': config_id,
            'user_id': user_id,
            'status': status,
            "page": 1,
            "limit": ADMIN_EXPORT_LIMIT,
        }
        ret = DepositBonusActivityHistoryResource.get_items(kwargs)
        items = ret['items']
        activity_id = ret['activity_id']
        for item in items:
            item['activity_id'] = activity_id
            item['net_amount'] = f"{item['net_amount']} {item['asset']}"
            item['applying_at'] = item['applying_at'].strftime("%Y-%m-%d %H:%M:%S")
            item['deposit_at'] = item['deposit_at'].strftime("%Y-%m-%d %H:%M:%S") if item['deposit_at'] else '-'
            item['gifts'] = item['gifts'] or '-'
            item['real_gifts'] = item['gifts']
            if item['status'] == DepositBonusActivityUserInfo.Status.INVALID.name:
                item['real_gifts'] = '-'
                item['status'] = DepositBonusActivityUserInfo.Status.INVALID.value
            else:
                item['status'] = DepositBonusActivityUserInfo.Status.VALID.value
            for k, v in item.items():
                if isinstance(v, Decimal):
                    item[k] = amount_to_str(v, 8)
        headers = DepositBonusActivityHistoryResource.export_headers
        header_map = {i["field"]: i[Language.ZH_HANS_CN] for i in headers}
        desc = '运营-活动-充值福利-参与详情-异步下载'
        return desc, header_map, items


@ns.route("/activity/history/template")
@respond_with_code
class DepositBonusActivityHistoryTemplateResource(Resource):

    export_headers = (
        {'field': 'email_userid', Language.ZH_HANS_CN: '用户ID/邮箱'},
        {'field': 'status', Language.ZH_HANS_CN: '状态(有效/无效)'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注'},
    )

    @classmethod
    def get(cls):
        """运营-活动-充值福利-参与详情-模版下载"""
        return export_xlsx(
            filename='deposit-bonus-activity-history-template',
            data_list=[],
            export_headers=cls.export_headers
        )


@ns.route("/activity/history/import")
@respond_with_code
class DepositBonusActivityHistoryImportResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            activity_id=wa_fields.Integer,
        )
    )
    def post(cls):
        """运营-活动-充值福利-参与详情-批量导入"""
        activity_id = request.form.get('activity_id')  # TODO Can't think of a better way
        if not activity_id:
            raise InvalidArgument(message="Activity ID is required for upload")
        activity_id = int(activity_id)
        activity = DepositBonusActivity.query.filter(
            DepositBonusActivity.id == activity_id,
            DepositBonusActivity.status == DepositBonusActivity.StatusType.ONLINE,
        ).first()
        if not activity:
            raise InvalidArgument(message='活动不存在，或必须是进行中的活动')

        if not (file := request.files.get('file')):
            raise InvalidArgument
        rows = get_table_rows(file, ['email_userid', 'status', 'remark'])
        user_ids = []
        for row in rows:
            if row['status'] == '有效':
                row['status'] = DepositBonusActivityUserInfo.Status.VALID
            elif row['status'] == '无效':
                row['status'] = DepositBonusActivityUserInfo.Status.INVALID
            else:
                raise InvalidArgument(message='状态必须是有效/无效')
            user_id = get_user_id_from_email(row['email_userid'])
            row['user_id'] = user_id
            user_ids.append(user_id)
        history_map = {
            item.user_id: item
            for item in DepositBonusActivityUserInfo.query.filter(
                DepositBonusActivityUserInfo.deposit_bonus_id == activity.id,
                DepositBonusActivityUserInfo.user_id.in_(user_ids),
            ).all()
        }
        for row in rows:
            user_id = row['user_id']
            if user_id not in history_map:
                raise InvalidArgument(message=f'活动不存在指定报名用户: {user_id}')
            history = history_map[user_id]
            old_data = history.to_dict(enum_to_name=True)
            history.status = row['status']
            history.remark = row['remark']
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.DepositActivity,
                old_data=old_data,
                new_data=history.to_dict(enum_to_name=True),
            )
        db.session.commit()
        return dict(
            total=len(rows),
        )
