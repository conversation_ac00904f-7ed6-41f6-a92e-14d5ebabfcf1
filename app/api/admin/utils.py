import hashlib
import os
from pathlib import Path


class UploadFileHelper:

    def __init__(self, chunk_dir):
        self.chunk_dir = chunk_dir

    def merge_file(self, filename, total_chunks):
        chunk_paths = [f'{self.chunk_dir}{filename}_{i}' for i in range(1, total_chunks + 1)]
        with open(f'{self.chunk_dir}{filename}', 'wb') as f:
            for p in chunk_paths:
                f.write(Path(p).read_bytes())

    def write_chunk_file(self, filename, file_stream, chunk_number):
        os.makedirs(self.chunk_dir, exist_ok=True)
        chunk_path = f'{self.chunk_dir}{filename}_{chunk_number}'
        with open(chunk_path, "wb") as f:
            f.write(file_stream)

    def read_file(self, filename):
        with open(f'{self.chunk_dir}{filename}', 'rb') as file:
            return file.read()

    def clear_chunks(self, filename, total_chunks, ignore_not_exist=False):
        chunk_paths = [f'{self.chunk_dir}{filename}_{i}' for i in range(1, total_chunks + 1)]
        for chunk_file in chunk_paths:
            Path(chunk_file).unlink(missing_ok=ignore_not_exist)
        Path(f'{self.chunk_dir}{filename}').unlink(missing_ok=ignore_not_exist)

    @classmethod
    def is_match_md5(cls, file_stream, req_md5):

        md5 = hashlib.md5()
        md5.update(file_stream)
        file_md5 = md5.hexdigest()
        if file_md5 == req_md5:
            return True
        return False
