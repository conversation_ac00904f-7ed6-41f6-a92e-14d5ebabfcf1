from flask import g
from marshmallow import Schema, fields as mm_fields
from marshmallow.validate import Length
from sqlalchemy import desc

from app.common.countries import get_country
from app.common.constants import Language, LANGUAGE_NAMES, language_cn_names
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField, PageField, LimitField, TimestampField
from app.business.questionnaire import QuestionnaireManage, QuestionnaireStatisticManage, QuestionnaireResponseManage, \
    export_questionnaire_response
from app.exceptions import InvalidArgument, RecordNotFound
from app.models import only_query_pagination, User, db
from app.models.mongo.questionnaire import QuestionnaireMySQL, QuestionnaireContentMySQL
from app.utils import batch_iter, now, datetime_to_utc8_str, AWSBucketPublic, export_xlsx

ns = Namespace('Questionnaire')


@ns.route('/list')
@respond_with_code
class QuestionnaireListResource(Resource):

    @classmethod
    def get_user_name_dict(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = User.query.filter(
                User.id.in_(ids)
            ).with_entities(
                User.id,
                User.name,
                User.email,
            ).all()
            res.update({user.id: user.name or user.email for user in users})
        return res

    @classmethod
    @ns.use_kwargs(dict(
        uid=mm_fields.String(),
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        status=EnumField(QuestionnaireMySQL.Status),
        creator=mm_fields.Integer(),
        page=PageField(missing=1),
        limit=LimitField(missing=10)
    ))
    def get(cls, **kwargs):
        """运营-活动-问卷工具-问卷列表"""
        page, limit = kwargs["page"], kwargs["limit"]

        query = QuestionnaireMySQL.query.filter(QuestionnaireMySQL.deleted == False)  # noqa: E712

        if questionnaire_uid := kwargs.get("uid"):
            query = query.filter(QuestionnaireMySQL.uid == questionnaire_uid)

        if start_time := kwargs.get("start_time"):
            query = query.filter(QuestionnaireMySQL.start_time >= start_time)

        if end_time := kwargs.get("end_time"):
            query = query.filter(QuestionnaireMySQL.end_time <= end_time)

        if status := kwargs.get("status"):
            query = QuestionnaireManage.get_filter_by_status(query, status)

        if creator := kwargs.get("creator"):
            query = query.filter(QuestionnaireMySQL.creator == creator)

        total = query.count()
        items = []
        creator_ids = []
        questionnaire_uids = []

        for q in only_query_pagination(query.order_by(desc(QuestionnaireMySQL.created_at)), page, limit):
            item = dict(
                uid=q.uid,
                name=q.name,
                status=q.status,
                start_time=datetime_to_utc8_str(q.start_time),
                end_time=datetime_to_utc8_str(q.end_time),
                creator=q.creator,
            )

            creator_ids.append(q.creator)
            questionnaire_uids.append(q.uid)
            items.append(item)

        user_name_map = cls.get_user_name_dict(creator_ids)
        questionnaire_statistic_map = QuestionnaireStatisticManage.get_questionnaire_statistic_map(
            questionnaire_uids, ["view_count", "login_view_count", "submit_count"]
        )
        questionnaire_url_map = QuestionnaireManage.get_questionnaire_url_map(questionnaire_uids)

        for item in items:
            questionnaire_statistic = questionnaire_statistic_map.get(item['uid'], {
                "view_count": 0,
                "login_view_count": 0,
                "submit_count": 0,
            })
            item['creator_name'] = user_name_map.get(item['creator'], '')
            item['view_count'] = questionnaire_statistic['view_count']
            item['login_view_count'] = questionnaire_statistic['login_view_count']
            item['submit_count'] = questionnaire_statistic['submit_count']
            item['questionnaire_url'] = questionnaire_url_map.get(item['uid'], '')

        extra = dict(
            questionnaire_name_info=QuestionnaireManage.get_questionnaire_name_info(),
            statuses=QuestionnaireMySQL.Status,
        )

        return dict(
            total=total,
            items=items,
            extra=extra
        )


@ns.route('/list/simple')
@respond_with_code
class QuestionnaireListSimpleResource(Resource):

    @classmethod
    def get(cls, **kwargs):
        """运营-活动-问卷工具-问卷筛选下拉栏数据"""
        return dict(
            items=QuestionnaireManage.get_questionnaire_name_info(),
        )


@ns.route('/')
@respond_with_code
class QuestionnaireResource(Resource):

    class QuestionnaireContentSchema(Schema):
        lang = EnumField(Language, required=True)
        title = mm_fields.String(required=True, validate=Length(max=1024))
        description = mm_fields.String(required=True, validate=Length(max=1024))
        reward_description = mm_fields.String(validate=Length(max=1024))
        declaration = mm_fields.String(validate=Length(max=1024))
        header_image_web = mm_fields.String()
        header_image_h5 = mm_fields.String()
        questions = mm_fields.List(mm_fields.Dict(), validate=Length(min=1))

    @classmethod
    @ns.use_kwargs(dict(
        uid=mm_fields.String(required=True)
    ))
    def get(cls, **kwargs):
        """活动-问卷工具-问卷详情"""
        q_id = kwargs['uid']
        questionnaire = QuestionnaireManage.get_questionnaire_by_id(q_id)
        if questionnaire is None:
            raise ValueError("unknown questionnaire")

        questionnaire_content_map = QuestionnaireManage.get_questionnaire_content(q_id)
        items = []

        for questionnaire_content in questionnaire_content_map.values():
            item = questionnaire_content.to_dict(enum_to_name=True)
            item.pop('questionnaire_uid')
            item.pop('id')
            item.pop('mongo_id')
            item.pop('created_at')
            item.pop('updated_at')
            item.pop('completed')
            if web_file_key := item.get("header_image_web"):
                item["header_image_web_url"] = AWSBucketPublic.get_file_url(web_file_key)
            if h5_file_key := item.get("header_image_h5"):
                item["header_image_h5_url"] = AWSBucketPublic.get_file_url(h5_file_key)
            items.append(item)

        return dict(
            questionnaire_uid=q_id,
            name=questionnaire.name,
            start_time=questionnaire.start_time.timestamp(),
            end_time=questionnaire.end_time.timestamp(),
            submit_limit=questionnaire.submit_limit.name,
            items=items,
        )

    @classmethod
    @ns.use_kwargs(dict(
        uid=mm_fields.String(),
        name=mm_fields.String(required=True, validate=Length(max=1024)),
        start_time=TimestampField(is_ms=True, required=True),
        end_time=TimestampField(is_ms=True, required=True),
        submit_limit=EnumField(QuestionnaireMySQL.SubmitLimit, required=True),
        items=mm_fields.List(mm_fields.Nested(QuestionnaireContentSchema()), required=True)
    ))
    def post(cls, **kwargs):
        """活动-问卷工具-创建/修改问卷"""
        q_id = kwargs.get('uid')
        name = kwargs["name"]
        start_time = kwargs["start_time"]
        end_time = kwargs["end_time"]
        submit_limit = kwargs["submit_limit"]

        # 检查问卷公共参数
        if start_time >= end_time:
            raise InvalidArgument(message="开始时间需要小于截止时间")

        # 更新问卷公共字段
        if q_id:
            questionnaire = QuestionnaireManage.get_questionnaire_by_id(q_id)
            if not questionnaire:
                raise RecordNotFound

            if questionnaire.deleted:
                raise InvalidArgument(message="问卷已删除")

            if questionnaire.status == QuestionnaireMySQL.Status.TERMINATION:
                raise InvalidArgument(message=f"{questionnaire.status.value}状态的问卷不支持修改")

            if questionnaire.status == QuestionnaireMySQL.Status.PENDING:
                # PENDING 状态允许修改名字
                questionnaire.name = name
                questionnaire.start_time = start_time
                questionnaire.end_time = end_time
                questionnaire.submit_limit = submit_limit
        else:
            if start_time <= now():
                raise InvalidArgument(message="开始时间不能设置到过去")

            questionnaire = QuestionnaireMySQL(
                uid=QuestionnaireManage.new_questionnaire_uid(),
                name=name,
                creator=g.user.id,
                start_time=start_time,
                end_time=end_time,
                submit_limit=submit_limit,
            )
            db.session.add(questionnaire)

        # 更新多语言问卷内容
        questionnaire_content_map = QuestionnaireManage.get_questionnaire_content(questionnaire.uid)
        last_lang_questions = None
        if questionnaire.status == QuestionnaireMySQL.Status.ACTIVE:
            # 生效中的问卷不允许修改问题结构，只允许修改内容
            last_lang_questions = list(questionnaire_content_map.values())[0].questions

        for item in kwargs['items']:
            lang = item['lang']
            title = item['title']
            description = item['description']
            reward_description = item.get('reward_description', '')
            declaration = item.get('declaration', '')
            header_image_web = item.get('header_image_web', '')
            header_image_h5 = item.get('header_image_h5', '')
            questions = item['questions']
            questions = QuestionnaireManage.validate_questions(questions, last_lang_questions)
            last_lang_questions = questions

            questionnaire_content = questionnaire_content_map.get(lang)
            if questionnaire_content:
                questionnaire_content.lang = lang
                questionnaire_content.title = title
                questionnaire_content.description = description
                questionnaire_content.reward_description = reward_description
                questionnaire_content.declaration = declaration
                questionnaire_content.header_image_web = header_image_web
                questionnaire_content.header_image_h5 = header_image_h5
                questionnaire_content.questions = questions
            else:
                questionnaire_content = QuestionnaireContentMySQL(
                    questionnaire_uid=questionnaire.uid,
                    lang=lang,
                    title=title,
                    description=description,
                    reward_description=reward_description,
                    declaration=declaration,
                    header_image_web=header_image_web,
                    header_image_h5=header_image_h5,
                    questions=questions,
                )
                db.session.add(questionnaire_content)

            questionnaire_content.completed = QuestionnaireManage.check_questionnaire_content_completed(
                questionnaire_content)

        db.session.commit()
        QuestionnaireManage.reload_questionnaire_cache(questionnaire.uid)
        return dict(questionnaire_uid=questionnaire.uid)

    @classmethod
    @ns.use_kwargs(dict(
        uid=mm_fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """活动-问卷工具-终止问卷"""
        q_id = kwargs['uid']

        questionnaire = QuestionnaireManage.get_questionnaire_by_id(q_id)
        if questionnaire is None:
            raise InvalidArgument(message="未知的问卷")

        if questionnaire.deleted:
            raise InvalidArgument(message="问卷已删除")

        if questionnaire.status == QuestionnaireMySQL.Status.TERMINATION:
            raise InvalidArgument(message="不能终止已经终止的问卷")

        questionnaire.end_time = now()
        db.session.commit()

        QuestionnaireManage.reload_questionnaire_cache(q_id)

    @classmethod
    @ns.use_kwargs(dict(
        uid=mm_fields.String(required=True)
    ))
    def delete(cls, **kwargs):
        """活动-问卷工具-删除问卷"""
        q_id = kwargs['uid']

        questionnaire = QuestionnaireManage.get_questionnaire_by_id(q_id)
        if questionnaire is None:
            raise InvalidArgument(message="问卷不存在")

        if questionnaire.deleted:
            raise InvalidArgument(message="问卷已删除")

        if questionnaire.status == QuestionnaireMySQL.Status.ACTIVE:
            raise InvalidArgument(message="不能删除生效中的问卷")

        questionnaire.deleted = True
        db.session.commit()

        QuestionnaireManage.delete_questionnaire_cache(q_id)


@ns.route('/response/list')
@respond_with_code
class QuestionnaireResponseListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        questionnaire_uid=mm_fields.String(required=True),
        lang=EnumField(Language, required=False),
        page=PageField(missing=1),
        limit=LimitField(missing=10),
        export=mm_fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """活动-问卷工具-问卷明细"""
        page, limit, export = kwargs["page"], kwargs["limit"], kwargs["export"]

        if export:
            export_questionnaire_response.delay(g.user.email, kwargs["questionnaire_uid"])
            return

        filters = dict(
            questionnaire_uid=kwargs["questionnaire_uid"],
        )
        if (lang := kwargs.get("lang")) is not None:
            filters['lang'] = lang

        questionnaire_content = QuestionnaireManage.get_questionnaire_content(kwargs["questionnaire_uid"])
        if not questionnaire_content:
            raise RecordNotFound(message="问卷无内容")
        responses = QuestionnaireResponseManage.get_responses(filters, page, limit)
        show_lang = QuestionnaireResponseManage.get_show_lang(kwargs["questionnaire_uid"])
        _language_cn_names = language_cn_names()

        items = []
        for response, answers in responses:
            items.append(dict(
                user_id=response.user_id,
                country=response.country,
                country_cn_name=get_country(
                    response.country).cn_name if response.country else QuestionnaireResponseManage.CountryNullFlag,
                lang=response.lang,
                lang_cn_name=_language_cn_names[response.lang],
                platform=response.platform,
                time=QuestionnaireResponseManage.format_duration(response.time),
                answers=[{
                    "question_uid": question['uid'],
                    "answer": QuestionnaireResponseManage.format_answer(
                        answers.get(question['uid'], {}), question, True
                    ),
                } for question in questionnaire_content.get(response.lang).questions]
            ))

        return dict(
            total=QuestionnaireResponseManage.get_responses_count(filters),
            items=items,
            extra=dict(
                questions=questionnaire_content[show_lang].questions
            )
        )


@ns.route('/statistic')
@respond_with_code
class QuestionnaireStatisticResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        questionnaire_uid=mm_fields.String(required=True),
        lang=EnumField(Language, required=False),
    ))
    def get(cls, **kwargs):
        """报表-用户报表-问卷报表"""

        user_data, question_datas = QuestionnaireStatisticManage.get_questionnaire_statistic_data(
            kwargs['questionnaire_uid'], kwargs.get('lang'))

        # 这里需要获取不去重的提交次数，使用user_lang_count即可
        questionnaire_statistic_map = QuestionnaireStatisticManage.get_questionnaire_statistic_map(
            [kwargs['questionnaire_uid']], ['user_lang_count'], lang=kwargs.get('lang'))
        questionnaire_statistic = questionnaire_statistic_map.get(kwargs['questionnaire_uid'], {})

        return dict(
            question_datas=question_datas,
            user_data=user_data,
            submit_count=questionnaire_statistic.get('user_lang_count', 0),
        )


@ns.route('/batch-upload/template')
@respond_with_code
class BatchUploadTemplateResource(Resource):

    export_headers = (
        {'field': '语言', Language.ZH_HANS_CN: '语言', Language.EN_US: 'Language'},
        {'field': 'lang', Language.ZH_HANS_CN: 'lang', Language.EN_US: 'lang'},
        {'field': '问卷标题', Language.ZH_HANS_CN: '问卷标题', Language.EN_US: 'Questionnaire Title'},
        {'field': '问卷说明', Language.ZH_HANS_CN: '问卷说明', Language.EN_US: 'Questionnaire Description'},
        {'field': '参与奖励', Language.ZH_HANS_CN: '参与奖励', Language.EN_US: 'Participation Reward'},
        {'field': '题目1题型', Language.ZH_HANS_CN: '题目1题型', Language.EN_US: 'Question 1 Type'},
        {'field': '题目1是否必填', Language.ZH_HANS_CN: '题目1是否必填', Language.EN_US: 'Question 1 Required'},
        {'field': '题目1内容', Language.ZH_HANS_CN: '题目1内容', Language.EN_US: 'Question 1 Content'},
        {'field': '题目1选项1', Language.ZH_HANS_CN: '题目1选项1', Language.EN_US: 'Question 1 Option 1'},
        {'field': '题目1选项2', Language.ZH_HANS_CN: '题目1选项2', Language.EN_US: 'Question 1 Option 2'},
        {'field': '题目2题型', Language.ZH_HANS_CN: '题目2题型', Language.EN_US: 'Question 2 Type'},
        {'field': '题目2是否必填', Language.ZH_HANS_CN: '题目2是否必填', Language.EN_US: 'Question 2 Required'},
        {'field': '题目2内容', Language.ZH_HANS_CN: '题目2内容', Language.EN_US: 'Question 2 Content'},
        {'field': '题目2选项1', Language.ZH_HANS_CN: '题目2选项1', Language.EN_US: 'Question 2 Option 1'},
        {'field': '题目2选项2', Language.ZH_HANS_CN: '题目2选项2', Language.EN_US: 'Question 2 Option 2'},
        {'field': '题目3题型', Language.ZH_HANS_CN: '题目3题型', Language.EN_US: 'Question 3 Type'},
        {'field': '题目3是否必填', Language.ZH_HANS_CN: '题目3是否必填', Language.EN_US: 'Question 3 Required'},
        {'field': '题目3内容', Language.ZH_HANS_CN: '题目3内容', Language.EN_US: 'Question 3 Content'},
        {'field': '题目3选项1', Language.ZH_HANS_CN: '题目3选项1', Language.EN_US: 'Question 3 Option 1'},
        {'field': '题目3选项2', Language.ZH_HANS_CN: '题目3选项2', Language.EN_US: 'Question 3 Option 2'},
        {'field': '题目4题型', Language.ZH_HANS_CN: '题目4题型', Language.EN_US: 'Question 4 Type'},
        {'field': '题目4是否必填', Language.ZH_HANS_CN: '题目4是否必填', Language.EN_US: 'Question 4 Required'},
        {'field': '题目4内容', Language.ZH_HANS_CN: '题目4内容', Language.EN_US: 'Question 4 Content'},
        {'field': '题目4选项1', Language.ZH_HANS_CN: '题目4选项1', Language.EN_US: 'Question 4 Option 1'},
        {'field': '题目4选项2', Language.ZH_HANS_CN: '题目4选项2', Language.EN_US: 'Question 4 Option 2'},
        {'field': '题目5题型', Language.ZH_HANS_CN: '题目5题型', Language.EN_US: 'Question 5 Type'},
        {'field': '题目5是否必填', Language.ZH_HANS_CN: '题目5是否必填', Language.EN_US: 'Question 5 Required'},
        {'field': '题目5内容', Language.ZH_HANS_CN: '题目5内容', Language.EN_US: 'Question 5 Content'},
        {'field': '题目5选项1', Language.ZH_HANS_CN: '题目5选项1', Language.EN_US: 'Question 5 Option 1'},
        {'field': '题目5选项2', Language.ZH_HANS_CN: '题目5选项2', Language.EN_US: 'Question 5 Option 2'},
        {'field': '问卷声明', Language.ZH_HANS_CN: '问卷声明', Language.EN_US: 'Questionnaire Declaration'},
    )

    @classmethod
    def get(cls):
        """运营-活动-问卷工具-问卷模板"""
        # Generate language data rows from LANGUAGE_NAMES
        languages = []
        for lang_enum, lang_info in LANGUAGE_NAMES.items():
            languages.append({
                "语言": lang_info.chinese,
                "lang": lang_enum.value.lower(),
            })

        # Add empty rows for notes section
        notes = [
            {},
            {"语言": "注意事项："},
            {"语言": "1、题型只能填：单选题、多选题、文本题、滑动评分题、上传文件题"},
            {"语言": "2、是否必填，填写“是”或者“否”即可"},
            {"语言": "3、提示语或“允许填写”需要去admin上传后手动编辑"},
            {"语言": "4、如需一次上传多个语区，问卷在不同语言题型和选项数量需要保持一致，如英语设置第一题是单选题，则其他语区都得是单选题"},
            {"语言": "5、如果需要新增题目及选项，按照题目x题型、题目x是否必填、题目x内容、题目x选项1、题目x选项2格式增加列即可"},
        ]

        return export_xlsx(
            filename='questionnaire-template',
            data_list=languages + notes,
            export_headers=cls.export_headers
        )
