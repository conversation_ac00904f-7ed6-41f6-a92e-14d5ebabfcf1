# -*- coding: utf-8 -*-
import datetime
import io
import json
import decimal
import os
import re

import openpyxl
from enum import Enum
from collections import defaultdict
from typing import List, Dict, <PERSON>ple
from flask import g, request, send_file
from app.api.admin.upload import ImageUploadResource
from app.exceptions.legacy import ImageFormatError
from flask_restx import fields as fx_fields
from openpyxl import Workbook
from sqlalchemy import or_
from webargs import fields
from marshmallow import fields as mm_fields, Schema, EXCLUDE
from werkzeug.datastructures import MultiDict

from app.models.mongo.translation import TranslationTaskMySQL

from ..common import (Namespace, Resource, respond_with_code)

from ..common.fields import (
    EnumField, PageField,
    LimitField, EnumMarshalField, JsonMarshalField,
    DateMarshalField, DateField, IconField, TimestampField, TimestampMarshalField, PositiveDecimalField
)
from app.common import Language, LANGUAGE_NAMES
from ...assets import list_all_assets
from ...business import <PERSON><PERSON><PERSON>ock, Lock<PERSON>eys, BusinessSettings
from ...business.clients.quotes import TokenInsightClient
from ...business.operation import TipBarHelper
from ...business.utils import drag_sort
from ...caches.activity import NewAssetSoonCache, NewAssetRecentCache
from ...caches.kline import AssetInformationCache
from ...exceptions import InvalidArgument
from ...models import (
    CoinInformation, AssetTag, AssetTagRelation, AssetTagTranslation, Market,
    AssetTopic, AssetTopicTranslation, db, CoinExplorer, CoinInformationTrans, NewAsset, ApiCoinInformationResult,
)
from ...models.mongo.assets import AssetApiSource, AssetCirculationSourceMySQL
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from ...utils import (
    compact_json_dumps, query_to_page, amount_to_str, ExcelExporter, BaseHTTPClient
)
from ...utils.helper import Struct

ns = Namespace('Quotes')


class TransSchema(Schema):
    lang = EnumField(Language, required=True)
    name = mm_fields.String(required=True)
    title = mm_fields.String(required=True)
    description = mm_fields.String(required=True)
    tag_id = mm_fields.Integer()
    coin_information_id = mm_fields.Integer()

    class Meta:
        UNKNOWN = EXCLUDE


@ns.route("/asset/tags")
@respond_with_code
class AssetTagsResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(AssetTag.StatusType),
            page=PageField(unlimited=True),
            limit=LimitField(missing=50)
        )
    )
    def get(cls, **kwargs):
        """运营-标签管理-标签列表"""
        params = Struct(**kwargs)
        q = AssetTag.query.filter(
            AssetTag.status != AssetTag.StatusType.INVALID).order_by(AssetTag.rank.asc())
        if params.status:
            q = q.filter(AssetTag.status == params.status)
        pagination = q.paginate(params.page, params.limit, error_out=False)
        tag_ids = {v.id for v in pagination.items}
        assets_tag_query = AssetTagRelation.query.filter(
            AssetTagRelation.tag_id.in_(tag_ids),
            AssetTagRelation.status == AssetTagRelation.StatusType.PASSED
        ).with_entities(AssetTagRelation.tag_id, AssetTagRelation.asset)
        assets_mapping = MultiDict([(v.tag_id, v.asset) for v in assets_tag_query])
        tag_translation_query = AssetTagTranslation.query.filter(
            AssetTagTranslation.tag_id.in_(tag_ids)
        ).with_entities(
            AssetTagTranslation.tag_id,
            AssetTagTranslation.lang,
            AssetTagTranslation.name,
            AssetTagTranslation.title,
            AssetTagTranslation.description,
        )
        tag_translation_mapping = MultiDict(
            [(v.tag_id, dict(name=v.name, title=v.title, description=v.description, lang=v.lang.name, tag_id=v.tag_id))
             for v in tag_translation_query]
        )
        items = []
        for v in pagination.items:
            translations = tag_translation_mapping.getlist(v.id)
            missing_langs = {lang.name for lang in AssetTagTranslation.LANGS} - {d['lang'] for d in translations}
            for missing_lang in missing_langs:
                translations.append(dict(name='', title='', description='', lang=missing_lang, tag_id=v.id))
            mapping = dict(
                id=v.id,
                name=v.display_name,
                assets=assets_mapping.getlist(v.id),
                translations=translations,
                rank=v.rank,
                status=v.status.name,
                is_show=v.is_show,
            )
            items.append(mapping)
        all_assets = list_all_assets()
        return dict(
            statuses={
                AssetTag.StatusType.PASSED.name: "开启",
                AssetTag.StatusType.DELETED.name: "关闭",
                AssetTag.StatusType.INVALID.name: "删除",
            },
            total=pagination.total,
            items=items,
            all_assets=all_assets,
            langs=[v.name for v in AssetTagTranslation.LANGS],
            lang_trans={lang.name: tran.chinese for lang, tran in LANGUAGE_NAMES.items()
                        if lang in AssetTagTranslation.LANGS}
        )

    @staticmethod
    def validate_tag(display_name):
        """AMM市场和杠杆市场设置时会自动添加杠杆/AMM标签,不允许添加同名标签"""
        if display_name in [AssetTag.AMMTag, AssetTag.MarginTag]:
            raise InvalidArgument(message='{}标签为特殊标签，简体中文下禁止添加此标签名！'.format(display_name))

    @classmethod
    def check_and_fill_translations(cls, translations: List[Dict]):
        # 标签名称、标签描述标题、标签描述详情的英文、简体中文、繁体中文必填，
        # 其他语言如果未填则默认填充英文
        tran_map = {i["lang"]: i for i in translations}
        required_langs = [Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK]  # 必填的语言
        for lang in required_langs:
            tran = tran_map.get(lang)
            if not tran or not tran["name"]:
                raise InvalidArgument(message="语言{}必填".format(lang))

        default_tran: Dict = tran_map[Language.EN_US]
        support_languages = set(AssetTagTranslation.LANGS)
        param_languages = set()
        too_long_languages = []
        for tran in translations:
            if tran["lang"] not in support_languages:
                continue
            if len(tran["title"]) > 256 or len(tran["description"]) > 4096:
                too_long_languages.append(tran["lang"])
            param_languages.add(tran["lang"])
        if too_long_languages:
            raise InvalidArgument(message="{}标题或详情过长".format(too_long_languages))

        for tran in translations:
            # 补全没填的语言
            if not tran["name"]:
                tran["name"] = default_tran["name"]
            if not tran["title"]:
                tran["title"] = default_tran["title"]
            if not tran["description"]:
                tran["description"] = default_tran["description"]

        if missing_languages := support_languages - param_languages:
            for missing_lang in missing_languages:
                # 补全缺失的语言
                missing_detail = default_tran.fromkeys(["name", "title", "description"])
                missing_detail["lang"] = missing_lang
                translations.append(missing_detail)

        return translations

    @classmethod
    @ns.use_kwargs(
        dict(
            translations=mm_fields.Nested(TransSchema, many=True, required=True),
            assets=mm_fields.List(mm_fields.String(required=True), required=True),
            status=EnumField(AssetTag.StatusType, required=True),
            is_show=mm_fields.Boolean(required=True),
        )
    )
    def post(cls, **kwargs):
        """运营-标签管理-新增标签"""
        body = Struct(**kwargs)
        trans = cls.check_and_fill_translations(body.translations)
        assets = body.assets
        post_langs = {v["lang"] for v in trans}
        trans_dict = {v["lang"]: v for v in trans}
        if set(AssetTagTranslation.LANGS) - post_langs:
            raise InvalidArgument(message="标签翻译缺失，请检查")
        if len(assets) == 0:
            raise InvalidArgument(message="需要选择对应的币种")
        info_assets = [v.code for v in CoinInformation.query.with_entities(CoinInformation.code)]
        for asset in assets:
            if asset not in info_assets:
                raise InvalidArgument(message=f"币种{asset}没有币种资料，需添加币种资料之后方可在此页面编辑")
        display_name = trans_dict[Language.ZH_HANS_CN]["name"]
        cls.validate_tag(display_name)
        t = AssetTag.query.order_by(AssetTag.rank.desc()).first()
        if t:
            rank = t.rank + 1
        else:
            rank = 1

        tag_obj = AssetTag(
            rank=rank,
            display_name=display_name,
            status=body.status,
            is_show=body.is_show,
        )
        db.session_add_and_commit(tag_obj)
        trans_objs = [
            AssetTagTranslation(
                tag_id=tag_obj.id,
                lang=lang,
                name=d["name"],
                title=d["title"],
                description=d["description"],
            )
            for lang, d in trans_dict.items()
        ]
        tag_asset_objs = [
            AssetTagRelation(
                tag_id=tag_obj.id,
                asset=asset
            )
            for asset in assets
        ]
        db.session.add_all(trans_objs)
        db.session.add_all(tag_asset_objs)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetTag,
            detail=kwargs,
        )


@ns.route("/asset/tag/<int:tag_id>")
@respond_with_code
class AssetTagResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            translations=mm_fields.Nested(TransSchema, many=True, required=True),
            assets=mm_fields.List(mm_fields.String(required=True), required=True),
            status=EnumField(AssetTag.StatusType, required=True),
            is_show=mm_fields.Boolean(required=True),
        )
    )
    def put(cls, tag_id, **kwargs):
        """运营-标签管理-修改标签"""
        body = Struct(**kwargs)
        tag_obj: AssetTag = AssetTag.query.get(tag_id)
        if not tag_obj:
            raise InvalidArgument(message="未找到对应的标签")
        tag_old_data = tag_obj.to_dict(enum_to_name=True)
        assets = body.assets
        info_assets = [v.code for v in CoinInformation.query.with_entities(CoinInformation.code)]
        for asset in assets:
            if asset not in info_assets:
                raise InvalidArgument(message=f"币种{asset}没有币种资料，需添加币种资料之后方可在此页面编辑")

        tag_id = tag_obj.id
        tag_obj.status = body.status
        tag_obj.is_show = body.is_show
        trans = AssetTagTranslation.query.filter(
            AssetTagTranslation.tag_id == tag_id,
        ).all()
        trans_map = {v.lang: v for v in trans}
        param_translations = AssetTagsResource.check_and_fill_translations(body.translations)
        for t in param_translations:
            trans_lang = t['lang']
            if trans_lang in trans_map:
                trans_obj = trans_map[trans_lang]
                trans_old_data = trans_obj.to_dict(enum_to_name=True)
                trans_obj.name = t['name']
                trans_obj.title = t['title']
                trans_obj.description = t['description']
                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectOperation.AssetTag,
                    old_data=trans_old_data,
                    new_data=trans_obj.to_dict(enum_to_name=True),
                    special_data=dict(lang=str(trans_lang), tag_id=tag_id),
                )
            elif trans_lang in AssetTagTranslation.LANGS:
                trans_obj = AssetTagTranslation(
                    tag_id=tag_id,
                    lang=trans_lang,
                    name=t['name'],
                    title=t['title'],
                    description=t['description'],
                )
                db.session.add(trans_obj)
                AdminOperationLog.new_add(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectOperation.AssetTag,
                    detail=trans_obj.to_dict(enum_to_name=True),
                )
        display_name = trans_map[Language.ZH_HANS_CN].name
        cls.validate_tag(display_name, tag_id)
        tag_obj.display_name = display_name
        old_asset_query = AssetTagRelation.query.filter(AssetTagRelation.tag_id == tag_id
                                                        ).all()
        old_assets = {v.asset for v in old_asset_query}
        new_assets = set(body.assets) - old_assets
        enable_old_assets = old_assets & set(body.assets)
        AssetTagRelation.query.filter(
            AssetTagRelation.tag_id == tag_id,
            AssetTagRelation.asset.in_(list(old_assets))
        ).update({AssetTagRelation.status: AssetTagRelation.StatusType.DELETED},
                 synchronize_session=False)
        assets_records = [AssetTagRelation(tag_id=tag_id, asset=v) for v in new_assets]
        db.session.add_all(assets_records)
        if len(enable_old_assets) > 0:
            AssetTagRelation.query.filter(
                AssetTagRelation.tag_id == tag_id,
                AssetTagRelation.asset.in_(list(enable_old_assets))
            ).update({AssetTagRelation.status: AssetTagRelation.StatusType.PASSED},
                     synchronize_session=False)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetTag,
            old_data=tag_old_data,
            new_data=tag_obj.to_dict(enum_to_name=True),
        )

    @classmethod
    def validate_tag(cls, display_name, tag_id):
        """AMM市场和杠杆市场设置时会自动添加杠杆/AMM标签,其标签不允许修改"""
        auto_tag_ids = AssetTag.get_auto_tag_ids()
        if (display_name in [AssetTag.AMMTag, AssetTag.MarginTag] and tag_id not in auto_tag_ids) or \
                (display_name not in [AssetTag.AMMTag, AssetTag.MarginTag] and tag_id in auto_tag_ids):
            # 其他标签名不能改为AMM/杠杆特殊标签名，特殊标签名也不能改成其他标签名
            raise InvalidArgument(message='{}标签为特殊标签，禁止修改此标签的简体中文名！'.format(display_name))

    @classmethod
    @ns.use_kwargs(
        dict(
            change=mm_fields.String(),
            status=EnumField(AssetTag.StatusType),
        )
    )
    def patch(cls, tag_id, **kwargs):
        """运营-标签管理-修改排序/开启/关闭"""
        body = Struct(**kwargs)
        tag_obj: AssetTag = AssetTag.query.get(tag_id)
        if not tag_obj:
            raise InvalidArgument(message="未找到对应的标签")
        old_data = tag_obj.to_dict(enum_to_name=True)
        old_rank = tag_obj.rank
        if body.status:
            tag_obj.status = body.status

            if body.status == AssetTag.StatusType.INVALID:
                tag_obj.rank = 0
                cls._update_tag_rank(old_rank)
                AssetTagRelation.query.filter(
                    AssetTagRelation.tag_id == tag_id,
                ).update(
                    {'status': AssetTagRelation.StatusType.DELETED},
                    synchronize_session=False
                )
            db.session.commit()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.AssetTag,
                old_data=old_data,
                new_data=tag_obj.to_dict(enum_to_name=True),
            )
            return
        if body.change == 'UP':
            change_tag_rank = tag_obj.rank - 1
            if change_tag_rank > 0:
                change_tag_obj = AssetTag.query.filter(AssetTag.rank == change_tag_rank).first()
                tag_obj.rank = change_tag_rank
                change_tag_obj.rank = old_rank
                db.session.commit()
        if body.change == 'DOWN':
            change_tag_rank = tag_obj.rank + 1
            last_tag_id = AssetTag.query.order_by(AssetTag.rank.desc()).first().id
            if last_tag_id != tag_obj.id:
                change_tag_obj = AssetTag.query.filter(AssetTag.rank == change_tag_rank).first()
                tag_obj.rank = change_tag_rank
                change_tag_obj.rank = old_rank
                db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetTag,
            old_data=old_data,
            new_data=tag_obj.to_dict(enum_to_name=True),
        )

    @classmethod
    def _update_tag_rank(cls, old_rank):
        tags = AssetTag.query.filter(AssetTag.rank > old_rank).order_by(AssetTag.rank.asc()).all()
        for tag_obj in tags:
            tag_obj.rank -= 1


@ns.route('/asset/tag/sorts')
@respond_with_code
class BannerSortResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            old_data=fields.Raw(required=True),
            old_index=fields.Integer(required=True),
            new_index=fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """运营-币种标签-拖拽排序"""
        with CacheLock(LockKeys.edit_operation_sort("asset_tag"), wait=False):
            old_data = kwargs['old_data']
            old_index = kwargs['old_index']
            new_index = kwargs['new_index']
            drag_sort(old_data, old_index, new_index, AssetTag, "rank")
            return {}


@ns.route('/asset/tags/batch-upload')
@respond_with_code
class AssetTagBatchUploadResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            excel_type=EnumField(["tag_info", "tag_asset"], required=True),
        )
    )
    def get(cls, **kwargs):
        """ 运营-币种标签-批量上传模版下载 """
        excel_type = kwargs["excel_type"]
        if excel_type == "tag_info":
            headers = ["标签ID", "标签中文名", "标签名称", "标签描述标题", "标签描述详情"]
            wb = Workbook(write_only=True)
            for lang in AssetTagTranslation.LANGS:
                ws = wb.create_sheet(lang.value)
                ws.append(headers)
        else:
            headers = ["标签ID", "标签中文名", "币种", "状态"]
            wb = Workbook(write_only=True)
            ws = wb.create_sheet()
            ws.append(headers)
        stream = io.BytesIO()
        wb.save(stream)
        stream.seek(0)
        return send_file(
            stream,
            download_name=f'{excel_type}.xlsx',
            as_attachment=True,
        )

    @classmethod
    def post(cls):
        """ 运营-币种标签-批量上传 """
        tag_info_file = request.files.get('tag_info')
        tag_asset_file = request.files.get('tag_asset')
        if not tag_info_file and not tag_asset_file:
            raise InvalidArgument

        if tag_info_file:
            data = cls.parse_tag_info_excel_to_data(tag_info_file)
            cls.create_or_update_tag_translations(data)
        else:
            data = cls.parse_tag_asset_excel_to_data(tag_asset_file)
            cls.update_tag_assets_status(data)

    @classmethod
    def parse_tag_info_excel_to_data(cls, tag_info_file) -> Dict:
        # 标签详情等信息
        sheet_title_lang_map = {i.value: i for i in AssetTagTranslation.LANGS}
        auto_tag_ids = AssetTag.get_auto_tag_ids()
        tag_id_lang_info_map = defaultdict(dict)
        wb = openpyxl.load_workbook(tag_info_file, read_only=True)
        for ws in wb.worksheets:
            if ws.title not in sheet_title_lang_map:
                continue

            lang = sheet_title_lang_map[ws.title]
            for row in list(ws.values)[1:]:
                # row: (标签id, 标签中文名称, 某个语言的标签名称, 标签描述标题, 标签描述详情)
                if not any(row):
                    continue
                tag_id = str(row[0]).strip()
                try:
                    tag_id = int(tag_id)
                except ValueError:
                    continue
                if tag_id in auto_tag_ids:
                    raise InvalidArgument(message="不支持修改AMM或杠杆标签")
                tag_id_lang_info_map[tag_id][lang] = [str(row[2]).strip(), str(row[3]).strip(), str(row[4]).strip()]

        # 补全没填的语言
        for tag_id in list(tag_id_lang_info_map):
            lang_info_map = tag_id_lang_info_map[tag_id]
            if Language.EN_US not in lang_info_map:
                raise InvalidArgument(message=f"标签 {tag_id} 缺少英语内容")
            if Language.ZH_HANS_CN not in lang_info_map:
                raise InvalidArgument(message=f"标签 {tag_id} 缺少中文内容")

            new_lang_info_map = {lang: v for lang, v in lang_info_map.items() if lang in AssetTagTranslation.LANGS}
            miss_langs = set(AssetTagTranslation.LANGS) - set(new_lang_info_map)
            if miss_langs:
                for lang in miss_langs:
                    new_lang_info_map[lang] = new_lang_info_map[Language.EN_US]
            tag_id_lang_info_map[tag_id] = new_lang_info_map
        return tag_id_lang_info_map

    @classmethod
    def create_or_update_tag_translations(cls, tag_id_lang_info_map: Dict):
        tag_ids = list(tag_id_lang_info_map)
        tag_rows = AssetTag.query.filter(
            AssetTag.id.in_(tag_ids),
        ).all()
        tag_id_row_map: Dict[str, AssetTag] = {i.id: i for i in tag_rows}

        # update
        for tag_id, row in tag_id_row_map.items():
            lang_info_map = tag_id_lang_info_map.get(tag_id)
            if not lang_info_map:
                continue
            old_data = row.to_dict(enum_to_name=True)
            row.display_name = lang_info_map[Language.ZH_HANS_CN][0]
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.AssetTag,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )

            trans = AssetTagTranslation.query.filter(
                AssetTagTranslation.tag_id == row.id,
            ).all()
            trans_map = {v.lang: v for v in trans}
            for lang, info in lang_info_map.items():
                if lang in trans_map:
                    trans_row = trans_map[lang]
                    old_trans_data = trans_row.to_dict(enum_to_name=True)
                    trans_row.name = info[0]
                    trans_row.title = info[1]
                    trans_row.description = info[2]
                    AdminOperationLog.new_edit(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectOperation.AssetTag,
                        old_data=old_trans_data,
                        new_data=trans_row.to_dict(enum_to_name=True),
                        special_data=dict(lang=str(lang), tag_id=trans_row.tag_id),
                    )
                elif lang in AssetTagTranslation.LANGS:
                    trans_obj = AssetTagTranslation(
                        tag_id=row.id,
                        lang=lang,
                        name=info[0],
                        title=info[1],
                        description=info[2],
                    )
                    db.session.add(trans_obj)
                    AdminOperationLog.new_add(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectOperation.AssetTag,
                        detail=trans_obj.to_dict(enum_to_name=True),
                    )
        db.session.commit()

    @classmethod
    def parse_tag_asset_excel_to_data(cls, tag_asset_file) -> Dict:
        # 标签币种、币种状态
        all_assets = set()
        tag_id_assets_status_map = defaultdict()
        auto_tag_ids = AssetTag.get_auto_tag_ids()
        wb = openpyxl.load_workbook(tag_asset_file, read_only=True)
        ws = wb.worksheets[0]
        for row in list(ws.values)[1:]:
            # row: (标签id, 标签中文名称, 币种列表, 标签状态)
            if not any(row):
                continue
            tag_id = str(row[0]).strip()
            try:
                tag_id = int(tag_id)
            except ValueError:
                continue
            if tag_id in auto_tag_ids:
                raise InvalidArgument(message="不支持修改AMM或杠杆标签")
            tag_assets = [i.strip() for i in str(row[2]).strip().split(",")] if row[2] else []
            tag_status = AssetTag.StatusType.PASSED if str(row[3]).strip() == "开启" else AssetTag.StatusType.DELETED
            tag_id_assets_status_map[tag_id] = [tag_assets, tag_status]
            all_assets.update(tag_assets)

        info_assets = [v.code for v in CoinInformation.query.with_entities(CoinInformation.code)]
        miss_info_assets = [i for i in all_assets if i not in info_assets]
        if miss_info_assets:
            raise InvalidArgument(message=f"币种{miss_info_assets}没有币种资料，需添加币种资料之后再上传")

        return tag_id_assets_status_map

    @classmethod
    def update_tag_assets_status(cls, tag_id_assets_status_map: Dict):
        tag_ids = list(tag_id_assets_status_map)
        tag_rows = AssetTag.query.filter(
            AssetTag.id.in_(tag_ids),
        ).all()
        tag_id_row_map: Dict[str, AssetTag] = {i.id: i for i in tag_rows}
        for tag_id, row in tag_id_row_map.items():
            assets, status = tag_id_assets_status_map[tag_id]
            old_data = row.to_dict(enum_to_name=True)
            row.status = status
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.AssetTag,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )

            tag_id = row.id
            old_relation_rows = AssetTagRelation.query.filter(AssetTagRelation.tag_id == tag_id).all()
            for r in old_relation_rows:
                if r.asset not in assets:
                    r.status = AssetTagRelation.StatusType.DELETED
                else:
                    r.status = AssetTagRelation.StatusType.PASSED

            old_assets = {v.asset for v in old_relation_rows}
            need_add_assets = set(assets) - old_assets
            if need_add_assets:
                assets_records = [AssetTagRelation(tag_id=tag_id, asset=v) for v in need_add_assets]
                db.session.add_all(assets_records)

        db.session.commit()


class TopicTransSchema(Schema):
    lang = EnumField(Language, required=True)
    name = mm_fields.String(required=True)
    url = mm_fields.URL(required=True)
    topic_id = mm_fields.Integer()

    class Meta:
        UNKNOWN = EXCLUDE


@ns.route("/asset/topics")
@respond_with_code
class AssetTopicsResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(AssetTopic.StatusType),
            page=PageField(unlimited=True),
            limit=LimitField(missing=50)
        )
    )
    def get(cls, **kwargs):
        """运营-标签管理-专题列表"""
        params = Struct(**kwargs)
        q = AssetTopic.query.order_by(AssetTopic.rank.asc())
        if params.status:
            q = q.filter(AssetTopic.status == params.status)
        pagination = q.paginate(params.page, params.limit, error_out=False)
        topic_ids = {v.id for v in pagination.items}
        topic_translation_query = AssetTopicTranslation.query.filter(
            AssetTopicTranslation.topic_id.in_(topic_ids)
        ).with_entities(
            AssetTopicTranslation.topic_id,
            AssetTopicTranslation.lang,
            AssetTopicTranslation.name,
            AssetTopicTranslation.url
        )
        topic_translation_mapping = MultiDict(
            [(v.topic_id, dict(name=v.name, lang=v.lang.name, topic_id=v.topic_id, url=v.url))
             for v in topic_translation_query]
        )
        return dict(
            statuses={
                AssetTopic.StatusType.PASSED.name: "开启",
                AssetTopic.StatusType.DELETED.name: "关闭",
            },
            total=pagination.total,
            items=[
                dict(
                    id=v.id,
                    name=v.display_name,
                    url=topic_translation_mapping.getlist(v.id)[0]['url'],
                    translations=topic_translation_mapping.getlist(v.id),
                    rank=v.rank,
                    status=v.status.name,
                )
                for v in pagination.items
            ],
            langs=[v.name for v in AssetTopicTranslation.LANGS],
            lang_trans={lang.name: tran.chinese for lang, tran in LANGUAGE_NAMES.items()
                        if lang in AssetTopicTranslation.LANGS}
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            translations=mm_fields.Nested(TopicTransSchema, many=True, required=True),
            status=EnumField(AssetTopic.StatusType, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 运营-标签管理-添加专题 """
        body = Struct(**kwargs)
        trans = body.translations
        post_langs = {v["lang"] for v in trans}
        trans_dict = {v["lang"]: v["name"] for v in trans}
        trans_url_dict = {v["lang"]: v["url"] for v in trans}
        if set(AssetTopicTranslation.LANGS) - post_langs:
            raise InvalidArgument(message="标签翻译缺失，请检查")
        display_name = trans_dict[Language.ZH_HANS_CN]
        t = AssetTopic.query.order_by(AssetTopic.rank.desc()).first()
        if t:
            rank = t.rank + 1
        else:
            rank = 1

        topic_obj = AssetTopic(
            rank=rank,
            display_name=display_name
        )
        db.session_add_and_commit(topic_obj)
        trans_objs = [
            AssetTopicTranslation(
                topic_id=topic_obj.id,
                lang=lang,
                name=name,
                url=trans_url_dict[lang],
            )
            for lang, name in trans_dict.items()
        ]
        db.session.add_all(trans_objs)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetTopic,
            detail=kwargs,
        )


@ns.route("/asset/topic/<int:topic_id>")
@respond_with_code
class AssetTopicResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            translations=mm_fields.Nested(TopicTransSchema, many=True, required=True),
            status=EnumField(AssetTopic.StatusType, required=True),
        )
    )
    def put(cls, topic_id, **kwargs):
        """运营-专题管理-修改专题"""
        body = Struct(**kwargs)
        topic_obj: AssetTopic = AssetTopic.query.get(topic_id)
        if not topic_obj:
            raise InvalidArgument(message="未找到对应的标签")
        old_data = topic_obj.to_dict(enum_to_name=True)
        topic_id = topic_obj.id
        topic_obj.status = body.status
        trans = AssetTopicTranslation.query.filter(
            AssetTopicTranslation.topic_id == topic_id,
        ).all()
        trans_map = {v.lang: v for v in trans}
        for t in body.translations:
            lang = t['lang']
            if lang not in trans_map:
                continue
            trans_obj = trans_map[lang]
            old_trans_data = trans_obj.to_dict(enum_to_name=True)
            trans_obj.name = t['name']
            trans_obj.url = t['url']
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.AssetTopic,
                old_data=old_trans_data,
                new_data=trans_obj.to_dict(enum_to_name=True),
                special_data=dict(lang=str(lang), topic_id=trans_obj.topic_id),
            )
        topic_obj.display_name = trans_map[Language.ZH_HANS_CN].name
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetTopic,
            old_data=old_data,
            new_data=topic_obj.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            change=mm_fields.String(),
            status=EnumField(AssetTopic.StatusType),
        )
    )
    def patch(cls, topic_id, **kwargs):
        """运营-专题管理-修改排序/开启/关闭"""
        body = Struct(**kwargs)
        topic_obj: AssetTopic = AssetTopic.query.get(topic_id)
        if not topic_obj:
            raise InvalidArgument(message="未找到对应的标签")
        old_data = topic_obj.to_dict(enum_to_name=True)
        if body.status:
            topic_obj.status = body.status
            db.session.commit()
        old_rank = topic_obj.rank
        if body.change == 'UP':
            change_topic_rank = topic_obj.rank - 1
            if change_topic_rank > 0:
                change_topic_obj = AssetTopic.query.filter(
                    AssetTopic.rank == change_topic_rank).first()
                topic_obj.rank = change_topic_rank
                change_topic_obj.rank = old_rank
                db.session.commit()
        if body.change == 'DOWN':
            change_topic_rank = topic_obj.rank + 1
            last_topic_id = AssetTopic.query.order_by(AssetTopic.rank.desc()).first().id
            if last_topic_id != topic_obj.id:
                change_topic_obj = AssetTopic.query.filter(
                    AssetTopic.rank == change_topic_rank).first()
                topic_obj.rank = change_topic_rank
                change_topic_obj.rank = old_rank
                db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetTopic,
            old_data=old_data,
            new_data=topic_obj.to_dict(enum_to_name=True),
        )


class ExplorerSchema(Schema):
    name = mm_fields.String(required=True)
    url = mm_fields.URL(required=True)
    id = mm_fields.Integer()

    class Meta:
        UNKNOWN = EXCLUDE


@ns.route('/new-asset-list')
@respond_with_code
class NewAssetListResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(NewAsset.StatusType),
        id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """运营-币种管理-新币专区-币种列表"""
        query = CoinInformation.query
        new_assets_query = NewAsset.query.order_by(NewAsset.id.desc())
        page = kwargs['page']
        limit = kwargs['limit']
        if status := kwargs.get('status'):
            new_assets_query = new_assets_query.filter(NewAsset.status == status)
        _paginate = new_assets_query.paginate(page, limit)
        new_asset_rows = _paginate.items
        new_assets = [i.asset for i in new_asset_rows]
        query = query.filter(CoinInformation.code.in_(new_assets))
        if coin_id := kwargs.get('id'):
            query = query.filter(CoinInformation.id == coin_id)
        query = query.order_by(
            CoinInformation.online_time.desc()
        )
        coin_list = [i.to_dict() for i in query.all()]
        coin_ids = [i['id'] for i in coin_list]
        trans_query = CoinInformationTrans.query.filter(
            CoinInformationTrans.lang == Language.ZH_HANS_CN,
            CoinInformationTrans.coin_information_id.in_(coin_ids),
        ).all()
        trans_map = {i.coin_information_id: i.description for i in trans_query}
        new_asset_status_map = {i.asset.strip().upper(): i.status.value for i in new_asset_rows}
        for item in coin_list:
            item['description'] = trans_map.get(item['id'], '')
            item['status'] = new_asset_status_map.get(item['code'].strip().upper(), NewAsset.StatusType.INVALID.value)
        extra = dict(statuses=NewAsset.StatusType)
        return dict(data=coin_list, extra=extra, total=_paginate.total)

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
    ))
    def post(cls, **kwargs):
        """运营-币种管理-新币专区-添加新币排行"""
        asset = kwargs['asset'].strip().upper()
        row = NewAsset.get_or_create(asset=asset)
        row.status = NewAsset.StatusType.VALID
        db.session_add_and_commit(row)
        NewAssetSoonCache.reload()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewAsset,
            detail=kwargs,
        )


@ns.route('/new-asset-information/<int:id_>')
@respond_with_code
class NewAssetInformationDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(NewAsset.StatusType)
    ))
    def patch(cls, id_, **kwargs):
        """运营-币种资料-修改新币专区资料"""
        coin_info = CoinInformation.query.get(id_)
        row: NewAsset = NewAsset.get_or_create(asset=coin_info.code)
        old_data = row.to_dict(enum_to_name=True)
        if status := kwargs.get('status'):
            if status == NewAsset.StatusType.VALID:
                if coin_info.status == CoinInformation.Status.DELETED:
                    raise InvalidArgument(message='该币种已下架，不能添加到新币专区！')
            row.status = status
        db.session_add_and_commit(row)
        NewAssetSoonCache.reload()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewAsset,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            special_data=dict(asset=row.asset),
        )


@ns.route('/coin-list')
@respond_with_code
class CoinListResource(Resource):
    marshal_fields = {
        'id': fx_fields.Integer,
        'code': fx_fields.String,
        'name': fx_fields.String,
        'icon': fx_fields.Nested(dict(
            file_key=fx_fields.String(attribute='icon'),
            file_url=IconField(attribute='icon'),
        ), attribute=lambda x: x),
        'thumbnail_icon': fx_fields.Nested(dict(
            file_key=fx_fields.String(attribute='thumbnail_icon'),
            file_url=IconField(attribute='thumbnail_icon'),
        ), attribute=lambda x: x),
        'issued_date': DateMarshalField,
        'online_time': TimestampMarshalField,
        'api_id': fx_fields.String,
        'issued_price_data': JsonMarshalField(default=[]),
        'circulation': fx_fields.String,
        'circulation_lock': fx_fields.Boolean(
            attribute=lambda x:
            x.circulation_type == CoinInformation.CirculationType.MANUAL_EDIT),
        'circulation_type': EnumMarshalField(
            CoinInformation.CirculationType, output_field_lower=False),
        'circulation_notice': fx_fields.Boolean,
        'total_supply': fx_fields.String,
        'official_website': fx_fields.String,
        'white_paper': fx_fields.String,
        'report_url': fx_fields.String,
        'source_code': fx_fields.String,
        'intro_en': fx_fields.String,
        'intro_cn': fx_fields.String,
        'telegram': fx_fields.String,
        'facebook': fx_fields.String,
        'twitter': fx_fields.String,
        'reddit': fx_fields.String,
        'medium': fx_fields.String,
        'discord': fx_fields.String,
        'youtube': fx_fields.String,
        'instagram': fx_fields.String,
        'status': EnumMarshalField(
            CoinInformation.Status, output_field_lower=False),
        'is_st': fx_fields.Boolean,
        'token_usage_data': JsonMarshalField(default=[]),
        'circulation_source_data': JsonMarshalField(default=[]),
        'token_unlock_data': JsonMarshalField(default=[]),
    }

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(CoinInformation.Status),
        tag_id=mm_fields.Integer,
        id=fields.Integer,
        code=mm_fields.String,
        circulation_type=EnumField(CoinInformation.CirculationType),
        is_st=fields.Boolean,
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """运营-币种资料-币种列表"""
        query = CoinInformation.query
        if status := kwargs.get('status'):
            query = query.filter(CoinInformation.status == status)
        if coin_id := kwargs.get('id'):
            query = query.filter(CoinInformation.id == coin_id)
        if code := kwargs.get('code'):
            query = query.filter(CoinInformation.code == code)
        if circulation_type := kwargs.get("circulation_type"):
            query = query.filter(CoinInformation.circulation_type == circulation_type)
        if tag_id := kwargs.get('tag_id'):
            assets = {v.asset for v in AssetTagRelation.query.filter(
                AssetTagRelation.tag_id == tag_id,
                AssetTagRelation.status == AssetTagRelation.StatusType.PASSED
            ).all()}
            query = query.filter(
                CoinInformation.code.in_(assets)
            )
        if (is_st := kwargs.get('is_st')) is not None:
            query = query.filter(CoinInformation.is_st == is_st)
        query = query.order_by(
            CoinInformation.id.desc()
        )
        result = query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        info_ids = [v["id"] for v in result['data']]
        translation_query = CoinInformationTrans.query.all()
        translation_mapping = MultiDict(
            [(v.coin_information_id, dict(
                description=v.description,
                lang=v.lang.name,
                coin_information_id=v.coin_information_id,
                introduces=v.introduce_list,
            ))
             for v in translation_query]
        )
        tag_query = AssetTagRelation.query.join(AssetTag).filter(
            AssetTagRelation.tag_id == AssetTag.id,
            AssetTagRelation.status == AssetTagRelation.StatusType.PASSED
        ).all()  # 标签状态为关闭的也展示
        asset_tag_data = MultiDict([(v.asset, v.tag_id)
                                    for v in tag_query])
        explorer_query = CoinExplorer.query.filter(
            CoinExplorer.coin_info_id.in_(info_ids),
            CoinExplorer.status == CoinExplorer.Status.VALID
        ).all()
        explorer_data = MultiDict([(v.coin_info_id, dict(id=v.id, name=v.name, url=v.url))
                                   for v in explorer_query])
        all_tag_query = AssetTag.query.filter(
            AssetTag.status != AssetTag.StatusType.INVALID
        ).order_by(AssetTag.rank.asc()).all()
        circulation_data = defaultdict(list)
        for v in AssetCirculationSourceMySQL.query.all():
            circulation_data[v.asset].append(
                dict(
                   source=v.source.name,
                   source_id=v.source_id,
                   extra=v.extra
                )
            )
        tag_data = [dict(tag_id=v.id, name=v.display_name) for v in all_tag_query]
        mapping_list = []
        sorted_langs = [lang.name for lang in CoinInformation.AVAILABLE_LANGS]
        for v in result["data"]:
            translations: List = translation_mapping.getlist(v["id"])
            translations = [i for i in translations if i["lang"] in sorted_langs]
            missing_langs = {lang.name for lang in CoinInformation.AVAILABLE_LANGS} - {d['lang'] for d in translations}
            for missing_lang in missing_langs:
                translations.append(dict(description='', lang=missing_lang, coin_information_id=v["id"], introduces=[]))
            translations.sort(key=lambda x: sorted_langs.index(x["lang"]))
            mapping = dict(
                v,
                tag_ids=asset_tag_data.getlist(v["code"]),
                explorer_data=explorer_data.getlist(v["id"]),
                circulation_source_data=list(circulation_data.get(v['code'], [])),
                translations=translations,
            )
            mapping_list.append(mapping)
        result["data"] = mapping_list
        result["tag_data"] = tag_data
        result["circulation_types"] = CoinInformation.CirculationType
        result['langs'] = [v.name for v in CoinInformation.AVAILABLE_LANGS]
        result['lang_trans'] = {lang.name: tran.chinese for lang, tran in LANGUAGE_NAMES.items()
                                if lang in CoinInformation.AVAILABLE_LANGS}
        result['api_types'] = {v.name: v.value for v in AssetApiSource}
        result['max_rate'] = BusinessSettings.asset_circulation_alert_limit_max_rate
        result['min_rate'] = BusinessSettings.asset_circulation_alert_limit_min_rate
        return result


class CoinInfoIntroduceSchema(Schema):
    """ 项目描述 """

    title = mm_fields.String(required=True)
    content = mm_fields.String(required=True)
    rich_content = mm_fields.String(required=True)

    class Meta:
        UNKNOWN = EXCLUDE


class CoinInfoTransSchema(Schema):
    lang = EnumField(Language, required=True)
    description = mm_fields.String(required=True)
    introduces = mm_fields.Nested(
        CoinInfoIntroduceSchema, many=True, required=False
    )
    coin_information_id = mm_fields.Integer()

    class Meta:
        unknown = EXCLUDE


@ns.route('/coin-information')
@respond_with_code
class CoinInformationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        keyword=fields.String(required=True),
        status=EnumField(CoinInformation.Status)
    ))
    def get(cls, **kwargs):
        """运营-币种资料-币种资料搜索"""
        keyword = kwargs['keyword']
        query = CoinInformation.query.filter(
            or_(
                CoinInformation.id == keyword,
                CoinInformation.code.contains(keyword),
                CoinInformation.name.contains(keyword),
            )
        ).with_entities(
            CoinInformation.id,
            CoinInformation.code,
            CoinInformation.name
        )
        if status := kwargs.get('status'):
            query = query.filter(CoinInformation.status == status)
        return [dict(id=r.id, code=r.code, name=r.name) for r in query]

    @classmethod
    def check_and_fill_translations(cls, translations: List[Dict]):
        # 标签名称、标签描述标题、标签描述详情的英文必填，
        # 其他语言如果未填则默认填充英文
        tran_map = {i["lang"]: i for i in translations}
        required_langs = [Language.EN_US, ]  # 必填的语言
        for lang in required_langs:
            tran = tran_map.get(lang)
            if not tran or not tran["description"] or not tran["introduces"]:
                raise InvalidArgument(message="语言{}必填".format(lang))

        default_tran: Dict = tran_map[Language.EN_US]
        support_languages = set(AssetTagTranslation.LANGS)
        param_languages = set()
        for tran in translations:
            if tran["lang"] not in support_languages:
                continue
            param_languages.add(tran["lang"])

        for tran in translations:
            # 补全没填的语言
            if not tran["description"]:
                tran["description"] = default_tran["description"]
            if not tran["introduces"]:
                tran["introduces"] = default_tran["introduces"]
            elif len(tran["introduces"]) == 1:
                introduce = tran["introduces"][0]
                if not introduce["title"] and not introduce["content"] and not introduce['rich_content']:
                    # 前端默认值，则填充英文内容
                    tran["introduces"] = default_tran["introduces"]

        if missing_languages := support_languages - param_languages:
            for missing_lang in missing_languages:
                # 补全缺失的语言
                missing_detail = default_tran.fromkeys(["description", "introduces"])
                missing_detail["lang"] = missing_lang
                translations.append(missing_detail)

        return translations

    @classmethod
    def check_token_unlock_data(cls, token_unlock_data, total_supply: decimal.Decimal) -> bool:
        total_unlock = decimal.Decimal(0)
        for item in token_unlock_data:
            try:
                item_date_split = item['date'].split('-')
                datetime.date(year=int(item_date_split[0]), month=int(item_date_split[1]), day=1)
                total_unlock += decimal.Decimal(item['amount'])
            except (IndexError, ValueError, decimal.InvalidOperation):
                return False
        if total_unlock > total_supply:
            return False
        return True

    @classmethod
    @ns.use_kwargs(dict(
        icon=fields.Dict(
            keys=fields.String, values=fields.Raw, required=True),
        thumbnail_icon=fields.Dict(keys=fields.String, values=fields.Raw, required=True),
        code=fields.String(required=True, validate=lambda s: bool(re.match('^[A-Z0-9_]{1,32}$', s))),
        name=fields.String(required=True),
        issued_date=DateField(to_date=True, allow_none=True),
        online_time=TimestampField(allow_none=True),
        total_supply=fields.Decimal(required=True),
        issued_price_data=fields.List(
            fields.Dict(
                keys=fields.String, values=fields.String), missing=None, allow_none=True),
        official_website=fields.String(allow_none=True, missing=''),
        white_paper=fields.String(missing=None),
        report_url=fields.String(missing=None),
        source_code=fields.String(missing=None),
        explorer=fields.String(missing=None),
        twitter=fields.String(missing=None),
        facebook=fields.String(missing=None),
        reddit=fields.String(missing=None),
        medium=fields.String(missing=None),
        discord=fields.String(missing=None),
        telegram=fields.String(missing=None),
        instagram=fields.String(missing=None),
        translations=fields.Nested(CoinInfoTransSchema, many=True, required=True),
        circulation=fields.Decimal(required=True),
        circulation_notice=fields.Boolean(default=False),
        circulation_lock=fields.Boolean(default=False),
        is_st=fields.Boolean(default=False),
        tag_ids=fields.List(fields.Integer(required=True)),
        explorer_data=fields.Nested(ExplorerSchema, many=True, required=True),
        status=EnumField(CoinInformation.Status, required=True),
        token_usage_data=fields.List(
            fields.Dict(keys=fields.String, values=fields.String), allow_none=True),
        circulation_source_data=fields.List(
            fields.Dict(keys=fields.String, values=fields.String), allow_none=True),
        token_unlock_data=fields.List(
            fields.Dict(keys=fields.String, values=fields.String), allow_none=True),
        api_id=fields.String(missing='', allow_none=True),
    ))
    def post(cls, **kwargs):
        """运营-币种资料-创建币种资料"""
        online_time = kwargs.get("online_time")
        if tag_ids := kwargs.get("tag_ids"):
            if len(tag_ids) != len(set(tag_ids)):
                raise InvalidArgument(message='标签有重复')
        if CoinInformation.query.filter(
                CoinInformation.code == kwargs['code']
        ).first():
            raise InvalidArgument(message='币种重复')
        if not (icon := kwargs['icon'].get('file_key')):
            raise InvalidArgument(message='icon格式错误')
        if not (thumbnail_icon := kwargs['thumbnail_icon'].get('file_key')):
            raise InvalidArgument(message='缩略图icon格式错误')
        if (issued_price_data := kwargs.get('issued_price_data')) is not None:
            issued_price_data = compact_json_dumps(issued_price_data)

        asset_circulation_sources = []
        if (circulation_source_data := kwargs.get('circulation_source_data')) is not None:
            for _v in circulation_source_data:
                if "source_id" not in _v or "source_id" not in _v:
                    raise InvalidArgument(message="流通量数据映射配置有误")
                asset_circulation_sources.append(
                    dict(
                        asset=kwargs['code'],
                        source=AssetApiSource[_v["source"]],
                        source_id=str(AssetApiSource[_v["source"]].parse_id(_v["source_id"])),
                        extra=""
                    )
                )
        trans = cls.check_and_fill_translations(kwargs["translations"])
        post_langs = {v["lang"] for v in trans}
        trans_dict = {v["lang"]: v for v in trans}
        if set(CoinInformation.AVAILABLE_LANGS) - post_langs:
            raise InvalidArgument(message="翻译缺失，请检查")
        explorer_objs = [CoinExplorer(
            name=v['name'],
            url=v['url']
        ) for v in kwargs['explorer_data']]
        circulation_type = CoinInformation.CirculationType.MANUAL_EDIT if kwargs["circulation_lock"] is True \
            else CoinInformation.CirculationType.THIRD_PARTY
        en_intro = trans_dict[Language.EN_US]["introduces"]
        intro_en = en_intro[0]["content"] if en_intro else ""
        cn_intro = trans_dict[Language.ZH_HANS_CN]["introduces"]
        intro_cn = cn_intro[0]["content"] if cn_intro else ""
        if (token_usage_data := kwargs.get('token_usage_data')) is not None:
            if len(token_usage_data) > 8:
                raise InvalidArgument(message='代币用途类别总数不能超过8')
            if len(token_usage_data) > 0 and \
                    sum(map(lambda x: decimal.Decimal(x['percent']), token_usage_data)) != decimal.Decimal('100'):
                raise InvalidArgument(message='代币用途总占比不等于100%')
            token_usage_data = compact_json_dumps(token_usage_data)
        if (token_unlock_data := kwargs.get('token_unlock_data')) is not None:
            if len({item['type'] for item in token_unlock_data}) > 8:
                raise InvalidArgument(message='代币解锁类别总数不能超过8')
            if not cls.check_token_unlock_data(token_unlock_data, decimal.Decimal(kwargs["total_supply"])):
                raise InvalidArgument(message='代币解锁数据异常')
            token_unlock_data = compact_json_dumps(token_unlock_data)
        c = CoinInformation(
            created_by=g.user.id,
            updated_by=g.user.id,
            code=kwargs['code'],
            icon=icon,
            thumbnail_icon=thumbnail_icon,
            name=kwargs['name'],
            circulation=kwargs['circulation'],
            circulation_notice=kwargs["circulation_notice"],
            is_st=kwargs["is_st"],
            circulation_type=circulation_type,
            issued_date=kwargs.get('issued_date'),
            online_time=online_time,
            issued_price_data=issued_price_data,
            total_supply=amount_to_str(kwargs["total_supply"]),
            official_website=kwargs['official_website'],
            white_paper=kwargs.get('white_paper'),
            report_url=kwargs.get('report_url'),
            source_code=kwargs.get('source_code'),
            twitter=kwargs.get('twitter'),
            facebook=kwargs.get('facebook'),
            reddit=kwargs.get('reddit'),
            medium=kwargs.get('medium'),
            discord=kwargs.get('discord'),
            telegram=kwargs.get('telegram'),
            instagram=kwargs.get('instagram'),
            intro_en=intro_en,
            intro_cn=intro_cn,
            coin_explorers=explorer_objs,
            api_id=kwargs.get('api_id'),
            status=kwargs["status"],
            token_usage_data=token_usage_data,
            token_unlock_data=token_unlock_data,
        )
        coin_info = c
        db.session.add(c)
        db.session.add_all(explorer_objs)
        tag_ids = kwargs['tag_ids']
        tag_objs = [AssetTagRelation(
            asset=kwargs['code'],
            tag_id=tag_id
        ) for tag_id in tag_ids]
        db.session.add_all(tag_objs)
        db.session.flush()  # 为了获得id
        trans_objs = [
            CoinInformationTrans(
                coin_information_id=c.id,
                lang=lang,
                description=d["description"],
                introduces=json.dumps(d["introduces"]),
            )
            for lang, d in trans_dict.items()
        ]
        db.session.add_all(trans_objs)
        db.session.commit()
        AssetCirculationSourceMySQL.query.filter_by(asset=c.code).delete()
        for _v in asset_circulation_sources:
            instance = AssetCirculationSourceMySQL(
                asset=_v['asset'],
                source=_v['source'].name,
                source_id=_v['source_id'],
                extra=_v.get('extra', '')
            )
            db.session.add(instance)
        db.session.commit()
        AssetInformationCache.reload()
        TipBarHelper.try_auto_gen_tip_bar_by_new_asset(coin_info)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CoinInfo,
            detail=kwargs,
        )

        return dict(
            id=c.id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        min_rate=PositiveDecimalField(required=True),
        max_rate=PositiveDecimalField(required=True),
    ))
    def patch(cls, **kwargs):
        old_data = dict(
            asset_circulation_alert_limit_max_rate=BusinessSettings.asset_circulation_alert_limit_max_rate,
            asset_circulation_alert_limit_min_rate=BusinessSettings.asset_circulation_alert_limit_min_rate,
        )
        BusinessSettings.asset_circulation_alert_limit_max_rate = kwargs['max_rate']
        BusinessSettings.asset_circulation_alert_limit_min_rate = kwargs['min_rate']

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CoinCirculationAlertLimit,
            old_data=old_data,
            new_data=dict(
                asset_circulation_alert_limit_max_rate=kwargs['max_rate'],
                asset_circulation_alert_limit_min_rate=kwargs['min_rate'],
            ),
        )


# noinspection PyUnresolvedReferences
@ns.route('/coin-information/<int:id_>')
@respond_with_code
class CoinInformationDetailResource(Resource):

    @classmethod
    def _check_translations(cls, id_):
        # 检查是否有正在进行中的异步任务
        tasks = TranslationTaskMySQL.get_business_tasks(TranslationTaskMySQL.Business.COIN_INFO, id_)
        tasks.sort(key=lambda x: x.created_at)
        status_map = dict()
        for task in tasks:
            status_map[task.target] = task.status
        for s in status_map.values():
            if s != TranslationTaskMySQL.Status.FINISHED:
                raise InvalidArgument(message='上架失败，异步翻译完成后才可上架')
        return bool(tasks)

    @classmethod
    @ns.use_kwargs(dict(
        icon=fields.Dict(keys=fields.String, values=fields.Raw),
        thumbnail_icon=fields.Dict(keys=fields.String, values=fields.Raw),
        name=fields.String,
        issued_date=DateField(to_date=True, allow_none=True),
        online_time=TimestampField(allow_none=True),
        total_supply=fields.Decimal(),
        issued_price_data=fields.List(
            fields.Dict(keys=fields.String, values=fields.String), allow_none=True),
        official_website=fields.String(allow_none=True, missing=''),
        white_paper=fields.String(missing=None),
        report_url=fields.String(missing=None),
        source_code=fields.String(missing=None),
        explorer=fields.String(missing=None),
        twitter=fields.String(missing=None),
        facebook=fields.String(missing=None),
        reddit=fields.String(missing=None),
        medium=fields.String(missing=None),
        discord=fields.String(missing=None),
        youtube=fields.String(missing=None),
        telegram=fields.String(missing=None),
        instagram=fields.String(missing=None),
        api_id=fields.String(missing='', allow_none=True),
        translations=fields.Nested(CoinInfoTransSchema, many=True),
        circulation=fields.Decimal(),
        circulation_notice=fields.Boolean(),
        circulation_lock=fields.Boolean(),
        tag_ids=fields.List(fields.Integer()),
        explorer_data=fields.Nested(ExplorerSchema, many=True),
        status=EnumField(CoinInformation.Status),
        is_st=fields.Boolean(missing=False),
        token_usage_data=fields.List(
            fields.Dict(keys=fields.String, values=fields.String), allow_none=True),
        circulation_source_data=fields.List(
            fields.Dict(keys=fields.String, values=fields.String), allow_none=True),
        token_unlock_data=fields.List(
            fields.Dict(keys=fields.String, values=fields.String), allow_none=True),
        auto_translation_languages=fields.List(EnumField(Language)),
    ))
    def patch(cls, id_, **kwargs):
        """运营-币种资料-修改币种资料"""
        c: CoinInformation = CoinInformation.query.get(id_)
        coin_info = c
        if not c:
            raise InvalidArgument
        old_data = c.to_dict(enum_to_name=True)
        c.updated_by = g.user.id
        if name := kwargs.get('name'):
            c.name = name
        icon = kwargs.get('icon')
        thumbnail_icon = kwargs.get('thumbnail_icon')
        if int(bool(icon)) + int(bool(thumbnail_icon)) == 1:
            raise InvalidArgument(message="icon图片和缩略图需要同时提供")
        if icon and thumbnail_icon:
            c.icon = icon.get('file_key')
            c.thumbnail_icon = thumbnail_icon.get('file_key')
        c.official_website = kwargs.get('official_website', '')
        for field in ['issued_date', "online_time"]:
            if field in kwargs:
                setattr(c, field, kwargs[field])
        if tag_ids := kwargs.get("tag_ids"):
            if len(tag_ids) != len(set(tag_ids)):
                raise InvalidArgument(message='标签有重复')
        if (issued_price_data := kwargs.get('issued_price_data')) is not None:
            c.issued_price_data = compact_json_dumps(issued_price_data)
        if total_supply := kwargs.get('total_supply'):
            c.total_supply = amount_to_str(total_supply)
        if (white_paper := kwargs.get('white_paper')) is not None:
            c.white_paper = white_paper
        if (report_url := kwargs.get('report_url')) is not None:
            c.report_url = report_url
        if (source_code := kwargs.get('source_code')) is not None:
            c.source_code = source_code
        if (twitter := kwargs.get('twitter')) is not None:
            c.twitter = twitter
        if (facebook := kwargs.get('facebook')) is not None:
            c.facebook = facebook
        if (reddit := kwargs.get('reddit')) is not None:
            c.reddit = reddit
        if (medium := kwargs.get('medium')) is not None:
            c.medium = medium
        if (discord := kwargs.get('discord')) is not None:
            c.discord = discord
        if (telegram := kwargs.get('telegram')) is not None:
            c.telegram = telegram
        if (youtube := kwargs.get('youtube')) is not None:
            c.youtube = youtube
        if (instagram := kwargs.get('instagram')) is not None:
            c.instagram = instagram
        if (api_id := kwargs.get('api_id')) is not None:
            c.api_id = api_id
        old_status = c.status
        if status := kwargs.get('status'):
            if status == CoinInformation.Status.DELETED:
                cls._check_can_offline(c.code)
            c.status = status
        if (circulation := kwargs.get('circulation')) is not None:
            c.circulation = circulation
        if (circulation_notice := kwargs.get('circulation_notice')) is not None:
            c.circulation_notice = circulation_notice
        if (is_st := kwargs.get('is_st')) is not None:
            c.is_st = is_st
        if kwargs.get('circulation_lock') is True:
            c.circulation_type = CoinInformation.CirculationType.MANUAL_EDIT
        else:
            c.circulation_type = CoinInformation.CirculationType.THIRD_PARTY
        if status == CoinInformation.Status.VALID and old_status != CoinInformation.Status.VALID:
            cls._check_translations(str(id_))
        asset_circulation_sources = []
        if (circulation_source_data := kwargs.get('circulation_source_data')) is not None:
            for _v in circulation_source_data:
                if "source_id" not in _v or "source_id" not in _v:
                    raise InvalidArgument(message="流通量数据映射配置有误")
                asset_circulation_sources.append(
                    dict(
                        asset=c.code,
                        source=AssetApiSource[_v["source"]],
                        source_id=str(AssetApiSource[_v["source"]].parse_id(_v["source_id"])),
                        extra=""
                    )
                )

        if (tag_ids := kwargs.get('tag_ids')) is not None:
            # 先删后曾保证编辑时的顺序
            AssetTagRelation.query.filter(
                AssetTagRelation.asset == c.code,
            ).delete()
            new_objs = [AssetTagRelation(tag_id=tag_id, asset=c.code) for tag_id in tag_ids]
            db.session.add_all(new_objs)
        if (explorer_data := kwargs.get('explorer_data')) is not None:
            old_query = CoinExplorer.query.filter(CoinExplorer.coin_info_id == c.id)
            old_query.update({"status": CoinExplorer.Status.DELETED},
                             synchronize_session=False)
            new_objs = [CoinExplorer(name=v["name"], url=v["url"], coin_info_id=c.id)
                        for v in explorer_data]
            db.session.add_all(new_objs)
        
        auto_translation_langs = kwargs.get('auto_translation_languages', [])
        if (translations := kwargs.get('translations')) is not None:
            translations = CoinInformationResource.check_and_fill_translations(translations)
            param_tran_map = {v["lang"]: v for v in translations}
            trans = CoinInformationTrans.query.filter(
                CoinInformationTrans.coin_information_id == c.id
            ).all()
            trans_map = {v.lang: v for v in trans}
            for t in translations:
                lang = t['lang']
                if lang in trans_map:
                    trans_obj = trans_map[t['lang']]
                    old_trans_data = trans_obj.to_dict(enum_to_name=True)
                    trans_obj.description = t['description']
                    trans_obj.introduces = json.dumps(t['introduces'])
                    AdminOperationLog.new_edit(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectOperation.CoinInfo,
                        old_data=old_trans_data,
                        new_data=trans_obj.to_dict(enum_to_name=True),
                        special_data=dict(lang=str(lang), coin_information_id=trans_obj.coin_information_id),
                    )
                elif lang in CoinInformation.AVAILABLE_LANGS:
                    obj = CoinInformationTrans(
                        coin_information_id=c.id,
                        description=t['description'],
                        introduces=json.dumps(t['introduces']),
                        lang=t['lang'],
                        is_auto_translation=t['lang'] in auto_translation_langs
                    )
                    db.session.add(obj)
                    AdminOperationLog.new_add(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectOperation.CoinInfo,
                        detail=obj.to_dict(enum_to_name=True),
                    )
            en_intro = param_tran_map[Language.EN_US]["introduces"]
            intro_en = en_intro[0]["content"] if en_intro else ""
            cn_intro = param_tran_map[Language.ZH_HANS_CN]["introduces"]
            intro_cn = cn_intro[0]["content"] if cn_intro else ""
            c.intro_en = intro_en
            c.intro_cn = intro_cn
        if (token_usage_data := kwargs.get('token_usage_data')) is not None:
            if len(token_usage_data) > 8:
                raise InvalidArgument(message='代币用途类别总数不能超过8')
            if len(token_usage_data) > 0 and \
                    sum(map(lambda x: decimal.Decimal(x['percent']), token_usage_data)) != decimal.Decimal('100'):
                raise InvalidArgument(message='代币用途总占比不等于100%')
            c.token_usage_data = compact_json_dumps(token_usage_data)
        if (token_unlock_data := kwargs.get('token_unlock_data')) is not None:
            if len({item['type'] for item in token_unlock_data}) > 8:
                raise InvalidArgument(message='代币解锁类别总数不能超过8')
            if not CoinInformationResource.check_token_unlock_data(token_unlock_data, decimal.Decimal(c.total_supply)):
                raise InvalidArgument(message='代币解锁数据异常')
            c.token_unlock_data = compact_json_dumps(token_unlock_data)
        db.session.commit()
        AssetCirculationSourceMySQL.query.filter_by(asset=c.code).delete()
        for _v in asset_circulation_sources:
            instance = AssetCirculationSourceMySQL(
                asset=_v['asset'],
                source=_v['source'].name,
                source_id=_v['source_id'],
                extra=_v.get('extra', '')
            )
            db.session.add(instance)
        cls._update_related_caches()
        TipBarHelper.try_auto_gen_tip_bar_by_new_asset(coin_info)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CoinInfo,
            old_data=old_data,
            new_data=c.to_dict(enum_to_name=True),
        )

    @classmethod
    def _check_can_offline(cls, code: str):
        online_statuses = [Market.Status.ONLINE, Market.Status.BIDDING, Market.Status.COUNTING_DOWN]
        record = Market.query.filter(
            Market.status.in_(online_statuses),
            or_(Market.base_asset == code,
                Market.quote_asset == code)
        ).first()
        if record:
            raise InvalidArgument(message='存在未下架的现货市场，无法下架币种资料，请先下架市场')

    @classmethod
    def _update_related_caches(cls):
        AssetInformationCache.reload()
        NewAssetSoonCache.reload()
        NewAssetRecentCache.reload()

    @classmethod
    @ns.use_kwargs(dict(
        status=fields.Enum(CoinInformation.Status, required=True)
    ))
    def put(cls, id_, **kwargs):
        """运营-币种资料-上/下架币种"""
        c: CoinInformation = CoinInformation.query.get(id_)
        if not c:
            raise InvalidArgument
        status = kwargs['status']
        old_status = c.status
        if status == CoinInformation.Status.DELETED:
            cls._check_can_offline(c.code)
        if status == CoinInformation.Status.VALID and old_status != CoinInformation.Status.VALID:
            cls._check_translations(str(id_))
        c.updated_by = g.user.id
        c.status = status
        db.session.commit()
        cls._update_related_caches()


@ns.route('/coin-information/batch-upload')
@respond_with_code
class CoinInformationBatchUploadResource(Resource):

    lang_name_key_map = {
        "中文": Language.ZH_HANS_CN.name,
        "繁体": Language.ZH_HANT_HK.name,
        "英文": Language.EN_US.name,
    }

    @classmethod
    def post(cls):
        """ 运营-币种资料-批量上传更新币种资料 """
        desc_file = request.files.get('desc_file')
        intro_file = request.files.get('intro_file')
        info_file = request.files.get('info_file')

        desc_data, intro_data, info_data = {}, {}, {}
        if desc_file:
            desc_data = cls.parse_excel_desc_file_to_data(desc_file)
        if intro_file:
            intro_data = cls.parse_excel_intro_file_to_data(intro_file)
        if info_file:
            info_data = cls.parse_excel_info_file_to_data(info_file)

        all_assets, fail_assets = cls.update_asset_information(desc_data, intro_data, info_data)
        return {
            "all_assets": all_assets,
            "fail_assets": fail_assets,
        }

    @classmethod
    def parse_excel_desc_file_to_data(cls, desc_file) -> Dict:
        # {'CET': {'EN': 'desc_str'}}
        asset_lang_desc_map = defaultdict(dict)  # 项目简介|一句话描述
        wb = openpyxl.load_workbook(desc_file, read_only=True)
        for ws in wb.worksheets[:3]:
            if ws.title not in cls.lang_name_key_map:
                continue

            lang = cls.lang_name_key_map[ws.title]
            for row in list(ws.values)[1:]:
                # row: (asset, desc)
                if not any(row):
                    continue
                asset_lang_desc_map[row[0]][lang] = row[1]

        return dict(asset_lang_desc_map)

    @classmethod
    def parse_excel_intro_file_to_data(cls, intro_file) -> Dict:
        # {'CET': {'EN': [{'title': '123', 'content': '111'}]}}
        asset_lang_intros_map = defaultdict(dict)  # 项目介绍，数组
        wb = openpyxl.load_workbook(intro_file, read_only=True)
        for ws in wb.worksheets[:3]:
            if ws.title not in cls.lang_name_key_map:
                continue

            lang = cls.lang_name_key_map[ws.title]
            for row in list(ws.values)[1:]:
                # 最多9列
                # row: (asset, title1, content1, ...title4, content4)
                if not any(row):
                    continue
                asset = row[0]
                intros = []
                for index in range(1, 5):
                    _title = row[index * 2 - 1]
                    _content = row[index * 2]
                    if not _title or not _content:
                        break
                    intros.append(
                        {"title": _title, "content": _content, "rich_content": ''}
                    )
                asset_lang_intros_map[asset][lang] = intros

        return dict(asset_lang_intros_map)

    excel_info_fields = [
        'name', 'tags', 'official_website', 'white_paper', 'report_url', 'source_code',
        'coin_explorer_name', 'coin_explorer_url', 'telegram', 'facebook', 'twitter',
        'reddit', 'medium', 'discord', 'youtube', 'instagram',
    ]

    @classmethod
    def parse_excel_info_file_to_data(cls, info_file) -> Dict:
        # {'CET': {'name': '', 'tags': 'Coin, Pow', 'official_website': '', 'white_paper': '', 'source_code': '',
        # 'coin_explorer_name': '', 'coin_explorer_url': '', 'telegram': '', 'facebook': '', 'twitter': '',
        # 'reddit': '', 'medium': '', 'discord': '', 'youtube': '', 'instagram': ''}}
        # 表格中的空白字段跳过不更新
        field_index_map = {index+1: field for index, field in enumerate(cls.excel_info_fields)}
        wb = openpyxl.load_workbook(info_file, read_only=True)
        ws = wb.worksheets[0]
        return {
            asset: {field: row[index] for index, field in field_index_map.items()}
            for row in list(ws.values)[1:] if (asset := row[0]) != ''
        }

    @classmethod
    def update_coin_explorer(cls, coin_explorer_name, coin_explorer_url, coin_info_id):
        if not coin_explorer_name or not coin_explorer_url:
            return
        coin_explorer_names = coin_explorer_name.splitlines()
        coin_explorer_urls = coin_explorer_url.splitlines()
        if len(coin_explorer_names) == 0 or (len(coin_explorer_names) != len(coin_explorer_urls)):
            return
        old_explorer_query = CoinExplorer.query.filter(CoinExplorer.coin_info_id == coin_info_id)
        old_explorer_query.update(
            {"status": CoinExplorer.Status.DELETED},
            synchronize_session=False
        )
        new_explorers = [CoinExplorer(
            name=coin_explorer_names[index], url=coin_explorer_urls[index], coin_info_id=coin_info_id
        ) for index in range(len(coin_explorer_names))]
        db.session.add_all(new_explorers)

    @classmethod
    def update_asset_information(
            cls,
            asset_lang_desc_map: Dict,
            asset_lang_intros_map: Dict,
            asset_info_map: Dict
    ) -> Tuple[List, List]:
        assets = list(set(asset_lang_desc_map) | set(asset_lang_intros_map) | set(asset_info_map))
        infos: List[CoinInformation] = CoinInformation.query.filter(
            CoinInformation.code.in_(assets),
        ).all()

        info_map: Dict[int, CoinInformation] = {i.id: i for i in infos}
        trans: List[CoinInformationTrans] = CoinInformationTrans.query.filter(
            CoinInformationTrans.coin_information_id.in_(list(info_map)),
        ).all()
        tag_id_map: Dict[str, int] = {tag.display_name.lower(): tag.id for tag in AssetTag.query.all()}

        success_assets = set()
        for tran in trans:
            info = info_map.get(tran.coin_information_id)
            if not info:
                continue
            lang = tran.lang.name
            if lang not in cls.lang_name_key_map.values():
                lang = Language.EN_US.name  # 其他语言用英语的内容
            asset = info.code
            description = asset_lang_desc_map.get(asset, {}).get(lang)
            if description:
                tran.description = description

            introduces = asset_lang_intros_map.get(asset, {}).get(lang)
            if introduces:
                tran.introduces = json.dumps(introduces)
            success_assets.add(asset)

        for asset_id, info in info_map.items():
            asset = info.code
            if not (info_data := asset_info_map.get(asset)):
                continue

            if info_data['tags']:
                AssetTagRelation.query.filter(
                    AssetTagRelation.asset == asset,
                ).delete()
                new_tag_relations = []
                for tag_name in info_data['tags'].split(','):
                    if (tag_id := tag_id_map.get(tag_name.strip().lower())) is None:
                        continue
                    new_tag_relations.append(AssetTagRelation(tag_id=tag_id, asset=asset))
                db.session.add_all(new_tag_relations)

            cls.update_coin_explorer(info_data['coin_explorer_name'], info_data['coin_explorer_url'], info.id)

            for field in set(cls.excel_info_fields) - {'tags', 'coin_explorer_name', 'coin_explorer_url'}:
                if info_data[field]:
                    setattr(info, field, info_data[field])

            success_assets.add(asset)

        db.session.commit()
        # AssetInformationCache.reload()  # 会导致接口超时，由定时任务自动刷新
        return assets, list(set(assets) - success_assets)


@ns.route('/coin-information/batch-download')
@respond_with_code
class UserTagGroupUserResource(Resource):

    @classmethod
    def get(cls):
        """ 运营-币种资料-批量下载币种资料 """
        data = cls.get_data()
        field_names = ['code'] + list(CoinInformationBatchUploadResource.excel_info_fields) + ['status']
        headers = [
            '币种名称', '币种全称', '标签', '官网', '白皮书', '研究报告', '源代码',
            '区块浏览器名称', '区块浏览器链接', 'Telegram', 'Facebook', 'Twitter',
            'Reddit', 'Medium', 'Discord', 'Youtube', 'Instagram', '状态',
        ]
        stream = ExcelExporter(
            data_list=data,
            fields=field_names,
            headers=headers
        ).export_streams()
        return send_file(
            stream,
            download_name=f'all_coin_information.xlsx',
            as_attachment=True
        )

    @classmethod
    def get_value(cls, info: CoinInformation, field: str):
        value = getattr(info, field)
        if isinstance(value, Enum):
            return value.value
        return value

    @classmethod
    def get_data(cls) -> list:
        tag_id_map: Dict[int, str] = {tag.id: tag.display_name for tag in AssetTag.query.all()}
        info_map: Dict[int, CoinInformation] = {info.id: info for info in CoinInformation.query.all()}
        asset_tag_relation_map = defaultdict(set)
        for tag_relation in AssetTagRelation.query.filter(
                AssetTagRelation.status == AssetTagRelation.StatusType.PASSED
        ).all():
            if tag_relation.tag_id in tag_id_map:
                asset_tag_relation_map[tag_relation.asset].add(tag_id_map[tag_relation.tag_id])
        coin_id_explorer_map = defaultdict(set)
        for explorer in CoinExplorer.query.filter(
                CoinExplorer.status == CoinExplorer.Status.VALID
        ).all():
            coin_id_explorer_map[explorer.coin_info_id].add((explorer.name, explorer.url))

        data = []
        for coin_id, info in info_map.items():
            asset = info.code
            explorer_names, explorer_urls = [], []
            for explorer_name, explorer_url in coin_id_explorer_map.get(coin_id, set()):
                explorer_names.append(explorer_name)
                explorer_urls.append(explorer_url)
            item_data = {
                field: cls.get_value(info, field)
                for field in list(
                    set(CoinInformationBatchUploadResource.excel_info_fields) -
                    {'tags', 'coin_explorer_name', 'coin_explorer_url'}
                ) + ['code', 'status']
            }
            item_data['tags'] = ', '.join(asset_tag_relation_map.get(asset, set()))
            item_data['coin_explorer_name'] = '\r\n'.join(explorer_names)
            item_data['coin_explorer_url'] = '\r\n'.join(explorer_urls)
            data.append(item_data)
        return data


@ns.route('/coin-information/icon-upload')
@respond_with_code
class CoinInformationIconUploadResource(Resource):

    ALLOWED_ZIP_FORMAT = ['png', 'jpeg', 'jpg', 'tiff']
    IMAGE_TYPE = ('png', 'jpeg', 'jpg', 'webp', 'gif', 'svg', 'tiff')

    @classmethod
    def post(cls):
        """ 运营-币种资料-上传币种图标 """
        origin_img = request.files.get('img')
        origin_file_size = len(origin_img.read())
        origin_img.seek(0)
        if not origin_img:
            raise InvalidArgument
        ext = os.path.splitext(origin_img.filename)[1].lstrip('.')
        img_type = ext.lower()
        if ext.lower() not in cls.IMAGE_TYPE:
            raise ImageFormatError
        zip_img, zip_img_type, source_filename = ImageUploadResource.try_auto_zip(ext, origin_img)

        if zip_img is origin_img:
            file_size = origin_file_size
        else:
            file_size = len(zip_img)

        if file_size <= origin_file_size:
            if file_size > 300 * 1024:
                raise InvalidArgument(message="上传失败，体积系统无法压缩≤300k（输出体积），请联系设计压缩后上传")
            img = zip_img
            ext = img_type = zip_img_type
        else:
            if file_size > 500 * 1024:
                raise InvalidArgument(message="上传失败，原图体积过大，请联系设计压缩后上传")
            origin_img.seek(0)
            img = origin_img
        return ImageUploadResource.upload_file(img, ext, img_type, source_filename)


@ns.route('/api-coin-info-result')
@respond_with_code
class ApiCoinInfoResultResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            api_id=fields.String(required=True)
        )
    )
    def get(cls, **kwargs):
        """运营-币种资料-查看API获取的币种资料"""
        api_id = kwargs['api_id']
        r = ApiCoinInformationResult.query.filter(
            ApiCoinInformationResult.api_id == api_id
        ).first()
        if not r:
            raise InvalidArgument(message=f"{api_id}对应的数据未找到,请检查API ID的填写")
        _fields = [
            'api_id', 'official_website', 'white_paper', 'report_url',
            'source_code', 'telegram', 'facebook', 'twitter', 'reddit', 'medium',
            'discord', 'youtube', 'instagram', 'en_short_description',
            'cn_short_description', 'en_long_description', 'cn_long_description']
        if r.coin_info_id:
            info = CoinInformation.query.get(r.coin_info_id)
            asset = info.code
            name = info.name
        else:
            info = CoinInformation.query.filter(CoinInformation.api_id == api_id).first()
            if not info:
                asset = ''
                name = ''
            else:
                asset = info.code
                name = info.name

        return {
            field: getattr(r, field)
            for field in _fields
        } | dict(asset=asset, name=name)

    @classmethod
    @ns.use_kwargs(
        dict(
            api_id=fields.String(required=True)
        )
    )
    def post(cls, **kwargs):
        """运营-币种资料-通过API获取币种资料"""
        api_id = kwargs['api_id']
        c = TokenInsightClient()
        try:
            result = c.get_detail_by_id(api_id)
        except BaseHTTPClient.BadResponse:
            raise InvalidArgument(message="获取失败，请检查api id填写是否正确")
        r = ApiCoinInformationResult.query.filter(
            ApiCoinInformationResult.api_id == api_id
        ).first()
        info = CoinInformation.query.get(api_id)
        if r:
            for k, v in result.items():
                setattr(r, k, v)
            r.api_source = ApiCoinInformationResult.ApiSource.TOKEN_INSIGHT
            if info:
                r.coin_info_id = info.id
            db.session.commit()
        else:
            result = ApiCoinInformationResult(
                api_source=ApiCoinInformationResult.ApiSource.TOKEN_INSIGHT,
                api_id=api_id,
                **result
            )
            if info:
                result.coin_info_id = info.id

            db.session_add_and_commit(result)

    @classmethod
    @ns.use_kwargs(
        dict(
            api_id=fields.String(required=True),
            official_website=fields.String(),
            white_paper=fields.String(),
            report_url=fields.String(),
            source_code=fields.String(),
            telegram=fields.String(),
            facebook=fields.String(),
            twitter=fields.String(),
            reddit=fields.String(),
            medium=fields.String(),
            discord=fields.String(),
            youtube=fields.String(),
            instagram=fields.String(),
            en_short_description=fields.String(),
            cn_short_description=fields.String(),
            en_long_description=fields.String(),
            cn_long_description=fields.String(),
        )
    )
    def patch(cls, **kwargs):
        """运营-币种资料-编辑API获取的下载资料"""
        api_id = kwargs['api_id']
        _fields = [
            'official_website', 'white_paper', 'report_url',
            'source_code', 'telegram', 'facebook', 'twitter', 'reddit', 'medium',
            'discord', 'youtube', 'instagram', 'en_short_description',
            'cn_short_description', 'en_long_description', 'cn_long_description']
        r = ApiCoinInformationResult.query.filter(
            ApiCoinInformationResult.api_id == api_id
        ).first()

        info = CoinInformation.query.get(r.api_id)
        if r:
            for k, v in kwargs.items():
                if k in _fields:
                    setattr(r, k, v)
            if info:
                r.coin_info_id = info.id
            db.session.commit()
        else:
            raise InvalidArgument(message=f"{api_id}对应的数据未找到,请检查填写的API ID")
