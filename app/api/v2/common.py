# -*- coding: utf-8 -*-
from enum import Enum
from typing import Set

from app.business import mem_cached
from app.common import SubAccountPermission
from app.models import WithdrawalPrivilegedUser, Withdrawal, Deposit, MarginLoanOrder


class BalanceAccountType(Enum):
    SPOT = "SPOT"
    MARGIN = "MARGIN"
    FUTURES = "FUTURES"


class WithdrawalMethod(Enum):

    ON_CHAIN = "on_chain"
    INTER_USER = 'inter_user'

    def get_withdrawal_type(self):
        return Withdrawal.Type.ON_CHAIN if self is self.ON_CHAIN else Withdrawal.Type.LOCAL

    @classmethod
    def get_method(cls, _type: Withdrawal.Type):
        return cls.ON_CHAIN if _type == Withdrawal.Type.ON_CHAIN else cls.INTER_USER


class DepositMethod(Enum):

    ON_CHAIN = "on_chain"
    INTER_USER = 'inter_user'

    def get_deposit_type(self):
        return Deposit.Type.ON_CHAIN if self is self.ON_CHAIN else Deposit.Type.LOCAL

    @classmethod
    def get_method(cls, _type: Deposit.Type):
        return cls.ON_CHAIN if _type == Deposit.Type.ON_CHAIN else cls.INTER_USER


class SubAccountParamPermission(Enum):

    FUTURES = 'futures'
    MARGIN = 'margin'
    AMM = 'amm'
    API = 'api'

    def get_origin_permission(self) -> SubAccountPermission:
        _mapping = {
            self.FUTURES: SubAccountPermission.PERPETUAL,
            self.MARGIN: SubAccountPermission.MARGIN,
            self.AMM: SubAccountPermission.AMM,
            self.API: SubAccountPermission.API,
        }
        return _mapping[self]

    @classmethod
    def parse_origin(cls, _p: SubAccountPermission):
        _mapping = {
           SubAccountPermission.PERPETUAL: cls.FUTURES,
           SubAccountPermission.MARGIN: cls.MARGIN,
           SubAccountPermission.AMM: cls.AMM,
           SubAccountPermission.API: cls.API
        }
        return _mapping[_p]


class MarginLoanOrderParamStatus(Enum):

    LOAN = 'loan'
    DEBT = 'debt'
    LIQUIDATED = 'liquidated'
    FINISH = 'finish'

    def get_origin(self) -> MarginLoanOrder.StatusType:
        _mapping = {
            self.LOAN: MarginLoanOrder.StatusType.PASS,
            self.DEBT: MarginLoanOrder.StatusType.ARREARS,
            self.LIQUIDATED: MarginLoanOrder.StatusType.BURST,
            self.FINISH: MarginLoanOrder.StatusType.FINISH,
        }
        return _mapping[self]

    @classmethod
    def parse_origin(cls, _s: MarginLoanOrder.StatusType):
        _mapping = {
            MarginLoanOrder.StatusType.PASS: cls.LOAN,
            MarginLoanOrder.StatusType.ARREARS: cls.DEBT,
            MarginLoanOrder.StatusType.BURST: cls.LIQUIDATED,
            MarginLoanOrder.StatusType.FINISH: cls.FINISH,
            MarginLoanOrder.StatusType.CREATE: cls.LOAN,
            MarginLoanOrder.StatusType.FAIL: cls.FINISH,
        }
        return _mapping[_s]


EMPTY_PAGINATION_DATA = dict(data=[], pagination=dict(total=0, has_next=False))


@mem_cached(600)
def withdrawal_privileged_user_ids() -> Set[int]:
    rows = WithdrawalPrivilegedUser.query.filter(
        WithdrawalPrivilegedUser.status == WithdrawalPrivilegedUser.Status.VALID
    ).with_entities(
        WithdrawalPrivilegedUser.user_id
    ).all()
    return {x for x, in rows}
