# -*- coding: utf-8 -*-
from decimal import Decimal

from flask import g

from ...common import (Namespace, Resource, respond_with_code_new_message)
from ...common.decorators import require_api_v2_auth
from ....assets import list_all_assets
from ....caches.prices import InvisibleAssetsCache
from ....exceptions import (
    UserCreditInfoNotExists,
)
from ....models import (
    CreditUser, CreditBalance
)

ns = Namespace('Credit')


@ns.route('/info')
@respond_with_code_new_message
class CreditInfoResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    def get(cls):
        credit_user = CreditUser.query.filter(
            CreditUser.user_id == g.user.id,
            CreditUser.status == CreditUser.StatusType.PASS
        ).first()
        if not credit_user:
            raise UserCreditInfoNotExists
        result = {
            'withdrawal_risk': credit_user.withdraw_rate,
            'equity': credit_user.current_balance,
            'repaied': credit_user.unfinished_credit_balance,
            'withdrawal_value': max(Decimal(), credit_user.can_withdraw_balance),
        }
        if credit_user.warn_rate is None:
            result['risk_rate'] = '0'
        else:
            result['risk_rate'] = credit_user.warn_rate
        return result


@ns.route('/balance')
@respond_with_code_new_message
class CreditBalanceResource(Resource):

    @classmethod
    @require_api_v2_auth
    def get(cls):
        rows = CreditBalance.query.filter(
            CreditBalance.user_id == g.user.id,
            CreditBalance.unflat_amount > 0,
        ).all()

        include_assets = set(list_all_assets()) - InvisibleAssetsCache().read()
        return [dict(
            ccy=x.asset,
            repaid=x.unflat_amount,
            interest=x.interest_amount
        ) for x in rows if x.asset in include_assets]
