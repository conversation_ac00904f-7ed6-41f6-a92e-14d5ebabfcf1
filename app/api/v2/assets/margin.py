# -*- coding: utf-8 -*-
import decimal
from decimal import Decimal

from flask import g
from marshmallow import fields

from app.api.common import (
    Namespace, respond_with_code_new_message, Resource,
    lock_request, limit_user_frequency,
)
from app.api.common.decorators import (
    api_permission_deco, require_user_permission,
    require_api_v2_auth,
)
from app.api.common.fields import (
    PositiveDecimalField, PageField, LimitField,
    EnumField, MarketField, AssetField,
)
from app.api.common.responses import api_v2_success
from app.api.v2.common import MarginLoanOrderParamStatus
from app.business import (
    CacheLock,
    LockKeys, UserSettings,
)
from app.business.margin.helper import (
    get_user_day_rate_fee, get_user_margin_balances,
)
from app.business.margin.loan import MarginOrderLoanOperation, report_loan_event, report_flat_event
from app.business.margin.repayment import MarginOrderFlatOperation
from app.caches import (
    <PERSON>ginAccountIdCache, <PERSON><PERSON><PERSON>setRuleCache, MarketCache,
    MarginAccountNameCache,
)
from app.common import PrecisionEnum, SubAccountPermission
from app.exceptions import (
    InvalidArgument,
    MarginBurstForbiddenFlat, MarginFlatAmountLimit,
)
from app.models import MarginLoanOrder, db
from app.utils.date_ import datetime_to_ms
from app.utils.helper import Struct

ns = Namespace('Margin')


@ns.route('/interest-limit')
@respond_with_code_new_message
class MarginConfigResource(Resource):

    @classmethod
    @require_api_v2_auth
    @ns.use_kwargs(dict(
        market=MarketField(
            required=True
        ),
        ccy=AssetField(required=True)
    ))
    def get(cls, **kwargs):
        market, asset = kwargs["market"], kwargs["ccy"]
        online_margin_markets = MarginAccountIdCache.list_online_markets()
        markets = list(online_margin_markets.values())
        if market not in markets:
            raise InvalidArgument
        market_cache = MarketCache(market).dict
        margin_cache = MarginAccountNameCache(market).dict
        margin_market_data = {
            "account_id": margin_cache["id"],
            "leverage": margin_cache['leverage'],
            "market": market,
            "base_asset": market_cache["base_asset"],
            "quote_asset": market_cache["quote_asset"],
            "warning_rate": margin_cache["warning_rate"],
            "max_liquidation_rate": margin_cache["max_liquidation_rate"],
        }
        if asset not in (market_cache["base_asset"], market_cache["quote_asset"]):
            raise InvalidArgument
        asset_rule_config = MarginAssetRuleCache(asset).dict
        return {
            "market": market,
            "ccy": asset,
            "leverage": margin_market_data["leverage"],
            "min_amount": asset_rule_config["min_loan"],
            "max_amount": asset_rule_config["max_loan"],
            "daily_interest_rate": get_user_day_rate_fee(g.user.id, asset),
        }


@ns.route('/borrow-history')
class MarginBorrowHistoryResource(Resource):

    GET_SCHEMA = dict(
        page=PageField(),
        limit=LimitField(),
        market=MarketField(),
        status=EnumField(MarginLoanOrderParamStatus, case_sensitive=False)
    )

    @classmethod
    @ns.use_kwargs(GET_SCHEMA)
    @require_api_v2_auth
    def get(cls, **kwargs):
        params = Struct(**kwargs)

        if not params.market:
            account_id = None
        else:
            online_margin_markets = MarginAccountIdCache.list_online_markets()
            margin_accounts = dict(zip(online_margin_markets.values(),
                                       online_margin_markets.keys()))
            if params.market not in margin_accounts:
                raise InvalidArgument(data=params.market)
            account_id = margin_accounts[params.market]

        result = MarginOrderLoanOperation.get_loan_history(
            g.user.id,
            account_id,
            None,
            MarginLoanOrderParamStatus.get_origin(kwargs["status"])
            if kwargs.get("status", None) else None,
            params.page,
            params.limit
        )

        data = []
        for _r in result["data"]:
            data.append(
                {
                    "borrow_id": _r["loan_id"],
                    "created_at": _r["create_time"] * 1000,
                    "market": _r["market_type"],
                    "ccy": _r["coin_type"],
                    "daily_interest_rate": _r["day_rate"],
                    "expired_at":  _r["expire_time"] * 1000,
                    "borrow_amount": _r["loan_amount"],
                    "to_repaied_amount": _r["unflat_amount"],
                    "is_auto_renew": _r["is_renew"],
                    "status": MarginLoanOrderParamStatus.parse_origin(_r["status"])
                }
            )
        pagination = dict(total=result["total"], has_next=result["has_next"])
        return api_v2_success(dict(data=data, pagination=pagination))


@ns.route('/borrow')
@respond_with_code_new_message
class BorrowResource(Resource):

    POST_SCHEMA = dict(
        market=MarketField(required=True),
        ccy=AssetField(required=True),
        borrow_amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES,
                                           rounding=decimal.ROUND_DOWN, required=True),
        is_auto_renew=fields.Boolean(required=True)
    )

    @classmethod
    @require_api_v2_auth
    @require_user_permission(sub_account_permissions=[SubAccountPermission.MARGIN])
    @api_permission_deco(check_trade=True)
    @lock_request(with_user=True)
    @limit_user_frequency(count=5, interval=5)
    @ns.use_kwargs(POST_SCHEMA)
    def post(cls, **kwargs):
        payload = Struct(**kwargs)
        operation = MarginOrderLoanOperation(g.user.id, margin_identity=payload.market)
        with CacheLock(LockKeys.margin_loan_or_flat(g.user.id), wait=False), \
             CacheLock(LockKeys.user_margin_account(g.user.id, operation.account_id), wait=False):
            db.session.rollback()
            operation = MarginOrderLoanOperation(g.user.id,
                                                 margin_identity=payload.market)
            loan_id = operation.add_new_loan_order(payload.ccy,
                                                   payload.borrow_amount,
                                                   payload.is_auto_renew)
            order: MarginLoanOrder = MarginLoanOrder.query.get(loan_id)
            report_loan_event(g.user.id)
            return dict(
                borrow_id=loan_id,
                market=order.market_name,
                ccy=order.asset,
                daily_interest_rate=order.day_rate,
                expired_at=datetime_to_ms(order.expire_at),
                borrow_amount=order.loan_amount,
                to_repaied_amount=order.unflat_amount + order.interest_amount,
                status=MarginLoanOrderParamStatus.parse_origin(order.status)
            )


@ns.route('/repay')
@respond_with_code_new_message
class RepayResource(Resource):

    POST_SCHEMA = dict(
        market=MarketField(required=True),
        ccy=AssetField(required=True),
        borrow_id=fields.Integer(),
        amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES,
                                    rounding=decimal.ROUND_DOWN, required=True),
    )

    @classmethod
    @require_api_v2_auth
    @api_permission_deco(check_trade=True)
    @ns.use_kwargs(POST_SCHEMA)
    def post(cls, **kwargs):
        data = Struct(**kwargs)
        user_id = g.user.id

        online_margin_markets = MarginAccountIdCache.list_online_markets()
        margin_accounts = dict(zip(online_margin_markets.values(),
                                   online_margin_markets.keys()))
        if data.market not in margin_accounts:
            raise InvalidArgument(data.market)
        account_id = margin_accounts[data.market]

        setting = UserSettings(g.user.id)
        if account_id in setting.forbidden_margin_flat:
            raise MarginBurstForbiddenFlat
        if data.amount < Decimal('0.********'):
            raise InvalidArgument(data.amount)
        with CacheLock(LockKeys.margin_loan_or_flat(user_id), wait=False),\
             CacheLock(LockKeys.user_margin_account(user_id, account_id), wait=False):
            db.session.rollback()
            tool = MarginOrderFlatOperation(user_id=user_id,
                                            account_id=account_id,
                                            asset=data.ccy)

            max_amount = tool.get_can_flat_amount()
            if max_amount < data.amount:
                raise MarginFlatAmountLimit
            if data.borrow_id:
                tool.flat_one_normal_order(data.borrow_id, data.amount)
            else:
                tool.flat_normal_orders(data.amount)

            # 添加还币完成之后欠款状态检查以及状态更新
            tool.check_user_arrers()
            report_flat_event(user_id)
            return {}


@ns.route('/balance')
class BalanceResource(Resource):

    @classmethod
    @require_api_v2_auth
    @ns.use_kwargs(
        dict(market=MarketField())
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        if market := kwargs.get('market', ''):
            if market not in MarginAccountNameCache.list_online_markets().values():
                raise InvalidArgument(market)
            balance_list = get_user_margin_balances(user_id, market)
        else:
            balance_list = get_user_margin_balances(user_id)
        result = []
        for data in balance_list:
            balance_base_ccy = data['balance_data']["sell_type"]
            balance_quote_ccy = data['balance_data']['buy_type']
            result.append(dict(
                margin_account=data['market_type'],
                base_ccy=data['sell_asset_type'],
                quote_ccy=data['buy_asset_type'],
                available=dict(
                    base_ccy=balance_base_ccy['available'],
                    quote_ccy=balance_quote_ccy['available'],
                ),
                frozen=dict(
                    base_ccy=balance_base_ccy['frozen'],
                    quote_ccy=balance_quote_ccy['frozen'],
                ),
                repaid=dict(
                    base_ccy=data['loan']['sell_type'],
                    quote_ccy=data['loan']['buy_type'],
                ),
                interest=dict(
                    base_ccy=data['interest']['sell_type'],
                    quote_ccy=data['interest']['buy_type'],
                ),
                rik_rate=data['rate'],
                liq_price=data['burst_price'],
            ))
        return api_v2_success(dict(data=result))
