# -*- coding: utf-8 -*-
import json

from app.api.common import Namespace, Resource
from app.api.common.responses import api_v2_success
from app.caches.system import TempMaintainCache, SystemMaintainManagerCache
from app.models import TempMaintain
from app.utils import current_timestamp

ns = Namespace('Maintain')


@ns.route('/info')
class MaintainInfoResource(Resource):

    V2_MAINTAIN_SCOPE_MAP = {
        TempMaintain.MaintainScope.ALL.name: 'ALL_SITE',
        TempMaintain.MaintainScope.SPOT.name: 'SPOT',
        TempMaintain.MaintainScope.PERPETUAL.name: 'FUTURES',
        TempMaintain.MaintainScope.ALL_SITE.name: 'ALL_SITE',
    }

    @classmethod
    def format_temp_maintain_row(cls, row):
        res = dict(
            started_at=row['started_at'] * 1000,
            ended_at=row['ended_at'] * 1000,
            protect_duration_start=row['protect_duration_start'] * 1000,
            protect_duration_end=row['protect_duration_end'] * 1000,
            announce_enabled=row['jump_page_enabled'],
            announce_url=row['jump_url'],
            scope=[cls.V2_MAINTAIN_SCOPE_MAP[scope] for scope in row['scope']],
        )
        return res

    @classmethod
    def format_maintain_row(cls, row):
        res = dict(
            started_at=row['start_time'] * 1000,
            ended_at=row['end_time'] * 1000,
            protect_duration_start=0,
            protect_duration_end=0,
            announce_enabled=True if row['url'] else False,
            announce_url=row['url'],
            scope=[TempMaintain.MaintainScope.ALL_SITE.name],
        )
        return res

    @classmethod
    def get_maintain_info_list(cls):
        delta_seconds = 60 * 30
        cache = SystemMaintainManagerCache()
        data = cache.saving_data
        res = []
        if data and data['on']:
            current_ts = current_timestamp(to_int=True)
            if data['start_time'] - current_ts < delta_seconds and data['end_time'] >= current_ts:
                maintain_info = cls.format_maintain_row(data)
                res.append(maintain_info)
        return res

    @classmethod
    def get_temp_maintain_info_list(cls):
        cache = TempMaintainCache()
        temp_maintain_list = []
        if _data := cache.read():
            temp_maintain_list = [cls.format_temp_maintain_row(i) for i in json.loads(_data)]

        return temp_maintain_list

    @classmethod
    def get(cls):
        maintain_list = cls.get_maintain_info_list()
        temp_maintain_list = cls.get_temp_maintain_info_list()
        data = maintain_list + temp_maintain_list
        return api_v2_success(dict(data=data))
