# -*- coding: utf-8 -*-

from collections import defaultdict

from webargs import fields

from ..common import Resource, Namespace, respond_with_code, json_string_success
from ...business import PriceManager
from ...business.amm import LiquidityService
from ...business.margin.helper import get_exchange_market_name, MarginUserAccountInfo
from ...caches import (
    AssetUSDPricesCache, AmmMarketCache, AssetCache,
    InternalMarketViewCache, MarketCache, MarginExchangeAssetMappingCache,
)
from ...caches.kline import AssetRecentChangeRateCache
from ...common import Currency
from ...exceptions import ServiceUnavailable, InvalidArgument
from ...models import (
    Market, MarginIndex, MarginIndexDetail, ComposeIndex,
    LiquidityPool, AmmMarket, CoinInformation
)
from ...utils import now

ns = Namespace('Market')


@ns.route('/list')
@respond_with_code
class MarketListResource(Resource):

    @classmethod
    def get(cls):
        cache = InternalMarketViewCache()
        if data := cache.read():
            return json_string_success(data)
        return cache.reload()


@ns.route('/margin-market')
@respond_with_code
class MarginMarketResource(Resource):

    @classmethod
    def get(cls):
        return MarginUserAccountInfo(None).all_account_info


@ns.route('/usd2cny')
@respond_with_code
class USD2CNYRateResource(Resource):

    @classmethod
    def get(cls):
        if (cny_to_usd := PriceManager.fiat_to_usd(Currency.CNY)) <= 0:
            raise ServiceUnavailable
        return dict(
            rate=1 / cny_to_usd
        )


@ns.route('/currency_rate')
@respond_with_code
class CurrencyRateResource(Resource):

    ALLOWED_PAIRS = {('USD', 'CNY'), ('USDT', 'USD')}

    @classmethod
    @ns.use_kwargs(dict(
        from_asset=fields.String(required=True),
        to_aset=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        from_asset, to_asset = kwargs['from_asset'], kwargs['to_asset']
        if (from_asset, to_asset) not in cls.ALLOWED_PAIRS:
            raise InvalidArgument
        return dict(
            rate=PriceManager.convert_price(from_asset, 1, to_asset)
        )


@ns.route('/price')
@respond_with_code
class MarketPriceResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        from_coin_type=fields.String(required=True),
        to_coin_type=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        from_asset, to_asset = kwargs['from_coin_type'], kwargs['to_coin_type']
        return dict(
            price=PriceManager.convert_price(from_asset, 1, to_asset)
        )


@ns.route('/index/new_config')
@respond_with_code
class MarketIndexNewConfigResource(Resource):
    def get(self):
        """
        example return
        {
        # 正常指数
        "BTCUSDT": {
            "prec": 2,
            "sources": [
                {
                  "exchange": "huobiglobal",
                  "trade_url": "https://api.huobi.pro/market/trade?symbol=btcusdt",
                  "weight": "0.33"
                },
                {
                    "exchange": "binance",
                    "trade_url": "https://api.binance.com/api/v1/trades?symbol=BTCUSDT&limit=1",
                    "weight": "0.33"
                },
                {
                    "exchange": "coinex",
                    "trade_url": "https://api.coinex.com/v1/market/deals?market=btcusdt&limit=1",
                    "weight": "0.34"
                },
              ]
            },
        # 合成指数的返回值
        "ETHBTC": {
            "prec": 2,
            "use_compose": true,
            "compose_first_market": "ETHUSDT",
            "compose_second_market": "BTCUSDT",
            "compose_methd": 2
            }
        }
        ...
        }
        :return:
        """
        data = MarginIndex.query.filter(MarginIndex.status == MarginIndex.StatusType.OPEN).all()
        all_markets = MarketCache.list_online_markets() + MarketCache.list_offline_markets()
        special_assets_mapping = defaultdict(dict)
        # 获取特殊映射的币种
        for _v in MarginIndexDetail.ExchangeNameType:
            _cache = MarginExchangeAssetMappingCache(_v.value)
            special_assets_mapping[_v] = _cache.hgetall()
        markets_data = {}
        for _market_detail in Market.query.with_entities(
            Market.name,
            Market.base_asset,
            Market.quote_asset
        ).all():
            _market = _market_detail.name
            if _market not in all_markets:
                continue
            markets_data[_market] = dict(
                name=_market,
                base_asset=_market_detail.base_asset,
                quote_asset=_market_detail.quote_asset,
            )
        all_index_id_mapping = {
            v.id: v.market_name
            for v in data if v.market_name in markets_data}

        index_detail_dict = defaultdict(list)
        detail_query = MarginIndexDetail.query.filter(
            MarginIndexDetail.margin_index_id.in_(list(all_index_id_mapping.keys())),
            MarginIndexDetail.status == MarginIndexDetail.StatusType.PASS)
        for detail in detail_query:
            if detail.margin_index_id not in all_index_id_mapping:
                continue
            _market_name = all_index_id_mapping[detail.margin_index_id]
            _market_base_asset = markets_data[_market_name]["base_asset"]
            _market_quote_asset = markets_data[_market_name]["quote_asset"]
            index_detail_dict[detail.margin_index_id].append(
                dict(
                    exchange=detail.exchange_name.name.lower(),
                    symbol=get_exchange_market_name(detail.exchange_name, _market_base_asset,
                                                    _market_quote_asset, special_assets_mapping),
                    weight=str(detail.weight)
                )
            )

        result = {}
        for v in data:
            if v.id in all_index_id_mapping:
                result[v.market_name] = dict(
                    prec=int(v.price_precision),
                    risk_check_time=v.risk_check_time,
                    sources=index_detail_dict[v.id]
                )
        compose_data = ComposeIndex.query.filter(
            ComposeIndex.status == ComposeIndex.StatusType.OPEN
        )
        for v in compose_data:
            result[v.name] = dict(
                use_compose=True,
                prec=int(v.price_precision),
                compose_first_market=v.first_market,
                compose_second_market=v.second_market,
                compose_methd=v.compose_method
            )
        return result


@ns.route('/rate')
@respond_with_code
class RatesResource(Resource):

    @classmethod
    def get(cls):
        prices = AssetUSDPricesCache().value.copy()
        return prices


@ns.route('/amm/list')
@respond_with_code
class AmmMarketListResource(Resource):

    @classmethod
    def get(cls):
        amm_markets = AmmMarket.query.filter(
            AmmMarket.status == AmmMarket.Status.ONLINE
        ).all()
        market_names = [x.name for x in amm_markets]
        markets = Market.query.filter(Market.name.in_(market_names)).all()
        pools = LiquidityPool.query.filter(LiquidityPool.market.in_(market_names)).all()

        markets = {x.name: x for x in markets}
        pools = {x.market: x for x in pools}

        result = []
        for amm_market in amm_markets:
            market_name = amm_market.name
            market = markets[market_name]
            pool = pools[market_name]
            # only online and bidding market

            valid = False
            match market.status:
                case Market.Status.ONLINE:
                    valid = True
                case Market.Status.BIDDING:
                    valid = True
                    if not amm_market.allow_in_bidding:
                        valid = False
                    if market.started_at and market.started_at > now():
                        valid = False
            if not valid:
                continue

            result.append(dict(
                name=market_name,
                base_asset=market.base_asset,
                quote_asset=market.quote_asset,
                base_asset_precision=market.base_asset_precision,
                quote_asset_precision=market.quote_asset_precision,
                min_order_amount=market.min_order_amount,
                market_value_limit=LiquidityService.MIN_LIQUIDITY_USD,
                user_id=pool.system_user_id,
                amm_type=amm_market.amm_type.name,
                price_delta=amm_market.delta,
                min_price=amm_market.min_price,
                max_price=amm_market.max_price
            ))
        
        sorted_markets = AmmMarketCache.list_amm_markets()
        sorted_markets = {x: i for x,i in enumerate(sorted_markets)}
        last = len(sorted_markets)
        result.sort(key=lambda x: sorted_markets.get(x['name'], last))
        return result


@ns.route('/assets')
@respond_with_code
class Assets(Resource):
    @classmethod
    def get(cls):
        all_coins = set(AssetCache.list_all_assets())
        all_coin_infos = CoinInformation.query.filter(
            CoinInformation.status == CoinInformation.Status.VALID,
        ).with_entities(
            CoinInformation.code,
            CoinInformation.name,
        ).all()
        return {coin_info.code: coin_info.name for coin_info in all_coin_infos if coin_info.code in all_coins}


@ns.route('/assets/recent-change-rates')
@respond_with_code
class AssetRecentChangeRates(Resource):
    @classmethod
    def get(cls):
        return AssetRecentChangeRateCache().get_rates_str()
