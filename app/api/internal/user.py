# -*- coding: utf-8 -*-
from flask import g
from flask import request
from flask_api.exceptions import AuthenticationFailed
from webargs import fields as wa_fields

from app import config
from app.models.authority import AdminUser
from ..common import (
    Resource, Namespace, respond_with_code,
    require_api_auth, failure
)
from ..common.responses import api_v2_success
from ..common.decorators import require_api_v2_ws_auth
from ...business.market_maker import MarketMakerHelper
from ...business.fee_constant import DEFAULT_CONTRACT_MAKER_FEE, DEFAULT_CONTRACT_TAKER_FEE
from ...business.copy_trading.base import CopyTradingSettings
from ...business.copy_trading.trader import get_copy_trader_sub_user, CopyTraderPerpetualOp
from ...business.api_v2 import convert_response_code
from ...business import cached
from ...caches.auth import UserLoginTokenCache
from ...caches.user import SubAccountInfoCache
from ...common import LOGIN_TOKEN_SIZE, TradeBusinessType
from ...exceptions import InvalidArgument, SubAccountNotAllowed
from ...models import Api<PERSON><PERSON>, User, ImUser, SubAccount, P2pUser
from ...utils import (
    timestamp_to_datetime, amount_to_str
)

ns = Namespace('User')


@ns.route('/auth')
@respond_with_code
class AuthUserResource(Resource):

    @classmethod
    def get(cls):
        # 子账号认证token格式: '{token}:{sub_user_id}'
        token = request.headers.get('AUTHORIZATION', '')
        items = token.rsplit(":", 1)
        if len(items) == 1:
            token, sub_user_id = items[0], None
        else:
            token, sub_user_id = items[0], items[1]

        if not token or len(token) != LOGIN_TOKEN_SIZE:
            raise AuthenticationFailed
        token_cache = UserLoginTokenCache.from_token(token)
        if token_cache is None:
            raise AuthenticationFailed

        user_id = token_cache.user_id
        if sub_user_id is not None:
            user_id = cls._verify_sub_user_id(sub_user_id, user_id)

        return dict(
            user_id=user_id,
        )

    @classmethod
    def _verify_sub_user_id(cls, sub_user_id, user_id) -> int:
        try:
            sub_user_id = int(sub_user_id)
        except ValueError:
            raise AuthenticationFailed

        sub_acc_info = SubAccountInfoCache(sub_user_id).info()  # 普通子帐号
        if sub_acc_info:
            if user_id != sub_acc_info["main_user_id"] and user_id not in sub_acc_info["manage_user_ids"]:
                raise AuthenticationFailed
            return sub_user_id

        # 合约跟单交易-带单人|跟单人的跟单子帐号
        copy_trade_sub_acc = SubAccount.query.filter(
            SubAccount.user_id == sub_user_id,
            SubAccount.type.in_([SubAccount.Type.COPY_TRADER, SubAccount.Type.COPY_FOLLOWER]),
        ).with_entities(SubAccount.user_id).first()
        if copy_trade_sub_acc:
            return sub_user_id
        raise AuthenticationFailed


@ns.route('/api/auth')
@respond_with_code
class ApiAuthUserResource(Resource):

    @classmethod
    def get(cls):
        business = request.headers.get('BUSINESS', '')
        if business == '':
            business_type = TradeBusinessType.SPOT
        elif business == TradeBusinessType.PERPETUAL.name:
            business_type = TradeBusinessType.PERPETUAL
        else:
            raise InvalidArgument
        user = require_api_auth(lambda: g.user, business_type=business_type)()
        user_id = user.id
        data = dict(
            user_id=user_id
        )
        return data


@ns.route('/api/v2/auth')
@respond_with_code
class ApiV2AuthUserResource(Resource):
    @classmethod
    def get(cls):
        user = require_api_v2_ws_auth(lambda: g.user)()
        user_id = user.id
        data = dict(
            user_id=user_id
        )
        return data


@ns.route('/contract/api/auth')
@respond_with_code
class ContractApiAuthResource(Resource):
    STATUS_PASS = 1
    STATUS_DELETE = 3

    @classmethod
    @ns.use_kwargs(dict(
        update_time=wa_fields.Integer(required=True, default=0)
    ))
    def get(cls, **kwargs):
        update_time = kwargs['update_time']

        if update_time == 0:
            api_auth = ApiAuth.query
        else:
            api_auth = ApiAuth.query.filter(
                ApiAuth.updated_at >= timestamp_to_datetime(update_time)
            )

        results = [{
            'user_id': item.user_id,
            'status': cls.STATUS_PASS if item.status == ApiAuth.Status.VALID
            else cls.STATUS_DELETE,
            'allow_ips': item.allowed_ips.split('\n') if item.allowed_ips else [],
            'expired_time': int(item.expired_at.timestamp())
            if item.expired_at else 0,
            'secret_key': item.secret_key,
            'access_id': item.access_id,
            'allow_trade': item.trading_enabled,
        } for item in api_auth]
        sys_maker_fee = amount_to_str(DEFAULT_CONTRACT_MAKER_FEE)
        sys_taker_fee = amount_to_str(DEFAULT_CONTRACT_TAKER_FEE)
        return {
            'api_auth': results,
            'default_taker_fee_rate': sys_taker_fee,
            'default_maker_fee_rate': sys_maker_fee,
        }


@ns.route('/info')
@respond_with_code
class UserInfoResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_ids=wa_fields.String(required=True)
    ))
    def get(cls, **kwargs):
        user_ids = set()
        try:
            for user_id in kwargs['user_ids'].split(','):
                user_ids.add(int(user_id))
        except Exception:
            raise InvalidArgument
        users = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email, User.name).all()
        return [dict(
            id=x.id,
            email=x.email or '',
            name=x.name or ''
        ) for x in users]


@ns.route('/search')
@respond_with_code
class UserSearchResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        keyword=wa_fields.String(required=True)
    ))
    def get(cls, **kwargs):
        if not (keyword := kwargs['keyword'].strip()):
            raise InvalidArgument
        return User.search_for_users(keyword)


@ns.route('/admin-users')
@respond_with_code
class AdminUserListResource(Resource):

    @classmethod
    def get(cls):
        admin_users = AdminUser.query.filter(
            AdminUser.status == AdminUser.Status.PASSED
        ).with_entities(AdminUser.user_id, AdminUser.name).all()
        admin_user_name_map = dict(admin_users)
        user_ids = [item.user_id for item in admin_users] + list(config['SUPER_ADMIN_USER'])
        user_ids = list(set(user_ids))
        user_emails = User.query.filter(User.id.in_(
            user_ids)).with_entities(User.id,
                                     User.email).all()
        user_email_map = dict(user_emails)
        res = []
        # super users
        for user_id in user_ids:
            res.append(dict(user_id=user_id,
                            name=admin_user_name_map.get(user_id),
                            email=user_email_map.get(user_id, '')))
        return res


@ns.route('/risk')
@respond_with_code
class UserRiskResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True)
    ))
    def post(cls, **kwargs):
        pass
        # TODO: 处理风险用户


@ns.route("/im")
@respond_with_code
class ImUserResource(Resource):
    model = ImUser

    @classmethod
    def get(cls):
        token = request.headers.get('AUTHORIZATION', '')
        if not token or len(token) != LOGIN_TOKEN_SIZE:
            raise AuthenticationFailed
        token_cache = UserLoginTokenCache.from_token(token)
        if token_cache is None:
            raise AuthenticationFailed
        user_id = token_cache.user_id
        row = P2pUser.query.filter(P2pUser.user_id == user_id).first()
        if not row:
            raise InvalidArgument
        return {
            "im_user_id": row.biz_user_id
        }


@ns.route("/makers")
@respond_with_code
class MakersResource(Resource):

    @classmethod
    @cached(3600)
    def get(cls):
        makers = MarketMakerHelper.list_all_maker_ids()
        return makers


@ns.route("/copy-trading")
class CopyTraderUserResource(Resource):
    method_endpoint_whitelists = {
        ('GET', '/v2/assets/futures/balance'),
        ('GET', '/v2/futures/order-status'),
        ('GET', '/v2/futures/batch-order-status'),
        ('GET', '/v2/futures/pending-order'),
        ('GET', '/v2/futures/finished-order'),
        ('GET', '/v2/futures/pending-stop-order'),
        ('GET', '/v2/futures/finished-stop-order'),
        ('GET', '/v2/futures/pending-position'),
        ('GET', '/v2/futures/finished-position'),
        ('GET', '/v2/futures/position-adl-history'),
        ('GET', '/v2/futures/position-settle-history'),
        ('GET', '/v2/futures/position-margin-history'),
        ('GET', '/v2/futures/position-funding-history'),
        ('GET', '/v2/futures/order-deals'),
        ('GET', '/v2/futures/user-deals'),

        ('GET', '/v2/account/futures-market-settings'),
        ('POST', '/v2/account/futures-market-settings'),

        # 下单接口
        ('POST', '/v2/futures/order'),
        ('POST', '/v2/futures/stop-order'),
        ('POST', '/v2/futures/batch-order'),
        ('POST', '/v2/futures/batch-stop-order'),

        # 改单和撤单
        ('POST', '/v2/futures/modify-order'),
        ('POST', '/v2/futures/modify-stop-order'),
        ('POST', '/v2/futures/cancel-all-order'),
        ('POST', '/v2/futures/cancel-order'),
        ('POST', '/v2/futures/cancel-stop-order'),
        ('POST', '/v2/futures/cancel-batch-order'),
        ('POST', '/v2/futures/cancel-batch-stop-order'),
        ('POST', '/v2/futures/cancel-order-by-client-id'),
        ('POST', '/v2/futures/cancel-stop-order-by-client-id'),

        # 平仓、调整杠杆、止盈止损
        ('POST', '/v2/futures/close-position'),
        ('POST', '/v2/futures/adjust-position-margin'),
        ('POST', '/v2/futures/adjust-position-leverage'),
        ('POST', '/v2/futures/set-position-stop-loss'),
        ('POST', '/v2/futures/set-position-take-profit'),

    }

    require_running_method_endpoints = (
        ('POST', '/v2/futures/order'),
        ('POST', '/v2/futures/stop-order'),
        ('POST', '/v2/futures/batch-order'),
        ('POST', '/v2/futures/batch-stop-order'),
        ('POST', '/v2/futures/adjust-position-margin'),
        ('POST', '/v2/futures/adjust-position-leverage'),
        ('POST', '/v2/account/futures-market-settings'),
        ('POST', '/v2/futures/set-position-stop-loss'),
        ('POST', '/v2/futures/set-position-take-profit'),
    )

    check_count_method_endpoints = (
        ('POST', '/v2/futures/order'),
        ('POST', '/v2/futures/stop-order'),
        ('POST', '/v2/futures/batch-order'),
        ('POST', '/v2/futures/batch-stop-order'),
    )

    check_market_method_endpoints = (
        ('POST', '/v2/futures/order'),
        ('POST', '/v2/futures/stop-order'),
        ('POST', '/v2/futures/batch-order'),
        ('POST', '/v2/futures/batch-stop-order'),
        ('POST', '/v2/futures/adjust-position-margin'),
        ('POST', '/v2/futures/adjust-position-leverage'),
        ('POST', '/v2/futures/set-position-stop-loss'),
        ('POST', '/v2/futures/set-position-take-profit'),
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            method=wa_fields.String(required=True),  # 请求方法
            endpoint=wa_fields.String(required=True),  # 请求路径
            params=wa_fields.Dict,  # 请求参数 query_string or body
        )
    )
    def post(cls, **kwargs):
        """ 带单人主帐号的带单认证，返回带单子帐号id """
        try:
            res = cls.process_(kwargs)
            return api_v2_success(dict(data=res))
        except Exception as e:  # noqa
            # 返回API V2格式的错误码
            code = convert_response_code(getattr(e, 'code', None))
            message = getattr(e, 'message', '')
            data = getattr(e, 'data', {})
            return failure(code, message, data)

    @classmethod
    def process_(cls, kwargs):
        trader_user = User.query.get(kwargs['user_id'])
        if trader_user.is_sub_account:
            raise SubAccountNotAllowed

        method = kwargs["method"]
        endpoint = kwargs["endpoint"]
        params = kwargs.get("params") or {}
        require_running, check_pos_count = cls.parse_and_check_method_endpoint_params(method, endpoint, params)
        copy_trader_sub_user = get_copy_trader_sub_user(
            trader_user.id,
            require_running=require_running,
            check_pos_count=check_pos_count,
            check_data=None,
        )
        cls.check_operate(copy_trader_sub_user, method, endpoint, params)
        return {"sub_user_id": copy_trader_sub_user.id}

    @classmethod
    def parse_and_check_method_endpoint_params(cls, method: str, endpoint: str, params: dict):
        method_endpoint = (method, endpoint)
        if method_endpoint not in cls.method_endpoint_whitelists:
            raise InvalidArgument(message="Copy trading is not available for this service")

        if method_endpoint in cls.check_market_method_endpoints:
            try:
                if method_endpoint in (
                    ('POST', '/v2/futures/batch-order'),
                    ('POST', '/v2/futures/batch-stop-order'),
                ):
                    p_markets = {o['market'] for o in params['orders']}
                else:
                    p_markets = {params['market']}
            except:  # noqa
                raise InvalidArgument
            all_markets = CopyTradingSettings.markets
            if p_markets & all_markets != p_markets:
                raise InvalidArgument(message="This Futures market is not supported in copy trading")

        if method_endpoint in cls.require_running_method_endpoints:
            require_running = True
        else:
            require_running = False
        if method_endpoint in cls.check_count_method_endpoints:
            check_pos_count = True
        else:
            check_pos_count = False
        return require_running, check_pos_count

    @classmethod
    def check_operate(cls, sub_user: User, method: str, endpoint: str, params: dict):
        method_endpoint = (method, endpoint)
        if method_endpoint == ('POST', '/v2/futures/adjust-position-leverage'):
            market = params['market']
            leverage = params['leverage']
            CopyTraderPerpetualOp.check_can_adjust_leverage(leverage)
            position_type = 1 if params['margin_mode'] == 'isolated' else 2
            CopyTraderPerpetualOp.check_can_adjust_position_type(sub_user.id, market, position_type)
        if method_endpoint in cls.check_count_method_endpoints:
            if method_endpoint in (
                    ('POST', '/v2/futures/batch-order'),
                    ('POST', '/v2/futures/batch-stop-order'),
            ):
                p_markets = {o['market'] for o in params['orders']}
            else:
                p_markets = {params['market']}
            for p_market in p_markets:
                CopyTraderPerpetualOp.check_current_leverage(sub_user.id, p_market)
