#!/usr/bin/env python3
import json
from collections import defaultdict
from datetime import datetime
from decimal import Decimal

from sqlalchemy import func, and_, or_
from webargs import fields

from ..common import Namespace, Resource, respond_with_code
from ..common.fields import EnumField, TimestampField, ChainField
from ...assets import list_all_assets, get_asset_chain_config
from ...business import (
    WalletDepositStatus, new_deposit, transition_deposit_status, cached,
    ServerClient, SPOT_ACCOUNT_ID, SiteSettings, UserPreferences
)
from ...business.market_maker import MarketMakerHelper
from ...business.abnormal_deposit import AbnormalDepositBusiness
from ...business.wallet_integration import re_accept_deposit
from ...business.wallet_sign import WalletSigner
from ...business.risk_control.abnormal_issuance import check_abnormal_issuance
from ...models import (
    User, SubAccount, Deposit, CreditBalance, ColdWalletHistory, Withdrawal,
    AssetPrice, InvestmentAccount
)
from ...caches.wallets import ChainAssetPendingWithdrawalAmountCache
from ...exceptions import InvalidArgument
from ...models.kyt import DepositSenderRiskAssessment
from ...models.spot import SystemAssetLiability
from ...models.wallet import AbnormalDepositApplication, DepositAudit
from ...utils import group_by, batch_iter
from ...utils.parser import JsonEncoder

ns = Namespace('Wallet')


@ns.route('/credit/assets')
@respond_with_code
class CreditAssetsResource(Resource):

    @classmethod
    def get(cls):
        credit_unflat_query = CreditBalance.query.with_entities(
            CreditBalance.asset,
            func.sum(CreditBalance.unflat_amount).label('total_unflat_amount')
        ).group_by(
            CreditBalance.asset
        )
        credit_unflat_dict = defaultdict(Decimal)
        for _v in credit_unflat_query:
            credit_unflat_dict[_v.asset] += _v.total_unflat_amount
        return credit_unflat_dict


@ns.route('/credit/user/assets')
@respond_with_code
class CreditUserAssetsResource(Resource):

    @classmethod
    @cached(10 * 60)
    def get_internal_user_balances(cls):
        inner_user_ids = MarketMakerHelper.list_inner_maker_ids(include_sub_account=False)
        sub_query = SubAccount.query.filter(
            SubAccount.main_user_id.in_(inner_user_ids),
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
        ).with_entities(SubAccount.main_user_id, SubAccount.user_id).all()
        sub_account_mapping = {v.user_id: v.main_user_id for v in sub_query}
        total_user_ids = set(inner_user_ids + list(sub_account_mapping.keys()))
        result = defaultdict(lambda: defaultdict(Decimal))
        _client = ServerClient()
        for user_id in total_user_ids:
            balance_result = _client.get_user_accounts_balances(user_id)
            main_user_id = sub_account_mapping.get(user_id, user_id)
            for account_id, assets in balance_result.items():
                account_id = int(account_id)
                if SPOT_ACCOUNT_ID == account_id or account_id == InvestmentAccount.ACCOUNT_ID:
                    for asset, values in assets.items():
                        available, frozen = Decimal(values['available']), Decimal(values['frozen'])
                        if available + frozen > Decimal():
                            result[str(main_user_id)][asset] += available + frozen
        return result

    @classmethod
    def get(cls):
        credit_unflat_query = CreditBalance.query.filter(
            CreditBalance.unflat_amount > 0,
        ).all()
        user_ids = list({v.user_id for v in credit_unflat_query})
        user_type_mapping = dict()
        for batch_user_ids in batch_iter(user_ids, 1000):
            users = User.query.filter(User.id.in_(batch_user_ids)).with_entities(
                    User.user_type,
                    User.id,
                ).all()
            for _user_data in users:
                user_type_mapping[_user_data.id] = _user_data.user_type.name
        results = []
        inner_user_balances = cls.get_internal_user_balances()
        for credit_record in credit_unflat_query:
            user_type = user_type_mapping.get(credit_record.user_id, '')
            results.append(
                dict(
                    user_id=credit_record.user_id,
                    asset=credit_record.asset,
                    unflat_amount=credit_record.unflat_amount,
                    user_type=user_type,
                    # 只统计内部做市商的资产
                    balance=inner_user_balances.get(str(credit_record.user_id), dict()).get(credit_record.asset, Decimal())
                    if user_type == User.UserType.INTERNAL_MAKER.name else Decimal()
                )
            )
        return results


@ns.route('/deposits')
@ns.route('/deposits/<int:id_>')
@respond_with_code
class DepositsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        user_id=fields.Integer(required=True),
        type=fields.String(missing=''),
        asset=fields.String(required=True),
        chain=fields.String(required=True),
        address=fields.String(required=True),
        memo=fields.String(missing=''),
        amount=fields.Decimal(required=True),
        tx_id=fields.String(required=True),
        vout=fields.Integer(required=True),
        confirmations=fields.Integer(required=True),
        status=EnumField((WalletDepositStatus.TOO_SMALL, WalletDepositStatus.PROCESSING,
                          WalletDepositStatus.CONFIRMING, WalletDepositStatus.FINISHED,
                          WalletDepositStatus.CANCELLED), required=True),
        signature=fields.String(required=True),
        senders=fields.Raw(required=False),
    ))
    def post(cls, **kwargs):
        if Deposit.query.filter(Deposit.wallet_deposit_id == kwargs['id']).first():
            return
        kwargs["wallet_deposit_id"] = kwargs["id"]
        kwargs.pop("id")
        senders = kwargs.pop("senders", None)
        kwargs["type_"] = kwargs.pop("type", "")
        deposit = new_deposit(**kwargs, senders=senders)
        if not deposit:
            raise InvalidArgument(message="add deposit failed")

    @classmethod
    @ns.use_kwargs(dict(
        confirmations=fields.Integer(required=True),
        status=EnumField((WalletDepositStatus.PROCESSING,
                          WalletDepositStatus.CONFIRMING, WalletDepositStatus.FINISHED,
                          WalletDepositStatus.CANCELLED), required=True),
    ))
    def patch(cls, id_: int, **kwargs):
        dep = Deposit.query.filter(Deposit.wallet_deposit_id == id_).first()
        if not dep:
            raise InvalidArgument
        try:
            transition_deposit_status(
                dep,
                kwargs['status'],
                kwargs['confirmations'],
            )
        except ValueError as e:
            if isinstance(e.args, tuple) and len(e.args) > 0:
                raise InvalidArgument(message=e.args[0])
            raise InvalidArgument


@ns.route('/deposit/fix')
@respond_with_code
class DepositFixResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        status=EnumField((WalletDepositStatus.PROCESSING, ), required=True)
    ))
    def post(cls, **kwargs):
        """用于钱包组手动修正充值记录，目前只有已取消的提现修正为处理中这一个场景"""
        dep = Deposit.query.filter(Deposit.wallet_deposit_id == kwargs['id']).first()
        if not dep:
            raise InvalidArgument
        try:
            re_accept_deposit(dep.id)
        except ValueError:
            raise InvalidArgument


@ns.route('/history/pool')
@respond_with_code
class PoolWalletHistory(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        last_id=fields.Integer(required=True),
        end_time=TimestampField
    ))
    def get(cls, **kwargs):
        last_id = kwargs['last_id']
        query = ColdWalletHistory.query.filter(
            ColdWalletHistory.type == ColdWalletHistory.Type.POOL,
            ColdWalletHistory.id > last_id
        ).with_entities(
            ColdWalletHistory.id, ColdWalletHistory.asset,
            ColdWalletHistory.chain, ColdWalletHistory.amount
        )
        if end_time := kwargs.get('end_time'):
            query = query.filter(ColdWalletHistory.created_at < end_time)
        rows = query.all()
        result = []
        for (asset, chain), items in group_by(lambda x: (x.asset, x.chain), rows).items():
            result.append(dict(
                asset=asset,
                chain=chain,
                amount=sum(x.amount for x in items)
            ))
        return dict(
            items=result,
            max_id=max(x.id for x in rows) if rows else last_id
        )


@ns.route('/assets/liability')
@respond_with_code
class SystemAssetLiabilityResource(Resource):

    @classmethod
    def get_assets_liabilities(cls):
        result = {}
        assets_list = list_all_assets()
        records = SystemAssetLiability.query.order_by(SystemAssetLiability.id.desc()).limit(
            len(assets_list) * 2)
        for row in records:
            if row.asset not in result:
                result[row.asset] = row
        return result

    @classmethod
    @ns.use_kwargs(dict(
        assets=fields.DelimitedList(fields.String(required=True))
    ))
    def get(cls, **kwargs):
        records = cls.get_assets_liabilities()
        assets = kwargs.get("assets", [])
        result = dict()
        result_assets = list(records.keys()) if not assets else assets
        for asset in result_assets:
            if asset in records:
                r: SystemAssetLiability = records[asset]
                result[asset] = {
                    'sys_total': r.sys_total,
                    'sys_debt': r.sys_debt
                }
        return result


@ns.route('/withdrawal')
@respond_with_code
class WithdrawalResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ids=fields.DelimitedList(fields.Int(required=True))
    ))
    def get(cls, **kwargs):
        ids = kwargs.get('ids', [])
        if not 0 < len(ids) < 1000:
            raise InvalidArgument
        rows = Withdrawal.query.filter(Withdrawal.id.in_(ids)).with_entities(
            Withdrawal.id, Withdrawal.asset, Withdrawal.chain, Withdrawal.fee, Withdrawal.fee_asset,
        ).all()
        return [dict(
            id=x.id,
            asset=x.asset,
            chain=x.chain,
            fee=x.fee,
            fee_asset=x.fee_asset,
        ) for x in rows]


@ns.route('/withdrawal/fee')
@respond_with_code
class WithdrawalFeeResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        assets=fields.DelimitedList(fields.String),
        start=TimestampField(required=True),
        end=TimestampField(required=True)
    ))
    def get(cls, **kwargs):
        assets = kwargs.get('assets', [])
        start = kwargs['start']
        end = kwargs['end']
        where = []
        try:
            for a in assets:
                asset, chain = a.split('-')
                where.append(and_(Withdrawal.asset == asset.upper(), Withdrawal.chain == chain.upper()))
        except ValueError:
            raise InvalidArgument
        # sql:108s
        query = Withdrawal.query.filter(
            Withdrawal.approved_by_user_at >= start,
            Withdrawal.approved_by_user_at <= end,
            Withdrawal.type == Withdrawal.Type.ON_CHAIN,
            Withdrawal.asset == Withdrawal.fee_asset,
            Withdrawal.status == Withdrawal.Status.FINISHED
        )
        if where:
            query = query.filter(or_(*where))
        rows = query.group_by(Withdrawal.asset, Withdrawal.chain).with_entities(
            Withdrawal.asset, Withdrawal.chain,
            func.sum(Withdrawal.fee).label('total_fee'), func.count('*').label('total_count')
        ).all()
        return [dict(
            asset=x.asset,
            chain=x.chain,
            total_fee=x.total_fee,
            total_count=x.total_count
        ) for x in rows]


@ns.route('/assets/statistic/price')
@respond_with_code
class AssetStatisticPriceResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        search_time=TimestampField(required=True, to_hour=True),
    ))
    def get(cls, **kwargs):
        search_time = kwargs['search_time']
        # 时间与时间的比较需要先格式化
        format_search_time = datetime(
            search_time.year, search_time.month,
            search_time.day, search_time.hour)
        return AssetPrice.get_nearest_price_map(format_search_time)


@ns.route('/log/abnormal-issuance')
@respond_with_code
class LogAbnormalIssuanceResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True),
        chain=fields.String(required=True),
        cur_issue_amount=fields.Decimal(required=True),
        his_issue_amount=fields.Decimal(required=True),
        create_time=TimestampField(required=True),
    ))
    def post(cls, **kwargs):
        data = dict(
            asset=kwargs['asset'],
            chain=kwargs['chain'],
            cur_amount=kwargs['cur_issue_amount'],
            his_amount=kwargs['his_issue_amount'],
            updated_at=int(kwargs['create_time'].timestamp()),
        )
        check_abnormal_issuance.delay(json.dumps(data, cls=JsonEncoder))


@ns.route('/settings')
@respond_with_code
class SettingsResource(Resource):

    @classmethod
    def get(cls):
        return {
            'withdrawals_enabled': SiteSettings.withdrawals_enabled,
            'deposits_enabled': SiteSettings.deposits_enabled,
            'withdrawals_disabled_by_risk_control': SiteSettings.withdrawals_disabled_by_risk_control,
        }


@ns.route('/assets/<asset>/<chain>/settings')
@respond_with_code
class AssetSettingsResource(Resource):

    @classmethod
    def get(cls, asset, chain):
        conf = get_asset_chain_config(asset, chain)
        return {
            'withdrawals_disabled_by_risk_control': conf.withdrawals_disabled_by_risk_control,
        }


@ns.route('/sign-pre')
@respond_with_code
class SignPreResources(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        group=fields.Integer(required=True),
        chain=ChainField(required=True),
        sender=fields.String(required=True),
        message=fields.String(required=True),
        commitment=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        return WalletSigner.tss_sign_pre(**kwargs)


@ns.route('/sign')
@respond_with_code
class SignResources(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        group=fields.Integer(required=True),
        wwid=fields.List(fields.Integer, required=True),
        chain=ChainField(required=True),
        sender=fields.String(required=True),
        recipients=fields.List(fields.Dict, required=True),
        raw_data=fields.Dict(required=True),
        signer=fields.String(required=True),
        message=fields.String(required=True),
        sign_func=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        recipients = kwargs['recipients']
        for recipient in recipients:
            if not (addr := recipient.get('address')) or not isinstance(addr, str):
                raise InvalidArgument
            if not (amount := recipient.get('amount')) or not isinstance(amount, dict):
                raise InvalidArgument
            if (identity := amount.get('identity')) is None or not isinstance(identity, str):
                raise InvalidArgument
            if (value := amount.get('value')) is None or not isinstance(value, (str, int)):
                raise InvalidArgument
        return WalletSigner.sign_tx(**kwargs)


@ns.route("/deposit/sender/kyt")
@respond_with_code
class DepositRiskCheckResources(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """用户充值检查用户是否需要强制kyt"""
        user_id = kwargs['user_id']
        return UserPreferences(user_id).opening_p2p_function


@ns.route("/withdrawal/pending-amounts")
@respond_with_code
class AssetPendingWithdrawalAmountResources(Resource):
    @classmethod
    def get(cls):
        """ 提现待打币种数量 """
        result = []
        data = ChainAssetPendingWithdrawalAmountCache().read()
        for k, d in data.items():
            info = json.loads(d)
            chain_, asset_ = k.split(":")
            result.append(
                dict(
                    chain=chain_,
                    asset=asset_,
                    **info,
                )
            )
        return result


@ns.route("/kyt/deposit/safe")
@respond_with_code
class KYTDepositSafeResources(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ids=fields.List(fields.Integer, required=True),
    ))
    def post(cls, **kwargs):
        """通过钱包 id，并依据 KYT 来判断是否可以通知钱包侧转热钱包"""
        ids = kwargs['ids']
        # if not DepositRiskAssessmentManager.is_kyt_enabled():
        #     return ids
        did_to_wid = cls._get_deposit_id_mapping(ids)
        dids = set(did_to_wid.keys())
        wids = set(did_to_wid.values())
        wids_by_ass, pending_wids = cls._get_ret_by_risk_assessments(dids, did_to_wid)

        pending_dids_by_da = cls._get_pending_deposit_audits(dids)
        pending_wids_by_da = {did_to_wid[did] for did in pending_dids_by_da}
        # 注：迁移的老数据可能没有对应关系，应该需要手动处理
        wids = wids & wids_by_ass
        safe_wids = wids - pending_wids - pending_wids_by_da
        return list(safe_wids)

    @classmethod
    def _get_deposit_id_mapping(cls, wallet_ids: list[int]) -> dict[int, int]:
        model = Deposit
        rows = model.query.with_entities(
            model.id,
            model.wallet_deposit_id,
        ).filter(
            model.wallet_deposit_id.in_(wallet_ids)
        ).all()
        return dict(rows)

    @classmethod
    def _get_ret_by_risk_assessments(cls, deposit_ids: set[int], did_to_wid) -> (set[int], set[int]):
        model = DepositSenderRiskAssessment
        rows = model.query.with_entities(
            model.deposit_id,
            model.status,
        ).filter(
            model.deposit_id.in_(deposit_ids)
        ).all()
        dids_by_ass = {x.deposit_id for x in rows}
        wids_by_ass = {did_to_wid[did] for did in dids_by_ass}
        pending_dids = {row.deposit_id for row in rows if row.status == DepositSenderRiskAssessment.Status.PENDING}
        pending_wids = {did_to_wid[did] for did in pending_dids}
        return wids_by_ass, pending_wids

    @classmethod
    def _get_pending_deposit_audits(cls, deposit_ids: set[int]) -> set[int]:
        model = DepositAudit
        rows = model.query.with_entities(
            model.deposit_id,
        ).filter(
            model.status != model.Status.AUDITED,
            model.deposit_id.in_(deposit_ids),
            model.type.in_(
                model.KYT_TYPES
            )
        ).all()
        return {row.deposit_id for row in rows}


@ns.route("/abnormal-deposits/status")
@respond_with_code
class AbnormalDepositsResources(Resource):
    ADMIN_STATUS_DICT = AbnormalDepositBusiness.ADMIN_STATUS_DICT

    @classmethod
    @ns.use_kwargs(dict(
        data=fields.List(fields.Dict, required=True),
    ))
    def post(cls, **kwargs):
        """通过钱包 (chain, tx_id) ，获取web自助找回记录状态"""
        data = kwargs['data']
        model = AbnormalDepositApplication
        where = []
        for item in data:
            where.append(and_(model.chain == item['chain'].upper(), model.tx_id == item['tx_id']))
        if not where:
            return dict(
                items={},
                status=cls.ADMIN_STATUS_DICT,
                type_dict={i.name: i.value for i in model.Type},
            )
        rows = model.query.with_entities(
            model.id,
            model.additional_info_status,
            model.status,
            model.chain,
            model.tx_id,
            model.type,
            model.total_usd,
            model.asset_rate,
            model.expect_fee_asset,
            model.expect_fee_amount,
            model.fee_asset,
            model.fee_amount,
        ).filter(or_(*where)).all()
        tx_apply_counts = cls._get_tx_apply_counts({row.tx_id for row in rows})
        row_mapping = {(row.chain, row.tx_id): row for row in rows}
        ret = {}
        for item in data:
            key = (item['chain'], item['tx_id'])
            row = row_mapping.get(key)
            status = None
            id_ = None
            type_ = None
            asset_rate = None
            fee_asset, fee_amount = None, None
            tx_apply_count = 0
            if row:
                id_ = row.id
                status = AbnormalDepositBusiness.get_admin_status(row)
                tx_apply_count = tx_apply_counts.get(row.tx_id, 0)
                type_ = row.type.name
                asset_rate = row.asset_rate
                fee_asset = row.fee_asset or row.expect_fee_asset
                fee_amount = row.fee_amount or row.expect_fee_amount
            ret[item['wallet_id']] = {
                'status': status,
                'id': id_,
                'tx_apply_count': tx_apply_count,
                'type': type_,
                'asset_rate': asset_rate,
                'fee_asset': fee_asset,
                'fee_amount': fee_amount,
            }
        return dict(
            items=ret,
            status=cls.ADMIN_STATUS_DICT,
            type_dict={i.name: i.value for i in model.Type},
        )

    @classmethod
    def _get_tx_apply_counts(cls, tx_ids: set) -> dict:
        model = AbnormalDepositApplication
        rows = model.query.with_entities(
            model.tx_id,
            func.count(model.id).label('count'),
        ).filter(
            model.tx_id.in_(tx_ids),
        ).group_by(
            model.tx_id
        ).all()
        return {row.tx_id: row.count for row in rows}
