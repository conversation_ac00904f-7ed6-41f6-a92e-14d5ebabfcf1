# -*- coding: utf-8 -*-

from webargs import fields as wa_fields

from ..common import (
    Resource, Namespace, respond_with_code,
)
from ...models import SubAccount

ns = Namespace('SubAccount')

url_prefix = '/sub_account'


@ns.route('/auth')
@respond_with_code
class SubAccountAuthResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            sub_users=wa_fields.List(wa_fields.Integer(required=True),
                                     default=[], missing=[])
        )
    )
    def post(cls, **kwargs):
        user_id = kwargs['user_id']
        q = SubAccount.query.filter(
            SubAccount.main_user_id == user_id,
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
            SubAccount.status.in_(
                [
                    SubAccount.Status.VALID,
                    SubAccount.Status.FROZEN,
                ]
            )
        )
        all_sub_user_ids = [v.user_id for v in
                            q.with_entities(SubAccount.user_id)]
        if kwargs["sub_users"]:
            if set(all_sub_user_ids) & set(kwargs['sub_users']) == \
                    set(kwargs['sub_users']):
                return dict(data=kwargs['sub_users'])

        return all_sub_user_ids
