import json
import time
from collections import defaultdict
from decimal import Decimal
from typing import NamedTuple, Any, List, Dict, Type

from flask import current_app
from kafka.consumer.fetcher import ConsumerRecord

from app.business import PriceManager
from app.business.mission_center.message import MissionMessageBiz
from app.business.mission_center.mission import UserMissionBiz, MissionBiz
from app.caches.user import SubMainUserCache
from app.common.constants import ProducerTopics
from app.models import db
from app.models.kafka import ConsumerMaxOffset
from app.models.mission_center import MissionCondition
from app.schedules.mission.mission import mission_send_reward_task


class EventData(NamedTuple):
    topic: str
    offset: int
    user_id: int
    sub_user_id: int
    timestamp: int
    biz_type: str
    biz_id: int
    data: Any

    @classmethod
    def load_kafka_message(cls, msg, sub_mapper: dict) -> 'EventData':
        value = json.loads(msg.value)
        event_user_id = value['user_id']
        user_id = sub_mapper.get(event_user_id, event_user_id)
        return cls(
            topic=msg.topic,
            user_id=user_id,
            data=value['event_data'],
            offset=msg.offset,
            sub_user_id=event_user_id if user_id != event_user_id else 0,
            timestamp=value['timestamp'],
            biz_type=value['biz_type'],
            biz_id=value['biz_id']
        )

    @property
    def unique_key(self) -> tuple:
        return self.topic, self.biz_id, self.biz_type, self.user_id

    def to_dict(self):
        base_data = self._asdict()
        return base_data


class TopicHandler:
    """事件处理基类"""
    KEY: MissionCondition
    BASE_ASSET = "USDT"

    def validate(self, events: list[EventData], monitor_config: dict) -> dict[int, list[EventData]]:
        """事件特殊校验过滤"""
        biz_events_mapper = defaultdict(list)
        for event in events:
            if event.user_id not in monitor_config:
                continue
            user_monitor_config = monitor_config[event.user_id]
            for um_id, time_dict in user_monitor_config.items():
                if event.timestamp < time_dict['used_at'] or event.timestamp > time_dict['expired_at']:
                    continue
                biz_events_mapper[um_id].append(event)
        return biz_events_mapper

    def aggregate(self, biz_events_mapper: dict[int, list[EventData]]) -> dict[int, dict[str, Decimal]]:
        raise NotImplementedError

    def process_batch(self, user_events: list[EventData], user_mission_config: dict):
        """批量处理事件"""
        # 事件过滤 & 分组事件
        biz_events_mapper = self.validate(user_events, user_mission_config)
        # 事件聚合
        user_mission_data = self.aggregate(biz_events_mapper)
        # 更新业务数据
        UserMissionBiz.update_user_mission(user_mission_data)


class TopicHandlerStrategy:
    """事件处理器策略基类"""

    def __init__(self):
        self._handlers: Dict[str, Type[TopicHandler()]] = {}

    def register(self, topic: str, handler: Type[TopicHandler]) -> None:
        """注册事件处理器"""
        if topic in self._handlers:
            raise KeyError(f"Duplicate handler for topic: {topic}")
        self._handlers[topic] = handler()

    def get_handler(self, topic: str) -> TopicHandler:
        """获取事件处理器实例"""
        handler_obj = self._handlers.get(topic)
        if not handler_obj:
            raise ValueError(f"No handler registered for topic: {topic}")

        return handler_obj

    def get_mission_condition_topics_mapper(self):
        mapper = defaultdict(list)
        for topic, handler in self._handlers.items():
            mapper[handler.KEY].append(topic)
        return mapper


topic_strategy = TopicHandlerStrategy()


def register_handle_topic(topics: list[str]):
    """事件处理器注册装饰器"""

    def decorator(cls: Type[TopicHandler]):
        for topic in topics:
            topic_strategy.register(topic, cls)
        return cls

    return decorator


class MissionTopicEventProcessor:
    """事件处理器，负责统一管理事件处理流程"""

    def __init__(self):
        self._max_offset = None
        self.monitor_config = None
        self.refresh_data()

    def refresh_data(self):
        """刷新数据"""
        current_app.logger.info('MissionEventConsumer refresh_data start')
        db.session.rollback()
        self.monitor_config = self._query_monitor_config()
        # 初始化用户任务 - 确保在同一个事务中处理
        new_user_missions = self._init_user_mission()
        if new_user_missions:
            db.session.commit()
            MissionMessageBiz.send_new_mission_notice(new_user_missions)
        current_app.logger.info('MissionEventConsumer refresh_data end')

    @classmethod
    def _query_sub_mapper(cls) -> dict[int, int]:
        return SubMainUserCache.get_sub_main_mapper()

    @classmethod
    def _query_monitor_config(cls) -> dict:
        mission_type_user_mapper = MissionBiz.get_monitor_config()
        mission_condition_topics_mapper = topic_strategy.get_mission_condition_topics_mapper()
        return {
            topic: user_data
            for mission_condition, user_data in mission_type_user_mapper.items()
            for topic in mission_condition_topics_mapper[mission_condition]
        }

    def _init_user_mission(self) -> list:
        """初始化用户任务"""
        monitor_user_mission_ids = {
            user_mission_id
            for user_data in self.monitor_config.values()
            for user_mission_data in user_data.values()
            for user_mission_id, data in user_mission_data.items()
            if data['is_new']
        }
        return UserMissionBiz.init_user_mission(monitor_user_mission_ids)

    @classmethod
    def _group_events_by_topic(cls, events: list[EventData]) -> dict[str, list[EventData]]:
        """按事件类型分组"""
        events_by_topic = defaultdict(list)
        for event in events:
            events_by_topic[event.topic].append(event)
        return events_by_topic

    def process_events(self, messages: List[ConsumerRecord]):
        """处理事件批次"""
        if not self.monitor_config:
            return
        sub_mapper = self._query_sub_mapper()
        events = [EventData.load_kafka_message(msg, sub_mapper) for msg in messages]
        events_by_topic = self._group_events_by_topic(events)

        for topic, topic_events in events_by_topic.items():
            try:
                handler = topic_strategy.get_handler(topic)
                handler.process_batch(topic_events, self.monitor_config.get(topic, {}))
            except ValueError:
                continue

        self._save_max_offset(messages)
        db.session.commit()
        mission_send_reward_task.delay()

    @classmethod
    def _save_max_offset(cls, messages: list[ConsumerRecord]):
        """保存消息最大偏移量"""
        mapper = defaultdict(list)
        for msg in messages:
            mapper[(msg.topic, msg.partition)].append(msg.offset)
        for (topic, partition), offsets in mapper.items():
            ConsumerMaxOffset.save_offset(topic, partition, max(offsets))

    def listen_for_update_mission(self):
        while True:
            time.sleep(10)
            try:
                self.refresh_data()
            except Exception as e:
                current_app.logger.error(f"Refresh data error: {e!r}")


class TradeDealsEventHandler(TopicHandler):

    def aggregate(self, biz_events_mapper: dict[int, list[EventData]]) -> dict[int, dict[str, Decimal]]:
        aggregate_data = defaultdict(lambda: {
            self.KEY.name: Decimal()
        })
        for biz_id, events in biz_events_mapper.items():
            for event in events:
                deal_data = event.data
                amount_rate = PriceManager.asset_to_asset(deal_data['amount_asset'], self.BASE_ASSET)
                aggregate_data[biz_id][self.KEY.name] += Decimal(deal_data['amount']) * amount_rate
        return aggregate_data


class CountEventHandler(TopicHandler):

    def aggregate(self, biz_events_mapper: dict[int, list[EventData]]):
        aggregate_data = defaultdict(lambda: {
            self.KEY.name: Decimal()
        })
        for biz_id, events in biz_events_mapper.items():
            for _ in events:
                aggregate_data[biz_id][self.KEY.name] += Decimal(1)
        return aggregate_data


@register_handle_topic([ProducerTopics.SPOT_DEALS.value, ProducerTopics.EXCHANGE_DEALS.value])
class SpotDealsEventHandler(TradeDealsEventHandler):
    """币币交易事件处理"""
    KEY = MissionCondition.SPOT_AMOUNT


@register_handle_topic([ProducerTopics.PERPETUAL_DEALS.value])
class PerpetualDealsEventHandler(TradeDealsEventHandler):
    """合约交易事件处理"""
    KEY = MissionCondition.PERPETUAL_AMOUNT


@register_handle_topic([ProducerTopics.DEPOSIT_REWARD.value])
class DepositEventHandler(TopicHandler):
    """充值事件处理"""
    KEY = MissionCondition.DEPOSIT_AMOUNT

    def aggregate(self, biz_events_mapper: dict[int, list[EventData]]) -> dict[int, dict[str, Decimal]]:
        aggregate_data = defaultdict(lambda: {self.KEY.name: Decimal()})
        for biz_id, events in biz_events_mapper.items():
            for event in events:
                deposit_data = event.data
                rate = PriceManager.asset_to_asset(deposit_data['amount_asset'], self.BASE_ASSET)
                aggregate_data[biz_id][self.KEY.name] += rate * Decimal(deposit_data['amount'])
        return aggregate_data


@register_handle_topic([ProducerTopics.COPY_TRADING.value])
class CopyTradingEventHandler(CountEventHandler):
    """完成跟单交易次数"""
    KEY = MissionCondition.COPY_TRADING_ONCE


@register_handle_topic([ProducerTopics.DEMO_TRADING.value])
class DemoTradingEventHandler(CountEventHandler):
    """完成合约模拟次数"""
    KEY = MissionCondition.DEMO_TRADING_ONCE


mission_topic_processor = MissionTopicEventProcessor()
