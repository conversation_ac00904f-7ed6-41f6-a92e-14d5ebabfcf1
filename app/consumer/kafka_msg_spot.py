# -*- coding: utf-8 -*-
import json
import time
from decimal import Decimal
from threading import Thread, Lock, Event
from typing import Dict, Any

from kafka import KafkaConsumer
from flask import current_app

from app import config
from app.business.order import Order
from app.business.push import send_spot_part_deal_notice_push, send_spot_total_deal_notice_push, \
    send_spot_stop_order_fail_notice_push, send_spot_limit_stop_order_notice_push, send_spot_market_order_notice_push
from app.business.pledge.helper import is_pledge_account
from app.caches import MarketCache
from app.common import StopOrderStatusIntType, OrderIntType, StopTopicEvent, OrderSideType
from app.utils import current_timestamp

SCOPE = 'job'
KEY = 'kafka'

order_map = dict()
exchange_sys_user_ids = set()
exchange_deal_order_ids = list()
lock = Lock()
event = Event()

"""
deals消息字段
{
    "timestamp": 1, 
    "id": 1,
    "market": "BTCUSDT",
    "stock": "BTC",
    "money": "USDT",
    "side": 1, // 1: ask, 2: bid
    "price": "50000",
    "amount": "100",
    "deal": "5000000",
    "ask_id": 12345,
    "ask_ctime": 1111.111,
    "ask_mtime": 1111.222,
    "ask_user_id": 1302,
    "ask_account": 0,
    "ask_type": 1, // 1: limit, 2: market 
    "ask_amount": "1",
    "ask_price": "1",
    "ask_left": "1",
    "ask_source": "api.v1",
    "ask_system": false,
    "ask_fee_asset": "BTC",
    "ask_fee": "0.1",
    "ask_client_id": "",
    "ask_deal_stock": "1111",
    "ask_deal_money": "1111",
    "bid_id": 12346,
    "bid_ctime": 1111.111,
    "bid_mtime": 1111.222,
    "bid_user_id": 1235,
    "bid_account": 0,
    "bid_type": 1, // 1: limit, 2: ask
    "bid_amount": "1",
    "bid_price": "1",
    "bid_left": "1",
    "bid_source": "api.v1",
    "bid_system": false,
    "bid_fee_asset": "USDT",
    "bid_fee": "100",
    "bid_client_id": ""
    "bid_deal_stock": "1111",
    "bid_deal_money": "1111",
}
"""


def build_order(msg: dict):
    ask_order = dict(
        id=msg['ask_id'],
        system=msg['ask_system'],
        source=msg['ask_source'],
        type=msg['ask_type'],
        account=msg['ask_account'],
        ctime=msg['ask_ctime'],
        mtime=msg['ask_mtime'],
        amount=msg['ask_amount'],
        left=msg['ask_left'],
        user=msg['ask_user_id'],
        market=msg['market'],
        deal_id=msg['id'],
        deal_stock=msg['ask_deal_stock'],
        deal_money=msg['ask_deal_money'],
        side=OrderSideType.SELL,
        stock=msg['stock'],
        money=msg['money']
    )
    bid_order = dict(
        id=msg['bid_id'],
        system=msg['bid_system'],
        source=msg['bid_source'],
        type=msg['bid_type'],
        account=msg['bid_account'],
        ctime=msg['bid_ctime'],
        mtime=msg['bid_mtime'],
        amount=msg['bid_amount'],
        left=msg['bid_left'],
        user=msg['bid_user_id'],
        market=msg['market'],
        deal_id=msg['id'],
        deal_stock=msg['bid_deal_stock'],
        deal_money=msg['bid_deal_money'],
        side=OrderSideType.BUY,
        stock=msg['stock'],
        money=msg['money']
    )
    return ask_order, bid_order


def handle_msg_worker():
    kafka_config = config['KAFKA_SPOT_CONFIG']
    consumer = KafkaConsumer(
        *kafka_config['KAFKA_TOPICS'],
        bootstrap_servers=kafka_config['KAFKA_SERVERS'],
        group_id=kafka_config['KAFKA_GROUP_ID'],
        max_poll_records=5000,
        max_partition_fetch_bytes=20 * 1024 * 1024,
    )
    try:
        for msg in consumer:
            if event.is_set():
                break
            # noinspection PyBroadException
            try:
                _handle_msg(json.loads(msg.value), msg.topic)
            except Exception:
                import traceback
                content = f'Error handling Kafka Message:\n' \
                          f'Msg: {msg!r} \n' \
                          f'Traceback: {traceback.format_exc()}'
                current_app.logger.error(content)
    finally:
        event.set()
        consumer.close()


def handle_order_cache_worker():
    global order_map
    time_limit = 10
    try:
        while True:
            if event.is_set():
                break

            time.sleep(1)
            delay_list = []
            with lock:
                for order_id in list(order_map.keys()):
                    order = order_map[order_id]
                    deal_amount = (Decimal(order['amount']) - Decimal(order['left']))
                    avg_amount = Order.get_avg_price((deal_amount, Decimal(order['deal_money']), order['market']))
                    if Decimal(order['left']) == 0:
                        delay_list.append(dict(task_name='total_deal_notice',
                                               params=(order['user'], order['market'],
                                                       order['side'].value, order['deal_stock'],
                                                       order['stock'], avg_amount, order['money'],
                                                       current_timestamp(to_int=True)
                                                       )))
                        del order_map[order_id]
                    else:
                        if order['mtime'] + time_limit > current_timestamp():
                            # 部分成交等一分钟后再推送
                            continue
                        delay_list.append(dict(task_name='part_deal_notice',
                                               params=(order['user'], order['market'],
                                                       order['side'].value, order['deal_stock'],
                                                       order['stock'], avg_amount, order['money'],
                                                       current_timestamp(to_int=True)
                                                       )))
                        del order_map[order_id]

            for task_data in delay_list:
                if task_data['task_name'] == 'total_deal_notice':
                    send_spot_total_deal_notice_push.delay(*task_data['params'])
                else:
                    send_spot_part_deal_notice_push.delay(*task_data['params'])
    finally:
        event.set()


def handle_exchange_order_worker():
    from app import create_app

    app = create_app()
    with app.app_context():
        # run_spot_kafka.py创建的context是主线程的，无法在其他线程使用
        _handle_exchange_order_worker()


def _handle_exchange_order_worker():
    from app.models import db
    from app.models.exchange import AssetExchangeSysUser, SysAssetExchangeOrder, SysAssetExchangeTradeOrderHistory
    from app.business.exchange import process_exchange_order_task

    def _update_sys_user_ids():
        sys_users = AssetExchangeSysUser.query.all()
        exchange_sys_user_ids.update([i.user_id for i in sys_users])

    def _process_by_order_id(_order_id):
        db.session.rollback()
        _order_history: SysAssetExchangeTradeOrderHistory = SysAssetExchangeTradeOrderHistory.query.filter(
            SysAssetExchangeTradeOrderHistory.order_id == _order_id,
        ).with_entities(
            SysAssetExchangeTradeOrderHistory.sys_exchange_order_id,
            SysAssetExchangeTradeOrderHistory.status,
        ).first()
        if _order_history and _order_history.status != SysAssetExchangeTradeOrderHistory.Status.FINISHED:
            # FINISHED状态说明已经处理了
            _sys_order: SysAssetExchangeOrder = SysAssetExchangeOrder.query.filter(
                SysAssetExchangeOrder.id == _order_history.sys_exchange_order_id,
            ).with_entities(
                SysAssetExchangeOrder.exchange_order_id,
                SysAssetExchangeOrder.status,
            ).first()
            if _sys_order and _sys_order.status != SysAssetExchangeOrder.Status.FINISHED:
                process_exchange_order_task.delay(_sys_order.exchange_order_id)
                current_app.logger.warning(f"kafka_consume_exchange_handler order_id: {_order_id} "
                                           f"sys_exchange_order_id: {_order_history.sys_exchange_order_id} "
                                           f"exchange_order_id: {_sys_order.exchange_order_id}")

    try:
        _update_sys_user_ids()
        while True:
            if event.is_set():
                break

            if exchange_deal_order_ids:
                while exchange_deal_order_ids:
                    order_id = exchange_deal_order_ids.pop()  # list.append\pop thread safety
                    _process_by_order_id(order_id)
            else:
                time.sleep(0.1)
    finally:
        event.set()


def run_kafka_consumer():
    t1 = Thread(target=handle_order_cache_worker)
    t1.start()
    exchange_thread = Thread(target=handle_exchange_order_worker)
    exchange_thread.start()
    handle_msg_worker()


def _handle_msg(msg: Dict[str, Any], topic):
    _handle_msg_for_push(msg, topic)
    _handle_msg_for_exchange(msg, topic)


def _handle_msg_for_push(msg: Dict[str, Any], topic):

    def can_push(_user_id: int, _system: bool, _source: str, _type: int, _account: int):
        if not _user_id:
            # _user_id=0为自成交
            return False
        if _system is True:
            return False
        # api下单不推送 api.v1 api.v2
        if 'api' in _source.lower():
            return False
        # 市价不推送
        if _type == OrderIntType.MARKET.value:
            return False
        if is_pledge_account(_account):
            return False
        return True

    match topic:
        case "deals":
            ask_order, bid_order = build_order(msg)
            for _order in [ask_order, bid_order]:
                if not can_push(_order['user'], _order['system'], _order['source'], _order['type'], _order['account']):
                    continue
                global order_map
                with lock:
                    order_map[_order['id']] = _order
            return
        case "stop":
            m = msg
            order = m['order']
            user_id = order['user']

            if not user_id:
                return
            if order['status'] == StopOrderStatusIntType.FAIL.value and msg['event'] == StopTopicEvent.STOP_EVENT_ACTIVE.value:
                # 计划委托失败
                send_spot_stop_order_fail_notice_push.delay(user_id, order['market'], order['side'],
                                                            current_timestamp(to_int=True))
                return
            market_data = MarketCache(order['market']).dict
            base_asset = market_data['base_asset']
            quote_asset = market_data['quote_asset']
            if order['status'] == StopOrderStatusIntType.ACTIVE.value and msg['event'] == StopTopicEvent.STOP_EVENT_ACTIVE.value:
                # 计划委托触发
                if order['type'] == OrderIntType.LIMIT.value:
                    send_spot_limit_stop_order_notice_push.delay(
                        user_id, order['market'], order['side'], order['amount'], base_asset, order['price'], quote_asset,
                        current_timestamp(to_int=True)
                    )
                else:
                    send_spot_market_order_notice_push.delay(
                        user_id, order['market'], order['side'], order['amount'], base_asset, quote_asset,
                        current_timestamp(to_int=True))
                    return
        case _:
            return


def _handle_msg_for_exchange(msg: Dict[str, Any], topic):
    if topic == "deals":
        ask_order, bid_order = build_order(msg)
        for order in [ask_order, bid_order]:
            user_id = order['user']
            if user_id not in exchange_sys_user_ids:
                return

            if order['type'] == OrderIntType.MARKET.value:
                # 市价单会立刻执行完 忽略，只处理限价单
                return
            if Decimal(order['left']) == 0:
                # 全部成交（忽略立刻全部成交的，只处理挂了一定时间的订单）
                delta_s = order['mtime'] - order['ctime']
                if delta_s >= 0.1:
                    exchange_deal_order_ids.append(order['id'])


__all__ = ['run_kafka_consumer']
