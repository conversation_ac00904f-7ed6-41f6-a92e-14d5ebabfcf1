from flask import current_app

from app import config
from app.consumer.server.base import ConsumerManager
from app.consumer.server.handler import PerpetualDealsHandler
from app.consumer.server.processors import PerpetualDealsCashbackProcessor, PerpetualDealsMissionProcessor


def run_server_perpetual_consumer():
    per_kafka_config = config['KAFKA_PERPETUAL_CONFIG']
    cfg = {
        "bootstrap_servers": per_kafka_config['KAFKA_SERVERS'],
        "group_id": "unified_deals_consumer",
        "enable_auto_commit": False,
        "max_poll_records": 5000,
        "max_partition_fetch_bytes": 20 * 1024 * 1024,
    }

    perpetual_mission_processor = PerpetualDealsMissionProcessor()

    perpetual_cashback_processor = PerpetualDealsCashbackProcessor()

    perpetual_cashback_processor.start_loop()

    perpetual_handler = PerpetualDealsHandler()

    perpetual_handler.register_processor(perpetual_mission_processor)

    perpetual_handler.register_processor(perpetual_cashback_processor)

    manager = ConsumerManager(cfg)

    manager.register_handler(perpetual_handler)

    try:
        manager.start()
    except KeyboardInterrupt:
        current_app.logger.info(
            "Received shutdown signal, closing consumer...")
    finally:
        manager.close()
