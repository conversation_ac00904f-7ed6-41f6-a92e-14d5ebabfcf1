# app/consumer/base.py
import time
from abc import abstractmethod
from decimal import Decimal
from typing import List, NamedTuple, Optional, Dict, Protocol

from flask import current_app
from kafka import KafkaConsumer, TopicPartition, OffsetAndMetadata
from kafka.consumer.fetcher import ConsumerRecord

from app.models import db


class DealOrder(NamedTuple):
    time: int
    market: str
    user_id: int
    amount: Decimal
    amount_asset: str

    def to_dict(self):
        return self._asdict()


class BusinessProcessor(Protocol):
    """业务处理器协议"""

    def process_messages(self, msg: List[ConsumerRecord]) -> None:
        """处理单条消息"""
        pass

    @classmethod
    def is_first_run(cls, topics):
        raise NotImplementedError


class TopicHandler:
    """Topic处理器基类"""

    def __init__(self):
        self.processors: List[BusinessProcessor] = []

    @property
    @abstractmethod
    def topic(self) -> str:
        """返回处理的topic"""
        pass

    def register_processor(self, processor: BusinessProcessor) -> None:
        """注册业务处理器"""
        self.processors.append(processor)

    def handle(self, msgs: List[ConsumerRecord]) -> None:
        """处理消息"""
        for processor in self.processors:
            try:
                current_app.logger.debug(f"分发 消息 到 {processor}")
                processor.process_messages(msgs)
            except Exception as e:
                current_app.logger.exception(
                    f"Processor error for topic {self.topic}: {e}"
                )
                continue

    def is_first_run(self, topics: List[str]):
        return all([p.is_first_run(topics) for p in self.processors])


class ConsumerManager:
    """消费者管理器"""

    def __init__(self, consumer_config: dict):
        self.consumer_config = consumer_config
        self._consumer: Optional[KafkaConsumer] = None
        self.topic_handlers: Dict[str, TopicHandler] = {}

    def register_handler(self, handler: TopicHandler) -> None:
        """注册Topic处理器"""
        self.topic_handlers[handler.topic] = handler

    @property
    def consumer(self) -> KafkaConsumer:
        """获取或创建KafkaConsumer实例"""
        if self._consumer is None:
            topics = list(self.topic_handlers.keys())
            self._consumer = KafkaConsumer(
                *topics,
                **self.consumer_config
            )
        return self._consumer

    def close(self) -> None:
        """关闭消费者"""
        if self._consumer is not None:
            try:
                self._consumer.close()
            except Exception as e:
                current_app.logger.error(f"Error closing consumer: {e}")
            finally:
                self._consumer = None
                db.session.rollback()

    def start(self) -> None:
        """启动消费者"""
        topics = list(self.topic_handlers.keys())
        is_first_run_flags = [
            handler.is_first_run(topics)
            for topic, handler in self.topic_handlers.items()
        ]
        if all(is_first_run_flags) and self.consumer.poll(max_records=1):
            # 首次执行时：offset从最新的消息开始消费
            self.consumer.seek_to_end()
            current_app.logger.info('Unified deals consumer seek_to_end')

        while True:
            try:
                records = self.consumer.poll(timeout_ms=1000)
                for topic_partition, msgs in records.items():
                    if not msgs:
                        continue
                    # 获取对应的handler处理消息
                    topic_handler = self.topic_handlers.get(topic_partition.topic, None)
                    if topic_handler:
                        topic_handler.handle(msgs)

                    # 提交offset
                    self._commit_offsets(msgs)

                time.sleep(1)

            except Exception as e:
                current_app.logger.error(
                    f"Consumer error: {e}. Retrying after 60s...")
                db.session.rollback()
                self.close()
                time.sleep(60)

    def _commit_offsets(self, msgs: List[ConsumerRecord]) -> None:
        """提交offset"""
        offsets = {}
        for msg in msgs:
            offsets[TopicPartition(msg.topic, msg.partition)] = OffsetAndMetadata(
                msg.offset + 1, None, -1
            )
        self.consumer.commit(offsets)
