# -*- coding: utf-8 -*-
import json
import time
from decimal import Decimal
from enum import IntEnum
from threading import Thread, Lock, Event
from typing import Dict, Any

from kafka import KafkaConsumer
from flask import current_app

from app import config
from app import create_app
from app.api.common.fields import PerpetualAveragePriceField
from app.business.push import send_perpetual_position_reduce_push, send_perpetual_total_deal_notice_push, \
    send_perpetual_part_deal_notice_push, send_perpetual_stop_order_fail_notice_push, \
    send_perpetual_limit_stop_order_notice_push, send_perpetual_market_stop_order_push, send_perpetual_position_close_notice_push
from app.business.perpetual import perpetual_liquidation_notice, \
    perpetual_adl_notice, perpetual_liquidation_warning, \
    perpetual_profit_loss_notice, perpetual_position_close_notice, perpetual_position_reduce_notice, \
    ClosePositionOperationType, PerpetualMarketCache, perpetual_open_pos_tp_sl_fail_notice
from app.common import StopOrderStatusIntType, OrderIntType, StopTopicEvent, OrderSideType
from app.utils import current_timestamp

SCOPE = 'job'
KEY = 'kafka'


"""
{
    "timestamp": 1649313562,
    "id": 12345,
    "market": "BTCUSDT",
    "stock": "BTC",
    "money": "USDT",
    "side": 1, // 1: ask, 2: bid
    "clearing_asset": "USDT",
    
    "ask_order_id": 111,
    "ask_user_id": 1302,
    "ask_deal_type": 1, // 1: 开仓, 2: 加仓, 3: 减仓, 4: 自定义平仓, 5: 强制减仓, 
    8: 减仓(系统强平), 9: 减仓(ADL), 10: 减仓(止盈), 11: 减仓(止损), 12: 平仓(系统强平), 
    13: 平仓(ADL), 14: 平仓(止盈), 15: 平仓(止损)
    "ask_fee": "4",
    "ask_fee_asset": "",
    "ask_asset_fee": "0",
    "ask_stock": "1",
    "ask_client_id": "",
    "ask_create_time": 1111.111,
    "ask_update_time": 1111.222,
    "ask_amount": "1",
    "ask_price": "1",
    "ask_left": "1",
    "ask_source": "api.v1",
    "ask_system": false,
    "ask_deal_stock": "1111",
    "ask_deal_fee":   "1111",

    "bid_order_id": 222,
    "bid_user_id": 1235,
    "bid_deal_type": 1, // 1: 开仓, 2: 加仓, 3: 减仓, 4: 自定义平仓, 5: 强制减仓, 
    8: 减仓(系统强平), 9: 减仓(ADL), 10: 减仓(止盈), 11: 减仓(止损), 
    12: 平仓(系统强平), 13: 平仓(ADL), 14: 平仓(止盈), 15: 平仓(止损)
    "bid_fee": "5",
    "bid_fee_asset": "",
    "bid_asset_fee": "0",
    "bid_stock": "1",
    "bid_client_id": "",
    "bid_create_time": 1111.111,
    "bid_update_time": 1111.222,
    "ask_amount": "1",
    "ask_price": "1",
    "ask_left": "1",
    "ask_source": "api.v1",
    "ask_system": false,
    "ask_deal_stock": "1111",
    "ask_deal_fee":   "1111",

    "price": "50000",
    "amount": "1"
}
"""


def build_order(msg: dict):
    ask_order = dict(
        id=msg['ask_order_id'],
        system=msg['ask_system'],
        source=msg['ask_source'],
        type=msg['ask_type'],
        create_time=msg['ask_create_time'],
        update_time=msg['ask_update_time'],
        amount=msg['ask_amount'],
        left=msg['ask_left'],
        user_id=msg['ask_user_id'],
        market=msg['market'],
        deal_id=msg['id'],
        deal_stock=msg['ask_deal_stock'],
        deal_type=msg["ask_deal_type"],
        side=OrderSideType.SELL,
        stock=msg['stock'],
        money=msg['money']
    )
    bid_order = dict(
        id=msg['bid_order_id'],
        system=msg['bid_system'],
        source=msg['bid_source'],
        type=msg['bid_type'],
        create_time=msg['bid_create_time'],
        update_time=msg['bid_update_time'],
        amount=msg['bid_amount'],
        left=msg['bid_left'],
        user_id=msg['bid_user_id'],
        market=msg['market'],
        deal_id=msg['id'],
        deal_stock=msg['bid_deal_stock'],
        deal_type=msg["bid_deal_type"],
        side=OrderSideType.BUY,
        stock=msg['stock'],
        money=msg['money']
    )
    return ask_order, bid_order


order_map = dict()
lock = Lock()
event = Event()
flask_app = create_app()


class PerpetualUserMsg(IntEnum):
    LIQUIDATION = 1
    ADL = 2
    LIQUIDATION_WARNING = 3
    STOP_LOSS = 4
    TAKE_PROFIT = 5
    MARKET_CLOSE = 6
    CLOSE_ALL = 7
    POSITION_REDUCE = 8
    OPEN_POSITION_STOP_LOSS_FAIL = 9
    OPEN_POSITION_TAKE_PROFIT_FAIL = 10


def check_cur_lag():
    from kafka import KafkaConsumer, TopicPartition

    TOPIC = 'perpetual_sys_deals'
    GROUP = 'perpetual_consumer'
    BOOTSTRAP_SERVERS = config['KAFKA_PERPETUAL_CONFIG']['KAFKA_SERVERS']

    consumer = KafkaConsumer(
        bootstrap_servers=BOOTSTRAP_SERVERS,
        group_id=GROUP,
        enable_auto_commit=False
    )

    for p in consumer.partitions_for_topic(TOPIC):
        tp = TopicPartition(TOPIC, p)
        consumer.assign([tp])
        committed = consumer.committed(tp)
        consumer.seek_to_end(tp)
        last_offset = consumer.position(tp)
        print("topic: %s partition: %s committed: %s last: %s lag: %s" % (
        TOPIC, p, committed, last_offset, (last_offset - committed)))

    consumer.close(autocommit=False)


def handle_msg_worker(topic):
    process_from_last = ['perpetual_sys_deals']
    consumer = KafkaConsumer(
        *[topic],
        bootstrap_servers=config['KAFKA_PERPETUAL_CONFIG']['KAFKA_SERVERS'],
        group_id=config['KAFKA_PERPETUAL_CONFIG']['KAFKA_GROUP_ID'],
        max_poll_records=5000,
        max_partition_fetch_bytes=20 * 1024 * 1024,
    )
    if topic in process_from_last and consumer.poll():
        # 重启后提交offset从最新的消息开始消费
        consumer.seek_to_end()
    with flask_app.app_context():
        current_app.logger.info(f'consumer_topic:{topic} start')
        try:
            for msg in consumer:
                if event.is_set():
                    break
                # noinspection PyBroadException
                try:
                    _handle_msg(json.loads(msg.value), msg.topic)
                except Exception:
                    import traceback
                    content = f'Error handling Kafka Message:\n' \
                              f'Msg: {msg!r} \n' \
                              f'Traceback: {traceback.format_exc()}'
                    current_app.logger.error(content)
        finally:
            event.set()
            consumer.close()


def handle_order_cache_worker():
    global order_map
    time_limit = 10
    with flask_app.app_context():
        try:
            while True:
                if event.is_set():
                    break
                time.sleep(0.1)
                delay_list = []
                with lock:
                    for order_id in list(order_map.keys()):
                        order = order_map[order_id]
                        avg_amount = PerpetualAveragePriceField().format(
                            [order['market'], order['amount'], order['left'], order['deal_stock']]
                        )
                        market_data = PerpetualMarketCache().get_market_info(order['market'])
                        stock = market_data['stock']
                        money = market_data['money']
                        deal_amount = Decimal(order['amount']) - Decimal(order['left'])
                        current_ts = current_timestamp()
                        if Decimal(order['left']) == 0:
                            if current_ts - order['update_time'] >= 5:
                                current_app.logger.info(
                                    f"total deal:{current_ts - order['update_time']}, user_id:{order['user_id']}, market:{order['market']}"
                                )
                            current_app.logger.info(
                                f"push total_deal order_id: {order_id}, user_id: {order['user_id']}, market:{order['market']}")
                            delay_list.append(dict(task_name='total_deal_notice',
                                                   params=(order['user_id'], order['market'],
                                                           order['side'], deal_amount,
                                                           stock, avg_amount, money, current_timestamp(to_int=True)
                                                           )))
                            del order_map[order_id]
                        else:
                            if order['update_time'] + time_limit > current_ts:
                                # 部分成交等一分钟后再推送
                                continue
                            if current_ts - (order['update_time']+ time_limit) >= 5:
                                current_app.logger.info(
                                    f"part deal:{current_ts - (order['update_time']+ time_limit)}, "
                                    f"user_id:{order['user_id']}, market:{order['market']}"
                                )
                            current_app.logger.info(
                                f"push part_deal order_id: {order_id}, user_id: {order['user_id']}, market:{order['market']}")
                            delay_list.append(dict(task_name='part_deal_notice',
                                                   params=(order['user_id'], order['market'],
                                                           order['side'], deal_amount,
                                                           stock, avg_amount, money, current_timestamp(to_int=True)
                                                           )))
                            del order_map[order_id]

                for task_data in delay_list:
                    if task_data['task_name'] == 'total_deal_notice':
                        send_perpetual_total_deal_notice_push.delay(*task_data['params'])
                    else:
                        send_perpetual_part_deal_notice_push.delay(*task_data['params'])
        finally:
            event.set()


def run_kafka_consumer():
    threads = [
        Thread(
            target=handle_msg_worker,
            args=(topic,)
        )
        for topic in config['KAFKA_PERPETUAL_CONFIG']['KAFKA_TOPICS']
    ]

    threads.append(Thread(target=handle_order_cache_worker))
    for thread in threads:
        thread.start()


def _handle_msg(msg: Dict[str, Any], topic):

    def can_push(_user_id: int,
                 _system: bool,
                 _source: str,
                 _type: int):
        if not _user_id:
            # _user_id=0为自成交
            return False
        if _system is True:
            return False
        # api下单不推送 api.v1 api.v2
        if 'api' in _source.lower():
            return False
        # 市价不推送
        if _type == OrderIntType.MARKET.value:
            return False
        return True

    if topic == 'perpetual_usermsg':
        m = msg.get('usermsg', dict())
        if not m:
            return

        if not m['user_id']:  # 系统账户
            return

        if msg['event'] == PerpetualUserMsg.LIQUIDATION.value:
            # 强平
            p = m['position']
            perpetual_liquidation_notice.delay(
                m['user_id'], m['market'], p['side'], m['leverage'], p['amount'],
                m['sign_price'], p['bkr_price'], p['liq_price']
            )
        elif msg['event'] == PerpetualUserMsg.ADL.value:
            # 自动减仓
            p = m['position']
            perpetual_adl_notice.delay(
                m['user_id'], m['market'], p['side'], m['amount'], m['price']
            )
        elif msg['event'] == PerpetualUserMsg.LIQUIDATION_WARNING.value:
            # 强平预警
            perpetual_liquidation_warning.delay(
                m['user_id'], m['market'], m['liq_risk']
            )
        elif msg['event'] == PerpetualUserMsg.STOP_LOSS.value:
            perpetual_profit_loss_notice.delay(
                m['user_id'], m['market'], ClosePositionOperationType.STOP_LOSS.name, m['position']['amount'], msg['success']
            )
        elif msg['event'] == PerpetualUserMsg.TAKE_PROFIT.value:
            perpetual_profit_loss_notice.delay(
                m['user_id'], m['market'], ClosePositionOperationType.TAKE_PROFIT.name, m['position']['amount'], msg['success']
            )
        elif msg['event'] == PerpetualUserMsg.MARKET_CLOSE.value:
            perpetual_position_close_notice.delay(
                m['user_id'], m['market'], ClosePositionOperationType.MARKET_CLOSE.name, m['position']['amount'], msg['success']
            )
        elif msg['event'] == PerpetualUserMsg.CLOSE_ALL.value:
            perpetual_position_close_notice.delay(
                m['user_id'], m['market'], ClosePositionOperationType.CLOSE_ALL.name, m['position']['amount'], msg['success']
            )
        elif msg['event'] == PerpetualUserMsg.POSITION_REDUCE.value:
            # 降档减仓
            perpetual_position_reduce_notice.delay(m['user_id'], m['market'], m['close_amount'], m['sign_price'])
            send_perpetual_position_reduce_push.delay([m['user_id']], m['market'], current_timestamp(to_int=True))
        elif msg['event'] == PerpetualUserMsg.OPEN_POSITION_STOP_LOSS_FAIL.value:
            # 合约开仓止盈止损失败
            perpetual_open_pos_tp_sl_fail_notice.delay(m['user_id'], m['market'], 'stop_loss')
        elif msg['event'] == PerpetualUserMsg.OPEN_POSITION_TAKE_PROFIT_FAIL.value:
            # 合约开仓止盈止损失败
            perpetual_open_pos_tp_sl_fail_notice.delay(m['user_id'], m['market'], 'take_profit')

    # 消息远不止这些，web只取需要的场景推送
    elif topic == 'perpetual_sys_deals':
        ask_order, bid_order = build_order(msg)
        for _order in [ask_order, bid_order]:
            if not can_push(_order['user_id'], _order['system'], _order['source'], _order['type']):
                continue
            global order_map
            with lock:
                order_map[_order['id']] = _order
        return
    elif topic == 'perpetual_sys_stops':
        m = msg['order']

        if not m['user_id']:
            return

        if m['status'] == StopOrderStatusIntType.FAIL.value and msg['event'] == StopTopicEvent.STOP_EVENT_ACTIVE.value:
            # 计划委托失败
            send_perpetual_stop_order_fail_notice_push.delay(m['user_id'], m['market'], m['side'],
                                                             current_timestamp(to_int=True))

        elif m['status'] == StopOrderStatusIntType.ACTIVE.value and msg['event'] == StopTopicEvent.STOP_EVENT_ACTIVE.value:
            # 计划委托触发
            market_data = PerpetualMarketCache().get_market_info(m['market'])
            base_asset = market_data['stock']
            quote_asset = market_data['money']
            if m['type'] == OrderIntType.LIMIT.value:
                send_perpetual_limit_stop_order_notice_push.delay(m['user_id'], m['market'], m['side'], m['amount'],
                                                                  base_asset, m['price'], quote_asset,
                                                                  current_timestamp(to_int=True))
            else:
                send_perpetual_market_stop_order_push.delay(m['user_id'], m['market'], m['side'], m['amount'],
                                                            base_asset, current_timestamp(to_int=True))

    elif topic == 'perpetual_his_positions':

        if not msg['user_id']:
            return
        # 限价平仓
        if msg['finish_type'] == 4:
            send_perpetual_position_close_notice_push.delay(msg['user_id'], msg['market'], msg['side'],
                                                            current_timestamp(to_int=True))
    else:
        current_app.logger.error(f'Invalid Kafka Message: {msg!r}')


__all__ = ['run_kafka_consumer']