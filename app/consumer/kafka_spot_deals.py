from flask import current_app

from app import config
from app.consumer.server.base import ConsumerManager
from app.consumer.server.handler import Spot<PERSON>ealsHandler
from app.consumer.server.processors import SpotDealsCashbackProcessor, SpotDealsMissionProcessor


def run_server_spot_consumer():
    """运行统一现货消费者"""
    kafka_config = config['KAFKA_SPOT_CONFIG']
    cfg = {
        "bootstrap_servers": kafka_config['KAFKA_SERVERS'],
        "group_id": "debug",
        "enable_auto_commit": False,
        "max_poll_records": 5000,
        "max_partition_fetch_bytes": 20 * 1024 * 1024,
    }

    # 创建业务处理器
    spot_mission_processor = SpotDealsMissionProcessor()

    spot_deals_cashback_processor = SpotDealsCashbackProcessor()

    spot_deals_cashback_processor.start_loop()

    # 创建Topic处理器
    spot_handler = SpotDealsHandler()

    # 注册业务处理器到Topic处理器
    spot_handler.register_processor(spot_mission_processor)
    spot_handler.register_processor(spot_deals_cashback_processor)

    # 创建消费者管理器
    manager = ConsumerManager(cfg)

    # 注册Topic处理器到管理器
    manager.register_handler(spot_handler)

    try:
        manager.start()
    except KeyboardInterrupt:
        current_app.logger.info(
            "Received shutdown signal, closing consumer...")
    finally:
        manager.close()
