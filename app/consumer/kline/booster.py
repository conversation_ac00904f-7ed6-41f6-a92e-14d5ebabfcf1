import random
import statistics
import time
from collections import defaultdict, deque
from decimal import Decimal
from typing import NamedTuple, Callable, Literal

from gevent import spawn_later
from gevent.pool import Pool
from gevent.lock import Semaphore
from flask import current_app

from app.common import OrderSideType, PerpetualMarketType
from app.business import PriceManager
from app.assets import AssetConfig
from app.caches import AmmMarketCache
from app.models import Market, PerpetualMarket, AssetConfig as AssetConfigModel, db
from app.models.system import KlineBoostSetting
from app.utils import quantize_amount, spawn_greenlet, copy_current_app_context, datetime_to_time


class BoostMarket(NamedTuple):
    name: str
    multiple: Decimal  # 刷量倍率
    amount_asset: str  # 订单/成交量币种
    amount_precision: int  # 数量精度
    price_precision: int  # 价格精度
    min_amount: Decimal  # 最小下单量
    price_size: Decimal | None  # 价格粒度，只有合约市场有


class SelfDeal(NamedTuple):
    market: str
    amount: Decimal
    price: Callable[[], tuple[int, Decimal]]
    after: float  # 表示在n秒后执行

    def __str__(self):
        return '(%s, %s, %s)' % (self.market, self.amount, self.after)

    def __repr__(self):
        return self.__str__()


def is_self_deal(deal):
    return deal['ask_user_id'] == 0 and deal['bid_user_id'] == 0


class Calculator:

    def __init__(self, sever_client_cls):
        self.client = sever_client_cls()
        self.deal_history = defaultdict(lambda: deque(maxlen=100))  # 最近n笔真实成交, {market: (amount, side)}
        self.last_deal_price = {}  # 最后成交价
        self._deal_benchmark = {} # 缓存单笔成交额的参考量及方向概率 {market: (amount, prob, time)}

    def new_deal(self, deal):
        market = deal['market']
        if not is_self_deal(deal):
            self.deal_history[market].append((Decimal(deal['amount']), deal['side']))
        self.last_deal_price[market] = Decimal(deal['price'])

    @classmethod
    def _calc_multiple(cls, biz: KlineBoostSetting.BusinessType):
        row = KlineBoostSetting.query.filter(KlineBoostSetting.business_type == biz).first()
        if not row:
            raise ValueError("kline booster config missing")
        start_day = datetime_to_time(row.start_day)
        start_multiple = row.start_multiple
        end_multiple = row.end_multiple
        increase_rate = row.increase_rate
        if increase_rate == 0:
            return start_multiple
        tod = int(time.time())
        tod -= tod % 86400
        v = start_multiple + (tod - start_day) // 86400 * increase_rate 
        v = v.quantize(Decimal('0.001'))
        if increase_rate > 0:
            v = min(v, end_multiple)
        elif increase_rate < 0:
            v = max(v, end_multiple)
        return v

    @classmethod
    def calc_multiple(cls, biz: Literal['spot', 'perpetual']):
        biz = KlineBoostSetting.BusinessType(biz)
        return cls._calc_multiple(biz) - 1 # 此处是计算要刷的量，需要减1

    @classmethod
    def calc_position_multiple(cls):
        return cls._calc_multiple(KlineBoostSetting.BusinessType.PERPETUAL_POSITION)

    def calc_deal_price(self, m: BoostMarket):
        """计算自成交价格和方向"""
        _, prob = self._calc_benchmark(m.name)
        if not prob:
            prob = 0.5
        prob = min(max(0.1, prob), 0.9) # 避免自成交全部是买单或卖单
        # 根据市场上过去的成交情况，确定成交方向，再根据成交方向确定价格浮动(买单上浮，卖单下浮)
        side = OrderSideType.BUY if random.random() < prob else OrderSideType.SELL
        direction = 1 if side == OrderSideType.BUY else -1
        if random.random() < 0.1: # 依然有10%的概率，买单价格下浮，卖单价格上浮
            direction *= -1
        price = self._calc_deal_price(m, direction)
        return (side, price)

    def _calc_deal_price(self, m: BoostMarket, direction):
        """计算自成交价格"""
        if not (price := self.last_deal_price.get(m.name)):
            price = self.client.market_last(m.name)
            if not price:
                raise ValueError('no valid price')
            self.last_deal_price[m.name] = price

        delta = price * random.randrange(0, 10) / 10000
        deal_price  = price + delta * direction
        deal_price = quantize_amount(deal_price, m.price_precision)
        if m.price_size:
            deal_price = (deal_price // m.price_size * m.price_size)
            deal_price = quantize_amount(deal_price, m.price_precision)
        return deal_price

    def calc_deal_count(self, m: BoostMarket, amount: Decimal):
        """计算要生成多少笔自成交"""
        bench, _ = self._calc_benchmark(m.name)
        # 取最小下单量的10倍，以及参考成交量的较大者
        bench = max(bench, m.min_amount * 10)
        if not bench:  # should not happen
            current_app.logger.warning('market %s has no benchamrk deal amount', m.name)
            return 3
        # 至少1笔，最多6笔
        count = int(amount / bench)
        count = max(count, 1)
        return min(6, count)

    def pick_min_amount(self, m: BoostMarket):
        return quantize_amount(m.min_amount + m.min_amount * random.randrange(10, 20) / 100, m.amount_precision)

    def _calc_benchmark(self, market: str):
        """计算单笔成交的参考量及成交方向(买)的概率"""
        # 因中位数计算复杂度不低，这里缓存一分钟
        now = int(time.time())
        v = self._deal_benchmark.get(market)
        if v and v[2] > now - 60:
            bench, prob = v[0], v[1]
        else:
            history = self.deal_history[market]
            if len(history) < 5:
                bench, prob = 0, 0
            else:
                bench = statistics.median(x[0] for x in history)
                prob = sum(x[0] for x in history if x[1] == OrderSideType.BUY) / sum(x[0] for x in history)
                self._deal_benchmark[market] = (bench, prob, now)
        return bench, prob
    
    def _random_split_one(self, value, n):
        """随机从value中划出1份，大小在平均值附近"""
        if n == 1:
            return value
        avg = value / n
        if isinstance(value, Decimal):
            vol = Decimal('1.5')
        else:
            vol = 1.5
        s = avg / vol
        e = avg * vol
        return s + random.randrange(0, 100) * (e - s) / 100

    def random_split_n(self, value, n):
        """把value随机分成n份，每份大小在平均值附近"""
        vs = []
        for _ in range(n):
            v = self._random_split_one(value, n)
            value -= v
            n -= 1
            vs.append(v)
        return vs

    def random_split_range(self, value, n):
        """在0到value中随机取n个值，任何相邻两个值的差在平均值附近"""
        vs = self.random_split_n(value, n + 1)
        rs = []
        s = 0
        for v in vs[:-1]:
            s += v
            rs.append(s)
        return rs


class Config:

    line_markets: dict[str, BoostMarket] = {}
    scale_markets: dict[str, BoostMarket] = {}
    prices: dict[str, Decimal] = {}
    order_limit: Decimal = 0  # 订单超过该市值不跟随刷量

    def update(self):
        raise NotImplementedError

    def listen_for_update(self):
        """监听并更新配置变动"""
        while True:
            time.sleep(300)
            try:
                self.update()
            except Exception as e:
                current_app.logger.error("selfdeal update market failed: %s", e)


class SpotConfig(Config):

    def update(self):
        db.session.rollback() # 常驻进程，需要结束事务，否则会因隔离级别一直查询到同样的数据
        rows = Market.query.filter(Market.status == Market.Status.ONLINE) \
                     .with_entities(Market.name, Market.base_asset,
                                    Market.base_asset_precision, Market.quote_asset_precision).all()
        amm_markets = set(AmmMarketCache.list_amm_markets())
        min_amounts = self._get_min_order_amount()

        multiple = Calculator.calc_multiple('spot')
        line_markets = []
        scale_markets = []
        for market, asset, amount_precision, price_precision in rows:
            m = BoostMarket(
                name=market, 
                multiple=multiple,
                amount_asset=asset,
                amount_precision=amount_precision,
                price_precision=price_precision,
                min_amount=min_amounts.get(asset, 0),
                price_size=None)
            # AMM市场只画线
            line_markets.append(m)
            if market not in amm_markets:
                scale_markets.append(m)

        self.line_markets = {x.name: x for x in line_markets}
        self.scale_markets = {x.name: x for x in scale_markets}
        self.prices = PriceManager.assets_to_usd()
        self.order_limit = Decimal(10000)
        current_app.logger.info("spot config updated")

    def _get_min_order_amount(self):
        result = {}
        rows = AssetConfigModel.query.filter(AssetConfigModel.key == AssetConfig.min_order_amount.name,
                                             AssetConfigModel.status == AssetConfigModel.Status.VALID).all()
        for row in rows:
            result[row.asset] = Decimal(row.value)
        return result


class PerpetualConfig(Config):

    def update(self):
        db.session.rollback()  # 常驻进程，需要结束事务，否则会因隔离级别一直查询到同样的数据
        rows = PerpetualMarket.query.filter(PerpetualMarket.status == PerpetualMarket.StatusType.OPEN) \
                              .with_entities(PerpetualMarket.name, PerpetualMarket.base_asset,
                                             PerpetualMarket.quote_asset, PerpetualMarket.market_type,
                                             PerpetualMarket.amount_precision, PerpetualMarket.quote_asset_precision,
                                             PerpetualMarket.min_order_amount, PerpetualMarket.price_size).all()
        multiple = Calculator.calc_multiple('perpetual')
        markets = [BoostMarket(
                name=x.name,
                multiple=multiple,
                amount_asset=x.base_asset if x.market_type == PerpetualMarketType.DIRECT else x.quote_asset,
                amount_precision=x.amount_precision,
                price_precision=x.quote_asset_precision,
                min_amount=x.min_order_amount,
                price_size=x.price_size
            ) for x in rows]
        self.line_markets = {x.name: x for x in markets}
        self.scale_markets = {x.name: x for x in markets}
        self.prices = PriceManager.assets_to_usd()
        self.order_limit = Decimal(50000)
        current_app.logger.info("perpetual config updated")


class ScaleBooster:
    """放大实际成交量"""

    window = 10

    def __init__(self, config: Config, calculator: Calculator,  executor: 'Executor'):
        self.last_deals = {}  # 上一个窗口的真实成交量
        self.latest_orders = {}  # 最近一段时间的订单 {order_id: amount}
        self._pool = Pool(10) # 用于执行订单查询
        self.config = config
        self.executor = executor
        self.calculator = calculator

    def new_deal(self, deal):
        if is_self_deal(deal):
            return
        if deal['timestamp'] < time.time() - self.window * 2:
            return
        m = self.config.scale_markets.get(deal['market'])
        if not m:
            return
        self._pool.spawn(copy_current_app_context(self._add_deal), m, deal)

    def _add_deal(self, m: BoostMarket, deal):
        # 大额订单成交不跟随刷量
        if self._is_large_order(m, deal):
            return
        market = deal['market']
        if market in self.last_deals:
            self.last_deals[market] += Decimal(deal['amount'])
        else:
            self.last_deals[market] = Decimal(deal['amount'])

    def self_deal(self, last_deals):
        for market, amount in last_deals.items():
            m = self.config.scale_markets.get(market)
            if not m:
                continue
            self._self_deal(m, amount * m.multiple)
    
    def _self_deal(self, m: BoostMarket, amount: Decimal):
        # 先评估需要执行的自成交笔数，再划分每笔成交量，最后处理低于最小下单量的成交量
        count = self.calculator.calc_deal_count(m, amount)
        ys = self.calculator.random_split_n(amount, count)
        ys = [quantize_amount(v, m.amount_precision) for v in ys]
        ys = self._normalise_amounts(m, ys)
        xs = self.calculator.random_split_range(self.window, len(ys))
        xs = [round(v, 1) for v in xs]
        price_fn = lambda: self.calculator.calc_deal_price(m)
        self.executor.execute([
            SelfDeal(m.name, y, price_fn, x) for y, x in zip(ys, xs)
        ])
    
    def _normalise_amounts(self, m, amounts):
        """处理小于最小下单量的金额"""
        s = 0
        for a in amounts:
            if a < m.min_amount:
                s += a
        if not s:
            return amounts
        if s:
            if s < m.min_amount:
                s = self.calculator.pick_min_amount(m)
            rs = [a for a in amounts if a >= m.min_amount]
            rs.append(s)
            return rs

    def _is_large_order(self, m: BoostMarket, deal):
        """判断这笔成交是否属于大额订单"""
        try:
            ask_id = deal.get('ask_id') or deal.get('ask_order_id')
            bid_id = deal.get('bid_id') or deal.get('bid_order_id')
            if not ask_id or not bid_id:
                raise ValueError('cannot read order id field')
            ask_amount = self._get_order_amount(deal['market'], deal['ask_user_id'], ask_id)
            bid_amount = self._get_order_amount(deal['market'], deal['bid_user_id'], bid_id)
        except Exception as e:
            current_app.logger.warning("get order amount failed: %s", e)
            return False

        if m.amount_asset == 'USD':
            price = 1
        else:
            price = self.config.prices.get(m.amount_asset, 0)
        if max(ask_amount, bid_amount) * price > self.config.order_limit:
            return True
        return False

    def _cache_order(self, order_id, amount):
        if len(self.latest_orders) >= 5000: # 缓存最近访问的5000条订单记录
            self.latest_orders.pop(next(iter(self.latest_orders)))
        self.latest_orders[order_id] = amount

    def _get_order_amount(self, market, user_id, order_id):
        if amount := self.latest_orders.get(order_id):
            return amount
        client = self.calculator.client
        order = client.pending_order_detail(market, order_id)
        if not order:
            order = client.finished_order_detail(user_id, order_id)
        if not order:
            raise ValueError('order %s not found' % order_id)
        amount = Decimal(order['amount'])
        self._cache_order(order_id, amount)
        return amount

    def run_forerver(self):
        while True:
            time.sleep(self.window)
            current_app.logger.info('scale booster run')
            # 避免在执行自成交时，又有新的成交插进来，引起数据不一致
            last_deals = self.last_deals
            self.last_deals = {}
            try:
                self.self_deal(last_deals)
            except Exception as e:
                current_app.logger.error("scale booster failed: %s", e)


class LineBooster:
    """画线"""

    window = 60
    size = 40

    def __init__(self, config: Config, calculator: Calculator, executor: 'Executor'):
        self.last_deal_time = {}  # 最新成交时间
        self.config = config
        self.executor = executor
        self.calculator = calculator

    def new_deal(self, deal):
        self.last_deal_time[deal['market']] = deal['timestamp']

    def self_deal(self):
        now = time.time()
        for m in self.config.line_markets.values():
            t = self.last_deal_time.get(m.name)
            # 如果窗口内前40秒没有任何成交，则执行一笔自成交
            if not t or t < now - self.size:
                self._self_deal(m)

    def _self_deal(self, m: BoostMarket):
        amount = self.calculator.pick_min_amount(m)
        after = self.calculator.random_split_range(self.window - self.size, 1)[0]
        price_fn = lambda: self.calculator.calc_deal_price(m)
        deal = SelfDeal(m.name, amount, price_fn, after)
        self.executor.execute([deal])

    def run_forerver(self):
        while True:
            time.sleep(self.window)
            current_app.logger.info('line booster run')
            try:
                self.self_deal()
            except Exception as e:
                current_app.logger.error("line booster failed: %s", e)


class Booster:

    def __init__(self, config: Config, calculator: Calculator, executor: 'Executor'):
        self._calculator = calculator
        self.scale_booster = ScaleBooster(config, calculator, executor)
        self.line_booster = LineBooster(config, calculator, executor)

    def new_deal(self, data):
        self._calculator.new_deal(data)
        self.scale_booster.new_deal(data)
        self.line_booster.new_deal(data)

    def run_forever(self):
        current_app.logger.info("line booster start, market count %s", len(self.line_booster.config.line_markets))
        spawn_greenlet(self.line_booster.run_forerver)
        current_app.logger.info("scale booster start market count %s", len(self.scale_booster.config.scale_markets))
        self.scale_booster.run_forerver()


class Executor:

    def __init__(self, concurrent: int, server_client_cls):
        self.semaphore = Semaphore(concurrent)
        self.client = server_client_cls()

    def execute(self, deals: list[SelfDeal]):
        fn = copy_current_app_context(self._execute)
        for deal in deals:
            spawn_later(deal.after, fn, deal)

    def _execute(self, deal: SelfDeal):
        # 如果刷不过来，直接放弃
        if not self.semaphore.acquire(timeout=10):
            current_app.logger.warning("drop selfdeal, market %s", deal.market)
            return
        try:
            side, price = deal.price()
            typ = 'buy' if side == OrderSideType.BUY else 'sell'
            current_app.logger.info('place %s order at %s, %s, price %s', typ, deal.market, deal.amount, price)
            self.client.market_self_deal(deal.market, deal.amount, price, side)
        except Exception as e:
            current_app.logger.error("%s selfdeal failed: %s", deal.market, e)
        self.semaphore.release()


def new_booster(config, sever_client_cls) -> Booster:
    return Booster(config, Calculator(sever_client_cls), Executor(20, sever_client_cls))
