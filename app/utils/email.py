# -*- coding: utf-8 -*-
from typing import List, Union

import requests

from boto3 import client as boto_client
from botocore.client import Config

from ..config import config


class BaseEmail:
    MONITOR_KEY: str = None

    def __init__(self, default_sender: str):
        self.default_sender = default_sender

    def send(self,
             recipient: Union[List[str], str],
             subject: str,
             content: str,
             sender: str = None,
             cc_address: Union[List[str], str] = None,
             bcc_address: Union[List[str], str] = None) -> bool:
        raise NotImplementedError


class _AWSEmail(BaseEmail):

    MONITOR_KEY: str = 'send_aws_email'

    def __init__(self,
                 region_name: str,
                 access_key_id: str,
                 secret_access_key: str,
                 default_sender: str):
        self._client = boto_client(
            'ses',
            region_name=region_name,
            aws_access_key_id=access_key_id,
            aws_secret_access_key=secret_access_key,
            config=Config(signature_version='s3v4')
        )
        super(_AWSEmail, self).__init__(default_sender)

    def send(self,
             recipient: Union[List[str], str],
             subject: str,
             content: str,
             sender: str = None,
             cc_address: Union[List[str], str] = None,
             bcc_address: Union[List[str], str] = None) -> bool:
        recipients = recipient if isinstance(recipient, list) else [recipient]
        sender = sender or self.default_sender
        destination = {'ToAddresses': recipients}
        if cc_address is not None:
            destination['CcAddresses'] = cc_address if isinstance(cc_address, list) else [cc_address]
        if bcc_address is not None:
            destination['BccAddresses'] = bcc_address if isinstance(bcc_address, list) else [bcc_address]
        # noinspection PyBroadException
        try:
            self._client.send_email(
                Source=f'"CoinEx" <{sender}>',
                Destination=destination,
                Message=dict(
                    Subject=dict(
                        Data=subject,
                        Charset='utf-8'
                    ),
                    Body=dict(
                        Html=dict(
                            Data=content,
                            Charset='utf-8'
                        )
                    )
                )
            )
        except Exception:
            return False
        return True


class _MailgunEmail(BaseEmail):
    MONITOR_KEY: str = 'send_mailgun_email'

    def __init__(self,
                 api_key: str,
                 domain: str,
                 default_sender: str):
        self._api_key = api_key
        self._domain = domain
        super(_MailgunEmail, self).__init__(default_sender)

    def send(self,
             recipient: Union[List[str], str],
             subject: str,
             content: str,
             sender: str = None,
             cc_address: Union[List[str], str] = None,
             bcc_address: Union[List[str], str] = None) -> bool:
        recipients = recipient if isinstance(recipient, list) else [recipient]
        sender = sender or self.default_sender
        url = f"https://api.eu.mailgun.net/v3/{self._domain}/messages"
        auth = ("api", self._api_key)
        data = {
            "from": f"CoinEx <{sender}>",
            "to": recipients,
            "subject": subject,
            "html": content
        }
        if cc_address is not None:
            data["cc"] = cc_address if isinstance(cc_address, list) else [cc_address]
        if bcc_address is not None:
            data["bcc"] = bcc_address if isinstance(bcc_address, list) else [bcc_address]
        # noinspection PyBroadException
        try:
            res = requests.post(url, auth=auth, data=data)
            if res.status_code != 200:
                return False
        except Exception:
            return False
        return True


_aws_config = config['AWS_EMAIL']
AWSEmail = _AWSEmail(
    _aws_config['region_name'],
    _aws_config['access_key_id'],
    _aws_config['secret_access_key'],
    _aws_config['from_addr']
)

_aws_comment_config = config['AWS_COMMENT_EMAIL']
AWSCommentEmail = _AWSEmail(
    _aws_comment_config['region_name'],
    _aws_comment_config['access_key_id'],
    _aws_comment_config['secret_access_key'],
    _aws_comment_config['from_addr']
)

_mailgun_config = config['MAILGUN_EMAIL']
MailgunEmail = _MailgunEmail(
    _mailgun_config['api_key'],
    _mailgun_config['domain'],
    _mailgun_config['from_addr']
)
