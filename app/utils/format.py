import re
from decimal import Decimal, ROUND_HALF_UP

from flask import current_app
from app.utils import amount_to_str, AmountType


def format_percent(
        amount: AmountType,
        decimals: int = 2,
        rounding: str = ROUND_HALF_UP,
        with_symbol: bool = False
) -> str:
    if not amount:
        return '0%'

    num = amount_to_str(Decimal(amount) * 100, decimals, rounding)
    value = f'{num}%'
    if not with_symbol:
        return value
    if Decimal(amount) > Decimal():
        value = f'+{value}'
    return value


def safe_div(a, b):
    return a / b if b else 0


def safe_percent_format(a, b):
    return f'{safe_div(a, b) * 100:.2f}%'


def format_seo_url_keyword(key_word: str):
    """将空格和%替换为-"""
    pattern = r'[\%\ ]+'
    return re.sub(pattern, '-', key_word).strip('-')


def format_op_log_dict(ns_str: str, detail: dict, main_key: str = '') -> dict:
    """处理操作日志数据中不能序列化的类型"""
    new_detail = {}
    for key, value in detail.items():
        if isinstance(value, bytes):
            current_app.logger.warning(f'delete bytes type data from the op log, ns: {ns_str}, key: {main_key}{key}')
            continue
        if isinstance(value, dict):
            value = format_op_log_dict(ns_str, value, main_key=f'{key}.')
        new_detail[key] = value
    return new_detail
