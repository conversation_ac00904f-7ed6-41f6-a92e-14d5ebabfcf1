# -*- coding: utf-8 -*-

from functools import partial
from typing import Union
from pyotp import TOTP, random_base32
from ..config import config
from .date_ import current_timestamp


def new_totp_auth_key() -> str:
    return random_base32()


def verify_totp_code(auth_key: str, code: Union[str, int]) -> bool:
    if config['BYPASS_2FA']:
        return True

    verify = partial(TOTP(auth_key).verify, str(code))
    now = current_timestamp(to_int=True)
    return any(verify(now + delta) for delta in (-30, 0, 30))
