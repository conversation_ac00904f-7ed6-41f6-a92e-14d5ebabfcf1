import threading
import time
from collections import OrderedDict


class BucketTTLCache:
    """
    ttl = bucket_size * interval
    BucketTTLCache 保证缓存设置后ttl时间内不会被淘汰，不保证超过ttl时间后一定会被淘汰
    适用于大量缓存会在有限时间内被存入取出，但少部分缓存需要ttl来做保底淘汰的场景
    interval 单位是秒
    BucketTTLCache 是协程安全的
    """
    def __init__(self, bucket_size=60, interval=1 * 60):
        self.cache = OrderedDict()

        self.mutex = threading.RLock()

        self.bucket_size = bucket_size
        self.interval = interval

        self._get_bucket()

    def _get_bucket_key(self):
        ts = time.time()
        return int(ts - (ts % self.interval))

    def _get_bucket(self, _key=None):

        if _key is None:
            _key = self._get_bucket_key()

        bucket = self.cache.get(_key)
        if bucket is None:
            self.cache[_key] = bucket = {}
            self._check_bucket()

        return bucket

    def _check_bucket(self):
        if len(self.cache) > self.bucket_size:
            self.cache.popitem(last=False)

    def set(self, k, v):
        with self.mutex:
            bucket = self._get_bucket()
            bucket[k] = v

    def get(self, k):
        with self.mutex:
            for key in self.cache.keys().__reversed__():
                bucket = self._get_bucket(key)
                if k in bucket:
                    return bucket.pop(k)
