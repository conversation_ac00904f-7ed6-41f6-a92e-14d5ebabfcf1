# -*- coding: utf-8 -*-
import json
from datetime import datetime, date
from decimal import Decimal
from enum import Enum, EnumMeta
from typing import Any
from types import MappingProxyType
from bson import ObjectId, Decimal128

from flask.json.provider import DefaultJSONProvider
from flask_babel.speaklater import LazyString
from dateutil.tz import UTC
import marshmallow as ma
from marshmallow import fields as mm_fields


class JsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, set):
            return list(obj)
        if isinstance(obj, datetime):
            return int(obj.timestamp())
        if isinstance(obj, date):
            dt = datetime(obj.year, obj.month, obj.day, tzinfo=UTC)
            return int(dt.timestamp())
        if isinstance(obj, Decimal):
            return f'{obj.normalize():f}'
        if isinstance(obj, Decimal128):
            return f'{obj.to_decimal().normalize():f}'
        if isinstance(obj, MappingProxyType):
            return dict(obj)
        if isinstance(obj, Enum):
            return obj.value
        if isinstance(obj, EnumMeta):
            return {e.name: e.value for e in obj}
        if isinstance(obj, LazyString):
            return str(obj)
        if isinstance(obj, ObjectId):
            return str(obj)
        return DefaultJSONProvider.default(obj)


def key_convert(key):
    if isinstance(key, Enum):
        return key.value
    return key


def dict_convent(obj: Any):
    if isinstance(obj, list):
        return [dict_convent(i) for i in obj]
    if not isinstance(obj, dict):
        return obj
    return {key_convert(k): dict_convent(v) for k, v in obj.items()}


# copy from webargs v6.1.1, v7+ remove this func, but we used
def dict2schema(dct, *, schema_class=ma.Schema):
    """Generate a `marshmallow.Schema` class given a dictionary of
    `Fields <marshmallow.fields.Field>`.
    """
    if hasattr(schema_class, "from_dict"):  # marshmallow 3
        return schema_class.from_dict(dct)
    attrs = dct.copy()

    class Meta:
        strict = True

    attrs["Meta"] = Meta
    return type("", (schema_class,), attrs)


def mm_schema(dct):
    def _validate_field(_f):
        # same as app/api/common/base.py:105
        if isinstance(_f, mm_fields.Field):
            return _f
        if isinstance(_f, type) and issubclass(_f, mm_fields.Field):
            return _f()
        raise TypeError(f'invalid field: {_f}')

    fields = {key: _validate_field(field) for key, field in dct.items()}

    return dict2schema(fields)

