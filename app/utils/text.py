# -*- coding: utf-8 -*-

from json import dumps as json_dumps
from functools import partial
from math import inf
from collections.abc import Sized

import re
from typing import Optional

# Thanks to:
# https://stackoverflow.com/questions/1175208/elegant-python-function-to-convert-camelcase-to-snake-case#comment76292557_12867228
from app.utils.parser import JsonEncoder

_RE_CAMEL = re.compile(r'((?<=[a-z0-9])[A-Z]|(?!^)(?<!_)[A-Z](?=[a-z]))')
_RE_NON_ALPHA_NUM = re.compile(r'[^0-9A-Za-z]')
_RE_PASSWORD = re.compile(r'[0-9A-Za-z`~!@#$%^&*-_=+]{10,32}')
_RE_withdraw_password = re.compile(r'^\d{6}$')
_RE_trade_password = re.compile(r'^\d{6}$')
_RE_ANTI_PHISHING_CODE = re.compile(r'[0-9A-Za-z]{4,20}')

_PWD_SPECIAL_CHARS = frozenset('`~!@#$%^&*-_=+')


def my_json_dumps(data, *args, **kwargs):
    return json_dumps(data, cls=JsonEncoder, *args, **kwargs)


compact_json_dumps = partial(my_json_dumps, separators=(',', ':'))


def camel_to_underscore(text: str) -> str:
    return _RE_CAMEL.sub(r'_\1', text).lower()


def underscore_to_camel(text: str) -> str:
    return ''.join([v.capitalize() for v in text.split('_')])


def truncate_text(text: str, max_size: int, side: str = 'middle',
                  *, dots_size: int = 3) -> str:
    sides = {'left', 'right', 'middle'}
    if side not in sides:
        raise ValueError(
            f'invalid argument `side`: {side!r} (options are {sides!r})')

    size = len(text)
    if size <= max_size:
        return text

    dots_size = min(dots_size, max_size)
    rmn_size = max_size - dots_size
    dots = '.' * dots_size

    if side == 'left':
        return f'{dots}{text[size - rmn_size:]}'
    elif side == 'right':
        return f'{text[:rmn_size]}{dots}'
    left_rmn_size = (rmn_size + 1) // 2
    return f'{text[:left_rmn_size]}{dots}{text[left_rmn_size-rmn_size:]}'


def strip_non_alpha_num(text: str) -> str:
    return _RE_NON_ALPHA_NUM.sub('', text)


def remove_prefix(text: str, prefix: str) -> str:
    if text.startswith(prefix):
        text = text[len(prefix):]
    return text


def remove_suffix(text: str, suffix: str) -> str:
    if text.endswith(suffix):
        text = text[:len(text)-len(suffix)]
    return text


def longest_common_prefix(*texts: str) -> str:
    if not texts:
        raise ValueError(f'no texts are provided')
    prefix = []
    for x in zip(*texts):
        if len(set(x)) != 1:
            break
        prefix.append(x[0])
    return ''.join(prefix)


def hide_text(text: str, head_size: int = 0, tail_size: int = 0,
              hidden_size: int = None):
    if head_size < 0 or tail_size < 0:
        raise ValueError
    text_size = len(text)
    tail_size = min(tail_size, text_size - head_size)
    hidden_size = hidden_size or max(text_size - head_size - tail_size, 0)
    return ''.join([
        text[:head_size],
        '*' * hidden_size,
        text[text_size - tail_size:]
    ])


_HIDDEN_TEXT_DEFAULTS = {
    1: (1, 0, 0),
    2: (1, 0, 0),
    6: (1, 1, 0),
    10: (2, 2, 0),
    12: (3, 3, 0),
    16: (4, 4, 0),
    32: (5, 5, 6),
    inf: (8, 8, 12)
}


def hide_text_default(text: Optional[str]) -> str:
    if not text:
        return ''
    size = len(text)
    for s, (head, tail, hidden) in _HIDDEN_TEXT_DEFAULTS.items():
        if size <= s:
            return hide_text(text, head, tail, hidden)
    return text


def hide_email(email: Optional[str], hide_domain: bool = False) -> str:
    if not email:
        return ''
    left, right = email.split('@')
    if hide_domain:
        parts = right.split('.', 1)
        parts[0] = '*' * 3
        right = '.'.join(parts)
    return '@'.join([hide_text_default(left), right])


def hide_mobile(mobile: Optional[str]) -> str:
    if not mobile:
        return ''
    return hide_text_default(mobile)


def validate_password(password: str) -> bool:
    return bool(_RE_PASSWORD.fullmatch(password)) \
        and any(c.isupper() for c in password) \
        and any(c.isdigit() for c in password)


def validate_withdraw_password(password: str) -> bool:
    return bool(_RE_withdraw_password.fullmatch(password))

def validate_trade_password(password: str) -> bool:
    return bool(_RE_trade_password.fullmatch(password))

def validate_anti_phishing_code(anti_phishing_code: str) -> bool:
    return bool(_RE_ANTI_PHISHING_CODE.fullmatch(anti_phishing_code))


def cal_password_level(password: str) -> int:
    score = 0

    if (pwd_len := len(password)) < 6:
        score += 5
    elif pwd_len < 9:
        score += 10
    else:
        score += 25

    lower, upper, digit, special = 0, 0, 0, 0
    for c in password:
        if c.isdigit():
            digit += 1
        elif c.isalpha():
            if c.islower():
                lower += 1
            else:
                upper += 1
        elif c in _PWD_SPECIAL_CHARS:
            special += 1

    if lower or upper:
        score += 10
    if lower and upper:
        score += 10
    if digit:
        score += 10
    if digit >= 3:
        score += 10
    if special:
        score += 10
    if special >= 3:
        score += 15

    return score


def max_length_validator(size: int):
    def validate(s: Sized):
        return len(s) <= size

    return validate

