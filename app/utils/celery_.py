# -*- coding: utf-8 -*-

from functools import wraps
from types import FunctionType
from typing import Union, Callable
from celery.schedules import crontab
from ..utils import func_to_str
from celery.worker import request as celery_request
import billiard.einfo

import sys


def scheduled(schedule: Union[int, crontab], *, queue: str = None):
    def dec(func):
        nonlocal queue
        _task = celery_task(func, queue=queue)

        from ..config.celery import beat_schedule
        _task_name = func_to_str(func)
        beat_schedule[_task_name] = {
            'task': _task_name,
            'schedule': schedule
        }
        return _task

    return dec


def celery_task(func=None, *, queue: str = None):
    """
    Usage:
        @task
        def bla_bla_bla():
          pass

        @task(queue='some_queue')
        def bla_bla_bla():
          pass
    """
    def dec(_func):
        if type(_func) is not FunctionType:
            raise TypeError(f'only functions are supported, '
                            f'but {_func!r} is a {type(_func)}')

        nonlocal queue
        if queue:
            _route_function(_func, queue)

        from app import celery
        return celery.task(_func, typing=False)

    if func is not None:
        return dec(func)

    return dec


def dynamic_schedule(schedule: Union[int, crontab],
                     module_name: str,
                     func_name: str,
                     func: Callable,
                     args: Union[list, tuple] = (),
                     kwargs: dict = None,
                     queue: str = None):
    @wraps(func)
    def john_doe():
        func(*args, **(kwargs or {}))

    john_doe.__module__ = module_name
    john_doe.__name__ = func_name
    john_doe.__qualname__ = func_name
    wrapped = scheduled(schedule, queue=queue)(john_doe)
    vars(sys.modules[module_name])[func_name] = wrapped
    return wrapped


def _route_prefix(prefix: str, queue: str):
    from ..config.celery import router, celery_queues

    router.set_queue_for_prefix(prefix, queue)
    if queue not in celery_queues:
        celery_queues[queue] = dict(
            exchange=queue,
            exchange_type='direct',
            binding_key=queue
        )


def _route_function(func: FunctionType, queue: str):
    _route_prefix(func_to_str(func), queue)


def route_module_to_celery_queue(module_name: str, queue: str):
    _route_prefix(module_name, queue)


def route_function_to_celery_queue(queue: str):
    def dec(func: FunctionType):
        _route_function(func, queue)
        return func
    return dec


def patch_celery_event_on_success():
    source_create_request_cls = celery_request.create_request_cls

    def patch_create_request_cls(
        base, task, pool, hostname, eventer, ref=celery_request.ref, revoked_tasks=celery_request.revoked_tasks,
        task_ready=celery_request.task_ready, trace=None, app=celery_request.current_app
    ):

        """
        patch create_request_cls 方法以实现task完成时发出的 task-succeeded 类型 event 中有 task_name 字段供监控脚本使用
        在初始化 task 之前，会调用 create_request_cls 方法给所有 task 初始化一个 req 对象，所以需要在 task 实例化前patch
        venv/lib/python3.11/site-packages/celery/app/task.py:430
        """
        request = source_create_request_cls(base, task, pool, hostname, eventer, ref, revoked_tasks, task_ready, trace, app)
        acks_late = task.acks_late
        events = eventer and eventer.enabled

        def on_success(self, failed__retval__runtime, **kwargs):
                failed, retval, runtime = failed__retval__runtime
                if failed:
                    exc = retval.exception
                    if isinstance(exc, billiard.einfo.ExceptionWithTraceback):
                        exc = exc.exc
                    if isinstance(exc, (SystemExit, KeyboardInterrupt)):
                        raise exc
                    return self.on_failure(retval, return_ok=True)
                task_ready(self)

                if acks_late:
                    self.acknowledge()

                if events:
                    self.send_event(
                        'task-succeeded', result=retval, runtime=runtime, task_name=self.name,
                    )

        request.on_success = on_success

        return request

    celery_request.create_request_cls = patch_create_request_cls
