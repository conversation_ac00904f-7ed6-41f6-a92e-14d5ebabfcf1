# -*- coding: utf-8 -*-

import hmac
from hashlib import sha256
from json import loads as json_loads
from typing import <PERSON>ple, Dict, Union, NamedTuple

import requests
from geetest import GeetestLib
from flask import g, current_app
from ..config import config


class Geetest3:

    FN_CHALLENGE = GeetestLib.FN_CHALLENGE
    CHALLENGE_SIZE = 32

    class Response(NamedTuple):
        success: int
        gt: str
        challenge: str

        @property
        def dict(self):
            return self._asdict()

    def __init__(self):
        self._gt = GeetestLib(*self._get_channel())

    def __repr__(self):
        return f'<{type(self).__name__}>'

    @classmethod
    def _get_channel(cls) -> Tuple[str, str]:
        channels = config['GEETEST3']
        channel = channels.get(g.get('lang')) or channels['default']
        return channel['id'], channel['key']

    def generate(self, user_id: Union[int, str] = None
                 ) -> <PERSON><PERSON>[int, Response]:
        status = self._gt.pre_process(str(user_id))
        response = json_loads(self._gt.get_response_str())
        return status, self.Response(**response)

    def validate(self, args: Dict[str, str], user_id: int, status: int
                 ) -> bool:
        g_cha = args.get(self._gt.FN_CHALLENGE, '')
        g_val = args.get(self._gt.FN_VALIDATE, '')
        g_code = args.get(self._gt.FN_SECCODE, '')

        gt = self._gt
        return (gt.success_validate(g_cha, g_val, g_code, user_id)
                if status == 1
                else gt.failback_validate(g_cha, g_val, g_code))


class Geetest4:

    def __init__(self, captcha_id: str, captcha_key: str):
        self.captcha_id = captcha_id
        self.captcha_key = captcha_key
        self.api_server = 'http://gcaptcha4.geetest.com'

    def validate(self, lot_number: str, captcha_output: str, pass_token: str, gen_time: str):
        sign_token = hmac.new(self.captcha_key.encode(), lot_number.encode(), sha256).hexdigest()
        # 极验服务不可用时，返回成功，避免影响业务
        try:
            r = requests.post(self.api_server + '/validate', data={
                'captcha_id': self.captcha_id,
                'lot_number': lot_number,
                'captcha_output': captcha_output,
                'pass_token': pass_token,
                'gen_time': gen_time,
                'sign_token': sign_token
            }, timeout=12)
        except Exception as e:
            current_app.logger.warning('geetest4 validate failed: %s', e)
            return True

        if r.status_code != 200:
            current_app.logger.warning('geetest4 validate failed, status: %s', r.status_code)
            return True
        if r.json()['result'] == 'success':
            return True
        current_app.logger.warning('geetest4 validate failed, result: %s', r.text)
        return False
