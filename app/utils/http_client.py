# -*- coding: utf-8 -*-

from collections import defaultdict, ChainMap
from decimal import Decimal
from functools import wraps
from logging import Logger
from types import MappingProxyType
from typing import Dict, Tuple, Callable, Type, Union, Any

from requests.adapters import HTTPAdapter
from werkzeug.local import LocalStack
from flask import current_app
from urllib.parse import urlparse

from ..config import config
from .net import url_join
from .chicken_ribs import NamedObject

import requests
import time
from urllib3.connectionpool import ConnectionPool


_local_stacks: Dict[str, LocalStack] = defaultdict(LocalStack)


_EMPTY = NamedObject('Empty', is_null=True)


class Context:

    key: str = None

    def __init__(self, **items: Any):
        self._items: dict = items

    def __repr__(self):
        return f'{type(self).__name__}({self._items})'

    def __enter__(self):
        _local_stacks[self.key].push(self)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        _local_stacks[self.key].pop()

    def items(self) -> MappingProxyType:
        return MappingProxyType(self._items)


class Headers(Context):

    key = 'headers'


class Options(Context):

    key = 'options'


class _Queue(ConnectionPool.QueueCls):
    """
    AWS负载均衡会直接丢弃闲置350秒的连接，并且不发送任何报文通知，
    App不知道连接已经被关闭了，继续发送数据时，负载均衡会返回RST报文。
    此处替换urllib3的连接池实现，让App主动关闭闲置连接。
    """

    idle_timeout = 300

    def get(self, block=True, timeout=None):
        item = super().get(block=block, timeout=timeout)
        if item is not None:
            idle = time.time() - item.last_active
            if idle > self.idle_timeout:
                item.close() # 直接关闭即可，urllib3会检测连接可用性并重连。
        return item

    def put(self, item, block=True, timeout=None):
        if item is not None:
            item.last_active = time.time()
        return super().put(item, block=block, timeout=timeout)


ConnectionPool.QueueCls = _Queue


class BaseHTTPClient:

    EMPTY = _EMPTY
    Timeout = requests.Timeout
    Headers = Headers
    Options = Options
    session = requests.Session()

    class BadResponse(RuntimeError):

        def __init__(self, code: int, data: Any = None):
            super().__init__(code, data)

        def __str__(self):
            code = self.code
            data = self.data
            return f'{code}: {data!r}' if data else f'{code}'

        @property
        def code(self):
            return self.args[0]

        @property
        def data(self):
            return self.args[1]

    def __init__(self,
                 base_url: str,
                 *,
                 auth: Any = None,
                 headers: dict = None,
                 logger: Logger = None,
                 **options):
        self._base_url = base_url
        self._auth = auth
        self._headers = headers or {}
        self._logger = logger
        self._options = self._default_options(options)

    def __repr__(self):
        return f'{type(self).__name__}({self._base_url!r})'

    @property
    def base_url(self):
        return self._base_url

    def headers(self):
        headers = self._headers
        context = _local_stacks[Headers.key].top
        if context is not None:
            headers = ChainMap(context.items(), headers)
        return MappingProxyType(headers)

    def options(self):
        options = self._options
        context = _local_stacks[Options.key].top
        if context is not None:
            options = ChainMap(context.items(), options)
        return MappingProxyType(options)

    def _default_options(self, replaced: Dict[str, Any] = None):
        options = {
            'timeout': 60
        }

        if ((socket_proxy := config.get('SOCKET_PROXY'))
            and (urlparse(self._base_url).netloc.split(':')[0]
                 not in ('localhost', '127.0.0.1'))):
            if proxy := socket_proxy.get('SOCKS5'):
                protocol = 'socks5h'
            else:
                proxy = socket_proxy.get('HTTP')
                protocol = 'http'
            options['proxies'] = dict(
                http=f'{protocol}://{proxy[0]}:{proxy[1]}',
                https=f'{protocol}://{proxy[0]}:{proxy[1]}'
            )

        if replaced:
            options.update(replaced)

        return options

    def _join_url(self, sub_url: str, **params: Any) -> str:
        return url_join(self._base_url, sub_url,
                        **{k: v for k, v in params.items() if v is not _EMPTY})

    @staticmethod
    def retry(count: int, *,
              timeout: float = None,
              max_sleep: int = 10,
              exc_whitelist: Union[Type[Exception],
                                   Tuple[Type[Exception], ...],
                                   Callable] = None,
              fail_safe: Any = _EMPTY):
        if count < 1:
            raise ValueError(f'`count` must be >= 1')

        options = {}
        if timeout is not None:
            options['timeout'] = timeout

        def is_whitelist_exc(_e) -> bool:
            if isinstance(_e, requests.RequestException):
                return True
            if exc_whitelist is None:
                return False
            if (isinstance(exc_whitelist, type)
                    and issubclass(exc_whitelist, Exception)):
                return isinstance(_e, exc_whitelist)
            if isinstance(exc_whitelist, tuple):
                return isinstance(_e, exc_whitelist)
            if callable(exc_whitelist):
                return exc_whitelist(_e)
            return False

        def dec(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                errors = []
                for _i in range(count):
                    with Options(**options):
                        try:
                            return func(*args, **kwargs)
                        except Exception as e:
                            if not is_whitelist_exc(e):
                                raise
                            errors.append(e)
                    if _i < count - 1:
                        time.sleep(min(1 << _i, max_sleep))
                if fail_safe is not _EMPTY:
                    return fail_safe
                raise errors[-1]  # no way it can be empty
            return wrapper

        return dec

    @classmethod
    def _log_error_response(cls, response: requests.Response) -> bool:
        request = response.request
        url = request.url
        method = request.method
        current_app.logger.error(
            f'{url=}, {method=}'
            f'status_code={response.status_code}, '
            f'resp_content={response.text}, '
            f'elapsed_seconds={response.elapsed.total_seconds()}')
        return True


class RESTClient(BaseHTTPClient):

    def get(self, api: str, **params: Any):

        return self._handle_response(
            self.session.get(self._join_url(api, **params),
                         auth=self._auth,
                         headers=self.headers(),
                         **self.options()))

    def post(self, api: str, json: Any = None,
             data: bytes = None):
        return self._handle_response(
            self.session.post(self._join_url(api),
                          json=json,
                          data=data,
                          auth=self._auth,
                          headers=self.headers(),
                          **self.options()))

    def put(self, api: str, json: Any = None,
            data: bytes = None):
        return self._handle_response(
            self.session.put(self._join_url(api),
                         json=json,
                         data=data,
                         auth=self._auth,
                         headers=self.headers(),
                         **self.options()))

    def delete(self, api: str, **params: Any):
        return self._handle_response(
            self.session.delete(self._join_url(api, **params),
                            auth=self._auth,
                            headers=self.headers(),
                            **self.options()))

    def _handle_response(self, response: requests.Response):
        if response.status_code // 100 != 2:
            raise self.BadResponse(
                response.status_code, response.content.decode())
        return response.json(parse_float=Decimal)


class JsonRPC2Client(BaseHTTPClient):

    class RPCBadResponse(RuntimeError):

        def __init__(self, response: dict):
            if not isinstance(response, dict):
                response = {}
            self._code = response.get('code', -1)
            self._data = response.get('data') or {}
            self._message = response.get('message', '')

        @property
        def code(self) -> int:
            return self._code

        @property
        def data(self):
            return self._data

        @property
        def message(self) -> str:
            return self._message

        @property
        def dict(self):
            return dict(
                code=self._code,
                data=self._data,
                message=self._message
            )

    def post(self, method: str, *args: Any, **kwargs: Any) -> Any:
        if args and kwargs:
            raise ValueError(
                f'please provide either `args` or `kwargs` but not both')
        response = self.session.post(
            self._base_url,
            json=self._prepare_data(method, kwargs or args),
            auth=self._auth,
            headers=self.headers(),
            **self.options()
        )
        status_code = response.status_code
        if status_code // 100 != 2:
            self._log_error_response(response)
            raise self.BadResponse(status_code, response.content.decode())
        data = response.json(parse_float=Decimal)
        if data.get('code', 0) != 0 or data.get('error'):
            raise self.RPCBadResponse(data.get('error'))
        return data['result']

    # noinspection PyMethodMayBeStatic
    def _prepare_data(self,
                      method: str,
                      params: Union[Tuple[Any, ...], Dict[str, Any]]):
        data = dict(
            jsonrpc='2.0',
            method=method,
            params=params,
            id=int(time.time())
        )
        if (isinstance(params, tuple)
                and len(params) == 1
                and params[0] is _EMPTY):
            # in case the server explicitly disallows the `params` argument
            data.pop('params')
        return data


class APIClient:

    EMPTY = _EMPTY
    Timeout = requests.Timeout
    Headers = Headers
    Options = Options

    class BadResponse(RuntimeError):

        def __init__(self, code: int, data: Any = None):
            super().__init__(code, data)

        def __str__(self):
            code = self.code
            data = self.data
            return f'{code}: {data!r}' if data else f'{code}'

        @property
        def code(self):
            return self.args[0]

        @property
        def data(self):
            return self.args[1]

    def __init__(self,
                 base_url: str,
                 *,
                 auth: Any = None,
                 headers: dict = None,
                 logger: Logger = None,
                 pool_size: int = 22,
                 **options):
        self._base_url = base_url
        self._auth = auth
        self._headers = headers or {}
        self._logger = logger
        self._options = self._default_options(options)
        self.session = requests.Session()
        self._configure_session(pool_size)

    def _configure_session(self, pool_size):
        """
        配置 Session 的连接池大小。
        """
        # 增加连接池大小
        adapter = HTTPAdapter(pool_connections=pool_size, pool_maxsize=pool_size)
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

    def __repr__(self):
        return f'{type(self).__name__}({self._base_url!r})'

    @property
    def base_url(self):
        return self._base_url

    def headers(self):
        headers = self._headers
        context = _local_stacks[Headers.key].top
        if context is not None:
            headers = ChainMap(context.items(), headers)
        return MappingProxyType(headers)

    def options(self):
        options = self._options
        context = _local_stacks[Options.key].top
        if context is not None:
            options = ChainMap(context.items(), options)
        return MappingProxyType(options)

    def _default_options(self, replaced: Dict[str, Any] = None):
        options = {
            'timeout': 60
        }

        if ((socket_proxy := config.get('SOCKET_PROXY'))
            and (urlparse(self._base_url).netloc.split(':')[0]
                 not in ('localhost', '127.0.0.1'))):
            if proxy := socket_proxy.get('SOCKS5'):
                protocol = 'socks5h'
            else:
                proxy = socket_proxy.get('HTTP')
                protocol = 'http'
            options['proxies'] = dict(
                http=f'{protocol}://{proxy[0]}:{proxy[1]}',
                https=f'{protocol}://{proxy[0]}:{proxy[1]}'
            )

        if replaced:
            options.update(replaced)

        return options

    def _join_url(self, sub_url: str, **params: Any) -> str:
        return url_join(self._base_url, sub_url,
                        **{k: v for k, v in params.items() if v is not _EMPTY})

    @staticmethod
    def retry(count: int, *,
              timeout: float = None,
              max_sleep: int = 10,
              exc_whitelist: Union[Type[Exception],
                                   Tuple[Type[Exception], ...],
                                   Callable] = None,
              fail_safe: Any = _EMPTY):
        if count < 1:
            raise ValueError(f'`count` must be >= 1')

        options = {}
        if timeout is not None:
            options['timeout'] = timeout

        def is_whitelist_exc(_e) -> bool:
            if isinstance(_e, requests.RequestException):
                return True
            if exc_whitelist is None:
                return False
            if (isinstance(exc_whitelist, type)
                    and issubclass(exc_whitelist, Exception)):
                return isinstance(_e, exc_whitelist)
            if isinstance(exc_whitelist, tuple):
                return isinstance(_e, exc_whitelist)
            if callable(exc_whitelist):
                return exc_whitelist(_e)
            return False

        def dec(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                errors = []
                for _i in range(count):
                    with Options(**options):
                        try:
                            return func(*args, **kwargs)
                        except Exception as e:
                            if not is_whitelist_exc(e):
                                raise
                            errors.append(e)
                    if _i < count - 1:
                        time.sleep(min(1 << _i, max_sleep))
                if fail_safe is not _EMPTY:
                    return fail_safe
                raise errors[-1]  # no way it can be empty
            return wrapper

        return dec

    @classmethod
    def _log_error_response(cls, response: requests.Response) -> bool:
        request = response.request
        url = request.url
        method = request.method
        current_app.logger.error(
            f'{url=}, {method=}'
            f'status_code={response.status_code}, '
            f'resp_content={response.text}, '
            f'elapsed_seconds={response.elapsed.total_seconds()}')
        return True

    def _handle_response(self, response: requests.Response):
        if response.status_code // 100 != 2:
            raise self.BadResponse(
                response.status_code, response.content.decode())
        return response.json(parse_float=Decimal)

    def get(self, api: str, **params: Any):
        return self._handle_response(
            self.session.get(self._join_url(api, **params),
                         auth=self._auth,
                         headers=self.headers(),
                         **self.options()))
