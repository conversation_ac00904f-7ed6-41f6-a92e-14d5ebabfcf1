# -*- coding: utf-8 -*-

class BaseConverter:
    # copy from django.utils.baseconv
    decimal_digits = "0123456789"

    def __init__(self, digits, sign="-"):
        self.sign = sign
        self.digits = digits
        if sign in self.digits:
            raise ValueError("Sign character found in converter base digits.")

    def __repr__(self):
        return "<%s: base%s (%s)>" % (self.__class__.__name__, len(self.digits), self.digits)

    def encode(self, i: int) -> str:
        neg, value = self.convert(i, self.decimal_digits, self.digits, "-")
        if neg:
            return self.sign + value
        return value

    def decode(self, s: str) -> int:
        neg, value = self.convert(s, self.digits, self.decimal_digits, self.sign)
        if neg:
            value = "-" + value
        return int(value)

    @classmethod
    def convert(cls, number, from_digits, to_digits, sign):
        if str(number)[0] == sign:
            number = str(number)[1:]
            neg = 1
        else:
            neg = 0

        # make an integer out of the number
        x = 0
        for digit in str(number):
            x = x * len(from_digits) + from_digits.index(digit)

        # create the result in base 'len(to_digits)'
        if x == 0:
            res = to_digits[0]
        else:
            res = ""
            while x > 0:
                digit = x % len(to_digits)
                res = to_digits[digit] + res
                x = int(x // len(to_digits))
        return neg, res
