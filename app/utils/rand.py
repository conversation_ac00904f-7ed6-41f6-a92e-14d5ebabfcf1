# -*- coding: utf-8 -*-

from secrets import token_hex
from random import sample
from .date_ import today

_DIGITS = tuple(map(str, range(10)))
_REFERRAL_CODE_CHARS = tuple('23456789abcdefghgkmnpqrstuvwxyz')
_REFERRAL_CODE_SIZE = 5

_C_BOX_CODE_SIZE = 6
_C_BOX_CODE_CHARS = tuple('23456789ABCDEFGHGKMNPQRSTUVWXYZ')


def new_hex_token(length: int = 32) -> str:
    return token_hex(length // 2).upper()


def new_verification_code(length: int = 6) -> str:
    return ''.join(sample(_DIGITS, length))


def new_referral_code(length: int = _REFERRAL_CODE_SIZE) -> str:
    return ''.join(sample(_REFERRAL_CODE_CHARS, length))


def new_file_key(length: int = 32, suffix: str = ''):
    # 生成aws S3的file key，请使用AWSBucketPublic/AWSBucketPrivate/AWSBucketTmp.new_file_key 方法

    key = f'{today().strftime("%Y-%m-%d")}/{token_hex(length // 2).upper()}'
    if suffix:
        key = f'{key}.{suffix}'
    return key


def new_c_box_code(length: int = _C_BOX_CODE_SIZE) -> str:
    return ''.join(sample(_C_BOX_CODE_CHARS, length))
