# -*- coding: utf-8 -*-
import time
from datetime import datetime, date, tzinfo, timezone, timedelta
from typing import Union

from dateutil import tz
from dateutil.parser import parse as parse_datetime
from dateutil.tz import UTC, gettz
from dateutil.relativedelta import relativedelta


def now(tz: tzinfo = None) -> datetime:
    return datetime.now(tz or UTC)


def today_datetime(tz: tzinfo = None) -> datetime:
    now_ = now(tz)
    return datetime(now_.year, now_.month, now_.day, tzinfo=UTC)


def today(tz: tzinfo = None) -> date:
    return now(tz).date()


def min_utc_datetime() -> datetime:
    min_datetime = datetime.min
    min_utc_dt = min_datetime.replace(tzinfo=UTC)
    return min_utc_dt


def convert_datetime(dt: datetime, date_field: str) -> datetime:
    fields = ["year", "month", 'day', 'hour', 'minute', 'second', 'microsecond']
    defaults = [None, 1, 1, 0, 0, 0, 0]
    default_dict = dict(zip(fields, defaults))
    if date_field not in fields:
        raise TypeError
    ins_dict = {}
    index = fields.index(date_field)
    for field in fields[:index+1]:
        ins_dict[field] = getattr(dt, field)
    for field in fields[index+1:]:
        ins_dict[field] = default_dict[field]
    ins_dict['tzinfo'] = UTC
    return datetime(
       **ins_dict
    )


def current_datetime(s_field: str):
    return convert_datetime(now(), s_field)


def current_timestamp(*, to_int: bool = False) -> Union[float, int]:
    timestamp = datetime.now().timestamp()
    if to_int:
        timestamp = int(timestamp)
    return timestamp


def timezone_to_offset(tz: Union[str, tzinfo]) -> int:
    if isinstance(tz, str):
        tz = gettz(tz)
    return int(tz.utcoffset(datetime.today()).total_seconds())


def date_to_datetime(value: date) -> datetime:
    return datetime(value.year, value.month, value.day, tzinfo=UTC)


def timestamp_to_datetime(timestamp: float, tz: tzinfo = None) -> datetime:
    return datetime.fromtimestamp(timestamp, tz or UTC)


def timestamp_to_date(timestamp: float, tz: tzinfo = None) -> date:
    return timestamp_to_datetime(timestamp, tz).date()


def str_to_datetime(text: str, tz: tzinfo = None) -> datetime:
    dt = parse_datetime(text)
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=UTC)
    if tz is not None:
        dt = dt.astimezone(tz)
    return dt


def datetime_to_str(dt: datetime, offset_minutes: int = 0,
                    fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=UTC)
    dt = dt.astimezone(timezone(timedelta(minutes=offset_minutes)))
    return dt.strftime(fmt)


def timestamp_to_str(timestamp: float,  tz: tzinfo = None, fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
    dt = timestamp_to_datetime(timestamp, tz)
    return datetime_to_str(dt, fmt=fmt)


def op_timestamp_to_str(
        timestamp: float,
        tz: tzinfo = None,
        fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    datetime_to_str函数依赖offset_minutes, 而时间戳的换算是不需要关注这个值,
    并且datetime_to_str函数如果dt本身就包含时区会导致时区信息被覆盖
    如果需要转换成UTC+8，可使用dateutil.tz.gettz("UTC+8")
    """
    dt = timestamp_to_datetime(timestamp, tz)
    return dt.strftime(fmt)


def datetime_to_time(_datetime):
    return int(time.mktime(_datetime.timetuple()))


def today_timestamp_utc() -> int:
    _now = now()
    return int(datetime(_now.year, _now.month, _now.day, tzinfo=UTC
                        ).timestamp())


def next_month(year: int, month: int, day: int = 1) -> date:
    return date(year, month, day) + relativedelta(months=1)

def cur_month(year: int, month: int, day: int = 1) -> date:
    return date(year, month, day)

def last_month(year: int, month: int, day: int = 1) -> date:
    return date(year, month, day) + relativedelta(months=-1)


def last_month_range(dt: Union[datetime, date]) -> tuple[date, date]:
    last_month_first_day = last_month(dt.year, dt.month, 1)
    this_month_first_day = date(dt.year, dt.month, 1)
    last_month_end_day = this_month_first_day + relativedelta(days=-1)
    return last_month_first_day, last_month_end_day


def datetime_to_utc8_str(dt: datetime, fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=UTC)
    dt = dt.astimezone(tz.gettz("UTC+8"))
    return dt.strftime(fmt)


def this_month():
    today_ = today()
    return date(today_.year, today_.month, 1)


def yesterday():
    return today() - timedelta(days=1)


def datetime_to_ms(dt: datetime):
    return int(dt.timestamp() * 1000 // 1)


def date_to_ms(dt: date):
    c_dt = date_to_datetime(dt)
    return int(c_dt.timestamp() * 1000 // 1)


def utc_0_clock_to_beijing_0_clock(date_: datetime) -> datetime:
    return date_ - timedelta(hours=8)


def month_first_day(date_: datetime) -> datetime:
    return datetime(date_.year, date_.month, 1)


def dt_to_today(dt: datetime) -> datetime:
    return dt.replace(hour=0, minute=0, second=0, microsecond=0)


def dt_to_hour(dt: datetime) -> datetime:
    return dt.replace(minute=0, second=0, microsecond=0)


def last_quarter(year: int, month: int, day: int = 1) -> date:
    current_quarter = (month - 1) // 3 + 1
    if current_quarter == 1:
        last_quarter_year = year - 1
        last_quarter_month = 10  # 去年第四季度
    else:
        last_quarter_year = year
        last_quarter_month = 3 * (current_quarter - 2) + 1  # 上个季度的第一个月
    return date(last_quarter_year, last_quarter_month, day)


def cur_quarter(year: int, month: int, day: int = 1) -> date:
    quarter_month = ((month - 1) // 3) * 3 + 1
    return date(year, quarter_month, day)


def cur_week(date_: datetime.date) -> datetime.date:
    """获取给定日期所属星期的星期日（星期日为所属星期的第1天）
    
    Returns:
        该日期所属星期的星期日
    """
    # 获取当前日期是星期几（0-6，0代表星期一）
    weekday = date_.weekday()
    # 计算距离本周日的天数（如果当前是周一，需要往前推6天；如果是周二，需要往前推5天，以此类推）
    days_to_sunday = (weekday + 1) % 7
    # 返回本周日
    return date_ - timedelta(days=days_to_sunday)

