# -*- coding: utf-8 -*-

from __future__ import annotations
from contextvars import ContextVar
from functools import cached_property
from itertools import chain
from collections import ChainMap
from typing import NamedTuple, Dict, Tuple, Type, Union
from logging import getLogger

from pymysql import connect
from pymysql.connections import Connection
from pymysql.cursors import Cursor
from pymysql.err import OperationalError, InterfaceError
from werkzeug.local import Local
from gevent.lock import Semaphore

from ..config import config as app_config

import re
import socket
import socks


_logger = getLogger(__name__)


TABLE_NOT_EXISTS = 1146
_ERR_CODE_SERVER_GONE = 2006
_ERR_CODE_CONNECTION_LOST = 2013


_connection_lock = Semaphore()


class _MyConnection(Connection):

    def connect(self, sock=None):  # noqa: F811
        if sock is None and self.host not in ('localhost', '127.0.0.1'):
            sock = socks.socksocket()
            if socks5_config := _SOCKET_PROXY.get('SOCKS5'):
                rdns = socks5_config[0] not in ('localhost', '127.0.0.1')
                sock.set_proxy(socks.SOCKS5, *socks5_config, rdns=rdns)
            elif http_config := _SOCKET_PROXY.get('HTTP'):
                sock.set_proxy(socks.HTTP, *http_config)
            sock.settimeout(3)
            sock.connect((self.host, self.port))
            sock.settimeout(None)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
        return super().connect(sock)


if _SOCKET_PROXY := app_config.get('SOCKET_PROXY'):
    connect = _MyConnection     # noqa


class _Cursor(Cursor):
    """
    Closing an unbuffered cursor(which is default) is not necessary.
    All results will be read immediately after sql been executed.
    It returns results from memory when calling fetchall, fetchone...
    """

    def execute(self, query, args=None):
        with _connection_lock:
            try:
                return super().execute(query, args)
            except OperationalError as e:
                if e.args[0] not in (_ERR_CODE_CONNECTION_LOST,
                                     _ERR_CODE_SERVER_GONE):
                    raise
            except InterfaceError as e:  # connection closed by pymysql
                if e.args[0] != 0:
                    raise

            self.connection.ping()
            return super().execute(query, args)


class _BaseExternalDBMeta(type):

    def __new__(mcs, name, bases, dct):
        # noinspection PyTypeChecker
        cls: _BaseExternalDBMeta = super().__new__(mcs, name, bases, dct)
        if any(isinstance(b, mcs) for b in cls.__bases__):  # ignore the base
            cls.tables = ChainMap(
                vars(cls).get('tables', {}),
                *((tables for b in cls.__mro__[1:]
                   if (isinstance(b, mcs)
                       and (tables := getattr(b, 'tables', None))))))
        return cls

    def _connect(cls):
        if not (config_key := getattr(cls, 'config', None)):
            raise AttributeError(f'{cls.__name__}.config is not defined')
        if not (config := app_config.get(config_key)):
            raise ValueError(f'config {config_key!r} does not exist')
        cls._db = connect(**config)
        # set to autocommit mode. donot need to call commit after query.
        # it is useful when a transaction holds some locks but is not closed after query.
        cls._db.autocommit(True)

    @property
    def db(cls) -> Connection:
        if not hasattr(cls, '_db'):
            with _connection_lock:
                if not hasattr(cls, '_db'):
                    cls._connect()
        return cls._db


class ExternalTableConfig(NamedTuple):
    fields: Dict[str, str]
    extra_field_args: Tuple[str, ...] = ()
    engine: str = None
    default_charset: str = None


local_storage = ContextVar("ExternalTable.local.storage")


class ExternalTable:

    DEFAULT_BUFFER_SIZE = 1024
    _RE_PK = re.compile(r'\s*primary key\s*\(`?([^`]+)`?\),?\s*', flags=re.I)
    _RE_FIELD = re.compile(r'\w+')

    def __init__(self,
                 db: Type[ExternalDB],
                 name: str,
                 fields: Dict[str, str] = None,
                 extra_field_args: Tuple[str, ...] = (),
                 *,
                 engine: str = None,
                 default_charset: str = None,
                 buffer_size: int = None):
        self._db = db
        self._name = name
        self._fields = fields or {}
        self._extra_field_args = extra_field_args
        self._engine = engine or 'InnoDB'
        self._default_charset = default_charset or 'utf8mb4'

        self._auto_inc_pk = self._find_auto_inc_pk()
        self._buffer_size = buffer_size or self.DEFAULT_BUFFER_SIZE
        self._buffer = []

        self._locals = Local(local_storage)

    def __repr__(self):
        return f'{type(self).__name__}({self._name!r})'

    def _find_auto_inc_pk(self):
        auto_inc = set()
        for field, type_ in self._fields.items():
            type_lower = type_.lower()
            if 'auto_increment' in type_lower:
                auto_inc.add(field)
            if 'primary key' in type_lower:
                return field if field in auto_inc else None
        for arg in self._extra_field_args:
            if m := self._RE_PK.match(arg):
                return field if (field := m.group(1)) in auto_inc else None
        return None

    @property
    def name(self) -> str:
        return self._name

    @property
    def cursor(self):
        return self._locals.cursor

    @cached_property
    def _create_sql(self):
        return ''.join([
            f"CREATE TABLE IF NOT EXISTS `{self._name}` (\n",
            ',\n'.join(chain(
                (f"`{field}` {type_}"
                 for field, type_ in self._fields.items()),
                (f"{extra}" for extra in self._extra_field_args))
            ),
            '\n) '
            f'ENGINE={self._engine} DEFAULT CHARSET={self._default_charset}'
        ])

    @cached_property
    def _truncate_sql(self):
        return f"TRUNCATE TABLE `{self._name}`"

    @cached_property
    def _insert_sql(self):
        auto_inc_pk = self._auto_inc_pk
        fields = ', '.join(f"`{field}`" for field in self._fields
                           if field != auto_inc_pk)
        return f"INSERT INTO `{self._name}` ({fields}) VALUES {{values}}"

    @classmethod
    def _wrap_field(cls, field: str):
        return f'`{field}`' if cls._RE_FIELD.fullmatch(field) else field

    def execute(self, sql: str, args: Union[tuple, list, dict] = None):
        _logger.debug('executing: %r, args=%r', sql, args)
        self._locals.cursor = cursor = self._db.cursor()
        return cursor.execute(sql, args)

    def exists(self) -> bool:
        return bool(self.execute(f"show tables like '{self._name}'"))

    def create_if_not_exists(self, *, truncate: bool = False):
        self.execute(self._create_sql)
        if truncate:
            self.execute(self._truncate_sql)

    def drop(self):
        self.execute(f"DROP TABLE `{self._name}`")

    def truncate(self):
        self.execute(self._truncate_sql)

    def count(self, where: str = None) -> int:
        sql = [f"SELECT COUNT(*) FROM `{self._name}`"]
        if where:
            sql.append(f"WHERE {self._wrap_field(where)}")
        self.execute(' '.join(sql))
        return self.cursor.fetchone()[0] or 0

    def select(self, *fields: str,
               where: str = None, group_by: str = None, having: str = None,
               order_by: str = None, limit: Union[int, Tuple[int, int]] = None,
               force_index: str = None
               ) -> Tuple[tuple]:
        wrap_field = self._wrap_field
        fields = ', '.join(map(wrap_field, fields))
        sql = [f"SELECT {fields or '*'} FROM `{self._name}`"]
        if force_index:
            sql.append(f"FORCE INDEX(`{force_index}`)")
        if where:
            sql.append(f"WHERE {wrap_field(where)}")
        if group_by:
            sql.append(f"GROUP BY {wrap_field(group_by)}")
        if having:
            sql.append(f"HAVING {having}")
        if order_by:
            sql.append(f"ORDER BY {wrap_field(order_by)}")
        if limit:
            if isinstance(limit, tuple):
                sql.append(f"LIMIT {limit[0]}, {limit[1]}")
            else:
                sql.append(f"LIMIT {limit}")

        self.execute(' '.join(sql) + ';')
        result = self.cursor.fetchall()
        return result

    def insert(self, *values):
        expected_count = len(self._fields) - bool(self._auto_inc_pk)
        got_count = len(values)
        if got_count != expected_count:
            raise ValueError(
                f'expected {expected_count} values; got {got_count}')

        buffer = self._buffer
        buffer.append('(' + ', '.join(f"'{value}'" for value in values) + ')')
        if len(buffer) >= self._buffer_size:
            self.flush()

    def update(self, where: str, **items: str):
        item_str = ', '.join(f"`{k}` = '{v}'" for k, v in items.items())
        self.execute(
            f"UPDATE `{self._name}` SET {item_str} "
            f"WHERE {self._wrap_field(where)}")

    def delete(self, where: str = None):
        sql = [f"DELETE FROM `{self._name}`"]
        if where:
            sql.append(f"WHERE {self._wrap_field(where)}")
        self.execute(' '.join(sql))
        return self.cursor.fetchone() or 0

    def flush(self):
        buffer = self._buffer
        if not buffer:
            return
        self.execute(self._insert_sql.format(values=', '.join(buffer)))
        del buffer[:]


class ExternalDB(metaclass=_BaseExternalDBMeta):

    config: str
    tables: Dict[str, ExternalTableConfig]

    db: Connection  # from metaclass

    TableConfig = ExternalTableConfig
    Table = ExternalTable

    @classmethod
    def cursor(cls):
        return cls.db.cursor(_Cursor)

    @classmethod
    def ping(cls):
        cls.db.ping()

    @classmethod
    def table(cls,
              name: str,
              fields: Dict[str, str] = None,
              extra_field_args: Tuple[str, ...] = (),
              *,
              engine: str = None,
              default_charset: str = None) -> ExternalTable:
        return ExternalTable(cls, name, fields, extra_field_args,
                             engine=engine, default_charset=default_charset)

    @classmethod
    def table_from_config(cls, name: str, config_name: str = None):
        config_name = config_name or name
        conf = cls.tables.get(config_name)
        if conf is None:
            raise ValueError(f'table config {config_name!r} does not exist')
        return cls.table(name, conf.fields, conf.extra_field_args,
                         engine=conf.engine,
                         default_charset=conf.default_charset)
