# -*- coding: utf-8 -*-
from functools import wraps
from collections import deque
from enum import EnumMeta
from typing import List, Iterable, Iterator, Callable, Generator, Any, TypeVar, Dict, \
    Optional

from flask_sqlalchemy import BaseQuery
from gevent.pool import Pool
from gevent import spawn
from sqlalchemy import func

from .chicken_ribs import NamedObject
from .flask_ import copy_current_app_context, auto_close_db_session

_EMPTY = NamedObject('Empty')
T = TypeVar('T')


def batch_iter(iterable: Iterable[T], n: int
               ) -> Generator[List[T], None, None]:
    if n <= 0:
        raise ValueError('`n` must be greater than 0')

    batch = []
    for x in iterable:
        if len(batch) >= n:
            yield batch
            batch = []
        batch.append(x)
    if batch:
        yield batch


def exhaust(iterator: Iterator):
    # This is more space-efficient than `list(iterator)`
    deque(iterator, maxlen=0)


def g_map(func: Callable, *iterables: Iterable,
          size: int = None, ordered: bool = False, fail_safe: Any = _EMPTY
          ) -> list:
    if fail_safe is not _EMPTY:
        @wraps(func)
        def _func(*args, **kwargs):
            # noinspection PyBroadException
            try:
                return func(*args, **kwargs)
            except Exception:
                return fail_safe() if callable(fail_safe) else fail_safe
    else:
        _func = func

    _func = auto_close_db_session(_func)
    _func = copy_current_app_context(_func)

    pool = Pool(size)
    return list(
        (pool.imap if ordered else pool.imap_unordered)(_func, *iterables))


def spawn_greenlet(func, *args, **kwargs):
    func = auto_close_db_session(func)
    func = copy_current_app_context(func)
    return spawn(func, *args, **kwargs)


def list_enum_names(enum_class: EnumMeta) -> List[str]:
    return [e.name for e in enum_class]


def list_enum_values(enum_class: EnumMeta) -> List[str]:
    return [e.value for e in enum_class]


def group_by(func: Callable[[T], Any], iterable: Iterable[T]
             ) -> Dict[Any, List[T]]:
    result = {}
    for t in iterable:
        key = func(t)
        if not (rs := result.get(key)):
            rs = result[key] = []
        rs.append(t)
    return result


def first(func: Callable[[T], bool], iterable: Iterable[T]) -> Optional[T]:
    for t in iterable:
        if func(t):
            return t
    return None


def iter_by_id(query: BaseQuery, query_model, is_desc: bool = True,
               batch_size: int = 20000) -> Generator[List, None, None]:
    """通过id分段查询结果，仅适用于query中不包含order_by语句的查询"""
    start_end_rec = query.with_entities(
        func.max(query_model.id).label('max_id'),
        func.min(query_model.id).label('min_id')
    ).first()
    if not start_end_rec.max_id:
        yield []
    else:
        if is_desc:
            start = start_end_rec.max_id
            end = start_end_rec.min_id
        else:
            start = start_end_rec.min_id
            end = start_end_rec.max_id

        start_id = start + 1 if is_desc else start - 1
        while True:
            if start_id == end:
                break
            if is_desc:
                end_id = max(start_id - batch_size, end)
                items = query.filter(query_model.id < start_id,
                                     query_model.id >= end_id
                                     ).order_by(query_model.id.desc()).all()
            else:
                end_id = min(start_id + batch_size, end)
                items = query.filter(query_model.id > start_id,
                                     query_model.id <= end_id
                                     ).order_by(query_model.id.asc()).all()
            yield items
            start_id = end_id
