# -*- coding: utf-8 -*-
import os
import csv
import uuid
from dataclasses import dataclass
from typing import Iterable, List, Tuple, Dict, Union, Any

from flask import send_file, Response, g
from openpyxl import Workbook
from ..common import Language
from .date_ import now

import io
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE


def _get_field_value(item, field):
    if isinstance(item, dict):
        return item.get(field)
    return getattr(item, field)


@dataclass
class ExcelExporter:
    data_list: List[Dict[str, Any]] = None
    fields: List[str] = None,
    headers: List[str] = None,
    size: int = 1000000

    def _create_wb(self) -> Workbook:
        from app.utils import batch_iter

        data_list = self.data_list or []
        wb = Workbook(write_only=True)
        if not data_list and self.headers:
            ws = wb.create_sheet()
            ws.append(self.headers)
        for chunk_data in batch_iter(data_list, self.size):
            ws = wb.create_sheet()
            if self.headers:
                ws.append(self.headers)
            for item in chunk_data:
                ws.append([_get_field_value(item, k) for k in self.fields])
        return wb

    def export_streams(self) -> io.BytesIO:
        wb = self._create_wb()
        stream = io.BytesIO()
        wb.save(stream)
        stream.seek(0)
        return stream

    def export_file(self, filename: str = 'tmp'):
        wb = self._create_wb()
        wb.save(f'{filename}.xlsx')


@dataclass
class ExcelFileExporter:
    fields: List[str] = None
    headers: List[str] = None
    max_rows: int = 10000  # 每次写入文件的行数阈值
    filename: str = f"{uuid.uuid4().hex}.xlsx"  # 默认文件名

    def __post_init__(self):
        # 初始化工作簿和工作表
        self._wb = Workbook(write_only=True)
        self._ws = self._wb.create_sheet()
        self._current_row_count = 0
        self._rows_buffer = []  # 用于缓存行数据
        if not self.filename.startswith("/tmp"):
            self.filename = os.path.join("/tmp", self.filename)
        # 处理header、fields映射
        fields = [item["field"] for item in self.headers]
        headers = [
            item[Language(g.lang)] if g.lang in (
                Language.EN_US.value, Language.ZH_HANS_CN.value)
            else item[Language.DEFAULT]
            for item in self.headers
        ]
        self.fields = fields
        self.headers = headers
        # 如果提供了表头，写入第一行
        if self.headers:
            self._ws.append(self.headers)
            self._current_row_count += 1

    def add_rows(self, rows: List[Any]):
        """
        添加多行数据，如果达到阈值，写入到文件。
        """
        if not isinstance(rows, list):
            rows = [rows]

        self._rows_buffer.extend(rows)
        while len(self._rows_buffer) >= self.max_rows:
            self._flush_to_file()

    def _flush_to_file(self):
        """
        将缓冲区的数据写入到工作表，并清空缓冲区。
        """
        for row in self._rows_buffer[:self.max_rows]:
            self._add_row(row)                
        self._rows_buffer = self._rows_buffer[self.max_rows:]

    def _finalize(self):
        """
        将剩余的缓冲区内容写入工作表，并保存文件。
        """
        if self._rows_buffer:
            for row in self._rows_buffer:
                self._add_row(row)
        self._wb.save(self.filename)

    def _add_row(self, row):
        """
        根据映射关系写入行数据
        """
        self._ws.append([_get_field_value(row, k) for k in self.fields])
        self._current_row_count += 1

    def export_stream(self) -> io.BytesIO:
        """
        导出为流对象。
        """
        self._finalize()
        stream = io.BytesIO()
        with open(self.filename, "rb") as f:
            stream.write(f.read())
        stream.seek(0)
        return stream


def export_xlsx(filename: str, data_list: List[Dict[str, Any]],
                export_headers: Tuple[Dict[Union[Language, str], str], ...]
                ) -> Response:
    fields = [item["field"] for item in export_headers]
    headers = [
        item[Language(g.lang)] if g.lang in (
            Language.EN_US.value, Language.ZH_HANS_CN.value)
        else item[Language.DEFAULT]
        for item in export_headers
    ]
    stream = ExcelExporter(
        data_list=data_list or [],
        fields=fields,
        headers=headers
    ).export_streams()
    now_date = now().strftime("%Y%m%d")
    return send_file(
        stream,
        download_name=f'{now_date}_{filename}.xlsx',
        as_attachment=True
    )


def export_xlsx_with_sheet(sheet_data: Dict[str, Dict[str, Any]], filename='xlsx', temporary=True) -> str:
    """
    sheet_data = {
        "sheet_name": {
            "header_mapper": {"id": "ID", "name": "名称", "email": "邮箱"},
            "data": [
                {
                    "id": 1,
                    "name": name,
                    "email": email
                }
            ]
        }
    }
    """
    wb = Workbook()
    for sheet_name, data in sheet_data.items():
        header_mapper = data['header_mapper']
        ws = wb.create_sheet(sheet_name)
        ws.append(list(header_mapper.values()))
        for item in data['data']:
            ws.append([item[key] for key in header_mapper.keys()])
    if 'Sheet' not in sheet_data:
        del wb['Sheet']

    stream = io.BytesIO()
    wb.save(stream)
    stream.seek(0)
    if temporary:
        # 临时性文件（运营导出、
        from app.utils import upload_file
        return upload_file(stream, filename)
    else:
        return send_file(
            stream,
            download_name=f'{filename}.xlsx',
            as_attachment=True
        )


def export_csv(data: List[Dict[str, Any] | List], headers: List[str], file_name: str) -> Response:
    with io.StringIO() as buf:
        csv_handler = csv.writer(buf)
        csv_handler.writerow(headers)
        d = []
        for item in data:
            if isinstance(item, dict):
                d.append([item[key] for key in headers])
            else:
                d.append(item)
        csv_handler.writerows(d)
        csv_buf = io.BytesIO()
        csv_buf.write(buf.getvalue().encode())
        csv_buf.seek(0)
    return send_file(
        csv_buf,
        download_name=f'{file_name}.csv',
        as_attachment=True,
        mimetype="text/csv",
    )


def remove_illegal_excel_chars(data: List[Dict[str, Any]], fields: Iterable[str] = None) -> None:
    """
    移除数据中的Excel非法字符
    """
    for item in data:
        for key, value in item.items():
            if fields and key not in fields:
                continue
            if isinstance(value, str):
                item[key] = ILLEGAL_CHARACTERS_RE.sub('', value)