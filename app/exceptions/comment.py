from flask_babel import gettext as _

from app.exceptions import ErrorWithResponseCode


class CommentUserBanned(ErrorWithResponseCode):

    response_code = 7001
    message_template = _('无法发表评论，你被禁言，%(banned_date)s后可发表评论')


class CommentUserBannedForever(ErrorWithResponseCode):

    response_code = 7002
    message_template = _('无法发表评论，你被禁言')


class CommentReplyUserBanned(ErrorWithResponseCode):

    response_code = 7003
    message_template = _('无法发表回复，你被禁言，%(banned_date)s后可发表回复')


class CommentReplyUserBannedForever(ErrorWithResponseCode):

    response_code = 7004
    message_template = _('无法发表回复，你被禁言')


class CommentDisabled(ErrorWithResponseCode):

    response_code = 7005
    message_template = _('当前内容已违规，无法进行互动')


class TranslationError(ErrorWithResponseCode):
    response_code = 7006
    message_template = _('翻译失败')


class CommentNotFound(ErrorWithResponseCode):

    response_code = 7007
    message_template = _("当前内容不存在")


class CommentDeleted(ErrorWithResponseCode):

    response_code = 7008
    message_template = _("当前内容已删除")


class UserNotAgreeCommentTOS(ErrorWithResponseCode):
    # 用户未签署协议
    response_code = 7009

class CommentLangMismatch(ErrorWithResponseCode):
    # 评论语言和语区不匹配
    response_code = 7010
    message_template = _("英语频道仅可发表英文内容，无法发表其他语言内容。")


class CommentTipAvailableAmountNotEnough(ErrorWithResponseCode):
    # 用户未签署协议
    response_code = 7011
    message_template = _("%(asset)s可用余额不足，可打赏%(amount)s %(asset)s")

class CommentDuplicate(ErrorWithResponseCode):
    # 评论内容重复
    response_code = 7012
    message_template = _("内容相似啦，试试换个表达方式吧～")

