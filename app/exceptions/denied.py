# -*- coding: utf-8 -*-

from flask_babel import gettext as _
from .base import ErrorWithResponseCode


class LoginForbidden(ErrorWithResponseCode):

    response_code = 116
    message_template = _('禁止登录。')


class FrozenLoginForbidden(ErrorWithResponseCode):
    response_code = 133
    message_template = _('禁止登录。')


class OperationDenied(ErrorWithResponseCode):

    response_code = 11000
    message_template = _('禁止操作')
