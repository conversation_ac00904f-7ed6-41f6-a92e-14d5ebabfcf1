from enum import IntEnum
from flask_babel import gettext as _

from app.exceptions import ErrorWithResponseCode
from app.utils import underscore_to_camel


class P2pExceptionCode(IntEnum):
    NOT_OPEN_P2P_FUNCTION = 6000
    NOT_OPEN_P2P_MERCHANT = 6001
    FORBID_P2P_TRADING = 6002
    OPENED_P2P_FUNCTION = 6003
    OPENED_P2P_MERCHANT = 6004
    FROZEN_MERCHANT = 6005
    NICKNAME_REPEAT = 6006
    NOT_KYC_VERIFICATION_PRO = 6007
    ASSET_NOT_SUPPORTED = 6008
    FIAT_NOT_SUPPORTED = 6009
    FIAT_LIMIT_ERROR = 6010
    ASSET_LIMIT_ERROR = 6011
    MERCHANT_OFFLINE = 6012
    NICKNAME_ERROR = 6013
    PAY_CHANNEL_INVALID = 6014
    ADV_CANCELED_ORDER_LIMIT = 6015
    ORDER_COMPLAINT_LIMIT = 6016
    NICKNAME_HAS_SENSITIVE_WORD = 6017
    MERCHANT_CANCELED = 6018
    NO_MARGIN = 6019
    PAID_MARGIN = 6020
    MARGIN_PAYMENT_ERROR = 6021
    MERCHANT_CANCELING = 6022
    MARGIN_PROCESS = 6023
    MARGIN_NO_ENOUGH = 6024
    KYC_INVALID = 6025
    KYC_PRO_INVALID = 6026
    KYC_INSTITUTION_INVALID = 6027
    TWO_FA_INVALID = 6028
    PERMISSION_INVALID = 6029
    MARGIN_REFUND_ERROR = 6030

    # 广告相关 6400-6499
    ADV_INVENTORY_SHORTAGE = 6400
    ADV_STOCKS_LE_MAX_LIMIT = 6401
    ADV_OFFLINE = 6402
    ADV_WITH_SELF = 6403
    ADV_NOT_ENOUGH = 6404
    ADV_NOT_PAY_CHANNEL = 6405
    ADV_NOT_STOCK = 6406
    PRICE_EXPIRED = 6407
    ADV_MIN_AMOUNT = 6408
    ADV_MAX_AMOUNT = 6409
    ASSET_OFFLINE = 6410
    FIAT_OFFLINE = 6411
    ASSET_AMOUNT_ERROR = 6412
    FIAT_AMOUNT_ERROR = 6413
    PRICE_RATIO_ERROR = 6414
    ADV_BUY_DISABLED = 6415
    ADV_SELL_DISABLED = 6416
    ADV_BUY_PUB_DISABLED = 6417
    ADV_SELL_PUB_DISABLED = 6418
    ADV_SHORTAGE_MAXIMUM = 6419
    ADV_HAS_BEEN_OFFLINE = 6420
    ADV_HAS_BEEN_ONLINE = 6421
    ADV_FIAT_UPDATE = 6422
    ADV_DATA_UPDATE = 6423
    ADV_PRICE_LOW = 6424
    ADV_PRICE_HIGH = 6425

    # 订单相关 6500-6599
    MERCHANT_MAX_ACTIVE_LIMIT = 6501
    USER_CANCEL_ORDER_LIMITED = 6502
    MERCHANT_CANCEL_ORDER_LIMITED = 6503
    FINISH_ORDER = 6504
    HAS_COMPLAINT_FINISH_ORDER = 6505
    MAX_CANCEL_ORDER = 6506
    ORDER_CONFIRM_TIMEOUT = 6507
    ORDER_PAY_TIMEOUT = 6508
    ORDER_BUY_DISABLED = 6509
    ORDER_SELL_DISABLED = 6510
    ORDER_ACCEPT_DISABLED = 6511
    ORDER_RELEASE_ASSET = 6512
    ORDER_RECEIVE_ASSET = 6513

    COMPLAINT_ACCEPT_DISABLED = 6514
    ORDER_HAS_COMPLAINT = 6515
    COMPLAINT_REOPEN = 6516
    COMPLAINT_NOT_YOURS = 6517
    COMPLAINT_STATUS_ERROR = 6518

    SELL_FORBIDDEN = 6519
    RISK_CHECK = 6520
    STATUS_ERROR = 6521
    ORDER_CONDITIONS_NOT_MET = 6522


MESSAGES = {
    # 用户
    P2pExceptionCode.NOT_OPEN_P2P_FUNCTION: _("未开通P2P业务"),
    P2pExceptionCode.NOT_OPEN_P2P_MERCHANT: _("未开通P2P商家业务"),
    P2pExceptionCode.OPENED_P2P_FUNCTION: _("你已经开通P2P业务"),
    P2pExceptionCode.OPENED_P2P_MERCHANT: _("你已经开通P2P商家权限"),
    P2pExceptionCode.NICKNAME_REPEAT: _("昵称重复，提交失败"),
    P2pExceptionCode.NOT_KYC_VERIFICATION_PRO: _("未完成高级kyc审核"),
    P2pExceptionCode.ASSET_NOT_SUPPORTED: _("当前币种不支持P2P交易"),
    P2pExceptionCode.FIAT_NOT_SUPPORTED: _("当前法币不支持P2P交易"),
    P2pExceptionCode.FORBID_P2P_TRADING: _("禁止P2P交易"),
    P2pExceptionCode.FROZEN_MERCHANT: _("商家已被冻结"),
    P2pExceptionCode.MERCHANT_OFFLINE: _("商家已休息"),
    P2pExceptionCode.NICKNAME_ERROR: _("用户名不得包含特殊字符和空格"),
    P2pExceptionCode.PAY_CHANNEL_INVALID: _("支付渠道已失效，请重新配置"),
    P2pExceptionCode.ADV_CANCELED_ORDER_LIMIT: _("取消订单数量达到单日上限"),
    P2pExceptionCode.ORDER_COMPLAINT_LIMIT: _("处理中的申诉订单数量达到上限"),
    P2pExceptionCode.NICKNAME_HAS_SENSITIVE_WORD: _("昵称不合法，提交失败"),
    P2pExceptionCode.MERCHANT_CANCELED: _("您还不是商家，请重新申请成为商家后重试"),
    P2pExceptionCode.NO_MARGIN: _("商家保证金不足，缴纳后恢复发布广告权限"),
    P2pExceptionCode.PAID_MARGIN: _("已划转保证金"),
    P2pExceptionCode.MARGIN_PAYMENT_ERROR: _("商家保证金扣款失败，请重试"),
    P2pExceptionCode.MERCHANT_CANCELING: _("暂不支持申请成为商家，请稍后重试或联系客服"),
    P2pExceptionCode.MARGIN_PROCESS: _("保证金存在待审核记录，请稍后重试"),
    P2pExceptionCode.MARGIN_NO_ENOUGH: _("现货账户余额不满足保证金要求"),
    P2pExceptionCode.KYC_INVALID: _("初级KYC身份异常，请工单联系客服"),
    P2pExceptionCode.KYC_PRO_INVALID: _("高级KYC身份异常，请工单联系客服"),
    P2pExceptionCode.KYC_INSTITUTION_INVALID: _("机构认证状态异常，请工单联系客服"),
    P2pExceptionCode.TWO_FA_INVALID: _("当前账号未绑定2FA，请前往安全设置进行绑定后重试"),
    P2pExceptionCode.PERMISSION_INVALID: _("当前账号权限异常，请工单联系客服"),
    P2pExceptionCode.MARGIN_REFUND_ERROR: _("余额不足，保证金退还失败"),
    # 广告
    P2pExceptionCode.FIAT_LIMIT_ERROR: _("单笔限额数量不符合法币的最大最小限额"),
    P2pExceptionCode.ASSET_LIMIT_ERROR: _("单笔限额数量不符合交易区最大最小限额"),
    P2pExceptionCode.ADV_INVENTORY_SHORTAGE: _("库存数量不符合的要求"),
    P2pExceptionCode.ADV_STOCKS_LE_MAX_LIMIT: _("库存数量应大于单笔限额最大值"),
    P2pExceptionCode.ADV_OFFLINE: _("广告暂未生效"),
    P2pExceptionCode.ADV_WITH_SELF: _("不能和自己的广告交易"),
    P2pExceptionCode.ADV_NOT_ENOUGH: _("不满足商家广告要求"),
    P2pExceptionCode.ADV_NOT_PAY_CHANNEL: _("广告不支持此支付渠道，请刷新页面"),
    P2pExceptionCode.PRICE_EXPIRED: _("价格数据已更新，请刷新页面"),
    P2pExceptionCode.ADV_MIN_AMOUNT: _("未满足广告最小下单金额"),
    P2pExceptionCode.ADV_MAX_AMOUNT: _("超过广告最大下单金额"),
    P2pExceptionCode.ASSET_OFFLINE: _("数字货币已下架，请刷新页面"),
    P2pExceptionCode.FIAT_OFFLINE: _("法币已下架，请刷新页面"),
    P2pExceptionCode.ASSET_AMOUNT_ERROR: _("数字货币数量错误"),
    P2pExceptionCode.FIAT_AMOUNT_ERROR: _("法币数量错误"),
    P2pExceptionCode.PRICE_RATIO_ERROR: _("价格比例错误"),
    P2pExceptionCode.ADV_BUY_DISABLED: _("暂无法买币，如需更多帮助请提交工单咨询"),
    P2pExceptionCode.ADV_SELL_DISABLED: _("暂无法卖币，如需更多帮助请提交工单咨询"),
    P2pExceptionCode.ADV_BUY_PUB_DISABLED: _("暂无法发布买单广告，如需更多帮助请提交工单咨询"),
    P2pExceptionCode.ADV_SELL_PUB_DISABLED: _("暂无法发布卖单广告，如需更多帮助请提交工单咨询"),
    P2pExceptionCode.ADV_SHORTAGE_MAXIMUM: _("您输入的库存数量超过限制"),
    P2pExceptionCode.ADV_HAS_BEEN_OFFLINE: _("当前的广告已下架"),
    P2pExceptionCode.ADV_HAS_BEEN_ONLINE: _("当前的广告已上架"),
    P2pExceptionCode.ADV_FIAT_UPDATE: _("法币发生变更，请刷新页面"),
    P2pExceptionCode.ADV_DATA_UPDATE: _("广告信息已发生变更，请刷新页面后重试"),
    P2pExceptionCode.ADV_PRICE_LOW: _("广告单价格低于最低发布价格，请调高"),
    P2pExceptionCode.ADV_PRICE_HIGH: _("广告单价格超出最高发布价格，请调低"),

    # 订单
    P2pExceptionCode.ADV_NOT_STOCK: _("该广告库存不足，暂时无法下单"),
    P2pExceptionCode.MERCHANT_MAX_ACTIVE_LIMIT: _("商家进行中的订单达到上限，请重新选择"),
    P2pExceptionCode.USER_CANCEL_ORDER_LIMITED: _("已达到当日取消订单上限，暂时无法下单"),
    P2pExceptionCode.MERCHANT_CANCEL_ORDER_LIMITED: _("广告状态异常，暂时无法下单"),
    P2pExceptionCode.FINISH_ORDER: _("放币失败，请稍后重试"),
    P2pExceptionCode.HAS_COMPLAINT_FINISH_ORDER: _("订单申诉中，无法放币"),
    P2pExceptionCode.MAX_CANCEL_ORDER: _("已达到当日取消订单上限，暂时无法取消"),
    P2pExceptionCode.ORDER_CONFIRM_TIMEOUT: _("接单超时"),
    P2pExceptionCode.ORDER_PAY_TIMEOUT: _("付款超时"),
    P2pExceptionCode.ORDER_BUY_DISABLED: _("该商家的广告无法下单"),
    P2pExceptionCode.ORDER_SELL_DISABLED: _("该商家的广告无法下单"),
    P2pExceptionCode.ORDER_ACCEPT_DISABLED: _("暂无法接单，如需更多帮助请提交工单咨询"),
    P2pExceptionCode.ORDER_RELEASE_ASSET: _("暂无法放币，如需更多帮助请提交工单咨询"),
    P2pExceptionCode.ORDER_RECEIVE_ASSET: _("该订单无法放币，如需更多帮助请提交工单咨询"),
    P2pExceptionCode.ORDER_CONDITIONS_NOT_MET: _("不满足交易条件，提交订单失败"),

    P2pExceptionCode.COMPLAINT_ACCEPT_DISABLED: _("未接单不允许创建申诉"),
    P2pExceptionCode.ORDER_HAS_COMPLAINT: _("订单申诉中"),
    P2pExceptionCode.COMPLAINT_REOPEN: _("请联系客服重新打开申诉"),
    P2pExceptionCode.COMPLAINT_NOT_YOURS: _("对方发起了申诉，请在申诉详情中联系客服"),
    P2pExceptionCode.COMPLAINT_STATUS_ERROR: _("申诉详情已更新，请刷新页面重试"),
    P2pExceptionCode.SELL_FORBIDDEN: _("暂无法交易，如需更多帮助请提交工单咨询"),
    P2pExceptionCode.RISK_CHECK: _("暂无法放币，请稍等"),
    P2pExceptionCode.STATUS_ERROR: _("订单状态已更新，请刷新重试"),
}

P2pExceptionMap = {code: type(
    underscore_to_camel(code.name),
    (ErrorWithResponseCode,),
    dict(
        response_code=code.value,
        message_template=message
    )) for code, message in MESSAGES.items()}


class AdvAssetLimitError(ErrorWithResponseCode):
    response_code = 6100
    message_template = _('发布广告数量达到限制')

    @property
    def message(self):
        if isinstance((count := self._data.get("count")), int):
            return _('一个交易市场单方向只能发布%(count)s个广告', count=count)
        return super().message


class OnlineAdvLimitError(ErrorWithResponseCode):
    response_code = 6101
    message_template = _('生效中的广告达到限制')

    @property
    def message(self) -> str:
        if isinstance((count := self._data.get("count")), int):
            return _("最多只能生效%(count)s个广告", count=count)
        return super().message


class P2pPermissionError(ErrorWithResponseCode):
    response_code = 6200
    message_template = _("P2P权限异常")


class ActiveOrderLimitError(ErrorWithResponseCode):
    response_code = 6500

    @property
    def message(self) -> str:
        if isinstance((count := self._data.get("count")), int):
            return _("当前进行中的订单数量%(count)s单，已超过进行中订单的数量限制，请完成进行中的订单", count=count)
        return super().message


class P2pAssetNotValidError(ErrorWithResponseCode):
    response_code = 6300
    message_template = _("交易区失效")

    @property
    def message(self) -> str:
        if isinstance((asset := self._data.get("asset")), str):
            return _("交易区 %(asset)s 暂不支持", asset=asset)
        return super().message


class P2pFiatNotValidError(ErrorWithResponseCode):
    response_code = 6301
    message_template = _("法币失效")

    @property
    def message(self) -> str:
        if isinstance((fiat := self._data.get("fiat")), str):
            return _("法币 %(fiat)s 暂不支持", fiat=fiat)
        return super().message


class PayChannelInvalidError(ErrorWithResponseCode):
    response_code = 6302
    message_template = _("支付渠道失效")

    @property
    def message(self) -> str:
        if isinstance((channel_names := self._data.get("channel_names")), str):
            return _("支付渠道 %(channel_names)s 失效", channel_names=channel_names)
        return super().message


class FiatPayChannelError(ErrorWithResponseCode):
    response_code = 6303
    message_template = _("法币支付渠道不支持")

    @property
    def message(self) -> str:
        if (isinstance((fiat := self._data.get("fiat")), str)
                and isinstance((channel_names := self._data.get("channel_names")), str)):
            return _("法币 %(fiat)s 不支持 %(channel_names)s 支付渠道", fiat=fiat, channel_names=channel_names)
        return super().message


class P2pSellForbiddenAfterSecurityEditing(ErrorWithResponseCode):

    response_code = 6305
    message_template = _('修改安全工具后24小时无法进行p2p卖币')


class P2pSellForbiddenAfterWithdrawPasswordEditing(ErrorWithResponseCode):

    response_code = 6306
    message_template = _('修改提现密码后24小时无法进行p2p卖币')


class P2pAdvStocksError(ErrorWithResponseCode):
    response_code = 6307
    message_template = _('库存不足')

    @property
    def message(self) -> str:
        if (isinstance((stock_amount := self._data.get("stock_amount")), str)
                and isinstance((base := self._data.get("base")), str)):
            return _("数量应大于单笔限额：%(stock_amount)s %(base)s", stock_amount=stock_amount, base=base)
        return super().message
