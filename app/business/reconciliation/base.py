# -*- coding: utf-8 -*-

from datetime import datetime, timedelta
from decimal import Decimal
from typing import <PERSON><PERSON><PERSON><PERSON>, <PERSON>ple, Optional
from ...models import db
from ...utils import now

import time


class Record(NamedTuple):

    start: Optional[datetime]
    end: datetime
    prev_balance: Decimal
    balance: Decimal
    input: Decimal
    output: Decimal
    error: Decimal


class BaseReconciliation:

    model: db.Model = None

    def run(self):
        if not self._prepare():
            return None

        start, before = self._get_last_record()
        end = now()
        if (start is not None
                and (end - start).total_seconds() < self._cool_down()):
            return None

        after = self._get_balance()
        if (wait := 5 - (now() - end).total_seconds()) > 0:
            time.sleep(wait)
        record = self._new_record(start, end, before, after)

        if self._get_error_sign(record.error):
            new_record, history_id = self._neutralise_errors(
                record, end, after, self._tolerance_window())
            if new_record is not None:
                record = new_record
                self._delete_histories_after(history_id)

        self._append_history(record)

        if self._get_error_sign(record.error):
            self._handle_error(record)

        return record

    # noinspection PyMethodMayBeStatic
    def _prepare(self) -> bool:
        return True

    def _get_last_record(self) -> Tuple[Optional[datetime], Decimal]:
        raise NotImplementedError

    def _get_balance(self) -> Decimal:
        raise NotImplementedError

    # noinspection PyMethodMayBeStatic
    def _cool_down(self) -> int:
        return 60

    # noinspection PyMethodMayBeStatic
    def _tolerance_window(self) -> int:
        return 3600

    def _new_record(self,
                    start: Optional[datetime],
                    end: datetime,
                    before: Decimal,
                    after: Decimal) -> Record:
        input_, output = self._get_input_and_output(start, end)
        error = after - (before + input_ - output)
        return Record(
            start,
            end,
            before,
            after,
            input_,
            output,
            error
        )

    def _get_input_and_output(self,
                              start: Optional[datetime],
                              end: datetime) -> Tuple[Decimal, Decimal]:
        raise NotImplementedError

    # noinspection PyMethodMayBeStatic
    def _get_error_sign(self, error: Decimal) -> int:
        if error == 0:
            return 0
        if error > 0:
            return 1
        return -1

    def _neutralise_errors(self,
                           record: Record,
                           end: datetime,
                           after: Decimal,
                           window: float
                           ) -> Tuple[Optional[Record], Optional[int]]:
        get_error_sign = self._get_error_sign
        record_sign = get_error_sign(record.error)
        if not record_sign:
            return None, None

        new_record = None
        history_id = None
        neutralisable = self._get_neutralisable_histories(
            end - timedelta(seconds=window))

        if record_sign > 0:
            for _id, _start, _before, _error in neutralisable:
                if _error >= 0:
                    continue
                _record = self._new_record(_start, end, _before, after)
                if get_error_sign(_record.error) < 0:
                    break
                new_record, history_id = _record, _id

        else:
            for _id, _start, _before, _error in neutralisable:
                if _error <= 0:
                    continue
                _record = self._new_record(_start, end, _before, after)
                if get_error_sign(_record.error) >= 0:
                    new_record, history_id = _record, _id
                    break
                if get_error_sign(_error) > 0:
                    new_record, history_id = _record, _id

        return new_record, history_id

    def _get_neutralisable_histories(self, start: datetime):
        raise NotImplementedError

    def _append_history(self, record: Record):
        raise NotImplementedError

    def _delete_histories_after(self, history_id: int):
        raise NotImplementedError

    def _handle_error(self, record: Record):
        pass
