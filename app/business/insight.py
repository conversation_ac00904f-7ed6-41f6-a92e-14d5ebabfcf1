from typing import List
from typing import Dict

from bs4 import BeautifulSoup

from collections import defaultdict

from decimal import Decimal

from app.caches import <PERSON><PERSON><PERSON><PERSON>
from app.common.constants import Language
from app.models import CoinInformation
from app.models.mongo import ZERO_OBJECT_ID

from app.caches.inverted_index import InsightInvertedIndexCache, InsightInvertedIndexData
from app.models.mongo.insight import (
    CoinExInsightMySQL as CoinExInsight,
    CoinExInsightContentMySQL as CoinExInsightContent,
    ArticleType,
    ArticleStatus,
    ReportType,
    MAX_COIN_COUNT,
)

from app.utils import batch_iter
from app.utils import cut_words
from app.utils import filter_stop_words

LIMIT = 30
MAX_SEARCH_KEYS = 50  # 限制最长搜索分词结果为50
TITLE_TF_IDF_FACTOR = Decimal('2')  # 对标题中的分词TF_IDF结果放大


def to_list_result(
        items: List, total: int, has_next: bool, new_last_id: str, participles: List[str] = None
) -> Dict:
    result = dict(
        items=items,
        total=total,
        has_next=has_next,
        last_id=new_last_id,
    )
    if participles is not None:
        result['participles'] = participles
    return result


def all_insight_page() -> dict:
    rows = CoinExInsight.query.filter(
        CoinExInsight.status == ArticleStatus.EFFECTIVE
    ).order_by(CoinExInsight.is_top.desc(), CoinExInsight.publish_at.desc()).all()
    
    row_map = {row.mongo_id: row for row in rows}
    row_ids = [row.mongo_id for row in rows]
    
    contents = CoinExInsightContent.query.filter(
        CoinExInsightContent.insight_id.in_(row_ids)
    ).all()
    contents.sort(key=lambda x: row_ids.index(x.insight_id))
    
    content_items = defaultdict(list)
    for content in contents:
        content_items[content.insight_id].append(content)
    
    langs = {lang for lang in Language}
    content_map = defaultdict(list)
    for insight_id, items in content_items.items():
        for item in items:
            report_type = item.report_type or ''
            content_map[(item.lang, item.article_type, report_type)].append(item)
            if item.article_type == ArticleType.REPORT:
                content_map[(item.lang, item.article_type, '')].append(item)

        en_us_item = {item.lang: item for item in items}.get(Language.EN_US)  # 英文必填
        missing_langs = langs - {item.lang for item in items}
        if not en_us_item:
            continue
        for lang in missing_langs:
            report_type = en_us_item.report_type or ''
            content_map[(lang, en_us_item.article_type, report_type)].append(en_us_item)
            if en_us_item.article_type == ArticleType.REPORT:
                content_map[(lang, en_us_item.article_type, '')].append(en_us_item)

    result = defaultdict(dict)
    for (lang, article_type, report_type), contents in content_map.items():
        total = len(contents)
        last_id = prev_last_id = ZERO_OBJECT_ID
        for item_contents in batch_iter(contents, LIMIT):
            items = []
            for content in item_contents:
                items.append(get_content_list_item(row_map[content.insight_id], content))
            new_last_id = items[-1]['id'] if len(items) > 0 else ZERO_OBJECT_ID
            result[(lang, article_type, report_type, last_id)] = to_list_result(items, total, True, new_last_id)
            prev_last_id = last_id
            last_id = new_last_id
        result[(lang, article_type, report_type, prev_last_id)]['has_next'] = False
        result[(lang, article_type, report_type, last_id)] = to_list_result([], total, False, ZERO_OBJECT_ID)
    return result


def all_last_insight_page() -> dict:
    """insight 最新列表"""
    rows = CoinExInsight.query.filter(
        CoinExInsight.status == ArticleStatus.EFFECTIVE
    ).order_by(CoinExInsight.publish_at.desc()).all()
    
    row_map = {row.mongo_id: row for row in rows}
    row_ids = [row.mongo_id for row in rows]
    
    contents = CoinExInsightContent.query.filter(
        CoinExInsightContent.insight_id.in_(row_ids)
    ).all()
    contents.sort(key=lambda x: row_ids.index(x.insight_id))
    
    content_items = defaultdict(list)
    for content in contents:
        content_items[content.insight_id].append(content)
    
    langs = {lang for lang in Language}
    content_map = defaultdict(list)
    for insight_id, items in content_items.items():
        for item in items:
            content_map[item.lang].append(item)

        en_us_item = {item.lang: item for item in items}.get(Language.EN_US)  # 英文必填
        missing_langs = langs - {item.lang for item in items}
        if not en_us_item:
            continue
        for lang in missing_langs:
            content_map[lang].append(en_us_item)

    result = defaultdict(dict)
    for lang, contents in content_map.items():
        total = len(contents)
        last_id = prev_last_id = ZERO_OBJECT_ID
        for item_contents in batch_iter(contents, LIMIT):
            items = []
            for content in item_contents:
                items.append(get_content_list_item(row_map[content.insight_id], content))
            new_last_id = items[-1]['id'] if len(items) > 0 else ZERO_OBJECT_ID
            result[(lang, last_id)] = to_list_result(items, total, True, new_last_id)
            prev_last_id = last_id
            last_id = new_last_id
        result[(lang, prev_last_id)]['has_next'] = False
        result[(lang, last_id)] = to_list_result([], total, False, ZERO_OBJECT_ID)
    return result


def get_content_list_item(insight: CoinExInsight, content: CoinExInsightContent) -> Dict:
    abstract = ''
    if content.abstract:
        abstract = content.abstract.replace('\n', '').replace('\r', '')
    return {
        'id': content.insight_id,  # 与前端交互的id为主表的id
        'seo_url_keyword': insight.seo_url_keyword,
        'title': content.title,
        'content': content.display_content,
        'publish_at': insight.publish_at,
        'is_top': insight.is_top,
        'abstract': abstract,
        'coins': insight.coins,
        'cover_url': content.cover_url,
        'app_cover_url': content.app_cover_url,
        'article_type': insight.article_type.name,
        'report_type': insight.report_type.name if insight.report_type else None,
    }


def insight_list(
        article_type: ArticleType,
        lang: Language,
        last_id: str,
        page: int = 1,
        limit: int = LIMIT,
        report_type: ReportType = None,
) -> Dict:
    insights = CoinExInsight.pagination(
        article_type,
        report_type=report_type,
        page=page,
        limit=limit,
    )
    total = CoinExInsight.count(
        article_type,
        report_type=report_type,
    )
    has_next = CoinExInsight.has_next(
        article_type,
        report_type=report_type,
        page=page,
        limit=limit,
    ) if len(insights) == limit else False

    insight_to_contents = get_insight_to_contents_by(insights, lang)
    items = [get_content_list_item(insight, content) for insight, content in insight_to_contents.items()]
    new_last_id = items[-1]['id'] if len(items) > 0 else ZERO_OBJECT_ID
    return to_list_result(items, total, has_next, new_last_id)


def get_insight_to_contents_by(insights: List[CoinExInsight], lang: Language) -> dict:
    insight_map = {x.mongo_id: x for x in insights}
    ids = [x.mongo_id for x in insights]
    contents = CoinExInsightContent.query.filter(
        CoinExInsightContent.insight_id.in_(ids)
    ).all()
    contents.sort(key=lambda x: ids.index(x.insight_id))
    
    content_items = defaultdict(list)
    for content in contents:
        content_items[content.insight_id].append(content)
    
    ret = {}
    for insight_id, items in content_items.items():
        mapping = {x.lang: x for x in items}
        c = mapping.get(lang)
        if not c:
            c = mapping[Language.EN_US]
        ret[insight_map[insight_id]] = c
    return ret


def search_insight_by_inverted_index(
        lang: Language,
        last_id: str,
        limit: int,
        search_keys: List[str],
        article_type: ArticleType = None,
):
    if len(search_keys) > MAX_SEARCH_KEYS:
        # 分词结果超过最大限制时，先过滤停用词后只保留前若干个分词结果用于查询
        search_keys = filter_stop_words(lang, search_keys)[:MAX_SEARCH_KEYS]
    search_keys = [item.lower() for item in search_keys]

    insight_word_map = defaultdict(lambda: defaultdict(Decimal))
    insight_publish_at_map = defaultdict(int)
    for word, items in InsightInvertedIndexCache(InsightInvertedIndexData, lang).get(
            search_keys,
            article_type=article_type,
    ).items():
        for item in items:
            tf_idf = item['tf_idf'] if item['search_type'] == CoinExInsightContent.SearchType.CONTENT \
                else item['tf_idf'] * TITLE_TF_IDF_FACTOR  # 标题中包含的分词乘以放大系数
            insight_word_map[item['insight_id']][word] = tf_idf
            insight_publish_at_map[item['insight_id']] = item['publish_at']
    tf_idf_map = {
        _id: (sum(word_map.values()), insight_publish_at_map.get(_id, 0))
        for _id, word_map in insight_word_map.items()
    }
    start_flag = True if (not last_id or last_id == ZERO_OBJECT_ID) else False
    result_ids = []
    traverse_count = 0
    for _id, _ in sorted(tf_idf_map.items(), key=lambda x: (x[1][0], x[1][1]), reverse=True):
        traverse_count += 1
        if _id == last_id:
            start_flag = True
            continue
        if start_flag:
            result_ids.append(_id)
        if len(result_ids) >= limit:
            break

    insights = CoinExInsight.query.filter(
        CoinExInsight.mongo_id.in_(result_ids)
    ).all()
    
    contents = CoinExInsightContent.query.filter(
        CoinExInsightContent.insight_id.in_(result_ids),
        CoinExInsightContent.lang == lang
    ).all()
    
    missing_ids = list(set(result_ids) - {c.insight_id for c in contents})
    if missing_ids:
        en_contents = CoinExInsightContent.query.filter(
            CoinExInsightContent.insight_id.in_(missing_ids),
            CoinExInsightContent.lang == Language.EN_US
        ).all()
        contents.extend(en_contents)
    
    insight_map = {item.mongo_id: item for item in insights}
    items = []
    for content in contents:
        insight = insight_map[content.insight_id]
        items.append(get_content_list_item(insight, content))
    total = len(tf_idf_map)
    has_next = True if traverse_count < total else False
    new_last_id = result_ids[-1] if len(result_ids) > 0 else ZERO_OBJECT_ID

    return to_list_result(items, total, has_next, new_last_id)


def search_insight_list(
        lang: Language,
        last_id: str,
        limit: int,
        search_key: str,
        article_type: ArticleType = None,
):
    if not search_key:
        return to_list_result([], 0, False, ZERO_OBJECT_ID, participles=[])

    search_keys = cut_words(lang, search_key)
    if not search_keys:
        return to_list_result([], 0, False, ZERO_OBJECT_ID, participles=[])
    result = search_insight_by_inverted_index(
        lang,
        last_id,
        limit,
        search_keys,
        article_type=article_type,
    )
    result['participles'] = search_keys
    return result


def update_all_insight_inverted_index_cache():
    """更新支持的所有语言的倒排索引缓存"""
    all_list = CoinExInsightContent.query.filter(
        CoinExInsightContent.status == ArticleStatus.EFFECTIVE
    ).all()
    
    id_to_en_us = {c.insight_id: c for c in all_list if c.lang == Language.EN_US}
    group_by_id = defaultdict(list)
    for content in all_list:
        group_by_id[content.insight_id].append(content)
    
    langs = {lang for lang in Language}
    group_by_lang = defaultdict(list)
    for insight_id, items in group_by_id.items():
        for item in items:
            group_by_lang[item.lang].append(item)
        en_us_item = id_to_en_us[insight_id]
        exist_langs = {item.lang for item in items}
        for lang in langs - exist_langs:
            group_by_lang[lang].append(en_us_item)

    for lang, items in group_by_lang.items():
        update_insight_inverted_index_cache(lang, items)


def update_insight_inverted_index_cache(lang: Language, items: List['CoinExInsightContent']):
    """根据语言更新资讯的倒排索引缓存"""
    if (total := len(items)) == 0:
        return
    word_map = defaultdict(list)
    for insight in items:
        if (total_word_count := len(insight.title_participles) + len(insight.content_participles)) == 0:
            continue
        insight_words = defaultdict(lambda: {
            'search_type_title': False,
            'search_type_content': False,
            'count': 0,
        })
        for word in insight.title_participles:
            insight_word = insight_words[word.lower()]
            insight_word['search_type_title'] = True
            insight_word['count'] += 1
        for word in insight.content_participles:
            insight_word = insight_words[word.lower()]
            insight_word['search_type_content'] = True
            insight_word['count'] += 1
        for word, data in insight_words.items():
            word_map[word].append({
                'article_type': insight.article_type,
                'report_type': insight.report_type,
                'insight_id': insight.insight_id,
                'publish_at': int(insight.publish_at.timestamp()),
                'tf': Decimal(data['count'] / total_word_count),
                'search_type': _get_search_type(data['search_type_title'], data['search_type_content']),
            })
    for _, items in word_map.items():
        idf = InsightInvertedIndexCache.calculate_idf(total, len(items))
        for item in items:
            item['tf_idf'] = item['tf'] * idf

    # 之前的keys对比新的keys计算出需要删除的key
    index_cache = InsightInvertedIndexCache(InsightInvertedIndexData, lang)
    del_keys = set(index_cache.hkeys()) - set(word_map.keys())

    index_cache.update(word_map)

    if len(del_keys) > 0:
        index_cache.hdel(*del_keys)


def _get_search_type(title: bool, content: bool) -> CoinExInsightContent.SearchType:
    if title and content:
        return CoinExInsightContent.SearchType.ALL
    elif title:
        return CoinExInsightContent.SearchType.TITLE
    return CoinExInsightContent.SearchType.CONTENT


def get_participles(lang: Language, title: str, content: str):
    text = '\n'.join(
        [item.get_text(' ') for item in BeautifulSoup(content, 'html.parser').contents])
    if '\n' in text:
        text = text.replace('\n', ' ')
    if '\r' in text:
        text = text.replace('\r', ' ')
    title_participles = cut_words(lang, title)
    content_participles = cut_words(lang, text)
    participles = title_participles + content_participles
    coins = _get_coins(participles)
    return {
        'lang': lang.name,
        'title_participles': title_participles,
        'content_participles': content_participles,
        'participles': participles,
        'coins': coins,
        'text': text,
    }


def _get_coins(participles):
    all_coin_map = get_all_coin_map()
    return list({
        all_coin_map[word]
        for word in get_full_participles(participles) if word in all_coin_map
    })[:MAX_COIN_COUNT]


def get_all_coin_map() -> dict[str, str]:
    all_coins = set(AssetCache.list_all_assets())
    all_coin_infos = CoinInformation.query.filter(
        CoinInformation.status == CoinInformation.Status.VALID,
    ).with_entities(
        CoinInformation.code,
        CoinInformation.name,
    ).all()
    coin_info_map = {}
    for coin_info in all_coin_infos:
        if coin_info.code not in all_coins:
            continue
        # 最多保留前三个单词用于特征匹配
        coin_info_map[' '.join(coin_info.name.split()[:3])] = coin_info.code
    # 先存储name再存储code是为了优先考虑币种的Symbol信息
    for coin_info in all_coin_infos:
        if coin_info.code not in all_coins:
            continue
        coin_info_map[coin_info.code] = coin_info.code
    return coin_info_map


def get_full_participles(participles: list[str]) -> set[str]:
    alpha_participles = [word for word in participles if len(word) > 0 and word[0].isalpha()]
    # 构成所有英文字符的一个、两个以及三个连续组合
    full_participles = set(alpha_participles)
    if (length := len(full_participles)) < 2:
        return full_participles
    for index in range(length - 2):
        word_2 = '{} {}'.format(alpha_participles[index], alpha_participles[index + 1])
        word_3 = '{} {}'.format(word_2, alpha_participles[index + 2])
        full_participles.add(word_2)
        full_participles.add(word_3)
    full_participles.add('{} {}'.format(alpha_participles[length - 2], alpha_participles[length - 1]))
    return full_participles
