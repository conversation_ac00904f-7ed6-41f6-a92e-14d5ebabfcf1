import math
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

import requests

from app.utils.date_ import date_to_datetime, now, timestamp_to_datetime

from ...models.mongo.exchange_assets import ExchangeType
from .base import BaseExchangeAPIClient, RequestMethod


class PoloniexAPIClient(BaseExchangeAPIClient):

    @dataclass(frozen=True)
    class Endpoint(object):
        NORMAL = 'https://api.poloniex.com/'
        DATA = 'https://api.poloniex.com/'

    @dataclass(frozen=True)
    class Apis(object):
        LAST_24H_TICKERS = '/markets/ticker24h'
        KLINE = "/markets/{symbol}/candles"


    exchange = ExchangeType.POLONIEX

    def __init__(self):
        self.params = dict()
        self.session = requests.Session()


    def do_request(self,
                   end_point: str,
                   api_path: str,
                   method: str,
                   params: dict | None = None,
                   json_body: dict | None = None):
        # TODO: not support sign for api
        return self._simple_do_request(end_point, api_path, method, params, json_body)

    def parse_tickers(self) -> dict:
        """
        dict(
            asset: [dict(market='', base='', quote='', base='', volume='')...]
        )
        """
        result = self.do_request(self.Endpoint.DATA,
                                 self.Apis.LAST_24H_TICKERS,
                                 RequestMethod.GET)
        final_data = defaultdict(list)
        remove_assets = ["USDT"]
        for _detail in result:
            base = quote = None
            raw_market = _detail["symbol"]
            _market_name = raw_market.upper()
            base, quote = _market_name.split("_")
            if not quote:
                continue
            if base in remove_assets:
                continue
            # if any([base.endswith(subfix) for subfix in excludes]):
            #     continue
            volume = Decimal(_detail["amount"])
            convert_market_name = f"{base}{quote}".upper()
            final_data[base].append(
                dict(market=convert_market_name, raw_market=raw_market, quote=quote, base=base, volume=volume)
            )
        return final_data

    def get_klines(self, market: str, start_time: datetime, end_time: datetime):
        market = market.replace('/', '_')

        delta = 86400 * 1000
        res_mts = [int(start_time.timestamp()) * 1000, int(end_time.timestamp()) * 1000]
        klines = []
        limit = 500  # per page max 500
        times = math.ceil((end_time - start_time).days / limit)
        cur_mts = res_mts[0] - delta
        end_mts = res_mts[1] + delta
        for _ in range(times):
            next_mts = cur_mts + 86400 * 1000 * limit
            next_mts = min(next_mts, end_mts)
            params = {
                "symbol": market,
                "interval": "DAY_1",
                "startTime": cur_mts,
                "endTime": next_mts,
                "limit": limit,
            }
            result = self.do_request(
                self.Endpoint.NORMAL,
                self.Apis.KLINE.format(symbol=market),
                RequestMethod.GET,
                params,
            )
            klines.extend([x for x in result if res_mts[0] <= int(x[-2]) <= res_mts[1]])
            if len(result) < limit:
                break
            cur_mts = max([int(x[6]) for x in result])
        klines.sort(key=lambda x: int(x[-2]))
        # [time, close, amount, volume]
        kl_res = [[int(int(x[-2]) / 1000), Decimal(x[3]), Decimal(x[5]), Decimal(x[4])] for x in klines]
        return kl_res

    def find_first_kline_ts(self, market: str) -> int:
        """ 找到市场最早的k线时间，先用月k线找到首月，再用日k线 """
        market = market.replace('/', '_')
        klines = []
        limit = 500
        for _ in range(10):
            # 默认返回最近的交易
            params = {
                "symbol": market,
                "interval": "MONTH_1",
                "limit": limit,
            }
            result = self.do_request(
                self.Endpoint.NORMAL,
                self.Apis.KLINE.format(symbol=market),
                RequestMethod.GET,
                params,
            )
            klines.extend(result)
            if len(result) < limit:
                break

        klines.sort(key=lambda x: int(x[-2]))
        if klines:
            first_month_ts = int(int(klines[0][-2]) / 1000)
        else:
            this_month = date_to_datetime(now().date().replace(day=1))
            first_month_ts = int(this_month.timestamp())

        start_time = timestamp_to_datetime(first_month_ts)
        end_time = timestamp_to_datetime(first_month_ts + 86400 * 31)
        kls = self.get_klines(market, start_time, end_time)
        if kls:
            return kls[0][0]
        return 0
