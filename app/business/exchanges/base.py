from dataclasses import dataclass
from typing import Optional
from urllib.parse import urljoin

import requests
from flask import current_app

from app.business import mem_cached
from app.caches import MarketCache
from app.models.mongo.exchange_assets import ExchangeType


@dataclass(frozen=True)
class RequestMethod(object):
    GET = 'get'
    POST = 'post'


class _BaseExchangeAPIClientMeta(type):

    exchange: ExchangeType = None
    _clients = {}

    def __new__(mcs, *args, **kwargs):
        cls = super().__new__(mcs, *args, **kwargs)
        exchange = getattr(cls, 'exchange', None)
        if exchange is None:  # base class
            return cls
        mcs._clients[exchange] = cls
        return cls

    @classmethod
    def get_api_client(mcs, exchange_type: ExchangeType):
        return _BaseExchangeAPIClientMeta._clients[exchange_type]()

    @classmethod
    def get_all_api_clients(mcs) -> dict:
        return {_e: _v() for _e, _v in _BaseExchangeAPIClientMeta._clients.items()}


class BaseExchangeAPIClient(metaclass=_BaseExchangeAPIClientMeta):

    exchange: ExchangeType
    consts: Optional[dataclass] = None
    helper: object | None = None
    params: dict | None = None
    end_points: Optional[dataclass] = None
    apis: Optional[dataclass] = None

    def do_request(self,
                   end_point: str,
                   api_path: str,
                   method: str,
                   params: dict,
                   json_body: dict):
        raise NotImplementedError

    def _simple_do_request(self,
                           end_point: str,
                           api_path: str,
                           method: str,
                           params: dict,
                           json_body: dict):
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',
            'Content-Type': 'application/json'
        }
        # TODO: not support sign for api
        request_path = api_path
        url = urljoin(end_point, request_path)
        response = getattr(self.session, method)(url,
                                                 headers=headers,
                                                 params=params, json=json_body)
        if response is not None and response.status_code != requests.codes.ok:
            current_app.logger.warning(f"req:{url} "
                                       f"resp_code:{response.status_code}, "
                                       f"req_headers: {response.request.headers},"
                                       f"req_body: {response.request.body},")
            return response.json()
        return response.json() if response else {}

    def get_asset_full_names(self) -> dict:
        return {}


get_all_api_clients = _BaseExchangeAPIClientMeta.get_all_api_clients
get_api_client = _BaseExchangeAPIClientMeta.get_api_client


@mem_cached(600)
def get_market_data():
    return MarketCache.online_markets_detail()
