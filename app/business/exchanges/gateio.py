from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

import requests

from app import config
from app.utils.date_ import date_to_datetime, now, timestamp_to_datetime

from ...models.mongo.exchange_assets import ExchangeType
from .base import BaseExchangeAPIClient, RequestMethod, get_market_data


class GateIOAPIClient(BaseExchangeAPIClient):

    @dataclass(frozen=True)
    class Endpoint(object):
        NORMAL = 'https://api.gateio.ws'

    @dataclass(frozen=True)
    class Apis(object):
        TICKERS = '/api/v4/spot/tickers'
        KLINE = "/api/v4/spot/candlesticks"

    exchange = ExchangeType.GATEIO

    def __init__(self):
        _config = config.get("GATEIO_API_CONFIG", dict())
        self.params = dict()
        self.session = requests.Session()

    def do_request(self,
                   end_point: str,
                   api_path: str,
                   method: str,
                   params: dict | None = None,
                   json_body: dict | None = None):
        # TODO: not support sign for api
        return self._simple_do_request(end_point, api_path, method, params, json_body)

    def parse_tickers(self) -> dict:
        """
        dict(
            asset: [dict(market='', base='', quote='', base='', volume='')...]
        )
        """
        result = self.do_request(self.Endpoint.NORMAL,
                                 self.Apis.TICKERS,
                                 RequestMethod.GET)

        final_data = defaultdict(list)
        our_market_details = get_market_data()
        common_quotes = ('USDT', 'ETH', 'BTC')
        excludes = ["2L", "2S", "3L", "3S", "5L", "5S", "UP", "DOWN", "3S", "3L"]
        excludes_whitelist = ["TUP", "JUP", "UP"]
        remove_assets = ["USDT"]
        for _detail in result:
            base = quote = None
            raw_market = _detail["currency_pair"]
            _market_name = raw_market.upper()
            if "_" in _market_name:
                parse_base, parse_quote = _market_name.split("_", 1)
                if any([parse_base.endswith(subfix) for subfix in excludes]) and (parse_base not in excludes_whitelist):
                    # 排除这些结尾的币种
                    continue
                convert_market_name = f"{parse_base}{parse_quote}".upper()
            else:
                convert_market_name = _market_name

            if _market_name in our_market_details:
                _our_detail = our_market_details[_market_name]
                quote = _our_detail["quote_asset"]
                base = _our_detail["base_asset"]
            else:
                for _quote in common_quotes:
                    if _market_name.endswith(_quote):
                        quote = _quote
                        base = _market_name.removesuffix(_quote).strip("_")
            if not quote:
                continue
            if base in remove_assets:
                continue
            volume = Decimal(_detail["quote_volume"])
            final_data[base].append(
                dict(market=convert_market_name, raw_market=raw_market, quote=quote, base=base, volume=volume)
            )
        return final_data

    def get_klines(self, market: str, start_time: datetime, end_time: datetime):
        """
        每个时间粒度的 K 线数据，从左到右依次为:
        - 秒(s)精度的 Unix 时间戳
        - 计价货币交易额 USDT
        - 收盘价
        - 最高价
        - 最低价
        - 开盘价
        - 基础货币交易量 BTC
        - 窗口是否关闭，true 代表此段K线蜡烛图数据结束，false 代表此段K线蜡烛图数据尚未结束
        """
        delta = 86400
        res_mts = [int(start_time.timestamp()), int(end_time.timestamp())]
        cur_ts = res_mts[0] - delta
        end_ts = res_mts[1] + delta
        klines = []
        limit = 1000 - 1  # per page max 1000
        while cur_ts <= end_ts:
            next_ts = cur_ts + 86400 * limit
            next_ts = min(next_ts, end_ts)
            params = {
                "currency_pair": market,
                "interval": "1d",
                "from": cur_ts,  # 包括from和to
                "to": next_ts,
            }
            result = self.do_request(
                self.Endpoint.NORMAL,
                self.Apis.KLINE,
                RequestMethod.GET,
                params,
            )
            if not result:
                break
            cur_ts = next_ts + 86400
            klines.extend([x for x in result if res_mts[0] <= int(x[0]) <= res_mts[1]])
        klines.sort(key=lambda x: int(x[0]))
        # [time, close, amount, volume]
        kl_res = [[int(x[0]), Decimal(x[2]), Decimal(x[6]), Decimal(x[1])] for x in klines]
        return kl_res

    def find_first_kline_ts(self, market: str) -> int:
        params = {
            "currency_pair": market,
            "interval": "30d",
            "limit": 1000,
        }
        klines = self.do_request(
            self.Endpoint.NORMAL,
            self.Apis.KLINE,
            RequestMethod.GET,
            params,
        )
        klines.sort(key=lambda x: int(x[0]))
        if klines:
            first_month_ts = int(klines[0][0])
        else:
            this_month = date_to_datetime(now().date().replace(day=1))
            first_month_ts = int(this_month.timestamp())

        start_time = timestamp_to_datetime(first_month_ts)
        end_time = timestamp_to_datetime(first_month_ts + 86400 * 31)
        kls = self.get_klines(market, start_time, end_time)
        if kls:
            # 市场上架了，但是没开盘，无K线. eg BD20_USDT
            return kls[0][0]
        return 0
