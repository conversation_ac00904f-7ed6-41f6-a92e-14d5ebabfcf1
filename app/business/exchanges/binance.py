import math
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

import requests

from app import config
from app.utils.date_ import date_to_datetime, now, timestamp_to_datetime

from ...models.mongo.exchange_assets import ExchangeType
from .base import BaseExchangeAPIClient, RequestMethod, get_market_data


class BinanceAPIClient(BaseExchangeAPIClient):

    @dataclass(frozen=True)
    class Endpoint(object):
        NORMAL = 'https://api.binance.com'
        DATA = 'https://data-api.binance.vision/'

    @dataclass(frozen=True)
    class Apis(object):
        TRADE_DAY_TICKERS = '/api/v3/ticker/tradingDays'
        LAST_24H_TICKERS = '/api/v3/ticker/24hr'
        KLINE = "/api/v3/klines"

    exchange = ExchangeType.BINANCE

    def __init__(self):
        _config = config.get("BINANCE_API_CONFIG", dict())
        self.params = dict()
        self.session = requests.Session()

    def do_request(self,
                   end_point: str,
                   api_path: str,
                   method: str,
                   params: dict | None = None,
                   json_body: dict | None = None):
        # TODO: not support sign for api
        return self._simple_do_request(end_point, api_path, method, params, json_body)

    def parse_tickers(self) -> dict:
        """
        dict(
            asset: [dict(market='', base='', quote='', base='', volume='')...]
        )
        """
        result = self.do_request(self.Endpoint.DATA,
                                 self.Apis.LAST_24H_TICKERS,
                                 RequestMethod.GET)
        final_data = defaultdict(list)
        our_market_details = get_market_data()
        remove_assets = ["USDT"]
        common_binance_quote = ('USDT', 'BNB', 'ETH',
                                'FDUSD', 'USDC', 'TUSD', 'DAI',
                                'XRP', 'TRX', 'DOGE', 'AEUR')
        excludes = ["2L", "2S", "3L", "3S", "5L", "5S", "UP", "DOWN", "3S", "3L"]
        excludes_whitelist = ["TUP", "JUP", "UP"]
        for _detail in result:
            base = quote = None
            raw_market = _detail["symbol"]
            _market_name = raw_market.upper()
            if _market_name in our_market_details:
                _our_detail = our_market_details[_market_name]
                quote = _our_detail["quote_asset"]
                base = _our_detail["base_asset"]
            else:
                for _quote in common_binance_quote:
                    if _market_name.endswith(_quote):
                        quote = _quote
                        base = _market_name.removesuffix(_quote)
            if not quote:
                continue
            if base in remove_assets:
                continue
            if any([base.endswith(subfix) for subfix in excludes]) and (base not in excludes_whitelist):
                continue
            volume = Decimal(_detail["quoteVolume"])
            convert_market_name = f"{base}{quote}".upper()
            final_data[base].append(
                dict(market=convert_market_name, raw_market=raw_market, quote=quote, base=base, volume=volume)
            )
        return final_data

    def get_klines(self, market: str, start_time: datetime, end_time: datetime):
        """
        [
            1499040000000,      // k线开盘时间
            "0.01634790",       // 开盘价
            "0.80000000",       // 最高价
            "0.01575800",       // 最低价
            "0.01577100",       // 收盘价(当前K线未结束的即为最新价)
            "148976.11427815",  // 成交量
            1499644799999,      // k线收盘时间
            "2434.19055334",    // 成交额
            308,                // 成交笔数
            "1756.87402397",    // 主动买入成交量
            "28.46694368",      // 主动买入成交额
            "17928899.62484339" // 请忽略该参数
        ]
        """
        delta = 86400 * 1000
        res_mts = [int(start_time.timestamp()) * 1000, int(end_time.timestamp()) * 1000]
        klines = []
        limit = 1000  # per page max 1000
        times = math.ceil((end_time - start_time).days / limit)
        cur_mts = res_mts[0] - delta
        end_mts = res_mts[1] + delta
        for _ in range(times):
            next_mts = cur_mts + 86400 * 1000 * limit
            next_mts = min(next_mts, end_mts)
            params = {
                "symbol": market,
                "interval": "1d",
                "startTime": cur_mts,
                "endTime": next_mts,
                "limit": limit,
            }
            result = self.do_request(
                self.Endpoint.NORMAL,
                self.Apis.KLINE,
                RequestMethod.GET,
                params,
            )
            klines.extend([x for x in result if res_mts[0] <= int(x[0]) <= res_mts[1]])
            if len(result) < limit:
                break
            cur_mts = max([int(x[6]) for x in result])
        klines.sort(key=lambda x: int(x[0]))
        # [time, close, amount, volume]
        kl_res = [[int(int(x[0]) / 1000), Decimal(x[4]), Decimal(x[5]), Decimal(x[7])] for x in klines]
        return kl_res

    def find_first_kline_ts(self, market: str) -> int:
        """ 找到市场最早的k线时间，先用月k线找到首月，再用日k线 """
        klines = []
        limit = 1000
        for _ in range(10):
            # 默认返回最近的交易
            params = {
                "symbol": market,
                "interval": "1M",
                "limit": limit,
            }
            result = self.do_request(
                self.Endpoint.NORMAL,
                self.Apis.KLINE,
                RequestMethod.GET,
                params,
            )
            klines.extend(result)
            if len(result) < limit:
                break

        klines.sort(key=lambda x: int(x[0]))
        if klines:
            first_month_ts = int(int(klines[0][0]) / 1000)
        else:
            this_month = date_to_datetime(now().date().replace(day=1))
            first_month_ts = int(this_month.timestamp())

        start_time = timestamp_to_datetime(first_month_ts)
        end_time = timestamp_to_datetime(first_month_ts + 86400 * 31)
        kls = self.get_klines(market, start_time, end_time)
        if kls:
            return kls[0][0]
        return 0
