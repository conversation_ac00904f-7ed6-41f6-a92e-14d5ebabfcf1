from collections import defaultdict
from dataclasses import dataclass
from decimal import Decimal
from datetime import datetime, timedelta

import requests

from .base import BaseExchangeAPIClient, RequestMethod
from .. import TradeSummaryDB
from ...models.mongo.exchange_assets import ExchangeType
from ...models.quotes import CoinInformation
from app.utils.date_ import now, timestamp_to_datetime, date_to_datetime, today


class CoinexAPIClient(BaseExchangeAPIClient):

    exchange = ExchangeType.COINEX

    @dataclass(frozen=True)
    class Endpoint(object):
        NORMAL = 'https://api.coinex.com'
        RES = 'https://www.coinex.com'

    @dataclass(frozen=True)
    class Apis(object):
        TICKERS = '/v2/spot/ticker'
        RES_KLINE = '/res/market/kline'
        RES_COINS = '/res/wallet/assets'

    def __init__(self):
        self.params = dict()
        self.session = requests.Session()

    def do_request(self,
                   end_point: str,
                   api_path: str,
                   method: str,
                   params: dict | None = None,
                   json_body: dict | None = None):
        # TODO: not support sign for api
        return self._simple_do_request(end_point, api_path, method, params, json_body)

    def parse_tickers(self) -> dict:
        """
        dict(
            asset: [dict(market='', base='', quote='', base='', volume='')...]
        )
        """

        today_ = today()
        yesterday = today_ - timedelta(days=1)

        coin_trade_summarys = TradeSummaryDB.get_coin_trade_summary_by_date(
            yesterday, ['market', 'stock_asset', 'money_asset', 'deal_volume']
        )

        final_data = defaultdict(list)
        for coin_trade_summary in coin_trade_summarys:
            _market_name = coin_trade_summary["market"]
            quote = coin_trade_summary["money_asset"]
            base = coin_trade_summary["stock_asset"]
            volume = Decimal(coin_trade_summary["deal_volume"])
            final_data[base].append(
                dict(market=_market_name, raw_market=_market_name, quote=quote, base=base, volume=volume)
            )
        return final_data

    def get_asset_full_names(self) -> dict:
        rows = CoinInformation.query.with_entities(CoinInformation.code, CoinInformation.name).all()
        return dict(rows)

    def get_klines(self, market: str, start_time: datetime, end_time: datetime):
        """
        [time, open, close, highest, lowest, amount, volume]
        """
        delta = 86400
        res_mts = [int(start_time.timestamp()), int(end_time.timestamp())]
        cur_ts = res_mts[0] - delta
        end_ts = res_mts[1] + delta
        limit = 1000
        klines = []
        while cur_ts <= end_ts:
            next_ts = cur_ts + 86400 * limit
            next_ts = min(next_ts, end_ts)
            params = {
                "market": market,
                "interval": 86400,
                "start_time": cur_ts,
                "end_time": next_ts,
                "limit": limit,
            }
            resp = self.do_request(
                self.Endpoint.RES,
                self.Apis.RES_KLINE,
                RequestMethod.GET,
                params,
            )
            result = resp['data']
            klines.extend([x for x in result if res_mts[0] <= int(x[0]) <= res_mts[1]])
            if not result:
                break
            cur_ts = next_ts + 86400
        klines.sort(key=lambda x: int(x[0]))
        # [time, close, amount, volume]
        kl_res = [[int(x[0]), Decimal(x[2]), Decimal(x[5]), Decimal(x[6])] for x in klines]
        return kl_res

    def find_first_kline_ts(self, market: str) -> int:
        now_ts = int(now().timestamp())
        end_ts = now_ts - now_ts // 86400
        params = {
            "market": market,
            "interval": 2592000,
            "start_time": 1483228800,  # 2017.1.1
            "end_time": end_ts,
        }
        response = self.do_request(
            self.Endpoint.RES,
            self.Apis.RES_KLINE,
            RequestMethod.GET,
            params,
        )
        klines = response['data']

        klines.sort(key=lambda x: int(x[0]))
        if klines:
            first_month_ts = int(klines[0][0])
        else:
            this_month = date_to_datetime(now().date().replace(day=1))
            first_month_ts = int(this_month.timestamp())

        start_time = timestamp_to_datetime(first_month_ts)
        end_time = timestamp_to_datetime(first_month_ts + 86400 * 31)
        kls = self.get_klines(market, start_time, end_time)
        if kls:
            return kls[0][0]
        return 0
