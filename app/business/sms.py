# -*- coding: utf-8 -*-
import random
from typing import List
from app import config
from app.caches.flow_control import SmsCountryResultCache, SmsCountryResultSetCache
from app.common import Language, CeleryQueues
from app.utils import (
    celery_task, route_module_to_celery_queue, SMSProvider, get_sms_sender
)
from ..caches import CountrySmsSettingCache, SmsSendHourCountCache
from .clients import monitor_wrap


route_module_to_celery_queue(__name__, CeleryQueues.SMS)


class SMSSender:

    @classmethod
    def send(cls,
             country_code: int,
             number: str,
             text: str,
             lang: str,
             providers: List[SMSProvider] = None,
             extra: dict = None) -> bool:
        if not country_code or not number:
            return False
        if not providers:
            providers = cls._select_providers(country_code, lang)
        original_text = text
        for provider in providers:
            handler = get_sms_sender(provider)
            # wrap with monitor
            monitored_send = monitor_wrap(
                handler.send,
                "sms_client",
                "job",
                labels={
                    "sms_provider": provider.value,
                    "sms_country": country_code,
                },
                failed_checker=lambda s: s is False,
            )
            text = cls._with_prefix(original_text, provider, lang)
            with monitored_send(country_code, number, text, extra) as ok:
                result_cache = SmsCountryResultCache(provider.name)
                result_set_cache = SmsCountryResultSetCache(provider.name)
                if ok:
                    result_cache.set_success(country_code)
                    result_set_cache.add(f"{country_code}:{number}")
                    break
                else:
                    result_cache.set_fail(country_code)
                    
        else:
            return False
        return True

    @classmethod
    def _with_prefix(cls, text: str, provider: SMSProvider, lang: str) -> str:
        if provider in (SMSProvider.PARS_GREEN, SMSProvider.TELEGRAM, SMSProvider.ALIYUN):
            return text
        if lang in [Language.ZH_HANS_CN.value, Language.ZH_HANT_HK.value]:
            prefix = config['SMS_PREFIX_CN']
        else:
            prefix = config['SMS_PREFIX_EN']
        return f'{prefix}{text}'

    @classmethod
    def _select_providers(cls, country_code: int, lang: str) -> List[SMSProvider]:
        if v := CountrySmsSettingCache(country_code).sms_providers:
            # 伊朗服务商只能发送波斯语
            setting_provider = [SMSProvider(x) for x in v if not (SMSProvider(x) == SMSProvider.PARS_GREEN and lang
                                                                  not in [Language.FA_IR.value])]
            if setting_provider:
                return setting_provider
        weights = SMSProvider.get_provider_weights()
        n = random.random() * sum(weights.values())
        s = 0
        for provider, weight in weights.items():
            s += weight
            if s >= n:
                break
        return [provider]
    
    @classmethod
    def select_providers(cls, country_code: int, lang: str) -> List[SMSProvider]:
        return cls._select_providers(country_code, lang)


@celery_task
def send_sms(country_code: int, mobile: str, text: str, lang: str, providers: List[str] = None, extra: dict = None) -> bool:
    if providers:
        providers = [SMSProvider[x] for x in providers]
    
    res = SMSSender.send(country_code, mobile, text, lang, providers, extra=extra)
    if res:
        SmsSendHourCountCache().incr(country_code, mobile)
    return res

@celery_task
def send_system_sms(content: str, mobile: List[str] = None):
    if not mobile:
        mobile = config['ADMIN_CONTACTS'].get('mobile')
        if not mobile:
            return

    for _mobile in mobile:
        SMSSender.send(
            86,
            _mobile,
            content,
            Language.ZH_HANS_CN.value,
            providers=[SMSProvider.NEXMO]
        )
