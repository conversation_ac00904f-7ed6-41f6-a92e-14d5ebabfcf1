import json
from copy import deepcopy
from datetime import timedelta
from decimal import Decimal
from enum import Enum
from typing import Any

from flask_babel import gettext as _, force_locale

from app import config
from app.business import UserPreferences
from app.business.clients.server import ServerClient
from app.business.email import send_mission_email
from app.business.mission_center.mission import MissionBiz
from app.business.push import send_mobile_push_by_user_ids, get_user_web_lang, get_user_app_lang
from app.caches.mission import Mission<PERSON><PERSON>, SendNoticeCache
from app.common import WebPushChannelType, MessageTitle, NoticePushType, MessageContent, MessageWebLink, \
    WebPushMessageType
from app.models import Message, db
from app.models.equity_center import EquityType
from app.models.mission_center import UserMission
from app.utils import now, url_join, current_timestamp
from app.utils.parser import JsonEncoder
from app.utils.push import AppPagePath, WebPagePath


class SendType(Enum):
    EMAIL = "邮件"
    PUSH = "push"
    SITE_MAIL = "站内信"
    WEB_PUSH = "web_push"


class EmailMixin:
    MISSION_CENTER = "/reward-center"
    REWARD_CENTER = "/reward-center?type=award"

    def get_mission_center_url(self) -> str:
        return url_join(config["SITE_URL"], self.MISSION_CENTER)

    def get_reward_center_url(self) -> str:
        return url_join(config["SITE_URL"], self.REWARD_CENTER)

    @staticmethod
    def format_email_params(user_id, params, fields):
        user_preference = UserPreferences(user_id)
        with force_locale(user_preference.language.value):
            email_params = {**params}
            for field in fields:
                email_params[field] = _(params[field])
        return email_params

    def send_email(self, param: dict[str, Any]):
        user_id = param["user_id"]
        pref = UserPreferences(user_id)
        send_mission_email(
            user_id,
            pref.language.value,
            self.name,
            json.dumps(param, cls=JsonEncoder, ensure_ascii=False)
        )


class SiteMailMixin:
    mail_title: MessageTitle
    mail_content: MessageContent
    web_link: MessageWebLink
    display_type: Message.DisplayType = Message.DisplayType.POPUP_WINDOW
    app_link: str

    def get_mail_title_and_content(self):
        return self.mail_title, self.mail_content

    def set_web_link(self, url):
        self.web_link = url

    def set_app_link(self, url):
        self.app_link = url

    def _create_message(self, user_id: int, params: dict[str, Any]) -> Message:
        title, content = self.get_mail_title_and_content()

        message = Message(
            user_id=user_id,
            title=title,
            content=content,
            params=json.dumps(params, cls=JsonEncoder, ensure_ascii=False),
            extra_info=json.dumps(
                dict(
                    web_link=self.web_link,
                    android_link=self.app_link,
                    ios_link=self.app_link,
                )
            ),
            display_type=self.display_type,
            expired_at=now() + timedelta(days=3),
            channel=Message.Channel.SYSTEM,
        )
        return message

    def send_site_mail(self, param: dict[str, Any]):
        tmp_param = deepcopy(param)
        if "site_url" in tmp_param:
            tmp_param.pop('site_url')
        user_id = tmp_param.pop('user_id')
        message = self._create_message(user_id, tmp_param)
        db.session.add(message)
        db.session.commit()


class PushMixin:
    app_push_url: str

    def send_push(self, params: dict[str, Any], title: str, message: str):
        user_id = params['user_id']
        app_lang = get_user_app_lang(user_id).value
        with force_locale(app_lang):
            _title_for_lang = _(title)
            _message_for_lang = _(message)
            send_mobile_push_by_user_ids.delay(
                user_ids=[user_id],
                content=_message_for_lang,
                title=_title_for_lang,
                ttl=0,
                url=self.app_push_url,
                created_at=current_timestamp(to_int=True),
            )


class WebPushMixin:
    web_push_url: WebPagePath

    def send_wp(self, params: dict[str, Any], title: str, message: str, timing_type: str):
        client = ServerClient()
        user_id = params['user_id']
        web_lang = get_user_web_lang(user_id).value
        with force_locale(web_lang):
            _trs_title = _(title)
            _trs_message = _(message)
        client.notice_user_message(
            user_id,
            WebPushChannelType.REWARD_CENTER.value,
            dict(
                title=_trs_title,
                content=_trs_message,
                url=params['site_url'],
                type=NoticePushType.SUCCESS,
                timing_type=timing_type,
                msg_type=WebPushMessageType.REWARD_CENTER.value
            )
        )


class NewMissionNotice(EmailMixin, PushMixin, WebPushMixin, SiteMailMixin):
    """新手任务推送触达"""
    name = "new_mission_notice"
    mail_title = MessageTitle.NEW_MISSION_NOTICE
    mail_content = MessageContent.NEW_MISSION_NOTICE

    def send_message(self, user_id: int, mission_title: str, title_template: str, message_params: dict):
        params = {
            "user_id": user_id,
            "title": mission_title,
            "site_url": self.get_mission_center_url(),
            'need_translates': [
                {
                    'name': 'title',
                    'text': title_template,
                    'params': message_params,
                    'is_translate': True
                }
            ]
        }

        # Email
        self.send_email(params)

        # Site Mail
        self.set_web_link(MessageWebLink.REWARD_CENTER_PAGE.value)
        self.set_app_link(AppPagePath.REWARD_CENTER.value.format(tab="tasks"))
        self.send_site_mail(params)

        # Push
        push_title = _("请查收新任务")
        push_message = _("%(title)s已上线，快来奖励中心接受挑战吧！请前往「奖励中心」查看详情。", title=mission_title)
        self.app_push_url = AppPagePath.REWARD_CENTER.value.format(tab="tasks")
        self.send_push(params, push_title, push_message)

        # Web Push
        self.send_wp(params, push_title, push_message, self.name)


class MissionRewardSent(EmailMixin, PushMixin, WebPushMixin, SiteMailMixin):
    """新手任务发奖触达"""
    name = "mission_reward_sent"
    mail_title = MessageTitle.MISSION_REWARD_SENT
    mail_content = MessageContent.MISSION_REWARD_SENT

    def send_message(self, user_id: int, mission_title: str, title_template: str, message_params: dict):
        params = {
            "user_id": user_id,
            "title": mission_title,
            "site_url": self.get_reward_center_url(),
            'need_translates': [
                {
                    'name': 'title',
                    'text': title_template,
                    'params': message_params,
                    'is_translate': True
                }
            ]
        }

        # Email
        self.send_email(params)

        # Site Mail
        self.set_web_link(MessageWebLink.REWARD_CENTER_PAGE.value + "?type=award")
        self.set_app_link(AppPagePath.REWARD_CENTER.value.format(tab="rewards"))
        self.send_site_mail(params)

        # Push
        push_title = _("任务奖励发放")
        push_message = _("恭喜，你已完成新用户专属任务！任务名称：%(title)s 请前往「我的奖励」查看详情。",
                         title=mission_title)
        self.app_push_url = AppPagePath.REWARD_CENTER.value.format(tab="rewards")
        self.send_push(params, push_title, push_message)

        # Web Push
        self.send_wp(params, push_title, push_message, self.name)


class MissionExpiringSoon(EmailMixin, PushMixin, WebPushMixin, SiteMailMixin):
    """新手任务到期触达"""
    name = "mission_expiring"
    mail_title = MessageTitle.MISSION_EXPIRING
    mail_content = MessageContent.MISSION_EXPIRING

    def send_message(
            self,
            user_id: int,
            mission_title: str,
            value: Decimal,
            value_type: str,
            reward_type: str,
            title_template: str,
            message_params: dict
    ):
        params = {
            "user_id": user_id,
            "title": mission_title,
            "value": value,
            "value_type": value_type,
            "reward_type": reward_type,
            "site_url": self.get_mission_center_url(),
            'need_translates': [
                {
                    'name': 'title',
                    'text': title_template,
                    'params': message_params,
                    'is_translate': True
                }
            ]
        }

        # Email
        self.send_email(params)

        # Site Mail
        self.set_web_link(MessageWebLink.REWARD_CENTER_PAGE.value)
        self.set_app_link(AppPagePath.REWARD_CENTER.value.format(tab="tasks"))
        self.send_site_mail(params)

        # Push
        push_title = _("任务即将到期")
        push_message = _(
            "你的新用户专属任务即将到期，完成即可获得 %(value)s %(value_type)s%(reward_type)s奖励！请前往「奖励中心」完成任务。",
            value=value,
            value_type=value_type,
            reward_type=reward_type
        )
        self.app_push_url = AppPagePath.REWARD_CENTER.value.format(tab="tasks")
        self.send_push(params, push_title, push_message)

        # Web Push
        self.send_wp(params, push_title, push_message, self.name)


class MissionMessageBiz:

    @classmethod
    def _base_send_message(cls, message_server, user_missions: list[UserMission]):
        mission_ids = [um.mission_id for um in user_missions]
        cache_mapper_data = MissionCache.get_cache_data_by_ids(mission_ids)
        user_ids = {i.user_id for i in user_missions}
        user_pref_mapper = {i: UserPreferences(i) for i in user_ids}
        for user_mission in user_missions:
            cache_data = cache_mapper_data[user_mission.mission_id]
            user_pref = user_pref_mapper[user_mission.user_id]
            with force_locale(user_pref.language.value):
                title = MissionBiz.build_title(cache_data)
                message_server.send_message(
                    user_mission.user_id,
                    title,
                    MissionBiz.get_title_template(cache_data['mission_condition']),
                    MissionBiz.get_build_title_params(cache_data)
                )

    @classmethod
    def send_new_mission_notice(cls, user_missions: list[UserMission]):
        message_server = NewMissionNotice()
        cls._base_send_message(message_server, user_missions)

    @classmethod
    def send_reward_sent_notice(cls, user_missions: list[UserMission]):
        message_server = MissionRewardSent()
        cls._base_send_message(message_server, user_missions)

    @classmethod
    def send_mission_expiring_notice(cls):
        message_server = MissionExpiringSoon()
        notice_cache = SendNoticeCache(message_server.name)
        max_days = 1
        user_missions = UserMission.query.filter(
            UserMission.expired_at <= now() + timedelta(days=max_days)
        ).all()
        mission_ids = [um.mission_id for um in user_missions]
        cache_mapper_data = MissionCache.get_cache_data_by_ids(mission_ids)
        user_ids = {i.user_id for i in user_missions}
        user_pref_mapper = {i: UserPreferences(i) for i in user_ids}
        send_ids = []
        for user_mission in user_missions:
            if notice_cache.has_id(user_mission.id):
                continue
            cache_data = cache_mapper_data[user_mission.mission_id]
            user_pref = user_pref_mapper[user_mission.user_id]
            with force_locale(user_pref.language.value):
                title = MissionBiz.build_title(cache_data)
                reward_type = cache_data['reward']['reward_type']
                message_server.send_message(
                    user_mission.user_id,
                    title,
                    cache_data['reward']['value'],
                    cache_data['reward']['value_type'],
                    _(EquityType[reward_type].value),
                    MissionBiz.get_title_template(cache_data['mission_condition']),
                    MissionBiz.get_build_title_params(cache_data)
                )
            send_ids.append(user_mission.id)
        if send_ids:
            notice_cache.add_ids(send_ids)
