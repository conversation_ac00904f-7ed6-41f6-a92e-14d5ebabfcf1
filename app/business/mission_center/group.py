from datetime import datetime
from typing import Any, Dict, List, Optional, Type, TypeVar, Tu<PERSON>

from sqlalchemy import func

from app.business import <PERSON><PERSON><PERSON><PERSON>, LockKeys
from app.business.mission_center.utils import MissionUtils
from app.caches.mission import MissionContentCache
from app.models import db, User, ReferralHistory
from app.models.mission_center import MissionPlanUserGroup, \
    LogicTemplate, MissionPlan, SceneType, UserMission

T = TypeVar('T')


class MissionGroupBiz:
    """任务分组业务类"""
    model = MissionPlanUserGroup

    @classmethod
    def create_or_update_group(
            cls,
            plan_id: int,
            scene_type: SceneType,
            logic_params: Dict[str, Any],
            logic_template: LogicTemplate,
            registered_at: Optional[datetime] = None
    ) -> MissionPlanUserGroup:
        """创建或更新分组"""
        group = cls.model.query.filter(
            cls.model.plan_id == plan_id
        ).first()
        if not group:
            group = cls.model(
                plan_id=plan_id
            )
        group.scene_type = scene_type
        group.group_type = cls.model.GroupType.LOGIC
        group.logic_params = logic_params
        group.logic_template = logic_template
        group.registered_at = logic_params.get(LogicTemplate.REGISTERED_AT_GE.name) or registered_at
        db.session.add(group)
        return group

    @classmethod
    def query_min_register_time(cls) -> Optional[datetime]:
        """查询最小注册时间"""
        return cls.model.query.join(
            MissionPlan, cls.model.plan_id == MissionPlan.id
        ).filter(
            MissionPlan.status == MissionPlan.Status.EFFECTIVE
        ).with_entities(
            func.min(cls.model.registered_at)
        ).scalar()

    @classmethod
    def get_info(cls, plan_id: int) -> Dict[str, Any]:
        """获取分组信息"""
        plan_group = cls.model.query.filter(
            cls.model.plan_id == plan_id
        ).with_entities(
            cls.model.logic_params,
            cls.model.group_ids,
            cls.model.logic_template
        ).first()
        return {
            "logic_params": plan_group.logic_params,
            "logic_template": plan_group.logic_template,
            "group_ids": plan_group.group_ids
        }

    @classmethod
    def get_group_params_by_ids(cls, plan_ids: List[int]) -> Dict[int, Dict[str, Any]]:
        """通过ID获取分组参数"""
        return {
            plan_id: logic_params for plan_id, logic_params in cls.model.query.filter(
                cls.model.plan_id.in_(plan_ids)
            ).with_entities(
                cls.model.plan_id,
                cls.model.logic_params
            ).all()
        }

    @classmethod
    def query_plan_groups(cls, scene_type: SceneType) -> List[MissionPlanUserGroup]:
        """查询计划分组"""
        from .plan import MissionPlanBiz
        plan_ids = MissionPlanBiz.query_effective_plan_ids(scene_type)
        groups = cls.model.query.filter(
            cls.model.plan_id.in_(plan_ids),
            cls.model.group_type == cls.model.GroupType.LOGIC
        ).all()
        plan_id_order = {plan_id: index for index, plan_id in enumerate(plan_ids)}
        return sorted(groups, key=lambda g: plan_id_order[g.plan_id])

    @classmethod
    def query_display_content_groups(cls, scene_type: SceneType) -> List[MissionPlanUserGroup]:
        from .plan import MissionPlanBiz
        base_groups = cls.query_plan_groups(scene_type)
        finished_display_plans = MissionPlanBiz.query_finished_display_plans()
        finished_groups = MissionPlanUserGroup.query.filter(
            MissionPlanUserGroup.plan_id.in_(finished_display_plans)
        ).all()
        return base_groups + finished_groups


class LogicStrategyRegistry:
    """逻辑策略注册表"""
    _strategies: Dict[LogicTemplate, Type['LogicStrategy']] = {}

    @classmethod
    def register(cls, template: LogicTemplate) -> Type:
        """注册策略装饰器"""

        def decorator(strategy_class: Type['LogicStrategy']) -> Type:
            cls._strategies[template] = strategy_class
            return strategy_class

        return decorator

    @classmethod
    def get_strategy(cls, template: LogicTemplate) -> 'LogicStrategy':
        """获取策略实例"""
        strategy_class = cls._strategies.get(template)
        if not strategy_class:
            raise ValueError(f"Unsupported logic template: {template}")
        return strategy_class()


class LogicStrategy:
    """逻辑策略基类"""
    template: LogicTemplate

    def validate(self, user_data: Dict[str, Any], params: Dict[str, Any]) -> bool:
        """验证用户是否符合条件"""
        raise NotImplementedError

    def query_data_by_users(self, users: List[User]) -> Dict[int, Any]:
        """通过user查询需要数据"""
        raise NotImplementedError


@LogicStrategyRegistry.register(LogicTemplate.CHANNEL_ID_EQ)
class ChannelIdEqStrategy(LogicStrategy):
    """Channel ID 等于指定值策略"""
    template = LogicTemplate.CHANNEL_ID_EQ

    def validate(self, user_data: Dict[str, Any], params: Dict[str, Any]) -> bool:
        """验证用户是否符合条件"""
        check_data = user_data[self.template.name]
        return check_data == params[self.template.name]

    def query_data_by_users(self, users: List[User]) -> Dict[int, str]:
        """查询用户数据"""
        return {u.id: u.channel for u in users}


@LogicStrategyRegistry.register(LogicTemplate.REFERER_ID_IN)
class RefererIdInStrategy(LogicStrategy):
    """推荐人ID 在某个范围内策略"""
    template = LogicTemplate.REFERER_ID_IN

    def validate(self, user_data: Dict[str, Any], params: Dict[str, Any]) -> bool:
        """验证用户是否符合条件"""
        params_value = params[self.template.name]
        user_ids = MissionUtils.update_user_email_id_to_ids(params_value)
        return user_data[self.template.name] in user_ids

    def query_data_by_users(self, users: List[User]) -> Dict[int, int]:
        """查询用户数据"""
        user_ids = [u.id for u in users]
        return {
            ee: er for ee, er in ReferralHistory.query.filter(
                ReferralHistory.referree_id.in_(user_ids),
                ReferralHistory.status == ReferralHistory.Status.VALID
            ).with_entities(
                ReferralHistory.referree_id,
                ReferralHistory.referrer_id
            ).all()
        }


@LogicStrategyRegistry.register(LogicTemplate.REGISTRATION_AREA_IN)
class RegistrationAreaInStrategy(LogicStrategy):
    """注册地区在指定范围内策略"""
    template = LogicTemplate.REGISTRATION_AREA_IN
    DEFAULT_CODE = "DEFAULT"

    def validate(self, user_data: Dict[str, Any], params: Dict[str, Any]) -> bool:
        """验证用户是否符合条件"""
        user_area = user_data.get(self.template.name, "")
        if not (params_value := params[self.template.name]):
            return True
        return user_area in params_value

    def query_data_by_users(self, users: List[User]) -> Dict[int, str]:
        """查询用户数据"""
        return {
            u.id: u.location_code or self.DEFAULT_CODE for u in users
        }


@LogicStrategyRegistry.register(LogicTemplate.REGISTERED_AT_GE)
class RegisteredAtGeStrategy(LogicStrategy):
    """注册时间大于等于指定时间策略"""
    template = LogicTemplate.REGISTERED_AT_GE

    def validate(self, user_data: Dict[str, Any], params: Dict[str, Any]) -> bool:
        """验证用户是否符合条件"""
        return user_data[self.template.name] >= params[self.template.name]

    def query_data_by_users(self, users: List[User]) -> Dict[int, datetime]:
        """查询用户数据"""
        return {
            u.id: u.created_at for u in users
        }


class UserGroupFilter:
    """用户分组过滤器"""

    def __init__(self, users: List[User], group: MissionPlanUserGroup):
        self._users = users
        self._logic_params = group.check_logic_params

    def _query_user_data(self) -> Dict[int, Dict[str, Any]]:
        """查询用户数据"""
        data_mappers = {}
        for lt in self._logic_params.keys():
            handler = LogicStrategyRegistry.get_strategy(LogicTemplate[lt])
            data_mappers[lt] = handler.query_data_by_users(self._users)

        result = {}
        for user in self._users:
            user_info = {}
            for t, mapper in data_mappers.items():
                if value := mapper.get(user.id):
                    user_info[t] = value
            if user_info:
                result[user.id] = user_info
        return result

    def _match_data(self, user_data: Dict[str, Any]) -> bool:
        """匹配数据"""
        result = []
        for tem in self._logic_params.keys():
            if tem not in user_data:
                result.append(False)
                continue
            handler = LogicStrategyRegistry.get_strategy(LogicTemplate[tem])
            result.append(handler.validate(user_data, self._logic_params))
        if not result:
            return False
        return all(result)

    def domain(self) -> List[User]:
        """获取符合条件的用户"""
        user_data_mapper = self._query_user_data()
        group_users = []
        for u in self._users:
            user_data = user_data_mapper.get(u.id, {})
            if not self._match_data(user_data) or not user_data:
                continue
            group_users.append(u)
        return group_users


class GroupMissionProcessor:
    """分组任务处理器"""

    def __init__(self, scene_type: SceneType):
        self.scene_type = scene_type

    def match_users_to_groups(
            self,
            users: List[User],
            groups: Optional[List[MissionPlanUserGroup]] = None
    ) -> Dict[MissionPlanUserGroup, List[User]]:
        """将用户匹配到对应的分组

        Args:
            users: 待匹配的用户列表
            groups: 可选的分组列表，如果为None则查询所有有效分组

        Returns:
            Dict[MissionPlanUserGroup, List[User]]: 分组到用户的映射
        """
        if not groups:
            groups = MissionGroupBiz.query_plan_groups(self.scene_type)

        group_users_mapper = {}
        remaining_users = set(users)

        for group in groups:
            if not remaining_users:
                break

            filter_users = list(remaining_users)
            group_users = UserGroupFilter(group=group, users=filter_users).domain()

            if group_users:
                group_users_mapper[group] = group_users
                remaining_users -= set(group_users)

        return group_users_mapper

    def create_missions_for_groups(
            self,
            group_users_mapper: Dict[MissionPlanUserGroup, List[User]]
    ) -> Tuple[List[UserMission], List[int]]:
        """为分组创建任务

        Args:
            group_users_mapper: 分组到用户的映射

        Returns:
            Tuple[List[UserMission], List[int]]: (创建的用户任务列表, 配额已满的计划ID列表)
        """
        from .mission import UserMissionBiz

        user_mission_objs = []
        finished_plan_ids = []

        for group, users in group_users_mapper.items():
            new_user_mission_objs, quota_reached = UserMissionBiz.add_user_mission(
                self.scene_type,
                group,
                users
            )

            if new_user_mission_objs:
                user_mission_objs.extend(new_user_mission_objs)
            if quota_reached:
                finished_plan_ids.append(group.plan_id)

        return user_mission_objs, finished_plan_ids

    def process_users(self, users: List[User]) -> None:
        """处理用户分组和任务创建
        
        Args:
            users: 待处理的用户列表
        """
        from .plan import MissionPlanBiz

        user_ids = [u.id for u in users]

        with CacheLock(LockKeys.mission_group_user(), wait=False):
            db.session.rollback()
            # 1. 获取原始用户数据
            origin_users = User.query.filter(User.id.in_(user_ids)).all()

            # 2. 匹配用户到分组
            group_users_mapper = self.match_users_to_groups(origin_users)

            # 3. 创建任务
            user_mission_objs, finished_plan_ids = self.create_missions_for_groups(
                group_users_mapper
            )

            # 4. 保存数据
            if user_mission_objs:
                db.session.bulk_save_objects(user_mission_objs)
            if finished_plan_ids:
                MissionPlanBiz.finish(finished_plan_ids)
            db.session.commit()

            # 5. 更新缓存
            if finished_plan_ids:
                MissionContentCache.reload()


class UserParamMatcher:
    """用户参数匹配器"""

    def __init__(self, scene_type: SceneType, user_params: Dict[str, Any]):
        self.scene_type = scene_type
        self.user_params = user_params

    @classmethod
    def _match_user_data(cls, logic_params: Dict[str, Any], user_info: Dict[str, Any]) -> bool:
        """匹配用户数据"""
        result = []
        for tem in logic_params.keys():
            if tem not in user_info:
                result.append(False)
                continue
            handler = LogicStrategyRegistry.get_strategy(LogicTemplate[tem])
            result.append(handler.validate(user_info, logic_params))
        if not result:
            return False
        return all(result)

    def find_matching_group(self) -> Optional[MissionPlanUserGroup]:
        """根据用户参数查找匹配的分组
        Returns:
            Optional[MissionPlanUserGroup]: 匹配到的分组，如果没有匹配到则返回None
        """
        groups = MissionGroupBiz.query_plan_groups(self.scene_type)
        return self.find_matching_group_by_groups(groups)

    def find_matching_group_by_groups(self, groups: list[MissionPlanUserGroup]) -> Optional[MissionPlanUserGroup]:
        # 1. 遍历分组进行匹配
        for group in groups:
            # 检查分组是否支持当前用户参数
            if not self._check_group_support_params(group):
                continue

            # 匹配用户参数
            if self._match_user_data(group.check_logic_params, self.user_params):
                return group

        return None

    def _check_group_support_params(self, group: MissionPlanUserGroup) -> bool:
        """检查分组是否支持给定的用户参数
        
        Args:
            group: 分组对象
            
        Returns:
            bool: 是否支持
        """
        # 检查必要的参数是否存在
        required_params = set(group.check_logic_params.keys())
        available_params = set(self.user_params.keys())

        # 如果用户参数中缺少必要的参数，则不支持
        if not required_params.issubset(available_params):
            return False

        return True


class LogicGroupValidator:
    """任务中心圈群逻辑"""
    group_model = MissionPlanUserGroup

    def __init__(self, scene_type: SceneType):
        self.scene_type = scene_type
        self._processor = GroupMissionProcessor(scene_type)

    def domain(self, users: List[User]) -> None:
        """批量处理用户分组
        保证用户任务唯一性的保证:
        1. 用户分组的时候使用余量分组保证，保证分组的时候用户不会被分到多个组中。 code: GroupUserMatcher.match_users_to_groups 来保证
        2. 创建用户任务的时候，检查用户是否已经存在任务，如果存在则不再创建。 code: UserMissionBiz.add_user_mission 来保证
        Args:
            users: 待处理的用户列表

        """
        self._processor.process_users(users)

    def find_matching_group(self, user_params: Dict[str, Any]) -> Optional[MissionPlanUserGroup]:
        """根据用户参数查找匹配的分组
        
        Args:
            user_params: 用户参数，包含IP、时区、设备信息等
            
        Returns:
            Optional[MissionPlanUserGroup]: 匹配到的分组，如果没有匹配到则返回None
        """
        matcher = UserParamMatcher(self.scene_type, user_params)
        return matcher.find_matching_group()
