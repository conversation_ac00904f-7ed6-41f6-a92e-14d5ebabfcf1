from collections import defaultdict
from datetime import date, timedelta

from sqlalchemy import func

from app.business.equity_center.helper import EquityCenterService
from app.models.equity_center import UserEquity
from app.models.mission_center import UserMission, DailyMissionStatistics, db


class MissionStatisticsBiz:

    @classmethod
    def get_plan_statistics_last_date(cls, plan_id: int):
        return DailyMissionStatistics.query.filter(
            DailyMissionStatistics.plan_id == plan_id
        ).with_entities(
            func.max(DailyMissionStatistics.report_date)
        ).scalar()

    def __init__(self, plan_id: int, report_date: date):
        self.plan_id = plan_id
        self.start_date = report_date
        self.end_date = report_date + timedelta(days=1)

    def statistics(self):
        ums = UserMission.query.filter(
            UserMission.plan_id == self.plan_id,
            UserMission.used_at >= self.start_date,
            UserMission.used_at < self.end_date
        ).all()
        if not ums:
            return
        statistics_data = defaultdict(lambda: {
            'delivery_count': 0,
            'completion_count': 0,
            'finished_count': 0,
            'real_reward_amount': 0,
            'reward_amount': 0,
        })
        biz_ids = {i.id for i in ums}
        equity_data_mapper = EquityCenterService.batch_query_user_eq_info(EquityCenterService.BizTypes.MISSION, biz_ids)
        for um in ums:
            equity_data = equity_data_mapper.get(um.id, {})
            # 推送失败不统计
            if um.status == UserMission.Status.FAILED:
                continue
            statistics_data[um.mission_id]['delivery_count'] += 1

            if equity_data:
                statistics_data[um.mission_id]['real_reward_amount'] += equity_data['real_amount']
                statistics_data[um.mission_id]['reward_amount'] += equity_data['cost_amount']

            if um.status == UserMission.Status.FINISHED and um.completed_at:
                statistics_data[um.mission_id]['completion_count'] += 1
                # 如果权益发放失败，则不统计已完成用户数
                if not equity_data:
                    continue
                if equity_data['status'] == UserEquity.Status.FAILED:
                    continue
                statistics_data[um.mission_id]['finished_count'] += 1

        for mission_id, data in statistics_data.items():
            statistics = DailyMissionStatistics.get_or_create(
                report_date=self.start_date,
                plan_id=self.plan_id,
                mission_id=mission_id
            )
            statistics.delivery_count = data['delivery_count']
            statistics.completion_count = data['completion_count']
            statistics.finished_count = data['finished_count']
            statistics.real_reward_amount = data['real_reward_amount']
            statistics.reward_amount = data['reward_amount']
            db.session.add(statistics)
        db.session.commit()
