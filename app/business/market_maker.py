#!/usr/bin/python
# -*- coding: utf-8 -*-
import calendar
import datetime
from decimal import Decimal
from typing import Dict, List, Set, Union
from flask_babel import gettext as _
from sqlalchemy import func

from app.models import (
    MarketMaker, UserTradeSummary, User, SubAccount
)
from app.models import db
from app.caches import InnerMakersCache
from app.utils import now, last_month
from .fee_constant import (
    PERPETUAL_MARKET_MAKER_DICT,
    MARKET_MAKER_DICT,
)
from .fee import update_user_fee_task


# 内部保护机制
# 在头部做市商(LV4, LV5)不足25人时，补足人数(LV4 15人，LV5 10人)
MARKET_MAKER_HIGH_LEVEL_MAP = {
    4: 15,
    5: 10
}

SPOT_MARKET_MAKER_APPRAISAL_MAP = {
    1: {
        "ratio": Decimal("1"),
        "ranking": _("末尾30%")
    },
    2: {
        "ratio": Decimal("0.7"),
        "ranking": '≤70%'
    },
    3: {
        "ratio": Decimal("0.45"),
        "ranking": '≤45%'
    },
    4: {
        "ratio": Decimal("0.25"),
        "ranking": '≤25%'
    },
    5: {
        "ratio": Decimal("0.1"),
        "ranking": '≤10%'
    }
}

PERPETUAL_MARKET_MAKER_APPRAISAL_MAP = {
    1: {
        "ratio": Decimal("1"),
        "ranking": _("末尾30%")
    },
    2: {
        "ratio": Decimal("0.7"),
        "ranking": '≤70%'
    },
    3: {
        "ratio": Decimal("0.45"),
        "ranking": '≤45%'
    },
    4: {
        "ratio": Decimal("0.25"),
        "ranking": '≤25%'
    },
    5: {
        "ratio": Decimal("0.1"),
        "ranking": '≤10%'
    }
}

MIN_SPOT_MARKET_MAKER_RANKING_AMOUNT = Decimal('1000000')
MIN_PERPETUAL_MARKET_MAKER_RANKING_AMOUNT = Decimal('5000000')


class MarketMakerHelper(object):

    def __init__(self, maker_type: MarketMaker.MakerType):
        self.maker_type = maker_type

    def get_maker_trade_amount_limit(self):
        if self.maker_type == MarketMaker.MakerType.SPOT:
            return MARKET_MAKER_DICT[1]['trade_amount']
        if self.maker_type == MarketMaker.MakerType.PERPETUAL:
            return PERPETUAL_MARKET_MAKER_DICT[1]['trade_amount']

    @classmethod
    def check_market_maker_type(cls, user_id: int):
        query = MarketMaker.query.filter(
            MarketMaker.user_id == user_id,
            MarketMaker.status == MarketMaker.StatusType.PASS
        )
        result = {
            MarketMaker.MakerType.SPOT: False,
            MarketMaker.MakerType.PERPETUAL: False,
        }
        for v in query:
            result[v.maker_type] = True
        return result

    @classmethod
    def get_user_new_maker_type(cls,
                                maker_status_dict: Dict) -> User.UserType:
        if all(maker_status_dict.values()):
            return User.UserType.EXTERNAL_MAKER
        for key, value in maker_status_dict.items():
            if not value:
                continue
            if key == MarketMaker.MakerType.SPOT:
                return User.UserType.EXTERNAL_SPOT_MAKER
            if key == MarketMaker.MakerType.PERPETUAL:
                return User.UserType.EXTERNAL_CONTRACT_MAKER
        return User.UserType.NORMAL

    @classmethod
    def change_user_type(cls,
                         user_id: int,
                         user_type: User.UserType):
        user = User.query.filter(
            User.id == user_id
        ).first()
        if user.user_type == user_type:
            return
        user.user_type = user_type
        db.session.commit()

    def add(self,
            user_id: int,
            level: int = 0,
            overwrite_level: bool = True,
            update_fee: bool = True,
            ):
        market_status = self.check_market_maker_type(user_id)

        if not market_status[self.maker_type]:
            mmer = MarketMaker.query.filter(
                MarketMaker.user_id == user_id,
                MarketMaker.maker_type == self.maker_type
            ).first()
            if not mmer:
                db.session.add(
                    MarketMaker(
                        user_id=user_id,
                        level=level,
                        maker_type=self.maker_type,
                        status=MarketMaker.StatusType.PASS
                    )
                )
            else:
                mmer.status = MarketMaker.StatusType.PASS
                if overwrite_level:
                    mmer.level = level
            market_status[self.maker_type] = True
            db.session.commit()
            user_type = self.get_user_new_maker_type(market_status)
            self.change_user_type(user_id, user_type)
            if update_fee:
                update_user_fee_task(user_id)

    def get_level(self, user_id: int):
        m = MarketMaker.query.filter(MarketMaker.user_id == user_id,
                                     MarketMaker.status ==
                                     MarketMaker.StatusType.PASS,
                                     MarketMaker.maker_type == self.maker_type
                                     ).first()
        if not m:
            return
        return m.level

    def delete(self, user_id: int, update_fee: bool = True):
        market_status = self.check_market_maker_type(user_id)
        m = MarketMaker.query.filter(
                MarketMaker.status == MarketMaker.StatusType.PASS,
                MarketMaker.user_id == user_id,
                MarketMaker.maker_type == self.maker_type
                ).first()
        if m:
            m.status = MarketMaker.StatusType.DELETE
            db.session.commit()
            market_status[m.maker_type] = False
            user_type = self.get_user_new_maker_type(market_status)
            self.change_user_type(user_id, user_type)
            if update_fee:
                update_user_fee_task(user_id)

    def is_exists(self, user_id: int):
        m = MarketMaker.query.filter(
                MarketMaker.status == MarketMaker.StatusType.PASS,
                MarketMaker.user_id == user_id,
                MarketMaker.maker_type == self.maker_type
                ).first()
        if m:
            return True
        return False


    @classmethod
    def get_trade_amount_by_user_date(cls, user_id,
                                      start_date: Union[datetime.date, datetime.datetime],
                                      end_date: Union[datetime.date, datetime.datetime],
                                      system: UserTradeSummary.System = UserTradeSummary.System.SPOT):
        trade_summary = UserTradeSummary.query.filter(
            UserTradeSummary.system == system,
            UserTradeSummary.report_date <= end_date,
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.user_id == user_id,
        ).with_entities(
            func.sum(UserTradeSummary.trade_amount).label('trade_amount')
        ).first()
        if trade_summary:
            return trade_summary.trade_amount or Decimal()
        return Decimal()

    @classmethod
    def get_trade_amount(cls,
                         user_id,
                         system: UserTradeSummary.System = UserTradeSummary.System.SPOT):
        date = now().date()
        return cls.get_trade_amount_by_user_date(user_id,
                                                 date - datetime.timedelta(days=30),
                                                 date,
                                                 system)

    @classmethod
    def get_last_month_trade_amount(cls,
                                    user_id,
                                    system: UserTradeSummary.System =
                                    UserTradeSummary.System.SPOT):
        today = now().date()
        last_month_first_day = last_month(today.year, today.month, 1)
        days = calendar.monthrange(last_month_first_day.year, last_month_first_day.month)[1]
        last_month_end_day = datetime.date(last_month_first_day.year, last_month_first_day.month, days)
        trade_summary = UserTradeSummary.query.filter(
                UserTradeSummary.system == system,
                UserTradeSummary.report_date <= last_month_end_day,
                UserTradeSummary.report_date >= last_month_first_day,
                UserTradeSummary.user_id == user_id,
                ).with_entities(
                func.sum(UserTradeSummary.trade_amount).label('trade_amount')
                ).first()
        if trade_summary:
            return trade_summary.trade_amount or Decimal()
        return Decimal()

    @classmethod
    def list_trade_amount(cls,
                          user_ids: List,
                          start_date: datetime.date,
                          end_date: datetime.date,
                          system:
                          UserTradeSummary.System
                          = UserTradeSummary.System.SPOT):

        trade_summary = UserTradeSummary.query.filter(
                UserTradeSummary.system == system,
                UserTradeSummary.user_id.in_(user_ids),
                UserTradeSummary.report_date <= end_date,
                UserTradeSummary.report_date >= start_date
                ).with_entities(
                UserTradeSummary.user_id,
                func.sum(UserTradeSummary.trade_amount).label('trade_amount')
                ).group_by(
                UserTradeSummary.user_id
                )

        return {item.user_id: item.trade_amount for item in trade_summary}

    def list_market_level(self):
        if self.maker_type == MarketMaker.MakerType.SPOT:
            return sorted([level for level in MARKET_MAKER_DICT])
        if self.maker_type == MarketMaker.MakerType.PERPETUAL:
            return sorted([level for level in PERPETUAL_MARKET_MAKER_DICT])

    def list_lock_maker(self) -> Dict[int, int]:
        return {v.user_id: v.lock_level for v in MarketMaker.query.filter(
                MarketMaker.status == MarketMaker.StatusType.PASS,
                MarketMaker.is_lock.is_(True),
                MarketMaker.maker_type == self.maker_type,
                MarketMaker.expired_time > now())}

    def list_lock_level_expired_maker(self) -> Set[int]:
        return {v.user_id for v in MarketMaker.query.filter(
                MarketMaker.status == MarketMaker.StatusType.PASS,
                MarketMaker.is_lock.is_(True),
                MarketMaker.maker_type == self.maker_type,
                MarketMaker.expired_time < now())}

    @classmethod
    def list_inner_maker_ids(cls, include_sub_account: bool=True):
        user_ids = InnerMakersCache().get_inner_makers()
        if include_sub_account:
            user_ids += cls._get_sub_accounts(user_ids)
        return user_ids

    @classmethod
    def _get_sub_accounts(cls, user_ids):
        sub_users = SubAccount.query.filter(
            SubAccount.main_user_id.in_(user_ids),
            SubAccount.type == SubAccount.Type.NORMAL,
        ).with_entities(SubAccount.user_id).all()
        return [x for x, in sub_users]

    @classmethod
    def list_all_maker_ids(cls, include_sub_account: bool = True):
        rows = MarketMaker.query.filter(
            MarketMaker.status == MarketMaker.StatusType.PASS
            ).with_entities(MarketMaker.user_id).all()
        makers = [r.user_id for r in rows]
        inner_makers = InnerMakersCache().get_inner_makers()
        user_ids = list(set(makers + inner_makers))
        if include_sub_account:
            user_ids += cls._get_sub_accounts(user_ids)
        return user_ids
