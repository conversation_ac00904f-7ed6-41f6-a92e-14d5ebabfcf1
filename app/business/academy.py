import json
import re
from enum import Enum

import math
from collections import defaultdict
from decimal import Decimal

from bs4 import BeautifulSoup

from app import Language
from app.caches.inverted_index import AcademyArticleInvertedIndexCache, AcademyArticleInvertedIndexData
from app.caches.operation import AcademyTagToArticleCache, AcademyArticleIndexCache, AcademyCoinToArticleCache
from app.models.academy import AcademyArticle, AcademyArticleContent, AcademyCategory, AcademyTag, \
    AcademyCategoryContent
from app.utils import batch_iter, cut_words, filter_stop_words


class AcademySearchBusiness:
    MAX_SEARCH_KEYS = 50

    @classmethod
    def search_academy_by(
            cls,
            lang: Language,
            last_id: int,
            limit: int,
            search_key: str,
            tag_id: int = None,
    ):
        if not search_key:
            return to_list_result([], 0, False, 0, participles=[])

        search_keys = cut_words(lang, search_key)
        if not search_keys:
            return to_list_result([], 0, False, 0, participles=[])

        result = cls.search_by_inverted_index(
            lang,
            last_id,
            limit,
            search_keys,
            tag_id=tag_id,
        )
        result['participles'] = search_keys
        return result

    @classmethod
    def search_by_inverted_index(
            cls,
            lang: Language,
            last_id: int,
            limit: int,
            search_keys: list[str],
            tag_id: int = None,
    ):
        if len(search_keys) > cls.MAX_SEARCH_KEYS:
            # 分词结果超过最大限制时，先过滤停用词后只保留前若干个分词结果用于查询
            search_keys = filter_stop_words(lang, search_keys)[:cls.MAX_SEARCH_KEYS]
        search_keys = [item.lower() for item in search_keys]

        words_map = defaultdict(lambda: defaultdict(Decimal))
        for word, items in AcademyArticleInvertedIndexCache(AcademyArticleInvertedIndexData, lang).get(
                search_keys,
        ).items():
            for item in items:
                tf_idf = item['tf_idf']
                words_map[item['article_id']][word] = tf_idf
        tf_idf_map = {
            _id: sum(word_map.values())
            for _id, word_map in words_map.items()
        }
        sorted_article_ids = [_id for _id, _ in sorted(tf_idf_map.items(), key=lambda x: (x[1]), reverse=True)]
        if tag_id:
            tag_mapping = AcademyTagToArticleCache().read_aside()
            tag_article_ids = tag_mapping.get(tag_id, [])
            match_id_set = set(sorted_article_ids)
            content_article_id_set = {i['article_id'] for i in AcademyArticleIndexCache.read_aside(lang)}
            f_tag_article_ids = [i for i in tag_article_ids if i not in match_id_set and i in content_article_id_set]
            sorted_article_ids.extend(f_tag_article_ids)

        start_flag = True if (not last_id or last_id == 0) else False
        result_ids = []
        traverse_count = 0
        for _id in sorted_article_ids:
            traverse_count += 1
            if _id == last_id:
                start_flag = True
                continue
            if start_flag:
                result_ids.append(_id)
            if len(result_ids) >= limit:
                break

        model = AcademyArticle
        article_rows = model.query.with_entities(
            model.id,
            model.primary_category_id,
            model.secondary_category_id,
            model.seo_url_keyword,
            model.published_at,
        ).filter(
            model.id.in_(result_ids)
        ).all()
        contents = []
        c_model = AcademyArticleContent
        [contents.append(item) for item in c_model.query.with_entities(
            c_model.article_id,
            c_model.lang,
            c_model.title,
            c_model.abstract,
            c_model.content,
        ).filter(
            c_model.article_id.in_(result_ids),
            c_model.lang == lang,
        ).all()]
        missing_ids = set(result_ids) - {c.article_id for c in contents}
        if missing_ids:
            to_translated = cls._get_need_translated_article_ids(
                {row.id: (row.primary_category_id, row.secondary_category_id) for row in article_rows}
            )
            fetch_fk_ids = missing_ids & to_translated
            if fetch_fk_ids:
                [contents.append(item) for item in c_model.query.with_entities(
                    c_model.article_id,
                    c_model.lang,
                    c_model.title,
                    c_model.abstract,
                    c_model.content,
                ).filter(
                    c_model.article_id.in_(fetch_fk_ids),
                    c_model.lang == Language.EN_US,
                ).all()]
        article_map = {item.id: item for item in article_rows}
        category_ids = set()
        [category_ids.update(
            [item.primary_category_id, item.secondary_category_id]
            if item.secondary_category_id else [item.primary_category_id]
        ) for item in article_rows]
        category_names = cls.get_category_names_by(lang, category_ids=category_ids)
        items = []
        content_map = {content.article_id: content for content in contents}
        for _id in result_ids:
            content = content_map.get(_id)
            article = article_map[content.article_id]
            items.append(cls.get_content_list_item(article, content, category_names))
        total = len(sorted_article_ids)
        has_next = True if traverse_count < total else False
        new_last_id = result_ids[-1] if len(result_ids) > 0 else 0
        return to_list_result(items, total, has_next, new_last_id)

    @classmethod
    def get_category_names_by(cls, lang: Language, category_ids: set[int]) -> dict:
        model = AcademyCategoryContent
        rows = model.query.with_entities(
            model.category_id,
            model.name
        ).filter(
            model.lang == lang,
            model.category_id.in_(category_ids)
        ).all()
        ret: dict[int, str] = dict(rows)
        missing_category_ids = set(category_ids) - set(ret.keys())
        if missing_category_ids:
            en_rows = model.query.with_entities(
                model.category_id,
                model.name
            ).filter(
                model.lang == Language.EN_US,
                model.category_id.in_(missing_category_ids)
            ).all()
            ret.update(dict(en_rows))
        return ret

    @classmethod
    def get_content_list_item(
            cls,
            article: AcademyArticle,
            content: AcademyArticleContent,
            category_names: dict
    ) -> dict:
        return {
            'id': article.id,  # 与前端交互的id为主表的id
            'primary_category_id': article.primary_category_id,
            'primary_category_name': category_names[article.primary_category_id],
            'secondary_category_id': article.secondary_category_id,
            'secondary_category_name': category_names[article.secondary_category_id] if article.secondary_category_id else None,
            'seo_url_keyword': article.seo_url_keyword,
            'published_at': article.published_at,
            'title': content.title,
            'abstract': content.abstract.replace('\n', '').replace('\r', ''),
        }

    @classmethod
    def update_all_inverted_index_cache(cls):
        articles = cls._get_articles()
        group_by_fk_id = defaultdict(list)
        c_model = AcademyArticleContent
        for chunk_ids in batch_iter(articles.keys(), 1000):
            contents = c_model.query.with_entities(
                c_model.article_id,
                c_model.lang,
                c_model.title_participles,
                c_model.content_participles,
            ).filter(
                c_model.article_id.in_(chunk_ids)
            ).all()
            for c in contents:
                group_by_fk_id[c.article_id].append(c)

        id_to_en_us = {}
        to_translated = cls._get_need_translated_article_ids(articles)
        for article_id in to_translated:
            en_content_dict = {c.article_id: c for c in group_by_fk_id[article_id] if c.lang is Language.EN_US}
            id_to_en_us.update(en_content_dict)
        langs = {lang for lang in Language}
        group_by_lang = defaultdict(list)
        for fk_id, items in group_by_fk_id.items():
            for item in items:
                group_by_lang[item.lang].append(item)
            if fk_id in to_translated:
                en_us_item = id_to_en_us[fk_id]
                exist_langs = {item.lang for item in items}
                for lang in langs - exist_langs:
                    group_by_lang[lang].append(en_us_item)

        for lang, items in group_by_lang.items():
            cls.update_lang_inverted_index_cache(lang, items)

    @classmethod
    def _get_articles(cls) -> dict:
        model = AcademyArticle
        rows = model.query.with_entities(
            model.id,
            model.primary_category_id,
            model.secondary_category_id,
        ).filter(
            model.status == model.Status.PUBLISHED,
        ).all()
        # 要求类别启用的文章
        enabled_cate_ids = cls._get_enabled_category_ids()
        ret = {}
        for row in rows:
            if row.primary_category_id not in enabled_cate_ids:
                continue
            if row.secondary_category_id:
                if row.secondary_category_id not in enabled_cate_ids:
                    continue
            ret[row.id] = (row.primary_category_id, row.secondary_category_id)
        return ret

    @classmethod
    def _get_enabled_category_ids(cls) -> set[int]:
        model = AcademyCategory
        rows = model.query.with_entities(
            model.id
        ).filter(
            model.status == model.Status.VALID,
            model.enabled.is_(True)
        ).all()
        return {row.id for row in rows}

    @classmethod
    def _get_need_translated_article_ids(cls, articles: dict) -> set:
        category_ids = cls._get_category_id_to_translate()
        ret = set()
        for article_id, value in articles.items():
            if value[0] in category_ids:
                ret.add(article_id)
        return ret

    @classmethod
    def _get_category_id_to_translate(cls) -> set:
        # 专业术语 sort_id = -1，若未配置其他语言，则使用英文配置
        category_id = AcademyCategory.get_term_category_id()
        return {category_id} if category_id else set()

    @classmethod
    def update_lang_inverted_index_cache(cls, lang: Language, items: list['AcademyArticleContent']):
        if (total := len(items)) == 0:
            return
        word_map = defaultdict(list)
        for item in items:
            title_participles = json.loads(item.title_participles) if item.title_participles else []
            content_participles = json.loads(item.content_participles) if item.content_participles else []
            if (total_word_count := len(title_participles) + len(content_participles)) == 0:
                continue
            words = defaultdict(lambda: {
                'count': 0,
            })
            for word in AcademyArticleContent(title_participles=item.title_participles).get_title_participles():
                word_obj = words[word.lower()]
                word_obj['count'] += 1
            for word in AcademyArticleContent(content_participles=item.content_participles).get_content_participles():
                word_obj = words[word.lower()]
                word_obj['count'] += 1
            for word, data in words.items():
                word_map[word].append({
                    'article_id': item.article_id,
                    'tf': Decimal(data['count'] / total_word_count),
                })
        for _, items in word_map.items():
            idf = AcademyArticleInvertedIndexCache.calculate_idf(total, len(items))
            for item in items:
                item['tf_idf'] = item['tf'] * idf

        # 之前的keys对比新的keys计算出需要删除的key
        index_cache = AcademyArticleInvertedIndexCache(AcademyArticleInvertedIndexData, lang)
        del_keys = set(index_cache.hkeys()) - set(word_map.keys())

        index_cache.update(word_map)

        if len(del_keys) > 0:
            index_cache.hdel(*del_keys)


class AcademyBusiness:
    DEFAULT_WPM = 275
    # 各语言平均分钟阅读速度
    WPM_MAPPING = {
        Language.ZH_HANS_CN: (300, 500),  # min_wpm, max_wpm
        Language.EN_US: (200, 300),
        Language.ZH_HANT_HK: (300, 500),
        Language.JA_JP: (400, 600),
        Language.RU_KZ: (180, 240),
        Language.KO_KP: (350, 450),
        Language.ID_ID: (250, 350),
        Language.TR_TR: (220, 300),
        Language.VI_VN: (300, 400),
        Language.FR_FR: (220, 300),
        Language.PT_PT: (200, 280),
        Language.DE_DE: (180, 260),
        Language.AR_AE: (150, 220),
        Language.ES_ES: (250, 350),
        Language.FA_IR: (200, 280),
        Language.TH_TH: (300, 400),
        Language.IT_IT: (240, 320),
        Language.PL_PL: (200, 280),
    }

    class WordBoundary(Enum):
        WORD_SEG = '空格分词'
        VISUAL_CHAR = '可视字符'  # 根据语言不同，有字符范围概念

    DEFAULT_BOUNDARY = WordBoundary.WORD_SEG
    WORD_BOUNDARY = {
        Language.ZH_HANS_CN: WordBoundary.VISUAL_CHAR,
        Language.ZH_HANT_HK: WordBoundary.VISUAL_CHAR,
        Language.JA_JP: WordBoundary.VISUAL_CHAR,
        Language.TH_TH: WordBoundary.VISUAL_CHAR,
        Language.KO_KP: WordBoundary.VISUAL_CHAR,

        Language.EN_US: WordBoundary.WORD_SEG,
        Language.FR_FR: WordBoundary.WORD_SEG,
        Language.DE_DE: WordBoundary.WORD_SEG,
        Language.AR_AE: WordBoundary.WORD_SEG,
        Language.ES_ES: WordBoundary.WORD_SEG,
        Language.FA_IR: WordBoundary.WORD_SEG,
        Language.PT_PT: WordBoundary.WORD_SEG,
        Language.PL_PL: WordBoundary.WORD_SEG,
        Language.ID_ID: WordBoundary.WORD_SEG,
        Language.RU_KZ: WordBoundary.WORD_SEG,
        Language.VI_VN: WordBoundary.WORD_SEG,
        Language.IT_IT: WordBoundary.WORD_SEG,
    }
    PATTERN_CN = r'[\u4e00-\u9fff]'
    PATTERN_HK = PATTERN_CN
    PATTERN_JP = r'[\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff]'
    PATTERN_KP = r'[\uac00-\ud7af\u1100-\u11ff]'
    # PATTERN_VN = r'\u0102-\u01b0'  # wrong
    PATTERN_TH = r'[\u0e00-\u0e7f]'
    # PATTERN_EN = r'\b[a-zA-Z0-9-]+\b'
    # 缺陷：匹配的是字母串，不是语义上的英文单词。
    # 不能识别缩写、带撇号词等。例如 "don’t"、"U.S.A."、"e-mail"。匹配结果会是 ['don', 't'], ['U', 'S', 'A'], ['e', 'mail']
    # 忽略上下文和语言规则："这是个变量abcde"。它会把 abcde 当成英文单词，但其实这只是个变量名或缩写。
    PATTERN_EN = r'[a-zA-Z]+'  # used for mixed lang only
    DEF_PATTERNS = {
        Language.ZH_HANS_CN: PATTERN_CN,
        Language.ZH_HANT_HK: PATTERN_HK,
        Language.JA_JP: PATTERN_JP,
        # Language.VI_VN: PATTERN_VN,
        Language.TH_TH: PATTERN_TH,
        Language.KO_KP: PATTERN_KP,
    }

    @classmethod
    def update_coin_to_article_data(cls):
        """更新关联币种 -> 文章映射关系"""
        ret = defaultdict(list)
        for row in cls._get_articles():
            for coin in AcademyArticle(coins=row.coins).get_coins():
                ret[coin].append(row.id)
        cache = AcademyCoinToArticleCache()
        ret = {k: json.dumps(v) for k, v in ret.items()}
        cache.save(ret)

    @classmethod
    def update_tag_to_article_data(cls):
        tags = cls._get_enabled_tags()
        ret = defaultdict(list)
        for row in cls._get_articles():
            for tag_id in AcademyArticle(tag_ids=row.tag_ids).get_tag_ids():
                if tag_id not in tags:
                    continue
                ret[tag_id].append(row.id)
        cache = AcademyTagToArticleCache()
        ret = {k: json.dumps(v) for k, v in ret.items()}
        cache.save(ret)

    @classmethod
    def _get_enabled_tags(cls) -> set:
        model = AcademyTag
        rows = model.query.with_entities(
            model.id
        ).filter(
            model.status == model.Status.VALID,
            model.enabled.is_(True)
        ).all()
        return {row.id for row in rows}

    @classmethod
    def _get_articles(cls):
        model = AcademyArticle
        rows = model.query.with_entities(
            model.id,
            model.primary_category_id,
            model.secondary_category_id,
            model.tag_ids,
            model.coins,
        ).filter(
            model.status == model.Status.PUBLISHED
        ).all()
        # 要求类别启用的文章
        enabled_cate_ids = cls._get_enabled_category_ids()
        res = []
        for row in rows:
            if row.primary_category_id not in enabled_cate_ids:
                continue
            if row.secondary_category_id and row.secondary_category_id not in enabled_cate_ids:
                continue
            res.append(row)
        return res

    @classmethod
    def _get_enabled_category_ids(cls) -> set[int]:
        model = AcademyCategory
        rows = model.query.with_entities(
            model.id
        ).filter(
            model.status == model.Status.VALID,
            model.enabled.is_(True)
        ).all()
        return {row.id for row in rows}

    @classmethod
    def get_parse_result(cls, lang: Language, title: str, abstract: str, content: str):
        text = '\n'.join(
            [item.get_text(' ') for item in BeautifulSoup(content, 'html.parser').contents])
        if '\n' in text:
            text = text.replace('\n', ' ')
        if '\r' in text:
            text = text.replace('\r', ' ')
        title_participles = cut_words(lang, title)
        if text:
            content_participles = cut_words(lang, text)
        else:
            content_participles = cut_words(lang, abstract)
        participles = title_participles + content_participles
        return {
            'participles': participles,
            'title_participles': title_participles,
            'content_participles': content_participles,
            'read_time': cls._calc_reading_time(lang, content or abstract),
        }

    @classmethod
    def update_parsed_result(cls, record, lang, content_input):
        title = content_input['title']
        abstract = content_input['abstract']
        content = content_input['content']
        record.title = title
        record.abstract = abstract
        record.content = content
        parsed_ret = cls.get_parse_result(lang, title, abstract, content)
        record.read_time = parsed_ret['read_time']
        title_participles = ''
        if parsed_ret['title_participles']:
            title_participles = json.dumps(parsed_ret['title_participles'])
        record.title_participles = title_participles
        content_participles = ''
        if parsed_ret['content_participles']:
            content_participles = json.dumps(parsed_ret['content_participles'])
        record.content_participles = content_participles

    @classmethod
    def _calc_reading_time(cls, lang: Language, content: str) -> int:
        # 解析 HTML
        soup = BeautifulSoup(content, 'html.parser')

        # 计算文字阅读时长
        text_content = soup.get_text()
        text_reading_time = cls._calc_text_reading_time(lang, text_content)

        # 计算图片阅读时长
        image_tags = soup.find_all('img')
        image_reading_time = cls._calc_image_reading_time(image_tags)

        # 总阅读时长（分钟）
        total_reading_time = text_reading_time + image_reading_time / 60

        # 最小截断为1分钟
        total_reading_time = max(math.ceil(total_reading_time), 1)
        return total_reading_time

    @classmethod
    def _calc_text_reading_time(cls, lang: Language, text: str) -> int:
        wb = cls.WORD_BOUNDARY.get(lang, cls.DEFAULT_BOUNDARY)
        if wb == cls.WordBoundary.VISUAL_CHAR:
            cleaned_text = ''.join(text.split())
            pattern = cls.DEF_PATTERNS[lang]
            chars = re.findall(pattern, cleaned_text)
            # 这里没有考虑混入其他语言的情况，如日语等
            word_count = len(chars)
            # 还需考虑下英文，因为这是常见的形式：该语言 + 英文（数字不考虑
            # 目前发现中文参杂英文时，在书写时通常不会刻意前后空格隔开，此时若使用 “\b” 进行边界匹配时，将无法提取 “AI改变世界” 中 AI 一词
            # 故去掉正则中 ”\b“ 以匹配中英连词
            en_words = re.findall(cls.PATTERN_EN, cleaned_text)
            word_count += len(en_words)
        else:
            word_count = len(text.split())  # 这里未去除标点符号
        if wpm_tuple := cls.WPM_MAPPING.get(lang):
            min_wpm, max_wpm = wpm_tuple
            wpm = min_wpm
        else:
            wpm = cls.DEFAULT_WPM
        reading_time = word_count / wpm
        return reading_time

    @classmethod
    def _calc_image_reading_time(cls, image_tags) -> int:
        num_images = len(image_tags)
        image_reading_time = 0

        # 图片阅读时间计算
        for i in range(num_images):
            if i < 10:
                image_reading_time += max(12 - i, 3)  # 第一张 12 秒，第二张 11 秒，依次递减到第10张，之后每张3秒
            else:
                image_reading_time += 3
        return image_reading_time


def to_list_result(
        items: list, total: int, has_next: bool, new_last_id: int, participles: list[str] = None
) -> dict:
    result = dict(
        items=items,
        total=total,
        has_next=has_next,
        last_id=new_last_id,
    )
    if participles is not None:
        result['participles'] = participles
    return result
