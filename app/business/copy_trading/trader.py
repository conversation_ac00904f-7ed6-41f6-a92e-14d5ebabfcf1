# -*- coding: utf-8 -*-
import json
import time
from collections import defaultdict
from datetime import datetime, date, timedelta
from decimal import Decimal
from functools import cached_property
from typing import Optional

from flask import g, current_app
from flask_babel import gettext

from app.business.user import UserRepository
from app.common import Language, CeleryQueues
from app.exceptions import (
    InvalidArgument, CopyTraderAdjustLeverageError,
    CopyTraderNameAlreadyExists, CopyTraderApplyTooFrequentError, CopyTraderNotExists,
    CopyTraderOpenPositionOverLimit,
)
from app.models import db, User, SubAccount, AssetPrice, UserApiFrequencyLimitRecord, ApiAuth
from app.models.copy_trading import (
    TimeRangeEnum,
    CopyTraderApplication,
    CopyTradingRunUserStatus,
    CopyTraderUser,
    CopyTraderHistory,
    CopyFollowerHistory,
    CopyTraderMarketOperation,
    CopyTraderStatistics,
    DailyCopyTraderStatistics,
    CopyTraderPositionChangeRecord,
)
from app.models.mongo.copy_trading import CopyTraderOperateLogMySQL as CopyTraderOperateLog
from app.business import PriceManager, PerpetualHistoryDB, UserPreferences, PerpetualServerClient, lock_call, CacheLock, LockKeys
from app.business.copy_trading.base import TraderSubUserManager, CopyTradingSettings, CopyRelationQuerier
from app.business.copy_trading.position import cancel_user_all_limit_and_stop_order, market_close_user_all_position
from app.business.copy_trading.message import (
    TraderMessageSender,
    send_trader_adjust_profit_share_rate_notice,
)
from app.caches import PerpetualMarketCache, PerpetualOfflineMarketCache
from app.caches.copy_trading import CopyTraderPositionCountCache
from app.utils import (
    quantize_amount, route_module_to_celery_queue, celery_task,
    batch_iter,
)
from app.utils.date_ import timestamp_to_datetime, now, today, today_datetime, convert_datetime, date_to_datetime, current_timestamp
from app.utils.parser import JsonEncoder


route_module_to_celery_queue(__name__, CeleryQueues.COPY_TRADING)


SUPPORT_LEVERAGES = [1, 2, 3, 5, 8, 10]  # 支持杠杆倍数（跟单人的跟单参数、带单子帐号交易市场不能超过最大值）


class CopyTraderApplyManager:
    """ 成为带单人申请相关 """
    MIN_BALANCE_USDT = Decimal("200")  # 最小账户资产 USDT
    PERPETUAL_TRADE_DAYS = 30  # 近N天的合约交易
    MIN_PERPETUAL_TRADE_COUNT = 1  # 最小交易次数
    MIN_PERPETUAL_PROFIT_AMOUNT = Decimal("500")  # 最小合约交易总盈利额

    RE_APPLY_INTERVAL_DAYS = 30  # 重新申请间隔天数

    SOCIAL_KEYS = ["telegram", "facebook", "twitter", "reddit", "medium", "discord"]

    def __init__(self, user: User):
        assert not user.is_sub_account
        self.user = user
        self._dt_prices_map = {}

    @cached_property
    def sub_users(self) -> list[SubAccount]:
        rows = SubAccount.query.filter(
            SubAccount.main_user_id == self.user.id,
        ).with_entities(SubAccount.user_id, SubAccount.type).all()
        return rows

    def get_dt_prices(self, date_: date) -> dict[str, Decimal]:
        if date_ in self._dt_prices_map:
            return self._dt_prices_map[date_]
        self._dt_prices_map[date_] = prices = AssetPrice.get_close_price_map(date_)
        return prices

    def has_kyc(self) -> bool:
        return self.user.kyc_status == User.KYCStatus.PASSED

    def is_active_follower(self) -> bool:
        active_follow_his = CopyFollowerHistory.query.filter(
            CopyFollowerHistory.user_id == self.user.id,
            CopyFollowerHistory.status != CopyFollowerHistory.Status.FINISHED,
        ).with_entities(CopyFollowerHistory.id).first()
        return bool(active_follow_his)

    def get_balance_data(self) -> tuple[bool, Decimal]:
        from app.business.risk_control.withdrawal import BalanceManager

        price_map = PriceManager.assets_to_usd()
        sub_user_ids = [i.user_id for i in self.sub_users]
        bm = BalanceManager(self.user.id, sub_user_ids, price_map)
        try:
            balance_usdt = quantize_amount(bm.get_current_balance_usd() / price_map['USDT'], 8)
        except:  # noqa
            balance_usdt = Decimal()
        is_ok = balance_usdt >= self.MIN_BALANCE_USDT
        return is_ok, balance_usdt

    def query_position_trade_data(self, start_dt: datetime, end_dt: datetime) -> tuple[int, Decimal]:
        """ 查时间内合约已平仓位（交易次数、总盈利额） """
        sub_user_ids = [
            i.user_id for i in self.sub_users
            if i.type in [SubAccount.Type.NORMAL, SubAccount.Type.COPY_TRADER]
        ]
        user_ids = [self.user.id] + sub_user_ids
        dbs_tables = defaultdict(list)
        for user_id_ in user_ids:
            _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id_, 'position_history')
            dbs_tables[(_db, _table)].append(user_id_)

        start_ts = int(start_dt.timestamp())
        end_ts = int(end_dt.timestamp())
        records = []
        columns = ('position_id', 'user_id', 'create_time', 'update_time',
                   'market', 'profit_real')
        for k, v in dbs_tables.items():
            _db, _table = k
            for ch_ids in batch_iter(v, 2000):
                user_id_str = ','.join(map(str, ch_ids))
                where = f' user_id in ({user_id_str}) ' \
                        f'and create_time >= {start_ts} and update_time < {end_ts} '
                _records = _db.table(_table).select(*columns, where=where)
                records.extend(_records)

        p_markets = {i[4] for i in records}
        p_market_asset_type = {}  # {合约市场：盈亏数的单位}
        for m in p_markets:
            market_info = PerpetualMarketCache().get_market_info(m)
            if not market_info:
                market_info = PerpetualOfflineMarketCache().get_market_info(m)
            if not market_info:
                continue
            balance_asset = PerpetualMarketCache.get_balance_asset(market_info)
            p_market_asset_type[m] = balance_asset

        profit_amount = Decimal()
        target_asset = "USDT"
        for r in records:
            market = r[4]
            if market not in p_market_asset_type:
                continue
            price_map = self.get_dt_prices(timestamp_to_datetime(int(r[3])).date())
            asset = p_market_asset_type[market]
            asset_rate = price_map.get(asset)
            target_rate = price_map.get(target_asset)
            if not (asset_rate and target_rate):
                continue
            profit_real = r[5]
            profit_amount += profit_real * asset_rate / target_rate

        trade_count = len(records)
        profit_amount = quantize_amount(profit_amount, 8)
        return trade_count, profit_amount

    def get_perpetual_trade_data(self) -> tuple[bool, int, bool, Decimal]:
        end_dt = now()
        start_dt = end_dt - timedelta(days=self.PERPETUAL_TRADE_DAYS)
        trade_count, profit_amount = self.query_position_trade_data(start_dt, end_dt)
        tc_is_ok = trade_count >= self.MIN_PERPETUAL_TRADE_COUNT
        pm_is_ok = True  # app旧版本兼容
        return tc_is_ok, trade_count, pm_is_ok, profit_amount

    def get_condition_status(self):
        """
        用户申请成为交易员时，对用户进行判断是否满足条件：
        1. 实名认证：已完成
        2. 资产要求：账户资产>=100 USDT
        3. 交易能力要求：
            3.1 近30天交易次数>=3次
            3.2 近30天合约交易总盈利额>1000U
        4. 用户没有触发风控条件
        5. 没有生效中的跟单
        用户需要同时满足以上条件才能进入申请流程。
        """
        balance_ok, balance_usd = self.get_balance_data()
        tc_is_ok, trade_count, pm_is_ok, profit_amount = self.get_perpetual_trade_data()
        conditions = {
            # kyc、balance_usd、perpetual_trade_count、perpetual_profit_usd、
            "has_kyc": self.has_kyc(),
            "balance_usd": balance_usd,
            "perpetual_trade_count": trade_count,
            "perpetual_profit_usd": profit_amount,
            "is_active_follower": self.is_active_follower(),
        }

        condition_details = [
            dict(
                name=gettext('完成初级实名认证'),
                value=conditions['has_kyc']
            ),
            dict(
                name=gettext('账户资产等值≥%(balance_usd)s USDT', balance_usd=self.MIN_BALANCE_USDT),
                value=conditions['balance_usd'] >= self.MIN_BALANCE_USDT,
            ),
            dict(
                name=gettext('近30天已平仓合约交易次数≥%(trade_count)s笔', trade_count=self.MIN_PERPETUAL_TRADE_COUNT),
                value=conditions['perpetual_trade_count'] >= self.MIN_PERPETUAL_TRADE_COUNT,
            ),
            dict(
                name=gettext('没有生效中的跟单'),
                value=not conditions['is_active_follower'],
            ),
        ]
        return conditions, condition_details

    def is_pass_all_condition(self) -> bool:
        if not self.has_kyc():
            return False
        if self.is_active_follower():
            return False
        tc_is_ok, trade_count, pm_is_ok, profit_amount = self.get_perpetual_trade_data()
        if not tc_is_ok or not pm_is_ok:
            return False
        balance_ok, balance_usd = self.get_balance_data()
        if not balance_ok:
            return False
        return True

    @classmethod
    def get_last_application(cls, user_id: int) -> CopyTraderApplication:
        r = CopyTraderApplication.query.filter(
            CopyTraderApplication.user_id == user_id,
        ).order_by(CopyTraderApplication.id.desc()).first()
        return r

    @classmethod
    def has_pending_application(cls, user_id: int) -> bool:
        r = CopyTraderApplication.query.filter(
            CopyTraderApplication.user_id == user_id,
            CopyTraderApplication.status == CopyTraderApplication.Status.CREATED,
        ).with_entities(CopyTraderApplication.id).first()
        return bool(r)

    def new_application(self, trader_info: dict, creator: int = None) -> CopyTraderApplication:
        """ 新增-带单人申请 """
        nickname = trader_info.get("nickname") or ""
        if nickname:
            UserRepository.check_nickname_rules(self.user, nickname)
        account_name = trader_info.get("account_name", "")
        if account_name:
            UserRepository.check_update_account_name(self.user, account_name)
        avatar = trader_info.get("avatar") or ""
        apl = CopyTraderApplication()
        apl.creator = creator or self.user.id
        apl.user_id = self.user.id
        apl.nickname = nickname
        apl.account_name = account_name
        apl.avatar = avatar
        apl.introduction = trader_info.get("introduction")
        apl.contact = trader_info.get("contact") or ""
        social_data = {k: str(trader_info.get(k) or "") for k in self.SOCIAL_KEYS}
        apl.social_data = json.dumps(social_data, cls=JsonEncoder)
        db.session.add(apl)
        db.session.commit()
        return apl

    def audit_pass_application(self, apl: CopyTraderApplication, op_user_id: int = None):
        """ 审核通过-带单人申请 """
        if apl.status != CopyTraderApplication.Status.CREATED:
            raise ValueError("apply status error")

        apl.status = CopyTraderApplication.Status.AUDITED
        apl.audited_at = now()
        if op_user_id:
            apl.auditor = op_user_id
        trader = self.become_trader_from_apply(apl)
        if apl.nickname != self.user.name:
            UserRepository.update_user_name(self.user, apl.nickname, auto_commit=False)
        if apl.account_name:
            UserRepository.update_user_account_name(self.user, apl.account_name, auto_commit=False)
        if apl.avatar:
            UserRepository.update_user_avatar(self.user, apl.avatar, auto_commit=False)
        db.session.commit()
        TraderMessageSender.send_apply_success(trader)

    @classmethod
    def require_nickname_not_exists(cls, nickname: str, exclude_user_id: int = None):
        q = CopyTraderUser.query.filter(
            CopyTraderUser.nickname == nickname,
        )
        if exclude_user_id:
            q = q.filter(CopyTraderUser.user_id != exclude_user_id)
        if q.first():
            raise CopyTraderNameAlreadyExists

    @classmethod
    def check_inactive_interval_days(cls, user_id: int):
        """ 主动取消带单者身份（可于30天后重新带单） """
        last_trade_his = CopyRelationQuerier.get_last_trade_his(user_id)
        if not last_trade_his:
            return
        if not last_trade_his.finished_at:
            raise CopyTraderApplyTooFrequentError(days=cls.RE_APPLY_INTERVAL_DAYS)

        now_ = now()
        next_apply_at: datetime = last_trade_his.finished_at + timedelta(days=cls.RE_APPLY_INTERVAL_DAYS)
        if next_apply_at > now():
            delta_days = max((next_apply_at - now_).days, 1)
            raise CopyTraderApplyTooFrequentError(days=delta_days)

    @classmethod
    def become_trader_from_apply(cls, apl: CopyTraderApplication) -> CopyTraderUser:
        """ 成为带单人，外部commit """
        user_id = apl.user_id
        cls.require_nickname_not_exists(apl.nickname, exclude_user_id=user_id)
        trader: CopyTraderUser = CopyTraderUser.get_or_create(user_id=user_id)
        if not trader.trader_id:
            trader.trader_id = CopyTraderUser.gen_new_trader_id()
        trader.nickname = apl.nickname
        trader.nickname_updated_at = None
        trader.last_started_at = now()
        trader.last_finished_at = None
        trader.avatar = apl.avatar
        trader.introduction = apl.introduction
        trader.contact = apl.contact
        #
        trader.min_copy_amount = CopyTradingSettings.min_copy_amount
        trader.max_copy_amount = CopyTradingSettings.max_copy_amount
        trader.profit_share_rate = CopyTradingSettings.default_profit_share_rate
        trader.display_priority = CopyTraderUser.NORMAL_DISPLAY_PRIORITY
        trader.cur_follower_num = 0
        trader.max_follower_num = CopyTradingSettings.max_follower_num
        trader.language = decision_language_by_name_and_intro(user_id, trader.nickname, trader.introduction)
        trader.status = CopyTraderUser.Status.INACTIVE
        #
        social_data = json.loads(apl.social_data) if apl.social_data else {}
        for sf in cls.SOCIAL_KEYS:
            setattr(trader, sf, social_data.get(sf, ""))
        db.session.add(trader)

        # start trading
        CopyTraderManager.start_trade(trader)

        # add statics
        CopyTraderManager.init_statics(user_id)
        return trader


class CopyTraderManager:
    """ 带单人的相关操作 """

    NICKNAME_UPDATE_INTERVAL_DAYS = 30
    MAX_FOLLOWER_NUM = 500
    PROFIT_SHARE_RATE_UPDATE_INTERVAL_DAYS = 14

    def __init__(self, user: User):
        assert not user.is_sub_account
        self.user = user

    @classmethod
    def get_trader_by_trader_id(cls, trader_id: str) -> Optional[CopyTraderUser]:
        """ 获取带单人 """
        model = CopyTraderUser
        query = model.query.filter(
            model.trader_id == trader_id,
            model.status != model.Status.DELETED,
        )
        return query.first()

    @classmethod
    def get_trader(cls, user_id: int) -> Optional[CopyTraderUser]:
        """ 获取带单人 """
        model = CopyTraderUser
        query = model.query.filter(
            model.user_id == user_id,
            model.status != model.Status.DELETED,
        )
        return query.first()

    @classmethod
    def get_trader_run_sub_id(cls, user_id: int) -> Optional[CopyTraderUser]:
        """ 获取带单人-带单子帐号uid """
        return TraderSubUserManager(user_id).get_run_sub_id()

    @classmethod
    def check_can_edit_nickname(cls, trader: CopyTraderUser) -> tuple[bool, int]:
        if not trader.nickname_updated_at:
            return True, 0
        now_ = now()
        next_edit_at: datetime = trader.nickname_updated_at + timedelta(days=cls.NICKNAME_UPDATE_INTERVAL_DAYS)
        if now_ >= next_edit_at:
            return True, 0
        delta_days = max((next_edit_at - now_).days, 1)
        return False, delta_days

    @classmethod
    def check_can_edit_profit_share_rate(cls, trader: CopyTraderUser) -> tuple[bool, int]:
        last_op_log = CopyTraderOperateLog.query_user_last_op(
            trader.user_id,
            CopyTraderOperateLog.OpType.EDIT_PROFIT_SHARE_RATE,
        )
        if not last_op_log:
            return True, 0
        now_ = now()
        last_op_at = convert_datetime(last_op_log.created_at, 'microsecond')
        next_edit_at: datetime = last_op_at + timedelta(days=cls.PROFIT_SHARE_RATE_UPDATE_INTERVAL_DAYS)
        if now_ >= next_edit_at:
            return True, 0
        delta_days = max((next_edit_at - now_).days, 1)
        return False, delta_days

    @classmethod
    def edit_profile_info(cls, trader: CopyTraderUser, new_profile: dict):
        from app.business.user import UserRepository
        """ 更改带单人的信息 """
        user = User.query.get(trader.user_id)
        if not user:
            return
        old_nickname = user.name
        new_nickname = new_profile.get("nickname")

        if new_nickname and new_nickname != old_nickname:
            UserRepository.update_user_name(user, new_nickname, auto_commit=False)

        if avatar := new_profile.get("avatar"):
            UserRepository.update_user_avatar(user, avatar)

        if introduction := new_profile.get("introduction"):
            trader.introduction = introduction
        trader.language = decision_language_by_name_and_intro(trader.user_id, trader.nickname, trader.introduction)
        db.session.add(trader)
        db.session.commit()

    @classmethod
    def edit_settings(cls, trader: CopyTraderUser, new_settings: dict):
        """ 更改带单人的带单参数 """
        old_ps_rate = trader.profit_share_rate
        ps_rate_is_changed = False
        if "profit_share_rate" in new_settings:
            new_ps_rate = new_settings["profit_share_rate"]
            ps_rate_is_changed = old_ps_rate != new_ps_rate
            if ps_rate_is_changed:
                can_edit, after_days = cls.check_can_edit_profit_share_rate(trader)
                if not can_edit:
                    raise InvalidArgument(message=gettext("由于你近期已修改分润比例，请于%(days)s天后再试", days=after_days))

            trader.profit_share_rate = new_ps_rate

        if "max_copy_amount" in new_settings and "min_copy_amount" in new_settings:
            trader.max_copy_amount = new_settings["max_copy_amount"]
            trader.min_copy_amount = new_settings["min_copy_amount"]
        db.session.add(trader)
        db.session.commit()

        if ps_rate_is_changed:
            op_log = CopyTraderOperateLog(
                created_at=now(),
                user_id=trader.user_id,
                op_type=CopyTraderOperateLog.OpType.EDIT_PROFIT_SHARE_RATE.name,
                detail=new_settings
            )
            db.session.add(op_log)
            db.session.commit()
            send_trader_adjust_profit_share_rate_notice.delay(trader.user_id, old_ps_rate, trader.profit_share_rate)

    @classmethod
    def add_one_follower(cls, trader: CopyTraderUser):
        assert not trader.is_full
        trader.cur_follower_num += 1

    @classmethod
    def remove_one_follower(cls, trader: CopyTraderUser):
        assert trader.cur_follower_num > 0
        trader.cur_follower_num -= 1

    @classmethod
    def start_trade(cls, trader: CopyTraderUser):
        """ 带单人-开始带单 """
        if trader.status != CopyTraderUser.Status.INACTIVE:
            raise ValueError

        sub_manager = TraderSubUserManager(trader.user_id)
        run_sub = sub_manager.allocate_sub_user()

        now_ = now()
        trader.status = CopyTraderUser.Status.ACTIVE
        trader.last_started_at = now_
        copy_his = CopyTraderHistory(
            user_id=trader.user_id,
            sub_user_id=run_sub.user_id,
            started_at=now_,
        )
        db.session.add(copy_his)

    @classmethod
    def try_get_last_active_trade_his(cls, user_id: int) -> Optional[CopyTraderHistory]:
        his_rows = CopyTraderHistory.query.filter(
            CopyTraderHistory.user_id == user_id,
            CopyTraderHistory.status != CopyTraderHistory.Status.FINISHED,
        ).all()
        if len(his_rows) > 1:
            raise ValueError
        return his_rows[0] if his_rows else None

    @classmethod
    def get_last_active_trade_his(cls, user_id: int) -> CopyTraderHistory:
        his_rows = CopyTraderHistory.query.filter(
            CopyTraderHistory.user_id == user_id,
            CopyTraderHistory.status != CopyTraderHistory.Status.FINISHED,
        ).all()
        if len(his_rows) == 0:
            raise ValueError(f"{user_id} active_trade_his_not_found")
        if len(his_rows) > 1:
            raise ValueError
        return his_rows[0]

    @classmethod
    def get_last_trade_his(cls, user_id: int) -> Optional[CopyTraderHistory]:
        his_rows = CopyTraderHistory.query.filter(
            CopyTraderHistory.user_id == user_id,
        ).order_by(CopyTraderHistory.id.desc()).first()
        return his_rows

    @classmethod
    def stop_trade(cls, trader: CopyTraderUser, finish_type: CopyTraderHistory.FinishType = CopyTraderHistory.FinishType.SELF):
        """ 带单人-结束带单，只改带单、跟进记录的状态 """
        from app.schedules.copy_trading import finish_copy_trading_trader_status

        if not trader.is_active:
            raise ValueError

        trade_his = cls.get_last_active_trade_his(trader.user_id)
        if trade_his.status != CopyTraderHistory.Status.ENDING:
            trade_his.status = CopyTraderHistory.Status.ENDING
            trade_his.finish_type = finish_type
        del_copy_trader_api_auth(trader.user_id)
        db.session.commit()

        try:
            cancel_user_all_limit_and_stop_order(trade_his.sub_user_id)
        except:  # noqa
            pass
        try:
            market_close_user_all_position(trade_his.sub_user_id, clear_take_profit_stop_loss=True)
        except:  # noqa
            pass
        finish_copy_trading_trader_status.delay(trade_his.sub_user_id)

    @classmethod
    def set_trade_finished(cls, trader: CopyTraderUser):
        """ 设置带单为结束 """
        trade_his = cls.get_last_active_trade_his(trader.user_id)
        trade_his.status = CopyTraderHistory.Status.FINISHED
        now_ = now()
        trade_his.finished_at = now_
        trader.last_finished_at = now_
        trader.cur_follower_num = 0
        trader.status = CopyTraderUser.Status.INACTIVE
        TraderSubUserManager(trader.user_id).free_sub_user(trade_his.sub_user_id)
        db.session.commit()
        TraderMessageSender.send_trade_finished(trader, trade_his)

    @classmethod
    def init_statics(cls, user_id: int):
        st_rows = CopyTraderStatistics.query.filter(CopyTraderStatistics.user_id == user_id).all()
        for r in st_rows:
            # 重新发起带单，所有盈亏统计数据清空，从用户重新成为交易员当天开始计算
            cols1 = ['trade_days', 'profit_count', 'loss_count', 'winning_rate',
                     'aum', 'margin_amount', 'profit_amount', 'profit_rate',
                     'follower_profit_amount', 'total_profit_amount', 'mdd',
                     ]
            for col in cols1:
                setattr(r, col, 0)
            r.last_trade_at = r.last_update_at = None
        existed_time_ranges = {i.time_range for i in st_rows}
        for tr in TimeRangeEnum:
            if tr not in existed_time_ranges:
                st = CopyTraderStatistics(user_id=user_id, time_range=tr)
                db.session.add(st)
        daily_st = DailyCopyTraderStatistics.get_or_create(user_id=user_id, date=today())
        db.session.add(daily_st)


class CopyTraderSearcher:
    """ 带单人名称的搜索 """
    pass


class CopyTraderPerpetualOp:
    """带单人一些合约操作"""

    @classmethod
    def is_copy_trader_sub_mode(cls) -> bool:
        return getattr(g, 'is_copy_trader_sub', False)

    @classmethod
    def adjust_sub_leverage(cls, user_id: int, trader_sub_id: int, market: str, position_type: int, leverage: int):
        """ 交易员存在仓位时不能更改保证金模式，允许调整杠杆倍数 """
        from app.business.perpetual.market import adjust_leverage

        if not cls.is_copy_trader_sub_mode():
            return

        leverage = int(leverage)
        cls.check_can_adjust_leverage(leverage)
        cls.check_can_adjust_position_type(trader_sub_id, market, position_type)
        adjust_leverage(trader_sub_id, market, position_type, str(leverage))
        op_row = CopyTraderMarketOperation(
            user_id=user_id,
            sub_user_id=trader_sub_id,
            market=market,
            type=CopyTraderMarketOperation.Type.ADJUST_LEVERAGE,
            detail=json.dumps({"position_type": position_type, "leverage": leverage}),
        )
        db.session_add_and_commit(op_row)
        execute_trader_market_operation_task.delay(op_row.id)

    @classmethod
    def check_can_adjust_position_type(cls, trader_sub_id: int, market: str, position_type: int):
        p_client = PerpetualServerClient()
        p_pref = p_client.get_preference(trader_sub_id, market)
        if p_pref["position_type"] != position_type and p_client.position_pending(trader_sub_id, market):
            raise CopyTraderAdjustLeverageError

    @classmethod
    def check_can_adjust_leverage(cls, new_leverage: int):
        if new_leverage > max(SUPPORT_LEVERAGES):
            raise InvalidArgument(message=gettext("合约跟单交易暂不支持10x以上杠杆"))

    @classmethod
    def check_current_leverage(cls, trader_sub_id: int, market: str):
        """ 检查当前设置的杠杆倍数（无仓位下单时） """
        p_client = PerpetualServerClient()
        if p_client.position_pending(trader_sub_id, market):
            return
        p_pref = p_client.get_preference(trader_sub_id, market)
        cur_leverage = int(p_pref['leverage'])
        cls.check_can_adjust_leverage(cur_leverage)


def get_copy_trader_sub_user(
    main_user_id: int,
    require_running: bool,
    check_pos_count: bool = False,
    check_data: dict = None,
) -> User:
    """ 获取带单人的带单子帐号user，合约相关的api调用
    提交接口 require_running=True，查询接口 require_running=False
    """
    trade_his = CopyTraderHistory.query.filter(
        CopyTraderHistory.user_id == main_user_id,
    ).order_by(CopyTraderHistory.id.desc()).first()
    if not trade_his:
        raise CopyTraderNotExists
    if require_running:
        if trade_his.status == CopyTraderHistory.Status.ENDING:
            raise InvalidArgument(message=gettext("系统正处理你的结束带单请求，禁止操作"))
        if trade_his.status == CopyTraderHistory.Status.FINISHED:
            raise InvalidArgument(message=gettext("请重新申请带单，通过申请后才可发起带单交易"))
    if check_pos_count:
        cur_pos_count = CopyTraderPositionCountCache().get_count(main_user_id)
        if cur_pos_count >= CopyTradingSettings.trader_daily_max_position_count:
            raise CopyTraderOpenPositionOverLimit
    if check_data:
        if "market" in check_data:
            market = check_data["market"]
            if not CopyTradingSettings.has_market(market):
                raise InvalidArgument(message=gettext("跟单交易不支持该合约市场"))
            if check_pos_count:  # 将要下单
                CopyTraderPerpetualOp.check_current_leverage(trade_his.sub_user_id, market)

    sub_user = User.query.get(trade_his.sub_user_id)
    return sub_user


def add_trader_sub_api_frequency_limit(sub_user_id: int):
    """ 交易员带单子帐号添加API限频 """
    default_rate_rows = UserApiFrequencyLimitRecord.query.filter(
        UserApiFrequencyLimitRecord.user_id == 0,
        UserApiFrequencyLimitRecord.status == UserApiFrequencyLimitRecord.Status.VALID,
    ).with_entities(
        UserApiFrequencyLimitRecord.group,
        UserApiFrequencyLimitRecord.limit_count,
    ).all()
    group_default_limit_count_map = dict(default_rate_rows)

    freq_rows = UserApiFrequencyLimitRecord.query.filter(
        UserApiFrequencyLimitRecord.user_id == sub_user_id,
        UserApiFrequencyLimitRecord.status == UserApiFrequencyLimitRecord.Status.VALID,
    ).all()
    group_row_map = {i.group: i for i in freq_rows}
    default_limit_count = 5
    for group_ in UserApiFrequencyLimitRecord.ApiGroups:
        if _lc := group_default_limit_count_map.get(group_):
            limit_count = int(_lc / 10)
        else:
            limit_count = default_limit_count
        if group_ not in group_row_map:
            record = UserApiFrequencyLimitRecord(
                user_id=sub_user_id,
                group=group_,
                status=UserApiFrequencyLimitRecord.Status.VALID,
                limit_count=limit_count,
            )
        else:
            record = group_row_map[group_]
            record.limit_count = limit_count
        db.session.add(record)
    db.session.commit()


def del_copy_trader_api_auth(main_user_id: int):
    """ 删除带单API """
    ApiAuth.query.filter(
        ApiAuth.user_id == main_user_id,
        ApiAuth.status == ApiAuth.Status.VALID,
        ApiAuth.source == ApiAuth.Source.COPY_TRADER,
    ).update(
        {'status': ApiAuth.Status.DELETED}, synchronize_session=False,
    )


def update_all_trader_sub_api_frequency_limit_count():
    """ 更新全部交易员带单子帐号的API限频 """
    default_rate_rows = UserApiFrequencyLimitRecord.query.filter(
        UserApiFrequencyLimitRecord.user_id == 0,
        UserApiFrequencyLimitRecord.status == UserApiFrequencyLimitRecord.Status.VALID,
    ).with_entities(
        UserApiFrequencyLimitRecord.group,
        UserApiFrequencyLimitRecord.limit_count,
    ).all()
    group_default_limit_count_map = dict(default_rate_rows)
    default_limit_count = 5

    rows = CopyTradingRunUserStatus.query.filter(
        CopyTradingRunUserStatus.type == CopyTradingRunUserStatus.Type.TRADER
    ).with_entities(
        CopyTradingRunUserStatus.user_id,
    ).all()
    sub_ids = [i.user_id for i in rows]
    for ch_sub_ids in batch_iter(sub_ids, 5000):
        rows = UserApiFrequencyLimitRecord.query.filter(
            UserApiFrequencyLimitRecord.user_id.in_(ch_sub_ids),
            UserApiFrequencyLimitRecord.status == UserApiFrequencyLimitRecord.Status.VALID,
        )
        for row in rows:
            if _lc := group_default_limit_count_map.get(row.group):
                limit_count = int(_lc / 10)
            else:
                limit_count = default_limit_count
            row.limit_count = limit_count
            db.session.add(row)
        db.session.commit()


def decision_language_by_name_and_intro(user_id: int, nickname: str, introduction: Optional[str]) -> Optional[Language]:
    """ 判断带单人的语言
    【昵称】字段全英文字符，【简介】字段英文字符占比较大（eg 70%），判定用户为 EN用户；
    【昵称】字段全英文字符，【简介】字段英文字符占比较小，判定用户为 当前语言用户；站内无该语言的判断为EN用户；
    【昵称】字段存在其他语言字符，判定用户为当前语言用户；站内无该语言的判断为EN用户；
    """
    def is_en_char(c: str) -> bool:
        return 0 <= ord(c) <= 127

    introduction = introduction or ''
    nickname_en_count = sum([is_en_char(i) for i in nickname])
    if nickname_en_count == len(nickname):
        intro_en_threshold = Decimal('0.7')
        introduction_en_count = sum([is_en_char(i) for i in introduction])
        if not introduction or Decimal(introduction_en_count / len(introduction)) >= intro_en_threshold:
            # 无简介 or 简介英文字符占比超过70%
            return Language.EN_US
    return UserPreferences(user_id).language


def update_trader_open_position_count_cache():
    """ 更新带单人每日开仓数缓存 """
    start_dt = today_datetime()
    end_dt = now()

    rows = CopyTraderPositionChangeRecord.query.filter(
        CopyTraderPositionChangeRecord.position_created_at >= start_dt,
        CopyTraderPositionChangeRecord.position_created_at < end_dt,
    ).with_entities(
        CopyTraderPositionChangeRecord.user_id,
        CopyTraderPositionChangeRecord.position_id,
    ).all()
    trader_pos_ids_map = defaultdict(set)
    for r in rows:
        trader_pos_ids_map[r.user_id].add(r.position_id)

    save_data = {str(k): str(len(v)) for k, v in trader_pos_ids_map.items()}
    cache_ = CopyTraderPositionCountCache()
    cache_.save(save_data)
    cache_.expire(cache_.TTL)


@celery_task
@lock_call(with_args=True)
def execute_trader_market_operation_task(operation_id: int):
    """ 带单人的一些操作，同步给跟单人 """
    from app.business.perpetual.market import adjust_leverage
    from app.business.copy_trading.order import PositionOperation

    op_row: CopyTraderMarketOperation = CopyTraderMarketOperation.query.get(operation_id)
    if op_row.status == CopyTraderMarketOperation.Status.FINISHED:
        return

    trader = CopyTraderManager.get_trader(op_row.user_id)
    if not trader.is_active:
        op_row.status = CopyTraderMarketOperation.Status.FINISHED
        db.session.add(op_row)
        db.session.commit()
        return

    market_info = PerpetualMarketCache().get_market_info(op_row.market)
    if not market_info:
        return

    synced_follow_ids = set(json.loads(op_row.result)) if op_row.result else set()
    follow_his_rows = CopyRelationQuerier.get_trader_active_follow_history(op_row.user_id)
    diff_follow_rows = [
        i for i in follow_his_rows
        if i.status == CopyFollowerHistory.Status.FOLLOWING and i.id not in synced_follow_ids
        and i.created_at < op_row.created_at
    ]
    if op_row.type == CopyTraderMarketOperation.Type.ADJUST_LEVERAGE:
        p_client = PerpetualServerClient()
        trader_pref = p_client.get_preference(op_row.sub_user_id, op_row.market)
        for fol_his in diff_follow_rows:
            if fol_his.leverage_type != CopyFollowerHistory.LeverageType.NONE:
                continue
            new_leverage = PositionOperation.get_follower_new_leverage(fol_his, market_info, trader_pref)
            if not new_leverage:
                continue
            follower_pref = p_client.get_preference(fol_his.sub_user_id, op_row.market)
            need_sync = new_leverage != follower_pref['leverage']
            if need_sync:
                try:
                    adjust_leverage(
                        fol_his.sub_user_id,
                        op_row.market,
                        follower_pref['position_type'],
                        str(new_leverage),
                    )
                except Exception as _e:
                    current_app.logger.exception(
                        f"execute_trader_market_operation_task ADJUST_LEVERAGE {fol_his.user_id} {fol_his.sub_user_id}"
                        f" error {_e!r}")
                synced_follow_ids.add(fol_his.id)
                op_row.result = json.dumps(list(synced_follow_ids))
                db.session.add(op_row)
                db.session.commit()
    op_row.status = CopyTraderMarketOperation.Status.FINISHED
    db.session.add(op_row)
    db.session.commit()


@celery_task
@lock_call(with_args=True)
def sync_trader_max_follower_num():
    """ 同步交易员的跟单人数上限 """
    max_num = CopyTradingSettings.max_follower_num
    rows: list[CopyTraderUser] = CopyTraderUser.query.with_entities(
        CopyTraderUser.id,
        CopyTraderUser.user_id,
        CopyTraderUser.cur_follower_num,
    ).all()
    for r in rows:
        if r.cur_follower_num > max_num:
            continue
        try:
            with CacheLock(LockKeys.copy_trader(r.user_id), wait=False):
                db.session.rollback()
                trader: CopyTraderUser = CopyTraderUser.query.get(r.id)
                if trader.cur_follower_num <= max_num:
                    trader.max_follower_num = max_num
                    db.session.add(trader)
                    db.session.commit()
        except:  # noqa
            db.session.rollback()


class InactiveTraderHelper:

    @classmethod
    def get_inactive_trade_histories(cls, inactive_days: int, last_trade_at_need_none: bool) -> list[CopyTraderHistory]:
        """ 获取不活跃带单记录
        交易员【带单天数】超过N天，而没有进行过带单交易（带单员主页没有【最后交易时间】），则算不活跃
        """
        today_ = today()
        inactive_date = today_ - timedelta(days=inactive_days)

        active_trade_his_rows = CopyRelationQuerier.get_active_trade_history()
        check_his_rows = [i for i in active_trade_his_rows if i.created_at.date() <= inactive_date]
        check_his_map: dict[int, CopyTraderHistory] = {i.user_id: i for i in check_his_rows}

        # 1. 查统计表的更新时间
        recheck_his_rows = []
        for ch_user_ids in batch_iter(check_his_map, 2000):
            st_rows: list[CopyTraderStatistics] = CopyTraderStatistics.query.filter(
                CopyTraderStatistics.user_id.in_(ch_user_ids),
                CopyTraderStatistics.time_range == TimeRangeEnum.ALL,
            ).with_entities(
                CopyTraderStatistics.user_id,
                CopyTraderStatistics.last_trade_at,
                CopyTraderStatistics.last_update_at,
            ).all()
            for st in st_rows:
                if st.last_update_at and st.last_update_at.date() >= today_:
                    if last_trade_at_need_none:
                        # 停止带单
                        if not st.last_trade_at:
                            recheck_his_rows.append(check_his_map[st.user_id])
                    else:
                        # 标记不活跃
                        if not st.last_trade_at or st.last_trade_at.date() <= inactive_date:
                            recheck_his_rows.append(check_his_map[st.user_id])

        # 2. 找出有成交的子帐号并排除（避免统计表异常的误判）
        sub_main_id_map = {i.sub_user_id: i.user_id for i in recheck_his_rows}
        dbs_tables = defaultdict(list)
        for sub_id in sub_main_id_map:
            _db, _table = PerpetualHistoryDB.user_to_db_and_table(sub_id, 'deal_history')
            dbs_tables[(_db, _table)].append(sub_id)

        has_deal_sub_ids = set()
        start_ts = int(date_to_datetime(inactive_date).timestamp())
        end_ts = current_timestamp(to_int=True) + 600
        columns = ('user_id', 'max(time)')
        for k, v in dbs_tables.items():
            _db, _table = k
            for ch_ids in batch_iter(v, 2000):
                user_id_str = ','.join(map(str, ch_ids))
                where = f' user_id in ({user_id_str}) ' \
                        f'and time >= {start_ts} and time <= {end_ts} '
                deal_records = _db.table(_table).select(*columns, where=where, group_by="user_id")
                has_deal_sub_ids.update([i[0] for i in deal_records])

        # 3. 找出有仓位的子帐号并排除
        check_pos_sub_ids = set(sub_main_id_map) - has_deal_sub_ids
        has_pending_pos_sub_ids = cls.get_has_pending_pos_user_ids(check_pos_sub_ids)
        res_inactive_sub_ids = check_pos_sub_ids - has_pending_pos_sub_ids

        res_inactive_main_ids = {sub_main_id_map[s] for s in res_inactive_sub_ids}
        res_inactive_his_rows = [i for i in recheck_his_rows if i.user_id in res_inactive_main_ids]
        return res_inactive_his_rows

    @classmethod
    def get_has_pending_pos_user_ids(cls, user_ids: set[int]) -> set[int]:
        no_pos_user_ids = set()
        client = PerpetualServerClient()
        for user_id in user_ids:
            try:
                if not client.position_pending(user_id):
                    no_pos_user_ids.add(user_id)
            except:  # noqa
                # 失败算有仓位
                continue
        has_pos_user_ids = user_ids - no_pos_user_ids
        return has_pos_user_ids

    @classmethod
    def find_and_mark_inactive_traders(cls):
        """ 标记不活跃的带单人，主页列表不展示交易员卡片，仍然可以搜索 """
        inactive_his_rows = cls.get_inactive_trade_histories(
            inactive_days=CopyTraderUser.INACTIVE_TRADER_DAYS,
            last_trade_at_need_none=False,
        )
        inactive_trader_ids = {i.user_id for i in inactive_his_rows}
        current_app.logger.warning(f"find_and_mark_inactive_traders inactive_trader_ids: {inactive_trader_ids}")
        if not inactive_trader_ids:
            return

        active_traders = CopyTraderUser.query.filter(
            CopyTraderUser.status == CopyTraderUser.Status.ACTIVE,
        ).all()
        for trader in active_traders:
            if trader.user_id in inactive_trader_ids:
                trader.display_priority = CopyTraderUser.INACTIVE_DISPLAY_PRIORITY
            elif trader.display_priority != CopyTraderUser.NORMAL_DISPLAY_PRIORITY:
                trader.display_priority = CopyTraderUser.NORMAL_DISPLAY_PRIORITY
        db.session.commit()

    @classmethod
    def notice_pre_stop_inactive_traders(cls):
        """ 系统操作结束不活跃的交易员-预提醒 """
        inactive_his_rows = cls.get_inactive_trade_histories(
            inactive_days=CopyTraderUser.INACTIVE_TRADER_DAYS,
            last_trade_at_need_none=True,
        )
        pre_stop_user_ids = {i.user_id for i in inactive_his_rows}
        current_app.logger.warning(f"notice_pre_stop_inactive_traders pre_stop_user_ids: {pre_stop_user_ids}")

        pre_stop_traders = CopyTraderUser.query.filter(
            CopyTraderUser.user_id.in_(pre_stop_user_ids),
            CopyTraderUser.status == CopyTraderUser.Status.ACTIVE,
        ).all()
        for trader in pre_stop_traders:
            TraderMessageSender.send_trade_pre_cancel(trader)

    @classmethod
    def system_stop_inactive_traders(cls):
        """ 系统操作结束不活跃的带单 """
        inactive_his_rows = cls.get_inactive_trade_histories(
            inactive_days=CopyTraderUser.STOP_INACTIVE_TRADER_DAYS,
            last_trade_at_need_none=True,
        )
        stop_user_ids = {i.user_id for i in inactive_his_rows}
        current_app.logger.warning(f"system_stop_inactive_trader stop_user_ids: {stop_user_ids}")
        if not stop_user_ids:
            return

        for stop_user_id in stop_user_ids:
            try:
                with CacheLock(LockKeys.copy_trader(stop_user_id), wait=False):
                    db.session.rollback()
                    trader = CopyTraderManager.get_trader(stop_user_id)
                    CopyTraderManager.stop_trade(trader, finish_type=CopyTraderHistory.FinishType.SYSTEM)
                current_app.logger.warning(f"system_stop_inactive_trader trader {stop_user_id} stop_success")
                time.sleep(10)  # 避免短时间内产生大量结束任务，影响其他任务
            except Exception as _e:
                current_app.logger.exception(f"system_stop_inactive_trader {stop_user_id} error {_e!r}")
                db.session.rollback()
