# -*- coding: utf-8 -*-
from decimal import Decimal

from flask import current_app

from app.models import db
from app.models.copy_trading import CopyTransferHistory
from app.business import (
    ServerClient, PerpetualServerClient, BalanceBusiness, ServerResponseCode, PerpetualResponseCode,
)
from app.utils import now


TRANSFER_TYPE_BUSINESS_MAP = {
    # 划转记录类型: { from: 转入账户流水类型, to: 转出账户流水类型  }
    CopyTransferHistory.Type.TRADER_TRANSFER_IN: {
        "from": BalanceBusiness.COPY_TRADING_TRANSFER,
        "to": BalanceBusiness.COPY_TRADING_TRANSFER,
    },
    CopyTransferHistory.Type.TRADER_TRANSFER_OUT: {
        "from": BalanceBusiness.COPY_TRADING_TRANSFER,
        "to": BalanceBusiness.COPY_TRADING_TRANSFER,
    },
    CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN: {
        "from": BalanceBusiness.COPY_TRADING_TRANSFER,
        "to": BalanceBusiness.COPY_TRADING_TRANSFER,
    },
    CopyTransferHistory.Type.FOLLOWER_TRANSFER_OUT: {
        "from": BalanceBusiness.COPY_TRADING_TRANSFER,
        "to": BalanceBusiness.COPY_TRADING_TRANSFER,
    },
    CopyTransferHistory.Type.FOLLOWER_PROFIT_TRANSFER_OUT: {
        "from": BalanceBusiness.COPY_PROFIT_SETTLEMENT,
        "to": BalanceBusiness.COPY_PROFIT_SETTLEMENT,
    },
}


class CopyTransferHelper:
    @classmethod
    def transfer(
        cls,
        main_user_id: int,
        sub_id: int,
        history_id: int,
        transfer_type: CopyTransferHistory.Type,
        asset: str,
        amount: Decimal,
    ) -> CopyTransferHistory:
        """ 带单人or跟单人的资金划转 """
        row = cls.create_transfer(main_user_id, sub_id, history_id, transfer_type, asset, amount)
        cls.transfer_by_history(row)
        return row

    @classmethod
    def create_transfer(
        cls,
        main_user_id: int,
        sub_id: int,
        history_id: int,
        transfer_type: CopyTransferHistory.Type,
        asset: str,
        amount: Decimal,
    ) -> CopyTransferHistory:
        assert amount > 0
        if transfer_type in [
            CopyTransferHistory.Type.TRADER_TRANSFER_IN,
            CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN,
        ]:
            # 转入：主-现货 -> 子-合约
            from_user_id = main_user_id
            to_user_id = sub_id
            from_account_type = CopyTransferHistory.AccountType.SPOT
            to_account_type = CopyTransferHistory.AccountType.PERPETUAL
        elif transfer_type in [
            CopyTransferHistory.Type.TRADER_TRANSFER_OUT,
            CopyTransferHistory.Type.FOLLOWER_TRANSFER_OUT,
        ]:
            # 转出：子-合约 -> 主-现货
            from_user_id = sub_id
            to_user_id = main_user_id
            from_account_type = CopyTransferHistory.AccountType.PERPETUAL
            to_account_type = CopyTransferHistory.AccountType.SPOT
        elif transfer_type in [
            CopyTransferHistory.Type.FOLLOWER_PROFIT_TRANSFER_OUT,
        ]:
            # 转出分润：子-合约 -> 子-合约
            from_user_id = main_user_id
            to_user_id = sub_id
            from_account_type = CopyTransferHistory.AccountType.PERPETUAL
            to_account_type = CopyTransferHistory.AccountType.PERPETUAL
        else:
            raise
        row = CopyTransferHistory(
            main_user_id=main_user_id,
            history_id=history_id,
            from_user_id=from_user_id,
            to_user_id=to_user_id,
            from_account_type=from_account_type,
            to_account_type=to_account_type,
            type=transfer_type,
            asset=asset,
            amount=amount,
        )
        db.session.add(row)
        db.session.commit()
        return row

    @classmethod
    def create_profit_transfer(
            cls,
            main_user_id: int,
            from_user_id: int,
            to_user_id: int,
            history_id: int,
            transfer_type: CopyTransferHistory.Type,
            asset: str,
            amount: Decimal,
    ) -> CopyTransferHistory:
        # 转出分润：跟单子-合约 -> 带单主-现货
        from_account_type = CopyTransferHistory.AccountType.PERPETUAL
        to_account_type = CopyTransferHistory.AccountType.SPOT
        row = CopyTransferHistory(
            main_user_id=main_user_id,
            history_id=history_id,
            from_user_id=from_user_id,
            to_user_id=to_user_id,
            from_account_type=from_account_type,
            to_account_type=to_account_type,
            type=transfer_type,
            asset=asset,
            amount=amount,
        )
        return row

    @classmethod
    def transfer_by_history(cls, row: CopyTransferHistory, on_finished_commit: bool = True):
        """ 执行划转（先扣后加） """
        if row.status not in [
            CopyTransferHistory.Status.CREATED,
            CopyTransferHistory.Status.DEDUCTED,
        ]:
            raise ValueError(f"CopyTransferHistory {row.id} status error: {row.status}")

        client = ServerClient()
        p_client = PerpetualServerClient()
        business_map = TRANSFER_TYPE_BUSINESS_MAP[row.type]
        from_business = business_map["from"]
        to_business = business_map["to"]
        assert from_business and to_business

        asset = row.asset
        amount = row.amount
        from_account_type = row.from_account_type
        to_account_type = row.to_account_type
        from_user_id = row.from_user_id
        to_user_id = row.to_user_id

        if row.status == CopyTransferHistory.Status.CREATED:
            try:
                if from_account_type == CopyTransferHistory.AccountType.SPOT:
                    client.add_user_balance(from_user_id, asset, -amount, from_business, row.id)
                elif from_account_type == CopyTransferHistory.AccountType.PERPETUAL:
                    p_client.add_user_balance(from_user_id, asset, -amount, from_business, row.id)
                else:
                    raise ValueError("unknown account_type")
                row.status = CopyTransferHistory.Status.DEDUCTED
                row.deducted_at = now()
                db.session.commit()
            except ServerClient.BadResponse as _e:
                if (_e.code == ServerResponseCode.INSUFFICIENT_BALANCE
                        or _e.code == PerpetualResponseCode.CONTRACT_BALANCE_NOT_ENOUGH):
                    row.status = CopyTransferHistory.Status.FAILED
                    db.session.commit()
                    current_app.logger.error(f'CopyTransferHistory {row.id} deduct failed BALANCE_NOT_ENOUGH')
                raise
            except Exception as e:
                current_app.logger.error(f'CopyTransferHistory {row.id} deduct failed: {e!r}')
                raise

        if row.status == CopyTransferHistory.Status.DEDUCTED:
            if to_account_type == CopyTransferHistory.AccountType.SPOT:
                client.add_user_balance(to_user_id, asset, amount, to_business, row.id)
            elif to_account_type == CopyTransferHistory.AccountType.PERPETUAL:
                p_client.add_user_balance(to_user_id, asset, amount, to_business, row.id)
            else:
                raise ValueError("unknown account_type")
            row.status = CopyTransferHistory.Status.FINISHED
            row.finished_at = now()
            if on_finished_commit:
                db.session.commit()

    @classmethod
    def create_finished_coupon_transfer_his(
        cls,
        main_user_id: int,
        sub_user_id: int,
        sys_user_id: int,
        history_id: int,
        transfer_type: CopyTransferHistory.Type,
        asset: str,
        amount: Decimal,
    ) -> CopyTransferHistory:
        assert amount > 0
        if transfer_type in [
            CopyTransferHistory.Type.TRADER_COUPON_USE,
            CopyTransferHistory.Type.FOLLOWER_COUPON_USE,
        ]:
            # 卡券使用：系统帐号-合约 -> 子-合约
            from_user_id = sys_user_id
            to_user_id = sub_user_id
            from_account_type = CopyTransferHistory.AccountType.PERPETUAL
            to_account_type = CopyTransferHistory.AccountType.PERPETUAL
        elif transfer_type in [
            CopyTransferHistory.Type.TRADER_COUPON_RECYCLE,
            CopyTransferHistory.Type.FOLLOWER_COUPON_RECYCLE,
        ]:
            # 卡券回收：子-合约 -> 系统帐号-合约
            from_user_id = sub_user_id
            to_user_id = sys_user_id
            from_account_type = CopyTransferHistory.AccountType.PERPETUAL
            to_account_type = CopyTransferHistory.AccountType.PERPETUAL
        else:
            raise
        now_ = now()
        row = CopyTransferHistory(
            main_user_id=main_user_id,
            history_id=history_id,
            from_user_id=from_user_id,
            to_user_id=to_user_id,
            from_account_type=from_account_type,
            to_account_type=to_account_type,
            type=transfer_type,
            asset=asset,
            amount=amount,
            status=CopyTransferHistory.Status.FINISHED,
            deducted_at=now_,
            finished_at=now_,
        )
        db.session.add(row)
        return row
