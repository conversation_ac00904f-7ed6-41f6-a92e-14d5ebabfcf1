# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from functools import cached_property

from sqlalchemy import func

from app.models import db
from app.models.copy_trading import (
    TimeRangeEnum, CopyTraderUser, CopyTraderHistory, CopyFollowerHistory, CopyTransferHistory,
    CopyTraderStatistics, DailyCopyTraderStatistics,
    DailyCopyTraderMarketStatistics, DailyCopyFollowerStatistics,
    CopyFollowerStatistics, CopyTraderProfitShareDetail
)
from app.business import PerpetualServerClient
from app.business.copy_trading.base import CopyRelationQuerier
from app.business.copy_trading.position import (
    query_users_time_range_positions,
    query_users_last_trade_time,
)
from app.utils import quantize_amount, batch_iter
from app.utils.date_ import now, today, date_to_datetime


class CopyStatistician:
    def __init__(self, date_: date):
        self.date = date_
        self.start_time = date_to_datetime(date_)
        self.end_time = min(now(), self.start_time + timedelta(days=1))

    @classmethod
    def get_subs_balance_data(cls, sub_ids: set[int]) -> dict[int, list]:
        """ 查询子帐号余额信息 """
        asset_ = "USDT"
        client = PerpetualServerClient()
        user_data_map = {}  # {user_id: [equity, profit_unreal]
        for ch_ids in batch_iter(sub_ids, 50):
            ch_res = client.get_users_balances(asset_, ch_ids)
            for uid, asset_bl in ch_res.items():
                bl = asset_bl.get(asset_, {})
                if bl:
                    _profit_unreal = Decimal(bl['profit_unreal'])
                    _equity = Decimal(bl['balance_total']) + Decimal(bl['margin']) + _profit_unreal
                    data_ = [_equity, _profit_unreal]
                else:
                    data_ = [Decimal(), Decimal()]
                user_data_map[uid] = data_
        return user_data_map

    @classmethod
    def get_trade_his_tran_in_amount_data(cls, trade_his_ids: set[int]) -> dict[int, Decimal]:
        """ 查时间范围内的的带单资金划转_转入，带单历史维度 """
        rows: list[CopyTransferHistory] = CopyTransferHistory.query.filter(
            CopyTransferHistory.history_id.in_(trade_his_ids),
            CopyTransferHistory.type == CopyTransferHistory.Type.TRADER_TRANSFER_IN,
            CopyTransferHistory.status == CopyTransferHistory.Status.FINISHED,
        ).with_entities(
            CopyTransferHistory.history_id,
            CopyTransferHistory.amount,
        ).all()
        trade_his_amount_map = defaultdict(Decimal)
        for r in rows:
            trade_his_amount_map[r.history_id] += r.amount
        return trade_his_amount_map

    @classmethod
    def get_follow_his_tran_in_amount_data(cls, follow_his_ids: set[int]) -> dict[int, Decimal]:
        """ 查时间范围内的的跟单资金划转_转入，跟单历史维度 """
        rows: list[CopyTransferHistory] = CopyTransferHistory.query.filter(
            CopyTransferHistory.history_id.in_(follow_his_ids),
            CopyTransferHistory.type == CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN,
            CopyTransferHistory.status == CopyTransferHistory.Status.FINISHED,
        ).with_entities(
            CopyTransferHistory.history_id,
            CopyTransferHistory.amount,
        ).all()
        fol_his_amount_map = defaultdict(Decimal)
        for r in rows:
            fol_his_amount_map[r.history_id] += r.amount
        return fol_his_amount_map

    @cached_property
    def trade_his_rows(self) -> list[CopyTraderHistory]:
        """ 查询时间范围内的带单历史 """
        return CopyRelationQuerier.get_time_range_trade_history(self.start_time, self.end_time)

    @cached_property
    def follow_his_rows(self) -> list[CopyFollowerHistory]:
        """ 查询时间范围内的跟单历史 """
        return CopyRelationQuerier.get_time_range_follow_history(self.start_time, self.end_time)

    def gen_daily_trader_market_statistics(self, force_update: bool = False):
        """ 带单人每日的合约市场交易偏好，只统计已平仓的仓位 """
        trade_his_rows = self.trade_his_rows
        if not trade_his_rows:
            return

        model = DailyCopyTraderMarketStatistics
        if not force_update and model.query.filter(model.date == self.date).first():
            return

        trader_sub_main_map = {}
        for r in trade_his_rows:
            r: CopyTraderHistory
            trader_sub_main_map[r.sub_user_id] = r.user_id
        trader_sub_ids = set(trader_sub_main_map.keys())
        min_created_at = min([i.created_at for i in trade_his_rows]) if trade_his_rows else None

        trader_market_summary_map = defaultdict(lambda: {
            "trade_count": 0,
            "profit_amount": Decimal(),
        })
        sub_fin_positions_map, _ = query_users_time_range_positions(
            trader_sub_ids, self.start_time, self.end_time, min_created_at,
        )
        for ps in sub_fin_positions_map.values():
            for p in ps:
                sub_id = p["user_id"]
                market = p["market"]
                trader_user_id = trader_sub_main_map[sub_id]
                key_ = (trader_user_id, market)
                market_sm = trader_market_summary_map[key_]
                market_sm["trade_count"] += 1
                market_sm["profit_amount"] += Decimal(p["profit_real"])

        daily_market_row_map = {
            (i.user_id, i.market): i for i in
            model.query.filter(model.date == self.date).all()
        }
        for key_, market_summary in trader_market_summary_map.items():
            if key_ not in daily_market_row_map:
                trader_user_id, market = key_
                r = model(user_id=trader_user_id, date=self.date, market=market)
            else:
                r = daily_market_row_map[key_]
            r: model
            r.trade_count = market_summary["trade_count"]
            r.profit_amount = quantize_amount(market_summary["profit_amount"], 8)
            db.session.add(r)
        db.session.commit()

    def update_daily_follower_statistics(self, follow_his_rows: list[CopyFollowerHistory]):
        """ 更新跟单人每日统计 """
        if not follow_his_rows:
            return

        # sub_id可能在当天不同的时间段跟了不同的带单人
        follow_his_ids = set()
        follower_sub_his_rows_map = defaultdict(list)
        for fol_his in follow_his_rows:
            follow_his_ids.add(fol_his.id)
            follower_sub_his_rows_map[fol_his.sub_user_id].append(fol_his)

        min_created_at = min([i.created_at for i in follow_his_rows]) if follow_his_rows else None
        fol_sub_ids = set(follower_sub_his_rows_map.keys())
        sub_fin_positions_map, sub_ped_positions_map = query_users_time_range_positions(
            fol_sub_ids, self.start_time, self.end_time, min_created_at,
        )
        follow_his_margin_amount_map = self.get_follow_his_tran_in_amount_data(follow_his_ids)
        sub_balance_data_map = self.get_subs_balance_data(fol_sub_ids)
        fol_sub_equity_amount_map = {
            _sub_id: _data[0] for _sub_id, _data in sub_balance_data_map.items()
        }
        follow_his_data_map = defaultdict(lambda: {
            "profit_amount": Decimal(),
            "pending_profit_amount": Decimal(),
            "margin_amount": Decimal(),
        })
        for sub_id in fol_sub_ids:
            sub_fin_ps = sub_fin_positions_map[sub_id]
            sub_ped_ps = sub_ped_positions_map[sub_id]
            sub_his_rows = follower_sub_his_rows_map[sub_id]
            fol_his_pos_map = self._split_positions_by_fol_his(sub_fin_ps, sub_ped_ps, sub_his_rows)
            for _f_his in sub_his_rows:
                _f_his: CopyFollowerHistory
                _fol_is_running = _f_his.status != CopyFollowerHistory.Status.FINISHED
                _sub_fin_ps, _sub_ped_ps = fol_his_pos_map.get(_f_his.id, [[], []])
                _f_info = self._summary_finished_positions_info(_sub_fin_ps)
                _p_info = self._summary_pending_positions_info(_sub_ped_ps)
                fol_his_d = follow_his_data_map[_f_his.id]
                fol_his_d['profit_amount'] = _f_info[0]
                fol_his_d['pending_profit_amount'] = _p_info[0]
                fol_sub_margin_amount = follow_his_margin_amount_map[_f_his.id]
                fol_his_d['margin_amount'] = fol_sub_margin_amount
                if _fol_is_running:
                    fol_sub_equity_amount = fol_sub_equity_amount_map.get(_f_his.sub_user_id, Decimal())
                else:
                    fol_sub_equity_amount = Decimal()
                fol_his_d['equity'] = fol_sub_equity_amount

        ago_date = self.date - timedelta(days=1)
        ago_fol_his_daily_row_map = {
            i.history_id: i for i in
            DailyCopyFollowerStatistics.query.filter(
                DailyCopyFollowerStatistics.date == ago_date,
            ).with_entities(
                DailyCopyFollowerStatistics.history_id,
                DailyCopyFollowerStatistics.profit_amount,
            ).all()
        }
        fol_his_daily_row_map = {
            i.history_id: i for i in
            DailyCopyFollowerStatistics.query.filter(DailyCopyFollowerStatistics.date == self.date).all()
        }
        for fol_his in follow_his_rows:
            his_id = fol_his.id
            if his_id not in fol_his_daily_row_map:
                r = DailyCopyFollowerStatistics(history_id=his_id, date=self.date)
                r.user_id = fol_his.user_id
                r.sub_user_id = fol_his.sub_user_id
                fol_his_daily_row_map[his_id] = r
            else:
                r = fol_his_daily_row_map[his_id]
            r: DailyCopyFollowerStatistics
            ago_r: DailyCopyFollowerStatistics = ago_fol_his_daily_row_map.get(his_id)
            fh_data = follow_his_data_map[fol_his.id]
            r.delta_profit_amount = quantize_amount(fh_data["profit_amount"], 8)
            r.delta_pending_profit = quantize_amount(fh_data["pending_profit_amount"], 8)
            if ago_r:
                profit_amount = r.delta_profit_amount + ago_r.profit_amount
            else:
                profit_amount = r.delta_profit_amount
            r.profit_amount = profit_amount
            r.margin_amount = fh_data["margin_amount"]
            r.equity = fh_data["equity"]
            db.session.add(r)
        db.session.commit()

    def update_daily_trader_statistics(
        self,
        trade_his_rows: list[CopyTraderHistory],
        follow_his_rows: list[CopyFollowerHistory],
    ):
        """ 更新带单人每日统计，部分跟单字段从跟单每日统计取 """
        if not trade_his_rows:
            return

        # 带单人用最新的带单历史来统计
        trader_his_rows_map = defaultdict(list)
        latest_trader_his_rows: list[CopyTraderHistory] = []
        for trade_his in trade_his_rows:
            trader_his_rows_map[trade_his.user_id].append(trade_his)
        for v in trader_his_rows_map.values():
            v.sort(key=lambda x: x.id)
            latest_trader_his_rows.append(v[-1])

        latest_trader_his_ids = set()
        trader_sub_latest_his_map = {}
        trader_latest_his_map = {}
        for trade_his in latest_trader_his_rows:
            latest_trader_his_ids.add(trade_his.id)
            trader_sub_latest_his_map[trade_his.sub_user_id] = trade_his
            trader_latest_his_map[trade_his.user_id] = trade_his
        trade_his_margin_amount_map = self.get_trade_his_tran_in_amount_data(latest_trader_his_ids)

        fol_his_daily_st_map: dict[int, DailyCopyFollowerStatistics] = {
            i.history_id: i for i in
            DailyCopyFollowerStatistics.query.filter(DailyCopyFollowerStatistics.date == self.date).all()
        }
        trade_his_follow_rows_map = defaultdict(list)
        following_fol_sub_ids = set()
        for fol_his in follow_his_rows:
            trade_his_follow_rows_map[fol_his.trader_history_id].append(fol_his)
            if fol_his.status == fol_his.Status.FOLLOWING:
                following_fol_sub_ids.add(fol_his.sub_user_id)

        min_created_at = min([i.created_at for i in latest_trader_his_rows]) if latest_trader_his_rows else None
        trader_sub_ids = set(trader_sub_latest_his_map.keys())
        sub_fin_positions_map, sub_ped_positions_map = query_users_time_range_positions(
            trader_sub_ids, self.start_time, self.end_time, min_created_at
        )

        query_bl_sub_ids = trader_sub_ids | following_fol_sub_ids
        sub_balance_data_map = self.get_subs_balance_data(query_bl_sub_ids)
        trade_sub_equity_amount_map = {
            _sub_id: _data[0] for _sub_id, _data in sub_balance_data_map.items()
            if _sub_id in trader_sub_ids
        }
        sub_unreal_profit_map = {
            _sub_id: _data[1] for _sub_id, _data in sub_balance_data_map.items()
        }
        sub_last_trade_time = query_users_last_trade_time(trader_sub_ids, self.start_time, self.end_time)
        trader_data_map = defaultdict(lambda: {
            "trade_count": 0,
            "profit_count": 0,
            "loss_count": 0,
            "equity": Decimal(),
            "margin_amount": Decimal(),
            "profit_amount": Decimal(),
            "pending_profit_amount": Decimal(),
            "follower_profit_amount": Decimal(),
            "follower_pending_profit_amount": Decimal(),
            "follower_margin_amount": Decimal(),
            "follower_equity": Decimal(),
            "total_profit_amount": Decimal(),
            "last_position_time": None,
        })
        for sub_id in trader_sub_ids:
            trade_his = trader_sub_latest_his_map[sub_id]
            sub_fin_ps = sub_fin_positions_map[sub_id]
            sub_ped_ps = sub_ped_positions_map[sub_id]
            _f_info = self._summary_finished_positions_info(sub_fin_ps)
            _p_info = self._summary_pending_positions_info(sub_ped_ps)
            trader_user_id = trade_his.user_id
            smm_d = trader_data_map[trader_user_id]
            smm_d['trade_count'] += len(sub_fin_ps)
            smm_d['profit_amount'] += _f_info[0]
            smm_d['profit_count'] += _f_info[1]
            smm_d['loss_count'] += _f_info[2]
            smm_d['margin_amount'] = trade_his_margin_amount_map[trade_his.id]
            smm_d['equity'] = trade_sub_equity_amount_map.get(sub_id, Decimal())
            smm_d['pending_profit_amount'] += _p_info[0]  # 当前仓位的已实现盈亏
            smm_d['pending_profit_amount'] += sub_unreal_profit_map.get(sub_id, Decimal())  # 当前仓位的未实现盈亏
            last_trade_time = sub_last_trade_time.get(sub_id)
            if last_trade_time and smm_d['last_position_time']:
                smm_d['last_position_time'] = max(last_trade_time, smm_d['last_position_time'])
            elif last_trade_time:
                smm_d['last_position_time'] = last_trade_time

            follow_his_rows = trade_his_follow_rows_map[trade_his.id]
            for fol_his in follow_his_rows:
                fol_st = fol_his_daily_st_map.get(fol_his.id)
                if not fol_st:
                    continue
                smm_d['follower_profit_amount'] += fol_st.delta_profit_amount
                smm_d['follower_pending_profit_amount'] += fol_st.delta_pending_profit
                if fol_his.status == CopyFollowerHistory.Status.FOLLOWING:
                    fol_sub_unreal_pm = sub_unreal_profit_map.get(fol_his.sub_user_id, Decimal())
                    smm_d['follower_pending_profit_amount'] += fol_sub_unreal_pm
                    smm_d['follower_margin_amount'] += fol_st.margin_amount
                    smm_d['follower_equity'] += fol_st.equity

        ago_date = self.date - timedelta(days=1)
        ago_daily_row_map = {
            i.user_id: i for i in
            DailyCopyTraderStatistics.query.filter(
                DailyCopyTraderStatistics.date == ago_date,
            ).with_entities(
                DailyCopyTraderStatistics.user_id,
                DailyCopyTraderStatistics.date,
                DailyCopyTraderStatistics.profit_amount,
                DailyCopyTraderStatistics.follower_profit_amount,
                DailyCopyTraderStatistics.total_profit_amount,
            ).all()
        }
        daily_row_map = {
            i.user_id: i for i in
            DailyCopyTraderStatistics.query.filter(DailyCopyTraderStatistics.date == self.date).all()
        }
        for trader_user_id, data_ in trader_data_map.items():
            trade_his_: CopyTraderHistory = trader_latest_his_map[trader_user_id]
            if trader_user_id not in daily_row_map:
                r = DailyCopyTraderStatistics(user_id=trader_user_id, date=self.date)
                daily_row_map[trader_user_id] = r
            else:
                r = daily_row_map[trader_user_id]
            ago_r: DailyCopyTraderStatistics = ago_daily_row_map.get(trader_user_id)
            r: DailyCopyTraderStatistics
            r.trade_count = data_["trade_count"]
            r.profit_count = data_["profit_count"]
            r.loss_count = data_["loss_count"]
            r.delta_profit_amount = quantize_amount(data_["profit_amount"], 8)
            r.delta_pending_profit = quantize_amount(data_["pending_profit_amount"], 8)
            r.delta_follower_profit_amount = quantize_amount(data_["follower_profit_amount"], 8)
            r.delta_follower_pending_profit = quantize_amount(data_["follower_pending_profit_amount"], 8)
            r.delta_total_profit_amount = r.delta_profit_amount + r.delta_follower_profit_amount
            if ago_r and ago_r.date >= trade_his_.started_at.date():
                profit_amount = r.delta_profit_amount + ago_r.profit_amount
                follower_profit_amount = r.delta_follower_profit_amount + ago_r.follower_profit_amount
                total_profit_amount = r.delta_total_profit_amount + ago_r.total_profit_amount
            else:
                profit_amount = r.delta_profit_amount
                follower_profit_amount = r.delta_follower_profit_amount
                total_profit_amount = r.delta_total_profit_amount
            r.profit_amount = profit_amount
            r.margin_amount = data_["margin_amount"]
            r.equity = data_["equity"]
            r.follower_profit_amount = follower_profit_amount
            r.follower_margin_amount = data_["follower_margin_amount"]
            r.total_profit_amount = total_profit_amount
            trader_total_profit = r.profit_amount + r.delta_pending_profit
            profit_rate = (
                quantize_amount(trader_total_profit / r.margin_amount, 8) if r.margin_amount else Decimal()
            )
            if r.id:
                r.max_profit_rate = max(profit_rate, r.profit_rate)
            else:
                r.max_profit_rate = profit_rate
            r.profit_rate = profit_rate
            r.aum = max(r.equity + data_['follower_equity'], Decimal())
            r.last_position_time = data_["last_position_time"]
            db.session.add(r)
        db.session.commit()

    def update_daily_statistics(self):
        """ 更新每日统计 """
        trade_his_rows = self.trade_his_rows
        follow_his_rows = self.follow_his_rows
        self.update_daily_follower_statistics(follow_his_rows)
        self.update_daily_trader_statistics(trade_his_rows, follow_his_rows)

    @classmethod
    def _split_positions_by_fol_his(cls, sub_fin_ps: list[dict], sub_ped_ps: list[dict], his_rows: list) -> dict:
        # 按仓位的创建时间处理，属于最近的跟单
        his_rows.sort(key=lambda x: x.id)
        result = {i.id: [[], []] for i in his_rows}
        if not his_rows:
            return result
        for f_pos in sub_fin_ps:
            ct = float(f_pos['create_time'])
            _ago_rows = [r for r in his_rows if r.created_at.timestamp() <= ct]
            if _ago_rows:
                _ago_id = _ago_rows[-1].id
            else:
                continue
            result[_ago_id][0].append(f_pos)
        for p_pos in sub_ped_ps:
            ct = float(p_pos['create_time'])
            _ago_rows = [r for r in his_rows if r.created_at.timestamp() <= ct]
            if _ago_rows:
                _ago_id = _ago_rows[-1].id
            else:
                continue
            result[_ago_id][1].append(p_pos)
        return result

    @classmethod
    def _summary_finished_positions_info(
        cls,
        positions: list[dict],
    ) -> tuple[Decimal, int, int]:
        # SUM(盈亏数)，盈利仓位个数，亏损仓位个数
        total_profit_amount = Decimal(0)
        profit_count = loss_count = 0
        for p in positions:
            profit_amount = Decimal(p["profit_real"])
            total_profit_amount += profit_amount
            if profit_amount > Decimal(0):
                profit_count += 1
            else:
                loss_count += 1
        return total_profit_amount, profit_count, loss_count

    @classmethod
    def _summary_pending_positions_info(cls, positions: list[dict]) -> tuple[Decimal]:
        # SUM(当前仓位的已实现盈亏)
        total_profit_amount = Decimal()
        for p in positions:
            profit_amount = Decimal(p["profit_real"])
            total_profit_amount += profit_amount
        return total_profit_amount,

    @classmethod
    def update_summary(cls):
        cls.update_follower_summary()
        cls.update_trader_summary()

    @classmethod
    def update_trader_summary(cls, trader_user_ids: list[int] = None):
        today_ = today()
        q = CopyTraderUser.query.filter(
            CopyTraderUser.status != CopyTraderUser.Status.DELETED,
        )
        if trader_user_ids:
            q = q.filter(CopyTraderUser.user_id.in_(trader_user_ids))
        all_traders: list[CopyTraderUser] = q.all()
        to_summary_traders: list[CopyTraderUser] = []
        for trader in all_traders:
            if trader.last_finished_at and trader.last_finished_at.date() + timedelta(days=3) < today_:
                # 最后一次带单的结束时间是N天前，则不需要再更新汇总数据了
                continue
            to_summary_traders.append(trader)
        if not to_summary_traders:
            return

        # 用户重新发起带单时，所有盈亏统计数据清空，从用户重新成为交易员当天开始计算
        trader_map = {i.user_id: i for i in to_summary_traders}
        trader_user_ids = [i.user_id for i in to_summary_traders]
        min_start_dt = min([i.last_started_at.date() for i in to_summary_traders])
        daily_rows = DailyCopyTraderStatistics.query.filter(
            DailyCopyTraderStatistics.user_id.in_(trader_user_ids),
            DailyCopyTraderStatistics.date >= min_start_dt,
        ).order_by(DailyCopyTraderStatistics.date.asc()).all()
        max_dt = max([i.date for i in daily_rows] or [today_])

        time_range_days_map = {
            TimeRangeEnum.DAY7: 7,
            TimeRangeEnum.DAY30: 30,
            TimeRangeEnum.DAY90: 90,
        }
        trader_tr_daily_rows_map = defaultdict(list)
        for r in daily_rows:
            trader = trader_map[r.user_id]
            if trader.last_started_at.date() > r.date:
                # 之前的带单统计
                continue
            trader_tr_daily_rows_map[(r.user_id, TimeRangeEnum.ALL)].append(r)
            for tr, days in time_range_days_map.items():
                if max_dt - timedelta(days=days) <= r.date:
                    trader_tr_daily_rows_map[(r.user_id, tr)].append(r)

        st_model = CopyTraderStatistics
        statistics_map = {
            (i.user_id, i.time_range): i for i in st_model.query.all()
        }
        sum_all_data_map = cls._summary_all_daily_trader_statistics_info(to_summary_traders)
        for trader in to_summary_traders:
            trader_user_id = trader.user_id
            for tr in TimeRangeEnum:
                key_ = (trader_user_id, tr)
                if key_ not in statistics_map:
                    st_row = st_model(user_id=trader_user_id, time_range=tr)
                    statistics_map[trader_user_id] = st_row
                else:
                    st_row = statistics_map[key_]

                tr_daily_rows = trader_tr_daily_rows_map[key_]
                _s_info = cls._summary_daily_trader_statistics_info(tr_daily_rows)
                for k, v in _s_info.items():
                    setattr(st_row, k, v)
                _all_info = sum_all_data_map[trader_user_id]
                for k, v in _all_info.items():
                    setattr(st_row, k, v)
                st_row.last_update_at = today_
                db.session.add(st_row)
            db.session.commit()
        db.session.commit()

    @classmethod
    def _summary_all_daily_trader_statistics_info(cls, traders: list[CopyTraderUser]) -> dict[int, dict]:
        """ 汇总全部的每日统计 """
        trader_user_ids = {i.user_id for i in traders}
        trader_data_map = defaultdict(lambda: {
            "trade_count": 0,
        })
        for ch_ids in batch_iter(trader_user_ids, 5000):
            ch_rows = DailyCopyTraderStatistics.query.filter(
                DailyCopyTraderStatistics.user_id.in_(ch_ids),
            ).group_by(
                DailyCopyTraderStatistics.user_id,
            ).with_entities(
                DailyCopyTraderStatistics.user_id,
                func.sum(DailyCopyTraderStatistics.trade_count).label("trade_count"),
            ).all()
            for r in ch_rows:
                trader_data_map[r.user_id]['trade_count'] = r.trade_count
        return trader_data_map

    @classmethod
    def _summary_daily_trader_statistics_info(cls, daily_rows: list[DailyCopyTraderStatistics]) -> dict:
        """ 汇总每日统计 """
        zero = Decimal()
        daily_rows.sort(key=lambda x: x.date)
        if daily_rows:
            # 部分字段取最新的
            last_d_row = daily_rows[-1]
            aum = last_d_row.aum
            equity = last_d_row.equity
            margin_amount = last_d_row.margin_amount
            delta_pending_profit = last_d_row.delta_pending_profit
            delta_follower_pending_profit = last_d_row.delta_follower_pending_profit
        else:
            aum = zero
            equity = zero
            margin_amount = zero
            delta_pending_profit = zero
            delta_follower_pending_profit = zero

        trade_days = len(daily_rows)
        trade_count = profit_count = loss_count = 0
        profit_amount = delta_pending_profit  # 交易员盈亏：历史仓位盈亏 + 当前仓位盈亏
        fol_profit_amount = delta_follower_pending_profit  # 跟单员盈亏：历史仓位盈亏 + 当前仓位盈亏
        last_position_times = []
        for r in daily_rows:
            trade_count += r.trade_count
            profit_count += r.profit_count
            loss_count += r.loss_count
            profit_amount += r.delta_profit_amount
            fol_profit_amount += r.delta_follower_profit_amount
            if r.last_position_time:
                last_position_times.append(r.last_position_time)
        last_trade_at = max(last_position_times) if last_position_times else None
        position_count = profit_count + loss_count
        winning_rate = quantize_amount(profit_count/position_count if position_count else 0, 4)

        mdd = zero
        if daily_rows:
            # 最大回撤率 = 周期内 Max (Abs( 最大收益率 - 當前收益率 ) / ( 最大收益率 + 1) )
            max_profit_rate = max([r.max_profit_rate for r in daily_rows])
            v2 = max_profit_rate + Decimal(1)
            if v2 != zero:
                last_profit_rate = daily_rows[-1].profit_rate
                mdd = abs((max_profit_rate - last_profit_rate) / v2)

        if margin_amount != zero:
            profit_rate = quantize_amount(profit_amount / margin_amount, 8)
        else:
            profit_rate = zero

        return {
            "trade_days": trade_days,
            "trade_count": trade_count,
            "profit_count": profit_count,
            "loss_count": loss_count,
            "winning_rate": winning_rate,
            "aum": aum,
            "equity": equity,
            "margin_amount": margin_amount,
            "profit_amount": profit_amount,
            "follower_profit_amount": fol_profit_amount,
            "total_profit_amount": profit_amount + fol_profit_amount,
            "profit_rate": profit_rate,
            "mdd": mdd,
            "last_trade_at": last_trade_at,
        }

    @classmethod
    def update_summary_profit_share_amount(cls):
        """ 更新带单人的分润总收益 """
        st_model = CopyTraderStatistics
        st_rows = st_model.query.all()
        sum_rows = CopyTraderProfitShareDetail.query.group_by(
            CopyTraderProfitShareDetail.copy_trader_user_id,
        ).with_entities(
            CopyTraderProfitShareDetail.copy_trader_user_id,
            func.sum(CopyTraderProfitShareDetail.amount).label("amount"),
        ).all()
        trader_profit_share_amount_map = dict(sum_rows)
        for r in st_rows:
            r: st_model
            r.profit_share_amount = trader_profit_share_amount_map.get(r.user_id, 0)
        db.session.commit()

    @classmethod
    def update_follower_summary(cls):
        today_ = today()
        st_model = CopyFollowerStatistics

        q_id_rows = DailyCopyFollowerStatistics.query.group_by(
            DailyCopyFollowerStatistics.history_id,
        ).with_entities(
            DailyCopyFollowerStatistics.history_id,
            func.max(DailyCopyFollowerStatistics.id).label('id'),
        ).all()
        row_ids = {i.id for i in q_id_rows}
        profit_amount_map = defaultdict(Decimal)
        his_profit_amount_map = defaultdict(Decimal)
        for ch_ids in batch_iter(row_ids, 5000):
            ch_rows = DailyCopyFollowerStatistics.query.filter(
                DailyCopyFollowerStatistics.id.in_(ch_ids),
            ).with_entities(
                DailyCopyFollowerStatistics.user_id,
                DailyCopyFollowerStatistics.history_id,
                DailyCopyFollowerStatistics.profit_amount,
                DailyCopyFollowerStatistics.delta_pending_profit,
            ).all()
            for r in ch_rows:
                profit_amount_map[r.user_id] += r.profit_amount + r.delta_pending_profit
                his_profit_amount_map[r.history_id] += r.profit_amount + r.delta_pending_profit

        start_time = date_to_datetime(today_) - timedelta(days=1)
        end_time = now()
        fol_rows = CopyRelationQuerier.get_time_range_follow_history(start_time, end_time)
        for r in fol_rows:
            r.total_profit_amount = r.total_copy_earn_amount = his_profit_amount_map.get(r.id, 0)
            db.session.add(r)
        db.session.commit()

        statistics_map = {i.user_id: i for i in st_model.query.all()}
        for follower_id, profit_amount in profit_amount_map.items():
            if not (st_row := statistics_map.get(follower_id)):
                st_row = st_model(user_id=follower_id)
                statistics_map[follower_id] = st_row

            st_row.profit_amount = profit_amount
            st_row.last_update_at = today_
            db.session.add(st_row)
        db.session.commit()

    @classmethod
    def update_one_follower_summary(cls, follow_his: CopyFollowerHistory):
        q_id_rows = DailyCopyFollowerStatistics.query.filter(
            DailyCopyFollowerStatistics.user_id == follow_his.user_id,
        ).group_by(
            DailyCopyFollowerStatistics.history_id,
        ).with_entities(
            DailyCopyFollowerStatistics.history_id,
            func.max(DailyCopyFollowerStatistics.id).label('id'),
        ).all()
        row_ids = {i.id for i in q_id_rows}
        total_profit_amount = Decimal(0)
        cur_fol_profit_amount = Decimal(0)
        for ch_ids in batch_iter(row_ids, 5000):
            ch_rows = DailyCopyFollowerStatistics.query.filter(
                DailyCopyFollowerStatistics.id.in_(ch_ids),
            ).with_entities(
                DailyCopyFollowerStatistics.history_id,
                DailyCopyFollowerStatistics.profit_amount,
                DailyCopyFollowerStatistics.delta_pending_profit,
            ).all()
            for r in ch_rows:
                total_profit_amount += r.profit_amount + r.delta_pending_profit
                if r.history_id == follow_his.id:
                    cur_fol_profit_amount = r.profit_amount + r.delta_pending_profit

        follow_his.total_profit_amount = follow_his.total_copy_earn_amount = cur_fol_profit_amount
        st_row = CopyFollowerStatistics.get_or_create(user_id=follow_his.user_id)
        st_row.profit_amount = total_profit_amount
        st_row.last_update_at = today()
        db.session.add(st_row)
        db.session.commit()

    @classmethod
    def update_trade_his_equity(cls, trade_his: CopyTraderHistory):
        """ 交易员划转保证金后，实时更新统计的账户权益数目 """
        zero = Decimal()
        balance_data = cls.get_subs_balance_data({trade_his.sub_user_id}).get(trade_his.sub_user_id, [])
        new_equity = balance_data[0] if balance_data else zero
        daily_r: DailyCopyTraderStatistics = DailyCopyTraderStatistics.query.filter(
            DailyCopyTraderStatistics.user_id == trade_his.user_id,
        ).order_by(DailyCopyTraderStatistics.date.desc()).first()
        if daily_r:
            old_equity = daily_r.equity
            daily_r.equity = new_equity
            daily_r.aum = max(daily_r.aum - old_equity + new_equity, zero)
        st_rows = CopyTraderStatistics.query.filter(
            CopyTraderStatistics.user_id == trade_his.user_id,
        ).all()
        for r in st_rows:
            old_equity = r.equity
            r.equity = new_equity
            r.aum = max(r.aum - old_equity + new_equity, zero)
        db.session.commit()
