# -*- coding: utf-8 -*-
from datetime import timedelta, datetime
from decimal import Decimal

from flask import current_app

from app.business.copy_trading.position import query_users_time_range_finish_positions
from app.business.copy_trading.trader import CopyTraderManager
from app.common import PrecisionEnum, CeleryQueues, ProducerTopics
from app.models import db, User
from app.models.copy_trading import (
    CopyTraderUser, CopyFollowerHistory, UserFavoriteCopyTrader, CopyTransferHistory,
    CopyTraderProfitShareDetail,
)
from app.exceptions import (
    InsufficientBalance, FollowInactiveCopyTraderError, FollowFullCopyTraderError,
    InvalidArgument,
)
from app.business import ServerClient, PerpetualServerClient, lock_call, LockKeys, CacheLock
from app.business.account import AccountTransferLogHelper
from app.business.copy_trading.base import FollowerSubUserManager
from app.business.copy_trading.transfer import CopyTransferHelper
from app.business.copy_trading.message import FollowerMessageSender
from app.utils import now, today, quantize_amount, celery_task, route_module_to_celery_queue
from app.utils.date_ import date_to_datetime, today_datetime, datetime_to_time
from app.producer.kafka_mission import mission_producer


route_module_to_celery_queue(__name__, CeleryQueues.COPY_TRADING)


def _send_copy_trading_msg(follow_his: CopyFollowerHistory):
    message = dict(
        event_data=dict(),
        biz_type=follow_his.__tablename__,
        biz_id=follow_his.id,
        timestamp=datetime_to_time(follow_his.created_at),
        user_id=follow_his.user_id
    )
    mission_producer.send_message(
        ProducerTopics.COPY_TRADING,
        message
    )


class TraderFavoriteHelper:
    """ 带单人的关注相关 """

    model = UserFavoriteCopyTrader

    @classmethod
    def add_favorite(cls, user_id: int, trader_user_id: int):
        """ 对带单人添加关注 """
        model = cls.model
        fv_row: model = model.get_or_create(
            user_id=user_id, copy_trader_user_id=trader_user_id
        )
        fv_row.status = model.Status.VALID
        db.session.add(fv_row)
        db.session.commit()

    @classmethod
    def cancel_favorite(cls, user_id: int, trader_user_id: int):
        """ 对带单人取消关注 """
        model = cls.model
        fv_row: model = model.query.filter(
            model.user_id == user_id,
            model.copy_trader_user_id == trader_user_id,
            model.status == model.Status.VALID,
        ).first()
        if not fv_row:
            return
        fv_row.status = model.Status.DELETED
        db.session.add(fv_row)
        db.session.commit()


class CopyFollowerManager:
    """ 跟单人的相关的操作 """

    def __init__(self, user: User):
        assert not user.is_sub_account
        self.user = user

    @classmethod
    def start_follow(cls, trader: CopyTraderUser, follower_user: User, follow_settings: dict) -> CopyFollowerHistory:
        """ 跟单人-开始跟单 """
        from app.business.copy_trading.trader import CopyTraderManager

        trader_user_id = trader.user_id
        if not trader.is_active:
            raise FollowInactiveCopyTraderError
        if trader.is_full:
            raise FollowFullCopyTraderError
        trade_his = CopyTraderManager.get_last_active_trade_his(trader_user_id)

        follower_user_id = follower_user.id
        f_trader = CopyTraderManager.get_trader(follower_user_id)
        if f_trader and f_trader.is_active:
            # 当前跟单人不能是带单人
            raise InvalidArgument
        if cls.has_trader_active_follow_his(trader_user_id, follower_user_id):
            raise InvalidArgument

        sub_manager = FollowerSubUserManager(follower_user_id)
        f_run_sub = sub_manager.allocate_sub_user()

        follow_his = CopyFollowerHistory(
            trader_history_id=trade_his.id,
            user_id=follower_user_id,
            sub_user_id=f_run_sub.user_id,
            copy_trader_user_id=trader_user_id,
            copy_trader_sub_user_id=trade_his.sub_user_id,
            margin_type=CopyFollowerHistory.MarginType.NONE,
            leverage=0,
            leverage_type=CopyFollowerHistory.LeverageType.NONE,
            last_profit_shared_at=today_datetime(),
            last_profit_share_rate=trader.profit_share_rate,
        )
        db.session.add(follow_his)

        CopyTraderManager.add_one_follower(trader)
        db.session.commit()
        _send_copy_trading_msg(follow_his)
        return follow_his

    @classmethod
    def edit_follow_settings(cls, his: CopyFollowerHistory, new_settings: dict) -> CopyFollowerHistory:
        """ 更改跟单人的跟单参数 """
        if his.status == CopyFollowerHistory.Status.FINISHED:
            raise ValueError

        his.leverage_type = CopyFollowerHistory.LeverageType.NONE
        his.margin_type = CopyFollowerHistory.MarginType.NONE
        his.leverage = 0
        his.profit_trigger_rate = None
        his.loss_trigger_rate = None
        db.session.add(his)
        db.session.commit()
        return his

    @classmethod
    def has_active_follow_his(cls, follower_id: int) -> bool:
        his_row = CopyFollowerHistory.query.filter(
            CopyFollowerHistory.user_id == follower_id,
            CopyFollowerHistory.status != CopyFollowerHistory.Status.FINISHED,
        ).with_entities(CopyFollowerHistory.id).first()
        return bool(his_row)

    @classmethod
    def has_trader_active_follow_his(cls, trader_user_id: int, follower_user_id: int) -> bool:
        his_row = CopyFollowerHistory.query.filter(
            CopyFollowerHistory.user_id == follower_user_id,
            CopyFollowerHistory.copy_trader_user_id == trader_user_id,
            CopyFollowerHistory.status != CopyFollowerHistory.Status.FINISHED,
        ).with_entities(CopyFollowerHistory.id).first()
        return bool(his_row)

    @classmethod
    def get_last_active_follow_his(cls, trader_user_id: int, follower_user_id: int) -> CopyFollowerHistory | None:
        his_rows = CopyFollowerHistory.query.filter(
            CopyFollowerHistory.user_id == follower_user_id,
            CopyFollowerHistory.copy_trader_user_id == trader_user_id,
            CopyFollowerHistory.status != CopyFollowerHistory.Status.FINISHED,
        ).all()
        if len(his_rows) > 1:
            raise ValueError(f"{follower_user_id} {trader_user_id} active_follow_his_num_error {len(his_rows)}")
        return his_rows[0] if his_rows else None

    @classmethod
    def transfer_out_margin_amount(
        cls,
        follow_his: CopyFollowerHistory,
        amount: Decimal,
    ) -> CopyTransferHistory:
        # 跟单人结束跟单转出保证金，从 跟单子帐号 转入 主帐号
        asset = "USDT"
        user_id = follow_his.user_id
        sub_user_id = follow_his.sub_user_id

        tran_row = CopyTransferHelper.create_transfer(
            main_user_id=user_id,
            sub_id=sub_user_id,
            history_id=follow_his.id,
            transfer_type=CopyTransferHistory.Type.FOLLOWER_TRANSFER_OUT,
            asset=asset,
            amount=amount,
        )
        CopyTransferHelper.transfer_by_history(tran_row, on_finished_commit=False)
        db.session.commit()
        AccountTransferLogHelper.add_log_by_transfer(tran_row)
        return tran_row

    @classmethod
    def add_margin_amount(cls, follow_his: CopyFollowerHistory, amount: Decimal) -> CopyTransferHistory:
        """ 跟单人增加保证金，从主帐号 转入 跟单子帐号，无法主动转出 """
        if follow_his.status != CopyFollowerHistory.Status.FOLLOWING:
            raise ValueError

        asset = "USDT"
        user_id = follow_his.user_id
        sub_user_id = follow_his.sub_user_id
        balance = ServerClient().get_user_balances(user_id, asset)[asset]
        if balance["available"] < amount:
            raise InsufficientBalance

        tran_row = CopyTransferHelper.create_transfer(
            main_user_id=user_id,
            sub_id=sub_user_id,
            history_id=follow_his.id,
            transfer_type=CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN,
            asset=asset,
            amount=amount,
        )
        cls.add_margin_amount_by_transfer(follow_his, tran_row)
        return tran_row

    @classmethod
    def retry_add_margin_amount(cls, tran_row: CopyTransferHistory) -> bool:
        """ 跟单人增加保证金的重试 """
        assert tran_row.type == CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN
        assert tran_row.status in [CopyTransferHistory.Status.CREATED, CopyTransferHistory.Status.DEDUCTED]
        follow_his: CopyFollowerHistory = CopyFollowerHistory.query.get(tran_row.history_id)
        if follow_his.status != CopyFollowerHistory.Status.FOLLOWING:
            return False
        cls.add_margin_amount_by_transfer(follow_his, tran_row)
        return True

    @classmethod
    def add_margin_amount_by_transfer(cls, follow_his: CopyFollowerHistory, tran_row: CopyTransferHistory):
        CopyTransferHelper.transfer_by_history(tran_row, on_finished_commit=False)
        follow_his.fund_amount += tran_row.amount
        db.session.commit()
        AccountTransferLogHelper.add_log_by_transfer(tran_row)

    @classmethod
    def stop_follow(cls, follow_his: CopyFollowerHistory):
        """ 跟单人-结束跟单 """
        if follow_his.status != CopyFollowerHistory.Status.FOLLOWING:
            raise ValueError

        profit_share_type = CopyTraderProfitShareDetail.ProfitShareType.END_BY_FOLLOWER.name
        follow_his.status = CopyFollowerHistory.Status.ENDING
        profit_share_type = CopyTraderProfitShareDetail.ProfitShareType[profit_share_type]
        share_row = CopyTraderProfitShareDetail(
            date=today(),
            trader_history_id=follow_his.trader_history_id,
            follow_history_id=follow_his.id,
            user_id=follow_his.user_id,
            sub_user_id=follow_his.sub_user_id,
            copy_trader_user_id=follow_his.copy_trader_user_id,
            copy_trader_sub_user_id=follow_his.copy_trader_sub_user_id,
            asset=CopyTraderProfitShareDetail.PROFIT_ASSET,
            profit_share_type=profit_share_type,
        )
        db.session.add(share_row)
        db.session.commit()

    @classmethod
    def set_follow_finished(cls, follow_his: CopyFollowerHistory):
        """ 设置跟单为结束 """
        from app.business.copy_trading.trader import CopyTraderManager

        if follow_his.ending_status != CopyFollowerHistory.EndingStatusType.PENDING_TRANSFER_OUT:
            raise ValueError
        trader = CopyTraderManager.get_trader(follow_his.copy_trader_user_id)

        follower_user_id = follow_his.user_id
        follow_his.ending_status = CopyFollowerHistory.EndingStatusType.FINISHED
        follow_his.status = CopyFollowerHistory.Status.FINISHED
        follow_his.finished_at = now()
        FollowerSubUserManager(follower_user_id).free_sub_user(follow_his.sub_user_id)
        CopyTraderManager.remove_one_follower(trader)
        db.session.commit()
        cls.send_finished_notice(follow_his, trader)

    @classmethod
    def send_finished_notice(cls, follow_his: CopyFollowerHistory, trader: CopyTraderUser):
        share_row = CopyTraderProfitShareDetail.query.filter(
            CopyTraderProfitShareDetail.trader_history_id == follow_his.trader_history_id,
            CopyTraderProfitShareDetail.follow_history_id == follow_his.id,
            CopyTraderProfitShareDetail.profit_share_type != CopyTraderProfitShareDetail.ProfitShareType.PERIODIC_SETTLEMENT,
            CopyTraderProfitShareDetail.status == CopyTraderProfitShareDetail.Status.FINISHED,
        ).order_by(CopyTraderProfitShareDetail.id.desc()).first()
        if not share_row:
            return
        if follow_his.fund_amount <= 0:
            # 跟单金额等于0的，不发送结束跟单的通知
            return
        follower_user_id = follow_his.user_id
        if share_row.profit_share_type == CopyTraderProfitShareDetail.ProfitShareType.STOP_LOSS_TRIGGER:
            FollowerMessageSender.send_stop_loss(follower_user_id, trader)
        elif share_row.profit_share_type == CopyTraderProfitShareDetail.ProfitShareType.TAKE_PROFIT_TRIGGER:
            FollowerMessageSender.send_take_profit(follower_user_id, trader)
        else:
            FollowerMessageSender.send_follow_finished(follower_user_id, trader)

    @classmethod
    def get_follower_profit_data(cls, follower: CopyFollowerHistory, start_time: datetime,
                                 end_time: datetime):
        finish_positions_query = query_users_time_range_finish_positions(
            {follower.sub_user_id},
            start_time,
            end_time,
            created_at=follower.created_at,
            fin_columns=['profit_real'])
        follower_profit_amount = sum([i['profit_real'] for i in finish_positions_query])
        follower_profit_amount = quantize_amount(follower_profit_amount, PrecisionEnum.COIN_PLACES)
        profit_share_rate = follower.last_profit_share_rate
        if not CopyTraderUser.profit_share_max >= profit_share_rate >= CopyTraderUser.profit_share_min:
            raise ValueError(f'profit_share_rate:{profit_share_rate} invalid arg')
        trader_profit_amount = follower_profit_amount * profit_share_rate if follower_profit_amount > 0 else 0
        trader_profit_amount = quantize_amount(trader_profit_amount, PrecisionEnum.COIN_PLACES)
        return follower_profit_amount, trader_profit_amount, profit_share_rate

    @classmethod
    def process_settlement_profit(cls, follower: CopyFollowerHistory):
        share_row = CopyTraderProfitShareDetail.query.filter(
            CopyTraderProfitShareDetail.trader_history_id == follower.trader_history_id,
            CopyTraderProfitShareDetail.follow_history_id == follower.id,
            CopyTraderProfitShareDetail.profit_share_type == CopyTraderProfitShareDetail.ProfitShareType.PERIODIC_SETTLEMENT,
            CopyTraderProfitShareDetail.status == CopyTraderProfitShareDetail.Status.CREATED,
        ).order_by(CopyTraderProfitShareDetail.id.asc()).first()
        if not share_row:
            return
        if not share_row.transfer_history_id:
            end_time = date_to_datetime(share_row.date)
            start_time = follower.last_profit_shared_at
            trader = CopyTraderManager.get_trader(follower.copy_trader_user_id)
            follower_profit_amount, trader_profit_amount, profit_share_rate = cls.get_follower_profit_data(
                follower, start_time, end_time)
            tran_row = CopyTransferHelper.create_profit_transfer(
                main_user_id=share_row.user_id,
                from_user_id=share_row.sub_user_id,
                to_user_id=follower.copy_trader_user_id,
                history_id=share_row.follow_history_id,
                transfer_type=CopyTransferHistory.Type.FOLLOWER_PROFIT_TRANSFER_OUT,
                asset=CopyTraderProfitShareDetail.PROFIT_ASSET,
                amount=trader_profit_amount,
            )
            db.session.add(tran_row)
            db.session.flush()
            share_row.transfer_history_id = tran_row.id
            share_row.profit_amount = follower_profit_amount
            share_row.amount = trader_profit_amount
            share_row.profit_share_rate = profit_share_rate
            follower.total_profit_amount += follower_profit_amount
            db.session.add(share_row)
            follower.last_profit_share_rate = trader.profit_share_rate
            db.session.commit()
        else:
            tran_row = CopyTransferHistory.query.get(share_row.transfer_history_id)
        try:
            CopyTransferHelper.transfer_by_history(tran_row, on_finished_commit=False)
        except Exception as _e:
            current_app.logger.warning(f"process_settlement_profit {follower.id} error: {_e!r}")
        if tran_row.status in [
            CopyTransferHistory.Status.CREATED,
            CopyTransferHistory.Status.DEDUCTED,
        ]:
            return
        if tran_row.status == CopyTransferHistory.Status.FINISHED:
            share_row.status = CopyTraderProfitShareDetail.Status.FINISHED
            follower.last_profit_shared_at = share_row.date
        elif tran_row.status == CopyTransferHistory.Status.FAILED:
            share_row.status = CopyTraderProfitShareDetail.Status.FAILED
            sub_user_balance = PerpetualServerClient().get_user_balances(
                share_row.sub_user_id)[CopyTraderProfitShareDetail.PROFIT_ASSET]
            total_sub_user_balance = sub_user_balance['available'] + \
                                        sub_user_balance['margin'] + \
                                        sub_user_balance['profit_unreal']
            current_app.logger.warning(
                f"user_id:{share_row.sub_user_id} total balance:{total_sub_user_balance}"
                f"available:{sub_user_balance['available']} tran_amount:{tran_row.amount} retry")

            if total_sub_user_balance < tran_row.amount:
                # 跟单子账号总资产金额小于分润划转金额则等下次分润周期
                follower.last_profit_shared_at = share_row.date
            else:
                # 跟单子账号总资产金额大于等于分润划转金额则等待下次重试
                current_app.logger.warning(f"user_id:{share_row.sub_user_id} total balance:{total_sub_user_balance}"
                                           f"available:{sub_user_balance['available']} tran_amount:{tran_row.amount} retry")
        db.session.commit()
        if share_row.status == CopyTraderProfitShareDetail.Status.FINISHED and share_row.amount > 0:
            trader = CopyTraderManager.get_trader(follower.copy_trader_user_id)
            end_time = share_row.date
            start_time = share_row.date - timedelta(
                days=CopyFollowerHistory.PERIODIC_SETTLEMENT_DAYS)
            FollowerMessageSender.send_profit_share(follower.user_id, trader,
                                                    share_row.amount, start_time, end_time)

    @classmethod
    def process_follower_profit(cls, follower: CopyFollowerHistory):
        CopyFollowerManager.process_settlement_profit(follower)  # 结束跟单先检查分润是否处理完
        share_row = CopyTraderProfitShareDetail.query.filter(
            CopyTraderProfitShareDetail.trader_history_id == follower.trader_history_id,
            CopyTraderProfitShareDetail.follow_history_id == follower.id,
            CopyTraderProfitShareDetail.profit_share_type != CopyTraderProfitShareDetail.ProfitShareType.PERIODIC_SETTLEMENT,
        ).first()
        if share_row.status != CopyTraderProfitShareDetail.Status.CREATED:
            return
        if not share_row.transfer_history_id:
            start_time = follower.last_profit_shared_at
            follower_profit_amount, trader_profit_amount, profit_share_rate = CopyFollowerManager.get_follower_profit_data(
                follower, start_time, None)
            tran_row = CopyTransferHelper.create_profit_transfer(
                main_user_id=share_row.user_id,
                from_user_id=share_row.sub_user_id,
                to_user_id=follower.copy_trader_user_id,
                history_id=share_row.follow_history_id,
                transfer_type=CopyTransferHistory.Type.FOLLOWER_PROFIT_TRANSFER_OUT,
                asset=CopyTraderProfitShareDetail.PROFIT_ASSET,
                amount=trader_profit_amount,
            )
            db.session.add(tran_row)
            db.session.flush()
            share_row.transfer_history_id = tran_row.id
            share_row.profit_amount = follower_profit_amount
            share_row.amount = trader_profit_amount
            share_row.profit_share_rate = profit_share_rate
            db.session.add(share_row)
            follower.last_profit_shared_at = now()
            db.session.commit()
        else:
            tran_row = CopyTransferHistory.query.get(share_row.transfer_history_id)
        CopyTransferHelper.transfer_by_history(tran_row, on_finished_commit=False)
        share_row.status = CopyTraderProfitShareDetail.Status.FINISHED
        db.session.commit()


@celery_task
@lock_call(with_args=True)
def retry_or_stop_zero_fund_follow_his(follow_his_id: int):
    """ 重试或结束`跟单资金为0的记录`，重试增加保证金 or (重试失败)结束该跟单 """
    from app.business.coupon.user_coupon import CopyTradingExperienceFeeService
    from app.schedules.copy_trading import finish_copy_trading_follower_status

    current_app.logger.warning(f"retry_or_stop_zero_fund_follow_his {follow_his_id} begin")
    follow_his: CopyFollowerHistory = CopyFollowerHistory.query.get(follow_his_id)
    sub_id = follow_his.sub_user_id
    if follow_his.fund_amount > Decimal(0):
        return
    if CopyTradingExperienceFeeService.get_his_sub_coupon_detail(follow_his_id, follow_his.sub_user_id):
        return

    all_tran_in_rows: list[CopyTransferHistory] = CopyTransferHistory.query.filter(
        CopyTransferHistory.main_user_id == follow_his.user_id,
        CopyTransferHistory.history_id == follow_his.id,
        CopyTransferHistory.type == CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN,
    ).all()
    if [i for i in all_tran_in_rows if i.status == CopyTransferHistory.Status.FINISHED]:
        return

    with CacheLock(LockKeys.copy_follower_sub(sub_id)):
        db.session.rollback()
        if follow_his.status != CopyFollowerHistory.Status.FOLLOWING:
            return

        max_retry_dt = now() - timedelta(days=1)
        retry_tran_in_rows = [
            i for i in all_tran_in_rows
            if i.status not in [CopyTransferHistory.Status.FAILED, CopyTransferHistory.Status.FINISHED]
        ]
        for tr in retry_tran_in_rows:
            if tr.created_at <= max_retry_dt:
                # 一天前的划转记录不再重试
                continue
            try:
                CopyFollowerManager.retry_add_margin_amount(tr)
            except Exception as _e:
                db.session.rollback()
                current_app.logger.warning(f"retry_or_stop_zero_fund_follow_his add_margin_amount {follow_his_id} {tr.id} error: {_e!r}")

        can_stop = not retry_tran_in_rows or all([i.status == CopyTransferHistory.Status.FAILED for i in retry_tran_in_rows])
        if not can_stop:
            current_app.logger.warning(f"retry_or_stop_zero_fund_follow_his {follow_his_id} can_not_stop")
            return

        sub_balance = PerpetualServerClient().get_user_balances(user_id=sub_id)
        total_equity = sum(v['balance_total'] for v in sub_balance.values())
        total_equity = quantize_amount(total_equity, 8)
        if total_equity > Decimal(0):
            current_app.logger.warning(f"retry_or_stop_zero_fund_follow_his {follow_his_id} {sub_id} total_equity {total_equity}")
            return

        CopyFollowerManager.stop_follow(follow_his)
        current_app.logger.warning(f"retry_or_stop_zero_fund_follow_his {follow_his_id} stop_success")
    finish_copy_trading_follower_status.delay(follow_his.id)
