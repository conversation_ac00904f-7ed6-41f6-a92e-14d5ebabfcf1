# -*- coding: utf-8 -*-
"""

服务器端通过RSA生成公钥，然后把公钥给客户端。

客服端在请求服务器前， 随机生成AES秘钥，然后用AES秘钥加密请求数据。

之后用RSA公钥对AES秘钥进行加密，然后把加密之后的AES秘钥和加密后的请求数据一起发送给服务器。

服务器收到请求之后，先用RSA私钥解密出AES秘钥，然后用AES秘钥对请求数据进行解密，获取请求数据。

"""

import binascii
import base64
import os

from Crypto.PublicKey import RSA
from Crypto.Cipher import AES
from Crypto import Random
from Crypto.Cipher import PKCS1_v1_5

from app.caches.red_packet import RsaKeyCache, AesKeyCache
from app.exceptions import InvalidArgument

TOTAL_BITS = 1024

BS = 16
# pad = lambda s: s + (BS - len(s) % BS) * chr(BS - len(s) % BS)
# unpad = lambda s: s[:-ord(s[len(s) - 1:])]


class RsaGenerator(object):
    def __init__(self, business):
        """
        密钥每天进行变更
        """
        self.business = business

    @classmethod
    def _generator_keys(cls):
        key = RSA.generate(TOTAL_BITS)
        return key.exportKey()

    def __priv_key(self):
        """
        :return:  内部函数
        """
        cache = RsaKeyCache(self.business)
        if cache.exists():
            priv_key = cache.value
        else:
            priv_key = self._generator_keys()
            cache.value = priv_key
            cache.expire(3 * 86400)
        return priv_key

    def priv_key_obj(self):
        return RSA.importKey(self.__priv_key())

    def pub_key_obj(self):
        return RSA.importKey(self.__priv_key()).publickey()

    def get_pubkey(self):
        return RSA.importKey(self.__priv_key()).publickey().exportKey()

    def get_pubkey_base64(self):
        return base64.b64encode(RSA.importKey(self.__priv_key()).publickey().exportKey()).decode()

    def get_pubkey_hex(self):
        return binascii.hexlify(RSA.importKey(self.__priv_key()).publickey().exportKey()).decode()

    def encrypt(self, text):
        """
        :param text: text string.
        :return: hex string.
        """
        # a为无用参数
        cipher = PKCS1_v1_5.new(self.pub_key_obj())
        return binascii.hexlify(cipher.encrypt(text))

    def encrypt_base64(self, text):
        """
        :param text: text string.
        :return: base64 string.
        """
        # a为无用参数
        cipher = PKCS1_v1_5.new(self.pub_key_obj())
        return base64.b64encode(cipher.encrypt(text))

    def decrypt(self, text):
        """
        :param text: hex string
        :return: text string.
        """
        cipher = PKCS1_v1_5.new(self.priv_key_obj())
        return cipher.decrypt(binascii.unhexlify(text), '')

    def decrypt_base64(self, text):
        """
        :param text: base64 string
        :return: text string.
        """
        cipher = PKCS1_v1_5.new(self.priv_key_obj())
        return cipher.decrypt(base64.b64decode(text), '')


class AESGenerator(object):
    def __init__(self, business):
        self.business = business

    @classmethod
    def _generator_key(cls):
        return os.urandom(16)

    @property
    def __key(self):
        """
        :return:  内部函数
        """
        cache = AesKeyCache(self.business)
        if cache.exists():
            key = cache.value
        else:
            key = self._generator_key()
            cache.value = key
        return key

    @property
    def key(self):
        return self.__key

    @classmethod
    def __pad(cls, text):
        """
        填充方式，加密内容必须为16字节的倍数，若不足则使用self.iv进行填充
        """
        text_length = len(text)
        amount_to_pad = AES.block_size - (text_length % AES.block_size)
        if amount_to_pad == 0:
            amount_to_pad = AES.block_size
        pad_chr = chr(amount_to_pad)
        return str.encode(text + pad_chr * amount_to_pad)

    @classmethod
    def __unpad(cls, text):
        pad_ord = ord(text[-1])
        return text[:-pad_ord]

    def encrypt(self, raw):
        raw = self.__pad(raw)
        iv = Random.new().read(AES.block_size)
        cipher = AES.new(self.__key, AES.MODE_CBC, iv)
        return binascii.hexlify(iv + cipher.encrypt(raw)).decode()

    def decrypt(self, enc):
        enc = binascii.unhexlify(enc)
        iv = enc[:AES.block_size]
        cipher = AES.new(self.__key, AES.MODE_CBC, iv)
        try:
            decrypted = cipher.decrypt(enc[AES.block_size:]).decode()
            res = self.__unpad(decrypted)
        except UnicodeDecodeError:
            raise InvalidArgument
        return res

    def encrypt_base64(self, raw):
        raw = self.__pad(raw)
        iv = Random.new().read(AES.block_size)
        cipher = AES.new(self.__key, AES.MODE_CBC, iv)
        return base64.b64encode(iv + cipher.encrypt(raw)).decode()

    def decrypt_base64(self, enc):
        enc = base64.b64decode(enc)
        iv = enc[:AES.block_size]
        cipher = AES.new(self.__key, AES.MODE_CBC, iv)
        return self.__unpad(cipher.decrypt(enc[AES.block_size:]).decode())


class AesTool(object):
    def __init__(self, key):
        self.key = key

    @classmethod
    def __pad(cls, text):
        """
        填充方式，加密内容必须为16字节的倍数，若不足则使用self.iv进行填充
        """
        text_length = len(text)
        amount_to_pad = AES.block_size - (text_length % AES.block_size)
        if amount_to_pad == 0:
            amount_to_pad = AES.block_size
        pad_chr = chr(amount_to_pad)
        return str.encode(text + pad_chr * amount_to_pad)

    @classmethod
    def __unpad(cls, text):
        pad_ord = ord(text[-1])
        return text[:-pad_ord]

    def encrypt(self, raw):
        raw = self.__pad(raw)
        iv = Random.new().read(AES.block_size)
        cipher = AES.new(self.key, AES.MODE_CBC, iv)
        return binascii.hexlify(iv + cipher.encrypt(raw)).decode()

    def decrypt(self, enc):
        enc = binascii.unhexlify(enc)
        iv = enc[:AES.block_size]
        try:
            cipher = AES.new(self.key, AES.MODE_CBC, iv)
        except ValueError:
            raise InvalidArgument
        return self.__unpad(cipher.decrypt(enc[AES.block_size:]).decode())

    def encrypt_base64(self, raw):
        raw = self.__pad(raw)
        iv = Random.new().read(AES.block_size)
        cipher = AES.new(self.key, AES.MODE_CBC, iv)
        return base64.b64encode(iv + cipher.encrypt(raw)).decode()

    def decrypt_base64(self, enc):
        enc = base64.b64decode(enc)
        iv = enc[:AES.block_size]
        cipher = AES.new(self.key, AES.MODE_CBC, iv)
        return self.__unpad(cipher.decrypt(enc[AES.block_size:]).decode())
