# -*- coding: utf-8 -*-
import random
import math
from decimal import Decimal

"""
红包预先生成的算法：
    剩余红包金额为M，剩余人数为N，那么有如下公式：
    
        每次抢到的金额 = 随机区间 （0， M / N X 2]
        采用此公式计算出来的期望是先抢到的期望要比后面的人的期望大一些
        
    添加限制条件：
    
        剩余红包金额在只能满足一人一份的情况下，则不采用随机算法
    
"""


class RandomPackClass(object):
    def __init__(self, total, count, min_amount=0):
        self.total = total
        self.remain = total
        self.n = 0
        self.amount = 0
        self.count = count
        self.min_amount = min_amount
        self.max_amount_func = lambda x, y: math.floor(x / (Decimal('1.0') * Decimal(y)) * 2)

    def __next__(self):
        self.n += 1
        if self.n < self.count:
            # 如果刚好份数每个人只够一份，则只分一份
            if self.remain - (self.count - self.n) == 0:
                self.amount = 1
                self.remain -= self.amount
            else:
                max_amount = self.max_amount_func(self.remain, self.count - self.n + 1)
                grab_amount = random.randint(self.min_amount, max_amount)
                if self.remain - grab_amount < self.count - self.n:
                    # 如果分走的数额不能够使最后剩余的数量每人一份，则分走的奖金减少至能保证每人一份
                    self.amount = self.remain - (self.count - self.n)
                    if self.amount < 0:
                        self.amount = 0
                    self.remain -= self.amount
                else:
                    self.amount = grab_amount
                    self.remain -= self.amount
            return self.amount
        elif self.n == self.count:
            self.amount = self.remain
            self.remain = 0
            return self.amount

        raise StopIteration()

    def __iter__(self):
        return self


def _generator_red_packet(total, count):
    """
    :param total: 总份数，int
    :param count: 人数，int
    :return:
    """

    result = list(RandomPackClass(total, count, min_amount=1))
    if total == sum(list(result)) and all([v > 0 for v in result]) and len(result) == count:
        return result
    else:
        # 防止金额计算不对，重新计算，如果一直计算不对，则会抛出递归深度错误
        return _generator_red_packet(total, count)


def _generator_delta_amount(total, count):
    """
    分配不够精度的剩余币数量
    :param total: 总份数，int
    :param count: 人数，int
    :return:
    """

    result = list(RandomPackClass(total, count, min_amount=0))
    if total == sum(result) and all([v >= 0 for v in result]) and len(result) == count:
        return result
    else:
        # 防止金额计算不对，重新计算，如果一直计算不对，则会抛出递归深度错误
        return _generator_delta_amount(total, count)


def generator_luck_package(cut_dict, count):
    """
    :param count:
    :param cut_dict:
       {
            'low_value': self.low_value
            'package': {
                'per_amount': self.per_amount,
                'total': self.amount // self.per_amount,
            },
            'rest': self.amount % self.per_amount
       }
    :return:
    """
    per_amount = cut_dict['package']['per_amount']
    if cut_dict['low_value']:
        big_amount_list = list(map(lambda x: x * per_amount, _generator_red_packet(cut_dict['package']['total'], count)))
        rest_amount_list = _generator_delta_amount(cut_dict['rest'], count)
        return list(map(sum, zip(big_amount_list, rest_amount_list)))
    else:
        big_amount_list = list(map(lambda x: x * per_amount, _generator_red_packet(cut_dict['package']['total'], count)))
        rest_amount_list = [cut_dict['rest'] if v == 0 else 0 for v in range(count)]
        return list(map(sum, zip(big_amount_list, rest_amount_list)))


def generator_normal_package(cut_dict, count):
    """
        :param count:
        :param cut_dict:
           {
                'package': {
                    'per_amount': self.per_amount,
                }
            }
        :return:
    """
    return [cut_dict['package']['per_amount']] * count


