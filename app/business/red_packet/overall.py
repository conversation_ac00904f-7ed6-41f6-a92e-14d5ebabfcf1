# -*- coding: utf-8 -*-

import json
from decimal import Decimal

from sqlalchemy import func

from app.business import <PERSON>Manager
from app.business.red_packet.send import LIMIT_AMOUNT, get_quantize
from app.caches import RedPacketOverall<PERSON>ache, CBoxCanTransferCoinCache
from app.models.red_packet import RedPacket, RedPacketHistory, RedPacketReturnHistory
from app.utils import current_timestamp
from app.utils.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>


def get_realtime_red_packet_overall():
    """
    获取实时统计数据
    :return:
    """
    coin_type_set = CBoxCanTransferCoinCache().get_coins()

    # 累计发放金额
    red_packet_sum_records = RedPacket.query.filter(
        RedPacket.status.in_([RedPacket.Status.PASSED,
                              RedPacket.Status.FINISHED,
                              RedPacket.Status.EXPIRED])
    ).with_entities(
        RedPacket.asset,
        func.Sum(RedPacket.total_amount).label('total_release_amount')
    ).group_by(RedPacket.asset).all()
    coin_data_map = {r.asset: r.total_release_amount
                     for r in red_packet_sum_records}

    # 累计领取金额
    red_packet_history_grab_records = RedPacketHistory.query.filter(
        RedPacketHistory.grab_at.isnot(None)
    ).with_entities(
        RedPacketHistory.asset,
        func.Sum(RedPacketHistory.amount).label('total_draw_amount')
    ).group_by(RedPacketHistory.asset).all()
    total_draw_amount_map = {r.asset: r.total_draw_amount
                             for r in red_packet_history_grab_records}

    # 当前可领金额
    red_packet_history_available_records = RedPacketHistory.query.filter(
        RedPacketHistory.grab_at.is_(None),
        RedPacketHistory.status == RedPacketHistory.Status.CREATED
    ).with_entities(
        RedPacketHistory.asset,
        func.Sum(RedPacketHistory.amount).label('total_available_amount')
    ).group_by(RedPacketHistory.asset).all()
    total_available_amount_map = {r.asset: r.total_available_amount
                                  for r in red_packet_history_available_records}

    # 累计退还金额
    red_packet_history_return_records = RedPacketReturnHistory.query.filter(
        RedPacketReturnHistory.status == RedPacketReturnHistory.Status.FINISHED
    ).with_entities(
        RedPacketReturnHistory.asset,
        func.Sum(RedPacketReturnHistory.return_amount).label('total_return_amount')
    ).group_by(RedPacketReturnHistory.asset).all()
    total_return_amount_map = {r.asset: r.total_return_amount
                               for r in red_packet_history_return_records}

    rates_cache = PriceManager.assets_to_usd()

    data_map = {}
    for asset, total_release_amount in coin_data_map.items():
        record = {
            'asset': asset,
            'total_release_amount': total_release_amount,
            'total_draw_amount': total_draw_amount_map.get(asset, 0),
            'total_return_amount': total_return_amount_map.get(asset, 0),
            'total_available_amount': total_available_amount_map.get(
                asset, 0),
            'single_red_packet_min_amount': get_single_red_packet_min_amount(
                rates_cache.get(asset, Decimal())),
            'status': RedPacketOverallCache.STATUS_OPEN
            if asset in coin_type_set
            else RedPacketOverallCache.STATUS_CLOSE,
        }
        data_map[asset] = json.dumps(record, cls=JsonEncoder)
    data_map[RedPacketOverallCache.KEY_TIMESTAMP] = current_timestamp()
    return data_map


def get_single_red_packet_min_amount(rate):
    rate = Decimal(rate)
    if rate == Decimal():
        return '--'
    per_amount = Decimal("{:.8f}".format(LIMIT_AMOUNT / rate))
    return get_quantize(per_amount)
