# -*- coding: utf-8 -*-
import re
from decimal import Decimal

COIN_PLACES = 8

RED_PACKET_GRAB_BUSINESS = 'red_packet_grab_business'
RED_PACKET_SEND_BUSINESS = 'red_packet_send_business'

MESSAGE_TYPE_PACKET_TO_ACCOUNT = 'to_account'
MESSAGE_TYPE_PACKET_REFUND = 'refund'

GRAB_EXPIRED_DAY = 7
GRAB_EXPIRED_TIME = GRAB_EXPIRED_DAY * 24 * 60 * 60

# C-BOX口令池保留的最少口令数
MIN_CODE_POOL_SIZE = 2000

# 每份红包最小值为0.01USD等值数字货币
LIMIT_AMOUNT = Decimal('0.01')
# 红包最大份数是200
PACK_MAX_AMOUNT = 2000

# 红包确认时间30分钟
CONFIRM_TIME = 60 * 30

_RE_C_BOX_CODE = re.compile(r'[A-Za-z0-9][A-Za-z0-9 ]{4,30}[A-Za-z0-9]')


def validate_c_box_code(code: str):
    if not _RE_C_BOX_CODE.fullmatch(code):
        return False
    return True
