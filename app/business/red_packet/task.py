# -*- coding: utf-8 -*-
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from flask import current_app
from sqlalchemy import func

from app.business import (
    ServerClient, lock_call,
)
from app.business.red_packet.constants import GRAB_EXPIRED_DAY
from app.business.red_packet.grab import send_red_packet_grabbed_notice, \
    send_red_packet_refund_notice, ReceiveEmailCBoxManager
from app.business.referral import ReferralBusiness
from app.common import CeleryQueues, BalanceBusiness
from app.exceptions import CBoxCheatedUser
from app.models import RedPacket, RedPacketHistory, \
    RedPacketReturnHistory, db, User, ReferralHistory
from app.utils import route_module_to_celery_queue, celery_task, \
    now

route_module_to_celery_queue(__name__, CeleryQueues.GIFT)


@celery_task
@lock_call(with_args=True)
def return_red_packet_task(red_packet_id: int):
    packet: RedPacket = RedPacket.query.filter(
        RedPacket.id == red_packet_id,
        RedPacket.status == RedPacket.Status.PASSED,
        RedPacket.effective_at.isnot(None),
        RedPacket.expired_at < now(),
        RedPacket.is_return.is_(False)
    ).first()
    if not packet:
        return
    q = RedPacketHistory.query.filter(
        RedPacketHistory.red_packet_id == red_packet_id,
        RedPacketHistory.status == RedPacketHistory.Status.CREATED,
        RedPacketHistory.grab_at.is_(None)
    ).with_entities(
        func.sum(RedPacketHistory.amount).label('return_amount')
    ).first()
    return_amount = q.return_amount if q and q.return_amount else Decimal()
    record = RedPacketReturnHistory(
        red_packet_id=red_packet_id,
        return_amount=return_amount,
        reason=RedPacketReturnHistory.Reason.EXPIRE,
        asset=packet.asset,
        status=RedPacketReturnHistory.Status.CREATED)
    packet.status = RedPacket.Status.EXPIRED
    packet.is_return = True
    db.session.add(record)
    db.session.commit()
    if return_amount == Decimal():
        # 全部都抢完了，则返回
        record.status = RedPacketReturnHistory.Status.FINISHED
        db.session.commit()
        return
    balance_server = ServerClient()
    try:
        balance_server.add_user_balance(
            packet.user_id,
            packet.asset,
            str(return_amount),
            BalanceBusiness.RED_PACKET_REFUND,
            record.id,
            {'remark': 'return red packet'}
        )
        record.status = RedPacketReturnHistory.Status.FINISHED
        RedPacketHistory.query.filter(
            RedPacketHistory.red_packet_id == red_packet_id,
            RedPacketHistory.status == RedPacketHistory.Status.CREATED,
            RedPacketHistory.grab_at.is_(None)
        ).update(
            {RedPacketHistory.status: RedPacketHistory.Status.EXPIRED},
            synchronize_session=False
        )
        db.session.commit()
        send_red_packet_refund_notice(packet, record)
    except Exception:
        record.status = RedPacketReturnHistory.Status.FAILED
        db.session.commit()
        error_msg = f'return red packet {red_packet_id}' \
                    f' return id {record.id} user {packet.user_id} ' \
                    f'return amount {return_amount} ' \
                    f'coin {packet.asset} error'
        current_app.logger.error(error_msg)


@celery_task
def signup_user_send_red_packet_task(user_id: int):
    """
    用户注册的时候发红包
    """
    user: User = User.query.get(user_id)
    if not user:
        return
    query = RedPacketHistory.query.filter(
        RedPacketHistory.status == RedPacketHistory.Status.UNREGISTER,
        RedPacketHistory.grab_at >= now() + timedelta(days=-GRAB_EXPIRED_DAY),
        RedPacketHistory.email == user.email
    ).all()

    now_ = now()
    balance_server = ServerClient()
    referral_invalid = False
    cheated_records = []
    for idx, record in enumerate(query):
        red_packet = RedPacket.query.get(record.red_packet_id)
        try:
            ReceiveEmailCBoxManager.verify_user_cheated(red_packet, user)
            record.user_id = user_id
        except CBoxCheatedUser:
            if idx == 0:
                referral_invalid = True
            cheated_records.append((record.red_packet_id, red_packet.user_id, record.asset, record.amount))
            record.user_id = user_id
            record.status = RedPacketHistory.Status.FAILED
            record.new_user_registered_at = now_
            db.session.commit()
            error_msg = f'red packet {record.red_packet_id} grab failed' \
                        f'grab id {record.id} ' \
                        f'user {user_id} send amount {record.amount} ' \
                        f'fail reason: cheated user'
            current_app.logger.warning(error_msg)
            continue
        # noinspection PyBroadException
        try:
            balance_server.add_user_balance(
                user_id,
                record.asset,
                str(record.amount),
                BalanceBusiness.RED_PACKET_GRABBING,
                record.id,
                {'remark': 'grab red packet'}
            )
            record.status = RedPacketHistory.Status.FINISHED
            record.new_user_registered_at = now_
            db.session.commit()
            send_red_packet_grabbed_notice(record)
        except Exception:
            record.status = RedPacketHistory.Status.FAILED
            db.session.commit()
            error_msg = f'red packet {record.red_packet_id} ' \
                        f'grab id {record.id} ' \
                        f'user {user_id} send amount {record.amount} ' \
                        f'coin {record.asset} error'
            current_app.logger.error(error_msg)

    if query and not referral_invalid:  # 风控检查不通过时，不添加推荐关系
        red_packet_history = query[0]
        red_packet = RedPacket.query.filter(
            RedPacket.id == red_packet_history.red_packet_id
        ).first()
        referrer_id = red_packet.user_id
        refer_history = ReferralHistory.query.filter(
            ReferralHistory.referree_id == user_id
        ).first()
        if not refer_history:
            refer_data = ReferralBusiness.get_referral_by_user(referrer_id)
            db.session.add(ReferralHistory(
                referral_id=refer_data.id,
                referrer_id=refer_data.user_id,
                referree_id=user_id
            ))
            db.session.commit()
    return_asset_to_senders(cheated_records)


def return_asset_to_senders(cheated_records: list):
    # 此处仅新增退还记录，具体退还流程由retry_return_redpacket_schedule处理
    for red_packet_id, user_id, asset, amount in cheated_records:
        record = RedPacketReturnHistory(
            red_packet_id=red_packet_id,
            return_amount=amount,
            reason=RedPacketReturnHistory.Reason.CHEATED_USER,
            asset=asset,
            status=RedPacketReturnHistory.Status.CREATED)
        db.session.add(record)
    db.session.commit()

