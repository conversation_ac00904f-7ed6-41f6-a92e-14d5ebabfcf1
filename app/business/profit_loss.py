from collections import defaultdict
from decimal import Decimal
from functools import cached_property

from flask import current_app

from app.assets import get_asset_config
from app.business import ServerClient, SPOT_ACCOUNT_ID, ALL_RECORD_ACCOUNT_ID, TradeSummaryDB
from app.business.site import BusinessSettings
from app.caches import MarginAccountNameCache, MarketCache
from app.common import OrderSideType, PrecisionEnum
from app.exceptions import InvalidArgument, OutOfRange
from app.utils import current_timestamp, quantize_amount
from app.utils.date_ import date_to_datetime


class MarketProfitLossAnalyzer(object):

    def __init__(self, user_id: int, market: str, margin: bool):
        self.user_id = user_id
        self.market = market
        self.margin = margin
        self.server_client: ServerClient = ServerClient()
        market_cache = MarketCache(self.market).dict
        self.base_asset = market_cache['base_asset']
        self.quote_asset = market_cache["quote_asset"]
        self.base_asset_precision = market_cache["base_asset_precision"]
        self.quote_asset_precision = market_cache["quote_asset_precision"]
        self.account_id = self._account_id
        self.min_amount = get_asset_config(self.base_asset).asset_user_min_amount
        if self.min_amount is None:
            current_app.logger.error(f"{self.base_asset} 资产用户最小数量未配置")
            raise InvalidArgument
        self.earliest_time = current_timestamp(to_int=True) - BusinessSettings.market_pl_days * 86400
        self.empty = False

    @cached_property
    def _account_id(self):
        return MarginAccountNameCache(self.market).dict["id"] \
            if self.margin else SPOT_ACCOUNT_ID

    @cached_property
    def min_amount_history_time(self):
        records = self.server_client.get_user_balance_history(
            self.user_id, self.base_asset, account_id=self.account_id,
            limit=BusinessSettings.market_pl_balance_history_count)
        if len(records) == 0:
            self.empty = True
            return self.earliest_time
        for record in records:
            if record["time"] < self.earliest_time:
                raise OutOfRange
            if Decimal(record['balance']) < self.min_amount:
                self.empty = True
                return int(record["time"])
            if Decimal(record["balance"]) - Decimal(record["change"]) < self.min_amount:
                return int(record["time"])
        raise OutOfRange

    @property
    def empty_data(self):
        zero = Decimal()
        return dict(
            min_asset_amount=self.min_amount,
            total_sell_amount=zero,
            total_buy_amount=zero,
            total_sell_money=zero,
            total_buy_money=zero,
            buy_avg_price=zero,
            sell_avg_price=zero,
            position_cost_money=zero,
            net_buy_amount=zero,
            position_avg_price=zero,
            current_price=zero,
            profit=zero,
            start=current_timestamp(to_int=True),
            end=current_timestamp(to_int=True),
            empty=True
        )

    def analyze(self):
        zero = Decimal('0')
        balance_data = self.server_client.get_user_balances(self.user_id, self.base_asset,
                                                            account_id=self.account_id)
        for asset, balance in balance_data.items():
            if asset == self.base_asset \
                and balance.get("available", zero) == zero \
                    and balance.get("frozen", zero) == zero:
                return self.empty_data

        min_time = self.min_amount_history_time
        if self.empty:
            empty_data_with_min_time = self.empty_data
            empty_data_with_min_time["start"] = min_time
            return empty_data_with_min_time
        deals = self.server_client.market_user_deals(
            self.user_id, self.market,
            account_id=self.account_id,
            start_time=min_time,
            end_time=current_timestamp(to_int=True),
            page=1,
            limit=BusinessSettings.market_pl_deal_history_count)

        if deals.has_next:
            raise OutOfRange
        total_buy_amount = Decimal()
        total_buy_money = Decimal()
        total_sell_amount = Decimal()
        total_sell_money = Decimal()
        amount_fee = Decimal()
        money_fee = Decimal()
        for deal in deals:
            if deal["side"] == OrderSideType.BUY:
                total_buy_amount += Decimal(deal["amount"])
                total_buy_money += Decimal(deal["deal"])
            if deal["side"] == OrderSideType.SELL:
                total_sell_amount += Decimal(deal["amount"])
                total_sell_money += Decimal(deal["deal"])
            if deal['fee_asset'] == self.base_asset:
                amount_fee += Decimal(deal['fee'])
            if deal['fee_asset'] == self.quote_asset:
                money_fee += Decimal(deal['fee'])

        buy_avg_price = total_buy_money / total_buy_amount if total_buy_amount > 0 \
            else Decimal()
        sell_avg_price = total_sell_money / total_sell_amount if total_sell_amount > 0 \
            else Decimal()
        position_cost_money = total_buy_money - total_sell_money + money_fee
        net_buy_amount = total_buy_amount - total_sell_amount - amount_fee
        position_avg_price = position_cost_money / net_buy_amount \
            if net_buy_amount >= self.min_amount else Decimal()
        current_price = Decimal(self.server_client.market_last(self.market))
        profit = (current_price - position_avg_price) * net_buy_amount \
            if position_avg_price > Decimal() or net_buy_amount >= self.min_amount else Decimal()
        amount_format = lambda x: quantize_amount(x, self.base_asset_precision)
        price_format = lambda x: quantize_amount(x, self.quote_asset_precision)

        def profit_format(_asset, _amount):
            precision = 8
            if _asset in ['USDT', 'USDC']:
                precision = 2
            elif _asset in ['BCH', 'ETH']:
                precision = 6
            return quantize_amount(_amount, precision)

        return dict(
            min_asset_amount=self.min_amount,
            total_sell_amount=amount_format(total_sell_amount),
            total_buy_amount=amount_format(total_buy_amount),
            total_sell_money=quantize_amount(total_sell_money, PrecisionEnum.COIN_PLACES),
            total_buy_money=quantize_amount(total_buy_money, PrecisionEnum.COIN_PLACES),
            buy_avg_price=price_format(buy_avg_price),
            sell_avg_price=price_format(sell_avg_price),
            position_cost_money=price_format(position_cost_money),
            net_buy_amount=amount_format(net_buy_amount),
            position_avg_price=price_format(position_avg_price),
            current_price=price_format(current_price),
            profit=profit_format(self.quote_asset, profit),
            start=min_time,
            end=current_timestamp(to_int=True),
            empty=self.empty
        )


class UserDealAnalyzer(object):

    def __init__(self, user_id: int, market: str, start: int, end: int):
        self.user_id = user_id
        self.market = market
        self.start = start
        self.end = end
        self.server_client: ServerClient = ServerClient()
        market_cache = MarketCache(self.market).dict
        self.base_asset = market_cache['base_asset']
        self.quote_asset = market_cache["quote_asset"]
        self.base_asset_precision = market_cache["base_asset_precision"]
        self.quote_asset_precision = market_cache["quote_asset_precision"]
        self.empty = False

    def check(self):
        if self.end < self.start:
            raise InvalidArgument
        if self.start % 86400 != 0 or self.end % 86400 != 0:
            raise InvalidArgument
        if self.end - self.start > BusinessSettings.deal_pl_days * 86400:
            raise OutOfRange

    def get_market_prices(self):
        r = self.server_client.market_kline(market=self.market, start_time=self.start,
                                            end_time=self.end, interval=86400)
        # 取收盘价
        kline_result = {}
        for v in r:
            if v[0] == self.end:
                continue
            kline_result[v[0]] = Decimal(v[2])
        return kline_result

    @classmethod
    def get_snap_shot_records(cls, user_id: int, market: str, start_time: int, end_time: int):
        records = TradeSummaryDB.get_user_daily_trade_summary_records(user_id, market, start_time, end_time)
        convert_records = []
        for record in records:
            _time = int(date_to_datetime(record["trade_date"]).timestamp())
            if record["buy_amount"] > Decimal('0') or record["buy_volume"] > Decimal('0'):
                convert_records.append(
                    dict(
                        time=_time,
                        side=OrderSideType.BUY,
                        deal=record["buy_volume"],
                        amount=record["buy_amount"],
                    )
                )
            if record["sell_amount"] > Decimal('0') or record["sell_volume"] > Decimal('0'):
                convert_records.append(
                    dict(
                        time=_time,
                        side=OrderSideType.SELL,
                        deal=record["sell_volume"],
                        amount=record["sell_amount"],
                    )
                )
        return convert_records

    def analyze(self):
        self.check()
        current_ts = current_timestamp(to_int=True)
        if self.end > current_ts:
            # 获取 server 最新的 dump 时间戳
            dump_st = TradeSummaryDB.get_dump_time()
            deals = self.server_client.market_user_deals(
                self.user_id, self.market,
                account_id=ALL_RECORD_ACCOUNT_ID,
                start_time=dump_st,
                end_time=self.end,
                page=1,
                limit=BusinessSettings.deal_pl_record_count)
            if deals.has_next:
                deals = []
            deals.extend(
               self.get_snap_shot_records(self.user_id, self.market, self.start, self.end)
            )
        else:
            deals = self.get_snap_shot_records(self.user_id, self.market, self.start, self.end)

        total_buy_amount = Decimal()
        total_buy_money = Decimal()
        total_sell_amount = Decimal()
        total_sell_money = Decimal()
        for deal in deals:
            if deal["side"] == OrderSideType.BUY:
                total_buy_amount += Decimal(deal["amount"])
                total_buy_money += Decimal(deal["deal"])
            if deal["side"] == OrderSideType.SELL:
                total_sell_amount += Decimal(deal["amount"])
                total_sell_money += Decimal(deal["deal"])

        buy_avg_price = total_buy_money / total_buy_amount if total_buy_amount > 0 \
            else Decimal()
        sell_avg_price = total_sell_money / total_sell_amount if total_sell_amount > 0 \
            else Decimal()
        kline_data = self.get_market_prices()
        date_with_sort_deals = defaultdict(list)
        for deal in deals:
            t = deal['time'] - deal['time'] % 86400
            date_with_sort_deals[t].append(deal)
        result = defaultdict(lambda: dict(ts=0, price=Decimal()))
        amount_format = lambda x: quantize_amount(x, self.base_asset_precision)
        price_format = lambda x: quantize_amount(x, self.quote_asset_precision)
        for t, price in kline_data.items():
            result[t]["ts"] = t
            result[t]["price"] = Decimal(price)
        for t, _deals in date_with_sort_deals.items():
            sell_records = [v for v in _deals if v['side'] == OrderSideType.SELL]
            buy_records = [v for v in _deals if v['side'] == OrderSideType.BUY]
            sell_amount = sum([Decimal(v['amount']) for v in sell_records])
            sell_money = sum([Decimal(v['deal']) for v in sell_records])
            buy_amount = sum([Decimal(v['amount']) for v in buy_records])
            buy_money = sum([Decimal(v['deal']) for v in buy_records])
            if len(sell_records) > 0:
                result[t]['sell'] = {
                    "avg_price": price_format(sell_money / sell_amount) if sell_amount > 0 else Decimal(),
                    "amount": amount_format(sell_amount),
                    "money": price_format(sell_money)
                }
            if len(buy_records) > 0:
                result[t]['buy'] = {
                    "avg_price": price_format(buy_money / buy_amount) if buy_amount > 0 else Decimal(),
                    "amount": amount_format(buy_amount),
                    "money": price_format(buy_money)
                }
        series_data = sorted(list(filter(lambda x: x["ts"] != 0, result.values())),
                             key=lambda x: -x["ts"])

        sell_data = [dict(ts=_v["ts"], price=_v["price"], avg_price=_v["sell"]["avg_price"])
                     for _v in series_data if "sell" in _v]
        buy_data = [dict(ts=_v["ts"], price=_v["price"], avg_price=_v["buy"]["avg_price"])
                    for _v in series_data if "buy" in _v]
        sell_high_price_data = max(sell_data, key=lambda x: x["avg_price"]) \
            if len(sell_data) > 0 else {}
        buy_low_price_data = min(buy_data, key=lambda x: x["avg_price"]) \
            if len(buy_data) > 0 else {}
        return dict(
            sell_high_price_data=sell_high_price_data,
            buy_low_price_data=buy_low_price_data,
            buy_avg_price=price_format(buy_avg_price),
            sell_avg_price=price_format(sell_avg_price),
            total_buy_amount=amount_format(total_buy_amount),
            total_buy_money=quantize_amount(total_buy_money, PrecisionEnum.COIN_PLACES),
            total_sell_amount=amount_format(total_sell_amount),
            total_sell_money=quantize_amount(total_sell_money, PrecisionEnum.COIN_PLACES),
            empty=self.empty,
            series=series_data
        )
