# -*- coding: utf-8 -*-
import hashlib

import requests
from flask import current_app

from app import config
from app.common import CeleryQueues
from app.utils import celery_task, route_module_to_celery_queue
from app.caches.statistics import AlertUserTimeCache

route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


def get_msg_handle_func(url):
    return send_slack_message
    # MSG_DOMAIN_MAP = {
    #     #  https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xx
    #     "qyapi.weixin.qq.com": send_wechat_work_message,
    #     # https://hooks.slack.com/services/xx/xx/xx
    #     "hooks.slack.com": send_slack_message,
    # }
    # for domain, func in MSG_DOMAIN_MAP.items():
    #     if domain in url:
    #         return func
    # raise ValueError(f"The domain of the msg URL need in {tuple(MSG_DOMAIN_MAP.keys())}")


def send_alert_notice(content: str, url: str, expired_seconds: int = 0, msg_id: str = None, at: str = None,) -> bool:
    """ 发送系统(告警)消息 """
    if not content or not url:
        return False  # for testing env
    # 如果有多个人，用逗号分隔","
    if at:
        at_users = at.split(",")
        at_str = " ".join([f"<@{_s}>" for _s in at_users])
        content = f"{content}\n{at_str}"

    send_func = get_msg_handle_func(url)
    if expired_seconds <= 0:
        send_func.delay(content, url)
        return True

    if not msg_id:
        msg_id = hashlib.md5(content.encode()).hexdigest()
    cache = AlertUserTimeCache(msg_id)
    if cache.exists():
        return False
    send_func.delay(content, url)
    cache.set_cache(expired_seconds)
    return True


@celery_task
def send_wechat_work_message(content: str, webhook_url: str) -> None:
    """发送企业微信消息
    example: {
        "webhook": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=XXXX"
    }
    """
    build_text_json = {
        "msgtype": "text",
        "text": {
            "content": content,
        }
    }
    # noinspection PyBroadException
    _do_request(url=webhook_url, json=build_text_json)


def _do_request(url: str, json: dict) -> None:
    resp = requests.post(url=url, json=json, timeout=15)
    if resp.status_code == 200:
        result = resp.json()
        if result.get("errcode") != 0:
            current_app.logger.error(f"{json} invalid code, response: {result}")
    else:
        current_app.logger.error(f"send request error: status code: {resp.status_code}, text: {resp.text}")


@celery_task
def send_telegram_message(content: str, webhook_url: str, chat_id: int) -> None:
    """ 发送电报消息 """
    build_text_json = {
        "chat_id": chat_id,
        "text": content
    }
    _do_telegram_request(url=webhook_url, json=build_text_json)


def _do_telegram_request(url: str, json: dict) -> None:
    resp = requests.post(url=url, json=json, timeout=15)
    if resp.status_code == 200:
        result = resp.json()
        if not result.get("ok"):
            current_app.logger.error(f"{json} invalid code, response: {result}")
    else:
        current_app.logger.error(f"send telegram request error: status code: {resp.status_code}, text: {resp.text}")


@celery_task
def send_slack_message(content: str, channel_id: str) -> None:
    """ 发送slack消息 """
    build_text_json = {
        "text": content
    }
    _do_slack_request(channel_id=channel_id, json=build_text_json)


def _do_slack_request(channel_id: str, json: dict) -> None:
    resp = requests.post(
        config["SLACK_MSG_URL"],
        headers={'Authorization': f'Bearer {config["SLACK_TOKEN"]}'},
        json={**json, 'channel': channel_id},
        timeout=15
    )

    # slack 直接使用 http code 来反馈错误，非200就是各种异常。
    if resp.status_code != 200:
        error_msg = f"send channel id: {channel_id} msg request error: status code: {resp.status_code}, text: {resp.text}"
        current_app.logger.error(error_msg)

