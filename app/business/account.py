from typing import Dict, Callable

from app.business import SPOT_ACCOUNT_ID
from app.models import db, \
    AccountTransferLog
from app.models.margin import MarginTransferHistory
from app.models.perpetual import PerpetualBalanceTransfer
from app.models.investment import InvestmentAccount, InvestmentBalanceHistory
from app.models.copy_trading import CopyTransferHistory


class AccountTransferLogHelper:
    """ 写入杠杠、合约、理财的划转历史到日志表 """
    model = AccountTransferLog

    @classmethod
    def parse_margin_tran(cls, row: MarginTransferHistory) -> dict:
        if row.transfer_type == MarginTransferHistory.TransferType.IN:
            source_account_type = cls.model.AccountType.SPOT
            target_account_type = cls.model.AccountType.MARGIN
        elif row.transfer_type == MarginTransferHistory.TransferType.OUT:
            source_account_type = cls.model.AccountType.MARGIN
            target_account_type = cls.model.AccountType.SPOT
        else:
            raise RuntimeError(f"parse MarginTransferHistory not support transfer_type:{row.transfer_type.name}")
        return dict(
            created_at=row.created_at,
            user_id=row.user_id,
            source_account_type=source_account_type,
            target_account_type=target_account_type,
            source_account_id=row.from_account_id,
            target_account_id=row.to_account_id,
            asset=row.asset,
            amount=abs(row.amount),
            transfer_id=row.id,
        )

    @classmethod
    def parse_perpetual_tran(cls, row: PerpetualBalanceTransfer) -> dict:
        if row.transfer_type == PerpetualBalanceTransfer.TransferType.TRANSFER_IN:
            source_account_type = cls.model.AccountType.SPOT
            source_account_id = SPOT_ACCOUNT_ID
            target_account_type = cls.model.AccountType.PERPETUAL
            target_account_id = None
        elif row.transfer_type == PerpetualBalanceTransfer.TransferType.TRANSFER_OUT:
            source_account_type = cls.model.AccountType.PERPETUAL
            source_account_id = None
            target_account_type = cls.model.AccountType.SPOT
            target_account_id = SPOT_ACCOUNT_ID
        else:
            raise RuntimeError(f"parse PerpetualBalanceTransfer not support transfer_type:{row.transfer_type.name}")
        return dict(
            created_at=row.created_at,
            user_id=row.user_id,
            source_account_type=source_account_type,
            target_account_type=target_account_type,
            source_account_id=source_account_id,
            target_account_id=target_account_id,
            asset=row.coin_type,
            amount=abs(row.amount),
            transfer_id=row.id,
        )

    @classmethod
    def parse_investment_tran(cls, row: InvestmentBalanceHistory) -> dict:
        if row.opt_type == InvestmentBalanceHistory.OptType.IN:
            source_account_type = cls.model.AccountType.SPOT
            source_account_id = SPOT_ACCOUNT_ID
            target_account_type = cls.model.AccountType.INVESTMENT
            target_account_id = InvestmentAccount.ACCOUNT_ID
        elif row.opt_type == InvestmentBalanceHistory.OptType.OUT:
            source_account_type = cls.model.AccountType.INVESTMENT
            source_account_id = InvestmentAccount.ACCOUNT_ID
            target_account_type = cls.model.AccountType.SPOT
            target_account_id = SPOT_ACCOUNT_ID
        else:
            raise RuntimeError(f"parse InvestmentBalanceHistory not support opt_type:{row.opt_type.name}")
        return dict(
            created_at=row.created_at,
            user_id=row.user_id,
            source_account_type=source_account_type,
            target_account_type=target_account_type,
            source_account_id=source_account_id,
            target_account_id=target_account_id,
            asset=row.asset,
            amount=abs(row.amount),
            transfer_id=row.id,
        )

    @classmethod
    def parse_copy_trading_tran(cls, row: CopyTransferHistory) -> dict:
        if row.type in [
            CopyTransferHistory.Type.TRADER_TRANSFER_IN,
            CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN,
        ]:
            source_account_type = cls.model.AccountType.SPOT
            source_account_id = SPOT_ACCOUNT_ID
            target_account_type = cls.model.AccountType.SUB
            target_account_id = None
        elif row.type in [
            CopyTransferHistory.Type.TRADER_TRANSFER_OUT,
            CopyTransferHistory.Type.FOLLOWER_TRANSFER_OUT,
        ]:
            source_account_type = cls.model.AccountType.SUB
            source_account_id = None
            target_account_type = cls.model.AccountType.SPOT
            target_account_id = SPOT_ACCOUNT_ID
        else:
            raise RuntimeError(f"parse CopyTransferHistory not support type:{row.type.name}")
        return dict(
            created_at=row.created_at,
            user_id=row.main_user_id,
            source_account_type=source_account_type,
            target_account_type=target_account_type,
            source_account_id=source_account_id,
            target_account_id=target_account_id,
            asset=row.asset,
            amount=abs(row.amount),
            transfer_id=row.id,
        )

    @classmethod
    def parse_transfer(cls, transfer_row) -> dict:
        model_parse_func_map: Dict[object, Callable] = {
            MarginTransferHistory: cls.parse_margin_tran,
            PerpetualBalanceTransfer: cls.parse_perpetual_tran,
            InvestmentBalanceHistory: cls.parse_investment_tran,
            CopyTransferHistory: cls.parse_copy_trading_tran,
        }
        _func = model_parse_func_map.get(type(transfer_row))
        if not _func:
            raise RuntimeError(f"not support transfer {transfer_row.__class__.__name__}")

        data = _func(transfer_row)
        return data

    @classmethod
    def add_log_by_transfer(cls, transfer_row) -> AccountTransferLog:
        data = cls.parse_transfer(transfer_row)
        log = AccountTransferLog(**data)
        db.session.add(log)
        db.session.commit()
        return log
