# -*- coding: utf-8 -*-
import json
from typing import Optional
from decimal import Decimal
from datetime import date, timedelta
from collections import defaultdict

from flask import current_app
from sqlalchemy import func

from app.config import config
from app.models import (
    db,
    Activity,
    AssetPrice,
    GiftHistory,
    Referral,
    ReferralHistory,
    ReferralAssetDetail,
    ReferralAssetHistory,
    BusinessAmbassadorAudit,
    BusinessAmbassador, BusinessAmbassadorShareUser, BusinessUser, BusinessTeam,
    BusinessReferralAssetDetail, BusinessAgentReferralAssetHistory, BusinessUserReferralAssetHistory,
    BusinessAmbassadorLoan, BusinessAmbassadorRepayHistory, SpecialReferreeUserRate, BusinessUserShareRateSnapshot,
)
from app.models.broker import BrokerReferralAssetDetail
from app.business import ServerClient, ServerResponseCode, BalanceBusiness, SPOT_ACCOUNT_ID
from app.business.email import send_bus_amb_notice_email
from app.utils import batch_iter, quantize_amount
from app.utils.date_ import next_month, today, now
from app.utils.parser import JsonEncoder


class BusRelationUserQuerier:
    """ 商务相关用户的查询 """
    @classmethod
    def get_bus_ambassador(cls, user_id: int, need_valid: bool) -> Optional[BusinessAmbassador]:
        """ 获取商务大使 """
        model = BusinessAmbassador
        query = model.query.filter(
            model.user_id == user_id,
        )
        if need_valid:
            query = query.filter(model.status == model.Status.VALID)
        return query.first()

    @classmethod
    def get_all_invalid_bus_ambassadors(cls) -> list[BusinessAmbassador]:
        """ 获取全部失效的商务大使 """
        bus_amb_rows = BusinessAmbassador.query.filter(
            BusinessAmbassador.status == BusinessAmbassador.Status.DELETED,
        ).all()
        return bus_amb_rows

    @classmethod
    def get_all_valid_bus_ambassadors(cls) -> list[BusinessAmbassador]:
        """ 获取全部正常的商务大使 """
        bus_amb_rows = BusinessAmbassador.query.filter(
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
        ).all()
        return bus_amb_rows

    @classmethod
    def get_all_bus_ambassadors(cls) -> list[BusinessAmbassador]:
        return BusinessAmbassador.query.filter().all()

    @classmethod
    def get_bus_user(cls, user_id: int, need_valid: bool) -> Optional[BusinessUser]:
        """ 获取商务用户 """
        model = BusinessUser
        query = model.query.filter(
            model.user_id == user_id,
        )
        if need_valid:
            query = query.filter(model.status == model.Status.VALID)
        return query.first()

    @classmethod
    def get_all_valid_bus_users(cls) -> list[BusinessUser]:
        bus_users = BusinessUser.query.filter(
            BusinessUser.status == BusinessUser.Status.VALID,
        ).all()
        return bus_users

    @classmethod
    def get_all_valid_bus_teams(cls) -> list[BusinessTeam]:
        bus_teams = BusinessTeam.query.filter(
            BusinessTeam.status == BusinessTeam.Status.VALID,
        ).all()
        return bus_teams

    @classmethod
    def get_bus_ambassador_share_users_map(
        cls,
        user_ids: list[int] = None,
    ) -> dict[int, list[BusinessAmbassadorShareUser]]:
        """ 获取商务大使的分成用户 """
        model = BusinessAmbassadorShareUser
        q = model.query.filter(model.status == model.Status.VALID)
        if user_ids:
            q = q.filter(model.bus_amb_id.in_(user_ids))
        rows = q.all()
        res = defaultdict(list)
        for r in rows:
            res[r.bus_amb_id].append(r)
        return res


class BusReferDetailHelper:
    """ 商务相关(商务代理、商务团队)的返佣明细，依赖大使返佣明细的数据 """

    REFER_ASSET = "USDT"  # 返佣币种

    @classmethod
    def check_data_ready(cls, date_: date) -> bool:
        # ReferralAssetDetail 和 BrokerReferralAssetDetail 生成时间不同
        if not ReferralAssetDetail.query.filter(
            ReferralAssetDetail.date == date_,
        ).first():
            current_app.logger.warning(f"BusReferDetailHelper {date_} ReferralAssetDetail not_ready")
            return False
        if not BrokerReferralAssetDetail.query.filter(
            BrokerReferralAssetDetail.date == date_,
        ).first():
            current_app.logger.warning(f"BusReferDetailHelper {date_} BrokerReferralAssetDetail not_ready")
            return False
        return True

    @classmethod
    def generate_bus_user_share_rate_snapshot(cls, date_: date, is_commit: bool):
        exist_snap_rows = BusinessUserShareRateSnapshot.query.filter(
            BusinessUserShareRateSnapshot.date == date_,
        ).all()
        exist_snap_map: dict[int, BusinessUserShareRateSnapshot] = {i.bus_user_id: i for i in exist_snap_rows}

        bus_users: list[BusinessUser] = BusinessUser.query.all()  # 已删除的商务也快照
        res_snap_rows = []
        team_rows: list[BusinessTeam] = BusinessTeam.query.all()
        team_map = {i.id: i for i in team_rows}
        for bus_user in bus_users:
            team: BusinessTeam = team_map.get(bus_user.team_id)
            if not team:
                continue
            valid_pr_rates = [p for p in bus_user.pr_rates if p['user_id'] in team.pr_user_ids]
            if snap := exist_snap_map.get(bus_user.user_id):
                snap.team_id = team.id
                snap.leader_id = team.leader_id
                snap.leader_rate = bus_user.leader_rate
                snap.pr_rates = valid_pr_rates
            else:
                snap = BusinessUserShareRateSnapshot(
                    date=date_,
                    bus_user_id=bus_user.user_id,
                    team_id=team.id,
                    leader_id=team.leader_id,
                    leader_rate=bus_user.leader_rate,
                    pr_rates=valid_pr_rates,
                )
            db.session.add(snap)
            res_snap_rows.append(snap)
        if is_commit:
            db.session.commit()
        return res_snap_rows

    @classmethod
    def get_bus_amb_ee_special_ref_rate(cls, bus_amb_map: dict):
        """ 找出针对商务大使生效的被邀请人特殊比例 """
        rows = SpecialReferreeUserRate.query.filter(
            SpecialReferreeUserRate.status == SpecialReferreeUserRate.Status.VALID,
        ).with_entities(
            SpecialReferreeUserRate.referree_id,
            SpecialReferreeUserRate.referrer_id,
            SpecialReferreeUserRate.rate,
        ).all()
        result = {}
        for r in rows:
            if r.referrer_id in bus_amb_map:
                # 被邀请人设置的新的返佣比例 对于 商务大使来说 是<商务大使+商务代理>整体的返佣比例
                # 此处返回整体的新返佣比例
                result[r.referree_id] = r.rate
        return result

    @classmethod
    def make_detail(cls, date_: date):
        """
        查当天的ReferralAssetDetail，user_id是商务大使的，得到被邀请人当天的交易手续费
        按照商务代理的返佣rate、团队的返佣team_rate，计算返佣数目，写入BusinessReferralAssetDetail
        """
        if not cls.check_data_ready(date_):
            return

        if BusinessReferralAssetDetail.query.filter(
            BusinessReferralAssetDetail.date == date_,
        ).first():
            current_app.logger.warning(f"BusReferDetailHelper_make_detail {date_} detail record existed")
            return

        ref_details: list[ReferralAssetDetail] = ReferralAssetDetail.query.filter(
            ReferralAssetDetail.date == date_,
            ReferralAssetDetail.type == ReferralAssetHistory.Type.AMBASSADOR,
            ReferralAssetDetail.asset == cls.REFER_ASSET,
        ).all()
        referral_ids = {i.referral_id for i in ref_details}
        referral_id_user_map = {}
        for ch_ids in batch_iter(referral_ids, 5000):
            ch_rows = Referral.query.filter(Referral.id.in_(ch_ids)).with_entities(
                Referral.id,
                Referral.user_id,
            )
            referral_id_user_map.update(dict(ch_rows))

        ee_ref_detail_map: dict[int, ReferralAssetDetail] = {
            i.referree_id: i for i in ref_details
        }  # 去重，每个被邀请人只保留一条（邀请码设置分成时会有2条：A->A A->B ）

        bus_amb_rows = BusRelationUserQuerier.get_all_valid_bus_ambassadors()
        bus_amb_map: dict[int, BusinessAmbassador] = {i.user_id: i for i in bus_amb_rows}
        bro_bus_amb_map: dict[int, BusinessAmbassador] = {i.user_id: i for i in bus_amb_rows if i.is_broker_amb}
        bus_amb_ee_sp_ref_rate_map = cls.get_bus_amb_ee_special_ref_rate(bus_amb_map)

        bus_share_users = BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
        ).all()
        bus_amb_share_users_map: dict[int, list[BusinessAmbassadorShareUser]] = defaultdict(list)
        for s in bus_share_users:
            s: BusinessAmbassadorShareUser
            bus_amb_share_users_map[s.bus_amb_id].append(s)

        bus_users = BusRelationUserQuerier.get_all_valid_bus_users()
        bus_user_map: dict[int, BusinessUser] = {i.user_id: i for i in bus_users}

        # 如果某一笔交易同时存在Broker和商务大使，且该Broker和商务大使上面都有商务团队
        # 对于Broker和商务大使上面商务团队的返佣，需要拿手续费减半，再乘对应的返佣比例去算返佣
        broker_details: list[BrokerReferralAssetDetail] = BrokerReferralAssetDetail.query.filter(
            BrokerReferralAssetDetail.date == date_,
        ).all()
        ee_bro_spot_fee_map = defaultdict(Decimal)  # 某一笔交易 给 商务大使-商务团队 的返佣
        ee_bro_per_fee_map = defaultdict(Decimal)
        ee_bro_ref_data_list = []  # 某一笔交易 给 Broker商务大使-商务团队 的返佣
        for r in broker_details:
            _ee_id = r.user_id
            _er_user_id = referral_id_user_map.get(_ee_id)  # 邀请人
            _is_bro_amb = r.broker_user_id in bro_bus_amb_map  # broker 是否是 broker商务大使
            _to_amb_team_rate = Decimal("0.5") if _is_bro_amb else Decimal("1")  # 手续费减半（给 商务大使-商务团队 的占比）
            _has_split = False
            if _er_user_id and _er_user_id in bus_amb_map:
                # _ee_id 是 商务大使邀请
                _bus_amb = bus_amb_map[_er_user_id]
                if _bus_amb.bus_user_id and _bus_amb.bus_user_id in bus_user_map:
                    # 商务大使 上面有团队
                    _has_split = True
                    __spot = quantize_amount(r.spot_fee_usd * r.refer_fee_usd_rate * _to_amb_team_rate, 8)
                    ee_bro_spot_fee_map[r.user_id] += __spot
                    __per = quantize_amount(r.perpetual_fee_usd * r.refer_fee_usd_rate * _to_amb_team_rate, 8)
                    ee_bro_per_fee_map[r.user_id] += __per
            if _is_bro_amb:
                _remain_rate = Decimal(1) - _to_amb_team_rate if _has_split else Decimal(1)
                __spot_b = quantize_amount(r.spot_fee_usd * r.refer_fee_usd_rate * _remain_rate, 8)
                __per_b = quantize_amount(r.perpetual_fee_usd * r.refer_fee_usd_rate * _remain_rate, 8)
                _b_ref_data = [r.user_id, r.broker_user_id, __spot_b, __per_b]
                ee_bro_ref_data_list.append(_b_ref_data)

        asset_rates = AssetPrice.get_close_price_map(date_)

        new_details = []
        for ref_d in ee_ref_detail_map.values():
            ee_user_id = ref_d.referree_id
            er_user_id = referral_id_user_map.get(ref_d.referral_id)  # 邀请人
            if not er_user_id:
                continue
            if er_user_id not in bus_amb_map:
                continue

            # 是商务大使，则再给商务代理、商务团队返佣
            bus_amb = bus_amb_map[er_user_id]

            # 检查<大使商务和商务代理的总发佣比例>是否正常
            share_users = bus_amb_share_users_map[er_user_id]
            total_rate = bus_amb.rate + sum([su.rate for su in share_users])
            if not (0 <= total_rate <= Decimal(1)):
                current_app.logger.error(
                    f"BusReferDetailHelper_make_detail bus_amb {er_user_id} ee {ref_d.referree_id} "
                    f"total_rate_invalid {total_rate}"
                )
                continue

            if ee_user_id in bus_amb_ee_sp_ref_rate_map:
                _new_total_rate = bus_amb_ee_sp_ref_rate_map[ee_user_id]
                _new_total_rate = min(total_rate, _new_total_rate)
                # 是特殊被邀请人，整体返佣比例下降，计算占比
                change_rate = _new_total_rate / total_rate if total_rate else Decimal(0)
            else:
                _new_total_rate = total_rate
                change_rate = Decimal(1)
            # 写入商务代理的detail，用于商务代理计算返佣数的手续费USD和商务大使相同
            spot_fee_usd = ref_d.spot_fee_usd
            per_fee_usd = ref_d.perpetual_fee_usd
            for su in share_users:
                su_ref_rate = su.rate * change_rate  # 商务代理也受特殊比例影响
                r_spot_amount = quantize_amount(spot_fee_usd * su_ref_rate / asset_rates[cls.REFER_ASSET], 8)
                r_per_amount = quantize_amount(per_fee_usd * su_ref_rate / asset_rates[cls.REFER_ASSET], 8)
                if (r_spot_amount + r_per_amount) <= 0:
                    continue
                new_details.append(
                    BusinessReferralAssetDetail(
                        date=date_,
                        bus_agent_id=su.target_user_id,
                        bus_amb_id=er_user_id,
                        referree_id=ref_d.referree_id,
                        referral_id=ref_d.referral_id,
                        type=BusinessReferralAssetDetail.Type.BUS_AGENT,
                        asset=cls.REFER_ASSET,
                        rate=su.rate,
                        spot_amount=r_spot_amount,
                        perpetual_amount=r_per_amount,
                        spot_fee_usd=spot_fee_usd,
                        perpetual_fee_usd=per_fee_usd,
                    )
                )

            if bus_amb.bus_user_id and bus_amb.bus_user_id in bus_user_map:
                # 用于商务团队计算返佣数的手续费USD，邀请人是商务大使时：还要包括 给经纪商返佣的那部分手续费
                t_spot_fee_usd = spot_fee_usd + ee_bro_spot_fee_map.get(ref_d.referree_id, 0)
                t_per_fee_usd = per_fee_usd + ee_bro_per_fee_map.get(ref_d.referree_id, 0)
                # 商务团队（商务组长+商务）的返佣比例为：（1-X）*Y
                # X指的是（商务大使+其关联商务代理）的返佣比例之和；Y是指商务大使给团队的返佣系数
                bus_user = bus_user_map[bus_amb.bus_user_id]
                team_refer_rate = (1 - _new_total_rate) * bus_amb.team_rate  # 商务团队也受特殊比例影响
                rt_spot_amount = quantize_amount(t_spot_fee_usd * team_refer_rate / asset_rates[cls.REFER_ASSET], 8)
                rt_per_amount = quantize_amount(t_per_fee_usd * team_refer_rate / asset_rates[cls.REFER_ASSET], 8)
                if (rt_spot_amount + rt_per_amount) > 0:
                    new_details.append(
                        BusinessReferralAssetDetail(
                            date=date_,
                            bus_user_id=bus_amb.bus_user_id,
                            team_id=bus_user.team_id,
                            bus_amb_id=er_user_id,
                            referree_id=ref_d.referree_id,
                            referral_id=ref_d.referral_id,
                            type=BusinessReferralAssetDetail.Type.BUS_TEAM,
                            asset=cls.REFER_ASSET,
                            rate=team_refer_rate,
                            leader_rate=bus_user.total_leader_rate,  # 团队的全部组长的分成比例总和（组长+PR）
                            spot_amount=rt_spot_amount,
                            perpetual_amount=rt_per_amount,
                            spot_fee_usd=t_spot_fee_usd,
                            perpetual_fee_usd=t_per_fee_usd,
                        )
                    )

        for b_ref_d in ee_bro_ref_data_list:
            b_ee, bro_user_id, spot_fee_usd, per_fee_usd = b_ref_d
            if bro_user_id not in bro_bus_amb_map:
                continue

            # 是broker商务大使，则再给商务代理、商务团队返佣
            bro_bus_amb = bus_amb_map[bro_user_id]

            # 检查<大使商务和商务代理的总发佣比例>是否正常
            bro_share_users = bus_amb_share_users_map[bro_user_id]
            total_rate = bro_bus_amb.rate + sum([su.rate for su in bro_share_users])
            if not (0 <= total_rate <= Decimal(1)):
                current_app.logger.error(
                    f"BusReferDetailHelper_make_detail bro_bus_amb {bro_user_id} ee {b_ee} "
                    f"total_rate_invalid {total_rate}"
                )
                continue

            if bro_bus_amb.bus_user_id and bro_bus_amb.bus_user_id in bus_user_map:
                t_spot_fee_usd = spot_fee_usd
                t_per_fee_usd = per_fee_usd
                bus_user = bus_user_map[bro_bus_amb.bus_user_id]
                team_refer_rate = (1 - total_rate) * bro_bus_amb.team_rate
                rt_spot_amount = quantize_amount(t_spot_fee_usd * team_refer_rate / asset_rates[cls.REFER_ASSET], 8)
                rt_per_amount = quantize_amount(t_per_fee_usd * team_refer_rate / asset_rates[cls.REFER_ASSET], 8)
                if (rt_spot_amount + rt_per_amount) > 0:
                    new_details.append(
                        BusinessReferralAssetDetail(
                            date=date_,
                            bus_user_id=bro_bus_amb.bus_user_id,
                            team_id=bus_user.team_id,
                            bus_amb_id=bro_user_id,
                            referree_id=b_ee,
                            referral_id=None,
                            type=BusinessReferralAssetDetail.Type.BROKER_TO_BUS_TEAM,
                            asset=cls.REFER_ASSET,
                            rate=team_refer_rate,
                            leader_rate=bus_user.total_leader_rate,  # 团队的全部组长的分成比例总和（组长+PR）
                            spot_amount=rt_spot_amount,
                            perpetual_amount=rt_per_amount,
                            spot_fee_usd=t_spot_fee_usd,
                            perpetual_fee_usd=t_per_fee_usd,
                        )
                    )

        for rows in batch_iter(new_details, 1000):
            db.session.bulk_save_objects(rows)
        cls.generate_bus_user_share_rate_snapshot(date_, is_commit=False)
        db.session.commit()
        current_app.logger.warning(f"BusReferDetailHelper_make_detail {date_} new {len(new_details)} detail records")


class BusAgentReferHelper:
    @classmethod
    def send_referral_reward(cls, date_: date):
        """ 商务代理的返佣发放 """
        BusReferDetailHelper.make_detail(date_)
        cls.summary_asset_history(date_)

        agent_ref_rows = BusinessAgentReferralAssetHistory.query.filter(
            BusinessAgentReferralAssetHistory.date == date_,
            BusinessAgentReferralAssetHistory.status == BusinessAgentReferralAssetHistory.Status.CREATED,
        ).all()
        created_at = date_ + timedelta(days=1)
        gift_rows = []
        for row in agent_ref_rows:
            gift = GiftHistory(
                created_at=created_at,
                user_id=row.user_id,
                activity_id=Activity.REFERRAL_ID,
                asset=row.asset,
                amount=row.amount,
                remark='gift for bus_agent refer',
                status=GiftHistory.Status.CREATED,
            )
            gift_rows.append(gift)
        for rows in batch_iter(gift_rows, 1000):
            db.session.bulk_save_objects(rows)

        BusinessAgentReferralAssetHistory.query.filter(
            BusinessAgentReferralAssetHistory.date == date_,
        ).update(
            {BusinessAgentReferralAssetHistory.status: BusinessAgentReferralAssetHistory.Status.FINISHED},
            synchronize_session=False,
        )
        db.session.commit()

    @classmethod
    def summary_asset_history(cls, date_: date):
        """ 汇总商务代理的返佣明细，写入流水表 """
        if BusinessAgentReferralAssetHistory.query.filter(
            BusinessAgentReferralAssetHistory.date == date_,
        ).first():
            current_app.logger.warning(f"BusAgentReferHelper_summary_asset_history {date_} history record existed")
            return

        sum_details = BusinessReferralAssetDetail.query.filter(
            BusinessReferralAssetDetail.date == date_,
            BusinessReferralAssetDetail.type == BusinessReferralAssetDetail.Type.BUS_AGENT,
            BusinessReferralAssetDetail.bus_agent_id.is_not(None),
        ).group_by(
            BusinessReferralAssetDetail.bus_agent_id,
            BusinessReferralAssetDetail.asset,
        ).with_entities(
            BusinessReferralAssetDetail.bus_agent_id,
            BusinessReferralAssetDetail.asset,
            func.sum(BusinessReferralAssetDetail.spot_amount + BusinessReferralAssetDetail.perpetual_amount),
            func.sum(BusinessReferralAssetDetail.spot_amount),
            func.sum(BusinessReferralAssetDetail.perpetual_amount),
        ).all()

        ref_rows = []
        for bus_agent_id, asset, total_amount, spot_amount, perpetual_amount in sum_details:
            ref_rows.append(
                BusinessAgentReferralAssetHistory(
                    date=date_,
                    user_id=bus_agent_id,
                    asset=asset,
                    amount=total_amount,
                    spot_amount=spot_amount,
                    perpetual_amount=perpetual_amount,
                )
            )
        for rows in batch_iter(ref_rows, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()
        current_app.logger.warning(f"BusAgentReferHelper_summary_asset_history {date_} "
                                   f"new {len(ref_rows)} history records")


class BusUserReferHelper:
    @classmethod
    def send_referral_reward(cls, month_: date):
        """ 商务｜商务组长的返佣发放，每月1号发上个月的，month_：上月1号 """
        start_date = month_
        end_date = next_month(start_date.year, start_date.month)  # 不包括下月1号的detail
        cls.summary_asset_history(start_date, end_date)

        bus_ref_rows = BusinessUserReferralAssetHistory.query.filter(
            BusinessUserReferralAssetHistory.date == start_date,
            BusinessUserReferralAssetHistory.status == BusinessUserReferralAssetHistory.Status.CREATED,
        ).all()
        for row in bus_ref_rows:
            # 划转，不走gift
            try:
                cls.transfer_from_history(row)
            except Exception as e:
                if isinstance(e, ServerClient.BadResponse) and e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                    break  # 余额不足，不继续划转了
                current_app.logger.error(
                    f"BusUserReferHelper_transfer {row.id} {row.user_id} {row.asset} {row.amount} error {e!r}"
                )

    @classmethod
    def _is_can_refer(
        cls,
        detail: BusinessReferralAssetDetail,
        bus_amb_map: dict[int, BusinessAmbassador],
        bus_user_map: dict[int, BusinessUser],
        bus_team_map: dict[int, BusinessTeam],
    ) -> tuple[bool, bool, bool]:
        """
        某个商务换了组（从A组换到B组） 或 某个商务换了组长（组长从用户A改为用户B）；
        则到了下个月1号结算时，上月数据的返佣情况为：
            这个商务名下的大使带来的手续费不给该商务的组长返佣，但需要正常给该商务返佣；
        PR是否返佣 和 组长是否返佣 基本保持一致，除了修改组长不影响PR

        return (bus_user_can_refer, leader_user_can_refer， pr_user_can_refer)
        """
        bus_amb = bus_amb_map.get(detail.bus_amb_id)
        if not bus_amb:
            # 商务大使失效了
            return False, False, False
        if not bus_amb.bus_refer_start_at or not bus_amb.bus_user_id:
            # 商务大使没绑定商务
            return False, False, False
        if bus_amb.bus_refer_start_at > detail.date:
            # 商务大使上个月修改了绑定的商务 or 重新生效了
            return False, False, False
        if bus_amb.bus_user_id != detail.bus_user_id:
            # 绑定的商务变化
            return False, False, False
        bus_user = bus_user_map.get(detail.bus_user_id)
        if not bus_user:
            # 商务失效了
            return False, False, False

        # 下面的判断：商务正常返佣，仅判断商务组长
        if bus_user.leader_refer_start_at > detail.date:
            # 商务上个月修改了绑定的团队 or 重新生效了
            return True, False, False
        if detail.team_id != bus_user.team_id:
            # 团队变化
            return True, False, False
        bus_team = bus_team_map.get(detail.team_id)
        if not bus_team:
            # 团队删除了
            return True, False, False
        if bus_team.leader_refer_start_at > detail.date:
            # 团队上个月修改了组长（团队没有重新生效的逻辑），对PR没有影响
            return True, False, True
        return True, True, True

    @classmethod
    def summary_asset_history(cls, start_date: date, end_date: date):
        """ 汇总商务和商务组长的返佣明细，写入流水表 """
        if BusinessUserReferralAssetHistory.query.filter(
            BusinessUserReferralAssetHistory.date == start_date,
        ).first():
            current_app.logger.warning(f"BusUserReferHelper_summary_asset_history {start_date} history record existed")
            return

        zero = Decimal()
        bus_amb_rows = BusRelationUserQuerier.get_all_valid_bus_ambassadors()
        bus_amb_map: dict[int, BusinessAmbassador] = {i.user_id: i for i in bus_amb_rows}
        bus_users = BusRelationUserQuerier.get_all_valid_bus_users()
        bus_user_map: dict[int, BusinessUser] = {i.user_id: i for i in bus_users}
        bus_teams = BusinessTeam.query.filter(
            BusinessTeam.status == BusinessTeam.Status.VALID,
        ).all()
        bus_team_map: dict[int, BusinessTeam] = {i.id: i for i in bus_teams}

        bus_share_users = BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
        ).all()
        bus_amb_share_users_map: dict[int, list[BusinessAmbassadorShareUser]] = defaultdict(list)
        for s in bus_share_users:
            s: BusinessAmbassadorShareUser
            bus_amb_share_users_map[s.bus_amb_id].append(s)

        date_bus_user_share_rate_map = defaultdict(dict)
        share_rate_snap_rows = BusinessUserShareRateSnapshot.query.filter(
            BusinessUserShareRateSnapshot.date >= start_date,
            BusinessUserShareRateSnapshot.date < end_date,
        ).all()
        for s_snap in share_rate_snap_rows:
            s_snap: BusinessUserShareRateSnapshot
            date_bus_user_share_rate_map[s_snap.date][s_snap.bus_user_id] = s_snap

        details: list[BusinessReferralAssetDetail] = BusinessReferralAssetDetail.query.filter(
            BusinessReferralAssetDetail.date >= start_date,
            BusinessReferralAssetDetail.date < end_date,
            BusinessReferralAssetDetail.type.in_(
                [BusinessReferralAssetDetail.Type.BUS_TEAM, BusinessReferralAssetDetail.Type.BROKER_TO_BUS_TEAM]
            ),
        ).all()

        # key: (user_id, asset, type, team_id)
        ref_data_map = defaultdict(lambda: dict(spot_amount=Decimal(), perpetual_amount=Decimal()))
        for detail in details:
            bus_user_can_refer, leader_user_can_refer, pr_can_refer = cls._is_can_refer(detail, bus_amb_map, bus_user_map, bus_team_map)
            if not bus_user_can_refer and not leader_user_can_refer and not pr_can_refer:
                continue

            bus_amb = bus_amb_map.get(detail.bus_amb_id)
            if not bus_amb:
                continue
            bus_user = bus_user_map.get(detail.bus_user_id)
            if not bus_user:
                continue
            _team_id = detail.team_id
            bus_team = bus_team_map.get(_team_id)
            if not bus_team:
                continue

            share_users = bus_amb_share_users_map[detail.bus_amb_id]
            total_rate = bus_amb.rate + sum([su.rate for su in share_users])
            if not (0 <= total_rate <= Decimal(1)):
                current_app.logger.error(
                    f"BusUserReferHelper_summary_asset_history bus_amb {detail.bus_amb_id} "
                    f"ee {detail.referree_id} total_rate_invalid {total_rate}"
                )
                continue

            # 商务、组长、PR的返佣，按照每天分成快照来计算
            share_rate_snap: BusinessUserShareRateSnapshot = date_bus_user_share_rate_map[detail.date][detail.bus_user_id]  # must exist
            assert 0 <= share_rate_snap.total_leader_rate <= 1

            # 用上个月每天计算好的返佣累加结算
            team_r_spot_amount = detail.spot_amount
            team_r_per_amount = detail.perpetual_amount

            if detail.type == BusinessReferralAssetDetail.Type.BUS_TEAM:
                _bu_type = BusinessUserReferralAssetHistory.Type.BUS_USER
                _bl_type = BusinessUserReferralAssetHistory.Type.BUS_LEADER
                _bp_type = BusinessUserReferralAssetHistory.Type.BUS_PR
            else:
                _bu_type = BusinessUserReferralAssetHistory.Type.BROKER_TO_BUS_USER
                _bl_type = BusinessUserReferralAssetHistory.Type.BROKER_TO_BUS_LEADER
                _bp_type = BusinessUserReferralAssetHistory.Type.BROKER_TO_BUS_PR

            if bus_user_can_refer:
                bus_key = (detail.bus_user_id, detail.asset, _bu_type, _team_id)
                bus_user_rate = 1 - share_rate_snap.total_leader_rate
                bus_spot_amount = quantize_amount(team_r_spot_amount * bus_user_rate, 8)
                if bus_spot_amount > zero:
                    ref_data_map[bus_key]["spot_amount"] += bus_spot_amount
                bus_per_amount = quantize_amount(team_r_per_amount * bus_user_rate, 8)
                if bus_per_amount > zero:
                    ref_data_map[bus_key]["perpetual_amount"] += bus_per_amount

            if leader_user_can_refer:
                leader_key = (bus_team.leader_id, detail.asset, _bl_type, _team_id)
                leader_rate = share_rate_snap.leader_rate
                leader_spot_amount = quantize_amount(team_r_spot_amount * leader_rate, 8)
                if leader_spot_amount > zero:
                    ref_data_map[leader_key]["spot_amount"] += leader_spot_amount
                leader_per_amount = quantize_amount(team_r_per_amount * leader_rate, 8)
                if leader_per_amount > zero:
                    ref_data_map[leader_key]["perpetual_amount"] += leader_per_amount

            if pr_can_refer:
                # 判断PR的最新状态（如果在结算前一天把某组的某个PR删除了，那这个PR就不能享受该组的这次返佣结算了）
                cur_pr_user_ids = set(bus_team.pr_user_ids)
                pr_user_rate_map = {i['user_id']: Decimal(i['rate']) for i in share_rate_snap.pr_rates if i['user_id'] in cur_pr_user_ids}
                for pr_user_id, pr_rate in pr_user_rate_map.items():
                    pr_key = (pr_user_id, detail.asset, _bp_type, _team_id)
                    pr_spot_amount = quantize_amount(team_r_spot_amount * pr_rate, 8)
                    if pr_spot_amount > zero:
                        ref_data_map[pr_key]["spot_amount"] += pr_spot_amount
                    pr_per_amount = quantize_amount(team_r_per_amount * pr_rate, 8)
                    if pr_per_amount > zero:
                        ref_data_map[pr_key]["perpetual_amount"] += pr_per_amount

        ref_rows = []
        admin_user_id = config['OFFICIAL_ADMIN_USER_ID']
        for key_, amounts in ref_data_map.items():
            user_id, asset, type_, team_id_ = key_
            spot_m = amounts["spot_amount"]
            per_m = amounts["perpetual_amount"]
            total_m = spot_m + per_m
            if total_m <= zero:
                continue
            ref_rows.append(
                BusinessUserReferralAssetHistory(
                    date=start_date,  # 这个月发上个月的
                    team_id=team_id_,
                    user_id=user_id,
                    pay_user_id=admin_user_id,
                    type=type_,
                    asset=asset,
                    amount=total_m,
                    spot_amount=spot_m,
                    perpetual_amount=per_m,
                )
            )
        for rows in batch_iter(ref_rows, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()
        current_app.logger.warning(f"BusUserReferHelper_summary_asset_history new {len(ref_rows)} history records")

    @classmethod
    def transfer_from_history(cls, history: BusinessUserReferralAssetHistory):
        """ 划转 """
        if history.status != BusinessUserReferralAssetHistory.Status.CREATED:
            return
        client = ServerClient()
        client.batch_add_user_balance(
            [
                dict(
                    user_id=history.pay_user_id,  # 扣admin
                    asset=history.asset,
                    business=BalanceBusiness.BUS_USER_REFER,
                    business_id=history.id,
                    amount=str(-history.amount),
                    detail={'remark': f'bus_user refer {history.id} {history.type.name}'},
                    account_id=SPOT_ACCOUNT_ID,
                ),
                dict(
                    user_id=history.user_id,  # 加给商务
                    asset=history.asset,
                    business=BalanceBusiness.BUS_USER_REFER,
                    business_id=history.id,
                    amount=str(history.amount),
                    detail={'remark': f'bus_user refer {history.id} {history.type.name}'},
                    account_id=SPOT_ACCOUNT_ID,
                ),
            ]
        )
        history.status = BusinessUserReferralAssetHistory.Status.FINISHED
        db.session.add(history)
        db.session.commit()

    @classmethod
    def retry_from_history(cls, history: BusinessUserReferralAssetHistory):
        """ 重试划转 """
        client = ServerClient()
        if history.status == BusinessUserReferralAssetHistory.Status.CREATED:
            result = client.asset_query_business(
                user_id=history.pay_user_id,
                asset=history.asset,
                business=BalanceBusiness.BUS_USER_REFER,
                business_id=history.id,
                account_id=SPOT_ACCOUNT_ID,
            )
            if result:
                history.status = BusinessUserReferralAssetHistory.Status.DEDUCTED
                db.session.commit()
            else:
                client.add_user_balance(
                    user_id=history.pay_user_id,
                    asset=history.asset,
                    business=BalanceBusiness.BUS_USER_REFER,
                    business_id=history.id,
                    amount=str(-history.amount),
                    detail={'remark': f'bus_user refer {history.id}'},
                    account_id=SPOT_ACCOUNT_ID,
                )
                history.status = BusinessUserReferralAssetHistory.Status.DEDUCTED
                db.session.commit()

        if history.status != BusinessUserReferralAssetHistory.Status.DEDUCTED:
            return

        client.add_user_balance(
            user_id=history.user_id,  # 加给商务
            asset=history.asset,
            business=BalanceBusiness.BUS_USER_REFER,
            business_id=history.id,
            amount=str(history.amount),
            detail={'remark': f'bus_user refer {history.id}'},
            account_id=SPOT_ACCOUNT_ID,
        )
        history.status = BusinessUserReferralAssetHistory.Status.FINISHED
        db.session.commit()


class BusinessAmbassadorRepayHelper:
    @classmethod
    def repay(cls, refer_date: date = None):
        """ 商务大使还币
        1. 返佣币种和欠款币种相同时才还
        2. 失效的商务大使不还
        """
        loan_rows: list[BusinessAmbassadorLoan] = BusinessAmbassadorLoan.query.filter(
            BusinessAmbassadorLoan.status == BusinessAmbassadorLoan.Status.ARREARS
        ).order_by(BusinessAmbassadorLoan.id.asc()).with_entities(
            BusinessAmbassadorLoan.bus_amb_id,
            BusinessAmbassadorLoan.asset,
            BusinessAmbassadorLoan.amount,
            BusinessAmbassadorLoan.repay_amount,
        ).all()
        if not loan_rows:
            return

        amb_loans_map: dict[int, list[BusinessAmbassadorLoan]] = defaultdict(list)
        for r in loan_rows:
            amb_loans_map[r.bus_amb_id].append(r)

        bus_amb_rows = BusRelationUserQuerier.get_all_valid_bus_ambassadors()
        loan_bus_amb_ids = {i.user_id for i in bus_amb_rows} & set(amb_loans_map)

        if not refer_date:
            refer_date = today() - timedelta(days=1)  # 默认查欠款商务大使前一天的返佣数，来还钱

        ref_asset_amount_map = defaultdict(lambda: defaultdict(Decimal))
        for ch_ids in batch_iter(loan_bus_amb_ids, 2000):
            ch_ref_rows = ReferralAssetHistory.query.filter(
                ReferralAssetHistory.date == refer_date,
                ReferralAssetHistory.user_id.in_(ch_ids),
                ReferralAssetHistory.status == ReferralAssetHistory.Status.FINISHED,
            ).all()
            for r in ch_ref_rows:
                ref_asset_amount_map[r.user_id][r.asset] += r.amount

        client = ServerClient()
        for bus_amb_id, ref_asset_amounts in ref_asset_amount_map.items():
            ref_assets = set(ref_asset_amounts)  # 返佣币种
            amb_loans = amb_loans_map[bus_amb_id]
            loan_assets = {i.asset for i in amb_loans}  # 欠款币种
            repay_assets = ref_assets & loan_assets  # 要还款的币种
            if not repay_assets:
                continue

            balances = client.get_user_balances(bus_amb_id)
            for asset in repay_assets:
                ref_amount = ref_asset_amounts[asset]
                avai_amount = Decimal(balances.get(asset, {}).get("available", "0"))
                unflat_amount = sum([i.amount - i.repay_amount for i in amb_loans if i.asset == asset])
                will_repay_amount = min(ref_amount, avai_amount, unflat_amount)
                if will_repay_amount <= 0:
                    continue
                try:
                    cls.repay_amb_loan(bus_amb_id, asset, will_repay_amount, refer_date)
                except Exception as e:
                    current_app.logger.error(
                        f"BusinessAmbassadorRepayHelper_repay_one {bus_amb_id} {asset} "
                        f"{will_repay_amount} {refer_date} error {e!r}"
                    )

    @classmethod
    def repay_amb_loan(
        cls,
        bus_amb_id: int,
        repay_asset: str,
        repay_amount: Decimal,
        repay_date: date,
    ) -> list[BusinessAmbassadorRepayHistory]:
        loan_rows: list[BusinessAmbassadorLoan] = BusinessAmbassadorLoan.query.filter(
            BusinessAmbassadorLoan.bus_amb_id == bus_amb_id,
            BusinessAmbassadorLoan.asset == repay_asset,
            BusinessAmbassadorLoan.status == BusinessAmbassadorLoan.Status.ARREARS
        ).order_by(BusinessAmbassadorLoan.id.asc()).all()
        if not loan_rows:
            return []

        repay_his_rows = []
        remain_repay_amount = repay_amount
        for loan_row in loan_rows:
            cur_amount, his = cls.repay_one_loan_row(loan_row, remain_repay_amount, repay_date)
            repay_his_rows.append(his)
            remain_repay_amount -= cur_amount
            if remain_repay_amount <= 0:
                break
        return repay_his_rows

    @classmethod
    def repay_one_loan_row(
        cls,
        loan_row: BusinessAmbassadorLoan,
        repay_amount: Decimal,
        repay_date: date,
    ) -> tuple[Decimal, Optional[BusinessAmbassadorRepayHistory]]:
        zero = Decimal()
        if repay_amount <= zero:
            return zero, None

        bus = BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id == loan_row.bus_amb_id,
        ).first()
        if bus and now() < bus.repay_datetime:
            # 未到商务大使设定的还款时间
            return zero, None

        exist_his = BusinessAmbassadorRepayHistory.query.filter(
            BusinessAmbassadorRepayHistory.date == repay_date,
            BusinessAmbassadorRepayHistory.loan_id == loan_row.id,
        ).first()
        if exist_his:
            return zero, exist_his  # 每天每条预付金，只还一次

        cur_repay_amount = min(repay_amount, loan_row.unflat_amount)
        if cur_repay_amount <= 0:
            return zero, None

        repay_row = BusinessAmbassadorRepayHistory(
            date=repay_date,
            loan_id=loan_row.id,
            bus_amb_id=loan_row.bus_amb_id,
            bus_user_id=loan_row.bus_user_id,
            asset=loan_row.asset,
            amount=cur_repay_amount,
        )
        db.session.add(repay_row)
        db.session.commit()

        client = ServerClient()
        try:
            client.batch_add_user_balance(
                [
                    dict(
                        user_id=repay_row.bus_amb_id,  # 扣商务大使
                        asset=repay_row.asset,
                        business=BalanceBusiness.BUS_AMB_LOAN_REPAY,
                        business_id=repay_row.id,
                        amount=str(-repay_row.amount),
                        detail={'remark': f'bus_amb repay {repay_row.id}'},
                        account_id=SPOT_ACCOUNT_ID,
                    ),
                    dict(
                        user_id=repay_row.bus_user_id,  # 加商务
                        asset=repay_row.asset,
                        business=BalanceBusiness.BUS_AMB_LOAN_REPAY,
                        business_id=repay_row.id,
                        amount=str(repay_row.amount),
                        detail={'remark': f'bus_amb repay {repay_row.id}'},
                        account_id=SPOT_ACCOUNT_ID,
                    ),
                ]
            )
            repay_row.status = BusinessAmbassadorRepayHistory.Status.FINISHED
            loan_row.repay_amount += cur_repay_amount
            if loan_row.unflat_amount <= 0:
                loan_row.status = BusinessAmbassadorLoan.Status.FINISHED
            db.session.commit()
            current_app.logger.warning(
                f"BusinessAmbassadorRepayHelper_repay_one {repay_row.id} {repay_row.loan_id} {repay_row.bus_amb_id} "
                f"{repay_row.bus_user_id} {repay_row.asset} {repay_row.amount} success")
        except Exception as e:
            if isinstance(e, ServerClient.BadResponse) and e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                repay_row.status = BusinessAmbassadorRepayHistory.Status.FAILED
                db.session.commit()
            current_app.logger.error(
                f"BusinessAmbassadorRepayHelper_repay_one {repay_row.id} {repay_row.loan_id} {repay_row.bus_amb_id} "
                f"{repay_row.bus_user_id} {repay_row.asset} {repay_row.amount} error {e!r}")
        return cur_repay_amount, repay_row


class BusAmbAppraisalHelper:
    """ 商务大使自动淘汰相关逻辑 """

    # 商务拓展的商务大使：生效后第150天，做淘汰预警判断和邮件触达；生效后第180天，做淘汰判断和邮件触达
    BUSINESS_EXPANSION_PENDING_DELETION_DAYS = 60
    BUSINESS_EXPANSION_DELETION_DAYS = 90

    NORMAL_AMB_RECALL_PENDING_DELETION_DAYS = 60
    NORMAL_AMB_RECALL_DELETION_DAYS = 90

    @classmethod
    def execute(cls):
        now_ = now()
        min_exp_eff_at = now_ - timedelta(days=cls.BUSINESS_EXPANSION_PENDING_DELETION_DAYS)
        min_rec_eff_at = now_ - timedelta(days=cls.NORMAL_AMB_RECALL_PENDING_DELETION_DAYS)
        del_exp_eff_at = now_ - timedelta(days=cls.BUSINESS_EXPANSION_DELETION_DAYS)
        del_rec_eff_at = now_ - timedelta(days=cls.NORMAL_AMB_RECALL_DELETION_DAYS)

        amb_rows = BusRelationUserQuerier.get_all_valid_bus_ambassadors()
        amb_rows = [i for i in amb_rows if i.type == BusinessAmbassador.Type.NORMAL]  # Broker商务大使不进行考核
        check_amb_rows = []
        for amb in amb_rows:
            if amb.check_status == BusinessAmbassador.CheckStatus.CHECK_PASSED:
                continue
            if amb.source == BusinessAmbassador.Source.BUSINESS_EXPANSION and amb.effected_at <= min_exp_eff_at:
                check_amb_rows.append(amb)
            elif amb.source == BusinessAmbassador.Source.NORMAL_AMB_RECALL and amb.effected_at <= min_rec_eff_at:
                check_amb_rows.append(amb)

        check_amb_map: dict[int, BusinessAmbassador] = {i.user_id: i for i in check_amb_rows}
        amb_share_for_me_map = defaultdict(list)
        q_share_ids = set()
        for ch_amb_ids in batch_iter(check_amb_map, 1000):
            share_for_me_rows = BusinessAmbassadorShareUser.query.filter(
                BusinessAmbassadorShareUser.target_user_id.in_(ch_amb_ids),
                BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
            ).all()
            for s in share_for_me_rows:
                amb_share_for_me_map[s.target_user_id].append(s)
                q_share_ids.add(s.bus_amb_id)

        q_ids = set(check_amb_map) | q_share_ids
        amb_ref_count_map = defaultdict(int)
        share_ref_count_map = defaultdict(int)
        for ch_q_ids in batch_iter(q_ids, 1000):
            ch_refs = ReferralHistory.query.filter(
                ReferralHistory.referrer_id.in_(ch_q_ids),
            ).with_entities(
                ReferralHistory.effected_at,
                ReferralHistory.referrer_id,
                ReferralHistory.referree_id,
            ).all()
            for ref in ch_refs:
                if amb := check_amb_map.get(ref.referrer_id):
                    if ref.effected_at > amb.effected_at:
                        amb_ref_count_map[amb.user_id] += 1
                shares = amb_share_for_me_map[ref.referrer_id]
                for share in shares:
                    share: BusinessAmbassadorShareUser
                    if ref.effected_at > share.effected_at:
                        share_ref_count_map[(amb.user_id, share.bus_amb_id)] += 1

        for amb in check_amb_rows:
            self_ref_cnt = amb_ref_count_map[amb.user_id]
            share_ref_cnt = sum([share_ref_count_map[(amb.user_id, s.bus_amb_id)] for s in amb_share_for_me_map[amb.user_id]])
            if self_ref_cnt or share_ref_cnt:
                amb.check_status = BusinessAmbassador.CheckStatus.CHECK_PASSED
                db.session.add(amb)
                db.session.commit()
                continue
            # 同时满足：
            # 从最近一次成为商务大使日起到第N天，邀请新用户为0；
            # 从最近一次成为商务大使日起到第N天，其作为商务代理邀请的大使，从绑定关系至今邀请的新用户为0；
            if amb.source == BusinessAmbassador.Source.BUSINESS_EXPANSION:
                notice_at = min_exp_eff_at
                del_at = del_exp_eff_at
            else:
                notice_at = min_rec_eff_at
                del_at = del_rec_eff_at
            if del_at <= amb.effected_at <= notice_at:
                old_check_status = amb.check_status
                amb.check_status = BusinessAmbassador.CheckStatus.CHECK_NOT_PASSED
                db.session.add(amb)
                db.session.commit()
                if old_check_status != BusinessAmbassador.CheckStatus.CHECK_NOT_PASSED:
                    cls.notice_pre_delete(amb)
            elif amb.effected_at < del_at:
                old_data = {"status": amb.status.name}
                amb.check_status = BusinessAmbassador.CheckStatus.CHECK_NOT_PASSED
                amb.status = BusinessAmbassador.Status.DELETED
                amb.delete_at = now_
                amb.delete_type = BusinessAmbassador.DeleteType.SYSTEM
                db.session.add(amb)
                new_data = {"status": amb.status.name}
                audit_row = BusinessAmbassadorAudit(
                    user_id=amb.user_id,
                    bus_user_id=amb.bus_user_id,
                    amb_type=amb.type,
                    type=BusinessAmbassadorAudit.Type.EDIT_AMB_STATUS,
                    status=BusinessAmbassadorAudit.Status.PASSED,
                    creator=0,
                    remark="自动淘汰",
                    first_comment="自动淘汰",
                    comment="自动淘汰",
                    old_data=json.dumps(old_data, cls=JsonEncoder),
                    new_data=json.dumps(new_data, cls=JsonEncoder),
                    file_keys=[],
                )
                db.session.add(audit_row)
                db.session.commit()
                cls.notice_delete(amb)

    @classmethod
    def notice_pre_delete(cls, amb: BusinessAmbassador):
        if amb.source == BusinessAmbassador.Source.BUSINESS_EXPANSION:
            end_at = amb.effected_at.date() + timedelta(days=cls.BUSINESS_EXPANSION_DELETION_DAYS)
        else:
            end_at = amb.effected_at.date() + timedelta(days=cls.NORMAL_AMB_RECALL_DELETION_DAYS)
        params = {
            "end_at": end_at.strftime("%Y-%m-%d"),
            "detail_url": cls.get_refer_page_url(),
        }
        send_bus_amb_notice_email.delay(amb.user_id, "bus_ambassador_delete_pre_notice", params)

    @classmethod
    def notice_delete(cls, amb: BusinessAmbassador):
        params = {
            "detail_url": cls.get_refer_page_url(),
        }
        send_bus_amb_notice_email.delay(amb.user_id, "bus_ambassador_delete_notice", params)

    @classmethod
    def get_refer_page_url(cls) -> str:
        return config['SITE_URL'] + f"/activity/refer"
