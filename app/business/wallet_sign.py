# -*- coding: utf-8 -*-

from collections import defaultdict
from decimal import Decimal
import time

from flask import current_app

from app.models import Withdrawal, WithdrawalSignature, AssetChainSignConfig, db
from app.exceptions import WalletSignFailed
from app.business import WalletManagerClient, LockKeys, CacheLock, PriceManager
from app.caches.wallets import GasAddressAmountCache, TmpAddressAmountCache, GasAddressThresholdCache
from app.utils import JsonRPC2Client
from app.config import config


class WalletSigner:

    @classmethod
    def tss_sign_pre(cls, chain: str, sender: str, message: str, commitment: str, group: int) -> dict:
        start = time.time()
        c = WalletManagerClient(group, chain)
        try:
            return c.tss_sign_pre(dict(chain=chain, sender=sender, message=message, commitment=commitment))
        except JsonRPC2Client.BadResponse as e:
            raise WalletSignFailed(message=f"wallet manager pre-sign error: http {e.code}")
        except JsonRPC2Client.RPCBadResponse as e:
            raise WalletSignFailed(message=f"wallet manager pre-sign error: {e.message}")
        finally:
            current_app.logger.warning(f"tss_sign_pre {chain} {time.time() - start}")

    @classmethod
    def sign_tx(cls, wwid: list[str], chain: str, sender: str, recipients: list[dict],
                raw_data: dict, signer: str, message: str, sign_func: str, group: int):
        start = time.time()
        with CacheLock(LockKeys.wallet_sign(chain), wait=30, ttl=300):
            db.session.rollback()
            # 打给gas地址、冷钱包情况下，没有提现单据
            sum_of_ws = {}
            if wwid:
                ws = cls.check_withdrawals_signed(chain, wwid)
                sum_of_ws = cls.sum_withdrawals(ws)
            # wallet_manager会检查recipients和raw_data(待签名数据)是否匹配
            # 因此在backend只检查recipients是安全的
            sum_of_rs = cls.sum_recipients(chain, recipients)
            cls.check_recipients_with_withdrawals(chain, sum_of_ws, sum_of_rs)

        c = WalletManagerClient(group, chain)
        try:
            sig = c.sign_tx(sign_func, dict(
                chain=chain,
                sender=sender,
                recipients=recipients,
                raw_data=raw_data,
                signer=signer,
                message=message
            ))
        except JsonRPC2Client.BadResponse as e:
            raise WalletSignFailed(message=f"wallet manager sign error: http {e.code}")
        except JsonRPC2Client.RPCBadResponse as e:
            raise WalletSignFailed(message=f"wallet manager sign error: {e.message}")

        if wwid:
            with CacheLock(LockKeys.wallet_sign(chain), wait=30, ttl=300):
                db.session.rollback()
                row = WithdrawalSignature.query.filter(WithdrawalSignature.withdrawal_id.in_(wwid),
                                                       WithdrawalSignature.status == WithdrawalSignature.Status.FINISHED).first()
                if row:
                    raise WalletSignFailed(message=f"duplicate sign")
                cls.set_withdrawals_signed(wwid)

        current_app.logger.warning(f"tss_sign {chain} {time.time() - start}")
        return {'signature': sig}

    @classmethod
    def check_withdrawals_signed(cls, chain, wwid):
        ss = WithdrawalSignature.query.filter(WithdrawalSignature.withdrawal_id.in_(wwid)).all()
        if any(x.status == WithdrawalSignature.Status.FINISHED for x in ss):
            raise WalletSignFailed(message="wwid already signed")
        ws = Withdrawal.query.filter(Withdrawal.id.in_(wwid)).all()
        if len(ws) != len(wwid):
            raise WalletSignFailed(message="wwid not found")
        if any(w.status != Withdrawal.Status.PROCESSING or w.chain != chain for w in ws):
            for w in ws:
                current_app.logger.error(f"sign_tx {chain} invalid status: {w.id}-{w.chain}-{w.status}")
            raise WalletSignFailed(message="invalid wwid status")
        return ws

    @classmethod
    def set_withdrawals_signed(cls, wwid):
        for wid in wwid:
            db.session.add(WithdrawalSignature(
                withdrawal_id=wid,
                status=WithdrawalSignature.Status.FINISHED,
            ))
        db.session.commit()

    @classmethod
    def sum_withdrawals(cls, ws):
        sum_of_ws = defaultdict(Decimal)
        for w in ws:
            key = (w.address, w.asset)
            sum_of_ws[key] += w.amount
        return sum_of_ws

    @classmethod
    def sum_recipients(cls, chain, recipients):
        identities = {x["amount"]["identity"] for x in recipients}
        rows = AssetChainSignConfig.query.filter(
            AssetChainSignConfig.chain == chain, AssetChainSignConfig.identity.in_(identities)
        ).all()
        id_to_asset = {x.identity: x for x in rows}
        sum_of_recipients = defaultdict(Decimal)
        for recipient in recipients:
            amount = recipient["amount"]
            if not (asset_info := id_to_asset.get(amount["identity"])):
                raise WalletSignFailed(message="identity not found")
            key = (recipient["address"], asset_info.asset)
            value = Decimal(amount["value"]) / (10**asset_info.precision)
            sum_of_recipients[key] += value
        return sum_of_recipients

    @classmethod
    def check_recipients_with_withdrawals(cls, chain, ws, rs):
        # 检查提现金额跟交易输出金额相等
        for key, value in ws.items():
            other = rs.get(key)
            _, asset = key
            if not other or not cls.amount_equal(chain, asset, value, other):
                raise WalletSignFailed(message="recipient amount not euqal")
        # 检查剩余的交易输出，是否是白名单地址或gas地址
        whitelist = cls.get_whitelist_addresses(chain)
        gas_address = cls.get_gas_address(chain)
        tmp_address = cls.get_temp_address(chain)
        to_gas = defaultdict(Decimal)
        to_tmp = defaultdict(Decimal)
        for key, amount in rs.items():
            if key in ws:
                continue
            address, asset = key
            if address in whitelist:
                continue
            if (isinstance(gas_address, str) and address == gas_address) \
                or (isinstance(gas_address, list) and address in gas_address):
                to_gas[asset] += amount
            elif address in tmp_address:
                to_tmp[asset] += amount
            else:
                raise WalletSignFailed(message="unrecognized recipient address")
        # 对gas地址转账限额，以防gas地址被盗的情况
        if to_gas:
            cls.check_gas_address_amount(chain, to_gas)
        # 对转账到临时地址的，检查限额
        if to_tmp:
            cls.check_tmp_address_amount(chain, to_tmp)

    @classmethod
    def amount_equal(cls, chain, asset, amount, other):
        if amount == other:
            return True
        # 对于链上精度小于数据库精度的情况，金额相差不能超过链上最小精度
        c = AssetChainSignConfig.query.filter(AssetChainSignConfig.chain == chain,
                                              AssetChainSignConfig.asset == asset).first()
        return abs(amount - other) <= Decimal(10) ** -c.precision

    @classmethod
    def get_whitelist_addresses(cls, chain):
        addrs = config["WALLET_WHITELIST_ADDRESS"]
        return addrs.get(chain, [])

    @classmethod
    def get_gas_address(cls, chain) -> str | list[str] | None:
        addrs = config["WALLET_GAS_ADDRESS"]
        return addrs.get(chain)

    @classmethod
    def get_temp_address(cls, chain) -> list:
        conf = config["WALLET_TEMP_ADDRESS"]
        data = conf.get(chain)
        if not data:
            return []
        # 临时地址带有过期时间，这样可以防止忘记删除
        result = []
        n = int(time.time())
        for addr, ttl in data['address']:
            if ttl < n:
                continue
            result.append(addr)
        return result

    @classmethod
    def check_gas_address_amount(cls, chain, assets):
        prices = PriceManager.assets_to_usd()
        amount = sum([prices.get(asset, 0) * v for asset, v in assets.items()])
        cache = GasAddressAmountCache()
        sent = cache.get_amount(chain)
        if sent + amount > GasAddressThresholdCache(chain).get_limit():
            raise WalletSignFailed(message="too many amount to gas address")
        cache.add_amount(chain, amount)

    @classmethod
    def check_tmp_address_amount(cls, chain, assets):
        limit = config["WALLET_TEMP_ADDRESS"][chain]['threshold']
        prices = PriceManager.assets_to_usd()
        amount = sum([prices.get(asset, 0) * v for asset, v in assets.items()])
        cache = TmpAddressAmountCache()
        sent = cache.get_amount(chain)
        if sent + amount > limit:
            raise WalletSignFailed(message="too many amount to tmp address")
        cache.add_amount(chain, amount)
