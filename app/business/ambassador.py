import json
import statistics
import time
from collections import defaultdict
from datetime import timedelta, date
from decimal import Decimal
from functools import cached_property
from typing import List, Dict, Set
from dateutil.relativedelta import relativedelta
from flask import current_app
from flask_babel import gettext, force_locale
from pyroaring import BitMap
from sqlalchemy import func

from app.business.market_maker import MarketMakerHelper
from app.business.referral import ReferralRepository
from app.business.summary import get_period_trade_amount_mapping, get_period_first_trade_user_set, check_data_ready
from app.business.user_tag import TagReader
from app.config import config
from app.business import TradeLogDB, ExchangeLogDB, TradeHistoryDB, PerpetualHistoryDB, TradeSummaryDB, \
    PerpetualSummaryDB, LockKeys, CacheLock
from app.business.email import send_potential_ambassador_email, send_meet_requirement_package_msg, \
    send_not_meet_requirement_package_msg, send_package_settlement_not_ambassador_msg, send_ambassador_package_invite
from app.business.utils import yield_query_records_by_time_range
from app.common import Language, P2pBusinessType, PerpetualMarketType, MessageWebLink, MessageContent, MessageTitle
from app.models import User, AmbassadorAgentHistory, AmbassadorStatistics, MonthlyAmbassadorReport, datetime, \
    Ambassador, ReferralAssetHistory, AmbassadorAgent, PotentialAmbassador, ReferralHistory, UserTradeSummary, \
    OnlyWithdrawalWhitelistUser, SubAccount, db, UserBusinessRecord, BusinessAmbassador, ReferralAssetDetail, \
    AssetPrice, UserExchangeSummary, UserTradeFeeSummary, Market, PerpetualMarket, \
    ReferralAssetSummary, AmbassadorDashboardDailyReferDetail, AmbassadorDashboardDailyRefer, \
    AmbassadorReferralHistoryExtra, AmbassadorDashboard, \
    AmbassadorDashboardMonthlyReferDetail, AmbassadorDashboardMonthlyRefer, \
    AmbassadorDashboardDailyTradeMarket, AmbassadorDashboardTradeMarket, Message, UserAmbassadorPackage, GiftHistory, \
    PackageSettlementHistory, AppraisalHistory, AmbassadorPackageBatch, AmbassadorType, PopupWindowContent, \
    PopupWindow, AppJumpList
from app.models.exchange import AssetExchangeOrder
from app.models.user_tag import UserTag, UserTagGroup
from app.models.wallet import Deposit
from app.models.p2p import P2pOrder
from app.utils import datetime_to_time, url_join, batch_iter, quantize_amount, amount_to_str
from app.utils.date_ import date_to_datetime, timestamp_to_date, timestamp_to_datetime, next_month, today, this_month, \
    last_month, now, yesterday
from app.utils.parser import JsonEncoder
from app.utils.logs import log_func_consume


AGENT_EXPORT_HEADERS = (
    {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
    {"field": "email", Language.ZH_HANS_CN: "邮箱"},
    {"field": "real_referral_rate", Language.ZH_HANS_CN: "返佣比例"},
    {"field": "total_ambassador_count", Language.ZH_HANS_CN: "推荐大使数"},
    {"field": "total_deal_ambassador_count", Language.ZH_HANS_CN: "有交易大使数"},
    {"field": "total_referral_count", Language.ZH_HANS_CN: "大使推荐用户数"},
    {"field": "total_deal_referral_count", Language.ZH_HANS_CN: "大使推荐交易用户数"},
    {"field": "overall_deal_amount", Language.ZH_HANS_CN: "推荐大使被邀请人累计交易量(USD)"},
    {"field": "overall_referral_amount", Language.ZH_HANS_CN: "累计返佣(USDT)"},
    {"field": "status", Language.ZH_HANS_CN: "生效状态"},
    {"field": "is_appraisal", Language.ZH_HANS_CN: "是否考核"},
    {"field": "effected_at", Language.ZH_HANS_CN: "成为代理时间"},
    {"field": "remark", Language.ZH_HANS_CN: "备注"},
)


def get_agents_statistic_data(records: List[AmbassadorAgent], export=False):
    agent_user_ids = [item.user_id for item in records]

    query_user_ids = set(agent_user_ids)
    users = User.query.filter(User.id.in_(query_user_ids)).with_entities(User.id, User.email).all()
    user_email_map = {u.id: u.email for u in users}

    history_query = AmbassadorAgentHistory.query

    # 计算累计大使数量
    ambassador_count_query = history_query.filter(
        AmbassadorAgentHistory.user_id.in_(agent_user_ids),
        AmbassadorAgentHistory.status == AmbassadorAgentHistory.Status.VALID
    ).group_by(
        AmbassadorAgentHistory.user_id,
    ).with_entities(
        AmbassadorAgentHistory.user_id,
        func.count(AmbassadorAgentHistory.ambassador_id.distinct()).label("ambassador_count")
    )
    ambassador_count_map = {x.user_id: x.ambassador_count for x in ambassador_count_query}

    # 计算推荐大使的相关数据
    ambassador_agent_query = history_query.filter(
        AmbassadorAgentHistory.user_id.in_(agent_user_ids),
        AmbassadorAgentHistory.status == AmbassadorAgentHistory.Status.VALID
    )
    ambassador_agent_relations = ambassador_agent_query.all()
    ambassador_ids = []
    agent_id_amb_ids_dict = defaultdict(list)

    agent_amount_map = dict()
    for item in ambassador_agent_relations:
        agent_id_amb_ids_dict[item.user_id].append(item.ambassador_id)
        ambassador_ids.append(item.ambassador_id)

    # 大使统计信息
    amb_statistics_map = {
        i.user_id: i for i in
        AmbassadorStatistics.query.filter(AmbassadorStatistics.user_id.in_(ambassador_ids)).all()
    }
    ambassador_monthly_report = MonthlyAmbassadorReport.query.filter(
        MonthlyAmbassadorReport.user_id.in_(ambassador_ids)
    ).all()

    # 计算大使代理的累计返佣
    agent_monthly_report = ReferralAssetHistory.query.filter(
        ReferralAssetHistory.user_id.in_(agent_user_ids),
        ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR_AGENT
    ).with_entities(func.sum(ReferralAssetHistory.amount).label('amount'),
                    ReferralAssetHistory.user_id,
                    ).group_by(
        ReferralAssetHistory.user_id
    ).all()
    agent_referral_amount_map = {item.user_id: item.amount for item in agent_monthly_report}

    overall_deal_amount_map = defaultdict(Decimal)
    for q in ambassador_monthly_report:
        overall_deal_amount_map[q.user_id] += q.deal_amount  # 总交易量
    for k, v in agent_id_amb_ids_dict.items():
        if k not in agent_amount_map:
            agent_amount_map[k] = defaultdict(Decimal)
        for ambassador_id in v:
            agent_amount_map[k]['overall_deal_amount'] += overall_deal_amount_map.get(ambassador_id, Decimal())

    res = []
    for item in records:
        if item.user_id not in agent_amount_map:
            agent_amount_map[item.user_id] = defaultdict(Decimal)

        total_deal_ambassador_count = 0  # 邀请有交易用户的大使数
        total_referral_count = total_deal_referral_count = 0
        agent_refer_amb_ids = agent_id_amb_ids_dict[item.user_id]
        for amb_id_ in agent_refer_amb_ids:
            if amb_stat := amb_statistics_map.get(amb_id_):
                total_referral_count += amb_stat.cur_valid_refer_count  # 大使当前有效的推荐用户数
                total_deal_referral_count += amb_stat.refer_deal_count  # 大使当前有效的推荐交易用户数
                if amb_stat.refer_deal_count > 0:
                    total_deal_ambassador_count += 1

        self_rate = Decimal("1")

        res.append(
            dict(
                user_id=item.user_id,
                email=user_email_map[item.user_id],
                real_referral_rate=item.referral_rate * self_rate,  # 剩下的给自己的实际返佣比例
                total_ambassador_count=ambassador_count_map.get(item.user_id, 0),
                total_deal_ambassador_count=total_deal_ambassador_count,
                total_referral_count=total_referral_count,
                total_deal_referral_count=total_deal_referral_count,
                overall_deal_amount=agent_amount_map[item.user_id]["overall_deal_amount"],
                overall_referral_amount=agent_referral_amount_map.get(item.user_id, Decimal()),
                status=item.status.name,
                remark=item.remark,
                is_appraisal=item.is_appraisal,
                effected_at=item.effected_at.strftime('%Y-%m-%d %H:%M:%S') if export else item.effected_at,
            )
        )
    return res


class PotentialSource:
    source_type: PotentialAmbassador.Source
    query_config: tuple  # 天数, 邀请人数， 天数，邀请用户产生的交易额  None 表示不执行
    email_template: str

    def __init__(self):
        self.today_date = today()

    def get_origin_source_user(self) -> Set[int]:
        raise NotImplementedError

    def get_ambassador_url(self) -> str:
        return ""

    def get_trade_user_set(self):
        max_date = UserBusinessRecord.query.with_entities(
            func.max(UserBusinessRecord.report_at)
        ).scalar() or self.today_date
        query = UserBusinessRecord.query.filter(
            UserBusinessRecord.report_at == max_date,
            UserBusinessRecord.business != UserBusinessRecord.Business.DEPOSIT
        ).with_entities(
            UserBusinessRecord.history_user_bit_map
        ).all()
        trade_user_set = set()
        for bitmap, in query:
            trade_user_set |= set(BitMap.deserialize(bitmap))
        return trade_user_set

    def get_user_refer_data(
            self,
            user_ids: Set[int] = None,
            start_days: int = None,
            min_referr_count: int = 0
    ) -> Dict[int, List[int]]:
        query = ReferralHistory.query.filter(
            ReferralHistory.status == ReferralHistory.Status.VALID
        )
        if user_ids:
            query = query.filter(
                ReferralHistory.referrer_id.in_(user_ids)
            )
        if start_days:
            query = query.filter(
                ReferralHistory.created_at >= self.today_date - timedelta(days=start_days)
            )
        refer_record = query.with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id
        ).all()
        refer_mapper = defaultdict(list)
        trade_user_set = self.get_trade_user_set()
        for referrer_id, referree_id in refer_record:
            if referree_id not in trade_user_set:
                continue
            refer_mapper[referrer_id].append(referree_id)
        return {
            user_id: refer_list for user_id, refer_list in refer_mapper.items() if len(refer_list) >= min_referr_count
        }

    def get_user_trade_set(
            self,
            refer_ee_re_mapper: Dict[int, int],
            start_days: int = None,
            min_trade_amount: Decimal = Decimal(),
    ):
        query = UserTradeSummary.query.filter(
            UserTradeSummary.user_id.in_(refer_ee_re_mapper.values())
        )
        if start_days:
            query = query.filter(
                UserTradeSummary.report_date >= self.today_date - timedelta(days=start_days)
            )
        query = query.group_by(
            UserTradeSummary.user_id
        ).with_entities(
            UserTradeSummary.user_id,
            func.sum(UserTradeSummary.trade_amount)
        ).all()
        refer_ee_trade_mapper = defaultdict(Decimal)
        for user_id, amount in query:
            er = refer_ee_re_mapper[user_id]
            refer_ee_trade_mapper[er] += amount
        return {user_id for user_id, amount in refer_ee_trade_mapper.items() if amount > min_trade_amount}

    def _get_exclude_user_ids(self) -> Set[int]:
        ambassador_user_set = self._valid_ambassadors
        only_white_user = OnlyWithdrawalWhitelistUser.query.filter(
            OnlyWithdrawalWhitelistUser.status == OnlyWithdrawalWhitelistUser.Status.VALID
        ).with_entities(
            OnlyWithdrawalWhitelistUser.user_id
        ).all()
        white_user_set = {user_id for user_id, in only_white_user}
        potential_query = PotentialAmbassador.query.with_entities(PotentialAmbassador.user_id).all()
        potential_user_set = {user_id for user_id, in potential_query}
        return ambassador_user_set | white_user_set | potential_user_set

    @cached_property
    def _valid_ambassadors(self) -> set[int]:
        model = Ambassador
        rows = model.query.filter(
            model.status == model.Status.VALID
        ).with_entities(
            model.user_id
        ).all()
        return {user_id for user_id, in rows}

    def filter_potential_users(self):
        """根据来源的 config 过滤用户"""
        if not self.query_config:
            return
        cycle_days, refer_count, cycle_trade_days, trade_amount = self.query_config
        source_user_set = self.get_origin_source_user()
        refer_mapper = self.get_user_refer_data(source_user_set, cycle_days, refer_count or 0)
        target_user_set = set(refer_mapper.keys())
        trade_refer_mapper = refer_mapper
        if trade_refer_mapper and cycle_trade_days != cycle_days:
            trade_refer_mapper = self.get_user_refer_data(source_user_set, cycle_trade_days)

        if trade_amount:
            refer_ee_re_mapper = {ee: re for re, ee_list in trade_refer_mapper.items() for ee in ee_list}
            target_user_set = self.get_user_trade_set(refer_ee_re_mapper, trade_amount)

        return target_user_set

    def save_potential_users(self):
        """过滤大使和仅提现用户"""
        potential_user_set = self.filter_potential_users()
        if not potential_user_set:
            return
        exclude_user_set = self._get_exclude_user_ids()
        save_user_set = potential_user_set - exclude_user_set
        potentials = []
        for user_id in save_user_set:
            potentials.append(PotentialAmbassador(
                user_id=user_id,
                source=self.source_type
            ))
        db.session.add_all(potentials)
        db.session.commit()

        if self.email_template:
            self._send_potential_ambassador_email(save_user_set)

    def _send_potential_ambassador_email(self, user_set):
        for user in User.query.filter(User.id.in_(user_set)).with_entities(User.id, User.name, User.email).all():
            send_potential_ambassador_email.delay(self.email_template, user.id, user.name, user.email, self.get_ambassador_url())


class InvalidAmbassadorSource(PotentialSource):
    source_type = PotentialAmbassador.Source.INVALID_AMBASSADOR
    query_config = (None, 1, None, 0)
    email_template = "potential_invalid_ambassador"

    def get_origin_source_user(self):
        invalid_ambassador = Ambassador.query.filter(
            Ambassador.status == Ambassador.Status.DELETED,
            Ambassador.type != Ambassador.Type.BUSINESS
        ).with_entities(
            Ambassador.user_id
        ).all()
        return {_id for _id, in invalid_ambassador}

    def get_send_push_user_set(self):
        return self.get_origin_source_user() - self._get_exclude_user_ids()

    def get_ambassador_url(self) -> str:
        return url_join(config['SITE_URL'], '/s/4G17')


class RejectedAmbassadorSource(PotentialSource):
    source_type = PotentialAmbassador.Source.REJECTED_AMBASSADOR
    query_config = None  # 线上不执行
    email_template = "potential_ambassador"

    def get_origin_source_user(self) -> Set[int]:
        return set()


class PotentialUserSource(PotentialSource):
    source_type = PotentialAmbassador.Source.POTENTIAL_USER
    # query_config = (30, 3, None, 0)
    email_template = "potential_ambassador"

    def filter_potential_users(self):
        """根据新规则进行打分得出潜在用户"""
        refer_tag_data = TagReader.get_single_tag_data(UserTag.TOTAL_REFER_USER_COUNT)
        refer_gt0_items = [{'user_id': k, 'value': v} for k, v in refer_tag_data.items() if v > 0]
        precondition_user_ids = {d['user_id'] for d in refer_gt0_items}
        precondition_user_ids -= self._valid_ambassadors
        refer_gt0_items = [d for d in refer_gt0_items if d['user_id'] not in self._valid_ambassadors]
        if len(precondition_user_ids) < 2:
            return set()
        tag_data = TagReader.get_all_tag_data(
            tags=[
                UserTag.INACTIVE_DAYS,
                UserTag.REFER_USER_TOTAL_FEE_USD,
            ],
        )
        fee_items = list()
        inactive_items = list()
        for tag, mapping in tag_data.items():
            for user_id, value in mapping.items():
                if user_id not in precondition_user_ids:
                    continue
                if tag is UserTag.REFER_USER_TOTAL_FEE_USD:
                    fee_items.append({'user_id': user_id, 'value': value})
                elif tag is UserTag.INACTIVE_DAYS:
                    inactive_items.append({'user_id': user_id, 'value': value})
                else:
                    pass
        fill_na_fee_ids = precondition_user_ids - set([d['user_id'] for d in fee_items])
        fill_na_fee_items = [{'user_id': user_id, 'value': Decimal()} for user_id in fill_na_fee_ids]
        fee_items.extend(fill_na_fee_items)
        fill_na_inactive_ids = precondition_user_ids - set([d['user_id'] for d in inactive_items])
        fill_na_inactive_items = [{'user_id': user_id, 'value': 0.01} for user_id in fill_na_inactive_ids]
        inactive_items.extend(fill_na_inactive_items)
        active_items = [{'user_id': d['user_id'], 'value': 1.0 / (d['value'] or 0.01)} for d in inactive_items]

        refer_gt0_items.sort(key=lambda d: d['value'])
        refer_scores = self._score(items=refer_gt0_items)
        fee_items.sort(key=lambda d: d['value'])
        fee_scores = self._score(items=fee_items)
        active_items.sort(key=lambda d: d['value'])
        active_scores = self._score(items=active_items)
        scores = defaultdict(dict)
        for user_id, value in refer_scores.items():
            scores[user_id].update(refer=value)
        for user_id, value in fee_scores.items():
            scores[user_id].update(fee=value)
        for user_id, value in active_scores.items():
            scores[user_id].update(active=value)
        total_scores = {}
        for user_id, score in scores.items():
            score: dict
            total_score = 0.1 * score.get('active', 0) + 0.7 * score.get('fee', 0) + 0.2 * score.get('refer', 0)
            total_scores[user_id] = total_score
        ret = list()
        inactive_mapping = {d['user_id']: d['value'] for d in inactive_items}
        for user_id, score in total_scores.items():
            score: int
            inactive_days = inactive_mapping[user_id]
            if not self._standard_satisfied(score, inactive_days):
                continue
            ret.append(user_id)
        return set(ret)

    @classmethod
    def _score(cls, items):
        sections = [d['value'] for d in items]
        q1, q2, q3 = statistics.quantiles(sections, n=4, method='inclusive')
        ret = {}
        for item in items:
            if item['value'] <= q1:
                score = 0
            elif q1 < item['value'] <= q2:
                score = 1
            elif q2 < item['value'] <= q3:
                score = 2
            else:
                score = 3
            ret[item['user_id']] = score
        return ret

    @classmethod
    def _standard_satisfied(cls, score, inactive_days, threshold=90):
        satisfied = inactive_days < threshold
        satisfied = 1 < score <= 3 and satisfied
        return satisfied

    def get_origin_source_user(self) -> Set[int]:
        """高潜力用户，直接通过邀请人数来筛选"""
        return set()

    def get_ambassador_url(self) -> str:
        return url_join(config['SITE_URL'], '/s/4GTX')


class QualityUserSource(PotentialSource):
    MIN_AMOUNT = 10 ** 5

    source_type = PotentialAmbassador.Source.QUALITY_USER
    query_config = (None, 1, None, 0)
    email_template = "potential_ambassador"

    def _get_sub_user_mapper(self):
        sub_query = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        return {sub.user_id: sub.main_user_id for sub in sub_query}

    def _get_cet_user_set(self, sub_user_mapper: Dict[int, int]) -> Set[int]:
        ts = datetime_to_time(datetime(self.today_date.year, self.today_date.month, self.today_date.day))
        slice_table = TradeLogDB.slice_balance_table(ts)
        if not slice_table:
            return set()
        records = slice_table.select(
            "user_id", "SUM(balance) as sum_balance",
            where="`asset` = 'CET'",
            group_by="user_id"
        )
        main_cet_user_mapper = defaultdict(Decimal)
        for user_id, balance in records:
            main_user_id = sub_user_mapper.get(user_id, user_id)
            main_cet_user_mapper[main_user_id] += balance

        main_user_set = {user_id for user_id, b in main_cet_user_mapper.items() if b >= self.MIN_AMOUNT}
        return main_user_set

    def _get_balance_user_set(self, sub_user_mapper: Dict[int, int]) -> Set[int]:
        ts = datetime_to_time(datetime(self.today_date.year, self.today_date.month, self.today_date.day))
        if not ExchangeLogDB.check_table_is_finished_by_count(ts, "user_balance_sum"):
            return set()
        table_name = ExchangeLogDB.user_balance_sum_table(ts)
        records = table_name.select(
            "user_id", "SUM(balance_usd)",
            group_by="user_id",
            having=f"SUM(balance_usd) >= {self.MIN_AMOUNT}"
        )
        big_user_set = {user_id for user_id, _ in records}
        sub_main_users = set(sub_user_mapper.keys()) | set(sub_user_mapper.values())
        user_id_str = ','.join(map(str, sub_main_users))
        sub_records = table_name.select(
            "user_id", "SUM(balance_usd) as sum_balance_usd",
            where=f"user_id in ({user_id_str})",
            group_by="user_id",
            force_index="user_account_uniq"
        )
        main_user_balance_mapper = defaultdict(Decimal)
        for user_id, balance in sub_records:
            main_user_id = sub_user_mapper.get(user_id, user_id)
            main_user_balance_mapper[main_user_id] += balance

        main_user_set = {user_id for user_id, b in main_user_balance_mapper.items() if b >= self.MIN_AMOUNT}
        return big_user_set | main_user_set

    def get_origin_source_user(self) -> Set[int]:
        sub_user_mapper = self._get_sub_user_mapper()
        cet_user_set = self._get_cet_user_set(sub_user_mapper)
        balance_user_set = self._get_balance_user_set(sub_user_mapper)
        return cet_user_set | balance_user_set

    def get_ambassador_url(self) -> str:
        return url_join(config['SITE_URL'], '/s/4GTV')


class AmbDashboardHelper:
    """ 大使数据看板-相关统计逻辑 """

    CHUNK_SIZE = 5000
    FLUSH_SIZE = 5000

    def __init__(self, report_date: date):
        self.report_date = report_date
        self.date_prices = {}

    @classmethod
    def check_data_ready(cls, report_date: date) -> bool:
        if not ReferralAssetDetail.query.filter(
            ReferralAssetDetail.date == report_date,
        ).with_entities(ReferralAssetDetail.id).first():
            current_app.logger.warning(f"AmbDashboardHelper {report_date} ReferralAssetDetail not_ready")
            return False
        if not UserTradeSummary.check_data_ready(report_date):  # check spot and per
            current_app.logger.warning(f"AmbDashboardHelper {report_date} UserTradeSummary not_ready")
            return False
        if not UserExchangeSummary.query.filter(
            UserExchangeSummary.report_date == report_date,
        ).with_entities(UserExchangeSummary.id).first():
            current_app.logger.warning(f"AmbDashboardHelper {report_date} UserExchangeSummary not_ready")
            return False
        return True

    @classmethod
    def merge_and_sum_dict(cls, *dict_list) -> dict[str, Decimal]:
        result = {}
        for d in dict_list:
            for k, v in d.items():
                if k in result:
                    result[k] += Decimal(v)
                else:
                    result[k] = Decimal(v)
        return result

    @classmethod
    def get_all_spot_market_asset_info(cls) -> dict[str, list[str, str]]:
        records = Market.query.with_entities(
            Market.name,
            Market.quote_asset,
            Market.base_asset,
        ).all()
        result = {}
        for name, quote_asset, base_asset in records:
            result[name] = [base_asset, quote_asset]
        return result

    @classmethod
    def get_all_per_market_asset_info(cls) -> dict[str, list[str, str, PerpetualMarketType]]:
        records = PerpetualMarket.query.with_entities(
            PerpetualMarket.name,
            PerpetualMarket.quote_asset,
            PerpetualMarket.base_asset,
            PerpetualMarket.market_type,
        ).all()
        result = {}
        for name, quote_asset, base_asset, market_type in records:
            result[name] = [base_asset, quote_asset, market_type]
        return result

    def log(self, *args):
        current_app.logger.warning(f"AmbDashboardHelper {self.report_date} {args}")

    def get_asset_price(self, date_: date, asset_: str) -> Decimal:
        # 只会查最近N天的价格
        if date_ not in self.date_prices:
            price_map = AssetPrice.get_close_price_map(date_)
            self.date_prices[date_] = price_map
        return self.date_prices[date_].get(asset_, Decimal())

    @cached_property
    def all_amb_ids(self) -> set[int]:
        # 失效大使也继续更新数据看板
        nor_amb_rows = Ambassador.query.with_entities(Ambassador.user_id).all()
        amb_ids = {i.user_id for i in nor_amb_rows}
        bus_amb_rows = BusinessAmbassador.query.with_entities(BusinessAmbassador.user_id).all()
        amb_ids.update({i.user_id for i in bus_amb_rows})
        return amb_ids

    @cached_property
    def refer_his_rows(self) -> list[ReferralHistory]:
        result = []
        for ch_amb_ids in batch_iter(self.all_amb_ids, 1000):
            ch_rows = ReferralHistory.query.filter(
                ReferralHistory.referrer_id.in_(ch_amb_ids),
            ).with_entities(
                ReferralHistory.created_at,
                ReferralHistory.referrer_id,
                ReferralHistory.referree_id,
                ReferralHistory.referral_id,
            ).all()
            result.extend([i for i in ch_rows if i.created_at.date() <= self.report_date])
        return result

    @classmethod
    def get_last_report_date(cls) -> date:
        return AmbassadorDashboard.query.order_by(AmbassadorDashboard.last_update_at).first().last_update_at.date()

    @cached_property
    def report_date_close_price_map(self):
        return AssetPrice.get_close_price_map(self.report_date)

    @cached_property
    @log_func_consume
    def all_sub_main_info(self) -> tuple[dict[int, dict], dict[int, list[int]]]:
        sub_main_map = {}
        main_subs_map = {}
        sub_rows = SubAccount.query.with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id,
        ).all()
        for r in sub_rows:
            sub_main_map[r.user_id] = r.main_user_id
            main_subs_map.setdefault(r.main_user_id, []).append(r.user_id)
        return sub_main_map, main_subs_map

    @classmethod
    @log_func_consume
    def query_has_trade_user_ids(cls) -> set[int]:
        spot_row = UserBusinessRecord.query.filter(
            UserBusinessRecord.business == UserBusinessRecord.Business.SPOT_TRADE,
        ).order_by(UserBusinessRecord.report_at.desc()).first()
        per_row = UserBusinessRecord.query.filter(
            UserBusinessRecord.business == UserBusinessRecord.Business.PERPETUAL_TRADE,
        ).order_by(UserBusinessRecord.report_at.desc()).first()
        trade_user_ids = set()
        if spot_row and spot_row.history_user_bit_map:
            trade_user_ids |= set(BitMap.deserialize(spot_row.history_user_bit_map))
        if per_row and per_row.history_user_bit_map:
            trade_user_ids |= set(BitMap.deserialize(per_row.history_user_bit_map))
        return trade_user_ids

    @classmethod
    @log_func_consume
    def get_amb_all_ref_amounts(cls, all_amb_ids: set[int]) -> tuple[dict, dict]:
        amb_spot_ref_asset_amount_map = defaultdict(lambda: defaultdict(Decimal))  # 大使累积现货返佣数
        amb_per_ref_asset_amount_map = defaultdict(lambda: defaultdict(Decimal))  # 大使累积合约返佣数
        for ch_amb_ids in batch_iter(all_amb_ids, 1000):
            ch_amb_ref_rows = ReferralAssetHistory.query.filter(
                ReferralAssetHistory.user_id.in_(ch_amb_ids),
            ).with_entities(
                ReferralAssetHistory.user_id,
                ReferralAssetHistory.type,
                ReferralAssetHistory.asset,
                ReferralAssetHistory.amount,
                ReferralAssetHistory.spot_amount,
                ReferralAssetHistory.perpetual_amount,
            ).all()
            for r in ch_amb_ref_rows:
                if r.type not in [ReferralAssetHistory.Type.REFERRAL, ReferralAssetHistory.Type.AMBASSADOR]:
                    continue
                if not r.spot_amount and not r.perpetual_amount:
                    amb_spot_ref_asset_amount_map[r.user_id][r.asset] += r.amount
                else:
                    if r.spot_amount:
                        amb_spot_ref_asset_amount_map[r.user_id][r.asset] += r.spot_amount
                    if r.perpetual_amount:
                        amb_per_ref_asset_amount_map[r.user_id][r.asset] += r.perpetual_amount
        return amb_spot_ref_asset_amount_map, amb_per_ref_asset_amount_map

    @classmethod
    @log_func_consume
    def get_amb_date_ref_amounts(cls, all_amb_ids: set[int], start_date: date, end_date: date) -> tuple[dict, dict]:
        amb_spot_ref_asset_amount_map = defaultdict(lambda: defaultdict(Decimal))  # 大使现货返佣数
        amb_per_ref_asset_amount_map = defaultdict(lambda: defaultdict(Decimal))  # 大使合约返佣数
        amb_ref_rows = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date >= start_date,
            ReferralAssetHistory.date <= end_date,
        ).with_entities(
            ReferralAssetHistory.user_id,
            ReferralAssetHistory.type,
            ReferralAssetHistory.asset,
            ReferralAssetHistory.amount,
            ReferralAssetHistory.spot_amount,
            ReferralAssetHistory.perpetual_amount,
        ).all()
        for r in amb_ref_rows:
            if r.type not in [ReferralAssetHistory.Type.REFERRAL, ReferralAssetHistory.Type.AMBASSADOR]:
                continue
            if r.user_id not in all_amb_ids:
                continue
            if r.spot_amount:
                amb_spot_ref_asset_amount_map[r.user_id][r.asset] += r.spot_amount
            if r.perpetual_amount:
                amb_per_ref_asset_amount_map[r.user_id][r.asset] += r.perpetual_amount
        return amb_spot_ref_asset_amount_map, amb_per_ref_asset_amount_map

    @classmethod
    @log_func_consume
    def get_users_all_trades_usds(cls, user_ids: set[int]) -> tuple[dict[int, Decimal], dict[int, Decimal]]:
        """ 查询指定用户的全部交易额 """
        user_spot_usd_map = defaultdict(Decimal)
        user_per_usd_map = defaultdict(Decimal)
        for ch_ids in batch_iter(user_ids, cls.CHUNK_SIZE):
            trade_rows = UserTradeSummary.query.filter(
                UserTradeSummary.user_id.in_(ch_ids),
            ).group_by(
                UserTradeSummary.user_id,
                UserTradeSummary.system,
            ).with_entities(
                UserTradeSummary.user_id,
                UserTradeSummary.system,
                func.sum(UserTradeSummary.trade_amount).label('total_amount'),
            ).all()
            for r in trade_rows:
                if r.system == UserTradeSummary.System.SPOT:
                    user_spot_usd_map[r.user_id] += r.total_amount
                else:
                    user_per_usd_map[r.user_id] += r.total_amount

            exc_rows = UserExchangeSummary.query.filter(
                UserExchangeSummary.user_id.in_(ch_ids),
            ).group_by(
                UserExchangeSummary.user_id,
            ).with_entities(
                UserExchangeSummary.user_id,
                func.sum(UserExchangeSummary.trade_amount).label('total_amount'),
            ).all()
            for r in exc_rows:
                user_spot_usd_map[r.user_id] += r.total_amount
        return user_spot_usd_map, user_per_usd_map

    @log_func_consume
    def get_users_all_and_first_deposit_info(self, user_ids: set[int]) -> tuple[dict, dict]:
        """ 查询指定用户的全部充值金额、首次充值信息 """
        user_dep_usd_map = defaultdict(Decimal)
        user_first_dep_info_map = {}
        last_price_map = self.report_date_close_price_map
        for ch_ids in batch_iter(user_ids, self.CHUNK_SIZE):
            dep_rows = Deposit.query.filter(
                Deposit.user_id.in_(ch_ids),
            ).with_entities(
                Deposit.user_id,
                Deposit.created_at,
                Deposit.asset,
                Deposit.amount,
                Deposit.type,
                Deposit.status,
            ).all()
            for row in dep_rows:
                if row.type == Deposit.Type.LOCAL:
                    continue
                if row.status not in [Deposit.Status.PROCESSING, Deposit.Status.CONFIRMING, Deposit.Status.FINISHED]:
                    continue

                _dt = row.created_at.date()
                _price = last_price_map.get(row.asset) or self.get_asset_price(_dt, row.asset)
                _usd = quantize_amount(_price * row.amount, 8)
                user_dep_usd_map[row.user_id] += _usd
                if row.user_id not in user_first_dep_info_map:
                    user_first_dep_info_map[row.user_id] = [row.created_at, _usd]
                else:
                    _exist_ct = user_first_dep_info_map[row.user_id][0]
                    if _exist_ct > row.created_at:
                        user_first_dep_info_map[row.user_id] = [row.created_at, _usd]

            p2p_rows = P2pOrder.query.filter(
                P2pOrder.customer_id.in_(ch_ids),
            ).with_entities(
                P2pOrder.customer_id,
                P2pOrder.created_at,
                P2pOrder.base,
                P2pOrder.base_amount,
                P2pOrder.side,
                P2pOrder.status,
            ).all()
            for row in p2p_rows:
                if row.side != P2pBusinessType.BUY:
                    continue
                if row.status != P2pOrder.Status.FINISHED:
                    continue

                _dt = row.created_at.date()
                _price = last_price_map.get(row.base) or self.get_asset_price(_dt, row.base)
                _usd = quantize_amount(_price * row.base_amount, 8)

                r_uid = row.customer_id
                user_dep_usd_map[r_uid] += _usd
                if r_uid not in user_first_dep_info_map:
                    user_first_dep_info_map[r_uid] = [row.created_at, _usd]
                else:
                    _exist_ct = user_first_dep_info_map[r_uid][0]
                    if _exist_ct > row.created_at:
                        user_first_dep_info_map[r_uid] = [row.created_at, _usd]

        return user_dep_usd_map, user_first_dep_info_map

    @log_func_consume
    def get_ee_all_refer_info(self, user_ids: set[int], all_amb_ids: set[int]) -> tuple[dict, dict, dict, dict]:
        """ 查询指定被邀请用户的全部返佣信息 """
        ee_ref_up_spot_amount_map = defaultdict(dict)  # 给邀请人的现货返佣数
        ee_ref_up_per_amount_map = defaultdict(dict)  # 给邀请人的合约返佣数
        ee_ref_up_spot_fee_usd_map = defaultdict(Decimal)  # 用于给邀请人的现货返佣的现货手续费
        ee_ref_up_per_fee_usd_map = defaultdict(Decimal)  # 用于给邀请人的合约返佣的合约手续费

        def _is_old_detail(_detail: ReferralAssetDetail) -> False:
            # 老数据：有返佣数但是没有手续费数目
            if not _detail.spot_fee_usd and not _detail.perpetual_fee_usd and (_detail.spot_amount > 0 or _detail.perpetual_amount > 0):
                return True
            return False

        for ch_ids in batch_iter(user_ids, self.CHUNK_SIZE):
            detail_rows: list[ReferralAssetDetail] = ReferralAssetDetail.query.filter(
                ReferralAssetDetail.referree_id.in_(ch_ids),
            ).with_entities(
                ReferralAssetDetail.date,
                ReferralAssetDetail.user_id,
                ReferralAssetDetail.referree_id,
                ReferralAssetDetail.asset,
                ReferralAssetDetail.spot_amount,
                ReferralAssetDetail.perpetual_amount,
                ReferralAssetDetail.spot_fee_usd,
                ReferralAssetDetail.perpetual_fee_usd,
            ).all()
            zero_fee_dts = {i.date for i in detail_rows if _is_old_detail(i)}
            user_date_fee_map = {}
            if zero_fee_dts:
                max_zero_fee_dt = max(zero_fee_dts)
                min_zero_fee_dt = min(zero_fee_dts)
                fee_summary_rows: list[UserTradeFeeSummary] = UserTradeFeeSummary.query.filter(
                    UserTradeFeeSummary.report_date >= min_zero_fee_dt,
                    UserTradeFeeSummary.report_date <= max_zero_fee_dt,
                ).with_entities(
                    UserTradeFeeSummary.report_date,
                    UserTradeFeeSummary.user_id,
                    UserTradeFeeSummary.system,
                    UserTradeFeeSummary.trade_fee_amount,
                ).all()
                for fs_row in fee_summary_rows:
                    key_ = (fs_row.user_id, fs_row.report_date)
                    if key_ not in user_date_fee_map:
                        user_date_fee_map[key_] = [0, 0]
                    if fs_row.system == UserTradeFeeSummary.System.SPOT:
                        user_date_fee_map[key_][0] += fs_row.trade_fee_amount
                    else:
                        user_date_fee_map[key_][1] += fs_row.trade_fee_amount

            for asset_d in detail_rows:
                if not (asset_d.user_id in all_amb_ids and asset_d.referree_id != asset_d.user_id):
                    continue

                ee_id = asset_d.referree_id
                if _is_old_detail(asset_d):
                    key_ = (ee_id, asset_d.date)
                    dt_fees_ = user_date_fee_map.get(key_, [0, 0])
                    if asset_d.spot_amount:
                        ee_ref_up_spot_fee_usd_map[ee_id] += dt_fees_[0]
                    if asset_d.perpetual_amount:
                        ee_ref_up_per_fee_usd_map[ee_id] += dt_fees_[1]
                else:
                    ee_ref_up_spot_fee_usd_map[ee_id] += asset_d.spot_fee_usd
                    ee_ref_up_per_fee_usd_map[ee_id] += asset_d.perpetual_fee_usd

            smy_rows: list[ReferralAssetSummary] = ReferralAssetSummary.query.filter(
                ReferralAssetSummary.referree_id.in_(ch_ids),
            ).order_by(ReferralAssetSummary.id.asc()).with_entities(
                ReferralAssetSummary.user_id,
                ReferralAssetSummary.referree_id,
                ReferralAssetSummary.asset,
                ReferralAssetSummary.spot_amount,
                ReferralAssetSummary.perpetual_amount,
            ).all()
            for smy in smy_rows:
                if not (smy.user_id in all_amb_ids and smy.referree_id != smy.user_id):
                    continue
                ee_id = smy.referree_id
                if smy.spot_amount:
                    spot_amount_map = ee_ref_up_spot_amount_map[ee_id]
                    spot_amount_map[smy.asset] = spot_amount_map.get(smy.asset, 0) + smy.spot_amount
                if smy.perpetual_amount:
                    per_amount_map = ee_ref_up_per_amount_map[ee_id]
                    per_amount_map[smy.asset] = per_amount_map.get(smy.asset, 0) + smy.perpetual_amount

        return ee_ref_up_spot_amount_map, ee_ref_up_spot_fee_usd_map, ee_ref_up_per_amount_map, ee_ref_up_per_fee_usd_map

    @classmethod
    @log_func_consume
    def query_user_trade_usds(cls, ee_ids: set[int], report_date: date) -> tuple[dict[int, Decimal], dict[int, Decimal]]:
        """ 查指定日期和用户 的现货、合约交易额 """
        spot_usd_map = {}
        per_usd_map = {}
        for ch_ee_ids in batch_iter(ee_ids, cls.CHUNK_SIZE):
            ch_trade_rows = UserTradeSummary.query.filter(
                UserTradeSummary.user_id.in_(ch_ee_ids),
                UserTradeSummary.report_date == report_date,
            ).with_entities(
                UserTradeSummary.user_id,
                UserTradeSummary.system,
                UserTradeSummary.trade_amount,
            ).all()
            for r in ch_trade_rows:
                if r.system == UserTradeSummary.System.SPOT:
                    spot_usd_map[r.user_id] = r.trade_amount
                elif r.system == UserTradeSummary.System.PERPETUAL:
                    per_usd_map[r.user_id] = r.trade_amount

            ch_ex_rows = UserExchangeSummary.query.filter(
                UserExchangeSummary.user_id.in_(ch_ee_ids),
                UserExchangeSummary.report_date == report_date,
            ).with_entities(
                UserExchangeSummary.user_id,
                UserExchangeSummary.trade_amount,
            ).all()
            for r in ch_ex_rows:
                spot_usd_map[r.user_id] = spot_usd_map.get(r.user_id, 0) + r.trade_amount
        return spot_usd_map, per_usd_map

    @classmethod
    @log_func_consume
    def query_date_trade_usds(cls, report_date: date) -> tuple[dict[int, Decimal], dict[int, Decimal]]:
        """ 查指定日期的 全部用户的现货、合约交易额 """
        spot_usd_map = {}
        per_usd_map = {}
        ch_trade_rows = UserTradeSummary.query.filter(
            UserTradeSummary.report_date == report_date,
        ).with_entities(
            UserTradeSummary.user_id,
            UserTradeSummary.system,
            UserTradeSummary.trade_amount,
        ).all()
        for r in ch_trade_rows:
            if r.system == UserTradeSummary.System.SPOT:
                spot_usd_map[r.user_id] = r.trade_amount
            elif r.system == UserTradeSummary.System.PERPETUAL:
                per_usd_map[r.user_id] = r.trade_amount

        ch_ex_rows = UserExchangeSummary.query.filter(
            UserExchangeSummary.report_date == report_date,
        ).with_entities(
            UserExchangeSummary.user_id,
            UserExchangeSummary.trade_amount,
        ).all()
        for r in ch_ex_rows:
            spot_usd_map[r.user_id] = spot_usd_map.get(r.user_id, 0) + r.trade_amount
        return spot_usd_map, per_usd_map

    @classmethod
    @log_func_consume
    def query_date_deposit_usds(cls, report_date: date, price_map: dict[str, Decimal]) -> dict[int, Decimal]:
        """ 查指定日期 全部用户的入金金额（入金行为包括链上充值和P2P买入，不包括站内转账） """
        deposit_usd_map = defaultdict(Decimal)

        # 这里只会增查最近N天的数据
        start_time = date_to_datetime(report_date)
        end_time = start_time + timedelta(days=1)
        for row in yield_query_records_by_time_range(
            table=Deposit,
            start_time=start_time,
            end_time=end_time,
            select_fields=(
                Deposit.user_id,
                Deposit.type,
                Deposit.asset,
                Deposit.amount,
                Deposit.status,
            )
        ):
            if row.type == Deposit.Type.LOCAL:
                continue
            if row.status not in [Deposit.Status.PROCESSING, Deposit.Status.CONFIRMING, Deposit.Status.FINISHED]:
                continue
            usd = quantize_amount(price_map.get(row.asset, 0) * row.amount, 8)
            deposit_usd_map[row.user_id] += usd

        for row in yield_query_records_by_time_range(
            table=P2pOrder,
            start_time=start_time,
            end_time=end_time,
            select_fields=(
                P2pOrder.customer_id,
                P2pOrder.base,
                P2pOrder.base_amount,
                P2pOrder.side,
                P2pOrder.status,
            )
        ):
            if row.side != P2pBusinessType.BUY:
                continue
            if row.status != P2pOrder.Status.FINISHED:
                continue
            usd = quantize_amount(price_map.get(row.base, 0) * row.base_amount, 8)
            deposit_usd_map[row.customer_id] += usd

        return deposit_usd_map

    @log_func_consume
    def generate_daily_refer_data(self):
        """ 生成每日返佣数据：每日的返佣明细 和 返佣记录 """
        report_date = self.report_date

        all_amb_ids = self.all_amb_ids
        ref_his_rows = self.refer_his_rows
        er_ee_ids_map = defaultdict(set)
        er_inc_ee_ids_map = defaultdict(set)  # 当天新增被邀请人
        for r in ref_his_rows:
            er_ee_ids_map[r.referrer_id].add(r.referree_id)
            if r.created_at.date() == report_date:
                er_inc_ee_ids_map[r.referrer_id].add(r.referree_id)

        ref_asset_detail_rows, ref_ee_ids = self.get_ee_ref_asset_details(report_date)

        ee_spot_usd_map, ee_per_usd_map = self.query_user_trade_usds(ref_ee_ids, report_date)
        price_map = self.report_date_close_price_map
        deposit_usd_map = self.query_date_deposit_usds(report_date, price_map)

        # 每日返佣明细（大使+被邀请人 维度）
        exist_detail_rows: list[AmbassadorDashboardDailyReferDetail] = AmbassadorDashboardDailyReferDetail.query.filter(
            AmbassadorDashboardDailyReferDetail.report_date == report_date,
        ).all()
        ee_exist_detail_row_map = {i.referree_id: i for i in exist_detail_rows}
        amb_dd_details_map = defaultdict(list)
        for asset_d in ref_asset_detail_rows:
            asset_d: ReferralAssetDetail
            ee_id = asset_d.referree_id
            if ee_id in ee_exist_detail_row_map:
                ee_detail = ee_exist_detail_row_map[ee_id]
            else:
                ee_detail = AmbassadorDashboardDailyReferDetail(
                    report_date=report_date,
                    user_id=asset_d.user_id,
                    referree_id=ee_id,
                    referral_id=asset_d.referral_id,
                )
                ee_exist_detail_row_map[ee_id] = ee_detail
            ee_detail.spot_trade_usd = ee_spot_usd_map.get(ee_id, 0)
            ee_detail.perpetual_trade_usd = ee_per_usd_map.get(ee_id, 0)
            ee_detail.spot_refer_fee_usd = asset_d.spot_fee_usd
            ee_detail.perpetual_refer_fee_usd = asset_d.perpetual_fee_usd
            ee_detail.refer_asset = asset_d.asset
            ee_detail.spot_refer_amount = asset_d.spot_amount
            ee_detail.perpetual_refer_amount = asset_d.perpetual_amount
            db.session.add(ee_detail)
            amb_dd_details_map[asset_d.user_id].append(ee_detail)

        # 每日返佣记录（大使维度）
        exist_refer_rows: list[AmbassadorDashboardDailyRefer] = AmbassadorDashboardDailyRefer.query.filter(
            AmbassadorDashboardDailyRefer.report_date == report_date,
        ).all()
        amb_exist_refer_row_map = {i.user_id: i for i in exist_refer_rows}
        for amb_id in all_amb_ids:
            ee_details = amb_dd_details_map[amb_id]
            spot_trade_usd = perpetual_trade_usd = 0
            spot_refer_fee_usd = perpetual_refer_fee_usd = 0
            spot_refer_asset_amounts = defaultdict(Decimal)
            per_refer_asset_amounts = defaultdict(Decimal)
            for _ee_detail in ee_details:
                _ee_detail: AmbassadorDashboardDailyReferDetail
                spot_trade_usd += _ee_detail.spot_trade_usd
                perpetual_trade_usd += _ee_detail.perpetual_trade_usd
                spot_refer_fee_usd += _ee_detail.spot_refer_fee_usd
                perpetual_refer_fee_usd += _ee_detail.perpetual_refer_fee_usd
                spot_refer_asset_amounts[_ee_detail.refer_asset] += _ee_detail.spot_refer_amount
                per_refer_asset_amounts[_ee_detail.refer_asset] += _ee_detail.perpetual_refer_amount

            if amb_id in amb_exist_refer_row_map:
                refer_row = amb_exist_refer_row_map[amb_id]
            else:
                refer_row = AmbassadorDashboardDailyRefer(
                    report_date=report_date,
                    user_id=amb_id,
                )

            inc_refer_ee_ids = {i for i in er_inc_ee_ids_map[amb_id]}
            refer_row.inc_refer_user_count = len(inc_refer_ee_ids)
            deposit_ee_ids = {i for i in er_ee_ids_map[amb_id] if deposit_usd_map.get(i, 0) > 0}
            refer_row.deposit_user_count = len(deposit_ee_ids)
            refer_row.trade_user_count = len(ee_details)
            refer_row.spot_trade_usd = spot_trade_usd
            refer_row.perpetual_trade_usd = perpetual_trade_usd
            refer_row.spot_refer_fee_usd = spot_refer_fee_usd
            refer_row.perpetual_refer_fee_usd = perpetual_refer_fee_usd
            refer_row.spot_refer_amounts = json.dumps(spot_refer_asset_amounts, cls=JsonEncoder)
            refer_row.perpetual_refer_amounts = json.dumps(per_refer_asset_amounts, cls=JsonEncoder)

            spot_refer_usd = Decimal()
            for asset_, amount_ in spot_refer_asset_amounts.items():
                spot_refer_usd += price_map.get(asset_, 0) * amount_
            spot_refer_usd = quantize_amount(spot_refer_usd, 8)
            refer_row.spot_refer_usd = spot_refer_usd

            per_refer_usd = Decimal()
            for asset_, amount_ in per_refer_asset_amounts.items():
                per_refer_usd += price_map.get(asset_, 0) * amount_
            per_refer_usd = quantize_amount(per_refer_usd, 8)
            refer_row.perpetual_refer_usd = per_refer_usd

            db.session.add(refer_row)
        db.session.commit()

    def get_ee_ref_asset_details(self, report_date: date) -> tuple[list[ReferralAssetDetail], set[int]]:
        all_amb_ids = self.all_amb_ids
        asset_detail_rows: list[ReferralAssetDetail] = ReferralAssetDetail.query.filter(
            ReferralAssetDetail.date == report_date,
        ).all()
        match_ee_ids = set()  # 邀请码有分成，避免重复
        match_asset_detail_rows = []
        for detail_r in asset_detail_rows:
            if detail_r.referree_id in match_ee_ids:
                continue
            if detail_r.user_id in all_amb_ids and detail_r.referree_id != detail_r.user_id:
                match_asset_detail_rows.append(detail_r)
                match_ee_ids.add(detail_r.referree_id)
        return match_asset_detail_rows, match_ee_ids

    @log_func_consume
    def generate_or_update_monthly_refer_data(self):
        """ 生成或更新每月返佣数据：每月的返佣明细 和 返佣记录 """
        daily_start_date = self.report_date.replace(day=1)
        daily_end_date = next_month(daily_start_date.year, daily_start_date.month)
        month_report_date = daily_start_date

        # （被邀请人）每月的返佣明细
        day_detail_model = AmbassadorDashboardDailyReferDetail
        day_detail_rows: list[day_detail_model] = day_detail_model.query.filter(
            day_detail_model.report_date >= daily_start_date,
            day_detail_model.report_date < daily_end_date,
        ).all()
        ee_month_detail_data_map = defaultdict(
            lambda: {
                'spot_trade_usd': Decimal(),
                'perpetual_trade_usd': Decimal(),
                'spot_refer_fee_usd': Decimal(),
                'perpetual_refer_fee_usd': Decimal(),
                'spot_refer_amounts': dict(),
                'perpetual_refer_amounts': dict(),
                'user_id': None,
                'referral_id': None,
            }
        )
        amb_month_trade_ee_ids_map = defaultdict(set)  # 月报-交易人数去重
        sum_fields = ['spot_trade_usd', 'perpetual_trade_usd', 'spot_refer_fee_usd', 'perpetual_refer_fee_usd']
        for d_detail_row in day_detail_rows:
            ee_month_detail = ee_month_detail_data_map[d_detail_row.referree_id]
            for field in sum_fields:
                ee_month_detail[field] += getattr(d_detail_row, field)

            if d_detail_row.spot_refer_amount > 0:
                spot_inc = {d_detail_row.refer_asset: d_detail_row.spot_refer_amount}
                ee_month_detail['spot_refer_amounts'] = self.merge_and_sum_dict(ee_month_detail['spot_refer_amounts'], spot_inc)
            if d_detail_row.perpetual_refer_amount > 0:
                per_inc = {d_detail_row.refer_asset: d_detail_row.perpetual_refer_amount}
                ee_month_detail['perpetual_refer_amounts'] = self.merge_and_sum_dict(ee_month_detail['perpetual_refer_amounts'], per_inc)
            ee_month_detail['user_id'] = d_detail_row.user_id
            ee_month_detail['referral_id'] = d_detail_row.referral_id
            amb_month_trade_ee_ids_map[d_detail_row.user_id].add(d_detail_row.referree_id)

        month_detail_model = AmbassadorDashboardMonthlyReferDetail
        exist_month_detail_rows: list[month_detail_model] = month_detail_model.query.filter(
            month_detail_model.report_date == month_report_date,
        ).all()
        ee_exist_month_detail_row_map = {i.referree_id: i for i in exist_month_detail_rows}
        easy_fields = ['spot_trade_usd', 'perpetual_trade_usd', 'spot_refer_fee_usd', 'perpetual_refer_fee_usd']
        dict_fields = ['spot_refer_amounts', 'perpetual_refer_amounts']
        for _idx, (ee_id, detail_month_data) in enumerate(ee_month_detail_data_map.items()):
            if ee_id in ee_exist_month_detail_row_map:
                ee_month_detail = ee_exist_month_detail_row_map[ee_id]
            else:
                ee_month_detail = AmbassadorDashboardMonthlyReferDetail(
                    report_date=month_report_date,
                    user_id=detail_month_data['user_id'],  # 大使id
                    referree_id=ee_id,  # 被邀请人id
                    referral_id=detail_month_data['referral_id'],
                )
            for field in easy_fields:
                setattr(ee_month_detail, field, detail_month_data[field])
            for field in dict_fields:
                setattr(ee_month_detail, field, json.dumps(detail_month_data[field], cls=JsonEncoder))

            db.session.add(ee_month_detail)
            if _idx and _idx % self.FLUSH_SIZE:
                db.session.flush()

        #
        # （大使）每月的返佣记录
        day_model = AmbassadorDashboardDailyRefer
        day_rows: list[day_model] = day_model.query.filter(
            day_model.report_date >= daily_start_date,
            day_model.report_date < daily_end_date,
        ).all()
        amb_month_data_map = defaultdict(
            lambda: {
                'trade_user_count': 0,
                'spot_trade_usd': Decimal(),
                'perpetual_trade_usd': Decimal(),
                'spot_refer_fee_usd': Decimal(),
                'perpetual_refer_fee_usd': Decimal(),
                'spot_refer_amounts': dict(),
                'perpetual_refer_amounts': dict(),
            }
        )
        sum_fields = ['spot_trade_usd', 'perpetual_trade_usd', 'spot_refer_fee_usd', 'perpetual_refer_fee_usd']
        dict_fields = ['spot_refer_amounts', 'perpetual_refer_amounts']
        for dd_row in day_rows:
            month_data = amb_month_data_map[dd_row.user_id]
            for field in sum_fields:
                month_data[field] += getattr(dd_row, field)
            for field in dict_fields:
                d_val = getattr(dd_row, field)
                d_val_dict = json.loads(d_val) if d_val else {}
                month_data[field] = self.merge_and_sum_dict(month_data[field], d_val_dict)
            month_data['trade_user_count'] = len(amb_month_trade_ee_ids_map[dd_row.user_id])

        month_model = AmbassadorDashboardMonthlyRefer
        exist_month_rows: list[month_model] = month_model.query.filter(
            month_model.report_date == month_report_date,
        ).all()
        amb_exist_month_row_map = {i.user_id: i for i in exist_month_rows}
        for _idx, (amb_id, amb_month_data) in enumerate(amb_month_data_map.items()):
            if amb_id in amb_exist_month_row_map:
                amb_month_row = amb_exist_month_row_map[amb_id]
            else:
                amb_month_row = AmbassadorDashboardMonthlyRefer(
                    report_date=month_report_date,
                    user_id=amb_id,
                )
            amb_month_row.trade_user_count = amb_month_data['trade_user_count']
            for field in sum_fields:
                setattr(amb_month_row, field, amb_month_data[field])
            for field in dict_fields:
                setattr(amb_month_row, field, json.dumps(amb_month_data[field], cls=JsonEncoder))

            db.session.add(amb_month_row)
            if _idx and _idx % self.FLUSH_SIZE:
                db.session.flush()
        db.session.commit()

    @log_func_consume
    def generate_new_ee_extra(self):
        """ 普通用户成为大使 or 大使新增被邀请人 时写入对应的extra """
        all_amb_ids = self.all_amb_ids
        ref_his_rows = self.refer_his_rows
        ee_ids = {i.referree_id for i in ref_his_rows}

        exist_extra_ee_id_rows = AmbassadorReferralHistoryExtra.query.with_entities(
            AmbassadorReferralHistoryExtra.referree_id,
        ).all()
        exist_extra_ee_ids = {i.referree_id for i in exist_extra_ee_id_rows}
        miss_extra_ee_ids = ee_ids - exist_extra_ee_ids
        current_app.logger.warning(f"AmbDashboardHelper generate_new_ee_extra miss_extra_ee_num {len(miss_extra_ee_ids)}")
        if not miss_extra_ee_ids:
            return

        yes_day = today() - timedelta(days=1)
        last_row = AmbassadorReferralHistoryExtra.query.order_by(
            AmbassadorReferralHistoryExtra.last_update_at.desc()
        ).first()
        if last_row and last_row.last_update_at.date() != yes_day:
            current_app.logger.warning(f"AmbDashboardHelper generate_new_ee_extra skip last_update_at {last_row.last_update_at.date()}")
            return

        last_update_at = last_row.last_update_at.date()
        ee_spot_trade_map, ee_per_trade_map = self.get_users_all_trades_usds(miss_extra_ee_ids)
        user_dep_usd_map, user_first_dep_info_map = self.get_users_all_and_first_deposit_info(miss_extra_ee_ids)
        (ee_ref_up_spot_amount_map, ee_ref_up_spot_fee_usd_map,
         ee_ref_up_per_amount_map, ee_ref_up_per_fee_usd_map) = self.get_ee_all_refer_info(miss_extra_ee_ids, all_amb_ids)

        for ee_id in miss_extra_ee_ids:
            extra_row = AmbassadorReferralHistoryExtra(referree_id=ee_id)

            extra_row.deposit_usd = user_dep_usd_map.get(ee_id, 0)
            if ee_id in user_first_dep_info_map:
                first_dep_info = user_first_dep_info_map[ee_id]
                extra_row.first_deposit_at = first_dep_info[0]
                extra_row.first_deposit_usd = first_dep_info[1]

            extra_row.spot_trade_usd = ee_spot_trade_map[ee_id]
            extra_row.perpetual_trade_usd = ee_per_trade_map[ee_id]

            extra_row.spot_refer_fee_usd = ee_ref_up_spot_fee_usd_map[ee_id]
            extra_row.perpetual_refer_fee_usd = ee_ref_up_per_fee_usd_map[ee_id]
            extra_row.spot_refer_amounts = json.dumps(ee_ref_up_spot_amount_map[ee_id], cls=JsonEncoder)
            extra_row.perpetual_refer_amounts = json.dumps(ee_ref_up_per_amount_map[ee_id], cls=JsonEncoder)

            extra_row.last_update_at = last_update_at
            db.session.add(extra_row)
        db.session.commit()

    @log_func_consume
    def update_ee_extra_inc_data(self):
        """ 更新大使的邀请extra记录-增量字段 """
        last_row = AmbassadorReferralHistoryExtra.query.order_by(
            AmbassadorReferralHistoryExtra.last_update_at.desc()
        ).first()
        end_dt = self.report_date
        start_dt = last_row.last_update_at.date() + timedelta(days=1) if last_row else end_dt
        loop_times = (end_dt - start_dt).days + 1
        current_app.logger.warning(f"AmbDashboardHelper update_ee_referral_his_extra last_update_at: {last_row.last_update_at} "
                                   f"start_dt: {start_dt} end_dt: {end_dt} loop_times: {loop_times}")
        while start_dt <= end_dt:
            current_app.logger.warning(f"AmbDashboardHelper update_ee_extra_inc_data_by_report_date {start_dt} start")
            self._update_ee_extra_inc_data_by_report_date(start_dt)
            current_app.logger.warning(f"AmbDashboardHelper update_ee_extra_inc_data_by_report_date {start_dt} end")
            start_dt += timedelta(days=1)

    def _update_ee_extra_inc_data_by_report_date(self, report_date):
        ref_his_rows = self.refer_his_rows
        ee_ids = {i.referree_id for i in ref_his_rows}

        if report_date == self.report_date:
            price_map = self.report_date_close_price_map
        else:
            price_map = AssetPrice.get_close_price_map(report_date)
        spot_usd_map, per_usd_map = self.query_date_trade_usds(report_date)
        deposit_usd_map = self.query_date_deposit_usds(report_date, price_map)

        exist_extra_ee_id_rows = AmbassadorReferralHistoryExtra.query.with_entities(
            AmbassadorReferralHistoryExtra.referree_id,
        ).all()
        exist_extra_ee_ids = {i.referree_id for i in exist_extra_ee_id_rows}
        miss_extra_ee_ids = ee_ids - exist_extra_ee_ids

        ref_asset_detail_rows, ref_ee_ids = self.get_ee_ref_asset_details(report_date)
        ee_ref_asset_detail_map = {i.referree_id: i for i in ref_asset_detail_rows}

        has_data_user_ids = set(spot_usd_map) | set(per_usd_map) | set(deposit_usd_map) | set(ref_ee_ids)
        to_update_ee_ids = has_data_user_ids & ee_ids
        to_update_ee_ids = to_update_ee_ids | miss_extra_ee_ids

        ee_exist_extra_map = {}
        for ch_ee_ids in batch_iter(to_update_ee_ids, self.CHUNK_SIZE):
            ch_extra_rows = AmbassadorReferralHistoryExtra.query.filter(
                AmbassadorReferralHistoryExtra.referree_id.in_(ch_ee_ids)
            ).all()
            ee_exist_extra_map.update({i.referree_id: i for i in ch_extra_rows})

        for _idx, ee_id in enumerate(to_update_ee_ids):
            if ee_id not in ee_exist_extra_map:
                continue
            extra_row: AmbassadorReferralHistoryExtra = ee_exist_extra_map[ee_id]
            extra_row.last_update_at = report_date
            extra_row.deposit_usd += deposit_usd_map.get(ee_id, 0)
            extra_row.spot_trade_usd += spot_usd_map.get(ee_id, 0)
            extra_row.perpetual_trade_usd += per_usd_map.get(ee_id, 0)

            ee_ref_detail_r: ReferralAssetDetail = ee_ref_asset_detail_map.get(ee_id)
            if ee_ref_detail_r:
                extra_row.spot_refer_fee_usd += ee_ref_detail_r.spot_fee_usd
                extra_row.perpetual_refer_fee_usd += ee_ref_detail_r.perpetual_fee_usd

                old_spot_refer_amounts = json.loads(extra_row.spot_refer_amounts) if extra_row.spot_refer_amounts else {}
                inc_spot_refer_amounts = {ee_ref_detail_r.asset: ee_ref_detail_r.spot_amount}
                new_spot_refer_amounts = self.merge_and_sum_dict(old_spot_refer_amounts, inc_spot_refer_amounts)
                extra_row.spot_refer_amounts = json.dumps(new_spot_refer_amounts, cls=JsonEncoder)

                old_per_refer_amounts = json.loads(extra_row.perpetual_refer_amounts) if extra_row.perpetual_refer_amounts else {}
                inc_per_refer_amounts = {ee_ref_detail_r.asset: ee_ref_detail_r.perpetual_amount}
                new_per_refer_amounts = self.merge_and_sum_dict(old_per_refer_amounts, inc_per_refer_amounts)
                extra_row.perpetual_refer_amounts = json.dumps(new_per_refer_amounts, cls=JsonEncoder)
            db.session.add(extra_row)

            if _idx and _idx % self.FLUSH_SIZE:
                db.session.flush()

        if not to_update_ee_ids:
            first_row: AmbassadorReferralHistoryExtra = AmbassadorReferralHistoryExtra.query.order_by(
                AmbassadorReferralHistoryExtra.id.asc()
            ).first()
            if first_row and first_row.last_update_at and first_row.last_update_at.date() < report_date:
                first_row.last_update_at = report_date
        db.session.commit()

    @log_func_consume
    def update_ee_extra_first_deposit_data(self):
        extra_rows: list[AmbassadorReferralHistoryExtra] = AmbassadorReferralHistoryExtra.query.filter(
            AmbassadorReferralHistoryExtra.first_deposit_at.is_(None),
        ).all()
        ee_extra_row_map = {i.referree_id: i for i in extra_rows}
        if not ee_extra_row_map:
            return

        user_first_dep_data_map = {}
        for ch_ee_ids in batch_iter(ee_extra_row_map.keys(), self.CHUNK_SIZE):
            ch_dep_rows = Deposit.query.filter(
                Deposit.user_id.in_(ch_ee_ids),
                Deposit.type == Deposit.Type.ON_CHAIN,
                Deposit.status.in_([Deposit.Status.PROCESSING, Deposit.Status.CONFIRMING, Deposit.Status.FINISHED]),
            ).with_entities(
                Deposit.created_at,
                Deposit.user_id,
                Deposit.asset,
                Deposit.amount,
            ).all()

            for r in ch_dep_rows:
                user_first_dep_data_map[r.user_id] = [r.created_at, r.asset, r.amount]
            ch_p2p_rows = P2pOrder.query.filter(
                P2pOrder.customer_id.in_(ch_ee_ids),
                P2pOrder.side == P2pBusinessType.BUY,
                P2pOrder.status == P2pOrder.Status.FINISHED,
            ).with_entities(
                P2pOrder.created_at,
                P2pOrder.customer_id,
                P2pOrder.base,
                P2pOrder.base_amount,
            ).all()
            for r in ch_p2p_rows:
                if r.customer_id not in user_first_dep_data_map:
                    user_first_dep_data_map[r.customer_id] = [r.created_at, r.base, r.base_amount]
                else:
                    dep_created_at = user_first_dep_data_map[r.customer_id][0]
                    if dep_created_at > r.created_at:
                        user_first_dep_data_map[r.customer_id] = [r.created_at, r.base, r.base_amount]

        for _idx, (ee_id, first_dep_data) in enumerate(user_first_dep_data_map.items()):
            extra_row = ee_extra_row_map[ee_id]
            _price = self.get_asset_price(first_dep_data[0].date(), first_dep_data[1])
            first_dep_usd = quantize_amount(_price * first_dep_data[2], 8)
            extra_row.first_deposit_at = first_dep_data[0]
            extra_row.first_deposit_usd = first_dep_usd
            db.session.add(extra_row)
            if _idx and _idx % self.FLUSH_SIZE:
                db.session.flush()
        db.session.commit()

    @log_func_consume
    def update_ee_extra_first_trade_data(self):
        extra_rows: list[AmbassadorReferralHistoryExtra] = AmbassadorReferralHistoryExtra.query.filter(
            AmbassadorReferralHistoryExtra.first_trade_at.is_(None),
        ).all()
        ee_extra_row_map = {i.referree_id: i for i in extra_rows}
        if not ee_extra_row_map:
            return

        trade_user_ids = self.query_has_trade_user_ids()
        q_ee_ids = set(ee_extra_row_map) & trade_user_ids
        q_ee_ids = set(list(q_ee_ids)[:10000])  # 限制下数目

        sub_id_main_id_map, main_id_sub_ids_map = self.all_sub_main_info
        q_ee_sub_ids = set()
        for _ee_id in q_ee_ids:
            _sub_ids = main_id_sub_ids_map.get(_ee_id, [])
            q_ee_sub_ids.update(_sub_ids)
        all_user_ids = q_ee_ids | q_ee_sub_ids
        self.log(f"update_ee_extra_first_trade_data q_ee_ids.len {len(q_ee_ids)} all_user_ids.len {len(all_user_ids)}")

        #
        first_exc_trade_info_map = self.query_user_first_exc_trade_info(all_user_ids)

        first_spot_trade_info_map = {}
        spot_market_asset_info_map = self.get_all_spot_market_asset_info()
        dbs_tables = TradeHistoryDB.users_to_dbs_and_tables(all_user_ids, table_name='order_history')
        for db_tables in dbs_tables:
            _db = db_tables[0]
            for _table, _table_user_ids in db_tables[1].items():
                if not _table_user_ids:
                    continue
                self.log(f"update_ee_extra_first_trade_data query_user_first_spot_trade_info {_table} {len(_table_user_ids)} users")
                _order_table = _db.table(_table)
                _deal_table = _db.table(_table.replace("order_history", "user_deal_history"))
                for _ch_table_user_ids in batch_iter(_table_user_ids, self.CHUNK_SIZE):
                    ch_first_spot_trade_info_map = self.query_user_first_spot_trade_info(
                        _order_table, _deal_table, _ch_table_user_ids, spot_market_asset_info_map
                    )
                    first_spot_trade_info_map.update(ch_first_spot_trade_info_map)

        first_per_trade_info_map = {}
        per_market_asset_info_map = self.get_all_per_market_asset_info()
        dbs_tables = PerpetualHistoryDB.users_to_dbs_and_tables(all_user_ids, table_name='order_history')
        for db_tables in dbs_tables:
            _db = db_tables[0]
            for _table, _table_user_ids in db_tables[1].items():
                if not _table_user_ids:
                    continue
                self.log(f"update_ee_extra_first_trade_data query_user_first_per_trade_info {_table} {len(_table_user_ids)} users")
                _order_table = _db.table(_table)
                _deal_table = _db.table(_table.replace("order_history", "deal_history"))
                for _ch_table_user_ids in batch_iter(_table_user_ids, self.CHUNK_SIZE):
                    ch_first_per_trade_info_map = self.query_user_first_per_trade_info(
                        _order_table, _deal_table, _ch_table_user_ids, per_market_asset_info_map
                    )
                    first_per_trade_info_map.update(ch_first_per_trade_info_map)

        ee_first_info_map = {}
        for uid in all_user_ids:
            info_list = []
            if exc_info := first_exc_trade_info_map.get(uid):
                info_list.append(exc_info)
            if spot_info := first_spot_trade_info_map.get(uid):
                info_list.append(spot_info)
            if per_info := first_per_trade_info_map.get(uid):
                info_list.append(per_info)
            if info_list:
                info_list.sort(key=lambda x: x[0])
                main_id = sub_id_main_id_map.get(uid, uid)
                ee_first_info_map[main_id] = info_list[0]

        for _idx, (ee_id, first_info) in enumerate(ee_first_info_map.items()):
            extra_row: AmbassadorReferralHistoryExtra = ee_extra_row_map[ee_id]
            extra_row.first_trade_at = first_info[0]
            extra_row.first_trade_usd = first_info[1]
            db.session.add(extra_row)
            if _idx and _idx % self.FLUSH_SIZE:
                db.session.flush()
        db.session.commit()

    @log_func_consume
    def query_user_first_exc_trade_info(self, user_ids: set[int]) -> dict[int, list]:
        """ 首次兑换订单数据 """
        # 不汇总到主帐号
        user_data_map = {}
        for ch_user_ids in batch_iter(user_ids, self.CHUNK_SIZE):
            exc_min_orders = AssetExchangeOrder.query.filter(
                AssetExchangeOrder.user_id.in_(ch_user_ids),
                AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED,
            ).group_by(
                AssetExchangeOrder.user_id,
            ).with_entities(
                AssetExchangeOrder.user_id,
                func.min(AssetExchangeOrder.id).label('id'),
                AssetExchangeOrder.created_at,
                AssetExchangeOrder.target_asset,
                AssetExchangeOrder.target_asset_exchanged_amount,
            ).all()
            min_order_ids = [i.id for i in exc_min_orders]

            exc_orders = AssetExchangeOrder.query.filter(
                AssetExchangeOrder.id.in_(min_order_ids),
            ).with_entities(
                AssetExchangeOrder.user_id,
                AssetExchangeOrder.created_at,
                AssetExchangeOrder.target_asset,
                AssetExchangeOrder.target_asset_exchanged_amount,
            ).all()
            for exc_order in exc_orders:
                user_id = exc_order.user_id
                date_ = exc_order.created_at.date()
                rate = self.get_asset_price(date_, exc_order.target_asset)
                usd = quantize_amount(rate * exc_order.target_asset_exchanged_amount, 8)
                user_data_map[user_id] = [exc_order.created_at, usd]

        return user_data_map

    @log_func_consume
    def query_user_first_spot_trade_info(self, order_table, deal_table, user_ids, market_asset_info_map):
        """ 首次现货订单数据 """
        # 不汇总到主帐号，优先查历史委托，没有历史委托的，再查成交记录，取第一条成交记录
        user_id_str = ','.join(map(str, user_ids))
        where = f'user_id in ({user_id_str}) '
        columns = ['user_id', 'min(order_id)']
        for _ in range(3):
            try:
                id_rows = order_table.select(
                    *columns,
                    where=where,
                    group_by='user_id',
                )
                break
            except Exception as _ee:
                self.log(f"query_user_first_spot_trade_info spot {order_table} error {_ee}, retry...")
                time.sleep(3)
        else:
            self.log(f"query_user_first_spot_trade_info spot {order_table} retry_fail return_empty_data")
            return {}

        if not id_rows:
            self.log(f"query_user_first_spot_trade_info {order_table} empty_data")
            order_rows = []
        else:
            order_ids = [i[1] for i in id_rows]
            order_id_str = ','.join(map(str, order_ids))
            order_where = f'order_id in ({order_id_str}) '
            order_rows = order_table.select(
                *['user_id', 'create_time', 'market', 'deal_money'],
                where=order_where,
            )

        user_trade_data_map = {}
        for user_id, create_time, market, deal_money in order_rows:
            if market not in market_asset_info_map:
                continue
            date_ = timestamp_to_date(create_time)
            quote_asset = market_asset_info_map[market][1]
            deal_money = quantize_amount(deal_money, 8)
            rate = self.get_asset_price(date_, quote_asset)
            usd = quantize_amount(rate * deal_money, 8)
            user_trade_data_map[user_id] = [timestamp_to_datetime(create_time), usd]

        miss_user_ids = set(user_ids) - set(user_trade_data_map)
        if not miss_user_ids:
            return user_trade_data_map

        #
        deal_user_id_str = ','.join(map(str, miss_user_ids))
        deal_where = f'user_id in ({deal_user_id_str}) '
        deal_columns = ['user_id', 'min(deal_id)']
        deal_id_rows = []
        for _ in range(3):
            try:
                deal_id_rows = deal_table.select(
                    *deal_columns,
                    where=deal_where,
                    group_by='user_id',
                )
                break
            except Exception as _ee:
                self.log(f"query_user_first_spot_trade_info spot_deal {deal_table} error {_ee}, retry...")
                time.sleep(3)

        if not deal_id_rows:
            self.log(f"query_user_first_spot_trade_info spot_deal {deal_table} empty_data")
            return user_trade_data_map

        deal_ids = [i[1] for i in deal_id_rows]
        deal_id_str = ','.join(map(str, deal_ids))
        deal_where = f'deal_id in ({deal_id_str}) '
        deal_rows = deal_table.select(
            *['user_id', 'time', 'market', 'deal'],
            where=deal_where,
        )
        for user_id, create_time, market, deal_money in deal_rows:
            if market not in market_asset_info_map:
                continue
            date_ = timestamp_to_date(create_time)
            quote_asset = market_asset_info_map[market][1]
            deal_money = quantize_amount(deal_money, 8)
            rate = self.get_asset_price(date_, quote_asset)
            usd = quantize_amount(rate * deal_money, 8)
            user_trade_data_map[user_id] = [timestamp_to_datetime(create_time), usd]

        return user_trade_data_map

    @log_func_consume
    def query_user_first_per_trade_info(self, order_table, deal_table, user_ids, market_asset_info_map):
        """ 首次合约订单数据 """
        # 不汇总到主帐号
        user_id_str = ','.join(map(str, user_ids))
        where = f'user_id in ({user_id_str}) '
        columns = ['user_id', 'min(order_id)']
        for _ in range(3):
            try:
                id_rows = order_table.select(
                    *columns,
                    where=where,
                    group_by='user_id',
                )
                break
            except Exception as _ee:
                self.log(f"query_user_first_per_trade_info {order_table} error {_ee}, retry...")
                time.sleep(3)
        else:
            self.log(f"query_user_first_per_trade_info {order_table} retry_fail return_empty_data")
            return {}

        if not id_rows:
            self.log(f"query_user_first_per_trade_info {order_table} empty_data")
            order_rows = []
        else:
            order_ids = [i[1] for i in id_rows]
            order_id_str = ','.join(map(str, order_ids))
            order_where = f'order_id in ({order_id_str}) '
            order_rows = order_table.select(
                *['user_id', 'create_time', 'market', 'deal_stock', 'price'],
                where=order_where,
            )

        user_trade_data_map = {}
        for user_id, create_time, market, deal_stock, price in order_rows:
            if market not in market_asset_info_map:
                continue
            date_ = timestamp_to_date(create_time)
            bse_asset, quote_asset, market_type = market_asset_info_map[market]
            if market_type == PerpetualMarketType.DIRECT:
                rate = self.get_asset_price(date_, quote_asset)
                usd = rate * deal_stock
            else:
                usd = price * deal_stock
            usd = quantize_amount(usd, 8)
            user_trade_data_map[user_id] = [timestamp_to_datetime(create_time), usd]

        miss_user_ids = set(user_ids) - set(user_trade_data_map)
        if not miss_user_ids:
            return user_trade_data_map

        #
        deal_user_id_str = ','.join(map(str, miss_user_ids))
        deal_where = f'user_id in ({deal_user_id_str}) '
        deal_columns = ['user_id', 'min(deal_id)']
        deal_id_rows = []
        for _ in range(3):
            try:
                deal_id_rows = deal_table.select(
                    *deal_columns,
                    where=deal_where,
                    group_by='user_id',
                )
                break
            except Exception as _ee:
                self.log(f"query_user_first_per_trade_info per_deal {deal_table} error {_ee}, retry...")
                time.sleep(3)

        if not deal_id_rows:
            self.log(f"query_user_first_per_trade_info per_deal {deal_table} empty_data")
            return user_trade_data_map

        deal_ids = [i[1] for i in deal_id_rows]
        deal_id_str = ','.join(map(str, deal_ids))
        deal_where = f'deal_id in ({deal_id_str}) '
        deal_rows = deal_table.select(
            *['user_id', 'time', 'market', 'deal_stock', 'price'],
            where=deal_where,
        )
        for user_id, create_time, market, deal_stock, price in deal_rows:
            if market not in market_asset_info_map:
                continue
            date_ = timestamp_to_date(create_time)
            bse_asset, quote_asset, market_type = market_asset_info_map[market]
            if market_type == PerpetualMarketType.DIRECT:
                rate = self.get_asset_price(date_, quote_asset)
                usd = rate * deal_stock
            else:
                usd = price * deal_stock
            usd = quantize_amount(usd, 8)
            user_trade_data_map[user_id] = [timestamp_to_datetime(create_time), usd]

        return user_trade_data_map

    @log_func_consume
    def update_amb_dashboard_data(self):
        report_date = self.report_date
        all_amb_ids = self.all_amb_ids
        ref_his_rows = self.refer_his_rows
        er_ee_ids_map = defaultdict(set)
        for r in ref_his_rows:
            er_ee_ids_map[r.referrer_id].add(r.referree_id)

        ref_his_extra_rows: list[AmbassadorReferralHistoryExtra] = AmbassadorReferralHistoryExtra.query.all()
        ee_extra_map: dict[int, AmbassadorReferralHistoryExtra] = {i.referree_id: i for i in ref_his_extra_rows}

        dashboard_rows = AmbassadorDashboard.query.all()
        dashboard_map = {i.user_id: i for i in dashboard_rows}
        q_ref_amount_start_dt = max([i.last_update_at.date() for i in dashboard_rows]) + timedelta(days=1)

        new_amb_ids = all_amb_ids - set(dashboard_map)
        new_amb_spot_ref_asset_amount_map, new_amb_per_ref_asset_amount_map = self.get_amb_all_ref_amounts(new_amb_ids)
        if q_ref_amount_start_dt <= report_date:
            amb_inc_spot_ref_asset_amount_map, amb_inc_per_ref_asset_amount_map = self.get_amb_date_ref_amounts(
                all_amb_ids, q_ref_amount_start_dt, report_date)
        else:
            amb_inc_spot_ref_asset_amount_map, amb_inc_per_ref_asset_amount_map = {}, {}

        for _idx, amb_id in enumerate(all_amb_ids):
            if amb_id in dashboard_map:
                dashboard_row = dashboard_map[amb_id]
            else:
                dashboard_row = AmbassadorDashboard(user_id=amb_id)

            ee_ids = er_ee_ids_map[amb_id]
            deposit_ee_ids = set()
            trade_ee_ids = set()
            spot_trade_usd = perpetual_trade_usd = Decimal()
            spot_refer_fee_usd = perpetual_refer_fee_usd = Decimal()
            for ee_id in ee_ids:
                ee_extra = ee_extra_map.get(ee_id)
                if not ee_extra:
                    continue
                if ee_extra.first_deposit_at or ee_extra.deposit_usd > 0:
                    deposit_ee_ids.add(ee_id)
                if ee_extra.first_trade_at or ee_extra.spot_trade_usd > 0 or ee_extra.perpetual_trade_usd > 0:
                    trade_ee_ids.add(ee_id)
                spot_trade_usd += ee_extra.spot_trade_usd
                perpetual_trade_usd += ee_extra.perpetual_trade_usd
                spot_refer_fee_usd += ee_extra.spot_refer_fee_usd
                perpetual_refer_fee_usd += ee_extra.perpetual_refer_fee_usd
            dashboard_row.refer_user_count = len(ee_ids)
            dashboard_row.deposit_user_count = len(deposit_ee_ids)
            dashboard_row.trade_user_count = len(trade_ee_ids)
            dashboard_row.spot_trade_usd = spot_trade_usd
            dashboard_row.perpetual_trade_usd = perpetual_trade_usd
            dashboard_row.spot_refer_fee_usd = spot_refer_fee_usd
            dashboard_row.perpetual_refer_fee_usd = perpetual_refer_fee_usd

            if amb_id in new_amb_ids:
                spot_refer_amounts = new_amb_spot_ref_asset_amount_map[amb_id]
                per_refer_amounts = new_amb_per_ref_asset_amount_map[amb_id]
            else:
                old_spot_refer_amounts = json.loads(dashboard_row.spot_refer_amounts) if dashboard_row.spot_refer_amounts else {}
                inc_spot_refer_amounts = amb_inc_spot_ref_asset_amount_map.get(amb_id, {})
                old_per_refer_amounts = json.loads(dashboard_row.perpetual_refer_amounts) if dashboard_row.perpetual_refer_amounts else {}
                inc_per_refer_amounts = amb_inc_per_ref_asset_amount_map.get(amb_id, {})
                spot_refer_amounts = self.merge_and_sum_dict(old_spot_refer_amounts, inc_spot_refer_amounts)
                per_refer_amounts = self.merge_and_sum_dict(old_per_refer_amounts, inc_per_refer_amounts)

            dashboard_row.spot_refer_amounts = json.dumps(spot_refer_amounts, cls=JsonEncoder)
            dashboard_row.perpetual_refer_amounts = json.dumps(per_refer_amounts, cls=JsonEncoder)
            dashboard_row.last_update_at = report_date

            db.session.add(dashboard_row)
            if _idx and _idx % self.FLUSH_SIZE:
                db.session.flush()
        db.session.commit()

    @log_func_consume
    def generate_daily_trade_market_data(self):
        all_amb_ids = self.all_amb_ids
        ref_his_rows = self.refer_his_rows
        ee_er_map = {i.referree_id: i.referrer_id for i in ref_his_rows}

        report_date = self.report_date
        price_map = self.report_date_close_price_map
        report_date_str = report_date.strftime('%Y-%m-%d')

        spot_table_name = TradeSummaryDB.get_user_trade_summary_tables(report_date, report_date)[0]
        columns = ('user_id', 'market', 'money_asset', 'deal_volume')
        where = f' trade_date = "{report_date_str}" AND deal_amount > 0'
        spot_rows = TradeSummaryDB.table(spot_table_name).select(
            *columns,
            where=where,
        )
        amb_market_spot_data_map = defaultdict(  # { amd_id: { market: data } }
            lambda: defaultdict(
                lambda: {
                    'trade_usd': Decimal(),
                    'user_ids': set(),  # ee_ids
                }
            )
        )
        sub_id_main_id_map, main_id_sub_ids_map = self.all_sub_main_info
        for row in spot_rows:
            raw_user_id, market, quote, deal_quote_amount = row
            user_id = sub_id_main_id_map.get(raw_user_id, raw_user_id)
            if user_id not in ee_er_map:
                continue
            er_id = ee_er_map[user_id]
            usd = quantize_amount(price_map.get(quote, 0) * deal_quote_amount, 8)
            market_spot_data = amb_market_spot_data_map[er_id][market]
            market_spot_data['user_ids'].add(user_id)
            market_spot_data['trade_usd'] += usd

        #
        per_table_name = PerpetualSummaryDB.get_user_trade_summary_tables(report_date, report_date)[0]
        columns = ('user_id', 'market', 'market_type', 'money_asset', 'deal_volume', 'deal_amount')
        where = f' trade_date = "{report_date_str}" AND deal_amount > 0'
        per_rows = PerpetualSummaryDB.table(per_table_name).select(
            *columns,
            where=where,
        )
        amb_market_per_data_map = defaultdict(  # { amd_id: { market: data } }
            lambda: defaultdict(
                lambda: {
                    'trade_usd': Decimal(),
                    'user_ids': set(),  # ee_ids
                }
            )
        )
        for row in per_rows:
            raw_user_id, market, market_type, quote_asset, deal_volume, deal_amount = row
            user_id = sub_id_main_id_map.get(raw_user_id, raw_user_id)
            if user_id not in ee_er_map:
                continue
            er_id = ee_er_map[user_id]
            if market_type == PerpetualMarketType.DIRECT:
                rate = price_map.get(quote_asset, 0)
                usd = rate * deal_volume
            else:
                usd = deal_amount
            usd = quantize_amount(usd, 8)
            market_per_data = amb_market_per_data_map[er_id][market]
            market_per_data['user_ids'].add(user_id)
            market_per_data['trade_usd'] += usd

        exist_tm_rows = AmbassadorDashboardDailyTradeMarket.query.filter(
            AmbassadorDashboardDailyTradeMarket.report_date == report_date,
        ).all()
        amb_exist_tm_row_map = {i.user_id: i for i in exist_tm_rows}
        for _idx, amb_id in enumerate(all_amb_ids):
            if amb_id in amb_exist_tm_row_map:
                trade_m_row = amb_exist_tm_row_map[amb_id]
            else:
                trade_m_row = AmbassadorDashboardDailyTradeMarket(
                    report_date=report_date,
                    user_id=amb_id,
                )
            spot_m_data = [[k, v['trade_usd'], v['user_ids']] for k, v in amb_market_spot_data_map[amb_id].items()]
            per_m_data = [[k, v['trade_usd'], v['user_ids']] for k, v in amb_market_per_data_map[amb_id].items()]
            market_data = {
                "spot": spot_m_data,
                "perpetual": per_m_data,
            }
            trade_m_row.market_data = json.dumps(market_data, cls=JsonEncoder)

            db.session.add(trade_m_row)
            if _idx and _idx % self.FLUSH_SIZE:
                db.session.flush()
        db.session.commit()

    @classmethod
    @log_func_consume
    def update_trade_market_data(cls, end_date: date):
        start_date = end_date - timedelta(days=365)
        daily_rows: list[AmbassadorDashboardDailyTradeMarket] = AmbassadorDashboardDailyTradeMarket.query.filter(
            AmbassadorDashboardDailyTradeMarket.report_date > start_date,
            AmbassadorDashboardDailyTradeMarket.report_date <= end_date,
        ).with_entities(
            AmbassadorDashboardDailyTradeMarket.report_date,
            AmbassadorDashboardDailyTradeMarket.user_id,
            AmbassadorDashboardDailyTradeMarket.market_data,
        ).all()

        model = AmbassadorDashboardTradeMarket

        dt_range_map = {
            end_date - timedelta(days=7): model.TimeRangeEnum.DAY7,
            end_date - timedelta(days=30): model.TimeRangeEnum.DAY30,
            end_date - timedelta(days=90): model.TimeRangeEnum.DAY90,
            end_date - timedelta(days=365): model.TimeRangeEnum.DAY365,
        }
        amb_range_data_map = defaultdict(list)
        amb_ids = set()
        for d_row in daily_rows:
            amb_ids.add(d_row.user_id)
            for dt, time_range in dt_range_map.items():
                if d_row.report_date > dt:
                    amb_range_data_map[(d_row.user_id, time_range)].append(d_row)

        exist_data_rows = model.query.all()
        exist_data_row_map = {(i.user_id, i.time_range): i for i in exist_data_rows}

        for _idx, amb_id in enumerate(amb_ids):
            for time_range in model.TimeRangeEnum:
                key_ = (amb_id, time_range)
                if key_ in exist_data_row_map:
                    row = exist_data_row_map[key_]
                else:
                    row = AmbassadorDashboardTradeMarket(user_id=amb_id, time_range=time_range)
                d_rows = amb_range_data_map[key_]
                sum_market_data = cls.summary_daily_trade_market_data(d_rows)
                row.market_data = json.dumps(sum_market_data, cls=JsonEncoder)
                db.session.add(row)

            if _idx and _idx % 1000:
                db.session.flush()
        db.session.commit()

    @classmethod
    def summary_daily_trade_market_data(cls, daily_rows: list[AmbassadorDashboardDailyTradeMarket], top_num: int = 5) -> dict:
        spot_market_sum_data = defaultdict(
            lambda: {
                'trade_usd': Decimal(),
                'user_ids': set(),
            }
        )
        per_market_sum_data = defaultdict(
            lambda: {
                'trade_usd': Decimal(),
                'user_ids': set(),
            }
        )
        for d_row in daily_rows:
            data = json.loads(d_row.market_data) if d_row.market_data else {}
            # [ [market, v['trade_usd'], v['user_id']] ]
            spot_data = data['spot']
            per_data = data['perpetual']
            for item in spot_data:
                market, trade_usd, user_ids = item
                trade_usd = Decimal(trade_usd)
                spot_market_sum_data[market]['trade_usd'] += trade_usd
                spot_market_sum_data[market]['user_ids'].update(user_ids)
            for item in per_data:
                market, trade_usd, user_ids = item
                trade_usd = Decimal(trade_usd)
                per_market_sum_data[market]['trade_usd'] += trade_usd
                per_market_sum_data[market]['user_ids'].update(user_ids)

        def _get_top_data(_sum_data):
            _flat_data = [[k, v['trade_usd'], len(v['user_ids'])] for k, v in _sum_data.items()]
            _flat_data.sort(key=lambda x: x[1], reverse=True)  # trade_usd desc
            _top_trade_usd_data = [[i[0], i[1]] for i in _flat_data[:top_num]]
            _flat_data.sort(key=lambda x: x[2], reverse=True)  # user_count desc
            _top_user_count_data = [[i[0], i[2]] for i in _flat_data[:top_num]]
            return _top_trade_usd_data, _top_user_count_data

        spot_trade_usd_data, spot_user_count_data = _get_top_data(spot_market_sum_data)
        per_trade_usd_data, per_user_count_data = _get_top_data(per_market_sum_data)
        summary_res = {
            "spot_trade_usd": spot_trade_usd_data,
            "spot_user_count": spot_user_count_data,
            "perpetual_trade_usd": per_trade_usd_data,
            "perpetual_user_count": per_user_count_data,
        }
        return summary_res

    @log_func_consume
    def run(self):
        if not self.check_data_ready(self.report_date):
            return False

        # 返佣明细、返佣记录
        self.generate_daily_refer_data()
        self.generate_or_update_monthly_refer_data()

        # 邀请记录的extra信息
        self.update_ee_extra_inc_data()  # 只更新，不新增extra
        self.generate_new_ee_extra()  # 只新增extra
        self.update_ee_extra_first_deposit_data()
        self.update_ee_extra_first_trade_data()

        # 大使面板
        self.update_amb_dashboard_data()

        # 交易市场数据
        self.generate_daily_trade_market_data()
        self.update_trade_market_data(self.report_date)
        return True


class AmbassadorPackageHelper:

    @classmethod
    def check_and_release_packages(cls):
        now_ = now()
        batches = AmbassadorPackageBatch.query.filter(
            AmbassadorPackageBatch.status == AmbassadorPackageBatch.Status.AUDITED,
            AmbassadorPackageBatch.release_time < now_,
        ).all()
        now_hour_time = datetime(now_.year, now_.month, now_.day, now_.hour, 0, 0)
        for batch in batches:
            review_time = batch.review_time
            review_hour_time = datetime(review_time.year, review_time.month, review_time.day, review_time.hour,
                                                 0, 0)
            if (now_hour_time - review_hour_time).total_seconds() / 3600 < 1:
                continue
            batch.status = AmbassadorPackageBatch.Status.RELEASED
            batch.actural_release_time = now_hour_time
            first_release_time = next_month(now_.year, now_.month) + relativedelta(months=1)
            batch.first_release_time = first_release_time
            packages = UserAmbassadorPackage.query.filter(
                UserAmbassadorPackage.batch_id == batch.id,
                UserAmbassadorPackage.status == UserAmbassadorPackage.Status.AUDITED,
            ).all()
            for package in packages:
                package.status = UserAmbassadorPackage.Status.PENDING
                package.first_release_time = first_release_time
            db.session.commit()
            cls.notice_package_release(batch, packages)

    @classmethod
    def notice_package_release(cls, batch, packages):
        cls._create_pop_windows(batch, packages)
        for package in packages:
            send_ambassador_package_invite.delay(package.id)



    @classmethod
    def _create_pop_windows(cls, batch, packages):
        def get_jump():
            """上线前提前刷好数据"""
            remark = '大使激励包通用跳转【系统创建】'
            model = AppJumpList
            row = model.query.with_entities(
                model.id
            ).filter(
                model.jump_type == model.JumpType.URL,
                model.remark == remark
            ).first()
            return row.id if row else None

        jump_id = get_jump()

        if not jump_id:
            print('大使激励包活动 jump_id 未配置\n')
            return
        user_ids = [i.user_id for i in packages]

        def create_user_group():
            model = UserTagGroup
            row = model(
                name=f'大使激励包{batch.id}自动创建',
                group_type=model.GroupType.IMPORT,
                rules='',
                remark='系统创建请勿修改',
            )
            row.set_user_ids(user_ids)
            row.user_count = len(user_ids)
            row.calc_status = model.CalcStatus.FINISHED
            row.last_updated_at = now()
            db.session.add(row)
            db.session.commit()
            return row.id

        group_id = create_user_group()
        end = batch.actural_release_time + timedelta(days=batch.valid_days)
        model = PopupWindow
        sort_id = model.query.filter(
            model.status == model.Status.VALID
        ).with_entities(func.max(model.sort_id)).scalar() or 0

        pages = [{'trigger_page': "HOME", 'param_type': "", 'page_op': "", 'trigger_page_params': ''}]
        window = model(
            name=f'大使激励包{batch.id} 领取弹窗',
            started_at=now(),
            ended_at=end,
            platform=model.Platform.WEB,
            trigger_pages=json.dumps(pages),
            frequency=model.Frequency.ONCE,
            filter_type=model.FilterType.FILTERS,
            whitelist_enabled=False,
            user_whitelist='',
            jump_page_enabled=True,
            jump_type=model.JumpType.URL,
            jump_id=jump_id,
            sort_id=sort_id + 1,
            pop_position=model.PopPosition.CENTER,
            content_style=model.ContentStyle.TEXT,
        )
        window.groups = json.dumps([group_id])
        window.user_bitmap = BitMap(user_ids).serialize()
        window.target_user_number = len(user_ids)
        db.session.add(window)
        db.session.flush()
        window_id = window.id
        package_amount = amount_to_str(batch.package_amount, 2)
        asset = batch.asset
        for lang in Language:
            with force_locale(lang.value):
                title = gettext('你的专属激励包已就位')
                content = gettext('你的专属%(package_amount)s %(asset)s大使激励包待领取',
                                  package_amount=package_amount, asset=asset)
                row = PopupWindowContent(
                    popup_window_id=window_id,
                    lang=lang,
                    title=title,
                    content=content,
                )
                db.session.add(row)
        db.session.commit()

    @classmethod
    def update_package_stats(cls):
        today_ = today()
        if not check_data_ready(yesterday(), with_fee=False):
            return
        packages = UserAmbassadorPackage.query.filter(
            UserAmbassadorPackage.status == UserAmbassadorPackage.Status.ACTIVE,
            UserAmbassadorPackage.current_period_daily_stat_done_at < today_,
            UserAmbassadorPackage.current_package_release_at >= today_,  # 当期激励包还未结算时不更新
        ).all()
        if not packages:
            return
        cls._update_package_stats(packages)

    @classmethod
    def _update_package_stats(cls, packages):
        user_ids = {i.user_id for i in packages}
        end = today()
        refs = cls.get_refs(user_ids, end)
        package_trade_user_dic, package_trade_amount_dic = cls.get_package_trade_dic(packages, refs, end)

        normal_ambs, business_ambs = ReferralRepository.get_ambassador_ids(user_ids)

        for package in packages:
            user_id = package.user_id
            current_refer_users = package_trade_user_dic[package.id]
            current_refer_amount = package_trade_amount_dic[package.id]
            package.current_refer_users += current_refer_users
            package.current_refer_amount += current_refer_amount
            package.total_refer_users += current_refer_users
            package.total_refer_amount += current_refer_amount
            package.current_period_daily_stat_done_at = end
            if user_id in normal_ambs:
                package.ambassador_type = AmbassadorType.NORMAL
            elif user_id in business_ambs:
                package.ambassador_type = AmbassadorType.BUSINESS
            else:
                package.ambassador_type = AmbassadorType.NONE

        db.session.commit()

    @classmethod
    def get_refs(cls, user_ids, end):
        market_makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
        market_makers = set(market_makers)
        refs = defaultdict(list)
        ref_rows = ReferralHistory.query.filter(
            ReferralHistory.status == ReferralHistory.Status.VALID,
            ReferralHistory.effected_at < end,
            ReferralHistory.referrer_id.in_(user_ids),
        ).with_entities(ReferralHistory.referrer_id, ReferralHistory.referree_id).all()
        for user_id, ref_user_id in ref_rows:
            if ref_user_id in market_makers:
                continue
            refs[user_id].append(ref_user_id)
        return refs

    @classmethod
    def get_package_trade_dic(cls, packages, refs, end):
        package_trade_user_dic, package_trade_amount_dic = defaultdict(int), defaultdict(Decimal)
        for package in packages:
            user_id = package.user_id
            ref_user_ids = refs.get(user_id, [])
            start = package.current_period_daily_stat_done_at
            new_referral_trade_user_set = get_period_first_trade_user_set(start, end, ref_user_ids)
            referree_trade_amount_mapping = get_period_trade_amount_mapping(start, end, ref_user_ids)
            package_trade_user_dic[package.id] = len(new_referral_trade_user_set)
            package_trade_amount_dic[package.id] = sum(referree_trade_amount_mapping.values())
        return package_trade_user_dic, package_trade_amount_dic
    
    @classmethod
    def update_batch_stats(cls):
        batches = AmbassadorPackageBatch.query.filter(
            AmbassadorPackageBatch.status == AmbassadorPackageBatch.Status.RELEASED,
        ).all()
        for batch in batches:
            packages = UserAmbassadorPackage.query.filter(
                UserAmbassadorPackage.batch_id == batch.id,
                UserAmbassadorPackage.claim_time.isnot(None),
            ).all()
            today_ = today()
            released_periods = max(0, today_.month - batch.first_release_time.month + 1)
            total_refer_users = 0
            total_refer_amount = Decimal()
            released_amount = Decimal()
            rewarded_user_ids = set()
            for package in packages:
                total_refer_users += package.total_refer_users
                total_refer_amount += package.total_refer_amount
                amount = package.total_released_amount
                released_amount += amount
                if amount > 0:
                    rewarded_user_ids.add(package.user_id)

            batch.released_periods = released_periods
            if released_periods == batch.periods:
                batch.status = AmbassadorPackageBatch.Status.FINISHED
            batch.claimed_users = len(packages)
            batch.rewarded_users = len(rewarded_user_ids)
            batch.total_released_amount = released_amount
            batch.total_refer_users = total_refer_users
            batch.total_refer_amount = total_refer_amount
            db.session.commit()

    @classmethod
    def check_package_expiration(cls):
        packages = UserAmbassadorPackage.query.filter(
            UserAmbassadorPackage.status == UserAmbassadorPackage.Status.PENDING,
        ).all()
        batch_ids = {i.batch_id for i in packages}
        batches = AmbassadorPackageBatch.query.filter(
            AmbassadorPackageBatch.id.in_(batch_ids),
        ).with_entities(
            AmbassadorPackageBatch.id,
            AmbassadorPackageBatch.actural_release_time,
            AmbassadorPackageBatch.valid_days,
        ).all()
        batch_expired_map = {}
        now_ = now()
        for batch in batches:
            t = batch.actural_release_time + timedelta(days=batch.valid_days)
            batch_expired_map[batch.id] = bool(t < now_)

        for package in packages:
            if batch_expired_map[package.batch_id]:
                package.status = UserAmbassadorPackage.Status.EXPIRED
        db.session.commit()

    @classmethod
    def has_normal_appraisal(cls):
        _today = today()
        report_date = last_month(_today.year, _today.month)
        last = AppraisalHistory.query.filter(
            AppraisalHistory.business_type == AppraisalHistory.BusinessType.AMBASSADOR,
            AppraisalHistory.report_date == report_date
        ).first()
        if last:
            return True
        return False

    @classmethod
    def finish_package_stats(cls):
        """需要等当天的激励包统计完成后再执行考核"""
        date_ = this_month()
        rec = UserAmbassadorPackage.query.filter(
            UserAmbassadorPackage.current_period_daily_stat_done_at == date_,
            UserAmbassadorPackage.current_package_release_at == date_,
        ).first()
        if not rec:
            return False
        return True

    @classmethod
    def add_settle_history(cls, package, batch, period_amount: Decimal, meet_requirement: bool, is_ambassador: bool):
        this_month_ = this_month()
        current_refer_users = package.current_refer_users
        current_refer_amount = package.current_refer_amount
        current_period = package.current_period
        if meet_requirement:
            settled_amount = period_amount
            is_settled = True
        else:
            settled_amount = Decimal()
            is_settled = False
        rec = PackageSettlementHistory(
            user_package_id=package.id,
            batch_id=package.batch_id,
            user_id=package.user_id,
            period=current_period,
            batch_name=batch.actural_release_time.strftime('%Y%m%d'),
            refer_users=current_refer_users,
            refer_amount=current_refer_amount,
            period_amount=period_amount,
            is_ambassador=is_ambassador,
            is_settled=is_settled,
            asset=batch.asset,
            settled_amount=settled_amount,
            settlement_time=this_month_,
        )
        db.session.add(rec)

    @classmethod
    def update_package(cls, package, is_last_period: bool, settle_amount: Decimal, user_not_ambassador: bool, invalidated: bool):
        current_period = package.current_period
        failed_checks = package.failed_checks
        this_month_ = this_month()
        if invalidated:
            package.status = UserAmbassadorPackage.Status.INVALIDATED
        else:
            if is_last_period:
                package.status = UserAmbassadorPackage.Status.FINISHED
            else:
                release_dt = next_month(this_month_.year, this_month_.month)
                package.current_period = current_period + 1
                package.current_period_start_time = this_month_
                package.current_period_end_time = release_dt - timedelta(days=1)
                package.current_package_release_at = release_dt
                package.current_refer_users = 0
                package.current_refer_amount = 0

        if user_not_ambassador:
            package.failed_checks = failed_checks + 1
        else:
            package.failed_checks = 0
        package.released_periods += 1
        package.total_released_amount += settle_amount

    @classmethod
    def get_period_amount(cls, batch, is_last_period):
        package_amount = batch.package_amount
        periods = batch.periods
        each_period_reward = quantize_amount(package_amount / periods, 2)
        if is_last_period:
            period_amount = package_amount - each_period_reward * (periods - 1)
        else:
            period_amount = each_period_reward
        return period_amount

    @classmethod
    def handle_user_not_ambassador(cls, package, batch):
        failed_checks = package.failed_checks
        current_period = package.current_period
        periods = batch.periods

        if failed_checks >= UserAmbassadorPackage.FAILED_CHECKS_LIMIT - 1:
            invalidated = True
        else:
            invalidated = False
        if current_period >= periods:
            is_last_period = True
        else:
            is_last_period = False
        period_amount = cls.get_period_amount(batch, is_last_period)
        # 先添加history，再更新package
        cls.add_settle_history(package, batch, period_amount, False, False)
        cls.update_package(package, is_last_period, Decimal(), True, invalidated)

        db.session.commit()
        cls.notice_user_not_ambassador(package, invalidated, period_amount, batch.asset)


    @classmethod
    def notice_user_not_ambassador(cls, package, invalidated, period_amount, asset):
        if not invalidated:
            title = MessageTitle.NOT_MEET_REQUIREMENT_PACKAGE.name
            content = MessageContent.PACKAGE_NOT_SETTLED_USER_NOT_AMBASSADOR.name
            web_link = MessageWebLink.AMBASSADOR_PACKAGE_PAGE.value

            db.session.add(
                Message(
                    user_id=package.user_id,
                    title=title,
                    content=content,
                    params=json.dumps({
                        'period_amount': amount_to_str(period_amount, 2),
                        'asset': asset,
                    }),
                    extra_info=json.dumps(
                        dict(
                            web_link=web_link,
                            android_link="",
                            ios_link="",
                        )
                    ),
                    display_type=Message.DisplayType.POPUP_WINDOW
                )
            )
            db.session.commit()
        send_package_settlement_not_ambassador_msg.delay(package.id, invalidated)

    @classmethod
    def handle_user_not_meet_package_requirement(cls, package, batch):
        current_period = package.current_period
        periods = batch.periods
        if current_period >= periods:
            is_last_period = True
        else:
            is_last_period = False
        period_amount = cls.get_period_amount(batch, is_last_period)
        cls.add_settle_history(package, batch, period_amount, False, True)
        cls.update_package(package, is_last_period, Decimal(), False, False)
        db.session.commit()
        cls.notice_user_not_meet_requirement(package, is_last_period, period_amount, batch.asset)

    @classmethod
    def notice_user_not_meet_requirement(cls, package, is_last_period, period_amount, asset):
        title = MessageTitle.NOT_MEET_REQUIREMENT_PACKAGE.name
        if is_last_period:
            content = MessageContent.NOT_MEET_REQUIREMENT_PACKAGE_LAST_PERIOD.name
            web_link=MessageWebLink.AMBASSADOR_PACKAGE_FINISHED_PAGE.value
        else:
            content = MessageContent.NOT_MEET_REQUIREMENT_PACKAGE.name
            web_link = MessageWebLink.AMBASSADOR_PACKAGE_PAGE.value
        db.session.add(
            Message(
                user_id=package.user_id,
                title=title,
                content=content,
                params=json.dumps({
                    'period_amount': amount_to_str(period_amount, 2),
                    'asset': asset,
                }),
                extra_info=json.dumps(
                    dict(
                        web_link=web_link,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.POPUP_WINDOW
            )
        )
        db.session.commit()
        send_not_meet_requirement_package_msg.delay(package.id, is_last_period)

    @classmethod
    def handle_user_meet_package_requirement(cls, package, batch, activity_id):
        asset = batch.asset
        current_period = package.current_period
        periods = batch.periods
        if current_period >= periods:
            is_last_period = True
        else:
            is_last_period = False
        period_amount = cls.get_period_amount(batch, is_last_period)
        with CacheLock(LockKeys.ambassador_package(package.id)):
            db.session.rollback()
            # 先添加history，再更新package
            cls.add_settle_history(package, batch, period_amount, True, True)
            gift_rec = GiftHistory(
                user_id=package.user_id,
                activity_id=activity_id,
                asset=asset,
                amount=period_amount,
                remark=f'激励包结算奖励;batch_id:{batch.id}, package_id:{package.id},'
                       f'settle period:{current_period}',
                status=GiftHistory.Status.CREATED,
            )
            db.session.add(gift_rec)
            cls.update_package(package, is_last_period, period_amount,  False, False)
            db.session.commit()
            cls.notice_user_meet_requirement(package, is_last_period, period_amount, asset)

    @classmethod
    def notice_user_meet_requirement(cls, package, is_last_period, settled_amount, asset):
        title = MessageTitle.MEET_REQUIREMENT_PACKAGE.name
        if is_last_period:
            content = MessageContent.MEET_REQUIREMENT_PACKAGE_LAST_PERIOD.name
            web_link = MessageWebLink.AMBASSADOR_PACKAGE_PAGE.value
        else:
            content = MessageContent.MEET_REQUIREMENT_PACKAGE.name
            web_link = MessageWebLink.AMBASSADOR_PACKAGE_FINISHED_PAGE.value
        db.session.add(
            Message(
                user_id=package.user_id,
                title=title,
                content=content,
                params=json.dumps({
                    'settled_amount': amount_to_str(settled_amount, 2),
                    'asset': asset,
                }),
                extra_info=json.dumps(
                    dict(
                        web_link=web_link,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.POPUP_WINDOW
            )
        )
        db.session.commit()

        send_meet_requirement_package_msg.delay(package.id, is_last_period=is_last_period)
