# -*- coding: utf-8 -*-
import json
import random
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from typing import List, Dict, Any

from flask import current_app
from pymysql import ProgrammingError

from app.api.common import get_request_platform
from app.api.common.request import RequestPlatform
from app.business import TradeSummaryDB, ServerClient, \
    PriceManager, PerpetualServerClient, UserPreferences, BalanceManager, LivenessCheckBusiness
from app.business.lock import lock_call
from app.business.clients.biz_monitor import biz_monitor
from app.business.user import update_user_two_fa_type
from app.caches import MarketCache, SecurityQuestionCache, UserLoginTokenCache
from app.caches.auth import EmailCodeCache
from app.caches.flow_control import (ResetTradePasswordFailureCache, ValidateTradePasswordFrequencyCache,
                                     ResetWithdrawPasswordFailureCache)
from app.common import WITHDRAW_PASSWORD_FAILURE_LIMIT, ProductEvent
from app.common.constants import TRADE_PASSWORD_FAILURE_LIMIT, CeleryQueues, EmailCodeType, PrecisionEnum, \
    MessageWebLink, MessageContent, MessageTitle
from app.exceptions import FrequencyExceeded, WithdrawPasswordError, WithdrawPasswordVersionError
from app.exceptions.user import TradePasswordError, TradePasswordVersionError
from app.models.security import SecurityResetAnswerHistory, \
    SecurityResetApplication, SecurityToolHistory
from app.models.user import SubAccount
from app.utils import datetime_to_time, today, next_month, \
    timestamp_to_datetime
from app.models import User, Deposit, Withdrawal, MarginLoanOrder, \
    OperationLog, UserLoginState, SecurityResetFile, UserFavoriteAsset, db, Message
from app.utils import now, quantize_amount
from app.utils.celery_ import celery_task
from flask_babel import gettext as _, force_locale


class SecurityResetMethod(Enum):
    ANSWER = 'answer_question'
    CUSTOMER = 'submit_application'
    UNFREEZE_ACCOUNT = 'unfreeze_account'


class QuestionType(Enum):
    """
    问题集合
    """
    trade_market = _("以下哪些现货市场你曾经交易过？")
    deposit_asset = _("以下哪些币种你曾经充值过？")
    withdraw_asset = _("以下哪些币种你曾经提现过？")
    margin_asset = _("以下哪些币种你曾经借过（杠杆借币）？")
    balance_asset = _("以下哪些币种，是你当前持有（价值在1USD以上）币种？")
    selected_asset = _("以下哪些币种，是你当前的自选币种？")
    sign_in_time = _("你的CoinEx注册时间（UTC+8）是？")


class SecuritySettingType(Enum):

    MOBILE = '手机'
    TOTP = 'TOTP'
    WEBAUTHN = 'webauthn'
    QRCODE_SIGNIN = '扫码登录'
    WITHDRAW_PASSWORD = '提现密码'
    PHISHING_CODE = '防钓鱼码'
    THIRD_PARTY_ACCOUNT = '谷歌账号绑定'
    FREEZE_ACCOUNT = '冻结账号'
    SIGN_OFF = '注销账号'
    WITHDRAWAL_APPROVER = '提现多人审核'

    GESTURE_PATTERN = '手势密码'
    FINGER_PRINT = '指纹密码'
    FACE_RECOGNITION = '面容ID'
    LOGIN_IP_LOCK = '登IP锁定'
    SECURITY_LOGIN = '安全登陆时长'


SECURITY_SETTING_TYPE_EVENT_MAP = {
        "login": {
            SecuritySettingType.QRCODE_SIGNIN: {
                'count': ProductEvent.QRCODE_SIGNIN_COUNT,
                'user_count': ProductEvent.QRCODE_SIGNIN_USER_COUNT,
            },
            SecuritySettingType.GESTURE_PATTERN: {
                'count': ProductEvent.GESTURE_PASSWORD_COUNT,
                'user_count': ProductEvent.GESTURE_PASSWORD_USER_COUNT,
            },
            SecuritySettingType.FINGER_PRINT: {
                'count': ProductEvent.FINGER_PASSWORD_COUNT,
                'user_count': ProductEvent.FINGER_PASSWORD_USER_COUNT,
            },
            SecuritySettingType.FACE_RECOGNITION: {
                'count': ProductEvent.FACI_ID_PASSWORD_COUNT,
                'user_count': ProductEvent.FACI_ID_PASSWORD_USER_COUNT,
            },

        },
        "2fa": {
            SecuritySettingType.WEBAUTHN: {
                'count': ProductEvent.WEB_AUTHN_COUNT,
                'user_count': ProductEvent.WEB_AUTHN_USER_COUNT,
            },
            SecuritySettingType.TOTP: {
                'count': ProductEvent.TOTP_COUNT,
                'user_count': ProductEvent.TOTP_USER_COUNT,
            },
            SecuritySettingType.MOBILE: {
                'count': ProductEvent.MOBILE_COUNT,
                'user_count': ProductEvent.MOBILE_USER_COUNT,
            },
        },
        "advance": {
            SecuritySettingType.WITHDRAW_PASSWORD: {
                'count': ProductEvent.WITHDRAW_PASSWORD_COUNT,
                'user_count': ProductEvent.WITHDRAW_PASSWORD_USER_COUNT,
            },
            SecuritySettingType.PHISHING_CODE: {
                'count': ProductEvent.PHISHING_CODE_COUNT,
                'user_count': ProductEvent.PHISHING_CODE_USER_COUNT,
            },
            SecuritySettingType.WITHDRAWAL_APPROVER: {
                'count': ProductEvent.WITHDRAWAL_APPROVER_COUNT,
                'user_count': ProductEvent.WITHDRAWAL_APPROVER_USER_COUNT,
            },
            SecuritySettingType.THIRD_PARTY_ACCOUNT: {
                'count': ProductEvent.THIRD_PARTY_ACCOUNT_COUNT,
                'user_count': ProductEvent.THIRD_PARTY_ACCOUNT_USER_COUNT,
            },
            SecuritySettingType.FREEZE_ACCOUNT: {
                'count': ProductEvent.FREEZE_ACCOUNT_COUNT,
                'user_count': ProductEvent.FREEZE_ACCOUNT_USER_COUNT,
            },
            SecuritySettingType.SIGN_OFF: {
                'count': ProductEvent.SIGN_OFF_COUNT,
                'user_count': ProductEvent.SIGN_OFF_USER_COUNT,
            },
            SecuritySettingType.LOGIN_IP_LOCK: {},
            SecuritySettingType.SECURITY_LOGIN: {},
        },
    }


SECURITY_SETTING_EVENT_DIC = {setting: event_map for typ_dic in SECURITY_SETTING_TYPE_EVENT_MAP.values()
                              for setting, event_map in typ_dic.items()}


SECURITY_GAUGE_EVENT_MAP = {
        SecuritySettingType.GESTURE_PATTERN: ProductEvent.GESTURE_PASSWORD_ENABLE_USER_COUNT,
        SecuritySettingType.FINGER_PRINT: ProductEvent.FINGER_PASSWORD_ENABLE_USER_COUNT,
        SecuritySettingType.FACE_RECOGNITION: ProductEvent.FACI_ID_PASSWORD_ENABLE_USER_COUNT,
        SecuritySettingType.WEBAUTHN: ProductEvent.WEB_AUTHN_ENABLE_COUNT,
        SecuritySettingType.TOTP: ProductEvent.TOTP_ENABLE_COUNT,
        SecuritySettingType.MOBILE: ProductEvent.MOBILE_ENABLE_COUNT,
        SecuritySettingType.WITHDRAW_PASSWORD: ProductEvent.WITHDRAWAL_PASSWORD_ENABLE_COUNT,
        SecuritySettingType.PHISHING_CODE: ProductEvent.PHISHING_CODE_ENABLE_COUNT,
        SecuritySettingType.WITHDRAWAL_APPROVER: ProductEvent.WITHDRAWAL_APPROVER_ENABLE_COUNT,
        SecuritySettingType.THIRD_PARTY_ACCOUNT: ProductEvent.THIRD_PARTY_ACCOUNT_ENABLE_COUNT,
        SecuritySettingType.LOGIN_IP_LOCK: ProductEvent.LOGIN_IP_LOCK_ENABLE_COUNT,
        SecuritySettingType.SECURITY_LOGIN: ProductEvent.SECURITY_LOGIN_ENABLE_COUNT,
    }


# 重置安全资料
def reset_security_info(user: User,
                        reset_type: SecurityResetApplication.ResetType,
                        op_role,
                        new_email=None,
                        admin_user_id=None,
                        from_api=True
                        ):
    request_platform = get_request_platform() if from_api else RequestPlatform.WEB
    # 重置提现密码
    if reset_type == SecurityResetApplication.ResetType.WITHDRAW_PASSWORD:
        user.extra.reset_withdraw_password()
        OperationLog.add(
            user.id,
            OperationLog.Operation.RESET_WITHDRAW_PASSWORD,
            user.email,
            request_platform
        )
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.RESET_WITHDRAW_PASSWORD,
            op_role,
            user.email,
            admin_user_id=admin_user_id,
        )
        return

    # 重置(解绑) TOTP
    if reset_type == SecurityResetApplication.ResetType.TOTP:
        user.set_totp_auth_key(None)
        OperationLog.add(user.id, OperationLog.Operation.RESET_TOTP_AUTH, '',
                         request_platform)
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.UNBIND_TOTP,
            op_role,
            user.email,
            admin_user_id=admin_user_id,
        )
    # 重置(解绑) 通讯密钥
    elif reset_type == SecurityResetApplication.ResetType.WEBAUTHN:
        user.delete_webauthn()
        OperationLog.add(user.id, OperationLog.Operation.UNBIND_WEBAUTHN, '',
                         request_platform)
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.UNBIND_WEBAUTHN,
            op_role,
            user.email,
            admin_user_id=admin_user_id,
        )
    # 重置(解绑) 手机号
    elif reset_type == SecurityResetApplication.ResetType.MOBILE:
        mobile_before = user.mobile
        user.set_mobile(None, None)
        OperationLog.add(user.id, OperationLog.Operation.RESET_MOBILE, '',
                         request_platform)
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.UNBIND_MOBILE,
            op_role,
            user.email,
            account_before=mobile_before,
            admin_user_id=admin_user_id,
        )
    # 设置新邮箱
    elif reset_type == SecurityResetApplication.ResetType.EMAIL:
        email_before = user.email
        user.set_email(new_email)
        for t in EmailCodeType:
            EmailCodeCache(email_before, t).delete()
        OperationLog.add(user.id, OperationLog.Operation.RESET_EMAIL, new_email,
                         request_platform)
        SecurityToolHistory.add(
            user.id,
            SecurityToolHistory.OpType.CHANGE_EMAIL,
            op_role,
            user.email,
            account_before=email_before,
            admin_user_id=admin_user_id,
        )
    # 清除登录态
    login_cache = UserLoginTokenCache(user.id)
    tokens = login_cache.clear_tokens()
    UserLoginState.clear_tokens(tokens)

    # 更新用户默认安全工具
    update_user_two_fa_type(user)


class SecurityBusiness:

    @classmethod
    def security_reset_reject_notice(cls, application: SecurityResetApplication):
        from app.business.email import send_reset_security_notice_email

        user_id = application.user_id
        user = User.query.get(user_id)
        pref = UserPreferences(user_id)
        with force_locale(pref.language.value):
            reason_text = application.get_reject_reason()
            message_reason_text = application.get_reject_reason(translate=False)

        send_reset_security_notice_email.delay(
            application.user_id, application.reset_type.name, application.status.name, reason_text,
            application.new_email)
        if application.reset_type == SecurityResetApplication.ResetType.EMAIL and application.new_email != user.email:
            send_reset_security_notice_email.delay(application.user_id,
                                                   application.reset_type.name,
                                                   application.status.name,
                                                   reason_text, user.email)
        title = {
            SecurityResetApplication.ResetType.MOBILE: MessageTitle.SECURITY_RESET_FAIL.name,
            SecurityResetApplication.ResetType.EMAIL: MessageTitle.SECURITY_RESET_FAIL.name,
            SecurityResetApplication.ResetType.TOTP: MessageTitle.SECURITY_RESET_FAIL.name,
            SecurityResetApplication.ResetType.WEBAUTHN: MessageTitle.SECURITY_RESET_FAIL.name,
            SecurityResetApplication.ResetType.WITHDRAW_PASSWORD: MessageTitle.RESET_WITHDRAW_PASSWORD_RESET_FAIL.name,
        }.get(application.reset_type, MessageTitle.SECURITY_RESET_FAIL.name)
        content = {
            SecurityResetApplication.ResetType.MOBILE: MessageContent.SECURITY_RESET_FAIL.name,
            SecurityResetApplication.ResetType.EMAIL: MessageContent.SECURITY_RESET_FAIL.name,
            SecurityResetApplication.ResetType.TOTP: MessageContent.SECURITY_RESET_FAIL.name,
            SecurityResetApplication.ResetType.WEBAUTHN: MessageContent.SECURITY_RESET_FAIL.name,
            SecurityResetApplication.ResetType.WITHDRAW_PASSWORD: MessageContent.SECURITY_RESET_WITHDRAW_PASSWORD_FAIL.name,
        }.get(application.reset_type, MessageContent.SECURITY_RESET_FAIL.name)
        web_link = {
            SecurityResetApplication.ResetType.MOBILE: MessageWebLink.ACCOUNT_SECURITY_PAGE.value,
            SecurityResetApplication.ResetType.EMAIL: MessageWebLink.ACCOUNT_SECURITY_PAGE.value,
            SecurityResetApplication.ResetType.TOTP: MessageWebLink.ACCOUNT_SECURITY_PAGE.value,
            SecurityResetApplication.ResetType.WEBAUTHN: MessageWebLink.ACCOUNT_SECURITY_PAGE.value,
            SecurityResetApplication.ResetType.WITHDRAW_PASSWORD: MessageWebLink.ACCOUNT_SETTING_PAGE.value,
        }.get(application.reset_type, MessageWebLink.ACCOUNT_SECURITY_PAGE.value)
        db.session.add(Message(
            user_id=application.user_id,
            title=title,
            content=content,
            params=json.dumps({
                'reason': message_reason_text
            }),
            extra_info=json.dumps(
                dict(
                    web_link=web_link,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.ACCOUNT_SECURITY,
        ))
        db.session.commit()

    @classmethod
    def security_reset_pass_notice(cls, application: SecurityResetApplication):
        from app.business.email import send_reset_security_notice_email

        send_reset_security_notice_email.delay(
            application.user_id, application.reset_type.name, application.status.name)

        title = {
            SecurityResetApplication.ResetType.MOBILE: MessageTitle.SECURITY_RESET_SUCCESS.name,
            SecurityResetApplication.ResetType.EMAIL: MessageTitle.SECURITY_RESET_SUCCESS.name,
            SecurityResetApplication.ResetType.TOTP: MessageTitle.SECURITY_RESET_SUCCESS.name,
            SecurityResetApplication.ResetType.WEBAUTHN: MessageTitle.RESET_WEBAUTHN_PASS.name,
            SecurityResetApplication.ResetType.WITHDRAW_PASSWORD: MessageTitle.RESET_WITHDRAW_PASSWORD_RESET_SUCCESS.name,
        }.get(application.reset_type, MessageTitle.SECURITY_RESET_SUCCESS.name)
        content = {
            SecurityResetApplication.ResetType.MOBILE: MessageContent.SECURITY_RESET_2FA_SUCCESS.name,
            SecurityResetApplication.ResetType.EMAIL: MessageContent.SECURITY_RESET_EMAIL_SUCCESS.name,
            SecurityResetApplication.ResetType.TOTP: MessageContent.SECURITY_RESET_2FA_SUCCESS.name,
            SecurityResetApplication.ResetType.WEBAUTHN: MessageContent.RESET_WEBAUTHN_PASS.name,
            SecurityResetApplication.ResetType.WITHDRAW_PASSWORD: MessageContent.SECURITY_RESET_WITHDRAW_PASSWORD_SUCCESS.name,
        }.get(application.reset_type, MessageContent.SECURITY_RESET_2FA_SUCCESS.name)

        db.session.add(Message(
            user_id=application.user_id,
            title=title,
            content=content,
            params=json.dumps({
                'reset_type': application.reset_type.value
            }),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.ACCOUNT_SECURITY_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.ACCOUNT_SECURITY,
        ))
        db.session.commit()


class _QuestionHelper:

    def __init__(self, user_id):
        from app.assets import list_all_assets
        user = User.query.get(user_id)
        if not user:
            raise ValueError
        self.user = user
        self.all_assets = list_all_assets()

    def get_answer(self, question):
        _f = getattr(self, f'_get_{question}', None)
        if not _f:
            raise NotImplementedError(f' method _get_{question} is not implemented')
        return _f()

    @staticmethod
    def organize_answer(correct_answers: List[Any], false_answers: List[Any],
                        correct_count: int = None, false_count: int = None,
                        none_of_those=True):
        """
        correct_answers: 正确答案列表
        false_answers: 错误(干扰)答案列表
        none_of_those: 选项里是否包含"以上都不正确"
        """
        if correct_count is None:
            correct_count = len(correct_answers)
        if false_count is None:
            false_count = len(false_answers)
        if correct_count + false_count > 26:
            raise ValueError("count of answers must be less than 26")
        random.shuffle(correct_answers)
        correct_answers = correct_answers[:correct_count]

        random.shuffle(false_answers)
        false_answers = false_answers[:false_count]

        alpha_map = dict()
        for i in range(26):
            alpha_map[i] = chr(ord('A') + i)
        answer_count = len(correct_answers) + len(false_answers)

        # 正确答案的索引
        correct_indexes = random.sample(list(range(answer_count)),
                                        k=len(correct_answers))

        # 错误答案的索引
        false_indexes = set(range(answer_count)) - set(correct_indexes)
        false_indexes = list(false_indexes)
        res = list(zip(correct_indexes, correct_answers))
        res.extend(list(zip(false_indexes, false_answers)))
        res.sort(key=lambda x: x[0])

        ans = [alpha_map[item] for item in correct_indexes]
        options = {alpha_map[k]: v for k, v in res}

        next_alpha = chr(ord('A') + answer_count)
        if none_of_those:
            # None 表示"以上都不正确"
            options[next_alpha] = None
        if not correct_answers:
            ans.append(next_alpha)
        return dict(
            options=options,  # 选项
            answer=ans  # 正确答案
        )

    def _get_trade_market(self):
        """
        用户交易过的现货市场（近一年）
        """
        _answer_count = 5

        user_id = self.user.id
        today_ = today()
        start_date = today_ - timedelta(days=365)
        table_names = []
        while start_date <= today_:
            table_names.append(start_date.strftime('%Y%m'))
            start_date = next_month(start_date.year, start_date.month)
        select_fields = ["stock_asset", "money_asset"]
        markets = set()
        dbs = (TradeSummaryDB, )
        for db_ in dbs:
            for table_name in table_names:
                try:
                    s = db_.table(f'user_trade_summary_{table_name}').select(
                        *select_fields,
                        where=f'user_id = {user_id}',
                        group_by='stock_asset, money_asset'
                    )
                    for item in s:
                        markets.add(f'{item[0]}/{item[1]}')
                except ProgrammingError as e:
                    if e.args[0] == 1146:
                        # table not exists
                        continue
                    raise
        market_details = MarketCache.list_online_markets_by_asset()
        all_markets = {f'{v["base_asset"]}/{v["quote_asset"]}' for v in market_details.values()}
        false_markets = all_markets - markets
        false_markets = list(false_markets)
        markets = list(markets)
        correct_count = min(len(markets), 3)
        return self.organize_answer(markets, false_markets, correct_count,
                                    _answer_count - correct_count)

    def _get_deposit_asset(self):
        """
        用户充值过的币种
        """
        _answer_count = 5

        assets = Deposit.query.filter(
            Deposit.user_id == self.user.id,
        ).with_entities(
            Deposit.asset.distinct().label("asset")
        ).all()
        assets = [item.asset for item in assets]
        random.shuffle(assets)
        false_assets = set(self.all_assets) - set(assets)
        false_assets = list(false_assets)

        correct_count = min(len(assets), 3)
        return self.organize_answer(assets, false_assets, correct_count, _answer_count - correct_count)

    def _get_withdraw_asset(self):
        """
        用户提现过的币种
        """
        _answer_count = 5

        assets = Withdrawal.query.filter(
            Withdrawal.user_id == self.user.id,
        ).with_entities(
            Withdrawal.asset.distinct().label("asset")
        ).all()
        assets = [item.asset for item in assets]
        random.shuffle(assets)
        false_assets = set(self.all_assets) - set(assets)
        false_assets = list(false_assets)

        correct_count = min(len(assets), 3)
        return self.organize_answer(assets, false_assets, correct_count,
                                    _answer_count - correct_count)

    def _get_margin_asset(self):
        """
        用户进行过杠杆借币的币种
        """
        _answer_count = 5

        assets = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id == self.user.id,
        ).with_entities(
            MarginLoanOrder.asset.distinct().label("asset")
        ).all()
        assets = [item.asset for item in assets]
        random.shuffle(assets)
        false_assets = set(self.all_assets) - set(assets)
        false_assets = list(false_assets)

        correct_count = min(len(assets), 3)
        return self.organize_answer(assets, false_assets, correct_count,
                                    _answer_count - correct_count)

    def _get_balance_asset(self):
        """
        用户持有价值大于1 USD的币种
        """
        _answer_count = 5

        def _get_total_asset(user_id: int):
            """现货(币币+杠杆+理财) + 合约"""
            merge_assets = defaultdict(Decimal)
            result = ServerClient().get_user_accounts_balances(user_id)
            for assets in result.values():
                for asset, values in assets.items():
                    merge_assets[asset] += values['available']
                    merge_assets[asset] += values['frozen']

            perpetual_client = PerpetualServerClient()
            result = perpetual_client.get_user_balances(user_id)
            for asset, values in result.items():
                merge_assets[asset] += values['available']
                merge_assets[asset] += values['frozen']
                merge_assets[asset] += values['margin']

            return merge_assets

        balance_map = _get_total_asset(self.user.id)
        price_map = PriceManager.assets_to_usd()
        assets = []
        for asset, balance in balance_map.items():
            usd = balance * price_map.get(asset, 0)
            if usd > Decimal('1'):
                assets.append(asset)
        false_assets = set(self.all_assets) - set(assets)
        assets, false_assets = list(assets), list(false_assets)

        correct_count = min(len(assets), 3)
        return self.organize_answer(assets, false_assets, correct_count,
                                    _answer_count - correct_count)

    def _get_selected_asset(self):
        """
        用户的自选币种
        """
        _answer_count = 5
        _q = UserFavoriteAsset.query.filter(
            UserFavoriteAsset.user_id == self.user.id,
            UserFavoriteAsset.status == UserFavoriteAsset.StatusType.PASSED
        ).order_by(UserFavoriteAsset.rank.asc()).all()
        assets = {v.asset for v in _q}
        false_assets = set(self.all_assets) - assets
        false_assets = list(false_assets)
        assets = list(assets)

        correct_count = min(len(assets), 3)
        return self.organize_answer(assets, false_assets, correct_count,
                                    _answer_count - correct_count)

    def _get_sign_in_time(self):
        """
        用户的注册时间
        """
        _answer_count = 5

        answer = self.user.created_at
        start, end = datetime(2018, 6, 1), now() - timedelta(days=1)
        start_ts = datetime_to_time(start)
        end_ts = datetime_to_time(end)
        correct = [datetime_to_time(answer)]
        wrong = []
        while len(wrong) < _answer_count - 1:
            random_ts = random.random() * (end_ts - start_ts) + start_ts
            if random_ts in wrong or abs(random_ts - correct[0]) < 3600 * 24 * 10:
                continue
            wrong.append(int(random_ts))
        return self.organize_answer(correct, wrong)


class SecurityQuestionBusiness:

    MAX_ALLOWED_ATTEMPTS = 3
    MIN_ALLOWED_ADMIN_AUDIT_COUNT = 1 # 答题次数大于等于此值，可允许走人工审核

    # 以下每一组，从组内所有问题中选select_count个作为候选问题
    _QUESTION_GROUPS = (
        dict(
            select_count=3,
            questions=(
                QuestionType.trade_market,
                QuestionType.margin_asset,
                QuestionType.balance_asset,
                QuestionType.selected_asset
            )
        ),
        dict(
            select_count=1,
            questions=(
                QuestionType.deposit_asset,
                QuestionType.withdraw_asset,
                QuestionType.sign_in_time
            )
        ),
    )

    # 从缓存中取或生成问题
    @classmethod
    def get_questions(cls, user_id):
        def _clear_answers(questions):
            for q in questions:
                q.pop('answers', None)
        if questions := SecurityQuestionCache(user_id).read():
            res = json.loads(questions)
        else:
            selected_questions = []
            for group in cls._QUESTION_GROUPS:
                select_count = group['select_count']
                questions = [(item.name, item.value) for item in group['questions']]
                questions = random.sample(questions, k=select_count)
                selected_questions.extend(questions)
            random.shuffle(selected_questions)
            res = []
            for question in selected_questions:
                name, subject = question
                helper = _QuestionHelper(user_id)
                ans = helper.get_answer(name)
                res.append(
                    dict(
                        subject_id=name,
                        options=ans['options'],
                        answers=ans['answer']
                    )
                )
            SecurityQuestionCache(user_id).set(json.dumps(res), ex=1800)

        tz_offset = UserPreferences(user_id).timezone_offset or 0
        question_type_map = {item.name: _(item.value) for item in QuestionType}
        for question in res:
            question['subject'] = question_type_map[question['subject_id']]

            # 对注册时间进行特殊处理
            if question['subject_id'] in (QuestionType.sign_in_time.name, ):
                option_map = question['options']
                for k in option_map.keys():
                    if option_map[k]:
                        val = int(option_map[k])
                        val += tz_offset * 60
                        option_map[k] = timestamp_to_datetime(val).strftime('%Y-%m-%d %H:%M:%S')
        _clear_answers(res)
        return res

    @classmethod
    def get_question_answers(cls, user_id):
        if questions := SecurityQuestionCache(user_id).read():
            return json.loads(questions)


    # 验证答案
    @classmethod
    def validate_answers(cls, user_id, answers: List[Dict]) -> bool:
        """
        [
            {
                id: "",
                answer: []
            },
        ]
        """
        questions = SecurityQuestionCache(user_id).read()
        if not questions:
            return False
        questions = json.loads(questions)
        question_map = dict()
        for q in questions:
            question_map[q['subject_id']] = q
        for answer in answers:
            id_ = answer['subject_id']
            if id_ not in question_map or \
                    set(answer['answers']) != set(question_map[id_]['answers']):
                return False
        return True

    @classmethod
    def clear_questions(cls, user_id):
        return SecurityQuestionCache(user_id).delete()

    @classmethod
    def get_failure_attempt_count(cls, user_id) -> int:
        history = SecurityResetAnswerHistory.query.filter(
            SecurityResetAnswerHistory.user_id == user_id
        ).order_by(SecurityResetAnswerHistory.id.desc()).limit(5).all()
        count = 0
        last_application = SecurityResetApplication.query.filter(
            SecurityResetApplication.user_id == user_id,
            SecurityResetApplication.status == SecurityResetApplication.StatusType.PASSED
        ).order_by(SecurityResetApplication.id.desc()).first()

        # 存的是value
        last_admin_history = SecurityToolHistory.query.filter(
            SecurityToolHistory.user_id == user_id,
            SecurityToolHistory.op_type.in_(
                (SecurityToolHistory.OpType.UNBIND_MOBILE.value,
                 SecurityToolHistory.OpType.UNBIND_TOTP.value,
                 SecurityToolHistory.OpType.UNBIND_WEBAUTHN.value)
            ),
            SecurityToolHistory.op_role == SecurityToolHistory.OpRole.ADMIN.value,
        ).order_by(SecurityToolHistory.id.desc()).first()

        for item in history:
            if item.status == SecurityResetAnswerHistory.Status.SUCCEEDED:
                break
            if last_application:
                operated_at = last_application.checked_at \
                    or last_application.audited_at or last_application.created_at
                if item.created_at < operated_at:
                    break
            if last_admin_history:
                if item.created_at < last_admin_history.created_at:
                    break
            count += 1
        return count

    # 用户有无答题机会
    @classmethod
    def has_answer_opportunity(cls, user_id) -> bool:
        return cls.get_failure_attempt_count(user_id) < cls.MAX_ALLOWED_ATTEMPTS

    @classmethod
    def get_opportunity_count(cls, user_id):
        return cls.MAX_ALLOWED_ATTEMPTS - cls.get_failure_attempt_count(user_id)

    @staticmethod
    def get_user_current_balance_usd(user_id: int) -> Decimal:
        price_map = PriceManager.assets_to_usd()
        manager = BalanceManager(user_id, [], price_map)  # 不算子账户
        balance_usd = manager.get_current_balance_usd()
        return quantize_amount(balance_usd, PrecisionEnum.CASH_PLACES)


class SecurityFileBusiness:

    # 以下几种用户要求上传不同的资料
    class UserIdentityType(Enum):
        NO_KYC_WITHDRAW_DEPOSIT = 1
        NO_KYC_NO_WITHDRAW_DEPOSIT = 2
        KYC_USER = 3
        # WITHDRAW_PASSWORD = 4
        EX_EMAIL_NO_KYC_WITHDRAW_DEPOSIT = 5  # 异常流程，仅重置邮箱。＜200刀
        EX_EMAIL_NO_KYC_WITHDRAW_DEPOSIT_GTE_200 = 6  # 异常流程，仅重置邮箱。>=200刀
        EX_EMAIL_NO_KYC_NO_WITHDRAW_DEPOSIT = 7  # 异常流程，仅重置邮箱。
        EX_EMAIL_NO_KYC_ACCOUNT_FORBID = 8  # 禁止提交
        LIVENESS_CHECK = 9

    required_image_map = {
        1: (SecurityResetFile.FileType.HISTORY_NOTICE_SNAPSHOT,
            SecurityResetFile.FileType.THIRD_PARTY_DEPOSIT_SNAPSHOT),
        2: (SecurityResetFile.FileType.HISTORY_NOTICE_SNAPSHOT,),
        3: (SecurityResetFile.FileType.FACE_WITH_ID_CARD,),
        4: (SecurityResetFile.FileType.FACE_WITH_ID_CARD,),
        5: (SecurityResetFile.FileType.THIRD_PARTY_DEPOSIT_SNAPSHOT,),
        6: (SecurityResetFile.FileType.THIRD_PARTY_DEPOSIT_VIDEO,),
        7: (),
        8: (),
        9: (),
    }

    required_unfreeze_image_map = {
        1: (SecurityResetFile.FileType.THIRD_PARTY_DEPOSIT_SNAPSHOT,
            SecurityResetFile.FileType.ID_CARD_FRONT,
            SecurityResetFile.FileType.ID_CARD_BACK,
            SecurityResetFile.FileType.FACE_WITH_ID_CARD),
        2: (),
        3: (SecurityResetFile.FileType.FACE_WITH_ID_CARD,),
        9: (),
    }

    @classmethod
    def get_user_identity_type(
            cls,
            user: User,
            reset_type: SecurityResetApplication.ResetType = None,
            balance_usd: Decimal = None,
            flow_case: SecurityResetApplication.FlowCase = None,
            support_liveness: bool = False,
    ):

        withdrawal = Withdrawal.query.filter(Withdrawal.user_id == user.id,
                                             Withdrawal.status.in_((Withdrawal.Status.FINISHED,
                                                                    Withdrawal.Status.PROCESSING,
                                                                    Withdrawal.Status.CONFIRMING))).first()
        deposit = Deposit.query.filter(Deposit.user_id == user.id,
                                       Deposit.type.in_((Deposit.Type.ON_CHAIN,
                                                         Deposit.Type.LOCAL)),
                                       Deposit.status.in_((Deposit.Status.FINISHED,
                                                           Deposit.Status.TOO_SMALL,
                                                           Deposit.Status.TO_HOT,
                                                           Deposit.Status.PROCESSING,
                                                           Deposit.Status.CONFIRMING))).first()
        has_deposit_withdrawal = withdrawal or deposit
        has_kyc = user.kyc_status == User.KYCStatus.PASSED
        history_applications_query = SecurityResetApplication.query.filter(
            SecurityResetApplication.user_id == user.id,
            SecurityResetApplication.status == SecurityResetApplication.StatusType.REJECTED,
        )
        history_applications = history_applications_query.all()
        reason_types = SecurityResetApplication.Reason
        for item in history_applications:
            if ({reason_types.INVALID_KYC_INFORMATION, reason_types.UNFREEZE_INVALID_KYC_INFORMATION}
                    & set(item.get_reason_list())):
                has_kyc = False
                break

        if not has_kyc:
            reset_email = reset_type is SecurityResetApplication.ResetType.EMAIL
            ex_reset_email = reset_email and flow_case is not SecurityResetApplication.FlowCase.NORMAL
            if has_deposit_withdrawal:
                identity = cls.UserIdentityType.NO_KYC_WITHDRAW_DEPOSIT
                if reset_email:
                    identity = cls.UserIdentityType.EX_EMAIL_NO_KYC_WITHDRAW_DEPOSIT
                if ex_reset_email:
                    if balance_usd is not None and balance_usd >= Decimal('200'):
                        identity = cls.UserIdentityType.EX_EMAIL_NO_KYC_WITHDRAW_DEPOSIT_GTE_200
                    elif balance_usd is not None and balance_usd >= Decimal('0.01'):
                        identity = cls.UserIdentityType.EX_EMAIL_NO_KYC_WITHDRAW_DEPOSIT
                    else:
                        identity = cls.UserIdentityType.EX_EMAIL_NO_KYC_ACCOUNT_FORBID
            else:
                identity = cls.UserIdentityType.NO_KYC_NO_WITHDRAW_DEPOSIT
                if reset_email:  # 对于邮箱类型，无论正常、异常流程
                    identity = cls.UserIdentityType.EX_EMAIL_NO_KYC_NO_WITHDRAW_DEPOSIT
                if ex_reset_email:
                    if balance_usd is not None and balance_usd < Decimal('0.01'):
                        identity = cls.UserIdentityType.EX_EMAIL_NO_KYC_ACCOUNT_FORBID
        else:
            if support_liveness:
                try:
                    LivenessCheckBusiness.upload_kyc_data(user.id)
                    can_liveness = True
                except Exception as e:  # noqa
                    current_app.logger.error(f"LivenessCheckBusiness upload_kyc_data {user.id} error:{e}")
                    can_liveness = False
                if can_liveness:
                    identity = cls.UserIdentityType.LIVENESS_CHECK
                else:
                    identity = cls.UserIdentityType.KYC_USER
            else:
                identity = cls.UserIdentityType.KYC_USER
        return identity

    @classmethod
    def get_required_file_type(cls, identity_type: UserIdentityType):
        return cls.required_image_map[identity_type.value]

    @classmethod
    def get_required_unfreeze_file_type(cls, identity_type: UserIdentityType):
        return cls.required_unfreeze_image_map[identity_type.value]


def check_withdraw_password_err_count(cache, limit):
    if cache.count() >= limit:
        raise FrequencyExceeded(message=_("提现密码错误次数超过限制，请24小时后重试"))


def check_withdraw_password_limit(user, withdraw_password):
    failure_limit, failure_ttl = WITHDRAW_PASSWORD_FAILURE_LIMIT
    failure_cache = ResetWithdrawPasswordFailureCache(user.id, ttl=failure_ttl)
    check_withdraw_password_err_count(failure_cache, failure_limit)

    if not user.extra.check_withdraw_password(withdraw_password):
        failure_cache.add_value()
        check_withdraw_password_err_count(failure_cache, failure_limit)
        raise WithdrawPasswordError({"count": failure_limit - failure_cache.count()})
    # 成功以后删除缓存
    failure_cache.delete()


def check_withdraw_password_by_api(user, withdraw_password):
    if UserPreferences(user.id).opening_withdraw_password and user.extra.has_withdraw_password:
        if not withdraw_password:
            raise WithdrawPasswordVersionError
        check_withdraw_password_limit(user, withdraw_password)
        update_security_statistics([user.id], SecuritySettingType.WITHDRAW_PASSWORD)


def update_security_statistics(user_ids: List[int], type_: SecuritySettingType):
    event_dic = SECURITY_SETTING_EVENT_DIC.get(type_)
    if not event_dic:
        return
    count_event = event_dic.get('count')
    if count_event:
        count = len(user_ids) or 1
        biz_monitor.increase_counter(count_event, value=count)
    if not user_ids:
        return
    user_count_event = event_dic.get('user_count')
    if user_count_event:
        biz_monitor.increase_uniq_counter(user_count_event, value=user_ids)


def check_trade_password(user: User, trade_password):
    freq_cache = ValidateTradePasswordFrequencyCache(user.id)
    if freq_cache.exists():
        return
    main_user = user.main_user
    if UserPreferences(main_user.id).opening_trade_password and main_user.extra.has_trade_password:
        if not trade_password:
            raise TradePasswordVersionError
        failure_limit, failure_ttl = TRADE_PASSWORD_FAILURE_LIMIT
        failure_cache = ResetTradePasswordFailureCache(user.id, ttl=failure_ttl)
        if failure_cache.count() >= failure_limit:
            raise FrequencyExceeded(message=_("交易密码错误次数超过限制，请24小时后重试"))

        if not main_user.extra.check_trade_password(trade_password):
            failure_cache.add_value()
            if failure_cache.count() >= failure_limit:
                raise FrequencyExceeded(message=_("交易密码错误次数超过限制，请24小时后重试"))
            raise TradePasswordError({"count": failure_limit - failure_cache.count()})
        freq_cache.set('1')
        freq_cache.expire(freq_cache.TTL)
        # 成功以后删除缓存
        failure_cache.delete()


@celery_task(queue=CeleryQueues.REAL_TIME)
@lock_call()
def delete_trade_password_sub_account_failure_cache(user_id: int):
    sub_user_ids = SubAccount.query.filter(
        SubAccount.main_user_id == user_id
    ).with_entities(
        SubAccount.user_id
    ).all()
    for item in sub_user_ids:
        ResetTradePasswordFailureCache(item.user_id).delete()