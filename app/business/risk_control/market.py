#!/usr/bin/env python3
import json
from decimal import Decimal
from collections import defaultdict
from enum import Enum
from itertools import chain

from dateutil import tz

from flask import current_app

from ..site import SiteSettings
from ..alert import send_alert_notice
from ..redshift import PerpetualRedShiftDB, TradeRedShiftDB
from ..voice import send_market_index_not_updated_notice
from ... import config
from ...caches.risk_control import IndexPriceAlertCache, IndexPriceNoticeIgnoreCache
from ...caches.system import MarketMaintainCache
from ...common import TradeBusinessType, PerpetualMarketType, CeleryQueues
from ...models import (
    Market, RiskUser, PerpetualMarket, SubAccount, MarginIndex, ComposeIndex, PerpetualMarketIndex,
    PerpetualComposeIndex, RiskEventLog, RiskControlMobileNoticeConfig,
)
from ...models.exchange import SysAssetExchangeOrder, AssetExchangeOrder, AssetExchangeSysUser
from ...utils import (
    celery_task, current_timestamp, quantize_amount, timestamp_to_datetime, datetime_to_str, group_by,
    batch_iter, amount_to_str, g_map,
)
from ...exceptions import ServiceUnavailable
from ..clients import ServerClient, PerpetualServerClient
from ..prices import PriceManager
from ..utils import yield_query_records_by_time_range
from .base import (
    alert_risk_msg, batch_add_risk_user, get_trading_whitelist_user, add_risk_event_log,
    MarketVolatilityRiskConfig, RiskControlGroupConfig,
)
from ...utils.date_ import op_timestamp_to_str


@celery_task(queue=CeleryQueues.RISK_CONTROL)
def check_market_volatility_task(market_ids: list, business=TradeBusinessType.SPOT.name):
    """
    检查市场波动，周期分钟内波动超过 risk_config.market_volatility_rate 且成交量大于 risk_config.market_volatility_amount USD
    """
    current_app.logger.info(f'check_market_volatility_task:{business}:{market_ids}')

    model = RiskUser

    if business == TradeBusinessType.SPOT.name:
        client = ServerClient()
        deal_db = TradeRedShiftDB
        market_config = MarketVolatilityRiskConfig().spot_market_volatility
        reason = model.Reason.MARKET_VOLATILITY
        source_detail = "币币市场"
        market_list = Market.query.filter(Market.id.in_(market_ids)).all()
    else:
        client = PerpetualServerClient()
        deal_db = PerpetualRedShiftDB
        market_config = MarketVolatilityRiskConfig().perpetual_market_volatility
        reason = model.Reason.PERPETUAL_MARKET_VOLATILITY
        source_detail = "合约市场"
        market_list = PerpetualMarket.query.filter(PerpetualMarket.id.in_(market_ids)).all()

    max_period_minuter = max([x["period"] for x in market_config]) * 15
    timestamp = int(current_timestamp())
    end_time = timestamp // 60 * 60 - 60
    min_start_time = end_time - (max_period_minuter - 1) * 60

    market_kline_map = {}
    for market in market_list:
        try:
            all_kline = client.market_kline(market=market.name, start_time=min_start_time, end_time=end_time, interval=60)
            if all_kline:
                market_kline_map[market.name] = all_kline
        except ServiceUnavailable as e:
            if e.data.get('code') == 504:  # timeout
                current_app.logger.info(f'get market {market.name} kline failed: {e.data}')
            continue

    new_market_list = list(market_kline_map.keys())
    if not new_market_list:
        return
    all_marker_trade_data = deal_db.get_trade_history_by_markets(new_market_list, min_start_time, end_time)
    market_trade_map = defaultdict(list)
    for trade in all_marker_trade_data:
        market_trade_map[trade["market"]].append(trade)
    exchange_trades = None
    if business == TradeBusinessType.SPOT.name:
        exchange_trades = _get_asset_exchange_trade(new_market_list, min_start_time, end_time)
    whitelist = _get_whitelist_users()
    market_name_map = {i.name: i for i in market_list}
    for name in new_market_list:
        market = market_name_map[name]
        all_kline = market_kline_map[name]
        trade_data = market_trade_map[name]
        exchange_trade_data = exchange_trades[name] if exchange_trades else None
        data_map_list = []
        for config_map in market_config:
            risk_data = build_risk_data(
                market, config_map, end_time, all_kline, trade_data, whitelist, reason,
                exchange_trade_data
            )
            if risk_data:
                data_map_list.append(risk_data)

        if data_map_list:
            send_risk_notice(
                data_map_list,
                source_detail,
                market,
                reason,
                end_time,
            )


def _get_asset_exchange_trade(markets, start_ts: int, end_ts: int):
    # 目前线上只配置了<最近15分钟>的风控检查
    start_dt = timestamp_to_datetime(start_ts)
    end_dt = timestamp_to_datetime(end_ts)
    model = SysAssetExchangeOrder

    order_ids = set()
    order_trades = defaultdict(lambda: defaultdict(Decimal))
    for row in yield_query_records_by_time_range(
        table=model,
        start_time=start_dt,
        end_time=end_dt,
        select_fields=(
            model.exchange_order_id,
            model.market,
            model.target_asset,
            model.target_asset_exchanged_amount,
        ),
        limit=2000,
    ):
        if row.market not in markets:
            continue
        if row.target_asset_exchanged_amount <= 0:
            continue
        order_ids.add(row.exchange_order_id)
        price = PriceManager.asset_to_usd(row.target_asset)
        if not price:
            continue
        order_trades[row.exchange_order_id][row.market] += price * row.target_asset_exchanged_amount
    orders = []
    if order_ids:
        orders = AssetExchangeOrder.query.with_entities(
            AssetExchangeOrder.id,
            AssetExchangeOrder.user_id,
            AssetExchangeOrder.source_asset,
            AssetExchangeOrder.target_asset,
        ).filter(
            AssetExchangeOrder.id.in_(order_ids)
        ).all()
    order_id_to_path = {}
    order_id_to_user = {}
    for order in orders:
        order_id_to_path[order.id] = f'{order.source_asset}-{order.target_asset}'
        order_id_to_user[order.id] = order.user_id
    ret = defaultdict(lambda: defaultdict(lambda: {'trade_usd': Decimal(), 'path': []}))
    for id_, market_to_trade in order_trades.items():
        user_id = order_id_to_user.get(id_)
        if not user_id:
            continue
        path = order_id_to_path.get(id_, '')
        for market, trade_usd in market_to_trade.items():
            ret[market][user_id]['trade_usd'] += trade_usd
            if path and path not in ret[market][user_id]['path']:
                ret[market][user_id]['path'].append(path)
    return ret


def get_asset_exchange_trade(markets, start_ts: int, end_ts: int):
    return _get_asset_exchange_trade(markets, start_ts, end_ts)


def _get_whitelist_users() -> set:
    """加上系统兑换用户"""
    whitelist = get_trading_whitelist_user()
    model = AssetExchangeSysUser
    rows = model.query.with_entities(
        model.user_id
    ).all()
    sub_user_ids = {row.user_id for row in rows}
    main_user_ids = set()
    for chunk_ids in batch_iter(sub_user_ids, 1000):
        sub_rows = SubAccount.query.filter(
            SubAccount.user_id.in_(chunk_ids)
        ).with_entities(SubAccount.main_user_id)
        main_user_ids |= {sub.main_user_id for sub in sub_rows}
    return whitelist | sub_user_ids | main_user_ids


def build_risk_data(
        market,
        config_map,
        end_time,
        all_kline,
        all_marker_trade_data,
        whitelist,
        reason,
        exchange_trade_data=None
):
    increase_rate_limit = Decimal(config_map["market_increase_rate"])
    decrease_rate_limit = Decimal(config_map["market_decrease_rate"])
    amount_limit = Decimal(config_map["market_volatility_amount"])
    period_minuter = config_map["period"] * 15
    start_time = end_time - (period_minuter - 1) * 60

    kline = [x for x in all_kline if x[0] >= start_time]
    high_idx, low_idx = 3, 4
    high = max(Decimal(x[high_idx]) for x in kline)
    low = min(Decimal(x[low_idx]) for x in kline)
    price_list = list(chain.from_iterable((Decimal(x[4]), Decimal(x[3])) for x in kline))
    increase_rate = calc_max_increase(price_list)
    decrease_rate = calc_max_decrease(price_list)

    if increase_rate < increase_rate_limit and decrease_rate < decrease_rate_limit:
        return {}

    # 小于阈值的百分比清空
    if increase_rate < increase_rate_limit:
        increase_rate = 0
    if decrease_rate < decrease_rate_limit:
        decrease_rate = 0

    # 成交量
    amount_idx = 6
    if isinstance(market, PerpetualMarket) and market.market_type == PerpetualMarketType.INVERSE:
        amount_idx = 5
    deal_amount = sum(Decimal(x[amount_idx]) for x in kline)
    price = PriceManager.asset_to_usd(market.quote_asset) if market.quote_asset != "USD" else 1
    total_amount = quantize_amount(deal_amount * price, 2)
    if total_amount < amount_limit:
        return {}

    big_user_ids = []
    small_user_ids = []
    user_deals_map = _list_market_deals(market, start_time, all_marker_trade_data)
    exchange_sources = _get_exchange_sources(exchange_trade_data, period_minuter)
    if exchange_trade_data:
        # 加上兑换，因为系统兑换用户将会排除
        for user_id, exchange_info in exchange_trade_data.items():
            user_deals_map[user_id] += exchange_info['trade_usd']

    for user_id, amount in user_deals_map.items():
        if user_id in whitelist:
            continue
        if amount >= Decimal(config_map["volatility_small_trade_amount"]):
            small_user_ids.append(user_id)

    source = f"{market.name}-{period_minuter}"
    big_user_ids, small_user_ids = exclude_last_risk_user(
        big_user_ids,
        small_user_ids,
        reason,
        start_time,
        source,
        exchange_sources=exchange_sources
    )
    if not any([big_user_ids, small_user_ids]):
        return {}

    risk_data = {
        "period_minuter": period_minuter,  # 周期
        "increase_rate": increase_rate,  # 上涨幅度
        "increase_rate_limit": increase_rate_limit,  # 上涨幅度阈值
        "decrease_rate": decrease_rate,  # 下跌幅度
        "decrease_rate_limit": decrease_rate_limit,  # 下跌幅度阈值
        "total_amount": total_amount,  # 成交市值
        "amount_limit": amount_limit,  # 成交市值阈值
        "user_deals_map": user_deals_map,  # 用户交易额
        "big_user_ids": big_user_ids,  # 大额风控用户id
        "small_user_ids": small_user_ids,  # 小额风控用户id
        "max_price": high,  # 最高价
        "min_price": low,  # 最低价
        "exchange_sources": exchange_sources  # 额外的兑换来源
    }
    return risk_data


def send_risk_notice(data_map_list, source_detail, market, reason, end_time):
    period_minuter_str = f'{"分钟、".join([str(i["period_minuter"]) for i in data_map_list])}分钟'
    detail = f'告警类型：{source_detail}市场异常波动告警\n' \
             f'市场：{market.name}\n' \
             f'周期：{period_minuter_str}\n' \
             f'波动率'

    # 告警只展示周期最大的数据
    max_period_data = sorted(data_map_list, key=lambda x: x["period_minuter"], reverse=True)[0]
    rate_detail = ""
    if max_increase_rate := max_period_data["increase_rate"]:
        rate_detail += f"上涨{max_increase_rate * 100:.2f}%（阈值为{max_period_data['increase_rate_limit'] * 100:.2f}%） "

    if max_decrease_rate := max_period_data["decrease_rate"]:
        rate_detail += f"下跌{max_decrease_rate * 100:.2f}%（阈值为{max_period_data['decrease_rate_limit'] * 100:.2f}%）"
    detail += rate_detail

    amount_detail = f"\n成交市值：{max_period_data['total_amount']}USD （阈值为{max_period_data['amount_limit']} USD）\n"
    detail += amount_detail

    risk_detail = f"{market.name}市场{period_minuter_str}内{rate_detail}，{amount_detail}"

    big_source_dict = defaultdict(set)
    small_source_dict = defaultdict(set)
    ex_source_dict = defaultdict(set)

    sorted_data_list = sorted(data_map_list, key=lambda x: x["period_minuter"])
    for data_map in sorted_data_list:
        minuter = data_map['period_minuter']
        for big_id in data_map["big_user_ids"]:
            big_source_dict[big_id].add(minuter)
        exchange_sources = data_map.get('exchange_sources', {})
        for ex_user_id, ex_source in exchange_sources.items():
            ex_source_dict[ex_user_id].add(ex_source)

    for data_map in sorted_data_list:
        minuter = data_map['period_minuter']
        for small_id in data_map["small_user_ids"]:
            if small_id in big_source_dict:
                big_source_dict[small_id].add(minuter)
            else:
                small_source_dict[small_id].add(minuter)

    def source_func(tmp_set):
        return '\n'.join([f"{market.name}-{i}" for i in sorted(tmp_set)])

    for source, batch_ids in group_by(lambda x: source_func(x[1]), big_source_dict.items()).items():
        if not ex_source_dict:
            batch_add_risk_user([i[0] for i in batch_ids], reason, risk_detail, source=source)
        else:
            for i in batch_ids:
                id_ = i[0]
                _source = source
                ex_source = ex_source_dict.get(id_)
                if ex_source:
                    ex_source = list(ex_source)
                    ex_source.sort()
                    ex_source_str = '\n'.join(ex_source)
                    _source = f'{_source}\n{ex_source_str}'
                batch_add_risk_user([id_], reason, risk_detail, source=_source)

    model = RiskUser
    small_permission = model.Permission.BALANCE_OUT_DISABLED
    for source, batch_ids in group_by(lambda x: source_func(x[1]), small_source_dict.items()).items():
        if not ex_source_dict:
            batch_add_risk_user([i[0] for i in batch_ids], reason, risk_detail, source=source,
                                permissions=[small_permission])
        else:
            for i in batch_ids:
                id_ = i[0]
                _source = source
                ex_source = ex_source_dict.get(id_)
                if ex_source:
                    ex_source = list(ex_source)
                    ex_source.sort()
                    ex_source_str = '\n'.join(ex_source)
                    _source = f'{_source}\n{ex_source_str}'
                batch_add_risk_user([id_], reason, risk_detail, source=_source,
                                    permissions=[small_permission])

    total = len([i for i in (big_source_dict.keys() | small_source_dict.keys()) if i])
    detail += f"风控用户数: {total}"
    alert_risk_msg(detail)
    finish_time = current_timestamp()

    max_start_time = end_time - (max_period_data["period_minuter"] - 1) * 60
    extra = dict(big_user_count=len(big_source_dict),
                 period_minuter=max_period_data["period_minuter"],
                 small_user_count=len(small_source_dict),
                 user_count=total,
                 increase_rate=max_increase_rate,
                 decrease_rate=max_decrease_rate,
                 increase_rate_limit=max_period_data["increase_rate_limit"],
                 decrease_rate_limit=max_period_data['decrease_rate_limit'],
                 max_price=max([i["max_price"] for i in data_map_list]),
                 min_price=max([i["min_price"] for i in data_map_list]),
                 amount=max_period_data['total_amount'],
                 amount_limit=max_period_data['amount_limit'],
                 market=market.name,
                 start=max_start_time, end=end_time)

    add_risk_event_log(market.name, reason,
                       end_time, finish_time, extra)


def _get_exchange_sources(exchange_trade_data, period_minuter):
    exchange_sources = {}
    if not exchange_trade_data:
        return exchange_sources
    for user_id, exchange_info in exchange_trade_data.items():
        path = exchange_info['path']
        path.sort()
        path_str = ';'.join(path)
        exchange_sources[user_id] = f'兑换：{path_str}-{period_minuter}'
    return exchange_sources


def exclude_last_risk_user(
        big_user_ids,
        small_user_ids,
        reason,
        start_time,
        source,
        exchange_sources=None,
):

    def uni(user_id, _source):
        return f"{user_id}{_source}"
    # 排除相同周期内重复告警的用户
    model = RiskUser
    last_users = model.query.filter(
        model.user_id.in_(big_user_ids + small_user_ids),
        model.created_at >= timestamp_to_datetime(start_time),
        model.reason == reason,
    ).all()

    # only_from_exchange_set = only_from_exchange_set or set()
    last_user_map = {}
    for user in last_users:
        source_list = user.source.split("\n")
        for old_source in source_list:
            last_user_map[uni(user.user_id, old_source)] = user

    small_permission = model.Permission.BALANCE_OUT_DISABLED
    new_big_user_ids = []
    for big_id in big_user_ids:
        last_user = last_user_map.get(uni(big_id, source))
        if (not last_user) or (last_user.permissions == [small_permission]):
            ex_source = None
            if exchange_sources and exchange_sources.get(big_id):
                ex_source = exchange_sources.get(big_id)
            if not ex_source:
                new_big_user_ids.append(big_id)
            else:
                last_user = last_user_map.get(uni(big_id, ex_source))
                if (not last_user) or (last_user.permissions == [small_permission]):
                    new_big_user_ids.append(big_id)
    new_small_user_ids = []
    for x in small_user_ids:
        if uni(x, source) not in last_user_map:
            ex_source = None
            if exchange_sources and exchange_sources.get(x):
                ex_source = exchange_sources.get(x)
            if ex_source:
                if uni(x, ex_source) not in last_user_map:
                    new_small_user_ids.append(x)
            else:
                new_small_user_ids.append(x)

    return new_big_user_ids, new_small_user_ids


def _list_market_deals(market, start_time, all_marker_trade_data):
    data = defaultdict(Decimal)
    price = PriceManager.asset_to_usd(market.quote_asset)
    for item in all_marker_trade_data:
        if item["time"] < start_time:
            continue
        if isinstance(market, PerpetualMarket) and market.market_type == PerpetualMarketType.INVERSE:
            val = Decimal(item['amount'])
        else:
            val = Decimal(item['amount']) * Decimal(item['price']) * price
        data[item['ask_user_id']] += val
        data[item['bid_user_id']] += val
    return data


def calc_max_increase(values: list) -> Decimal:
    # 找到区间内最大的涨幅
    if not values:
        return Decimal(0)
    _min, rate = values[0], 0
    for val in values:
        if val < _min:
            _min = val
        else:
            new_rate = (val - _min) / _min
            if new_rate > rate:
                rate = new_rate
    return rate


def calc_max_decrease(values: list) -> Decimal:
    # 找到区间内最大的跌幅
    if not values:
        return Decimal(0)
    _max, rate = values[0], 0
    for val in values:
        if val > _max:
            _max = val
        else:
            new_rate = (_max - val) / _max
            if new_rate > rate:
                rate = new_rate
    return rate


class IndexPriceRiskControlHelper:
    class IndexPriceReasonType(Enum):
        NO_TRADE = 1  # 所超过5min没有成交
        PRICE_MISS_TEMP = 2  # （1min内）未能成功获取到价格
        PRICE_MISS = 3  # 超过1min未能成功获取到价格
        PRICE_DEVIATE = 4  # 最新价格相对于5s前的指数价格偏差超过25%

    class ReasonStrType(Enum):
        MARGIN = '杠杆'
        PERPETUAL = '合约'

    class ReasonType(Enum):
        SPOT = 1
        PERPETUAL = 2

    @classmethod
    def format_index_price_record(cls, record, reason_str):
        reason_detail_map = {
            cls.IndexPriceReasonType.NO_TRADE.value:
                "{market}{reason_str}市场，由于{ban_exchange}交易所超过{risk_min}min没有成交，已被剔除权重并“拉黑”15min。该市场当前权重为：{detail_str}。",
            cls.IndexPriceReasonType.PRICE_MISS_TEMP.value:
                "{market}{reason_str}市场，由于{ban_exchange}交易所（1min内）未能成功获取到价格，已被临时剔除权重。该市场当前权重为：{detail_str}。",
            cls.IndexPriceReasonType.PRICE_MISS.value:
                "{market}{reason_str}市场，由于{ban_exchange}交易所超过1min未能成功获取到价格，已被剔除权重并“拉黑”15min。该市场当前权重为：{detail_str}。",
            cls.IndexPriceReasonType.PRICE_DEVIATE.value:
                "{market}{reason_str}市场，由于{ban_exchange}交易所最新价格相对于5s前的指数价格偏差超过10%，已被剔除权重并“拉黑”15min。该市场当前权重为：{detail_str}。",
        }
        market = record['market']
        ban_exchange = record['ban_exchange']
        detail = json.loads(record['detail'])
        risk_min = int(detail.get('risk_time', 900) / 60)
        # 历史原因server的detail旧结构{A:'',B'',C''}，新结构{'weight':{A:'',B'',C''}}
        weight = detail.get('weight', detail)
        detail_str = [(k + str(Decimal(v) * 100) + "%") for k, v in weight.items()]

        reason_detail = reason_detail_map[record['reason']].format(market=market, ban_exchange=ban_exchange,
                                                                   detail_str=detail_str, risk_min=risk_min,
                                                                   reason_str=reason_str)
        if not detail_str:
            reason_detail = reason_detail + '已启动“兜底机制”，采用CoinEx最新成交价。'
        return dict(
            id=record['id'],
            market=market,
            reason_type_str=reason_str,
            trigger_time=datetime_to_str(timestamp_to_datetime(record['trigger_time']), 480),
            resume_time=datetime_to_str(timestamp_to_datetime(record['resume_time']), 480) if record[
                'resume_time'] else '-',
            ban_exchange=record['ban_exchange'],
            reason_detail=reason_detail,
        )

    @classmethod
    def run(cls,
            index_type: IndexPriceAlertCache.IndexType):
        risk_cfg = RiskControlGroupConfig().index_update_monitor
        if index_type == IndexPriceAlertCache.IndexType.MARGIN:
            _normal_model = MarginIndex
            _compose_model = ComposeIndex
            _client = ServerClient()

            def format_index(_market: str):
                return f"{_market}_INDEX"

            can_check = SiteSettings.trading_enabled & SiteSettings.spot_trading_enabled
            special_markets: set = risk_cfg["index_monitor_special_spot_markets"]
            special_minutes = risk_cfg["index_not_update_minutes_special_markets"]

        elif index_type == IndexPriceAlertCache.IndexType.PERPETUAL:
            _normal_model = PerpetualMarketIndex
            _compose_model = PerpetualComposeIndex
            _client = PerpetualServerClient()

            def format_index(_market: str):
                return f"{_market}_INDEXPRICE"

            can_check = SiteSettings.trading_enabled & SiteSettings.perpetual_trading_enable
            special_markets: set = risk_cfg["index_monitor_special_perpetual_markets"]
            special_minutes = risk_cfg["index_not_update_minutes_special_markets"]
        else:
            raise ValueError
        if not can_check:
            # 维护时，不检查
            return
        markets = set()
        if _normal_model == MarginIndex:
            q = _normal_model.query.filter(
                _normal_model.status == _normal_model.StatusType.OPEN,
            ).with_entities(_normal_model.market_name).all()
            for v in q:
                markets.add(v.market_name)
        else:
            q = _normal_model.query.filter(
                _normal_model.status == _normal_model.StatusType.OPEN,
            ).with_entities(_normal_model.name).all()
            for v in q:
                markets.add(v.name)
        c_q = _compose_model.query.filter(
            _compose_model.status == _compose_model.StatusType.OPEN,
            ).with_entities(_compose_model.name).all()
        for v in c_q:
            markets.add(v.name)
        markets = list(markets)

        def get_last_minutes_close_price(market: str):
            _ts = current_timestamp(to_int=True)
            _interval = 60
            _fix_ts = _ts - _ts % _interval
            _start_ts = _fix_ts - _interval
            _index_market = format_index(market)
            result = _client.market_kline(market=_index_market, start_time=_start_ts, end_time=_fix_ts, interval=_interval)
            for _v in result:
                if _v[0] == _start_ts:
                    return Decimal(_v[1])
            return Decimal(0)

        init_data = {
            _market: Decimal('0')
            for _market in markets
        }

        try:
            # server的接口只要返回，则认为在更新，如果不返回会认为没有在更新
            index_data = _client.index_list()
            prices_data = init_data | {
                _market: Decimal(_data['index'])
                for _market, _data in index_data.items()
            }
            index_kline_data = dict(zip(markets, g_map(
                get_last_minutes_close_price, markets,
                ordered=True, fail_safe=Decimal(),
                size=50)))
            # 覆盖从index.list 获取的数据
            for _market, _price in index_kline_data.items():
                if _price > Decimal(0):
                    prices_data[_market] = _price

        except Exception as e:
            current_app.logger.error(f"get {index_type.name} index list data error {e!r}")
            prices_data = init_data

        minutes = risk_cfg["index_not_update_minutes"]
        ignore_hours = risk_cfg["index_monitor_notice_ignore_hours"]
        ignore_seconds = ignore_hours * 3600
        ignore_markets = IndexPriceNoticeIgnoreCache(index_type).get_valid_markets(ignore_seconds)
        # 配置单市场停服也不进行告警
        ignore_markets |= set(MarketMaintainCache.get_market_maintains().keys())
        normal_markets = list(set(markets) - set(special_markets))
        normal_price_data = {_market: _price for _market, _price in prices_data.items() if _market in normal_markets}
        special_price_data = {_market: _price for _market, _price in prices_data.items() if _market in special_markets}
        exception_markets = cls.monitor_price(
            index_type,
            normal_price_data,
            list(set(markets) - special_markets),
            minutes=minutes
        )
        if special_markets:
            special_exception_markets = cls.monitor_price(
                index_type,
                special_price_data,
                list(special_markets),
                minutes=special_minutes
            )
        else:
            special_exception_markets = set()

        notice_markets = exception_markets - ignore_markets
        special_notice_markets = special_exception_markets - ignore_markets

        if notice_markets or special_notice_markets:
            end_time = current_timestamp(to_int=True)
            start_time = end_time - minutes * 60
            _type_mapping = {
                IndexPriceAlertCache.IndexType.MARGIN: '杠杆',
                IndexPriceAlertCache.IndexType.PERPETUAL: '合约',
            }
            _type_text = _type_mapping[index_type]
            trigger_time_str = op_timestamp_to_str(end_time, tz.gettz("UTC+8"))
            markets_str = ' '.join(notice_markets | {f'{v}(特殊配置市场)' for v in special_notice_markets})
            extra = dict(minutes=minutes,
                         markets=markets_str,
                         trigger_time_str=trigger_time_str,
                         source=_type_text)
            slack_msg = f"""
【指数价格不更新告警】
{_type_text}市场连续{minutes}分钟指数价格不更新，请及时处理。
(特殊市场配置时间: {special_minutes}分钟)
具体市场如下：{markets_str}
            """
            add_risk_event_log(_type_text, RiskEventLog.Reason.INDEX_PRICE_NOT_UPDATED, start_time, end_time, extra)
            send_alert_notice(
                slack_msg,
                config["ADMIN_CONTACTS"].get("customer_service"),
                at=config["ADMIN_CONTACTS"]["slack_at"].get("index_monitor_notice")
            )
            mobiles = RiskControlMobileNoticeConfig.get_mobiles(
                RiskControlMobileNoticeConfig.MobileNoticeEventType.MARKET_INDEX_MONITOR
            )
            for mobile in mobiles:
                send_market_index_not_updated_notice(
                    mobile,
                    minutes
                )
            IndexPriceNoticeIgnoreCache(index_type).add(list(notice_markets | special_notice_markets), ignore_seconds)

    @classmethod
    def monitor_price(cls,
                      index_type: IndexPriceAlertCache.IndexType,
                      prices_data: dict[str, Decimal],
                      markets: list[str],
                      minutes: int) -> set:
        ts = current_timestamp(to_int=True)
        secs = minutes * 60
        cache = IndexPriceAlertCache(index_type)

        remove_keys = []
        # 按市场分组存储价格和时间戳
        histories = defaultdict(set)

        save_data = {
            f"{_market}:{ts}:{amount_to_str(_price)}": ts
            for _market, _price in prices_data.items()
        }

        for _market, _price in prices_data.items():
            if _market in markets:
                histories[_market].add((Decimal(_price), ts))

        for entry, score in cache.zrange_withscores(0, -1):
            market, save_ts, price = entry.split(":")
            if market in markets:
                if int(save_ts) < ts - secs:
                    remove_keys.append(entry)
                else:
                    histories[market].add((Decimal(price), score))

        # 清理过期的数据
        if remove_keys:
            cache.zrem(*remove_keys)

        cache.zadd(save_data)

        exception_markets = set()
        for market, history in histories.items():
            # 按时间戳排序，确保顺序
            if len(history) >= minutes:
                if len(set([_v[0] for _v in history])) == 1:
                    exception_markets.add(market)
        return exception_markets
