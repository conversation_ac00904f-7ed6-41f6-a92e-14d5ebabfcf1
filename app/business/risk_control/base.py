#!/usr/bin/env python3
import json
from collections import defaultdict
from datetime import datetime
from decimal import Decimal
from enum import Enum

from typing import Iterable, Set, List, Optional, Mapping, Any, Tuple, Dict, Literal

from flask import current_app
from sqlalchemy import or_, and_, func

from app.business.market_maker import MarketMakerHelper
from app.business.clients.server import ServerClient, PerpetualServerClient
from .. import PerpetualSysHistoryDB
from ..p2p.permission import update_user_p2p_permission_task
from ...business import UserSettings, PriceManager
from ...caches.config import RiskControlSettingCache, WithdrawalFuseSettingsCache, \
    DepositFuseSettingsCache
from ...config import config
from ...exceptions import InvalidArgument
from ...models import (CreditUser, MarketMaker, RiskUser, SubAccount, User, MarginLoanOrder, UserCheckRequest,
                       WithdrawalWhitelistUser, db, RiskEventLog, MarginLiquidationHistory,
                       UserTradeSummary, AirdropActivityRewardHistory, TradeRankActivityJoinUser,
                       DiscountActivityLotteryHistory, UserLiquidity, InvestmentBalanceHistory, ReferralHistory,
                       AirdropActivityLotteryHistory, UserSetting)
from ...business.alert import send_alert_notice
from ...models.activity import NoviceUserReport
from ...models.mongo.risk_control import (
    UserPreventRiskControlConfigMySQL as UserPreventRiskControlConfig, 
    UserPreventRiskControlEventMySQL as UserPreventRiskControlEvent,
    )
from ...models.pledge import PledgePosition
from ...models.staking import StakingUserSummary
from ...models.strategy import UserStrategy
from ...models.system import RiskControlSetting, WithdrawalFuseSetting, DepositFuseSetting
from ...utils import (
    quantize_amount, celery_task, timestamp_to_datetime, BaseConfig, ConfigField,
    ConfigMode, current_timestamp, now, batch_iter,
)
from ...utils.config_ import _convert, _dump, _load, GroupConfigField
from ...utils.parser import JsonEncoder


class RiskControlGroupConfig(BaseConfig):
    F = ConfigField
    GF = GroupConfigField
    model = RiskControlSetting
    cache = RiskControlSettingCache

    withdrawal_approve = GF(
        {
            "withdrawal_approve_min_amount": F(Decimal, "提现审核阈值(USD)", default=Decimal("200"), detail="低于该值不需要进行风控检查"),
            "withdrawal_approve_acc_amount": F(
                Decimal, "提现审核累计阈值(USD)", default=Decimal("1000"), detail="24小时内累计提现达到该值需要进行风控检查"
            ),
        },
        "提现审核"
    )

    abnormal_profit = GF(
        {
            "abnormal_profit_rate": F(Decimal, "异常盈利比例", default=Decimal("1.5")),
            "abnormal_profit_amount": F(Decimal, "异常盈利金额(USD)", default=Decimal("1000")),
        },
        "异常盈利"
    )
    kyt_thresholds = GF(
        {
            "kyt_threshold": F(Decimal, "单笔充值阈值（USD）", default=Decimal("1000"),
                               detail='单笔充值金额≥阈值的充值则进入KYT检查'),
            # "kyt_period": F(float, "累计充值周期（H）", default=0,
            #                 detail='单用户最近XX小时累计充值≥累计充值阈值则进入KYT检查'),
            "kyt_accumulated_threshold": F(Decimal, "累计充值阈值（USD）", default=Decimal('0'),
                                           detail='单用户 0 点至 23:59:59 累计充值≥累计充值阈值则进入KYT检查'),
        },
        "KYT 累计充值阈值"
    )
    immediately_charged = GF(
        {
            "ic_monitor_threshold": F(Decimal, "允许监控阈值（USD）", default=Decimal("1000"),
                                      detail='提现≥该值才进行风控检查'),
            "ic_delta_minutes": F(int, "提现距离充值时间（分钟）", default=30,
                                  detail='提现时间距离上一次充值时间在X分钟内'),
            "ic_accumulated_ratio_threshold": F(list, "提现占比监控阈值组", default=[
                (24, Decimal("0.3")),
            ], detail='近N小时累计提现市值大于账户资产的占比的M'),
            "ic_position_usd_threshold": F(list, "持仓市值监控阈值组", default=[
                (30, Decimal("5000"), 25),
            ], detail='过去X天，账户持仓市值大于 P USD的天数少于Y天（最大支持设置天数：60）'),
            "ic_trade_assets_threshold": F(list, "交易币种监控阈值组", default=[
                (7, 10),
            ], detail='A天内交易币种少于B个'),
            "ic_deposit_monitor_threshold": F(Decimal, "充值允许监控阈值（USD）", default=Decimal("500"),
                                              detail='充值≥该值才进行风控检查'),
        },
        "即充即提（已KYC）"
    )
    immediately_charged_no_kyc = GF(
        {
            "no_kyc_ic_monitor_threshold": F(Decimal, "允许监控阈值（USD）", default=Decimal("1000"),
                                             detail='提现≥该值才进行风控检查'),
            "no_kyc_ic_delta_minutes": F(int, "提现距离充值时间（分钟）", default=30,
                                         detail='提现时间距离上一次充值时间在X分钟内'),
            "no_kyc_ic_accumulated_ratio_threshold": F(list, "提现占比监控阈值组", default=[
                (24, Decimal("0.3")),
            ], detail='近N小时累计提现市值大于账户资产的占比的M'),
            "no_kyc_ic_position_usd_threshold": F(list, "持仓市值监控阈值组", default=[
                (30, Decimal("5000"), 5),
            ], detail='过去X天，账户持仓市值大于 P USD的天数少于Y天（最大支持设置天数：60）'),
            "no_kyc_ic_trade_assets_threshold": F(list, "交易币种监控阈值组", default=[
                (7, 10),
            ], detail='A天内交易币种少于B个'),
            "no_kyc_ic_deposit_monitor_threshold": F(Decimal, "充值允许监控阈值（USD）", default=Decimal("500"),
                                                     detail='充值≥该值才进行风控检查'),
        },
        "即充即提（未KYC）"
    )
    new_user_immediately_charged = GF(
        {
            "nuic_hour_amount_threshold": F(list, "距离注册时间（小时）与提现金额（USD）组合", default=[
                (0.5, Decimal("10000")),
            ], detail='这笔提现时间距离用户注册时间在Y小时内且这笔提现金额≥Z USD则触发风控;（只要满足一组就触发）'),
        },
        "新注册即提现（已KYC）"
    )
    new_user_immediately_charged_no_kyc = GF(
        {
            "no_kyc_nuic_hour_amount_threshold": F(list, "距离注册时间（小时）与提现金额（USD）组合", default=[
                (0.5, Decimal("10000")),
            ], detail='这笔提现时间距离用户注册时间在Y小时内且这笔提现金额≥Z USD则触发风控;（只要满足一组就触发）'),
        },
        "新注册即提现（未KYC）"
    )
    withdrawal_no_deposit = GF(
        {
            "wnd_monitor_threshold": F(Decimal, "允许监控阈值（USD）", default=Decimal("10000"),
                                       detail='提现≥该值才进行风控检查'),
            "wnd_accumulated_deposit_withdrawal_ratio": F(Decimal, "累计链上充值占提现市值占比", default=Decimal("0.5"),
                                                          detail='该用户有链上充值记录，但累计链上充值市值＜本次提现市值*N；或无链上充值，则提现触发风控；'),
        },
        "提现无链上充值记录（已KYC）"
    )
    withdrawal_no_deposit_no_kyc = GF(
        {
            "no_kyc_wnd_monitor_threshold": F(Decimal, "允许监控阈值（USD）", default=Decimal("10000"),
                                              detail='提现≥该值才进行风控检查'),
            "no_kyc_wnd_accumulated_deposit_withdrawal_ratio": F(Decimal, "累计链上充值占提现市值占比",
                                                                 default=Decimal("0.5"),
                                                                 detail='该用户有链上充值记录，但累计链上充值市值＜本次提现市值*N；或无链上充值，则提现触发风控；'),
        },
        "提现无链上充值记录（未KYC）"
    )
    perpetual_balance = GF(
        {
            "perpetual_balance_transfer_amount": F(
                Decimal, "转出/转入对账不平", default=Decimal(1000), detail="web转出/转入==server转入/转出 差额数据超过正负阈值(USD)"
            ),
            "perpetual_balance_flat_amount": F(
                Decimal, "与上次合约资产对账不平", default=Decimal(1000), detail="上次对账合约资产+转入-转出=当前合约资产>=阈值(USD)"
            )
        },
        "合约对账不平"
    )

    perpetual_liquidation = GF(
        {
            "perpetual_liquidation_top_amount": F(
                Decimal, "合约异常穿仓阈值(大币种)", default=Decimal(5000), detail="合约市场的穿仓金额≥合约穿仓阈值(USD)"
            ),
            "perpetual_liquidation_amount": F(
                Decimal, "合约异常穿仓阈值(小币种)", default=Decimal(2000), detail="合约市场的穿仓金额≥合约穿仓阈值(USD)"
            ),
        },
        "合约异常穿仓"
    )

    sms = GF(
        {
            "sms_monitor_interval": F(float, "短信监控时间周期(小时)", default=12),
            "sms_monitor_count": F(int, "短信监控条数", default=50)
        },
        "短信监控"
    )

    country_sms_alert_threshold = GF(
        {
            "country_sms_alert_threshold_24h_count": F(int, '单国家近24H发送量阈值', default=100),
            "country_sms_alert_threshold_increase": F(Decimal, '大于近30日均值倍数', default=Decimal(1.5)),
        },
        "国家短信告警"
    )

    spot_big_booking_order = GF(
        {
            "booking_order_offset": F(list, "挂单价格偏差", default=[
                (Decimal(1000 * 10000), Decimal("0.005")),
                (Decimal(500 * 10000), Decimal("0.001")),
                (Decimal(100 * 10000), Decimal("0.002")),
                (Decimal(0), Decimal("0.05")),
            ], detail="日成交额在该档位的市场，统计大额挂单时只统计距离最新价价差在对应范围内的挂单"),

            "asset_order_threshold": F(list, "币种挂单阈值(USD)", default=[
                (Decimal(1000 * 10000), Decimal(30 * 10000)),
                (Decimal(500 * 10000), Decimal(20 * 10000)),
                (Decimal(100 * 10000), Decimal(10 * 10000)),
                (Decimal(10 * 10000), Decimal(5 * 10000)),
                (Decimal(1 * 10000), Decimal(2 * 10000)),
                (Decimal(0), Decimal(10000)),
            ], detail="日成交额在该档位的币种，单用户该币种当前累积有效挂单大于等于该档位阈值则告警"),
        },
        "币币-大额挂单监控"
    )

    margin_basis_rate_auto_tip = GF(
        {
            "margin_tip_index_price_basis_rate": F(Decimal, '指数价格基差率', default=Decimal(500),
                                                   detail='某个杠杆市场触发基差率告警时，基差率≥该阈值时自动在前端配置风险提示'),
            "margin_tip_trigger_count": F(Decimal, '基差率告警触发次数', default=3,
                                          detail='某个杠杆市场近3小时内基差率告警触发次数≥该阈值时自动在前端配置风险提示'),
            "margin_tip_duration_hours": F(Decimal, '自动触发风险提示持续时间（小时）', default=4,
                                           detail='某个杠杆市场自动触发风险提示后的持续时间，到了持续时间需检查近1小时内是否仍有基差率告警触发'),
        },
        "杠杆基差率自动提示"
    )
    perpetual_basis_rate_auto_tip = GF(
        {
            "perpetual_tip_sign_price_basis_rate": F(Decimal, '标记价格基差率', default=Decimal(500),
                                                     detail='某个合约市场触发基差率告警时，基差率≥该阈值时自动在前端配置风险提示'),
            "perpetual_tip_trigger_count": F(Decimal, '基差率告警触发次数', default=3,
                                             detail='某个合约市场近3小时内基差率告警触发次数≥该阈值时自动在前端配置风险提示'),
            "perpetual_tip_duration_hours": F(Decimal, '自动触发风险提示持续时间（小时）', default=4,
                                              detail='某个合约市场自动触发风险提示后的持续时间，到了持续时间需检查近1小时内是否仍有基差率告警触发'),
        },
        "合约基差率自动提示"
    )

    coin_asset_liability_not_equal = GF(
        {
            "coin_asset_liability_rate": F(Decimal, '平台负债百分比阈值(%)', default=Decimal(2),
                                           detail='当平台权益绝对值 ≥ 平台负债 * 百分比阈值且平台权益绝对值市值≥阈值时触发风控'),
            "coin_asset_threshold": F(Decimal, '平台权益绝对值市值阈值（USD）', default=Decimal(10000),
                                      detail='当平台权益绝对值 ≥ 平台负债 * 百分比阈值且平台权益绝对值市值≥阈值时触发风控'),
            "coin_deteriorate_rate": F(Decimal, '智能风控恶化百分比阈值', default=Decimal(50),
                                       detail='设置防干扰时间后，如果后续对账相较于上次风控的数据进一步恶化'
                                              '（增加的数值≥币种平台负债*负债比分百阈值*恶化百分比阈值），则再次触发风控'),
        },
        "币种资产负债不平参数配置"
    )

    asset_liability_not_equal = GF(
        {
            "asset_liability_not_equal_threshold": F(Decimal, '全站资产负债不平阈值（USD）', default=Decimal(5_000_000),
                                                     detail='当全站的平台权益绝对值≥阈值时，开启全站【风控禁止提现】'),
            "asset_liability_risk_recover_at": F(datetime, '资产负债风控恢复时间', default=None,
                                                 detail='在该时间之前，全站资产负债风控使用临时阈值'),
            "deteriorate_rate": F(Decimal, '智能风控恶化百分比阈值', default=Decimal(20),
                                  detail='设置时间后，如果后续对账相较于上次风控的数据进一步恶化'
                                           '（增加的数值≥全站资产负债不平阈值*恶化百分比阈值），则再次触发风控'),
        },
        "全站资产负债不平参数配置"
    )

    audit_withdrawals_blocked_by_asset_liability = GF(
        {
            "awbbal_slack": F(int, 'slack群告警提现笔数', default=10,
                              detail='某个币种因为资产负债不平风控，导致卡待审核的提现记录＞该值则进行slack群告警'),
            "awbbal_mobile": F(int, '电话告警提现笔数', default=50,
                               detail='某个币种因为资产负债不平风控，导致卡待审核的提现记录＞该值则进行电话告警'),
        },
        "币种资产负债不平提现卡待审核告警"
    )

    signed_withdrawals_cancel_threshold = F(Decimal, "最近24小时已签名且取消的提现市值（USD）", default=Decimal(500000),
                                            detail="最近24H全站已签名但提现取消的提现市值≥阈值时触发风控")

    # deposits_fuse_threshold 迁移至 DepositFuseConfig
    # deposits_fuse_threshold = F(Decimal, "全站充值熔断市值倍数阈值", default=Decimal(2),
    #                             detail="最近24H充值金额/最近7天平均金额≥该值时触发风控")

    margin_liquidation_amount: Decimal = F(Decimal, "杠杆异常穿仓", default=Decimal(1000), detail="杠杆市场的穿仓金额≥杠杆穿仓阈值(USD)")
    margin_loan_flat_amount: Decimal = F(
        Decimal, "杠杆对账不平", default=Decimal(2000), detail="上次对账未还数量+借出-还币=当前未还数量＞阈值(USD)"
    )
    investment_balance_flat_amount: Decimal = F(
        Decimal, "理财对账不平", default=Decimal(2000), detail="上次对账余额+转入-转出+利息=当前余额>=阈值(USD)"
    )
    p2p_flat_amount: Decimal = F(
        Decimal, "p2p对账不平", default=Decimal(2000), detail="近24H p2p买入总金额 - 近24H p2p卖出总金额 >= 阈值(USD)"
    )
    sign_price_basis_rate: Decimal = F(
        Decimal, '标记价格基差率', default=Decimal(250), detail="某个合约市场当连续5分钟的「标记价格基差率」绝对值≥该阈值时触发告警（bp）"
    )
    funding_rate_diff: Decimal = F(
        Decimal, "资金费率偏差", default=Decimal('1'), detail="某个合约市场当前「资金费率」与竞品交易所的之差绝对值≥该阈值时触发告警（%）"
    )
    margin_index_price_basis_rate: Decimal = F(
        Decimal,
        '杠杆指数价格基差率',
        default=Decimal(250),
        detail="某个杠杆市场当连续5分钟的「指数价格基差率」绝对值≥该阈值时触发告警（bp）"
    )
    withdrawal_delay_audition: list = F(list, "提现延迟审核", default=[
        (Decimal('10000'), Decimal('100000'), 1),
        (Decimal('100000'), Decimal('200000'), 5),
        (Decimal('200000'), Decimal('500000'), 10),
        (Decimal('500000'), Decimal('1000000'), 30),
        (Decimal('1000000'), Decimal('Infinity'), 60),
    ], detail='近24H累计提现市值该档位的用户，提现会延迟审核对应时间')

    pledge_loan_unflat: list =F(list, '借贷对账不平', default=[
        ('USDT', Decimal('1000')),
    ], detail='上次对账待还数量 + 新增利息 + 借出 - 还币 = 当前待还数量 ≥ 对账不平阈值')
    pledge_fund_repay = GF(
        {
            "all_pledge_fund_repay_threshold": F(
                Decimal, "借贷异常穿仓阈值", default=Decimal(10000), detail="借贷总穿仓金额≥借贷穿仓阈值(USD)"
            ),
            "pledge_fund_repay_user_threshold": F(
                Decimal, "穿仓借贷用户阈值", default=Decimal(1000),
                detail="单用户穿仓市值大于阈值的穿仓金额从大到小排名前5的借贷用户，提现受限"
            ),
            "pledge_fund_repay_user_trade_threshold": F(
                Decimal, "对手盘成交阈值", default=Decimal(100),
                detail="单用户对手盘订单成交总市值大于阈值的对手用户，提现受限"
            ),
        },
        "借贷异常穿仓"
    )
    abnormal_issuance = GF(
        {
            "ai_delta_minutes": F(
                int, '筛查范围(分钟)', default=30,
                detail='向前追溯N分钟，排查这时间范围内有充值记录的用户'),
            "ai_accumulated_deposit_usd": F(
                Decimal, '单用户累计充值市值(USD)', default=Decimal(5000),
                detail='单用户累计充值市值高于M USD，标记为异常，异常用户禁止提现'),
        },
        "币种异常增发"
    )

    index_update_monitor = GF(
        {
            "index_not_update_minutes": F(
                int, '连续不更新时间(分钟)', default=5,
                detail='指数价格连续XX分钟不更新时，进行相关告警。'),
            "index_monitor_notice_ignore_hours": F(
                int, '防干扰时间(小时)', default=3,
                detail='某个市场触发告警后，后续XX个小时内不再检查该市场'),
            "index_monitor_special_spot_markets": F(
                set, '特殊现货市场', default=set(),
                detail='特殊现货市场配置'),
            "index_monitor_special_perpetual_markets": F(
                set, '特殊合约市场', default=set(),
                detail='特殊合约市场配置'),
            "index_not_update_minutes_special_markets": F(
                int, '特殊市场连续不更新时间(分钟)', default=5,
                detail='特殊市场指数价格连续XX分钟不更新时，进行相关告警。'),
        },
        "指数价格不更新"
    )

    def __init__(self):
        super().__init__(ConfigMode.REAL_TIME)

    def __getattribute__(self, name):
        if type(self)._has_field(name):
            return self._get(name)
        if type(self)._has_group_field(name):
            return self.get_many(name)
        return super().__getattribute__(name)

    def get_sms_limit(self) -> (int, int):
        sms_config = self.sms
        return sms_config["sms_monitor_count"], int(sms_config["sms_monitor_interval"] * 3600)

    def _get_all(self) -> Mapping[str, str]:
        return self.cache().get_values()

    def _get_one(self, name: str) -> Optional[str]:
        return self.cache().get_value(name)

    def get_many(self, name: str) -> Mapping[Any, Any]:
        # 参数组使用此函数获取键值，防止并发冲突
        field_group = self._get_group_field(name).field_group
        values = self.cache().get_value_with_keys(list(field_group.keys()))
        ret = {}
        for name, field in field_group.items():
            v = values.get(name) or field.default
            if v is not None:
                v = _load(field.type, v, (self, name))
            ret[name] = _convert(field.type, v, (self, name)) if v is not None else v
        return ret

    def set_many(self, name: str, mapping: Mapping):
        # 参数组使用此函数设置键值，防止并发冲突
        group = self._get_group_field(name)
        ret = {}
        for field_name, val in mapping.items():
            field = group.field_group[field_name]
            type_ = field.type
            try:
                value = _convert(type_, val, (self, field_name))
            except Exception as e:
                raise type(e)(f'{val!r} is invalid to {self._dot_name(field_name)}')
            value_str = _dump(type_, value)
            ret[field_name] = value_str
            row = self.model.get_or_create(key=field_name)
            row.value = value_str
            db.session.add(row)
        db.session.commit()
        self.cache().set_values(ret)

    def _set_one(self, name: str, value: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            row = self.model(key=name)
            db.session.add(row)
        row.value = value
        row.status = self.model.Status.VALID
        db.session.commit()
        self.cache().set_value(name, value)

    def _del_one(self, name: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            return
        row.status = self.model.Status.DELETED
        db.session.commit()
        self.cache().del_value(name)


class MarketVolatilityRiskConfig(BaseConfig):
    F = ConfigField
    GF = GroupConfigField
    model = RiskControlSetting
    cache = RiskControlSettingCache

    new_market_volatility = {
        "period": F(int, "周期(15分钟的倍数)", default=1, validator=lambda x: 0 < x < 97),  # 限制在15分钟-24小时之间
        "market_increase_rate": F(Decimal, "异常市场波动上浮阈值", default=Decimal("1.5"),
                                  detail="周期内，上涨幅度>=异常市场波动幅度阈值", validator=lambda x: x > 0),
        "market_decrease_rate": F(Decimal, "异常市场波动下跌阈值", default=Decimal("0.5"),
                                  detail="周期内，下跌幅度>=异常市场幅度阈值", validator=lambda x: 0 < x < 1),
        "market_volatility_amount": F(Decimal, "异常市场交易阈值", default=Decimal("20000"),
                                      detail="周期内，总成交市值>=异常市场交易阈值"),
        # 【异常用户】在异常波动期间有交易行为的用户
        "volatility_big_trade_amount": F(Decimal, "异常市场用户交易量大额阈值", default=Decimal("2000"),
                                         detail="其交易量＞大额阈值(USD)，提现受限，交易受限，禁止借币，禁止划出"),
        "volatility_small_trade_amount": F(Decimal, "异常市场用户交易量小额阈值", default=Decimal("200"),
                                           detail="交易量>=小额阈值(USD) 提现受限")
    }

    default_item = [{k: v.default for k, v in new_market_volatility.items()}]

    spot_market_volatility = F(list, "币币市场", default=default_item)
    perpetual_market_volatility = F(list, "合约市场", default=default_item)

    def __init__(self):
        super().__init__(ConfigMode.REAL_TIME)

    def format_desc(self):
        """
        {
            "spot_market_volatility": [
                [{"desc": "周期", "name": "period", "val": 15, "detail": "异常市场波动检查周期(分钟)"}]
                [{"desc": "异常市场波动上浮阈值", "name": "market_increase_rate", "val": "1.5",
                 "detail": "15分钟内，上涨或下跌幅度>=现货波动幅度阈值(USD)"}]
            ]
        }
        """

        fields = self.fields()
        ret = defaultdict(list)
        for name, val in fields.items():
            data = getattr(self, name)
            if not data:
                data = val.default
            for item in data:
                tmp_list = []
                for k, v in item.items():
                    if k == 'volatility_big_trade_amount':
                        continue
                    tmp = {
                        "desc": self.new_market_volatility[k].desc,
                        "name": k,
                        "value": v,
                        "detail": self.new_market_volatility[k].meta.get("detail", "")
                    }
                    tmp_list.append(tmp)
                ret[name].append(tmp_list)
        return ret

    def _get_all(self) -> Mapping[str, str]:
        pass

    def _get_one(self, name: str) -> Optional[str]:
        return self.cache().get_value(name)

    def _set_one(self, name: str, value: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            row = self.model(key=name)
            db.session.add(row)
        row.value = value
        row.status = self.model.Status.VALID
        db.session.commit()
        self.cache().set_value(name, value)

    def _del_one(self, name: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            return
        row.status = self.model.Status.DELETED
        db.session.commit()
        self.cache().del_value(name)


class _WithdrawalFuseConfig(BaseConfig):

    _supports_valid_intervals = True

    F = ConfigField

    withdrawal_fuse_usd_threshold = F(
        Decimal,
        "提现市值阈值",
        default=Decimal("2"),
        detail="当（最近24H提现市值/最近N日均值）≥提现市值阈值时，关闭全站提现并进行相关告警",
        )
    withdrawal_fuse_count_threshold = F(
        Decimal,
        "提现笔数阈值",
        default=Decimal("2"),
        detail="当（最近24H提现笔数/最近N日均值）≥提现笔数阈值，关闭全站提现并进行相关告警"
    )
    # 默认值是0, 如果不为0的话表示临时阈值是生效的
    tmp_withdrawal_fuse_usd_threshold = F(
        Decimal,
        "提现市值临时阈值",
        default=Decimal("0"),
        detail="触发限制后，可以通过设置该值来避免后续的重复限制，设置时需要同时设置失效时间，在失效时间之前，提现笔数阈值以该临时阈值为准",
        support_valid_intervals=True
    )
    # 默认值是0, 如果不为0的话表示临时阈值是生效的
    tmp_withdrawal_fuse_count_threshold = F(
        Decimal,
        "提现笔数临时阈值",
        default=Decimal("0"),
        detail="触发限制后，可以通过设置该值来避免后续的重复限制，设置时需要同时设置失效时间，在失效时间之前，提现笔数阈值以该临时阈值为准",
        support_valid_intervals=True
    )

    withdrawal_fuse_remark = F(
        str,
        "提现熔断机制备注",
        default="",
        detail="提现熔断机制备注",
    )

    @property
    def fields_and_values_json(self) -> List[Dict[str, Any]]:
        result = self._get_all()
        config_data = result[0]
        fields = []
        for name, _ in self._fields.items():
            field_json = self.get_field_and_value_json(name)
            field_json["config_value"] = config_data.get(name, field_json["default_value"])
            fields.append(field_json)
        return fields

    def get_valid_interval(self, name: str
                           ) -> Optional[Tuple[Optional[float],
                                               Optional[float]]]:

        r = WithdrawalFuseSetting.query.filter(
            WithdrawalFuseSetting.key == name,
            WithdrawalFuseSetting.status == WithdrawalFuseSetting.Status.VALID
        ).first()
        if not r:
            return None

        def to_timestamp(_t):
            return _t.timestamp() if isinstance(_t, datetime) else _t

        return [to_timestamp(r.valid_from), to_timestamp(r.valid_till)] \
            if r.valid_from and r.valid_till else None

    def _get_all(self) -> Tuple[Dict[str, str],
                                Dict[str, BaseConfig.TimeInterval]]:
        values = {}
        valid_intervals = {}
        cache = WithdrawalFuseSettingsCache()
        for name, data in cache.get_values().items():
            value, valid_from, valid_till \
                = cache.load_value_with_valid_interval(data)
            if value is None:
                continue
            values[name] = value
            valid_intervals[name] = valid_from, valid_till
        return values, valid_intervals

    def _get_one(self, name: str) -> Optional[str]:
        cache = WithdrawalFuseSettingsCache()
        value, valid_from, valid_till \
            = cache.load_value_with_valid_interval(cache.get_value(name))
        if value is None:
            return None
        _now = current_timestamp()
        if (valid_from is not None and valid_from > _now
                or valid_till is not None and valid_till <= _now):
            return None
        return value

    def _set_one(self, name: str, value: str):
        cache = WithdrawalFuseSettingsCache()
        if cache.load_value_with_valid_interval(cache.get_value(name)) \
                == (value, None, None):
            return
        model = WithdrawalFuseSetting
        row = model.query.filter(model.key == name).first()
        if not row:
            row = model(key=name)
            db.session.add(row)
        row.value = value
        row.status = model.Status.VALID
        row.valid_from = None
        row.valid_till = None
        db.session.commit()
        cache.set_value(
            name, cache.dump_value_with_valid_interval(value, None, None))

    def _del_one(self, name: str):
        model = WithdrawalFuseSetting
        row = model.query.filter(model.key == name).first()
        if not row:
            return
        row.status = model.Status.DELETED
        db.session.commit()
        WithdrawalFuseSettingsCache().del_value(name)

    def _set_one_with_valid_interval(self, name: str, value: str,
                                     valid_from: float = None,
                                     valid_till: float = None):
        model = WithdrawalFuseSetting
        row = model.query.filter(model.key == name).first()
        if not row:
            row = model(key=name)
            db.session.add(row)
        row.value = value
        row.valid_from = (timestamp_to_datetime(valid_from)
                          if valid_from is not None
                          else None),
        row.valid_till = (timestamp_to_datetime(valid_till)
                          if valid_till is not None
                          else None)
        row.status = model.Status.VALID
        db.session.commit()
        cache = WithdrawalFuseSettingsCache()
        cache.set_value(
            name,
            cache.dump_value_with_valid_interval(value, valid_from, valid_till)
        )


class _DepositFuseConfig(BaseConfig):

    _supports_valid_intervals = True

    F = ConfigField

    deposits_fuse_threshold = F(Decimal, "充值市值倍数阈值", default=Decimal(2),
                                detail="最近24H充值金额/最近7天平均金额≥该值时触发风控")

    tmp_deposits_fuse_threshold = F(
        Decimal,
        "充值市值倍数临时阈值",
        default=Decimal("0"),
        detail="触发限制后，可以通过设置该值来避免后续的重复限制，设置时需要同时设置失效时间，在失效时间之前，充值熔断市值倍数阈值以该临时阈值为准",
        support_valid_intervals=True
    )

    deposit_fuse_remark = F(
        str,
        "备注",
        default="",
        detail="可添加备注说明",
    )

    @property
    def fields_and_values_json(self) -> List[Dict[str, Any]]:
        result = self._get_all()
        config_data = result[0]
        fields = []
        for name, _ in self._fields.items():
            field_json = self.get_field_and_value_json(name)
            field_json["config_value"] = config_data.get(name, field_json["default_value"])
            fields.append(field_json)
        return fields

    def get_valid_interval(self, name: str
                           ) -> Optional[Tuple[Optional[float],
                                               Optional[float]]]:

        r = DepositFuseSetting.query.filter(
            DepositFuseSetting.key == name,
            DepositFuseSetting.status == DepositFuseSetting.Status.VALID
        ).first()
        if not r:
            return None

        def to_timestamp(_t):
            return _t.timestamp() if isinstance(_t, datetime) else _t

        return [to_timestamp(r.valid_from), to_timestamp(r.valid_till)] \
            if r.valid_from and r.valid_till else None

    def _get_all(self) -> Tuple[Dict[str, str],
                                Dict[str, BaseConfig.TimeInterval]]:
        values = {}
        valid_intervals = {}
        cache = DepositFuseSettingsCache()
        for name, data in cache.get_values().items():
            value, valid_from, valid_till \
                = cache.load_value_with_valid_interval(data)
            if value is None:
                continue
            values[name] = value
            valid_intervals[name] = valid_from, valid_till
        return values, valid_intervals

    def _get_one(self, name: str) -> Optional[str]:
        cache = DepositFuseSettingsCache()
        value, valid_from, valid_till \
            = cache.load_value_with_valid_interval(cache.get_value(name))
        if value is None:
            return None
        _now = current_timestamp()
        if (valid_from is not None and valid_from > _now
                or valid_till is not None and valid_till <= _now):
            return None
        return value

    def _set_one(self, name: str, value: str):
        cache = DepositFuseSettingsCache()
        if cache.load_value_with_valid_interval(cache.get_value(name)) \
                == (value, None, None):
            return
        model = DepositFuseSetting
        row = model.query.filter(model.key == name).first()
        if not row:
            row = model(key=name)
            db.session.add(row)
        row.value = value
        row.status = model.Status.VALID
        row.valid_from = None
        row.valid_till = None
        db.session.commit()
        cache.set_value(
            name, cache.dump_value_with_valid_interval(value, None, None))

    def _del_one(self, name: str):
        model = DepositFuseSetting
        row = model.query.filter(model.key == name).first()
        if not row:
            return
        row.status = model.Status.DELETED
        db.session.commit()
        DepositFuseSettingsCache().del_value(name)

    def _set_one_with_valid_interval(self, name: str, value: str,
                                     valid_from: float = None,
                                     valid_till: float = None):
        model = DepositFuseSetting
        row = model.query.filter(model.key == name).first()
        if not row:
            row = model(key=name)
            db.session.add(row)
        row.value = value
        row.valid_from = (timestamp_to_datetime(valid_from)
                          if valid_from is not None
                          else None),
        row.valid_till = (timestamp_to_datetime(valid_till)
                          if valid_till is not None
                          else None)
        row.status = model.Status.VALID
        db.session.commit()
        cache = DepositFuseSettingsCache()
        cache.set_value(
            name,
            cache.dump_value_with_valid_interval(value, valid_from, valid_till)
        )


WithdrawalFuseConfig = _WithdrawalFuseConfig(mode=ConfigMode.REAL_TIME)
DepositFuseConfig = _DepositFuseConfig(mode=ConfigMode.REAL_TIME)


class FailOption(Enum):
    """
    what to to when risk control check failed
    """
    DUMMY = 'dummy'  # just record fail result
    RETRY = 'retry'  # do nothing and retry after a while
    ENFORCE_POLICY = 'enforce_policy'  # enforce risk control policy

    def __json__(self):
        """
        used for celery json serializer
        """
        return self.name

    @classmethod
    def parse(cls, value):
        if isinstance(value, cls):
            return value
        for e in cls:
            if value == e.name or value == e.value:
                return e
        raise ValueError(f'{value} cannot convert to enum type {cls.__name__}')


DEPOSIT_CHECK_IGNORE_ASSETS = ('CET',)


def withdrawals_disabled_by_risk_control(user_id: int, cancel_withdrawal: bool = False) -> bool:
    balance_out_disabled = RiskUser.test(user_id, RiskUser.Permission.BALANCE_OUT_DISABLED)
    if cancel_withdrawal:   # 用户被限制提现时允许取消提现请求
        if balance_out_disabled:
            return False
    if balance_out_disabled:
        return True
    # 存在待检查的异常盈利风控项，不能进行提现
    record = UserCheckRequest.query.filter(
        UserCheckRequest.user_id == user_id,
        UserCheckRequest.business.in_(UserCheckRequest.__WITHDRAWAL_CHECK_BUSINESSES__),
        UserCheckRequest.status == UserCheckRequest.Status.CREATED
    ).first()
    if record:
        return True
    return False


def add_risk_user(user_id: int, reason: RiskUser.Reason, detail: str,
                  *, source: str = None, permissions: List[RiskUser.Permission] = None):
    _add_risk_user(user_id, reason, detail, source, permissions)
    row = SubAccount.query.filter(SubAccount.user_id == user_id).first()
    if row:
        _add_risk_user(row.main_user_id, reason, detail, source, permissions)


def batch_add_risk_user(user_ids: Iterable[int], reason: RiskUser.Reason, detail: str,
                        *, source: str = None, permissions: List[RiskUser.Permission] = None):
    # 封禁子账户权限同时也封禁主账户权限
    main_user_ids = SubAccount.query.with_entities(SubAccount.main_user_id).filter(
        SubAccount.user_id.in_(user_ids)).all()
    block_user_ids = set(user_ids) | set(x for x, in main_user_ids)
    for user_id in block_user_ids:
        _add_risk_user(user_id, reason, detail, source, permissions)


def _add_risk_user(user_id: int, reason: RiskUser.Reason, detail: str,
                   source: str = None, permissions: List[RiskUser.Permission] = None):
    if not user_id:
        # 合约保险基金账户是0
        return

    sub_record = SubAccount.query.filter(SubAccount.user_id == user_id).with_entities(SubAccount.main_user_id).first()
    main_user_id = sub_record.main_user_id if sub_record else user_id

    if UserPreventRiskControlConfig.check_ignore(main_user_id, reason, source):
        # 如果免打扰配置
        UserPreventRiskControlEvent.add_event(main_user_id, reason,
                                              dict(
                                                user_id=user_id,
                                                main_user_id=main_user_id,
                                                reason=reason.value,
                                                source=source,
                                                detail=detail,
                                                permissions=[_v.name for _v in permissions] if permissions else []
                                              ))
        # todo: clean this two docs
        return

    record = RiskUser.add(
        user_id=user_id,
        reason=reason,
        detail=detail,
        source=source
    )
    if permissions is None:
        permissions = RiskUser.PermissionMap.get(reason, [])  # reason to permission
    record.permissions = permissions
    biz_values = {source} if source else None
    for permission in permissions:
        _block_permission_dispatch(user_id, permission, biz_values)
    db.session_add_and_commit(record)


def unlock_risk_user_by_reason(reason: RiskUser.Reason, source: str = None, asset=None):
    # 目前仅限于对账平之后恢复之前被风控的用户
    if reason not in [
        RiskUser.Reason.MARGIN_LOAN_FLAT_CHECK,
        RiskUser.Reason.INVESTMENT_BALANCE_CHECK,
        RiskUser.Reason.RED_PACKET_CHECK,
    ]:
        raise InvalidArgument(message='reason invalid')
    # sql:1s
    risk_query = RiskUser.query.filter(
        RiskUser.reason == reason,
        RiskUser.source == source,
        RiskUser.status == RiskUser.Status.AUDIT_REQUIRED,
    ).all()
    now_ = now()

    if reason == RiskUser.Reason.MARGIN_LOAN_FLAT_CHECK:
        # 杠杆对账按币种区分
        risk_query = [i for i in risk_query if json.loads(i.detail).get('asset', '') == asset]

    for risk_users in batch_iter(risk_query, 100):
        for risk_user in risk_users:
            risk_user.remark = '系统对账风控自动解除'
            risk_user.audited_at = now_
            risk_user.status = RiskUser.Status.AUDITED
        db.session.commit()
        for risk_user in risk_users:
            permissions = {perm: {source} for perm in risk_user.permissions}
            try_unblock_permissions(risk_user.user_id, permissions)
    if len(risk_query):
        if reason == RiskUser.Reason.RED_PACKET_CHECK:
            msg = f'{source}红包对账已平，被风控用户已自动审核通过。'
        elif reason == RiskUser.Reason.INVESTMENT_BALANCE_CHECK:
            msg = f'{source}理财对账已平，被风控用户已自动审核通过。'
        else:
            msg = f'{source}市场{asset}杠杆对账已平，被风控用户已自动审核通过。'
        alert_risk_msg(msg)


def update_p2p_permission(user_id: int, value: bool):
    for permission in [
        UserSettings.p2p_sell_disabled,
        UserSettings.p2p_merchant_sell_accept_disabled,
        UserSettings.p2p_disabled_trans
    ]:
        update_user_p2p_permission_task.delay(user_id, permission.name, value)


def admin_block_permissions(user_id: int, permissions: dict[RiskUser.Permission, set[str]]):
    for permission in permissions:
        biz_values = permissions.get(permission)
        _block_permission_dispatch(user_id, permission, biz_values)


def _block_permission_dispatch(user_id, permission, biz_values: set[str] = None):
    if permission in RiskUser.PRI_PERMS:
        _block_permission(user_id, permission)
    elif permission in RiskUser.SEC_PERMS:
        assert biz_values
        _block_sec_permission(user_id, permission, biz_values)


def _block_permission(user_id, permission):
    user_setting = UserSettings(user_id)
    if permission == RiskUser.Permission.BALANCE_OUT_DISABLED:  # 禁止提现不设置权限，由风控控制提现审核
        user_setting.sub_account_transfer_disabled_by_risk_control = True
        user_setting.red_packet_disabled_by_risk_control = True
        update_p2p_permission(user_id, True)
    elif permission == RiskUser.Permission.TRANSFER_OUT_DISABLED:
        user_setting.perpetual_transfer_out_disabled_by_risk_control = True
        user_setting.margin_transfer_out_disabled_by_risk_control = True
    elif permission == RiskUser.Permission.TRADING_DISABLED:
        user_setting.trading_disabled_by_risk_control = True
    elif permission == RiskUser.Permission.MARGIN_LOAN_DISABLED:
        user_setting.margin_loan_disabled_by_risk_control = True
    elif permission == RiskUser.Permission.TRADING_LIMITED:
        accounts = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id == user_id,
            MarginLoanOrder.status == MarginLoanOrder.StatusType.PASS
        ).with_entities(
            MarginLoanOrder.account_id
        ).all()
        accounts = [x for x, in accounts] or [-1]  # there's no -1 margin account, it means no account
        user_setting.spot_trading_disabled_by_risk_control = True
        user_setting.perpetual_limited_by_risk_control = True
        user_setting.only_allowed_margin_accounts_by_risk_control.update(set(accounts))


def _block_sec_permission(user_id, permission, biz_values: set[str]):
    user_setting = UserSettings(user_id)
    if permission == RiskUser.Permission.ASSET_BALANCE_IN_DISABLED:
        # TODO：若以后有多个风控触发该权限，需考虑并发情况。参考：UserSettings.safe_add
        user_setting.balance_in_assets_disabled_by_risk_control.update(biz_values)


def try_unblock_permissions(user_id: int, permissions: dict[RiskUser.Permission, set[str]]):
    rows = RiskUser.query.filter(
        RiskUser.user_id == user_id,
        RiskUser.status.in_((RiskUser.Status.AUDIT_REQUIRED, RiskUser.Status.AUDIT_REJECTED))
    ).all()
    perm_to_sources = defaultdict(set)
    perms_set = set()
    for row in rows:
        perms = row.permissions
        for perm in perms:
            perm_to_sources[perm].add(row.source)
        perms_set.update(perms)

    for permission in permissions:
        if permission in RiskUser.PRI_PERMS:
            if permission not in perms_set:
                _unblock_permission(user_id, permission)
        elif permission in RiskUser.SEC_PERMS:
            biz_values = permissions.get(permission)
            assert biz_values
            source_set = perm_to_sources[permission]
            unblock_biz_values = biz_values - source_set
            _unblock_sec_permission(user_id, permission, unblock_biz_values)


def _unblock_permission(user_id, permission):
    user_setting = UserSettings(user_id)
    if permission == RiskUser.Permission.BALANCE_OUT_DISABLED:
        user_setting.sub_account_transfer_disabled_by_risk_control = False
        user_setting.red_packet_disabled_by_risk_control = False
        update_p2p_permission(user_id, False)
    elif permission == RiskUser.Permission.TRANSFER_OUT_DISABLED:
        user_setting.perpetual_transfer_out_disabled_by_risk_control = False
        user_setting.margin_transfer_out_disabled_by_risk_control = False
    elif permission == RiskUser.Permission.TRADING_DISABLED:
        user_setting.trading_disabled_by_risk_control = False
    elif permission == RiskUser.Permission.MARGIN_LOAN_DISABLED:
        user_setting.margin_loan_disabled_by_risk_control = False
    elif permission == RiskUser.Permission.TRADING_LIMITED:
        user_setting.spot_trading_disabled_by_risk_control = False
        user_setting.perpetual_limited_by_risk_control = False
        user_setting.only_allowed_margin_accounts_by_risk_control = []


def _unblock_sec_permission(user_id, permission, biz_values: set[str]):
    user_setting = UserSettings(user_id)
    if permission == RiskUser.Permission.ASSET_BALANCE_IN_DISABLED:
        user_setting.balance_in_assets_disabled_by_risk_control.difference_update(biz_values)


@celery_task
def process_permissions_task(op: Literal['block', 'unblock'], permissions: dict[int, dict[str, set]]):
    # convert name to enum
    m = RiskUser.Permission.__members__
    permissions = {k: {m[x]: y for x, y in v.items()} for k, v in permissions.items()}
    match op:
        case 'block':
            for user_id, pers in permissions.items():
                admin_block_permissions(user_id, pers)
        case 'unblock':
            for user_id, pers in permissions.items():
                try_unblock_permissions(user_id, pers)
        case _:
            raise ValueError


def alert_risk_msg(msg: str, url=None):
    current_app.logger.error(msg)
    contacts_url = url if url else config["ADMIN_CONTACTS"]["customer_service"]
    send_alert_notice(msg, contacts_url)


def get_trading_whitelist_user() -> Set[int]:
    """
    获取交易白名单用户
    """
    credit_user_ids = CreditUser.query.with_entities(CreditUser.user_id).filter(
        CreditUser.status == CreditUser.StatusType.PASS).all()
    makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
    sys_user_ids = {v for k, v in config.items() if k.endswith('USER_ID') and isinstance(v, int) and v > 0}
    user_ids = {x for x, in credit_user_ids} | set(makers) | sys_user_ids
    # 子账号也在白名单
    sub_accounts = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
    for sub_user_id, main_user_id in sub_accounts:
        if main_user_id in user_ids:
            user_ids.add(sub_user_id)
    return user_ids


def last_risk_event_log(market: str, reason: RiskUser.Reason | RiskEventLog.Reason):
    reason = RiskEventLog.trans_reason(reason)
    row = RiskEventLog.query.filter(
        RiskEventLog.source == market,
        RiskEventLog.reason == reason,
    ).order_by(
        RiskEventLog.id.desc()
    ).first()
    return row


def add_risk_event_log(source: str, reason: RiskUser.Reason | RiskEventLog.Reason,
                       start_time: int, end_time: int,
                       extra: dict, resume_time: int = 0,
                       status: RiskEventLog.Status = RiskEventLog.Status.NONE
                       ):
    reason = RiskEventLog.trans_reason(reason)
    row = RiskEventLog(
        source=source,
        start_time=timestamp_to_datetime(start_time),
        reason=reason,
        extra=json.dumps(extra, cls=JsonEncoder),
        status=status
    )
    if end_time:
        row.end_time = timestamp_to_datetime(end_time)
    if resume_time:
        row.resume_time = timestamp_to_datetime(resume_time)
    db.session_add_and_commit(row)

    if reason in [RiskEventLog.Reason.MARKET_VOLATILITY,
                  RiskEventLog.Reason.PERPETUAL_MARKET_VOLATILITY,
                  RiskEventLog.Reason.MARGIN_LIQUIDATION,
                  RiskEventLog.Reason.PERPETUAL_LIQUIDATION,
                  ]:
        format_risk_event_log.delay(row.id)


@celery_task()
def format_risk_event_log(log_id):
    def _get_max_min_price_by_kline(_client, _market, _start, _end):
        kline = _client.market_kline(
            market=_market, start_time=_start, end_time=_end, interval=60)
        _max_price = 0
        _min_price = 0
        if kline:
            _max_price = max(Decimal(x[3]) for x in kline)
            _min_price = min(Decimal(x[4]) for x in kline)
        return _max_price, _min_price

    row = RiskEventLog.query.get(log_id)

    if not row:
        current_app.logger.error(f"invalid event log:{log_id}")
        return
    extra = json.loads(row.extra)
    reason = row.reason
    market = row.source
    if reason in [RiskEventLog.Reason.MARKET_VOLATILITY, RiskEventLog.Reason.PERPETUAL_MARKET_VOLATILITY]:
        if reason == RiskEventLog.Reason.MARKET_VOLATILITY:
            client = ServerClient()
            result = client.trade_net_rank(market=[market],
                                           start_time=extra['start'],
                                           end_time=extra['end'])
        else:
            client = PerpetualServerClient()
            result = client.query_net_rank(market=[market],
                                           start_time=extra['start'],
                                           end_time=extra['end'])

        sell_list_rank = [dict(
            net=quantize_amount(r["net"], 8),
            total=quantize_amount(r['total'], 8),
            user_id=r['user_id']
        ) for r in result['sell'] if Decimal(
            r["net"]) > 0][:5]
        buy_list_rank = [dict(net=quantize_amount(r["net"], 8),
                              total=quantize_amount(r['total'], 8),
                              user_id=r['user_id']
                              ) for r in result['buy'] if Decimal(
            r["net"]) > 0][:5]
        extra['sell_list_rank'] = sell_list_rank
        extra['buy_list_rank'] = buy_list_rank

    elif reason == RiskEventLog.Reason.MARGIN_LIQUIDATION:
        client = ServerClient()
        start = extra['start']
        end = extra['end']
        account_id = extra['account_id']
        extra['max_price'], extra['min_price'] = _get_max_min_price_by_kline(client, market, start, end)

        liq_history = MarginLiquidationHistory.query.filter(
            MarginLiquidationHistory.account_id == account_id,
            MarginLiquidationHistory.created_at >= timestamp_to_datetime(extra['start']),
            MarginLiquidationHistory.created_at < timestamp_to_datetime(extra['end']),
            MarginLiquidationHistory.status == MarginLiquidationHistory.Status.FINISH,
        ).all()
        user_loan_value_dict = defaultdict(Decimal)
        price_map = PriceManager.assets_to_usd()
        for liq_his in liq_history:
            user_loan_value_dict[liq_his.user_id] += liq_his.loan_base_asset_amount * price_map.get(liq_his.base_asset,
                                                                                                    0)
            user_loan_value_dict[liq_his.user_id] += liq_his.insurance_base_asset_amount * price_map.get(
                liq_his.base_asset, 0)
            user_loan_value_dict[liq_his.user_id] += liq_his.loan_quote_asset_amount * price_map.get(
                liq_his.quote_asset, 0)
            user_loan_value_dict[liq_his.user_id] += liq_his.insurance_quote_asset_amount * price_map.get(
                liq_his.quote_asset, 0)
        user_list_rank = [dict(user_id=k, value=v) for k, v in
                          sorted(user_loan_value_dict.items(), key=lambda item: item[1], reverse=True)][:5]
        extra['user_list_rank'] = user_list_rank

    elif reason == RiskEventLog.Reason.PERPETUAL_LIQUIDATION:
        client = PerpetualServerClient()
        start = extra['start']
        end = extra['end']
        extra['max_price'], extra['min_price'] = _get_max_min_price_by_kline(client, market, start, end)
        records = PerpetualSysHistoryDB.table('insurance_history').select(
            "user_id, asset, SUM(`change`)",
            where=f"`market`='{market}' AND `type`=2 AND `time`>={start} AND `time`<{end}",
            group_by="user_id, asset")
        user_liq_value_dict = defaultdict(Decimal)
        price_map = PriceManager.assets_to_usd()
        price_map['USD'] = Decimal('1')
        for his in records:
            user_liq_value_dict[his[0]] += his[2] * price_map.get(his[1], 0)
        user_list_rank = [dict(user_id=k, value=v) for k, v in
                          sorted(user_liq_value_dict.items(), key=lambda item: item[1], reverse=True)][:10]
        extra['user_list_rank'] = user_list_rank

    row.extra = json.dumps(extra, cls=JsonEncoder)
    db.session_add_and_commit(row)


class WithdrawalCheckCondition:
    """提现只允许主账号"""

    @classmethod
    def check(cls, main_user_id: int):
        funcs = [
            cls.is_market_maker,
            cls.is_whitelist_user,
        ]
        for f in funcs:
            if f(main_user_id):
                return True
        return False

    @classmethod
    def is_market_maker(cls, main_user_id: int) -> bool:
        model = MarketMaker
        min_level = 4
        row = model.query.with_entities(model.user_id).filter(
            model.status == model.StatusType.PASS,
            model.user_id == main_user_id,
            model.level >= min_level
        ).first()
        if row:
            return True
        if main_user_id in MarketMakerHelper.list_inner_maker_ids(include_sub_account=False):
            return True
        return False

    @classmethod
    def is_whitelist_user(cls, user_id: int) -> bool:
        model = WithdrawalWhitelistUser
        row = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID
        ).first()
        if row:
            return True
        return False


class ImmediatelyWithdrawalCheckCondition(WithdrawalCheckCondition):

    @classmethod
    def check(cls, main_user_id: int) -> bool:
        # 逐个检查，提前返回
        funcs = [
            cls.is_market_maker,
            cls.is_whitelist_user,
            cls.is_perpetual_trade_user,
            cls.is_airdrop_user,
            cls.is_trade_rank_user,
            cls.is_dibs_user,
            cls.is_new_user_package_user,
            cls.is_staking_user,
            cls.is_strategy_trade_user,
            cls.is_amm_user,
            cls.is_invest_user,
            cls.is_pledge_user,
            cls.is_margin_loan_user,
        ]
        for f in funcs:
            if f(main_user_id):
                return True
        return False

    @classmethod
    def is_perpetual_trade_user(cls, main_user_id: int) -> bool:
        # 已与产品确认可以接受 T+1
        model = UserTradeSummary
        row = model.query.filter(
            model.user_id == main_user_id,
            model.system == model.System.PERPETUAL
        ).first()
        if row:
            return True
        return False

    @classmethod
    def is_airdrop_user(cls, main_user_id: int) -> bool:
        model = AirdropActivityRewardHistory
        ret = cls._to_bool(model, main_user_id)
        if not ret:
            model = AirdropActivityLotteryHistory
            ret = cls._to_bool(model, main_user_id)
        return ret

    @classmethod
    def is_trade_rank_user(cls, main_user_id: int) -> bool:
        model = TradeRankActivityJoinUser
        return cls._to_bool(model, main_user_id)

    @classmethod
    def is_dibs_user(cls, main_user_id: int) -> bool:
        model = DiscountActivityLotteryHistory
        return cls._to_bool(model, main_user_id)

    @classmethod
    def is_new_user_package_user(cls, main_user_id: int) -> bool:
        model = NoviceUserReport
        return cls._to_bool(model, main_user_id)

    @classmethod
    def is_staking_user(cls, main_user_id: int) -> bool:
        model = StakingUserSummary
        return cls._to_bool(model, main_user_id)

    @classmethod
    def is_strategy_trade_user(cls, main_user_id: int) -> bool:
        model = UserStrategy
        return cls._to_bool(model, main_user_id)

    @classmethod
    def is_amm_user(cls, main_user_id: int) -> bool:
        model = UserLiquidity
        sub_users = cls._get_sub_users(main_user_id)
        return cls._to_bool(model, main_user_id, sub_users=sub_users)

    @classmethod
    def is_invest_user(cls, main_user_id: int) -> bool:
        model = InvestmentBalanceHistory
        sub_users = cls._get_sub_users(main_user_id)
        return cls._to_bool(model, main_user_id, sub_users=sub_users)

    @classmethod
    def is_pledge_user(cls, main_user_id: int) -> bool:
        model = PledgePosition
        return cls._to_bool(model, main_user_id)

    @classmethod
    def is_margin_loan_user(cls, main_user_id: int) -> bool:
        model = MarginLoanOrder
        sub_users = cls._get_sub_users(main_user_id)
        return cls._to_bool(model, main_user_id, sub_users=sub_users)

    @classmethod
    def _to_bool(cls, model, main_user_id: int, sub_users: list | None = None) -> bool:
        user_ids = [main_user_id]
        if sub_users:
            user_ids.extend(list(sub_users))
        row = model.query.filter(
            model.user_id.in_(user_ids)
        ).first()
        if row:
            return True
        return False

    @classmethod
    def _get_sub_users(cls, main_user_id: int) -> list:
        model = SubAccount
        rows = model.query.with_entities(
            model.user_id
        ).filter(
            model.main_user_id == main_user_id
        ).all()
        return [row.user_id for row in rows]


class NewUserImmediatelyWithdrawalCheckCondition(WithdrawalCheckCondition):

    @classmethod
    def check(cls, main_user_id: int):
        funcs = [
            cls.is_market_maker,
            cls.is_whitelist_user,
            cls.is_referral_user,
            cls.has_channel,
        ]
        for f in funcs:
            if f(main_user_id):
                return True
        return False

    @classmethod
    def is_referral_user(cls, main_user_id: int) -> bool:
        model = ReferralHistory
        row = model.query.filter(
            model.referrer_id == main_user_id
        ).first()
        if row:
            return True
        return False

    @classmethod
    def has_channel(cls, main_user_id: int) -> bool:
        model = User
        row = model.query.with_entities(
            model.channel
        ).filter(
            model.id == main_user_id
        ).first()
        if row.channel:
            return True
        return False


class WithdrawalNoOnChainDepositCheckCondition(WithdrawalCheckCondition):
    pass


class WashSaleRiskConfig(BaseConfig):
    F = ConfigField
    GF = GroupConfigField
    model = RiskControlSetting
    cache = RiskControlSettingCache

    wash_sale = {
        "market_avg_daily_deal": F(Decimal, "日均成交市值", default=Decimal("1000"),
                                   detail='近N日日均成交市值低于X的市场入围监控'),
        "market_sale_count_period": F(int, "日均成交统计周期", default=5, detail='统计单市场近N日日均成交市值'),
        "price_fluctuation_abs_rate": F(Decimal, "价格波动绝对值", default=Decimal("40"),
                                           detail="某市场价格上下波动超过阈值时为异常波动"),
        "index_price_diff_rate": F(Decimal, "指数偏离阈值", default=Decimal("10"),
                                   detail="某市场价格偏离指数价格百分比大于阈值时入围监控"),
        "risk_deals_time_range": F(int, "排查时间范围(分钟)", default=30,
                                   detail="基于触发“入围特征”的时间向前推T分钟，排查次周期内的特征"),
        "deal_user_count_threshold": F(int, "成交人数", default=5,
                                       detail="周期内卖+买成交账号数排重后少于阈值的为异常"),
        "deal_amount_over_daily_avg_rate": F(Decimal, "成交超额阈值", default=Decimal('100'),
                                                 detail="P分钟内成交额超过日均成交市值（X）的R%"),
        "deal_price_abs_rate": F(Decimal, "异常价格范围", default=Decimal('10'),
                                 detail="异常价格对应价格范围范围内的订单为异常成交"),
        "time_range_deal_amount": F(Decimal, "异常价格范围内成交市值", default=Decimal("100"),
                                    detail="排查周期内异常价格范围内成交的市值"),
        "user_trade_amount_threshold": F(Decimal, "用户成交市值", default=Decimal("200"),
                                         detail="该市场买单和卖单周期内成交排名前W的用户，单用户成交额需大于G标记为异常用户"),
        "user_trade_rank": F(int, "用户成交排名", default=5,
                             detail="该市场买单和卖单周期内成交排名各前W的用户纳入排查")
        }

    default_item = [{k: v.default for k, v in wash_sale.items()}]

    wash_sale_conf = F(list, "现货市场防对敲", default=default_item)

    def __init__(self):
        super().__init__(ConfigMode.REAL_TIME)

    def format_desc(self):
        """
        {
            "wash_sale_conf": [
                [{"desc": "日均成交统计周期", "name": "market_sale_count_period", "val": 5, "detail": "统计单市场近N日日均成交市值"}]
                [{"desc": "日均成交市值", "name": "market_avg_daily_deal", "val": "1000",
                 "detail": "近N日日均成交市值低于X的市场入围监控"}]
            ]
        }
        """

        fields = self.fields()
        ret = defaultdict(list)
        for name, val in fields.items():
            data = getattr(self, name)
            if not data:
                data = val.default
            for item in data:
                tmp_list = []
                for k, v in item.items():
                    tmp = {
                        "desc": self.wash_sale[k].desc,
                        "name": k,
                        "value": v,
                        "detail": self.wash_sale[k].meta.get("detail", "")
                    }
                    tmp_list.append(tmp)
                ret[name].append(tmp_list)
        return ret

    def get_conf_list(self):
        data_lis = getattr(self, 'wash_sale_conf')
        return self.fmt_fields(data_lis)

    @staticmethod
    def fmt_fields(data_lis):
        decimal_fields = ('market_avg_daily_deal', 'price_fluctuation_abs_rate', 'index_price_diff_rate',
                          'deal_amount_over_daily_avg_rate', 'deal_price_abs_rate', 'time_range_deal_amount',
                          'user_trade_amount_threshold')
        for rec in data_lis:
            for field, val in rec.items():
                if field in decimal_fields:
                    d_val = Decimal(val)
                    rec[field] = d_val
        return data_lis

    def _get_all(self) -> Mapping[str, str]:
        pass

    def _get_one(self, name: str) -> Optional[str]:
        return self.cache().get_value(name)

    def _set_one(self, name: str, value: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            row = self.model(key=name)
            db.session.add(row)
        row.value = value
        row.status = self.model.Status.VALID
        db.session.commit()
        self.cache().set_value(name, value)

    def _del_one(self, name: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            return
        row.status = self.model.Status.DELETED
        db.session.commit()
        self.cache().del_value(name)


def get_deposit_withdrawal_rc_whitelist() -> set[int]:
    """充提风控白名单"""
    from app.business.market_maker import MarketMakerHelper

    mm_min_level = 4
    outer_makers = MarketMaker.query.with_entities(
        MarketMaker.user_id
    ).filter(
        MarketMaker.status == MarketMaker.StatusType.PASS,
        MarketMaker.level >= mm_min_level
    ).all()
    outer_maker_uids = {x.user_id for x in outer_makers}

    inner_maker_uids = set(MarketMakerHelper.list_inner_maker_ids())
    model = WithdrawalWhitelistUser
    rows = model.query.with_entities(
        model.user_id
    ).filter(
        model.status == model.Status.VALID,
    ).all()
    whitelist = {row.user_id for row in rows}
    sub_users = SubAccount.query.filter(
        SubAccount.main_user_id.in_(whitelist),
        SubAccount.type == SubAccount.Type.NORMAL,
    ).with_entities(SubAccount.user_id).all()
    sub_uids = {x for x, in sub_users}
    whitelist |= sub_uids
    whitelist |= outer_maker_uids | inner_maker_uids
    return whitelist


def get_user_setting_risk_query():
    model = UserSetting
    now_ = now()
    margin_field = UserSettings.margin_loan_enabled
    query = model.query.filter(
        or_(model.valid_till.is_(None), model.valid_till > now_),  # 生效中的
        or_(
            # 被风控的设置项
            and_(model.key == margin_field.name, model.value == margin_field.db_value(False)),
            and_(
                and_(model.key.notin_([
                    margin_field.name,
                    UserSettings.daily_withdrawal_limit.name,
                    UserSettings.withdrawal_limit_30_days.name,
                ])),
                or_(model.value == margin_field.db_value(True), func.char_length(model.value) > 2),
            )
        ),
    )
    return query
