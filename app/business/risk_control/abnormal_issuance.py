import json
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal

from app import config
from app.assets.asset import try_get_asset_chain_config
from app.business import WalletClient, PriceManager, send_alert_notice, lock_call
from app.business.asset import AssetAlertHelper
from app.business.credit import list_credit_users
from app.business.market_maker import MarketMakerHelper
from app.business.risk_control.base import RiskControlGroupConfig, batch_add_risk_user, add_risk_event_log
from app.business.utils import yield_query_records_by_time_range
from app.caches.kline import AssetRankCache
from app.caches.operation import AssetAbnormalIssuanceCache
from app.common import PrecisionEnum
from app.models import Deposit, AssetAbnormalIssuanceConfig, RiskUser, RiskEventLog
from app.utils import format_percent, amount_to_str, timestamp_to_datetime, celery_task, now, current_timestamp


@celery_task
@lock_call(with_args=True)
def check_abnormal_issuance(data: str = None):
    """ 币种异常增发监控 """

    def _get_asset_ranks():
        return AssetRankCache('circulation_usd').read_assets()

    def _get_last_asset_deposit(_asset: str, _chain: str, _minutes: int) -> dict:
        ret = defaultdict(Decimal)
        start_time = create_time - timedelta(minutes=_minutes)
        _asset_user_data = defaultdict(lambda: defaultdict(Decimal))
        for row in yield_query_records_by_time_range(
                table=Deposit, start_time=start_time, end_time=now(),
                select_fields=(
                        Deposit.user_id,
                        Deposit.type,
                        Deposit.asset,
                        Deposit.chain,
                        Deposit.amount,
                        Deposit.status)
        ):
            if row.type == Deposit.Type.LOCAL:
                continue
            if row.status not in [
                Deposit.Status.CONFIRMING,
                Deposit.Status.FINISHED,
                Deposit.Status.TO_HOT,
            ]:
                continue
            if row.asset != _asset:
                continue
            if row.chain != _chain:
                continue
            if row.user_id in whitelist:
                continue
            usd = abs(prices.get(row.asset, 0) * row.amount)
            ret[row.user_id] += usd
        return ret

    def _get_asset_thresholds() -> list[dict]:
        model = AssetAbnormalIssuanceConfig
        rows = model.query.order_by(model.rank_max.asc()).all()
        ret = []
        for row in rows:
            if row.issuance_threshold is None:
                continue
            if row.issuance_usd_threshold is None:
                continue
            ret.append(
                {
                    'rank_min': row.rank_min,
                    'rank_max': row.rank_max,
                    'issuance_threshold': row.issuance_threshold,
                    'issuance_usd_threshold': row.issuance_usd_threshold,
                }
            )
        return ret

    def _get_exclude_assets() -> set:
        """请注意：针对增发市值的监控，剔除稳定币USDT、USDC、DAI，增发币数监控仍包含全部币种"""
        return {'USDT', 'USDC', 'DAI'}

    def _calculate_threshold(_asset):
        if _asset not in asset_ranks:
            return
        rank = asset_ranks.index(_asset) + 1
        thresholds.sort(key=lambda d: d['rank_max'])
        for idx, _threshold in enumerate(thresholds, start=1):
            if _threshold['rank_min'] <= rank <= _threshold['rank_max']:
                return _threshold, f'{idx}档'
        return

    if not data:
        assets_data = WalletClient().get_abnormal_issuance_assets()
    else:
        assets_data = [json.loads(data)]
    if not assets_data:
        return
    prices = PriceManager.assets_to_usd()
    whitelist = get_whitelist_users()
    risk_cfg = RiskControlGroupConfig().abnormal_issuance
    asset_ranks = _get_asset_ranks()
    thresholds = _get_asset_thresholds()
    for item in assets_data:
        create_time = timestamp_to_datetime(item['updated_at'])
        asset, chain = item['asset'], item['chain']
        cur_issue_amount, _ = Decimal(item['cur_amount']), Decimal(item['his_amount'])
        maintain_cache = AssetAbnormalIssuanceCache(asset, chain)
        maintain_cache.update(cur_issue_amount)
        his_issue_amount = maintain_cache.read_his_amount()
        r = _calculate_threshold(asset)
        if not r:
            continue
        threshold, rank_desc = r
        delta_amount = cur_issue_amount - his_issue_amount
        delta_amount_usd = prices.get(asset, Decimal('1')) * delta_amount
        delta_ratio = delta_amount / his_issue_amount if his_issue_amount else 0
        if (
                delta_ratio >= threshold['issuance_threshold']
                or
                (
                        delta_amount_usd >= threshold['issuance_usd_threshold']
                        and asset not in _get_exclude_assets()
                )
        ):
            cfg = try_get_asset_chain_config(asset, chain)
            if not cfg:
                continue
            if cfg.deposits_disabled_by_rc_abnormal and cfg.withdrawals_disabled_by_rc_abnormal:
                # 产品要求若充提开关都关闭，则无需风控及告警
                continue
            old_d_value = cfg.deposits_disabled_by_rc_abnormal
            old_w_value = cfg.withdrawals_disabled_by_rc_abnormal
            cfg.deposits_disabled_by_rc_abnormal = True
            cfg.withdrawals_disabled_by_rc_abnormal = True
            AssetAlertHelper.deposit_withdrawal_risk_alert(
                asset, chain, 'deposits_disabled_by_rc_abnormal',
                old_d_value, True, '触发币种异常增发'
            )
            AssetAlertHelper.deposit_withdrawal_risk_alert(
                asset, chain, 'withdrawals_disabled_by_rc_abnormal',
                old_w_value, True, '触发币种异常增发'
            )
            user_deposits = _get_last_asset_deposit(asset, chain, risk_cfg['ai_delta_minutes'])
            risk_users = []
            for uid, deposit_usd in user_deposits.items():
                if deposit_usd <= risk_cfg['ai_accumulated_deposit_usd']:
                    continue
                risk_users.append(dict(user_id=uid, value=deposit_usd))
            detail = f'''
            {asset}-{chain} 发行量告警 ({rank_desc}):
    历史发行量: {amount_to_str(his_issue_amount)}
    当前发行量: {amount_to_str(cur_issue_amount)}
    增量: {format_percent(delta_ratio, 2)}
    增发市值：{amount_to_str(delta_amount_usd, 2)} USD
    充提已关闭
    被风控用户数：{len(risk_users)}'''
            send_alert_notice(
                detail,
                config["ADMIN_CONTACTS"].get("asset_abnormal_issuance"),
            )
            batch_add_risk_user(
                [x['user_id'] for x in risk_users],
                RiskUser.Reason.ABNORMAL_ISSUANCE,
                detail,
                source=asset
            )
            extra = dict(
                asset=asset,
                chain=chain,
                cur_issue_amount=cur_issue_amount,
                his_issue_amount=his_issue_amount,
                delta_amount_usd=amount_to_str(delta_amount_usd, PrecisionEnum.CASH_PLACES),
                user_list_rank=list(sorted(risk_users, key=lambda x: x['value'], reverse=True))[:10],
                user_count=len(risk_users),
                rank_desc=rank_desc,
            )
            add_risk_event_log(
                asset,
                RiskEventLog.Reason.ABNORMAL_ISSUANCE,
                current_timestamp(to_int=True),
                current_timestamp(to_int=True),
                extra,
                0
            )
            maintain_cache.remove_his_amount(cur_issue_amount)


def get_whitelist_users() -> set[int]:
    maker_ids_with_sub_users = MarketMakerHelper.list_all_maker_ids()
    credit_users = list_credit_users()
    whitelist = set(maker_ids_with_sub_users) | credit_users
    return whitelist
