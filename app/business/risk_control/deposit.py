#!/usr/bin/env python3
from collections import defaultdict
from datetime import date, datetime, timedelta
from decimal import Decimal

from ...caches.system import AssetChangeEventCache
from ...models import (
    Deposit, AssetPrice, CoinInformation,
)
from ...utils import amount_to_str
from ...utils.date_ import today, date_to_datetime
from ..utils import yield_query_records_by_time_range
from .base import get_deposit_withdrawal_rc_whitelist


def get_temp_assets() -> list[str]:
    return AssetChangeEventCache().get_assets()


def is_temp_asset(asset: str, tmp_exclude_assets: list[str]) -> bool:
    # 过滤掉改名中的币种导致的风控告警
    return asset in tmp_exclude_assets


class AccumulatedDepositHelper:

    def __init__(self):
        self._date_asset_prices_map = {}

    def get_last_n_day_avg_data(self, days: int = 7, min_usd: Decimal = 1000) -> dict[str, dict]:
        """
        查询最近N日均值：
        （1）如果当天充值市值＜1000USD（为0也算低于1000刀）则不纳入统计；
        （2）计算7日均值时往前追溯数据，有符合要求（充值市值≥1000USD）的数据才算进去；
        （3）如果追溯到上币那天还没有7天符合要求的数据，则有几天数据则除几；
        （4）如果连1天数据都没有，则该币种无7日均值数据，不进行监控；
        """
        max_days = days * 2  # 最多往前追溯1倍的天数
        end_date = today()
        start_date = end_date - timedelta(days=max_days)
        start_time = date_to_datetime(start_date)
        end_time = date_to_datetime(end_date)

        rows = yield_query_records_by_time_range(
            table=Deposit, start_time=start_time, end_time=end_time,
            select_fields=(
                Deposit.id,
                Deposit.type,
                Deposit.user_id,
                Deposit.asset,
                Deposit.amount,
                Deposit.status,
                Deposit.created_at,
            ),
        )
        assets = self.get_assets()
        new_assets = self.get_new_assets(start_time)
        date_asset_data_map = defaultdict(lambda: defaultdict(lambda: {
            'count': 0,
            'amount': Decimal(),
        }))
        whitelist = self.get_whitelist_users()
        for row in rows:  # rows id倒叙
            if row.type == Deposit.Type.LOCAL:
                continue
            if row.status == Deposit.Status.CANCELLED:
                continue
            if row.user_id in whitelist:
                continue
            if row.asset not in assets:
                continue

            create_dt = row.created_at.date()
            online_time = new_assets.get(row.asset)
            if online_time:  # 新币上线时间，在某一天当中情况处理
                get_date = (online_time + timedelta(days=1)).date()
                if get_date >= end_date:
                    continue
                if create_dt < get_date:
                    continue
            date_asset_data_map[create_dt][row.asset]['count'] += 1
            date_asset_data_map[create_dt][row.asset]['amount'] += row.amount
            if self.check_last_n_day_data(date_asset_data_map, days, min_usd):
                break

        asset_dates_map = self.get_asset_dates_map(date_asset_data_map, min_usd)
        ret = {}
        for asset, dates in asset_dates_map.items():
            last_dates = list(sorted(dates, reverse=True))[:days]  # 取最新的N天
            if not last_dates:
                continue
            values = [date_asset_data_map[dt][asset] for dt in last_dates]
            denominator = len(values)
            total_count = sum([i['count'] for i in values])
            total_amount = sum([i['amount'] for i in values])
            if not total_count or not total_amount or not denominator:
                # 一天的数据都没有，不计算均值，也不会监控
                continue
            last_7d_avg_count = total_count / denominator
            last_7d_avg_amount = total_amount / denominator
            avg_data = dict(
                last_7d_avg_count=amount_to_str(last_7d_avg_count),
                last_7d_avg_amount=amount_to_str(last_7d_avg_amount),
            )
            ret[asset] = avg_data
        return ret

    @classmethod
    def get_whitelist_users(cls) -> set[int]:
        return get_deposit_withdrawal_rc_whitelist()

    @classmethod
    def get_assets(cls) -> set:
        model = CoinInformation
        rows = model.query.with_entities(
            model.code,
        ).filter(
            model.status == model.Status.VALID,
        ).all()
        return {row.code for row in rows}

    @classmethod
    def get_new_assets(cls, start_time) -> dict[str, datetime]:
        model = CoinInformation
        rows = model.query.with_entities(
            model.code,
            model.online_time,
        ).filter(
            model.status == model.Status.VALID,
            model.online_time > start_time,
        ).all()
        return {row.code: row.online_time for row in rows}

    def check_last_n_day_data(self, date_asset_data_map: dict[date, dict], days: int, min_usd: Decimal) -> bool:
        if len(date_asset_data_map) < days:
            return False
        asset_dates_map = self.get_asset_dates_map(date_asset_data_map, min_usd)
        date_counts = [len(i) for i in asset_dates_map.values()]
        if not date_counts:
            return False
        return min(date_counts) >= days and len(asset_dates_map) == len(date_asset_data_map)

    def get_asset_dates_map(self, date_asset_data_map: dict[date, dict], min_usd: Decimal) -> dict[str, set]:
        asset_dates_map = defaultdict(set)  # 币种满足最小市值的天数列表
        for dt, asset_data_map in date_asset_data_map.items():
            asset_prices = self.get_date_asset_prices(dt)
            for asset, data in asset_data_map.items():
                usd = data['amount'] * asset_prices.get(asset, 0)
                if usd >= min_usd:
                    asset_dates_map[asset].add(dt)
        return asset_dates_map

    def get_date_asset_prices(self, dt: date) -> dict[str, Decimal]:
        if not (v := self._date_asset_prices_map.get(dt)):
            self._date_asset_prices_map[dt] = v = AssetPrice.get_close_price_map(dt)
        return v

    @classmethod
    def get_asset_circulations(cls) -> dict:
        model = CoinInformation
        rows = model.query.with_entities(
            model.code,
            model.circulation,
        ).filter(
            model.status == model.Status.VALID,
        ).all()
        return dict(rows)
