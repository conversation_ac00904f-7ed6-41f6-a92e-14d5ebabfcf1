# -*- coding: utf-8 -*-
import json
from datetime import timed<PERSON><PERSON>
from collections import defaultdict

from flask_babel import gettext, force_locale
from typing import Any, List, Optional, Dict, Iterable

from flask import current_app
from app.models.base import db

from app.models.operation import AppAutoPushHistory, ChannelRewardActivity, ChannelRewardHistory, TriggerPriceInterval
from .push_base import PushBusinessHandler

from .. import Language, config
from .clients.biz_monitor import biz_monitor
from .user import UserPreferences
from app.business.clients.server import ServerClient
from app.common import CeleryQueues, PerpetualMarketType, WebPushChannelType, NoticePushType, WebPushMessageType, \
    BalanceBusiness, IncreaseEvent, AppPushEvent, MessageContent, MessageTitle, MessageWebLink, PrecisionEnum
from app.utils import (batch_iter, celery_task,
                       BaseHTTPClient, route_module_to_celery_queue, datetime_to_str, now,
                       MobilePusher, current_timestamp)
from app.utils.push import App<PERSON>age<PERSON><PERSON>, WebPagePath, PushType, PushTag
from ..caches import PerpetualMarketCache, MarketCache
from ..caches.push import SpotOrderNormalDealLimitCache, PerpetualOrderNormalDealLimitCache, \
    SpotStopOrderLimitCache, PerpetualStopOrderLimitCache, AppSubscriptionNoticeLimitCache, \
    UserPreference as UserPreferenceModel, \
    AppMarketPriceNoticeIntervalLimitCache
from ..common.push import AppPushBusiness
from ..exceptions import InvalidArgument
from ..models import MarketPriceNotice, Deposit, User, Withdrawal, Message, MessagePush
from ..models.auto_invest import AutoInvestPlanNoticeConfig
from ..models.exchange import AssetExchangeOrder, AssetExchangeOrderTransferHistory
from app.utils import amount_to_str
from ..utils.amount import calculate_ratio

route_module_to_celery_queue(__name__, CeleryQueues.PUSH)


def get_user_app_lang(user_id: int) -> Language:
    pref = UserPreferences(user_id)
    if lang := pref.app_language:
        return lang
    elif lang := pref.web_language:
        return lang
    else:
        return pref.language


def get_user_web_lang(user_id: int) -> Language:
    pref = UserPreferences(user_id)
    if lang := pref.web_language:
        return lang
    elif lang := pref.app_language:
        return lang
    else:
        return pref.language


def batch_get_user_push_lang_map(user_ids: set[int], is_app: bool) -> dict[int, Language]:
    user_app_lang_map = {}
    user_web_lang_map = {}
    user_common_lang_map = {}
    lang_key_container_map = {
        UserPreferences.app_language.name: user_app_lang_map,
        UserPreferences.web_language.name: user_web_lang_map,
        UserPreferences.language.name: user_common_lang_map
    }
    lang_keys = list(lang_key_container_map)
    for ch_ids in batch_iter(user_ids, 2000):
        ch_rows = UserPreferenceModel.query.filter(
            UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
            UserPreferenceModel.user_id.in_(ch_ids),
            UserPreferenceModel.key.in_(lang_keys),
        ).with_entities(
            UserPreferenceModel.user_id,
            UserPreferenceModel.key,
            UserPreferenceModel.value,
        ).all()
        for r in ch_rows:
            container = lang_key_container_map[r.key]
            container[r.user_id] = r.value

    res = {}
    for uid in user_ids:
        if is_app:
            lang = user_app_lang_map.get(uid) or user_web_lang_map.get(uid)
        else:
            lang = user_web_lang_map.get(uid) or user_app_lang_map.get(uid)
        lang = lang or user_common_lang_map.get(uid) or Language.DEFAULT
        if isinstance(lang, str):
            lang = Language[lang]
        res[uid] = lang
    return res


def get_market_business_name(market):
    business_name = market
    if MarketCache(market).exists():  # 只检查上架市场
        market_info = MarketCache(market).dict
        business_name = f"{market_info['base_asset']}/{market_info['quote_asset']}"
    return business_name


def limit_app_subscribe_notice(user_id):
    handler = PushBusinessHandler(None)
    notice_cache = AppSubscriptionNoticeLimitCache(user_id)

    if handler.can_push([user_id]) and notice_cache.can_push():
        handler.set_pushed([user_id])
        notice_cache.set_pushed()
        return False
    return True


def send_mobile_push_to_user_ids(user_ids: str | List[str], message: str,
                                 title: str = '', url: str = '',
                                 ttl: Optional[int] = None,
                                 uniq_key: str = '',
                                 extras: dict = None,
                                 created_at: int = None,
                                 ) -> None | dict:
    from . import monitor_wrap
    p = MobilePusher()
    if not isinstance(user_ids, list):
        user_ids = [user_ids]
    new_title = config["PUSH_PREFIX"] + title

    monitored_send_mobile_push_by_user_ids = monitor_wrap(
        p.send_mobile_push_by_user_ids,
        "send_mobile_push_by_user_ids",
        "web",
        labels={"valid_user_ids_len": len(user_ids)},
    )

    with monitored_send_mobile_push_by_user_ids(
        user_ids, message, new_title, url, ttl, uniq_key, extras, created_at=created_at
    ) as ret:
        pass
    return ret


def send_mobile_push_to_tags(tags: str | List[str], message: str,
                             business_push_id: Any, platform: str = '',
                             title: str = '', url: str = '',
                             ttl: Optional[int] = None,
                             extras: dict = None,
                             created_at: int = None) -> None | dict:
    from . import monitor_wrap
    p = MobilePusher()
    if not isinstance(tags, list):
        tags = [tags]
    new_title = config["PUSH_PREFIX"] + title
    monitored_send_mobile_push_by_tags = monitor_wrap(
        p.send_mobile_push_by_tags,
        "send_mobile_push_by_tags",
        "web",
        labels={"tags": tags},
    )

    with monitored_send_mobile_push_by_tags(
        tags, message, business_push_id, platform, new_title, url, ttl, extras=extras, created_at=created_at
    ) as ret:
        pass
    return ret


@celery_task
def send_mobile_push_by_lang(lang: str,
                             title: str,
                             content: str,
                             business_push_id: Any,
                             url: str = '',
                             ttl: Optional[int] = None,
                             extras: dict = None,
                             created_at: int = None,
                             has_checked_push_limit: bool = False):
    """
    business_push_id用于生成推送报告，可以是任何唯一标识
    """
    if not has_checked_push_limit:
        raise InvalidArgument(message='未能校验及设置广播消息推送数量限制！')
    try:
        send_mobile_push_to_tags(lang, content, business_push_id,
                                 title=title, url=url, ttl=ttl, extras=extras,
                                 created_at=created_at or current_timestamp(to_int=True))
    except BaseHTTPClient.BadResponse as e:
        current_app.logger.info(f"send mobile push failed, {title}, {e}")


def get_url_users_mapping(url: str, user_ids: Iterable[int]):
    site_url = config['SITE_URL']
    if not url.startswith(site_url):
        return {url: user_ids}

    user_host_url_mapping = {}
    for u_ids in batch_iter(user_ids, 2000):
        query = UserPreferenceModel.query.filter(
            UserPreferenceModel.key == 'host_url',
            UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
            UserPreferenceModel.user_id.in_(u_ids)
        ).with_entities(
            UserPreferenceModel.user_id,
            UserPreferenceModel.value
        ).all()
        for v in query:
            user_host_url_mapping[v.user_id] = v.value

    url_users_mapping = defaultdict(set)
    for u_id in user_ids:
        user_host_url = user_host_url_mapping.get(u_id, site_url)
        replaced_url = url.replace(site_url, user_host_url, 1)
        url_users_mapping[replaced_url].add(u_id)
    return url_users_mapping


@celery_task
def send_mobile_push_by_user_ids(user_ids: List[int],
                                 title: str,
                                 content: str,
                                 url: str = '',
                                 ttl: Optional[int] = None,
                                 extras: Dict = None,
                                 created_at: int = None,
                                 ):
    url_users_mapping = get_url_users_mapping(url, user_ids)
    for r_url, u_ids in url_users_mapping.items():
        for ids in batch_iter(u_ids, 2000):
            try:
                send_mobile_push_to_user_ids(
                    [str(v) for v in ids], content, title, url=r_url, ttl=ttl, extras=extras,
                    created_at=created_at or current_timestamp(to_int=True),
                )
            except BaseHTTPClient.BadResponse as e:  # ignore aliase not exists error
                current_app.logger.info(f"send mobile push failed, {title}, {e}")


def auto_push_read_extras(event: IncreaseEvent) -> Dict:
    extras = {'push_type': PushType.AUTO_PUSH.name,
              'business_push_id': event.value
              }
    return extras


@celery_task
def send_price_and_rate_alter_push(notice_id, open_price, last_price, created_at: int = None):
    model = MarketPriceNotice
    notice = model.query.get(notice_id)
    user_id = notice.user_id
    business = AppPushBusiness.MarketPriceSubscribe
    handler = PushBusinessHandler(business)
    if not handler.can_push([user_id]):
        return
    trade_type, market = notice.trade_type, notice.market
    interval_cache = AppMarketPriceNoticeIntervalLimitCache(notice_id)
    # 产品要求 always 类型限制24小时内最多推送10次
    if notice.ttl_type == model.TTLType.ALWAYS:
        if not interval_cache.can_push():
            return

    if trade_type == model.TradeType.SPOT:
        extras = auto_push_read_extras(IncreaseEvent.READ_PRICE_NOTICE_SPOT_COUNT)
        biz_monitor.increase_counter(AppPushEvent.PUSH_PRICE_NOTICE_SPOT_COUNT)
        url = AppPagePath.SPOT_MARKET_DETAIL.value.format(market=market)
        info = MarketCache(market).dict
        asset = info['base_asset']
    else:
        extras = auto_push_read_extras(IncreaseEvent.READ_PRICE_NOTICE_PERPETUAL_COUNT)
        biz_monitor.increase_counter(AppPushEvent.PUSH_PRICE_NOTICE_PERPETUAL_COUNT)
        url = AppPagePath.PERPETUAL_MARKET_DETAIL.value.format(market=market)
        infos = PerpetualMarketCache().read_aside()
        asset = infos[market]['stock']

    lang = get_user_app_lang(user_id).value
    with force_locale(lang):
        rate = amount_to_str(calculate_ratio(open_price, last_price) * 100, 2)
        if notice.rule == model.RuleType.PRICE_RISE:
            content = gettext("%(asset)s价格上涨至%(price)s。去查看>>", asset=asset, price=last_price)
        elif notice.rule == model.RuleType.PRICE_FALL:
            content = gettext("%(asset)s价格下跌至%(price)s。去查看>>", asset=asset, price=last_price)
        elif notice.rule == model.RuleType.RATE_RISE:
            content = gettext(
                "%(asset)s日涨幅达%(rate)s%%，最新价格：%(price)s。去查看>>",
                asset=asset, price=last_price, rate=rate
            )
        else:
            content = gettext(
                "%(asset)s日跌幅达%(rate)s%%，最新价格：%(price)s。去查看>>",
                asset=asset, price=last_price, rate=rate
            )
        title = gettext("订阅价格提醒")

    if notice.ttl_type == model.TTLType.ONCE:
        notice.state = model.State.CLOSE
    elif notice.ttl_type == model.TTLType.DAILY:
        notice.noticed_at = now()
    send_mobile_push_by_user_ids([user_id], title, content, url, ttl=600, extras=extras,
                                 created_at=created_at)
    db.session_add_and_commit(notice)
    handler.set_pushed([user_id])
    if notice.ttl_type == model.TTLType.ALWAYS:
        interval_cache.set_pushed()
    biz_monitor.increase_counter(AppPushEvent.PUSH_PRICE_NOTICE_ALL_COUNT)


@celery_task
def send_popular_market_price_broadcast(asset: str, market: str,
                                        direction: str, price: str,
                                        rate: str, ttl: int, extra: Dict, created_at: int = None):
    zh_title = zh_content = ''
    history = AppAutoPushHistory(
                key=asset,
                type=AppAutoPushHistory.Type.RISE_FALL,
                title=zh_title,
                content=zh_content,
                extra=json.dumps(extra),
            )
    db.session_add_and_commit(history)
    for lang in Language:
        with force_locale(lang.value):
            market_name = get_market_business_name(market)
            if direction == MarketPriceNotice.Direction.RISE.name:
                title = gettext("%(asset)s 5分钟内上涨%(rate)s%%", asset=asset, rate=rate)
                content = gettext("%(market)s最新价格：%(price)s，上涨%(rate)s%%。请密切关注行情走势。", market=market_name, price=price,
                                  rate=rate)
            else:
                title = gettext("%(asset)s 5分钟内下跌%(rate)s%%", asset=asset, rate=rate)
                content = gettext("%(market)s最新价格：%(price)s，下跌%(rate)s%%。请密切关注行情走势。", market=market_name, price=price,
                                  rate=rate)
            url = AppPagePath.SPOT_MARKET_DETAIL.value.format(market=market)
            send_mobile_push_by_lang(lang.value,
                                     title,
                                     content,
                                     history.id,
                                     url,
                                     ttl,
                                     extras=dict(business_push_id=history.id,
                                                 push_type=PushType.BROADCAST.name),
                                     created_at=created_at)
            if lang == Language.ZH_HANS_CN:
                zh_title, zh_content = title, content
    history = AppAutoPushHistory.query.get(history.id)
    history.title = zh_title
    history.content = zh_content
    db.session.commit()


@celery_task
def send_popular_market_level_broadcast(asset: str, direction: str,
                                        price: str, market: str,
                                        level: str, ttl: int, extra: Dict, created_at: int = None):
    zh_title = zh_content = ''
    history = AppAutoPushHistory(
                key=asset,
                type=AppAutoPushHistory.Type.BREAKTHROUGH,
                title=zh_title,
                content=zh_content,
                extra=json.dumps(extra),
            )
    db.session_add_and_commit(history)
    url = AppPagePath.SPOT_MARKET_DETAIL.value.format(market=market)
    for lang in Language:
        with force_locale(lang.value):
            if direction == MarketPriceNotice.Direction.RISE.name:
                title = gettext(
                    "%(asset)s突破%(level)sUSDT📈", asset=asset, level=level
                )
                content = gettext(
                    "%(asset)s最新价格：%(price)sUSDT。去查看>>", asset=asset, price=price
                )
            else:
                title = gettext(
                    "%(asset)s跌破%(level)sUSDT📉", asset=asset, level=level
                )
                content = gettext(
                    "%(asset)s最新价格：%(price)sUSDT。去查看>>", asset=asset, price=price
                )
            tag_name = '{}_{}'.format(PushTag.ASSET_PRICE_BREAK.value, lang.value)
            extras = dict(business_push_id=history.id, push_type=PushType.BROADCAST.name)
            send_mobile_push_to_tags(tag_name, content, history.id, '', title, url, ttl,
                                     extras=extras, created_at=created_at)
            if lang == Language.ZH_HANS_CN:
                zh_title, zh_content = title, content
    history = AppAutoPushHistory.query.get(history.id)
    history.title = zh_title
    history.content = zh_content
    db.session.commit()


@celery_task
def send_new_asset_price_rise_notice(asset: str, market: str, change_rate_percent: str,
                                     ttl: int, extra: dict, created_at: int):
    zh_title = zh_content = ''
    history = AppAutoPushHistory(
        key=asset,
        type=AppAutoPushHistory.Type.NEW_ASSET_RISE,
        title=zh_title,
        content=zh_content,
        extra=json.dumps(extra)
    )
    db.session_add_and_commit(history)
    url = AppPagePath.SPOT_MARKET_DETAIL.value.format(market=market)
    for lang in Language:
        with force_locale(lang.value):
            title = gettext('新币上涨提醒')
            content = gettext('新币%(asset)s上线涨幅达到%(change_rate_percent)s，去查看>>',
                              asset=asset, change_rate_percent=change_rate_percent)
            extras = dict(business_push_id=history.id, push_type=PushType.BROADCAST.name)
            tag_name = '{}_{}'.format(PushTag.NEW_ASSET_RISE.value, lang.value)
            send_mobile_push_to_tags(tag_name, content, history.id, '', title, url, ttl,
                                     extras=extras, created_at=created_at)
            if lang == Language.ZH_HANS_CN:
                zh_title, zh_content = title, content
        history = AppAutoPushHistory.query.get(history.id)
        history.title = zh_title
        history.content = zh_content
        db.session.commit()


@celery_task
def send_price_broadcast_strategy_push(send_history_id: int, asset: str, interval_name: str, direction: str,
                                       change_rate_percent: str, price: str, mobile_ttl: int, created_at: int):
    """发送行情类触发的策略广播PUSH"""
    market = f'{asset}USDT'
    url = AppPagePath.SPOT_MARKET_DETAIL.value.format(market=market)
    business_push_id = f'auto_push_strategy_send_history:{send_history_id}'
    extras = dict(business_push_id=business_push_id, push_type=PushType.STRATEGY_PUSH.name)
    for lang in Language:
        with force_locale(lang.value):
            interval = gettext(getattr(TriggerPriceInterval, interval_name).value)
            if direction == 'RISE':
                title = gettext('%(asset)s%(interval)s价格涨幅达到%(change_rate_percent)s',
                                asset=asset, interval=interval, change_rate_percent=change_rate_percent)
            elif direction == 'FALL':
                title = gettext('%(asset)s%(interval)s价格跌幅达到%(change_rate_percent)s',
                                asset=asset, interval=interval, change_rate_percent=change_rate_percent)
            else:
                raise InvalidArgument(message='未知的币种涨跌方向！')
            content = gettext('最新价格%(price)sUSDT，立即查看>>', price=price)
            send_mobile_push_by_lang(lang.value, title, content, business_push_id, url, mobile_ttl, extras, created_at,
                                     has_checked_push_limit=True)


@celery_task
def send_trending_assets_broadcast(
        asset: str,
        market: str,
        direction: str,
        rate: str,
        price: str,
        ttl: int,
        extra: Dict,
        created_at: int = None
    ):
    zh_title = zh_content = ''
    history = AppAutoPushHistory(
                key=asset,
                type=AppAutoPushHistory.Type.TRENDING_ASSET,
                title=zh_title,
                content=zh_content,
                extra=json.dumps(extra),
            )
    db.session_add_and_commit(history)
    display_market = get_market_business_name(market)
    for lang in Language:
        with force_locale(lang.value):
            if direction == MarketPriceNotice.Direction.RISE.name:
                title = gettext("%(asset)s 24小时内上涨%(rate)s%%", asset=asset, rate=rate)
                content = gettext(
                    "%(market)s最新价格：%(price)s，24小时内上涨%(rate)s%%。请密切关注行情走势。",
                    market=display_market, price=price, rate=rate
                )
            else:
                title = gettext("%(asset)s 24小时下跌%(rate)s%%", asset=asset, rate=rate)
                content = gettext(
                    "%(market)s最新价格：%(price)s，24小时内下跌%(rate)s%%。请密切关注行情走势。",
                    market=display_market, price=price, rate=rate
                )
            url = AppPagePath.SPOT_MARKET_DETAIL.value.format(market=market)
            send_mobile_push_by_lang(
                lang.value, title, content, history.id, url, ttl,
                extras=dict(business_push_id=history.id, push_type=PushType.BROADCAST.name),
                created_at=created_at,
            )
            if lang == Language.ZH_HANS_CN:
                zh_title, zh_content = title, content
    history = AppAutoPushHistory.query.get(history.id)
    history.title = zh_title
    history.content = zh_content
    db.session.commit()


@celery_task
def send_trending_assets_daily_report_broadcast(
        assets: List[str],
        rate_map: Dict[str, str],
        ttl: int,
        created_at: int = None,
    ):
    zh_title = zh_content = ''
    history = AppAutoPushHistory(
                type=AppAutoPushHistory.Type.TRENDING_ASSET_DAILY_REPORT,
                title=zh_title,
                content=zh_content,
            )
    db.session_add_and_commit(history)
    for lang in Language:
        with force_locale(lang.value):
            title = gettext("24H热搜榜TOP 5")
            content = ''
            for asset in assets:
                rate = rate_map[asset]
                content += f"{asset} {rate}%; "
            url = AppPagePath.HOME.value
            send_mobile_push_by_lang(lang.value, title,
                                        content, history.id, url,
                                        ttl, extras=dict(business_push_id=history.id,
                                                        push_type=PushType.BROADCAST.name), created_at=created_at)
            if lang == Language.ZH_HANS_CN:
                zh_title, zh_content = title, content
    history = AppAutoPushHistory.query.get(history.id)
    history.title = zh_title
    history.content = zh_content
    db.session.commit()


@celery_task
def send_user_asset_notice_push(business_str: str, user_ids: List[int], asset: str, market: str, direction: str, price: str, rate: str,
                                created_at: int = None):
    for user_id in user_ids:
        lang = get_user_app_lang(user_id).value

        with force_locale(lang):
            if business_str == AppPushBusiness.UserHoldAssetPrice.name:
                title = gettext('持仓币种提醒')
            elif business_str == AppPushBusiness.UserFavoriteAssetPrice.name:
                title = gettext('自选币种提醒')
            else:
                return

            if direction == MarketPriceNotice.Direction.RISE.name:
                content = gettext(
                    "%(asset)s 当日上涨%(rate)s%%，最新价格：%(price)sUSDT。去查看>>",
                    asset=asset, rate=rate, price=price
                )
            else:
                content = gettext(
                    "%(asset)s 当日下跌%(rate)s%%，最新价格：%(price)sUSDT。去查看>>",
                    asset=asset, rate=rate, price=price
                )
            extras = auto_push_read_extras(IncreaseEvent.READ_ASSET_NOTICE_CUSTOM_COUNT)
            url = AppPagePath.SPOT_MARKET_DETAIL.value.format(market=market)
            business = getattr(AppPushBusiness, business_str)
            handler = PushBusinessHandler(business)
            if not handler.can_push([user_id]):
                continue
            send_mobile_push_by_user_ids([user_id], title, content, url, extras=extras,
                                         created_at=created_at)
            biz_monitor.increase_counter(AppPushEvent.PUSH_ASSET_NOTICE_CUSTOM_COUNT)
            handler.set_pushed([user_id])


@celery_task
def send_resume_deposit_withdrawal(user_ids: List[int], asset: str, type_: str, created_at: int = None):
    for user_id in user_ids:
        lang = get_user_app_lang(user_id).value
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        with force_locale(lang):
            if type_ == BalanceBusiness.DEPOSIT.name:
                title = gettext('充值恢复')
                content = gettext('%(asset)s已恢复充值功能。', asset=asset)
                app_url = AppPagePath.DEPOSIT.value.format(asset=asset)
            else:
                title = gettext('提现恢复')
                content = gettext('%(asset)s已恢复提现功能。', asset=asset)
                app_url = AppPagePath.WITHDRAW.value.format(asset=asset)
            send_mobile_push_by_user_ids([user_id], title, content, app_url, created_at=created_at)
            handler.set_pushed([user_id])


@celery_task
def send_margin_liquidation_push(user_ids: List[int], market: str, created_at: int = None):
    """杠杆-爆仓通知"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('强制还币通知')
            content = gettext('你在%(market)s杠杆账户风险率已触发强制还币流程。', market=get_market_business_name(market))
            app_url = AppPagePath.BORROWING_RECORD.value
            extras = auto_push_read_extras(IncreaseEvent.READ_LOAN_RISK_FLAT)
            send_mobile_push_by_user_ids([user_id], title, content, app_url, extras=extras,
                                         created_at=created_at)
            handler.set_pushed([user_id])
            biz_monitor.increase_counter(IncreaseEvent.PUSH_LOAN_RISK_FLAT)

        with force_locale(web_lang):
            title = gettext('强制还币通知')
            content = gettext('你在%(market)s杠杆账户风险率已触发强制还币流程。', market=get_market_business_name(market))
            web_url = WebPagePath.BORROWING_RECORD.value
            client = ServerClient()
            client.notice_user_message(user_id,
                                       WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.NOTICE))


@celery_task
def send_margin_liquidation_warning_push(user_ids: List[int], market: str, risk_rate: str, created_at: int = None):
    """杠杆-爆仓预警"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        m = MarketCache(market).dict
        market_name = f"{m['base_asset']}-{m['quote_asset']}#margin"
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('杠杆强平预警')
            content = gettext(
                '你的%(market)s杠杆账户借币风险率已达 %(risk_rate)s%%。', market=get_market_business_name(market), risk_rate=risk_rate
            )
            app_url = AppPagePath.SPOT_TRADE.value.format(market=market, account=2, trade_type='')

            extras = auto_push_read_extras(IncreaseEvent.READ_LOAN_LIQUIDATION_WARN)
            send_mobile_push_by_user_ids([user_id], title, content, app_url, extras=extras,
                                         created_at=created_at)
            biz_monitor.increase_counter(IncreaseEvent.PUSH_LOAN_LIQUIDATION_WARN)
            handler.set_pushed([user_id])

        with force_locale(web_lang):
            title = gettext('杠杆强平预警')
            content = gettext(
                '你的%(market)s杠杆账户借币风险率已达 %(risk_rate)s%%。', market=get_market_business_name(market), risk_rate=risk_rate
            )
            web_url = WebPagePath.MARGIN_MARKET.value + market_name
            client = ServerClient()
            client.notice_user_message(user_id,
                                       WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.NOTICE))


@celery_task
def send_margin_loan_order_expired_push(user_ids: List[int], asset: str, market: str, created_at: int = None):
    """杠杆-借币到期提醒"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('借币到期提醒')
            content = gettext('你在%(market)s杠杆市场的%(asset)s借币订单即将到期。', market=get_market_business_name(market), asset=asset)
            app_url = AppPagePath.BORROWING_RECORD.value
            extras = auto_push_read_extras(IncreaseEvent.READ_LOAN_ORDER_EXPIRE)
            send_mobile_push_by_user_ids([user_id], title, content, app_url, extras=extras,
                                         created_at=created_at)
            biz_monitor.increase_counter(IncreaseEvent.PUSH_LOAN_ORDER_EXPIRE)
            handler.set_pushed([user_id])

        with force_locale(web_lang):
            title = gettext('借币到期提醒')
            content = gettext('你在%(market)s杠杆市场的%(asset)s借币订单即将到期。', market=get_market_business_name(market), asset=asset)
            web_url = WebPagePath.BORROWING_RECORD.value
            client = ServerClient()
            client.notice_user_message(user_id,
                                       WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.NOTICE))


@celery_task
def send_margin_renew_failed_push(user_ids: List[int], asset: str, market: str, created_at: int = None):
    """杠杆-续借失败通知"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('续借失败通知')
            content = gettext('你在%(market)s杠杆市场的%(asset)s借币订单续借失败。', market=get_market_business_name(market), asset=asset)
            app_url = AppPagePath.BORROWING_RECORD.value
            extras = auto_push_read_extras(IncreaseEvent.READ_LOAN_RENEW_FAIL)
            send_mobile_push_by_user_ids([user_id], title, content, app_url, extras=extras,
                                         created_at=created_at)
            handler.set_pushed([user_id])
            biz_monitor.increase_counter(IncreaseEvent.PUSH_LOAN_RENEW_FAIL)

        with force_locale(web_lang):
            title = gettext('续借失败通知')
            content = gettext('你在%(market)s杠杆市场的%(asset)s借币订单续借失败。', market=get_market_business_name(market), asset=asset)
            web_url = WebPagePath.BORROWING_RECORD.value
            client = ServerClient()
            client.notice_user_message(user_id,
                                       WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.FAIL))


@celery_task
def send_margin_loan_order_force_flat_push(user_ids: List[int], asset: str, market: str, created_at: int = None):
    """杠杆-强制还币通知"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('强制还币通知')
            content = gettext(
                '你在%(market)s杠杆市场的%(asset)s借币订单已到期，并触发强制还币流程。',
                market=get_market_business_name(market),
                asset=asset
            )
            app_url = AppPagePath.BORROWING_RECORD.value
            extras = auto_push_read_extras(IncreaseEvent.READ_LOAN_EXPIRE_FLAT)
            send_mobile_push_by_user_ids([user_id], title, content, app_url, extras=extras,
                                         created_at=created_at)
            biz_monitor.increase_counter(IncreaseEvent.PUSH_LOAN_EXPIRE_FLAT)
            handler.set_pushed([user_id])

        with force_locale(web_lang):
            title = gettext('强制还币通知')
            content = gettext(
                '你在%(market)s杠杆市场的%(asset)s借币订单已到期，并触发强制还币流程。',
                market=get_market_business_name(market),
                asset=asset
            )
            web_url = WebPagePath.BORROWING_RECORD.value
            client = ServerClient()
            client.notice_user_message(user_id,
                                       WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.NOTICE))


@celery_task
def send_margin_loan_order_force_flat_balance_push(user_ids: List[int], asset: str, market: str,
                                                   created_at: int = None):
    """杠杆-强制还币通知-借币池余额不足"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('强制还币通知')
            content = gettext(
                '你在%(market)s杠杆市场的%(asset)s借币订单，因借币池余额不足触发强制还币流程。',
                market=get_market_business_name(market),
                asset=asset
            )
            app_url = AppPagePath.BORROWING_RECORD.value
            extras = auto_push_read_extras(IncreaseEvent.READ_LOAN_NOT_ENOUGH_FLAT)
            send_mobile_push_by_user_ids([user_id], title, content, app_url, extras=extras,
                                         created_at=created_at)
            biz_monitor.increase_counter(IncreaseEvent.PUSH_LOAN_NOT_ENOUGH_FLAT)
            handler.set_pushed([user_id])

        with force_locale(web_lang):
            title = gettext('强制还币通知')
            content = gettext(
                '你在%(market)s杠杆市场的%(asset)s借币订单，因借币池余额不足触发强制还币流程。',
                market=get_market_business_name(market),
                asset=asset
            )
            client = ServerClient()
            web_url = WebPagePath.BORROWING_RECORD.value
            client.notice_user_message(user_id,
                                       WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.NOTICE))


def send_perpetual_adl_push(user_ids: List[int], market: str, created_at: int = None):
    """合约自动减仓通知"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('合约自动减仓')
            content = gettext(
                '你的%(market_type_name)s%(market)s已触发自动减仓流程。',
                market_type_name=_get_market_type_name(market), market=market
            )
            app_url = AppPagePath.PERPETUAL_TRADE.value.format(market=market, trade_type='')
            extras = auto_push_read_extras(IncreaseEvent.READ_PERPETUAL_ADL)
            send_mobile_push_by_user_ids([user_id], title, content, app_url, extras=extras,
                                         created_at=created_at)
            biz_monitor.increase_counter(IncreaseEvent.PUSH_PERPETUAL_ADL)
            handler.set_pushed([user_id])

        with force_locale(web_lang):
            title = gettext('合约自动减仓')
            content = gettext(
                '你的%(market_type_name)s%(market)s已触发自动减仓流程。',
                market_type_name=_get_market_type_name(market), market=market
            )
            client = ServerClient()
            market_data = PerpetualMarketCache().get_market_info(market)
            stock = market_data['stock']
            money = market_data['money']
            market_name = f'{stock}-{money}'
            web_url = WebPagePath.PERPETUAL_LIQ_RECORD.value + market_name
            client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.NOTICE))


@celery_task
def send_perpetual_liquidation_push(user_ids: List[int], market: str, created_at: int = None):
    """合约爆仓通知"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('合约强平通知')
            content = gettext(
                '你的%(market_type_name)s%(market)s已触发强平流程。',
                market_type_name=_get_market_type_name(market), market=market
            )
            extras = auto_push_read_extras(IncreaseEvent.READ_PERPETUAL_FLAT_NOTICE)
            biz_monitor.increase_counter(IncreaseEvent.PUSH_PERPETUAL_FLAT_NOTICE)
            app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='current_position')
            send_mobile_push_by_user_ids([user_id], title, content, app_url, extras=extras,
                                         created_at=created_at)
            handler.set_pushed([user_id])

        with force_locale(web_lang):
            title = gettext('合约强平通知')
            content = gettext(
                '你的%(market_type_name)s%(market)s已触发强平流程。',
                market_type_name=_get_market_type_name(market), market=market
            )
            client = ServerClient()
            web_url = WebPagePath.PERPETUAL_HISTORY_RECORD.value
            client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.NOTICE))


@celery_task
def send_perpetual_position_reduce_push(user_ids: List[int], market: str, created_at: int = None):
    """合约降档减仓通知"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue

        market_data = PerpetualMarketCache().get_market_info(market)
        stock = market_data['stock']
        money = market_data['money']
        market_name = f'{stock}-{money}'
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('合约降档减仓通知')
            content = gettext(
                '你的%(market_type_name)s%(market)s持仓已触发降档减仓。',
                market_type_name=_get_market_type_name(market), market=market
            )
            app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='deal_record')
            send_mobile_push_by_user_ids([user_id], title, content, app_url, created_at=created_at)
            handler.set_pushed([user_id])
        with force_locale(web_lang):
            title = gettext('合约降档减仓通知')
            content = gettext(
                '你的%(market_type_name)s%(market)s持仓已触发降档减仓。',
                market_type_name=_get_market_type_name(market), market=market
            )
            client = ServerClient()
            web_url = WebPagePath.PERPETUAL_LIQ_RECORD.value + market_name
            client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.NOTICE))


@celery_task
def send_perpetual_liquidation_warning_push(user_ids: List[int], market: str, risk_rate: str, created_at: int = None):
    """合约爆仓警告"""
    for user_id in user_ids:
        if User.query.get(user_id).user_type != User.UserType.NORMAL:
            continue
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            continue
        app_lang = get_user_app_lang(user_id).value
        web_lang = get_user_web_lang(user_id).value
        with force_locale(app_lang):
            title = gettext('合约强平预警')
            content = gettext(
                '你的%(market_type_name)s%(market)s仓位风险率已达到 %(risk_rate)s%%。',
                market_type_name=_get_market_type_name(market), market=market, risk_rate=risk_rate
            )
            app_url = AppPagePath.PERPETUAL_TRADE.value.format(market=market, trade_type='')
            extras = auto_push_read_extras(IncreaseEvent.READ_PERPETUAL_FLAT_WARN)
            send_mobile_push_by_user_ids([user_id], title, content, app_url, extras=extras,
                                         created_at=created_at)
            biz_monitor.increase_counter(IncreaseEvent.PUSH_PERPETUAL_FLAT_WARN)
            handler.set_pushed([user_id])

        with force_locale(web_lang):
            title = gettext('合约强平预警')
            content = gettext(
                '你的%(market_type_name)s%(market)s仓位风险率已达到 %(risk_rate)s%%。',
                market_type_name=_get_market_type_name(market), market=market, risk_rate=risk_rate
            )
            market_data = PerpetualMarketCache().get_market_info(market)
            stock = market_data['stock']
            money = market_data['money']
            market_name = f'{stock}-{money}'
            web_url = WebPagePath.PERPETUAL_LIQ_RECORD.value + market_name
            client = ServerClient()
            client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                       dict(title=title, content=content, url=web_url, type=NoticePushType.NOTICE))


def _get_market_type_name(market: str):
    info = PerpetualMarketCache().get_market_info(market)
    if info['type'] == PerpetualMarketType.DIRECT.value:
        market_type_name = gettext('正向合约')
    else:
        market_type_name = gettext('反向合约')
    return market_type_name


def _get_asset_name(market: str, asset):
    info = PerpetualMarketCache().get_market_info(market)
    if info['type'] == PerpetualMarketType.DIRECT.value:
        asset_name = asset
    else:
        asset_name = gettext('张')
    return asset_name


def _get_order_type_name(side: int):
    if side == 1:
        order_type_name = gettext('卖出')
    else:
        order_type_name = gettext('买入')
    return order_type_name


def _get_perpetual_order_type_name(side: int):
    if side == 1:
        order_type_name = gettext('卖出/做空')
    else:
        order_type_name = gettext('买入/做多')
    return order_type_name


@celery_task
def send_asset_exchange_order_push(exchange_order_id: int, created_at: int = None):
    """ 发送币种兑换的push通知 """
    order: AssetExchangeOrder = AssetExchangeOrder.query.get(exchange_order_id)
    notice_type = NoticePushType.SUCCESS
    user_id = order.user_id
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value

    if order.result == AssetExchangeOrder.Result.ALL:
        with force_locale(app_lang):
            app_title = gettext("兑换成功")
            app_msg = gettext(
                "你已成功兑换获得 %(target_asset_exchanged_amount)s %(target_asset)s。",
                target_asset_exchanged_amount=amount_to_str(order.target_asset_exchanged_amount, 8),
                target_asset=order.target_asset,
            )
        with force_locale(web_lang):
            web_title = gettext("兑换成功")
            web_msg = gettext(
                "你已成功兑换获得 %(target_asset_exchanged_amount)s %(target_asset)s。",
                target_asset_exchanged_amount=amount_to_str(order.target_asset_exchanged_amount, 8),
                target_asset=order.target_asset,
            )
    elif order.result == AssetExchangeOrder.Result.FAILED:
        notice_type = NoticePushType.FAIL
        transfer = AssetExchangeOrderTransferHistory.query.filter(
            AssetExchangeOrderTransferHistory.exchange_order_id == exchange_order_id,
            AssetExchangeOrderTransferHistory.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_REMAIN_SOURCE_ASSET,
        ).first()
        amount = transfer.amount if transfer else order.source_asset_amount
        with force_locale(app_lang):
            app_title = gettext("兑换失败")
            app_msg = gettext(
                "你的兑换交易失败，%(source_asset_amount)s %(source_asset)s 已返还。",
                source_asset_amount=amount_to_str(amount, 8),
                source_asset=order.source_asset,
            )
        with force_locale(web_lang):
            web_title = gettext("兑换失败")
            web_msg = gettext(
                "你的兑换交易失败，%(source_asset_amount)s %(source_asset)s 已返还。",
                source_asset_amount=amount_to_str(amount, 8),
                source_asset=order.source_asset,
            )

    elif order.result == AssetExchangeOrder.Result.PARTIAL:
        notice_type = NoticePushType.NOTICE
        source_asset_remain_amount = order.source_asset_amount - order.source_asset_exchanged_amount
        with force_locale(app_lang):
            app_title = gettext("部分兑换成功")
            app_msg = gettext(
                "你兑换获得 %(target_asset_exchanged_amount)s %(target_asset)s，"
                "剩余未兑换的 %(source_asset_remain_amount)s %(source_asset)s 已返还。",
                target_asset_exchanged_amount=amount_to_str(order.target_asset_exchanged_amount, 8),
                target_asset=order.target_asset,
                source_asset_remain_amount=amount_to_str(source_asset_remain_amount, 8),
                source_asset=order.source_asset,
            )
        with force_locale(web_lang):
            web_title = gettext("部分兑换成功")
            web_msg = gettext(
                "你兑换获得 %(target_asset_exchanged_amount)s %(target_asset)s，"
                "剩余未兑换的 %(source_asset_remain_amount)s %(source_asset)s 已返还。",
                target_asset_exchanged_amount=amount_to_str(order.target_asset_exchanged_amount, 8),
                target_asset=order.target_asset,
                source_asset_remain_amount=amount_to_str(source_asset_remain_amount, 8),
                source_asset=order.source_asset,
            )
    else:
        return
    pref = UserPreferences(user_id)
    if pref.app_exchange_notice and not limit_app_subscribe_notice(user_id):
        app_url = AppPagePath.ASSET_EXCHANGE_ORDER.value.format(id=exchange_order_id)
        send_mobile_push_by_user_ids([user_id], app_title, app_msg, app_url, created_at=created_at)
    web_url = WebPagePath.EXCHANGE_RECORD.value
    client = ServerClient()
    client.notice_user_message(
        user_id,
        WebPushChannelType.NORMAL.value,
        dict(title=web_title, content=web_msg, url=web_url, type=notice_type, msg_type=WebPushMessageType.EXCHANGE.value),
    )


@celery_task
def send_credited_deposit_push(deposit_id: int, created_at: int = None):
    deposit = Deposit.query.get(deposit_id)
    user_id = deposit.user_id
    pref = UserPreferences(deposit.user_id)
    date_str = datetime_to_str(deposit.created_at, pref.timezone_offset)
    amount_str = f'{amount_to_str(deposit.amount)} {deposit.asset}'
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    with force_locale(app_lang):
        title = gettext("充值成功")
        msg = gettext(
            "你充值的%(amount)s 已到账。",
            create_time=date_str,
            amount=amount_str,
        )
        if pref.app_deposit_withdrawal_notice and not limit_app_subscribe_notice(user_id):
            app_url = AppPagePath.DEPOSIT_RECORD.value
            send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("充值成功")
        msg = gettext(
            "你充值的%(amount)s 已到账。",
            create_time=date_str,
            amount=amount_str,
        )
        web_url = WebPagePath.DEPOSIT_RECORD.value
        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.SUCCESS))


@celery_task
def send_withdrawal_notice_push(withdrawal_id: int, created_at: int = None):
    withdrawal = Withdrawal.query.get(withdrawal_id)
    pref = UserPreferences(withdrawal.user_id)
    user_id = withdrawal.user_id

    amount_str = f'{amount_to_str(withdrawal.amount)} {withdrawal.asset}'
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    if pref.app_deposit_withdrawal_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("提现成功")
            msg = gettext(
                "你已成功提出%(amount)s。",
                amount=amount_str,
            )
            app_url = AppPagePath.WITHDRAW_RECORD.value
            send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("提现成功")
        msg = gettext(
            "你已成功提出%(amount)s。",
            amount=amount_str,
        )
        web_url = WebPagePath.WITHDRAW_RECORD.value
        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                               dict(title=title, content=msg, url=web_url, type=NoticePushType.SUCCESS))


@celery_task
def send_ieo_notice_push(user_id: int, project_name: str, is_lottery: bool, created_at: int = None):
    pref = UserPreferences(user_id)
    notice_type = NoticePushType.SUCCESS if is_lottery else NoticePushType.FAIL
    web_url = WebPagePath.IEO_RECORD.value
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    if pref.app_ieo_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("中签通知")
            if is_lottery:
                msg = gettext(
                    "恭喜！你在CoinEx Dock参与的%(project_name)s项目已成功中签。",
                    project_name=project_name,
                )
            else:
                msg = gettext(
                    "很遗憾，你在CoinEx Dock参与的%(project_name)s项目申购未中签。",
                    project_name=project_name,
                )
            send_mobile_push_by_user_ids([user_id], title, msg, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("中签通知")
        if is_lottery:
            msg = gettext(
                "恭喜！你在CoinEx Dock参与的%(project_name)s项目已成功中签。",
                project_name=project_name,
            )
        else:
            msg = gettext(
                "很遗憾，你在CoinEx Dock参与的%(project_name)s项目申购未中签。",
                project_name=project_name,
            )
        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                               dict(title=title, content=msg, url=web_url, type=notice_type))


@celery_task
def send_spot_part_deal_notice_push(user_id, market, side, amount, asset, counter_amount, counter_asset,
                                    created_at: int = None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)
    limit_cache = SpotOrderNormalDealLimitCache(user_id)
    if not limit_cache.count() < limit_cache.count_limit:
        return
    limit_cache.add_value()
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    if pref.app_spot_limit_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("币币订单成交提醒")

            msg = gettext(
                f"你的%(market)s%(side)s委托已部分成交，成交数量%(amount)s %(asset)s，成交均价%(counter_amount)s %(counter_asset)s。",
                market=get_market_business_name(market),
                side=_get_order_type_name(side),
                amount=amount,
                asset=asset,
                counter_amount=counter_amount,
                counter_asset=counter_asset,
            )
            app_url = AppPagePath.SPOT_ORDER.value.format(type='current_order')
            send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("币币订单成交提醒")
        msg = gettext(
            f"你的%(market)s%(side)s委托已部分成交，成交数量%(amount)s %(asset)s，成交均价%(counter_amount)s %(counter_asset)s。",
            market=get_market_business_name(market),
            side=_get_order_type_name(side),
            amount=amount,
            asset=asset,
            counter_amount=counter_amount,
            counter_asset=counter_asset,
        )
        client = ServerClient()
        web_url = WebPagePath.ORDER_CURRENT_NORMAL_RECORD.value
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                               dict(title=title, content=msg, url=web_url, type=NoticePushType.NOTICE))


@celery_task
def send_spot_total_deal_notice_push(user_id, market, side, amount, asset, counter_amount, counter_asset,
                                     created_at: int = None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)
    limit_cache = SpotOrderNormalDealLimitCache(user_id)
    if not limit_cache.count() < limit_cache.count_limit:
        return
    limit_cache.add_value()

    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value

    if pref.app_spot_limit_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("币币订单成交提醒")
            msg = gettext(
                f"你的%(market)s%(side)s委托已全部成交，成交数量%(amount)s %(asset)s，成交均价%(counter_amount)s %(counter_asset)s。",
                market=get_market_business_name(market),
                side=_get_order_type_name(side),
                amount=amount,
                asset=asset,
                counter_amount=counter_amount,
                counter_asset=counter_asset,
            )
        app_url = AppPagePath.SPOT_ORDER.value.format(type='history_order')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("币币订单成交提醒")
        msg = gettext(
            f"你的%(market)s%(side)s委托已全部成交，成交数量%(amount)s %(asset)s，成交均价%(counter_amount)s %(counter_asset)s。",
            market=get_market_business_name(market),
            side=_get_order_type_name(side),
            amount=amount,
            asset=asset,
            counter_amount=counter_amount,
            counter_asset=counter_asset,
        )
        client = ServerClient()
        web_url = WebPagePath.ORDER_HISTORY_NORMAL_RECORD.value
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.SUCCESS))


@celery_task
def send_spot_stop_order_fail_notice_push(user_id, market, side, created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    if pref.app_spot_stop_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("币币订单委托提醒")
            msg = gettext(
                f"你的%(market)s%(side)s委托失败。",
                market=get_market_business_name(market),
                side=_get_order_type_name(side),
            )
        app_url = AppPagePath.SPOT_ORDER.value.format(type='history_order')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("币币订单委托提醒")
        msg = gettext(
            f"你的%(market)s%(side)s委托失败。",
            market=get_market_business_name(market),
            side=_get_order_type_name(side),
        )
        client = ServerClient()
        web_url = WebPagePath.ORDER_HISTORY_STOP_RECORD.value
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                               dict(title=title, content=msg, url=web_url, type=NoticePushType.FAIL))


@celery_task
def send_spot_limit_stop_order_notice_push(user_id, market, side, amount, asset, counter_amount, counter_asset,
                                           created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)
    limit_cache = SpotStopOrderLimitCache(user_id)
    if not limit_cache.count() < limit_cache.count_limit:
        return
    limit_cache.add_value()
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value

    if pref.app_spot_stop_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("币币订单委托提醒")
            msg = gettext(
                f"你的%(market)s计划限价%(side)s委托已触发，委托数量%(amount)s %(asset)s，委托价格%(counter_amount)s %(counter_asset)s。",
                market=get_market_business_name(market),
                side=_get_order_type_name(side),
                amount=amount,
                asset=asset,
                counter_amount=counter_amount,
                counter_asset=counter_asset,
            )
        app_url = AppPagePath.SPOT_ORDER.value.format(type='history_order')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("币币订单委托提醒")
        msg = gettext(
            f"你的%(market)s计划限价%(side)s委托已触发，委托数量%(amount)s %(asset)s，委托价格%(counter_amount)s %(counter_asset)s。",
            market=get_market_business_name(market),
            side=_get_order_type_name(side),
            amount=amount,
            asset=asset,
            counter_amount=counter_amount,
            counter_asset=counter_asset,
        )
        client = ServerClient()
        web_url = WebPagePath.ORDER_HISTORY_STOP_RECORD.value
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.SUCCESS))


@celery_task
def send_spot_market_order_notice_push(user_id, market, side, amount, asset, counter_asset, created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    limit_cache = SpotStopOrderLimitCache(user_id)
    if not limit_cache.count() < limit_cache.count_limit:
        return
    limit_cache.add_value()
    if pref.app_spot_stop_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("币币订单委托提醒")
            if side == 1:
                msg = gettext(
                    f"你的%(market)s计划市价%(side)s委托已触发，委托数量%(amount)s %(asset)s。",
                    market=get_market_business_name(market),
                    side=_get_order_type_name(side),
                    amount=amount,
                    asset=asset,
                )
            else:
                msg = gettext(
                    f"你的%(market)s计划市价%(side)s委托已触发，交易额%(amount)s %(asset)s。",
                    market=get_market_business_name(market),
                    side=_get_order_type_name(side),
                    amount=amount,
                    asset=counter_asset,
                )
            app_url = AppPagePath.SPOT_ORDER.value.format(type='history_order')
            send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("币币订单委托提醒")
        if side == 1:
            msg = gettext(
                f"你的%(market)s计划市价%(side)s委托已触发，委托数量%(amount)s %(asset)s。",
                market=get_market_business_name(market),
                side=_get_order_type_name(side),
                amount=amount,
                asset=asset,
            )
        else:
            msg = gettext(
                f"你的%(market)s计划市价%(side)s委托已触发，交易额%(amount)s %(asset)s。",
                market=get_market_business_name(market),
                side=_get_order_type_name(side),
                amount=amount,
                asset=counter_asset,
            )
        client = ServerClient()
        web_url = WebPagePath.ORDER_HISTORY_STOP_RECORD.value
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.SUCCESS))


# perpetual

@celery_task
def send_perpetual_part_deal_notice_push(user_id, market, side, amount, asset, counter_amount, counter_asset,
                                         created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    limit_cache = PerpetualOrderNormalDealLimitCache(user_id)
    if not limit_cache.count() < limit_cache.count_limit:
        return
    limit_cache.add_value()

    if pref.app_perpetual_limit_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("合约成交提醒")
            msg = gettext(
                f"你的%(market_type_name)s%(market)s%(side)s委托已部分成交，成交数量%(amount)s %(asset)s，"
                f"成交均价%(counter_amount)s %(counter_asset)s。",
                market_type_name=_get_market_type_name(market),
                market=market,
                side=_get_perpetual_order_type_name(side),
                amount=amount,
                asset=_get_asset_name(market, asset),
                counter_amount=counter_amount,
                counter_asset=counter_asset,
            )
        app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='current_order')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("合约成交提醒")
        msg = gettext(
            f"你的%(market_type_name)s%(market)s%(side)s委托已部分成交，成交数量%(amount)s %(asset)s，"
            f"成交均价%(counter_amount)s %(counter_asset)s。",
            market_type_name=_get_market_type_name(market),
            market=market,
            side=_get_perpetual_order_type_name(side),
            amount=amount,
            asset=_get_asset_name(market, asset),
            counter_amount=counter_amount,
            counter_asset=counter_asset,
        )
        web_url = WebPagePath.PERPETUAL_CURRENT_HISTORY_RECORD.value
        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.NOTICE))


@celery_task
def send_perpetual_total_deal_notice_push(user_id, market, side, amount, asset, counter_amount, counter_asset,
                                          created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)
    limit_cache = PerpetualOrderNormalDealLimitCache(user_id)
    if not limit_cache.count() < limit_cache.count_limit:
        return
    limit_cache.add_value()

    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value

    if pref.app_perpetual_limit_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("合约成交提醒")
            msg = gettext(
                f"你的%(market_type_name)s%(market)s%(side)s委托已完全成交，成交数量%(amount)s %(asset)s，"
                f"成交均价%(counter_amount)s %(counter_asset)s。",
                market_type_name=_get_market_type_name(market),
                market=market,
                side=_get_perpetual_order_type_name(side),
                amount=amount,
                asset=_get_asset_name(market, asset),
                counter_amount=counter_amount,
                counter_asset=counter_asset,
            )
        app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='history_order')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("合约成交提醒")
        msg = gettext(
            f"你的%(market_type_name)s%(market)s%(side)s委托已完全成交，成交数量%(amount)s %(asset)s，"
            f"成交均价%(counter_amount)s %(counter_asset)s。",
            market_type_name=_get_market_type_name(market),
            market=market,
            side=_get_perpetual_order_type_name(side),
            amount=amount,
            asset=_get_asset_name(market, asset),
            counter_amount=counter_amount,
            counter_asset=counter_asset,
        )
        web_url = WebPagePath.PERPETUAL_HISTORY_RECORD.value
        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.SUCCESS))


@celery_task
def send_perpetual_limit_stop_order_notice_push(user_id, market, side, amount, asset, counter_amount, counter_asset,
                                                created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)
    limit_cache = PerpetualStopOrderLimitCache(user_id)
    if not limit_cache.count() < limit_cache.count_limit:
        return
    limit_cache.add_value()
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value

    if pref.app_perpetual_stop_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("合约委托提醒")
            msg = gettext(
                f"你的%(market_type_name)s%(market)s计划限价%(side)s委托已触发，"
                f"委托数量%(amount)s %(asset)s，委托价格%(counter_amount)s %(counter_asset)s。",
                market_type_name=_get_market_type_name(market),
                market=market,
                side=_get_perpetual_order_type_name(side),
                amount=amount,
                asset=_get_asset_name(market, asset),
                counter_amount=counter_amount,
                counter_asset=counter_asset,
            )
            app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='current_order')
            send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("合约委托提醒")
        msg = gettext(
            f"你的%(market_type_name)s%(market)s计划限价%(side)s委托已触发，"
            f"委托数量%(amount)s %(asset)s，委托价格%(counter_amount)s %(counter_asset)s。",
            market_type_name=_get_market_type_name(market),
            market=market,
            side=_get_perpetual_order_type_name(side),
            amount=amount,
            asset=_get_asset_name(market, asset),
            counter_amount=counter_amount,
            counter_asset=counter_asset,
        )
        web_url = WebPagePath.PERPETUAL_CURRENT_HISTORY_RECORD.value
        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.SUCCESS))


@celery_task
def send_perpetual_market_stop_order_push(user_id, market, side, amount, asset, created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)

    limit_cache = PerpetualStopOrderLimitCache(user_id)
    if not limit_cache.count() < limit_cache.count_limit:
        return
    limit_cache.add_value()
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value

    if pref.app_perpetual_stop_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("合约委托提醒")
            msg = gettext(
                f"你的%(market_type_name)s%(market)s计划市价%(side)s委托已触发，委托数量%(amount)s %(asset)s。",
                market_type_name=_get_market_type_name(market),
                market=market,
                side=_get_perpetual_order_type_name(side),
                amount=amount,
                asset=_get_asset_name(market, asset),
            )
        app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='history_order')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("合约委托提醒")
        msg = gettext(
            f"你的%(market_type_name)s%(market)s计划市价%(side)s委托已触发，委托数量%(amount)s %(asset)s。",
            market_type_name=_get_market_type_name(market),
            market=market,
            side=_get_perpetual_order_type_name(side),
            amount=amount,
            asset=_get_asset_name(market, asset),
        )
        web_url = WebPagePath.PERPETUAL_STOP_HISTORY_RECORD.value
        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.SUCCESS))


@celery_task
def send_perpetual_stop_order_fail_notice_push(user_id, market, side, created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    pref = UserPreferences(user_id)
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value

    if pref.app_perpetual_stop_order_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            title = gettext("合约委托失败")
            msg = gettext(
                f"你的%(market_type_name)s%(market)s%(side)s委托失败。",
                market_type_name=_get_market_type_name(market),
                market=market,
                side=_get_perpetual_order_type_name(side),
            )
        app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='history_order')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        title = gettext("合约委托失败")
        msg = gettext(
            f"你的%(market_type_name)s%(market)s%(side)s委托失败。",
            market_type_name=_get_market_type_name(market),
            market=market,
            side=_get_perpetual_order_type_name(side),
        )
        web_url = WebPagePath.PERPETUAL_STOP_HISTORY_RECORD.value
        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.FAIL))


@celery_task
def send_perpetual_take_profit_notice_push(user_id, market, market_type, asset, amount, success, created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return

    pref = UserPreferences(user_id)
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    if pref.app_perpetual_profit_loss_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            if success:
                title = gettext('合约止盈成功通知')
                msg = gettext(
                    '你在%(market_type)s %(market)s中的止盈设置已被触发，并且全部平仓成功。',
                    market_type=market_type, market=market
                )
            else:
                title = gettext('合约止盈部分失败通知')
                msg = gettext(
                    '你在%(market_type)s %(market)s中的止盈设置已部分平仓，剩余未平仓位%(amount)s%(asset)s。',
                    market_type=market_type, market=market, amount=amount, asset=asset
                )
        app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='history_order')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        if success:
            title = gettext('合约止盈成功通知')
            msg = gettext(
                '你在%(market_type)s %(market)s中的止盈设置已被触发，并且全部平仓成功。',
                market_type=market_type, market=market
            )
            type_ = NoticePushType.SUCCESS
        else:
            title = gettext('合约止盈部分失败通知')
            msg = gettext(
                '你在%(market_type)s %(market)s中的止盈设置已部分平仓，剩余未平仓位%(amount)s%(asset)s。',
                market_type=market_type, market=market, amount=amount, asset=asset
            )
            type_ = NoticePushType.NOTICE
    web_url = WebPagePath.PERPETUAL_STOP_HISTORY_RECORD.value
    client = ServerClient()
    client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                               dict(title=title, content=msg, url=web_url, type=type_))


@celery_task
def send_perpetual_stop_loss_notice_push(user_id, market, market_type, asset, amount, success, created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return

    pref = UserPreferences(user_id)
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    if pref.app_perpetual_profit_loss_notice and not limit_app_subscribe_notice(user_id):
        with force_locale(app_lang):
            if success:
                title = gettext('合约止损成功通知')
                msg = gettext(
                    '你在%(market_type)s %(market)s中的止损设置已被触发，并且全部平仓成功。',
                    market_type=market_type, market=market
                )
            else:
                title = gettext('合约止损部分失败通知')
                msg = gettext(
                    '你在%(market_type)s %(market)s中的止损设置已部分平仓，剩余未平仓位%(amount)s%(asset)s。',
                    market_type=market_type, market=market, amount=amount, asset=asset
                )
        app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='history_order')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
    with force_locale(web_lang):
        if success:
            title = gettext('合约止损成功通知')
            msg = gettext(
                '你在%(market_type)s %(market)s中的止损设置已被触发，并且全部平仓成功。',
                market_type=market_type, market=market
            )
            type_ = NoticePushType.SUCCESS
        else:
            title = gettext('合约止损部分失败通知')
            msg = gettext(
                '你在%(market_type)s %(market)s中的止损设置已部分平仓，剩余未平仓位%(amount)s%(asset)s。',
                market_type=market_type, market=market, amount=amount, asset=asset
            )
            type_ = NoticePushType.NOTICE
    web_url = WebPagePath.PERPETUAL_STOP_HISTORY_RECORD.value
    client = ServerClient()
    client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                               dict(title=title, content=msg, url=web_url, type=type_))


@celery_task
def send_perpetual_position_close_notice_push(user_id, market, side, created_at=None):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    handler = PushBusinessHandler(None)
    if not handler.can_push([user_id]):
        return
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    with force_locale(app_lang):
        title = gettext('合约平仓提醒')
        if side == NoticePushType.SUCCESS:
            msg = gettext(
                '你的%(market_type_name)s%(market)s做空仓位已平。',
                market_type_name=_get_market_type_name(market), market=market
            )
        else:
            msg = gettext(
                '你的%(market_type_name)s%(market)s做多仓位已平。',
                market_type_name=_get_market_type_name(market), market=market
            )
        app_url = AppPagePath.PERPETUAL_ORDER.value.format(type='deal_record')
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
        handler.set_pushed([user_id])
    with force_locale(web_lang):
        title = gettext('合约平仓提醒')
        if side == NoticePushType.SUCCESS:
            msg = gettext(
                '你的%(market_type_name)s%(market)s做空仓位已平。',
                market_type_name=_get_market_type_name(market), market=market
            )
        else:
            msg = gettext(
                '你的%(market_type_name)s%(market)s做多仓位已平。',
                market_type_name=_get_market_type_name(market), market=market
            )

        web_url = WebPagePath.PERPETUAL_HISTORY_POSITION_RECORD.value

        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=title, content=msg, url=web_url, type=NoticePushType.SUCCESS))


@celery_task
def send_first_withdrawal_check_notice(user_id, created_at=None):
    user = User.query.get(user_id)

    message_popup_expired_at = now() + timedelta(days=3)
    params = {
        'email': user.email,
    }
    message = Message(
        user_id=user_id,
        title=MessageTitle.WITHDRAW_EMAIL_CHECK.name,
        content=MessageContent.WITHDRAW_EMAIL_CHECK.name,
        params=json.dumps(params),
        extra_info=json.dumps(
            dict(
                web_link=MessageWebLink.WITHDRAWAL_RECORD_PAGE.value,
                android_link="",
                ios_link="",
            )
        ),
        display_type=Message.DisplayType.POPUP_WINDOW,
        expired_at=message_popup_expired_at,
        channel=Message.Channel.DEPOSIT_WITHDRAWAL,
    )
    db.session.add(message)
    db.session.commit()
    handler = PushBusinessHandler(None)
    if not handler.can_push([user_id]):
        return
    app_lang = get_user_app_lang(user_id).value
    with force_locale(app_lang):
        title = gettext(MessageTitle.WITHDRAW_EMAIL_CHECK.value)
        msg = gettext(
            MessageContent.WITHDRAW_EMAIL_CHECK.value,
            email=user.email,
        )
        app_url = AppPagePath.WITHDRAW_RECORD.value
        send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)
        handler.set_pushed([user_id])


@celery_task
def send_auto_invest_profit_push(
        plan_id: int, user_id: int, source_asset: str, target_asset: str, profit_amount: str, profit_rate: str,
        notice_type: str, created_at: int = None,
):
    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    if limit_app_subscribe_notice(user_id):
        return
    app_lang = get_user_app_lang(user_id).value
    with force_locale(app_lang):
        if notice_type == AutoInvestPlanNoticeConfig.NoticeType.PROFIT_AMOUNT.name:
            title = gettext(MessageTitle.AUTO_INVEST_PROFIT_AMOUNT.value)
            msg = gettext(
                MessageContent.AUTO_INVEST_PROFIT_AMOUNT.value,
                target_asset=target_asset,
                source_asset=source_asset,
                profit_amount=profit_amount,
            )
        else:
            title = gettext(MessageTitle.AUTO_INVEST_PROFIT_RATE.value)
            msg = gettext(
                MessageContent.AUTO_INVEST_PROFIT_RATE.value,
                target_asset=target_asset,
                profit_rate=profit_rate,
            )

    app_url = AppPagePath.AUTO_INVEST_ORDER_DETAIL.value.format(id=str(plan_id))
    send_mobile_push_by_user_ids([user_id], title, msg, app_url, created_at=created_at)


@celery_task
def send_spot_grid_push(user_id: int, template: str, params: dict, created_at: int = None):
    if template == "spot_grid_take_profit":
        title = MessageTitle.SPOT_GRID_TAKE_PROFIT_TRIGGERED
        content = MessageContent.SPOT_GRID_TAKE_PROFIT_TRIGGERED
    elif template == "spot_grid_stop_loss":
        title = MessageTitle.SPOT_GRID_STOP_LOSS_TRIGGERED
        content = MessageContent.SPOT_GRID_STOP_LOSS_TRIGGERED
    elif template == "spot_grid_exceed_price_range":
        title = MessageTitle.SPOT_GRID_EXCEED_PRICE_RANGE
        content = MessageContent.SPOT_GRID_EXCEED_PRICE_RANGE
    else:
        return

    if User.query.get(user_id).user_type != User.UserType.NORMAL:
        return
    if limit_app_subscribe_notice(user_id):
        return
    app_lang = get_user_app_lang(user_id).value
    with force_locale(app_lang):
        title_ = gettext(title.value, **params)
        content_ = gettext(content.value, **params)
    app_url = AppPagePath.SPOT_GRID_DETAIL.value.format(id=str(params['strategy_id']))
    send_mobile_push_by_user_ids([user_id], title_, content_, app_url, created_at=created_at)


@celery_task
def send_pledge_push(user_id: int, template: str, params: dict, created_at: int = None):
    """ 借贷相关push """
    if template == "pledge_liq_warning":
        title = MessageTitle.PLEDGE_LIQ_WARNING
        content = MessageContent.PLEDGE_LIQ_WARNING
    elif template == "pledge_liq":
        title = MessageTitle.PLEDGE_LIQ
        content = MessageContent.PLEDGE_LIQ
    else:
        return
    handler = PushBusinessHandler(None)
    if not handler.can_push([user_id]):
        return
    app_lang = get_user_app_lang(user_id).value
    with force_locale(app_lang):
        title_ = gettext(title.value, **params)
        content_ = gettext(content.value, **params)
    send_mobile_push_by_user_ids([user_id], title_, content_, created_at=created_at)
    handler.set_pushed([user_id])


@celery_task
def send_channel_reward_activity_push(channel_reward_activity_id):
    channel_reward_activity = ChannelRewardActivity.query.filter(
        ChannelRewardActivity.id == channel_reward_activity_id,
        ChannelRewardActivity.status == ChannelRewardActivity.Status.FINISHED,
    ).first()
    if not channel_reward_activity:
        return
    user_rows = ChannelRewardHistory.query.filter(
        ChannelRewardHistory.channel_reward_activity_id == channel_reward_activity.id,
        ChannelRewardHistory.status == ChannelRewardHistory.Status.VALID,
    ).all()

    if channel_reward_activity.message_type == ChannelRewardActivity.MessageType.DEFAULT:
        for row in user_rows:
            db.session.add(Message(
                user_id=row.user_id,
                title=MessageTitle.ACTIVITY_REWARD_RECEIPT.name,
                content=MessageContent.ACTIVITY_REWARD_RECEIPT.name,
                params=json.dumps({
                    'activity': channel_reward_activity.business_name,
                    'reward': f'{amount_to_str(row.amount, PrecisionEnum.COIN_PLACES)}{row.asset}',
                }),
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.SPOT_ASSET_HISTORY_PAGE.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.POPUP_WINDOW,
                channel=Message.Channel.ACTIVITY,
            ))
    else:
        message_push = MessagePush.query.get(channel_reward_activity.message_temp_id)
        for row in user_rows:
            msg_row = Message.new_push_message(
                push_row=message_push,
                user_id=row.user_id,
                **message_push.jump_link_map,
            )
            db.session.add(msg_row)
        message_push.status = MessagePush.Status.FINISHED
    db.session.commit()


@celery_task
def send_staking_remove_success_push(user_id: int, asset: str):
    web_url = WebPagePath.INVESTMENT_RECORD.value + f'?pageType=staking&coin={asset}&operate=UNSTAKE'
    client = ServerClient()
    web_lang = get_user_web_lang(user_id).value
    with force_locale(web_lang):
        title = gettext("赎回申请提交成功")
        msg = gettext("查看赎回记录")
    client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                               dict(title=title,
                                    content='',
                                    url_text=msg, 
                                    url=web_url, type=NoticePushType.SUCCESS))


def send_push_by_business(user_id: int, 
                          push_params: dict | None, 
                          message_params: dict | None, business: str, type_: str):
    handler_map = {
        "COMMENT": send_comment_push,
        "DEMO_TRADING": send_demo_trading_push,
    }

    handler = handler_map.get(business)
    if handler:
        handler.delay(user_id, push_params, message_params, type_, current_timestamp(to_int=True))
    

@celery_task
def send_comment_push(user_id: int, push_params: dict | None, message_params: dict | None, type_: str, created_at: int):

    WEB_PUSH_FLAG = False  # 评论需求先不启用webpush

    _ban_reason_map = {
        'FRAUD': gettext('欺诈/诈骗'),
        'MALICIOUS': gettext('恶意/消极'),
        'SPAM': gettext('垃圾消息'),
        'FAKE': gettext('虚假互动'),
    }

    _ban_reason_non_trans_map = {
        'FRAUD': '欺诈/诈骗',
        'MALICIOUS': '恶意/消极',
        'SPAM': '垃圾消息',
        'FAKE': '虚假互动',
    }

    handler = PushBusinessHandler(AppPushBusiness.CommonGroupStrategyPush)
    can_push = True
    if type_ == "interaction" and not handler.can_push([user_id]):
        can_push = False
    if not push_params and not message_params:
        return
    app_title = app_content = web_title = web_content = ""
    msg_title = msg_content = msg_web_link = msg_android_link = msg_ios_link = ''
    web_url = app_url = ''
    msg_params = {}

    pref = UserPreferences(user_id)
    app_lang = get_user_app_lang(user_id).value
    web_lang = get_user_web_lang(user_id).value
    if type_ == "interaction":
        if push_params['total_count'] <= 0:
            return
        app_count = web_count = tip_count = 0
        if count := push_params.get('up_count'):
            with force_locale(web_lang):
                web_content += gettext('%(count)s位用户对您发表的内容点赞，', count=count)
            web_count += count
            if pref.app_comment_message_up_notice:
                with force_locale(app_lang):
                    app_content += gettext('%(count)s位用户对您发表的内容点赞，', count=count)
                app_count += count
        if count := push_params.get('reply_count'):
            with force_locale(web_lang):
                web_content += gettext('%(count)s位用户回复了您，', count=count)
            web_count += count
            if pref.app_comment_message_reply_notice:
                with force_locale(app_lang):
                    app_content += gettext('%(count)s位用户回复了您，', count=count)
                app_count += count
        if count := push_params.get('at_count'):
            with force_locale(web_lang):
                web_content += gettext('%(count)s位用户@了您，', count=count)
            web_count += count
            if pref.app_comment_message_at_notice:
                with force_locale(app_lang):
                    app_content += gettext('%(count)s位用户@了您，', count=count)
                app_count += count
        if count := push_params.get('tip_count'):
            amount_str = ', '.join([f'{amount}{asset}' for asset, amount in push_params.get('tip_amount', {}).items()])
            with force_locale(web_lang):
                if not web_content:
                    web_content = gettext('您发表的内容')
                web_content += gettext('收到%(count)s次打赏，打赏金额%(amount)s，', count=count, amount=amount_str)
            tip_count = count
            if pref.app_comment_message_at_notice:
                with force_locale(app_lang):
                    if not app_content:
                        app_content = gettext('您发表的内容')
                    app_content += gettext(
                        '%(count)s位用户打赏了您，打赏金额%(amount)s，', count=count, amount=amount_str
                    )
                app_count += count

            msg_title = MessageTitle.COMMENT_TIPS
            msg_content = MessageContent.COMMENT_TIPS
            msg_web_link = MessageWebLink.INTERACTIVE_MESSAGE.value
            msg_params = {
                "count": count,
                "amount": amount_str
            }

        if web_count or tip_count:
            with force_locale(web_lang):
                web_content += gettext('点击查看详情')
                if web_count:
                    web_title = gettext("收到%(count)s条互动消息", count=web_count)
                    if tip_count:
                        web_title += gettext("+%(count)s次打赏", count=tip_count)
                elif tip_count:
                    web_title = gettext("收到%(count)s次打赏", count=tip_count)

        if app_count or tip_count:
            with force_locale(app_lang):
                app_content += gettext('点击查看详情')
                if app_count:
                    app_title = gettext("收到%(count)s条互动消息", count=app_count)
                    if tip_count:
                        app_title += gettext("+%(count)s次打赏", count=tip_count)
                elif tip_count:
                    app_title = gettext("收到%(count)s次打赏", count=tip_count)

        web_url = WebPagePath.INTERACTIVE_MESSAGE.value
        app_url = AppPagePath.MESSAGE_CENTER.value.format(type="interactive")
    elif type_ == "ban":
        if ban_days := push_params.get('ban_days'):
            with force_locale(web_lang):
                web_title = gettext('账号禁言%(count)s天', count=ban_days)
                web_content = gettext(
                    '由于您的账号币种评论内容违规，您的账号%(count)s天无法发表币种评论和回复。', count=ban_days)
            with force_locale(app_lang):
                app_title = gettext('账号禁言%(count)s天', count=ban_days)
                app_content = gettext(
                    '由于您的账号币种评论内容违规，您的账号%(count)s天无法发表币种评论和回复。', count=ban_days)
        else:
            with force_locale(web_lang):
                web_title = gettext('账号永久禁言')
                web_content = gettext('由于您的账号币种评论内容违规，您的账号永久无法发表币种评论和回复。')
            with force_locale(app_lang):
                app_title = gettext('账号永久禁言')
                app_content = gettext('由于您的账号币种评论内容违规，您的账号永久无法发表币种评论和回复。')
        msg_title = MessageTitle.COMMENT_BAN_FOR_DAYS if ban_days else MessageTitle.COMMENT_BAN_FOR_EVER
        msg_content = MessageContent.COMMENT_BAN_FOR_DAYS if ban_days else MessageContent.COMMENT_BAN_FOR_EVER

        reason = push_params['reason']

        reason_list = reason.split(',')
        reason_list = [_ban_reason_non_trans_map.get(r) for r in reason_list if _ban_reason_non_trans_map.get(r)]
        if not reason:
            current_app.logger.error(f"Comment unknown ban reason: {push_params['reason']}")
            reason_list = [_ban_reason_non_trans_map['FRAUD'], ]  # default
        msg_params = {
            'count': ban_days,
            'need_translates': [
                {
                    'name': 'reason',
                    'text': '{reason}',
                    'params': {'reason': reason_list}
                }
            ]
        }
        web_url = WebPagePath.MESSAGE_DETAIL.value
        app_url = AppPagePath.MESSAGE_CENTER.value.format(type="message")
    elif type_ == "warning":
        app_title = web_title = push_params.get('title')
        app_content = web_content = push_params.get('content')
        web_url = WebPagePath.MESSAGE_DETAIL.value
        app_url = AppPagePath.MESSAGE_CENTER.value.format(type="message")

        msg_title = MessageTitle.COMMENT_WARNING
        msg_content = MessageContent.COMMENT_WARNING
        msg_params = {
            "title": app_title,
            "content": app_content
        }

    message = None
    if msg_title and msg_content:
        message = Message(
            user_id=user_id,
            title=msg_title,
            content=msg_content,
            params=json.dumps(msg_params),
            extra_info=json.dumps(
                dict(
                    web_link=msg_web_link,
                    android_link=msg_android_link,
                    ios_link=msg_ios_link,
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.SYSTEM,
        )
        db.session_add_and_commit(message)

    if app_title and app_content and can_push:
        send_mobile_push_by_user_ids([user_id], app_title, app_content, app_url, created_at=created_at)

    if WEB_PUSH_FLAG and web_title and web_content and can_push:
        if web_url == WebPagePath.MESSAGE_DETAIL.value:
            if message:
                web_url += f'?id={message.id}'
            else:
                web_url = WebPagePath.MESSAGE.value
        client = ServerClient()
        client.notice_user_message(user_id, WebPushChannelType.NORMAL.value,
                                   dict(title=web_title,
                                        content=web_content,
                                        url_text='',
                                        url=web_url, type=NoticePushType.NOTICE))
    if type_ == "interaction":
        handler.set_pushed([user_id])


@celery_task
def send_demo_trading_push(user_id: int, push_params: dict | None, message_params: dict | None, type_: str, created_at: int):
    app_lang = get_user_app_lang(user_id).value
    with force_locale(app_lang):
        push_params_ = {k: gettext(v) for k, v in push_params.items()}
        title = gettext(message_params['title'])
        content = gettext(message_params['content'], **push_params_)
        send_mobile_push_by_user_ids([user_id], title, content, message_params['app_url'],
                                     created_at=current_timestamp(to_int=True))

