import json
import socket
from datetime import datetime
from logging import getLogger
from typing import Dict, Any, List

from app import config
from app.common import get_country
from app.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, User, Withdrawal
from app.utils import (GeoIP, RESTClient, amount_to_str, current_timestamp,
                       datetime_to_time)

_logger = getLogger(__name__)


class AntiFraudClient:

    def __init__(self, use_udp=False) -> None:
        conf = config['CLIENT_CONFIGS']['ant_fraud_server']
        self._address = conf['host'], conf['port']
        self.client = RESTClient(config['CLIENT_CONFIGS']['ant_fraud_server']['url'])
        if use_udp:
            self._udp_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    def post(self, path: str, _json: dict):
        r = self.client.post(path, json=_json)
        if (code := r.get('code', -1)) != 0:
            raise RESTClient.BadResponse(code, r.get('message', r))
        return r['data']

    def get(self, path: str, params: dict):
        r = self.client.get(path, **params)
        if (code := r.get('code', -1)) != 0:
            raise RESTClient.BadResponse(code, r.get('message', r))
        return r['data']

    def put(self, path: str, _json: dict):
        r = self.client.put(path, json=_json)
        if (code := r.get('code', -1)) != 0:
            raise RESTClient.BadResponse(code, r.get('message', r))
        return r['data']

    def delete(self, path: str, params: dict):
        r = self.client.delete(path, **params)
        if (code := r.get('code', -1)) != 0:
            raise RESTClient.BadResponse(code, r.get('message', r))
        return r['data']

    def report(self, type_: str, data: dict):
        if sock := getattr(self, '_udp_sock', None):
            msg = json.dumps({'type': type_, 'data': data})
            try:
                sock.sendto(msg.encode(), self._address)
            except Exception as e:
                _logger.error(e)
        else:
            self.post("/data", {"type": type_, "data": data})

    def user_assessment(self, user_id: int):
        """normal, risk, unconfimred, banned"""
        return self.post('/user/assessment', {'user_id': user_id})

    def report_device(self, user_id: int, device_type: str, device_id: str, device_info: Dict[str, Any]):
        self.report(
            'device_info', dict(
                time=current_timestamp(),
                user_id=user_id,
                device_type=device_type,
                device_id=device_id,
                device_info=device_info
            )
        )

    def report_registration(self, user: User, user_agent: str):
        if not (location := user.location_code):
            if country := get_country(GeoIP(user.registration_ip).country_code):
                location = country.iso_3

        self.report('registration_info', dict(
            user_id=user.id,
            time=datetime_to_time(user.created_at),
            email=user.email,
            ip=user.registration_ip,
            location=location,
            user_agent=user_agent
        ))

    def report_login(self, log: LoginHistory):
        location = log.location  # LoginHistory 的location 是中文，需要转换一次
        if country := get_country(GeoIP(log.ip).country_code):
            location = country.iso_3

        data = dict(
            user_id=log.user_id,
            time=datetime_to_time(log.created_at),
            ip=log.ip,
            location=location,
            user_agent=log.user_agent
        )
        self.report('login_log', data)

    def report_event_log(self, user_id: int, page: str, event: str):
        self.report('event_log', dict(
            user_id=user_id,
            time=current_timestamp(),
            page=page,
            event=event
        ))

    def report_kyc(self, user_id: int, kyc_update_time: datetime, kyc_type: str, kyc_status: str):
        self.report('kyc_info', dict(
            user_id=user_id,
            time=datetime_to_time(kyc_update_time),
            kyc_type=kyc_type,
            kyc_status=kyc_status,
        ))

    def report_sms(
        self, user_id: int, sms_send_time: datetime, mobile_num: str, mobile_country_code: str, mobile_country: str,
        sms_business_code: str, has_user_binded_mobile: bool,
    ):
        self.report('sms_send_log', dict(
            user_id=user_id,
            time=datetime_to_time(sms_send_time),
            mobile_num=mobile_num,
            mobile_country_code=mobile_country_code,
            country=mobile_country,
            business=sms_business_code,
            has_user_binded_mobile=has_user_binded_mobile,
        ))

    def report_user_balance_history(
        self, user_id: int, time: int, account: int, asset: str, change: str, balance: str
    ):
        self.report('balance_history_log', dict(
            user_id=user_id,
            time=time,
            account=account,
            asset=asset,
            change=change,
            balance=balance,
        ))

    def report_transfer_log(self, w: Withdrawal):
        self.report('transfer_log', dict(
            from_user_id=w.user_id,
            to_user_id=w.recipient_user_id,
            time=datetime_to_time(w.created_at),
            asset=w.asset,
            amount=amount_to_str(w.amount),
        ))

    def get_risk_user(self, user_id: int = None, page: int = 1, limit: int = 100, export: bool = False):
        params = dict(page=page, limit=limit, export=export)
        if user_id:
            params['user_id'] = user_id
        return self.get("/user/risk", params)

    def batch_get_risk_users(self, user_ids: List[int] = None, page: int = 1, limit: int = 100, export: bool = False):
        params = dict(page=page, limit=limit, export=export)
        if user_ids:
            params['user_ids'] = user_ids
        return self.post("/user/risk/by_user_ids", params)

    def add_or_update_risk_user(self, user_id: int, edit_user_id: int, remark: str):
        return self.post("/user/risk", dict(user_id=user_id, edit_user_id=edit_user_id, remark=remark))

    def delete_risk_user(self, user_id: int, edit_user_id: int):
        return self.post(f"/user/risk/delete", dict(user_id=user_id, edit_user_id=edit_user_id))

    def import_risk_users(self, edit_user_id, risk_users: List[Dict[str, Any]]):
        return self.post('/user/risk/import', dict(edit_user_id=edit_user_id, risk_users=risk_users))

    def get_feature_list(self, feature_name: str = None, feature_type: str = None, identity_type: str = None,
                         page: int = 1, limit: int = 100):
        params = dict(page=page, limit=limit)
        if feature_name is not None:
            params['feature_name'] = feature_name
        if feature_type is not None:
            params['feature_type'] = feature_type
        if identity_type is not None:
            params['identity_type'] = identity_type
        return self.get("/feature", params)

    def update_feature(self, feature_name: str, description: str = None):
        return self.post('/feature', dict(feature_name=feature_name, description=description))

    def get_feature_config_list(self, feature_name: str, config_name: str, config_key: str = None, export: bool = False,
                                page: int = 1, limit: int = 100):
        params = dict(
            feature_name=feature_name, config_name=config_name, export=export, page=page, limit=limit
        )
        if config_key is not None:
            params['config_key'] = config_key
        return self.get("/feature/config", params)

    def create_or_update_feature_config(
            self, feature_name: str, config_name: str, config_key: str, config_value: str = None):
        return self.post('/feature/config', dict(
            feature_name=feature_name, config_name=config_name, config_key=config_key, config_value=config_value,
        ))

    def delete_feature_config(self, feature_name: str, config_name: str, config_key: str):
        return self.delete('/feature/config', dict(
            feature_name=feature_name, config_name=config_name, config_key=config_key
        ))

    def get_risk_user_list(self, risk_model_name: str, user_id: int = None, cal_time: str = None,
                           export: bool = False, page: int = 1, limit: int = 100):
        params = dict(
            risk_model_name=risk_model_name, export=export, page=page, limit=limit
        )
        if user_id is not None:
            params['user_id'] = user_id
        if cal_time is not None:
            params['cal_time'] = cal_time
        return self.get("/risk/user", params)

    def get_risk_user_info_list(self, risk_model_name: str, user_id: int = None, start_time: int = None,
                                end_time: int = None, page: int = 1, limit: int = 100):
        params = dict(
            risk_model_name=risk_model_name, page=page, limit=limit
        )
        if user_id is not None:
            params['user_id'] = user_id
        if start_time is not None:
            params['start_time'] = start_time
        if end_time is not None:
            params['end_time'] = end_time
        return self.get("/risk/user/info", params)

    def import_risk_user_info(self, risk_model_name: str, manual_annotation_infos: dict):
        return self.post("/risk/user/info", dict(
            risk_model_name=risk_model_name, manual_annotation_infos=manual_annotation_infos,
        ))

    def export_risk_user_info(self, risk_model_name: str, start_time: int, end_time: int):
        return self.get("/risk/user/info/export", dict(
            risk_model_name=risk_model_name, start_time=start_time, end_time=end_time,
        ))

    def update_risk_user(self, risk_model_name: str, risk_user_id: int, description: str = None):
        return self.post('/risk/user', dict(
            risk_model_name=risk_model_name, risk_user_id=risk_user_id, description=description))

    def update_risk_model(self, risk_model_name: str, description: str = None):
        return self.post('/risk/model', dict(risk_model_name=risk_model_name, description=description))

    def get_risk_model_list(self):
        return self.get('/risk/model', {})
