# -*- coding: utf-8 -*-
import datetime
from decimal import Decimal

from bs4 import BeautifulSoup
from werkzeug.datastructures import MultiDict

from app.common.constants import PrecisionEnum
from ...utils import RESTClient, batch_iter, quantize_amount
from ...config import config


class CMCAPIClient:

    def __init__(self):
        _conf = config['QUOTES_CLIENT_CONFIG']['cmc']
        headers = {
            "X-CMC_PRO_API_KEY": _conf["api_key"],
            "Accept": "application/json"
        }
        self._client = RESTClient(_conf['url'],
                                  headers=headers)

    def get_ids_circulation(self, ids: list):
        url = '/v2/cryptocurrency/quotes/latest'
        result = {}

        def _format_circulation(_value: str | None) -> Decimal:
            if _value is not None:
                return Decimal(_value)
            else:
                return Decimal('0')

        for _bids in batch_iter(ids, 200):
            id_str = ','.join(map(str, _bids))
            r = self._client.get(
                url,
                **dict(id=id_str)
            )

            for v in r['data'].values():
                self_reported_c_supply = _format_circulation(v['self_reported_circulating_supply'])
                c_supply = _format_circulation(v['circulating_supply'])
                circulation = c_supply or self_reported_c_supply
                if circulation > 0:
                    result[v["id"]] = dict(
                        asset=v["symbol"],
                        circulation=circulation,
                        name=v["name"],
                        id=v["id"],
                        rank=v["cmc_rank"])
        return result

    def get_asset_listings_latest(self):
        # 当前加密货币总数：8k+
        r0 = self._get_asset_listings_latest(dict(
            limit=5000,
            start=1,
        ))
        r1 = self._get_asset_listings_latest(dict(
            limit=5000,
            start=5001,
        ))
        ret = []
        for v in r0['data'] + r1['data']:
            circulation = Decimal(v["circulating_supply"])
            if circulation == Decimal():
                try:
                    circulation = Decimal(v["self_reported_circulating_supply"])
                except TypeError:
                    pass
            price = Decimal(v['quote'].get('USD', {}).get('price') or Decimal())
            ret.append(
                dict(
                    asset=v["symbol"],
                    price=price,
                    circulation=circulation,
                    total_supply=Decimal(v['total_supply']),
                    name=v["name"],
                    id=v["id"],
                    rank=v["cmc_rank"]
                )
            )
        ret = MultiDict([(v["asset"], v) for v in ret])
        for k, v in list(ret.items()):
            if k.upper() not in ret:
                ret[k.upper()] = v
        return ret

    def get_asset_usd_prices(self) -> dict[str, list[dict]]:
        """ 获取CMC的币种USD汇率 """
        r0 = self._get_asset_listings_latest(
            dict(
                limit=5000,
                start=1,
            )
        )
        r1 = self._get_asset_listings_latest(
            dict(
                limit=5000,
                start=5001,
            )
        )
        asset_rates_map = dict()
        for v in r0['data'] + r1['data']:
            price = Decimal(v['quote'].get('USD', {}).get('price') or Decimal())
            price = quantize_amount(price, PrecisionEnum.PRICE_PLACES)
            if not price:
                continue
            symbol = v["symbol"].upper()
            d = dict(
                id=v["id"],
                usd_price=price,
            )
            asset_rates_map.setdefault(symbol, []).append(d)
        return asset_rates_map

    def _get_asset_listings_latest(self, params: dict = None):
        _params = dict(
            limit=5000
        )
        _params = params or _params
        return self._client.get(
            "/v1/cryptocurrency/listings/latest",
            **_params
        )

    def get_historical_assets_volume(self, date: datetime.date):
        params = dict(
            limit=5000,
            date=date.strftime('%Y-%m-%d'),
        )
        result = self._client.get(
            "/v1/cryptocurrency/listings/historical",
            **params
        )
        data = result["data"]
        ret = []
        for item in data:
            volume_mapping = item['quote']['USD']
            ret.append(dict(
                asset=item['symbol'],
                volume_24h=volume_mapping['volume_24h'],
            ))
        return MultiDict([(v["asset"], v) for v in ret])

    def get_exchange_assets(self, exchange_id):
        result = self._client.get(
            "/v1/exchange/market-pairs/latest",
            **{
                'limit': 5000,
                'id': exchange_id,
            }
        )
        ret = {}
        data = result["data"]
        for market_pair in data['market_pairs']:
            category = market_pair['category']
            market_pair_base = market_pair['market_pair_base']
            market_pair_quote = market_pair['market_pair_quote']
            ret.setdefault(category, set()).add((market_pair_base['currency_id'], market_pair_base['currency_symbol']))
            ret.setdefault(category, set()).add((market_pair_quote['currency_id'], market_pair_quote['currency_symbol']))
            ret.setdefault('markets', list()).append({
                'category': category,
                'base_currency_symbol': market_pair_base['currency_symbol'],
                'quote_currency_symbol': market_pair_quote['currency_symbol'],
                'base_currency_id': market_pair_base['currency_id'],
                'quote_currency_id': market_pair_quote['currency_id'],
            })

        return exchange_id, ret

    def get_price_from_cmc_frontend(self, id_: int):
        import requests
        params = dict(
            id=str(id_),
            start=1,
            limit=1,
            sort='cmc_rank_advanced',
            category='spot'
        )
        url = 'https://api.coinmarketcap.com/data-api/v3/cryptocurrency/market-pairs/latest'
        ret = requests.get(url, params=params).json()
        if data := ret['data']['marketPairs']:
            return Decimal(data[0]['price'])
        return 0

    def get_market_pairs_by_id(self, id_: int):
        params = dict(
            id=str(id_),
            start=1,
            limit=1,
            sort='cmc_rank_advanced',
            category='spot'
        )

        return self.get_market_pairs(params)

    def get_market_pairs(self, params: dict):
        """
        {
          "status": {
            "timestamp": "2025-03-31T08:52:08.617Z",
            "error_code": 0,
            "error_message": null,
            "elapsed": 72,
            "credit_count": 1,
            "notice": "Some exchange details have redacted market details (indicated with \"-1\"). See the API FAQ for details."
          },
          "data": {
            "id": 1,
            "name": "Bitcoin",
            "symbol": "BTC",
            "num_market_pairs": 10,
            "market_pairs": [
              {
                "exchange": {
                  "id": 270,
                  "name": "Binance",
                  "slug": "binance"
                },
                "outlier_detected": 0,
                "exclusions": null,
                "market_pair_base": {
                  "exchange_symbol": "BTC",
                  "currency_symbol": "BTC",
                  "currency_id": 1,
                  "currency_type": "cryptocurrency"
                },
                "market_pair_quote": {
                  "exchange_symbol": "FDUSD",
                  "currency_symbol": "FDUSD",
                  "currency_id": 26081,
                  "currency_type": "cryptocurrency"
                },
                "quote": {
                  "exchange_reported": {
                    "price": "82274.51",
                    "volume_24h_base": "18013.94309692",
                    "volume_24h_quote": "1482088341.4672904",
                    "last_updated": "2025-03-31T08:50:59.000Z"
                  },
                  "USD": {
                    "price": "82113.37873108",
                    "volume_24h": "1479177904.2310185",
                    "depth_negative_two": "10516914.06457554",
                    "depth_positive_two": "8104710.18824824",
                    "last_updated": "2025-03-31T08:50:59.000Z"
                  }
                },
                "market_id": 1262381,
                "market_pair": "BTC/FDUSD",
                "category": "spot",
                "fee_type": "percentage"
              },
            ]
          }
        }
        """
        api = '/v2/cryptocurrency/market-pairs/latest'
        ret = self._client.get(api, **params)
        if ret['status']['error_code'] != 0:
            return 0
        pair_dic = ret['data']['market_pairs'][0]
        return Decimal(pair_dic["quote"]['USD']['price'])


class CoinGeckoClient:

    def __init__(self):
        _conf = config['QUOTES_CLIENT_CONFIG']['coingecko']
        headers = {
            "Accept": "application/json"
        }
        self._client = RESTClient(_conf['url'],
                                  headers=headers)

    def get_ids_circulation(self, ids: list):
        url = "/api/v3/coins/markets"

        def _format_circulation(_value: str | None) -> Decimal:
            if _value is not None:
                return Decimal(_value)
            else:
                return Decimal('0')

        result = {}
        for _bids in batch_iter(ids, 200):
            id_str = ','.join(map(str, _bids))
            r = self._client.get(
                url,
                **dict(ids=id_str, vs_currency="usd")
            )
            for v in r:
                circulation = _format_circulation(v["circulating_supply"])
                if circulation > 0:
                    result[v["id"]] = dict(
                        asset=v["symbol"].upper(),
                        circulation=circulation,
                        id=v["id"]
                    )
        return result


class TokenInsightClient:

    def __init__(self):
        _conf = config['QUOTES_CLIENT_CONFIG']['token_insight']
        headers = {
            "Accept": "application/json",
            "TI_API_KEY": _conf["api_key"]
        }
        self._client = RESTClient(_conf['url'], headers=headers)

    @classmethod
    def process_html(cls, html_content: str):
        """
        处理HTML，只保留白名单中的标签

        :param html_content: 要处理的HTML字符串
        :return: 处理后的HTML字符串
        """
        # 定义允许的标签白名单
        whitelist = {
            'h1', 'h2', 'h3',  # 标题
            'p',
            'strong', 'b', 'em', 'i',  # 文本强调
            'ol', 'ul', 'li',  # 列表
            'a', 'span'  # 链接和替换标签
        }

        # 创建BeautifulSoup对象
        soup = BeautifulSoup(html_content, 'html.parser')

        # 递归处理所有标签
        for tag in soup.find_all(True):
            # 如果标签不在白名单中
            if tag.name not in whitelist:
                # 保留标签内的文本
                tag.unwrap()

        # 处理链接：移除href属性
        for a_tag in soup.find_all('a'):
            # 移除href属性
            a_tag.unwrap()

        # 处理加粗标签：确保使用<strong>
        for b_tag in soup.find_all('b'):
            strong = soup.new_tag('strong')
            strong.string = b_tag.get_text()
            b_tag.replace_with(strong)

        # 处理斜体标签：确保使用<em>
        for i_tag in soup.find_all('i'):
            em = soup.new_tag('em')
            em.string = i_tag.get_text()
            i_tag.replace_with(em)

        # 返回处理后的HTML字符串
        return soup

    def get_detail_by_id(self, api_id: str) -> dict | None:
        detail_url = f"/api/v1/coins/{api_id}"
        res = self._client.get(detail_url)
        if (status := res["status"]) and status["code"] == 0:
            community = res['data']['community']
            resources = res['data']['resource']
            localization = res['data']['localization']
            en_short_description = ''
            cn_short_description = ''
            en_long_description = ''
            cn_long_description = ''

            for _localization in localization:
                lang = _localization['lang']
                if lang == 'en':
                    en_short_description = _localization['description_short']
                    en_long_description = self.process_html(_localization['description'])
                if lang == 'cn':
                    cn_short_description = _localization['description_short']
                    cn_long_description = self.process_html(_localization['description'])

            return {
                'official_website': website[0] if (website := res['data']['website']) else '',
                'white_paper': white_paper[0] if (white_paper := resources['whitepaper']) else '',
                'report_url': doc[0] if (doc := resources['doc']) else '',
                'source_code': source_code[0] if (source_code := res['data']['code']) else '',
                'telegram': community['telegram'],
                'facebook': community['facebook'],
                'twitter': community['twitter'],
                'reddit': community['reddit'],
                'medium': community['medium'],
                'discord': community['discord'],
                'youtube': community['youtube'],
                'instagram': community['instagram'],
                'en_short_description': en_short_description,
                'cn_short_description': cn_short_description,
                'en_long_description': en_long_description,
                'cn_long_description': cn_long_description
            }
        return None
