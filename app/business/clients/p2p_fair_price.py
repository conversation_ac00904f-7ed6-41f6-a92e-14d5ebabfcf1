import json
from decimal import Decimal

import numpy as np
from flask import current_app

from app import config
from app.business import send_alert_notice, PriceManager
from app.caches import FiatUSDPricesCache
from app.caches.p2p import P2pSiteFairPriceCache, P2pFiatLimitCache
from app.common import P2pBusinessType
from app.models import db, P2pFairPrice, P2pFairPriceSnapshot, P2pFiatFairPrice, P2pFiatFairPriceSnapshot
from app.models.mongo.p2p.config import P2pFiatConfigMySQL
from app.utils import RESTClient, current_timestamp, quantize_amount, now
from app.utils.parser import JsonEncoder


class BaseFairPriceCrawl:
    platform: P2pFairPrice.Platform
    url: str
    headers = {
        'accept': 'application/json',
        'content-type': 'application/json;charset=UTF-8',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                      'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
    }

    def __init__(self, fiat: str, asset: str, side: P2pBusinessType):
        self.fiat = fiat
        self.side = side
        self.asset = asset
        self.client = RESTClient("https://", headers=self.headers)

    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        raise NotImplementedError

    def handle_resp_data(self, data):
        raise NotImplementedError

    def extract_price_list(self, data):
        price_list = [Decimal(i['price']) for i in data]
        return price_list

    def calc_fair_price(self, price_list) -> Decimal:
        if not price_list:
            return Decimal(0)
        mean = Decimal(np.mean(price_list))
        std = Decimal(np.std(price_list))
        # 平均值±2.5倍标准差为有效区间
        offset = Decimal('2.5') * std
        valid_prices = [i for i in price_list if mean - offset <= i <= mean + offset]
        if valid_prices:
            return self.get_price_by_side(valid_prices, self.side)
        else:
            # current_app.logger.warning(f"{self.platform} valid prices is empty: {price_list}")
            return Decimal(0)

    @staticmethod
    def get_price_by_side(prices, side):
        # 买币区取最小值为该平台买一价，买币区取最大值为该平台卖一价
        if side == P2pBusinessType.BUY:
            return min(prices)
        elif side == P2pBusinessType.SELL:
            return max(prices)

    def get_fair_price(self):
        try:
            resp_data = self.request_data(self.fiat, self.asset, self.side)
            data = self.handle_resp_data(resp_data)
            price_list = self.extract_price_list(data)
        except Exception:
            # current_app.logger.warning(f"{self.platform} request p2p price error: {e}")
            return Decimal(0)
        price = self.calc_fair_price(price_list)
        return price


class BinanceFairPriceCrawl(BaseFairPriceCrawl):
    platform = P2pFairPrice.Platform.BINANCE
    url = "p2p.binance.com/bapi/c2c/v2/friendly/c2c/adv/search"

    @RESTClient.retry(3, timeout=5)
    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        side_map = {
            P2pBusinessType.BUY: "BUY",
            P2pBusinessType.SELL: "SELL",
        }
        params = {
            "fiat": fiat,
            "page": 1,
            "rows": 20,
            "tradeType": side_map[side],
            "asset": asset,
            "countries": [],
            "proMerchantAds": False,
            "shieldMerchantAds": False,  # 是否仅显示神盾广告方
            "filterType": "all",  # 是否限可交易广告
            "periods": [],
            "additionalKycVerifyFilter": 1,  # 是否无需认证的广告商
            "publisherType": None,
            "payTypes": [],
            "classifies": [
                "mass",
                "profession",
                "fiat_trade"
            ]
        }
        resp_data = self.client.post(self.url, json=params)
        return resp_data

    def handle_resp_data(self, data):
        if data['code'] != "000000":
            # current_app.logger.warning(f"{self} request p2p price error: {data}")
            return []
        return data["data"]

    def extract_price_list(self, data):
        price_list = [Decimal(i['adv']['price']) for i in data]
        return price_list


class BybitFairPriceCrawl(BaseFairPriceCrawl):
    platform = P2pFairPrice.Platform.BYBIT
    url = "api2.bybit.com/fiat/otc/item/online"

    @RESTClient.retry(3, timeout=10)
    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        side_map = {
            P2pBusinessType.BUY: "1",
            P2pBusinessType.SELL: "0",
        }
        params = {
            "userId": "",
            "tokenId": asset,
            "currencyId": fiat,
            "payment": [],
            "paymentPeriod": [],
            "side": side_map[side],
            "size": "20",
            "page": "1",
            "amount": "",
            "authMaker": False,
            "canTrade": False,
            "itemRegion": 2,
            "sortType": "TRADE_PRICE",
        }
        resp_data = self.client.post(self.url, json=params)
        return resp_data

    def handle_resp_data(self, data):
        if data['ret_code'] != 0:
            # current_app.logger.warning(f"{self.platform} request p2p price error: {data}")
            return []
        return data["result"]["items"]


class OKXFairPriceCrawl(BaseFairPriceCrawl):
    platform = P2pFairPrice.Platform.OKX
    url = "www.okx.com/v3/c2c/tradingOrders/getMarketplaceAdsPrelogin"
    side_map = {
        P2pBusinessType.BUY: "buy",
        P2pBusinessType.SELL: "sell",
    }

    @RESTClient.retry(3, timeout=5)
    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        sort_map = {
            P2pBusinessType.BUY: "price_desc",
            P2pBusinessType.SELL: "price_asc",
        }
        params = {
            'side': self.side_map[side],
            'paymentMethod': 'all',
            'userType': 'all',
            'hideOverseasVerificationAds': 'false',
            'sortType': sort_map[side],
            'limit': '100',
            'cryptoCurrency': asset,
            'fiatCurrency': fiat,
            'currentPage': '1',
            'numberPerPage': '20',
            't': int(current_timestamp() * 1000),
        }
        resp_data = self.client.get(self.url, **params)
        return resp_data

    def handle_resp_data(self, data):
        if data['code'] != 0:
            current_app.logger.warning(f"{self.platform} request p2p price error: {data}")
            return []
        return data["data"][self.side_map[self.side]]


class BitgetFairPriceCrawl(BaseFairPriceCrawl):
    # 产品要求暂时不使用 Bitget 价格
    # platform = P2pFairPrice.Platform.BITGET
    url = "www.bitget.com/v1/p2p/pub/adv/queryAdvList"

    @RESTClient.retry(3, timeout=5)
    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        side_map = {
            P2pBusinessType.BUY: "1",
            P2pBusinessType.SELL: "2",
        }
        params = {
            "side": side_map[side],
            "pageNo": 1,
            "pageSize": 20,
            "coinCode": asset,
            "fiatCode": fiat,
            "languageType": 5
        }
        resp_data = self.client.post(self.url, json=params)
        return resp_data

    def handle_resp_data(self, data):
        if data['code'] != "00000":
            current_app.logger.warning(f"{self.platform} request p2p price error: {data}")
            return []
        return data["data"]["dataList"]

    def extract_price_list(self, data):
        price_list = [Decimal(i['priceValue']) for i in data]
        return price_list


class CoinexFairPriceCrawl(BaseFairPriceCrawl):
    platform = P2pFairPrice.Platform.COINEX

    def get_fair_price(self):
        price = P2pSiteFairPriceCache().read_fiat_price(self.fiat)
        return price


class FairPriceBiz:

    def __init__(self, fiat, asset="USDT"):
        self.fiat = fiat
        self.asset = asset

    def get_fair_price_map(self):
        fair_price_map = {}
        fail_platform = []
        for crawl in BaseFairPriceCrawl.__subclasses__():
            # 跳过关闭的渠道
            if not getattr(crawl, "platform", None):
                continue
            buy_price = crawl(self.fiat, self.asset, P2pBusinessType.BUY).get_fair_price()
            sell_price = crawl(self.fiat, self.asset, P2pBusinessType.SELL).get_fair_price()

            if buy_price and sell_price:
                fair_price = (buy_price + sell_price) / 2
            elif buy_price:
                fair_price = buy_price
            else:
                fair_price = sell_price

            if fair_price:
                fair_price_map[crawl.platform] = fair_price
            else:
                fail_platform.append(crawl.platform.name)

        self.alter_zero_price(fail_platform, self.fiat)
        return fair_price_map

    @staticmethod
    def calc_fair_price(price_map):
        # 优先使用站内价格
        if price := price_map.get(P2pFairPrice.Platform.COINEX):
            return price
        else:
            sort_prices = sorted(price_map.values())
            # 取中位数
            price = np.median(sort_prices)
        return price

    @classmethod
    def alter_zero_price(cls, platforms, fiat):
        if not platforms:
            return
        plat_str = '，'.join(platforms)
        err_msg = f"P2P公允价格爬虫无法获取 {plat_str} 的 {fiat} 价格数据"
        send_alert_notice(
            err_msg,
            config["ADMIN_CONTACTS"].get("p2p_mer_act"),
            expired_seconds=86400,
            at=config["ADMIN_CONTACTS"]['slack_at'].get("p2p_mer_act")
        )
        current_app.logger.warning(f"{plat_str} get {fiat} price failed")

    def handle_fair_price(self):
        price_map = self.get_fair_price_map()
        if price_map:
            price = self.calc_fair_price(price_map)
        else:
            price = Decimal()
        model = P2pFairPrice
        obj = model.query.filter(model.fiat == self.fiat).first()
        source_data = {k.name: str(v) for k, v in price_map.items()}
        if obj:
            if obj.price == price:
                return
            else:
                obj.price = price
                obj.source_data = source_data
        else:
            obj = P2pFairPrice(
                fiat=self.fiat,
                price=price,
                source_data=source_data,
            )
            db.session_add_and_flush(obj)
        self.save_fair_snapshot(obj)
        db.session.commit()
        return obj

    @staticmethod
    def save_fair_snapshot(obj):
        snap = json.loads(json.dumps(obj.to_dict(), cls=JsonEncoder))
        row = P2pFairPriceSnapshot(
            snap_at=now(),
            snap=snap,
            source_id=obj.id,
            fiat=obj.fiat
        )
        db.session.add(row)

    @classmethod
    def refresh_all_fair_price(cls):
        rows = P2pFiatConfigMySQL.get_all_valid_data()
        fiats = [i.fiat for i in rows]
        for fiat in fiats:
            try:
                FairPriceBiz(fiat).handle_fair_price()
            except Exception as e:
                current_app.logger.error(f"Update fiat fair price failed: {fiat}")
                current_app.logger.exception(e)


class MerActFairPriceBiz:

    @classmethod
    def handle_mer_fair_price(cls, fiats):
        model = P2pFiatFairPrice
        rows = model.query.filter(model.fiat.in_(fiats))
        mer_fair_price_map = {i.fiat: i for i in rows}
        # 查询法币公平价格，并同步到 商家法币公平价格
        fair_price_map = cls.query_fiats_fair_price_map(fiats)
        for fiat in fiats:
            fair_price_row = fair_price_map.get(fiat)
            if not fair_price_row:
                current_app.logger.warning(f"Base fiat price not found: {fiat}")
                continue
            price = quantize_amount(fair_price_row.price, 2)
            if obj := mer_fair_price_map.get(fiat):
                if obj.price == fair_price_row.price:
                    continue
                obj.price = fair_price_row.price
            else:
                obj = P2pFiatFairPrice(
                    fiat=fiat,
                    price=price,
                )
                db.session_add_and_flush(obj)
            cls.save_fair_snapshot(obj)
        db.session.commit()
        return obj

    @classmethod
    def query_fiats_fair_price_map(cls, fiats):
        base_model = P2pFairPrice
        base_rows = base_model.query.filter(
            base_model.fiat.in_(fiats)
        ).all()
        base_map = {i.fiat: i for i in base_rows}
        return base_map

    @staticmethod
    def save_fair_snapshot(obj):
        snap = json.loads(json.dumps(obj.to_dict(), cls=JsonEncoder))
        row = P2pFiatFairPriceSnapshot(
            snap_at=now(),
            snap=snap,
            source_id=obj.id,
            fiat=obj.fiat
        )
        db.session.add(row)

    @classmethod
    def refresh_all_mer_fair_price(cls):
        model = P2pFiatFairPrice
        rows = model.query.with_entities(model.fiat).all()
        fiats = [i.fiat for i in rows]
        try:
            cls.handle_mer_fair_price(fiats)
        except Exception:
            current_app.logger.exception(f"Update mer-act fair price failed")


class P2pFiatConfigPriceBiz:

    @classmethod
    def refresh_fiats_conf_price(cls, fiats=None):
        if not fiats:
            fiats = P2pFiatConfigMySQL.get_all_valid_data()
            fair_prices = P2pFairPrice.query.all()
            third_prices = FiatUSDPricesCache().get_prices()
        else:
            fiats = P2pFiatConfigMySQL.query.filter(P2pFiatConfigMySQL.fiat.in_(fiats)).all()
            fair_prices = P2pFairPrice.query.filter(P2pFairPrice.fiat.in_([item.mongo_id for item in fiats])).all()
            third_prices = FiatUSDPricesCache().get_prices(fiats)
        
        price_map = {i.fiat: i.price for i in fair_prices}
        usdt_price = PriceManager.asset_to_usd("USDT")

        for obj in fiats:
            price = price_map.get(obj.fiat)
            source = P2pFiatConfigMySQL.Sources.FAIR_PRICE
            if not price:
                if third := third_prices.get(obj.fiat):
                    # p2p场景下，使用u本位
                    price = quantize_amount(usdt_price / third, obj.precision) if third else Decimal()
                    source = P2pFiatConfigMySQL.Sources.POLYGON
            
            if price and obj.auto_price != price:
                obj.auto_price = price
                obj.source = source.name
        
        db.session.commit()
        P2pFiatLimitCache.reload()
