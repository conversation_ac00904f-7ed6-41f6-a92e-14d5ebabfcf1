# -*- coding: utf-8 -*-
from decimal import Decimal
from enum import IntEnum
from typing import List, Dict

from flask import current_app

from app.config import config
from app.utils import RESTClient, amount_to_str


class StrategyResponseCode(IntEnum):
    GRID_EXISTS = 201
    GRID_NOT_EXISTS = 202


class StrategyClient:

    def __init__(self):
        conf = config['CLIENT_CONFIGS']['amm']
        self.client = RESTClient(conf['url'])

    @staticmethod
    def _check_error(data):
        if (code := data.get('code', -1)) != 0:
            raise RESTClient.BadResponse(code, data.get('message', data))

    def new_spot_grid(self, *, user_id: int, market: str,
                      lowest_price: Decimal, highest_price: Decimal,
                      grid_price_gap: Decimal, entry_price: Decimal,
                      buy_amount: Decimal, sell_amount: Decimal,
                      quote_asset_precision: int, base_asset_precision: int,
                      maker_fee_rate: Decimal, taker_fee_rate: Decimal
                      ):
        payload = {
            "user_id": user_id,  # user_id 对应 策略id, 每个user只能同时运行一个策略
            "market": market,
            "min_price": amount_to_str(lowest_price),
            "max_price": amount_to_str(highest_price),
            "spread": amount_to_str(grid_price_gap),
            "entry_price": amount_to_str(entry_price),
            "buy_amount": amount_to_str(buy_amount),
            "sell_amount": amount_to_str(sell_amount),
            "base_asset_precision": base_asset_precision,
            "quote_asset_precision": quote_asset_precision,
            "maker_fee_rate": amount_to_str(maker_fee_rate),
            "taker_fee_rate": amount_to_str(taker_fee_rate),
        }
        r = self.client.post("/grid", payload)
        if r.get('code', -1) != 0:
            current_app.logger.warning(f"execute_new_spot_grid:{user_id} {payload} error_response {r}")
        if r.get('code', -1) == StrategyResponseCode.GRID_EXISTS:
            current_app.logger.warning(f"execute_new_spot_grid:{user_id} GRID_EXISTS")
            return True
        self._check_error(r)
        return True

    def stop_spot_grid(self, *, user_id: int):
        r = self.client.post(f"/grid/stop/{user_id}")
        self._check_error(r)

    def start_spot_grid(self, *, user_id: int):
        r = self.client.post(f"/grid/start/{user_id}")
        self._check_error(r)

    def terminate_spot_grid(self, *, user_id: int) -> bool:
        r = self.client.delete(f"/grid/{user_id}")
        if r.get('code', -1) == StrategyResponseCode.GRID_NOT_EXISTS:
            current_app.logger.warning(f"terminate_spot_grid:{user_id} GRID_NOT_EXISTS")
            return True
        self._check_error(r)
        return True

    def batch_update(self, *, data_list: List[Dict]):
        # item.keys: id, maker_fee_rate, taker_fee_rate
        r = self.client.put(f"/grid/batch", data_list)
        self._check_error(r)
        return True
