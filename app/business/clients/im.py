import random
import socket
from enum import Enum
from hashlib import sha256

from flask import current_app

from app import config
from app.utils import RESTClient, now, current_timestamp


class ImContentType(Enum):
    TEXT = 1
    IMAGE = 2
    VIDEO = 3
    TEXT_NOTICE = 1001
    ORDER_CARD = 1002
    SAY_HI = 1003


class SystemContentLevel(Enum):
    INFO = "info"
    WARNING = "warning"


class ImServerClient:

    def __init__(self, *, use_udp: bool = False):
        conf = config['CLIENT_CONFIGS']['im_server']
        self.client = RESTClient(conf['url'])
        if use_udp:
            self._sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    @RESTClient.retry(3, timeout=5)
    def post(self, path: str, data: dict):
        r = self.client.post(path, json=data)
        if (code := r.get('code', -1)) != 0:
            current_app.logger.error(f"request im error: {code}, {r.get('message', r)}")
        return r['data']

    @classmethod
    def _generate_client_msg_id(cls, send_id: str):
        now_str = now().strftime("%Y-%m-%d %H:%M:%S")
        hash_str = "-".join([now_str, send_id, str(random.randint(0, 2 ** 32))])
        return sha256(hash_str.encode()).hexdigest()

    def send_text_notification(self, send_id: str, recv_id: str, content: str,
                               content_type=ImContentType.TEXT_NOTICE):
        return self.post("/internal/send_system_notification", dict(
            client_msg_id=self._generate_client_msg_id(send_id),
            content=content,
            content_type=content_type.value,
            recv_id=recv_id,
            send_id=send_id,
            create_time=current_timestamp(to_int=True),
        ))

    def get_order_unread_seqs(self, user_id, session_ids: list = None):
        data = {"user_id": user_id}
        if session_ids:
            data["convo_ids"] = session_ids
        ret = self.post("/internal/get_user_has_unread_seqs", data)
        return ret

    def get_order_im_msg(self, im_user_id, session_id, order_by="ASC"):
        """
        接口详情查看 https://yapi.coinex.viadeploy.com/project/23/interface/api/4415
        """
        msgs = []
        begin = 0
        batch = 2000
        while True:
            params = dict(
                user_id=im_user_id,
                seq_ranges=[dict(
                    convo_id=session_id,
                    begin=begin,
                    end=begin + batch,
                    num=2000
                )],
                order=order_by
            )
            data = self.post("/internal/sync_message", params)
            if not data:
                return msgs
            msg_info = data[0]
            msgs.extend(msg_info["msgs"])
            if msg_info["is_end"]:
                break
            begin += batch
        return msgs

    def add_block(self, owner_biz_user_id: str, block_biz_user_id: str):
        data = {'owner_user_id': owner_biz_user_id, 'block_user_id': block_biz_user_id}
        ret = self.post("/internal/add_block", data)
        return ret

    def remove_block(self, owner_biz_user_id: str, block_biz_user_id: str):
        data = {'owner_user_id': owner_biz_user_id, 'block_user_id': block_biz_user_id}
        ret = self.post("/internal/remove_block", data)
        return ret
