# -*- coding: utf-8 -*-

from decimal import Decimal
from typing import Dict

from flask import current_app

from ...config import config
from ...utils import RESTClient


class JuheClient:

    def __init__(self, api_keys: Dict[str, str] = None):
        self._api_keys = api_keys or config['JUHE']
        self._client = RESTClient('http://op.juhe.cn',
                                  logger=current_app.logger)

    def get_exchange_rate(self, from_currency: str, to_currency: str
                          ) -> Decimal:
        response = self._client.get('/onebox/exchange/currency', **{
            'key': self._api_keys.get('exchange_rate'),
            'from': from_currency,
            'to': to_currency
        })
        if response.get('error_code'):
            return Decimal()

        for item in response['result']:
            if item['currencyF'] == from_currency:
                return Decimal(item['result'])

        return Decimal()


class OpenExchangeClient:
    def __init__(self):
        self._api_key = config['OPEN_EXCHANGE']
        self._client = RESTClient('https://openexchangerates.org', logger=current_app.logger)

    def get_exchange_rates(self) -> Dict[str, Decimal]:
        # 默认使用USD作为基础货币, 免费版也仅支持USD
        response = self._client.get("/api/latest.json", **{
            "app_id": self._api_key,
            "show_alternative": False
        })
        if not response.get("rates"):
            return {}

        return {
            c: Decimal(v) for c, v in dict(response['rates']).items()
        }


class PolygonClient:
    def __init__(self):
        self._api_key = config['POLYGON']['api_key']
        self._client = RESTClient('https://api.polygon.io', logger=current_app.logger)

    def get_exchange_rate(self, from_currency: str, to_currency: str) -> Decimal:
        response = self._client.get(f"/v1/conversion/{from_currency}/{to_currency}", **{
            "apiKey": self._api_key,
            "precision": 8,  
        })
        return Decimal(response.get('converted', 0))


class FixerClient:
    def __init__(self):
        self._api_key = config['FIXER']['api_key']
        self._client = RESTClient('http://data.fixer.io', logger=current_app.logger)

    def get_exchange_rates(self) -> Dict[str, Decimal]:
        # 默认使用EUR作为基础货币, 免费版不支持USD
        response = self._client.get(f"/api/latest", **{
            "access_key": self._api_key,
        })
        if not response.get("rates"):
            return {}
        return {
            c: Decimal(v) for c, v in dict(response['rates']).items()
        }