# -*- coding: utf-8 -*-
import json
import socket
from logging import getLogger
from typing import Any, Iterator, List
from urllib.parse import urlencode

from app import config
from app.common.events import EventTag, EventMetrics
from app.utils import RESTClient, current_timestamp

_logger = getLogger(__name__)


class BizMonitorClient:
    def __init__(self):
        conf = config['CLIENT_CONFIGS']['biz_monitor']
        self.client = RESTClient(conf['url'])

    def _handle_response(self, response_data):
        if (status := response_data.get('code', -1)) != 0:
            raise RESTClient.BadResponse(status, response_data)
        return response_data['data']

    @RESTClient.retry(3, timeout=5)
    def report_metric(self, data: bytes):
        response = self.client.post("/v1/metrics", data=data)
        resp = self._handle_response(response)
        return resp

    def get_metrics_chart_data(
            self,
            start_time: int,
            end_time: int,
            metric_id: int,
            metric_type: int,
            period: str,
            label_ids: List[int]
    ):
        label_mapper_data = self.get_metrics(
            start_time=start_time,
            end_time=end_time,
            metric_id=metric_id,
            metric_type=metric_type,
            period=period
        )

        if 0 in label_ids:
            data = self.get_metric_tag_data(
                start_time=start_time,
                end_time=end_time,
                metric_id=metric_id,
                metric_type=metric_type,
                period=period
            )
            label_mapper_data["0"] = data
        return {int(key): data for key, data in label_mapper_data.items() if int(key) in label_ids}

    def get_metrics(
            self,
            start_time: int,
            end_time: int,
            metric_id: int,
            metric_type: int,
            period: str,
            label_id: int = None
    ):
        """查询 API 访问次数趋势数"""
        params = dict(
            start_time=start_time,
            end_time=end_time,
            metric_id=metric_id,
            metric_type=metric_type,
            period=period
        )
        if label_id:
            params["label_id"] = label_id
        response = self.client.get("/internal/metrics", **params)
        data = self._handle_response(response)
        return data

    def get_metric_tag_data(
        self,
        start_time: int,
        end_time: int,
        metric_id: int,
        metric_type: int,
        period: str
    ):
        """查询标签tag聚合数据"""
        label_ids = self.get_metric_tags(metric_id)
        label_ids.append(0)
        params = dict(
            start_time=start_time,
            end_time=end_time,
            metric_id=metric_id,
            metric_type=metric_type,
            period=period,
            label_ids=label_ids
        )
        url_params = urlencode(params, doseq=True)
        response = self.client.get(f"/internal/metrics/{metric_id}?{url_params}")
        data = self._handle_response(response)
        return data

    def get_metric_tags(self, metric_id: int):
        """查询指标标签"""
        response = self.client.get("/internal/metric/labels", **{
            "metric_id": metric_id
        })
        data = self._handle_response(response)
        if not data:
            return []
        return data

    def get_rw_metric_data(self,
                           metric_id: int,
                           metric_type: int,
                           label_ids: List[int]
                           ):
        now_ = current_timestamp(to_int=True)
        params = dict(
            time=now_,
            metric_type=metric_type,
            metric_id=metric_id
        )
        response = self.client.get('/internal/rw-metrics', **params)
        data = self._handle_response(response)
        res = dict()
        if not data:
            return res
        for label_id in data:
            l_id = int(label_id)
            if l_id in label_ids:
                res[l_id] = data[label_id]
        if 0 in label_ids:
            res[0] = data['-1']
        return res


class BizMonitor:

    def __init__(self, dummy: bool = False):
        conf = config['CLIENT_CONFIGS']['biz_monitor_server']
        self._address = conf['host'], conf["port"]
        self._dummy = dummy
        if not dummy:
            self._sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    def _emit(self, data):
        if self._dummy:
            return
        if isinstance(data, dict):
            data = [data]
        data = json.dumps(data, ensure_ascii=False).encode('utf-8')
        # noinspection PyBroadException
        try:
            self._sock.sendto(data, self._address)
        except Exception as e:
            _logger.error(e)

    @classmethod
    def platform_to_event_tag(cls, platform):
        event_tag = EventTag.WEB
        if platform.is_ios():
            event_tag = EventTag.IOS
        elif platform.is_android():
            event_tag = EventTag.ANDROID
        elif platform.is_web():
            event_tag = EventTag.WEB
        return event_tag

    @classmethod
    def get_source_tag(cls):
        from app.api.common import get_request_platform
        platform = get_request_platform()
        return cls.platform_to_event_tag(platform)

    def increase_counter(self, event: EventMetrics, value: int = 1, tag: EventTag = None, *, with_source=False):
        """上报统计值"""
        if with_source:
            tag = self.get_source_tag()

        self._emit(dict(
            id=event.value,
            label=tag.value if tag else 0,
            c=value
        ))

    def increase_uniq_counter(
            self,
            event: EventMetrics,
            value: Iterator[Any],
            tag: EventTag = None,
            *,
            with_source=False
    ):
        """上报唯一值"""
        if with_source:
            tag = self.get_source_tag()
        self._emit(dict(
            id=event.value,
            label=tag.value if tag else 0,
            uc=[str(v) for v in value]
        ))

    def increase_guage(self, event: EventMetrics, value: int, tag: EventTag = None, *, with_source=False):
        """上报瞬间值"""
        if with_source:
            tag = self.get_source_tag()
        self._emit(dict(
            id=event.value,
            label=tag.value if tag else 0,
            g=value
        ))


biz_monitor = BizMonitor()
