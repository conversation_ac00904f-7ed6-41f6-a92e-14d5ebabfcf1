from types import MappingProxyType
from typing import (NamedTuple, Set, Iterable)

from ....utils import RESTClient
from ....config import config
from .base import BaseKYTClient

# Mature and Emerging network
_CHAIN_TO_ME_NETWORK = MappingProxyType({
    'ALGO': 'Algorand',
    'ARBITRUM': 'Arbitrum',
    'AVA_C': 'Avalanche',
    'BASE': 'Base',
    'BSC': 'Binance_Smart_Chain',
    'BTC': 'Bitcoin',
    'BCH': 'Bitcoin_Cash',
    'CELO': 'Celo',
    'CRONOS': 'Cronos',
    'DASH': 'Dash',
    'DOGE': 'Dog<PERSON>oin',
    'EOSIO': 'EOS',
    'ERC20': 'Ethereum',
    'ETC': 'Ethereum_Classic',
    'FTM': 'Fantom',
    'LTC': 'Litecoin',
    'OPTIMISM': 'Optimism',
    'MATIC': 'Polygon',
    'SOL': 'Solana',
    'TRC20': 'Tron',
    'XRP': 'XRP',
    'ZEC': 'Zcash',
})
## not support yet
#_CHAIN_TO_PREGROTH_NETWORK = MappingProxyType({
#    'KAR': 'Acala',
#    'ELF': 'Aelf',
#    'AE': 'Aeternity',
#    'AGORIC': 'Agoric',
#    'AKASH': 'Akash',
#    'ALEO': 'Aleo',
#    'ALPH': 'Alephium',
#    'APTOS': 'Aptos',
#    'ARBITRUM_NOVA': 'Arbitrum_Nova',
#    'ARCH': 'Archway',
#    'ARDR': 'Ardor',
#    'ARK': 'Ark',
#    'AR': 'Arweave',
#    'ASTR': 'Astar',
#    'AURORA': 'Aurora',
#    'AVA': 'Avalanche_X_Chain',
#    'BAND': 'Bandchain',
#    'BEAM': 'Beam',
#    'BNC': 'Bifrost',
#    'BSV': 'Bitcoin_Satoshi_Vision',
#    'KUB': 'Bitkub',
#    'BTS': 'Bitshares',
#    'TAO': 'Bittensor',
#    'BLAST': 'Blast',
#    'BOBA': 'Boba',
#    'CANTO': 'Canto',
#    'ADA': 'Cardano',
#    'CSPR': 'Casper',
#    'CELESTIA': 'Celestia',
#    'XCH': 'Chia',
#    'CHILIZ': 'Chiliz2',
#    'CLV': 'CLV',
#    'CCD': 'Concordium',
#    'CFX': 'Conflux',
#    'DAG': 'Constellation',
#    'CTXC': 'Cortex',
#    'ATOM': 'Cosmos',
#    'DCR': 'Decred',
#    'DEFI': 'DeFiChain',
#    'DESO': 'Deso',
#    'DGB': 'Digibyte',
#    'DYDX': 'DYDX',
#    'DYMENSION': 'Dymension',
#    'XEC': 'Ecash',
#    'ELA': 'Elastos_Main_Chain',
#    'EGLD': 'Elrond',
#    'EWC': 'Energy_Web',
#    'ENJIN': 'Enjin_Relaychain',
#    'ERG': 'Ergo',
#    'ETHPOW': 'Ethereum_PoW',
#    'EVMOS': 'Evmos',
#    'FET': 'Fetch_AI',
#    'FIL': 'Filecoin',
#    'FIRO': 'Firo',
#    'FLARE': 'Flare',
#    'FSN': 'Fusion',
#    'GRIN': 'Grin',
#    'ONE': 'Harmony',
#    'HTR': 'Hathor',
#    'XHV': 'Haven',
#    'HECO': 'HECO',
#    'HBAR': 'Hedera',
#    'HIVE': 'Hive',
#    'ZEN': 'Horizen',
#    'ICX': 'Icon',
#    'INJ': 'Injective',
#    'ICP': 'Internet_Computer',
#    'IOST': 'Iost',
#    'IOTA': 'Iota',
#    'IOTX': 'Iotex',
#    'IRIS': 'IRISnet',
#    'KDA': 'Kadena',
#    'KAI': 'Kardiachain',
#    'KAR': 'Karura',
#    'KAS': 'Kaspa',
#    'KAVA': 'Kava',
#    'KLAY': 'Klaytn',
#    'KMD': 'Komodo',
#    'KUJIRA': 'Kujira',
#    'KSM': 'Kusama',
#    'LINEA': 'Linea',
#    'LUKSO': 'Lukso',
#    'MANTA': 'Manta_Pacific',
#    'MANTLE': 'Mantle',
#    'MERLIN': 'Merlin_Chain',
#    'MTL': 'Metal',
#    'METIS': 'Metis',
#    'MINA': 'Mina',
#    'MOB': 'MobileCoin',
#    'MONA': 'Monacoin',
#    'XMR': 'Monero',
#    'GLMR': 'Moonbeam',
#    'MOVR': 'Moonriver',
#    'NEAR': 'Near',
#    'NEM': 'Nem',
#    'NEO3': 'Neo',
#    'CKB': 'Nervos',
#    'NTRN': 'Neutron',
#    'NIMIQ': 'Nimiq',
#    'ROSE': 'Oasis',
#    'OAS': 'Oasys',
#    'ONT': 'Ontology',
#    'ORAI': 'Oraichain',
#    'OSMO': 'Osmosis',
#    'XPRT': 'Persistence',
#    'COMPOSABLE': 'Picasso',
#    'PIVX': 'Pivx',
#    'LAT': 'PlatON',
#    'POKT': 'Pocket_Network',
#    'DOT': 'Polkadot',
#    'POLYMESH': 'Polymesh',
#    'XPR': 'Proton',
#    'QTUM': 'Qtum',
#    'XRD': 'Radix',
#    'RVN': 'Ravencoin',
#    'RON': 'Ronin',
#    'RSK': 'RSK',
#    'SAGA': 'Saga',
#    'SCRT': 'Secret',
#    'SEI': 'Sei',
#    'SDN': 'Shiden',
#    'SC': 'Sia',
#    'SEP20': 'smartBCH',
#    'SXP': 'Solar',
#    'SGB': 'Songbird',
#    'STX': 'Stacks',
#    'STARKNET': 'Starknet',
#    'XLM': 'Stellar',
#    'STRATIS': 'Stratis',
#    'STRD': 'Stride',
#    'SUI': 'Sui',
#    'XYM': 'Symbol',
#    'SYS': 'Syscoin',
#    'TELOS': 'Telos_Zero',
#    'LUNC': 'Terra',
#    'LUNA': 'Terra2',
#    'XTZ': 'Tezos',
#    'THETA': 'Theta',
#    'HTR': 'Thorchain',
#    'TON': 'TON',
#    'VECHAIN': 'VeChain',
#    'VLX': 'Velas',
#    'VENOM': 'Venom',
#    'XVG': 'Verge',
#    'VTC': 'Vertcoin',
#    'VITE': 'Vite',
#    'WAN': 'Wanchain',
#    'WAVES': 'Waves',
#    'WAXP': 'Wax',
#    'WEMIX': 'Wemix',
#    'XDC': 'XinFin',
#    'XPLA': 'XPLA',
#    'ZIL': 'Zilliqa',
#    'ZETA': 'Zetachain_ZEVM',
#    'ZKSYNC': 'zkSync2'
#})



# https://support.chainalysis.com/hc/en-us/articles/16492397142292-Category-IDs
_ERR_CATEGORY_ID = -1 # 自定义
_ERR_DESCRIPTION = 'screening failed'
_CATEGORY_IDs = {  # do not remove
    _ERR_CATEGORY_ID: _ERR_DESCRIPTION,
    1: 'Child Abuse Material',
    2: 'Darknet Market',
    3: 'Sanctioned Entity',
    6: 'Stolen Funds',
    12: 'Ransomware',
    13: 'Mixing',
    16: 'Gambling',
    18: 'Scam',
    23: 'Terrorist Financing',
    25: 'Sanctioned Jurisdiction',
    28: 'Fraud Shop',
    29: 'Illicit Actor-Org',
    30: 'Infrastructure as a service',
    34: 'Special Measures',
    35: 'Malware',
    36: 'Online Pharmacy',
    39: 'Seized Funds',
    41: 'Unnamed service',
    999: 'Custom Address',
}  # 风控定义的文档：https://docs.google.com/spreadsheets/d/1tOScS8bSOf0_8VmN5mptB6fxMTL9QggLIPp6bNKDJrA/edit?gid=0#gid=0

#
# class _PreScreenRating:
#     HIGH_RISK = 'highRisk'
#     LOW_RISK = 'lowRisk'
#     UNKNOWN = 'unknown'


class _ChainalysisRiskLevel:
    LOW = 'LOW'
    MEDIUM = 'MEDIUM'
    HIGH = 'HIGH'
    SEVERE = 'SEVERE'


_ChainalysisRiskLevel2int = {
    _ChainalysisRiskLevel.LOW: 0,
    _ChainalysisRiskLevel.MEDIUM: 1,
    _ChainalysisRiskLevel.HIGH: 2,
    _ChainalysisRiskLevel.SEVERE: 3,
}
_ChainalysisUnSafeRiskLevels = {
    _ChainalysisRiskLevel.HIGH, _ChainalysisRiskLevel.SEVERE}


class Assessment(NamedTuple):
    risk_level: str
    is_safe: bool


class ChainalysisClient(BaseKYTClient, RESTClient):
    _base_url = 'https://api.chainalysis.com/api/kyt'
    default_user = 'default_user'
    CATEGORY_IDs = _CATEGORY_IDs
    ERR_CATEGORY_ID = _ERR_CATEGORY_ID
    CHAIN_TO_ME_NETWORK = _CHAIN_TO_ME_NETWORK

    def __init__(self, **kwargs):
        try:
            token = config['KYT_CONFIG']['chainalysis']["token"]
        except KeyError:
            token = ''
        super().__init__(
            self._base_url,
            headers={'Token': f'{token}'},
            **kwargs)

    @classmethod
    def list_supported_chains(cls) -> Iterable[str]:
        return set(_CHAIN_TO_ME_NETWORK)

    @classmethod
    def is_chain_supported(cls, chain: str) -> bool:
        return chain in _CHAIN_TO_ME_NETWORK

    def get_transaction_assessment(self, external_id: str) -> Assessment:
        if not external_id:
            raise Exception('argument error')
        result = self.get(f'/v2/transfers/{external_id}/alerts')
        alerts = result.get('alerts')
        if not alerts:
            return Assessment(_ChainalysisRiskLevel.LOW, True)
        unique_alerts = set()
        for alert in alerts:
            unique_alerts.add(alert['alertLevel'])
        risk_level = self._get_highest_risk_level(unique_alerts)
        return Assessment(risk_level, self._is_safe(risk_level))

    @classmethod
    def _get_highest_risk_level(cls, alerts: Set[str]) -> str:
        highest_risk = max([_ChainalysisRiskLevel2int[alert]
                            for alert in alerts])
        int2risk_level = {v: k for k, v in _ChainalysisRiskLevel2int.items()}
        return int2risk_level[highest_risk]

    @classmethod
    def _is_safe(cls, risk_level: str) -> bool:
        return bool(risk_level not in _ChainalysisUnSafeRiskLevels)

    def is_transaction_processed(self, external_id: str) -> bool:
        if not external_id:
            raise Exception('argument error')
        result = self.get(f'/v2/transfers/{external_id}')
        return bool(result.get('updatedAt'))

    def register_transaction(
            self, asset: str, chain: str, tx_id: str,
            recipient: str, user_id: str = None) -> str:
        if not user_id:
            user_id = self.default_user
        network = _CHAIN_TO_ME_NETWORK.get(chain)
        if not network:
            raise Exception(f'chain {chain} not support by chainalysis')
        if not asset or not tx_id or not recipient:
            raise Exception(f'argument error')
        params = {
            'network': network,
            'asset': asset,
            'transferReference': f'{tx_id}:{recipient}',
            'direction': "received",
        }
        result = self.post(f'/v2/users/{user_id}/transfers', json=params)
        return result['externalId']

    def register_withdrawal_addresses(
            self,
            user_id: int,
            asset: str,
            chain: str,
            address: str
    ) -> int | None:
        network = _CHAIN_TO_ME_NETWORK.get(chain)
        if not network:
            raise Exception(f'chain {chain} not support by chainalysis')
        if not asset or not user_id or not address:
            raise Exception(f'argument error')
        schema = {
            'network': network,
            'asset': asset,
            'address': address,
        }
        params = [schema]
        result = self.post(f'/v1/users/{user_id}/withdrawaladdresses', json=params)
        ret = result[0]
        if cluster := ret.get('cluster'):
            return cluster['categoryId']
        return None
