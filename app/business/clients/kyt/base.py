from enum import Enum
from typing import Tuple, List, NamedTuple


class RiskLevel(Enum):
    UNKNOWN = 'UNKNOWN'
    HIGH = 'HIGH'
    MEDIUM = 'MEDIUM'
    LOW = 'LOW'
    SEVERE = 'SEVERE'


class RiskScore:
    class Tag(NamedTuple):
        name: str
        impact: int

    def __init__(self, score: float, tags: List[Tag] = None):
        self._score = min(100., score)
        if not tags:
            self._tags = []
        else:
            if len(set(tag.name for tag in tags)) != len(tags):
                raise Exception('tags are overlapping')
            self._tags = tags

    @property
    def score(self) -> float:
        return self._score

    @property
    def tags(self) -> List[Tag]:
        return self._tags

    def __repr__(self):
        def trunc_tags():
            _max = 3
            if len(self._tags) <= _max:
                return self._tags
            _tags = self._tags[:_max]
            _tags = ', '.join(repr(_tag) for _tag in _tags)
            return f'[{_tags}, ...]'

        return f'{type(self).__name__}({self._score}, {trunc_tags()})'


class UsageQuota(NamedTuple):

    type: str
    used: int
    total: int
    time_used: int
    time_total: int
    remark: str = ''
    
    @property
    def used_ratio(self) -> float:
        return self.used / self.total

    @property
    def time_progress(self) -> float:
        return self.time_used / self.time_total


class BaseKYTClient:

    RiskLevel = RiskLevel
    RiskScore = RiskScore
    UsageQuota = UsageQuota

    def get_address_risk_info(
            self,
            address: str,
            chain: str) -> Tuple[RiskScore, RiskLevel]:
        raise NotImplementedError

    def get_usage_quotas(self) -> List[UsageQuota]:
        raise NotImplementedError
