# -*- coding: utf-8 -*-
from typing import List

from app import config
from app.models import UserApiFrequencyLimitRecord as FrequencyLimit
from app.utils import RESTClient

API_TYPE_PATH_MAPPING = {
    FrequencyLimit.ApiGroups.QUERY_ACCOUNT: [
        "GET:/v1/balance/",
        "GET:/v1/balance/info",
        "GET:/v1/account/market/fee",
        "GET:/v1/account/amm/balance",
        "GET:/v1/account/investment/balance",
        "GET:/v1/credit/info",
        "GET:/v1/credit/balance",
        "GET:/v1/sub_account/auth/api",
        "GET:/v1/sub_account/auth/api/:id_",
        "GET:/v1/balance/deposit/address/:coin_type",

        "GET:/v2/account/subs",
        "GET:/v2/account/subs/api-detail",
        "GET:/v2/account/subs/info",
        "GET:/v2/account/subs/api",
        "GET:/v2/account/subs/spot-balance",
        "GET:/v2/account/trade-fee-rate",
        "GET:/v2/assets/spot/balance",
        "GET:/v2/assets/futures/balance",
        "GET:/v2/assets/margin/balance",
        "GET:/v2/assets/financial/balance",
        "GET:/v2/assets/amm/liquidity",
        "GET:/v2/assets/credit/info",
        "GET:/v2/assets/deposit-address",
        "GET:/v2/assets/deposit-withdraw-config",
        "GET:/v2/assets/amm/income-history"
    ],
    FrequencyLimit.ApiGroups.ACCOUNT: [
        "PUT:/v1/account/settings",
        "POST:/v1/margin/loan",
        "POST:/v1/margin/flat",
        "POST:/v1/margin/transfer",
        "PUT:/v1/sub_account/frozen",
        "PUT:/v1/sub_account/unfrozen",
        "PUT:/v1/sub_account/auth/api/:id_",
        "POST:/v1/sub_account/transfer",
        "POST:/v1/sub_account/register",
        "POST:/v1/sub_account/auth/api",
        "DELETE:/v1/sub_account/auth/api/:id_",
        "PUT:/v1/balance/deposit/address/:coin_type",
        "POST:/v1/balance/coin/withdraw",
        "DELETE:/v1/balance/coin/withdraw",
        "POST:/v1/contract/balance/transfer",

        "POST:/v2/account/subs",
        "POST:/v2/account/subs/frozen"
        "POST:/v2/account/subs/unfrozen",
        "POST:/v2/account/subs/api",
        "POST:/v2/account/subs/edit-api",
        "POST:/v2/account/subs/delete-api",
        "POST:/v2/account/subs/transfer",
        "POST:/v2/account/settings",
        "POST:/v2/assets/margin/borrow",
        "POST:/v2/assets/margin/repay",
        "POST:/v2/assets/renewal-deposit-address",
        "POST:/v2/assets/withdraw",
        "POST:/v2/assets/cancel-withdraw",
        "POST:/v2/assets/transfer",
        "POST:/v2/assets/amm/add-liquidity",
        "POST:/v2/assets/amm/remove-liquidity"
    ],
    FrequencyLimit.ApiGroups.QUERY_ACCOUNT_HISTORY: [
        "GET:/v1/balance/coin/withdraw",
        "GET:/v1/balance/coin/deposit",
        "GET:/v1/account/balance/history",
        "GET:/v1/investment/transfer/history",
        "GET:/v1/margin/transfer/history",
        "GET:/v1/margin/loan/history",
        "GET:/v1/contract/transfer/history",
        "GET:/v1/sub_account/transfer/history",

        "GET:/v2/account/subs/transfer-history",
        "GET:/v2/assets/margin/borrow-history",
        "GET:/v2/assets/margin/interest-limit",
        "GET:/v2/assets/deposit-history",
        "GET:/v2/assets/withdraw",
        "GET:/v2/assets/transfer-history"
    ],
    FrequencyLimit.ApiGroups.ORDER: [
        "POST:/v1/order/limit",
        "POST:/v1/order/market",
        "POST:/v1/order/stop/limit",
        "POST:/v1/order/stop/market",
        "POST:/v1/order/ioc",
        "POST:/v1/order/modify",
        "POST:/v1/order/stop/modify",

        "POST:/v2/spot/order",
        "POST:/v2/spot/stop-order",
        "POST:/v2/spot/modify-order",
        "POST:/v2/spot/modify-stop-order",
        "POST:/v2/spot/batch-order",
        "POST:/v2/spot/batch-stop-order"
    ],
    FrequencyLimit.ApiGroups.CANCEL: [
        "DELETE:/v1/order/pending",
        "DELETE:/v1/order/stop/pending/:order_id",
        "POST:/v2/spot/cancel-order",
        "POST:/v2/spot/cancel-stop-order",
        "POST:/v2/spot/cancel-batch-order",
        "POST:/v2/spot/cancel-batch-stop-order",
    ],
    FrequencyLimit.ApiGroups.ORDERS: [
        "POST:/v1/order/limit/batch",
    ],
    FrequencyLimit.ApiGroups.CANCELS: [
        "POST:/v2/spot/cancel-all-order",
        "POST:/v2/spot/cancel-order-by-client-id",
        "POST:/v2/spot/cancel-stop-order-by-client-id"
        "DELETE:/v1/order/pending/batch",
        "DELETE:/v1/order/stop/pending",
        "DELETE:/v1/order/pending/by_client_id",
        "DELETE:/v1/order/stop/pending/by_client_id",
    ],
    FrequencyLimit.ApiGroups.QUERY_ORDER: [
        "GET:/v1/order/status",
        "GET:/v1/order/status/batch",
        "GET:/v1/order/pending",
        "GET:/v1/order/stop/pending",

        "GET:/v2/spot/order-status",
        "GET:/v2/spot/batch-order-status",
        "GET:/v2/spot/pending-order",
        "GET:/v2/spot/pending-stop-order"
    ],
    FrequencyLimit.ApiGroups.QUERY_ORDER_HISTORY: [
        "GET:/v1/order/deals",
        "GET:/v1/order/user/deals",
        "GET:/v1/order/finished",
        "GET:/v1/order/stop/finished",

        "GET:/v2/spot/finished-order",
        "GET:/v2/spot/finished-stop-order",
        "GET:/v2/spot/user-deals",
        "GET:/v2/spot/order-deals"
    ],
    # 合约接口 -----------
    FrequencyLimit.ApiGroups.PERPETUAL_ORDER: [
        "POST:/perpetual/v1/order/put_limit",
        "POST:/perpetual/v1/order/put_market",
        "POST:/perpetual/v1/order/put_stop_limit",
        "POST:/perpetual/v1/order/put_stop_market",
        "POST:/perpetual/v1/position/adjust_margin",
        "POST:/perpetual/v1/position/stop_loss",
        "POST:/perpetual/v1/position/take_profit",
        "POST:/perpetual/v1/position/market_close",
        "POST:/perpetual/v1/order/modify",
        "POST:/perpetual/v1/order/modify_stop",

        "POST:/v2/futures/order",
        "POST:/v2/futures/stop-order",
        "POST:/v2/futures/modify-order",
        "POST:/v2/futures/modify-stop-order",
        "POST:/v2/futures/adjust-position-margin",
        "POST:/v2/futures/set-position-stop-loss",
        "POST:/v2/futures/set-position-take-profit",
        "POST:/v2/futures/adjust-position-leverage",
        "POST:/v2/futures/batch-order",
        "POST:/v2/futures/batch-stop-order",
    ],
    FrequencyLimit.ApiGroups.PERPETUAL_CANCEL: [
        "POST:/v2/futures/cancel-order",
        "POST:/v2/futures/cancel-stop-order",
        "POST:/v2/futures/cancel-batch-order",
        "POST:/v2/futures/cancel-batch-stop-order",
        "POST:/v2/futures/close-position",
        "POST:/perpetual/v1/order/cancel",
        "POST:/perpetual/v1/order/cancel_stop",
        "POST:/perpetual/v1/order/close_limit",
        "POST:/perpetual/v1/order/close_market",
    ],
    FrequencyLimit.ApiGroups.PERPETUAL_ORDERS: [
    ],
    FrequencyLimit.ApiGroups.PERPETUAL_CANCELS: [
        "POST:/v2/futures/cancel-all-order",
        "POST:/v2/futures/cancel-order-by-client-id",
        "POST:/v2/futures/cancel-stop-order-by-client-id",
        "POST:/perpetual/v1/order/cancel_all",
        "POST:/perpetual/v1/order/cancel_batch",
        "POST:/perpetual/v1/order/cancel_stop_all",
        "POST:/perpetual/v1/order/cancel/by_client_id",
        "POST:/perpetual/v1/order/cancel_stop/by_client_id",
    ],
    FrequencyLimit.ApiGroups.PERPETUAL_QUERY_ORDER: [
        "GET:/perpetual/v1/order/pending",
        "GET:/perpetual/v1/order/stop_pending",
        "GET:/perpetual/v1/order/status",
        "GET:/perpetual/v1/order/stop_status",

        "GET:/v2/futures/order-status",
        "GET:/v2/futures/batch-order-status",
        "GET:/v2/futures/pending-order",
        "GET:/v2/futures/pending-stop-order",
        "GET:/v2/futures/pending-position",
    ],
    FrequencyLimit.ApiGroups.PERPETUAL_QUERY_ACCOUNT: [
        "GET:/perpetual/v1/asset/query",
        "GET:/perpetual/v1/position/funding",
        "GET:/perpetual/v1/position/pending",
        "GET:/perpetual/v1/position/finished",
        "GET:/perpetual/v1/position/adl_history",
        "GET:/perpetual/v1/position/margin_history",
        "GET:/perpetual/v1/position/settle_history",

        "GET:/v2/futures/position-margin-history",
        "GET:/v2/futures/position-funding-history",
        "GET:/v2/futures/position-adl-history",
        "GET:/v2/futures/position-settle-history"
    ],
    FrequencyLimit.ApiGroups.PERPETUAL_QUERY_ORDER_HISTORY: [
        "GET:/perpetual/v1/order/finished",
        "GET:/perpetual/v1/order/stop_finished",

        "GET:/v2/futures/user-deals",
        "GET:/v2/futures/order-deals",
        "GET:/v2/futures/finished-order",
        "GET:/v2/futures/finished-stop-order",
        "GET:/v2/futures/finished-position",
    ],
}


class ApiGatewayClient:
    def __init__(self):
        conf = config['CLIENT_CONFIGS']['api_gateway']
        self.client = RESTClient(conf['url'], headers={"canary": "true"})

    def _handle_response(self, response_data):
        if (status := response_data.get('code', -1)) != 0:
            raise RESTClient.BadResponse(status, response_data)
        return response_data['data']

    def _to_list_query_params(self, query_name: str, list_params: List[str]) -> str:
        if not list_params:
            return ""
        str_params = f'&{query_name}='.join(list_params)
        return f"{query_name}={str_params}"

    def _get_default_paths(self, group) -> List[str]:
        return API_TYPE_PATH_MAPPING.get(group)

    def req_trending(self, group: FrequencyLimit.ApiGroups, user_id: int = None):  # [[时间戳，次数]]
        """查询 API 访问次数趋势数据"""
        params = self._to_list_query_params("groups", [group.name])
        if user_id:
            params += f"&user_id={user_id}"
        response = self.client.get(f"/internal/rate_limiter/user/req_trending?{params}")
        return self._handle_response(response)

    def req_top_n(self, group: FrequencyLimit.ApiGroups, latest: int, top_n: int = 100):  # [[用户ID, 次数]]
        """查询 API 访问次数 TOPN 数据"""
        params = self._to_list_query_params("groups", [group.name])
        if latest:
            params += f"&latest={latest}"
        if top_n:
            params += f"&top_n={top_n}"
        response = self.client.get(f"internal/rate_limiter/user/req_top_n?{params}")
        return self._handle_response(response) or []

    def req_api_resource(self, groups: List[FrequencyLimit.ApiGroups], latest: int = 0):
        params = self._to_list_query_params("groups", [g.name for g in groups])
        params += f"&latest={latest}"
        response = self.client.get(f"internal/rate_limiter/user/req_api_resource?{params}")
        return self._handle_response(response) or {}

