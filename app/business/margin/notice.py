# -*- coding: utf-8 -*-
import json
from enum import Enum
from decimal import Decimal
from typing import Dict

from app.common import MessageContent, MessageTitle, MessageWebLink, CeleryQueues
from app.models import Message, db, User
from app.utils import celery_task, route_module_to_celery_queue, amount_to_str
from app.caches import MarginLiquidationWarningCache
from ..email import (
    send_margin_renew_notice_email,
    send_margin_renew_succeeded_email,
    send_margin_liquidation_email,
    send_margin_liquidation_warning_email,
)
from ..push import (
    send_margin_loan_order_expired_push,
    send_margin_renew_failed_push,
    send_margin_loan_order_force_flat_push,
    send_margin_loan_order_force_flat_balance_push,
    get_market_business_name,
    send_margin_liquidation_push,
    send_margin_liquidation_warning_push,
)
from ... import config


route_module_to_celery_queue(__name__, CeleryQueues.MARGIN)


@celery_task
def margin_loan_order_expired_task(user_id, asset: str, amount: str, expired_time: str, market_name: str,
                                   created_at: int = None):
    """
    借币订单到期通知
    """
    send_margin_renew_notice_email.delay(
        user_id=user_id,
        asset=asset,
        amount=amount,
        expired_time=expired_time,
        template_type="margin_loan_order_expired",
        market_name=market_name,
    )
    send_margin_loan_order_expired_push.delay([user_id], asset, market_name, created_at=created_at)


class ForceFlatType(Enum):
    NOT_ENOUGH = '系统余额不足'
    UNFLAT = '尚未还币'


@celery_task
def margin_loan_order_force_flat_task(user_id, asset: str, amount: str, expired_time, market_name: str,
                                      force_flat_type: str = None, created_at: int = None):
    """
    强制还币通知
    :return:
    """
    send_margin_renew_notice_email.delay(
        user_id=user_id,
        amount=amount,
        asset=asset,
        expired_time=expired_time,
        template_type="margin_loan_order_force_flat",
        market_name=market_name,
        force_flat_type=force_flat_type,
    )
    amount_str = f'{amount_to_str(amount)} {asset}'
    if force_flat_type == ForceFlatType.NOT_ENOUGH.name:
        send_margin_loan_order_force_flat_balance_push.delay([user_id], asset, market=market_name,
                                                             created_at=created_at)
        content = MessageContent.LOAN_ORDER_FORCE_FLAT_BY_NOT_ENOUGH.name
    else:
        send_margin_loan_order_force_flat_push.delay([user_id], asset, market=market_name, created_at=created_at)
        content = MessageContent.LOAN_ORDER_FORCE_FLAT_BY_NOT_FLAT.name

    db.session_add_and_commit(
        Message(
            user_id=user_id,
            title=MessageTitle.LOAN_ORDER_FORCE_FLAT.name,
            content=content,
            params=json.dumps(
                {
                    "market": get_market_business_name(market_name),
                    "amount": amount_str,
                }
            ),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.MARGIN_LOAN_RECORD_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.TRADE_NOTIFICATION,
        )
    )


@celery_task
def margin_renew_succeeded_task(user_id: int, market: str, asset: str, amount: str,
                                day_rate: str, expired_time: str, renew_days: int):
    """
    续借成功通知
    """
    send_margin_renew_succeeded_email.delay(
        user_id=user_id,
        market=market,
        amount=amount,
        day_rate=day_rate,
        expired_time=expired_time,
        renew_days=renew_days,
        asset=asset,
        site_url=config['SITE_URL'],
    )


@celery_task
def margin_renew_failed_task(user_id, asset: str, amount: str, expired_time: str, market_name: str,
                             created_at: int = None):
    """
    续借失败通知
    """
    send_margin_renew_notice_email.delay(
        user_id=user_id,
        amount=amount,
        asset=asset,
        expired_time=expired_time,
        template_type="margin_renew_failed",
        market_name=market_name,
    )
    send_margin_renew_failed_push.delay([user_id], asset, market=market_name, created_at=created_at)


@celery_task
def margin_liquidation_notice(
    user_id: int, market: str, index_price: str,
    risk_rate: str, loan_asset_str_map: Dict[str, str],
    created_at: int = None,
):
    """
    爆仓通知
    """
    user: User = User.query.get(user_id)
    if not user:
        return

    amount_str = "；".join([f"{k} {v}" for k, v in loan_asset_str_map.items() if Decimal(v) > 0])
    db.session.add(
        Message(
            user_id=user_id,
            title=MessageTitle.LOAN_ORDER_FORCE_FLAT.name,
            content=MessageContent.LOAN_ORDER_FORCE_FLAT_BY_LIQUIDATION.name,
            params=json.dumps(
                dict(
                    market=market,
                    amount=amount_str,
                    risk_rate="{}%".format(amount_to_str(Decimal(risk_rate) * Decimal('100'), 8)),
                )
            ),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.MARGIN_LOAN_RECORD_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.TRADE_NOTIFICATION,
        )
    )
    db.session.commit()

    send_margin_liquidation_email.delay(user_id, market, index_price, risk_rate, loan_asset_str_map)
    send_margin_liquidation_push.delay([user_id], market, created_at)


@celery_task
def margin_liquidation_warning(user_id: int, market: str, warning_rate: str, created_at: int = None):
    """
    爆仓警告
    """
    user: User = User.query.get(user_id)
    if not user:
        return
    cache = MarginLiquidationWarningCache(market, user_id)
    if cache.exists():
        return
    cache.gen()
    warning_rate = Decimal(warning_rate)
    send_margin_liquidation_warning_email.delay(user_id, market, amount_to_str(warning_rate, 8))
    send_margin_liquidation_warning_push.delay([user_id], market, risk_rate=amount_to_str(warning_rate, 8),
                                               created_at=created_at)
