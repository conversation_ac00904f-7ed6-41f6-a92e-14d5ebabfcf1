#!/usr/bin/python
# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import date, datetime, timedelta
from decimal import Decimal

from sqlalchemy import func

from ..utils import query_records_by_time_range
from ...models import (
    DailyMarginFundReport, MarginInsuranceHistory, db,
    MonthlyMarginFundReport, MarginRealInsuranceHistory,
)
from ...models.pledge import PledgeInsuranceHistory, PledgeRealInsuranceHistory
from ...utils.date_ import date_to_datetime


class MarginFundProcessor(object):

    def __init__(self, asset: str, report_date: date):
        self.asset = asset
        self.report_date = report_date

    @property
    def is_generate(self):
        return DailyMarginFundReport.query.filter(
            DailyMarginFundReport.asset == self.asset,
            DailyMarginFundReport.report_date == self.report_date
        ).first()

    @property
    def last_report_date(self):
        r = DailyMarginFundReport.query.filter(
            DailyMarginFundReport.asset == self.asset
        ).order_by(
            DailyMarginFundReport.id.desc()
        ).first()
        if r:
            return r.report_date
        else:
            _r = MarginInsuranceHistory.query.filter(
                MarginInsuranceHistory.asset == self.asset
            ).order_by(MarginInsuranceHistory.id.asc()).first()
            return date(2019, 5, 1) if not _r else _r.created_at.date() - timedelta(days=1)

    @property
    def start_date(self):
        return self.last_report_date

    @property
    def today_date(self):
        return datetime.utcnow().date()

    def _get_data(self, start_date: date, end_date: date):
        return self._get_sum_data_by_datetime(self.asset, start_date, end_date)

    @classmethod
    def _get_sum_data_by_datetime(cls, asset: str, start_date: date, end_date: date):
        """only support small time range"""
        start_datetime = date_to_datetime(start_date)
        end_datetime = date_to_datetime(end_date)
        records = query_records_by_time_range(MarginInsuranceHistory,
                                              start_datetime,
                                              end_datetime,
                                              filters=dict(asset=asset),
                                              limit=5000)
        p_records = query_records_by_time_range(PledgeInsuranceHistory,
                                                start_datetime,
                                                end_datetime,
                                                filters=dict(asset=asset),
                                                limit=5000)
        all_records = records + p_records
        all_records.sort(key=lambda x: x.created_at, reverse=True)

        result = defaultdict(lambda: dict(
            asset=asset,
            liquidation=Decimal('0'),
            interest_fund=Decimal('0'),
            liquidation_fund=Decimal('0'),
            amount=Decimal('0'),
            transfer=Decimal('0'),
            balance=Decimal('0'),
            real_balance=Decimal('0')
        ))
        balance_dict = {}
        real_balance_dict = {}

        for _record in all_records:
            date_ = _record.created_at.date()
            if _record.history_type in [
                MarginInsuranceHistory.HistoryType.LIQUIDATION,
                PledgeInsuranceHistory.HistoryType.PLEDGE_LIQUIDATION,
            ]:
                result[date_]['liquidation'] += _record.amount
                result[date_]['amount'] += _record.amount
            if _record.history_type in [
                MarginInsuranceHistory.HistoryType.INTEREST,
                PledgeInsuranceHistory.HistoryType.PLEDGE_INTEREST,
            ]:
                result[date_]['interest_fund'] += _record.amount
                result[date_]['amount'] += _record.amount
            if _record.history_type in [
                MarginInsuranceHistory.HistoryType.LIQUIDATION_INCOME,
                PledgeInsuranceHistory.HistoryType.PLEDGE_LIQUIDATION_INCOME,
            ]:
                result[date_]['liquidation_fund'] += _record.amount
                result[date_]['amount'] += _record.amount
            if date_ not in balance_dict:
                balance_dict[date_] = _record.balance

        real_records = query_records_by_time_range(
            MarginRealInsuranceHistory,
            start_datetime,
            end_datetime,
            filters=dict(asset=asset),
            limit=5000)
        p_real_records = query_records_by_time_range(
            PledgeRealInsuranceHistory,
            start_datetime,
            end_datetime,
            filters=dict(asset=asset),
            limit=5000)
        all_real_records = real_records + p_real_records
        all_real_records.sort(key=lambda x: x.created_at, reverse=True)
        for _real_record in all_real_records:
            date_ = _real_record.created_at.date()
            if _real_record.history_type == MarginRealInsuranceHistory.HistoryType.TRANSFER:
                result[date_]["transfer"] += -abs(_real_record.amount)
            if date_ not in real_balance_dict:
                real_balance_dict[date_] = _real_record.balance
        for report_date, v in balance_dict.items():
            result[report_date]["balance"] = v
        for report_date, v in real_balance_dict.items():
            result[report_date]["real_balance"] = v
        return result

    def generate(self):
        if not self.is_generate:
            query_end_date = self.report_date + timedelta(days=1)
            history_data = self._get_data(self.last_report_date+timedelta(days=1), query_end_date)
            result = [dict(_data, report_date=_date) for _date, _data in history_data.items()]
            result.sort(key=lambda x: x['report_date'])
            records = [
                DailyMarginFundReport(
                    report_date=v['report_date'],
                    asset=v["asset"],
                    # 平台利息注入
                    interest_fund=v["interest_fund"],
                    # 爆仓费注入
                    liquidation_fund=v["liquidation_fund"],
                    # 穿仓分摊
                    liquidation=v['liquidation'],
                    # 变动
                    amount=v["amount"],
                    # 现在总额
                    balance=v['balance'],
                    # 划转
                    transfer=v["transfer"],
                    # 实际值
                    real_balance=v['real_balance']
                )
                for v in result
            ]
            db.session.add_all(records)
            db.session.commit()
        return True

    @staticmethod
    def generate_monthly_report(start_date: date, end_date: date):
        day_before_end_date = end_date - timedelta(days=1)
        daily_balances = DailyMarginFundReport.query.filter(
            DailyMarginFundReport.report_date == day_before_end_date
        ).with_entities(DailyMarginFundReport.asset,
                        DailyMarginFundReport.balance,
                        DailyMarginFundReport.real_balance).all()
        daily_balance_map = {v.asset: v.balance for v in daily_balances}
        daily_real_balance_map = {v.asset: v.real_balance for v in daily_balances}
        merged_data = DailyMarginFundReport.query.filter(
            DailyMarginFundReport.report_date >= start_date,
            DailyMarginFundReport.report_date < end_date
        ).group_by(DailyMarginFundReport.asset).with_entities(
            DailyMarginFundReport.asset,
            func.sum(DailyMarginFundReport.interest_fund).label('interest_fund'),
            func.sum(DailyMarginFundReport.liquidation_fund).label('liquidation_fund'),
            func.sum(DailyMarginFundReport.liquidation).label('liquidation'),
            func.sum(DailyMarginFundReport.amount).label('amount'),
            func.sum(DailyMarginFundReport.transfer).label('transfer'),
        ).all()
        records = []
        for item in merged_data:
            records.append(MonthlyMarginFundReport(
                report_date=start_date,
                asset=item.asset,
                interest_fund=item.interest_fund,
                liquidation=item.liquidation,
                liquidation_fund=item.liquidation_fund,
                amount=item.amount,
                transfer=item.transfer,
                balance=daily_balance_map.get(item.asset, 0),
                real_balance=daily_real_balance_map.get(item.asset, 0)
            ))
        db.session.bulk_save_objects(records)
        db.session.commit()

