# -*- coding: utf-8 -*-
import datetime
from decimal import Decimal

from flask import current_app
from typing import Union, Optional

from .. import SiteSettings
from ..clients.biz_monitor import biz_monitor
from ..lendable import LendableAmountProcessor
from ...caches.margin import MarginAccountIdCache, MarginAssetRuleCache
from ...common import PrecisionEnum, MarginEvent
from ...exceptions import MarginForbiddenLoan, MarginLoanAmountMinLimit, MarginLoanAmountMaxLimit, \
    MarginLoanError, InvalidArgument
from ...business import UserSettings, UserPreferences, ServerClient, BalanceBusiness, BusinessSettings
from .helper import BUY_TYPE, SELL_TYPE, MarginInsuranceOperation
from .helper import MarginAccountHelper, get_user_day_rate_fee

from ...models import (
    MarginLoanOrder, db, LendableAssetChangeHistory,
    UserMarginAssetRule,
)
from ...utils import now, amount_to_str, quantize_amount


class MarginOrderLoanOperation(object):

    def __init__(self, user_id: int, margin_identity: Union[int, str]):
        self.user_id = user_id
        accounts_dict = MarginAccountIdCache.list_online_markets()
        if margin_identity not in accounts_dict.keys() and \
                margin_identity not in accounts_dict.values():
            raise InvalidArgument(margin_identity)
        if margin_identity in accounts_dict.keys():
            self.account_id = margin_identity
            self.market_name = accounts_dict[margin_identity]
        if margin_identity in accounts_dict.values():
            self.market_name = margin_identity
            self.account_id = dict(zip(
                accounts_dict.values(),
                accounts_dict.keys()))[margin_identity]
        self.market_cache = MarginAccountHelper.get_account_info(self.account_id)
        self.client = ServerClient(logger=current_app.logger)

    def is_can_loan(self):
        settings = UserSettings(self.user_id)
        if UserPreferences(self.user_id).opening_margin_function and \
                settings.can_margin_loan() and self.account_id not in settings.forbidden_margin_accounts:
            return True
        return False

    @classmethod
    def get_loan_history(cls,
                         user_id: int,
                         account_id: Optional[int],
                         order_id: Optional[int],
                         status: Optional[MarginLoanOrder.StatusType],
                         page: int,
                         limit: int):
        order_query = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id == user_id
        )
        if status:
            order_query = order_query.filter(
                MarginLoanOrder.status == status
            )
        if account_id:
            order_query = order_query.filter(
                MarginLoanOrder.account_id == account_id,
            )
        if order_id:
            order_query = order_query.filter(
                MarginLoanOrder.id == order_id,
            )
        order_query = order_query.order_by(
            MarginLoanOrder.id.desc()
        )
        records = order_query.paginate(page, limit, error_out=False)
        data = []
        for r in records.items:
            data.append({
                "loan_id": r.id,
                "create_time": int(r.created_at.timestamp()),
                "market_type": r.market_name,
                "coin_type": r.asset,
                "day_rate": amount_to_str(r.day_rate, PrecisionEnum.COIN_PLACES),
                "loan_amount": amount_to_str(r.loan_amount, PrecisionEnum.COIN_PLACES),
                "interest_amount": amount_to_str(r.interest_amount, PrecisionEnum.COIN_PLACES),
                # 待还包括借币和利息
                "unflat_amount": amount_to_str((r.unflat_amount + r.interest_amount), PrecisionEnum.COIN_PLACES),
                "expire_time": int(r.expire_at.timestamp()),
                "is_renew": r.is_renew,
                "status": r.status,
            })
        return dict(
            page=page,
            limit=limit,
            total=records.total,
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=data,
            total_page=records.pages
        )

    def add_new_loan_order(self, asset: str, amount: Decimal, renew: bool = False):
        if not self.is_can_loan():
            raise MarginForbiddenLoan
        if self.market_name in SiteSettings.forbidden_margin_markets:
            raise MarginForbiddenLoan
        asset_mapping = {
            self.market_cache["sell_asset_type"]: SELL_TYPE,
            self.market_cache['buy_asset_type']: BUY_TYPE
        }
        if asset not in asset_mapping:
            raise MarginForbiddenLoan
        asset_type = asset_mapping[asset]
        asset_config = MarginAssetRuleCache(asset).dict
        if asset_config['status'] != MarginAssetRuleCache.model.StatusType.OPEN:
            raise MarginForbiddenLoan
        min_loan = Decimal(asset_config['min_loan'])
        max_loan = Decimal(asset_config['max_loan'])
        period = asset_config['period']
        day_rate = get_user_day_rate_fee(self.user_id, asset)
        if Decimal(amount) < Decimal(min_loan):
            raise MarginLoanAmountMinLimit(amount=min_loan, asset=asset)
        if Decimal(amount) > Decimal(max_loan):
            # 配置了用户特殊借币额度，去掉限制
            _asset_special_loan = UserMarginAssetRule.query.filter(
                UserMarginAssetRule.user_id == self.user_id,
                UserMarginAssetRule.asset == asset,
                UserMarginAssetRule.status == UserMarginAssetRule.StatusType.PASS,
            ).first()
            if not _asset_special_loan or _asset_special_loan.max_loan <= Decimal(max_loan):
                raise MarginLoanAmountMaxLimit

        margin_account_utils = MarginAccountHelper(self.user_id, self.account_id)

        # 服务端的校验需要添加系数
        max_loan_info = margin_account_utils.calculate_loan_max_amount_server()
        loan_max_amount = max_loan_info[asset_type]
        if Decimal(amount) > loan_max_amount:
            raise MarginLoanAmountMaxLimit

        fund_rate = BusinessSettings.margin_interest_fund_percent
        loan_order = MarginLoanOrder(
            user_id=self.user_id,
            account_id=self.account_id,
            market_name=self.market_name,
            asset=asset,
            day_rate=day_rate,
            loan_amount=amount,
            unflat_amount=amount,
            # 修改为每小时
            interest_amount=amount_to_str(Decimal(amount) * Decimal(day_rate) / 24, 8),
            expire_at=now() + datetime.timedelta(days=period),
            is_renew=bool(renew),
            status=MarginLoanOrder.StatusType.CREATE,
        )
        db.session.add(loan_order)
        db.session.commit()
        try:
            self.client.add_user_balance(
                user_id=self.user_id,
                asset=asset,
                amount=amount_to_str(amount, PrecisionEnum.COIN_PLACES),
                business=BalanceBusiness.MARGIN_LOAN,
                business_id=loan_order.id,
                detail={"remark": "margin for loan"},
                account_id=self.account_id,
            )
            loan_order.status = MarginLoanOrder.StatusType.PASS
            LendableAmountProcessor(
                asset, amount,
                LendableAssetChangeHistory.BusinessType.MARGIN_LOAN
            ).process_new_record()

        except Exception as e:
            current_app.logger.error(
                f"{self.user_id} {self.account_id} {asset} order id {loan_order.id} loan error {e!r}")
            retry_margin_loan_async(loan_order)
            raise MarginLoanError

        db.session.commit()
        interest_amount = quantize_amount(Decimal(amount) * Decimal(day_rate) / 24,
                                          PrecisionEnum.COIN_PLACES)
        # 计息并添加
        MarginInsuranceOperation.interest_and_fund_record(loan_order, interest_amount, fund_rate)
        db.session.commit()
        return loan_order.id


def report_loan_event(user_id):
    biz_monitor.increase_counter(
        MarginEvent.LOAN_COUNT,
        with_source=True
    )
    biz_monitor.increase_uniq_counter(
        MarginEvent.LOAN_NUM,
        value=[user_id],
        with_source=True
    )
    biz_monitor.increase_counter(
        MarginEvent.USER_LOAN_COUNT,
        with_source=True
    )
    biz_monitor.increase_uniq_counter(
        MarginEvent.USER_LOAN_NUM,
        value=[user_id],
        with_source=True
    )


def report_flat_event(user_id):
    biz_monitor.increase_counter(
        MarginEvent.FLAT_COUNT
    )
    biz_monitor.increase_uniq_counter(
        MarginEvent.FLAT_NUM,
        value=[user_id]
    )
    biz_monitor.increase_counter(
        MarginEvent.USER_FLAT_COUNT,
        with_source=True
    )
    biz_monitor.increase_uniq_counter(
        MarginEvent.USER_FLAT_NUM,
        value=[user_id],
        with_source=True
    )


def retry_margin_loan_async(loan_order: MarginLoanOrder):
    from app.schedules.margin import retry_create_margin_loan_order_task

    retry_create_margin_loan_order_task.apply_async((loan_order.id,), countdown=10, expires=120)
