# -*- coding: utf-8 -*-

import json
from decimal import Decimal

from datetime import timedelta


from app.common import CeleryQueues, ProducerTopics
from app.utils import route_module_to_celery_queue, quantize_amount, \
    celery_task, timestamp_to_datetime, now, datetime_to_time
from .clients import ViaBTCPoolClient
from ..models import (db, User, ViaBTCPoolOrder, <PERSON>po<PERSON>t, <PERSON>drawal,
                      ColdWalletHistory)
from ..business import (ServerClient, BalanceBusiness, CacheLock, LockKeys,
                        SiteSettings)
from ..business.utils import yield_query_records_by_time_range
from ..exceptions import InsufficientBalance, ServiceUnavailable
from ..config import config
from .lock import lock_call
from ..producer import mission_producer
from ..utils import g_map, batch_iter

route_module_to_celery_queue(__name__, CeleryQueues.VIABTC_POOL)


@celery_task
@lock_call(with_args=False)
def do_viabtc_pool_local_transfer(quickly=True):
    if not SiteSettings.external_transfers_enabled:
        return
    from ..assets import asset_to_default_chain
    pool_user_id = config['CLIENT_CONFIGS']['viabtc_pool']['user_id']

    if quickly:
        created_orders = []
        end = now()
        start = end - timedelta(days=1)
        query = yield_query_records_by_time_range(ViaBTCPoolOrder,
                                                  start_time=start,
                                                  end_time=end,
                                                  select_fields=[ViaBTCPoolOrder.id, ViaBTCPoolOrder.status])
        for row in query:
            if row.status != ViaBTCPoolOrder.Status.CREATED:
                continue
            created_orders.append(row)
    else:                                               
        # sql:15s
        created_orders = ViaBTCPoolOrder.query.filter(
            ViaBTCPoolOrder.status == ViaBTCPoolOrder.Status.CREATED
            ).with_entities(ViaBTCPoolOrder.id).all()
    
    for _order in created_orders:
        with CacheLock(LockKeys.viabtc_pool_transfer_status_update(_order.id)):
            db.session.rollback()
            order = ViaBTCPoolOrder.query.filter(
                ViaBTCPoolOrder.id == _order.id).first()
            if order.status != ViaBTCPoolOrder.Status.CREATED:
                continue
            order.status = ViaBTCPoolOrder.Status.TRANSFERRING

            asset = order.asset
            amount = order.amount

            withdrawal = Withdrawal(
                user_id=pool_user_id,
                type=Withdrawal.Type.LOCAL,
                asset=asset,
                address=order.email,
                amount=amount,
                fee=Decimal(0),
                fee_asset=asset,
                recipient_user_id=order.user_id,
                status=Withdrawal.Status.FINISHED
            )
            db.session.add(withdrawal)
            deposit = Deposit(
                user_id=order.user_id,
                address=order.email,
                type=Deposit.Type.LOCAL,
                asset=asset,
                amount=amount,
                status=Deposit.Status.PROCESSING,
                sender_user_id=pool_user_id,
            )
            db.session.add(deposit)
            db.session.flush()
            order.deposit_id = deposit.id
            order.withdrawal_id = withdrawal.id
            db.session.commit()

            server_client = ServerClient()
            try:
                server_client.add_user_balance(
                    order.user_id, asset, amount,
                    BalanceBusiness.DEPOSIT, deposit.id)
            except server_client.BadResponse as e:
                if e.code == server_client.ResponseCode.INSUFFICIENT_BALANCE:
                    order.status = ViaBTCPoolOrder.Status.TRANSFER_FAILED
                    withdrawal.status = Withdrawal.Status.FAILED
                    db.session.commit()
                    raise InsufficientBalance
                raise ServiceUnavailable
            _now = now()
            deposit.confirmed_at = _now
            withdrawal.sent_at = _now
            deposit.status = Deposit.Status.FINISHED
            order.status = ViaBTCPoolOrder.Status.TRANSFER_SUCCEEDED
            db.session.commit()

            ColdWalletHistory.add_history(
                asset,
                'ERC20' if asset in ('USDT', 'ETH') else asset_to_default_chain(asset),
                amount,
                '',
                ColdWalletHistory.Type.POOL,
                db.session)
    notify_viabtc_pool_orders.delay()


@celery_task
@lock_call()
def add_viabtc_pool_transfer_orders():
    if not SiteSettings.external_transfers_enabled:
        return
    from ..assets import list_all_assets
    processing_orders = ViaBTCPoolClient().get_processing_orders()

    exists_order_ids = set()
    for _orders in batch_iter(processing_orders, 500):
        _rows = ViaBTCPoolOrder.query.filter(
            ViaBTCPoolOrder.order_id.in_([x['id'] for x in _orders])
        ).with_entities(ViaBTCPoolOrder.order_id).all()
        exists_order_ids |= {x for x, in _rows}
    processing_orders = [x for x in processing_orders if x['id'] not in exists_order_ids]

    online_assets = list_all_assets()
    for order in processing_orders:
        if order['status'] != 'processing':
            continue
        if 'external_user_id' not in order or not order['external_user_id']:
            continue
        asset = order['coin']
        if asset == 'BHA':
            asset = 'BCHA'
        if asset == 'BEL':
            asset = 'BELLS'
        if asset not in online_assets:
            continue
        user_id = int(order['external_user_id'])
        user = User.query.get(user_id)
        if not user:
            continue

        order_id = order['id']
        email = order['address']
        amount = quantize_amount(order['amount'], 8)
        order_at = timestamp_to_datetime(order['order_time'])
        remark = json.dumps({'external_user_id': order['user_id']})

        db.session.add(
            ViaBTCPoolOrder(
                user_id=user_id,
                email=email,
                order_id=order_id,
                asset=asset,
                amount=amount,
                order_at=order_at,
                status=ViaBTCPoolOrder.Status.CREATED,
                remark=remark
            )
        )
        db.session.commit()
    if processing_orders:
        do_viabtc_pool_local_transfer.delay()


@celery_task
@lock_call()
def notify_viabtc_pool_orders():
    orders = []
    end = now()
    start = end - timedelta(days=1)
    query = yield_query_records_by_time_range(ViaBTCPoolOrder,
                                              start_time=start,
                                              end_time=end,
                                              select_fields=[ViaBTCPoolOrder.id, ViaBTCPoolOrder.order_id, ViaBTCPoolOrder.status])
    for order in query:
        if order.status != ViaBTCPoolOrder.Status.TRANSFER_SUCCEEDED:
            continue
        orders.append(order)
    if not orders:
        return
    client = ViaBTCPoolClient()
    result = g_map(client.notify_order_completed, [x.order_id for x in orders],
                   size=20, ordered=True, fail_safe=...)
    succeeded_ids = [x.id for x, r in zip(orders, result) if r is True]
    ViaBTCPoolOrder.query.filter(
        ViaBTCPoolOrder.id.in_(succeeded_ids)
    ).update(
        {ViaBTCPoolOrder.status: ViaBTCPoolOrder.Status.FINISHED},
        synchronize_session=False
    )
    db.session.commit()
    if not succeeded_ids:
        return
    pool_orders = ViaBTCPoolOrder.query.filter(
        ViaBTCPoolOrder.id.in_(succeeded_ids)
    ).all()
    messages = []
    for pool_order in pool_orders:
        message = dict(
            event_data=dict(
                amount=pool_order.amount,
                amount_asset=pool_order.asset,
                order_type="pool_order"
            ),
            biz_type=ViaBTCPoolOrder.__tablename__,
            biz_id=pool_order.id,
            timestamp=datetime_to_time(pool_order.created_at),
            user_id=pool_order.user_id
        )
        messages.append(message)
    mission_producer.send_multiple_messages(ProducerTopics.DEPOSIT_REWARD, messages)


@celery_task
@lock_call()
def fix_pool_order_status():
    from ..assets import asset_to_default_chain

    transferring_orders = []
    end = now() - timedelta(minutes=10)
    start = end - timedelta(days=1)
    query = yield_query_records_by_time_range(ViaBTCPoolOrder,
                                              start_time=start,
                                              end_time=end,
                                              select_fields=[ViaBTCPoolOrder.deposit_id, ViaBTCPoolOrder.asset,
                                                             ViaBTCPoolOrder.amount, ViaBTCPoolOrder.status])
    for order in query:
        if order.status != ViaBTCPoolOrder.Status.TRANSFERRING:
            continue
        transferring_orders.append(order)

    for order in transferring_orders:
        deposit = Deposit.query.filter(
            Deposit.id == order.deposit_id,
            Deposit.type == Deposit.Type.LOCAL,
            Deposit.status == Deposit.Status.FINISHED
        ).first()
        if deposit:
            order = ViaBTCPoolOrder.query.get(order.id)
            order.status = ViaBTCPoolOrder.Status.TRANSFER_SUCCEEDED
            db.session.commit()
            ColdWalletHistory.add_history(
                order.asset,
                'ERC20' if order.asset in ('USDT', 'ETH') else asset_to_default_chain(order.asset),
                order.amount,
                '',
                ColdWalletHistory.Type.POOL,
                db.session)
