import re
from base64 import b64encode, b64decode
from collections import defaultdict
from datetime import datetime
from enum import Enum

from marshmallow import fields as mm_fields
from sqlalchemy import and_
from marshmallow.utils import EXCLUDE
from marshmallow.validate import Length

from app import Language, config
from app.business import lock_call
from app.caches.questionnaire import QuestionnaireViewCountCache, QuestionnaireLoginViewCountCache, QuestionnaireCache
from app.common import language_cn_names, get_country, CeleryQueues
from app.exceptions import InvalidArgument
from app.models import only_query_pagination, db
from app.models.mongo.questionnaire import (QuestionnaireMySQL, QuestionnaireContentMySQL, QuestionnaireResponseMySQL,
                                            QuestionnaireAnswerMySQL, QuestionnaireStatisticMySQL, QuestionType)
from app.schedules.reports.admin_async_download import _send_async_download_email
from app.utils import now, new_hex_token, celery_task, AWSBucketPrivate
from app.utils.parser import mm_schema

QuestionnaireUrlPrefix = f"{config['SITE_URL']}/questionnaire"


class QuestionnaireManage:
    QuestionnaireUidSize = 8
    QuestionUidSize = 8

    class IconType(Enum):
        STAR = "star"
        LIKE = "like"

    class TextType(Enum):
        ALL = "all"
        EMAIL = "email"
        NUMBER = "number"
        AT_ID = "@id"
        LINK = "link"

    class FileType(Enum):
        PNG = 'png'
        JPG = 'jpg'

    question_schema_map = {
        QuestionType.SINGLE_OP.name: mm_schema(dict(
            uid=mm_fields.String(required=True),
            type=mm_fields.Enum(QuestionType, required=True),
            question=mm_fields.String(required=True, validate=Length(max=1024)),
            required=mm_fields.Boolean(required=True),
            options=mm_fields.List(mm_fields.Nested(
                mm_schema(dict(
                    id=mm_fields.Integer(required=True),
                    name=mm_fields.String(required=True),
                    has_input=mm_fields.Boolean(missing=False),
                ))
            ), validate=Length(min=1))
        ))(unknown=EXCLUDE),

        QuestionType.MULTI_OP.name: mm_schema(dict(
            uid=mm_fields.String(required=True),
            type=mm_fields.Enum(QuestionType, required=True),
            question=mm_fields.String(required=True, validate=Length(max=1024)),
            required=mm_fields.Boolean(required=True),
            options=mm_fields.List(mm_fields.Nested(
                mm_schema(dict(
                    id=mm_fields.Integer(required=True),
                    name=mm_fields.String(required=True),
                    has_input=mm_fields.Boolean(missing=False),
                ))
            ), validate=Length(min=1))
        ))(unknown=EXCLUDE),

        QuestionType.SLIDE_SCORE.name: mm_schema(dict(
            uid=mm_fields.String(required=True),
            type=mm_fields.Enum(QuestionType, required=True),
            question=mm_fields.String(required=True, validate=Length(max=1024)),
            required=mm_fields.Boolean(required=True),
            max_score=mm_fields.Integer(required=True, validate=lambda x: 5 <= x <= 10),
            icon_type=mm_fields.Enum(IconType, missing=IconType.STAR),
            min_label=mm_fields.String(required=True, validate=Length(max=1024)),
            max_label=mm_fields.String(required=True, validate=Length(max=1024)),
        ))(unknown=EXCLUDE),

        QuestionType.TEXT.name: mm_schema(dict(
            uid=mm_fields.String(required=True),
            type=mm_fields.Enum(QuestionType, required=True),
            question=mm_fields.String(required=True, validate=Length(max=1024)),
            required=mm_fields.Boolean(required=True),
            desc=mm_fields.String(validate=Length(max=1024), missing=""),
            type_limit=mm_fields.Enum(TextType, missing=TextType.ALL),
        ))(unknown=EXCLUDE),

        QuestionType.UPLOAD_FILE.name: mm_schema(dict(
            uid=mm_fields.String(required=True),
            type=mm_fields.Enum(QuestionType, required=True),
            question=mm_fields.String(required=True, validate=Length(max=1024)),
            required=mm_fields.Boolean(required=True),
            type_limit=mm_fields.List(mm_fields.Enum(FileType)),
            size_limit=mm_fields.Integer(missing=1024 * 1024),  # default is 1mb
        ))(unknown=EXCLUDE),
    }

    @classmethod
    def new_questionnaire_uid(cls):
        return new_hex_token(cls.QuestionnaireUidSize)

    @classmethod
    def new_question_id(cls):
        return new_hex_token(cls.QuestionUidSize)

    @classmethod
    def get_questionnaire_by_id(cls, questionnaire_uid, only: [str] = None):
        query = QuestionnaireMySQL.query.filter_by(uid=questionnaire_uid)
        if only is not None:
            query = query.with_entities(*[getattr(QuestionnaireMySQL, field) for field in only])
        return query.first()

    @classmethod
    def get_status(cls, questionnaire):
        # 使用with_entities 指定查询字段时，property属性会不可用，此时使用该方法
        _now = now()
        if _now >= questionnaire.end_time:
            return QuestionnaireMySQL.Status.TERMINATION
        if _now < questionnaire.start_time:
            return QuestionnaireMySQL.Status.PENDING
        return QuestionnaireMySQL.Status.ACTIVE

    @classmethod
    def get_questionnaire_content(cls, questionnaire_uid):
        contents = QuestionnaireContentMySQL.query.filter_by(questionnaire_uid=questionnaire_uid).all()
        return {content.lang: content for content in contents}

    @classmethod
    def get_questionnaire_content_by_lang(cls, questionnaire_uid, lang):
        return QuestionnaireContentMySQL.query.filter_by(questionnaire_uid=questionnaire_uid, lang=lang).first()

    @classmethod
    def get_questionnaire_name_info(cls, include_deleted=False):
        query = QuestionnaireMySQL.query.with_entities(
            QuestionnaireMySQL.uid, QuestionnaireMySQL.name, QuestionnaireMySQL.deleted,
        )

        if not include_deleted:
            query = query.filter(QuestionnaireMySQL.deleted == False)  # noqa: E712

        questionnaires = query.all()
        return [{
            'questionnaire_uid': q.uid,
            'questionnaire_name': q.name,
        } for q in questionnaires]

    @classmethod
    def get_filter_by_status(cls, query, status: QuestionnaireMySQL.Status):
        _now = now()
        match status:
            case QuestionnaireMySQL.Status.PENDING:
                return query.filter(QuestionnaireMySQL.start_time > _now)
            case QuestionnaireMySQL.Status.ACTIVE:
                return query.filter(and_(
                    QuestionnaireMySQL.start_time <= _now,
                    QuestionnaireMySQL.end_time >= _now
                ))
            case QuestionnaireMySQL.Status.TERMINATION:
                return query.filter(QuestionnaireMySQL.end_time < _now)

    @classmethod
    def get_filter_by_time_range(cls, query, start_time: datetime = None, end_time: datetime = None):
        if start_time:
            query = query.filter(QuestionnaireMySQL.start_time <= start_time)
        if end_time:
            query = query.filter(QuestionnaireMySQL.end_time >= end_time)
        return query

    @classmethod
    def reload_questionnaire_cache(cls, questionnaire_uid):
        QuestionnaireCache(questionnaire_uid).reload()

    @classmethod
    def delete_questionnaire_cache(cls, questionnaire_uid):
        QuestionnaireCache(questionnaire_uid).delete()

    @classmethod
    def validate_questions(cls, questions, last_lang_questions=None):
        if last_lang_questions is not None:
            if len(questions) != len(last_lang_questions):
                raise InvalidArgument(message="多语言问卷问题数量不一致")

        question_uids = []
        # 填充问题和选项id，校验题目结构
        for index, question in enumerate(questions):
            last_lang_question = last_lang_questions[index] if last_lang_questions else None
            if not question.get('uid'):
                if last_lang_question is not None:
                    question['uid'] = last_lang_question['uid']
                else:
                    question['uid'] = cls.new_question_id()
            else:
                if last_lang_question is not None:
                    if question['uid'] != last_lang_question['uid']:
                        raise InvalidArgument(message="多语言问卷问题uid不一致")

            if last_lang_question is not None:
                if question.get('required') != last_lang_question.get('required'):
                    raise InvalidArgument(message="多语言问卷问题是否必填不一致")

            question_uids.append(question['uid'])

            match question.get('type'):
                case QuestionType.SINGLE_OP.name | QuestionType.MULTI_OP.name:
                    if last_lang_question is not None:
                        if len(last_lang_question.get('options', [])) != len(question.get('options', [])):
                            raise InvalidArgument(message="多语言问卷问题选项数不一致")
                    for idx, option in enumerate(question.get('options', [])):
                        if last_lang_question is not None:
                            if last_lang_question.get(
                                    'options', [])[idx].get('has_input', False) != option.get('has_input', False):
                                raise InvalidArgument(message="多语言问卷选择题是否允许输入不一致")
                        option['id'] = idx + 1
                case QuestionType.TEXT.name:
                    if last_lang_question is not None:
                        if question.get('type_limit') != last_lang_question.get('type_limit'):
                            raise InvalidArgument(message="多语言问卷文本题文本类型不一致")
                case QuestionType.SLIDE_SCORE.name:
                    if last_lang_question is not None:
                        if question.get('max_score') != last_lang_question.get('max_score'):
                            raise InvalidArgument(message="多语言问卷评分题分数上限不一致")
                case QuestionType.UPLOAD_FILE.name:
                    if last_lang_question is not None:
                        # 对于type_limit，还要保持顺序一致，所以比较列表而不是set
                        if question.get('type_limit', []) != last_lang_question.get('type_limit', []):
                            raise InvalidArgument(message="多语言问卷文件题文件类型不一致")
                    if last_lang_question is not None:
                        if question.get('size_limit') != last_lang_question.get('size_limit'):
                            raise InvalidArgument(message="多语言问卷文件题尺寸限制不一致")
                case _:
                    raise InvalidArgument("未知的问题类型")

        # 检查问题id是否重复
        if len(question_uids) != len(set(question_uids)):
            raise InvalidArgument(message="问题 uid 不能重复")

        # 校验格式
        _questions = []
        for question in questions:
            if (question_schema := cls.question_schema_map.get(question.get('type'))) is None:
                raise InvalidArgument(message='question type is invalid')

            try:
                question = question_schema.load(question)
                question = cls.convert_enum_to_name(question)
            except Exception as err:
                raise InvalidArgument(message=f'question is invalid {err}')
            _questions.append(question)

        return _questions

    @classmethod
    def check_questionnaire_content_completed(cls, questionnaire_content: QuestionnaireContentMySQL):
        for field in ["title", "description"]:
            if cls.check_empty_string(getattr(questionnaire_content, field)):
                return False

        for question in questionnaire_content.questions:
            if cls.check_empty_string(question.get("question")):
                return False

            match question.get('type'):
                case QuestionType.SINGLE_OP.name | QuestionType.MULTI_OP.name:
                    for option in question.get('options', []):
                        if cls.check_empty_string(option.get("name")):
                            return False
                case _:
                    pass

        return True

    @classmethod
    def check_empty_string(cls, s: str | None) -> bool:
        if s is None:
            return True

        if not isinstance(s, str):
            return True

        if not s.strip():
            return True

        return False

    @classmethod
    def convert_enum_to_name(cls, data):
        if isinstance(data, Enum):
            return data.name
        elif isinstance(data, dict):
            for k, v in data.items():
                data[k] = cls.convert_enum_to_name(v)
        elif isinstance(data, list):
            data = [cls.convert_enum_to_name(i) for i in data]

        return data

    @classmethod
    def get_questionnaire_url_map(cls, questionnaire_uids):
        return {
            uid: f"{QuestionnaireUrlPrefix}/{uid}"
            for uid in questionnaire_uids
        }


class QuestionnaireResponseManage:
    ResponseUidSize = 8
    OnceSubmitResponseUid = "00000000"
    CountryNullFlag = "-"

    answer_schema_map = {
        QuestionType.SINGLE_OP.name: mm_schema(dict(
            option_id=mm_fields.Integer(required=True),
            input=mm_fields.String(validate=Length(max=1024)),
        ))(unknown=EXCLUDE),

        QuestionType.MULTI_OP.name: mm_schema(dict(
            options=mm_fields.List(mm_fields.Nested(
                mm_schema(dict(
                    option_id=mm_fields.Integer(required=True),
                    input=mm_fields.String(validate=Length(max=1024)),
                ))
            ), validate=Length(min=1)),
        ))(unknown=EXCLUDE),

        QuestionType.SLIDE_SCORE.name: mm_schema(dict(
            score=mm_fields.Integer(required=True),
        ))(unknown=EXCLUDE),

        QuestionType.TEXT.name: mm_schema(dict(
            text=mm_fields.String(required=True),
        ))(unknown=EXCLUDE),

        QuestionType.UPLOAD_FILE.name: mm_schema(dict(
            file_key=mm_fields.String(required=True),
        ))(unknown=EXCLUDE),
    }

    @classmethod
    def new_response_uid(cls):
        return new_hex_token(cls.ResponseUidSize)

    @classmethod
    def get_user_answer_map(cls, questionnaire_uid, user_id):
        answers = QuestionnaireAnswerMySQL.query.filter_by(
            questionnaire_uid=questionnaire_uid,
            user_id=user_id,
            response_uid=cls.OnceSubmitResponseUid
        ).all()
        return {answer.question_uid: answer.answer for answer in answers}

    @classmethod
    def check_user_has_once_submit_response(cls, questionnaire_uid, user_id):
        return bool(QuestionnaireResponseMySQL.query.filter_by(
            questionnaire_uid=questionnaire_uid,
            user_id=user_id,
            uid=cls.OnceSubmitResponseUid
        ).with_entities(QuestionnaireResponseMySQL.questionnaire_uid).first())

    # 校验邮箱的正则表达式
    @classmethod
    def validate_email(cls, email):
        pattern = r"^[a-zA-Z0-9_.\-!#$%&'*+/=?^`{}|~]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"
        return re.match(pattern, email) is not None

    # 校验数字的正则表达式
    @classmethod
    def validate_number(cls, number):
        pattern = r'^\d+(\.\d+)?$'
        return re.match(pattern, number) is not None

    # 校验链接的正则表达式
    @classmethod
    def validate_url(cls, url):
        pattern = (r'^(https?:\/\/)?((([a-z\d]([a-z\d-]*[a-z\d])*)\.)+[a-z]{2,}|((\d{1,3}\.){3}\d{1,3}))(\:\d+)?(\/['
                   r'-a-z\d%_.~+]*)*(\?[;&a-z\d%_.~+=-]*)?(\#[-a-z\d_]*)?$')
        return re.match(pattern, url, re.RegexFlag.IGNORECASE) is not None

    # 校验 @id 类型的字符串
    @classmethod
    def validate_at_id(cls, at_id):
        pattern = r'^@[a-zA-Z0-9_]+$'
        return re.match(pattern, at_id) is not None

    @classmethod
    def validate_answer(cls, question, answer):
        if (answer_schema := cls.answer_schema_map.get(question.get('type'))) is None:
            raise InvalidArgument(message='answer type is invalid')

        try:
            answer = answer_schema.load(answer)
        except Exception as err:
            raise InvalidArgument(message=f'answer is invalid {err}')

        match question.get('type'):
            case QuestionType.SINGLE_OP.name:
                for option in question.get('options', []):
                    if answer['option_id'] == option['id']:
                        break
                else:
                    raise InvalidArgument(message=f'answer value is invalid: unknown option_id')
            case QuestionType.MULTI_OP.name:
                option_id_set = set(option['id'] for option in question.get('options', []))
                for option in answer['options']:
                    if option['option_id'] not in option_id_set:
                        raise InvalidArgument(message=f'answer value is invalid: unknown option_id')
            case QuestionType.SLIDE_SCORE.name:
                if not 0 < answer.get('score', 0) <= question.get('max_score', 0):
                    raise InvalidArgument(message=f'answer value is invalid: score over limit')
            case QuestionType.TEXT.name:
                text = answer['text']
                match question.get('type_limit'):
                    case QuestionnaireManage.TextType.EMAIL.name:
                        if not cls.validate_email(text):
                            raise InvalidArgument(message='answer text type is not email')
                    case QuestionnaireManage.TextType.NUMBER.name:
                        if not cls.validate_number(text):
                            raise InvalidArgument(message='answer text type is not number')
                    case QuestionnaireManage.TextType.AT_ID.name:
                        if not cls.validate_at_id(text):
                            raise InvalidArgument(message='answer text type is not at_id')
                    case QuestionnaireManage.TextType.LINK.name:
                        if not cls.validate_url(text):
                            raise InvalidArgument(message='answer text type is not link')
                    case _:
                        pass
            case QuestionType.UPLOAD_FILE.name:
                pass  # 仅上传接口做限制，不再对文件做限制
            case _:
                raise InvalidArgument(message='answer type is invalid')

        return answer

    @classmethod
    def get_response_count_map(cls, questionnaire_uid, group_by):
        if group_by not in ['lang']:
            raise InvalidArgument(message='invalid group_by')

        result = db.session.query(
            getattr(QuestionnaireResponseMySQL, group_by),
            db.func.count().label('count')
        ).filter_by(questionnaire_uid=questionnaire_uid).group_by(
            getattr(QuestionnaireResponseMySQL, group_by)
        ).all()

        return {item[0]: item[1] for item in result}

    @classmethod
    def get_response_user_count_map(cls, questionnaire_uid, group_by):
        if group_by not in ['lang']:
            raise InvalidArgument(message='invalid group_by')

        # 按语言分组统计不重复用户数
        result = db.session.query(
            getattr(QuestionnaireResponseMySQL, group_by),
            db.func.count(db.distinct(QuestionnaireResponseMySQL.user_id)).label('count')
        ).filter_by(questionnaire_uid=questionnaire_uid).group_by(
            getattr(QuestionnaireResponseMySQL, group_by)
        ).all()

        response_user_count_map = {item[0]: item[1] for item in result}

        # 统计总的不重复用户数
        total_users = db.session.query(
            db.func.count(db.distinct(QuestionnaireResponseMySQL.user_id))
        ).filter_by(questionnaire_uid=questionnaire_uid).scalar()

        response_user_count_map["ALL"] = total_users
        return response_user_count_map

    @classmethod
    def get_responses(cls, filters, page, limit):
        responses = only_query_pagination(
            QuestionnaireResponseMySQL.query.filter_by(**filters).order_by(QuestionnaireResponseMySQL.created_at),
            page,
            limit
        )

        response_uids = [response.uid for response in responses]
        answer_map = defaultdict(lambda: defaultdict(dict))

        answers = QuestionnaireAnswerMySQL.query.filter(
            QuestionnaireAnswerMySQL.response_uid.in_(response_uids)
        ).with_entities(
            QuestionnaireAnswerMySQL.response_uid,
            QuestionnaireAnswerMySQL.question_uid,
            QuestionnaireAnswerMySQL.answer,
            QuestionnaireAnswerMySQL.user_id
        ).all()

        for answer in answers:
            answer_map[answer.response_uid][answer.user_id][answer.question_uid] = dict(answer.answer)

        _responses = []
        for response in responses:
            _responses.append((response, answer_map[response.uid][response.user_id]))

        return _responses

    @classmethod
    def get_responses_count(cls, filters):
        return QuestionnaireResponseMySQL.query.filter_by(**filters).count()

    @classmethod
    def get_all_responses(cls, filters):
        page = 1
        limit = 1000
        while responses := cls.get_responses(filters, page, limit):
            for response in responses:
                yield response
            page += 1

    @classmethod
    def get_show_lang(cls, questionnaire_uid):
        questionnaire_content = QuestionnaireManage.get_questionnaire_content(questionnaire_uid)

        # 优先检查有没有中英文
        for lang in [Language.ZH_HANS_CN, Language.ZH_HANT_HK, Language.EN_US]:
            if questionnaire_content[lang].completed:
                return lang

        # 获取用户提交最多的语言
        lang_count_map = cls.get_response_count_map(questionnaire_uid, "lang")
        if lang_count_map:
            return max(lang_count_map, key=lang_count_map.get)

        # 无提交的情况下，获取第一个能查到的语言
        for lang in Language:
            if questionnaire_content[lang].completed:
                return lang

        raise InvalidArgument(message="无法获取完整的问卷内容")

    @classmethod
    def format_answer(cls, answer, question, only_value=False, file_ttl=None):
        if not answer:
            if only_value:
                return ""
            return answer

        match question['type']:
            case QuestionType.SINGLE_OP.name:
                for option in question['options']:
                    if option['id'] == answer.get('option_id', 0):
                        answer['option'] = f"{option['id']}.{option['name']}"
                        answer['has_input'] = option.get('has_input', False)
                        break
                if only_value:
                    res = [answer['option']]
                    if answer['has_input'] and answer.get('input'):
                        res.append(f"输入内容: {answer.get('input')}")
                    return '\n'.join(res)
            case QuestionType.MULTI_OP.name:
                options = answer.get('options', [])
                option_map = {op['option_id']: dict(op) for op in options}
                _options = []
                for option in question['options']:
                    if option['id'] in option_map:
                        _option = option_map[option['id']]
                        _option['option'] = f"{option['id']}.{option['name']}"
                        _option['has_input'] = option.get('has_input', False)
                        _options.append(_option)
                answer['options'] = _options
                if only_value:
                    res = []
                    for option in _options:
                        res.append(option['option'])
                        if option['has_input'] and option.get('input'):
                            res.append(f"输入内容: {option.get('input')}")
                    return '\n'.join(res)
            case QuestionType.UPLOAD_FILE.name:
                if answer.get('file_key'):
                    answer['file_url'] = AWSBucketPrivate.get_file_url(answer['file_key'], file_ttl)
                if only_value:
                    return answer.get('file_url') or answer.get('file_key', '')
            case QuestionType.TEXT.name:
                if only_value:
                    return answer.get('text', '')
            case QuestionType.SLIDE_SCORE.name:
                if only_value:
                    return str(answer.get('score', -1))  # 异常值-1
            case _:
                pass

        return answer

    @classmethod
    def format_duration(cls, seconds):
        # 计算小时、分钟和秒数
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60

        # 返回格式化的字符串
        if hours > 0:
            return f"{hours}'{minutes:02d}'{seconds:02d}"
        else:
            return f"{minutes}'{seconds:02d}"


@celery_task(queue=CeleryQueues.REPORT)
@lock_call(with_args=['email', 'questionnaire_uid'])
def export_questionnaire_response(email, questionnaire_uid):
    if not email:
        return
    questionnaire_content = QuestionnaireManage.get_questionnaire_content(questionnaire_uid)
    if not questionnaire_content:
        return

    show_lang = QuestionnaireResponseManage.get_show_lang(questionnaire_uid)
    ret = []
    langs = language_cn_names()
    for response, answers in QuestionnaireResponseManage.get_all_responses(dict(
            questionnaire_uid=questionnaire_uid,
    )):
        record = {
            'user_id': response.user_id,
            'country_cn_name': get_country(
                response.country).cn_name if response.country else QuestionnaireResponseManage.CountryNullFlag,
            'lang_cn_name': langs[response.lang],
            'platform': response.platform.value,
            'time': QuestionnaireResponseManage.format_duration(response.time),
        }

        for idx, question in enumerate(questionnaire_content.get(response.lang).questions):
            match question['type']:
                # 多选题需要展开成多列展示
                case QuestionType.MULTI_OP.name:
                    options_count = max(len(question.get('options', [])), 1)
                    answer = QuestionnaireResponseManage.format_answer(
                        answers.get(question['uid'], {}), question, only_value=False, file_ttl=60 * 60 * 24 * 7,
                    )

                    options = answer.get('options', [])
                    option_map = {op['option_id']: dict(op) for op in options}
                    for i in range(options_count):
                        option = option_map.get(i + 1, {})
                        option_str = option.get('option', "")
                        if option.get('has_input') and option.get('input'):
                            option_str += f"\n输入内容: {option.get('input')}"
                        record[f'question{idx}_{i}'] = option_str

                case _:
                    record[f'question{idx}'] = QuestionnaireResponseManage.format_answer(
                        answers.get(question['uid'], {}), question, only_value=True, file_ttl=60 * 60 * 24 * 7,
                    )

        ret.append(record)

    header_mapping = {
        "user_id": "用户id",
        "country_cn_name": "用户国家",
        "lang_cn_name": "用户语区",
        "platform": "填写终端",
        "time": "填写时长",
    }

    for idx, question in enumerate(questionnaire_content[show_lang].questions):
        match question['type']:
            # 多选题需要展开成多列展示
            case QuestionType.MULTI_OP.name:
                options_count = max(len(question.get('options', [])), 1)
                for i in range(options_count):
                    header_mapping[f'question{idx}_{i}'] = question['question'] + f"\n选项{i+1}"
            case _:
                header_mapping[f'question{idx}'] = question['question']

    desc = '运营-活动-问卷工具-问卷明细-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


class QuestionnaireStatisticManage:
    UNSUBMIT_FLAG = "-1"
    UNSUBMIT_FIELD_NAME = "未提交"

    @classmethod
    def get_questionnaire_statistic_map(cls, questionnaire_uids, only: [str], lang: Language = None) -> dict:
        questionnaire_statistic_map = {}
        query = QuestionnaireStatisticMySQL.query.filter(
            QuestionnaireStatisticMySQL.questionnaire_uid.in_(questionnaire_uids)
        )
        if only:
            query = query.with_entities(
                QuestionnaireStatisticMySQL.questionnaire_uid,
                *[getattr(QuestionnaireStatisticMySQL, field) for field in only]
            )
        for qs in query.all():
            questionnaire_statistic = {}
            for field in only:
                match field:
                    case "submit_count":
                        if lang is not None:
                            questionnaire_statistic[field] = getattr(qs, field).get(lang.value, 0)
                        else:
                            questionnaire_statistic[field] = getattr(qs, field).get("ALL", 0)
                    case "view_count" | "login_view_count" | "user_lang_count":
                        if lang is not None:
                            questionnaire_statistic[field] = getattr(qs, field).get(lang.value, 0)
                        else:
                            questionnaire_statistic[field] = sum(getattr(qs, field).values())

                    case "user_country_count_map" | "user_platform_count_map":
                        if lang is not None:
                            questionnaire_statistic[field] = getattr(qs, field).get(lang.value, {})
                        else:
                            merge_count_map = defaultdict(int)
                            for lang, count_map in getattr(qs, field).items():
                                for k, count in count_map.items():
                                    merge_count_map[k] += count
                            questionnaire_statistic[field] = merge_count_map
                    case "user_answer_map":
                        pass
                    case _:
                        raise KeyError(f'Unknown field "{field}"')

            questionnaire_statistic_map[qs.questionnaire_uid] = questionnaire_statistic

        return questionnaire_statistic_map

    @classmethod
    def get_questionnaire_statistic_data(cls, questionnaire_uid, lang: Language = None):
        if lang is not None:
            lang = lang.value

        questionnaire_statistic = cls.get_questionnaire_statistic(questionnaire_uid)
        questionnaire_content = QuestionnaireManage.get_questionnaire_content(questionnaire_uid)
        if not questionnaire_content:
            return {}, []

        # format user_data
        langs = {lang.value: lang_value for lang, lang_value in language_cn_names().items()}
        user_data = {}
        for field in ["user_country_count_map", "user_platform_count_map"]:
            count_map = getattr(questionnaire_statistic, field)
            if lang is not None:
                user_data[field] = count_map.get(lang, {})
            else:
                _count_map = defaultdict(int)
                for question_uid_count_map in count_map.values():
                    for question_uid, count in question_uid_count_map.items():
                        _count_map[question_uid] += count
                user_data[field] = _count_map

        user_lang_count_map = questionnaire_statistic.user_lang_count
        if lang is not None:
            user_data['user_lang_count'] = {lang: user_lang_count_map.get(lang, 0)}
        else:
            user_data['user_lang_count'] = user_lang_count_map

        _user_data = {}
        for k, v in user_data.items():
            _sum = sum(v.values())
            v = sorted(v.items(), key=lambda item: item[1], reverse=True)

            match k:
                case "user_country_count_map":
                    _user_data["user_country_count"] = [{
                        "country_code": country_code,
                        "country_name": (QuestionnaireResponseManage.CountryNullFlag
                                         if country_code == QuestionnaireResponseManage.CountryNullFlag
                                         else get_country(country_code).cn_name),
                        "rate": count * 100 / _sum if _sum else 0,
                    } for country_code, count in v]
                case "user_platform_count_map":
                    _user_data["user_platform_count"] = [{
                        "platform": platform,
                        "rate": count * 100 / _sum if _sum else 0,
                    } for platform, count in v]
                case "user_lang_count":
                    _user_data[k] = [{
                        "lang": langs[lang],
                        "rate": count * 100 / _sum if _sum else 0,
                    } for lang, count in v]

        # format question_data
        show_lang = QuestionnaireResponseManage.get_show_lang(questionnaire_uid)
        questions = questionnaire_content[show_lang].questions
        user_answer_map = questionnaire_statistic.user_answer_map
        if lang is not None:
            question_data = user_answer_map.get(lang, {})
            submit_count = user_lang_count_map.get(lang, 0)
        else:
            question_data = defaultdict(lambda: defaultdict(int))
            for user_answer_question_map in user_answer_map.values():
                for question_uid, answer_count_map in user_answer_question_map.items():
                    for answer, count in answer_count_map.items():
                        question_data[question_uid][answer] += count

            submit_count = sum(user_lang_count_map.values())

        _question_datas = []
        for question in questions:
            question_uid = question['uid']
            answer_count_map = question_data.get(question_uid, {})

            _question_data = {
                "question_uid": question_uid,
                "question_type": question['type'],
                "question": question['question'],
            }
            match question['type']:
                case QuestionType.SINGLE_OP.name | QuestionType.MULTI_OP.name:
                    answers = [{
                        "option_id": option['id'],
                        "answer": f"{index + 1}.{option['name']}",
                        "count": answer_count_map.get(str(option['id']), 0),
                        "rate": answer_count_map.get(str(option['id']), 0) * 100 / submit_count if submit_count else 0,
                    } for index, option in enumerate(question['options'])]

                    if cls.UNSUBMIT_FLAG in answer_count_map:
                        answers.append({
                            "option_id": cls.UNSUBMIT_FLAG,
                            "answer": cls.UNSUBMIT_FIELD_NAME,
                            "count": answer_count_map.get(cls.UNSUBMIT_FLAG, 0),
                            "rate": answer_count_map.get(
                                cls.UNSUBMIT_FLAG, 0) * 100 / submit_count if submit_count else 0,
                        })

                    answers = sorted(answers, key=lambda item: item['rate'], reverse=True)
                    _question_data['answers'] = answers
                case QuestionType.SLIDE_SCORE.name:
                    answers = [{
                        "score": score,
                        "answer": score,
                        "count": answer_count_map.get(str(score), 0),
                        "rate": answer_count_map.get(str(score), 0) * 100 / _sum if _sum else 0,
                    } for score in range(1, question['max_score'] + 1)]

                    if cls.UNSUBMIT_FLAG in answer_count_map:
                        answers.append({
                            "option_id": int(cls.UNSUBMIT_FLAG),
                            "answer": cls.UNSUBMIT_FIELD_NAME,
                            "count": answer_count_map.get(cls.UNSUBMIT_FLAG, 0),
                            "rate": answer_count_map.get(
                                cls.UNSUBMIT_FLAG, 0) * 100 / submit_count if submit_count else 0,
                        })

                    answers = sorted(answers, key=lambda item: item['rate'], reverse=True)
                    _question_data['answers'] = answers
                case QuestionType.TEXT.name:
                    top_count_text = []
                    # 任意输入的次数大于1，就认为有高频输入
                    if answer_count_map and max(answer_count_map.values()) > 1:
                        top_count_text = [
                            cls.text_decode(key) for key, value in
                            sorted(answer_count_map.items(), key=lambda item: item[1], reverse=True)[:10]
                        ]

                    _question_data['text'] = top_count_text
                case QuestionType.UPLOAD_FILE.name:
                    pass

            _question_datas.append(_question_data)

        return _user_data, _question_datas

    @classmethod
    def get_questionnaire_statistic(cls, questionnaire_uid) -> QuestionnaireStatisticMySQL:
        if (qs := QuestionnaireStatisticMySQL.query.filter_by(questionnaire_uid=questionnaire_uid).first()) is not None:
            return qs

        qs = QuestionnaireStatisticMySQL(
            questionnaire_uid=questionnaire_uid,
            view_count={lang.value: 0 for lang in Language},
            login_view_count={lang.value: 0 for lang in Language},
            submit_count={lang.value: 0 for lang in Language},
            user_country_count_map={lang.value: {} for lang in Language},
            user_lang_count={lang.value: 0 for lang in Language},
            user_platform_count_map={lang.value: {
                platform.value: 0
                for platform in QuestionnaireResponseMySQL.Platform
            } for lang in Language},
            user_answer_map={lang.value: {} for lang in Language},
        )
        db.session.add(qs)
        db.session.commit()
        return qs

    @classmethod
    def update_questionnaire_statistic_count(cls, questionnaire_uid):
        questionnaire_statistic = cls.get_questionnaire_statistic(questionnaire_uid)

        for field, counter in {
            "view_count": QuestionnaireViewCountCache,
            "login_view_count": QuestionnaireLoginViewCountCache,
        }.items():

            count_map = getattr(questionnaire_statistic, field).copy()
            for lang in Language:
                if (count := counter(questionnaire_uid, lang.value).getset("0")) is not None:
                    count_map[lang.value] += int(count)

            setattr(questionnaire_statistic, field, count_map)

        user_lang_count = questionnaire_statistic.user_lang_count.copy()
        user_submit_count = questionnaire_statistic.submit_count.copy()

        user_lang_count.update({
            k.value if not isinstance(k, str) else k: v
            for k, v in
            QuestionnaireResponseManage.get_response_count_map(questionnaire_uid, "lang").items()
        })
        user_submit_count.update({
            k.value if not isinstance(k, str) else k: v
            for k, v in
            QuestionnaireResponseManage.get_response_user_count_map(questionnaire_uid, "lang").items()
        })

        questionnaire_statistic.user_lang_count = user_lang_count
        questionnaire_statistic.submit_count = user_submit_count
        db.session.commit()

    @classmethod
    def update_questionnaire_statistic(cls, questionnaire_uid):
        questionnaire_statistic = cls.get_questionnaire_statistic(questionnaire_uid)
        questionnaire_content = QuestionnaireManage.get_questionnaire_content(questionnaire_uid)
        if not questionnaire_content:
            return

        questions = list(questionnaire_content.values())[0].questions

        user_country_count_map = {lang.value: {} for lang in Language}
        user_lang_count = {lang.value: 0 for lang in Language}
        user_platform_count_map = {lang.value: {
            platform.value: 0
            for platform in QuestionnaireResponseMySQL.Platform
        } for lang in Language}
        user_answer_map = {lang.value: {} for lang in Language}

        # {lang: {text_question_uid: counter}}
        text_counter_map = {lang.value: defaultdict(lambda: defaultdict(int)) for lang in Language}

        for response, answers in QuestionnaireResponseManage.get_all_responses(dict(
                questionnaire_uid=questionnaire_uid,
        )):

            country = response.country or QuestionnaireResponseManage.CountryNullFlag
            user_country_count_map[response.lang.value][
                country] = user_country_count_map[response.lang.value].get(country, 0) + 1
            user_lang_count[response.lang.value] += 1
            user_platform_count_map[response.lang.value][response.platform.value] += 1

            user_answer_lang_map = user_answer_map[response.lang.value]
            for idx, question in enumerate(questions):
                question_uid = question['uid']

                answer = answers.get(question_uid, {})
                user_answer_lang_question_map = user_answer_lang_map.get(question_uid, defaultdict(int))
                match question['type']:
                    case QuestionType.SINGLE_OP.name:
                        if answer:
                            user_answer_lang_question_map[str(answer['option_id'])] += 1
                        else:
                            user_answer_lang_question_map[cls.UNSUBMIT_FLAG] += 1  # 代表未填写
                    case QuestionType.MULTI_OP.name:
                        if answer:
                            for option in answer['options']:
                                option_id = option['option_id']
                                user_answer_lang_question_map[str(option_id)] += 1
                        else:
                            user_answer_lang_question_map[cls.UNSUBMIT_FLAG] += 1  # 代表未填写
                    case QuestionType.SLIDE_SCORE.name:
                        if answer:
                            user_answer_lang_question_map[str(answer['score'])] += 1
                        else:
                            user_answer_lang_question_map[cls.UNSUBMIT_FLAG] += 1  # 代表未填写
                    case QuestionType.TEXT.name:
                        if answer.get('text'):
                            text_counter_map[response.lang.value][question_uid][answer['text']] += 1
                    case _:
                        continue

                user_answer_lang_map[question_uid] = user_answer_lang_question_map

        # 获取top10高频text
        for lang, text_counter in text_counter_map.items():
            user_answer_lang_map = user_answer_map[lang]
            for question_uid, counter in text_counter.items():
                filtered_items = [(key, value) for key, value in counter.items() if value != 1]
                top_10_max_text = sorted(filtered_items, key=lambda x: x[1], reverse=True)[:10]
                user_answer_lang_map[question_uid] = {cls.text_encode(k): v for k, v in top_10_max_text}

        questionnaire_statistic.user_country_count_map = user_country_count_map
        questionnaire_statistic.user_lang_count = user_lang_count
        questionnaire_statistic.user_platform_count_map = user_platform_count_map
        questionnaire_statistic.user_answer_map = user_answer_map
        db.session.commit()

        cls.update_questionnaire_statistic_count(questionnaire_uid)

    @staticmethod
    def text_encode(text: str) -> str:
        """ 统计表中会写入以用户输入为key的数据，当用户输入 "." 和 "$" 时，会触发mongo的插入错误，为避免该错误，使用b64编解码用户输入 """
        if not text:
            return text

        return b64encode(text.encode()).decode()

    @staticmethod
    def text_decode(text: str) -> str:
        if not text:
            return text

        return b64decode(text.encode()).decode()
