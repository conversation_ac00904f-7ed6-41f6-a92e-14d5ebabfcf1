# -*- coding: utf-8 -*-
import json
import time
from copy import deepcopy
from datetime import date, timedelta, datetime
from decimal import Decimal
from functools import partial
from typing import Dict, Union, Optional, List, Any
from logging import getLogger
from traceback import format_exc

from flask_babel import gettext
from flask_babel import gettext as _
from sqlalchemy import func, or_
from sqlalchemy.orm import Session

from . import CacheLock, LockKeys
from .wallet_integration import cancel_withdrawal
from .clients.wallet import WalletClient
from .risk_control import withdrawals_disabled_by_risk_control
from ..assets import has_asset, get_asset_chain_config, get_asset_config
from ..caches.assets import WalletNodeAbnormalStatusCache, HotWalletBalanceCache
from ..caches.user import UserBalanceSumCache
from ..exceptions import (
    InvalidArgument, AssetNotFound, WalletUnderMaintenance,
    WithdrawalsSuspended, InvalidWithdrawalAddress, WithdrawalPrecisionExceeded,
    WithdrawalAmountTooSmall, LocalTransfersSuspended, InvalidAccount, InsufficientBalance,
    WithdrawalLimitExceeded, DepositPrivacyAssetRequireKyc, WithdrawalPrivacyAssetRequireKyc,
    WithdrawalFeeAssetNotCustomizable, InternalServerError, EmailNotBound, WithdrawalForbidden,
    WithdrawalLimit30DaysExceeded, ServiceUnavailable
)
from ..exceptions.p2p import P2pSellForbiddenAfterSecurityEditing, P2pSellForbiddenAfterWithdrawPasswordEditing, \
    P2pExceptionMap, P2pExceptionCode
from ..models import (
    Deposit, Withdrawal, User, CreditUser, new_session, WithdrawalPrivilegedUser,
    ApiWithdrawalAddress, db, WithdrawalFeeHistory, WithdrawalApprover, WithdrawalApprove,
    EmailToken, ApiWithdrawalAddressWhiteListUser, RiskUser
)
from ..models.spot import SystemAssetLiability
from ..common import BalanceBusiness, LOCAL_TRANSFER_MAX_PRECISION, CUSTOM_WITHDRAWAL_FEE_ASSETS, \
    WITHDRAWAL_WIDE_LIMIT_MULTI
from ..models.wallet import WithdrawalRestrictedFund, WithdrawalRestrictedFundChangeLog, WithdrawalCancel
from ..utils import quantize_amount, now, WhyNot, quantize_amount_non_zero, datetime_to_time
from ..business import UserSettings, SiteSettings, lock_call
from ..business.email import (
    send_sent_withdrawal_notice_email, send_credited_deposit_notice_email,
    send_withdrawal_confirmation_email,
)
from .clients import ServerClient
from ..caches import (UserDailyWithdrawnAmountCache, AssetSettingViewCache,
                      AssetFileConfigViewCache)
from ..caches.wallets import CustomWithdrawalAssetPriceCache, User30DaysWithdrawalUsdCache
from .prices import PriceManager


_logger = getLogger(__name__)


@lock_call(wait=True)
def update_assets():
    from app.schedules.wallet.assets import sync_assets

    _logger.info('update_assets: updating caches...')
    sync_assets()

    _logger.info('update_assets: refreshing API caches...')
    AssetSettingViewCache.reload()
    AssetFileConfigViewCache.reload()

    _logger.info('update_assets: informing server...')
    ServerClient().update_assets()

    _logger.info('update_assets: done')


def _get_user_confirming_deposit_amounts(user_id: int) -> Dict[str, Decimal]:
    rows = (Deposit.query
                   .filter(Deposit.user_id == user_id,
                           Deposit.status.in_((Deposit.Status.CONFIRMING,
                                               Deposit.Status.EXCEPTION)))
                   .group_by(Deposit.asset, Deposit.status)
                   .with_entities(Deposit.asset, Deposit.status, func.sum(Deposit.amount))
                   .all())
    result = {}
    for asset, status, amount in rows:
        if status == Deposit.Status.EXCEPTION:
            # 考虑币价波动的影响。确认中的不考虑，防止用户本来可以提，充值了一笔(确认中)导致反而不可提的情况。
            amount = quantize_amount(amount * Decimal('1.2'), 8)
        result.setdefault(asset, Decimal())
        result[asset] += amount
    return result


def get_user_withdrawable_amount(user_id: int, asset: str, realtime: bool = True
                                 ) -> Dict[str, Decimal]:
    from app.business.credit import (get_credit_user_real_time_can_withdraw_usd,
                                     has_credit_unflat_asset)

    user = User.query.get(user_id)

    server_client = ServerClient()
    balances = server_client.get_user_balances(user_id)
    confirming_deposts = _get_user_confirming_deposit_amounts(user_id)
    prices = PriceManager.assets_to_usd()

    asset_price = prices[asset]
    available_asset = balances.get(asset, {}).get('available', Decimal())
    frozen_asset = balances.get(asset, {}).get('frozen', Decimal())
    total_available_usd = sum(prices.get(a, 0) * item.get('available', 0) for a, item in balances.items())

    # 可提现市值 = min(现货账户可用资产市值, 授信剩余可提现市值) - 确认中的充值资产市值
    # 币种可提现数量 = min(可提现市值 / 币种价格, 币种可用数量)

    credit_limit_usd = None     # None means no credit limit, zero means limit is zero.
    confirming_usd = Decimal()

    if confirming_deposts:
        confirming_usd = sum(prices.get(a, 0) * amount for a, amount in confirming_deposts.items())

    if user.user_type is not User.UserType.INTERNAL_MAKER:
        credit_user: CreditUser = user.main_user.credit_user
        if (credit_user is not None
                and credit_user.status is CreditUser.StatusType.PASS
                and has_credit_unflat_asset(user.main_user)):  # 没有授信未还时不用计算，避免误差
            if realtime:
                _, can_withdraw_balance = get_credit_user_real_time_can_withdraw_usd(user.main_user_id)
            else:
                can_withdraw_balance = credit_user.can_withdraw_balance
            credit_limit_usd = max(can_withdraw_balance, Decimal())

    max_withdrawable_usd = total_available_usd if credit_limit_usd is None else min(total_available_usd, credit_limit_usd)
    max_withdrawable_usd -= confirming_usd
    max_withdrawable_usd -= WithdrawalRestrictedFundHelper.get_user_fund_usd(user_id)

    if max_withdrawable_usd == total_available_usd: # 直接使用币种可用数量即可，避免转换成USD计算带来的误差
        withdrawable_asset = available_asset
    else:
        withdrawable_asset = max_withdrawable_usd / asset_price
        withdrawable_asset = min(withdrawable_asset, available_asset)
        withdrawable_asset = max(withdrawable_asset, Decimal())

    quantize = partial(quantize_amount, decimals=8)

    return dict(
        withdrawable_asset=quantize(withdrawable_asset),
        frozen_asset=quantize(frozen_asset),
        confirming_usd=quantize(confirming_usd),
        asset_price=asset_price,
        # 下面两个字段为了兼容旧版API，不要使用
        available_asset=quantize(available_asset),
        withdrawable_usd=quantize(withdrawable_asset * asset_price),
    )


def get_every_status_withdrawal_limit(user_id: int):
    user_settings = UserSettings(user_id)
    manual_limit = user_settings.daily_withdrawal_limit
    quantize = partial(quantize_amount, decimals=8)
    return dict(
        no_2fa=Decimal(),
        has_2fa=quantize(SiteSettings.user_daily_withdrawal_limit_without_kyc),
        has_kyc=quantize(SiteSettings.user_daily_withdrawal_limit_with_kyc),
        has_kyc_pro=quantize(SiteSettings.user_daily_withdrawal_limit_with_kyc_pro),
        has_kyc_institution=quantize(SiteSettings.user_daily_withdrawal_limit_with_kyc_institution),
        manual_limit=manual_limit,
    )


def get_user_daily_withdrawal_limit(user: Union[int, User]
                                    ) -> Dict[str, Decimal]:
    if isinstance(user, int):
        user_id = user
        if (user := User.query.get(user_id)) is None:
            raise ValueError(f'invalid user id: {user_id!r}')
    else:
        user_id = user.id

    user_settings = UserSettings(user_id)

    if not user_settings.withdrawals_enabled:
        limit_usd = Decimal()
    elif user.kyc_status is User.KYCStatus.PASSED:
        if user.kyc_type == user.KYCType.INSTITUTION:
            limit_usd = SiteSettings.user_daily_withdrawal_limit_with_kyc_institution
        else:
            limit_usd = SiteSettings.user_daily_withdrawal_limit_with_kyc
            if user.kyc_pro_status is User.KycProStatus.PASSED:
                limit_usd = SiteSettings.user_daily_withdrawal_limit_with_kyc_pro
    else:
        limit_usd = SiteSettings.user_daily_withdrawal_limit_without_kyc

    if (manual_limit := user_settings.daily_withdrawal_limit) is not None:
        limit_usd = max(limit_usd, manual_limit)

    withdrawn_usd = UserDailyWithdrawnAmountCache(user_id).get_withdrawn_amount()
    quantize = partial(quantize_amount, decimals=8)
    return dict(
        limit_usd=quantize(limit_usd),
        withdrawn_usd=quantize(withdrawn_usd)
    )


def get_30_days_withdrawal_limit(user: Union[int, User]):
    if isinstance(user, int):
        user_id = user
        if (user := User.query.get(user_id)) is None:
            raise ValueError(f'invalid user id: {user_id!r}')
    else:
        user_id = user.id
    if user.kyc_status == User.KYCStatus.PASSED:
        return dict(
            kyc_status=True,
            limit_usd=None,
            withdrawn_usd=None
        )
    else:
        user_settings = UserSettings(user_id)
        limit_usd = SiteSettings.user_30_days_withdrawal_limit_without_kyc
        if (manual_limit := user_settings.withdrawal_limit_30_days) is not None:
            limit_usd = max(limit_usd, manual_limit)
        return dict(
            kyc_status=False,
            limit_usd=limit_usd,
            withdrawn_usd=User30DaysWithdrawalUsdCache(user_id).get_all_amount()
        )


def check_withdrawal_amount_exceeded(user: Union[int, User], asset: str, amount: Decimal):
    to_withdrawal_usd = PriceManager.asset_to_usd(asset) * amount
    exceeded, from_daily_limit = user_withdrawal_amount_exceeded(user, to_withdrawal_usd)
    if not exceeded:
        return True
    if from_daily_limit:
        raise WithdrawalLimitExceeded
    else:
        raise WithdrawalLimit30DaysExceeded


def user_withdrawal_amount_exceeded(user: Union[int, User], to_withdrawal_usd: Decimal = Decimal()) -> (bool, bool):
    withdrawal_daily_info = get_user_daily_withdrawal_limit(user)
    daily_remain_withdrawn_usd = withdrawal_daily_info['limit_usd'] - withdrawal_daily_info['withdrawn_usd']
    withdrawal_info_30_days = get_30_days_withdrawal_limit(user)
    from_daily_limit = True
    if withdrawal_info_30_days['kyc_status']:
        remain_withdrawn_usd = daily_remain_withdrawn_usd
    else:
        remain_withdrawn_usd_30_days = withdrawal_info_30_days['limit_usd'] - withdrawal_info_30_days['withdrawn_usd']
        remain_withdrawn_usd = min(remain_withdrawn_usd_30_days, daily_remain_withdrawn_usd)
        if remain_withdrawn_usd == remain_withdrawn_usd_30_days:
            from_daily_limit = False

    return to_withdrawal_usd > remain_withdrawn_usd * WITHDRAWAL_WIDE_LIMIT_MULTI, from_daily_limit


def check_user_withdrawn_amount_exceeded(user: Union[int, User]):
    exceeded, from_daily_limit = user_withdrawal_amount_exceeded(user)
    if not exceeded:
        return True
    if from_daily_limit:
        raise WithdrawalLimitExceeded
    else:
        raise WithdrawalLimit30DaysExceeded


def add_withdrawal_amount_to_cache(user_id, asset, amount, at: datetime = None):
    user = User.query.get(user_id)
    UserDailyWithdrawnAmountCache(user_id).add_amount(asset, amount, at)
    if user.kyc_status != User.KYCStatus.PASSED:
        User30DaysWithdrawalUsdCache(user_id).add_amount(asset, amount, at)


def finish_local_withdrawal(withdrawal_id: int, session: Session = None
                            ) -> Optional[Deposit]:
    if session is None:
        with new_session() as session:
            return finish_local_withdrawal(withdrawal_id, session)

    withdrawal: Withdrawal = session.query(Withdrawal) \
        .filter(Withdrawal.id == withdrawal_id,
                Withdrawal.type == Withdrawal.Type.LOCAL,
                Withdrawal.status == Withdrawal.Status.AUDITED) \
        .first()
    if withdrawal is None:
        return None

    withdrawal.status = Withdrawal.Status.FINISHED
    withdrawal.sent_at = now()
    deposit = Deposit(
        user_id=withdrawal.recipient_user_id,
        type=Deposit.Type.LOCAL,
        asset=withdrawal.asset,
        address=withdrawal.address,
        amount=withdrawal.amount,
        sender_user_id=withdrawal.user_id,
        status=Deposit.Status.PROCESSING
    )
    session.add(deposit)
    session.commit()

    credit_local_deposit(deposit.id, session)

    send_sent_withdrawal_notice_email.delay(withdrawal_id)

    return deposit


def credit_local_deposit(deposit_id: int, session: Session = None) -> bool:
    if session is None:
        with new_session() as session:
            return credit_local_deposit(deposit_id, session)

    deposit = session.query(Deposit) \
        .filter(Deposit.id == deposit_id,
                Deposit.type == Deposit.Type.LOCAL,
                Deposit.status == Deposit.Status.PROCESSING) \
        .first()
    if deposit is None:
        return False

    server_client = ServerClient()
    try:
        server_client.add_user_balance(
            deposit.user_id,
            deposit.asset,
            deposit.amount,
            BalanceBusiness.DEPOSIT,
            deposit.id)
    except server_client.BadResponse:
        _logger.error(format_exc())
    else:
        deposit.confirmed_at = now()
        deposit.status = Deposit.Status.FINISHED
        session.commit()
        send_credited_deposit_notice_email.delay(deposit.id)
    return True


def cal_monthly_avg_hot_balance(asset: str, query_month: date) -> Decimal:
    return cal_monthly_avg_wallet_balance(asset, query_month, 'hot')


def cal_monthly_avg_cold_balance(asset: str, query_month: date) -> Decimal:
    return cal_monthly_avg_wallet_balance(asset, query_month, 'cold')


def cal_monthly_avg_wallet_balance(asset: str, query_month: date,
                                   wallet: str) -> Decimal:
    start_date = query_month.replace(day=1)
    end_date = (start_date + timedelta(days=32)).replace(day=1)

    query = SystemAssetLiability.query.filter(
        SystemAssetLiability.asset == asset,
        SystemAssetLiability.created_at < end_date,
        SystemAssetLiability.created_at >= start_date
    )
    if wallet == "hot":
        balance = query.with_entities(
            func.avg(SystemAssetLiability.hot_wallet).label('average')
        ).first().average or Decimal()
    elif wallet == "cold":
        balance = query.with_entities(
            func.avg(SystemAssetLiability.cold_wallet).label('average')
        ).first().average or Decimal()
    else:
        raise Exception(f'Not supported wallet: {wallet}')
    return quantize_amount(balance, 8)


class AddressExtraConfigs:
    """用于特殊币种充提时额外信息输入"""

    @staticmethod
    def _convert_kda_chain_id(v: str):
        v = int(v)
        if not 0 <= v < 20:
            raise InvalidArgument
        return v

    EXTRA_CONFS = {
        'KDA': [{
            'type': 'select',  # select or input
            'key': 'chain_id',  # used for request
            'name': gettext('链号'),  # used for display
            'description': gettext('Kadena 目前支持20条链运行，并以「Chain 0」-「Chain 19」进行命名，'
                                   '不同链号之间资产相互隔离，因此同一笔转账金额不能同时来源于多条链。'
                                   '提现时需按照收款方对链号的要求进行选择，若无要求可选择默认链号。'),
            'required': True,
            'default': '0',  # default value is used for frontend.
            'options': [
                dict(key=str(i), value=str(i)) for i in range(20)
            ],  # for select. key and value are string type. not need for input.
            'converter': '_convert_kda_chain_id'
        }]
    }

    @classmethod
    def get_extra_conf(cls, chain) -> List:
        conf = cls.EXTRA_CONFS.get(chain)
        if not conf:
            return []
        conf = deepcopy(conf)
        for c in conf:
            c['name'] = gettext(c['name'])
            c['description'] = gettext(c['description'])
            c.pop('converter')
        return conf

    @classmethod
    def format_extra(cls, extra: Optional[str]) -> Dict:
        """format extra to response"""
        # inputs are all string types, so return as is.
        if not extra:
            return {}
        extra = json.loads(extra)
        return {k: str(v) for k, v in extra.items()}

    @classmethod
    def dump_extra(cls, extra: Optional[Dict]) -> Optional[str]:
        """dump extra to database"""
        if not extra:
            return None
        return json.dumps(extra)

    @classmethod
    def reslove_extra(cls, chain, extra: Dict[str, str]) -> Dict[str, Any]:
        """reslove extra from request"""
        if not (conf := cls.EXTRA_CONFS.get(chain)):
            return {}
        values = {}
        for i in conf:
            v = extra.get(i['key'])
            if i['required'] and v is None:
                raise InvalidArgument
            if v:
                try:
                    values[i['key']] = getattr(cls, i['converter'])(v)
                except Exception:
                    raise InvalidArgument
        return values

    @classmethod
    def require_extra(cls, chain) -> bool:
        try:
            cls.reslove_extra(chain, {})
        except InvalidArgument:
            return True
        return False


class WithdrawalHelper:

    @classmethod
    def validate_fee_asset(cls,
                           asset: str,
                           fee_asset: str
        ):
        fee_is_same_asset = fee_asset == asset
        if not fee_is_same_asset and fee_asset not in CUSTOM_WITHDRAWAL_FEE_ASSETS:
            raise InvalidArgument

    @classmethod
    def get_custom_fee_amount(cls, asset: str, chain: str, fee_asset: str) -> Decimal:
        cs_fees = get_custom_withdrawal_fee_by_chain(asset, chain)
        if fee_asset not in cs_fees:
            raise WithdrawalFeeAssetNotCustomizable
        fee = Decimal(cs_fees[fee_asset])
        if fee < Decimal():
            raise WithdrawalFeeAssetNotCustomizable
        if fee == Decimal():
            ac_conf = get_asset_chain_config(asset, chain)
            # 手续费数目是0时，也允许自定义手续费数目是0
            if ac_conf.withdrawal_fee != Decimal():
                raise WithdrawalFeeAssetNotCustomizable
        return fee

    @classmethod
    def get_onchain_withdrawal_fee(cls, asset: str, fee_asset: str, chain: str):
        if asset == fee_asset:
            ac_conf = get_asset_chain_config(asset, chain)
            fee = ac_conf.withdrawal_fee
        else:
            fee = cls.get_custom_fee_amount(asset, chain, fee_asset)
        return fee

    @classmethod
    def validate_user_permission(cls, user: User):
        if not user.email:
            raise EmailNotBound

        user_settings = UserSettings(user.id)
        user_settings.check_withdrawals_disabled_after_editing()
        if not user_settings.withdrawals_enabled:
            raise WithdrawalForbidden

    @classmethod
    def validate_local_transfer_params(
            cls,
            asset: str,
            amount: Decimal,
    ):
        if not SiteSettings.local_transfers_enabled:
            raise LocalTransfersSuspended
        if not get_asset_config(asset).local_transfers_enabled:
            raise LocalTransfersSuspended

        if quantize_amount(amount, LOCAL_TRANSFER_MAX_PRECISION) != amount:
            raise WithdrawalPrecisionExceeded
        if amount <= 0:
            raise WithdrawalAmountTooSmall
        return True

    @classmethod
    def get_local_transfer_user(cls, user_id: int, address: str) -> User:
        recipient_user = User.from_account(address)
        if not isinstance(recipient_user, User):
            raise InvalidAccount
        if recipient_user.id == user_id:
            raise InvalidAccount
        return recipient_user

    @classmethod
    def validate_api_local_transfer_params(cls, user_id: int, asset: str, address: str,
                                           amount: Decimal):
        cls.validate_local_transfer_params(asset, amount)
        if ApiWithdrawalAddress.query \
                .filter(ApiWithdrawalAddress.user_id == user_id,
                        ApiWithdrawalAddress.type == ApiWithdrawalAddress.Type.LOCAL,
                        ApiWithdrawalAddress.address == address,
                        ApiWithdrawalAddress.status
                        == ApiWithdrawalAddress.Status.VALID) \
                .first() is None:
            raise InvalidWithdrawalAddress(message=_('此地址不在API提现白名单内，请前往添加'))
        return True

    @classmethod
    def send_confirmation_emails(cls, user: User, withdrawal_id: int, resend: bool = False):
        from app.api.common import get_request_host_url
        from app.business.security import update_security_statistics, SecuritySettingType

        approves = WithdrawalApprove.query.filter(
            WithdrawalApprove.withdrawal_id == withdrawal_id
        ).all()
        if approves:    # 多人审核
            # 此处应查询所有状态的approver，approver可能被已删除，但之前的提现依然需要他审核
            approvers = WithdrawalApprover.query.filter(WithdrawalApprover.user_id == user.id).all()
            approvers = {x.id: x for x in approvers}
            for approve in approves:
                if approve.status != WithdrawalApprove.Status.CREATED: # 只向未确认的重发
                    continue
                email_token = EmailToken.new(
                    user.id,
                    approvers[approve.approver_id].email,
                    EmailToken.EmailType.WITHDRAWAL,
                    dict(withdrawal_id=withdrawal_id, approve_id=approve.id),
                    ttl=3600
                )
                send_withdrawal_confirmation_email.delay(
                    approve.approver_id, withdrawal_id, email_token.token, get_request_host_url(), resend=resend
                )
            update_security_statistics([user.id], SecuritySettingType.WITHDRAWAL_APPROVER)
        else:
            email_token = EmailToken.new(
                user.id,
                user.email,
                EmailToken.EmailType.WITHDRAWAL,
                dict(withdrawal_id=withdrawal_id),
                ttl=1800
            )
            send_withdrawal_confirmation_email.delay(None, withdrawal_id, email_token.token, get_request_host_url())

    @classmethod
    def check_wallet_balance_and_node_statuses(cls, asset: str, chain: str, amount: Decimal) -> bool:
        if WalletNodeAbnormalStatusCache(chain).exists():
            return False
        if HotWalletBalanceCache(asset, chain).get_total() < amount:
            return False
        return True

    @classmethod
    def validate_onchain_withdrawal_params(
            cls,
            user: User,
            asset: str,
            chain: str,
            address: str,
            memo: str,
            amount: Decimal,
            extra: Optional[Dict]):
        user_id = user.id
        if not chain:
            raise InvalidArgument
        if not has_asset(asset, chain):
            raise AssetNotFound(asset, chain)

        if not SiteSettings.withdrawals_enabled:
            raise WalletUnderMaintenance
        withdrawal_privacy_asset_require_kyc(user, asset)

        ac_conf = get_asset_chain_config(asset, chain)
        if WithdrawalPrivilegedUser.has_user(user_id):
            w_enabled = ac_conf.withdrawals_all_enabled_to_privileged_users
        else:
            w_enabled = ac_conf.withdrawals_all_enabled
        if not w_enabled:
            raise WithdrawalsSuspended

        extra = AddressExtraConfigs.reslove_extra(chain, extra)
        try:
            valid = WalletClient().validate_withdrawal_address(chain, address, memo, extra, asset=asset)
        except:  # noqa
            raise InternalServerError
        if not valid:
            raise InvalidWithdrawalAddress(
                message=valid.reason if isinstance(valid, WhyNot) else None
            )
        if quantize_amount(amount, ac_conf.withdrawal_precision) != amount:
            raise WithdrawalPrecisionExceeded
        if amount <= 0 or amount < ac_conf.min_withdrawal_amount:
            raise WithdrawalAmountTooSmall
        return True

    @classmethod
    def validate_api_on_chain_withdrawal_params(
            cls,
            user: User,
            asset: str,
            chain: str,
            address: str,
            memo: str,
            amount: Decimal,
            extra: Optional[Dict]):
        user_id = user.id
        cls.validate_onchain_withdrawal_params(
            user, asset, chain, address, memo, amount, extra
        )
        cls.validate_onchain_withdrawal_address(user_id, asset, chain, address)
        return True

    @classmethod
    def validate_onchain_withdrawal_address(cls, user_id: int, asset: str, chain: str, address: str) -> bool:
        api_w_address_row = ApiWithdrawalAddress.query.filter(
            ApiWithdrawalAddress.user_id == user_id,
            ApiWithdrawalAddress.type == ApiWithdrawalAddress.Type.ON_CHAIN,
            or_(
                ApiWithdrawalAddress.asset == asset,
                ApiWithdrawalAddress.asset == ''
            ),
            ApiWithdrawalAddress.chain == chain,
            ApiWithdrawalAddress.address == address,
            ApiWithdrawalAddress.status == ApiWithdrawalAddress.Status.VALID,
        ).first()
        if api_w_address_row:
            return True
        if ApiWithdrawalAddressWhiteListUser.is_whitelist_user(user_id):
            return True
        raise InvalidWithdrawalAddress(message=_('此地址不在API提现白名单内，请前往添加'))

    @classmethod
    def validate_withdrawal_limit(cls,
                                  user: User,
                                  asset: str,
                                  amount: Decimal,
                                  fee: Decimal,
                                  fee_asset: str):

        withdrawable = get_user_withdrawable_amount(user.id, asset)
        if fee_asset == asset:
            if amount + fee > withdrawable['withdrawable_asset']:
                raise InsufficientBalance
        else:
            if amount > withdrawable['withdrawable_asset']:
                raise InsufficientBalance
            balances = ServerClient().get_user_balances(user.id, asset=fee_asset)
            fee_asset_avai = balances.get(fee_asset, {}).get('available', Decimal())
            if fee > fee_asset_avai:
                raise InvalidArgument(message=gettext('手续费币种%(fee_asset)s可用资产不足', fee_asset=fee_asset))
        check_withdrawal_amount_exceeded(user, asset, amount)

    @classmethod
    def do_withdrawal(
            cls,
            w_type: Withdrawal.Type,
            user: User,
            asset: str,
            chain: Optional[str],
            address: str,
            memo: str,
            extra: Optional[Dict[str, Any]],
            amount: Decimal,
            fee: Decimal,
            fee_asset: str,
            recipient_id: Optional[int],
            remark: str):

        with CacheLock(LockKeys.user_withdrawal(user.id), wait=False):
            db.session.rollback()
            cls.validate_withdrawal_limit(user, asset, amount, fee, fee_asset)
            withdrawal = Withdrawal(
                user_id=user.id,
                type=w_type,
                chain=chain,
                asset=asset,
                address=address,
                amount=amount,
                fee=fee,
                fee_asset=fee_asset,
                memo=memo,
                attachment=AddressExtraConfigs.dump_extra(extra),
                recipient_user_id=recipient_id,
                remark=remark
            )
            db.session.add(withdrawal)
            db.session.flush()

            approvers = WithdrawalApprover.get_approvers(user.id)
            if len(approvers) > 1:  # 多人审核，记录审核信息
                for approver in approvers:
                    db.session.add(WithdrawalApprove(
                        withdrawal_id=withdrawal.id,
                        approver_id=approver.id
                    ))
            db.session.commit()
            add_withdrawal_amount_to_cache(user.id, asset, amount)
            cls.send_confirmation_emails(user, withdrawal.id)
            return withdrawal

    @classmethod
    def do_api_withdrawal(
            cls,
            w_type: Withdrawal.Type,
            user: User,
            asset: str,
            chain: Optional[str],
            address: str,
            memo: str,
            extra: Optional[Dict[str, Any]],
            amount: Decimal,
            fee: Decimal,
            fee_asset: str,
            recipient_id: Optional[int],
            remark: str):
        from .risk_control.withdrawal import WithdrawalRiskCheck

        user_id = user.id
        with CacheLock(LockKeys.user_withdrawal(user_id)):
            db.session.rollback()
            cls.validate_withdrawal_limit(user, asset, amount, fee, fee_asset)
            withdrawal = db.session_add_and_commit(Withdrawal(
                user_id=user_id,
                type=w_type,
                chain=chain,
                asset=asset,
                address=address,
                amount=amount,
                fee=fee,
                fee_asset=fee_asset,
                memo=memo,
                recipient_user_id=recipient_id,
                attachment=json.dumps(extra) if extra else None,
                remark=remark,
            ))
            add_withdrawal_amount_to_cache(user_id, asset, amount)

            def add_user_balance(*args):
                exc = None
                for i in range(3):
                    try:
                        return server_client.add_user_balance(*args)
                    except server_client.BadResponse as e:
                        if e.code == server_client.ResponseCode.INSUFFICIENT_BALANCE:
                            raise InsufficientBalance
                        exc = ServiceUnavailable
                    except ServiceUnavailable as e:
                        exc = e
                    if i < 2:
                        time.sleep(1)
                raise exc

            server_client = ServerClient()
            try:
                if fee > 0:
                    add_user_balance(
                        user_id, fee_asset, -fee,
                        BalanceBusiness.WITHDRAWAL_FEE, withdrawal.id)
                add_user_balance(
                    user_id, asset, -amount,
                    BalanceBusiness.WITHDRAWAL, withdrawal.id)
            except Exception:
                withdrawal.status = Withdrawal.Status.CANCELLED
                cls.add_cancel_record(withdrawal.id, withdrawal.user_id)
                db.session.commit()
                add_withdrawal_amount_to_cache(user_id, asset, -amount, withdrawal.created_at)
                raise
            else:
                withdrawal.approved_by_user_at = now()
                withdrawal.status = Withdrawal.Status.AUDIT_REQUIRED
                db.session.add(WithdrawalFeeHistory(
                    withdrawal_id=withdrawal.id,
                    user_id=user_id,
                    asset=fee_asset,
                    amount=fee,
                    fee_type=WithdrawalFeeHistory.FeeType.FEE
                ))
                db.session.commit()
                UserBalanceSumCache(withdrawal.user_id).delete()
                # 请求风控检查
                WithdrawalRiskCheck.add_check_request(withdrawal)
                return withdrawal

    @classmethod
    def do_cancel_withdrawal(cls, user_id: int, withdraw_id: int,
                             cancel_type: WithdrawalCancel.CancelType = WithdrawalCancel.CancelType.SYSTEM,
                             cancel_user_id: int = WithdrawalCancel.SYSTEM_USER_ID):
        row = Withdrawal.query \
            .filter(Withdrawal.id == withdraw_id,
                    Withdrawal.user_id == user_id) \
            .first()
        if row is None or row.status not in (Withdrawal.Status.CREATED,
                                             Withdrawal.Status.AUDIT_REQUIRED,
                                             Withdrawal.Status.AUDITED):
            raise InvalidArgument(message=_('提现不能撤销，如有疑问请联系客服'))
        if row.status in (Withdrawal.Status.AUDIT_REQUIRED,
                          Withdrawal.Status.AUDITED) \
                and withdrawals_disabled_by_risk_control(user_id, True):
            raise InternalServerError
        cancel_withdrawal(withdraw_id, cancel_type=cancel_type, cancel_user_id=cancel_user_id)

    @classmethod
    def add_cancel_record(cls, withdrawal_id: int, user_id: int,
                          cancel_type: WithdrawalCancel.CancelType = WithdrawalCancel.CancelType.SYSTEM,
                          cancel_user_id: int = WithdrawalCancel.SYSTEM_USER_ID,
                          commit: bool = False):
        is_risk_control = RiskUser.test(user_id, RiskUser.Permission.BALANCE_OUT_DISABLED)
        row = WithdrawalCancel.get_or_create(withdrawal_id=withdrawal_id)
        row.cancel_type = cancel_type
        row.cancel_user_id = cancel_user_id
        row.is_risk_control = is_risk_control
        db.session.add(row)
        if commit:
            db.session.commit()


class P2pLocalTransfer(WithdrawalHelper):

    @classmethod
    def check_transfer_permission(cls, user_id, asset, check_security=True):
        if check_security:
            cls.check_user_security_edit_permission(user_id)
        cls.check_user_permission(user_id)
        cls.check_site_permission(asset)

    @classmethod
    def check_user_security_edit_permission(cls, user_id):
        user_settings = UserSettings(user_id)
        if user_settings.withdrawals_disabled_after_security_editing:
            raise P2pSellForbiddenAfterSecurityEditing
        elif user_settings.withdrawals_disabled_after_withdraw_password_editing:
            raise P2pSellForbiddenAfterWithdrawPasswordEditing

    @classmethod
    def check_user_permission(cls, user_id):
        user_settings = UserSettings(user_id)
        if not user_settings.withdrawals_sendable:
            raise P2pExceptionMap[P2pExceptionCode.PERMISSION_INVALID]

    @classmethod
    def check_site_permission(cls, asset):
        if not SiteSettings.local_transfers_enabled:
            raise P2pExceptionMap[P2pExceptionCode.PERMISSION_INVALID]
        if not get_asset_config(asset).local_transfers_enabled:
            raise P2pExceptionMap[P2pExceptionCode.PERMISSION_INVALID]

    @classmethod
    def check_lock(cls, user, asset, amount):
        cls.check_transfer_permission(user.id, asset)
        cls.validate_withdrawal_limit(user, asset, amount, Decimal(), asset)

    @classmethod
    def add_withdrawal_amount_cache(cls, user_id, asset, amount):
        add_withdrawal_amount_to_cache(user_id, asset, amount)

    @classmethod
    def check_before_finish(cls, user, asset):
        cls.check_transfer_permission(user.id, asset, check_security=False)
        if withdrawals_disabled_by_risk_control(user.id):
            raise P2pExceptionMap[P2pExceptionCode.RISK_CHECK]

    @classmethod
    def cancel_withdrawal(cls, user_id, asset, amount, create_at):
        if amount < 0:
            raise ValueError("amount must gt 0")
        add_withdrawal_amount_to_cache(user_id, asset, -amount, create_at)


def get_custom_withdrawal_fee_by_chain(withdrawal_asset: str, chain: str) -> Dict[str, Decimal]:
    """ 获取某个链下的自定义提现手续费币种的数目，{fee_asset, fee_amount} """
    from app.assets import get_asset_chain_config

    ac_conf = get_asset_chain_config(withdrawal_asset, chain)

    price_cache = CustomWithdrawalAssetPriceCache()
    all_assets = {*CUSTOM_WITHDRAWAL_FEE_ASSETS, withdrawal_asset}
    asset_prices = price_cache.hmget_with_keys(list(all_assets))
    asset_prices = {i[0]: Decimal(i[1]) for i in asset_prices}

    withdrawal_asset_price = asset_prices.get(withdrawal_asset, Decimal(0))
    withdrawal_fee_usd = ac_conf.withdrawal_fee * withdrawal_asset_price

    result = {}
    for c_asset in CUSTOM_WITHDRAWAL_FEE_ASSETS:
        if c_asset == withdrawal_asset:
            c_fee_amount = ac_conf.withdrawal_fee
        else:
            c_price = asset_prices.get(c_asset)
            if not c_price:
                continue
            c_fee_amount = quantize_amount(withdrawal_fee_usd / c_price, 8)
            c_fee_amount = quantize_amount_non_zero(c_fee_amount, 2)
        result[c_asset] = c_fee_amount
    return result


def get_custom_withdrawal_fee_by_fee_asset(withdrawal_asset: str, fee_asset: str) -> Dict[str, Decimal]:
    """ 获取某个自定义提现手续费币种在所有链下 需要的数目，{chain1, fee_amount} """
    from app.assets import asset_to_chains, get_asset_chain_config

    price_cache = CustomWithdrawalAssetPriceCache()
    all_assets = {withdrawal_asset, fee_asset}
    asset_prices = price_cache.hmget_with_keys(list(all_assets))
    asset_prices = {i[0]: Decimal(i[1]) for i in asset_prices}

    result = {}
    for chain_ in asset_to_chains(withdrawal_asset):
        ac_conf = get_asset_chain_config(withdrawal_asset, chain_)
        if not ac_conf.is_visible:
            continue
        withdrawal_asset_price = asset_prices.get(withdrawal_asset, Decimal(0))
        withdrawal_fee_usd = ac_conf.withdrawal_fee * withdrawal_asset_price
        if withdrawal_asset == fee_asset:
            c_fee_amount = ac_conf.withdrawal_fee
        else:
            c_price = asset_prices.get(fee_asset)
            if not c_price:
                continue
            c_fee_amount = quantize_amount(withdrawal_fee_usd / c_price, 8)
            c_fee_amount = quantize_amount_non_zero(c_fee_amount, 2)
        result[chain_] = c_fee_amount
    return result


def deposit_privacy_asset_require_kyc(user: User, asset: str):
    if user.kyc_status != User.KYCStatus.PASSED and get_asset_config(asset).is_privacy:
        raise DepositPrivacyAssetRequireKyc


def withdrawal_privacy_asset_require_kyc(user: User, asset: str):
    """ 隐私币的一些操作 要求 用户完成KYC """
    if user.kyc_status != User.KYCStatus.PASSED and get_asset_config(asset).is_privacy:
        raise WithdrawalPrivacyAssetRequireKyc


class WithdrawalRestrictedFundHelper:
    model = WithdrawalRestrictedFund
    change_log_model = WithdrawalRestrictedFundChangeLog

    @classmethod
    def get_user_fund_usd(cls, user_id: int) -> Decimal:
        now_ = now()
        amount_usd = WithdrawalRestrictedFund.query.filter(
            WithdrawalRestrictedFund.user_id == user_id,
            WithdrawalRestrictedFund.status == WithdrawalRestrictedFund.StatusType.NORMAL,
            WithdrawalRestrictedFund.expired_at > now_
        ).with_entities(
            func.sum(WithdrawalRestrictedFund.amount_usd).label("amount_usd"),
        ).scalar() or Decimal()
        return amount_usd

    @classmethod
    def create_fund(cls,
                    biz_type: WithdrawalRestrictedFund.BizType,
                    biz_id: int,
                    user_id: int,
                    asset: str,
                    amount: Decimal,
                    amount_usd: Decimal,
                    expired_at: datetime,
                    log_new_data: dict,
                    change_user_id: int = WithdrawalRestrictedFundChangeLog.SYSTEM_USER_ID):
        fund = cls.model(
            biz_type=biz_type,
            biz_id=biz_id,
            user_id=user_id,
            asset=asset,
            amount=amount,
            amount_usd=amount_usd,
            expired_at=expired_at
        )
        db.session.add(fund)
        db.session.flush()

        new_data = {
            **log_new_data,
            'expired_at': datetime_to_time(expired_at),
        }
        log = cls.change_log_model(
            record_id=fund.id,
            old_data={},
            new_data=new_data,
            change_user_id=change_user_id
        )
        db.session.add(log)

    @classmethod
    def update_user_funds(
            cls,
            user_id: int,
            time_delta: timedelta,
            log_old_data: dict,
            log_new_data: dict,
            biz_type: WithdrawalRestrictedFund.BizType = None,
            biz_id: int = None,
            change_user_id: int = WithdrawalRestrictedFundChangeLog.SYSTEM_USER_ID):
        now_ = now()
        change_logs = []
        query = WithdrawalRestrictedFund.query.filter(
            WithdrawalRestrictedFund.user_id == user_id,
            WithdrawalRestrictedFund.status == WithdrawalRestrictedFund.StatusType.NORMAL,
            WithdrawalRestrictedFund.expired_at > now_
        )
        if biz_type:
            query = query.filter(
                WithdrawalRestrictedFund.biz_type == biz_type
            )
        if biz_id:
            query = query.filter(
                WithdrawalRestrictedFund.biz_id == biz_id
            )
        restricted_funds = query.all()
        for row in restricted_funds:
            row: WithdrawalRestrictedFund
            old_expired_at = row.expired_at
            row.expired_at += time_delta
            change_logs.append(cls.change_log_model(
                record_id=row.id,
                old_data={
                    **log_old_data,
                    'expired_at': datetime_to_time(old_expired_at)
                },
                new_data={
                    **log_new_data,
                    'expired_at': datetime_to_time(row.expired_at)
                },
                change_user_id=change_user_id
            ))
        db.session.add_all(change_logs)
