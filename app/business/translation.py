import html
import requests
from bs4 import BeautifulSoup, element
import time
from functools import partial

from flask import current_app
from app import config, Language


def translate_v2(source: Language, target: Language, html: str, with_html_tag=False):
    if source is target:
        return html
    ret = translate_by_line_with_html(
        source.value, target.value, html
    )
    if not with_html_tag:
        ret = BeautifulSoup(ret, 'lxml').text
    return ret


def translate_by_line_with_html(source, target, html: str):
    """以博客编辑器为基础解析标签内容"""
    soup = BeautifulSoup(html, "lxml")
    tags = soup.body.div  # 所有 div 标签下子节点
    if not tags:  # 此种情况是非富文本内容
        tags = soup.findAll('p')

    translate_func = partial(do_translate, source, target)
    r = ''
    for tag in tags:
        r += translate_func(str(tag))
    rhtml = BeautifulSoup(r, 'lxml')
    ret = "".join(str(item) for item in rhtml.body)
    return f'<div class="ql-editor">{ret}</div>'


def translate(source: Language, target: Language, html: str, with_html_tag=False):
    if source is target:
        return html
    ret = translate_by_line(
        source.value, target.value, html
    )
    ret = add_r2l_style(rich_content=ret, lang=target)
    ret = after_translate(ret)
    # ret = f'<div class="ql-editor"><p>{ret}</p></div>'
    if not with_html_tag:
        ret = BeautifulSoup(ret, 'lxml').text
    return ret


def add_r2l_style(rich_content, lang):
    if lang in [
        Language.AR_AE,
        Language.FA_IR,
    ]:
        soup = BeautifulSoup(rich_content, "lxml")
        # tag_names = ['h1', 'h2', 'h3', 'p', '']
        for tag in soup.findAll('p'):
            if tag.get("style") is None:
                tag["style"] = "direction: rtl; text-align: right;"
        return "".join(str(item) for item in soup.body)
    return rich_content


def translate_by_line(source, target, html: str):
    """以博客编辑器为基础解析标签内容"""
    soup = BeautifulSoup(html, "lxml")
    tags = soup.body.div  # 所有 div 标签下子节点
    if not tags:  # 此种情况是非富文本内容
        tags = soup.findAll('p')

    translate_func = partial(do_translate, source, target)
    for tag in tags:
        if tag.text is not None:
            children_str = build_tag_by_translate(tag, translate_func)
            if attr_str := _parse_tag_attrs(tag):
                phtml = f'<{tag.name} {attr_str}>{children_str}</{tag.name}>'
            else:
                phtml = f'<{tag.name}>{children_str}</{tag.name}>'
            phtml = BeautifulSoup(phtml, 'lxml')
            phtml = phtml.find(tag.name)
            tag.replace_with(phtml)
    return "".join(str(item) for item in soup.body)


def build_tag_by_translate(ori_tag, translate_func):
    r = ''
    for tag in ori_tag.contents:
        if not isinstance(tag, element.Tag):
            # element.NavigableString
            if text := tag.get_text():
                text = translate_func(text)
            else:
                text = ''
            r += text
            continue
        if tag.isSelfClosing:
            r += str(tag)
        elif attr_str := _parse_tag_attrs(tag):
            r += f'<{tag.name} {attr_str}>{build_tag_by_translate(tag, translate_func)}</{tag.name}>'
        else:
            r += f'<{tag.name}>{build_tag_by_translate(tag, translate_func)}</{tag.name}>'
    return r


def _parse_tag_attrs(tag: element.Tag) -> str:
    ret = ''
    if not isinstance(tag, element.Tag):
        return ret
    attr_fields = [
        'class',
        'style',
        'href',
        'rel',
        'target',
    ]
    for attr_field in attr_fields:
        if attr_field in tag.attrs:
            attrs = tag.attrs[attr_field]
            if attr_field == 'rel':
                attrs = ' '.join(attrs)
            ret += f' {attr_field}="{attrs}" '
    return ret


def do_translate(source, target, q):
    if source == target:
        return q

    # https://cloud.google.com/translate/docs/languages?hl=zh-cn
    lang_dict = {
        Language.EN_US.value: "en",
        Language.ZH_HANS_CN.value: "zh-CN",
        Language.ZH_HANT_HK.value: "zh-TW",
        Language.JA_JP.value: "ja",
        Language.RU_KZ.value: "ru",
        Language.KO_KP.value: "ko",
        Language.ID_ID.value: "id",
        Language.ES_ES.value: "es",
        Language.FA_IR.value: "fa",
        Language.TR_TR.value: "tr",
        Language.VI_VN.value: "vi",
        Language.AR_AE.value: "ar",
        Language.FR_FR.value: "fr",
        Language.PT_PT.value: "pt",
        Language.DE_DE.value: "de",
        Language.TH_TH.value: "th",
    }

    api_key = config['GOOGLE_TRANSLATION_CONFIG']['api_key']
    # https://cloud.google.com/translate/docs/reference/rest/v2/translate
    resp = requests.post(
        f"https://translation.googleapis.com/language/translate/v2?key={api_key}&target={lang_dict[target]}&source={lang_dict[source]}&q={q}",
    )

    time.sleep(0.01)
    try:
        if (data := resp.json().get("data")) is not None:
            if "translations" in data and len(data["translations"]) >= 1:
                return data["translations"][0]["translatedText"]
    except Exception as e:
        current_app.logger.info(f"google translation err: {e}")
        current_app.logger.info("err : ", resp.content.decode())


def after_translate(desc: str):
    # 有些翻译后的简介，会有如 &quot; (") &#39; (') 之类的内容，需处理
    html.unescape(desc)  # https://stackoverflow.com/questions/2087370/decode-html-entities-in-python-string
    desc = desc.replace("&#39;", "'")  # 单个单引号无法通过html库处理，一些语言中会有单个单引号，如法语的冠词写法
    desc = desc.replace("&quot;", '"')  # 有些情况html库转换有问题，兜底处理一下
    return desc


class TranError(Exception):
    pass
