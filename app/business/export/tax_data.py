import csv
import io
import json
import zipfile
from functools import partial
from typing import List, Dict
from uuid import uuid4

from app import config
from app.business import TradeHistoryDB, PerpetualHistoryDB, UserPreferences, \
    send_notice_email, lock_call
from app.common import CeleryQueues, MessageTitle, MessageContent, \
    MessageWebLink, BalanceBusiness
from app.exceptions import InvalidArgument
from app.models import db, TaxExportHistory, User, Message
from app.utils import celery_task, ExternalTable, timestamp_to_datetime, upload_file, now, \
    url_join, datetime_to_str, quantize_amount


class TaxDataExportHandler:
    account_query_dic = {
        'spot': {'account_filter_sql': ' AND account = 0 ', 'future_filter_sql': ''},
        'margin': {'account_filter_sql': ' AND account > 0 AND account < 20000 ', 'future_filter_sql': ''},
        'investment': {'account_filter_sql': ' AND account = 20000 ', 'future_filter_sql': ''},
        'future': {'account_filter_sql': '', 'future_filter_sql': ' AND business not in ("funding", "trade", "fee", "settle") '},
    }
    hide_transfer_filter_sql = ' AND business not in ("margin_transfer", "investment_transfer",' \
                               ' "contract_transfer_in", "contract_transfer_out", "contract_transfer") '

    export_headers = ['Time', 'Account', 'Operation', 'Coin', 'Asset change', 'Balance']
    account_to_csv_dic = {
        'spot': 'Spot Market Account',
        'margin': 'Margin Account',
        'investment': 'Financing Account',
        'future': 'Future Account'
    }

    @classmethod
    def run(cls, id_, notice_user=True):
        record = cls.get_export_record(id_)
        query_account = record['account']
        user_id = record['user_id']
        accounts = [query_account.value] if query_account != TaxExportHistory.Account.ALL else list(cls.account_query_dic.keys())

        accounts_data = []
        for account in accounts:
            data = cls.get_account_balance_history(
                account, record['user_id'], record['asset'], record['start_time'],
                record['end_time'],
                record['hide_transfer'])
            accounts_data.append((account, data))
        file_url = cls.upload(user_id, accounts_data)
        saved = cls._save(id_, file_url)
        if saved and notice_user:
            cls.send_email(user_id)
            cls.send_notice(user_id)

    @classmethod
    def get_export_record(cls, id_) -> Dict:
        rec = TaxExportHistory.query.get(id_)
        if not rec or rec.status != TaxExportHistory.Status.PENDING:
            raise InvalidArgument(message='税务导出申请记录不存在或已完成')
        return rec.to_dict()

    @classmethod
    def get_account_balance_history(cls, account, user_id, asset, start_time, end_time,
                                    hide_transfer: bool = False) -> List:
        account_filter_sql = cls.account_query_dic[account]['account_filter_sql']
        asset_filter_sql = ' AND asset = "{}" '.format(asset) if asset != 'ALL' else ''
        hide_transfer_filter_sql = cls.hide_transfer_filter_sql if hide_transfer else ''
        where = f'user_id={user_id} ' \
                f'AND time>={start_time} ' \
                f'AND time<={end_time} ' \
                f'{account_filter_sql} {asset_filter_sql} {hide_transfer_filter_sql} '
        order_by = ' time DESC, id DESC '
        future_filter_sql = cls.account_query_dic[account]['future_filter_sql']
        if future_filter_sql:
            balance_table = cls.get_perpetual_balance_table(user_id)
            where += future_filter_sql
        else:
            balance_table = cls.get_spot_balance_table(user_id)

        latest_rec_id = cls._get_latest_rec_id(balance_table, where, order_by)
        if not latest_rec_id:
            return []
        res = cls._get_account_balance_history(balance_table, where, order_by, latest_rec_id)
        return cls._fmt(user_id, account, res)

    @classmethod
    def get_perpetual_balance_table(cls, user_id) -> ExternalTable:
        _db, _table_name = PerpetualHistoryDB.user_to_db_and_table(user_id, "balance_history")
        balance_table = _db.table(_table_name)
        return balance_table

    @classmethod
    def get_spot_balance_table(cls, user_id) -> ExternalTable:
        _db, _table_name = TradeHistoryDB.user_to_db_and_table(user_id, "balance_history")
        balance_table = _db.table(_table_name)
        return balance_table

    @classmethod
    def _get_latest_rec_id(cls, balance_table, where, order_by) -> int:
        result = balance_table.select(
            'id',
            where=where,
            order_by=order_by,
            limit=1
        )
        return result[0][0] if result else 0

    @classmethod
    def _get_account_balance_history(cls, balance_table, where, order_by, latest_rec_id) -> List:
        res = []
        iter_size = 5000
        columns = ['id', 'time', 'business', 'asset', 'change', 'balance', 'detail']
        start_id = latest_rec_id + 500
        while True:
            record_id_filter_sql = f' AND id < {start_id} '
            where_sql = where + record_id_filter_sql
            limit_ = TaxExportHistory.MAX_EXPORT_COUNT - len(res)
            limit = min(limit_, iter_size)
            ret = balance_table.select(
                *columns,
                where=where_sql,
                order_by=order_by,
                limit=limit
            )
            res.extend(ret)
            if not ret or len(res) >= TaxExportHistory.MAX_EXPORT_COUNT:
                break
            start_id = min([i[0] for i in ret])
        return res

    @classmethod
    def _fmt(cls, user_id: int, account: str, data_list: List) -> List:
        pref = UserPreferences(user_id)
        dt_to_str = partial(datetime_to_str,
                            offset_minutes=pref.timezone_offset)
        quantize = partial(quantize_amount, decimals=8)
        res = []
        for item in data_list:
            time_str = dt_to_str(timestamp_to_datetime(item[1]))
            account_csv = cls.account_to_csv_dic[account]
            business, asset = item[2], item[3]
            change, balance = item[4], item[5]
            detail = json.loads(item[-1]) if item[-1] else {}
            if 'f' in detail:
                business_str = BalanceBusiness.TRADING_FEE.value
            else:
                business_str = business
            change_str = f'{quantize(change):+.8f}'
            balance_str = f'{quantize(balance):.8f}'
            res.append([time_str, account_csv, business_str, asset, change_str, balance_str])
        return res

    @classmethod
    def upload(cls, user_id, accounts_data):
        now_ = now().strftime('%Y-%m-%d')
        zip_buf = io.BytesIO()
        with zipfile.ZipFile(zip_buf, 'w', compression=zipfile.ZIP_DEFLATED) as zip_file:
            for account, data in accounts_data:
                file_name = '{}_{}_{}_{}.csv'.format(account, now_, user_id,
                                                     str(uuid4())[:8])
                csv_buf = io.StringIO()
                csv_handler = csv.writer(csv_buf)
                csv_handler.writerow(cls.export_headers)
                csv_handler.writerows(data)
                content = csv_buf.getvalue()
                zip_file.writestr(file_name, content)
        zip_data = zip_buf.getvalue()  # 获取压缩后的数据流要在with语句之外，否则压缩文件解压时会报不可预料的压缩文件末端
        file_url = upload_file(zip_data, 'zip', ttl=TaxExportHistory.EXPIRE_DAYS*86400)
        return file_url

    @classmethod
    def _save(cls, id_, file_url):
        now_ = now()
        rec = TaxExportHistory.query.get(id_)
        if rec.status == TaxExportHistory.Status.PENDING:
            rec.file_url = file_url
            rec.status = TaxExportHistory.Status.FINISHED
            rec.finished_at = now_
            db.session.commit()
            return True
        else:
            return False

    @classmethod
    def send_email(cls, user_id):
        user = User.query.get(user_id)
        email = user.main_user_email
        pref = UserPreferences(user_id)
        site_url = url_join(config['SITE_URL'], '/asset/history/spot?export=1')
        send_notice_email(
            email=email,
            email_type='tax_export_success',
            template_args=dict(
                name=user.name_displayed,
                site_url=site_url
            ),
            lang=pref.language.value
        )

    @classmethod
    def send_notice(cls, user_id):
        """发送站内信"""
        db.session_add_and_commit(
            Message(
                user_id=user_id,
                title=MessageTitle.TAX_EXPORT_SUCCESS.name,
                content=MessageContent.TAX_EXPORT_SUCCESS.name,
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.TAX_EXPORT_PAGE.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.TEXT,
                channel=Message.Channel.SYSTEM,
            )
        )


@celery_task(queue=CeleryQueues.STATISTIC)
@lock_call(with_args=True, ttl=3600)
def export_tax_data(id_, notice_user=True):
    """报税数据导出"""
    TaxDataExportHandler.run(id_, notice_user)
