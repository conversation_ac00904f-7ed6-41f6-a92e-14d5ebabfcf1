import json
from decimal import Decimal

from app import Language
from app.business import PerpetualHistoryDB, PerpetualSysHistoryDB, PriceManager
from app.business.email import send_internal_user_email
from app.caches import PerpetualMarketCache, PerpetualOfflineMarketCache
from app.common import CeleryQueues, PerpetualMarketType, InsuranceType
from app.models import PerpetualMarket
from app.utils import celery_task, timestamp_to_datetime, upload_file, datetime_to_str
from app.utils.export import ExcelExporter


def get_liquidation_records(query_data):
    market = query_data['market']
    side = query_data['side']
    user_id = query_data['id']
    start_time = int(query_data['start_time'] / 1000)
    end_time = int(query_data['end_time'] / 1000)
    insurance_change = query_data.get('insurance_change')
    page = query_data['page']
    limit = query_data['limit']
    export = query_data['export']
    offset = (page - 1) * limit
    total, records = PerpetualSysHistoryDB.get_liquidation_history(
        start_time, end_time, offset, limit, user_id, market, side, insurance_change, export
    )
    market_list = {i['market'] for i in records}
    market_query = PerpetualMarket.query.filter(
        PerpetualMarket.name.in_(market_list)).with_entities(
        PerpetualMarket.name,
        PerpetualMarket.market_type,
        PerpetualMarket.status,
    ).all()
    market_type_map = {i.name: i.market_type.value for i in market_query}
    online_market_list = {i.name for i in market_query if i.status == PerpetualMarket.StatusType.OPEN}
    for item in records:
        all_deal_stock, all_insurance = PerpetualHistoryDB.sum_deal_history(
            item['user_id'], item['position_id'])
        all_deal_stock = all_deal_stock or 0
        all_insurance = all_insurance or 0
        item['average_deal_price'] = Decimal()
        if all_deal_stock > Decimal():
            if market_type_map[item['market']] == PerpetualMarketType.DIRECT.value:
                item['average_deal_price'] = all_deal_stock / item['liq_amount']
            else:
                item['average_deal_price'] = item['liq_amount'] / all_deal_stock
        item['insurance_change'] = all_insurance

        asset = PerpetualMarketCache.get_balance_asset(item['market']) \
            if item['market'] in online_market_list else PerpetualOfflineMarketCache.get_balance_asset(
            item['market'])
        if market:
            asset_rate = 1
        else:
            asset_rate = Decimal('1') if asset == 'USD' else PriceManager.asset_to_usd(asset)

        insurance = all_insurance * asset_rate
        item['insurance_change_value'] = insurance

    return records, total


def get_insurance_summary_data(query_data):
    market = query_data['market']
    user_id = query_data['id']
    start_time = int(query_data['start_time'] / 1000)
    end_time = int(query_data['end_time'] / 1000)
    insurance_change = query_data.get('insurance_change')
    total_amount, total_income_amount, total_expense_amount = Decimal(), Decimal(), Decimal()

    insurance_records = PerpetualSysHistoryDB.get_insurance_records(start_time, end_time, user_id,
                                                                    market, insurance_change)
    for asset, type_, change in insurance_records:
        if market:
            asset_rate = 1
        else:
            asset_rate = PriceManager.asset_to_usd(asset)
        amount = change * asset_rate
        if type_ == InsuranceType.ADD.value:
            total_income_amount += amount
            total_amount += amount
        else:
            total_expense_amount += amount
            total_amount -= amount
    return total_amount, total_income_amount, -total_expense_amount


@celery_task(queue=CeleryQueues.DAILY)
def export_liquidation_records(query_data):
    """admin合约爆仓记录导出"""

    query_data = json.loads(query_data)
    if query_data.get('insurance_change'):
        query_data['insurance_change'] = PerpetualSysHistoryDB.InsuranceChange(query_data['insurance_change'])
    record, _ = get_liquidation_records(query_data)

    header_data = [
        ('id', 'ID'),
        ('liq_time_str', '爆仓时间'),
        ('user_id', '持仓用户'),
        ('market', '合约'),
        ('side_str', '类型'),
        ('liq_amount', '仓位数量'),
        ('open_price', '开仓均价'),
        ('liq_price', '强平价格'),
        ('bkr_price', '破产价'),
        ('average_deal_price', '实际成交均价'),
        ('profit_real', '用户亏损'),
        ('insurance_change', '保险基金变化'),
    ]

    for row in record:
        row['liq_time_str'] = datetime_to_str(timestamp_to_datetime(row['liq_time']), 480)
        row['side_str'] = '买入平空' if row['side'] == 1 else '卖出平多'

    data_streams = ExcelExporter(
        data_list=record,
        headers=[r[1] for r in header_data],
        fields=[r[0] for r in header_data],
    ).export_streams()
    file_url = upload_file(data_streams, 'xlsx')
    email_content = f'''合约爆仓记录数据 excel 下载链接：<a href={file_url}>{file_url}</a>'''
    send_internal_user_email(
        email=query_data['email'],
        email_content=email_content,
        subject=f'合约爆仓记录数据',
        lang=Language.ZH_HANS_CN.value
    )
