import time
from collections import defaultdict
from datetime import date
from decimal import Decimal
from functools import wraps
from typing import Iterable
from typing import Union, Dict, Any, Tu<PERSON>, List

from openpyxl import load_workbook
from sqlalchemy import func, inspect

from app.business import PerpetualHistoryDB, PerpetualMarketCache, MarketCache, UserPreferences, PriceManager, now
from app.business.external_dbs import ExternalDB, TradeHistoryDB, ExchangeLogDB, TradeLogDB, TradeSummaryDB
from app.caches import PerpetualOfflineMarketCache
from app.common import get_country, Language, LANGUAGE_NAMES, PerpetualMarketType
from app.models import User, SubAccount, LoginHistory, UserPreference, ReferralHistory, \
    UserTradeSummary, \
    PerpetualBalanceTransfer, AssetPrice, Deposit, FiatOrder, UserTradeFeeSummary, db, \
    UserBusinessRecord, BitMap
from app.utils import datetime_to_time, batch_iter
from app.utils.export import export_xlsx_with_sheet


def add_print(func):
    @wraps(func)
    def dec(*args, **kwargs):
        print(f'[begin timestamp: {time.time()}] {func.__name__}>>>')
        ret = func(*args, **kwargs)
        print(f'[end timestamp: {time.time()}] {func.__name__}<<<')
        return ret

    return dec


class ExportHelper:

    @classmethod
    def get_country_cn_name(cls, country_code) -> str:
        country = get_country(country_code)
        return country.cn_name if country else None

    @classmethod
    def get_language_cn_name(cls, lang: Union[str, Language]) -> str:
        if isinstance(lang, str):
            lang = Language[lang]
        return LANGUAGE_NAMES[lang].chinese

    @classmethod
    def get_perpetual_market_mapper(cls) -> Dict[str, Dict[str, Any]]:
        markets_data = PerpetualMarketCache().read_aside()
        offline_markets_data = PerpetualOfflineMarketCache().read_aside()
        markets_data.update(offline_markets_data)
        return markets_data

    @classmethod
    def get_sport_market_mapper(cls) -> Dict[str, Dict[str, Any]]:
        return {
            market: MarketCache(market).dict
            for market in MarketCache.list_online_markets() + MarketCache.list_offline_markets()
        }

    @classmethod
    def read_excel_by_sheets(cls, file, head_list, skip_rows=1) -> Dict[str, List[Dict[str, str]]]:
        """读取多个sheet 的excel数据"""
        wb = load_workbook(file, read_only=True)
        data = {}
        for sheet_name in wb.sheetnames:
            rows = []
            for row in wb[sheet_name].values:
                if not any(row):
                    continue
                rows.append(row)
            data[sheet_name] = [dict(zip(head_list, r)) for r in rows[skip_rows:]]
        return data

    @classmethod
    def write_excel_by_sheets(cls, sheet_data: Dict[str, Dict[str, Any]]) -> str:
        """
        将组合好的数据写入多个 sheet 页面
        sheet_data = {
            "sheet_name": {
                "header_mapper": {"id": "ID", "name": "名称", "email": "邮箱"},
                "data": [
                    {
                        "id": 1,
                        "name": name,
                        "email": email
                    }
                ]
            }
        }
        """
        return export_xlsx_with_sheet(sheet_data)


class PerpetualTradeExport:

    @classmethod
    def group_tables(cls, user_ids, table) -> Dict[Tuple[ExternalDB, str], List[int]]:
        tables = defaultdict(list)
        for user_id in user_ids:
            _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id, table)
            tables[(_db, _table)].append(user_id)
        return tables

    @classmethod
    def user_trade_deal_records(cls, user_ids: Iterable[int], start_time, end_time, columns=None):
        """获取用户合约成交记录"""
        start_at_time = datetime_to_time(start_time)
        end_at_time = datetime_to_time(end_time)
        result = defaultdict(list)
        tables = cls.group_tables(user_ids, 'deal_history')
        default_columns = [
            'time',
            'market',
            'deal_type',
            'user_id',
            'side',
            'role',
            'position_type',
            'price',
            'open_price',
            'amount',
            'position_amount',
            'margin_amount',
            'leverage',
            'deal_stock',
            'deal_fee',
            'deal_profit',
            'deal_insurance',
            'fee_asset',
            'fee_discount',
            'fee_price',
        ]
        columns = columns or default_columns
        for key, ids in tables.items():
            _db, _table = key
            user_id_str = ','.join(map(str, ids))
            where = f'time >= {start_at_time} and ' \
                    f'time < {end_at_time} and ' \
                    f'user_id in ({user_id_str})'
            records = _db.table(_table).select(
                *columns,
                where=where,
            )
            for item in records:
                data = dict(zip(columns, item))
                result[data['user_id']].append(data)

        return result

    @classmethod
    def get_user_first_trade_data(cls, user_ids: Iterable[int], columns=None) -> Dict[str, Dict[str, Any]]:
        """获取用户首次合约成交记录"""
        result = {}
        markets = ExportHelper.get_perpetual_market_mapper()
        tables = cls.group_tables(user_ids, 'deal_history')
        default_columns = [
            'time',
            'market',
            'user_id',
            'price',
            'amount',
            'deal_stock',
        ]
        columns = columns or default_columns
        fields = ', '.join([f'`{field}`' for field in columns])
        for key, ids in tables.items():
            _db, _table = key
            cursor = _db.cursor()
            user_id_str = ','.join(map(str, ids))
            cursor.execute(
                f"""select {fields} from {_table} a where not exists (
                    select * from {_table} b where a.user_id=b.user_id and a.id>b.id
                ) and user_id in ({user_id_str});"""
            )
            for item in cursor.fetchall():
                data = dict(zip(columns, item))
                market_dict = markets[data['market']]
                if market_dict['type'] == PerpetualMarketType.INVERSE:
                    data['deal_volume'] = data['price'] * data['deal_stock']
                else:
                    data["deal_volume"] = data['deal_stock']
                result[data['user_id']] = data

        return result

    @classmethod
    def get_user_first_transfer_data(cls, user_ids: Iterable[int], date_price_mapper=None):
        """获取用户首次划转记录"""
        if not date_price_mapper:
            date_price_mapper = {}
        table_name = "perpetual_balance_transfer"
        user_id_str = ",".join(map(str, user_ids))
        records = db.session.execute(
            f"""select * from {table_name} a where not exists
            (select * from {table_name} b where a.user_id=b.user_id
            and a.id<b.id) and user_id in ({user_id_str}) and status='{PerpetualBalanceTransfer.Status.FINISHED.name}'
            and transfer_type='{PerpetualBalanceTransfer.TransferType.TRANSFER_IN.name}';"""
        ).fetchall()
        fields = inspect(PerpetualBalanceTransfer).c.keys()
        result = {}
        for record in records:
            item = dict(zip(fields, record))
            created_at = item['created_at']
            created_date = created_at.date()
            if created_date not in date_price_mapper.keys():
                date_price_mapper[created_date] = AssetPrice.get_close_price_map(created_date)
            price = date_price_mapper[created_date].get(item["coin_type"], Decimal())
            item["volume"] = price * item["amount"]
            result[item["user_id"]] = item
        return result


class SpotTradeExport:

    @classmethod
    def get_user_first_trade_data(cls, user_ids: List[int]):
        """获取现货首次成交记录"""
        tables = TradeHistoryDB.users_to_dbs_and_tables(user_ids, 'user_deal_history')
        columns = [
            'time',
            'market',
            'user_id',
            'price',
            'amount',
            'deal',
        ]
        fields = ', '.join([f'`{field}`' for field in columns])
        result = {}
        for db, table_user_mapper in tables:    # noqa: F402
            cursor = db.cursor()
            for table, ids in table_user_mapper.items():
                user_id_str = ','.join(map(str, ids))
                cursor.execute(
                    f"""select {fields} from {table} a where not exists (
                        select * from {table} b where a.user_id=b.user_id and a.id>b.id
                    ) and user_id in ({user_id_str});"""
                )
                for item in cursor.fetchall():
                    data = dict(zip(columns, item))
                    result[data['user_id']] = data
        return result

    @classmethod
    def get_user_cet_position(cls, export_time, user_ids=None):
        """获取用户CET 持仓量"""
        ts = datetime_to_time(export_time)
        slice_table = TradeLogDB.slice_balance_table(ts)
        group_by_field = "user_id"
        where = "`asset` = 'CET'"
        if user_ids:
            where += f" AND user_id in ({','.join(map(str, user_ids))})"
        result = slice_table.select("user_id", "SUM(balance) as balance", where=where, group_by=group_by_field)
        data = defaultdict(Decimal)
        for user_id, balance in result:
            data[user_id] = balance
        return {i: b for i, b in result} if result else {}

    @classmethod
    def get_user_asset_trade_summary(
            cls,
            start_date: date,
            end_date: date,
            sub_user_map: Dict[int, int]
    ) -> Dict[int, Dict[str, Decimal]]:
        """获取用户某段时间内 币种的交易额"""
        all_user_ids = set(list(sub_user_map.keys()) + list(sub_user_map.values()))
        prices = PriceManager.assets_to_usd()
        cursor = TradeSummaryDB.cursor()
        result = defaultdict(lambda: defaultdict(Decimal))
        for table_name in TradeSummaryDB.get_user_trade_summary_tables(start_date, end_date):
            sql = f"select user_id, money_asset, sum(deal_volume) from {table_name} " \
                  f"where trade_date >='{start_date}' and trade_date <= '{end_date}' " \
                  "group by user_id, money_asset"
            cursor.execute(sql)
            rows = cursor.fetchall()
            for user_id, asset, amount in rows:
                if user_id not in all_user_ids:
                    continue
                main_user_id = sub_user_map.get(user_id, user_id)
                result[main_user_id][asset] += prices.get(asset, 0) * amount
        return result


class UserExport:

    @classmethod
    def get_target_users(
            cls,
            start_time=None,
            end_time=None,
            locations=None,
            user_ids=None,
            columns=None,
            user_type=None
    ):
        """查询用户信息"""
        query = User.query
        if start_time:
            query = query.filter(
                User.created_at >= start_time
            )
        if end_time:
            query = query.filter(
                User.created_at < end_time
            )
        if locations:
            query = query.filter(
                User.location_code.in_(locations)
            )
        if user_ids:
            query = query.filter(
                User.id.in_(user_ids)
            )
        if user_type:
            query = query.filter(
                User.user_type.in_(user_type)
            )
        if columns:
            query = query.with_entities(
                *(getattr(User, i) for i in columns)
            )
        return {
            i.id: i for i in query.all()
        }

    @classmethod
    def get_sub_user_mapper(cls, user_ids: List[int] = None):
        """查询用户子账号"""
        query = SubAccount.query
        if user_ids:
            query = query.filter(
                SubAccount.user_id.in_(user_ids)
            )
        query = query.with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id
        ).all()
        return {
            i.user_id: i.main_user_id for i in query
        }

    @classmethod
    def get_user_final_login_time_mapper(cls, user_ids: List[int] = None):
        """查询用户最后一次登录时间"""
        query = LoginHistory.query
        if user_ids:
            query = query.filter(
                LoginHistory.user_id.in_(user_ids)
            )
        query = query.group_by(
            LoginHistory.user_id
        ).with_entities(
            LoginHistory.user_id,
            func.max(LoginHistory.created_at)
        ).all()
        return {
            i: t for i, t in query
        }

    @classmethod
    def get_user_final_login_record(cls, user_ids):
        """查询用户最后一次登录记录"""
        table_name = "login_history"
        user_id_str = ",".join(map(str, user_ids))
        records = db.session.execute(
            f"""select * from {table_name} a where not exists
            (select * from {table_name} b where a.user_id=b.user_id
            and a.id<b.id) and user_id in ({user_id_str});"""
        ).fetchall()
        fields = inspect(LoginHistory).c.keys()
        result = {}
        for record in records:
            item = dict(zip(fields, record))
            result[item["user_id"]] = item
        return result

    @classmethod
    def get_user_lang_mapper(cls, user_ids: List[int] = None) -> Dict[int, Language]:
        """查询用户当前语言设置"""
        query = UserPreference.query.filter(
            UserPreference.status == UserPreference.Status.VALID,
            UserPreference.key == UserPreferences.language.name
        )
        if user_ids:
            query = query.filter(
                UserPreference.user_id.in_(user_ids)
            )
        query = query.with_entities(
            UserPreference.user_id,
            UserPreference.value
        ).all()
        return {i: Language[v] for i, v in query}

    @classmethod
    def get_user_referrer_mapper(cls, user_ids: List[int] = None, search_by_er=True):
        """通过邀请人或者被邀请人查询 被邀请人: 邀请人"""
        query = ReferralHistory.query.filter(
            ReferralHistory.status == ReferralHistory.Status.VALID
        )
        if user_ids and search_by_er:
            query = query.filter(
                ReferralHistory.referrer_id.in_(user_ids)
            )
        if user_ids and not search_by_er:
            query = query.filter(
                ReferralHistory.referree_id.in_(user_ids)
            )

        query = query.with_entities(
            ReferralHistory.referree_id,
            ReferralHistory.referrer_id
        ).all()
        return {
            ee: er for ee, er in query
        }

    @classmethod
    def get_user_referrer_count_mapper(cls, user_ids: List[int] = None):
        """获取用户的邀请人数"""
        query = ReferralHistory.query.filter(
            ReferralHistory.status == ReferralHistory.Status.VALID
        )
        if user_ids:
            query = query.filter(
                ReferralHistory.referrer_id.in_(user_ids)
            )
        query = query.group_by(
            ReferralHistory.referrer_id
        ).with_entities(
            ReferralHistory.referrer_id,
            func.count(ReferralHistory.referree_id)
        ).all()
        return {
            i: c for i, c in query
        }

    @classmethod
    def get_user_system_trade_mapper(
            cls,
            user_ids: List[int] = None,
            start_time=None,
            end_time=None
    ) -> Dict[int, Dict[UserTradeSummary.System, Dict[str, Any]]]:
        """获取用户交易量的统计"""
        query = UserTradeSummary.query
        if start_time:
            query = query.filter(
                UserTradeSummary.report_date >= start_time
            )
        if end_time:
            query = query.filter(
                UserTradeSummary.report_date < end_time
            )
        if user_ids:
            query = query.filter(
                UserTradeSummary.user_id.in_(user_ids)
            )
        query = query.group_by(
            UserTradeSummary.user_id,
            UserTradeSummary.system
        ).with_entities(
            UserTradeSummary.user_id,
            UserTradeSummary.system,
            func.count(UserTradeSummary.report_date.distinct()).label("trade_days"),
            func.sum(UserTradeSummary.trade_amount).label("sum_amount")
        ).all()
        result = defaultdict(lambda: defaultdict(dict))
        for user_id, system, trade_days, amount in query:
            result[user_id][system]["amount"] = amount  # 交易额
            result[user_id][system]["trade_days"] = trade_days  # 交易天数
        return result

    @classmethod
    def get_user_trade_fee_amount(cls, start_time=None, end_time=None, user_ids=None):
        """获取用户交易手续费"""
        query = UserTradeFeeSummary.query
        if start_time:
            query = query.filter(
                UserTradeFeeSummary.report_date >= start_time
            )
        if end_time:
            query = query.filter(
                UserTradeFeeSummary.report_date < end_time
            )
        if user_ids:
            query = query.filter(
                UserTradeFeeSummary.user_id.in_(user_ids)
            )
        query = query.group_by(
            UserTradeFeeSummary.user_id,
            UserTradeFeeSummary.system,

        ).with_entities(
            UserTradeFeeSummary.user_id,
            UserTradeFeeSummary.system,
            func.sum(UserTradeFeeSummary.trade_fee_amount).label("sum_fee")
        ).all()
        result = defaultdict(lambda: defaultdict(Decimal))
        for user_id, system, fee_amount in query:
            result[user_id][system] += fee_amount
        return result

    @classmethod
    def get_user_trade_amount(cls, start_time, end_time, user_ids):
        """获取用户某段时间的交易量"""
        system_mapper = cls.get_user_system_trade_mapper(
            start_time=start_time,
            end_time=end_time,
            user_ids=user_ids
        )
        res = defaultdict(Decimal)
        for user_id, map_ in system_mapper.items():
            for v in map_.values():
                res[user_id] += v["amount"]
        return res

    @classmethod
    def get_user_deposit_volume(cls, user_ids, start_time=None, end_time=None, date_price_mapper=None):
        """获取用户入金数量"""
        if not date_price_mapper:
            date_price_mapper = {}
        query = Deposit.query.filter(
            Deposit.user_id.in_(user_ids),
            Deposit.status.in_((Deposit.Status.FINISHED, Deposit.Status.CONFIRMING)),
            Deposit.type == Deposit.Type.ON_CHAIN
        )
        fiat_query = FiatOrder.query.filter(
            FiatOrder.user_id.in_(user_ids),
            FiatOrder.status == FiatOrder.StatusType.APPROVED,
            FiatOrder.order_type == FiatOrder.OrderType.BUY
        )
        if start_time:
            query = query.filter(
                Deposit.created_at >= start_time
            )
            fiat_query = fiat_query.filter(
                FiatOrder.created_at >= start_time
            )
        if end_time:
            query = query.filter(
                Deposit.created_at < end_time
            )
            fiat_query = fiat_query.filter(
                FiatOrder.created_at < end_time
            )
        records = query.with_entities(
            Deposit.user_id,
            Deposit.asset,
            Deposit.amount,
            Deposit.created_at,

        ).all()
        fiat_records = fiat_query.with_entities(
            FiatOrder.user_id,
            FiatOrder.asset,
            FiatOrder.coin_amount,
            FiatOrder.created_at
        ).all()
        result = defaultdict(Decimal)
        for user_id, asset, amount, created_date in records + fiat_records:
            created_date = created_date.date()
            if created_date not in date_price_mapper.keys():
                date_price_mapper[created_date] = AssetPrice.get_close_price_map(created_date)
            price = date_price_mapper[created_date].get(asset, Decimal())
            volume = price * amount
            result[user_id] += volume

        return result

    @classmethod
    def get_user_is_trade_mapper(
            cls,
            user_ids: List[int],
            business: UserBusinessRecord.Business = None
    ) -> Dict[Any, Dict[Any, bool]]:
        max_date = UserBusinessRecord.query.with_entities(
            func.max(UserBusinessRecord.report_at)
        ).scalar() or now().date()
        query = UserBusinessRecord.query.filter(
            UserBusinessRecord.report_at == max_date
        )
        if business:
            query = query.filter(
                UserBusinessRecord.business == business
            )
        result = defaultdict(lambda: defaultdict(bool))
        for item in query.all():
            business = item.business
            user_set = set(BitMap.deserialize(item.history_user_bit_map))
            for user_id in user_ids:
                result[user_id][business.value] = user_id in user_set

        return result


class UserBalanceExport:

    @classmethod
    def get_user_account_type_balance(cls, balance_time, user_ids: List[int]) -> Dict[int, Dict[int, Decimal]]:
        """获取用户账户类型资产余额"""
        balance_ts = datetime_to_time(balance_time)
        table = ExchangeLogDB.user_account_balance_table(balance_ts)
        record = table.select(
            *("user_id", "account_type", "SUM(balance_usd) as balance_usd"),
            where=f"`user_id` in ({','.join(map(str, user_ids))})",
            group_by="user_id, account_type"
        )
        account_type_mapper = defaultdict(lambda: defaultdict(Decimal))
        for user_id, account_type, balance_usd in record:
            account_type_mapper[user_id][account_type] += balance_usd
        return account_type_mapper

    @classmethod
    def get_user_all_balance(cls, balance_time, user_ids: List[int]):
        """获取用户的所有资产余额"""
        result = cls.get_user_account_type_balance(balance_time, user_ids)
        return {i: sum(v.values()) for i, v in result.items()}

    @classmethod
    @add_print
    def get_user_balance_sum(cls, user_ids):
        """获取用户总资产快照（未按主账号汇总）"""
        mapping = {}
        ts = int(now().timestamp())
        last_ts = ts - ts % (60 * 60 * 24)
        table = ExchangeLogDB.user_account_balance_sum_table(last_ts)
        for chunk_user_ids in batch_iter(user_ids, 5000):
            user_id_str = ','.join(map(str, chunk_user_ids))
            rows = table.select(
                'user_id', 'balance_usd',
                where=f' user_id in ({user_id_str})'
            )
            for (user_id, balance_usd) in rows:
                mapping.update({user_id: balance_usd})
        return mapping
