#!/usr/bin/env python3
import time
import uuid
from decimal import Decimal
from typing import Any, Dict, List

from flask import g

from ...business import cached, PriceManager
from ...config import config
from ...models import User, FiatOrder
from ...utils import AmountType, RESTClient, amount_to_str, quantize_amount
from ...utils.files import AWSBucketPublic
from .base import BasePartnerClient, Order, PaymentMethod, Quote, QuoteMethod, SupportType


class SimplexClient(BasePartnerClient):

    name = 'Simplex'
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_simplex_678.png')
    buy_assets = ['BTC', 'BCH', 'ETH', 'LTC', 'USDT', 'USDC']
    buy_fiats = [
        'USD', 'EUR', 'JPY', 'KRW',
        'BGN', 'BRL', 'CAD', 'CHF', 'CLP', 'COP', 'CZK',
        'DKK', 'GBP', 'HUF', 'IDR', 'ILS', 'INR', 'KZT', 'MAD',
        'MXN', 'MYR', 'NOK', 'NZD', 'PEN', 'PLN', 'QAR',
        'SEK', 'VND', 'ZAR', 'AUD', 'HKD', 'PHP', 'RON',
        'SAR', 'SGD', 'IQD', 'UAH',
        'JMD', 'BOB', 'PYG', 'ISK', 'TZS', 'GHS', 'OMR', 'LKR',
        'BHD', 'MOP', 'AED', 'PAB', 'MNT', 'TWD', 'MUR',
        'KES', 'THB', 'GTQ', 'KWD', 'BWP', 'BDT',
        'NGN', 'JOD',
    ]
    sell_assets = ['BTC', 'ETH', 'USDT', 'USDC']
    sell_fiats = ['EUR']
    support_types = [SupportType.BUY]
    buy_payment_methods = [PaymentMethod.VISA, PaymentMethod.MASTER, PaymentMethod.APPLE_PAY, PaymentMethod.PIX]
    sell_payment_methods = [PaymentMethod.SEPA, PaymentMethod.SWIFT]
    buy_quote_methods = [QuoteMethod.BY_ASSET, QuoteMethod.BY_FIAT]
    buy_fee_rate = Decimal('0.035')
    sell_fee_rate = Decimal('0.005')
    min_fee = Decimal(0)

    buy_order_limit_min = Decimal(30)
    buy_order_limit_max = Decimal(15000)
    sell_order_limit_min = Decimal(100)
    sell_order_limit_max = Decimal(14999)
    daily_limit = Decimal(20000)
    monthly_limit = Decimal(50000)
    help_url = 'https://www.simplex.com/support/'

    network_map = {
        'USDT': 'TRC20',
        'USDC': 'ERC20',
    }

    _blockchain_map = {
        ('USDT', 'TRC20'): 'USDT-TRC20',
        ('USDT', 'ERC20'): 'USDT',
        ('USDC', 'TRC20'): 'USDT-TRC20',
        ('USDC', 'ERC20'): 'USDC',
        ('USDC', 'BSC'): 'USDC-SC',
    }

    def __init__(self):
        self._conf = config['SIMPLEX_CONFIG']
        apikey = self._conf['apikey']
        self._client = RESTClient(self._conf['url'], headers={'Authorization': f'ApiKey {apikey}'})
        # TODO 暂时不上 simplex sell
        if SupportType.SELL in self.support_types:
            self._sell_client = RESTClient(
                self._conf['sell_url'],
                headers={'Authorization': f'ApiKey {self._conf["sell_apikey"]}'}
            )

    def _handle_response(self, response_data):
        if error := response_data.get('error'):
            raise RESTClient.BadResponse(-1, error)
        return response_data

    def _get_buy_quote_request(self, asset: str, fiat: str, request_amount: AmountType, _from=None, user_id=None) -> dict:
        request_params = {
            'end_user_id': user_id or '0',
            'digital_currency': asset,
            'fiat_currency': fiat,
            'client_ip': getattr(g, 'request_ip', ''),
            'requested_currency': _from or fiat,
            'requested_amount': float(request_amount),
            "wallet_id": 'coinex'
        }
        r = self._client.post('/wallet/merchant/v2/quote', request_params)
        r = self._handle_response(r)
        return r

    def _get_sell_quote_request(self, asset: str, fiat: str, asset_amount: AmountType,
                                fiat_amount: AmountType,
                                user_id=None) -> dict:
        params = dict(
            base_currency=asset,
            quote_currency=fiat,
            account_id=user_id or '0',
            wallet_id='coinex'
        )
        # millionths of a unit.
        if asset_amount and not fiat_amount:
            params["base_amount"] = float(asset_amount) * 10 ** 6
        elif fiat_amount and not asset_amount:
            params["quote_amount"] = float(fiat_amount) * 10 ** 6
        r = self._sell_client.get('v3/get-quote', **params)
        r = self._handle_response(r)
        return r

    @cached(300)
    def _get_price(self, asset: str, fiat: str, support_type: str) -> str:
        fiat_amount = getattr(self, f"{support_type}_order_limit_max") / 2
        if fiat != 'USD':
            fiat_amount = quantize_amount(fiat_amount / PriceManager.fiat_to_usd(fiat), 2)
        if support_type == SupportType.BUY.value:
            r = self._get_buy_quote_request(asset, fiat, fiat_amount)
            asset_amount = Decimal(r['digital_money']['amount'])
            fiat_amount = Decimal(r['fiat_money']['total_amount'])
            price = fiat_amount / asset_amount

        elif support_type == SupportType.SELL.value:
            r = self._get_sell_quote_request(asset, fiat, Decimal(), fiat_amount)
            # rate not include fee
            price = Decimal(r['rate']) * (1 - self.sell_fee_rate)
        else:
            raise ValueError(f'invalid support type: {support_type}')
        
        return amount_to_str(price, self._price_precision(asset))

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        price = self._get_price(asset, fiat, support_type.value)
        return Decimal(price)

    def fiat_to_asset_amount(self, fiat: str, asset: str, fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def quote(self,
              user: User,
              from_: str,
              to: str,
              amount: AmountType,
              support_type=SupportType.BUY) -> Quote:
        asset_amount = Decimal()
        fiat_amount = Decimal()
        assets = getattr(self, f"{support_type.value}_assets")
        fiats = getattr(self, f"{support_type.value}_fiats")
        if from_ in assets and to in fiats:
            asset = from_
            fiat = to
            asset_amount = amount
        elif from_ in fiats and to in assets: 
            asset = to
            fiat = from_
            fiat_amount = amount
        else:
            raise ValueError(f'invalid asset and fiat `{from_}` `{to}`')

        asset_to_quote = self._blockchain_map.get((asset, self.get_asset_chain(asset))) or asset
        if support_type == SupportType.BUY:
            r = self._get_buy_quote_request(asset_to_quote, fiat, amount, from_, str(user.id))
            asset_amount = Decimal(r['digital_money']['amount'])
            asset_amount = quantize_amount(asset_amount, self._asset_amount_precision(asset))
            fiat_amount = Decimal(r['fiat_money']['total_amount'])
            real_fiat_amount = fiat_amount
            price = Decimal()
            if asset_amount > 0:
                if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
                    real_fiat_amount -= (self.min_fee - fee)
                    if real_fiat_amount < 0:
                        real_fiat_amount = 0
                price = quantize_amount(real_fiat_amount / asset_amount, self._price_precision(asset))
        elif support_type == SupportType.SELL:
            r = self._get_sell_quote_request(asset_to_quote, fiat, asset_amount, fiat_amount, str(user.id))
            price = quantize_amount(Decimal(r['rate'] * (1 - self.sell_fee_rate)), self._price_precision(asset))
            if fiat_amount and not asset_amount:
                asset_amount = quantize_amount(fiat_amount / price, self._asset_amount_precision(asset))
            elif asset_amount and not fiat_amount:
                fiat_amount = quantize_amount(asset_amount * price, 2)
            else:
                raise ValueError(f'invalid amount error, fiat_amount: {fiat_amount}, asset_amount: {asset_amount}')
        else:
            raise ValueError(f'invalid support_type: {support_type}')

        return Quote(
            id=r['quote_id'],
            asset=asset,
            asset_amount=asset_amount,
            fiat=fiat,
            fiat_amount=fiat_amount,
            price=price,
            support_type=support_type
        )

    def _place_sell_order(self, quote: Quote, address: str, user: User) -> tuple:
        data = {
            "referer_url": f"{config['SITE_URL']}",
            "return_url": f"{config['SITE_URL']}/trade-crypto",
            "txn_details": {
                "quote_id": quote.id,
                "refund_crypto_address": address
            },
            "account_details": {
                "account_id": str(user.id),
                "app_provider_id": "coinex",
                "web_sessions": [{
                    "ip": getattr(g, 'request_ip', user.registration_ip),
                    "timestamp": time.time()
                }]
            }
        }
        r = self._sell_client.post("/v3/initiate-sell", data)
        r = self._handle_response(r)
        return r['txn_id'], r['txn_url']

    def _place_buy_order(self, quote: Quote, address: str, user: User, order_id: str, payment_id: str):
        asset_to_order = self._blockchain_map.get((quote.asset, self.get_asset_chain(quote.asset))) or quote.asset
        data = {
            'account_details': {
                'app_version_id': '1',
                'app_end_user_id': str(user.id),
                "app_provider_id": "coinex",
                'signup_login': {
                    'timestamp': user.created_at.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    'ip': getattr(g, 'request_ip', user.registration_ip)
                },
            },
            'transaction_details': {
                'payment_details': {
                    'quote_id': quote.id,
                    'order_id': order_id,
                    'payment_id': payment_id,
                    'destination_wallet': {
                        'currency': asset_to_order,
                        'address': address
                    }
                }
            }
        }
        self._client.post('/wallet/merchant/v2/payments/partner/data', data)

    def place_order(self,
                    user: User,
                    quote: Quote,
                    address: str) -> Order:
        order_id = str(uuid.uuid4())
        payment_id = str(uuid.uuid4())
        support_type = SupportType(quote.support_type)
        
        if support_type == SupportType.BUY:
            self._place_buy_order(quote, address, user, order_id, payment_id)
            order = Order(id=order_id, payment_url=self._conf['payment_url'], extra={
                'version': '1',
                'partner': 'coinex',
                'payment_flow_type': 'wallet',
                'payment_id': payment_id
            })
        elif support_type == SupportType.SELL:
            txn_id, payment_url = self._place_sell_order(quote, address, user)
            order = Order(id=order_id, payment_url=payment_url, extra={
                'payment_id': txn_id
            })
        else:
            raise ValueError(f'invalid support_type: {support_type}')

        return order

    def get_events(self) -> List[Dict[str, Any]]:
        r = self._client.get('/wallet/merchant/v2/events')
        return self._handle_response(r)

    def delete_event(self, event_id: str):
        r = self._client.delete(f'/wallet/merchant/v2/events/{event_id}')
        return self._handle_response(r)

    def get_msgs(self):
        r = self._sell_client.get('/v3/msg')
        return self._handle_response(r)

    def ack_receipt(self, msg_id: str):
        r = self._sell_client.post(f'/v3/msg/{msg_id}/ack')
        return self._handle_response(r)

    def msg_response(self, msg_id: str, data: dict):
        self._sell_client.post(f"v3/msg/{msg_id}/response", data)
        self.ack_receipt(msg_id)

    def get_supported_fiats(self):
        currencies = self._client.get('/v2/supported_fiat_currencies', public_key=self._conf['public_key'])
        return [currency['ticker_symbol'].upper() for currency in currencies]

    def get_supported_assets(self):
        currencies = self._client.get('/v2/supported_crypto_currencies', public_key=self._conf['public_key'])
        return [currency['ticker_symbol'].upper() for currency in currencies]

    def get_tx_info(self, order_detail, asset: str, order_type: FiatOrder.OrderType):
        """仅当订单完成时，才有 TX 信息"""
        chain = tx_id = tx_url = None
        if order_type is FiatOrder.OrderType.BUY:
            chain = self.get_asset_chain(asset)  # to ours
            tx_id = order_detail.get('blockchain_txn_hash')
        return chain, tx_id, tx_url
