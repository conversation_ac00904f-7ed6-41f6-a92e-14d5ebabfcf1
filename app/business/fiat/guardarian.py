import uuid
from decimal import Decimal
from itertools import product
from typing import Any, Dict, Optional

from flask import g
from .. import PriceManager
from ...caches.fiat import FiatPriceCache
from ...config import config
from ...models import User, FiatOrder
from ...utils import (AmountType, RESTClient, amount_to_str, quantize_amount, g_map)
from ...utils.files import AWSBucketPublic
from .base import BasePartnerClient, Order, PaymentMethod, Quote, QuoteMethod, SupportType


class GuardarianClient(BasePartnerClient):
    name = 'Guardarian'
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_guardarian.png')
    buy_assets = ['BTC', 'ETH', 'USDT', 'USDC']  # 用户角度
    buy_fiats = [
        'USD', 'GBP', 'EUR', 'TRY', 'DKK', 'PLN',
        'HRK', 'RON', 'CZK', 'ZAR', 'ILS', 'AED', 'AUD',
        'CAD', 'JPY', 'NZD', 'NOK', 'SGD', 'PHP', 'CHF',
        'KRW', 'COP', 'BGN', 'MXN', 'BRL', 'THB',
        'TWD', 'HKD', 'PEN', 'OMR', 'BDT', 'VND', 'GHS',
    ]
    sell_fiats = ['USD', 'EUR', 'GBP', 'GHS']
    sell_assets = ['BTC', 'ETH', 'USDT', 'USDC']
    support_types = [SupportType.BUY, SupportType.SELL]
    buy_payment_methods = sell_payment_methods = [
        PaymentMethod.VISA,
        PaymentMethod.MASTER,
        PaymentMethod.PIX,
        PaymentMethod.REVOLUT
    ]
    buy_quote_methods = [QuoteMethod.BY_FIAT]
    sell_quote_methods = [QuoteMethod.BY_ASSET]
    buy_fee_rate = Decimal('0.0323')
    sell_fee_rate = Decimal('0.0042')
    min_fee = Decimal()

    buy_order_limit_min = Decimal(10)
    buy_order_limit_max = Decimal(5000)
    sell_order_limit_max = Decimal(1500)
    sell_order_limit_min = Decimal(200)
    daily_limit = Decimal(2600)
    monthly_limit = Decimal(78000)
    help_url = 'https://guardarian.freshdesk.com/support/home'

    network_map = {
        'USDT': 'TRC20',
        'USDC': 'ERC20',
    }

    _blockchain_map = {
        'BSC': 'BSC',
        'ERC20': 'ETH',
        'TRC20': 'TRX',
        'BTC': 'BTC'
    }

    def __init__(self):
        self._conf = config['GUARDARIAN_CONFIG']
        self._client = RESTClient(self._conf['url'], headers={
            "x-api-key": self._conf['x-api-key'],
            'X-Forwarded-For': getattr(g, 'request_ip', None)
        })

    def get_prices(self):

        def _get_prices(self_assets, self_fiats, support_type):
            assets = []
            fiats = []
            for asset, fiat in product(self_assets, self_fiats):
                assets.append(asset)
                fiats.append(fiat)
            support_types = [support_type] * len(assets)
            prices = g_map(self._get_price, assets, fiats, support_types, ordered=True, fail_safe=Decimal(), size=5)
            return {
                f"{support_type.value}-{asset}-{fiat}": amount_to_str(price, 2)
                for asset, fiat, price in zip(assets, fiats, prices) if price
            }

        buy_prices = _get_prices(self.buy_assets, self.buy_fiats, SupportType.BUY)
        sell_prices = _get_prices(self.sell_assets, self.sell_fiats, SupportType.SELL)
        return {**buy_prices, **sell_prices}

    def _get_price(self, asset, fiat, support_type):
        fiat_amount = getattr(self, f"{support_type.value}_order_limit_max") / 2
        if fiat != 'USD':
            fiat_usd = PriceManager.fiat_to_usd(fiat)
            if fiat_usd == 0:
                return 0
            fiat_amount = quantize_amount(fiat_amount / fiat_usd, 2)
        if support_type is SupportType.BUY:
            amount = fiat_amount
        else:
            asset_usd = PriceManager.asset_to_usd(asset)
            if asset_usd == 0:
                return 0
            amount = fiat_amount / asset_usd

        data = self._get_estimate_data(asset, fiat, support_type, amount)
        value = data.get("value", 0)
        return self._get_price_by(amount, value, support_type)

    def _get_estimate_data(self, asset, fiat, support_type, amount, _type=None):
        chain = self.get_asset_chain(asset)
        if support_type is SupportType.BUY:
            if _type == 'reverse':
                params = {
                    'from_currency': fiat,
                    'to_currency': asset,
                    'to_network': self._blockchain_map[chain],
                    'to_amount': amount,
                    'type': _type
                }
            else:
                params = {
                    'from_currency': fiat,
                    'to_currency': asset,
                    'to_network': self._blockchain_map[chain],
                    'from_amount': amount,
                }
        else:
            if _type == 'reverse':
                params = {
                    'from_currency': asset,
                    'to_currency': fiat,
                    'from_network': self._blockchain_map[chain],
                    'to_amount': amount,
                    'type': _type
                }
            else:
                params = {
                    'from_currency': asset,
                    'to_currency': fiat,
                    'from_network': self._blockchain_map[chain],
                    'from_amount': amount,
                }
        data = self._client.get("/v1/estimate", **params)
        return data

    @classmethod
    def _get_price_by(cls, amount, value, support_type, _type=None):
        value = Decimal(value)
        if support_type is SupportType.BUY:
            if _type == 'reverse':
                return value / amount if amount > 0 else 0
            return amount / value if value > 0 else 0
        else:
            if _type == 'reverse':
                return amount / value if value > 0 else 0
            return value / amount if amount > 0 else 0

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        cache = FiatPriceCache(self.name.lower())
        return quantize_amount(cache.get_price(asset, fiat, support_type), self._price_precision(asset))

    def fiat_to_asset_amount(self, fiat: str, asset: str, fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def quote(self,
              user: User,
              from_: str,
              to: str,
              amount: AmountType,
              support_type=SupportType.BUY) -> Quote:
        assets = getattr(self, f"{support_type.value}_assets")
        fiats = getattr(self, f"{support_type.value}_fiats")
        asset_amount = fiat_amount = Decimal()
        amount = Decimal(amount)
        _type = 'direct'
        if from_ in assets and to in fiats:
            asset = from_
            fiat = to
            asset_amount = amount
            if support_type is SupportType.BUY:
                _type = 'reverse'
        elif from_ in fiats and to in assets:
            asset = to
            fiat = from_
            fiat_amount = amount
            if support_type is SupportType.SELL:
                _type = 'reverse'
        else:
            raise ValueError(f'invalid asset and fiat `{from_}` `{to}`')

        price_data = self._get_estimate_data(asset, fiat, support_type, amount, _type)
        price = self._get_price_by(amount, price_data['value'], support_type)
        return_amount = Decimal(price_data['value'])
        if support_type == SupportType.BUY:
            asset_amount = return_amount
            if _type == 'reverse':
                fiat_amount = return_amount
                asset_amount = amount
        else:
            fiat_amount = return_amount
            if _type == 'reverse':
                fiat_amount = amount
                asset_amount = return_amount
        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            asset_amount=quantize_amount(asset_amount, self._asset_amount_precision(asset)),
            fiat=fiat,
            fiat_amount=quantize_amount(fiat_amount, 2),
            price=quantize_amount(price, self._price_precision(asset)),
            support_type=support_type
        )

    def place_order(self,
                    user: User,
                    quote: Quote,
                    address: str) -> Order:
        data = {}
        support_type = SupportType(quote.support_type)
        chain = self.get_asset_chain(quote.asset)
        if support_type == SupportType.BUY:
            data.update(
                {
                    'from_amount': quote.fiat_amount,
                    'from_currency': quote.fiat,
                    'to_currency': quote.asset,
                    'to_network': self._blockchain_map[chain],
                    "payout_address": address,
                    "skip_choose_payout_address": True,
                }
            )
        elif support_type == SupportType.SELL:
            data.update(
                {
                    'from_amount': quote.asset_amount,
                    'from_currency': quote.asset,
                    'to_currency': quote.fiat,
                    'from_network': self._blockchain_map[chain],
                }
            )
        if email := user.main_user.email:
            contact_info = dict(email=email)
        else:
            contact_info = dict(phone_number=user.main_user.mobile) 
        data['customer'] = {
            'contact_info': contact_info
        }
        ret = self._client.post("/v1/transaction", json=data)
        return Order(
            id=ret['id'],
            payment_url=ret['redirect_url'],
            extra=ret
        )

    def get_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        r = self._client.get(f'/v1/transaction/{order_id}')
        return r

    def get_supported_fiats(self):
        currencies = self._client.get('/v1/currencies/fiat')
        return [currency['ticker'].upper() for currency in currencies]

    def get_supported_assets(self):
        currencies = self._client.get('/v1/currencies/crypto')
        return [currency['ticker'].upper() for currency in currencies]

    def get_tx_info(self, order_detail, asset: str, order_type: FiatOrder.OrderType):
        """仅当订单完成时，才有 TX 信息"""
        chain = tx_id = tx_url = None
        if order_type is FiatOrder.OrderType.BUY:
            chain = self.get_asset_chain(asset)  # to ours
            tx_id = order_detail['output_hash']
        return chain, tx_id, tx_url
