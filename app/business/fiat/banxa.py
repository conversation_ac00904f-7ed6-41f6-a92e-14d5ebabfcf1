#!/usr/bin/env python3
import hmac
import time
import uuid
from decimal import Decimal
from itertools import product
from typing import Any, Dict
from urllib.parse import urlencode

from .base import BasePartnerClient, Order, PaymentMethod, Quote, QuoteMethod, SupportType
from ...caches.fiat import FiatPriceCache
from ...config import config
from ...models import FiatOrder, User
from ...utils import AmountType, RESTClient, amount_to_str, quantize_amount, g_map
from ...utils.files import AWSBucketPublic


class BanxaClient(BasePartnerClient):
    name = 'Banxa'
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_banxa.png')
    buy_assets = ['BTC', 'ETH', 'USDT', 'USDC']
    buy_fiats = [
        'USD', 'AUD', 'CAD', 'CZK', 'DKK', 'EUR', 'GBP', 'HKD', 'NOK', 'PLN',
        'SEK', 'JPY', 'SGD', 'TRY', 'AED', 'BRL', 'IDR', 'INR',
        'KRW', 'MXN', 'NZD', 'PHP', 'QAR', 'SAR', 'THB', 'VND', 'ZAR', 'CHF',
        'TWD', 'CLP', 'COP'
    ]
    sell_fiats = ['AUD', 'CAD', 'EUR', 'GBP']
    sell_assets = ['BTC', 'USDT', 'USDC']
    support_types = [SupportType.BUY, SupportType.SELL]
    buy_payment_methods = sell_payment_methods = [PaymentMethod.VISA, PaymentMethod.MASTER, PaymentMethod.SEPA, PaymentMethod.BANK]
    buy_quote_methods = [QuoteMethod.BY_ASSET, QuoteMethod.BY_FIAT]
    sell_quote_methods = [QuoteMethod.BY_ASSET, QuoteMethod.BY_FIAT]
    buy_fee_rate = Decimal('0.0199')
    sell_fee_rate = Decimal('0')
    min_fee = Decimal(10)

    buy_order_limit_min = Decimal(50)
    buy_order_limit_max = Decimal(10000)
    sell_order_limit_max = Decimal(10000)
    sell_order_limit_min = Decimal(200)
    daily_limit = Decimal(15000)
    monthly_limit = Decimal(60000)
    help_url = 'https://support.banxa.com/'

    network_map = {
        'USDT': 'TRC20',
        'USDC': 'ERC20'
    }

    _blockchain_map = {
        'TRC20': 'TRON',
        'ERC20': 'ETH'
    }

    def __init__(self):
        self._conf = config['BANXA_CONFIG']

    def _generate_hmac(self, payload, nonce):
        hmac_code = hmac.digest(self._conf['secret'], payload.encode('utf8'), 'SHA256')
        return self._conf['key'] + ':' + hmac_code.hex() + ':' + str(nonce)

    def _send_get_request(self, url, params=None):
        if params:
            url = f"{url}?{urlencode(params)}"
        nonce = int(time.time())
        data = 'GET\n' + url + '\n' + str(nonce)
        auth_header = self._generate_hmac(data, nonce)
        response = RESTClient(self._conf['url'], headers={
                'Authorization': 'Bearer ' + auth_header,
                'Content-Type': 'application/json'
        }).get(url)
        return self._handle_response(response)

    def _send_post_request(self, url, request_params: Dict[str, Any]):
        params_list = []
        for k, value in request_params.items():
            params_list.append(f'"{k}":"{value}"')
        params_str = ','.join(params_list)
        params = "{" + params_str + "}"

        nonce = int(time.time())

        data = 'POST\n' + url + '\n' + str(nonce) + '\n' + params

        auth_header = self._generate_hmac(data, nonce)

        response = RESTClient(self._conf['url'], headers={
            'Authorization': 'Bearer ' + auth_header,
            'Content-Type': 'application/json'
        }).post(api=url, data=params.encode("utf8"))

        return self._handle_response(response)

    def get_prices(self):

        def _get_prices(self_assets, self_fiats, support_type):
            assets = []
            fiats = []
            for asset, fiat in product(self_assets, self_fiats):
                assets.append(asset)
                fiats.append(fiat)
            support_types = [support_type] * len(assets)
            prices = g_map(self._get_price, assets, fiats, support_types, ordered=True, fail_safe=Decimal(), size=5)
            return {
                f"{support_type.value}-{asset}-{fiat}": amount_to_str(price, 2)
                for asset, fiat, price in zip(assets, fiats, prices) if price
            }

        buy_prices = _get_prices(self.buy_assets, self.buy_fiats, SupportType.BUY)
        sell_prices = _get_prices(self.sell_assets, self.sell_fiats, SupportType.SELL)
        return {**buy_prices, **sell_prices}

    def _get_price(self, asset, fiat, support_type):
        if support_type is SupportType.BUY:
            # 使用官方最大值(10000)得到的平均值计算的价格过低(1.01)。
            # 用户目前购买最大值是 2000 左右，使用此值计算 usdt 价格会在 1.03 左右，更符合实际情况。
            tmp_limit_max = Decimal(2000)
            fiat_amount = int((self.buy_order_limit_min + tmp_limit_max) / 2)
            params = dict(source=fiat, target=asset, source_amount=fiat_amount)
            data = self._send_get_request('/api/prices', params)
            price = 0
            if price_dict := data.get("prices"):
                price_data = price_dict[0]
                if coin_amount := price_data.get('coin_amount'):
                    price = Decimal(price_data['fiat_amount']) / Decimal(coin_amount)
        else:
            params = dict(source=asset, target=fiat)
            data = self._send_get_request('/api/prices', params)
            price = data.get("spot_price", 0)
        return quantize_amount(price, 2)

    def _handle_response(self, response_data):
        if not (data := response_data.get('data')):
            raise RESTClient.BadResponse(-1, f"error={data}")
        return data

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        cache = FiatPriceCache(self.name.lower())
        return cache.get_price(asset, fiat, support_type)

    def fiat_to_asset_amount(self, fiat: str, asset: str, fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def quote(self, user, from_: str, to: str, amount: AmountType, support_type=SupportType.BUY) -> Quote:
        assets = getattr(self, f"{support_type.value}_assets")
        fiats = getattr(self, f"{support_type.value}_fiats")
        asset_amount, fiat_amount = Decimal(), Decimal()
        if from_ in assets and to in fiats:
            asset, fiat = from_, to
            asset_amount = amount
        elif from_ in fiats and to in assets:
            asset, fiat = to, from_
            fiat_amount = amount
        else:
            raise ValueError(f'invalid asset and fiat `{from_}` `{to}`')

        if support_type == SupportType.BUY:
            params = dict(
                source=fiat,
                target=asset
            )
            if asset_amount:
                params["target_amount"] = asset_amount
            if fiat_amount:
                params["source_amount"] = fiat_amount
        else:
            params = dict(
                source=asset,
                target=fiat
            )
            if asset_amount:
                params["source_amount"] = asset_amount
            if fiat_amount:
                params["target_amount"] = fiat_amount

        chain = self.get_asset_chain(asset, user)
        blockchain = self._blockchain_map.get(chain)
        if blockchain:
            params["blockchain"] = blockchain
        data = self._send_get_request("/api/prices", params)
        if not (price_data := data.get("prices")):
            raise ValueError(f"Banxa Server Error")
        price_dict = price_data[0]
        asset_amount = price_dict['coin_amount']
        fiat_amount = price_dict['fiat_amount']
        price = Decimal(fiat_amount) / Decimal(asset_amount) if asset_amount else 0
        # price = data['spot_price']
        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            asset_amount=quantize_amount(asset_amount, self._asset_amount_precision(asset)),
            fiat=fiat,
            fiat_amount=quantize_amount(fiat_amount, 2),
            price=quantize_amount(price, 2),
            support_type=support_type,
        )

    def place_order(self, user, quote: Quote, address: str) -> Order:
        support_type = SupportType(quote.support_type)
        chain = self.get_asset_chain(quote.asset, user)
        data = {}
        blockchain = self._blockchain_map.get(chain)
        if blockchain:
            data["blockchain"] = blockchain
        if support_type == SupportType.BUY:
            data = {
                "account_reference": user.email,
                "target": quote.asset,
                "source": quote.fiat,
                "wallet_address": address,
                "source_amount": amount_to_str(quote.fiat_amount),
                "return_url_on_success": config["SITE_URL"],
                "return_url_on_cancelled": config["SITE_URL"],
                "return_url_on_failure": config["SITE_URL"],
                **data
            }
        elif support_type == SupportType.SELL:
            data = {
                "account_reference": user.email,
                "target": quote.fiat,
                "source": quote.asset,
                "refund_address": address,
                "source_amount": amount_to_str(quote.asset_amount),
                "return_url_on_success": config["SITE_URL"],
                "return_url_on_cancelled": config["SITE_URL"],
                "return_url_on_failure": config["SITE_URL"],
                **data
            }
        r = self._send_post_request('/api/orders', data)
        order = r.get('order')
        if not order:
            raise ValueError("Banxa Server Error")
        order_id = order['id']
        return Order(
            id=order_id,
            payment_url=order['checkout_url'],
            extra={
                "payment_id": order_id
            }
        )

    def get_order(self, order_id):
        data = self._send_get_request(f"/api/orders/{order_id}")
        return data.get("order")

    def get_supported_fiats(self):
        currencies = self._send_get_request(f"/api/fiats/buy")
        return [currency['fiat_code'].upper() for currency in currencies['fiats']]

    def get_supported_assets(self):
        currencies = self._send_get_request(f"/api/coins/buy")
        return [currency['coin_code'].upper() for currency in currencies['coins']]

    def get_tx_info(self, order_detail, asset: str, order_type: FiatOrder.OrderType, user_id):
        """仅当订单完成时，才有 TX 信息"""
        chain = tx_id = tx_url = None
        if order_type is FiatOrder.OrderType.BUY:
            user = User.query.get(user_id)
            chain = self.get_asset_chain(asset, user)  # to ours
            tx_id = order_detail['tx_hash']
        return chain, tx_id, tx_url

    def get_asset_chain(self, asset: str, user=None) -> str:
        if user and user.location_code == 'USA' and asset in ('USDT', 'USDC'):
            return 'ERC20'
        return super().get_asset_chain(asset)