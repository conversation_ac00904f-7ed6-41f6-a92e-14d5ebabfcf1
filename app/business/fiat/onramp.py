import base64
import hashlib
import hmac
import json
import time
import uuid
from datetime import datetime
from decimal import Decimal
from itertools import product

from app import config
from app.business import cached, PriceManager
from app.business.fiat.base import BasePartnerClient, SupportType, PaymentMethod, QuoteMethod, Quote, Order
from app.caches.fiat import FiatPriceCache
from app.models import FiatOrder
from app.utils import RESTClient, quantize_amount, AmountType, g_map, amount_to_str, AWSBucketPublic, url_join


class OnRampClient(BasePartnerClient):
    """https://docs.onramp.money/onramp/"""
    name = "OnRamp"
    logo = AWSBucketPublic.get_file_url("coinex_picture_manage_ic_onramp.png")
    buy_assets = ["USDT", "ETH", ]
    sell_assets = ["USDT", "ETH", "USDC"]
    buy_fiats = [
        "INR", "TRY", "AED", "MXN", "VND", "NGN", "BRL", "PEN",
        "COP", "CLP", "EUR", "IDR", "KES", "GBP",
        "THB", "ZAR", "ARS"
    ]
    sell_fiats = ["INR", "TRY", "NGN"]
    support_types = [
        SupportType.BUY,
        SupportType.SELL
    ]
    buy_payment_methods = sell_payment_methods = [
        PaymentMethod.UPI,
        PaymentMethod.IMPS,
        PaymentMethod.BANK,
    ]
    buy_quote_methods = [QuoteMethod.BY_FIAT]
    sell_quote_methods = [QuoteMethod.BY_ASSET]
    buy_fee_rate = Decimal('0.0025')
    # DONE: confirming sell fee rate
    sell_fee_rate = Decimal('0.0025')
    min_fee = Decimal('0.0')

    buy_order_limit_min = Decimal(20)
    buy_order_limit_max = Decimal(500000)

    sell_order_limit_min = Decimal(20)
    sell_order_limit_max = Decimal(500000)

    daily_limit = Decimal(1000000)
    monthly_limit = Decimal(1000000)
    help_url = 'https://onramp-money.freshdesk.com/support/home/'
    max_page_num = 3

    # DONE: confirming offramp USDC network
    network_map = {
        "USDT": "TRC20",
        "ETH": "ERC20",
        "USDC": "ERC20",
    }

    action_map = {
        SupportType.BUY: 1,
        SupportType.SELL: 2
    }

    fiat_type_map = {
        "INR": 1,
        "TRY": 2,
        "AED": 3,
        "MXN": 4,
        "VND": 5,
        "NGN": 6,
        "BRL": 7,
        "PEN": 8,
        "COP": 9,
        "CLP": 10,
        "EUR": 12,
        "IDR": 14,
        "KES": 15,
        "GBP": 20,
        "THB": 27,
        "ZAR": 17,
        "ARS": 29,
    }

    def __init__(self):
        self._conf = config['ONRAMP_CONFIG']
        self._api_key = self._conf["api_key"]

    def _get_sign(self, payload):
        return hmac.new(self._conf["api_secret"].encode(), payload.encode(), hashlib.sha512).hexdigest()

    def _request(self, method, url, body=None):
        methods = ["get", "post", "put", "delete"]
        if method not in method:
            raise ValueError(f"methods must in {methods}")
        payload_data = {
            "timestamp": int(time.time() * 1000),
        }
        if body:
            payload_data["body"] = body
        payload = base64.b64encode(json.dumps(payload_data).encode()).decode()
        client = RESTClient(
            self._conf['url'],
            headers={
                'Accept': 'application/json',
                'Content-Type': 'application/json;charset=UTF-8',
                'X-ONRAMP-SIGNATURE': self._get_sign(payload),
                'X-ONRAMP-APIKEY': self._api_key,
                'X-ONRAMP-PAYLOAD': payload
            }
        )
        func = getattr(client, method)
        resp = func(url, body) if body else func(url)
        return self._handler_resp(resp)

    @staticmethod
    def _handler_resp(resp):
        return resp["data"]

    @cached(600)
    def _get_all_config(self, support_type: str):
        # DONE: support offramp config
        if SupportType[support_type] is SupportType.BUY:
            endpoint = "/v2/buy/public/allConfig"
        else:
            endpoint = "/v2/sell/public/allConfig"
        data = self._request("get", endpoint)
        return data

    def _get_asset_chain_id(self, asset: str, chain: str, support_type: SupportType):
        coin_config = self._get_all_config(support_type.name)
        asset_cfg, chain_cfg = coin_config["allCoinConfig"], coin_config["networkConfig"]
        chain_map = {v["chainSymbol"]: k for k, v in chain_cfg.items()}
        return asset_cfg[asset.lower()]["coinId"], chain_map[chain.lower()]

    def calc_price(self, asset, fiat, support_type):
        fiat_amount = int(getattr(self, f"{support_type.value}_order_limit_max") / 2)
        if support_type is SupportType.BUY:
            amount = fiat_amount
        else:
            asset_usd = PriceManager.asset_to_usd(asset)
            amount = fiat_amount / asset_usd
        data = self._get_quote_response(asset, fiat, amount, support_type)
        if support_type is SupportType.BUY:
            value = Decimal(data["quantity"])
            return amount / value if value > 0 else 0
        else:
            value = Decimal(data["fiatAmount"])
            return value / amount if amount > 0 else 0

    def _get_quote_response(self, asset, fiat, amount, support_type):
        endpoint = "/v2/common/transaction/quotes"
        chain = self.get_asset_chain(asset)
        asset_id, chain_id = self._get_asset_chain_id(asset, chain, support_type)
        body = {
            "coinId": asset_id,
            "chainId": chain_id,
            "fiatType": self.fiat_type_map[fiat],
            "type": self.action_map[support_type]
        }
        if support_type is SupportType.BUY:
            body.update({"fiatAmount": float(amount)})
        else:
            body.update({"quantity": float(amount)})
        r = self._request("post", endpoint, body=body)
        return r

    def _get_prices(self, assets, fiats, support_type):
        tmp_assets = []
        tmp_fiats = []
        for asset, fiat in product(assets, fiats):
            tmp_assets.append(asset)
            tmp_fiats.append(fiat)
        support_types = [support_type] * len(tmp_assets)
        prices = g_map(self.calc_price, tmp_assets, tmp_fiats, support_types,
                       ordered=True,
                       fail_safe=Decimal(),
                       size=5)
        return {
            f"{support_type.value}-{asset}-{fiat}": amount_to_str(price, 2)
            for asset, fiat, price in zip(tmp_assets, tmp_fiats, prices) if price
        }

    def get_prices(self):
        buy_prices = self._get_prices(self.buy_assets, self.buy_fiats, SupportType.BUY)
        sell_prices = self._get_prices(self.sell_assets, self.sell_fiats, SupportType.SELL)
        return {
            **buy_prices,
            **sell_prices
        }

    def quote(self, user, from_: str, to: str, amount: AmountType, support_type=SupportType.BUY) -> Quote:
        assets = getattr(self, f"{support_type.value}_assets")
        fiats = getattr(self, f"{support_type.value}_fiats")
        amount = Decimal(amount)
        if from_ in assets and to in fiats:
            asset, fiat = from_, to
            asset_amount = amount
            price = self.asset_to_fiat(asset, fiat, support_type)
            fiat_amount = price * asset_amount
        elif from_ in fiats and to in assets:
            asset, fiat = to, from_
            fiat_amount = amount
            price = self.asset_to_fiat(asset, fiat, support_type)
            asset_amount = self.fiat_to_asset_amount(fiat, asset, amount)
        else:
            raise ValueError(f'invalid asset and fiat `{from_}` `{to}`')

        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            fiat=fiat,
            asset_amount=asset_amount,
            fiat_amount=fiat_amount,
            price=price,
            support_type=support_type
        )

    def fiat_to_asset_amount(self, fiat: str, asset: str, fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, self._asset_amount_precision(asset))

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        cache = FiatPriceCache(self.name.lower())
        return cache.get_price(asset, fiat, support_type)

    def place_order(self, user, quote: Quote, address: str) -> Order:
        support_type = SupportType(quote.support_type)
        # if config.get("LOCAL"):
        #     chain = "matic20-test"
        #     address = "******************************************"
        # else:
        chain = self.get_asset_chain(quote.asset)
        order_id = str(uuid.uuid4())
        data = {
            "appId": self._conf["app_id"],
            'network': chain.lower(),
            'coinCode': quote.asset.lower(),
            "merchantRecognitionId": order_id,
            "fiatType": self.fiat_type_map[quote.fiat]
        }
        if support_type == SupportType.BUY:
            tmp_data = {
                'walletAddress': address,
                'fiatAmount': quote.fiat_amount,
            }
        else:
            tmp_data = {
                'coinAmount': quote.asset_amount,
            }

        data.update(tmp_data)
        # DONE: offramp api with params reconfirming
        payment_url = self._conf['payment_url'][support_type.name.lower()]
        payment_url = url_join(payment_url, '', **data)

        return Order(
            id=order_id,
            payment_url=payment_url,
            extra={}
        )

    def get_order_list(self, create_time: datetime):
        buy_orders = self.get_buy_order_list(create_time)
        sell_orders = self.get_sell_order_list(create_time)
        return buy_orders + sell_orders

    def get_buy_order_list(self, create_time: datetime):
        endpoint = "/v1/transaction/merchantHistory"
        order_list = []
        for page in range(1, self.max_page_num + 1):
            body = {
                "page": page,
                "pageSize": 500,
                "order": "DESC",
                "since": create_time.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
            }
            data = self._request("post", endpoint, body)
            if not data:
                break
            order_list.extend(data)

        return order_list

    def get_sell_order_list(self, create_time: datetime):
        endpoint = "/v1/transaction/merchantHistory"
        order_list = []
        for page in range(1, self.max_page_num + 1):
            body = {
                "page": page,
                "pageSize": 500,
                "order": "DESC",
                "since": create_time.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
                "type": 2,
            }
            data = self._request("post", endpoint, body)
            if not data:
                break
            order_list.extend(data)

        return order_list

    def get_supported_fiats(self):
        # DONE: confirming supported fiats
        fiats = [*self.buy_fiats, *self.sell_fiats]
        return [currency.upper() for currency in list(set(fiats))]

    def get_supported_assets(self):
        # DONE: confirming supported offramp assets
        br = self._get_all_config(SupportType.BUY.name)
        sr = self._get_all_config(SupportType.SELL.name)
        r = list(set(br['allCoinConfig'].keys()) | set(sr['allCoinConfig'].keys()))
        return [currency.upper() for currency in r]

    def get_tx_info(self, order_detail, asset: str, order_type: FiatOrder.OrderType):
        """仅当订单完成时，才有 TX 信息"""
        chain = tx_id = tx_url = None
        if order_type is FiatOrder.OrderType.BUY:
            chain = self.get_asset_chain(asset)  # to ours
            tx_id = order_detail['transactionHash']
            cfg = self._get_all_config(SupportType.BUY.name)
            chain_cfg = cfg["networkConfig"]
            chain_id = f'{order_detail["chainId"]}'
            base_tx_url = chain_cfg.get(chain_id, {}).get('hashLink', '')
            if base_tx_url:
                tx_url = f'{base_tx_url}{tx_id}'
        else:
            pass
            # SELL tx hash 有问题，暂不处理
            # cfg = self._get_all_config(SupportType.SELL.name)
            # chain_cfg = cfg["networkConfig"]
            # chain_id = f'{order_detail["chainId"]}'
            # base_tx_url = chain_cfg.get(chain_id, {}).get('hashLink', '')
            # tx_id = order_detail['transactionHash']
            # if base_tx_url:
            #     tx_url = f'{base_tx_url}{tx_id}'
        return chain, tx_id, tx_url
