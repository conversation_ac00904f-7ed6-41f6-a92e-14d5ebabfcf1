#!/usr/bin/env python3
import uuid
from decimal import Decimal
from typing import Dict, List
from urllib.parse import urlencode

from requests.auth import HTT<PERSON><PERSON><PERSON><PERSON><PERSON>uth

from app import config
from app.business.fiat import Quote, Order
from app.business.fiat.base import BasePartnerClient, QuoteMethod, PaymentMethod, SupportType
from app.caches.fiat import FiatPriceCache
from app.utils import AmountType, RESTClient, amount_to_str, quantize_amount
from app.utils.files import AWSBucketPublic


class XanPoolClient(BasePartnerClient):
    # name = "XanPool"
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_xanpool_677.png')
    sell_assets = buy_assets = ["BTC", "ETH", "USDT"]
    buy_fiats = ["HKD", "INR", "MYR", "NZD", "VND", "THB", "IDR", "AUD", "PHP", "SGD"]
    sell_fiats = ["HKD", "VND", "THB", "MYR"]
    support_types = [SupportType.BUY, SupportType.SELL]
    sell_payment_methods = buy_payment_methods = [PaymentMethod.FPS, PaymentMethod.UPI, PaymentMethod.GOPAY,
                                                  PaymentMethod.DUITNOW]
    buy_quote_methods = [QuoteMethod.BY_ASSET, QuoteMethod.BY_FIAT]
    sell_quote_methods = [QuoteMethod.BY_ASSET, QuoteMethod.BY_FIAT]
    sell_fee_rate = buy_fee_rate = Decimal('0.035')
    min_fee = Decimal()

    buy_order_limit_min = Decimal(100)
    buy_order_limit_max = Decimal(800)
    sell_order_limit_max = Decimal(800)
    sell_order_limit_min = Decimal(100)
    daily_limit = Decimal(20000)
    monthly_limit = Decimal(50000)
    help_url = 'https://support.xanpool.com/hc/en-us/sections/360000379655-FAQs'

    network_map = {
        'USDT': 'TRC20'
    }

    _blockchain_map = {
        'ERC20': 'ethereum',
        'BSC': 'binance_smart_chain',
        'TRC20': 'tron'
    }

    def __init__(self):
        self._conf = config['XANPOOL_CONFIG']
        self._client = RESTClient(self._conf['url'], auth=HTTPBasicAuth(self._conf['apiKey'], self._conf['apiSecret']))

    def _handle_response(self, response_data):
        if error := response_data.get('error'):
            raise RESTClient.BadResponse(
                response_data.get("statusCode", -1),
                f"error={error}; message={response_data.get('message')}"
            )
        return response_data

    def get_prices(self):
        buy_data = self._get_price(self.buy_assets, self.buy_fiats, SupportType.BUY.value)
        sell_data = self._get_price(self.sell_assets, self.sell_fiats, SupportType.SELL.value)
        return {**buy_data, **sell_data}

    def _get_price(self, assets: List[str], fiats: List[str], support_type: str) -> Dict[str, str]:
        data = self._client.get('/prices', **{
            "type": support_type,
            "currencies": ",".join(fiats),
            "cryptoCurrencies": ",".join(assets)
        })
        return {
            f"{support_type}-{i['cryptoCurrency']}-{i['currency']}": amount_to_str(i["cryptoPrice"], 2) for i in data
        }

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        cache = FiatPriceCache(self.name.lower())
        return cache.get_price(asset, fiat, support_type=support_type)

    def fiat_to_asset_amount(self, fiat: str, asset: str, fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def quote(self, user, from_: str, to: str, amount: AmountType, support_type=SupportType.BUY) -> Quote:
        assets = getattr(self, f"{support_type.value}_assets")
        fiats = getattr(self, f"{support_type.value}_fiats")
        asset_amount = fiat_amount = Decimal()
        amount = Decimal(amount)
        if from_ in assets and to in fiats:
            asset = from_
            fiat = to
            asset_amount = amount
        elif from_ in fiats and to in assets:
            asset = to
            fiat = from_
            fiat_amount = amount
        else:
            raise ValueError(f"invalid asset and fiat `{from_}` `{to}`")

        r = self._client.post('/transactions/estimate', {
            "type": support_type.value,
            "cryptoCurrency": asset,
            "currency": fiat,
            "fiat": amount_to_str(fiat_amount),
            "crypto": amount_to_str(asset_amount)
        })
        price = Decimal(r['cryptoPrice'])
        if not fiat_amount:
            fiat_amount = quantize_amount(asset_amount * price, 2)
        if not asset_amount:
            asset_amount = quantize_amount(fiat_amount / price, self._asset_amount_precision(asset))

        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            asset_amount=asset_amount,
            fiat=fiat,
            fiat_amount=fiat_amount,
            price=amount_to_str(r['cryptoPrice'], 2),
            support_type=support_type
        )

    def place_order(self, user, quote: Quote, address: str) -> Order:
        order_id = str(uuid.uuid4())
        payment_id = str(uuid.uuid4())

        chain = self.get_asset_chain(quote.asset)
        network = self._blockchain_map.get(chain)
        data = {
            "apiKey": self._conf["apiKey"],
            "cryptoCurrency": quote.asset,
            "currency": quote.fiat,
            "wallet": address,
            "partnerData": payment_id,
            "redirectUrl": self._conf["redirectUrl"],
            "transactionType": quote.support_type,
            "isWebView": True,
            **({"chain": network} if network else {})
        }
        if quote.support_type == SupportType.SELL.value:
            data["crypto"] = quote.asset_amount
        else:
            data["fiat"] = quote.fiat_amount

        payment_url = f"{self._conf['payment_url']}?{urlencode(data)}"
        return Order(
            id=order_id,
            payment_url=payment_url,
            extra={
                "payment_id": payment_id
            }
        )

    def get_supported_fiats(self):
        data = self._client.get('/methods')
        currencies = data['buy']
        return [currency['currency'].upper() for currency in currencies]

    def get_supported_assets(self):
        data = self._client.get('/v2/cryptocurrencies')
        currencies = data['buy']
        return [currency['currency'].upper() for currency in currencies]
