import base64
import hashlib
import hmac
import json
import time
import uuid
from decimal import Decimal
from typing import Any, Dict, Optional
from urllib import parse
from urllib.parse import urlparse, urlencode

from .. import cached, PriceManager
from ...config import config
from ...models import User, FiatOrder
from ...utils import (AmountType, RESTClient, amount_to_str, quantize_amount, url_join, AWSBucketPublic, )
from .base import BasePartnerClient, Order, PaymentMethod, Quote, QuoteMethod, SupportType


class AlchemyPayClient(BasePartnerClient):
    name = 'AlchemyPay'
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_alchemypay.png')
    buy_assets = ['BTC', 'ETH', 'USDT', 'USDC', ]
    buy_fiats = ['PKR', 'OMR', 'MOP', 'MAD', 'KWD', 'GHS', 'CZK', 'BYN', 'BHD', 'SAR', 'QAR', 'CLP', 'ZAR', 'PEN',
                 'HUF', 'INR', 'PHP', 'SGD', 'COP', 'EUR', 'TRY', 'AED', 'MKD', 'HRK', 'NOK', 'MXN', 'BRL', 'THB',
                 'MYR', 'BGN', 'HKD', 'TWD', 'CAD', 'AUD', 'NZD', 'KZT', 'CHF', 'MDL', 'ILS', 'DKK', 'SEK', 'RON',
                 'PLN', 'KRW', 'GBP', 'USD', 'JPY', 'NGN', 'VND', 'IDR']
    sell_fiats = ['USD', 'EUR', 'ARS', 'AUD', 'BRL', 'CAD', 'CLP', 'COP', 'GBP', 'HKD', 'IDR', 'INR', 'KRW', 'LKR',
                  'MXN', 'MYR', 'PEN', 'PHP', 'SGD', 'THB', 'UYU', 'TRY', 'VND']
    sell_assets = ['BTC', 'ETH', 'USDT', 'USDC', ]
    support_types = [SupportType.BUY, SupportType.SELL]
    # TODO: logo required
    buy_payment_methods = [PaymentMethod.BANK, PaymentMethod.VISA, PaymentMethod.MASTER, PaymentMethod.APPLE_PAY]
    sell_payment_methods = [PaymentMethod.PAYPAL, PaymentMethod.SEPA, ]
    buy_quote_methods = [QuoteMethod.BY_FIAT]
    sell_quote_methods = [QuoteMethod.BY_ASSET]
    buy_fee_rate = Decimal('0.045')
    sell_fee_rate = Decimal('0.01')
    min_fee = Decimal()

    buy_order_limit_min = Decimal(100)
    buy_order_limit_max = Decimal(1500)
    sell_order_limit_max = Decimal(1900)
    sell_order_limit_min = Decimal(100)
    daily_limit = Decimal(2000)  # 300 ～ 2000
    monthly_limit = Decimal(50000)  # depends on kyc level and ramp type.
    help_url = 'https://support.alchemypay.org/hc/en-us'

    network_map = {
        'USDT': 'TRC20',
        'USDC': 'ERC20',
        'ETH': 'ERC20',
        'BTC': 'BTC',
    }  # to ours

    _blockchain_map = {
        'ERC20': 'ETH',
        'TRC20': 'TRX',
        'BTC': 'BTC'
    }  # ours to third

    def __init__(self):
        self._conf = config['ALCHEMYPAY_CONFIG']
        self._client = RESTClient(self._conf['url'], headers={
            'appid': self._conf['app_id'],
            'secret_key': self._conf['secret_key'],
        })

    def get_req(self, url: str, params: dict = None):
        params = params or {}
        c = RESTClient(self._conf['url'], headers=self._sign_headers(
            params,
            'GET',
            request_url=url_join(self._conf['url'], url),
        ))
        ret = c.get(url, **params)
        return ret

    def post_req(self, body: dict, url: str):
        c = RESTClient(self._conf['url'], headers=self._sign_headers(
            body,
            'POST',
            request_url=url_join(self._conf['url'], url),
        ))
        ret = c.post(url, json=body)
        return ret

    def _sign_headers(self, body: dict, method: str, request_url: str) -> dict:
        timestamp = str(int(time.time() * 1000))
        content = timestamp + method.upper() + self._get_path(request_url)
        if method == 'POST':
            content += self._get_json_body(json.dumps(body))
        else:
            content += f'?{self._get_s2s(body)}'
        key = self._conf['secret_key'].encode('utf-8')
        message = content.encode('utf-8')
        signature = hmac.new(key, message, hashlib.sha256).digest()
        signature_b64 = base64.b64encode(signature).decode('utf-8')
        return {
            'Content-Type': 'application/json',
            'appid': self._conf['app_id'],
            'timestamp': timestamp,
            'sign': signature_b64,
        }

    def _get_path(self, request_url):
        parsed_url = urlparse(request_url)
        path = parsed_url.path
        params = dict(parsed_url.query)
        if not params:
            return path
        sorted_params = {k: params[k] for k in sorted(params)}
        query_string = urlencode(sorted_params)
        return f'{path}?{query_string}'

    def _get_json_body(self, body):
        try:
            json_data = json.loads(body)
        except (json.JSONDecodeError, TypeError):
            json_data = {}
        if not json_data:
            return ''
        json_data = self._remove_empty_keys(json_data)
        return json.dumps(self._sort_object(json_data), separators=(',', ':'))

    def _remove_empty_keys(self, data):
        return {k: v for k, v in data.items() if v}

    def _sort_object(self, data):
        if isinstance(data, dict):
            return {k: self._sort_object(v) for k, v in sorted(data.items())}
        elif isinstance(data, list):
            data.sort(key=self._sort_object)
            return [self._sort_object(item) for item in data]
        else:
            return data

    def sign_payment_url(self, body: dict, support_type: SupportType) -> str:
        timestamp = str(int(time.time() * 1000))
        app_id = self._conf['app_id']
        body.update({
            'timestamp': timestamp,
            'appId': app_id,
        })
        s2s: str = self._get_s2s(params=body)
        if support_type is SupportType.BUY:
            msg = f'{timestamp}GET/index/rampPageBuy?{s2s}'
        else:
            msg = f'{timestamp}GET/index/rampPageSell?{s2s}'
        hmac_sign = self._page_hmac256(msg=msg)
        params = dict(body)
        params.update(sign=hmac_sign)
        real_s2s = self._get_s2s(params)
        return f'{self._conf["payment_url"]}?{real_s2s}'

    @staticmethod
    def _get_s2s(params: Dict[str, object]) -> str:
        if "sign" in params:
            params["sign"] = parse.quote_plus(params["sign"])
        s2s = '&'.join([f"{k}={v}" for k, v in sorted(params.items()) if v is not None and str(v)])
        return s2s

    def _page_hmac256(self, msg: str) -> str:
        data = hmac.new(self._conf['secret_key'].encode('utf-8'), msg.encode('utf-8'), hashlib.sha256).digest()
        return base64.b64encode(data).decode('utf-8')

    def _api_hmac256(self, msg: str) -> str:
        data = hmac.new(self._conf['secret_key'].encode(), msg.encode(), hashlib.sha256).digest()
        return data.hex().lower()

    @cached(300)
    def _get_price(self, asset: str, fiat: str, support_type: str) -> str:
        fiat_amount = getattr(self, f"{support_type}_order_limit_max") / 4
        if fiat != 'USD':
            fiat_amount = quantize_amount(fiat_amount / PriceManager.fiat_to_usd(fiat), 2)

        chain = self.get_asset_chain(asset)
        params = {
            "crypto": asset,
            "network": self._blockchain_map[chain],
            "fiat": fiat,
        }
        if support_type == SupportType.BUY.value:
            params.update({"side": SupportType.BUY.name, "amount": amount_to_str(fiat_amount, 2)})
        else:
            asset_usd = PriceManager.asset_to_usd(asset)
            amount = fiat_amount / asset_usd
            params.update({"side": SupportType.SELL.name, "amount": amount_to_str(amount, 8)})
        resp = self.post_req(params, url='/v4/merchant/order/quote')
        if not resp['data']:
            raise ValueError(f'{resp}')
        price = resp['data']['cryptoPrice']
        return amount_to_str(price, self._price_precision(asset))

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        price = self._get_price(asset, fiat, support_type.value)
        return Decimal(price)

    def fiat_to_asset_amount(self, fiat: str, asset: str,
                             fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def quote(self,
              user: User,
              from_: str,
              to: str,
              amount: AmountType,
              support_type=SupportType.BUY) -> Quote:
        assets = getattr(self, f"{support_type.value}_assets")
        fiats = getattr(self, f"{support_type.value}_fiats")
        asset_amount, fiat_amount = Decimal(), Decimal()
        if from_ in assets and to in fiats:
            asset, fiat = from_, to
            asset_amount = amount
        elif from_ in fiats and to in assets:
            asset, fiat = to, from_
            fiat_amount = amount
        else:
            raise ValueError(f'invalid asset and fiat `{from_}` `{to}`')

        chain = self.get_asset_chain(asset, user)
        params = {
            "crypto": asset,
            "network": self._blockchain_map[chain],
            "fiat": fiat,
            "amount": amount_to_str(amount),
            "side": support_type.name,
        }
        resp = self.post_req(params, url='/v4/merchant/order/quote')
        # quote_price = resp['data']['cryptoPrice']
        asset_amount = resp['data'].get('cryptoQuantity') or asset_amount
        fiat_amount = resp['data'].get('fiatQuantity') or fiat_amount
        price = Decimal(fiat_amount) / Decimal(asset_amount) if asset_amount else 0
        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            asset_amount=quantize_amount(asset_amount, self._asset_amount_precision(asset)),
            fiat=fiat,
            fiat_amount=quantize_amount(fiat_amount, 2),
            price=quantize_amount(price, 2),
            support_type=support_type,
        )

    def place_order(self,
                    user: User,
                    quote: Quote,
                    address: str) -> Order:
        support_type = SupportType(quote.support_type)
        chain = self.get_asset_chain(quote.asset)
        params = {
            "crypto": quote.asset,
            "network": self._blockchain_map[chain],
            "fiat": quote.fiat,
            "merchantOrderNo": quote.id
        }
        if support_type is SupportType.BUY:
            params.update({
                "fiatAmount": quote.fiat_amount,
                "showTable": support_type.value,
                "address": address,
            })
        else:
            params.update({
                "cryptoAmount": quote.asset_amount,
                "showTable": support_type.value,
            })
        payment_url = self.sign_payment_url(body=params, support_type=support_type)
        return Order(
            id=params['merchantOrderNo'],
            payment_url=payment_url,
            extra={}
        )

    def get_order(self, order_id: str, order_type: FiatOrder.OrderType) -> Optional[Dict[str, Any]]:
        params = {
            'merchantOrderNo': order_id,
            'side': order_type.name,
        }
        resp = self.get_req(url='/v4/merchant/query/trade', params=params)
        return resp['data'] if resp else None

    def get_supported_fiats(self):
        resp = self.get_req(url='/v4/merchant/fiat/list')
        return list({currency['currency'].upper() for currency in resp['data']})

    def get_supported_assets(self):
        resp = self.get_req(url='/v4/merchant/crypto/list')
        return list({currency['crypto'].upper() for currency in resp['data']})
