from decimal import Decimal
import json
from typing import Dict, Any
from itertools import product
from urllib.parse import urlencode
import uuid
from app.exceptions.basic import InvalidArgument

from app.utils import AWSBucketPublic, AmountType, quantize_amount, amount_to_str, g_map, RESTClient
from app.caches.fiat import FiatPriceCache
from app import config
from app.common import PrecisionEnum
from app.utils.push import WebPagePath
from .base import BasePartnerClient, Order, PaymentMethod, Quote, QuoteMethod, SupportType


class OnmetaClient(BasePartnerClient):

    name = 'Onmeta'
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_onmeta.png')
    sell_assets = ['ETH', 'USDT', 'USDC']
    buy_assets = ['ETH', 'USDT', 'USDC', 'BTC']
    sell_fiats = ['INR', ]
    buy_fiats = ['PHP', 'INR']
    support_types = [SupportType.BUY, SupportType.SELL]
    buy_payment_methods = sell_payment_methods = [
        PaymentMethod.UPI, PaymentMethod.IMPS, PaymentMethod.NEFT
    ]
    buy_quote_methods = [QuoteMethod.BY_FIAT]
    sell_quote_methods = [QuoteMethod.BY_ASSET, QuoteMethod.BY_FIAT]
    
    buy_fee_rate = Decimal('0.005')
    sell_fee_rate = Decimal('0.01')
    min_fee = Decimal()

    buy_order_limit_min = Decimal(20)
    buy_order_limit_max = Decimal(6000)
    sell_order_limit_max = Decimal(6000)
    sell_order_limit_min = Decimal(20)
    daily_limit = Decimal(2600)
    monthly_limit = Decimal(78000)
    help_url = 'https://onmeta.in/faq1'
    network_map = {
        'USDT': 'BSC', 
        'USDC': 'ERC20',
        'ETH': 'ERC20',
    }

    _blockchain_map = {
        'BSC': 'bsc',
        'TRC20': 'tron',
        'ERC20': 'ethereum',
    }

    _chain_id_map = {
        'BTC': 42162,
        'BSC': 56,
        'TRC20': 729,
        'ERC20': 1,
    }

    TOKEN_DECIMALS = 18

    DEFAULT_QUOTE_AMOUNT = Decimal('5000')

    def __init__(self):
        self._conf = config['ONMETA_CONFIG']
        self._client = RESTClient(self._conf['url'], headers={'x-api-key': self._conf['client_id']})
        

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        cache = FiatPriceCache(self.name.lower())
        return quantize_amount(cache.get_price(asset, fiat, support_type), 
                               self._price_precision(asset))

    def fiat_to_asset_amount(self, fiat: str, asset: str, fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def quote(self, user, from_: str, to: str, amount: AmountType, support_type: SupportType) -> Quote:
        assets = getattr(self, f"{support_type.value}_assets")
        fiats = getattr(self, f"{support_type.value}_fiats")
        asset_amount = fiat_amount = None
        if from_ in assets and to in fiats:
            asset, fiat = from_, to
            asset_amount = amount
        elif from_ in fiats and to in assets:
            asset, fiat = to, from_
            fiat_amount = amount
        else:
            raise
        
        _chain = self.get_asset_chain(asset)
        _chain_id = self._chain_id_map.get(_chain)
        if not _chain_id:
            raise
        if support_type is SupportType.BUY:
            params = dict(
                buyTokenSymbol=asset,
                chainId=_chain_id,
                fiatCurrency=fiat,
                fiatAmount=float(fiat_amount),
                # buyTokenAddress=_address,
            )
            resp = self._client.post('/v1/quote/buy', json=params)
            resp = self._process_response(resp)
            data = resp['data']
            asset_amount = Decimal(data['receivedTokens'] or '0')
            price = fiat_amount / asset_amount
        else:
            params = dict(
                sellTokenSymbol=asset,
                chainId=_chain_id,
                fiatCurrency=fiat,
                # fiatAmount=float(fiat_amount),
            )
            if fiat_amount:
                params['fiatAmount'] = float(fiat_amount)
            else:
                params['sellTokenAmount'] = float(asset_amount)
            resp = self._client.post('/v1/quote/sell', json=params)
            resp = self._process_response(resp)
            data = resp['data']
            asset_amount, fiat_amount = Decimal(data['sellTokens'] or '0'), Decimal(data['fiatAmount'] or '0')
            price = fiat_amount / asset_amount
        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            asset_amount=quantize_amount(asset_amount, self._asset_amount_precision(asset)),
            fiat=fiat,
            fiat_amount=quantize_amount(fiat_amount, 2),
            price=quantize_amount(price, self._price_precision(asset)),
            support_type=support_type
        )

    def place_order(self, user, quote: Quote, address: str) -> Order:
        
        support_type = SupportType(quote.support_type)
        _chain = self.get_asset_chain(quote.asset)
        _chain_id = self._chain_id_map.get(_chain)
        if not _chain_id:
            raise
        
        data = dict(
            elementId='widget',
            apiKey=self._conf['client_id'],
            # environment=self._conf['environment'],
            userEmail=user.main_user.email,
            fiatType=quote.fiat,
            fiatAmount=float(quote.fiat_amount),
            chainId=_chain_id,
            tokenSymbol=quote.asset,
            successRedirectUrl=WebPagePath.FIAT_HISTORY.value,
            metaData=json.dumps(dict(order_id=quote.id))
        )
        if support_type == SupportType.BUY:
            data.update(
                dict(
                    walletAddress=address,
                    offRamp='disabled'
                )
            )
        else:
            data.update(
                dict(
                    onRamp='disabled'
                )
            )
        basic_url = self._conf['widget_url']
        query_string = urlencode(data)
        return Order(
            id=quote.id,
            payment_url=f'{basic_url}?{query_string}',
            extra={}
        )

    def get_prices(self):

        def _get_prices(self_assets, self_fiats, support_type):
            assets = []
            fiats = []
            for asset, fiat in product(self_assets, self_fiats):
                assets.append(asset)
                fiats.append(fiat)
            support_types = [support_type] * len(assets)
            prices = g_map(self._get_price, assets, fiats, support_types, ordered=True, fail_safe=Decimal(), size=5)
            return {
                f"{support_type.value}-{asset}-{fiat}": amount_to_str(price, 2)
                for asset, fiat, price in zip(assets, fiats, prices) if price
            }

        buy_prices = _get_prices(self.buy_assets, self.buy_fiats, SupportType.BUY)
        sell_prices = _get_prices(self.sell_assets, self.sell_fiats, SupportType.SELL)
        return {**buy_prices, **sell_prices}

    
    def _process_response(self, resp: Dict[str, Any]) -> Dict[str, Any]:
        if (code:= resp.get('Code')) and code != 200:
            raise InvalidArgument(message=resp['Message'])
        return resp

    def _get_price(self, asset, fiat, support_type) -> str:
        _chain = self.get_asset_chain(asset)
        _chain_id = self._chain_id_map.get(_chain)
        if not _chain_id:
            return '0'
        if support_type is SupportType.BUY:
            params = dict(
                buyTokenSymbol=asset,
                chainId=_chain_id,
                fiatCurrency=fiat,
                fiatAmount=float(self.DEFAULT_QUOTE_AMOUNT),
                # buyTokenAddress=_address,
            )
            resp = self._client.post('/v1/quote/buy', json=params)
            resp = self._process_response(resp)
            data = resp['data']
            amount = Decimal(data['receivedTokens'] or '0')
            return amount_to_str(self.DEFAULT_QUOTE_AMOUNT / amount, 
                                 PrecisionEnum.PRICE_PLACES)
        else:
            params = dict(
                sellTokenSymbol=asset,
                # sellTokenAddress=_address,
                chainId=_chain_id,
                fiatCurrency=fiat,
                fiatAmount=float(self.DEFAULT_QUOTE_AMOUNT),
                # senderAddress='',
            )
            resp = self._client.post('/v1/quote/sell', json=params)
            resp = self._process_response(resp)
            data = resp['data']
            asset_amount, fiat_amount = data['sellTokens'] or '0', data['fiatAmount'] or '0'
            return amount_to_str(Decimal(fiat_amount) / Decimal(asset_amount),
                                 PrecisionEnum.PRICE_PLACES)
        


    def get_supported_assets(self):

        result = self._client.get('/v1/tokens')
        res = set()
        for item in result:
            res.add(item['symbol'])
        return list(res)