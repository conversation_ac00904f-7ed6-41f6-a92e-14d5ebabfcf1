from decimal import Decimal
from typing import Dict, Optional, Any
from itertools import product
from urllib.parse import urlparse, parse_qs
import uuid

from app.utils import AWSBucketPublic, AmountType, quantize_amount, amount_to_str, g_map, RESTClient
from app.caches.fiat import FiatPriceCache
from app import config
from app.common import PrecisionEnum
from .base import BasePartnerClient, Order, PaymentMethod, Quote, QuoteMethod, SupportType
from ...models import FiatOrder


class LoopipayClient(BasePartnerClient):

    # name = 'LoopiPay'
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_loopipay.png')
    sell_assets = ['ETH', 'USDT', 'USDC']
    buy_assets = ['BTC', 'ETH', 'USDT', 'USDC']
    sell_fiats = ['BRL']
    buy_fiats = ['BRL']
    support_types = [SupportType.BUY, SupportType.SELL]
    buy_payment_methods = sell_payment_methods = [
        PaymentMethod.PIX,
    ]
    buy_quote_methods = [QuoteMethod.BY_FIAT]
    sell_quote_methods = [QuoteMethod.BY_ASSET]
    
    buy_fee_rate = Decimal('0.0323')
    sell_fee_rate = Decimal('0.0042')
    min_fee = Decimal()

    buy_order_limit_min = Decimal(100)
    buy_order_limit_max = Decimal(1500)
    sell_order_limit_max = Decimal(900)
    sell_order_limit_min = Decimal(50)
    daily_limit = Decimal(2600)
    monthly_limit = Decimal(78000)
    help_url = 'https://loopipay.com/faq'
    network_map = {
        'USDT': 'ERC20', 
        'USDC': 'ERC20',
        'ETH': 'ERC20'
    }

    _blockchain_map = {
        'BSC': 'bsc',
        'TRC20': 'tron',
        'ERC20': 'ethereum',
    }

    TOKEN_DECIMALS = 18

    def __init__(self):
        self._conf = config['LOOPIPAY_CONFIG']
        secret = self._conf['secret']
        self._client = RESTClient(self._conf['url'], headers={'x-api-key': secret, 'User-Agent': 'PostmanRuntime/7.29.2'})
        

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        cache = FiatPriceCache(self.name.lower())
        return quantize_amount(cache.get_price(asset, fiat, support_type), 
                               self._price_precision(asset))

    def fiat_to_asset_amount(self, fiat: str, asset: str, fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def quote(self, user, from_: str, to: str, amount: AmountType, support_type: SupportType) -> Quote:
        assets = getattr(self, f"{support_type.value}_assets")
        fiats = getattr(self, f"{support_type.value}_fiats")
        asset_amount = fiat_amount = None
        if from_ in assets and to in fiats:
            asset, fiat = from_, to
            asset_amount = amount
        elif from_ in fiats and to in assets:
            asset, fiat = to, from_
            fiat_amount = amount

        chain = self.get_asset_chain(asset)
        if fiat_amount is not None:
            amount = quantize_amount(fiat_amount, PrecisionEnum.CASH_PLACES)
            fiat_quantity = fiat_amount.scaleb(PrecisionEnum.CASH_PLACES)
            params = dict(
                fiat=fiat,
                token=asset,
                fiatDecimals=str(PrecisionEnum.CASH_PLACES.value),
                fiatQuantity=amount_to_str(fiat_quantity, 0),
                network=self._blockchain_map[chain],
                paymentMethod=PaymentMethod.PIX.name,
                type='BUY'
            )
        else:
            amount = quantize_amount(asset_amount, self.TOKEN_DECIMALS)
            token_quantity = amount.scaleb(self.TOKEN_DECIMALS)
            params = dict(
                fiat=to,
                token=from_,
                tokenDecimals=str(self.TOKEN_DECIMALS),
                tokenQuantity=amount_to_str(token_quantity, 0),
                network=self._blockchain_map[chain],
                paymentMethod=PaymentMethod.PIX.name,
                type='SELL'
            )
        params.update(dict(
            clientId=self._conf['client_id'],
        ))
        r = self._client.get('/public/v1/quote/', **params)
        q = r['tokenPrice']['quantity']
        d = r['tokenPrice']['decimals']
        price = Decimal(q).scaleb(-int(d))

        q = r['token']['quantity']
        d = r['token']['decimals']
        asset_amount = Decimal(q).scaleb(-int(d))

        q = r['fiat']['quantity']
        d = r['fiat']['decimals']
        fiat_amount = Decimal(q).scaleb(-int(d))

        asset, fiat = params['token'], params['fiat']
        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            asset_amount=quantize_amount(asset_amount, self._asset_amount_precision(asset)),
            fiat=fiat,
            fiat_amount=quantize_amount(fiat_amount, 2),
            price=quantize_amount(price, self._price_precision(asset)),
            support_type=support_type
        )

    def place_order(self, user, quote: Quote, address: str) -> Order:
        data = {}
        support_type = SupportType(quote.support_type)
        chain = self.get_asset_chain(quote.asset)
        if support_type == SupportType.BUY:
            data.update(
                dict(
                    clientId='coinex',
                    fiat=quote.fiat,
                    fiatDecimals=str(PrecisionEnum.CASH_PLACES.value),
                    fiatQuantity=amount_to_str(Decimal(quote.fiat_amount).scaleb(PrecisionEnum.CASH_PLACES.value), 0),
                    network=self._blockchain_map[chain],
                    paymentMethod=PaymentMethod.PIX.name,
                    token=quote.asset,
                    type='BUY',
                    walletAddress=address,
                )
            )
        elif support_type == SupportType.SELL:
            data.update(
                dict(
                    clientId='coinex',
                    fiat=quote.fiat,
                    tokenDecimals=str(self.TOKEN_DECIMALS),
                    tokenQuantity=amount_to_str(Decimal(quote.asset_amount).scaleb(self.TOKEN_DECIMALS), 0),
                    network=self._blockchain_map[chain],
                    paymentMethod=PaymentMethod.PIX.name,
                    token=quote.asset,
                    type='SELL'
                )
            )

        r = self._client.get("/public/v1/partner/quote/url", **data)
        url = r['url']
        parsed_result = urlparse(url)
        query = parse_qs(parsed_result.query)
        return Order(
            id=query['externalId'][0],
            payment_url=url,
            extra={}
        )

    def get_prices(self):

        def _get_prices(self_assets, self_fiats, support_type):
            assets = []
            fiats = []
            for asset, fiat in product(self_assets, self_fiats):
                assets.append(asset)
                fiats.append(fiat)
            support_types = [support_type] * len(assets)
            prices = g_map(self._get_price, assets, fiats, support_types, ordered=True, fail_safe=Decimal(), size=5)
            return {
                f"{support_type.value}-{asset}-{fiat}": amount_to_str(price, 2)
                for asset, fiat, price in zip(assets, fiats, prices) if price
            }

        buy_prices = _get_prices(self.buy_assets, self.buy_fiats, SupportType.BUY)
        sell_prices = _get_prices(self.sell_assets, self.sell_fiats, SupportType.SELL)
        return {**buy_prices, **sell_prices}

    def _get_price(self, asset, fiat, support_type) -> Dict[str, str]:
        if support_type is SupportType.BUY:
            type_ = 'BUY'
        else:
            type_ = 'SELL'
        chain = self.get_asset_chain(asset)
        params = dict(
            fiat=fiat,
            token=asset,
            network=self._blockchain_map[chain],
            type=type_,
            clientId=self._conf['client_id'],
            paymentMethod=PaymentMethod.PIX.name
        )

        r = self._client.get('/public/v1/quote/token', **params)
        decimals = r['tokenPrice']['decimals']
        quantity = r['tokenPrice']['quantity']
        return Decimal(quantity).scaleb(-int(decimals))

    def get_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        r = self._client.get(f'/public/v1/orders/{order_id}')
        return r

    def get_tx_info(self, order_detail, asset: str, order_type: FiatOrder.OrderType):
        """仅当订单完成时，才有 TX 信息"""
        chain = None
        if order_type is FiatOrder.OrderType.BUY:
            chain = self.get_asset_chain(asset)  # to ours
        tx_id = order_detail['transaction']['hash']
        tx_url = order_detail['transaction']['explorerUrl']
        return chain, tx_id, tx_url
