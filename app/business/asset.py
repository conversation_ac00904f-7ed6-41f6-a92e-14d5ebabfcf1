from app.assets import asset_to_chains, get_asset_chain_config
from app.config import config


def set_asset_withdrawals_disabled_by_asset_liability(asset: str, disabled: bool, reason: str):
    """同时更改币种下所有链的withdrawals_disabled_by_risk_control设置"""
    from app.business import send_alert_notice
    asset_has_disabled = False
    for chain in asset_to_chains(asset):
        asset_chain_config = get_asset_chain_config(asset, chain)
        old_value = asset_chain_config.withdrawals_disabled_by_asset_liability
        asset_chain_config.withdrawals_disabled_by_asset_liability = disabled
        if old_value != disabled:
            AssetAlertHelper.deposit_withdrawal_risk_alert(asset, chain, 'withdrawals_disabled_by_asset_liability',
                                                           old_value, disabled, reason)
        if old_value:
            asset_has_disabled = True
    if asset_has_disabled and not disabled:
        url = config['ADMIN_CONTACTS']['asset_liability']
        msg = f'{asset}资产负债对账已平，已恢复提现。'
        send_alert_notice(msg, url)


class AssetAlertHelper:
    deposit_withdrawal_risk_alert_fields = {
        'deposits_disabled_by_accumulate_rc_incr': 'web风控累计充值关闭充值（环比涨幅）',
        'deposits_disabled_by_accumulate_rc_proportion': 'web风控累计充值关闭充值（流通量占比）',
        'withdrawals_disabled_by_asset_liability': 'web资产负债风控关闭提现',
        'withdrawals_disabled_by_accumulate_rc': 'web累计币种提现风控关闭提现',
        'deposits_disabled_by_rc_abnormal': 'web币种异常增发风控关闭充值',
        'withdrawals_disabled_by_rc_abnormal': 'web币种异常增发风控关闭提现',
    }

    @classmethod
    def deposit_withdrawal_risk_alert(cls, asset: str, chain: str, field: str, old_value: bool, value: bool,
                                      reason: str, op_user_id: int = None):
        from app.business import send_alert_notice, get_admin_user_name_map

        def get_value(v_):
            return '✅' if v_ else '❎'

        if field not in cls.deposit_withdrawal_risk_alert_fields:
            return
        if op_user_id:
            operator = get_admin_user_name_map([op_user_id]).get(op_user_id, str(op_user_id))
        else:
            operator = 'system'

        send_alert_notice(
            f'{asset}-{chain}设置更新：\n'
            f'{cls.deposit_withdrawal_risk_alert_fields[field]}: {get_value(old_value)} -> {get_value(value)} \n'
            f'操作人：{operator} \n'
            f'原因：{reason}',
            config["ADMIN_CONTACTS"]["asset_deposit_withdrawal_switch"]
        )
