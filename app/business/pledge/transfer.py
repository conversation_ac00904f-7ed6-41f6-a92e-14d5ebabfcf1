# -*- coding: utf-8 -*-
from decimal import Decimal
from typing import Dict, Optional, List
from functools import cached_property

from flask import current_app

from app.models import db
from app.models.pledge import PledgeTransferHistory
from app.business import SPOT_ACCOUNT_ID, ServerClient, BalanceBusiness, ServerResponseCode
from .helper import get_loan_asset_info
from app.utils import now


TRANSFER_TYPE_BUSINESS_MAP = {
    # 划转记录类型: { from: 转入账户流水类型, to: 转出账户流水类型  }
    PledgeTransferHistory.Type.ADD_COLLATERAL: {
        "from": BalanceBusiness.PLEDGE_ASSET_LOCK,
        "to": BalanceBusiness.PLEDGE_ASSET_LOCK,
    },
    PledgeTransferHistory.Type.REMOVE_COLLATERAL: {
        "from": BalanceBusiness.PLEDGE_ASSET_RELEASE,
        "to": BalanceBusiness.PLEDGE_ASSET_RELEASE,
    },
    PledgeTransferHistory.Type.SEND_BORROWING: {
        "from": None,
        "to": BalanceBusiness.PLEDGE_LOAN_ASSET_ADD,
    },
    PledgeTransferHistory.Type.SPOT_ACCOUNT_REPAY: {
        "from": BalanceBusiness.PLEDGE_REPAY,
        "to": None,
    },
    PledgeTransferHistory.Type.PLEDGE_ACCOUNT_REPAY: {
        "from": BalanceBusiness.PLEDGE_REPAY,
        "to": None,
    },
    PledgeTransferHistory.Type.PLEDGE_ACCOUNT_PARTIAL_REPAY: {
        "from": BalanceBusiness.PLEDGE_REPAY,
        "to": None,
    },
    PledgeTransferHistory.Type.PLEDGE_ACCOUNT_LIQ_REPAY: {
        "from": BalanceBusiness.PLEDGE_LIQ,
        "to": None,
    },
    PledgeTransferHistory.Type.REFUND_REMAIN_PLEDGE: {
        "from": BalanceBusiness.PLEDGE_ASSET_RELEASE,
        "to": BalanceBusiness.PLEDGE_ASSET_RELEASE,
    },
    PledgeTransferHistory.Type.LIQ_FEE: {
        "from": BalanceBusiness.PLEDGE_LIQ_FEE,
        "to": None,
    },
}


class TransferHelper:
    def __init__(self, user_id: int, loan_asset: str):
        self.user_id = user_id
        self.loan_asset = loan_asset

    @cached_property
    def pledge_account_id(self) -> int:
        return get_loan_asset_info(self.loan_asset).account_id

    def new_add_pledge_trans(
        self,
        asset_amount_dict: Dict[str, Decimal],
        position_id: Optional[int] = None,
    ) -> List[PledgeTransferHistory]:
        """ 增加质押资产的划转 """
        rows = []
        for asset, amount in asset_amount_dict.items():
            row = PledgeTransferHistory(
                user_id=self.user_id,
                loan_asset=self.loan_asset,
                position_id=position_id,
                type=PledgeTransferHistory.Type.ADD_COLLATERAL,
                from_account_id=SPOT_ACCOUNT_ID,
                to_account_id=self.pledge_account_id,
                asset=asset,
                amount=amount,
            )
            db.session.add(row)
            rows.append(row)
        return rows

    def new_remove_pledge_tran(
        self,
        asset: str,
        amount: Decimal,
        position_id: int,
    ) -> PledgeTransferHistory:
        """ 减少质押资产的划转 """
        row = PledgeTransferHistory(
            user_id=self.user_id,
            loan_asset=self.loan_asset,
            position_id=position_id,
            type=PledgeTransferHistory.Type.REMOVE_COLLATERAL,
            from_account_id=self.pledge_account_id,
            to_account_id=SPOT_ACCOUNT_ID,
            asset=asset,
            amount=amount,
        )
        db.session.add(row)
        return row

    def new_send_borrowing_tran(self, position_id: int, amount: Decimal) -> PledgeTransferHistory:
        """ 增加借币币种的划转 """
        row = PledgeTransferHistory(
            user_id=self.user_id,
            loan_asset=self.loan_asset,
            position_id=position_id,
            type=PledgeTransferHistory.Type.SEND_BORROWING,
            from_account_id=None,
            to_account_id=SPOT_ACCOUNT_ID,
            asset=self.loan_asset,
            amount=amount,
        )
        db.session.add(row)
        return row

    def new_spot_account_repay_tran(self, position_id: int, repay_amount: Decimal) -> PledgeTransferHistory:
        """ 现货账户还币-划转 """
        row = PledgeTransferHistory(
            user_id=self.user_id,
            loan_asset=self.loan_asset,
            position_id=position_id,
            type=PledgeTransferHistory.Type.SPOT_ACCOUNT_REPAY,
            from_account_id=SPOT_ACCOUNT_ID,
            to_account_id=None,
            asset=self.loan_asset,
            amount=repay_amount,
        )
        db.session.add(row)
        return row

    def new_pledge_account_repay_tran(
        self,
        position_id: int,
        repay_amount: Decimal,
        type_: PledgeTransferHistory.Type,
    ) -> PledgeTransferHistory:
        """ 质押账户还币-划转 """
        assert type_ in [
            PledgeTransferHistory.Type.PLEDGE_ACCOUNT_REPAY,
            PledgeTransferHistory.Type.PLEDGE_ACCOUNT_LIQ_REPAY,
            PledgeTransferHistory.Type.PLEDGE_ACCOUNT_PARTIAL_REPAY
        ]
        row = PledgeTransferHistory(
            user_id=self.user_id,
            loan_asset=self.loan_asset,
            position_id=position_id,
            type=type_,
            from_account_id=self.pledge_account_id,
            to_account_id=None,
            asset=self.loan_asset,
            amount=repay_amount,
        )
        db.session.add(row)
        return row

    def new_refund_trans(self, position_id: int, asset_amount_dict: Dict[str, Decimal]) -> List[PledgeTransferHistory]:
        """ 质押账户余额退回的划转 """
        rows = []
        for asset, amount in asset_amount_dict.items():
            row = PledgeTransferHistory(
                user_id=self.user_id,
                loan_asset=self.loan_asset,
                position_id=position_id,
                type=PledgeTransferHistory.Type.REFUND_REMAIN_PLEDGE,
                from_account_id=self.pledge_account_id,
                to_account_id=SPOT_ACCOUNT_ID,
                asset=asset,
                amount=amount,
            )
            db.session.add(row)
            rows.append(row)
        return rows

    @classmethod
    def batch_do_add_pledge_trans(cls, add_pledge_trans: List[PledgeTransferHistory]):
        """ 执行批量增加质押币的划转，依次处理，先扣后加，某条失败时，后面的都改成失败 """
        assert add_pledge_trans and len({i.position_id for i in add_pledge_trans}) == 1
        fail_exception = None
        for r in add_pledge_trans:
            if fail_exception is None:
                try:
                    cls.do_single_transfer(r, on_finished_commit=True)
                except Exception as e:
                    fail_exception = e
            else:
                r.status = PledgeTransferHistory.Status.FAILED
                db.session.commit()
        if fail_exception:
            raise fail_exception

    @classmethod
    def do_single_transfer(cls, row: PledgeTransferHistory, on_finished_commit: bool = True):
        """ 执行单条划转 """
        type_enum = PledgeTransferHistory.Type
        if row.type in [
            type_enum.ADD_COLLATERAL,
            type_enum.REMOVE_COLLATERAL,
            type_enum.REFUND_REMAIN_PLEDGE,
        ]:
            return cls.do_deduct_and_add_transfer(row, on_finished_commit)
        elif row.type in [
            type_enum.SEND_BORROWING,
        ]:
            return cls.do_only_add_transfer(row, on_finished_commit)
        elif row.type in [
            type_enum.SPOT_ACCOUNT_REPAY,
            type_enum.PLEDGE_ACCOUNT_REPAY,
            type_enum.PLEDGE_ACCOUNT_PARTIAL_REPAY,
            type_enum.PLEDGE_ACCOUNT_LIQ_REPAY,
            type_enum.LIQ_FEE,
        ]:
            return cls.do_only_deduct_transfer(row, on_finished_commit)
        else:
            raise ValueError(f"not support {row.type.name} transfer")

    @classmethod
    def do_deduct_and_add_transfer(cls, row: PledgeTransferHistory, on_finished_commit: bool):
        """ 执行单条划转（先扣后加）"""
        assert row.from_account_id is not None and row.to_account_id is not None
        client = ServerClient()
        business_map = TRANSFER_TYPE_BUSINESS_MAP[row.type]
        from_business = business_map["from"]
        to_business = business_map["to"]
        assert from_business and to_business
        if row.status == PledgeTransferHistory.Status.CREATED:
            try:
                result = client.add_user_balance(
                    user_id=row.user_id,
                    asset=row.asset,
                    amount=str(-row.amount),
                    business=from_business,
                    business_id=row.id,
                    detail={"remark": f"pledge for transfer {row.id}"},
                    account_id=row.from_account_id,
                )
                if not result:
                    current_app.logger.error(
                        f"pledge_do_deduct_and_add_transfer {row.id} {row.type.name} {row.asset} {row.amount} "
                        f"from {row.from_account_id} to {row.to_account_id} deduct DUPLICATE_BALANCE_UPDATE"
                    )
            except Exception as e:
                current_app.logger.error(
                    f"pledge_do_deduct_and_add_transfer {row.id} {row.type.name} {row.asset} {row.amount} "
                    f"from {row.from_account_id} to {row.to_account_id} failed {e!r}"
                )
                if getattr(e, 'code', None) == ServerResponseCode.INSUFFICIENT_BALANCE:
                    row.status = PledgeTransferHistory.Status.FAILED
                    db.session.commit()
                raise
            row.status = PledgeTransferHistory.Status.DEDUCTED
            row.deducted_at = now()
            db.session.commit()

        if row.status == PledgeTransferHistory.Status.DEDUCTED:
            result = client.add_user_balance(
                user_id=row.user_id,
                asset=row.asset,
                amount=str(row.amount),
                business=to_business,
                business_id=row.id,
                detail={"remark": f"pledge for transfer {row.id}"},
                account_id=row.to_account_id,
            )
            if not result:
                current_app.logger.error(
                    f"pledge_do_deduct_and_add_transfer {row.id} {row.type.name} {row.asset} {row.amount} "
                    f"from {row.from_account_id} to {row.to_account_id} add DUPLICATE_BALANCE_UPDATE"
                )
            row.status = PledgeTransferHistory.Status.FINISHED
            row.finished_at = now()
            if on_finished_commit:
                db.session.commit()

    @classmethod
    def do_only_deduct_transfer(cls, row: PledgeTransferHistory, on_finished_commit: bool):
        """ 执行单条划转（只扣减余额）"""
        assert row.from_account_id is not None and row.to_account_id is None
        client = ServerClient()
        if row.status == PledgeTransferHistory.Status.CREATED:
            from_business = TRANSFER_TYPE_BUSINESS_MAP[row.type]["from"]
            assert from_business
            try:
                result = client.add_user_balance(
                    user_id=row.user_id,
                    asset=row.asset,
                    amount=str(-row.amount),
                    business=from_business,
                    business_id=row.id,
                    detail={"remark": f"pledge for transfer {row.id}"},
                    account_id=row.from_account_id,
                )
                if not result:
                    current_app.logger.error(
                        f"pledge_do_only_deduct_transfer {row.id} {row.type.name} {row.asset} {row.amount} "
                        f"from_account_id {row.from_account_id} deduct DUPLICATE_BALANCE_UPDATE"
                    )
            except Exception as e:
                current_app.logger.error(
                    f"pledge_do_only_deduct_transfer {row.id} {row.type.name} {row.asset} {row.amount} "
                    f"from_account_id {row.from_account_id} failed {e!r}"
                )
                if getattr(e, 'code', None) == ServerResponseCode.INSUFFICIENT_BALANCE:
                    row.status = PledgeTransferHistory.Status.FAILED
                    db.session.commit()
                raise
            row.status = PledgeTransferHistory.Status.FINISHED
            row.deducted_at = row.finished_at = now()
            if on_finished_commit:
                db.session.commit()

    @classmethod
    def do_only_add_transfer(cls, row: PledgeTransferHistory, on_finished_commit: bool):
        """ 执行单条划转（只增加余额）"""
        assert row.from_account_id is None and row.to_account_id is not None
        client = ServerClient()
        if row.status == PledgeTransferHistory.Status.CREATED:
            to_business = TRANSFER_TYPE_BUSINESS_MAP[row.type]["to"]
            assert to_business
            try:
                result = client.add_user_balance(
                    user_id=row.user_id,
                    asset=row.asset,
                    amount=str(row.amount),
                    business=to_business,
                    business_id=row.id,
                    detail={"remark": f"pledge for transfer {row.id}"},
                    account_id=row.to_account_id,
                )
                if not result:
                    current_app.logger.error(
                        f"pledge_do_only_add_transfer {row.id} {row.type.name} {row.asset} {row.amount} "
                        f"to_account_id {row.to_account_id} add DUPLICATE_BALANCE_UPDATE"
                    )
            except Exception as e:
                current_app.logger.error(
                    f"pledge_do_only_add_transfer {row.id} {row.type.name} {row.asset} {row.amount} "
                    f"to_account_id {row.to_account_id} failed {e!r}"
                )
                raise
            row.finished_at = now()
            row.status = PledgeTransferHistory.Status.FINISHED
            if on_finished_commit:
                db.session.commit()
