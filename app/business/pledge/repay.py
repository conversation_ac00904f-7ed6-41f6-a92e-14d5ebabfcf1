# -*- coding: utf-8 -*-
import json
from decimal import Decima<PERSON>
from typing import Op<PERSON>, Tu<PERSON>, List
from datetime import timed<PERSON><PERSON>

from flask import current_app

from app.business.pledge.loan import PledgeOperator
from app.exceptions import InsufficientBalance, PledgePositionOperationFailed
from app.models import db, LendableAssetChangeHistory, MarginInsurance
from app.models.pledge import (
    LoanAsset,
    PledgeRepayHistory,
    PledgePosition,
    PledgeExchangeHistory,
    PledgeTransferHistory,
    PledgeInsuranceHistory,
    PledgeRealInsuranceHistory,
    PledgeLiquidationWhitelistUser,
    PledgeLoanOrder,
    PledgeLoanOrderRenewHistory,
)
from app.common import PrecisionEnum, CeleryQueues
from app.business import ServerClient, SPOT_ACCOUNT_ID, CacheLock, LockKeys, lock_call, SiteSettings
from app.business.lendable import LendableAmountProcessor
from app.assets import try_get_asset_config
from .helper import get_loan_asset_info, get_user_last_active_position
from .transfer import <PERSON><PERSON><PERSON>per
from .position import Position<PERSON>anager, on_position_finished, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .exchange import execute_position_exchange_task
from .notice import (
    pledge_close_repay_finished_notice,
    pledge_loan_order_renew_success_notice,
    pledge_loan_order_renew_fail_notice,
    pledge_loan_order_force_repay_notice,
)
from app.utils import quantize_amount, celery_task, now, route_module_to_celery_queue
from app.utils.parser import JsonEncoder


route_module_to_celery_queue(__name__, CeleryQueues.PLEDGE)


class SpotAccountRepayHelper:
    """ 现货账户的借币币种来还币 """
    def __init__(self, position: PledgePosition):
        self.position = position
        self.tran_helper = TransferHelper(self.position.user_id, self.position.loan_asset)

    def check_balance(self, require_amount: Decimal):
        asset = self.position.loan_asset
        balance = ServerClient().get_user_balances(self.position.user_id, asset=asset, account_id=SPOT_ACCOUNT_ID)
        amount = balance.get(asset, {}).get("available", Decimal())
        if amount < require_amount:
            raise InsufficientBalance

    def repay(self, repay_amount: Decimal):
        """ 未还清则减仓，全部还清则平仓 """
        if not self.position.is_repayable():
            raise PledgePositionOperationFailed
        assert Decimal() < repay_amount <= self.position.total_unflat_amount
        self.check_balance(repay_amount)

        repay_tran = self.tran_helper.new_spot_account_repay_tran(self.position.id, repay_amount)
        db.session.commit()
        self.execute_repay_by_tran(repay_tran)

    def execute_repay_by_tran(self, repay_tran: PledgeTransferHistory):
        from app.business.pledge.liquidation import PositionLiquidator

        if repay_tran.is_finished:
            return  # 重复调用

        self.tran_helper.do_single_transfer(repay_tran, on_finished_commit=False)
        assert repay_tran.is_finished

        position = self.position
        pre_is_arrears = position.status == PledgePosition.Status.ARREARS
        reduce_debt_amount, reduce_interest_amount = PositionManager.reduce_position(
            position,
            repay_tran.amount,
            repay_type=PledgeRepayHistory.Type.REPAY_BY_LOAN,
        )
        if position.total_unflat_amount == Decimal():
            PositionManager.set_position_finished(position, PledgePosition.FinishType.REPAY_BY_SPOT)
        self.new_repay_history(self.position, reduce_debt_amount, reduce_interest_amount)
        LendableAmountProcessor(
            self.position.loan_asset,
            reduce_debt_amount,
            LendableAssetChangeHistory.BusinessType.PLEDGE_FLAT,
        ).process_new_record()  # inner committed
        db.session.commit()

        if position.is_finished():
            on_position_finished(position.id)
            if pre_is_arrears:
                PositionLiquidator.remove_user_limit(position.user_id)
            RefundHelper.refund(position)  # 全部还清时质押账户币种的退回

    @classmethod
    def new_repay_history(
        cls,
        position: PledgePosition,
        repay_debt_amount: Decimal,
        repay_interest_amount: Decimal,
        repay_type: PledgeRepayHistory.Type = PledgeRepayHistory.Type.REPAY_BY_LOAN,
    ) -> PledgeRepayHistory:
        row = PledgeRepayHistory(
            user_id=position.user_id,
            position_id=position.id,
            loan_asset=position.loan_asset,
            type=repay_type,
            exchange_id=None,
            repay_debt_amount=repay_debt_amount,
            repay_interest_amount=repay_interest_amount,
            used_pledge_data=None,
            status=PledgeRepayHistory.Status.FINISHED,
        )
        db.session.add(row)
        return row


class PledgeAccountRepayHelper:
    """ 质押账户还币 """

    SUPPORT_REPAY_TYPE_DICT = {
        PledgeRepayHistory.Type.REPAY_BY_PLEDGE: PledgeExchangeHistory.Type.REPAY,
        PledgeRepayHistory.Type.PARTIAL_REPAY_BY_PLEDGE: PledgeExchangeHistory.Type.PARTIAL_REPAY,
        PledgeRepayHistory.Type.SYS_REPAY_BY_PLEDGE: PledgeExchangeHistory.Type.REPAY,
        PledgeRepayHistory.Type.LIQ: PledgeExchangeHistory.Type.LIQ,
    }

    SUPPORT_REPAY_TYPE_TRAN_DICT = {
        PledgeRepayHistory.Type.REPAY_BY_PLEDGE: PledgeTransferHistory.Type.PLEDGE_ACCOUNT_REPAY,
        PledgeRepayHistory.Type.PARTIAL_REPAY_BY_PLEDGE: PledgeTransferHistory.Type.PLEDGE_ACCOUNT_PARTIAL_REPAY,
        PledgeRepayHistory.Type.SYS_REPAY_BY_PLEDGE: PledgeTransferHistory.Type.PLEDGE_ACCOUNT_REPAY,
        PledgeRepayHistory.Type.LIQ: PledgeTransferHistory.Type.PLEDGE_ACCOUNT_LIQ_REPAY,
    }

    def __init__(self, position: PledgePosition, repay_type: PledgeRepayHistory.Type):
        assert repay_type in self.SUPPORT_REPAY_TYPE_DICT
        self.position = position
        self.repay_type = repay_type
        #
        self.exc_type = self.SUPPORT_REPAY_TYPE_DICT[repay_type]
        self.tran_type = self.SUPPORT_REPAY_TYPE_TRAN_DICT[repay_type]

    def start_close_repay(self):
        """ 用质押币种还币（主动平仓），修改仓位状态 开始兑换交易 """
        if not self.position.is_borrowing():
            raise PledgePositionOperationFailed

        self.new_exchange_and_repay_history()
        self.position.status = PledgePosition.Status.REPAYING
        borrowing_loan_orders = LoanOrderHelper.get_pos_borrowing_loan_orders(self.position.id)
        for loan_order in borrowing_loan_orders:
            loan_order.status = PledgeLoanOrder.Status.REPAYING
        db.session.commit()
        execute_position_exchange_task.delay(self.position.id)

    def new_exchange_and_repay_history(self, exchange_detail: str = None) -> Tuple[PledgeExchangeHistory, PledgeRepayHistory]:
        exc_his = PledgeExchangeHistory(
            user_id=self.position.user_id,
            position_id=self.position.id,
            type=self.exc_type,
            exchange_detail=exchange_detail
        )
        db.session.add(exc_his)
        db.session.flush()
        repay_his = PledgeRepayHistory(
            user_id=self.position.user_id,
            position_id=self.position.id,
            loan_asset=self.position.loan_asset,
            type=self.repay_type,
            exchange_id=exc_his.id,
            repay_debt_amount=0,
            repay_interest_amount=0,
            used_pledge_data=None,
        )
        db.session.add(repay_his)
        return exc_his, repay_his

    def try_terminate_close_repay(self) -> Optional[PledgeExchangeHistory]:
        """ 尝试终止<用质押币>主动还币的流程 """
        position = self.position
        if position.status != PledgePosition.Status.REPAYING:
            return
        exc_his: PledgeExchangeHistory = PledgeExchangeHistory.query.filter(
            PledgeExchangeHistory.user_id == position.user_id,
            PledgeExchangeHistory.position_id == position.id,
            PledgeExchangeHistory.type == PledgeExchangeHistory.Type.REPAY,
        ).first()
        if exc_his and exc_his.status == PledgeExchangeHistory.Status.RUNNING:
            exc_his.status = PledgeExchangeHistory.Status.FINISHED
            exc_his.finished_at = now()

        repay_his: PledgeRepayHistory = PledgeRepayHistory.query.filter(
            PledgeRepayHistory.user_id == position.user_id,
            PledgeRepayHistory.position_id == position.id,
            PledgeRepayHistory.type.in_(
                [PledgeRepayHistory.Type.REPAY_BY_PLEDGE, PledgeRepayHistory.Type.SYS_REPAY_BY_PLEDGE]
            ),
        ).first()
        if repay_his and repay_his.status == PledgeRepayHistory.Status.RUNNING:
            repay_his.status = PledgeRepayHistory.Status.TERMINATED
        return exc_his

    @classmethod
    def has_close_repay_tran(cls, position_id: int) -> bool:
        """ 是否有还币划转，当<主动还币-用质押币> 兑换完成，并写入了还币划转记录后，不再强平 """
        repay_tran: PledgeTransferHistory = PledgeTransferHistory.query.filter(
            PledgeTransferHistory.position_id == position_id,
            PledgeTransferHistory.type == PledgeTransferHistory.Type.PLEDGE_ACCOUNT_REPAY,
        ).with_entities(
            PledgeTransferHistory.id,
        ).first()
        return bool(repay_tran)

    #
    def new_repay_tran(self, repay_amount: Decimal) -> PledgeTransferHistory:
        position = self.position
        assert Decimal() < repay_amount <= position.total_unflat_amount
        tran_helper = TransferHelper(position.user_id, position.loan_asset)
        repay_tran = tran_helper.new_pledge_account_repay_tran(position.id, repay_amount, self.tran_type)
        return repay_tran

    def query_repay_tran(self) -> Optional[PledgeTransferHistory]:
        repay_tran: PledgeTransferHistory = PledgeTransferHistory.query.filter(
            PledgeTransferHistory.position_id == self.position.id,
            PledgeTransferHistory.type == self.tran_type,
        ).first()
        return repay_tran

    def query_repay_his(self) -> Optional[PledgeRepayHistory]:
        repay_his: PledgeRepayHistory = PledgeRepayHistory.query.filter(
            PledgeRepayHistory.position_id == self.position.id,
            PledgeRepayHistory.type == self.repay_type,
        ).first()
        return repay_his

    def query_exchange_his(self) -> Optional[PledgeExchangeHistory]:
        exc_his: PledgeExchangeHistory = PledgeExchangeHistory.query.filter(
            PledgeExchangeHistory.position_id == self.position.id,
            PledgeExchangeHistory.type == self.exc_type,
        ).order_by(PledgeExchangeHistory.id.desc()).first()
        return exc_his

    def execute_repay_by_tran(self, repay_tran: PledgeTransferHistory):
        """ 用质押账户的借币币种来还币，兑换完成后调用 """
        if repay_tran.is_finished:
            return  # 重复调用

        TransferHelper.do_single_transfer(repay_tran, on_finished_commit=False)
        assert repay_tran.is_finished

        position = self.position
        repay_his = self.query_repay_his()
        reduce_debt_amount, reduce_interest_amount = PositionManager.reduce_position(
            position,
            repay_tran.amount,
            repay_type=self.repay_type,
        )
        repay_his.repay_debt_amount = reduce_debt_amount
        repay_his.repay_interest_amount = reduce_interest_amount
        used_pledge_data = json.dumps(self.query_exchange_his().get_used_pledge_data(), cls=JsonEncoder)
        repay_his.used_pledge_data = used_pledge_data
        repay_his.status = PledgeRepayHistory.Status.FINISHED
        LendableAmountProcessor(
            position.loan_asset,
            reduce_debt_amount,
            LendableAssetChangeHistory.BusinessType.PLEDGE_FLAT,
        ).process_new_record()  # inner committed
        db.session.commit()


class FundRepayHelper:
    """ 保险基金还币 """

    @classmethod
    def repay(cls, position: PledgePosition, reduce_debt_amount: Decimal, reduce_interest_amount: Decimal):
        """ 强平穿仓，保险基金垫付 """
        fund_repay_his = PledgeRepayHistory(
            user_id=position.user_id,
            position_id=position.id,
            loan_asset=position.loan_asset,
            type=PledgeRepayHistory.Type.FUND,
            exchange_id=None,
            repay_debt_amount=reduce_debt_amount,
            repay_interest_amount=reduce_interest_amount,
            used_pledge_data=None,
            status=PledgeRepayHistory.Status.FINISHED,  # 无需划转操作，只修改基金余额，状态是已完成
        )
        db.session.add(fund_repay_his)

        total_repay_amount = reduce_debt_amount + reduce_interest_amount
        cls.liq_repay_and_fund_record(position.loan_asset, total_repay_amount)

    @classmethod
    def liq_repay_and_fund_record(cls, asset: str, repay_amount: Decimal):
        """ 更新保险基金，写入流水 """
        asset_fund_row = MarginInsurance.query.filter(MarginInsurance.asset == asset).first()
        MarginInsurance.query.filter(
            MarginInsurance.id == asset_fund_row.id,
        ).update(
            {
                MarginInsurance.amount: MarginInsurance.amount - repay_amount,
                MarginInsurance.real_amount: MarginInsurance.real_amount - repay_amount,
            }
        )
        fund_his = PledgeInsuranceHistory(
            amount=-repay_amount,
            asset=asset,
            balance=asset_fund_row.amount,
            history_type=PledgeInsuranceHistory.HistoryType.PLEDGE_LIQUIDATION,
        )
        real_fund_his = PledgeRealInsuranceHistory(
            amount=-repay_amount,
            asset=asset,
            balance=asset_fund_row.real_amount,
            history_type=PledgeRealInsuranceHistory.HistoryType.PLEDGE_LIQUIDATION,
        )
        db.session.add(fund_his)
        db.session.add(real_fund_his)

    @classmethod
    def liq_fee_and_fund_record(cls, fee_asset: str, fee_amount: Decimal):
        """ 强平清算费转入对应币种的杠杆保险基金 """
        fund_asset_row = MarginInsurance.query.filter(MarginInsurance.asset == fee_asset).first()
        if not fund_asset_row:
            # 强平清算费可能是质押币种
            return

        MarginInsurance.query.filter(
            MarginInsurance.id == fund_asset_row.id,
        ).update(
            {
                MarginInsurance.amount: MarginInsurance.amount + fee_amount,
                MarginInsurance.real_amount: MarginInsurance.real_amount + fee_amount,
            }
        )
        fund_his = PledgeInsuranceHistory(
            amount=fee_amount,
            balance=fund_asset_row.amount,
            asset=fee_asset,
            history_type=PledgeInsuranceHistory.HistoryType.PLEDGE_LIQUIDATION_INCOME,
        )
        real_fund_his = PledgeRealInsuranceHistory(
            amount=fee_amount,
            balance=fund_asset_row.real_amount,
            asset=fund_asset_row.asset,
            history_type=PledgeRealInsuranceHistory.HistoryType.PLEDGE_LIQUIDATION_INCOME,
        )
        db.session.add(fund_his)
        db.session.add(real_fund_his)


class RefundHelper:
    """ 退回质押账户的剩余币种 相关逻辑 """

    @classmethod
    def query_refund_trans(cls, position_id: int) -> List[PledgeTransferHistory]:
        refund_trans: List[PledgeTransferHistory] = PledgeTransferHistory.query.filter(
            PledgeTransferHistory.position_id == position_id,
            PledgeTransferHistory.type == PledgeTransferHistory.Type.REFUND_REMAIN_PLEDGE,
        ).all()
        return refund_trans

    @classmethod
    def refund(cls, position: PledgePosition) -> bool:
        assert position.total_unflat_amount == Decimal()
        refund_trans = cls.query_refund_trans(position.id)
        tf_helper = TransferHelper(position.user_id, position.loan_asset)
        if not refund_trans:
            account_id = get_loan_asset_info(position.loan_asset).account_id
            balances = ServerClient().get_user_balances(position.user_id, account_id=account_id)
            refund_asset_amount_dict = {}
            for asset, balance_info in balances.items():
                assert balance_info["frozen"] == Decimal()
                balance_amount = quantize_amount(balance_info["available"], PrecisionEnum.COIN_PLACES)
                if balance_amount > Decimal():
                    refund_asset_amount_dict[asset] = balance_amount
            if refund_asset_amount_dict:
                refund_trans = tf_helper.new_refund_trans(position.id, refund_asset_amount_dict)
                db.session.commit()
            # else 刚好还完币，无剩余

        for r in refund_trans:
            if not r.is_finished:
                tf_helper.do_single_transfer(r, on_finished_commit=True)

        if not refund_trans or all([r.is_finished for r in refund_trans]):
            # 没有退回的币 or 都退回成功
            return True

    @classmethod
    def retry_refund_transfer(cls, tran: PledgeTransferHistory):
        if tran.type != PledgeTransferHistory.Type.REFUND_REMAIN_PLEDGE:
            return
        if tran.is_finished:
            return

        with CacheLock(key=LockKeys.user_pledge(tran.user_id, tran.loan_asset), wait=False):
            db.session.rollback()
            position: PledgePosition = PledgePosition.query.filter(
                PledgePosition.user_id == tran.user_id,
                PledgePosition.loan_asset == tran.loan_asset,
            ).order_by(PledgePosition.id.desc()).first()
            if not position or position.id != tran.position_id:
                return
            tf_helper = TransferHelper(position.user_id, position.loan_asset)
            tf_helper.do_single_transfer(tran, on_finished_commit=True)


@celery_task
@lock_call(with_args=True)
def execute_close_repay_task(position_id: int):
    """ 执行<用质押币>主动还币的流程 """
    from .liquidation import PositionLiquidator

    position: PledgePosition = PledgePosition.query.get(position_id)
    if position.status != PledgePosition.Status.REPAYING:
        return

    repay_his: PledgeRepayHistory = PledgeRepayHistory.query.filter(
        PledgeRepayHistory.position_id == position_id,
        PledgeRepayHistory.type.in_(
            [PledgeRepayHistory.Type.REPAY_BY_PLEDGE, PledgeRepayHistory.Type.SYS_REPAY_BY_PLEDGE]
        ),
    ).order_by(PledgeRepayHistory.id.desc()).first()
    if not repay_his:
        return

    repay_helper = PledgeAccountRepayHelper(position, repay_his.type)
    exc_his = repay_helper.query_exchange_his()
    if not exc_his.finished_at:
        execute_position_exchange_task.delay(position_id)
        return

    # 兑换已完成，开始还币
    loan_asset = position.loan_asset
    with CacheLock(key=LockKeys.user_pledge(position.user_id, loan_asset), wait=3):
        db.session.rollback()
        if position.status != PledgePosition.Status.REPAYING:
            # 仓位状态变化
            return

        assert get_user_last_active_position(position.user_id, position.loan_asset).id == position_id
        repay_tran = repay_helper.query_repay_tran()
        if not repay_tran:
            account_id = get_loan_asset_info(loan_asset).account_id
            balance = ServerClient().get_user_balances(position.user_id, loan_asset, account_id=account_id)
            balance_amount = quantize_amount(balance[loan_asset]["available"], PrecisionEnum.COIN_PLACES)
            if balance_amount < position.total_unflat_amount:
                # 兑换交易得到的目标币种不够还，触发强平，忽略白名单
                cur_ltv, _, liq_ltv = PositionManager.calc_ltv_and_liq_ltvs(position)
                PositionLiquidator(position.id).start_liq(cur_ltv, liq_ltv)  # 不对比ltv 大小

                msg = (
                    f"pledge_execute_position_repay_task position:{position_id} loan_asset:{loan_asset} repay to liq, "
                    f"exchanged_amount:{balance_amount} total_unrepay_amount:{position.total_unflat_amount}"
                )
                current_app.logger.error(msg)
                return

            # 写入质押账户还币的划转 commit后，不会再被系统强平了
            repay_amount = position.total_unflat_amount
            repay_tran = repay_helper.new_repay_tran(repay_amount)
            db.session.commit()

        repay_helper.execute_repay_by_tran(repay_tran)  # success or raise
        assert position.total_unflat_amount == Decimal()  # 主动还币一定是全部还清
        PositionManager.set_position_finished(position, PledgePosition.FinishType.REPAY_BY_PLEDGE)
        db.session.commit()
        on_position_finished(position.id)
        pledge_close_repay_finished_notice(repay_his=repay_helper.query_repay_his())
        RefundHelper.refund(position)


@celery_task
@lock_call(with_args=True)
def execute_partial_close_repay_task(position_id: int):
    """ 执行<用质押币>部分还币（部分平仓）的流程 """

    position: PledgePosition = PledgePosition.query.get(position_id)
    loan_asset = position.loan_asset
    account_id = get_loan_asset_info(loan_asset).account_id
    if position.status != PledgePosition.Status.BORROWING:
        return
    repay_helper = PledgeAccountRepayHelper(position, PledgeRepayHistory.Type.PARTIAL_REPAY_BY_PLEDGE)
    exc_his = repay_helper.query_exchange_his()
    # 1. 兑换未完成，继续兑换
    if not exc_his.finished_at:
        execute_position_exchange_task.delay(position_id)
        return

    # 2. 兑换已完成，开始还币
    with CacheLock(key=LockKeys.user_pledge(position.user_id, loan_asset), wait=3):
        db.session.rollback()
        if position.status != PledgePosition.Status.BORROWING:
            # 仓位状态变化
            return
        assert get_user_last_active_position(position.user_id, position.loan_asset).id == position_id
        balance = ServerClient().get_user_balances(position.user_id, loan_asset, account_id=account_id)
        balance_amount = quantize_amount(balance[loan_asset]["available"], PrecisionEnum.COIN_PLACES)
        balance_amount = min(balance_amount, position.total_unflat_amount)
        if balance_amount > Decimal():
            repay_tran = repay_helper.new_repay_tran(balance_amount)
            db.session.commit()
            repay_helper.execute_repay_by_tran(repay_tran)  # success or raise

        # 3. 减少全部目标质押币
        exchange_detail = json.loads(exc_his.exchange_detail)
        refund_assets = list(exchange_detail["sell_amount_data"])
        balance = ServerClient().get_user_balances(position.user_id, account_id=account_id)
        available_assets = {k for k in balance.keys() if balance.get(k, {}).get("available", 0) > 0}

        loan_asset_info: LoanAsset = get_loan_asset_info(position.loan_asset)
        if position.total_unflat_amount == Decimal():
            PositionManager.set_position_finished(position, PledgePosition.FinishType.REPAY_BY_PLEDGE)
            db.session.commit()
            on_position_finished(position.id)
            RefundHelper.refund(position)
            return
        if position.total_unflat_amount < loan_asset_info.min_loan_amount or available_assets == set(refund_assets):
            PledgeAccountRepayHelper(position, PledgeRepayHistory.Type.REPAY_BY_PLEDGE).start_close_repay()
            return
        for asset in refund_assets:
            balance_amount = quantize_amount(balance.get(asset, {}).get("available", 0),
                                             PrecisionEnum.COIN_PLACES)
            if balance_amount <= Decimal():
                continue

            PledgeOperator.remove_pledge_asset(position, asset, balance_amount, ltv=loan_asset_info.min_liquidation_ltv)


@celery_task
@lock_call(with_args=True)
def retry_spot_repay_tran_task(tran_id: int):
    """ 现货账户还币划转的重试 """
    tran: PledgeTransferHistory = PledgeTransferHistory.query.filter(
        PledgeTransferHistory.id == tran_id,
        PledgeTransferHistory.type == PledgeTransferHistory.Type.SPOT_ACCOUNT_REPAY,
    ).first()
    if not tran or tran.is_finished:
        return

    position: PledgePosition = PledgePosition.query.get(tran.position_id)
    if position.status != PledgePosition.Status.BORROWING:
        return

    loan_asset = position.loan_asset
    with CacheLock(key=LockKeys.user_pledge(position.user_id, loan_asset), wait=3):
        db.session.rollback()
        if position.status != PledgePosition.Status.BORROWING:
            return
        # 仓位状态还是借币中，才重试
        assert get_user_last_active_position(position.user_id, position.loan_asset).id == position.id
        SpotAccountRepayHelper(position).execute_repay_by_tran(tran)


@celery_task
@lock_call(with_args=True)
def loan_order_renew_or_force_repay_task(position_id: int):
    """ 借币订单续借 or 强制还币 """
    from app.business.pledge.loan import LoanAmountHelper

    if not (SiteSettings.spot_trading_enabled and SiteSettings.trading_enabled):
        return

    now_ = now()
    loan_orders: List[PledgeLoanOrder] = PledgeLoanOrder.query.filter(
        PledgeLoanOrder.position_id == position_id,
        PledgeLoanOrder.status == PledgeLoanOrder.Status.BORROWING,
        PledgeLoanOrder.expire_at <= now_,
    ).order_by(PledgeLoanOrder.id.asc()).all()
    if not loan_orders:
        return

    position = get_user_last_active_position(loan_orders[0].user_id, loan_orders[0].loan_asset)
    if position.id != position_id:
        current_app.logger.error(
            f"loan_order_renew_or_force_repay_task un_match_data position:{position_id} "
            f"loan_order_ids:{[i.id for i in loan_orders]}"
        )
        return
    if not position.is_borrowing():
        return

    def asset_amount_can_renew(__asset, __amount) -> bool:
        if LoanAmountHelper.get_system_rest_amount(__asset) >= __amount:
            return True
        asset_config = try_get_asset_config(__asset)
        if asset_config and asset_config.margin_renew_enabled:
            return True
        return False

    def get_order_renew_fail_count(_loan_order_id) -> int:
        _renew_rows = PledgeLoanOrderRenewHistory.query.filter(
            PledgeLoanOrderRenewHistory.loan_order_id == _loan_order_id,
        ).order_by(
            PledgeLoanOrderRenewHistory.id.desc(),
        ).limit(PledgeLoanOrderRenewHistory.MAX_RENEW_FAIL_COUNT).all()
        _count = 0
        for _r in _renew_rows:
            if _r.status == PledgeLoanOrderRenewHistory.Status.SUCCESS:
                break
            _count += 1
        return _count

    user_id = position.user_id
    loan_asset = position.loan_asset
    is_whitelist_user = PledgeLiquidationWhitelistUser.is_whitelist_user(user_id)
    force_repay_orders = []  # 强制还币订单
    renew_orders = []  # 可续借订单
    renew_fail_orders = []  # 续借失败订单
    notice_delay_order_counts = []  # 延期订单: 失败数次

    # 1. 判断订单是续借 还是强制还币
    for loan_order in loan_orders:
        if not loan_order.is_renew:
            # 未开启续借
            force_repay_orders.append(loan_order)
            continue

        # 开启续借&可续借
        can_renew = is_whitelist_user or asset_amount_can_renew(loan_order.loan_asset, loan_order.debt_amount)
        if can_renew:
            renew_orders.append(loan_order)
            continue

        # 开启续借&不可续借
        order_renew_fail_count = get_order_renew_fail_count(loan_order.id)
        if order_renew_fail_count < PledgeLoanOrderRenewHistory.MAX_RENEW_FAIL_COUNT:
            notice_delay_order_counts.append([loan_order, order_renew_fail_count])
        else:
            # 最后一次续借失败
            if order_renew_fail_count == PledgeLoanOrderRenewHistory.MAX_RENEW_FAIL_COUNT:
                renew_fail_orders.append(loan_order)
            force_repay_orders.append(loan_order)

    with CacheLock(key=LockKeys.user_pledge(user_id, loan_asset), wait=1):
        db.session.rollback()
        if not position.is_borrowing():
            return

        # 2 进行续借、延期
        if renew_fail_orders:
            LoanOrderHelper.add_renew_fail_his_by_loan_orders(renew_fail_orders)
        renew_success_his_rows = []
        if renew_orders:
            renew_success_his_rows = LoanOrderHelper.renew_success_loan_orders(renew_orders)
        renew_delay_notice_params = []
        if notice_delay_order_counts:
            delay_days = PledgeLoanOrderRenewHistory.RENEW_FAIL_DELAY_DAYS
            renew_detail_his_rows = LoanOrderHelper.renew_delay_loan_orders(
                [[i[0], delay_days] for i in notice_delay_order_counts]
            )
            _fail_counts = [i[1] for i in notice_delay_order_counts]
            renew_delay_notice_params = list(zip(renew_detail_his_rows, _fail_counts))
        db.session.commit()
        for renew_his_ in renew_success_his_rows:
            pledge_loan_order_renew_success_notice(renew_his_)
        for fail_renew_his_, fail_cnt_ in renew_delay_notice_params:
            pledge_loan_order_renew_fail_notice(fail_renew_his_, fail_cnt_)

        if not force_repay_orders:
            return

        # 3 再进行强制还币，优先现货账户还币，现货余额不足则用质押账户还币（主动平仓）
        # 先检查是否有未完成的划转
        pending_tran = PledgeTransferHistory.query.filter(
            PledgeTransferHistory.position_id == position_id,
            PledgeTransferHistory.type == PledgeTransferHistory.Type.SPOT_ACCOUNT_REPAY,
            PledgeTransferHistory.status.in_(
                [PledgeTransferHistory.Status.CREATED, PledgeTransferHistory.Status.DEDUCTED]
            ),
            PledgeTransferHistory.created_at >= now_ - timedelta(hours=24),
        ).first()
        if pending_tran:
            if pending_tran.created_at <= now_ - timedelta(minutes=1):
                retry_spot_repay_tran_task.delay(pending_tran.id)
            current_app.logger.warning(
                f"loan_order_renew_or_force_repay_task position:{position_id} pending_tran:{pending_tran.id} "
                f"force_repay_order_ids:{[i.id for i in force_repay_orders]}"
            )
            return

        total_force_repay_amount = sum([i.total_unflat_amount for i in force_repay_orders])
        assert total_force_repay_amount > Decimal()
        loan_asset = position.loan_asset
        balance = ServerClient().get_user_balances(position.user_id, asset=loan_asset, account_id=SPOT_ACCOUNT_ID)
        avai_amount = balance.get(loan_asset, {}).get("available", Decimal())
        if avai_amount < total_force_repay_amount:
            PledgeAccountRepayHelper(position, PledgeRepayHistory.Type.SYS_REPAY_BY_PLEDGE).start_close_repay()
        else:
            spot_repay_helper = SpotAccountRepayHelper(position)
            repay_tran = spot_repay_helper.tran_helper.new_spot_account_repay_tran(
                position.id, total_force_repay_amount
            )
            db.session.commit()
            spot_repay_helper.tran_helper.do_single_transfer(repay_tran, on_finished_commit=False)

            reduce_debt_amount, reduce_interest_amount = PositionManager.reduce_position_with_loan_orders(
                position,
                force_repay_orders,
                repay_type=PledgeRepayHistory.Type.SYS_REPAY_BY_LOAN,
            )
            if position.total_unflat_amount == Decimal():
                PositionManager.set_position_finished(position, PledgePosition.FinishType.REPAY_BY_SPOT)
            SpotAccountRepayHelper.new_repay_history(
                position,
                reduce_debt_amount,
                reduce_interest_amount,
                repay_type=PledgeRepayHistory.Type.SYS_REPAY_BY_LOAN,
            )
            LendableAmountProcessor(
                position.loan_asset,
                reduce_debt_amount,
                LendableAssetChangeHistory.BusinessType.PLEDGE_FLAT,
            ).process_new_record()  # inner committed
            db.session.commit()
            if position.is_finished():
                on_position_finished(position.id)
                RefundHelper.refund(position)
        for repay_o in force_repay_orders:
            pledge_loan_order_force_repay_notice(repay_o)
