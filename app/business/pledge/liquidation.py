# -*- coding: utf-8 -*-
import json
from decimal import Decimal, ROUND_UP
from functools import cached_property
from typing import List

from flask import current_app

from app.models import db, MarginInsurance, LendableAssetChangeHistory
from app.models.pledge import (
    P<PERSON>Position,
    PledgeLoanOrder,
    PledgeLiquidationWhitelistUser,
    PledgeLiquidationHistory,
    PledgeRepayHistory,
    PledgeTransferHistory,
    PledgeAssetTempOfflineUser,
)
from app.common import PrecisionEnum, CeleryQueues
from .helper import (
    get_loan_asset_info,
    get_user_last_active_position,
    get_cached_market_index_prices,
    PledgeValueHelper,
)
from .position import PositionManager, LoanOrderHelper, on_position_finished
from .exchange import execute_position_exchange_task
from .transfer import TransferHelper
from .repay import PledgeAccountRepayHelper, FundRepayHelper, RefundHelper
from .notice import pledge_position_liq_warning_notice, pledge_position_liq_notice
from app.business import ServerClient, lock_call, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ock, UserSettings
from app.business.lendable import LendableAmountProcessor
from app.caches.pledge import AdminPledgePositionLtvCache
from app.utils import celery_task, now, quantize_amount, route_module_to_celery_queue
from app.utils.parser import JsonEncoder


route_module_to_celery_queue(__name__, CeleryQueues.PLEDGE)


LIQ_FEE_RATE = Decimal("0.02")


class PositionLiquidator:
    def __init__(self, position_id: int):
        self.position: PledgePosition = PledgePosition.query.get(position_id)

    def is_need_check(self) -> bool:
        """ 是否需要进行强平检查，double check """
        if self.position.status not in PledgePosition.CAN_LIQ_STATUSES:
            return False
        if PledgeAccountRepayHelper.has_close_repay_tran(self.position.id):
            # 主动还币兑换完并且写入了还币划转，不再强平
            return False
        if PledgeLiquidationWhitelistUser.is_whitelist_user(self.position.user_id):
            return False
        if PledgeAssetTempOfflineUser.get_by_user_id(self.position.user_id):
            current_app.logger.warning(
                f"pledge_liq_check position: {self.position.id} "
                f"user_id: {self.position.user_id} pledge_asset_temp_offline_user"
            )
            return False
        return True

    def check(self):
        """ 强平检查 """
        if not self.is_need_check():
            return

        position = self.position
        with CacheLock(key=LockKeys.user_pledge(position.user_id, position.loan_asset), wait=5):
            db.session.rollback()
            if not self.is_need_check():
                return

            cur_ltv, warning_ltv, liq_ltv = PositionManager.calc_ltv_and_liq_ltvs(position)
            # 待还越大 ltv越大
            if cur_ltv >= liq_ltv:
                self.start_liq(cur_ltv, liq_ltv)
            elif cur_ltv >= warning_ltv:
                pledge_position_liq_warning_notice(position, cur_ltv)
        AdminPledgePositionLtvCache().set_pos_ltv(position.id, cur_ltv)

    def start_liq(self, ltv: Decimal, liq_ltv: Decimal):
        """ 开始强平，终止主动还币，修改仓位状态，开始兑换交易 """
        position = self.position

        repay_helper = PledgeAccountRepayHelper(position, PledgeRepayHistory.Type.LIQ)
        liq_exc_his, liq_repay_his = repay_helper.new_exchange_and_repay_history()
        if position.status == PledgePosition.Status.REPAYING and (
            close_exc_his := repay_helper.try_terminate_close_repay()
        ):
            liq_exc_his.init_pledge_data = close_exc_his.init_pledge_data  # 复制主动还币的初始质押币数目
        self.new_liq_history(liq_exc_his.id, ltv)

        loan_orders = LoanOrderHelper.get_pos_borrowing_loan_orders(position.id)
        for loan_order in loan_orders:
            loan_order.status = PledgeLoanOrder.Status.LIQ
        position.status = PledgePosition.Status.LIQ
        db.session.commit()
        execute_position_exchange_task.delay(position.id)
        pledge_position_liq_notice(position, ltv, liq_ltv)

    def new_liq_history(self, exchange_id: int, triggering_ltv: Decimal) -> PledgeLiquidationHistory:
        liq_his = PledgeLiquidationHistory(
            user_id=self.position.user_id,
            position_id=self.position.id,
            exchange_id=exchange_id,
            loan_asset=self.position.loan_asset,
            init_total_unflat_amount=self.position.total_unflat_amount,
            triggering_ltv=triggering_ltv,
            repay_amount=0,
            status=PledgeLiquidationHistory.Status.RUNNING,
        )
        db.session.add(liq_his)
        return liq_his

    def execute(self):
        position = self.position
        if position.status != PledgePosition.Status.LIQ:
            return

        if PledgeAssetTempOfflineUser.get_by_user_id(position.user_id):
            current_app.logger.warning(
                f"pledge_liq_execute position: {position.id} "
                f"user_id: {position.user_id} pledge_asset_temp_offline_user"
            )
            return

        repay_helper = PledgeAccountRepayHelper(position, PledgeRepayHistory.Type.LIQ)
        liq_exc_his = repay_helper.query_exchange_his()
        if not liq_exc_his.finished_at:
            execute_position_exchange_task.delay(position.id)
            return

        # 兑换已完成，开始强平还币
        assert get_user_last_active_position(position.user_id, position.loan_asset).id == position.id
        repay_tran = repay_helper.query_repay_tran()
        if not repay_tran:
            loan_asset = position.loan_asset
            account_id = get_loan_asset_info(loan_asset).account_id
            balance = ServerClient().get_user_balances(position.user_id, loan_asset, account_id=account_id)
            balance_amount = quantize_amount(balance[loan_asset]["available"], PrecisionEnum.COIN_PLACES)
            assert balance_amount > Decimal()
            repay_amount = min(position.total_unflat_amount, balance_amount)
            repay_tran = repay_helper.new_repay_tran(repay_amount)
            db.session.commit()

        repay_helper.execute_repay_by_tran(repay_tran)  # success or raise
        liq_his = self.liq_history
        liq_his.repay_amount = repay_tran.amount
        liq_his.used_pledge_data = repay_helper.query_repay_his().used_pledge_data
        db.session.commit()

        # 不管是否足够还币，都尝试收取强平清算费（可能会剩一些 低市值没被兑换的质押币）
        if not self.take_liq_fee():
            return
        if self.position.total_unflat_amount > Decimal():
            # 不够还，用保险基金垫付
            self.repay_by_fund()
        else:
            self.end_liq(is_refund=True)

    @cached_property
    def liq_history(self) -> PledgeLiquidationHistory:
        liq_his: PledgeLiquidationHistory = PledgeLiquidationHistory.query.filter(
            PledgeLiquidationHistory.position_id == self.position.id,
        ).first()
        return liq_his

    def _new_liq_fee_trans(self) -> List[PledgeTransferHistory]:
        position = self.position
        pledge_account_id = get_loan_asset_info(position.loan_asset).account_id
        balances = ServerClient().get_user_balances(position.user_id, account_id=pledge_account_id)
        asset_balance_amount_dict = {}
        for asset, balance_info in balances.items():
            assert balance_info["frozen"] == Decimal()
            available = quantize_amount(balance_info["available"], PrecisionEnum.COIN_PLACES)
            if available > Decimal():
                asset_balance_amount_dict[asset] = available

        loan_asset = position.loan_asset
        market_index_price_dict = get_cached_market_index_prices()

        pledge_asset_amount_dict = dict(asset_balance_amount_dict)
        sorted_assets = []
        if loan_asset in asset_balance_amount_dict:
            # 优先用剩余的借币币种
            sorted_assets.append(loan_asset)
            pledge_asset_amount_dict.pop(loan_asset)
        sorted_p_assets = PledgeValueHelper.sort_pledge_assets(pledge_asset_amount_dict, market_index_price_dict)
        sorted_assets.extend(sorted_p_assets)
        sorted_assets = sorted(set(sorted_assets), key=sorted_assets.index)

        loan_asset_price = PledgeValueHelper.get_asset_index_price(loan_asset, market_index_price_dict)
        liq_his = self.liq_history
        loan_asset_fee_amount = liq_his.init_total_unflat_amount * liq_his.fee_rate
        total_fee_usdt = loan_asset_fee_amount * loan_asset_price
        total_fee_usdt = quantize_amount(total_fee_usdt, PrecisionEnum.COIN_PLACES, ROUND_UP)

        rest_fee_usdt = total_fee_usdt
        deduct_asset_amount_dict = {}
        for asset in sorted_assets:
            balance_amount = asset_balance_amount_dict[asset]
            p_asset_price = PledgeValueHelper.get_asset_index_price(asset, market_index_price_dict)
            _usdt = balance_amount * p_asset_price
            if rest_fee_usdt >= _usdt:
                deduct_asset_amount_dict[asset] = balance_amount
                rest_fee_usdt -= _usdt
            else:
                # 向上取整
                _fee_amount = quantize_amount(rest_fee_usdt / p_asset_price, PrecisionEnum.COIN_PLACES, ROUND_UP)
                deduct_asset_amount_dict[asset] = _fee_amount
                rest_fee_usdt = Decimal()
            if rest_fee_usdt <= Decimal():
                break

        fee_trans = []
        for asset, amount in deduct_asset_amount_dict.items():
            row = PledgeTransferHistory(
                user_id=position.user_id,
                loan_asset=position.loan_asset,
                position_id=position.id,
                type=PledgeTransferHistory.Type.LIQ_FEE,
                from_account_id=pledge_account_id,
                to_account_id=None,
                asset=asset,
                amount=amount,
            )
            db.session.add(row)
            fee_trans.append(row)
        db.session.commit()
        return fee_trans

    def take_liq_fee(self) -> bool:
        """ 收取强平清算费 """
        position = self.position
        liq_his = self.liq_history
        if not liq_his.fee_rate:
            liq_his.fee_rate = LIQ_FEE_RATE
            db.session.commit()

        # 按第一次写入的数目去扣
        liq_fee_trans: List[PledgeTransferHistory] = PledgeTransferHistory.query.filter(
            PledgeTransferHistory.user_id == position.user_id,
            PledgeTransferHistory.position_id == position.id,
            PledgeTransferHistory.type == PledgeTransferHistory.Type.LIQ_FEE,
        ).all()
        if not liq_fee_trans:
            liq_fee_trans = self._new_liq_fee_trans()

        for r in liq_fee_trans:
            if not r.is_finished:
                TransferHelper.do_single_transfer(r, on_finished_commit=True)

        # 质押账户余额不足-无法收取强平清算费 or 强平清算费收取成功
        fee_is_deducted = not liq_fee_trans or all([r.is_finished for r in liq_fee_trans])
        if not fee_is_deducted:
            return False
        if not liq_his.fee_at:
            liq_his.fee_at = now()
            fee_data = {r.asset: r.amount for r in liq_fee_trans}
            liq_his.fee_data = json.dumps(fee_data, cls=JsonEncoder)
            for r in liq_fee_trans:
                FundRepayHelper.liq_fee_and_fund_record(r.asset, r.amount)
            db.session.commit()
        return True

    def update_liq_loan_orders_to_arrears(self):
        loan_orders = PledgeLoanOrder.query.filter(
            PledgeLoanOrder.position_id == self.position.id,
            PledgeLoanOrder.status == PledgeLoanOrder.Status.LIQ,
        ).all()
        for loan_order in loan_orders:
            if loan_order.total_unflat_amount > Decimal():
                loan_order.status = PledgeLoanOrder.Status.ARREARS

    def repay_by_fund(self):
        """ 强平还币后，使用保险基金垫付 """
        asset_fund_row: MarginInsurance = MarginInsurance.query.filter(
            MarginInsurance.asset == self.position.loan_asset,
        ).first()
        fund_amount = asset_fund_row.amount if asset_fund_row else Decimal()
        if fund_amount <= Decimal():
            self.set_user_limit(self.position.user_id)
            self.position.status = PledgePosition.Status.ARREARS
            self.update_liq_loan_orders_to_arrears()
            self.set_liq_his_finished()
            db.session.commit()
            return

        if fund_amount >= self.position.total_unflat_amount:
            # 保险基金足够垫付
            real_pad_amount = self.position.total_unflat_amount
            is_arrears = False
        else:
            # 保险基金不足垫付
            real_pad_amount = fund_amount
            is_arrears = True
            self.set_user_limit(self.position.user_id)

        reduce_debt_amount, reduce_interest_amount = PositionManager.reduce_position(
            self.position,
            real_pad_amount,
            repay_type=PledgeRepayHistory.Type.FUND,
        )
        FundRepayHelper.repay(self.position, reduce_debt_amount, reduce_interest_amount)
        liq_his = self.liq_history
        liq_his.fund_repay_amount += real_pad_amount
        self.set_liq_his_finished()

        if is_arrears:
            self.position.status = PledgePosition.Status.ARREARS
            self.update_liq_loan_orders_to_arrears()
        LendableAmountProcessor(
            self.position.loan_asset,
            reduce_debt_amount,
            LendableAssetChangeHistory.BusinessType.PLEDGE_FLAT,
        ).process_new_record()  # inner committed
        db.session.commit()

        if not is_arrears:
            self.end_liq(is_refund=False)

    def arrears_full_repay_by_fund(self):
        """ 欠款中的仓位，使用保险基金完全垫付 """
        if self.position.status != PledgePosition.Status.ARREARS:
            return

        asset_fund_row: MarginInsurance = MarginInsurance.query.filter(
            MarginInsurance.asset == self.position.loan_asset,
        ).first()
        fund_amount = asset_fund_row.amount if asset_fund_row else Decimal()
        if fund_amount <= Decimal():
            return
        if fund_amount < self.position.total_unflat_amount:
            return

        real_pad_amount = self.position.total_unflat_amount
        reduce_debt_amount, reduce_interest_amount = PositionManager.reduce_position(
            self.position,
            real_pad_amount,
            repay_type=PledgeRepayHistory.Type.FUND,
        )
        assert self.position.total_unflat_amount == Decimal()
        FundRepayHelper.repay(self.position, reduce_debt_amount, reduce_interest_amount)
        liq_his = self.liq_history
        liq_his.fund_repay_amount += real_pad_amount

        LendableAmountProcessor(
            self.position.loan_asset,
            reduce_debt_amount,
            LendableAssetChangeHistory.BusinessType.PLEDGE_FLAT,
        ).process_new_record()  # inner committed
        db.session.commit()

        self.end_liq(is_refund=False)
        self.remove_user_limit(self.position.user_id)

    def end_liq(self, is_refund: bool = True):
        """ 结束强平，修改仓位状态 """
        position = self.position
        assert position.total_unflat_amount == Decimal()

        self.set_liq_his_finished()
        PositionManager.set_position_finished(position, PledgePosition.FinishType.LIQ)
        db.session.commit()
        on_position_finished(position.id)
        if is_refund:
            RefundHelper.refund(position)

    def set_liq_his_finished(self):
        liq_his = self.liq_history
        if liq_his.status != PledgeLiquidationHistory.Status.FINISHED:
            liq_his.status = PledgeLiquidationHistory.Status.FINISHED

    @classmethod
    def set_user_limit(cls, user_id: int):
        """ 禁止用户提现，仓位强平中->欠款中 调用 """
        user_settings = UserSettings(user_id)
        user_settings.withdrawals_disabled_due_to_pledge_arrears = True

    @classmethod
    def remove_user_limit(cls, user_id: int):
        """ 恢复用户提现，仓位欠款中->已结束 调用 """
        user_settings = UserSettings(user_id)
        user_settings.withdrawals_disabled_due_to_pledge_arrears = False


@celery_task
@lock_call(with_args=True)
def check_position_liquidation_task(position_id: int):
    """ 仓位强平检查 """
    PositionLiquidator(position_id).check()


@celery_task
@lock_call(with_args=True)
def execute_position_liquidation_task(position_id: int):
    """ 仓位强平流程执行 """
    PositionLiquidator(position_id).execute()


@celery_task
@lock_call(with_args=True)
def repay_arrears_position_by_fund_task(position_id: int):
    """ 欠款中的仓位 尝试用保险基金还币 """
    helper = PositionLiquidator(position_id)
    position = helper.position
    with CacheLock(key=LockKeys.user_pledge(position.user_id, position.loan_asset)):
        db.session.rollback()
        helper.arrears_full_repay_by_fund()
