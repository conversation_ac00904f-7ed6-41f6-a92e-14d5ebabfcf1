import json
from collections import defaultdict
from decimal import Decimal
from functools import cached_property
from typing import Tu<PERSON>, Dict, List, Optional

import math
from sqlalchemy import func

from app.business import ExchangeLogDB
from app.caches import PerpetualMarketCache, MarketCache
from app.caches.kline import AssetQuotesDataCache
from app.common import CeleryQueues, PrecisionEnum, PerpetualMarketType
from app.models import db, Market, PerpetualMarket
from app.models.market_liquidity import Exchange, UnitLiquidityData, MarketType, YesterdayMedianBidAskSpread, \
    MarketLiquidityStatistic, PriceVolatility
from app.utils import current_timestamp, quantize_amount, route_module_to_celery_queue, safe_div, group_by

route_module_to_celery_queue(__name__, CeleryQueues.STATISTIC)


class Calculator:
    market_type: MarketType
    exchange: Exchange
    stage_list = [
        ('depth_tolerance_02', Decimal('0.002')),
        ('depth_tolerance_05', Decimal('0.005')),
        ('depth_tolerance_10', Decimal('0.01')),
        ('depth_tolerance_20', Decimal('0.02')),
        ('depth_tolerance_50', Decimal('0.05')),
    ]
    continuity_pair = [
        ('continuity_tolerance_05', Decimal('0.005')),
        ('continuity_tolerance_10', Decimal('0.01')),
        ('continuity_tolerance_20', Decimal('0.02')),
        ('continuity_tolerance_50', Decimal('0.05')),
    ]

    def calculate_market_liquidity(self):
        table_ts, ts = self.get_snapshot_ts()
        if not ts:  # 数据还未生成
            return
        if self.rec_has_created(ts):
            return
        group_items = self.get_depth_snapshot_data(table_ts, ts)
        filtered_group_items = self.filter_by_depth(group_items)
        markets_price_dic, market_bid_ask_dic = self.get_market_info_dic(filtered_group_items)
        self.insert_unit_data(markets_price_dic, market_bid_ask_dic, filtered_group_items, ts)

    def filter_by_depth(self, group_items):
        raise NotImplementedError

    def get_snapshot_ts(self) -> Tuple[int, int]:
        current_ts = current_timestamp(to_int=True)
        today_ts = current_ts - current_ts % 86400
        table = self.get_table(today_ts)
        if not table.exists():
            return today_ts, 0
        columns = ('max(`snapshot_ts`)', )
        ret = table.select(*columns)
        snapshot_ts = ret[0][0] or 0
        return today_ts, snapshot_ts

    def rec_has_created(self, ts):
        rec = UnitLiquidityData.query.filter(
            UnitLiquidityData.exchange == self.exchange,
            UnitLiquidityData.market_type == self.market_type,
            UnitLiquidityData.report_time == ts
        ).first()
        return bool(rec)

    def get_depth_snapshot_data(self, table_ts: int, snapshot_ts: int):
        raise NotImplementedError

    @staticmethod
    def get_market_info_dic(group_items):
        markets_price_dic, market_bid_ask_dic = dict(), defaultdict(dict)
        for market, market_lis in group_items.items():
            if not market_lis:
                continue
            info = market_lis[0]
            depth_dic = info['data']
            last_price = Decimal(depth_dic.get('last', '0'))
            if not last_price:
                continue
            bids, asks = depth_dic['bids'], depth_dic['asks']
            markets_price_dic[market] = last_price
            market_bid_ask_dic[market]['bids'] = [(Decimal(i[0]), Decimal(i[1])) for i in bids]
            market_bid_ask_dic[market]['asks'] = [(Decimal(i[0]), Decimal(i[1])) for i in asks]
        return markets_price_dic, market_bid_ask_dic

    def insert_unit_data(self, markets_price_dic, market_bid_ask_dic, group_items, ts):
        continuity_data = self.calculate_continuity_data(market_bid_ask_dic)
        for market, bid_ask_data in market_bid_ask_dic.items():
            price = markets_price_dic.get(market)
            _depth_data = group_items[market]
            bid_ask_1, bid_ask_10 = self.get_bid_asks_data(bid_ask_data)
            depth_data = self.get_market_stage_data(_depth_data)
            rec = UnitLiquidityData(
                market=market,
                market_type=self.market_type,
                exchange=self.exchange,
                report_time=ts,
                bid_ask_1=bid_ask_1,
                bid_ask_10=bid_ask_10,
                sign_price=price,
                depth_tolerance_02=depth_data['depth_tolerance_02'],
                depth_tolerance_05=depth_data['depth_tolerance_05'],
                depth_tolerance_10=depth_data['depth_tolerance_10'],
                depth_tolerance_20=depth_data['depth_tolerance_20'],
                depth_tolerance_50=depth_data['depth_tolerance_50'],
                continuity_tolerance_05=continuity_data[market]['continuity_tolerance_05'],
                continuity_tolerance_10=continuity_data[market]['continuity_tolerance_10'],
                continuity_tolerance_20=continuity_data[market]['continuity_tolerance_20'],
                continuity_tolerance_50=continuity_data[market]['continuity_tolerance_50'],
                final_continuity_tolerance=continuity_data[market]['final_continuity_tolerance'],
            )
            db.session.add(rec)
        db.session.commit()

    @staticmethod
    def get_bid_asks_data(bid_ask_data):
        if not bid_ask_data:
            return None, None
        bids, asks = bid_ask_data['bids'], bid_ask_data['asks']
        bids.sort(key=lambda x: x[0], reverse=True)
        asks.sort(key=lambda x: x[0])
        bid_1 = bids[0][0] if bids else 0
        ask_1 = asks[0][0] if asks else 0
        bid_10 = bids[9][0] if len(bids) >= 10 else 0
        ask_10 = asks[9][0] if len(asks) >= 10 else 0
        bid_ask_1 = safe_div(abs(bid_1 - ask_1), (bid_1 + ask_1) / 2)
        bid_ask_10 = safe_div(abs(bid_10 - ask_10), (bid_10 + ask_10) / 2)
        return bid_ask_1, bid_ask_10

    def get_market_stage_data(self, _market_depth_list: List):
        stage_depth_dict = defaultdict(Decimal)
        # 升序排列
        _market_depth_list = sorted(_market_depth_list, key=lambda x: Decimal(x["depth"]))
        for _stage_key, _stage in self.stage_list:
            for index, _depth_data in enumerate(_market_depth_list):
                _data = _depth_data["data"]
                extra = {
                    'contract_size': _data.get('contract_size', 1),
                    'contract_mult': _data.get('contract_mult', 1),
                }
                if not _data:
                    continue
                asks, bids = _data["asks"], _data["bids"]
                if not len(asks) or not len(bids):
                    continue
                ask_1, bid_1 = Decimal(asks[0][0]), Decimal(bids[0][0])
                last_price = (ask_1 + bid_1) / 2
                _depth = Decimal(_depth_data["depth"])
                # (max, min)
                price_range = (last_price * (Decimal('1') + _stage),
                               last_price * (Decimal('1') - _stage))
                # 按照深度格式化(max, min)
                fix_price_range = (price_range[0] - price_range[0] % _depth + _depth,
                                   price_range[1] - price_range[1] % _depth)
                # 从大到小排序
                _depths = sorted(_data["asks"] + _data["bids"], reverse=True, key=lambda item: Decimal(item[0]))
                # (max, min)
                if len(_depths) == 0:
                    break
                _depth_range = (Decimal(_depths[0][0]), Decimal(_depths[-1][0]))

                def check_range_cover(_range1: Tuple[Decimal, Decimal],
                                      _range2: Tuple[Decimal, Decimal]) -> bool:
                    return _range1[0] >= _range2[0] and _range1[1] <= _range2[1]

                def check_price_in_range(_price: Decimal,
                                         _range: Tuple[Decimal, Decimal]) -> bool:
                    return _range[0] >= _price >= _range[1]

                _take_range = None
                if check_range_cover(_depth_range, fix_price_range):
                    _take_range = fix_price_range
                if _take_range is not None:
                    stage_depth_dict[_stage_key] = sum([
                        self.get_depth_usd(Decimal(v[0]), Decimal(v[1]), _depth_data, extra)
                        for v in
                        list(filter(lambda x: check_price_in_range(Decimal(x[0]), _take_range), _depths))])
                    stage_depth_dict[_stage_key] = quantize_amount(stage_depth_dict[_stage_key],
                                                                   PrecisionEnum.CASH_PLACES)
                    break

                def get_same_interval(_range1: Tuple[Decimal, Decimal],
                                      _range2: Tuple[Decimal, Decimal]
                                      ) -> Optional[Tuple[Decimal, Decimal]]:

                    min_max = max(_range1[1], _range2[1])
                    max_min = min(_range1[0], _range2[0])
                    return (max_min, min_max) if max_min >= min_max else None

                if index == len(_market_depth_list) - 1:
                    take_range = get_same_interval(fix_price_range, _depth_range) or fix_price_range
                    stage_depth_dict[_stage_key] = sum([
                        self.get_depth_usd(Decimal(v[0]), Decimal(v[1]), _depth_data, extra)
                        for v in
                        list(filter(lambda x: check_price_in_range(Decimal(x[0]), take_range), _depths))])
                    stage_depth_dict[_stage_key] = quantize_amount(stage_depth_dict[_stage_key],
                                                                   PrecisionEnum.CASH_PLACES)
                    break

        return stage_depth_dict

    def get_table(self, table_ts):
        raise NotImplementedError

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        raise NotImplementedError

    def insert_yesterday_median_bid_ask_spread(self):
        ts = current_timestamp(to_int=True)
        end = ts - ts % 86400
        start = end - 86400
        recs = UnitLiquidityData.query.filter(
            UnitLiquidityData.report_time < end,
            UnitLiquidityData.report_time >= start,
            UnitLiquidityData.market_type == self.market_type,
            UnitLiquidityData.exchange == self.exchange,
        ).with_entities(
            UnitLiquidityData.market,
            UnitLiquidityData.bid_ask_1,
        ).all()
        yesterday_data = defaultdict(list)
        for market, sign_price in recs:
            yesterday_data[market].append(sign_price)
        res = defaultdict(Decimal)

        def cal_median_of_lis():
            if not data_lis:
                return 0
            data_lis.sort()
            length = len(data_lis)
            idx = int(length / 2)
            if not length % 2:
                return (data_lis[idx] + data_lis[idx - 1]) / 2
            else:
                return data_lis[idx]

        for market, data_lis in yesterday_data.items():
            median = cal_median_of_lis()
            res[market] = median
        for market, val in res.items():
            row = YesterdayMedianBidAskSpread(
                market=market,
                market_type=self.market_type,
                exchange=self.exchange,
                report_time=start,
                value=val
            )
            db.session.add(row)
        db.session.commit()

    def aggregate(self):
        ts = current_timestamp(to_int=True)
        today_ts = ts - ts % 86400
        report_ts = ts - ts % 600

        yesterday_median_data_dic = self.get_yesterday_median_data_dic(today_ts)
        bid_ask_data = self.get_bid_ask_depth_data(report_ts)
        price_volatility_data = self.get_price_volatility_data(report_ts)
        continuity_data = self.get_continuity_data(report_ts)
        markets = (set(yesterday_median_data_dic.keys()) | set(bid_ask_data.keys())
                   | set(price_volatility_data.keys()) | set(continuity_data.keys()))
        for market in markets:
            row = MarketLiquidityStatistic(
                market=market,
                market_type=self.market_type,
                exchange=self.exchange,
                report_time=report_ts,
                yesterday_median_bid_ask_spread=yesterday_median_data_dic.get(market),
                sign_price=bid_ask_data[market].get('sign_price'),
                bid_ask_1=bid_ask_data[market].get('bid_ask_1'),
                bid_ask_10=bid_ask_data[market].get('bid_ask_10'),
                price_volatility=price_volatility_data.get(market),
                depth_tolerance_02=bid_ask_data[market].get('depth_tolerance_02'),
                depth_tolerance_05=bid_ask_data[market].get('depth_tolerance_05'),
                depth_tolerance_10=bid_ask_data[market].get('depth_tolerance_10'),
                depth_tolerance_20=bid_ask_data[market].get('depth_tolerance_20'),
                depth_tolerance_50=bid_ask_data[market].get('depth_tolerance_50'),
                continuity_tolerance_05=continuity_data[market].get('continuity_tolerance_05'),
                continuity_tolerance_10=continuity_data[market].get('continuity_tolerance_10'),
                continuity_tolerance_20=continuity_data[market].get('continuity_tolerance_20'),
                continuity_tolerance_50=continuity_data[market].get('continuity_tolerance_50'),
                final_continuity_tolerance=continuity_data[market].get('final_continuity_tolerance'),
            )
            db.session.add(row)
        db.session.commit()
        self.insert_price_volatility_data(ts, price_volatility_data)

    def get_yesterday_median_data_dic(self, end):
        start = end - 86400
        records = YesterdayMedianBidAskSpread.query.filter(
            YesterdayMedianBidAskSpread.market_type == self.market_type,
            YesterdayMedianBidAskSpread.exchange == self.exchange,
            YesterdayMedianBidAskSpread.report_time == start,
        ).with_entities(
            YesterdayMedianBidAskSpread.market,
            YesterdayMedianBidAskSpread.value
        )
        res = dict()
        for market, value in records:
            res[market] = value
        return res

    def get_bid_ask_depth_data(self, end):
        start = end - 60 * 10
        records = UnitLiquidityData.query.filter(
            UnitLiquidityData.market_type == self.market_type,
            UnitLiquidityData.exchange == self.exchange,
            UnitLiquidityData.report_time >= start,
            UnitLiquidityData.report_time < end
        ).with_entities(
            UnitLiquidityData.market,
            func.avg(UnitLiquidityData.bid_ask_1).label('bid_ask_1'),
            func.avg(UnitLiquidityData.bid_ask_10).label('bid_ask_10'),
            func.avg(UnitLiquidityData.sign_price).label('sign_price'),
            func.avg(UnitLiquidityData.depth_tolerance_02).label('depth_tolerance_02'),
            func.avg(UnitLiquidityData.depth_tolerance_05).label('depth_tolerance_05'),
            func.avg(UnitLiquidityData.depth_tolerance_10).label('depth_tolerance_10'),
            func.avg(UnitLiquidityData.depth_tolerance_20).label('depth_tolerance_20'),
            func.avg(UnitLiquidityData.depth_tolerance_50).label('depth_tolerance_50'),
        ).group_by(
            UnitLiquidityData.market,
        ).all()
        res = defaultdict(lambda: defaultdict(Decimal))
        for record in records:
            market = record.market
            res[market]['bid_ask_1'] = record.bid_ask_1
            res[market]['bid_ask_10'] = record.bid_ask_10
            res[market]['sign_price'] = record.sign_price
            res[market]['depth_tolerance_02'] = record.depth_tolerance_02
            res[market]['depth_tolerance_05'] = record.depth_tolerance_05
            res[market]['depth_tolerance_10'] = record.depth_tolerance_10
            res[market]['depth_tolerance_20'] = record.depth_tolerance_20
            res[market]['depth_tolerance_50'] = record.depth_tolerance_50
        return res

    def get_price_volatility_data(self, end):
        market_price_dic = self.get_last_hour_price_data(end)
        res = dict()
        for market, price_lis in market_price_dic.items():
            price_volatility = self.calculate_price_volatility(price_lis)
            res[market] = price_volatility
        return res

    def get_continuity_data(self, end):
        res = defaultdict(dict)
        start = end - 60 * 10
        rec = UnitLiquidityData.query.filter(
            UnitLiquidityData.market_type == self.market_type,
            UnitLiquidityData.exchange == self.exchange,
            UnitLiquidityData.report_time < end,
            UnitLiquidityData.report_time >= start,
        ).with_entities(
            UnitLiquidityData.report_time
        ).order_by(
            UnitLiquidityData.report_time.desc()
        ).first()
        if not rec:
            return res
        report_time = rec.report_time
        recs = UnitLiquidityData.query.filter(
            UnitLiquidityData.market_type == self.market_type,
            UnitLiquidityData.exchange == self.exchange,
            UnitLiquidityData.report_time == report_time
        ).all()
        for record in recs:
            market = record.market
            res[market]['continuity_tolerance_05'] = record.continuity_tolerance_05
            res[market]['continuity_tolerance_10'] = record.continuity_tolerance_10
            res[market]['continuity_tolerance_20'] = record.continuity_tolerance_20
            res[market]['continuity_tolerance_50'] = record.continuity_tolerance_50
            res[market]['final_continuity_tolerance'] = record.final_continuity_tolerance
        return res

    def get_last_hour_price_data(self, end):
        start = end - 3600
        recs = UnitLiquidityData.query.filter(
            UnitLiquidityData.market_type == self.market_type,
            UnitLiquidityData.exchange == self.exchange,
            UnitLiquidityData.report_time >= start,
            UnitLiquidityData.report_time < end
        ).with_entities(
            UnitLiquidityData.market,
            UnitLiquidityData.sign_price
        ).all()
        res = defaultdict(list)
        for rec in recs:
            res[rec.market].append(rec.sign_price)
        return res

    @staticmethod
    def calculate_price_volatility(prices):
        """
        计算实时价格波动率（标准差）
        """
        if len(prices) < 2:
            return 0  # 如果数据点少于2个，无法计算波动率

        # 计算每分钟的收益率 Rt = (Pt - Pt-1) / Pt-1
        returns = []
        for t in range(1, len(prices)):
            rt = safe_div((prices[t] - prices[t - 1]), prices[t - 1])
            returns.append(rt)

        # 计算收益率的平均值 R'
        mean_return = sum(returns) / len(returns)

        # 计算波动率 σ = sqrt(1/(n-1) * sum((Rt - R')^2))
        variance = safe_div(sum((rt - mean_return) ** 2 for rt in returns), (len(returns) - 1))
        volatility = math.sqrt(variance)
        return volatility

    def calculate_continuity_data(self, data):

        def get_mid_price(bids, asks):
            """计算盘口中位价"""
            if not bids or not asks:
                return None
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            return (best_bid + best_ask) / 2

        def get_price_intervals(mid_price, interval_size_percent, num_intervals=50):
            """生成价格区间"""
            res = []
            interval_size = mid_price * interval_size_percent / num_intervals
            for i in range(num_intervals):
                lower = mid_price - (num_intervals - i) * interval_size
                upper = mid_price - (num_intervals - i - 1) * interval_size
                res.append((lower, upper))
            for i in range(num_intervals):
                lower = mid_price + i * interval_size
                upper = mid_price + (i + 1) * interval_size
                res.append((lower, upper))
            return res

        def check_orders_in_interval(orders, interval):
            """检查某个价格区间内是否有挂单"""
            for price, _ in orders:
                if interval[0] < price <= interval[1]:
                    return True
            return False

        market_field_dic = self.get_market_final_continuity_field_dic()
        results = defaultdict(lambda: defaultdict(Decimal))
        for symbol, order_book in data.items():
            bids = order_book['bids']
            asks = order_book['asks']
            mid_price = get_mid_price(bids, asks)
            if mid_price is None:
                continue
            final_continuity_field = market_field_dic.get(symbol, 'continuity_tolerance_05')
            for field, interval_size_percent in self.continuity_pair:
                intervals = get_price_intervals(mid_price, interval_size_percent)
                if not intervals:
                    score = Decimal()
                else:
                    score = 100
                    for interval in intervals:
                        has_bid = check_orders_in_interval(bids, interval)
                        has_ask = check_orders_in_interval(asks, interval)
                        if not has_bid and not has_ask:
                            score -= 1
                results[symbol][field] = max(score, 0)  # Ensure score doesn't go below 0
                if field == final_continuity_field:
                    results[symbol]['final_continuity_tolerance'] = score
        return results

    def get_market_final_continuity_field_dic(self) -> dict:
        raise NotImplementedError

    def insert_price_volatility_data(self, ts, price_volatility_data):
        for market, value in price_volatility_data.items():
            rec = PriceVolatility(
                market=market,
                market_type=self.market_type,
                exchange=self.exchange,
                report_time=ts,
                value=value
            )
            db.session.add(rec)
        db.session.commit()


class PerpetualCalculator(Calculator):

    def __init__(self):
        self.market_type = MarketType.PERPETUAL

    @cached_property
    def all_reverse_markets(self):
        markets = [market for market, v in PerpetualMarketCache().read_aside().items()
                   if v["type"] == PerpetualMarketType.INVERSE]
        return markets

    def get_depth_snapshot_data(self, table_ts: int, snapshot_ts: int):
        table = self.get_table(table_ts)
        fields = ("depth", "market", "money_rate", "stock_rate",
                  "data", "snapshot_ts")
        results = table.select(*fields, where=f"`snapshot_ts` = {snapshot_ts}")
        items = [dict(zip(fields, i)) for i in results]
        for item in items:
            data = item['data']
            item['data'] = json.loads(data)
        group_items = group_by(lambda x: x["market"], items)
        result = dict()
        for market, _data in group_items.items():
            result[market] = _data
        return result

    def filter_by_depth(self, group_items):
        res = dict()
        cache = PerpetualMarketCache().read_aside()
        for market, market_lis in group_items.items():
            if len(market_lis) <= 1:
                res[market] = market_lis
                continue
            market_info = cache[market]
            default_depth = Decimal(market_info["default_merge"])
            tmp = None
            market_lis.sort(key=lambda x: Decimal(x["depth"]))
            for depth_dic in market_lis:
                depth = depth_dic['depth']
                if depth < default_depth:
                    continue
                tmp = depth_dic
                break
            if tmp:
                res[market] = [tmp]
            else:
                res[market] = [market_lis[-1]]
        return res

    def get_table(self, table_ts):
        raise NotImplementedError

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        raise NotImplementedError

    def get_market_final_continuity_field_dic(self) -> dict:

        def get_final_continuity_field(leverage):
            """根据市值确定最终评分取自哪个字段"""
            if leverage == 20:
                return 'continuity_tolerance_20'  # 2%
            elif leverage == 50:
                return 'continuity_tolerance_10'  # 1%
            elif leverage == 100:
                return 'continuity_tolerance_05'  # 0.5%
            else:
                return 'continuity_tolerance_50'  # 5%

        market_leverage_dic = self.get_market_leverage_dic()
        res = dict()
        for market, leverage in market_leverage_dic.items():
            final_continuity_field = get_final_continuity_field(leverage)
            res[market] = final_continuity_field
        return res

    @staticmethod
    def get_market_leverage_dic():
        recs = PerpetualMarket.query.filter(
            PerpetualMarket.status == PerpetualMarket.StatusType.OPEN).with_entities(
            PerpetualMarket.name,
            PerpetualMarket.leverages
        ).all()
        res = dict()
        for market, leverage_str in recs:
            lev_lis = list(map(Decimal, leverage_str.split(',')))
            lev_lis.sort()
            res[market] = lev_lis[-1]
        return res


class CoinexPerpetualCalculator(PerpetualCalculator):
    exchange = Exchange.CoinEx

    def get_table(self, table_ts):
        return ExchangeLogDB.perpetual_depth_snapshot_table(table_ts)

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount
        return price * amount * Decimal(_data["money_rate"])


class BinancePerpetualCalculator(PerpetualCalculator):
    exchange = Exchange.Binance

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.exchange.value)

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        contract_size = 1
        if _extra:
            contract_size = Decimal(_extra['contract_size'])
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount * contract_size
        return price * amount * Decimal(_data["money_rate"])


class OkxPerpetualCalculator(PerpetualCalculator):
    exchange = Exchange.OKX

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.exchange.value)

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        contract_size = 1
        if _extra:
            contract_size = Decimal(_extra['contract_size'])
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount * contract_size
        return price * amount * Decimal(_data["money_rate"]) * contract_size


class GatePerpetualCalculator(PerpetualCalculator):
    exchange = Exchange.GATE

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.exchange.value)

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        contract_mult = 1
        if _extra:
            contract_mult = Decimal(_extra['contract_mult'])
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount
        return price * amount * Decimal(_data["money_rate"]) * contract_mult


class KucoinPerpetualCalculator(PerpetualCalculator):
    exchange = Exchange.KUCOIN

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.exchange.value)

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        contract_mult = 1
        if _extra:
            contract_mult = Decimal(_extra['contract_mult'])
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount
        return price * amount * Decimal(_data["money_rate"]) * contract_mult


class SpotCalculator(Calculator):

    def __init__(self):
        self.market_type = MarketType.SPOT

    def get_depth_snapshot_data(self, table_ts: int, snapshot_ts: int):
        table = self.get_table(table_ts)
        fields = ("depth", "market", "quote_asset_rate", "base_asset_rate",
                  "data", "snapshot_ts")
        results = table.select(*fields, where=f"`snapshot_ts` = {snapshot_ts}")
        items = [dict(zip(fields, i)) for i in results]
        for item in items:
            data = item['data']
            item['data'] = json.loads(data)
        group_items = group_by(lambda x: x["market"], items)
        result = dict()
        for market, _data in group_items.items():
            result[market] = _data
        return result

    def filter_by_depth(self, group_items):
        res = dict()
        for market, market_lis in group_items.items():
            if len(market_lis) <= 1:
                res[market] = market_lis
                continue
            cache = MarketCache(market).dict
            default_depth = Decimal(cache["default_depth"])
            tmp = None
            market_lis.sort(key=lambda x: Decimal(x["depth"]))
            for depth_dic in market_lis:
                depth = depth_dic['depth']
                if depth < default_depth:
                    continue
                tmp = depth_dic
                break
            if tmp:
                res[market] = [tmp]
            else:
                res[market] = [market_lis[-1]]
        return res

    def get_market_final_continuity_field_dic(self) -> dict:

        def get_final_continuity_field(market_cap):
            """根据市值确定最终评分取自哪个字段"""
            if market_cap <= Decimal('10000000'):
                return 'continuity_tolerance_50'  # 5%
            elif market_cap <= Decimal('100000000'):
                return 'continuity_tolerance_20'  # 2%
            elif market_cap <= Decimal('5000000000'):
                return 'continuity_tolerance_10'  # 1%
            else:
                return 'continuity_tolerance_05'  # 0.5%

        market_cap_dic = self.get_market_cap_dic()
        res = dict()
        for market, cap in market_cap_dic.items():
            final_continuity_field = get_final_continuity_field(cap)
            res[market] = final_continuity_field
        return res

    def get_market_cap_dic(self):
        assets_data = AssetQuotesDataCache().get_all_data()
        asset_info_dic = {i['asset']: i for i in assets_data}
        markets_asset_dic = self.get_spot_market_asset_dic()
        res = dict()
        for market, asset in markets_asset_dic.items():
            asset_info = asset_info_dic.get(asset)
            if not asset_info:
                res[market] = Decimal()
            else:
                res[market] = Decimal(asset_info['circulation_usd'])
        return res

    @staticmethod
    def get_spot_market_asset_dic():
        recs = Market.query.filter(Market.status == Market.Status.ONLINE).with_entities(
            Market.name,
            Market.base_asset
        )
        return dict(recs)

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        return price * amount * _data["quote_asset_rate"]


class CoinexSpotCalculator(SpotCalculator):
    exchange = Exchange.CoinEx

    def get_table(self, table_ts):
        return ExchangeLogDB.spot_depth_snapshot_table(table_ts)


class BinanceSpotCalculator(SpotCalculator):
    exchange = Exchange.Binance

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class OkxSpotCalculator(SpotCalculator):
    exchange = Exchange.OKX

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class GateSpotCalculator(SpotCalculator):
    exchange = Exchange.GATE

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class HtxSpotCalculator(SpotCalculator):
    exchange = Exchange.HTX

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class MexcSpotCalculator(SpotCalculator):
    exchange = Exchange.MEXC

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class BybitSpotCalculator(SpotCalculator):
    exchange = Exchange.BYBIT

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class KucoinSpotCalculator(SpotCalculator):
    exchange = Exchange.KUCOIN

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class BitgetSpotCalculator(SpotCalculator):
    exchange = Exchange.BITGET

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class LbankSpotCalculator(SpotCalculator):
    exchange = Exchange.LBANK

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class BitmartSpotCalculator(SpotCalculator):
    exchange = Exchange.BITMART

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class BingxSpotCalculator(SpotCalculator):
    exchange = Exchange.BINGX

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


class CoinwSpotCalculator(SpotCalculator):
    exchange = Exchange.COINW

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_spot_depth_snapshot_table(table_ts, self.exchange.value)


PERPETUAL_CALCULATORS = {
    CoinexPerpetualCalculator.exchange.value: CoinexPerpetualCalculator,
    BinancePerpetualCalculator.exchange.value: BinancePerpetualCalculator,
    OkxPerpetualCalculator.exchange.value: OkxPerpetualCalculator,
    GatePerpetualCalculator.exchange.value: GatePerpetualCalculator,
    KucoinPerpetualCalculator.exchange.value: KucoinPerpetualCalculator,
}


SPOT_CALCULATORS = {
    CoinexSpotCalculator.exchange.value: CoinexSpotCalculator,
    BinanceSpotCalculator.exchange.value: BinanceSpotCalculator,
    OkxSpotCalculator.exchange.value: OkxSpotCalculator,
    GateSpotCalculator.exchange.value: GateSpotCalculator,
    HtxSpotCalculator.exchange.value: HtxSpotCalculator,
    MexcSpotCalculator.exchange.value: MexcSpotCalculator,
    BybitSpotCalculator.exchange.value: BybitSpotCalculator,
    KucoinSpotCalculator.exchange.value: KucoinSpotCalculator,
    BitgetSpotCalculator.exchange.value: BitgetSpotCalculator,
    LbankSpotCalculator.exchange.value: LbankSpotCalculator,
    BitmartSpotCalculator.exchange.value: BitmartSpotCalculator,
    BingxSpotCalculator.exchange.value: BingxSpotCalculator,
    CoinwSpotCalculator.exchange.value: CoinwSpotCalculator,
}
