import datetime
from collections import defaultdict
from typing import Iterable, Set, Union, List

from flask import current_app

from app.caches import SetCache
from app.caches.push import (CommonGroupBroadcastCountLimitCache, CommonGroupBroadcastFrequencyLimitCache,
                             GlobalBroadcastLimitCache, NoticeGroupBroadcastCountLimitCache,
                             NoticeGroupBroadcastFrequencyLimitCache)
from app.common.push import AppPushBusiness, AppBroadcastBusiness, PushBroadcastLimitGroup, \
    PushGroupCountLimitConfigDic, PushGroupFrequencyLimitConfigDic, PushBusinessLimitConfigDic
from app.exceptions import InvalidArgument
from app.models import AppPushBusinessHistory, AppPush, AutoPushStrategy

from app.utils import current_timestamp


class _PushLimitMeta(type):
    cache_keys_dic = dict()

    def __new__(mcs, name, bases, dct):
        cls = super().__new__(mcs, name, bases, dct)
        key = getattr(cls, 'key', None)
        if key is None:
            return cls
        if key in mcs.cache_keys_dic:
            raise ValueError(f'{cls.__name__} 的 key与{mcs.cache_keys_dic[key]} 重复，请修改后重新添加！')
        mcs.cache_keys_dic[key] = cls.__name__
        return cls


class PushLimiterBase(metaclass=_PushLimitMeta):
    """按user_id推送的push限制集类"""
    default_interval: int
    count_limit: int
    key: str    # 作为缓存查询和和数据表存储时的唯一键
    store_unit_interval: int   # 将该时间段内用户所有push写入同一个时间片的db记录（此时间段内同一用户多次推送也记录为1次）
    tmp_store_his_send_ts: int   # 内存中临时存放的push数据的时间分片
    tmp_store_his_send_users: set   # 内存中临时存放的push用户
    store_db = AppPushBusinessHistory   # 持久化存放push数据的表
    limit_cache: SetCache

    def __init__(self):
        self.limit_cache = SetCache(self.key)

    def get_limit_users(self) -> Set:
        ret = self.limit_cache.smembers()
        return {int(i) for i in ret} if ret else set()

    def is_limited(self, user_id: int) -> bool:
        return self.limit_cache.sismember(str(user_id))

    def can_push(self, user_ids: Union[list, Set]) -> Set:
        if len(user_ids) <= 3:
            res = set()
            for user_id in user_ids:
                if self.is_limited(user_id):
                    continue
                res.add(user_id)
        else:  # 数量较多时，才全量读取缓存的value
            limit_users = self.get_limit_users()
            res = set(user_ids) - limit_users
        return res

    @classmethod
    def set_pushed(cls, user_ids: Set):
        now_ = current_timestamp(to_int=True)
        ts = now_ - now_ % cls.store_unit_interval   # 当前的时间分片
        if cls.tmp_store_his_send_ts < ts:    # 内存中有暂存的历史数据，先dump进数据库，再存当前时间分片的数据
            his_send_ts = cls.tmp_store_his_send_ts
            his_send_users = cls.tmp_store_his_send_users
            cls.tmp_store_his_send_ts = ts
            cls.tmp_store_his_send_users = set()   # 先清空，再dump，避免并发导致重复dump
            if his_send_users:
                current_app.logger.warning(
                    f'dump business: {cls.key}, at:{datetime.datetime.fromtimestamp(now_)}, '
                    f'tmp_ts: {datetime.datetime.fromtimestamp(his_send_ts)}, users:{len(his_send_users)}')
                cls.store_db.set_pushed(his_send_users, cls.key, cls.default_interval, his_send_ts)

        cls.tmp_store_his_send_users.update(user_ids)

    @classmethod
    def reload(cls):
        """从数据库中筛选已经达到推送限制的用户"""
        now_ = current_timestamp(to_int=True)
        records = cls.store_db.query.filter(
            cls.store_db.business == cls.key,
            cls.store_db.expire_at >= now_
        ).all()
        user_send_times = defaultdict(int)
        for record in records:
            user_ids = record.get_send_user_ids()
            for user_id in user_ids:
                user_send_times[user_id] += 1
        res = set()
        for user_id, count in user_send_times.items():
            if count >= cls.count_limit:
                res.add(user_id)
        cls.limit_cache = SetCache(cls.key)
        cls.limit_cache.save(res)


class AppAnnouncementPushLimiter(PushLimiterBase):
    """公告订阅消息推送限制"""
    default_interval = PushBusinessLimitConfigDic[AppPushBusiness.AnnouncementSubscribe]['interval']
    count_limit = PushBusinessLimitConfigDic[AppPushBusiness.AnnouncementSubscribe]['count']
    key = PushBusinessLimitConfigDic[AppPushBusiness.AnnouncementSubscribe]['key']
    store_unit_interval: int = 10 * 60
    tmp_store_his_send_ts: int = 0
    tmp_store_his_send_users = set()


class AppActivitySubscribePushLimiter(PushLimiterBase):
    """活动订阅消息推送限制"""
    default_interval = PushBusinessLimitConfigDic[AppPushBusiness.ActivitySubscribe]['interval']
    count_limit = PushBusinessLimitConfigDic[AppPushBusiness.ActivitySubscribe]['count']
    key = PushBusinessLimitConfigDic[AppPushBusiness.ActivitySubscribe]['key']
    store_unit_interval: int = 10 * 60
    tmp_store_his_send_ts: int = 0
    tmp_store_his_send_users = set()


class AppBlogPushLimiter(PushLimiterBase):
    """博客订阅消息推送限制"""

    default_interval = PushBusinessLimitConfigDic[AppPushBusiness.BlogSubscribe]['interval']
    count_limit = PushBusinessLimitConfigDic[AppPushBusiness.BlogSubscribe]['count']
    key = PushBusinessLimitConfigDic[AppPushBusiness.BlogSubscribe]['key']
    store_unit_interval: int = 10 * 60
    tmp_store_his_send_ts: int = 0
    tmp_store_his_send_users = set()


class UserNoticeGroupCountPushLimiter(PushLimiterBase):
    """提醒消息组推送数量限制"""
    default_interval = PushGroupCountLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['interval']
    count_limit = PushGroupCountLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['count']
    key: str = PushGroupCountLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['key']
    store_unit_interval: int = 5 * 60
    tmp_store_his_send_ts: int = 0
    tmp_store_his_send_users = set()


class UserNoticeGroupFrequencyPushLimiter(PushLimiterBase):
    """提醒消息组推送频率限制"""
    default_interval: int = PushGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['interval']
    count_limit = PushGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['count']
    key: str = PushGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.NoticeMsg]['key']
    store_unit_interval: int = 5 * 60
    tmp_store_his_send_ts: int = 0
    tmp_store_his_send_users = set()


class UserCommonGroupCountPushLimiter(PushLimiterBase):
    """一般消息组推送数量限制"""
    default_interval = PushGroupCountLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['interval']
    count_limit = PushGroupCountLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['count']
    key: str = PushGroupCountLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['key']
    store_unit_interval: int = 5 * 60
    tmp_store_his_send_ts: int = 0
    tmp_store_his_send_users = set()


class UserCommonGroupFrequencyPushLimiter(PushLimiterBase):
    """一般消息组推送频率限制"""
    default_interval = PushGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['interval']
    count_limit = PushGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['count']
    key: str = PushGroupFrequencyLimitConfigDic[PushBroadcastLimitGroup.CommonMsg]['key']
    store_unit_interval: int = 5 * 60
    tmp_store_his_send_ts: int = 0
    tmp_store_his_send_users = set()


class UserGlobalLimiter(PushLimiterBase):
    """用户推送全局限制"""
    default_interval: int = 86400
    count_limit = 70
    key = "user_global_push_limit"
    store_unit_interval: int = 5 * 60
    tmp_store_his_send_ts: int = 0
    tmp_store_his_send_users = set()


"""当要给用户发送push时，若设置了该业务push数量限制，push业务所属的组总数/频率限制，用户可发送的全局数量限制时，
需要符合以上数量限制都未达到才可push"""
"""某项配置为None时表示该推送业务/所属的组不需要限制或已经单独在其他地方设置了限制的缓存"""

PUSH_BUSINESS_LIMITER_DIC = {
    AppPushBusiness.Default: {
        'business_limiter': None,
        'group_count_limiter': None,
        'group_frequency_limiter': None,
        'global_limiter': UserGlobalLimiter
    },
    AppPushBusiness.AnnouncementSubscribe: {
        'business_limiter': AppAnnouncementPushLimiter,
        'group_count_limiter': None,
        'group_frequency_limiter': None,
        'global_limiter': UserGlobalLimiter
    },
    AppPushBusiness.ActivitySubscribe: {
        'business_limiter': AppActivitySubscribePushLimiter,
        'group_count_limiter': None,
        'group_frequency_limiter': None,
        'global_limiter': UserGlobalLimiter
    },
    AppPushBusiness.BlogSubscribe: {
        'business_limiter': AppBlogPushLimiter,
        'group_count_limiter': None,
        'group_frequency_limiter': None,
        'global_limiter': UserGlobalLimiter
    },

    AppPushBusiness.UserHoldAssetPrice: {
        'business_limiter': None,
        'group_count_limiter': UserNoticeGroupCountPushLimiter,
        'group_frequency_limiter': UserNoticeGroupFrequencyPushLimiter,
        'global_limiter': UserGlobalLimiter
    },
    AppPushBusiness.UserFavoriteAssetPrice: {
        'business_limiter': None,
        'group_count_limiter': UserNoticeGroupCountPushLimiter,
        'group_frequency_limiter': UserNoticeGroupFrequencyPushLimiter,
        'global_limiter': UserGlobalLimiter
    },
    AppPushBusiness.MarketPriceSubscribe: {
        'business_limiter': None,
        'group_count_limiter': UserNoticeGroupCountPushLimiter,
        'group_frequency_limiter': UserNoticeGroupFrequencyPushLimiter,
        'global_limiter': UserGlobalLimiter
    },
    AppPushBusiness.NoticeGroupStrategyPush: {
        'business_limiter': None,
        'group_count_limiter': UserNoticeGroupCountPushLimiter,
        'group_frequency_limiter': UserNoticeGroupFrequencyPushLimiter,
        'global_limiter': UserGlobalLimiter
    },
    AppPushBusiness.CommonGroupStrategyPush: {
        'business_limiter': None,
        'group_count_limiter': UserCommonGroupCountPushLimiter,
        'group_frequency_limiter': UserCommonGroupFrequencyPushLimiter,
        'global_limiter': UserGlobalLimiter
    },

}


BROADCAST_BUSINESS_CACHE_DIC = {
    AppBroadcastBusiness.Default: {
        'business_cache': None,
        'group_count_cache': None,
        'group_frequency_cache': None,
        'global_limit_cache': GlobalBroadcastLimitCache
    },
    AppBroadcastBusiness.PoplarMarketLevelBreakThrough: {
        'business_cache': None,
        'group_count_cache': CommonGroupBroadcastCountLimitCache,
        'group_frequency_cache': CommonGroupBroadcastFrequencyLimitCache,
        'global_limit_cache': GlobalBroadcastLimitCache
    },
    AppBroadcastBusiness.NewAssetPriceRise: {
        'business_cache': None,
        'group_count_cache': CommonGroupBroadcastCountLimitCache,
        'group_frequency_cache': CommonGroupBroadcastFrequencyLimitCache,
        'global_limit_cache': GlobalBroadcastLimitCache
    },
    AppBroadcastBusiness.NoticeGroupStrategyBroadcast: {
        'business_cache': None,
        'group_count_cache': NoticeGroupBroadcastCountLimitCache,
        'group_frequency_cache': NoticeGroupBroadcastFrequencyLimitCache,
        'global_limit_cache': GlobalBroadcastLimitCache
    },
    AppBroadcastBusiness.CommonGroupStrategyBroadcast: {
        'business_cache': None,
        'group_count_cache': CommonGroupBroadcastCountLimitCache,
        'group_frequency_cache': CommonGroupBroadcastFrequencyLimitCache,
        'global_limit_cache': GlobalBroadcastLimitCache
    },
}


class PushBusinessHandler:
    """app 非广播类push推送相关操作"""

    def __init__(self, business: AppPushBusiness = None):
        if business is None:
            business_info = PUSH_BUSINESS_LIMITER_DIC[AppPushBusiness.Default]
        else:
            business_info = PUSH_BUSINESS_LIMITER_DIC.get(business)
            if not business_info:
                raise InvalidArgument(message='未知的PUSH业务配置！')
        self.limiter_lis = [i for i in business_info.values() if i]

    def set_pushed(self, user_ids: Iterable):
        for limiter in self.limiter_lis:
            limiter.set_pushed(set(user_ids))

    def can_push(self, user_ids: Iterable) -> set:
        """
        校验用户是否可以发送push推送规则：
        用户一定时间内被push的总条数是否达到限制 >> push所属的分组是否达到设置的条数限制 >> 该push业务是否达到设定的条数限制
        """
        valid_user_ids = user_ids
        for limiter in self.limiter_lis:
            valid_user_ids = limiter().can_push(valid_user_ids)
        return valid_user_ids

    def get_limit_users(self):
        res = set()
        for limiter in self.limiter_lis:
            user_ids = limiter().get_limit_users()
            res.update(user_ids)
        return res


class BroadcastBusinessHandler:
    """app广播类push相关操作"""

    def __init__(self, business: AppBroadcastBusiness = None):
        if business is None:
            business_info = BROADCAST_BUSINESS_CACHE_DIC[AppBroadcastBusiness.Default]
        else:
            business_info = BROADCAST_BUSINESS_CACHE_DIC.get(business)
            if not business_info:
                raise InvalidArgument(message='未知的广播业务！')
        self.limiter_cache_lis = [i for i in business_info.values() if i]

    def set_pushed(self):
        for limiter_cache in self.limiter_cache_lis:
            limiter_cache().set_pushed()

    def can_push(self):
        """
        校验广播是否可以发送的规则：
        一定时间段内总push条数是否达到限制 >> 该广播所属的分组是否达到设置的条数限制 >> 该广播业务是否达到设定的条数限制
        """
        for limiter_cache in self.limiter_cache_lis:
            if not limiter_cache().can_push():
                return False
        return True

    def can_push_times(self):
        count_lis = []
        for limiter_cache in self.limiter_cache_lis:
            ret = limiter_cache().can_push_times()
            count_lis.append(ret)
        return min(count_lis)


class PushTaskIdManager:
    """
    管理推送任务和第三方推送服务的task_id关联关系的类
    """
    
    # 推送类型枚举
    class PushType:
        APP_PUSH = 'app_push'
        AUTO_PUSH_STRATEGY = 'auto_push_strategy'
    push_type_config = {
        PushType.APP_PUSH: {
            'model': AppPush,
            'suspend_statuses': (AppPush.Status.SUSPENDING, AppPush.Status.SUSPENDED)
        },
        PushType.AUTO_PUSH_STRATEGY: {
            'model': AutoPushStrategy,
            'suspend_statuses': (AutoPushStrategy.Status.SUSPENDING, AutoPushStrategy.Status.SUSPENDED)
        }
    }

    def __init__(self, push_type: str, push_id: int):
        """
        Args:
            push_type: 推送类型，可以是 APP_PUSH 或 AUTO_PUSH_STRATEGY
            push_id: 推送任务的ID
        """
        self.push_type = push_type
        self.push_id = push_id
        self.cache_key = f"push_task_ids:{push_type}:{push_id}"
        self.cache = SetCache(self.cache_key)
        self.cache.expire(86400)
    
    def add_task_id(self, task_id: str):
        """
        添加新的task_id
        """
        self.cache.sadd(task_id)
    
    def get_task_ids(self) -> List[str]:
        """
        获取当前推送任务关联的所有task_id
        """
        return self.cache.smembers()
    
    def is_push_task_active(self) -> bool:
        """
        判断推送任务是否处于活动状态
        """
        config = self.push_type_config.get(self.push_type)
        if not config:
            return True
        
        record = config['model'].query.get(self.push_id)
        if record and record.status in config['suspend_statuses']:
            return False
        return True
    
    @classmethod
    def for_app_push(cls, push_id: int) -> 'PushTaskIdManager':
        """
        创建用于AppPush的管理器实例
        """
        return cls(cls.PushType.APP_PUSH, push_id)
    
    @classmethod
    def for_auto_push_strategy(cls, strategy_id: int) -> 'PushTaskIdManager':
        """
        创建用于AutoPushStrategy的管理器实例
        """
        return cls(cls.PushType.AUTO_PUSH_STRATEGY, strategy_id)
