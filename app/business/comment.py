


from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from enum import Enum
from flask import current_app
from flask_babel import gettext as _

from app import config

from app.business import ServerClient, SPOT_ACCOUNT_ID
from app.business.lock import CacheLock, LockKeys
from app.business.prices import PriceManager
from app.business.site import BusinessSettings
from app.business.user import UserRepository, UserSettings
from app.business.wallet import add_withdrawal_amount_to_cache, get_user_daily_withdrawal_limit
from app.caches import func
from app.caches.user import UserVisitPermissionCache
from app.common import BalanceBusiness
from app.common.constants import PrecisionEnum
from app.exceptions import InvalidArgument
from app.exceptions.basic import ServiceUnavailable
from app.exceptions.comment import CommentTipAvailableAmountNotEnough
from app.exceptions.legacy import WithdrawalForbidden
from app.models.comment import CommentTipTransfer
from app.models.quotes import CoinInformation
from app.models.wallet import WithdrawalApprover
from app.schedules.comment import notice_tip_finished_task
from app.utils import RESTClient
from app.utils.amount import amount_to_str, quantize_amount
from app.utils.date_ import now
from app.utils.net import get_url_base
from app.models import db

url_base = get_url_base(config['CLIENT_CONFIGS']['comment_internal']['url'])
_client = RESTClient(url_base)

class CommentTipHandler:

    TIP_ASSET = 'CET'

    @classmethod
    def check_tip_available(cls, user_id, receive_user_id, comment_id, amount):
        if amount <= 0:
            raise InvalidArgument
        try:
            r = _client.get(f'/internal/comment/{comment_id}')
        except Exception:
            raise ServiceUnavailable
        r = r['data']
        if not r['comment_id'] or r['user_id'] != receive_user_id:
            raise InvalidArgument
        if user_id == receive_user_id:
            raise InvalidArgument(message=_("不支持打赏自己"))
        settings = UserSettings(user_id)
        if not settings.withdrawals_enabled:
            raise WithdrawalForbidden
        sign_off_users = UserRepository.get_sign_off_users([receive_user_id])
        if sign_off_users:
            raise InvalidArgument(message=_("不支持打赏当前账户"))
        asset_forbidden = cls.TIP_ASSET in UserSettings(receive_user_id).balance_in_assets_disabled_by_risk_control
        if asset_forbidden or UserVisitPermissionCache().check_user_permission(
                receive_user_id,
                [UserVisitPermissionCache.FORBIDDEN_VALUE,
                UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE]
            ):
                raise InvalidArgument(message=_("不支持打赏当前账户"))
        approver = WithdrawalApprover.query.filter(
            WithdrawalApprover.user_id == user_id,
            WithdrawalApprover.is_self.is_(False),
            WithdrawalApprover.status == WithdrawalApprover.Status.VALID,
        ).first()
        if approver:
            raise InvalidArgument(message=_("你设置了提现多人审核，当前无法使用打赏功能"))

        available_amount = cls.get_tip_limit_amount(user_id, cls.TIP_ASSET)['amount']
        if amount > available_amount:
            raise CommentTipAvailableAmountNotEnough(asset=cls.TIP_ASSET, amount=amount_to_str(available_amount))

    @classmethod
    def send_tip(cls, send_user_id: int, receive_user_id: int, comment_id: int, amount: Decimal, asset: str = None):
        asset = asset or cls.TIP_ASSET
        asset_info = CoinInformation.query.filter(CoinInformation.code == asset).with_entities(
            CoinInformation.id, CoinInformation.code
        ).first()
        if not asset_info:
            raise InvalidArgument
        with CacheLock(LockKeys.comment_tip_operation(send_user_id), wait=False): 
            db.session.rollback()
            # 新建打赏资产变更记录
            comment_tip_transfer = CommentTipTransfer(
                source_user_id=send_user_id,
                target_user_id=receive_user_id,
                comment_id=comment_id,
                amount=amount,
                asset=asset,
                asset_id=asset_info.id,
            )
            db.session_add_and_commit(comment_tip_transfer)
            server_client = ServerClient()
            # 扣减发送方资产
            try:
                server_client.add_user_balance(
                    user_id=send_user_id,
                    asset=asset,
                    business=BalanceBusiness.COMMENT_TIP_OUT,
                    business_id=comment_tip_transfer.id,
                    amount=str(-amount),
                    detail={'receive_user_id': receive_user_id},
                    account_id=SPOT_ACCOUNT_ID,
                )
                add_withdrawal_amount_to_cache(send_user_id, asset, -amount, comment_tip_transfer.created_at)
            except Exception as e:
                current_app.logger.error(f"commit tip transfer deduct error {e!r}")
                raise InvalidArgument(message=_("无法打赏"))

            # 更新打赏记录状态
            comment_tip_transfer.status = CommentTipTransfer.Status.DEDUCTED
            db.session.commit()

            # 增加接收方资产
            try:
                server_client.add_user_balance(
                    user_id=receive_user_id,
                    asset=asset,
                    business=BalanceBusiness.COMMENT_TIP_IN,
                    business_id=comment_tip_transfer.id,
                    amount=str(amount),
                    detail={'send_user_id': send_user_id},
                    account_id=SPOT_ACCOUNT_ID,
                )
            except Exception as e:
                current_app.logger.error(f"commit tip transfer add error {e!r}")
                raise InvalidArgument
            # 更新打赏记录状态
            comment_tip_transfer.status = CommentTipTransfer.Status.FINISHED
            db.session.commit()
            notice_tip_finished_task.delay(comment_tip_transfer.id)
    
    @classmethod
    def get_tip_limit_amount(cls, user_id: int, asset: str) -> Decimal:

        class Type(Enum):
            WITHDRAWAL_LIMIT = "WITHDRAWAL_LIMIT"
            BALANCE = "BALANCE"
            TIP_LIMIT = "TIP_LIMIT"

        """打赏限额"""
        tip_history = CommentTipTransfer.query.filter(
            CommentTipTransfer.source_user_id == user_id,
            CommentTipTransfer.status != CommentTipTransfer.Status.FAILED,
            CommentTipTransfer.created_at >= now() - timedelta(days=1),
        ).with_entities(CommentTipTransfer.asset, func.sum(CommentTipTransfer.amount)).group_by(
            CommentTipTransfer.asset
        ).all()
        tip_map = dict(tip_history)
        tipped_amount = tip_map.get(asset, Decimal())
        can_tip_amount = BusinessSettings.tip_limit_amount - tipped_amount
        balance_amount = ServerClient().get_user_balances(user_id, asset).get(asset, {}).get('available', Decimal())
        withdrawal_limit_usd = get_user_daily_withdrawal_limit(user_id)['limit_usd']
        price = PriceManager.asset_to_usd(asset)
        withdrawal_limit_amount = Decimal()
        if price:
            withdrawal_limit_amount = withdrawal_limit_usd / price
            withdrawal_limit_amount = quantize_amount(withdrawal_limit_amount, PrecisionEnum.COIN_PLACES)
        # 可提现数量（复用提现“可提现数量”字段）、每日打赏上限、现货账户可用3者取其小

        amount_types = {
            Type.WITHDRAWAL_LIMIT: withdrawal_limit_amount,
            Type.BALANCE: balance_amount,
            Type.TIP_LIMIT: can_tip_amount,
        }
        min_amount = min(amount_types.values())

        type_ = None
        for k, v in amount_types.items():
            if v == min_amount:
                type_ = k
                break
        return dict(amount=min_amount, type=type_)