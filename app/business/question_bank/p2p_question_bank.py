from flask_babel import gettext as _

# 用于刷脚本使用，上线后可删除
P2P_QUESTIONS = [
    {
        "question": _("我在P2P交易时，交易对手是："),
        "options": [
            _("CoinEx官方"),
            _("发布P2P订单的广告商"),
            _("随机分配的对手"),
            _("我自己的钱包资金"),
        ],
        "answer": 1
    },
    {
        "question": _("频繁取消订单，我会受到什么影响？"),
        "options": [
            _("如待接单状态取消订单，无影响"),
            _("如商家接单后取消3次，会限制当日下单"),
            _("会影响本人的完单率"),
            _("以上都对"),
        ],
        "answer": 3
    },
    {
        "question": _("发现商家提供支付方式内容有误，我应该："),
        "options": [
            _("先抓紧时间转账"),
            _("及时与商家沟通，要求提供正确的支付方式"),
            _("立即申诉并举报商家"),
            _("检查本人设置是否有误"),
        ],
        "answer": 1
    },
    {
        "question": _("买币向商家转账时，我应该："),
        "options": [
            _("使用自己选择的付款方式，并保证实名与平台认证一致"),
            _("不一定要用本人实名的卡，转账金额正确即可"),
            _("可以分批次多笔转账"),
            _("使用来路不明的资金转账"),
        ],
        "answer": 0
    },
    {
        "question": _("确认放币前，我应该："),
        "options": [
            _("不能只看对方截图，需要在选择的收款账号中确认实际到账情况"),
            _("检查转账人是否与商家实名一致"),
            _("检查该笔转账有无异常（如冻结、延迟等）"),
            _("以上都对"),
        ],
        "answer": 3
    },
    {
        "question": _("确认放币后，我还能追回数字货币吗？"),
        "options": [
            _("一旦点击确认放币，系统会自动解冻数字货币并划转至对方账户，无法追回"),
            _("可通过平台协助追回"),
            _("可通过申诉反馈追回"),
            _("可与对方协商退回"),
        ],
        "answer": 0
    },
    {
        "question": _("交易过程中发现对方有异常行为，我应该："),
        "options": [
            _("立即停止交易并发起申诉"),
            _("自行排查原因并继续交易"),
            _("交易完成后再自行申诉"),
            _("取消订单并不再理会"),
        ],
        "answer": 0
    },
    {
        "question": _("申诉过程中需要查看客服最新回复，我应该："),
        "options": [
            _("在聊天窗口和客服进行沟通"),
            _("仅限申诉进度页面在客服进行沟通"),
            _("提交工单询问最新消息"),
            _("向交易对手方询问最新信息"),
        ],
        "answer": 1
    },
]
