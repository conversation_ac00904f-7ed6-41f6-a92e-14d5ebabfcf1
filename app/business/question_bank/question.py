from flask import g
from sqlalchemy import func

from app import Language
from app.models import db
from app.exceptions import RecordNotFound, InvalidArgument
from app.models.mongo import Status
from app.models.mongo.question import QuestionMySQL as Question, QuestionGroupMySQL as QuestionGroup, QuestionDetailSchema
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation


class QuestionBase:

    @classmethod
    def get_by_id(cls, obj_id):
        return cls.model.query.filter(cls.model.mongo_id == obj_id).first()

    @classmethod
    def get_all_valid_query(cls):
        return cls.model.query.filter(cls.model.status == Status.VALID)

    @classmethod
    def delete_by_id(cls, obj_id):
        row = cls.get_by_id(obj_id)
        if not row:
            raise RecordNotFound()
        row.status = Status.INVALID
        db.session.commit()


class QuestionManage(QuestionBase):
    model = Question

    @classmethod
    def create(cls, name, option_type, tag, detail_lang_map):
        if not name:
            name = cls.get_name_by_detail(detail_lang_map)
        
        question = Question(
            name=name,
            option_type=option_type,
            tag=tag,
            detail_lang_map=cls.build_detail_map(option_type, detail_lang_map)
        )
        db.session.add(question)
        db.session.flush()  # 获取自增ID

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Question,
            detail=question.to_dict(),
        )

        db.session.commit()
        return question

    @classmethod
    def update_by_id(cls, obj_id, name, option_type, tag, detail_lang_map):
        row = cls.get_by_id(obj_id)
        if not row:
            raise RecordNotFound()
        if not name:
            name = cls.get_name_by_detail(detail_lang_map)
        
        old_data = row.to_dict()
        row.name = name
        row.option_type = option_type
        row.tag = tag
        row.detail_lang_map = cls.build_detail_map(option_type, detail_lang_map)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Question,
            old_data=old_data,
            new_data=row.to_dict(),
        )

        db.session.commit()

    @classmethod
    def build_detail_map(cls, option_type, detail_lang_map):
        cls.check_detail_map(detail_lang_map)
        if option_type == Question.OptionType.Radio:
            for lang, detail in detail_lang_map.items():
                detail["answer"] = int(detail["answer"])
        return {k.name: QuestionDetailSchema(**v).model_dump() for k, v in detail_lang_map.items()}

    @classmethod
    def check_detail_map(cls, detail_lang_map):
        for lang, detail in detail_lang_map.items():
            for field in ["question", "answer", "options"]:
                if not detail.get(field):
                    raise InvalidArgument(message=f"{lang} 语言问题配置错误，缺少 {field}")

    @classmethod
    def get_name_by_detail(cls, detail_lang_map):
        cn, en = Language.ZH_HANS_CN, Language.EN_US
        lang_data = detail_lang_map.get(cn) or detail_lang_map.get(en)
        return lang_data["question"]

    @classmethod
    def delete_by_id(cls, obj_id):
        super().delete_by_id(obj_id)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Question,
            detail=dict(id=str(obj_id)),
        )


class QuestionGroupManage(QuestionBase):
    model = QuestionGroup

    @classmethod
    def filter_valid_ids(cls, question_ids):
        valid_rows = QuestionManage.get_all_valid_query().filter(
            Question.mongo_id.in_(question_ids)
        ).all()
        return [i.mongo_id for i in valid_rows]  # 转换为字符串存储

    @classmethod
    def create(cls, name, tag, question_ids):
        group = QuestionGroup(
            name=name,
            tag=tag,
            question_ids=cls.filter_valid_ids(question_ids)
        )
        db.session.add(group)
        db.session.flush()  # 获取自增ID

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.QuestionGroup,
            detail=group.to_dict(),
        )

        db.session.commit()
        return group

    @classmethod
    def delete(cls, _id):
        row = cls.get_by_id(_id)
        if not row:
            raise RecordNotFound
        row.status = Status.INVALID
        db.session.commit()

    @classmethod
    def update_by_id(cls, obj_id, name, tag, question_ids):
        row = cls.get_by_id(obj_id)
        if not row:
            raise RecordNotFound()
        
        old_data = row.to_dict()
        row.name = name
        row.tag = tag
        row.question_ids = cls.filter_valid_ids(question_ids)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.QuestionGroup,
            old_data=old_data,
            new_data=row.to_dict(),
        )

        db.session.commit()

    @classmethod
    def query_has_id_group(cls, q_id):
        return cls.model.query.filter(
            func.json_contains(cls.model.question_ids, f'"{q_id}"')
        ).all()

    @classmethod
    def group_remove_id(cls, q_id):
        groups = cls.query_has_id_group(q_id)
        for group in groups:
            group.question_ids = [qid for qid in group.question_ids if qid != str(q_id)]
        db.session.commit()
