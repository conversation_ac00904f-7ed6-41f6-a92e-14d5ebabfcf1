"""
以下是针对抽奖自增号做的一层映射转换
目前Dibs活动使用7位数，参与用户上限为9999999
"""


def get_int_mapping(fix: int):
    return {i: (fix + i) % 10 for i in range(10)}


def get_revert_int_mapping(fix: int):
    return {v: k for k, v in get_int_mapping(fix).items()}


def convert(k: int, n: int, fix: int) -> str:
    s = str(k).zfill(n)
    mapping = get_int_mapping(fix)
    result = []
    for i in range(n):
        transfer_time = i + 1
        transfer_num = _num = int(s[i])
        for _ in range(transfer_time):
            transfer_num = mapping[_num]
            _num = transfer_num
        result.append(transfer_num)
    return ''.join(map(str, result))


def convert_back(k: str, n: int, fix: int) -> str:
    s = str(k).zfill(n)
    mapping = get_revert_int_mapping(fix)
    result = []
    for i in range(n):
        transfer_time = i + 1
        transfer_num = _num = int(s[i])
        for _ in range(transfer_time):
            transfer_num = mapping[_num]
            _num = transfer_num
        result.append(transfer_num)
    return ''.join(map(str, result))
