from itertools import chain

from app.exceptions import InvalidArgument
from app.models import LoginRelationHistory, User
from app.models.activity import NovicePrefectureActivity, CouponApply, CouponPool, UserCoupon


def get_package_apply_ids(task_group_condition):
    tmp = chain.from_iterable([j["coupon_apply_ids"] for i in task_group_condition for j in i["coupon_group"]])
    return list(tmp)


def check_activity_valid(kwargs, novice=None):
    model = NovicePrefectureActivity
    query = model.query.filter(
        model.start_at <= kwargs["end_at"],
        model.end_at >= kwargs["start_at"],
        model.status != model.Status.OFFLINE,
    )
    if novice:
        query = query.filter(model.id != novice.id)
    if query.count():
        raise InvalidArgument(message="活动时间与其他活动冲突，请检查已有 待上架/上架中 活动时间")

    if kwargs["activity_type"] == model.ActivityType.OTHER:
        return

    if kwargs["activity_type"] == model.ActivityType.COUPON:
        if not kwargs["user_group_condition"]:
            raise InvalidArgument(message="请选择卡券领取条件")
        check_novice_coupon([kwargs["coupon_apply_id"]], kwargs["end_at"])
    else:
        if not kwargs["user_group_condition"]:
            raise InvalidArgument(message="请选择活动参与条件")
        check_novice_package(kwargs.get("task_group_condition"), kwargs["end_at"], novice)
        if task_group_condition := kwargs.get("task_group_condition"):
            for task in task_group_condition:
                for group in task["coupon_group"]:
                    group.pop("apply_list", "")


def check_novice_coupon(coupon_apply_ids: list, end_at):
    if not coupon_apply_ids:
        raise InvalidArgument(message="请选择卡券")
    model = CouponApply
    apply_rows = model.query.filter(model.id.in_(coupon_apply_ids)).all()
    for apply in apply_rows:
        if apply.send_at >= end_at:
            raise InvalidArgument(message=f"卡券发放ID {apply.id} 发放时间需要早于活动结束时间")


def check_novice_package(task_group_condition, end_at, novice=None):
    from app.business.user_group import FilterType

    if not task_group_condition:
        raise InvalidArgument(message="请创建新手礼包任务模块")

    max_task_limit = 6
    if len(task_group_condition) > max_task_limit:
        raise InvalidArgument(message=f"最多创建{max_task_limit}个任务模块")

    coupon_apply_ids = get_package_apply_ids(task_group_condition)
    check_novice_coupon(coupon_apply_ids, end_at)
    coupon_apply_set, task_set = set(), set()
    max_condition_limit = 3
    filter_type = FilterType
    period_set = {filter_type.PACKAGE_REGISTER_DAY.name, filter_type.PACKAGE_ACTIVATE_DAY.name}
    amount_set = {filter_type.PACKAGE_DEPOSIT_AMOUNT.name,
                  filter_type.PACKAGE_ASSET_SPOT_AMOUNT.name, filter_type.PACKAGE_PERPETUAL_AMOUNT.name}
    trade_set = {filter_type.PACKAGE_FIRST_OPERATE.name, *amount_set}
    n_model = NovicePrefectureActivity
    for task in task_group_condition:
        task_type = task["task_type"]
        task_name = n_model.TaskType[task_type].value
        if task_type in task_set:
            raise InvalidArgument(message=f"不能创建相同任务模块 {task_name}")
        task_set.add(task_type)
        coupon_group = task["coupon_group"]
        for coupon in task["coupon_group"]:
            coupon_apply_ids = coupon["coupon_apply_ids"]
            for apply_id in coupon_apply_ids:
                if apply_id in coupon_apply_set:
                    raise InvalidArgument(message=f"不能添加相同卡券发放 {apply_id}")
            condition = coupon["condition"]
            coupon_apply_set.update(coupon_apply_ids)
            keys = {i["key"] for i in condition}
            base_err = f"任务模块 {task_name} 卡券发放 {coupon_apply_ids} "
            if keys & period_set and not keys & trade_set:
                raise InvalidArgument(message=f"{base_err} 不能单独添加统计周期条件，需要搭配 首次行为/额度要求 条件")
            if len(keys & period_set) > 1:
                raise InvalidArgument(message=f"{base_err} 不能同时添加多个 统计周期 条件")
            if len([i["key"] for i in condition if i["key"] in amount_set]) > 1:
                raise InvalidArgument(message=f"{base_err} 不能同时添加多个 额度要求 条件")
        if len(coupon_group) > max_condition_limit and not novice:
            raise InvalidArgument(
                message=f"任务模块 {task_name} 卡券领取条件超出限制，最多配置 {max_condition_limit} 个领取条件")


def get_package_apply_condition_map(task_group_condition):
    apply_map = {}
    for task in task_group_condition:
        for group in task["coupon_group"]:
            apply_ids = group["coupon_apply_ids"]
            for apply_id in apply_ids:
                apply_map[apply_id] = group["condition"]
    return apply_map


def check_condition_dup_receive(user_id, pool, task_group_condition):
    apply_id = pool.apply_coupon_id
    apply_ids = []
    for task in task_group_condition:
        for group in task["coupon_group"]:
            if apply_id in group["coupon_apply_ids"]:
                apply_ids = group["coupon_apply_ids"]
                break

    if not apply_ids:
        return False

    p_model = CouponPool
    rows = CouponPool.query.filter(p_model.apply_coupon_id.in_(apply_ids)).with_entities(p_model.id).all()
    pool_ids = [i.id for i in rows]

    u_model = UserCoupon
    u_count = UserCoupon.query.filter(u_model.pool_id.in_(pool_ids), u_model.user_id == user_id).count()
    return u_count == 0


def get_package_pool_status(pool):
    model = CouponPool
    if pool:
        if pool.status == model.Status.EXPIRED:
            pool_status = "已过期"
        elif pool.status == model.Status.PAUSED:
            pool_status = "已暂停"
        elif pool.send_count >= pool.total_count:
            pool_status = "已领完"
        else:
            pool_status = "发放中"
    else:
        pool_status = "待发放"
    return pool_status


def get_novice_source_user_ids(novice: NovicePrefectureActivity):
    from app.business.user_group import get_novice_package_user_st_et
    from app.business.utils import yield_query_records_by_time_range

    model = NovicePrefectureActivity
    if novice.activity_type == model.ActivityType.PACKAGE:
        st, et = get_novice_package_user_st_et(novice)
    else:
        st, et = novice.start_at, novice.end_at

    u_model = User
    user_types = [u_model.UserType.NORMAL,
                  u_model.UserType.EXTERNAL]
    user_ids = []
    for row in yield_query_records_by_time_range(
            u_model, st, et, [u_model.id], u_model.user_type.in_(user_types)
    ):
        user_ids.append(row.id)
    return user_ids


def get_novice_user_ids(novice: NovicePrefectureActivity) -> list[int]:
    from app.caches.activity import NoviceRiskUserCache
    user_ids = get_novice_source_user_ids(novice)
    risk_user_ids = NoviceRiskUserCache(novice.id).get_user_ids()
    user_ids = list(set(user_ids) - set(risk_user_ids))
    return user_ids


def get_novice_risk_user(novice_id: int, user_id=None, page=None, limit=None) -> (list, int):
    from app.caches.activity import NoviceRiskUserCache

    empty_ret = [], 0
    risk_user_ids = NoviceRiskUserCache(novice_id).get_user_ids()

    if user_id:
        if user_id in risk_user_ids:
            risk_user_ids = [user_id]
        else:
            return empty_ret

    total = len(risk_user_ids)
    if page and limit:
        risk_user_ids = list(risk_user_ids)[(page - 1) * limit: page * limit]
    if not risk_user_ids:
        return empty_ret

    u_model = User
    user_row = u_model.query.filter(
        u_model.id.in_(risk_user_ids)
    ).with_entities(u_model.id, u_model.email, u_model.created_at).all()

    login_model = LoginRelationHistory
    activity_rows = login_model.query.filter(
        login_model.user_id.in_(risk_user_ids),
        login_model.is_registration.is_(True),
    ).with_entities(
        login_model.user_id, login_model.device_id
    ).all()
    old_user_device_map = {i.user_id: i.device_id for i in activity_rows}

    ret = []
    for idx, user in enumerate(user_row):
        _id = idx + 1
        if page and limit:
            _id += (page - 1) * limit
        ret.append({
            "id": _id,
            "user_id": user.id,
            "email": user.email,
            "device_id": old_user_device_map[user.id],
            "risk_type": "相同设备",
            "status": "生效中",
            "risk_time": user.created_at
        })

    return ret, total
