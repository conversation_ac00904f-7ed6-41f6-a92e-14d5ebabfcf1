import datetime
import json
from collections import defaultdict, namedtuple
from decimal import Decimal

from dateutil import relativedelta
from sqlalchemy import or_, and_
from app import config
from flask import current_app
from app.business import ExchangeLogDB

from app.common import PrecisionEnum
from app.models import (
    db, SubAccount, User, DepositBonusActivity,
    DepositBonusActivityConfig, DepositBonusActivityApplyUser, Deposit, Withdrawal,
    DepositBonusActivityUserInfo, AssetPrice, RedPacket
)

from app.utils import quantize_amount, batch_iter, now, current_timestamp, timestamp_to_datetime
from app.utils.parser import JsonEncoder


class DepositBonusActivityBusiness:
    model = DepositBonusActivity
    config_model = DepositBonusActivityConfig

    def __init__(self, activity_id):
        self.activity = self.model.query.get(activity_id)

        # 缓存数据
        # 一个活动下，一个人参加多个不同子活动的共用数据
        self.first_user_deposits = set()
        self.comeback_users = set()
        self.user_deposits = {}
        self.user_withdrawals = defaultdict(list)
        self.user_red_packets = defaultdict(list)

    def update_user_infos(self):
        """更新用户统计信息"""
        configs = self.config_model.query.filter(
            self.config_model.deposit_bonus_id == self.activity.id
        ).all()
        for cfg in configs:
            self._update_user_infos_by_cfg(cfg)

    def _update_user_infos_by_cfg(self, cfg):
        """按子活动更新用户统计信息"""
        now_ = now()
        applying_users_mapping = self.get_applying_users(cfg.id)
        deposit_asset, deposit_chains = self.activity.asset, self.activity.get_chains()
        applying_users = list(applying_users_mapping.keys())

        self._update_user_deposits(applying_users_mapping, deposit_asset, deposit_chains)
        self._update_user_withdrawals(applying_users_mapping, deposit_asset)
        self._update_user_red_packet(applying_users_mapping, deposit_asset)

        new_users, old_users, old_comeback_users = self._get_new_old_users(applying_users)

        user_statics = self._get_user_statics(cfg.id)

        self._update_user_first_deposits(new_users, old_users, set(self.user_deposits.keys()), set(user_statics.keys()))

        self._update_comeback_users(old_comeback_users)

        to_ranking_data = self._get_to_ranking_data()
        ret = []
        model = DepositBonusActivityUserInfo
        for user_id in applying_users:
            user_info = user_statics.get(user_id)
            if user_info is None:
                user_info = model(
                    user_id=user_id,
                    deposit_bonus_id=self.activity.id,
                    activity_id=cfg.id,
                )
            user_info.report_at = now_
            if not user_info.deposit_at:
                user_info.deposit_at = to_ranking_data[user_id].get('deposit_at')
            user_info.amount = to_ranking_data[user_id].get('amount', Decimal())
            user_info.net_amount = to_ranking_data[user_id].get('net_amount', Decimal())
            user_info.type = model.Type.NEW if user_id in new_users else model.Type.OLD
            user_info.is_comeback = user_id in self.comeback_users and user_info.net_amount > Decimal()
            if not user_info.id:  # 只在第一次进行更新，后续数据没有去拿已经存在记录的信息
                user_info.is_first_deposit = user_id in self.first_user_deposits
            ret.append(user_info)
        for rows in batch_iter(ret, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()

        self._update_rankings(cfg)

    def _get_new_old_users(self, applying_users) -> (set[int], set[int], set[int]):
        user_infos = self._get_user_infos(applying_users)
        new_users, old_users, old_comeback_users = set(), set(), set()
        for user_id, created_at in user_infos.items():
            if created_at >= self.activity.start_time:
                new_users.add(user_id)
            else:
                old_users.add(user_id)
                if created_at < self.activity.start_time + relativedelta.relativedelta(months=-3):
                    old_comeback_users.add(user_id)
        return new_users, old_users, old_comeback_users

    def _get_to_ranking_data(self) -> dict:
        to_ranking_data = defaultdict(dict)
        UObj = namedtuple("UObj", ["user_id", "amount", "created_at", "is_deposit"])
        for user_id, deposits in self.user_deposits.items():
            withdrawals = self.user_withdrawals[user_id]
            red_packets = self.user_red_packets[user_id]
            deposits = [UObj(x.user_id, x.amount, x.created_at, True) for x in deposits]
            withdrawals = [UObj(x.user_id, x.amount, x.created_at, False) for x in withdrawals]
            red_packets = [UObj(x.user_id, x.total_amount, x.effective_at, False) for x in red_packets]
            deposits_withdrawals_red_packets = []
            deposits_withdrawals_red_packets.extend(deposits)
            deposits_withdrawals_red_packets.extend(withdrawals)
            deposits_withdrawals_red_packets.extend(red_packets)
            sorted_deposits_withdrawals = sorted(deposits_withdrawals_red_packets, key=lambda x: x.created_at)
            net_amount = Decimal()
            total_amount = Decimal()
            for dw in sorted_deposits_withdrawals:
                if dw.is_deposit:
                    net_amount += dw.amount
                    total_amount += dw.amount
                else:
                    net_amount -= dw.amount
                if net_amount >= self.activity.threshold:
                    if to_ranking_data[user_id].get('deposit_at'):
                        continue
                    # 统计第一笔净充值达标时间
                    assert dw.is_deposit
                    to_ranking_data[user_id]['deposit_at'] = dw.created_at
            to_ranking_data[user_id]['net_amount'] = net_amount
            to_ranking_data[user_id]['amount'] = total_amount
        return to_ranking_data

    def get_applying_users(self, activity_id: int) -> dict:
        model = DepositBonusActivityApplyUser
        rows = model.query.with_entities(
            model.user_id,
            model.created_at,
        ).filter(
            model.deposit_bonus_id == self.activity.id,
            model.activity_id == activity_id,
        ).all()
        return {row.user_id: row.created_at for row in rows}

    def _update_user_deposits(
            self,
            applying_users_mapping: dict[str, datetime.datetime],
            asset: str,
            chains: list[str]
    ):
        model = Deposit
        pool_user_id = config['CLIENT_CONFIGS']['viabtc_pool']['user_id']
        type_filter = or_(
            and_(
                model.type == model.Type.ON_CHAIN,
                model.chain.in_(chains),
            ),
            and_(
                model.type == model.Type.LOCAL,
                model.sender_user_id == pool_user_id,
            )
        )
        ret = defaultdict(list)
        query_users = set(applying_users_mapping.keys()) - set(self.user_deposits.keys())
        for batch_ids in batch_iter(query_users, 5000):
            query = model.query.filter(
                model.asset == asset,
                model.created_at >= self.activity.start_time,
                model.created_at < self.activity.end_time,
                model.user_id.in_(batch_ids),
                model.status.in_([
                    model.Status.FINISHED,
                    model.Status.TO_HOT,
                    model.Status.CONFIRMING,
                ]),
                *[type_filter],
            )
            rows = query.with_entities(
                model.user_id,
                model.asset,
                model.amount,
                model.created_at,
            ).all()
            for row in rows:
                ret[row.user_id].append(row)
        self.user_deposits.update(ret)

    def _update_user_withdrawals(
            self,
            applying_users_mapping: dict[str, datetime.datetime],
            asset: str
    ):
        model = Withdrawal
        query_users = set(applying_users_mapping.keys()) - set(self.user_withdrawals.keys())
        for batch_ids in batch_iter(query_users, 5000):
            query = model.query.filter(
                model.asset == asset,
                model.created_at >= self.activity.start_time,
                model.created_at < self.activity.end_time,
                model.user_id.in_(batch_ids),
                model.status.in_([
                    model.Status.FINISHED,
                    model.Status.CONFIRMING,
                ]),
            )
            rows = query.with_entities(
                model.user_id,
                model.amount,
                model.created_at,
            ).all()
            for row in rows:
                self.user_withdrawals[row.user_id].append(row)

    def _update_user_red_packet(
            self,
            applying_users_mapping: dict[str, datetime.datetime],
            asset: str
    ):
        model = RedPacket
        query_users = set(applying_users_mapping.keys()) - set(self.user_red_packets.keys())
        for batch_ids in batch_iter(query_users, 5000):
            rows = model.query.filter(
                model.user_id.in_(batch_ids),
                model.asset == asset,
                model.effective_at >= self.activity.start_time,
                model.effective_at < self.activity.end_time,
                model.status.in_([model.Status.DEDUCTED,
                                  model.Status.PASSED,
                                  model.Status.EXPIRED,
                                  model.Status.FINISHED])
            ).with_entities(
                model.user_id,
                model.effective_at,
                model.total_amount,
            ).all()
            for row in rows:
                self.user_red_packets[row.user_id].append(row)

    @staticmethod
    def _get_user_infos(user_ids: list[int]) -> dict:
        ret = {}
        for chunk_user_ids in batch_iter(user_ids, 5000):
            rows = User.query.with_entities(
                User.id,
                User.created_at
            ).filter(
                User.id.in_(chunk_user_ids)
            ).all()
            ret.update(dict(rows))
        return ret

    def _get_user_statics(self, activity_id: int) -> dict:
        model = DepositBonusActivityUserInfo
        rows = model.query.filter(
            model.deposit_bonus_id == self.activity.id,
            model.activity_id == activity_id,
        ).all()
        return {row.user_id: row for row in rows}

    def _update_user_first_deposits(
            self,
            new_users: set[int],
            old_users: set[int],
            deposit_users: set[int],
            records_users: set[int],
    ):
        model = Deposit
        deposit_old_users = set()
        to_updated_old_users = old_users - records_users - self.first_user_deposits
        for chunk_uids in batch_iter(to_updated_old_users, 5000):
            rows = model.query.with_entities(
                model.user_id
            ).filter(
                model.asset == self.activity.asset,
                model.type == model.Type.ON_CHAIN,
                model.user_id.in_(chunk_uids),
                model.status.in_([
                    model.Status.FINISHED,
                    model.Status.TO_HOT,
                    model.Status.CONFIRMING,
                ]),
                model.created_at < self.activity.start_time,
            ).group_by(
                model.user_id
            ).all()
            deposit_old_users |= {row.user_id for row in rows}

        self.first_user_deposits |= new_users & deposit_users
        checks = to_updated_old_users - deposit_old_users
        self.first_user_deposits |= checks & deposit_users

    def _update_comeback_users(
            self,
            old_users: set[int],
    ):
        def _get_user_balance_sum(user_ids):
            """获取用户总资产快照（未按主账号汇总）"""
            ret = {}
            ts = int(self.activity.start_time.timestamp())
            last_ts = ts - ts % (60 * 60 * 24)
            table = ExchangeLogDB.user_account_balance_sum_table(last_ts)
            if not table.exists():
                current_app.logger.warning(f"<deposit_bonus>table does not exist: {table}")
                return ret
            for chunk_user_ids in batch_iter(user_ids, 5000):
                user_id_str = ','.join(map(str, chunk_user_ids))
                rows = table.select(
                    'user_id', 'balance_usd',
                    where=f' user_id in ({user_id_str})'
                )
                for (user_id, balance_usd) in rows:
                    ret.update({user_id: balance_usd})
            return ret

        to_updated_old_users = old_users - self.comeback_users
        sub_to_main = self._get_sub_to_main(to_updated_old_users)
        query_users = to_updated_old_users | set(sub_to_main.keys())
        user_balances = _get_user_balance_sum(query_users)
        sum_user_balances = defaultdict(Decimal)
        for uid, balance_usd in user_balances.items():
            main_uid = sub_to_main.get(uid, uid)
            sum_user_balances[main_uid] += balance_usd
        self.comeback_users |= to_updated_old_users - set(sum_user_balances.keys())
        self.comeback_users |= {uid for uid, balance_usd in sum_user_balances.items() if balance_usd == Decimal()}

    @staticmethod
    def _get_sub_to_main(user_ids):
        ret = dict()
        model = SubAccount
        for ids in batch_iter(user_ids, 5000):
            tmp = model.query.filter(
                model.main_user_id.in_(ids)
            ).with_entities(model.user_id, model.main_user_id).all()
            ret.update(dict(tmp))
        return ret

    def _update_rankings(self, cfg):
        model = DepositBonusActivityUserInfo
        rows = model.query.filter(
            model.deposit_bonus_id == self.activity.id,
            model.activity_id == cfg.id,
        ).all()
        rankings = self._get_rankings_by(cfg, rows)
        for row in rows:
            reward = rankings[row.user_id]
            row.rank = reward.get('rank')
            row.gift_status = reward.get('gift_status', model.GiftStatus.NONE)
            row.gifts = json.dumps(reward.get('gifts'), cls=JsonEncoder) if reward.get('gifts') else ''
        db.session.commit()

    def _get_rankings_by(self, cfg, qualified_rows):
        ts = current_timestamp(to_int=True)
        now_ts = ts - ts % 3600
        end_ts = int(self.activity.end_time.timestamp())
        end_ts -= end_ts % 3600
        update_time = min(end_ts, now_ts)
        updated_at = timestamp_to_datetime(update_time)
        asset_rates = AssetPrice.get_price_map(updated_at)
        cfg: DepositBonusActivityConfig
        deposit_at_not_null_rows = [x for x in qualified_rows if x.deposit_at]
        deposit_at_not_null_rows = sorted(deposit_at_not_null_rows, key=lambda x: x.deposit_at)
        rankings = defaultdict(dict)
        model = DepositBonusActivityUserInfo
        total_net_amount_mapping = {}
        for idx, row in enumerate(deposit_at_not_null_rows, start=1):
            gifts = []
            gift_status = model.GiftStatus.NONE
            if row.net_amount < self.activity.threshold:
                # 达标时间是第一次统计出来的，如果后续净充值小于活动充值阈值，则无奖励
                rankings[row.user_id]['rank'] = idx
                rankings[row.user_id]['gift_status'] = gift_status
                rankings[row.user_id]['gifts'] = gifts
                continue
            for rule_idx, gift_rule in enumerate(cfg.cached_gift_rules, start=1):
                gift = {}
                if gift_rule['rank_min'] <= idx <= gift_rule['rank_max']:
                    if rule_idx not in total_net_amount_mapping:
                        total_net_amount = sum([
                            x.net_amount for i, x in enumerate(deposit_at_not_null_rows, start=1) if
                            gift_rule['rank_min'] <= i <= gift_rule[
                                'rank_max'] and x.net_amount >= self.activity.threshold
                        ])
                        total_net_amount_mapping[rule_idx] = total_net_amount
                    else:
                        total_net_amount = total_net_amount_mapping[rule_idx]
                    gift_status = model.GiftStatus.CREATED
                    gift['gift_type'] = gift_rule['gift_type']
                    if gift_rule['gift_type'] == cfg.GiftType.COUPON.name:
                        gift['coupon_apply_id'] = gift_rule['coupon_apply_id']
                        gift['gift_amount'] = gift_rule['rank_limit']
                    else:
                        deposit_asset_price = asset_rates.get(self.activity.asset, 0)
                        gift['gift_asset'] = gift_rule['gift_asset']
                        if cfg.mode is cfg.ActivityMode.PROPORTION:
                            gift_amount = row.net_amount * gift_rule['proportion']
                            if gift_rule['gift_asset'] != self.activity.asset:
                                gift_asset_usd = row.net_amount * deposit_asset_price * gift_rule['proportion']
                                gift_asset_price = asset_rates.get(gift_rule['gift_asset'], 0)
                                gift_amount = (gift_asset_usd / gift_asset_price) if gift_asset_price else Decimal()
                        else:
                            total_net_amount_usd = total_net_amount * deposit_asset_price
                            net_amount_usd = row.net_amount * deposit_asset_price
                            ratio = (net_amount_usd / total_net_amount_usd) if total_net_amount_usd else Decimal()
                            gift_amount = ratio * gift_rule['rank_total']
                        gift_amount = min([gift_amount, gift_rule['rank_limit']])
                        gift['gift_amount'] = quantize_amount(gift_amount, PrecisionEnum.COIN_PLACES)
                    gifts.append(gift)
            rankings[row.user_id]['rank'] = idx
            rankings[row.user_id]['gift_status'] = gift_status
            rankings[row.user_id]['gifts'] = gifts
        return rankings
