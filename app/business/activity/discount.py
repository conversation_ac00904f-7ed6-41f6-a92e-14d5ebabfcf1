from app.business import ServerClient, current_app, BalanceBusiness, db, InvalidArgument, CacheLock, \
    LockKeys
from app.caches.activity import DiscountActivityLotteryCache
from app.models import DiscountActivityOrder, DiscountActivity, amount_to_str, DiscountActivityLotteryHistory, config


def process_discount_activity_order(order_id):
    with CacheLock(LockKeys.discount_order(order_id), wait=False):
        db.session.rollback()
        order = DiscountActivityOrder.query.filter(
            DiscountActivityOrder.id == order_id,
            DiscountActivityOrder.status == DiscountActivityOrder.StatusType.CREATED,
        ).first()
        if not order:
            return
        activity_id = order.discount_activity_id
        activity = DiscountActivity.query.filter(
            DiscountActivity.id == activity_id,
        ).first()
        pay_asset = DiscountActivity.PAY_ASSET
        total_pay_amount = activity.pay_amount
        user_id = order.user_id
        client = ServerClient(current_app.logger)
        if order.asset_status == DiscountActivityOrder.AssetStatusType.PENDING_DEDUCT:
            try:
                client.add_user_balance(
                    user_id=user_id,
                    asset=pay_asset,
                    amount=amount_to_str(-total_pay_amount, 8),
                    business=BalanceBusiness.DIBS_ACTIVITY_SUBSCRIBE,
                    business_id=order.id,
                )
                order.asset_status = DiscountActivityOrder.AssetStatusType.DEDUCTED
            except Exception as e:
                current_app.logger.error("DEDUCTED:{} error:{}".format(order.id, e))
                raise InvalidArgument
            finally:
                db.session.commit()
        if order.asset_status == DiscountActivityOrder.AssetStatusType.DEDUCTED:
            try:
                client.add_user_balance(
                    user_id=config['DISCOUNT_ADMIN_USER_ID'],
                    asset=pay_asset,
                    amount=amount_to_str(total_pay_amount, 8),
                    business=BalanceBusiness.DIBS_ACTIVITY_SUBSCRIBE,
                    business_id=order.id,
                )
                order.asset_status = DiscountActivityOrder.AssetStatusType.FINISHED
            except Exception as e:
                current_app.logger.error("FINISHED:{} error:{}".format(order.id, e))
                raise InvalidArgument
            finally:
                db.session.commit()

        lottery_number = DiscountActivityLotteryCache().hincrby(activity_id, 1)
        row = DiscountActivityLotteryHistory(user_id=user_id, lottery_number=lottery_number,
                                             discount_activity_id=activity_id)
        db.session.add(row)
        order.status = DiscountActivityOrder.StatusType.VALID
        db.session.commit()
        return row.lottery_number_zfill
