from datetime import timedelta

from flask import current_app

from app.models import db
from app.models.user import User
from app.models.summary import UserTradeFeeSummary
from app.models.summary import UserExchangeSummary
from app.models.activity import GuideToRateTrustpilotEmail

from app.business.user import UserPreferences
from app.business.email import send_guide_to_rate_trustpilot_email

from app.common.constants import Language

from app.utils.iterable import batch_iter
from app.utils.date_ import today, date_to_datetime, cur_month, next_month


def count_guide_to_rate_trustpilot_email() -> (int, int, int, int):
    """返回当月推送新用户数量、当月推送老用户数量、当天推送新用户数量、当天推送老用户数量"""
    _today = today()
    start_month_dt = date_to_datetime(cur_month(_today.year, _today.month))
    end_month_dt = date_to_datetime(next_month(_today.year, _today.month))
    start_day_dt = date_to_datetime(_today)
    end_day_dt = start_day_dt + timedelta(days=1)

    _t = GuideToRateTrustpilotEmail.Type
    month_count = {_t.NEW_USER: 0, _t.OLD_USER: 0}
    day_count = {_t.NEW_USER: 0, _t.OLD_USER: 0}
    for row in GuideToRateTrustpilotEmail.query.filter(
        GuideToRateTrustpilotEmail.created_at >= start_month_dt,
        GuideToRateTrustpilotEmail.created_at < end_month_dt,
    ).all():
        month_count[row.type] += 1
        if start_day_dt <= row.created_at < end_day_dt:
            day_count[row.type] += 1

    return month_count[_t.NEW_USER], month_count[_t.OLD_USER], day_count[_t.NEW_USER], day_count[_t.OLD_USER]


def send_guide_to_rate_trustpilot_user_ids(can_send_new: int, can_send_old: int) -> (dict[int, str], dict[int, str]):
    _today = today()

    sent_user_ids = set()
    for row in GuideToRateTrustpilotEmail.query.filter().with_entities(
        GuideToRateTrustpilotEmail.user_id,
    ).all():
        sent_user_ids.add(row.user_id)

    d = _today + timedelta(days=-1)
    all_user_ids = set()
    for row in UserTradeFeeSummary.query.filter(
        UserTradeFeeSummary.report_date == d,
    ).with_entities(
        UserTradeFeeSummary.user_id,
    ).all():
        if row.user_id not in sent_user_ids:
            all_user_ids.add(row.user_id)
    for row in UserExchangeSummary.query.filter(
        UserExchangeSummary.report_date == d,
    ).with_entities(
        UserExchangeSummary.user_id,
    ).all():
        if row.user_id not in sent_user_ids:
            all_user_ids.add(row.user_id)

    three_months_ago = date_to_datetime(_today + timedelta(days=-90))
    new_user_map, old_user_map = {}, {}
    for user_ids in batch_iter(list(all_user_ids), 2000):
        for user in User.query.filter(
            User.id.in_(user_ids),
        ).with_entities(
            User.id,
            User.created_at,
            User.user_type,
            User.email,
        ).all():
            if user.user_type != User.UserType.NORMAL:
                continue
            if not user.email:
                continue
            if UserPreferences(user.id).language != Language.EN_US:
                # 只发送英语用户
                continue
            if user.created_at >= three_months_ago:
                if len(new_user_map) < can_send_new:
                    new_user_map[user.id] = user.email
            else:
                if len(old_user_map) < can_send_old:
                    old_user_map[user.id] = user.email
        if len(new_user_map) >= can_send_new and len(old_user_map) >= can_send_old:
            break

    return new_user_map, old_user_map


def send_email(user_id_map: dict[int, str], is_new_user: bool):
    emails = []
    for user_id, email in user_id_map.items():
        send_guide_to_rate_trustpilot_email(user_id, is_new_user)

        emails.append(GuideToRateTrustpilotEmail(
            user_id=user_id,
            email=email,
            type=GuideToRateTrustpilotEmail.Type.NEW_USER if is_new_user else GuideToRateTrustpilotEmail.Type.OLD_USER,
        ))

        current_app.logger.info(f'send guide to rate trustpilot email, '
                                f'user_id: {user_id}, email: {email}, is_new_user: {is_new_user}')

    if len(emails) > 0:
        db.session.add_all(emails)
        db.session.commit()


def do_send_guide_to_rate_trustpilot_email():
    new_user_per_day = 100  # 新用户每天发100封
    new_user_per_month = 1000   # 新用户每月发1000封
    old_user_per_day = 400  # 老用户每天发400封
    old_user_per_month = 4000   # 老用户每月发1000封

    month_new, month_old, day_new, day_old = count_guide_to_rate_trustpilot_email()
    can_send_new = min(new_user_per_month - month_new, new_user_per_day - day_new)
    can_send_old = min(old_user_per_month - month_old, old_user_per_day - day_old)
    if can_send_new <= 0 and can_send_old <= 0:
        return

    new_user_id_map, old_user_id_map = send_guide_to_rate_trustpilot_user_ids(can_send_new, can_send_old)
    send_email(new_user_id_map, True)
    send_email(old_user_id_map, False)
