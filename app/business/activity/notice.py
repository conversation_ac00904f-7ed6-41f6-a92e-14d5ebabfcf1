import json
from collections import defaultdict
from decimal import Decimal
from json import loads as json_loads
from datetime import timedelta, datetime
from typing import Set

from flask_babel import gettext, force_locale

from app.business import filter_active_users, ServerClient, PriceManager, BalanceManager, ExchangeLogDB
from app.business.operation import ActivityAutoPushHelper
from app.business.trade import get_all_user_trade_map, get_all_user_spot_trade_map, get_all_user_perpetual_map, \
    get_all_exchange_tade_map, get_all_user_amm_trade_map, get_all_user_spot_grid_trade_map, get_all_user_auto_invest_trade_map
from app.business.user_group import TradeRankUserGroupValidator
from app.config import config
from app.models import TradeRankActivity, AirdropActivity, DiscountActivity, TradeRankActivityJoinUser, User, AirdropActivityCondition, \
    DiscountActivityCondition, TradeRankActivityDetail, AirdropActivityDetail, AirdropActivityReward, ActivityCondition, \
    EmailPushContent, DiscountActivityDetail, EmailPush, AppPush, AutoPushHistory, db, SubAccount, DiscountActivityStatistic, \
    AirdropActivityStatistic, VipUser, LiquidityHistory, InvestmentBalanceHistory, FiatOrder, UserTradeSummary, MarginLoanOrder
from app.models.auto_invest import AutoInvestPlan
from app.models.exchange import AssetExchangeOrder
from app.models.pledge import PledgePosition
from app.models.strategy import UserStrategy
from app.models.activity import LaunchMiningProject, LaunchMiningPool
from app.models.operation import NotificationBar
from app.utils import today, current_timestamp, timestamp_to_datetime, url_join, datetime_to_str, amount_to_str, batch_iter,  \
    today_datetime, now


class ActivityAutoNotice:

    NoticeExpireTime = timedelta(hours=6)

    @classmethod
    def get_90days_active_users(cls) -> set[int]:
        end_date = today()
        start_date = end_date - timedelta(days=90)
        active_user_ids = filter_active_users(start_date, end_date)
        user_main_map = {}
        for ids_ in batch_iter(active_user_ids, 2000):
            chunk_users = SubAccount.query.filter(
                SubAccount.user_id.in_(ids_)
            ).with_entities(
                SubAccount.user_id,
                SubAccount.main_user_id
            ).all()
            user_main_map.update(dict(chunk_users))
        sub_ids = set(user_main_map.keys())
        main_ids = set(user_main_map.values())
        user_ids = (active_user_ids - sub_ids) | main_ids
        return user_ids

    @classmethod
    def notice(cls):
        ts = current_timestamp()
        ts = ts - ts % 600
        dt = timestamp_to_datetime(ts)
        cls._trade_rank_activity_start_email(dt)
        cls._spot_trade_rank_activity_end_app_push(dt)
        cls._perpetual_trade_rank_activity_start_app_push(dt)
        cls._perpetual_trade_rank_activity_end_app_push(dt)
        cls._airdrop_activity_start_email(dt)
        cls._airdrop_activity_end_app_push(dt)
        cls._discount_activity_start_email(dt)
        cls._discount_activity_end_app_push(dt)
        cls._launch_pool_pre_notices(dt)
        cls._launch_pool_start_notices(dt)
        cls._launch_pool_end_notices(dt)

    @classmethod
    def _is_auto_push_exist(cls, push_type: AutoPushHistory.PushType, business: AutoPushHistory.Business, activity_id: int):
        row = AutoPushHistory.query.filter(
            AutoPushHistory.push_type == push_type,
            AutoPushHistory.business == business,
            AutoPushHistory.business_key == str(activity_id)
        ).first()
        return bool(row)

    @classmethod
    def _add_auto_push(cls, push_type: AutoPushHistory.PushType, business: AutoPushHistory.Business, activity_id: int,
                       expired_at: datetime):
        row = AutoPushHistory(
            push_type=push_type,
            business=business,
            business_key=str(activity_id),
            expired_at=expired_at
        )
        db.session_add_and_commit(row)

    @classmethod
    def _trade_rank_activity_start_email(cls, dt: datetime):
        push_type = AutoPushHistory.PushType.EmailPush
        business = AutoPushHistory.Business.TradeRankStart
        rows = TradeRankActivity.query.filter(
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
        ).with_entities(
            TradeRankActivity.id,
            TradeRankActivity.started_at
        ).all()
        for row in rows:
            if dt < row.started_at + timedelta(hours=2) or dt >= row.started_at + timedelta(hours=2) + cls.NoticeExpireTime:
                continue
            if cls._is_auto_push_exist(push_type, business, row.id):
                continue
            expired_at = row.started_at + timedelta(hours=2) + cls.NoticeExpireTime
            cls._add_auto_push(push_type, business, row.id, expired_at)

    @classmethod
    def _spot_trade_rank_activity_end_app_push(cls, dt: datetime):
        push_type = AutoPushHistory.PushType.AppPush
        business = AutoPushHistory.Business.TradeRankEnd
        rows = TradeRankActivity.query.filter(
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
            TradeRankActivity.type.in_(TradeRankActivity.SPOT_TYPES),
        ).with_entities(
            TradeRankActivity.id,
            TradeRankActivity.ended_at
        ).all()
        for row in rows:
            if dt < row.ended_at - timedelta(hours=48) or dt >= row.ended_at - timedelta(hours=48) + cls.NoticeExpireTime:
                continue
            if cls._is_auto_push_exist(push_type, business, row.id):
                continue
            expired_at = row.ended_at - timedelta(hours=48) + cls.NoticeExpireTime
            cls._add_auto_push(push_type, business, row.id, expired_at)

    @classmethod
    def _perpetual_trade_rank_activity_start_app_push(cls, dt: datetime):
        push_type = AutoPushHistory.PushType.AppPush
        business = AutoPushHistory.Business.TradeRankStart
        rows = TradeRankActivity.query.filter(
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
            TradeRankActivity.type.in_(TradeRankActivity.PERPETUAL_TYPES),
        ).with_entities(
            TradeRankActivity.id,
            TradeRankActivity.started_at
        ).all()
        for row in rows:
            if dt < row.started_at or dt >= row.started_at + cls.NoticeExpireTime:
                continue
            if cls._is_auto_push_exist(push_type, business, row.id):
                continue
            expired_at = row.started_at + cls.NoticeExpireTime
            cls._add_auto_push(push_type, business, row.id, expired_at)

    @classmethod
    def _perpetual_trade_rank_activity_end_app_push(cls, dt: datetime):
        push_type = AutoPushHistory.PushType.AppPush
        business = AutoPushHistory.Business.TradeRankEnd
        rows = TradeRankActivity.query.filter(
            TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
            TradeRankActivity.type.in_(TradeRankActivity.PERPETUAL_TYPES),
        ).with_entities(
            TradeRankActivity.id,
            TradeRankActivity.ended_at
        ).all()
        for row in rows:
            if dt < row.ended_at - timedelta(hours=72) or dt >= row.ended_at - timedelta(hours=72) + cls.NoticeExpireTime:
                continue
            if cls._is_auto_push_exist(push_type, business, row.id):
                continue
            expired_at = row.ended_at - timedelta(hours=72) + cls.NoticeExpireTime
            cls._add_auto_push(push_type, business, row.id, expired_at)

    @classmethod
    def _airdrop_activity_start_email(cls, dt: datetime):
        push_type = AutoPushHistory.PushType.EmailPush
        business = AutoPushHistory.Business.AirdropStart
        rows = AirdropActivity.query.filter(
            AirdropActivity.status == AirdropActivity.StatusType.ONLINE,
        ).with_entities(
            AirdropActivity.id,
            AirdropActivity.start_time
        ).all()
        for row in rows:
            if dt < row.start_time + timedelta(hours=2) or dt >= row.start_time + timedelta(hours=2) + cls.NoticeExpireTime:
                continue
            if cls._is_auto_push_exist(push_type, business, row.id):
                continue
            expired_at = row.start_time + timedelta(hours=2) + cls.NoticeExpireTime
            cls._add_auto_push(push_type, business, row.id, expired_at)

    @classmethod
    def _airdrop_activity_end_app_push(cls, dt: datetime):
        push_type = AutoPushHistory.PushType.AppPush
        business = AutoPushHistory.Business.AirdropEnd
        rows = AirdropActivity.query.filter(
            AirdropActivity.status == AirdropActivity.StatusType.ONLINE,
        ).with_entities(
            AirdropActivity.id,
            AirdropActivity.end_time
        ).all()
        for row in rows:
            if dt < row.end_time - timedelta(hours=12) or dt >= row.end_time - timedelta(hours=12) + cls.NoticeExpireTime:
                continue
            if cls._is_auto_push_exist(push_type, business, row.id):
                continue
            expired_at = row.end_time - timedelta(hours=12) + cls.NoticeExpireTime
            cls._add_auto_push(push_type, business, row.id, expired_at)

    @classmethod
    def _discount_activity_start_email(cls, dt: datetime):
        push_type = AutoPushHistory.PushType.EmailPush
        business = AutoPushHistory.Business.DiscountStart
        rows = DiscountActivity.query.filter(
            DiscountActivity.status == DiscountActivity.StatusType.ONLINE,
        ).with_entities(
            DiscountActivity.id,
            DiscountActivity.start_time
        ).all()

        for row in rows:
            if dt < row.start_time + timedelta(hours=2) or dt >= row.start_time + timedelta(hours=2) + cls.NoticeExpireTime:
                continue
            if cls._is_auto_push_exist(push_type, business, row.id):
                continue
            expired_at = row.start_time + timedelta(hours=2) + cls.NoticeExpireTime
            cls._add_auto_push(push_type, business, row.id, expired_at)

    @classmethod
    def _discount_activity_end_app_push(cls, dt: datetime):
        push_type = AutoPushHistory.PushType.AppPush
        business = AutoPushHistory.Business.DiscountEnd
        rows = DiscountActivity.query.filter(
            DiscountActivity.status == DiscountActivity.StatusType.ONLINE,
        ).with_entities(
            DiscountActivity.id,
            DiscountActivity.end_time
        ).all()
        for row in rows:
            if dt < row.end_time - timedelta(hours=12) or dt >= row.end_time - timedelta(hours=12) + cls.NoticeExpireTime:
                continue
            if cls._is_auto_push_exist(push_type, business, row.id):
                continue
            expired_at = row.end_time - timedelta(hours=12) + cls.NoticeExpireTime
            cls._add_auto_push(push_type, business, row.id, expired_at)

    @classmethod
    def _get_need_notice_launch_pool_data(cls, is_pre=False) -> tuple[list[LaunchMiningProject], dict[int, list[LaunchMiningPool]]]:
        query = LaunchMiningProject.query.filter(
            LaunchMiningProject.status == LaunchMiningProject.Status.ONLINE,
            LaunchMiningProject.end_time > now(),
        ).with_entities(
            LaunchMiningProject.id,
            LaunchMiningProject.auto_online_time,
            LaunchMiningProject.start_time,
            LaunchMiningProject.end_time,
        )
        if is_pre:
            query = query.filter(LaunchMiningProject.auto_online_time.isnot(None))

        projects: list[LaunchMiningProject] = query.limit(100).all()
        project_ids = [i.id for i in projects]
        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id.in_(project_ids),
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).with_entities(
            LaunchMiningPool.id,
            LaunchMiningPool.project_id,
        ).all()
        pcj_pools_map: dict[int, list[LaunchMiningPool]] = defaultdict(list)
        for p in pools:
            pcj_pools_map[p.project_id].append(p)

        pcj_pools_map = {k: v for k, v in pcj_pools_map.items() if len(v) in {1, 2}}  # 1个或2个质押池才自动触达
        return projects, pcj_pools_map

    @classmethod
    def _launch_pool_pre_notices(cls, dt: datetime):
        business = AutoPushHistory.Business.LaunchPoolMiningPre
        projects, pcj_pools_map = cls._get_need_notice_launch_pool_data()

        for row in projects:
            auto_online_time = row.auto_online_time
            if not (auto_online_time + timedelta(hours=1) <= dt <= auto_online_time + timedelta(hours=1) + cls.NoticeExpireTime):
                continue
            if dt >= row.end_time:
                continue

            push_type1 = AutoPushHistory.PushType.AppPush
            expired_at = auto_online_time + timedelta(hours=1) + cls.NoticeExpireTime
            if cls._is_auto_push_exist(push_type1, business, row.id):
                continue
            cls._add_auto_push(push_type1, business, row.id, expired_at)

            push_type2 = AutoPushHistory.PushType.NotificationBar
            if cls._is_auto_push_exist(push_type2, business, row.id):
                continue
            cls._add_auto_push(push_type2, business, row.id, expired_at)

    @classmethod
    def _launch_pool_start_notices(cls, dt: datetime):
        business = AutoPushHistory.Business.LaunchPoolMiningStart
        projects, pcj_pools_map = cls._get_need_notice_launch_pool_data()

        for row in projects:
            start_time = row.start_time
            if not (start_time <= dt <= start_time + cls.NoticeExpireTime):
                continue
            if dt >= row.end_time:
                continue

            expired_at = start_time + cls.NoticeExpireTime
            push_type1 = AutoPushHistory.PushType.EmailPush
            if cls._is_auto_push_exist(push_type1, business, row.id):
                continue
            cls._add_auto_push(push_type1, business, row.id, expired_at)

            push_type2 = AutoPushHistory.PushType.AppPush
            if cls._is_auto_push_exist(push_type2, business, row.id):
                continue
            cls._add_auto_push(push_type2, business, row.id, expired_at)

    @classmethod
    def _launch_pool_end_notices(cls, dt: datetime):
        business = AutoPushHistory.Business.LaunchPoolMiningEnd
        projects, pcj_pools_map = cls._get_need_notice_launch_pool_data()

        for row in projects:
            end_time = row.end_time
            # 活动时间小于24小时，不发送push
            if end_time - row.start_time <= timedelta(hours=24):
                continue
            if not (end_time - timedelta(hours=24) <= dt <= end_time - timedelta(hours=24) + cls.NoticeExpireTime):
                continue
            if dt >= row.end_time:
                continue

            expired_at = end_time - timedelta(hours=24) + cls.NoticeExpireTime
            push_type = AutoPushHistory.PushType.AppPush
            if cls._is_auto_push_exist(push_type, business, row.id):
                continue
            cls._add_auto_push(push_type, business, row.id, expired_at)

    @classmethod
    def create_launch_pool_pre_app_push(cls, pcj_id):
        push_name = f'mining_{pcj_id}_pre sys_auto_push app_push'
        if exist_row := AppPush.query.filter(AppPush.name == push_name).first():
            return exist_row.id

        pcj_id = int(pcj_id)
        pcj: LaunchMiningProject = LaunchMiningProject.query.get(pcj_id)
        reward_asset = pcj.reward_asset
        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj_id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        pool_num = len(pools)

        user_ids = cls.get_90days_active_users()
        url_path = "/mining"
        url = url_join(config["SITE_URL"], url_path)
        titles = {}
        contents = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                if pool_num == 1:
                    titles[lang] = gettext(
                        '挖矿预告：提前存入%(stake_asset)s，赚取%(reward_asset)s收益',
                        stake_asset=pools[0].stake_asset,
                        reward_asset=reward_asset,
                    )
                    contents[lang] = gettext('活动开始即享%(reward_asset)s小时收益>>', reward_asset=reward_asset)
                elif pool_num == 2:
                    titles[lang] = gettext(
                        '挖矿预告：提前存入%(stake_asset1)s 或 %(stake_asset2)s，赚取%(reward_asset)s收益',
                        stake_asset1=pools[0].stake_asset,
                        stake_asset2=pools[1].stake_asset,
                        reward_asset=reward_asset,
                    )
                    contents[lang] = gettext('活动开始即享%(reward_asset)s小时收益>>', reward_asset=reward_asset)

        if not titles or not contents:
            return

        return ActivityAutoPushHelper.create_app_push(
            list(user_ids),
            titles,
            contents,
            url,
            config['ACTIVITY_PUSH']['launch_pool_jump_id'],
            name=push_name,
            push_type=AppPush.PushType.ACTIVITY,
        )

    @classmethod
    def create_launch_pool_pre_notification_bar(cls, pcj_id):
        push_name = f'mining_{pcj_id}_pre sys_auto_push notification_bar'
        if exist_row := NotificationBar.query.filter(NotificationBar.name == push_name).first():
            return exist_row.id

        pcj_id = int(pcj_id)
        pcj: LaunchMiningProject = LaunchMiningProject.query.get(pcj_id)
        reward_asset = pcj.reward_asset
        reward_total_amount_str = amount_to_str(pcj.reward_total_amount)
        start_time_str = datetime_to_str(pcj.start_time, fmt="%m-%d %H:%M")
        end_time_str = datetime_to_str(pcj.end_time, fmt="%m-%d %H:%M")

        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj_id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        pool_num = len(pools)

        titles = {}
        summarys = {}
        contents = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                if pool_num == 1:
                    titles[lang] = gettext(
                        '存入%(stake_asset)s，赚取%(reward_asset)s收益',
                        stake_asset=pools[0].stake_asset,
                        reward_asset=reward_asset,
                    )
                    summarys[lang] = gettext(
                        '参与挖矿，瓜分%(reward_total_amount)s %(reward_asset)s奖池',
                        reward_total_amount=reward_total_amount_str,
                        reward_asset=reward_asset,
                    )
                    contents[lang] = gettext(
                        '现在前往活动页面，存入最低%(min_stake_amount)s %(stake_asset)s，'
                        '即可在%(start_time)s - %(end_time)s（UTC）期间赚取每小时%(reward_asset)s收益！',
                        min_stake_amount=amount_to_str(pools[0].user_min_stake_amount),
                        stake_asset=pools[0].stake_asset,
                        start_time=start_time_str,
                        end_time=end_time_str,
                        reward_asset=reward_asset,
                    )
                elif pool_num == 2:
                    titles[lang] = gettext(
                        '存入%(stake_asset1)s 或 %(stake_asset2)s，赚取%(reward_asset)s收益',
                        stake_asset1=pools[0].stake_asset,
                        stake_asset2=pools[1].stake_asset,
                        reward_asset=reward_asset,
                    )
                    summarys[lang] = gettext(
                        '参与挖矿，瓜分%(reward_total_amount)s %(reward_asset)s奖池',
                        reward_total_amount=reward_total_amount_str,
                        reward_asset=reward_asset,
                    )
                    contents[lang] = gettext(
                        '现在前往活动页面，存入最低%(min_stake_amount1)s %(stake_asset1)s 或 %(min_stake_amount2)s %(stake_asset2)s，'
                        '即可在%(start_time)s - %(end_time)s（UTC）期间赚取每小时%(reward_asset)s收益！',
                        min_stake_amount1=amount_to_str(pools[0].user_min_stake_amount),
                        stake_asset1=pools[0].stake_asset,
                        min_stake_amount2=amount_to_str(pools[1].user_min_stake_amount),
                        stake_asset2=pools[1].stake_asset,
                        start_time=start_time_str,
                        end_time=end_time_str,
                        reward_asset=reward_asset,
                    )
        if not titles or not contents or not summarys:
            return

        trigger_pages = [
            {"trigger_page": NotificationBar.TriggerPage.QUOTES.name, "trigger_page_params": ""},
            {"trigger_page": NotificationBar.TriggerPage.ASSET_DATA.name, "trigger_page_params": ""},
        ]
        return ActivityAutoPushHelper.create_notification_bar(
            name=push_name,
            begin_at=pcj.auto_online_time + timedelta(hours=1) if pcj.auto_online_time else now(),
            end_at=pcj.end_time,
            jump_page_enabled=True,
            jump_id=config['ACTIVITY_PUSH']['launch_pool_jump_id'],
            title=titles,
            summary=summarys,
            content=contents,
            trigger_pages=trigger_pages,
            template_kwargs={},
        )

    @classmethod
    def _hold_cet_active_users(cls):
        user_ids = cls.get_90days_active_users()  # main user id
        ts = current_timestamp(to_int=True)
        user_cet_map = ExchangeLogDB.get_user_account_type_balances(
            timestamp=ts,
            asset='CET',
            account_types=[],
        )
        to_send_user_ids = {i for i in user_ids if i and user_cet_map.get(i, 0) > 0}
        return list(to_send_user_ids)

    @classmethod
    def create_launch_pool_start_email_push(cls, pcj_id):
        push_name = f'mining_{pcj_id}_start sys_auto_push email_push'
        if exist_row := EmailPush.query.filter(EmailPush.name == push_name).first():
            return exist_row.id

        pcj_id = int(pcj_id)
        pcj: LaunchMiningProject = LaunchMiningProject.query.get(pcj_id)
        reward_asset = pcj.reward_asset
        reward_total_amount_str = amount_to_str(pcj.reward_total_amount)
        start_time = datetime_to_str(pcj.start_time, fmt="%m-%d %H:%M")
        end_time = datetime_to_str(pcj.end_time, fmt="%m-%d %H:%M")

        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj_id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        pool_num = len(pools)

        user_ids = cls._hold_cet_active_users()
        email_type = 'activity/launch_pool_mining_start'
        url_path = "/mining"
        detail_url = url_join(config["SITE_URL"], url_path)

        if pool_num == 1:
            pool_params = dict(
                stake_asset=pools[0].stake_asset,
                min_stake_amount=amount_to_str(pools[0].user_min_stake_amount),
            )
            title = '挖矿开启：存入{{ stake_asset }}，瓜分{{ reward_total_amount }} {{ reward_asset }}'
            gettext('挖矿开启：存入{{ stake_asset }}，瓜分{{ reward_total_amount }} {{ reward_asset }}')
        elif pool_num == 2:
            title = '挖矿开启：存入{{ stake_asset1 }} 或 {{ stake_asset2 }}，瓜分{{ reward_total_amount }} {{ reward_asset }}'
            gettext('挖矿开启：存入{{ stake_asset1 }} 或 {{ stake_asset2 }}，瓜分{{ reward_total_amount }} {{ reward_asset }}')
            pool_params = dict(
                stake_asset1=pools[0].stake_asset,
                min_stake_amount1=amount_to_str(pools[0].user_min_stake_amount),
                stake_asset2=pools[1].stake_asset,
                min_stake_amount2=amount_to_str(pools[1].user_min_stake_amount),
            )
        else:
            return

        titles = {}
        condition_str = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                condition_str[lang] = gettext("完成初级KYC认证")
                titles[lang] = gettext(title)

        template_kwargs = dict(
            title=title,
            reward_total_amount=reward_total_amount_str,
            reward_asset=reward_asset,
            start_time=start_time,
            end_time=end_time,
            condition=condition_str,
            detail_url=detail_url,
            pool_num=pool_num,
        )
        template_kwargs.update(pool_params)

        return ActivityAutoPushHelper.create_email_push(
            user_ids,
            title,
            'notice',
            email_type,
            template_kwargs,
            name=push_name,
            push_type=EmailPush.PushType.ACTIVITY,
        )

    @classmethod
    def create_launch_pool_start_app_push(cls, pcj_id):
        push_name = f'mining_{pcj_id}_start sys_auto_push app_push'
        if exist_row := AppPush.query.filter(AppPush.name == push_name).first():
            return exist_row.id

        pcj_id = int(pcj_id)
        pcj: LaunchMiningProject = LaunchMiningProject.query.get(pcj_id)
        reward_asset = pcj.reward_asset
        reward_total_amount_str = amount_to_str(pcj.reward_total_amount)
        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj_id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        pool_num = len(pools)

        user_ids = cls.get_90days_active_users()
        url_path = "/mining"
        url = url_join(config["SITE_URL"], url_path)
        titles = {}
        contents = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                if pool_num == 1:
                    titles[lang] = gettext(
                        '挖矿开启：存入%(stake_asset)s，赚%(reward_asset)s小时收益',
                        stake_asset=pools[0].stake_asset,
                        reward_asset=reward_asset,
                    )
                    contents[lang] = gettext(
                        '最低存入%(min_stake_amount)s %(stake_asset)s，即可参与瓜分%(reward_total_amount)s %(reward_asset)s奖池>>',
                        min_stake_amount=amount_to_str(pools[0].user_min_stake_amount),
                        stake_asset=pools[0].stake_asset,
                        reward_total_amount=reward_total_amount_str,
                        reward_asset=reward_asset,
                    )
                elif pool_num == 2:
                    titles[lang] = gettext(
                        '挖矿开启：存入%(stake_asset1)s 或 %(stake_asset2)s，赚%(reward_asset)s小时收益',
                        stake_asset1=pools[0].stake_asset,
                        stake_asset2=pools[1].stake_asset,
                        reward_asset=reward_asset,
                    )
                    contents[lang] = gettext(
                        '最低存入%(min_stake_amount1)s %(stake_asset1)s 或 %(min_stake_amount2)s %(stake_asset2)s，'
                        '即可参与瓜分%(reward_total_amount)s %(reward_asset)s奖池>>',
                        min_stake_amount1=amount_to_str(pools[0].user_min_stake_amount),
                        stake_asset1=pools[0].stake_asset,
                        min_stake_amount2=amount_to_str(pools[1].user_min_stake_amount),
                        stake_asset2=pools[1].stake_asset,
                        reward_total_amount=reward_total_amount_str,
                        reward_asset=reward_asset,
                    )

        if not titles or not contents:
            return

        return ActivityAutoPushHelper.create_app_push(
            list(user_ids),
            titles,
            contents,
            url,
            config['ACTIVITY_PUSH']['launch_pool_jump_id'],
            name=push_name,
            push_type=AppPush.PushType.ACTIVITY,
        )

    @classmethod
    def create_launch_pool_end_app_push(cls, pcj_id):
        push_name = f'mining_{pcj_id}_end sys_auto_push app_push'
        if exist_row := AppPush.query.filter(AppPush.name == push_name).first():
            return exist_row.id

        pcj_id = int(pcj_id)
        pcj: LaunchMiningProject = LaunchMiningProject.query.get(pcj_id)
        reward_asset = pcj.reward_asset
        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj_id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        pool_num = len(pools)

        user_ids = cls.get_90days_active_users()
        url_path = "/mining"
        url = url_join(config["SITE_URL"], url_path)
        titles = {}
        contents = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                if pool_num == 1:
                    if pools[0].apr:
                        titles[lang] = gettext(
                            '挖矿即将结束：仅需%(min_stake_amount)s %(stake_asset)s即可参与',
                            min_stake_amount=amount_to_str(pools[0].user_min_stake_amount),
                            stake_asset=pools[0].stake_asset,
                        )
                        max_apr = pools[0].apr or Decimal(0)
                        apr_str = amount_to_str(max_apr * Decimal(100), 2)
                        contents[lang] = gettext(
                            '赚取免费%(reward_asset)s，预估APR %(apr)s%% >>',
                            reward_asset=reward_asset,
                            apr=apr_str,
                        )
                    else:
                        titles[lang] = gettext('挖矿即将结束')
                        contents[lang] = gettext(
                            '仅需%(min_stake_amount)s %(stake_asset)s即可参与，立即赚取免费%(reward_asset)s >>',
                            min_stake_amount=amount_to_str(pools[0].user_min_stake_amount),
                            stake_asset=pools[0].stake_asset,
                            reward_asset=reward_asset,
                        )
                elif pool_num == 2:
                    max_apr = max([p.apr or Decimal(0) for p in pools])
                    if max_apr:
                        titles[lang] = gettext(
                            '挖矿即将结束：赚取免费%(reward_asset)s',
                            reward_asset=reward_asset,
                        )
                        apr_str = amount_to_str(max_apr * Decimal(100), 2)
                        contents[lang] = gettext(
                            '预估APR %(apr)s%% >>',
                            apr=apr_str,
                        )
                    else:
                        titles[lang] = gettext(
                            '挖矿即将结束：赚取免费%(reward_asset)s',
                            reward_asset=reward_asset,
                        )
                        contents[lang] = gettext(
                            '最低存入%(min_stake_amount1)s %(stake_asset1)s 或 %(min_stake_amount2)s %(stake_asset2)s，'
                            '即可赚取免费%(reward_asset)s >>',
                            min_stake_amount1=amount_to_str(pools[0].user_min_stake_amount),
                            stake_asset1=pools[0].stake_asset,
                            min_stake_amount2=amount_to_str(pools[1].user_min_stake_amount),
                            stake_asset2=pools[1].stake_asset,
                            reward_asset=reward_asset,
                        )

        if not titles or not contents:
            return

        return ActivityAutoPushHelper.create_app_push(
            list(user_ids),
            titles,
            contents,
            url,
            config['ACTIVITY_PUSH']['launch_pool_jump_id'],
            name=push_name,
            push_type=AppPush.PushType.ACTIVITY,
        )

    @classmethod
    def create_trade_rank_start_email_push(cls, activity_id):
        activity_id = int(activity_id)
        row = TradeRankActivity.query.get(activity_id)
        if row.type in TradeRankActivity.SPOT_TYPES:
            return cls._create_spot_trade_rank_start_email_push(row)
        else:
            return cls._create_perpetual_trade_rank_start_email_push(row)

    @classmethod
    def _create_spot_trade_rank_start_email_push(cls, row: TradeRankActivity):
        user_ids = cls._trade_rank_start_email_users(row)
        email_type = 'activity/spot_trade_rank_start'
        push_name = f'spot trade rank activity auto push {row.id}'
        url_path = "/activity/trade-rank/" + str(row.id)
        site_url = url_join(config["SITE_URL"], url_path)
        start_time = datetime_to_str(row.started_at, fmt="%m-%d %H:%M")
        end_time = datetime_to_str(row.ended_at, fmt="%m-%d %H:%M")
        details = TradeRankActivityDetail.query.filter(
            TradeRankActivityDetail.trade_activity_id == row.id,
        ).all()
        titles = {d.lang: d.title for d in details}
        markets = json.loads(row.markets) if row.markets else []
        if markets:
            markets_str = ','.join(markets)
        else:
            gettext('全部币币市场')
            markets_str = '全部币币市场'
        template_kwargs = dict(
            title=titles,
            start_time=start_time,
            end_time=end_time,
            market=markets_str,
            threshold=amount_to_str(row.least_trade_amount),
            amount=amount_to_str(row.gift_amount),
            asset=row.gift_asset,
            site_url=site_url,
        )

        return ActivityAutoPushHelper.create_email_push(
            user_ids,
            titles,
            'notice',
            email_type,
            template_kwargs,
            name=push_name,
            push_type=EmailPush.PushType.ACTIVITY,
        )

    @classmethod
    def _create_perpetual_trade_rank_start_email_push(cls, row: TradeRankActivity):
        user_ids = cls._trade_rank_start_email_users(row)
        email_type = 'activity/perpetual_trade_rank_start'
        push_name = f'perpetual trade rank activity auto push {row.id}'
        url_path = "/activity/trade-rank/" + str(row.id)
        site_url = url_join(config["SITE_URL"], url_path)
        end_time = datetime_to_str(row.ended_at, fmt="%m-%d %H:%M")
        template_kwargs = dict(
            end_time=end_time,
            amount=amount_to_str(row.gift_amount),
            asset=row.gift_asset,
            site_url=site_url
        )
        title = '恭喜！您具备合约交易赛报名资格'
        gettext('恭喜！您具备合约交易赛报名资格')
        return ActivityAutoPushHelper.create_email_push(
            user_ids,
            title,
            'notice',
            email_type,
            template_kwargs,
            name=push_name,
            push_type=EmailPush.PushType.ACTIVITY,
        )

    @classmethod
    def _trade_rank_start_email_users(cls, row: TradeRankActivity):
        user_ids = cls.get_90days_active_users()
        join_users = TradeRankActivityJoinUser.query.filter(
            TradeRankActivityJoinUser.trade_activity_id == row.id
        ).with_entities(TradeRankActivityJoinUser.user_id).all()
        joined_user_ids = {r.user_id for r in join_users}
        user_ids -= joined_user_ids
        to_send_user_ids = TradeRankUserGroupValidator(user_ids, row.user_group_condition).validate()
        return list(to_send_user_ids)

    @classmethod
    def create_trade_rank_start_app_push(cls, activity_id):
        activity_id = int(activity_id)
        row = TradeRankActivity.query.get(activity_id)
        if row.type in TradeRankActivity.PERPETUAL_TYPES:
            return cls._create_perpetual_trade_rank_start_app_push(row)
        else:
            return

    @classmethod
    def _create_perpetual_trade_rank_start_app_push(cls, row: TradeRankActivity):
        user_ids = cls._perpetual_trade_rank_start_app_push_users(row)
        url_path = "/activity/trade-rank/" + str(row.id)
        url = url_join(config["SITE_URL"], url_path)
        push_name = f'perpetual trade rank start auto push {row.id}'
        titles = {}
        contents = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                titles[lang] = gettext('恭喜！您具备合约交易赛报名资格')
                contents[lang] = gettext('报名有机会瓜分%(amount)s %(asset)s',
                                         amount=amount_to_str(row.gift_amount), asset=row.gift_asset)

        return ActivityAutoPushHelper.create_app_push(
            user_ids,
            titles,
            contents,
            url,
            config['ACTIVITY_PUSH']['trade_rank_jump_id'],
            name=push_name,
            push_type=AppPush.PushType.ACTIVITY
        )

    @classmethod
    def _perpetual_trade_rank_start_app_push_users(cls, row: TradeRankActivity):
        join_users = TradeRankActivityJoinUser.query.filter(
            TradeRankActivityJoinUser.trade_activity_id == row.id
        ).with_entities(TradeRankActivityJoinUser.user_id).all()
        joined_user_ids = {r.user_id for r in join_users}

        to_send_user_ids = list()
        last_id = 0
        limit = 5000
        while True:
            rows = User.query.filter(
                User.id > last_id,
                User.user_type == User.UserType.NORMAL,
            ).with_entities(User.id).order_by(User.id).limit(limit).all()
            if rows:
                last_id = rows[-1].id

            user_ids = [r.id for r in rows]
            for user_id in user_ids:
                if user_id in joined_user_ids:
                    continue
                to_send_user_ids.append(user_id)

            if len(rows) != limit:
                break

        return to_send_user_ids

    @classmethod
    def create_trade_rank_end_app_push(cls, activity_id):
        activity_id = int(activity_id)
        row = TradeRankActivity.query.get(activity_id)
        if row.type in TradeRankActivity.SPOT_TYPES:
            return cls._create_spot_trade_rank_end_app_push(row)
        else:
            return cls._create_perpetual_trade_rank_end_app_push(row)

    @classmethod
    def _create_spot_trade_rank_end_app_push(cls, row: TradeRankActivity):
        user_ids = cls._spot_trade_rank_end_app_push_users(row)
        url_path = "/activity/trade-rank/" + str(row.id)
        url = url_join(config["SITE_URL"], url_path)
        push_name = f'spot trade rank end auto push {row.id}'
        titles = {}
        contents = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                titles[lang] = gettext('%(amount)s %(asset)s奖池即将失效', amount=amount_to_str(row.gift_amount), asset=row.gift_asset)
                contents[lang] = gettext('点击领取 >>')

        return ActivityAutoPushHelper.create_app_push(
            user_ids,
            titles,
            contents,
            url,
            config['ACTIVITY_PUSH']['trade_rank_jump_id'],
            name=push_name,
            push_type=AppPush.PushType.ACTIVITY
        )

    @classmethod
    def _spot_trade_rank_end_app_push_users(cls, row: TradeRankActivity):
        user_ids = cls.get_90days_active_users()
        to_send_user_ids = TradeRankUserGroupValidator(user_ids, row.user_group_condition).validate()
        return list(to_send_user_ids)

    @classmethod
    def _create_perpetual_trade_rank_end_app_push(cls, row: TradeRankActivity):
        user_ids = cls._perpetual_trade_rank_end_app_push_users(row)
        url_path = "/activity/trade-rank/" + str(row.id)
        url = url_join(config["SITE_URL"], url_path)
        push_name = f'perpetual trade rank end auto push {row.id}'
        titles = {}
        contents = {}
        rules = json.loads(row.gift_rules)
        rank_amount = rules[0].get('rank_amount') if rules else 0
        max_amount = rank_amount or row.max_split_reward_amount
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                titles[lang] = gettext('温馨提示：合约交易赛活动进度已过半')
                contents[lang] = gettext('提升排名最高可得%(amount)s %(asset)s，立即查看 >>',
                                         amount=amount_to_str(max_amount), asset=row.gift_asset)

        return ActivityAutoPushHelper.create_app_push(
            user_ids,
            titles,
            contents,
            url,
            config['ACTIVITY_PUSH']['trade_rank_jump_id'],
            name=push_name,
            push_type=AppPush.PushType.ACTIVITY
        )

    @classmethod
    def _perpetual_trade_rank_end_app_push_users(cls, row: TradeRankActivity):
        join_users = TradeRankActivityJoinUser.query.filter(
            TradeRankActivityJoinUser.trade_activity_id == row.id
        ).with_entities(TradeRankActivityJoinUser.user_id).all()
        user_ids = {r.user_id for r in join_users}
        to_send_user_ids = list(user_ids)
        return to_send_user_ids

    @classmethod
    def create_airdrop_start_email_push(cls, activity_id):
        activity_id = int(activity_id)
        row = AirdropActivity.query.get(activity_id)
        if row.label_type == AirdropActivity.LabelType.ASSET:
            return cls._create_asset_airdrop_start_email_push(row)
        else:
            return cls._create_other_airdrop_start_email_push(row)

    @classmethod
    def _create_asset_airdrop_start_email_push(cls, row: AirdropActivity):
        user_ids = cls._airdrop_activity_start_email_users(row)
        email_type = 'activity/asset_airdrop_activity_start'
        push_name = f'asset airdrop activity auto push {row.id}'
        url_path = "/activity/airdrop/" + str(row.id)
        site_url = url_join(config["SITE_URL"], url_path)
        start_time = datetime_to_str(row.start_time, fmt="%m-%d %H:%M")
        end_time = datetime_to_str(row.end_time, fmt="%m-%d %H:%M")
        reward = AirdropActivityReward.query.filter(
            AirdropActivityReward.airdrop_activity_id == row.id,
            AirdropActivityReward.type == AirdropActivityReward.Type.ASSET
        ).first()
        reward_asset = reward.asset
        reward_total_amount = row.total_count * reward.amount
        conditions = AirdropActivityCondition.query.filter(
            AirdropActivityCondition.airdrop_activity_id == row.id,
        ).all()
        conditions = [dict(key=c.key, value=c.value) for c in conditions]
        condition_str = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                condition_str[lang] = ActivityCondition.build_condition_str(conditions)

        data = dict(
            amount=amount_to_str(reward_total_amount),
            asset=reward_asset,
            start_time=start_time,
            end_time=end_time,
            condition=condition_str,
            site_url=site_url
        )

        title = "参与{{ asset }}空投活动，赢取{{ amount }} {{ asset }}"
        gettext("参与{{ asset }}空投活动，赢取{{ amount }} {{ asset }}")
        return ActivityAutoPushHelper.create_email_push(
            user_ids,
            title,
            'notice',
            email_type,
            data,
            name=push_name,
            push_type=EmailPush.PushType.ACTIVITY,
        )

    @classmethod
    def _create_other_airdrop_start_email_push(cls, row: AirdropActivity):
        user_ids = cls._airdrop_activity_start_email_users(row)
        email_type = 'activity/airdrop_activity_start'
        push_name = f'other airdrop activity auto push {row.id}'
        url_path = "/activity/airdrop/" + str(row.id)
        site_url = url_join(config["SITE_URL"], url_path)
        start_time = datetime_to_str(row.start_time, fmt="%m-%d %H:%M")
        end_time = datetime_to_str(row.end_time, fmt="%m-%d %H:%M")
        details = AirdropActivityDetail.query.filter(
            AirdropActivityDetail.airdrop_activity_id == row.id,
        ).all()
        titles = {d.lang: d.title for d in details}
        conditions = AirdropActivityCondition.query.filter(
            AirdropActivityCondition.airdrop_activity_id == row.id,
        ).all()
        conditions = [dict(key=c.key, value=c.value) for c in conditions]
        condition_str = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                condition_str[lang] = ActivityCondition.build_condition_str(conditions)

        data = dict(
            title=titles,
            start_time=start_time,
            end_time=end_time,
            condition=condition_str,
            site_url=site_url
        )

        return ActivityAutoPushHelper.create_email_push(
            user_ids,
            titles,
            'notice',
            email_type,
            data,
            name=push_name,
            push_type=EmailPush.PushType.ACTIVITY,
        )

    @classmethod
    def _airdrop_activity_start_email_users(cls, row: AirdropActivity):
        user_ids = cls.get_90days_active_users()
        conditions = {
            i.key: i.value for i in AirdropActivityCondition.query.filter(
                AirdropActivityCondition.airdrop_activity_id == row.id
            ).all()
        }
        to_send_user_ids = AirdropActivityValidator(user_ids, row.id, conditions).validate()
        return list(to_send_user_ids)

    @classmethod
    def create_airdrop_end_app_push(cls, activity_id):
        activity_id = int(activity_id)
        row = AirdropActivity.query.get(activity_id)
        return cls._create_airdrop_activity_end_app_push(row)

    @classmethod
    def _create_airdrop_activity_end_app_push(cls, row: AirdropActivity):
        user_ids = cls._airdrop_activity_end_app_push_users(row)
        url_path = "/activity/airdrop/" + str(row.id)
        url = url_join(config["SITE_URL"], url_path)
        push_name = f'airdrop end auto push {row.id}'
        titles = {}
        contents = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                titles[lang] = gettext('参与CoinEx空投站，最后12小时！')
                contents[lang] = gettext('答题赢取空投奖励 >>')

        return ActivityAutoPushHelper.create_app_push(
            user_ids,
            titles,
            contents,
            url,
            config['ACTIVITY_PUSH']['airdrop_jump_id'],
            name=push_name,
            push_type=AppPush.PushType.ACTIVITY
        )

    @classmethod
    def _airdrop_activity_end_app_push_users(cls, row: AirdropActivity):
        user_ids = cls.get_90days_active_users()
        conditions = {
            i.key: i.value for i in AirdropActivityCondition.query.filter(
                AirdropActivityCondition.airdrop_activity_id == row.id
            ).all()
        }
        to_send_user_ids = AirdropActivityValidator(user_ids, row.id, conditions).validate()
        return list(to_send_user_ids)

    @classmethod
    def create_discount_start_email_push(cls, activity_id):
        activity_id = int(activity_id)
        row = DiscountActivity.query.get(activity_id)
        return cls._create_discount_start_email_push(row)

    @classmethod
    def _create_discount_start_email_push(cls, row: DiscountActivity):
        user_ids = cls._discount_activity_start_email_users(row)
        email_type = 'activity/dibs_activity_start'
        push_name = f'discount activity auto push {row.id}'
        url_path = "/activity/dibs/" + str(row.id)
        site_url = url_join(config["SITE_URL"], url_path)
        start_time = datetime_to_str(row.start_time, fmt="%m-%d %H:%M")
        end_time = datetime_to_str(row.end_time, fmt="%m-%d %H:%M")
        details = DiscountActivityDetail.query.filter(
            DiscountActivityDetail.discount_activity_id == row.id,
        ).all()
        titles = {d.lang: d.title for d in details}
        conditions = DiscountActivityCondition.query.filter(
            DiscountActivityCondition.discount_activity_id == row.id,
        ).all()
        conditions = [dict(key=c.key, value=c.value) for c in conditions]
        condition_str = {}
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                condition_str[lang] = ActivityCondition.build_condition_str(conditions)

        data = dict(
            title=titles,
            start_time=start_time,
            end_time=end_time,
            condition=condition_str,
            asset=row.asset,
            discount=amount_to_str(row.discount_type.value * 100),
            site_url=site_url
        )

        return ActivityAutoPushHelper.create_email_push(
            user_ids,
            titles,
            'notice',
            email_type,
            data,
            name=push_name,
            push_type=EmailPush.PushType.ACTIVITY,
        )

    @classmethod
    def _discount_activity_start_email_users(cls, row: DiscountActivity):
        user_ids = cls.get_90days_active_users()
        conditions = {
            i.key: i.value for i in DiscountActivityCondition.query.filter(
                DiscountActivityCondition.discount_activity_id == row.id
            ).all()
        }
        to_send_user_ids = DiscountActivityValidator(user_ids, row.id, conditions).validate()
        return list(to_send_user_ids)

    @classmethod
    def create_discount_end_app_push(cls, activity_id):
        activity_id = int(activity_id)
        row = DiscountActivity.query.get(activity_id)
        return cls._create_discount_activity_end_app_push(row)

    @classmethod
    def _create_discount_activity_end_app_push(cls, row: DiscountActivity):
        user_ids = cls._discount_activity_end_app_push_users(row)
        url_path = "/activity/dibs/" + str(row.id)
        url = url_join(config["SITE_URL"], url_path)
        push_name = f'discount end auto push {row.id}'
        titles = {}
        contents = {}
        discount = amount_to_str(row.discount_type.value * 100)
        for lang in EmailPushContent.AVAILABLE_LANGS:
            with force_locale(lang.value):
                titles[lang] = gettext('CoinEx打折购即将结束，最后12小时，速来！')
                contents[lang] = gettext('参与%(asset)s打折购，立减%(discount)s%%，立即查看 >>', asset=row.asset, discount=discount)

        return ActivityAutoPushHelper.create_app_push(
            user_ids,
            titles,
            contents,
            url,
            config['ACTIVITY_PUSH']['discount_jump_id'],
            name=push_name,
            push_type=AppPush.PushType.ACTIVITY
        )

    @classmethod
    def _discount_activity_end_app_push_users(cls, row: DiscountActivity):
        user_ids = cls.get_90days_active_users()
        conditions = {
            i.key: i.value for i in DiscountActivityCondition.query.filter(
                DiscountActivityCondition.discount_activity_id == row.id
            ).all()
        }
        to_send_user_ids = DiscountActivityValidator(user_ids, row.id, conditions).validate()
        return list(to_send_user_ids)


class ActivityValidator:

    def __init__(self, user_ids: Set[int], activity_id: int, conditions: dict):
        self.user_ids = user_ids
        self.activity_id = activity_id
        self.conditions = conditions

    def validate(self) -> Set[int]:
        for key, value in self.conditions.items():
            getattr(self, f'_handle_{key.lower()}')(value)
        return self.user_ids

    def _handle_kyc(self, value):
        if not bool(int(value)):
            return
        filter_user_ids = set()
        for ids_ in batch_iter(self.user_ids, 2000):
            rows = User.query.filter(
                User.id.in_(ids_),
                User.kyc_status == User.KYCStatus.PASSED
            ).with_entities(
                User.id
            ).all()
            passed_user_ids = {r.id for r in rows}
            filter_user_ids |= passed_user_ids
        self.user_ids = filter_user_ids

    def _handle_vip(self, value):
        level = int(value)
        if level == 0:
            return
        filter_user_ids = set()
        for ids_ in batch_iter(self.user_ids, 2000):
            rows = VipUser.query.filter(
                VipUser.user_id.in_(ids_),
                VipUser.status == VipUser.StatusType.PASS,
                VipUser.level >= level
            ).with_entities(
                VipUser.user_id
            ).all()
            passed_user_ids = {r.user_id for r in rows}
            filter_user_ids |= passed_user_ids
        self.user_ids = filter_user_ids

    def _handle_holding(self, value):
        holding_condition = json_loads(value)
        need_holding = holding_condition[ActivityCondition.HoldingKeys.NEED_HOLDING.name]
        if need_holding == ActivityCondition.NeedHoldingType.NOT_LIMITED.value:
            return
        asset_holding = holding_condition[ActivityCondition.HoldingKeys.ASSET_HOLDING.name]
        filter_user_ids = set()
        for user_id in self.user_ids:
            history = ServerClient().get_user_balance_history(user_id, asset_holding, limit=1)
            holding = True
            if need_holding == ActivityCondition.NeedHoldingType.HOLD.value:
                holding = True if history else False
            if need_holding == ActivityCondition.NeedHoldingType.NOT_HOLD.value:
                holding = False if history else True
            if holding:
                filter_user_ids.add(user_id)
        self.user_ids = filter_user_ids

    def _handle_balance_value(self, value):
        balance_condition = json_loads(value)
        balance_usd = Decimal(balance_condition[ActivityCondition.BalanceValueKey.BALANCE_VALUE.name])
        balance_op_type = balance_condition[ActivityCondition.BalanceValueKey.BALANCE_OP_TYPE.name]
        if balance_op_type == ActivityCondition.BalanceValueOperateType.NOT_LIMITED.value:
            return
        filter_user_ids = set()
        for user_id in self.user_ids:
            current_balance_usd = Decimal(self._get_user_balance_usd(user_id))
            if balance_op_type == ActivityCondition.BalanceValueOperateType.GREATER.value:
                balance_usd_cond = current_balance_usd >= balance_usd
            else:
                balance_usd_cond = current_balance_usd < balance_usd
            if balance_usd_cond:
                filter_user_ids.add(user_id)
        self.user_ids = filter_user_ids

    def _handle_trade_value(self, value):
        value_key = ActivityCondition.TradeValueKey
        trade_condition = json_loads(value)
        trade_op_type = trade_condition[value_key.TRADE_OP_TYPE.name]
        trade_business_type = trade_condition[value_key.TRADE_BUSINESS_TYPE.name]
        if trade_op_type == ActivityCondition.TradeValueOperateType.NOT_LIMITED.value:
            return

        filter_user_ids = set()
        if trade_business_type == ActivityCondition.TradeBusinessTypeKey.REAL_TIME.value:
            value_key = ActivityCondition.TradeValueKey
            days = trade_condition[value_key.TRADE_DAY_RANGE.name]
            trade_type_range = trade_condition.get(value_key.TRADE_TYPE_RANGE.name)
            user_trade_value_map = self.get_user_trade_value_map(days, trade_type_range)
            trade_before_start_user_list = set()
        else:
            user_trade_value_map = {}
            trade_before_start_user_list = self._get_trade_before_start_user_list(self.activity_id)
        for user_id in self.user_ids:
            if trade_business_type == ActivityCondition.TradeBusinessTypeKey.REAL_TIME.value:
                trade_usd = Decimal(trade_condition[ActivityCondition.TradeValueKey.TRADE_VALUE.name])
                if trade_op_type == ActivityCondition.TradeValueOperateType.GREATER.value:
                    trade_usd_cond = user_trade_value_map.get(user_id, Decimal()) >= trade_usd
                else:
                    trade_usd_cond = user_trade_value_map.get(user_id, Decimal()) < trade_usd
            else:
                if trade_op_type == ActivityCondition.TradeValueOperateType.GREATER.value:
                    trade_usd_cond = user_id in trade_before_start_user_list
                else:
                    trade_usd_cond = user_id not in trade_before_start_user_list
            if trade_usd_cond:
                filter_user_ids.add(user_id)
        self.user_ids = filter_user_ids

    def _handle_registered_value(self, value):
        if not value:
            return
        json_condition = json_loads(value)
        threshold_time = json_condition[ActivityCondition.RegisteredTimeKey.REGISTERED_VALUE.value]
        option_type = json_condition[ActivityCondition.RegisteredTimeKey.REGISTERED_OP_TYPE.value]
        if option_type == ActivityCondition.RegisteredTimeType.NOT_LIMITED.value:
            return
        threshold_datetime = timestamp_to_datetime(int(threshold_time) / 1000)
        filter_user_ids = set()
        for ids_ in batch_iter(self.user_ids, 2000):
            rows = User.query.filter(
                User.id.in_(ids_),
            ).with_entities(
                User.id,
                User.created_at,
            ).all()
            for user in rows:
                user_registered_at = user.created_at
                if option_type == ActivityCondition.RegisteredTimeType.GREATER.value:
                    reg_condition = user_registered_at > threshold_datetime
                else:
                    reg_condition = user_registered_at < threshold_datetime
                if reg_condition:
                    filter_user_ids.add(user.id)
        self.user_ids = filter_user_ids

    def _handle_used_value(self, value):
        used_funcs = self.fmt_used_condition(value)
        if not used_funcs:
            return
        filter_users = self.user_ids
        for func_type in used_funcs:
            rows = []
            match func_type:
                case ActivityCondition.UsedType.SPOT:
                    rows = UserTradeSummary.query.filter(
                        UserTradeSummary.system == UserTradeSummary.System.SPOT,
                        UserTradeSummary.user_id.in_(filter_users)
                    ).with_entities(
                        UserTradeSummary.user_id
                    ).all()
                case ActivityCondition.UsedType.PERPETUAL:
                    rows = UserTradeSummary.query.filter(
                        UserTradeSummary.system == UserTradeSummary.System.PERPETUAL,
                        UserTradeSummary.user_id.in_(filter_users)
                    ).with_entities(
                        UserTradeSummary.user_id
                    ).all()
                case ActivityCondition.UsedType.MARGIN:
                    rows = MarginLoanOrder.query.filter(
                        MarginLoanOrder.user_id.in_(filter_users)
                    ).with_entities(
                        MarginLoanOrder.user_id
                    ).all()
                case ActivityCondition.UsedType.EXCHANGE:
                    rows = AssetExchangeOrder.query.filter(
                        AssetExchangeOrder.user_id.in_(filter_users)
                    ).with_entities(
                        AssetExchangeOrder.user_id
                    ).all()
                case ActivityCondition.UsedType.FIAT:
                    rows = FiatOrder.query.filter(
                        FiatOrder.user_id.in_(filter_users)
                    ).with_entities(
                        FiatOrder.user_id
                    ).all()
                case ActivityCondition.UsedType.INVESTMENT:
                    rows = InvestmentBalanceHistory.query.filter(
                        InvestmentBalanceHistory.user_id.in_(filter_users)
                    ).with_entities(
                        InvestmentBalanceHistory.user_id
                    ).all()
                case ActivityCondition.UsedType.AMM:
                    rows = LiquidityHistory.query.filter(
                        LiquidityHistory.user_id.in_(filter_users)
                    ).with_entities(
                        LiquidityHistory.user_id
                    ).all()
                case ActivityCondition.UsedType.AUTO_INVEST:
                    rows = AutoInvestPlan.query.filter(
                        AutoInvestPlan.user_id.in_(filter_users)
                    ).with_entities(
                        AutoInvestPlan.user_id
                    ).all()
                case ActivityCondition.UsedType.SPOT_GRID:
                    rows = UserStrategy.query.filter(
                        UserStrategy.user_id.in_(filter_users)
                    ).with_entities(
                        UserStrategy.user_id
                    ).all()
                case ActivityCondition.UsedType.PLEDGE:
                    rows = PledgePosition.query.filter(
                        PledgePosition.user_id.in_(filter_users)
                    ).with_entities(
                        PledgePosition.user_id
                    ).all()
            filter_users &= {i.user_id for i in rows}
        self.user_ids = filter_users

    @classmethod
    def _get_trade_before_start_user_list(cls, activity_id: int) -> Set[int]:
        raise NotImplementedError

    @classmethod
    def fmt_used_condition(cls, used_condition: str) -> [ActivityCondition.UsedType]:
        if not used_condition:
            return []
        return [ActivityCondition.UsedType[func_] for func_ in used_condition.split(",")]

    @classmethod
    def _get_user_balance_usd(cls, user_id):
        price_map = PriceManager.assets_to_usd()
        sub_user_ids = [i.user_id for i in SubAccount.query.filter(
            SubAccount.main_user_id == user_id, SubAccount.status == SubAccount.Status.VALID).all()]
        bm = BalanceManager(user_id, sub_user_ids, price_map)
        current_balance_usd = bm.get_current_balance_usd()
        return amount_to_str(current_balance_usd, 2)

    @classmethod
    def get_user_trade_value_map(cls, days, trade_type_range):
        # 取进x天的数据（到今天最新一个小时）
        st = today_datetime() - timedelta(days=days - 1)
        et = now().replace(minute=0, second=0, microsecond=0)
        _type = ActivityCondition.TradeType
        func_map = {
            _type.ALL.name: get_all_user_trade_map,
            _type.SPOT.name: get_all_user_spot_trade_map,
            _type.PERPETUAL.name: get_all_user_perpetual_map,
            _type.EXCHANGE.name: get_all_exchange_tade_map,
            _type.AMM.name: get_all_user_amm_trade_map,
            _type.SPOT_GRID.name: get_all_user_spot_grid_trade_map,
            _type.AUTO_INVEST.name: get_all_user_auto_invest_trade_map,
        }
        user_trade_map = defaultdict(Decimal)
        for trade_type in trade_type_range:
            if trade_type in func_map:
                trade_map = func_map[trade_type](st, et)
                for _user_id, amount in trade_map.items():
                    user_trade_map[_user_id] += amount
        return user_trade_map


class DiscountActivityValidator(ActivityValidator):
    @classmethod
    def _get_trade_before_start_user_list(cls, activity_id):
        statistic = DiscountActivityStatistic.query.filter(
            DiscountActivityStatistic.discount_activity_id == activity_id,
            DiscountActivityStatistic.business_type == DiscountActivityStatistic.BusinessType.TRADE_BEFORE_START,
        ).first()
        if not statistic:
            return set()
        return set(statistic.get_user_ids())


class AirdropActivityValidator(ActivityValidator):
    @classmethod
    def _get_trade_before_start_user_list(cls, activity_id):
        statistic = AirdropActivityStatistic.query.filter(
            AirdropActivityStatistic.airdrop_activity_id == activity_id,
            AirdropActivityStatistic.business_type == AirdropActivityStatistic.BusinessType.TRADE_BEFORE_START,
        ).first()
        if not statistic:
            return set()
        return set(statistic.get_user_ids())
