# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from random import seed, randint
from enum import Enum
from typing import Dict, List, NamedTuple, Optional, Set, Tuple
import time

from dateutil.relativedelta import relativedelta
from flask_babel import gettext, force_locale
from sqlalchemy import func

from app.exceptions import BoxGrabEnd, UserAlreadyOpenBox
from app.common import Language, MessageTitle, MessageContent
from app.common.events import FifthEvent
from app.config import config
from app.business import (
    current_app,
    BalanceBusiness,
    CacheLock,
    LockKeys,
    ExchangeLogDB,
    TradeSummaryDB,
    TradeHistoryDB,
    PerpetualHistoryDB,
)
from app.business.clients.biz_monitor import biz_monitor
from app.models import (
    db,
    AssetPrice,
    Activity,
    User,
    VipUser,
    GiftHistory,
    InvestmentBalanceHistory,
    Deposit,
    UserTradeSummary,
    UserTradeFeeSummary,
    Message,
    UserPreference,
)
from app.models.exchange import AssetExchangeOrder
from app.models.activity import FifthAnniversaryChallenge, FifthAnniversaryBox, Coupon, CouponApply
from app.caches.activity import (
    FifthBoxRemainNumCache,
    FifthBoxRewardRemainNumCache,
    FifthUserBoxRewardCache,
    FifthBroadcastCache,
    DynamicUserCouponCache,
    FifthRiskEventCache,
    FifthDealHunterUserCache,
    FifthRiskUserCache,
    FifthUserFeeCache,
)
from app.utils import (
    now,
    today,
    batch_iter,
    hide_email,
    current_timestamp,
    today_datetime,
    quantize_amount,
    amount_to_str,
)
from app.utils.parser import JsonEncoder


class _Reward(NamedTuple):
    # 盲盒奖品
    name: str  # 奖品名称
    total: int  # 批次中奖品总数
    can_weight: bool  # 抽奖时是否可被加权


# 周年盲盒-奖品相关
ANNIVERSARY_BOX_BATCH_NUM = 5  # 盲盒奖品批次数目
ANNIVERSARY_BOX_BATCH_REWARD_NUM = 8000  # 盲盒奖品批次 奖品总数目
ANNIVERSARY_BOX_NUM = ANNIVERSARY_BOX_BATCH_NUM * ANNIVERSARY_BOX_BATCH_REWARD_NUM  # 盲盒总数
ANNIVERSARY_BOX_REWARDS = (
    # 周年盲盒-奖品列表
    _Reward(FifthAnniversaryBox.RewardType.USDT_200.name, 1, True),
    _Reward(FifthAnniversaryBox.RewardType.USDT_50.name, 4, True),
    _Reward(FifthAnniversaryBox.RewardType.USDT_10.name, 140, True),
    _Reward(FifthAnniversaryBox.RewardType.CET_100.name, 2189, True),
    _Reward(FifthAnniversaryBox.RewardType.CASHBACK_FEE_COUPON.name, 2000, False),
    _Reward(FifthAnniversaryBox.RewardType.EXPERIENCE_FEE_COUPON.name, 2000, False),
    _Reward(FifthAnniversaryBox.RewardType.VIP.name, 1666, False),
)

# 终极盲盒-奖品相关
ULTIMATE_BOX_BATCH_NUM = 5
ULTIMATE_BOX_BATCH_REWARD_NUM = 4000
ULTIMATE_BOX_NUM = ULTIMATE_BOX_BATCH_NUM * ULTIMATE_BOX_BATCH_REWARD_NUM
ULTIMATE_BOX_REWARDS = (
    _Reward(FifthAnniversaryBox.RewardType.USDT_2000.name, 1, True),
    _Reward(FifthAnniversaryBox.RewardType.USDT_500.name, 2, True),
    _Reward(FifthAnniversaryBox.RewardType.USDT_200.name, 10, True),
    _Reward(FifthAnniversaryBox.RewardType.USDT_50.name, 104, False),
    _Reward(FifthAnniversaryBox.RewardType.CET_200.name, 3883, False),
)


class UserWorth(Enum):
    HIGH = "高价值"
    MIDDLE = "中价值"
    LOW = "低价值"


USER_WORTH_WEIGHT_MAP = {
    # 用户价值: 权重
    UserWorth.HIGH: 10,
    UserWorth.MIDDLE: 10,
    UserWorth.LOW: 1,
}


def report_ip_coupon_risk_event(ip: str, user_id: int):
    biz_monitor.increase_counter(
        FifthEvent.IP_COUPON_LIMIT_COUNT,
        with_source=True,
    )
    biz_monitor.increase_uniq_counter(
        FifthEvent.IP_COUPON_LIMIT_NUM,
        value=[user_id],
        with_source=True,
    )
    FifthRiskEventCache("ip-coupon", today()).rpush(f"{ip}:{user_id}")


def report_ip_box_risk_event(ip: str, user_id: int):
    biz_monitor.increase_counter(
        FifthEvent.IP_BOX_LIMIT_COUNT,
        with_source=True,
    )
    biz_monitor.increase_uniq_counter(
        FifthEvent.IP_BOX_LIMIT_NUM,
        value=[user_id],
        with_source=True,
    )
    FifthRiskEventCache("ip-box", today()).rpush(f"{ip}:{user_id}")


def report_device_box_risk_event(device_id: str, user_id: int):
    biz_monitor.increase_counter(
        FifthEvent.DEVICE_BOX_LIMIT_COUNT,
        with_source=True,
    )
    biz_monitor.increase_uniq_counter(
        FifthEvent.DEVICE_BOX_LIMIT_NUM,
        value=[user_id],
        with_source=True,
    )
    FifthRiskEventCache("device-box", today()).rpush(f"{device_id}:{user_id}")


def get_weight_by_trade_fee(trade_fee_usd: Decimal) -> int:
    if trade_fee_usd >= Decimal("31"):
        user_worth = UserWorth.HIGH
    elif trade_fee_usd >= Decimal("5"):
        user_worth = UserWorth.MIDDLE
    else:
        user_worth = UserWorth.LOW
    return USER_WORTH_WEIGHT_MAP[user_worth]


def draw_a_bucket(buckets: List[Dict]) -> Optional[str]:
    # 随机抽奖
    if not buckets:
        return None

    max_ = buckets[-1]["end"]
    seed()
    random_num = randint(1, max_)
    reward = None
    for bucket in buckets:
        if random_num <= bucket["end"]:
            reward = bucket["reward"]
            break
    return reward


class FifthBoxManager:

    NEW_USER_REGISTRATION_DT = date(2022, 12, 22)  # 晚于此时间都算新用户
    ANN_NEW_USER_REGISTRATION_DT = date(2022, 12, 28)  # 周年盲盒晚于此时间都算新用户

    def __init__(self, box_type: FifthAnniversaryBox.BoxType):
        self.box_type = box_type

    def calc_batch_num(self, box_remain_num: int) -> int:
        # 计算当前盲盒的奖品批次
        if self.box_type == FifthAnniversaryBox.BoxType.ANNIVERSARY:
            batch_reward_num = ANNIVERSARY_BOX_BATCH_REWARD_NUM
            total_batch_num = ANNIVERSARY_BOX_BATCH_NUM
        else:
            batch_reward_num = ULTIMATE_BOX_BATCH_REWARD_NUM
            total_batch_num = ULTIMATE_BOX_BATCH_NUM

        box_grab_num = batch_reward_num * total_batch_num - box_remain_num  # 已抢数目
        cur_batch_num = 1
        while cur_batch_num <= total_batch_num:
            if box_grab_num < cur_batch_num * batch_reward_num:
                return cur_batch_num
            cur_batch_num += 1
        return total_batch_num

    def gen_weight_lottery_buckets(self, user_weight: int, reward_remain_map: Dict[str, int]) -> List:
        if self.box_type == FifthAnniversaryBox.BoxType.ANNIVERSARY:
            reward_defs = ANNIVERSARY_BOX_REWARDS
        else:
            reward_defs = ULTIMATE_BOX_REWARDS

        buckets = []
        weighted_end = 0
        for reward_def in reward_defs:
            reward_def: _Reward
            remain = reward_remain_map.get(reward_def.name, 0)
            weight = user_weight if reward_def.can_weight else 1
            if remain > 0:
                start = weighted_end
                end = start + weight * remain
                buckets.append(
                    {
                        "reward": reward_def.name,
                        "end": end,
                    }
                )
                weighted_end = end
        return buckets

    def build_user_profile(self, user: User, row: FifthAnniversaryChallenge) -> dict:
        """ 权重、是否合约交易、是否vip5、是否是羊毛党 """
        user_id = row.user_id
        weight = get_weight_by_trade_fee(row.trade_fee_usd)
        vip5_row = VipUser.query.filter(
            VipUser.user_id == user_id,
            VipUser.status == VipUser.StatusType.PASS,
            VipUser.level == 5,
        ).first()
        is_new_user = is_deal_hunter = False
        if user.created_at.date() >= self.NEW_USER_REGISTRATION_DT:
            # 判断最近注册的新用户是否是羊毛党
            is_deal_hunter = FifthDealHunterUserCache().sismember(str(user_id))
        if user.created_at.date() >= self.ANN_NEW_USER_REGISTRATION_DT:
            is_new_user = True

        user_profile = {
            "weight": weight,
            "has_perpetual_trade": row.perpetual_deal_usd > Decimal(),
            "is_vip5": bool(vip5_row),
            "is_deal_hunter": is_deal_hunter,
            "is_new_user": is_new_user,
        }
        return user_profile

    def open_box(self, user_id: int, user_profile: dict) -> str:
        # 开盲盒
        with CacheLock(LockKeys.fifth_box(self.box_type.name), wait=False):
            exist_box_type, exist_reward = FifthUserBoxRewardCache().get_user_reward(user_id)
            if exist_box_type or exist_reward:
                raise UserAlreadyOpenBox

            box_remain_cache = FifthBoxRemainNumCache(self.box_type.name)
            box_remain = int(box_remain_cache.read())
            if box_remain <= 0:
                raise BoxGrabEnd

            if self.box_type == FifthAnniversaryBox.BoxType.ULTIMATE and user_profile.get("is_deal_hunter"):
                # 终极盲盒 羊毛党用户直接发vip5奖品，同时vip5奖品不算在盲盒总数中
                deal_hunter_reward = FifthAnniversaryBox.RewardType.VIP5.name
                current_app.logger.warning(f"deal_hunter user:{user_id} reward: {deal_hunter_reward}")
                # 只写用户奖励，不减盲盒总数，也不减盲盒批次奖品剩余数目
                FifthUserBoxRewardCache().set_user_reward(user_id, self.box_type.name, deal_hunter_reward)
                return deal_hunter_reward
            if self.box_type == FifthAnniversaryBox.BoxType.ANNIVERSARY and user_profile.get("is_new_user"):
                new_user_reward = FifthAnniversaryBox.RewardType.VIP.name
                current_app.logger.warning(f"new_user_reward user:{user_id} reward: {new_user_reward}")
                # 只写用户奖励，不减盲盒总数，也不减盲盒批次奖品剩余数目
                FifthUserBoxRewardCache().set_user_reward(user_id, self.box_type.name, new_user_reward)
                return new_user_reward

            #
            batch_num = self.calc_batch_num(box_remain)
            reward_remain_cache = FifthBoxRewardRemainNumCache(box_type=self.box_type.name, batch_num=batch_num)
            reward_remain_map = reward_remain_cache.get_remain_data()
            if sum(reward_remain_map.values()) <= 0:
                raise BoxGrabEnd

            reward = self.draw_reward(reward_remain_map, user_profile)
            current_app.logger.warning(
                f"user:{user_id} draw {self.box_type.name} box_remain: {box_remain} batch:{batch_num} reward: {reward}"
            )
            if not reward:
                raise BoxGrabEnd

            # 先减盲盒剩余数目、奖品剩余数目，再写用户奖励
            box_remain_cache.incr(-1)
            reward_remain_cache.hincrby(reward, -1)
            FifthUserBoxRewardCache().set_user_reward(user_id, self.box_type.name, reward)
            return reward

    def draw_reward(self, reward_remain_map: Dict, user_profile: Dict) -> Optional[str]:
        user_weight = user_profile["weight"]
        if self.box_type == FifthAnniversaryBox.BoxType.ANNIVERSARY:
            # 周年盲盒-先尝试避开奖品限制（有合约交易不发合约体验金，VIP5不发VIP）
            exclude_rewards = []
            if user_profile.get("has_perpetual_trade"):
                exclude_rewards.append(FifthAnniversaryBox.RewardType.EXPERIENCE_FEE_COUPON.name)
            if user_profile.get("is_vip5"):
                exclude_rewards.append(FifthAnniversaryBox.RewardType.VIP.name)
            if exclude_rewards:
                new_reward_remain_map = dict(reward_remain_map)
                for k in exclude_rewards:
                    new_reward_remain_map[k] = 0
                if sum(new_reward_remain_map.values()) > 0:
                    buckets = self.gen_weight_lottery_buckets(user_weight, new_reward_remain_map)
                    reward = draw_a_bucket(buckets)
                    if reward:
                        return reward

        # 正常抽奖
        buckets = self.gen_weight_lottery_buckets(user_weight, reward_remain_map)
        reward = draw_a_bucket(buckets)
        return reward


class FifthChallengeTaskManager:

    start_date = date(2022, 1, 1)

    @classmethod
    def get_has_exchange_user_ids(cls) -> Set[int]:
        """ 兑换user_ids """
        has_exchange_users = AssetExchangeOrder.query.with_entities(
            func.distinct(AssetExchangeOrder.user_id).label('user_id')
        ).all()
        return {i.user_id for i in has_exchange_users}

    @classmethod
    def get_has_investment_user_ids(cls) -> Set[int]:
        """ 理财user_ids """
        inv_transfer_in_rows = InvestmentBalanceHistory.query.filter(
            InvestmentBalanceHistory.created_at >= cls.start_date,
            InvestmentBalanceHistory.opt_type == InvestmentBalanceHistory.OptType.IN,
            InvestmentBalanceHistory.status == InvestmentBalanceHistory.StatusType.SUCCESS,
        ).with_entities(
            func.distinct(InvestmentBalanceHistory.user_id).label('user_id')
        ).all()
        return {i.user_id for i in inv_transfer_in_rows}

    @classmethod
    def batch_get_user_deal_map(cls, user_ids: List[int]) -> Tuple[Dict, Dict]:
        """ 批量查询用户现货、合约成交额 """
        user_spot_deal_usd_map = defaultdict(Decimal)
        user_perpetual_deal_usd_map = defaultdict(Decimal)
        for ch_user_ids in batch_iter(user_ids, 5000):
            trade_rows = UserTradeSummary.query.filter(
                UserTradeSummary.user_id.in_(ch_user_ids),
                UserTradeSummary.report_date >= cls.start_date,
            ).group_by(
                UserTradeSummary.user_id,
                UserTradeSummary.system,
            ).with_entities(
                UserTradeSummary.user_id,
                UserTradeSummary.system,
                func.sum(UserTradeSummary.trade_amount).label("amount"),
            ).all()
            for row in trade_rows:
                if row.system == UserTradeSummary.System.SPOT:
                    user_spot_deal_usd_map[row.user_id] = row.amount
                else:
                    user_perpetual_deal_usd_map[row.user_id] = row.amount
        return user_spot_deal_usd_map, user_perpetual_deal_usd_map

    @classmethod
    def batch_get_on_chain_deposit_usd(cls, user_ids: List[int], daily_asset_price_map: Dict) -> Dict[int, Decimal]:
        """ 批量查询链上充值 """
        start_deposit_id = 16766755
        user_on_chain_usd_map = defaultdict(Decimal)
        for ch_user_ids in batch_iter(user_ids, 5000):
            deposit_rows = Deposit.query.filter(
                Deposit.id > start_deposit_id,
                Deposit.user_id.in_(ch_user_ids),
                Deposit.type == Deposit.Type.ON_CHAIN,
                Deposit.status.in_(
                    [
                        Deposit.Status.CONFIRMING,
                        Deposit.Status.TO_HOT,
                        Deposit.Status.FINISHED,
                    ]
                ),
            ).with_entities(
                Deposit.user_id,
                Deposit.asset,
                func.max(Deposit.created_at).label("created_at"),
                func.sum(Deposit.amount).label("amount"),
            ).group_by(
                Deposit.user_id,
                Deposit.asset,
            ).all()
            for row in deposit_rows:
                asset_price_map = daily_asset_price_map.get(row.created_at.date(), {})
                if row.asset not in asset_price_map:
                    continue
                usd = quantize_amount(row.amount * asset_price_map[row.asset], 8)
                user_on_chain_usd_map[row.user_id] += usd
        return user_on_chain_usd_map

    @classmethod
    def get_on_chain_deposit_usd(cls, user_id: int, daily_asset_price_map: Dict) -> Decimal:
        deposit_rows = Deposit.query.filter(
            Deposit.user_id == user_id,
            Deposit.created_at >= cls.start_date,
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.status.in_(
                [
                    Deposit.Status.CONFIRMING,
                    Deposit.Status.TO_HOT,
                    Deposit.Status.FINISHED,
                ]
            ),
        ).all()
        on_chain_usd = Decimal()
        for row in deposit_rows:
            asset_price_map = daily_asset_price_map.get(row.created_at.date(), {})
            if row.asset not in asset_price_map:
                continue
            on_chain_usd += row.amount * asset_price_map[row.asset]
        return on_chain_usd

    @classmethod
    def batch_get_spot_today_deal_usd(cls, user_ids: List[int]) -> Dict[int, Decimal]:
        """ 批量获取当天的现货成交额 """
        user_deal_usd_map = defaultdict(Decimal)
        start_at_time = int(today_datetime().timestamp())
        dbs_tables = TradeHistoryDB.users_to_dbs_and_tables(user_ids, 'user_deal_history')
        for db_tables in dbs_tables:
            _db = db_tables[0]
            for _table, _table_user_ids in db_tables[1].items():
                for ch_user_ids in batch_iter(_table_user_ids, 5000):
                    user_ids_str = ','.join(map(str, ch_user_ids))
                    where = f' `user_id` in ({user_ids_str})  AND `time` > {start_at_time} '
                    records = _db.table(_table).select(
                        *["user_id", "SUM(`amount` * `price`) as deal_usd"],
                        where=where,
                        group_by="user_id"
                    )
                    for row in records:
                        user_deal_usd_map[row[0]] = quantize_amount(row[1], 2)
        return user_deal_usd_map

    @classmethod
    def get_spot_today_deal_usd(cls, user_id: int) -> Decimal:
        # 获取当天的现货成交额
        _db, _table = TradeHistoryDB.user_to_db_and_table(user_id, 'user_deal_history')
        start_at_time = int(today_datetime().timestamp())
        where = f' `user_id` = {user_id}  ' \
                f' AND `time` > {start_at_time} '
        records = _db.table(_table).select(
            "SUM(`amount` * `price`) as deal_usd",
            where=where,
        )
        deal_usd = Decimal()
        for row in records:
            deal_usd = row[0] or Decimal()
            break
        return quantize_amount(deal_usd, 2)

    @classmethod
    def batch_get_perpetual_today_deal_usd(cls, user_ids: List[int]) -> Dict[int, Decimal]:
        """ 批量获取当天的合约成交额 """
        user_deal_usd_map = defaultdict(Decimal)
        start_at_time = int(today_datetime().timestamp())
        dbs_tables = PerpetualHistoryDB.users_to_dbs_and_tables(user_ids, 'deal_history')
        for db_tables in dbs_tables:
            _db = db_tables[0]
            for _table, _table_user_ids in db_tables[1].items():
                for ch_user_ids in batch_iter(_table_user_ids, 5000):
                    user_ids_str = ','.join(map(str, ch_user_ids))
                    where = f' `user_id` in ({user_ids_str})  AND `time` > {start_at_time} '
                    records = _db.table(_table).select(
                        *["user_id", "SUM(`deal_stock`) as `deal_usd`"],
                        where=where,
                        group_by="user_id"
                    )
                    for row in records:
                        user_deal_usd_map[row[0]] = quantize_amount(row[1], 2)
        return user_deal_usd_map

    @classmethod
    def get_perpetual_today_deal_usd(cls, user_id: int) -> Decimal:
        # 获取当天的合约成交额
        _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'deal_history')
        start_at_time = int(today_datetime().timestamp())
        where = f' `user_id` = {user_id}  ' \
                f' AND `time` > {start_at_time} '
        records = _db.table(_table).select(
            "SUM(`deal_stock`) as `deal_usd`",
            where=where,
        )
        deal_usd = Decimal()
        for row in records:
            deal_usd = row[0] or Decimal()
            break
        return quantize_amount(deal_usd, 2)

    @classmethod
    def batch_get_user_trade_fee_usd(cls, user_ids: List[int]) -> Dict[int, Decimal]:
        user_trade_fee_usd_map = {}
        for ch_user_ids in batch_iter(user_ids, 2000):
            trade_fee_rows = UserTradeFeeSummary.query.filter(
                UserTradeFeeSummary.user_id.in_(ch_user_ids),
                UserTradeFeeSummary.report_date >= cls.start_date,
            ).group_by(
                UserTradeFeeSummary.user_id,
            ).with_entities(
                UserTradeFeeSummary.user_id,
                func.sum(UserTradeFeeSummary.trade_fee_amount),
            ).all()
            user_trade_fee_usd_map.update(dict(trade_fee_rows))
        return user_trade_fee_usd_map

    @classmethod
    def update_task_status(cls, row: FifthAnniversaryChallenge) -> bool:
        now_ = now()
        ann_msg = ult_msg = None
        if not row.box1_finished_at and row.check_box1_task_finished:
            row.box1_finished_at = now_
            ann_msg = new_box_qualify_message(row.user_id, FifthAnniversaryBox.BoxType.ANNIVERSARY)
        if not row.finished_at and row.check_all_task_finished:
            row.finished_at = now_
            ult_msg = new_box_qualify_message(row.user_id, FifthAnniversaryBox.BoxType.ULTIMATE)

        # 同时完成终极盲盒和周年盲盒，只发终极盲盒的站内信
        if ult_msg:
            db.session.add(ult_msg)
        elif ann_msg:
            db.session.add(ann_msg)

        is_updated = bool(ult_msg or ann_msg)
        return is_updated


class FifthBoxRewardSender:

    VIP_REWARDS = [
        FifthAnniversaryBox.RewardType.VIP,
        FifthAnniversaryBox.RewardType.VIP5,
    ]

    COUPON_REWARDS = [
        FifthAnniversaryBox.RewardType.EXPERIENCE_FEE_COUPON,
        FifthAnniversaryBox.RewardType.CASHBACK_FEE_COUPON,
    ]

    ASSET_REWARD_ASSET_AMOUNT_MAP = {
            FifthAnniversaryBox.RewardType.USDT_2000: ["USDT", 2000],
            FifthAnniversaryBox.RewardType.USDT_500: ["USDT", 500],
            FifthAnniversaryBox.RewardType.USDT_200: ["USDT", 200],
            FifthAnniversaryBox.RewardType.USDT_50: ["USDT", 50],
            FifthAnniversaryBox.RewardType.USDT_10: ["USDT", 10],
            FifthAnniversaryBox.RewardType.CET_200: ["CET", 200],
            FifthAnniversaryBox.RewardType.CET_100: ["CET", 100],
        }

    @classmethod
    def send(cls, row: FifthAnniversaryBox):
        if row.reward in cls.VIP_REWARDS:
            return cls.send_vip(row)
        elif row.reward in cls.COUPON_REWARDS:
            return cls.send_coupon(row)
        elif row.reward in cls.ASSET_REWARD_ASSET_AMOUNT_MAP:
            return cls.send_asset(row)

    @classmethod
    def send_vip(cls, row: FifthAnniversaryBox):
        from app.business.vip import VipHelper
        from app.business.fee import update_user_fee_task

        user_id = row.user_id
        now_ = now()
        new_expired_time = now_ + relativedelta(months=1)
        vip_user: VipUser = VipUser.query.filter(
            VipUser.user_id == user_id,
        ).first()
        is_vip5_reward = row.reward == FifthAnniversaryBox.RewardType.VIP5
        if is_vip5_reward:
            vip_remark = "fifth anniversary vip5 reward"
        else:
            vip_remark = "fifth anniversary vip level up reward"
        is_update_user_fee = False
        if not vip_user:
            # 不是vip
            new_lock_level = 5 if is_vip5_reward else 1
            vip_user = VipUser(
                user_id=user_id,
                level=new_lock_level,
                real_level=new_lock_level,
                lock_level=new_lock_level,
                status=VipUser.StatusType.PASS,
                is_lock=True,
                remark=vip_remark,
                expired_time=new_expired_time,
            )
            db.session.add(vip_user)
            vip_change_history_data = dict(
                user_id=user_id,
                old_level=0,
                new_level=vip_user.level,
                new_lock_level=vip_user.lock_level,
                old_lock_level=0,
                expired_time=new_expired_time,
                admin_user_id=None,
            )
            is_update_user_fee = True
        elif vip_user.status != VipUser.StatusType.PASS:
            # vip已失效
            new_lock_level = 5 if is_vip5_reward else 1
            vip_user.status = VipUser.StatusType.PASS
            vip_user.real_level = new_lock_level
            vip_user.lock_level = new_lock_level
            vip_user.level = new_lock_level
            vip_user.is_lock = True
            vip_user.expired_time = new_expired_time
            vip_user.remark = vip_remark
            vip_change_history_data = dict(
                user_id=user_id,
                old_level=0,
                new_level=vip_user.level,
                new_lock_level=vip_user.lock_level,
                old_lock_level=0,
                expired_time=new_expired_time,
                admin_user_id=None,
            )
            is_update_user_fee = True
        else:
            # 已是vip
            old_level = vip_user.level
            old_lock_level = vip_user.lock_level
            if is_vip5_reward:
                new_level = new_lock_level = 5
            else:
                new_level = min(old_level + 1, 5)
                # 可能之前没保底等级
                new_lock_level = max(new_level, old_lock_level + 1)
                new_lock_level = min(new_lock_level, 5)

            vip_user.is_lock = True
            if not vip_user.lock_level or vip_user.lock_level < new_lock_level:
                vip_user.lock_level = new_lock_level
            if not vip_user.expired_time or vip_user.expired_time < new_expired_time:
                vip_user.expired_time = new_expired_time
            vip_user.level = max(new_lock_level, vip_user.real_level, new_level)
            if vip_user.level != old_level:
                is_update_user_fee = True
            if not vip_user.remark:
                vip_user.remark = vip_remark
            vip_change_history_data = dict(
                user_id=user_id,
                old_level=old_level,
                new_level=vip_user.level,
                new_lock_level=vip_user.lock_level,
                old_lock_level=old_lock_level,
                expired_time=new_expired_time,
                admin_user_id=None,
            )

        row.status = FifthAnniversaryBox.Status.FINISHED
        row.send_at = now()
        db.session.commit()

        if vip_change_history_data:
            VipHelper.add_change_history(**vip_change_history_data)
        if is_update_user_fee:
            update_user_fee_task.delay(user_id)

    @classmethod
    def send_coupon(cls, row: FifthAnniversaryBox):
        if row.reward == FifthAnniversaryBox.RewardType.CASHBACK_FEE_COUPON:
            coupon_type = Coupon.CouponType.CASHBACK_FEE
        else:
            coupon_type = Coupon.CouponType.EXPERIENCE_FEE
        # 先写缓存
        DynamicUserCouponCache(
            CouponApply.DynamicUser.FIVE_ACTIVITY_GIFT.name,
            coupon_type.name,
        ).add_users([row.user_id])

        row.status = FifthAnniversaryBox.Status.FINISHED
        row.send_at = now()
        db.session.commit()

    @classmethod
    def send_asset(cls, row: FifthAnniversaryBox):
        asset, amount = cls.ASSET_REWARD_ASSET_AMOUNT_MAP[row.reward]
        fifth_activity_id = Activity.get_or_create_fifth_anniversary_activity_id()
        gift_record = GiftHistory(
            user_id=row.user_id,
            activity_id=fifth_activity_id,
            asset=asset,
            amount=amount,
            lock_time=0,
            status=GiftHistory.Status.CREATED,
            remark=f"gift for fifth anniversary reward activity_id:{fifth_activity_id}",
        )
        db.session.add(gift_record)
        row.status = FifthAnniversaryBox.Status.FINISHED
        row.send_at = now()
        db.session.commit()


def update_fifth_user_fee():
    # 更新大挑战用户手续费
    box_user_ids = FifthUserBoxRewardCache().hkeys()
    box_user_ids = {int(i) for i in box_user_ids}
    challenge_rows = FifthAnniversaryChallenge.query.filter(
        FifthAnniversaryChallenge.box1_finished_at.is_not(None),
    ).with_entities(FifthAnniversaryChallenge.user_id.distinct()).all()
    challenge_user_ids = {i[0] for i in challenge_rows}

    cache = FifthUserFeeCache()
    old_fee_map = {int(k): Decimal(v) for k, v in cache.read().items()}

    to_update_user_ids = challenge_user_ids - box_user_ids
    to_update_user_ids = {i for i in to_update_user_ids if old_fee_map.get(i, Decimal()) < Decimal("3")}
    user_fee_usd_map = FifthChallengeTaskManager.batch_get_user_trade_fee_usd(list(to_update_user_ids))

    data = {str(uid): amount_to_str(usd, 2) for uid, usd in user_fee_usd_map.items()}
    cache.hmset(data)


def get_to_update_challenge_rows() -> List[FifthAnniversaryChallenge]:
    today_ = today()
    if today_ >= date(2023, 1, 8):
        return []

    rows: List[FifthAnniversaryChallenge] = FifthAnniversaryChallenge.query.filter(
        FifthAnniversaryChallenge.finished_at.is_(None),
    ).all()
    current_app.logger.warning(f"get_to_update_challenge_rows to_update_rows: {len(rows)}")
    return rows


def update_user_challenge_task_status():
    """ 更新用户大挑战任务的状态 """
    rows: List[FifthAnniversaryChallenge] = get_to_update_challenge_rows()
    if not rows:
        return

    # 批量更新是否兑换和理财（比较快）
    has_exchange_user_ids = FifthChallengeTaskManager.get_has_exchange_user_ids()
    has_investment_user_ids = FifthChallengeTaskManager.get_has_investment_user_ids()
    updated_num = 0
    for row in rows:
        if not row.has_exchange_trade:
            row.has_exchange_trade = row.user_id in has_exchange_user_ids
        if not row.has_investment:
            row.has_investment = row.user_id in has_investment_user_ids

        if FifthChallengeTaskManager.update_task_status(row):
            updated_num += 1
    db.session.commit()
    current_app.logger.warning(f"update_user_challenge_task_status updated_num: {updated_num}")
    update_fifth_user_fee()  # 任务状态更新后 同时更新手续费


def update_user_challenge_deal_task():
    # 更新现货、合约交易额
    to_update_rows: List[FifthAnniversaryChallenge] = get_to_update_challenge_rows()
    if not to_update_rows:
        return

    rows = [row for row in to_update_rows if not row.spot_deal_usd_qualified or not row.perpetual_deal_usd_qualified]
    user_row_map = {r.user_id: r for r in rows}
    user_spot_deal_usd_map, user_perpetual_deal_usd_map = FifthChallengeTaskManager.batch_get_user_deal_map(list(user_row_map))
    today_spot_user_ids = set()
    today_per_user_ids = set()
    for row in rows:
        if not row.spot_deal_usd_qualified:
            spot_deal_usd = user_spot_deal_usd_map.get(row.user_id, 0)
            row.spot_deal_usd = spot_deal_usd
            if not row.spot_deal_usd_qualified:
                today_spot_user_ids.add(row.user_id)
        if not row.perpetual_deal_usd_qualified:
            perpetual_deal_usd = user_perpetual_deal_usd_map.get(row.user_id, 0)
            row.perpetual_deal_usd = perpetual_deal_usd
            if not row.perpetual_deal_usd_qualified:
                today_per_user_ids.add(row.user_id)

    if today_spot_user_ids:
        today_spot_deal_usd_map = FifthChallengeTaskManager.batch_get_spot_today_deal_usd(list(today_spot_user_ids))
        for user_id in today_spot_user_ids:
            row = user_row_map[user_id]
            today_spot_deal_usd = today_spot_deal_usd_map.get(row.user_id, 0)
            row.spot_deal_usd = row.spot_deal_usd + today_spot_deal_usd
    if today_per_user_ids:
        today_per_deal_usd_map = FifthChallengeTaskManager.batch_get_perpetual_today_deal_usd(list(today_per_user_ids))
        for user_id in today_per_user_ids:
            row = user_row_map[user_id]
            today_per_deal_usd = today_per_deal_usd_map.get(row.user_id, 0)
            row.perpetual_deal_usd = row.perpetual_deal_usd + today_per_deal_usd

    db.session.commit()


def update_user_challenge_deposit_task():
    # 更新链上充值
    to_update_rows: List[FifthAnniversaryChallenge] = get_to_update_challenge_rows()
    if not to_update_rows:
        return

    rows = [row for row in to_update_rows if not row.on_chain_deposit_usd_qualified]
    start_date = FifthChallengeTaskManager.start_date
    end_date = today()
    daily_asset_price_map = AssetPrice.get_close_price_range_map(start_date, end_date)
    no_deposit_user_ids = [row.user_id for row in rows]
    user_on_chain_usd_map = FifthChallengeTaskManager.batch_get_on_chain_deposit_usd(no_deposit_user_ids, daily_asset_price_map)
    for row in rows:
        if not row.on_chain_deposit_usd_qualified:
            row.on_chain_deposit_usd = user_on_chain_usd_map.get(row.user_id, 0)
    db.session.commit()


def update_fifth_qualify_broadcast_cache(nums):
    # 开箱机会 (取最新的2000条)
    ann_rows: List[FifthAnniversaryChallenge] = (
        FifthAnniversaryChallenge.query.filter(
            FifthAnniversaryChallenge.box1_finished_at.is_not(None),
        )
        .order_by(FifthAnniversaryChallenge.box1_finished_at.desc())
        .with_entities(
            FifthAnniversaryChallenge.user_id,
            FifthAnniversaryChallenge.box1_finished_at,
        )
        .limit(nums)
    )
    ult_rows: List[FifthAnniversaryChallenge] = (
        FifthAnniversaryChallenge.query.filter(
            FifthAnniversaryChallenge.finished_at.is_not(None),
        )
        .order_by(FifthAnniversaryChallenge.finished_at.desc())
        .with_entities(
            FifthAnniversaryChallenge.user_id,
            FifthAnniversaryChallenge.finished_at,
        )
        .limit(nums)
    )
    data_list = []
    for row in ult_rows:
        data_list.append([row.user_id, row.finished_at, "ult_box"])
    for row in ann_rows:
        data_list.append([row.user_id, row.box1_finished_at, "ann_box"])

    # 完成时间倒叙，取最新N条，再按完成时间顺序
    data_list.sort(key=lambda x: x[1], reverse=True)
    data_list = data_list[:nums]
    data_list.reverse()

    user_ids = {i[0] for i in data_list}
    user_email_map = {}
    for ch_user_ids in batch_iter(user_ids, 1000):
        ch_users = (
            User.query.filter(User.id.in_(ch_user_ids))
            .with_entities(
                User.id,
                User.email,
            )
            .all()
        )
        user_email_map.update({i.id: hide_email(i.email) for i in ch_users})

    for lang in Language:
        if lang == Language.ZH_HANS_CN:
            continue
        msgs = []
        with force_locale(lang.value):
            for data in data_list:
                user_id, _, box_type = data
                email = user_email_map.get(user_id)
                if not email:
                    continue
                box_type_str = gettext("周年盲盒") if box_type == "ann_box" else gettext("终极盲盒")
                msg = gettext("获得%(box_type)s开箱机会", box_type=box_type_str)
                msgs.append([email, msg])
        if msgs:
            cache = FifthBroadcastCache("qualify", lang.value)
            cache.set(json.dumps(msgs, cls=JsonEncoder))


def update_fifth_reward_broadcast_cache(nums):

    def _sort_by_cost(_r: FifthAnniversaryBox):
        if _r.reward in [
            FifthAnniversaryBox.RewardType.USDT_2000,
            FifthAnniversaryBox.RewardType.USDT_500,
            FifthAnniversaryBox.RewardType.USDT_200,
            FifthAnniversaryBox.RewardType.USDT_50,
        ]:
            # 值最大, 优先展示
            return 3
        elif _r.reward in [FifthAnniversaryBox.RewardType.CET_200]:
            return 2
        return 1

    rows: List[FifthAnniversaryBox] = FifthAnniversaryBox.query.filter().all()
    usdt2000_rewards = []
    other_rewards = []
    for r in rows:
        if r.reward == FifthAnniversaryBox.RewardType.USDT_2000:
            usdt2000_rewards.append(r)
        else:
            other_rewards.append(r)

    if len(rows) < 4000:
        # 按抽奖时间
        sorted_rows = sorted(other_rewards, key=lambda x: x.created_at)
        sorted_rows = sorted_rows[-nums:]
    else:
        # 先按奖品类型排序，取前N条，再按抽奖时间正序
        sorted_rows = sorted(other_rewards, key=_sort_by_cost)
        sorted_rows = sorted_rows[-nums:]
        sorted_rows.sort(key=lambda x: x.created_at)

    big_idx = 0
    display_rows = []
    for idx, r in enumerate(sorted_rows, 1):
        if usdt2000_rewards and (idx == 10 or idx % 500 == 0):
            display_rows.append(usdt2000_rewards[big_idx])
            big_idx += 1
            big_idx = 0 if big_idx >= len(usdt2000_rewards) else big_idx
        display_rows.append(r)
    display_rows = display_rows[:nums]

    user_ids = {i.user_id for i in display_rows}
    user_email_map = {}
    for ch_user_ids in batch_iter(user_ids, 1000):
        ch_users = (
            User.query.filter(User.id.in_(ch_user_ids))
            .with_entities(
                User.id,
                User.email,
            )
            .all()
        )
        user_email_map.update({i.id: hide_email(i.email) for i in ch_users})

    icon_rewards = [
        FifthAnniversaryBox.RewardType.USDT_2000,
        FifthAnniversaryBox.RewardType.USDT_500,
        FifthAnniversaryBox.RewardType.USDT_200,
    ]

    for lang in Language:
        if lang == Language.ZH_HANS_CN:
            continue
        msgs = []
        with force_locale(lang.value):
            vip_reward_msg_tmp = gettext("获得VIP特权")
            coupon_reward_msg_tmp = gettext("获得随机卡券")
            asset_reward_msg_tmp = gettext("获得%(amount)s %(asset)s")
            for row in display_rows:
                email = user_email_map.get(row.user_id)
                if not email:
                    continue
                params = {}
                if row.reward in FifthBoxRewardSender.ASSET_REWARD_ASSET_AMOUNT_MAP:
                    msg = asset_reward_msg_tmp
                    asset, amount = FifthBoxRewardSender.ASSET_REWARD_ASSET_AMOUNT_MAP[row.reward]
                    params.update({"amount": amount, "asset": asset})
                elif row.reward in FifthBoxRewardSender.COUPON_REWARDS:
                    msg = coupon_reward_msg_tmp
                elif row.reward in FifthBoxRewardSender.VIP_REWARDS:
                    msg = vip_reward_msg_tmp
                else:
                    continue
                is_icon_reward = int(row.reward in icon_rewards)  # 是否展示icon
                msgs.append([email, gettext(msg, **params), is_icon_reward])
        if msgs:
            cache = FifthBroadcastCache("reward", lang.value)
            cache.set(json.dumps(msgs, cls=JsonEncoder))


def update_fifth_broadcast_cache():
    """ 更新 轮播信息缓存 """
    box_end_date = date(2023, 1, 8)
    today_ = today()
    if today_ >= box_end_date:
        # 活动结束
        return

    nums = 2000
    update_fifth_qualify_broadcast_cache(nums)
    update_fifth_reward_broadcast_cache(nums)


def fast_update_fifth_reward_broadcast_cache():
    # 从用户奖品缓存中更新轮播
    time.sleep(30)  # 开奖30s后跑
    all_user_reward_map = FifthUserBoxRewardCache().get_all_user_reward()
    user_ids = list(all_user_reward_map)
    user_email_map = {}
    for ch_user_ids in batch_iter(user_ids, 1000):
        ch_users = (
            User.query.filter(User.id.in_(ch_user_ids))
                .with_entities(
                User.id,
                User.email,
            )
            .all()
        )
        user_email_map.update({i.id: hide_email(i.email) for i in ch_users})

    asset_reward_asset_amount_map = {k.name: v for k, v in FifthBoxRewardSender.ASSET_REWARD_ASSET_AMOUNT_MAP.items()}
    coupon_rewards = [i.name for i in FifthBoxRewardSender.COUPON_REWARDS]
    vip_rewards = [i.name for i in FifthBoxRewardSender.VIP_REWARDS]
    for lang in Language:
        if lang == Language.ZH_HANS_CN:
            continue
        msgs = []
        with force_locale(lang.value):
            vip_reward_msg_tmp = gettext("获得VIP特权")
            coupon_reward_msg_tmp = gettext("获得随机卡券")
            asset_reward_msg_tmp = gettext("获得%(amount)s %(asset)s")
            for user_id, box_and_reward in all_user_reward_map.items():
                email = user_email_map.get(user_id)
                if not email:
                    continue
                params = {}
                _box_type, reward = box_and_reward
                if reward in asset_reward_asset_amount_map:
                    msg = asset_reward_msg_tmp
                    asset, amount = asset_reward_asset_amount_map[reward]
                    params.update({"amount": amount, "asset": asset})
                elif reward in coupon_rewards:
                    msg = coupon_reward_msg_tmp
                elif reward in vip_rewards:
                    msg = vip_reward_msg_tmp
                else:
                    continue
                msgs.append([email, gettext(msg, **params), 0])
        if msgs:
            cache = FifthBroadcastCache("reward", lang.value)
            cache.set(json.dumps(msgs, cls=JsonEncoder))


def persist_user_fifth_box_reward() -> int:
    """ 持久化 用户盲盒奖品信息 """
    reward_cache = FifthUserBoxRewardCache()
    all_user_reward_map = reward_cache.get_all_user_reward()

    rows = FifthAnniversaryBox.query.with_entities(FifthAnniversaryBox.user_id.distinct()).all()
    exist_user_ids = {i[0] for i in rows}
    new_user_ids = set(all_user_reward_map) - exist_user_ids
    for idx, user_id in enumerate(new_user_ids, 1):
        box_type, reward = all_user_reward_map[user_id]
        new_row = FifthAnniversaryBox(
            user_id=user_id,
            type=FifthAnniversaryBox.BoxType[box_type],
            reward=FifthAnniversaryBox.RewardType[reward],
        )
        db.session.add(new_row)
        if idx % 500 == 0:
            db.session.commit()
    db.session.commit()
    current_app.logger.warning(f"persist_user_fifth_box_reward new_persist:{len(new_user_ids)}")
    return len(new_user_ids)


def send_user_fifth_box_reward():
    """ 发放 用户盲盒的奖品 """
    from app.business import update_gift_history_task
    from app.business.coupon.pool import update_coupon_dynamic_user_task

    no_vip5_reward_num = FifthAnniversaryBox.query.filter(
        FifthAnniversaryBox.reward != FifthAnniversaryBox.RewardType.VIP5,
    ).with_entities(func.count()).scalar() or 0
    max_no_vip5_reward_num = ANNIVERSARY_BOX_NUM + ULTIMATE_BOX_NUM
    if no_vip5_reward_num > max_no_vip5_reward_num:
        # 抽奖数目异常，不执行发奖逻辑
        current_app.logger.error(f"send_user_fifth_box_reward 奖品数异常, "
                                 f"非VIP5奖品数:{no_vip5_reward_num}, "
                                 f"盲盒总数:{max_no_vip5_reward_num}")
        return -1

    rows = FifthAnniversaryBox.query.filter(
        FifthAnniversaryBox.status == FifthAnniversaryBox.Status.CREATED,
    ).all()
    has_coupon = has_gift = False
    for row in rows:
        FifthBoxRewardSender.send(row)
        if row.reward in FifthBoxRewardSender.ASSET_REWARD_ASSET_AMOUNT_MAP:
            has_gift = True
        if row.reward in FifthBoxRewardSender.COUPON_REWARDS:
            has_coupon = True

    if has_gift:
        fifth_activity_id = Activity.get_or_create_fifth_anniversary_activity_id()
        update_gift_history_task.delay(fifth_activity_id, BalanceBusiness.GIFT.value, wait=False)
    if has_coupon:
        update_coupon_dynamic_user_task.delay(CouponApply.DynamicUser.FIVE_ACTIVITY_GIFT.name)

    current_app.logger.warning(f"send_user_fifth_box_reward new_send:{len(rows)}")
    return len(rows)


def update_fifth_deal_hunter_user():
    """ 更新羊毛党用户 """
    dt = FifthBoxManager.NEW_USER_REGISTRATION_DT
    new_user = User.query.filter(
        User.created_at >= dt,
        User.id > 4473488,  # 12.19的最新注册用户id
    ).order_by(User.id.asc()).first()
    if not new_user:
        return

    new_user_id = new_user.id  # 大于此id的用户都是新用户
    to_check_users = FifthAnniversaryChallenge.query.filter(
        FifthAnniversaryChallenge.user_id >= new_user_id,
    ).with_entities(
        FifthAnniversaryChallenge.user_id,
    ).all()
    if not to_check_users:
        return

    trade_check_user_ids = [i.user_id for i in to_check_users]
    asset_prices = AssetPrice.get_close_price_map(today())
    user_trade_usd_map = defaultdict(Decimal)
    for ch_user_ids in batch_iter(trade_check_user_ids, 1000):
        trade_rows = UserTradeSummary.query.filter(
            UserTradeSummary.user_id.in_(ch_user_ids),
        ).group_by(
            UserTradeSummary.user_id,
        ).with_entities(
            UserTradeSummary.user_id,
            func.sum(UserTradeSummary.trade_amount),
        ).all()
        for r in trade_rows:
            user_trade_usd_map[r[0]] += r[1]

        ex_rows = AssetExchangeOrder.query.filter(
            AssetExchangeOrder.user_id.in_(ch_user_ids),
            AssetExchangeOrder.target_asset_exchanged_amount > 0,
        ).group_by(
            AssetExchangeOrder.user_id,
            AssetExchangeOrder.target_asset,
        ).with_entities(
            AssetExchangeOrder.user_id,
            AssetExchangeOrder.target_asset,
            func.sum(AssetExchangeOrder.target_asset_exchanged_amount),
        ).all()
        for r in ex_rows:
            user_trade_usd_map[r[0]] += r[2] * asset_prices.get(r[1], Decimal())

    balance_check_user_ids = [u for u in trade_check_user_ids if user_trade_usd_map[u] < Decimal("1500")]
    yesterday_ts = current_timestamp(to_int=True) - 86400
    yesterday_table = ExchangeLogDB.user_account_balance_sum_table(yesterday_ts)
    user_balance_usd_map = defaultdict(Decimal)
    for ch_user_ids in batch_iter(balance_check_user_ids, 1000):
        user_id_str = ','.join(map(str, ch_user_ids))
        where = f'user_id in ({user_id_str})'
        rows = yesterday_table.select("user_id", "balance_usd", where=where)
        for user_id, total_usd in rows:
            user_balance_usd_map[user_id] = Decimal(total_usd)

    result = {str(u) for u in balance_check_user_ids if user_balance_usd_map[u] < Decimal("80")}
    FifthDealHunterUserCache().save(result)


def update_fifth_risk_user():
    """ 更新风险用户 """
    trade_start_dt = date(2022, 12, 24)
    trade_end_dt = date(2023, 1, 8)
    today_ = today()
    if today_ >= trade_end_dt:
        return

    dt = now() - timedelta(hours=12)
    local_deposits = Deposit.query.filter(
        Deposit.type == Deposit.Type.LOCAL,
        Deposit.created_at >= dt,
        Deposit.status.in_(
            [
                Deposit.Status.CONFIRMING,
                Deposit.Status.TO_HOT,
                Deposit.Status.FINISHED,
            ]
        ),
    ).with_entities(
        Deposit.id,
        Deposit.user_id,
        Deposit.sender_user_id,
    ).all()
    sender_receiver_ids_map = defaultdict(set)
    pool_user_id = config['CLIENT_CONFIGS']['viabtc_pool']['user_id']
    for r in local_deposits:
        if r.sender_user_id == pool_user_id:
            continue
        sender_receiver_ids_map[r.sender_user_id].add(r.user_id)

    deposit_risk_user_ids = set()
    deposit_threshold = 10
    for receiver_ids in sender_receiver_ids_map.values():
        if len(receiver_ids) >= deposit_threshold:
            # 某个账户向10个及以上账号进行站内转账，则被转账账号无法领取盲盒
            deposit_risk_user_ids.update(receiver_ids)

    #
    user_market_deal_map = defaultdict(lambda: defaultdict(Decimal))
    trade_start_dt_str = trade_start_dt.strftime('%Y-%m-%d')
    trade_end_dt_str = trade_end_dt.strftime('%Y-%m-%d')
    table_names = [f"user_trade_summary_{i}" for i in TradeSummaryDB.get_tables_suffix(trade_start_dt, today_)]
    for table_name in table_names:
        table = TradeSummaryDB.table(table_name)
        if not table.exists():
            continue
        rows = table.select(
            *("user_id", "market", "SUM(deal_volume)"),
            where=f" trade_date >= '{trade_start_dt_str}' AND trade_date < '{trade_end_dt_str}' ",
            group_by="user_id, market",
        )
        for r in rows:
            user_market_deal_map[r[0]][r[1]] += r[2]

    trade_risk_user_ids = set()
    stable_rate_threshold = Decimal("0.9999")
    stable_markets = ["USDCUSDT", "BUSDUSDT", "USTCUSDT"]
    for user_id, market_deal_map in user_market_deal_map.items():
        total_deal = sum(market_deal_map.values())
        stable_deal = sum([v for k, v in market_deal_map.items() if k in stable_markets])
        if total_deal != 0 and stable_deal / total_deal >= stable_rate_threshold:
            trade_risk_user_ids.add(user_id)

    has_per_user_ids = set()
    trade_risk_user_ids = trade_risk_user_ids - has_per_user_ids  # 排除有合约交易的

    risk_cache = FifthRiskUserCache()
    exist_risk_user_map = risk_cache.hgetall()

    old_trade_user_ids = {int(u) for u, v in exist_risk_user_map.items() if v == "trade"}
    del_trade_user_ids = old_trade_user_ids - trade_risk_user_ids - deposit_risk_user_ids
    if del_trade_user_ids:
        risk_cache.hdel(*[str(u) for u in del_trade_user_ids])

    add_result = {}
    for user_id in deposit_risk_user_ids:
        uid_str = str(user_id)
        if uid_str not in exist_risk_user_map:
            add_result[uid_str] = "deposit"
    for user_id in trade_risk_user_ids:
        uid_str = str(user_id)
        if uid_str not in exist_risk_user_map:
            add_result[uid_str] = "trade"
    if add_result:
        risk_cache.hmset(add_result)


def new_box_qualify_message(user_id: int, box_type: FifthAnniversaryBox.BoxType):
    if box_type == FifthAnniversaryBox.BoxType.ANNIVERSARY:
        title = MessageTitle.FIFTH_ANN_BOX_QUALIFY.name
        content = MessageContent.FIFTH_ANN_BOX_QUALIFY.name
        link = "https://www.coinex.com/s/49NJ"
    else:
        title = MessageTitle.FIFTH_ULT_BOX_QUALIFY.name
        content = MessageContent.FIFTH_ULT_BOX_QUALIFY.name
        link = "https://www.coinex.com/s/49NU"
    return Message(
        user_id=user_id,
        title=title,
        content=content,
        params=json.dumps({}),
        extra_info=json.dumps(
            dict(
                web_link=link,
                android_link="",
                ios_link="",
            )
        ),
        display_type=Message.DisplayType.POPUP_WINDOW,
        channel=Message.Channel.ACTIVITY,
    )


def send_already_box_qualify_user_message():
    """ 发送盲盒开箱资格的站内信(之前完成任务用户) """
    rows: List[FifthAnniversaryChallenge] = FifthAnniversaryChallenge.query.filter(
        FifthAnniversaryChallenge.box1_finished_at.is_not(None),
    ).all()
    ann_users = set()
    ult_users = set()
    for row in rows:
        if row.check_all_task_finished:
            ult_users.add(row.user_id)
        elif row.check_box1_task_finished:
            ann_users.add(row.user_id)

    if ann_users:
        for user_id in ann_users:
            db.session.add(new_box_qualify_message(user_id, FifthAnniversaryBox.BoxType.ANNIVERSARY))
    if ult_users:
        for user_id in ult_users:
            db.session.add(new_box_qualify_message(user_id, FifthAnniversaryBox.BoxType.ULTIMATE))
    db.session.commit()
    return len(ann_users) + len(ult_users)


def send_box_qualify_user_push():
    """ 发送push """
    from app.business.push import send_mobile_push_by_user_ids

    rows: List[FifthAnniversaryChallenge] = FifthAnniversaryChallenge.query.filter(
        FifthAnniversaryChallenge.box1_finished_at.is_not(None),
    ).all()
    ann_users = set()
    ult_users = set()
    for row in rows:
        if row.check_all_task_finished:
            ult_users.add(row.user_id)
        elif row.check_box1_task_finished:
            ann_users.add(row.user_id)

    ult_title = gettext("你的终极盲盒可以开箱啦！")
    ult_content = gettext("1月1日0点(UTC)准时开箱，最高2000 USDT，数量有限，立即抢 >>")
    ult_link = "https://www.coinex.com/s/49N6"
    ann_title = gettext("你的周年盲盒可以开箱啦！")
    ann_content = gettext("1月1日0点(UTC)准时开箱，最高200 USDT，数量有限，立即抢 >>")
    ann_link = "https://www.coinex.com/s/49NK"

    all_user_ids = set(ann_users) | ult_users
    current_app.logger.warning(f"send_box_qualify_user_push total_push:{len(all_user_ids)}")
    lang_user_mapper = defaultdict(list)
    for ch_user_ids in batch_iter(all_user_ids, 5000):
        user_prefs = UserPreference.query.filter(
            UserPreference.user_id.in_(ch_user_ids),
            UserPreference.key == 'language',
        ).with_entities(
            UserPreference.user_id,
            UserPreference.value
        ).all()
        for i in user_prefs:
            lang_user_mapper[i.value].append(i.user_id)

    for lang, user_ids in lang_user_mapper.items():
        ult_user_ids = {i for i in user_ids if i in ult_users}
        if ult_user_ids:
            with force_locale(lang):
                _title_for_lang = gettext(ult_title)
                _content_for_lang = gettext(ult_content)
                send_mobile_push_by_user_ids.delay(
                    user_ids=list(ult_user_ids),
                    content=_content_for_lang,
                    title=_title_for_lang,
                    ttl=0,
                    url=ult_link,
                )
        ann_user_ids = {i for i in user_ids if i in ann_users and i not in ult_user_ids}
        if ann_user_ids:
            with force_locale(lang):
                _title_for_lang = gettext(ann_title)
                _content_for_lang = gettext(ann_content)
                send_mobile_push_by_user_ids.delay(
                    user_ids=list(ann_user_ids),
                    content=_content_for_lang,
                    title=_title_for_lang,
                    ttl=0,
                    url=ann_link,
                )


def reduce_fifth_ann_box_remain():
    """ 减少周年盲盒剩余数（增加领取进度） """
    reduce_percent = randint(1, 3)  # 每小时领取 1% 或 2% 或 3%
    reduce_num = int(ANNIVERSARY_BOX_NUM * reduce_percent / 100)

    box_type = FifthAnniversaryBox.BoxType.ANNIVERSARY
    box_remain_cache = FifthBoxRemainNumCache(box_type.name)
    with CacheLock(LockKeys.fifth_box(box_type.name), wait=True):
        box_remain = int(box_remain_cache.read())
        current_app.logger.warning(f"reduce_fifth_ann_box_remain box_remain:{box_remain}")
        if box_remain <= 0:
            return

        # 只减盲盒剩余数，不处理批次奖品剩余数
        reduce_num = min(reduce_num, box_remain)
        new_remain = box_remain_cache.incr(-reduce_num)
    current_app.logger.warning(f"reduce_fifth_ann_box_remain reduce_num:{reduce_num} new_remain:{new_remain}")


def export_challenge_user_finished_task_data():
    """ 大挑战-任务完成数据 """
    task1_num = task2_num = task3_num = task4_num = task5_num = 0
    box1_num = box2_num = 0
    rows: List[FifthAnniversaryChallenge] = FifthAnniversaryChallenge.query.all()
    for row in rows:
        if row.on_chain_deposit_usd_qualified:
            task1_num += 1
        if row.spot_deal_usd_qualified:
            task2_num += 1
        if row.has_exchange_trade:
            task3_num += 1
        if row.has_investment:
            task4_num += 1
        if row.perpetual_deal_usd_qualified:
            task5_num += 1
        if row.check_box1_task_finished:
            box1_num += 1
        if row.check_all_task_finished:
            box2_num += 1

    reward_rows = FifthAnniversaryBox.query.group_by(
        FifthAnniversaryBox.type,
    ).with_entities(
        FifthAnniversaryBox.type,
        func.count(FifthAnniversaryBox.id),
    ).all()
    box_reward_num_map = dict(reward_rows)
    anniversary_box_num = box_reward_num_map.get(FifthAnniversaryBox.BoxType.ANNIVERSARY, 0)
    ultimate_box_num = box_reward_num_map.get(FifthAnniversaryBox.BoxType.ULTIMATE, 0)

    msg = (
        f"领取大挑战门票人数: {len(rows)}, 完成任务1: {task1_num}, "
        f"完成任务2: {task2_num}, 完成任务3: {task3_num}, "
        f"完成任务4: {task4_num}, 完成任务5: {task5_num}, "
        f"达标周年盲盒人数: {box1_num}, 达标终极盲盒人数: {box2_num}, "
        f"开启盲盒A人数: {anniversary_box_num}, 开启盲盒B人数: {ultimate_box_num}"
    )
    current_app.logger.warning(f"export_challenge_user_finished_task_data {msg}")
    return msg
