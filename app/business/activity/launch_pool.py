# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decima<PERSON>
from typing import Optional

from flask import current_app
from flask_babel import gettext as _
from sqlalchemy import func

from app.common import BalanceBusiness, CeleryQueues, MessageTitle, MessageContent, MessageWebLink
from app.config import config
from app.exceptions import InvalidArgument, InsufficientBalance
from app.models import db, GiftHistory, Message
from app.models.activity import (
    LaunchMiningProject, LaunchMiningPool, LaunchMiningPoolUserInfo, LaunchMiningPoolOperateHistory,
    LaunchMiningUserStakeSnapshot,
)
from app.business import LockKeys, CacheLock, lock_call, PriceManager
from app.business.utils import query_records_by_time_range
from app.business.clients import ServerClient, ServerResponseCode
from app.caches import MarketCache
from app.caches.user import AbnormalUserCache
from app.caches.activity import LaunchProjectLastRewardCache, LaunchProjectCache
from app.utils import now, quantize_amount, amount_to_str, celery_task, batch_iter
from app.utils.date_ import convert_datetime, date_to_datetime
from app.utils.parser import JsonEncoder


class LaunchMiningOp:
    @classmethod
    def is_clear_user(cls, user_id: int) -> bool:
        """ 是否是清退用户 """
        from app.caches.user import UserVisitPermissionCache

        only_withdraw_status = UserVisitPermissionCache().get_user_permission(user_id)
        if only_withdraw_status == UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE:
            return False
        elif only_withdraw_status in (
            UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE,
            UserVisitPermissionCache.FORBIDDEN_VALUE,
        ):
            return True
        return False

    @classmethod
    def stake(cls, project: LaunchMiningProject, pool: LaunchMiningPool, user_id: int, amount: Decimal):
        """ 存入质押币，外部加锁 """
        if pool.project_id != project.id:
            raise InvalidArgument

        user_info_row: LaunchMiningPoolUserInfo = LaunchMiningPoolUserInfo.query.filter(
            LaunchMiningPoolUserInfo.pool_id == pool.id,
            LaunchMiningPoolUserInfo.user_id == user_id,
        ).first()
        if not user_info_row:
            user_info_row = LaunchMiningPoolUserInfo(
                project_id=project.id,
                pool_id=pool.id,
                user_id=user_id,
                reward_asset=project.reward_asset,
            )
            db.session_add_and_commit(user_info_row)

        stake_asset = pool.stake_asset
        if user_info_row.stake_amount + amount > pool.user_max_stake_amount:
            raise InvalidArgument(
                message=_(
                    "使用已达到每人存入上限 %(amount)s%(asset)s",
                    amount=amount_to_str(pool.user_max_stake_amount),
                    asset=stake_asset,
                )
            )

        client = ServerClient()
        balances = client.get_user_balances(user_id, stake_asset)
        available = balances[stake_asset]["available"]
        if amount > available:
            raise InsufficientBalance

        op_his = LaunchMiningPoolOperateHistory(
            project_id=project.id,
            pool_id=pool.id,
            user_id=user_id,
            type=LaunchMiningPoolOperateHistory.Type.STAKE,
            reward_asset=project.reward_asset,
            asset=stake_asset,
            amount=amount,
            status=LaunchMiningPoolOperateHistory.Status.CREATED,
            time=now(),
        )
        db.session_add_and_commit(op_his)
        cls.do_stake_by_his(pool, user_info_row, op_his)

    @classmethod
    def do_stake_by_his(
        cls,
        pool: LaunchMiningPool,
        user_info_row: LaunchMiningPoolUserInfo,
        op_his: LaunchMiningPoolOperateHistory,
    ):
        assert op_his.type == LaunchMiningPoolOperateHistory.Type.STAKE
        assert op_his.status == LaunchMiningPoolOperateHistory.Status.CREATED

        user_id = op_his.user_id
        stake_asset = op_his.asset
        amount = op_his.amount

        def _on_success():
            user_info_row.stake_amount += amount
            user_info_row.max_stake_amount = max(user_info_row.max_stake_amount, user_info_row.stake_amount)
            pool.stake_amount += amount
            op_his.status = LaunchMiningPoolOperateHistory.Status.FINISHED

        try:
            ServerClient().lock_user_balance(
                user_id=user_id,
                asset=stake_asset,
                amount=amount_to_str(amount, 8),
                business=BalanceBusiness.LAUNCH_POOL_MINING,
                business_id=op_his.id,
            )
            _on_success()
            db.session.commit()
        except Exception as e:
            current_app.logger.error(
                f"LaunchMiningPoolOperation_stake {op_his.id} {user_id} {op_his.type.name} "
                f"{op_his.asset} {op_his.amount} failed {e!r}"
            )
            if getattr(e, 'code', None) == ServerResponseCode.DUPLICATE_BALANCE_UPDATE:
                _on_success()
                db.session.commit()
            elif getattr(e, 'code', None) == ServerResponseCode.INSUFFICIENT_BALANCE:
                op_his.status = LaunchMiningPoolOperateHistory.Status.FAILED
                db.session.commit()
            else:
                raise e

    @classmethod
    def retrieve(cls, project: LaunchMiningProject, pool: LaunchMiningPool, user_id: int,
                 amount: Decimal, is_manual: bool = False):
        """ 取出质押币 """
        if pool.project_id != project.id:
            raise InvalidArgument

        user_info_row: LaunchMiningPoolUserInfo = LaunchMiningPoolUserInfo.query.filter(
            LaunchMiningPoolUserInfo.pool_id == pool.id,
            LaunchMiningPoolUserInfo.user_id == user_id,
        ).first()
        if not user_info_row:
            raise InvalidArgument
        if amount > user_info_row.stake_amount or amount <= Decimal():
            raise InvalidArgument
        if (pool.reward_mode == LaunchMiningPool.RewardMode.AFTER_EVENT
                and amount != user_info_row.stake_amount):
            # AFTER_EVENT只能一次性全取出
            raise InvalidArgument(message='invalid stake_amount')

        stake_asset = pool.stake_asset
        op_his = LaunchMiningPoolOperateHistory(
            project_id=project.id,
            pool_id=pool.id,
            user_id=user_id,
            type=LaunchMiningPoolOperateHistory.Type.RETRIEVE,
            reward_asset=project.reward_asset,
            asset=stake_asset,
            amount=amount,
            status=LaunchMiningPoolOperateHistory.Status.CREATED,
            time=now(),
        )
        if is_manual and pool.reward_mode == LaunchMiningPool.RewardMode.AFTER_EVENT:
            LaunchMiningPoolOperateHistory.query.filter(
                LaunchMiningPoolOperateHistory.pool_id == pool.id,
                LaunchMiningPoolOperateHistory.user_id == user_id,
                LaunchMiningPoolOperateHistory.type == LaunchMiningPoolOperateHistory.Type.REWARD_SETTLEMENT,
                LaunchMiningPoolOperateHistory.status == LaunchMiningPoolOperateHistory.Status.FINISHED,
            ).update({"status": LaunchMiningPoolOperateHistory.Status.FAILED},
                     synchronize_session=False)
            user_info_row.total_reward_amount = 0
        db.session_add_and_commit(op_his)
        cls.do_retrieve_by_his(pool, user_info_row, op_his)

    @classmethod
    def do_retrieve_by_his(
        cls,
        pool: LaunchMiningPool,
        user_info_row: LaunchMiningPoolUserInfo,
        op_his: LaunchMiningPoolOperateHistory,
    ):
        assert op_his.type == LaunchMiningPoolOperateHistory.Type.RETRIEVE
        assert op_his.status == LaunchMiningPoolOperateHistory.Status.CREATED

        user_id = op_his.user_id
        stake_asset = op_his.asset
        amount = op_his.amount

        def _on_success():
            user_info_row.stake_amount -= amount
            pool.stake_amount -= amount
            op_his.status = LaunchMiningPoolOperateHistory.Status.FINISHED
            op_his.time = now()

        client = ServerClient(current_app.logger)
        try:
            client.unlock_user_balance(
                user_id=user_id,
                asset=stake_asset,
                amount=amount_to_str(amount, 8),
                business=BalanceBusiness.LAUNCH_POOL_MINING,
                business_id=op_his.id,
            )
            _on_success()
            db.session.commit()
        except Exception as e:
            current_app.logger.error(
                f"LaunchMiningPoolOperation_retrieve {op_his.id} {user_id} {op_his.type.name} "
                f"{op_his.asset} {op_his.amount} failed {e!r}"
            )
            if getattr(e, 'code', None) == ServerResponseCode.DUPLICATE_BALANCE_UPDATE:
                _on_success()
                db.session.commit()
            elif getattr(e, 'code', None) == ServerResponseCode.INSUFFICIENT_BALANCE:
                op_his.status = LaunchMiningPoolOperateHistory.Status.FAILED
                db.session.commit()
            else:
                raise e


class LaunchMiningRewardHelper:

    @classmethod
    def get_online_projects(cls, delay_hours: int = 0) -> list[LaunchMiningProject]:
        """ 活动中&未发奖 """
        now_ = now()
        end_ = now_ - timedelta(hours=delay_hours)  # 4:30活动结束，5点整还要结算一次奖励
        projects: list[LaunchMiningProject] = LaunchMiningProject.query.filter(
            LaunchMiningProject.status == LaunchMiningProject.Status.ONLINE,
            LaunchMiningProject.reward_status == LaunchMiningProject.RewardStatus.CREATED,
            LaunchMiningProject.start_time <= now_,
            LaunchMiningProject.end_time >= end_,
        ).all()
        return projects

    @classmethod
    def get_need_send_reward_projects(cls) -> list[LaunchMiningProject]:
        """ 需要发奖的活动：活动结束&未发奖 """
        now_ = now()
        projects: list[LaunchMiningProject] = LaunchMiningProject.query.filter(
            LaunchMiningProject.status == LaunchMiningProject.Status.ONLINE,
            LaunchMiningProject.reward_status != LaunchMiningProject.RewardStatus.FINISHED,
            LaunchMiningProject.end_time <= now_,
        ).all()
        return projects

    @classmethod
    def group_project_rewards_by_pool_user(
        cls,
        pcj: LaunchMiningProject,
        check_amount: bool = True,
    ) -> tuple[dict[int, dict[int, Decimal]], dict[int, LaunchMiningPool]]:
        settle_reward_rows = LaunchMiningPoolOperateHistory.query.filter(
            LaunchMiningPoolOperateHistory.project_id == pcj.id,
            LaunchMiningPoolOperateHistory.type == LaunchMiningPoolOperateHistory.Type.REWARD_SETTLEMENT,
        ).with_entities(
            LaunchMiningPoolOperateHistory.pool_id,
            LaunchMiningPoolOperateHistory.user_id,
            LaunchMiningPoolOperateHistory.amount,
            LaunchMiningPoolOperateHistory.status,
        ).all()
        pool_user_reward_map = defaultdict(lambda: defaultdict(Decimal))
        for r in settle_reward_rows:
            reward_amount = r.amount if r.status == LaunchMiningPoolOperateHistory.Status.FINISHED else 0
            pool_user_reward_map[r.pool_id][r.user_id] += reward_amount

        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj.id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        pool_map = {i.id: i for i in pools}

        if check_amount:
            pcj_total_reward = Decimal()
            for pool_id, user_reward_map in pool_user_reward_map.items():
                pool_total_reward = sum(user_reward_map.values())
                pool = pool_map[pool_id]
                if pool_total_reward > pool.reward_total_amount:
                    current_app.logger.warning(
                        f"LaunchMiningPoolOperation_send_project_reward pcj:{pcj.id} pool:{pool.id} "
                        f"pool_total_reward error {pool_total_reward} {pool.reward_total_amount}"
                    )
                    raise ValueError("pool_total_reward error")
                pcj_total_reward += pool_total_reward
            if pcj_total_reward > pcj.reward_total_amount:
                current_app.logger.warning(
                    f"LaunchMiningPoolOperation_send_project_reward pcj:{pcj.id} "
                    f"pcj_total_reward error {pcj_total_reward} {pcj.reward_total_amount}"
                )
                raise ValueError("pcj_total_reward error")
        return pool_user_reward_map, pool_map

    @classmethod
    def save_user_stake_snapshots(cls, snapshot_time: Optional[datetime] = None):
        """ 保存用户质押的快照 """
        projects = cls.get_online_projects()
        project_ids = [i.id for i in projects if i.can_gen_stake_snapshot]
        if not project_ids:
            return

        snapshot_at = snapshot_time or convert_datetime(now(), 'minute')
        if LaunchMiningUserStakeSnapshot.query.filter(
            LaunchMiningUserStakeSnapshot.snapshot_at == snapshot_at,
        ).first():
            current_app.logger.error(f"LaunchMiningPoolOperation_save_user_stake_snapshots {snapshot_at} existed")
            return

        user_info_rows = LaunchMiningPoolUserInfo.query.filter(
            LaunchMiningPoolUserInfo.project_id.in_(project_ids),
            LaunchMiningPoolUserInfo.stake_amount > 0,
        ).with_entities(
            LaunchMiningPoolUserInfo.pool_id,
            LaunchMiningPoolUserInfo.user_id,
            LaunchMiningPoolUserInfo.stake_amount,
        ).all()
        snapshot_rows = []
        for info in user_info_rows:
            r = LaunchMiningUserStakeSnapshot(
                pool_id=info.pool_id,
                user_id=info.user_id,
                snapshot_at=snapshot_at,
                stake_amount=info.stake_amount,
            )
            snapshot_rows.append(r)
        db.session.bulk_save_objects(snapshot_rows)
        db.session.commit()
        current_app.logger.warning(
            f"LaunchMiningPoolOperation_save_user_stake_snapshots "
            f"{snapshot_at} save {len(snapshot_rows)} rows"
        )

    @classmethod
    def save_hourly_reward_settlement(cls, reward_hour: datetime, projects: list[LaunchMiningProject] = None):
        """ 保存每小时奖励（当前小时，结算前一个小时的奖励，time存当前小时）"""
        projects = projects or cls.get_online_projects(delay_hours=1)
        if not projects:
            return

        for pcj in projects:
            if pcj.last_reward_settle_at:
                start_reward_hour = convert_datetime(pcj.last_reward_settle_at, "hour") + timedelta(hours=1)
            else:
                # 首次奖励结算时间：活动开始后的第一个整点
                start_reward_hour = pcj.first_reward_settle_hour

            end_reward_hour = min(reward_hour, pcj.last_reward_settle_hour)
            current_app.logger.warning(
                f"LaunchMiningPoolOperation_save_hourly_reward_settlement pcj:{pcj.id} "
                f"{start_reward_hour} -> {end_reward_hour} start"
            )
            while start_reward_hour <= end_reward_hour:
                cls.save_pool_hourly_reward_settlement(pcj, start_reward_hour)
                start_reward_hour += timedelta(hours=1)

    @classmethod
    def save_pool_hourly_reward_settlement(cls, pcj: LaunchMiningProject, reward_hour: datetime):
        if LaunchMiningPoolOperateHistory.query.filter(
            LaunchMiningPoolOperateHistory.project_id == pcj.id,
            LaunchMiningPoolOperateHistory.type == LaunchMiningPoolOperateHistory.Type.REWARD_SETTLEMENT,
            LaunchMiningPoolOperateHistory.time == reward_hour,
        ).first():
            current_app.logger.warning(
                f"LaunchMiningPoolOperation_save_pool_hourly_reward_settlement pcj:{pcj.id} "
                f"reward_hour {reward_hour} existed"
            )
            return

        pool_user_reward_amount_map = cls.calc_project_pool_hourly_rewards(pcj, reward_hour)
        pool_reward_rows_map = {}
        for pool_id, user_reward_map in pool_user_reward_amount_map.items():
            pool_reward_rows = []
            for user_id, reward_amount in user_reward_map.items():
                if reward_amount <= Decimal():
                    continue
                reward_settle_his = LaunchMiningPoolOperateHistory(
                    project_id=pcj.id,
                    pool_id=pool_id,
                    user_id=user_id,
                    type=LaunchMiningPoolOperateHistory.Type.REWARD_SETTLEMENT,
                    reward_asset=pcj.reward_asset,
                    asset=pcj.reward_asset,
                    amount=reward_amount,
                    status=LaunchMiningPoolOperateHistory.Status.FINISHED,
                    time=reward_hour,
                )
                pool_reward_rows.append(reward_settle_his)
            pool_reward_rows_map[pool_id] = pool_reward_rows
            current_app.logger.warning(
                f"LaunchMiningPoolOperation_save_pool_hourly_reward_settlement pcj:{pcj.id} pool:{pool_id} "
                f"{reward_hour} save {len(pool_reward_rows)} rows"
            )

        for _rows in pool_reward_rows_map.values():
            db.session.bulk_save_objects(_rows)
        pcj.last_reward_settle_at = reward_hour
        db.session.commit()

        return pool_user_reward_amount_map

    @classmethod
    def calc_project_pool_hourly_rewards(cls, pcj: LaunchMiningProject, reward_hour: datetime):
        zero = Decimal(0)
        end_hour = reward_hour
        start_hour = end_hour - timedelta(hours=1)
        pool_user_reward_amount_map = defaultdict(dict)
        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj.id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        for pool in pools:
            snap_rows = LaunchMiningUserStakeSnapshot.query.filter(
                LaunchMiningUserStakeSnapshot.pool_id == pool.id,
                LaunchMiningUserStakeSnapshot.snapshot_at > start_hour,
                LaunchMiningUserStakeSnapshot.snapshot_at <= end_hour,
            ).all()
            user_ids = {i.user_id for i in snap_rows}
            retrieve_time_map = cls.get_user_retrieve_time_map(pool, user_ids, start_hour, end_hour)
            snapshot_ats = set()
            pool_sum_stake_amount = Decimal()
            user_sum_stake_amount_map = defaultdict(Decimal)
            for r in snap_rows:
                pool_sum_stake_amount += r.stake_amount
                snapshot_ats.add(r.snapshot_at)
                if (pool.reward_mode == LaunchMiningPool.RewardMode.AFTER_EVENT and
                        r.user_id in retrieve_time_map and
                        retrieve_time_map[r.user_id] >= r.snapshot_at):
                    # 用户最近一次取出时间之前的快照忽略统计
                    continue
                user_sum_stake_amount_map[r.user_id] += r.stake_amount
            if not snapshot_ats:
                # 该小时无快照，无法发奖
                current_app.logger.error(
                    f"LaunchMiningPoolOperation_save_pool_hourly_reward_settlement pcj:{pcj.id} pool:{pool.id} "
                    f"{start_hour} ~ {end_hour} snapshot_ats empty"
                )
                continue

            snapshot_count = len(snapshot_ats)
            avg_total_stake_amount = quantize_amount(pool_sum_stake_amount / snapshot_count, 8)
            if not avg_total_stake_amount:
                current_app.logger.error(
                    f"LaunchMiningPoolOperation_save_pool_hourly_reward_settlement pcj:{pcj.id} pool:{pool.id} "
                    f"{start_hour} ~ {end_hour} avg_total_stake_amount eq_zero"
                )
                continue

            user_avg_stake_amount_map = {
                user_id: quantize_amount(amount / snapshot_count, 8)
                for user_id, amount in user_sum_stake_amount_map.items()
            }

            user_reward_amount_map = pool_user_reward_amount_map[pool.id]
            hourly_total_reward_amount = pool.reward_total_amount / len(pcj.reward_settle_hour_points)
            for user_id, avg_stake_amount in user_avg_stake_amount_map.items():
                rate = quantize_amount(avg_stake_amount / avg_total_stake_amount, 8)
                reward = rate * hourly_total_reward_amount
                real_reward = quantize_amount(reward, 8)
                if real_reward == zero and reward != zero:
                    current_app.logger.error(
                        f"LaunchMiningPoolOperation_save_pool_hourly_reward_settlement pcj:{pcj.id} pool:{pool.id} "
                        f"{start_hour} ~ {end_hour} {user_id} reward {reward} real_reward_eq_zero {real_reward}"
                    )
                user_reward_amount_map[user_id] = real_reward
            if sum(user_reward_amount_map.values()) > hourly_total_reward_amount:
                raise ValueError(f"LaunchMiningPoolOperation_save_pool_hourly_reward_settlement reward_amount error")
        return pool_user_reward_amount_map

    @classmethod
    def get_user_retrieve_time_map(cls, pool: LaunchMiningPool, user_ids: set[int],
                              start_hour: datetime, end_hour: datetime):
        time_map = dict()
        if pool.reward_mode == LaunchMiningPool.RewardMode.AFTER_EVENT:
            for batch_ids in batch_iter(user_ids, 2000):
                his_query = LaunchMiningPoolOperateHistory.query.filter(
                    LaunchMiningPoolOperateHistory.user_id.in_(batch_ids),
                    LaunchMiningPoolOperateHistory.type == LaunchMiningPoolOperateHistory.Type.RETRIEVE,
                    LaunchMiningPoolOperateHistory.pool_id == pool.id,
                    LaunchMiningPoolOperateHistory.time > start_hour,
                    LaunchMiningPoolOperateHistory.time <= end_hour,
                ).with_entities(
                    LaunchMiningPoolOperateHistory.time,
                    LaunchMiningPoolOperateHistory.user_id,
                ).order_by(
                    LaunchMiningPoolOperateHistory.id.desc()
                ).all()
                for row in his_query:
                    if row.user_id not in time_map:
                        time_map[row.user_id] = row.time
                    elif time_map[row.user_id] < row.time:
                        time_map[row.user_id] = row.time
        return time_map

    @classmethod
    def update_user_reward_info(cls):
        projects = cls.get_online_projects(delay_hours=1)
        for pcj in projects:
            try:
                cls.update_project_user_reward_info(pcj)
            except Exception as e:
                current_app.logger.error(
                    f"LaunchMiningPoolOperation_update_project_user_reward_info pcj:{pcj.id} error {e!r}"
                )

    @classmethod
    def update_project_user_reward_info(cls, pcj: LaunchMiningProject):
        pool_user_reward_map, pool_map = cls.group_project_rewards_by_pool_user(pcj)
        user_info_rows = LaunchMiningPoolUserInfo.query.filter(
            LaunchMiningPoolUserInfo.project_id == pcj.id,
        ).all()
        pool_user_info_map = defaultdict(dict)
        for r in user_info_rows:
            pool_user_info_map[r.pool_id][r.user_id] = r

        for pool_id, user_reward_map in pool_user_reward_map.items():
            sum_reward_amount = sum(user_reward_map.values())
            pool = pool_map[pool_id]
            pool.reward_distribute_amount = sum_reward_amount
            for user_id, user_reward_amount in user_reward_map.items():
                user_info_r = pool_user_info_map[pool_id].get(user_id)
                if user_info_r:
                    user_info_r.total_reward_amount = user_reward_amount
        db.session.commit()

    @classmethod
    def send_finished_project_rewards(cls):
        """ 项目结束时发奖励 """
        projects = cls.get_need_send_reward_projects()
        if not projects:
            return

        try:
            AbnormalUserCache().reload()  # 更新最新的羊毛党名单
        except:  # noqa
            pass

        this_hour = convert_datetime(now(), 'hour')
        cls.save_hourly_reward_settlement(this_hour, projects)  # 发奖前，再结算一次每小时奖励
        for pcj in projects:
            try:
                update_pcj_pool_locked_stake_amount.delay(pcj.id)
                cls.send_one_project_reward(pcj)
                cls.update_project_user_reward_info(pcj)
                cls.add_reward_send_success_message(pcj)
            except Exception as e:
                current_app.logger.error(f"send_one_project_reward pcj:{pcj.id} error {e!r}")
                db.session.rollback()

    @classmethod
    def send_one_project_reward(cls, pcj: LaunchMiningProject):
        from app.business.gift import update_gift_history_task

        if not LaunchMiningPoolOperateHistory.query.filter(
            LaunchMiningPoolOperateHistory.project_id == pcj.id,
            LaunchMiningPoolOperateHistory.type == LaunchMiningPoolOperateHistory.Type.REWARD_SEND,
        ).first():
            # 发奖前再检查一次结算记录
            cls.check_and_update_reward_settlement_his(pcj)
            cls.generate_one_project_reward_send_his(pcj)

        # 同步发奖
        pending_gift_record = GiftHistory.query.filter(
            GiftHistory.activity_id == pcj.activity_id,
            GiftHistory.asset == pcj.reward_asset,
            GiftHistory.status == GiftHistory.Status.CREATED,
        ).first()
        if pending_gift_record:
            update_gift_history_task(
                activity_id=pcj.activity_id,
                business=BalanceBusiness.LAUNCH_POOL_MINING.value,
                pay_from_admin_user_id=config["LAUNCH_POOL_ADMIN_USER_ID"],
            )
        project_pools_retrieve_all_stake(pcj.id)
        pcj.reward_status = LaunchMiningProject.RewardStatus.FINISHED
        clear_finished_project_data(pcj.id)
        db.session.commit()

    @classmethod
    def check_and_update_reward_settlement_his(cls, pcj: LaunchMiningProject):
        if pcj.reward_status != LaunchMiningProject.RewardStatus.CREATED:
            return
        pools = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj.id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        for pool in pools:
            if pool.reward_mode != LaunchMiningPool.RewardMode.AFTER_EVENT:
                continue
            user_ids = [i.user_id for i in LaunchMiningPoolUserInfo.query.filter(
                LaunchMiningPoolUserInfo.project_id == pcj.id,
                LaunchMiningPoolUserInfo.pool_id == pool.id,
            ).with_entities(
                LaunchMiningPoolUserInfo.user_id
            ).all()]
            retrieve_time_map = cls.get_user_retrieve_time_map(
                pool, user_ids, pcj.first_reward_settle_hour, pcj.last_reward_settle_hour
            )
            for batch_ids in batch_iter(retrieve_time_map.keys(), 500):
                for user_id in batch_ids:
                    LaunchMiningPoolOperateHistory.query.filter(
                        LaunchMiningPoolOperateHistory.pool_id == pool.id,
                        LaunchMiningPoolOperateHistory.user_id == user_id,
                        LaunchMiningPoolOperateHistory.type == LaunchMiningPoolOperateHistory.Type.REWARD_SETTLEMENT,
                        LaunchMiningPoolOperateHistory.status == LaunchMiningPoolOperateHistory.Status.FINISHED,
                        LaunchMiningPoolOperateHistory.time < retrieve_time_map[user_id],
                    ).update({"status": LaunchMiningPoolOperateHistory.Status.FAILED},
                             synchronize_session=False)
                db.session.commit()


    @classmethod
    def generate_one_project_reward_send_his(cls, pcj: LaunchMiningProject):
        pool_user_reward_map, pool_map = cls.group_project_rewards_by_pool_user(pcj)

        reward_asset = pcj.reward_asset
        total_reward_amount = sum([sum(i.values()) for i in pool_user_reward_map.values()])
        sys_user_id = config["LAUNCH_POOL_ADMIN_USER_ID"]
        balance = ServerClient().get_user_balances(sys_user_id, asset=reward_asset)
        available = balance[reward_asset]['available']
        if available < total_reward_amount:
            raise ValueError(f"LaunchMiningPoolOperation_generate_project_reward_send_his "
                             f"sys_user_id {sys_user_id} balance_not_enough "
                             f"total_reward_amount: {total_reward_amount} available: {available}")

        now_ = now()
        reward_rows = []
        gift_rows = []
        reward_block_user_ids = set()
        abnormal_users = AbnormalUserCache().get_users()
        for pool_id, user_reward_map in pool_user_reward_map.items():
            for user_id, reward_amount in user_reward_map.items():
                if user_id in abnormal_users:
                    reward_block_user_ids.add(user_id)
                    current_app.logger.warning(
                        f"LaunchMiningPoolOperation_generate_project_reward_send_his "
                        f"pcj:{pcj.id} find abnormal_user: {user_id}"
                    )
                    continue
                reward_send_his = LaunchMiningPoolOperateHistory(
                    project_id=pcj.id,
                    pool_id=pool_id,
                    user_id=user_id,
                    type=LaunchMiningPoolOperateHistory.Type.REWARD_SEND,
                    reward_asset=reward_asset,
                    asset=reward_asset,
                    amount=reward_amount,
                    status=LaunchMiningPoolOperateHistory.Status.FINISHED,
                    time=now_,
                )
                reward_rows.append(reward_send_his)
                if reward_amount == 0:
                    # 不记录资产流水，仅作为参与记录保留
                    continue
                gift_record = GiftHistory(
                    user_id=user_id,
                    activity_id=pcj.activity_id,
                    asset=reward_asset,
                    amount=reward_amount,
                    lock_time=0,
                    status=GiftHistory.Status.CREATED,
                    remark=f'gift for launch pcj {pcj.id} pool {pool_id}',
                )
                gift_rows.append(gift_record)
        if reward_rows:
            db.session.bulk_save_objects(reward_rows)
            db.session.bulk_save_objects(gift_rows)
        LaunchMiningPoolUserInfo.query.filter(
            LaunchMiningPoolUserInfo.project_id == pcj.id,
            LaunchMiningPoolUserInfo.reward_status == LaunchMiningPoolUserInfo.RewardStatus.CREATED,
        ).update(
            {LaunchMiningPoolUserInfo.reward_status: LaunchMiningPoolUserInfo.RewardStatus.FINISHED},
            synchronize_session=False,
        )
        if reward_block_user_ids:
            LaunchMiningPoolUserInfo.query.filter(
                LaunchMiningPoolUserInfo.project_id == pcj.id,
                LaunchMiningPoolUserInfo.user_id.in_(reward_block_user_ids),
            ).update(
                {LaunchMiningPoolUserInfo.reward_status: LaunchMiningPoolUserInfo.RewardStatus.BLOCKED},
                synchronize_session=False,
            )
        pcj.reward_status = LaunchMiningProject.RewardStatus.SENDING
        db.session.commit()
        current_app.logger.warning(
            f"LaunchMiningPoolOperation_generate_project_reward_send_his pcj:{pcj.id} save {len(reward_rows)} rows"
        )

    @classmethod
    def add_reward_send_success_message(cls, pcj: LaunchMiningProject):
        pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
            LaunchMiningPool.project_id == pcj.id,
            LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
        ).all()
        pool_map = {i.id: i for i in pools}

        user_info_rows = LaunchMiningPoolUserInfo.query.filter(
            LaunchMiningPoolUserInfo.project_id == pcj.id,
            LaunchMiningPoolUserInfo.reward_status == LaunchMiningPoolUserInfo.RewardStatus.FINISHED,
            LaunchMiningPoolUserInfo.total_reward_amount > 0,
        ).order_by(
            LaunchMiningPoolUserInfo.id.asc()
        ).with_entities(
            LaunchMiningPoolUserInfo.user_id,
            LaunchMiningPoolUserInfo.pool_id,
            LaunchMiningPoolUserInfo.total_reward_amount,
        ).all()
        user_rows_map: dict[int, list[LaunchMiningPoolUserInfo]] = defaultdict(list)
        for row in user_info_rows:
            user_rows_map[row.user_id].append(row)

        message_popup_expired_at = now() + timedelta(days=3)
        for user_id, rows in user_rows_map.items():
            reward_amount = Decimal()
            stake_assets = []
            for row in rows:
                reward_amount += row.total_reward_amount
                pool: LaunchMiningPool = pool_map[row.pool_id]
                if pool.stake_asset not in stake_assets:
                    stake_assets.append(pool.stake_asset)
            db.session.add(
                Message(
                    user_id=user_id,
                    title=MessageTitle.LAUNCH_MINING_REWARD,
                    content=MessageContent.LAUNCH_MINING_REWARD,
                    params=json.dumps({
                        'stake_assets': "、".join(stake_assets),
                        'reward_asset': pcj.reward_asset,
                        'reward_amount': amount_to_str(reward_amount),
                    }),
                    extra_info=json.dumps(
                        dict(
                            web_link=MessageWebLink.SPOT_ASSET_PAGE.value,
                            android_link="",
                            ios_link="",
                        )
                    ),
                    display_type=Message.DisplayType.POPUP_WINDOW,
                    expired_at=message_popup_expired_at,
                    channel=Message.Channel.ACTIVITY,
                )
            )
        db.session.commit()


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def project_pools_retrieve_all_stake(project_id: int):
    """ 关闭活动、活动结束时：已质押资产自动解锁 """
    pcj: LaunchMiningProject = LaunchMiningProject.query.get(project_id)
    pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
        LaunchMiningPool.project_id == project_id,
        LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
    ).all()
    for pool in pools:
        with CacheLock(key=LockKeys.launch_pool(pool.id), wait=False):
            db.session.rollback()
            user_info_rows = LaunchMiningPoolUserInfo.query.filter(
                LaunchMiningPoolUserInfo.project_id == project_id,
                LaunchMiningPoolUserInfo.pool_id == pool.id,
                LaunchMiningPoolUserInfo.stake_amount > 0,
            ).with_entities(
                LaunchMiningPoolUserInfo.user_id,
                LaunchMiningPoolUserInfo.stake_amount,
            ).all()
            for r in user_info_rows:
                try:
                    LaunchMiningOp.retrieve(pcj, pool, r.user_id, r.stake_amount)
                except Exception as e:
                    current_app.logger.error(
                        f"project_pools_retrieve_all_stake pcj:{pcj.id} pool:{pool.id} user:{r.user_id} error {e!r}"
                    )


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def update_pcj_pool_locked_stake_amount(project_id: int):
    # 活动结束或关闭后，更新池子的总存入数量
    pcj: LaunchMiningProject = LaunchMiningProject.query.get(project_id)
    pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
        LaunchMiningPool.project_id == project_id,
        LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
    ).all()
    end_time = pcj.end_time
    start_time = date_to_datetime(end_time.date())
    if start_time == end_time:
        start_time = start_time - timedelta(days=1)
    pool_ids = [i.id for i in pools]
    pool_max_stake_amount_map = get_pools_time_range_max_stake_amount_map(pool_ids, start_time, end_time)
    for pool in pools:
        pool.locked_stake_amount = pool_max_stake_amount_map.get(pool.id, 0)
    db.session.commit()
    LaunchProjectCache.reload()


def get_pools_time_range_max_stake_amount_map(
    pool_ids: list[int],
    start_time: datetime,
    end_time: datetime,
) -> dict[int, Decimal]:
    """ 获取时间范围内池子的最大质押数 """
    rows = LaunchMiningUserStakeSnapshot.query.filter(
        LaunchMiningUserStakeSnapshot.pool_id.in_(pool_ids),
        LaunchMiningUserStakeSnapshot.snapshot_at >= start_time,
        LaunchMiningUserStakeSnapshot.snapshot_at <= end_time,
    ).group_by(
        LaunchMiningUserStakeSnapshot.pool_id,
        LaunchMiningUserStakeSnapshot.snapshot_at,
    ).with_entities(
        LaunchMiningUserStakeSnapshot.pool_id,
        LaunchMiningUserStakeSnapshot.snapshot_at,
        func.sum(LaunchMiningUserStakeSnapshot.stake_amount).label("stake_amount"),
    ).all()
    pool_amount0_map = defaultdict(list)  # 整点，优先取整点的数目
    pool_amount1_map = defaultdict(list)
    for r in rows:
        if r.snapshot_at.minute == 0:
            pool_amount0_map[r.pool_id].append(r.stake_amount)
        else:
            pool_amount1_map[r.pool_id].append(r.stake_amount)

    result = {}
    for pool_id in pool_ids:
        if amount0s := pool_amount0_map[pool_id]:
            result[pool_id] = max(amount0s)
        elif amount1s := pool_amount1_map[pool_id]:
            result[pool_id] = max(amount1s)
        else:
            result[pool_id] = Decimal()
    return result


def clear_finished_project_data(project_id: int):
    """ 活动结束后清理一些无用的数据 """
    LaunchProjectLastRewardCache(project_id).delete()


def retry_created_launch_pool_operate_his():
    _now = now()
    end_time = _now - timedelta(minutes=2)
    start_time = _now - timedelta(hours=12)

    pending_rows = query_records_by_time_range(
        LaunchMiningPoolOperateHistory,
        start_time,
        end_time,
    )
    for op_his in pending_rows:
        if op_his.status != LaunchMiningPoolOperateHistory.Status.CREATED:
            continue
        try:
            retry_one_created_launch_pool_op_his(op_his)
        except Exception as e:
            current_app.logger.error(
                f"retry_one_created_launch_pool_op_his "
                f"user:{op_his.user_id} {op_his.type.name} {op_his.id} error {e!r}"
            )


def retry_one_created_launch_pool_op_his(op_his: LaunchMiningPoolOperateHistory):
    if op_his.type == LaunchMiningPoolOperateHistory.Type.STAKE:
        pool: LaunchMiningPool = LaunchMiningPool.query.get(op_his.pool_id)
        with CacheLock(key=LockKeys.launch_pool(pool.id), wait=False):
            db.session.rollback()
            pcj: LaunchMiningProject = LaunchMiningProject.query.get(op_his.project_id)
            if not pcj.can_staking:
                return
            user_info_row: LaunchMiningPoolUserInfo = LaunchMiningPoolUserInfo.query.filter(
                LaunchMiningPoolUserInfo.pool_id == pool.id,
                LaunchMiningPoolUserInfo.user_id == op_his.user_id,
            ).first()
            if not user_info_row:
                return
            LaunchMiningOp.do_stake_by_his(pool, user_info_row, op_his)
            current_app.logger.warning(
                f"retry_one_created_launch_pool_op_his pcj:{pcj.id} pool:{pool.id} "
                f"user:{op_his.user_id} STAKE op_his:{op_his.id} success"
            )
    elif op_his.type == LaunchMiningPoolOperateHistory.Type.RETRIEVE:
        pool: LaunchMiningPool = LaunchMiningPool.query.get(op_his.pool_id)
        with CacheLock(key=LockKeys.launch_pool(pool.id), wait=False):
            db.session.rollback()
            pcj: LaunchMiningProject = LaunchMiningProject.query.get(op_his.project_id)
            user_info_row: LaunchMiningPoolUserInfo = LaunchMiningPoolUserInfo.query.filter(
                LaunchMiningPoolUserInfo.pool_id == pool.id,
                LaunchMiningPoolUserInfo.user_id == op_his.user_id,
            ).first()
            if not user_info_row:
                return
            LaunchMiningOp.do_retrieve_by_his(pool, user_info_row, op_his)
            current_app.logger.warning(
                f"retry_one_created_launch_pool_op_his pcj:{pcj.id} pool:{pool.id} "
                f"user:{op_his.user_id} RETRIEVE op_his:{op_his.id} success"
            )


def update_projects_statistics_info():
    """ 更新项目的统计信息 """
    now_ = now()
    projects: list[LaunchMiningProject] = LaunchMiningProject.query.filter(
        LaunchMiningProject.status == LaunchMiningProject.Status.ONLINE,
        LaunchMiningProject.end_time >= now_ - timedelta(hours=1),
    ).with_entities(
        LaunchMiningProject.id,
    ).all()
    for pcj in projects:
        try:
            update_pcj_pool_statistics_info(pcj.id)
        except Exception as e:
            current_app.logger.error(f"update_pcj_pool_statistics_info pcj {pcj.id} error {e!r}")


def update_pcj_pool_statistics_info(project_id: int):
    """ 更新池子的统计信息 """
    pools: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
        LaunchMiningPool.project_id == project_id,
        LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
    ).all()
    pool_ids = [i.id for i in pools]

    snap_rows = LaunchMiningUserStakeSnapshot.query.filter(
        LaunchMiningUserStakeSnapshot.pool_id.in_(pool_ids),
    ).group_by(
        LaunchMiningUserStakeSnapshot.pool_id,
        LaunchMiningUserStakeSnapshot.snapshot_at,
    ).with_entities(
        LaunchMiningUserStakeSnapshot.pool_id,
        LaunchMiningUserStakeSnapshot.snapshot_at,
        func.sum(LaunchMiningUserStakeSnapshot.stake_amount).label('stake_amount'),
    ).all()
    pool_max_stake_amount_map = defaultdict(Decimal)
    for r in snap_rows:
        pool_max_stake_amount_map[r.pool_id] = max(pool_max_stake_amount_map[r.pool_id], r.stake_amount)

    pool_user_count_rows = LaunchMiningPoolUserInfo.query.filter(
        LaunchMiningPoolUserInfo.pool_id.in_(pool_ids),
    ).group_by(
        LaunchMiningPoolUserInfo.pool_id,
    ).with_entities(
        LaunchMiningPoolUserInfo.pool_id,
        func.count(LaunchMiningPoolUserInfo.id).label('user_count')
    ).all()
    pool_user_count_map = dict(pool_user_count_rows)

    for pool in pools:
        st_info = {
            "max_stake_amount": pool_max_stake_amount_map[pool.id],
            "user_count": pool_user_count_map.get(pool.id, 0),
        }
        pool.statistics_info = json.dumps(st_info, cls=JsonEncoder)
        db.session.add(pool)
    db.session.commit()


def auto_online_pending_projects():
    """ 自动上架PENDING状态的活动 """
    now_ = now()
    projects: list[LaunchMiningProject] = LaunchMiningProject.query.filter(
        LaunchMiningProject.status == LaunchMiningProject.Status.PENDING,
        LaunchMiningProject.auto_online_time <= now_ - timedelta(minutes=2),
    ).all()
    for pcj in projects:
        pcj.status = LaunchMiningProject.Status.ONLINE
    db.session.commit()
    if projects:
        LaunchProjectCache.reload()


def update_project_pools_apr():
    """ 更新活动pool的预估APR """
    now_ = now()
    projects: list[LaunchMiningProject] = LaunchMiningProject.query.filter(
        LaunchMiningProject.status == LaunchMiningProject.Status.ONLINE,
        LaunchMiningProject.start_time <= now_,
        LaunchMiningProject.end_time >= now_,
    ).all()
    if not projects:
        return
    pool_rows: list[LaunchMiningPool] = LaunchMiningPool.query.filter(
        LaunchMiningPool.project_id.in_([i.id for i in projects]),
        LaunchMiningPool.status == LaunchMiningPool.Status.VALID,
    ).all()
    pcj_pools_map: dict[int, list[LaunchMiningPool]] = defaultdict(list)
    for p in pool_rows:
        pcj_pools_map[p.project_id].append(p)

    prices = PriceManager.assets_to_usd()
    online_market_names = set(MarketCache.list_online_markets())
    has_updated = False
    for pcj in projects:
        market_name = f"{pcj.reward_asset}USDT"  # 只能取上架中的现货市场与USDT币对的价格字段
        reward_asset_price = prices.get(pcj.reward_asset, 0)
        pools: list[LaunchMiningPool] = pcj_pools_map[pcj.id]

        if market_name not in online_market_names or not reward_asset_price:
            # 活动中途币种下架了，此时前端页面将不展示预估apr
            for pool in pools:
                if pool.apr is not None:
                    pool.apr = None
                    has_updated = True
            continue

        points = pcj.reward_settle_hour_points
        for pool in pools:
            stake_asset_prices = prices.get(pool.stake_asset, 0)
            if not stake_asset_prices:
                continue
            # 预估APR=每小时奖励金额 / 最新总存入金额*24*365*100%
            hourly_reward_amount = pool.reward_total_amount / len(points)
            cost = (pool.stake_amount or pool.locked_stake_amount) * stake_asset_prices
            if not cost:
                continue
            apr = (hourly_reward_amount * reward_asset_price / cost) * 24 * 365
            apr = quantize_amount(apr, 8)
            if apr:
                pool.apr = apr
                has_updated = True
    db.session.commit()
    if has_updated:
        LaunchProjectCache.reload()
