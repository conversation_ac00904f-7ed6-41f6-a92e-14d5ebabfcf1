# -*- coding: utf-8 -*-
from typing import List, Dict
from app.models import AntiFraudScore


class AntiFraudHelper:

    @staticmethod
    def get_anti_fraud_data(
        activity_type: AntiFraudScore.ActivityType, activity_id: int
    ) -> None | dict:
        """获取羊毛党自动计算得分数据"""
        record = AntiFraudScore.query.filter(
            AntiFraudScore.activity_type == activity_type,
            AntiFraudScore.activity_id == activity_id,
        ).first()
        if not record:
            return None
        # mysql will auto convert key to str, so we need to convert it back to int
        res = {int(key): item for key, item in record.data.items()}
        return res

    @staticmethod
    def batch_get_anti_fraud_data(
        activity_ids: List[int]
    ) -> Dict[int, dict]:
        """批量获取羊毛党自动计算得分数据
        返回:
            Dict[int, dict]: key为activity_id，value为对应的anti_fraud_data
        """
        records = AntiFraudScore.query.filter(
            AntiFraudScore.activity_id.in_(activity_ids),
        ).all()

        result = {}
        for record in records:
            data = {int(key): item for key, item in record.data.items()}
            result[record.activity_id] = data

        return result
