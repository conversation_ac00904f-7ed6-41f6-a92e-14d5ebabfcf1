# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from typing import List, Dict

from app import config
from app.business import PerpetualLogDB, \
    TradeLogDB
from app.business.alert import send_alert_notice
from app.business.amm import get_all_user_amm_assets, LiquidityService
from app.business.coupon import CouponTool
from app.business.pledge.helper import group_pledge_total_unflat
from app.business.coupon.base import get_coupon_service
from app.models import db, CreditBalance, MarginLoanOrder, AirdropActivityReward
from app.models.activity import UserAirDropBalanceSlice, Coupon, CouponApply, CouponPool
from app.utils import quantize_amount
from app.exceptions import UsingCouponLimit, UsingCouponObtained, InvalidArgument

AMM_BALANCE = 'amm_balance'
SPOT_BALANCE = 'spot_balance'
PERPETUAL_BALANCE = 'perpetual_balance'
LOAN_BALANCE = 'loan_balance'
CREDIT_BALANCE = 'credit_balance'

_ALERT_URL = config["ADMIN_CONTACTS"]["web_notice"]

AIRDROP_ADMIN_USER_ID = config["AIRDROP_ADMIN_USER_ID"]


def _get_amm_balance(assets: List[str]):
    # AMM资产
    asset_map = get_all_user_amm_assets()
    amm_items = []
    for user_id, asset_amount in asset_map.items():
        for asset, amount in asset_amount.items():
            if asset in assets:
                amm_items.append(
                    dict(balance=amount, user_id=user_id, asset=asset))
    return amm_items


def _get_loan_balance(assets: List[str]):
    # 杠杆借币
    loans = MarginLoanOrder.query.filter(
        MarginLoanOrder.asset.in_(assets),
        MarginLoanOrder.status.notin_([
            MarginLoanOrder.StatusType.FINISH,
            MarginLoanOrder.StatusType.FAIL
        ])
    ).with_entities(MarginLoanOrder.user_id,
                    MarginLoanOrder.asset,
                    MarginLoanOrder.interest_amount,
                    MarginLoanOrder.unflat_amount).all()

    user_asset_unflat_dict = defaultdict(lambda: defaultdict(Decimal))
    for item in loans:
        user_asset_unflat_dict[item.user_id][item.asset] += item.unflat_amount + item.interest_amount

    # 借贷
    pledge_unflat_data = group_pledge_total_unflat()
    for key, amount in pledge_unflat_data.items():
        user_id, account, asset = key
        if asset not in assets:
            continue
        user_asset_unflat_dict[user_id][asset] += amount

    loan_items = []
    for user_id, asset_amount_map in user_asset_unflat_dict.items():
        for asset, amount in asset_amount_map.items():
            loan_items.append(
                dict(
                    user_id=user_id,
                    asset=asset,
                    balance=-amount,
                )
            )
    return loan_items


def _get_credit_balance(assets: List[str]):
    # 授信借币
    credit_items = []
    credits_ = CreditBalance.query.filter(
        CreditBalance.asset.in_(assets),
        CreditBalance.unflat_amount > Decimal()
    ).with_entities(
        CreditBalance.user_id,
        CreditBalance.asset,
        CreditBalance.interest_amount,
        CreditBalance.unflat_amount).all()

    for item in credits_:
        credit_items.append(dict(
            user_id=item.user_id,
            asset=item.asset,
            balance=-(item.unflat_amount + item.interest_amount)
        ))
    return credit_items


def _get_spot_balance(timestamp: int, assets: List[str]):
    asset_str = '","'.join(assets)
    _fields = ['user_id', 'asset', 'balance']
    table = TradeLogDB.slice_balance_table(timestamp)
    if table is None:
        send_alert_notice(f'每月空投server现货资产快照未生成, 快照时间：{timestamp}', _ALERT_URL)
        return []
    items = table.select(
        'user_id',
        'asset',
        'SUM(`balance`) `balance`',
        group_by='`user_id`, `asset`',
        where=f'`asset` in ("{asset_str}") AND user_id != 0'
    )
    amm_system_user_ids = set(LiquidityService.list_amm_system_user_ids())
    items = [dict(zip(_fields, i)) for i in items]
    res = []
    for item in items:
        item['balance'] = quantize_amount(item['balance'], 8)
        if item['balance'] > Decimal() and item['user_id'] not in amm_system_user_ids:
            res.append(item)
    return res


def _get_perpetual_balance(timestamp: int, assets: List[str]):
    asset_str = '","'.join(assets)
    _fields = ['user_id', 'asset', 'balance']
    table = PerpetualLogDB.slice_balance_table(timestamp)
    if table is None:
        send_alert_notice(f'每月空投server合约资产快照未生成, 快照时间：{timestamp}', _ALERT_URL)
        return []
    items = table.select(
        'user_id',
        'asset',
        'SUM(`balance`) `balance`',
        group_by='`user_id`, `asset`',
        where=f'`asset` in ("{asset_str}") AND user_id != 0'
    )

    items = [dict(zip(_fields, i)) for i in items]
    res = []
    for item in items:
        item['balance'] = quantize_amount(item['balance'], 8)
        if item['balance'] > Decimal():
            res.append(item)
    return res


def _save_balance(balance_map: Dict[str, List], timestamp: int):
    balances: Dict = defaultdict(lambda: defaultdict(Decimal))
    for type_, records in balance_map.items():
        for record in records:
            balances[(record['user_id'], record['asset'])][type_] += record['balance']
    items = UserAirDropBalanceSlice.query.filter(
        UserAirDropBalanceSlice.slice_timestamp == timestamp
    ).all()
    item_map = dict()
    for item in items:
        item_map[(item.user_id, item.asset)] = item
    commit_records = []
    for key, type_map in balances.items():
        user_id, asset = key
        record = item_map.get((user_id, asset))
        if not record:
            record = UserAirDropBalanceSlice(user_id=user_id,
                                             slice_timestamp=timestamp,
                                             asset=asset)
            commit_records.append(record)
        for type_, amount in type_map.items():
            setattr(record, type_, amount)
    if commit_records:
        db.session.bulk_save_objects(commit_records)


def save_user_airdrop_balance_snapshot(*, assets: List[str],
                                       timestamp: int):
    history = UserAirDropBalanceSlice.query.filter(
        UserAirDropBalanceSlice.slice_timestamp == timestamp,
        UserAirDropBalanceSlice.asset.in_(assets)
    ).first()
    if not history:
        record_map = dict()
        record_map[AMM_BALANCE] = _get_amm_balance(assets)
        record_map[CREDIT_BALANCE] = _get_credit_balance(assets)
        record_map[LOAN_BALANCE] = _get_loan_balance(assets)
        _save_balance(record_map, timestamp)
        db.session.commit()

    history = UserAirDropBalanceSlice.query.filter(
        UserAirDropBalanceSlice.slice_timestamp == timestamp,
        UserAirDropBalanceSlice.spot_balance > Decimal(),
        UserAirDropBalanceSlice.asset.in_(assets)
    ).first()
    if not history:
        spot_records = _get_spot_balance(timestamp, assets)
        _save_balance({SPOT_BALANCE: spot_records}, timestamp)
        db.session.commit()

    history = UserAirDropBalanceSlice.query.filter(
        UserAirDropBalanceSlice.slice_timestamp == timestamp,
        UserAirDropBalanceSlice.perpetual_balance > Decimal(),
        UserAirDropBalanceSlice.asset.in_(assets)
    ).first()
    if not history:
        perpetual_records = _get_perpetual_balance(timestamp, assets)
        _save_balance({PERPETUAL_BALANCE: perpetual_records}, timestamp)
        db.session.commit()


def get_airdrop_coupon_details(apply_ids):
    query = CouponApply.query.join(Coupon).filter(
        CouponApply.id.in_(apply_ids)
    ).with_entities(
        CouponApply.id,
        Coupon.coupon_type,
        Coupon.id.label("coupon_id"),
        Coupon.value_type,
        Coupon.value,
        CouponApply.title,
        CouponApply.remark,
    ).all()
    return {item.id: {
        "apply_id": item.id,
        "coupon_type": item.coupon_type.name,
        "coupon_id": item.coupon_id,
        "value": CouponTool.value_display(item.coupon_type, item.value),
        "value_type": item.value_type,  # 空投的卡券翻译都是前端处理，这里就不进行多语言转化。
        "title": item.title,
        "remark": item.remark,
    } for item in query}


def get_airdrop_activity_rewards(airdrop_activity_id):
    rewards = AirdropActivityReward.query.filter(AirdropActivityReward.airdrop_activity_id == airdrop_activity_id).all()
    coupon_apply_ids = {r.coupon_apply_id for r in rewards if r.coupon_apply_id}
    coupon_mapping = get_airdrop_coupon_details(coupon_apply_ids)
    asset_rewards, coupon_rewards = [], []
    for r in rewards:
        if r.type == AirdropActivityReward.Type.ASSET:
            asset_rewards.append(dict(asset=r.asset, amount=r.amount))
        elif r.type == AirdropActivityReward.Type.COUPON:
            coupon_detail = coupon_mapping[r.coupon_apply_id]
            coupon_rewards.append(coupon_detail)

    return asset_rewards, coupon_rewards


def send_airdrop_coupons(user_id, coupon_apply_ids):
    pools = CouponPool.query.filter(
        CouponPool.apply_coupon_id.in_(coupon_apply_ids),
    ).order_by(CouponPool.expired_at).all()
    receive_result = []
    for pool in pools:
        coupon = Coupon.query.get(pool.coupon_id)
        coupon_service = get_coupon_service(coupon.coupon_type)
        is_success = True
        message = None
        try:
            coupon_service.active(pool, user_id, auto_commit=False)
            pool.add_user([user_id])

        except (UsingCouponLimit, UsingCouponObtained, InvalidArgument) as e:
            is_success = False
            message = e.message or e.message_template
        receive_result.append({
            "pool_id": pool.id,
            "apply_id": pool.apply_coupon_id,
            "is_success": is_success,
            "message": message
        })
    return receive_result


def batch_send_airdrop_coupons(coupon_apply_ids: list, user_ids: set) -> dict:
    if not coupon_apply_ids:
        return {}
    receive_result = defaultdict(lambda: defaultdict(dict))
    pools = CouponPool.query.filter(
        CouponPool.apply_coupon_id.in_(coupon_apply_ids),
    ).order_by(CouponPool.expired_at).all()
    for pool in pools:
        apply_id = pool.apply_coupon_id
        coupon = Coupon.query.get(pool.coupon_id)
        coupon_service = get_coupon_service(coupon.coupon_type)
        user_coupons = coupon_service.batch_active(pool, user_ids)
        success_user_ids = {user_coupon.user_id for user_coupon in user_coupons}
        for user_id in user_ids:
            is_success = user_id in success_user_ids
            receive_result[apply_id][user_id] = {
                "pool_id": pool.id,
                "apply_id": apply_id,
                "is_success": is_success,
            }
        pool.set_send_user_ids(success_user_ids)
        db.session.flush()
    return receive_result
