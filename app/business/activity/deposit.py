# -*- coding: utf-8 -*-
import json
from decimal import Decimal

from sqlalchemy import func
from sqlalchemy import or_

from app.exceptions import InvalidArgument
from app.models import DepositActivity, DepositActivityRule, Deposit, \
    DepositActivityRank
from app.models import db
from app.utils import now


def generator_rule_description(deposit_obj: DepositActivity):
    type_ = deposit_obj.type
    gift_asset = deposit_obj.gift_asset
    deposit_asset = deposit_obj.deposit_asset
    rule_record: DepositActivityRule = DepositActivityRule.query.filter(
        DepositActivityRule.deposit_activity_id == deposit_obj.id
    ).first()
    rule = json.loads(rule_record.rule_data)
    if type_ == DepositActivity.Type.RATE:
        return f"按比例：充 1{deposit_asset} 送 {rule['rate_gift']}" \
               f"{gift_asset}（封顶{rule['rate_upper_limit']}{gift_asset}）"
    elif type_ == DepositActivity.Type.FIX:
        return f"按定额：满 {rule['fix_threshold']}{deposit_asset} " \
               f"送 {rule['fix_gift_amount']}{gift_asset}"
    elif type_ == DepositActivity.Type.PARTITION:
        return f"按瓜分：充 {deposit_asset} 送 {gift_asset} " \
               f"单个用户活动期{deposit_asset}" \
               f"累计充值量/活动期累计{deposit_asset}总充值额 * 总送币数"
    elif type_ == DepositActivity.Type.RANK:
        rank_str = f'</br>'.join(
            [f'第{v["rank_min"]}到{v["rank_max"]}名固定送{v["rank_amount"]}个'
             for v in rule])
        return f"按排名：充 {deposit_asset} 送 {gift_asset} </br> {rank_str}"
    return ''


class DepositActivityRankProcessor:

    db_divider_num = 100

    def __init__(self, deposit_activity_id):
        self.deposit_activity: DepositActivity = DepositActivity.query.filter(
            DepositActivity.id == deposit_activity_id,
            DepositActivity.status == DepositActivity.Status.PASSED,
            DepositActivity.type.in_([DepositActivity.Type.PARTITION,
                                      DepositActivity.Type.RANK])
        ).first()
        if not self.deposit_activity:
            raise InvalidArgument(message='deposit_activity_id not found')
        self.rule: DepositActivityRule = DepositActivityRule.query.filter(
            DepositActivityRule.deposit_activity_id == deposit_activity_id
        ).first()
        if not self.rule:
            raise InvalidArgument(message='deposit_activity_id rule not found')

    # noinspection PyMethodMayBeStatic
    def get_hour(self):
        return now().replace(minute=0, second=0, microsecond=0)

    def get_rank_time(self):
        return min(self.deposit_activity.ended_at, self.get_hour())

    def should_update(self):
        return self.get_rank_time() != self.deposit_activity.ended_at

    def get_last_update_time(self):
        r = DepositActivityRank.query.filter(
            DepositActivityRank.activity_id == self.deposit_activity.activity_id
        ).first()
        return r.rank_time if r else 0

    def get_total_amount(self):
        deposit_data = Deposit.query.filter(
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.asset == self.deposit_activity.deposit_asset,
            or_(
                Deposit.status == Deposit.Status.CONFIRMING,
                Deposit.status == Deposit.Status.FINISHED,
                Deposit.status == Deposit.Status.TO_HOT
            ),
            Deposit.created_at >= self.deposit_activity.started_at,
            Deposit.created_at < self.deposit_activity.ended_at
        ).with_entities(
            func.sum(Deposit.amount).label('deposit_amount')
        ).first()
        return deposit_data.deposit_amount if deposit_data else Decimal()

    def rule_parser(self, rank_data):
        rule = json.loads(self.rule.rule_data)

        def get_rank_amount(user_rank, rank_rule):
            s = sorted(rank_rule, key=lambda x: x["rank_min"])
            for r in s:
                if r["rank_min"] <= user_rank <= r["rank_max"]:
                    return Decimal(r["rank_amount"])
            return Decimal()

        result = {}
        if self.deposit_activity.type == DepositActivity.Type.PARTITION:
            total = self.get_total_amount()
            for k, v in rank_data.items():
                result[k] = {
                    "deposit_amount": v["deposit_amount"],
                    "rank": v["rank"],
                    "gift_amount": v["deposit_amount"] / total *
                                   self.deposit_activity.total_amount
                    if total > 0 else Decimal(),
                }
        else:
            for k, v in rank_data.items():
                if gift_amount := get_rank_amount(v["rank"], rule):
                    result[k] = {
                        "deposit_amount": v["deposit_amount"],
                        "rank": v["rank"],
                        "gift_amount": gift_amount,
                    }
        return result

    def get_rank_data(self):
        # 最小充值量判断
        total_amount = func.sum(Deposit.amount).label('deposit_amount')
        deposit_data = Deposit.query.filter(
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.asset == self.deposit_activity.deposit_asset,
            or_(
                Deposit.status == Deposit.Status.CONFIRMING,
                Deposit.status == Deposit.Status.FINISHED,
                Deposit.status == Deposit.Status.TO_HOT
            ),
            Deposit.created_at >= self.deposit_activity.started_at,
            Deposit.created_at < self.deposit_activity.ended_at
        ).with_entities(
            total_amount, Deposit.user_id
        ).group_by(
            Deposit.user_id
        ).having(
            total_amount > self.deposit_activity.least_amount
        ).all()
        result = sorted([(v.user_id, v.deposit_amount) for v in deposit_data],
                        key=lambda x: -x[1])
        return {v[0]: {"deposit_amount": v[1], "rank": index + 1}
                for index, v in enumerate(result)}

    def get_last_rank_user(self):
        q = DepositActivityRank.query.filter(
            DepositActivityRank.activity_id == self.deposit_activity.activity_id
        ).with_entities(
            DepositActivityRank.user_id
        )
        return set(v.user_id for v in q)

    def process(self):
        # 上次更新时间大于等于活动的结束时间,则不再重新更新排名
        # last_update_time = self.get_last_update_time()
        # if last_update_time >= self.deposit_activity.end_time:
        #     return
        # # 上次更新时间大于等于活动的结束时间,则不再重新更新排名
        # if last_update_time == self.get_hour():
        #     return
        exists_users = self.get_last_rank_user()
        rank_data = self.get_rank_data()
        rank_data = self.rule_parser(rank_data)
        create_data = {key: v for key, v in rank_data.items()
                       if key not in exists_users}
        edit_data = {key: v for key, v in rank_data.items()
                     if key in exists_users}
        delete_data = set(exists_users) - set(rank_data.keys())
        if create_data:
            self.create_records(create_data)
        if edit_data:
            self.edit_records(edit_data)
        if delete_data:
            self.delete_records(delete_data)
        db.session.commit()

    def create_records(self, data):
        rank_at = self.get_rank_time()
        count = 0
        for user_id, v in data.items():
            count += 1
            db.session.add(
                DepositActivityRank(
                    rank_at=rank_at,
                    activity_id=self.deposit_activity.activity_id,
                    user_id=user_id,
                    rank=v["rank"],
                    deposit_amount=v['deposit_amount'],
                    gift_amount=v["gift_amount"],
                    gift_asset=self.deposit_activity.gift_asset
                )
            )
            if count % self.db_divider_num == 0:
                db.session.flush()
        db.session.flush()

    def edit_records(self, data):
        rank_at = self.get_rank_time()
        count = 0
        user_ids = data.keys()
        query = DepositActivityRank.query.filter(
            DepositActivityRank.activity_id == self.deposit_activity.activity_id,
            DepositActivityRank.user_id.in_(user_ids)
        ).all()
        query_dict = {v.user_id: v for v in query}
        for user_id, v in data.items():
            count += 1
            for key, value in v.items():
                setattr(query_dict[user_id], key, value)
            setattr(query_dict[user_id], 'rank_at', rank_at)
            if count % self.db_divider_num == 0:
                db.session.flush()
        db.session.flush()

    def delete_records(self, data):
        DepositActivityRank.query.filter(
            DepositActivityRank.activity_id == self.deposit_activity.activity_id,
            DepositActivityRank.user_id.in_(data)
        ).delete(synchronize_session=False)
        db.session.commit()
