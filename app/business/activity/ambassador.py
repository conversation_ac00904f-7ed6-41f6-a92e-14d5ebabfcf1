from collections import defaultdict
from decimal import Decimal
from functools import cached_property

from app.business import TradeHistoryDB, PerpetualHistoryDB
from app.business.bus_referral import BusRelationUserQuerier
from app.business.user import UserRepository
from app.caches import MarketCache, PerpetualMarketCache
from app.common import PerpetualMarketType
from app.models import db, Ambassador, ReferralHistory, SubAccount, AssetPrice, LoginRelationHistory, \
    AmbassadorActivityUserDetailInfo, User, ApiAuth, BusinessAmbassador
from app.models import OperationAmbassadorActivity, AmbassadorActivityUserInfo, ActivityBlackList, \
    AmbassadorActivityApplyUser
from app.models.exchange import AssetExchangeOrder, AssetExchangeOrderFee
from app.utils import quantize_amount, batch_iter, current_timestamp, timestamp_to_datetime


class ActivityBusiness:
    model = OperationAmbassadorActivity

    REMARK = '大使身份失效'

    def __init__(self, activity_id):
        self.activity = self.model.query.get(activity_id)

        self.write_back_mapping = defaultdict(list)

    def update_user_infos(self):
        """更新用户统计信息"""
        updated_at = timestamp_to_datetime(self._update_ts)
        # 活动中大使可能报名，以及报名大使可在活动有效期间邀请新用户
        self._find_ambassador_to_add_blacklist()
        self._create_or_update_details()
        spot_deal, perp_deal, exchange_deal, fee, spot_fee, perp_fee = self._get_valid_trade_info()
        amb_spot, amb_perp, amb_exchange = defaultdict(Decimal), defaultdict(Decimal), defaultdict(Decimal)
        amb_fee = defaultdict(Decimal)
        amb_spot_fee = defaultdict(Decimal)
        amb_perp_fee = defaultdict(Decimal)
        amb_new_spot, amb_new_perp, amb_new_exchange = defaultdict(Decimal), defaultdict(Decimal), defaultdict(Decimal)
        amb_new_fee = defaultdict(Decimal)
        amb_new_spot_fee = defaultdict(Decimal)
        amb_new_perp_fee = defaultdict(Decimal)
        amb_new_referree_ids = self._get_new_referral_mapping()
        for ambassador_user_id, referral_user_ids in self.referral_user_mapping.items():
            new_referree_ids = amb_new_referree_ids.get(ambassador_user_id, [])
            for user_id in referral_user_ids:
                amb_spot[ambassador_user_id] += spot_deal.get(user_id, 0)
                amb_perp[ambassador_user_id] += perp_deal.get(user_id, 0)
                amb_exchange[ambassador_user_id] += exchange_deal.get(user_id, 0)
                amb_fee[ambassador_user_id] += fee.get(user_id, 0)
                amb_spot_fee[ambassador_user_id] += spot_fee.get(user_id, 0)
                amb_perp_fee[ambassador_user_id] += perp_fee.get(user_id, 0)
                if user_id not in new_referree_ids:
                    continue
                amb_new_spot[ambassador_user_id] += spot_deal.get(user_id, 0)
                amb_new_perp[ambassador_user_id] += perp_deal.get(user_id, 0)
                amb_new_exchange[ambassador_user_id] += exchange_deal.get(user_id, 0)
                amb_new_fee[ambassador_user_id] += fee.get(user_id, 0)
                amb_new_spot_fee[ambassador_user_id] += spot_fee.get(user_id, 0)
                amb_new_perp_fee[ambassador_user_id] += perp_fee.get(user_id, 0)

        model = AmbassadorActivityUserInfo
        rows = model.query.filter(
            model.activity_id == self.activity.id,
        ).all()
        user_infos = {item.user_id: item for item in rows}
        ambassador_levels = self._get_ambassador_mapping()
        bus_amb_ids = set(i.user_id for i in BusRelationUserQuerier.get_all_valid_bus_ambassadors())
        ret = []
        for user_id in self._cached_applying_users:
            ambassador_level = ambassador_levels.get(user_id)
            user_info = user_infos.get(user_id)
            if user_info is None:
                user_info = model(
                    user_id=user_id,
                    activity_id=self.activity.id,
                )
            all_referral_user_ids = set(self.referral_user_mapping.get(user_id, []))
            new_referree_ids = set(amb_new_referree_ids.get(user_id, [])) & all_referral_user_ids
            user_info.report_at = updated_at
            user_info.amb_type = model.AmbassadorType.BUS if user_id in bus_amb_ids else model.AmbassadorType.NORMAL
            user_info.all_referral_count = len(all_referral_user_ids)
            user_info.referral_count = len(new_referree_ids)
            user_info.all_spot_amount = amb_spot.get(user_id, 0)
            user_info.spot_amount = amb_new_spot.get(user_id, 0)
            user_info.all_perp_amount = amb_perp.get(user_id, 0)
            user_info.perp_amount = amb_new_perp.get(user_id, 0)
            user_info.all_exchange_amount = amb_exchange.get(user_id, 0)
            user_info.exchange_amount = amb_new_exchange.get(user_id, 0)
            user_info.all_spot_fee_amount = amb_spot_fee.get(user_id, 0)
            user_info.all_perp_fee_amount = amb_perp_fee.get(user_id, 0)
            user_info.all_fee_amount = amb_fee.get(user_id, 0)
            user_info.spot_fee_amount = amb_new_spot_fee.get(user_id, 0)
            user_info.perp_fee_amount = amb_new_perp_fee.get(user_id, 0)
            user_info.fee_amount = amb_new_fee.get(user_id, 0)
            user_info.ambassador_level = ambassador_level
            user_info.valid_referral_count = len(self.write_back_mapping[user_id])
            ret.append(user_info)
        for rows in batch_iter(ret, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()

        self.update_rankings()

    @cached_property
    def _update_ts(self):
        ts = current_timestamp(to_int=True)
        now_ts = ts - ts % 3600
        update_ts = min(int(self.activity.ended_at.timestamp()), now_ts)
        return update_ts

    @cached_property
    def referral_user_mapping(self):
        return self._get_referral_user_mapping()

    def _get_referral_user_mapping(self):
        if self.activity.type in [
            self.activity.Type.FEE_AMOUNT,
            self.activity.Type.PERP_TRADE_AMOUNT,
        ]:
            rows = self._cached_new_referral_rows
        else:
            rows = self.__get_referral_rows_by_effected_at()
        ret = {}
        for row in rows:
            ret.setdefault(row.referrer_id, []).append(row.referree_id)
        return ret

    def _get_new_referral_mapping(self):
        ret = {}
        for row in self._cached_new_referral_rows:
            ret.setdefault(row.referrer_id, []).append(row.referree_id)
        return ret

    @cached_property
    def _cached_new_referral_rows(self):
        return self.__get_new_referral_rows()

    def __get_new_referral_rows(self):
        model = ReferralHistory
        rows = []
        for ch_user_ids in batch_iter(self._cached_applying_users, 1000):
            ch_rows = model.query.with_entities(
                model.referrer_id,
                model.referree_id,
            ).filter(
                model.referral_type == model.ReferralType.AMBASSADOR,
                model.referrer_id.in_(ch_user_ids),
                model.effected_at >= self.activity.started_at,
                model.effected_at < self.activity.ended_at,
            ).all()
            rows.extend(ch_rows)
        return rows

    def __get_referral_rows_by_effected_at(self):
        model = ReferralHistory
        rows = []
        for ch_user_ids in batch_iter(self._cached_applying_users, 1000):
            ch_rows = model.query.with_entities(
                model.referrer_id,
                model.referree_id,
                model.effected_at,
            ).filter(
                model.referral_type == model.ReferralType.AMBASSADOR,
                model.referrer_id.in_(ch_user_ids),
                model.effected_at < self.activity.ended_at,
            ).all()
            rows.extend(ch_rows)

        referrer_ids = {row.referrer_id for row in rows}
        if not referrer_ids:
            return rows

        amb_rows = Ambassador.query.with_entities(
            Ambassador.user_id,
            Ambassador.effected_at,
        ).filter(
            Ambassador.status == Ambassador.Status.VALID,
            Ambassador.user_id.in_(referrer_ids)
        ).all()
        biz_amb_rows = BusinessAmbassador.query.with_entities(
            BusinessAmbassador.user_id,
            BusinessAmbassador.effected_at,
        ).filter(
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
            BusinessAmbassador.user_id.in_(referrer_ids)
        ).all()
        amb_mapping = {row.user_id: row.effected_at for row in amb_rows}
        biz_amb_mapping = {row.user_id: row.effected_at for row in biz_amb_rows}
        ret = []
        for row in rows:
            amb_effected_at = amb_mapping.get(row.referrer_id)
            if not amb_effected_at:
                amb_effected_at = biz_amb_mapping.get(row.referrer_id)
                if not amb_effected_at:
                    continue
            # 不判断生效时间
            # if row.effected_at < amb_effected_at:
            #     continue
            ret.append(row)
        return ret

    def _get_valid_trade_info(self):
        spot_deal, perp_deal, exchange_deal, fee, spot_fee, perp_fee = self._trade_info
        invalid_users = self._get_invalid_users()

        def valid(x: dict) -> dict:
            return {key: value for key, value in x.items() if key not in invalid_users}

        valid_spot_deal = valid(spot_deal)
        valid_perp_deal = valid(perp_deal)
        valid_exchange_deal = valid(exchange_deal)
        valid_fee = valid(fee)
        valid_spot_fee = valid(spot_fee)
        valid_perp_fee = valid(perp_fee)
        return valid_spot_deal, valid_perp_deal, valid_exchange_deal, valid_fee, valid_spot_fee, valid_perp_fee

    def _get_invalid_users(self):
        model = AmbassadorActivityUserDetailInfo
        rows = model.query.with_entities(
            model.user_id
        ).filter(
            model.activity_id == self.activity.id,
            model.status == model.Status.INVALID
        ).all()
        user_ids = {row.user_id for row in rows}
        return user_ids

    @cached_property
    def _duplicate_device_users(self):
        main_user_ids = [user_id for user_ids in self.referral_user_mapping.values() for user_id in user_ids]
        return LoginRelationHistory.analysis_duplicate_device_register_by(main_user_ids)

    @cached_property
    def _trade_info(self):
        return self._get_trade_info()

    def _get_trade_info(self):
        start_ts, end_ts = int(self.activity.started_at.timestamp()), self._update_ts
        spot_deal, spot_fee, spot_self_deal, spot_self_fee = self._get_spot_trade_info(start_ts, end_ts)
        perp_deal, perp_fee, perp_self_deal, perp_self_fee = self._get_perp_trade_info(start_ts, end_ts)
        exchange_deal, exchange_fee = self._get_exchange_trade_info(self.activity.started_at, self.activity.ended_at)
        fee = defaultdict(Decimal)
        for user_id, amount_usd in spot_fee.items():
            fee[user_id] += amount_usd
        for user_id, amount_usd in perp_fee.items():
            fee[user_id] += amount_usd
        for user_id, amount_usd in exchange_fee.items():
            spot_fee[user_id] += amount_usd
            fee[user_id] += amount_usd

        for user_id, amount_usd in spot_self_fee.items():
            spot_fee[user_id] -= amount_usd
            fee[user_id] -= amount_usd
        for user_id, amount_usd in perp_self_fee.items():
            perp_fee[user_id] -= amount_usd
            fee[user_id] -= amount_usd

        for user_id, amount_usd in spot_self_deal.items():
            spot_deal[user_id] -= amount_usd
        for user_id, amount_usd in perp_self_deal.items():
            perp_deal[user_id] -= amount_usd
        return spot_deal, perp_deal, exchange_deal, fee, spot_fee, perp_fee

    def _get_spot_trade_info(self, start_ts, end_ts):
        user_ids = self._calculation_users
        where = f'time >= {start_ts} AND time < {end_ts}'
        records = []
        market_info_map = MarketCache.online_markets_detail()
        for ids in batch_iter(user_ids, 5000):
            tmp = TradeHistoryDB.get_users_history(
                ids, ['user_id', 'time', 'market', 'fee', 'fee_asset', 'deal', 'deal_user_id'], where,
                'user_deal_history'
            )
            for t in tmp:
                if t['market'] not in market_info_map:
                    continue
                t['deal_asset_'] = market_info_map[t['market']]['quote_asset']
                records.append(t)

        return self._handle_trade_info(records)

    def _get_perp_trade_info(self, start_ts, end_ts):
        user_ids = self._calculation_users
        where = f'time >= {start_ts} AND time < {end_ts}'
        records = []
        balance_asset_map = dict()
        market_info_map = PerpetualMarketCache().read_aside()
        for m in market_info_map:
            asset_ = PerpetualMarketCache.get_balance_asset(m)
            balance_asset_map[m] = asset_
        for ids in batch_iter(user_ids, 5000):
            tmp = PerpetualHistoryDB.get_users_deals(
                ids, ['user_id', 'time', 'market', 'deal_fee', 'fee_asset', 'price', 'amount', 'deal_user_id'], where
            )
            for t in tmp:
                if t['market'] not in market_info_map:
                    continue
                if not t['fee_asset']:
                    t['fee_asset'] = balance_asset_map[t['market']]
                t['fee'] = t['deal_fee']
                info = market_info_map[t['market']]
                if info['type'] == PerpetualMarketType.DIRECT:
                    t['deal'] = t['price'] * t['amount']
                else:
                    t['deal'] = t['amount']
                t['deal_asset_'] = info['money']
                records.append(t)
        return self._handle_trade_info(records)

    def _get_exchange_trade_info(self, start_at, end_at):
        model = AssetExchangeOrder
        rows = model.query.with_entities(
            model.target_asset_exchanged_amount,
            model.target_asset,
            model.user_id,
            model.id,
        ).filter(
            model.updated_at >= start_at,
            model.updated_at < end_at,
            model.status == model.Status.FINISHED,
            model.user_id.in_(self._calculation_users)
        ).all()
        price_map = self._price_map
        ids = []
        deal = defaultdict(Decimal)
        for row in rows:
            ids.append(row.id)
            user_id = self._referral_sub_user_dict.get(row.user_id, row.user_id)
            deal[user_id] += row.target_asset_exchanged_amount * price_map.get(row.target_asset, 0)

        fee_model = AssetExchangeOrderFee
        fee_rows = fee_model.query.with_entities(
            fee_model.user_id,
            fee_model.asset,
            fee_model.amount,
        ).filter(
            fee_model.exchange_order_id.in_(ids)
        ).all()
        fee = defaultdict(Decimal)
        for row in fee_rows:
            user_id = self._referral_sub_user_dict.get(row.user_id, row.user_id)
            fee[user_id] += row.amount * price_map.get(row.asset, 0)
        return deal, fee

    def _handle_trade_info(self, records):
        price_map = self._price_map
        price_map['USD'] = Decimal('1')
        deal, fee = defaultdict(Decimal), defaultdict(Decimal)
        self_deal, self_fee = defaultdict(Decimal), defaultdict(Decimal)
        for item in records:
            user_id = self._referral_sub_user_dict.get(item['user_id'], item['user_id'])
            fee_usd = item['fee'] * price_map.get(item['fee_asset'], 0)
            fee[user_id] += fee_usd
            deal_usd = item['deal'] * price_map.get(item['deal_asset_'], 0)
            deal[user_id] += deal_usd
            # 自成交
            main_deal_user_id = self._referral_sub_user_dict.get(item['deal_user_id'], item['deal_user_id'])
            if user_id == main_deal_user_id:
                self_deal[user_id] += deal_usd
                self_fee[user_id] += fee_usd
        return deal, fee, self_deal, self_fee

    @cached_property
    def _price_map(self):
        price_map = AssetPrice.get_price_map(timestamp_to_datetime(self._update_ts))
        return price_map

    @cached_property
    def _calculation_users(self):
        user_ids = [user_id for user_ids in self.referral_user_mapping.values() for user_id in user_ids]
        sub_user_ids = list(self._referral_sub_user_dict.keys())
        main_user_ids = list(set(self._referral_sub_user_dict.values()))
        return list(set(user_ids + sub_user_ids + main_user_ids))

    @cached_property
    def _referral_sub_user_dict(self):
        user_ids = [user_id for user_ids in self.referral_user_mapping.values() for user_id in user_ids]
        res = dict()
        model = SubAccount
        for ids in batch_iter(user_ids, 5000):
            tmp = model.query.filter(
                model.main_user_id.in_(ids)
            ).with_entities(model.user_id, model.main_user_id).all()
            res.update(dict(tmp))
        return res

    def _find_ambassador_to_add_blacklist(self):
        """活动期间内，只要大使身份失效就不再参与派奖（此后再恢复身份也不再计算"""
        applying_users = self._cached_applying_users
        model = Ambassador
        rows = model.query.with_entities(
            model.user_id
        ).filter(
            model.status == model.Status.VALID,
            model.user_id.in_(applying_users)
        ).all()
        biz_rows = BusinessAmbassador.query.with_entities(
            BusinessAmbassador.user_id
        ).filter(
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
            BusinessAmbassador.user_id.in_(applying_users)
        ).all()
        valid = {row.user_id for row in rows}
        valid |= {row.user_id for row in biz_rows}
        invalid = applying_users - valid
        if invalid:
            self.set_blacklist_batch(user_ids=invalid, remark=self.REMARK)

    def _get_ambassador_mapping(self):
        applying_users = self._cached_applying_users
        model = Ambassador
        rows = model.query.filter(
            model.user_id.in_(applying_users)
        ).all()
        biz_rows = BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id.in_(applying_users)
        ).all()
        row_mapping = {row.user_id: row for row in rows}
        biz_row_mapping = {row.user_id: row for row in biz_rows}
        ret = {}
        for user_id in applying_users:
            amb = row_mapping.get(user_id)
            if amb and amb.status == Ambassador.Status.VALID:
                ret.update({amb.user_id: amb.level.name})
                continue
            biz_amb = biz_row_mapping.get(user_id)
            if biz_amb and biz_amb.status == BusinessAmbassador.Status.VALID:
                ret.update({biz_amb.user_id: 'BUSINESS'})
                continue
            if amb and not biz_amb:
                ret.update({amb.user_id: amb.level.name})
                continue
            if biz_amb and not amb:
                ret.update({biz_amb.user_id: 'BUSINESS'})
                continue
            if amb and biz_amb:  # 都失效时
                if amb.effected_at >= biz_amb.effected_at:
                    ret.update({amb.user_id: amb.level.name})
                else:
                    ret.update({biz_amb.user_id: 'BUSINESS'})
        return ret

    def update_rankings(self):
        rows = AmbassadorActivityUserInfo.query.filter(
            AmbassadorActivityUserInfo.activity_id == self.activity.id,
        ).all()
        blacklist = self._get_blacklist()
        rankings = self._get_rankings_by(rows)
        for row in rows:
            reward = rankings.get(row.user_id)
            # 如果取消给用户发奖，取消排名，取消奖励
            if reward and row.user_id not in blacklist:
                row.rank = reward['rank']
                row.gift_amount = reward['gift_amount']
                row.ratio = reward['ratio']
            else:
                row.rank = None
                row.gift_amount = 0
                row.ratio = 0
        db.session.commit()

    def _create_or_update_details(self):
        main_user_ids = [user_id for user_ids in self.referral_user_mapping.values() for user_id in user_ids]
        users = {}
        for chunk_user_ids in batch_iter(main_user_ids, 5000):
            rows = User.query.with_entities(
                User.id,
                User.created_at
            ).filter(
                User.id.in_(chunk_user_ids)
            ).all()
            users.update(dict(rows))

        enabled_api_users = {
            v.user_id for v in ApiAuth.query.with_entities(
                ApiAuth.user_id.distinct().label('user_id')
            ).filter(
                ApiAuth.status == ApiAuth.Status.VALID
            ).all()
        }

        spot_deal, perp_deal, exchange_deal, fee, _, _ = self._trade_info
        model = AmbassadorActivityUserDetailInfo
        rows = model.query.filter(model.activity_id == self.activity.id).all()
        details = {row.user_id: row for row in rows}
        amb_new_referree_ids = self._get_new_referral_mapping()
        ret = []
        for ambassador_user_id, referral_user_ids in self.referral_user_mapping.items():
            new_referree_ids = amb_new_referree_ids.get(ambassador_user_id, [])
            for user_id in referral_user_ids:
                status = None
                if user_id in self._duplicate_device_users:
                    remark = '设备ID重复'
                    status = model.Status.INVALID
                else:
                    remark = ''
                detail = details.get(user_id)
                if detail is None:
                    detail = model(
                        user_id=user_id,
                        activity_id=self.activity.id,
                    )
                if status is not None:
                    # 自动判重无效优先级高于手动编辑
                    detail.status = status
                if detail.status is model.Status.VALID:
                    self.write_back_mapping[ambassador_user_id].append(user_id)
                spot_amount = spot_deal.get(user_id, 0)
                perp_amount = perp_deal.get(user_id, 0)
                exchange_amount = exchange_deal.get(user_id, 0)
                fee_amount = fee.get(user_id, 0)
                _amount = spot_amount + perp_amount + exchange_amount + fee_amount
                if _amount == 0 and self.activity.type in [
                    self.activity.Type.ALL_FEE_AMOUNT,
                    self.activity.Type.ALL_PERP_TRADE_AMOUNT,
                ]:  # 活动期间未发生交易，且属于全部xx类型
                    continue
                type_ = model.Type.NEW
                if user_id not in new_referree_ids:
                    type_ = model.Type.OLD
                detail.type = type_
                detail.ambassador_user_id = ambassador_user_id
                detail.register_at = users.get(user_id)
                detail.api_enabled = user_id in enabled_api_users
                detail.spot_amount = spot_amount
                detail.perp_amount = perp_amount
                detail.exchange_amount = exchange_amount
                detail.fee_amount = fee_amount
                detail.remark = remark or detail.remark
                ret.append(detail)

        for rows in batch_iter(ret, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()

    def _get_blacklist(self) -> set:
        model = ActivityBlackList
        rows = model.query.filter(
            model.activity_id == self.activity.activity_id,
            model.status == model.Status.PASSED,
        ).with_entities(
            model.user_id,
        ).all()
        return {row.user_id for row in rows}

    def _get_rankings_by(self, qualified_rows):
        if self.activity.type in [
            self.model.Type.FEE_AMOUNT,
            self.model.Type.ALL_FEE_AMOUNT,
        ]:  # 当 type == FEE_AMOUNT 时，fee_amount == all_fee_amount
            key = 'all_fee_amount'
        else:  # 同理，perp_amount == all_perp_amount
            key = 'all_perp_amount'
        rows = sorted(qualified_rows, key=lambda row: getattr(row, key), reverse=True)
        total_gift_amount = self.activity.gift_amount(self.get_applying_user_count())
        gift_rule = self.activity.gift_rule(self.get_applying_user_count())
        if not gift_rule['top']:  # 认为是历史数据
            raise RuntimeError
        if not gift_rule['other']:
            raise RuntimeError
        total_amount = sum(getattr(row, key) for row in rows)
        if total_amount <= 0:
            return {}

        top_rows = rows[:3]
        ret = {}
        rank = 0
        for idx, row in enumerate(top_rows):
            rank += 1
            value = getattr(row, key)
            ratio = Decimal(value / total_amount)
            gift_amount = gift_rule['top'][idx]
            gift_amount = quantize_amount(gift_amount, 8)
            ret.update({
                row.user_id: {
                    'rank': rank,
                    'gift_amount': gift_amount,
                    'ratio': ratio,  # 对应交易额度占比
                }
            })
        total_top_gift_amount = sum([value['gift_amount'] for value in ret.values()])
        remain_gift_amount = total_gift_amount - total_top_gift_amount
        if remain_gift_amount < 0:
            raise RuntimeError

        other_rows = rows[3:]
        other_total_amount = sum(getattr(row, key) for row in other_rows)
        for row in other_rows:
            rank += 1
            value = getattr(row, key)
            ratio = Decimal(value / total_amount)
            if other_total_amount > 0:
                split_ratio = value / other_total_amount
            else:
                split_ratio = 0
            gift_amount = split_ratio * remain_gift_amount
            if gift_amount > gift_rule['other']:
                gift_amount = gift_rule['other']
            gift_amount = quantize_amount(gift_amount, 8)
            ret.update({
                row.user_id: {
                    'rank': rank,
                    'gift_amount': gift_amount,
                    'ratio': ratio,  # 对应交易额度占比
                }
            })
        return ret

    def get_applying_user_count(self):
        return len(self._cached_applying_users)

    def get_activity_cleared_users(self) -> set:
        """获取该活动清退用户"""
        exclude_users = UserRepository.get_abnormal_users()
        activity_users = set(self.get_applying_users())
        return exclude_users & activity_users

    @cached_property
    def _cached_applying_users(self):
        return self.get_applying_users()

    def get_applying_users(self):
        model = AmbassadorActivityApplyUser
        rows = model.query.with_entities(
            model.user_id
        ).filter(
            model.activity_id == self.activity.id
        ).all()
        return {row.user_id for row in rows}

    def set_blacklist_batch(self, user_ids, remark='清退用户或活动黑名，无法发奖'):
        for uids in batch_iter(user_ids, 2000):
            for user_id in uids:
                self.set_blacklist(
                    user_id=user_id,
                    remark=remark,
                    commit=False
                )
            db.session.commit()

    def set_blacklist(self, user_id, remark, commit=False):
        model = ActivityBlackList
        row = model.get_or_create(
            user_id=user_id,
            activity_id=self.activity.activity_id
        )
        row.status = model.Status.PASSED
        row.remark = remark
        db.session.add(row)
        if commit:
            db.session.commit()

    def unset_blacklist(self, user_id, remark, commit=False):
        model = ActivityBlackList
        row = model.get_or_create(
            user_id=user_id,
            activity_id=self.activity.activity_id
        )
        row.status = model.Status.DELETED
        row.remark = remark
        db.session.add(row)
        if commit:
            db.session.commit()

    def add_user(self, user_id, commit=False):
        row = AmbassadorActivityApplyUser(
            user_id=user_id,
            activity_id=self.activity.id
        )
        db.session.add(row)
        if commit:
            db.session.commit()
