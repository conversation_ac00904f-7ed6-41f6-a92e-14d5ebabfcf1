from datetime import timedelta, date
from decimal import Decimal

from flask import current_app

from app.business import lock_call, PerpetualServerClient, ServerClient, ServerResponseCode
from app.business.clients.server import _BaseServerClient
from app.business.coupon.message import get_coupon_message_server, SendTiming
from app.business.coupon.utils import CouponTool
from app.caches.activity import CouponCache, CouponWarningCache
from app.common import CeleryQueues, TradeBusinessType, BalanceBusiness
from app.models import db
from app.models.activity import UserCoupon, CouponBalanceHistory, UserCouponTrace, ExperienceFeeUserCoupon, \
    CouponDailyBalanceHistory, Coupon, CashBackFeeUserCoupon, CashBackUserCouponTrace, CashBackFeeTraceDetail, \
    PerpetualSubsidyUserCoupon, PerpetualSubsidyUserCouponTrace, PerpetualSubsidyTraceDetail, \
    CopyTradingExperienceFeeCoupon, CopyTradingExperienceFeeUserCoupon, CopyTradingExperienceFeeUserCouponTrace
from app.models.copy_trading import CopyTraderHistory, CopyFollowerHistory, CopyTransferHistory
from app.utils import celery_task, batch_iter, quantize_amount, now, today


def _get_client_by_type(
        business: BalanceBusiness,
        is_deduct: False
):
    """
    合约体验金以及跟单合约体验金，发放以及回收的时候扣减的是合约账号
    其他都是从现货账号
    :return:
    """
    # 合约体验金发放资金的时候 增加资产使用合约账号
    if business in [BalanceBusiness.COUPON, BalanceBusiness.COPY_TRADING_EXPERIENCE_FEE] and not is_deduct:
        return PerpetualServerClient()
    # 从用户合约账号回收资产的时候使用合约账号
    if business in [BalanceBusiness.COUPON_RECYCLE, BalanceBusiness.COPY_TRADING_EXPERIENCE_FEE_RECYCLE] and is_deduct:
        return PerpetualServerClient()
    return ServerClient()


def _send_balance_insufficient_notice(e: Exception):
    if not (isinstance(e, _BaseServerClient.BadResponse) and e.code == ServerResponseCode.INSUFFICIENT_BALANCE):
        return
    warning_cache = CouponWarningCache(CouponWarningCache.WarningType.OWE)
    if warning_cache.exists():
        return
    warn_text = "卡券系统账户资金不足，无法发放卡券奖励，请尽快补充资金!"
    CouponTool.send_system_balance_warn_notice(warn_text)
    warning_cache.gen()


def _coupon_single_balance_change(
        user_coupon: UserCoupon,
        balance_type: CouponBalanceHistory.BusinessType,
        trade_type: TradeBusinessType,
        balance_business: BalanceBusiness,
        *,
        amount: Decimal = None,
        user_id: int = None,  # 合约跟单体验金是加给跟单子帐号
) -> bool:
    history = CouponBalanceHistory.query.filter(
        CouponBalanceHistory.user_coupon_id == user_coupon.id,
        CouponBalanceHistory.business_type == balance_type
    ).first()
    amount = amount or user_coupon.coupon_value
    user_id = user_id or user_coupon.user_id
    if not history:
        history = CouponBalanceHistory(
            user_coupon_id=user_coupon.id,
            business_type=balance_type,
            system_user_id=CouponTool.COUPON_BUSINESS_USER_ID,
            user_id=user_id,
            asset=user_coupon.coupon_value_type,
            amount=amount
        )
        db.session.add(history)
        db.session.commit()

    if history.amount != amount:
        history.amount = amount

    to_sys_user_bus = (
        BalanceBusiness.COUPON_RECYCLE,
        BalanceBusiness.COPY_TRADING_EXPERIENCE_FEE_RECYCLE,
    )
    is_add_to_sys_user = balance_business in to_sys_user_bus
    if history.status == CouponBalanceHistory.Status.CREATED:
        deduct_user_id = history.user_id if is_add_to_sys_user else history.system_user_id
        client = _get_client_by_type(balance_business, is_deduct=True)
        try:
            client.add_user_balance(
                user_id=deduct_user_id,
                asset=history.asset,
                amount=-history.amount,
                business=balance_business,
                business_id=history.id
            )
        except Exception as e:
            if not is_add_to_sys_user:
                _send_balance_insufficient_notice(e)
            current_app.logger.error(f"{balance_type.value} {trade_type.value} {balance_business.value} balance error {e!r}")
            return False
        history.status = CouponBalanceHistory.Status.DEDUCTED
        db.session.commit()

    if history.status != CouponBalanceHistory.Status.DEDUCTED:
        return False
    add_user_id = history.system_user_id if is_add_to_sys_user else history.user_id
    client = _get_client_by_type(balance_business, is_deduct=False)
    try:
        client.add_user_balance(
            user_id=add_user_id,
            asset=history.asset,
            amount=history.amount,
            business=balance_business,
            business_id=user_coupon.id
        )
    except Exception as e:
        current_app.logger.error(f"add user balance error {e!r}")
        return False
    history.status = CouponBalanceHistory.Status.FINISHED
    return True


def _coupon_daily_balance_change(
        user_coupon: UserCoupon,
        balance_date: date,
        amount: Decimal,
        coupon_type: Coupon.CouponType,
        balance_business: BalanceBusiness
):
    server = ServerClient()
    history = CouponDailyBalanceHistory.query.filter(
        CouponDailyBalanceHistory.coupon_type == coupon_type,
        CouponDailyBalanceHistory.user_id == user_coupon.user_id,
        CouponDailyBalanceHistory.date == balance_date,
        CouponDailyBalanceHistory.user_coupon_id == user_coupon.id
    ).first()
    if not history:
        system_user_id = CouponTool.COUPON_BUSINESS_USER_ID
        history = CouponDailyBalanceHistory(
            date=balance_date,
            user_id=user_coupon.user_id,
            system_user_id=system_user_id,
            user_coupon_id=user_coupon.id,
            coupon_type=coupon_type,
            asset=user_coupon.coupon_value_type,
            amount=amount,
        )
        db.session_add_and_commit(history)

    if history.status == CouponDailyBalanceHistory.Status.CREATED:
        try:
            server.add_user_balance(
                user_id=history.system_user_id,
                asset=history.asset,
                business=balance_business,
                business_id=history.id,
                amount=-history.amount
            )
        except Exception as e:
            _send_balance_insufficient_notice(e)
            current_app.logger.error(f"daily coupon balance created status  error {e!r}")
            return False
        history.status = CouponDailyBalanceHistory.Status.DEDUCTED
        history.deducted_at = now()
        db.session.commit()

    if history.status != CouponDailyBalanceHistory.Status.DEDUCTED:
        return False

    try:
        server.add_user_balance(
            user_id=history.user_id,
            asset=history.asset,
            amount=history.amount,
            business=balance_business,
            business_id=history.id
        )
    except Exception as e:
        current_app.logger.error(f"daily coupon balance add user balance error {e!r}")
        return False
    history.status = CouponDailyBalanceHistory.Status.FINISHED
    history.finished_at = now()
    return True


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def send_coupon_balance_task(user_coupon_id):
    """合约体验金发放卡劵资产"""
    user_coupon: UserCoupon = UserCoupon.query.get(user_coupon_id)
    if not user_coupon:
        return
    change_result = _coupon_single_balance_change(
        user_coupon=user_coupon,
        balance_type=CouponBalanceHistory.BusinessType.SEND,
        trade_type=TradeBusinessType.PERPETUAL,
        balance_business=BalanceBusiness.COUPON
    )
    if change_result:
        user_coupon.status = UserCoupon.Status.ACTIVE
        db.session.commit()


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def recycle_coupon_balance_task(user_coupon_id: int = None):
    """合约体验金-回收卡劵"""
    recycle_coupon_query = UserCoupon.query.select_from(UserCoupon).join(
        Coupon, UserCoupon.coupon_id == Coupon.id,
    ).filter(
        UserCoupon.status == UserCoupon.Status.TO_BE_RECYCLED,
        Coupon.coupon_type == Coupon.CouponType.EXPERIENCE_FEE,
    )
    if user_coupon_id:
        recycle_coupon_query = recycle_coupon_query.filter(
            UserCoupon.id == user_coupon_id
        )
    recycle_coupon_ids_rows = recycle_coupon_query.with_entities(UserCoupon.id).all()
    if not recycle_coupon_ids_rows:
        return

    recycle_coupon_ids = {u.id for u in recycle_coupon_ids_rows}
    to_recycled_user_coupons = []
    traces, coupon_details = [], []

    for ids in batch_iter(recycle_coupon_ids, 1000):
        to_recycled_user_coupons += UserCoupon.query.filter(
            UserCoupon.id.in_(ids),
            UserCoupon.status == UserCoupon.Status.TO_BE_RECYCLED,
        ).all()
        traces += UserCouponTrace.query.filter(
            UserCouponTrace.user_coupon_id.in_(ids)
        ).all()
        coupon_details += ExperienceFeeUserCoupon.query.filter(
            ExperienceFeeUserCoupon.user_coupon_id.in_(ids)
        ).all()
    coupon_detail_mapper = {d.user_coupon_id: d for d in coupon_details}
    coupon_trace_mapper = {trace.user_coupon_id: trace for trace in traces}
    for user_coupon in to_recycled_user_coupons:  # type: UserCoupon
        trace_data = coupon_trace_mapper.get(user_coupon.id)  # type: UserCouponTrace
        user_coupon_detail = coupon_detail_mapper[user_coupon.id]  # type: ExperienceFeeUserCoupon

        user_trade_amount = trace_data.trade_amount if trace_data else Decimal()
        profit_real = trace_data.profit_real if trace_data else Decimal()

        user_coupon_detail.user_trade_amount = user_trade_amount
        user_coupon_detail.profit_real = profit_real
        # 防止Server出现异常，走重试流程，没有交易信息的BUG
        db.session.commit()
        transfer_balance = CouponTool.get_user_transfer_balance(
            user_coupon.user_id,
            user_coupon.coupon_value_type,
            TradeBusinessType.PERPETUAL
        )
        if transfer_balance <= 0 or profit_real <= -user_coupon.coupon_value:
            user_coupon.status = UserCoupon.Status.USED
            user_coupon_detail.reason = ExperienceFeeUserCoupon.RecycledReason.DEFICIT
            db.session.commit()
            continue
        # 累计已实现盈亏 >= 0  正常回收 否则 回收 面额 - | 已实现盈亏 |
        recycle_amount = user_coupon.coupon_value
        if profit_real < 0:
            recycle_amount = user_coupon.coupon_value - abs(profit_real)
        recycle_amount = quantize_amount(min(transfer_balance, recycle_amount), 8)
        # 回收资产
        if user_coupon.status != UserCoupon.Status.TO_BE_RECYCLED:
            continue

        change_result = _coupon_single_balance_change(
            user_coupon=user_coupon,
            balance_type=CouponBalanceHistory.BusinessType.RECYCLE,
            trade_type=TradeBusinessType.PERPETUAL,
            balance_business=BalanceBusiness.COUPON_RECYCLE,
            amount=recycle_amount
        )
        if not change_result:
            return
        user_coupon_detail.recycled_amount = recycle_amount
        if recycle_amount < user_coupon.coupon_value:
            user_coupon_detail.reason = ExperienceFeeUserCoupon.RecycledReason.SOME
        else:
            user_coupon_detail.reason = ExperienceFeeUserCoupon.RecycledReason.ALL

        if user_coupon_detail.user_trade_amount and user_coupon_detail.user_trade_amount > 0:
            user_coupon.status = UserCoupon.Status.USED
        else:
            user_coupon.status = UserCoupon.Status.EXPIRED
        db.session.commit()


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def copy_trading_experience_fee_send_task(user_coupon_id):
    """发放合约跟单体验金卡劵的资产，系统账户 -> 带单子帐号｜跟单子帐号"""
    from app.business.copy_trading.base import CopyRelationQuerier
    from app.business.copy_trading.transfer import CopyTransferHelper

    user_coupon: UserCoupon = UserCoupon.query.get(user_coupon_id)
    if not user_coupon:
        return
    coupon_detail: CopyTradingExperienceFeeCoupon = CopyTradingExperienceFeeCoupon.query.filter(
        CopyTradingExperienceFeeCoupon.coupon_id == user_coupon.coupon_id,
    ).first()
    if not coupon_detail:
        return
    cp_user_coupon: CopyTradingExperienceFeeUserCoupon = CopyTradingExperienceFeeUserCoupon.query.filter(
        CopyTradingExperienceFeeUserCoupon.user_coupon_id == user_coupon_id,
    ).first()
    if not cp_user_coupon:
        return

    sub_user_id = None  # 跟单对应记录的状态需要是进行中，否则不划转
    if coupon_detail.trade_type == CopyTradingExperienceFeeCoupon.TradeType.TRADER:
        cp_transfer_type = CopyTransferHistory.Type.TRADER_COUPON_USE
        trade_his = CopyRelationQuerier.get_trade_his_by_id(cp_user_coupon.history_id)
        if trade_his and trade_his.status == CopyTraderHistory.Status.RUNNING:
            sub_user_id = trade_his.sub_user_id
    elif coupon_detail.trade_type == CopyTradingExperienceFeeCoupon.TradeType.FOLLOWER:
        cp_transfer_type = CopyTransferHistory.Type.FOLLOWER_COUPON_USE
        follow_his = CopyRelationQuerier.get_follow_his_by_id(cp_user_coupon.history_id)
        if follow_his and follow_his.status == CopyFollowerHistory.Status.FOLLOWING:
            sub_user_id = follow_his.sub_user_id
    else:
        raise ValueError(f"user_coupon:{user_coupon_id} unknown trade_type:{coupon_detail.trade_type.name}")

    if not sub_user_id:
        current_app.logger.error(
            f"copy_trading_experience_fee_send_task user_coupon_id {user_coupon_id} {coupon_detail.trade_type} "
            f"his_id:{cp_user_coupon.history_id} not_found_sub_user_id"
        )
        return

    change_result = _coupon_single_balance_change(
        user_coupon=user_coupon,
        balance_type=CouponBalanceHistory.BusinessType.SEND,
        trade_type=TradeBusinessType.PERPETUAL,
        balance_business=BalanceBusiness.COPY_TRADING_EXPERIENCE_FEE,
        user_id=sub_user_id,
    )
    if change_result:
        user_coupon.status = UserCoupon.Status.ACTIVE
        CopyTransferHelper.create_finished_coupon_transfer_his(
            main_user_id=user_coupon.user_id,
            sub_user_id=sub_user_id,
            sys_user_id=CouponTool.COUPON_BUSINESS_USER_ID,
            history_id=cp_user_coupon.history_id,
            transfer_type=cp_transfer_type,
            asset=user_coupon.coupon_value_type,
            amount=user_coupon.coupon_value,
        )
        db.session.commit()


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def copy_trading_experience_fee_recycle_task(sp_user_coupon_id: int = None):
    """合约跟单体验金-回收卡劵"""
    from app.business.copy_trading.base import CopyRelationQuerier
    from app.business.copy_trading.transfer import CopyTransferHelper

    recycle_q = UserCoupon.query.select_from(UserCoupon).join(
        Coupon, UserCoupon.coupon_id == Coupon.id,
    ).filter(
        UserCoupon.status == UserCoupon.Status.TO_BE_RECYCLED,
        Coupon.coupon_type == Coupon.CouponType.COPY_TRADING_EXPERIENCE_FEE,
    )
    if sp_user_coupon_id:
        recycle_q = recycle_q.filter(UserCoupon.id == sp_user_coupon_id)
    recycle_coupon_ids_rows = recycle_q.with_entities(UserCoupon.id).all()
    if not recycle_coupon_ids_rows:
        return

    recycle_coupon_ids = {u.id for u in recycle_coupon_ids_rows}
    to_recycled_user_coupons, traces, coupon_details = [], [], []

    for ids in batch_iter(recycle_coupon_ids, 2000):
        to_recycled_user_coupons += UserCoupon.query.filter(
            UserCoupon.id.in_(ids),
            UserCoupon.status == UserCoupon.Status.TO_BE_RECYCLED,
        ).all()
        traces += CopyTradingExperienceFeeUserCouponTrace.query.filter(
            CopyTradingExperienceFeeUserCouponTrace.user_coupon_id.in_(ids),
        ).all()
        coupon_details += CopyTradingExperienceFeeUserCoupon.query.filter(
            CopyTradingExperienceFeeUserCoupon.user_coupon_id.in_(ids),
        ).all()

    zero = Decimal()
    coupon_detail_mapper = {d.user_coupon_id: d for d in coupon_details}
    coupon_trace_mapper = {trace.user_coupon_id: trace for trace in traces}
    for user_coupon in to_recycled_user_coupons:
        user_coupon: UserCoupon
        trace_data: CopyTradingExperienceFeeUserCouponTrace = coupon_trace_mapper.get(user_coupon.id)
        user_coupon_detail: CopyTradingExperienceFeeUserCoupon = coupon_detail_mapper[user_coupon.id]

        user_trade_amount = trace_data.trade_amount if trace_data else Decimal()
        profit_real = trace_data.profit_real if trace_data else Decimal()
        user_coupon_detail.user_trade_amount = user_trade_amount
        user_coupon_detail.profit_real = profit_real
        db.session.commit()

        sub_user_id = None  # 跟单对应记录的状态需要是进行中，否则不划转
        if user_coupon_detail.trade_type == CopyTradingExperienceFeeCoupon.TradeType.TRADER:
            cp_transfer_type = CopyTransferHistory.Type.TRADER_COUPON_RECYCLE
            trade_his = CopyRelationQuerier.get_trade_his_by_id(user_coupon_detail.history_id)
            if trade_his and trade_his.status != CopyTraderHistory.Status.FINISHED:
                sub_user_id = trade_his.sub_user_id
        elif user_coupon_detail.trade_type == CopyTradingExperienceFeeCoupon.TradeType.FOLLOWER:
            cp_transfer_type = CopyTransferHistory.Type.FOLLOWER_COUPON_RECYCLE
            follow_his = CopyRelationQuerier.get_follow_his_by_id(user_coupon_detail.history_id)
            if follow_his and follow_his.status != CopyFollowerHistory.Status.FINISHED:
                sub_user_id = follow_his.sub_user_id
        else:
            current_app.logger.error(
                f"copy_trading_experience_fee_recycle_task user_coupon_id {user_coupon.id} "
                f"unknown {user_coupon_detail.trade_type.name} "
            )
            continue
        if not sub_user_id:
            current_app.logger.error(
                f"copy_trading_experience_fee_recycle_task user_coupon_id {user_coupon.id} "
                f"{user_coupon_detail.trade_type.name} his_id:{user_coupon_detail.history_id} not_found_sub_user_id"
            )
            continue

        transfer_balance = CouponTool.get_user_transfer_balance(
            sub_user_id,
            user_coupon.coupon_value_type,
            TradeBusinessType.PERPETUAL,
        )
        is_deficit = CopyTradingExperienceFeeUserCoupon.calc_is_deficit(
            user_coupon.coupon_value_type, user_coupon.coupon_value, profit_real
        )
        if transfer_balance <= zero or is_deficit:
            user_coupon_detail.reason = CopyTradingExperienceFeeUserCoupon.RecycledReason.DEFICIT
            user_coupon.status = UserCoupon.Status.USED
            db.session.commit()
            continue

        # 累计已实现盈亏 >= 0 正常回收
        # 否则 回收金额 = 面额 - abs(已实现盈亏)
        recycle_amount = user_coupon.coupon_value
        if profit_real < zero:
            recycle_amount = user_coupon.coupon_value - abs(profit_real)
        recycle_amount = quantize_amount(min(transfer_balance, recycle_amount), 8)
        if recycle_amount <= Decimal():
            continue

        if user_coupon.status != UserCoupon.Status.TO_BE_RECYCLED:
            continue

        change_result = _coupon_single_balance_change(
            user_coupon=user_coupon,
            balance_type=CouponBalanceHistory.BusinessType.RECYCLE,
            trade_type=TradeBusinessType.PERPETUAL,
            balance_business=BalanceBusiness.COPY_TRADING_EXPERIENCE_FEE_RECYCLE,
            amount=recycle_amount,
            user_id=sub_user_id,
        )
        if not change_result:
            continue

        user_coupon_detail.recycled_amount = recycle_amount
        if recycle_amount < user_coupon.coupon_value:
            user_coupon_detail.reason = CopyTradingExperienceFeeUserCoupon.RecycledReason.SOME
        else:
            user_coupon_detail.reason = CopyTradingExperienceFeeUserCoupon.RecycledReason.ALL
        if user_coupon_detail.user_trade_amount and user_coupon_detail.user_trade_amount > 0:
            user_coupon.status = UserCoupon.Status.USED
        else:
            user_coupon.status = UserCoupon.Status.EXPIRED
        CopyTransferHelper.create_finished_coupon_transfer_his(
            main_user_id=user_coupon.user_id,
            sub_user_id=sub_user_id,
            sys_user_id=CouponTool.COUPON_BUSINESS_USER_ID,
            history_id=user_coupon_detail.history_id,
            transfer_type=cp_transfer_type,
            asset=user_coupon.coupon_value_type,
            amount=recycle_amount,
        )
        db.session.commit()


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def trade_gift_given_balance_task(user_coupon_id: int = None):
    """交易赠金券发放资产"""
    to_given_user_query = UserCoupon.query.filter(
        UserCoupon.status == UserCoupon.Status.TO_BE_GIVEN
    )
    if user_coupon_id:
        to_given_user_query = to_given_user_query.filter(
            UserCoupon.id == user_coupon_id
        )
    to_given_user_coupons = to_given_user_query.all()
    if not to_given_user_coupons:
        return
    for user_coupon in to_given_user_coupons:
        change_result = _coupon_single_balance_change(
            user_coupon=user_coupon,
            balance_type=CouponBalanceHistory.BusinessType.SEND,
            trade_type=TradeBusinessType.SPOT,
            balance_business=BalanceBusiness.TRADE_GIFT_COUPON
        )
        if not change_result:
            return
        user_coupon.status = UserCoupon.Status.USED
        db.session.commit()


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def cashback_coupon_send_fee_task(user_coupon_id):
    user_coupon = UserCoupon.query.get(user_coupon_id)
    user_coupon_detail = CashBackFeeUserCoupon.query.filter(
        CashBackFeeUserCoupon.user_coupon_id == user_coupon_id
    ).first()
    trace_data = CashBackUserCouponTrace.query.filter(
        CashBackUserCouponTrace.user_coupon_id == user_coupon_id
    ).first()
    if not trace_data or not user_coupon_detail:
        return
    trace_detail = CashBackFeeTraceDetail.query.filter(
        CashBackFeeTraceDetail.trace_id == trace_data.id,
        CashBackFeeTraceDetail.cashback_date == trace_data.traced_date
    ).first()
    # 没有手续费返现记录
    if trace_data and not trace_detail and trace_data.traced_date < today():
        trace_data.traced_date = trace_data.traced_date + timedelta(days=1)
        db.session.commit()
        return
    if not trace_detail:
        return
    # 计算出返现数量的异常容错
    if trace_detail.amount <= 0:
        trace_data.traced_date = trace_data.traced_date + timedelta(days=1)
        trace_detail.cashback_status = CashBackFeeTraceDetail.CashBackStatus.FINISHED
        db.session.commit()
        return
    change_result = _coupon_daily_balance_change(
        user_coupon=user_coupon,
        balance_date=trace_detail.cashback_date,
        amount=trace_detail.amount,
        coupon_type=Coupon.CouponType.CASHBACK_FEE,
        balance_business=BalanceBusiness.CASHBACK_FEE
    )
    if not change_result:
        return
    # 重置追踪日期和金额
    trace_data.traced_date = trace_data.traced_date + timedelta(days=1)
    trace_detail.cashback_status = CashBackFeeTraceDetail.CashBackStatus.FINISHED
    # 更新进度条
    balances = CouponTool.get_coupon_daily_balance_history(
        user_coupon.user_id,
        user_coupon.id,
        Coupon.CouponType.CASHBACK_FEE
    )
    cashback_amount = sum(i.amount for i in balances) if balances else 0
    user_coupon_detail.real_cashback_amount = cashback_amount
    db.session.commit()
    if user_coupon.status != UserCoupon.Status.USED:
        return
    coupon_type = CouponCache.get_coupon_type(user_coupon.coupon_id)
    send_server = get_coupon_message_server(SendTiming.USED, coupon_type)
    send_server.send_message(user_coupon)


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def perpetual_subsidy_send_task(user_coupon_id):
    """ 合约补贴金发送任务 """
    user_coupon = UserCoupon.query.get(user_coupon_id)
    user_coupon_detail: PerpetualSubsidyUserCoupon = PerpetualSubsidyUserCoupon.query.filter(
        PerpetualSubsidyUserCoupon.user_coupon_id == user_coupon_id
    ).first()
    trace_data: PerpetualSubsidyUserCouponTrace = PerpetualSubsidyUserCouponTrace.query.filter(
        PerpetualSubsidyUserCouponTrace.user_coupon_id == user_coupon_id
    ).first()
    if not trace_data or not user_coupon_detail:
        return

    trace_detail = PerpetualSubsidyTraceDetail.query.filter(
        PerpetualSubsidyTraceDetail.trace_id == trace_data.id,
        PerpetualSubsidyTraceDetail.subsidy_date == trace_data.next_subsidy_date,
    ).first()
    if trace_data and not trace_detail:
        # 没有当天的追踪明细记录
        trace_data.next_subsidy_date = trace_data.next_subsidy_date + timedelta(days=1)
        db.session.commit()
        return
    if not trace_detail or trace_detail.subsidy_amount <= 0:
        return
    if trace_detail.status == PerpetualSubsidyTraceDetail.Status.FINISHED:
        return

    change_result = _coupon_daily_balance_change(
        user_coupon=user_coupon,
        balance_date=trace_detail.subsidy_date,
        amount=trace_detail.subsidy_amount,
        coupon_type=Coupon.CouponType.PERPETUAL_SUBSIDY,
        balance_business=BalanceBusiness.PERPETUAL_SUBSIDY_COUPON,
    )
    if not change_result:
        return

    trace_data.next_subsidy_date = trace_data.next_subsidy_date + timedelta(days=1)
    trace_detail.status = PerpetualSubsidyTraceDetail.Status.FINISHED
    balances = CouponTool.get_coupon_daily_balance_history(
        user_coupon.user_id,
        user_coupon.id,
        Coupon.CouponType.PERPETUAL_SUBSIDY,
    )
    total_subsidy_amount = sum(i.amount for i in balances) if balances else 0
    user_coupon_detail.subsidy_amount = total_subsidy_amount
    db.session.commit()