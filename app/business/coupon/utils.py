import json
from collections import defaultdict
from datetime import datetime, timedel<PERSON>
from decimal import Decimal
from typing import Dict, Any, List, Union, Tuple

from sqlalchemy import func

from app import config
from app.business import send_alert_notice
from app.business.clients import ServerClient, PerpetualServerClient
from app.business.coupon.base import get_auto_use_coupon_types, get_coupon_service, UserCouponStatus, \
    get_coupon_type_using_status_mapper, get_allow_use_many_types
from app.business.coupon.message import TimingSendMixin
from app.business.external_dbs import PerpetualHistoryDB, TradeHistoryDB
from app.business.prices import PriceManager
from app.business.email import send_coupon_balance_warn_email
from app.caches import PerpetualMarketCache, PerpetualOfflineMarketCache, MarketCache, CouponCache
from app.caches.activity import PushAvailablePoolCache, CouponPoolCache, DeliveryUserCouponCache, \
    CouponPopupReadCache, FriendGiftCouponCache, FriendGiftMulLimitCouponCache
from app.common import TradeBusinessType
from app.models import AmmMarket, ReferralHistory
from app.models.activity import InvestmentIncRateUserCoupon, CouponDailyBalanceHistory, Coupon, UserCoupon, \
    CouponApply, CouponApplyDraft, CouponBalanceWarnConfig, CouponPool
from app.models.exchange import AssetExchangeOrder, SysAssetExchangeOrder
from app.utils import quantize_amount, today, now, amount_to_str


class CouponTool:

    COUPON_BUSINESS_USER_ID = config["COUPON_USER_ID"]
    COUPON_POOL_URL = config['SITE_URL'] + "/my/info/coupon"
    PERPETUAL_URL = config['SITE_URL'] + "/futures"
    SPOT_URL = config['SITE_URL'] + "/exchange"

    @classmethod
    def get_exclude_market(cls):
        # 剔除交易费率最低的市场
        amm_market = {
            name for name, in AmmMarket.query.filter(
                AmmMarket.status == AmmMarket.Status.ONLINE,
                AmmMarket.amm_type == AmmMarket.AmmType.FINITE
            ).with_entities(AmmMarket.name).all()
        }
        return amm_market

    @classmethod
    def check_not_allow_next_use_coupon(cls, coupon_type: Union[str, Coupon.CouponType]):
        if isinstance(coupon_type, str):
            coupon_type = Coupon.CouponType[coupon_type]
        return coupon_type in cls.not_allow_next_use_coupons()

    @classmethod
    def get_user_transfer_balance(cls, user_id: int, asset: str, trade_type) -> Decimal:
        """获账户的资产"""
        zero = Decimal()
        server = ServerClient() if trade_type == TradeBusinessType.SPOT else PerpetualServerClient()
        asset_balance = server.get_user_balances(
            user_id=user_id,
            asset=asset
        ).get(asset, {})
        if not asset_balance:
            return zero
        keyword = "available" if trade_type == TradeBusinessType.SPOT else "transfer"
        return quantize_amount(asset_balance.get(keyword, zero), 8)

    @classmethod
    def dump_coupon_data(cls, coupon_type_mapper) -> Dict[str, Any]:
        result = {}
        for coupon_type, coupon_ids in coupon_type_mapper.items():
            coupon_server = get_coupon_service(coupon_type)
            detail_mapper = coupon_server.dump_coupon_detail_data(coupon_ids)
            result.update(detail_mapper)
        return result

    @classmethod
    def get_perpetual_trade_amount(cls, user_id: int, start_at: datetime, end_at: datetime, target_asset: str):
        _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'deal_history')
        start_at_time = start_at.timestamp()
        end_at_time = end_at.timestamp()
        where = f' `user_id` = {user_id}  ' \
                f' AND `time` > {start_at_time}  ' \
                f' AND `time` < {end_at_time}  '
        records = _db.table(_table).select(
            "MAX(`time`) as max_time",
            "SUM(`deal_stock`) as `trade_amount`",
            "SUM(`deal_fee`) as `trade_fee_amount`",
            "market",
            where=where,
            group_by="market"
        )
        max_time = start_at_time
        trade_amount_volume, trade_fee_usd = Decimal(), Decimal()
        perpetual_dict_map = PerpetualMarketCache().read_aside()
        off_perpetual_dict_map = PerpetualOfflineMarketCache().read_aside()

        for m_time, trade_amount, trade_fee_amount, market in records:
            if market in perpetual_dict_map:
                market_dict = perpetual_dict_map[market]
                balance_asset = PerpetualMarketCache.get_balance_asset(market_dict)
            else:
                market_dict = off_perpetual_dict_map[market]
                balance_asset = PerpetualOfflineMarketCache.get_balance_asset(market_dict)
            amount_rate = PriceManager.asset_to_asset(balance_asset, target_asset)
            fee_rate = PriceManager.asset_to_usd(balance_asset)
            max_time = max(max_time, m_time)
            trade_amount_volume += trade_amount * amount_rate
            trade_fee_usd += trade_fee_amount * fee_rate
        return max_time, quantize_amount(trade_amount_volume, 8), quantize_amount(trade_fee_usd, 8)

    @classmethod
    def get_spot_trade_amount(cls, user_id: int, start_at: datetime, end_at: datetime, target_asset: str):
        _db, _table = TradeHistoryDB.user_to_db_and_table(user_id, 'user_deal_history')
        start_at_time = start_at.timestamp()
        end_at_time = end_at.timestamp()
        where = f' `user_id` = {user_id}  ' \
                f' AND `time` > {start_at_time}  ' \
                f' AND `time` < {end_at_time}  '
        records = _db.table(_table).select(
            "market", "fee_asset",
            "MAX(`time`) as `max_time`",
            "SUM(`amount` * `price`) as trade_amount",
            "SUM(`fee`) as fee_amount",
            where=where,
            group_by="market, fee_asset"
        )
        trade_volume, fee_usd = Decimal(), Decimal()
        max_time = start_at_time
        exclude_market = cls.get_exclude_market()
        for market, fee_asset, m_time, trade_amount, fee_amount in records:
            if market in exclude_market:
                continue
            quote_asset = MarketCache(market).dict["quote_asset"]
            amount_rate = PriceManager.asset_to_asset(quote_asset, target_asset)
            fee_rate = PriceManager.asset_to_usd(fee_asset)
            max_time = max(max_time, m_time)
            trade_volume += trade_amount * amount_rate
            fee_usd += fee_amount * fee_rate

        return max_time, quantize_amount(trade_volume, 8), quantize_amount(fee_usd, 8)

    @classmethod
    def get_exchange_order_amount(cls, user_id: int, start_at: datetime, end_at: datetime, target_asset: str):
        exchanges = AssetExchangeOrder.query.filter(
            AssetExchangeOrder.updated_at > start_at,
            AssetExchangeOrder.updated_at <= end_at,
            AssetExchangeOrder.user_id == user_id,
            AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED
        ).with_entities(
            AssetExchangeOrder.id,
            AssetExchangeOrder.updated_at,
            AssetExchangeOrder.exchange_path
        ).all()
        exclude_market = cls.get_exclude_market()
        effective_order_mapper = {}
        for exchange in exchanges:
            json_path = json.loads(exchange.exchange_path)
            if all([path in exclude_market for path in json_path]):
                continue
            effective_order_mapper[exchange.id] = exchange

        system_orders = SysAssetExchangeOrder.query.filter(
            SysAssetExchangeOrder.exchange_order_id.in_(effective_order_mapper.keys()),
            SysAssetExchangeOrder.fee_amount > 0
        ).with_entities(
            SysAssetExchangeOrder.exchange_order_id,
            SysAssetExchangeOrder.target_asset,
            SysAssetExchangeOrder.target_asset_exchanged_amount,
            SysAssetExchangeOrder.fee_asset,
            SysAssetExchangeOrder.fee_amount
        ).all()

        trade_volume, fee_usd = Decimal(), Decimal()
        max_time = start_at
        for ex_id, trade_asset, trade_amount, fee_asset, fee_amount in system_orders:

            exchange_order = effective_order_mapper[ex_id]
            max_time = max(max_time, exchange_order.updated_at)
            amount_rate = PriceManager.asset_to_asset(trade_asset, target_asset)
            fee_rate = PriceManager.asset_to_usd(fee_asset)

            trade_volume += trade_amount * amount_rate
            fee_usd += fee_amount * fee_rate

        return max_time, quantize_amount(trade_volume, 8), quantize_amount(fee_usd, 8)

    @classmethod
    def get_user_trade_amount(cls, user_id: int, start_at: datetime, end_at: datetime, asset: str) -> Tuple[
        float, Decimal, Decimal]:
        market_list = PerpetualMarketCache.balance_asset_to_markets(asset)
        _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'deal_history')
        start_at_time = start_at.timestamp()
        end_at_time = end_at.timestamp()
        market_str = '","'.join(market_list)
        where = f' `user_id` = {user_id}  ' \
                f' AND `time` > {start_at_time}  ' \
                f' AND `time` < {end_at_time}  ' \
                f'AND `market` in ("{market_str}") '
        # 保险基金 > 0，爆仓系统扣除了用户的保证金，也需要统计到累计已实现盈亏中
        records = _db.table(_table).select(
            'Max(`time`) as `max_time`',
            'SUM(`deal_stock`) as `trade_amount` ',
            'SUM(case when `fee_asset` = "" and `deal_insurance` > 0 then deal_profit - deal_fee - deal_insurance '
            'when `fee_asset` = "" and `deal_insurance` <= 0 then deal_profit - deal_fee '
            'when `fee_asset` != "" and `deal_insurance` > 0 then deal_profit - deal_insurance '
            'else deal_profit end) as `deal_profit`',
            where=where
        )
        max_time, sum_trade_amount, sum_deal_profit = records[0]
        return max_time or start_at_time, quantize_amount(sum_trade_amount or Decimal(), 8), \
               quantize_amount(sum_deal_profit or Decimal(), 8)

    @classmethod
    def get_user_funding(cls, user_id: int, start_at: datetime, end_at: datetime, asset: str) -> Tuple[float, Decimal]:
        start_at_time = start_at.timestamp()
        end_at_time = end_at.timestamp()
        _funding_db, _funding_table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'position_funding_history')
        funding_where = f'`user_id` = {user_id} ' \
                        f' AND `time` > {start_at_time} ' \
                        f' AND `time` < {end_at_time} ' \
                        f' AND `asset` = "{asset}" '
        funding_records = _funding_db.table(_funding_table).select(
            'SUM(`funding`)', 'MAX(`time`)',
            where=funding_where
        )
        funding_amount, max_time = funding_records[0]
        return max_time or start_at_time, quantize_amount(funding_amount or Decimal(), 8)

    @classmethod
    def get_active_investment_user_coupons(cls) -> List:
        """获取使用中的理财加息券信息"""
        _today = today()
        q = UserCoupon.query.select_from(UserCoupon).join(Coupon, UserCoupon.coupon_id == Coupon.id).filter(
            UserCoupon.status == UserCoupon.Status.ACTIVE,
            UserCoupon.usable_expired_at > _today,  # usable_expired_at 已对齐到天
            Coupon.coupon_type == Coupon.CouponType.INVESTMENT_INCREASE_RATE,
            Coupon.status == Coupon.CouponStatus.OPEN,
        )
        user_coupons: List[UserCoupon] = q.all()
        if not user_coupons:
            return []

        coupon_ids = {i.coupon_id for i in user_coupons}
        user_coupon_ids = {i.id for i in user_coupons}
        coupon_rows = Coupon.query.filter(
            Coupon.id.in_(coupon_ids)
        ).with_entities(
            Coupon.id,
            Coupon.usable_days,
        ).all()
        coupon_map = {i.id: i for i in coupon_rows}
        coupon_detail_map = cls.dump_coupon_data({Coupon.CouponType.INVESTMENT_INCREASE_RATE: list(coupon_ids)})
        user_coupon_details = InvestmentIncRateUserCoupon.query.filter(
            InvestmentIncRateUserCoupon.user_coupon_id.in_(user_coupon_ids),
        ).all()
        user_coupon_detail_map = {i.user_coupon_id: i for i in user_coupon_details}

        user_coupon_infos = []
        for user_coupon in user_coupons:
            coupon = coupon_map[user_coupon.coupon_id]
            coupon_detail = coupon_detail_map[user_coupon.coupon_id]
            user_coupon_detail = user_coupon_detail_map[user_coupon.id]
            info = {
                "coupon_id": user_coupon.coupon_id,
                "user_coupon_id": user_coupon.id,
                "user_id": user_coupon.user_id,
                "asset": user_coupon_detail.investment_asset,
                "principal_amount_limit": Decimal(coupon_detail["principal_amount_limit"]),
                "principal_asset": coupon_detail["principal_asset"],
                "max_inc_days": coupon.usable_days,  # 加息天数=使用有效期
                "value": user_coupon.coupon_value,
            }
            user_coupon_infos.append(info)
        return user_coupon_infos

    @classmethod
    def check_sending_end(cls, pool: CouponPool, user_id: int) -> (int | None, bool):
        is_sending_end = pool.send_count >= pool.total_count
        if is_sending_end:
            return None, True
        if pool.dynamic_user_type is CouponApply.DynamicUser.REFERRAL_GIFT_MUL:
            referrer_id, is_sending_end = CouponTool.check_referral_gift_mul(
                pool.coupon_id, pool.id, pool.apply_coupon_id, user_id
            )
            return referrer_id, is_sending_end
        return None, False

    @classmethod
    def check_user_obtained_coupon(cls, pool_id: int, user_id: int) -> bool:
        """检查用户是否已领取"""
        return bool(UserCoupon.query.filter(
            UserCoupon.pool_id == pool_id,
            UserCoupon.user_id == user_id
        ).first())

    @classmethod
    def check_users_obtained_coupon(cls, pool_id: int, user_ids: set) -> set:
        """批量检查用户是否已领取，返回已领取的用户id集合"""
        rows = UserCoupon.query.filter(
            UserCoupon.pool_id == pool_id,
            UserCoupon.user_id.in_(user_ids)
        ).all()
        return {r.user_id for r in rows}

    @classmethod
    def get_same_coupon_type_count(cls, coupon_type: Coupon.CouponType, user_id: int) -> int:
        """获取已拥有同种类型使用中的卡劵的数量"""
        coupon_server = get_coupon_service(coupon_type)
        return coupon_server.get_user_coupon_type_count(user_id)

    @classmethod
    def check_had_using_coupon_type(cls, coupon_type: Coupon.CouponType, user_id: int) -> bool:
        """检查是否拥有相同类型的卡劵"""
        coupon_server = get_coupon_service(coupon_type)
        if not coupon_server.is_auto_use or coupon_server.allow_using_many:
            return False
        return bool(coupon_server.get_user_coupon_type_count(user_id, status=coupon_server.using_status))

    @classmethod
    def check_users_had_using_coupon_type(cls, coupon_type: Coupon.CouponType, user_ids: set) -> set:
        """批量检查是否拥有相同类型的卡劵，返回拥有的用户id集合"""
        res = set()
        coupon_server = get_coupon_service(coupon_type)
        for user_id in user_ids:
            if not coupon_server.is_auto_use or coupon_server.allow_using_many:
                continue
            if coupon_server.get_user_coupon_type_count(user_id, status=coupon_server.using_status):
                res.add(user_id)
        return res

    @classmethod
    def check_referral_gift_mul(cls, coupon_id: int, pool_id: int, apply_id: int, user_id: int) -> (int | None, bool):
        """检查邀请礼-多人类型，单人最大邀请是否超出限制"""
        apply = CouponApply.query.with_entities(
            CouponApply.origin_id,
            CouponApply.send_at,
        ).filter(
            CouponApply.id == apply_id
        ).first()
        if not apply:
            return None, True
        apply_draft = CouponApplyDraft.query.with_entities(
            CouponApplyDraft.invitees_limit
        ).filter(
            CouponApplyDraft.id == apply.origin_id
        ).first()
        if not apply_draft:
            return None, True
        if not apply_draft.invitees_limit:
            # 限制上线前的历史数据为 0，包含历史正在进行中的
            return None, False
        referrer_id = ReferralHistory.query.with_entities(
            ReferralHistory.referrer_id
        ).filter(
            ReferralHistory.referree_id == user_id,
            ReferralHistory.status == ReferralHistory.Status.VALID,
        ).scalar() or None
        if not referrer_id:
            return referrer_id, True
        referral_rows = ReferralHistory.query.with_entities(
            ReferralHistory.referree_id
        ).filter(
            ReferralHistory.referrer_id == referrer_id,
            ReferralHistory.status == ReferralHistory.Status.VALID,
            ReferralHistory.effected_at >= apply.send_at,
        ).all()
        referree_ids = {referree_id for referree_id, in referral_rows}
        if len(referree_ids) <= apply_draft.invitees_limit:
            return referrer_id, False
        received_count = UserCoupon.query.with_entities(
            func.count('*')
        ).filter(
            UserCoupon.user_id.in_(referree_ids),
            UserCoupon.pool_id == pool_id,
            UserCoupon.coupon_id == coupon_id,
        ).scalar() or 0
        remain = received_count < apply_draft.invitees_limit
        return referrer_id, not remain

    @classmethod
    def dump_user_coupon_status(cls, coupon_type: Coupon.CouponType, status: UserCoupon.Status):
        using_status = cls.get_coupon_type_using_status(coupon_type)
        if status in using_status:
            return UserCouponStatus.USING
        return status

    @classmethod
    def get_coupon_type_using_status(cls, coupon_type: Union[Coupon.CouponType, None]):
        coupon_type_using_mapper = get_coupon_type_using_status_mapper()
        if not coupon_type:
            return list(set([j for i in coupon_type_using_mapper.values() for j in i]))
        return coupon_type_using_mapper[coupon_type]

    @classmethod
    def clear_cache(cls):
        from app.business.coupon.pool import reload_coupon_pool_cache_task
        reload_coupon_pool_cache_task.delay()
        PushAvailablePoolCache.delete_all()

    @classmethod
    def get_coupon_daily_balance_history(cls, user_id, user_coupon_id, coupon_type):
        return CouponDailyBalanceHistory.query.filter(
            CouponDailyBalanceHistory.user_id == user_id,
            CouponDailyBalanceHistory.user_coupon_id == user_coupon_id,
            CouponDailyBalanceHistory.coupon_type == coupon_type,
            CouponDailyBalanceHistory.status == CouponDailyBalanceHistory.Status.FINISHED
        ).all()

    @classmethod
    def auto_use_coupon_types(cls):
        return get_auto_use_coupon_types()

    @classmethod
    def not_allow_next_use_coupons(cls):
        return set(cls.auto_use_coupon_types()) - set(get_allow_use_many_types())

    @classmethod
    def clear_pool_caches(cls, pools):
        for pool in pools:
            # 清除 触达缓存
            pool_id = pool.id
            TimingSendMixin.delete_send_cache_by_pool(pool_id)
            CouponPoolCache.remove_pool(pool_id)
            DeliveryUserCouponCache(pool_id).delete()
            CouponPopupReadCache(pool_id).delete()
            FriendGiftCouponCache(pool_id).delete()
            FriendGiftMulLimitCouponCache(pool_id).delete()

    @classmethod
    def get_friend_gift_coupon_and_apply_list(
            cls
    ) -> Tuple[List[Tuple[Coupon, CouponApply]], List[Tuple[Coupon, CouponApply]]]:
        """ 获取生效中好友礼的卡券和卡券发放 """
        apply_list = CouponApply.query.filter(
            CouponApply.dynamic_user_type.in_(CouponApply.DYNAMIC_USER_REFERRAL_GIFTS),
            CouponApply.status == CouponApply.Status.FINISHED
        ).all()
        if not apply_list:
            return [], []

        coupon_ids = {i.coupon_id for i in apply_list}
        coupons = Coupon.query.filter(Coupon.id.in_(list(coupon_ids))).all()
        coupon_map = {i.id: i for i in coupons}

        results = []
        invalid_results = []
        for apply in apply_list:
            coupon = coupon_map[apply.coupon_id]
            if apply.send_at > now() - timedelta(days=coupon.receivable_days):
                results.append((coupon, apply))
            else:
                invalid_results.append((coupon, apply))
        return results, invalid_results

    @classmethod
    def has_friend_gift(cls, user_id: int) -> bool:
        apply_list = CouponApply.query.filter(
            CouponApply.dynamic_user_type.in_(CouponApply.DYNAMIC_USER_REFERRAL_GIFTS),
            CouponApply.status == CouponApply.Status.FINISHED
        ).all()
        for apply in apply_list:
            if user_id in apply.cached_user_ids:
                return True
        return False

    @classmethod
    def update_apply_and_draft_status(cls, apply_draft: CouponApplyDraft, apply: CouponApply, is_pass=False):
        if apply_draft.status == CouponApplyDraft.Status.REJECTED:
            apply_draft.status = CouponApplyDraft.Status.CREATED
            apply.status = CouponApply.Status.DRAFT
        elif not is_pass and apply_draft.status == CouponApplyDraft.Status.CREATED:
            apply_draft.status = CouponApplyDraft.Status.REJECTED
            apply.status = CouponApply.Status.DRAFT
        elif is_pass and apply.status == CouponApply.Status.DRAFT:
            apply.status = CouponApply.Status.CREATED
            apply_draft.status = CouponApplyDraft.Status.PASSED
            if apply.account_balance_status == CouponApply.AccountBalanceStatus.CREATED:
                apply.account_balance_status = CouponApply.AccountBalanceStatus.SENT
        elif not is_pass and apply.status == CouponApply.Status.CREATED:
            apply.status = CouponApply.Status.CANCEL
            if apply.account_balance_status == CouponApply.AccountBalanceStatus.SENT:
                apply.account_balance_status = CouponApply.AccountBalanceStatus.TO_BE_RECYCLED
        return apply_draft, apply

    @classmethod
    def value_display(cls, coupon_type: Coupon.CouponType, value: Decimal) -> str:
        """前端卡券展示值"""
        str_value = amount_to_str(value)
        if coupon_type == Coupon.CouponType.VIP_UPGRADE:
            str_value = f"+{str_value}"
        return str_value

    @classmethod
    def update_friend_gift_mul_cache(cls, pool_id: int, referrer_id: int):
        # +1
        cache = FriendGiftMulLimitCouponCache(pool_id)
        cache.add(referrer_id)

    @classmethod
    def send_system_balance_warn_notice(cls, warn_text: str):
        c = CouponBalanceWarnConfig.query.first()
        for user_id in c.warn_user_ids.split(","):
            send_coupon_balance_warn_email.delay(user_id, warn_text)
        send_alert_notice(
            warn_text,
            config["ADMIN_CONTACTS"]["coupon_notice"]
        )



def get_coupon_apply_by_type(coupon_type):
    model = CouponApply
    coupon_ids = CouponCache.get_coupon_type_ids(coupon_type)
    coupon_rows = model.query.filter(
        model.coupon_id.in_(coupon_ids)
    ).with_entities(
        model.id,
        model.origin_id,
        model.origin_type
    ).all()
    return coupon_rows


def build_coupon_apply_id_title_mapper(coupon_rows, distribution_map=None):
    origin_type_ids_mapper = defaultdict(set)
    for apply in coupon_rows:
        origin_type_ids_mapper[apply.origin_type].add(apply.origin_id)
    apply_model = CouponApply
    draft_model = CouponApplyDraft
    draft_mapper = {id_: title for id_, title in draft_model.query.filter(
        draft_model.id.in_(origin_type_ids_mapper[apply_model.OriginType.DRAFT])
    ).with_entities(
        draft_model.id,
        draft_model.title
    ).all()}
    result = {}
    for apply_id, origin_id, origin_type in coupon_rows:
        if origin_type == apply_model.OriginType.DISTRIBUTION:
            if distribution_map:
                result[apply_id] = f"{origin_id}-{apply_id} {distribution_map[origin_id]}"
        else:
            result[apply_id] = f"{apply_id} {draft_mapper[origin_id]}"
    return result


def get_coupon_apply_id_title_mapper(coupon_type):
    coupon_rows = get_coupon_apply_by_type(coupon_type)
    origin_type_ids_mapper = defaultdict(set)
    for apply in coupon_rows:
        origin_type_ids_mapper[apply.origin_type].add(apply.origin_id)
    return build_coupon_apply_id_title_mapper(coupon_rows)


def get_coupon_apply_by_coupon_type(coupon_type):
    coupon_ids = CouponCache.get_coupon_type_ids(coupon_type)
    return CouponApply.query.filter(CouponApply.coupon_id.in_(coupon_ids)).all()
