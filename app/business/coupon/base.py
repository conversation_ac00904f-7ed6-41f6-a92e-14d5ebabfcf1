from datetime import timed<PERSON><PERSON>, datetime
from decimal import Decimal
from enum import Enum
from typing import Tuple, Union, List, Dict, Set

from flask_babel import gettext as _
from pyroaring import BitMap
from sqlalchemy import func

from app.business.coupon.message import get_coupon_message_server, SendTiming
from app.business.lock import <PERSON>acheLock, LockKeys
from app.business.user_group import UserGroupValidator
from app.caches.activity import CouponPoolCache, AvailableCouponPoolUserCache, PushAvailablePoolCache, \
    CouponRiskWhiteCache, CouponRiskCache
from app.caches.system import IpWhiteListCache
from app.caches.user import UserVisitPermissionCache
from app.exceptions import (
    CouponHasRunOut,
    CouponSendEnd,
    UsingCouponObtained,
    UsingCouponLimit, InvalidArgument
)
from app.models import ModelBase, db
from app.models.activity import (
    Coupon,
    UserCoupon,
    InvestmentIncreaseRateCoupon,
    CouponCodePool,
    CouponPool,
    CouponApply,
    CouponExchangeHistory, CouponRisk
)
from app.utils import now, amount_to_str, datetime_to_time


class CouponPoolStatus(Enum):
    AVAILABLE = 'available'  # 可领取
    ENDING = 'ending'  # 已领完


class UserCouponStatus(Enum):
    CREATED = "created"  # 待激活
    USING = "using"  # 使用中
    USED = 'used'  # 已使用
    EXPIRED = 'expired'  # 已过期
    INVALID = "invalid"  # 已失效


class _CouponMeta(type):
    _coupon_services = {}
    _coupon_type_using_status = {}
    _auto_use_coupon_type = []
    _allow_using_many_coupon_type = []

    def __new__(mcs, name, bases, dct):
        cls = super().__new__(mcs, name, bases, dct)
        coupon_type = getattr(cls, 'coupon_type', None)
        if coupon_type is None:  # base class
            return cls
        mcs._coupon_services[coupon_type] = cls
        if getattr(cls, "is_auto_use", False):
            mcs._auto_use_coupon_type.append(coupon_type)
        if getattr(cls, "allow_using_many", False):
            mcs._allow_using_many_coupon_type.append(coupon_type)
        mcs._coupon_type_using_status[coupon_type] = getattr(cls, "using_status", [])
        return cls


class BaseCouponService(metaclass=_CouponMeta):
    name: str
    coupon_type: Coupon.CouponType
    coupon_detail: ModelBase
    is_auto_use: bool
    allow_using_many: bool
    using_status: List[UserCoupon.Status] = [UserCoupon.Status.ACTIVE]

    FUTURE_COUPON_TYPES = {Coupon.CouponType.EXPERIENCE_FEE, Coupon.CouponType.COPY_TRADING_EXPERIENCE_FEE}

    @classmethod
    def get_user_coupon_type_count(
            cls,
            user_id: int, *,
            status: List[UserCoupon.Status] = None
    ) -> int:
        """获取状态数量, 默认获取使用中"""
        if not status:
            status = cls.using_status
        using_count = UserCoupon.query.select_from(UserCoupon).join(Coupon, UserCoupon.coupon_id == Coupon.id).filter(
            UserCoupon.status.in_(status),
            UserCoupon.user_id == user_id,
            Coupon.coupon_type == cls.coupon_type
        ).with_entities(
            func.count("*")
        ).scalar()
        return using_count or 0

    @classmethod
    def _exchange_update_cache(cls, code_pool, pool_id, user_id):
        if code_pool.send_count >= code_pool.total_count:
            CouponPoolCache.update_one(pool_id)
        if not cls.is_auto_use:
            AvailableCouponPoolUserCache(pool_id).add_user(user_id)

    @classmethod
    def _check_user_exchange_condition(cls, user_id, code_pool):
        return UserGroupValidator(
            user_id,
            code_pool.user_group_condition
        ).validate()

    @classmethod
    def _handler_exchange(cls, coupon_pool, user_id):
        user_coupon = cls.active(coupon_pool, user_id, auto_commit=False)  # auto_commit=True时active方法会先回滚
        return user_coupon

    @classmethod
    def _dump_model_to_json(cls, m):
        exclude_fields = ["id", "created_at", "updated_at", "coupon_id"]
        m_dict = m.to_dict()
        m_json = {}
        for k, v in m_dict.items():
            if k in exclude_fields:
                continue
            if isinstance(v, Decimal):
                m_json[k] = amount_to_str(v)
            elif isinstance(v, Enum):
                m_json[k] = v.name
            elif isinstance(v, datetime):
                m_json[k] = datetime_to_time(v)
            else:
                m_json[k] = v
        if isinstance(m, InvestmentIncreaseRateCoupon):
            # 特殊字段处理
            m_json["assets"] = m_json["assets"].split(",")
        return m_json

    @classmethod
    def dump_coupon_detail_data(cls, coupon_ids: List[int]):
        query = cls.coupon_detail.query.filter(
            cls.coupon_detail.coupon_id.in_(coupon_ids)
        ).all()
        return {i.coupon_id: cls._dump_model_to_json(i) for i in query}

    @classmethod
    def exchange(cls, code_pool_id: int, user_id: int) -> Tuple[Union[UserCoupon, None], List[Dict]]:
        """
        用户兑换卡劵
        """
        with CacheLock(LockKeys.exchange_coupon(code_pool_id), wait=False):
            db.session.rollback()
            code_pool = CouponCodePool.query.get(code_pool_id)
            coupon = Coupon.query.get(code_pool.coupon_id)
            coupon_pool = CouponPool.query.filter(
                CouponPool.apply_coupon_id == code_pool.apply_coupon_id,
            ).first()
            if code_pool.send_count >= code_pool.total_count:
                raise CouponHasRunOut

            if coupon_pool.send_user_type == CouponApply.SendUserType.SOME:
                is_passed, failed_items = cls._check_user_exchange_condition(user_id, code_pool)
                if not is_passed:
                    return None, failed_items
            code_pool.send_count += 1
            user_ids = BitMap.deserialize(coupon_pool.send_user_ids) \
                if coupon_pool.send_user_ids else set()
            user_ids.add(user_id)
            coupon_pool.set_send_user_ids(set(user_ids))
            history = CouponExchangeHistory(
                user_id=user_id,
                code_pool_id=code_pool.id,
                type=coupon.coupon_type,
                asset=coupon.value_type,
                amount=coupon.value
            )
            db.session.add(history)
            db.session.flush()
            user_coupon = cls._handler_exchange(coupon_pool, user_id)
            if coupon.coupon_type == Coupon.CouponType.TRADING_GIFT:
                history.amount = user_coupon.coupon_value
            db.session.commit()
            cls._exchange_update_cache(code_pool, user_coupon.pool_id, user_id)
            cls.activated_update_task(user_id)
            message_service = get_coupon_message_server(SendTiming.EXCHANGE, coupon.coupon_type)
            message_service.send_message(coupon, user_coupon)
            return user_coupon, []

    @classmethod
    def active(cls, pool: CouponPool, user_id: int, auto_commit=True, with_lock=True) -> UserCoupon:
        """
        用户使用/激活卡劵
        卡劵状态 待领取 -> 待使用
        """
        pool_id = pool.id
        if with_lock:
            with CacheLock(LockKeys.receive_coupon(pool_id), wait=False):
                if auto_commit:
                    db.session.rollback()
                return cls._active(pool, user_id, auto_commit)
        else:
            return cls._active(pool, user_id, auto_commit)

    @classmethod
    def _active(cls, pool: CouponPool, user_id: int, auto_commit=True) -> UserCoupon:
        from app.business.coupon.utils import CouponTool

        pool_id = pool.id
        pool = CouponPool.query.get(pool_id)
        coupon = Coupon.query.get(pool.coupon_id)
        referrer_id, is_sending_end = CouponTool.check_sending_end(pool, user_id)
        if is_sending_end:
            raise CouponSendEnd
        if CouponTool.check_user_obtained_coupon(pool_id, user_id):
            raise UsingCouponObtained
        if CouponTool.check_had_using_coupon_type(coupon.coupon_type, user_id):
            raise UsingCouponLimit

        user_coupon = UserCoupon(
            user_id=user_id,
            pool_id=pool_id,
            coupon_id=pool.coupon_id,
            coupon_value=coupon.value,
            coupon_value_type=coupon.value_type
        )
        if not cls.is_auto_use and coupon.activation_days:
            user_coupon.activation_expired_at = now() + timedelta(days=coupon.activation_days)
        db.session.add(user_coupon)
        pool.send_count += 1
        db.session.flush()
        cls.active_by_coupon_type(user_coupon, pool)
        if cls.is_auto_use:
            cls.use(user_coupon, coupon)
        # 不提交只有直接发放的情况。不会出现间隙锁的问题。
        if auto_commit:
            db.session.commit()
            cls.active_update_cache(pool, user_id)
            cls.activated_update_task(user_id)
        if pool.dynamic_user_type is CouponApply.DynamicUser.REFERRAL_GIFT_MUL:
            # 提交后写缓存
            if referrer_id:
                CouponTool.update_friend_gift_mul_cache(pool_id, referrer_id)
        return user_coupon

    @classmethod
    def batch_active(cls, pool: CouponPool, user_ids: set) -> List[UserCoupon]:
        from app.business.coupon.utils import CouponTool

        pool_id = pool.id
        pool = CouponPool.query.get(pool_id)
        coupon = Coupon.query.get(pool.coupon_id)
        if pool.send_count + len(user_ids) > pool.total_count:
            raise CouponSendEnd

        obtain_users = CouponTool.check_users_obtained_coupon(pool_id, user_ids)
        had_users = CouponTool.check_users_had_using_coupon_type(coupon.coupon_type, user_ids)
        to_active_users = user_ids - obtain_users - had_users

        user_coupons = []
        for user_id in to_active_users:
            user_coupon = UserCoupon(
                user_id=user_id,
                pool_id=pool_id,
                coupon_id=pool.coupon_id,
                coupon_value=coupon.value,
                coupon_value_type=coupon.value_type
            )
            if coupon.activation_days:
                user_coupon.activation_expired_at = now() + timedelta(days=coupon.activation_days)
            user_coupons.append(user_coupon)
        db.session.add_all(user_coupons)
        pool.send_count += len(user_coupons)
        db.session.flush()

        cls.batch_active_by_coupon_type(user_coupons, pool)
        if cls.is_auto_use:
            for user_coupon in user_coupons:
                cls.use(user_coupon, coupon)

        return user_coupons

    @classmethod
    def active_by_coupon_type(cls, user_coupon: UserCoupon, pool: CouponPool):
        """各个卡券业务数据创建"""
        raise NotImplementedError

    @classmethod
    def batch_active_by_coupon_type(cls, user_coupons: List[UserCoupon], pool: CouponPool):
        """各个卡券业务数据创建"""
        raise NotImplementedError

    @classmethod
    def active_update_cache(cls, pool, user_id):
        AvailableCouponPoolUserCache(pool.id).del_user(user_id)
        PushAvailablePoolCache.delete_one(user_id)
        if pool.send_count >= pool.total_count:
            CouponPoolCache.update_one(pool.id)

    @classmethod
    def activated_update_task(cls, user_id):
        """领取成功之后，异步任务"""
        pass

    @classmethod
    def use(cls, user_coupon: UserCoupon, coupon: Coupon, *, auto_commit=False, **kwargs):
        """卡劵从待使用 -> 使用中"""
        raise NotImplementedError

    @classmethod
    def _check_risk_white(cls, user_id, limit_type, apply_id, value):
        if CouponRiskWhiteCache(limit_type, apply_id).has_value(value):
            return True
        elif CouponRiskWhiteCache(CouponRiskWhiteCache.RiskWhiteType.USER_ID.name, apply_id).has_value(user_id):
            return True
        only_withdraw_status = UserVisitPermissionCache().get_user_permission(user_id)
        if value in IpWhiteListCache().get_ip_list() or \
                only_withdraw_status == UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE:
            return True
        return False

    @classmethod
    def check_receive_coupon_risk(cls, user_id: int, pool: CouponPool, limit_type: CouponRisk.LimitType, value: str):
        if pool.dynamic_user_type not in [
            CouponApply.DynamicUser.NOVICE_PREFECTURE
        ]:
            return None
        if not value:
            return None
        cache = cls.get_risk_cache(pool, user_id, limit_type, value)
        if not cache:
            return None
        limit_count = cache.get_limit_count()
        if cache.count() >= limit_count:
            cls.insert_coupon_risk_users(pool.id, limit_type.name, value, {user_id})
            raise InvalidArgument(message=_("触发风控，无法领取"))
        return cache

    @classmethod
    def get_risk_cache(cls, pool: CouponPool, user_id, limit_type, value):
        if cls._check_risk_white(user_id, limit_type.name, pool.apply_coupon_id, value):
            return
        ttl = int(pool.get_expired_at().timestamp()) - int(now().timestamp())
        cache = CouponRiskCache(
            pool.apply_coupon_id,
            limit_type,
            value,
            interval=ttl
        )
        return cache

    @classmethod
    def receive(
            cls,
            coupon: Coupon,
            pool: CouponPool,
            user_id: int,
            device_id: str = None,
            ip: str = None
    ) -> UserCoupon:
        """用户领取"""
        ip_risk_cache = cls.check_receive_coupon_risk(
            user_id,
            pool,
            limit_type=CouponRisk.LimitType.IP,
            value=ip
        )
        device_risk_cache = cls.check_receive_coupon_risk(
            user_id,
            pool,
            limit_type=CouponRisk.LimitType.DEVICE_ID,
            value=device_id
        )
        user_coupon = cls.active(pool, user_id)
        message = get_coupon_message_server(SendTiming.USE_COUPON, coupon.coupon_type)
        message.send_message(user_coupon)
        if ip_risk_cache:
            ip_risk_cache.add_value(ip)
        if device_risk_cache:
            device_risk_cache.add_value(device_id)
        return user_coupon

    @classmethod
    def insert_coupon_risk_users(cls, pool_id: int, limit_type: str, value: str, users: Set[int]):
        pool = CouponPool.query.get(pool_id)
        if not pool:
            return
        risk_record = CouponRisk.query.filter(
            CouponRisk.coupon_apply_id == pool.apply_coupon_id,
            CouponRisk.limit_type == CouponRisk.LimitType[limit_type],
            CouponRisk.value == value
        ).first()
        if not risk_record:
            risk_record = CouponRisk(
                coupon_apply_id=pool.apply_coupon_id,
                limit_type=CouponRisk.LimitType[limit_type],
                value=value,
                risk_type=CouponRisk.RiskType.APPLY_ID
            )
            db.session.add(risk_record)
        if risk_record.user_ids:
            user_ids = set(BitMap.deserialize(risk_record.user_ids))
        else:
            user_ids = set()
        add_users = users | user_ids
        risk_record.user_ids = BitMap(add_users).serialize()
        risk_record.status = CouponRisk.Status.ACTIVE
        risk_record.risk_expired_at = pool.get_expired_at()
        db.session.commit()
        CouponRiskWhiteCache(limit_type, pool.apply_coupon_id).del_value(value)


def get_coupon_service(coupon_type: Union[str, Coupon.CouponType]) -> BaseCouponService:
    if isinstance(coupon_type, str):
        coupon_type = Coupon.CouponType[coupon_type]
    return _CouponMeta._coupon_services[coupon_type]


def get_auto_use_coupon_types():
    return _CouponMeta._auto_use_coupon_type


def get_allow_use_many_types():
    return _CouponMeta._allow_using_many_coupon_type


def get_coupon_type_using_status_mapper():
    return _CouponMeta._coupon_type_using_status


def get_all_coupon_types() -> Set[Coupon.CouponType]:
    return set(_CouponMeta._coupon_services.keys())
