from collections import defaultdict
from datetime import timed<PERSON><PERSON>, datetime
from decimal import Decimal
from typing import Dict, List

from sqlalchemy import func

from app import config
from app.business import <PERSON><PERSON><PERSON><PERSON>, <PERSON>ache<PERSON><PERSON>, VipHelper
from app.business.alert import send_alert_notice
from app.business.coupon.balance import copy_trading_experience_fee_send_task
from app.business.coupon.balance import send_coupon_balance_task
from app.business.coupon.base import BaseCouponService
from app.caches.activity import RandomCouponValueCache
from app.exceptions import (
    UsingCouponSameInvestmentAsset, UsingCouponExpired, UsingCouponLimit, UsingPerpetualSubsidyCouponLimit,
    UsingCouponStatusInvalid,
)
from app.models import VipUser
from app.models import db, AssetPrice
from app.models.activity import (
    Coupon,
    ExperienceFeeCoupon,
    ExperienceFeeUserCoupon,
    UserCoupon,
    CouponPool, TradingGiftCoupon, TradingGiftUserCoupon, InvestmentIncRateUserCoupon, InvestmentIncreaseRateCoupon,
    CouponApply,
    CouponBalanceHistory, CouponDailyBalanceHistory,
    CashBackFeeUserCoupon, CashBackFeeCoupon, CashBackUserCouponTrace, CashBackFeeTraceDetail,
    PerpetualSubsidyCoupon, PerpetualSubsidyUserCoupon, VipUpgradeCoupon, VipUpgradeUserCoupon,
    CopyTradingExperienceFeeCoupon, CopyTradingExperienceFeeUserCoupon,
)
from app.models.vip import VipUserBuff
from app.schedules.vip import update_vip_user_task
from app.utils import now, batch_iter, quantize_amount
from app.utils.date_ import convert_datetime, today_datetime


class ExperienceFeeService(BaseCouponService):
    name = "合约体验金"
    coupon_type = Coupon.CouponType.EXPERIENCE_FEE
    coupon_detail = ExperienceFeeCoupon
    is_auto_use = True
    allow_using_many = False
    using_status = [UserCoupon.Status.CREATED, UserCoupon.Status.ACTIVE, UserCoupon.Status.TO_BE_RECYCLED]

    @classmethod
    def active_by_coupon_type(cls, user_coupon: UserCoupon, pool: CouponPool) -> UserCoupon:
        ex_user_coupon = ExperienceFeeUserCoupon(
            user_coupon_id=user_coupon.id
        )
        db.session.add(ex_user_coupon)
        db.session.flush()
        return user_coupon

    @classmethod
    def batch_active_by_coupon_type(cls, user_coupons: List[UserCoupon], pool: CouponPool) -> List[UserCoupon]:
        """各个卡券业务数据创建"""
        if not user_coupons:
            return user_coupons
        new_rows = []
        for user_coupon in user_coupons:
            ex_user_coupon = ExperienceFeeUserCoupon(
                user_coupon_id=user_coupon.id
            )
            new_rows.append(ex_user_coupon)
        db.session.add_all(new_rows)
        db.session.flush()
        return user_coupons

    @classmethod
    def use(cls, user_coupon: UserCoupon, coupon: Coupon, *, auto_commit=False, **kwargs) -> UserCoupon:
        _now = now()
        user_coupon.used_at = _now
        user_coupon.usable_expired_at = _now + timedelta(days=coupon.usable_days)
        send_coupon_balance_task.delay(user_coupon.id)
        return user_coupon


class TradingGiftCouponStatus(BaseCouponService):
    name = "交易赠金券"
    coupon_type = Coupon.CouponType.TRADING_GIFT
    coupon_detail = TradingGiftCoupon
    is_auto_use = True
    allow_using_many = False
    using_status = [UserCoupon.Status.CREATED, UserCoupon.Status.ACTIVE, UserCoupon.Status.TO_BE_GIVEN]

    @classmethod
    def _get_random_coupon_value(cls, pool_id, coupon_value, send_total):
        random_cache = RandomCouponValueCache(pool_id)
        if not random_cache.exists():
            random_cache.reload(coupon_value, send_total)
        return random_cache.get_random_value()

    @classmethod
    def active_by_coupon_type(cls, user_coupon: UserCoupon, pool: CouponPool) -> UserCoupon:
        coupon = Coupon.query.get(user_coupon.coupon_id)  # type: Coupon
        coupon_detail = TradingGiftCoupon.query.filter(
            TradingGiftCoupon.coupon_id == user_coupon.coupon_id
        ).first()  # type: TradingGiftCoupon
        if coupon_detail.amount_type == TradingGiftCoupon.AmountType.RANDOM:
            user_coupon.coupon_value = cls._get_random_coupon_value(user_coupon.pool_id, coupon.value, pool.total_count)
        tg_user_coupon = TradingGiftUserCoupon(
            user_coupon_id=user_coupon.id
        )
        db.session.add(tg_user_coupon)
        db.session.flush()
        return user_coupon

    @classmethod
    def batch_active_by_coupon_type(cls, user_coupons: List[UserCoupon], pool: CouponPool) -> List[UserCoupon]:
        """各个卡券业务数据创建"""
        if not user_coupons:
            return user_coupons
        coupon = Coupon.query.get(pool.coupon_id)  # type: Coupon
        coupon_detail = TradingGiftCoupon.query.filter(
            TradingGiftCoupon.coupon_id == pool.coupon_id
        ).first()  # type: TradingGiftCoupon

        new_rows = []
        for user_coupon in user_coupons:
            if coupon_detail.amount_type == TradingGiftCoupon.AmountType.RANDOM:
                user_coupon.coupon_value = cls._get_random_coupon_value(user_coupon.pool_id, coupon.value, pool.total_count)
            tg_user_coupon = TradingGiftUserCoupon(
                user_coupon_id=user_coupon.id
            )
            new_rows.append(tg_user_coupon)
        db.session.add_all(new_rows)
        db.session.flush()
        return user_coupons

    @classmethod
    def use(cls, user_coupon: UserCoupon, coupon: Coupon, *, auto_commit=False, **kwargs):
        _now = now()
        user_coupon.used_at = _now
        user_coupon.usable_expired_at = _now + timedelta(days=coupon.usable_days)
        user_coupon.status = UserCoupon.Status.ACTIVE
        return user_coupon


class InvestmentCouponState(BaseCouponService):
    name = "理财加息券"
    coupon_type = Coupon.CouponType.INVESTMENT_INCREASE_RATE
    coupon_detail = InvestmentIncreaseRateCoupon
    is_auto_use = False
    allow_using_many = True
    using_status = [UserCoupon.Status.ACTIVE]

    @classmethod
    def active_by_coupon_type(cls, user_coupon: UserCoupon, pool: CouponPool) -> UserCoupon:
        iv_user_coupon = InvestmentIncRateUserCoupon(
            user_coupon_id=user_coupon.id,
            investment_asset="",  # 使用的时候再设置
        )
        db.session.add(iv_user_coupon)
        db.session.flush()
        return user_coupon

    @classmethod
    def batch_active_by_coupon_type(cls, user_coupons: List[UserCoupon], pool: CouponPool) -> List[UserCoupon]:
        """各个卡券业务数据创建"""
        if not user_coupons:
            return user_coupons
        new_rows = []
        for user_coupon in user_coupons:
            iv_user_coupon = InvestmentIncRateUserCoupon(
                user_coupon_id=user_coupon.id,
                investment_asset="",  # 使用的时候再设置
            )
            new_rows.append(iv_user_coupon)
        db.session.add_all(new_rows)
        db.session.flush()
        return user_coupons

    @classmethod
    def use(cls, user_coupon: UserCoupon, coupon: Coupon, *, auto_commit=False, **kwargs):
        asset = kwargs['asset']
        with CacheLock(LockKeys.use_coupon(user_coupon.id), wait=False):
            db.session.rollback()

            if cls.check_same_asset_using_coupon(user_coupon.user_id, asset):
                raise UsingCouponSameInvestmentAsset

            _now = now()
            if user_coupon.activation_expired_at < _now:
                user_coupon.status = UserCoupon.Status.EXPIRED
                db.session.commit()
                raise UsingCouponExpired

            user_coupon.used_at = _now
            # 过期时间对齐到天（+1天）
            user_coupon.usable_expired_at = convert_datetime(_now, "day") + timedelta(days=coupon.usable_days + 1)
            user_coupon.status = UserCoupon.Status.ACTIVE
            iv_user_coupon = InvestmentIncRateUserCoupon.query.filter(
                InvestmentIncRateUserCoupon.user_coupon_id == user_coupon.id,
            ).first()
            iv_user_coupon.investment_asset = asset
            if auto_commit:
                db.session.add(user_coupon)
                db.session.add(iv_user_coupon)
                db.session.commit()
        return user_coupon

    @classmethod
    def check_same_asset_using_coupon(cls, user_id: int, asset: str) -> bool:
        # 检查用户的同一币种是否已被其他券加息了
        row = (
            InvestmentIncRateUserCoupon.query.select_from(InvestmentIncRateUserCoupon)
            .join(
                UserCoupon, InvestmentIncRateUserCoupon.user_coupon_id == UserCoupon.id
            )
            .filter(
                UserCoupon.user_id == user_id,
                UserCoupon.status == UserCoupon.Status.ACTIVE,
                InvestmentIncRateUserCoupon.investment_asset == asset,
            )
            .first()
        )
        return bool(row)


class CashBackFeeCouponStatus(BaseCouponService):
    name = "手续费返现券"
    coupon_type = Coupon.CouponType.CASHBACK_FEE
    coupon_detail = CashBackFeeCoupon
    is_auto_use = True
    allow_using_many = True
    using_status = [UserCoupon.Status.ACTIVE]

    @classmethod
    def active_by_coupon_type(cls, user_coupon: UserCoupon, pool: CouponPool) -> UserCoupon:
        cf_user_coupon = CashBackFeeUserCoupon(
            user_coupon_id=user_coupon.id,
        )
        db.session.add(cf_user_coupon)
        db.session.flush()
        return user_coupon

    @classmethod
    def batch_active_by_coupon_type(cls, user_coupons: List[UserCoupon], pool: CouponPool) -> List[UserCoupon]:
        """各个卡券业务数据创建"""
        if not user_coupons:
            return user_coupons
        new_rows = []
        for user_coupon in user_coupons:
            cf_user_coupon = CashBackFeeUserCoupon(
                user_coupon_id=user_coupon.id,
            )
            new_rows.append(cf_user_coupon)
        db.session.add_all(new_rows)
        db.session.flush()
        return user_coupons

    @classmethod
    def has_real_active_user_coupon(cls, user_id: int) -> bool:
        return bool(UserCoupon.query.join(Coupon).filter(
            UserCoupon.user_id == user_id,
            Coupon.coupon_type == cls.coupon_type,
            UserCoupon.status.in_(cls.using_status),
            UserCoupon.real_used_at.isnot(None)
        ).first())

    @classmethod
    def use(cls, user_coupon: UserCoupon, coupon: Coupon, *, auto_commit=False, **kwargs) -> UserCoupon:
        with CacheLock(LockKeys.use_coupon_by_user(user_coupon.user_id), wait=True):
            _now = now()
            user_coupon.used_at = _now
            user_coupon.usable_expired_at = _now + timedelta(days=coupon.usable_days)
            user_coupon.status = UserCoupon.Status.ACTIVE
            if not cls.has_real_active_user_coupon(user_coupon.user_id):
                user_coupon.real_used_at = _now
        return user_coupon

    @classmethod
    def check_daily_cashback_status(cls, date_: datetime.date) -> bool:
        """检查某天的手续费返现是否已经发放"""
        r = UserCoupon.query.select_from(UserCoupon).join(CashBackUserCouponTrace).filter(
            CashBackUserCouponTrace.traced_date <= date_,
            UserCoupon.status == UserCoupon.Status.ACTIVE,
            UserCoupon.real_used_at.isnot(None)
        ).with_entities(
            UserCoupon.coupon_id.distinct()
        ).all()
        # 没有返现券活动以及当天奖励已发放时没有记录，返回True
        if r and (now() - today_datetime()).seconds > 3600 * 2:
            send_alert_notice(
                f"手续费返现券结算未完成，会影响到返佣, 请及时处理",
                config["ADMIN_CONTACTS"]["web_notice"],
                at="U03TQ4H652B"
            )
        return not r

    @classmethod
    def get_daily_cashback_result(
            cls,
            cashback_date: datetime.date,
            price_map: Dict[str, Decimal] = None
    ) -> Dict[str, Dict[int, Decimal]]:
        """获取某天的手续费返现金额"""
        details = CashBackFeeTraceDetail.query.join(
            CashBackUserCouponTrace
        ).filter(
            CashBackFeeTraceDetail.cashback_date == cashback_date
        ).with_entities(
            CashBackFeeTraceDetail.spot_amount,
            CashBackFeeTraceDetail.perpetual_amount,
            CashBackFeeTraceDetail.exchange_amount,
            CashBackUserCouponTrace.user_coupon_id,
            CashBackUserCouponTrace.asset
        )
        user_coupon_ids = [detail.user_coupon_id for detail in details]
        user_coupon_map = dict()
        for ids in batch_iter(user_coupon_ids, 1000):
            tmp = UserCoupon.query.filter(
                UserCoupon.id.in_(ids),
            ).with_entities(
                UserCoupon.id,
                UserCoupon.user_id
            ).all()
            user_coupon_map.update(dict(tmp))
        if not price_map:
            price_map = AssetPrice.get_close_price_map(cashback_date)
        result = defaultdict(lambda: defaultdict(Decimal))
        for detail in details:
            user_id = user_coupon_map[detail.user_coupon_id]
            asset = detail.asset
            result['spot'][user_id] += detail.spot_amount * price_map.get(asset, 0)
            result['perpetual'][user_id] += detail.perpetual_amount * price_map.get(asset, 0)
            result['exchange'][user_id] += detail.exchange_amount * price_map.get(asset, 0)
        for r in result.values():
            for k, v in r.items():
                r[k] = quantize_amount(v, 8)
        return result


class PerpetualSubsidyCouponStatus(BaseCouponService):
    name = "合约补贴金"
    coupon_type = Coupon.CouponType.PERPETUAL_SUBSIDY
    coupon_detail = PerpetualSubsidyCoupon
    is_auto_use = False
    allow_using_many = False
    using_status = [UserCoupon.Status.ACTIVE]

    @classmethod
    def active_by_coupon_type(cls, user_coupon: UserCoupon, pool: CouponPool) -> UserCoupon:
        pps_user_coupon = PerpetualSubsidyUserCoupon(user_coupon_id=user_coupon.id)
        db.session.add(pps_user_coupon)
        db.session.flush()
        return user_coupon

    @classmethod
    def batch_active_by_coupon_type(cls, user_coupons: List[UserCoupon], pool: CouponPool) -> List[UserCoupon]:
        """各个卡券业务数据创建"""
        if not user_coupons:
            return user_coupons
        new_rows = []
        for user_coupon in user_coupons:
            pps_user_coupon = PerpetualSubsidyUserCoupon(user_coupon_id=user_coupon.id)
            new_rows.append(pps_user_coupon)
        db.session.add_all(new_rows)
        db.session.flush()
        return user_coupons

    @classmethod
    def _check_using_coupon(cls, coupon_type: Coupon.CouponType, user_id: int) -> bool:
        return bool(UserCoupon.query.select_from(UserCoupon).join(Coupon, UserCoupon.coupon_id == Coupon.id).filter(
            UserCoupon.status == UserCoupon.Status.ACTIVE,
            UserCoupon.user_id == user_id,
            Coupon.coupon_type == coupon_type,
        ).first())

    @classmethod
    def use(cls, user_coupon: UserCoupon, coupon: Coupon, *, auto_commit=False, **kwargs) -> UserCoupon:
        with CacheLock(LockKeys.use_coupon(user_coupon.id), wait=False):
            db.session.rollback()
            if cls._check_using_coupon(coupon.coupon_type, user_coupon.user_id):
                raise UsingPerpetualSubsidyCouponLimit

            _now = now()
            user_coupon.used_at = _now
            user_coupon.usable_expired_at = _now + timedelta(days=coupon.usable_days)
            user_coupon.status = UserCoupon.Status.ACTIVE
            if auto_commit:
                db.session.add(user_coupon)
                db.session.commit()

        return user_coupon

    @classmethod
    def _query_coupon_balance_history(cls, apply: CouponApply, business_type=CouponBalanceHistory.BusinessType.SEND):
        pool: CouponPool = CouponPool.query.filter(
            CouponPool.apply_coupon_id == apply.id,
        ).first()
        user_coupon_ids = {
            i.id for i in UserCoupon.query.filter(
                UserCoupon.pool_id == pool.id
            ).with_entities(
                UserCoupon.id
            ).all()
        }
        histories = CouponDailyBalanceHistory.query.filter(
            CouponDailyBalanceHistory.user_coupon_id.in_(user_coupon_ids),
            CouponDailyBalanceHistory.coupon_type == cls.coupon_type,
            CouponDailyBalanceHistory.status == CouponDailyBalanceHistory.Status.FINISHED
        ).group_by(
            CouponDailyBalanceHistory.asset
        ).with_entities(
            CouponDailyBalanceHistory.asset,
            func.sum(CouponDailyBalanceHistory.amount)
        ).all()
        return histories


class VipUpgradeCouponStatus(BaseCouponService):
    name = "vip升级券"
    coupon_type = Coupon.CouponType.VIP_UPGRADE
    coupon_detail = VipUpgradeCoupon
    is_auto_use = True
    allow_using_many = True
    using_status = [UserCoupon.Status.ACTIVE]

    @classmethod
    def check_active_restrictions(cls, user_id: int, coupon: Coupon):
        pass

    @classmethod
    def active_by_coupon_type(cls, user_coupon: UserCoupon, pool: CouponPool) -> UserCoupon:
        vip_user = VipUser.query.filter(
            VipUser.user_id == user_coupon.user_id,
            VipUser.status == VipUser.StatusType.PASS
        ).first()
        vip_user_coupon = VipUpgradeUserCoupon(
            user_coupon_id=user_coupon.id,
            current_level=min(vip_user.level + user_coupon.coupon_value,
                              VipHelper.MAX_LEVEL) if vip_user else user_coupon.coupon_value,
            origin_level=vip_user.level if vip_user else 0
        )
        db.session.add(vip_user_coupon)
        db.session.flush()
        return user_coupon

    @classmethod
    def batch_active_by_coupon_type(cls, user_coupons: List[UserCoupon], pool: CouponPool) -> List[UserCoupon]:
        """各个卡券业务数据创建"""
        if not user_coupons:
            return user_coupons
        vip_user_mapper = {v.user_id: v for v in VipUser.query.filter(
            VipUser.user_id.in_({i.user_id for i in user_coupons}),
            VipUser.status == VipUser.StatusType.PASS
        ).all()}
        new_rows = []
        for user_coupon in user_coupons:
            vip_user = vip_user_mapper.get(user_coupon.user_id)
            vip_user_coupon = VipUpgradeUserCoupon(
                user_coupon_id=user_coupon.id,
                current_level=min(vip_user.level + user_coupon.coupon_value,
                                  VipHelper.MAX_LEVEL) if vip_user else user_coupon.coupon_value,
                origin_level=vip_user.level if vip_user else 0
            )
            new_rows.append(vip_user_coupon)
        db.session.add_all(new_rows)
        db.session.flush()
        return user_coupons

    @classmethod
    def upgrade_vip_buff(cls, user_coupon: UserCoupon, coupon_vip_level: int):
        db.session.add(VipUserBuff(
            user_id=user_coupon.user_id,
            buff_type=VipUserBuff.BuffType.UPGRADE,
            value=coupon_vip_level,
            expire_at=user_coupon.usable_expired_at
        ))

    @classmethod
    def activated_update_task(cls, user_id):
        vip_user = VipUser.query.filter(
            VipUser.user_id == user_id,
            VipUser.status == VipUser.StatusType.PASS
        ).first()
        update_vip_user_task.delay(
            user_id=user_id,
            report_date_str=now().date().strftime("%Y-%m-%d"),
            old_level=vip_user.level if vip_user else 0,
            check_level=vip_user.real_level if vip_user else 0,
            lock_level=vip_user.lock_level if vip_user else 0,
        )

    @classmethod
    def use(cls, user_coupon: UserCoupon, coupon: Coupon, *, auto_commit=False, **kwargs) -> UserCoupon:
        _now = now()
        user_coupon.used_at = _now
        user_coupon.usable_expired_at = _now + timedelta(days=coupon.usable_days)
        user_coupon.status = UserCoupon.Status.ACTIVE
        cls.upgrade_vip_buff(user_coupon, coupon.value)
        return user_coupon


class CopyTradingExperienceFeeService(BaseCouponService):
    name = "合约跟单体验金"
    coupon_type = Coupon.CouponType.COPY_TRADING_EXPERIENCE_FEE
    coupon_detail = CopyTradingExperienceFeeCoupon
    is_auto_use = False
    allow_using_many = True
    using_status = [UserCoupon.Status.ACTIVE, UserCoupon.Status.TO_BE_RECYCLED]

    @classmethod
    def active_by_coupon_type(cls, user_coupon: UserCoupon, pool: CouponPool) -> UserCoupon:
        detail_cp: CopyTradingExperienceFeeCoupon = CopyTradingExperienceFeeCoupon.query.filter(
            CopyTradingExperienceFeeCoupon.coupon_id == user_coupon.coupon_id,
        ).first()
        ex_user_coupon = CopyTradingExperienceFeeUserCoupon(
            user_coupon_id=user_coupon.id,
            history_id=None,  # 使用的时候再设置
            sub_user_id=None,  # 使用的时候再设置
            trade_type=detail_cp.trade_type,
        )
        db.session.add(ex_user_coupon)
        db.session.flush()
        return user_coupon

    @classmethod
    def batch_active_by_coupon_type(cls, user_coupons: List[UserCoupon], pool: CouponPool) -> List[UserCoupon]:
        """各个卡券业务数据创建"""
        if not user_coupons:
            return user_coupons
        detail_cp: CopyTradingExperienceFeeCoupon = CopyTradingExperienceFeeCoupon.query.filter(
            CopyTradingExperienceFeeCoupon.coupon_id == pool.coupon_id,
        ).first()
        new_rows = []
        for user_coupon in user_coupons:
            ex_user_coupon = CopyTradingExperienceFeeUserCoupon(
                user_coupon_id=user_coupon.id,
                history_id=None,
                sub_user_id=None,
                trade_type=detail_cp.trade_type,
            )
            new_rows.append(ex_user_coupon)
        db.session.add_all(new_rows)
        db.session.flush()
        return user_coupons

    @classmethod
    def use(cls, user_coupon: UserCoupon, coupon: Coupon, *, auto_commit=False, **kwargs) -> UserCoupon:
        with CacheLock(LockKeys.use_coupon(user_coupon.id), wait=False):
            db.session.rollback()

            sub_user_id = kwargs['sub_user_id']
            history_id = kwargs['history_id']
            if cls.check_sub_using_coupon(user_coupon.user_id, sub_user_id):
                raise UsingCouponLimit
            if user_coupon.status != UserCoupon.Status.CREATED or user_coupon.used_at:
                raise UsingCouponStatusInvalid

            _now = now()
            user_coupon.used_at = _now
            user_coupon.usable_expired_at = _now + timedelta(days=coupon.usable_days)

            cp_user_coupon: CopyTradingExperienceFeeUserCoupon = CopyTradingExperienceFeeUserCoupon.query.filter(
                CopyTradingExperienceFeeUserCoupon.user_coupon_id == user_coupon.id,
            ).first()
            cp_user_coupon.history_id = history_id
            cp_user_coupon.sub_user_id = sub_user_id

            if auto_commit:
                db.session.add(user_coupon)
                db.session.add(cp_user_coupon)
                db.session.commit()

            copy_trading_experience_fee_send_task.delay(user_coupon.id)
        return user_coupon

    @classmethod
    def check_sub_using_coupon(cls, user_id: int, sub_user_id: int) -> bool:
        """ 检查用户的带单｜跟单子帐号是否已有使用中的卡券；带单｜跟单子帐号是独立的，只查sub_id """
        row = cls.query_sub_using_coupon(user_id, sub_user_id)
        return bool(row)

    @classmethod
    def query_sub_using_coupon(cls, user_id, sub_user_id):
        row = CopyTradingExperienceFeeUserCoupon.query.select_from(CopyTradingExperienceFeeUserCoupon).join(
            UserCoupon,
            CopyTradingExperienceFeeUserCoupon.user_coupon_id == UserCoupon.id,
        ).filter(
            UserCoupon.user_id == user_id,
            UserCoupon.status.in_(cls.using_status),
            CopyTradingExperienceFeeUserCoupon.sub_user_id == sub_user_id,
        ).first()
        return row

    @classmethod
    def query_sub_all_using_coupon(cls, user_id, sub_user_id) -> list[CopyTradingExperienceFeeUserCoupon]:
        rows = CopyTradingExperienceFeeUserCoupon.query.select_from(CopyTradingExperienceFeeUserCoupon).join(
            UserCoupon,
            CopyTradingExperienceFeeUserCoupon.user_coupon_id == UserCoupon.id,
        ).filter(
            UserCoupon.user_id == user_id,
            UserCoupon.status.in_(cls.using_status),
            CopyTradingExperienceFeeUserCoupon.sub_user_id == sub_user_id,
        ).all()
        return rows

    @classmethod
    def get_sub_using_coupon_balance(cls, user_id: int, sub_user_id: int, asset: str = 'USDT') -> Decimal:
        """ 获取用户的带单｜跟单子帐号 使用中的卡券面额，这部分不允许转出 """
        using_coupon_balance = CopyTradingExperienceFeeUserCoupon.query.select_from(
            CopyTradingExperienceFeeUserCoupon,
        ).join(
            UserCoupon,
            CopyTradingExperienceFeeUserCoupon.user_coupon_id == UserCoupon.id,
        ).filter(
            UserCoupon.user_id == user_id,
            UserCoupon.status.in_(cls.using_status),
            UserCoupon.coupon_value_type == asset,
            CopyTradingExperienceFeeUserCoupon.sub_user_id == sub_user_id,
        ).with_entities(
            func.sum(UserCoupon.coupon_value)
        ).scalar() or Decimal()
        return using_coupon_balance

    @classmethod
    def get_his_sub_coupon_detail(cls, history_id: int, sub_user_id: int) -> CopyTradingExperienceFeeUserCoupon:
        cp_user_coupon: CopyTradingExperienceFeeUserCoupon = CopyTradingExperienceFeeUserCoupon.query.filter(
            CopyTradingExperienceFeeUserCoupon.history_id == history_id,
            CopyTradingExperienceFeeUserCoupon.sub_user_id == sub_user_id,
        ).first()
        return cp_user_coupon
