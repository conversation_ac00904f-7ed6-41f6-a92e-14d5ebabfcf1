from collections import defaultdict
from datetime import timed<PERSON><PERSON>, datetime
from decimal import Decimal
from typing import Dict, Union, List

from flask import current_app
from sqlalchemy import or_, func

from app.business import TradeSummaryDB, PerpetualSummaryDB, PerpetualHistoryDB, lock_call
from app.business.broker import get_broker_id_by_client_id
from app.business.coupon.balance import recycle_coupon_balance_task, trade_gift_given_balance_task, \
    cashback_coupon_send_fee_task, perpetual_subsidy_send_task, copy_trading_experience_fee_recycle_task
from app.business.coupon.base import get_coupon_service
from app.business.coupon.utils import CouponTool
from app.caches import AmmMarketCache, PerpetualMarketCache, PerpetualOfflineMarketCache
from app.common import TradeBusinessType, CeleryQueues, PrecisionEnum
from app.models import ModelBase, db, InvestmentBalanceHistory, AssetPrice, SubAccount
from app.models.activity import (
    ExperienceFeeCoupon,
    Coupon,
    UserCoupon,
    UserCouponTrace, ExperienceFeeUserCoupon, TradingGiftUserCouponTrace, TradingGiftUserCoupon,
    InvestmentIncRateUserCoupon, InvestmentIncRateUserCouponTrace, CouponPool, InvestmentIncreaseRateCoupon,
    CouponDailyBalanceHistory, TradingGiftCoupon, CashBackUserCouponTrace, CashBackFeeUserCoupon, CashBackFeeCoupon,
    CashBackFeeTraceDetail, PerpetualSubsidyCoupon, PerpetualSubsidyUserCoupon,
    PerpetualSubsidyUserCouponTrace, PerpetualSubsidyTraceDetail,
    CopyTradingExperienceFeeCoupon, CopyTradingExperienceFeeUserCoupon, CopyTradingExperienceFeeUserCouponTrace
)
from app.models.broker import Broker
from app.models.exchange import AssetExchangeOrder, SysAssetExchangeOrder, ExchangeFeeType
from app.models.copy_trading import CopyTraderHistory, CopyFollowerHistory
from app.utils import now, batch_iter, timestamp_to_datetime, today, quantize_amount, celery_task
from app.utils.date_ import date_to_datetime


class _CouponTraceMeta(type):
    _coupon_trace = {}

    def __new__(mcs, name, bases, dct):
        cls = super().__new__(mcs, name, bases, dct)
        coupon_type = getattr(cls, 'coupon_type', None)
        if coupon_type is None:  # base class
            return cls
        mcs._coupon_trace[coupon_type] = cls
        return cls


class BaseCouponTrace(metaclass=_CouponTraceMeta):
    coupon_type: Coupon.CouponType
    user_coupon_detail: ModelBase
    user_trace: ModelBase

    def __init__(self, coupon_id):
        self._coupon = Coupon.query.get(coupon_id)
        self._coupon_service = get_coupon_service(self.coupon_type)
        self._now = now()
        self._today = today()

    @classmethod
    def _get_trace_users(cls, user_id):
        sub_users = SubAccount.query.filter(
            SubAccount.main_user_id == user_id,
            SubAccount.type == SubAccount.Type.STRATEGY,
            SubAccount.status == SubAccount.Status.VALID
        ).with_entities(
            SubAccount.user_id
        ).all()
        sub_user_ids = [i for i, in sub_users]
        return [user_id] + sub_user_ids

    @classmethod
    def _get_trace_sub_user_mapper(cls, user_ids: list[int]):
        mapper = {}
        for ids_ in batch_iter(user_ids, 1000):
            mapper.update({sid: mid for sid, mid in SubAccount.query.filter(
                SubAccount.main_user_id.in_(ids_),
                SubAccount.type == SubAccount.Type.STRATEGY,
                SubAccount.status == SubAccount.Status.VALID
            ).with_entities(
                SubAccount.user_id,
                SubAccount.main_user_id
            ).all()})
        return mapper

    def _get_trace_user_coupons(self, coupon_id) -> Dict[int, UserCoupon]:
        """查询需要追踪的user_coupon"""
        query = UserCoupon.query.filter(
            UserCoupon.coupon_id == coupon_id,
            UserCoupon.status.in_(self._coupon_service.using_status)
        ).all()
        user_coupon_mapper = {item.user_id: item for item in query}
        return user_coupon_mapper

    def _handler_expiration(self, user_coupon: UserCoupon, trace_data: ModelBase):
        """追踪过过程中的过期操作 需要在 _filter_and_update_trace_data 之后执行"""
        raise NotImplementedError

    def _create_trace_data(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: ModelBase,
            coupon_detail: ModelBase
    ) -> ModelBase:
        """创建追踪数据"""
        raise NotImplementedError

    def _filter_and_update_trace_data(self, user_coupon: UserCoupon, trace_data: ModelBase) -> Union[ModelBase, None]:
        """执行追踪查询并且修改追踪数据, None 为异常情况终止追踪"""
        raise NotImplementedError

    def _traced_user_coupon_operating(self, user_coupon: UserCoupon):
        """追踪后续的操作"""
        pass

    def _finally_update_cache(self):
        """追踪后刷新缓存"""
        raise NotImplementedError

    def _update_user_coupon_and_details(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: ModelBase,
            trace_data: ModelBase,
            *,
            coupon_detail: ModelBase
    ):
        """追踪数据回写"""
        raise NotImplementedError

    def trace(self):
        """追踪过程"""
        coupon_id = self._coupon.id
        coupon_detail = self._coupon_service.coupon_detail.query.filter(
            self._coupon_service.coupon_detail.coupon_id == coupon_id
        ).first()
        if not coupon_detail:
            return
        user_coupon_mapper = self._get_trace_user_coupons(coupon_id)
        using_coupon_ids = [user_coupon.id for user_coupon in user_coupon_mapper.values()]
        trace_query, user_coupon_details = [], []
        for ids in batch_iter(using_coupon_ids, 1000):
            trace_query += self.user_trace.query.filter(
                self.user_trace.user_coupon_id.in_(ids)
            ).all()
            user_coupon_details += self.user_coupon_detail.query.filter(
                self.user_coupon_detail.user_coupon_id.in_(ids)
            ).all()
        user_trace_data_mapper = {trace.user_coupon_id: trace for trace in trace_query}
        user_coupon_detail_mapper = {detail.user_coupon_id: detail for detail in user_coupon_details}
        for user_id, user_coupon in user_coupon_mapper.items():
            user_coupon_detail = user_coupon_detail_mapper[user_coupon.id]
            if not self._coupon_service.allow_using_many and \
                    CouponTool.get_same_coupon_type_count(self._coupon.coupon_type, user_id) > 1:
                current_app.logger.error(f"user had many same coupon type coupon, user_id :{user_id}")
                continue
            trace_data = user_trace_data_mapper.get(user_coupon.id)
            if not trace_data:
                trace_data = self._create_trace_data(user_coupon, user_coupon_detail, coupon_detail)
                db.session.add(trace_data)
                db.session.flush()
            trace_data = self._filter_and_update_trace_data(user_coupon, trace_data)
            if not trace_data:
                continue
            self._update_user_coupon_and_details(
                user_coupon,
                user_coupon_detail,
                trace_data,
                coupon_detail=coupon_detail
            )
            # 理财加息 usable_expired_at 是延时
            if (user_coupon.used_at + timedelta(days=self._coupon.usable_days) < self._now or
                user_coupon.usable_expired_at < self._now) and user_coupon.status == UserCoupon.Status.ACTIVE:
                self._handler_expiration(user_coupon, trace_data)
            db.session.commit()
            self._traced_user_coupon_operating(user_coupon)
        self._finally_update_cache()


class ExperienceFeeCouponTrace(BaseCouponTrace):
    coupon_type = Coupon.CouponType.EXPERIENCE_FEE
    user_coupon_detail = ExperienceFeeUserCoupon
    user_trace = UserCouponTrace

    def _handler_expiration(self, user_coupon: UserCoupon, trace_data: UserCouponTrace):
        user_coupon.status = UserCoupon.Status.TO_BE_RECYCLED

    def _create_trace_data(self, user_coupon: UserCoupon, user_coupon_detail: ExperienceFeeUserCoupon,
                           coupon_detail: ExperienceFeeCoupon):
        trace_data = UserCouponTrace(
            user_coupon_id=user_coupon.id,
            trade_amount=Decimal(),
            profit_real=Decimal(),
            asset=user_coupon.coupon_value_type,
            traced_at=user_coupon.used_at,
            profit_at=user_coupon.used_at
        )
        return trace_data

    def _filter_and_update_trace_data(self, user_coupon: UserCoupon, trace_data: UserCouponTrace):
        user_id = user_coupon.user_id
        end_at = user_coupon.usable_expired_at
        max_trade_time, trade_amount, deal_profit = CouponTool.get_user_trade_amount(
            user_id=user_coupon.user_id,
            start_at=trace_data.traced_at,
            end_at=end_at,
            asset=user_coupon.coupon_value_type
        )
        # 资金费率
        max_funding_time, funding_amount = CouponTool.get_user_funding(
            user_id=user_id,
            start_at=trace_data.profit_at,
            end_at=end_at,
            asset=user_coupon.coupon_value_type
        )
        trace_data.trade_amount += trade_amount
        trace_data.profit_real += (funding_amount + deal_profit)
        trace_data.traced_at = timestamp_to_datetime(max_trade_time)
        trace_data.profit_at = timestamp_to_datetime(max_funding_time)
        return trace_data

    def _update_user_coupon_and_details(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: ExperienceFeeUserCoupon,
            trace_data: UserCouponTrace,
            *,
            coupon_detail: ExperienceFeeCoupon
    ):
        # 卡劵完成进度条
        user_coupon_detail.user_trade_amount = trace_data.trade_amount
        # 累计交易额 > 卡劵面额 (已达标)
        if trace_data.trade_amount >= coupon_detail.qualified_trade_amount:
            user_coupon.status = UserCoupon.Status.USED
            user_coupon_detail.user_trade_amount = trace_data.trade_amount
            user_coupon_detail.profit_real = trace_data.profit_real
            user_coupon_detail.reason = ExperienceFeeUserCoupon.RecycledReason.QUALIFIED
            db.session.commit()
            return
        # 累计已实现盈亏 < -体验金 (全部亏损)
        if trace_data.profit_real <= -user_coupon.coupon_value:
            user_coupon.status = UserCoupon.Status.USED
            user_coupon_detail.user_trade_amount = trace_data.trade_amount
            user_coupon_detail.profit_real = trace_data.profit_real
            user_coupon_detail.reason = ExperienceFeeUserCoupon.RecycledReason.DEFICIT
            db.session.commit()
            return

    def _finally_update_cache(self):
        CouponTool.clear_cache()
        recycle_coupon_balance_task.delay()


class TradingGiftCouponTrace(BaseCouponTrace):
    coupon_type = Coupon.CouponType.TRADING_GIFT
    user_coupon_detail = TradingGiftUserCoupon
    user_trace = TradingGiftUserCouponTrace

    def _create_trace_data(self, user_coupon: UserCoupon, user_coupon_detail: TradingGiftUserCoupon,
                           coupon_detail: TradingGiftCoupon) -> ModelBase:
        trace_data = TradingGiftUserCouponTrace(
            user_coupon_id=user_coupon.id,
            trade_type=coupon_detail.trade_type,
            traced_at=user_coupon.used_at,
            exchanged_at=user_coupon.used_at,
            trade_amount=Decimal(),
            trade_fee_usd=Decimal(),
            asset=user_coupon.coupon_value_type
        )
        return trace_data

    @classmethod
    def _get_full_user_spot_trade_data(cls, user_id, start_at, end_at, asset):
        user_ids = cls._get_trace_users(user_id)
        time_list = []
        total_trade_volume, total_fee_usd = Decimal(), Decimal()
        for uid in user_ids:
            max_time, spot_trade_volume, spot_fee_usd = CouponTool.get_spot_trade_amount(
                uid,
                start_at,
                end_at,
                asset
            )
            total_trade_volume += spot_trade_volume
            total_fee_usd += spot_fee_usd
            time_list.append(max_time)
        return max(time_list), total_trade_volume, total_fee_usd

    @classmethod
    def _filter_spot_trace_data(cls, user_id, end_at, coupon_value_type, trace_data):
        max_time, spot_trade_volume, spot_fee_usd = cls._get_full_user_spot_trade_data(
            user_id,
            trace_data.traced_at,
            end_at,
            coupon_value_type
        )
        # 兑换
        max_exchange_time, exchange_trade_volume, exchange_fee_usd = CouponTool.get_exchange_order_amount(
            user_id,
            trace_data.exchanged_at,
            end_at,
            coupon_value_type
        )
        trade_volume = spot_trade_volume + exchange_trade_volume
        fee_usd = spot_fee_usd + exchange_fee_usd
        trace_data.exchanged_at = max_exchange_time
        return max_time, trade_volume, fee_usd

    @classmethod
    def _filter_perpetual_trace_data(cls, user_id, end_at, coupon_value_type, trace_data):
        return CouponTool.get_perpetual_trade_amount(
            user_id,
            trace_data.traced_at,
            end_at,
            coupon_value_type,
        )

    def _filter_and_update_trace_data(self, user_coupon: UserCoupon, trace_data: TradingGiftUserCouponTrace):
        user_id = user_coupon.user_id
        end_at = user_coupon.usable_expired_at
        filter_func = getattr(self, f'_filter_{trace_data.trade_type.value}_trace_data')
        max_time, trade_volume, fee_usd = filter_func(user_id, end_at, user_coupon.coupon_value_type, trace_data)
        trace_data.traced_at = timestamp_to_datetime(max_time)
        trace_data.trade_fee_usd += fee_usd
        trace_data.trade_amount += trade_volume
        return trace_data

    def _handler_expiration(self, user_coupon: UserCoupon, trace_data: TradingGiftUserCouponTrace):
        user_coupon.status = UserCoupon.Status.EXPIRED

    def _update_user_coupon_and_details(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: ModelBase,
            trace_data: ModelBase,
            *,
            coupon_detail: ModelBase
    ):
        # 前端页面的数据展示
        user_coupon_detail.user_trade_amount = trace_data.trade_amount
        user_coupon_detail.user_trade_fee = trace_data.trade_fee_usd
        if trace_data.trade_amount >= coupon_detail.qualified_trade_amount:
            user_coupon.status = UserCoupon.Status.TO_BE_GIVEN
            db.session.commit()

    def _traced_user_coupon_operating(self, user_coupon):
        trade_gift_given_balance_task.delay()

    def _finally_update_cache(self):
        CouponTool.clear_cache()


class InvestmentIncreaseCouponTrace(BaseCouponTrace):
    coupon_type = Coupon.CouponType.INVESTMENT_INCREASE_RATE
    user_coupon_detail = InvestmentIncRateUserCoupon
    user_trace = InvestmentIncRateUserCouponTrace

    def _get_trace_user_coupons(self, coupon_id) -> Dict[int, UserCoupon]:
        dt = today() - timedelta(days=31)
        pool_ids = {
            id_ for id_, in CouponPool.query.filter(
                or_(CouponPool.created_at >= dt, CouponPool.expired_at >= dt),
                CouponPool.coupon_id == coupon_id,
            ).with_entities(
                CouponPool.id
            ).all()
        }
        return {
            item.user_id: item for item in UserCoupon.query.filter(
                UserCoupon.coupon_id == coupon_id,
                UserCoupon.status == UserCoupon.Status.ACTIVE,
                UserCoupon.pool_id.in_(pool_ids),
            ).all()
        }

    @classmethod
    def _get_first_investment_time(cls, _user_id):
        # 首次理财时间
        _first_inv = InvestmentBalanceHistory.query.filter(
            InvestmentBalanceHistory.user_id == _user_id,
            InvestmentBalanceHistory.opt_type == InvestmentBalanceHistory.OptType.IN,
            InvestmentBalanceHistory.status == InvestmentBalanceHistory.StatusType.SUCCESS,
        ).order_by(InvestmentBalanceHistory.id.asc()).first()
        if _first_inv:
            return _first_inv.created_at

    @classmethod
    def _get_transfer_in_investment_asset_amount(cls, _user_id, _asset, _start_time, _end_time):
        # 某段时间的转入
        _transfer_in_rows = (
            InvestmentBalanceHistory.query.filter(
                InvestmentBalanceHistory.user_id == _user_id,
                InvestmentBalanceHistory.asset == _asset,
                InvestmentBalanceHistory.opt_type == InvestmentBalanceHistory.OptType.IN,
                InvestmentBalanceHistory.status == InvestmentBalanceHistory.StatusType.SUCCESS,
                InvestmentBalanceHistory.success_at >= _start_time,
                InvestmentBalanceHistory.success_at <= _end_time,
            )
            .with_entities(InvestmentBalanceHistory.amount)
            .all()
        )
        return sum([i.amount for i in _transfer_in_rows])

    @classmethod
    def _get_last_investment_time(cls, _user_id, _end):
        # 最近一次理财时间
        _last_inv = InvestmentBalanceHistory.query.filter(
            InvestmentBalanceHistory.user_id == _user_id,
            InvestmentBalanceHistory.opt_type == InvestmentBalanceHistory.OptType.IN,
            InvestmentBalanceHistory.status == InvestmentBalanceHistory.StatusType.SUCCESS,
            InvestmentBalanceHistory.created_at <= _end,
        ).order_by(InvestmentBalanceHistory.id.desc()).first()
        if _last_inv:
            return _last_inv.created_at

    @classmethod
    def _get_inc_interest_rows(cls, _user_id, _user_coupon_id):
        # 加息总收益
        _inc_rows = CouponDailyBalanceHistory.query.filter(
            CouponDailyBalanceHistory.user_id == _user_id,
            CouponDailyBalanceHistory.user_coupon_id == _user_coupon_id,
            CouponDailyBalanceHistory.status == CouponDailyBalanceHistory.Status.FINISHED,
        ).all()
        return _inc_rows

    @classmethod
    def _get_last_inc_interest_date(cls):
        # 最新加息收益发放时间
        row = CouponDailyBalanceHistory.query.order_by(CouponDailyBalanceHistory.date.desc()).first()
        if row:
            return row.date

    def _create_trace_data(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: InvestmentIncRateUserCoupon,
            coupon_detail: InvestmentIncreaseRateCoupon
    ) -> InvestmentIncRateUserCouponTrace:
        trace_data = InvestmentIncRateUserCouponTrace(
            user_coupon_id=user_coupon.id,
            traced_at=now(),
            investment_asset=user_coupon_detail.investment_asset,
            increase_income_amount=Decimal(),
            transfer_in_investment_amount=Decimal(),
            first_investment_at=self._get_first_investment_time(user_coupon.user_id),
        )
        db.session.add(trace_data)
        return trace_data

    def _handler_expiration(self, user_coupon: UserCoupon, trace_data: InvestmentIncRateUserCouponTrace):
        _user_last_inc_date = user_coupon.usable_expired_at.date() - timedelta(days=1)  # 用户最后加息日
        _last_inc_date = self._get_last_inc_interest_date()  # 最新加息流水的时间
        if _last_inc_date == _user_last_inc_date:
            # 确保最后一天加息已发放，并及时更新前端展示状态（注：改成USED状态，会影响到加息流程）
            user_coupon.status = UserCoupon.Status.USED

    def _filter_and_update_trace_data(self, user_coupon: UserCoupon, trace_data: InvestmentIncRateUserCouponTrace):
        user_id = user_coupon.user_id
        now_ = now()
        trace_data.transfer_in_investment_amount = self._get_transfer_in_investment_asset_amount(
            user_id,
            trace_data.investment_asset,
            user_coupon.used_at,
            user_coupon.usable_expired_at,
        )
        inc_interest_rows = self._get_inc_interest_rows(user_id, user_coupon.id)
        total_income = sum([i.amount for i in inc_interest_rows])
        trace_data.increase_income_amount = total_income
        if not trace_data.first_investment_at:
            # 使用卡券时 可能还没理过财
            trace_data.first_investment_at = self._get_first_investment_time(user_id)

        day_31_ago = today() - timedelta(days=31)
        if user_coupon.created_at.date() >= day_31_ago:
            # 超过30天不再更新
            trace_data.last_investment_at = self._get_last_investment_time(user_id, now_)
        trace_data.traced_at = now_
        return trace_data

    def _update_user_coupon_and_details(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: InvestmentIncRateUserCoupon,
            trace_data: InvestmentIncRateUserCouponTrace,
            *,
            coupon_detail: InvestmentIncreaseRateCoupon
    ):
        inc_interest_rows = self._get_inc_interest_rows(user_coupon.user_id, user_coupon.id)
        user_coupon_detail.actual_increase_income_days = len(inc_interest_rows)
        user_coupon_detail.increase_income_amount = trace_data.increase_income_amount
        if user_coupon.usable_expired_at.date() <= today():
            user_coupon.status = UserCoupon.Status.USED

    def _finally_update_cache(self):
        # FIXME
        CouponTool.clear_cache()


class CashBackFeeCouponTrace(BaseCouponTrace):
    coupon_type = Coupon.CouponType.CASHBACK_FEE
    user_coupon_detail = CashBackFeeUserCoupon
    user_trace = CashBackUserCouponTrace

    @classmethod
    def _batch_trade_fee_amount(
            cls,
            main_user_ids: list[int],
            current_date: datetime.date,
            target_asset: str,
            trade_type: str
    ) -> dict[int, Decimal]:
        enum_trade_type = TradeBusinessType[trade_type]
        trace_users = main_user_ids
        sub_user_mapper = {}
        if enum_trade_type == TradeBusinessType.SPOT:
            _db = TradeSummaryDB
            # 需要统计策略子账号
            sub_user_mapper = cls._get_trace_sub_user_mapper(main_user_ids)
            trace_users += sub_user_mapper.keys()
        else:
            _db = PerpetualSummaryDB
        amm_market = AmmMarketCache.list_amm_markets()
        # 使用次日0点汇率 = 当前的收盘价
        price_map = AssetPrice.get_close_price_map(current_date)
        date_str = current_date.strftime('%Y-%m-%d')
        month_str = current_date.strftime('%Y%m')
        user_trade_amount = defaultdict(Decimal)
        for ids_ in batch_iter(trace_users, 1000):
            user_id_str = ','.join(map(str, ids_))
            records = _db.table(f'user_fee_summary_{month_str}').select(
                "market",
                "SUM(`fee`)",
                "asset",
                "user_id",
                where=f"`user_id` in ({user_id_str}) and `trade_date` = '{date_str}'",
                group_by='market, asset, user_id'
            )
            for market, fee_amount, asset, user_id in records:
                main_user_id = sub_user_mapper.get(user_id, user_id)
                if enum_trade_type == TradeBusinessType.SPOT and market in amm_market:
                    continue
                asset_rate = price_map.get(asset)
                target_rate = price_map.get(target_asset)
                if not (asset_rate and target_rate):
                    continue
                user_trade_amount[main_user_id] += fee_amount * asset_rate / target_rate
        return user_trade_amount

    @classmethod
    def _batch_client_trade_fee_amount(
            cls,
            main_user_ids: list[int],
            current_date: datetime.date,
            target_asset: str,
            trade_type: str
    ) -> dict[int, Decimal]:
        enum_trade_type = TradeBusinessType[trade_type]
        trace_users = main_user_ids
        sub_user_mapper = {}
        if enum_trade_type == TradeBusinessType.SPOT:
            _db = TradeSummaryDB
            # 需要统计策略子账号
            sub_user_mapper = cls._get_trace_sub_user_mapper(main_user_ids)
            trace_users += sub_user_mapper.keys()
        else:
            _db = PerpetualSummaryDB

        amm_market = AmmMarketCache.list_amm_markets()
        # 使用次日0点汇率 = 当前的收盘价
        price_map = AssetPrice.get_close_price_map(current_date)
        date_str = current_date.strftime('%Y-%m-%d')
        month_str = current_date.strftime('%Y%m')
        broker_user_mapper = {i.broker_id: i.user_id for i in Broker.query.filter(
            Broker.status == Broker.Status.VALID
        ).all()}
        broker_trade_fee_mapper = defaultdict(Decimal)
        for ids_ in batch_iter(trace_users, 1000):
            user_id_str = ','.join(map(str, ids_))
            records = _db.table(f'client_fee_summary_{month_str}').select(
                "market",
                "SUM(`fee`)",
                "asset",
                "client_id",
                "user_id",
                where=f"`user_id` in ({user_id_str}) and `trade_date` = '{date_str}'",
                group_by='market, asset, client_id, user_id',
            )
            for market, fee_amount, asset, client_id, user_id in records:
                if enum_trade_type == TradeBusinessType.SPOT and market in amm_market:
                    continue
                broker_id = get_broker_id_by_client_id(client_id)
                if broker_id not in broker_user_mapper:
                    continue
                main_user_id = sub_user_mapper.get(user_id, user_id)
                asset_rate = price_map.get(asset)
                target_rate = price_map.get(target_asset)
                if not (asset_rate and target_rate):
                    continue
                broker_trade_fee_mapper[main_user_id] += fee_amount * asset_rate / target_rate
        return broker_trade_fee_mapper

    @classmethod
    def _batch_exchange_fee_amount(
            cls,
            main_user_ids: list[int],
            current_date: datetime.date,
            target_asset: str
    ) -> dict[int, Decimal]:
        price_map = AssetPrice.get_price_map()
        ex_fee_mapper = defaultdict(Decimal)
        for ids_ in batch_iter(main_user_ids, 1000):
            exchanges = AssetExchangeOrder.query.filter(
                AssetExchangeOrder.updated_at > current_date,
                AssetExchangeOrder.updated_at <= current_date + timedelta(days=1),
                AssetExchangeOrder.user_id.in_(ids_),
                AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED
            ).with_entities(
                AssetExchangeOrder.id,
                AssetExchangeOrder.user_id
            ).all()

            exchange_user_mapper = {ex_id: user_id for ex_id, user_id in exchanges}
            exchange_order_ids = set(exchange_user_mapper.keys())
            system_orders = SysAssetExchangeOrder.query.filter(
                SysAssetExchangeOrder.exchange_order_id.in_(exchange_order_ids),
                SysAssetExchangeOrder.fee_type == ExchangeFeeType.WEB,
                SysAssetExchangeOrder.fee_amount > 0
            ).with_entities(
                SysAssetExchangeOrder.exchange_order_id,
                SysAssetExchangeOrder.fee_asset,
                SysAssetExchangeOrder.fee_amount
            ).all()
            for exchange_order_id, fee_asset, fee_amount in system_orders:
                asset_rate = price_map.get(fee_asset)
                target_rate = price_map.get(target_asset)
                if not (asset_rate and target_rate):
                    continue
                main_user_id = exchange_user_mapper[exchange_order_id]
                ex_fee_mapper[main_user_id] += fee_amount * asset_rate / target_rate
        return ex_fee_mapper

    def _create_trace_data(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: CashBackFeeUserCoupon,
            coupon_detail: CashBackFeeUserCoupon
    ) -> CashBackUserCouponTrace:
        trace_data = CashBackUserCouponTrace(
            user_coupon_id=user_coupon.id,
            trade_type=coupon_detail.trade_type,
            traced_at=user_coupon.real_used_at,
            asset=user_coupon.coupon_value_type,
            traced_date=user_coupon.real_used_at.date()
        )
        return trace_data

    def _filter_and_update_trace_data(self, user_coupon: UserCoupon, trace_data: ModelBase) -> Union[ModelBase, None]:
        """手续费返现券使用批量追踪"""
        pass

    def _update_trace_data_by_fee(
            self,
            user_coupon: UserCoupon,
            trace_data: CashBackUserCouponTrace,
            spot_fee: Decimal,
            exchange_fee: Decimal,
            perpetual_fee: Decimal,
    ):
        if trace_data.traced_date >= self._today:
            return
        user_id = user_coupon.user_id
        coupon_amount = user_coupon.coupon_value
        balances = CouponTool.get_coupon_daily_balance_history(user_id, user_coupon.id, self._coupon.coupon_type)
        cashback_amount = sum(i.amount for i in balances) if balances else 0
        left_cashback_amount = coupon_amount - cashback_amount
        spot_cashback_amount = perpetual_cashback_amount = exchange_cashback_amount = Decimal()
        if left_cashback_amount > 0:
            exchange_cashback_amount = min(exchange_fee, left_cashback_amount)
            left_cashback_amount -= exchange_cashback_amount
        if left_cashback_amount > 0:
            spot_cashback_amount = min(spot_fee, left_cashback_amount)
            left_cashback_amount -= spot_cashback_amount
        if left_cashback_amount > 0:
            perpetual_cashback_amount = min(perpetual_fee, left_cashback_amount)
            left_cashback_amount -= perpetual_cashback_amount
        next_cashback_amount = quantize_amount(
            spot_cashback_amount + perpetual_cashback_amount + exchange_cashback_amount,
            PrecisionEnum.COIN_PLACES
        )

        if next_cashback_amount > 0:
            trace_detail = CashBackFeeTraceDetail.get_or_create(
                trace_id=trace_data.id,
                cashback_date=trace_data.traced_date
            )
            db.session.add(trace_detail)
            trace_detail.amount = next_cashback_amount
            trace_detail.spot_amount = spot_cashback_amount
            trace_detail.perpetual_amount = perpetual_cashback_amount
            trace_detail.exchange_amount = exchange_cashback_amount

        trace_data.cashback_amount = cashback_amount + next_cashback_amount
        trace_data.traced_at = self._now
        return trace_data

    def _handler_expiration(self, user_coupon: UserCoupon, trace_data: CashBackUserCouponTrace):
        if trace_data.cashback_amount > 0:
            user_coupon.status = UserCoupon.Status.USED
        else:
            user_coupon.status = UserCoupon.Status.EXPIRED

    def _update_user_coupon_and_details(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: CashBackFeeUserCoupon,
            trace_data: CashBackUserCouponTrace,
            *,
            coupon_detail: CashBackFeeCoupon
    ):
        if trace_data.cashback_amount >= user_coupon.coupon_value:
            user_coupon.status = UserCoupon.Status.USED
        user_coupon_detail.back_amount = trace_data.cashback_amount

    @classmethod
    def update_next_user_coupon_to_trace(cls, user_id, trace_date):
        # 先处理已过期未使用的
        expired_user_coupons = UserCoupon.query.join(
            Coupon
        ).filter(
            Coupon.coupon_type == cls.coupon_type,
            UserCoupon.user_id == user_id,
            UserCoupon.status == UserCoupon.Status.ACTIVE,
            UserCoupon.real_used_at.is_(None),
            func.date(UserCoupon.usable_expired_at) <= trace_date
        ).with_entities(
            UserCoupon.id
        ).all()
        expired_user_coupon_ids = [i.id for i in expired_user_coupons]

        UserCoupon.query.filter(
            UserCoupon.id.in_(expired_user_coupon_ids)
        ).update({
            UserCoupon.status: UserCoupon.Status.EXPIRED
        })

        db.session.flush()

        next_user_coupon = UserCoupon.query.join(
            Coupon
        ).filter(
            Coupon.coupon_type == cls.coupon_type,
            UserCoupon.user_id == user_id,
            UserCoupon.status == UserCoupon.Status.ACTIVE,
            UserCoupon.real_used_at.is_(None)
        ).order_by(
            UserCoupon.used_at
        ).with_entities(
            UserCoupon.id
        ).first()

        if not next_user_coupon:
            return
        user_coupon = UserCoupon.query.get(next_user_coupon.id)
        # 这里必须是追踪日期的次日的时间，否则追踪函数不是幂等的
        user_coupon.real_used_at = date_to_datetime(trace_date + timedelta(days=1))
        trace = CashBackUserCouponTrace.query.filter(
            CashBackUserCouponTrace.user_coupon_id == user_coupon.id
        ).first()
        real_used_date = user_coupon.real_used_at.date()
        # 处理线上已经返现过的卡券，重新被追踪重置追踪日期到真实使用当日（防止修改成单张追踪，重新启用，追踪日期落后导致多返）
        if trace and trace.traced_date < real_used_date:
            trace.traced_date = real_used_date

    def _traced_user_coupon_operating(self, user_coupon: UserCoupon):
        cashback_coupon_send_fee_task.delay(user_coupon.id)

    def _finally_update_cache(self):
        pass

    def _get_trace_user_coupons(self, coupon_id) -> Dict[int, UserCoupon]:
        query = UserCoupon.query.filter(
            UserCoupon.coupon_id == coupon_id,
            UserCoupon.real_used_at.isnot(None),
            UserCoupon.status.in_(self._coupon_service.using_status)
        ).all()
        user_coupon_mapper = {item.user_id: item for item in query}
        return user_coupon_mapper

    @classmethod
    def _get_active_trace_user_coupon_count(cls, user_id: int) -> int:
        using_count = UserCoupon.query.select_from(UserCoupon).join(
            Coupon, UserCoupon.coupon_id == Coupon.id
        ).filter(
            UserCoupon.status == UserCoupon.Status.ACTIVE,
            UserCoupon.user_id == user_id,
            UserCoupon.real_used_at.isnot(None),
            Coupon.coupon_type == cls.coupon_type
        ).with_entities(
            func.count("*")
        ).scalar()
        return using_count or 0

    def batch_trace(self, trace_date: datetime.date):
        """
        批量追踪卡券
        适合每天结算类型的卡券，以及用户统计时间范围一致的类型
        """
        coupon_id = self._coupon.id
        coupon_detail = self._coupon_service.coupon_detail.query.filter(
            self._coupon_service.coupon_detail.coupon_id == coupon_id
        ).first()
        if not coupon_detail:
            return
        user_coupon_mapper = self._get_trace_user_coupons(coupon_id)
        using_coupon_ids = [user_coupon.id for user_coupon in user_coupon_mapper.values()]
        trace_query, user_coupon_details = [], []
        for ids in batch_iter(using_coupon_ids, 1000):
            trace_query += self.user_trace.query.filter(
                self.user_trace.user_coupon_id.in_(ids)
            ).all()
            user_coupon_details += self.user_coupon_detail.query.filter(
                self.user_coupon_detail.user_coupon_id.in_(ids)
            ).all()
        user_trace_data_mapper = {trace.user_coupon_id: trace for trace in trace_query}
        user_coupon_detail_mapper = {detail.user_coupon_id: detail for detail in user_coupon_details}
        create_trace_data = []
        main_user_ids = []
        trace_user_coupons = []
        for user_coupon in user_coupon_mapper.values():
            user_coupon_detail = user_coupon_detail_mapper[user_coupon.id]
            trace_data = user_trace_data_mapper.get(user_coupon.id)
            if not trace_data:
                trace_data = self._create_trace_data(user_coupon, user_coupon_detail, coupon_detail)
                db.session.add(trace_data)
                db.session.flush()
                create_trace_data.append(trace_data)
                user_trace_data_mapper[user_coupon.id] = trace_data
            if trace_data.traced_date != trace_date:
                continue
            main_user_ids.append(user_coupon.user_id)
            trace_user_coupons.append(user_coupon)
        db.session.bulk_save_objects(create_trace_data)
        db.session.commit()
        if not trace_user_coupons:
            return
        user_spot_fee_mapper, client_spot_fee_mapper, exchange_fee_mapper = {}, {}, {}
        if coupon_detail.trade_type in [CashBackFeeCoupon.TradeType.SPOT, CashBackFeeCoupon.TradeType.ALL]:
            user_spot_fee_mapper = self._batch_trade_fee_amount(
                list(main_user_ids),
                trace_date,
                self._coupon.value_type,
                CashBackFeeCoupon.TradeType.SPOT.name
            )
            client_spot_fee_mapper = self._batch_client_trade_fee_amount(
                list(main_user_ids),
                trace_date,
                self._coupon.value_type,
                CashBackFeeCoupon.TradeType.SPOT.name
            )
            exchange_fee_mapper = self._batch_exchange_fee_amount(
                list(main_user_ids),
                trace_date,
                self._coupon.value_type,
            )
        user_perpetual_fee_mapper, client_perpetual_fee_mapper = {}, {}
        if coupon_detail.trade_type in [CashBackFeeCoupon.TradeType.PERPETUAL, CashBackFeeCoupon.TradeType.ALL]:
            user_perpetual_fee_mapper = self._batch_trade_fee_amount(
                list(main_user_ids),
                trace_date,
                self._coupon.value_type,
                CashBackFeeCoupon.TradeType.PERPETUAL.name
            )
            client_perpetual_fee_mapper = self._batch_client_trade_fee_amount(
                list(main_user_ids),
                trace_date,
                self._coupon.value_type,
                CashBackFeeCoupon.TradeType.PERPETUAL.name
            )

        for user_coupon in trace_user_coupons:
            user_id = user_coupon.user_id
            user_coupon_detail = user_coupon_detail_mapper[user_coupon.id]
            if self._get_active_trace_user_coupon_count(user_id) > 1:
                current_app.logger.error(f"user had many same coupon type coupon, user_id :{user_id}")
                continue
            trace_data = user_trace_data_mapper[user_coupon.id]
            zero = Decimal()
            spot_fee = max(
                user_spot_fee_mapper.get(user_id, zero) - client_spot_fee_mapper.get(user_id, zero),
                zero
            )
            perpetual_fee = max(
                user_perpetual_fee_mapper.get(user_id, zero) - client_perpetual_fee_mapper.get(user_id, zero),
                zero
            )
            trace_data = self._update_trace_data_by_fee(
                user_coupon,
                trace_data,
                spot_fee,
                exchange_fee_mapper.get(user_id, zero),
                perpetual_fee
            )
            if not trace_data:
                continue
            self._update_user_coupon_and_details(
                user_coupon,
                user_coupon_detail,
                trace_data,
                coupon_detail=coupon_detail
            )
            if user_coupon.usable_expired_at < self._now and user_coupon.status == UserCoupon.Status.ACTIVE:
                self._handler_expiration(user_coupon, trace_data)
            # 当前卡券使用完成，自动开启下一张卡券的追踪
            if user_coupon.status != UserCoupon.Status.ACTIVE:
                self.update_next_user_coupon_to_trace(user_coupon.user_id, trace_date)
            db.session.commit()

        for uc in trace_user_coupons:
            self._traced_user_coupon_operating(uc)
        self._finally_update_cache()


class PerpetualSubsidyCouponTrace(BaseCouponTrace):
    coupon_type = Coupon.CouponType.PERPETUAL_SUBSIDY
    user_coupon_detail = PerpetualSubsidyUserCoupon
    user_trace = PerpetualSubsidyUserCouponTrace

    @classmethod
    def _get_trace_user_ids(cls, main_user_id: int, trade_type: PerpetualSubsidyCoupon.TradeType) -> List[int]:
        user_ids = [main_user_id]
        if trade_type == PerpetualSubsidyCoupon.TradeType.PERPETUAL_TRADE:
            pass
        else:
            # 后续上了其他类型，可能还有子帐号
            raise ValueError(f"PerpetualSubsidyCouponTrace not support trade_type:{trade_type.name}")
        return user_ids

    @classmethod
    def _get_position_loss_amount(
            cls,
            user_id: int,
            start_dt: datetime,
            end_dt: datetime,
            target_asset: str,
            trade_type: PerpetualSubsidyCoupon.TradeType,
    ) -> Decimal:
        """ 查某天内已平仓位的亏损数 """
        user_ids = cls._get_trace_user_ids(user_id, trade_type)
        dbs_tables = defaultdict(list)
        for user_id_ in user_ids:
            _db, _table = PerpetualHistoryDB.user_to_db_and_table(user_id_, 'position_history')
            dbs_tables[(_db, _table)].append(user_id_)

        start_ts = int(start_dt.timestamp())
        end_ts = int(end_dt.timestamp())
        records = []
        columns = ('position_id', 'user_id', 'create_time', 'update_time',
                   'market', 'finish_type', 'amount_max_margin', 'profit_real')
        for k, v in dbs_tables.items():
            _db, _table = k
            user_id_str = ','.join(map(str, v))
            where = f' user_id in ({user_id_str}) ' \
                    f'and update_time >= {start_ts} and update_time < {end_ts} ' \
                    f'and profit_real<0 '
            _records = _db.table(_table).select(*columns, where=where)
            records.extend(_records)

        p_markets = {i[4] for i in records}
        p_market_asset_type = {}  # {合约市场：盈亏数的单位}
        for m in p_markets:
            market_info = PerpetualMarketCache().get_market_info(m)
            if not market_info:
                market_info = PerpetualOfflineMarketCache().get_market_info(m)
            if not market_info:
                continue
            balance_asset = PerpetualMarketCache.get_balance_asset(market_info)
            p_market_asset_type[m] = balance_asset

        amount = Decimal()
        price_map = AssetPrice.get_close_price_map(start_dt.date())  # 使用次日0点汇率 = 当前的收盘价
        for r in records:
            market = r[4]
            if market not in p_market_asset_type:
                current_app.logger.warning(f"user_subsidy_get_loss {user_id} {start_ts} {end_ts} "
                                           f"not found market {market}")
                continue
            asset = p_market_asset_type[market]
            asset_rate = price_map.get(asset)
            target_rate = price_map.get(target_asset)
            if not (asset_rate and target_rate):
                current_app.logger.warning(f"user_subsidy_get_loss {user_id} {start_ts} {end_ts} "
                                           f"not found price {asset_rate} {target_rate}")
                continue
            profit_real = r[7]
            amount += abs(profit_real * asset_rate / target_rate)
        return quantize_amount(amount, 8)

    def _create_trace_data(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: PerpetualSubsidyUserCoupon,
            coupon_detail: PerpetualSubsidyCoupon,
    ) -> PerpetualSubsidyUserCouponTrace:
        trace_data = PerpetualSubsidyUserCouponTrace(
            user_coupon_id=user_coupon.id,
            asset=user_coupon.coupon_value_type,
            trade_type=coupon_detail.trade_type,
            traced_at=user_coupon.used_at,
            next_subsidy_date=user_coupon.used_at.date()
        )
        return trace_data

    def _filter_and_update_trace_data(self, user_coupon: UserCoupon, trace_data: PerpetualSubsidyUserCouponTrace):
        if trace_data.next_subsidy_date >= self._today:
            # 今天追踪前一天的数据
            return

        _next_subsidy_dt = date_to_datetime(trace_data.next_subsidy_date)
        # 某天的new_subsidy_amount=0时，traced_at和next_subsidy_date会同步更新，这里用卡券使用日期来判断
        if trace_data.traced_at.date() == user_coupon.used_at.date():
            start_dt = user_coupon.used_at  # 第一天的数据统计，从卡券使用时间开始
        else:
            start_dt = _next_subsidy_dt
        end_dt = _next_subsidy_dt + timedelta(days=1)

        user_id = user_coupon.user_id
        coupon_amount = user_coupon.coupon_value
        loss_amount = self._get_position_loss_amount(
            user_id=user_id,
            start_dt=start_dt,
            end_dt=end_dt,
            target_asset=user_coupon.coupon_value_type,
            trade_type=trace_data.trade_type,
        )
        balances = CouponTool.get_coupon_daily_balance_history(user_id, user_coupon.id, self._coupon.coupon_type)
        already_subsidy_amount = sum(i.amount for i in balances) if balances else 0
        left_subsidy_amount = coupon_amount - already_subsidy_amount
        if left_subsidy_amount > 0:
            coupon_detail: PerpetualSubsidyCoupon = PerpetualSubsidyCoupon.query.filter(
                PerpetualSubsidyCoupon.coupon_id == self._coupon.id
            ).first()
            new_subsidy_amount = quantize_amount(loss_amount * coupon_detail.subsidy_rate, 8)
            new_subsidy_amount = min(left_subsidy_amount, new_subsidy_amount)
        else:
            new_subsidy_amount = 0

        if new_subsidy_amount > 0:
            trace_detail: PerpetualSubsidyTraceDetail = PerpetualSubsidyTraceDetail.get_or_create(
                trace_id=trace_data.id,
                subsidy_date=trace_data.next_subsidy_date,
            )
            db.session.add(trace_detail)
            trace_detail.position_loss_amount = loss_amount
            trace_detail.subsidy_amount = new_subsidy_amount
        trace_data.traced_at = self._now
        trace_data.total_subsidy_amount = already_subsidy_amount + new_subsidy_amount

        # update user_coupon_detail info
        user_coupon_detail: PerpetualSubsidyUserCoupon = PerpetualSubsidyUserCoupon.query.filter(
            PerpetualSubsidyUserCoupon.user_coupon_id == user_coupon.id,
        ).first()
        _, trade_volume, fee_usd = CouponTool.get_perpetual_trade_amount(
            user_id,
            start_dt,
            end_dt,
            user_coupon.coupon_value_type,
        )
        user_coupon_detail.active_trade_usd += trade_volume
        user_coupon_detail.active_trade_fee_usd += fee_usd

        return trace_data

    def _handler_expiration(self, user_coupon: UserCoupon, trace_data: PerpetualSubsidyUserCouponTrace):
        if trace_data.total_subsidy_amount > 0:
            user_coupon.status = UserCoupon.Status.USED
        else:
            user_coupon.status = UserCoupon.Status.EXPIRED

    def _update_user_coupon_and_details(
            self,
            user_coupon: UserCoupon,
            user_coupon_detail: PerpetualSubsidyUserCoupon,
            trace_data: PerpetualSubsidyUserCouponTrace,
            *,
            coupon_detail: PerpetualSubsidyCoupon,
    ):
        if trace_data.total_subsidy_amount >= user_coupon.coupon_value:
            user_coupon.status = UserCoupon.Status.USED

    def _traced_user_coupon_operating(self, user_coupon: UserCoupon):
        perpetual_subsidy_send_task.delay(user_coupon.id)

    def _finally_update_cache(self):
        pass


class CopyTradingExperienceFeeCouponTrace(BaseCouponTrace):
    coupon_type = Coupon.CouponType.COPY_TRADING_EXPERIENCE_FEE
    user_coupon_detail = CopyTradingExperienceFeeUserCoupon
    user_trace = CopyTradingExperienceFeeUserCouponTrace

    def _handler_expiration(self, user_coupon: UserCoupon, trace_data: UserCouponTrace):
        user_coupon.status = UserCoupon.Status.TO_BE_RECYCLED

    def _create_trace_data(
        self,
        user_coupon: UserCoupon,
        user_coupon_detail: CopyTradingExperienceFeeUserCoupon,
        coupon_detail: CopyTradingExperienceFeeCoupon,
    ) -> CopyTradingExperienceFeeUserCouponTrace:
        trace_data = CopyTradingExperienceFeeUserCouponTrace(
            user_coupon_id=user_coupon.id,
            trade_amount=Decimal(),
            profit_real=Decimal(),
            asset=user_coupon.coupon_value_type,
            traced_at=user_coupon.used_at,
            profit_at=user_coupon.used_at,
        )
        return trace_data

    def _filter_and_update_trace_data(
        self,
        user_coupon: UserCoupon,
        trace_data: CopyTradingExperienceFeeUserCouponTrace,
    ) -> CopyTradingExperienceFeeUserCouponTrace:
        from app.business.copy_trading.base import CopyRelationQuerier

        coupon_detail: CopyTradingExperienceFeeCoupon = CopyTradingExperienceFeeCoupon.query.filter(
            CopyTradingExperienceFeeCoupon.coupon_id == self._coupon.id
        ).first()
        cp_user_coupon: CopyTradingExperienceFeeUserCoupon = CopyTradingExperienceFeeUserCoupon.query.filter(
            CopyTradingExperienceFeeUserCoupon.user_coupon_id == user_coupon.id,
        ).first()

        sub_user_id = None
        cp_finish_dt = None
        if coupon_detail.trade_type == CopyTradingExperienceFeeCoupon.TradeType.TRADER:
            trade_his = CopyRelationQuerier.get_trade_his_by_id(cp_user_coupon.history_id)
            if trade_his:
                sub_user_id = trade_his.sub_user_id
                if trade_his.status == CopyTraderHistory.Status.FINISHED:
                    cp_finish_dt = trade_his.finished_at
        elif coupon_detail.trade_type == CopyTradingExperienceFeeCoupon.TradeType.FOLLOWER:
            follow_his = CopyRelationQuerier.get_follow_his_by_id(cp_user_coupon.history_id)
            if follow_his:
                sub_user_id = follow_his.sub_user_id
                if follow_his.status == CopyFollowerHistory.Status.FOLLOWING:
                    cp_finish_dt = follow_his.finished_at
        if not sub_user_id:
            return trace_data

        end_at = min(user_coupon.usable_expired_at, cp_finish_dt) if cp_finish_dt else user_coupon.usable_expired_at
        max_trade_time, trade_amount, deal_profit = CouponTool.get_user_trade_amount(
            user_id=sub_user_id,
            start_at=trace_data.traced_at,
            end_at=end_at,
            asset=user_coupon.coupon_value_type,
        )
        max_funding_time, funding_amount = CouponTool.get_user_funding(
            user_id=sub_user_id,
            start_at=trace_data.profit_at,
            end_at=end_at,
            asset=user_coupon.coupon_value_type,
        )
        trace_data.trade_amount += trade_amount
        trace_data.profit_real += (funding_amount + deal_profit)
        trace_data.traced_at = timestamp_to_datetime(max_trade_time)
        trace_data.profit_at = timestamp_to_datetime(max_funding_time)
        return trace_data

    def _update_user_coupon_and_details(
        self,
        user_coupon: UserCoupon,
        user_coupon_detail: CopyTradingExperienceFeeUserCoupon,
        trace_data: CopyTradingExperienceFeeUserCouponTrace,
        *,
        coupon_detail: CopyTradingExperienceFeeCoupon,
    ):
        # 卡劵完成进度条
        user_coupon_detail.user_trade_amount = trace_data.trade_amount
        # 累计交易额 > 卡劵面额 (已达标)
        if trace_data.trade_amount >= coupon_detail.qualified_trade_amount:
            user_coupon.status = UserCoupon.Status.USED
            user_coupon_detail.user_trade_amount = trace_data.trade_amount
            user_coupon_detail.profit_real = trace_data.profit_real
            user_coupon_detail.reason = CopyTradingExperienceFeeUserCoupon.RecycledReason.QUALIFIED
            db.session.commit()
            return
        # 累计已实现盈亏 < -体验金 (全部亏损)
        is_deficit = CopyTradingExperienceFeeUserCoupon.calc_is_deficit(
            user_coupon.coupon_value_type, user_coupon.coupon_value, trace_data.profit_real
        )
        if is_deficit:
            user_coupon.status = UserCoupon.Status.USED
            user_coupon_detail.user_trade_amount = trace_data.trade_amount
            user_coupon_detail.profit_real = trace_data.profit_real
            user_coupon_detail.reason = CopyTradingExperienceFeeUserCoupon.RecycledReason.DEFICIT
            db.session.commit()
            return

    def _finally_update_cache(self):
        CouponTool.clear_cache()
        copy_trading_experience_fee_recycle_task.delay()

    @classmethod
    def early_finish_follower_coupon(cls, fol_user_id: int, sub_user_id: int, follow_his_id: int):
        cls.early_finish_user_coupon(fol_user_id, sub_user_id, follow_his_id)

    @classmethod
    def early_finish_trader_coupon(cls, trader_user_id: int, sub_user_id: int, trade_his_id: int):
        cls.early_finish_user_coupon(trader_user_id, sub_user_id, trade_his_id)

    @classmethod
    def early_finish_user_coupon(cls, user_id: int, sub_user_id: int, his_id: int):
        from app.business.coupon.user_coupon import CopyTradingExperienceFeeService

        cp_user_coupons = CopyTradingExperienceFeeService.query_sub_all_using_coupon(user_id, sub_user_id)
        if not cp_user_coupons:
            return
        assert len(cp_user_coupons) == 1
        cp_user_coupon = cp_user_coupons[0]
        assert cp_user_coupon.history_id == his_id
        cls.early_finish_by_user_coupon_id(cp_user_coupon.user_coupon_id)

    @classmethod
    def early_finish_by_user_coupon_id(cls, user_coupon_id: int):
        user_coupon: UserCoupon = UserCoupon.query.get(user_coupon_id)
        if user_coupon.status in [UserCoupon.Status.ACTIVE, UserCoupon.Status.TO_BE_RECYCLED]:
            cls.early_finish_by_user_coupon(user_coupon)
        assert user_coupon.status in [UserCoupon.Status.USED, UserCoupon.Status.EXPIRED]

    @classmethod
    def early_finish_by_user_coupon(cls, user_coupon: UserCoupon):
        """ 提前结束一个卡券 """
        # 1. 再追踪一次
        user_coupon_id = user_coupon.id
        user_coupon_detail = cls.user_coupon_detail.query.filter(
            cls.user_coupon_detail.user_coupon_id == user_coupon_id,
        ).first()
        trace_data = cls.user_trace.query.filter(
            cls.user_trace.user_coupon_id == user_coupon_id,
        ).first()

        self = cls(user_coupon.coupon_id)
        coupon_id = self._coupon.id
        coupon_detail = self._coupon_service.coupon_detail.query.filter(
            self._coupon_service.coupon_detail.coupon_id == coupon_id,
        ).first()
        if not trace_data:
            trace_data = self._create_trace_data(user_coupon, user_coupon_detail, coupon_detail)
            db.session.add(trace_data)
            db.session.flush()
        trace_data = self._filter_and_update_trace_data(user_coupon, trace_data)
        self._update_user_coupon_and_details(
            user_coupon,
            user_coupon_detail,
            trace_data,
            coupon_detail=coupon_detail
        )
        # 2. 修改状态
        if user_coupon.status == UserCoupon.Status.ACTIVE:
            user_coupon.status = UserCoupon.Status.TO_BE_RECYCLED
            db.session.commit()
        # 3. 同步执行回收
        if user_coupon.status == UserCoupon.Status.TO_BE_RECYCLED:
            copy_trading_experience_fee_recycle_task(user_coupon.id)


def get_coupon_trace_server(coupon_type: Union[str, Coupon.CouponType]):
    if isinstance(coupon_type, str):
        coupon_type = Coupon.CouponType[coupon_type]
    trace = _CouponTraceMeta._coupon_trace.get(coupon_type)
    if not trace:
        raise ValueError(f"coupon type: {coupon_type.name} trace server not implemented")
    return trace


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def trace_user_coupon_task(coupon_id):
    """追踪卡劵使用情况"""
    coupon = Coupon.query.get(coupon_id)
    # 手续费返现券、合约补贴金 每天结算, VIP升级券单独追踪
    if coupon.coupon_type in [
        Coupon.CouponType.CASHBACK_FEE,
        Coupon.CouponType.PERPETUAL_SUBSIDY,
        Coupon.CouponType.VIP_UPGRADE
    ]:
        return
    trace_server = get_coupon_trace_server(coupon.coupon_type)
    trace_server(coupon_id).trace()


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def trace_cashback_fee_coupon(coupon_id):
    """手续费返现券追踪"""
    coupon = Coupon.query.get(coupon_id)
    if not coupon or coupon.coupon_type != Coupon.CouponType.CASHBACK_FEE:
        return
    today_ = today()
    trace_date = UserCoupon.query.join(
        CashBackUserCouponTrace, UserCoupon.id == CashBackUserCouponTrace.user_coupon_id
    ).filter(
        UserCoupon.coupon_id == coupon_id,
        UserCoupon.status == UserCoupon.Status.ACTIVE,
        UserCoupon.real_used_at.isnot(None),
        CashBackUserCouponTrace.traced_date < today_
    ).with_entities(
        func.min(CashBackUserCouponTrace.traced_date)
    ).scalar()
    trace_date = trace_date or today_ - timedelta(days=1)
    while trace_date < today_:
        CashBackFeeCouponTrace(coupon_id).batch_trace(trace_date)
        trace_date += timedelta(days=1)


@celery_task(queue=CeleryQueues.ACTIVITY)
@lock_call(with_args=True)
def trace_perpetual_subsidy_coupon(coupon_id):
    """合约补贴金的追踪"""
    coupon = Coupon.query.get(coupon_id)
    if not coupon or coupon.coupon_type != Coupon.CouponType.PERPETUAL_SUBSIDY:
        return
    PerpetualSubsidyCouponTrace(coupon_id).trace()
