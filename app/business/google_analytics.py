from functools import cached_property

import requests
from flask import current_app

from app.caches.report import GoogleAnalyticRefreshTokenCache
from app.common import search_for_countries
from app.config import config


class GoogleAnalytics:
    google_config = config["GOOGLE_ANALYTIC_OAUTH_CONFIG"]
    client_id = config["GOOGLE_ANALYTIC_OAUTH_CONFIG"]["client_id"]
    client_secret = config["GOOGLE_ANALYTIC_OAUTH_CONFIG"]["client_secret"]
    coinex_view_id = config["GOOGLE_ANALYTIC_OAUTH_CONFIG"]["coinex_view_id"]
    redirect_uri = config["GOOGLE_ANALYTIC_OAUTH_CONFIG"]["redirect_uri"]
    get_token_url = config["GOOGLE_ANALYTIC_OAUTH_CONFIG"]["token_uri"]
    api_uri = config["GOOGLE_ANALYTIC_OAUTH_CONFIG"]["api_uri"]
    base_url = api_uri.format(view_id=coinex_view_id)

    # beta接口官方提示后续可能会有改动https://content-analyticsdata.googleapis.com/v1beta/properties/{view_id}:runReport?alt=json
    # 接口文档参考https://developers.google.com/analytics/devguides/reporting/data/v1?hl=zh-cn
    # api请求字段文档参考https://ga-dev-tools.google/ga4/query-explorer/

    def __init__(self):
        # 每次初始化刷新 access token
        self.access_token = None
        self._refresh_access_token()

    @cached_property
    def refresh_token(self):
        return GoogleAnalyticRefreshTokenCache().read()

    def _refresh_access_token(self):
        if not self.refresh_token:
            raise Exception(
                "fail to get refresh_token! "
                "please run update_google_analytic_oauth_refresh_token.py"
            )
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": self.refresh_token,
            "grant_type": "refresh_token",
        }
        r = requests.post(self.get_token_url, data=data, timeout=10)
        if r.json().get("access_token"):
            self.access_token = r.json()["access_token"]
        else:
            error_msg = "Get access token through refresh token failed."
            current_app.logger.error(error_msg)

    def do_post_with_access_token(self, post_body):
        headers = {
            "Authorization": "Bearer {}".format(self.access_token),
        }
        r = requests.post(
            url=self.base_url, json=post_body, headers=headers, timeout=10
        )
        if r.status_code == 200:
            return r.json()
        else:
            error_msg = "do_post_with_access_token failed: {}, {}, {}, {}".format(
                r.status_code, r.url, r.content, r.text
            )
            current_app.logger.error(error_msg)
            return None

    def get_uv_channel(self, start_date, end_date):
        str_start_date = start_date.strftime("%Y-%m-%d")
        str_end_date = end_date.strftime("%Y-%m-%d")

        data = {
            "dimensions": [{"name": "sessionDefaultChannelGroup"}],
            "metrics": [{"name": "activeUsers"}],
            "dateRanges": [{"startDate": str_start_date, "endDate": str_end_date}],
        }
        r = self.do_post_with_access_token(post_body=data)
        rows = r["rows"]
        uv_channel = [
            {
                "channel": item["dimensionValues"][0]["value"],
                "uv": int(item["metricValues"][0]["value"]),
            }
            for item in rows
        ]
        uv_channel.sort(key=lambda x: x["uv"], reverse=True)

        return uv_channel

    def get_uv_country(self, start_date, end_date):
        str_start_date = start_date.strftime("%Y-%m-%d")
        str_end_date = end_date.strftime("%Y-%m-%d")

        data = {
            "dimensions": [{"name": "country"}],
            "metrics": [{"name": "activeUsers"}],
            "dateRanges": [{"startDate": str_start_date, "endDate": str_end_date}],
        }
        result = self.do_post_with_access_token(post_body=data)
        rows = result["rows"]
        country_uv_list = [
            {
                "country": item["dimensionValues"][0]["value"],
                "uv": int(item["metricValues"][0]["value"]),
            }
            for item in rows
        ]
        country_uv_list.sort(key=lambda x: x["uv"], reverse=True)
        country_uv_rank = []
        for country in country_uv_list:
            search_result = search_for_countries(country["country"])
            cn_name = search_result[0].cn_name if search_result else country["country"]
            country_uv_rank.append({"country": cn_name, "uv": country["uv"]})
        return country_uv_rank

    def get_new_uv(self, start_date, end_date):
        str_start_date = start_date.strftime("%Y-%m-%d")
        str_end_date = end_date.strftime("%Y-%m-%d")

        data = {
            "metrics": [{"name": "newUsers"}],
            "dateRanges": [{"startDate": str_start_date, "endDate": str_end_date}],
        }
        r = self.do_post_with_access_token(post_body=data)
        return int(r["rows"][0]["metricValues"][0]["value"])
