#!/usr/bin/env python
# -*- coding: utf-8 -*-
from __future__ import annotations

from decimal import Decimal
from enum import Enum, unique, IntEnum
from typing import Dict, Union, Optional, Tuple

from flask import current_app
from flask_babel import get_locale, format_percent, gettext
from flask_restx import fields
from werkzeug.utils import cached_property

from . import PriceManager
from .clients import ServerClient, PerpetualServerClient, SPOT_ACCOUNT_ID
from ..assets import get_asset_config, list_pre_assets
from ..caches import (
    PerpetualMarketCache, MarginAccountIdCache, MarketCache, AmmMarketCache,
    IndexPriceCache, AssetCache
)
from ..caches.order import (
    OrderPriceLimitCache, SpotMarketDeviationCache,
    PerpetualMarketDeviationCache, SpotMarketPriceDeviationCache, PerpetualMarketPriceDeviationCache
)
from ..caches.config import PriceVerifySettingCache
from ..common import (
    OrderType, OrderSideType, OrderBusinessType, DEFAULT_DEPTH_LIMIT, OrderOption,
    USER_ORDER_OPTIONS, OrderIntType, StopOrderIntType,
)
from .fee import FeeFetcher
from ..business import UserPreferences, SiteSettings
from .flex_config import ConfSettings
from .price_config import PriceVerifySettingModel

from ..exceptions import (
    InvalidArgument, OrderException, OrderExceptionMap, ForbidTrading,
    MarginIndexPriceError,
)
from ..models import User, PreTradingAssetConfig
from ..models.system import PriceVerifySetting
from ..utils import amount_to_str, AmountType

from .func_cache import mem_cached

PERPETUAL_SPECIAL_MARKETS = {
    "BTCUSD", "BTCUSDT", "ETHUSD", "ETHUSDT"
}

PERPETUAL_SPECIAL_MARKETS_LIMIT_ORDER_RATE = Decimal('0.05')
PERPETUAL_SPECIAL_MARKETS_STOP_LIMIT_ORDER_RATE = Decimal('0.05')


PriceVerifySettings = ConfSettings(
    PriceVerifySetting, PriceVerifySettingCache(), PriceVerifySettingModel)


def get_max_price_for_pre_asset(base_asset: str) -> Decimal | None:
    if base_asset in list_pre_assets():
        _conf = PreTradingAssetConfig.query.filter(
            PreTradingAssetConfig.asset == base_asset
        ).first()
        return _conf.pledge_ratio
    return None


def get_pre_asset_max_prices() -> dict[str, Decimal]:
    q = PreTradingAssetConfig.query.filter(
    ).with_entities(PreTradingAssetConfig.asset, PreTradingAssetConfig.pledge_ratio).all()
    return {v.asset: v.pledge_ratio for v in q}


def auto_set_order_limit_caches(market: str, price: Decimal):
    # 设置初始价格
    for _type in (OrderType.LIMIT_ORDER_TYPE, OrderType.MARKET_ORDER_TYPE):
        cache = OrderPriceLimitCache(market, _type)
        if cache.is_margin:
            _max_buy_rate = cache.MARGIN_MAX_INCREASE_RATE
            _max_sell_rate = cache.MARGIN_MAX_DECREASE_RATE
        else:
            if _type == OrderType.MARKET_ORDER_TYPE:
                _max_buy_rate = PriceVerifySettings.limit_normal_buy_period
                _max_sell_rate = PriceVerifySettings.limit_normal_sell_period
            else:
                _max_buy_rate = PriceVerifySettings.market_normal_buy_period
                _max_sell_rate = PriceVerifySettings.market_normal_sell_period

        max_price = price * (1 + _max_buy_rate)
        min_price = price * (1 - _max_sell_rate)
        precision = cache.get_price_precision()
        _base_asset = cache.market_info['base_asset']
        if (p := get_max_price_for_pre_asset(_base_asset)) is not None:
            max_price = min(p, max_price)
            min_price = max(Decimal(), min_price)

        cache.hmset({
            'max_price': amount_to_str(max_price, precision),
            'min_price': amount_to_str(min_price, precision),
        })
    return True


def set_order_limit_price(market: str,
                          max_price: Decimal,
                          min_price: Decimal,
                          precision: int,
                          order_type: OrderType = OrderType.LIMIT_ORDER_TYPE):
    cache = OrderPriceLimitCache(market, order_type)
    cache.hmset({
        'max_price': amount_to_str(max_price, precision),
        'min_price': amount_to_str(min_price, precision),
    })


class VerifyPriceTool(object):

    def __init__(self, market: str, business_type: OrderBusinessType, order_type: OrderType,
                 sell_or_buy: OrderSideType,
                 amount: AmountType,
                 price: AmountType,
                 stop_price: AmountType,
                 account_id: int = 0,
                 order_asset: str = None):
        self.market = market
        if business_type not in OrderBusinessType:
            raise InvalidArgument(business_type)
        if order_type not in OrderType:
            raise InvalidArgument(order_type)
        if sell_or_buy not in OrderSideType:
            raise InvalidArgument(sell_or_buy)
        self.business_type = business_type
        self.order_type = order_type
        self.sell_or_buy = sell_or_buy
        self.price = Decimal(price)
        self.stop_price = Decimal(stop_price)
        self.amount = Decimal(amount)
        self.order_asset = order_asset

        self.check_margin_params(account_id, market)
        self.account_id = account_id
        if self.business_type != OrderBusinessType.PERPETUAL_BUSINESS_TYPE and \
                self.is_open_margin_market(market):
            self.business_type = OrderBusinessType.MARGIN_BUSINESS_TYPE
            if self.account_id != SPOT_ACCOUNT_ID and \
                    self.market in SiteSettings.forbidden_margin_markets:
                raise ForbidTrading

    def validate(self, user_type: User.UserType):
        instance = BasePriceTool.get_all_tools()[self.order_type](self.market,
                                                                  self.business_type,
                                                                  self.sell_or_buy,
                                                                  self.price,
                                                                  self.amount,
                                                                  self.stop_price,
                                                                  self.order_asset)
        if user_type == User.UserType.INTERNAL_MAKER:
            return
        instance.validate()

    @classmethod
    def is_open_margin_market(cls, market: str) -> bool:
        return market in MarginAccountIdCache.list_online_markets().values()

    @classmethod
    def check_margin_params(cls, account_id: int, market: str) -> bool:
        if account_id == SPOT_ACCOUNT_ID:
            return True
        if account_id in MarginAccountIdCache.list_online_markets().keys() and \
                MarginAccountIdCache.list_online_markets()[account_id] == market:
            return True
        raise InvalidArgument(f'{account_id} not match {market}')


def raise_helper(business_type: OrderBusinessType,
                 order_type: OrderType,
                 sell_or_buy: OrderSideType,
                 rate: Decimal,
                 precision: Optional[int] = 2,
                 is_price: bool = False):
    code_mapping = {
        OrderBusinessType.NORMAL_BUSINESS_TYPE: {
            OrderType.MARKET_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.SELL_MARKET_ORDER_PRICE_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.BUY_MARKET_ORDER_PRICE_OVER_DEVIATION,
            },
            OrderType.LIMIT_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.NORMAL_SELL_LIMIT_ORDER_PRICE_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.NORMAL_BUY_LIMIT_ORDER_PRICE_OVER_DEVIATION,
            },
            OrderType.STOP_LIMIT_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.STOP_LIMIT_ORDER_PRICE_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.STOP_LIMIT_ORDER_PRICE_OVER_DEVIATION,
            },
            OrderType.STOP_MARKET_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.STOP_MARKET_SELL_ORDER_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.STOP_MARKET_BUY_ORDER_OVER_DEVIATION,
            }
        },
        OrderBusinessType.MARGIN_BUSINESS_TYPE: {
            OrderType.MARKET_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.SELL_MARKET_ORDER_PRICE_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.BUY_MARKET_ORDER_PRICE_OVER_DEVIATION,
            },
            OrderType.LIMIT_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.MARGIN_LIMIT_ORDER_PRICE_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.MARGIN_LIMIT_ORDER_PRICE_OVER_DEVIATION,
            },
            OrderType.STOP_LIMIT_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.STOP_LIMIT_ORDER_PRICE_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.STOP_LIMIT_ORDER_PRICE_OVER_DEVIATION,
            },
            OrderType.STOP_MARKET_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.STOP_MARKET_SELL_ORDER_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.STOP_MARKET_BUY_ORDER_OVER_DEVIATION,
            }
        },
        OrderBusinessType.PERPETUAL_BUSINESS_TYPE: {
            OrderType.MARKET_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.PERPETUAL_MARKET_ORDER_PRICE_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.PERPETUAL_MARKET_ORDER_PRICE_OVER_DEVIATION,
            },
            OrderType.LIMIT_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.PERPETUAL_LIMIT_ORDER_PRICE_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.PERPETUAL_LIMIT_ORDER_PRICE_OVER_DEVIATION,
            },
            OrderType.STOP_LIMIT_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.STOP_LIMIT_ORDER_PRICE_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.STOP_LIMIT_ORDER_PRICE_OVER_DEVIATION,
            },
            OrderType.STOP_MARKET_ORDER_TYPE: {
                OrderSideType.SELL: OrderException.PERPETUAL_STOP_MARKET_ORDER_OVER_DEVIATION,
                OrderSideType.BUY: OrderException.PERPETUAL_STOP_MARKET_ORDER_OVER_DEVIATION,
            }
        },
    }
    code = code_mapping[business_type][order_type][sell_or_buy]
    if is_price:
        rate = amount_to_str(rate, precision)
    else:
        lang = get_locale()
        if lang and lang.text_direction == 'rtl':
            rate = format_percent(rate, format='%')
        else:
            rate = format_percent(rate, format='.%')
    raise OrderExceptionMap[code](rate=rate)


class _BaseRuleCheckerMeta(type):

    _tools: Dict = {}

    def __new__(mcs, name, bases, dct):
        cls = super().__new__(mcs, name, bases, dct)
        type_name = 'order_type'
        rule_type = getattr(cls, type_name, None)
        if rule_type is None:
            return cls
        if rule_type not in OrderType:
            raise ValueError('invalid rule type')
        mcs._tools[rule_type] = cls
        return cls

    @classmethod
    def get_all_tools(mcs):
        return mcs._tools


class BasePriceTool(metaclass=_BaseRuleCheckerMeta):

    def __init__(self, market: str, business_type: OrderBusinessType,
                 sell_or_buy: OrderSideType, price: Decimal, amount: Decimal,
                 stop_price: Decimal = Decimal('0'),
                 order_asset: str = None):
        self.market = market
        if business_type not in OrderBusinessType:
            raise InvalidArgument(business_type)
        self.business_type = business_type
        self.sell_or_buy = sell_or_buy
        self.price = price
        self.amount = amount
        self.stop_price = stop_price
        self.order_asset = order_asset

    def validate(self):
        return getattr(self, "validate_{business_type}".format(
            business_type=self.business_type.value))()

    def get_spot_market_check_depth_range(self) -> Tuple[Decimal, Decimal]:
        market_info = MarketCache(self.market).dict
        depths = market_info["depths"]
        return Decimal(market_info["default_depth"]), max(depths)


class MarketOrderPriceTool(BasePriceTool):

    order_type = OrderType.MARKET_ORDER_TYPE

    # 市价单
    NORMAL_MAX_BUY_RATE = Decimal('0.1')
    NORMAL_MAX_SELL_RATE = Decimal('0.1')

    MARGIN_MAX_BUY_RATE = Decimal('0.1')
    MARGIN_MAX_SELL_RATE = Decimal('0.1')

    PERPETUAL_MAX_BUY_RATE = Decimal('0.03')
    PERPETUAL_MAX_SELL_RATE = Decimal('0.03')

    def _get_last_price(self, client: Union[ServerClient, PerpetualServerClient]):
        try:
            result = client.market_last(self.market)
            last = Decimal(result)
        except Exception as ex:
            current_app.logger.info(ex)
            current_app.logger.info('get market last error')
            result = client.market_last(self.market)
            last = Decimal(result)
        return last

    @cached_property
    def normal_last_price(self):
        market_server_client = ServerClient()
        return self._get_last_price(market_server_client)

    @cached_property
    def perpetual_index_price(self):
        market_client = PerpetualServerClient()
        market_status = market_client.get_market_status(self.market, 86400)
        index_price = Decimal(market_status.get('index_price'))
        return index_price

    def get_check_price(self):
        if self.business_type == OrderBusinessType.NORMAL_BUSINESS_TYPE:
            last_price = self.normal_last_price
        elif self.business_type == OrderBusinessType.MARGIN_BUSINESS_TYPE:
            try:
                cache = IndexPriceCache()
                index_price = cache.get_price(self.market)
            except (KeyError, ValueError):
                raise MarginIndexPriceError
            if not index_price or index_price <= 0:
                raise MarginIndexPriceError
            last_price = index_price
        else:
            last_price = self.perpetual_index_price
        return last_price

    def get_rate(self):
        if self.business_type in (
                OrderBusinessType.NORMAL_BUSINESS_TYPE,
                OrderBusinessType.MARGIN_BUSINESS_TYPE):
            _default = SpotMarketDeviationCache.DEFAULT_RATE
            _cache = SpotMarketPriceDeviationCache(self.business_type, self.sell_or_buy)
            rate = Decimal(_cache.hget(self.market) or _default)
            if not rate:
                rate = self.NORMAL_MAX_BUY_RATE
        elif self.business_type in [OrderBusinessType.PERPETUAL_BUSINESS_TYPE]:
            _default = PerpetualMarketDeviationCache.DEFAULT_RATE
            _cache = PerpetualMarketPriceDeviationCache(self.sell_or_buy)
            rate = Decimal(_cache.hget(self.market) or _default)
            if not rate:
                rate = self.PERPETUAL_MAX_BUY_RATE
        else:
            raise InvalidArgument
        return rate

    def _get_estimated_deal_price(self, min_merge: Decimal, max_merge: Decimal):
        if self.business_type == OrderBusinessType.PERPETUAL_BUSINESS_TYPE:
            order_server_client = PerpetualServerClient()
        else:
            order_server_client = ServerClient()
            market_info = MarketCache(self.market).dict
            base_asset, quote_asset = market_info['base_asset'], market_info['quote_asset']
            if not self.order_asset:
                if self.sell_or_buy == OrderSideType.BUY:
                    self.order_asset = quote_asset
                else:
                    self.order_asset = base_asset

        generate_merges = []
        start_merge = min_merge
        while start_merge <= max_merge:
            generate_merges.append(start_merge)
            start_merge *= 10

        for _merge in generate_merges:
            depth = order_server_client.market_order_depth(market=self.market,
                                                           limit=DEFAULT_DEPTH_LIMIT,
                                                           interval=str(_merge))
            deal_amount = Decimal('0')
            index = 0
            if len(depth["bids"]) == 0 and self.sell_or_buy == OrderSideType.SELL:
                raise OrderExceptionMap[OrderException.MERGE_DEPTH_NOT_MATCH]
            if len(depth["asks"]) == 0 and self.sell_or_buy == OrderSideType.BUY:
                raise OrderExceptionMap[OrderException.MERGE_DEPTH_NOT_MATCH]
            orders = depth["bids"] if self.sell_or_buy == OrderSideType.SELL else depth["asks"]
            for item in orders:
                if self.business_type == OrderBusinessType.PERPETUAL_BUSINESS_TYPE:
                    deal_amount += Decimal(item[1])
                else:
                    if self.order_asset == base_asset:
                        deal_amount += Decimal(item[1])
                    else:
                        deal_amount += Decimal(item[0]) * Decimal(item[1])

                price = Decimal(item[0])
                index += 1
                if deal_amount >= self.amount:
                    return price

        raise OrderExceptionMap[OrderException.PRICE_NOT_MATCH](placeholder=gettext('交易量'))

    def _get_price_range(self):
        price_map = OrderPriceLimitCache(self.market, OrderType.MARKET_ORDER_TYPE).read()
        if price_map:
            # 没拿到价格的时候不检查
            max_price = Decimal(price_map["max_price"])
            min_price = Decimal(price_map["min_price"])
            return min_price, max_price
        return None, None

    def validate_margin(self):
        last_price = self.get_check_price()
        min_depth, max_depth = self.get_spot_market_check_depth_range()
        deal_price = self._get_estimated_deal_price(min_depth, max_depth)
        rate = self.get_rate()
        lang = get_locale()
        if lang and lang.text_direction == 'rtl':
            rate_str = format_percent(rate, format='%')
        else:
            rate_str = format_percent(rate, format='.%')
        if self.sell_or_buy == OrderSideType.SELL:
            if deal_price < last_price * (1 - rate):
                err_code = OrderException.MARGIN_MARKET_SELL_ORDER_PRICE_OVER_DEVIATION
                raise OrderExceptionMap[err_code](rate=rate_str)
        else:
            if deal_price > last_price * (1 + rate):
                err_code = OrderException.MARGIN_MARKET_BUY_ORDER_PRICE_OVER_DEVIATION
                raise OrderExceptionMap[err_code](rate=rate_str)

    def validate_normal(self):
        min_depth, max_depth = self.get_spot_market_check_depth_range()
        deal_price = self._get_estimated_deal_price(min_depth, max_depth)
        min_price, max_price = self._get_price_range()
        if self.sell_or_buy == OrderSideType.SELL:
            if min_price and deal_price < min_price:
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, min_price, None, True)
        else:
            if max_price and deal_price > max_price:
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, max_price, None, True)
        last_price = self.get_check_price()
        rate = self.get_rate()
        if self.sell_or_buy == OrderSideType.SELL:
            if deal_price < last_price * (1 - rate):
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, rate)
        else:
            if deal_price > last_price * (1 + rate):
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, rate)

    def validate_perpetual(self):
        info = PerpetualMarketCache().get_market_info(self.market)
        if not info:
            raise InvalidArgument
        min_check_depth = Decimal(info["default_merge"])
        max_depth = max(map(Decimal, info["merge"]))
        last_price = self.get_check_price()
        deal_price = self._get_estimated_deal_price(min_check_depth, max_depth)
        rate = self.get_rate()
        if self.sell_or_buy == OrderSideType.SELL:
            if deal_price < last_price * (1 - rate):
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, rate)
        else:
            if deal_price > last_price * (1 + rate):
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, rate)


class StopMarketOrderPriceTool(MarketOrderPriceTool):

    order_type = OrderType.STOP_MARKET_ORDER_TYPE

    # 计划市价单
    NORMAL_MAX_BUY_RATE = Decimal('0.2')
    NORMAL_MAX_SELL_RATE = Decimal('0.2')

    MARGIN_MAX_BUY_RATE = Decimal('0.2')
    MARGIN_MAX_SELL_RATE = Decimal('0.2')

    PERPETUAL_MAX_BUY_RATE = Decimal('0.2')
    PERPETUAL_MAX_SELL_RATE = Decimal('0.2')

    def _validate(self):
        min_depth, max_depth = self.get_spot_market_check_depth_range()
        deal_price = self._get_estimated_deal_price(min_depth, max_depth)
        rate = self.get_rate()
        if self.sell_or_buy == OrderSideType.SELL:
            if deal_price < self.stop_price * (1 - rate):
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, rate)
        else:
            if deal_price > self.stop_price * (1 + rate):
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, rate)

    def validate_margin(self):
        self._validate()

    def validate_normal(self):
        self._validate()

    def validate_perpetual(self):
        info = PerpetualMarketCache().get_market_info(self.market)
        if not info:
            raise InvalidArgument
        min_check_depth = Decimal(info["default_merge"])
        max_depth = max(map(Decimal, info["merge"]))
        deal_price = self._get_estimated_deal_price(min_check_depth, max_depth)
        rate = self.get_rate()
        if self.sell_or_buy == OrderSideType.SELL:
            if deal_price < self.stop_price * (1 - rate):
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, rate)
        else:
            if deal_price > self.stop_price * (1 + rate):
                raise_helper(self.business_type, self.order_type, self.sell_or_buy, rate)


class LimitOrderPriceTool(BasePriceTool):

    order_type = OrderType.LIMIT_ORDER_TYPE
    # 限价单
    MARGIN_MAX_BUY_RATE = Decimal('0.2')
    MARGIN_MAX_SELL_RATE = Decimal('0.2')

    PERPETUAL_MAX_BUY_RATE = Decimal('0.2')
    PERPETUAL_MAX_SELL_RATE = Decimal('0.2')

    def validate_margin(self):
        # 移除杠杆单周期限价
        # 第二步，判断 委托价与指数价的偏离情况
        if self.sell_or_buy == OrderSideType.SELL:
            rate = PriceVerifySettings.limit_margin_sell_rate
        else:
            rate = PriceVerifySettings.limit_margin_buy_rate
        try:
            cache = IndexPriceCache()
            index_price = cache.get_price(self.market)
        except (KeyError, ValueError):
            raise MarginIndexPriceError
        if not index_price or index_price <= 0:
            raise MarginIndexPriceError

        if self.sell_or_buy == OrderSideType.SELL:
            if self.price < index_price * (Decimal('1') - rate):
                raise_helper(self.business_type, self.order_type,
                             self.sell_or_buy, rate)
        else:
            if self.price > index_price * (1 + rate):
                raise_helper(self.business_type, self.order_type,
                             self.sell_or_buy, rate)

    def validate_normal(self):
        # 单周期限价
        result = OrderPriceLimitCache(self.market).read()
        # 没拿到价格的时候不检查
        if not result:
            return
        max_price = Decimal(result['max_price'])
        min_price = Decimal(result['min_price'])
        market_info = MarketCache(self.market).dict
        precision = int(market_info['quote_asset_precision'])
        if self.sell_or_buy == OrderSideType.SELL:
            if self.price < min_price:
                raise_helper(self.business_type, self.order_type,
                             self.sell_or_buy, min_price, precision, True)
        else:
            if self.price > max_price:
                raise_helper(self.business_type, self.order_type,
                             self.sell_or_buy, max_price, precision, True)

    def validate_perpetual(self):
        if self.sell_or_buy == OrderSideType.SELL:
            rate = PriceVerifySettings.limit_perpetual_sell_rate
        else:
            rate = PriceVerifySettings.limit_perpetual_buy_rate
        if self.market in PERPETUAL_SPECIAL_MARKETS:
            rate = PERPETUAL_SPECIAL_MARKETS_LIMIT_ORDER_RATE
        market_client = PerpetualServerClient()
        market_status = market_client.get_market_status(self.market, 86400)
        index_price = Decimal(market_status.get('index_price'))
        if self.sell_or_buy == OrderSideType.SELL:
            if self.price < index_price * (Decimal('1') - rate):
                raise_helper(self.business_type, self.order_type,
                             self.sell_or_buy, rate)
        else:
            if self.price > index_price * (Decimal('1') + rate):
                raise_helper(self.business_type, self.order_type,
                             self.sell_or_buy, rate)


class StopLimitOrderPriceTool(BasePriceTool):

    order_type = OrderType.STOP_LIMIT_ORDER_TYPE

    # 计划委托单
    NORMAL_MAX_BUY_RATE = Decimal('1')
    NORMAL_MAX_SELL_RATE = Decimal('0.5')

    MARGIN_MAX_BUY_RATE = Decimal('0.2')
    MARGIN_MAX_SELL_RATE = Decimal('0.2')

    PERPETUAL_MAX_BUY_RATE = Decimal('0.2')
    PERPETUAL_MAX_SELL_RATE = Decimal('0.2')

    def validate(self):
        key = 'stop_limit_{business_type}_{sell_or_buy}_rate'.format(
            business_type=self.business_type.value,
            sell_or_buy=self.sell_or_buy.name
        ).lower()
        rate = getattr(PriceVerifySettings, key)

        if self.business_type == OrderBusinessType.PERPETUAL_BUSINESS_TYPE \
                and self.market in PERPETUAL_SPECIAL_MARKETS:
            rate = PERPETUAL_SPECIAL_MARKETS_STOP_LIMIT_ORDER_RATE
        if self.sell_or_buy == OrderSideType.SELL:
            if self.price < self.stop_price * (Decimal('1') - rate):
                raise_helper(self.business_type, self.order_type,
                             self.sell_or_buy, rate)
        else:
            if self.price > self.stop_price * (1 + rate):
                raise_helper(self.business_type, self.order_type,
                             self.sell_or_buy, rate)


class OrderFeeOption:

    _no_discount = Decimal(1)

    def __init__(self, market_cache: dict, user_id: int, use_in_asset=False):
        # AMM市场不支持CET抵扣，收取交易所得币种作为手续费。
        # 普通市场手续费币种优先级为CET(开启抵扣) > MONEY > STOCK.
        if AmmMarketCache.has(market_cache['name']):
            self._fee_asset = None
            self._fee_discount = self._no_discount
            self._option = OrderOption.NORMAL
        elif UserPreferences(user_id).cet_discount_enabled:
            self._fee_asset = SiteSettings.fee_deduction_asset
            self._fee_discount = FeeFetcher(user_id).fetch_fee_deduction_rate(market_cache['name'])
            self._option = OrderOption.USE_MONEY_FEE
        else:
            self._fee_asset = None
            self._fee_discount = self._no_discount
            self._option = OrderOption.USE_MONEY_FEE
        if use_in_asset:
            # 使用交易所得币种作为手续费
            self._option = OrderOption.NORMAL

    @property
    def fee_asset(self) -> Optional[str]:
        return self._fee_asset

    @property
    def fee_discount(self) -> Decimal:
        return self._fee_discount

    @property
    def option(self) -> OrderOption:
        return self._option

    def with_option(self, option: OrderOption = None, hide: bool = False) -> OrderOption:
        op = self._option
        if option:  # 过滤用户不允许使用的option
            value = 0
            for _op in USER_ORDER_OPTIONS:
                value |= _op
            option &= value
            op |= option
        if hide:
            op |= OrderOption.HIDE
        return op


class Order(object):
    class OrderSourceType(Enum):
        API_V1 = 'api.v1'
        API_V2 = 'api.v2'
        WEB = 'web'
        IOS = 'iOS'
        ANDROID = 'Android'
        UNKNOWN = 'unknown'
        SYSTEM = 'system'

    class OrderStatusType(Enum):
        PART_DEAL = 'part_deal'
        NOT_DEAL = 'not_deal'
        DONE = 'done'
        CANCEL = 'cancel'

    @unique
    class StopOrderStatusType(IntEnum):
        ACTIVE = 1
        FAIL = 2
        CANCEL = 3

    @unique
    class NormalOrderType(IntEnum):
        LIMIT = 1
        MARKET = 2

    @unique
    class StopOrderType(IntEnum):
        STOP_LIMIT = 1
        STOP_MARKET = 2

    @unique
    class OrderSideType(IntEnum):
        SELL = 1
        BUY = 2

    class TradeRoleType(Enum):
        MAKER = 1
        TAKER = 2

    StatusThreshold = Decimal('0.01')

    @classmethod
    def get_status(cls, value: Tuple[Decimal, Decimal, int, bool, Union[Decimal, None]]):
        deal_amount, amount, order_type, is_pending, min_amount = value
        if cls.NormalOrderType(order_type) == cls.NormalOrderType.MARKET:
            """
                委托数量-成交数量）>=min{（1%*委托数量），最小下单数量}
                若是大于【较小者】，则成交状态定义为“部分成交”
                若是小于【较小者】，则成交状态定义为“全部成交”
            """
            threshold_val = cls.StatusThreshold * amount
            min_val = min(cls.StatusThreshold * amount, min_amount) if min_amount is not None else threshold_val
            if amount and (amount - deal_amount) >= min_val:
                return Order.OrderStatusType.PART_DEAL.value
            else:
                return Order.OrderStatusType.DONE.value

        if amount == deal_amount:
            return Order.OrderStatusType.DONE.value
        elif is_pending and amount > deal_amount > 0:
            return Order.OrderStatusType.PART_DEAL.value
        elif is_pending and deal_amount == 0:
            return Order.OrderStatusType.NOT_DEAL.value
        elif not is_pending:
            return Order.OrderStatusType.CANCEL.value
        return ''

    @classmethod
    def get_stop_status(cls, value):
        try:
            return cls.StopOrderStatusType(value).name.lower()
        except KeyError:
            return ""

    class StopStatusFields(fields.Raw):
        def format(self, value):
            return Order.get_stop_status(value)

    class StatusFields(fields.Raw):
        def format(self, value: Tuple[Dict, int, bool]):
            order, order_type, is_pending = value
            option, side, market, amount = order['option'], order["side"], order["market"], order["amount"]
            try:
                market_info = MarketCache(market).dict
            except RuntimeError:
                return ''  # market not exist
            base_asset, quote_asset = market_info["base_asset"], market_info["quote_asset"]
            min_amount = None
            if AssetCache.has_asset(base_asset):
                min_amount = get_asset_config(base_asset).min_order_amount
            is_amount_reversed = bool(option & OrderOption.REVERSE_AMOUNT)  # 市价单以交易币种数量下买单或以定价币种数量下卖单时，值为True
            if order_type == OrderIntType.MARKET:
                if (side == OrderSideType.SELL and is_amount_reversed) or (
                        side == OrderSideType.BUY and not is_amount_reversed):
                    deal_amount = Decimal(order['deal_money'])
                    if min_amount is not None:
                        min_amount = PriceManager.asset_to_asset(base_asset, quote_asset) * min_amount
                else:
                    deal_amount = Decimal(order['deal_stock'])
            else:  # 限价单+剩余情况的市价单
                deal_amount = Decimal(order['deal_stock'])

            return Order.get_status((deal_amount, Decimal(amount), order_type, is_pending, min_amount))

    class SystemOrderFields(fields.Raw):
        def format(self, value):
            if value == Order.OrderSourceType.SYSTEM.value:
                return 1
            return 0

    @staticmethod
    def get_amount(value: Tuple[Decimal, int, int]) -> str:
        amount, type_, side = value
        if Order.NormalOrderType(type_) == Order.NormalOrderType.MARKET and \
                Order.OrderSideType(side) == Order.OrderSideType.BUY:
            return "-"
        return str(amount)

    class AmountFields(fields.Raw):
        def format(self, value):
            return Order.get_amount(value)

    class OrderStopTypeFields(fields.Raw):
        def format(self, value):
            return Order.StopOrderType(value).name.lower()

    @staticmethod
    def get_avg_price(value: Tuple[Decimal, Decimal, str]) -> str:
        deal_amount, deal_money, market = value
        if deal_amount <= 0:
            return '0.00'
        avg_price = deal_money / deal_amount
        coin_places = get_market_price_prices(market)
        return amount_to_str(avg_price, coin_places)

    class AvgPriceFields(fields.Raw):
        def format(self, value):
            return Order.get_avg_price(value)

    class SpotOrderEffectField(fields.Raw):

        def format(self, value):
            option = value
            types = (
                OrderOption.IOC,
                OrderOption.FOK,
            )
            for _type in types:
                if option & _type == _type:
                    return _type.effect_type_name
            # NORMAL
            return OrderOption.NORMAL.effect_type_name

    class PerpetualOrderEffectField(fields.Raw):

        def format(self, value):
            # AL对应1，IOC对应2，FOK是3
            # 0 对应市价单，这里返回空字符串
            types = {
                1: OrderOption.NORMAL,
                2: OrderOption.IOC,
                3: OrderOption.FOK,
            }
            effect_type = value & 0b11
            if effect_type == 0:
                return ''
            for _type, option_type in types.items():
                if effect_type == _type:
                    return option_type.effect_type_name
            return OrderOption.NORMAL.effect_type_name

    @staticmethod
    def get_api_avg_price(value: Tuple[Decimal, Decimal, str]) -> str:
        deal_amount, deal_money, market = value
        if deal_amount <= 0:
            return '0.00'
        avg_price = deal_money / deal_amount

        return amount_to_str(avg_price, 20)

    class AvgApiPriceFields(fields.Raw):
        def format(self, value: Tuple[Decimal, Decimal, str]) -> str:
            return Order.get_api_avg_price(value)

    class PriceFields(fields.Raw):
        def format(self, value: Tuple[Decimal, str]) -> str:
            price, market = value
            coin_places = get_market_price_prices(market)
            return amount_to_str(price, coin_places)

    class BasePrecisionFields(fields.Raw):
        def format(self, value: Tuple[Decimal, str]) -> str:
            data, market = value
            coin_places = get_market_quote_asset_precision(market)
            return amount_to_str(data, coin_places)

    DealAmountFields = QuotePrecisionFields = PriceFields

    class MarketPriceFields(fields.Raw):
        def format(self, value: Tuple[Decimal, str, int]) -> str:
            price, market, order_type = value
            coin_places = get_market_price_prices(market)
            if Order.NormalOrderType(order_type) == Order.NormalOrderType.MARKET:
                return '-'
            return amount_to_str(price, coin_places)

    class MarketMarginOrderTypeFields(fields.Raw):
        def format(self, value: Tuple[int, int]) -> str:
            order_type_idx, account_id = value
            order_type = Order.NormalOrderType(order_type_idx)

            if 0 < int(account_id) < 10000:
                return order_type.name.lower() + "(margin)"
            return order_type.name.lower()

    class DealFeeFields(fields.Raw):
        """兼容旧接口，deal_fee字段已被money_fee和stock_fee字段取代"""

        def format(self, value: Tuple[str, str]):
            stock_fee, money_fee = value
            if Decimal(money_fee) > 0:
                return money_fee
            return stock_fee

    class AmountAssetFields(fields.Raw):
        def format(self, value: Tuple[str, OrderSideType, Union[OrderIntType, StopOrderIntType], int]) -> str:
            market, sell_or_buy, order_type, option = value
            try:
                base_asset, quote_asset = get_market_assets(market)
            except RuntimeError:
                return ''  # market not exist
            if order_type in (OrderIntType.MARKET, StopOrderIntType.STOP_MARKET):
                is_amount_reversed = bool(option & OrderOption.REVERSE_AMOUNT)
                if sell_or_buy == OrderSideType.BUY:
                    return base_asset if is_amount_reversed else quote_asset
                else:
                    return quote_asset if is_amount_reversed else base_asset
            else:
                return base_asset


@mem_cached(600)
def get_market_price_prices(market_name: str):
    if precision := MarketCache.get_market_price_precision(market_name):
        return int(precision)
    return 20


@mem_cached(600)
def get_market_quote_asset_precision(market_name: str):
    if market_name in MarketCache.list_online_markets():
        cache = MarketCache(market_name).dict
        if cache:
            return cache['quote_asset_precision']
    return 20


@mem_cached(600)
def get_market_base_asset_precision(market_name: str):
    if market_name in MarketCache.list_online_markets():
        cache = MarketCache(market_name).dict
        if cache:
            return cache['base_asset_precision']
    return 20


@mem_cached(600)
def get_market_assets(market_name: str):
    market_info = MarketCache(market_name).dict
    return market_info['base_asset'], market_info['quote_asset']
