from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from flask import current_app
from sqlalchemy import func

from app import config
from app.business import Server<PERSON><PERSON>, <PERSON>acheLock, LockKeys, ServerResponseCode
from app.business.p2p.message import (P2pMarginExcessRefundMessage, P2pMarginPenaltyMessage,
                                      P2pMerCompensationMessage, P2pUserCompensationMessage)
from app.business.p2p.utils import send_p2p_alert, check_margin_enough_balance
from app.common import BalanceBusiness
from app.exceptions import OperationNotAllowed
from app.exceptions.basic import InvalidArgument
from app.exceptions.p2p import P2pExceptionMap, P2pExceptionCode
from app.models import P2pUserMargin, P2pMarginHistory, db, P2pUserMarginHistory
from app.models.user import SignOffUser, P2pMerchant
from app.utils import now, amount_to_str

FROM = 'from'
TO = 'to'


class P2pUserMarginHistoryBiz:
    u_model = P2pUserMargin
    his_model = P2pUserMarginHistory
    sys_model = P2pMarginHistory
    SYSTEM_ID = config['P2P_MARGIN_USER_ID']
    MARGIN_FIX_START_MIN = 3
    MARGIN_FIX_END_MIN = 120

    ASSET = "USDT"

    BIZ_SYS_TYPE_MAP = {
        his_model.BizType.MER_PAYMENT: sys_model.Type.PAYMENT,
        his_model.BizType.MER_REFUND: sys_model.Type.REFUND,
        his_model.BizType.EXCESS_REFUND: sys_model.Type.REFUND,
        his_model.BizType.MER_COMPENSATION: sys_model.Type.REFUND,
        his_model.BizType.MER_PENALTY: sys_model.Type.REFUND,
    }

    @classmethod
    def payment(cls, user_id, amount):
        assert amount >= Decimal()
        check_margin_enough_balance(user_id, amount, cls.ASSET)
        his_row = cls._create_pending_row(
            user_id, amount, cls.his_model.BizType.MER_PAYMENT, to_user_id=cls.SYSTEM_ID)
        cls.process_margin_trans(user_id, his_row.id)

    @classmethod
    def refund(cls, user_id):
        user_margin = cls.u_model.get_user_row(user_id)
        if not user_margin:
            raise OperationNotAllowed("no user margin found")
        amount = user_margin.paid_margin
        cls._check_system_balance(amount)
        his_row = cls._create_pending_row(
            user_id, amount, cls.his_model.BizType.MER_REFUND, to_user_id=user_id)
        cls.process_margin_trans(user_id, his_row.id)

    @classmethod
    def pending_excess_refund(cls, user_id, amount, biz_remark):
        assert amount > Decimal()
        cls._check_user_margin(
            user_id, cls.his_model.BizType.EXCESS_REFUND, amount)
        cls._check_system_balance(amount)
        return cls._create_pending_row(
            user_id, amount, cls.his_model.BizType.EXCESS_REFUND, to_user_id=user_id, biz_remark=biz_remark)

    @classmethod
    def pending_deduct_for_user(cls, user_id: int, to_user_id: int, amount: Decimal, biz_type: his_model.BizType, 
                                biz_remark: str = "", order_id: int = None):
        assert amount > Decimal()
        assert biz_type in cls.his_model.deduct_types()
        cls._check_user_margin(user_id, biz_type, amount)
        cls._check_system_balance(amount)
        return cls._create_pending_row(
            user_id, amount, biz_type, to_user_id, biz_remark, order_id)

    @classmethod
    def process_margin_trans(cls, user_id, his_id: int, audit_user_id: int = None):
        his_row: P2pUserMarginHistory = cls._process_margin_trans(
            user_id, his_id, audit_user_id)
        user_margin = cls._transferring(his_id)

        try:
            cls._send_message(his_row)
        except Exception as e:
            current_app.logger.error(
                f"send p2p margin message error: {e}, his_row: {his_row.id}, user_id: {user_id}")
        return user_margin
    
    @classmethod
    def _send_message(cls, his_row):
        # 根据不同的类型发送消息
        user_id = his_row.user_id
        match his_row.biz_type:
            case cls.his_model.BizType.MER_COMPENSATION:
                P2pMerCompensationMessage().send_message(user_id, his_row.amount)
                P2pUserCompensationMessage().send_message(his_row.to_user_id, his_row.amount)
            case cls.his_model.BizType.MER_PENALTY:
                P2pMarginPenaltyMessage().send_message(user_id, his_row.amount)
            case cls.his_model.BizType.EXCESS_REFUND:
                P2pMarginExcessRefundMessage().send_message(user_id, his_row.amount)

    @classmethod
    def _process_margin_trans(cls, user_id, his_id: int, audit_user_id: int = None):
        # 检查是否存在进行中的任务
        with CacheLock(LockKeys.p2p_user_margin(user_id)):
            db.session.rollback()
            his_row = cls.his_model.query.get(his_id)
            cls._check_user_signoff(his_row)
            if his_row.status != cls.his_model.Status.PENDING:
                raise OperationNotAllowed(
                    message="his row status is not PENDING")
            cls._check_user_margin(
                his_row.user_id, his_row.biz_type, his_row.amount)
            # 商家缴纳和退还保证金对象都是系统ID
            # 只有被划扣时对象才算用户ID
            his_row.status = cls.his_model.Status.PROCESS
            if audit_user_id:
                his_row.audit_user_id = audit_user_id
                his_row.audit_at = now()

            if his_row.biz_type in cls.his_model.refund_types():
                from_id = cls.SYSTEM_ID
                to_id = his_row.user_id
            elif his_row.biz_type in cls.his_model.payment_types():
                from_id = his_row.user_id
                to_id = cls.SYSTEM_ID
            else:
                from_id = cls.SYSTEM_ID
                to_id = his_row.to_user_id

            sys_row = P2pSysMarginBiz.init(
                from_id, to_id, his_row.amount, cls.BIZ_SYS_TYPE_MAP[his_row.biz_type])
            his_row.sys_his_id = sys_row.id

            db.session.commit()
            return his_row
        
    @classmethod
    def _check_user_signoff(cls, his_row):
        if his_row.biz_type in cls.his_model.refund_types() and SignOffUser.is_signoff_user(his_row.user_id):
            his_row.status = cls.his_model.Status.FAIL
            his_row.remark = "商家账号已注销，无法退回现货账户"
            db.session.commit()
            raise InvalidArgument(message="用户已注销")

    @classmethod
    def _check_user_margin(cls, user_id, biz_type, amount):
        margin = cls.u_model.get_user_row(user_id)
        if not margin:
            raise OperationNotAllowed("商家保证金不存在")
        paid_margin = margin.paid_margin
        if biz_type == cls.his_model.BizType.MER_COMPENSATION:
            if paid_margin < amount:
                raise OperationNotAllowed(message="商家保证金余额低于赔付金额")
        elif biz_type == cls.his_model.BizType.MER_PENALTY:
            if paid_margin < amount:
                raise OperationNotAllowed(message="商家保证金余额低于违规扣减金额")
        elif biz_type == cls.his_model.BizType.EXCESS_REFUND:
            if paid_margin - amount < margin.require_margin:
                raise OperationNotAllowed(message="扣减后保证金余额低于应缴保证金，请先调整应缴保证金")

    @classmethod
    def _create_pending_row(cls, mer_id: int, amount: Decimal, biz_type: his_model.BizType,
                            to_user_id: int = None, biz_remark: str = "", order_id: int = None):
        with CacheLock(LockKeys.p2p_user_margin(mer_id)):
            db.session.rollback()
            # 检查不存在 pending 或者 process 的记录
            if cls.his_model.query.filter(
                cls.his_model.user_id == mer_id,
                cls.his_model.status.in_([
                    cls.his_model.Status.PENDING,
                    cls.his_model.Status.PROCESS
                ])
            ).first():
                raise P2pExceptionMap[P2pExceptionCode.MARGIN_PROCESS]
            his_row = P2pUserMarginHistory(
                user_id=mer_id,
                to_user_id=to_user_id,
                amount=amount,
                biz_type=biz_type,
                biz_remark=biz_remark,
            )
            if order_id:
                his_row.p2p_order_id = order_id
            db.session_add_and_commit(his_row)
            return his_row

    @classmethod
    def _transferring(cls, his_id):
        with CacheLock(LockKeys.p2p_margin_trans(his_id)):
            db.session.rollback()
            his_row = cls.his_model.query.get(his_id)
            if cls._check_his_status(his_row):
                return
            is_deduct = True if his_row.biz_type in his_row.deduct_types() else False
            sys_row = cls.sys_model.query.get(his_row.sys_his_id)
            try:
                P2pSysMarginBiz.transferring(sys_row, is_deduct)
                return cls._transfer_success(his_row)
            except ServerClient.BadResponse as e:
                if e.code == ServerResponseCode.DUPLICATE_BALANCE_UPDATE:
                    raise P2pExceptionMap[P2pExceptionCode.PAID_MARGIN]
                if e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                    if his_row.biz_type in cls.his_model.payment_types():
                        cls._transfer_fail(his_row, f"商家现货余额不足取消")
                        raise P2pExceptionMap[P2pExceptionCode.MARGIN_PAYMENT_ERROR]
                    else:
                        cls._transfer_fail(his_row, f"P2P保证金系统账户余额不足取消")
                        # 系统账号余额不足，告警
                        err_content = (f"当前系统账户 {sys_row.from_id} 余额不足"
                                       f"无法转入用户 {sys_row.to_id} 保证金，请检查流水记录")
                        send_p2p_alert(err_content)
                        raise P2pExceptionMap[P2pExceptionCode.MARGIN_REFUND_ERROR]
                raise e

    @classmethod
    def _transfer_success(cls, his_row):
        his_row.status = cls.his_model.Status.SUCCESS
        return cls._update_user_margin(his_row)
    
    @classmethod
    def _transfer_fail(cls, his_row, remark: str):
        his_row.status = cls.his_model.Status.FAIL
        his_row.remark = remark
        db.session.commit()

    @classmethod
    def _update_user_margin(cls, his_row: his_model):
        if his_row.biz_type == cls.his_model.BizType.MER_PAYMENT:
            amount = his_row.amount
        else:
            amount = -his_row.amount
        # 修改商家保证金余额
        user_margin: P2pUserMargin = cls.u_model.query.filter(
            cls.u_model.user_id == his_row.user_id).first()
        # 如果划扣后保证金余额小于应缴保证金，增加宽限期
        if his_row.biz_type in his_row.deduct_types():
            user_margin.extend_deadline(P2pUserMargin.GraceSource.EXIST)
        elif his_row.biz_type == cls.his_model.BizType.MER_REFUND:
            # 取消商家身份
            m_model = P2pMerchant
            mer_row = m_model.query.filter(m_model.user_id == his_row.user_id).first()
            mer_row.status = m_model.Status.CANCELED
            mer_row.margin_status = m_model.MarginStatus.INVALID
        user_margin.paid_margin += amount
        # 更新历史记录当前余额
        his_row.balance = user_margin.paid_margin
        db.session.commit()
        return user_margin

    @classmethod
    def _check_his_status(cls, his_row):
        match his_row.status:
            case cls.his_model.Status.PROCESS:
                return False
            case cls.his_model.Status.SUCCESS:
                return True
            case _:
                raise OperationNotAllowed(
                    message="his row status is not PROCESS")

    @classmethod
    def _get_system_balance(cls):
        asset = cls.ASSET
        return ServerClient().get_user_balances(cls.SYSTEM_ID, asset=asset).get(asset, {}).get('available', 0)

    @classmethod
    def _check_system_balance(cls, amount: Decimal):
        balance = cls._get_system_balance()
        if balance < amount:
            err = f"P2P保证金系统账户 {cls.SYSTEM_ID} 余额不足，取消执行"
            err_content = f"{err}，无法转出 {amount} USDT 保证金，请检查资金流水记录"
            send_p2p_alert(err_content)
            raise OperationNotAllowed(message=err)

    @classmethod
    def _check_exist_process_row(cls, user_id):
        if cls.his_model.get_user_query(user_id).filter(
                cls.his_model.status.in_([
                    cls.his_model.Status.PENDING,
                    cls.his_model.Status.PROCESS
                ])
        ).first():
            raise P2pExceptionMap[P2pExceptionCode.MARGIN_PROCESS]

    @classmethod
    def fixup_mer_payment_refund_pending(cls):
        his_rows = cls.his_model.query.filter(
            cls.his_model.created_at < now() - timedelta(minutes=cls.MARGIN_FIX_START_MIN),
            cls.his_model.created_at >= now() - timedelta(minutes=cls.MARGIN_FIX_END_MIN),
            cls.his_model.status == cls.his_model.Status.PENDING,
            cls.his_model.biz_type.in_(list(cls.his_model.mer_op_types())),
        ).all()
        for his_row in his_rows:
            with CacheLock(LockKeys.p2p_margin_trans(his_row.id)):
                db.session.rollback()
                his_row = cls.his_model.query.get(his_row.id)
                if his_row.status != cls.his_model.Status.PENDING:
                    continue
                cls.process_margin_trans(his_row.user_id, his_row.id)

    @classmethod
    def fixup_margin_trans_process(cls):
        status_map = {
            cls.sys_model.Status.SUCCESS: cls.his_model.Status.SUCCESS,
            cls.sys_model.Status.FAIL: cls.his_model.Status.FAIL,
        }

        his_rows = cls.his_model.query.filter(
            cls.his_model.created_at < now() - timedelta(minutes=cls.MARGIN_FIX_START_MIN),
            cls.his_model.created_at >= now() - timedelta(minutes=cls.MARGIN_FIX_END_MIN),
            cls.his_model.status == cls.his_model.Status.PROCESS,
        ).all()
        client = ServerClient()
        for his_row in his_rows:
            with CacheLock(LockKeys.p2p_margin_trans(his_row.id)):
                db.session.rollback()
                his_row = cls.his_model.query.get(his_row.id)
                if his_row.status != cls.his_model.Status.PROCESS:
                    continue

                sys_row = cls.sys_model.query.get(his_row.sys_his_id)
                # 系统流水已执行完，同步用户保证金流水
                if sys_row.status != cls.sys_model.Status.PROCESS:
                    his_row.status = status_map[sys_row.status]
                    his_row.remark = sys_row.remark
                    db.session.commit()
                    continue

            asset = sys_row.asset
            from_ret = client.asset_query_business(
                sys_row.from_id, asset, cls.SYS_BUS_MAP[sys_row.type][FROM], sys_row.id)
            if not from_ret:
                cls._transferring(his_row.id)
                continue

            to_bus = cls.SYS_BUS_MAP[sys_row.type][TO]
            to_ret = client.asset_query_business(
                sys_row.to_id, asset, to_bus, sys_row.id)
            if to_ret:
                cls._transfer_success(sys_row, his_row)
                continue

            # 查资金记录二次确认
            ret = client.get_user_balance_history(
                sys_row.to_id, asset, business=to_bus)
            if ret and sys_row.id in {i["detail"]["id"] for i in ret}:
                cls._transfer_success(sys_row, his_row)
            else:
                try:
                    client.add_user_balance(
                        sys_row.to_id, asset, sys_row.amount, to_bus, sys_row.id)
                    cls._transfer_success(sys_row, his_row)
                except Exception as e:
                    current_app.logger.error(
                        f"p2p margin payment {sys_row.from_id} to {sys_row.id} error: {e}")

    @classmethod
    def margin_balance_check(cls):
        # 系统账户 和 所有商家的 paid_margin 对账告警
        system_balance = cls._get_system_balance()
        all_paid_margin = cls.u_model.query.with_entities(
            func.sum(cls.u_model.paid_margin)
        ).scalar()
        if system_balance != all_paid_margin:
            diff_amount = system_balance - all_paid_margin
            err_content = f"P2P 保证金对账不平，差额为{amount_to_str(diff_amount)} 请检查流水记录"
            send_p2p_alert(err_content)

    @classmethod
    def user_margin_negative_check(cls):
        # 用户负数保证金告警
        rows = cls.u_model.query.filter(
            cls.u_model.paid_margin < Decimal()
        ).all()
        err_content = ""
        for row in rows:
            err_content += f"\n 用户 {row.user_id} 保证金为负数 {row.paid_margin}，请检查流水记录"
        if err_content:
            send_p2p_alert(err_content)


class P2pSysMarginBiz:
    sys_model = P2pMarginHistory

    SYS_BUS_MAP = {
        P2pMarginHistory.Type.PAYMENT: {
            FROM: BalanceBusiness.P2P_MARGIN_PAYMENT,
            TO: BalanceBusiness.P2P_MARGIN_SYS_ADD,
        },
        P2pMarginHistory.Type.REFUND: {
            FROM: BalanceBusiness.P2P_MARGIN_SYS_SUB,
            TO: BalanceBusiness.P2P_MARGIN_REFUND,
        }
    }

    @classmethod
    def init(cls, from_id: int, to_id: int, amount: Decimal, type: sys_model.Type):
        # 外层提交
        sys_row = P2pMarginHistory(
            from_id=from_id,
            to_id=to_id,
            amount=amount,
            type=type,
        )
        db.session_add_and_flush(sys_row)
        return sys_row

    @classmethod
    def transferring(cls, sys_row, is_deduct=False):
        if cls._check_sys_status(sys_row):
            return sys_row
        # 不需要保证金的地区
        if sys_row.amount == Decimal():
            sys_row.status = cls.sys_model.Status.SUCCESS
            db.session.commit()
        asset = sys_row.asset
        row_id = sys_row.id

        if is_deduct:
            from_bus = BalanceBusiness.P2P_MARGIN_DEDUCT
            to_bus = BalanceBusiness.SYSTEM
        else:
            from_bus = cls.SYS_BUS_MAP[sys_row.type][FROM]
            to_bus = cls.SYS_BUS_MAP[sys_row.type][TO]
        try:
            ServerClient().batch_add_user_balance([
                dict(
                    user_id=sys_row.from_id,
                    asset=asset,
                    amount=-sys_row.amount,
                    business=from_bus,
                    business_id=row_id
                ),
                dict(
                    user_id=sys_row.to_id,
                    asset=asset,
                    amount=sys_row.amount,
                    business=to_bus,
                    business_id=row_id
                ),
            ])
            sys_row.status = cls.sys_model.Status.SUCCESS
            db.session.commit()
        except ServerClient.BadResponse as e:
            if e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                sys_row.status = cls.sys_model.Status.FAIL
                sys_row.remark = f"P2P保证金系统账户余额不足取消"
                db.session.commit()
            raise e

    @classmethod
    def _check_sys_status(cls, sys_row):
        match sys_row.status:
            case cls.sys_model.Status.PROCESS:
                return False
            case cls.sys_model.Status.SUCCESS:
                return True
            case _:
                raise OperationNotAllowed(
                    message="sys row status is not PROCESS")


def update_require_margin(row: P2pUserMargin, amount, source):
    if row.require_margin != amount:
        row.extend_deadline(source)
        row.require_margin = amount
