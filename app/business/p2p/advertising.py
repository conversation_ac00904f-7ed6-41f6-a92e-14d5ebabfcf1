import copy
from collections import defaultdict
from decimal import Decimal, ROUND_UP
from enum import Enum
from functools import partial, reduce
from typing import Any, NamedTuple

from flask import g, current_app
from flask_api.exceptions import AuthenticationFailed
from flask_babel import gettext as _, force_locale, gettext
from mongoengine import QuerySet
from sqlalchemy import or_, func

from app import Language
from app.models import db
from app.business.p2p.utils import unique_option
from app.business import SiteSettings, PriceManager
from app.business.lock import CacheLock, LockKeys
from app.business.p2p.config import p2p_setting
from app.business.p2p.message import AutoOfflineAdvertising
from app.business.p2p.pay_channel import PayChannelBus, UserPayChannelBus
from app.business.p2p.permission import PermissionBus
from app.business.p2p.user import P2pUserManger
from app.business.p2p.relation import P2pRelationManager
from app.business.p2p.utils import P2pUtils
from app.caches.p2p import UniqueOptionCache, AdvertisingBookCache, P2pAssetConfigCache, PayChannelCache, \
    P2pFiatLimitCache, FiatPayChannelCache
from app.common import P2pBusinessType, P2pAmountType, CeleryQueues
from app.exceptions import InvalidArgument
from app.exceptions.p2p import AdvAssetLimitError, OnlineAdvLimitError, P2pExceptionMap, P2pExceptionCode, \
    P2pAssetNotValidError, P2pFiatNotValidError, PayChannelInvalidError, FiatPayChannelError, P2pAdvStocksError
from app.models import User, KycVerificationPro
from app.models.mongo import P2pCountryFiatMySQL
from app.models.mongo.p2p.config import P2pFiatConfigMySQL, P2pAssetConfigMySQL
from app.models.mongo.p2p.mer_act import P2pMerActMySQL as P2pMerAct
from app.models.mongo.p2p.advertising import P2pAdvertisingMySQL, AutoOfflineAdvReason, \
    P2pAdvertisingChangeLogMySQL, P2pAdvListSnapshotMySQL, TradeStatisticsModel
from app.models.user import P2pMerchant, P2pUser
from app.utils import now, quantize_amount, group_by, amount_to_str, celery_task
from app.utils.helper import Struct


class TradeLimitation(Enum):
    COMPLETION_RATE = "完单率"  # 完单率 >=
    REGISTERED_TIME = "注册时间"  # 注册时间 >=
    IS_SAME_KYC_AREA = "对手方来自同KYC认证地区"  # 对手方来自同KYC认证地区 ==
    NON_ADVERTISER = "不和广告商交易"  # 不和广告商交易 ==


class Operator(Enum):
    EQ = "eq"  # 等于
    GE = "ge"  # 大于等于
    LE = "le"  # 小于等于


class P2pAdvParams(NamedTuple):
    price: Decimal
    stocks_quantity: Decimal
    is_manually_limit: bool
    limit_unit: str
    min_limit: Decimal
    max_limit: Decimal
    pay_channel_ids: list[str]
    user_channel_ids: list[str]


class P2pAdvertisingSchema:
    """
    广告参数校验:
    法币单笔限额:
    - 模型: P2pFiatConfig
    - 缓存: P2pFiatLimitCache
    - 数据来源: admin 配置 fait min_limit max_limit precision
    - 应用范围: 广告单笔限额
    - 单位: USDT
    - eg: 100 ~ 10000 USDT
    交易区限额:
    - 模型: P2pAssetConfig
    - 缓存: P2pAssetConfigCache
    - 数据来源: admin 配置 asset min_limit max_limit  precision
    - 应用范围: 广告库存限制  min_limit <= 商家库存 <= max_limit
    - 单位: asset
    - eg: 0.001 ~ 1 BTC
    广告配置参数:
    - 价格: price 单位 fiat  eg: 0.99 USD
    - 库存: stocks_quantity 单位 asset  eg: 1 BTC
    - 单笔限额限制: is_manually_limit 手动填写/自动获取
    - 单笔限制单位: limit_unit  可选: asset, fiat
    - 单笔限制最小值: min_limit
    - 单笔限制最大值: max_limit
    联动配置:
    库存限制:
        1. 交易区间最小值 <= 库存 <= 交易区间最大值
        2. 库存 > 单笔限额最大值

    """

    def __init__(self, user_id: int, adv_params: dict[str, Any], adv_id: str = None,
                 only_check_oline=False) -> None:
        self.user_id = user_id
        self.adv_type = adv_params['adv_type']
        self.base = adv_params['base']
        self.quote = adv_params['quote']
        self.status = adv_params['status']
        self._adv = P2pAdvertisingMySQL.get_user_adv_by_id(adv_id, self.user_id) if adv_id else None
        self._params: P2pAdvParams = None
        self._only_check_oline = only_check_oline
        self._init_params(adv_params, adv_id)

    def _get_stocks_quantity(self, stocks_quantity):
        req_stocks_quantity = self.fmt_base_amount(stocks_quantity)
        if not self._adv or self._only_check_oline:
            return req_stocks_quantity
        stocks_quantity = self._adv.lock_stocks_quantity + req_stocks_quantity
        return stocks_quantity

    def _init_params(self, adv_params: dict[str, Any], adv_id: str = None):
        pay_channel_ids, user_channel_ids = self._get_pay_channel_ids(
            self.user_id,
            self.adv_type,
            adv_params.get("pay_channel_ids")
        )
        if adv_id and not self._adv:
            raise InvalidArgument
        zero = Decimal()
        self._params = P2pAdvParams(
            price=self.fmt_quote_amount(adv_params['price']),
            stocks_quantity=self._get_stocks_quantity(adv_params['stocks_quantity']),
            is_manually_limit=adv_params['is_manually_limit'],
            limit_unit=adv_params.get('limit_unit', ""),
            min_limit=adv_params.get("min_limit", zero),
            max_limit=adv_params.get("max_limit", zero),
            pay_channel_ids=pay_channel_ids,
            user_channel_ids=user_channel_ids,
        )

    def _get_all_stocks_quantity(self, update_stocks_quantity: Decimal):
        return self._adv.lock_stocks_quantity + update_stocks_quantity

    def fmt_base_amount(self, amount: Decimal):
        return P2pUtils.fmt_base_amount(self.base, amount)

    def fmt_quote_amount(self, amount: Decimal):
        return P2pUtils.fmt_quote_amount(self.quote, amount)

    def get_adv_params(self):
        """获取创建/更新 advertising 参数"""
        return self._params

    def set_limit_params(self, limit_unit: str, max_limit: Decimal, min_limit: Decimal):
        if not self._params.is_manually_limit:
            return
        self._params._replace(limit_unit=limit_unit)
        self._params._replace(max_limit=max_limit)
        self._params._replace(min_limit=min_limit)

    def check_stocks_quantity_with_asset_config(self):
        """检查库存数量是否符合admin交易区间配置"""
        if not (asset_config := P2pAssetConfigMySQL.get_limit_info(self.base)):
            raise P2pExceptionMap[P2pExceptionCode.ASSET_NOT_SUPPORTED]
        if self._params.stocks_quantity < asset_config['min_limit']:
            raise P2pExceptionMap[P2pExceptionCode.ADV_INVENTORY_SHORTAGE]
        if self._params.stocks_quantity > asset_config['max_limit']:
            raise P2pExceptionMap[P2pExceptionCode.ADV_SHORTAGE_MAXIMUM]

    def fmt_limit_amount(self, limit_unit: str, limit_amount: Decimal) -> Decimal:
        if limit_unit == self.base:
            return self.fmt_base_amount(limit_amount)
        elif limit_unit == self.quote:
            return self.fmt_quote_amount(limit_amount)
        return limit_amount

    def get_limit_data(
            self,
            limit_unit: str,
            min_limit: Decimal,
            max_limit: Decimal,
            fiat_config: dict[str, Any]
    ) -> tuple[str, Decimal, Decimal]:
        if self._params.is_manually_limit:
            min_limit = self.fmt_limit_amount(limit_unit, min_limit)
            max_limit = self.fmt_limit_amount(limit_unit, max_limit)
            return limit_unit, min_limit, max_limit
        default_limit_unit = P2pFiatConfigMySQL.DEFAULT_LIMIT_UNIT
        min_limit = P2pUtils.fmt_base_amount(default_limit_unit, Decimal(fiat_config['min_limit']))
        max_limit = P2pUtils.fmt_base_amount(default_limit_unit, Decimal(fiat_config['max_limit']))
        return default_limit_unit, min_limit, max_limit

    def check_request_limit_params(self):
        # 校验参数 手动填写 必须填写 limit_unit 和 min_limit/max_limit
        if self._params.is_manually_limit and not (
                self._params.limit_unit and (self._params.min_limit or self._params.max_limit)
        ):
            raise InvalidArgument
        if (self._params.is_manually_limit and self._params.min_limit and
                self._params.max_limit and self._params.max_limit <= self._params.min_limit):
            raise InvalidArgument

    def check_stock_limit_params(self):
        """检查广告单笔限额配置"""
        if not (fiat_config := P2pFiatLimitCache.get_fiat_info(self.quote)):
            raise P2pExceptionMap[P2pExceptionCode.FIAT_NOT_SUPPORTED]
        self.check_request_limit_params()
        # 参数格式化
        limit_unit, min_limit, max_limit = self.get_limit_data(
            self._params.limit_unit,
            self._params.min_limit,
            self._params.max_limit,
            fiat_config
        )
        self.set_limit_params(limit_unit, max_limit, min_limit)
        rate = P2pUtils.get_p2p_asset_to_fiat_rate(P2pFiatConfigMySQL.DEFAULT_LIMIT_UNIT, self.quote)
        # 系统允许的 最大最小值 单位为 price_unit
        system_min_limit, system_max_limit = self._get_system_limit_by_limit_unit(limit_unit, rate, fiat_config)

        asset_rate = PriceManager.asset_to_asset(self.base, limit_unit) or Decimal(1)
        # 检查库存 和 最大限额
        check_max_limit = max_limit or system_max_limit
        if self._params.is_manually_limit and limit_unit == self.quote:
            if self._params.price * self._params.stocks_quantity <= check_max_limit:
                raise P2pAdvStocksError({
                    'stock_amount': amount_to_str(
                        P2pUtils.fmt_base_amount(self.base, check_max_limit / self._params.price, rounding=ROUND_UP)
                    ),
                    'base': self.base
                })
        else:
            if self._params.stocks_quantity * asset_rate <= check_max_limit:
                raise P2pAdvStocksError({
                    'stock_amount': amount_to_str(
                        P2pUtils.fmt_base_amount(self.base, check_max_limit / asset_rate, rounding=ROUND_UP)
                    ),
                    'base': self.base
                })

        if not self._params.is_manually_limit:
            return
        if limit_unit == self.quote and rate:
            if max_limit and max_limit > system_max_limit * Decimal(1 + P2pFiatLimitCache.SLIDE_EXCHANGE_RATE):
                raise P2pExceptionMap[P2pExceptionCode.FIAT_LIMIT_ERROR]
            if min_limit and min_limit < system_min_limit * Decimal(1 - P2pFiatLimitCache.SLIDE_EXCHANGE_RATE):
                raise P2pExceptionMap[P2pExceptionCode.FIAT_LIMIT_ERROR]
        else:
            if max_limit and max_limit * asset_rate > system_max_limit:
                raise P2pExceptionMap[P2pExceptionCode.FIAT_LIMIT_ERROR]
            if min_limit and min_limit * asset_rate < system_min_limit:
                raise P2pExceptionMap[P2pExceptionCode.FIAT_LIMIT_ERROR]

    def _get_system_limit_by_limit_unit(
            self,
            limit_unit: str,
            quote_default_rate: Decimal,
            fiat_config: dict[str, Any]
    ) -> tuple[Decimal, Decimal]:
        """获取系统允许的单笔限额"""
        min_limit, max_limit = Decimal(fiat_config['min_limit']), Decimal(fiat_config['max_limit'])
        asset_rate = PriceManager.asset_to_asset(P2pFiatConfigMySQL.DEFAULT_LIMIT_UNIT, self.base)
        if self._params.is_manually_limit and limit_unit == self.base:  # 商家限额单位为base
            p2p_asset_config = P2pAssetConfigCache.get_asset_info(self.base)
            system_min_limit = quantize_amount(
                asset_rate * min_limit, p2p_asset_config['precision']
            )
            system_max_limit = quantize_amount(
                asset_rate * max_limit, p2p_asset_config['precision']
            )
        elif limit_unit == self.quote:  # 商家限额单位为quote
            # quote_default_rate 1 USDT = x quote
            # price 1 base = x quote
            rate = quote_default_rate * (Decimal(1) / asset_rate) or self._params.price
            system_min_limit = P2pFiatLimitCache.format_limit_amount(min_limit * rate)
            system_max_limit = P2pFiatLimitCache.format_limit_amount(max_limit * rate)
        else:
            system_min_limit = min_limit
            system_max_limit = max_limit
        return system_min_limit, system_max_limit

    @classmethod
    def _get_pay_channel_ids(cls, user_id: int, adv_type: P2pBusinessType, pay_channel_ids: list[str]):
        if not pay_channel_ids:
            return [], []
        if adv_type == P2pBusinessType.BUY:
            return pay_channel_ids, []
        return list(UserPayChannelBus(user_id).get_channel_ids_by_user_channel(pay_channel_ids)), pay_channel_ids

    def check_fait_pay_channel(self):
        fait_pay_channels = FiatPayChannelCache().read_one_fiat(self.quote)
        fait_pay_channel_ids = set(fait_pay_channels)
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if not fait_pay_channel_ids:
            fait_pay_channel_ids = set(PayChannelBus(lang).get_normal_channel_ids())
        not_support_ids = set(self._params.pay_channel_ids) - fait_pay_channel_ids
        if not not_support_ids:
            return
        channel_names_mapper = PayChannelBus(lang).get_channel_name_by_ids(list(not_support_ids))
        raise FiatPayChannelError({"fiat": self.quote, "channel_names": ",".join(channel_names_mapper.values())})

    def _check_merchant_account(self):
        user: User = User.query.get(self.user_id)
        P2pUserManger(user.id).check_merchant_valid_status()
        exclude_fiats = P2pUtils.get_merchant_exclude_fiats(user.kyc_country)
        if self.quote in exclude_fiats:
            raise P2pFiatNotValidError({"fiat": self.quote})

    def _check_online_base_quote(self):
        if not P2pAssetConfigMySQL.check_asset_valid(self.base):
            raise P2pAssetNotValidError({"asset": self.base})
        if not P2pCountryFiatMySQL.check_fiat_valid(self.quote):
            raise P2pFiatNotValidError({"fiat": self.quote})

    @classmethod
    def _check_site_settings(cls):
        if not SiteSettings.p2p_trading_enabled:
            raise P2pExceptionMap[P2pExceptionCode.FORBID_P2P_TRADING]

    def _check_publish_permissions(self):
        self._check_site_settings()
        self._check_merchant_status()
        PermissionBus.check_publish_advertising_permission(self.user_id, self.adv_type)

    def _check_merchant_status(self):
        if not (merchant := P2pMerchant.query.filter(P2pMerchant.user_id == self.user_id).first()):
            raise P2pExceptionMap[P2pExceptionCode.NOT_OPEN_P2P_MERCHANT]
        if merchant.status == P2pMerchant.Status.INACTIVE:
            raise P2pExceptionMap[P2pExceptionCode.FROZEN_MERCHANT]

    def _check_online_adv_limit(self):
        query = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.user_id == self.user_id,
            P2pAdvertisingMySQL.status == P2pAdvertisingMySQL.Status.ONLINE
        )
        if self._adv:
            query = query.filter(P2pAdvertisingMySQL.mongo_id != self._adv.mongo_id)
        online_adv_count = query.count()
        if online_adv_count >= p2p_setting.merchant_max_active_adv_limit:
            raise OnlineAdvLimitError({"count": p2p_setting.merchant_max_active_adv_limit})

    def _check_pay_channel_ids(self):
        if not self._params.pay_channel_ids:
            raise PayChannelInvalidError
        if not (inactive_channels := PayChannelBus.get_inactive_pay_channels(self._params.pay_channel_ids)):
            return
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        channel_names_mapper = PayChannelBus(lang).get_channel_name_by_ids(inactive_channels)
        raise PayChannelInvalidError({"channel_names": ",".join(channel_names_mapper.values())})

    def _check_stocks_quantity(self):
        self.check_stock_limit_params()

    def _check_asset_adv_limit(self):
        query = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.user_id == self.user_id,
            P2pAdvertisingMySQL.base == self.base,
            P2pAdvertisingMySQL.adv_type == self.adv_type
        )
        if self._adv:
            query = query.filter(P2pAdvertisingMySQL.mongo_id != self._adv.mongo_id)
        online_asset_adv_count = query.count()
        if online_asset_adv_count >= p2p_setting.merchant_asset_direction_limit:
            raise AdvAssetLimitError({"count": p2p_setting.merchant_asset_direction_limit})

    def _check_adv_price(self):
        conf = P2pFiatLimitCache.get_fiat_info(self.quote)
        if conf.get("is_price_limit"):
            if self._params.price < Decimal(conf["min_price"]):
                raise P2pExceptionMap[P2pExceptionCode.ADV_PRICE_LOW]
            if self._params.price > Decimal(conf["max_price"]):
                raise P2pExceptionMap[P2pExceptionCode.ADV_PRICE_HIGH]

    def check_online_params(self):
        """检查广告上线参数检查"""
        if self.status == P2pAdvertisingMySQL.Status.OFFLINE:
            return
        # 检查商家账号安全
        self._check_merchant_account()
        # 检查 base asset 是否支持
        self._check_online_base_quote()
        # 检查用户权限
        self._check_publish_permissions()
        # 检查上架广告个数
        self._check_online_adv_limit()
        # 检查支付渠道
        self._check_pay_channel_ids()
        # 检查库存
        self._check_stocks_quantity()
        # 检查价格
        self._check_adv_price()
        if P2pUtils.check_p2p_cancel_limit_user_id(self.user_id):
            raise P2pExceptionMap[P2pExceptionCode.ADV_CANCELED_ORDER_LIMIT]
        if P2pUtils.check_user_complaint_limit(self.user_id):
            raise P2pExceptionMap[P2pExceptionCode.ORDER_COMPLAINT_LIMIT]

    def check_post_params(self):
        self._check_asset_adv_limit()

    def domain(self) -> P2pAdvParams:
        """校验参数"""
        self.check_stocks_quantity_with_asset_config()
        self.check_stock_limit_params()
        # 检查广告限价
        self._check_adv_price()
        self.check_fait_pay_channel()
        self.check_online_params()
        self.check_post_params()
        return self.get_adv_params()


class P2pAdvertisingBiz:
    """p2p 广告业务处理类"""

    @classmethod
    def get_books(cls, fiat: str, asset: str):
        """"获取广告盘口数据"""
        book_list = AdvertisingBookCache(asset).get_book_list(fiat)
        adv_type_mapper = group_by(lambda x: x['adv_type'], book_list)
        return dict(
            asset=asset,
            fiat=fiat,
            rate=P2pUtils.get_p2p_asset_to_fiat_rate(asset, fiat),
            books={
                "buy": sorted(
                    adv_type_mapper.get(P2pBusinessType.BUY.value, []),
                    key=lambda x: x['price']
                ),
                "sell": sorted(
                    adv_type_mapper.get(P2pBusinessType.SELL.value, []),
                    key=lambda x: x['price'],
                    reverse=True
                )
            }
        )

    @classmethod
    def get_advertising_status_count(cls, user_id: int):
        online_count = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.user_id == user_id,
            P2pAdvertisingMySQL.status == P2pAdvertisingMySQL.Status.ONLINE
        ).count()
        
        offline_count = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.user_id == user_id,
            P2pAdvertisingMySQL.status == P2pAdvertisingMySQL.Status.OFFLINE
        ).count()

        return dict(
            offline_count=offline_count,
            online_count=online_count
        )

    @classmethod
    def flush_advertising_updated_at_by_merchant(cls, user_id: int):
        advs = P2pAdvertisingMySQL.query.filter_by(user_id=user_id).all()
        for adv in advs:
            adv.updated_at = now()
        db.session.commit()

    @classmethod
    def get_online_advertising_merchants(cls):
        return {
            adv.user_id for adv in P2pAdvertisingMySQL.query.filter(
                P2pAdvertisingMySQL.status == P2pAdvertisingMySQL.Status.ONLINE
            ).with_entities(P2pAdvertisingMySQL.user_id).all()
        }

    @classmethod
    def gen_adv_channel_info(cls, adv):
        lang = Language.ZH_HANS_CN
        user = User.query.get(adv.user_id)
        if adv.adv_type == P2pBusinessType.BUY:
            new_data = PayChannelBus(lang).get_channel_name_by_ids(adv.pay_channel_ids)
        else:
            new_data = UserPayChannelBus(user.id).get_user_pay_channel_data(
                lang, user.kyc_full_name, inactive=True, user_channel_ids=adv.user_channel_ids
            )
        return new_data

    @classmethod
    def user_pay_channel_delete_update_advertising(
            cls,
            user_id: int,
            user_pay_channel_id: str,
            pay_channel_id: str
    ):
        """商家手动删除收款方式"""
        if not P2pMerchant.query.filter(
                P2pMerchant.user_id == user_id
        ).first():
            return
            
        # 使用 func.json_contains 查询 JSON 字段
        advs = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.user_id == user_id,
            P2pAdvertisingMySQL.adv_type == P2pBusinessType.SELL,
            func.json_contains(P2pAdvertisingMySQL.user_channel_ids, f'"{str(user_pay_channel_id)}"')
        ).all()
        
        for adv in advs:  # type: P2pAdvertisingMySQL
            user_channel_ids = adv.user_channel_ids
            user_channel_ids.remove(user_pay_channel_id)
            adv.user_channel_ids = user_channel_ids
            adv.pay_channel_ids = [i for i in adv.pay_channel_ids if i != pay_channel_id]
            if not user_channel_ids:
                P2pAdvertisingManger(adv.mongo_id).auto_offline(AutoOfflineAdvReason.PAY_CHANNEL_INVALID)
        
        db.session.commit()

    @classmethod
    def pay_channel_invalid_update_advertising(cls, pay_channel_id: str):
        """自动下架广告-支付渠道失效"""
        advs = P2pAdvertisingMySQL.query.filter(
            func.json_contains(P2pAdvertisingMySQL.pay_channel_ids, f'"{str(pay_channel_id)}"')
        ).all()
        for adv in advs:  # type: P2pAdvertisingMySQL
            pay_channel_ids = adv.pay_channel_ids
            pay_channel_ids.remove(pay_channel_id)
            adv.pay_channel_ids = pay_channel_ids
            if adv.adv_type == P2pBusinessType.SELL:
                user_pay_channel_ids = UserPayChannelBus(adv.user_id).get_user_pay_channels(pay_channel_id)
                adv.user_channel_ids = [i for i in adv.user_channel_ids if i not in user_pay_channel_ids]
            if not pay_channel_ids:
                P2pAdvertisingManger(adv.mongo_id).auto_offline(AutoOfflineAdvReason.PAY_CHANNEL_INVALID)

        db.session.commit()

    @classmethod
    def offline_advertising_by_settings(
            cls,
            reason: AutoOfflineAdvReason,
            asset: str = None,
            fiat: str = None
    ):
        """自动下架广告-根据配置改动"""
        if not asset and not fiat:
            return
        query = P2pAdvertisingMySQL.query
        match reason:
            case AutoOfflineAdvReason.ASSET_INVALID:
                if not asset:
                    raise ValueError('asset is required')
                query = query.filter(P2pAdvertisingMySQL.base == asset)
            case AutoOfflineAdvReason.FIAT_INVALID:
                if not fiat:
                    raise ValueError('fiat is required')
                query = query.filter(P2pAdvertisingMySQL.quote == fiat)
            case _:
                raise ValueError('invalid reason')
        advs = query.with_entities(P2pAdvertisingMySQL.mongo_id).all()
        for adv in advs:
            P2pAdvertisingManger(adv.mongo_id).auto_offline(reason)

    @classmethod
    def offline_advertising_by_price(cls, fiats=None):
        """自动下架广告-根据公允价格"""
        fiat_maps = {k: v for k, v in P2pFiatLimitCache.get_all().items() if v.get("is_price_limit")}
        if not fiats:
            fiats = list(fiat_maps.keys())
        advs = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.status == P2pAdvertisingMySQL.Status.ONLINE,
            P2pAdvertisingMySQL.quote.in_(fiats)
        ).all()
        for adv in advs:
            if not (conf := fiat_maps.get(adv.quote)):
                continue
            min_price, max_price = Decimal(conf["min_price"]), Decimal(conf["max_price"])
            if not (min_price <= adv.price <= max_price):
                P2pAdvertisingManger(adv.mongo_id).auto_offline(
                    AutoOfflineAdvReason.PRICE_LIMIT,
                    extra={"min_price": min_price, "max_price": max_price}
                )


class P2pAdvertisingManger:

    def __init__(self, adv_id: str):
        self._object_id = adv_id
        self._adv: P2pAdvertisingMySQL = self._get_adv_object()

    def _get_adv_object(self):
        return P2pAdvertisingMySQL.get_adv_by_id(self._object_id)

    @property
    def adv(self):
        return self._adv

    def get_active_user_pay_channel(self, user_pay_channel_ids: list[str]):
        pay_channel_list = UserPayChannelBus(self._adv.user_id).get_channel_map_list(user_pay_channel_ids)
        return [data['user_pay_channel_id'] for data in pay_channel_list]

    def get_pay_channel_ids(self):
        if self._adv.adv_type == P2pBusinessType.BUY:
            return P2pUtils.get_active_pay_ids(self._adv.pay_channel_ids)
        return P2pUtils.get_active_user_pay_channel(self._adv.user_id, self._adv.user_channel_ids)

    def info(self, merchant_user_id):
        if not self._adv or self._adv.user_id != merchant_user_id:
            raise InvalidArgument
        info_data = dict(
            id=self._object_id,
            adv_type=self._adv.adv_type.name,
            base=self._adv.base,
            quote=self._adv.quote,
            price=self._adv.price,
            stocks_mode=self._adv.stocks_mode.name,
            is_manually_limit=self._adv.is_manually_limit,
            pay_channel_ids=self.get_pay_channel_ids(),
            payment_timeliness=self._adv.payment_timeliness_minute,
            stocks_quantity=self._adv.real_stocks_quantity,
            limitation_filter=self._adv.limitation_filter,
            transaction_notes=self._adv.transaction_notes,
            say_hi_msg=self._adv.say_hi_msg,
            status=self._adv.status.name,
        )
        if self._adv.is_manually_limit:
            if self._adv.min_limit:
                info_data['min_limit'] = self._adv.min_limit
            if self._adv.max_limit:
                info_data['max_limit'] = self._adv.max_limit
            info_data['limit_unit'] = self._adv.limit_unit
        return info_data

    def trading_watch_info(self):
        merchant_name_mapper = P2pUserManger.get_merchant_infos([self._adv.user_id])
        return dict(
            id=self._object_id,
            adv_type=self._adv.show_user_adv_type.name,
            base=self._adv.base,
            quote=self._adv.quote,
            price=self._adv.price,
            **self._adv.limit_data(),
            payment_timeliness=self._adv.payment_timeliness_minute,
            transaction_notes=self._adv.transaction_notes,
            stocks_quantity=self._adv.real_stocks_quantity,
            pay_channel_ids=P2pUtils.get_active_pay_ids(self._adv.pay_channel_ids),
            user_pay_channel_info=UserPayChannelBus(self._adv.user_id).get_channel_map_list(self._adv.user_channel_ids),
            extra={
                **self._adv.display_extr,
                **merchant_name_mapper.get(self._adv.user_id, {})
            }
        )

    def get_adv_create_order_side(self):
        # 用户从广告创建订单的方向 和 广告发布的方向 是相反的
        return P2pBusinessType.reverse(self._adv.adv_type)

    def check_adv_can_create(self, customer_id, seller_pay_channel_id, price, base_amount,
                             quote_amount, amount_type, base, quote):
        adv = self._adv
        # 检查广告状态
        if adv.status == P2pAdvertisingMySQL.Status.OFFLINE:
            raise P2pExceptionMap[P2pExceptionCode.ADV_OFFLINE]
        # 检查用户是否为广告商本身
        if adv.user_id == customer_id:
            raise P2pExceptionMap[P2pExceptionCode.ADV_WITH_SELF]
        # 检查价格
        self._check_amount(adv, price, base_amount, quote_amount, amount_type, base, quote)
        # 检查广告库存
        self._check_stock(base_amount)
        # 检查支付方式
        self._check_pay_channel(customer_id, seller_pay_channel_id)
        # 检查用户是否符合广告高级限制
        if not self.check_user_reach_requirement(customer_id):
            raise P2pExceptionMap[P2pExceptionCode.ADV_NOT_ENOUGH]

    def _check_pay_channel(self, customer_id, seller_pay_channel_id):
        if not seller_pay_channel_id:
            raise InvalidArgument(message="must need seller_pay_channel_id")
        side = self.adv.adv_type
        sell_user_id = self.adv.user_id if side == P2pBusinessType.SELL else customer_id
        bus = UserPayChannelBus(sell_user_id)
        channel = bus.get_source_by_id(seller_pay_channel_id)
        if not channel:
            raise PayChannelInvalidError
        # 1. 检查支付渠道是否有效
        bus.check_channel_active_valid(channel)
        # 2. 检查支付渠道
        if side == P2pBusinessType.BUY:
            if channel.pay_channel_id not in self.adv.pay_channel_ids:
                raise P2pExceptionMap[P2pExceptionCode.ADV_NOT_PAY_CHANNEL]
        else:
            if seller_pay_channel_id not in self.adv.user_channel_ids:
                raise P2pExceptionMap[P2pExceptionCode.ADV_NOT_PAY_CHANNEL]

    def _check_stock(self, quote_amount):
        if self.adv.real_stocks_quantity < quote_amount:
            raise P2pExceptionMap[P2pExceptionCode.ADV_NOT_STOCK]

    @classmethod
    def _get_quote_adv_limit(cls, adv: P2pAdvertisingMySQL):
        """获取 quote 单位的广告限额, 用来下单"""
        system_min_limit, system_max_limit = P2pUtils.get_quote_default_adv_limit(adv.base, adv.quote, adv.price)
        return adv.quote_min_limit or system_min_limit, adv.quote_max_limit or system_max_limit

    @classmethod
    def _get_base_adv_limit(cls, adv: P2pAdvertisingMySQL):
        """获取 base 单位的广告限额, 用来比较 库存和最小下单量"""
        system_min_limit, system_max_limit = P2pUtils.get_base_default_adv_limit(adv.base, adv.quote)  # 单位 base
        if not adv.is_manually_limit:
            return system_min_limit, system_max_limit
        if adv.limit_unit == adv.base:
            return adv.min_limit or system_min_limit, adv.max_limit or system_max_limit
        # limit_unit == quote
        asset_info = P2pAssetConfigCache.get_asset_info(adv.base)
        quote_min_limit = quantize_amount(adv.min_limit / adv.price, asset_info['precision'])
        quote_max_limit = quantize_amount(adv.max_limit / adv.price, asset_info['precision'])
        return quote_min_limit or system_min_limit, quote_max_limit or system_max_limit

    def _check_amount(self, adv: P2pAdvertisingMySQL, price: Decimal, base_amount: Decimal,
                      quote_amount: Decimal, amount_type: P2pAmountType,
                      base: str, quote: str):
        """
        根据 side 计算 price, base_amount, quote_amount 的关系
        """
        if base and base != self.adv.base:
            raise P2pExceptionMap[P2pExceptionCode.ADV_DATA_UPDATE]
        if quote and quote != adv.quote:
            raise P2pExceptionMap[P2pExceptionCode.ADV_FIAT_UPDATE]
        if price != self.adv.price:
            raise P2pExceptionMap[P2pExceptionCode.PRICE_EXPIRED]
        assert base_amount > Decimal()
        assert quote_amount > Decimal()
        adv_min_limit, adv_max_limit = self._get_quote_adv_limit(adv)
        if quote_amount < adv_min_limit:
            raise P2pExceptionMap[P2pExceptionCode.ADV_MIN_AMOUNT]
        if quote_amount > adv_max_limit:
            raise P2pExceptionMap[P2pExceptionCode.ADV_MAX_AMOUNT]
        asset_info = P2pAssetConfigCache.get_asset_info(adv.base)
        if not asset_info:
            raise P2pAssetNotValidError({"asset": adv.base})
        asset_round = partial(quantize_amount, decimals=asset_info["precision"])
        fiat_info = P2pFiatLimitCache.get_fiat_info(adv.quote)
        if not fiat_info:
            raise P2pFiatNotValidError({"fiat": adv.quote})
        fiat_round = partial(quantize_amount, decimals=fiat_info["precision"])  # 前端也是 ROUND_DOWN
        if base_amount != asset_round(base_amount):
            raise P2pExceptionMap[P2pExceptionCode.ASSET_AMOUNT_ERROR]
        if quote_amount != fiat_round(quote_amount):
            raise P2pExceptionMap[P2pExceptionCode.FIAT_AMOUNT_ERROR]
        if amount_type == P2pAmountType.QUOTE:
            # 以行情货币（法币）为准
            if asset_round(quote_amount / price) != base_amount:
                raise P2pExceptionMap[P2pExceptionCode.PRICE_RATIO_ERROR]
        else:
            # 以交易货币（数字货币）为准
            if fiat_round(base_amount * price) != quote_amount:
                raise P2pExceptionMap[P2pExceptionCode.PRICE_RATIO_ERROR]

    @classmethod
    def _fmt_limitation_filter(cls, limitation_filter):
        return [
            {
                k: v.name if isinstance(v, Enum) else v for k, v in i.items()
            } for i in limitation_filter if i.get('value')  # 过滤掉因前端问题无效的配置数据
        ]

    @classmethod
    def _payment_timeliness_to_second(cls, payment_timeliness):
        return payment_timeliness * 60

    @classmethod
    def create(cls, user_id, adv_schem: dict[str, Any]) -> P2pAdvertisingMySQL:
        """
        创建广告
        :return:
        """
        base, quote = adv_schem["base"], adv_schem["quote"]
        adv_type = adv_schem['adv_type']
        with CacheLock(LockKeys.p2p_create_advertising(user_id, base, adv_type.value), wait=False):
            adv_data: P2pAdvParams = P2pAdvertisingSchema(user_id, adv_schem).domain()
            p2p_adv = P2pAdvertisingMySQL(
                user_id=user_id,
                adv_type=adv_type,
                base=base,
                quote=quote,
                price=adv_data.price,
                stocks_mode=adv_schem['stocks_mode'],
                is_manually_limit=adv_data.is_manually_limit,
                pay_channel_ids=adv_data.pay_channel_ids,
                user_channel_ids=adv_data.user_channel_ids,
                payment_timeliness=cls._payment_timeliness_to_second(adv_schem['payment_timeliness']),
                stocks_quantity=adv_data.stocks_quantity,
                limitation_filter=cls._fmt_limitation_filter(adv_schem['limitation_filter']),
                transaction_notes=adv_schem.get('transaction_notes', ''),
                say_hi_msg=adv_schem.get('say_hi_msg', ''),
                status=adv_schem['status'],
                extra=TradeStatisticsModel(**P2pUserManger(user_id).get_merchant_trade_statistics()).model_dump()
            )
            p2p_adv.adv_number = p2p_adv.generate_id()
            if adv_data.is_manually_limit:
                p2p_adv.limit_unit = adv_data.limit_unit
                p2p_adv.min_limit = adv_data.min_limit
                p2p_adv.max_limit = adv_data.max_limit
            db.session_add_and_commit(p2p_adv)
            return p2p_adv

    def get_adv_pay_channel_ids(self):
        if self._adv.adv_type == P2pBusinessType.BUY:
            return self._adv.pay_channel_ids
        return self._adv.user_channel_ids

    def online(self):
        """
        上架广告:
        1. 检查用户权限
        2. 检查上架广告个数
        3. 检查当前广告状态
        4. 修改广告状态
        5. 添加广告日志数据
        :return:
        """
        with (CacheLock(LockKeys.p2p_advertising(self._adv.mongo_id), wait=False),
              CacheLock(LockKeys.p2p_advertising_online(self._adv.user_id), wait=False)):
            self._adv: P2pAdvertisingMySQL = self._get_adv_object()
            self._check_is_self_adv(g.user.id)
            origin_adv = copy.deepcopy(self._adv)
            if self._adv.status == P2pAdvertisingMySQL.Status.ONLINE:
                raise P2pExceptionMap[P2pExceptionCode.ADV_HAS_BEEN_ONLINE]
            adv_schema = self._adv.to_check_params_dict()
            adv_schema['status'] = P2pAdvertisingMySQL.Status.ONLINE
            adv_schema['pay_channel_ids'] = self.get_adv_pay_channel_ids()
            P2pAdvertisingSchema(self._adv.user_id, adv_schema, only_check_oline=True).check_online_params()
            self._adv.status = P2pAdvertisingMySQL.Status.ONLINE
            self._adv.updated_at = now()
            db.session.commit()
            
            P2pAdvertisingChangeLogMySQL.save_change_log(origin_adv, self._adv)

    def auto_offline(self, reason: AutoOfflineAdvReason, extra=None):
        """
        自动下架广告:
        1. 库存不足最小下单量
        2. 风控
        3. 高级kyc失效
        4. 冻结商家权限
        5. 支付方式全部失效
        6. 法币或者数字货币下架
        """
        if self._adv.status == P2pAdvertisingMySQL.Status.OFFLINE:
            return
        origin_adv = copy.deepcopy(self._adv)
        self._adv.status = P2pAdvertisingMySQL.Status.OFFLINE
        self._adv.updated_at = now()
        db.session.commit()

        P2pAdvertisingChangeLogMySQL.save_change_log(origin_adv, self._adv, reason, extra)
        AutoOfflineAdvertising().send_message(self._adv, reason)

    def offline(self):
        """
        下架广告
        :return:
        """
        with CacheLock(LockKeys.p2p_advertising(str(self._adv.mongo_id)), wait=False):
            self._adv: P2pAdvertisingMySQL = self._get_adv_object()
            self._check_is_self_adv(g.user.id)
            origin_adv = copy.deepcopy(self._adv)
            if self._adv.status == P2pAdvertisingMySQL.Status.OFFLINE:
                raise P2pExceptionMap[P2pExceptionCode.ADV_HAS_BEEN_OFFLINE]
            self._adv.status = P2pAdvertisingMySQL.Status.OFFLINE
            self._adv.updated_at = now()
            db.session.commit()

            P2pAdvertisingChangeLogMySQL.save_change_log(origin_adv, self._adv)

    def _check_is_self_adv(self, user_id):
        if self._adv.user_id != user_id:
            raise InvalidArgument(message="not self adv")

    def _get_all_stocks_quantity(self, update_stocks_quantity: Decimal):
        return self._adv.lock_stocks_quantity + update_stocks_quantity

    def update(self, user_id: int, adv_schem: dict[str, Any]):
        """
        修改广告
        1.检查特定数据的合法性
        2.如果订单状态有改动，需要校验上架广告的数量
        3.修改广告的数据
        4.增加广告日志数据
        :return:
        """
        self._check_is_self_adv(user_id)
        base = adv_schem["base"]
        enum_limitation_filter = adv_schem.pop("limitation_filter")
        limitation_filter = self._fmt_limitation_filter(enum_limitation_filter)
        update_stocks_quantity = P2pUtils.fmt_base_amount(base, adv_schem['stocks_quantity'])
        adv_data = P2pAdvertisingSchema(user_id, adv_schem, self._adv.mongo_id).domain()
        adv_schem.pop("pay_channel_ids")
        adv_schem['payment_timeliness'] = self._payment_timeliness_to_second(adv_schem['payment_timeliness'])
        with CacheLock(LockKeys.p2p_advertising(self._adv.mongo_id), wait=False):
            self._adv: P2pAdvertisingMySQL = self._get_adv_object()
            # 更新库存，需要重新计算库存数量
            stocks_quantity = self._get_all_stocks_quantity(update_stocks_quantity)
            origin_adv = copy.deepcopy(self._adv)
            
            # 直接更新对象属性，而不是使用update()方法
            for key, value in adv_schem.items():
                setattr(self._adv, key, value)
                
            # 设置其他属性
            self._adv.price = adv_data.price
            self._adv.stocks_quantity = stocks_quantity
            self._adv.limitation_filter = limitation_filter
            self._adv.pay_channel_ids = adv_data.pay_channel_ids
            self._adv.user_channel_ids = adv_data.user_channel_ids
            self._adv.is_manually_limit = adv_data.is_manually_limit
            self._adv.updated_at = now()
            
            if adv_data.is_manually_limit:
                self._adv.limit_unit = adv_data.limit_unit
                self._adv.min_limit = adv_data.min_limit
                self._adv.max_limit = adv_data.max_limit
            else:
                self._adv.limit_unit = ""
                self._adv.min_limit = Decimal("0")
                self._adv.max_limit = Decimal("0")
            
            db.session.add(self._adv)
            P2pAdvertisingChangeLogMySQL.save_change_log(origin_adv, self._adv)
            db.session.commit()
            return self._adv

    @classmethod
    def _check_stocks_option(cls, source_amount: Decimal, lock_amount: Decimal):
        """检查库存模式以及库存余量"""
        if not source_amount or source_amount < lock_amount:
            return False
        return True

    def is_unlimited_stock(self):
        return self._adv.stocks_mode == P2pAdvertisingMySQL.StocksMode.UNLIMITED

    @unique_option(UniqueOptionCache.OptionType.P2p_LOCK_STOCK)
    def lock_stock(self, amount: Decimal, *, unique_type=None, unique_id=None):
        """
        锁定库存
        :return:
        """
        if not amount:
            return
        if not self._check_stocks_option(self._adv.real_stocks_quantity, amount):
            raise P2pExceptionMap[P2pExceptionCode.ADV_NOT_STOCK]
        with CacheLock(LockKeys.p2p_advertising(self._adv.mongo_id), wait=False):
            self._adv: P2pAdvertisingMySQL = self._get_adv_object()

            self._adv.lock_stocks_quantity += amount
            # 绕过updated_at字段的自动更新触发器
            self._adv.updated_at = P2pAdvertisingMySQL.__table__.c.updated_at
            db.session.commit()
            is_sufficient = self._check_limited_mode_quantity()
            if not is_sufficient:
                self.auto_offline(AutoOfflineAdvReason.INVENTORY_SHORTAGE)

    def adv_check_min_limit(self) -> Decimal:
        """
        检查广告的最小下单量: 单位 base
        """
        if not self._adv.is_manually_limit:
            system_min_limit = Decimal(P2pFiatLimitCache.get_fiat_info(self._adv.quote).get("min_limit", 0))
            asset_rate = PriceManager.asset_to_asset(P2pFiatConfigMySQL.DEFAULT_LIMIT_UNIT, self._adv.base)
            asset_info = P2pAssetConfigCache.get_asset_info(self._adv.base)
            return quantize_amount(system_min_limit * asset_rate, asset_info["precision"])
        if self._adv.limit_unit == self._adv.quote:
            return self._adv.min_limit / self._adv.price
        else:
            return self._adv.min_limit

    def _check_limited_mode_quantity(self):
        """检查实际库存是否满足限量模式的最小下单量"""
        if self._adv.stocks_mode != P2pAdvertisingMySQL.StocksMode.LIMITED:
            return True
        min_limit, _ = self._get_base_adv_limit(self._adv)
        if self._adv.real_stocks_quantity < min_limit:
            return False
        return True

    @unique_option(UniqueOptionCache.OptionType.P2p_REDUCE_STOCK)
    def reduce_stock(self, amount: Decimal, *, unique_type=None, unique_id=None):
        """
        减少库存
        """
        if not amount:
            return
        if not self._check_stocks_option(self._adv.lock_stocks_quantity, amount):
            raise InvalidArgument(message="reduce stock error")
        with CacheLock(LockKeys.p2p_advertising(self._adv.mongo_id), wait=False):
            self._adv: P2pAdvertisingMySQL = self._get_adv_object()

            self._adv.lock_stocks_quantity -= amount
            self._adv.stocks_quantity -= amount
            # 绕过updated_at字段的自动更新触发器
            self._adv.updated_at = P2pAdvertisingMySQL.__table__.c.updated_at
            db.session.commit()

    @unique_option(UniqueOptionCache.OptionType.P2p_UNLOCK_STOCK)
    def unlock_stock(self, amount: Decimal, *, unique_type=None, unique_id=None):
        """
        解冻库存
        """
        if not amount:
            return
        if not self._check_stocks_option(self._adv.lock_stocks_quantity, amount):
            raise InvalidArgument(message="unlock stock error")
        with CacheLock(LockKeys.p2p_advertising(self._adv.mongo_id), wait=False):
            self._adv: P2pAdvertisingMySQL = self._get_adv_object()

            # 使用SQLAlchemy风格更新，绕过updated_at字段的自动更新触发器
            self._adv.lock_stocks_quantity -= amount
            # 绕过updated_at字段的自动更新触发器
            self._adv.updated_at = P2pAdvertisingMySQL.__table__.c.updated_at
            db.session.commit()

    def check_user_reach_requirement(self, user_id) -> bool:
        """检查用户是否符合广告要求"""
        adv_key_mapper = UserReachLimitationFilter(user_id, [self._adv.mongo_id]).parse()
        return all([True & b for k in adv_key_mapper.values() for _, b in k.items()])

    @classmethod
    def get_self_advertising(cls, user_id: int):
        """获取商家自己的广告"""
        advertising = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.user_id == user_id
        ).order_by(P2pAdvertisingMySQL.updated_at.desc()).all()
        adv_type_mapper = defaultdict(list)
        for adv in advertising:
            adv_dict = adv.to_self_dict()
            pay_channel_ids = adv_dict["pay_channel_ids"]
            adv_dict['pay_channel_ids'] = P2pUtils.get_active_pay_ids(pay_channel_ids)
            adv_type_mapper[adv.adv_type].append(adv_dict)
        return adv_type_mapper


class UserReachLimitationFilter:
    """检测用户是否符合广告的限制规则（需要批量查询）"""

    _LIMITER_MSG_MAPPER = {
        TradeLimitation.REGISTERED_TIME: _("您的注册时间未满足广告商要求"),
        TradeLimitation.IS_SAME_KYC_AREA: _("您的认证地区未满足广告商要求"),
        TradeLimitation.COMPLETION_RATE: _("您的完单率未满足广告商要求"),
        TradeLimitation.NON_ADVERTISER: _("您未满足广告方要求")
    }

    def __init__(self, user_id: int, adv_ids: list[str]):
        self.p2p_user: P2pUser = P2pUser.query.filter(P2pUser.user_id == user_id).first()
        self.filter_mapper = self.parse_limitation_filter(adv_ids)
        self._adv_ids = adv_ids

    @classmethod
    def _convert_value(cls, user_value: Any, value: str) -> Any:
        match value:
            case 'true' | 'True':
                return True
            case 'false' | 'False':
                return False
        return type(user_value)(value) if user_value is not None else value

    @classmethod
    def _calculate(cls, user_value: Any, op: Operator, value: str) -> bool:
        value = cls._convert_value(user_value, str(value))
        match op:
            case Operator.EQ:
                return user_value == value
            case Operator.GE:
                return user_value >= value
            case Operator.LE:
                return user_value <= value

    @classmethod
    def parse_filter(cls, filters: list[dict[str, Any]]):
        return [
            (
                TradeLimitation[item["key"]],
                Operator[item['op']],
                item["value"]
            ) for item in filters
        ]

    def parse_limitation_filter(self, dav_ids: list[str]) -> dict[
        TradeLimitation,
        dict[
            int,
            list[dict[str, tuple[Operator, Any]]]
        ]
    ]:
        # {key: {user_id: {adv_id: (op, value)}}}
        limitation_filter_mapper = defaultdict(lambda: defaultdict(list))
        adv_list = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.mongo_id.in_([str(_id) for _id in dav_ids])
        ).with_entities(
            P2pAdvertisingMySQL.mongo_id,
            P2pAdvertisingMySQL.user_id,
            P2pAdvertisingMySQL.limitation_filter
        ).all()
        for adv in adv_list:
            limit_filter = adv.limitation_filter
            if not limit_filter:
                continue
            for key, op, value in self.parse_filter(limit_filter):
                limitation_filter_mapper[key][adv.user_id].append({adv.mongo_id: (op, value)})
        return limitation_filter_mapper

    def _get_default_parse_result(self):
        return {id_: {i: True for i in TradeLimitation} for id_ in self._adv_ids}

    def parse_with_msg(self):
        adv_id_msg_mapper = defaultdict(list)
        for _id, value in self.parse().items():
            for key, v in value.items():
                if v:
                    continue
                adv_id_msg_mapper[_id].append(self._LIMITER_MSG_MAPPER[key])
        return adv_id_msg_mapper

    def parse(self) -> dict[str, dict[TradeLimitation, bool]]:
        if not self.p2p_user:
            return self._get_default_parse_result()
        adv_limiter_mapper = defaultdict(dict)
        for key, value in self.filter_mapper.items():
            obj_id_mapper = getattr(self, f'_handler_{key.name.lower()}')(value)
            # 没有配置补默认值
            _ = {
                adv_limiter_mapper[id_].update({key: is_conform if id_ in self._adv_ids else True})
                for id_, is_conform in obj_id_mapper.items()
            }
        return adv_limiter_mapper

    def _handler_completion_rate(self, user_filter_mapper: dict[int, list[dict[str, tuple[Operator, Any]]]]):
        # 完单率 user summary 存储的是小数，adv存储的是百分数
        user_completion_rate = self.p2p_user.completion_rate * Decimal(100)
        return {
            obj_id: self._calculate(user_completion_rate, op, v)
            for value in user_filter_mapper.values() for i in value for obj_id, (op, v) in i.items()
            if v
        }

    def _handler_registered_time(self, user_filter_mapper: dict[int, list[dict[str, tuple[Operator, Any]]]]):
        user: User = User.query.get(self.p2p_user.user_id)
        registered_days = (now() - user.created_at).days
        return {
            obj_id: self._calculate(registered_days, op, v)
            for value in user_filter_mapper.values() for i in value for obj_id, (op, v) in i.items()
            if v
        }

    def _handler_is_same_kyc_area(self, user_filter_mapper: dict[int, list[dict[str, tuple[Operator, Any]]]]):
        user: User = User.query.get(self.p2p_user.user_id)
        kyc_country = user.kyc_country
        merchant_user_ids = user_filter_mapper.keys()
        merchant_kyc_mapper = {
            merchant_user_id: country for merchant_user_id, country in KycVerificationPro.query.filter(
                KycVerificationPro.user_id.in_(merchant_user_ids)
            ).with_entities(
                KycVerificationPro.user_id,
                KycVerificationPro.country
            ).all()
        }
        return {
            obj_id: self._calculate(merchant_kyc_mapper.get(merchant_user_id), op, kyc_country)
            for merchant_user_id, value in user_filter_mapper.items() for i in value for obj_id, (op, _) in i.items()
        }

    def _handler_non_advertiser(self, user_filter_mapper: dict[int, list[dict[str, tuple[Operator, Any]]]]):
        user_is_merchant = not bool(P2pMerchant.query.filter(
            P2pMerchant.user_id == self.p2p_user.user_id,
            P2pMerchant.status == P2pMerchant.Status.ACTIVE
        ).first())
        return {
            obj_id: user_is_merchant
            for value in user_filter_mapper.values() for i in value for obj_id, _ in i.items()
        }


class AdvertisingSortBy(Enum):
    PRICE = "price"
    COMPLETION_RATE = 'completion_rate'
    DEAL_COUNT = 'deal_count'
    ACCEPTANCE_RATE = 'acceptance_rate'


class AdvertisingSortByType(Enum):
    DESC = "-"
    ASC = "+"


class AdvertisingUserPreferences(Enum):
    """
    用户查询偏好
    """
    IS_TRADED = "is_traded"  # 是否交易过


class UserPreferencesResultType(Enum):
    """广告搜索用户偏好解析结果类型"""
    USER_ID = "user_id"
    ADV_ID = "adv_id"


class AdvertisingUserPreferencesParse:
    """
    解析广告用户偏好筛选项
    返回对应的商家或者广告ID
    """

    _USER_PREFERENCES_CONFIG = {
        AdvertisingUserPreferences.IS_TRADED: UserPreferencesResultType.USER_ID
    }

    def __init__(self, user_id: int, user_preferences: list[AdvertisingUserPreferences]):
        self.user_preferences = user_preferences
        self._user_id = user_id

    def parse(self):
        result_mapper = defaultdict(list)
        for user_preference in self.user_preferences:
            v = getattr(self, f'_handler_{user_preference.name.lower()}')()
            result_mapper[self._USER_PREFERENCES_CONFIG[user_preference]].append(v)
        intersection = lambda *lists: reduce(set.intersection, map(set, lists))
        return {k: intersection(*v) for k, v in result_mapper.items()}

    def _handler_is_traded(self) -> set[int]:
        return P2pUtils.get_user_traded_merchants(self._user_id)


class P2pAdvertisingSearchManager:
    """p2p 广告搜索解析管理类"""

    def __init__(self, user: User, lang: Language):
        """依赖: 交易者的用户信息"""
        self._user = user
        self._lang = lang

    def _dump_query_advertising(self, adv_list: list[P2pAdvertisingMySQL]):
        adv_ids = []
        active_pay_channel_ids = set(PayChannelCache.get_all().keys())
        for item in adv_list:
            adv_ids.append(item.mongo_id)
        adv_limiter_msg_mapper = {}
        traded_merchant_ids = {}
        if self._user:
            adv_limiter_msg_mapper = UserReachLimitationFilter(self._user.id, adv_ids).parse_with_msg()
            traded_merchant_ids = P2pUtils.get_user_traded_merchants(self._user.id)
        data = []
        for i in adv_list:
            base_dict = i.to_index_dict()
            pay_channel_ids = base_dict.pop("pay_channel_ids")
            base_dict["pay_channel_ids"] = [i for i in pay_channel_ids if i in active_pay_channel_ids]
            if not base_dict["pay_channel_ids"]:
                continue
            limiter_msgs = adv_limiter_msg_mapper.get(i.mongo_id, [])
            with force_locale(self._lang.value):
                translation_limiter_msgs = [gettext(i) for i in limiter_msgs]

            data.append(dict(
                user_preferences=dict(
                    is_traded=i.user_id in traded_merchant_ids,
                    limiter_msgs=translation_limiter_msgs,
                    is_allowed=not bool(limiter_msgs)
                ),
                **base_dict,
            ))
        return data

    def index_advertising(self, kwargs):
        """首页广告搜索"""
        page, limit = kwargs['page'], kwargs['limit']
        params = Struct(**kwargs)
        total, query = P2pAdvertisingSearch(self._user).search_by_index(
            params.base,
            params.quote,
            P2pBusinessType.reverse(params.adv_type),
            params.pay_channel_ids,
            page,
            limit,
            amount=params.amount,
            sort_by=params.sort_by,
            sort_by_type=params.sort_type,
            user_preferences=params.user_preferences,
            only_follow=params.only_follow,
            margin_enough=params.margin_enough,
        )
        adv_list = [i for i in query.all()]
        merchant_ids = set()
        for item in adv_list:
            merchant_ids.add(item.user_id)
        merchant_name_mapper = P2pUserManger.get_merchant_infos(list(merchant_ids))
        data = self._dump_query_advertising(adv_list)
        items = []
        adv_ids = set()
        for item in data:
            # TODO 暂时解决数据重复的问题
            if item["id"] in adv_ids:
                continue
            else:
                adv_ids.add(item["id"])
            extra = item.pop("extra")
            merchant_user_id = item.pop("user_id")
            name_data = merchant_name_mapper.get(merchant_user_id, {})
            items.append(dict(
                merchant=dict(
                    **name_data,
                    **extra
                ),
                **item
            ))

        if len({len(adv_ids), len(adv_list), len(data)}) > 1:
            current_app.logger.error(f"query adv error total {total} adv_list {len(adv_list)}  "
                                     f"dump_list {len(data)} adv_ids {len(adv_ids)}")
            current_app.logger.error(f"""advertising query params: {str(query.statement)} \r
                                query compiled: {query.statement.compile(dialect=db.engine.dialect)}""")
        return dict(
            total=total,
            items=items
        )

    def merchant_advertising(self, merchant_user_id: int, page: int, limit: int) -> dict[str, Any]:
        """商家广告搜索"""
        p2p_merchant: P2pMerchant = P2pMerchant.query.filter(P2pMerchant.user_id == merchant_user_id).first()
        if not p2p_merchant or p2p_merchant.is_invalid:
            return {}
        total, query = P2pAdvertisingMySQL.query_user_advertising(merchant_user_id, page, limit)
        adv_list = [i for i in query.all()]
        data = self._dump_query_advertising(adv_list)
        items = []
        for item in data:
            item.pop("extra")
            item.pop("user_id")
            items.append(item)

        return dict(
            total=total,
            items=items
        )


class P2pAdvertisingSearch:
    """p2p 广告搜索"""

    def __init__(
            self,
            user,
    ):
        self.user = user

    def check_params(self, user_preferences):
        if not user_preferences:
            return True
        if AdvertisingUserPreferences.IS_TRADED in user_preferences and not self.user:
            raise AuthenticationFailed

    def _query_by_user_preferences(self, query, user_preferences) -> QuerySet:
        if not self.user or not user_preferences:
            return query
        for k, v in AdvertisingUserPreferencesParse(self.user.id, user_preferences).parse().items():
            match k:
                case UserPreferencesResultType.ADV_ID:
                    query = query.filter(P2pAdvertisingMySQL.mongo_id.in_(v))
                case UserPreferencesResultType.USER_ID:
                    query = query.filter(P2pAdvertisingMySQL.user_id.in_(v))
                case _:
                    raise InvalidArgument(message=f"UserPreferencesResultType {k}")
        return query

    @classmethod
    def _sorted_by_params(
            cls,
            query,
            sort_by: AdvertisingSortBy,
            sort_by_type: AdvertisingSortByType
    ):
        # 确定排序方向
        is_desc = sort_by_type == AdvertisingSortByType.DESC
        
        if sort_by == AdvertisingSortBy.PRICE:
            # 价格排序
            price_order = P2pAdvertisingMySQL.price.desc() if is_desc else P2pAdvertisingMySQL.price.asc()
            query = query.order_by(price_order, P2pAdvertisingMySQL.updated_at)
        else:
            # 使用 JSON 字段排序（extra 字段中的嵌套属性）
            json_path = f"$.{sort_by.value}"
            if is_desc:
                # 降序排序
                query = query.order_by(
                    func.json_extract(P2pAdvertisingMySQL.extra, json_path).desc(),
                    P2pAdvertisingMySQL.updated_at
                )
            else:
                # 升序排序
                query = query.order_by(
                    func.json_extract(P2pAdvertisingMySQL.extra, json_path).asc(),
                    P2pAdvertisingMySQL.updated_at
                )
        
        return query

    @classmethod
    def get_invalid_merchant_ids(cls, margin_enough=False):
        model = P2pMerchant
        query = model.query.filter(
                or_(
                    model.shop_status == model.ShopStatus.CLOSED,
                    model.status == model.Status.INACTIVE
                )
            ).with_entities(
                model.user_id
            )
        if margin_enough:
            query = query.filter(model.margin_status == model.MarginStatus.INVALID)
        return {
            c.user_id for c in query.all()
        }

    def search_by_index(
            self,
            base: str,
            quote: str,
            adv_type: P2pBusinessType,
            pay_channel_ids: list[str],
            page: int,
            limit: int,
            *,
            amount: Decimal = None,
            sort_by: AdvertisingSortBy = None,
            sort_by_type: AdvertisingSortByType = None,
            user_preferences: list[AdvertisingUserPreferences] = None,
            only_follow: bool = None,
            margin_enough: bool = False,
    ) -> (int, QuerySet):
        self.check_params(user_preferences)
        query = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.base == base,
            P2pAdvertisingMySQL.quote == quote,
            P2pAdvertisingMySQL.adv_type == adv_type,
            P2pAdvertisingMySQL.status == P2pAdvertisingMySQL.Status.ONLINE
        )
        
        if pay_channel_ids:
            str_pay_channel_ids = [str(pid) for pid in pay_channel_ids]
            query = query.filter(
                func.json_overlaps(P2pAdvertisingMySQL.pay_channel_ids, 
                                  func.json_array(*str_pay_channel_ids))
            )
        
        if amount:
            query = query.filter(
                P2pAdvertisingMySQL.max_limit >= amount,
                P2pAdvertisingMySQL.min_limit <= amount
            )
        
        if sort_by and sort_by_type:
            query = self._sorted_by_params(query, sort_by, sort_by_type)

        user_id__nin = self.get_invalid_merchant_ids(margin_enough)
        if self.user:
            relation_manager = P2pRelationManager(self.user.id)
            user_id__nin.update(set(relation_manager.all_block_data().keys()))
            
            if user_preferences:
                query = self._query_by_user_preferences(query, user_preferences)
            
            if only_follow is not None and only_follow:
                follow_user_ids = list(relation_manager.all_follow_data().keys())
                query = query.filter(P2pAdvertisingMySQL.user_id.in_(follow_user_ids))

        query = query.filter(~P2pAdvertisingMySQL.user_id.in_(user_id__nin))
        
        total = query.count()
        paginated_query = query.offset((page - 1) * limit).limit(limit)
        return total, paginated_query


class P2pAdvListSnapBiz:
    """ 商家广告快照 """
    model = P2pMerAct

    @classmethod
    def before_act_start_save(cls):
        """活动期间10分钟存一次快照基础数据"""
        model = P2pMerAct
        acts = model.query.filter(
            model.status == model.Status.ONLINE
        ).all()
        for act in acts:
            if act.buy_reward:
                save_adv_list_snap(act.fiat, P2pBusinessType.BUY.value)
            if act.sell_reward:
                save_adv_list_snap(act.fiat, P2pBusinessType.SELL.value)

    @classmethod
    def act_start_save(cls):
        """活动开始期间每分钟保存一次"""
        model = P2pMerAct
        acts = model.query.filter(
            model.status == model.Status.ONLINE,
            model.start_at <= now()
        ).all()
        for act in acts:
            if act.buy_reward:
                save_adv_list_snap(act.fiat, P2pBusinessType.BUY.value)
            if act.sell_reward:
                save_adv_list_snap(act.fiat, P2pBusinessType.SELL.value)


@celery_task(queue=CeleryQueues.P2P)
def save_adv_list_snap(fiat, side):
    max_limit = 1000
    side = P2pBusinessType(side)
    base_params = dict(
        base="USDT",
        quote=fiat,
        adv_type=side,
        pay_channel_ids=[],
        page=1,
        limit=max_limit,
        sort_by=AdvertisingSortBy.PRICE,
    )
    if side == P2pBusinessType.BUY:
        _, query = P2pAdvertisingSearch(None).search_by_index(
            **base_params,
            sort_by_type=AdvertisingSortByType.DESC,
        )
    else:
        _, query = P2pAdvertisingSearch(None).search_by_index(
            **base_params,
            sort_by_type=AdvertisingSortByType.ASC,
        )
    data = [i.to_dict(enum_to_name=True) for i in query.all()]
    snap_at = now()
    snapshot = P2pAdvListSnapshotMySQL(
        snap=data,
        snap_at=snap_at,
        fiat=fiat,
        side=side,
    )
    db.session_add_and_commit(snapshot)
    
    # 删除一分钟内,小于当前时间的快照数据
    minuter = snap_at.replace(second=0, microsecond=0)
    P2pAdvListSnapshotMySQL.query.filter(
        P2pAdvListSnapshotMySQL.fiat == fiat,
        P2pAdvListSnapshotMySQL.side == side,
        P2pAdvListSnapshotMySQL.snap_at >= minuter,
        P2pAdvListSnapshotMySQL.snap_at < snap_at
    ).delete()
    db.session.commit()
