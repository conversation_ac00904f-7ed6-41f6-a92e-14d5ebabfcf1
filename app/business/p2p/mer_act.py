import json
from collections import defaultdict
from copy import deepcopy
from datetime import timed<PERSON><PERSON>, datetime
from decimal import Decimal

from app.business.p2p.advertising import TradeLimitation
from app.business.p2p.config import p2p_mer_act_settings
from app.business.p2p.message import P2pMerActRewardFail, P2pMerActPointCancel
from app.business.p2p.permission import P2pPermissionManager
from app.business.p2p.utils import P2pUtils
from app.caches import func
from app.caches.p2p import P2pAssetConfigCache
from app.common import P2pBusinessType
from app.models import db, P2pOrder, P2pOrderEvent, P2pFiatFairPriceSnapshot, P2pMerActUser, HourP2pMerActPoint, \
    P2pMerActRewardHistory, P2pMerchant, GiftHistory, P2pFiatFairPrice, P2pMerActUserReward
from app.models.mongo.p2p.advertising import P2pAdvListSnapshotMySQL
from app.models.mongo.p2p.mer_act import P2pMerActMySQL as P2pMerAct
from app.utils import today_datetime, quantize_amount, amount_to_str
from app.utils.date_ import date_to_datetime, datetime_to_str, dt_to_today, now, dt_to_hour
from app.utils.parser import JsonEncoder


class P2pMerActBiz:
    model = P2pMerAct
    DEFAULT_BASE = "USDT"
    MAX_REWARD_RANK = 10

    @classmethod
    def save_act_hour_point_history(cls, act: model, st_hour, user_ids=None):
        # 修改需调整配套的导出函数 export_users_act_point
        et_hour = st_hour + timedelta(hours=1)
        fair_snap_map = cls._get_fair_snap_map(act.fiat, st_hour, et_hour)
        if not fair_snap_map:
            return
        # 用户报名后才算积分
        user_pass_map = cls._get_act_user_pass_map(act, user_ids)
        asset_info = P2pAssetConfigCache.get_asset_info(cls.DEFAULT_BASE)
        system_min_limit, system_max_limit = P2pUtils.get_base_default_adv_limit(cls.DEFAULT_BASE, act.fiat)
        for side in act.get_valid_side():
            user_point_map = defaultdict(Decimal)
            adv_list_snap_map = cls._get_adv_list_snap_map(act.fiat, st_hour, et_hour, side)
            if not adv_list_snap_map:
                continue
            adv_ids = set()
            for minuter, adv_list in adv_list_snap_map.items():
                # 活动未开始不计算
                if minuter < act.start_at:
                    continue
                fair = fair_snap_map[minuter]
                price, _ = cls._get_fair_price(fair)
                for rank, adv in enumerate(adv_list, 1):
                    user_id = adv["user_id"]
                    adv_price = Decimal(adv["price"])
                    adv_min, adv_max = cls._get_snap_adv_min_max(adv, asset_info, system_min_limit, system_max_limit)
                    # 报名后的用户才计算积分
                    if user_id not in user_pass_map or minuter < user_pass_map[user_id]:
                        continue
                    limit_ret, _ = cls._check_act_limit(adv_min, adv_max, act)
                    if not limit_ret:
                        continue
                    limitation_ret, _ = cls._check_adv_limitation(adv)
                    if not limitation_ret:
                        continue
                    price_ret, _ = cls._check_price_range(side, price, adv_price, fair)
                    if not price_ret:
                        continue
                    adv_ids.add(adv["id"])
                    point = cls._calc_point(price, rank, adv_price)
                    if point:
                        user_point_map[user_id] += point
            # 获取惩罚用户ids
            orders = cls._get_adv_orders(adv_ids, st_hour, et_hour, user_pass_map)
            punish_user_ids = cls._get_hour_punish_user_ids(orders)
            # 惩罚用户积分减半
            for user_id in punish_user_ids:
                if user_id in user_point_map:
                    user_point_map[user_id] /= 2
            # 将写入小时数据
            cls.save_point_history(act, st_hour, side, user_point_map, HourP2pMerActPoint.PointType.ADV)
            db.session.commit()

    @classmethod
    def save_act_day_point_history(cls, act, st_dt):
        # 每日计算积分，根据完单率奖励积分
        et_dt = min(st_dt + timedelta(days=1), act.end_at)
        cancel_user_ids = cls._get_cancel_user_ids(act.act_id)
        for side in act.get_valid_side():
            user_sum_point_map = cls.get_sum_point_map(act.act_id, st_dt, et_dt, side)
            last_hour = et_dt - timedelta(hours=1)
            # 取消资格积分清0
            cancel_point_map = {i: Decimal() for i in cancel_user_ids if i in user_sum_point_map}
            if cancel_point_map:
                cls.save_point_history(act, last_hour, side, cancel_point_map, HourP2pMerActPoint.PointType.REVOKE)

            point_user_ids = set(user_sum_point_map.keys()) - cancel_user_ids
            user_pass_map = cls._get_act_user_pass_map(act, point_user_ids)
            orders = cls._get_day_p2p_orders(act.fiat, st_dt, et_dt, point_user_ids, side)
            # 过滤参加活动前的订单
            orders = [i for i in orders if i.created_at >= user_pass_map[i.merchant_id]]
            # 过滤掉积分请0的用户
            punish_user_ids = cls._get_day_punish_user_ids(orders)
            # 将惩罚用户的积分减为0
            punish_point_map = {i: Decimal() for i in punish_user_ids}
            if punish_point_map:
                cls.save_point_history(act, last_hour, side, punish_point_map, HourP2pMerActPoint.PointType.PUNISH)

            new_sum_point_map = {k: v for k, v in user_sum_point_map.items() if k not in punish_user_ids}
            # 获得今日完单率
            user_finish_rate_map = cls._get_users_day_finish_rate(orders)
            # 计算奖励积分
            order_point_map = dict()
            for user_id, finish_rate in user_finish_rate_map.items():
                if finish_rate and user_id in new_sum_point_map:
                    rate = cls._calc_completion_rate(finish_rate, act.completion_rate_list)
                    order_point_map[user_id] = (rate - 1) * new_sum_point_map[user_id]
            last_hour = et_dt - timedelta(hours=1)
            if order_point_map:
                cls.save_point_history(act, last_hour, side, order_point_map, HourP2pMerActPoint.PointType.ORDER)
            db.session.commit()

            for punish_id in punish_user_ids:
                P2pMerActPointCancel(act.act_id).send_message(punish_id)

    @classmethod
    def _get_cancel_user_ids(cls, act_id) -> set:
        model = P2pMerActUser
        rows = model.query.filter(
            model.act_id == act_id,
            model.status == model.Status.CANCELLED
        ).with_entities(model.user_id).all()
        return {i.user_id for i in rows}

    @classmethod
    def _get_fair_price(cls, fair):
        ptype = P2pFiatFairPrice.PriceType
        if fair["price_type"] == ptype.AUTO.value and (price := Decimal(fair["price"])):
            return price, fair["price_type"]
        else:
            return Decimal(fair["manual_price"]), ptype.MANUAL.value

    @classmethod
    def save_point_history(cls, act, save_hour, side, user_point_map, point_type: HourP2pMerActPoint.PointType):
        # 本函数由外层提交
        user_ids = list(user_point_map.keys())
        if not user_ids:
            return
        # 取今日0点开始的积分
        st = dt_to_today(save_hour)
        et = save_hour + timedelta(hours=1)
        all_point_map = cls.get_sum_point_map(act.act_id, st, et, side)
        p_type = HourP2pMerActPoint.PointType
        # 积分清空
        if point_type in {p_type.PUNISH, p_type.REVOKE}:
            user_point_map = {k: -all_point_map.get(k, Decimal()) for k in user_point_map.keys()}
        # all_point_增加最新数据，重新排序
        for user_id, point in user_point_map.items():
            if user_id not in all_point_map:
                all_point_map[user_id] = point
            else:
                all_point_map[user_id] += point
        sort_tuple = sorted(all_point_map.items(), key=lambda x: x[1], reverse=True)
        rank_map = {i[0]: rank for rank, i in enumerate(sort_tuple, 1)}
        for user_id, point in user_point_map.items():
            # 测试建议结算的0分记录写入
            if point or point_type == p_type.ORDER:
                tmp = HourP2pMerActPoint.get_or_create(
                    report_hour=save_hour,
                    act_id=act.act_id,
                    user_id=user_id,
                    fiat=act.fiat,
                    side=side,
                    point_type=point_type,
                )
                tmp.rank = rank_map[user_id],
                tmp.point = point
                tmp.total_point = all_point_map[user_id]
                db.session.add(tmp)

    # 发放奖励
    @classmethod
    def save_act_day_reward_history(cls, act: model, st_day):
        # 外层commit，保证不重复发放
        model = P2pMerActRewardHistory
        reward_map = {
            P2pBusinessType.BUY: act.buy_reward,
            P2pBusinessType.SELL: act.sell_reward
        }
        et_day = st_day + timedelta(days=1)
        fail_rows = []
        for side in act.get_valid_side():
            reward_amount = reward_map[side]
            reward_asset = act.reward_asset
            # 获取1-24点所有用户的积分
            user_point_map = cls.get_sum_point_map(act.act_id, st_day, et_day, side)
            # 取前 MAX_REWARD_RANK 名发放发放奖励
            reward_user_list = sorted(user_point_map.items(), key=lambda x: x[1], reverse=True)[:cls.MAX_REWARD_RANK]
            reward_user_ids = [i[0] for i in reward_user_list]
            inactive_ids = cls.get_merchant_inactive_users(reward_user_ids)
            abnormal_ids = cls.get_perm_abnormal_users(reward_user_ids)
            # 获取用户奖励历史
            user_reward_map = cls.get_user_reward_map(act, reward_user_ids, side)
            for rank, (user_id, _) in enumerate(reward_user_list, 1):
                rank_rate = cls._calc_rank_rate(rank, act.rank_rate_list)
                if not rank_rate:
                    continue
                amount = reward_amount * rank_rate
                point = user_point_map[user_id]
                base_row = P2pMerActRewardHistory(
                    act_id=act.act_id,
                    user_id=user_id,
                    side=side,
                    amount=amount,
                    asset=reward_asset,
                    rank=rank,
                    point=point,
                    reward_date=st_day.date(),
                    reward_type=act.reward_type,
                    reward_lock_day=act.reward_lock_day,
                    merchant_status=P2pMerchant.Status.ACTIVE,
                    release_status=model.ReleaseStatus.CREATED,
                )
                db.session.add(base_row)
                # 权限异常，发放失败
                if user_id in inactive_ids or user_id in abnormal_ids:
                    if user_id in inactive_ids:
                        base_row.merchant_status = P2pMerchant.Status.INACTIVE
                    if user_id in abnormal_ids:
                        base_row.perm_status = model.PermStatus.INVALID
                    base_row.release_status = model.ReleaseStatus.FAIL
                    fail_rows.append(base_row)
                else:
                    # 增加累计奖励
                    u_row = user_reward_map[user_id]
                    u_row.lock_amount += amount
        # 发放失败的用户触达
        for row in fail_rows:
            P2pMerActRewardFail(row.act_id).send_message(row.user_id)

    @classmethod
    def get_user_reward_map(cls, act, user_ids, side):
        model = P2pMerActUserReward
        rows = model.query.filter(
            model.side == side,
            model.act_id == act.act_id,
            model.user_id.in_(user_ids),
        ).all()
        reward_map = {i.user_id: i for i in rows}
        new_ids = {i for i in user_ids if i not in reward_map.keys()}
        for new_id in new_ids:
            row = P2pMerActUserReward(
                act_id=act.act_id,
                user_id=new_id,
                side=side,
                reward_type=act.reward_type,
                asset=act.reward_asset,
                lock_amount=Decimal(),
            )
            db.session.add(row)
            reward_map[new_id] = row
        return reward_map

    @classmethod
    def update_act_statistic(cls):
        # 更新活动统计
        today_ = today_datetime()
        # 参与人数在报名期间开始统计
        acts = cls.model.query.filter(
            cls.model.status.in_([cls.model.Status.ONLINE, cls.model.Status.OFFLINE]),
            cls.model.apply_start_at <= now(),
            cls.model.end_at >= today_ - timedelta(days=30)  # 补偿30天
        ).all()
        u_model = P2pMerActUser
        for act in acts:
            act.apply_count = u_model.query.filter(u_model.act_id == act.act_id).count()
            act.buy_reward_count = cls._calc_side_reward_count(act.act_id, P2pBusinessType.BUY)
            act.sell_reward_count = cls._calc_side_reward_count(act.act_id, P2pBusinessType.SELL)
        db.session.commit()

    @classmethod
    def export_users_act_point(cls, act, user_ids, st=None, et=None, act_side=None):

        def a2s(x):
            return amount_to_str(str(x))

        st_dt = st or cls.get_act_start_hour(act)
        # 导出的结束时间要多一个小时
        et_dt = et or cls.get_statistic_end_hour(act) + timedelta(hours=1)
        act_et_hour = cls.get_act_end_hour(act)
        items = []
        # 用户报名后才算积分
        user_pass_map = cls._get_act_user_pass_map(act, user_ids)
        if not user_pass_map:
            return []
        fair_snap_map = cls._get_fair_snap_map(act.fiat, st_dt, et_dt)
        if not fair_snap_map:
            return []

        model = P2pOrder
        rows = model.query.filter(
            model.quote == act.fiat,
            model.merchant_id.in_(user_ids),
            model.created_at >= st_dt,
            model.created_at < et_dt,
        ).all()
        all_orders = [i for i in rows if i.created_at >= user_pass_map[i.merchant_id]]
        asset_info = P2pAssetConfigCache.get_asset_info(cls.DEFAULT_BASE)
        system_min_limit, system_max_limit = P2pUtils.get_base_default_adv_limit(cls.DEFAULT_BASE, act.fiat)
        for side in act.get_valid_side():
            if act_side and side != act_side:
                continue
            adv_list_snap_map = cls._get_adv_list_snap_map(act.fiat, st_dt, et_dt, side)
            if not adv_list_snap_map:
                continue
            cur_hour = st_dt.replace(minute=0, second=0, microsecond=0)
            et_hour = et_dt.replace(minute=0, second=0, microsecond=0)
            # 包含最后一个小时
            while cur_hour < et_hour:
                user_hour_point_map = defaultdict(Decimal)
                cur_minuter = cur_hour
                next_hour = cur_hour + timedelta(hours=1)
                # 计算分钟积分
                while cur_minuter < next_hour:
                    minuter = cur_minuter
                    cur_minuter += timedelta(minutes=1)
                    adv_list = adv_list_snap_map.get(minuter)
                    # 活动未开始不计算
                    if minuter < act.start_at or not adv_list:
                        continue
                    hour = minuter.hour
                    # 活动时间左开右闭
                    if hour < act.valid_start_hour or hour >= act.valid_end_hour:
                        continue
                    fair = fair_snap_map[minuter]
                    price, price_type = cls._get_fair_price(fair)
                    for rank, adv in enumerate(adv_list, 1):
                        user_id = adv["user_id"]
                        adv_price = Decimal(adv["price"])
                        adv_min, adv_max = cls._get_snap_adv_min_max(
                            adv, asset_info, system_min_limit, system_max_limit)
                        # 报名后的用户才计算积分
                        if user_id not in user_pass_map:
                            continue
                        side_str = "买" if side == P2pBusinessType.BUY else "卖"
                        rule_str = json.dumps(P2pFiatFairPriceSnapshot.load_rule(fair), cls=JsonEncoder)
                        item = dict(
                            minuter=datetime_to_str(minuter),
                            user_id=user_id,
                            act_id=act.act_id,
                            rank=rank,
                            side=side_str,
                            adv_price=adv_price,
                            adv_number=adv["adv_number"],
                            price=price,
                            rule=rule_str,
                            price_type=price_type,
                            point=Decimal(0),
                            point_type="挂单积分",
                            sort_factor="",
                            offset_percent="",
                            tmp_point="",
                            extra="",
                            error="",
                        )
                        items.append(item)
                        pass_at = user_pass_map[user_id]
                        if minuter < pass_at:
                            item["error"] = f"小于用户审核通过时间 {datetime_to_str(pass_at)}"
                            continue
                        limit_ret, error = cls._check_act_limit(adv_min, adv_max, act)
                        if not limit_ret:
                            item["error"] = error
                            continue
                        limitation_ret, error = cls._check_adv_limitation(adv)
                        if not limitation_ret:
                            item["error"] = error
                            continue
                        price_ret, error = cls._check_price_range(side, price, adv_price, fair)
                        if not price_ret:
                            item["error"] = error
                            continue
                        sort_factor = cls._calc_sort_factor(rank)
                        s_str = a2s(sort_factor)
                        item["sort_factor"] = f"max(0, (1 - {rank} / 100)) = {s_str}"
                        price_str = a2s(price)
                        offset_percent = cls._calc_offset_percent(price, adv_price)
                        o_str = a2s(offset_percent)
                        item["offset_percent"] = \
                            f"abs({a2s(adv_price)} - {price_str}) / {price_str} = {o_str}"

                        tmp_point = cls._calc_tmp_point(offset_percent, sort_factor)
                        item["tmp_point"] = \
                            f"{s_str} * max(0, 1 - {o_str}) * 100 = {a2s(tmp_point)}"

                        point = cls._calc_point(price, rank, adv_price)
                        if point:
                            user_hour_point_map[user_id] += point
                            item["point"] = point
                # 计算小时惩罚积分
                if user_hour_point_map:
                    hour_orders = [
                        i for i in all_orders
                        if cur_hour <= i.created_at < next_hour and i.side == P2pBusinessType.reverse(side)
                    ]
                    punish_user_map = cls._get_hour_punish_user_map(hour_orders)
                    # 惩罚用户积分减半
                    min_delay = p2p_mer_act_settings.min_acceptance_delay
                    max_delay = p2p_mer_act_settings.max_acceptance_delay
                    for user_id, orders in punish_user_map.items():
                        tmp_item = cls.__format_export_empty_item(items[-1])
                        punish_point = user_hour_point_map[user_id] / 2
                        tmp_item.update(dict(
                            minuter=datetime_to_str(next_hour - timedelta(minutes=1)),
                            point=-punish_point,
                            point_type="小时惩罚扣分",
                            error=f"没有接单/接单时间小于{min_delay}秒/大于{max_delay}秒，"
                                  f"订单号: {';'.join([f'{i[0]}, {i[1]}' for i in orders])}",
                        ))
                        items.append(tmp_item)

                # 每日结算
                if items and (next_hour.hour == 0 or next_hour == act_et_hour):
                    tmp_st = dt_to_today(cur_hour)
                    user_sum_point_map = cls.get_sum_point_map(
                        act.act_id, tmp_st, next_hour, side, user_ids, allow_zero=True)
                    p_types = HourP2pMerActPoint.PointType
                    if user_sum_point_map:
                        point_user_ids = set(user_sum_point_map.keys())
                        user_pass_map = cls._get_act_user_pass_map(act, point_user_ids)
                        day_orders = [i for i in all_orders if tmp_st <= i.created_at < next_hour
                                      and i.side == P2pBusinessType.reverse(side)]
                        punish_map = cls._get_day_punish_user_map(day_orders)
                        # 将惩罚用户的积分减为0
                        export_et = datetime_to_str(next_hour - timedelta(minutes=1))
                        for user_id, order_ids in punish_map.items():
                            tmp_item = cls.__format_export_empty_item(items[-1])
                            tmp_item.update(dict(
                                minuter=export_et,
                                point=-user_sum_point_map[user_id],
                                point_type=p_types.PUNISH.value,
                                error=f"商家联系接单超时次数≥3次，积分清0, 订单号: {','.join(order_ids)}",
                            ))
                            items.append(tmp_item)
                        punish_ids = set(punish_map.keys())
                        new_sum_point_map = {k: v for k, v in user_sum_point_map.items() if k not in punish_ids}

                        if new_sum_point_map:
                            # 获得今日完单率
                            user_finish_rate_map = cls._get_users_day_finish_rate(day_orders)
                            # 计算奖励积分
                            for user_id, finish_rate in user_finish_rate_map.items():
                                if finish_rate:
                                    rate = cls._calc_completion_rate(finish_rate, act.completion_rate_list)
                                    tmp_item = cls.__format_export_empty_item(items[-1])
                                    point = (rate - 1) * new_sum_point_map[user_id]
                                    tmp_item.update(dict(
                                        minuter=export_et,
                                        point=point,
                                        point_type=p_types.ORDER.value,
                                        day_rate=rate,
                                        finish_rate=user_finish_rate_map[user_id],
                                        order_info=f"每日积分 * (完单率奖励 - 1) = "
                                                   f"{a2s(new_sum_point_map[user_id])} * ({a2s(rate)} - 1) = {point}",
                                    ))
                                    items.append(tmp_item)

                cur_hour = next_hour
        return items

    @classmethod
    def __format_export_empty_item(cls, item):
        tmp_item = deepcopy(item)
        for k in item.keys():
            if k not in ["user_id", "act_id", "side"]:
                tmp_item[k] = ""
        return tmp_item

    @classmethod
    def export_user_reward_history(cls, act: model, st: datetime):
        reward_map = {
            P2pBusinessType.BUY: act.buy_reward,
            P2pBusinessType.SELL: act.sell_reward
        }
        st_day = date_to_datetime(st.date())
        et_day = cls.get_statistic_end_day(act)
        cur_day = st_day
        model = P2pMerActUser
        act_user_ids = [i.user_id for i in model.query.filter(
            model.act_id == act.act_id,
            model.status.in_([model.Status.PASSED, model.Status.CANCELLED])
        )]
        items = []
        while cur_day < et_day:
            next_day = cur_day + timedelta(days=1)
            for side in act.get_valid_side():
                reward_amount = reward_map[side]
                reward_asset = act.reward_asset
                user_point_map = cls.get_sum_point_map(act.act_id, cur_day, next_day, side)
                reward_user_list = sorted(user_point_map.items(), key=lambda x: x[1], reverse=True)
                side_str = "买" if side == P2pBusinessType.BUY else "卖"
                reward_date = cur_day.date().strftime("%Y-%m-%d")
                for rank, (user_id, _) in enumerate(reward_user_list, 1):
                    rank_rate = cls._calc_rank_rate(rank, act.rank_rate_list) or 0
                    item = dict(
                        act_id=act.act_id,
                        user_id=user_id,
                        point=user_point_map[user_id],
                        side=side_str,
                        rank=rank,
                        reward_date=reward_date,
                        asset=reward_asset,
                        rank_rate=rank_rate,
                        amount=reward_amount * rank_rate,
                    )
                    # 无权限发放失败
                    if rank > cls.MAX_REWARD_RANK:
                        item["error"] = f"排名 {rank} 大于奖励有效排名 {cls.MAX_REWARD_RANK}"
                    items.append(item)
                zero_users = {i for i in act_user_ids if i not in user_point_map}
                for user_id in zero_users:
                    item = dict(
                        act_id=act.act_id,
                        user_id=user_id,
                        point=0,
                        side=side_str,
                        rank='--',
                        reward_date=reward_date,
                        asset=reward_asset,
                        rank_rate=0,
                        amount=0,
                        error="无积分",
                    )
                    items.append(item)
            cur_day = next_day
        return items

    @classmethod
    def _calc_side_reward_count(cls, act_id, side):
        model = P2pMerActRewardHistory
        g_model = GiftHistory
        return model.query.join(g_model, model.gift_id == g_model.id).filter(
            model.release_status == model.ReleaseStatus.RELEASED,
            model.act_id == act_id,
            model.side == side,
            g_model.status.in_([
                g_model.Status.LOCKED,
                g_model.Status.REAL_FROZEN,
                g_model.Status.FINISHED
            ])
        ).with_entities(func.sum(g_model.amount)).scalar() or Decimal()

    @classmethod
    def get_statistic_acts(cls, day_dt):
        statuses = [cls.model.Status.ONLINE, cls.model.Status.OFFLINE]
        acts = cls.model.query.filter(
            cls.model.status.in_(statuses),
            cls.model.start_at <= now(),
            cls.model.end_at >= day_dt - timedelta(days=30)  # 补偿30天
        ).all()
        return acts

    @classmethod
    def get_merchant_inactive_users(cls, user_ids):
        p_model = P2pMerchant
        p2p_mers = p_model.query.filter(
            p_model.user_id.in_(user_ids),
            p_model.status == p_model.Status.INACTIVE
        )
        return {i.user_id for i in p2p_mers}

    @classmethod
    def get_perm_abnormal_users(cls, user_ids):
        abnormal_ids = set(i for i in user_ids if not P2pPermissionManager.check_all_permission(i))
        return abnormal_ids

    @classmethod
    def get_abnormal_user_ids(cls, user_ids):
        inactive_ids = cls.get_merchant_inactive_users(user_ids)
        abnormal_ids = cls.get_perm_abnormal_users(list(i for i in user_ids if i not in inactive_ids))
        return inactive_ids | abnormal_ids

    @classmethod
    def _get_day_p2p_orders(cls, fiat, st_dt, et_dt, user_ids, side):
        model = P2pOrder
        rows = model.query.filter(
            model.merchant_id.in_(user_ids),
            model.created_at >= st_dt,
            model.created_at < et_dt,
            model.quote == fiat,
            model.side == P2pBusinessType.reverse(side)
        ).all()
        return rows

    @classmethod
    def _get_day_punish_user_ids(cls, orders) -> set:
        return set(cls._get_day_punish_user_map(orders).keys())

    @classmethod
    def _get_day_punish_user_map(cls, orders) -> dict:
        """
        对于用户下单后，商家没有接单响应，连续接单超时次数≥3次，积分清0
        """
        max_cancel = 3
        user_dict = defaultdict(list)
        for order in orders:
            if (order.status == P2pOrder.Status.CANCELED and
                    order.cancel_type == P2pOrder.CancelType.CONFIRM_TIMEOUT):
                user_dict[order.merchant_id].append(order.order_id)
            else:
                if len(user_dict[order.merchant_id]) < max_cancel:
                    user_dict[order.merchant_id] = []
        ret_dict = {k: v for k, v in user_dict.items() if len(v) >= max_cancel}
        return ret_dict

    @classmethod
    def _get_users_day_finish_rate(cls, orders):
        # 完单率 规则与 P2pUserTradeSummary.completion_rate 相同，但是订单数量的取值变成了 有效完单数
        # 有效完单数 = Min(每日每个用户的上限，当日跟同一用户成交订单数)，每日上限 = 3
        day_limit = 3
        user_cancel_map = defaultdict(int)
        user_finish_map = defaultdict(lambda: defaultdict(int))
        user_finish_rate_map = dict()
        c_types = {P2pOrder.CancelType.PAY_TIMEOUT,
                   P2pOrder.CancelType.BUYER_CANCEL}
        for order in orders:
            if order.status == P2pOrder.Status.CANCELED and order.cancel_type in c_types:
                user_cancel_map[order.merchant_id] += 1
            elif order.status == P2pOrder.Status.FINISHED:
                count = user_finish_map[order.merchant_id][order.customer_id]
                if count < day_limit:
                    user_finish_map[order.merchant_id][order.customer_id] += 1
        for user_id, finish_map in user_finish_map.items():
            finish_count = sum(finish_map.values())
            cancel_count = user_cancel_map[user_id]
            finish_rate = Decimal(finish_count) / Decimal(finish_count + cancel_count)
            user_finish_rate_map[user_id] = finish_rate
        return user_finish_rate_map

    @classmethod
    def _calc_completion_rate(cls, rate: Decimal, rate_cfg: list[P2pMerAct.Config]):
        if not rate:
            return Decimal()
        for cfg in rate_cfg:
            if cfg.rank_min < rate <= cfg.rank_max:
                return cfg.rank_amount
        return Decimal(1)

    @classmethod
    def _calc_rank_rate(cls, rank: int, rank_cfg: list[P2pMerAct.Config]):
        for cfg in rank_cfg:
            if cfg.rank_min <= rank <= cfg.rank_max:
                return cfg.rank_amount / (cfg.rank_max - cfg.rank_min + 1)

    @classmethod
    def _check_act_limit(cls, adv_min, adv_max, act):
        # 检查广告是否在活动限额内
        if act.min_limit and adv_min > act.min_limit:
            return False, f"广告最小限额 {adv_min} 大于活动最小限额 {act.min_limit}"
        if act.max_limit and adv_max < act.max_limit:
            return False, f"广告最大限额 {adv_max} 小于活动最大限额 {act.max_limit}"
        return True, ""

    @classmethod
    def _get_snap_adv_min_max(cls, adv, asset_info, system_min_limit, system_max_limit):
        # 对应 P2pAdvertisingSchema._get_base_adv_limit 的逻辑
        _min = Decimal(adv["min_limit"] or 0)
        _max = Decimal(adv["max_limit"] or 0)
        if not adv["is_manually_limit"]:
            return system_min_limit, system_max_limit
        if adv["limit_unit"] == adv["base"]:
            return _min or system_min_limit, _max or system_max_limit
        _min = quantize_amount(_min / Decimal(adv["price"]), asset_info['precision']) if _min else system_min_limit
        _max = quantize_amount(_max / Decimal(adv["price"]), asset_info['precision']) if _max else system_max_limit
        return _min, _max

    @classmethod
    def _format_min(cls, dt):
        return dt.replace(second=0, microsecond=0)

    @classmethod
    def _get_hour_punish_user_map(cls, orders):
        """
        对于用户下单后，系统判断商家响应接单速度低于或高于阈值(参数待定)，
        该小时挂单积分奖励减半（如果同一小时内出现多笔订单触发减半，也只会减半一次）
        """
        if not orders:
            return {}
        order_create_map = {i.id: i for i in orders}
        # 到事件表查询
        e_model = P2pOrderEvent
        events = e_model.query.filter(
            e_model.order_id.in_(list(order_create_map.keys())),
            e_model.value == P2pOrder.Status.CONFIRMED.name,
        ).with_entities(e_model.order_id, e_model.created_at).all()
        event_map = {i.order_id: i.created_at for i in events}
        # 计算订单确认时间
        min_delay = p2p_mer_act_settings.min_acceptance_delay
        max_delay = p2p_mer_act_settings.max_acceptance_delay
        punish_map = defaultdict(list)
        for order_id, o in order_create_map.items():
            confirm_at = event_map.get(order_id)
            if not confirm_at and o.cancel_type == P2pOrder.CancelType.CONFIRM_TIMEOUT:
                punish_map[o.merchant_id].append((o.order_id, "商家超时"))
                continue
            if confirm_at:
                seconds = (confirm_at - o.created_at).seconds
                if not min_delay <= seconds < max_delay:
                    punish_map[o.merchant_id].append((o.order_id, f"接单时间:{seconds}秒"))
        return punish_map

    @classmethod
    def _get_hour_punish_user_ids(cls, orders) -> set:
        return set(cls._get_hour_punish_user_map(orders).keys())

    @classmethod
    def _get_adv_orders(cls, adv_ids, st_hour, et_hour, user_pass_map):
        model = P2pOrder
        rows = model.query.filter(
            model.adv_id.in_(adv_ids),
            model.created_at >= st_hour,
            model.created_at < et_hour,
        ).all()
        if not rows:
            return set()
        # 过滤掉参与活动前的订单
        rows = [i for i in rows if i.created_at >= user_pass_map[i.merchant_id]]
        return rows

    @classmethod
    def _check_adv_limitation(cls, adv) -> (bool, str):
        # 检查广告是否设置了不合理的高级设置
        ADV_REGISTER_DAY = 90
        limitation = adv["limitation_filter"]
        for limit in limitation:
            key = limit["key"]
            if key == TradeLimitation.COMPLETION_RATE.name:
                val = Decimal(limit["value"])
                if val > Decimal():
                    return False, f"广告存在完单率大于 {val}% 限制"
            elif key == TradeLimitation.REGISTERED_TIME.name:
                val = Decimal(limit["value"])
                if val >= Decimal(ADV_REGISTER_DAY):
                    return False, f"广告存在注册时间大于等于 {val}天 限制"
        return True, ""

    @classmethod
    def _check_price_range(cls, side, price, adv_price, fair):
        # 检查价格是否在公允价格偏差内
        version = fair.get("version")
        rule = P2pFiatFairPriceSnapshot.load_rule(fair)
        base = Decimal("1")
        if side == P2pBusinessType.BUY:
            left_key, right_key = "buy_range_left", "buy_range_right"
        else:
            left_key, right_key = "sell_range_left", "sell_range_right"
        error = ""
        side_str = "买" if side == P2pBusinessType.BUY else "卖"
        adv_price_str = amount_to_str(adv_price)
        if not version:
            if side == P2pBusinessType.BUY:
                left_price = quantize_amount((base - rule[left_key]) * price, 2)
                ret = adv_price >= left_price
                if not ret:
                    error = f"{side_str} 单价格不符合: {adv_price_str} >= {left_price} ({price} * (1 - {rule[left_key]}))"
            else:
                right_price = quantize_amount((base + rule[right_key]) * price, 2)
                ret = adv_price <= right_price
                if not ret:
                    error = f"{side_str} 单价格不符合: {adv_price_str} <= {right_price} ({price} * (1 - {rule[right_key]}))"
        else:
            left_price = quantize_amount((base - rule[left_key]) * price, 2)
            right_price = quantize_amount((base + rule[right_key]) * price, 2)
            ret = left_price <= adv_price <= right_price
            if not ret:
                error = (f"{side_str} 单价格 {adv_price_str} 不在 "
                         f"[((1 - {rule[left_key]}) * {price}) {left_price}, "
                         f"{right_price} ((1 + {rule[right_key]}) * {price})] 范围内")
        return ret, error

    @classmethod
    def _calc_point(cls, price, rank, adv_price):
        # 积分公式
        sort_factor = cls._calc_sort_factor(rank)
        offset_percent = cls._calc_offset_percent(price, adv_price)
        tmp_point = cls._calc_tmp_point(offset_percent, sort_factor)
        # 最小单位是0.0001，不足0.0001不计算分数
        point = quantize_amount(tmp_point, 4)
        return point

    @classmethod
    def _calc_tmp_point(cls, offset_percent, sort_factor):
        # 积分公式：排序因子 * max(0, 1 - 偏离百分比) * 100
        return sort_factor * max(Decimal(), 1 - offset_percent) * 100

    @staticmethod
    def _calc_sort_factor(rank: int):
        # 排序因子, 产品公式：max(0, (1 - 排名 / 100))
        x = max(0, 1 - rank / p2p_mer_act_settings.max_point_rank)
        return quantize_amount(Decimal(x), 8)

    @staticmethod
    def _calc_offset_percent(price: Decimal, adv_price: Decimal):
        # 偏离百分比, 产品公式：abs(广告价格 - 公允价格) / 公允价格
        x = abs(adv_price - price) / price
        return quantize_amount(Decimal(x), 8)

    @classmethod
    def _get_fair_snap_map(cls, fiat, st_hour, et_hour):
        # 获取本小时的公允价格
        s_model = P2pFiatFairPriceSnapshot
        snap_rows = s_model.query.filter(
            s_model.fiat == fiat,
            s_model.snap_at >= st_hour,
            s_model.snap_at < et_hour
        ).order_by(s_model.snap_at.asc()).all()
        # 将数据以分钟为key转换为字典
        minuter_map = {cls._format_min(i.snap_at): i.snap for i in snap_rows}
        # 如果第一条的时间不是整点，追溯上一条价格数据
        if not snap_rows:
            new_row = (cls._get_pre_fair_snap_row(s_model, fiat, st_hour) or
                       cls._get_next_fair_snap_row(s_model, fiat, st_hour))
            if not new_row:
                return {}
        else:
            new_row = snap_rows[0]
            if snap_rows[0].snap_at.minute != 0:
                if tmp_row := cls._get_pre_fair_snap_row(s_model, fiat, st_hour):
                    new_row = tmp_row
        minuter_map[st_hour] = new_row.snap
        fill_minuter_map = cls._fill_minuter_map(st_hour, et_hour, minuter_map)
        return fill_minuter_map

    @classmethod
    def _get_adv_list_snap_map(cls, fiat, st_hour, et_hour, side):
        # 获取本小时的广告快照
        s_model = P2pAdvListSnapshotMySQL
        snap_rows = s_model.query.filter(
            s_model.fiat == fiat,
            s_model.snap_at >= st_hour,
            s_model.snap_at < et_hour,
            s_model.side == side,
        ).order_by(s_model.snap_at.asc()).all()
        # 将数据以分钟为key转换为字典
        minuter_map = {cls._format_min(i.snap_at): i.snap for i in snap_rows}
        # 如果第一条的时间不是整点，追溯上一条价格数据
        if not snap_rows:
            new_row = (cls._get_pre_adv_snap_row(s_model, fiat, st_hour, side) or
                       cls._get_next_adv_snap_row(s_model, fiat, st_hour, side))
            if not new_row:
                return {}
        else:
            new_row = snap_rows[0]
            if snap_rows[0].snap_at.minute != 0:
                if tmp_row := cls._get_pre_adv_snap_row(s_model, fiat, st_hour, side):
                    new_row = tmp_row
        minuter_map[st_hour] = new_row.snap
        fill_minuter_map = cls._fill_minuter_map(st_hour, et_hour, minuter_map)
        return fill_minuter_map

    @staticmethod
    def _get_pre_adv_snap_row(model, fiat, st_hour, side):
        return model.query.filter(
            model.fiat == fiat,
            model.snap_at <= st_hour,
            model.side == side,
        ).order_by(model.snap_at.desc()).first()

    @staticmethod
    def _get_next_adv_snap_row(model, fiat, st_hour, side):
        return model.query.filter(
            model.fiat == fiat,
            model.snap_at >= st_hour,
            model.side == side,
        ).order_by(model.snap_at.asc()).first()

    @staticmethod
    def _get_pre_fair_snap_row(model, fiat, st_hour):
        return model.query.filter(
            model.fiat == fiat,
            model.snap_at <= st_hour,
        ).order_by(model.snap_at.desc()).first()

    @staticmethod
    def _get_next_fair_snap_row(model, fiat, st_hour):
        return model.query.filter(
            model.fiat == fiat,
            model.snap_at >= st_hour,
        ).order_by(model.snap_at).first()

    @classmethod
    def _fill_minuter_map(cls, st_dt, et_dt, minuter_map):
        # 补充每一分钟的数据
        last_row = minuter_map[st_dt]
        new_map = dict()
        while st_dt < et_dt:
            val = minuter_map.get(st_dt, last_row)
            new_map[st_dt] = val
            last_row = val
            st_dt += timedelta(minutes=1)
        return new_map

    @classmethod
    def _get_act_user_pass_map(cls, act, user_ids=None) -> dict:
        model = P2pMerActUser
        rows = model.query.filter(
            model.act_id == act.act_id,
            model.status == model.Status.PASSED
        ).with_entities(model.user_id, model.pass_at)
        if user_ids:
            rows = rows.filter(model.user_id.in_(list(user_ids)))
        return {i.user_id: i.pass_at for i in rows}

    @classmethod
    def get_sum_point_map(cls, act_id, st, et, side, user_ids=None, allow_zero=False):
        """ 积分汇总 """
        model = HourP2pMerActPoint
        query = model.query.filter(
            model.report_hour >= st,
            model.report_hour < et,
            model.act_id == act_id,
            model.side == side,
        )
        if user_ids:
            query = query.filter(model.user_id.in_(user_ids))
        rows = query.group_by(model.user_id).with_entities(
            model.user_id,
            func.sum(model.point).label("sum_point")
        ).all()
        if allow_zero:
            point_map = {i.user_id: i.sum_point for i in rows}
        else:
            point_map = {i.user_id: i.sum_point for i in rows if i.sum_point > Decimal()}
        return point_map

    @classmethod
    def get_act_newest_day(cls, act: P2pMerAct) -> datetime:
        return min(dt_to_today(act.real_end_at), today_datetime())

    @classmethod
    def get_statistic_end_day(cls, act: P2pMerAct) -> datetime:
        return dt_to_today(min(act.end_at, today_datetime()) - timedelta(seconds=1))

    @classmethod
    def get_act_default_day_dt(cls, act):
        return dt_to_today(act.start_at)

    @classmethod
    def get_statistic_end_hour(cls, act):
        """
        活动可能2中形式，一种
        1. 以整点结束，取上一个小时
        2. 非整点结束，取当前小时
        化简为 dt_to_hour(act.end_at - timedelta(seconds=1))
        """
        act_end = cls.get_act_end_hour(act)
        now_end = dt_to_hour(now()) - timedelta(hours=1)
        return min(act_end, now_end)

    @classmethod
    def get_act_end_hour(cls, act):
        return dt_to_hour(act.real_end_at)

    @classmethod
    def get_act_start_hour(cls, act):
        return act.start_at.replace(minute=0, second=0, microsecond=0)
