import json
from copy import deepcopy
from datetime import timed<PERSON>ta
from decimal import Decimal
from enum import Enum
from typing import Any

from flask import current_app
from flask_babel import gettext as _, force_locale

from app import config, Language
from app.business import ServerClient, UserPreferences, lock_call
from app.business.clients.im import ImServerClient, SystemContentLevel, ImContentType
from app.business.email import send_p2p_email
from app.business.push import send_mobile_push_by_user_ids, get_user_web_lang, get_user_app_lang
from app.business.push_base import PushBusinessHandler
from app.caches.p2p import P2pNoticeCache
from app.common import WebPushChannelType, MessageTitle, NoticePushType, MessageContent, P2pBusinessType, \
    MessageWebLink, CeleryQueues
from app.models import P2pOrder, Message, db, P2pOrderComplaint, P2pUser
from app.models.mongo import UserPayChannelMySQL, P2pPayChannelMySQL
from app.models.mongo.p2p.advertising import P2pAdvertisingMySQL, AutoOfflineAdvReason
from app.models.mongo.p2p.mer_act import P2pMerActMySQL as P2pMerAct
from app.models.mongo.p2p.order import P2pOrderCreateSnapMySQL
from app.utils import now, url_join, batch_iter, current_timestamp, celery_task
from app.utils.parser import JsonEncoder
from app.utils.push import AppPagePath, WebPagePath


class SendType(Enum):
    EMAIL = "邮件"
    PUSH = "push"
    SITE_MAIL = "站内信"
    WEB_PUSH = "web_push"  # web socket 推送消息
    IM = "im"  # im 消息


class SendTiming(Enum):
    KYC_PASS = "kyc审核成功"
    KYC_FAIL = "kyc审核失败"


SITE_TRANSLATION_MAPPER = {
    P2pBusinessType.BUY: _("用户买币"),
    P2pBusinessType.SELL: _("用户卖币")
}


class UserType(Enum):
    BUYER = _("买家")
    SELLER = _("卖家")


class RoleType(Enum):
    SELLER = "seller"
    BUYER = "buyer"
    CUSTOMER = "customer"
    MERCHANT = "merchant"
    PLAINTIFF = "plaintiff"
    DEFENDANT = "defendant"


class EmailMixin:
    USER_ORDER_DETAIL = "/p2p/order-detail"
    ADV_INFO_DETAIL = "/p2p-merchant/advertisement-publish"
    USER_PAY_CHANNEL = "/p2p/user/center"
    MERCHANT_PAY_CHANNEL = "/p2p-merchant/payment"
    COMPLAINT_DETAIL = "/p2p/order/appeal"

    def get_order_info_url(self, order_id: str) -> str:
        return url_join(config["SITE_URL"], self.USER_ORDER_DETAIL, orderId=order_id)

    def get_adv_info_url(self, adv_id: str) -> str:
        return url_join(config["SITE_URL"], self.ADV_INFO_DETAIL, id=adv_id)

    def get_user_pay_channel_url(self):
        return url_join(config["SITE_URL"], self.USER_PAY_CHANNEL)

    def get_merchant_pay_channel_url(self):
        return url_join(config["SITE_URL"], self.MERCHANT_PAY_CHANNEL)

    def get_complaint_url(self, order_id):
        return f"{url_join(config['SITE_URL'], self.COMPLAINT_DETAIL)}/{order_id}"

    @staticmethod
    def format_email_params(user_id, params, fields):
        user_preference = UserPreferences(user_id)
        with force_locale(user_preference.language.value):
            email_params = {**params}
            for field in fields:
                email_params[field] = _(params[field])
        return email_params

    def send_email(self, param: dict[str, Any]):
        user_id = param["user_id"]
        pref = UserPreferences(user_id)
        send_p2p_email.delay(
            user_id,
            pref.language.value,
            self.name,
            json.dumps(param, cls=JsonEncoder, ensure_ascii=False)
        )


class SiteMailMixin:
    mail_title: MessageTitle
    mail_content: MessageContent
    web_link: MessageWebLink
    display_type: Message.DisplayType = Message.DisplayType.POPUP_WINDOW
    app_link: str

    def get_mail_title_and_content(self):
        return self.mail_title, self.mail_content

    def set_web_link(self, url):
        self.web_link = url

    def set_app_link(self, url):
        self.app_link = url

    def set_order_info_link(self, order_id: str):
        self.set_web_link(url_join('', MessageWebLink.P2P_COMPLAINT_PAGE.value.format(order_id=order_id)))
        self.set_app_link(AppPagePath.P2P_ORDER_DETAIL.value.format(order_id=order_id))

    def set_adv_info_web_link(self, adv_id: str):
        self.set_web_link(url_join('', MessageWebLink.P2P_MERCHANT_ADV_PAGE.value, id=adv_id))

    def _create_message(self, user_id: int, params: dict[str, Any]) -> Message:
        title, content = self.get_mail_title_and_content()

        message = Message(
            user_id=user_id,
            title=title,
            content=content,
            params=json.dumps(params, cls=JsonEncoder, ensure_ascii=False),
            extra_info=json.dumps(
                dict(
                    web_link=self.web_link,
                    android_link=self.app_link,
                    ios_link=self.app_link,
                )
            ),
            display_type=self.display_type,
            expired_at=now() + timedelta(days=3),
            channel=Message.Channel.SYSTEM,
        )
        return message

    def send_site_mail(self, param: dict[str, Any]):
        tmp_param = deepcopy(param)
        if "site_url" in tmp_param:
            tmp_param.pop('site_url')
        user_id = tmp_param.pop('user_id')
        message = self._create_message(user_id, tmp_param)
        db.session.add(message)
        db.session.commit()


PUSH_TITLE_CONTENT_MAPP = {
    "remind_receive_order": (_("P2P接单提醒"), _("您发布的广告已有用户下单，请前往确认订单。")),
    "merchant_reject_order": (_("P2P拒绝接单提醒"), _("您发起的P2P订单商家拒绝接单，请重新下单。")),
    "received_order_to_payment": (_("P2P订单已确认"), _("您发起的买币订单商家已确认，请前往付款。")),
    "received_order_wait_payment": (_("P2P订单已确认"), _("您发起的卖币订单商家已确认，请等待商家付款。")),
    "buyer_payment_deadline": (_("P2P订单付款提醒"), _("您的P2P买币订单5分钟后将超时取消，请前往付款。")),
    "paid_wait_release_asset": (_("P2P订单放币提醒"), _("您的P2P订单买家已付款，请前往放币。")),
}


class PushMixin:
    app_push_url: str

    def set_push_order_info_url(self, order_id: id):
        self.app_push_url = AppPagePath.P2P_ORDER_DETAIL.value.format(order_id=order_id)

    def send_push(self, params: dict[str, Any]):
        title, message = PUSH_TITLE_CONTENT_MAPP.get(self.name, (None, None))
        if not title or not message:
            return
        user_id = params['user_id']
        handler = PushBusinessHandler(None)
        if not handler.can_push([user_id]):
            return
        app_lang = get_user_app_lang(user_id).value
        with force_locale(app_lang):
            _title_for_lang = _(title)
            _message_for_lang = _(message)
            send_mobile_push_by_user_ids.delay(
                user_ids=[user_id],
                content=_message_for_lang,
                title=_title_for_lang,
                ttl=0,
                url=self.app_push_url,
                created_at=current_timestamp(to_int=True),
            )
            handler.set_pushed([user_id])


class WebPushMixin:
    web_push_url: WebPagePath

    def send_wp(self, params: dict[str, Any]):
        client = ServerClient()
        title, message = PUSH_TITLE_CONTENT_MAPP.get(self.name, (None, None))
        if not title or not message:
            return
        user_id = params['user_id']
        web_lang = get_user_web_lang(user_id).value
        with force_locale(web_lang):
            _trs_title = _(title)
            _trs_message = _(message)
        try:
            client.notice_user_message(
                user_id,
                WebPushChannelType.NORMAL.value,
                dict(title=_trs_title, content=_trs_message, url=params['site_url'], type=NoticePushType.NOTICE)
            )
        except Exception as e:
            current_app.logger.error(f"p2p server notice error: {e}: params: {params}")
            return


class ImMixin:
    class ImSendTiming(Enum):
        CREATED = "created"  # {"merchant_im_id": ""}
        CONFIRMED = "confirmed"  # {"seller_im_id": "", "buyer_im_id": ""}
        PAYMENT_DEADLINE = "payment_deadline"  # {"seller_im_id": "", "buyer_im_id": ""}
        PAID = "paid"  # {"seller_im_id": "", "buyer_im_id": ""}
        PAID_CERT = "paid_cert"  # {"seller_im_id": "", "buyer_im_id": ""}
        RELEASE_DEADLINE = "release_deadline"  # {"seller_im_id": "", "buyer_im_id": ""}
        FINISHED = "finished"  # {"seller_im_id": "", "buyer_im_id": ""}
        # 取消
        CANCELED_BY_CREATED_CUSTOMER = "canceled_by_created_customer"
        CANCELED_BY_MERCHANT = "canceled_by_merchant"  # {"seller_im_id": "", "buyer_im_id": ""}
        CANCELED_BY_BUYER = "canceled_by_buyer"  # {"seller_im_id": "", "buyer_im_id": ""}
        CANCELED_BY_CONFIRM_TIMEOUT = "canceled_by_confirm_timeout"  # {"seller_im_id": "", "buyer_im_id": ""}
        CANCELED_BY_PAY_TIMEOUT = "canceled_by_pay_timeout"  # {"seller_im_id": "", "buyer_im_id": ""}
        CANCELED_BY_SYSTEM = "canceled_by_system"  # {"seller_im_id": "", "buyer_im_id": ""}

        CANCELED_BY_SERVICE = "canceled_by_service"  # {"seller_im_id": "", "buyer_im_id": ""}
        # 申诉
        CREATED_COMPLAINT = "created_complaint"  # {"plaintiff_im_id": "", "defendant_im_id": ""}
        FINISHED_COMPLAINT = "finished_complaint"  # {"plaintiff_im_id": "", "defendant_im_id": ""}

    MESSAGE_MAPPER = {
        "remind_receive_order": ImSendTiming.CREATED,
        "received_order_to_payment": ImSendTiming.CONFIRMED,
        "received_order_wait_payment": ImSendTiming.CONFIRMED,
        "buyer_payment_deadline": ImSendTiming.PAYMENT_DEADLINE,
        "paid_wait_release_asset": ImSendTiming.PAID,
        "paid_cert": ImSendTiming.PAID_CERT,
        "release_long_time": ImSendTiming.RELEASE_DEADLINE,
        "finished_order": ImSendTiming.FINISHED,
        "merchant_reject_order": ImSendTiming.CANCELED_BY_MERCHANT,
        "buyer_cancelled_order": ImSendTiming.CANCELED_BY_BUYER,
        "created_complaint": ImSendTiming.CREATED_COMPLAINT,
        "finished_complaint": ImSendTiming.FINISHED_COMPLAINT,
        "created_customer_cancelled_order": ImSendTiming.CANCELED_BY_CREATED_CUSTOMER,
    }

    @classmethod
    def get_message_mapper(cls):
        im_message_mapper = {
            # 按照订单方向
            cls.ImSendTiming.CREATED.name: {
                P2pBusinessType.SELL.name: {
                    "message": _(
                        "您有一笔订单待确认，请与对方沟通并确认是否接单，在您未确认接单前，对方的收款方式将不展示给您。"),
                    "content_level": SystemContentLevel.WARNING.name
                },
                P2pBusinessType.BUY.name: {
                    "message": _(
                        "您有一笔订单待确认，请与对方沟通并确认是否接单，在未确认接单前，您的收款详情不会展示给对方。"),
                    "content_level": SystemContentLevel.WARNING.name
                },
            },
            cls.ImSendTiming.CANCELED_BY_CREATED_CUSTOMER.name: {
                RoleType.MERCHANT.name: {
                    "message": _("对方已取消订单。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.CUSTOMER.name: {
                    "message": _("您已取消订单。"),
                    "content_level": SystemContentLevel.INFO.name
                },
            },
            cls.ImSendTiming.CONFIRMED.name: {
                RoleType.BUYER.name: {
                    "message": _("您有一笔订单待支付，付款后，请点击“已付款”按钮。"),
                    "content_level": SystemContentLevel.WARNING.name
                },
                RoleType.SELLER.name: {
                    "message": _("您有一笔订单待对方支付，请及时沟通并等待对方付款。"),
                    "content_level": SystemContentLevel.INFO.name
                },
            },
            cls.ImSendTiming.PAYMENT_DEADLINE.name: {
                RoleType.BUYER.name: {
                    "message": _("您的订单还有五分钟即将超时，超时后将自动取消，请及时付款。"),
                    "content_level": SystemContentLevel.WARNING.name
                },
                RoleType.SELLER.name: {
                    "message": _("您的订单还有五分钟即将付款超时，超时后将自动取消。"),
                    "content_level": SystemContentLevel.INFO.name
                }
            },
            cls.ImSendTiming.PAID.name: {
                RoleType.BUYER.name: {
                    "message": _("已付款，等待对方放币。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.SELLER.name: {
                    "message": _("对方已付款，请查收后确认放币。"),
                    "content_level": SystemContentLevel.WARNING.name
                },
            },
            cls.ImSendTiming.PAID_CERT.name: {
                RoleType.BUYER.name: {
                    "message": _("您已上传了付款凭证，<a>点击查看</a>"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.SELLER.name: {
                    "message": _("对方已上传了付款凭证，<a>点击查看</a>"),
                    "content_level": SystemContentLevel.WARNING.name
                },
            },
            cls.ImSendTiming.RELEASE_DEADLINE.name: {
                RoleType.BUYER.name: {
                    "message": _("已向对方发送放币提醒，请尝试沟通提醒，如长时间未放币，可点击订单申诉联系客服处理。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.SELLER.name: {
                    "message": _("对方已付款，请及时放币。"),
                    "content_level": SystemContentLevel.WARNING.name
                }
            },
            cls.ImSendTiming.FINISHED.name: {
                RoleType.BUYER.name: {
                    "message": _("对方已放币，订单完成。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.SELLER.name: {
                    "message": _("已放币，订单完成。"),
                    "content_level": SystemContentLevel.INFO.name
                },
            },
            cls.ImSendTiming.CANCELED_BY_MERCHANT.name: {
                RoleType.MERCHANT.name: {
                    "message": _("商家拒绝接单，订单已取消。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.CUSTOMER.name: {
                    "message": _("商家拒绝接单，订单已取消。"),
                    "content_level": SystemContentLevel.INFO.name
                },
            },

            cls.ImSendTiming.CANCELED_BY_BUYER.name: {
                RoleType.BUYER.name: {
                    "message": _("您已取消订单。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.SELLER.name: {
                    "message": _("对方已取消订单。"),
                    "content_level": SystemContentLevel.INFO.name
                },
            },
            cls.ImSendTiming.CANCELED_BY_CONFIRM_TIMEOUT.name: {
                RoleType.SELLER.name: {
                    "message": _("确认超时，该订单已取消。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.BUYER.name: {
                    "message": _("确认超时，该订单已取消。"),
                    "content_level": SystemContentLevel.INFO.name
                },
            },
            cls.ImSendTiming.CANCELED_BY_PAY_TIMEOUT.name: {
                RoleType.SELLER.name: {
                    "message": _("付款超时，该订单已取消。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.BUYER.name: {
                    "message": _(
                        "付款超时，该订单已取消，如您已向对方转账，请及时联系对方退回或点击订单申诉联系客服处理。"),
                    "content_level": SystemContentLevel.INFO.name
                },
            },
            cls.ImSendTiming.CANCELED_BY_SERVICE.name: {
                RoleType.BUYER.name: {
                    "message": _("客服介入, 该订单已取消"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.SELLER.name: {
                    "message": _("客服介入, 该订单已取消， 您的数字资产已解冻"),
                    "content_level": SystemContentLevel.INFO.name
                },
            },
            cls.ImSendTiming.CANCELED_BY_SYSTEM.name: {
                RoleType.BUYER.name: {
                    "message": _("订单已被系统取消。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.SELLER.name: {
                    "message": _("该订单已被系统取消，您的数字资产已解冻。"),
                    "content_level": SystemContentLevel.INFO.name
                },
            },
            # 申诉
            cls.ImSendTiming.CREATED_COMPLAINT.name: {
                RoleType.PLAINTIFF.name: {
                    "message": _("您已提交申诉，请等待客服介入，在此期间也可以尝试联系对方沟通解决。"),
                    "content_level": SystemContentLevel.INFO.name
                },
                RoleType.DEFENDANT.name: {
                    "message": _("对方发起了一笔申诉，请与对方沟通并尝试解决。"),
                    "content_level": SystemContentLevel.WARNING.name
                },
            },
            cls.ImSendTiming.FINISHED_COMPLAINT.name: {
                RoleType.PLAINTIFF.name: {
                    "message": _("申诉已完成，请点击申诉进度查看处理详情。"),
                    "content_level": SystemContentLevel.WARNING.name
                },
                RoleType.DEFENDANT.name: {
                    "message": _("申诉已完成，请点击申诉进度查看处理详情。"),
                    "content_level": SystemContentLevel.WARNING.name
                },
            },
        }
        return im_message_mapper

    def __init__(self):
        self.client = ImServerClient()

    def get_im_content(self, order) -> str:
        timing = self.MESSAGE_MAPPER[self.name]
        return timing.name

    @classmethod
    def get_im_recv_send_id(cls, order):
        from app.business.p2p.order_factor import P2pOrderFactor
        return P2pOrderFactor.get_im_user_map(order)

    @classmethod
    def get_system_im_params(cls, order: P2pOrder):
        user_im_mapper = cls.get_im_recv_send_id(order)
        plaintiff_im_id, defendant_im_id = "", ""
        if order.complaint_id:
            complaint: P2pOrderComplaint = P2pOrderComplaint.query.get(order.complaint_id)
            plaintiff_im_id = user_im_mapper[complaint.plaintiff_id]
            defendant_im_id = user_im_mapper[complaint.defendant_id]

        ret = {
            "side": order.side.name,
            "merchant_im_id": user_im_mapper[order.merchant_id],
            "customer_im_id": user_im_mapper[order.customer_id],
            "seller_im_id": user_im_mapper[order.seller_id],
            "buyer_im_id": user_im_mapper[order.buyer_id],
            "plaintiff_im_id": plaintiff_im_id,
            "defendant_im_id": defendant_im_id
        }
        return ret

    def send_order_im(self, order: P2pOrder):
        im_user_mapper = self.get_im_recv_send_id(order)
        if len(im_user_mapper.values()) < 2:
            return
        content = self.get_im_content(order)
        params = self.get_system_im_params(order)
        if not content:
            return
        try:
            self.client.send_text_notification(
                recv_id=im_user_mapper[order.merchant_id],
                send_id=im_user_mapper[order.customer_id],
                content=json.dumps(dict(
                    message_enum=content,
                    params=params
                ), cls=JsonEncoder, ensure_ascii=False)
            )
        except Exception as e:
            current_app.logger.error(f"send order im error: {e}, params: {params} ")
            return


class _P2pMessageMate(type):
    def __new__(mcs, name, bases, dct):
        cls = super().__new__(mcs, name, bases, dct)
        name = getattr(cls, 'name', None)
        if not name:
            return cls
        if SiteMailMixin in bases:
            cls._check_site_mail_config()
        if EmailMixin in bases:
            cls._check_email_config()
        if WebPushMixin in bases:
            cls._check_web_push_config()
        if PushMixin in bases:
            cls._check_push_config()
        return cls

    def _check_site_mail_config(cls):
        if not getattr(cls, 'mail_title', None):
            pass

    def _check_email_config(cls):
        pass

    def _check_web_push_config(cls):
        pass

    def _check_push_config(cls):
        pass


class SendTimingMixin(metaclass=_P2pMessageMate):
    name: str

    @classmethod
    def get_user_type(cls, order: P2pOrder, user_id: int):
        if user_id == order.seller_id:
            return UserType.SELLER
        return UserType.BUYER

    def send_message(self, *args, **kwargs):
        pass


class RemindReceiveOrder(SendTimingMixin, EmailMixin, PushMixin, WebPushMixin, SiteMailMixin, ImMixin):
    """提醒商家接单"""

    # 您发布的广告已有用户下单，请前往确认订单。
    # 订单编号：12345678。
    # 下单方向：用户买币。（如果是用户卖币则显示：用户卖币）
    # 数字货币：123.12
    # USDT；法币：123.34USD。

    name = "remind_receive_order"
    mail_title = MessageTitle.P2P_REMIND_RECEIVE_ORDER
    mail_content = MessageContent.P2P_REMIND_RECEIVE_ORDER

    def send_message(self, order: P2pOrder):
        user_id = order.merchant_id
        order_id = order.order_id
        send_params = {
            "order_id": order_id,
            "side": SITE_TRANSLATION_MAPPER[order.side],
            "base": order.base,
            "quote": order.quote,
            "base_amount": order.base_amount,
            "quote_amount": order.quote_amount,
            "user_id": user_id,
            "site_url": self.get_order_info_url(order_id)
        }
        email_params = self.format_email_params(user_id, send_params, ["side"])
        self.send_email(email_params)
        self.send_wp(send_params)
        self.set_push_order_info_url(order_id)
        self.send_push(send_params)
        self.set_order_info_link(order_id)
        self.send_site_mail(send_params)
        self.send_order_im(order)


class CreatedCustomerCancelledOrder(SendTimingMixin, EmailMixin, SiteMailMixin, ImMixin):
    """用户取消订单"""

    name = "created_customer_cancelled_order"
    mail_title = MessageTitle.P2P_ORDER_CANCELLED
    mail_content = MessageContent.P2P_ORDER_AUTO_CANCELLED

    def send_message(self, order: P2pOrder):
        user_id = order.merchant_id
        param = {
            "order_id": order.order_id,
            "user_id": user_id,
            "cancel_reason": _("对方主动取消"),
            "site_url": self.get_order_info_url(order.order_id)
        }
        email_params = self.format_email_params(user_id, param, ["cancel_reason"])
        self.set_order_info_link(order_id=order.order_id)
        self.send_site_mail(param)
        self.send_order_im(order)
        self.name = "order_auto_cancelled"  # 复用邮件模版
        self.send_email(email_params)


class MerchantRejectOrder(SendTimingMixin, EmailMixin, PushMixin, WebPushMixin, SiteMailMixin, ImMixin):
    """商家拒绝接单"""

    # 您发起的P2P订单商家拒绝接单。
    # 订单编号：12345678。
    # 详情请点击查看。

    name = "merchant_reject_order"
    mail_title = MessageTitle.P2P_MERCHANT_REJECT_ORDER
    mail_content = MessageContent.P2P_MERCHANT_REJECT_ORDER

    def send_message(self, order: P2pOrder):
        params = {
            "order_id": order.order_id,
            "user_id": order.customer_id,
            "site_url": self.get_order_info_url(order.order_id)
        }
        self.send_email(params)
        self.set_order_info_link(order_id=order.order_id)
        self.send_site_mail(params)
        self.send_wp(params)
        self.set_push_order_info_url(order.order_id)
        self.send_push(params)
        self.send_order_im(order)


class ReceivedOrderToPayment(SendTimingMixin, EmailMixin, PushMixin, WebPushMixin, SiteMailMixin, ImMixin):
    """商家已接单,提醒用户付款"""
    """
    您发起的买币订单商家已确认，请前往查看订单并付款。
    订单编号：12345678。
    您需支付：123.34USD，付款后请点击页面上的“已付款”按钮。
    您将收到：123.12USDT。
    """
    name = "received_order_to_payment"
    mail_title = MessageTitle.P2P_RECEIVED_ORDER
    mail_content = MessageContent.P2P_RECEIVED_ORDER_TO_PAYMENT

    def send_message(self, order: P2pOrder):
        _, to_amount = order.from_to_base_amounts
        order_id = order.order_id
        params = {
            "order_id": order_id,
            "quote": order.quote,
            "base": order.base,
            "quote_amount": order.quote_amount,
            "user_id": order.customer_id,
            "to_amount": to_amount,
            "site_url": self.get_order_info_url(order_id)
        }
        self.send_email(params)
        self.send_wp(params)
        self.set_push_order_info_url(order_id)
        self.send_push(params)
        self.set_order_info_link(order_id=order_id)
        self.send_site_mail(params)
        self.send_order_im(order)


class ReceivedOrderWaitPayment(SendTimingMixin, EmailMixin, PushMixin, WebPushMixin, SiteMailMixin, ImMixin):
    """商家已接单, 提醒用户等待商家付款"""
    """
    您发起的卖币订单商家已确认，请前往查看订单并等待商家付款。
    订单编号：12345678。
    您将收到：123.34USD。
    收到后需放币：123.12USDT。
    """
    name = "received_order_wait_payment"
    mail_title = MessageTitle.P2P_RECEIVED_ORDER
    mail_content = MessageContent.P2P_RECEIVED_ORDER_WAIT_PAYMENT

    def send_message(self, order: P2pOrder):
        from_amount, _ = order.from_to_base_amounts
        params = {
            "order_id": order.order_id,
            "base": order.base,
            "quote": order.quote,
            "base_amount": order.base_amount,
            "quote_amount": order.quote_amount,
            "user_id": order.customer_id,
            "from_amount": from_amount,
            "site_url": self.get_order_info_url(order.order_id)
        }
        self.send_email(params)
        self.send_wp(params)
        self.set_push_order_info_url(order.order_id)
        self.send_push(params)
        self.set_order_info_link(order_id=order.order_id)
        self.send_site_mail(params)
        self.send_order_im(order)


class BuyerPaymentDeadline(SendTimingMixin, EmailMixin, PushMixin, WebPushMixin, SiteMailMixin, ImMixin):
    """还剩5分钟，提醒买家付款"""
    """
    您的P2P买币订单5分钟后将超时取消，请前往查看订单并付款。
    订单编号：12345678。
    您需支付：123.34USD，付款后请点击页面上的“已付款”按钮。
    您将收到：123.12USDT。
    """
    name = "buyer_payment_deadline"
    mail_title = MessageTitle.P2P_BUYER_PAYMENT_DEADLINE
    mail_content = MessageContent.P2P_BUYER_PAYMENT_DEADLINE

    def send_message(self, order: P2pOrder):
        _, to_amount = order.from_to_base_amounts
        order_id = order.order_id
        cache = P2pNoticeCache(self.name)
        if cache.has_biz_id(order_id):
            return
        params = {
            "order_id": order_id,
            "quote": order.quote,
            "base": order.base,
            "quote_amount": order.quote_amount,
            "user_id": order.buyer_id,
            "to_amount": to_amount,
            "site_url": self.get_order_info_url(order_id)
        }
        self.send_email(params)
        self.send_wp(params)
        self.set_push_order_info_url(order_id)
        self.send_push(params)
        self.set_order_info_link(order_id=order_id)
        self.send_site_mail(params)
        self.send_order_im(order)
        cache.add_biz_id(order_id)


class PaidWaitReleaseAsset(SendTimingMixin, EmailMixin, PushMixin, WebPushMixin, SiteMailMixin, ImMixin):
    """已付款,待放币"""
    # 您的C2C订单买家已付款，请前往放币。
    # 订单编号：12345678。
    # 您将收到：123.34 USD。
    # 收到款项后，请您通过第三方支付平台核对收款信息，确认无误后再操作放币（123.12
    # USDT）。
    name = "paid_wait_release_asset"
    mail_title = MessageTitle.P2P_PAID_WAIT_RELEASE_ASSET
    mail_content = MessageContent.P2P_PAID_WAIT_RELEASE_ASSET

    cert_name = "paid_cert"

    def send_message(self, order: P2pOrder):
        from_amount, _ = order.from_to_base_amounts
        order_id = order.order_id
        params = {
            "order_id": order_id,
            "quote": order.quote,
            "base": order.base,
            "quote_amount": order.quote_amount,
            "user_id": order.seller_id,
            "from_amount": from_amount,
            "site_url": self.get_order_info_url(order_id)
        }
        self.send_email(params)
        self.send_wp(params)
        self.set_push_order_info_url(order_id)
        self.send_push(params)
        self.set_order_info_link(order_id=order_id)
        self.send_site_mail(params)
        self.send_order_im(order)

    def send_order_im(self, order: P2pOrder):
        super().send_order_im(order)
        if order.cert_file_ids and (cert_file_ids := json.loads(order.cert_file_ids)):
            self.send_cert_im(order, cert_file_ids)

    def send_cert_im(self, order, cert_file_ids):
        from app.business.p2p.order import P2pOrderFileBus

        content = self.MESSAGE_MAPPER[self.cert_name].name
        im_user_mapper = self.get_im_recv_send_id(order)
        params = self.get_system_im_params(order)
        img_keys = P2pOrderFileBus.get_p2p_file_keys(cert_file_ids)
        if not img_keys:
            return
        params["img_keys"] = img_keys
        self.client.send_text_notification(
            recv_id=im_user_mapper[order.merchant_id],
            send_id=im_user_mapper[order.customer_id],
            content=json.dumps(dict(
                message_enum=content,
                params=params
            ), cls=JsonEncoder, ensure_ascii=False)
        )


class ReleaseLongTime(SendTimingMixin, ImMixin):
    """放币超时"""
    name = "release_long_time"

    def send_message(self, order: P2pOrder):
        cache = P2pNoticeCache(self.name)
        if cache.has_biz_id(order.order_id):
            return
        self.send_order_im(order)
        cache.add_biz_id(order.order_id)


class FinishedOrder(SendTimingMixin, EmailMixin, SiteMailMixin, ImMixin):
    """已放币"""
    # 卖家已放币，并且用户收到数字货币，提醒买家订单已完成
    # 您的C2C订单卖家已放币，订单已完成。
    # 订单编号：12345678。
    # 您已收到：123.12
    # USDT。
    # 数字货币已经划转到您的现货账户，立即查看。
    name = "finished_order"
    mail_title = MessageTitle.P2P_FINISHED_ORDER
    mail_content = MessageContent.P2P_FINISHED_ORDER
    web_link = MessageWebLink.SPOT_ASSET_PAGE.value
    app_link = AppPagePath.SPOT_PAGE.value

    def send_message(self, order: P2pOrder):
        _, to_amount = order.from_to_base_amounts
        params = {
            "order_id": order.order_id,
            "base": order.base,
            "user_id": order.buyer_id,
            "to_amount": to_amount
        }
        self.send_email(params)
        self.send_site_mail(params)
        self.send_order_im(order)


class OrderAutoCancel(SendTimingMixin, EmailMixin, SiteMailMixin, ImMixin):
    """订单自动取消"""
    # 您的C2C订单已取消，原因是：XXX。（这里见右侧备注的自动取消原因，按实际情况展示）
    # 订单编号：12345678。
    # 详情请点击查看。
    name = "order_auto_cancelled"
    mail_title = MessageTitle.P2P_ORDER_CANCELLED
    mail_content = MessageContent.P2P_ORDER_AUTO_CANCELLED

    def get_im_content(self, order: P2pOrder):
        if order.cancel_type == P2pOrder.CancelType.PAY_TIMEOUT:
            return ImMixin.ImSendTiming.CANCELED_BY_PAY_TIMEOUT.name
        elif order.cancel_type == P2pOrder.CancelType.CONFIRM_TIMEOUT:
            return ImMixin.ImSendTiming.CANCELED_BY_CONFIRM_TIMEOUT.name
        else:
            return ImMixin.ImSendTiming.CANCELED_BY_SYSTEM.name

    def send_message(self, order: P2pOrder):
        if not order.cancel_type:
            return
        for user_id in [order.customer_id, order.merchant_id]:
            param = {
                "order_id": order.order_id,
                "user_id": user_id,
                "cancel_reason": order.cancel_type.value,
                "site_url": self.get_order_info_url(order.order_id)
            }
            email_params = self.format_email_params(user_id, param, ["cancel_reason"])
            self.send_email(email_params)
            self.set_order_info_link(order_id=order.order_id)
            self.send_site_mail(param)

        self.send_order_im(order)


class BuyerCancelOrder(SendTimingMixin, EmailMixin, SiteMailMixin, ImMixin):
    """买家取消订单"""
    # 您的C2C订单已被买家取消，原因是：XXX。（取买家取消时选择的原因）
    # 订单编号：12345678。
    # 详情请点击查看。
    name = "buyer_cancelled_order"
    mail_title = MessageTitle.P2P_ORDER_CANCELLED
    mail_content = MessageContent.P2P_BUYER_ORDER_CANCELLED

    def send_message(self, order: P2pOrder):
        if not order.cancel_reason:
            return
        user_id = order.seller_id
        params = {
            "order_id": order.order_id,
            "user_id": user_id,
            "cancel_reason": order.cancel_reason.value,
            "site_url": self.get_order_info_url(order.order_id)
        }
        email_params = self.format_email_params(user_id, params, ["cancel_reason"])
        self.send_email(email_params)
        self.set_order_info_link(order_id=order.order_id)
        self.send_site_mail(params)
        self.send_order_im(order)


class CreatedComplaint(SendTimingMixin, EmailMixin, SiteMailMixin, ImMixin):
    # 您的C2C订单买家已发起申诉，原因是：XXX。（取发起申诉时选择的原因）
    # 订单编号：12345678。
    # 详情请点击查看。
    # 您的C2C订单卖家已发起申诉，原因是：XXX。（取发起申诉时选择的原因）
    # 订单编号：12345678。
    # 详情请点击查看。
    name = "created_complaint"
    mail_title = MessageTitle.P2P_CREATED_COMPLAINT
    mail_content = MessageContent.P2P_CREATED_COMPLAINT

    def send_message(self, order: P2pOrder):
        complaint: P2pOrderComplaint = P2pOrderComplaint.query.filter(
            P2pOrderComplaint.order_id == order.id
        ).first()
        if not complaint:
            return
        user_id = complaint.defendant_id
        params = {
            "order_id": order.order_id,
            "reason": complaint.complaint_reason.value,
            "user_id": user_id,
            "user_type": self.get_user_type(order, complaint.plaintiff_id).value,
            "site_url": self.get_order_info_url(order.order_id)
        }
        email_params = self.format_email_params(user_id, params, ["reason", "user_type"])
        self.send_email(email_params)
        self.set_order_info_link(order.order_id)
        self.send_site_mail(params)
        self.send_order_im(order)


class UpdatedComplaint(SendTimingMixin, EmailMixin, SiteMailMixin):
    # 您的C2C申诉有新的通知，立即查看。
    # 订单编号：12345678。
    name = "updated_complaint"
    mail_title = MessageTitle.P2P_UPDATED_COMPLAINT
    mail_content = MessageContent.P2P_UPDATED_COMPLAINT

    def send_message(self, order, send_user_id: int):
        order_id = order.order_id
        params = {
            "order_id": order_id,
            "user_id": send_user_id,
            "site_url": self.get_complaint_url(order_id)
        }
        self.send_email(params)
        self.set_order_info_link(order_id)
        self.send_site_mail(params)


class OperationComplaintForReleaseAssetToBuyer(SendTimingMixin, EmailMixin, SiteMailMixin):
    """
    放币给买家，通知买家
    您的C2C订单客服已处理，数字货币已经划转到您的现货账户，立即查看。
    订单编号：12345678。
    您已收到：123.12USDT。
    """

    name = "operation_complaint_for_release_to_buyer"

    mail_title = MessageTitle.P2P_OPERATION_COMPLAINT
    mail_content = MessageContent.P2P_OPERATION_COMPLAINT_FOR_RELEASE_TO_BUYER
    web_link = MessageWebLink.SPOT_ASSET_PAGE.value
    app_link = AppPagePath.SPOT_PAGE.value

    def send_message(self, order: P2pOrder):
        _, to_amount = order.from_to_base_amounts
        params = {
            "order_id": order.order_id,
            "base": order.base,
            "user_id": order.buyer_id,
            "to_amount": to_amount
        }
        self.send_email(params)
        self.send_site_mail(params)


class OperationComplaintForReleaseAssetToSeller(SendTimingMixin, EmailMixin, SiteMailMixin):
    """
    放币给买家，通知卖家
    您的C2C订单客服已处理，您的数字货币已经划转给买家。
    订单编号：12345678。
    划转数字货币：123.12USDT。
    详情请点击查看。
    """
    name = "operation_complaint_for_release_to_seller"
    mail_title = MessageTitle.P2P_OPERATION_COMPLAINT
    mail_content = MessageContent.P2P_OPERATION_COMPLAINT_FOR_RELEASE_TO_SELLER

    def send_message(self, order: P2pOrder):
        from_amount, _ = order.from_to_base_amounts
        params = {
            "order_id": order.order_id,
            "base": order.base,
            "user_id": order.seller_id,
            "from_amount": from_amount,
            "site_url": self.get_order_info_url(order.order_id)
        }
        self.send_email(params)
        self.set_order_info_link(order.order_id)
        self.send_site_mail(params)


class OperationComplaintForCancelOrderToBuyer(SendTimingMixin, EmailMixin, SiteMailMixin):
    """
    您的C2C订单客服已处理，数字货币已经释放给卖家。
    订单编号：12345678。
    释放数字货币：123.12USDT。
    详情请点击查看。
    """
    name = "operation_complaint_for_cancel_to_buyer"
    mail_title = MessageTitle.P2P_OPERATION_COMPLAINT
    mail_content = MessageContent.P2P_OPERATION_COMPLAINT_FOR_CANCEL_TO_BUYER

    def send_message(self, order: P2pOrder):
        _, to_amount = order.from_to_base_amounts
        params = {
            "order_id": order.order_id,
            "base": order.base,
            "user_id": order.buyer_id,
            "to_amount": to_amount,
            "site_url": self.get_order_info_url(order.order_id)
        }
        self.send_email(params)
        self.set_order_info_link(order.order_id)
        self.send_site_mail(params)


class OperationComplaintForCancelOrderToSeller(SendTimingMixin, EmailMixin, SiteMailMixin):
    """
    您的C2C订单客服已处理，您冻结的数字货币已经释放。
    订单编号：12345678。
    释放数字货币：123.12USDT。
    详情请点击查看。
    """
    name = "operation_complaint_for_cancel_to_seller"
    mail_title = MessageTitle.P2P_OPERATION_COMPLAINT
    mail_content = MessageContent.P2P_OPERATION_COMPLAINT_FOR_CANCEL_TO_SELLER
    web_link = MessageWebLink.SPOT_ASSET_PAGE.value
    app_link = AppPagePath.SPOT_PAGE.value

    def send_message(self, order: P2pOrder):
        from_amount, _ = order.from_to_base_amounts
        params = {
            "order_id": order.order_id,
            "base": order.base,
            "user_id": order.seller_id,
            "from_amount": from_amount,
            "site_url": self.get_order_info_url(order.order_id),
            "spot_url": url_join(config["SITE_URL"], self.web_link)
        }
        self.send_email(params)
        self.set_order_info_link(order.order_id)
        self.send_site_mail(params)


class CanceledComplaint(SendTimingMixin, EmailMixin, SiteMailMixin):
    """
    【CoinEx】C2C订单申诉已取消	您的C2C订单已被买家取消申诉。
    订单编号：12345678。
    详情请点击查看。

    【CoinEx】C2C订单申诉已取消	您的C2C订单已被卖家取消申诉。
    订单编号：12345678。
    详情请点击查看。
    """

    name = "canceled_complaint"
    mail_title = MessageTitle.P2P_CANCELED_COMPLAINT
    mail_content = MessageContent.P2P_CANCELED_COMPLAINT

    def send_message(self, order: P2pOrder):
        complaint: P2pOrderComplaint = P2pOrderComplaint.query.filter(
            P2pOrderComplaint.order_id == order.id
        ).first()
        if not complaint:
            return
        user_id = complaint.defendant_id
        params = {
            "order_id": order.order_id,
            "user_type": self.get_user_type(order, complaint.plaintiff_id).value,
            "user_id": user_id,
            "site_url": self.get_order_info_url(order.order_id)
        }
        email_params = self.format_email_params(user_id, params, ["user_type"])
        self.send_email(email_params)
        self.set_order_info_link(order.order_id)
        self.send_site_mail(params)


class FinishedComplaint(SendTimingMixin, EmailMixin, SiteMailMixin, ImMixin):
    """
    您的C2C订单申诉已完成。
    订单编号：12345678。
    详情请点击查看。
    """

    name = "finished_complaint"
    mail_title = MessageTitle.P2P_FINISHED_COMPLAINT
    mail_content = MessageContent.P2P_FINISHED_COMPLAINT

    def send_message(self, order: P2pOrder):
        for user_id in [order.customer_id, order.merchant_id]:
            param = {
                "order_id": order.order_id,
                "user_id": user_id,
                "site_url": self.get_order_info_url(order.order_id)
            }
            self.send_email(param)
            self.set_order_info_link(order.order_id)
            self.send_site_mail(param)
        self.send_order_im(order)


class RestartComplaint(SendTimingMixin, EmailMixin, SiteMailMixin):
    """
    您的C2C订单申诉被重新开启。
    订单编号：12345678。
    详情请点击查看。
    """
    name = "restart_complaint"
    mail_title = MessageTitle.P2P_RESTART_COMPLAINT
    mail_content = MessageContent.P2P_RESTART_COMPLAINT

    def send_message(self, order: P2pOrder):
        for user_id in [order.customer_id, order.merchant_id]:
            param = {
                "order_id": order.order_id,
                "user_id": user_id,
                "site_url": self.get_order_info_url(order.order_id)
            }
            self.send_email(param)
            self.set_order_info_link(order.order_id)
            self.send_site_mail(param)


class AutoOfflineAdvertising(SendTimingMixin, EmailMixin, SiteMailMixin):
    """
    您的C2C广告单被自动下架。
    原因是：XXXX
    广告单编号：12345678。
    详情请点击查看。
    """
    name = "auto_offline_advertising"
    mail_title = MessageTitle.P2P_AUTO_OFFLINE_ADVERTISING
    mail_content = MessageContent.P2P_AUTO_OFFLINE_ADVERTISING
    app_link = ''

    def send_message(self, adv: P2pAdvertisingMySQL, reason: AutoOfflineAdvReason):
        user_id = adv.user_id
        adv_id = str(adv.mongo_id)
        param = dict(
            user_id=user_id,
            adv_number=adv.adv_number,
            reason=reason.value,
            site_url=self.get_adv_info_url(adv_id)
        )
        email_params = self.format_email_params(user_id, param, ["reason"])
        self.send_email(email_params)
        self.set_adv_info_web_link(adv_id)
        self.send_site_mail(param)


class AddPayChannel(SendTimingMixin, EmailMixin, SiteMailMixin):
    """
    你已成功增加C2C收款方式：银行卡。
    详情请点击查看。
    如非本人操作，请尽快联系我们。
    """
    name = "add_payment_channel"
    mail_title = MessageTitle.P2P_ADD_PAYMENT_CHANNEL
    mail_content = MessageContent.P2P_ADD_PAYMENT_CHANNEL

    def send_message(self, user_pay_chanel: UserPayChannelMySQL):
        from app.business.p2p.pay_channel import PayChannelBus
        user_id = user_pay_chanel.user_id
        channel_id = user_pay_chanel.pay_channel_id

        p2p_user: P2pUser = P2pUser.query.filter(P2pUser.user_id == user_id).first()
        user_preference = UserPreferences(user_id)
        mapper = PayChannelBus(user_preference.language).get_channel_name_by_ids([channel_id])

        pay_channel = mapper.get(str(channel_id))
        if not p2p_user or not pay_channel:
            return
        if p2p_user.is_merchant:
            web_link = MessageWebLink.P2P_MERCHANT_PAY_CHANNEL_PAGE.value
            site_url = self.get_merchant_pay_channel_url()
        else:
            web_link = MessageWebLink.P2P_USER_PAY_CHANNEL_PAGE.value
            site_url = self.get_user_pay_channel_url()
        param = {
            "user_id": user_id,
            "pay_channel": pay_channel,
            "site_url": site_url,
            "support_url": config["SUPPORT_URL"]
        }
        self.send_email(param)
        self.set_web_link(web_link)
        self.set_app_link(AppPagePath.P2P_PAY_CHANNEL.value)
        self.send_site_mail(param)


class PayChannelInvalid(SendTimingMixin, EmailMixin, SiteMailMixin):
    """
    你设置的支付渠道【XXX】已失效，请重新设置。
    详情请点击查看。
    """
    name = "payment_channel_invalid"
    mail_title = MessageTitle.P2P_PAYMENT_CHANNEL_INVALID
    mail_content = MessageContent.P2P_PAYMENT_CHANNEL_INVALID

    def send_message(self, pay_channel: P2pPayChannelMySQL):
        from app.business.p2p.pay_channel import PayChannelBus
        user_ids = {u.user_id for u in 
                        UserPayChannelMySQL.query.filter_by(
                                pay_channel_id=pay_channel.mongo_id
                            ).with_entities(
                                UserPayChannelMySQL.user_id
                            ).all()}
        p2p_user_mapper = {}
        for ids in batch_iter(user_ids, 1000):
            p2p_user_mapper.update({
                i.user_id: i for i in P2pUser.query.filter(P2pUser.user_id.in_(ids)).all()
            })
        for user_id in user_ids:
            p2p_user = p2p_user_mapper.get(user_id)
            if not p2p_user:
                continue
            if p2p_user.is_merchant:
                web_link = MessageWebLink.P2P_MERCHANT_PAY_CHANNEL_PAGE.value
                site_url = self.get_merchant_pay_channel_url()
            else:
                web_link = MessageWebLink.P2P_USER_PAY_CHANNEL_PAGE.value
                site_url = self.get_user_pay_channel_url()
            user_preference = UserPreferences(user_id)
            mapper = PayChannelBus(user_preference.language).get_channel_name_by_ids([pay_channel.mongo_id])

            pay_channel_name = mapper.get(str(pay_channel.mongo_id))
            param = {
                "user_id": user_id,
                "pay_channel": pay_channel_name,
                "site_url": site_url
            }
            self.send_email(param)
            self.set_web_link(web_link)
            self.set_app_link(AppPagePath.P2P_PAY_CHANNEL.value)
            self.send_site_mail(param)


class BecomeMerchant(EmailMixin):
    MERCHANT_PAGE = "/p2p-merchant"
    SECURITY_PAGE = "/my/info/security"

    def send_message(self, user):
        if user.has_totp_or_web_auth:
            params = {
                "site_url": url_join(config["SITE_URL"], self.MERCHANT_PAGE)
            }
            self.name = "become_merchant"
            # 发送邮件
        else:
            params = {
                "site_url": url_join(config["SITE_URL"], self.SECURITY_PAGE)
            }
            self.name = "become_merchant_not_2fa"
        params["user_id"] = user.id
        self.send_email(params)


class OrderMsg:

    def send_create_message(self, order: P2pOrder):
        from app.business.p2p.order import P2pOrderCreateSnapBiz

        user_im_mapper = ImMixin.get_im_recv_send_id(order)
        snap_data = P2pOrderCreateSnapMySQL.get_by_order_id(order.id)
        ct_im_id = user_im_mapper[order.customer_id]
        mc_im_id = user_im_mapper[order.merchant_id]
        client = ImServerClient()
        if msg := snap_data.adv.get("say_hi_msg"):
            client.send_text_notification(mc_im_id,
                                          ct_im_id,
                                          msg,
                                          ImContentType.SAY_HI)
        lang_data = P2pOrderCreateSnapBiz(snap_data).get_channel_lang_data()
        self.send_order_card_msg(user_im_mapper, order, lang_data)

    @staticmethod
    def send_order_card_msg(user_im_mapper, order, lang_data):
        client = ImServerClient()
        ct_im_id = user_im_mapper[order.customer_id]
        mc_im_id = user_im_mapper[order.merchant_id]
        card_data = {
            "order_id": order.order_id,
            "created_at": order.created_at,
            "buyer_im_id": user_im_mapper[order.buyer_id],
            "seller_im_id": user_im_mapper[order.seller_id],
            "customer_im_id": user_im_mapper[order.customer_id],
            "merchant_im_id": user_im_mapper[order.merchant_id],
            "side": order.side.name,
            "quote": order.quote,
            "quote_amount": order.quote_amount,
            "base": order.base,
            "user_base_amount": order.base_amount,
            "merchant_base_amount": order.merchant_base_amount,
            "merchant_fee_amount": order.merchant_fee_amount,
            "price": order.price,
            "lang_data": {k: {"name": v["name"]} for k, v in lang_data.items()}
        }
        content = json.dumps(card_data, cls=JsonEncoder)
        client.send_text_notification(mc_im_id, ct_im_id, content, ImContentType.ORDER_CARD)


class P2pMerActMessage:
    email_path = ""
    site_title = ""
    site_content = ""
    site_url: MessageWebLink = ""
    site_display_type: Message.DisplayType = Message.DisplayType.POPUP_WINDOW

    def __init__(self, act_id):
        self.act = P2pMerAct.query.filter_by(act_id=act_id).first()

    def send_message(self, user_id, **kwargs):
        lang_map = self.act.lang_map
        lang = UserPreferences(user_id).language.name
        if lang not in lang_map:
            lang = Language.DEFAULT.name
        act_name = lang_map[lang]["display_name"]

        params = {
            "act_name": act_name,
            "act_url": MessageWebLink.P2P_MER_ACT_PAGE.value.format(act_id=self.act.act_id),
            "mer_url": MessageWebLink.P2P_MERCHANT_PAGE.value,
            "act_help_url": MessageWebLink.P2P_HELP_PAGE.value,
            "user_id": user_id,
            **kwargs
        }

        if self.email_path:
            self._send_email(params)
        if self.site_title:
            self._send_site_mail(params)

    def _send_email(self, params: dict[str, Any]):
        for key, value in params.items():
            if key.endswith('_url'):
                params[key] = url_join(config["SITE_URL"], value)
        user_id = params["user_id"]
        pref = UserPreferences(user_id)
        send_p2p_email.delay(
            user_id,
            pref.language.value,
            self.email_path,
            json.dumps(params, cls=JsonEncoder, ensure_ascii=False)
        )

    def _send_site_mail(self, params: dict[str, Any]):
        copy_params = deepcopy(params)
        if "site_url" in copy_params:
            copy_params.pop('site_url')
        user_id = copy_params.pop('user_id')
        if self.site_url == MessageWebLink.P2P_MER_ACT_PAGE:
            site_url = self.site_url.value.format(act_id=self.act.act_id)
        else:
            site_url = self.site_url.value if self.site_url else ""
        message = Message(
            user_id=user_id,
            title=self.site_title,
            content=self.site_content,
            params=json.dumps(params, cls=JsonEncoder, ensure_ascii=False),
            extra_info=json.dumps(
                dict(
                    web_link=site_url,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=self.site_display_type,
            expired_at=now() + timedelta(days=3),
            channel=Message.Channel.SYSTEM,
        )
        db.session.add(message)
        db.session.commit()


class P2pMerActApplySuccess(P2pMerActMessage):
    site_title = MessageTitle.P2P_MER_ACT_APPLY_SUCCESS
    site_content = MessageContent.P2P_MER_ACT_APPLY_SUCCESS


class P2pMerActAuditSuccess(P2pMerActMessage):
    email_path = "mer_act_audit_pass"
    site_title = MessageTitle.P2P_MER_ACT_AUDIT_SUCCESS
    site_content = MessageContent.P2P_MER_ACT_AUDIT_SUCCESS
    site_url = MessageWebLink.P2P_MERCHANT_PAGE


class P2pMerActAuditFail(P2pMerActMessage):
    email_path = "mer_act_audit_fail"
    site_title = MessageTitle.P2P_MER_ACT_AUDIT_FAIL
    site_content = MessageContent.P2P_MER_ACT_AUDIT_FAIL


class P2pMerActAuditCancel(P2pMerActMessage):
    site_title = MessageTitle.P2P_MER_ACT_AUDIT_CANCEL
    site_content = MessageContent.P2P_MER_ACT_AUDIT_CANCEL


class P2pMerActRewardSuccess(P2pMerActMessage):
    email_path = "mer_act_reward_success"
    site_title = MessageTitle.P2P_MER_ACT_REWARD_SUCCESS
    site_content = MessageContent.P2P_MER_ACT_REWARD_SUCCESS
    site_url = MessageWebLink.P2P_MER_ACT_PAGE


class P2pMerActRewardFreeze(P2pMerActMessage):
    site_title = MessageTitle.P2P_MER_ACT_REWARD_FREEZE
    site_content = MessageContent.P2P_MER_ACT_REWARD_FREEZE


class P2pMerActRewardCancel(P2pMerActMessage):
    site_title = MessageTitle.P2P_MER_ACT_REWARD_CANCEL
    site_content = MessageContent.P2P_MER_ACT_REWARD_CANCEL


class P2pMerActRewardFail(P2pMerActMessage):
    email_path = "mer_act_reward_fail"
    site_title = MessageTitle.P2P_MER_ACT_REWARD_FAIL
    site_content = MessageContent.P2P_MER_ACT_REWARD_FAIL


class P2pMerActPointCancel(P2pMerActMessage):
    site_title = MessageTitle.P2P_MER_ACT_POINT_CANCEL
    site_content = MessageContent.P2P_MER_ACT_POINT_CANCEL


class P2pMarginMessageBase:
    email_path = ""
    site_title = ""
    site_content = ""
    web_link = ""

    def send_message(self, user_id, grace_deadline=None, amount=None):
        grace_deadline = grace_deadline.strftime("%Y-%m-%d") if grace_deadline else None
        params = {
            "user_id": user_id,
            "grace_deadline": grace_deadline,
            "amount": amount,
        }
        self._send(params)

    def _send(self, params: dict[str, Any]):
        if self.email_path:
            self._send_email(params)
        if self.site_title:
            self._send_site_mail(params)

    def _send_email(self, params: dict[str, Any]):
        user_id = params["user_id"]
        pref = UserPreferences(user_id)
        for key, value in params.items():
            if key.endswith('_url'):
                params[key] = url_join(config["SITE_URL"], value)
        send_p2p_email.delay(
            user_id,
            pref.language.value,
            self.email_path,
            json.dumps(params, cls=JsonEncoder, ensure_ascii=False)
        )

    def _send_site_mail(self, params: dict[str, Any]):
        copy_params = deepcopy(params)
        user_id = copy_params.pop('user_id')
        extra_info = ""
        if self.web_link:
            extra_info = json.dumps(
                dict(
                    web_link=self.web_link.value,
                    android_link="",
                    ios_link="",
                )
            )
        message = Message(
            user_id=user_id,
            title=self.site_title,
            content=self.site_content,
            params=json.dumps(params, cls=JsonEncoder, ensure_ascii=False),
            extra_info=extra_info,
            display_type=Message.DisplayType.POPUP_WINDOW,
            expired_at=now() + timedelta(days=3),
            channel=Message.Channel.SYSTEM,
        )
        db.session.add(message)
        db.session.commit()


class P2pMarginPaymentMessage(P2pMarginMessageBase):
    email_path = "p2p_margin_payment"
    site_title = MessageTitle.P2P_MARGIN_PAYMENT
    site_content = MessageContent.P2P_MARGIN_PAYMENT


class P2pMarginShortfallMessage(P2pMarginMessageBase):
    email_path = "p2p_margin_shortfall"
    site_title = MessageTitle.P2P_MARGIN_SHORTFALL
    site_content = MessageContent.P2P_MARGIN_SHORTFALL


class P2pMarginChangeMessage(P2pMarginMessageBase):
    email_path = "p2p_margin_change"
    site_title = MessageTitle.P2P_MARGIN_CHANGE
    site_content = MessageContent.P2P_MARGIN_CHANGE


class P2pMarginChangeZeroMessage(P2pMarginMessageBase):
    site_title = MessageTitle.P2P_MARGIN_CHANGE
    site_content = MessageContent.P2P_MARGIN_CHANGE_ZERO


class P2pMerchantCancelMessage(P2pMarginMessageBase):
    email_path = "p2p_mer_cancel"
    site_title = MessageTitle.P2P_MER_CANCEL
    site_content = MessageContent.P2P_MER_CANCEL


class P2pMerCompensationMessage(P2pMarginMessageBase):
    """商家保证金扣除通知"""
    email_path = "p2p_mer_compensation"
    site_title = MessageTitle.P2P_MER_COMPENSATION
    site_content = MessageContent.P2P_MER_COMPENSATION
    web_link: MessageWebLink = MessageWebLink.P2P_MERCHANT_PAGE

    def send_message(self, user_id, amount):
        params = {
            "user_id": user_id,
            "amount": amount,
            "mer_url": MessageWebLink.P2P_MERCHANT_PAGE.value,
            "rule_url": MessageWebLink.P2P_MER_STANDARD_PAGE.value,
        }
        self._send(params)


class P2pMarginPenaltyMessage(P2pMarginMessageBase):
    """商家违规扣款通知"""
    email_path = "p2p_mer_penalty"
    site_title = MessageTitle.P2P_MARGIN_PENALTY
    site_content = MessageContent.P2P_MARGIN_PENALTY
    web_link: MessageWebLink = MessageWebLink.P2P_MERCHANT_PAGE

    def send_message(self, user_id, amount):
        params = {
            "user_id": user_id,
            "amount": amount,
            "rule_url": MessageWebLink.P2P_MER_STANDARD_PAGE.value,
            "mer_url": MessageWebLink.P2P_MERCHANT_PAGE.value,
        }
        self._send(params)


class P2pMarginExcessRefundMessage(P2pMarginMessageBase):
    """商家保证金超额返还通知"""
    email_path = "p2p_mer_excess_refund"
    site_title = MessageTitle.P2P_MARGIN_EXCESS_REFUND
    site_content = MessageContent.P2P_MARGIN_EXCESS_REFUND
    web_link: MessageWebLink = MessageWebLink.P2P_MERCHANT_PAGE

    def send_message(self, user_id, amount):
        params = {
            "user_id": user_id,
            "amount": amount,
            "mer_url": MessageWebLink.P2P_MERCHANT_PAGE.value,
        }
        self._send(params)


class P2pUserCompensationMessage(P2pMarginMessageBase):
    """P2P交易赔付到账通知"""
    email_path = "p2p_user_compensation"
    site_title = MessageTitle.P2P_USER_COMPENSATION
    site_content = MessageContent.P2P_USER_COMPENSATION
    web_link: MessageWebLink = MessageWebLink.SPOT_ASSET_HISTORY_PAGE

    def send_message(self, user_id, amount):
        params = {
            "user_id": user_id,
            "amount": amount,
        }
        self._send(params)


@celery_task(queue=CeleryQueues.P2P)
def send_p2p_margin_change_message(user_id, amount, old_amount):
    amount, old_amount = Decimal(amount), Decimal(old_amount)
    if amount == Decimal() or old_amount > amount:
        P2pMarginChangeZeroMessage().send_message(user_id, amount=amount)
    else:
        P2pMarginChangeMessage().send_message(user_id, amount=amount)


@celery_task(queue=CeleryQueues.P2P)
@lock_call(with_args=True)
def send_order_status_msg(order_id, status):
    statuses = P2pOrder.Status
    cancel_types = P2pOrder.CancelType
    order = P2pOrder.query.get(order_id)
    status = statuses(status)
    match status:
        case statuses.CREATED:
            OrderMsg().send_create_message(order)
            RemindReceiveOrder().send_message(order)
        case statuses.CONFIRMED:
            if order.side == P2pBusinessType.BUY:
                ReceivedOrderToPayment().send_message(order)
            else:
                ReceivedOrderWaitPayment().send_message(order)
        case statuses.PAID:
            PaidWaitReleaseAsset().send_message(order)
        case statuses.FINISHED:
            FinishedOrder().send_message(order)
        case statuses.CANCELED:
            cancel_type = order.cancel_type
            if cancel_type == cancel_types.BUYER_CANCEL:
                BuyerCancelOrder().send_message(order)
            elif cancel_type == cancel_types.MERCHANT_CREATED_CANCEL:
                MerchantRejectOrder().send_message(order)
            elif cancel_type == cancel_types.CUSTOMER_CREATED_CANCEL:
                CreatedCustomerCancelledOrder().send_message(order)
            else:
                OrderAutoCancel().send_message(order)


class ComplaintBroadMsgType(Enum):
    CREATE = "create"
    AUDIT_UPDATE = "audit_update"
    CANCEL_ORDER = "cancel_order"
    RELEASE_ASSET = "release_asset"
    CANCEL = "cancel"
    FINISH = "finish"
    REOPEN = "reopen"


@celery_task(queue=CeleryQueues.P2P)
@lock_call(with_args=True)
def send_complaint_broad_msg(order_id, msg_type):

    order = P2pOrder.query.get(order_id)
    complaint = P2pOrderComplaint.query.get(order.complaint_id)
    msg_type = ComplaintBroadMsgType(msg_type)
    match msg_type:
        case ComplaintBroadMsgType.CREATE:
            CreatedComplaint().send_message(order)
        case ComplaintBroadMsgType.REOPEN:
            RestartComplaint().send_message(order)
        case ComplaintBroadMsgType.CANCEL:
            CanceledComplaint().send_message(order)
        case ComplaintBroadMsgType.FINISH:
            FinishedComplaint().send_message(order)
        case ComplaintBroadMsgType.AUDIT_UPDATE:
            UpdatedComplaint().send_message(order, complaint.plaintiff_id)
            UpdatedComplaint().send_message(order, complaint.defendant_id)
        case ComplaintBroadMsgType.RELEASE_ASSET:
            OperationComplaintForReleaseAssetToBuyer().send_message(order)
            OperationComplaintForReleaseAssetToSeller().send_message(order)
        case ComplaintBroadMsgType.CANCEL_ORDER:
            OperationComplaintForCancelOrderToBuyer().send_message(order)
            OperationComplaintForCancelOrderToSeller().send_message(order)


@celery_task(queue=CeleryQueues.P2P)
@lock_call(with_args=True)
def send_complaint_user_msg(order_id, user_id):
    order = P2pOrder.query.get(order_id)
    UpdatedComplaint().send_message(order, user_id)


@celery_task(queue=CeleryQueues.P2P)
@lock_call(with_args=True)
def send_message_to_user_by_pay_channel_invalid_task(pay_channel_id: str):
    pay_channel = P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.mongo_id==pay_channel_id).first()
    if not pay_channel:
        return
    PayChannelInvalid().send_message(pay_channel)
