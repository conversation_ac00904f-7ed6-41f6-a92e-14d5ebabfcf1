# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from typing import List, Iterable

from sqlalchemy import func

from app.business import ExchangeLogDB, PriceManager, TradeLogDB, PerpetualLogDB
from app.business.amm import batch_get_user_amm_assets
from app.common import AccountBalanceType, PrecisionEnum
from app.models import MarginLoanOrder, SubAccount
from app.utils import quantize_amount, current_timestamp, batch_iter


class UserTopBalanceRankHelper:

    @classmethod
    def get_user_rank_data(cls,
                           timestamp: int,
                           account_type: AccountBalanceType | None,
                           asset: str | None,
                           user_ids: Iterable[int] | None,
                           page: int | None = None,
                           limit: int | None = None) ->list:
        table = ExchangeLogDB.user_top_balance_rank_table(timestamp)
        if not table.exists():
            return []
        account_type = account_type.value if account_type else 'ALL'
        asset = asset or 'ALL'
        where = f"account_type='{account_type}' AND asset='{asset}' AND user_id!=0"
        if user_ids:
            user_ids_str = ','.join(map(str, user_ids))
            where += f' and `user_id` in ({user_ids_str})'
        if not page and not limit:
            rank_data = table.select("user_id", "balance", "balance_usd",
                                     where=where, order_by="balance_usd desc")
            return rank_data
        offset = (page - 1) * limit
        rank_data = table.select("user_id", "balance", "balance_usd",
                                where=where,
                                order_by="balance_usd desc",
                                limit=(offset, limit))
        return rank_data
    
    @classmethod
    def get_total_user_count(cls,
                             timestamp: int,
                             account_type: AccountBalanceType | None,
                             asset: str | None) -> int:
        table = ExchangeLogDB.user_top_balance_rank_table(timestamp)
        if not table.exists():
            return 0
        account_type = account_type.value if account_type else 'ALL'
        asset = asset or 'ALL'
        where = f"account_type='{account_type}' AND asset='{asset}' AND user_id!=0"
        data = table.select("count(*)", where=where)
        if not data:
            return 0
        return data[0][0]

    @classmethod
    def get_total_usd(cls,
                      timestamp: int,
                      account_type: AccountBalanceType | None,
                      asset: str | None) -> tuple[Decimal, Decimal]:
        table = ExchangeLogDB.user_top_balance_rank_table(timestamp)
        if not table.exists():
            return Decimal(), Decimal()
        account_type = account_type.value if account_type else 'ALL'
        asset = asset or 'ALL'
        where = f"account_type='{account_type}' AND asset='{asset}' AND user_id=0"
        data = table.select("balance, balance_usd", where=where)
        if not data:
            return Decimal(), Decimal()
        return data[0]


class UserTotalBalanceHelper(object):

    def __init__(self, main_user_ids: Iterable[int]):
        self.main_user_ids = main_user_ids
        self.sub_account_mapping = self.get_sub_account_mapping(main_user_ids)
        self.asset_prices = PriceManager.assets_to_usd()

    def _get_user_loan_asset_usd(self, user_ids: List[int]) -> List:
        """杠杆借币"""
        loans = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id.in_(user_ids),
            MarginLoanOrder.status == MarginLoanOrder.StatusType.PASS
        ).with_entities(
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset,
            func.sum(MarginLoanOrder.unflat_amount).label("total")
        ).group_by(
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset
        ).all()
        loans = [(v.user_id, v.asset, v.total) for v in loans]
        return loans

    @classmethod
    def get_sub_account_mapping(cls, _main_user_ids: Iterable[int]):
        q = SubAccount.query.filter(
            SubAccount.main_user_id.in_(_main_user_ids)
        ).with_entities(SubAccount.main_user_id, SubAccount.user_id).all()
        return {v.user_id: v.main_user_id for v in q}

    def get_user_balances(self):
        current_ts = current_timestamp(to_int=True)
        spot_table = TradeLogDB.slice_balance_table(current_ts)
        if not spot_table:
            spot_table = TradeLogDB.slice_balance_table(current_ts - 3600)
        perpetual_table = PerpetualLogDB.slice_balance_table(current_ts)
        if not perpetual_table:
            perpetual_table = PerpetualLogDB.slice_balance_table(current_ts - 3600)
        all_user_ids = list(self.main_user_ids) + list(self.sub_account_mapping.keys())
        total_records = []
        if spot_table:
            for uids in batch_iter(all_user_ids, 2000):
                user_id_string = ','.join(map(str, uids))
                records = spot_table.select(
                    'user_id',
                    'asset',
                    'SUM(`balance`) `balance`',
                    where=f'user_id in ({user_id_string})',
                    group_by='`user_id`, `asset`'
                )
                total_records.extend(records)
        if perpetual_table:
            for uids in batch_iter(all_user_ids, 2000):
                user_id_string = ','.join(map(str, uids))
                records = perpetual_table.select(
                    'user_id',
                    'asset',
                    'SUM(`balance`) `balance`',
                    where=f'user_id in ({user_id_string})',
                    group_by='`user_id`, `asset`'
                )
                total_records.extend(records)
        user_balance_data = defaultdict(Decimal)
        for _record in total_records:
            _uid, _asset, _balance = _record
            user_balance_data[self.sub_account_mapping.get(_uid, _uid)] += \
                self.asset_prices.get(_asset, Decimal()) * Decimal(_balance)
        amm_balances = batch_get_user_amm_assets(all_user_ids)
        for _uid, _balance_data in amm_balances.items():
            for _asset, _balance in _balance_data.items():
                user_balance_data[self.sub_account_mapping.get(_uid, _uid)] += \
                    self.asset_prices.get(_asset, Decimal()) * Decimal(_balance)
        loan_balances = self._get_user_loan_asset_usd(all_user_ids)
        for _record in loan_balances:
            _uid, _asset, _balance = _record
            user_balance_data[self.sub_account_mapping.get(_uid, _uid)] -= \
                self.asset_prices.get(_asset, Decimal()) * Decimal(_balance)
        return {_uid: quantize_amount(_usd, PrecisionEnum.CASH_PLACES)
                for _uid, _usd in user_balance_data.items()}

    @classmethod
    def get_users_total_balance(self, ts: int, user_ids: Iterable) -> dict:
        if not user_ids:
            return {}
        table = ExchangeLogDB.user_account_balance_sum_table(ts)
        in_ = ','.join(map(str, user_ids))
        rows = table.select("user_id, balance_usd", where=f"user_id in ({in_})")
        return {x: y for x, y in rows}
