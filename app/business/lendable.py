# -*- coding: utf-8 -*-
from decimal import Decimal

from flask import current_app

from .lock import <PERSON>acheLock, LockKeys
from app.common import CeleryQueues
from app.models import LendableAssetChangeHistory, db
from app.utils import AmountType, celery_task


class LendableAmountProcessor(object):

    def __init__(self,
                 asset: str,
                 amount: AmountType,
                 business_type: LendableAssetChangeHistory.BusinessType):
        self.asset = asset
        self.amount = amount
        self.business_type = business_type

    def process_new_record(self):
        record = LendableAssetChangeHistory.add_new_record(
            asset=self.asset,
            amount=self.amount,
            business_type=self.business_type
        )
        process_lendable_amount_history_with_record.delay(record.id, self.asset)


def process_lendable_amount_history(asset: str, record_id: int):
    from app.assets import get_asset_config
    db.session.rollback()
    record = LendableAssetChangeHistory.query.filter(
        LendableAssetChangeHistory.id == record_id,
        LendableAssetChangeHistory.status == \
        LendableAssetChangeHistory.StatusType.CREATED
    ).first()
    if not record:
        current_app.logger.error(
            f"LendableAssetChangeHistory id {record_id} not found or "
            f"status not created"
        )
        return
    amount = record.amount
    if record.business_type in (
            LendableAssetChangeHistory.BusinessType.MARGIN_LOAN,
            LendableAssetChangeHistory.BusinessType.CREDIT_LOAN,
            LendableAssetChangeHistory.BusinessType.PLEDGE_LOAN,
    ):
        change_amount = - abs(Decimal(amount))
    else:
        change_amount = abs(Decimal(amount))
    config = get_asset_config(asset)
    if not config:
        current_app.logger.error(
            "{asset} config not found"
        )
        return
    config.lendable_amount = config.lendable_amount + change_amount
    record.status = LendableAssetChangeHistory.StatusType.FINISHED
    db.session.commit()


@celery_task(queue=CeleryQueues.INVESTMENT)
def process_lendable_amount_history_with_record(record_id: int, asset: str):
    with CacheLock(LockKeys.process_lendable_amount(asset), wait=False):
        process_lendable_amount_history(asset, record_id)

