# -*- coding: utf-8 -*-

import base64
import requests
import datetime
import hmac
import hashlib
import json
from decimal import Decimal
from typing import Dict, List, NamedTuple
from urllib.parse import urlparse

from dateutil.tz import UTC
from flask import current_app
from celery.schedules import crontab

from app.config import config
from app.exceptions import InvalidArgument
from app.utils import BaseHTTPClient
from app.utils.net import url_join
from app.utils.parser import JsonEncoder
from app.models import (
    db,
    UserRiskScreen,
    UserRiskScreenRequest,
    RefinitivGroup,
    UserRiskScreenCase,
    UserRiskScreenCaseResult,
    RefinitivProfileInfo,
)
from app.business import lock_call
from app.common import CeleryQueues
from app.business.risk_screen.base import BaseScreenClient, RiskScreenBusiness
from app.api.common.decorators import limit_frequency
from app.utils import scheduled, now, celery_task, today, group_by
from app.utils.date_ import parse_datetime


class CaseIdExistsError(Exception):
    pass


class RefinitivAPI(BaseHTTPClient):
    """ 路孚特风险筛查-API """

    gateway_path = "/v2"
    content_type = "application/json"
    algorithm = "hmac-sha256"
    options = {
        "timeout": 60,
    }
    frequency_limit = 15

    def __init__(self):
        conf: Dict = config["REFINITIV_WC1_CONFIG"]
        url = conf.get("url") or ""  # https://api-worldcheck.refinitiv.com
        super().__init__(url)
        self.host = url
        self.api_key = conf.get("api_key")
        self.api_secret = conf.get("api_secret")

        parsed_info = urlparse(url)
        self.gateway_host = parsed_info.netloc  # api-worldcheck.refinitiv.com

    def gen_signature(self, data_to_sign: str) -> str:
        hmac_obj = hmac.new(self.api_secret.encode("utf-8"), msg=data_to_sign.encode("utf-8"), digestmod=hashlib.sha256)
        signature = base64.b64encode(hmac_obj.digest()).decode()
        return signature

    def _request(
        self,
        request_method: str,
        api_path: str,
        url_params: Dict = None,
        body_payload: Dict = None,
    ) -> requests.Response:

        # 建议每秒不超过10次请求
        deco = limit_frequency(count=self.frequency_limit, interval=1, func_name="refinitiv_req")
        deco(lambda: True)()

        # GET请求path参数
        params = url_params or {}
        # POST、PUT等请求的body
        payload = body_payload or {}
        payload = json.dumps(payload)

        # 生成signature
        date_str = datetime.datetime.now(UTC).strftime("%a, %d %b %Y %H:%M:%S GMT")
        full_path = url_join(self.gateway_path, api_path)  # /v2/xxx
        data_to_sign_list = [
            f"(request-target): {request_method.lower()} {full_path}",
            f"host: {self.gateway_host}",
            f"date: {date_str}",
            f"content-type: {self.content_type}",
            f"content-length: {len(payload)}",
            f"{payload}",
        ]
        data_to_sign = "\n".join(data_to_sign_list)
        signature = self.gen_signature(data_to_sign)

        # 构建auth头
        auth_header_str = " ".join(["(request-target)", "host", "date", "content-type", "content-length"])
        authorization = (
            f'Signature keyId="{self.api_key}",algorithm="{self.algorithm}",headers="{auth_header_str}",signature="{signature}"'
        )

        headers = {
            "Date": date_str,
            "Authorization": authorization,
            "Content-Type": self.content_type,
            "Content-Length": str(len(payload)),
        }
        full_url = url_join(self.host, full_path, **params)
        response = requests.request(request_method.upper(), full_url, headers=headers, data=payload, **self.options)
        return response

    # ========================================== BASE APIS ==========================================

    def get_group_info(self) -> List:
        # 获取group信息
        resp = self._request(
            request_method="GET",
            api_path="/groups",
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_group_case_template(self, group_id: str) -> Dict:
        # 获取group下的case模版
        resp = self._request(
            request_method="GET",
            api_path=f"/groups/{group_id}/caseTemplate",
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_resolution_toolkits(self, group_id: str) -> Dict:
        # 获取解析工具列表
        resp = self._request(
            request_method="GET",
            api_path=f"/groups/{group_id}/resolutionToolkits",
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_iso_country_map(self) -> Dict:
        # 获取可用的iso国家map
        resp = self._request(
            request_method="GET",
            api_path="/reference/countries",
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_iso_nationality_map(self) -> Dict:
        # 获取可用的iso国籍map
        resp = self._request(
            request_method="GET",
            api_path="/reference/nationalities",
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def check_case_id_available(self, case_id: str) -> bool:
        # 判断case_id是否可用
        resp = self._request(
            request_method="HEAD",
            api_path="/caseIdentifiers",
            url_params={"caseId": case_id},
        )
        status_code = resp.status_code
        if status_code == 404:
            return True
        elif status_code == 200:
            return False
        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_case_system_id(self, case_id: str) -> Dict:
        # 根据case_id获取case_system_id
        resp = self._request(
            request_method="GET",
            api_path="/caseReferences",
            url_params={"caseId": case_id},
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def create_new_case(self, case_info: Dict) -> Dict:
        # 创建一个新的case
        resp = self._request(
            request_method="POST",
            api_path="/cases",
            body_payload=case_info,
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_case_info(self, case_system_id: str) -> Dict:
        # 获取case信息
        resp = self._request(
            request_method="GET",
            api_path=f"/cases/{case_system_id}",
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def update_case_info(self, case_system_id: str, to_update_case_info: Dict) -> Dict:
        # 更新case信息
        resp = self._request(
            request_method="PATCH",
            api_path=f"/cases/{case_system_id}",
            url_params={"screen": "NONE"},
            body_payload=to_update_case_info,
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def update_case_rating(self, case_system_id: str, rating: str, reason: str):
        # 更新case评级
        rating_info = {
            "caseRating": rating,
            "reason": reason,
        }
        resp = self._request(
            request_method="PUT",
            api_path=f"/cases/{case_system_id}/rating",
            body_payload=rating_info,
        )
        status_code = resp.status_code
        if status_code == 204:
            return

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def sync_save_and_screen_new_case(self, new_case_info: Dict) -> Dict:
        # 执行同步筛查：创建和筛查一个新case，并返回筛查结果
        resp = self._request(
            request_method="POST",
            api_path="/cases/screeningRequest",
            body_payload=new_case_info,
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()
        if status_code == 400:
            errors = resp.json()
            if errors and errors[0].get("error") == "CASE_ID_EXISTS":
                raise CaseIdExistsError

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def async_screen_by_case_system_id(self, case_system_id: str):
        # 异步筛查
        resp = self._request(
            request_method="POST",
            api_path=f"/cases/{case_system_id}/screeningRequest",
        )
        status_code = resp.status_code
        if status_code == 201:
            return

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_multi_cases_screening_status(self, cases: List[Dict]) -> List[Dict]:
        # 获取多个case的筛查状态, case keys: caseSystemId, dateFrom
        body_payload = {
            "screeningStatusRequests": cases,
        }
        resp = self._request(
            request_method="POST",
            api_path=f"/cases/screeningStatus",
            body_payload=body_payload,
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_case_audit_events(self, case_system_id: str, filters: Dict = None) -> Dict:
        # 获取case的审计事件列表
        resp = self._request(request_method="POST", api_path=f"/cases/{case_system_id}/auditEvents", body_payload=filters)
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_async_screen_result(self, case_system_id: str) -> List[Dict]:
        # 获取异步筛查结果
        resp = self._request(
            request_method="GET",
            api_path=f"/cases/{case_system_id}/results",
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def get_world_check_profile_data(self, world_check_profile_id: str) -> Dict:
        # 获取风险库记录的信息
        resp = self._request(
            request_method="GET",
            api_path=f"/reference/profile/{world_check_profile_id}",
        )
        status_code = resp.status_code
        if status_code == 200:
            return resp.json()

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def resolute_case_results(
        self, case_system_id: str, result_ids: List[str], status_id: str, risk_id: str, reason_id: str, remark: str
    ):
        # 处理有风险的case的result
        body = {
            "statusId": status_id,
            "riskId": risk_id,
            "reasonId": reason_id,
            "resolutionRemark": remark,
            "resultIds": result_ids,
        }
        resp = self._request(
            request_method="PUT",
            api_path=f"/cases/{case_system_id}/results/resolution",
            body_payload=body,
        )
        status_code = resp.status_code
        if status_code == 204:
            return

        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())


class RefinitivScreenClient(BaseScreenClient):
    """ 路孚特风险筛查 """

    name = UserRiskScreenRequest.ThirdParty.Refinitiv.name

    class GroupConfig(NamedTuple):
        group_id: str
        individual_second_fields: List[Dict]
        organisation_second_fields: List[Dict]
        resolution_toolkits: Dict

    def __init__(self, screen_request: UserRiskScreenRequest):
        super().__init__(screen_request)
        self.group_config = self.get_group_config()
        self.group_id = self.group_config.group_id
        self.client = RefinitivAPI()

    @classmethod
    def get_group_row(cls) -> RefinitivGroup:
        row: RefinitivGroup = RefinitivGroup.query.order_by(RefinitivGroup.id.asc()).first()
        return row

    @classmethod
    def get_group_config(cls) -> GroupConfig:
        group_row = cls.get_group_row()
        secondary_fields = json.loads(group_row.secondary_fields)
        return cls.GroupConfig(
            group_id=group_row.group_id,
            individual_second_fields=secondary_fields["individual"],
            organisation_second_fields=secondary_fields["organisation"],
            resolution_toolkits=json.loads(group_row.resolution_toolkits),
        )

    def process_screen_request(self):
        type_ = self.screen_request.type
        if type_ not in [
            UserRiskScreenRequest.Type.INDIVIDUAL,
            UserRiskScreenRequest.Type.COMPANY,
            UserRiskScreenRequest.Type.DIRECTOR,
        ]:
            raise InvalidArgument(f"not support request type: {type_}")

        case = self.update_or_create_local_case()
        if case.status == UserRiskScreenCase.Status.CREATED and not case.case_system_id:
            # 新创建的case
            screen_results = self.sync_save_and_screen_new_case(case)
            self.process_case_screen_result(case, screen_results)
        else:
            # 失败重试、重新筛查
            screen_results = self.fetch_async_screen_result(case)
            self.process_case_screen_result(case, screen_results)

    def parse_to_secondary_field_dict(self, entity_type: str, secondary_field_list: List[Dict]) -> Dict:
        # 参数id ->  参数名
        if entity_type == UserRiskScreenCase.EntityType.INDIVIDUAL.name:
            field_defs = self.group_config.individual_second_fields
        else:
            field_defs = self.group_config.organisation_second_fields

        result = {}
        type_id_value_map = {f["typeId"]: f["value"] for f in secondary_field_list}
        for field_def in field_defs:
            type_id = field_def["type_id"]
            label = field_def["label"]
            if type_id in type_id_value_map:
                result[label] = type_id_value_map[type_id]
        return result

    def parse_to_secondary_field_list(self, entity_type: str, secondary_info: Dict) -> List[Dict]:
        # 参数名 -> 参数id
        from app.caches.risk_screen import RefinitivNationalityCache

        if entity_type == UserRiskScreenCase.EntityType.INDIVIDUAL.name:
            field_defs = self.group_config.individual_second_fields
        else:
            field_defs = self.group_config.organisation_second_fields

        #
        gender_values = ["MALE", "FEMALE", "UNSPECIFIED"]
        support_countries = RefinitivNationalityCache().hkeys()

        def check_gender(val) -> bool:
            values = gender_values
            return val in values

        def check_country(val) -> bool:
            return val in support_countries

        def check_date(val) -> bool:
            try:
                datetime.datetime.strptime(val, "%Y-%m-%d")
                return True
            except Exception as _e:
                current_app.logger.warning(f"refinitiv_secondary_field.check_date failed: {_e!r}")
            return False

        field_check_func_map = {
            "GENDER": check_gender,
            "COUNTRY": check_country,
            "DATE": check_date,
        }
        field_value_key_map = {
            "DATE": "dateTimeValue",
        }

        result_field_list = []
        for field_def in field_defs:
            field_name = field_def["label"]
            if field_name in secondary_info:
                value = secondary_info[field_name]
                if not value:
                    continue
                value_type = field_def["value_type"]
                check_func = field_check_func_map.get(value_type)
                if check_func and check_func(value):
                    value_key = field_value_key_map.get(value_type, "value")
                    result_field_list.append({"typeId": field_def["type_id"], value_key: value})
        return result_field_list

    def update_or_create_local_case(self) -> UserRiskScreenCase:
        track_no = self.screen_request.track_no
        user_id = self.screen_request.user_id
        type_ = self.screen_request.type
        info = json.loads(self.screen_request.info)
        if type_ in [
            UserRiskScreenRequest.Type.INDIVIDUAL,
            UserRiskScreenRequest.Type.DIRECTOR,
        ]:
            entity_type = UserRiskScreenCase.EntityType.INDIVIDUAL.name
            gender = info["gender"]
            date_of_birth = info["date_of_birth"]
        else:
            entity_type = UserRiskScreenCase.EntityType.ORGANISATION.name
            gender = date_of_birth = None

        exist_case: UserRiskScreenCase = UserRiskScreenCase.query.filter(
            UserRiskScreenCase.user_id == user_id,
            UserRiskScreenCase.group_id == self.group_id,
            UserRiskScreenCase.entity_type == entity_type,
            UserRiskScreenCase.case_id == track_no,
        ).first()
        if exist_case:
            # update info
            exist_case.name = info["name"]
            exist_case.country = info["country"]
            exist_case.gender = gender
            exist_case.date_of_birth = date_of_birth
            exist_case.status = UserRiskScreenCase.Status.CREATED
            db.session.commit()
            return exist_case

        case = UserRiskScreenCase(
            request_id=self.screen_request.id,
            user_id=user_id,
            case_id=track_no,
            case_system_id=None,
            group_id=self.group_id,
            entity_type=entity_type,
            name=info["name"],
            country=info["country"],
            gender=gender,
            date_of_birth=date_of_birth,
            status=UserRiskScreenCase.Status.CREATED,
        )
        db.session.add(case)
        db.session.commit()
        return case

    def build_case_secondary_fields(self, local_case: UserRiskScreenCase) -> List[Dict]:
        entity_type = local_case.entity_type
        if entity_type == UserRiskScreenCase.EntityType.INDIVIDUAL.name:
            gender = local_case.gender.name if local_case.gender else None
            dob = None
            if local_case.date_of_birth and local_case.date_of_birth < today():
                # 筛查时不允许出生日期超过今天
                dob = local_case.date_of_birth.strftime("%Y-%m-%d")
            secondary_field_dict = {
                "NATIONALITY": local_case.country,
                "GENDER": gender,
                "DATE_OF_BIRTH": dob,
            }
        else:
            secondary_field_dict = {
                "NATIONALITY": local_case.country,
            }
        return self.parse_to_secondary_field_list(entity_type, secondary_field_dict)

    def sync_save_and_screen_new_case(self, local_case: UserRiskScreenCase) -> List[Dict]:
        # 同步：创建case并执行筛查
        body = {
            "providerTypes": ["WATCHLIST"],
            "caseScreeningState": {"WATCHLIST": "INITIAL"},
            "entityType": local_case.entity_type,
            "groupId": self.group_id,
            "caseId": local_case.case_id,
            "name": local_case.name,
            "secondaryFields": self.build_case_secondary_fields(local_case),
        }
        try:
            response = self.client.sync_save_and_screen_new_case(body)
        except CaseIdExistsError:
            case_system_id = self.client.get_case_system_id(local_case.case_id)["caseSystemId"]
            screen_results = self.client.get_async_screen_result(case_system_id)
        else:
            case_system_id = response["caseSystemId"]
            screen_results = response["results"]
        local_case.case_system_id = case_system_id
        db.session.add(local_case)
        db.session.commit()
        return screen_results

    def fetch_async_screen_result(self, case_row: UserRiskScreenCase) -> List[Dict]:
        # 获取异步筛查结果
        self.sync_case_info(case_row)
        self.client.async_screen_by_case_system_id(case_row.case_system_id)
        screen_results = self.client.get_async_screen_result(case_row.case_system_id)
        return screen_results

    def sync_case_info(self, case_row: UserRiskScreenCase):
        # 同步case信息
        case_system_id = case_row.case_system_id
        case_info = self.client.get_case_info(case_system_id)
        gender = case_row.gender.name if case_row.gender else None
        dob = case_row.date_of_birth.strftime("%Y-%m-%d") if case_row.date_of_birth else None
        secondary_field_dict = self.parse_to_secondary_field_dict(case_row.entity_type, case_info.get("secondaryFields") or [])
        if (
            case_info["name"] != case_row.name
            or (case_row.country and case_row.country != secondary_field_dict.get("NATIONALITY"))
            or (gender and gender != secondary_field_dict.get("GENDER"))
            or (dob and dob != secondary_field_dict.get("DATE_OF_BIRTH"))
        ):
            to_update_case_info = {
                "providerTypes": ["WATCHLIST"],
                "name": case_row.name,
                "secondaryFields": self.build_case_secondary_fields(case_row),
            }
            self.client.update_case_info(case_system_id, to_update_case_info)

    def process_case_screen_result(self, case_row: UserRiskScreenCase, screen_results: List[Dict]):
        # 处理case筛查匹配的结果
        if not screen_results:
            # no match, no risk
            case_row.status = UserRiskScreenCase.Status.PASSED
            RiskScreenBusiness.update_user_risk_status(case_row.user_id, UserRiskScreen.Status.PASSED)
            return

        resolution_fields = self.group_config.resolution_toolkits["resolution_fields"]
        resolution_status_dict: Dict = resolution_fields["status_dict"]
        resolution_risk_dict: Dict = resolution_fields["risk_dict"]
        resolution_reason_dict: Dict = resolution_fields["reason_dict"]
        profile_ids = []
        result_rows = []
        screen_results = sorted(screen_results, key=lambda x: Decimal(str(x["matchScore"])), reverse=True)
        for screen_res in screen_results:
            profile_id = screen_res["referenceId"]
            result_id = screen_res.get("resultId") or ""
            result_row: UserRiskScreenCaseResult = UserRiskScreenCaseResult.get_or_create(
                case_id=case_row.case_id,
                profile_id=profile_id,
                result_id=result_id,
            )
            result_row.match_score = Decimal(screen_res["matchScore"])
            resolution_info = screen_res.get("resolution")
            if resolution_info and (status_id := resolution_info.get("statusId")):
                # Refinitiv已经自动解析了，同步下状态
                if not result_row.match_status and (match_status := resolution_status_dict.get(status_id)):
                    result_row.match_status = match_status
                if not result_row.risk and (risk := resolution_risk_dict.get(resolution_info.get("riskId"))):
                    result_row.risk = risk
                if not result_row.reason and (reason := resolution_reason_dict.get(resolution_info.get("reasonId"))):
                    result_row.reason = reason
                if not result_row.resolution_remark and (remark := resolution_info.get("resolutionRemark")):
                    result_row.resolution_remark = remark
            db.session.add(result_row)
            profile_ids.append(profile_id)
            result_rows.append(result_row)
        case_row.status = UserRiskScreenCase.Status.AUDIT_REQUIRED
        db.session.commit()

        for profile_id_ in profile_ids[:100]:
            try:
                self.save_or_update_profile(profile_id_)
            except Exception as _e:
                current_app.logger.warning(f"save_or_update_profile {profile_id_} failed: {_e!r}")

        # 全部都是不匹配，认为是无风险的
        if all([i.match_status == UserRiskScreenCaseResult.MatchStatus.FALSE.name for i in result_rows]):
            case_row.status = UserRiskScreenCase.Status.PASSED
            RiskScreenBusiness.update_user_risk_status(case_row.user_id, UserRiskScreen.Status.PASSED)
            return

    def save_or_update_profile(self, profile_id: str, force_update: bool = False):
        # 保存风险库-记录的信息
        profile_row = RefinitivProfileInfo.get_or_create(profile_id=profile_id)
        if profile_row.id and not force_update:
            return

        profile_data = self.client.get_world_check_profile_data(profile_id)
        profile_row.detail = json.dumps(profile_data, cls=JsonEncoder)
        modification_date = profile_data.get("modificationDate")
        if modification_date:
            modified_at = datetime.datetime.strptime(modification_date, "%Y-%m-%dT%H:%M:%SZ")
            profile_row.modified_at = modified_at
        db.session.add(profile_row)
        db.session.commit()

    @classmethod
    def case_rescreen_result_is_ready(cls, case: UserRiskScreenCase) -> bool:
        # 重新筛查时 判断新的筛查是否已经完成了
        filters = {
            "query": "actionType==SCREENED_CASE",  # 只查询筛查事件
            "pagination": {"currentPage": 1, "itemsPerPage": 10},  # max itemsPerPage: 250
            "sort": [{"columnName": "eventDate", "order": "DESCENDING"}],
        }
        response = RefinitivAPI().get_case_audit_events(case.case_system_id, filters)
        events = response.get("results") or []
        if not events:
            return False

        latest_event = events[0]
        event_dt = parse_datetime(latest_event["eventDate"]).replace(tzinfo=UTC)
        return event_dt >= case.updated_at

    def sync_invalid_profiles(self, case: UserRiskScreenCase) -> int:
        screen_result_rows = UserRiskScreenCaseResult.query.filter(
            UserRiskScreenCaseResult.case_id == case.case_id,
        ).order_by(
            UserRiskScreenCaseResult.match_score.desc()
        ).with_entities(
            UserRiskScreenCaseResult.profile_id
        ).all()
        profile_ids = [r.profile_id for r in screen_result_rows]
        profile_info_model = RefinitivProfileInfo
        profile_rows = profile_info_model.query.filter(
            profile_info_model.profile_id.in_(profile_ids),
        ).all()
        valid_profile_ids = {r.profile_id for r in profile_rows if r.detail and json.loads(r.detail)}
        no_sync_profile_ids = set(profile_ids) - valid_profile_ids
        if no_sync_profile_ids:
            for profile_id_ in profile_ids:
                try:
                    self.save_or_update_profile(profile_id_, force_update=True)
                except Exception as _e:
                    current_app.logger.warning(f"batch_save_or_update_profiles {profile_id_} failed: {_e!r}")

        return len(no_sync_profile_ids)


@celery_task(queue=CeleryQueues.REAL_TIME)
def sync_refinitiv_case_result_resolution_task(result_row_ids: List[str]):
    """ 同步case结果的解析数据，result_row_ids只能属于一个case。result_row_ids的解析数据需要一样 """
    result_rows = UserRiskScreenCaseResult.query.filter(
        UserRiskScreenCaseResult.id.in_(result_row_ids),
    ).all()

    case_ids = {i.case_id for i in result_rows}
    assert len(case_ids) == 1
    case_id = list(case_ids)[0]
    case: UserRiskScreenCase = UserRiskScreenCase.query.filter(UserRiskScreenCase.case_id == case_id).first()
    if case.third_party != UserRiskScreenRequest.ThirdParty.Refinitiv:
        raise Exception('must be refinitiv case')

    kits = RefinitivScreenClient.get_group_config().resolution_toolkits
    fields_ = kits["resolution_fields"]
    # {label: id}, eg: {'POSITIVE': '5jb6w1hxishw1g8t0tlouplxc'}
    resolution_status_dict: Dict = {v: k for k, v in fields_["status_dict"].items()}
    resolution_risk_dict: Dict = {v: k for k, v in fields_["risk_dict"].items()}
    resolution_reason_dict: Dict = {v: k for k, v in fields_["reason_dict"].items()}

    first_row: UserRiskScreenCaseResult = result_rows[0]
    result_ids = [i.result_id for i in result_rows]
    status_id = resolution_status_dict.get(first_row.match_status)
    risk_id = resolution_risk_dict.get(first_row.risk)
    reason_id = resolution_reason_dict.get(first_row.reason)
    remark = first_row.resolution_remark
    if not all([status_id, risk_id, reason_id]):
        current_app.logger.warning(f"refinitiv.resolute_risk_case resolution kits not match")
        return

    client = RefinitivAPI()
    try:
        client.resolute_case_results(case.case_system_id, result_ids, status_id, risk_id, reason_id, remark)
    except Exception as _e:
        current_app.logger.warning(f"refinitiv.resolute_risk_case failed: {_e!r}")
    else:
        for result_row in result_rows:
            result_row.resolution_sync_status = UserRiskScreenCaseResult.SyncStatus.SYNCED
        db.session.commit()


@scheduled(crontab(hour="*/1", minute="7"), queue=CeleryQueues.REAL_TIME)
@lock_call()
def sync_refinitiv_case_result_resolution_schedule():
    before_at = now() - datetime.timedelta(minutes=20)
    min_at = now() - datetime.timedelta(hours=10)
    result_rows = UserRiskScreenCaseResult.query.filter(
        UserRiskScreenCaseResult.resolver_id.isnot(None),
        UserRiskScreenCaseResult.resolution_sync_status == UserRiskScreenCaseResult.SyncStatus.NOT_SYNCED,
        UserRiskScreenCaseResult.resolved_at <= before_at,
        UserRiskScreenCaseResult.resolved_at >= min_at,
    ).all()
    case_ids = {row.case_id for row in result_rows}
    filter_case_ids = {
        row.case_id
        for row in UserRiskScreenCase.query.filter(
            UserRiskScreenCase.third_party == UserRiskScreenRequest.ThirdParty.Refinitiv,
            UserRiskScreenCase.case_id.in_(case_ids)
        ).with_entities(UserRiskScreenCase.case_id).all()
    }
    for case_id, rows_ in group_by(lambda x: x.case_id, result_rows).items():
        if case_id in filter_case_ids:
            sync_refinitiv_case_result_resolution_task.delay([r.id for r in rows_])
