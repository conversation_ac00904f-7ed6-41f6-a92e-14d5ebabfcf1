# -*- coding: utf-8 -*-

from decimal import Decimal
from math import ceil
from typing import Dict, Iterable, Union


from ..common import Currency, FIXED_ASSET_PRICES
from ..caches import AssetUSDPricesCache, FiatUSDPricesCache
from ..utils import quantize_amount, AmountType


class PriceManager:
    _DEFAULT_PRECISION = 12

    @classmethod
    def list_asset(cls) -> list:
        return AssetUSDPricesCache().hkeys()

    @classmethod
    def asset_to_usd(cls, asset: str) -> Decimal:
        if (fixed := FIXED_ASSET_PRICES.get(asset)) is not None:
            return fixed
        return cls._quantize(AssetUSDPricesCache().get_price(asset))

    @classmethod
    def assets_to_usd(cls, assets: Iterable[str] = None) -> Dict[str, Decimal]:
        prices = AssetUSDPricesCache().get_prices(assets)
        quantize = cls._quantize
        return {asset: quantize(price)
                for asset, price in prices.items()}

    @classmethod
    def fiat_to_usd(cls, currency: Union[str, Currency]) -> Decimal:
        if (currency := cls._validate_currency(currency)) is Currency.USD:
            return Decimal(1)
        return cls._quantize(FiatUSDPricesCache().get_price(currency.name))

    @classmethod
    def fiats_to_usd(cls, currencies: Iterable[Union[str, Currency]]
                     ) -> Dict[Currency, Decimal]:
        quantize = cls._quantize
        prices = FiatUSDPricesCache().get_prices(
            c.name for c in map(cls._validate_currency, currencies))
        prices[Currency.USD.name] = Decimal(1)
        return {currency: quantize(price)
                for currency, price in prices.items()}

    @classmethod
    def asset_to_asset(cls, src_asset: str, trg_asset: str) -> Decimal:
        if src_asset == trg_asset:
            return Decimal(1)
        if (trg_to_usd := cls.asset_to_usd(trg_asset)) <= 0:
            return Decimal()
        return cls._quantize(cls.asset_to_usd(src_asset) / trg_to_usd)

    @classmethod
    def assets_to_asset(cls, src_assets: Iterable[str], trg_asset: str
                        ) -> Dict[str, Decimal]:
        prices_cache = AssetUSDPricesCache()

        if (trg_price := prices_cache.get_price(trg_asset)) <= 0:
            prices = dict.fromkeys(src_assets, Decimal())
            prices[trg_asset] = Decimal(1)
            return prices

        src_prices = prices_cache.get_prices(src_assets)
        quantize = cls._quantize
        return {asset: quantize(price / trg_price)
                for asset, price in src_prices.items()}

    @classmethod
    def asset_to_fiat(cls, asset: str, fiat: Union[str, Currency]
                      ) -> Decimal:
        if (fiat_to_usd := cls.fiat_to_usd(fiat)) <= 0:
            return Decimal()
        return cls._quantize(cls.asset_to_usd(asset) / fiat_to_usd)

    @classmethod
    def assets_to_fiat(cls, assets: Iterable[str], fiat: Union[str, Currency]
                       ) -> Dict[str, Decimal]:
        if (fiat_to_usd := cls.fiat_to_usd(fiat)) <= 0:
            return dict.fromkeys(assets, Decimal())

        src_prices = AssetUSDPricesCache().get_prices(assets)
        quantize = cls._quantize
        return {asset: quantize(price / fiat_to_usd)
                for asset, price in src_prices.items()}

    @classmethod
    def convert_price(cls, src: Union[str, Currency],
                      src_price: AmountType,
                      trg: Union[str, Currency]) -> Decimal:
        src_price = Decimal(src_price)

        if src == trg:
            return src_price

        def to_usd(_a):
            return (cls.fiat_to_usd(_a)
                    if (isinstance(_a, Currency)
                        or isinstance(getattr(Currency, _a, None), Currency))
                    else cls.asset_to_usd(_a))

        try:
            return cls._quantize(src_price * to_usd(src) / to_usd(trg))
        except ZeroDivisionError:
            return Decimal()

    @classmethod
    def _validate_currency(cls, currency: Union[str, Currency]) -> Currency:
        if isinstance(currency, Currency):
            return currency
        currency = getattr(Currency, currency.upper(), None)
        if not isinstance(currency, Currency):
            raise ValueError(f'currency {currency!r} does not exist')
        return currency

    @classmethod
    def _quantize(cls, price: Decimal) -> Decimal:
        return quantize_amount(price, cls._DEFAULT_PRECISION)

    @classmethod
    def asset_to_min_order_amount(cls, asset: str) -> Decimal:
        min_usd = Decimal(5) if asset == 'BTC' else Decimal(1)
        if (asset_price := PriceManager.asset_to_usd(asset)) <= 0:
            return Decimal()
        return cls.price_to_min_order_amount(asset_price, min_usd)

    @classmethod
    def perprtual_asset_to_min_order_amount(cls, asset: str, min_usd: Decimal) -> Decimal:
        if (asset_price := PriceManager.asset_to_usd(asset)) <= 0:
            return Decimal()
        return cls.price_to_min_order_amount(asset_price, min_usd)

    @classmethod
    def price_to_min_order_amount(cls,
                                  price_usd: Decimal,
                                  min_usd: Decimal = Decimal(1)):
        min_asset = min_usd / price_usd
        # 1, 5, 10, 50, 100, ...
        min_amount = Decimal(10) ** int(ceil(min_asset.log10()))
        if min_asset <= (half_least_amount := min_amount / 2):
            min_amount = half_least_amount

        return min_amount
