# -*- coding: utf-8 -*-
import datetime
import json
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from typing import Optional, Type

from flask import current_app
from celery.schedules import crontab
from pyroaring import BitMap
from sqlalchemy import func

from app.models.referral import ReferralAssetDetail

from .. import UserPreferences
from ..summary import (
    get_period_trade_amount_mapping, get_period_trade_fee_mapping,
    check_data_ready, get_period_increase_trade_users,
)
from ...caches.admin import UserFirstNormalReferTimeCache
from ...common import CeleryQueues, language_cn_names, get_country, ReportType
from ...exceptions import InvalidArgument
from ...models import (AssetPrice, DailyReferReport, DailyUserReport,
                       ReferralAssetHistory, ReferralHistory, db, DailyAmbassadorReferralReport,
                       AmbassadorAgent, Ambassador, DailyReferTypeReport, LiquiditySlice,
                       UserTradeFeeSummary, UserBusinessRecord, DailyNormalReferReport,
                       DailySpotTradeReport, DailyPerpetualTradeReport, MonthlyNormalReferReport,
                       DailyUserNormalReferStatistic, DailyNormalReferStatisticAggr, User, VipUser,
                       BusinessAgentReferralAssetHistory)
from ...models.broker import BrokerReferralAssetHistory, BrokerReferralAssetDetail
from ...schedules.reports.refer_report import get_market_maker_ids
from ...utils import quantize_amount, scheduled, batch_iter, amount_to_str
from ...utils.date_ import date_to_datetime, str_to_datetime, datetime_to_str, next_month
from ..external_dbs import PerpetualSummaryDB, TradeSummaryDB
from ..lock import lock_call
from ..referral import ReferralBusiness
from .base import BaseReporter, BaseMonthlyReporter


def modify_end_date_kwarg(kwargs):
    """查询月报表时应该将end_date+1month-1day用以查询daily记录"""
    if 'start_date' not in kwargs or 'end_date' not in kwargs:
        return
    end_date = kwargs['end_date']
    kwargs['end_date'] = next_month(end_date.year, end_date.month) - timedelta(days=1)


def get_refer_page_data(kwargs, query, day_model, filter_fun):
    total_data = {
        "refer_count": "--",
        "spot_trade_usd": "--",
        "perpetual_trade_usd": "--",
        "spot_fee_usd": "--",
        "perpetual_fee_usd": "--",
        "refer_total_amount": "--",
        "deal_count": "--",
        "deposit_count": "--",
        "refer_total_usd": "--"
    }
    page, limit = kwargs["page"], kwargs["limit"]
    keys = kwargs.keys() - {"report_type", "page", "limit", "export"}
    if keys and all([bool(kwargs.get(i)) for i in keys]):
        rows = query.all()
        for field in total_data.keys():
            if field not in ["deal_count", "deposit_count"]:
                total_data[field] = amount_to_str(sum([getattr(row, field, 0) for row in rows]), 2)
        deal_count, deposit_count = set(), set()
        if kwargs["report_type"] == ReportType.DAILY:
            for row in rows:
                deal_count.update(json.loads(row.deal_user_list))
                deposit_count.update(json.loads(row.deposit_user_list))
        else:
            modify_end_date_kwarg(kwargs)
            daily_query = filter_fun(day_model, kwargs)
            for row in daily_query.all():
                deal_count.update(json.loads(row.deal_user_list))
                deposit_count.update(json.loads(row.deposit_user_list))
        total_data["deal_count"] = len(deal_count)
        total_data["deposit_count"] = len(deposit_count)
        count = len(rows)
        page_rows = rows[(page - 1) * limit: page * limit]
    else:
        page_rows = query.paginate(page, limit, error_out=False).items
        count = query.count()
    return count, total_data, page_rows


def get_user_amount_and_count(user_ids, start_date):
    user_amount = Decimal()
    user_count = 0
    for chunk_ids in batch_iter(user_ids, 1000):
        end_date = start_date + datetime.timedelta(days=1)
        mapping = get_period_trade_amount_mapping(start_date, end_date, user_ids=chunk_ids)
        user_amount += sum(mapping.values())
        user_count += len(mapping.keys())
    return user_count, user_amount


def get_deal_count_amount_by_ids(user_ids, start_date):
    deal_count, deal_amount = 0, Decimal()
    for chunk_ids in batch_iter(user_ids, 1000):
        end_date = start_date + datetime.timedelta(days=1)
        trade_mapping = get_period_trade_amount_mapping(start_date, end_date, chunk_ids)
        deal_count += len(trade_mapping.keys())
        deal_amount += sum(trade_mapping.values())
    return deal_count, deal_amount


def get_ambassador_id_set(end_date):
    return set(i.user_id for i in Ambassador.query.filter(
        Ambassador.status == Ambassador.Status.VALID,
        Ambassador.effected_at < end_date,
    ).with_entities(
        Ambassador.user_id
    ))


def get_day_fee_by_user_ids(user_ids, start_date):
    invitee_fee_amount = Decimal()
    for chunk_ids in batch_iter(user_ids, 1000):
        chunk_fee = ReferralAssetDetail.query.filter(
                ReferralAssetDetail.referree_id.in_(chunk_ids),
                ReferralAssetDetail.date == start_date,
            ).with_entities(
                func.sum(ReferralAssetDetail.spot_fee_usd + ReferralAssetDetail.perpetual_fee_usd)
            ).scalar() or 0
        invitee_fee_amount += chunk_fee
    return invitee_fee_amount


def get_day_trade_user_set(start_date):
    query = UserBusinessRecord.query.filter(
        UserBusinessRecord.report_at == start_date,
        UserBusinessRecord.business != UserBusinessRecord.Business.DEPOSIT
    ).with_entities(
        UserBusinessRecord.new_user_bit_map
    ).all()
    trade_user_set = set()
    for bitmap, in query:
        trade_user_set |= set(BitMap.deserialize(bitmap))
    return trade_user_set


def get_normal_referrer_id_set(refer_data, ambassador_id_set):
    # 邀请人
    referrer_id_set = set(i.referrer_id for i in refer_data)
    # 排除大使
    normal_referrer_ids = referrer_id_set - ambassador_id_set
    return normal_referrer_ids


def get_site_trade_and_fee_usd(model, st):
    site_data = model.query.filter(
        model.report_date == st,
    ).with_entities(
        model.trade_usd,
        model.fee_usd,
    ).first()
    if site_data:
        return site_data.trade_usd, site_data.fee_usd
    else:
        current_app.logger.warning(f"{st} 引用 DailySpotTradeReport 数据未就绪")
        return 0, 0


class DailyReferReporter(BaseReporter):
    model = DailyReferReport

    def get_first_date(self) -> Optional[date]:
        return None

    def get_fee_map(self, start_date):
        details = ReferralAssetDetail.query.filter(
            ReferralAssetDetail.date == start_date,
            ReferralAssetDetail.user_id != ReferralAssetDetail.referree_id
        ).group_by(
            ReferralAssetDetail.referree_id
        ).with_entities(
            ReferralAssetDetail.referree_id,
            func.sum(ReferralAssetDetail.spot_fee_usd + ReferralAssetDetail.perpetual_fee_usd)
        ).all()

        return dict(details)

    def run(self, start_date: date, end_date: date):
        # 需要在UserTradeSummary更新以及返佣发放之后
        # 被邀请人
        referee_user_ids = {v.referree_id for v in
                            ReferralHistory.query.filter(
                                ReferralHistory.created_at < date_to_datetime(end_date),
                                ReferralHistory.status == ReferralHistory.Status.VALID,
                            ).with_entities(ReferralHistory.referree_id)}
        if not TradeSummaryDB.is_data_completed(start_date) or \
                not PerpetualSummaryDB.is_data_completed(start_date):
            raise InvalidArgument(message='refer task not processed')

        if not ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date >= start_date,
            ReferralAssetHistory.date < end_date
        ).first():
            raise InvalidArgument(message='referral_asset_history is empty')

        asset_rate = defaultdict(Decimal)
        asset_rate.update(AssetPrice.get_close_price_map(start_date))
        referee_fee_map = self.get_fee_map(start_date)
        invitee_all_fee = quantize_amount(
            sum([usd for user_id, usd in referee_fee_map.items()
                 if user_id in referee_user_ids]), 2)

        trade_data = get_period_trade_amount_mapping(start_date, end_date, user_ids=referee_user_ids)
        invitee_trade_usd = sum(trade_data.values())
        invitee_count = len(trade_data.keys())
        daily_increase_trade_users = get_period_increase_trade_users(start_date - timedelta(days=1), start_date)
        new_trade_invitee_users = daily_increase_trade_users & set(trade_data)
        new_invitee_count = len(new_trade_invitee_users)

        bus_agent_amount = Decimal()  # 商务代理返佣
        for item in BusinessAgentReferralAssetHistory.query.filter(
                BusinessAgentReferralAssetHistory.date == start_date,
                BusinessAgentReferralAssetHistory.status == BusinessAgentReferralAssetHistory.Status.FINISHED,
        ).with_entities(
            BusinessAgentReferralAssetHistory.amount,
        ).all():
            bus_agent_amount += item.amount

        ref_amounts = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date >= start_date,
            ReferralAssetHistory.date < end_date
        ).group_by(
            ReferralAssetHistory.type
        ).with_entities(
            ReferralAssetHistory.type,
            func.sum(ReferralAssetHistory.amount)
        )
        ref_amounts = dict(ref_amounts)

        refer_amount = ref_amounts.get(ReferralAssetHistory.Type.REFERRAL, 0)
        ambassador_amount = ref_amounts.get(ReferralAssetHistory.Type.AMBASSADOR, 0)
        ambassador_agent_amount = ref_amounts.get(ReferralAssetHistory.Type.AMBASSADOR_AGENT, 0)

        ambassador_amount += bus_agent_amount

        inviter_count = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date >= start_date,
            ReferralAssetHistory.date < end_date
        ).with_entities(
            func.count(ReferralAssetHistory.user_id.distinct())
        ).scalar() or 0
        market_makers = get_market_maker_ids()
        referrer_count = ReferralHistory.query.filter(
            ReferralHistory.created_at < end_date,
            ReferralHistory.created_at >= start_date,
            ReferralHistory.referree_id.notin_(market_makers),
            ReferralHistory.status == ReferralHistory.Status.VALID,
        ).with_entities(
            func.count(ReferralHistory.referree_id.distinct().label('referree_id'))
        ).scalar() or 0

        broker_amount = BrokerReferralAssetHistory.query.filter(
            BrokerReferralAssetHistory.date >= start_date,
            BrokerReferralAssetHistory.date < end_date
        ).with_entities(
            func.sum(BrokerReferralAssetHistory.amount)
        ).scalar() or 0

        refer_usd = Decimal()
        asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.REFERRAL]
        normal_refer_usd = asset_rate[asset] * refer_amount
        refer_usd += asset_rate[asset] * refer_amount

        asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR]
        refer_usd += asset_rate[asset] * ambassador_amount

        asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR_AGENT]
        refer_usd += asset_rate[asset] * ambassador_agent_amount

        asset = BrokerReferralAssetDetail.REFERRAL_ASSET
        refer_usd += asset_rate[asset] * broker_amount

        refer_usd = quantize_amount(refer_usd, 2)

        refer_rate = quantize_amount(refer_usd / invitee_all_fee, 4) \
            if invitee_all_fee > Decimal() else Decimal()

        trade_fees = get_period_trade_fee_mapping(start_date, end_date)
        all_trade_fee_usd = quantize_amount(sum(trade_fees.values()), 2)
        amm_liq_slice_rows = LiquiditySlice.query.filter(
            LiquiditySlice.date == start_date,
        ).with_entities(
            LiquiditySlice.fee_usd,
        ).all()
        amm_refund_fee_usd = sum([i.fee_usd for i in amm_liq_slice_rows])
        total_fee_usd = max(all_trade_fee_usd - amm_refund_fee_usd, 0)
        # 总返佣比例 = 返佣总金额 / (全站贡献手续费-全站AMM分红支出)
        total_refer_rate = quantize_amount(refer_usd / total_fee_usd, 4) if total_fee_usd else Decimal()
        user_report = DailyUserReport.query.filter(
            DailyUserReport.report_date == start_date
        ).first()
        if not user_report or user_report.active_trade_user == 0:
            invitee_trade_percent = Decimal()
        else:
            invitee_trade_percent = quantize_amount(invitee_count / user_report.active_trade_user, 4)
        if not user_report or user_report.increase_user == 0:
            invitee_percent = Decimal()
        else:
            invitee_percent = quantize_amount(referrer_count / user_report.increase_user, 4)
        if not user_report or user_report.increase_trade_user == 0:
            new_trade_percent = Decimal()
        else:
            new_trade_percent = quantize_amount(new_invitee_count / user_report.increase_trade_user, 4)

        total_invitee = ReferralHistory.query.filter(
            ReferralHistory.created_at < end_date,
            ReferralHistory.referree_id.notin_(market_makers),
        ).with_entities(
            func.count(ReferralHistory.referree_id.distinct().label('referree_id'))
        ).scalar() or 0

        record = self.model(
            report_date=start_date,
            total_invitee=total_invitee,
            invitee_count=invitee_count,
            new_invitee_count=new_invitee_count,
            new_trade_percent=new_trade_percent,
            invitee_trade_percent=invitee_trade_percent,
            invitee_trade_usd=invitee_trade_usd,
            invitee_all_fee=invitee_all_fee,
            inviter_count=inviter_count,
            referrer_count=referrer_count,
            invitee_percent=invitee_percent,
            refer_amount=refer_amount,
            normal_refer_usd=normal_refer_usd,
            broker_amount=broker_amount,
            ambassador_amount=ambassador_amount,
            ambassador_agent_amount=ambassador_agent_amount,
            refer_usd=refer_usd,
            refer_rate=refer_rate,
            all_trade_fee_usd=all_trade_fee_usd,
            total_refer_rate=total_refer_rate
        )
        db.session_add_and_commit(record)


class DailyAmbassadorReferralReporter(BaseReporter):
    model = DailyAmbassadorReferralReport

    def get_first_date(self) -> Optional[date]:
        return None

    def get_fee_map(self, start_date):
        details = ReferralAssetDetail.query.filter(
            ReferralAssetDetail.date == start_date,
            ReferralAssetDetail.user_id != ReferralAssetDetail.referree_id
        ).group_by(
            ReferralAssetDetail.referree_id
        ).with_entities(
            ReferralAssetDetail.referree_id,
            func.sum(ReferralAssetDetail.spot_fee_usd + ReferralAssetDetail.perpetual_fee_usd)
        ).all()
        return dict(details)

    def run(self, start_date: date, end_date: date):
        # 依赖DailyUserReport, DailyReferTypeReport, UserTradeFeeSummary
        # 另依赖 UserBusinessRecord
        if not check_data_ready(start_date):
            current_app.logger.warning("{} DailyAmbassadorReferralReporter-UserTradeSummary 数据未就绪".format(start_date))
            return

        user_report: DailyUserReport = DailyUserReport.query.filter(
            DailyUserReport.report_date == start_date).first()
        refer_type_report: DailyReferTypeReport = DailyReferTypeReport.query.filter(
            DailyReferTypeReport.report_date == start_date,
            DailyReferTypeReport.type == DailyReferTypeReport.Type.REFERRAL
        ).first()
        fee_summary = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.report_date == start_date
        ).first()

        if not user_report or not refer_type_report or not fee_summary:
            return

        # 周期内大使邀请新增的交易用户/周期内全站新增的交易用户
        def get_history_trade_users(business, date_):
            row = UserBusinessRecord.query.with_entities(
                UserBusinessRecord.history_user_bit_map
            ).filter(
                UserBusinessRecord.business == business,
                UserBusinessRecord.report_at == date_
            ).first()
            return set(BitMap.deserialize(row.history_user_bit_map)) if row else None

        spot_trade_0 = get_history_trade_users(UserBusinessRecord.Business.SPOT_TRADE, start_date)
        spot_trade_1 = get_history_trade_users(
            UserBusinessRecord.Business.SPOT_TRADE,
            start_date - datetime.timedelta(days=1)
        )
        perp_trade_0 = get_history_trade_users(UserBusinessRecord.Business.PERPETUAL_TRADE, start_date)
        perp_trade_1 = get_history_trade_users(
            UserBusinessRecord.Business.PERPETUAL_TRADE,
            start_date - datetime.timedelta(days=1)
        )
        if spot_trade_0 is None or spot_trade_1 is None:
            return
        if perp_trade_0 is None or perp_trade_1 is None:
            return

        # 大使/大使代理人数(历史总和)
        ambassador_agent_count = AmbassadorAgent.query.filter(
            AmbassadorAgent.status == AmbassadorAgent.Status.VALID,
            AmbassadorAgent.created_at < end_date,
        ).with_entities(func.count()).scalar() or 0
        agent_rows = AmbassadorAgent.query.with_entities(
            AmbassadorAgent.user_id
        ).filter(
            AmbassadorAgent.status == AmbassadorAgent.Status.VALID,
            AmbassadorAgent.created_at < end_date,
        ).all()
        agent_user_ids = {row.user_id for row in agent_rows}
        ambassador_count = Ambassador.query.filter(
            Ambassador.status == Ambassador.Status.VALID,
            Ambassador.effected_at < end_date,
        ).with_entities(func.count()).scalar() or 0

        # 新增大使/大使代理人数
        new_ambassador_agent_count = AmbassadorAgent.query.filter(
            AmbassadorAgent.created_at >= start_date,
            AmbassadorAgent.created_at < end_date,
            AmbassadorAgent.status == AmbassadorAgent.Status.VALID
        ).with_entities(func.count()).scalar() or 0
        new_ambassador_count = Ambassador.query.filter(
            Ambassador.effected_at >= start_date,
            Ambassador.effected_at < end_date,
            Ambassador.status == Ambassador.Status.VALID
        ).with_entities(func.count()).scalar() or 0

        # 大使
        ambassador_ids = Ambassador.query.filter(
            Ambassador.status == Ambassador.Status.VALID,
            Ambassador.effected_at < end_date,
        ).with_entities(
            Ambassador.user_id
        ).all()
        ambassador_ids = [item.user_id for item in ambassador_ids]
        only_agent_count = len(agent_user_ids - set(ambassador_ids))
        history_referrees = []
        history_referrees_1 = []
        new_referree_count = 0
        referree_total_count = 0
        market_makers = get_market_maker_ids()
        for chunk_amb_ids in batch_iter(ambassador_ids, 1000):
            chunk_history_referrees = ReferralHistory.query.filter(
                ReferralHistory.created_at < end_date,
                ReferralHistory.referrer_id.in_(chunk_amb_ids),
                ReferralHistory.referree_id.notin_(market_makers),
                ReferralHistory.status == ReferralHistory.Status.VALID
            ).with_entities(ReferralHistory.referree_id.distinct().label('referree_id')).all()
            history_referrees.extend(chunk_history_referrees)

            chunk_history_referrees_1 = ReferralHistory.query.filter(
                ReferralHistory.created_at < start_date,
                ReferralHistory.referrer_id.in_(chunk_amb_ids),
                ReferralHistory.referree_id.notin_(market_makers),
                ReferralHistory.status == ReferralHistory.Status.VALID
            ).with_entities(ReferralHistory.referree_id.distinct().label('referree_id')).all()
            history_referrees_1.extend(chunk_history_referrees_1)

            chunk_new_referree_count = ReferralHistory.query.filter(
                ReferralHistory.created_at >= start_date,
                ReferralHistory.created_at < end_date,
                ReferralHistory.referrer_id.in_(chunk_amb_ids),
                ReferralHistory.referree_id.notin_(market_makers),
                ReferralHistory.status == ReferralHistory.Status.VALID
            ).with_entities(func.count(ReferralHistory.referree_id.distinct().label(
                'referree_id'))).scalar() or 0
            new_referree_count += chunk_new_referree_count

            _total_count = ReferralHistory.query.filter(
                ReferralHistory.created_at < end_date,
                ReferralHistory.referrer_id.in_(chunk_amb_ids),
                ReferralHistory.referree_id.notin_(market_makers),
            ).with_entities(
                func.count(ReferralHistory.referree_id.distinct().label('referree_id'))
            ).scalar() or 0
            referree_total_count += _total_count

        trade_users_0 = (spot_trade_0 | perp_trade_0) - market_makers
        trade_users_1 = (spot_trade_1 | perp_trade_1) - market_makers
        new_trade_user_site_count = len(trade_users_0 - trade_users_1)

        # 受邀请人数
        referree_ids = [r.referree_id for r in history_referrees]
        referree_ids_1 = [r.referree_id for r in history_referrees_1]
        new_trade_user_count = len(
            (trade_users_0 & set(referree_ids)) - (trade_users_1 & set(referree_ids_1))
        )

        # 受邀请人交易额
        deal_count, deal_amount = get_deal_count_amount_by_ids(referree_ids, start_date)

        asset_rate = defaultdict(Decimal)
        asset_rate.update(AssetPrice.get_close_price_map(start_date))
        referee_fee_map = self.get_fee_map(start_date)
        invitee_all_fee = quantize_amount(
            sum([usd for user_id, usd in referee_fee_map.items()
            if user_id in referree_ids]), 2)

        # 收到返佣大使(大使代理)人数/总金额
        referral_amounts = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date >= start_date,
            ReferralAssetHistory.date < end_date,
            ReferralAssetHistory.amount > Decimal(),
            ReferralAssetHistory.type.in_([ReferralAssetHistory.Type.AMBASSADOR, ReferralAssetHistory.Type.AMBASSADOR_AGENT]),
        ).group_by(
            ReferralAssetHistory.user_id,
            ReferralAssetHistory.type,
        ).with_entities(
            ReferralAssetHistory.user_id,
            ReferralAssetHistory.type,
            func.sum(ReferralAssetHistory.amount).label('amount'),
        ).all()
        ambassador_refer_count, ambassador_refer_amount = 0, Decimal()
        ambassador_agent_refer_count, ambassador_agent_refer_amount = 0, Decimal()
        for r in referral_amounts:
            if r.type == ReferralAssetHistory.Type.AMBASSADOR:
                if r.user_id in ambassador_ids:
                    # 邀请码可以设置分成，所以大使类型的返佣可能会有2个人
                    ambassador_refer_count += 1
                    ambassador_refer_amount += r.amount  # 返佣数目包括分成的
                elif r.user_id in referree_ids:
                    ambassador_refer_amount += r.amount  # 返佣数目包括分成的
            elif r.type == ReferralAssetHistory.Type.AMBASSADOR_AGENT:
                if r.user_id in agent_user_ids:
                    ambassador_agent_refer_count += 1
                ambassador_agent_refer_amount += r.amount

        ambassador_asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR]
        ambassador_refer_amount_usd = asset_rate[ambassador_asset] * ambassador_refer_amount
        ambassador_refer_amount_usd = \
            quantize_amount(ambassador_refer_amount_usd, 2)

        agent_asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR_AGENT]
        ambassador_agent_refer_amount_usd = asset_rate[agent_asset] * ambassador_agent_refer_amount
        ambassador_agent_refer_amount_usd = quantize_amount(
            ambassador_agent_refer_amount_usd, 2)
        ambassador_refer_rate = quantize_amount(
            ambassador_refer_amount_usd / invitee_all_fee, 4) \
            if invitee_all_fee > Decimal() else Decimal()

        fees = get_period_trade_fee_mapping(start_date, end_date)
        site_total_fee = sum(fees.values())

        increase_user = User.query.filter(
            User.created_at >= start_date,
            User.created_at < end_date,
            User.user_type == User.UserType.NORMAL,  # 只考虑普通用户
        ).count()
        if increase_user == 0:
            new_user_percent = 0
        else:
            new_user_percent = quantize_amount(
                new_referree_count / increase_user, 4)
        if (
            denominator := site_total_fee - refer_type_report.fee_usd - ambassador_refer_amount_usd - ambassador_agent_refer_amount_usd
        ) == 0:
            fee_percent = 0
        else:
            fee_percent = quantize_amount(
                (invitee_all_fee - ambassador_refer_amount_usd - ambassador_agent_refer_amount_usd) / denominator, 4)

        if user_report.active_trade_user == 0:
            trade_percent = Decimal()
        else:
            trade_percent = quantize_amount(deal_count / user_report.active_trade_user, 4)

        record = self.model.get_or_create(report_date=start_date)
        record.ambassador_agent_count = ambassador_agent_count
        record.ambassador_count = ambassador_count
        record.new_ambassador_agent_count = new_ambassador_agent_count
        record.new_ambassador_count = new_ambassador_count
        record.refer_count = new_referree_count
        record.refer_deal_count = deal_count
        record.refer_deal_amount = deal_amount
        record.refer_fee_amount = invitee_all_fee
        record.refer_ambassador_count = ambassador_refer_count
        record.ambassador_referral_amount = ambassador_refer_amount_usd
        record.refer_ambassador_agent_count = ambassador_agent_refer_count
        record.ambassador_agent_referral_amount = ambassador_agent_refer_amount_usd
        record.ambassador_referral_rate = ambassador_refer_rate
        record.new_user_percent = new_user_percent
        record.fee_percent = fee_percent
        record.only_agent_count = only_agent_count
        record.ambassador_refer_user_count = referree_total_count
        record.new_trade_user_count = new_trade_user_count
        record.new_trade_user_site_count = new_trade_user_site_count
        record.trade_percent = trade_percent
        db.session_add_and_commit(record)


class DailyNormalReferralReporter(BaseReporter):
    model = DailyNormalReferReport

    def get_first_date(self) -> Optional[date]:
        return None

    def run(self, st: date, et: date):
        """
        依赖 DailySpotTradeReport, DailyPerpetualTradeReport, DailyUserReport,
        UserTradeSummary, UserTradeFeeSummary, UserBusinessRecord
        """

        if not check_data_ready(st):
            current_app.logger.warning(f"{st} {self.model} 引用 summary 数据未就绪")
            return

        # 获取每日所有新增人数，新增交易用户，总交易人数
        daily_user = DailyUserReport.query.filter(
            DailyUserReport.report_date == st
        ).with_entities(
            DailyUserReport.increase_user,
            DailyUserReport.increase_trade_user,
            DailyUserReport.active_trade_user,
            DailyUserReport.exchange_user,
        ).first()
        if not daily_user:
            current_app.logger.warning(f"{st} {self.model} 引用 DailyUserReport 数据未就绪")
            return

        # 全站现货
        spot_trade, spot_fee = get_site_trade_and_fee_usd(DailySpotTradeReport, st)
        # 全站合约
        perp_trade, perp_fee = get_site_trade_and_fee_usd(DailyPerpetualTradeReport, st)

        # 全站交易额计算双边要 * 2
        site_trade_usd = (spot_trade + perp_trade) * 2
        site_fee_usd = spot_fee + perp_fee

        refer_data = ReferralHistory.query.filter(
            ReferralHistory.created_at >= st,
            ReferralHistory.created_at < et,
        ).with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
        ).all()

        # 所有大使 id, 产品说大使人数远少于每日交易人数，用于后续过滤交易中的普通邀请用户
        ambassador_id_set = get_ambassador_id_set(et)

        normal_referrer_ids = get_normal_referrer_id_set(refer_data, ambassador_id_set)
        if normal_referrer_ids:
            # 新增邀请人, 有一个 hash 缓存
            refer_first_map = dict(UserFirstNormalReferTimeCache().hmget_with_keys(list(normal_referrer_ids)))
            # 最新邀请时间是当天的即为新增邀请人
            new_referrer = [k for k, v in refer_first_map.items() if str_to_datetime(v).date() == st]
        else:
            new_referrer = []
        invitee_ids = [i.referree_id for i in refer_data if i.referrer_id in normal_referrer_ids]

        # 受邀请人交易用户总数，交易额总数
        new_invitee_trade_count, _ = get_user_amount_and_count(invitee_ids, st)

        # 获取周期内全站交易用户中，属于普通邀请的用户
        """
        1. 先查出周期内所有交易用户id
        2. 拿着 id 去交易历史表中 确定 是邀请用户，
        3. 邀请用户对应的邀请人去除身份为大使的，得到普通邀请人
        4. 到2的返回数据中过滤普通邀请人对应的邀请用户
        """
        trade_invitee_ids = []
        trade_user_set = get_day_trade_user_set(st)
        for chunk_ids in batch_iter(list(trade_user_set), 1000):
            tmp_data = ReferralHistory.query.filter(
                ReferralHistory.referree_id.in_(chunk_ids)
            ).with_entities(
                ReferralHistory.referrer_id,
                ReferralHistory.referree_id,
            ).all()
            trade_invitee_ids.extend(i.referree_id for i in tmp_data if i.referrer_id not in ambassador_id_set)

        _, invitee_trade_usd = get_user_amount_and_count(trade_invitee_ids, st)

        # 普通邀请手续费
        invitee_fee_amount = get_day_fee_by_user_ids(trade_invitee_ids, st)

        row: DailyNormalReferReport = self.model.get_or_create(report_date=st)
        row.referrer_count = len(normal_referrer_ids)
        row.new_referrer_count = len(new_referrer)

        row.invitee_count = len(invitee_ids)
        row.increase_user_count = daily_user.increase_user if daily_user else 0

        row.trade_invitee_count = len(trade_invitee_ids)
        row.trade_user_count = daily_user.active_trade_user - daily_user.exchange_user if daily_user else 0

        row.new_trade_invitee_count = new_invitee_trade_count
        row.increase_trade_user_count = daily_user.increase_trade_user if daily_user else 0

        row.invitee_trade_usd = quantize_amount(invitee_trade_usd, 2)
        row.site_trade_usd = quantize_amount(site_trade_usd, 2)

        row.invitee_fee_usd = quantize_amount(invitee_fee_amount, 2)
        row.site_fee_usd = quantize_amount(site_fee_usd, 2)
        db.session_add_and_commit(row)


class MonthNormalReferralReporter(BaseMonthlyReporter):
    daily_model: Type[db.Model] = DailyNormalReferReport
    monthly_model: Type[db.Model] = MonthlyNormalReferReport

    def get_first_month(self):
        return None

    def run(self, start_month: date, end_month: date):
        query = self.daily_model.query.filter(
            self.daily_model.report_date >= start_month,
            self.daily_model.report_date < end_month
        ).with_entities(
            func.sum(self.daily_model.referrer_count).label('referrer_count'),
            func.sum(self.daily_model.new_referrer_count).label('new_referrer_count'),
            func.sum(self.daily_model.invitee_count).label('invitee_count'),
            func.sum(self.daily_model.increase_user_count).label('increase_user_count'),
            func.sum(self.daily_model.trade_invitee_count).label('trade_invitee_count'),
            func.sum(self.daily_model.trade_user_count).label('trade_user_count'),
            func.sum(self.daily_model.new_trade_invitee_count).label('new_trade_invitee_count'),
            func.sum(self.daily_model.increase_trade_user_count).label('increase_trade_user_count'),
            func.sum(self.daily_model.invitee_trade_usd).label('invitee_trade_usd'),
            func.sum(self.daily_model.site_trade_usd).label('site_trade_usd'),
            func.sum(self.daily_model.invitee_fee_usd).label('invitee_fee_usd'),
            func.sum(self.daily_model.site_fee_usd).label('site_fee_usd'),
        ).first()

        row = self.monthly_model.get_or_create(report_date=start_month)
        for field, value in dict(query).items():
            setattr(row, field, value)
        db.session_add_and_commit(row)


class DailyUserNormalReferStatistics(BaseReporter):
    model = DailyUserNormalReferStatistic

    def get_first_date(self) -> Optional[date]:
        return None

    def run(self, st: date, et: date):
        """
        依赖 UserTradeSummary, UserTradeFeeSummary, UserBusinessRecord, ReferralAssetHistory
        """
        if not check_data_ready(st):
            current_app.logger.warning(f"{st} {self.model} 引用 summary 数据未就绪")
            return

        refer_data = ReferralHistory.query.filter(
            ReferralHistory.created_at >= st,
            ReferralHistory.created_at < et,
        ).with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
        ).all()

        # 所有大使 id, 产品说大使人数远少于每日交易人数，用于后续过滤交易中的普通邀请用户
        ambassador_id_set = get_ambassador_id_set(et)
        normal_referrer_ids = get_normal_referrer_id_set(refer_data, ambassador_id_set)
        # 用户id 维度字典
        refer_map = {
            i: {
                "invitee_count": 0,
                "new_trade_invitee_count": 0,
                "invitee_trade_usd": Decimal(),
                "invitee_fee_usd": Decimal(),
                "refer_asset_cet": Decimal()
            } for i in normal_referrer_ids
        }

        invitee_map = {
            i.referree_id: i.referrer_id for i in refer_data if i.referrer_id in normal_referrer_ids
        }

        for key in invitee_map:
            refer_map[invitee_map[key]]["invitee_count"] += 1

        invitee_ids = list(invitee_map.keys())
        # 受邀请人交易用户总数，交易额总数
        for chunk_ids in batch_iter(invitee_ids, 1000):
            trade_summary = get_period_trade_amount_mapping(st, et, user_ids=chunk_ids)
            for user_id, amount in trade_summary.items():
                refer = refer_map[invitee_map[user_id]]
                refer["new_trade_invitee_count"] += 1
                refer["invitee_trade_usd"] += amount

            chunk_fee = ReferralAssetDetail.query.filter(
                ReferralAssetDetail.referree_id.in_(chunk_ids),
                ReferralAssetDetail.date == st,
            ).group_by(ReferralAssetDetail.user_id).with_entities(
                ReferralAssetDetail.referree_id.label('user_id'),
                func.sum(ReferralAssetDetail.spot_fee_usd + ReferralAssetDetail.perpetual_fee_usd).label("trade_fee_amount")
            ).all()
            
            for i in chunk_fee:
                refer = refer_map[invitee_map[i.user_id]]
                refer["invitee_fee_usd"] += i.trade_fee_amount

        for chunk_ids in batch_iter(normal_referrer_ids, 1000):
            _model = ReferralAssetHistory
            chunk_asset = _model.query.filter(
                _model.date == st,
                _model.type == _model.Type.REFERRAL,
                _model.status == _model.Status.FINISHED,
                _model.user_id.in_(chunk_ids)
            ).with_entities(
                _model.user_id,
                _model.amount
            )
            for i in chunk_asset:
                refer = refer_map[i.user_id]
                refer["refer_asset_cet"] += i.amount

        # 1000条 commit 一次
        for chunk_ids in batch_iter(normal_referrer_ids, 1000):
            for referrer_id in chunk_ids:
                row: DailyUserNormalReferStatistic = self.model.get_or_create(report_date=st, user_id=referrer_id)
                refer = refer_map[referrer_id]
                row.user_id = referrer_id
                row.invitee_count = refer["invitee_count"]
                row.new_trade_invitee_count = refer["new_trade_invitee_count"]
                row.invitee_trade_usd = quantize_amount(refer["invitee_trade_usd"], 2)
                row.invitee_fee_usd = quantize_amount(refer["invitee_fee_usd"], 2)
                row.refer_asset_cet = quantize_amount(refer["refer_asset_cet"], 2)
                db.session.add(row)
            db.session.commit()


class DailyNormalReferStatisticAggregate(BaseReporter):
    data_model = DailyUserNormalReferStatistic
    model = DailyNormalReferStatisticAggr

    def get_first_date(self) -> Optional[date]:
        return None

    def run(self, st: date, et: date):
        """
        依赖 DailyUserNormalReferStatistic
        """

        def aggr_data_by_time_type(user_ids, day_interval):
            query = self.data_model.query.filter(self.data_model.user_id.in_(user_ids))
            # None 表示查历史全部
            if day_interval != self.model.DayInterval.ALL:
                # 最近 xx 天只需减去 x-1 天
                query = query.filter(
                    self.data_model.report_date > st - datetime.timedelta(days=day_interval.value - 1)
                )
            cursor = query.group_by(self.data_model.user_id).with_entities(
                self.data_model.user_id,
                func.sum(self.data_model.invitee_count).label("invitee_count"),
                func.sum(self.data_model.new_trade_invitee_count).label("new_trade_invitee_count"),
                func.sum(self.data_model.invitee_trade_usd).label("invitee_trade_usd"),
                func.sum(self.data_model.invitee_fee_usd).label("invitee_fee_usd"),
                func.sum(self.data_model.refer_asset_cet).label("refer_asset_cet"),
            )
            return {i.user_id: i for i in cursor}

        # 先获取基础表中所有 user_id ，后续根据 user_id 切分
        data_query = self.data_model.query.filter().with_entities(
            self.data_model.user_id
        ).distinct()
        refer_user_ids = [i.user_id for i in data_query]

        ambassador_id_set = set(i.user_id for i in Ambassador.query.filter(
                Ambassador.effected_at < et,
            ).with_entities(
                Ambassador.user_id
            ))
        lang_map = language_cn_names()

        for chunk_ids in batch_iter(refer_user_ids, 500):

            user_query = User.query.join(
                VipUser, User.id == VipUser.user_id, isouter=True
            ).filter(User.id.in_(chunk_ids)).with_entities(
                User.created_at,
                User.location_code,
                VipUser.level,
                User.id
            ).all()
            user_info_map = {i.id: i for i in user_query}

            interval_user_data_map = {
                interval: aggr_data_by_time_type(chunk_ids, interval) for interval in self.model.DayInterval
            }
            # {DayInterval.DAY_7: {"100": {"invitee_count": 100}}}
            for interval, user_data_map in interval_user_data_map.items():
                for user_id, item in user_data_map.items():
                    row: DailyNormalReferStatisticAggr = self.model.get_or_create(
                        user_id=user_id,
                        day_interval=interval.value,
                    )
                    user_info = user_info_map[user_id]
                    row.report_date = st
                    row.is_history_ambassador = user_id in ambassador_id_set
                    row.country = c.cn_name if (c := get_country(user_info.location_code)) else "其他",
                    row.vip_level = user_info.level or 0,
                    row.user_create_at = datetime_to_str(user_info.created_at),
                    row.language = lang_map.get(UserPreferences(user_id).language, "/"),

                    for field, value in dict(item).items():
                        setattr(row, field, value)
                    db.session.add(row)
            db.session.commit()


@scheduled(crontab(hour='2-4', minute='9'), queue=CeleryQueues.REPORT)
@lock_call()
def run_daily_refer_report_schedule():
    reporter = DailyReferReporter()
    reporter.dispatch()


@scheduled(crontab(hour='2-4', minute='20'), queue=CeleryQueues.REPORT)
@lock_call()
def run_daily_ambassador_referral_report_schedule():
    reporter = DailyAmbassadorReferralReporter()
    reporter.dispatch()


@scheduled(crontab(hour='2-4', minute=20), queue=CeleryQueues.REPORT)
@lock_call()
def run_daily_normal_referral_reporter():
    reporter = DailyNormalReferralReporter()
    reporter.dispatch()


@scheduled(crontab(minute=0, hour=2, day_of_month=1), queue=CeleryQueues.REPORT)
@lock_call()
def run_month_normal_referral_reporter():
    reporter = MonthNormalReferralReporter()
    reporter.dispatch()


@scheduled(crontab(hour='2-4', minute=20), queue=CeleryQueues.REPORT)
@lock_call()
def run_daily_user_normal_refer_statistics():
    reporter = DailyUserNormalReferStatistics()
    reporter.dispatch()


@scheduled(crontab(hour='2-4', minute=40), queue=CeleryQueues.REPORT)
@lock_call()
def run_daily_normal_refer_statistic_aggregate():
    reporter = DailyNormalReferStatisticAggregate()
    reporter.dispatch()
