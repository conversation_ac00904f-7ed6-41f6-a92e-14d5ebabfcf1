# -*- coding: utf-8 -*-
from datetime import date, timedelta
from decimal import Decimal
from typing import Optional, List

from celery.schedules import crontab
from flask import current_app

from app.business import lock_call, PriceManager
from app.common import CeleryQueues
from app.models import (
    DailySiteFiatOrderReport, DailyAssetFiatOrderReport, FiatOrder, db, DailyThirdPartyFiatOrderReport,
    MonthlyThirdPartyFiatOrderReport,
)
from app.utils import scheduled, group_by
from .base import BaseReporter, BaseMonthlyReporter
from ..fiat import get_fiat_currencies, get_fiat_partners
from ..fiat.base import first_use_fiat_tag_fmt, SupportType
from ..user_tag import check_user_tag_finished, TagReader
from ...models.user_tag import UserTag
from ...utils.date_ import date_to_datetime, datetime_to_time, today


class BaseFiatReporter(BaseReporter):

    def get_first_date(self):
        return None

    @staticmethod
    def get_fiat_tag(third_party: str = None):
        if third_party:
            tag = getattr(UserTag, first_use_fiat_tag_fmt(third_party))
        else:
            tag = UserTag.FIRST_FIAT_ORDER_TIME
        return tag

    def check_user_tag(self, report_date: date, third_party: str) -> bool:
        if report_date < today():
            # 刷历史数据不用检测标签数据
            return True
        tag = self.get_fiat_tag(third_party)
        ret, _ = check_user_tag_finished([tag], report_date)
        if ret:
            return True
        else:
            current_app.logger.warning(f"用户标签 {tag} 数据未就绪")

    def get_user_count_and_new_count(self, report_date: date, third_parties: List[str] = None) -> [int, int]:
        yesterday = report_date - timedelta(days=1)
        yes_time = datetime_to_time(yesterday)
        today_time = datetime_to_time(report_date)

        user_set, user_new_set = set(), set()
        tags = [self.get_fiat_tag(third_party) for third_party in third_parties]
        all_data_dic = TagReader.get_all_tag_data(tags)
        for data in all_data_dic.values():
            for uid, _time in data.items():
                if _time < today_time:
                    user_set.add(uid)
                if yes_time <= _time < today_time:
                    user_new_set.add(uid)

        return len(user_set), len(user_new_set)

    def get_report_type(self, support_type: SupportType = None):
        if support_type == SupportType.BUY:
            return self.model.ReportType.BUY
        if support_type == SupportType.SELL:
            return self.model.ReportType.SELL
        return self.model.ReportType.ALL

    def add_daily_report(self, start_date: date, end_date: date,
                         third_party: str = None, support_type: SupportType = None):
        start_dt = date_to_datetime(start_date)
        end_dt = date_to_datetime(end_date)
        query = FiatOrder.query.filter(
            FiatOrder.created_at >= start_dt,
            FiatOrder.created_at < end_dt,
        )
        if third_party:
            query = query.filter(FiatOrder.third_party == third_party)
        fiat_rates = PriceManager.fiats_to_usd(get_fiat_currencies())
        apply_user_set = set()
        deal_user_set = set()
        apply_count = 0
        deal_count = 0
        deal_usd = Decimal()
        if not support_type:
            user_count, new_user_count = self.get_user_count_and_new_count(end_date, [third_party])
        else:
            if third_party:
                user_count, new_user_count = self.get_user_count_and_new_count(end_date, [third_party])
            else:
                third_parties = get_fiat_partners(support_type)
                user_count, new_user_count = self.get_user_count_and_new_count(end_date, third_parties)

        for record in query:
            if support_type == SupportType.BUY:
                if record.order_type == FiatOrder.OrderType.BUY:
                    if (third_party and third_party == record.third_party.lower()) or not third_party:
                        if record.status == FiatOrder.StatusType.APPROVED:
                            deal_count += 1
                            deal_usd += record.fiat_total_amount * fiat_rates[record.fiat_currency]
                            deal_user_set.add(record.user_id)
                        apply_count += 1
                        apply_user_set.add(record.user_id)
            elif support_type == SupportType.SELL:
                if record.order_type == FiatOrder.OrderType.SELL:
                    if (third_party and third_party == record.third_party.lower()) or not third_party:
                        if record.status == FiatOrder.StatusType.APPROVED:
                            deal_count += 1
                            deal_usd += record.fiat_total_amount * fiat_rates[record.fiat_currency]
                            deal_user_set.add(record.user_id)
                        apply_count += 1
                        apply_user_set.add(record.user_id)
            else:
                if record.status == FiatOrder.StatusType.APPROVED:
                    deal_count += 1
                    deal_usd += record.fiat_total_amount * fiat_rates[record.fiat_currency]
                    deal_user_set.add(record.user_id)
                apply_count += 1
                apply_user_set.add(record.user_id)

        obj_dict = dict(
            apply_user_count=len(apply_user_set),
            deal_user_count=len(deal_user_set),
            apply_count=apply_count,
            deal_count=deal_count,
            deal_usd=deal_usd,
            user_count=user_count,
            new_user_count=new_user_count
        )
        if third_party:
            obj_dict["third_party"] = third_party
            record = self.model.get_or_create(report_date=start_date, third_party=third_party,
                                              report_type=self.get_report_type(support_type))
        else:
            record = self.model.get_or_create(report_date=start_date, report_type=self.get_report_type(support_type))

        for field, val in obj_dict.items():
            setattr(record, field, val)
        db.session.add(record)
        db.session.flush()

    def run(self, start_date: date, end_date: date, third_party: str = None):
        if not self.check_user_tag(end_date, third_party):
            return
        self.add_daily_report(start_date, end_date, third_party)
        self.add_daily_report(start_date, end_date, third_party, SupportType.BUY)
        self.add_daily_report(start_date, end_date, third_party, SupportType.SELL)
        db.session.commit()


class SiteFiatReporter(BaseFiatReporter):
    model = DailySiteFiatOrderReport

    def get_first_date(self) -> Optional[date]:
        record = FiatOrder.query.order_by(FiatOrder.created_at.asc()).first()
        return record.created_at.date() if record else None


class DailyThirdPartyFiatReporter(BaseFiatReporter):
    model = DailyThirdPartyFiatOrderReport

    def run(self, start_date: date, end_date: date, third_party: str = None):
        for _third_party in get_fiat_partners():
            super().run(start_date, end_date, third_party=_third_party)


class MonthlyThirdPartyFiatReporter(BaseMonthlyReporter):
    daily_model = DailyThirdPartyFiatOrderReport
    monthly_model = MonthlyThirdPartyFiatOrderReport

    def get_first_month(self):
        return None

    def get_report_type(self, support_type: SupportType = None):
        if support_type == SupportType.BUY:
            return self.daily_model.ReportType.BUY, self.monthly_model.ReportType.BUY
        if support_type == SupportType.SELL:
            return self.daily_model.ReportType.SELL, self.monthly_model.ReportType.SELL
        return self.daily_model.ReportType.ALL, self.monthly_model.ReportType.ALL

    def add_monthly_report(self, start_month: date, end_month: date, support_type: SupportType = None):
        third_party_list = get_fiat_partners(support_type)

        query = FiatOrder.query.filter(
            FiatOrder.created_at >= start_month,
            FiatOrder.created_at < end_month,
        )

        third_party_dict = {
            k: {
                "apply_user_set": set(),
                "deal_user_set": set(),
                "third_party": k,
            } for k in third_party_list
        }

        for row in query:
            third_party = row.third_party.lower()
            if support_type == SupportType.BUY:
                if third_party in third_party_list and row.order_type == FiatOrder.OrderType.BUY:
                    _buy_dict = third_party_dict[third_party]
                    _buy_dict["apply_user_set"].add(row.user_id)
                    if row.status == FiatOrder.StatusType.APPROVED:
                        _buy_dict["deal_user_set"].add(row.user_id)
            elif support_type == SupportType.SELL:
                if third_party in third_party_list and row.order_type == FiatOrder.OrderType.SELL:
                    _sell_dict = third_party_dict[third_party]
                    _sell_dict["apply_user_set"].add(row.user_id)
                    if row.status == FiatOrder.StatusType.APPROVED:
                        _sell_dict["deal_user_set"].add(row.user_id)
            else:
                if third_party in third_party_list:
                    _dict = third_party_dict[third_party]
                    _dict["apply_user_set"].add(row.user_id)
                    if row.status == FiatOrder.StatusType.APPROVED:
                        _dict["deal_user_set"].add(row.user_id)

        daily_report_type, monthly_report_type = self.get_report_type(support_type)
        rows: List[DailyThirdPartyFiatOrderReport] = self.daily_model.query.filter(
            self.daily_model.report_date >= start_month,
            self.daily_model.report_date < end_month,
            self.daily_model.report_type == daily_report_type
        ).order_by(self.daily_model.report_date).all()

        group_items = group_by(lambda x: x.third_party, rows)
        for party in third_party_list:
            _dict = third_party_dict[party]
            items = group_items[party]
            _dict["user_count"] = items[-1].user_count
            _dict["deal_usd"] = sum([i.deal_usd for i in items])
            _dict["deal_count"] = sum([i.deal_count for i in items])
            _dict["apply_count"] = sum([i.apply_count for i in items])
            _dict["new_user_count"] = sum([i.new_user_count for i in items])
            _dict["apply_user_count"] = len(_dict.pop("apply_user_set"))
            _dict["deal_user_count"] = len(_dict.pop("deal_user_set"))

            record = self.monthly_model.get_or_create(
                report_date=start_month, third_party=party, report_type=monthly_report_type
            )
            for field, val in _dict.items():
                setattr(record, field, val)
            db.session.add(record)
            db.session.flush()

    def run(self, start_month: date, end_month: date):
        self.add_monthly_report(start_month, end_month)
        self.add_monthly_report(start_month, end_month, SupportType.BUY)
        self.add_monthly_report(start_month, end_month, SupportType.SELL)
        db.session.commit()


class AssetFiatReporter(BaseReporter):
    model = DailyAssetFiatOrderReport

    def get_first_date(self) -> Optional[date]:
        record = FiatOrder.query.order_by(FiatOrder.created_at.asc()).first()
        return record.created_at.date() if record else None

    def run(self, start_date: date, end_date: date):
        start_dt = date_to_datetime(start_date)
        end_dt = date_to_datetime(end_date)
        query = FiatOrder.query.filter(
            FiatOrder.created_at >= start_dt,
            FiatOrder.created_at < end_dt,
        ).with_entities(
            FiatOrder.asset.distinct().label('asset')
        )
        assets = set([v.asset for v in query])
        for asset in assets:
            self.run_by_asset(asset, start_date, end_date)

    def run_by_asset(self, asset: str, start_date: date, end_date: date):
        start_dt = date_to_datetime(start_date)
        end_dt = date_to_datetime(end_date)
        query = FiatOrder.query.filter(
            FiatOrder.created_at >= start_dt,
            FiatOrder.created_at < end_dt,
            FiatOrder.asset == asset,
        )
        fiat_rates = PriceManager.fiats_to_usd(get_fiat_currencies())
        apply_user_set = set()
        deal_user_set = set()
        apply_count = 0
        deal_count = 0
        deal_usd = Decimal()

        for record in query:
            if record.status == FiatOrder.StatusType.APPROVED:
                deal_count += 1
                deal_usd += record.fiat_total_amount * fiat_rates[
                    record.fiat_currency]
                deal_user_set.add(record.user_id)
            apply_count += 1
            apply_user_set.add(record.user_id)
        r = self.model(
            report_date=start_date,
            asset=asset,
            apply_user_count=len(apply_user_set),
            deal_user_count=len(deal_user_set),
            apply_count=apply_count,
            deal_count=deal_count,
            deal_usd=deal_usd
        )
        db.session.add(r)
        db.session.commit()


@scheduled(crontab(hour='2-4', minute='0'), queue=CeleryQueues.REPORT)
@lock_call()
def run_daily_site_fiat_report_schedule():
    reporter = SiteFiatReporter()
    reporter.dispatch()
    third_reporter = DailyThirdPartyFiatReporter()
    third_reporter.dispatch()


@scheduled(crontab(hour='2-4', minute="0"), queue=CeleryQueues.REPORT)
@lock_call()
def run_monthly_site_fiat_report_schedule():
    MonthlyThirdPartyFiatReporter().dispatch()


@scheduled(crontab(hour=0, minute='4-9'), queue=CeleryQueues.REPORT)
@lock_call()
def run_daily_asset_fiat_report_schedule():
    reporter = AssetFiatReporter()
    reporter.dispatch()
