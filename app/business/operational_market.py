#!/usr/bin/env python
from typing import Optional

from app.exceptions import InvalidArgument
from app.models import db
from app.models import LoginPageMarket


class OperationalMarketBusiness:
    """
    首页行情管理类
    """

    def __init__(self, model_cls: db.Model, id_):
        self.model_cls = model_cls
        self.operational_market: db.Model = self.model_cls.query.get(id_)
        self.check_exists()

    @classmethod
    def total_count(cls, model_cls):
        return model_cls.query.count()

    @property
    def is_exists(self):
        return True if self.operational_market else False

    def check_exists(self):
        if not self.is_exists:
            raise InvalidArgument(message=f'市场不在列表中')

    @classmethod
    def add(cls, model_cls: db.Model, market: str,
            status: Optional[LoginPageMarket.Status]):
        """
        添加行情管理
        """
        if model_cls.query.filter(
            model_cls.market == market
        ).first():
            raise InvalidArgument(message=f'市场 {market} 已经在列表中')
        item = model_cls(
            market=market,
            sort_id=cls.total_count(model_cls) + 1,
        )
        if status:
            item.status = status
        db.session.add(item)
        db.session.commit()

    def edit(self, market: Optional[str],
             status: Optional[LoginPageMarket.Status]):
        """
        编辑
        """
        if market and self.operational_market.market != market:
            if self.model_cls.query.filter(
                    self.model_cls.market == market
            ).first():
                raise InvalidArgument(message=f'市场 {market} 已经在列表中')
            self.operational_market.market = market
        if status:
            self.operational_market.status = status
        db.session.commit()

    def delete(self):
        """
        删除市场
        """
        db.session.delete(self.operational_market)
        db.session.commit()
        self._reorder_all()

    def _reorder_all(self):
        query = self.model_cls.query.order_by(
            self.model_cls.sort_id
        )
        sort_id = 0
        for item in query:
            sort_id += 1
            item.sort_id = sort_id
        db.session.commit()

    def get_by_sort_id(self, sort_id):
        return self.model_cls.query.filter(
            self.model_cls.sort_id == sort_id
        ).first()

    def reorder(self, up: bool):
        if up:
            if self.operational_market.sort_id == 1:
                raise InvalidArgument(message='已经是第一')
            record = self.get_by_sort_id(self.operational_market.sort_id - 1)
        else:
            last_record = self.model_cls.query.order_by(
                    self.model_cls.sort_id.desc()
                ).first()
            if self.operational_market.sort_id == last_record.sort_id:
                raise InvalidArgument(message='已经是最后')
            record = self.get_by_sort_id(self.operational_market.sort_id + 1)
        self.operational_market.sort_id, record.sort_id = \
            record.sort_id, self.operational_market.sort_id
        db.session.commit()

    @classmethod
    def list_all_open_records(cls, model_cls):
        query = model_cls.query.filter(
            model_cls.status == model_cls.Status.OPEN
        ).order_by(
            model_cls.sort_id
        ).with_entities(
            model_cls.market
        )
        return [r.market for r in query]
