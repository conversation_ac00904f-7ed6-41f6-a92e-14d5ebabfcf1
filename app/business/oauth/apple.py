import json
from urllib.parse import urlencode
import uuid

from flask import current_app
from marshmallow import fields
import jwt
import jwt.algorithms

from app import config
from app.api.common.request import get_request_platform
from app.caches.user import ThirdPartyAccountConfigCache
from app.exceptions.basic import InvalidArgument
from app.utils.date_ import current_timestamp
from app.utils.http_client import RESTClient
from .base import BaseOAuthClient

class AppleOAuthClient(BaseOAuthClient):

    name = 'apple'
    config_name = 'APPLE_OAUTH_CONFIG'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.client = RESTClient(self.config['base_url'], timeout=5)

    @property
    def _client_id(self):
        platform = get_request_platform()
        is_ios = platform.is_ios()
        if is_ios:
            client_id = self.config['ios_client_id']
        else:
            client_id = self.config['client_id']
        return client_id
    
    def _get_pubkey(self, kid: str):

        def _find_match(keys):
            for k in keys:
                if k['kid'] == kid:
                    return k

        cache = ThirdPartyAccountConfigCache(self.name)

        pubkeys = result = None
        pubkeys = cache.read()
        if pubkeys:
            pubkeys = json.loads(pubkeys)
        if pubkeys and (k:= _find_match(pubkeys)):
            result = jwt.algorithms.RSAAlgorithm.from_jwk(k)
        if result:
            return result
        pubkeys = self.client.get('/auth/keys')['keys']
        cache.set(json.dumps(pubkeys))
        cache.expire(cache.TTL)
        if k:= _find_match(pubkeys):
            return jwt.algorithms.RSAAlgorithm.from_jwk(k)
        
    def _verify_decoded_identity_token(self, decoded: dict) -> bool:
        iss, aud, exp, email, email_verified = \
            decoded['iss'], decoded['aud'], decoded['exp'], decoded.get('email'), decoded.get('email_verified')
        verified = iss == 'https://appleid.apple.com' \
            and aud == self._client_id \
            and email_verified \
            and exp > current_timestamp(to_int=True) \
            and email
        return verified


    def get_user_info(self, code: str) -> dict:
        jwt_header = \
        {
            "alg": self.config['private_key']['alg'],
            "kid": self.config['private_key']['kid']
        }
        current_ts = current_timestamp(to_int=True)
        jwt_payload = \
        {
            "iss": self.config['iss'],
            "iat": current_ts,
            "exp": current_ts + 3600,
            "aud": self.config['base_url'],
            "sub": self._client_id
        }
        pk = self.config['private_key']['key'].strip()
        client_secret = jwt.encode(jwt_payload, pk, algorithm=self.config['private_key']['alg'], headers=jwt_header)
        post_data = \
        {
            "client_id": self._client_id,
            "client_secret": client_secret,
            "code": code,
            "grant_type": "authorization_code",
        }
        try:
            validated_data = self.client.post('/auth/token', data=post_data)
        except Exception as e:
            current_app.logger.error(f'Apple OAuth failed {code[:10]} {e!r}')
            raise InvalidArgument(message="Apple authorization failed")
        return self.get_user_info_from_token(validated_data['id_token'])

    
    def get_auth_url(self) -> str:
        base_url = self.config['base_url']
        params = {
            'client_id': self._client_id,
            'redirect_uri': config['SITE_URL'],
            'response_type': 'code id_token',
            'scope': 'name email',
            'state': str(uuid.uuid4())
            }
        query_str = urlencode(params)
        return f'{base_url}?{query_str}'
    
    def get_user_info_from_token(self, token: str) -> dict:
        try:
            _headers = jwt.get_unverified_header(token)
            pubkey = self._get_pubkey(_headers['kid'])
            
            assert pubkey
            decoded = jwt.decode(token, pubkey, algorithms=['RS256'], 
                                audience=self._client_id)
        except Exception as e:
            current_app.logger.error(f'Apple account information unavailable {e!r}')
            raise InvalidArgument(message="Apple account information unavailable")
        if not self._verify_decoded_identity_token(decoded):
            raise InvalidArgument(message="Apple account verification failed")
        email = decoded['email']
        user_id = decoded['sub']
        is_private_email = decoded.get('is_private_email', False)
        is_private_email = fields.Boolean().deserialize(is_private_email)
        return dict(
            email=email,
            user_id=user_id,
            is_private_email=is_private_email
        )