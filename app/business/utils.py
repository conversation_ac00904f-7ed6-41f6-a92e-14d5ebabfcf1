# -*- coding: utf-8 -*-
from datetime import date, datetime
from functools import total_ordering
from typing import Type, Optional, Generator, Iterable, Any, Union

from pytz import UTC
from sqlalchemy.sql.elements import BinaryExpression, BooleanClauseList

from app.utils.iterable import T

from app.models import AssetPrice, db, User
from app.business import PriceManager
from app.utils import batch_iter
from app.utils.date_ import convert_datetime, date_to_datetime
from app.exceptions import RecordNotFound


def fetch_user_emails(user_ids: set) -> dict:
    user_emails = {}
    for chunk_user_ids in batch_iter(user_ids, 5000):
        rows = User.query.with_entities(
            User.id,
            User.email,
        ).filter(User.id.in_(chunk_user_ids)).all()
        user_emails.update(dict(rows))
    return user_emails


def get_asset_price_by_datetime(dt: datetime):
    result = PriceManager.assets_to_usd(PriceManager.list_asset())
    hour = convert_datetime(dt, 'hour')
    query = AssetPrice.query.filter(
        AssetPrice.date == hour
    )
    for v in query:
        result[v.asset] = v.price
    return result


@total_ordering
class AssetComparator(object):

    def __init__(self, asset):
        self.asset = asset

    def __lt__(self, other):
        from app.assets import AssetUtils
        t = AssetUtils.TOP_ASSETS

        if self.asset in t and other.asset in t:
            return t.index(self.asset) < t.index(other.asset)

        if self.asset in t:
            return True

        if other.asset in t:
            return False

        return self.asset < other.asset

    def __eq__(self, other):
        return self.asset == other.asset


@total_ordering
class PerpetualMarketComparator(object):
    stock_assets_idx_map = {'CET': 0, 'USDT': 1, 'USDC': 2, 'BTC': 3, 'ETH': 4}
    money_assets_idx_map = {'USDT': 0, 'USDC': 1}

    def __init__(self, stock_asset: str, money_asset: str):
        self.stock_asset = stock_asset
        self.money_asset = money_asset

    def __lt__(self, other):
        if self.stock_asset in self.stock_assets_idx_map and other.stock_asset in self.stock_assets_idx_map:
            if self.stock_assets_idx_map[self.stock_asset] < self.stock_assets_idx_map[other.stock_asset]:
                return True
            elif self.stock_assets_idx_map[self.stock_asset] > self.stock_assets_idx_map[other.stock_asset]:
                return False
            else:
                return self.money_assets_idx_map.get(self.money_asset, 2) < self.money_assets_idx_map.get(other.money_asset, 2)
        elif self.stock_asset in self.stock_assets_idx_map:
            return True
        elif other.stock_asset in self.stock_assets_idx_map:
            return False
        elif self.stock_asset == other.stock_asset:
            return self.money_assets_idx_map.get(
                self.money_asset, max(self.money_assets_idx_map.values())+1) < self.money_assets_idx_map.get(
                other.money_asset, max(self.money_assets_idx_map.values())+1)
        else:
            return self.stock_asset < other.stock_asset

    def __eq__(self, other):
        return self.stock_asset == other.stock_asset and self.money_asset == other.money_asset


def yield_query_records_by_time_range(
        table: Type[db.Model],
        start_time: Optional[datetime | date],
        end_time: Optional[datetime | date],
        select_fields: Iterable[db.Column],
        filters: Optional[Union[dict, BinaryExpression, BooleanClauseList]] = None,
        limit: int = 2000,
        filter_in_query: bool = True) -> Generator[T, None, None]:
    """
    按主键id倒序查询最近一段时间的记录，该方法不适用于查询过早的记录。
    如果符合筛选条件的数据越稀疏或者筛选条件越复杂的话，扫全表的概率越大，不适合使用此方法。
    filter: dict, BinaryExpression, BooleanClauseList,
    当为dict时，使用filter_by函数，另外则使用filter函数
    filter_in_query: 在sql中还是在内存中做筛选，注意在内存中做筛选时的相等判断是强类型的，相比mysql更严格。
                     对于一些表，要找到满足filters的limit条记录，可能需要扫描大量记录；
                     全表满足filters的记录少于limit时，需要扫描全表。这种情况在内存中做筛选更有效。
    返回为生成器，在大批量读取数据时，处理完其中一批之后再取读取下一批
    """
    if start_time and type(start_time) is date:
        start_time = date_to_datetime(start_time)
    if end_time and type(end_time) is date:
        end_time = date_to_datetime(end_time)
    filter_func = None
    if filters is not None:
        if not isinstance(filters, (dict, BinaryExpression, BooleanClauseList)):
            raise TypeError("filters must be dict, sqlalchemy expression or None")
        if isinstance(filters, dict):
            filter_func = "filter_by"
        else:
            filter_func = "filter"

    last = None
    _fields = list(select_fields)
    if getattr(table, "created_at") not in _fields:
        _fields.append(getattr(table, "created_at"))
    if getattr(table, "id") not in _fields:
        _fields.append(getattr(table, "id"))
    while last is None or last.created_at > start_time:
        query = table.query.with_hint(table, "FORCE INDEX(PRI)")
        if filter_in_query and filter_func:
            if filter_func == "filter_by":
                query = query.filter_by(**filters)
            if filter_func == "filter":
                query = query.filter(filters)
        if last:
            query = query.filter(table.id < last.id)
        rows = query.order_by(table.id.desc()).limit(limit).with_entities(
            *_fields
        ).all()
        if not start_time:
            start_time = datetime.min.replace(tzinfo=UTC)
        if not end_time:
            end_time = datetime.max.replace(tzinfo=UTC)
        for row in rows:
            if start_time < row.created_at < end_time:
                if filter_in_query:
                    yield row
                elif isinstance(filters, dict) and all(getattr(row, k) == v for k, v in filters.items()):
                    yield row
        if len(rows) != limit:
            break
        last = rows[-1]


def query_records_by_time_range(table: Type[db.Model], start_time: datetime | date, end_time: datetime | date,
                                filters: Optional[dict] = None,
                                limit: int = 2000,
                                filter_in_query: bool = True):
    """按主键id倒序查询最近一段时间的记录，该方法不适用于查询过早的记录。
        filter_in_query: 在sql中还是在内存中做筛选，注意在内存中做筛选时的相等判断是强类型的，相比mysql更严格。
                         对于一些表，要找到满足filters的limit条记录，可能需要扫描大量记录；
                         全表满足filters的记录少于limit时，需要扫描全表。这种情况在内存中做筛选更有效。
    """
    if filters and not isinstance(filters, dict):   # maybe misuse, add a check.
        raise TypeError("filters must be dict or None")
    
    if type(start_time) is date:
        start_time = date_to_datetime(start_time)
    if type(end_time) is date:
        end_time = date_to_datetime(end_time)

    results = []
    last = None
    while last is None or last.created_at > start_time:
        query = table.query.with_hint(table, "FORCE INDEX(PRI)")
        if filters and filter_in_query:
            query = query.filter_by(**filters)
        if last:
            query = query.filter(table.id < last.id)
        rows = query.order_by(table.id.desc()).limit(limit).all()
        for row in rows:
            if start_time < row.created_at < end_time:
                if filter_in_query or all(getattr(row, k) == v for k, v in filters.items()):
                    results.append(row)
        if len(rows) != limit:
            break
        last = rows[-1]
    return results


def yield_query_records_slice_from_user_id(
        table: Type[db.Model],
        select_fields: Iterable[db.Column],
        filters: Optional[Any] = None,
        user_id_field_name: str = "user_id",
        limit: int = 20000) -> Generator[T, None, None]:
    """
    按照filters进行过滤，需要注意user_id字段必须是索引，否则无法提升性能甚至更慢
    """
    max_user_id = User.query.order_by(User.id.desc()).with_entities(User.id).first().id
    _q = table.query.with_entities(getattr(table, user_id_field_name), *select_fields)
    if isinstance(filters, BinaryExpression) or isinstance(filters, BooleanClauseList):
        _q = _q.filter(filters)
    cursor_user_id = 0
    while cursor_user_id < max_user_id:
        cursor_end_user_id = min(max_user_id, cursor_user_id + limit)
        records = _q.filter(getattr(table, user_id_field_name) > cursor_user_id,
                            getattr(table, user_id_field_name) <= cursor_end_user_id).with_entities(
            getattr(table, user_id_field_name),
            *select_fields
        ).all()
        yield from records
        cursor_user_id = cursor_end_user_id


def drag_sort(old_data: dict, old_index: int, new_index: int, model: db.Model, field: str):

    edit_row = model.query.get(old_data[old_index]["id"])
    swap_row = model.query.get(old_data[new_index]["id"])
    if not edit_row or not swap_row:
        raise RecordNotFound
    swap_row_sort_id = getattr(swap_row, field)
    edit_row_sort_id = getattr(edit_row, field)
    attr = getattr(model, field)
    if edit_row_sort_id > swap_row_sort_id:
        # 表示进行下移
        model.query.filter(
            attr >= swap_row_sort_id,
            attr < edit_row_sort_id
        ).update(
            {attr: attr + 1},
            synchronize_session=False
        )
    else:
        # 表示进行上移
        model.query.filter(
            attr > edit_row_sort_id,
            attr <= swap_row_sort_id
        ).update(
            {attr: attr - 1},
            synchronize_session=False
        )
    setattr(edit_row, field, swap_row_sort_id)
    db.session.commit()


def yield_table_all_rows(table: Type[db.Model], last_id=None):
    # 使用 id 正序排序获取一个 table 所有数据
    batch = 2000
    start_id = last_id or 0
    while True:
        rows = table.query.filter(
            table.id > start_id
        ).order_by(
            table.id
        ).limit(
            batch
        ).with_hint(
            table, "FORCE INDEX(PRI)"
        ).all()
        if not rows:
            break
        for row in rows:
            start_id = row.id
            yield row
