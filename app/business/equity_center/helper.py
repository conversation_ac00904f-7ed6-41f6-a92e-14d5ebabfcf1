# -*- coding: utf-8 -*-
from decimal import Decimal
from datetime import timedelta
from typing import Optional, Callable
from app.models.equity_center import db, EquityType, EquityBaseInfo, EquitySetting, UserEquity, UserAirdropEquity, \
    UserCashbackEquity
from app.models.user import UserBizTag
from app.config import config
from app.business import ServerClient, SPOT_ACCOUNT_ID
from app.caches.config import EquitySettingCache
from app.utils import BaseConfig, ConfigField, ConfigMode, batch_iter
from app.utils.amount import amount_to_str


class _EquitySettings(BaseConfig):
    F = ConfigField
    model = EquitySetting
    cache = EquitySettingCache

    cet_balance_warning_amount = F(Decimal, "系统结算账户CET预警余额", default=20000)
    cet_balance_warning_emails: set = F(set, "系统结算账户CET预警邮箱", default=())
    cet_transfer_enabled: bool = F(bool, '开启系统结算账户CET划转', default=True,
                                   remark="关闭后，空投权益停止发放、返现权益停止返现")
    cashback_expired_notice_hours: int = F(int, '返现权益到期触达小时', default=24)

    def __init__(self, model: ConfigMode = ConfigMode.REAL_TIME):
        super().__init__(model)

    def _get_all(self) -> dict[str, str]:
        pass

    def _get_one(self, name: str) -> Optional[str]:
        return self.cache().get_value(name)

    def _set_one(self, name: str, value: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            row = self.model(key=name)
            db.session.add(row)
        row.value = value
        row.status = self.model.Status.VALID
        db.session.commit()
        self.cache().set_value(name, value)

    def _del_one(self, name: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            return
        row.status = self.model.Status.DELETED
        db.session.commit()
        self.cache().del_value(name)


EquitySettings = _EquitySettings(model=ConfigMode.REAL_TIME)


def check_sys_balance_enough(asset: str, total_amount: Decimal) -> bool:
    """ 检查系统帐号，某个币种资产是否足够 """
    sys_user_id = config["EQUITY_CENTER_ADMIN_USER_ID"]
    balance = ServerClient().get_user_balances(user_id=sys_user_id, asset=asset, account_id=SPOT_ACCOUNT_ID)
    avl_amount = balance.get(asset, {}).get('available', Decimal())
    return avl_amount >= total_amount


class EquityCenterService:
    """ 权益中心对外逻辑，提供给其他模块（任务中心）调用 """

    # 暂时 所有状态以及类型都可以调用，后续控制调用细节
    BizTypes = UserEquity.BusinessType
    UserEquityStatus = UserEquity.Status

    FAILED_STATUS = {
        UserEquity.Status.FAILED,
        UserEquity.Status.CREATED
    }

    @classmethod
    def batch_query_equity_basic_info(cls, eq_ids: set[int]) -> dict[int, dict]:
        eq_base_info_rows = EquityBaseInfo.query.filter(
            EquityBaseInfo.id.in_(eq_ids),
        ).with_entities(
            EquityBaseInfo.id,
            EquityBaseInfo.type,
            EquityBaseInfo.cost_asset,
            EquityBaseInfo.cost_amount,
            EquityBaseInfo.status,
            EquityBaseInfo.extra_data
        ).all()
        res = {
            i.id: {
                "type": i.type,
                "cost_asset": i.cost_asset,
                "cost_amount": i.cost_amount,
                "status": i.status,
                "cashback_asset": i.extra_data.get('cashback_asset') if i.extra_data else None
            }
            for i in eq_base_info_rows
        }
        return res

    @classmethod
    def get_or_create_airdrop_base_equity(
            cls,
            airdrop_asset: str,
            airdrop_amount: Decimal,
            creator: int,
            remark: str = "",
    ) -> int:
        """查询或创建一个空投权益配置"""
        assert airdrop_amount > 0
        remark = remark[:512]
        value_asset = "USDT"
        extra_data = dict(
            value_asset=value_asset,
        )
        base_eq: EquityBaseInfo = EquityBaseInfo.query.filter(
            EquityBaseInfo.type == EquityType.AIRDROP,
            EquityBaseInfo.cost_asset == airdrop_asset,
            EquityBaseInfo.cost_amount == airdrop_amount,
        ).first()
        if base_eq and base_eq.extra_data == extra_data:
            base_eq.status = EquityBaseInfo.Status.OPEN
            base_eq.creator = creator
            base_eq.remark = remark
        else:
            base_eq = EquityBaseInfo(
                type=EquityType.AIRDROP,
                creator=creator,
                remark=remark,
                cost_asset=airdrop_asset,
                cost_amount=airdrop_amount,
                extra_data=extra_data,
            )
        db.session.add(base_eq)
        db.session.commit()
        return base_eq.id

    # #######################################################################################

    @classmethod
    def _create_user_airdrop_equity(cls, eq_base_info: EquityBaseInfo, user_eq: UserEquity) -> UserAirdropEquity:
        air_status = UserAirdropEquity.Status.CREATED if user_eq.status == UserEquity.Status.CREATED else UserAirdropEquity.Status.FAILED
        air_equity = UserAirdropEquity(
            user_id=user_eq.user_id,
            user_equity_id=user_eq.id,
            status=air_status,
            airdrop_asset=eq_base_info.cost_asset,
            airdrop_amount=eq_base_info.cost_amount,
            value_asset=eq_base_info.extra_data["value_asset"],
            value_amount=0,
        )
        return air_equity

    @classmethod
    def _create_user_cashback_equity(cls, eq_base_info: EquityBaseInfo, user_eq: UserEquity) -> UserCashbackEquity:
        start_time = user_eq.created_at
        end_time = start_time + timedelta(days=eq_base_info.extra_data["effective_days"])
        if user_eq.status == UserEquity.Status.CREATED:
            user_eq.status = UserEquity.Status.USING
            eq_status = UserCashbackEquity.Status.USING
        else:
            eq_status = UserCashbackEquity.Status.FAILED
        cb_equity = UserCashbackEquity(
            user_id=user_eq.user_id,
            user_equity_id=user_eq.id,
            start_time=start_time,
            end_time=end_time,
            status=eq_status,
            cost_asset=eq_base_info.cost_asset,
            cost_amount=eq_base_info.cost_amount,
            cashback_scope=UserCashbackEquity.CashbackScope[eq_base_info.extra_data["cashback_scope"]],
            cashback_asset=eq_base_info.extra_data["cashback_asset"],
        )
        return cb_equity

    @classmethod
    def get_create_specific_equity_func(cls, eq_type: EquityType) -> Callable:
        equity_type_specific_func_map = {
            EquityType.AIRDROP: cls._create_user_airdrop_equity,
            EquityType.CASHBACK: cls._create_user_cashback_equity,
        }
        return equity_type_specific_func_map[eq_type]

    @classmethod
    def batch_get_or_create_user_equity(cls, params: list[dict], is_commit: bool) -> dict:
        """批量查询 or 创建多个用户权益
        param keys: biz_id, biz_type, user_id, equity_id, status
        (biz_id, biz_type, user_id) 唯一
        """
        bus_ids = set()
        bus_types = set()
        equity_ids = set()
        user_ids = set()
        for param in params:
            bus_ids.add(param['biz_id'])
            bus_types.add(param['biz_type'])
            equity_ids.add(param['equity_id'])
            user_ids.add(param['user_id'])
            assert param['status'] in [
                UserEquity.Status.CREATED,
                UserEquity.Status.FAILED,
            ]
        if not bus_types:
            return {}

        assert len(bus_types) <= 1  # BusinessType必须相同
        bus_type = list(bus_types)[0]

        batch_size = 5000
        eq_base_info_rows = []
        for ch_eq_ids in batch_iter(equity_ids, batch_size):
            ch_eq_base_info_rows = EquityBaseInfo.query.filter(
                EquityBaseInfo.id.in_(ch_eq_ids),
            ).all()
            eq_base_info_rows.extend(ch_eq_base_info_rows)
        eq_base_info_map: dict[int, EquityBaseInfo] = {i.id: i for i in eq_base_info_rows}

        user_eq_rows = []
        for ch_bus_ids in batch_iter(bus_ids, batch_size):
            ch_user_eq_rows: list[UserEquity] = UserEquity.query.filter(
                UserEquity.business_id.in_(ch_bus_ids),
                UserEquity.business_type == bus_type,
                UserEquity.user_id.in_(user_ids),
            ).all()
            user_eq_rows.extend(ch_user_eq_rows)
        bus_id_type_user_eq_map = {}
        for u_eq in user_eq_rows:
            bus_id_type_user_eq_map[(u_eq.business_id, u_eq.business_type, u_eq.user_id)] = u_eq

        cashback_eq_user_ids = set()
        result_map = {}
        for param in params:
            user_id = param['user_id']
            equity_id = param['equity_id']
            status = param['status']
            eq_base_info = eq_base_info_map[equity_id]

            biz_id = param['biz_id']
            biz_type = param['biz_type']
            key_ = (biz_id, biz_type, user_id)
            if key_ in bus_id_type_user_eq_map:
                result_map[key_] = bus_id_type_user_eq_map[key_]
                continue

            user_eq = UserEquity(
                user_id=user_id,
                equity_id=equity_id,
                type=eq_base_info.type,
                business_id=biz_id,
                business_type=biz_type,
                status=status,
            )
            db.session.add(user_eq)
            db.session.flush()

            new_specific_func = cls.get_create_specific_equity_func(eq_base_info.type)
            user_specific_eq = new_specific_func(eq_base_info, user_eq)
            db.session.add(user_specific_eq)
            result_map[key_] = user_eq
            if eq_base_info.type == EquityType.CASHBACK and status == UserEquity.Status.CREATED:
                cashback_eq_user_ids.add(user_id)

        # 增加返佣标签
        for ch_eq_user_ids in batch_iter(cashback_eq_user_ids, batch_size):
            UserBizTag.batch_add_user_tag(
                biz_tag=UserBizTag.BizTag.EE_NOT_REFERRAL,
                source=UserBizTag.Source.CASHBACK_EQUITY,
                user_ids=set(ch_eq_user_ids),
            )

        if is_commit:
            db.session.commit()
        return result_map

    @classmethod
    def batch_query_user_eq_info(cls, biz_type: UserEquity.BusinessType, biz_ids: set[int]) -> dict:
        """ biz_ids + biz_type 需要唯一，目前仅MISSION使用 """
        user_eq_rows = UserEquity.query.filter(
            UserEquity.business_id.in_(biz_ids),
            UserEquity.business_type == biz_type,
        ).with_entities(
            UserEquity.id,
            UserEquity.business_id,
            UserEquity.type,
            UserEquity.status,
            UserEquity.created_at,
        ).all()
        air_user_eq_ids = [i.id for i in user_eq_rows if i.type == EquityType.AIRDROP]
        air_eq_row_map = {}
        if air_user_eq_ids:
            air_eq_rows = UserAirdropEquity.query.filter(
                UserAirdropEquity.user_equity_id.in_(air_user_eq_ids),
            ).with_entities(
                UserAirdropEquity.user_equity_id,
                UserAirdropEquity.airdrop_asset,
                UserAirdropEquity.airdrop_amount,
            ).all()
            air_eq_row_map = {i.user_equity_id: i for i in air_eq_rows}
        cb_user_eq_ids = [i.id for i in user_eq_rows if i.type == EquityType.CASHBACK]
        cb_eq_row_map = {}
        if cb_user_eq_ids:
            cb_eq_rows = UserCashbackEquity.query.filter(
                UserCashbackEquity.user_equity_id.in_(cb_user_eq_ids),
            ).with_entities(
                UserCashbackEquity.user_equity_id,
                UserCashbackEquity.cost_asset,
                UserCashbackEquity.cost_amount,
                UserCashbackEquity.cashback_amount,
                UserCashbackEquity.used_cost_amount
            ).all()
            cb_eq_row_map = {i.user_equity_id: i for i in cb_eq_rows}

        result = dict()
        for eq_row in user_eq_rows:
            if eq_row.type == EquityType.AIRDROP:
                eq_detail: UserAirdropEquity = air_eq_row_map[eq_row.id]
                cost_asset = eq_detail.airdrop_asset
                cost_amount = eq_detail.airdrop_amount
                real_amount = Decimal() if eq_row.status in cls.FAILED_STATUS \
                    else eq_detail.airdrop_amount
            elif eq_row.type == EquityType.CASHBACK:
                eq_detail: UserCashbackEquity = cb_eq_row_map[eq_row.id]
                cost_asset = eq_detail.cost_asset
                cost_amount = eq_detail.cost_amount
                real_amount = eq_detail.used_cost_amount
            else:
                continue
            result[eq_row.business_id] = {
                "type": eq_row.type,
                "status": eq_row.status,
                "cost_asset": cost_asset,
                "cost_amount": cost_amount,
                "real_amount": real_amount,
                "created_at": eq_row.created_at,
            }
        return result

    @classmethod
    def async_send_airdrop_equity(cls):
        from app.business.equity_center.airdrop import send_user_airdrop_equity_task

        send_user_airdrop_equity_task.delay()

    @classmethod
    def format_equity_base_info(cls, row: EquityBaseInfo) -> str:
        cashback_scope_str = UserCashbackEquity.CashbackScope[row.extra_data["cashback_scope"]].value
        s = f'{amount_to_str(row.cost_amount)} {row.cost_asset} {cashback_scope_str}({row.id})'
        return s

    @classmethod
    def get_all_equity_dict(cls) -> dict[int, str]:
        model = EquityBaseInfo
        rows = model.query.filter(
            model.type == EquityType.CASHBACK,
            model.status == model.Status.OPEN
        ).with_entities(
            model.id,
            model.cost_asset,
            model.cost_amount,
            model.extra_data,
        ).all()
        all_equity_dict = {}
        for row in rows:
            all_equity_dict.update({row.id: cls.format_equity_base_info(row)})
        return all_equity_dict

    @classmethod
    def check_biz_equity_finished(cls, biz_type: UserEquity.BusinessType, biz_ids: set[int]) -> bool:
        """检查业务权益是否完成，biz_ids + biz_type 需要唯一，目前仅MISSION使用"""
        user_eq_rows = UserEquity.query.filter(
            UserEquity.business_id.in_(biz_ids),
            UserEquity.business_type == biz_type,
        ).with_entities(
            UserEquity.id,
            UserEquity.status,
        ).all()
        if not user_eq_rows:
            return True
        status_set = {i.status for i in user_eq_rows}
        return not bool(status_set - {UserEquity.Status.FINISHED, UserEquity.Status.FAILED, UserEquity.Status.EXPIRED})

    @classmethod
    def query_biz_id_by_biz_type_status(cls, biz_type: UserEquity.BusinessType, equity_status: UserEquity.Status) -> set[int]:
        """ business_id + biz_type 需要唯一，目前仅MISSION使用 """
        return {
            ue.business_id for ue in UserEquity.query.filter(
                UserEquity.business_type == biz_type,
                UserEquity.status == equity_status
            ).with_entities(
                UserEquity.business_id
            ).all()
        }
