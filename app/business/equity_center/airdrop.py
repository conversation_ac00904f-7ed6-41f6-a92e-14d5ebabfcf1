# -*- coding: utf-8 -*-
from decimal import Decimal
from datetime import timedelta

from flask import current_app

from app.models.equity_center import (
    db,
    UserEquity,
    UserAirdropEquity,
    UserAirdropEquityHistory,
)
from app import config
from app.common import CeleryQueues
from app.business import <PERSON>acheLock, LockKeys, ServerClient, BalanceBusiness, SPOT_ACCOUNT_ID, lock_call, PriceManager
from app.business.equity_center.helper import EquitySettings
from app.business.utils import yield_query_records_by_time_range
from app.utils import now, quantize_amount, route_module_to_celery_queue, celery_task


route_module_to_celery_queue(__name__, CeleryQueues.REWARD_CENTER)


class AirdropEquityHelper:

    @classmethod
    def finish_one_equity(cls, user_equity_id: int, is_commit: bool):
        user_eq: UserEquity = UserEquity.query.get(user_equity_id)
        assert user_eq
        air_eq: UserAirdropEquity = UserAirdropEquity.query.filter(
            UserAirdropEquity.user_equity_id == user_equity_id,
        ).first()
        assert air_eq

        air_eq.status = UserAirdropEquity.Status.FINISHED
        user_eq.status = UserEquity.Status.FINISHED
        user_eq.finished_at = now()
        if is_commit:
            db.session.commit()

    @classmethod
    def send_one_airdrop_equity(
        cls,
        air_equity: UserAirdropEquity,
        asset_rates: dict[str, Decimal],
    ) -> UserAirdropEquityHistory:
        """发放一个空投权益"""
        assert air_equity.status == UserAirdropEquity.Status.CREATED

        eq_his: UserAirdropEquityHistory = UserAirdropEquityHistory.query.filter(
            UserAirdropEquityHistory.user_equity_id == air_equity.user_equity_id,
        ).first()
        if not eq_his:
            airdrop_asset = air_equity.airdrop_asset
            airdrop_amount = air_equity.airdrop_amount
            value_asset = air_equity.value_asset
            airdrop_asset_price = asset_rates[airdrop_asset]
            value_asset_price = asset_rates[value_asset]
            value_amount = quantize_amount(airdrop_asset_price * airdrop_amount / value_asset_price, 8)
            save_asset_rates = {
                airdrop_asset: airdrop_asset_price,
                value_asset: value_asset_price,
            }
            eq_his = UserAirdropEquityHistory(
                user_id=air_equity.user_id,
                user_equity_id=air_equity.user_equity_id,
                airdrop_asset=airdrop_asset,
                airdrop_amount=airdrop_amount,
                value_asset=value_asset,
                value_amount=value_amount,
                asset_rates=save_asset_rates,
            )
            db.session.add(eq_his)
            air_equity.value_amount += value_amount
            db.session.commit()

        if eq_his.status in [
            UserAirdropEquityHistory.Status.CREATED,
            UserAirdropEquityHistory.Status.DEDUCTED,
        ]:
            cls.transfer_by_equity_history(eq_his)
        if eq_his.status == UserAirdropEquityHistory.Status.FINISHED:
            cls.finish_one_equity(air_equity.user_equity_id, is_commit=True)
        return eq_his

    @classmethod
    def transfer_by_equity_history(cls, row: UserAirdropEquityHistory):
        amount_ = row.airdrop_amount
        assert amount_ > 0
        asset_ = row.airdrop_asset
        from_user_id = config["EQUITY_CENTER_ADMIN_USER_ID"]
        to_user_id = row.user_id
        from_business = to_business = BalanceBusiness.EQUITY_AIRDROP
        bus_id = row.id

        client = ServerClient()
        remark = f"airdrop_equity_his for {row.id}"
        if row.status == UserAirdropEquityHistory.Status.CREATED:
            if amount_ > Decimal():
                try:
                    result = client.add_user_balance(
                        user_id=from_user_id,
                        asset=asset_,
                        amount=str(-amount_),
                        business=from_business,
                        business_id=bus_id,
                        detail={"remark": remark},
                        account_id=SPOT_ACCOUNT_ID,
                    )
                    if not result:
                        current_app.logger.error(
                            f"equity_airdrop_transfer_by_equity_history {row.id} {asset_} {amount_} "
                            f"from {from_user_id} to {to_user_id} deduct DUPLICATE_BALANCE_UPDATE"
                        )
                except Exception as e:
                    current_app.logger.error(
                        f"equity_airdrop_transfer_by_equity_history {row.id} {asset_} {amount_} "
                        f"from {from_user_id} to {to_user_id} failed {e!r}"
                    )
                    # 不处理余额不足的情况
                    raise
            row.status = UserAirdropEquityHistory.Status.DEDUCTED
            row.deducted_at = now()
            db.session.commit()

        if row.status == UserAirdropEquityHistory.Status.DEDUCTED:
            if amount_ > Decimal():
                result = client.add_user_balance(
                    user_id=to_user_id,
                    asset=asset_,
                    amount=str(amount_),
                    business=to_business,
                    business_id=bus_id,
                    detail={"remark": remark},
                    account_id=SPOT_ACCOUNT_ID,
                )
                if not result:
                    current_app.logger.error(
                        f"equity_airdrop_transfer_by_equity_history {row.id} {asset_} {amount_} "
                        f"from {from_user_id} to {to_user_id} deduct DUPLICATE_BALANCE_UPDATE"
                    )
            row.status = UserAirdropEquityHistory.Status.FINISHED
            row.finished_at = now()
            db.session.commit()


@celery_task
@lock_call()
def send_user_airdrop_equity_task():
    """ 空投权益-资产发放 """
    if not EquitySettings.cet_transfer_enabled:
        return

    now_ = now()
    start_dt = now_ - timedelta(hours=24)
    end_dt = now_
    pending_statues = [UserAirdropEquity.Status.CREATED]

    asset_rates = PriceManager.assets_to_usd()
    for air_eq_r in yield_query_records_by_time_range(
        table=UserAirdropEquity,
        start_time=start_dt,
        end_time=end_dt,
        select_fields=[
            UserAirdropEquity.id,
            UserAirdropEquity.user_id,
            UserAirdropEquity.status,
        ],
        limit=5000,
    ):
        if air_eq_r.status not in pending_statues:
            continue

        try:
            with CacheLock(key=LockKeys.airdrop_equity_send(air_eq_r.id)):
                db.session.rollback()
                air_eq_row = UserAirdropEquity.query.get(air_eq_r.id)
                AirdropEquityHelper.send_one_airdrop_equity(air_eq_row, asset_rates)
        except Exception as _e:
            db.session.rollback()
            current_app.logger.exception(f"send_one_airdrop_equity {air_eq_r.id} error: {_e!r}")
