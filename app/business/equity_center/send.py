# -*- coding: utf-8 -*-
from flask import current_app
import random

from app.models.equity_center import db, EquityBaseInfo, EquitySendApply
from app.business import lock_call
from app.business.user import UserRepository
from app.business.equity_center.helper import EquityCenterService
from app.business.equity_center.notice import send_equity_delivery_notice
from app.utils import batch_iter, now, celery_task


@celery_task
@lock_call(with_args=True)
def create_user_equity_by_send_apply(apply_id: int):
    """ 通过发放申请批量创建用户权益 """
    from app.business.push_statistic import EquitySendApplyUserParser

    apply: EquitySendApply = EquitySendApply.query.get(apply_id)
    if not apply:
        return
    if apply.status not in [
        EquitySendApply.Status.PASSED,
        EquitySendApply.Status.SENDING,
    ]:
        return

    if apply.status == EquitySendApply.Status.PASSED:
        # 开始发放
        apply.status = EquitySendApply.Status.SENDING  # 也会重试
        db.session.commit()

    eq_info: EquityBaseInfo = EquityBaseInfo.query.get(apply.equity_id)
    if not eq_info or not eq_info.is_open:
        return

    abnormal_users = UserRepository.get_abnormal_users()
    # group_user_ids = apply.cached_group_user_ids
    group_user_ids, _ = EquitySendApplyUserParser(apply).parse()  # 按照实际发放时间确定的圈群用户来发
    already_send_user_ids = apply.get_send_user_ids()
    remain_send_user_ids = group_user_ids - already_send_user_ids - abnormal_users
    remain_send_count = apply.total_send_count - len(already_send_user_ids)
    if remain_send_count > 0:
        if len(remain_send_user_ids) > remain_send_count:
            # 发放数量<圈群人数时，发放用户随机选
            new_send_user_ids = list(random.sample(list(remain_send_user_ids), remain_send_count))
        else:
            new_send_user_ids = list(remain_send_user_ids)
    else:
        new_send_user_ids = []
    assert len(new_send_user_ids) <= remain_send_count

    user_eq_map = {}
    batch_size = 5000
    for ch_user_ids in batch_iter(new_send_user_ids, batch_size):
        equity_params = []
        for user_id in ch_user_ids:
            equity_params.append(
                dict(
                    biz_id=apply_id,
                    biz_type=EquityCenterService.BizTypes.PLATFORM_SEND,
                    user_id=user_id,
                    equity_id=apply.equity_id,
                    status=EquityCenterService.UserEquityStatus.CREATED,
                )
            )
        ch_user_eq_map = EquityCenterService.batch_get_or_create_user_equity(equity_params, is_commit=False)
        user_eq_map.update(ch_user_eq_map)
        apply.update_send_user_ids(set(ch_user_ids))
        db.session.commit()

    # 完成发放
    apply.status = EquitySendApply.Status.FINISHED
    apply.send_finished_at = now()
    db.session.commit()

    # notice
    user_eq_ids = {v.id for v in user_eq_map.values()}
    try:
        send_equity_delivery_notice(eq_info.type, user_eq_ids)
    except Exception as _e:
        db.session.rollback()
        current_app.logger.exception(f"send_equity_delivery_notice {apply.id} error: {_e!r}")
