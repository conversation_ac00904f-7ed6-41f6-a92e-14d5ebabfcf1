# -*- coding: utf-8 -*-
import json
from datetime import timedelta, datetime
from enum import Enum
from typing import Optional

from flask import current_app
from flask_babel import force_locale, gettext

from app.common import (
    CeleryQueues, MessageTitle, MessageContent, MessageWebLink, NoticePushType, WebPushMessageType, WebPushChannelType,
    Language,
)
from app.config import config
from app.models import db, Message
from app.models.equity_center import (
    EquityType,
    UserEquity,
    UserCashbackEquity,
    UserAirdropEquity,
)
from app.business import ServerClient, lock_call, UserPreferences
from app.business.email import send_equity_center_notice_email
from app.business.user import batch_get_user_pref_fields_map
from app.business.equity_center.helper import EquitySettings
from app.business.push import PushBusinessHandler, send_mobile_push_by_user_ids, batch_get_user_push_lang_map
from app.caches.equity_center import EquityNoticeSendCache
from app.utils import now, amount_to_str, batch_iter, celery_task, route_module_to_celery_queue, url_join, datetime_to_str
from app.utils.push import WebPagePath, AppPagePath
from app.utils.parser import JsonEncoder


route_module_to_celery_queue(__name__, CeleryQueues.REWARD_CENTER)


class NoticeScene(Enum):
    # 通知场景
    EXPIRING = "即将过期"
    DELIVERY = "直接发放"


class NoticeType(Enum):
    # 通知方式
    EMAIL = "邮件"
    WEB_PUSH = "Web push"
    PUSH = "App push"
    MESSAGE = "站内信"


class BaseMessageSender:
    _sender_map = {}

    EQUITY_TYPE: EquityType

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        BaseMessageSender._sender_map[cls.EQUITY_TYPE] = cls

    @classmethod
    def get_sender(cls, equity_type: EquityType) -> Optional['BaseMessageSender']:
        return cls._sender_map.get(equity_type)

    @classmethod
    def get_message_link(cls) -> str:
        return MessageWebLink.REWARD_CENTER_PAGE.value + "?type=award"

    @classmethod
    def get_app_push_url(cls) -> str:
        return AppPagePath.REWARD_CENTER.value.format(tab="rewards")

    @classmethod
    def get_web_push_url(cls) -> str:
        return WebPagePath.REWARD_CENTER_PAGE.value

    @classmethod
    def get_email_url(cls) -> str:
        return url_join(config["SITE_URL"], "/reward-center?type=award")

    @classmethod
    def _get_msg_title_content(cls, notice_scene: NoticeScene) -> tuple[MessageTitle, MessageContent]:
        raise NotImplementedError

    @classmethod
    def _build_msg_params(cls, row, notice_scene: NoticeScene, tz_offset: int) -> dict:
        raise NotImplementedError

    @classmethod
    def _build_notice_data(cls, row, notice_scene: NoticeScene, lang: Language, tz_offset: int, now_: datetime):
        """构建所有类型的通知数据"""
        msg_title, msg_content = cls._get_msg_title_content(notice_scene)
        
        msg_params = cls._build_msg_params(row, notice_scene, tz_offset)
        key = (row.user_equity_id, row.user_id)

        msg_data = dict(
            user_id=row.user_id,
            title=msg_title,
            content=msg_content,
            params=json.dumps(msg_params, cls=JsonEncoder),
            extra_info=json.dumps(
                dict(
                    web_link=cls.get_message_link(),
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            expired_at=now_ + timedelta(days=3),
            channel=Message.Channel.ACTIVITY,
        )

        email_data = dict(
            **msg_params,
            site_url=cls.get_email_url(),
        )

        with force_locale(lang.value):
            ap_title = gettext(msg_title.value)
            ap_content = gettext(msg_content.value, **msg_params)
        app_push_data = dict(
            title=ap_title,
            content=ap_content,
            ttl=0,
            url=cls.get_app_push_url(),
            created_at=int(now_.timestamp()),
        )

        web_push_data = dict(
            title=ap_title,
            content=ap_content,
            url=cls.get_web_push_url(),
            type=NoticePushType.SUCCESS,
            timing_type=f'{cls.EQUITY_TYPE.name}_{notice_scene.name}'.lower(),
            msg_type=WebPushMessageType.REWARD_CENTER.value,
            data={},
        )

        return key, msg_data, email_data, app_push_data, web_push_data

    @classmethod
    def batch_send_email(cls, notice_scene: NoticeScene, email_template_name: str, user_eq_id_data_map: dict[tuple[int, int], dict]):
        cache = EquityNoticeSendCache(notice_scene=notice_scene.name, notice_type=NoticeType.EMAIL.name)
        sent_user_eq_ids = cache.get_sent_user_equity_ids()
        new_sent_user_eq_ids = set()
        for (user_eq_id, user_id), data in user_eq_id_data_map.items():
            if user_eq_id in sent_user_eq_ids:
                continue
            send_equity_center_notice_email.delay(user_id, email_template_name, data)
            new_sent_user_eq_ids.add(user_eq_id)
        if new_sent_user_eq_ids:
            cache.add_user_equity_ids(new_sent_user_eq_ids)

    @classmethod
    def batch_send_web_push(cls, notice_scene: NoticeScene, user_eq_id_data_map: dict[tuple[int, int], dict]):
        cache = EquityNoticeSendCache(notice_scene=notice_scene.name, notice_type=NoticeType.WEB_PUSH.name)
        sent_user_eq_ids = cache.get_sent_user_equity_ids()
        new_sent_user_eq_ids = set()
        client = ServerClient()
        for (user_eq_id, user_id), data in user_eq_id_data_map.items():
            if user_eq_id in sent_user_eq_ids:
                continue
            try:
                client.notice_user_message(user_id, WebPushChannelType.REWARD_CENTER.value, data)
                new_sent_user_eq_ids.add(user_eq_id)
            except Exception as _e:
                current_app.logger.error(f"EquityMessageSender batch_send_web_push {user_id} {user_eq_id} error: {_e!r}")
        if new_sent_user_eq_ids:
            cache.add_user_equity_ids(new_sent_user_eq_ids)

    @classmethod
    def batch_send_app_push(cls, notice_scene: NoticeScene, user_eq_id_data_map: dict[tuple[int, int], dict]):
        cache = EquityNoticeSendCache(notice_scene=notice_scene.name, notice_type=NoticeType.PUSH.name)
        sent_user_eq_ids = cache.get_sent_user_equity_ids()
        
        user_ids = [k[1] for k in user_eq_id_data_map.keys() if k[0] not in sent_user_eq_ids]
        handler = PushBusinessHandler(None)
        can_push_user_ids = handler.can_push(user_ids)

        new_sent_user_ids = set()
        new_sent_user_eq_ids = set()
        for (user_eq_id, user_id), data in user_eq_id_data_map.items():
            if user_eq_id in sent_user_eq_ids:
                continue
            if user_id not in can_push_user_ids:
                continue

            send_mobile_push_by_user_ids.delay(
                user_ids=[user_id],
                **data,
            )
            new_sent_user_ids.add(user_id)
            new_sent_user_eq_ids.add(user_eq_id)

        if new_sent_user_ids:
            handler.set_pushed(new_sent_user_ids)
        if new_sent_user_eq_ids:
            cache.add_user_equity_ids(new_sent_user_eq_ids)

    @classmethod
    def batch_send_message(cls, notice_scene: NoticeScene, user_eq_id_data_map: dict[tuple[int, int], dict]):
        cache = EquityNoticeSendCache(notice_scene=notice_scene.name, notice_type=NoticeType.MESSAGE.name)
        sent_user_eq_ids = cache.get_sent_user_equity_ids()
        new_sent_user_eq_ids = set()
        messages = []
        for (user_eq_id, user_id), data in user_eq_id_data_map.items():
            if user_eq_id in sent_user_eq_ids:
                continue
            message = Message(**data)
            messages.append(message)
            new_sent_user_eq_ids.add(user_eq_id)
        for rows in batch_iter(messages, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()

        if new_sent_user_eq_ids:
            cache.add_user_equity_ids(new_sent_user_eq_ids)

    @classmethod
    def _send_notices(cls, notice_scene: NoticeScene, eq_rows: list[UserCashbackEquity]):
        """发送所有类型的通知"""
        now_ = now()
        user_ids = {i.user_id for i in eq_rows}
        user_lang_map = batch_get_user_push_lang_map(user_ids, is_app=True)
        pref_fields = [
            UserPreferences.timezone_offset.name,  # noqa
        ]
        user_pref_map = batch_get_user_pref_fields_map(user_ids, pref_fields)

        user_eq_msg_data_map = {}
        user_eq_email_data_map = {}
        user_eq_app_push_data_map = {}
        user_eq_web_push_data_map = {}

        for r in eq_rows:
            lang = user_lang_map[r.user_id]
            tz_offset = user_pref_map[r.user_id][UserPreferences.timezone_offset.name]  # noqa
            
            key, msg_data, email_data, app_push_data, web_push_data = cls._build_notice_data(
                r, notice_scene, lang, tz_offset, now_
            )
            user_eq_msg_data_map[key] = msg_data
            user_eq_email_data_map[key] = email_data
            user_eq_app_push_data_map[key] = app_push_data
            user_eq_web_push_data_map[key] = web_push_data

        if user_eq_msg_data_map:
            cls.batch_send_message(notice_scene, user_eq_msg_data_map)
        if user_eq_email_data_map:
            email_template_name = f"{cls.EQUITY_TYPE.name.lower()}_{notice_scene.name.lower()}"
            cls.batch_send_email(notice_scene, email_template_name, user_eq_email_data_map)
        if user_eq_app_push_data_map:
            cls.batch_send_app_push(notice_scene, user_eq_app_push_data_map)
        if user_eq_web_push_data_map:
            cls.batch_send_web_push(notice_scene, user_eq_web_push_data_map)

    @classmethod
    def send_delivery_notices(cls, user_eq_ids: set[int]):
        pass


class CashbackMessageSender(BaseMessageSender):
    EQUITY_TYPE = EquityType.CASHBACK

    @classmethod
    def _get_msg_title_content(cls, notice_scene: NoticeScene) -> tuple[MessageTitle, MessageContent]:
        scene_title_content_map = {
            NoticeScene.EXPIRING: (MessageTitle.CASHBACK_EQUITY_EXPIRING, MessageContent.CASHBACK_EQUITY_EXPIRING),
            NoticeScene.DELIVERY: (MessageTitle.CASHBACK_EQUITY_DELIVERY, MessageContent.CASHBACK_EQUITY_DELIVERY),
        }
        title_content = scene_title_content_map.get(notice_scene)
        if not title_content:
            raise ValueError(f"Unsupported notice scene: {notice_scene}")
        return title_content

    @classmethod
    def _build_msg_params(cls, row, notice_scene: NoticeScene, tz_offset: int) -> dict:
        return {
            "asset": row.cost_asset,
            "amount": amount_to_str(row.cost_amount),
            "expired_at": datetime_to_str(row.end_time, tz_offset),
        }

    @classmethod
    def send_expiring_notices(cls):
        now_ = now()
        notice_scene = NoticeScene.EXPIRING

        hours = EquitySettings.cashback_expired_notice_hours
        eq_rows = UserCashbackEquity.query.filter(
            UserCashbackEquity.status == UserCashbackEquity.Status.USING,
            UserCashbackEquity.end_time >= now_,
            UserCashbackEquity.end_time <= now_ + timedelta(hours=hours),
        ).with_entities(
            UserCashbackEquity.user_equity_id,
            UserCashbackEquity.user_id,
            UserCashbackEquity.cost_asset,
            UserCashbackEquity.cost_amount,
            UserCashbackEquity.end_time,
        ).all()

        cls._send_notices(notice_scene, eq_rows)

    @classmethod
    def send_delivery_notices(cls, user_eq_ids: set[int]):
        notice_scene = NoticeScene.DELIVERY

        eq_rows = []
        for ch_user_eq_ids in batch_iter(user_eq_ids, 5000):
            ch_eq_rows = UserCashbackEquity.query.filter(
                UserCashbackEquity.user_equity_id.in_(ch_user_eq_ids),
            ).with_entities(
                UserCashbackEquity.user_equity_id,
                UserCashbackEquity.user_id,
                UserCashbackEquity.cost_asset,
                UserCashbackEquity.cost_amount,
                UserCashbackEquity.end_time,
            ).all()
            eq_rows.extend(ch_eq_rows)

        cls._send_notices(notice_scene, eq_rows)


class AirdropMessageSender(BaseMessageSender):
    EQUITY_TYPE = EquityType.AIRDROP

    @classmethod
    def _get_msg_title_content(cls, notice_scene: NoticeScene) -> tuple[MessageTitle, MessageContent]:
        scene_title_content_map = {
            NoticeScene.DELIVERY: (MessageTitle.AIRDROP_EQUITY_DELIVERY, MessageContent.AIRDROP_EQUITY_DELIVERY),
        }
        title_content = scene_title_content_map.get(notice_scene)
        if not title_content:
            raise ValueError(f"Unsupported notice scene: {notice_scene}")
        return title_content

    @classmethod
    def _build_msg_params(cls, row, notice_scene: NoticeScene, tz_offset: int) -> dict:
        return {
            "asset": row.airdrop_asset,
            "amount": amount_to_str(row.airdrop_amount),
        }

    @classmethod
    def send_delivery_notices(cls, user_eq_ids: set[int]):
        notice_scene = NoticeScene.DELIVERY

        eq_rows = []
        for ch_user_eq_ids in batch_iter(user_eq_ids, 5000):
            ch_eq_rows = UserAirdropEquity.query.filter(
                UserAirdropEquity.user_equity_id.in_(ch_user_eq_ids),
            ).with_entities(
                UserAirdropEquity.user_equity_id,
                UserAirdropEquity.user_id,
                UserAirdropEquity.airdrop_asset,
                UserAirdropEquity.airdrop_amount,
            ).all()
            eq_rows.extend(ch_eq_rows)

        cls._send_notices(notice_scene, eq_rows)


@celery_task
@lock_call()
def clean_up_equity_notice_send_cache_task():
    """ 清理通知缓存的数据key """
    all_user_eq_ids = set()
    cache_eq_ids_list = []
    for notice_scene in NoticeScene:
        for notice_type in NoticeType:
            cache = EquityNoticeSendCache(notice_scene=notice_scene.name, notice_type=notice_type.name)
            sent_user_eq_ids = cache.get_sent_user_equity_ids()
            all_user_eq_ids.update(sent_user_eq_ids)
            cache_eq_ids_list.append([cache, sent_user_eq_ids])

    can_del_eq_ids = set()
    for ch_ids in batch_iter(all_user_eq_ids, 5000):
        ch_eq_rows = UserEquity.query.filter(
            UserEquity.id.in_(ch_ids),
            UserEquity.status == UserEquity.Status.FINISHED,
        ).with_entities(
            UserEquity.id,
        ).all()
        can_del_eq_ids.update({i.id for i in ch_eq_rows})

    for cache, c_sent_user_eq_ids in cache_eq_ids_list:
        cache: EquityNoticeSendCache
        _del_eq_ids: set = can_del_eq_ids & c_sent_user_eq_ids
        if _del_eq_ids:
            cache.del_user_equity_ids(_del_eq_ids)


@celery_task
@lock_call()
def send_equity_expiring_notice_task():
    CashbackMessageSender.send_expiring_notices()


def send_equity_delivery_notice(equity_type: EquityType, user_eq_ids: set[int]):
    """ 权益直接发放触达 """
    sender_cls = BaseMessageSender.get_sender(equity_type)
    if sender_cls:
        sender_cls.send_delivery_notices(user_eq_ids)
