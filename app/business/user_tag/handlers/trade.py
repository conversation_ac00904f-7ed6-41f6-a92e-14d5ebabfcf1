import json
import math
from collections import defaultdict, Counter
from datetime import date, timed<PERSON>ta
from decimal import Decimal
from functools import partial
from typing import Optional, List

from flask import current_app
from pyroaring import BitMap
from sqlalchemy import func

from app import config
from app.business import PerpetualLogDB, PerpetualSysHistoryDB, PerpetualHistoryDB
from app.business.market_maker import MarketMakerHelper
from app.business.user_tag import TagReader
from app.business.user_tag.handlers.base import (
    <PERSON><PERSON><PERSON><PERSON>,
    AggregateHandler, TagRelation, SimpleTagSaveMixin,
)
from app.business.user_tag.helper import (
    data_dumps, data_loads,
    get_sub_account_mapping, get_disabled_user_ids, yield_get_user_first_data,
    yield_get_user_latest_data,
)

from app.business.utils import query_records_by_time_range, yield_query_records_by_time_range
from app.caches import PerpetualMarketCache, PerpetualOfflineMarketCache
from app.common import PrecisionEnum, TradeBusinessType, P2pBusinessType
from app.models import User<PERSON>rade<PERSON>ummary, Asset<PERSON><PERSON>, db, UserExchangeSummary, P2pOrder
from app.models.base import read_only_session
from app.models.copy_trading import CopyFollowerHistory
from app.models.exchange import AssetExchangeOrder
from app.models.pledge import PledgePosition, PledgeInterestHistory, PledgeLoanHistory
from app.models.user_tag import (
    UserTag, TagRequirement, UserTagRequirement, TagRawValueType, is_filter_user,
)
from app.utils import batch_iter, quantize_amount, amount_to_str, today, RESTClient
from app.utils.date_ import date_to_datetime, datetime_to_time, timestamp_to_date


class FirstDealTagHandler(SimpleTagSaveMixin, AggregateHandler):

    read_slot = frozenset([16])
    write_slot = frozenset([16])

    impl_tags = {
        UserTag.FIRST_TRADE_TIME,
        UserTag.FIRST_SPOT_TRADE_TIME,
        UserTag.FIRST_PERPETUAL_TRADE_TIME,
        UserTag.LATEST_TRADE_TIME,
        UserTag.LATEST_SPOT_TRADE_TIME,
        UserTag.LATEST_PERPETUAL_TRADE_TIME,
        UserTag.FIRST_SPOT_TRADE_USD,
        UserTag.FIRST_PERPETUAL_TRADE_USD,
        UserTag.LATEST_SPOT_TRADE_USD,
        UserTag.LATEST_PERPETUAL_TRADE_USD,
        UserTag.FIRST_MARGIN_TRADE_TIME,
        UserTag.LATEST_MARGIN_TRADE_TIME,
        UserTag.FIRST_ONLY_SPOT_TRADE_TIME,
        UserTag.LATEST_ONLY_SPOT_TRADE_TIME,
        UserTag.FIRST_ONLY_SPOT_TRADE_USD,
        UserTag.LATEST_ONLY_SPOT_TRADE_USD,
    }

    tag_relations = [
        TagRelation(
            tags={
                UserTag.LATEST_PERPETUAL_TRADE_USD,
                UserTag.FIRST_MARGIN_TRADE_TIME,
                UserTag.LATEST_MARGIN_TRADE_TIME,
                UserTag.FIRST_ONLY_SPOT_TRADE_TIME,
                UserTag.LATEST_ONLY_SPOT_TRADE_TIME,
                UserTag.FIRST_ONLY_SPOT_TRADE_USD,
                UserTag.LATEST_ONLY_SPOT_TRADE_USD,
            },
            requires={
                TagRequirement.MARGIN_MARKET_TRADE_VOL,
                TagRequirement.ONLY_SPOT_MARKET_TRADE_VOL,
            }
        ),
    ]

    def flush(self, report_date: Optional[date]):
        first_exchange_func = partial(
            yield_get_user_first_data,
            AssetExchangeOrder,
            [AssetExchangeOrder.user_id,
             AssetExchangeOrder.created_at
             ])
        latest_exchange_func = partial(
            yield_get_user_latest_data,
            AssetExchangeOrder,
            [AssetExchangeOrder.user_id,
             AssetExchangeOrder.created_at])
        first_trade_func = partial(
            yield_get_user_first_data,
            UserTradeSummary,
            [UserTradeSummary.user_id, UserTradeSummary.trade_amount, UserTradeSummary.report_date],
            )
        latest_trade_func = partial(
            yield_get_user_latest_data,
            UserTradeSummary,
            [UserTradeSummary.user_id, UserTradeSummary.trade_amount, UserTradeSummary.report_date],
            )
        first_spot_trade_func = partial(
            yield_get_user_first_data,
            UserTradeSummary,
            [UserTradeSummary.user_id, UserTradeSummary.trade_amount,
             UserTradeSummary.report_date],
            UserTradeSummary.system == UserTradeSummary.System.SPOT)
        first_exchange_trade_func = partial(
            yield_get_user_first_data,
            UserExchangeSummary,
            [UserExchangeSummary.user_id, UserExchangeSummary.trade_amount,
             UserExchangeSummary.report_date])
        latest_spot_trade_func = partial(
            yield_get_user_latest_data,
            UserTradeSummary,
            [UserTradeSummary.user_id, UserTradeSummary.trade_amount,
             UserTradeSummary.report_date],
            UserTradeSummary.system == UserTradeSummary.System.SPOT)
        latest_exchange_trade_func = partial(
            yield_get_user_latest_data,
            UserExchangeSummary,
            [UserExchangeSummary.user_id, UserExchangeSummary.trade_amount,
             UserExchangeSummary.report_date])
        first_perpetual_trade_func = partial(
            yield_get_user_first_data,
            UserTradeSummary,
            [UserTradeSummary.user_id, UserTradeSummary.trade_amount,
             UserTradeSummary.report_date],
            UserTradeSummary.system == UserTradeSummary.System.PERPETUAL)
        latest_perpetual_trade_func = partial(
            yield_get_user_latest_data,
            UserTradeSummary,
            [UserTradeSummary.user_id, UserTradeSummary.trade_amount,
             UserTradeSummary.report_date],
            UserTradeSummary.system == UserTradeSummary.System.PERPETUAL)
        flush_data = {
            _tag: {}
            for _tag in self.impl_tags
        }
        time_tag_mapping = {
            UserTag.FIRST_TRADE_TIME: first_trade_func,
            UserTag.LATEST_TRADE_TIME: latest_trade_func,
            UserTag.FIRST_SPOT_TRADE_TIME: first_spot_trade_func,
            UserTag.LATEST_SPOT_TRADE_TIME: latest_spot_trade_func,
            UserTag.FIRST_PERPETUAL_TRADE_TIME: first_perpetual_trade_func,
            UserTag.LATEST_PERPETUAL_TRADE_TIME: latest_perpetual_trade_func,
        }

        for _tag, _func in time_tag_mapping.items():
            for _record in _func():
                flush_data[_tag][_record["user_id"]] = _record["report_date"]
        for _record in first_exchange_func():
            _tags = [UserTag.FIRST_SPOT_TRADE_TIME, UserTag.FIRST_TRADE_TIME]
            for _tag in _tags:
                if _record["user_id"] in flush_data[_tag]:
                    flush_data[_tag][_record["user_id"]] = min(
                        flush_data[_tag][_record["user_id"]],
                        _record["created_at"].date()
                    )
                else:
                    flush_data[_tag][_record["user_id"]] = _record["created_at"].date()
        for _record in latest_exchange_func():
            _tag = UserTag.LATEST_TRADE_TIME
            _tags = [UserTag.LATEST_SPOT_TRADE_TIME, UserTag.LATEST_TRADE_TIME]
            for _tag in _tags:
                if _record["user_id"] in flush_data[_tag]:
                    flush_data[_tag][_record["user_id"]] = max(
                        flush_data[_tag][_record["user_id"]],
                        _record["created_at"].date()
                    )
                else:
                    flush_data[_tag][_record["user_id"]] = _record["created_at"].date()

        perpetual_trade_tag_mapping = {
            UserTag.FIRST_PERPETUAL_TRADE_USD: first_perpetual_trade_func,
            UserTag.LATEST_PERPETUAL_TRADE_USD: latest_perpetual_trade_func
        }

        for _tag, _func in perpetual_trade_tag_mapping.items():
            for _record in _func():
                flush_data[_tag][_record["user_id"]] = _record["trade_amount"]
        spot_trade_tag_mapping = {
            UserTag.FIRST_SPOT_TRADE_USD: [first_spot_trade_func, first_exchange_trade_func],
            UserTag.LATEST_SPOT_TRADE_USD: [latest_spot_trade_func, latest_exchange_trade_func],
        }

        def get_trade_usd(__data, latest: bool = False):
            if latest:
                _date = max([v[0] for v in __data])
            else:
                _date = min([v[0] for v in __data])
            return sum([v[1] for v in __data if v[0] == _date])

        for _tag, _func_list in spot_trade_tag_mapping.items():
            origin_data = defaultdict(list)
            for _func in _func_list:
                for _record in _func():
                    origin_data[_record["user_id"]].append([_record["report_date"], _record["trade_amount"]])
            match _tag:
                case UserTag.FIRST_SPOT_TRADE_USD:
                    for _uid, _data in origin_data.items():
                        flush_data[_tag][_uid] = get_trade_usd(_data, False)
                case UserTag.LATEST_SPOT_TRADE_USD:
                    for _uid, _data in origin_data.items():
                        flush_data[_tag][_uid] = get_trade_usd(_data, True)

        self._increase_update_first_or_last_data(report_date, flush_data)
        # convert tag enum to str
        convert_data = {
            _tag.name: [(_user_id, data_dumps(_value)) for _user_id, _value in _all_tag_data.items()]
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data

    def _increase_update_first_or_last_data(self, report_date, flush_data):
        _query_date = report_date - timedelta(days=1)  # 这里因为 UserTagRequirement 更新日期减 1 了
        reqs_data = UserTagRequirement.query.filter(
            UserTagRequirement.tag.in_(
                [
                    TagRequirement.MARGIN_MARKET_TRADE_VOL.name,
                    TagRequirement.ONLY_SPOT_MARKET_TRADE_VOL.name,
                ]
            ),
            UserTagRequirement.report_date == _query_date
        ).all()
        tag_data = TagReader.get_all_tag_data([
            UserTag.FIRST_MARGIN_TRADE_TIME,
            UserTag.LATEST_MARGIN_TRADE_TIME,
            UserTag.FIRST_ONLY_SPOT_TRADE_TIME,
            UserTag.LATEST_ONLY_SPOT_TRADE_TIME,
            UserTag.FIRST_ONLY_SPOT_TRADE_USD,
            UserTag.LATEST_ONLY_SPOT_TRADE_USD,
        ])
        for tag, user_data in tag_data.items():  # init data
            for uid, value in user_data.items():
                if tag in [
                    UserTag.FIRST_MARGIN_TRADE_TIME,
                    UserTag.LATEST_MARGIN_TRADE_TIME,
                    UserTag.FIRST_ONLY_SPOT_TRADE_TIME,
                    UserTag.LATEST_ONLY_SPOT_TRADE_TIME,
                ]:
                    flush_data[tag][uid] = timestamp_to_date(value)
                else:
                    flush_data[tag][uid] = value
        for req_data in reqs_data:
            req_data: UserTagRequirement
            user_id = req_data.user_id
            if req_data.tag == TagRequirement.MARGIN_MARKET_TRADE_VOL.name:
                if not flush_data[UserTag.FIRST_MARGIN_TRADE_TIME].get(user_id):
                    flush_data[UserTag.FIRST_MARGIN_TRADE_TIME][user_id] = req_data.report_date
                flush_data[UserTag.LATEST_MARGIN_TRADE_TIME][user_id] = req_data.report_date
            elif req_data.tag == TagRequirement.ONLY_SPOT_MARKET_TRADE_VOL.name:
                if not flush_data[UserTag.FIRST_ONLY_SPOT_TRADE_TIME].get(user_id):
                    flush_data[UserTag.FIRST_ONLY_SPOT_TRADE_TIME][user_id] = req_data.report_date
                if not flush_data[UserTag.FIRST_ONLY_SPOT_TRADE_USD].get(user_id):
                    flush_data[UserTag.FIRST_ONLY_SPOT_TRADE_USD][user_id] = Decimal(req_data.value)
                flush_data[UserTag.LATEST_ONLY_SPOT_TRADE_TIME][user_id] = req_data.report_date
                flush_data[UserTag.LATEST_ONLY_SPOT_TRADE_USD][user_id] = Decimal(req_data.value)


class PerpetualPositionHandler(SimpleTagSaveMixin, TagHandler):

    read_slot = frozenset([2])
    write_slot = frozenset([2])

    impl_tags = {
        UserTag.LATEST_PERPETUAL_POSITION_MARKETS,
    }

    def flush(self, report_date: Optional[date]):
        if not report_date:
            current_app.logger.error(f"report_date {report_date} is not allowed be none")
            return
        ts = int(date_to_datetime(report_date).timestamp())
        slice_position_table = PerpetualLogDB.slice_position_table(ts)
        if not slice_position_table:
            current_app.logger.error(f"slice position table {ts} is not ready")
            return
        records = slice_position_table.select(
            "user_id", "market"
        )
        result = defaultdict(set)
        sub_user_mapping = get_sub_account_mapping()
        for record in records:
            user_id, market = record
            user_id = sub_user_mapping.get(user_id, user_id)
            result[user_id].add(market)
        flush_data = {
            UserTag.LATEST_PERPETUAL_POSITION_MARKETS: result
        }
        # convert tag enum to str
        convert_data = {
            _tag.name: {_user_id: data_dumps(_value) for _user_id, _value in _all_tag_data.items()}
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data


class TradeMarketAssetsHandler(SimpleTagSaveMixin, AggregateHandler):

    read_slot = frozenset([13])
    write_slot = frozenset([13])

    impl_tags = {
        UserTag.FIRST_TRADE_ASSETS,
        # UserTag.LATEST_TRADE_ASSETS,
        # UserTag.LATEST_SPOT_TRADE_MARKETS,
        # UserTag.LATEST_PERPETUAL_TRADE_MARKETS,
        UserTag.FIRST_PLEDGE_ASSETS,
        UserTag.LATEST_PLEDGE_ASSETS,
    }
    tag_relations = [
        # TagRelation(
        #     tags={UserTag.LATEST_SPOT_TRADE_MARKETS, },
        #     requires={TagRequirement.SPOT_DEAL_MARKETS, }
        # ),
        # TagRelation(
        #     tags={UserTag.LATEST_PERPETUAL_TRADE_MARKETS, },
        #     requires={TagRequirement.PERPETUAL_DEAL_MARKETS, }
        # ),
        TagRelation(
            tags={UserTag.FIRST_TRADE_ASSETS, },
            requires={TagRequirement.SPOT_TRADE_ASSETS, TagRequirement.PERPETUAL_TRADE_ASSETS}
        ),
    ]

    def flush(self, report_date: Optional[date]):
        """增量更新"""
        flush_data = {
            _tag: {}
            for _tag in self.impl_tags
        }
        mm_user_ids = set(MarketMakerHelper.list_all_maker_ids())
        # for result in yield_get_user_latest_data(
        #     UserTagRequirement,
        #     [UserTagRequirement.user_id, UserTagRequirement.value],
        #     UserTagRequirement.tag == TagRequirement.SPOT_DEAL_MARKETS.name):
        #     flush_data[UserTag.LATEST_SPOT_TRADE_MARKETS][result["user_id"]] = \
        #         data_loads(result["value"], TagRawValueType.STRING)
        #
        # for result in yield_get_user_latest_data(
        #     UserTagRequirement,
        #     [UserTagRequirement.user_id, UserTagRequirement.value],
        #     UserTagRequirement.tag == TagRequirement.PERPETUAL_DEAL_MARKETS.name):
        #     flush_data[UserTag.LATEST_PERPETUAL_TRADE_MARKETS][result["user_id"]] = \
        #         data_loads(result["value"], TagRawValueType.STRING)
        #
        # latest_spot_assets_func = partial(
        #     yield_get_user_latest_data,
        #     UserTagRequirement,
        #     [UserTagRequirement.user_id, UserTagRequirement.report_date,
        #      UserTagRequirement.value],
        #     UserTagRequirement.tag == TagRequirement.SPOT_TRADE_ASSETS.name)
        #
        first_spot_assets_func = partial(
            yield_get_user_first_data,
            UserTagRequirement,
            [UserTagRequirement.user_id, UserTagRequirement.report_date,
             UserTagRequirement.value],
            UserTagRequirement.tag == TagRequirement.SPOT_TRADE_ASSETS.name)
        # latest_perpetual_assets_func = partial(
        #     yield_get_user_latest_data,
        #     UserTagRequirement,
        #     [UserTagRequirement.user_id, UserTagRequirement.report_date,
        #      UserTagRequirement.value],
        #     UserTagRequirement.tag == TagRequirement.PERPETUAL_TRADE_ASSETS.name)
        first_perpetual_assets_func = partial(
            yield_get_user_first_data,
            UserTagRequirement,
            [UserTagRequirement.user_id, UserTagRequirement.report_date,
             UserTagRequirement.value],
            UserTagRequirement.tag == TagRequirement.PERPETUAL_TRADE_ASSETS.name)
        first_trade_assets_result = defaultdict(lambda: defaultdict(set))
        # latest_trade_assets_result = defaultdict(lambda: defaultdict(set))

        for record in first_spot_assets_func():
            first_trade_assets_result[record["user_id"]][record["report_date"]] \
                |= set(data_loads(record["value"], TagRawValueType.STRING))
        for record in first_perpetual_assets_func():
            first_trade_assets_result[record["user_id"]][record["report_date"]] \
                |= set(data_loads(record["value"], TagRawValueType.STRING))
        # for record in latest_spot_assets_func():
        #     latest_trade_assets_result[record["user_id"]][record["report_date"]] \
        #         |= set(data_loads(record["value"], TagRawValueType.STRING))
        # for record in latest_perpetual_assets_func():
        #     latest_trade_assets_result[record["user_id"]][record["report_date"]] \
        #         |= set(data_loads(record["value"], TagRawValueType.STRING))
        for user_id, _values in first_trade_assets_result.items():
            _value = _values.get(min(list(_values.keys())), set())
            if not _value:
                continue
            flush_data[UserTag.FIRST_TRADE_ASSETS][user_id] = _value
        # for user_id, _values in latest_trade_assets_result.items():
        #     _value = _values.get(max(list(_values.keys())), set())
        #     if not _value:
        #         continue
        #     flush_data[UserTag.LATEST_TRADE_ASSETS][user_id] = _value
        first_pledge_data, last_pledge_data = self._get_first_and_last_pledge_data()
        for user_id, _value in first_pledge_data.items():
            if user_id in mm_user_ids:
                continue
            flush_data[UserTag.FIRST_PLEDGE_ASSETS][user_id] = _value
        for user_id, _value in last_pledge_data.items():
            if user_id in mm_user_ids:
                continue
            flush_data[UserTag.LATEST_PLEDGE_ASSETS][user_id] = _value

        # convert tag enum to str
        convert_data = {
            _tag.name: [(_user_id, data_dumps(_value)) for _user_id, _value in _all_tag_data.items()]
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data

    def _get_first_and_last_pledge_data(self):
        first_rows = PledgeLoanHistory.query.with_entities(
            func.min(PledgeLoanHistory.id).label('id')
        ).filter(
            PledgeLoanHistory.pledge_data.isnot(None)
        ).group_by(
            PledgeLoanHistory.user_id
        ).all()
        first_ids = [row.id for row in first_rows]
        first_data = defaultdict(set)
        for chunk_ids in batch_iter(first_ids, 5000):
            rows = PledgeLoanHistory.query.with_entities(
                PledgeLoanHistory.user_id,
                PledgeLoanHistory.pledge_data,
            ).filter(
                PledgeLoanHistory.id.in_(chunk_ids)
            ).all()
            for row in rows:
                if not row.pledge_data:
                    continue
                pledge_data = json.loads(row.pledge_data)
                _assets = list(pledge_data.keys())
                first_data[row.user_id].update(_assets)
        last_rows = PledgeLoanHistory.query.with_entities(
            func.max(PledgeLoanHistory.id).label('id')
        ).filter(
            PledgeLoanHistory.pledge_data.isnot(None)
        ).group_by(
            PledgeLoanHistory.user_id
        ).all()
        last_ids = [row.id for row in last_rows]
        last_data = defaultdict(set)
        for chunk_ids in batch_iter(last_ids, 5000):
            rows = PledgeLoanHistory.query.with_entities(
                PledgeLoanHistory.user_id,
                PledgeLoanHistory.pledge_data,
            ).filter(
                PledgeLoanHistory.id.in_(chunk_ids)
            ).all()
            for row in rows:
                if not row.pledge_data:
                    continue
                pledge_data = json.loads(row.pledge_data)
                _assets = list(pledge_data.keys())
                last_data[row.user_id].update(_assets)
        return first_data, last_data


class TradeDateDataHandler(AggregateHandler):

    read_slot = frozenset([15])
    write_slot = frozenset([15])

    priority = 996

    impl_tags = {
        UserTag.RECENT_7D_SPOT_MARKETS,
        UserTag.RECENT_30D_SPOT_MARKETS,
        UserTag.RECENT_90D_SPOT_MARKETS,
        UserTag.RECENT_7D_PERPETUAL_MARKETS,
        UserTag.RECENT_30D_PERPETUAL_MARKETS,
        UserTag.RECENT_90D_PERPETUAL_MARKETS,
        UserTag.RECENT_7D_EXCHANGE_MARKETS,
        UserTag.RECENT_30D_EXCHANGE_MARKETS,
        UserTag.RECENT_90D_EXCHANGE_MARKETS,
        UserTag.RECENT_7D_MARGIN_TRADE_MARKETS,
        UserTag.RECENT_30D_MARGIN_TRADE_MARKETS,
        UserTag.RECENT_90D_MARGIN_TRADE_MARKETS,

        UserTag.RECENT_7D_SPOT_TRADE_USD,
        UserTag.RECENT_30D_SPOT_TRADE_USD,
        UserTag.RECENT_90D_SPOT_TRADE_USD,
        UserTag.RECENT_7D_PERPETUAL_TRADE_USD,
        UserTag.RECENT_30D_PERPETUAL_TRADE_USD,
        UserTag.RECENT_90D_PERPETUAL_TRADE_USD,
        UserTag.RECENT_7D_EXCHANGE_TRADE_USD,
        UserTag.RECENT_30D_EXCHANGE_TRADE_USD,
        UserTag.RECENT_90D_EXCHANGE_TRADE_USD,
        UserTag.RECENT_7D_MARGIN_TRADE_USD,
        UserTag.RECENT_30D_MARGIN_TRADE_USD,
        UserTag.RECENT_90D_MARGIN_TRADE_USD,

        UserTag.SPOT_MARKETS_TRADE_7D_TOP3,
        UserTag.SPOT_MARKETS_TRADE_30D_TOP3,
        UserTag.SPOT_MARKETS_TRADE_90D_TOP3,
        UserTag.EXCHANGE_MARKETS_TRADE_7D_TOP3,
        UserTag.EXCHANGE_MARKETS_TRADE_30D_TOP3,
        UserTag.EXCHANGE_MARKETS_TRADE_90D_TOP3,
        UserTag.PERPETUAL_MARKETS_TRADE_7D_TOP3,
        UserTag.PERPETUAL_MARKETS_TRADE_30D_TOP3,
        UserTag.PERPETUAL_MARKETS_TRADE_90D_TOP3,
        UserTag.MARGIN_MARKETS_TRADE_7D_TOP3,
        UserTag.MARGIN_MARKETS_TRADE_30D_TOP3,
        UserTag.MARGIN_MARKETS_TRADE_90D_TOP3,

        UserTag.SPOT_TRADE_7D_ACTIVE_DAYS,
        UserTag.SPOT_TRADE_30D_ACTIVE_DAYS,
        UserTag.SPOT_TRADE_90D_ACTIVE_DAYS,
        UserTag.EXCHANGE_TRADE_7D_ACTIVE_DAYS,
        UserTag.EXCHANGE_TRADE_30D_ACTIVE_DAYS,
        UserTag.EXCHANGE_TRADE_90D_ACTIVE_DAYS,
        UserTag.PERPETUAL_TRADE_7D_ACTIVE_DAYS,
        UserTag.PERPETUAL_TRADE_30D_ACTIVE_DAYS,
        UserTag.PERPETUAL_TRADE_90D_ACTIVE_DAYS,
        UserTag.RECENT_30D_PERPETUAL_EVERYDAY_TRADE_USD,
        UserTag.MARGIN_TRADE_7D_ACTIVE_DAYS,
        UserTag.MARGIN_TRADE_30D_ACTIVE_DAYS,
        UserTag.MARGIN_TRADE_90D_ACTIVE_DAYS,

        UserTag.RECENT_7D_TRADE_USD,
        UserTag.RECENT_30D_TRADE_USD,
        UserTag.RECENT_90D_TRADE_USD,
        UserTag.RECENT_7D_PLEDGE_ASSET_USD,
        UserTag.RECENT_30D_PLEDGE_ASSET_USD,
        UserTag.RECENT_90D_PLEDGE_ASSET_USD,
        UserTag.RECENT_7D_PLEDGE_LOAN_USD,
        UserTag.RECENT_30D_PLEDGE_LOAN_USD,
        UserTag.RECENT_90D_PLEDGE_LOAN_USD,
        UserTag.ONLY_SPOT_TRADE_7D_ACTIVE_DAYS,
        UserTag.ONLY_SPOT_TRADE_30D_ACTIVE_DAYS,
        UserTag.ONLY_SPOT_TRADE_90D_ACTIVE_DAYS,
        UserTag.ONLY_SPOT_MARKETS_TRADE_7D_TOP3,
        UserTag.ONLY_SPOT_MARKETS_TRADE_30D_TOP3,
        UserTag.ONLY_SPOT_MARKETS_TRADE_90D_TOP3,
        UserTag.RECENT_7D_ONLY_SPOT_TRADE_USD,
        UserTag.RECENT_30D_ONLY_SPOT_TRADE_USD,
        UserTag.RECENT_90D_ONLY_SPOT_TRADE_USD,
        UserTag.RECENT_7D_ONLY_SPOT_MARKETS,
        UserTag.RECENT_30D_ONLY_SPOT_MARKETS,
        UserTag.RECENT_90D_ONLY_SPOT_MARKETS,
        UserTag.RECENT_7D_PLEDGE_INTEREST_USD,
        UserTag.RECENT_30D_PLEDGE_INTEREST_USD,
        UserTag.RECENT_90D_PLEDGE_INTEREST_USD,
    }
    tag_relations = [
        TagRelation(
            tags=impl_tags,
            requires={
                      TagRequirement.SPOT_MARKET_TRADE_VOL,
                      TagRequirement.PERPETUAL_MARKET_TRADE_VOL,
                      TagRequirement.MARGIN_MARKET_TRADE_VOL,
                      TagRequirement.PLEDGE_LOAN_USD,
                      TagRequirement.PLEDGE_ASSET_USD,
                      TagRequirement.ONLY_SPOT_MARKET_TRADE_VOL,
            }
        ),
    ]

    def save(self, report_date: Optional[date]):
        reqs_data = UserTagRequirement.query.filter(
            UserTagRequirement.tag.in_(
                [
                    TagRequirement.SPOT_MARKET_TRADE_VOL.name,
                    TagRequirement.PERPETUAL_MARKET_TRADE_VOL.name,
                    TagRequirement.MARGIN_MARKET_TRADE_VOL.name,
                    TagRequirement.PLEDGE_LOAN_USD.name,
                    TagRequirement.PLEDGE_ASSET_USD.name,
                    TagRequirement.ONLY_SPOT_MARKET_TRADE_VOL.name,
                 ]
            ),
            UserTagRequirement.report_date >= report_date - timedelta(days=90)
        ).all()
        # {tag: {user_id: {market1, market2}}}
        spot_markets_data = defaultdict(lambda: defaultdict(set))
        only_spot_markets_data = defaultdict(lambda: defaultdict(set))
        margin_markets_data = defaultdict(lambda: defaultdict(set))
        perpetual_markets_data = defaultdict(lambda: defaultdict(set))
        exchange_markets_data = defaultdict(lambda: defaultdict(set))
        spot_markets_active_data = defaultdict(lambda: defaultdict(set))
        only_spot_markets_active_data = defaultdict(lambda: defaultdict(set))
        margin_markets_active_data = defaultdict(lambda: defaultdict(set))
        perpetual_markets_active_data = defaultdict(lambda: defaultdict(set))
        exchange_markets_active_data = defaultdict(lambda: defaultdict(set))
        spot_markets_top3_data = defaultdict(lambda: defaultdict(Counter))
        only_spot_markets_top3_data = defaultdict(lambda: defaultdict(Counter))
        margin_markets_top3_data = defaultdict(lambda: defaultdict(Counter))
        perpetual_markets_top3_data = defaultdict(lambda: defaultdict(Counter))
        exchange_markets_top3_data = defaultdict(lambda: defaultdict(Counter))
        perpetual_everyday_trade_data = defaultdict(lambda: defaultdict(Decimal))
        # {tag: {user_id: usd}}
        spot_trade_usd_data = defaultdict(lambda: defaultdict(Decimal))
        only_spot_trade_usd_data = defaultdict(lambda: defaultdict(Decimal))
        margin_trade_usd_data = defaultdict(lambda: defaultdict(Decimal))
        perpetual_trade_usd_data = defaultdict(lambda: defaultdict(Decimal))
        exchange_trade_usd_data = defaultdict(lambda: defaultdict(Decimal))
        user_total_trade_usd_data = defaultdict(lambda: defaultdict(Decimal))
        user_pledge_asset_usd_data = defaultdict(lambda: defaultdict(Decimal))
        user_pledge_loan_usd_data = defaultdict(lambda: defaultdict(Decimal))
        user_pledge_interest_usd_data = defaultdict(lambda: defaultdict(Decimal))
        for req_data in reqs_data:
            req_data: UserTagRequirement
            if req_data.tag == TagRequirement.SPOT_MARKET_TRADE_VOL.name:
                if req_data.report_date >= report_date - timedelta(days=7):
                    spot_markets_data[UserTag.RECENT_7D_SPOT_MARKETS][
                        req_data.user_id].add(req_data.scope)
                    spot_markets_active_data[UserTag.SPOT_TRADE_7D_ACTIVE_DAYS][
                        req_data.user_id].add(req_data.report_date)
                    spot_markets_top3_data[UserTag.SPOT_MARKETS_TRADE_7D_TOP3][
                        req_data.user_id][req_data.scope] += Decimal(req_data.value)
                    spot_trade_usd_data[UserTag.RECENT_7D_SPOT_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)
                    user_total_trade_usd_data[UserTag.RECENT_7D_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)
                if req_data.report_date >= report_date - timedelta(days=30):
                    spot_markets_data[UserTag.RECENT_30D_SPOT_MARKETS][
                        req_data.user_id].add(req_data.scope)
                    spot_markets_active_data[UserTag.SPOT_TRADE_30D_ACTIVE_DAYS][
                        req_data.user_id].add(req_data.report_date)
                    spot_markets_top3_data[UserTag.SPOT_MARKETS_TRADE_30D_TOP3][
                        req_data.user_id][req_data.scope] += Decimal(req_data.value)
                    spot_trade_usd_data[UserTag.RECENT_30D_SPOT_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)
                    user_total_trade_usd_data[UserTag.RECENT_30D_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)

                spot_markets_data[UserTag.RECENT_90D_SPOT_MARKETS][
                    req_data.user_id].add(req_data.scope)
                spot_markets_active_data[UserTag.SPOT_TRADE_90D_ACTIVE_DAYS][
                    req_data.user_id].add(req_data.report_date)
                spot_markets_top3_data[UserTag.SPOT_MARKETS_TRADE_90D_TOP3][
                    req_data.user_id][req_data.scope] += Decimal(req_data.value)
                spot_trade_usd_data[UserTag.RECENT_90D_SPOT_TRADE_USD][
                    req_data.user_id] += Decimal(req_data.value)
                user_total_trade_usd_data[UserTag.RECENT_90D_TRADE_USD][
                    req_data.user_id] += Decimal(req_data.value)

            if req_data.tag == TagRequirement.MARGIN_MARKET_TRADE_VOL.name:
                if req_data.report_date >= report_date - timedelta(days=7):
                    margin_markets_data[UserTag.RECENT_7D_MARGIN_TRADE_MARKETS][
                        req_data.user_id].add(req_data.scope)
                    margin_markets_active_data[UserTag.MARGIN_TRADE_7D_ACTIVE_DAYS][
                        req_data.user_id].add(req_data.report_date)
                    margin_markets_top3_data[UserTag.MARGIN_MARKETS_TRADE_7D_TOP3][
                        req_data.user_id][req_data.scope] += Decimal(req_data.value)
                    margin_trade_usd_data[UserTag.RECENT_7D_MARGIN_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)
                if req_data.report_date >= report_date - timedelta(days=30):
                    margin_markets_data[UserTag.RECENT_30D_MARGIN_TRADE_MARKETS][
                        req_data.user_id].add(req_data.scope)
                    margin_markets_active_data[UserTag.MARGIN_TRADE_30D_ACTIVE_DAYS][
                        req_data.user_id].add(req_data.report_date)
                    margin_markets_top3_data[UserTag.MARGIN_MARKETS_TRADE_30D_TOP3][
                        req_data.user_id][req_data.scope] += Decimal(req_data.value)
                    margin_trade_usd_data[UserTag.RECENT_30D_MARGIN_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)

                margin_markets_data[UserTag.RECENT_90D_MARGIN_TRADE_MARKETS][
                    req_data.user_id].add(req_data.scope)
                margin_markets_active_data[UserTag.MARGIN_TRADE_90D_ACTIVE_DAYS][
                    req_data.user_id].add(req_data.report_date)
                margin_markets_top3_data[UserTag.MARGIN_MARKETS_TRADE_90D_TOP3][
                    req_data.user_id][req_data.scope] += Decimal(req_data.value)
                margin_trade_usd_data[UserTag.RECENT_90D_MARGIN_TRADE_USD][
                    req_data.user_id] += Decimal(req_data.value)

            if req_data.tag == TagRequirement.PERPETUAL_MARKET_TRADE_VOL.name:
                if req_data.report_date >= report_date - timedelta(days=7):
                    perpetual_markets_data[UserTag.RECENT_7D_PERPETUAL_MARKETS][
                        req_data.user_id].add(req_data.scope)
                    perpetual_markets_active_data[UserTag.PERPETUAL_TRADE_7D_ACTIVE_DAYS][
                        req_data.user_id].add(req_data.report_date)
                    perpetual_markets_top3_data[UserTag.PERPETUAL_MARKETS_TRADE_7D_TOP3][
                        req_data.user_id][req_data.scope] += Decimal(req_data.value)
                    perpetual_trade_usd_data[UserTag.RECENT_7D_PERPETUAL_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)
                    user_total_trade_usd_data[UserTag.RECENT_7D_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)
                if req_data.report_date >= report_date - timedelta(days=30):
                    perpetual_markets_data[UserTag.RECENT_30D_PERPETUAL_MARKETS][
                        req_data.user_id].add(req_data.scope)
                    perpetual_markets_active_data[UserTag.PERPETUAL_TRADE_30D_ACTIVE_DAYS][
                        req_data.user_id].add(req_data.report_date)
                    perpetual_markets_top3_data[UserTag.PERPETUAL_MARKETS_TRADE_30D_TOP3][
                        req_data.user_id][req_data.scope] += Decimal(req_data.value)
                    perpetual_trade_usd_data[UserTag.RECENT_30D_PERPETUAL_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)
                    user_total_trade_usd_data[UserTag.RECENT_30D_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)
                    _date_str = req_data.report_date.strftime("%Y-%m-%d")
                    perpetual_everyday_trade_data[req_data.user_id][_date_str] += \
                        quantize_amount(Decimal(req_data.value),
                                        PrecisionEnum.CASH_PLACES)

                perpetual_markets_data[UserTag.RECENT_90D_PERPETUAL_MARKETS][
                    req_data.user_id].add(req_data.scope)
                perpetual_markets_active_data[UserTag.PERPETUAL_TRADE_90D_ACTIVE_DAYS][
                    req_data.user_id].add(req_data.report_date)
                perpetual_markets_top3_data[UserTag.PERPETUAL_MARKETS_TRADE_90D_TOP3][
                    req_data.user_id][req_data.scope] += Decimal(req_data.value)
                perpetual_trade_usd_data[UserTag.RECENT_90D_PERPETUAL_TRADE_USD][
                    req_data.user_id] += Decimal(req_data.value)
                user_total_trade_usd_data[UserTag.RECENT_90D_TRADE_USD][
                    req_data.user_id] += Decimal(req_data.value)
            if req_data.tag == TagRequirement.PLEDGE_ASSET_USD.name:
                if req_data.report_date >= report_date - timedelta(days=7):
                    user_pledge_asset_usd_data[UserTag.RECENT_7D_PLEDGE_ASSET_USD][
                        req_data.user_id] += Decimal(req_data.value)
                if req_data.report_date >= report_date - timedelta(days=30):
                    user_pledge_asset_usd_data[UserTag.RECENT_30D_PLEDGE_ASSET_USD][
                        req_data.user_id] += Decimal(req_data.value)
                user_pledge_asset_usd_data[UserTag.RECENT_90D_PLEDGE_ASSET_USD][
                    req_data.user_id] += Decimal(req_data.value)
            if req_data.tag == TagRequirement.PLEDGE_LOAN_USD.name:
                if req_data.report_date >= report_date - timedelta(days=7):
                    user_pledge_loan_usd_data[UserTag.RECENT_7D_PLEDGE_LOAN_USD][
                        req_data.user_id] += Decimal(req_data.value)
                if req_data.report_date >= report_date - timedelta(days=30):
                    user_pledge_loan_usd_data[UserTag.RECENT_30D_PLEDGE_LOAN_USD][
                        req_data.user_id] += Decimal(req_data.value)
                user_pledge_loan_usd_data[UserTag.RECENT_90D_PLEDGE_LOAN_USD][
                    req_data.user_id] += Decimal(req_data.value)

            if req_data.tag == TagRequirement.ONLY_SPOT_MARKET_TRADE_VOL.name:
                if req_data.report_date >= report_date - timedelta(days=7):
                    only_spot_markets_data[UserTag.RECENT_7D_ONLY_SPOT_MARKETS][
                        req_data.user_id].add(req_data.scope)
                    only_spot_markets_active_data[UserTag.ONLY_SPOT_TRADE_7D_ACTIVE_DAYS][
                        req_data.user_id].add(req_data.report_date)
                    only_spot_markets_top3_data[UserTag.ONLY_SPOT_MARKETS_TRADE_7D_TOP3][
                        req_data.user_id][req_data.scope] += Decimal(req_data.value)
                    only_spot_trade_usd_data[UserTag.RECENT_7D_ONLY_SPOT_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)
                if req_data.report_date >= report_date - timedelta(days=30):
                    only_spot_markets_data[UserTag.RECENT_30D_ONLY_SPOT_MARKETS][
                        req_data.user_id].add(req_data.scope)
                    only_spot_markets_active_data[UserTag.ONLY_SPOT_TRADE_30D_ACTIVE_DAYS][
                        req_data.user_id].add(req_data.report_date)
                    only_spot_markets_top3_data[UserTag.ONLY_SPOT_MARKETS_TRADE_30D_TOP3][
                        req_data.user_id][req_data.scope] += Decimal(req_data.value)
                    only_spot_trade_usd_data[UserTag.RECENT_30D_ONLY_SPOT_TRADE_USD][
                        req_data.user_id] += Decimal(req_data.value)

                only_spot_markets_data[UserTag.RECENT_90D_ONLY_SPOT_MARKETS][
                    req_data.user_id].add(req_data.scope)
                only_spot_markets_active_data[UserTag.ONLY_SPOT_TRADE_90D_ACTIVE_DAYS][
                    req_data.user_id].add(req_data.report_date)
                only_spot_markets_top3_data[UserTag.ONLY_SPOT_MARKETS_TRADE_90D_TOP3][
                    req_data.user_id][req_data.scope] += Decimal(req_data.value)
                only_spot_trade_usd_data[UserTag.RECENT_90D_ONLY_SPOT_TRADE_USD][
                    req_data.user_id] += Decimal(req_data.value)

        del reqs_data
        mm_user_ids = set(MarketMakerHelper.list_all_maker_ids())
        exchange_orders = query_records_by_time_range(
            AssetExchangeOrder,
            date_to_datetime(report_date - timedelta(days=90)),
            date_to_datetime(report_date))
        daily_price_map = AssetPrice.get_close_price_range_map(
            start_date=report_date - timedelta(days=90),
            end_date=report_date)
        exchange_datas = defaultdict(Decimal)
        sub_user_mapping = get_sub_account_mapping()
        for _order in exchange_orders:
            main_user_id = sub_user_mapping.get(_order.user_id, _order.user_id)
            if main_user_id in mm_user_ids:
                continue
            _date = _order.created_at.date()
            exchange_datas[(main_user_id, f"{_order.source_asset}{_order.target_asset}", _date)] \
                = _order.target_asset_exchanged_amount * \
              daily_price_map[_date].get(_order.target_asset, Decimal())
        for key, usd in exchange_datas.items():
            user_id, market, order_report_date = key
            if order_report_date >= report_date - timedelta(days=7):
                exchange_markets_data[UserTag.RECENT_7D_EXCHANGE_MARKETS][
                    user_id].add(market)
                spot_markets_data[UserTag.RECENT_7D_SPOT_MARKETS][
                    user_id].add(market)
                exchange_markets_active_data[UserTag.EXCHANGE_TRADE_7D_ACTIVE_DAYS][
                    user_id].add(order_report_date)
                spot_markets_active_data[UserTag.SPOT_TRADE_7D_ACTIVE_DAYS][
                    user_id].add(order_report_date)
                exchange_markets_top3_data[UserTag.EXCHANGE_MARKETS_TRADE_7D_TOP3][
                    user_id][market] += usd
                spot_markets_top3_data[UserTag.SPOT_MARKETS_TRADE_7D_TOP3][
                    user_id][market] += usd
                exchange_trade_usd_data[UserTag.RECENT_7D_EXCHANGE_TRADE_USD][
                    user_id] += usd
                spot_trade_usd_data[UserTag.RECENT_7D_SPOT_TRADE_USD][
                    user_id] += usd
                user_total_trade_usd_data[UserTag.RECENT_7D_TRADE_USD][
                    user_id] += usd
            if order_report_date >= report_date - timedelta(days=30):
                exchange_markets_data[UserTag.RECENT_30D_EXCHANGE_MARKETS][
                    user_id].add(market)
                spot_markets_data[UserTag.RECENT_30D_SPOT_MARKETS][
                    user_id].add(market)
                exchange_markets_active_data[UserTag.EXCHANGE_TRADE_30D_ACTIVE_DAYS][
                    user_id].add(order_report_date)
                spot_markets_active_data[UserTag.SPOT_TRADE_30D_ACTIVE_DAYS][
                    user_id].add(order_report_date)
                exchange_markets_top3_data[UserTag.EXCHANGE_MARKETS_TRADE_30D_TOP3][
                    user_id][market] += usd
                spot_markets_top3_data[UserTag.SPOT_MARKETS_TRADE_30D_TOP3][
                    user_id][market] += usd
                exchange_trade_usd_data[UserTag.RECENT_30D_EXCHANGE_TRADE_USD][
                    user_id] += usd
                spot_trade_usd_data[UserTag.RECENT_30D_SPOT_TRADE_USD][
                    user_id] += usd
                user_total_trade_usd_data[UserTag.RECENT_30D_TRADE_USD][
                    user_id] += usd
            exchange_markets_data[UserTag.RECENT_90D_EXCHANGE_MARKETS][
                user_id].add(market)
            spot_markets_data[UserTag.RECENT_90D_SPOT_MARKETS][
                user_id].add(market)
            exchange_markets_active_data[UserTag.EXCHANGE_TRADE_90D_ACTIVE_DAYS][
                user_id].add(order_report_date)
            spot_markets_active_data[UserTag.SPOT_TRADE_90D_ACTIVE_DAYS][
                user_id].add(order_report_date)
            exchange_markets_top3_data[UserTag.EXCHANGE_MARKETS_TRADE_90D_TOP3][
                user_id][market] += usd
            spot_markets_top3_data[UserTag.SPOT_MARKETS_TRADE_90D_TOP3][
                user_id][market] += usd
            exchange_trade_usd_data[UserTag.RECENT_90D_EXCHANGE_TRADE_USD][
                user_id] += usd
            spot_trade_usd_data[UserTag.RECENT_90D_SPOT_TRADE_USD][
                user_id] += usd
            user_total_trade_usd_data[UserTag.RECENT_90D_TRADE_USD][
                user_id] += usd

        pledge_interest_datas = defaultdict(Decimal)
        for v in yield_query_records_by_time_range(
                PledgeInterestHistory,
                date_to_datetime(report_date - timedelta(days=90)),
                date_to_datetime(report_date),
                [
                    PledgeInterestHistory.created_at,
                    PledgeInterestHistory.user_id,
                    PledgeInterestHistory.loan_asset,
                    PledgeInterestHistory.amount
                ],
        ):
            main_user_id = sub_user_mapping.get(v.user_id, v.user_id)
            if main_user_id in mm_user_ids:
                continue
            _date = v.created_at.date()
            _usd = daily_price_map[_date].get(v.loan_asset, Decimal()) * v.amount
            pledge_interest_datas[(main_user_id, _date)] += _usd
        for (user_id, _date), usd in pledge_interest_datas.items():
            if _date >= report_date - timedelta(days=7):
                user_pledge_interest_usd_data[UserTag.RECENT_7D_PLEDGE_INTEREST_USD][user_id] += usd
            if _date >= report_date - timedelta(days=30):
                user_pledge_interest_usd_data[UserTag.RECENT_30D_PLEDGE_INTEREST_USD][user_id] += usd
            user_pledge_interest_usd_data[UserTag.RECENT_90D_PLEDGE_INTEREST_USD][user_id] += usd
        insert_records = []
        write_model = self.get_write_model()
        for _uid, _data in perpetual_everyday_trade_data.items():
            result = sorted([(v[0], amount_to_str(v[1])) for v in _data.items()])
            insert_records.append(
                write_model(
                    user_id=_uid,
                    tag=UserTag.RECENT_30D_PERPETUAL_EVERYDAY_TRADE_USD.name,
                    value=data_dumps(result)
                )
            )
        datas0 = [
            spot_markets_data,
            only_spot_markets_data,
            perpetual_markets_data,
            margin_markets_data,
            exchange_markets_data,

            spot_trade_usd_data,
            only_spot_trade_usd_data,
            perpetual_trade_usd_data,
            margin_trade_usd_data,
            exchange_trade_usd_data,
            user_total_trade_usd_data,
            user_pledge_asset_usd_data,
            user_pledge_loan_usd_data,
            user_pledge_interest_usd_data,
        ]
        for data in datas0:
            for _tag, _data in data.items():
                for _user_id, _value in _data.items():
                    insert_records.append(
                        write_model(
                            user_id=_user_id,
                            tag=_tag.name,
                            value=data_dumps(_value)
                        )
                    )
        datas1 = [
            spot_markets_active_data,
            only_spot_markets_active_data,
            perpetual_markets_active_data,
            margin_markets_active_data,
            exchange_markets_active_data,
        ]
        for data in datas1:
            for _tag, _data in data.items():
                for _user_id, _value in _data.items():
                    insert_records.append(
                        write_model(
                            user_id=_user_id,
                            tag=_tag.name,
                            value=data_dumps(len(_value))
                        )
                    )
        datas2 = [
            spot_markets_top3_data,
            only_spot_markets_top3_data,
            perpetual_markets_top3_data,
            margin_markets_top3_data,
            exchange_markets_top3_data,
        ]
        for data in datas2:
            for _tag, _data in data.items():
                for _user_id, _value in _data.items():
                    insert_records.append(
                        write_model(
                            user_id=_user_id,
                            tag=_tag.name,
                            value=data_dumps([_v[0] for _v in _value.most_common(3)])
                        )
                    )

        # 清除对象引用，释放内存
        for objs in [
            datas0,
            datas1,
            datas2,
        ]:
            for obj in objs:
                del obj
        write_model.query.filter(write_model.tag.in_([_tag.name for _tag in self.impl_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)


class PerpetualLiquidationHandler(TagHandler):

    read_slot = frozenset([2])
    write_slot = frozenset([2])

    impl_tags = {
        UserTag.PERPETUAL_7D_LIQ_COUNT,
        UserTag.PERPETUAL_30D_LIQ_COUNT,
        UserTag.PERPETUAL_90D_LIQ_COUNT,
    }

    @classmethod
    def query_liq_records_range(cls, start: int, end: int) -> List:
        last_id = 0
        limit = 10000
        records = []
        while True:
            if last_id:
                where = f"id < {last_id}"
            else:
                where = None
            rows = PerpetualSysHistoryDB.table("positionliq_history").select(
                "liq_time", "user_id", "id",
                where=where,
                limit=limit,
                order_by="id desc"
            )
            if not rows:
                break

            for row in rows:
                if not start <= row[0] <= end:
                    continue
                records.append(row)
            if len(rows) > 0:
                last_id = records[-1][-1]
            if rows[-1][0] < start:
                break
        return records

    def save(self, report_date: Optional[date]):
        start_date = report_date - timedelta(days=90)
        start = datetime_to_time(start_date)
        end = datetime_to_time(report_date)
        records = self.query_liq_records_range(start, end)

        insert_records = []
        write_model = self.get_write_model()
        result = defaultdict(lambda: defaultdict(int))
        sub_user_mapping = get_sub_account_mapping()
        for record in records:
            liq_time, user_id, _ = record
            main_user_id = sub_user_mapping.get(user_id, user_id)
            _date = timestamp_to_date(liq_time)
            if _date >= report_date - timedelta(days=7):
                result[UserTag.PERPETUAL_7D_LIQ_COUNT][main_user_id] += 1
            if _date >= report_date - timedelta(days=30):
                result[UserTag.PERPETUAL_30D_LIQ_COUNT][main_user_id] += 1

            result[UserTag.PERPETUAL_90D_LIQ_COUNT][main_user_id] += 1

        disabled_user_ids = get_disabled_user_ids()

        for _tag, _data in result.items():
            for _user_id, _value in _data.items():
                if _user_id in disabled_user_ids:
                    continue
                insert_records.append(
                    write_model(
                        user_id=_user_id,
                        tag=_tag.name,
                        value=data_dumps(_value)
                    )
                )
        write_model.query.filter(write_model.tag.in_([_tag.name for _tag in self.impl_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)


class TradeDealCountDataHandler(AggregateHandler):

    read_slot = frozenset([22])
    write_slot = frozenset([22])

    impl_tags = {
        UserTag.RECENT_30D_SPOT_DEAL_CNT,
        UserTag.RECENT_90D_SPOT_DEAL_CNT,
        UserTag.RECENT_30D_PERPETUAL_DEAL_CNT,
        UserTag.RECENT_90D_PERPETUAL_DEAL_CNT,
    }

    tag_relations = [
        TagRelation(
            tags={UserTag.RECENT_30D_SPOT_DEAL_CNT,  UserTag.RECENT_90D_SPOT_DEAL_CNT},
            requires={TagRequirement.SPOT_DEAL_ORDERS, }
        ),
        TagRelation(
            tags={UserTag.RECENT_30D_PERPETUAL_DEAL_CNT, UserTag.RECENT_30D_PERPETUAL_DEAL_CNT},
            requires={TagRequirement.PERPETUAL_DEAL_ORDERS, }
        ),
    ]

    def save(self, report_date: Optional[date]):
        from app.business.market_maker import MarketMakerHelper
        # sql:917s
        with read_only_session() as ro_session:
            reqs_data = ro_session.query(UserTagRequirement).filter(
                UserTagRequirement.tag.in_(
                    [
                        TagRequirement.SPOT_DEAL_ORDERS.name,
                        TagRequirement.PERPETUAL_DEAL_ORDERS.name,
                    ]
                ),
                UserTagRequirement.report_date >= report_date - timedelta(days=90)
            ).all()
        user_tag_data = defaultdict(set)

        mm_user_ids = set(MarketMakerHelper.list_all_maker_ids())

        def _get_tags(_type, _record_date: date):
            match _type:
                case TradeBusinessType.SPOT:
                    if _record_date >= report_date - timedelta(days=30):
                        return [UserTag.RECENT_30D_SPOT_DEAL_CNT, UserTag.RECENT_90D_SPOT_DEAL_CNT]
                    else:
                        return [UserTag.RECENT_90D_SPOT_DEAL_CNT]
                case TradeBusinessType.PERPETUAL:
                    if _record_date >= report_date - timedelta(days=30):
                        return [UserTag.RECENT_30D_PERPETUAL_DEAL_CNT, UserTag.RECENT_90D_PERPETUAL_DEAL_CNT]
                    else:
                        return [UserTag.RECENT_90D_PERPETUAL_DEAL_CNT]
            return []

        for req_data in reqs_data:
            if req_data.user_id in mm_user_ids:
                continue

            _type = TradeBusinessType.SPOT if req_data.tag == TagRequirement.SPOT_DEAL_ORDERS.name \
                else TradeBusinessType.PERPETUAL
            tags = _get_tags(_type, req_data.report_date)
            for _tag in tags:
                bm = BitMap()
                _ids = set(bm.deserialize(TagRequirement.deserialize(req_data.value)))
                user_tag_data[(_tag, req_data.user_id)] |= _ids
        insert_records = []
        write_model = self.get_write_model()
        for key, _value in user_tag_data.items():
            _tag, _user_id = key
            if is_filter_user(_user_id):
                continue
            _store_value = len(_value)
            insert_records.append(
                write_model(
                    user_id=_user_id,
                    tag=_tag.name,
                    value=data_dumps(_store_value)
                )
            )
        write_model.query.filter(write_model.tag.in_([_tag.name for _tag in self.impl_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)


class TotalTradeDataHandler(SimpleTagSaveMixin, TagHandler):

    read_slot = frozenset([9])
    write_slot = frozenset([9])

    impl_tags = {
        UserTag.TOTAL_SPOT_TRADE_USD,
        UserTag.TOTAL_PERPETUAL_TRADE_USD,
        UserTag.TOTAL_TRADE_USD,
    }

    def flush(self, report_date: Optional[date]):
        from app.business.market_maker import MarketMakerHelper
        q = UserTradeSummary.query.with_entities(
            UserTradeSummary.system,
            func.sum(UserTradeSummary.trade_amount).label("total"),
            UserTradeSummary.user_id
        ).group_by(
            UserTradeSummary.system,
            UserTradeSummary.user_id
        ).all()
        ex_q = UserExchangeSummary.query.with_entities(
            func.sum(UserExchangeSummary.trade_amount).label("total"),
            UserExchangeSummary.user_id
        ).group_by(
            UserExchangeSummary.user_id
        ).all()
        flush_data = {}
        mm_user_ids = set(MarketMakerHelper.list_all_maker_ids())
        flush_data[UserTag.TOTAL_TRADE_USD] = defaultdict(Decimal)
        flush_data[UserTag.TOTAL_SPOT_TRADE_USD] = defaultdict(Decimal)
        flush_data[UserTag.TOTAL_PERPETUAL_TRADE_USD] = defaultdict(Decimal)
        for v in q:
            if v.user_id in mm_user_ids:
                continue
            flush_data[UserTag.TOTAL_TRADE_USD][v.user_id] += v.total
            if v.system == UserTradeSummary.System.SPOT:
                flush_data[UserTag.TOTAL_SPOT_TRADE_USD][v.user_id] += v.total
            else:
                flush_data[UserTag.TOTAL_PERPETUAL_TRADE_USD][v.user_id] += v.total
        for v in ex_q:
            if v.user_id in mm_user_ids:
                continue
            flush_data[UserTag.TOTAL_SPOT_TRADE_USD][v.user_id] += v.total
            flush_data[UserTag.TOTAL_TRADE_USD][v.user_id] += v.total
        convert_data = {
            _tag.name: {_user_id: data_dumps(_value) for _user_id, _value in _all_tag_data.items()}
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data


class TotalPledgeDataHandler(AggregateHandler):

    read_slot = frozenset([9])
    write_slot = frozenset([9])

    impl_tags = {
        UserTag.TOTAL_PLEDGE_HOURS,
        UserTag.TOTAL_PLEDGE_LOAN_USD,
        UserTag.TOTAL_PLEDGE_ASSET_USD,
    }

    tag_relations = [
        TagRelation(
            tags=impl_tags,
            requires={
                TagRequirement.PLEDGE_ASSET_USD,
                TagRequirement.PLEDGE_LOAN_USD,
            }
        ),
    ]

    def flush(self, report_date: Optional[date]):
        mm_user_ids = set(MarketMakerHelper.list_all_maker_ids())
        pledge_q = PledgePosition.query.with_entities(
            PledgePosition.user_id,
            PledgePosition.finished_at,
            PledgePosition.created_at,
        ).filter(
            PledgePosition.finished_at.isnot(None)
        ).all()
        flush_data = {}
        user_pledge_seconds = defaultdict(int)
        for v in pledge_q:
            if v.user_id in mm_user_ids:
                continue
            user_pledge_seconds[v.user_id] += (v.finished_at - v.created_at).total_seconds()
        flush_data[UserTag.TOTAL_PLEDGE_HOURS] = {}
        for uid, seconds in user_pledge_seconds.items():
            flush_data[UserTag.TOTAL_PLEDGE_HOURS][uid] = math.ceil(seconds / 3600)

        reqs_data = UserTagRequirement.query.filter(
            UserTagRequirement.tag.in_(
                [tag_req.name for tag_rel in self.tag_relations for tag_req in tag_rel.requires]
            ),
        ).all()
        pledge_loan_usd_data = defaultdict(Decimal)
        pledge_asset_usd_data = defaultdict(Decimal)
        for req_data in reqs_data:
            req_data: UserTagRequirement
            if req_data.tag == TagRequirement.PLEDGE_ASSET_USD.name:
                pledge_asset_usd_data[req_data.user_id] += Decimal(req_data.value)
            if req_data.tag == TagRequirement.PLEDGE_LOAN_USD.name:
                pledge_loan_usd_data[req_data.user_id] += Decimal(req_data.value)
        flush_data[UserTag.TOTAL_PLEDGE_ASSET_USD] = pledge_asset_usd_data
        flush_data[UserTag.TOTAL_PLEDGE_LOAN_USD] = pledge_loan_usd_data
        convert_data = {
            _tag.name: {_user_id: data_dumps(_value) for _user_id, _value in _all_tag_data.items()}
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data

    def save(self, report_date: Optional[date]):
        new_data = self.flush(report_date)
        insert_records = []
        write_model = self.get_write_model()
        disabled_user_ids = get_disabled_user_ids()
        for _tag_name, _tag_data in new_data.items():
            for (_uid, _dump_value) in _tag_data.items():
                if _uid in disabled_user_ids:
                    continue
                insert_records.append(
                    write_model(
                        user_id=_uid,
                        tag=_tag_name,
                        value=_dump_value
                    )
                )
        write_model.query.filter(
            write_model.tag.in_([_tag.name for _tag in self.impl_tags])
        ).delete(synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        if report_date:
            self.mark_finished(report_date)


class P2POrderHandler(TagHandler):
    read_slot = frozenset([15])
    write_slot = frozenset([15])

    impl_tags = {
        UserTag.RECENT_30D_P2P_DEAL_USD,
        UserTag.RECENT_90D_P2P_DEAL_USD,
        UserTag.RECENT_30D_P2P_BUY_USD,
        UserTag.RECENT_90D_P2P_BUY_USD,
        UserTag.RECENT_30D_P2P_SELL_USD,
        UserTag.RECENT_90D_P2P_SELL_USD,
        UserTag.RECENT_30D_P2P_DEAL_COUNT,
        UserTag.RECENT_90D_P2P_DEAL_COUNT,
        UserTag.RECENT_30D_P2P_DEAL_ASSETS,
    }

    def save(self, report_date: Optional[date]):
        insert_records = []
        write_model = self.get_write_model()
        daily_price_map = AssetPrice.get_close_price_range_map(
            start_date=report_date - timedelta(days=90),
            end_date=report_date)
        p2p_deal_usd_ret = defaultdict(lambda: defaultdict(Decimal))
        p2p_deal_count_ret = defaultdict(lambda: defaultdict(int))
        p2p_deal_asset_ret = defaultdict(lambda: defaultdict(set))
        # P2P 订单只有主账号
        for v in yield_query_records_by_time_range(
                P2pOrder,
                date_to_datetime(report_date - timedelta(days=90)),
                date_to_datetime(report_date),
                [
                    P2pOrder.customer_id,
                    P2pOrder.merchant_id,
                    P2pOrder.base,
                    P2pOrder.base_amount,
                    P2pOrder.quote,
                    P2pOrder.quote_amount,
                    P2pOrder.side,
                    P2pOrder.created_at,
                ],
                P2pOrder.status == P2pOrder.Status.FINISHED
        ):
            _date = v.created_at.date()
            _usd = daily_price_map[_date].get(v.base, Decimal()) * v.base_amount
            buy_user_id = v.customer_id if v.side is P2pBusinessType.BUY else v.merchant_id
            sell_user_id = v.customer_id if v.side is P2pBusinessType.SELL else v.merchant_id
            if _date >= report_date - timedelta(days=30):
                p2p_deal_usd_ret[UserTag.RECENT_30D_P2P_DEAL_USD][buy_user_id] += _usd
                p2p_deal_usd_ret[UserTag.RECENT_30D_P2P_DEAL_USD][sell_user_id] += _usd
                p2p_deal_usd_ret[UserTag.RECENT_30D_P2P_BUY_USD][buy_user_id] += _usd
                p2p_deal_usd_ret[UserTag.RECENT_30D_P2P_SELL_USD][sell_user_id] += _usd
                p2p_deal_count_ret[UserTag.RECENT_30D_P2P_DEAL_COUNT][buy_user_id] += 1
                p2p_deal_count_ret[UserTag.RECENT_30D_P2P_DEAL_COUNT][sell_user_id] += 1
                p2p_deal_asset_ret[UserTag.RECENT_30D_P2P_DEAL_ASSETS][buy_user_id].update([v.base, v.quote])
                p2p_deal_asset_ret[UserTag.RECENT_30D_P2P_DEAL_ASSETS][sell_user_id].update([v.base, v.quote])
            p2p_deal_usd_ret[UserTag.RECENT_90D_P2P_DEAL_USD][buy_user_id] += _usd
            p2p_deal_usd_ret[UserTag.RECENT_90D_P2P_DEAL_USD][sell_user_id] += _usd
            p2p_deal_usd_ret[UserTag.RECENT_90D_P2P_BUY_USD][buy_user_id] += _usd
            p2p_deal_usd_ret[UserTag.RECENT_90D_P2P_SELL_USD][sell_user_id] += _usd
            p2p_deal_count_ret[UserTag.RECENT_90D_P2P_DEAL_COUNT][buy_user_id] += 1
            p2p_deal_count_ret[UserTag.RECENT_90D_P2P_DEAL_COUNT][sell_user_id] += 1
        datas0 = [
            p2p_deal_usd_ret,
            p2p_deal_count_ret,
            p2p_deal_asset_ret,
        ]
        for data in datas0:
            for _tag, _data in data.items():
                for _user_id, _value in _data.items():
                    insert_records.append(
                        write_model(
                            user_id=_user_id,
                            tag=_tag.name,
                            value=data_dumps(_value)
                        )
                    )
        for obj in datas0:
            del obj
        write_model.query.filter(write_model.tag.in_([_tag.name for _tag in self.impl_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)


class UserBehaviorAggregateHandler(SimpleTagSaveMixin, AggregateHandler):
    read_slot = frozenset([19])
    write_slot = frozenset([19])

    priority = 997

    impl_tags = {
        UserTag.HAS_ONLY_SPOT_TRADE,
    }

    tag_relations = [
        TagRelation(
            tags={UserTag.HAS_ONLY_SPOT_TRADE, },
            requires={TagRequirement.ONLY_SPOT_MARKET_TRADE_VOL, }
        ),
    ]

    def flush(self, report_date: Optional[date]):
        """增量更新"""
        flush_data = {
            _tag: {}
            for _tag in self.impl_tags
        }
        for v in yield_get_user_latest_data(
                UserTagRequirement,
                [UserTagRequirement.user_id, ],
                UserTagRequirement.tag == TagRequirement.ONLY_SPOT_MARKET_TRADE_VOL.name
        ):
            flush_data[UserTag.HAS_ONLY_SPOT_TRADE][v["user_id"]] = True
        convert_data = {
            _tag.name: [(_user_id, data_dumps(_value)) for _user_id, _value in _all_tag_data.items()]
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data


class PerpetualProfitHandler(TagHandler):

    read_slot = frozenset([2])
    write_slot = frozenset([2])

    impl_tags = {
        UserTag.RECENT_30D_PERPETUAL_PROFIT_USD,
    }

    def save(self, report_date: Optional[date]):
        sub_account_mapping = get_sub_account_mapping()
        mm_user_ids = set(MarketMakerHelper.list_all_maker_ids())
        disabled_user_ids = get_disabled_user_ids()
        q = UserTradeSummary.query.filter(
            UserTradeSummary.system == UserTradeSummary.System.PERPETUAL,
            UserTradeSummary.report_date < report_date,
            UserTradeSummary.report_date >= (report_date - timedelta(days=30)),
        ).with_entities(UserTradeSummary.user_id).all()
        main_uids = {v.user_id for v in q}
        main_to_sub = defaultdict(set)
        for sub_uid, main_uid in sub_account_mapping.items():
            main_to_sub[main_uid].add(sub_uid)
        uids = set(main_uids)
        for main_uid in main_uids:
            if main_uid in mm_user_ids:
                continue
            uids |= main_to_sub[main_uid]
        perpetual_position_tables = PerpetualHistoryDB().users_to_dbs_and_tables(
            uids,
            "position_history")
        start_ts = datetime_to_time(date_to_datetime(report_date - timedelta(days=30)))
        end_ts = datetime_to_time(date_to_datetime(report_date))
        daily_price_map = AssetPrice.get_close_price_range_map(
            start_date=report_date - timedelta(days=30),
            end_date=report_date)
        profit_usd_ret = defaultdict(Decimal)
        market_to_balances = {}
        for db_conf in perpetual_position_tables:
            db_name = db_conf[0]
            for table_name, _uids in db_conf[1].items():
                _table = db_name.table(table_name)
                for ids in batch_iter(_uids, 5000):
                    id_str = ','.join([str(v) for v in ids])
                    _records = _table.select(
                        *['user_id', 'update_time', 'market', 'profit_real'],
                        where=f' user_id in ({id_str}) AND update_time >= {start_ts} AND update_time < {end_ts} ',
                    )
                    for r in _records:
                        user_id, update_time, market, profit_real = r
                        balance_asset = self.get_market_balance_asset(market, market_to_balances)
                        if not balance_asset:
                            continue
                        _date = timestamp_to_date(update_time)
                        if balance_asset == 'USD':
                            price = Decimal('1')
                        else:
                            price = daily_price_map[_date].get(balance_asset, Decimal())
                        main_uid = sub_account_mapping.get(user_id, user_id)
                        _usd = price * profit_real
                        profit_usd_ret[main_uid] += _usd

        insert_records = []
        write_model = self.get_write_model()

        for _uid, value in profit_usd_ret.items():
            if _uid in disabled_user_ids:
                continue
            insert_records.append(
                write_model(
                    user_id=_uid,
                    tag=UserTag.RECENT_30D_PERPETUAL_PROFIT_USD.name,
                    value=data_dumps(value)
                )
            )
        write_model.query.filter(
            write_model.tag.in_(
                [tag.name for tag in self.impl_tags])
        ).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)

    @staticmethod
    def get_market_balance_asset(market, market_to_balances) -> None | str:
        if market not in market_to_balances:
            market_info = PerpetualMarketCache().get_market_info(market)
            if not market_info:
                market_info = PerpetualOfflineMarketCache().get_market_info(market)
            if not market_info:
                market_to_balances[market] = None
            else:
                balance_asset = PerpetualMarketCache.get_balance_asset(market_info)
                market_to_balances[market] = balance_asset
        return market_to_balances[market]


class CopyTradingDataHandler(TagHandler):

    read_slot = frozenset([23])
    write_slot = frozenset([23])

    impl_tags = {
        UserTag.RECENT_30D_PERPETUAL_FOLLOW_USD,
        UserTag.RECENT_90D_PERPETUAL_FOLLOW_USD,
        UserTag.PERPETUAL_30D_FOLLOW_DAYS,
        UserTag.PERPETUAL_90D_FOLLOW_DAYS,
        UserTag.TOTAL_PERPETUAL_FOLLOW_DAYS,
    }

    def save(self, report_date: Optional[date]):
        disabled_user_ids = get_disabled_user_ids()

        q = CopyFollowerHistory.query.order_by(
            CopyFollowerHistory.id.asc()
        ).with_entities(
            CopyFollowerHistory.user_id,
            CopyFollowerHistory.sub_user_id,
            CopyFollowerHistory.created_at.label("started_at"),
            CopyFollowerHistory.finished_at,
            CopyFollowerHistory.fund_amount,
        ).all()

        def get_dates_in_range(start_dt, end_dt) -> set:
            _current_date = start_dt.date()
            _end_date = end_dt.date()

            date_list = set()
            while _current_date <= _end_date:
                date_list.add(_current_date)
                _current_date += timedelta(days=1)

            return date_list

        def count_dates_in_range(start_date: date, end_date: date, date_set: set):
            return sum(1 for d in date_set if start_date <= d <= end_date)

        def intervals_overlap(interval1: tuple[date, date], interval2: tuple[date, date]) -> bool:
            # interval1 和 interval2 都是 (start, end) 形式的元组
            start1, end1 = interval1
            start2, end2 = interval2
            # 判断两个区间是否重合
            return end1 >= start2 and end2 >= start1

        user_follow_date_data = defaultdict(set)
        user_follow_date_amount_data = defaultdict(Decimal)
        user_follow_sub_account_mapping = defaultdict(set)
        for v in q:
            if v.finished_at and v.started_at:
                dates = get_dates_in_range(v.started_at, v.finished_at)
                user_follow_date_data[v.user_id] |= dates
                user_follow_date_amount_data[(v.user_id, v.started_at.date(), v.finished_at.date())] += v.fund_amount
            elif v.started_at:
                dates = get_dates_in_range(v.started_at, date_to_datetime(report_date))
                user_follow_date_data[v.user_id] |= dates
                user_follow_date_amount_data[(v.user_id, v.started_at.date(), today())] += v.fund_amount
            user_follow_sub_account_mapping[v.user_id].add(v.sub_user_id)

        user_follow_date_count_data = defaultdict(int)
        user_follow_amount_data = defaultdict(Decimal)

        for _uid, _dates in user_follow_date_data.items():
            user_follow_date_count_data[(_uid, UserTag.PERPETUAL_30D_FOLLOW_DAYS)] = \
                count_dates_in_range(report_date - timedelta(days=30),
                                     report_date - timedelta(days=1),
                                     _dates)
            user_follow_date_count_data[(_uid, UserTag.PERPETUAL_90D_FOLLOW_DAYS)] = \
                count_dates_in_range(report_date - timedelta(days=90),
                                     report_date - timedelta(days=1),
                                     _dates)
            user_follow_date_count_data[(_uid, UserTag.TOTAL_PERPETUAL_FOLLOW_DAYS)] = len(_dates)

        insert_records = []
        write_model = self.get_write_model()

        for _key, _amount in user_follow_date_amount_data.items():
            _uid, _s_date, _e_date = _key
            if _uid in disabled_user_ids:
                continue
            in_30days = intervals_overlap((report_date - timedelta(days=30), report_date - timedelta(days=1)),
                                          (_s_date, _e_date))
            in_90days = intervals_overlap((report_date - timedelta(days=90), report_date - timedelta(days=1)),
                                          (_s_date, _e_date))
            if in_30days:
                user_follow_amount_data[(_uid, UserTag.RECENT_30D_PERPETUAL_FOLLOW_USD)] += _amount
            if in_90days:
                user_follow_amount_data[(_uid, UserTag.RECENT_90D_PERPETUAL_FOLLOW_USD)] += _amount

        for _key, count in user_follow_date_count_data.items():
            _uid, _tag = _key
            if _uid in disabled_user_ids:
                continue
            insert_records.append(
                write_model(
                    user_id=_uid,
                    tag=_tag.name,
                    value=data_dumps(count)
                )
            )
        for _key, count in user_follow_amount_data.items():
            _uid, _tag = _key
            if _uid in disabled_user_ids:
                continue
            insert_records.append(
                write_model(
                    user_id=_uid,
                    tag=_tag.name,
                    value=data_dumps(count)
                )
            )
        write_model.query.filter(
            write_model.tag.in_(
                [tag.name for tag in self.impl_tags])
        ).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)


class DemoTradingDataHandler(TagHandler):

    read_slot = frozenset([24])
    write_slot = frozenset([24])

    impl_tags = {
        UserTag.HAS_DEMO_TRADING,
        UserTag.FIRST_DEMO_TRADING_TIME,
        UserTag.LATEST_DEMO_TRADING_TIME,
        UserTag.RECENT_7D_PDT_MARKETS,
        UserTag.RECENT_30D_PDT_MARKETS,
        UserTag.RECENT_90D_PDT_MARKETS,
        UserTag.RECENT_7D_PDT_DEAL_CNT,
        UserTag.RECENT_30D_PDT_DEAL_CNT,
        UserTag.RECENT_90D_PDT_DEAL_CNT,
        UserTag.RECENT_7D_PDT_TRADE_USD,
        UserTag.RECENT_30D_PDT_TRADE_USD,
        UserTag.RECENT_90D_PDT_TRADE_USD,
        UserTag.RECENT_30D_PDT_PROFIT_USD,
        UserTag.RECENT_60D_PDT_PROFIT_USD,
        UserTag.RECENT_90D_PDT_PROFIT_USD,
        UserTag.RECENT_30D_PDT_FEE_USD,
        UserTag.RECENT_60D_PDT_FEE_USD,
        UserTag.RECENT_90D_PDT_FEE_USD,
        UserTag.TOTAL_PDT_TRADE_USD,
        UserTag.TOTAL_PDT_PROFIT_USD,
        UserTag.TOTAL_PDT_FEE_USD,
        UserTag.TOTAL_RECEIVED_USD,
    }

    def save(self, report_date: Optional[date]):
        disabled_user_ids = get_disabled_user_ids()
        write_model = self.get_write_model()
        sub_main_mapping = get_sub_account_mapping()
        client = RESTClient(config["CLIENT_CONFIGS"]["demo_trading"]["internal_url"])
        insert_records = []
        params = dict(page=1, limit=1000, tag=UserTag.RECENT_7D_PDT_DEAL_CNT.name)
        user_tag_data = {
            _tag: {}
            for _tag in self.impl_tags
        }
        data = client.get("/exchange/report/user-tag", **params)
        if data["code"] != 0:
            current_app.logger.warning(f"{report_date} demo trading user tag data not finished")
            return
        for _tag in self.impl_tags:
            # 合约模拟盘不支持子账号, 不需要合并统计
            finish = False
            params = dict(page=1, limit=1000, tag=_tag.name)
            while not finish:
                try:
                    data = client.get("/exchange/report/user-tag", **params)['data']
                    if not data["has_next"]:
                        finish = True
                    else:
                        params["page"] = params["page"] + 1
                    for item in data["records"]:
                        _uid, value_str = item['user_id'], item["value"]
                        user_tag_data[_tag][_uid] = value_str

                except Exception as e: # noqa
                    current_app.logger.error(f'{_tag} data error, {params=}, {e!r}')
                    finish = True

        for _tag, _tag_data in user_tag_data.items():
            for _uid, _value_str in _tag_data.items():
                if _uid in disabled_user_ids or _uid in sub_main_mapping:
                    continue
                insert_records.append(
                    write_model(
                        user_id=_uid,
                        tag=_tag.name,
                        value=_value_str
                    )
                )
        write_model.query.filter(write_model.tag.in_([_tag.name for _tag in self.impl_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)
