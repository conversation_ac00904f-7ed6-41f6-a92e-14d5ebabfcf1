from collections import defaultdict
from datetime import date
from typing import List, Dict, Any, NamedTuple, Set, Optional, Tu<PERSON>, Union

from sqlalchemy import case

from app.caches.user_tag import FinishedTagRequirementCache, FinishedTagDataCache
from app.models import db
from app.models.user_tag import UserTag, TagRequirement, USER_TAG_DATA_TABLES_MAPPING
from app.utils import batch_iter
from app.business.user_tag.helper import get_disabled_user_ids


class TagRelation(NamedTuple):
    tags: Set[UserTag]
    requires: Set[TagRequirement]


class BaseHandlerMeta(type):
    _supported_tags: Set[UserTag] = set()
    _supported_requires: Set[TagRequirement] = set()
    _handlers: Dict = {}
    _tag_handler_mapping: Dict = {}
    # not include requirement handlers
    _handler_read_table_mapping: Dict = {}
    _handler_write_table_mapping: Dict = {}
    _require_handler_mapping: Dict = {}
    _priority_mapping: Dict = {}
    _default_priority: int = 1

    def __new__(mcs, name, bases, dct):
        cls = super().__new__(mcs, name, bases, dct)
        _tag_attr = 'impl_tags'
        _require_attr = 'impl_requires'
        _relations_attr = 'tag_relations'
        _priority_attr = "priority"
        _read_slot_attr = "read_slot"
        _write_slot_attr = "write_slot"
        _default_read_slot = frozenset([1])
        _default_write_slot = frozenset([1])
        impl_tags = getattr(cls, _tag_attr, set())
        impl_requires = getattr(cls, _require_attr, set())
        _priority = getattr(cls, _priority_attr, mcs._default_priority)
        if not impl_tags and (not impl_requires):
            return cls
        read_slot = getattr(cls, _read_slot_attr, _default_read_slot)
        write_slot = getattr(cls, _write_slot_attr, _default_write_slot)
        if len(read_slot) > 1 or len(write_slot) > 1:
            raise ValueError(f'{read_slot:} or {write_slot:} config error, only support one element')
        read_slot, write_slot = list(read_slot)[0], list(write_slot)[0]
        slot_range = min(USER_TAG_DATA_TABLES_MAPPING.keys()), max(USER_TAG_DATA_TABLES_MAPPING.keys())
        if not slot_range[0] <= read_slot <= slot_range[1]:
            raise ValueError(f'{read_slot:} config error, only allow in range {slot_range}')
        if not slot_range[0] <= write_slot <= slot_range[1]:
            raise ValueError(f'{write_slot:} config error, only allow in range {slot_range}')

        if not impl_tags:
            cls.impl_tags = set()
        if not impl_requires:
            cls.impl_requires = set()
        tag_relations_list = getattr(cls, _relations_attr, [])
        if not all([isinstance(_v, set) for _v in (impl_tags, impl_requires)]):
            raise ValueError(f'attribute type error')
        if not isinstance(tag_relations_list, list):
            raise ValueError(f'attribute type error')

        if not all([isinstance(_tag, UserTag) for _tag in impl_tags]):
            raise ValueError(f'{cls.__name__} impl_tags definition error')

        for _tag in impl_tags:
            if _tag in mcs._supported_tags:
                raise ValueError(f'user tag {_tag} is already supported')

        if not all([isinstance(_require, TagRequirement) for _require in impl_requires]):
            raise ValueError(f'{cls.__name__} impl_requires definition error')

        for _require in impl_requires:
            if _require in mcs._supported_requires:
                raise ValueError(f'tag require {_require} is already supported')
        if not all([isinstance(_relation, TagRelation) for _relation in tag_relations_list]):
            raise ValueError(f'{cls.__name__} impl_tag_relations definition error')
        requires = {req for v in tag_relations_list for req in v.requires}
        if not_supported_reqs := requires - (mcs._supported_requires | impl_requires):
            raise ValueError(f'{cls.__name__} tag_relations reqs not fully supported,'
                             f'data {not_supported_reqs}')
        relation_tags = {_tag for v in tag_relations_list for _tag in v.tags}
        if relation_tags - impl_tags:
            raise ValueError(f'{cls.__name__} tags in tag_relations not match with impl_tags, '
                             f'data {relation_tags - impl_tags}')

        if cls.__name__ in mcs._handlers:
            raise ValueError(f"tag handler name {cls.__name__} has exists")

        for _tag in impl_tags:
            mcs._tag_handler_mapping[_tag] = cls
        for _require in impl_requires:
            mcs._require_handler_mapping[_require] = cls
        mcs._supported_tags |= impl_tags
        mcs._supported_requires |= impl_requires
        mcs._priority_mapping[cls.__name__] = _priority
        mcs._handlers[cls.__name__] = cls
        mcs._handler_read_table_mapping[cls.__name__] = USER_TAG_DATA_TABLES_MAPPING[read_slot]
        mcs._handler_write_table_mapping[cls.__name__] = USER_TAG_DATA_TABLES_MAPPING[write_slot]
        return cls

    @classmethod
    def get_priority_mapping(mcs):
        return mcs._priority_mapping

    @classmethod
    def get_default_priority(mcs):
        return mcs._default_priority

    @classmethod
    def get_tag_handler(mcs, tag: UserTag):
        return mcs._tag_handler_mapping[tag]

    @classmethod
    def get_supported_tags(mcs) -> Set[UserTag]:
        return mcs._supported_tags

    @classmethod
    def get_not_supported_tags(mcs) -> Set[UserTag]:
        tags = mcs._supported_tags
        return {tag for tag in UserTag if tag not in tags}

    @classmethod
    def get_require_handler(mcs, _require: TagRequirement):
        return mcs._require_handler_mapping.get(_require)

    @classmethod
    def get_supported_requires(mcs) -> Set[TagRequirement]:
        return mcs._supported_requires

    @classmethod
    def get_not_supported_requires(mcs) -> Set[TagRequirement]:
        reqs = mcs._supported_requires
        return {_req for _req in TagRequirement if _req not in reqs}

    @classmethod
    def get_handler_by_name(mcs, _name: str):
        return mcs._handlers[_name]

    @classmethod
    def tag_handler_mapping(mcs) -> Dict[UserTag, Any]:
        return {_tag: _handler for _tag, _handler in mcs._tag_handler_mapping.items()}

    @classmethod
    def get_all_handler_names(mcs) -> List[str]:
        return [_name for _name in mcs._handlers]

    @classmethod
    def get_handler_write_table(mcs, _name) -> db.Model:
        return mcs._handler_write_table_mapping[_name]

    @classmethod
    def get_handler_read_table(mcs, _name) -> db.Model:
        return mcs._handler_read_table_mapping[_name]

    @classmethod
    def get_tag_write_table(mcs, tag: UserTag) -> db.Model:
        handler = mcs.get_tag_handler(tag)
        return mcs._handler_write_table_mapping[handler.__name__]

    @classmethod
    def get_tag_read_table(mcs, tag: UserTag) -> db.Model:
        handler = mcs.get_tag_handler(tag)
        return mcs._handler_read_table_mapping[handler.__name__]


class CacheSetMixin:

    @classmethod
    def mark_finished(cls, report_date: date):
        tag_cache = FinishedTagDataCache(report_date)
        req_cache = FinishedTagRequirementCache(report_date)
        # noinspection PyUnresolvedReferences
        if cls.impl_tags:
            # noinspection PyUnresolvedReferences
            tag_cache.sadd(*[_tag.name for _tag in cls.impl_tags])
            tag_cache.expire(tag_cache.expired_seconds)
        # noinspection PyUnresolvedReferences
        if cls.impl_requires:
            # noinspection PyUnresolvedReferences
            req_cache.sadd(*[_req.name for _req in cls.impl_requires])
            req_cache.expire(req_cache.expired_seconds)


class SimpleTagSaveMixin:

    def save(self, report_date: Optional[date]):
        new_data = self.flush(report_date)
        delete_or_update_user_tag_data(self.impl_tags, new_data)
        if report_date:
            self.mark_finished(report_date)


class TagHandler(CacheSetMixin, metaclass=BaseHandlerMeta):
    # 简单标签数据类(无数据依赖)
    impl_tags = set()

    def get_write_model(self):
        handler_name = self.__class__.__name__
        write_model: db.Model = get_handler_write_table(handler_name)
        return write_model

    def get_read_model(self):
        handler_name = self.__class__.__name__
        read_model: db.Model = get_handler_write_table(handler_name)
        return read_model

    def check_finished(self, report_date: date):
        tag_names = {_tag.name for _tag in self.impl_tags}
        if tag_names & FinishedTagDataCache(report_date).smembers():
            return True
        return False

    def save(self, report_date: Optional[date]):
        raise NotImplementedError


class RequirementHandler(CacheSetMixin, metaclass=BaseHandlerMeta):
    # 标签依赖数据类
    impl_requires = set()

    def check_finished(self, report_date: date):
        req_names = {_req.name for _req in self.impl_requires}
        if req_names & FinishedTagRequirementCache(report_date).smembers():
            return True
        return False

    def save(self, report_date: Optional[date]):
        raise NotImplementedError


# 聚合生成
class AggregateHandler(CacheSetMixin, metaclass=BaseHandlerMeta):
    # TagRequirement -> UserTag
    # 有依赖数据的标签
    impl_tags = set()
    # 只有聚合类的标签才有这个定义
    tag_relations = []

    def get_write_model(self):
        handler_name = self.__class__.__name__
        write_model: db.Model = get_handler_write_table(handler_name)
        return write_model

    def get_read_model(self):
        handler_name = self.__class__.__name__
        read_model: db.Model = get_handler_write_table(handler_name)
        return read_model

    def check_finished(self, report_date: date):
        tag_names = {_tag.name for _tag in self.impl_tags}
        if tag_names & FinishedTagDataCache(report_date).smembers():
            return True
        return False

    def check_relations_finished(self, report_date: date):
        req_names = {req.name for v in self.tag_relations for req in v.requires}
        if req_names - FinishedTagRequirementCache(report_date).smembers():
            return False
        return True

    def save(self, report_date: Optional[date]):
        raise NotImplementedError


get_all_handler_names = BaseHandlerMeta.get_all_handler_names
get_handler_by_name = BaseHandlerMeta.get_handler_by_name
get_not_supported_tags = BaseHandlerMeta.get_not_supported_tags
get_supported_tags = BaseHandlerMeta.get_supported_tags
get_handler_priority_mapping = BaseHandlerMeta.get_priority_mapping

get_handler_write_table = BaseHandlerMeta.get_handler_write_table
get_handler_read_table = BaseHandlerMeta.get_handler_read_table
get_tag_write_table = BaseHandlerMeta.get_tag_write_table
get_tag_read_table = BaseHandlerMeta.get_tag_read_table
get_default_priority = BaseHandlerMeta.get_default_priority


def batch_update_with_case_sql(update_data: dict, write_model: db.Model, limit: int = 5000):
    _ids = sorted(update_data.keys())
    for _bids in batch_iter(_ids, limit):
        # 构造 CASE 表达式
        case_statement = case(
            {_id: update_data[_id] for _id in _bids},
            value=write_model.id
        )
        # 执行更新
        write_model.query.filter(write_model.id.in_(_bids)).update(
            {write_model.value: case_statement},
            synchronize_session=False
        )


def delete_or_update_user_tag_data(
        user_tag_set: Set[UserTag],
        new_result: Dict[str, Union[Dict[int, str], List[Tuple[int, str]]]]):
    # 这里需要保证user_tag_set是在同一个handler里面的
    write_table = [get_tag_write_table(tag) for tag in user_tag_set][0]
    history_query = write_table.query.filter(
        write_table.tag.in_(
            [
                _tag.name
                for _tag in user_tag_set
            ]
        )
    ).all()
    # data format
    # {UserTag.XXX.name: {user_id: value, ...}, UserTag.YYY.name: {user_id: value, ...}}
    history_data = defaultdict(dict)
    id_mapping = defaultdict(dict)
    disable_user_ids = get_disabled_user_ids()
    for _v in history_query:
        history_data[_v.tag][_v.user_id] = _v.value
        id_mapping[_v.tag][_v.user_id] = _v.id
    insert_data = defaultdict(dict)
    update_data = defaultdict(dict)
    for _tag, _all_tag_data in new_result.items():
        if isinstance(_all_tag_data, dict):
            for _user_id, _value in _all_tag_data.items():
                if _user_id in disable_user_ids:
                    continue
                if _user_id not in history_data[_tag]:
                    insert_data[_tag][_user_id] = _value
                elif history_data[_tag][_user_id] != _value:
                    update_data[_tag][_user_id] = _value
        elif isinstance(_all_tag_data, list):
            for (_user_id, _value) in _all_tag_data:
                if _user_id in disable_user_ids:
                    continue
                if _user_id not in history_data[_tag]:
                    insert_data[_tag][_user_id] = _value
                elif history_data[_tag][_user_id] != _value:
                    update_data[_tag][_user_id] = _value
    insert_records = []
    for _tag, _users_data in insert_data.items():
        for _user_id, _value in _users_data.items():
            insert_records.append(
                write_table(
                    tag=_tag,
                    user_id=_user_id,
                    value=_value
                )
            )
    for _tag, _users_data in update_data.items():
        build_update_data = dict()
        for _user_id, _value in _users_data.items():
            build_update_data[id_mapping[_tag][_user_id]] = _value
        batch_update_with_case_sql(build_update_data, write_table)
    for objs in batch_iter(insert_records, 5000):
        db.session.bulk_save_objects(objs)
    db.session.commit()


def update_single_user_tag_data(user_tag_name: str,
                                tag_result: Union[Dict[int, str], List[Tuple[int, str]]]):
    write_table = get_tag_write_table(UserTag[user_tag_name])
    history_query = write_table.query.filter(
        write_table.tag == user_tag_name).all()
    # data format
    # {UserTag.XXX.name: {user_id: value, ...}, UserTag.YYY.name: {user_id: value, ...}}
    history_data = dict()
    id_mapping = dict()
    for _v in history_query:
        history_data[_v.user_id] = _v.value
        id_mapping[_v.user_id] = _v.id
    insert_data = dict()
    update_data = dict()
    if isinstance(tag_result, dict):
        for _user_id, _value in tag_result.items():
            if _user_id not in history_data:
                insert_data[_user_id] = _value
            elif history_data[_user_id] != _value:
                update_data[_user_id] = _value
    elif isinstance(tag_result, list):
        for (_user_id, _value) in tag_result:
            if _user_id not in history_data:
                insert_data[_user_id] = _value
            elif history_data[_user_id] != _value:
                update_data[_user_id] = _value
    else:
        raise ValueError
    insert_records = []
    for _user_id, _value in insert_data.items():
        insert_records.append(
            write_table(
                tag=user_tag_name,
                user_id=_user_id,
                value=_value
            )
        )
    build_update_data = dict()
    for _user_id, _value in update_data.items():
        build_update_data[id_mapping[_user_id]] = _value

    batch_update_with_case_sql(build_update_data, write_table)
    db.session.commit()

    for objs in batch_iter(insert_records, 5000):
        db.session.bulk_save_objects(objs)

    db.session.commit()
