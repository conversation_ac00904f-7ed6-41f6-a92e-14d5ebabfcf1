import json
import typing
from collections import defaultdict
from typing import Set, List, Dict, NamedTuple, Callable, Optional, Any, Iterable, Union

from flask import current_app

from app.business.fiat import get_fiat_partners
from app.business.user_tag.handlers.base import get_supported_tags, get_tag_read_table
from app.business.user_tag.helper import data_loads, get_disabled_user_ids
from app.business.fee_constant import VIP_LEVEL_DICT, MARKET_MAKER_DICT
from app.business.referral import AMBASSADOR_SOURCE
from app.business.user import UserRepository
from app.common import list_country_codes_3, get_country, Platform, LANGUAGE_NAMES, AreaInfo, Currency, ShareUserTag
from app.assets import list_all_assets
from app.business import UserPreferences, cached
from app.exceptions import InvalidArgument
from app.models import Market, PerpetualMarket, Ambassador, PublicityChannel, \
    ShortLinkInfo
from app.models.activity import Coupon, CouponDistribution
from app.models.user_tag import (
    UserTag, TagRawValueType, OperatorType, RuleType,
    GroupRuleType, get_all_fiat_party_tag, AllowType, is_filter_user,
)
from app.utils import batch_iter, timestamp_to_datetime, amount_to_str

DEFAULT_SUPPORTED_OPERATORS = [
    OperatorType.LE,
    OperatorType.GT,
    OperatorType.LT,
    OperatorType.GE,
    OperatorType.EQ,
    OperatorType.NE,
    OperatorType.BETWEEN,
    OperatorType.IN,
    OperatorType.NOT_IN
]


RAW_TYPE_SUPPORTED_OPERATORS = {
    TagRawValueType.BOOL: (OperatorType.EQ, OperatorType.NE),
    TagRawValueType.INT:  (
                            OperatorType.LE,
                            OperatorType.GT,
                            OperatorType.LT,
                            OperatorType.GE,
                            OperatorType.EQ,
                            OperatorType.NE,
                            OperatorType.BETWEEN
                            ),
    TagRawValueType.DECIMAL: (
                            OperatorType.LE,
                            OperatorType.GT,
                            OperatorType.LT,
                            OperatorType.GE,
                            OperatorType.EQ,
                            OperatorType.NE,
                            OperatorType.BETWEEN
                            ),
    TagRawValueType.TIME: DEFAULT_SUPPORTED_OPERATORS,
    TagRawValueType.STRING: [OperatorType.EQ, OperatorType.NE,
                             OperatorType.IN, OperatorType.NOT_IN]
}


def get_bool_options(only_true: bool = False) -> List:
    if only_true:
        return [dict(label="是", value=True)]
    return [dict(label="是", value=True), dict(label="否", value=False)]


def get_asset_and_fiat_options() -> List:
    return get_asset_options() + get_fiat_options()


def get_asset_options() -> List:
    return [dict(label=_asset, value=_asset) for _asset in list_all_assets()]


def get_fiat_options() -> List:
    return [dict(label=fiat.name, value=fiat.name) for fiat in Currency]


@cached(60 * 5)
def get_spot_market_options() -> List:
    names = [v.name for v in
             Market.query.filter(
                 Market.status == Market.Status.ONLINE
             ).with_entities(Market.name).all()]
    return [dict(label=_name, value=_name) for _name in names]


@cached(60 * 5)
def get_perpetual_market_options() -> List:
    names = [v.name for v in
             PerpetualMarket.query.filter(
                 PerpetualMarket.status == PerpetualMarket.StatusType.OPEN
             ).with_entities(PerpetualMarket.name).all()]
    return [dict(label=_name, value=_name) for _name in names]


@cached(60 * 5)
def get_all_market_options() -> List:
    names = [v.name for v in
             Market.query.filter(
                 Market.status == Market.Status.ONLINE
             ).with_entities(Market.name).all()] + \
            [v.name for v in
             PerpetualMarket.query.filter(
                 PerpetualMarket.status == PerpetualMarket.StatusType.OPEN
             ).with_entities(PerpetualMarket.name).all()]
    names = list(set(names))
    return [dict(label=_name, value=_name) for _name in names]


@cached(60 * 5)
def get_publicity_channel_options() -> List:
    records = PublicityChannel.query.all()
    channel_id_dic = dict()
    all_channels = []
    pub_name_map = PublicityChannel.get_all_pub_name_map()
    for record in records:
        channel_id_dic[record.id] = pub_name_map[record.id]
        if record.platform == PublicityChannel.Platform.APP:
            all_channels.append(pub_name_map[record.id])
    short_links = ShortLinkInfo.query.filter(
        ShortLinkInfo.status == ShortLinkInfo.StatusType.VALID
    ).with_entities(
        ShortLinkInfo.id,
        ShortLinkInfo.publicity_channel_id
    ).all()
    short_link_pc_id_dic = dict()
    for item in short_links:
        channel = f'a{item.id}'
        all_channels.append(channel)
        short_link_pc_id_dic[channel] = item.publicity_channel_id

    res = []
    res_channels = set(UserRepository.get_all_channels()) & set(all_channels)
    for channel in res_channels:
        if channel in short_link_pc_id_dic:
            publicity_channel_id = short_link_pc_id_dic[channel]
            channel = channel_id_dic[publicity_channel_id]
        res.append(dict(label=channel, value=channel))
    return res


def get_coupon_distribution_options():
    q = CouponDistribution.query.with_entities(
        CouponDistribution.id,
        CouponDistribution.name).all()
    return [dict(label=f"{v.id}_{v.name}", value=v.id) for v in q]


class UserTagProperty(NamedTuple):
    tag: UserTag
    raw_type: TagRawValueType
    options: Optional[Callable] = None
    supported_operations: Optional[List[OperatorType]] = None
    allow_types: Optional[List[AllowType]] = None

    def get_allow_types(self):
        return list(AllowType) if not self.allow_types else self.allow_types

    def get_supported_operations(self):
        return self.supported_operations if self.supported_operations else \
            RAW_TYPE_SUPPORTED_OPERATORS[self.raw_type]

    def get_display_option_value(self, value: Union[List[Any], Any]) -> str:
        values = []
        build_data = [v for v in value] if isinstance(value, list) else [value]
        for v in build_data:
            if isinstance(v, typing.Hashable):
                values.append(self.get_display_options().get(v, v))
            elif isinstance(v, list):
                values.append(json.dumps(v))
            else:
                values.append(v)
        display_values = []
        if self.raw_type is TagRawValueType.TIME:
            for v in values:
                dt = timestamp_to_datetime(v)
                dt_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                display_values.append(dt_str)
        elif self.raw_type in [TagRawValueType.DECIMAL, TagRawValueType.INT]:
            for v in values:
                display_values.append(amount_to_str(v))
        else:
            display_values = values
        return ', '.join(display_values)

    def get_display_options(self) -> Dict:
        display_options = {}
        options = self.get_options()
        for option in options:
            display_options.update({option['value']: option['label']})
        return display_options

    def get_options(self):
        if self.options is None:
            if self.raw_type == TagRawValueType.BOOL:
                return get_bool_options()
            return []
        return self.options()

    def get_value_type(self):
        if self.raw_type is TagRawValueType.BOOL:
            return 'bool'
        if self.raw_type is TagRawValueType.INT:
            return 'int'
        if self.raw_type is TagRawValueType.DECIMAL:
            return 'decimal'
        if self.raw_type is TagRawValueType.TIME:
            return 'datetime'
        if self.raw_type is TagRawValueType.STRING:
            return 'str'


def build_basic_tag_property(tag: UserTag, raw_type: TagRawValueType,
                             option_func: Optional[Callable] = None,
                             supported_operations: Optional[List] = None) -> UserTagProperty:

    return UserTagProperty(
        tag=tag,
        raw_type=raw_type,
        options=option_func,
        supported_operations=supported_operations
    )


TAG_PROPERTIES = [
    UserTagProperty(
        tag=UserTag.RECENT_30D_PERPETUAL_EVERYDAY_TRADE_USD,
        raw_type=TagRawValueType.STRING,
        allow_types=[AllowType.PORTRAIT],
        options=lambda: []
    ),
    UserTagProperty(
        tag=UserTag.EMAIL_PUSH_TYPES,
        raw_type=TagRawValueType.STRING,
        options=lambda: [
            dict(label=UserPreferences.allows_activity_emails.desc, value='activity'),
            dict(label=UserPreferences.allows_blog_emails.desc, value='blog'),
            dict(label=UserPreferences.allows_announcement_emails.desc, value='announcement'),
        ],
    ),
    UserTagProperty(
        tag=UserTag.COUPON_USER_CONVERSION,
        raw_type=TagRawValueType.STRING,
        options=get_coupon_distribution_options
    ),
    UserTagProperty(
        tag=UserTag.LANGUAGE,
        raw_type=TagRawValueType.STRING,
        options=lambda: [
            dict(label=v.chinese, value=k.name)
            for k, v in LANGUAGE_NAMES.items()
        ],
    ),
    *[
        build_basic_tag_property(tag, TagRawValueType.BOOL,
                                 lambda: get_bool_options(True),
                                 [OperatorType.EQ])
        for tag in (
            UserTag.TRIED_FIAT_SELL,
            UserTag.TRIED_FIAT_BUY,
            UserTag.USED_FIAT_SELL,
            UserTag.USED_FIAT_BUY,
        )
     ],
    *[
        build_basic_tag_property(tag, TagRawValueType.BOOL)
        for tag in (
            UserTag.CET_DISCOUNT,
            UserTag.ENABLE_MARGIN,
            UserTag.ENABLE_PERPETUAL,
            UserTag.ENABLE_PROFIT_LOSS,
            UserTag.AMBASSADOR,
            UserTag.BUSINESS_AMBASSADOR,
            UserTag.COPY_TRADER,
            UserTag.ENABLE_API,
            UserTag.AMBASSADOR_AGENT,
            UserTag.MARKET_MAKER,
            UserTag.HAS_KYC,
            UserTag.IS_REFERER,
            UserTag.JOIN_AIRDROP,
            UserTag.JOIN_DOCK,
            UserTag.JOIN_MINING,
            UserTag.JOIN_COUPON,
            UserTag.HAS_SPOT_TRADE,
            UserTag.HAS_PERPETUAL_TRADE,
            UserTag.HAS_PENDING_ORDER,
            UserTag.HAS_PENDING_POSITION,
            UserTag.HAS_TRADE,
            UserTag.HAS_MOBILE,
            UserTag.HAS_TOTP,
            UserTag.HAS_ONCHAIN_DEPOSIT,
            UserTag.HAS_AMM,
            UserTag.HAS_EXCHANGE,
            UserTag.HAS_INVEST,
            UserTag.HAS_PLEDGE,
            UserTag.HAS_P2P,
            UserTag.HAS_COPY_TRADING,
            UserTag.HAS_STAKING,
            UserTag.HAS_ASSET_CONVERSION,
            UserTag.HAS_DEMO_TRADING,
            UserTag.VIEW_PERPETUAL_TUTORIAL_QUESTION,
            UserTag.VIEW_PERPETUAL_TUTORIAL_VIDEO,
            UserTag.JOIN_PERPETUAL_TRADE_ACTIVITY,
            UserTag.JOIN_DIBS,
            UserTag.IS_POOL_USER,
            UserTag.FIRST_PERPETUAL_HAS_PROFIT,
            UserTag.HAS_AUTO_INVEST_PLAN,
            UserTag.HAS_SPOT_GRID_STRATEGY,
            UserTag.IS_DEVICE_ID_UNIQUE_FOR_USER,
            UserTag.IS_MARGIN_USER,
            UserTag.HAS_ONLY_SPOT_TRADE,
        )
    ],
    UserTagProperty(
        tag=UserTag.REFERER_ID,
        raw_type=TagRawValueType.INT,
    ),
    UserTagProperty(
        tag=UserTag.VIP_LEVEL,
        raw_type=TagRawValueType.INT,
        options=lambda: [dict(label=str(v), value=v) for v in [0] + list(VIP_LEVEL_DICT.keys())],
    ),
    UserTagProperty(
        tag=UserTag.MARKET_MAKER_LEVEL,
        raw_type=TagRawValueType.INT,
        options=lambda: [dict(label=str(v), value=v) for v in [0] + list(MARKET_MAKER_DICT.keys())],
    ),
    UserTagProperty(
        tag=UserTag.AMBASSADOR_LEVEL,
        raw_type=TagRawValueType.STRING,
        options=lambda: [dict(label=v.name, value=v.name) for v in Ambassador.Level],
    ),
    UserTagProperty(
        tag=UserTag.AMBASSADOR_SOURCE,
        raw_type=TagRawValueType.STRING,
        options=lambda: [dict(label=v, value=k) for k, v in AMBASSADOR_SOURCE.items()],
    ),
    UserTagProperty(
        tag=UserTag.COUNTRY,
        raw_type=TagRawValueType.STRING,
        options=lambda: [
            dict(label=get_country(v).cn_name, value=v)
            for v in list_country_codes_3()
        ],
    ),
    UserTagProperty(
        tag=UserTag.AREA,
        raw_type=TagRawValueType.STRING,
        options=lambda: [
            dict(label=v.value, value=v.name)
            for v in AreaInfo
        ],
    ),
    UserTagProperty(
        tag=UserTag.REFER_SOURCE,
        raw_type=TagRawValueType.STRING,
        options=lambda: [
            dict(label="自然注册", value="NONE"),
            dict(label="普通推荐", value="NORMAL"),
            dict(label="大使推荐", value="AMBASSADOR"),
        ],
    ),
    UserTagProperty(
        tag=UserTag.PLATFORM,
        raw_type=TagRawValueType.STRING,
        options=lambda: [
            dict(label=Platform.WEB.name, value=Platform.WEB.name),
            dict(label=Platform.ANDROID.name, value=Platform.ANDROID.name),
            dict(label=Platform.IOS.name, value=Platform.IOS.name),
        ],
    ),
    *[
        build_basic_tag_property(tag, TagRawValueType.TIME)
        for tag in [

            UserTag.REGISTER_TIME,
            UserTag.ENABLE_PERPETUAL_TIME,
            UserTag.FIRST_DEPOSIT_TIME,
            UserTag.LATEST_DEPOSIT_TIME,
            UserTag.LATEST_WITHDRAW_TIME,
            UserTag.LATEST_PUSH_READ_TIME,
            UserTag.LATEST_EMAIL_READ_TIME,
            UserTag.FIRST_TRADE_TIME,
            UserTag.LATEST_TRADE_TIME,
            UserTag.FIRST_SPOT_TRADE_TIME,
            UserTag.LATEST_SPOT_TRADE_TIME,
            UserTag.FIRST_PERPETUAL_TRADE_TIME,
            UserTag.LATEST_PERPETUAL_TRADE_TIME,
            UserTag.FIRST_EXCHANGE_TIME,
            UserTag.LATEST_EXCHANGE_TIME,
            UserTag.FIRST_INVEST_TIME,
            UserTag.LATEST_INVEST_TIME,
            UserTag.FIRST_AMM_TIME,
            UserTag.LATEST_AMM_TIME,
            UserTag.FIRST_MARGIN_LOAN_TIME,
            UserTag.LATEST_MARGIN_LOAN_TIME,
            UserTag.FIRST_PLEDGE_TIME,
            UserTag.LATEST_PLEDGE_TIME,
            UserTag.FIRST_AUTO_INVEST_TIME,
            UserTag.FIRST_SPOT_GRID_TIME,
            UserTag.FIRST_P2P_TIME,
            UserTag.LATEST_P2P_TIME,
            UserTag.FIRST_ASSET_CONVERSION_TIME,
            UserTag.LATEST_ACTIVE_TIME,
            UserTag.LATEST_REFERER_REGISTER_TIME,
            UserTag.FIRST_FIAT_ORDER_TIME,
            *get_all_fiat_party_tag(),
            UserTag.FIRST_REFERER_REGISTER_TIME,
            UserTag.LATEST_USED_FIAT_TIME,
            UserTag.FIRST_MARGIN_TRADE_TIME,
            UserTag.LATEST_MARGIN_TRADE_TIME,
            UserTag.FIRST_ONLY_SPOT_TRADE_TIME,
            UserTag.LATEST_ONLY_SPOT_TRADE_TIME,

            UserTag.FIRST_DEMO_TRADING_TIME,
            UserTag.LATEST_DEMO_TRADING_TIME,
        ]
    ],
    UserTagProperty(
        tag=UserTag.USED_FIAT_PARTNERS,
        raw_type=TagRawValueType.STRING,
        options=lambda: [
            dict(label=v, value=v)
            for v in get_fiat_partners()
        ]
    ),
    *[
        build_basic_tag_property(tag, TagRawValueType.DECIMAL)
        for tag in [
            UserTag.FIRST_DEPOSIT_AMOUNT,
            UserTag.LATEST_DEPOSIT_AMOUNT,
            UserTag.FIRST_SPOT_TRADE_USD,
            UserTag.FIRST_PERPETUAL_TRADE_USD,
            UserTag.LATEST_SPOT_TRADE_USD,
            UserTag.LATEST_PERPETUAL_TRADE_USD,

            UserTag.RECENT_7D_SPOT_TRADE_USD,
            UserTag.RECENT_30D_SPOT_TRADE_USD,
            UserTag.RECENT_90D_SPOT_TRADE_USD,
            UserTag.RECENT_7D_PERPETUAL_TRADE_USD,
            UserTag.RECENT_30D_PERPETUAL_TRADE_USD,
            UserTag.RECENT_90D_PERPETUAL_TRADE_USD,
            UserTag.RECENT_7D_EXCHANGE_TRADE_USD,
            UserTag.RECENT_30D_EXCHANGE_TRADE_USD,
            UserTag.RECENT_90D_EXCHANGE_TRADE_USD,
            UserTag.RECENT_7D_TRADE_USD,
            UserTag.RECENT_30D_TRADE_USD,
            UserTag.RECENT_90D_TRADE_USD,
            UserTag.REFER_USER_7D_TRADE_USD,
            UserTag.REFER_USER_30D_TRADE_USD,
            UserTag.REFER_USER_90D_TRADE_USD,
            UserTag.MAX_POSITION_USD,
            UserTag.TOTAL_USD,
            UserTag.TOTAL_MARGIN_USD,
            UserTag.TOTAL_TRADE_USD,
            UserTag.TOTAL_SPOT_TRADE_USD,
            UserTag.TOTAL_PERPETUAL_TRADE_USD,
            UserTag.RECENT_7D_PDT_TRADE_USD,
            UserTag.RECENT_30D_PDT_TRADE_USD,
            UserTag.RECENT_90D_PDT_TRADE_USD,
            UserTag.RECENT_30D_PDT_PROFIT_USD,
            UserTag.RECENT_60D_PDT_PROFIT_USD,
            UserTag.RECENT_90D_PDT_PROFIT_USD,
            UserTag.RECENT_30D_PDT_FEE_USD,
            UserTag.RECENT_60D_PDT_FEE_USD,
            UserTag.RECENT_90D_PDT_FEE_USD,
            UserTag.TOTAL_PDT_TRADE_USD,
            UserTag.TOTAL_PDT_PROFIT_USD,
            UserTag.TOTAL_PDT_FEE_USD,
            UserTag.TOTAL_RECEIVED_USD,

            UserTag.AMM_USD,
            UserTag.INVEST_USD,
            UserTag.CET_AMOUNT,
            UserTag.RECENT_7D_INTO_INVEST_USD,
            UserTag.RECENT_30D_INTO_INVEST_USD,
            UserTag.RECENT_90D_INTO_INVEST_USD,

            UserTag.REFER_USER_TOTAL_TRADE_USD,
            UserTag.REFER_USER_TOTAL_FEE_USD,
            UserTag.FIRST_PERPETUAL_7D_TRADE_USD,
            UserTag.FIRST_PERPETUAL_30D_TRADE_USD,
            UserTag.FIRST_PERPETUAL_7D_FEE_USD,
            UserTag.FIRST_PERPETUAL_30D_FEE_USD,
            UserTag.RECENT_90D_DEPOSIT_USD,
            UserTag.RECENT_180D_DEPOSIT_USD,
            UserTag.RECENT_90D_WITHDRAWAL_USD,
            UserTag.RECENT_180D_WITHDRAWAL_USD,
            UserTag.RECENT_7D_FEE_USD,
            UserTag.RECENT_30D_FEE_USD,
            UserTag.RECENT_1Y_FEE_USD,
            UserTag.RECENT_7D_TOTAL_USD_AVG,
            UserTag.RECENT_30D_TOTAL_USD_AVG,
            UserTag.RECENT_90D_TOTAL_USD_AVG,
            UserTag.RECENT_7D_MARGIN_TRADE_USD,
            UserTag.RECENT_30D_MARGIN_TRADE_USD,
            UserTag.RECENT_90D_MARGIN_TRADE_USD,
            UserTag.TOTAL_PLEDGE_LOAN_USD,
            UserTag.TOTAL_PLEDGE_ASSET_USD,
            UserTag.RECENT_7D_PLEDGE_ASSET_USD,
            UserTag.RECENT_30D_PLEDGE_ASSET_USD,
            UserTag.RECENT_90D_PLEDGE_ASSET_USD,
            UserTag.RECENT_7D_PLEDGE_LOAN_USD,
            UserTag.RECENT_30D_PLEDGE_LOAN_USD,
            UserTag.RECENT_90D_PLEDGE_LOAN_USD,
            UserTag.RECENT_7D_PLEDGE_INTEREST_USD,
            UserTag.RECENT_30D_PLEDGE_INTEREST_USD,
            UserTag.RECENT_90D_PLEDGE_INTEREST_USD,
            UserTag.RECENT_7D_ONLY_SPOT_TRADE_USD,
            UserTag.RECENT_30D_ONLY_SPOT_TRADE_USD,
            UserTag.RECENT_90D_ONLY_SPOT_TRADE_USD,
            UserTag.FIRST_ONLY_SPOT_TRADE_USD,
            UserTag.LATEST_ONLY_SPOT_TRADE_USD,
            UserTag.RECENT_30D_P2P_DEAL_USD,
            UserTag.RECENT_90D_P2P_DEAL_USD,
            UserTag.RECENT_30D_P2P_BUY_USD,
            UserTag.RECENT_90D_P2P_BUY_USD,
            UserTag.RECENT_30D_P2P_SELL_USD,
            UserTag.RECENT_90D_P2P_SELL_USD,
            UserTag.RECENT_30D_PERPETUAL_PROFIT_USD,
            UserTag.RECENT_30D_PERPETUAL_FOLLOW_USD,
            UserTag.RECENT_90D_PERPETUAL_FOLLOW_USD,
        ]
    ],
    *[
        build_basic_tag_property(tag, TagRawValueType.STRING, get_all_market_options)
        for tag in (
            UserTag.LATEST_SPOT_TRADE_MARKETS,
            UserTag.RECENT_7D_AMM_MARKETS,
            UserTag.RECENT_30D_AMM_MARKETS,
            UserTag.RECENT_90D_AMM_MARKETS,
            UserTag.RECENT_7D_SPOT_MARKETS,
            UserTag.RECENT_30D_SPOT_MARKETS,
            UserTag.RECENT_90D_SPOT_MARKETS,
            UserTag.RECENT_7D_PERPETUAL_MARKETS,
            UserTag.RECENT_30D_PERPETUAL_MARKETS,
            UserTag.RECENT_90D_PERPETUAL_MARKETS,
            UserTag.RECENT_7D_EXCHANGE_MARKETS,
            UserTag.RECENT_30D_EXCHANGE_MARKETS,
            UserTag.RECENT_90D_EXCHANGE_MARKETS,

            UserTag.RECENT_7D_PDT_MARKETS,
            UserTag.RECENT_30D_PDT_MARKETS,
            UserTag.RECENT_90D_PDT_MARKETS,

            UserTag.SPOT_MARKETS_TRADE_7D_TOP3,
            UserTag.SPOT_MARKETS_TRADE_30D_TOP3,
            UserTag.SPOT_MARKETS_TRADE_90D_TOP3,
            UserTag.EXCHANGE_MARKETS_TRADE_7D_TOP3,
            UserTag.EXCHANGE_MARKETS_TRADE_30D_TOP3,
            UserTag.EXCHANGE_MARKETS_TRADE_90D_TOP3,
            UserTag.PERPETUAL_MARKETS_TRADE_7D_TOP3,
            UserTag.PERPETUAL_MARKETS_TRADE_30D_TOP3,
            UserTag.PERPETUAL_MARKETS_TRADE_90D_TOP3,

            UserTag.RECENT_7D_MARGIN_TRADE_MARKETS,
            UserTag.RECENT_30D_MARGIN_TRADE_MARKETS,
            UserTag.RECENT_90D_MARGIN_TRADE_MARKETS,
            UserTag.MARGIN_MARKETS_TRADE_7D_TOP3,
            UserTag.MARGIN_MARKETS_TRADE_30D_TOP3,
            UserTag.MARGIN_MARKETS_TRADE_90D_TOP3,
            UserTag.ONLY_SPOT_MARKETS_TRADE_7D_TOP3,
            UserTag.ONLY_SPOT_MARKETS_TRADE_30D_TOP3,
            UserTag.ONLY_SPOT_MARKETS_TRADE_90D_TOP3,
            UserTag.RECENT_7D_ONLY_SPOT_MARKETS,
            UserTag.RECENT_30D_ONLY_SPOT_MARKETS,
            UserTag.RECENT_90D_ONLY_SPOT_MARKETS,
        )
    ],
    *[
        build_basic_tag_property(tag, TagRawValueType.STRING, get_asset_options)
        for tag in (
            UserTag.FAVORITE_ASSETS,
            UserTag.FIRST_TRADE_ASSETS,
            UserTag.LATEST_UNFLAT_ASSETS,
            UserTag.LATEST_LOAN_ASSETS,
            UserTag.LATEST_TRADE_ASSETS,
            UserTag.RECENT_7D_INVEST_ASSETS,
            UserTag.RECENT_30D_INVEST_ASSETS,
            UserTag.RECENT_90D_INVEST_ASSETS,
            UserTag.LATEST_POSITION_ASSETS,
            UserTag.FIRST_PLEDGE_ASSETS,
            UserTag.LATEST_PLEDGE_ASSETS,
        )
    ],
    *[
        build_basic_tag_property(tag, TagRawValueType.STRING, get_asset_and_fiat_options)
        for tag in (
            UserTag.RECENT_30D_P2P_DEAL_ASSETS,
        )
    ],
    *[
        build_basic_tag_property(tag, TagRawValueType.INT)
        for tag in (
            UserTag.SPOT_TRADE_7D_ACTIVE_DAYS,
            UserTag.SPOT_TRADE_30D_ACTIVE_DAYS,
            UserTag.SPOT_TRADE_90D_ACTIVE_DAYS,
            UserTag.EXCHANGE_TRADE_7D_ACTIVE_DAYS,
            UserTag.EXCHANGE_TRADE_30D_ACTIVE_DAYS,
            UserTag.EXCHANGE_TRADE_90D_ACTIVE_DAYS,
            UserTag.PERPETUAL_TRADE_7D_ACTIVE_DAYS,
            UserTag.PERPETUAL_TRADE_30D_ACTIVE_DAYS,
            UserTag.PERPETUAL_TRADE_90D_ACTIVE_DAYS,
            UserTag.INVEST_7D_ACTIVE_DAYS,
            UserTag.INVEST_30D_ACTIVE_DAYS,
            UserTag.INVEST_90D_ACTIVE_DAYS,
            UserTag.AMM_7D_ACTIVE_DAYS,
            UserTag.AMM_30D_ACTIVE_DAYS,
            UserTag.AMM_90D_ACTIVE_DAYS,
            UserTag.REFER_USER_7D_COUNT,
            UserTag.REFER_USER_30D_COUNT,
            UserTag.REFER_USER_90D_COUNT,
            UserTag.REFER_TRADE_USER_7D_COUNT,
            UserTag.REFER_TRADE_USER_30D_COUNT,
            UserTag.REFER_TRADE_USER_90D_COUNT,
            UserTag.TOTAL_REFER_USER_COUNT,
            UserTag.TOTAL_REFER_TRADE_USER_COUNT,
            UserTag.PERPETUAL_7D_LIQ_COUNT,
            UserTag.PERPETUAL_30D_LIQ_COUNT,
            UserTag.PERPETUAL_90D_LIQ_COUNT,
            UserTag.MARGIN_7D_LIQ_COUNT,
            UserTag.MARGIN_30D_LIQ_COUNT,
            UserTag.MARGIN_90D_LIQ_COUNT,
            UserTag.INACTIVE_DAYS,
            UserTag.INACTIVE_TRADE_DAYS,
            UserTag.USER_30D_ACTIVE_DAYS,
            UserTag.MARGIN_TRADE_7D_ACTIVE_DAYS,
            UserTag.MARGIN_TRADE_30D_ACTIVE_DAYS,
            UserTag.MARGIN_TRADE_90D_ACTIVE_DAYS,
            UserTag.PERPETUAL_30D_FOLLOW_DAYS,
            UserTag.PERPETUAL_90D_FOLLOW_DAYS,
            UserTag.TOTAL_PERPETUAL_FOLLOW_DAYS,

            UserTag.RECENT_30D_SPOT_DEAL_CNT,
            UserTag.RECENT_90D_SPOT_DEAL_CNT,
            UserTag.RECENT_30D_PERPETUAL_DEAL_CNT,
            UserTag.RECENT_90D_PERPETUAL_DEAL_CNT,

            UserTag.RECENT_7D_PDT_DEAL_CNT,
            UserTag.RECENT_30D_PDT_DEAL_CNT,
            UserTag.RECENT_90D_PDT_DEAL_CNT,

            UserTag.TOTAL_PLEDGE_HOURS,
            UserTag.ONLY_SPOT_TRADE_7D_ACTIVE_DAYS,
            UserTag.ONLY_SPOT_TRADE_30D_ACTIVE_DAYS,
            UserTag.ONLY_SPOT_TRADE_90D_ACTIVE_DAYS,
            UserTag.RECENT_30D_P2P_DEAL_COUNT,
            UserTag.RECENT_90D_P2P_DEAL_COUNT,

        )
    ],
    UserTagProperty(
        tag=UserTag.LATEST_PERPETUAL_TRADE_MARKETS,
        raw_type=TagRawValueType.STRING,
        options=get_perpetual_market_options
    ),
    UserTagProperty(
        tag=UserTag.LATEST_PERPETUAL_POSITION_MARKETS,
        raw_type=TagRawValueType.STRING,
        options=get_perpetual_market_options
    ),
    UserTagProperty(
        tag=UserTag.OWN_COUPON_TYPE,
        raw_type=TagRawValueType.STRING,
        options=lambda: [dict(label=v.value, value=v.name) for v in Coupon.CouponType],
    ),
    UserTagProperty(
        tag=UserTag.TO_BE_COLLECTED_COUPON_TYPE,
        raw_type=TagRawValueType.STRING,
        options=lambda: [dict(label=v.value, value=v.name) for v in Coupon.CouponType],
    ),
    UserTagProperty(
        tag=UserTag.USER_EMAIL,
        raw_type=TagRawValueType.STRING,
    ),
    UserTagProperty(
        tag=UserTag.PUBLICITY_CHANNEL,
        raw_type=TagRawValueType.STRING,
        options=get_publicity_channel_options
    ),
    UserTagProperty(
        tag=UserTag.SHARE_POP_WIN,
        raw_type=TagRawValueType.STRING,
        options=lambda: [dict(label=f'[{v.value.value}, {v.value.desc}]', value=v.value.value) for v in ShareUserTag]
    ),
]


def check_missing_tag_properties():
    _tags = set(get_supported_tags())
    _property_tags = {v.tag for v in TAG_PROPERTIES}
    if _tags - _property_tags:
        current_app.logger.warning(f"{_tags - _property_tags} not in properties")


check_missing_tag_properties()


class TagReader:

    @classmethod
    def check_rule_type(cls, raw_rule: Dict):
        try:
            rule_type = RuleType(raw_rule["type"])
            if rule_type == RuleType.MER:
                raise InvalidArgument(message="群组首个规则不能为互斥规则")
        except ValueError:
            raise InvalidArgument(message="规则校验失败")

    @classmethod
    def check_group_type(cls, raw_rule: Dict):
        try:
            rule_type = RuleType(raw_rule["type"])
            if rule_type == RuleType.MER:
                raise InvalidArgument(message="群组首个规则不能为互斥规则")
        except ValueError:
            raise InvalidArgument(message="规则校验失败")

    @classmethod
    def _convert_rule_from_raw(cls, raw_rule: Dict) -> Optional[Dict]:
        fields = ("id", "op", "value", "type")
        if not all([field in raw_rule for field in fields]):
            return
        try:
            tag = UserTag[raw_rule["id"]]
        except ValueError:
            return
        try:
            op = OperatorType(raw_rule["op"])
        except ValueError:
            return
        try:
            rule_type = RuleType(raw_rule["type"])
        except ValueError:
            return
        tag_property = cls.get_tag_property(tag)
        if not tag_property:
            return
        value = raw_rule["value"]
        try:
            loads_value = [data_loads(_v, tag_property.raw_type) for _v in value]
        except Exception:
            return
        if len(loads_value) == 0:
            return
        if op == OperatorType.BETWEEN:
            if len(loads_value) != 2:
                return
        return dict(
            tag=tag,
            op=op,
            value=loads_value,
            type=rule_type
        )

    @classmethod
    def validate_group_rules(cls, component_rules: List):
        if len(component_rules) > 0:
            group = component_rules[0]
            if "type" in group and group["type"] == GroupRuleType.MER.name:
                raise InvalidArgument(message='首个群组不能为互斥群组')
        for group in component_rules:
            if "type" in group and group["type"] not in (GroupRuleType.MER.name,
                                                         GroupRuleType.NORMAL.name):
                raise InvalidArgument(message='规则校验失败')
            rules = group["filters"]
            if len(rules) > 0:
                cls.check_rule_type(rules[0])
            cls.validate_rules(rules)

    @classmethod
    def get_db_save_rules(cls, component_rules: List) -> List:
        return [[group['filters'], group.get('type', GroupRuleType.NORMAL.name)]
                for group in component_rules]

    @classmethod
    def build_component_rules(cls, rules: List):
        result = [dict(filters=[], tags=[]) for _ in rules]
        for index, group_config in enumerate(rules):
            group_rules, group_type = group_config
            result[index]["type"] = GroupRuleType[group_type]
            for rule in group_rules:
                try:
                    tag = UserTag[rule["id"]]
                except ValueError:
                    continue
                tag_property = cls.get_tag_property(tag)
                if not tag_property:
                    continue
                result[index]["filters"].append(rule)
                result[index]["tags"].append(
                    dict(
                        id=rule["id"],
                        label=rule["label"],
                        ops=[v.value for v in tag_property.get_supported_operations()],
                        options=tag_property.get_options(),
                        value_type=tag_property.raw_type.name
                    )
                )
        return result

    @classmethod
    def get_tag_property(cls, tag: UserTag) -> Optional[UserTagProperty]:
        for _property in TAG_PROPERTIES:
            if _property.tag == tag:
                return _property

    @classmethod
    def validate_rules(cls, group_rules: List):
        for rule in group_rules:
            if not cls._convert_rule_from_raw(rule):
                raise InvalidArgument(message='规则校验失败')

    @classmethod
    def get_rules_from_component(cls, component_rules: List):
        parsed_rules = []
        for group_data in component_rules:
            rules = group_data["filters"]
            _type = group_data.get("type", GroupRuleType.NORMAL.name)
            parse_group_data = [rules, _type]
            parsed_rules.append(parse_group_data)
        return parsed_rules

    @classmethod
    def parse_rules(cls, rules: List):
        parsed_rules = []
        for group_data in rules:
            if len(group_data) == 2 and isinstance(group_data[-1], str):
                _parsed_rules = [cls._convert_rule_from_raw(_rule) for _rule in group_data[0]]
                _type = GroupRuleType[group_data[-1]]
            else:
                _parsed_rules = [cls._convert_rule_from_raw(_rule) for _rule in group_data]
                _type = GroupRuleType.NORMAL
            parse_group_data = dict(rules=_parsed_rules,
                                    type=_type)
            parsed_rules.append(parse_group_data)
        return parsed_rules

    @classmethod
    def get_tag_property_mapping(cls):
        return {p.tag.name: dict(
            supported_operations={op.name: op.value for op in p.get_supported_operations()},
            options=p.get_options()
        ) for p in TAG_PROPERTIES}

    @classmethod
    def get_user_tag_data_by(cls, tag_names: List[str], user_ids: List[int]):
        tags = [UserTag[tag_name] for tag_name in tag_names]
        table_tags_mapping = defaultdict(list)
        for tag in tags:
            table_tags_mapping[get_tag_read_table(tag)].append(tag)
        user_data = defaultdict(list)
        tag_property_map = {p.tag: p for p in TAG_PROPERTIES}
        for model, _model_tags in table_tags_mapping.items():
            for chunk_user_ids in batch_iter(user_ids, 10000):
                rows = model.query.filter(
                    model.tag.in_([_tag.name for _tag in _model_tags]),
                    model.user_id.in_(chunk_user_ids)
                ).all()
                for row in rows:
                    if is_filter_user(row.user_id):
                        continue
                    tag = UserTag[row.tag]
                    tag_property = tag_property_map.get(tag)
                    value = data_loads(row.value, tag_property.raw_type)
                    user_data[row.user_id].append(dict(tag=tag, value=value))
        return user_data

    @classmethod
    def get_all_tag_data(cls, tags: Iterable[UserTag]):
        """read from database"""
        table_tags_mapping = defaultdict(list)
        for tag in tags:
            table_tags_mapping[get_tag_read_table(tag)].append(tag)
        result = defaultdict(dict)
        for model, _model_tags in table_tags_mapping.items():
            db_records = model.query.filter(
                model.tag.in_([
                    _tag.name
                    for _tag in _model_tags
                ])
            ).all()
            tag_property_map = {
                _tag: cls.get_tag_property(_tag)
                for _tag in _model_tags
            }
            for _record in db_records:
                if is_filter_user(_record.user_id):
                    continue
                _tag = UserTag[_record.tag]
                _tag_property = tag_property_map.get(_tag, None)
                _raw_type = _tag_property.raw_type
                result[_tag][_record.user_id] = data_loads(_record.value, _raw_type)
        return result

    @classmethod
    def get_single_tag_data(cls, tag: UserTag):
        """read from database"""
        read_table = get_tag_read_table(tag)
        db_records = read_table.query.filter(read_table.tag == tag.name).all()
        result = dict()
        _tag_property = cls.get_tag_property(tag)
        if not _tag_property:
            return dict()
        for _record in db_records:
            if is_filter_user(_record.user_id):
                continue
            _raw_type = _tag_property.raw_type
            result[_record.user_id] = data_loads(_record.value, _raw_type)
        return result

    @classmethod
    def check_value_with_operator(cls, value: Any, op: OperatorType, rule_value: List) -> bool:
        method_mapping = {
            OperatorType.LE: "__le__",
            OperatorType.LT: "__lt__",
            OperatorType.GE: "__ge__",
            OperatorType.GT: "__gt__",
            OperatorType.EQ: "__eq__",
            OperatorType.NE: "__ne__",
        }
        if isinstance(value, list):
            if op == OperatorType.IN:
                if set(value) & set(rule_value):
                    return True
            if op == OperatorType.NOT_IN:
                if not (set(value) & set(rule_value)):
                    return True
            if op == OperatorType.EQ:
                if set(value) == set(rule_value):
                    return True
            if op == OperatorType.NE:
                if set(value) != set(rule_value):
                    return True

        else:
            if op in method_mapping:
                _r = getattr(value, method_mapping[op])(rule_value[0])
                if _r is NotImplemented:
                    return False
                return _r
            if op == OperatorType.IN:
                return value in rule_value
            if op == OperatorType.NOT_IN:
                return value not in rule_value
            if op == OperatorType.BETWEEN and len(rule_value) == 2:
                return rule_value[0] <= value <= rule_value[1]
        return False

    @classmethod
    def aggregate_tag_rule(cls, tag_data: Dict[int, Any], rule: Dict[str, Any]) -> Set[int]:
        user_ids = set()
        op = rule["op"]
        rule_value = rule["value"]
        for user_id, _value in tag_data.items():
            if is_filter_user(user_id):
                continue
            if cls.check_value_with_operator(_value, op, rule_value):
                user_ids.add(user_id)
        return user_ids

    @classmethod
    def yield_aggregate_tag_rule(cls, tag: UserTag, rule: Dict[str, Any]) -> Iterable[int]:
        op = rule["op"]
        rule_value = rule["value"]
        _tag_property = cls.get_tag_property(tag)
        if not _tag_property:
            return
        user_ids = set()
        read_model = get_tag_read_table(tag)
        _query = read_model.query.filter(
            read_model.tag == tag.name
        ).with_entities(read_model.value, read_model.user_id).all()
        for _record in _query:
            _raw_type = _tag_property.raw_type
            _value = data_loads(_record.value, _raw_type)
            if cls.check_value_with_operator(_value, op, rule_value):
                if is_filter_user(_record.user_id):
                    continue
                user_ids.add(_record.user_id)
        yield from user_ids

    @classmethod
    def aggregate_single_group(cls, group_rules: Dict) -> Set[int]:
        if len(group_rules) == 0:
            return set()
        user_ids = set(cls.yield_aggregate_tag_rule(group_rules[0]["tag"], group_rules[0]))
        if not user_ids:
            return set()
        for rule in group_rules[1:]:
            tag = rule["tag"]
            _rule_type = rule["type"]
            rule_user_ids = set(cls.yield_aggregate_tag_rule(tag, rule))
            if not rule_user_ids:
                return set()
            if _rule_type == RuleType.MER:
                user_ids -= rule_user_ids
            else:
                user_ids &= rule_user_ids
        return user_ids

    @classmethod
    def aggregate(cls, groups: List) -> Set[int]:
        if len(groups) == 0:
            return set()
        user_ids = cls.aggregate_single_group(groups[0]["rules"])
        for group in groups[1:]:
            group_rule_type = group["type"]
            group_rules = group["rules"]
            if group_rule_type == GroupRuleType.MER:
                user_ids -= cls.aggregate_single_group(group_rules)
            else:
                user_ids |= cls.aggregate_single_group(group_rules)
        return user_ids - get_disabled_user_ids()
