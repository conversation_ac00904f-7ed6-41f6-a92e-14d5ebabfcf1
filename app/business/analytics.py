#!/usr/bin/env python3
import time
from enum import Enum
from typing import Dict, List, Optional, Union

import requests
from flask import current_app
from sqlalchemy import func

from .. import config
from ..common import CeleryQueues
from ..models import PageVisitor, db
from ..models.mongo.app import AppleADSAttributionMySQL as AppleADSAttribution
from ..utils import celery_task


class Scope(Enum):
    PERPETUAL_ACTIVITY = 'perpetual_activity'
    KUN_AIRDROP = 'kun_airdrop'
    WOO_AIRDROP = 'woo_airdrop'
    FNX_AIRDROP = 'fnx_airdrop'
    ANNIVERSARY = 'anniversary'
    SPRING_PERPETUAL = 'spring_perpetual'
    RUSSIAN_ACTIVITY = 'russian_activity'


class Page(Enum):
    HOME = '/'
    PERPETUAL_ACTIVITY = '/activity/perpetual-contest'
    KUN_AIRDROP_ACTIVITY = '/activity/kun'
    WOO_AIRDROP_ACTIVITY = '/activity/woo'
    FNX_AIRDROP_ACTIVITY = '/activity/fnx'
    ANNIVERSARY_ACTIVITY = '/activity/anniversary'
    ANNIVERSARY_BILL = '/mobile/bill'
    SPRING_PERPETUAL_ACTIVITY = '/activity/spring-perpetual'
    RUSSIAN_ACTIVITY_MART = '/activity/mart'


class Channel(Enum):
    COINEX = 'coinex'
    EMAIL = 'email'
    MEDIA = 'media'
    AMBASSADOR = 'ambassador'
    DOMESTIC_COMMUNITY = 'domestic_community'
    FOREIGN_COMMUNITY = 'foreign_community'


def normalise_channel(channel: Optional[str]) -> str:
    if not channel:
        return Channel.COINEX.value
    try:
        return Channel(channel.lower()).value
    except ValueError:
        return Channel.COINEX.value


def _get_value(x):
    return x.value if isinstance(x, Enum) else x


def add_page_visitor(scope: Union[str, Scope],
                     page:  Union[str, Page],
                     user_id: int,
                     channel: Union[str, Channel] = None):
    scope, page, channel = _get_value(scope), _get_value(page), _get_value(channel)
    if not channel:
        channel = Channel.COINEX.value
    row = PageVisitor.query.filter(
            PageVisitor.scope == scope,
            PageVisitor.page == page,
            PageVisitor.user_id == user_id
        ).first()
    if row:
        return
    row = PageVisitor(
        scope=scope,
        page=page,
        user_id=user_id,
        channel=channel
    )
    db.session_add_and_commit(row)


def group_page_visitors(scope: Union[str, Scope],
                        page: Union[str, Page] = None,
                        channel: Union[str, Channel] = None) -> List[Dict[str, any]]:
    scope, page, channel = _get_value(scope), _get_value(page), _get_value(channel)
    columns = ('scope', 'page', 'channel', 'count')
    query = PageVisitor.query.filter(PageVisitor.scope == scope)
    if page:
        query = query.filter(PageVisitor.page == page)
    if channel:
        query = query.filter(PageVisitor.channel == channel)
    rows = query.group_by(
        PageVisitor.page,
        PageVisitor.channel
    ).with_entities(
        PageVisitor.scope,
        PageVisitor.page,
        PageVisitor.channel,
        func.count('*')
    ).all()
    return [dict(zip(columns, row)) for row in rows]


def get_page_visitors(scope: Union[str, Scope],
                      page: Union[str, Page] = None,
                      channel: Union[str, Channel] = None) -> List[int]:
    scope, page, channel = _get_value(scope), _get_value(page), _get_value(channel)
    query = PageVisitor.query.filter(PageVisitor.scope == scope)
    if page:
        query = query.filter(PageVisitor.page == page)
    if channel:
        query = query.filter(PageVisitor.channel == channel)
    rows = query.with_entities(PageVisitor.user_id).all()
    return [row.user_id for row in rows]


@celery_task(queue=CeleryQueues.REAL_TIME)
def save_apple_ads_info(device_id: str, token: str):

    url = config["APPLE_ADS_URL"]
    headers = {
        'Content-Type': 'text/plain'
    }
    resp = requests.post(url, headers=headers, data=token, timeout=10)
    code = resp.status_code
    base_info = f"device_id: {device_id} token: {token} get apple ads"

    if code == 400:
        current_app.logger.error(f"{base_info} token invalid, first retry")
        # 如果 token 拿到后马上使用，有可能会报400，官方建议间隔 5s 重试一次
        time.sleep(5)
        resp = requests.post(url, headers=headers, data=token, timeout=10)
        code = resp.status_code

    if code == 200:
        data = resp.json()
        if not data:
            current_app.logger.error(f"{base_info} not data")
            return
        data["device_id"] = device_id
        # 字段名转换映射
        field_mapping = {
            'orgId': 'org_id',
            'campaignId': 'campaign_id',
            'conversionType': 'conversion_type',
            'clickDate': 'click_date',
            'adGroupId': 'ad_group_id',
            'countryOrRegion': 'country_or_region',
            'keywordId': 'keyword_id',
            'adId': 'ad_id'
        }
        
        # 转换字段名
        mysql_data = {}
        for mongo_field, mysql_field in field_mapping.items():
            if mongo_field in data:
                mysql_data[mysql_field] = data[mongo_field]
        mysql_data['device_id'] = device_id
        
        # 查询或创建记录
        ads = AppleADSAttribution.query.filter_by(device_id=device_id).first()
        if not ads:
            ads = AppleADSAttribution(**mysql_data)
        else:
            # 更新现有记录
            for key, value in mysql_data.items():
                setattr(ads, key, value)
        
        try:
            db.session.add(ads)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to save Apple Ads info: {str(e)}")
            raise
    elif code == 400:
        current_app.logger.error(f"{base_info} token invalid")
    elif code >= 500:
        current_app.logger.error(f"{base_info} server error")
    else:
        current_app.logger.error(f"{base_info} request error code: {code}")
