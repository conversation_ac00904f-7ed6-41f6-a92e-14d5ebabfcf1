# -*- coding: utf-8 -*-
import json
from decimal import Decimal
from typing import Optional

from sqlalchemy import or_, and_

from flask import current_app

from app.config import config
from app.models import db, Message
from app.models.wallet import (
    Deposit, AbnormalDepositApplication, AbnormalDepositApplicationTransferHistory, AbnormalDepositApplicationChangelog
)
from app.common import MessageContent, MessageTitle, MessageWebLink, PrecisionEnum
from app.business import BalanceBusiness, ServerClient, SPOT_ACCOUNT_ID, UserPreferences, WalletClient, CacheLock, LockKeys, PriceManager
from app.business.email import send_abnormal_deposit_application_finished_email
from app.utils import now, amount_to_str, datetime_to_str, quantize_amount


class AbnormalDepositBusiness:
    # admin筛选、展示的状态
    ADMIN_STATUS_DICT = {
        "CREATED": "初始状态-待定",
        "ADDITIONAL_INFO_REQUIRED": "待补充资料",
        "ADDITIONAL_INFO_REQUIRE_MORE": "待额外补充资料",
        "ADDITIONAL_INFO_AUDIT_REQUIRED": "补充资料审核中",
        "AUDIT_REQUIRED": "初审中",
        "CHECK_REQUIRED": "复审中",
        "DOUBLE_CHECK_REQUIRED": "三审中",
        "PROCESSING": "资金处理中",
        "ASSET_CHANGE_FAILED": "资产变更失败",
        "REJECTED": "审核不通过",
        "CANCELLED": "已取消",
        "FINISHED": "找回成功",
    }

    NEED_AUDIT_STATUSES = {
        "AUDIT_REQUIRED",
        "ADDITIONAL_INFO_AUDIT_REQUIRED",
        "CHECK_REQUIRED",
        "DOUBLE_CHECK_REQUIRED",
        "CREATED",
    }

    NEED_DOUBLE_CHECK_TYPES = [
        AbnormalDepositApplication.Type.WRONG_MEMO,
        AbnormalDepositApplication.Type.RECIPIENT_IS_HOT_WALLET
    ]
    NEED_DOUBLE_CHECK_MIN_TOTAL_USD = Decimal("1000")

    # 如果 钱包异常充值 匹配 下面类型的找回记录 ，在Web侧生成的充值记录 是财务用户的
    DEPOSIT_TO_FINANCE_TYPES = [
        AbnormalDepositApplication.Type.RECIPIENT_IS_HOT_WALLET,
        AbnormalDepositApplication.Type.WRONG_CHAIN_OF_ADDRESS,
    ]
    APPLY_TYPE_TRANSFER_TYPE_MAP = {
        # { 找回类型: 资产变更划转类型 }
        AbnormalDepositApplication.Type.WRONG_CHAIN_WITH_RECORDABLE:
            AbnormalDepositApplicationTransferHistory.Type.WRONG_CHAIN_WITH_RECORDABLE_CHANGE,
        AbnormalDepositApplication.Type.RECIPIENT_IS_HOT_WALLET:
            AbnormalDepositApplicationTransferHistory.Type.RECIPIENT_IS_HOT_WALLET_CHANGE,
        AbnormalDepositApplication.Type.WRONG_CHAIN_OF_ADDRESS:
            AbnormalDepositApplicationTransferHistory.Type.WRONG_CHAIN_OF_ADDRESS_CHANGE,
    }

    @classmethod
    def get_finance_user_id(cls) -> int:
        return config['FINANCE_USER_ID']

    @classmethod
    def is_need_double_check(cls, row: AbnormalDepositApplication):
        return row.type in cls.NEED_DOUBLE_CHECK_TYPES and row.total_usd >= cls.NEED_DOUBLE_CHECK_MIN_TOTAL_USD

    @classmethod
    def get_admin_status(cls, row: AbnormalDepositApplication) -> str:
        status_class = AbnormalDepositApplication.Status
        if row.status in [
            status_class.REJECTED,
            status_class.CANCELLED,
            status_class.FINISHED,
        ]:
            return row.status.name
        if row.status == status_class.CHECK_REQUIRED or \
                (not cls.is_need_double_check(row) and row.status == status_class.FEE_ASSET_CHANGE_DEDUCTED):
            return status_class.CHECK_REQUIRED.name
        elif row.status == status_class.DOUBLE_CHECK_REQUIRED or \
                (cls.is_need_double_check(row) and row.status == status_class.FEE_ASSET_CHANGE_DEDUCTED):
            return status_class.DOUBLE_CHECK_REQUIRED.name
        elif row.status in [status_class.CHECKED, status_class.PROCESSING, status_class.FAILED]:
            return "PROCESSING"
        elif row.status in [
            status_class.ASSET_CHANGE_PROCESSING,
            status_class.ASSET_CHANGE_DEDUCTED,
        ]:
            return "ASSET_CHANGE_FAILED"
        elif row.status == status_class.AUDIT_REQUIRED:
            aq_status = [
                AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED,
                AbnormalDepositApplication.AdditionalInfoStatus.PROVIDED_CR,
            ]
            if row.additional_info_status in aq_status:
                # 补充资料审核中
                return "ADDITIONAL_INFO_AUDIT_REQUIRED"
        elif row.status == status_class.CREATED:
            if row.additional_info_status == AbnormalDepositApplication.AdditionalInfoStatus.REQUIRED:
                # 待补充资料
                return "ADDITIONAL_INFO_REQUIRED"
            rm_status = [
                AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE,
                AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE_CR,
            ]
            if row.additional_info_status in rm_status:
                # 待额外补充资料
                return "ADDITIONAL_INFO_REQUIRE_MORE"
            if row.additional_info_status == AbnormalDepositApplication.AdditionalInfoStatus.UNKNOWN:
                return "CREATED"

        # 其他情况都当成 审核中
        return "AUDIT_REQUIRED"

    @classmethod
    def admin_status_to_filter(cls, admin_status: str):
        model = AbnormalDepositApplication
        if admin_status in ["REJECTED", "CANCELLED", "FINISHED"]:
            _status_enum = model.Status[admin_status]
            return model.status == _status_enum
        if admin_status == "DOUBLE_CHECK_REQUIRED":
            return or_(
                model.status == model.Status.DOUBLE_CHECK_REQUIRED,
                and_(
                    model.status == model.Status.FEE_ASSET_CHANGE_DEDUCTED,
                    model.type.in_(cls.NEED_DOUBLE_CHECK_TYPES),
                    model.total_usd >= cls.NEED_DOUBLE_CHECK_MIN_TOTAL_USD
                )
            )
        elif admin_status == "CHECK_REQUIRED":
            return or_(
                model.status == model.Status.CHECK_REQUIRED,
                and_(
                    model.status == model.Status.FEE_ASSET_CHANGE_DEDUCTED,
                    or_(
                        model.type.not_in(cls.NEED_DOUBLE_CHECK_TYPES),
                        model.total_usd < cls.NEED_DOUBLE_CHECK_MIN_TOTAL_USD
                    )
                )
            )
        elif admin_status == "PROCESSING":
            _status_enums = [
                model.Status.CHECKED,
                model.Status.PROCESSING,
                model.Status.FAILED,
            ]
            return model.status.in_(_status_enums)
        elif admin_status == "ASSET_CHANGE_FAILED":
            _status_enums = [
                model.Status.ASSET_CHANGE_PROCESSING,
                model.Status.ASSET_CHANGE_DEDUCTED,
            ]
            return model.status.in_(_status_enums)
        elif admin_status == "ADDITIONAL_INFO_AUDIT_REQUIRED":
            return and_(
                model.status == model.Status.AUDIT_REQUIRED,
                or_(
                    model.additional_info_status == model.AdditionalInfoStatus.PROVIDED,
                    model.additional_info_status == model.AdditionalInfoStatus.PROVIDED_CR,
                ),
            )
        elif admin_status == "ADDITIONAL_INFO_REQUIRED":
            return and_(
                model.status == model.Status.CREATED,
                model.additional_info_status == model.AdditionalInfoStatus.REQUIRED,
            )
        elif admin_status == 'ADDITIONAL_INFO_REQUIRE_MORE':
            return and_(
                model.status == model.Status.CREATED,
                or_(
                    model.additional_info_status == model.AdditionalInfoStatus.REQUIRE_MORE,
                    model.additional_info_status == model.AdditionalInfoStatus.REQUIRE_MORE_CR,
                ),
            )
        elif admin_status == "CREATED":
            return and_(
                model.status == model.Status.CREATED,
                model.additional_info_status == model.AdditionalInfoStatus.UNKNOWN,
            )

        # AUDIT_REQUIRED
        return and_(
            model.status == model.Status.AUDIT_REQUIRED,
            model.additional_info_status.notin_(
                [
                    model.AdditionalInfoStatus.PROVIDED,
                    model.AdditionalInfoStatus.PROVIDED_CR,
                ]
            )
        )

    @classmethod
    def batch_get_is_take_fee(cls, rows: list[AbnormalDepositApplication]) -> dict[int, bool]:
        """ 是否已收手续费 """
        is_take_fee_map = {}
        new_fee_rows = []
        for r in rows:
            if r.is_new:
                if r.is_need_fee:
                    new_fee_rows.append(r)
                else:
                    is_take_fee_map[r.id] = False
            else:
                is_take_fee_map[r.id] = bool(r.is_need_fee and r.fee_amount and r.fee_asset and r.fee_amount > 0)
        if new_fee_rows:
            apl_ids = [i.id for i in new_fee_rows]
            trans_rows = AbnormalDepositApplicationTransferHistory.query.filter(
                AbnormalDepositApplicationTransferHistory.application_id.in_(apl_ids),
                AbnormalDepositApplicationTransferHistory.status == AbnormalDepositApplicationTransferHistory.Status.FINISHED,
            ).with_entities(
                AbnormalDepositApplicationTransferHistory.application_id,
            ).all()
            take_fee_apl_ids = {i.application_id for i in trans_rows}  # 有划转就是收了手续费
            for apl_id in apl_ids:
                is_take_fee_map[apl_id] = apl_id in take_fee_apl_ids
        return is_take_fee_map

    @classmethod
    def get_asset_rate(cls, asset: str) -> Decimal:
        from app.caches.prices import CMCAssetRateInfoCache

        asset_rate = PriceManager.asset_to_usd(asset)
        if not asset_rate:
            asset_rate = CMCAssetRateInfoCache().get_single_asset_price(asset) or Decimal()
        return asset_rate

    @classmethod
    def calc_expect_fee_amount(cls, asset_price: Decimal, amount: Decimal) -> Decimal:
        """ 计算 预计手续费数目
        资产市值≤5万USD：收取10%；如果10%的币种市值＜50USD，则按50USD对应的币种数量收取；
        5万USD＜资产市值≤20万U的部分：收取5%；
        资产市值＞20万USD以上部分：收取2.5%；
        """
        assert asset_price > Decimal(0)

        part1_threshold = Decimal(50000)
        part2_threshold = Decimal(200000)

        usd = asset_price * amount
        if usd <= part1_threshold:
            fee_usd = usd * Decimal('0.1')
            fee_usd = max(fee_usd, Decimal(50))
        elif usd <= part2_threshold:
            part1_fee_usd = part1_threshold * Decimal('0.1')
            part2_fee_usd = (usd - part1_threshold) * Decimal('0.05')
            fee_usd = part1_fee_usd + part2_fee_usd
        else:
            part1_fee_usd = part1_threshold * Decimal('0.1')
            part2_fee_usd = (part2_threshold - part1_threshold) * Decimal('0.05')
            part3_fee_usd = (usd - part2_threshold) * Decimal('0.025')
            fee_usd = part1_fee_usd + part2_fee_usd + part3_fee_usd
        fee_amount = quantize_amount(fee_usd / asset_price, 8)
        fee_amount = min(fee_amount, amount)  # 找回的数目，还不够手续费。最多全收
        return fee_amount

    @classmethod
    def update_expect_fee_amount(cls, row: AbnormalDepositApplication, admin_user_id: int = None, ignore_is_manual: bool = False):
        if not (row.is_new and row.is_need_fee):
            return
        if row.expect_fee_is_manual and not ignore_is_manual:
            return

        if row.type in AbnormalDepositApplication.NEW_NEED_SERVICE_FEE_TYPES and row.asset_rate:
            expect_fee_amount = AbnormalDepositBusiness.calc_expect_fee_amount(row.asset_rate, row.tx_amount)
            expect_fee_asset = row.asset
            assert expect_fee_amount <= row.tx_amount
        else:
            expect_fee_amount = expect_fee_asset = None

        if row.expect_fee_amount != expect_fee_amount or row.expect_fee_asset != expect_fee_asset:
            old_expect_fee_amount = row.expect_fee_amount
            old_expect_fee_amount_str = amount_to_str(old_expect_fee_amount) if old_expect_fee_amount is not None else '-'
            expect_fee_amount_str = amount_to_str(expect_fee_amount) if expect_fee_amount is not None else '-'
            row.expect_fee_amount = expect_fee_amount
            row.expect_fee_asset = expect_fee_asset
            AbnormalDepositApplicationChangelog.add(
                application_id=row.id,
                user_id=row.user_id,
                admin_user_id=admin_user_id,
                change_type=AbnormalDepositApplicationChangelog.ChangeType.DETAIL.name,
                old_value=f"手续费{old_expect_fee_amount_str} {row.expect_fee_asset or '-'}",
                new_value=f"手续费{expect_fee_amount_str} {row.expect_fee_asset or '-'}",
            )

    @classmethod
    def find_new_apply_by_deposit_info(cls, user_id: int, tx_id: str, chain: str, asset: str, ) -> list[AbnormalDepositApplication]:
        """ 钱包ABNORMAL类型的充值记录，判断user_id、tx_id、chain、asset 是否匹配有 对应的找回订单 """
        apply_rows = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.type.in_(cls.DEPOSIT_TO_FINANCE_TYPES),
            AbnormalDepositApplication.is_new.is_(True),
            AbnormalDepositApplication.user_id == user_id,
            AbnormalDepositApplication.asset == asset,
            AbnormalDepositApplication.chain == chain,
            AbnormalDepositApplication.tx_id == tx_id,
            AbnormalDepositApplication.status.in_([
                AbnormalDepositApplication.Status.CHECKED,
                AbnormalDepositApplication.Status.PROCESSING,
                AbnormalDepositApplication.Status.ASSET_CHANGE_PROCESSING,
                AbnormalDepositApplication.Status.ASSET_CHANGE_DEDUCTED,
                AbnormalDepositApplication.Status.FINISHED,  # 已完成也包括
            ])
        ).with_entities(AbnormalDepositApplication.id).all()
        return apply_rows

    @classmethod
    def find_apply_by_deposit_id(cls, deposit_id: int) -> Optional[AbnormalDepositApplication]:
        """通过deposit_id查找申请记录"""
        if not deposit_id:
            return None
        apply = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.deposit_id == deposit_id,
        ).first()
        return apply

    @classmethod
    def finish_new_apply_by_finance_user_deposit(cls, apply_id: int):
        """
        【充到热钱包】和【充错地址】这两个类型的处理方案改为：
        1. 找回订单审核通过后，钱包还是正常给找回订单的用户生成充值记录
        2. web收到钱包同步过来的这种类型的充值记录后，给财务账号（收手续费的账号）生成充值记录入账。
        3. web这边通过资产变更，扣除财务账号的资产（扣除理应给用户入账的部分），给用户账号增加资产。
        """
        with CacheLock(LockKeys.abnormal_deposit_application(apply_id)):
            db.session.rollback()

            apply: AbnormalDepositApplication = AbnormalDepositApplication.query.get(apply_id)
            assert apply.is_new
            assert apply.type in cls.DEPOSIT_TO_FINANCE_TYPES
            assert apply.status == AbnormalDepositApplication.Status.PROCESSING

            finance_user_id = cls.get_finance_user_id(),
            finance_deps: list[Deposit] = Deposit.query.filter(
                Deposit.user_id == finance_user_id,
                Deposit.type == Deposit.Type.ON_CHAIN,
                Deposit.status == Deposit.Status.FINISHED,
                Deposit.asset == apply.asset,
                Deposit.chain == apply.chain,
                Deposit.tx_id == apply.tx_id,
            ).with_entities(
                Deposit.id,
                Deposit.asset,
                Deposit.amount,
            ).all()
            if not finance_deps:
                current_app.logger.warning(f"new_ab_apply: {apply_id} {apply.type.name} not_match finished_finance_dep")
                return
            if len(finance_deps) != 1:
                # 充值找回记录没有vout，出现匹配多笔的情况 手动处理
                raise ValueError(f"new_ab_apply: {apply_id} {apply.type.name} many finished_finance_dep {[i.id for i in finance_deps]}")

            finance_dep = finance_deps[0]
            assert finance_dep.amount == apply.tx_amount

            # 关联dep_id
            other_bind_apply = cls.find_apply_by_deposit_id(finance_dep.id)
            if other_bind_apply:
                assert other_bind_apply.id == apply_id
            if apply.deposit_id:
                assert apply.deposit_id == finance_dep.id
            else:
                apply.deposit_id = finance_dep.id
                apply.fee_asset = apply.expect_fee_asset
                apply.fee_amount = apply.expect_fee_amount
                apply.status = AbnormalDepositApplication.Status.ASSET_CHANGE_PROCESSING
                db.session.commit()

            # 处理资产
            fee_amount = apply.fee_amount
            assert fee_amount >= 0
            user_remain_amount = finance_dep.amount - fee_amount  # 用户要增加的资产｜财务要扣减的资产
            assert user_remain_amount >= 0
            history = cls.get_or_create_transfer_his(apply, asset=finance_dep.asset, amount=user_remain_amount)
        cls.do_asset_change_by_new_apply(apply, history)  # CacheLock

    @classmethod
    def do_asset_change_by_new_apply(cls, apply: AbnormalDepositApplication, history: AbnormalDepositApplicationTransferHistory = None):
        old_status = apply.status

        if not history:
            tran_model = AbnormalDepositApplicationTransferHistory
            tran_type = cls.APPLY_TYPE_TRANSFER_TYPE_MAP[apply.type]
            history = tran_model.query.filter(
                tran_model.application_id == apply.id,
                tran_model.type == tran_type,
            ).first()
            assert history

        with CacheLock(LockKeys.abnormal_deposit_application(apply.id)):
            db.session.rollback()
            cls.do_transfer_by_his(apply, history)

        if old_status != apply.status:
            AbnormalDepositApplicationChangelog.add(
                application_id=apply.id,
                user_id=apply.user_id,
                admin_user_id=None,
                change_type=AbnormalDepositApplicationChangelog.ChangeType.STATUS.name,
                old_value=old_status.name,
                new_value=apply.status.name,
            )
            db.session.commit()
        if apply.status == AbnormalDepositApplication.Status.FINISHED:
            notice_abnormal_deposit_application_finished(apply)

    @classmethod
    def get_or_create_transfer_his(
        cls, row: AbnormalDepositApplication,
        asset: str,
        amount: Decimal,
    ) -> AbnormalDepositApplicationTransferHistory:
        assert amount >= Decimal()

        user_id = row.user_id
        finance_user_id = cls.get_finance_user_id()
        tran_model = AbnormalDepositApplicationTransferHistory
        tran_type = cls.APPLY_TYPE_TRANSFER_TYPE_MAP[row.type]
        history = tran_model.query.filter(
            tran_model.application_id == row.id,
            tran_model.type == tran_type,
        ).first()
        if not history:
            history = AbnormalDepositApplicationTransferHistory(
                application_id=row.id,
                type=tran_type,
                from_user_id=finance_user_id,
                to_user_id=user_id,
                asset=asset,
                amount=amount,
            )
            db.session.add(history)
            db.session.commit()
        return history

    @classmethod
    def do_transfer_by_his(cls, row: AbnormalDepositApplication, history: AbnormalDepositApplicationTransferHistory):
        asset = history.asset
        amount = history.amount
        business_id = history.get_business_id()
        finance_user_id = config["FINANCE_USER_ID"]
        apply_id = row.id
        balance_remark = f"asset change for ab_dep_apply {apply_id} history_id:{history.id}"
        client = ServerClient()

        status_enum = AbnormalDepositApplication.Status

        def _update_to_deducted():
            row.status = status_enum.ASSET_CHANGE_DEDUCTED
            history.status = AbnormalDepositApplicationTransferHistory.Status.DEDUCTED
            history.deducted_at = now()

        def _update_to_finished():
            row.status = status_enum.FINISHED
            history.status = AbnormalDepositApplicationTransferHistory.Status.FINISHED
            history.finished_at = now()

        # 扣减财务资产
        if row.status == status_enum.ASSET_CHANGE_PROCESSING and amount == 0:
            _update_to_deducted()
            db.session.commit()
        elif row.status == status_enum.ASSET_CHANGE_PROCESSING and amount > 0:
            deduct_result = client.asset_query_business(
                user_id=finance_user_id,
                asset=asset,
                business=BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
                business_id=business_id,
            )
            if deduct_result:
                _update_to_deducted()
                db.session.commit()
            else:
                # 尝试扣款
                finance_balance_result = client.get_user_balances(finance_user_id, asset, account_id=SPOT_ACCOUNT_ID)
                available = Decimal(finance_balance_result.get(asset, {}).get("available", Decimal()))
                if available < amount:
                    raise ValueError(f"row:{apply_id} 财务账户资产不足")

                try:
                    client.add_user_balance(
                        user_id=finance_user_id,
                        asset=asset,
                        amount=amount_to_str(-amount, 8),
                        business=BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
                        business_id=business_id,
                        detail={"remark": balance_remark},
                        account_id=SPOT_ACCOUNT_ID,
                    )
                except client.BadResponse as _e:
                    raise Exception(f"row:{apply_id} 扣减财务账户资产失败：{_e}")
                else:
                    _update_to_deducted()
                    db.session.commit()

        if row.status == status_enum.ASSET_CHANGE_DEDUCTED and amount == 0:
            _update_to_finished()
            db.session.commit()
        elif row.status == status_enum.ASSET_CHANGE_DEDUCTED and amount > 0:
            add_result = client.asset_query_business(
                user_id=row.user_id,
                asset=asset,
                business=BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
                business_id=business_id,
            )
            if add_result:
                _update_to_finished()
                db.session.commit()
            else:
                try:
                    client.add_user_balance(
                        user_id=row.user_id,
                        asset=asset,
                        amount=amount_to_str(amount, 8),
                        business=BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
                        business_id=business_id,
                        detail={"remark": balance_remark},
                        account_id=SPOT_ACCOUNT_ID,
                    )
                except client.BadResponse as _e:
                    raise Exception(f"row:{apply_id} 扣减财务账户资产成功，增加用户资产失败：{_e}")
                else:
                    _update_to_finished()
                    db.session.commit()


def notice_abnormal_deposit_application_finished(row: AbnormalDepositApplication):
    """ 发送成功通知 """
    pref = UserPreferences(row.user_id)
    if row.type in AbnormalDepositApplication.REFUND_TYPES:
        extra_params = {
            "refund_tx_id": row.refund_tx_id,
            "refund_tx_url": WalletClient().get_explorer_tx_url(row.chain, row.refund_tx_id) if row.refund_tx_id else "",
        }
        if row.is_new and row.is_need_fee and row.fee_asset:
            content = MessageContent.ABNORMAL_DEP_APPLY_SUCCESS_BY_REFUND_WITH_FEE.name
            web_link = MessageWebLink.ABNORMAL_DEP_APPLY_FEE_RULE_PAGE.value
            extra_params.update(
                {
                    'fee_asset': row.fee_asset,
                    'fee_amount': amount_to_str(row.fee_amount, PrecisionEnum.COIN_PLACES),
                }
            )
        else:
            content = MessageContent.ABNORMAL_DEPOSIT_APPLICATION_SUCCESS_BY_REFUND.name
            web_link = MessageWebLink.DEPOSIT_RECOVERY_PAGE.value
    else:
        extra_params = {}
        if row.is_new and row.is_need_fee and row.fee_asset:
            content = MessageContent.ABNORMAL_DEP_APPLY_SUCCESS_BY_SPOT_WITH_FEE.name
            web_link = MessageWebLink.ABNORMAL_DEP_APPLY_FEE_RULE_PAGE.value
            extra_params.update(
                {
                    'fee_asset': row.fee_asset,
                    'fee_amount': amount_to_str(row.fee_amount, PrecisionEnum.COIN_PLACES),
                }
            )
        else:
            content = MessageContent.ABNORMAL_DEPOSIT_APPLICATION_SUCCESS_BY_SPOT.name
            web_link = MessageWebLink.DEPOSIT_RECORD_PAGE.value
    msg = Message(
        user_id=row.user_id,
        title=MessageTitle.ABNORMAL_DEPOSIT_APPLICATION_SUCCESS.name,
        content=content,
        params=json.dumps(
            dict(
                time=datetime_to_str(row.created_at, pref.timezone_offset),
                amount=amount_to_str(row.tx_amount, PrecisionEnum.COIN_PLACES),
                asset=row.asset,
                **extra_params,
            )
        ),
        extra_info=json.dumps(
            dict(
                web_link=web_link,
                android_link="",
                ios_link="",
            )
        ),
        display_type=Message.DisplayType.TEXT,
        channel=Message.Channel.DEPOSIT_WITHDRAWAL,
    )
    db.session_add_and_commit(msg)
    send_abnormal_deposit_application_finished_email.delay(row.id)
