# -*- coding: utf-8 -*-

from typing import <PERSON><PERSON>, Optional

from flask_babel import gettext
from sqlalchemy import func

from app.exceptions import InvalidArgument
from app.models import db, User, SubAccount
from app.models.strategy import StrategyRunUserStatus, UserStrategy
from app.utils import now


STRATEGY_RUN_USER_NUM_LIMIT = 20  # 单个主账号最多可创建的策略子账号数目


class RunUserManager:
    def __init__(self, main_user_id: int):
        self.main_user_id = main_user_id
        self.max_run_user_num = STRATEGY_RUN_USER_NUM_LIMIT

    def get_strategy_user_nums(self) -> int:
        """ 当前策略子账号的数目 """
        return (
            SubAccount.query.filter(
                SubAccount.main_user_id == self.main_user_id,
                SubAccount.type == SubAccount.Type.STRATEGY,
            )
            .with_entities(func.count(SubAccount.id))
            .scalar()
            or 0
        )

    def new_strategy_user(self, name: str) -> <PERSON><PERSON>[User, SubAccount, StrategyRunUserStatus]:
        """ 新增策略子账号 """
        user = User(
            name=f"{name}",
            user_type=User.UserType.SUB_ACCOUNT,
        )
        db.session.add(user)
        db.session.flush()
        sub_account = SubAccount(
            user_id=user.id,
            main_user_id=self.main_user_id,
            type=SubAccount.Type.STRATEGY,
            remark="strategy",
        )
        db.session.add(sub_account)
        db.session.flush()
        strategy_user = StrategyRunUserStatus(
            user_id=user.id,
            main_user_id=self.main_user_id,
            remark="strategy",
        )
        db.session.add(strategy_user)
        db.session.flush()
        return user, sub_account, strategy_user

    def get_usable_strategy_user(self) -> Optional[StrategyRunUserStatus]:
        """ 获取可用的策略子账号 """
        run_user: StrategyRunUserStatus = (
            StrategyRunUserStatus.query.filter(
                StrategyRunUserStatus.main_user_id == self.main_user_id,
                StrategyRunUserStatus.status == StrategyRunUserStatus.Status.USABLE,
            )
            .order_by(StrategyRunUserStatus.id.asc())
            .first()
        )
        return run_user

    def _allocate_strategy_user(self) -> StrategyRunUserStatus:
        run_user = self.get_usable_strategy_user()
        if run_user:
            return run_user

        # 没有可用的，尝试创建
        cur_num = self.get_strategy_user_nums()
        if cur_num >= self.max_run_user_num:
            raise InvalidArgument(message=gettext('运行中（包括已暂停）的策略不得超过%(max_count)s个', max_count=self.max_run_user_num))

        _name = f"_sys_strategy_{cur_num + 1}"
        _use, _sub, new_run_user = self.new_strategy_user(_name)
        return new_run_user

    def allocate_strategy_user(self) -> StrategyRunUserStatus:
        """ 分配一个可用的策略子账号 """
        run_user = self._allocate_strategy_user()
        run_user.status = StrategyRunUserStatus.Status.UNUSABLE
        return run_user

    @classmethod
    def free_strategy_user(cls, run_user: StrategyRunUserStatus):
        run_user.status = StrategyRunUserStatus.Status.USABLE


class BaseStrategyManager:
    def __init__(self, main_user_id: int):
        self.main_user_id = main_user_id

    def new_strategy(self, type_: UserStrategy.Type) -> UserStrategy:
        run_user = RunUserManager(self.main_user_id).allocate_strategy_user()
        basic_sty = UserStrategy(
            user_id=self.main_user_id,
            type=type_,
            run_user_id=run_user.user_id,
        )
        db.session.add(basic_sty)
        db.session.flush()
        return basic_sty

    def set_strategy_running(self, basic_sty: UserStrategy):
        basic_sty.started_at = now()
        basic_sty.status = UserStrategy.Status.RUNNING

    def set_strategy_paused(self, basic_sty: UserStrategy):
        basic_sty.status = UserStrategy.Status.PAUSED

    def set_strategy_terminated(self, basic_sty: UserStrategy):
        """ 设置策略状态为已终止，策略执行用户设置为可用 """
        basic_sty.terminated_at = now()
        basic_sty.status = UserStrategy.Status.TERMINATED
        run_user: StrategyRunUserStatus = StrategyRunUserStatus.query.filter(
            StrategyRunUserStatus.user_id == basic_sty.run_user_id,
            StrategyRunUserStatus.main_user_id == self.main_user_id,
        ).first()
        RunUserManager(self.main_user_id).free_strategy_user(run_user)
