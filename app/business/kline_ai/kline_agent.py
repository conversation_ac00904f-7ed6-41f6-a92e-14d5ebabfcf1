# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Set

import math
import pandas as pd
from flask import current_app

from app import Language, config
from app.business import ServerClient, PerpetualServerClient
from app.business.clients.ai_translate import AITranslateClient, ModelType, ModelFamilyName, FormatType
from app.business.kline_ai.kline_analyzer import AnalyzeResult
from app.business.kline_ai.kline_analyzer import KlineAnalyzer
from app.business.kline_ai.kline_backtest_constant import BACKTEST_THRESHOLD
from app.business.kline_ai.kline_calculator import KlineCalculator
from app.caches import PerpetualMarketCache
from app.caches.kline import AssetRankCache
from app.caches.kline_ai import KlineAnalysisLastUpdateCache
from app.caches.spot import MarketCache
from app.common import TradeBusinessType, PerpetualMarketType
from app.models.base import db
from app.models.kline_ai import KlineAnalyst, KlineAnalysisBatch, KlineAnalysis, KlineAnalysisContent
from app.models.mongo.translation import TranslationTaskMySQL
from app.utils import current_timestamp, RESTClient, g_map

TIMEFRAMES = {
    900: '15min',
    3600: '1hour',
    14400: '4hour',
    86400: '1day',
    604800: '1week',
}


class KlineAgent:

    def __init__(self, analyst: KlineAnalyst):
        self.analyst = analyst

    def analyze_markets(self):
        """分析全部有效市场 K线数据"""
        time_rec = [current_timestamp(to_int=True)]
        # 1. 获取满足分析条件的市场
        available_markets = self._get_available_markets()
        if len(available_markets) == 0:
            current_app.logger.info(f"Kline Analyze: [{self.analyst.name}], NOT any market in condition!")
            return 0
        current_app.logger.warning(f"Kline Analyze: [{self.analyst.name}], total markets: {len(available_markets)}")
        current_ts = current_timestamp(to_int=True)
        analyst = self.analyst
        batch = KlineAnalysisBatch(
            analyst_id=analyst.id,
            market_count=len(available_markets),
            kline_time_start=analyst.get_start_time(current_ts),
            kline_time_end=current_ts,
        )
        db.session.add(batch)
        db.session.commit()
        current_app.logger.info(f"Kline Analyze: [{self.analyst.name}], new batch [{batch.id}]")

        # 2. 分析所有市场
        time_rec.append(current_timestamp(to_int=True))
        #   并发分析
        results = g_map(self._analyze_single_market, available_markets, size=8)
        total = len(results)
        results = [result for result in results if result is not None]
        current_app.logger.info(f"Kline Analyze: [{analyst.name}]-batch[{batch.id}], analyze complete. "
                                f"total [{total}], non-empty [{len(results)}]")
        #   记录分析结果
        source_lang = Language.ZH_HANS_CN
        for result in results:
            # 保存分析结果
            analysis = KlineAnalysis(
                analyst_id=analyst.id,
                batch_id=batch.id,
                market=result.market,
                business_type=result.business_type,
                bp=result.bp.content.get('score') if result.bp is not None else None,
                input_token_count=result.input_tokens,
                output_token_count=result.output_tokens,
                created_at=result.created_at,  # 以K线数据的最后一条时间戳为准
                kline_time_end=result.kline_time_end,
                close_price=result.close_price,
                interval=analyst.interval,
            )
            current_app.logger.warning(f"Kline Analyze, analysis created: [{analyst.name}]-analysis[{analysis.id}], "
                                       f"kline_time_end: {analysis.kline_time_end}")
            db.session.add(analysis)
            db.session.flush()

            result.analysis_id = analysis.id
            result.updated_at = analysis.updated_at
            result.analyze_batch_id = batch.id
            # 保存原文分析内容
            content = KlineAnalysisContent(
                analysis_id=analysis.id,
                lang=source_lang.name,
                text=result.summary.content['text'],
                input_token_count=0,
                output_token_count=0,
            )
            db.session.add(content)
            db.session.commit()
            # 更新时间戳缓存
            (KlineAnalysisLastUpdateCache(analyst.id, result.market_with_type)
             .set(str(int(datetime.timestamp(result.created_at)))))
            current_app.logger.info(f'Update last_update_cache: analyst_id:{analyst.id}, '
                                    f'market:{result.market_with_type}')
            if abs(analysis.bp) > BACKTEST_THRESHOLD:
                # 模拟本金回测
                from app.schedules.kline_ai import kline_open_sim_trade
                kline_open_sim_trade.delay(analysis.id)

        # 3. 启动异步翻译任务
        time_rec.append(current_timestamp(to_int=True))
        current_app.logger.warning(f"Kline Analyze: [{analyst.name}]-batch[{batch.id}], "
                                f"star translate async: {len(results)}")
        g_map(self._translate, results, size=15)

        time_rec.append(current_timestamp(to_int=True))
        # 4. 输出最终花费时间
        current_app.logger.warning(f"Kline Analyze Complete: [{analyst.name}]-batch[{batch.id}], "
                                   f"total [{len(results)}] markets, "
                                   f"first market: {results[0].market if len(results)>0 else ''}, "
                                   f"Total time：{time_rec[-1] - time_rec[0]}s, "
                                   f"get markets：{time_rec[1] - time_rec[0]}s, "
                                   f"analyze：{time_rec[2] - time_rec[1]}s "
                                   f"translate async：{time_rec[3] - time_rec[2]}s "
                                   )
        return batch.id

    def _analyze_single_market(self, market_with_type: str) -> AnalyzeResult | None:
        analyst = self.analyst
        current_app.logger.info(f"Kline Analyze:[{analyst.name}]-[{market_with_type}], start")

        market, business_type = market_with_type.split('-')
        business_type = TradeBusinessType[business_type]

        kline_df = self._kline_data_for_analyze(market, business_type)
        if len(kline_df) == 0:
            current_app.logger.exception(f"Kline Analyze:[{analyst.name}]-[{market_with_type}], get kline data failed")
            return None
        KlineCalculator(kline_df).calculate_ma().calculate_macd().calculate_bollinger_bands()

        analyzed_tuple = KlineAnalyzer.analyze(kline_df,market=market)
        if analyzed_tuple is None:
            return None
        result = AnalyzeResult(*analyzed_tuple)
        result.market = market
        result.business_type = business_type
        result.market_with_type = market_with_type
        result.created_at = kline_df['datetime'].iloc[-1]
        result.kline_time_end = kline_df['timestamp'].iloc[-1]
        result.close_price = kline_df['close'].iloc[-1]
        current_app.logger.info(f"Kline Analyze:[{analyst.name}]-[{market_with_type}], complete："
                                f"bullish probability [{result.bp.content}]")
        return result

    def _kline_data_for_analyze(self, market: str, business_type: TradeBusinessType) -> pd.DataFrame:
        """获取用于分析的K线数据"""
        analyst = self.analyst
        interval = analyst.interval
        end_time = current_timestamp(to_int=True)
        start_time = analyst.get_start_time(end_time)
        df = self._get_kline_data(market, business_type, start_time, end_time, interval)
        if len(df) == 0:
            return df
        if end_time - df['timestamp'][len(df) - 1] < interval:
            df = df[:-1]  # 去掉最后一条数据，避免用未完成的K线做分析

        if business_type == TradeBusinessType.PERPETUAL:
            # 对于反向合约市场，需要交换 amount 和 volume 两列的内容
            if market_info := PerpetualMarketCache().get_market_info(market):
                if market_info['type'] == PerpetualMarketType.INVERSE:
                    df.rename(columns={'amount': 'amount1'}, inplace=True)
                    df.rename(columns={'volume': 'amount'}, inplace=True)
                    df.rename(columns={'amount1': 'volume'}, inplace=True)
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
        df['datetime_str'] = df['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
        df.index = pd.DatetimeIndex(df['datetime'])
        # df['datetime_tz'] = df['datetime'].dt.tz_localize('UTC').dt.tz_convert(self.target_tz)
        # df.index = df.index.tz_localize('UTC').tz_convert('Asia/Shanghai')
        return df

    def _get_available_markets(self):
        """获取全部需要分析的市场，合约市场的价格走势与现货市场一致，所以不再分析合约市场
        这里还是保留 business_type 相关信息，以防止将来有特别的合约市场需要单独分析
        return example: ['BTCUSDT-SPOT', 'ETHUSDT-SPOT']
        """
        analyst = self.analyst
        available_markets = []

        if analyst.market_range == KlineAnalyst.MarketRange.ALL:
            spot_markets = set(MarketCache.list_online_markets())
        elif analyst.market_range == KlineAnalyst.MarketRange.HOT100:
            spot_markets = self._get_hot100_markets()
        else:
            raise ValueError(f"Kline Analyze: Unknown market range: {analyst.market_range}")

        for market in spot_markets:
            if market_with_type := self._check_condition(market, TradeBusinessType.SPOT):
                available_markets.append(market_with_type)

        return available_markets

    def _check_condition(self, market: str, business_type: TradeBusinessType) -> str | None:
        """检查是否满足触发分析条件"""
        analyst = self.analyst
        market_with_type = f'{market}-{business_type.name}'

        current = current_timestamp(to_int=True)

        last_update = KlineAnalysisLastUpdateCache(analyst.id, market_with_type).get()

        if last_update is None:
            current_app.logger.info(
                f"Kline Analyze:[{analyst.name}] 1st time analyze [{market_with_type}], trigger."
            )
            return market_with_type

        last_update = int(last_update)
        elapsed_time = (current - last_update) // 60
        if elapsed_time < analyst.trigger_period_min:
            current_app.logger.info(
                f"Kline Analyze:[{analyst.name}], market [{market_with_type}], "
                f"It has been [{elapsed_time}] minutes since the last update, "
                f"which is less than the minimum period [{analyst.trigger_period_min}m], NOT trigger."
            )
            return None

        if elapsed_time > analyst.trigger_period_max:
            current_app.logger.info(
                f"Kline Analyze:[{analyst.name}, market [{market_with_type}], "
                f"It has been [{elapsed_time}] minutes since the last update, "
                f"which is more than the maximum period [{analyst.trigger_period_max}m], trigger."
            )
            return market_with_type

        volatility = self._get_volatility(market, business_type, current, last_update, analyst.interval)
        if volatility >= analyst.trigger_volatility:
            current_app.logger.info(
                f"Kline Analyze:[{analyst.name}], market [{market_with_type}], volatility [{volatility}%], "
                f"exceeds the threshold [{analyst.trigger_volatility}%], trigger"
            )
            return market_with_type
        else:
            current_app.logger.info(
                f"Kline Analyze:[{analyst.name}], market [{market_with_type}], volatility [{volatility}%], "
                f"below the threshold [{analyst.trigger_volatility}%], NOT trigger."
            )
            return None

    @staticmethod
    def _translate(result: AnalyzeResult):
        content = result.summary.content['text']

        translator = AITranslateClient(
            format_type=FormatType.TEXT,
            model_family=ModelFamilyName.GEMINI,
            model_type=ModelType.POWERFUL,
            business=TranslationTaskMySQL.Business.KLINE_ANALYSIS,
        )
        source_lang = Language.ZH_HANS_CN
        translations = {}
        for target_lang in Language:
            if target_lang == source_lang:
                # 原文不需要翻译
                continue
            current_app.logger.warning(f"Kline Analyze: translate async start [{result.analysis_id}], "
                                    f"[{result.market_with_type}] to '{target_lang.name}'")
            # 翻译
            try:
                translation = translator.translate_async(
                    content=content,
                    source=source_lang,
                    target=target_lang,
                    business_id=str(result.analysis_id),
                )
                translations[target_lang] = translation
                current_app.logger.warning(f"Kline Analyze: translate async complete [{result.analysis_id}], "
                                           f"[{result.market_with_type}] to '{target_lang.name}'")
            except Exception as e:
                current_app.logger.exception(f"Kline Analyze: translate failed: {e}")
        return translations

    @staticmethod
    def _get_hot100_markets() -> Set[str]:
        """获取市值或交易量 Top100 的市场"""
        top_circulation_assets = AssetRankCache('circulation_usd').read_assets()
        top_circulation_assets.remove('USDT')
        top_volume_assets = AssetRankCache('volume_usd').read_assets()
        top_volume_assets.remove('USDT')

        top_num = 10 if 'test.' in config['SITE_URL'] else 100
        hot_assets = set(top_circulation_assets[:top_num] + top_volume_assets[:top_num])
        hot_markets = set([f'{asset}USDT' for asset in hot_assets])
        current_app.logger.info(f"Kline Analyze: [{len(hot_markets)}] hot markets")
        return hot_markets

    @staticmethod
    def _get_volatility(market: str, business_type: TradeBusinessType, current: int, last_update: int,
                        interval: int) -> int:
        """获取波动率, 百分数"""
        df = KlineAgent._get_kline_data(market, business_type, last_update, current, interval)
        if len(df) == 0:
            return 0
        high_prices = df['high'].astype(float)
        highest_price = high_prices.max()
        low_prices = df['low'].astype(float)
        lowest_price = low_prices.min()

        return int((highest_price / lowest_price - 1) * 100)

    @staticmethod
    def _get_kline_data(market: str, business_type: TradeBusinessType, start_time: int, end_time: int, interval: int) \
            -> pd.DataFrame:
        # 获取K线数据
        # return KlineAgent._get_kline_data_on_testing(market, business_type, start_time, end_time, interval)
        c = ServerClient() if business_type == TradeBusinessType.SPOT else PerpetualServerClient()
        klines = c.market_kline(market=market, start_time=start_time, end_time=end_time, interval=interval)
        df = pd.DataFrame(klines, columns=['timestamp', 'open', 'close', 'high', 'low', 'amount', 'volume'])
        return df

    @staticmethod
    def _get_kline_data_on_testing(market: str, business_type: TradeBusinessType,
                                   start_time: int, end_time: int, interval: int) -> pd.DataFrame:
        # 测试环境下，使用API获取正式K线数据
        current = current_timestamp(to_int=True)
        c = RESTClient('https://api.coinex.com/v2')
        limit = math.ceil((current - start_time) / interval)
        if limit > 1000:
            current_app.logger.info(f"Kline Analyze: limit can not exceed 1000 in kline interface v2, "
                             f"start_time:{start_time}, current_time:{current}, interval: {interval}")
        path = '/spot/kline' if business_type == TradeBusinessType.SPOT else '/futures/kline'
        try:
            klines = c.get(path,
                           market=market,
                           limit=limit,
                           period=TIMEFRAMES[interval],
                           )
        except RESTClient.BadResponse as e:
            current_app.logger.exception(f"Kline Analyze:[{market}-{business_type}], get kline data failed: {e}")
            return []
        if klines['code'] != 0:
            current_app.logger.exception(
                f"Kline Analyze:[{market}-{business_type}], get kline data failed: {klines}"
            )
            return []
        df = pd.DataFrame.from_dict(klines['data'])
        df.rename(columns={'value': 'amount', 'created_at': 'timestamp'}, inplace=True)
        df['timestamp'] = df['timestamp'] // 1000
        df = df[df['timestamp'] < end_time]
        return df
