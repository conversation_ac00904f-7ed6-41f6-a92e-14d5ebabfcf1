# -*- coding: utf-8 -*-
from flask_babel import gettext as _
from app.business import <PERSON><PERSON><PERSON>ock, LockKeys, \
    PerpetualServerClient
from app.exceptions.perpetual import PerpetualResponseCode


def adjust_leverage(user_id: int, market: str, position_type: int,
                    leverage: str):
    """杠杆调整"""
    with CacheLock(LockKeys.adjust_leverage(user_id, market), wait=False):
        client = PerpetualServerClient()
        try:
            return client.adjust_leverage(user_id, market, position_type, leverage)
        except client.BadResponse as e:
            if e.code == PerpetualResponseCode.CONTRACT_BALANCE_NOT_ENOUGH:
                e.message_template = _('杠杆倍数过低，可用保证金不足')
            elif e.code == PerpetualResponseCode.CONTRACT_AMOUNT_EXCEED_LIMIT:
                e.message_template = _('杠杆倍数超过当前仓位允许的最大值')
            raise
        