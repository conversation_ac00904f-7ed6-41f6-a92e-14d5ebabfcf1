# -*- coding: utf-8 -*-
import json
from flask_babel import force_locale, gettext

from app.caches.perpetual import PerpetualAdlNoticeCache
from ..copy_trading.message import FollowerMessageSender
from ..copy_trading.trader import CopyTraderManager

from ..push import send_perpetual_liquidation_push, \
    send_perpetual_liquidation_warning_push, send_perpetual_adl_push
from ..user import UserPreferences
from ..email import send_perpetual_liquidation_email, \
    send_perpetual_adl_email, send_perpetual_liquidation_warning_email
from app.caches import PerpetualLiquidationWarningCache, PerpetualMarketCache
from app.common import (
    CeleryQueues,
    MessageTitle,
    MessageContent,
    MessageWebLink,
    position_side_map,
    perpetual_direction_map,
    adl_order_side_map,
    PerpetualMarketType,
    PERPETUAL_MARKET_TYPE_MAP,
)
from app.models import User, db, Message
from app.utils import route_module_to_celery_queue, celery_task, amount_to_str, current_timestamp
from ...models.copy_trading import CopyFollowerHistory

route_module_to_celery_queue(__name__, CeleryQueues.PERPETUAL)


def get_liq_price_prec(market: str):
    market_info = PerpetualMarketCache().get_market_info(market)
    if market_info['type'] == PerpetualMarketType.DIRECT.value:
        return int(market_info['money_prec'])
    return 2

@celery_task
def perpetual_liquidation_notice(
    user_id: int, market: str, side: int, leverage: str, amount: str,
    sign_price: str, bkr_price: str, liq_price: str
):
    """
    合约爆仓通知
    """
    user = User.query.get(user_id)
    if not user:
        return

    if user.user_type == User.UserType.SUB_ACCOUNT:
        # 处理子账号并且为跟单的情况
        follower_history = CopyFollowerHistory.query.filter(
            CopyFollowerHistory.sub_user_id == user_id,
            CopyFollowerHistory.status == CopyFollowerHistory.Status.FOLLOWING
        ).first()
        if follower_history:
            trader = CopyTraderManager.get_trader(follower_history.copy_trader_user_id)
            FollowerMessageSender.send_position_liquidation(follower_history.user_id, trader, market, leverage)
            return

    liq_price = amount_to_str(liq_price, get_liq_price_prec(market))
    lang = UserPreferences(user_id).language
    with force_locale(lang.value):
        side_type = gettext(position_side_map[side])
        direction_type = gettext(perpetual_direction_map[side])
        info = PerpetualMarketCache().get_market_info(market)
        market_type = PERPETUAL_MARKET_TYPE_MAP[info["type"]]
    db.session_add_and_commit(
        Message(
            user_id=user_id,
            title=MessageTitle.PERPETUAL_LIQUIDATE.name,
            content=MessageContent.PERPETUAL_LIQUIDATE.name,
            params=json.dumps(
                dict(
                    market=market,
                    market_type=market_type,
                    liq_price=liq_price,
                )
            ),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.PERPETUAL_DEAL_RECORD_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.TRADE_NOTIFICATION,
        )
    )

    send_perpetual_liquidation_push.delay([user_id], market, current_timestamp(to_int=True))
    send_perpetual_liquidation_email.delay(
        user.id, market, side_type, leverage,
        amount, sign_price, bkr_price, liq_price, direction_type,
    )


@celery_task
def perpetual_adl_notice(
    user_id: int, market: str, side: int, amount: str, price: str
):
    """
    合约自动减仓通知
    """
    user = User.query.get(user_id)
    if not user:
        return
    if user.user_type in (
        User.UserType.EXTERNAL_MAKER,
        User.UserType.INTERNAL_MAKER,
        User.UserType.EXTERNAL_CONTRACT_MAKER,
        User.UserType.EXTERNAL_SPOT_MAKER,
    ):
        cache = PerpetualAdlNoticeCache(market, user_id)
        if cache.exists():
            return
        cache.gen()
    price = amount_to_str(price, get_liq_price_prec(market))

    lang = UserPreferences(user_id).language
    with force_locale(lang.value):
        deal_type = gettext(adl_order_side_map[side])
        info = PerpetualMarketCache().get_market_info(market)
        market_type = PERPETUAL_MARKET_TYPE_MAP[info["type"]]

    db.session_add_and_commit(
        Message(
            user_id=user_id,
            title=MessageTitle.PERPETUAL_ADL_NOTICE.name,
            content=MessageContent.PERPETUAL_ADL_NOTICE.name,
            params=json.dumps(dict(
                market=market,
                market_type=market_type,
                liq_amount=amount,
                liq_price=price,
            )),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.PERPETUAL_CURRENT_POSITION_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.TRADE_NOTIFICATION,
        )
    )

    send_perpetual_adl_push([user_id], market, current_timestamp(to_int=True))
    send_perpetual_adl_email.delay(user.id, market, deal_type, amount, price)


@celery_task
def perpetual_liquidation_warning(
    user_id: int, market: str, liq_risk: str
):
    """
    合约爆仓警告
    """
    user = User.query.get(user_id)
    if not user:
        return
    liq_risk = float(liq_risk)
    if 0.7 <= liq_risk < 0.9:
        cache = PerpetualLiquidationWarningCache(market, user_id, '0.7')
        if cache.exists():
            return
        cache.gen()
    if liq_risk >= 0.9:
        cache = PerpetualLiquidationWarningCache(market, user_id, '0.9')
        if cache.exists():
            return
        cache.gen()
    risk_str = amount_to_str(str(liq_risk * 100), 2)
    send_perpetual_liquidation_warning_email.delay(user.id, market, risk_str)
    send_perpetual_liquidation_warning_push.delay([user_id], market, risk_rate=risk_str,
                                                  created_at=current_timestamp(to_int=True))
