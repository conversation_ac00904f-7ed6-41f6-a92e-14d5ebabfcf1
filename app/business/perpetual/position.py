# -*- coding: utf-8 -*-
from datetime import date, timedelta
from decimal import Decimal
import json
from enum import Enum, IntEnum
from collections import defaultdict, Counter
from typing import Union, Optional, Dict, Set, List, Generator

from app.business import UserPreferences, send_perpetual_profit_loss_email, \
    send_perpetual_position_close_result_email, \
    send_perpetual_position_reduce_email, CacheLock, LockKeys, PerpetualServerClient, send_open_pos_tp_sl_fail_email
from app.business.push import send_perpetual_take_profit_notice_push, send_perpetual_stop_loss_notice_push
from app.utils import today
from app.business.external_dbs import PerpetualHistoryDB
from app.common import (
    MessageTitle,
    MessageContent,
    MessageWebLink,
    PerpetualMarketType,
    CeleryQueues,
    PositionDealIntType,
    PERPETUAL_MARKET_TYPE_MAP,
    PositionSide, FundingRateType,
)
from app.caches import PerpetualMarketCache
from app.exceptions import InvalidArgument
from app.models import User, db, Message, PerpetualMarket
from app.utils import celery_task, route_module_to_celery_queue, batch_iter, current_timestamp
from flask_babel import gettext as _, force_locale, gettext
from app.utils.amount import AmountType, quantize_amount_non_zero
from app.utils.date_ import date_to_datetime
from app.utils.push import WebPagePath, AppPagePath

route_module_to_celery_queue(__name__, CeleryQueues.PERPETUAL)


class ClosePositionOperationType(Enum):
    TAKE_PROFIT = _('止盈')
    STOP_LOSS = _('止损')
    MARKET_CLOSE = _('合约平仓')
    CLOSE_ALL = _('合约一键平仓')


position_target_map = {
    PositionDealIntType.TYPE_POSITION_OPEN: _('开仓'),
    PositionDealIntType.TYPE_POSITION_INCREASE: _('加仓'),
    PositionDealIntType.TYPE_POSITION_DECREASE: _('减仓'),
    PositionDealIntType.TYPE_POSITION_CLOSE: _('平仓'),
    PositionDealIntType.TYPE_POSITION_SYSTEM_LIQ: _('强制减仓'),
    PositionDealIntType.TYPE_POSITION_LIQ: _('强平'),
    PositionDealIntType.TYPE_POSITION_ADL: _('自动减仓'),
    PositionDealIntType.TYPE_TAKE_PROFIT: _('止盈'),
    PositionDealIntType.TYPE_STOP_LOSS: _('止损'),
    PositionDealIntType.TYPE_MARKET_CLOSE_ALL: _('一键全平'),
    PositionDealIntType.TYPE_CLOSE_ALL: _('一键平仓')
}


class TakeProfitStopLossPriceType(IntEnum):
    DEAL_PRICE = 1
    SIGN_PRICE = 3


@celery_task
def perpetual_profit_loss_notice(user_id: int, market: str,
                                 operation: Union[str, ClosePositionOperationType],
                                 amount: str,
                                 success: bool):
    """
    合约止盈止损触发通知
    """
    user = User.query.get(user_id)
    if not user:
        return
    if isinstance(operation, str):
        operation = getattr(ClosePositionOperationType, operation)
    if user.user_type == User.UserType.SUB_ACCOUNT:
        account_type = 'sub_account'
    else:
        account_type = 'account'
    lang = UserPreferences(user_id).language

    market_record: PerpetualMarket = PerpetualMarket.query.filter(
        PerpetualMarket.name == market
    ).first()
    if market_record.market_type == PerpetualMarketType.DIRECT:
        asset = market_record.base_asset
    else:
        asset = 'cont'
        with force_locale(lang.value):
            asset = _(asset)
    with force_locale(lang.value):
        market_type = _(PERPETUAL_MARKET_TYPE_MAP[market_record.market_type.value])

    if operation == ClosePositionOperationType.TAKE_PROFIT:
        send_perpetual_take_profit_notice_push.delay(user_id, market, market_type, asset, amount, success,
                                                     current_timestamp(to_int=True))
    else:
        send_perpetual_stop_loss_notice_push.delay(user_id, market, market_type, asset, amount, success,
                                                   current_timestamp(to_int=True))
    send_perpetual_profit_loss_email.delay(user.id, market, account_type, operation.name, amount, asset, success, market_type)
    send_tp_sl_notice(operation, user_id, market, market_type, asset, amount, success)


def send_tp_sl_notice(operation, user_id, market, market_type, asset, amount, success):

    if operation == ClosePositionOperationType.TAKE_PROFIT:
        if success:
            title = MessageTitle.TAKE_PROFIT_SUCCESS.name
            content = MessageContent.TAKE_PROFIT_SUCCESS.name
        else:
            title = MessageTitle.TAKE_PROFIT_FAIL.name
            content = MessageContent.TAKE_PROFIT_FAIL.name
    else:
        if success:
            title = MessageTitle.STOP_LOSS_SUCCESS.name
            content = MessageContent.STOP_LOSS_SUCCESS.name
        else:
            title = MessageTitle.STOP_LOSS_FAIL.name
            content = MessageContent.STOP_LOSS_FAIL.name
    extra_info = json.dumps(
        dict(
            web_link=WebPagePath.PERPETUAL_STOP_HISTORY_RECORD.value,
            android_link=AppPagePath.PERPETUAL_ORDER.value.format(type='history_order'),
            ios_link=AppPagePath.PERPETUAL_ORDER.value.format(type='history_order'),
        )
    )
    params = json.dumps(dict(
        market=market,
        market_type=market_type,
        asset=asset,
        amount=amount
    ))

    message = Message(
        user_id=user_id,
        title=title,
        content=content,
        extra_info=extra_info,
        params=params,
        display_type=Message.DisplayType.TEXT,
        channel=Message.Channel.TRADE_NOTIFICATION,
    )
    db.session_add_and_commit(message)


@celery_task
def perpetual_position_close_notice(user_id: int, market: Optional[str],
                                    operation: Union[str, ClosePositionOperationType],
                                    amount: str, success: bool):
    """
    合约平仓(一键平仓/市价全平)通知
    """
    user = User.query.get(user_id)
    if not user:
        return
    if isinstance(operation, str):
        operation = getattr(ClosePositionOperationType, operation)
    if not success:
        market_record: PerpetualMarket = PerpetualMarket.query.filter(
            PerpetualMarket.name == market
        ).first()

        if market_record.market_type == PerpetualMarketType.DIRECT:
            asset = market_record.base_asset
        else:
            asset = 'cont'
            lang = UserPreferences(user_id).language
            with force_locale(lang.value):
                asset = _(asset)
        send_perpetual_position_close_result_email.delay(user.id, market, operation.name, amount, asset)


@celery_task
def perpetual_position_reduce_notice(user_id: int, market: str, amount: str, sign_price: str):
    """
    合约降档减仓通知
    """
    user = User.query.get(user_id)
    if not user:
        return

    lang = UserPreferences(user_id).language
    with force_locale(lang.value):
        info = PerpetualMarketCache().get_market_info(market)
        market_type = gettext(PERPETUAL_MARKET_TYPE_MAP[info["type"]])
        money_asset = info["money"]
        if info["type"] == PerpetualMarketType.DIRECT:
            asset = info["stock"]
        else:
            asset = _("cont")

    amount_str = f'{amount} {asset}'
    sign_price_str = f"{sign_price} {money_asset}"
    db.session.add(
        Message(
            user_id=user_id,
            title=MessageTitle.PERPETUAL_POSITION_REDUCE.name,
            content=MessageContent.PERPETUAL_POSITION_REDUCE.name,
            params=json.dumps(dict(
                market=market,
                market_type=market_type,
                amount=amount_str,
                sign_price=sign_price_str,
            )),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.PERPETUAL_CURRENT_POSITION_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.TRADE_NOTIFICATION,
        )
    )
    db.session.commit()
    send_perpetual_position_reduce_email.delay(user.id, market, amount, sign_price_str)


def get_profit_unreal(
        sign_price: AmountType,
        side: Union[int, PositionSide],
        settle_price: AmountType,
        amount: AmountType,
        market_type: Union[int, PerpetualMarketType]
) -> Decimal:
    """
    获取合约未实现盈亏
    （多仓）未实现盈亏=合约数量*合约面值*（1/结算价格-1/标记价格）
    （空仓）未实现盈亏=合约数量*合约面值*（1/标记价格-1/结算价格）
    :param sign_price: 标记价格
    :param side: 方向 1:空仓, 2:多仓
    :param settle_price: 结算价格
    :param amount: 合约数量
    :return:
    """
    profit_unreal = Decimal()
    sign_price = Decimal(sign_price)
    settle_price = Decimal(settle_price)
    amount = Decimal(amount)
    if sign_price:
        if side == PositionSide.SHORT:
            if market_type == PerpetualMarketType.INVERSE:
                profit_unreal = amount * (1 / sign_price - 1 / settle_price)
            else:
                profit_unreal = amount * (settle_price - sign_price)
        else:
            if market_type == PerpetualMarketType.INVERSE:
                profit_unreal = amount * (1 / settle_price - 1 / sign_price)
            else:
                profit_unreal = amount * (sign_price - settle_price)
    return profit_unreal


def set_settle_switch(user_id: int, status: int, market: str = None):
    with CacheLock(LockKeys.set_settle_switch(user_id, market), wait=False):
        client = PerpetualServerClient()
        client.set_settle_switch(user_id, status, market)


@celery_task
def perpetual_open_pos_tp_sl_fail_notice(user_id: int, market: str, type_: str):
    """合约开仓止盈止损失败通知"""
    if type_ == 'stop_loss':
        content = MessageContent.OPEN_POSITION_STOP_LOSS_FAIL.name
    elif type_ == 'take_profit':
        content = MessageContent.OPEN_POSITION_TAKE_PROFIT_FAIL.name
    else:
        raise InvalidArgument(message='止盈止损类型错误')
    web_link_market = get_web_link_market(market)

    _perpetual_open_pos_tp_sl_fail_notice(user_id, market, web_link_market, content)
    send_open_pos_tp_sl_fail_email.delay(user_id, market, web_link_market, type_)


def _perpetual_open_pos_tp_sl_fail_notice(user_id: int, market: str, web_link_market: str, content: str):
    user = User.query.get(user_id)
    if not user:
        return
    db.session.add(
        Message(
            user_id=user_id,
            title=MessageTitle.OPEN_POSITION_TP_SL_FAIL.name,
            content=content,
            params=json.dumps(dict(
                market=market,
            )),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.PERPETUAL_MARKET_TRADE_PAGE.value.format(market=web_link_market),
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.TRADE_NOTIFICATION,
        )
    )
    db.session.commit()


def get_web_link_market(market):
    info = PerpetualMarketCache().get_market_info(market)
    fmt_market = f'{info["stock"]}-{info["money"]}'.lower()
    return fmt_market


def get_adl_liq_positions(start_time: int, end_time: int, user_position_map: Dict[int, List[int]]) -> Set[int]:
    """
    筛选出有触发自动减仓的仓位
    判断仓位是否有触发自动减仓：
    1. 从 positionliq_history 表中拿 position_id、user_id，
    2. 去 deal_history_(分表编号) 找到 deal_id、deal_user_id，
    3. 去对手的 deal_history_(分表编号) 中看 deal_type，如果 deal_type 为 9 为 13，则为触发了自动减仓
    """
    batch_size = 2000
    user_ids = list(user_position_map.keys())
    deal_result = []
    dbs_tables = PerpetualHistoryDB.users_to_dbs_and_tables(user_ids, 'deal_history')
    for db_tables in dbs_tables:
        _db = db_tables[0]
        columns = ('market', 'position_id', 'user_id', 'deal_user_id', 'deal_id')
        for _table, _table_user_ids in db_tables[1].items():
            for _u_ids in batch_iter(_table_user_ids, batch_size):
                _position_ids = []
                for u_id in _u_ids:
                    _position_ids.extend(user_position_map[u_id])
                for _p_ids in batch_iter(_position_ids, batch_size):
                    records = _db.table(_table).select(
                        *columns,
                        where=f"user_id in ({','.join(map(str, _u_ids))}) "
                              f"AND position_id in ({','.join(map(str, _p_ids))})",
                    )
                    deal_result.extend(list(dict(zip(columns, item)) for item in records))

    def _get_deal_position_map_key(deal_id, user_id, deal_user_id):
        return str(deal_id) + '_' + str(user_id) + '_' + str(deal_user_id)

    deal_position_map = {}
    user_deal_map = defaultdict(list)
    for row in deal_result:
        user_deal_map[row['deal_user_id']].append(row['deal_id'])
        _key = _get_deal_position_map_key(row['deal_id'], row['user_id'], row['deal_user_id'])
        deal_position_map[_key] = row['position_id']

    deal_user_ids = list(user_deal_map.keys())
    result = []
    dbs_tables = PerpetualHistoryDB.users_to_dbs_and_tables(deal_user_ids, 'deal_history')
    for db_tables in dbs_tables:
        _db = db_tables[0]
        columns = ('market', 'user_id', 'deal_user_id', 'deal_id')
        for _table, _table_user_ids in db_tables[1].items():
            for _u_ids in batch_iter(_table_user_ids, batch_size):
                _deal_ids = []
                for u_id in _u_ids:
                    _deal_ids.extend(user_deal_map[u_id])
                for _d_ids in batch_iter(_deal_ids, batch_size):
                    records = _db.table(_table).select(
                        *columns,
                        where=f"time >= {start_time} AND time < {end_time} "
                              f"AND user_id in ({','.join(map(str, _u_ids))}) "
                              f"AND deal_id in ({','.join(map(str, _d_ids))}) "
                              f"AND deal_type in (9, 13)",
                    )
                    result.extend(list(dict(zip(columns, item)) for item in records))

    adl_liq_positions = set()
    for item in result:
        _key = _get_deal_position_map_key(item['deal_id'], item['deal_user_id'], item['user_id'])
        if position_id := deal_position_map.get(_key):
            adl_liq_positions.add(position_id)

    return adl_liq_positions


class PositionFundingSummaryHelper:

    @classmethod
    def get_funding_details(cls,
                            start_date: date,
                            end_date: date) -> Generator:
        limit = 10000
        fields = ["id", "time", "side", "market", "type", "funding_rate", "funding", "asset", "amount", "price"]
        start_ts = int(date_to_datetime(start_date).timestamp())
        end_ts = int(date_to_datetime(end_date).timestamp())

        for _db, _table_str in PerpetualHistoryDB.iter_db_and_table(table_name='position_funding_history'):

            last_id = None
            _table = _db.table(_table_str)

            while True:
                if last_id is None:
                    rows = _table.select(*fields, limit=limit, order_by="id desc")
                else:
                    rows = _table.select(*fields, where=f'id < {last_id}',
                                         limit=limit, order_by="id desc")

                for row in rows:
                    row = dict(zip(fields, row))
                    if start_ts <= row["time"] < end_ts:
                        yield row
                if len(rows) != limit:
                    break
                last_record = dict(zip(fields, rows[-1]))
                if last_record['time'] < start_ts:
                    break
                last_id = last_record['id']

    @classmethod
    def get_usdt_fload_rate(cls):
        """
        I(一日浮动利率) = FRC*A
        其中A = 资金补偿系数，目前固定为0.6
        FRC = 所有市场多头资金费用总和/所有市场的日均持仓价值总和
        所有市场多头资金费用总和（该市场周期内每次支付的多头资金费用总和，既不是总金额也不是净金额）
        日均持仓价值:即该市场在前一日每个收取资金费用时间点的仓位价值（单向）的平均值（只计算资金费率为正的时刻的数据，若都不为正，则该市场不计入统计）
        时间范围为一天，数据更新时间为次日UTC 2:00
        计算出的利率精度取两位有效数字，即如果利率大于 0.1%，取到 0.01%，如果小于 0.1%，取到 0.001%
        """
        summary_fee_data = defaultdict(Decimal)
        position_summary_data = defaultdict(Decimal)
        market_position_total_data = defaultdict(Decimal)
        market_counter = Counter()
        _today = today()
        _yesterday = _today - timedelta(days=1)
        for detail in cls.get_funding_details(_yesterday, _today):
            _ts, _market = detail['time'], detail['market']
            _ts = _ts - _ts % 3600
            position_summary_data[(_market, _ts)] += detail["amount"] * detail["price"]
            if detail["type"] == FundingRateType.PAY and detail['side'] == PositionSide.LONG:
                summary_fee_data[(_market, _ts)] += abs(Decimal(detail['funding']))

        _asset = 'USDT'
        filter_markets = {v.name for v in PerpetualMarket.query.filter(
            PerpetualMarket.market_type == PerpetualMarketType.DIRECT,
            PerpetualMarket.quote_asset == _asset,
        ).with_entities(PerpetualMarket.name).all()}
        final_markets = set()
        fee = Decimal()
        for key, value in summary_fee_data.items():
            if key[0] in filter_markets:
                final_markets.add(key[0])
                fee += Decimal(value)


        for key, value in position_summary_data.items():
            _market, _ts = key
            if _market in final_markets:
                market_position_total_data[_market] += Decimal(value)
                market_counter[_market] += 1

        total = Decimal()
        for _market, _value in market_position_total_data.items():
            total += _value / market_counter[_market]
        # promise positive
        fee = abs(fee)
        total = abs(total / 2)
        # 保留两位有效数字
        return quantize_amount_non_zero(Decimal('0.6') * fee / total if total else Decimal(), 2)


def get_user_profit_real_usd_map(start_timestamp: int, end_timestamp: int, asset_price_map: dict) -> (dict, dict):
    """统计时间内用户正向合约与反向合约盈亏(USD)"""
    records = []
    for db_ in range(len(PerpetualHistoryDB.DBS)):
        for table_ in range(PerpetualHistoryDB.TABLE_COUNT):
            records.extend(
                PerpetualHistoryDB.get_position_history_by_update_time(
                    db_,
                    table_,
                    start_timestamp,
                    end_timestamp,
                )
            )
    direct_user_profit_real_usd_map = defaultdict(Decimal)  # 正向合约盈亏
    inverse_user_profit_real_usd_map = defaultdict(Decimal)  # 反向合约盈亏
    perp_market_cache = PerpetualMarketCache()
    market_price_map = {}
    for record in records:
        _, user_id, market, _, market_type, profit_real = record
        if market in market_price_map:
            market_price = market_price_map[market]
        else:
            market_info = perp_market_cache.get_market_info(market)
            if not market_info:
                market_price = Decimal()
            else:
                balance_asset = perp_market_cache.get_balance_asset(market_info)
                market_price = asset_price_map.get(balance_asset, Decimal())
            market_price_map[market] = market_price
        if market_type == PerpetualMarketType.DIRECT:
            direct_user_profit_real_usd_map[user_id] += (profit_real * market_price)
        else:
            inverse_user_profit_real_usd_map[user_id] += (profit_real * market_price)
    return direct_user_profit_real_usd_map, inverse_user_profit_real_usd_map
