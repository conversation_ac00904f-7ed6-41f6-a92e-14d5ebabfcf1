# -*- coding: utf-8 -*-
from decimal import Decimal

from flask import current_app
from sqlalchemy import func
from app.assets.asset import get_asset_config

from app.business import <PERSON><PERSON><PERSON>ock, LockKeys, PerpetualServerClient, \
    ServerClient
from app.common import BalanceBusiness
from app.exceptions import InsufficientBalance
from app.exceptions.basic import TransferNotAllowed
from app.models import PerpetualBalanceTransfer, db
from app.models.activity import UserCoupon, Coupon
from app.utils import amount_to_str, AmountType, now


def perpetual_transfer_in(
        user_id: int, coin_type: str, amount: AmountType) -> bool:
    from app.business.account import AccountTransferLogHelper

    with CacheLock(
            key=LockKeys.perpetual_balance_transfer_in(user_id=user_id,
                                                       coin_type=coin_type),
            wait=False
    ):
        db.session.rollback()
        if (conf := get_asset_config(coin_type)) and not conf.account_transfer_enabled:
            raise TransferNotAllowed
        amount = Decimal(amount)
        if amount <= Decimal():
            return False

        perpetual_client = PerpetualServerClient(current_app.logger)
        client = ServerClient(current_app.logger)
        res = client.get_user_balances(user_id, coin_type)
        available_amount = Decimal(
            res.get(coin_type, {}).get('available', '0'))
        if available_amount < amount:
            raise InsufficientBalance
        transfer_log = PerpetualBalanceTransfer(
            user_id=user_id,
            coin_type=coin_type,
            amount=amount_to_str(amount, 8),
            status=PerpetualBalanceTransfer.Status.CREATED,
            transfer_type=PerpetualBalanceTransfer.TransferType.TRANSFER_IN
        )
        db.session.add(transfer_log)
        db.session.commit()

        try:
            client.add_user_balance(
                user_id=user_id,
                asset=coin_type,
                amount=amount_to_str(-amount, 8),
                business=BalanceBusiness.PERPETUAL_TRANSFER,
                business_id=transfer_log.id,
                detail={'remark': 'contract transfer in'}
            )
        except Exception as e:
            current_app.logger.error(
                f'{transfer_log.id} {BalanceBusiness.PERPETUAL_TRANSFER.value} failed: {e!r}')
            retry_perpetual_transfer_async(transfer_log)
            return False

        transfer_log.deducted_at = now()
        transfer_log.status = PerpetualBalanceTransfer.Status.DEDUCTED
        db.session.commit()

        try:
            perpetual_client.add_user_balance(
                user_id=user_id,
                asset=coin_type,
                amount=amount_to_str(amount, 8),
                business=BalanceBusiness.PERPETUAL_TRANSFER_IN,
                business_id=transfer_log.id,
                detail={'remark': 'contract transfer in'}
            )
        except Exception as e:
            current_app.logger.error(f'{transfer_log.id} {BalanceBusiness.PERPETUAL_TRANSFER_IN.value} failed: {e!r}')
            retry_perpetual_transfer_async(transfer_log)
            return False
        
        transfer_log.finished_at = now()
        transfer_log.status = PerpetualBalanceTransfer.Status.FINISHED
        db.session.commit()
        AccountTransferLogHelper.add_log_by_transfer(transfer_log)
        return True


def get_user_using_coupon_balance(user_id: int, coin_type: str) -> Decimal:
    using_coupon_balance = UserCoupon.query.join(Coupon).filter(
        UserCoupon.user_id == user_id,
        Coupon.coupon_type == Coupon.CouponType.EXPERIENCE_FEE,
        UserCoupon.coupon_value_type == coin_type,
        UserCoupon.status.in_(
            (UserCoupon.Status.CREATED, UserCoupon.Status.ACTIVE, UserCoupon.Status.TO_BE_RECYCLED)
        )
    ).with_entities(
        func.sum(UserCoupon.coupon_value)
    ).scalar() or Decimal()
    return using_coupon_balance


def perpetual_transfer_out(
        user_id: int, coin_type: str, amount: AmountType) -> bool:
    from app.business.account import AccountTransferLogHelper

    with CacheLock(
            key=LockKeys.perpetual_balance_transfer_out(user_id=user_id,
                                                        coin_type=coin_type),
            wait=False
    ):
        db.session.rollback()
        if (conf := get_asset_config(coin_type)) and not conf.account_transfer_enabled:
            raise TransferNotAllowed
        amount = Decimal(amount)
        if amount <= Decimal():
            return False
        perpetual_client = PerpetualServerClient(current_app.logger)
        client = ServerClient(current_app.logger)
        res = perpetual_client.get_user_balances(user_id, coin_type)
        transfer_amount = Decimal(
            res.get(coin_type, {}).get('transfer', '0'))
        using_coupon_amount = get_user_using_coupon_balance(user_id, coin_type)
        if transfer_amount - using_coupon_amount < amount:
            raise InsufficientBalance
        transfer_log = PerpetualBalanceTransfer(
            user_id=user_id,
            coin_type=coin_type,
            amount=amount_to_str(amount, 8),
            status=PerpetualBalanceTransfer.Status.CREATED,
            transfer_type=PerpetualBalanceTransfer.TransferType.TRANSFER_OUT
        )
        db.session.add(transfer_log)
        db.session.commit()

        try:
            perpetual_client.add_user_balance(
                user_id=user_id,
                asset=coin_type,
                amount=amount_to_str(-amount, 8),
                business=BalanceBusiness.PERPETUAL_TRANSFER_OUT,
                business_id=transfer_log.id,
                detail={'remark': 'contract transfer out'}
            )
        except Exception as e:
            current_app.logger.error(f'{transfer_log.id} {BalanceBusiness.PERPETUAL_TRANSFER_OUT.value} failed: {e!r}')
            return False

        transfer_log.deducted_at = now()
        transfer_log.status = PerpetualBalanceTransfer.Status.DEDUCTED
        db.session.commit()

        try:
            client.add_user_balance(
                user_id=user_id,
                asset=coin_type,
                amount=amount_to_str(amount, 8),
                business=BalanceBusiness.PERPETUAL_TRANSFER,
                business_id=transfer_log.id,
                detail={'remark': 'contract transfer out'}
            )
        except Exception as e:
            current_app.logger.error(f'{transfer_log.id} {BalanceBusiness.PERPETUAL_TRANSFER.value} failed: {e!r}')
            return False

        transfer_log.finished_at = now()
        transfer_log.status = PerpetualBalanceTransfer.Status.FINISHED
        db.session.commit()
        AccountTransferLogHelper.add_log_by_transfer(transfer_log)
        return True


def retry_perpetual_transfer_async(transfer_history):
    from app.schedules.transfer import retry_transfer_perpetual_balance_task

    retry_transfer_perpetual_balance_task.apply_async((transfer_history.id,), countdown=3, expires=60)
