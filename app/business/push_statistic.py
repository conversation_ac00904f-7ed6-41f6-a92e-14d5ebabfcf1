# -*- coding: utf-8 -*-

from __future__ import annotations

from enum import Enum, IntEnum
from decimal import Decimal
from datetime import datetime, date, timedelta
from functools import partial
from types import MappingProxyType
from typing import (NamedTuple, Tuple, Set, Iterable, Callable, Mapping,
                    Pattern, Union, Optional, Any, Dict)

from flask import has_request_context

from app.models.referral import AmbassadorPackageBatch


from ..caches.operation import PushStatisticCache, \
    PushStatisticTimeStampSetCache
from ..caches.user import UserConfigKeyCache
from ..exceptions import OperationNotAllowed
from ..models import User, EmailPush, db, AppPush, PopupWindow, \
    ModelBase, PushUrlConfig, TipBar, AppStartPage
from ..models.activity import CouponApply, PerpetualNoviceBenefits, CouponDistribution
from ..models.equity_center import EquitySendApply
from ..models.message import MessagePush
from ..utils import (timestamp_to_datetime, list_enum_names,
                     timestamp_to_date, celery_task, current_timestamp, now, ConfigField, today_datetime)
from ..common import Language, CeleryQueues
from .user import UserPreferences, UserSettings

PushType = EmailPush.PushType


class FilterType(Enum):

    REGISTRATION_TIME = '注册时间'
    LOGIN_TIME = '登录时间'
    LANGUAGE = '语言'
    BALANCE = '余额'
    ACTIVENESS = '活跃'
    DESIGNATION = '指定名单'
    COUNTRY = '国家'
    MARKET_VALUE = '持仓市值'
    SPOT_DEAL_VOLUME = '成交额-现货交易'
    PERPETUAL_DEAL_VOLUME = '成交额-合约交易'
    TOTAL_DEAL_VOLUME = '成交额-全部交易'
    INVESTMENT_TRANSFER_IN = '成交额-理财'
    AMM_PARTICIPATED_COUNT = 'AMM参与次数'
    SPOT_MARKET = '现货市场'
    PERPETUAL_MARKET = '合约市场'
    LAST_TRADE_TIME = '最后一次'
    USER_SOURCE = '用户来源'
    VIP_LEVEL = 'VIP等级'
    EXPERIENCE_FEE = '合约体验金'
    TRADING_GIFT_PERPETUAL = '交易赠金券-合约交易'
    TRADING_GIFT_SPOT = "交易赠金券-现货交易"


class OperatorType(IntEnum):

    NONE = 0
    UNARY = 1
    BINARY = 2


class Operator(Enum):

    LE = '<='
    LT = '<'
    GE = '>='
    GT = '>'
    EQ = '='
    NE = '!='
    IN = 'in'
    NOT_IN = 'not in'


OT = OperatorType
Op = Operator


class ExpressionConfig:

    ANY_VALUE_LEN = 0, 1 << 32

    def __init__(self,
                 operator_type: OperatorType,
                 operators: Iterable[Operator],
                 value_type: type,
                 value_converter: Callable = None,
                 value_options: Union[Iterable, Callable] = None,
                 value_len: Union[int, Tuple[int, int]] = None,
                 key_pattern: Pattern = None,
                 key_options: Union[Iterable[str], Callable] = None,
                 key_validator: Callable = None):
        self._operator_type = operator_type
        self._operators = frozenset(operators)

        self._value_type = value_type
        if issubclass(value_type, Enum) and value_converter is None:
            value_converter = partial(getattr, value_type)
        self._value_converter = value_converter
        self._value_options = (frozenset(value_options)
                               if (value_options is not None
                                   and not callable(value_options))
                               else value_options)  # for hinting only
        self._value_len = value_len

        self._key_pattern = key_pattern
        self._key_options = (frozenset(key_options)
                             if (key_options is not None
                                 and not callable(key_options))
                             else key_options)  # for hinting only
        self._key_validator = key_validator

    @property
    def operator_type(self):
        return self._operator_type

    @property
    def operators(self):
        return self._operators

    @property
    def value_type(self):
        return self._value_type

    @property
    def value_options(self):
        options = self._value_options
        if options is not None:
            return options() if callable(options) else options
        if issubclass((value_type := self._value_type), Enum):
            # noinspection PyTypeChecker
            return list_enum_names(value_type)
        return None

    @property
    def key_options(self):
        if not callable(options := self._key_options):
            return options
        return options()

    def from_list(self, array: list) -> Expression:
        op_type = self._operator_type
        if len(array) != (expected_size := op_type + 1):
            raise ValueError(f'expected list of size {expected_size}')

        if op_type >= OperatorType.UNARY:
            op = array[-2]
            try:
                op = Operator(op)
            except ValueError:
                raise ValueError(f'invalid operator {op!r}')
            if op not in self._operators:
                raise ValueError(f'invalid operator {op!r}')
        else:
            op = None

        if op_type is OperatorType.BINARY:
            key = array[-3]
            if (key_pattern := self._key_pattern) is not None:
                if not (m := key_pattern.fullmatch(key)):
                    raise ValueError(f'invalid key {key!r}')
                key = m.group(0)
            if (key_validator := self._key_validator) is not None:
                if not key_validator(key):
                    raise ValueError(f'invalid key {key!r}')
        else:
            key = None

        value = array[-1]
        if (value_len := self._value_len) is not None:
            if not isinstance(value, (list, tuple)):
                raise ValueError(f'expected multiple values')
            if isinstance(value_len, int):
                if len(value) != value_len:
                    raise ValueError(f'expected {value_len} value(s)')
            else:
                min_len, max_len = value_len
                if not min_len <= len(value) <= max_len:
                    raise ValueError(f'expected {min_len} to {max_len} values')
            value = list(map(self._convert_value, value))
        else:
            value = self._convert_value(value)

        return Expression(op_type, value, op, key)

    def _convert_value(self, value):
        if (value_converter := self._value_converter) is not None:
            try:
                value = value_converter(value)
            except Exception:
                raise ValueError(f'value {value!r} cannot be converted')
        if not isinstance(value, (value_type := self._value_type)):
            value = value_type(value)
        return value


class Expression(NamedTuple):

    operator_type: OperatorType
    value: Any
    operator: Optional[Operator]
    key: Optional[str]


class TimeRange(Enum):
    SEVEN_DAYS = '最近七天'
    THIRTY_DAYS = '最近三十天'
    NINETY_DAYS = '最近九十天'


class TradeType(Enum):
    SPOT_TRADE = '现货交易'
    PERPETUAL_TRADE = '合约交易'
    TOTAL_TRADE = '全部交易'
    INVESTMENT = '理财'


class ReferType(Enum):
    AMBASSADOR_REFER = '大使refer'
    NORMAL_REFER = '普通refer'
    NOT_REFER = '非refer'


class CouponType(Enum):
    CREATED = "待领取"
    USING = "使用中"


def _list_assets():
    from ..assets import list_all_assets
    return list_all_assets()


def _has_asset(asset: str):
    from ..assets import has_asset
    return has_asset(asset)


def _list_countries_code():
    from app.common import list_country_codes_3, get_country
    return [f'{i} {get_country(i).cn_name}' for i in list_country_codes_3()]


def _list_time_ranges(with_all=False):

    def time_ranges():
        ranges = [e.value for e in TimeRange]
        if with_all:
            ranges.append('全部时间')
        return ranges
    return time_ranges


def _list_trade_types():
    return [e.value for e in TradeType]


def _list_refer_types():
    return [e.value for e in ReferType]


def _list_coupon_types():
    return [e.value for e in CouponType]


def _list_vip_levels():
    from app.business.fee_constant import VIP_LEVEL_DICT
    levels = list(VIP_LEVEL_DICT.keys())
    levels.sort()
    if 0 not in levels:
        levels.insert(0, 0)
    return levels


def _list_spot_markets():
    from app.caches.spot import MarketCache
    return MarketCache.list_online_markets()


def _list_perpetual_markets():
    from app.caches.perpetual import PerpetualMarketCache
    return PerpetualMarketCache().get_market_list()


FILTER_CONFIGS: Mapping[FilterType, Tuple[ExpressionConfig, ...]] \
    = MappingProxyType({
        FilterType.REGISTRATION_TIME: (
            ExpressionConfig(OT.UNARY, [Op.GE, Op.LT], datetime,
                             timestamp_to_datetime),
        ),
        # FilterType.LOGIN_TIME: (
        #     ExpressionConfig(OT.UNARY, [Op.GE, Op.LT], datetime,
        #                      timestamp_to_datetime),
        # ),
        FilterType.LANGUAGE: (
            ExpressionConfig(OT.UNARY, [Op.EQ, Op.NE], Language),
            ExpressionConfig(OT.UNARY, [Op.IN, Op.NOT_IN], Language,
                             value_len=ExpressionConfig.ANY_VALUE_LEN),
        ),
        FilterType.BALANCE: (
            ExpressionConfig(OT.BINARY, [Op.GT, Op.GE, Op.LT, Op.LE], Decimal,
                             key_options=_list_assets,
                             key_validator=_has_asset),
        ),
        FilterType.ACTIVENESS: (
            ExpressionConfig(OT.UNARY, [Op.GE, Op.LE], date, timestamp_to_date
                             ),
        ),
        FilterType.DESIGNATION: (
            ExpressionConfig(OT.UNARY, [Op.IN], list, lambda x: list(map(int, x.split(','))) if isinstance(x, str) else x),
        ),
        FilterType.COUNTRY: (
            ExpressionConfig(OT.UNARY, [Op.EQ, Op.NE], str, value_options=_list_countries_code),
            ExpressionConfig(
                OT.UNARY,
                [Op.IN, Op.NOT_IN],
                list,
                value_options=_list_countries_code,
            ),
        ),
        FilterType.MARKET_VALUE: (
            ExpressionConfig(OT.UNARY, [Op.EQ, Op.GT, Op.GE, Op.LT, Op.LE], Decimal),
        ),
        FilterType.SPOT_DEAL_VOLUME: (
            ExpressionConfig(OT.BINARY, [Op.GT, Op.LT], Decimal, key_options=_list_time_ranges(),),
        ),
        FilterType.PERPETUAL_DEAL_VOLUME: (
            ExpressionConfig(OT.BINARY, [Op.GT, Op.LT], Decimal, key_options=_list_time_ranges(),),
        ),
        FilterType.TOTAL_DEAL_VOLUME: (
            ExpressionConfig(OT.BINARY, [Op.GT, Op.LT], Decimal, key_options=_list_time_ranges(),),
        ),
        FilterType.INVESTMENT_TRANSFER_IN: (
            ExpressionConfig(OT.BINARY, [Op.GT, Op.LT], Decimal, key_options=_list_time_ranges(), ),
        ),
        FilterType.AMM_PARTICIPATED_COUNT: (
            ExpressionConfig(OT.BINARY, [Op.GT, Op.LT], int, key_options=_list_time_ranges(with_all=True),),
        ),
        FilterType.SPOT_MARKET: (
            ExpressionConfig(
                OT.BINARY,
                [Op.EQ],
                str,
                key_options=_list_time_ranges(with_all=True),
                value_options=_list_spot_markets,
            ),
            ExpressionConfig(
                OT.BINARY,
                [Op.IN],
                list,
                key_options=_list_time_ranges(with_all=True),
                value_options=_list_spot_markets,
            ),
        ),
        FilterType.PERPETUAL_MARKET: (
            ExpressionConfig(
                OT.BINARY,
                [Op.EQ],
                str,
                key_options=_list_time_ranges(with_all=True),
                value_options=_list_perpetual_markets,
            ),
            ExpressionConfig(
                OT.BINARY,
                [Op.IN],
                list,
                key_options=_list_time_ranges(with_all=True),
                value_options=_list_perpetual_markets,
            ),
        ),
        FilterType.LAST_TRADE_TIME: (
            ExpressionConfig(
                OT.BINARY,
                [Op.GT],
                date,
                timestamp_to_date,
                key_options=_list_trade_types,
            ),
        ),
        FilterType.USER_SOURCE: (
            ExpressionConfig(OT.UNARY, [Op.EQ], str, value_options=_list_refer_types,),
        ),
        FilterType.VIP_LEVEL: (
            ExpressionConfig(OT.UNARY, [Op.GE, Op.LE], int, value_options=_list_vip_levels,),
        ),
        FilterType.EXPERIENCE_FEE: (
            ExpressionConfig(OT.UNARY, [Op.NOT_IN], list, value_options=_list_coupon_types,),
        ),
        FilterType.TRADING_GIFT_PERPETUAL: (
            ExpressionConfig(OT.UNARY, [Op.NOT_IN], list, value_options=_list_coupon_types,),
        ),
        FilterType.TRADING_GIFT_SPOT: (
            ExpressionConfig(OT.UNARY, [Op.NOT_IN], list, value_options=_list_coupon_types,),
        )
    })


def _get_query_date(expr: Expression):
    query_datetime = _get_query_datetime(expr)
    query_date = query_datetime.date() if query_datetime else None
    return query_date


def _get_query_datetime(expr: Expression):
    if expr.key not in [e.value for e in TimeRange]:
        return None
    query_datetime = datetime.now()
    if expr.key == TimeRange.SEVEN_DAYS.value:
        query_datetime -= timedelta(days=7)
    elif expr.key == TimeRange.THIRTY_DAYS.value:
        query_datetime -= timedelta(days=30)
    elif expr.key == TimeRange.NINETY_DAYS.value:
        query_datetime -= timedelta(days=90)
    return query_datetime


@celery_task(queue=CeleryQueues.STATISTIC)
def email_push_user_statistic(email_push_id: int):
    email_push = EmailPush.query.get(email_push_id)
    if not email_push:
        return

    email_push_parser = EmailPushUserParser(email_push)
    email_push_parser.parse()
    email_push.user_count = len(email_push_parser.parsed_user_ids)
    email_push.subscribe_count = len(email_push_parser.parsed_subscribe_user_ids)
    if email_push_parser.parsed_user_ids:
        email_push.set_user_ids(list(email_push_parser.parsed_user_ids))
    db.session.commit()


@celery_task(queue=CeleryQueues.STATISTIC)
def app_push_user_statistic(app_push_id: int):
    app_push = AppPush.query.get(app_push_id)
    if not app_push:
        return

    app_push_parser = AppPushUserParser(app_push)
    app_push_parser.parse()
    app_push.user_count = len(app_push_parser.parsed_user_ids)
    app_push.subscribe_count = len(app_push_parser.parsed_subscribe_user_ids)
    if app_push_parser.parsed_user_ids:
        app_push.set_user_ids(list(app_push_parser.parsed_user_ids))
    db.session.commit()


@celery_task(queue=CeleryQueues.STATISTIC)
def message_push_user_statistic(msg_push_id: int):
    msg_push: MessagePush = MessagePush.query.get(msg_push_id)
    if not msg_push:
        return

    app_push_parser = MessagePushUserParser(msg_push)
    app_push_parser.parse()
    msg_push.user_count = len(app_push_parser.parsed_user_ids)
    msg_push.subscribe_count = len(app_push_parser.parsed_subscribe_user_ids)
    msg_push.set_user_ids(list(app_push_parser.parsed_user_ids))
    db.session.commit()


class PushUserParser:

    def __init__(self, push: Union[EmailPush, AppPush]):
        self.push = push
        self.parsed_user_ids = set()
        self.parsed_subscribe_user_ids = set()
        self.push_mapping = {}

    def parse(self, allow_update=False) -> Tuple[Set[int], Set[int]]:
        if self.push.user_type is self.push.UserType.ALL_USER:  # 无客群条件
            return self.parse_all_user()
        else:  # 有客群条件
            return self.parse_target_user(allow_update)

    def parse_target_user(self, allow_update=False) -> Tuple[Set[int], Set[int]]:
        if not allow_update and not has_request_context():
            allow_update = True  # 非flask请求，允许更新客群统计
        group_ids = self.push.get_groups()
        if not group_ids:
            return set(), set()
        parsed_user_ids = set()
        group_to_users = UserTagGroupBiz.get_groups_users(group_ids, allow_update)
        for _, user_list in group_to_users.items():
            parsed_user_ids |= set(user_list)
        parsed_user_ids -= self.whitelist_users
        parsed_user_ids = {x for x in parsed_user_ids if x > 0}
        parsed_user_ids, parsed_subscribe_user_ids = self._get_push_type_users(user_ids=parsed_user_ids)
        self.parsed_user_ids = parsed_user_ids
        self.parsed_subscribe_user_ids = parsed_subscribe_user_ids
        return parsed_user_ids, parsed_subscribe_user_ids

    def parse_all_user(self) -> Tuple[Set[int], Set[int]]:
        user_ids = {
            user.id for user in User.query.with_entities(User.id).filter(
                User.email.isnot(None)
            ).all()
        }
        parsed_user_ids, parsed_subscribe_user_ids = self._get_push_type_users(user_ids)
        self.parsed_user_ids = parsed_user_ids
        self.parsed_subscribe_user_ids = parsed_subscribe_user_ids
        return parsed_user_ids, parsed_subscribe_user_ids

    @property
    def whitelist_users(self) -> Set[int]:
        if self.push.whitelist_enabled:
            return set(self.push.cached_user_whitelist)
        return set()

    def _get_push_type_users(self, user_ids: Set[int]) -> Tuple[Set[int], Set[int]]:
        login_disabled_user_ids = self._get_settings_user_ids(UserSettings, UserSettings.login_disabled_by_admin)
        parsed_user_ids = user_ids
        if self.push.push_type is self.push.PushType.SYSTEM:
            if self.push.push_scope_type is self.push.PushScopeType.INCLUDE_UNSUBSCRIBE:
                parsed_subscribe_user_ids = parsed_user_ids - login_disabled_user_ids
            else:
                parsed_subscribe_user_ids = parsed_user_ids - login_disabled_user_ids - self.get_unsubscribed_user_ids()
        else:
            parsed_subscribe_user_ids = set()
            field = self.push_mapping.get(self.push.push_type)
            if field:
                disabled_users = self._get_settings_user_ids(UserPreferences, field, False)
                parsed_subscribe_user_ids = parsed_user_ids - disabled_users - login_disabled_user_ids
        return parsed_user_ids, parsed_subscribe_user_ids

    @classmethod
    def _get_settings_user_ids(cls, config_cls, field: ConfigField, value: bool = True) -> set:
        return get_settings_user_ids(config_cls, field, value)

    @classmethod
    def get_unsubscribed_user_ids(cls):
        raise NotImplementedError


class EmailPushUserParser(PushUserParser):

    def __init__(self, push: EmailPush):
        super().__init__(push)
        self.push_mapping = {
            self.push.PushType.ANNOUNCEMENT: UserPreferences.allows_announcement_emails,
            self.push.PushType.ACTIVITY: UserPreferences.allows_activity_emails,
            self.push.PushType.BLOG: UserPreferences.allows_blog_emails,
        }

    @classmethod
    def get_unsubscribed_user_ids(cls) -> Set[int]:
        announcement_disabled_users = cls._get_settings_user_ids(
                    UserPreferences, UserPreferences.allows_announcement_emails, False)
        activity_disabled_users = cls._get_settings_user_ids(
            UserPreferences, UserPreferences.allows_activity_emails, False)
        blog_disabled_users = cls._get_settings_user_ids(
            UserPreferences, UserPreferences.allows_blog_emails, False)
        return announcement_disabled_users & activity_disabled_users & blog_disabled_users


class AppPushUserParser(PushUserParser):

    def __init__(self, push: AppPush):
        super().__init__(push)
        self.push_mapping = {
            self.push.PushType.ANNOUNCEMENT: UserPreferences.allows_announcement_app,
            self.push.PushType.ACTIVITY: UserPreferences.allows_activity_app,
            self.push.PushType.BLOG: UserPreferences.allows_blog_app,
            self.push.PushType.INFORMATION: UserPreferences.app_information_notice,
        }

    @classmethod
    def get_unsubscribed_user_ids(cls) -> Set[int]:
        announcement_disabled_users = cls._get_settings_user_ids(
            UserPreferences, UserPreferences.allows_announcement_app, False)
        activity_disabled_users = cls._get_settings_user_ids(
            UserPreferences, UserPreferences.allows_activity_app, False)
        blog_disabled_users = cls._get_settings_user_ids(
            UserPreferences, UserPreferences.allows_blog_app, False)
        information_disabled_users = cls._get_settings_user_ids(
            UserPreferences, UserPreferences.app_information_notice, False)
        return announcement_disabled_users & activity_disabled_users & blog_disabled_users & information_disabled_users


class MessagePushUserParser(PushUserParser):

    def __init__(self, push: MessagePush):
        super().__init__(push)
        self.push_mapping = {}

    def parse(self) -> Tuple[Set[int], Set[int]]:
        return self.parse_target_user()

    def _get_push_type_users(self, user_ids: Set[int]) -> Tuple[Set[int], Set[int]]:
        login_disabled_user_ids = self._get_settings_user_ids(UserSettings, UserSettings.login_disabled_by_admin)
        parsed_user_ids = user_ids
        parsed_subscribe_user_ids = parsed_user_ids - login_disabled_user_ids
        return parsed_user_ids, parsed_subscribe_user_ids

    @classmethod
    def get_unsubscribed_user_ids(cls) -> Set[int]:
        return set()


class SimpleUserParser:

    def __init__(self, obj: ModelBase):
        self.obj = obj
        self.is_parse_target = True

    def parse(self, allow_update=False):
        if self.is_parse_target:
            return self.parse_target_user(allow_update)
        else:
            return self.parse_all_user()

    def parse_all_user(self):
        # 特殊处理，全部用户逻辑保持与之前逻辑一致
        return set(), set()

    def parse_target_user(self, allow_update=False):
        if not allow_update and not has_request_context():
            allow_update = True  # 非flask请求，允许更新客群统计
        login_disabled_user_ids = get_settings_user_ids(UserSettings, UserSettings.login_disabled_by_admin)
        group_ids = self.obj.get_groups()
        group_to_users = UserTagGroupBiz.get_groups_users(ids=group_ids, allow_update=allow_update)
        user_ids = set()
        for _, user_list in group_to_users.items():
            user_ids |= set(user_list)
        parsed_user_ids = user_ids - self.whitelist_users
        parsed_subscribe_user_ids = parsed_user_ids - login_disabled_user_ids
        return parsed_user_ids, parsed_subscribe_user_ids

    @property
    def whitelist_users(self) -> Set:
        raise NotImplementedError


class PopupWindowUserParser(SimpleUserParser):

    def __init__(self, popup: PopupWindow):
        super().__init__(popup)
        if self.obj.filter_type is self.obj.FilterType.NONE:
            self.is_parse_target = False

    @property
    def whitelist_users(self) -> Set:
        if self.obj.whitelist_enabled:
            return set(self.obj.cached_user_whitelist)
        return set()


class AppStartPageUserParser(SimpleUserParser):

    def __init__(self, popup: PopupWindow):
        super().__init__(popup)
        if self.obj.filter_type is self.obj.FilterType.NONE:
            self.is_parse_target = False

    @property
    def whitelist_users(self) -> Set:
        if self.obj.whitelist_enabled:
            return set(self.obj.cached_user_whitelist)
        return set()



class TipBarUserParser(SimpleUserParser):

    def __init__(self, bar: TipBar):
        super().__init__(bar)
        if self.obj.filter_type is self.obj.FilterType.NONE:
            self.is_parse_target = False

    @property
    def whitelist_users(self) -> Set:
        if self.obj.whitelist_enabled:
            return set(self.obj.cached_user_whitelist)
        return set()


class EquitySendApplyUserParser(SimpleUserParser):

    def __init__(self, apply: EquitySendApply):
        super().__init__(apply)
        self.is_parse_target = True

    @property
    def whitelist_users(self) -> Set:
        return set()


class CouponApplyUserParser(SimpleUserParser):

    def __init__(self, apply: CouponApply):
        super().__init__(apply)
        self.is_parse_target = False
        if apply.send_user_type in [CouponApply.SendUserType.SOME, CouponApply.SendUserType.DYNAMIC]:
            if apply.source is not CouponApply.Source.EXCHANGE:
                self.is_parse_target = True

    @property
    def whitelist_users(self) -> Set:
        return set()


class PerpetualNoviceUserParser(SimpleUserParser):

    def __init__(self, benefit: PerpetualNoviceBenefits):
        super().__init__(benefit)

    @property
    def whitelist_users(self) -> Set:
        return set()


class CouponDistributionParser(SimpleUserParser):

    def __init__(self, distribution: CouponDistribution):
        super().__init__(distribution)

    @property
    def whitelist_users(self) -> Set:
        return set()


class AmbassadorPackagePushParser(SimpleUserParser):

    def __init__(self, batch: AmbassadorPackageBatch):
        super().__init__(batch)

    @property
    def whitelist_users(self) -> Set:
        return set()
    

class DepositWithdrawalPopupWindowUserParser(PopupWindowUserParser):
    pass


class UserTagGroupBiz:

    @classmethod
    def get_tag_group_info_dict(cls, ids):
        ret = {}
        items = cls.get_tag_group_info(ids)
        for item in items:
            ret[item['id']] = item
        return ret

    @classmethod
    def get_tag_group_info(cls, ids):
        from app.models.user_tag import UserTagGroup

        model = UserTagGroup
        rows = model.query.with_entities(
            model.id,
            model.name,
            model.user_count,
        ).filter(
            model.id.in_(ids)
        ).all()
        ret = []
        for row in rows:
            ret.append({
                'id': row.id,
                'name': row.name,
                'user_count': row.user_count,
            })
        return ret

    @classmethod
    def filter_tag_group_ids(cls, ids):
        from app.models.user_tag import UserTagGroup

        model = UserTagGroup
        rows = model.query.with_entities(
            model.id,
        ).filter(
            model.id.in_(ids),
            model.status == model.Status.PASSED
        ).all()
        return [row.id for row in rows]

    @classmethod
    def get_groups_users(cls, ids, allow_update=False):
        from app.schedules.user_tag import user_tag_group_statistic
        from app.models.user_tag import UserTagGroup

        ret = {}
        model = UserTagGroup
        rows = model.query.filter(
            model.id.in_(ids)
        ).all()
        err = OperationNotAllowed(message="客群未更新，请到用户分群页面更新后重试")
        for row in rows:
            r = row
            if row.group_type is model.GroupType.RULE:
                if row.calc_status is model.CalcStatus.CREATED:
                    if not allow_update:
                        raise err
                    r = user_tag_group_statistic(row.id)
                else:
                    r = row
                    # TODO: 标签更新时间起点
                    if row.last_updated_at.timestamp() < today_datetime().timestamp():
                        if not allow_update:
                            raise err
                        r = user_tag_group_statistic(row.id)

            ret.update({r.id: r.get_user_ids()})
        return ret


def get_settings_user_ids(configs: type[UserPreferences] | type[UserSettings], field: ConfigField, value: bool = True) -> set:
    return UserConfigKeyCache(configs.MODEL, field.name, field.db_value(value)).get_field_value_user_ids()


def get_hour_ts(ts):
    ts = int(ts)
    return ts - ts % 3600


def get_day_ts(ts):
    ts = int(ts)
    return ts - ts % 86400


class PushStatisticBase:
    MODEL = None
    PUSH_KEY: str = ""  # 子类 PUSH_KEY 不能重复
    TIME_FIELD: str = ""
    REPORT_FIELD: str = ""

    HOUR_DELTA = 3600
    HOUR_SERIES_NUM = 24  # 小时-曲线的个数
    DAY_SERIES_NUM = 7  # 天-曲线的个数
    expire_time = 86400 * 31  # 需要统计的时间 31天

    NEED_REFRESH = True  # 定时同步到Mysql

    @classmethod
    def count_cache(cls, id_):
        # 新的统计指标不用自己实现缓存， 下面的 time_stamp_cache 同理
        cache = PushStatisticCache(cls.PUSH_KEY, id_)
        return cache

    @classmethod
    def time_stamp_cache(cls, id_, hour_ts=None):
        cache = PushStatisticCache(cls.PUSH_KEY, id_, hour_ts)
        return cache

    @classmethod
    def time_stamp_set_cache(cls, id_):
        cache = PushStatisticTimeStampSetCache(cls.PUSH_KEY, id_)
        return cache

    @classmethod
    def count(cls, id_):
        return cls.count_cache(id_).pfcount()

    @classmethod
    def get_hour_and_save_ts(cls, start_ts: int) -> Tuple[int, int]:
        save_point_ts = get_hour_ts(start_ts + cls.expire_time)
        hour_ts = get_hour_ts(current_timestamp())
        return hour_ts, save_point_ts

    @classmethod
    def add_to_cache(cls, id_, token):
        cache = cls.count_cache(id_)
        row = cls.MODEL.query.get(id_)
        if not row:
            return
        if cache.pfadd(token):
            start_ts = int(getattr(row, cls.TIME_FIELD).timestamp())
            hour_ts, save_point_ts = cls.get_hour_and_save_ts(start_ts)
            if hour_ts < save_point_ts:
                ts_cache = cls.time_stamp_cache(id_, hour_ts)
                ts_cache.pfadd(token)
                cls.time_stamp_set_cache(id_).sadd(str(hour_ts))

    @classmethod
    def refresh_report_field(cls, id_):
        row = cls.MODEL.query.get(id_)
        if not row:
            return
        cache = cls.count_cache(id_)
        if count := cache.pfcount():
            setattr(row, cls.REPORT_FIELD, count)
            db.session.commit()

    @classmethod
    def get_timestamp_range_cache(cls, id_, start_ts: int, end_ts: int) -> Dict[int, int]:
        hour_count_map = {}
        ts_set = cls.time_stamp_set_cache(id_).smembers()
        for ts in range(start_ts, end_ts + 1, cls.HOUR_DELTA):
            count = 0
            if str(ts) in ts_set:
                cache = cls.time_stamp_cache(id_, ts)
                count = cache.pfcount()
            hour_count_map[ts] = count
        return hour_count_map

    @classmethod
    def get_series_range_mapping(cls, push_id, start_at):
        if isinstance(start_at, datetime):
            start_at = int(start_at.timestamp())
        start_ts = get_hour_ts(start_at)
        end_ts = start_ts + 86400 * cls.DAY_SERIES_NUM
        return cls.get_timestamp_range_cache(push_id, start_ts, end_ts)

    @classmethod
    def get_hour_series_data(cls, series_range_mapping):
        _hour_series = []
        ts_keys = list(sorted(series_range_mapping.keys()))
        start_ts, end_ts = ts_keys[0], ts_keys[-1]
        _hour_point_ts = start_ts + cls.HOUR_DELTA * cls.HOUR_SERIES_NUM  # 推送后N小时的曲线
        _hour_sum_count = 0
        for cur_ts in range(start_ts, end_ts + 1, cls.HOUR_DELTA):
            _hour_sum_count += series_range_mapping[cur_ts]
            _hour_series.append({
                "key": cur_ts,
                "value": {cls.REPORT_FIELD: _hour_sum_count}
            })
        return _hour_series

    @classmethod
    def get_days_series_data(cls, series_range_mapping):
        ts_keys = list(sorted(series_range_mapping.keys()))
        start_ts, end_ts = ts_keys[0], ts_keys[-1]
        day_start_ts, day_end_ts = get_day_ts(start_ts), get_day_ts(end_ts)
        day_ts = 86400
        _day_sum_count = 0
        _day_series = []
        # +1 是为了统计最后一天的数据
        for day_cur_ts in range(day_start_ts, day_end_ts + 1, day_ts):
            # -1 不与下一天重复零点统计
            for hour_ts in range(day_cur_ts, day_cur_ts + day_ts - 1, cls.HOUR_DELTA):
                _day_sum_count += series_range_mapping.get(hour_ts, 0)
            _day_series.append({
                "key": day_cur_ts,
                "value": {cls.REPORT_FIELD: _day_sum_count}
            })
        return _day_series

    @classmethod
    def get_series_data(cls, row):
        _hour_series, _day_series = [], []
        series_range_mapping = cls.get_series_range_mapping(row.id, getattr(row, cls.TIME_FIELD))
        if series_range_mapping:
            _hour_series = cls.get_hour_series_data(series_range_mapping)
            _day_series = cls.get_days_series_data(series_range_mapping)

        _hour_field = f"_{cls.HOUR_SERIES_NUM}h_series"
        _day_field = f"_{cls.DAY_SERIES_NUM}d_series"
        return {
            _hour_field: _hour_series[:cls.HOUR_SERIES_NUM],
            _day_field: _day_series,
        }

    @classmethod
    def refresh_report_field_to_db(cls):
        pass


class EmailPushReadCountStatistic(PushStatisticBase):
    MODEL = EmailPush
    PUSH_KEY: str = "email_push_read"
    TIME_FIELD: str = "push_time"
    REPORT_FIELD: str = "user_read_count"
    DAY_SERIES_NUM = 30  # 天-曲线的个数

    @classmethod
    def refresh_report_field_to_db(cls):
        _now = now()
        push_ids = cls.MODEL.query.filter(
            cls.MODEL.status == cls.MODEL.Status.FINISHED,
            cls.MODEL.push_time <= _now,
            cls.MODEL.push_time > _now - timedelta(days=cls.DAY_SERIES_NUM),
        ).with_entities(cls.MODEL.id).all()
        push_ids = [i.id for i in push_ids]
        for id_ in push_ids:
            cls.refresh_report_field(id_)


class EmailPushClickCountStatistic(PushStatisticBase):
    MODEL = PushUrlConfig
    PUSH_KEY: str = "push_url_click"
    TIME_FIELD: str = "created_at"
    REPORT_FIELD: str = "click_count"
    DAY_SERIES_NUM = 30  # 天-曲线的个数

    NEED_REFRESH = False

    @classmethod
    def get_series_data(cls, row: EmailPush):
        ids = row.cached_url_ids
        start_ts = get_hour_ts(row.push_time.timestamp())
        end_ts = start_ts + 86400 * cls.DAY_SERIES_NUM
        series_range_mapping_list = [cls.get_series_range_mapping(id_, start_ts) for id_ in ids]
        series_range_mapping = {}
        for cur_ts in range(start_ts, end_ts + 1, cls.HOUR_DELTA):
            tmp_list = [i[cur_ts] for i in series_range_mapping_list]
            if tmp_list:
                series_range_mapping[cur_ts] = max(tmp_list)
            else:
                series_range_mapping[cur_ts] = 0
        _hour_series = cls.get_hour_series_data(series_range_mapping)
        _day_series = cls.get_days_series_data(series_range_mapping)

        _hour_field = f"_{cls.HOUR_SERIES_NUM}h_series"
        _day_field = f"_{cls.DAY_SERIES_NUM}d_series"
        return {
            _hour_field: _hour_series[:cls.HOUR_SERIES_NUM],
            _day_field: _day_series,
        }


class AppPushReadCountStatistic(PushStatisticBase):
    MODEL = AppPush
    PUSH_KEY: str = "app_push_read"
    TIME_FIELD: str = "push_time"
    REPORT_FIELD: str = "user_read_count"

    @classmethod
    def refresh_report_field_to_db(cls):
        _now = now()
        push_ids = cls.MODEL.query.filter(
            cls.MODEL.status == cls.MODEL.Status.FINISHED,
            cls.MODEL.push_time <= _now,
            cls.MODEL.push_time > _now - cls.MODEL.DUE_LIMIT
        ).with_entities(cls.MODEL.id).all()
        push_ids = [i.id for i in push_ids]
        for id_ in push_ids:
            cls.refresh_report_field(id_)


class PopupPageViewStatistic(PushStatisticBase):
    MODEL = PopupWindow
    PUSH_KEY: str = "popup_page_view"
    REPORT_FIELD: str = "page_view"
    TIME_FIELD: str = "started_at"

    @classmethod
    def refresh_report_field_to_db(cls):
        """更新弹窗打开数"""
        now_ = now()
        push_ids = cls.MODEL.query.filter(
            cls.MODEL.status == cls.MODEL.Status.VALID,
            cls.MODEL.started_at < now_,
            cls.MODEL.ended_at > now_ - timedelta(minutes=30)   # 更新下架前最后的数据
        ).with_entities(cls.MODEL.id).all()
        push_ids = [i.id for i in push_ids]
        for id_ in push_ids:
            cls.refresh_report_field(id_)


# 继承基础类，不需要将数据更新到数据库
class WebPopupPageViewStatistic(PushStatisticBase):
    MODEL = PopupWindow
    PUSH_KEY: str = "web_popup_page_view"
    REPORT_FIELD: str = "page_view"
    TIME_FIELD: str = "started_at"


class AndroidPopupPageViewStatistic(WebPopupPageViewStatistic):
    PUSH_KEY: str = "android_popup_page_view"


class IosPopupPageViewStatistic(WebPopupPageViewStatistic):
    PUSH_KEY: str = "ios_popup_page_view"


class PopupClickCountStatistic(PopupPageViewStatistic):
    MODEL = PopupWindow
    PUSH_KEY: str = "popup_click_count"
    REPORT_FIELD: str = "click_count"
    TIME_FIELD: str = "started_at"


class WebPopupClickCountStatistic(PushStatisticBase):
    MODEL = PopupWindow
    PUSH_KEY: str = "web_popup_click_count"
    REPORT_FIELD: str = "click_count"
    TIME_FIELD: str = "started_at"


class AndroidPopupClickCountStatistic(WebPopupClickCountStatistic):
    PUSH_KEY: str = "android_popup_click_count"


class IosPopupClickCountStatistic(WebPopupClickCountStatistic):
    PUSH_KEY: str = "ios_popup_click_count"


class StartPageViewStatistic(PushStatisticBase):
    MODEL = AppStartPage
    PUSH_KEY: str = "start_page_view"
    REPORT_FIELD: str = "page_view"
    TIME_FIELD: str = "started_at"

    @classmethod
    def refresh_report_field_to_db(cls):
        """更新启动页打开数"""
        now_ = now()
        push_ids = cls.MODEL.query.filter(
            cls.MODEL.status == cls.MODEL.Status.VALID,
            cls.MODEL.started_at < now_,
            cls.MODEL.ended_at > now_ - timedelta(minutes=30)   # 更新下架前最后的数据
        ).with_entities(cls.MODEL.id).all()
        push_ids = [i.id for i in push_ids]
        for id_ in push_ids:
            cls.refresh_report_field(id_)


class StartPageClickCountStatistic(StartPageViewStatistic):
    MODEL = AppStartPage
    PUSH_KEY: str = "start_page_click_count"
    REPORT_FIELD: str = "click_count"
    TIME_FIELD: str = "started_at"


class MessagePushReadStatistic(PushStatisticBase):
    MODEL = MessagePush
    PUSH_KEY: str = "message_push_read"
    REPORT_FIELD: str = "user_popup_read_count"
    TIME_FIELD: str = "push_time"
    DAY_SERIES_NUM = 30

    @classmethod
    def refresh_report_field_to_db(cls):
        _now = now()
        push_ids = cls.MODEL.query.filter(
            cls.MODEL.status == cls.MODEL.Status.FINISHED,
            cls.MODEL.push_time <= _now,
            cls.MODEL.push_time > _now - timedelta(days=10),
        ).with_entities(cls.MODEL.id).all()
        push_ids = [i.id for i in push_ids]
        for push_id in push_ids:
            cls.refresh_report_field(push_id)


class MessagePushClickStatistic(MessagePushReadStatistic):
    MODEL = MessagePush
    PUSH_KEY: str = "message_push_click"
    REPORT_FIELD: str = "user_click_count"
    TIME_FIELD: str = "push_time"
    DAY_SERIES_NUM = 30


def get_push_report_map(cls):
    ret = {}
    for sub_cls in cls.__subclasses__():
        ret[sub_cls.PUSH_KEY] = sub_cls
        ret.update(get_push_report_map(sub_cls))
    return ret


PUSH_REPORT_MAP = get_push_report_map(PushStatisticBase)