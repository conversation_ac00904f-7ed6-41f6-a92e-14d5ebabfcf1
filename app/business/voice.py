# -*- coding: utf-8 -*-

from enum import Enum

from app.common import CeleryQueues
from app.utils import celery_task, route_module_to_celery_queue
from app.utils.voice import AliyunVoice

route_module_to_celery_queue(__name__, CeleryQueues.SMS)


class VoiceCaller:

    class Template(Enum):
        # 管理员你好，${product}服务不可用，持续${time}分钟，请尽快处理。
        ServerHealthNotice = 'TTS_303655038'
        # 管理员你好，当前风控待审核数量较多，请尽快处理。
        RiskUserNotice = 'TTS_303580042'
        # 管理员你好，风控已关闭全站提现，请尽快处理。
        WithdrawalDisabledNotice = 'TTS_303670035'
        # 管理员你好，提现取消风控告警，已关闭全站提现，请及时处理。
        WithdrawalCancelNotice = 'TTS_307460100'
        # 管理员你好，全站充值熔断，已关闭全站充值，请及时处理。
        DepositDisabledNotice = 'TTS_307485086'
        # 管理员你好，${asset}累计充值告警，已达到限制阈值，已关闭该币种充值，风控用户数${count}，请尽快处理。
        AssetDepositDisabledNotice = 'TTS_303635036'
        # 尊敬的管理员你好，微诺公司后台管理系统中${asset}资产不平待审核记录大于${threshold}条，请及时调整处理。
        AuditBlockedNotice = 'TTS_304280026'
        # 管理员您好，管理系统中监测到全站资产负债不平，请及时处理。
        AssetLiabilityAllNotice = 'TTS_304835042'
        # 管理员您好, 提现待审核记录已达${count}条, 阈值${threshold}条，请及时处理。
        WithdrawalAuditRequiredCountNotice = 'TTS_307470071'
        # 管理员您好, 提现已审核记录已达${count}条, 阈值${threshold}条，请及时处理。
        WithdrawalAuditedCountNotice = 'TTS_307465068'
        # 管理员您好, ${asset}累计提现告警，已关闭${asset}的提现，风控用户数${count}，请及时处理。
        WithdrawalAccumulatedRCNotice = 'TTS_307570136'  # 暂无使用
        # 管理员您好, 用户维度单笔充值卡待审核，请及时处理
        UserAccumulatedAssetDepositProportion = 'TTS_313405067'
        # 管理员您好, 市场指数价格连续${minutes}分钟无更新，请及时处理
        MarketIndexNotUpdatedNotice = ''
        # 管理员您好, 服务商${name}报错，已触发兜底措施，请及时处理
        KYTServiceAbnormalNotice = 'TTS_313390113'  #
        # 管理员您好, 已触发用户维度充值风控，请及时处理
        AssetBalanceInDisabledNotice = 'TTS_313475206'

    @classmethod
    def call(cls, mobile_num: str, templ: Template, templ_args: dict = None):
        AliyunVoice.call(mobile_num, templ.value, templ_args)


@celery_task
def send_server_health_notice(mobile_num: str, business: str, period: int):
    templ_args = {
        'product': business,
        'time': period
    }
    VoiceCaller.call(mobile_num, VoiceCaller.Template.ServerHealthNotice, templ_args)


@celery_task
def send_risk_control_notice(mobile_num: str):
    VoiceCaller.call(mobile_num, VoiceCaller.Template.RiskUserNotice)


@celery_task
def send_withdrawal_disabled_notice(mobile_num: str):
    VoiceCaller.call(mobile_num, VoiceCaller.Template.WithdrawalDisabledNotice)


@celery_task
def send_withdrawal_cancel_notice(mobile_num: str):
    VoiceCaller.call(mobile_num, VoiceCaller.Template.WithdrawalCancelNotice)


@celery_task
def send_deposit_disabled_notice(mobile_num: str):
    VoiceCaller.call(mobile_num, VoiceCaller.Template.DepositDisabledNotice)


@celery_task
def send_asset_deposit_disabled_notice(mobile_num: str, asset: str, user_count: int):
    templ_args = {
        'asset': asset,
        'count': user_count,
    }
    VoiceCaller.call(mobile_num, VoiceCaller.Template.AssetDepositDisabledNotice, templ_args)


@celery_task
def send_audit_blocked_notice(mobile_num: str, asset: str, threshold: int):
    templ_args = {
        'asset': asset,
        'threshold': threshold,
    }
    VoiceCaller.call(mobile_num, VoiceCaller.Template.AuditBlockedNotice, templ_args)


@celery_task
def send_asset_liability_all(mobile_num: str):
    VoiceCaller.call(mobile_num, VoiceCaller.Template.AssetLiabilityAllNotice)


@celery_task
def send_withdrawal_audit_count_call(mobile_num: str, audited: bool, count: int, threshold: int):
    templ_args = {
        'count': count,
        'threshold': threshold,
    }
    if audited:
        VoiceCaller.call(mobile_num,
                         VoiceCaller.Template.WithdrawalAuditedCountNotice,
                         templ_args)
    else:
        VoiceCaller.call(mobile_num,
                         VoiceCaller.Template.WithdrawalAuditRequiredCountNotice,
                         templ_args)


@celery_task
def send_accumulated_withdrawal_monitor_call(mobile_num: str, asset: str, count: str):
    VoiceCaller.call(mobile_num,
                     VoiceCaller.Template.WithdrawalAccumulatedRCNotice,
                     dict(asset=asset, count=count))


@celery_task
def send_user_asset_balance_in_disabled_notice(mobile_num: str):
    VoiceCaller.call(mobile_num, VoiceCaller.Template.AssetBalanceInDisabledNotice)


@celery_task
def send_market_index_not_updated_notice(mobile_num: str, minutes: int):
    VoiceCaller.call(mobile_num,
                     VoiceCaller.Template.MarketIndexNotUpdatedNotice,
                     dict(minutes=minutes))


@celery_task
def send_kyt_service_abnormal_notice(mobile_num: str, name: str):
    templ_args = {
        'name': name,
    }
    VoiceCaller.call(mobile_num, VoiceCaller.Template.KYTServiceAbnormalNotice, templ_args)
