import datetime
import json
from collections import defaultdict
from decimal import Decimal
from typing import Set, <PERSON>, Union

from flask import current_app
from flask_babel import force_locale, gettext
from pyroaring import BitMap

from app.common.push import PushBroadcastLimitGroup, PriceStrategy24HoursPushLimit, PriceStrategyOneHourPushLimit, \
    AppPushBusiness, AppBroadcastBusiness
from app.models import AutoPushStrategy, ContentAppPushStrategyDetail, db, AutoPushStrategySendHistory, \
    ContentPushStrategyContent, PriceBroadcastAutoPushStrategyDetail, AssetsType, \
    BillboardRankRankKeyMapping, BillboardConfExcludeAssets, TriggerIntervalToKeyMapping, PriceTagAutoPushStrategyDetail
from app.utils import now, today_datetime, batch_iter, amount_to_str, current_timestamp
from .push import send_mobile_push_to_user_ids, get_url_users_mapping, send_price_broadcast_strategy_push
from .push_base import PushBusinessH<PERSON><PERSON>, BroadcastBusiness<PERSON>and<PERSON>, PushTaskIdManager
from .. import Language
from ..caches import AssetUSDPricesCache
from ..caches.kline import AssetRankCache, AssetPeriodPriceCache
from ..caches.prices import AssetRealTimeRateCache
from ..common import PrecisionEnum
from ..exceptions import InvalidArgument
from ..models import UserPreference as UserPreferenceModel
from ..utils.push import PushType, AppPagePath


class StrategyPushAbstract:

    def check_and_push(self):
        raise NotImplementedError


class PriceStrategyPushMixin:
    strategy: AutoPushStrategy
    strategy_detail: Union[PriceBroadcastAutoPushStrategyDetail, PriceTagAutoPushStrategyDetail]

    def check_history_send_limit(self):
        start = now() - datetime.timedelta(days=1)
        hour_now = now() - datetime.timedelta(hours=1)
        rows = AutoPushStrategySendHistory.query.filter(
            AutoPushStrategySendHistory.strategy_id == self.strategy.id,
            AutoPushStrategySendHistory.send_at >= start
        ).all()
        hour_push_times = daily_push_times = 0
        for row in rows:
            daily_push_times += 1
            if row.send_at > hour_now:
                hour_push_times += 1
        daily_remain_times = PriceStrategy24HoursPushLimit - daily_push_times if (
                PriceStrategy24HoursPushLimit > daily_push_times) else 0
        hour_remain_times = PriceStrategyOneHourPushLimit - hour_push_times if (
                PriceStrategyOneHourPushLimit > hour_push_times) else 0
        return min([daily_remain_times, hour_remain_times])

    def get_to_send_assets(self) -> List:
        sent_assets = self.get_24h_sent_assets()
        if self.strategy_detail.assets_type == AssetsType.CHOSEN:
            assets = json.loads(self.strategy_detail.assets)
        else:
            assets = self._get_billboard_assets()
        return [asset for asset in assets if asset not in sent_assets]

    def _get_billboard_assets(self):
        rank = self.strategy_detail.bill_board_rank or 0
        key_ = BillboardRankRankKeyMapping[self.strategy_detail.bill_board]
        cache = AssetRankCache(key_)
        assets = cache.read_assets()[:rank]
        exclude_assets = json.loads(self.strategy_detail.exclude_assets) if self.strategy_detail.exclude_assets else []
        exclude_assets.extend(BillboardConfExcludeAssets)
        return [asset for asset in assets if asset not in exclude_assets]

    @staticmethod
    def get_24h_sent_assets():
        start = now() - datetime.timedelta(days=1)
        recs = AutoPushStrategySendHistory.query.filter(
            AutoPushStrategySendHistory.send_at >= start,
            AutoPushStrategySendHistory.trigger_asset.isnot(None)
        ).with_entities(
            AutoPushStrategySendHistory.trigger_asset
        ).all()
        return {i.trigger_asset for i in recs}

    def filter_reach_trigger_threshold_assets(self, assets) -> List:
        trigger_price_interval = self.strategy_detail.trigger_price_interval
        interval_key = TriggerIntervalToKeyMapping[trigger_price_interval]
        res = []
        prices = AssetUSDPricesCache().read()
        for asset in assets:
            data = AssetPeriodPriceCache().get_asset_data(asset)
            if not data:
                continue
            one_day_change_rate = AssetRealTimeRateCache().get_asset_real_time_rate(asset)
            data["one_day"]["change_rate"] = one_day_change_rate
            change_rate_percent = Decimal(data[interval_key]['change_rate']) * 100
            if change_rate_percent > 0:
                trigger_rise_threshold = self.strategy_detail.trigger_rise_threshold
                if trigger_rise_threshold and trigger_rise_threshold <= change_rate_percent:
                    res.append((asset, 'RISE', change_rate_percent, prices[asset]))
            else:
                trigger_fall_threshold = self.strategy_detail.trigger_fall_threshold
                if trigger_fall_threshold and trigger_fall_threshold < abs(change_rate_percent):
                    res.append((asset, 'FALL', change_rate_percent, prices[asset]))
        return res


class PriceBroadcastAutoPushStrategyHandler(StrategyPushAbstract, PriceStrategyPushMixin):
    """广播触达策略-行情触发"""

    def __init__(self, strategy: AutoPushStrategy):
        self.strategy = strategy
        self.strategy_detail = PriceBroadcastAutoPushStrategyDetail.query.filter(
            PriceBroadcastAutoPushStrategyDetail.strategy_id == strategy.id
        ).first()

    def check_and_push(self):
        if not (can_push_times := self.strategy_can_push_times()):
            return
        if not (to_send_assets := self.get_to_send_assets()):
            return
        if not (asset_price_rate_lis := self.filter_reach_trigger_threshold_assets(to_send_assets)):
            return
        can_push_asset_lis = asset_price_rate_lis[:can_push_times]
        for asset, direction, change_rate_percent, price in can_push_asset_lis:
            self.push(asset, direction, change_rate_percent, price)
            self.set_pushed()

    def strategy_can_push_times(self) -> int:
        if not (g_can_push_times := self.group_and_global_can_push()):
            return 0
        if not (s_can_push_times := self.check_history_send_limit()):
            return 0
        return min(g_can_push_times, s_can_push_times)

    def group_and_global_can_push(self):
        business = self.get_broadcast_business()
        return BroadcastBusinessHandler(business).can_push_times()

    def get_broadcast_business(self):
        if self.strategy.push_group == PushBroadcastLimitGroup.NoticeMsg:
            business = AppBroadcastBusiness.NoticeGroupStrategyBroadcast
        elif self.strategy.push_group == PushBroadcastLimitGroup.CommonMsg:
            business = AppBroadcastBusiness.CommonGroupStrategyBroadcast
        else:
            raise InvalidArgument(message='策略分组只能是一般消息和提醒消息！')
        return business

    def push(self, asset, direction, change_rate_percent, price):
        price_str = amount_to_str(price, PrecisionEnum.PRICE_PLACES)
        change_rate_percent_str = f'{abs(change_rate_percent)}%'
        mobile_ttl = 10 * 60
        created_at = current_timestamp(to_int=True)
        interval = self.strategy_detail.trigger_price_interval.name
        send_history = self.set_strategy_pushed(asset)
        send_price_broadcast_strategy_push.delay(send_history.id, asset, interval, direction,
                                                 change_rate_percent_str, price_str, mobile_ttl, created_at)

    def set_pushed(self):
        self.set_group_and_global_pushed()

    def set_strategy_pushed(self, asset):
        rec = AutoPushStrategySendHistory(
            strategy_id=self.strategy.id,
            send_at=now(),
            trigger_asset=asset
        )
        db.session_add_and_commit(rec)
        return rec

    def set_group_and_global_pushed(self):
        business = self.get_broadcast_business()
        BroadcastBusinessHandler(business).set_pushed()


class PriceTagAutoPushStrategyHandler(StrategyPushAbstract, PriceStrategyPushMixin):
    """push触达策略-行情触发"""

    def __init__(self, strategy: AutoPushStrategy):
        self.strategy = strategy
        self.strategy_detail = PriceTagAutoPushStrategyDetail.query.filter(
            PriceTagAutoPushStrategyDetail.strategy_id == strategy.id
        ).first()

    def check_and_push(self):
        if not (can_push_times := self.strategy_can_push_times()):
            return
        if not (to_send_assets := self.get_to_send_assets()):
            return
        if not (asset_price_rate_lis := self.filter_reach_trigger_threshold_assets(to_send_assets)):
            return
        can_push_asset_lis = asset_price_rate_lis[:can_push_times]
        for asset, direction, change_rate_percent, price in can_push_asset_lis:
            send_user_ids = self.push(asset, direction, change_rate_percent, price)
            self.set_pushed(send_user_ids)

    def strategy_can_push_times(self) -> int:
        return self.check_history_send_limit()

    def push(self, asset, direction, change_rate_percent, price):
        user_ids = self.strategy_detail.subscribe_users
        limit_users = self.get_limit_users()
        user_ids -= limit_users
        if not user_ids:
            return
        user_lang_mapping = defaultdict(set)
        for u_ids in batch_iter(user_ids, 5000):
            lang_query = UserPreferenceModel.query.filter(
                UserPreferenceModel.key.in_(('language', 'app_language')),
                UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
                UserPreferenceModel.user_id.in_(u_ids)
            ).with_entities(
                UserPreferenceModel.user_id,
                UserPreferenceModel.key,
                UserPreferenceModel.value
            ).all()
            user_lang_dic = defaultdict(dict)
            for v in lang_query:
                user_lang_dic[v.user_id][v.key] = v.value
            for user_id, lang_dic in user_lang_dic.items():
                if 'app_language' in lang_dic:
                    user_lang_mapping[getattr(Language, lang_dic['app_language'])].add(user_id)
                else:
                    user_lang_mapping[getattr(Language, lang_dic['language'])].add(user_id)
        sent_user_ids = set()
        market = f'{asset}USDT'
        url = AppPagePath.SPOT_MARKET_DETAIL.value.format(market=market)
        push_history = self.set_strategy_pushed(asset, len(user_ids))
        push_task_manager = PushTaskIdManager.for_auto_push_strategy(self.strategy.id)
        for lang, lang_user_ids in user_lang_mapping.items():
            if len(lang_user_ids) == 0:
                continue
            if not push_task_manager.is_push_task_active():
                self.strategy.status = AutoPushStrategy.Status.SUSPENDED
                db.session.commit()
                break
            url_users_mapping = get_url_users_mapping(url, lang_user_ids)
            title, content = self.get_title_and_content(lang, asset, direction, change_rate_percent, price)
            for r_url, _u_ids in url_users_mapping.items():
                for _lang_user_ids in batch_iter(_u_ids, 2000):        
                    try:
                        result = send_mobile_push_to_user_ids(
                            [str(v) for v in _lang_user_ids],
                            content, title,
                            url=r_url,
                            extras=dict(business_push_id=f'auto_push_strategy_send_history:{push_history.id}',
                                        push_type=PushType.STRATEGY_PUSH.name),
                        )
                    except Exception as e:
                        current_app.logger.error(f"send app push error : {e!r}")
                        continue
                    if not result:
                        continue
                    avail_user_ids = {int(i) for i in result['data']['avail_user_ids']}
                    task_id = result['data']['task_id']
                    push_task_manager.add_task_id(task_id)
                    sent_user_ids.update(avail_user_ids)
        push_history.send_user_count = len(sent_user_ids)
        db.session.commit()
        return sent_user_ids

    def get_limit_users(self) -> Set:
        business = self.get_push_business()
        ret = PushBusinessHandler(business).get_limit_users()
        return ret

    def get_push_business(self):
        if self.strategy.push_group == PushBroadcastLimitGroup.CommonMsg:
            business = AppPushBusiness.CommonGroupStrategyPush
        elif self.strategy.push_group == PushBroadcastLimitGroup.NoticeMsg:
            business = AppPushBusiness.NoticeGroupStrategyPush
        else:
            raise InvalidArgument(message='消息类型只能是普通消息和提醒消息！')
        return business

    def get_title_and_content(self, lang, asset, direction, change_rate_percent, price):
        with force_locale(lang.value):
            interval = gettext(self.strategy_detail.trigger_price_interval.value)
            if direction == 'RISE':
                title = gettext('%(asset)s%(interval)s价格涨幅达到%(change_rate_percent)s',
                                asset=asset, interval=interval, change_rate_percent=change_rate_percent)
            elif direction == 'FALL':
                title = gettext('%(asset)s%(interval)s价格跌幅达到%(change_rate_percent)s',
                                asset=asset, interval=interval, change_rate_percent=change_rate_percent)
            else:
                raise InvalidArgument(message='未知的币种涨跌方向！')
            content = gettext('最新价格%(price)sUSDT，立即查看>>', price=price)
        return title, content

    def set_pushed(self, user_ids):
        self.set_group_and_global_pushed(user_ids)

    def set_strategy_pushed(self, asset, user_count):
        rec = AutoPushStrategySendHistory(
            strategy_id=self.strategy.id,
            send_at=now(),
            trigger_asset=asset,
            user_count=user_count,
        )
        db.session_add_and_commit(rec)
        return rec

    def set_group_and_global_pushed(self, user_ids):
        business = self.get_push_business()
        PushBusinessHandler(business).set_pushed(user_ids)


class ContentAppPushStrategyHandler(StrategyPushAbstract):
    """标签触发-内容push"""

    def __init__(self, strategy: AutoPushStrategy):
        self.strategy = strategy
        self.strategy_detail = ContentAppPushStrategyDetail.query.filter(
            ContentAppPushStrategyDetail.strategy_id == strategy.id
        ).first()

    def check_and_push(self):
        if not self.strategy_can_push():
            return
        push_user_ids = self.strategy_detail.subscribe_users
        if not push_user_ids:
            return
        valid_push_user_ids = self.get_valid_user_ids(push_user_ids)
        sent_user_ids = self.push(valid_push_user_ids)
        self.set_pushed(sent_user_ids)

    def strategy_can_push(self) -> bool:
        now_ = now()
        if self.strategy_detail.execute_duration == ContentAppPushStrategyDetail.ExecuteDuration.RANGE:
            if not self.strategy_detail.start_time <= now_ <= self.strategy_detail.end_time:
                return False
        now_stamp = int(now_.timestamp())
        now_delta_st = now_stamp % 86400
        today_ = today_datetime()
        if self.strategy_detail.send_at and self.strategy_detail.send_at >= today_:  # 今天已经发送过了，就不再发了
            return False
        # 当前时间在距离预定每日发送时间10分钟内，视为有效
        if not (self.strategy_detail.daily_plan_send_at <= now_delta_st <=
                self.strategy_detail.daily_plan_send_at + ContentAppPushStrategyDetail.daily_send_range):
            return False
        return True

    def get_valid_user_ids(self, push_user_ids: Set[int]) -> Set:
        sent_users = self.get_sent_users()
        limit_users = self.get_limit_users()
        return push_user_ids - sent_users - limit_users

    def get_sent_users(self) -> Set:
        query = AutoPushStrategySendHistory.query.filter(
            AutoPushStrategySendHistory.strategy_id == self.strategy.id
        )
        if self.strategy_detail.push_limit_interval == ContentAppPushStrategyDetail.PushLimitInterval.FOREVER:
            query = query.all()
        else:
            today_ = today_datetime()
            interval = ContentAppPushStrategyDetail.interval_delta_days_dic[
                self.strategy_detail.push_limit_interval] - 1
            start = today_ - datetime.timedelta(days=interval)
            query = query.filter(
                AutoPushStrategySendHistory.send_at >= start
            ).all()
        sent_users = set()
        for row in query:
            user_ids = set(row.get_user_ids())
            sent_users.update(user_ids)
        return sent_users

    def get_limit_users(self) -> Set:
        business = self.get_push_business()
        ret = PushBusinessHandler(business).get_limit_users()
        return ret

    def get_push_business(self):
        if self.strategy.push_group == PushBroadcastLimitGroup.CommonMsg:
            business = AppPushBusiness.CommonGroupStrategyPush
        elif self.strategy.push_group == PushBroadcastLimitGroup.NoticeMsg:
            business = AppPushBusiness.NoticeGroupStrategyPush
        else:
            raise InvalidArgument(message='消息类型只能是普通消息和提醒消息！')
        return business

    def push(self, user_ids):
        contents = {c.lang: c for c in self.strategy_detail.contents}
        user_lang_mapping = defaultdict(set)
        for u_ids in batch_iter(user_ids, 5000):
            lang_query = UserPreferenceModel.query.filter(
                UserPreferenceModel.key.in_(('language', 'app_language')),
                UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
                UserPreferenceModel.user_id.in_(u_ids)
            ).with_entities(
                UserPreferenceModel.user_id,
                UserPreferenceModel.key,
                UserPreferenceModel.value
            ).all()
            user_lang_dic = defaultdict(dict)
            for v in lang_query:
                user_lang_dic[v.user_id][v.key] = v.value
            for user_id, lang_dic in user_lang_dic.items():
                if 'app_language' in lang_dic:
                    user_lang_mapping[getattr(Language, lang_dic['app_language'])].add(user_id)
                else:
                    user_lang_mapping[getattr(Language, lang_dic['language'])].add(user_id)
        final_lang_mapping = {
            lang: set()
            for lang in ContentPushStrategyContent.AVAILABLE_LANGS if lang in contents
        }
        for lang, lang_user_ids in user_lang_mapping.items():
            if lang not in ContentPushStrategyContent.AVAILABLE_LANGS:
                continue
            else:
                if lang in contents:
                    final_lang_mapping[lang] |= lang_user_ids
        history = self.set_strategy_pushed(len(user_ids))
        sent_user_ids = set()
        push_task_manager = PushTaskIdManager.for_auto_push_strategy(self.strategy.id)
        for lang, lang_user_ids in final_lang_mapping.items():
            if not push_task_manager.is_push_task_active():
                self.strategy.status = AutoPushStrategy.Status.SUSPENDED
                db.session.commit()
                break
            if len(lang_user_ids) == 0:
                continue
            content = contents[lang]
            url = content.url or self.strategy_detail.url
            url_users_mapping = get_url_users_mapping(url, lang_user_ids)
            for r_url, _u_ids in url_users_mapping.items():
                for _lang_user_ids in batch_iter(_u_ids, 2000):
                    try:
                        result = send_mobile_push_to_user_ids(
                            [str(v) for v in _lang_user_ids],
                            content.content, content.title,
                            url=r_url,
                            extras=dict(business_push_id=f'auto_push_strategy_send_history:{history.id}',
                                        push_type=PushType.STRATEGY_PUSH.name),
                        )
                    except Exception as e:
                        current_app.logger.error(f"send app push error : {e!r}")
                        continue
                    if not result:
                        continue
                    avail_user_ids = {int(i) for i in result['data']['avail_user_ids']}
                    task_id = result['data']['task_id']
                    sent_user_ids.update(avail_user_ids)
                    push_task_manager.add_task_id(task_id)
        history.send_users = BitMap(sent_user_ids).serialize()
        history.send_user_count = len(sent_user_ids)
        db.session.commit()
        return sent_user_ids

    def set_pushed(self, user_ids: Set[int]):
        self.set_group_and_global_pushed(user_ids)

    def set_strategy_pushed(self, user_count):
        rec = AutoPushStrategySendHistory(
            strategy_id=self.strategy.id,
            send_at=now(),
            user_count=user_count,
        )
        db.session.add(rec)
        self.strategy_detail.send_at = now()
        db.session.commit()
        return rec

    def set_group_and_global_pushed(self, user_ids):
        business = self.get_push_business()
        PushBusinessHandler(business).set_pushed(user_ids)


class AutoPushStrategyDelegate:
    handler_mapping = {
        AutoPushStrategy.Type.PRICE_BROADCAST: PriceBroadcastAutoPushStrategyHandler,
        AutoPushStrategy.Type.PRICE_TAG_PUSH: PriceTagAutoPushStrategyHandler,
        AutoPushStrategy.Type.CONTENT_TAG_PUSH: ContentAppPushStrategyHandler,
    }

    @classmethod
    def run(cls):
        valid_strategies = cls.get_valid_strategies()
        for strategy in valid_strategies:
            if not (handler := cls.handler_mapping.get(strategy.type)):
                continue
            handler(strategy).check_and_push()

    @staticmethod
    def get_valid_strategies():
        recs = AutoPushStrategy.query.filter(
            AutoPushStrategy.status == AutoPushStrategy.Status.AUDITED
        ).all()
        return recs
