# -*- coding: utf-8 -*-
from enum import Enum
from .base import db, ModelBase
from ..common import TradeType, TradeBusinessType
from ..utils import datetime_to_str


class UserConfigTradeFee(ModelBase):

    class StatusType(Enum):
        PASS = 'pass'
        DELETE = 'delete'

    __table_args__ = (
        db.UniqueConstraint(
            'user_id', 'trade_type', 'business_type', 'market_name',
            name='user_id_trade_type_business_type_market_name_unique'),
    )
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    trade_type = db.Column(db.Enum(TradeType), nullable=False)
    business_type = db.Column(db.Enum(TradeBusinessType), nullable=False)
    market_name = db.Column(db.String(32), nullable=False, default='')

    fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    expired_time = db.Column(db.MYSQL_DATETIME_6)

    status = db.Column(db.Enum(StatusType), nullable=False)
    remark = db.Column(db.String(128), nullable=False, default='')

    @property
    def record_detail(self) -> str:
        market = self.market_name
        if self.market_name == 'ALL':
            market = '全部市场'
        biz = {TradeBusinessType.SPOT: '现货', TradeBusinessType.PERPETUAL: '合约'}.get(self.business_type)
        fee = f'{(self.fee * 100).normalize():{"f"}}%'
        expired_time = '永不过期'
        if self.expired_time:
            expired_time = datetime_to_str(self.expired_time, offset_minutes=60 * 8)
        return f'{market}{biz}，{self.trade_type.value}费率为{fee}，到期时间为{expired_time}'


class UserTradeFeeDiscount(ModelBase):

    __table_args__ = (
        db.UniqueConstraint('user_id', 'market_name', name='user_id_market_name_unique'),
    )

    class StatusType(Enum):
        PASS = 'pass'
        DELETE = 'delete'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    market_name = db.Column(db.String(32), nullable=False)  # ALL为所有市场
    discount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    expired_time = db.Column(db.MYSQL_DATETIME_6) # 空永不过期
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.PASS)
    remark = db.Column(db.String(128))

    @property
    def record_detail(self) -> str:
        market = self.market_name
        if self.market_name == 'ALL':
            market = '全部市场'
        discount = self.discount.normalize()
        expired_time = '永不过期'
        if self.expired_time:
            expired_time = datetime_to_str(self.expired_time, offset_minutes=60 * 8)
        return f'{market}，折扣为{discount}，到期时间为{expired_time}'
