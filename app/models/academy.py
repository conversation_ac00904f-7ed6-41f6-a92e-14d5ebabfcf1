import json
from enum import Enum
from flask_babel import gettext as _

from ..common import Language
from .base import ModelBase, db


class AcademyCategory(ModelBase):
    GLOSSARY_ID = 1
    GLOSSARY_SORT_ID = -1
    SID = -20  # 与前端约定的最新分类（逻辑上的二级目录）
    SEC_NAME = _('最新')

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    parent_id = db.Column(db.Integer, nullable=True, index=True, comment='父级目录 id')
    name = db.Column(db.String(128), nullable=False)
    remark = db.Column(db.String(256), nullable=False, default='')
    sort_id = db.Column(db.Integer, nullable=False)  # 专业术语 sort_id = -1
    enabled = db.Column(db.Boolean, nullable=False, default=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)

    @classmethod
    def get_term_category_id(cls) -> int:
        # 专业术语上线前初始化，且不可删除
        row = cls.query.with_entities(
            cls.id
        ).filter(
            cls.id == cls.GLOSSARY_ID,
            cls.status == cls.Status.VALID,
            cls.enabled.is_(True)
        ).first()
        return row.id


class AcademyCategoryContent(ModelBase):
    category_id = db.Column(db.Integer, nullable=False, index=True)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    name = db.Column(db.String(128), nullable=False)


class AcademyTag(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    name = db.Column(db.String(128), nullable=False)
    remark = db.Column(db.String(256), nullable=False)
    enabled = db.Column(db.Boolean, nullable=False, default=True)
    display = db.Column(db.Boolean, nullable=False, default=False, comment='搜索框是否展示', index=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)


class AcademyTagContent(ModelBase):
    tag_id = db.Column(db.Integer, nullable=False, index=True)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    name = db.Column(db.String(128), nullable=False)


class AcademyArticle(ModelBase):
    class Status(Enum):
        DRAFT = '草稿'
        CREATED = '待审核'
        AUDITED = '待发布'
        PUBLISHED = '已发布'
        REJECTED = '已拒绝'
        OFFLINE = '已下架'
        DELETED = '已删除'

    title = db.Column(db.String(512), nullable=False)
    published_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    seo_url_keyword = db.Column(db.String(1024), nullable=False, default="", comment='URL关键词')
    primary_category_id = db.Column(db.Integer, nullable=False, comment='1级目录')
    secondary_category_id = db.Column(db.Integer, nullable=True, comment='2级目录')
    tag_ids = db.Column(db.TEXT, nullable=False, default='', comment='标签 id')
    coins = db.Column(db.TEXT, nullable=False, default='', comment='关联币种')
    remark = db.Column(db.String(128), nullable=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.DRAFT)

    is_hot = db.Column(db.Boolean, nullable=False, default=False, comment='是否热门')
    is_front = db.Column(db.Boolean, nullable=False, default=False, comment='是否展示首页')   # 专业术语可设置
    sort_id = db.Column(db.Integer, nullable=False, index=True)
    read_count = db.Column(db.Integer, nullable=False, comment='阅读量', default=0)

    audited_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    auditor_remark = db.Column(db.String(128), nullable=False, default='')

    __table_args__ = (
        db.Index('primary_secondary_category_id', 'primary_category_id', 'secondary_category_id'),
    )

    def get_tag_ids(self):
        if not self.tag_ids:
            return []
        return json.loads(self.tag_ids)

    def get_coins(self):
        if not self.coins:
            return []
        return json.loads(self.coins)


class AcademyArticleContent(ModelBase):
    article_id = db.Column(db.Integer, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False, default='')
    abstract = db.Column(db.Text, nullable=False, default='')
    content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    read_time = db.Column(db.Integer, nullable=False, comment='阅读时长（分钟）', default=0)

    title_participles = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='', comment='标题的分词结果')
    content_participles = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='', comment='正文的分词结果')

    __table_args__ = (
        db.UniqueConstraint('article_id', 'lang', name='article_lang_uniq'),
    )

    def get_title_participles(self) -> list:
        if self.title_participles:
            return json.loads(self.title_participles)
        return []

    def get_content_participles(self) -> list:
        if self.content_participles:
            return json.loads(self.content_participles)
        return []

    @classmethod
    def get_or_create_by_article(cls, article_id, lang):
        lang = Language[lang] if isinstance(lang, str) else lang
        row = cls.query.filter(
            cls.article_id == article_id,
            cls.lang == lang
        ).first()
        if row:
            return row
        else:
            row = cls(article_id=article_id, lang=lang)
            db.session.add(row)
            return row
