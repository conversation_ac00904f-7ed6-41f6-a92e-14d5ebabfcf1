#!/usr/bin/env python3
import json
from decimal import Decimal
from typing import List
from enum import Enum

from sqlalchemy import func

from .base import db, ModelBase
from app.utils import quantize_amount, current_timestamp


class RiskUser(ModelBase):
    """
    被风控账户
    """

    class Permission(Enum):
        # 注：此枚举的顺序 会影响到 block_permissions列
        BALANCE_OUT_DISABLED = 'balance_out_disabled'           # 禁止提现更改为提现受限（可以提交提现单，需要人工审核，允许自行取消提现）
        TRADING_DISABLED = 'trading_disabled'                   # 禁止交易，所有交易类型
        MARGIN_LOAN_DISABLED = 'margin_loan_disabled'           # 禁止杠杆借币
        TRADING_LIMITED = 'trading_limited'                     # 交易受限，合约仅可平/减仓，杠杆仅限在有仓位的市场交易，禁止现货交易
        TRANSFER_OUT_DISABLED = 'transfer_out_disabled'         # 禁止杠杆划出、合约划出
        ASSET_BALANCE_IN_DISABLED = 'asset_balance_in_disabled' # 单币种充值受限。该币种卡充值
        # TODO：刷完数据后，再移除
        BALANCE_OUT_LIMITED = 'balance_out_limited'             # 提现受限（可以提交提现单，但是需要人工审核，允许自行取消提现）

    class Reason(Enum):
        ABNORMAL_PROFIT = 'abnormal_profit'                      # 异常盈利
        IMMEDIATELY_WITHDRAWAL = 'immediately_withdrawal'        # 即充即提
        NEW_USER_IMMEDIATELY_WITHDRAWAL = 'new_user_immediately_withdrawal'  # 新注册即提现
        WITHDRAWAL_NO_ON_CHAIN_DEPOSIT = 'withdrawal_no_on_chain_deposit'  # 提现无链上充值记录
        PERIOD_ABNORMAL_PROFIT = 'period_abnormal_profit'        # 定期异常盈利检查
        WITHDRAWAL_NO_DEPOSIT = 'withdrawal_no_deposit'          # 提现但没有充值记录(含红包)
        MARKET_VOLATILITY = 'market_volatility'                  # 币币市场波动
        PERPETUAL_MARKET_VOLATILITY = 'perpetual_market_volatility'  # 合约市场波动
        INVESTMENT_BALANCE_CHECK = 'investment_balance_check'    # 理财对账
        MARGIN_LIQUIDATION = 'margin_liquidation'                # 杠杆穿仓异常
        MARGIN_LOAN_FLAT_CHECK = 'margin_loan_flat_check'        # 杠杆对账
        PERPETUAL_BALANCE_CHECK = 'perpetual_balance_check'      # 合约对账
        PERPETUAL_LIQUIDATION = 'perpetual_liquidation'          # 合约穿仓异常
        RED_PACKET_CHECK = 'red_packet_check'                    # 红包对账
        P2P_BALANCE_CHECK = 'p2p_balance_check'                  # P2P对账
        VIABTC_TRANS_BEYOND_THRESHOLD = 'viabtc_trans_beyond_threshold'   # 矿池入账超过阈值
        SMS_MONITORING_AUTO = 'sms_monitoring_auto'                       # 短信监控-自动
        SMS_MONITORING_MANUAL = 'sms_monitoring_manual'                   # 短信监控-人工
        ORDER_DEPOSIT_BEYOND_THRESHOLD = 'order_deposit_beyond_threshold'   # 订单充值金额超过阈值  DONE: 仅记录展示保留
        USER_DEPOSIT_BEYOND_THRESHOLD = 'user_deposit_user_beyond_threshold'   # 用户充值金额超过阈值 DONE: 仅记录展示保留
        USER_ASSET_PENDING_ORDER_THRESHOLD = 'user_asset_pending_order_threshold'   # 单用户单个币种当前累计挂单监控超过阈值
        ACCUMULATED_ASSET_DEPOSIT = 'accumulated_asset_deposit'  # 币种累计充值监控（环比涨幅）
        ACCUMULATED_ASSET_DEPOSIT_PROPORTION = 'accumulated_asset_deposit_proportion'  # 币种累计充值监控（流通量占比）
        # 仅展示保留
        USER_ACCUMULATED_ASSET_DEPOSIT_PROPORTION = 'user_accumulated_asset_deposit_proportion'  # 用户维度累计充值风控
        ACCUMULATED_ASSET_WITHDRAWAL = 'accumulated_asset_withdrawal' # 币种累计提现监控
        P2P_TRANS_ABNORMAL = "p2p_unlock_abnormal"   # P2P解冻异常
        P2P_HIGH_RISK_USER = "p2p_high_risk_user"   # P2P高风险用户
        BUS_ONLINE_COIN_MARKET_VOLATILITY = 'bus_online_coin_market_volatility'  # 商务上币市场异常波动
        # DONE: 仅记录展示保留
        BUS_ONLINE_COIN_DEPOSIT_ASSET_VOLATILITY = 'bus_online_coin_deposit_asset_volatility'  # 商务上币充值风控（币种）
        # DONE: 仅记录展示保留
        BUS_ONLINE_COIN_DEPOSIT_USER_VOLATILITY = 'bus_online_coin_deposit_user_volatility'  # 商务上币充值风控（用户）
        WASH_DEAL = "wash_deal"   # 现货防对敲监控
        PLEDGE_LIQUIDATION_BEYOND_THRESHOLD = "pledge_liquidation_beyond_threshold"   # 借贷穿仓超过阈值
        PLEDGE_LOAN_FLAT_CHECK = 'pledge_loan_flat_check'   # 借贷对账不平
        ABNORMAL_ISSUANCE = "abnormal_issuance"   # 币种异常增发
        WITHDRAWAL_ADDRESS_BLACKLISTED = 'withdrawal_address_blacklisted'  # 提现地址在是黑名单地址中
        ASSET_BALANCE_IN_DISABLED = 'asset_balance_in_disabled'  # 单币种充值受限。该币种卡充值

    PRI_PERMS = (
        Permission.BALANCE_OUT_DISABLED,
        Permission.TRADING_DISABLED,
        Permission.MARGIN_LOAN_DISABLED,
        Permission.TRADING_LIMITED,
        Permission.TRANSFER_OUT_DISABLED,
    )  # 单维度权限
    SEC_PERMS = (Permission.ASSET_BALANCE_IN_DISABLED,)  # 二级维度权限，需联合 source 字段
    SEC_PERM_REASONS = (Reason.ASSET_BALANCE_IN_DISABLED, )

    class Status(Enum):
        AUDIT_REQUIRED = 'audit_required'
        AUDITED = 'audited'
        AUDIT_REJECTED = 'audit_rejected'

    # 该表是默认的封禁行为，实际以block_permissions字段为准。
    PermissionMap = {
        Reason.ABNORMAL_PROFIT: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.IMMEDIATELY_WITHDRAWAL: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.NEW_USER_IMMEDIATELY_WITHDRAWAL: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.WITHDRAWAL_NO_ON_CHAIN_DEPOSIT: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.PERIOD_ABNORMAL_PROFIT: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.WITHDRAWAL_NO_DEPOSIT: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.MARKET_VOLATILITY: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.PERPETUAL_MARKET_VOLATILITY: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.INVESTMENT_BALANCE_CHECK: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.MARGIN_LIQUIDATION: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.MARGIN_LOAN_FLAT_CHECK: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.PERPETUAL_BALANCE_CHECK: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.PERPETUAL_LIQUIDATION: (
            Permission.BALANCE_OUT_DISABLED,
        ),
        Reason.RED_PACKET_CHECK: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.VIABTC_TRANS_BEYOND_THRESHOLD: (Permission.BALANCE_OUT_DISABLED,),
        Reason.USER_ASSET_PENDING_ORDER_THRESHOLD: (),
        Reason.ACCUMULATED_ASSET_DEPOSIT: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.USER_ACCUMULATED_ASSET_DEPOSIT_PROPORTION: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.ACCUMULATED_ASSET_WITHDRAWAL: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.P2P_BALANCE_CHECK: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.P2P_TRANS_ABNORMAL: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.P2P_HIGH_RISK_USER: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.BUS_ONLINE_COIN_MARKET_VOLATILITY: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.WASH_DEAL: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.PLEDGE_LIQUIDATION_BEYOND_THRESHOLD: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.PLEDGE_LOAN_FLAT_CHECK: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.ABNORMAL_ISSUANCE: (Permission.BALANCE_OUT_DISABLED, ),
        Reason.WITHDRAWAL_ADDRESS_BLACKLISTED: (Permission.BALANCE_OUT_DISABLED, Permission.TRADING_LIMITED),
        Reason.ASSET_BALANCE_IN_DISABLED: (Permission.BALANCE_OUT_DISABLED, Permission.ASSET_BALANCE_IN_DISABLED),
    }

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    reason = db.Column(db.StringEnum(Reason), nullable=False)
    detail = db.Column(db.String(512))
    source = db.Column(db.String(64))
    remark = db.Column(db.String(512))
    risk_count = db.Column(db.Integer, comment='风控次数')
    block_permissions = db.Column(db.String(512))
    audited_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.AUDIT_REQUIRED)

    __table_args__ = (
        db.Index('user_id_reason_idx', 'user_id', 'reason'),
    )

    @property
    def permissions(self) -> List[Permission]:
        if not self.block_permissions:
            return []
        p_names = json.loads(self.block_permissions)
        return [self.Permission[p] for p in p_names]

    @permissions.setter
    def permissions(self, permissions: List[Permission]):
        order_p = sorted(permissions, key=list(self.Permission).index)
        self.block_permissions = json.dumps([i.name for i in order_p])

    @classmethod
    def test(cls, user_id: int, permission: Permission) -> bool:
        # 这里当前仅用作于 BALANCE_OUT_DISABLED，usersettings 没有对应的用户设置权限项
        # 暂不处理二级维度权限：SEC_PERMS
        if permission in cls.SEC_PERMS:
            raise RuntimeError('不支持二级维度权限')
        rows = RiskUser.query.filter(
            RiskUser.user_id == user_id,
            RiskUser.status.in_((RiskUser.Status.AUDIT_REQUIRED, RiskUser.Status.AUDIT_REJECTED))
        ).all()
        return any(permission in row.permissions for row in rows)

    @classmethod
    def batch_test(cls, user_ids: set[int], permission: Permission) -> set:
        # 这里当前仅用作于 BALANCE_OUT_DISABLED
        # 暂不处理二级维度权限：SEC_PERMS
        if permission in cls.SEC_PERMS:
            raise RuntimeError('不支持二级维度权限')
        rows = RiskUser.query.filter(
            RiskUser.user_id.in_(user_ids),
            RiskUser.status.in_((RiskUser.Status.AUDIT_REQUIRED, RiskUser.Status.AUDIT_REJECTED))
        ).all()
        risk_user_ids = set()
        for r in rows:
            if permission in r.permissions:
                risk_user_ids.add(r.user_id)
        return risk_user_ids

    @classmethod
    def get_reasons_by_permission(cls, permission: Permission) -> set[Reason]:
        reasons = {
            reason for reason, reason_ps in cls.PermissionMap.items()
            if permission in reason_ps
        }
        return reasons

    @classmethod
    def add(
            cls,
            user_id: int,
            reason: Reason,
            detail: str = None,
            source: str = None,
            remark: str = None,
            status: Status = None,
            commit: bool = False
    ):
        row = cls(
            user_id=user_id,
            reason=reason,
            detail=detail or '',
            source=source or '',
            remark=remark or '',
            risk_count=cls.get_create_user_risk_count(user_id)
        )
        if status:
            row.status = status
        if commit:
            db.session_add_and_commit(row)
        return row

    @classmethod
    def get_create_user_risk_count(cls, user_id):
        count = cls.query.with_entities(
            func.count()
        ).filter(
            cls.user_id == user_id
        ).scalar() or 0
        return count + 1


class _CheckBase(ModelBase):

    __abstract__ = True

    class Status(Enum):
        PASSED = 'passed'
        FAILED = 'failed'
        DELETED = 'deleted'

    start_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_time = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    status = db.Column(db.Enum(Status), nullable=False)
    detail = db.Column(db.String(512))
    remark = db.Column(db.String(512))


class UserCheckRequest(ModelBase):
    """
    用户风控检查请求
    """

    class Status(Enum):
        CREATED = 'created'
        COMPLETED = 'completed'
    
    class Business(Enum):
        ABNORMAL_PROFIT = 'abnormal_profit'
        WITHDRAWAL_NO_DEPOSIT = 'withdrawal_no_deposit'  # 目前已经停用
        WITHDRAWAL_NO_ON_CHAIN_DEPOSIT = 'withdrawal_no_on_chain_deposit'
        IMMEDIATELY_WITHDRAWAL = 'immediately_withdrawal'
        NEW_USER_IMMEDIATELY_WITHDRAWAL = 'new_user_immediately_withdrawal'
        WITHDRAWAL_ADDRESS_BLACKLISTED = 'withdrawal_address_blacklisted'  # 判断是黑名单地址则写入，同时状态是COMPLETED

    class CheckBusiness(Enum):
        Withdrawal = "提现"
        P2p = "p2p卖出"

    # 提现检查项
    __WITHDRAWAL_CHECK_BUSINESSES__ = (
        Business.ABNORMAL_PROFIT,
        Business.IMMEDIATELY_WITHDRAWAL,
        Business.NEW_USER_IMMEDIATELY_WITHDRAWAL,
        Business.WITHDRAWAL_NO_ON_CHAIN_DEPOSIT,
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    business = db.Column(db.StringEnum(Business), nullable=False)
    """
    context = {
        "bus_id": bus_id,
        "business": CheckBusiness.Withdrawal.name
    }
    """
    context = db.Column(db.String(512), nullable=True, default='')
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)


class P2pBalanceCheck(_CheckBase):
    """
    p2p 对账
    """
    asset = db.Column(db.String(32), nullable=False, index=True)
    release_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    receive_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    diff_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    diff_volume_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class PreMarketSettleCheck(_CheckBase):
    """ 预测市场 对账 """
    asset = db.Column(db.String(32), nullable=False, index=True)
    expect_issue_settle_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    expect_position_settle_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    expect_settle_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    real_settle_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    diff_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    diff_volume_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class RedPacketCheck(_CheckBase):
    """
    红包对账
    """

    asset = db.Column(db.String(32), nullable=False, index=True)
    last_remain_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    send_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    grab_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    return_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    remain_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    diff_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MarginLoanFlatCheck(_CheckBase):
    """
    杠杆借还对账
    """

    asset = db.Column(db.String(32), nullable=False, index=True)
    account_id = db.Column(db.Integer, nullable=False)
    last_unflat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    new_loan_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    new_flat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    unflat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    diff_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class InvestmentBalanceCheck(_CheckBase):
    """
    理财对账
    """

    asset = db.Column(db.String(32), nullable=False, index=True)
    last_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    in_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    out_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    current_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    diff_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class PerpetualBalanceCheck(_CheckBase):
    """
    永续合约对账
    """

    asset = db.Column(db.String(32), nullable=False, index=True)
    transfer_in_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    transfer_out_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    server_transfer_in_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    server_transfer_out_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    last_server_transfer_in_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    last_server_transfer_out_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    insurance_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    user_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    last_record_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    record_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    diff_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class PledgeLoanFlatCheck(_CheckBase):
    """
    质押借贷对账
    """

    last_unflat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    new_loan_interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 兼容保留
    new_loan_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    new_interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    new_flat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    unflat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    diff_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    asset = db.Column(db.String(32), nullable=False, index=True)


class WithdrawalWhitelistUser(ModelBase):

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False,
                        unique=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(128))

    user = db.relationship('User', foreign_keys=[user_id])

    def _row_to_dict_hook_(self, dict_: dict):
        user = self.user
        dict_.update(
            user_name=user.name,
            user_email=user.email,
            user_mobile=user.mobile
        )


class WithdrawalNoDelayUser(ModelBase):

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False,
                        unique=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(128))
    user = db.relationship('User', foreign_keys=[user_id])

    def _row_to_dict_hook_(self, dict_: dict):
        user = self.user
        dict_.update(
            user_name=user.name,
            user_email=user.email,
            user_mobile=user.mobile
        )


class ViaBTCRiskControlConfig(ModelBase):
    """矿池入账风控配置"""

    RISK_CONTROL_ASSET_STATIC_DAYS = 5  # 币种风控检查时取前5天的统计数据
    RISK_CONTROL_USERS_STATIC_DAYS = 5  # 将特征用户(风控日前十天入账不少于5天的用户)定义为异常用户时的取的有转账记录的天数
    RISK_CONTROL_USERS_THRESHOLD = 2  # 筛选异常用户时的入账阈值
    RISK_CONTROL_STATIC_LARGEST_DAYS = 10  # 筛选异常用户时的最大筛选天数
    RISK_CONTROL_USERTRANS_RATE = Decimal('0.01')  # 风控时确定非特征用户(风控日前十天入账少于5天的用户)是否异常时使用的指标，即当该用户当日入账币种占总入账比例大于此值时，定义为异常用户   # noqa: E501

    class Status(Enum):
        OPEN = "open"
        CLOSE = "close"

    asset = db.Column(db.String(32), nullable=False)
    threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    risk_control_least_usd = db.Column(db.MYSQL_DECIMAL_26_8,
                                       nullable=False, default=0)   # 触发该币种风控的最小入账值(usd)，超过此值后再超过threshold，才触发风控
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.OPEN)
    remark = db.Column(db.String(128), default='')
    temp_threshold = db.Column(db.MYSQL_DECIMAL_26_8)   # 临时设置的阈值
    temp_threshold_expire_at = db.Column(db.MYSQL_DATETIME_6)     # 临时阈值的失效时间


class SpecialAssetRiskControlConfig(ModelBase):
    """特殊币种风控配置"""
    # 已废弃

    class Status(Enum):
        OPEN = "open"
        CLOSE = "close"

    class Type(Enum):
        PRIVACY = 'privacy'  # 隐私币

    asset = db.Column(db.String(32), nullable=False, index=True)
    threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.OPEN)
    type = db.Column(db.StringEnum(Type), nullable=False, default=Type.PRIVACY)


class AssetAccumulatedDepositConfig(ModelBase):
    """币种累计充值阈值配置（环比涨幅）"""

    UNIT = Decimal('1000000')  # 市值单位：百万 USD

    rank_min = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    rank_max = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    deposit_threshold = db.Column(db.MYSQL_DECIMAL_26_8)
    user_deposit_threshold = db.Column(db.MYSQL_DECIMAL_26_8)
    limit_count_threshold = db.Column(db.MYSQL_DECIMAL_26_8)
    limit_amount_threshold = db.Column(db.MYSQL_DECIMAL_26_8)
    temp_limit_count_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    temp_limit_amount_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    temp_limit_expire_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    remark = db.Column(db.String(128), default='')


class SpecialAssetAccumulatedDepositConfig(ModelBase):
    """特殊币种累计充值阈值配置（环比涨幅）"""

    class Status(Enum):
        OPEN = "open"
        CLOSE = "close"

    asset = db.Column(db.String(32), nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.OPEN)
    deposit_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    user_deposit_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    limit_count_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    limit_amount_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    temp_limit_count_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    temp_limit_amount_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    temp_limit_expire_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    remark = db.Column(db.String(128), default='')


class AssetAccumulatedDepositConfigProportion(ModelBase):
    """币种累计充值阈值配置（流通量占比）"""

    UNIT = Decimal('1000000')  # 市值单位：百万 USD

    rank_min = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    rank_max = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 用户维度
    user_period = db.Column(db.Integer, comment='累计充值周期(H)')  # 保留，防止日后使用
    user_period_dp = db.Column(db.MYSQL_DECIMAL_26_8, comment='累计充值流通量占比')
    user_single_dp = db.Column(db.MYSQL_DECIMAL_26_8, comment='单笔充值流通量占比')  # 保留，防止日后使用

    # 币种维度
    asset_period = db.Column(db.Integer, comment='累计充值周期(H)')
    asset_period_dp = db.Column(db.MYSQL_DECIMAL_26_8, comment='累计充值流通量占比')
    user_deposit_threshold = db.Column(db.MYSQL_DECIMAL_26_8, comment='用户大额充值市值（USD）')

    remark = db.Column(db.String(128), default='')


class SpecialAssetAccumulatedDepositConfigProportion(ModelBase):
    """特殊币种累计充值阈值配置（流通量占比）"""

    class Status(Enum):
        OPEN = "open"
        CLOSE = "close"

    asset = db.Column(db.String(32), nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.OPEN)
    # 用户维度
    user_period = db.Column(db.Integer, nullable=False, comment='累计充值充值周期(H)')  # 保留，防止日后使用
    user_period_dp = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='累计充值流通量占比')
    user_single_dp = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='单笔充值流通量占比')  # 保留，防止日后使用

    # 币种维度
    asset_period = db.Column(db.Integer, nullable=False, comment='累计充值充值周期(H)')
    asset_period_dp = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='累计充值流通量占比')
    user_deposit_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='用户大额充值市值（USD）')

    remark = db.Column(db.String(128), default='')


class AssetAccumulatedWithdrawalConfig(ModelBase):

    """币种累计提现阈值配置"""
    IGNORE_COUNT_CONFIG = 1000000

    circulation_min = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 单位百万USD
    circulation_max = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 单位百万USD
    withdrawal_threshold = db.Column(db.MYSQL_DECIMAL_26_8)
    user_withdrawal_threshold = db.Column(db.MYSQL_DECIMAL_26_8)
    limit_amount_threshold = db.Column(db.MYSQL_DECIMAL_26_8)
    temp_limit_amount_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    # 两个关于笔数的配置暂时不需要，这里写个默认值，防止后续逻辑需要加上
    limit_count_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=IGNORE_COUNT_CONFIG)
    temp_limit_count_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True, default=IGNORE_COUNT_CONFIG)
    temp_limit_expire_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    remark = db.Column(db.String(128), default='')


class SpecialAssetAccumulatedWithdrawalConfig(ModelBase):
    """特殊币种累计提现阈值配置"""

    IGNORE_COUNT_CONFIG = 1000000

    class Status(Enum):
        OPEN = "open"
        CLOSE = "close"

    asset = db.Column(db.String(32), nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.OPEN)
    withdrawal_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    user_withdrawal_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=IGNORE_COUNT_CONFIG)
    limit_amount_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    temp_limit_amount_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    # 两个关于笔数的配置暂时不需要，这里写个默认值，防止后续会
    limit_count_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=IGNORE_COUNT_CONFIG)
    temp_limit_count_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True, default=IGNORE_COUNT_CONFIG)
    temp_limit_expire_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    remark = db.Column(db.String(128), default='')


class RiskEventLog(ModelBase):
    """
    风控事件记录
    """

    class Status(Enum):
        NONE = '无需审核'
        AUDIT_REQUIRED = '待审核'
        PASSED = '审核通过'
        REJECTED = '已拒绝'

    class Reason(Enum):
        # 风控用户事件，与风控用户reason关联
        MARKET_VOLATILITY = 'market_volatility'
        PERPETUAL_MARKET_VOLATILITY = "perpetual_market_volatility"
        MARGIN_LIQUIDATION = 'margin_liquidation'
        MARGIN_LOAN_FLAT_CHECK = 'margin_loan_flat_check'
        PERPETUAL_LIQUIDATION = 'perpetual_liquidation'
        PERPETUAL_BALANCE_CHECK = 'perpetual_balance_check'
        RED_PACKET_CHECK = 'red_packet_check'
        P2P_BALANCE_CHECK = 'p2p_balance_check'
        INVESTMENT_BALANCE_CHECK = 'investment_balance_check'
        VIABTC_TRANS_BEYOND_THRESHOLD = 'viabtc_trans_beyond_threshold'
        BUS_ONLINE_COIN_MARKET_VOLATILITY = 'bus_online_coin_market_volatility'  # 商务上币市场异常波动
        # DONE: 仅记录展示保留
        BUS_ONLINE_COIN_DEPOSIT_VOLATILITY = 'bus_online_coin_deposit_volatility'  # 商务上币充值风控（币种）
        # 系统告警事件
        NO_DEALS = 'no_deals'
        # 指数价格不更新
        INDEX_PRICE_NOT_UPDATED = 'index_price_not_updated'
        ABNORMAL_ISSUANCE = 'abnormal_issuance'
        WITHDRAWAL_FUSE = 'withdrawal_fuse'
        ACCUMULATED_ASSET_DEPOSIT = 'accumulated_asset_deposit'
        ACCUMULATED_ASSET_DEPOSIT_PROPORTION = 'accumulated_asset_deposit_proportion'
        ACCUMULATED_ASSET_WITHDRAWAL = 'accumulated_asset_withdrawal'
        WITHDRAWALS_DISABLED_BY_ASSET_LIABILITY = 'withdrawals_disabled_by_asset_liability'
        WITHDRAWALS_DISABLED_BY_COIN_ASSET_LIABILITY = 'withdrawals_disabled_by_coin_asset_liability'
        SIGNED_WITHDRAWALS_CANCEL = 'signed_withdrawals_cancel'
        DEPOSITS_FUSE = 'deposits_fuse'
        WASH_DEAL = 'wash_deal'   # 现货防对敲监控
        PLEDGE_LOAN_FLAT_CHECK = 'pledge_loan_flat_check'
        PLEDGE_LIQUIDATION_BEYOND_THRESHOLD = 'pledge_liquidation_beyond_threshold'

    UReason = RiskUser.Reason
    # 风控枚举映射
    UserToLogReasonMap = {
        UReason.MARKET_VOLATILITY: Reason.MARKET_VOLATILITY,
        UReason.PERPETUAL_MARKET_VOLATILITY: Reason.PERPETUAL_MARKET_VOLATILITY,
        UReason.INVESTMENT_BALANCE_CHECK: Reason.INVESTMENT_BALANCE_CHECK,
        UReason.MARGIN_LIQUIDATION: Reason.MARGIN_LIQUIDATION,
        UReason.MARGIN_LOAN_FLAT_CHECK: Reason.MARGIN_LOAN_FLAT_CHECK,
        UReason.PERPETUAL_BALANCE_CHECK: Reason.PERPETUAL_BALANCE_CHECK,
        UReason.PERPETUAL_LIQUIDATION: Reason.PERPETUAL_LIQUIDATION,
        UReason.RED_PACKET_CHECK: Reason.RED_PACKET_CHECK,
        UReason.P2P_BALANCE_CHECK: Reason.P2P_BALANCE_CHECK,
        UReason.VIABTC_TRANS_BEYOND_THRESHOLD: Reason.VIABTC_TRANS_BEYOND_THRESHOLD,
        UReason.ACCUMULATED_ASSET_DEPOSIT: Reason.ACCUMULATED_ASSET_DEPOSIT,
        UReason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION: Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION,
        UReason.ACCUMULATED_ASSET_WITHDRAWAL: Reason.ACCUMULATED_ASSET_WITHDRAWAL,
        UReason.BUS_ONLINE_COIN_MARKET_VOLATILITY: Reason.BUS_ONLINE_COIN_MARKET_VOLATILITY,
        UReason.BUS_ONLINE_COIN_DEPOSIT_ASSET_VOLATILITY: Reason.BUS_ONLINE_COIN_DEPOSIT_VOLATILITY,
    }

    ALLOWED_AUDIT_REASONS = [
        Reason.ACCUMULATED_ASSET_DEPOSIT,
        Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION,
    ]

    reason = db.Column(db.StringEnum(Reason), nullable=False)
    source = db.Column(db.String(64), index=True)
    remark = db.Column(db.String(512))
    start_time = db.Column(db.MYSQL_DATETIME_6)
    end_time = db.Column(db.MYSQL_DATETIME_6)
    resume_time = db.Column(db.MYSQL_DATETIME_6)
    extra = db.Column(db.MYSQL_MEDIUM_TEXT)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.NONE)
    operate_at = db.Column(db.MYSQL_DATETIME_6, comment='审核时间')

    @classmethod
    def trans_reason(cls, reason: RiskUser.Reason | Reason):
        if isinstance(reason, RiskUser.Reason):
            return cls.UserToLogReasonMap.get(reason)
        return reason

    @classmethod
    def check_rc_ignore(cls, asset: str, reason: Reason, ignore_seconds: int) -> bool:
        """币种维度风控防干扰"""
        if reason not in cls.ALLOWED_AUDIT_REASONS:
            return False
        row = cls.query.with_entities(
            cls.operate_at,
            cls.status,
        ).filter(
            cls.reason == reason,
            cls.source == asset,
        ).order_by(
            cls.id.desc()
        ).first()
        if not row or row.status is not cls.Status.PASSED:
            return False
        operate_at = row.operate_at
        if not operate_at:
            return False
        delta = current_timestamp(to_int=True) - int(operate_at.timestamp())
        return delta < ignore_seconds


class BusOnlineCoinRiskConfigByMarket(ModelBase):
    """商务上币风控市场维度配置"""

    market = db.Column(db.String(64), nullable=False, unique=True)  # 市场名
    period_by_min = db.Column(db.Integer, nullable=False)  # 检查周期(min)
    decrease_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 跌幅阈值(eg: 0.8 = 80%)
    sell_volume_usd_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 卖单成交额阈值(USD)

    def get_period_by_hour(self):
        return quantize_amount(Decimal(self.period_by_min) / 60, 2)


class BusOnlineCoinRiskConfigByAsset(ModelBase):
    """商务上币风控币种维度配置"""

    # TODO: 已废弃

    asset = db.Column(db.String(32), nullable=False, unique=True)  # 市场名
    asset_period_by_min = db.Column(db.Integer, nullable=False)  # 币种维度充值检查周期(min)
    asset_deposit_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 币种维度总充值阈值(币本位)
    asset_deposit_each_user_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 币种维度下单个用户大额充值阈值(币本位)
    user_period_by_min = db.Column(db.Integer, nullable=False)  # 用户维度充值检查周期(min)
    user_deposit_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 用户维度充值阈值(币本位)

    def get_asset_period_by_hour(self):
        return quantize_amount(Decimal(self.asset_period_by_min) / 60, 2)

    def get_user_period_by_hour(self):
        return quantize_amount(Decimal(self.user_period_by_min) / 60, 2)


class CountrySmsRiskConfig(ModelBase):
    """国家短信区号风控"""

    class Status(Enum):
        OPEN = "正常"
        CLOSE = "关闭"

    mobile_country_code = db.Column(db.Integer, nullable=False, unique=True, comment="区号")
    admin_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), comment="操作人")
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.OPEN, comment="状态")
    days = db.Column(db.Integer, nullable=False, comment="注册天数")


class AssetAbnormalIssuanceConfig(ModelBase):
    """币种异常增发配置"""

    rank_min = db.Column(db.Integer, nullable=False)
    rank_max = db.Column(db.Integer, nullable=False)
    issuance_threshold = db.Column(db.MYSQL_DECIMAL_26_8, comment='异常增发占比阈值')
    issuance_usd_threshold = db.Column(db.MYSQL_DECIMAL_26_8, comment='异常增发市值阈值')
    remark = db.Column(db.String(128), default='')


class RiskControlMobileNoticeConfig(ModelBase):

    class MobileNoticeEventType(Enum):
        SERVER_HEALTH = "全站撮合（server）未成交告警"
        RISK_USER_AUDIT_REQUIRED = "风控用户待审核告警"
        SITE_WITHDRAWAL_FUSE = "全站提现熔断告警"
        ASSET_ACCUMULATED_WITHDRAWAL = "币种累计提现告警"
        # 已经签名过的提现被取消风控
        WITHDRAWAL_CANCELLED = "提现取消风控"
        WITHDRAWAL_AUDIT_REQUIRED = "WEB提现卡待审核"
        WITHDRAWAL_AUDITED = "WEB提现卡已审核"
        SITE_DEPOSIT_FUSE = "全站充值熔断告警"
        ASSET_ACCUMULATED_DEPOSIT_INCREASE = "币种累计充值告警（环比涨幅）"
        ASSET_ACCUMULATED_DEPOSIT_CIRCULATION = "币种累计充值告警（流通量占比）"
        USER_ACCUMULATED_ASSET_DEPOSIT_PROPORTION = "用户维度单笔充值风控"
        ASSET_BALANCE_IN_DISABLED = "用户维度单笔充值风控（新）"
        ASSET_LIABILITY = "全站资产负债不平"
        ASSET_LIABILITY_WITHDRAWAL_AUDIT_REQUIRED = "币种资产负债不平提现卡待审核"
        MARKET_INDEX_MONITOR = "市场指数价格不更新"
        KYT_SERVICE_ABNORMAL_NOTICE = "KYT 服务异常告警"

    class StatusType(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    DESCRIPTION_MAPPING = {
        MobileNoticeEventType.SERVER_HEALTH: "全站撮合3分钟未成交则告警，后续每分钟告警一次",
        MobileNoticeEventType.RISK_USER_AUDIT_REQUIRED:	"定时检查用户风控列表里待审核的数量，这次比上次多100条则告警",
        MobileNoticeEventType.SITE_WITHDRAWAL_FUSE:	"最近24H提现笔数、提现市值/最近7日均值≥阈值时，关闭全站提现，并告警",
        MobileNoticeEventType.ASSET_ACCUMULATED_WITHDRAWAL:	"最近24H充提现数量/最近7日均值≥对应币种阈值时，关闭该币种提现，并告警",
        MobileNoticeEventType.WITHDRAWAL_CANCELLED:	"最近24H全站已签名但提现取消的提现市值≥阈值时，关闭全站提现，并告警",
        MobileNoticeEventType.WITHDRAWAL_AUDIT_REQUIRED: "每5分钟检测WEB这边提现记录，如果状态为「待审核」的记录≥阈值，则告警",
        MobileNoticeEventType.WITHDRAWAL_AUDITED: "每5分钟检测WEB这边提现记录，如果状态为「已审核」的记录≥阈值，则告警",
        MobileNoticeEventType.SITE_DEPOSIT_FUSE: "最近24H充值市值/最近7日均值≥阈值时，关闭全站充值，并告警",
        MobileNoticeEventType.ASSET_ACCUMULATED_DEPOSIT_INCREASE: "最近24H充值笔数、充值数量/最近N日均值≥对应币种阈值时，"
                                                                  "关闭该币种充值，并告警",
        MobileNoticeEventType.ASSET_ACCUMULATED_DEPOSIT_CIRCULATION:
            "最近XXH内累计充值数量≥对应币种币种总流通量（取币种资料数据）*比例阈值，关闭该币种充值，并告警",
        MobileNoticeEventType.USER_ACCUMULATED_ASSET_DEPOSIT_PROPORTION: '用户维度单笔充值风控',
        MobileNoticeEventType.ASSET_BALANCE_IN_DISABLED: '用户维度单笔充值风控（新）',
            MobileNoticeEventType.ASSET_LIABILITY:
            "全站资产负债不平，平台权益绝对值≥阈值且权益为负；或平台权益绝对值≥阈值且权益为正但连续三次不平，触发后关闭全站提现并告警",
        MobileNoticeEventType.ASSET_LIABILITY_WITHDRAWAL_AUDIT_REQUIRED:
            "某个币种因为资产负债不平风控，导致卡待审核的提现记录＞阈值，则进行电话告警",
        MobileNoticeEventType.MARKET_INDEX_MONITOR: "市场指数价格连续超过分钟不更新"
    }

    user_id = db.Column(db.Integer, nullable=False, index=True)
    event_type = db.Column(db.StringEnum(MobileNoticeEventType), nullable=False, index=True)
    status = db.Column(db.StringEnum(StatusType), nullable=False, index=True, default=StatusType.VALID)
    mobile = db.Column(db.String(32), nullable=False, index=True)
    remark = db.Column(db.String(128))

    @classmethod
    def get_description(cls, _event_type: MobileNoticeEventType):
        return cls.DESCRIPTION_MAPPING.get(_event_type)

    @classmethod
    def get_mobiles(cls, event_type: MobileNoticeEventType):
        q = cls.query.filter(
            cls.event_type == event_type,
            cls.status == cls.StatusType.VALID
        ).order_by(
            cls.id.desc()
        ).with_entities(
            cls.mobile.distinct().label('mobile'),
        )
        return [v.mobile for v in q]
