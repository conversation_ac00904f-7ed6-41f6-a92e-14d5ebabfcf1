# -*- coding: utf-8 -*-
from decimal import Decimal
from enum import Enum
from functools import cached_property

from flask_babel import gettext
from pyroaring import BitMap

from app.models.base import db, ModelBase
from app.common.constants import BusinessParty


class EquityType(Enum):
    CASHBACK = gettext('手续费返现')
    AIRDROP = gettext('空投')


class EquitySetting(ModelBase):
    """权益中心全局配置"""
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, index=True, default=Status.VALID)


class EquityBaseInfo(ModelBase):
    """ 权益基本信息｜权益模版 """

    class Status(Enum):
        OPEN = "启用"
        CLOSE = "禁用"

    type = db.Column(db.StringEnum(EquityType), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.OPEN)
    creator = db.Column(db.Integer, nullable=False)
    remark = db.Column(db.String(512), nullable=False, default="")
    # 通用字段
    cost_asset = db.Column(db.String(32), nullable=False)  # 成本币种
    cost_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 成本币种数目
    #
    extra_data = db.Column(db.MYSQL_JSON, nullable=False)  # 不同权益特有的数据
    """
    CASHBACK: cashback_scope, cashback_asset, effective_days
        cost_asset=USDT cashback_asset=CET
    
    AIRDROP: value_asset
        cost_asset=CET value_asset=USDT
    """

    @property
    def is_open(self) -> bool:
        return self.status == self.Status.OPEN


class UserEquity(ModelBase):
    """ 用户权益基础表 """

    __table_args__ = (
        db.UniqueConstraint("business_id", "business_type", "user_id", name="business_id_type_user_id_unique"),
    )

    class BusinessType(Enum):
        # 业务类型|发放类型
        MISSION = gettext('任务发放')
        # AIRDROP_ACTIVITY = gettext('空投活动')
        # DEPOSIT_BONUS_ACTIVITY = gettext('充值福利活动发放')
        PLATFORM_SEND = gettext('平台发放')

    class Status(Enum):
        # 只存大状态
        CREATED = "初始状态"  # 不在前端展示
        USING = "使用中"
        # 以下都是结束态
        EXPIRED = "已过期"
        FINISHED = "已使用"
        FAILED = "失败"  # 风控等情况

    user_id = db.Column(db.Integer, nullable=False, index=True)
    equity_id = db.Column(db.Integer, nullable=False, index=True)
    type = db.Column(db.StringEnum(EquityType), nullable=False)
    business_id = db.Column(db.Integer, nullable=False)  # 和用户相关，如：用户任务ID
    business_type = db.Column(db.StringEnum(BusinessType), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False)
    finished_at = db.Column(db.MYSQL_DATETIME_6)  # 权益的结束时间


class UserAirdropEquity(ModelBase):
    """ 用户空投权益 """

    class Status(Enum):
        CREATED = "处理中"  # 不在前端展示
        FINISHED = "已到账"
        FAILED = "失败"  # 风控等情况，未到账

    user_id = db.Column(db.Integer, nullable=False, index=True)
    user_equity_id = db.Column(db.Integer, nullable=False, unique=True)
    status = db.Column(db.StringEnum(Status), nullable=False)
    #
    airdrop_asset = db.Column(db.String(32), nullable=False, index=True)  # 空投币种｜成本币种
    airdrop_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 空投数目｜成本币种数目
    value_asset = db.Column(db.String(32), nullable=False)  # 价值币种
    value_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 价值币种数目

    def __repr__(self):
        return f'<{type(self).__name__} id={self.id} user_eq_id={self.user_equity_id}>'


class UserAirdropEquityHistory(ModelBase):
    """ 用户空投权益-明细，对应资金流水 """

    class Status(Enum):
        CREATED = "created"
        DEDUCTED = "deducted"
        FAILED = "failed"
        FINISHED = "finished"

    user_id = db.Column(db.Integer, nullable=False, index=True)
    user_equity_id = db.Column(db.Integer, nullable=False, unique=True)  # 1对1
    airdrop_asset = db.Column(db.String(32), nullable=False)  # 本次空投币种
    airdrop_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 本次空投数目
    value_asset = db.Column(db.String(32), nullable=False)  # 本次空投的价值币种
    value_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 本次空投的价值币种数目
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)  # 资产-划转状态
    deducted_at = db.Column(db.MYSQL_DATETIME_6)  # 资产-扣减系统帐号资产时间
    finished_at = db.Column(db.MYSQL_DATETIME_6)  # 资产-划转完成时间
    asset_rates = db.Column(db.MYSQL_JSON, nullable=False)  # 相关币种的USD汇率


class UserCashbackEquity(ModelBase):
    """ 用户返现权益 """

    class CashbackScope(Enum):
        # 返现范围
        SPOT = "币币交易"  # 兑换、现货、杠杆及策略交易
        PERPETUAL = "合约交易"  # 正反向合约和跟单交易
        ALL = "币币/合约交易"

    SPOT_SCOPES = [CashbackScope.SPOT, CashbackScope.ALL]
    PERPETUAL_SCOPES = [CashbackScope.PERPETUAL, CashbackScope.ALL]

    class Status(Enum):
        USING = "使用中"
        FINISHED = "已使用"  # 终止态
        EXPIRED = "已过期"  # 终止态
        FAILED = "失败"  # 风控等情况，不进行结算和返现

    user_id = db.Column(db.Integer, nullable=False, index=True)
    user_equity_id = db.Column(db.Integer, nullable=False, unique=True)
    start_time = db.Column(db.MYSQL_DATETIME_6, index=True)  # 权益开始时间
    end_time = db.Column(db.MYSQL_DATETIME_6, index=True)  # 权益过期时间
    status = db.Column(db.StringEnum(Status), nullable=False, index=True)
    # 复制EquityBaseInfo.extra
    cost_asset = db.Column(db.String(32), nullable=False)  # 成本币种
    cost_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 成本币种数目
    cashback_scope = db.Column(db.StringEnum(CashbackScope), nullable=False)  # 返现范围
    cashback_asset = db.Column(db.String(32), nullable=False)  # 返现币种
    # cost_asset_price * cost_amount >= cashback_asset_price * cashback_amount
    #
    cashback_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 累积已返现币种数目
    used_cost_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 累积已返现币种数目的成本|已使用的成本
    last_cashback_at = db.Column(db.MYSQL_DATETIME_6)  # 最近一次返现时间

    def __repr__(self):
        return f'<{type(self).__name__} id={self.id} user_eq_id={self.user_equity_id}>'

    @property
    def remain_cost_amount(self) -> Decimal:
        """ 剩余返现成本 """
        return max(self.cost_amount - self.used_cost_amount, Decimal(0))


class UserCashbackEquityHistory(ModelBase):
    """ 用户返现权益-明细 """

    __table_args__ = (
        db.UniqueConstraint("user_equity_id", "settle_his_id", name="user_equity_id_settle_his_id_unique"),
    )

    user_id = db.Column(db.Integer, nullable=False, index=True)
    user_equity_id = db.Column(db.Integer, nullable=False)
    settle_his_id = db.Column(db.Integer, nullable=False, index=True)  # 结算历史ID，一条settle_his_id 可以关联 多条明细记录
    cost_asset = db.Column(db.String(32), nullable=False)  # 成本币种
    cashback_asset = db.Column(db.String(32), nullable=False)  # 返现币种
    delta_cost_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 本次返现的成本币种数目
    delta_cashback_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 本次返现数目
    total_used_cost_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 累积返现的成本币种数目
    total_cashback_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 累积返现数目
    settle_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 本次权益结算的时间，不一定是结算记录的时间或者交易的时间


class UserCashbackEquityTransferHistory(ModelBase):
    """ 用户返现权益-划转记录（同一个用户权益的多笔明细 合并为 一笔划转） """

    class Status(Enum):
        CREATED = "待划转"
        DEDUCTED = "已扣减"
        FAILED = "失败"
        FINISHED = "已完成"

    user_id = db.Column(db.Integer, nullable=False, index=True)
    user_equity_id = db.Column(db.Integer, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)  # 划转币种
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 划转数目
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)  # 返现资产-划转状态
    deducted_at = db.Column(db.MYSQL_DATETIME_6)  # 返现资产-扣减系统帐号资产时间
    finished_at = db.Column(db.MYSQL_DATETIME_6)  # 返现资产-划转完成时间
    his_ids = db.Column(db.MYSQL_JSON, nullable=False)  # 关联的明细记录id


class UserCashbackSettlementHistory(ModelBase):
    """ 返现权益的用户-交易手续费-结算历史 """

    class Status(Enum):
        SETTLING = "结算中"
        FINISHED = "已结算"
        EXPIRED = "已过期"

    class TradeType(Enum):
        SPOT = "币币"  # 包括兑换
        PERPETUAL = "合约"

    id = db.Column(db.BigInteger, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)  # 结算用户，主账号
    trade_type = db.Column(db.StringEnum(TradeType), nullable=False)
    # 结算时间，在权益时间范围内则可结算。正常情况是交易时间，异常情况：脚本手动指定在返现权益时间范围内的某个时间点
    settle_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)

    cost_asset = db.Column(db.String(32), nullable=False)  # 成本币种
    total_cost_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 本次手续费 换算为 成本币种 的数目
    used_cost_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 已使用的成本币种数目
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.SETTLING, index=True)
    asset_rates = db.Column(db.MYSQL_JSON, nullable=False)  # 相关币种的USD汇率
    fee_his_ids = db.Column(db.MYSQL_JSON, nullable=False)  # 关联的统计记录id

    @property
    def remain_cost_amount(self) -> Decimal:
        """ 剩余可结算成本 """
        return max(self.total_cost_amount - self.used_cost_amount, Decimal(0))


class UserCashbackTradeFeeHistory(ModelBase):
    """ 返现权益的用户-交易手续费统计（现货、合约、兑换、策略、跟单等）
    按成交记录或兑换订单的维度去统计
    策略和跟单的手续费 存到对应的sub_user_id上
    """

    __table_args__ = (
        db.UniqueConstraint("trade_user_id", "trade_business_id", "trade_type", name="user_id_trade_type_unique"),
    )

    INVALID_SETTLE_HIS_ID = -1

    class TradeType(Enum):
        EXCHANGE = "兑换"
        SPOT = "币币"
        PERPETUAL = "合约"

    id = db.Column(db.BigInteger, primary_key=True)
    trade_user_id = db.Column(db.Integer, nullable=False)  # 交易用户，可能是子帐号
    trade_business_id = db.Column(db.BigInteger, nullable=False, index=True)  # order_deal_id, AssetExchangeOrder.id
    trade_type = db.Column(db.StringEnum(TradeType), nullable=False)
    trade_time = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)  # 交易时间，根据交易时间来判断是否可以结算
    # fees: [[market, fee_asset, fee_amount]]
    # 单个用户的一笔兑换订单可能有2条手续费（跨市场时）；单个用户的一笔成交记录也可能有2条手续费（自成交时）
    fees = db.Column(db.MYSQL_JSON, nullable=False)
    # 结算记录ID，标记是否生成了结算记录。
    # 一条Settle记录定时汇总多条FeeHistory。如果是无效的手续费统计（用户权益已结束等）则更新为-1
    settle_his_id = db.Column(db.Integer, nullable=True, index=True)


class EquitySendApply(ModelBase):
    """权益发放申请"""

    class SendType(Enum):
        DELIVERY = '直接发放'

    class Status(Enum):
        CREATED = "待审核"
        PASSED = "待发放"
        SENDING = "发放中"
        FINISHED = "已发放"
        DISABLED = "已禁用"
        REJECTED = "审核未通过"

    send_type = db.Column(db.StringEnum(SendType), nullable=False)  # 发放类型
    business_party = db.Column(db.StringEnum(BusinessParty), nullable=False)  # 业务方
    title = db.Column(db.String(128), nullable=False)  # 标题
    equity_id = db.Column(db.Integer, nullable=False, index=True)  # 发放的权益id
    equity_type = db.Column(db.StringEnum(EquityType), nullable=False)  # 发放的权益类型，冗余
    total_send_count = db.Column(db.Integer, nullable=False)  # 总发放数量
    success_send_count = db.Column(db.Integer, nullable=False, default=0)  # 成功发放数量，len(send_user_ids)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    send_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 发放时间
    send_finished_at = db.Column(db.MYSQL_DATETIME_6)  # 发放完成时间
    creator = db.Column(db.Integer, index=True)  # 创建人
    remark = db.Column(db.String(256), nullable=False)
    #
    group_ids = db.Column(db.MYSQL_JSON, nullable=False)  # 用户圈群, [1, 2, 3]
    group_user_count = db.Column(db.Integer, nullable=False, default=0)  # group_ids里的用户数，len(group_user_ids)
    group_user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')  # group_ids里 user_id 的 bitmap
    send_user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')  # 实际发放权益的user_id 的 bitmap

    def get_groups(self) -> list[int]:
        # for parse
        return self.group_ids

    @cached_property
    def cached_group_user_ids(self) -> set:
        """客群user_ids"""
        if not self.group_user_ids:
            return set()
        bm = BitMap.deserialize(self.group_user_ids)
        if not bm:
            return set()
        return set(bm)

    def set_group_user_ids(self, user_ids: set[int]):
        """设置客群 user_ids"""
        self.group_user_count = len(user_ids)
        self.group_user_ids = BitMap(user_ids).serialize() # noqa

    def get_send_user_ids(self) -> set:
        """获取已发放user_ids"""
        if not self.send_user_ids:
            return set()
        bm = BitMap.deserialize(self.send_user_ids)
        if not bm:
            return set()
        return set(bm)

    def update_send_user_ids(self, user_ids: set[int]):
        """更新已发放 user_ids"""
        if self.send_user_ids:
            bm = BitMap.deserialize(self.send_user_ids)
            old_send_user_ids = set(bm)
        else:
            old_send_user_ids = set()
        new_send_user_ids = old_send_user_ids | user_ids
        self.success_send_count = len(new_send_user_ids)
        self.send_user_ids = BitMap(new_send_user_ids).serialize() # noqa
