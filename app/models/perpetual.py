# -*- coding: utf-8 -*-

from datetime import datetime
from enum import Enum, IntEnum

from sqlalchemy.dialects.mysql import DECIMAL

from .base import db, ModelBase
from ..common import PerpetualMarketType


class PerpetualBalanceTransfer(db.Model):
    """
    合约资产划转
    """

    class Status(Enum):
        CREATED = 'created'
        FAILED = 'failed'
        DEDUCTED = 'deducted'
        FINISHED = 'finished'

    class TransferType(Enum):
        TRANSFER_IN = 'transfer_in'
        TRANSFER_OUT = 'transfer_out'

    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    updated_at = db.Column(db.MYSQL_DATETIME_6,
                           default=datetime.utcnow,
                           onupdate=datetime.utcnow)

    status = db.Column(db.Enum(Status), nullable=False)
    coin_type = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    transfer_type = db.Column(db.Enum(TransferType), nullable=False)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    deducted_at = db.Column(db.MYSQL_DATETIME_6)
    finished_at = db.Column(db.MYSQL_DATETIME_6)


class MarketSummaryHistory(db.Model):
    """
    开仓数量统计(小时)
    """
    ALL_MARKETS = 'ALL'

    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    updated_at = db.Column(db.MYSQL_DATETIME_6,
                           default=datetime.utcnow,
                           onupdate=datetime.utcnow)

    market_type = db.Column(db.String(32), nullable=False, index=True)
    position_short_users = db.Column(db.Integer, nullable=False, default=0)
    position_long_users = db.Column(db.Integer, nullable=False, default=0)
    position_short_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_long_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    report_time = db.Column(db.Integer, nullable=False)
    position_short_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_long_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class DailyMarketSummaryHistory(ModelBase):
    """
    开仓数量统计(天)
    """
    ALL_MARKETS = 'ALL'

    __table_args__ = (
        db.UniqueConstraint('market_type', 'report_time',
                            name='market_type_report_time_unique'),
    )
    report_time = db.Column(db.Integer, nullable=False)
    market_type = db.Column(db.String(32), nullable=False)
    position_short_users = db.Column(db.Integer, nullable=False, default=0)
    position_long_users = db.Column(db.Integer, nullable=False, default=0)
    position_short_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_long_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_short_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_long_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class PerpetualTradeSummary(ModelBase):
    """
    合约市场交易统计(每小时)
    （仅作为前端表格展示用，不能用作admin数据统计）
    """
    ALL_MARKETS = 'ALL'

    __table_args__ = (
        db.UniqueConstraint('market', 'report_time',
                            name='market_report_time_unique'),
    )
    report_time = db.Column(db.Integer, nullable=False)
    market = db.Column(db.String(32), nullable=False)
    taker_buy_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 主动买入量
    taker_sell_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)    # 主动卖出量
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)    # 取自k线的成交量
    taker_buy_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    taker_sell_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class DailyPerpetualTradeSummary(ModelBase):
    """
    合约市场交易统计(每日)
    （仅作为前端表格展示用，不能用作admin数据统计）
    """
    ALL_MARKETS = 'ALL'

    __table_args__ = (
        db.UniqueConstraint('market', 'report_time',
                            name='market_report_time_unique'),
    )
    report_time = db.Column(db.Integer, nullable=False)
    market = db.Column(db.String(32), nullable=False)
    taker_buy_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 主动买入量
    taker_sell_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)    # 主动卖出量
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8,
                             nullable=False, default=0)    # 取自k线的成交量（由于自成交存在，此值大于主动买入量+主动卖出量）
    taker_buy_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    taker_sell_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class BigCustomerMarketSummaryHistory(ModelBase):
    """
    大户开仓数量统计(小时)
    """
    RANK_PERCENT = '0.20'  # 持仓数量前20%为大户
    ALL_MARKETS = 'ALL'

    __table_args__ = (
        db.UniqueConstraint('market_type', 'report_time',
                            name='market_type_report_time_unique'),
    )
    report_time = db.Column(db.Integer, nullable=False)
    market_type = db.Column(db.String(32), nullable=False)
    position_short_users = db.Column(db.Integer, nullable=False, default=0)
    position_long_users = db.Column(db.Integer, nullable=False, default=0)
    position_short_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_long_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class DailyBigCustomerMarketSummaryHistory(ModelBase):
    """
    大户开仓数量统计(天)
    """
    RANK_PERCENT = '0.20'  # 持仓数量前20%为大户
    ALL_MARKETS = 'ALL'

    __table_args__ = (
        db.UniqueConstraint('market_type', 'report_time',
                            name='market_type_report_time_unique'),
    )

    report_time = db.Column(db.Integer, nullable=False)
    market_type = db.Column(db.String(32), nullable=False)
    position_short_users = db.Column(db.Integer, nullable=False, default=0)
    position_long_users = db.Column(db.Integer, nullable=False, default=0)
    position_short_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_long_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class NormalCustomerMarketSummaryHistory(ModelBase):
    """散户持仓多空比"""
    __table_args__ = (
        db.UniqueConstraint('market_type', 'report_time',
                            name='market_type_report_time_unique'),
    )
    ALL_MARKETS = 'ALL'

    report_time = db.Column(db.Integer, nullable=False)
    market_type = db.Column(db.String(32), nullable=False)
    position_short_users = db.Column(db.Integer, nullable=False, default=0)
    position_long_users = db.Column(db.Integer, nullable=False, default=0)
    position_short_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_long_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class DailyNormalCustomerMarketSummaryHistory(ModelBase):
    """散户持仓多空比"""
    __table_args__ = (
        db.UniqueConstraint('market_type', 'report_time',
                            name='market_type_report_time_unique'),
    )
    ALL_MARKETS = 'ALL'

    report_time = db.Column(db.Integer, nullable=False)
    market_type = db.Column(db.String(32), nullable=False)
    position_short_users = db.Column(db.Integer, nullable=False, default=0)
    position_long_users = db.Column(db.Integer, nullable=False, default=0)
    position_short_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_long_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class BasisRateHistory(ModelBase):
    """按小时指数价格基差率（只存储全站加权值）"""
    ALL_MARKETS = 'ALL'

    __table_args__ = (
        db.UniqueConstraint('market_type', 'report_time',
                            name='market_type_report_time_unique'),
    )
    report_time = db.Column(db.Integer, nullable=False)
    market_type = db.Column(db.String(32), nullable=False)
    basis_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class DailyBasisRateHistory(ModelBase):
    """每日指数价格基差率（只存储全站加权值）"""
    ALL_MARKETS = 'ALL'

    __table_args__ = (
        db.UniqueConstraint('market_type', 'report_time',
                            name='market_type_report_time_unique'),
    )
    report_time = db.Column(db.Integer, nullable=False)
    market_type = db.Column(db.String(32), nullable=False)
    basis_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class FundingRateHistory(ModelBase):
    """资金费率（只存储全站加权值）"""
    ALL_MARKETS = 'ALL'

    __table_args__ = (
        db.UniqueConstraint('market_type', 'report_time',
                            name='market_type_report_time_unique'),
    )
    report_time = db.Column(db.Integer, nullable=False)
    market_type = db.Column(db.String(32), nullable=False)
    funding_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class HourlyPerpetualMarginBurstReport(ModelBase):
    """每小时爆仓统计(全站)"""
    ALL_MARKETS = 'ALL'

    __table_args__ = (
        db.UniqueConstraint('market', 'report_time',
                            name='market_report_time_unique'),
    )

    report_time = db.Column(db.Integer, nullable=False)
    market = db.Column(db.String(32), nullable=False, default=ALL_MARKETS)
    long_burst_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)    # 爆仓量(USD)
    short_burst_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)    # 爆仓量(USD)


class PerpetualMarketIndex(ModelBase):
    """
    admin: 永续普通指数配置
    """

    class StatusType(Enum):
        OPEN = "open"
        CLOSE = "close"

    name = db.Column(db.String(32), nullable=False, unique=True)
    name_show = db.Column(db.String(32), nullable=False)
    risk_check_time = db.Column(db.Integer, nullable=False)
    price_precision = db.Column(db.Integer, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)
    is_visible = db.Column(db.Boolean, nullable=False, default=True)


class PerpetualMarketIndexDetail(ModelBase):
    """
    admin: 永续普通指数价格权重配置
    """

    class StatusType(Enum):
        PASS = "pass"
        DELETE = "delete"

    perpetual_market_index_id = db.Column(db.Integer, db.ForeignKey("perpetual_market_index.id"))

    exchange_name = db.Column(db.String(32), nullable=False)
    market = db.Column(db.String(32), nullable=False)
    weight = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    url = db.Column(db.String(128), nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)

    margin_index = db.relationship('PerpetualMarketIndex',
                                   backref=db.backref("perpetual_market_index_detail",
                                                      lazy='dynamic'))


class PerpetualComposeIndex(ModelBase):
    """
    admin: 永续合成指数价格配置
    """

    class StatusType(Enum):
        OPEN = "open"
        CLOSE = "close"

    class ComposeMethodType(IntEnum):
        MULTI = 1
        DIV = 2

    name = db.Column(db.String(32), nullable=False)

    price_precision = db.Column(db.Integer, nullable=False)

    first_market = db.Column(db.String(32), nullable=False)
    second_market = db.Column(db.String(32), nullable=False)

    compose_method = db.Column(db.Enum(ComposeMethodType), nullable=False)

    status = db.Column(db.Enum(StatusType), nullable=False,
                       default=StatusType.CLOSE)

    is_visible = db.Column(db.Boolean, nullable=False, default=True)


class PerpetualAssetIndex(ModelBase):
    """
    admin: 永续资产配置
    """

    class StatusType(Enum):
        OPEN = "open"
        CLOSE = "close"

    name = db.Column(db.String(32), nullable=False, unique=True)
    save_precision = db.Column(db.Integer, nullable=False)
    show_precision = db.Column(db.Integer, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)
    is_visible = db.Column(db.Boolean, nullable=False, default=True)


class PerpetualMarket(ModelBase):
    """
    admin: 永续市场配置
    """

    DEFAULT_INTEREST = 0

    class StatusType(Enum):
        OPEN = "open"
        CLOSE = "close"

    class MarginType(Enum):
        CROSS = "cross"
        ISOLATED = "isolated"

    class FundingLimitType(Enum):
        CALC_BY_MIN_MAINTEN_MARGIN = '关联最低维持保证金率'
        MANUAL = '临时手动配置'

    name = db.Column(db.String(32), nullable=False, unique=True)
    # 市场类型
    market_type = db.Column(db.Enum(PerpetualMarketType), nullable=False)
    # 费率精度
    fee_precision = db.Column(db.Integer, nullable=False)
    # 合约张数精度
    amount_precision = db.Column(db.Integer, nullable=False)
    # 交易货币名
    base_asset = db.Column(db.String(32), nullable=False)
    # 交易货币精度
    base_asset_precision = db.Column(db.Integer, nullable=False)
    # 定价货币名
    quote_asset = db.Column(db.String(32), nullable=False)
    # 定价货币精度
    quote_asset_precision = db.Column(db.Integer, nullable=False)
    # 价格粒度
    price_size = db.Column(DECIMAL(precision=26, scale=20), nullable=False)
    # 最小下单数量
    min_order_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 乘数
    multiplier = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 杠杆倍数配置
    leverages = db.Column(db.String(256), nullable=False)
    # 深度配置
    depths = db.Column(db.String(256), nullable=False)
    # 默认杠杆
    leverage_default = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 默认深度
    depth_default = db.Column(DECIMAL(precision=26, scale=20), nullable=False)
    # 默认仓位类型
    position_type_default = db.Column(db.Enum(MarginType), nullable=False)
    # 保证金影响额
    impact_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 资金费率每天起算时间点 单位 小时
    funding_start = db.Column(db.Integer, nullable=False)
    # 调整为动态资金费率的时间点
    dynamic_funding_start = db.Column(db.Integer, nullable=False)
    # 默认资金费率间隔时间 单位 小时
    default_funding_interval = db.Column(db.Integer, nullable=False)
    # 资金费率间隔时间 单位 小时
    funding_interval = db.Column(db.Integer, nullable=False)
    # 资金费率精度
    funding_rate_precision = db.Column(db.Integer, nullable=False)
    # 资金费率最大值
    funding_max = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 资金费率最小值
    funding_min = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 资金费率上下限配置
    funding_limit_type = db.Column(db.StringEnum(FundingLimitType), nullable=False,
                                   default=FundingLimitType.CALC_BY_MIN_MAINTEN_MARGIN)

    # 强平风险预警阈值
    liq_risk_alert = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 利率 readonly
    interest = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    status = db.Column(db.Enum(StatusType), nullable=False)
    offline_visible = db.Column(db.Boolean, nullable=False, default=False)  # 市场下架后，是否可见


class PerpetualMarketLimitConfig(ModelBase):
    """
    admin: 永续市场仓位档位配置
    """
    class StatusType(Enum):
        PASS = "pass"
        DELETE = "delete"

    perpetual_market_id = db.Column(db.Integer, db.ForeignKey(
        "perpetual_market.id"))
    # 仓位档位
    max_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 最高杠杆倍数
    max_leverage = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 位置保证金率
    mainten_margin_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.PASS)


class PerpetualInsuranceTransferHistory(ModelBase):
    """
    合约保险基金划转记录表
    """
    __table_args__ = (
        db.UniqueConstraint('report_date', 'asset',
                            name='report_date_asset_unique'),
    )

    class StatusType(Enum):
        CREATED = "created"
        DEDUCTED = "deducted"
        FINISHED = "finished"

    report_date = db.Column(db.DATE, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    asset = db.Column(db.String(32), index=True, nullable=False)
    # 理论值
    theoretic_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # current balance, snapshot for margin real insurance, 0 or < 0, after transfer
    balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 扣减时间
    deducted_at = db.Column(db.MYSQL_DATETIME_6, index=True)
    # 转出金额
    transfer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)
