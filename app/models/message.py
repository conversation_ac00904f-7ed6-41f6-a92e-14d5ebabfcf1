# -*- coding: utf-8 -*-
import json
from enum import Enum
from decimal import Decimal
from functools import cached_property
from operator import attrgetter
from typing import Tuple, Set, Dict

from flask_babel import gettext as _
from pyroaring import BitMap

from ..utils import mobile_country_code_to_countries, now, format_percent
from .base import ModelBase, db
from app.common import Language, MessageTitle, MessageContent


class Message(ModelBase):
    """ 站内信 """

    class Status(Enum):
        UNREAD = "未读"
        READ = "已读"
        DELETED = "已删除"  # 清空已读

    class DisplayType(Enum):
        TEXT = "文本"
        POPUP_WINDOW = "弹窗"

    class Channel(Enum):
        SYSTEM = _("系统消息")
        DEPOSIT_WITHDRAWAL = _("充值提现")
        TRADE_NOTIFICATION = _("交易通知")
        ACTIVITY = _("福利动态")
        ACCOUNT_SECURITY = _("账号安全")

    user_id = db.Column(db.Integer, db.<PERSON><PERSON>ey("user.id"), nullable=False)
    title = db.Column(db.StringEnum(MessageTitle), nullable=False)
    content = db.Column(db.StringEnum(MessageContent), nullable=False)
    params = db.Column(db.String(4096), nullable=False, default="")
    extra_info = db.Column(db.String(4096), nullable=False, default="")  # 存落地页信息
    display_type = db.Column(db.StringEnum(DisplayType), nullable=False)
    expired_at = db.Column(db.MYSQL_DATETIME_6)  # 弹窗的过期时间
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.UNREAD)
    channel = db.Column(db.StringEnum(Channel), nullable=False, default=Channel.SYSTEM)  # 消息渠道

    @cached_property
    def extra_info_dict(self):
        return json.loads(self.extra_info) if self.extra_info else {}

    @property
    def link_info_dict(self) -> dict:
        link_keys = ["web_link", "android_link", "ios_link"]
        return {key: self.extra_info_dict.get(key) for key in link_keys}

    # 手动推送的站内信相关
    @classmethod
    def new_push_message(
        cls,
        push_row: "MessagePush",
        user_id: int,
        web_link: str,
        android_link: str,
        ios_link: str = "",
    ) -> "Message":
        return Message(
            user_id=user_id,
            title=MessageTitle.PUSH_MSG_TEMPLATE.name,
            content=MessageContent.PUSH_MSG_TEMPLATE.name,
            params=json.dumps({}),
            extra_info=json.dumps(
                dict(
                    web_link=web_link,
                    android_link=android_link,
                    ios_link=ios_link,
                    push_id=push_row.id,
                )
            ),
            display_type=push_row.display_type,
            channel=push_row.channel,
        )

    def is_push_message(self) -> bool:
        return self.title == MessageTitle.PUSH_MSG_TEMPLATE

    def parse_push_id(self) -> int:
        return int(self.extra_info_dict.get("push_id", 0))

    def is_need_report(self) -> bool:
        """ 手动站内信&有跳转链接 则需要上报 """
        return self.is_push_message() and any(self.link_info_dict.values())


class CountrySmsSetting(ModelBase):

    country_code = db.Column(db.Integer, nullable=False, unique=True)
    sms_providers = db.Column(db.Text, nullable=False, default='')

    @property
    def region_cn_names(self) -> Tuple[str, ...]:
        return tuple(map(attrgetter('cn_name'),
                         mobile_country_code_to_countries(self.country_code)))

    @property
    def region_en_names(self) -> Tuple[str, ...]:
        return tuple(map(attrgetter('en_name'),
                         mobile_country_code_to_countries(self.country_code)))

    def _row_to_dict_hook_(self, dict_: dict):
        dict_.update(
            region_cn_names=self.region_cn_names,
            region_en_names=self.region_en_names
        )


class MessagePush(ModelBase):
    """ 站内信(手动)推送表 """

    class JumpType(Enum):
        NATIVE = "原生"
        URL = "URL"

    class Status(Enum):
        DRAFT = "未提交"
        CREATED = "待审核"
        AUDITED = "待推送"
        REJECTED = "未通过"
        PROCESSING = "处理中"
        FINISHED = "已推送"
        CANCELLED = "已取消"
        DELETED = "已删除"
        FAILED = "推送失败"

    name = db.Column(db.String(512), nullable=False, index=True)
    channel = db.Column(db.StringEnum(Message.Channel), nullable=False)
    display_type = db.Column(db.StringEnum(Message.DisplayType), nullable=False)
    push_time = db.Column(db.MYSQL_DATETIME_6, nullable=False, default=now, index=True)  # 推送时间
    jump_page_enabled = db.Column(db.Boolean, nullable=False, default=False)  # 是否跳转
    web_jump_type = db.Column(db.StringEnum(JumpType))
    web_jump_id = db.Column(db.Integer, db.ForeignKey("app_jump_list.id"))
    android_jump_type = db.Column(db.StringEnum(JumpType))
    android_jump_id = db.Column(db.Integer, db.ForeignKey("app_jump_list.id"))
    remark = db.Column(db.String(512), nullable=False, default="")
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.DRAFT)
    # 客群相关信息
    groups = db.Column(db.TEXT, nullable=False, default="")  # 格式：[用户分群 id]
    whitelist_enabled = db.Column(db.Boolean, nullable=False, default=False)  # 是否开启白名单
    user_whitelist = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default="")  # 白名单用户id, '1370,250'
    user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b"")  # 客群user_ids bitmap
    send_user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b"")  # 已发送的user_ids
    # 统计相关信息
    user_count = db.Column(db.Integer, nullable=False, default=0)  # 客群用户数
    send_user_count = db.Column(db.Integer, nullable=False, default=0)  # 送达客群数, len(send_user_ids)
    subscribe_count = db.Column(db.Integer, nullable=False, default=0)  # 客群中订阅用户数
    user_popup_read_count = db.Column(db.Integer, nullable=False, default=0)  # 弹窗时 已读用户数
    user_click_count = db.Column(db.Integer, nullable=False, default=0)  # 跳转时 点击链接的用户数
    # 审核相关信息
    created_by = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, index=True)
    audited_by = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=True)
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    auditor_remark = db.Column(db.String(512), nullable=False, default="")
    finished_at = db.Column(db.MYSQL_DATETIME_6)

    @cached_property
    def jump_link_map(self) -> Dict[str, str]:
        from app.models.system import AppJumpList

        web_link = android_link = ""
        if self.jump_page_enabled:
            jump_ids = []
            if self.web_jump_id:
                jump_ids.append(self.web_jump_id)
            if self.android_jump_id:
                jump_ids.append(self.android_jump_id)
            app_jump_rows = AppJumpList.query.filter(
                AppJumpList.id.in_(jump_ids),
            ).all()
            jump_link_map = {r.id: r.jump_data for r in app_jump_rows}
            web_link = jump_link_map.get(self.web_jump_id) or ""
            android_link = jump_link_map.get(self.android_jump_id) or ""
        return {
           "web_link": web_link,
           "android_link": android_link,
           "ios_link": "",  # ios暂时没有
        }

    @cached_property
    def parsed_msg_push(self):
        from app.business.push_statistic import MessagePushUserParser

        parser = MessagePushUserParser(self)
        parser.parse()
        return parser

    @property
    def users(self) -> Set[int]:
        """客群用户"""
        return self.parsed_msg_push.parsed_user_ids

    @property
    def subscribe_users(self) -> Set[int]:
        """订阅用户"""
        return self.parsed_msg_push.parsed_subscribe_user_ids

    def set_user_ids(self, user_ids: list):
        """设置客群 user_ids"""
        self.user_ids = BitMap(user_ids).serialize()

    def set_send_user_ids(self, send_user_ids: list):
        """设置发送客群 user_ids"""
        self.send_user_ids = BitMap(send_user_ids).serialize()

    @property
    def list_send_user_ids(self) -> list:
        """发送客群 user_ids"""
        if not self.send_user_ids:
            return []
        bm = BitMap.deserialize(self.send_user_ids)
        if not bm:
            return []
        return list(bm)

    def user_read_rate(self, count) -> str:
        """消息推送打开率"""
        if not self.send_user_count:
            return '0%'
        amount = Decimal(count / self.send_user_count)
        return format_percent(amount, 2)

    def user_click_rate(self, click_count, read_count) -> str:
        """ 点击率=点击量/浏览量 """
        if not read_count or not self.jump_page_enabled:
            return '0%'
        amount = Decimal(click_count / read_count)
        return format_percent(amount, 2)

    def get_groups(self) -> list:
        if not self.groups:
            return []
        return json.loads(self.groups)

    @cached_property
    def cached_user_ids(self) -> list:
        """客群 user_ids"""
        if not self.user_ids:
            return []
        bm = BitMap.deserialize(self.user_ids)
        if not bm:
            return []
        return list(bm)

    @cached_property
    def cached_user_whitelist(self) -> list:
        if not self.user_whitelist:
            return []
        return [int(user_id) for user_id in self.user_whitelist.split(',')]


class MessagePushContent(ModelBase):
    """ 站内信(手动)推送内容表 """

    __table_args__ = (db.UniqueConstraint("message_push_id", "lang", name="message_push_lang_uniq"),)

    message_push_id = db.Column(db.Integer, db.ForeignKey("message_push.id"), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False)
    content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)

    msg_push = db.relationship('MessagePush', backref=db.backref('contents', lazy='dynamic'))
