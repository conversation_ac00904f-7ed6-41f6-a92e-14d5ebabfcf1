from enum import Enum

from app import Language
from app.models import ModelBase, db


class PartnerPosition(ModelBase):
    Language = Language

    class WorkType(Enum):
        FULL_TIME = '全职'
        PART_TIME = '兼职'
        ALL = '全职&兼职'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    name = db.Column(db.String(256), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False, index=True)
    work_type = db.Column(db.Enum(WorkType), nullable=False)
    deliver_mail = db.Column(db.String(64), nullable=False)
    description = db.Column(db.String(4096), nullable=False)
    requirement = db.Column(db.String(4096), nullable=False, default='')
    welfare = db.Column(db.String(4096), nullable=False, default='')
    sort_id = db.Column(db.Integer, nullable=False, index=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


class PositionCountry(db.Model):

    id = db.Column(db.Integer, primary_key=True)
    location_code = db.Column(db.String(3), nullable=False)
    position_id = db.Column(db.Integer, db.ForeignKey('partner_position.id'), nullable=False)

    __table_args__ = (
        db.UniqueConstraint('position_id', 'location_code', name='pos_code_uniq'),
    )

    COUNTRY_LANG_INDEX = {
        Language.EN_US: (
            "USA", "GBR", "IND", "CAN", "SGP", "AUS", "PHL", "PAK", "ZAF", "NZL", "BGD", "IRL", "NGA",
            "NLD", "GHA"
        ),
        Language.ZH_HANS_CN: (
            "MYS", "HKG", "MAC"
        ),
        Language.ZH_HANT_HK: (
            "MYS", "HKG", "MAC"
        ),
        Language.FA_IR: (
            "IRN", "AFG", "TJK"
        ),
        Language.AR_AE: (
            "ARE", "SAU", "EGY", "DZA", "IRQ", "QAT", "OMN", "BHR", "KWT", "SYR", "MAR", "LBN", "JOR",
            "LBY", "TUN", "YEM", "MRT", "SOM"
        ),
        Language.TR_TR: (
            "TUR", "AZE"
        ),
        Language.RU_KZ: (
            "RUS", "BLR", "UKR", "KAZ", "KGZ"
        ),
        Language.ES_ES: (
            "ESP", "ARG", "BOL", "CHL", "COL", "CRI", "CUB", "DOM", "ECU", "SLV", "GNQ", "GTM", "HND",
            "MEX", "NIC",
            "PAN", "PRY", "PER", "PRI", "URY", "VEN", "GIB"
        ),
        Language.PT_PT: (
            "BRA", "PRT"
        ),
        Language.FR_FR: (
            "FRA", "CHE", "BEL", "LUX", "MCO", "MAR", "DZA", "TUN", "SDN", "BEN", "CIV", "CMR", "COD",
            "SEN", "MDG",
            "MLI", "REU", "TCD", "TGO", "BFA", "GIN", "GAB", "BDI", "HTI", "MRT"
        ),
        Language.DE_DE: (
            "DEU", "AUT", "CHE", "LUX"
        ),
        Language.JA_JP: ("JPN",),
        Language.KO_KP: ("KOR",),
        Language.VI_VN: ("VNM",),
        Language.ID_ID: ("IDN",),
    }

    @classmethod
    def exists(cls, lang: Language, iso3_code: str):
        iso3_codes = cls.COUNTRY_LANG_INDEX.get(lang) or ()
        return iso3_code in iso3_codes

    @classmethod
    def batch_create(cls, relationship_mapping: dict):
        pending = []
        for position_id, iso3_codes in relationship_mapping.items():
            for iso3_code in iso3_codes:
                pending.append(
                    cls(
                        location_code=iso3_code,
                        position_id=position_id
                    )
                )
        db.session.add_all(pending)
        db.session.commit()

    @classmethod
    def get_relationship_mapping(cls, position_ids: list) -> dict:
        if not position_ids:
            return {}

        model = PositionCountry
        objs = model.query.filter(
            model.position_id.in_(position_ids)
        ).all()
        mapping = {}
        for obj in objs:
            mapping.setdefault(obj.position_id, []).append(obj.location_code)

        return mapping
