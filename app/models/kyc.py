import json
from enum import Enum
from flask_babel import gettext as _

from app.models import ModelBase, db, KycVerification
from app.utils import amount_to_str


class KYCInstitution(ModelBase):
    RejectionReason = KycVerification.RejectionReason

    class Status(Enum):
        CREATED = '待审核'
        PASSED = '已通过'
        REJECTED = '已拒绝'
        CANCELED = '已取消'

    __table_args__ = (
        db.Index('ix_updated_at', 'updated_at'),
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    auditor = db.Column(db.Integer, db.ForeignKey('user.id'))
    status = db.Column(db.Enum(Status), nullable=False)
    rejection_reason = db.Column(db.Enum(RejectionReason), nullable=True)
    custom_rejection_reason = db.Column(db.String(4096), nullable=True)
    remark = db.Column(db.String(256), nullable=True)

    is_custom_reason = db.Column(db.<PERSON>, nullable=False, default=False)

    def get_reject_reason(self, translate=True):
        if self.is_custom_reason:
            return self.custom_rejection_reason
        else:
            if translate:
                return _(self.rejection_reason.value) if self.rejection_reason else None
            else:
                return self.rejection_reason.value if self.rejection_reason else None


class InstitutionCompany(ModelBase):
    institution_id = db.Column(db.Integer, db.ForeignKey('kyc_institution.id'), nullable=False)
    name = db.Column(db.String(512), nullable=False)
    register_code = db.Column(db.String(512), nullable=False)
    nature_of_business = db.Column(db.String(512), nullable=False)
    funding_source = db.Column(db.String(512), nullable=False)
    contact = db.Column(db.String(512), nullable=False)
    location_code = db.Column(db.String(3), nullable=False)
    register_address = db.Column(db.String(512), nullable=False)
    government_url = db.Column(db.String(512), nullable=False)

    apply_reason = db.Column(db.String(512), nullable=True)

    certificate_file_ids = db.Column(db.String(512), nullable=False)
    structure_file_ids = db.Column(db.String(512), nullable=False)
    constitution_file_ids = db.Column(db.String(512), nullable=False)
    roster_file_ids = db.Column(db.String(512), nullable=False)
    authorization_letter_file_ids = db.Column(db.String(512), nullable=False)
    statement_file_ids = db.Column(db.String(512), nullable=False)

    coi_file_ids = db.Column(db.String(512), nullable=True)
    other_file_ids = db.Column(db.String(512), nullable=True)

    def get_certificate_file_ids(self):
        return json.loads(self.certificate_file_ids)

    def get_structure_file_ids(self):
        return json.loads(self.structure_file_ids)

    def get_constitution_file_ids(self):
        return json.loads(self.constitution_file_ids)

    def get_roster_file_ids(self):
        return json.loads(self.roster_file_ids)

    def get_authorization_letter_file_ids(self):
        return json.loads(self.authorization_letter_file_ids)

    def get_statement_file_ids(self):
        return json.loads(self.statement_file_ids)

    def get_coi_file_ids(self):
        if not self.coi_file_ids:
            return []
        return json.loads(self.coi_file_ids)

    def get_other_file_ids(self):
        if not self.other_file_ids:
            return []
        return json.loads(self.other_file_ids)


class InstitutionDirector(ModelBase):
    class IDType(Enum):
        ID_CARD = _('身份证')
        PASSPORT = _('护照')

    institution_id = db.Column(db.Integer, db.ForeignKey('kyc_institution.id'), nullable=False)
    first_name = db.Column(db.String(64), nullable=False)
    last_name = db.Column(db.String(64), nullable=False)
    location_code = db.Column(db.String(3), nullable=False)
    id_type = db.Column(db.Enum(IDType), nullable=False)
    id_number = db.Column(db.String(512), nullable=False)
    identity_front_file_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    identity_back_file_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)
    face_img_file_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)
    is_beneficiary = db.Column(db.Boolean, nullable=False, default=False)

    shareholding_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)

    @property
    def name(self):
        return f'{self.first_name} {self.last_name}'

    @property
    def shareholding_ratio_display(self):
        if not self.shareholding_ratio:
            if self.is_beneficiary:
                return '高于 25%'
            return '0%'
        return f'{amount_to_str(self.shareholding_ratio)}%'


class UserRiskScreen(ModelBase):
    """ 用户风险筛查状态 """

    class Status(Enum):
        CREATED = "筛查中"
        PASSED = "无风险"
        RISKED = "有风险"
        CANCELLED = "已取消"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), unique=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class UserRiskScreenRequest(ModelBase):
    """ 用户风险筛查请求 """

    class ThirdParty(Enum):
        Refinitiv = "路孚特"
        DowJones = "道琼斯"

    class Type(Enum):
        # 机构kyc时，用户会有2个筛查请求：公司信息的筛查 和 董事长信息的筛查
        INDIVIDUAL = "个人"
        COMPANY = "公司"
        DIRECTOR = "公司董事长"

    class Status(Enum):
        CREATED = "待处理"
        PROCESSING = "处理中"
        FINISHED = "已完成"
        CANCELLED = "已取消"

    class Source(Enum):
        # 风险筛查请求的来源
        KYC_USER = "KYC用户"
        RICH_USER = "资产大户"
        LARGE_DEPOSIT_USER = "充值大户"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    track_no = db.Column(db.String(256), nullable=True, unique=True)  # 内部标识
    third_party = db.Column(db.String(64), nullable=True)
    type = db.Column(db.Enum(Type), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False)
    source = db.Column(db.StringEnum(Source), nullable=True)
    info = db.Column(db.MYSQL_MEDIUM_TEXT)  # 用来筛查的信息


class UserRiskScreenCase(ModelBase):
    """ 用户风险筛查Case """

    class EntityType(Enum):
        INDIVIDUAL = "个人"
        ORGANISATION = "组织|公司"

    class Gender(Enum):
        MALE = "男"
        FEMALE = "女"

    class Rating(Enum):
        NO_RISK = "无风险"
        LOW = "低"
        MEDIUM = "中"
        HIGH = "高"
        UNKNOWN = "未知"
        NOT_RATED = "未评级"

    class Status(Enum):
        CREATED = "待筛查"
        AUDIT_REQUIRED = "待审核"
        PASSED = "已通过"
        RISKED = "已拒绝"
        CANCELLED = "已取消"

    class RejectionReason(Enum):
        REGULATORY_ENFORCEMENT = _('由于监管要求，无法为你提供更多服务')

    request_id = db.Column(db.Integer, db.ForeignKey('user_risk_screen_request.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), index=True)
    case_id = db.Column(db.String(256), unique=True)  # UserRiskScreenRequest.track_no

    third_party = db.Column(db.StringEnum(UserRiskScreenRequest.ThirdParty), nullable=False)
    entity_type = db.Column(db.String(32), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    rating = db.Column(db.StringEnum(Rating), nullable=False, default=Rating.NOT_RATED)
    rating_remark = db.Column(db.String(512))
    # 筛查信息
    name = db.Column(db.String(256), nullable=False)
    country = db.Column(db.String(16), nullable=True, index=True)
    gender = db.Column(db.StringEnum(Gender))
    date_of_birth = db.Column(db.Date)
    # 审核信息
    auditor_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    rejection_reason = db.Column(db.String(4096), nullable=False, default='')
    is_custom_reason = db.Column(db.Boolean, nullable=False, default=False)
    remark = db.Column(db.String(512), default='')

    case_system_id = db.Column(db.String(256))
    group_id = db.Column(db.String(256))


class UserRiskScreenCaseResult(ModelBase):
    """ Case筛查的匹配结果 """

    class MatchStatus(Enum):
        POSITIVE = "匹配"
        POSSIBLE = "可能匹配"
        FALSE = "不匹配"
        UNSPECIFIED = "未指定"

    class Risk(Enum):
        HIGH = "高"
        MEDIUM = "中"
        LOW = "低"
        UNKNOWN = "未知"
        UNSPECIFIED = "未指定"

    class SyncStatus(Enum):
        SYNCED = "已同步"
        NOT_SYNCED = "未同步"

    case_id = db.Column(db.String(256), nullable=False, index=True)  # UserRiskScreenCase.case_id
    profile_id = db.Column(db.String(256), nullable=False, index=True)
    match_score = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 解析人和解析时间
    resolver_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    resolved_at = db.Column(db.MYSQL_DATETIME_6)
    # 解析信息
    match_status = db.Column(db.String(32))  # MatchStatus.name
    risk = db.Column(db.String(32))  # Risk.name
    reason = db.Column(db.String(256))
    resolution_remark = db.Column(db.String(512))

    result_id = db.Column(db.String(256))
    resolution_sync_status = db.Column(db.StringEnum(SyncStatus), default=SyncStatus.NOT_SYNCED)


class RefinitivGroup(ModelBase):
    """ Refinitiv Group信息 """

    # 只保存一条数据
    group_id = db.Column(db.String(256), nullable=False, unique=True)
    name = db.Column(db.String(256), nullable=False)
    secondary_fields = db.Column(db.MYSQL_MEDIUM_TEXT)
    resolution_toolkits = db.Column(db.MYSQL_MEDIUM_TEXT)


class RefinitivProfileInfo(ModelBase):
    """ Refinitiv-风险库-信息（人 or 公司） """

    profile_id = db.Column(db.String(256), unique=True)
    modified_at = db.Column(db.MYSQL_DATETIME_6)
    detail = db.Column(db.MYSQL_MEDIUM_TEXT)  # origin json


class DowjonesProfileInfo(ModelBase):
    """ Dowjones-风险库-信息（人 or 公司） """

    profile_id = db.Column(db.String(256), unique=True)
    detail = db.Column(db.MYSQL_MEDIUM_TEXT)  # origin json
