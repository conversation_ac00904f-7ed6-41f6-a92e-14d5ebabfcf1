# -*- coding: utf-8 -*-
from enum import Enum
from datetime import datetime

import json
from typing import Union, Dict

from .base import db, ModelBase
from ..utils.parser import JsonEncoder


class AdminUser(ModelBase):
    """
    用户表
    """

    class Status(Enum):
        PASSED = '启用'
        DELETED = '禁用'

    user_id = db.Column(db.Integer, db.<PERSON>ey('user.id'), nullable=False)
    name = db.Column(db.String(32), nullable=False)
    department = db.Column(db.String(32), nullable=False)
    position = db.Column(db.String(32), nullable=False)
    remark = db.Column(db.String(256), nullable=False, default='')
    status = db.Column(db.Enum(Status), nullable=False, default=Status.PASSED)
    op_user = db.Column(db.String(32), nullable=False)

    user = db.relationship('User',
                           backref=db.backref('admin_user', lazy='dynamic'),
                           foreign_keys=user_id)


class AdminRole(ModelBase):
    """
    角色表
    """

    class Status(Enum):
        PASSED = '启用'
        DELETED = '禁用'

    name = db.Column(db.String(32), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.PASSED)
    remark = db.Column(db.String(32), nullable=False)
    op_user = db.Column(db.String(32), nullable=False)


class AdminUserRole(ModelBase):
    """
    用户角色分配表
    """
    __table_args__ = (db.UniqueConstraint('user_id', 'admin_role_id',
                                          name='user_role_unique'),)

    class Status(Enum):
        PASSED = '启用'
        DELETED = '禁用'

    user_id = db.Column(db.Integer, db.ForeignKey('admin_user.user_id'),
                        nullable=False, index=True)
    admin_role_id = db.Column(db.Integer,
                              db.ForeignKey('admin_role.id'),
                              nullable=False, index=True)
    remark = db.Column(db.String(128), nullable=False, default='')
    status = db.Column(db.Enum(Status), nullable=False, default=Status.PASSED)
    op_user = db.Column(db.String(32), nullable=False)

    user = db.relationship(
        'AdminUser',
        backref=db.backref('admin_user_roles', lazy='dynamic'),
        foreign_keys=user_id
    )
    role = db.relationship(
        'AdminRole',
        backref=db.backref('admin_user_roles', lazy='dynamic'),
        foreign_keys=admin_role_id
    )


class AdminPermission(ModelBase):
    """
    权限表
    """
    __table_args__ = (db.UniqueConstraint('rule', 'method', 'app',
                                          name='rule_method_app_unique'),)

    class Status(Enum):
        PASSED = '启用'
        DELETED = '禁用'

    class Method(Enum):
        GET = 'GET'
        POST = 'POST'
        PUT = 'PUT'
        PATCH = 'PATCH'
        DELETE = 'DELETE'

    class Level(Enum):
        LOW = '低'
        MIDDLE = '中'
        HIGH = '高'
    
    class App(Enum):
        WEB = 'web'
        WALLET = 'wallet'
        INFORMATION = 'information'
        COMMENT = 'comment'

    name = db.Column(db.String(64), nullable=True)
    endpoint = db.Column(db.String(128), nullable=False)
    method = db.Column(db.Enum(Method), nullable=False)
    rule = db.Column(db.String(128), nullable=False)
    app = db.Column(db.StringEnum(App), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.PASSED)
    op_user = db.Column(db.Integer, db.ForeignKey('user.id'))

    @property
    def display_name(self):
        return '[{}]{}'.format(self.app.value, self.name)


class AdminRolePermission(ModelBase):
    """
    角色分配权限表
    """
    __table_args__ = (
        db.UniqueConstraint(
            'admin_role_id', 'admin_permission_id',
            name='admin_permission_unique'),
    )

    class Status(Enum):
        PASSED = '启用'
        DELETED = '禁用'

    admin_role_id = db.Column(
        db.Integer, db.ForeignKey('admin_role.id'), nullable=False, index=True)
    admin_permission_id = db.Column(
        db.Integer, db.ForeignKey('admin_permission.id'),
        nullable=False, index=True)
    remark = db.Column(db.String(128), nullable=False, default='')
    status = db.Column(db.Enum(Status), nullable=False, default=Status.PASSED)

    role = db.relationship(
        'AdminRole',
        backref=db.backref('admin_role_permissions', lazy='dynamic'),
        foreign_keys=admin_role_id
    )
    permission = db.relationship(
        'AdminPermission',
        backref=db.backref('admin_role_permissions', lazy='dynamic'),
        foreign_keys=admin_permission_id
    )


class AdminUserMenu(ModelBase):
    """
    用户菜单表
    """

    class Status(Enum):
        PASSED = '启用'
        DELETED = '禁用'

    name = db.Column(db.String(64), nullable=False)
    user_id = db.Column(
        db.Integer, db.ForeignKey('admin_user.user_id'), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.PASSED)


class AdminSignInLog(db.Model):

    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    ip = db.Column(db.String(64), nullable=False, default='', index=True)
    location = db.Column(db.String(128), nullable=False, default='')
    user_agent = db.Column(db.String(512), nullable=False, default='')

    user = db.relationship(
        'User',
        backref=db.backref('admin_sign_in_logs', lazy='dynamic'))


class AdminOperationLog(ModelBase):

    class Operation(Enum):
        EDIT_USER = 'edit_user'
        EDIT_USER_EMAIL = 'edit_user_email'
        EDIT_USER_EMAIL_AUDIT = 'edit_user_email_audit'
        EDIT_USER_KYC = 'edit_user_kyc'
        EDIT_USER_KYC_PRO = 'edit_user_kyc_pro'
        EDIT_USER_TYPE = 'edit_user_type'
        ADD_VIP_USER = 'add_vip_user'
        EDIT_VIP_USER_LEVEL = 'edit_vip_user_level'
        EDIT_USER_PREFERENCE = 'edit_user_preference'
        EDIT_USER_SETTING = 'edit_user_setting'
        DELETE_USER_SETTING = 'delete_user_setting'
        DELETE_THIRD_PARTY_ACCOUNT = 'delete_third_party_account'

        ADD_PERPETUAL_MARKET = 'add_perpetual_market'
        EDIT_PERPETUAL_MARKET = 'edit_perpetual_market'
        EDIT_SPOT_MARKET_MAKER_LEVEL = 'edit_spot_market_maker_level'
        EDIT_PERPETUAL_MARKET_MAKER_LEVEL = 'edit_perpetual_market_maker_level'
        ADD_SPOT_MARKET_MAKER = 'add_spot_market_maker'
        ADD_PERPETUAL_MARKET_MAKER = 'add_perpetual_market_maker'
        DELETE_SPOT_MARKET_MAKER = 'delete_spot_market_maker'
        DELETE_PERPETUAL_MARKET_MAKER = 'delete_perpetual_market_maker'
        DELETE_WITHDRAWAL_APPROVER = 'delete_withdrawal_approver'
        ADD_SPECIAL_USER_FEE = 'add_special_user_fee'
        EDIT_SPECIAL_USER_FEE = 'edit_special_user_fee'
        DELETE_SPECIAL_USER_FEE = 'delete_special_user_fee'

        ADD_INNER_MARKET_MAKER = 'add_inner_market_maker'
        DELETE_INNER_MARKET_MAKER = 'delete_inner_market_maker'
        EDIT_INNER_MARKET_MAKER = 'edit_inner_market_maker'

        ADD_CREDIT_USER = 'add_credit_user'
        EDIT_CREDIT_USER = 'edit_credit_user'
        DELETE_CREDIT_USER = 'delete_credit_user'

        DELETE_CREDIT_RECORD = 'delete_credit_record'
        FLAT_CREDIT_RECORD = 'flat_credit_record'

        EDIT_MARGIN_DAY_RATE = 'edit_margin_day_rate'
        DELETE_MARGIN_DAY_RATE = 'delete_margin_day_rate'

        ADD_DEPOSIT_ACTIVITY = 'add_deposit_activity'
        EDIT_DEPOSIT_ACTIVITY = 'edit_deposit_activity'
        SEND_DEPOSIT_ACTIVITY_GIFT = 'send_deposit_activity_gift'

        ADD_TRADE_ACTIVITY = 'add_trade_activity'
        EDIT_TRADE_ACTIVITY = 'edit_trade_activity'
        SEND_TRADE_ACTIVITY_GIFT = 'send_trade_activity_gift'

        ADD_AMBASSADOR_ACTIVITY = 'add_ambassador_activity'
        EDIT_AMBASSADOR_ACTIVITY = 'edit_ambassador_activity'
        SEND_AMBASSADOR_ACTIVITY_GIFT = 'send_ambassador_activity_gift'

        MAINTAIN_MODE = 'maintain_mode'

        ADD_MINING_ACTIVITY = 'add_mining_activity'
        EDIT_MINING_ACTIVITY = 'edit_mining_activity'

        ADD_TEMP_MAINTAIN = 'add_temp_maintain'
        EDIT_TEMP_MAINTAIN = 'edit_temp_maintain'
        DELETE_TEMP_MAINTAIN = 'delete_temp_maintain'
        STOP_TEMP_MAINTAIN = 'stop_temp_maintain'

        ADD_SPOT_MARKET = 'add_spot_market'
        EDIT_SPOT_MARKET = 'edit_spot_market'
        CANCEL_SPOT_MARKET_ORDERS = 'cancel_spot_market_orders'
        CANCEL_SPOT_MARKET_EXCHANGE_ORDERS = 'cancel_spot_market_exchange_orders'

        ADD_MARGIN_MARKET = 'add_margin_market'
        EDIT_MARGIN_MARKET = 'edit_margin_market'
        ADD_MARGIN_ASSET_RULE = 'add_margin_asset_rule'
        EDIT_MARGIN_ASSET_RULE = 'edit_margin_asset_rule'

        EDIT_ASSET_CONFIG = 'edit_asset_config'
        EDIT_ASSET_CHAIN_CONFIG = 'edit_asset_chain_config'

        EDIT_BUSINESS_SETTINGS = 'edit_business_settings'
        EDIT_SITE_SETTINGS = 'edit_site_settings'
        EDIT_COUNTRY_SETTINGS = 'edit_country_settings'
        EDIT_COUNTRY_SMS_SETTINGS = 'edit_country_sms_settings'
        DELETE_COUNTRY_SMS_SETTINGS = 'delete_country_sms_settings'
        EDIT_IP_WHITELIST = 'edit_ip_whitelist'

        DELETE_CELERY_QUEUE = 'delete_celery_queue'
        UNFREEZE_ACCOUNT = 'unfreeze_account'

        ADD_ADMIN_USER = 'add_admin_user'
        EDIT_ADMIN_USER = 'edit_admin_user'
        UNBIND_ADMIN_USER_WEBAUTHN = 'unbind_admin_user_webauthn'
        EDIT_ADMIN_USER_ROLES = 'edit_admin_user_roles'
        ADD_ADMIN_ROLE = 'add_admin_role'
        EDIT_ADMIN_ROLE = 'edit_admin_role'
        EDIT_ADMIN_ROLE_PERMISSIONS = 'edit_admin_role_permissions'
        EDIT_ADMIN_PERMISSION = 'edit_admin_permission'

        EDIT_WITHDRAWAL_FUSE_CONFIG = 'edit_withdrawal_fuse_config'
        ADD_MARKET_MAINTAIN = 'add_market_maintain'
        EDIT_MARKET_MAINTAIN = 'edit_market_maintain'
        DELETE_MARKET_MAINTAIN = 'delete_market_maintain'

        # 跟单
        EDIT_COPY_TRADING_CONFIG = "edit_copy_trading_config"

        # p2p
        P2P_COMPLAINT_OP = 'p2p_complaint_op'
        ADD_P2P_ASSET_CONFIG = 'add_p2p_asset_config'
        EDIT_P2P_ASSET_CONFIG = 'edit_p2p_asset_config'
        EDIT_P2P_TRADE_CONFIG = 'edit_p2p_trade_config'
        ADD_P2P_FIAT_CONFIG = "add_p2p_fiat_config"
        EDIT_P2P_FIAT_CONFIG = "edit_p2p_fiat_config"
        ADD_PAY_CHANNEL_CONFIG = "add_pay_channel_config"
        EDIT_PAY_CHANNEL_CONFIG = "edit_pay_channel_config"
        ADD_FIAT_PAY_CHANNEL = "add_fiat_pay_channel"
        EDIT_FIAT_PAY_CHANNEL = "edit_fiat_pay_channel"
        ADD_COUNTRY_PAY_CHANNEL = "add_country_pay_channel"
        EDIT_COUNTRY_PAY_CHANNEL = "edit_country_pay_channel"
        EDIT_COUNTRY_FIAT_CONFIG = "edit_country_fiat_config"

        EDIT_KYT_AUDIT = 'edit_kyt_audit'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    target_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))    # 可能为NULL
    operation = db.Column(db.String(64), nullable=False, index=True)
    detail = db.Column(db.Text, nullable=False)
    remark = db.Column(db.String(512))

    user = db.relationship('User', foreign_keys=[user_id])

    @classmethod
    def add(cls, user_id: int, operation: Operation, detail: Union[str, Dict],
            target_user_id: int = None,
            remark: str = ''):
        if isinstance(detail, dict):
            detail = json.dumps(detail, cls=JsonEncoder, ensure_ascii=False)
        log = cls(
            user_id=user_id,
            target_user_id=target_user_id,
            operation=operation.name,
            detail=detail,
            remark=remark
        )
        db.session_add_and_commit(log)


class AdminUserWebAuthn(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    last_used_at = db.Column(db.MYSQL_DATETIME_6)

    name = db.Column(db.String(512), nullable=False, default='')
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    rp_id = db.Column(db.String(512), nullable=False)
    sign_count = db.Column(db.Integer, nullable=False)
    credential_id = db.Column(db.String(1024), nullable=False) # urlsafe_b64encode
    public_key = db.Column(db.String(1024), nullable=False) # urlsafe_b64encode
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
