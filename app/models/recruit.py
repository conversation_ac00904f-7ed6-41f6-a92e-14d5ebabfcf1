from enum import Enum

from app import Language
from app.models import ModelBase, db


class Job(ModelBase):
    Language = Language

    class JobType(Enum):
        TECHNOLOGY = '技术'
        PRODUCT = '产品'
        DESIGN = '设计'
        CUSTOMER_SERVICE = '客服'
        PUBLIC_RELATIONS = '公关'
        BUSINESS = '商务'
        COMPLIANCE = '合规'
        INVESTMENT_RESEARCH = '投研'
        FUNCTION = '职能'

    class WorkPlace(Enum):
        HKG = '香港'
        WORLD_WIDE = '全球'

    class Experience(Enum):
        LESS_THAN_ONE_YEAR = '1年以内'
        ONE_TO_THREE_YEARS = '1-3年'
        THREE_TO_FIVE_YEARS = '3-5年'
        FIVE_TO_TEN_YEARS = '5-10年'
        MORE_THAN_TEN_YEARS = '10年以上'

    class Education(Enum):
        ANY = '学历不限'
        AT_LEAST_JUNIOR = '大专及以上'
        AT_LEAST_UNDERGRADUATE = '本科及以上'
        AT_LEAST_POSTGRADUATE = '硕士研究生及以上'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    job_type = db.Column(db.StringEnum(JobType), nullable=False)
    work_place = db.Column(db.StringEnum(WorkPlace), nullable=False)
    experience = db.Column(db.StringEnum(Experience), nullable=False)
    education = db.Column(db.StringEnum(Education), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)


class JobContent(db.Model):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANT_HK)

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.Text, nullable=False, default='')
    content = db.Column(db.Text, nullable=False, default='')
    lang = db.Column(db.StringEnum(Language), nullable=False)
    job_id = db.Column(db.Integer, db.ForeignKey('job.id'), nullable=False)
