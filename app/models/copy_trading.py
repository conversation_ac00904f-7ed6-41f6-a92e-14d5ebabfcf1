from collections import defaultdict
from datetime import timedelta, datetime
from decimal import Decimal
from enum import Enum
from .base import ModelBase, db
from app.common import Language, PrecisionEnum
from app.utils import now, new_hex_token, AWSBucketPublic, quantize_amount


class TimeRangeEnum(Enum):
    DAY7 = "7天"
    DAY30 = "30天"
    DAY90 = "90天"
    ALL = "全部时间"


class CopyTradingSetting(ModelBase):
    """跟单交易全局配置"""
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, index=True, default=Status.VALID)


class CopyTradingMarket(ModelBase):
    """跟单交易市场配置"""

    class Status(Enum):
        ONLINE = "上架"
        OFFLINE = "下架"

    market = db.Column(db.String(32), nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False)
    slippage_limit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal())  # 价格滑点


class CopyTradingRunUserStatus(ModelBase):
    """ 跟单执行子帐号状态 """

    class Status(Enum):
        USABLE = "可用"  # 空闲中
        UNUSABLE = "不可用"  # 带单中

    class Type(Enum):
        TRADER = '带单子账号'  # 只有1个
        FOLLOWER = '跟单子账号'  # 最多5个

    __table_args__ = (db.UniqueConstraint("main_user_id", "user_id", name="main_sub_id_unique"),)

    main_user_id = db.Column(db.Integer, nullable=False)  # 主账号ID
    user_id = db.Column(db.Integer, nullable=False, unique=True)  # 子账号ID
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.USABLE)
    type = db.Column(db.StringEnum(Type), nullable=False)
    remark = db.Column(db.String(512), nullable=False, default="")


class CopyTraderApplication(ModelBase):
    """跟单交易员-申请记录"""

    class Status(Enum):
        CREATED = 'created'
        AUDITED = 'audited'
        REJECTED = 'rejected'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, index=True)
    nickname = db.Column(db.String(64), nullable=True)
    account_name = db.Column(db.String(128), nullable=False)
    avatar = db.Column(db.String(512), nullable=False, default="")
    introduction = db.Column(db.String(512), nullable=True)
    contact = db.Column(db.String(256), nullable=False, default="")
    social_data = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default="")
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, index=True)
    creator = db.Column(db.Integer, nullable=False)
    auditor = db.Column(db.Integer, index=True)  # 审核人
    audited_at = db.Column(db.MYSQL_DATETIME_6)


class CopyTraderUser(ModelBase):
    """跟单交易员基础信息"""

    profit_share_max = 1
    profit_share_min = 0

    INACTIVE_TRADER_DAYS = 45
    STOP_INACTIVE_TRADER_DAYS = 60

    NORMAL_DISPLAY_PRIORITY = 0
    INACTIVE_DISPLAY_PRIORITY = -1

    class Status(Enum):
        ACTIVE = "带单中"
        INACTIVE = "未带单"
        DELETED = "已失效"

    trader_id = db.Column(db.String(64), nullable=False, unique=True)  # 暴露给前端接口
    user_id = db.Column(db.Integer, nullable=False, unique=True)  # 不暴露给前端接口
    nickname = db.Column(db.String(64))
    nickname_updated_at = db.Column(db.MYSQL_DATETIME_6)  # 修改昵称后，1个月内不可再次修改
    # TODO 待删除 统一使用 user_extra 中的 avatar
    avatar = db.Column(db.String(512), nullable=False, default="")
    introduction = db.Column(db.String(512), nullable=True)
    contact = db.Column(db.String(256), nullable=False, default="")
    telegram = db.Column(db.String(64), nullable=False, default="")
    facebook = db.Column(db.String(64), nullable=False, default="")
    twitter = db.Column(db.String(64), nullable=False, default="")
    reddit = db.Column(db.String(64), nullable=False, default="")
    medium = db.Column(db.String(64), nullable=False, default="")
    discord = db.Column(db.String(64), nullable=False, default="")
    #
    admin_remark = db.Column(db.String(64), nullable=False, default="")
    # 带单相关参数
    language = db.Column(db.StringEnum(Language), nullable=False, default=Language.EN_US, index=True)
    cur_follower_num = db.Column(db.Integer, nullable=False)
    max_follower_num = db.Column(db.Integer, nullable=False)
    profit_share_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal())  # 分润比例
    max_copy_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal())  # 最大跟单金额
    min_copy_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal())  # 最小跟单金额
    status = db.Column(db.StringEnum(Status), nullable=False, index=True)
    display_priority = db.Column(db.Integer, nullable=False, default=0)  # 前端展示的优先级，不活跃则小于0
    last_started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 最新一次带单开始运行时间
    last_finished_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)  # 最新一次带单结束时间

    @classmethod
    def gen_new_trader_id(cls) -> str:
        for _ in range(100):
            new_id = new_hex_token(8)
            if not cls.query.filter(cls.trader_id == new_id).first():
                return new_id
        raise RuntimeError

    @classmethod
    def get_avatar_url(cls, file_key: str) -> str:
        if not file_key:
            return ''
        return AWSBucketPublic.get_file_url(file_key)

    @property
    def is_active(self) -> bool:
        """ 是否带单中 """
        return self.status == self.Status.ACTIVE

    @property
    def is_inactive(self) -> bool:
        """ 是否未单中 """
        return self.status == self.Status.INACTIVE

    @property
    def is_full(self) -> bool:
        """ 当前跟单用户是否满了，满了则不允许新用户来跟单 """
        return self.cur_follower_num >= self.max_follower_num


class CopyTraderHistory(ModelBase):
    """带单人-带单历史
    写入CopyTraderUser表 或 CopyTraderUser.status -> VALID 时，写入该表记录
    """

    class Status(Enum):
        RUNNING = "带单中"
        ENDING = "带单结束中"
        FINISHED = "带单已结束"

    class FinishType(Enum):
        SELF = '带单人自己结束'
        SYSTEM = '系统结束'

    __table_args__ = (
        db.Index('user_id_status', 'user_id', 'status'),
    )

    user_id = db.Column(db.Integer, nullable=False)
    sub_user_id = db.Column(db.Integer, nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.RUNNING)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)  # 带单开始运行时间
    finished_at = db.Column(db.MYSQL_DATETIME_6, nullable=True, index=True)  # 带单终止时间
    finish_type = db.Column(db.StringEnum(FinishType))  # 终止类型


class UserFavoriteCopyTrader(ModelBase):
    """用户关注跟单交易员表"""

    __table_args__ = (db.UniqueConstraint("user_id", "copy_trader_user_id", name="user_trader_user_id_unique"),)

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    copy_trader_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)


class CopyFollowerHistory(ModelBase):
    """跟单人的跟单历史"""

    PERIODIC_SETTLEMENT_DAYS = 30  # 分润周期天数

    class Status(Enum):
        FOLLOWING = "跟单中"
        ENDING = "结束中"
        FINISHED = "跟单完成"

    class EndingStatusType(Enum):
        PENDING_CLOSE_POSITION = '待平仓'
        PENDING_PROFIT = '待分润'
        PENDING_TRANSFER_OUT = '待转出'
        FINISHED = '已完成'

    class MarginType(Enum):
        CROSS = "cross"  # 全仓
        ISOLATED = "isolated"  # 逐仓
        NONE = "none"  # 跟随交易员

    class LeverageType(Enum):
        SELF = "self"  # 指定
        NONE = "none"  # 跟随交易员

    created_at = db.Column(db.MYSQL_DATETIME_6, default=now, index=True)
    trader_history_id = db.Column(db.Integer, db.ForeignKey("copy_trader_history.id"), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    sub_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # 跟单用的系统子账号，属于user的
    copy_trader_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    copy_trader_sub_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # 带单人用的带单子帐号，冗余
    profit_trigger_rate = db.Column(db.MYSQL_DECIMAL_26_8)  # 触发止盈的收益率(已废弃，仅兼容老数据)
    loss_trigger_rate = db.Column(db.MYSQL_DECIMAL_26_8)  # 触发止损的收益率(已废弃，仅兼容老数据)
    margin_type = db.Column(db.StringEnum(MarginType), nullable=False)  # 保证金模式
    leverage = db.Column(db.Integer, nullable=False, default=0)  # 杠杆倍数
    leverage_type = db.Column(db.StringEnum(LeverageType), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.FOLLOWING, index=True)
    ending_status = db.Column(db.StringEnum(EndingStatusType), nullable=False, default=EndingStatusType.PENDING_CLOSE_POSITION)
    # 跟单利润相关的统计
    fund_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单金额
    total_profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单总盈亏
    total_copy_earn_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 累计跟单收益
    expected_profit_share_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 预计分润
    last_profit_shared_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)  # 最近一次分润结算时间（UTC0点）
    last_profit_share_rate = db.Column(db.MYSQL_DECIMAL_26_8, default=0)  # 最近一次交易员的分润记录
    finished_at = db.Column(db.MYSQL_DATETIME_6, index=True)

    @property
    def next_profit_shared_at(self):
        return self.calc_next_profit_shared_at(self.last_profit_shared_at)

    @classmethod
    def calc_next_profit_shared_at(cls, last_profit_shared_at: datetime) -> datetime:
        return last_profit_shared_at + timedelta(days=cls.PERIODIC_SETTLEMENT_DAYS)


class CopyTraderMarketOperation(ModelBase):
    """ 交易员市场维度的操作指令 """

    class Status(Enum):
        CREATED = "created"
        FINISHED = "finished"  # 跟单员操作都执行完成

    class Type(Enum):
        ADJUST_LEVERAGE = "adjust_leverage"  # 交易员对现有的仓位调整杠杆倍数，跟单员也会同步调整

    user_id = db.Column(db.Integer, index=True, nullable=False)
    sub_user_id = db.Column(db.Integer, index=True, nullable=False)
    market = db.Column(db.String(32), index=True, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, index=True)
    type = db.Column(db.StringEnum(Type))
    detail = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')  # 操作详情，json
    result = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')  # 执行结果，json


class CopyTraderPositionChangeRecord(ModelBase):
    """交易员仓位变化记录（包含ADL，强平） kafka消费写入
    """

    class Status(Enum):
        CREATED = "created"
        PROCESSED = "processed"
        FINISHED = "finished"  # 跟单员操作都执行完成

    class OperationType(Enum):
        ADD_POSITION = "add_position"
        REDUCE_POSITION = "reduce_position"
        CLOSE_POSITION = "close_position"

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    sub_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # 交易员的系统子账号
    market = db.Column(db.String(32), nullable=False)  # 市场
    asset = db.Column(db.String(32), nullable=False)  # 币种
    position_detail = db.Column(db.MYSQL_MEDIUM_TEXT)  # 仓位详情
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 当前仓位数量
    amount_delta = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 仓位数量变化（对比上一条消息）
    settle_val = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 当前仓位价值USDT
    settle_val_delta = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 仓位价值变化USDT（对比上一条消息）
    position_id = db.Column(db.Integer, nullable=False, index=True)  # 带单的仓位ID
    side = db.Column(db.Integer, nullable=False, index=True)  # 仓位方向
    event = db.Column(db.Integer, nullable=False)  # 事件
    status = db.Column(db.StringEnum(Status), index=True, nullable=False, default=Status.CREATED)
    offset = db.Column(db.BigInteger, nullable=False, unique=True)  # kafka 结合topic避免重复消费
    msg_sent_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # kafka 发送时间
    position_created_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)  # 仓位创建时间
    position_updated_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 仓位更新时间

    @classmethod
    def get_op_by_offset(cls, offset):
        op = CopyTraderPositionChangeRecord.query.filter(
            CopyTraderPositionChangeRecord.offset == offset,
        ).first()
        return op

    @classmethod
    def get_op_by_id(cls, id_):
        op = CopyTraderPositionChangeRecord.query.filter(
            CopyTraderPositionChangeRecord.id == id_,
        ).first()
        return op

    @property
    def avg_price(self):
        avg_price = abs(self.settle_val_delta/self.amount_delta
                        if self.amount_delta else 0)
        return quantize_amount(avg_price, PrecisionEnum.PRICE_PLACES)

    @property
    def change_delta_data(self):
        last_op = CopyTraderPositionChangeRecord.query.filter(
            CopyTraderPositionChangeRecord.id < self.id,
            CopyTraderPositionChangeRecord.position_id == self.position_id,
        ).order_by(CopyTraderPositionChangeRecord.id.desc()).first()

        last_op_data = last_op.to_dict() if last_op else defaultdict(Decimal)
        amount_delta = self.amount - last_op_data['amount']
        settle_val_delta = self.settle_val - last_op_data['settle_val']
        avg_price = self.avg_price
        side = self.side
        event = self.event
        return dict(
            amount_delta=amount_delta,
            settle_val_delta=settle_val_delta,
            avg_price=avg_price,
            side=side,
            event=event,
            is_first=True if not last_op else False,
        )

    @property
    def operation_type(self):
        _type = None
        if self.amount == 0 or self.event == 5:  # 5表示强平
            _type = self.OperationType.CLOSE_POSITION
        elif self.amount_delta > 0:  # 加仓
            _type = self.OperationType.ADD_POSITION
        elif self.amount_delta < 0:  # 减仓
            _type = self.OperationType.REDUCE_POSITION
        return _type


class CopyFollowerOrderOperation(ModelBase):
    """跟单员操作记录"""

    class Status(Enum):
        CREATED = "created"
        FINISHED = "finished"
        FAILED = "failed"

    change_record_id = db.Column(db.Integer, db.ForeignKey("copy_trader_position_change_record.id"))  # 用于追溯交易员的原始操作
    follow_history_id = db.Column(db.Integer, db.ForeignKey("copy_follower_history.id"), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    sub_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # 跟单员的系统子账号
    copy_trader_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    operation_type = db.Column(db.String(32))  # 操作类型
    market = db.Column(db.String(32))  # 市场
    fail_reason = db.Column(db.String(512))  # 失败结果
    order_id = db.Column(db.BigInteger, index=True)  # 跟单的订单ID
    executed_at = db.Column(db.MYSQL_DATETIME_6)  # 操作执行时间
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    order_detail = db.Column(db.MYSQL_MEDIUM_TEXT)  # 订单详情
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 下单成交数量
    deal_stock = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 下单成交金额

    @property
    def client_id(self):
        return f'copy_trading_{self.id}_{self.change_record_id}'

    @property
    def avg_price(self):
        avg_price = abs(self.deal_stock / self.amount
                        if self.amount else 0)
        return quantize_amount(avg_price, PrecisionEnum.PRICE_PLACES)


class CopyTraderStatistics(ModelBase):
    """跟单交易员统计信息"""

    __table_args__ = (db.UniqueConstraint("user_id", "time_range", name="user_id_time_range_unique"),)

    user_id = db.Column(db.Integer, nullable=False)
    time_range = db.Column(db.StringEnum(TimeRangeEnum), nullable=False, index=True)  # 最近N天的统计
    trade_days = db.Column(db.Integer, nullable=False, default=0)  # 交易天数
    trade_count = db.Column(db.Integer, nullable=False, default=0)  # 交易次数
    profit_count = db.Column(db.Integer, nullable=False, default=0)  # 交易员盈利仓位个数
    loss_count = db.Column(db.Integer, nullable=False, default=0)  # 交易员亏损仓位个数
    # todo 2025.05.08 当前交易员不到500，每次查询结果<500条，数据量比较小对于以下字段直接排序，后续如果数量太大需要优化
    winning_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 胜率profit_count/(profit_count+loss_count)
    aum = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 资产规模，所有type都一样
    equity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 账户权益
    margin_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 转入保证金数
    profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 盈亏额
    profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 盈亏率 = 盈亏额 / 转入保证金数
    follower_profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单员人盈亏
    total_profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 整体盈亏, 交易员盈亏＋跟单员盈亏
    mdd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 最大回撤
    profit_share_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 分润总收益
    last_trade_at = db.Column(db.MYSQL_DATETIME_6)
    last_update_at = db.Column(db.MYSQL_DATETIME_6)


class CopyFollowerStatistics(ModelBase):
    """跟单人统计信息"""
    user_id = db.Column(db.Integer, nullable=False, unique=True)
    profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal())  # 跟单总盈亏
    last_update_at = db.Column(db.MYSQL_DATETIME_6)


class DailyCopyTraderMarketStatistics(ModelBase):
    """带单人-交易市场每日统计"""

    __table_args__ = (db.UniqueConstraint("user_id", "date", "market", name="user_date_market_unique"),)

    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False)
    market = db.Column(db.String(32), nullable=False)
    trade_count = db.Column(db.Integer, nullable=False, default=0)
    profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class DailyCopyTraderStatistics(ModelBase):
    """带单人-每日统计数据"""

    __table_args__ = (db.UniqueConstraint("user_id", "date", name="user_id_date_unique"),)

    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False)
    trade_count = db.Column(db.Integer, nullable=False, default=0)  # 带单人当天交易次数|已平仓个数
    profit_count = db.Column(db.Integer, nullable=False, default=0)  # 带单人当天盈利仓位个数
    loss_count = db.Column(db.Integer, nullable=False, default=0)  # 带单人当天亏损仓位个数
    delta_profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 带单人当天盈亏（已平仓的）
    delta_pending_profit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 带单人当天盈亏（当前仓位的）
    delta_follower_profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单员人当天盈亏（已平仓的）
    delta_follower_pending_profit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单人当天盈亏（当前仓位的）
    delta_total_profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 当天整体盈亏, 交易员盈亏＋跟单员盈亏
    profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 带单人累加盈亏
    margin_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 带单人累加转入保证金数
    equity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 账户权益
    follower_profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单员人累计盈亏
    follower_margin_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单人累加转入保证金数
    total_profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 累计整体盈亏, 交易员盈亏＋跟单员盈亏
    profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 当天最新的盈亏率
    max_profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 当天最大的盈亏率
    aum = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 当天资产规模，即equity+sum(follower.equity)
    last_position_time = db.Column(db.MYSQL_DATETIME_6)  # 当天最后的开仓/平仓时间


class DailyCopyFollowerStatistics(ModelBase):
    """跟单人-每日统计数据"""

    __table_args__ = (db.UniqueConstraint("history_id", "date", name="history_id_date_unique"),)

    date = db.Column(db.Date, nullable=False, index=True)
    history_id = db.Column(db.Integer, nullable=False)  # 跟单记录ID
    user_id = db.Column(db.Integer, nullable=False, index=True)  # 跟单人-主帐号
    sub_user_id = db.Column(db.Integer, nullable=False, index=True)  # 跟单人-跟单子帐号
    delta_profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单子帐号当天盈亏（已平仓的）
    delta_pending_profit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单子帐号当天盈亏（当前仓位的）
    profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单子帐号累加盈亏
    margin_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单子帐号累加转入保证金数
    equity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 账户权益


class CopyTraderProfitShareDetail(ModelBase):
    """ 跟单交易员分润记录
    跟单者的跟单子账户 -> 交易员的跟单子账户
    """

    PROFIT_ASSET = 'USDT'

    class ProfitShareType(Enum):
        PERIODIC_SETTLEMENT = '周期结算'  # 周期结算，多条
        # 下面只有1条
        # 2025.06.03 https://app.clickup.com/t/86eter1n3 去掉止盈止损
        TAKE_PROFIT_TRIGGER = '止盈触发'
        STOP_LOSS_TRIGGER = '止损触发'
        END_BY_FOLLOWER = '跟单人结束跟单'
        END_BY_TRADER = '带单人-结束带单'

    class Status(Enum):
        CREATED = "created"
        FINISHED = "finished"
        FAILED = "failed"

    __table_args__ = (db.UniqueConstraint("date", "follow_history_id", "profit_share_type",
                                          name="date_follow_history_id_profit_share_type_unique"),)

    date = db.Column(db.Date, nullable=False)
    trader_history_id = db.Column(db.Integer, db.ForeignKey("copy_trader_history.id"), nullable=False)
    follow_history_id = db.Column(db.Integer, db.ForeignKey("copy_follower_history.id"), nullable=False)
    transfer_history_id = db.Column(db.Integer, db.ForeignKey("copy_transfer_history.id"))
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    sub_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # out
    copy_trader_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    copy_trader_sub_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # in
    asset = db.Column(db.String(32), nullable=False)
    profit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 已实现盈亏
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 分润金额
    profit_share_rate = db.Column(db.MYSQL_DECIMAL_26_8, default=0)  # 分润比例
    profit_share_type = db.Column(db.StringEnum(ProfitShareType), nullable=False)  # 分润类型
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class CopyTransferHistory(ModelBase):
    """ 跟单资金划转历史 """

    class Type(Enum):
        # 主帐号转入转出的都是现货账户，子帐号转入转出的都是合约账户
        TRADER_TRANSFER_IN = "带单资金划转-转入"  # trader spot -> trader sub per
        TRADER_TRANSFER_OUT = "带单资金划转-转出"  # trader sub per -> trader spot
        TRADER_COUPON_USE = "带单体验金到账-划入"  # sys_user per -> trader sub per
        TRADER_COUPON_RECYCLE = "带单体验金回收-划出"  # trader sub per -> sys_user per
        FOLLOWER_TRANSFER_IN = "跟单资金划转-转入"  # follower spot -> follower sub per
        FOLLOWER_TRANSFER_OUT = "跟单资金划转-转出"  # follower sub per -> follower spot
        FOLLOWER_COUPON_USE = "跟单体验金到账-划入"  # sys_user per -> trader sub per
        FOLLOWER_COUPON_RECYCLE = "跟单体验金回收-划出"  # trader sub per -> sys_user per
        FOLLOWER_PROFIT_TRANSFER_OUT = "跟单分润资金划转-转出"  # follower sub per -> trader sub per

    TRADER_TYPES = [
        Type.TRADER_TRANSFER_IN, Type.TRADER_TRANSFER_OUT,
        Type.TRADER_COUPON_USE, Type.TRADER_COUPON_RECYCLE,
    ]
    FOLLOWER_TYPES = [
        Type.FOLLOWER_TRANSFER_IN, Type.FOLLOWER_TRANSFER_OUT,
        Type.FOLLOWER_COUPON_USE, Type.FOLLOWER_COUPON_RECYCLE,
    ]

    class AccountType(Enum):
        SPOT = "spot"
        PERPETUAL = "perpetual"

    class Status(Enum):
        CREATED = "created"
        DEDUCTED = "deducted"
        FAILED = "failed"  # 扣减失败
        FINISHED = "finished"

    __table_args__ = (
        db.Index('main_user_id_his_id_type', 'main_user_id', 'history_id', 'type'),
    )

    main_user_id = db.Column(db.Integer, nullable=False)  # 带单人、跟单人
    history_id = db.Column(db.Integer, nullable=False, index=True)  # 带单记录id、跟单记录id
    from_user_id = db.Column(db.Integer, nullable=False, index=True)
    to_user_id = db.Column(db.Integer, nullable=False, index=True)
    from_account_type = db.Column(db.StringEnum(AccountType), nullable=False)
    to_account_type = db.Column(db.StringEnum(AccountType), nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    deducted_at = db.Column(db.MYSQL_DATETIME_6)
    finished_at = db.Column(db.MYSQL_DATETIME_6)
