#!/usr/bin/env python3

from enum import Enum

from .base import db, ModelBase


class ViaBTCPoolOrder(ModelBase):

    class Status(Enum):
        CREATED = 'created'
        TRANSFERRING = 'transferring'
        TRANSFER_FAILED = 'transfer_failed'
        TRANSFER_SUCCEEDED = 'transfer_succeeded'
        FINISHED = 'finished'

    user_id = db.Column(db.Integer, db.Foreign<PERSON>ey('user.id'))
    email = db.Column(db.String(64), nullable=False, index=True)
    order_id = db.Column(db.BigInteger, nullable=False, unique=True)
    deposit_id = db.Column(db.Integer, nullable=True)
    withdrawal_id = db.Column(db.Integer, nullable=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    order_at = db.Column(db.MYSQL_DATETIME_6, index=True)

    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)
    remark = db.Column(db.String(128), nullable=False, default="")

    user = db.relationship('User', backref=db.backref(
        'viabtc_pool_orders', lazy='dynamic'))
