from app import Language
from app.models import ModelBase, db


class UserEmail<PERSON>heck(ModelBase):

    title = db.Column(db.String(32), nullable=False)
    admin_user_id = db.Column(db.Integer, nullable=False)


class UserEmailCheckDetail(ModelBase):

    ref_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON>('user_email_check.id'), nullable=False)
    email = db.Column(db.String(64), nullable=False)
    user_id = db.Column(db.Integer, nullable=True)
    is_coinex = db.Column(db.<PERSON><PERSON>an, nullable=False, default=False)
    is_cleared = db.Column(db.<PERSON>, nullable=True)
    is_fraud = db.Column(db.<PERSON><PERSON><PERSON>, nullable=True)
    activity_id = db.Column(db.String(64), nullable=True)
    anti_fraud_data = db.Column(db.<PERSON>, nullable=True)

    def load_anti_fraud_data(self):
        """
        anti_fraud_data 结构如下
        {
            "score": 0, 
            "total_usd": "1185498989.94", 
            "is_surolus": false, 
            "similar_emails": {}, 
            "same_login_ip_cnt": 0,
            "same_device_id_cnt": 0, 
            "same_register_ip_cnt": 0, 
            "peer_transfer_user_ids": []
        }
        """
        data = {"activity_id": self.activity_id}
        if not self.anti_fraud_data:
            return data
        data["anti_fraud_score"] = self.anti_fraud_data.get('score', 0)
        data["anti_fraud_detail"] = f"(同注册IP数 {self.anti_fraud_data.get('same_register_ip_cnt', 0)}; " \
            f"同登录IP数 {self.anti_fraud_data.get('same_login_ip_cnt', 0)}; " \
            f"同设备ID数 {self.anti_fraud_data.get('same_device_id_cnt', 0)}; " \
            f"账号资产 {self.anti_fraud_data.get('total_usd', 0)}; " \
            f"站内转账关联账号 {len(self.anti_fraud_data.get('peer_transfer_user_ids', []))};"
        data["similar_emails"] = "; ".join([email for _, email in self.anti_fraud_data.get('similar_emails', {}).items()])
        return data


class TelegramGroupBot(ModelBase):
    API_HOST = 'https://api.telegram.org'

    name = db.Column(db.String(32), nullable=False, default='', comment='group name')
    token = db.Column(db.String(64), nullable=False, comment='bot token')
    lang = db.Column(db.StringEnum(Language), nullable=False, default=Language.EN_US, comment='language to send msg')
    chat_id = db.Column(db.String(16), nullable=False)

    @property
    def send_msg_url(self):
        return f'{self.API_HOST}/bot{self.token}/sendMessage'


class UserMaskIdTransfer(ModelBase):

    title = db.Column(db.String(32), nullable=False)
    admin_user_id = db.Column(db.Integer, nullable=False)
    file_key = db.Column(db.String(512), nullable=False)
