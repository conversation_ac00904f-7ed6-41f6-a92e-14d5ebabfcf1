# -*- coding: utf-8 -*-
from app.models import db, Enum

from .base import ModelBase
from ..common import PerpetualMarketType


class UserTradeSummary(ModelBase):
    """
    交易统计
    """
    __table_args__ = (db.UniqueConstraint('report_date', 'system', 'user_id',
                                          name='report_date_system_user_id'),)

    class System(Enum):
        SPOT = 'spot'
        PERPETUAL = 'perpetual'

    report_date = db.Column(db.DATE, nullable=False)
    system = db.Column(db.Enum(System), nullable=False, default=System.SPOT)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    taker_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    maker_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    rank = db.Column(db.Integer, nullable=False, default=0)

    user = db.relationship(
        'User',
        backref=db.backref('user_trade_summary', lazy='dynamic'),
        foreign_keys=user_id)

    @classmethod
    def check_data_ready(cls, report_date, system=None) -> bool:
        # 判断某天的数据是否已生成
        q = cls.query.filter(cls.report_date == report_date)
        if system:
            q = q.filter(cls.system == system)
            return bool(q.first())
        else:
            spot_q = q.filter(cls.system == cls.System.SPOT)
            perp_q = q.filter(cls.system == cls.System.PERPETUAL)
            return bool(spot_q.first()) and bool(perp_q.first())


class UserExchangeSummary(ModelBase):
    """
    兑换交易统计
    """
    __table_args__ = (db.UniqueConstraint('report_date', 'user_id', name='report_date_user_id'),)

    report_date = db.Column(db.DATE, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    web_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    server_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    @classmethod
    def check_data_ready(cls, report_date) -> bool:
        # 判断某天的数据是否已生成
        q = cls.query.filter(cls.report_date == report_date)
        if bool(q.first()):
            return True
        from app.models.exchange import AssetExchangeOrder
        if AssetExchangeOrder.check_is_exchanged(report_date):
            return False
        return True


class UserTradeFeeSummary(ModelBase):
    """
    交易手续费统计
    """
    __table_args__ = (db.UniqueConstraint('report_date', 'system', 'user_id',
                                          name='report_date_system_user_id'),)

    class System(Enum):
        SPOT = 'spot'
        PERPETUAL = 'perpetual'

    report_date = db.Column(db.DATE, nullable=False)
    system = db.Column(db.Enum(System), nullable=False, default=System.SPOT)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    trade_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    rank = db.Column(db.Integer, nullable=False, default=0)

    user = db.relationship(
        'User',
        backref=db.backref('user_trade_fee_summary', lazy='dynamic'),
        foreign_keys=user_id)

    @classmethod
    def check_data_ready(cls, report_date, system=None) -> bool:
        # 判断某天的数据是否已生成
        q = cls.query.filter(cls.report_date == report_date)
        if system:
            q = q.filter(cls.system == system)
            return bool(q.first())
        else:
            spot_q = q.filter(cls.system == cls.System.SPOT)
            perp_q = q.filter(cls.system == cls.System.PERPETUAL)
            return bool(spot_q.first()) and bool(perp_q.first())


class SpotAssetTradeSummary(ModelBase):
    """
    币币资产交易统计
    """

    report_date = db.Column(db.DATE, nullable=False, index=True)
    money_asset = db.Column(db.String(32), nullable=False)
    stock_asset = db.Column(db.String(32), nullable=False)
    market = db.Column(db.String(32), nullable=False)
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class PerpetualAssetTradeSummary(ModelBase):
    """
    合约资产交易统计
    """

    report_date = db.Column(db.DATE, nullable=False, index=True)
    money_asset = db.Column(db.String(32), nullable=False)
    stock_asset = db.Column(db.String(32), nullable=False)
    market = db.Column(db.String(32), nullable=False)
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class UserPerpetualProfitRealSummary(ModelBase):
    """
    合约盈利统计
    """
    __table_args__ = (db.UniqueConstraint('report_date', 'market_type', 'user_id', name='report_market_user_id'),)

    report_date = db.Column(db.DATE, nullable=False)
    market_type = db.Column(db.Enum(PerpetualMarketType), nullable=False)   # 市场类型
    user_id = db.Column(db.Integer, nullable=False)
    profit_real_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 已实现盈亏(USD)
    rank = db.Column(db.Integer, nullable=False, default=0)
