#!/usr/bin/python
# -*- coding: utf-8 -*-
from enum import Enum
from .base import db, ModelBase

from sqlalchemy import (
    Column, Integer, String,
)


class CommentTipTransfer(ModelBase):
    """评论打赏资产变更表"""

    class Status(Enum):
        CREATED = "created"  # 待扣减 source_user_id 的资产
        DEDUCTED = "deducted"  # 已扣减 source_user_id 的资产
        FINISHED = "finished"  # 已增加 target_user_id 的资产
        FAILED = "failed"	   # 资产变更失败

    source_user_id = Column(Integer, nullable=False, index=True)  # 打赏用户ID
    target_user_id = Column(Integer, nullable=False, index=True)  # 被打赏用户ID
    comment_id = Column(Integer, nullable=False, index=True)  	  # 打赏评论ID
    amount = Column(db.MYSQL_DECIMAL_26_8, nullable=False)  		  # 打赏金额（CET）
    asset = Column(String(32), nullable=False) # 币种
    asset_id = Column(Integer, nullable=False) # 币种 id（防止币种重名，需要记录 id，当前使用币种资料id）
    status = Column(db.StringEnum(Status), default=Status.CREATED, nullable=False)
    tip_id = Column(Integer, nullable=True, unique=True) # 评论系统打赏 id
