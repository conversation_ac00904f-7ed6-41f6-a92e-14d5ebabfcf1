# -*- coding: utf-8 -*-

from typing import List

from .base import Enum, ModelBase, db


class AntiFraudScore(ModelBase):

    # same_register_ip_cnt = db.Column(db.Integer, nullable=False, default=0)
    # same_login_ip_cnt = db.Column(db.Integer, nullable=False, default=0)
    # same_device_id_cnt = db.Column(db.Integer, nullable=False, default=0)
    # total_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # peer_transfer_users = db.Column(
    #     db.MYSQL_MEDIUM_BLOB, nullable=False, default=b""
    # )  # bitmap
    # # 羊毛得分
    # score = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # # 相似邮箱数
    # similar_email_cnt = db.Column(db.Integer, nullable=False, default=0)
    # similar_emails = db.Column()

    class Status(Enum):
        CREATED = "已创建"
        FINISHED = "已完成" # 完成了羊毛得分的计算
        REPORTED = "已上传" # 完成了羊毛党的上报

    class ActivityType(Enum):
        TRADE_RANK = "交易排位赛"
        AIRDROP = "空投"
        DIBS = "Dibs"
        DEPOSIT_BONUS = "充值福利"

    __table_args__ = (
        db.UniqueConstraint(
            "activity_id", "activity_type", name="activity_id_type_unique"
        ),
    )
    activity_id = db.Column(db.Integer, db.ForeignKey("activity.id"))
    activity_type = db.Column(db.StringEnum(ActivityType), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)
    data = db.Column(db.JSON, nullable=True, default={})

    def load_similar_email_user_ids(self, above_similar_emails_cnt=2) -> List[int]:
        res = []
        for user_id, data in self.data.items():
            if len(data.get("similar_emails", {})) >= above_similar_emails_cnt:
                res.append(int(user_id))
        return res