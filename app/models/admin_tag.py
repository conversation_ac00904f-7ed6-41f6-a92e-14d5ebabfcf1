from enum import Enum
from .base import db, ModelBase


class AdminTagCategory(ModelBase):
    # CET大户，资产大户，内部账号暂时只做同步数据展示，无处理
    class Status(Enum):
        PASSED = '生效'
        DELETED = '失效'

    name = db.Column(db.String(128), nullable=False, unique=True)
    remark = db.Column(db.String(512), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.PASSED)


class AdminTagUser(ModelBase):

    __table_args__ = (
        db.UniqueConstraint('tag_id', 'user_id', name='tag_id_user_id_unique'),
    )

    class Status(Enum):
        PASSED = '生效'
        DELETED = '失效'

    tag_id = db.Column(db.Integer, db.ForeignKey('admin_tag_category.id'), nullable=False)
    remark = db.Column(db.String(512), nullable=False, default='')
    user_id = db.Column(db.Integer, index=True, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.PASSED)
    updated_by = db.Column(db.Integer)
    detail = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')  # 操作详情，json
