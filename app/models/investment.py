# -*- coding: utf-8 -*-
from enum import Enum

from .base import ModelBase, db


class InvestmentAccount(ModelBase):
    """
    理财账户
    """
    class StatusType(Enum):
        OPEN = "open"
        CLOSE = "close"

    ACCOUNT_ID = 20000

    account_id = db.Column(db.Integer, nullable=False, default=ACCOUNT_ID)

    asset = db.Column(db.String(32), nullable=False, unique=True)
    status = db.Column(db.Enum(StatusType), nullable=False)


class InvestmentBalanceHistory(ModelBase):
    """
    用户的理财记录
    """
    class StatusType(Enum):
        CREATE = 'create'
        SUCCESS = "success"
        DEDUCTED = 'deducted'
        FAIL = "fail"

    class OptType(Enum):
        IN = "in"
        OUT = "out"  # amount为负
        INTEREST = "interest"

    __table_args__ = (
        db.UniqueConstraint('user_id', 'asset', 'report_date', name='user_id_asset_report_date'),
        db.Index("idx_created_at", "created_at"),
        db.Index("idx_user_id_opt_type_status", "user_id", "opt_type", "status"),
        db.Index("idx_user_id_opt_type_status_created_at", "user_id", "opt_type", "status", "created_at"),
    )

    success_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    # 用户发放利息的日息，做mysql数据库的唯一约束，保证不重复发
    report_date = db.Column(db.Date, nullable=True, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))

    investment_account_id = db.Column(db.Integer, nullable=False)
    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    opt_type = db.Column(db.Enum(OptType), nullable=False)
    day_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)


class UserInvestmentSummary(ModelBase):
    """
    用户累计理财收益汇总
    """

    __table_args__ = (
        db.UniqueConstraint('user_id', 'asset', name='user_id_asset_uniq'),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))

    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class InvestmentUserAMMSlice(ModelBase):
    """
    投资账号AMM持仓统计
    """

    __table_args__ = (
        db.UniqueConstraint('report_date', 'market', name='report_date_market_uniq'),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False, index=True)
    base_asset = db.Column(db.String(32), nullable=False)
    quote_asset = db.Column(db.String(32), nullable=False)
    online_days = db.Column(db.Integer, nullable=False, default=0)
    base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_cost = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_usd_profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    profit_rate_7d = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    profit_rate_30d = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class InvestmentUserSpotSlice(ModelBase):
    """
    投资账号现货持仓统计
    """

    __table_args__ = (
        db.UniqueConstraint('report_date', 'asset', name='report_date_asset_uniq'),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False, index=True)
    online_days = db.Column(db.Integer, nullable=False, default=0)
    price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    avg_price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)    # 购买均价
    profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class InvestmentUserAMMMarketRemark(ModelBase):
    """投资账号AMM市场备注"""

    market = db.Column(db.String(32), nullable=False, unique=True)
    remark = db.Column(db.Text, nullable=False)


class InvestmentUserSpotAssetRemark(ModelBase):
    """投资账号现货资产备注"""

    asset = db.Column(db.String(32), nullable=False, unique=True)
    remark = db.Column(db.Text, nullable=False)
