from datetime import timedelta
from enum import Enum
from decimal import Decimal
from operator import and_

from bson import ObjectId
from sqlalchemy import func, or_

from app.common import P2pBusinessType, P2pOrderUserType
from app.exceptions.basic import InvalidArgument
from app.models import db, ModelBase
from app.utils import quantize_amount
from app.utils.date_ import now, today, dt_to_today
from flask_babel import gettext as _


class P2pOrder(ModelBase):
    """P2P 订单"""

    class Status(Enum):
        CREATED = "待接单"
        CONFIRMED = "待付款"
        PAID = "已付款"
        TO_FINISH = "放币中"
        FINISHED = "已完成"
        CANCELED = "已取消"

    class CancelType(Enum):
        # 接单阶段的取消
        CONFIRM_TIMEOUT = _("商家超时未确认")
        MERCHANT_CREATED_CANCEL = _("商家拒绝接单")
        CUSTOMER_CREATED_CANCEL = _("用户接单前取消")
        # 接单后的取消
        PAY_TIMEOUT = _("买家超时未付款")
        BUYER_CANCEL = _("买方取消")
        SERVICE_CANCEL = _("客服手动取消")  # 偏卖家
        # 系统取消
        SYSTEM_CANCEL = _("系统取消")

    class CancelReason(Enum):
        # 买家
        NO_WANT_BUY = _("不想交易了")
        INFO_ERROR = _("信息填写错误")
        ACCIDENTAL_PAY = _('误点"我已付款"')
        OTHER = _("其它原因")
        # 卖家
        SELL_PAY_CHANNEL_ERROR = _("卖家收款方式错误")
        MUTUAL_CANCEL = _("协商一致取消")

    __table_args__ = (
        db.Index("created_at", "created_at"),
    )

    order_id = db.Column(db.String(32), nullable=False, comment="订单ID(16位)", unique=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, comment="用户ID(发起方)", index=True)
    merchant_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, comment="商家ID(接单方)", index=True)
    adv_id = db.Column(db.String(64), nullable=False, comment="广告ID", index=True)

    price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="广告价格")
    # 所有单都是以用户对广告商发起
    # 如果用户发起买单，相当于用 法币 买入 数字货币，数字货币为交易货币
    # 用户发起卖单，卖出 数字货币 换取 法币，数字货币为交易货币
    base = db.Column(db.String(25), nullable=False, comment="交易货币")  # 数字货币
    base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="交易货币金额")
    quote = db.Column(db.String(25), nullable=False, comment="定价货币", index=True)  # 法币
    quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="定价货币金额")
    side = db.Column(db.StringEnum(P2pBusinessType), nullable=False, comment="订单方向")

    user_pay_channel_id = db.Column(db.String(30), nullable=False, comment="卖方支付渠道ID")
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED,
                       comment="订单状态", index=True)

    cert_file_ids = db.Column(db.TEXT, comment="凭证文件ID")
    confirm_timeliness = db.Column(db.Integer, comment="接单过期时间")
    payment_timeliness = db.Column(db.Integer, comment="付款过期时间")

    cancel_type = db.Column(db.StringEnum(CancelType), comment="取消类型")
    cancel_reason = db.Column(db.StringEnum(CancelReason), comment="取消原因")
    cancel_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), comment="取消用户ID")

    complaint_id = db.Column(db.Integer, comment="申诉ID")
    stock_id = db.Column(db.Integer, comment="库存流水ID")
    trans_id = db.Column(db.Integer, comment="资产流水ID")
    session_id = db.Column(db.String(128), comment="订单会话ID")

    merchant_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="商家手续费率")
    merchant_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="商家手续费数量")
    cancel_pre_status = db.Column(db.StringEnum(Status), comment="订单取消前状态")
    finished_at = db.Column(db.MYSQL_DATETIME_6, comment="订单完成时间", index=True)
    admin_remark = db.Column(db.TEXT, comment="管理员备注")

    @property
    def from_to_base_amounts(self) -> tuple[Decimal, Decimal]:
        return self.calc_from_to_base_amounts(self.base_amount, self.merchant_fee_amount, self.side)

    @classmethod
    def calc_from_to_base_amounts(
            cls, order_amount: Decimal, mc_fee_amount: Decimal, side: P2pBusinessType,
    ) -> tuple[Decimal, Decimal]:
        if side == P2pBusinessType.BUY:
            # 用户买入，转入数目就是订单数
            to_amount = order_amount
            # 商家卖出，商家转出的数字货币中还要包括手续费
            from_amount = to_amount + mc_fee_amount
        else:
            # 用户卖出，转出数目就是订单数
            from_amount = order_amount
            # 商家买入，商家转入的数字货币里要先扣除手续费
            to_amount = from_amount - mc_fee_amount
        assert from_amount - to_amount == mc_fee_amount
        return from_amount, to_amount

    @property
    def merchant_base_amount(self):
        return self.base_amount + self.merchant_fee_amount if self.side == P2pBusinessType.BUY \
            else self.base_amount - self.merchant_fee_amount

    @property
    def buyer_id(self):
        return self.customer_id if self.side == P2pBusinessType.BUY else self.merchant_id

    @property
    def seller_id(self):
        return self.customer_id if self.side == P2pBusinessType.SELL else self.merchant_id

    def get_user_type(self, user_id) -> P2pOrderUserType:
        return P2pOrderUserType.CUSTOMER if user_id == self.customer_id else P2pOrderUserType.MERCHANT

    @classmethod
    def generate_order_id(cls):
        while True:
            new_id = today().strftime('%Y%m%d') + str(int(str(ObjectId()), 16))[-8:]
            if not cls.query.filter_by(order_id=new_id).first():
                return new_id
            cls.generate_order_id()

    def get_opponent_user_id(self, user_id):
        return self.customer_id if user_id == self.merchant_id else self.merchant_id

    @property
    def is_traded_status(self):
        if self.status in self.traded_statuses() or self.cancel_pre_status in self.traded_statuses():
            return True
        return False

    def format_order_side(self, user_id):
        return P2pBusinessType.reverse(self.side) if user_id == self.merchant_id else self.side

    @classmethod
    def traded_statuses(cls):
        return {cls.Status.CONFIRMED, cls.Status.PAID, cls.Status.TO_FINISH, cls.Status.FINISHED}

    @classmethod
    def cancel_count_types(cls):
        return [
            cls.CancelType.PAY_TIMEOUT,
            cls.CancelType.BUYER_CANCEL,
            cls.CancelType.SERVICE_CANCEL,
        ]

    @property
    def has_complaint(self):
        return bool(self.complaint_id)

    @classmethod
    def get_user_query(cls, user_id):
        return cls.query.filter(
            or_(cls.customer_id == user_id, cls.merchant_id == user_id),
        )

    @classmethod
    def get_two_user_query(cls, user_id, user_id2):
        return cls.query.filter(
            cls.customer_id.in_([user_id, user_id2]),
            cls.merchant_id.in_([user_id, user_id2]),
        )

    def is_seller(self, user_id):
        return user_id == self.seller_id

    @classmethod
    def get_user_buy_query(cls, user_id):
        return cls.query.filter(
            or_(
                and_(cls.customer_id == user_id, cls.side == P2pBusinessType.BUY),
                and_(cls.merchant_id == user_id, cls.side == P2pBusinessType.SELL),
            ),
            cls.status == cls.Status.FINISHED,
        )

    @classmethod
    def get_user_sell_query(cls, user_id):
        return cls.query.filter(
            or_(
                and_(cls.customer_id == user_id, cls.side == P2pBusinessType.SELL),
                and_(cls.merchant_id == user_id, cls.side == P2pBusinessType.BUY),
            ),
            cls.status == cls.Status.FINISHED,
        )
    
    @classmethod
    def get_real_id(cls, order_id):
        order = cls.query.filter(cls.order_id == order_id).with_entities(cls.id).first()
        if not order:
            raise InvalidArgument(message="p2p订单不存在，请检查订单ID")
        return order[0]
    
    @classmethod
    def get_show_id(cls, id_):
        return cls.query.filter(cls.id == id_).first().order_id
    
class P2pOrderStockHistory(ModelBase):
    """广告库存流水历史"""

    """
        扣减 -> 归还/完成
    """

    class Status(Enum):
        CREATE = "预锁定"
        LOCK = "已锁定"

        TO_FINISH = "预完成"
        FINISH = "已完成"  # 结束态

        TO_UNLOCK = "预解锁"
        UNLOCK = "已解锁"  # 结束状态

    order_id = db.Column(db.Integer, comment="订单ID", unique=True)
    adv_id = db.Column(db.String(64), nullable=False, comment="广告ID")
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="操作数量")
    status = db.Column(db.StringEnum(Status), nullable=False, comment="库存操作类型", index=True)

    @classmethod
    def to_fixup_status(cls):
        return {cls.Status.CREATE, cls.Status.TO_FINISH, cls.Status.TO_UNLOCK}


class P2pOrderTransHistory(ModelBase):
    class Status(Enum):
        CREATE = "预锁定"
        LOCK = "已锁定"  # 已锁定 -> 待预解冻扣款/解锁
        FAIL = "已失败"  # 终止态

        TO_UNLOCK_SUB = "预解冻扣款"
        UNLOCK_SUB = "已解冻扣款"  # 解锁+扣款原子操作 -> 待完成
        FINISH = "已完成"  # 加资产（终止态）

        TO_UNLOCK = "预解锁"  #
        UNLOCK = "已解锁"  # 终止态，取消订单使用

    order_id = db.Column(db.Integer, comment="订单ID", unique=True)
    asset = db.Column(db.String(32), nullable=False, index=True, comment="币种")
    from_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="发起方扣减数量")
    to_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="收款方接收数量")
    from_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, comment="发起方")
    to_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, comment="收款方")
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATE, comment="流水状态", index=True)
    lock_at = db.Column(db.MYSQL_DATETIME_6, comment="锁定时间")

    @classmethod
    def to_fixup_status(cls):
        return {cls.Status.CREATE, cls.Status.TO_UNLOCK_SUB, cls.Status.TO_UNLOCK, cls.Status.UNLOCK_SUB}


class P2pOrderFile(ModelBase):
    class FileType(Enum):
        PAY_CERT = '支付凭证'
        COMPLAINT = '申诉材料'

    order_id = db.Column(db.Integer, db.ForeignKey('p2p_order.id'), nullable=False)
    file_type = db.Column(db.StringEnum(FileType), nullable=False)
    file_key = db.Column(db.String(256), nullable=False)

    @classmethod
    def get_by_id(cls, file_id):
        return cls.query.get(file_id)


class P2pOrderEvent(ModelBase):
    class EventType(Enum):
        STATUS = "状态变更"  # 这个类型 value 为 订单status枚举的name

    """
    [
        {
            "event_type": EventType.STATUS.name,
            "op_type": "FINISHED",  # 对应 P2pOrder 的 Status 枚举的 name
            "extra": "{}",    # str
        },
    ]
    """

    order_id = db.Column(db.Integer, db.ForeignKey('p2p_order.id'), nullable=False, comment="订单ID")
    buyer_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, comment="买方ID")
    seller_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, comment="卖方ID")
    event_type = db.Column(db.StringEnum(EventType), nullable=False, comment="事件类型")
    value = db.Column(db.String(2048), nullable=False, comment="事件值")
    extra = db.Column(db.JSON, comment="额外信息")


class P2pOrderComplaint(ModelBase):
    """P2P 订单申诉表"""

    class Status(Enum):
        CREATED = "新工单"
        PENDING = "处理中"
        FINISHED = "已关闭"
        CANCELED = "申诉取消"

    class Reason(Enum):
        # 买方
        PAID_NOT_RELEASED = _("已付款，卖家未放币")
        PAID_ORDER_CANCEL = _("已付款，但订单已取消")
        OVER_PAID = _("转账金额大于订单金额")
        SELL_PAY_CHANNEL_ERROR = _("卖家收款方式问题")
        # 卖方
        NO_RECEIVED = _("未收到买家付款")
        AMOUNT_MISMATCH = _("转账金额与订单金额不一致")
        BUY_PAY_CHANNEL_ERROR = _("买方付款方式出现问题")
        # 其他
        OTHER = _("其他")

    __table_args__ = (
        db.Index("plaintiff_id_defendant_id_order_id", "plaintiff_id", "defendant_id", "order_id"),
        db.Index("idx_created_at", "created_at"),
        db.Index("idx_updated_at", "updated_at"),
    )

    order_id = db.Column(db.Integer, db.ForeignKey('p2p_order.id'), nullable=False, unique=True)
    plaintiff_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, comment="申诉用户ID")
    defendant_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, comment="被申诉用户ID", index=True)
    audit_id = db.Column(db.Integer, db.ForeignKey('user.id'), comment="审核人员ID")
    complaint_reason = db.Column(db.StringEnum(Reason), nullable=False, comment="申诉原因")
    complaint_status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    order_display_id = db.Column(db.String(32), nullable=False, comment="订单显示ID", unique=True)
    remark = db.Column(db.TEXT, nullable=False, default="", comment="客服备注")

    def opponent_id(self, user_id):
        return self.defendant_id if user_id == self.plaintiff_id else self.plaintiff_id

    @property
    def is_finish(self):
        return self.complaint_status in {self.Status.FINISHED, self.Status.CANCELED}

    @classmethod
    def get_user_query(cls, user_id):
        return cls.query.filter(or_(cls.plaintiff_id == user_id, cls.defendant_id == user_id))


class P2pOrderComplaintRecord(ModelBase):
    """P2P 订单申诉详情记录表 """

    __table_args__ = (
        db.Index("complaint_id_user_id", "complaint_id", "user_id"),
    )

    class UserType(Enum):
        PLAINTIFF = "申诉方"
        DEFENDANT = "被申诉方"
        AUDIT = "管理员"

    class NoticeType(Enum):
        DUPLEX = "双向"
        BROADCAST = "广播"

    class OpType(Enum):
        DESCRIBE = "描述"
        UPLOAD_IMAGE = "上传图片"
        UPLOAD_VIDEO = "上传视频"
        SEND_MSG = "发送消息"  # 审核人员操作
        PERMISSION = "权限操作"  # 审核人员操作
        CHANGE_STATUS = "状态变更"

    class AuditOperation(Enum):
        RELEASE_ASSET = "放币"
        CANCEL_ORDER = "取消订单"
        FREEZE_BUYER = "冻结买方P2P权限"
        FREEZE_SELLER = "冻结卖方p2p权限"
        DISABLED_BUYER_WITHDRAW = "禁止买方提现"
        DISABLED_SELLER_WITHDRAW = "禁止卖方提现"
        DISABLED_BUYER_TRADE = "买方交易限制"
        DISABLED_SELLER_TRADE = "卖方交易限制"

    class WinnerType(Enum):
        BUYER = "处理结果偏买家，已执行放币操作"
        SELLER = "处理结果偏卖家，已取消订单，并解冻相关资产"
        OTHER = "其它"

    """
    [
        {
            "op_type": "DESCRIBE",
            "op_detail": "描述内容",    # str
        },
        {
            "op_type": "UPLOAD_IMAGE",
            "op_detail": ["https://1332132.png"],   # list
        },
        {
            "op_type": "UPLOAD_VIDEO",
            "op_detail": ["https://1332132.mp4"],   # list
        },
        {
            "op_type": "SEND_MSG",
            "op_detail": "这是一条消息",  str
        },
        {
            "op_type": "PERMISSION",
            "op_detail": "RELEASE_ASSET", # 对应 AuditOperation 枚举的 name
        },
        {
            "op_type": "CHANGE_STATUS",
            "op_detail": "FINISH",  # 对应 Status 枚举的 name
            # 当 op_type=CHANGE_STATUS 且 op_detail=FINISH 才需要使用 extra，枚举的值看 Winner 枚举类的 name
            "extra": {"winner": "BUYER"} 
        },
    ]
    """

    complaint_id = db.Column(
        db.Integer, db.ForeignKey('p2p_order_complaint.id'), nullable=False, comment="申诉ID")

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, comment="用户ID")
    user_type = db.Column(db.StringEnum(UserType), nullable=False, comment="用户当前类型(申诉/被申诉/管理员)")
    notice_type = db.Column(db.StringEnum(NoticeType), nullable=False, default=NoticeType.DUPLEX, comment="通知类型")
    op_type = db.Column(db.StringEnum(OpType), nullable=False, comment="操作类型")
    op_detail = db.Column(db.String(1024), nullable=False, comment="操作详情")
    # 目前只会在 operation_type == SEND_MSG 使用
    to_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), comment="操作对手方")
    operation = db.Column(db.StringEnum(AuditOperation), comment="审核人员操作")
    # winner "申诉偏向结果"
    extra = db.Column(db.JSON, comment="额外信息")


class P2pOrderComplaintAnchor(ModelBase):

    ADMIN_ID = 0    # 代表所有客服

    """用户查看记录锚点"""
    complaint_id = db.Column(db.Integer, db.ForeignKey('p2p_order_complaint.id'), nullable=False, comment="申诉ID")
    user_id = db.Column(db.Integer, nullable=False, comment="用户ID", index=True)
    record_id = db.Column(db.Integer, comment="记录ID")
    max_record_id = db.Column(db.Integer, nullable=False, comment="最大记录ID")


class P2pComplaintReply(ModelBase):
    """p2p申诉话术"""

    reply_type = db.Column(db.String(256), nullable=False, comment="话术类型")
    title = db.Column(db.String(256), nullable=False, comment="话术场景")
    content = db.Column(db.TEXT, nullable=False, comment="内容")
    sort_id = db.Column(db.Integer, nullable=False, index=True, comment="排序ID")


class P2pSetting(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, index=True,
                       default=Status.VALID)


class P2pMerActSetting(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, index=True,
                       default=Status.VALID)


class P2pUserTradeSummary(ModelBase):
    """
    p2p用户交易基础统计
    - 用户统计单边
    - 商家统计双边
    """
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    deal_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=Decimal())  # 成交量 USDT
    deal_count = db.Column(db.Integer, default=0)  # 成交订单数
    confirmed_cancel_count = db.Column(db.Integer, default=0)  # 确认后取消 (买家超时未付款取消数量 + 买方手动取消)
    total_order_count = db.Column(db.Integer, default=0)  # 总订单数
    deal_sell_count = db.Column(db.Integer, default=0)  # 完成卖出订单数
    deal_buy_count = db.Column(db.Integer, default=0)  # 完成买入订单数
    # 待接单状态下接单总数
    total_release_time = db.Column(db.Integer, default=0)  # 总计放币时间
    total_payment_time = db.Column(db.Integer, default=0)  # 总计支付时间
    # 商家特殊字段
    merchant_cancel_count = db.Column(db.Integer, default=0)  # 商家取消订单数 (接单超时取消数量+商家拒绝数量)
    acceptance_count = db.Column(db.Integer, default=0)  # 商家接单总数

    @property
    def acceptance_rate(self):
        """接单率 = 待接单状态下接单总数/（待接单状态下接单总数+接单超时取消数量+商家拒绝数量）"""
        return quantize_amount(
            Decimal(self.acceptance_count) / Decimal(self.acceptance_count + self.merchant_cancel_count), 4) \
            if self.acceptance_count else Decimal()

    @property
    def completion_rate(self):
        """完单率 = 完成订单数/（完成订单数 + 买家超时未付款取消数量 + 买方手动取消）"""
        return quantize_amount(
            Decimal(self.deal_count) / Decimal(self.deal_count + self.confirmed_cancel_count), 4) \
            if self.deal_count else Decimal()

    @property
    def avg_payment_time(self):
        """付款的平均时间"""
        return int(self.total_payment_time / self.deal_buy_count) if self.deal_buy_count else 0

    @property
    def avg_release_time(self):
        """放币的平均时间"""
        return int(self.total_release_time / self.deal_sell_count) if self.deal_sell_count else 0


class P2pMerchantDailyTradeSummary(ModelBase):
    """
    p2p 用户每日数据统计
    - 主要是用来存储商家的交易数据
    """

    ONLINE_VALID_SECONDS = 15 * 60  # 有效天数判定时间间隔（s）

    __table_args__ = (
        db.UniqueConstraint(
            'user_id', 'report_date',
            name='user_id_report_date_unique'),
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    report_date = db.Column(db.Date, nullable=False, index=True)
    current_buy_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    sum_buy_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    current_sell_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    sum_sell_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    trade_amount_usd = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    online_time = db.Column(db.Integer, nullable=False, default=0)  # 在线时长（s）
    total_online_time = db.Column(db.Integer, nullable=False, default=0)  # 总在线时长（s）
    new_deal_customer_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')  # 新增交易对手
    all_deal_customer_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')  # 累计交易对手
    total_valid_days = db.Column(db.Integer, nullable=False, default=0)  # 总有效天数
    daily_merchant_cancel_count = db.Column(db.Integer, default=0)  # 每日商家取消订单数 (接单超时取消数量+商家拒绝数量)
    daily_acceptance_count = db.Column(db.Integer, default=0)  # 每日商家接单总数
    daily_deal_count = db.Column(db.Integer, default=0)  # 每日成交订单数
    total_deal_count = db.Column(db.Integer, default=0)  # 每日成交订单数
    daily_order_count = db.Column(db.Integer, default=0)  # 每日订单数
    total_order_count = db.Column(db.Integer, default=0)  # 每日订单数

    @classmethod
    def get_last_report_date(cls):
        """获取上一天的报告日期"""
        last_report_date = cls.query.with_entities(func.max(cls.report_date)).scalar()
        return last_report_date

    @property
    def current_is_valid_date(self):
        return self.online_time >= self.ONLINE_VALID_SECONDS

    @property
    def avg_online_time(self):
        """平均在线时长(累计在线时长/有效在线天数 当日的数据不统计)"""
        if not self.total_valid_days:
            return 0
        total_valid_days = self.total_valid_days - 1 if self.current_is_valid_date else self.total_valid_days
        return int((self.total_online_time - self.online_time) / total_valid_days) if total_valid_days else 0


class P2pOrderFeeHistory(ModelBase):
    """ p2p订单-手续费记录 """

    class Type(Enum):
        MERCHANT = "商家手续费"

    created_at = db.Column(db.MYSQL_DATETIME_6, default=now, index=True)
    order_id = db.Column(db.BigInteger, nullable=False, index=True)
    type = db.Column(db.StringEnum(Type), nullable=False, default=Type.MERCHANT)
    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class P2pFairPrice(ModelBase):
    """p2p站内法币公允价格"""

    class Platform(Enum):
        BINANCE = "币安"
        OKX = 'OKX'
        BITGET = 'Bitget'
        BYBIT = 'Bybit'
        COINEX = '站内成交均值'

    class Asset(Enum):
        USDT = 'USDT'
        BTC = 'BTC'
        ETH = 'ETH'

    fiat = db.Column(db.String(24), nullable=False, unique=True, comment="法币")
    asset = db.Column(db.StringEnum(Asset), default=Asset.USDT, nullable=False, comment="币种")
    price = db.Column(db.MYSQL_DECIMAL_26_8, default=0, nullable=False, comment="自动获取价格")
    source_data = db.Column(db.JSON, nullable=False, comment="原始数据")


class P2pFairPriceSnapshot(ModelBase):
    """p2p法币公允价格快照"""
    snap = db.Column(db.JSON, nullable=False)
    source_id = db.Column(db.Integer, nullable=False, index=True, comment="原数据的ID")
    fiat = db.Column(db.String(24), nullable=False, index=True, comment="法币")
    snap_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True, comment="原数据的更新时间")


class P2pFiatFairPrice(ModelBase):
    """p2p商家活动 公允价格 （数据来源：P2pFairPrice）"""

    # ！！！规则修改需要修改版本号，防止历史快照出错
    NOW_VERSION = 1
    VERSION_1_RULE = {
        "buy_range_left": "0",
        "buy_range_right": "0",
        "sell_range_left": "0",
        "sell_range_right": "0",
    }

    class PriceType(Enum):
        AUTO = '自动'
        MANUAL = '人工'

    fiat = db.Column(db.String(24), nullable=False, unique=True, comment="法币")
    asset = db.Column(db.StringEnum(P2pFairPrice.Asset), default=P2pFairPrice.Asset.USDT, nullable=False,
                      comment="币种")
    price = db.Column(db.MYSQL_DECIMAL_26_8, default=0, nullable=False, comment="自动获取价格")
    manual_price = db.Column(db.MYSQL_DECIMAL_26_8, default=0, nullable=False, comment="手动设置价格")
    price_type = db.Column(db.StringEnum(PriceType), default=PriceType.AUTO, nullable=False, comment="价格来源")
    rule = db.Column(db.JSON, default=VERSION_1_RULE, comment="规则")
    # 刷完数据再修改 version
    version = db.Column(db.Integer, nullable=False, default=NOW_VERSION, comment="规则版本")
    # price_range 是没有 version 时期使用的字段，后续请使用 rule 字段
    price_range = db.Column(db.MYSQL_DECIMAL_26_8, default=0, nullable=False, comment="有效区间")  # 已废弃
    source_data = db.Column(db.JSON, nullable=False, comment="原始数据")  # 已废弃

    def get_rule_price(self):
        base = Decimal("1")
        price = self.display_price()
        return {
            "buy_left_price": (base - Decimal(self.rule.get("buy_range_left", "0"))) * price,
            "buy_right_price": (base + Decimal(self.rule.get("buy_range_right", "0"))) * price,
            "sell_left_price": (base - Decimal(self.rule.get("sell_range_left", "0"))) * price,
            "sell_right_price": (base + Decimal(self.rule.get("sell_range_right", "0"))) * price,
        }

    def display_price(self):
        if self.price_type == self.PriceType.AUTO and self.price:
            return self.price
        else:
            return self.manual_price

    @classmethod
    def get_by_fiat(cls, fiat):
        return cls.query.filter(cls.fiat == fiat).first()


class P2pFiatFairPriceSnapshot(ModelBase):
    """p2p商家活动 法币公允价格快照"""
    snap = db.Column(db.JSON, nullable=False)
    source_id = db.Column(db.Integer, nullable=False, index=True, comment="原数据的ID")
    fiat = db.Column(db.String(24), nullable=False, index=True, comment="法币")
    snap_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True, comment="原数据的更新时间")

    @classmethod
    def load_rule(cls, snap: dict):
        version = snap.get("version")
        if not version:
            old_range = snap["price_range"]
            return {
                "buy_range_left": Decimal(old_range),
                "sell_range_right": Decimal(old_range),
            }
        elif version == 1:
            rule = snap["rule"]
            parse_rule = dict()
            for field, val in P2pFiatFairPrice.VERSION_1_RULE.items():
                parse_rule[field] = Decimal(rule.get(field, val))
            return parse_rule
        else:
            raise ValueError(f"Unknown version: {version}")


class P2pUserMargin(ModelBase):
    """p2p保证金"""

    GRACE_DAYS = 7  # 宽限期

    class MarginType(Enum):
        COUNTRY = "地区"
        PERSON = "个人"

    class GraceSource(Enum):
        EXIST = "存量"
        INCR = "增量"
        COUNTRY = "地区"
        PERSON = "个人"

    __table_args__ = (db.Index("idx_updated_at", "updated_at"),)

    user_id = db.Column(db.Integer, unique=True, comment="用户ID")
    require_margin = db.Column(db.MYSQL_DECIMAL_26_8, default=0, nullable=False, comment="应缴保证金")
    paid_margin = db.Column(db.MYSQL_DECIMAL_26_8, default=0, nullable=False, comment="实缴保证金")
    margin_type = db.Column(db.StringEnum(MarginType), default=MarginType.COUNTRY, nullable=False, comment="保证金类型")
    grace_deadline = db.Column(db.MYSQL_DATETIME_6, index=True, default=now, nullable=False, comment="宽限期到期时间")
    grace_source = db.Column(
        db.StringEnum(GraceSource), default=GraceSource.INCR, nullable=False, comment="宽限期来源")

    @property
    def margin_enough(self):
        return self.paid_margin >= self.require_margin

    @property
    def missing_margin(self):
        return max(self.require_margin - self.paid_margin, Decimal())

    def extend_deadline(self, source):
        # 只给原先保证金足够的商家延迟宽限期
        if self.margin_enough:
            new_deadline = dt_to_today(now()) + timedelta(days=P2pUserMargin.GRACE_DAYS)
            self.grace_deadline = max(new_deadline, self.grace_deadline)
        if self.paid_margin:
            self.grace_source = source

    @classmethod
    def get_user_row(cls, user_id):
        row = cls.query.filter(cls.user_id == user_id).first()
        return row

    @property
    def margin_pass(self):
        if self.paid_margin >= self.require_margin or (self.grace_deadline and now() < self.grace_deadline):
            return True
        return False


class P2pUserMarginHistory(ModelBase):
    """p2p商家保证金流水表"""

    class Status(Enum):
        PENDING = "待审核"
        PROCESS = "处理中"
        SUCCESS = "已完成"
        FAIL = "已取消"

    class BizType(Enum):
        MER_PAYMENT = "商家缴纳"
        MER_REFUND = "商家退还"  # 全额退还
        EXCESS_REFUND = "超额缴纳退回"
        MER_COMPENSATION = "用户赔付"
        MER_PENALTY = "商家违规扣除"

    class Asset(Enum):
        USDT = "USDT"

    __table_args__ = (db.Index("idx_created_at", "created_at"),)

    user_id = db.Column(db.Integer, index=True, nullable=False, comment="商家ID")
    to_user_id = db.Column(db.Integer, index=True, comment="接收用户ID")
    status = db.Column(db.StringEnum(Status), default=Status.PENDING, index=True, nullable=False, comment="状态")
    asset = db.Column(db.String(64), default=Asset.USDT.name, nullable=False, comment="币种")
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="划转数量")
    sys_his_id = db.Column(db.Integer, unique=True, comment="系统流水ID")
    balance = db.Column(db.MYSQL_DECIMAL_26_8, comment="当前余额")

    # 工单/审核相关
    audit_user_id = db.Column(db.Integer, comment="审核人ID")
    audit_at = db.Column(db.DateTime, comment="审核时间")

    # 业务补充字段
    biz_type = db.Column(db.StringEnum(BizType), index=True, nullable=False, comment="业务类型")
    p2p_order_id = db.Column(db.Integer, index=True, comment="关联的p2p订单ID")
    biz_remark = db.Column(db.String(512), default="", comment="业务说明")
    remark = db.Column(db.String(256), default="", comment="备注")

    @classmethod
    def refund_types(cls):
        return {cls.BizType.MER_REFUND, cls.BizType.EXCESS_REFUND}
    
    @classmethod
    def mer_op_types(cls):
        return {cls.BizType.MER_REFUND, cls.BizType.MER_PAYMENT}

    @classmethod
    def payment_types(cls):
        return {cls.BizType.MER_PAYMENT}

    @classmethod
    def deduct_types(cls):
        """获取划扣类型"""
        return {cls.BizType.MER_COMPENSATION, cls.BizType.MER_PENALTY}

    @classmethod
    def admin_biz_types(cls):
        return {cls.BizType.EXCESS_REFUND, cls.BizType.MER_COMPENSATION, cls.BizType.MER_PENALTY}


class P2pMarginHistory(ModelBase):
    """p2p系统保证金流水"""

    class Status(Enum):
        PROCESS = "划转中"
        SUCCESS = "划转成功"
        FAIL = "划转失败"

    class Type(Enum):
        PAYMENT = "缴纳保证金"
        REFUND = "返还保证金"

    class Asset(Enum):
        USDT = "USDT"

    __table_args__ = (db.Index("idx_created_at", "created_at"),)

    from_id = db.Column(db.Integer, index=True, comment="发送用户")
    to_id = db.Column(db.Integer, index=True, comment="接收用户")
    amount = db.Column(db.MYSQL_DECIMAL_26_8, comment="押金数量")
    asset = db.Column(db.String(64), default=Asset.USDT.name, comment="币种")
    status = db.Column(db.StringEnum(Status), index=True, default=Status.PROCESS, comment="划转状态")
    type = db.Column(db.StringEnum(Type), comment="流水类型")
    remark = db.Column(db.String(256), comment="备注")

    # 已废弃
    balance = db.Column(db.MYSQL_DECIMAL_26_8, comment="系统账户当前余额")


    @classmethod
    def get_user_query(cls, user_id):
        return cls.query.filter(or_(cls.from_id == user_id, cls.to_id == user_id))


class P2pMarginCountry(ModelBase):
    """p2p国家保证金配置"""

    class Status(Enum):
        VALID = "有效"
        INVALID = "无效"

    class Asset(Enum):
        USDT = "USDT"

    country_code = db.Column(db.String(3), nullable=False, unique=True, comment="国家代码")
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="保证金数量")
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID, comment="状态")
    asset = db.Column(db.StringEnum(Asset), nullable=False, default=Asset.USDT, comment="币种")

    @classmethod
    def get_country_margin(cls, country_code):
        row = cls.query.filter(cls.country_code == country_code).first()
        return row.amount if row else Decimal()


class P2pTPlusNRule(ModelBase):
    class Status(Enum):
        VALID = "valid"
        INVALID = "invalid"
        DELETED = "deleted"

    name = db.Column(db.String(128), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    limit_days = db.Column(db.Integer, nullable=False)  # 限制值
    group_condition = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default="")
    remark = db.Column(db.String(256))


class P2pUserTPlusNRecord(ModelBase):
    GLOBAL_SETTING_RULE_ID = 0
    GLOBAL_SETTING_RULE_NAME = '全局配置'

    USER_SETTING_RULE_ID = -1
    USER_SETTING_RULE_NAME = '个人配置'

    user_id = db.Column(db.Integer, nullable=False, index=True)  # 用户ID
    # 命中策略
    match_rules = db.Column(db.JSON, nullable=True, default=[])  # [P2pTPlusNRule.id]
    # 命中策略中的生效策略
    effect_rule = db.Column(db.Integer)  # P2pTPlusNRule.id
    # 策略生效的天数
    rules_effect_days = db.Column(db.Integer)
    # 用户配置的天数
    user_setting_days = db.Column(db.Integer)
    # 全局配置的天数
    global_setting_days = db.Column(db.Integer)
    # 实际生效的策略
    real_rule = db.Column(db.Integer, nullable=False)
    # 实际生效的天数
    real_days = db.Column(db.Integer, nullable=False)

    @classmethod
    def get_real_rule_id_and_days(cls, global_setting_days: int, user_setting_days: int, effect_rule_id: int,
                                  rules_effect_days: int):
        # 策略优先级：个人配置 > 策略命中 = 全局配置
        if user_setting_days is not None:
            return cls.USER_SETTING_RULE_ID, user_setting_days
        elif effect_rule_id:
            if rules_effect_days >= global_setting_days:
                return effect_rule_id, rules_effect_days
            else:
                return cls.GLOBAL_SETTING_RULE_ID, global_setting_days
        else:
            return cls.GLOBAL_SETTING_RULE_ID, global_setting_days


class P2pBlockRelation(ModelBase):
    __table_args__ = (db.UniqueConstraint('user_id', 'target_user_id', name='idx_user_id_tar_id'),)

    user_id = db.Column(db.Integer, nullable=False)  # 操作者ID
    target_user_id = db.Column(db.Integer, index=True, nullable=False)  # 被拉黑者ID


class P2pFollowRelation(ModelBase):
    __table_args__ = (db.UniqueConstraint('user_id', 'target_user_id', name='idx_user_id_tar_id'),)

    user_id = db.Column(db.Integer, nullable=False)  # 操作者ID
    target_user_id = db.Column(db.Integer, index=True, nullable=False)  # 被关注者ID
