# -*- coding: utf-8 -*-

from typing import List

from .base import Enum, ModelBase, db
from app.common.constants import Language


class Video(db.ModelBase):
    """用于视频管理"""
    class Usage(Enum):
        TUTORIAL = 'tutorial'   # 教程（用户教育）

    user_id = db.Column(db.Integer)
    file_id = db.Column(db.Integer, nullable=False)
    file_key = db.Column(db.String(128), nullable=False)
    name = db.Column(db.String(128), nullable=False)  # 视频名称
    usage = db.Column(db.StringEnum(Usage), nullable=True, default=None)


class VideoSubtitle(db.ModelBase):
    """视频字幕"""
    __table_args__ = (
        db.UniqueConstraint(
            'video_id', 'lang',
            name='vidio_subtitle_lang_unique'),
    )
    video_id = db.Column(db.Integer, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)  # 语言代码，如 'en_US', 'zh_Hans_CN'
    file_id = db.Column(db.Integer, nullable=False)
    file_key = db.Column(db.String(128), nullable=False)  # 字幕文件的存储键
    is_translated = db.Column(db.Boolean, nullable=False, default=False)  # 是否为自动生成的字幕

