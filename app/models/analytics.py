# -*- coding: utf-8 -*-

from .base import db, ModelBase


class PageVisitor(ModelBase):

    __table_args__ = (
        db.UniqueConstraint('scope', 'page', 'user_id', name='scope_page_user_id'),
        )

    scope = db.Column(db.String(64), nullable=False)
    page = db.Column(db.String(512), nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('user.id'), nullable=False)
    channel = db.Column(db.String(64), nullable=False)
