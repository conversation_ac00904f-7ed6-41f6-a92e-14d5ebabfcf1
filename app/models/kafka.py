from app.models import ModelBase, db


class ConsumerMaxOffset(ModelBase):

    __table_args__ = (db.UniqueConstraint("topic", "partition", "offset", name="topic_partition_offset_unique"),)

    topic = db.Column(db.String(64), nullable=False)
    partition = db.Column(db.Integer, nullable=False)
    offset = db.Column(db.BigInteger, nullable=False)

    @classmethod
    def query_max_offset_mapper(cls) -> dict:
        return {
            (row.topic, row.partition): row.offset for row in cls.query.all()
        }

    @classmethod
    def save_offset(cls, topic: str, partition: int, offset: int) -> None:
        row = cls.get_or_create(topic=topic, partition=partition)
        row.offset = offset
        db.session_add_and_flush(row)

    @classmethod
    def save_multiple_offset(cls, topic_partition_mapper: dict) -> None:
        for (topic, partition), offsets in topic_partition_mapper.items():
            row = cls.get_or_create(topic=topic, partition=partition)
            row.offset = max(offsets)
            db.session.add(row)
        db.session.flush()

    @classmethod
    def query_has_topic_row(cls, topics: list[str]) -> bool:
        return bool(cls.query.filter(cls.topic.in_(topics)).first())
