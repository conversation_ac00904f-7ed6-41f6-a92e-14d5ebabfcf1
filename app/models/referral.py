# -*- coding: utf-8 -*-
import json

from enum import Enum
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Set, Optional
from functools import cached_property

from flask_babel import gettext as _

from ..common import Language, TradeBusinessType
from ..utils import now, datetime_to_str
from .base import ModelBase, db, row_to_dict


class Referral(db.Model):

    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    code = db.Column(db.String(32), nullable=False, unique=True)
    rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 返佣给自己的比例
    is_default = db.Column(db.<PERSON>, nullable=False)
    remark = db.Column(db.String(256), nullable=False, default="")


class ReferralHistory(db.Model):

    __table_args__ = (
        db.UniqueConstraint('referrer_id', 'referree_id',
                            name='referrer_id_referree_id'),
    )

    class Status(Enum):
        VALID = 'valid'
        EXPIRED = 'expired'

    class ReferralType(Enum):   # 记录被推荐时推荐人的身份（推荐人身份可能日后会发生变化）
        AMBASSADOR = 'ambassador'   # 大使推荐
        NORMAL = 'normal'   # 普通推荐

    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)

    referral_id = db.Column(db.Integer, db.ForeignKey('referral.id'))
    referrer_id = db.Column(db.Integer, db.ForeignKey('user.id'))   # 邀请人
    referree_id = db.Column(db.Integer, db.ForeignKey('user.id'))   # 被邀请人
    effected_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)  # 生效时间
    status = db.Column(db.StringEnum(Status), default=Status.VALID)

    referral = db.relationship(
        'Referral', backref=db.backref('referral_histories', lazy='dynamic'),
        foreign_keys=[referral_id])
    referrer = db.relationship(
        'User', backref=db.backref('referrer_histories', lazy='dynamic'),
        foreign_keys=[referrer_id])
    referree = db.relationship(
        'User', backref=db.backref('referree_histories', lazy='dynamic'),
        foreign_keys=[referree_id])
    referral_type = db.Column(db.StringEnum(ReferralType), nullable=True)

    def to_dict(self, *, with_hook: bool = True, enum_to_name: bool = False):
        return row_to_dict(self, with_hook=with_hook, enum_to_name=enum_to_name)


class ReferralCode(db.Model):

    MIN_POOL_SIZE = 1000

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(32), nullable=False, unique=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID,
                       index=True)


class ReferralPicture(ModelBase):
    NOW_VERSION = 2

    class Status(Enum):
        VALID = '生效中'
        DELETED = '已删除'

    Language = Language

    file_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    version = db.Column(db.Integer, nullable=False, default=NOW_VERSION, comment="版本号")

    file = db.relationship(
        'File', backref=db.backref('referral_pictures', lazy='dynamic'),
        foreign_keys=[file_id])


class ReferralCopyWriting(ModelBase):
    """ 推荐文案表 """

    class Status(Enum):
        VALID = "生效中"
        DELETED = "已删除"

    rank = db.Column(db.Integer, nullable=False)
    content = db.Column(db.String(256), nullable=False)  # duplicate with ZH_HANS_CN
    remark = db.Column(db.String(256), nullable=False, default="")
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


class ReferralCopyWritingTranslation(ModelBase):
    """ 推荐文案多语言表 """

    # 当前admin中设置文案支持的语言
    LANGUAGES = [Language.EN_US, Language.ZH_HANT_HK, Language.ZH_HANS_CN,
                 Language.JA_JP, Language.RU_KZ, Language.KO_KP,
                 Language.ID_ID, Language.ES_ES, Language.FA_IR,
                 Language.TR_TR, Language.VI_VN, Language.AR_AE,
                 Language.FR_FR, Language.DE_DE, Language.PT_PT, Language.TH_TH]

    class Status(Enum):
        VALID = "生效中"
        DELETED = "已删除"

    copy_writing_id = db.Column(db.Integer, db.ForeignKey("referral_copy_writing.id"), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    content = db.Column(db.String(256), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


class ReferralRate(ModelBase):

    class Status(Enum):
        VALID = '生效中'
        DELETED = '已删除'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    business = db.Column(db.Enum(TradeBusinessType), nullable=False)
    rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    expired_at = db.Column(db.MYSQL_DATETIME_6) # NULL为永不过期
    remark = db.Column(db.String(512))
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    @classmethod
    def get_user_rate(cls, user_id: int) -> Dict[TradeBusinessType, Decimal]:
        rows = cls.query.filter(cls.user_id == user_id, cls.status == cls.Status.VALID).all()
        result = {}
        for row in rows:
            if row.expired_at is None or row.expired_at > now():
                result[row.business] = row.rate
        return result

    @classmethod
    def batch_get_user_rate(cls, user_ids: list[int]) -> Dict[int, Dict[TradeBusinessType, Decimal]]:
        rows = cls.query.filter(
            cls.user_id.in_(user_ids),
            cls.status == cls.Status.VALID,
        ).all()
        result = {}
        for row in rows:
            if row.expired_at is None or row.expired_at > now():
                if row.user_id not in result:
                    result[row.user_id] = {}
                result[row.user_id][row.business] = row.rate
        return result

    @property
    def record_detail(self) -> str:
        biz = {TradeBusinessType.SPOT: '现货', TradeBusinessType.PERPETUAL: '合约'}.get(self.business)
        rate = f'{(self.rate * 100).normalize():{"f"}}%'
        expired_at = '永不过期'
        if self.expired_at:
            expired_at = datetime_to_str(self.expired_at, offset_minutes=60 * 8)
        return f'{biz}返佣比例为{rate}，到期时间为{expired_at}'


class SpecialReferreeUserRate(ModelBase):
    """ 被邀请人的特殊返佣比例 """

    BUS_AMB_THRESHOLD_RATE = Decimal('0.5')  # 商务大使：如果邀请人（A账号）返佣比例＞50%，且B被识别为API交易，把B对A的返佣比例改为50%
    NORMAL_THRESHOLD_RATE = Decimal('0.45')  # 平台大使｜普通用户

    class Status(Enum):
        VALID = '生效中'
        INVALID = '未生效'

    class ReferrerType(Enum):
        # 邀请人类型
        AMB = '平台大使'
        BUS_AMB = '商务大使'
        NORMAL = '普通用户'

    referree_id = db.Column(db.Integer, nullable=False, unique=True)   # 被邀请人
    referrer_id = db.Column(db.Integer, nullable=False, index=True)   # 邀请人
    referral_id = db.Column(db.Integer, nullable=False, index=True)   # 邀请码ID，冗余
    referrer_type = db.Column(db.StringEnum(ReferrerType), nullable=False, index=True)
    trigger_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    # 邀请人原始的返佣比例，冗余，不用做实际返佣（如果现货跟合约的不一样，就保存更高的那个）
    # 对于商务大使，origin_referrer_rate是商务大使+商务代理的总返佣比例
    origin_referrer_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, index=True)
    # 被邀请人的返佣比例，和邀请人的返佣比例取min后 再用做实际返佣，且只用于当前的referree_id -> referrer_id
    # 对于商务大使，rate是商务大使+商务代理的总返佣比例
    rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    rate_manual_edit_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)  # Admin手动设置rate的时间
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID, index=True)
    remark = db.Column(db.String(512), nullable=False, default='')


class AmbassadorApplication(ModelBase):
    """大使(代理)申请"""

    class Type(Enum):
        AMBASSADOR = "大使"
        AMBASSADOR_AGENT = "大使代理"  # 待删除不再使用

    class CommunityScale(Enum):
        BELOW_200 = "200人以下"
        BETWEEN_200_500 = "200-500人"
        BETWEEN_500_1000 = "500-1000人"
        ABOVE_1000 = "1000人以上"

    class Reason(Enum):
        INTEREST = "我有时间、乐于奉献、热爱数字货币与区块链"
        KOL = "我是KOL或资深大V"
        LEADER = "我是社区群主，拥有群资源"
        OTHER = "其他"

    class LastInvalidReason(Enum):
        # 上次失效原因
        SYSTEM = "自动失效"
        MANUAL = "手动失效"
        NOT_AMB = "未成为过大使"

    class Status(Enum):
        CREATED = 'created'
        AUDITED = 'audited'
        REJECTED = 'rejected'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    type = db.Column(db.Enum(Type), nullable=False)  # 待删除不再使用，删除前需刷数据
    name = db.Column(db.String(64), nullable=False)
    head = db.Column(db.String(256))
    mobile_num = db.Column(db.String(64), nullable=False)
    city = db.Column(db.String(256), nullable=False)
    profession = db.Column(db.String(256), nullable=False)
    proficient_langs = db.Column(db.String(256), nullable=False)
    community_scale = db.Column(db.Enum(CommunityScale), nullable=False)
    reason = db.Column(db.Enum(Reason), nullable=False)
    plan = db.Column(db.Text, nullable=False)
    plan_detail = db.Column(db.Text, nullable=False)
    referral_code = db.Column(db.String(32))
    wechat = db.Column(db.String(64))
    telegram = db.Column(db.String(64))

    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)
    last_invalid_reason = db.Column(db.StringEnum(LastInvalidReason), nullable=True)


class AmbassadorGuide(ModelBase):
    """ 大使攻略 """

    class Status(Enum):
        VALID = "生效中"
        DELETED = "已删除"

    title = db.Column(db.String(256), nullable=False)  # duplicate with ZH_HANS_CN
    rank = db.Column(db.Integer, nullable=False)
    remark = db.Column(db.String(256), nullable=False, default="")
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


class AmbassadorGuideTranslation(ModelBase):
    """ 大使攻略多语言表 """

    __table_args__ = (db.UniqueConstraint("guide_id", "lang", name="ambassador_guide_lang_uniq"),)

    AVAILABLE_LANGUAGES = [
        Language.EN_US,
        Language.ZH_HANS_CN,
        Language.ZH_HANT_HK,
        Language.JA_JP,
        Language.RU_KZ,
        Language.KO_KP,
        Language.ID_ID,
        Language.ES_ES,
        Language.FA_IR,
        Language.TR_TR,
        Language.VI_VN,
        Language.AR_AE,
        Language.FR_FR,
    ]

    class Status(Enum):
        VALID = "生效中"
        DELETED = "已删除"

    guide_id = db.Column(db.Integer, db.ForeignKey("ambassador_guide.id"), nullable=False)
    lang = db.Column(db.String(32), nullable=False)  # str, Language_enum.name
    title = db.Column(db.String(256), nullable=False)
    url = db.Column(db.String(256), nullable=False, default="")
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


class Ambassador(ModelBase):
    """大使"""

    class Status(Enum):
        VALID = '正常'
        DELETED = '关闭'

    class Level(Enum):
        SILVER = 'Silver'
        GOLD = 'Gold'
        DIAMOND = 'Diamond'

    class Type(Enum):
        NORMAL = "普通大使"  # 老数据都是这个类型
        BUSINESS = "商务大使"  # 已废弃，兼容保留，后续再删除该枚举。商务大使在新表中

    class Source(Enum):
        NORMAL = "自然申请"
        AMBASSADOR = "大使推荐"
        BUSINESS = "商务推荐"
        VIABTC_POOL = "矿池大使"

    LEVEL_NAMES = {
        Level.SILVER: _('白银'),
        Level.GOLD: _('黄金'),
        Level.DIAMOND:  _('钻石')
    }

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), unique=True)
    remark = db.Column(db.String(128))
    # 考核等级
    appraisal_level = db.Column(db.Enum(Level))
    status = db.Column(db.Enum(Status), default=Status.VALID)
    # 保险等级
    lock_level = db.Column(db.Enum(Level))
    # 保险等级过期时间
    expired_time = db.Column(db.MYSQL_DATETIME_6)
    type = db.Column(db.StringEnum(Type), nullable=False)
    source = db.Column(db.StringEnum(Source), nullable=False, default=Source.NORMAL)
    # 大使(首次｜再次)生效时间, effected_at之前邀请的用户都走普通返佣
    effected_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    continent = db.Column(db.String(32), nullable=True)  # Continent.name

    user = db.relationship(
        'User', backref=db.backref('ambassador', lazy='dynamic'))

    @property
    def level(self):
        if self.expired_time and self.expired_time > now():
            level_list = [i for i in self.Level]
            # 取考核和保底中最大的
            _idx = max([level_list.index(self.lock_level),
                        level_list.index(self.appraisal_level)])
            return level_list[_idx]
        return self.appraisal_level

    @property
    def is_from_viabtc_pool(self) -> bool:
        return self.source == self.Source.VIABTC_POOL


class AmbassadorAgent(ModelBase):
    """大使代理"""

    class Status(Enum):
        VALID = '正常'
        DELETED = '关闭'

    class ActiveStatus(Enum):
        ACTIVE = '生效中'
        INACTIVE = '已失效'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), unique=True)
    business_user_id = db.Column(db.Integer, db.ForeignKey("user.id"))  # 推荐商务用户ID，可为空。待删除不再使用
    business_user_rate = db.Column(db.MYSQL_DECIMAL_26_8)  # 推荐商务用户的分成比例。待删除不再使用
    remark = db.Column(db.String(128))
    referral_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 是否考核
    is_appraisal = db.Column(db.Boolean, default=True)
    # 是否生效
    active_status = db.Column(db.Enum(ActiveStatus), default=ActiveStatus.ACTIVE)  # 待删除不再使用
    status = db.Column(db.Enum(Status), default=Status.VALID)
    effected_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, default=now)
    user = db.relationship(
        "User",
        backref=db.backref("ambassador_agent", lazy="dynamic"),
        foreign_keys=[user_id],
    )


class AmbassadorAgentHistory(ModelBase):
    """大使代理关系"""
    __table_args__ = (
        db.UniqueConstraint('user_id', 'ambassador_id',
                            name='user_id_ambassador_id'),
    )

    class Status(Enum):
        VALID = '正常'
        DELETED = '关闭'  # 失效

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # 大使代理
    ambassador_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    effected_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, default=now)

    status = db.Column(db.Enum(Status), default=Status.VALID)


class AmbassadorStar(ModelBase):
    """大使风采/优秀大使"""

    class Continent(Enum):
        ASIA = '亚洲'
        AFRICA = '非洲'
        EUROPE = '欧洲'
        SOUTH_AMERICA = '南美洲'
        NORTH_AMERICA = '北美洲'
        OCEANIA = '大洋洲'

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    name = db.Column(db.String(32), nullable=False, default='')
    intro_en = db.Column(db.Text, nullable=False, default='')
    intro_cn = db.Column(db.Text, nullable=False, default='')
    intro_tc = db.Column(db.Text, nullable=False, default='')
    file_id = db.Column(db.Integer, db.ForeignKey("file.id"))
    sort_id = db.Column(db.Integer, nullable=False)
    continent = db.Column(db.String(32), nullable=True)  # Continent.name
    country = db.Column(db.String(16), nullable=True)

    user = db.relationship(
        'User', backref=db.backref('ambassador_star', lazy='dynamic'))


class ReferralAssetHistory(ModelBase):
    """返佣发放记录，该表对应发放流水"""

    class Type(Enum):
        REFERRAL = 'referral'
        AMBASSADOR = 'ambassador'
        AMBASSADOR_AGENT = 'ambassador_agent'

    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'

    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    type = db.Column(db.Enum(Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)  # 老数据为null
    perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)  # 老数据为null
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)


class ReferralCodeAssetDetail(ModelBase):
    """ 用户邀请码的返佣发放, user_id和referral_id纬度的聚合 """

    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    referral_id = db.Column(db.Integer, db.ForeignKey('referral.id'))
    type = db.Column(db.Enum(ReferralAssetHistory.Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class ReferralAssetDetail(ModelBase):
    """ 返佣发放明细, 包含返佣和大使返佣 """

    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    referree_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    referral_id = db.Column(db.Integer, db.ForeignKey('referral.id'))
    type = db.Column(db.Enum(ReferralAssetHistory.Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class AmbassadorAgentAssetDetail(ModelBase):
    """大使代理返佣发放明细"""

    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'

    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))  # 代理user_id 或 分成的user_id（商务、团队、老逻辑的分成用户）
    agent_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    ambassador_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class ReferralAssetSummary(ModelBase):
    """用户返佣累计"""
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    referree_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    referral_id = db.Column(db.Integer, db.ForeignKey('referral.id'))
    type = db.Column(db.StringEnum(ReferralAssetHistory.Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    summary_date = db.Column(db.Date, nullable=False, index=True)


class DailyUserReferralSlice(ModelBase):
    """用户每日返佣数据"""
    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    # NULL为该用户邀请数据汇总
    referral_id = db.Column(db.Integer, db.ForeignKey('referral.id'))
    referral_count = db.Column(db.Integer, nullable=False)
    amount_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class DailyUserReferralTypeSlice(ModelBase):
    """用户每日返佣数据(user_id + referral_id + type)"""
    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    type = db.Column(db.StringEnum(ReferralAssetHistory.Type), nullable=False)
    # NULL为该用户 type 邀请数据汇总
    referral_id = db.Column(db.Integer, db.ForeignKey('referral.id'))
    referral_count = db.Column(db.Integer, nullable=False)
    # 大使单位 USDT，普通单位 CET
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    amount_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class AmbassadorStatistics(ModelBase):
    """ 大使纬度的统计信息 """

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))  # 大使user_id
    refer_count = db.Column(db.Integer, nullable=False, default=0)  # 历史总邀请人数
    refer_total_deal_count = db.Column(db.Integer, nullable=False, default=0)  # 历史总邀请人数中交易过的人数
    refer_deal_count = db.Column(db.Integer, nullable=False, default=0)  # 当前VALID状态的邀请人数中交易过的人数
    cur_valid_refer_count = db.Column(db.Integer, nullable=False, default=0)  # 当前VALID状态的邀请人数
    last_update_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 每天更新一次


class PotentialAmbassador(ModelBase):
    """"高潜大使线索库"""
    CHANGE_STATUS_DAYS = 7

    class Source(Enum):
        INVALID_AMBASSADOR = "失效大使"
        REJECTED_AMBASSADOR = "审核不通过大使"
        POTENTIAL_USER = "普通推荐高潜用户"
        QUALITY_USER = "大客户"

    class Status(Enum):
        CREATED = "待转化"
        PUSH = "平台转化"           # 通过PUSH触达后，用户自行成为大使
        BUSINESS = "商务转化"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), unique=True)
    ambassador_id = db.Column(db.Integer, db.ForeignKey('ambassador.id'))
    source = db.Column(db.StringEnum(Source), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class AmbassadorBusinessTrace(ModelBase):
    """商务线索库"""

    class Status(Enum):
        CREATED = "待分配"
        PROGRESS = "跟进中"
        PUSH = "转化为平台大使"      # 通过PUSH触达后，用户自行成为大使
        BUSINESS = "转化为商务大使"  # 通过商务跟进成为大使，会记录在”商务大使列表“中
        FAILED = "未转化"

    potential_id = db.Column(db.Integer, db.ForeignKey('potential_ambassador.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    mobile = db.Column(db.String(64))  # 大使申请的手机
    telegram = db.Column(db.String(64))  # 大使申请的电报号
    status = db.Column(db.StringEnum(Status), default=Status.CREATED)
    trace_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # 跟进人
    remark = db.Column(db.String(256))


class BusinessTraceChangeHistory(ModelBase):
    """商务线索库更改记录"""
    class Type(Enum):
        USER = "跟进人"
        STATUS = "跟进状态"
        REMARK = "备注"

    business_trade_id = db.Column(db.Integer, db.ForeignKey('ambassador_business_trace.id'))
    type = db.Column(db.StringEnum(Type), nullable=False)
    value = db.Column(db.String(256))


class BusAmbType(Enum):
    NORMAL = "普通商务大使"
    BROKER = "Broker商务大使"  # 同时是broker和商务大使，返佣逻辑和普通商务大使一致，额外多了作为Broker身份再向上(给商务团队)返佣的逻辑


class BusinessAmbassadorAudit(ModelBase):
    """ 商务大使审核记录 """

    class Type(Enum):
        ADD_AMB = "新增商务大使"
        EDIT_AMB_STATUS = "修改状态"
        EDIT_RATE = "修改返佣比例"  # 修改自己的比例 or 关联商务代理的比例
        EDIT_BUS_USER = "修改商务关系"
        EDIT_DELAY_REPAY_MONTH = "修改预付金还款时间"
        RESTORE_INVALID_REFERRAL = "恢复失效邀请关系"

    class Status(Enum):
        CREATED = '待初审'
        FIRST_PASSED = '待复审'
        PASSED = '已审核'
        REJECTED = '已拒绝'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 操作的商务大使id
    bus_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)  # 操作的商务大使所属的商务
    amb_type = db.Column(db.StringEnum(BusAmbType), nullable=False)  # 商务大使类型，用于筛选
    type = db.Column(db.StringEnum(Type), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    creator = db.Column(db.Integer, nullable=False)
    first_auditor = db.Column(db.Integer)  # 初审人
    first_audited_at = db.Column(db.MYSQL_DATETIME_6)  # 初审时间
    auditor = db.Column(db.Integer)  # 复审人
    audited_at = db.Column(db.MYSQL_DATETIME_6)  # 复审时间
    remark = db.Column(db.String(1024), nullable=False, default="")  # 提交审核时的备注
    first_comment = db.Column(db.String(1024), nullable=False, default="")  # 初审意见
    comment = db.Column(db.String(1024), nullable=False, default="")  # 复审意见
    old_data = db.Column(db.Text, nullable=False, default="")
    new_data = db.Column(db.Text, nullable=False, default="")
    file_keys = db.Column(db.JSON, default=[], comment="图片说明列表")


class BusinessAmbassador(ModelBase):
    """商务大使 (每个商务大使都默认是商务代理) """

    Type = BusAmbType

    class Status(Enum):
        VALID = '正常'
        DELETED = '关闭'

    class Source(Enum):
        BUSINESS_EXPANSION = "商务拓展"
        NORMAL_AMB_RECALL = "平台大使召回"

    class CheckStatus(Enum):
        NOT_CHECK = "未检查"
        CHECK_NOT_PASSED = "检查不通过"  # 预淘汰状态
        CHECK_PASSED = "检查通过"  # 非预淘汰状态

    class DeleteType(Enum):
        MANUAL = "手动关闭"
        SYSTEM = "自动淘汰"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    type = db.Column(db.StringEnum(Type), nullable=False)
    source = db.Column(db.StringEnum(Source), nullable=False)
    check_status = db.Column(db.StringEnum(CheckStatus), nullable=False, default=CheckStatus.NOT_CHECK)  # 预淘汰检查状态
    # 该商务大使自己的返佣比例（邀请码设置了分成，则和被邀请人再分这个比例）
    rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 该商务大使 给 团队的返佣系数
    team_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 邀请关系早于effected_at的按普通返佣
    effected_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 最近一次成为商务大使（或把状态改为正常）的时间
    bus_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)  # 该商务大使是哪个商务邀请进来的
    # 该商务大使 和 它所属商务bus_user_id 绑定关系的时间，如果当月有关系变更，下月1号不向上给商务、商务组长返佣(本月的返佣)
    bind_bus_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    # 开始给商务、商务组长返佣的日期，修改商务后会改为下月1号
    bus_refer_start_at = db.Column(db.Date, nullable=True)
    mobile_num = db.Column(db.String(64), nullable=False, default="")
    delay_repay_month = db.Column(db.Integer, nullable=False, default=0)    # 预付金延迟还款月份，成为大使经过该月份后才开始还款
    remark = db.Column(db.String(256), nullable=False, default="")
    delete_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)  # 关闭时间
    delete_type = db.Column(db.StringEnum(DeleteType), nullable=True)  # 关闭类型

    @property
    def is_broker_amb(self) -> bool:
        return self.type == self.Type.BROKER

    @property
    def is_valid(self) -> bool:
        return self.status == self.Status.VALID

    @property
    def repay_datetime(self) -> datetime:
        return self.effected_at + timedelta(days=30 * self.delay_repay_month)

    @classmethod
    def calc_percent(cls, amb_rate: Decimal, share_rates: list[Decimal]) -> Decimal:
        """ 计算商务大使-返佣比例/总比例 的百分比 """
        total_rate = amb_rate + sum(share_rates)
        p = amb_rate / total_rate if total_rate else Decimal()
        return p

    @property
    def total_ref_rate(self) -> Decimal:
        """ 返回商务大使+商务代理的总比例 """
        shares = BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.bus_amb_id == self.user_id,
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID
        ).all()
        return self.rate + sum([i.rate for i in shares])


class BusinessAmbassadorShareUser(ModelBase):
    """商务大使的返佣分成用户
    商务大使的返佣比例 + 关联的所有商务代理的返佣比例，范围在：[0，100%)，注：商务代理 指 其他商务大使
    0 <= BusinessAmbassador.rate + SUM(BusinessAmbassadorShareUser.rate) < 1
    """

    __table_args__ = (
        db.UniqueConstraint('bus_amb_id', 'target_user_id', name='bus_amb_target_user_unique'),
    )

    class Status(Enum):
        VALID = '正常'
        DELETED = '删除'

    bus_amb_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)   # 商务大使
    target_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 向该用户分成返佣，也是某一个商务大使
    rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 商务大使bus_amb_id 给 target_user_id 的返佣比例
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    effected_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, default=now)


class BusinessUser(ModelBase):
    """商务，是商务大使的上级"""

    class Status(Enum):
        VALID = "正常"
        DELETED = "关闭"

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, unique=True)  # 商务用户ID
    team_id = db.Column(db.Integer, db.ForeignKey("business_team.id"), nullable=False)  # 团队ID, 必填
    leader_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 该商务 给 商务团队组长 的分成比例
    pr_rates = db.Column(db.MYSQL_JSON, default=[], nullable=False)  # 该商务 给 团队PR  的分成比例， [ {user_id: 123, rate: 0.1} ]
    effected_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, default=now)  # 最近一次成为商务（或把状态改为正常）的时间
    # 该商务 和 它所属团队 绑定关系的时间，如果当月有关系变更，下月1号不向上给商务组长返佣(本月的返佣)
    bind_team_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    # 开始给商务团队组长返佣的日期，修改团队后会改为下月1号
    leader_refer_start_at = db.Column(db.Date, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(256), nullable=False, default="")

    @property
    def total_leader_rate(self) -> Decimal:
        # 组长 + PR 的总分成比例
        return self.leader_rate + sum([Decimal(i['rate']) for i in self.pr_rates])


class BusinessTeam(ModelBase):
    """商务团队，商务创建时要选择某个团队，商务组长可以是某个商务，团队可以配置多个PR
    可以把PR理解成【另一种类型】的组长，一起瓜分这个团队的【大使和Broker】带来的返佣
    商务配置的（组长分成比例+所有PR分成比例）的范围在[0,1]
    """

    class Status(Enum):
        VALID = "正常"
        DELETED = "删除"

    name = db.Column(db.String(64), nullable=False, default="")  # 团队名
    leader_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # 团队组长user_id，可修改
    pr_user_ids = db.Column(db.MYSQL_JSON, default=[], nullable=False)  # 商务团队配置的返佣分成PR用户
    # 该团队 设置 组长 的时间，如果当月组长发生变更，下月1号不给商务组长返佣(本月的返佣)
    set_leader_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    # 开始给组长返佣的日期，修改组长后会改为下月1号
    leader_refer_start_at = db.Column(db.Date, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(256), nullable=False, default="")


class BusRelationChangelog(ModelBase):
    """ 商务相关-变更记录 """

    class ChangeType(Enum):
        EDIT_TEAM_LEADER = "修改团队组长"
        EDIT_TEAM_PR = "修改团队统筹账号"
        EDIT_BUS_USER_TEAM = "修改商务所属团队"
        EDIT_BUS_USER_LEADER_RATE = "调整商务的组长分成比例"
        EDIT_BUS_USER_PR_RATE = "调整商务的统筹账号分成比例"
        EDIT_BUS_USER_STATUS = "调整商务状态"

    biz_id = db.Column(db.Integer, nullable=False, index=True)  # 业务ID（team_id、user_id）
    admin_user_id = db.Column(db.Integer, index=True)
    change_type = db.Column(db.StringEnum(ChangeType), nullable=False)
    detail = db.Column(db.MYSQL_JSON, nullable=False)

    @classmethod
    def add(
        cls,
        biz_id: int,
        admin_user_id: Optional[int],
        change_type: 'BusRelationChangelog.ChangeType',
        old_value,
        new_value,
        is_commit: bool = False,
    ) -> 'BusRelationChangelog':
        # change_type, old_value, new_value: 枚举.name
        detail = {
            "old": old_value,
            "new": new_value,
        }
        log = cls(
            biz_id=biz_id,
            admin_user_id=admin_user_id,
            change_type=change_type,
            detail=detail,
        )
        db.session.add(log)
        if is_commit:
            db.session.commit()
        return log


class BusinessAmbassadorLoanApply(ModelBase):
    """商务大使-预付金申请｜借币申请
    扣除提交商务bus_id的资产，加给商务大使bus_amb_id
    """

    class Status(Enum):
        CREATED = '待审核'
        REJECTED = '已拒绝'
        AUDITED = '已审核(待划转)'  # TransferStatus.CREATED
        FINISHED = '已审核'  # TransferStatus.FINISHED

    class TransferStatus(Enum):
        CREATED = "created"  # 待扣减商务bus_id的资产
        DEDUCTED = "deducted"  # 已扣商务bus_id的资产
        FINISHED = "finished"  # 商务大使bus_amb_id资产已增加
        FAILED = "failed"

    bus_amb_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 要借币的商务大使用户id
    bus_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 商务用户id
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 借币数目
    creator = db.Column(db.Integer, nullable=False)  # 创建人
    auditor = db.Column(db.Integer)  # 审核人
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    reason = db.Column(db.String(256), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    transfer_status = db.Column(db.StringEnum(TransferStatus), nullable=True)


class BusinessAmbassadorLoan(ModelBase):
    """商务大使-预付金记录｜借币记录"""

    class Status(Enum):
        ARREARS = "欠款中"
        FINISHED = '已还清'

    apply_id = db.Column(db.Integer, db.ForeignKey('business_ambassador_loan_apply.id'), nullable=False, unique=True)
    bus_amb_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 要借币的商务大使用户id
    bus_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 商务用户id（借出）
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 借币数目
    repay_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 已还数目
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.ARREARS)

    @property
    def unflat_amount(self) -> Decimal:
        return self.amount - self.repay_amount


class BusinessAmbassadorRepayHistory(ModelBase):
    """商务大使还款-还币历史"""

    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'
        FAILED = 'failed'  # 扣减失败

    date = db.Column(db.Date, nullable=False, index=True)
    loan_id = db.Column(db.Integer, db.ForeignKey("business_ambassador_loan_apply.id"), nullable=False)
    bus_amb_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # 还币的商务大使用户id
    bus_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 商务用户id，本条还款转入的人
    asset = db.Column(db.String(32), nullable=False, index=True)  # 还款币种
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 还款数目
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class BusinessAgentReferralAssetHistory(ModelBase):
    """商务代理的返佣发放记录，该表对应发放流水，每个商务代理每天一次"""

    class Status(Enum):
        CREATED = 'created'
        DEDUCTED = 'deducted'
        FINISHED = 'finished'

    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # spot_amount + perpetual_amount
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class BusinessUserShareRateSnapshot(ModelBase):
    """商务用户 和 商务团队 分成比例快照
    和 BusinessReferralAssetDetail 一起使用，确定团队内 商务、组长、PR 各自的返佣分成
    """

    date = db.Column(db.Date, nullable=False, index=True)
    bus_user_id = db.Column(db.Integer, nullable=False, index=True)
    team_id = db.Column(db.Integer, nullable=False, index=True)  # 团队ID
    leader_id = db.Column(db.Integer, nullable=False)  # 团队组长user_id
    # 商务的分成比例 = 1 - leader_rate - sum(pr_rates)
    leader_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 商务 给 团队组长 的分成比例
    pr_rates = db.Column(db.MYSQL_JSON, default=[], nullable=False)  # 商务 给 团队PR 的分成用户

    @property
    def total_leader_rate(self) -> Decimal:
        # 组长 + PR 的总分成比例
        return self.leader_rate + sum([Decimal(i['rate']) for i in self.pr_rates])


class BusinessUserReferralAssetHistory(ModelBase):
    """商务团队的返佣发放记录，该表对应发放流水，每个商务每月一次"""

    class Type(Enum):
        BUS_USER = '商务大使 分给 商务的'
        BUS_LEADER = '商务大使 分给 商务组长的'
        BUS_PR = '商务大使 分给 商务PR的'
        BROKER_TO_BUS_USER = 'Broker 分给 商务的'
        BROKER_TO_BUS_LEADER = 'Broker 分给 商务组长的'
        BROKER_TO_BUS_PR = 'Broker 分给 商务PR的'

    class Status(Enum):
        CREATED = 'created'
        DEDUCTED = "deducted"
        FINISHED = 'finished'

    date = db.Column(db.Date, nullable=False, index=True)
    team_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    pay_user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # 转出用户，admin
    type = db.Column(db.StringEnum(Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class BusinessReferralAssetDetail(ModelBase):
    """商务相关的返佣发放明细 """

    class Type(Enum):
        BUS_AGENT = '商务大使 分给 商务代理的'  # 包括2种商务大使
        BUS_TEAM = '商务大使 分给 团队的'  # 次月统计会再分为2份：商务和商务组长 （包括2种商务大使）
        BROKER_TO_BUS_TEAM = 'Broker 分给 团队的'  # 次月统计会再分为2份：商务和商务组长 （Broker商务大使作为broker）

    date = db.Column(db.Date, nullable=False, index=True)
    bus_agent_id = db.Column(db.Integer, index=True)  # 商务代理，团队类型时为None
    bus_user_id = db.Column(db.Integer, index=True)  # 商务id，代理类型时为None
    team_id = db.Column(db.Integer, index=True)  # 团队id，代理类型时为None
    bus_amb_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # 邀请人｜商务大使
    referree_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 被邀请人｜Broker时是交易人
    referral_id = db.Column(db.Integer, db.ForeignKey('referral.id'), nullable=True)  # 商务大使的邀请码｜Broker时是None
    type = db.Column(db.StringEnum(Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 代理or团队的返佣比例
    leader_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 已废弃。存团队组长的分成比例之和(组长+PR)，代理类型时无用
    # BUS_TEAM、BROKER_TO_BUS_TEAM类型的spot_amount、perpetual_amount 是团队每日返佣，会再按leader_rate分为2份
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 现货返佣数
    perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 合约返佣数
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人-现货手续费
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人-合约手续费


class BusinessTeamStatistics(ModelBase):
    """ 商务团队的统计信息 """

    team_id = db.Column(db.Integer, unique=True)  # 团队id
    total_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 团队的累计返佣
    pending_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 团队的当月待返佣
    last_update_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 每天更新一次


class BusinessUserStatistics(ModelBase):
    """ 商务的统计信息 """

    user_id = db.Column(db.Integer, unique=True)  # 商务id
    refer_bus_amb_count = db.Column(db.Integer, nullable=False, default=0)  # 推荐商务大使数
    refer_bus_amb_delete_count = db.Column(db.Integer, nullable=False, default=0)  # 已失效的推荐商务大使数
    refer_count = db.Column(db.Integer, nullable=False, default=0)  # 该商务下级的大使推荐的用户数
    refer_trade_count = db.Column(db.Integer, nullable=False, default=0)  # 该商务下级的大使推荐的用户里有交易的人数
    total_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 商务的累计返佣
    pending_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 商务的当月待返佣
    last_update_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 每天更新一次


class BusinessAmbassadorStatistics(ModelBase):
    """ 商务大使的统计信息 """
    user_id = db.Column(db.Integer, unique=True)  # 商务id
    # 总的邀请数据
    # 当前生效的邀请人数
    effect_refer_count = db.Column(db.Integer, nullable=False, default=0)
    # 当前生效的邀请人中的交易人数：effect_refer_count里有交易的人数，包括币币、兑换、合约
    effect_refer_trade_count = db.Column(db.Integer, nullable=False, default=0)
    # refer交易总额：effect_refer_count的用户产生的交易总额，折算成USD，包括币币、兑换、合约
    effect_refer_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    effect_refer_spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0,
                                         comment="该大使refer用户产生的现货交易额（包括兑换）")
    effect_refer_perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0,
                                              comment="该大使refer用户产生的合约交易额")
    # 累计返佣：本次成为大使后收到的大使返佣，单位为USDT，不统计返CET的部分
    total_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment="累计返佣")
    month_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment="当月返佣")
    amb_refer_count = db.Column(db.Integer, nullable=False, default=0)  # 该用户之前做大使和本次做商务大使期间邀请的总人数
    amb_refer_trade_count = db.Column(db.Integer, nullable=False, default=0)  # amb_refer_count中有交易的人数
    # 本次成为商务大使后的邀请数据
    new_refer_count = db.Column(db.Integer, nullable=False, default=0)
    new_refer_trade_count = db.Column(db.Integer, nullable=False, default=0)
    new_refer_month_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    new_refer_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    new_refer_spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    new_refer_perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    new_total_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    new_month_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # broker商务大使的字段，仅在商务相关页面展示
    bro_effect_refer_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    bro_effect_refer_spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    bro_effect_refer_perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    last_update_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 每天更新一次


class BusinessAmbassadorPermission(ModelBase):
    """商务大使权限设置"""

    class Type(Enum):
        BUS_LIST = '商务列表'
        BUS_AMB_LIST = '商务大使列表'
        BUS_AMB_LOAN_LIST = '商务大使预付金列表'
        BUS_AMB_REPORT = '商务大使返佣记录'
        BUS_AGENT_REPORT = '商务代理返佣记录'

    user_id = db.Column(db.Integer, unique=True)

    permission_list = db.Column(db.Text, nullable=False, default='')

    @classmethod
    def is_limit_visible(cls, user_id: int, _type: Type):
        """返回True表示限制，False表示不限制"""
        permission = BusinessAmbassadorPermission.query.filter(
            BusinessAmbassadorPermission.user_id == user_id,
        ).first()
        if not permission:
            return False
        if not permission.permission_list:
            return False
        permission_list = json.loads(permission.permission_list)
        return _type.value in permission_list


class KolReferralExport(ModelBase):

    class Status(Enum):
        PROCESSING = "processing"
        FINISHED = "finished"
        DELETED = "deleted"
    
    class Type(Enum):
        ALL_USER = "全部用户"
        NEW_USER = "新用户"

    title = db.Column(db.String(32), nullable=False)
    user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'', comment='需导出的KOL的user_id')
    start_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False)
    admin_user_id = db.Column(db.Integer, nullable=False)

    def set_user_ids(self, user_ids: list):
        """设置导出 user_ids"""
        self.user_ids = ",".join([str(item) for item in user_ids]).encode()

    @cached_property
    def cached_user_ids(self) -> list:
        """获取导出 user_ids"""
        if not self.user_ids:
            return []
        return [int(item) for item in self.user_ids.decode().split(",")]


class KolReferralExportReport(ModelBase):

    # 邀请人数：指定时间范围内该用户邀请注册人数
    # 邀请交易人数：指定时间范围内邀请注册的人里，在该时间范围内有交易的人数
    # 邀请充值人数：指定时间范围内邀请注册的人里，在该时间范围内有充值的人数
    # 邀请充值金额：指定时间范围内邀请注册的人里，在该时间范围内的充值金额
    # 邀请现货交易额：指定时间范围内邀请注册的人里，在该时间范围内的现货交易额，币币+兑换
    # 邀请现货手续费：指定时间范围内邀请注册的人里，在该时间范围内的现货手续费，币币+兑换
    # 邀请合约交易额：指定时间范围内邀请注册的人里，在该时间范围内的合约交易额
    # 邀请合约手续费：指定时间范围内邀请注册的人里，在该时间范围内的合约手续费
    # 返佣金额：指定时间范围里，收到的返佣金额

    ref_id = db.Column(db.Integer, db.ForeignKey('kol_referral_export.id'), nullable=False)
    user_id = db.Column(db.Integer, nullable=True)
    email = db.Column(db.String(64), nullable=False)
    referral_code = db.Column(db.String(64), nullable=False)

    referral_user_count = db.Column(db.Integer, nullable=False, default=0)                          # 邀请人数
    referral_trading_user_count = db.Column(db.Integer, nullable=False, default=0)                  # 邀请交易人数人数
    referral_depositing_user_count = db.Column(db.Integer, nullable=False, default=0)               # 邀请充值人数值的人数
    referral_deposit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)           # 邀请充值金额内的充值金额
    referral_spot_trading_volume = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)       # 邀请现货交易额
    referral_spot_trading_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)         # 邀请现货手续费
    referral_perpetual_trading_volume = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 邀请合约交易额
    referral_perpetual_trading_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)    # 邀请合约手续费
    referral_reward = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)                   # 返佣金额      


class AmbassadorDashboard(ModelBase):
    """ 大使-数据看板-总览 """
    user_id = db.Column(db.Integer, nullable=False, unique=True)  # 大使id
    refer_user_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请人数，包括失效的
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请人数里入金人数（链上充值和P2P买入）
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请人数里交易人数（币币、兑换、合约，子账号交易也算）
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里截至当前产生的总现货交易额
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里截至当前产生的合约交易额
    spot_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里截至当前产生的用于返佣的总现货手续费
    perpetual_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里截至当前产生的用于返佣的总合约手续费
    spot_refer_amounts = db.Column(db.String(1024), nullable=False)  # 大使总现货返佣金额，可能有CET和USDT
    perpetual_refer_amounts = db.Column(db.String(1024), nullable=False)  # 大使总合约返佣金额，可能有CET和USDT
    last_update_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 最后更新时间


class AmbassadorReferralHistoryExtra(ModelBase):
    """ ReferralHistory的扩展表，但是只包括大使的邀请记录，统计`被邀请人`相关的数据 """

    referree_id = db.Column(db.Integer, nullable=False, unique=True)  # ReferralHistory.referree_id

    # 仅更新一次
    first_deposit_at = db.Column(db.MYSQL_DATETIME_6, index=True)  # 被邀请人-首次入金时间（链上充值和P2P买入）
    first_deposit_usd = db.Column(db.MYSQL_DECIMAL_26_8)  # 被邀请人-首次入金usd
    first_trade_at = db.Column(db.MYSQL_DATETIME_6, index=True)  # 被邀请人-首次交易时间
    first_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8)  # 被邀请人-首次交易usd

    # 每日增量更新
    deposit_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人-截至当前的总入金金额（链上充值和P2P买入）
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人-截至当前的总现货交易金额
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人-截至当前的总合约交易金额
    # spot_refer_fee_usd、perpetual_refer_amounts 手续费是直接用来返佣的，需扣除AMM分红、手续费返现券、Broker返佣等
    # 读ReferralAssetDetail.spot_fee_usd 和 perpetual_fee_usd，注意同一天ReferralAssetDetail.referree_id可能重复（邀请码分成）
    spot_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人-截至当前的总现货手续费金额
    perpetual_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人-截至当前的总合约手续费金额
    spot_refer_amounts = db.Column(db.String(1024), nullable=False)  # 被邀请人-截至当前返给邀请人的总现货返佣
    perpetual_refer_amounts = db.Column(db.String(1024), nullable=False)  # 被邀请人-截至当前返给邀请人的总合约返佣
    last_update_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)  # 最后更新时间


class AmbassadorDashboardDailyRefer(ModelBase):
    # noqa
    """ 大使-数据看板-每日返佣数据（大使+日期 维度） """
    __table_args__ = (
        db.UniqueConstraint("user_id", "report_date", name="user_id_report_date_unique"),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False)
    inc_refer_user_count = db.Column(db.Integer, nullable=False, default=0)  # 当天新增的邀请人数
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人里当天有入金的人数（链上充值和P2P买入）
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人里当天有交易的人数（币币、兑换、合约，子账号交易也算）
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里当天产生的总现货交易额
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里当天产生的合约交易额
    spot_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里当天产生的用于返佣的总现货手续费
    perpetual_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里当天产生的用于返佣的总合约手续费
    spot_refer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 当天现货总返佣金额USD 即spot_refer_amounts的USD
    perpetual_refer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 当天合约总返佣金额USD
    spot_refer_amounts = db.Column(db.String(1024), nullable=False)  # 当天现货返佣金额，老数据可能有CET和USDT
    perpetual_refer_amounts = db.Column(db.String(1024), nullable=False)  # 当天合约返佣金额，老数据可能有CET和USDT


class AmbassadorDashboardMonthlyRefer(ModelBase):
    # noqa
    """ 大使-数据看板-每月返佣数据（大使+日期 维度） """
    __table_args__ = (
        db.UniqueConstraint("user_id", "report_date", name="user_id_report_date_unique"),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里截至当前产生的总现货交易额
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里截至当前产生的合约交易额
    spot_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里截至当前产生的用于返佣的总现货手续费
    perpetual_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请人数里截至当前产生的用于返佣的总合约手续费
    spot_refer_amounts = db.Column(db.String(1024), nullable=False)  # 现货返佣金额，可能有CET和USDT
    perpetual_refer_amounts = db.Column(db.String(1024), nullable=False)  # 合约返佣金额，可能有CET和USDT


class AmbassadorDashboardDailyReferDetail(ModelBase):
    """ 大使-数据看板-每日返佣明细数据（大使+被邀请人+日期 维度） """

    __table_args__ = (
        db.UniqueConstraint("user_id", "report_date", "referree_id", name="user_id_report_date_ee_id_unique"),
        db.Index("idx_ee_id_report_date", "referree_id", "report_date"),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False)  # 大使id
    referree_id = db.Column(db.Integer, nullable=False)  # 被邀请人id
    referral_id = db.Column(db.Integer, index=True)  # 邀请码id
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人当天产生的现货交易额
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人当天产生的合约交易额
    spot_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人当天产生的现货手续费（用来返佣的）
    perpetual_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人当天产生的合约手续费（用来返佣的）
    refer_asset = db.Column(db.String(32), nullable=False)  # 当天返佣币种
    spot_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 当天现货返佣数目
    perpetual_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 当天合约返佣数目


class AmbassadorDashboardMonthlyReferDetail(ModelBase):
    """ 大使-数据看板-每月返佣明细数据（大使+被邀请人+日期 维度） """

    __table_args__ = (
        db.UniqueConstraint("user_id", "report_date", "referree_id", name="user_id_report_date_ee_id_unique"),
        db.Index("idx_ee_id_report_date", "referree_id", "report_date"),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False)  # 大使id
    referree_id = db.Column(db.Integer, nullable=False)  # 被邀请人id
    referral_id = db.Column(db.Integer, index=True)  # 邀请码id
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人当月产生的现货交易额
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人当月产生的合约交易额
    spot_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人当月产生的现货手续费（用来返佣的）
    perpetual_refer_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人当月产生的合约手续费（用来返佣的）
    spot_refer_amounts = db.Column(db.String(1024), nullable=False)  # 当月现货返佣数目
    perpetual_refer_amounts = db.Column(db.String(1024), nullable=False)  # 当月合约返佣数目


class AmbassadorDashboardDailyTradeMarket(ModelBase):
    """ 大使-数据看板-每日市场交易数据（大使维度+日期 维度）"""

    __table_args__ = (
        db.UniqueConstraint("user_id", "report_date", name="user_id_report_date_unique"),
    )

    class MarketType(Enum):
        SPOT = "现货市场"
        PERPETUAL = "合约市场"

    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False)  # 大使id
    market_data = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)
    # { "spot": [ [market1, total_trade_usd1, ee_user_ids1] ], "perpetual": [] }


class AmbassadorDashboardTradeMarket(ModelBase):
    """ 大使-数据看板-市场交易统计数据（大使维度+统计范围 维度） """

    __table_args__ = (
        db.UniqueConstraint("user_id", "time_range", name="user_id_time_range_unique"),
    )

    class TimeRangeEnum(Enum):
        DAY7 = "7天"
        DAY30 = "30天"
        DAY90 = "90天"
        DAY365 = "365天"

    user_id = db.Column(db.Integer, nullable=False)  # 大使id
    time_range = db.Column(db.StringEnum(TimeRangeEnum), nullable=False)  # 最近N天的统计
    market_data = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)
    # { "spot": [ [market1, trade_usd1, user_ids1] ], "perpetual": [] }


class AmbassadorPackageBatch(ModelBase):
    """大使激励包批次"""

    class Status(Enum):
        CREATED = '待审核'  # 已创建
        AUDITED = '待发放'  # 已审核
        RELEASED = '已发放'  # 已发放
        FINISHED = '已结束'  # 已结束
        STOPPED = '已停用'  # 已停用
        REJECTED = '已驳回'  # 已驳回

    # 基本信息
    batch_name = db.Column(db.String(64), nullable=False, comment='批次名称')
    asset = db.Column(db.String(32), nullable=False, comment='币种')
    package_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='单个激励包金额')
    periods = db.Column(db.Integer, nullable=False, comment='激励包期数')
    valid_days = db.Column(db.Integer, nullable=False, comment='领取有效期(天)')
    status = db.Column(db.StringEnum(Status), nullable=False,
                       default=Status.CREATED)
    # 发放条件
    refer_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  comment='要求的月度Refer交易量(USD)')
    refer_trade_users = db.Column(db.Integer, nullable=False,
                                 comment='要求的月度交易用户数')

    # 发放信息
    release_time = db.Column(db.MYSQL_DATETIME_6, nullable=False,
                            comment='计划发放时间')
    actural_release_time = db.Column(db.MYSQL_DATETIME_6, comment='实际发放时间')
    total_users = db.Column(db.Integer, default=0, comment='发放总人数')
    first_release_time = db.Column(db.Date, comment='首期发放时间')

    creator_id = db.Column(db.Integer, nullable=False, comment='创建人ID')
    reviewer_id = db.Column(db.Integer, comment='审核人ID')
    review_time = db.Column(db.MYSQL_DATETIME_6, comment='审核时间')
    stop_time = db.Column(db.MYSQL_DATETIME_6, comment='停用时间')
    group_ids = db.Column(db.String(1024), comment='客群ID')    # json

    # 统计数据
    released_periods = db.Column(db.Integer, default=0,
                                 comment='已发放期数')
    claimed_users = db.Column(db.Integer, default=0, comment='已领取人数')
    rewarded_users = db.Column(db.Integer, default=0, comment='已获得奖励人数')
    total_released_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0,
                                      comment='已发放总金额')
    total_refer_users = db.Column(db.Integer, default=0,
                                  comment='累计Refer交易用户数')
    total_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0,
                                   comment='累计Refer交易额(USD)')

    @property
    def reward_users(self) -> Set[int]:
        from app.business.push_statistic import AmbassadorPackagePushParser
        """客群用户"""
        parser = AmbassadorPackagePushParser(self)
        _, users = parser.parse()
        return users

    def get_groups(self):
        if not self.group_ids:
            return []
        return json.loads(self.group_ids)


class AmbassadorType(Enum):
        NORMAL = '平台大使'  # 普通大使
        BUSINESS = '商务大使'  # 商务大使
        NONE = '普通用户'  # 非大使


class UserAmbassadorPackage(ModelBase):
    """用户激励包"""
    FAILED_CHECKS_LIMIT = 3

    class Status(Enum):
        CREATED = '待审核'  # 已创建
        AUDITED = '待发放'  # 待发放
        PENDING = '待领取'  # 待领取
        ACTIVE = '生效中'  # 生效中
        EXPIRED = '已过期'  # 未领取已过期
        FINISHED = '已结束'  # 已结束
        STOPPED = '已停用'  # 已停用
        INVALIDATED = '已失效'  # 已失效
        DELETED = '已删除'  # 已删除
    # 关联信息
    batch_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    ambassador_type = db.Column(db.StringEnum(AmbassadorType), nullable=False,
                               comment='大使类型')

    # 状态信息
    status = db.Column(db.StringEnum(Status), nullable=False,
                      default=Status.CREATED)
    claim_time = db.Column(db.MYSQL_DATETIME_6, comment='领取时间')
    stop_time = db.Column(db.MYSQL_DATETIME_6, comment='停用时间')
    first_release_time = db.Column(db.Date, comment='首期发放时间')

    # 进度信息
    current_period = db.Column(db.Integer, default=0, comment='当前期数')
    failed_checks = db.Column(db.Integer, default=0,
                            comment='连续未通过大使检查次数')
    current_period_daily_stat_done_at = db.Column(db.Date, nullable=True,
                            comment='当期激励包最新统计时间')   # 每天都会对激励包的推荐数据进行统计并更新进来
    current_refer_users = db.Column(db.Integer, default=0,
                           comment='当期Refer交易用户数')
    current_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0,
                            comment='当期Refer交易额(USD)')
    current_period_start_time = db.Column(db.Date, nullable=True,
                            comment='当期统计开始时间')
    current_period_end_time = db.Column(db.Date, nullable=True,
                            comment='当期统计结束时间')
    current_package_release_at = db.Column(db.Date, nullable=True,
                            comment='当期激励包即将发放的时间')

    # 统计数据
    released_periods = db.Column(db.Integer, default=0, comment='已发放期数')
    total_released_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0,
                                    comment='已发放总金额')
    total_refer_users = db.Column(db.Integer, default=0,
                                 comment='累计Refer交易用户数')
    total_refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0,
                                  comment='累计Refer交易额(USD)')


class PackageSettlementHistory(ModelBase):
    """激励包结算记录"""
    GIFT_SETTLE_PREFIX = 'ambassador_package'

    __table_args__ = (
        db.Index('idx_user_settlement_time', 'user_id', 'settlement_time'),
        db.UniqueConstraint("user_id", "user_package_id", "period", name="user_id_package_period_unique"),
    )

    # 关联信息
    batch_id = db.Column(db.Integer, nullable=False, index=True)
    user_package_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False)
    period = db.Column(db.Integer, nullable=False, comment='结算期数')
    batch_name = db.Column(db.String(64), nullable=False, comment='批次名称')   # 使用批次实际发放时间
    # 结算数据
    refer_users = db.Column(db.Integer, nullable=False,
                           comment='当期Refer交易用户数')
    refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                            comment='当期Refer交易额(USD)')
    period_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                            comment='当期激励包金额')
    is_ambassador = db.Column(db.MYSQL_BOOL, nullable=False,
                            comment='当期是否为大使')
    is_settled = db.Column(db.MYSQL_BOOL, nullable=False, default=False,
                          comment='是否已结算(获得奖励)')
    asset = db.Column(db.String(32), nullable=False,
                      comment='结算币种')
    settled_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0,
                              comment='结算金额')

        # 结算时间（格式化为每月1号）
    settlement_time = db.Column(db.Date, nullable=False,
                               comment='结算时间')
    


