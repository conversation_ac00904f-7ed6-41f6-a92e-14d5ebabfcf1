from enum import Enum


from app.common import P2pBusinessType, P2pMerActRewardType
from app.models import db, ModelBase, P2pMerchant


# 活动用户表
class P2pMerActUser(ModelBase):
    """p2p商家活动用户表"""

    __table_args__ = (
        db.UniqueConstraint('act_id', 'user_id', name='uq_act_id_user_id'),
        db.Index("idx_created_at", "created_at"),
    )

    class Status(Enum):
        CREATED = "待审核"  # 报名成功
        PASSED = "审核通过"
        REJECTED = "已拒绝"
        CANCELLED = "已取消"

    act_id = db.Column(db.Integer, nullable=False, comment="活动ID")
    user_id = db.Column(db.Integer, nullable=False, index=True, comment="用户ID")
    other_link = db.Column(db.TEXT, comment="其他平台主页")
    intro = db.Column(db.TEXT, comment="自我介绍")
    images = db.Column(db.JSON, default=[], comment="补充资料")
    pdfs = db.Column(db.JSON, default=[], comment="pdf文件")
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, comment="状态")
    pass_at = db.Column(db.MYSQL_DATETIME_6, comment="审核通过时间")
    remark = db.Column(db.String(512), default="", comment="审核备注")


class HourP2pMerActPoint(ModelBase):
    """p2p商家活动小时积分表"""

    class PointType(Enum):
        ADV = "广告挂单"
        ORDER = "完单积分奖励"
        PUNISH = "惩罚清零"
        REVOKE = "取消活动资格"

    report_hour = db.Column(db.MYSQL_DATETIME_6, index=True, nullable=False, comment="报告日期小时")
    act_id = db.Column(db.Integer, nullable=False, index=True, comment="活动ID")
    user_id = db.Column(db.Integer, nullable=False, index=True, comment="用户ID")
    fiat = db.Column(db.String(24), index=True, nullable=False, comment="法币")
    side = db.Column(db.StringEnum(P2pBusinessType), nullable=False, comment="交易区")
    point_type = db.Column(db.StringEnum(PointType), default=PointType.ADV, nullable=False, comment="积分类型")
    point = db.Column(db.MYSQL_DECIMAL_26_8, default=0, comment="积分")
    rank = db.Column(db.Integer, nullable=False, comment="本小时积分排名")
    total_point = db.Column(db.MYSQL_DECIMAL_26_8, default=0, comment="每日当前累计积分")


class P2pMerActUserReward(ModelBase):
    """p2p商家活动用户奖励表"""

    __table_args__ = (
        db.UniqueConstraint('act_id', 'user_id', 'side', name='uq_act_id_user_id_side'),
    )

    act_id = db.Column(db.Integer, index=True, comment="活动ID")
    user_id = db.Column(db.Integer, nullable=False, index=True, comment="用户ID")
    reward_type = db.Column(db.StringEnum(P2pMerActRewardType), comment="活动奖励类型")
    side = db.Column(db.StringEnum(P2pBusinessType), nullable=False, comment="交易区")
    asset = db.Column(db.String(32), nullable=False, comment="奖励代币")
    lock_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0, nullable=False, index=True, comment="锁定代币数量")
    release_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0, nullable=False, comment="已解锁奖励代币数量")


class P2pMerActRewardHistory(ModelBase):
    """p2p商家活动奖励历史"""

    class ReleaseStatus(Enum):
        CREATED = "待发放"
        RELEASED = "已发放"
        FAIL = "发放失败"

    class PermStatus(Enum):
        VALID = "正常"
        INVALID = "异常"

    act_id = db.Column(db.Integer, index=True, nullable=False, comment="活动ID")
    user_id = db.Column(db.Integer, nullable=False, index=True, comment="用户ID")
    reward_type = db.Column(db.StringEnum(P2pMerActRewardType), comment="活动奖励类型")
    asset = db.Column(db.String(32), nullable=False, comment="奖励代币")
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="奖励代币数量")
    side = db.Column(db.StringEnum(P2pBusinessType), nullable=False, comment="交易区")
    point = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="单日积分")
    rank = db.Column(db.Integer, nullable=False, comment="单日排名")
    reward_date = db.Column(db.Date, nullable=False, index=True, comment="获奖日期")
    reward_lock_day = db.Column(db.Integer, nullable=False, comment="锁定时间(天)")
    release_status = db.Column(db.StringEnum(ReleaseStatus), nullable=False, comment="发放状态")
    gift_id = db.Column(db.Integer, index=True, comment="礼物ID")
    merchant_status = db.Column(db.StringEnum(P2pMerchant.Status), default=P2pMerchant.Status.ACTIVE, comment="商家状态")
    perm_status = db.Column(db.StringEnum(PermStatus), default=PermStatus.VALID, comment="权限状态")


class P2pMerActStatisticTime(ModelBase):
    """p2p统计时间表"""

    class TimeType(Enum):
        HOUR = "小时"
        DAY = "天"

    act_id = db.Column(db.Integer, nullable=False, index=True, comment="活动ID")
    hour_point_time = db.Column(db.MYSQL_DATETIME_6, comment="小时积分发放时间")
    day_point_time = db.Column(db.MYSQL_DATETIME_6, comment="天积分发放时间")
    day_reward_time = db.Column(db.MYSQL_DATETIME_6, comment="天奖励发放时间")
