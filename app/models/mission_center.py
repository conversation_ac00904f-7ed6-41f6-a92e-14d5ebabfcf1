from datetime import datetime
from enum import Enum

from dateutil.tz import UTC
from flask_babel import gettext as _

from app.models import ModelBase, db
from app.common.constants import BusinessParty


class SceneType(Enum):
    NEWBIE = "新手任务"


class LogicTemplate(Enum):
    # key + op

    # NEWBIE
    CHANNEL_ID_EQ = 'channel_id_eq'
    REFERER_ID_IN = 'referer_id_in'
    REGISTRATION_AREA_IN = 'registration_area_in'
    REGISTERED_AT_GE = 'registered_at_ge'


class MissionPlan(ModelBase):
    """任务计划"""

    class Status(Enum):
        DRAFT = "待提交"  # 待提交审核
        PENDING = "待审核"  # 待审核
        PASSED = "待推送"  # 已审核
        REJECTED = "审核未通过"  # 被拒绝
        EFFECTIVE = "已生效"  # 生效中
        STOPPED = "已停止"  # 已停止
        FINISHED = "已结束"  # 已结束
        DELETED = "已删除"  # 已删除

    class StatisticsStatus(Enum):
        CREATED = "created"  # 已创建
        PROCESSING = "processing"  # 处理中
        COMPLETED = "completed"  # 已完成

    # 计划名称
    name = db.Column(db.String(128), nullable=False)
    # 场景类型
    scene_type = db.Column(db.StringEnum(SceneType), nullable=False)
    # 业务方
    business_party = db.Column(
        db.StringEnum(BusinessParty), nullable=False, default=BusinessParty.OTHERS
    )
    # 计划开始时间
    start_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    finished_at = db.Column(db.MYSQL_DATETIME_6)
    # 计划结束时间
    end_at = db.Column(db.MYSQL_DATETIME_6)
    # 状态
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.DRAFT)
    # 推送总数量
    total = db.Column(db.Integer, nullable=False, default=0)
    # 优先级 (推送)
    priority = db.Column(db.Integer, nullable=False, default=0)
    # 推送内容 {lang: {title: "", content: ""}}
    deliver_content = db.Column(db.JSON)
    # 拒绝原因
    rejected_reason = db.Column(db.TEXT)
    # 创建人
    created_by = db.Column(db.Integer, nullable=False)
    # 审核人员
    auditor_by = db.Column(db.Integer)
    # 统计状态
    statistics_status = db.Column(db.StringEnum(StatisticsStatus), nullable=False, default=StatisticsStatus.CREATED)


class MissionPlanUserGroup(ModelBase):
    class GroupType(Enum):
        USER_TAG = "user_tag"
        LOGIC = "logic"

    plan_id = db.Column(db.Integer, nullable=False, index=True)
    # 场景类型
    scene_type = db.Column(db.StringEnum(SceneType), nullable=False)
    # 分组类型
    group_type = db.Column(db.StringEnum(GroupType), nullable=False, default=GroupType.USER_TAG)
    # {"CHANNEL_ID": ""}
    logic_params = db.Column(db.JSON)
    logic_template = db.Column(db.StringEnum(LogicTemplate), nullable=False)
    # group_ids 用户画像id [1, 2, 3]
    group_ids = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    registered_at = db.Column(db.MYSQL_DATETIME_6)

    @property
    def check_logic_params(self):
        logic_params = self.logic_params
        if self.registered_at:
            logic_params[LogicTemplate.REGISTERED_AT_GE.name] = self.registered_at
        return logic_params


class MissionCondition(Enum):
    DEPOSIT_AMOUNT = _("入金")
    SPOT_AMOUNT = _("币币交易")
    PERPETUAL_AMOUNT = _("合约交易")
    COPY_TRADING_ONCE = _("跟单交易")
    DEMO_TRADING_ONCE = _("模拟交易")


class Mission(ModelBase):
    """任务"""

    # 计划ID（冗余）
    plan_id = db.Column(db.Integer, nullable=False, index=True)
    sequence = db.Column(db.Integer, nullable=False)
    # 任务期限(日)
    deadline_days = db.Column(db.Integer, nullable=False)
    # 权益ID
    equity_id = db.Column(db.Integer, index=True)
    mission_condition = db.Column(db.StringEnum(MissionCondition), nullable=False)
    # {SPOT_AMOUNT: "100", "asset": "USDT"}
    logic_params = db.Column(db.JSON, nullable=False)


class UserMission(ModelBase):
    """用户任务"""

    __table_args__ = (
        db.UniqueConstraint('mission_id', 'user_id', name='uq_user_mission_idx'),
    )

    MIN_UTC_DATETIME = datetime.min.replace(tzinfo=UTC)
    MAX_UTC_DATETIME = datetime.max.replace(tzinfo=UTC)

    class Status(Enum):
        PENDING = '进行中'
        FINISHED = '已完成'
        SETTLING = '结算中'
        EXPIRED = '已过期'
        FAILED = '已失败'

    class FailReason(Enum):
        RISK_USER = '用户异常'  # 风控用户
        SETTLING_ERROR = "结算异常"

    mission_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    plan_id = db.Column(db.Integer, nullable=False, index=True)
    scene_type = db.Column(db.StringEnum(SceneType), nullable=False)
    # 冗余字段
    mission_condition = db.Column(db.StringEnum(MissionCondition), nullable=False)
    progress = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 最后一次更新时间
    last_updated_at = db.Column(db.MYSQL_DATETIME_6)
    completed_at = db.Column(db.MYSQL_DATETIME_6)
    # 实际生效时间(任务被监控的时间)
    used_at = db.Column(db.MYSQL_DATETIME_6, index=True, default=MIN_UTC_DATETIME)
    expired_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True, default=MAX_UTC_DATETIME)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.PENDING)
    fail_reason = db.Column(db.StringEnum(FailReason))
    event_data = db.Column(db.JSON)


class DailyMissionStatistics(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('report_date', 'plan_id', 'mission_id', name='uq_date_plan_mission_idx'),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    plan_id = db.Column(db.Integer, nullable=False, index=True)
    mission_id = db.Column(db.Integer, nullable=False)
    # 推送用户数
    delivery_count = db.Column(db.Integer, nullable=False, default=0)
    # 任务完成人数
    completion_count = db.Column(db.Integer, nullable=False, default=0)
    # 奖励完成任务
    finished_count = db.Column(db.Integer, nullable=False, default=0)
    # 应发奖励价值
    real_reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 应发奖励价值
    reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
