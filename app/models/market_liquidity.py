from enum import Enum

from .base import db, ModelBase


class MarketType(Enum):
    SPOT = 'spot'
    PERPETUAL = 'perpetual'


class Exchange(Enum):
    CoinEx = 'coinex'
    Binance = 'Binance'
    OKX = 'OKX'
    GATE = 'Gate'
    HTX = 'Huobi'
    MEXC = 'Mexc'
    BYBIT = 'Bybit'
    KUCOIN = 'Kucoin'
    BITGET = 'Bitget'
    LBANK = 'Lbank'
    BITMART = 'bitmart'
    BINGX = 'BingX'
    COINW = 'CoinW'


class MarketLiquidityStatistic(ModelBase):
    """现货、合约流动性统计"""
    spot_exchanges_with_priority = [
        Exchange.CoinEx,
        Exchange.Binance,
        Exchange.OKX,
        Exchange.GATE,
        Exchange.HTX,
        Exchange.MEXC,
        Exchange.BYBIT,
        Exchange.KUCOIN,
        Exchange.BITGET,
        Exchange.LBANK,
        Exchange.BITMART,
        Exchange.BINGX,
        Exchange.COINW,
    ]
    perpetual_exchanges_with_priority = [
        Exchange.CoinEx,
        Exchange.Binance,
        Exchange.OKX,
        Exchange.GATE,
        Exchange.KUCOIN,
    ]

    __table_args__ = (
        db.UniqueConstraint('report_time', 'market_type', 'exchange', 'market',
                            name='time_market_type_exchange_name_uniq'),
    )

    market = db.Column(db.String(32), nullable=False)
    market_type = db.Column(db.StringEnum(MarketType), nullable=False)
    exchange = db.Column(db.StringEnum(Exchange), nullable=False)
    report_time = db.Column(db.Integer, nullable=False)
    # 盘口表现
    bid_ask_1 = db.Column(db.MYSQL_DECIMAL_26_8)
    bid_ask_10 = db.Column(db.MYSQL_DECIMAL_26_8)
    yesterday_median_bid_ask_spread = db.Column(db.MYSQL_DECIMAL_26_8)  # 昨日盘口价差中位数
    sign_price = db.Column(db.MYSQL_DECIMAL_26_8)
    price_volatility = db.Column(db.MYSQL_DECIMAL_26_8)   # 价格波动率
    # 深度表现
    depth_tolerance_02 = db.Column(db.MYSQL_DECIMAL_26_8)   # ±0.2%深度
    depth_tolerance_05 = db.Column(db.MYSQL_DECIMAL_26_8)   # ±0.5%深度
    depth_tolerance_10 = db.Column(db.MYSQL_DECIMAL_26_8)   # ±1%深度
    depth_tolerance_20 = db.Column(db.MYSQL_DECIMAL_26_8)   # ±2%深度
    depth_tolerance_50 = db.Column(db.MYSQL_DECIMAL_26_8)   # ±5%深度
    # 挂单表现
    continuity_tolerance_05 = db.Column(db.Integer)   # ±0.5%连续性评分
    continuity_tolerance_10 = db.Column(db.Integer)   # ±1%连续性评分
    continuity_tolerance_20 = db.Column(db.Integer)   # ±2%连续性评分
    continuity_tolerance_50 = db.Column(db.Integer)   # ±5%连续性评分
    final_continuity_tolerance = db.Column(db.Integer)   # 最终连续性评分


class YesterdayMedianBidAskSpread(ModelBase):
    """昨日盘口价差中位数"""
    __table_args__ = (
        db.UniqueConstraint('report_time', 'market_type', 'exchange', 'market',
                            name='time_market_type_exchange_name_uniq'),
    )

    market = db.Column(db.String(32), nullable=False)
    market_type = db.Column(db.StringEnum(MarketType), nullable=False)
    exchange = db.Column(db.StringEnum(Exchange), nullable=False)
    report_time = db.Column(db.Integer, nullable=False)
    value = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class PriceVolatility(ModelBase):
    """价格波动率"""
    __table_args__ = (
        db.UniqueConstraint('report_time', 'market_type', 'exchange', 'market',
                            name='time_market_type_exchange_name_uniq'),
    )

    market = db.Column(db.String(32), nullable=False)
    market_type = db.Column(db.StringEnum(MarketType), nullable=False)
    exchange = db.Column(db.StringEnum(Exchange), nullable=False)
    report_time = db.Column(db.Integer, nullable=False)
    value = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class UnitLiquidityData(ModelBase):
    """市场流动性每分钟数据"""
    __table_args__ = (
        db.UniqueConstraint('report_time', 'market_type', 'exchange', 'market',
                            name='time_market_type_exchange_name_uniq'),
    )
    market = db.Column(db.String(32), nullable=False)
    market_type = db.Column(db.StringEnum(MarketType), nullable=False)
    exchange = db.Column(db.StringEnum(Exchange), nullable=False)
    report_time = db.Column(db.Integer, nullable=False)
    # 盘口表现
    bid_ask_1 = db.Column(db.MYSQL_DECIMAL_26_8)
    bid_ask_10 = db.Column(db.MYSQL_DECIMAL_26_8)
    sign_price = db.Column(db.MYSQL_DECIMAL_26_8)
    # 深度表现
    depth_tolerance_02 = db.Column(db.MYSQL_DECIMAL_26_8)  # ±0.2%深度
    depth_tolerance_05 = db.Column(db.MYSQL_DECIMAL_26_8)  # ±0.5%深度
    depth_tolerance_10 = db.Column(db.MYSQL_DECIMAL_26_8)  # ±1%深度
    depth_tolerance_20 = db.Column(db.MYSQL_DECIMAL_26_8)  # ±2%深度
    depth_tolerance_50 = db.Column(db.MYSQL_DECIMAL_26_8)  # ±5%深度
    # 挂单表现
    continuity_tolerance_05 = db.Column(db.Integer)  # ±0.5%连续性评分
    continuity_tolerance_10 = db.Column(db.Integer)  # ±1%连续性评分
    continuity_tolerance_20 = db.Column(db.Integer)  # ±2%连续性评分
    continuity_tolerance_50 = db.Column(db.Integer)  # ±5%连续性评分
    final_continuity_tolerance = db.Column(db.Integer)  # 最终连续性评分


class MarketLiquidityFilter(ModelBase):
    """市场流动性筛选条件"""
    user_id = db.Column(db.Integer, nullable=False)
    market_type = db.Column(db.StringEnum(MarketType), nullable=False)
    and_filters = db.Column(db.MYSQL_MEDIUM_TEXT)
    or_filters = db.Column(db.MYSQL_MEDIUM_TEXT)

