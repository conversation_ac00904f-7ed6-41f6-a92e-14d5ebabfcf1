# -*- coding: utf-8 -*-

from __future__ import annotations

import json
import re
from collections import defaultdict
from enum import Enum, IntEnum
from datetime import datetime, timedelta, date
from uuid import uuid4
from functools import partial
from typing import List, Tuple, Union, Optional, Any, Dict, Set, Iterable
from decimal import Decimal

from flask_babel import gettext as _
from pyroaring import BitMap
from sqlalchemy import and_, or_
from werkzeug.security import check_password_hash, generate_password_hash
from sqlalchemy.dialects.mysql import INTEGER as MYSQL_INTEGER
from user_agents.parsers import parse as parse_user_agent
from werkzeug.utils import cached_property

from ..common import (EMAIL_TOKEN_TTL, EMAIL_TOKEN_SIZE,
                      EMAIL_TOKEN_COOLDOWN, API_SECRET_KEY_SIZE,
                      WITHDRAWAL_SUSPENSION_AFTER_SECURITY_RESET,
                      SubAccountPermission,
                      )
from ..common.webauthn import get_webauthn_name_by_aaguid
from ..exceptions import FrequencyExceeded, AccountBoundByAnOther, InvalidArgument
from ..utils import (
    new_hex_token, validate_email, validate_mobile, quantize_amount,
    compact_json_dumps, now, today, cal_password_level,
    AWSBucketPublic, AWSBucketPrivate, hide_email, hide_mobile, hide_text_default,
    hide_text, today_datetime, timestamp_to_datetime, batch_iter,
)
from .base import db, ModelBase
from ..utils.kms import KMSClient
from ..utils.parser import JsonEncoder


class User(ModelBase):
    class UserType(Enum):
        NORMAL = 'normal'
        INTERNAL_MAKER = 'inner_maker'
        EXTERNAL_MAKER = 'outer_maker'
        EXTERNAL_SPOT_MAKER = 'spot_outer_maker'
        EXTERNAL_CONTRACT_MAKER = 'contract_outer_maker'
        EXTERNAL = 'institution'
        SUB_ACCOUNT = 'sub_account'

    class KYCStatus(Enum):
        NONE = 'no'
        PROCESSING = 'processing'
        PASSED = 'pass'
        FAILED = 'fail'

    class KycProStatus(Enum):
        NONE = 'none'
        PROCESSING = 'processing'
        PASSED = 'pass'
        FAILED = 'fail'

    class KYCType(Enum):
        NONE = 'none'
        INDIVIDUAL = 'individual'
        INSTITUTION = 'institution'

    class PasswordLevel(Enum):
        LOW = 'low'
        MIDDLE = 'middle'
        HIGH = 'high'

    TOTP_AUTH_KEY_FLAG = "Y"

    name = db.Column(db.String(64), nullable=False)
    registration_ip = db.Column(db.String(64), nullable=False, default='')
    registration_location = db.Column(db.String(128),
                                      nullable=False, default='')
    email = db.Column(db.String(64), unique=True)
    email_updated_at = db.Column(db.MYSQL_DATETIME_6)

    mobile_country_code = db.Column(db.SmallInteger)
    mobile_num = db.Column(db.String(32), index=True)
    mobile_updated_at = db.Column(db.MYSQL_DATETIME_6)

    login_password_hash = db.Column(db.String(128), default='')
    login_password_updated_at = db.Column(db.MYSQL_DATETIME_6)
    login_password_level = db.Column(db.Enum(PasswordLevel), nullable=False,
                                     default=PasswordLevel.LOW)
    trading_password_hash = db.Column(db.String(128), default='')
    trading_password_updated_at = db.Column(db.MYSQL_DATETIME_6)

    user_type = db.Column(db.Enum(UserType),
                          nullable=False, default=UserType.NORMAL)

    totp_auth_key = db.Column(db.String(128), default='')
    totp_auth_updated_at = db.Column(db.MYSQL_DATETIME_6)

    kyc_status = db.Column(db.Enum(KYCStatus),
                           nullable=False, default=KYCStatus.NONE)

    location_code = db.Column(db.String(3), index=True)

    sub_domain = db.Column(db.String(64), unique=True)

    channel = db.Column(db.String(64))
    remark = db.Column(db.String(64))

    __table_args__ = (
        db.UniqueConstraint('mobile_country_code', 'mobile_num',
                            name='mobile_uniq'),
    )

    def __repr__(self):
        return f'<{type(self).__name__} {self.id}, {self.email!r}>'

    @property
    def has_2fa(self) -> bool:
        return bool(self.mobile or self.totp_auth_key or self.web_authn_list)

    @property
    def has_totp_or_web_auth(self) -> bool:
        return bool(self.totp_auth_key or self.web_authn_list)

    @property
    def mobile(self) -> str:
        if not (country_code := self.mobile_country_code):
            return ''
        if not (number := self.mobile_num):
            return ''
        return f'+{country_code}{number}'
    
    @property
    def web_authn_list(self):
        return [v for v in UserWebAuthn.query.filter(
            UserWebAuthn.user_id == self.id,
            UserWebAuthn.status == UserWebAuthn.Status.VALID).all()]

    @cached_property
    def admin_web_authn_list(self):
        from app.models.authority import AdminUserWebAuthn
        return [v for v in AdminUserWebAuthn.query.filter(
            AdminUserWebAuthn.user_id == self.id,
            AdminUserWebAuthn.status == AdminUserWebAuthn.Status.VALID).all()]

    @property
    def is_sub_account(self) -> bool:
        return self.user_type == User.UserType.SUB_ACCOUNT

    @property
    def communication_name(self) -> str:
        return self.email or self.mobile or self.name_displayed

    @property
    def name_displayed(self) -> str:
        return self.name or self.email or self.mobile or ''

    @property
    def nickname(self) -> str:
        email_prefix = self.email.split("@", 1)[0] if self.email else ''
        return self.name or email_prefix or self.mobile or ''

    @property
    def hidden_email(self) -> str:
        return hide_email(self.email)

    @property
    def hidden_name(self) -> str:
        return hide_text_default(self.name) or hide_email(self.email) \
               or hide_mobile(self.mobile) or ''

    @property
    def hidden_complete_name(self) -> str:
        return hide_text_default(self.name) or hide_email(self.email, hide_domain=True) \
               or hide_mobile(self.mobile) or ''

    @property
    def kyc_verification(self) -> Optional[KycVerification]:
        return self.kyc_verifications \
            .filter(KycVerification.status == KycVerification.Status.PASSED) \
            .order_by(KycVerification.id.desc()) \
            .first()

    @property
    def kyc_country(self) -> str:
        return (kyc.country if (kyc := self.kyc_verification) is not None
                else '')

    @property
    def kyc_first_name(self) -> str:
        return (kyc.first_name if (kyc := self.kyc_verification) is not None
                else '')
    
    @property
    def kyc_last_name(self) -> str:
        return (kyc.last_name if (kyc := self.kyc_verification) is not None
                else '')

    @property
    def kyc_full_name(self) -> str:
        return (kyc.full_name if (kyc := self.kyc_verification) is not None
                else '')

    @property
    def kyc_id_number(self):
        number = (kyc.id_number if (kyc := self.kyc_verification) is not None
                  else '')
        return (hide_text(number, 2, 2) if len(number) > 8
                else hide_text(number, 3, 3))

    @property
    def kyc_type(self):
        """仅有认证状态使用，否则不标识认证类型"""
        from app.models import KYCInstitution

        if self.kyc_status in (self.KYCStatus.PROCESSING, self.KYCStatus.PASSED):
            model = KYCInstitution
            institutions = model.query.filter(
                model.user_id == self.id,
                model.status.in_(
                    [
                        model.Status.CREATED,
                        model.Status.PASSED,
                    ]
                )
            ).all()
            if institutions:
                kyc_type = self.KYCType.INSTITUTION
            else:
                kyc_type = self.KYCType.INDIVIDUAL

        elif self.kyc_status == self.KYCStatus.FAILED:
            kyc_ins = KYCInstitution.query.filter(
                KYCInstitution.status == KYCInstitution.Status.REJECTED,
                KYCInstitution.user_id == self.id,
            ).order_by(KYCInstitution.id.desc()).first()
            kyc_ind = KycVerification.query.filter(
                KycVerification.status == KycVerification.Status.REJECTED,
                KycVerification.user_id == self.id,
            ).order_by(KycVerification.id.desc()).first()
            if kyc_ins and kyc_ind:
                if kyc_ins.updated_at > kyc_ind.updated_at:
                    kyc_type = self.KYCType.INSTITUTION
                else:
                    kyc_type = self.KYCType.INDIVIDUAL

            elif kyc_ins:
                kyc_type = self.KYCType.INSTITUTION

            else:
                kyc_type = self.KYCType.INDIVIDUAL

        else:
            kyc_type = self.KYCType.NONE
        return kyc_type

    @property
    def kyc_pro_status(self):
        _map = {
            KycVerificationPro.Status.PASSED: self.KycProStatus.PASSED,
            KycVerificationPro.Status.CREATED: self.KycProStatus.PROCESSING,
            KycVerificationPro.Status.AUDIT_REQUIRED: self.KycProStatus.PROCESSING,
            KycVerificationPro.Status.REJECTED: self.KycProStatus.FAILED,
            KycVerificationPro.Status.CANCELLED: self.KycProStatus.FAILED,
        }
        r = KycVerificationPro.query.filter(
            KycVerificationPro.user_id == self.id,
        ).order_by(KycVerificationPro.id.desc()).first()
        if not r:
            return self.KycProStatus.NONE
        return _map.get(r.status, self.KycProStatus.NONE)

    @property
    def kyc_institution(self):
        from app.models import KYCInstitution
        return KYCInstitution.query.filter(
                KYCInstitution.user_id == self.id,
                KYCInstitution.status == KYCInstitution.Status.PASSED
            ).first()

    @property
    def main_user(self) -> User:
        sub_user = self.sub_account_ref
        if sub_user:
            main_user: User = sub_user.main_user
            return main_user
        return self

    @property
    def main_user_id(self) -> int:
        return self.main_user.id

    @property
    def main_user_email(self) -> str:
        return self.main_user.email

    @property
    def main_user_name_displayed(self) -> str:
        return self.main_user.name_displayed

    @property
    def main_anti_phishing_code(self) -> str:
        from app.business import UserPreferences
        return UserPreferences(self.main_user.id).anti_phishing_code

    @property
    def main_user_type(self) -> UserType:
        return self.main_user.user_type

    @property
    def extra(self) -> UserExtra:
        return UserExtra.get_or_create(user_id=self.id)

    @property
    def referrer(self) -> Optional[User]:

        from app.models import ReferralHistory
        history = ReferralHistory.query \
            .filter(ReferralHistory.referree_id == self.id) \
            .first()
        return history.referrer if history is not None else None

    @property
    def referrees(self) -> List[User]:

        from app.models import ReferralHistory
        return ReferralHistory.query \
            .filter(ReferralHistory.referrer_id == self.id) \
            .all()

    @property
    def referee_count(self) -> int:

        from app.models import ReferralHistory
        return ReferralHistory.query \
            .filter(ReferralHistory.referrer_id == self.id).count()

    def check_login_password(self, password: str) -> bool:
        if self.login_password_hash is None:
            return False
        return check_password_hash(self.login_password_hash, password)

    def set_login_password(self, password: str):
        had_password = bool(self.login_password_hash)
        self.login_password_hash = generate_password_hash(password)
        self.login_password_level = self.cal_password_level(password)
        self.login_password_updated_at = now()
        db.session.commit()
        if had_password:
            self.suspend_withdrawal_after_security_reset()

    def get_totp_auth_key(self) -> str:
        totp_auth_key = self.extra.totp_auth_key
        if totp_auth_key:
            return KMSClient.decrypt(totp_auth_key)
        else:
            return ""

    def set_totp_auth_key(self, auth_key: Optional[str]):
        had_totp = bool(self.totp_auth_key)
        _now = now()
        self.totp_auth_key = self.TOTP_AUTH_KEY_FLAG if auth_key else auth_key
        self.totp_auth_updated_at = _now
        self.extra.set_totp_auth_key(auth_key, _now)
        db.session.commit()
        if had_totp:
            self.suspend_withdrawal_after_security_reset()

    def set_email(self, email: str):
        had_email = bool(self.email)
        self.email = email
        self.email_updated_at = now()
        db.session.commit()
        if had_email:
            self.suspend_withdrawal_after_security_reset()

    def set_default_account_name(self):
        default_account_name = self.extra.default_account_name(self.id)
        db.session.add(self.extra)
        self.extra.account_name = default_account_name
        db.session.commit()

    def set_mobile(self, country_code: Optional[int], number: Optional[str]):
        had_mobile = bool(self.mobile)
        self.mobile_country_code = country_code
        self.mobile_num = number
        self.mobile_updated_at = now()
        db.session.commit()
        if had_mobile:
            self.suspend_withdrawal_after_security_reset()

    def set_admin_webauthn(self, auth: Dict):
        from app.models.authority import AdminUserWebAuthn
        num = AdminUserWebAuthn.query.filter(
            AdminUserWebAuthn.user_id == self.id,
            AdminUserWebAuthn.status == AdminUserWebAuthn.Status.VALID
        ).count()
        name = f"Passkey #{num+1}"
        auth_obj = AdminUserWebAuthn(
            name=name,
            user_id=self.id,
            rp_id=auth["rp_id"],
            credential_id=auth["credential_id"],
            public_key=auth["public_key"],
            sign_count=auth["sign_count"],
            status=AdminUserWebAuthn.Status.VALID
        )
        db.session.add(auth_obj)
        db.session.commit()

    def set_webauthn(self, auth: Dict) -> UserWebAuthn:
        from app.business import CacheLock, LockKeys
        with CacheLock(LockKeys.webauthn(self.id), wait=False):
            db.session.rollback()
            num = UserWebAuthn.query.filter(
                UserWebAuthn.user_id == self.id,
                UserWebAuthn.status == UserWebAuthn.Status.VALID
            ).count()
            if num >= UserWebAuthn.MAX_WEBAUTHN_COUNT:
                raise InvalidArgument(message=_("创建通行密钥超过最大上限"))
            authn_row = UserWebAuthn.get_auth_by_user_id_credential_id(self.id, auth["credential_id"])
            if authn_row:
                raise InvalidArgument(message=_("通行密钥已绑定"))
            name = get_webauthn_name_by_aaguid(auth['aaguid']) or f"Passkey #{num+1}"
            auth_obj = UserWebAuthn(
                name=name,
                user_id=self.id,
                rp_id=auth["rp_id"],
                credential_id=auth["credential_id"],
                public_key=auth["public_key"],
                transports=json.dumps(auth["transports"]),
            )
            db.session.add(auth_obj)
            db.session.commit()
        return auth_obj

    def delete_webauthn(self):
        UserWebAuthn.query.filter(
            UserWebAuthn.user_id == self.id,
            UserWebAuthn.status == UserWebAuthn.Status.VALID
        ).update(
                {'status': UserWebAuthn.Status.DELETED},
                synchronize_session=False
            )
        db.session.commit()
        self.suspend_withdrawal_after_security_reset()

    def suspend_withdrawal_after_security_reset(self):
        from ..business import UserSettings
        seconds = WITHDRAWAL_SUSPENSION_AFTER_SECURITY_RESET
        UserSettings(self.id).set(
            'withdrawals_disabled_after_security_editing',
            True,
            valid_till=now() + timedelta(seconds=seconds))

    @classmethod
    def from_account(cls, account: str) -> Union[User, List[User], None]:
        if not account:
            return None
        if '@' in account:
            if not validate_email(account):
                return None
            return cls.query.filter(cls.email == account).first()

        if account.startswith('+'):
            if not (mobile_valid := validate_mobile(account)):
                return None
            country_code, mobile_num = mobile_valid
            return cls.query \
                .filter(cls.mobile_country_code == country_code,
                        cls.mobile_num == mobile_num) \
                .first()

        if account.isdigit():
            users = User.query \
                .filter(User.mobile_num == account) \
                .all()
            if mobile_valid := validate_mobile(account):
                country_code, mobile_num = mobile_valid
                users.extend(User.query
                             .filter(User.mobile_country_code == country_code,
                                     User.mobile_num == mobile_num))
            if not users:
                return None
            return users if len(users) > 1 else users[0]

        return None

    @classmethod
    def full_match_user(cls, keyword: str) -> Optional[int]:
        """
        only support full match for email
        """
        query = cls.query
        if not validate_email(keyword):
            return
        result = query.filter(cls.email == keyword).with_entities(
           User.id
        ).all()
        return result[0].id if result else None

    @classmethod
    def search_for_users(cls, keyword: str) -> List[int]:
        query = cls.query
        ors = []
        mobile_country, mobile_num = None, None
        if keyword.isdigit():
            ors.append(cls.id == keyword)
            if len(keyword) > 5:
                ors.append(cls.mobile_num == keyword)
            if (mobile_valid := validate_mobile(keyword)) and len(mobile_valid[1]) > 5:
                mobile_country, mobile_num = mobile_valid
                ors.append(and_(cls.mobile_country_code == mobile_country,
                                cls.mobile_num == mobile_num))
        elif keyword.startswith('+'):
            if (mobile_valid := validate_mobile(keyword)) and len(mobile_valid[1]) > 5:
                mobile_country, mobile_num = mobile_valid
                ors.append(and_(cls.mobile_country_code == mobile_country,
                                cls.mobile_num == mobile_num))
        elif validate_email(keyword):
            ors.append(cls.email == keyword)
        else:
            ors.append(User.name == keyword)
        
        if not ors:
            return []

        def key(_x):
            _id, _email, _mobile_c, _mobile_n, _name = _x
            return (str(_id) == keyword,
                    _mobile_c == mobile_country and _mobile_n == mobile_num,
                    _mobile_n == keyword,
                    _name == keyword,
                    (len(keyword) / len(_email)) if _email else 0.)

        return [
            x[0] for x in sorted(
                query.filter(or_(*ors))
                .with_entities(cls.id,
                               cls.email,
                               cls.mobile_country_code,
                               cls.mobile_num,
                               cls.name).limit(10),
                key=key, reverse=True)
        ]

    @classmethod
    def cal_password_level(cls, password: str) -> PasswordLevel:
        score = cal_password_level(password)
        if score >= 70:
            return cls.PasswordLevel.HIGH
        if score >= 40:
            return cls.PasswordLevel.MIDDLE
        return cls.PasswordLevel.LOW

    @classmethod
    def get_by_mobile(cls, country_code, mobile):
        return cls.query.filter(
            cls.mobile_num == mobile,
            cls.mobile_country_code == country_code
        ).first()

    @property
    def user_display_name(self) -> str:
        return f"{self.nickname}(@{self.extra.display_account_name})"


class UserExtra(ModelBase):
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), unique=True)
    withdraw_password_hash = db.Column(db.String(128), default='')
    withdraw_password_updated_at = db.Column(db.MYSQL_DATETIME_6, default=now)
    totp_auth_key = db.Column(db.String(512), default='')
    totp_auth_updated_at = db.Column(db.MYSQL_DATETIME_6, default=now)

    trade_password_hash = db.Column(db.String(128), default='')
    trade_password_updated_at = db.Column(db.MYSQL_DATETIME_6, default=now)

    account_name = db.Column(db.String(128), unique=True)
    avatar = db.Column(db.String(512), default="")

    @property
    def has_withdraw_password(self) -> bool:
        return bool(self.withdraw_password_hash)

    def check_withdraw_password(self, password: str) -> bool:
        if not self.withdraw_password_hash:
            return False
        return check_password_hash(self.withdraw_password_hash, password)

    def set_withdraw_password(self, password: Optional[str], suspend: bool = False):
        from ..business import UserPreferences
        self.withdraw_password_hash = generate_password_hash(password)
        self.withdraw_password_updated_at = now()
        db.session_add_and_commit(self)
        UserPreferences(self.user_id).opening_withdraw_password = True
        if suspend:
            self.suspend_withdrawal_after_security_edit()

    def reset_withdraw_password(self):
        from ..business import UserPreferences
        self.withdraw_password_hash = ""
        self.withdraw_password_updated_at = now()
        db.session.commit()
        self.suspend_withdrawal_after_security_edit()
        UserPreferences(self.user_id).opening_withdraw_password = False

    def suspend_withdrawal_after_security_edit(self):
        from ..business import UserSettings
        seconds = WITHDRAWAL_SUSPENSION_AFTER_SECURITY_RESET
        UserSettings(self.user_id).set(
            'withdrawals_disabled_after_withdraw_password_editing',
            True,
            valid_till=now() + timedelta(seconds=seconds))
    
    @property
    def has_trade_password(self) -> bool:
        return bool(self.trade_password_hash)

    def check_trade_password(self, password: str) -> bool:
        if not self.trade_password_hash:
            return False
        return check_password_hash(self.trade_password_hash, password)

    def set_trade_password(self, password: Optional[str]):
        self.trade_password_hash = generate_password_hash(password)
        self.trade_password_updated_at = now()
        db.session_add_and_commit(self)

    def reset_trade_password(self):
        from ..business import UserPreferences
        self.trade_password_hash = ""
        self.trade_password_updated_at = now()
        db.session.commit()
        UserPreferences(self.user_id).opening_trade_password = False

    def set_totp_auth_key(self, auth_key: str | None, _now: datetime, need_commit=False):
        self.totp_auth_key = KMSClient.encrypt(auth_key) if auth_key else auth_key
        self.totp_auth_updated_at = _now
        if need_commit:
            db.session_add_and_commit(self)
        else:
            db.session.add(self)

    @classmethod
    def default_account_name(cls, user_id: int):
        return f"user{user_id}"

    def check_has_set_account_name(self):
        return self.account_name != self.default_account_name(self.user_id)

    @property
    def display_account_name(self):
        return self.account_name or self.default_account_name(self.user_id)

    @cached_property
    def avatar_url(self):
        from app.business.user import UserRepository
        return UserRepository.get_user_avatar_url(self.avatar)

    @cached_property
    def admin_avatar_url(self):
        """admin不展示节日头像"""
        from app.business.user import UserRepository
        avatar_key = self.avatar or UserRepository.get_system_default_avatar()
        return UserRepository.get_user_avatar_url(avatar_key)


class UserSetting(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    key = db.Column(db.String(64), nullable=False)
    value = db.Column(db.String(2048), nullable=False)
    valid_from = db.Column(db.MYSQL_DATETIME_6)
    valid_till = db.Column(db.MYSQL_DATETIME_6)

    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    __table_args__ = (
        db.Index('user_key_idx', 'user_id', 'key'),
    )

    @classmethod
    def get_user_ids(cls,
                     key_value_mapping: dict,
                     filter_user_ids: Optional[Iterable[int]] = None):
        batch_size = 3000
        result = set()
        query = cls.query.filter(cls.status == cls.Status.VALID).with_entities(
            cls.user_id
        )
        build_conditions = [and_(
           cls.key == key,
           cls.value == value
        ) for key, value in key_value_mapping.items()]
        query = query.filter(
            *build_conditions
        )
        if filter_user_ids is not None:
            for batch_user_ids in batch_iter(filter_user_ids, batch_size):
                u_ids = {v.user_id
                         for v in
                         query.filter(
                            cls.user_id.in_(batch_user_ids)
                         )}
                result |= u_ids
        else:
            u_ids = {v.user_id for v in query.with_entities(cls.user_id).all()}
            result |= u_ids
        return result


class UserWebAuthn(ModelBase):

    MAX_WEBAUTHN_COUNT = 10
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    name = db.Column(db.String(512), nullable=False, default='')
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    rp_id = db.Column(db.String(512), nullable=False)
    credential_id = db.Column(db.String(1024), nullable=False) # urlsafe_b64encode
    public_key = db.Column(db.String(1024), nullable=False) # urlsafe_b64encode
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    sign_count = db.Column(db.Integer, nullable=False, default=0)
    transports = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    last_used_at = db.Column(db.MYSQL_DATETIME_6)

    @classmethod
    def get_auth_by_user_id_credential_id(cls, user_id: int, credential_id: str):
        auth = UserWebAuthn.query.filter(
            UserWebAuthn.user_id == user_id,
            UserWebAuthn.credential_id == credential_id,
            UserWebAuthn.status == UserWebAuthn.Status.VALID,
        ).first()
        return auth


class UserPreference(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    key = db.Column(db.String(64), nullable=False)
    value = db.Column(db.String(2048), nullable=False)

    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    __table_args__ = (
        db.Index('user_key_idx', 'user_id', 'key'),
    )


def _ua_parse_app_version(user_agent: str):
    match = re.search(r'CoinEx App Version:\s*(\d+\.\d+\.\d+)', user_agent)
    if match:
        return match.group(1).strip()
    else:
        return ''


def _ua_parse_app_device(user_agent: str):
    match = re.search(r'CoinEx Device:\s*([^;]*);', user_agent)
    if match:
        return match.group(1).strip()
    else:
        return ''


class UserLoginState(db.Model):
    class Status(Enum):
        ONLINE = 'online'
        OFFLINE = 'offline'

    class DeviceType(Enum):
        PC = "PC"
        MOBILE = "MOBILE"

    def get_device_type(self):
        _parse_data = self.device.split("/")
        if len(_parse_data) >= 1:
            _type = _parse_data[0].strip()
            if _type in ("PC", "Other"):
                return self.DeviceType.PC
            return self.DeviceType.MOBILE
        return self.DeviceType.PC

    id = db.Column(db.BigInteger, primary_key=True)  # this is a BigInteger
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    updated_at = db.Column(db.MYSQL_DATETIME_6,
                           default=datetime.utcnow,
                           onupdate=datetime.utcnow)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    ip = db.Column(db.String(64), nullable=False)
    location = db.Column(db.String(128), nullable=False, default='')
    device = db.Column(db.String(256), nullable=False)
    token = db.Column(db.String(64), nullable=False, unique=True)
    expires_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)

    status = db.Column(db.Enum(Status), nullable=False, default=Status.ONLINE)

    __table_args__ = (
        db.Index('user_expiration_idx', 'user_id', 'expires_at'),
    )

    @classmethod
    def list_user_online_states(cls, user_id: int) -> List[UserLoginState]:
        return cls.query \
            .filter(cls.user_id == user_id,
                    cls.status == cls.Status.ONLINE,
                    cls.expires_at >= datetime.utcnow()) \
            .all()

    @classmethod
    def list_user_online_states_with_paginate(cls, user_id: int, page: int, limit: int
                                              ):
        return cls.query \
            .filter(cls.user_id == user_id,
                    cls.status == cls.Status.ONLINE,
                    cls.expires_at >= datetime.utcnow()) \
            .order_by(cls.id.desc()).paginate(page, limit, error_out=False)

    @classmethod
    def renew_to(cls, token: str, expires_at: Union[int, datetime]):
        if isinstance(expires_at, int):
            expires_at = timestamp_to_datetime(expires_at)
        row = cls.query.filter(cls.token == token).first()
        if row:
            row.expires_at = expires_at
            db.session.commit()

    @classmethod
    def clear_tokens(cls, tokens: List[str]):
        cls.query.filter(cls.token.in_(tokens)).update(
            {cls.status: cls.Status.OFFLINE},
            synchronize_session=False
        )
        db.session.commit()

    @classmethod
    def get_device(cls, user_agent: str, app_version: str):
        parsed = parse_user_agent(user_agent)
        device = parsed.get_device()
        if app_device := _ua_parse_app_device(user_agent):
            device = app_device
        third = parsed.get_browser()
        if app_version:
            third = app_version
        return "{device} / {os} / {third}".format(
            device=device,
            os=parsed.get_os(),
            third=third
        )


class LoginHistory(ModelBase):

    class FailReason(Enum):
        EMAIL_CODE = '邮箱验证码错误'
        TWO_FA = '2FA验证码错误'
        TWO_FA_MOBILE = '手机验证码错误'
        TWO_FA_TOTP = 'TOTP错误'
        TWO_FA_WEBAUTHN = '安全密钥错误'
        DOUBLE_CHECK = '待二次确认'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    account = db.Column(db.String(64), nullable=False, default='')
    ip = db.Column(db.String(64), nullable=False, default='', index=True)
    location = db.Column(db.String(128), nullable=False, default='')
    user_agent = db.Column(db.String(512), nullable=False, default='')
    platform = db.Column(db.String(32), nullable=False, default='')
    successful = db.Column(db.Boolean, nullable=False, default=False)
    device_id = db.Column(db.String(64), nullable=True)
    fail_reason = db.Column(db.String(64), nullable=True)

    user = db.relationship('User',
                           backref=db.backref('login_history', lazy='dynamic'))

    __table_args__ = (
        db.Index('user_id_idx', 'user_id', 'ip'),
    )

    @classmethod
    def get_login_user_agent(cls, user_agent: str, app_version: str):
        if app_version:
            return f'CoinEx App Version: {app_version}; {user_agent}'
        return user_agent

    @property
    def device(self):
        from app.api.common.request import RequestPlatform

        parsed = parse_user_agent(self.user_agent)
        if self.platform == RequestPlatform.WEB.value:
            return str(parsed)
        else:
            device = parsed.get_device()
            if app_device := _ua_parse_app_device(self.user_agent):
                device = app_device
            third = parsed.get_browser()
            if app_version := _ua_parse_app_version(self.user_agent):
                third = app_version
            return "{device} / {os} / {third}".format(
                device=device,
                os=parsed.get_os(),
                third=third
            )

    @property
    def user_agent_for_admin(self):
        from app.api.common.request import RequestPlatform

        if self.platform == RequestPlatform.WEB.value:
            return self.user_agent_web
        return self.user_agent_mobile

    @property
    def user_agent_web(self):
        parsed = parse_user_agent(self.user_agent)
        os = parsed.os.family.split()[0]
        browser = parsed.browser.family.split()[0]
        return f'{os} {browser}'
    user_agent_human = user_agent_web

    @property
    def user_agent_mobile(self):
        parsed = parse_user_agent(self.user_agent)
        brand = f'{parsed.device.brand} {parsed.device.model}'
        os = f'{parsed.os.family} {parsed.os.version_string}'
        return f'{brand} {os}'

    @classmethod
    def is_new_device(cls, user_id: int, device_id: str) -> bool:
        rows = cls.query.filter(
            cls.user_id == user_id,
            cls.created_at > now() - timedelta(days=365),
            cls.successful.is_(True)
        ).with_entities(
            cls.device_id
        ).all()
        f = True
        for x in rows:
            if not x[0]:
                continue
            # 移除校验位后对比，兼容旧数据
            if x[0] == device_id or x[0] == device_id[:-4]:
                f = False
                break
        return f


class LoginRelationHistory(ModelBase):
    """
    通过查询IP/设备ID的访问记录，定位出有相同IP/设备ID的关联账号；
    可查询账号：搜索出该账号访问过的注册IP，最近访问IP，设备ID（web+APP），以及有过相同访问记录的其他账号。
    可查询IP：搜索出该IP下，访问过的账号，以及这些账号的，注册IP，设备ID（web+APP）
    可查询设备ID（WEB+APP）：搜索出该设备ID下，访问过的账号，以及这些账号的注册IP，最近访问IP
    2022年前只导入用户注册时候对应的登录记录
    2022年之后导入所有相关成功登录记录
    """
    class Platform(Enum):
        WEB = 'web'
        ANDROID = 'Android'
        IOS = 'iOS'
    login_history_id = db.Column(db.BigInteger, nullable=False, unique=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    ip = db.Column(db.String(64), nullable=False, default='', index=True)
    is_registration = db.Column(db.Boolean, nullable=False, default=False)  # 是否是注册的记录
    device_id = db.Column(db.String(64), nullable=True, index=True)
    platform = db.Column(db.StringEnum(Platform), nullable=False, default=Platform.WEB)
    device = db.Column(db.String(128), nullable=False, default='')

    @classmethod
    def analysis_duplicate_device_register_by(cls, user_ids) -> set:
        """根据指定用户，计算出重复设备注册的用户"""
        pending_checks = {}
        for chunk_user_ids in batch_iter(user_ids, 5000):
            rows = cls.query.with_entities(
                cls.user_id,
                cls.device_id
            ).filter(
                cls.user_id.in_(chunk_user_ids),
                cls.is_registration.is_(True)
            ).all()
            pending_checks.update({row.user_id: row.device_id for row in rows})

        device_users = defaultdict(set)
        device_ids = list(set(pending_checks.values()))
        for chunk_device_ids in batch_iter(device_ids, 5000):
            rows = cls.query.with_entities(
                cls.user_id,
                cls.device_id
            ).filter(
                cls.device_id.in_(chunk_device_ids),
                cls.is_registration.is_(True)
            ).all()
            for row in rows:
                device_users[row.device_id].add(row.user_id)

        targets = set()
        for user_id, device_id in pending_checks.items():
            using_users = device_users[device_id]
            for using_user_id in using_users:
                if user_id != using_user_id:
                    targets.add(user_id)
                    break
        return targets

    @classmethod
    def analysis_duplicate_device_by(cls, sub_account_dic) -> set:
        """子账号归入主账号"""
        records = defaultdict(set)
        start = 0
        query_limit = 100000
        while True:
            rows = cls.query.filter(cls.id > start).order_by(
                cls.id.asc()).limit(query_limit).with_entities(
                cls.id,
                cls.device_id,
                cls.user_id
            ).all()
            if not rows:
                break
            for id_, device_id, user_id in rows:
                if not device_id:
                    continue
                main_user_id = sub_account_dic.get(user_id, user_id)
                records[device_id].add(main_user_id)
            start = rows[-1].id
        duplicated_user_ids = set()
        for uid_set in records.values():
            if len(uid_set) > 1:
                duplicated_user_ids.update(uid_set)
        return duplicated_user_ids

    @classmethod
    def query_duplicate_device_users(cls, user_ids, is_get_device_mapper: bool = False) -> set | dict:
        # 找出用户注册的设备
        activity_rows = []
        for batch_ids in batch_iter(user_ids, 3000):
            rows = cls.query.filter(
                cls.user_id.in_(batch_ids),
                cls.is_registration.is_(True),
            ).with_entities(
                cls.user_id, cls.device_id
            ).all()
            activity_rows += rows

        # 找到这些设备第一次注册的记录
        device_set = {row.device_id for row in activity_rows}
        device_rows = []
        for batch_ids in batch_iter(device_set, 3000):
            rows = cls.query.filter(
                cls.device_id.in_(batch_ids),
                cls.is_registration.is_(True),
            ).with_entities(
                cls.user_id, cls.device_id, cls.created_at
            ).all()
            device_rows += rows

        device_first_user_map = {i.device_id: i.user_id for i in
                                 sorted(device_rows, key=lambda x: x.created_at, reverse=True)}
        risk_user_device_mapper = {}
        for row in activity_rows:
            device_id = row.device_id
            if device_id in device_first_user_map and row.user_id != device_first_user_map[device_id]:
                risk_user_device_mapper[row.user_id] = device_id
        if is_get_device_mapper:
            return risk_user_device_mapper
        return set(risk_user_device_mapper.keys())


class SubAccount(ModelBase):
    class Status(Enum):
        VALID = 'pass'
        FROZEN = 'frozen'

    class ManageStatus(Enum):
        # 授权托管状态
        UNMANAGED = '未授权'
        MANAGED = '授权中'

    class Type(Enum):
        NORMAL = '手动创建的子账号'
        STRATEGY = '用于运行策略的子账号'
        COPY_TRADER = '用于运行<跟单交易-带单人>的子账号'  # 只有1个
        COPY_FOLLOWER = '用于运行<跟单交易-跟单人>的子账号'  # 最多5个

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    main_user_id = db.Column(db.Integer, db.ForeignKey('user.id'),
                             nullable=False)
    user = db.relationship(
        'User',
        foreign_keys=[user_id],
        backref=db.backref('sub_account_ref', uselist=False))
    main_user = db.relationship(
        'User',
        foreign_keys=[main_user_id],
        backref=db.backref('sub_accounts', lazy='dynamic'))

    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    is_visible = db.Column(db.Boolean, nullable=False, default=True)  # 为False时表示admin禁用，web、api侧不可见
    remark = db.Column(db.String(64), default='')
    admin_remark = db.Column(db.String(256), default='')
    permissions = db.Column(db.String(1024), nullable=False, default='')  # 全部权限存空字符串，避免新增权限需要刷数据
    manage_status = db.Column(db.StringEnum(ManageStatus), nullable=False, default=ManageStatus.UNMANAGED)
    type = db.Column(db.StringEnum(Type), nullable=False, default=Type.NORMAL)

    @property
    def enum_permissions(self) -> List[SubAccountPermission]:
        if not self.permissions:
            # 默认全部权限
            return list(SubAccountPermission)
        p_names = json.loads(self.permissions)
        return [SubAccountPermission[p] for p in p_names]

    def has_permission(self, per: SubAccountPermission) -> bool:
        return per in self.enum_permissions

    @classmethod
    def get_main_user_sub_mapping(cls, main_user_ids: Iterable[int]) -> Dict[int, Set]:
        result = dict()
        for batch_main_u_ids in batch_iter(main_user_ids, 5000):
            q = cls.query.with_entities(
                cls.status == cls.Status.VALID,
                cls.type == cls.Type.NORMAL,
                cls.user_id.in_(batch_main_u_ids)
            ).with_entities(cls.user_id, cls.main_user_id).all()
            for v in q:
                sub_ids = result.get(v.main_user_id, {})
                sub_ids.add(v.user_id)
                result[v.main_user_id] = sub_ids
        return result



class SubAccountManagerRelation(ModelBase):
    """ 子账号托管关系 """

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    __table_args__ = (db.UniqueConstraint("manager_id", "user_id", name="manager_id_user_id_unique"),)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    main_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    manager_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    main_user_remark = db.Column(db.String(64), nullable=False, default="")
    manager_remark = db.Column(db.String(64), nullable=False, default="")

    @classmethod
    def get_disabled_sub_user_set(cls, manager_id: int) -> Set[int]:
        manage_relations = cls.query.filter(
            cls.manager_id == manager_id,
            cls.status == cls.Status.VALID,
        ).all()
        main_user_ids = [v.main_user_id for v in manage_relations]
        m = ClearedUser.query.filter(
            ClearedUser.user_id.in_(main_user_ids),
            ClearedUser.status == ClearedUser.Status.FORBIDDEN,
            ClearedUser.valid.is_(True)).with_entities(
            ClearedUser.user_id
        ).all()
        disabled_main_ids = {v.user_id for v in m}
        return {v.user_id for v in manage_relations
                if v.main_user_id in disabled_main_ids}


class SubAccountLimitRecord(ModelBase):
    """ 用户-子账号上限配置 """
    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, unique=True)  # main_user_id
    limit_num = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(256), nullable=False, default='')

    user = db.relationship("User", foreign_keys=[user_id])


class BusinessSystemUserRecord(ModelBase):
    """ 用户-业务账号配置 """
    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, unique=True)  # main_user_id
    # limit_num = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(256), nullable=False, default='')

    user = db.relationship("User", foreign_keys=[user_id])


class UserBizTag(ModelBase):
    """ 用户业务标签 """

    __table_args__ = (
        db.Index('idx_biz_tag_status', 'biz_tag', 'status'),
    )

    class BizTag(Enum):
        EE_NOT_REFERRAL = "被邀请人不返佣"

    class Source(Enum):
        CASHBACK_EQUITY = "手续费返现权益"

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    user_id = db.Column(db.Integer, nullable=False, index=True)
    biz_tag = db.Column(db.StringEnum(BizTag), nullable=False)
    source = db.Column(db.StringEnum(Source), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)

    @classmethod
    def batch_add_user_tag(cls, biz_tag: BizTag, source: Source, user_ids: set[int]):
        exist_rows = cls.query.filter(
            cls.user_id.in_(user_ids),
            cls.biz_tag == biz_tag,
            cls.source == source,
        ).all()
        exist_row_map = {i.user_id: i for i in exist_rows}
        for user_id in user_ids:
            if user_id in exist_row_map:
                row = exist_row_map[user_id]
            else:
                row = cls(
                    user_id=user_id,
                    biz_tag=biz_tag,
                    source=source,
                )
                exist_row_map[user_id] = row
            row.status = cls.Status.VALID
            db.session.add(row)
        return exist_row_map

    @classmethod
    def batch_del_user_tag(cls, biz_tag: BizTag, source: Source, user_ids: set[int]):
        cls.query.filter(
            cls.user_id.in_(user_ids),
            cls.biz_tag == biz_tag,
            cls.source == source,
            cls.status == cls.Status.VALID,
        ).update(
            {'status': cls.Status.DELETED},
            synchronize_session=False,
        )

    @classmethod
    def query_tag_user_ids(cls, biz_tag: BizTag) -> set[int]:
        rows = cls.query.filter(
            cls.biz_tag == biz_tag,
            cls.status == cls.Status.VALID,
        ).with_entities(
            cls.user_id,
        ).all()
        return {i.user_id for i in rows}


class EmailToken(db.Model):
    class EmailType(Enum):
        REGISTRATION = 'sign_up'
        EMAIL_BINDING = 'add_email'
        EMAIL_EDIT = 'edit_email'
        LOGIN_PASSWORD_RESET = 'reset_login_password'
        TOTP_BINDING = 'add_totp'
        WITHDRAWAL = 'withdraw_coin'
        RED_PACKET = 'red_packet'
        API_EXPIRATION_EXTEND = 'api_expiration_extend'
        API_WITHDRAWAL_ADDRESS = 'api_withdrawal_address'
        WITHDRAWAL_APPROVER = 'withdrawal_approver'
        WITHDRAWAL_APPROVER_MEMBER = 'withdrawal_approver_member'
        KYT_DEPOSIT_AUDIT = 'kyt_deposit_audit'
        LIVENESS_CHECK = 'liveness_check'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    expired_at = db.Column(db.MYSQL_DATETIME_6, nullable=False,
                           default=(lambda:
                                    datetime.now()
                                    + timedelta(seconds=EMAIL_TOKEN_TTL)))

    email = db.Column(db.String(64))
    token = db.Column(db.String(64), index=True)
    email_type = db.Column(db.StringEnum(EmailType), nullable=False)
    data = db.Column(db.String(1024), nullable=False, default='')

    is_used = db.Column(db.Boolean, nullable=False, default=False)

    user = db.relationship('User',
                           backref=db.backref('email_tokens', lazy='dynamic'))

    @classmethod
    def new(cls, user_id: int, email: str, email_type: EmailType,
            data: Any = None, ttl: int = None,
            *, expire_previous: bool = False) -> EmailToken:
        if expire_previous:
            last_token: cls = cls.query \
                .filter(cls.user_id == user_id,
                        cls.email == email,
                        cls.email_type == email_type) \
                .order_by(cls.id.desc()) \
                .first()
            _now = now()
            if last_token:
                since_last = (_now - last_token.created_at).total_seconds()
                cool_down = EMAIL_TOKEN_COOLDOWN
                if since_last < cool_down:
                    message = _('请在%(seconds)s秒后再试。',
                                seconds=cool_down - since_last)
                    raise FrequencyExceeded(message=message)
            cls.query \
                .filter(cls.expired_at > _now,
                        cls.user_id == user_id, 
                        cls.email == email,
                        cls.email_type == email_type) \
                .update(dict(expired_at=_now))

        row = cls(
            user_id=user_id,
            email=email,
            email_type=email_type,
            token=new_hex_token(EMAIL_TOKEN_SIZE),
            data=compact_json_dumps(data)
        )
        if ttl is not None:
            row.expired_at = now() + timedelta(seconds=ttl)
        return db.session_add_and_commit(row)

    @classmethod
    def validate(cls, token: str, email_type: EmailType
                 ) -> Optional[EmailToken]:
        return cls.query \
            .filter(cls.token == token,
                    cls.email_type == email_type,
                    cls.is_used.is_(False),
                    cls.expired_at >= now()) \
            .first()

    @property
    def data_json(self):
        return json.loads(self.data)


class KycVerification(ModelBase):

    class IDType(Enum):
        ID_CARD = _('身份证')
        PASSPORT = _('护照')
        DRIVING_LICENSE = _('驾驶证')
        NIN = 'NIN'     # 尼日利亚
        BVN = 'BVN'     # 尼日利亚
        CPF = 'CPF'     # 巴西

    class Status(Enum):
        CREATED = 'init' # 三方审核
        AUDIT_REQUIRED = 'audit_required'  # admin审核
        SCREENING = "screening"  # 风险筛查中
        PASSED = 'verify_ok'
        REJECTED = 'verify_error'
        DUPLICATE_VERIFICATION = 'other_verified'
        CANCELLED = 'cancelled'

    class RejectionReason(Enum):
        PHOTO_MISMATCHED = _('持证人照片与证件信息不符')
        DOCUMENT_MISSING = _('无证件信息')
        DOCUMENT_UNSUPPORTED = _('抱歉，暂不支持该证件类型')
        DOCUMENT_ILLEGIBLE = _('证件信息模糊')
        DOCUMENT_EXPIRED = _('证件已过期，请换证后再认证')
        AGE_MISMATCHED = _('年龄不符')
        NATIONALITY_ERROR = _('国籍有误，请核对国籍选项')
        ID_IS_SAMPLE = _('请勿上传样本证件照，情节严重或触发系统封号')
        ID_USED_AS_SELFIE = _(
            '自拍使用完整证件照')
        SELFIE_IS_SCREEN_PAPER_VIDEO = _(
            '拍照画面来自打印件、截屏、视频等')
        FACE_ABSENT = _('照片未露出面部')
        HANDWRITTEN_NOTE_MISMATCHED = _('手持声明书信息不符')
        INFO_MISMATCHED = _('提供的信息有误')

        ID_ISSUED_BY_COUNTRY_RESIDENCE = _('请上传居住国家发行的有效证件')
        SHOW_FACE_WITH_ID = _('请按要求露脸，并同时手持声明书和证件')
        TICKET_CONTACTED = _('我们已通过工单联系您，请查看邮箱')
        INVALID_TIME_FORMAT = _('手持声明书时间有误，请按年/月/日格式重新书写')
        BLURRY_ANNOUNCEMENT = _('手持声明书模糊')

        DOCUMENT_MANIPULATED = _('证件信息被篡改或被处理过，无法认证')
        PHOTO_MANIPULATED = _('证件照片被篡改或被后期处理过')
        FRAUDSTER = _('证件人可能存在个人信誉问题，情节严重将触发系统封号')
        FAKE = _('上传证件照片为网络假证')
        PHOTO_MISMATCH = _('持证人人脸与证件信息不符')
        PUNCHED_DOCUMENT = _('证件已经失效或损坏，无法认证')
        MISMATCH_PRINTED_BARCODE_DATA = _('证件信息无法匹配，请重新上传')
        ADDITIONAL_DOCUMENT_REQUIRED = _("请补充提供其他文件")
        DIGITAL_COPY = _('上传证件照非原件，请重新上传')
        NOT_READABLE_DOCUMENT = _('证件照模糊，请拍摄清晰的图片')
        BLURRED = _('证件照模糊，无法认证')
        HIDDEN_PART_DOCUMENT = _('证件照片信息被遮挡，请按示例上传')
        DAMAGED_DOCUMENT = _('证件被损坏或证件照拍摄不规范，请按要求重新认证')
        PRESET_ID_UNMATCHED = _('填入的信息与证件信息不符，请重新填写')
        NO_DOCUMENT = _('证件照片不符合要求，请按示例上传')
        SAMPLE_DOCUMENT = _('请勿上传样本证件照，情节严重将触发系统封号')
        MISSING_FRONT = _('请按要求上传证件正面照')
        MISSING_BACK = _('请按要求上传证件反面照')
        MISSING_PAGE = _('请上传完整证件页面')
        MISSING_SIGNATURE = _('证件未签名，无法认证')
        CAMERA_BLACK_WHITE = _('请勿上传黑白证件照')
        INVALID_WATERMARK = _('证件照片出现水印无效，暂不支持该证件类型')
        MANUAL_REJECTION = _('证件照片被裁剪，无法认证')
        NOT_MATCHED_WITH_LAST_SUBMIT = _('和上次完成认证的人员信息不一致')

        SELFIE_MANIPULATED = _('人脸识别操作不规范，请重试')
        AGE_DIFFERENCE_TOO_BIG = _('人脸识别认证与本人证件照年龄差距过大，无法认证')
        FACE_NOT_FULLY_VISIBLE = _('人脸识别未露出面部，无法认证')
        SELFIE_BAD_QUALITY = _('人脸识别像素太低，无法认证')
        BLACK_AND_WHITE = _('人脸识别不能用黑白像认证')
        LIVENESS_BAD_QUALITY = _('人脸识别图像像素太低，无法认证')
        LIVENESS_FAILED = _('人脸识别操作不规范，请重新认证')
        VIDEO_FAILED = _('视频认证未完成，请重新认证')
        UNSUPPORTED_VIDEO_LANGUAGE = _("不支持的视频认证语言")

        AGE_NOT_ALLOWED = _('平台仅支持18至70周岁的用户进行实名认证')
        DOCUMENT_EXISTED = _('证件号码已存在')
        UPLOAD_FAILED = _('证件信息上传失败，请重新上传')

        OTHER = _('证件信息无法匹配，请重新上传证件认证')

        CREDIBILITY_PROBLEM = _('持证人存在信誉问题，无法认证通过')

        FACE_TOO_CLOSE = _('人脸检测离摄像头太近，请保持距离重新拍摄认证')
        FACE_TOO_SMALL = _('人脸识别头像太小，请重新拍摄认证')
        FACE_NOT_FOUND = _('无法获取到人脸信息，请按操作重新认证')

        # KYC Institution
        COMPANY_ADDR_DOES_NOT_EXISTS = _('填写的公司地址不存在')
        COMPANY_INFO_UNABLE_TO_CONFIRM = _('公司信息无法查证')
        COMPANY_COUNTRY_DOES_NOT_SUPPORT = _('暂不支持该国籍认证')
        COMPANY_BEHAVIOR_ILLEGAL = _('注册公司存在违法违规行为，无法认证')
        INSTITUTION_INFO_WRONG = _('提供的资料有误')
        INSTITUTION_UPLOAD_FILE_FUZZY = _('上传的文件信息模糊')
        INSTITUTION_UPLOAD_FILE_IS_COPY = _('上传的文件非原文件')
        INSTITUTION_UPLOAD_FILE_WRONG = _('上传文件有误')
        COMPANY_NOT_BENEFICIARIES = _('公司未明确受益人')
        COMPANY_NOT_REPRESENTATIVES = _('公司未明确代表人')
        COMPANY_NOT_STRUCTURE = _('公司未明确股权结构')
        EXPERIENCE_REQUIREMENT_MISMATCH = _('经验要求不符')
        THIRD_PARTY_INVOLVED = _('申请人在接受第三方验证')

        REGULATORY_ENFORCEMENT = _('由于监管要求，无法为你提供更多服务')
        WRONG_ID_NUMBER = _("证件号码有误，请重新填写")
        BAD_PROOF_OF_ADDRESS = _("地址证明有误")
        BAD_PROOF_OF_PAYMENT = _("付款证明有误")
        WRONG_ADDRESS = _("文件地址与用户输入的地址不匹配")
        INCOMPATIBLE_LANGUAGE = _("不支持的文件语言")

        SERVICE_TIME_OUT = _('第三方超时未审核')

        # 重复枚举
        FACE_MISMATCHED = _('持证人照片与证件信息不符')
        BAD_QUALITY = _('证件照模糊，无法认证')
        GLARE = _('证件照片信息被遮挡，请按示例上传')
        WRONG_DOCUMENT_PAGE = _('证件照片信息被遮挡，请按示例上传')
        DIFFERENT_PERSONS_SHOWN = _('证件照片不符合要求，请按示例上传')
        OTHER_DENIED = _('证件信息无法匹配，请重新上传证件认证')
        FACE_CROPPED = _('人脸识别未露出面部，无法认证')
        COMPANY_COUNTRY_WRONG = _('国籍有误，请核对国籍选项')

    class PlatForm:
        WEB = 'web'
        APP = 'app'

    class Gender(Enum):
        MALE = "男"
        FEMALE = "女"

    class ExpireDateType(Enum):
        NEVER = "永不到期"
        EXPIRABLE = "有效期"

    class ServiceType(Enum):
        JUMIO = 'Jumio'
        REFINITIV = '路孚特'
        SUMSUB = 'Sumsub'
        MANUAL_AUDIT = '人工通道'

    class DocType(Enum):
        DOC = '文件认证'
        NON_DOC = '非文件认证'

    class SubmitType(Enum):
        REGULAR = '普通'
        SUBMIT_ON_PASS = '重新认证'

    __table_args__ = (
        db.Index('ix_updated_at', 'updated_at'),
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    country = db.Column(db.String(16), index=True, nullable=False)
    # 国籍 只用来admin展示
    nationality = db.Column(db.String(16), nullable=True)
    id_type = db.Column(db.StringEnum(IDType), nullable=False)
    id_number = db.Column(db.String(128), nullable=False, index=True)
    first_name = db.Column(db.String(64), nullable=True)
    last_name = db.Column(db.String(64), nullable=True)
    name = db.Column(db.String(128), nullable=False, default='')

    front_img_file_id = db.Column(db.Integer, db.ForeignKey('file.id'))
    back_img_file_id = db.Column(db.Integer, db.ForeignKey('file.id'))
    face_img_file_id = db.Column(db.Integer, db.ForeignKey('file.id'))

    rejection_reason = db.Column(db.String(4096))
    custom_rejection_reason = db.Column(db.String(4096), nullable=True)
    is_custom_reason = db.Column(db.Boolean, nullable=False, default=False)

    auditor_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    audited_at = db.Column(db.MYSQL_DATETIME_6)

    platform = db.Column(db.String(10))

    gender = db.Column(db.String(10), nullable=True, default='')
    date_of_birth = db.Column(db.Date, nullable=True)
    doc_expire_date_type = db.Column(db.Enum(ExpireDateType))
    doc_expire_date = db.Column(db.Date, nullable=True)

    service_type = db.Column(db.StringEnum(ServiceType), nullable=True)
    doc_type = db.Column(db.StringEnum(DocType), default=DocType.DOC)

    submit_type = db.Column(db.StringEnum(SubmitType), nullable=True, default=SubmitType.REGULAR)
    history_id = db.Column(db.Integer, nullable=True)

    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)

    user = db.relationship(
        'User',
        foreign_keys=[user_id],
        backref=db.backref('kyc_verifications', lazy='dynamic'))
    auditor = db.relationship(
        'User',
        foreign_keys=[auditor_id],
        backref=db.backref('audited_kyc_verifications', lazy='dynamic'))
    front_img_file = db.relationship('File', foreign_keys=[front_img_file_id])
    back_img_file = db.relationship('File', foreign_keys=[back_img_file_id])
    face_img_file = db.relationship('File', foreign_keys=[face_img_file_id])

    remark = db.Column(db.String(512), default="", nullable=True, comment="备注")

    def get_reject_reason(self, translate=True):
        if self.is_custom_reason:
            return self.custom_rejection_reason
        else:
            if translate:
                return ';'.join(
                    [_(self.RejectionReason[i].value) for i in self.rejection_reason.split(',')]
                ) if self.rejection_reason else None
            else:
                return ';'.join(
                    [self.RejectionReason[i].value for i in self.rejection_reason.split(',')]
                ) if self.rejection_reason else None

    @property
    def full_name(self):
        return self.name or f'{self.first_name} {self.last_name}'

    @classmethod
    def has_user_reached_daily_limit(cls, user_id: int):
        return KycVerification.query \
            .filter(KycVerification.user_id == user_id,
                    KycVerification.created_at >= today_datetime()) \
            .count() >= 5

    @cached_property
    def last_passed_at(self) -> datetime | None:
        from . import UserRiskScreenCase

        if self.status != self.Status.PASSED:
            return None
        case: UserRiskScreenCase = UserRiskScreenCase.query.filter(
            UserRiskScreenCase.user_id == self.user_id,
            UserRiskScreenCase.entity_type == UserRiskScreenCase.EntityType.INDIVIDUAL.name,
            UserRiskScreenCase.status == UserRiskScreenCase.Status.PASSED,
        ).first()
        if not case:
            return None
        return case.audited_at or case.created_at


class KycVerificationPro(ModelBase):
    """kyc高级认证"""

    class Status(Enum):
        CREATED = "待审核"     # 三方审核
        AUDIT_REQUIRED = '待人工审核'  # admin审核
        PASSED = "已通过"
        REJECTED = "已拒绝"
        CANCELLED = "已取消"

    class RejectionReason(Enum):
        ADDRESS_ERROR = _("居住地址信息有误")
        ADDRESS_FILE_ERROR = _("地址证明文件不符合要求")
        UNABLE_SERVICE = _("由于监管要求，无法为您提供更多服务。")
        KYC_INVALID = _("个人初级认证失效")
        INVALID_ADDRESS_PROOF = _("地址证明文件不符合要求")
        INCOMPLETE_RESIDENTIAL_ADDRESS = _("居住地址信息填写不完整")
        ADDRESS_PROOF_DATE_INVALID = _("请提供签发日期在三个月内的居住地址证明文件")
        ADDRESS_MISMATCH = _("填写的居住地址与地址证明文件不匹配")
        INCOMPLETE_ADDRESS_PROOF = _("居住地址证明文件不完整")
        ADDRESS_PROOF_MISSING_ADDRESS = _("居住地址证明文件未包含地址信息")
        ADDRESS_PROOF_UNREADABLE = _("居住地址证明文件无法打开")
        ADDRESS_PROOF_MISSING_NAME = _("居住地址证明文件上无姓名")
        BLURRY_ADDRESS_PROOF = _("居住地址证明文件模糊")
        KYC_SCAN_PROVIDED = _("初级KYC证件为扫描件，请重新认证后提交高级KYC")
        KYC_DOCUMENT_DAMAGED = _("初级KYC证件已损坏，请重新认证后提交高级KYC")
        KYC_DOCUMENT_EXPIRED = _("初级KYC证件已过期，请重新认证后提交高级KYC")
        SUPPORT_TICKET_SENT = _("我们已通过工单联系您，请查看邮件")

        ADDRESS_PROOF_IS_SCREENSHOT = _("地址证明文件为截图")
        ADDRESS_PROOF_MISSING_FULL_NAME = _("地址证明文件上未展示全名")
        ADDRESS_PROOF_MISSING_FULL_ADDRESS = _("地址证明文件上未展示全部地址信息")
        ADDRESS_PROOF_MISSING_SIG = _("地址证明文件上没有签发机构的印章或签名")
        SAME_ADDRESS_PROOF = _("之前提供了相同的地址证明文件")
        ADDRESS_PROOF_MANIPULATED = _("地址证明文件被篡改或被后期处理过")
        ADDRESS_PROOF_NEED_TRANSLATION = _("地址证明文件语言无法识别")
        THIRD_PARTY_SPAM = _("认证次数超过了第三方的限制")
        OTHER = _("无法通过高级认证")
        NAME_MISMATCH = _("识别的姓名与用户初级KYC姓名不一致")
        COUNTRY_MISMATCH = _("身份证件签发国家/地区与地址证明文件不匹配")

        SERVICE_TIME_OUT = _('第三方超时未审核')

    class ServiceType(Enum):
        SUMSUB = 'Sumsub'
        MANUAL_AUDIT = '人工通道'

    class Platform(Enum):
        WEB = 'web'
        APP = 'app'

    __table_args__ = (
        db.Index('ix_updated_at', 'updated_at'),
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    country = db.Column(db.String(128), nullable=False)
    address = db.Column(db.String(256), nullable=False)
    address_file_id = db.Column(db.Integer, db.ForeignKey('file.id'))  # TODO 弃用 待删除

    address_file_ids = db.Column(db.String(256), nullable=False)  # file id 列表，逗号分隔
    pro_name = db.Column(db.String(128), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)

    platform = db.Column(db.StringEnum(Platform))
    service_type = db.Column(db.StringEnum(ServiceType), nullable=False)
    parsed_info = db.Column(db.MYSQL_MEDIUM_TEXT)

    # admin 相关字段
    rejection_reason = db.Column(db.String(4096), comment="拒绝原因")
    custom_rejection_reason = db.Column(db.Text, comment="自定义原因")
    remark = db.Column(db.String(256), nullable=False, default='')
    # 待确定
    # is_custom_reason = db.Column(db.Boolean, nullable=False, default=False, comment="是否自定义原因")
    auditor_id = db.Column(db.Integer, comment="审核用户ID")
    audited_at = db.Column(db.DateTime, comment="审核时间")
    history_id = db.Column(db.Integer, nullable=True)

    @property
    def address_file_url(self):
        file = File.query.get(self.address_file_id)
        if not file:
            return ""
        return file.private_url

    @property
    def address_file_urls(self):
        if not self.address_file_ids:
            return []
        file_ids = self.address_file_ids.split(',')
        return [f.private_url for f in File.query.filter(File.id.in_(file_ids)).all()]

    def get_reject_reason(self, translate=True):
        if self.custom_rejection_reason:
            return self.custom_rejection_reason
        else:
            if translate:
                return ';'.join(
                    [_(self.RejectionReason[i].value) for i in self.rejection_reason.split(',')]
                ) if self.rejection_reason else None
            else:
                return ';'.join(
                    [self.RejectionReason[i].value for i in self.rejection_reason.split(',')]
                ) if self.rejection_reason else None


class KycVerificationHistory(ModelBase):
    """
    第三方审核认证记录
    """
    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'
        CANCELLED = 'cancelled'
        FAILED = 'failed'
        RECHANGED = 'rechanged'

    class RejectType(Enum):
        RETRY = 'retry'
        FINAL = 'final'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    transaction_id = db.Column(db.String(64))  # 第三方标识
    request_id = db.Column(db.String(64), nullable=False)  # 内部标识
    verification_url = db.Column(db.String(512))
    detail = db.Column(db.MYSQL_MEDIUM_TEXT)
    rejection_reason = db.Column(db.String(4096)) # 第三方拒绝原因
    reject_type = db.Column(db.StringEnum(RejectType), default=RejectType.RETRY)     # 第三方拒绝类型
    service_type = db.Column(db.StringEnum(KycVerification.ServiceType))
    doc_type = db.Column(db.StringEnum(KycVerification.DocType), default=KycVerification.DocType.DOC)

    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)

    def get_reject_reason(self, translate=True):
        Reason = KycVerification.RejectionReason
        if translate:
            return ';'.join(
                [_(Reason[i].value) for i in self.rejection_reason.split(',')]
            ) if self.rejection_reason else None
        else:
            return ';'.join(
                [Reason[i].value for i in self.rejection_reason.split(',')]
            ) if self.rejection_reason else None


class KycVerificationProHistory(ModelBase):
    """
    高级KYC第三方审核认证记录
    """

    class RejectType(Enum):
        RETRY = 'retry'
        FINAL = 'final'

    class Status(Enum):
        CREATED = '待审核'
        FINISHED = '已完成'
        FAILED = '已失败'
        CANCELLED = '已取消'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    transaction_id = db.Column(db.String(64))  # 第三方标识
    request_id = db.Column(db.String(64), nullable=False)  # 内部标识
    detail = db.Column(db.MYSQL_MEDIUM_TEXT)
    rejection_reason = db.Column(db.String(4096))  # 第三方拒绝原因
    reject_type = db.Column(db.StringEnum(RejectType), default=RejectType.RETRY)  # 第三方拒绝类型
    service_type = db.Column(db.StringEnum(KycVerificationPro.ServiceType))
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)

    def get_reject_reason(self, translate=True):
        Reason = KycVerificationPro.RejectionReason
        if translate:
            return ';'.join(
                [_(Reason[i].value) for i in self.rejection_reason.split(',')]
            ) if self.rejection_reason else None
        else:
            return ';'.join(
                [Reason[i].value for i in self.rejection_reason.split(',')]
            ) if self.rejection_reason else None


class KycVerificationStatistics(ModelBase):
    report_date = db.Column(db.Date, nullable=False)
    service_type = db.Column(db.StringEnum(KycVerification.ServiceType))    # 为空时表示全部
    third_party_count = db.Column(db.Integer, default=0) # 第三方审核数量
    third_party_passed_count = db.Column(db.Integer, default=0) # 第三方审核通过数量
    second_audited_count = db.Column(db.Integer, default=0) # 需要二次审核确认的数量
    admin_count = db.Column(db.Integer, default=0) # 人工审核数量
    admin_passed_count = db.Column(db.Integer, default=0) # 人工审核通过的数量


class KycCountryConfig(ModelBase):

    country = db.Column(db.String(16), nullable=False)
    service_type = db.Column(db.StringEnum(KycVerification.ServiceType), nullable=False)
    is_audit_required = db.Column(db.Boolean, nullable=False, default=False)
    pro_service_type = db.Column(db.StringEnum(KycVerificationPro.ServiceType), nullable=False)
    pro_is_audit_required = db.Column(db.Boolean, nullable=False, default=False)
    pro_supported = db.Column(db.Boolean, nullable=False, default=True)
    supported_id_types = db.Column(db.String(256), nullable=False, default='')      # 文件认证支持的证件类型
    id_type_pages = db.Column(db.String(256), nullable=False, default='')           # 文件认证证件单双页配置
    non_doc_id_types = db.Column(db.String(256), nullable=False, default='')        # 非文件认证支持的证件类型


class LivenessCheckProfile(ModelBase):

    user_id = db.Column(db.Integer, nullable=False)
    kyc_id = db.Column(db.Integer, nullable=True)
    request_id = db.Column(db.String(64), nullable=False)  # 内部标识
    transaction_id = db.Column(db.String(64))  # 第三方标识


class LivenessCheckHistory(ModelBase):

    class Business(Enum):
        MANUAL = '手动发起'
        RESET_SECURITY = '重置安全工具'
        UNFREEZE_ACCOUNT = '自助解冻'

    class Status(Enum):
        CREATED = '待验证'
        AUDIT_REQUIRED = '待第三方审核'
        MANUAL_AUDIT_REQUIRED = '待人工审核'
        PASSED = '已通过'
        REJECTED = '已拒绝'
        CANCELLED = '已取消'

    class AuditType(Enum):
        THIRD_PARTY = "第三方审核"
        ADMIN = "人工审核"

    class Reason(Enum):
        # 新增或修改需同步到 SecurityResetApplication.Reason
        BAD_FACE_COMPARISON = _("未能清楚识别脸部，请重新自拍确保您的脸部清晰可见")
        SELFIE_LIVENESS = _("活体检测失败，请再次尝试或使用其他设备完成")
        WATERMARK = _("请重新自拍确保相机无水印")
        WITH_PHONE = _("活体检测时请勿让手机出现在画面中")
        MANY_PEOPLE = _("请确保活体检测时仅一人出镜")
        FAKE = _("检测到可疑行为")
        FORCED = _("检测到强制验证行为")
        SELFIE_MISMATCH = _("自拍照与证件照片不符")
        SELFIE_WITH_ID = _("请手持之前上传的身份证件完成自拍，确保身份证件信息清晰可见")
        VIDEO_SELFIE = _("请确保您的脸部在视频中清晰可见")
        OTHER = _("活体检测失败")

    REQUIRE_INSTANT_CHECK_BUSINESSES = []    # 暂无

    user_id = db.Column(db.Integer, nullable=False)
    kyc_id = db.Column(db.Integer, nullable=True)
    action_id = db.Column(db.String(64), nullable=False)  # 内部标识
    transaction_id = db.Column(db.String(64))  # 第三方标识
    business = db.Column(db.StringEnum(Business), nullable=False)
    business_id = db.Column(db.Integer)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    reason = db.Column(db.String(4096))     # 第三方拒绝理由。多个拒绝理由使用,分割
    auditor_id = db.Column(db.Integer)
    audited_at = db.Column(db.DateTime)
    admin_reason = db.Column(db.String(4096))   # 人工审核拒绝理由。多个拒绝理由使用,分割
    custom_admin_reason = db.Column(db.String(4096))
    email = db.Column(db.String(64))
    remark = db.Column(db.String(256))
    face_img_file_id = db.Column(db.Integer)

    def get_reject_reason(self, translate=True):
        if admin_reject_reason := self.get_admin_reject_reason(translate):
            return admin_reject_reason
        else:
            return self.get_third_reject_reason(translate)

    def get_third_reject_reason(self, translate=True):
        if self.reason:
            li = self.reason.split(',')
            value_li = [self.Reason[i].value for i in li]
            if translate:
                return ';'.join([_(i) for i in value_li])
            else:
                return ';'.join(value_li)
        else:
            return ''

    def get_admin_reject_reason(self, translate=True):
        if self.custom_admin_reason:
            return self.custom_admin_reason
        else:
            if self.admin_reason:
                li = self.admin_reason.split(',')
                value_li = [self.Reason[i].value for i in li]
                if translate:
                    return ';'.join([_(i) for i in value_li])
                else:
                    return ';'.join(value_li)
            else:
                return ''

    @property
    def face_img_file_url(self):
        file = File.query.get(self.face_img_file_id)
        if not file:
            return ""
        return file.private_url

    @property
    def audit_type(self) -> LivenessCheckHistory.AuditType | None:
        # 审核类型：按审核的结果，分为”第三方审核“、”人工审核“，未审核的为空
        if self.status == self.Status.MANUAL_AUDIT_REQUIRED:
            return self.AuditType.ADMIN
        elif self.status in [self.Status.PASSED, self.Status.REJECTED]:
            if self.auditor_id:
                return self.AuditType.ADMIN
            else:
                return self.AuditType.THIRD_PARTY
        else:
            return None


class KycNonDocStats(ModelBase):
    __table_args__ = (db.UniqueConstraint("user_id", "service_type", name="user_id_service_type_unique"),)

    MAX_FAIL_COUNT = 3

    user_id = db.Column(db.Integer, nullable=False)
    service_type = db.Column(db.StringEnum(KycVerification.ServiceType), nullable=False, default=KycVerification.ServiceType.SUMSUB)
    fail_count = db.Column(db.Integer, nullable=False, default=0)
    is_forbidden = db.Column(db.MYSQL_BOOL, nullable=False, default=False)


class File(db.Model):
    class Provider(Enum):
        AWS = 'aws'

    class MimeType(Enum):
        IMG_PNG = 'image/png'
        IMG_JPG = 'image/jpeg'
        IMG_SVG = 'image/svg+xml'
        IMG_WEBP = 'image/webp'
        FILE = 'file'
        VIDEO = 'video'
        VTT = 'text/vtt'

    class ACLType(Enum):
        PRIVATE = "private"
        PUBLIC_READ = "public_read"

    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    bucket = db.Column(db.String(64), nullable=False)
    key = db.Column(db.String(128), nullable=False)
    name = db.Column(db.String(128), nullable=False, default='')
    size = db.Column(db.Integer, nullable=False, default=0)
    extra = db.Column(db.Text)

    provider = db.Column(db.StringEnum(Provider), nullable=False)
    mime_type = db.Column(db.StringEnum(MimeType), nullable=False,
                          default=MimeType.FILE)
    acl = db.Column(db.StringEnum(ACLType), nullable=False,
                    default=ACLType.PUBLIC_READ)

    user = db.relationship('User', backref=db.backref('files', lazy='dynamic'))

    @property
    def static_url(self) -> str:
        if self.provider == self.Provider.AWS:
            return AWSBucketPublic.get_file_url(self.key)
        return ''

    @property
    def private_url(self) -> str:
        if self.provider == self.Provider.AWS:
            return AWSBucketPrivate.get_file_url(self.key)
        return ''

    @classmethod
    def new(cls, user_id: int, key: str, name: str,
            size: int = 0, extra: str = None,
            mime_type: MimeType = MimeType.FILE,
            acl: ACLType = ACLType.PUBLIC_READ,
            provider: Provider = Provider.AWS, ):
        from app.config import config

        if provider != cls.Provider.AWS:
            raise
        bucket = config['AWS_FILE']['bucket_name']
        return cls(
                user_id=user_id,
                bucket=bucket,
                key=key,
                name=name,
                size=size,
                extra=extra,
                mime_type=mime_type,
                provider=provider,
                acl=acl
                )


class ApiAuth(ModelBase):
    DEFAULT_TTL = 86400 * 30 * 3
    API_NUM_LIMIT = 50
    ALLOW_IPS_NUM_LIMIT = 50
    SEND_EMAIL_DELTA_HOURS = (24, 48, 72)   # 需要保证元组内的元素按升序排列

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class Source(Enum):
        SELF = 'SELF'
        BROKER = 'BROKER'
        COPY_TRADER = 'COPY_TRADER'  # 带单人的带单子账号使用

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    creator_id = db.Column(db.Integer, nullable=False)

    allowed_ips = db.Column(db.String(2048), nullable=False, default='')
    access_id = db.Column(db.String(64), nullable=False, unique=True,
                          default=lambda: uuid4().hex.upper())
    secret_key = db.Column(db.String(128), nullable=False,
                           default=partial(new_hex_token, API_SECRET_KEY_SIZE))
    remark = db.Column(db.String(512), nullable=False, default='')
    expired_at = db.Column(db.MYSQL_DATETIME_6)

    withdrawals_enabled = db.Column(db.Boolean, nullable=False, default=False)
    trading_enabled = db.Column(db.Boolean, nullable=False, default=False)

    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    source = db.Column(db.StringEnum(Source), nullable=False, default=Source.SELF)
    source_name = db.Column(db.String(64), nullable=False, default='')

    user = db.relationship('User',
                           backref=db.backref('user_auths', lazy='dynamic'))

    def __repr__(self):
        return f'<{type(self).__name__}' \
               f'({self.id}, {self.access_id!r}, {self.remark!r})>'

    @classmethod
    def new(cls, user_id: int, creator_id: int, allowed_ips: str = '', remark: str = '',
            withdrawals_enabled: bool = True,
            trading_enabled: bool = True,
            ttl: int = None,
            source: Source = Source.SELF,
            source_name: str = '',
            ) -> ApiAuth:
        return db.session_add_and_commit(cls(
                user_id=user_id,
                creator_id=creator_id,
                allowed_ips=allowed_ips,
                remark=remark,
                source=source,
                source_name=source_name,
                expired_at=(None if ttl is None
                            else now() + timedelta(seconds=ttl)),
                withdrawals_enabled=withdrawals_enabled,
                trading_enabled=trading_enabled
                ))

    @property
    def is_expired(self):
        return False if self.expired_at is None else now() >= self.expired_at


class BindingAddress(ModelBase):
    class StatusType(Enum):
        PASS = 'pass'
        DELETE = 'delete'

    __table_args__ = (
            db.UniqueConstraint('user_id', 'address',
                                name='user_id_address_unique'),
            )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    binding_time = db.Column(db.MYSQL_DATETIME_6)
    address = db.Column(db.String(256), nullable=False, index=True)
    remark = db.Column(db.String(256), nullable=False, default='')
    status = db.Column(db.Enum(StatusType), nullable=False)


class VipUser(ModelBase):
    class StatusType(Enum):
        PASS = 'pass'
        DELETE = 'delete'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), unique=True)
    level = db.Column(db.Integer, nullable=False, default=0)
    real_level = db.Column(db.Integer, nullable=False, default=0)

    lock_level = db.Column(db.Integer, nullable=False, default=0)

    status = db.Column(db.Enum(StatusType), nullable=False)
    is_lock = db.Column(db.Boolean, nullable=False, default=False)
    remark = db.Column(db.String(128), nullable=False, default='')
    expired_time = db.Column(db.MYSQL_DATETIME_6)

    user = db.relationship('User', backref=db.backref('vip', lazy='dynamic'))


class InnerMarketMaker(ModelBase):
    """ 内部做市商(admin展示) """

    class StatusType(Enum):
        VALID = "生效中"
        DELETED = "已删除"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    status = db.Column(db.StringEnum(StatusType), nullable=False)
    remark = db.Column(db.String(256), nullable=False, default='')
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'))  # admin最后修改人


class MarketMaker(ModelBase):
    """ 现货|合约做市商 """
    __table_args__ = (
            db.UniqueConstraint('user_id', 'maker_type',
                                name='user_id_maker_type_unique'),
            )

    class StatusType(Enum):
        PASS = 'pass'
        DELETE = 'delete'

    class MakerType(Enum):
        SPOT = 'spot'
        PERPETUAL = 'perpetual'

    class AppraisalType(Enum):
        APPRAISAL = 'appraisal'
        NO_APPRAISAL = 'no_appraisal'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    maker_type = db.Column(db.Enum(MakerType), nullable=False)
    level = db.Column(db.Integer)

    remark = db.Column(db.String(511), nullable=False, default='')
    notice = db.Column(db.Boolean, default=False)

    is_lock = db.Column(db.Boolean, default=False)

    real_level = db.Column(db.Integer, nullable=False, default=0)   # 考核等级
    lock_level = db.Column(db.Integer, nullable=False, default=0)

    expired_time = db.Column(db.MYSQL_DATETIME_6)
    # 做市商是否参与考核
    appraisal_type = db.Column(db.StringEnum(AppraisalType), default=AppraisalType.APPRAISAL, nullable=False)

    status = db.Column(db.Enum(StatusType), nullable=False)

    user = db.relationship('User',
                           backref=db.backref('market_maker', lazy='dynamic'))


class MarketMakerReportSendEmail(ModelBase):
    """ 做市商日报接收邮箱 """

    MAX_NUM = 5  # 单个user 生效中邮箱的最大数目

    class Status(Enum):
        VALID = "生效中"
        DELETED = "已删除"

    __table_args__ = (
        db.UniqueConstraint("email", "user_id", name="email_user_id_unique"),
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    email = db.Column(db.String(64))
    remark = db.Column(db.String(128), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)


class MarketMakerApplication(ModelBase):
    """ 做市商申请 """

    class Type(Enum):
        SPOT = '现货做市商'
        PERPETUAL = '合约做市商'
        SPOT_AND_PERPETUAL = '现货&合约做市商'

    class Status(Enum):
        # 目前只做展示，没有审核功能
        CREATED = 'created'
        # AUDITED = '已审核'
        # REJECTED = '已拒绝'

    class Source(Enum):
        AUTO_DISCOVERY = "自己发现"
        FRIEND_RECOMMEND = "朋友推荐"
        SOCIAL_MEDIA = "社交媒体"
        MARKET_PLATFORM = "行情平台"
        ADVERTISEMENT = "广告"
        OTHER = "其他"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    source = db.Column(db.StringEnum(Source), nullable=False)
    name = db.Column(db.String(64), nullable=False)
    city = db.Column(db.String(256), nullable=False)
    contact_name = db.Column(db.String(64), nullable=False)
    contact_info = db.Column(db.String(128), nullable=False)
    official_website = db.Column(db.String(256), nullable=False)
    remark = db.Column(db.String(128), nullable=False, default='')
    introduction = db.Column(db.Text, nullable=False)
    extra_info = db.Column(db.Text, nullable=False)  # 图片等信息


class AppraisalHistory(ModelBase):

    __table_args__ = (
        db.UniqueConstraint('user_id', 'business_type', 'report_date', name='user_business_report_uniq'),
    )

    class ResultStatus(Enum):
        LEVEL_UP = 'level_up'  # 升级
        LEVEL_DOWN = 'level_down'  # 降级
        NOT_CHANGE = 'not_change'  # 等级未变化
        NOT_LEAST_LEVEL = 'not_least_level'  # 未满足最低要求

    class BusinessType(Enum):
        SPOT = 'spot_market_maker'
        PERPETUAL = 'perpetual_market_maker'
        VIP = 'vip'
        AMBASSADOR = 'ambassador'
        AMBASSADOR_AGENT = 'ambassador_agent'
        MAKERCASHBACK = 'makercashback'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    business_type = db.Column(db.Enum(BusinessType), nullable=False)
    report_date = db.Column(db.Date, nullable=False)
    result_status = db.Column(db.Enum(ResultStatus), nullable=False)
    # {"old_level":1, "new_level": 2, "lock_level": 1}
    result = db.Column(db.String(256), nullable=False)


class UserActivenessHistory(db.Model):

    EPOCH = date(2017, 12, 20)
    EPOCH_ORDINAL = EPOCH.toordinal()
    GROUP_SIZE = 32
    GROUP_CAPACITY = 1 << GROUP_SIZE
    FLAGS_LIMIT = (1 << (GROUP_SIZE - 1)) - 1

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    index = db.Column(db.Integer, nullable=False, index=True)
    flags = db.Column(MYSQL_INTEGER, nullable=False, default=0)
    flags_rev = db.Column(MYSQL_INTEGER, nullable=False, default=0)

    __table_args__ = (
        db.UniqueConstraint('index', 'user_id', name='index_user_uniq'),
    )

    @classmethod
    def date_to_ordinal(cls, date_: Union[date, int]) -> int:
        if isinstance(date_, date):
            date_ = date_.toordinal()
        return date_ - cls.EPOCH_ORDINAL

    @classmethod
    def date_to_offset(cls, date_: Union[date, int]) -> Tuple[int, int]:
        return divmod(cls.date_to_ordinal(date_), cls.GROUP_SIZE)

    @classmethod
    def index_flag_to_date(cls, index: int, flag: int) -> date:
        ep = cls.EPOCH_ORDINAL + index * cls.GROUP_SIZE + cls.flag_to_offset(flag)
        return datetime.fromordinal(ep).date()

    @classmethod
    def index_offset_to_date(cls, index: int, offset: int) -> date:
        ep = cls.EPOCH_ORDINAL + index * cls.GROUP_SIZE + offset
        return datetime.fromordinal(ep).date()

    @classmethod
    def offset_to_flag(cls, offset: int) -> int:
        flag = 1 << offset
        if flag > cls.FLAGS_LIMIT:
            flag -= cls.GROUP_CAPACITY
        return flag

    @classmethod
    def flag_to_offset(cls, flag: int) -> int:
        start = 1 << cls.GROUP_SIZE
        result = cls.GROUP_SIZE
        while start > 0:
            if flag & start:
                return result
            start >>= 1
            result -= 1
        return 0

    @classmethod
    def or_flags(cls, flags: int, offset: int) -> int:
        flags |= (1 << offset)
        if flags > cls.FLAGS_LIMIT:
            flags -= cls.GROUP_CAPACITY
        return flags

    @classmethod
    def reverse_flags(cls, flags: int) -> int:
        result = 0
        for i in range(cls.GROUP_SIZE):
            result <<= 1
            result |= flags & 1
            flags >>= 1
        if result > cls.FLAGS_LIMIT:
            result -= cls.GROUP_CAPACITY
        return result

    @classmethod
    def filter_active_users(cls, start: date = None, end: date = None) -> List[int]:
        # 当天活跃用户有缓存，直接查DB会少数据。改用 business.user.filter_active_users
        if start is None:
            start = cls.EPOCH
        if end is None:
            end = today()
        if start > end:
            raise InvalidArgument(message='查询的开始时间不能大于结束时间！')
        start_index, start_offset = cls.date_to_offset(start)
        end_index, end_offset = cls.date_to_offset(end)
        res = set()
        query_start = start
        for index in range(start_index, end_index+1):
            period_end = cls.index_offset_to_date(index, cls.GROUP_SIZE-1)   # 当前index的最后一天
            query_end = min(end, period_end)
            users = cls._filter_active_users(query_start, query_end, index)
            res |= users
            query_start = cls.index_offset_to_date(index+1, 0)   # 下一个index的第一天

        return list(res)

    @classmethod
    def _filter_active_users(cls, start, end, index):
        """start和end都是同一个index下的时间"""
        res = set()
        start_index, start_offset = cls.date_to_offset(start)
        end_index, end_offset = cls.date_to_offset(end)
        records = cls.query.filter(
            cls.index == index,
            ).with_entities(
            cls.user_id,
            cls.flags
        ).all()
        active_days_to_query = 0
        for i in range(start_offset, end_offset + 1):
            active_days_to_query = cls.or_flags(active_days_to_query, i)    # 所有要查询的日期都放入该变量中
        for user_id, flags in records:
            if flags & active_days_to_query:    # 二者二进制按位与操作为True，说明用户在该时间段是活跃的
                res.add(user_id)
        return res

    @classmethod
    def is_user_active(cls, user_id: int, date_: Union[date, int]) -> bool:
        index, offset = cls.date_to_offset(date_)
        row: cls = cls.query \
            .filter(cls.user_id == user_id,
                    cls.index == index) \
            .first()
        return bool(row is not None and row.flags & (1 << offset))

    @classmethod
    def set_user_active(cls, user_id: int, date_: Union[date, int],
                        *, commit: bool = True):
        index, offset = cls.date_to_offset(date_)
        row: cls = cls.query \
            .filter(cls.user_id == user_id,
                    cls.index == index) \
            .first()
        if row is None:
            row = cls(
                user_id=user_id,
                index=index
            )
            db.session.add(row)
            flags = flags_rev = 0
        else:
            flags = row.flags
            flags_rev = row.flags_rev

        row.flags = cls.or_flags(flags, offset)
        row.flags_rev = cls.or_flags(flags_rev, cls.GROUP_SIZE - 1 - offset)

        if commit:
            db.session.commit()

    @classmethod
    def get_user_latest_active_date(cls, user_id):
        rec = cls.query.filter(
            cls.user_id == user_id
        ).order_by(
            cls.id.desc()
        ).first()
        if not rec:
            return None
        return cls.index_flag_to_date(rec.index, rec.flags)


class OperationLog(ModelBase):

    class Operation(Enum):
        SIGN_IN_ADMIN = 'sign_in_admin'
        SIGN_UP = 'sign_up'
        THIRD_PARTY_SIGN_UP = 'third_party_sign_up'
        THIRD_PARTY_FIRST_SET_LOGIN_PASSWORD = 'third_party_first_set_login_password'
        EDIT_NAME = 'edit_name'
        EDIT_ACCOUNT_NAME = 'edit_account_name'
        EDIT_USER_AVATAR = 'edit_user_avatar'
        ADD_MOBILE = 'add_mobile'
        EDIT_MOBILE = 'edit_mobile'
        RESET_MOBILE = 'reset_mobile'
        ADD_EMAIL = 'add_email'
        EDIT_EMAIL = 'edit_email'
        RESET_EMAIL = 'reset_email'
        ADD_TOTP_AUTH = 'add_totp_auth'
        EDIT_TOTP_AUTH = 'edit_totp_auth'
        RESET_TOTP_AUTH = 'reset_totp_auth'
        ADD_WEBAUTHN = 'add_webauthn'
        EDIT_WEBAUTHN_NAME = 'edit_webauthn_name'
        DELETE_WEBAUTHN = 'delete_webauthn'
        UNBIND_WEBAUTHN = 'unbind_webauthn'
        BIND_THIRD_PARTY_ACCOUNT = 'bind_third_party_account'
        UNBIND_THIRD_PARTY_ACCOUNT = 'unbind_third_party_account'
        BIND_REFERRER = 'bind_referrer'

        EDIT_LOGIN_PASSWORD = 'edit_login_password'
        RESET_LOGIN_PASSWORD = 'reset_login_password'
        ADD_TRADE_PASSWORD = 'add_trade_password'
        EDIT_TRADE_PASSWORD = 'edit_trade_password'
        RESET_TRADE_PASSWORD = 'reset_trade_password'
        TRADE_PASSWORD_SWITCH = 'trade_password_switch'
        FORBID_ACCOUNT = 'forbid_account'
        SIGN_OFF = 'sign_off'

        ADD_WITHDRAW_ADDRESS = 'add_withdraw_address'
        ADD_WITHDRAWAL_APPROVER = 'add_withdrawal_approver'
        DELETE_WITHDRAW_ADDRESS = 'delete_withdraw_address'
        DELETE_WITHDRAW_APPROVER = 'delete_withdrawal_approver'
        ADD_API_WITHDRAW_ADDRESS = 'add_api_withdraw_address'
        DELETE_API_WITHDRAW_ADDRESS = 'delete_api_withdraw_address'
        BATCH_ADD_API_WITHDRAW_ADDRESS = 'batch_add_api_withdraw_address'

        ADD_WITHDRAW_PASSWORD = 'add_withdraw_password'
        EDIT_WITHDRAW_PASSWORD = 'edit_withdraw_password'
        RESET_WITHDRAW_PASSWORD = 'reset_withdraw_password'

        WITHDRAW_PASSWORD_SWITCH = 'withdraw_password_switch'

        ADD_ANTI_PHISHING_CODE = 'add_anti_phishing_code'
        EDIT_ANTI_PHISHING_CODED = 'edit_anti_phishing_code'

        TURN_FEE_SWITCH = 'turn_fee_switch'

        AUTO_MARGIN_LOAN = 'auto_margin_loan'
        AUTO_MARGIN_FLAT = 'auto_margin_flat'

        WEB_LOGIN_IP_LOCKING_SWITCH = 'web_login_ip_locking_switch'
        WEB_SECURITY_LOGIN_DURATION_SWITCH = 'web_security_login_duration_switch'

        UPDATE_APP_FACE_ID_STATUS = 'update_app_face_id_status'
        UPDATE_APP_FINGER_PWD_STATUS = 'update_app_finger_pwd_status'
        UPDATE_APP_GESTURE_PWD_STATUS = 'update_app_gesture_pwd_status'
        ONLY_ALLOW_SMS_VERIFICATION_SWITCH = 'only_allow_sms_verification_switch'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    operation = db.Column(db.String(64), nullable=False)
    platform = db.Column(db.String(64), nullable=False)
    detail = db.Column(db.String(512), nullable=False)

    @classmethod
    def add(cls, user_id: int, operation: Operation, detail: dict | str,
            platform: Union[Enum, str], auto_commit=True):
        if isinstance(platform, Enum):
            platform = platform.value
        if isinstance(detail, dict):
            detail = json.dumps(detail, cls=JsonEncoder, ensure_ascii=False)
        log = cls(
            user_id=user_id,
            operation=operation.name,
            platform=platform,
            detail=detail
        )
        db.session.add(log)
        if not auto_commit:
            return
        db.session.commit()


class SubAccountAssetTransfer(ModelBase):

    class Status(Enum):
        CREATED = 'created'
        DEDUCTED = 'deducted'
        FAILED = 'failed'
        FINISHED = 'success'

    class AccountType(Enum):
        SPOT = "spot"
        PERPETUAL = "perpetual"

    main_user_id = db.Column(db.Integer, nullable=False, index=True)  # 主账号ID, 用于查询
    source = db.Column(db.ForeignKey('user.id'), nullable=False)
    source_account_type = db.Column(db.StringEnum(AccountType), nullable=False)
    target = db.Column(db.ForeignKey('user.id'), nullable=False)
    target_account_type = db.Column(db.StringEnum(AccountType), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)


class UserFollowMarket(ModelBase):

    class TradeType(Enum):
        SPOT = 'spot'
        DIRECT_PERPETUAL = 'direct_perpetual'
        INVERSE_PERPETUAL = 'inverse_perpetual'

    MIX_PERPETUAL_TRADE_TYPE = 'perpetual'  # 正向反向合约


    class EditUserType(Enum):
        SYSTEM = 'system'
        USER = 'user'

    market_type = db.Column(db.String(32), nullable=False)
    trade_type = db.Column(db.Enum(TradeType), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    rank = db.Column(db.Integer)
    edited_by = db.Column(db.StringEnum(EditUserType), nullable=False, default=EditUserType.USER)


class UserMarketFloatWindows(ModelBase):

    class DisplayType(Enum):
        WHEEL = 'wheel'
        PAGE = 'page'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    market_float_windows = db.Column(db.JSON, nullable=False, default=[])
    display_type = db.Column(db.StringEnum(DisplayType), nullable=False, default=DisplayType.PAGE)
    # 分页条数
    page_limit = db.Column(db.Integer, nullable=False, default=5)
    # 轮播间隔，单位s
    wheel_interval = db.Column(db.Integer, nullable=False, default=5)
    # 是否开启涨跌幅
    rate_status = db.Column(db.Boolean, nullable=False, default=True)


class BigCustomer(ModelBase):
    # 已弃用，新表为BigUserCustomer

    class Type(Enum):
        POSITION = '持仓大户'
        TRADE = '交易大户'
        POTENTIAL = '潜在大户'
        LOSS = '流失大户'

    class Attention(Enum):
        LOW = '低'
        MIDDLE = '中'
        HIGH = '高'
        NO_CARE = '不关注'
        NO = '未处理'

    class Status(Enum):
        VALID = '生效中'
        DELETED = '已删除'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    type = db.Column(db.Enum(Type), nullable=False)
    attention = db.Column(
        db.Enum(Attention), nullable=False, default=Attention.NO)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    cet_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    market_value = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    thirty_avg_cet_amount = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    thirty_avg_market_value = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    thirty_trade_amount = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    remark = db.Column(db.Text, nullable=False, default='')

    user = db.relationship('User',
                           backref=db.backref('big_customer', lazy='dynamic'))


class BigUserCustomer(ModelBase):

    class StatusType(Enum):
        PASS = '有效'
        INVALID = '无效'
        DELETED = '已删除'

    class TagType(Enum):
        IS_BIG_CET_TYPE = 'CET'
        IS_BIG_BALANCE_TYPE = '资产'

    user_id = db.Column(db.Integer, unique=True)
    business_user_id = db.Column(db.Integer, nullable=True)
    remark = db.Column(db.String(256), nullable=False, default='')
    status = db.Column(db.StringEnum(StatusType), nullable=False, default=StatusType.PASS)
    # 当前CET数量
    cet_amount = db.Column(db.MYSQL_DECIMAL_26_8)
    # 当前资产USD
    balance_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 历史峰值USD
    highest_balance_usd = db.Column(db.MYSQL_DECIMAL_26_8)
    # 大客户标签
    is_big_cet_type = db.Column(db.Boolean)
    is_big_balance_type = db.Column(db.Boolean)
    spot_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    perpetual_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    @classmethod
    def is_big_customer(cls, user_id):
        row = cls.query.filter(
            cls.user_id == user_id,
            cls.is_big_balance_type.is_(True),
            cls.status == cls.StatusType.PASS,
        ).first()
        return True if row else False


class BigCustomerSetting(ModelBase):
    """大客户参数配置"""
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)

    status = db.Column(db.Enum(Status), nullable=False, index=True,
                       default=Status.VALID)


class UserBindingAccount(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('account_id', 'account_type',
                            name='account_id_account_type'),)

    class AccountType(Enum):
        TELEGRAM = 'telegram'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    account_type = db.Column(db.Enum(AccountType), nullable=False)
    account_id = db.Column(db.String(128), nullable=False)
    account_name = db.Column(db.String(128), nullable=False)
    extra = db.Column(db.Text)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


    @classmethod
    def add_or_update(cls, user_id: int, account_type: AccountType,
                      account_id: str, account_name: str, extra: str = None):
        row = cls.query.filter(
            cls.account_id == account_id ,
            cls.account_type == account_type
        ).first()
        if row:
            if row.status == cls.Status.VALID and row.user_id != user_id:
                raise AccountBoundByAnOther
            row.user_id = user_id
            row.account_name = account_name
            row.extra = extra
            row.status = cls.Status.VALID
            db.session.commit()
        else:
            row = cls.query.filter(
                cls.user_id == user_id,
                cls.account_type == account_type
            ).first()
            if row:
                row.account_id = account_id
                row.account_name = account_name
                row.extra = extra
                row.status = cls.Status.VALID
            else:
                row = cls(
                    user_id=user_id,
                    account_type=account_type,
                    account_id=account_id,
                    account_name=account_name,
                    extra=extra
                )
                db.session.add(row)
            db.session.commit()

    @classmethod
    def reset_binding_account(cls, user_id: int, account_type: AccountType):
        row = cls.query.filter(
            cls.user_id == user_id,
            cls.account_type == account_type,
            cls.status == cls.Status.VALID
        ).first()
        if row:
            row.status = cls.Status.DELETED
        db.session.commit()

    @classmethod
    def get_binding_account(cls, user_id: int, account_type: AccountType):
        return cls.query.filter(
            cls.user_id == user_id,
            cls.account_type == account_type,
            cls.status == cls.Status.VALID
        ).first()


class MarketPriceAlert(ModelBase):
    MARKET_ALERTS_COUNT = 50
    TOTAL_ALERTS_COUNT = 500

    class Direction(Enum):
        RISE = 'rise'
        FALL = 'fall'

    class Status(Enum):
        VALID = 'valid'
        INVALID = 'invalid'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    market = db.Column(db.String(32), nullable=False, index=True)
    price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)
    direction = db.Column(db.Enum(Direction), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False)


class MarketPriceNotice(ModelBase):
    MARKET_ALERTS_COUNT = 10
    TOTAL_ALERTS_COUNT = 60

    class Direction(Enum):
        RISE = 'rise'
        FALL = 'fall'

    class NoticeType(Enum):
        PRICE = 'price'  # 价格涨跌
        RATE = 'rate'  # 涨跌幅

    class RuleType(IntEnum):
        PRICE_RISE = 1
        PRICE_FALL = 2
        RATE_RISE = 3
        RATE_FALL = 4

    RULE_MAP = dict(
        PRICE_RISE=dict(direction=Direction.RISE.name, notice_type=NoticeType.PRICE.name),
        PRICE_FALL=dict(direction=Direction.FALL.name, notice_type=NoticeType.PRICE.name),
        RATE_RISE=dict(direction=Direction.RISE.name, notice_type=NoticeType.RATE.name),
        RATE_FALL=dict(direction=Direction.FALL.name, notice_type=NoticeType.RATE.name),
    )

    class TradeType(Enum):
        SPOT = 'spot'
        PERPETUAL = 'perpetual'

    class TTLType(Enum):
        ONCE = 'once'
        DAILY = 'daily'
        ALWAYS = 'always'

    class State(Enum):  # 记录生效状态
        OPEN = 'open'
        CLOSE = 'close'

    class Status(Enum):  # 逻辑删除
        VALID = 'valid'
        INVALID = 'invalid'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    market = db.Column(db.String(32), nullable=False, index=True)
    trade_type = db.Column(db.StringEnum(TradeType), nullable=False)
    ttl_type = db.Column(db.StringEnum(TTLType), nullable=False)
    rule = db.Column(db.StringEnum(RuleType), nullable=False)
    value = db.Column(db.String(64), nullable=False)
    noticed_at = db.Column(db.MYSQL_DATETIME_6)
    status = db.Column(db.StringEnum(Status), nullable=False)
    state = db.Column(db.StringEnum(State), nullable=False)


class UserLastActiveness(ModelBase):
    """用户最近一次活跃时间"""
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, index=True, unique=True)
    last_active_date = db.Column(db.Date, nullable=False, index=True)


class UserStatusChangeHistory(ModelBase):
    """用户状态变更记录"""

    class Type(Enum):
        # 不全
        VIP = "VIP"
        AMBASSADOR = "AMBASSADOR"
        AMBASSADOR_AGENT = "AMBASSADOR_AGENT"
        TEAM_USER = "TEAM_USER"  # 团队。待移除
        BUSINESS_USER = "BUSINESS_USER"  # 商务。待移除
        P2P_T_PLUS_N = "P2P_T_PLUS_N"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    type = db.Column(db.String(32), nullable=False)  # 类型：做市商 / VIP / 费率变更等
    action = db.Column(db.String(32), nullable=False)
    admin_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    detail = db.Column(db.Text, nullable=False, default='')


class ZendeskUserTags(ModelBase):
    """zendesk用户标签表"""

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, nullable=False, index=True, unique=True)
    # zendesk_id需要用bigint存储
    zendesk_id = db.Column(db.BigInteger, nullable=False, index=True)
    tags = db.Column(db.String(256), nullable=False, default='')


class UserGuideHistory(ModelBase):
    """ 用户-新手指引历史 """

    class Type(Enum):
        FLOATING_LAYER = "FloatingLayer"  # 浮层

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    type = db.Column(db.String(64), nullable=False, default=Type.FLOATING_LAYER.name)  # 指引类型, 字符串
    location = db.Column(db.String(64), nullable=False)  # 触发页面
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


class UserSpecialConfigChangeLog(ModelBase):

    class SpecialConfigType(Enum):
        FEE = '特殊费率'
        CET_DISCOUNT = 'CET 特殊折扣'
        REFERRAL_RATE = '特殊返佣比例'
        MARGIN_DAILY_RATE = '特殊杠杆日息'
        WITHDRAWAL_WHITELIST = '提现白名单'
        NO_DELAY_WHITELIST = '免延迟审核'
        WITHDRAWAL_PRIVILEGED_WHITELIST = '特殊提现白名单'
        DAILY_WITHDRAWAL_LIMIT = '特殊提现额度'
        MARGIN_LOAN_LIMIT = '特殊借币额度'
        MARGIN_ACCOUNT_LOAN_LIMIT = '特殊借币额度-市场'
        FREQUENCY_LIMIT = 'api限频'
        ONLY_WITHDRAWAL_WHITELIST = '清退白名单'
        ONLY_WITHDRAWAL = '仅支持提现用户'
        CLEARED_USER = '清退用户'
        IEO_WHITELIST = '投资用户白名单'
        SUB_ACCOUNT_LIMIT = '子账号上限'
        BUSINESS_ACCOUNT = '业务账号配置'
        API_SPOT_TRADING_WHITELIST = 'API现货交易白名单'

    class OpType(Enum):
        CREATE = '增加'
        UPDATE = '修改'
        DELETE = '删除'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    config_type = db.Column(db.String(16), nullable=False, index=True)
    op_type = db.Column(db.String(8), nullable=False, index=True)
    change_detail = db.Column(db.String(1024), nullable=False)
    change_remark = db.Column(db.String(1024), nullable=False, default='')
    admin_user_id = db.Column(db.Integer, nullable=False)
    op_id = db.Column(db.Integer, nullable=True)    # 操作记录的id

    @classmethod
    def add(cls,
            user_id: int,
            config_type: SpecialConfigType,
            op_type: OpType,
            admin_user_id: int,
            change_detail: str,
            change_remark: str = '',
            op_id: int = None
            ):
        log = cls(
            user_id=user_id,
            config_type=config_type.value,
            op_type=op_type.value,
            change_detail=change_detail,
            change_remark=change_remark or '',
            admin_user_id=admin_user_id,
            op_id=op_id
        )
        db.session_add_and_commit(log)


class OnlyWithdrawalWhitelistUser(ModelBase):
    """ 仅支持提现白名单(免清退白名单) """

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, unique=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(128), default="")

    user = db.relationship("User", foreign_keys=[user_id])

    def _row_to_dict_hook_(self, dict_: dict):
        user = self.user
        dict_.update(
            user_name=user.name,
            user_email=user.email,
            user_mobile=user.mobile,
        )


class OnlyWithdrawalUser(ModelBase):

    #  TODO: after migration data, then drop table

    """ 仅支持提现名单 """

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, unique=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(128), default="")

    user = db.relationship("User", foreign_keys=[user_id])

    def _row_to_dict_hook_(self, dict_: dict):
        user = self.user
        dict_.update(
            user_name=user.name,
            user_email=user.email,
            user_mobile=user.mobile,
        )


class UserBusinessRecord(ModelBase):

    class Business(Enum):
        SPOT_TRADE = 'spot_trade'
        PERPETUAL_TRADE = 'perpetual_trade'
        DEPOSIT = 'deposit'

    report_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    # 历史所有用户bitmap
    history_user_bit_map = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)
    # 周期内用户bitmap
    new_user_bit_map = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)
    business = db.Column(db.Enum(Business), nullable=False)

    __table_args__ = (
        db.Index('business_report_at_idx', 'business', 'report_at'),
    )

    def get_history_users(self):
        if not self.history_user_bit_map:
            return []
        bm = BitMap.deserialize(self.history_user_bit_map)
        if not bm:
            return []
        return list(bm)


class UserApiFrequencyLimitRecord(ModelBase):
    """
    用户api限频配置
    """
    DEFAULT_LIMIT = 1000

    __table_args__ = (
        db.Index('idx_user_id_status', 'user_id', 'status'),
    )

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    class ApiGroups(Enum):
        ORDER = "下单&改单"
        ORDERS = "批量下单"
        QUERY_ORDER = "查询订单"
        QUERY_ORDER_HISTORY = "查询订单历史"
        ACCOUNT = "账户改动"
        QUERY_ACCOUNT = "账户查询"
        QUERY_ACCOUNT_HISTORY = "账户历史数据查询"

        PERPETUAL_ORDER = "合约下单&改单"
        PERPETUAL_ORDERS = "合约批量下单"
        PERPETUAL_QUERY_ORDER = "合约查询订单"
        PERPETUAL_QUERY_ORDER_HISTORY = "合约查询订单历史"
        PERPETUAL_QUERY_ACCOUNT = "合约账户查询"

        CANCEL = "撤单"
        CANCELS = "批量撤单"
        PERPETUAL_CANCEL = "合约撤单"
        PERPETUAL_CANCELS = "合约批量撤单"

        VERSION_APIV1 = "API V1"
        VERSION_APIV2 = "API V2"

    user_id = db.Column(db.Integer, nullable=False)
    limit_count = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    group = db.Column(db.StringEnum(ApiGroups), nullable=False)
    remark = db.Column(db.String(256), nullable=False, default='')


class UserLongLimiterRecord(ModelBase):
    """长周期用户特殊配置"""
    DEFAULT_LONG_PERIOD_HOUR = 1
    DEFAULT_LOW_RATE_SECOND = 1

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    user_id = db.Column(db.Integer, nullable=False)
    group = db.Column(db.StringEnum(UserApiFrequencyLimitRecord.ApiGroups), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    # 限制周期 单位Hour
    long_period_hour = db.Column(db.Integer, nullable=False)
    limit_total_count = db.Column(db.Integer, nullable=False)
    low_rate_second = db.Column(db.Integer, nullable=False)
    remark = db.Column(db.String(256), nullable=False, default='')


class UserApiSpeedRecord(ModelBase):
    """用户 API加速 特殊配置"""
    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    user_id = db.Column(db.Integer, index=True, nullable=False)
    group = db.Column(db.StringEnum(UserApiFrequencyLimitRecord.ApiGroups), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    ms = db.Column(db.Integer, nullable=False, default=0)
    remark = db.Column(db.String(256), nullable=False, default='')


class ApiTradingWhiteList(ModelBase):

    class Type(Enum):
        SPOT = '现货做市商'
        PERPETUAL = '合约做市商'

    class Status(Enum):
        VALID = '生效中'
        DELETED = '已删除'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    market_type = db.Column(db.StringEnum(Type), nullable=False)
    allowed_markets = db.Column(db.Text, nullable=False)  # empty string means not markets allowed
    remark = db.Column(db.String(256), nullable=False, default='')
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    @property
    def markets(self) -> List[str]:
        if not self.allowed_markets:
            return []
        markets = self.allowed_markets.split(',')
        return markets

    @markets.setter
    def markets(self, markets: List[str]):
        if not markets:
            self.allowed_markets = ''
            return
        self.allowed_markets = ','.join(markets)


class ClearedUser(ModelBase):

    class Status(Enum):
        FORBIDDEN = '已清退'
        WITHDRAWAL_ONLY = '仅提现'

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, unique=True)
    status = db.Column(db.StringEnum(Status), nullable=False)
    valid = db.Column(db.Boolean, nullable=False, default=True)
    remark = db.Column(db.String(256), nullable=True, default='')

    user = db.relationship("User", foreign_keys=[user_id])

    def _row_to_dict_hook_(self, dict_: dict):
        user = self.user
        dict_.update(
            user_name=user.name,
            user_email=user.email,
            user_mobile=user.mobile,
        )

    @classmethod
    def get_user_ids(cls, status: Optional[Status] = None,
                     filter_user_ids: Optional[Iterable[int]] = None) -> Set[int]:
        batch_size = 5000
        result = set()
        query = cls.query.filter(cls.valid.is_(True))
        if status:
            query = query.filter(cls.status == status)
        if filter_user_ids:
            for batch_user_ids in batch_iter(filter_user_ids, batch_size):
                u_ids = {v.user_id
                         for v in query.filter(cls.user_id.in_(batch_user_ids)).with_entities(cls.user_id)}
                result |= u_ids
        else:
            result |= {v.user_id for v in query.with_entities(cls.user_id)}
        return result


class TaxExportHistory(ModelBase):
    """报税数据导出记录"""
    MONTHLY_EXPORT_LIMIT = 6   # 每个用户每月限制导出条数
    EXPIRE_DAYS = 7     # 数据导出后的有效期7天
    MAX_EXPORT_COUNT = 50000     # 导出的最大条数
    TTL_HOUR = 2    # 已超过2小时仍未生成报告的导出记录

    class Status(Enum):
        PENDING = 'pending'
        FINISHED = 'finished'
        EXPIRED = 'expired'

    class Account(Enum):
        ALL = 'ALL'
        SPOT = 'spot'
        MARGIN = 'margin'
        INVESTMENT = 'investment'
        FUTURE = 'future'

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False)
    start_time = db.Column(db.Integer, nullable=False)
    end_time = db.Column(db.Integer, nullable=False)
    account = db.Column(db.StringEnum(Account), nullable=False, default=Account.ALL)
    asset = db.Column(db.String(128), nullable=False, default='ALL')
    hide_transfer = db.Column(db.Boolean, nullable=False, default=False)
    file_url = db.Column(db.String(512), default='')
    finished_at = db.Column(db.MYSQL_DATETIME_6)


class SignOffUser(ModelBase):
    """
    注销用户
    """
    user_id = db.Column(db.Integer, nullable=False, unique=True)
    # 注销时邮箱
    email = db.Column(db.String(64), nullable=False)
    # 注销时手机号
    mobile_num = db.Column(db.String(32))
    # 注销时资产
    balance_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    # 客户端设备登出
    has_device_logged_out = db.Column(db.Boolean, nullable=False, default=False)
    # 余量资产划转
    has_asset_cleared_off = db.Column(db.Boolean, nullable=False, default=False)

    @classmethod
    def get_user_ids(cls,
                     filter_user_ids: Optional[Iterable[int]] = None) -> Set[int]:
        batch_size = 3000
        result = set()
        query = cls.query
        if filter_user_ids:
            for batch_user_ids in batch_iter(filter_user_ids, batch_size):
                u_ids = {v.user_id
                         for v in query.filter(cls.user_id.in_(batch_user_ids)).all()}
                result |= u_ids
        else:
            result |= {v.user_id for v in query}
        return result

    @property
    def hidden_email(self) -> str:
        return hide_email(self.email)
    
    @classmethod
    def is_signoff_user(cls, user_id: int) -> bool:
        return cls.query.filter(cls.user_id == user_id).first() is not None


class SignOffUserBalanceTransferHistory(ModelBase):
    """
    注销用户资产转移记录
    """
    class Status(Enum):
        CREATED = "created"
        DEDUCTED = "deducted"
        FINISHED = "finished"

    user_id = db.Column(db.Integer, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    finished_at = db.Column(db.MYSQL_DATETIME_6, index=True)  # 历史数据为null（划转到admin的）


class CleanedBalanceTransferHistory(ModelBase):

    class Status(Enum):
        CREATED = "created"
        CANCELLED = 'cancelled'
        FINISHED = "finished"

    user_id = db.Column(db.Integer, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    account = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    finished_at = db.Column(db.MYSQL_DATETIME_6, index=True)


class UserShareWindowRecord(ModelBase):
    __table_args__ = (
        db.Index('ix_created_at', 'created_at'),
    )

    class PopType(Enum):
        first_spot_trade = "首次币币成交"

        first_position_finished = "首次正收益合约完全平仓"  # 2024/12/20 之前需求是首次合约完全平仓
        first_perpetual_50_percent_profit = "合约收益率首次突破50%"
        first_perpetual_100_percent_profit = "合约收益率首次突破100%"
        first_perpetual_200_percent_profit = "合约收益率首次突破200%"
        first_perpetual_300_percent_profit = "合约收益率首次突破300%"
        first_perpetual_400_percent_profit = "合约收益率首次突破400%"
        first_perpetual_500_percent_profit = "合约收益率首次突破500%"
        first_perpetual_1000_percent_profit = "合约收益率首次突破1000%"
        highest_perpetual_profit_rate = "合约最高收益率"

        # 第二期（前端弹窗）
        first_auto_invest = "首次定投"
        first_grid = "首次网格"
        first_exchange = "首次兑换"
        first_amm = "首次AMM"
        first_investment = "首次理财"
        first_pledge = "首次借贷"
        first_margin = "首次杠杆"
        first_cet_2000 = "资产首次大于 2000 CET"

        # 第三期
        investment_amount = "理财收益突破"    # 500/3000/10000
        stake_amount = "质押收益突破"     # 500/3000/10000
        first_vip = "首次VIP"
        first_launch_pool = "首次获得挖矿收益"

        user_anniversary = "用户注册x周年"
        spot_profit_rate = "盈亏率"  # 0.2/0.3/0.5/1

    class Status(Enum):
        PENDING = "待弹窗"
        POPPED = "已弹窗"

    user_id = db.Column(db.Integer, index=True, nullable=False)
    pop_type = db.Column(db.StringEnum(PopType), index=True, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.POPPED, index=True)
    detail = db.Column(db.JSON, default=dict(), nullable=True)


class UserPopWindowRecord(ModelBase):
    """ !!!已废弃，后续请使用 UserShareWindowsRecord """

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    first_spot_trade = db.Column(db.Boolean, default=False)
    first_position_finished = db.Column(db.Boolean, default=False)
    first_perpetual_50_percent_profit = db.Column(db.Boolean, default=False)
    first_perpetual_100_percent_profit = db.Column(db.Boolean, default=False)
    first_perpetual_200_percent_profit = db.Column(db.Boolean, default=False)
    first_perpetual_300_percent_profit = db.Column(db.Boolean, default=False)
    first_perpetual_400_percent_profit = db.Column(db.Boolean, default=False)
    first_perpetual_500_percent_profit = db.Column(db.Boolean, default=False)
    first_perpetual_1000_percent_profit = db.Column(db.Boolean, default=False)
    highest_perpetual_profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, default=0)

    # 前端上报
    first_auto_invest = db.Column(db.Boolean, default=False)
    first_grid = db.Column(db.Boolean, default=False)
    first_exchange = db.Column(db.Boolean, default=False)
    first_amm = db.Column(db.Boolean, default=False)
    first_investment = db.Column(db.Boolean, default=False)
    first_pledge = db.Column(db.Boolean, default=False)
    first_margin = db.Column(db.Boolean, default=False)
    first_cet_2000 = db.Column(db.Boolean, default=False)


class UserWaitingPopWindow(ModelBase):
    """ !!!已废弃"""

    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    pop_type = db.Column(db.StringEnum(UserShareWindowRecord.PopType), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    detail = db.Column(db.String(256), nullable=False)


class PopWindowDumpHistory(ModelBase):
    """币币订单表order_history/合约仓位表position_history最新同步"""

    class Type(Enum):
        SPOT_ORDER = '币币订单'
        PERPETUAL_POSITION = '合约仓位'
        VIP = "首次VIP"
        LAUNCH_POOL = "首次挖矿收益"

    dump_type = db.Column(db.StringEnum(Type), nullable=False)
    dump_db = db.Column(db.Integer, nullable=False, default=0)
    dump_table = db.Column(db.Integer, nullable=False, default=0)
    dump_id = db.Column(db.Integer, nullable=False)


class UserBalanceMerkleConfig(ModelBase):

    assets = db.Column(db.String(256), nullable=False)
    chain = db.Column(db.String(32), nullable=False)
    addresses = db.Column(db.Text)


class UserBalanceMerkleInfo(ModelBase):

    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'

    snapshot_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    user_balances = db.Column(db.String(1024)) # {asset: balance}
    wallet_balances = db.Column(db.MYSQL_MEDIUM_TEXT) # [{asset, chain, balances: {address: balance}}, ...], 只包含默克尔证明的资产
    block_heights = db.Column(db.String(2014)) # {chain: height}
    prices = db.Column(db.Text)  # {asset: price}
    wallet_total_balances = db.Column(db.MYSQL_MEDIUM_TEXT)  # [{asset, chain, balance}, ...], 包含钱包所有资产，新增字段，旧数据没有
    wallet_proof_file = db.Column(db.String(2014))  # 该字段手动填入
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)

    @classmethod
    def get_finish(cls):
        return cls.query.filter(
            cls.status == cls.Status.FINISHED
        ).order_by(cls.snapshot_time.desc())

    @classmethod
    def get_latest(cls):
        return cls.get_finish().first()

    @classmethod
    def get_all(cls):
        return cls.get_finish().limit(20).all()

    def get_asset_balances(self) -> Dict[str, Decimal]:
        result = {}
        data = json.loads(self.wallet_balances)
        for x in data:
            a = x['asset']
            v = sum(Decimal(v) for v in x['balances'].values())
            if a in result:
                result[a] += v
            else:
                result[a] = v
        return result

    def sum_wallet_total_balances(self) -> Decimal:
        """获取钱包所有资产总市值"""
        if not self.wallet_total_balances:
            return Decimal()
        data = json.loads(self.wallet_total_balances)
        prices = json.loads(self.prices)
        total = Decimal()
        for item in data:
            total += Decimal(prices.get(item['asset'], 0)) * Decimal(item['balance'])
        return quantize_amount(total, 2)

    def sum_snapshot_balances(self) -> Dict[str, Decimal]:
        """用于修改snapshot后重新计算self.user_balances"""
        r = {}
        rows = UserBalanceMerkleSnapshot.get_snapshot_records(self.id)
        for row in rows:
            data = json.loads(row.balances)
            for k, v in data.items():
                v = Decimal(v)
                if k in r:
                    r[k] += v
                else:
                    r[k] = v
        return r

    def add_external_wallet_balance(self, asset: str, chain: str, addrs: dict):
        """添加质押等额外的钱包资产"""
        new = json.loads(self.wallet_balances)
        for x in new:
            if x['asset'] == asset and x['chain'] == chain:
                for addr, amount in addrs.items():
                    if addr in x['balances']:
                        x['balances'][addr] = Decimal(x['balances'][addr]) + Decimal(amount)
                    else:
                        x['balances'][addr] = amount
        self.wallet_balances = json.dumps(new, cls=JsonEncoder)
        db.session.commit()


class UserBalanceMerkleSnapshot(ModelBase):

    snapshot_id = db.Column(db.Integer, nullable=False)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    nonce = db.Column(db.String(64), nullable=False)
    balances = db.Column(db.String(1024), nullable=False)

    @classmethod
    def get_snapshot_records(cls, snapshot_id: int) -> list['UserBalanceMerkleSnapshot']:
        row = UserBalanceMerkleSnapshot.query.filter(
            UserBalanceMerkleSnapshot.snapshot_id == snapshot_id
        ).order_by(UserBalanceMerkleSnapshot.id.desc()).first()
        if not row:
            return []

        last = row.id + 1
        limit = 30000
        result = []
        while True:
            rows = UserBalanceMerkleSnapshot.query.filter(
                UserBalanceMerkleSnapshot.id < last
            ).order_by(UserBalanceMerkleSnapshot.id.desc()) \
             .with_hint(UserBalanceMerkleSnapshot, "FORCE INDEX(PRI)").limit(limit).all()
            # 此处假设同一次快照的数据id是连续的
            has_next = False
            for row in rows:
                if row.snapshot_id == snapshot_id:
                    has_next = True
                    result.append(row)
            if len(rows) != limit or not has_next:
                break
            last = rows[-1].id
        return result


class UserBalanceMerkleTree(ModelBase):

    snapshot_id = db.Column(db.Integer, nullable=False)
    hash = db.Column(db.String(64), nullable=False, unique=True)
    parent_id = db.Column(db.Integer, index=True)   # null for root node
    is_left = db.Column(db.Boolean, nullable=False)
    balances = db.Column(db.String(1024), nullable=False)

    @classmethod
    def get_root_hash(cls, snapshot_id: int) -> str:
        row = cls.query.filter(
            cls.snapshot_id == snapshot_id,
            cls.parent_id.is_(None)
        ).first()
        return row.hash


class RiskUserSource(ModelBase):
    """ 冻结用户来源 """

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    class Source(Enum):
        ADMIN_FORBID = "客服Admin冻结"
        RISK_CONTROL_FORBID = "用户触发风控"
        MANUAL_BATCH_FORBID = "研发手动冻结"
        USER_SIGN_OFF = "用户自己注销"
        USER_FREEZE = "用户自己冻结"
        RESET_SECURITY = "重置安全工具"
        RISK_CONTROL_BATCH_FORBID = "风控批量冻结"

    user_id = db.Column(db.Integer, nullable=False, unique=True)
    source = db.Column(db.StringEnum(Source), nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(128), nullable=False, default="")


class ThirdPartyAccount(ModelBase):
    """
    第三方账号
    """
    class Source(Enum):
        GOOGLE = 'Google'
        APPLE = 'Apple'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    source = db.Column(db.StringEnum(Source), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    name = db.Column(db.String(64), nullable=False)
    # 第三方账号的唯一标识
    third_party_id = db.Column(db.String(256), nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), default=Status.VALID)


class AccountTransferLog(ModelBase):
    """ 用户账户维度的划转历史
    不用做实际的划转，无状态，只作为日志
    """

    class AccountType(Enum):
        SPOT = _("现货账户")
        PERPETUAL = _("合约账户")
        MARGIN = _("杠杆账户")
        INVESTMENT = _("理财账户")
        STAKING = "质押理财账户"
        SUB = "子账户"

    __table_args__ = (
        db.Index('user_id_created_at', 'user_id', 'created_at'),
    )

    created_at = db.Column(db.MYSQL_DATETIME_6, default=now)
    user_id = db.Column(db.Integer, nullable=False)
    source_account_type = db.Column(db.StringEnum(AccountType), nullable=False)
    target_account_type = db.Column(db.StringEnum(AccountType), nullable=False)
    source_account_id = db.Column(db.Integer, nullable=True)  # 杠杠账户时有用，合约账户是null
    target_account_id = db.Column(db.Integer, nullable=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    transfer_id = db.Column(db.Integer, nullable=False)    # 对应某个划转记录表的id


class P2pMerchant(ModelBase):
    """p2p商家"""

    class Status(Enum):
        ACTIVE = "active"
        INACTIVE = "inactive"
        CANCELING = "canceling"  # 保证金赎回中，此时不允许重新申请
        CANCELED = "cancel"   # 取消身份(仍保留商家查看功能)
        FORBID = "forbid"  # 被禁用（停止赎回保证金）

    class ShopStatus(Enum):
        OPEN = "open"
        CLOSED = "closed"

    class MarginStatus(Enum):
        VALID = "有效"
        INVALID = "无效"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    status = db.Column(db.StringEnum(Status), default=Status.ACTIVE, nullable=False)
    shop_status = db.Column(db.StringEnum(ShopStatus), index=True, default=ShopStatus.CLOSED, nullable=False)  # 店铺状态
    remark = db.Column(db.String(128))
    contact = db.Column(db.String(256))  # 联系方式
    self_introduction = db.Column(db.Text)  # 自我介绍
    margin_status = db.Column(db.StringEnum(MarginStatus), default=MarginStatus.INVALID, nullable=False,
                              comment="保证金状态")
    canceling_time = db.Column(db.MYSQL_DATETIME_6, index=True, comment="发起取消时间")

    @classmethod
    def trans_margin_status(cls, val: bool):
        return cls.MarginStatus.VALID if val else cls.MarginStatus.INVALID

    @property
    def is_invalid(self):
        return (self.status != self.Status.ACTIVE
                or self.shop_status == self.ShopStatus.CLOSED
                or self.margin_status == self.MarginStatus.INVALID
                )

    @property
    def is_inactive(self):
        return self.status in {self.Status.INACTIVE, self.Status.FORBID}

    @property
    def is_cancel(self):
        return self.status in {self.Status.CANCELING, self.Status.CANCELED}


class P2pUser(ModelBase):
    """p2p用户"""

    class Status(Enum):
        ACTIVE = "active"
        INACTIVE = "inactive"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    status = db.Column(db.StringEnum(Status), default=Status.ACTIVE, nullable=False)
    # TODO 待删除 统一使用 user_extra 中的 name
    nickname = db.Column(db.String(125))
    profile_photo_url = db.Column(db.String(256), default="")  # 暂时不支持
    merchant_id = db.Column(db.Integer)  # 商家ID
    biz_user_id = db.Column(db.String(32), unique=True)

    @property
    def is_merchant(self):
        return bool(self.merchant_id)

    @property
    def completion_rate(self):
        from app.models.p2p import P2pUserTradeSummary
        summary = P2pUserTradeSummary.query.filter(P2pUserTradeSummary.user_id == self.user_id).first()
        return summary.completion_rate if summary else Decimal()

    @classmethod
    def get_by_user_id(cls, user_id):
        return cls.query.filter(cls.user_id == user_id).first()

    @classmethod
    def get_users_map(cls, *user_ids):
        users = cls.query.filter(cls.user_id.in_(user_ids)).all()
        return {u.user_id: u for u in users}


class ImUser(ModelBase):
    class UserType(Enum):
        P2P = "p2p"

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    __table_args__ = (db.UniqueConstraint('user_id', 'user_type', name="uni_user_id_user_type"),)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, index=True)
    user_type = db.Column(db.StringEnum(UserType), default=UserType.P2P, nullable=False)
    status = db.Column(db.StringEnum(Status), default=Status.VALID, nullable=False)
    im_user_id = db.Column(db.String(64), nullable=False, unique=True)

    @classmethod
    def gen_new_im_user_id(cls):
        while True:
            new_id = new_hex_token(8)
            if not cls.query.filter(cls.im_user_id == new_id).first():
                return new_id

    @classmethod
    def get_p2p_by_user_id(cls, user_id):
        return cls.query.filter(cls.user_id == user_id, cls.user_type == cls.UserType.P2P).first()


class MaskUser(ModelBase):
    __table_args__ = (db.UniqueConstraint('user_id', 'mask_id', name="user_mask_id"),)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    mask_id = db.Column(db.String(64), nullable=False, unique=True)

    @classmethod
    def get_or_create(cls, user_id):
        row = cls.query.filter(
                cls.user_id == user_id
            ).first()
        if not row:
            row = cls.new_mask_user(user_id)
        return row

    @classmethod
    def new_mask_user(cls, user_id):
        new_mask_id = cls.gen_new_mask_id()
        row = cls(
            user_id=user_id,
            mask_id=new_mask_id,
        )
        db.session_add_and_commit(row)
        return row

    @classmethod
    def gen_new_mask_id(cls):
        while True:
            new_id = new_hex_token(8)
            if not cls.query.filter(cls.mask_id == new_id).first():
                return new_id

    @classmethod
    def get_mask_user_mapping(cls, user_ids: list | set) -> {int: str}:
        ret = {}
        for chunk_user_ids in batch_iter(user_ids, 2000):
            rows = cls.query.with_entities(
                cls.user_id,
                cls.mask_id,
            ).filter(
                cls.user_id.in_(chunk_user_ids)
            ).all()
            ret.update(dict(rows))
        return ret

    @classmethod
    def get_mask_id(cls, user_id):
        return cls.get_or_create(user_id=user_id).mask_id


class P2pMerchantChangeLog(ModelBase):
    """p2p广告商数据修改日志(暂时只有修改营业状态的时候记录)"""
    class ChangeType(Enum):
        SHOP_STATUS = "shop_status"

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    change_type = db.Column(db.StringEnum(ChangeType), nullable=False)
    change_before = db.Column(db.String(1024), nullable=False)
    change_after = db.Column(db.String(1024), nullable=False)

    @classmethod
    def save_change_log(cls, user_id: int, change_type: ChangeType, change_before: str, change_after: str):
        db.session.add(cls(
            user_id=user_id,
            change_type=change_type,
            change_before=change_before,
            change_after=change_after,
        ))

    @classmethod
    def save_shop_status_change_log(cls, user_id: int, change_before: str, change_after: str):
        cls.save_change_log(user_id, cls.ChangeType.SHOP_STATUS, change_before, change_after)

    @classmethod
    def get_user_open_shop_data(cls, user_id: int, start_datetime: datetime, end_datetime: datetime):
        end_datetime = min(end_datetime, now())
        query = cls.query.filter(
            cls.user_id == user_id,
            cls.change_type == cls.ChangeType.SHOP_STATUS,
            cls.created_at >= start_datetime,
            cls.created_at < end_datetime,
        ).order_by(cls.id).with_entities(
            cls.change_before,
            cls.change_after,
            cls.created_at
        ).all()
        merchant = P2pMerchant.query.filter(P2pMerchant.user_id == user_id).first()
        start_datetime = max(start_datetime, merchant.created_at)
        if not merchant:
            return []
        if not query:
            if merchant.shop_status == P2pMerchant.ShopStatus.OPEN:
                return [[start_datetime, end_datetime]]
            else:
                return []
        data = []
        for before_status, after_status, created_at in query:
            # 关闭店铺
            if before_status == P2pMerchant.ShopStatus.OPEN.name and \
                    after_status == P2pMerchant.ShopStatus.CLOSED.name:
                if not data:
                    data.append([start_datetime, created_at])
                else:
                    data[-1][1] = created_at
            # 打开店铺
            else:
                data.append([created_at, None])
        if not data:
            return data
        if data[-1][1] is None:
            data[-1][1] = end_datetime
        return data


class ActivityPartyUser(ModelBase):

    class ActivityParty(Enum):
        VERTUS = 'vertus'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    activity_party = db.Column(db.StringEnum(ActivityParty), nullable=False)
    mask_id = db.Column(db.String(64), nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
