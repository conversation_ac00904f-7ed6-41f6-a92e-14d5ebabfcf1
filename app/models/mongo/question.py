from enum import Enum
from typing import List, Any, Optional

from pydantic import BaseModel

from app.models import db
from app.models.base import M2MModelBase
from app.models.mongo import Status


class QuestionTag(Enum):
    Airdrop = "空投"
    Anniversary = "周年庆"
    PerpetualTutorial = "合约测试"
    P2pTutorial = "p2p新手引导"
    Other = "其他"


class QuestionGroupMySQL(M2MModelBase):
    """MySQL版本的问题集合模型"""

    __tablename__ = "question_group"

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255))
    question_ids = db.Column(db.MYSQL_JSON)  # 存储ObjectId的字符串列表
    tag = db.Column(db.StringEnum(QuestionTag), default=QuestionTag.Other.name)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)

    __table_args__ = (
        db.Index("idx_tag", "tag"),
        db.Index("idx_name", "name"),
        # question_ids 目前暂时不需要建索引，mysql 的 json array 索引效率很低，也没什么必要
    )


class QuestionDetailSchema(BaseModel):
    """问题详情的Pydantic模型"""

    question: str
    description: str = ""
    answer: Any = ""
    answer_analysis: str = ""
    options: List[Any] = []


class QuestionMySQL(M2MModelBase):
    """MySQL版本的问题模型"""

    __tablename__ = "question"

    class OptionType(Enum):
        Radio = "单选"

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    option_type = db.Column(db.StringEnum(OptionType), default=OptionType.Radio.name)
    tag = db.Column(db.StringEnum(QuestionTag), default=QuestionTag.Other.name)
    detail_lang_map = db.Column(db.MYSQL_JSON)  # 存储问题详情的JSON数据
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID.name)

    __table_args__ = (
        db.Index("idx_tag", "tag"),
        db.Index("idx_name", "name"),
    )

    def get_detail(self, lang: str) -> Optional[QuestionDetailSchema]:
        """获取指定语言的问题详情"""
        if not self.detail_lang_map or lang not in self.detail_lang_map:
            return None
        return QuestionDetailSchema(**self.detail_lang_map[lang])

    def set_detail(self, lang: str, detail: QuestionDetailSchema):
        """设置指定语言的问题详情"""
        if not self.detail_lang_map:
            self.detail_lang_map = {}
        self.detail_lang_map[lang] = detail.model_dump()
