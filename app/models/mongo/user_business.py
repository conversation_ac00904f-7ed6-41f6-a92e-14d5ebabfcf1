from enum import Enum

from ...api.common.request import RequestPlatform
from app.models import db
from app.models.base import M2MModelBase


class AssetTradeBehaviorMySQL(M2MModelBase):
    """MySQL 版本的资产交易行为模型"""
    __tablename__ = 'asset_trade_behaviors'

    id = db.Column(db.Integer, primary_key=True)
    report_at = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, unique=True)
    data = db.Column(db.MYSQL_JSON, nullable=False)



class UserAfInfoMySQL(M2MModelBase):
    """MySQL 版本的用户 Appsflyer 信息模型"""
    __tablename__ = 'user_af_infos'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    mask_id = db.Column(db.String(255), nullable=False, unique=True)
    af_id = db.Column(db.String(255), nullable=False, index=True)
    platform = db.Column(db.StringEnum(RequestPlatform), nullable=False)
    is_new = db.Column(db.Boolean, default=False)
    register_af_id = db.Column(db.String(255), index=True)
    register_platform = db.Column(db.StringEnum(RequestPlatform))
    channel = db.Column(db.String(255))

    __table_args__ = (
        db.Index('idx_created_at', 'created_at'),
    )

    @classmethod
    def get_min_dt(cls):
        """获取最早的新用户记录时间"""
        af_row = cls.query.filter_by(is_new=True).order_by(cls.id).first()
        if af_row:
            return af_row.created_at


class UserAfEventMySQL(M2MModelBase):
    """MySQL 版本的用户 Appsflyer 事件模型"""
    __tablename__ = 'user_af_events'

    class Event(Enum):
        FIRST_LOCAL_DEPOSIT = "s2s_firsttransfer_success"
        FIRST_ON_CHAIN_DEPOSIT = "s2s_firstdeposit_success"
        FIRST_P2P_BUY = "s2s_firstp2p_success"
        FIRST_TRADE = "s2s_firsttrade_success"
        TOTAL_FEE_5 = "total_tradefee_5"
        TOTAL_FEE_200 = "total_tradefee_200"
        TOTAL_FEE_1000 = "total_tradefee_1000"
        TOTAL_FEE_5000 = "total_tradefee_5000"
        TOTAL_FEE_20000 = "total_tradefee_2W"
        TOTAL_FEE_50000 = "total_tradefee_5W"
        TOTAL_FEE_100000 = "total_tradefee_10W"
        TOTAL_FEE_500000 = "total_tradefee_50W"
        REGISTER_SUCCESS = "register_success"   # 由客户端上报的事件

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    mask_id = db.Column(db.String(255), nullable=False)
    event = db.Column(db.StringEnum(Event), nullable=False)
    event_time = db.Column(db.MYSQL_DATETIME_6)
    af_report = db.Column(db.MYSQL_DATETIME_6)
    af_status = db.Column(db.Boolean, default=False)

    __table_args__ = (
        db.UniqueConstraint('user_id', 'event', name='uq_user_id_event'),
    )


class AfEventDumpMySQL(M2MModelBase):
    """MySQL 版本的事件导出记录模型"""
    __tablename__ = 'af_event_dumps'

    id = db.Column(db.Integer, primary_key=True)
    event = db.Column(db.StringEnum(UserAfEventMySQL.Event), nullable=False)
    dump_at = db.Column(db.MYSQL_DATETIME_6)


class AfEventDataMySQL(M2MModelBase):
    """MySQL 版本的事件归因数据模型"""
    __tablename__ = 'af_event_data'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    event_name = db.Column(db.String(255))
    event_time = db.Column(db.MYSQL_DATETIME_6)
    event_data = db.Column(db.Text)

    __table_args__ = (
        db.Index('idx_user_id_event_name_event_time', 'user_id', 'event_name', 'event_time'),
        db.Index('idx_event_name_event_time', 'event_name', 'event_time'),
    )

    @classmethod
    def get_newest_dt(cls, event_name):
        """获取最新的事件时间"""
        row = cls.query.filter_by(event_name=event_name).order_by(cls.event_time.desc()).first()
        if row:
            return row.event_time


class AfChannelInfoMySQL(M2MModelBase):
    """MySQL 版本的渠道信息模型"""
    __tablename__ = 'af_channel_infos'

    class SpecialChannel(Enum):
        ORIGIN = "自然"
        OTHER = "其他"

    id = db.Column(db.Integer, primary_key=True)
    channel = db.Column(db.String(255), unique=True)

    @classmethod
    def get_all_channel(cls):
        """获取所有渠道"""
        channels = [row.channel for row in cls.query.all()]
        return channels + [cls.SpecialChannel.OTHER.name]