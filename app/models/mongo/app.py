from app.models import db
from app.models.base import M2MModelBase


class AppleADSAttributionMySQL(M2MModelBase):
    __tablename__ = 'apple_ads'
    
    device_id = db.Column(db.String(255), nullable=True, index=True)
    attribution = db.Column(db.<PERSON>, default=False)
    org_id = db.Column(db.Integer, nullable=True)
    campaign_id = db.Column(db.Integer, nullable=True)
    conversion_type = db.Column(db.String(255), nullable=True)
    click_date = db.Column(db.DateTime, nullable=True)
    ad_group_id = db.Column(db.Integer, nullable=True)
    country_or_region = db.Column(db.String(255), nullable=True)
    keyword_id = db.Column(db.Integer, nullable=True)
    ad_id = db.Column(db.Integer, nullable=True)