import json

from functools import partial

from enum import Enum


from app.models import db
from app.models.base import M2MModelBase

from app.utils.parser import JsonEncoder
from app.utils.format import format_op_log_dict


class _Namespace:
    def __init__(self, ns_name: str, ns_desc: str):
        self.name = ns_name
        self.desc = ns_desc


class _Object:
    def __init__(self, obj_name: str, obj_desc: str):
        self.name = obj_name
        self.desc = obj_desc


class _NamespaceObject:
    def __init__(self, obj_name: str, obj_desc: str, namespace: _Namespace):
        self.namespace = namespace
        self.object = _Object(obj_name, obj_desc)

    def __str__(self):
        return f'namespace: {self.namespace.name}, object: {self.object.name}'


class BaseOPNamespaceObjectMeta(type):

    namespace: _Namespace = None
    _namespace_map: dict[_Namespace, list[_NamespaceObject]] = {}

    def __new__(mcs, *args, **kwargs):
        cls = super().__new__(mcs, *args, **kwargs)
        namespace = getattr(cls, 'namespace', None)
        if namespace is None:  # base class
            return cls
        objs: list[_NamespaceObject] = []
        for item in dir(cls):
            attr = getattr(cls, item)
            if isinstance(attr, _NamespaceObject):
                objs.append(attr)
        mcs._namespace_map[namespace] = objs
        return cls

    @classmethod
    def namespaces(cls) -> list[_Namespace]:
        return list(cls._namespace_map.keys())

    @classmethod
    def get_namespace(cls, name: str) -> _Namespace | None:
        for ns in cls.namespaces():
            if ns.name == name:
                return ns
        return None

    @classmethod
    def get_namespace_by_desc(cls, desc: str) -> _Namespace | None:
        for ns in cls.namespaces():
            if ns.desc == desc:
                return ns
        return None

    @classmethod
    def objects(cls, namespace: _Namespace) -> list[_NamespaceObject]:
        return cls._namespace_map.get(namespace, [])

    @classmethod
    def get_object(cls, namespace: _Namespace, name: str) -> _Object | None:
        for ns_obj in cls.objects(namespace):
            if ns_obj.object.name == name:
                return ns_obj.object
        return None

    @classmethod
    def get_object_by_desc(cls, namespace: _Namespace, desc: str) -> _Object | None:
        for ns_obj in cls.objects(namespace):
            if ns_obj.object.desc == desc:
                return ns_obj.object
        return None


class BaseOPNamespaceObject(metaclass=BaseOPNamespaceObjectMeta):

    namespace: _Namespace = None


_NAMESPACE_USER = _Namespace('USER', '用户')
_NAMESPACE_WALLET = _Namespace('WALLET', '钱包')
_NAMESPACE_FIAT = _Namespace('FIAT', '法币')
_NAMESPACE_SPOT = _Namespace('SPOT', '现货')
_NAMESPACE_PERPETUAL = _Namespace('PERPETUAL', '合约')
_NAMESPACE_OPERATION = _Namespace('OPERATION', '运营')
_NAMESPACE_RISK = _Namespace('RISK', '风控')
_NAMESPACE_SYSTEM = _Namespace('SYSTEM', '系统')


class OPNamespaceObjectUser(BaseOPNamespaceObject):

    namespace = _NAMESPACE_USER

    F = partial(_NamespaceObject, namespace=namespace)

    LoginAdmin = F('LOGIN_ADMIN', '登录管理后台')
    Basics = F('BASICS', '账户信息')
    Level = F('LEVEL', '等级费率')
    AssetsPreview = F('ASSETS_PREVIEW', '资产总览')
    SpotAssets = F('SPOT_ASSETS', '现货账户')
    MarginAssets = F('MARGIN_ASSETS', '杠杆账户')
    PerpetualAssets = F('PERPETUAL_ASSETS', '合约账户')
    InvestAssets = F('INVEST_ASSETS', '理财账户')
    CreditAssets = F('CREDIT_ASSETS', '授信账户')
    AMMAssets = F('AMM_ASSETS', '做市账户')
    PledgeAssets = F('PLEDGE_ASSETS', '借贷账户')
    DepositInfo = F('DEPOSIT_INFO', '充值信息')
    WithdrawalInfo = F('WITHDRAWAL_INFO', '提现信息')
    SpotOrders = F('SPOT_ORDERS', '币币订单')
    PerpetualOrders = F('PERPETUAL_ORDERS', '合约订单')
    BalanceHistory = F('BALANCE_HISTORY', '资产流水')
    Coupons = F('COUPONS', '卡券记录')
    LoginHistory = F('LOGIN_HISTORY', '登陆记录')
    OperationHistory = F('OPERATION_HISTORY', '操作记录')
    Mobile = F('MOBILE', '电话号')
    TOTP = F('TOTP', 'TOTP')
    Webauthn = F('WEBAUTHN', '通行密钥')
    Remark = F('REMARK', '备注')
    Email = F('EMAIL', '邮箱')
    Type = F('TYPE', '账号类型')
    VIP = F('VIP', 'VIP')
    VIPLevel = F('VIP_LEVEL', 'VIP等级')
    Preference = F('PREFERENCE', '偏好')
    Setting = F('SETTING', '设置')
    ThirdPartyAccount = F('THIRD_PARTY_ACCOUNT', '第三方账号')
    WithdrawalApprover = F('WITHDRAWAL_APPROVER', '提现审核邮箱')
    SpecialFee = F('SPECIAL_FEE', '特殊费率')
    InnerMarketMaker = F('INNER_MARKET_MAKER', '内部做市商')
    SpotMarketMaker = F('SPOT_MARKET_MAKER', '现货做市商')
    PerpetualMarketMaker = F('PERPETUAL_MARKET_MAker', '合约做市商')
    MarketMakerEmail = F('MARKET_MAKER_EMAIL', '做市商日报接收邮箱')
    MarketMakerApply = F('MARKET_MAKER_APPLY', '做市商申请')
    MarginDayRate = F('MARGIN_DAY_RATE', '杠杆日息')
    ResetAvatar = F("RESET_AVATAR", '用户头像')
    SpecialReferreeRate = F('SPECIAL_REFERREE_RATE', '邀请人返佣比例')
    UserCheck = F('USER_CHECK', '用户验证')
    MaskIdTransfer = F('MASK_ID_TRANSFER', 'CUID转换')
    BigUserCustomer = F('BIG_USER_CUSTOMER', '大客户管理')
    BigUserCustomerSetting = F('BIG_USER_CUSTOMER_SETTING', '大客户标签配置')
    Broker = F('BROKER', '经纪商')
    BrokerApplication = F('BROKER_APPLICATION', '经纪商申请')
    KolUser = F('KOL_USER', 'KOL用户')
    KolApply = F('KOL_APPLY', 'KOL申请')
    Ambassador = F('AMBASSADOR', '平台大使')
    AmbassadorGuide = F('AMBASSADOR_GUIDE', '平台大使攻略')
    AmbassadorStar = F('AMBASSADOR_STAR', '平台大使风采')
    AmbassadorApplication = F('AMBASSADOR_APPLICATION', '平台大使申请')
    UserAmbassadorActivity = F('USER_AMBASSADOR_ACTIVITY', '平台大使活动')
    Agent = F('AGENT', '平台代理')
    BusAmbassadorTeam = F('BUS_AMBASSADOR_TEAM', '商务大使团队')
    BusAmbassadorUser = F('BUS_AMBASSADOR_USER', '商务大使')
    BusAmbassadorPermission = F('BUS_AMBASSADOR_PERMISSION', '商务大使权限设置')
    BusUser = F('BUS_USER', '商务')
    BusAmbassadorLoanApply = F('BUS_AMBASSADOR_LOAN_APPLY', '商务大使预付金申请')
    SpecialCETDiscount = F('SPECIAL_CET_DISCOUNT', '特殊CET折扣配置')
    SpecialAmbassadorRate = F('SPECIAL_AMBASSADOR_RATE', '特殊返佣比例')
    SpecialMarginAccountRule = F('SPECIAL_MARGIN_ACCOUNT_RULE', '特殊市场借币额度配置')
    SpecialMarginAssetRule = F('SPECIAL_MARGIN_ASSET_RULE', '特殊币种借币额度配置')
    SpecialWithdrawalNoDelay = F('SPECIAL_WITHDRAWAL_NO_DELAY', '特殊提现免延迟审核')
    SpecialWithdrawalWhitelist = F('SPECIAL_WITHDRAWAL_WHITELIST', '特殊提现免风控白名单')
    SpecialWithdrawalPrivileged = F('SPECIAL_WITHDRAWAL_PRIVILEGED', '特殊提现白名单')
    SpecialWithdrawalLimit = F('SPECIAL_WITHDRAWAL_LIMIT', '特殊提现额度配置')
    OnlyWithdrawalWhitelist = F('ONLY_WITHDRAWAL_WHITELIST', '清退白名单')
    Cleared = F('CLEARED', '仅支持提现用户')
    FrequencyLimitAPI = F('FREQUENCY_LIMIT_API', '特殊API频率限制')
    FrequencyLimitLangPeriod = F('FREQUENCY_LIMIT_LONG_PERIOD', '特殊长周期频率限制')
    APISpeed = F('API_SPEED', 'API加速配置')
    IEOWhitelist = F('IEO_WHITELIST', '投资用户白名单')
    SubAccountLimit = F('SUB_ACCOUNT_LIMIT', '子账户上限配置')
    BusinessSystemUser = F('BUSINESS_SYSTEM_USER', '业务账号配置')
    AdminTagCategory = F('ADMIN_TAG_CATEGORY', '用户标签类别')
    AdminTagUser = F('ADMIN_TAG_USER', '用户标签管理')
    SubAccount = F('SUB_ACCOUNT', '子账号管理')
    APISpotTradingWhitelist = F('API_SPOT_TRADING_WHITELIST', '现货交易用户白名单')
    UserNameBlock = F('USER_NAME_BLOCK', '用户名或账户名屏蔽词')


class OPNamespaceObjectWallet(BaseOPNamespaceObject):

    namespace = _NAMESPACE_WALLET

    F = partial(_NamespaceObject, namespace=namespace)

    CreditUser = F('CREDIT_USER', '授信账户')
    CreditRecord = F('CREDIT_RECORD', '授信记录')
    CreditAssetRate = F('CREDIT_ASSET_RATE', '授信币种利率')
    CreditAssetUserRate = F('CREDIT_ASSET_USER_RATE', '授信用户利率')
    KYT = F('KYT', '充值风险评估')
    DepositAudit = F('DEPOSIT_AUDIT', '充值审核')
    EDDAudit = F('EDD_AUDIT', 'EDD审核')
    KYTWhitelistUser = F('KYT_WHITELIST_USER', 'KYT 用户维度白名单')
    KYTRiskAssessment = F('KYT_RISK_ASSESSMENT', 'KYT 充值风险评估记录')
    KYTFromAddressWhitelist = F('KYT_FROM_ADDRESS_WHITELIST', 'KYT 白名单 from 地址')
    KYTFromAddressBlacklist = F('KYT_FROM_ADDRESS_BLACKLIST', 'KYT 充值黑名单')
    KYTSettings = F('KYT_SETTINGS', 'KYT 设置')
    KYTChainAssessor = F('KYT_CHAIN_ASSESSOR', 'KYT 链服务商设置')


class OPNamespaceObjectFiat(BaseOPNamespaceObject):

    namespace = _NAMESPACE_FIAT

    F = partial(_NamespaceObject, namespace=namespace)

    P2PComplaintOP = F('P2P_COMPLAINT_OP', 'P2P申诉')
    P2PComplaintRecord = F('P2P_COMPLAINT_RECORD', 'P2P申诉记录')
    P2PComplaintReply = F('P2P_COMPLAINT_REPLY', 'P2P申诉话术')
    P2PAssetConfig = F('P2P_ASSET_CONFIG', 'P2P资产配置')
    P2PTradeConfig = F('P2P_TRADE_CONFIG', 'P2P交易配置')
    P2PFiatConfig = F('P2P_FIAT_CONFIG', 'P2P法币配置')
    P2PPayChannelConfig = F('P2P_PAY_CHANNEL_CONFIG', 'P2P支付渠道配置')
    P2PFiatPayChannel = F('P2P_FIAT_PAY_CHANNEL', 'P2P法币支付渠道')
    P2PCountryPayChannel = F('P2P_COUNTRY_PAY_CHANNEL', 'P2P地区推荐支付渠道配置')
    P2PCountryFiatConfig = F('P2P_COUNTRY_FIAT_CONFIG', 'P2P地区法币对照表')
    P2PTPlusNRuleConfig = F('P2P_T_PLUS_N_CONFIG', 'P2P T+N风控策略配置')
    P2PTPlusNRuleGlobalConfig = F('P2P_T_PLUS_N_GLOBAL_CONFIG', 'P2P T+N风控策略全局配置')
    P2POrder = F('P2P_ORDER', 'P2P订单')
    P2PMerchant = F('P2P_MERCHANT', 'P2P广告商')
    P2PAdvertising = F('P2P_ADVERTISING', 'P2P广告单')
    P2PMarginHistory = F('P2P_MARGIN_HISTORY', 'P2P保证金收支记录')
    FiatActivity = F('FIAT_ACTIVITY', '法币活动')


class OPNamespaceObjectSpot(BaseOPNamespaceObject):

    namespace = _NAMESPACE_SPOT

    F = partial(_NamespaceObject, namespace=namespace)

    Market = F('MARKET', '市场')
    MarketOrders = F('MARKET_ORDERS', '市场订单')
    MarketOfflineContent = F('MARKET_OFFLINE_CONTENT', '市场下架文案')
    ExchangeOrders = F('EXCHANGE_ORDERS', '兑换订单')
    MarginMarket = F('MARKET_MARKET', '杠杆市场配置')
    MarginAssetRule = F('MARGIN_ASSET_RULE', '杠杆借币配置')
    MarginIndex = F('MARGIN_INDEX', '杠杆普通指数配置')
    MarginSpecialIndexMapping = F('MARGIN_SPECIAL_INDEX_MAPPING', '特殊币种映射管理')
    MarginCompose = F('MARGIN_COMPOSE', '杠杆合成指数配置')
    LoanAssetUserRate = F('LOAN_ASSET_USER_RATE', '用户特殊借贷费率')
    LoanAsset = F('LOAN_ASSET', '借贷币种配置')
    LoanAssetStageConfig = F('LOAN_ASSET_STAGE_CONFIG', '借贷币种档位配置')
    LoanPledgeAsset = F('LOAN_PLEDGE_ASSET', '借贷质押币配置')
    AMMMarketConfig = F('AMM_MARKET_CONFIG', 'AMM市场配置')
    ExchangeConfig = F('EXCHANGE_CONFIG', '兑换档位配置')
    AutoInvestConfig = F('AUTO_INVEST_CONFIG', '定投档位配置')
    AutoInvestMarket = F('AUTO_INVEST_MARKET', '定投市场档位')
    AutoInvestPlan = F('AUTO_INVEST_PLAN', '定投策略')
    GridConfig = F('GRID_CONFIG', '现货网格档位配置')
    GridMarket = F('GRID_MARKET', '现货网格市场配置')
    GridStrategy = F('GRID_STRATEGY', '现货网格策略')
    PreTradingAssetConfig = F('PRE_TRADING_ASSET_CONFIG', '预测市场币种配置')
    PreTradingAssetContent = F('PRE_TRADING_ASSET_CONTENT', '预测市场交割规则文案')
    PledgeLiqWhitelist = F('PLEDGE_LIQ_WHITELIST', '借贷清算白名单')


class OPNamespaceObjectPerpetual(BaseOPNamespaceObject):

    namespace = _NAMESPACE_PERPETUAL

    F = partial(_NamespaceObject, namespace=namespace)

    Market = F('MARKET', '合约市场配置')
    MarketOfflineContent = F('MARKET_OFFLINE_CONTENT', '市场下架文案')
    PerpetualIndex = F('PERPETUAL_INDEX', '合约普通指数配置')
    PerpetualCompose = F('PERPETUAL_COMPOSE', '合约合成指数配置')
    PerpetualAssets = F('PERPETUAL_ASSETS', '合约资产配置')
    CopyTradingConfig = F('COPY_TRADING_CONFIG', '跟单交易全局配置')
    CopyTradingMarket = F('COPY_TRADING_MARKET', '跟单交易市场配置')
    CopyTradingTrader = F('COPY_TRADING_TRADER', '跟单交易交易员')
    CopyTradingFollow = F('COPY_TRADING_FOLLOW', '跟单交易记录')


class OPNamespaceObjectOperation(BaseOPNamespaceObject):

    namespace = _NAMESPACE_OPERATION

    F = partial(_NamespaceObject, namespace=namespace)

    DepositActivity = F('DEPOSIT_ACTIVITY', '充值送币活动')
    TradeActivity = F('TRADE_ACTIVITY', '交易送币活动')
    AmbassadorActivity = F('AMBASSADOR_ACTIVITY', '大使活动')
    MiningActivity = F('MINING_ACTIVITY', '挖矿活动')
    AirdropActivity = F('AIRDROP_ACTIVITY', '空投活动')
    AirdropActivityRandom = F('AIRDROP_ACTIVITY_RANDOM', '随机空投活动')
    DiscountActivity = F('DISCOUNT_ACTIVITY', 'Dibs活动')
    DiscountActivityLottery = F('DISCOUNT_ACTIVITY_LOTTERY', 'Dibs活动抽奖')
    IEOActivity = F('IEO_ACTIVITY', '投资项目')
    NoviceActivity = F('NOVICE_ACTIVITY', '新手活动')
    PerpetualTopicNovice = F('PERPETUAL_TOPIC_NOVICE', '合约专题页-新手福利')
    PerpetualTopicActivity = F('PERPETUAL_TOPIC_ACTIVITY', '合约专题页-活动专区')
    ChannelActivity = F('CHANNEL_ACTIVITY', '渠道活动')
    ChannelActivityUser = F('CHANNEL_ACTIVITY_USER', '渠道活动-获奖用户')
    CoinHalvingActivity = F('COIN_HALVING_ACTIVITY', '减半活动')
    TradeRank = F('TRADE_RANK', '交易排名')
    UserTag = F('USER_TAG', '用户标签')
    UserTagCategory = F('USER_TAG_CATEGORY', '用户标签类别')
    UserTagGroup = F('USER_TAG_GROUP', '用户分群')
    UserTagGroupPortrait = F('USER_TAG_GROUP_PORTRAIT', '用户群体画像')
    Template = F('TEMPLATE', '模板管理')
    TemplateContent = F('TEMPLATE_CONTENT', '模版内容管理')
    EmailPush = F('EMAIL_PUSH', '邮件推送')
    EmailPushContent = F('EMAIL_PUSH_CONTENT', '邮件推送内容')
    AssetOfflineEmailPush = F('ASSET_OFFLINE_EMAIL_PUSH', '下架资产邮件推送')
    PopupWindows = F('POPUP_WINDOWS', '弹窗管理')
    PopupWindowsContent = F('POPUP_WINDOWS_CONTENT', '弹窗内容')
    APPPush = F('APP_PUSH', 'APP推送')
    APPPushContent = F('APP_PUSH_CONTENT', 'APP推送内容')
    APPAutoPushStrategy = F('APP_AUTO_PUSH_STRATEGY', 'APP自动推送策略')
    APPAutoPushContent = F('APP_AUTO_PUSH_CONTENT', 'APP自动推送内容')
    MessagePush = F('MESSAGE_PUSH', '站内信推送')
    MessagePushContent = F('MESSAGE_PUSH_CONTENT', '站内信推送内容')
    Zendesk_Article_Update = F('ZENDESK_ARTICLE_UPDATE', 'Zendesk文章拉取更新')
    NotificationBar = F('NOTIFICATION_BAR', '通知栏')
    NotificationBarContent = F('NOTIFICATION_BAR_CONTENT', '通知栏内容')
    TipBar = F('TIP_BAR', '提示条')
    TipBarContent = F('TIP_BAR_CONTENT', '提示条内容')
    AssetMaintain = F('ASSET_MAINTAIN', '币种维护')
    AssetMaintainContent = F('ASSET_MAINTAIN_CONTENT', '币种维护内容')
    TempMaintain = F('TEMP_MAINTAIN', '停服维护')
    TempMaintainContent = F('TEMP_MAINTAIN_CONTENT', '停服维护通知')
    MarketMaintain = F('MARKET_MAINTAIN', '单市场停服维护')
    AppActivate = F('APP_ACTIVATE', '活动宣传页')
    AppActivateContent = F('APP_ACTIVATE_CONTENT', '活动宣传页内容')
    AppEntrance = F('APP_ENTRANCE', '金刚区')
    AppEntranceTranslation = F('APP_ENTRANCE_TRANSLATION', '金刚区翻译')
    StartPage = F('START_PAGE', '启动页')
    StartPageContent = F('START_PAGE_CONTENT', '启动页内容')
    DynamicFalling = F('DYNAMIC_FALLING', '动效飘落')
    Banner = F('BANNER', 'Banner')
    BannerContent = F('BANNER_CONTENT', 'Banner内容')
    FooterConfig = F('FOOTER_CONFIG', 'Footer配置')
    SNSConfig = F('SNS_CONFIG', 'SNS 配置')
    LoginPageMarket = F('LOGIN_PAGE_MARKET', '登录页市场配置')
    PageInset = F('PAGE_INSET', '页面插画')
    PageInsetContent = F('PAGE_INSET_CONTENT', '页面插画内容')
    InsetConfig = F('INSET_CONFIG', '首页插画')
    InsetConfigContent = F('INSET_CONFIG_CONTENT', '首页插画内容')
    NewerGuide = F('NEWER_GUIDE', '新手引导')
    PerpetualActivity = F('PERPETUAL_ACTIVITY', '合约交易活动专区')
    PerpetualActivityContent = F('PERPETUAL_ACTIVITY_CONTENT', '合约交易活动内容')
    ExposureActivity = F('EXPOSURE_ACTIVITY', '站内功能曝光标识')
    CalendarActivity = F('CALENDAR_ACTIVITY', '日历活动')
    CalendarActivityContent = F('CALENDAR_ACTIVITY_CONTENT', '日历活动内容')
    Portrait = F('PORTRAIT', '账户头像')
    MarketBanner = F('MARKET_BANNER', '投放营销')
    MarketBannerContent = F('MARKET_BANNER_CONTENT', '投放营销内容')
    ReferralPicture = F('REFERRAL_PICTURE', '推荐图片')
    ReferralCopyWriting = F('REFERRAL_COPY_WRITING', '推荐文案')
    ReferralCopyWritingTranslation = F('REFERRAL_COPY_WRITING_TRANSLATION', '推荐文案翻译')
    ReferralActivity = F('REFERRAL_ACTIVITY', '推荐返佣活动')
    ReferralActivityContent = F('REFERRAL_ACTIVITY_CONTENT', '推荐返佣活动内容')
    Coupon = F('COUPON', '卡券管理')
    CouponApply = F('COUPON_APPLY', '卡券发放管理')
    CouponDistribution = F('COUPON_DISTRIBUTION', '卡券智能投放')
    CBoxTheme = F('CBOX_THEME', 'C-Box主题')
    CBoxOnlyNewUserConfig = F('CBOX_ONLY_NEW_USER_CONFIG', 'C-Box仅新用户领取')
    CBoxCode = F('CBOX_CODE', 'C-Box特殊口令')
    CBoxPacket = F('CBOX_PACKET', 'C-Box特殊额度')
    P2pMerAct = F('P2P_MER_ACT', 'p2p商家活动')
    P2pMerActReward = F('P2P_MER_ACT', 'p2p商家活动奖励')
    P2pMerActUser = F('P2P_MER_ACT_USER', 'p2p商家活动报名用户')
    P2pMerActFair = F('P2P_MER_ACT_PRICE', 'p2p商家活动公允价格')
    P2pCountryMargin = F('P2P_COUNTRY_MARGIN', 'p2p国家保证金')
    P2pUserMargin = F('P2P_USER_MARGIN', 'p2p用户保证金')
    APPVersion = F('APP_VERSION', 'APP版本管理')
    APPCert = F('APP_CERT', 'APP证书管理')
    NewAsset = F('NEW_ASSET', '新币专区')
    AssetTag = F('ASSET_TAG', '币种标签管理')
    AssetTopic = F('ASSET_TOPIC', '币种专题管理')
    CoinInfo = F('COIN_INFO', '币种资料')
    CoinCirculationAlertLimit = F('COIN_CIRCULATION_ALERT_LIMIT', '币种流通量阈值')
    CoinApplication = F('COIN_APPLICATION', '币种申请')
    CoinApplicationIEO = F('COIN_APPLICATION_IEO', '币种融资申请')
    DepositWithdrawPopupWindows = F('DEPOSIT_WITHDRAW_POPUP_WINDOWS', '充提弹窗配置')
    BlogCategory = F('BLOG_CATEGORY', '博客类别管理')
    Blog = F('BLOG', '博客管理')
    Insight = F('INSIGHT', 'Insight管理')
    AcademyCategory = F('ACADEMY_CATEGORY', '学院类别管理')
    AcademyTag = F('ACADEMY_TAG', '学院标签管理')
    AcademyArticle = F('ACADEMY_ARTICLE', '学院文章管理')
    ShortLink = F('SHORT_LINK', '短链接配置')
    ShortLinkPublicityChannel = F('SHORT_LINK_PUBLICITY_CHANNEL', '短链接-推广渠道')
    ShortLinkPublicityChannelCategory = F('SHORT_LINK_PUBLICITY_CHANNEL_CATEGORY', '短链接-推广渠道平台')
    ShortLinkExposureProject = F('SHORT_LINK_EXPOSURE_PROJECT', '短链接-曝光专项')
    VideoUpload = F('VIDEO_UPLOAD', '视频上传管理')
    Question = F('QUESTION', '题库问题管理')
    QuestionGroup = F('QUESTION_GROUP', '题库问题组管理')
    RecruitJob = F('RECRUIT_JOB', '职位招聘管理')
    RecruitJobContent = F('RECRUIT_JOB_CONTENT', '职位招聘内容')
    PartnerPosition = F('PARTNER_POSITION', '合伙人职位管理')
    CharityBanner = F('CHARITY_BANNER', '慈善-Banner管理')
    CharityDonation = F('CHARITY_DONATION', '慈善-捐赠管理')
    CharityActivity = F('CHARITY_ACTIVITY', '慈善-活动管理')
    CharityVideo = F('CHARITY_VIDEO', '慈善-视频管理')
    CharityCategory = F('CHARITY_CATEGORY', '慈善-类别管理')
    CharityFootPrintCategory = F('CHARITY_FOOT_PRINT_CATEGORY', '慈善-慈善足迹类别管理')
    CharityFootPrint = F('CHARITY_FOOT_PRINT', '慈善-慈善足迹管理')
    CharityFootPrintContent = F('CHARITY_FOOT_PRINT_CONTENT', '慈善-慈善足迹内容')
    WalletTraffic = F('WALLET_TRAFFIC', 'Wallet运营位配置')
    NewBieMissionPlan = F('NEW_BIE_MISSION_PLAN', '新手任务计划')


class OPNamespaceObjectRisk(BaseOPNamespaceObject):

    namespace = _NAMESPACE_RISK

    F = partial(_NamespaceObject, namespace=namespace)

    UnfreezeAccount = F('UNFREEZE_ACCOUNT', '自助解冻')
    WithdrawalFuseConfig = F('WITHDRAWAL_FUSE_CONFIG', '全站提现熔断阈值配置')
    DepositFuseConfig = F('DEPOSIT_FUSE_CONFIG', '全站充值熔断阈值配置')
    Kyc = F('KYC', 'KYC')
    KycPro = F('KYC_PRO', '高级KYC')
    KycInstitution = F('KYC_INSTITUTION', 'KYC机构')
    LivenessCheck = F('LIVENESS_CHECK', '活体识别')
    KycCountryConfig = F('KYC_COUNTRY_CONFIG', 'KYC配置表')
    BusOnlineCoinConfigByMarket = F('BUS_ONLINE_COIN_CONFIG_BY_MARKET', '商务上币风控配置(市场)')
    BusOnlineCoinConfigByAsset = F('BUS_ONLINE_COIN_CONFIG_BY_ASSET', '商务上币风控配置(币种充值)')
    PriceVerifySettings = F('PRICE_VERIFY_SETTINGS', '价格保护机制参数设置')
    MobileNoticeConfig = F('MOBILE_NOTICE_CONFIG', '风控电话告警配置')
    RiskUser = F('RISK_USER', '冻结用户')
    RiskEvent = F('RISK_EVENT', '系统风控事件')
    ViaBTCTransThresholdConf = F('VIABTC_TRANS_THRESHOLD_CONF', '矿池币种阈值配置')
    RiskConfigSetting = F('RISK_CONFIG_SETTING', '风控参数配置')
    MarketVolatilityRisk = F('MARKET_VOLATILITY_RISK', '市场异常波动参数配置')
    AccumulatedDepositConfig = F('ACCUMULATED_DEPOSIT_CONFIG', '币种累计充提阈值配置')
    WashSale = F('WASH_SALE', '现货防对敲参数配置')
    AbnormalIssuanceConfig = F('ABNORMAL_ISSUANCE_CONFIG', '币种异常增发配置')
    WithdrawalAddressBlocklist = F('WITHDRAWAL_ADDRESS_BLOCKLIST', '提现地址黑名单配置')
    RiskUserAudit = F('RISK_USER_AUDIT', '用户风控审核')
    InvestmentBalanceCheck = F('INVESTMENT_BALANCE_CHECK', '理财对账')
    RedPacketCheck = F('RED_PACKET_CHECK', 'C-Box对账')
    MarginLoanFlatCheck = F('MARGIN_LOAN_FLAT_CHECK', '杠杆借还对账')
    PerpetualBalanceCheck = F('PERPETUAL_BALANCE_CHECK', '合约对账')
    P2pBalanceCheck = F('P2P_BALANCE_CHECK', 'P2P对账')
    PreTradingSettleCheck = F('PRE_TRADING_SETTLE_CHECK', '预测市场对账')
    PledgeLoanFlatCheck = F('PLEDGE_LOAN_FLAT_CHECK', '质押借贷对账')
    IndividualCase = F('INDIVIDUAL_CASE', '个人风险筛查')
    Reset2FA = F('RESET_2FA', '安全工具重置')
    VerifyChannel = F('VERIFY_CHANNEL', '官方渠道验证')
    AntiFraudBlocklist = F('ANTI_FRAUD_BLOCKLIST', '羊毛党黑名单')
    AntiFraudFeature = F('ANTI_FRAUD_FEATURE', '反欺诈系统特征')
    AntiFraudRiskUser = F('ANTI_FRAUD_RISK_USER', '羊毛党风险用户')
    AntiFraudRiskModel = F('ANTI_FRAUD_RISK_MODEL', '羊毛党风险模型')
    LawEnforcement = F('LAW_ENFORCEMENT', '执法用户')
    CountrySms = F('COUNTRY_SMS', '国家短信区号')
    DepositWithdrawalSwitch = F('DEPOSIT_WITHDRAWAL_SWITCH', '风控关闭充提币种')


class OPNamespaceObjectSystem(BaseOPNamespaceObject):

    namespace = _NAMESPACE_SYSTEM

    F = partial(_NamespaceObject, namespace=namespace)

    AssetConfig = F('ASSET_CONFIG', '币种配置')
    AssetChainConfig = F('ASSET_CHAIN_CONFIG', '币种-链配置')
    BusinessSettings = F('BUSINESS_SETTINGS', '业务变量配置')
    SiteSettings = F('SITE_SETTINGS', '站点配置')
    CountrySettings = F('COUNTRY_SETTINGS', '国家配置')
    CountrySMSSettings = F('COUNTRY_SMS_SETTINGS', '国家短信配置')
    IPWhitelist = F('IP_WHITELIST', 'IP白名单')
    CeleryQueue = F('CELERY_QUEUE', 'Celery队列')
    AdminUser = F('ADMIN_USER', '管理员')
    AdminUserWebauthn = F('ADMIN_USER_WEBAUTHN', '管理员通行密钥')
    AdminUserRoles = F('ADMIN_USER_ROLES', '管理员角色')
    AdminRole = F('ADMIN_ROLE', '角色')
    AdminRolePermissions = F('ADMIN_ROLE_PERMISSIONS', '角色权限')
    AdminPermission = F('ADMIN_PERMISSION', '权限')
    KlineBoostSetting = F('KLINE_BOOST_SETTING', 'K线刷量配置')
    PropTradingConfig = F('PROP_TRADING_CONFIG', '自营交易配置')
    PropTradingUser = F('PROP_TRADING_USER', '自营交易用户')


class AdminOperationLogMySQL(M2MModelBase):
    """MySQL 版本的管理员操作日志模型"""
    __tablename__ = 'admin_operation_logs'

    class Operation(Enum):
        # 新增记录类型时优先从下列已有操作类型中选择, 删除、发送等操作可以适当拓展匹配的操作定义
        # 如遇到无法与下列已有操作类型所匹配的操作可以新增操作类型, 操作类型必须是一个描述简单清晰的动词
        # 新增操作类型后, 需要在下面添加对应的new_函数, 以及在api/admin/op_log.py中OPERATION_NAME_MAP添加对应中文描述
        LOGIN = 'login'  # 登录
        QUERY = 'query'  # 查询
        ADD = 'add'  # 新增
        EDIT = 'edit'  # 编辑
        DELETE = 'delete'  # 删除, 包括解绑、撤单操作
        AUDIT = 'audit'  # 审核
        SEND = 'send'  # 发送, 包括派奖操作
        STOP = 'stop'  # 停止, 包括下架活动操作
        FLAT = 'flat'  # 平仓等操作

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False, comment='操作人ID')
    target_user_id = db.Column(db.Integer, nullable=True, comment='被操作人ID')
    namespace = db.Column(db.String(50), nullable=False, comment='操作的分类大类')
    object = db.Column(db.String(50), nullable=False, comment='操作对象')
    operation = db.Column(db.StringEnum(Operation), nullable=False, comment='操作类型')
    detail = db.Column(db.MYSQL_JSON, nullable=False, comment='操作详情')

    __table_args__ = (
        db.Index('ix_created_at_desc', db.desc('created_at')),
        db.Index('ix_user_id', 'user_id'),
        db.Index('ix_target_user_id_created_at', 'target_user_id', db.desc('created_at')),
        db.Index('ix_namespace_object', 'namespace', 'object'),
    )

    def show_detail(self):
        """显示操作详情"""
        detail = json.loads(self.detail)
        if not isinstance(detail, dict):
            return str(detail)
        result = []
        for k, v in detail.items():
            if isinstance(v, dict) and 'old' in v and 'new' in v:
                result.append(f'{k}: {v["old"]} ➡️ {v["new"]}')
            else:
                result.append(f'{k}: {v}')
        return '\n'.join(result)

    @classmethod
    def get(cls, obj_id: str) -> 'AdminOperationLogMySQL':
        """通过 ID 获取单条记录"""
        admin_operation_log = cls.query.filter(
            AdminOperationLogMySQL.mongo_id == obj_id
        ).first()
        return admin_operation_log

    @classmethod
    def _new(
            cls,
            user_id: int,
            ns_obj: _NamespaceObject,
            operation: Operation,
            detail: dict,
            target_user_id: int = None
    ):
        """创建新记录"""
        log = cls(
            user_id=user_id,
            target_user_id=target_user_id,
            namespace=ns_obj.namespace.name,
            object=ns_obj.object.name,
            operation=operation.name,
            detail=json.dumps(detail, cls=JsonEncoder, ensure_ascii=False)
        )
        db.session.add(log)
        db.session.commit()
        return log

    @classmethod
    def _get_detail(
            cls,
            ns_obj: _NamespaceObject,
            old_data: dict = None,
            new_data: dict = None,
            special_data: dict = None,
            only_special_data: bool = False,
    ) -> dict:
        """生成详情数据"""
        skip_keys = {'created_at', 'updated_at'}
        if old_data is None:
            old_data = dict()
        if new_data is None:
            new_data = dict()
        detail = {}
        for key, value in old_data.items():
            if key in skip_keys or key in detail:
                continue
            new_value = new_data.get(key)
            if value != new_value:
                detail[key] = dict(old=value, new=new_value)
        for key, value in new_data.items():
            if key in skip_keys or key in detail:
                continue
            if key not in old_data:
                detail[key] = dict(old=None, new=value)
        if not detail and not only_special_data:
            return {}
        if 'id' in old_data:
            detail['id'] = old_data['id']
        if special_data:
            detail.update(special_data)
        return format_op_log_dict(ns_obj.__str__(), detail)

    @classmethod
    def new_login(cls, user_id: int, ns_obj: _NamespaceObject, detail: dict = None, target_user_id: int = None):
        """创建登录记录"""
        if detail is None:
            detail = dict()
        return cls._new(user_id, ns_obj, cls.Operation.LOGIN, detail, target_user_id=target_user_id)

    @classmethod
    def new_query(cls, user_id: int, ns_obj: _NamespaceObject, detail: dict = None, target_user_id: int = None):
        """创建查询记录"""
        if detail is None:
            detail = dict()
        return cls._new(user_id, ns_obj, cls.Operation.QUERY, detail, target_user_id=target_user_id)

    @classmethod
    def new_add(cls, user_id: int, ns_obj: _NamespaceObject, detail: dict = None, target_user_id: int = None):
        """创建新增记录"""
        if detail is None:
            detail = dict()
        return cls._new(user_id, ns_obj, cls.Operation.ADD, detail, target_user_id=target_user_id)

    @classmethod
    def new_edit(
            cls,
            user_id: int,
            ns_obj: _NamespaceObject,
            old_data: dict = None,
            new_data: dict = None,
            special_data: dict = None,
            target_user_id: int = None,
            only_special_data: bool = False,
    ):
        """创建编辑记录"""
        detail = cls._get_detail(
            ns_obj=ns_obj,
            old_data=old_data,
            new_data=new_data,
            special_data=special_data,
            only_special_data=only_special_data,
        )
        if not detail:
            return None
        return cls._new(user_id, ns_obj, cls.Operation.EDIT, detail, target_user_id=target_user_id)

    @classmethod
    def new_add_or_edit(
            cls,
            user_id: int,
            ns_obj: _NamespaceObject,
            old_data: dict = None,
            new_data: dict = None,
            special_data: dict = None,
            target_user_id: int = None
    ):
        """创建新增或编辑记录"""
        if old_data is None:
            cls.new_add(
                user_id=user_id,
                ns_obj=ns_obj,
                detail=new_data,
                target_user_id=target_user_id,
            )
        else:
            cls.new_edit(
                user_id=user_id,
                ns_obj=ns_obj,
                old_data=old_data,
                new_data=new_data,
                special_data=special_data,
                target_user_id=target_user_id,
            )

    @classmethod
    def new_delete(cls, user_id: int, ns_obj: _NamespaceObject, detail: dict = None, target_user_id: int = None):
        """创建删除记录"""
        if detail is None:
            detail = dict()
        return cls._new(user_id, ns_obj, cls.Operation.DELETE, detail, target_user_id=target_user_id)

    @classmethod
    def new_audit(cls, user_id: int, ns_obj: _NamespaceObject, detail: dict = None, target_user_id: int = None):
        """创建审核记录"""
        if detail is None:
            detail = dict()
        return cls._new(user_id, ns_obj, cls.Operation.AUDIT, detail, target_user_id=target_user_id)

    @classmethod
    def new_send(cls, user_id: int, ns_obj: _NamespaceObject, detail: dict = None, target_user_id: int = None):
        """创建发送记录"""
        if detail is None:
            detail = dict()
        return cls._new(user_id, ns_obj, cls.Operation.SEND, detail, target_user_id=target_user_id)

    @classmethod
    def new_stop(cls, user_id: int, ns_obj: _NamespaceObject, detail: dict = None, target_user_id: int = None):
        """创建停止记录"""
        if detail is None:
            detail = dict()
        return cls._new(user_id, ns_obj, cls.Operation.STOP, detail, target_user_id=target_user_id)

    @classmethod
    def new_flat(cls, user_id: int, ns_obj: _NamespaceObject, detail: dict = None, target_user_id: int = None):
        """创建平仓记录"""
        if detail is None:
            detail = dict()
        return cls._new(user_id, ns_obj, cls.Operation.FLAT, detail, target_user_id=target_user_id)
