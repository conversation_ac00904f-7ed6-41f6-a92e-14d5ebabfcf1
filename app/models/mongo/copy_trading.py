# -*- coding: utf-8 -*-
from enum import Enum
from typing import Optional

from app.utils import now
from app.models import db
from app.models.base import M2MModelBase


class CopyTraderOperateLogMySQL(M2MModelBase):
    """MySQL 版本的交易员操作日志"""
    __tablename__ = 'copy_trader_operate_log'

    class OpType(Enum):
        EDIT_PROFIT_SHARE_RATE = "编辑分润比例"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.BigInteger, nullable=False)
    op_type = db.Column(db.StringEnum(OpType), nullable=False)
    detail = db.Column(db.MYSQL_JSON, default={})

    __table_args__ = (
        db.Index('idx_user_id_op_type', 'user_id', 'op_type'),
    )

    @classmethod
    def save_one(cls, user_id: int, op_type, detail: dict):
        r = cls(
            created_at=now(),
            user_id=user_id,
            op_type=op_type.name,
            detail=detail
        )
        db.session.add(r)
        db.session.commit()
        return r

    @classmethod
    def query_user_last_op(
        cls, user_id: int, op_type: OpType, only: list[str] = None
    ) -> Optional['CopyTraderOperateLogMySQL']:
        query = cls.query.filter(
            cls.user_id == user_id,
            cls.op_type == op_type.name
        )
        if only:
            query = query.with_entities(*[getattr(cls, field) for field in only])
        return query.order_by(cls.created_at.desc()).first()