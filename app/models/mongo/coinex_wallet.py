from copy import copy
from enum import Enum
from typing import Optional

from pydantic import BaseModel

from app.models import db
from app.models.base import M2MModelBase

from app.models.mongo import Status, DisplayStatus
from app.utils import AWSBucketPublic


class IconMapSchema(BaseModel):
    """图标映射Schema"""
    icon: Optional[str] = None
    day_banner: Optional[str] = None
    night_banner: Optional[str] = None


class TrafficMySQL(M2MModelBase):
    """MySQL 版本的引流模型"""
    __tablename__ = 'coinex_wallet_traffic'

    class Platform(Enum):
        WEB = 'web'

    class ShowPage(Enum):
        HOME = '首页'
        STAKING = '质押页面'

    class JumpType(Enum):
        URL = 'URL'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False, index=True, comment="名称")
    start_at = db.Column(db.DateTime, nullable=False, index=True, comment="开始时间")
    end_at = db.Column(db.DateTime, nullable=False, index=True, comment="结束时间")
    platform = db.Column(db.StringEnum(Platform), nullable=False, comment="平台")
    show_page = db.Column(db.StringEnum(ShowPage), nullable=False, index=True, comment="展示位置")
    jump_type = db.Column(db.StringEnum(JumpType), nullable=False, comment="跳转类型")
    jump_id = db.Column(db.Integer, nullable=False, comment="跳转链接")
    icon_map = db.Column(db.MYSQL_JSON, comment="图标文件映射")
    content_lang_map = db.Column(db.MYSQL_JSON, default={}, comment="多语言文案")
    display_status = db.Column(
        db.StringEnum(DisplayStatus),
        nullable=False,
        index=True,
        default=DisplayStatus.PENDING,
        comment="上下架状态",
    )
    admin_user_id = db.Column(db.Integer)
    rank = db.Column(db.Integer, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID, comment="删除")

    @classmethod
    def url_name(cls, name):
        """保持与MongoDB版本相同的方法"""
        return f"{name}_url"

    @classmethod
    def add_image_url(cls, item: dict):
        """保持与MongoDB版本相同的方法"""
        tmp_map = copy(item["icon_map"])
        for k, field_key in tmp_map.items():
            item["icon_map"][cls.url_name(k)] = AWSBucketPublic.get_file_url(field_key)
        return item

    def to_dict(self):
        """转换为字典格式"""
        data = super().to_dict(enum_to_name=True)
        # 确保JSON字段正确序列化
        if self.icon_map:
            data['icon_map'] = self.icon_map
        if self.content_lang_map:
            data['content_lang_map'] = self.content_lang_map
        return data