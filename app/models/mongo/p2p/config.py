from datetime import datetime
from decimal import Decimal
from enum import Enum

from pydantic import BaseModel

from app.models import db
from app.models.mongo import Status
from app.models.base import M2MModelBase


class P2pFiatRule(BaseModel):
    """法币规则Pydantic模型"""
    left: Decimal = Decimal(0)
    right: Decimal = Decimal(0)


class P2pFiatConfigMySQL(M2MModelBase):
    """MySQL 版本的法币配置模型"""
    __tablename__ = 'p2p_fiat_config'
    __table_args__ = (
        db.Index('fiat_idx', 'fiat'),
    )

    DEFAULT_LIMIT_UNIT = "USDT"

    class PriceType(Enum):
        AUTO = '自动'
        MANUAL = '人工'

    class Sources(Enum):
        FAIR_PRICE = '公允价格'
        POLYGON = 'polygon'

    # 引用原模型的枚举类

    fiat = db.Column(db.String(32), nullable=False, unique=True)
    precision = db.Column(db.Integer, nullable=False)
    min_limit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 单位 USDT
    max_limit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    is_price_limit = db.Column(db.Boolean, default=False)  # 是否开启限价
    price_type = db.Column(db.StringEnum(PriceType), default=PriceType.AUTO.name)  # 价格类型
    auto_price = db.Column(db.MYSQL_DECIMAL_PRICE, default=Decimal(0))  # 自动价格
    manual_price = db.Column(db.MYSQL_DECIMAL_PRICE, default=Decimal(0))  # 人工价格
    rule = db.Column(db.MYSQL_JSON(P2pFiatRule))  # 限价规则
    source = db.Column(db.StringEnum(Sources), default=Sources.FAIR_PRICE.name)  # 价格类型

    def to_dict(self, **kwargs):
        """
        重写父类的 to_dict 方法，对 form 字段进行额外处理
        """
        rule_data = {}
        if self.rule:
            # 如果是 FormModel 对象，调用其 model_dump 方法转换为字典
            if hasattr(self.rule, 'model_dump'):
                rule_data = self.rule.model_dump()
            else:
                rule_data = self.rule
        
        # 创建一个副本，避免修改原对象
        obj_copy = self.__class__(**{c.name: getattr(self, c.name) for c in self.__table__.columns})
        obj_copy.rule = rule_data
        
        # 调用父类的 to_dict 方法
        return super(P2pFiatConfigMySQL, obj_copy).to_dict(**kwargs)

    @classmethod
    def get_all_valid_data(cls):
        """获取所有有效数据"""
        return db.session.query(cls).all()

    def fair_price(self):
        """获取公允价格"""
        return self.auto_price if self.price_type == self.PriceType.AUTO else self.manual_price


class P2pAssetConfigMySQL(M2MModelBase):
    """MySQL 版本的币种交易区配置模型"""
    __tablename__ = 'p2p_asset_config'
    __table_args__ = (
        db.Index('asset_idx', 'asset'),
    )

    asset = db.Column(db.String(20), nullable=False)
    merchant_fee_rate = db.Column(db.DECIMAL(10, 4), nullable=False)
    min_limit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 单位 asset
    max_limit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 单位 asset
    online_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)  # 上线时间
    precision = db.Column(db.Integer, nullable=False, default=8)  # 数字货币精度
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)

    @classmethod
    def get_assets(cls):
        """获取所有有效资产"""
        return [c.asset for c in db.session.query(cls.asset).filter(cls.status == Status.VALID).all()]

    @classmethod
    def get_all_valid_data(cls):
        """获取所有有效数据"""
        return db.session.query(cls).all()

    @classmethod
    def get_limit_info(cls, asset: str):
        """获取限额信息"""
        query = db.session.query(cls.min_limit, cls.max_limit).filter(
            cls.asset == asset,
            cls.status == Status.VALID
        ).first()
        
        if not query:
            return {}
        
        return dict(
            min_limit=query.min_limit,
            max_limit=query.max_limit
        )

    @classmethod
    def get_merchant_fee_rate(cls, asset: str) -> Decimal:
        """获取商户费率"""
        row = db.session.query(cls.merchant_fee_rate).filter(
            cls.asset == asset,
            cls.status == Status.VALID
        ).first()
        
        if not row:
            raise ValueError(f"{asset} p2p_config not found")
        
        return row.merchant_fee_rate

    @classmethod
    def check_asset_valid(cls, asset: str):
        """检查资产是否有效"""
        return bool(db.session.query(cls.id).filter(
            cls.asset == asset,
            cls.status == Status.VALID.name
        ).first())