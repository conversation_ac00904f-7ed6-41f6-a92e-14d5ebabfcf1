import decimal
from collections import defaultdict
from decimal import Decimal
from enum import Enum

from bson import ObjectId
from dateutil.tz import UTC
from flask_babel import gettext as _

from app.common import P2pBusinessType
from app.models import db
from app.models.base import M2MModelBase
from app.utils import today, now
from pydantic import BaseModel


class AutoOfflineAdvReason(Enum):
    INVENTORY_SHORTAGE = _("库存短缺")
    RISK_CONTROL = _("触发风控")
    KYC_PRO_INVALID = _("高级kyc失效")
    KYC_INSTITUTION_INVALID = _("机构kyc失效")
    FROZEN_MERCHANT = _("商家权限被冻结")
    ASSET_INVALID = _("系统不支持该币种")
    FIAT_INVALID = _("系统不支持该法币")
    PAY_CHANNEL_INVALID = _("支付渠道失效")
    CANCELED_ORDER_LIMIT = _("取消订单数量达到单日上限")
    ORDER_COMPLAINT_LIMIT = _("处理中的申诉订单数量达到上限")
    MERCHANT_2FA_INVALID = _("安全工具失效")
    OFFLINE_BY_ADMIN = _("广告单违反P2P交易规则，已被自动下架")
    PRICE_LIMIT = _("价格偏离限价区间")
    CANCEL_MERCHANT = _("商家取消身份")
    NO_MARGIN = _("保证金不足")


class TradeStatisticsModel(BaseModel):
    """广告交易统计 Pydantic Model"""
    deal_count: int = 0  # 成交数
    completion_rate: float = 0  # 完单率
    acceptance_rate: float = 0  # 接单率
    avg_payment_time: int = 0  # 平均支付时间 单位s
    avg_release_time: int = 0  # 平均放币时间 单位s


class P2pAdvertisingMySQL(M2MModelBase):
    """MySQL 版本的广告模型"""
    __tablename__ = 'p2p_advertising'

    class StocksMode(Enum):
        UNLIMITED = "unlimited"
        LIMITED = "limited"

    class Status(Enum):
        OFFLINE = "offline"
        ONLINE = "online"

    mongo_id = db.Column(db.String(32), nullable=False, index=True)
    adv_number = db.Column(db.String(32), nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    adv_type = db.Column(db.StringEnum(P2pBusinessType), nullable=False)
    base = db.Column(db.String(32), nullable=False)
    quote = db.Column(db.String(32), nullable=False)
    price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)
    stocks_mode = db.Column(db.StringEnum(StocksMode), nullable=False, default=StocksMode.UNLIMITED)
    stocks_quantity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    lock_stocks_quantity = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    is_manually_limit = db.Column(db.Boolean, nullable=False, default=False)
    max_limit = db.Column(db.MYSQL_DECIMAL_26_8)
    min_limit = db.Column(db.MYSQL_DECIMAL_26_8)
    limit_unit = db.Column(db.String(32))
    pay_channel_ids = db.Column(db.MYSQL_JSON)  # 使用JSON类型存储ObjectId列表
    user_channel_ids = db.Column(db.MYSQL_JSON)  # 使用JSON类型存储ObjectId列表
    payment_timeliness = db.Column(db.Integer, nullable=False)
    limitation_filter = db.Column(db.MYSQL_JSON)  # 使用JSON类型存储限制条件列表
    transaction_notes = db.Column(db.Text)
    say_hi_msg = db.Column(db.Text)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.OFFLINE)
    extra = db.Column(db.MYSQL_JSON)  # 使用JSON类型存储交易统计数据

    __table_args__ = (
        db.Index('quote_base_pay_channel_updated_price_idx', 'quote', 'base', 'updated_at', 'price'),
    )

    def to_dict(self, **kwargs):
        """
        重写父类的 to_dict 方法，对 id 字段进行替换处理
        """
        # 创建一个副本，避免修改原对象
        obj_copy = self.__class__(**{c.name: getattr(self, c.name) for c in self.__table__.columns})
        obj_copy.id = self.mongo_id

        # 调用父类的 to_dict 方法
        return super(P2pAdvertisingMySQL, obj_copy).to_dict(**kwargs)

    @classmethod
    def generate_id(cls):
        """生成广告编号"""
        new_id = today().strftime('%Y%m%d') + str(int(str(ObjectId()), 16))[-8:]
        if not cls.query.filter_by(adv_number=new_id).first():
            return new_id
        return cls.generate_id()

    @property
    def real_stocks_quantity(self):
        return self.stocks_quantity - (self.lock_stocks_quantity or Decimal())

    @property
    def quote_min_limit(self):
        from app.business.p2p.utils import P2pUtils
        min_limit = self.min_limit or Decimal()
        if self.limit_unit == self.quote:
            return min_limit
        return P2pUtils.fmt_quote_amount(self.quote, min_limit * self.price)

    @property
    def quote_max_limit(self):
        from app.business.p2p.utils import P2pUtils
        max_limit = self.max_limit or Decimal()
        if self.limit_unit == self.quote:
            return max_limit
        return P2pUtils.fmt_quote_amount(self.quote, max_limit * self.price)

    def limit_data(self) -> dict[str, Decimal]:
        data = {}
        if not self.is_manually_limit:
            return data
        if self.min_limit:
            data['min_limit'] = self.quote_min_limit
        if self.max_limit:
            data['max_limit'] = self.quote_max_limit
        return data

    def to_simple_dict(self):
        """返回广告的简单字典表示"""
        return dict(
            id=str(self.mongo_id),
            adv_number=self.adv_number,
            base=self.base,
            quote=self.quote,
            price=self.price,
            stocks_quantity=self.real_stocks_quantity,
            pay_channel_ids=self.pay_channel_ids,
        )

    def to_index_dict(self):
        """返回用于索引的广告字典表示"""
        base_dict = self.to_simple_dict()
        return dict(
            adv_type=self.show_user_adv_type.name,
            **base_dict,
            **self.limit_data(),
            user_id=self.user_id,
            extra=self.display_extr
        )

    def to_self_dict(self):
        """返回广告的自身字典表示"""
        base_dict = self.to_simple_dict()
        return dict(
            **base_dict,
            is_manually_limit=self.is_manually_limit,
            **self.limit_data(),
            status=self.status.name,
            adv_type=self.adv_type.name,
            updated_at=self.updated_at,
            created_at=self.created_at,
            stocks_mode=self.stocks_mode.name
        )

    def to_check_params_dict(self):
        """返回用于参数检查的广告字典表示"""
        base_dict = self.to_simple_dict()
        return dict(
            **base_dict,
            is_manually_limit=self.is_manually_limit,
            adv_type=self.adv_type,
            limit_unit=self.limit_unit,
            min_limit=self.min_limit,
            max_limit=self.max_limit,
            stocks_mode=self.stocks_mode
        )

    @property
    def display_extr(self):
        if not self.extra:
            return TradeStatisticsModel().model_dump()
        return self.extra

    @property
    def show_user_adv_type(self):
        if self.adv_type == P2pBusinessType.SELL:
            return P2pBusinessType.BUY
        else:
            return P2pBusinessType.SELL

    @property
    def payment_timeliness_minute(self):
        return self.payment_timeliness // 60

    @classmethod
    def get_adv_by_id(cls, id_):
        return cls.query.filter_by(mongo_id=id_).first()

    @classmethod
    def get_user_adv_by_id(cls, id_, user_id):
        return cls.query.filter_by(mongo_id=id_, user_id=user_id).first()

    @classmethod
    def query_user_advertising(cls, user_id: int, page: int, limit: int) -> tuple[int, list]:
        query = cls.query.filter_by(status=cls.Status.ONLINE)
        if user_id:
            query = query.filter_by(user_id=user_id)
        # 商家详情广告排序: 根据更新顺序，优先买单(用户角度)
        query = query.order_by(cls.adv_type.desc(), cls.updated_at.desc())
        total = query.count()
        items = query.offset((page - 1) * limit).limit(limit)
        return total, items


ADV_FIELDS_DISPLAY_MAPPER = {
    'adv_type': "广告方向",
    'base': "数字货币",
    'quote': "法币",
    'price': "价格",
    'stocks_mode': "库存模式",
    'stocks_quantity': "库存数量",
    'max_limit': "最大单笔限额",
    'min_limit': "最小单笔限额",
    'limit_unit': "单笔限额单位",
    'is_manually_limit': "是否手动开启单笔限额",
    'pay_channel_ids': "付款方式",
    'user_channel_ids': "收款方式",
    'payment_timeliness': "付款时效",
    'limitation_filter': "高级设置",
    'transaction_notes': "交易备注",
    'say_hi_msg': "自动回复",
    'status': "广告状态",

}


class AdminStocksMode(Enum):
    UNLIMITED = "无库存模式"
    LIMITED = "有库存模式"


class P2pAdvertisingChangeLogMySQL(M2MModelBase):
    """MySQL 版本的广告变更日志模型"""
    __tablename__ = 'p2p_advertising_change_log'

    mongo_id = db.Column(db.String(32), nullable=False, index=True)
    adv_id = db.Column(db.String(32), nullable=False, index=True)
    old_snapshot = db.Column(db.MYSQL_JSON)  # 使用JSON类型存储快照数据
    new_snapshot = db.Column(db.MYSQL_JSON)  # 使用JSON类型存储快照数据
    auto_offline_reason = db.Column(db.Text)

    @classmethod
    def save_change_log(cls, old_adv, new_adv, offline_reason: AutoOfflineAdvReason = None, extra=None):
        adv_id = new_adv.mongo_id
        if offline_reason:
            if offline_reason == AutoOfflineAdvReason.PRICE_LIMIT:
                offline_reason = (f"【系统操作】由于价格偏离限价区间，修改 【 广告状态 】字段, 由 生效中 修改为 未生效"
                                  f"（广告单价格为 {new_adv.price}，当前限价区间为 {extra['min_price']}~{extra['max_price']} ")
            else:
                offline_reason = offline_reason.value
        
        log = cls(
            adv_id=str(adv_id),
            old_snapshot=old_adv.to_dict(enum_to_name=True) if old_adv else {},
            new_snapshot=new_adv.to_dict(enum_to_name=True) if new_adv else {},
            auto_offline_reason=offline_reason
        )
        db.session.add(log)
        db.session.commit()
        return log

    @classmethod
    def get_pay_channel_names(cls, channel_ids: set[str]):
        from .pay_channel import P2pPayChannelMySQL
        channels = P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.mongo_id.in_(channel_ids)).all()
        return ", ".join([i.name for i in channels])

    def get_user_pay_channel_names(self, user_pay_channel_ids: set[str]):
        from .pay_channel import UserPayChannelMySQL
        user_channels = UserPayChannelMySQL.query.filter(UserPayChannelMySQL.mongo_id.in_(user_pay_channel_ids)).all()
        channel_ids = {i.pay_channel_id for i in user_channels}
        return self.get_pay_channel_names(channel_ids)

    @classmethod
    def get_limitation_filter_diff(cls, new_limitation_filter: list, old_limitation_filter: list):
        from app.business.p2p.advertising import UserReachLimitationFilter
        new_filter = UserReachLimitationFilter.parse_filter(new_limitation_filter)
        old_filter = UserReachLimitationFilter.parse_filter(old_limitation_filter)
        new_key_values = {i[0]: i[2] for i in new_filter}
        old_key_values = {i[0]: i[2] for i in old_filter}
        add_keys = set(new_key_values.keys()) - set(old_key_values.keys())
        remove_keys = set(old_key_values.keys()) - set(new_key_values.keys())
        change_keys = set(old_key_values.keys()) & set(new_key_values.keys())
        result = []
        for k in add_keys:
            result.append(f"新增限制条件: {k.value}")
        for k in remove_keys:
            result.append(f"删除限制条件: {k.value}")
        for k in change_keys:
            if old_key_values[k] != new_key_values[k]:
                result.append(f"修改限制条件: {k.value} {old_key_values[k]} -> {new_key_values[k]}")
        return result

    def to_admin_display(self):
        from app.api.admin.p2p.advertising import AdminStatusEnum
        old_snapshot = self.old_snapshot
        new_snapshot = self.new_snapshot

        def is_decimal(number_str):
            try:
                if isinstance(number_str, str):
                    Decimal(number_str)
                    return True
            except decimal.InvalidOperation:
                return False

        def update_display(keys_):
            r = []

            for k in keys_:
                old_value = old_snapshot[k]
                new_value = new_snapshot[k]

                if k == "pay_channel_ids" and new_snapshot['adv_type'] == P2pBusinessType.BUY.name:
                    add_channels = set(new_value) - set(old_value)
                    remove_channels = set(old_value) - set(new_value)

                    if add_channels:
                        r.append(f'新增付款方式: {self.get_pay_channel_names(add_channels)}')
                    if remove_channels:
                        r.append(f'删除付款方式: {self.get_pay_channel_names(remove_channels)}')
                    continue
                elif k == "user_channel_ids" and new_snapshot['adv_type'] == P2pBusinessType.SELL.name:
                    add_channels = set(new_value) - set(old_value)
                    remove_channels = set(old_value) - set(new_value)
                    if add_channels:
                        r.append(f'新增收款方式: {self.get_user_pay_channel_names(add_channels)}')
                    if remove_channels:
                        r.append(f'删除收款方式: {self.get_user_pay_channel_names(remove_channels)}')
                    continue
                elif k == "limitation_filter":
                    if filter_change := self.get_limitation_filter_diff(new_value, old_value):
                        r += filter_change
                    continue
                elif k == "is_manually_limit" and old_value != new_value:
                    r.append(
                        f"修改【 {ADV_FIELDS_DISPLAY_MAPPER[k]} 】字段, {'关闭 -> 开启' if new_value else '开启 -> 关闭'} ")
                    continue
                if not old_value and not new_value:
                    continue
                if old_value and not new_value:
                    r.append(f'删除 【 {ADV_FIELDS_DISPLAY_MAPPER[k]} 】字段, 删除: {old_value}')
                if not old_value and new_value:
                    r.append(f'新增 【 {ADV_FIELDS_DISPLAY_MAPPER[k]} 】字段, {new_value}')
                if is_decimal(old_value) and is_decimal(new_value):
                    old_value = Decimal(old_value)
                    new_value = Decimal(new_value)
                if (k not in ["user_channel_ids", "pay_channel_ids"] and old_value and new_value
                        and old_value != new_value):
                    if k == "status":
                        old_value = AdminStatusEnum[old_value].value
                        new_value = AdminStatusEnum[new_value].value
                    if k == "stocks_mode":
                        old_value = AdminStocksMode[old_value].value
                        new_value = AdminStocksMode[new_value].value
                    if k == "payment_timeliness":
                        old_value = f"{old_value // 60} 分钟"
                        new_value = f"{new_value // 60} 分钟"
                    r.append(f'修改 【 {ADV_FIELDS_DISPLAY_MAPPER[k]} 】字段, 由 {old_value} 修改为 {new_value}')

            return r

        show_keys = ADV_FIELDS_DISPLAY_MAPPER.keys()
        old_keys = old_snapshot.keys()
        new_keys = new_snapshot.keys()
        common_keys = set(new_keys) & set(old_keys) & set(show_keys)
        change_logs = update_display(common_keys)

        if self.auto_offline_reason:
            change_logs.append(f"广告自动下架 原因: {self.auto_offline_reason}")

        return change_logs

    @classmethod
    def get_adv_change_logs(cls, adv_id: str):
        change_logs = cls.query.filter_by(adv_id=adv_id).order_by(cls.id.desc()).all()
        result = []
        for i in change_logs:
            show_info = i.to_admin_display()
            if not show_info:
                continue
            result.append({
                'timestamp': i.created_at,
                "change_logs": i.to_admin_display()
            })
        return result

    @classmethod
    def get_adv_online_data(cls, adv: 'P2pAdvertisingMySQL', start_datetime, end_datetime):
        # 广告修改日志数量较少，直接查询即可
        end_datetime = min(end_datetime, now())
        query_logs = cls.query.filter_by(adv_id=adv.mongo_id).order_by(cls.id).all()
        logs = [i for i in query_logs if start_datetime <= i.created_at.replace(tzinfo=UTC) < end_datetime]
        start_datetime = max(start_datetime, adv.created_at.replace(tzinfo=UTC))
        if not logs:
            if adv.status == P2pAdvertisingMySQL.Status.ONLINE:
                return [[start_datetime, end_datetime]]
            else:
                return []
        data = []
        # 处理新增广告的情况
        for log in logs:  # type: P2pAdvertisingChangeLogMySQL
            old_status = log.old_snapshot['status']
            new_status = log.new_snapshot['status']
            log_created_at = log.created_at.replace(tzinfo=UTC)
            if old_status == new_status:
                continue
            if old_status == P2pAdvertisingMySQL.Status.ONLINE.name and new_status == P2pAdvertisingMySQL.Status.OFFLINE.name:
                # 下架
                if not data:
                    data.append([start_datetime, log_created_at])
                else:
                    last_data = data[-1]
                    last_data[1] = log_created_at
            else:
                # 上架
                data.append([log_created_at, None])
        if not data:
            return data
        last_online_data = data[-1]
        if last_online_data[1] is None:
            last_online_data[1] = end_datetime
        data = [i for i in data if i[0] and i[1]]
        return data

    @classmethod
    def get_quote_adv_online_map(cls, adv: 'P2pAdvertisingMySQL', start_datetime, end_datetime):
        end_datetime = min(end_datetime, now())
        query_logs = cls.query.filter_by(adv_id=adv.mongo_id).order_by(cls.id).all()
        all_data = defaultdict(list)
        logs = [i for i in query_logs if start_datetime <= i.created_at.replace(tzinfo=UTC) < end_datetime]
        start_datetime = max(start_datetime, adv.created_at.replace(tzinfo=UTC))
        if not logs:
            if adv.status == P2pAdvertisingMySQL.Status.ONLINE:
                all_data[adv.quote].append([start_datetime, end_datetime])
            return all_data

        online = P2pAdvertisingMySQL.Status.ONLINE.name
        offline = P2pAdvertisingMySQL.Status.OFFLINE.name
        for log in logs:  # type: P2pAdvertisingChangeLogMySQL
            old_status, new_status = log.old_snapshot['status'], log.new_snapshot['status']
            old_quote, new_quote = log.old_snapshot['quote'], log.new_snapshot['quote']
            log_created_at = log.created_at.replace(tzinfo=UTC)

            # 1. 币种下架
            if old_status == online and new_status == offline:
                data = all_data[old_quote]
                if not data:
                    data.append([start_datetime, log_created_at])
                else:
                    data[-1][1] = log_created_at
            # 2. 币种上架
            elif old_status == offline and new_status == online:
                all_data[new_quote].append([log_created_at, None])
            # 3. 上架中币种切换
            elif old_status == online and new_status == online and old_quote != new_quote:
                all_data[old_quote][-1][1] = log_created_at
                all_data[new_quote].append([log_created_at, None])

        if not all_data:
            return all_data
        for quote, data_list in all_data.items():
            if data_list[-1][1] is None:
                data_list[-1][1] = end_datetime
        return all_data


class P2pAdvListSnapshotMySQL(M2MModelBase):
    """MySQL 版本的广告列表快照模型"""
    __tablename__ = 'p2p_adv_list_snapshot'

    mongo_id = db.Column(db.String(32), nullable=False, index=True)
    snap = db.Column(db.MYSQL_JSON)  # 使用JSON类型存储快照列表
    snap_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    fiat = db.Column(db.String(32), nullable=False)
    side = db.Column(db.StringEnum(P2pBusinessType), nullable=False)

    __table_args__ = (
        db.Index('fiat_side', 'fiat', 'side'),
        db.Index('snap_at', 'snap_at'),
    )