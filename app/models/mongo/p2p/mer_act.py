from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from enum import Enum

from pydantic import BaseModel, Field

from app.common import P2pBusinessType, P2pMerActRewardType
from app.models.base import M2MModelBase, db

from app.utils import now

# Pydantic 模型用于 MySQL JSON 字段
class ConfigModel(BaseModel):
    """配置模型，用于排名和完单率奖励"""
    rank_min: Decimal = Field(..., description="排名最小值")
    rank_max: Decimal = Field(..., description="排名最大值")
    rank_amount: Decimal = Field(..., description="奖励金额")


class P2pMerActMySQL(M2MModelBase):
    """p2p商家活动 MySQL 模型"""
    __tablename__ = 'p2p_mer_act'
    
    class Status(Enum):
        ONLINE = '已上架'
        PENDING = '待上架'
        OFFLINE = '已下架'
        EARLY_OFFLINE = '提前下架'

    Config = ConfigModel
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False, comment="活动名称")
    act_id = db.Column(db.Integer, nullable=False, unique=True, comment="活动ID")
    activity_id = db.Column(db.Integer, nullable=False, comment="礼物活动ID,用于发奖")
    apply_start_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, comment="报名开始时间")
    apply_end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, comment="报名结束时间")
    start_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, comment="活动开始时间")
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, comment="活动结束时间")
    valid_start_hour = db.Column(db.Integer, nullable=False, comment="每日有效挂单开始小时")
    valid_end_hour = db.Column(db.Integer, nullable=False, comment="每日有效挂单结束时小时")
    reward_type = db.Column(db.StringEnum(P2pMerActRewardType), nullable=False, comment="活动奖励类型")
    reward_asset = db.Column(db.String(255), nullable=False, comment="奖励币种")
    fiat = db.Column(db.String(255), nullable=False, comment="法币交易区")
    
    # 使用 MYSQL_JSON 替换 ListField
    pay_channel_ids = db.Column(db.MYSQL_JSON, comment="支付渠道ID列表")
    max_limit = db.Column(db.MYSQL_DECIMAL_26_8, comment="最大限额")
    min_limit = db.Column(db.MYSQL_DECIMAL_26_8, comment="最小限额")
    
    buy_reward = db.Column(db.MYSQL_DECIMAL_26_8, comment="买币区奖励")
    sell_reward = db.Column(db.MYSQL_DECIMAL_26_8, comment="卖币区奖励")
    reward_lock_day = db.Column(db.Integer, nullable=False, comment="奖励锁定时间(天)")
    
    # 使用 MYSQL_JSON 替换 DictField
    lang_map = db.Column(db.MYSQL_JSON, nullable=False, comment="多语言数据")
    image_key = db.Column(db.String(255), comment="活动图片")
    status = db.Column(db.StringEnum(Status), default=Status.PENDING, comment="活动状态")
    
    apply_count = db.Column(db.Integer, default=0, comment="参与人数")
    buy_reward_count = db.Column(db.Integer, default=0, comment="买入区已发放奖励")
    sell_reward_count = db.Column(db.Integer, default=0, comment="卖出区已发放奖励")
    
    # 使用 MYSQL_JSON 结合 Pydantic 替换 EmbeddedDocument
    completion_rate_list = db.Column(db.MYSQL_JSON(ConfigModel), comment="完单率奖励列表")
    rank_rate_list = db.Column(db.MYSQL_JSON(ConfigModel), comment="排名奖励列表")
    
    def _process_config_list(self, config_list):
        """
        处理 ConfigModel 类型的列表，将其转换为字典列表
        """
        result = []
        if config_list:
            for config in config_list:
                # 如果是 ConfigModel 对象，调用其 model_dump 方法转换为字典
                if hasattr(config, 'model_dump'):
                    result.append(config.model_dump())
                else:
                    result.append(config)
        return result

    def to_dict(self, **kwargs):
        """
        重写父类的 to_dict 方法，对 ConfigModel类型 字段进行额外处理
        """
        # 处理 completion_rate_list 和 rank_rate_list
        completion_data = self._process_config_list(self.completion_rate_list)
        rank_data = self._process_config_list(self.rank_rate_list)
        
        # 创建一个副本，避免修改原对象
        obj_copy = self.__class__(**{c.name: getattr(self, c.name) for c in self.__table__.columns})
        obj_copy.completion_rate_list = completion_data
        obj_copy.rank_rate_list = rank_data

        # 调用父类的 to_dict 方法
        return super(P2pMerActMySQL, obj_copy).to_dict(**kwargs)

    @classmethod
    def get_online_act(cls, act_id: str):
        """获取上线中的活动"""
        return cls.query.filter(
            cls.act_id == act_id,
            cls.status == cls.Status.ONLINE
        ).first()
    
    def get_valid_side(self):
        """获取有效的交易方向"""
        ret = []
        if self.buy_reward:
            ret.append(P2pBusinessType.BUY)
        if self.sell_reward:
            ret.append(P2pBusinessType.SELL)
        return ret
    
    def early_offline(self):
        """提前下线活动"""
        self.status = self.Status.EARLY_OFFLINE
        now_ = now()
        self.apply_end_at = min(self.apply_end_at, now_)
        self.end_at = min(self.end_at, now_)
        db.session.commit()
    
    @property
    def real_end_at(self):
        """获取实际结束时间"""
        return self.end_at - timedelta(seconds=1)
    
    def get_act_days(self):
        """获取活动天数"""
        if self.end_at <= self.start_at:
            return 0
        else:
            return (self.real_end_at.date() - self.start_at.date()).days + 1
    
    def get_all_reward(self):
        """获取所有奖励"""
        act_days = self.get_act_days()
        buy_reward = self.buy_reward * act_days if self.buy_reward else Decimal()
        sell_reward = self.sell_reward * act_days if self.sell_reward else Decimal()
        return buy_reward + sell_reward
