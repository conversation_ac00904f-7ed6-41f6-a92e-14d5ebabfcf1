# -*- coding: utf-8 -*-

from app.models import db
from app.models.base import M2MModelBase


class ApiAssetExchangeOrderMySQL(M2MModelBase):
    """MySQL 版本的 API 资产兑换订单模型"""
    __tablename__ = 'api_asset_exchange_orders'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.BigInteger, nullable=False)
    exchange_order_id = db.Column(db.BigInteger, nullable=False)
    client_id = db.Column(db.String(255), nullable=False)

    __table_args__ = (
        db.Index('idx_user_id_exchange_order_id', 'user_id', 'exchange_order_id'),
        db.Index('idx_user_id_client_id', 'user_id', 'client_id'),
        db.Index('idx_exchange_order_id', 'exchange_order_id'),
    )

    @classmethod
    def save_one(cls, user_id: int, exchange_order_id: int, client_id: str):
        """保存一条记录"""
        record = cls(
            user_id=user_id,
            exchange_order_id=exchange_order_id,
            client_id=client_id
        )
        db.session.add(record)
        db.session.commit()
        return record

    @classmethod
    def query_by_user_id_client_id(
        cls, user_id: int, client_id: str, only: list[str] = None
    ) -> list['ApiAssetExchangeOrderMySQL']:
        """根据用户ID和客户端ID查询订单"""
        query = cls.query.filter(
            cls.user_id == user_id,
            cls.client_id == client_id
        )
        if only:
            query = query.with_entities(*[getattr(cls, field) for field in only])
        return query.all()

    @classmethod
    def query_by_order_ids(
        cls, order_ids: list[int], only: list[str] = None
    ) -> list['ApiAssetExchangeOrderMySQL']:
        """根据订单ID列表查询订单"""
        query = cls.query.filter(cls.exchange_order_id.in_(order_ids))
        if only:
            query = query.with_entities(*[getattr(cls, field) for field in only])
        return query.all()