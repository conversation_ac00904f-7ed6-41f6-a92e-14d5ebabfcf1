# -*- coding: utf-8 -*-
import json
from enum import Enum

from .base import db, ModelBase
from app.common import Language, Media
from app.utils import AWSBucketPublic


class PeriodType(Enum):
    MINUTE = "minute"
    HOUR = "hour"
    DAY = "day"

    def to_seconds(self):
        if self == PeriodType.MINUTE:
            return 60
        if self == PeriodType.HOUR:
            return 60 * 60
        if self == PeriodType.DAY:
            return 60 * 60 * 24
        raise ValueError


class CoinInformation(ModelBase):

    AVAILABLE_LANGS = (
        Language.EN_US, Language.ZH_HANT_HK, Language.ZH_HANS_CN,
        Language.KO_KP, Language.JA_JP, Language.RU_KZ, Language.ES_ES,
        Language.ID_ID, Language.FA_IR, Language.TR_TR, Language.VI_VN,
        Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
        Language.TH_TH, Language.PL_PL, Language.IT_IT)

    class Type(Enum):
        COIN = '公链'
        TOKEN = '非公链'

    class Status(Enum):
        VALID = '上架中'
        DELETED = '已下架'

    class CirculationType(Enum):
        MANUAL_EDIT = "手动编辑"
        AUTO_TRACK = "自动追踪"
        THIRD_PARTY = "第三方"

    created_by = db.Column(db.Integer, db.ForeignKey('user.id'),
                           nullable=False)
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'),
                           nullable=False)
    code = db.Column(db.String(32), nullable=False, unique=True)
    name = db.Column(db.String(64), nullable=False)
    icon = db.Column(db.String(128), nullable=False)
    thumbnail_icon = db.Column(db.String(128), nullable=False)
    # 流通总量
    circulation = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 流通总量是否提示
    circulation_notice = db.Column(db.Boolean, default=False, nullable=False)
    # 流通量获取类型
    circulation_type = db.Column(db.StringEnum(CirculationType), default=CirculationType.THIRD_PARTY,
                                 nullable=False)
    # 第三方获取的api_id
    api_id = db.Column(db.String(128), index=True, nullable=True)
    # 上币时间
    online_time = db.Column(db.MYSQL_DATETIME_6)
    issued_date = db.Column(db.DATE)
    issued_price_data = db.Column(db.Text, nullable=False)
    total_supply = db.Column(db.String(64), nullable=False)
    official_website = db.Column(db.String(128))
    white_paper = db.Column(db.String(512))
    source_code = db.Column(db.String(128))
    explorer = db.Column(db.String(128))
    report_url = db.Column(db.String(512))
    contract_address = db.Column(db.String(256))
    intro_en = db.Column(db.Text, nullable=False)  # 兼容保留
    intro_cn = db.Column(db.Text, nullable=False)  # 兼容保留

    telegram = db.Column(db.String(256))
    facebook = db.Column(db.String(256))
    twitter = db.Column(db.String(256))
    reddit = db.Column(db.String(256))
    medium = db.Column(db.String(256))
    discord = db.Column(db.String(256))
    youtube = db.Column(db.String(256))
    instagram = db.Column(db.String(256))
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    is_st = db.Column(db.Boolean, default=False)    # 是否被标记为st（即将下架）

    # 代币用途字段: [{"type":"User","percent":"70"}, ...]
    token_usage_data = db.Column(db.Text)
    # 代币解锁字段: [{"type":"User","date":"202410","amount":"3500000"}, ...]
    token_unlock_data = db.Column(db.Text)

    @classmethod
    def get_online_ts(cls, asset: str) -> int | None:
        online_dt_q = cls.query.filter(
            cls.code == asset,
            cls.status == cls.Status.VALID
        ).with_entities(cls.online_time).first()
        online_dt = online_dt_q.online_time if online_dt_q else None
        return int(online_dt.timestamp()) if online_dt else None

    @classmethod
    def get_all_assets_online_ts(cls) -> dict[str, int]:
        online_dt_q = cls.query.filter(
            cls.status == cls.Status.VALID
        ).with_entities(cls.code, cls.online_time).all()
        return {v.code: int(v.online_time.timestamp())
                for v in online_dt_q if v.online_time}

    @property
    def social_url(self):
        data = []
        if self.twitter:
            data.append(dict(media=Media.TWITTER.value, url=self.twitter))
        if self.telegram:
            data.append(dict(media=Media.TELEGRAM.value, url=self.telegram))
        if self.facebook:
            data.append(dict(media=Media.FACEBOOK.value, url=self.facebook))
        if self.reddit:
            data.append(dict(media=Media.REDDIT.value, url=self.reddit))
        if self.medium:
            data.append(dict(media=Media.MEDIUM.value, url=self.medium))
        if self.discord:
            data.append(dict(media=Media.DISCORD.value, url=self.discord))
        if self.youtube:
            data.append(dict(media=Media.YOUTUBE.value, url=self.youtube))
        if self.instagram:
            data.append(dict(media=Media.INSTAGRAM.value, url=self.instagram))
        return data

    @classmethod
    def get_icon_url(cls, icon_key: str) -> str:
        if not icon_key:
            return ''
        return AWSBucketPublic.get_file_url(icon_key)

    @classmethod
    def get_thumbnail_icon_url(cls, thumbnail_icon_key: str, icon_key: str) -> str:
        file_key = thumbnail_icon_key or icon_key
        return cls.get_icon_url(file_key)


class CoinInformationTrans(ModelBase):

    coin_information_id = db.Column(db.Integer, db.ForeignKey('coin_information.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    description = db.Column(db.Text, nullable=False)  # 项目简介
    introduces = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)  # 项目介绍:标题,内容和富文本内容，存json数组，
    # [{'title': '', 'content': '', 'rich_content': ''}]
    is_auto_translation = db.Column(db.Boolean, nullable=True, default=False)

    @property
    def introduce_list(self) -> list:
        return json.loads(self.introduces) if self.introduces else []


class CoinExplorer(ModelBase):
    """币种区块浏览器信息"""
    class Status(Enum):
        VALID = '上架中'
        DELETED = '已下架'

    coin_info_id = db.Column(db.Integer, db.ForeignKey('coin_information.id'), nullable=False)
    name = db.Column(db.String(64), nullable=False)
    url = db.Column(db.String(128), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    coin_info = db.relationship('CoinInformation',
                                backref=db.backref("coin_explorers", lazy='dynamic'))


class ApiCoinInformationResult(ModelBase):

    class ApiSource(Enum):
        TOKEN_INSIGHT = 'token_insight'

    finished_at = db.Column(db.MYSQL_DATETIME_6, index=True)

    coin_info_id = db.Column(db.Integer, index=True, nullable=True)
    api_source = db.Column(db.StringEnum(ApiSource), nullable=False)
    api_id = db.Column(db.String(128), index=True, nullable=True)

    official_website = db.Column(db.String(512))
    white_paper = db.Column(db.String(512))
    report_url = db.Column(db.String(512))
    source_code = db.Column(db.String(512))

    telegram = db.Column(db.String(512))
    facebook = db.Column(db.String(512))
    twitter = db.Column(db.String(512))
    reddit = db.Column(db.String(512))
    medium = db.Column(db.String(512))
    discord = db.Column(db.String(512))
    youtube = db.Column(db.String(512))
    instagram = db.Column(db.String(512))
    en_short_description = db.Column(db.Text, nullable=False, default='')
    cn_short_description = db.Column(db.Text, nullable=False, default='')
    en_long_description = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    cn_long_description = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')


class UserFavoriteAsset(ModelBase):

    __table_args__ = (
        db.UniqueConstraint('user_id', 'asset',
                            name='user_id_asset_unique'),
    )

    class StatusType(Enum):
        PASSED = "passed"
        DELETED = "deleted"

    class EditUserType(Enum):
        SYSTEM = 'system'
        USER = 'user'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    rank = db.Column(db.Integer, nullable=False, index=True)
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.PASSED)
    edited_by = db.Column(db.StringEnum(EditUserType), nullable=False, default=EditUserType.USER)

    @classmethod
    def reorder_user_asset_rank(cls, user_id):
        query = cls.query.filter(
            cls.user_id == user_id,
            cls.status == cls.StatusType.PASSED,
        ).order_by(cls.rank.asc()).all()
        rank = 0
        for db_record in query:
            rank += 1
            db_record.rank = rank
        db.session.commit()


class AssetTagRelation(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('asset', 'tag_id',
                            name='asset_tag_id_unique'),
    )

    class StatusType(Enum):
        PASSED = "passed"
        DELETED = "deleted"

    asset = db.Column(db.String(32), nullable=False)
    tag_id = db.Column(db.Integer, db.ForeignKey('asset_tag.id'), nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.PASSED)


class AssetTag(ModelBase):
    """币种标签"""
    class StatusType(Enum):
        PASSED = "passed"
        DELETED = "deleted"

        INVALID = "invalid"  # 不可逆删除

    AMMTag = "AMM"
    MarginTag = '杠杆'

    display_name = db.Column(db.String(64), nullable=False, default=False)
    rank = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(StatusType), nullable=False, default=StatusType.PASSED)
    is_show = db.Column(db.Boolean, nullable=False, default=False)   # tag描述是否在交易页展示

    @classmethod
    def get_auto_tag_ids(cls):
        """获取可以自动加标签tag的tag_ids"""
        records = cls.query.filter(cls.display_name.in_([cls.AMMTag, cls.MarginTag])).with_entities(cls.id).all()
        auto_tag_ids = [i.id for i in records]
        return auto_tag_ids


class AssetTagTranslation(ModelBase):
    LANGS = [
        Language.EN_US, Language.ZH_HANT_HK, Language.ZH_HANS_CN,
        Language.KO_KP, Language.JA_JP, Language.RU_KZ, Language.ES_ES,
        Language.ID_ID, Language.FA_IR, Language.TR_TR, Language.VI_VN,
        Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
        Language.TH_TH, Language.PL_PL, Language.IT_IT
    ]

    tag_id = db.Column(db.Integer, db.ForeignKey('asset_tag.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    name = db.Column(db.String(32), nullable=False)
    title = db.Column(db.String(256), nullable=False)  # 标签描述标题
    description = db.Column(db.String(4096), nullable=False)  # 标签描述内容


class AssetTopic(ModelBase):
    """币种专题"""
    class StatusType(Enum):
        PASSED = "passed"
        DELETED = "deleted"

    display_name = db.Column(db.String(64), nullable=False, default=False)
    rank = db.Column(db.Integer, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.PASSED)


class AssetTopicTranslation(ModelBase):
    # 中文简体、繁体、英语、韩语、日语、俄语、西班牙语
    LANGS = [
        Language.EN_US, Language.ZH_HANT_HK, Language.ZH_HANS_CN,
        Language.KO_KP, Language.JA_JP, Language.RU_KZ, Language.ES_ES,
    ]

    topic_id = db.Column(db.Integer, db.ForeignKey('asset_topic.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    name = db.Column(db.String(32), nullable=False)
    url = db.Column(db.String(512), nullable=False)


class AssetPriceKline(db.Model):
    """币种价格 (to USD)"""
    __table_args__ = (
        db.Index('asset_period_time_index', 'asset', 'period', 'time'),
    )

    id = db.Column(db.BigInteger, primary_key=True)
    asset = db.Column(db.String(32), nullable=False)
    period = db.Column(db.Enum(PeriodType), nullable=False)
    # 时间戳
    time = db.Column(db.Integer, nullable=False, index=True)
    # TO usd
    price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)


class AssetCirculationHistory(db.Model):
    """币种流通量(to USD), one hour to update"""

    __table_args__ = (
        db.Index('asset_time_index', 'asset', 'time'),
    )

    id = db.Column(db.BigInteger, primary_key=True)
    asset = db.Column(db.String(32), nullable=False)
    # 时间戳
    time = db.Column(db.Integer, nullable=False, index=True)
    # 流通量
    circulation = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 流通市值
    circulation_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class NewAsset(ModelBase):
    """新币专区"""

    class StatusType(Enum):
        VALID = "上架中"
        INVALID = "已下架"

    asset = db.Column(db.String(32), nullable=False, unique=True)
    status = db.Column(db.StringEnum(StatusType), nullable=False)
