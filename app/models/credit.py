#!/usr/bin/python
# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from datetime import datetime
from enum import Enum

from sqlalchemy import func

from .base import db, ModelBase
from ..utils.helper import StrTrans as _f


class CreditUser(ModelBase):
    class StatusType(Enum):
        PASS = _f('pass', '正常')
        DELETE = _f('delete', '删除')

    class CreditGrade(Enum):
        SUPER = '超级授信'
        NORMAL = '普通授信'

    withdraw_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), unique=True)

    credit_grade = db.Column(db.StringEnum(CreditGrade), nullable=False, default=CreditGrade.NORMAL)
    status = db.Column(db.Enum(StatusType), nullable=False)

    # 为null时 代表 授信用户被删除 或 授信没借币|已还清
    warn_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)

    current_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal(0))
    unfinished_credit_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                          default=Decimal())

    can_withdraw_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal(0))

    can_withdraw = db.Column(db.Boolean, default=True)  # 是否可提现，更新风险率时自动更新

    # 交易受限(禁止现货交易，合约交易仅限平/减仓，杠杆交易仅允许有借币的市场)，在Admin手动设置
    # 只用于Admin的展示和筛选，控制权限实际是UserSettings
    trading_limited = db.Column(db.Boolean, nullable=False, default=False)

    # 以下字段废弃
    trade_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    can_trade_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal(0))
    can_trade = db.Column(db.Boolean, default=True)

    user = db.relationship('User', backref=db.backref('credit_user', uselist=False))


class CreditBalance(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('asset', 'user_id', name='user_id_asset_unique'),
    )

    asset = db.Column(db.String(32), nullable=False)
    unflat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 未平授信余额, 不包括利息
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    interest_amount = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0
    )  # 所有授信记录的未还利息总额(开始借币时计息一次，此后每满1小时计息一次)
    interest_at = db.Column(
        db.MYSQL_DATETIME_6, default=datetime.utcnow
    )  # 更新利息的时间

    @classmethod
    def get_unflat_amount(cls, asset=None, user_id=None):
        query = cls.query
        if user_id:
            query = query.filter_by(
                user_id=user_id,
            )
        if asset:
            query = query.filter_by(
                asset=asset
            )
            record = query.with_entities(
                cls.asset,
                func.sum(cls.unflat_amount).label('total_amount')
            ).first()
            if record:
                return record.total_amount if record.total_amount else Decimal()
            else:
                return Decimal()
        else:
            query_result = query.group_by(cls.asset).with_entities(
                cls.asset,
                func.sum(cls.unflat_amount).label('total_amount')
            ).all()
            result = defaultdict(Decimal)
            for r in query_result:
                if r:
                    if r.total_amount:
                        result[r.asset] = r.total_amount
            return result


class CreditAssetHistory(ModelBase):
    __table_args__ = (
        db.Index('user_id_credit_type_status', 'user_id', 'credit_type', 'status'),
    )

    class CreditType(Enum):
        CREDIT = _f('credit', '授信')
        FLAT = _f('flat', '还币')

    class OperationType(Enum):
        DELETE = _f('delete', '删除')
        CHECK = _f('check', '复审')
        AUDIT = _f('audit', '初审')

    class StatusType(Enum):
        CREATE = _f('create', '待审核')
        AUDIT = _f('audit', '待复核')
        FINISH = _f('finish', '已生效')
        DELETE = _f('delete', '已删除')
        FAIL = _f('fail', '失败')

    # 授信完成时间或者还币完成时间
    finished_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)

    asset = db.Column(db.String(32), nullable=False)
    credit_type = db.Column(db.Enum(CreditType), nullable=False)
    # credit 表示授信金额，flat 表示还币金额（不包括利息）
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    # credit 无意义;
    # flat 表示还的利息金额, amount + interest_amount 是本次还币操作输入的总金额
    interest_amount = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0
    )

    # 操作后未平授信余额
    unflat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    audit_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    check_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    oper_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    user = db.relationship('User',
                           primaryjoin="foreign(CreditAssetHistory.user_id) == User.id",
                           backref=db.backref('credit_asset_histories', lazy='dynamic'))

    status = db.Column(db.Enum(StatusType), nullable=False)

    remark = db.Column(db.String(256), nullable=False, default='')


class CreditAssetRate(ModelBase):
    """ 授信币种-利率表 """

    class StatusType(Enum):
        PASSED = "passed"
        DELETED = "deleted"

    asset = db.Column(db.String(32), nullable=False, unique=True)
    day_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 日利率
    status = db.Column(db.Enum(StatusType), nullable=False)


class CreditAssetUserRate(ModelBase):
    """ 授信币种-用户利率表 """

    __table_args__ = (
        db.UniqueConstraint(
            "user_id",
            "asset",
            name="user_id_asset_unique",
        ),
    )

    class StatusType(Enum):
        PASSED = "passed"
        DELETED = "deleted"

    ALL_ASSET = ""

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    asset = db.Column(db.String(32), nullable=False)  # asset存空字符串 代表ALL
    day_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 日利率
    expired_at = db.Column(db.MYSQL_DATETIME_6)
    remark = db.Column(db.String(256), nullable=False, default="")
    status = db.Column(db.Enum(StatusType), nullable=False)


class CreditAssetInterestReceivableHistory(ModelBase):
    """
    应收授信利息记录表, 更新待收利息插入相关记录

    利息计算规则：
    1. 计算利息是一个小时一次的（取当时最新），如果利率修改了，修改前的时间是按照老的收，
        修改后的时间按新的收。页面上展示最新的利率。
    2. 同一个币种，多条授信记录合并到一起后再计算利息
    """

    __table_args__ = (db.Index("user_id_asset", "user_id", "asset"),)

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    asset = db.Column(db.String(32), nullable=False)

    unflat_amount = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False
    )  # 计算应收授信利息时刻的该币种的未平授信余额
    receivable_amount = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False
    )  # 应收授信利息

    # 冗余字段
    day_rate = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False
    )  # 计算利息时用的日利率（取计算时刻的最新设置的利率）
