# -*- coding: utf-8 -*-

from enum import Enum

from flask_babel import gettext as _
from sqlalchemy import or_

from ..config import config
from .base import db, ModelBase
from .user import User, SubAccount
from ..utils import quantize_amount


class AmmMarket(ModelBase):

    class AmmType(Enum):
        INFINITE = '无限区间'
        FINITE = '有限区间'

    class Status(Enum):
        ONLINE = '上架'
        OFFLINE = '下架'

    name = db.Column(db.String(32), nullable=False, unique=True)
    amm_type = db.Column(db.Enum(AmmType), nullable=False)
    fee_refund_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    delta = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 档位差
    initial_price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)
    min_price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)
    max_price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)
    allow_in_bidding = db.Column(db.MYSQL_BOOL, nullable=False, default=False)  # 允许竞价模式注入流动性
    status = db.Column(db.Enum(Status), nullable=False, default=Status.ONLINE)


class LiquidityPool(ModelBase):

    market = db.Column(db.String(32), nullable=False, unique=True)
    liquidity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    system_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    @classmethod
    def derive_system_user(cls, market: str, commit=False):
        parent_user_id = config['AMM_DERIVE_USER_ID']
        parent = User.query.get(parent_user_id)
        user = User(
            login_password_hash=parent.login_password_hash,
            login_password_level=parent.login_password_level,
            login_password_updated_at=parent.login_password_updated_at,
            registration_ip=parent.registration_ip,
            name=f'AMM_{market}',
            user_type=User.UserType.SUB_ACCOUNT
        )
        db.session.add(user)
        db.session.flush()
        sub_account = SubAccount(
            user_id=user.id,
            main_user_id=parent_user_id,
            remark='amm system user'
        )
        db.session.add(sub_account)
        db.session.flush()
        if commit:
            db.session.commit()
        return user, sub_account


class UserLiquidity(ModelBase):

    market = db.Column(db.String(32), nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    liquidity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class LiquidityLock(ModelBase):

    class Status(Enum):
        LOCKED = 'locked'
        UNLOCKED = 'unlocked'

    market = db.Column(db.String(32), nullable=False, index=True)   # 所有市场为ALL
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False)
    locked_at = db.Column(db.MYSQL_DATETIME_6)
    unlocked_at = db.Column(db.MYSQL_DATETIME_6)
    remark = db.Column(db.String(512))

    @classmethod
    def locked(cls, market: str, user_id: int) -> bool:
        row = cls.query.filter(
            cls.user_id == user_id, 
            cls.status == cls.Status.LOCKED,
            or_(
                cls.market == market,
                cls.market == 'ALL'
            )
        ).first()
        return True if row else False


class LiquidityHistory(ModelBase):

    class Business(Enum):
        ADD = _('增加流动性')
        REMOVE = _('提取流动性')

    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'
        FAILED = 'failed'

    market = db.Column(db.String(32), nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    business = db.Column(db.Enum(Business), nullable=False)
    base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)


class AmmFeeTransferHistory(ModelBase):

    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'
        FAILED = 'failed'

    time = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)  # 按小时对齐(老数据为0点)
    market = db.Column(db.String(32), nullable=False, index=True)
    base_amount = db.Column(db.String(32), nullable=False)
    quote_amount = db.Column(db.String(32), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)


class LiquiditySlice(ModelBase):
    """资金池流动性每日快照"""
    date = db.Column(db.Date, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False, index=True)
    liquidity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False) # 注入资金池的手续费
    fee_base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 1日年化收益率
    seven_profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 7日年化收益率


class UserLiquiditySlice(ModelBase):
    """用户流动性每日快照"""
    date = db.Column(db.Date, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    liquidity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class AmmMarketHourlyStatistics(ModelBase):
    """Amm市场每小时的交易、流动性数据统计"""

    __table_args__ = (db.UniqueConstraint("market", "time", name="market_time_unique"),)

    # 存开始时间（0点~1点，存0点； 23点~次日0点，存23点）
    time = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False)
    # 当天累计数据
    fee_base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 小时增量数据，用于注入
    delta_fee_base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    delta_fee_quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 注入资金池比例，注入手续费时确定并设置该值。未注入时为空。
    fee_refund_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    # 资金池相关数据
    pool_liquidity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    pool_base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    pool_quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    @property
    def fee_refunded(self) -> bool:
        """手续费分红是否已注入资金池"""
        return self.fee_refund_rate is not None

    @property
    def delta_refund_fee_base_amount(self):
        if not self.fee_refunded:
            raise RuntimeError("amm market %s fee has not refunded" % self.market)
        # using delta_ field
        return quantize_amount(self.delta_fee_base_amount * self.fee_refund_rate, 8)

    @property
    def delta_refund_fee_quote_amount(self):
        if not self.fee_refunded:
            raise RuntimeError("amm market %s fee has not refunded" % self.market)
        return quantize_amount(self.delta_fee_quote_amount * self.fee_refund_rate, 8)


class UserLiquidityProfit(ModelBase):
    """Amm市场用户收益，小时更新，每天一条"""
    __table_args__ = (db.UniqueConstraint("date", "market", "user_id", name="date_market_user_id_unique"),)

    date = db.Column(db.Date, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    market = db.Column(db.String(32), nullable=False)
    fee_base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    last_update = db.Column(db.MYSQL_DATETIME_6, nullable=False)  # 按小时对齐


class UserLiquidityIncomeSummary(ModelBase):
    """用户全站收益记录"""
    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    market_count = db.Column(db.Integer, nullable=False, default=0)
    liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class UserLiquidityMarketIncomeDetail(ModelBase):
    """用户市场收益记录"""
    date = db.Column(db.Date, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    market = db.Column(db.String(32), nullable=False)
    # 当天收益
    daily_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 历史总收益
    total_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
