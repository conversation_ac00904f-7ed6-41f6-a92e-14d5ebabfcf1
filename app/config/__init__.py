# -*- coding: utf-8 -*-

import os
from typing import Dict, Any


config: Dict[str, Any] = {}


def _load_py_file(filename: str):
    file_path = os.path.join(os.path.dirname(__file__), filename)
    if not os.path.exists(file_path):
        return {}

    mod_name = os.path.splitext(filename)[0]
    mod = __import__(mod_name, globals(), level=1)
    return {k: v for k, v in mod.__dict__.items() if not k.startswith('_') and k.isupper()}


def _load_config(filename: str):
    c = _load_py_file(filename)
    for k, v in c.items():
        config[k] = v


def _load_env(filename: str):
    c = _load_py_file(filename)
    for k, v in c.items():
        if k.startswith('ENV_'):
            os.environ[k] = str(v)


def _patch_config():
    mysql_conf = config['MYSQL']
    username = mysql_conf['username']
    password = mysql_conf['password']
    host = mysql_conf['host']
    db_name = mysql_conf['db_name']
    charset = mysql_conf['charset']

    # noinspection SpellCheckingInspection
    config['SQLALCHEMY_DATABASE_URI'] = (
        f'mysql+pymysql://'
        f'{username}:{password}@{host}/{db_name}?'
        f'charset={charset}')

    read_only_mysql_conf = config['READ_ONLY_MYSQL']
    username = read_only_mysql_conf['username']
    password = read_only_mysql_conf['password']
    host = read_only_mysql_conf['host']
    db_name = read_only_mysql_conf['db_name']
    charset = read_only_mysql_conf['charset']

    # noinspection SpellCheckingInspection
    read_only_uri = (f'mysql+pymysql://'
        f'{username}:{password}@{host}/{db_name}?'
        f'charset={charset}')
    config['SQLALCHEMY_BINDS'] = {
        "read_only": read_only_uri
        }

_load_env('env.py')
_load_config('default.py')
_load_config('testing_release.py')
_load_config('environment.py')
_patch_config()
