# -*- coding: utf-8 -*-

#################### 环境变量定义 ####################

import os as _os

_ENV_NUM = int(_os.getenv('ENV_NUM', 1))

_SPOT_SERVERS = ['***********', '**********', '**********']
_PERPETUAL_SERVERS = ['**********', '***********', '***********']

# 合约模拟盘
_DEMO_PERPETUAL_SERVER_IP = '**********'

# 测试环境钱包的稳定服务
_WALLET_SERVICE_IP = "**********"
_WALLET_SERVICE_PORT = 80

_SERVER_MYSQL_IP = _SPOT_SERVERS[_ENV_NUM - 1]
if _ENV_NUM == 1:
    _SPOT_KAFKA_IP = 'b-2.uatall.3s4ky5.c2.kafka.ap-east-1.amazonaws.com:9092,b-1.uatall.3s4ky5.c2.kafka.ap-east-1.amazonaws.com:9092,b-3.uatall.3s4ky5.c2.kafka.ap-east-1.amazonaws.com:9092'
else:
    _SPOT_KAFKA_IP = [_SPOT_SERVERS[_ENV_NUM - 1] + ":9092"]
if _ENV_NUM == 1:
    _PERPETUAL_KAFKA_IP = 'b-2.uatall.3s4ky5.c2.kafka.ap-east-1.amazonaws.com:9092,b-1.uatall.3s4ky5.c2.kafka.ap-east-1.amazonaws.com:9092,b-3.uatall.3s4ky5.c2.kafka.ap-east-1.amazonaws.com:9092'
else:
    _PERPETUAL_KAFKA_IP = [_PERPETUAL_SERVERS[_ENV_NUM - 1] + ":9092"]
_SPOT_SERVER_IP = _SPOT_SERVERS[_ENV_NUM - 1]
_PERPETUAL_SERVER_IP = _PERPETUAL_SERVERS[_ENV_NUM - 1]

_SPOT_SERVER_PORT = 8080
_SPOT_SERVER_API_PORT = 9000
_PERPETUAL_SERVER_PORT = 8080

_COIN_COMMENT_PORTS = [5108,5109,5110]

_COIN_COMMENT_PORT = _COIN_COMMENT_PORTS[_ENV_NUM - 1]

PRODUCER_KAFKA_IP = 'b-2.uatall.3s4ky5.c2.kafka.ap-east-1.amazonaws.com:9092,b-3.uatall.3s4ky5.c2.kafka.ap-east-1.amazonaws.com:9092,b-1.uatall.3s4ky5.c2.kafka.ap-east-1.amazonaws.com:9092'

##################### 环境配置 #####################

# server kafka
KAFKA_SPOT_CONFIG = {
    "KAFKA_TOPICS": ['stops',
                     'deals',
                     ],
    "KAFKA_SERVERS": _SPOT_KAFKA_IP,
    "KAFKA_GROUP_ID": "spot_consumer",
}

KAFKA_PERPETUAL_CONFIG = {
    "KAFKA_TOPICS": ['perpetual_usermsg',
                     'perpetual_sys_orders',
                     'perpetual_sys_stops',
                     'perpetual_his_positions',
                     ],
    "KAFKA_SERVERS": _PERPETUAL_KAFKA_IP,
    "KAFKA_GROUP_ID": "perpetual_consumer",
}

# reward center producer kafka

EVENTS_KAFKA_CONFIG = {
    "KAFKA_SERVERS": PRODUCER_KAFKA_IP,
    "KAFKA_GROUP_ID": "mission_event_consumer"
}


PRODUCER_TOPIC_PREFIX = f'web-{_ENV_NUM}'


# mysql
MYSQL = {
    'host': 'vino-uat-db.cluster-cm6pyfonwyec.ap-east-1.rds.amazonaws.com',
    'username': 'uat_coinex_backend_db_user',
    'password': 'lVLG96Yvstxb0nez',
    'db_name': f'coinex_backend_{_ENV_NUM}',
    'charset': 'utf8mb4'
}

READ_ONLY_MYSQL = {
    'host': 'vino-uat-db.cluster-cm6pyfonwyec.ap-east-1.rds.amazonaws.com',
    'username': 'uat_coinex_backend_db_user_ro',
    'password': '%qP2HeAgG6gRt$=R',
    'db_name': f'coinex_backend_{_ENV_NUM}',
    'charset': 'utf8mb4'
}

# redis
REDIS = {
    "host": "uat-coinex.nqdbud.0001.ape1.cache.amazonaws.com",
    "port": 6379,
    "db": _ENV_NUM,
    "password": "",
}

CELERY_BROKER_URL = f"redis://@uat-coinex.nqdbud.0001.ape1.cache.amazonaws.com:6379/{_ENV_NUM}"

EXCHANGE_LOG_MYSQL = {
    'host': 'vino-uat-db.cluster-cm6pyfonwyec.ap-east-1.rds.amazonaws.com',
    'user': 'uat_coinex_backend_db_user',
    'passwd': 'lVLG96Yvstxb0nez',
    'db': f'exchange_log{"_2" if _ENV_NUM == 2 else ""}',
    'charset': 'utf8'
}

TRADE_LOG_MYSQL = {
    'host': _SERVER_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'trade_log',
    'charset': 'utf8'
}

TRADE_SUMMARY_MYSQL = {
    'host': _SERVER_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'trade_summary',
    'charset': 'utf8'
}

TRADE_HISTORY_0_MYSQL = {
    'host': _SERVER_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'trade_history_0',
    'charset': 'utf8'
}

TRADE_HISTORY_1_MYSQL = {
    'host': _SERVER_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'trade_history_1',
    'charset': 'utf8'
}

TRADE_HISTORY_2_MYSQL = {
    'host': _SERVER_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'trade_history_2',
    'charset': 'utf8'
}

TRADE_HISTORY_3_MYSQL = {
    'host': _SERVER_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'trade_history_3',
    'charset': 'utf8'
}

TRADE_HISTORY_4_MYSQL = {
    'host': _SERVER_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'trade_history_4',
    'charset': 'utf8'
}


if _ENV_NUM == 3:
    _PERPETUAL_MYSQL_IP = _PERPETUAL_SERVERS[_ENV_NUM - 1]
else:
    _PERPETUAL_MYSQL_IP = _SERVER_MYSQL_IP

PERPETUAL_LOG = {
    'host': _PERPETUAL_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'perpetual_log',
    'charset': 'utf8'
}

PERPETUAL_SUMMARY = {
    'host': _PERPETUAL_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'perpetual_summary',
    'charset': 'utf8'
}

PERPETUAL_SYS_HISTORY = {
    'host': _PERPETUAL_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'perpetual_sys_history',
    'charset': 'utf8'
}

PERPETUAL_HISTORY_DB_CONFIG0 = {
    'host': _PERPETUAL_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'perpetual_user_history_0',
    'charset': 'utf8'
}

PERPETUAL_HISTORY_DB_CONFIG1 = {
    'host': _PERPETUAL_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'perpetual_user_history_1',
    'charset': 'utf8'
}

PERPETUAL_HISTORY_DB_CONFIG2 = {
    'host': _PERPETUAL_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'perpetual_user_history_2',
    'charset': 'utf8'
}

PERPETUAL_HISTORY_DB_CONFIG3 = {
    'host': _PERPETUAL_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'perpetual_user_history_3',
    'charset': 'utf8'
}

PERPETUAL_HISTORY_DB_CONFIG4 = {
    'host': _PERPETUAL_MYSQL_IP,
    'port': 3306,
    'user': 'root',
    'passwd': 'shit',
    'db': 'perpetual_user_history_4',
    'charset': 'utf8'
}

CLIENT_CONFIGS = {
    "server": {
        "url": f"http://{_SPOT_SERVER_IP}:{_SPOT_SERVER_PORT}",
    },
    "perpetual_server": {
        "url": f"http://{_PERPETUAL_SERVER_IP}:{_PERPETUAL_SERVER_PORT}",
    },
    "monitor_master_server": {
        "host": '127.0.0.1',
        "port": 8888,
    },
    "biz_monitor_server": {
        "host": "**********",
        "port": 8899,
    },
    "ant_fraud_server": {
        "host": "**********",
        "port": 8878,
        "url": "http://**********:8877",
    },
    "viabtc_pool": {
        "url": "http://test1.pool-web.viadeploy.com",
        "user_id": 429,
        "api_key": "d479c5e0b402a1d3efaa697b31e24219",
        "secret_key": "cc6bd5b7d962edf5c670cf90ada8fe4520907e936beaf22b97acac36e2eeeb98",
    },
    "amm": {
        "url": "http://**********:8007",
    },
    "wallet": {
        "url": f"http://{_WALLET_SERVICE_IP}:{_WALLET_SERVICE_PORT}",
        "salt": "pbhTeG73fJoAzOOMUJlMUD2j"
    },
    "wallet_manager": {
        0: {
            "url": "http://**********:24604",
            "salt": "Ae5NyU@~n9YYR#$eIlPY+QVg",
        },
        1: {
            "url": "http://**********:24604",
            "salt": "Ae5NyU@~n9YYR#$eIlPY+QVg",
        },
        2: {
            "url": "http://**********:24604",
            "salt": "Ae5NyU@~n9YYR#$eIlPY+QVg",
        },
    },
    "wallet_manager_tee": {
        0: {
            "url": "http://**********:24604",
            "salt": "Ae5NyU@~n9YYR#$eIlPY+QVg",
        },
        1: {
            "url": "http://**********:24604",
            "salt": "Ae5NyU@~n9YYR#$eIlPY+QVg",
        },
        2: {
            "url": "http://**********:24604",
            "salt": "Ae5NyU@~n9YYR#$eIlPY+QVg",
        },
    },
    "api_gateway": {
        "url": "http://**********:8866"
    },
    "biz_monitor": {
        "url": "http://**********:8869"
    },
    "SPOT_WEBSOCKET": {
        "url": f"ws://exchangews{_ENV_NUM}-test.coinex.com",
    },
    "PERPETUAL_WEBSOCKET": {
        "url": f"ws://perpetualws{_ENV_NUM}-test.coinex.com",
    },
    'im_server': {
        "url": "http://**********:8088"
    },
    'information_internal': {
        'url': 'http://**********:8004/internal/exchange/',
    },
    "demo_trading": {
        "frontend_url": "http://**********:8005/res/",
        "api_url": "http://**********:8005/v2/",
        "internal_url": "http://**********:8005/internal/",
        'url': 'http://**********:8004',
    },
    'comment_internal': {
        'url': f'http://127.0.0.1:{_COIN_COMMENT_PORT}',
    },
    "coinex_wallet": {
        'url': "http://test.wallet.coinex.viadeploy.com",
        # 测试环境需要以下2个 header
        'CF-Access-Client-Secret': '9146ccd8792636a0ae84f33bf8160a9148136c55c60c52b0013539ffa368890c',
        'CF-Access-Client-Id': '986b4b5aad5f40871a1155b67c3255db.access',
    }
}


ONE_API_CLAUDE = {
    'TOKEN': '',
    'BASE_URL': 'http://10.30.0.204/v1'
}

ONE_API_GPT = {
    'TOKEN': '',
    'BASE_URL': 'http://10.30.0.204/v1'
}

OPENAI = {
    'TOKEN': '',
    'PROXY': ''
}

#################### 业务配置 ####################

# admin
SUPER_ADMIN_USER = []

# 初审用户id
AUDIT_USERS = ()

# 资产变更初审用户id
ASSET_UPDATE_AUDIT_USERS = ()

# 资产变更复审用户id
ASSET_UPDATE_CHECK_USERS = ()

# 小额资产恢复审核用户id
CLEANED_ASSET_RESUME_AUDIT_USERS = ()

# 薪资发放初审用户id
SALARY_PAY_AUDIT_USERS = ()

# 薪资发放复审用户id
SALARY_PAY_CHECK_USERS = ()

# 提成发放初审用户id
COMMISSION_PAY_AUDIT_USERS = ()

# 提成发放复审用户id
COMMISSION_PAY_CHECK_USERS = ()

# 充提报表忽略内部账号邮箱
DEPOSIT_WITHDRAWAL_REPORT_IGNORE_USERS = ()

# IEO白名单
IEO_WHITE_LIST = []

# 小币兑换
SMALL_COIN_EXCHANGE_USER_ID = 990
# CET回购
CET_BUYBACK_USER_ID = 991
# admin账号
OFFICIAL_ADMIN_USER_ID = 992
# admin市场账号
MARKET_ADMIN_USER_ID = 904
# AMM派生账号
AMM_DERIVE_USER_ID = 425
# 盘前交易(预测市场)派生账号
PRE_TRADING_USER_ID = 992
# 合约保险基金账户
PERPETUAL_INSURANCE_USER_ID = 0
# 财务账户
FINANCE_USER_ID = 20
# 卡券账户
COUPON_USER_ID = 993
# 卡券执行账号
COUPON_BUSINESS_USER_ID = 994
# 保险基金兑换账号
INSURANCE_EXCHANGE_USER_ID = 1555
# 空投账号
AIRDROP_ADMIN_USER_ID = 993
# 充值福利账号
DEPOSIT_BONUS_ADMIN_USER_ID = 993
# launch_pool挖矿账号
LAUNCH_POOL_ADMIN_USER_ID = 1650
# Dibs账号
DISCOUNT_ADMIN_USER_ID = 992
# 权益中心系统结算账号
EQUITY_CENTER_ADMIN_USER_ID = 1650
# 自营交易账户
PROP_TRADING_USER_ID = 1106
# 自营投资账户
INVESTMENT_USER_ID = 798
# 币币类型交易赛账号
SPOT_TRADE_RANK_ADMIN_USER_ID = 993
# 合约类型交易赛
PERPETUAL_TRADE_RANK_ADMIN_USER_ID = 992
# 大使活动发奖账户
AMBASSADOR_ACTIVITY_ADMIN_USER_ID = 992
# P2P商家活动账号
P2P_MER_ACT_USER_ID = 1679
# KYC审核角色
KYC_AUDITOR_ROLES = [19]
# P2P保证金账号
P2P_MARGIN_USER_ID = 10001137   # 线上为 7606072
# 大使激励包结算账号
AMBASSADOR_PACKAGE_USER_ID = 1687    # 线上为 7878132

ASSET_REPORT_IGNORE_USERS = []

CAN_REVIEW_MISSION_PLAN_USERS = [1260, 1180, 1679, 63, 1318]

# slack api
SLACK_MSG_URL = 'https://slack.com/api/chat.postMessage'

# slack token
SLACK_TOKEN = "*********************************************************"

# 告警通知
ADMIN_CONTACTS = {
    "customer_service": "",  # 客服风控告警
    "index_price_alert": "",  # 指数价格保护告警
    "web_notice": "",  # web异常告警
    "asset_liability": "",  # 资产负债告警
    "cet_notice": "",  # CET价格异常告警
    "server_notice": "",  # server-告警
    "ambassador_notice": "",  # 大使周报
    "server_notice_mobile": [],
    "risk_control_mobile": [],
    "withdrawal_disabled_mobile": [],
    "withdrawal_cancel_mobile": [],  # 已签名但提现取消电话通知相关人员
    "site_deposit_fuse_mobile": [],  # 全站充值熔断风控电话通知相关人员
    "audit_withdrawals_blocked_mobile": [],  # 待审核阻塞电话通知相关人员
    "asset_liability_all_mobile": [],  # 全站资产负债不平电话通知相关人员
    "slack_at": {
        "accumulated_deposit_notice": "",
        "p2p_notice": "U03U4H42HLK",
        "asset_liability_asset_notices": [],  # 币种维度资产负债告警@相关人员
        "asset_liability_all_notices": [],  # 全站维度资产负债告警@相关人员
        "audit_withdrawals_blocked_notices": [],  # 待审核阻塞告警@相关人员
        "p2p_mer_act": "",   # p2p商家活动告警
        "asset_amount_notice": "U03U4H42HLK",
        "p2p_mer_adv_export": "",
        "fiat_price": "U03U4H42HLK",  # 法币汇率更新告警
        "kyt_notice": "",
    },
    "credit_notice": "",  # 授信用户风险告警
    'fiat_notice': "",  # 服务商法币更新提醒
    "ambassador_pre_notice": "",  # 大使晋降级预提醒
    "big_booking_deposit_notice": "",  # 大额挂单充值告警
    "reserve_ratio_notice": "",  # 准备金率告警
    "asset_notice": "",  # 币种异动站内监控
    "market_notice": "",  # 合约/杠杆市场数据监控
    "deposit_disabled_mobile": [],  # 币种累计充值监控
    "discount_activity_order_notice": "",  # Dibs活动ADMIN账号购币通知
    "p2p_notice": "C07102M177X",  # p2p异常告警
    "copy_trading": "",  # 合约跟单交易告警
    "sp_investment_notice": "C06T2S2AKGE",  # 投资账号持仓数据监控
    "asset_deposit_withdrawal_switch": "C06T2S2AKGE",  # 币种充提开关告警
    "coupon_notice": "C07TYBDT78Q",  # 卡券告警
    "p2p_mer_act": "",
    "asset_amount_notice": "C0892B62WJX",  # 币种数量告警
    "p2p_mer_adv_export": "",  # p2p商家广告数据导出
    "asset_abnormal_issuance": "C04NUSSPGR5",  # 币种异常增发告警
    "kyt_notice": "",  # KYT 告警
}

GRAYLOG_LOGGER_HOST = "127.0.0.1"
GRAYLOG_LOGGER_PORT = 8888

SITE_URL = 'https://coinex9.test.viadeploy.com'

BYPASS_2FA = True

UPLOAD_STATIC_URL = "https://coinexstatic.viadeploy.com"

UPLOAD_CDN_URL = "https://resource.test.viadeploy.com/res/upload"

SYSTEM_ADMIN_EMAIL_SIGNATURE = "CoinEx-Test"

EMAIL_PREFIX = "Test"

PUSH_PREFIX = "【Test】"

BINDING_ADDRESS_CET_ASSET_URL = "https://testnet.coinex.net/res/addresses/{address}"

# disable data backup
BACKUP_ENABLED = False

MONITOR_ENABLED = False

#################### 第三方服务配置 ####################


ALIYUN_VOICE = {
    "access_key_id": "",  # no test account
    "access_key_secret": "",
    "show_number": ""
}

ALIYUN_SMS = {
    "access_key_id": "",  # no test account
    "access_key_secret": "",
}

# 没有初始值会报错
TENCENT_SMS = {
    "secret_id": "test",  # no test account
    "secret_key": "test",
    "app_id": "test",
}

AWS_EMAIL = {
    'access_key_id': '********************',
    'secret_access_key': 'jKzDuL+0Gj0enJ1gmMgLNMdnas71lrvSQR1I822i',
    'region_name': 'us-west-2',
    'from_addr': '*******'
}

AWS_COMMENT_EMAIL = {
    'access_key_id': '********************',
    'secret_access_key': 'oug7TrNEa/wnyUgNhPrRY/ejRsDCxW/CP3HbPgec',
    'region_name': 'us-west-2',
    'from_addr': '*******'
}

MAILGUN_EMAIL = {
    'api_key': '**************************************************',
    'domain': 'mail.coinex.com',
    'from_addr': '*******'
}

AWS_FILE = {
    "access_key_id": "********************",
    "secret_access_key": "ufqQxfdyYzAiTa0hBkIuh2ZduBNPc+HxJUMYaE3n",
    "region_name": "ap-northeast-1",
    "bucket_name": "coinex-web-test",
    "backup_bucket": "coinex-web-test",
    "private_path": "private",
    "tmp_path": "tmp",
}

AZURE_FILE = {
    "account_name": "test2azurebackup",
    "container_name": "test",
    "sas_token": "si=rule-test&spr=https&sv=2022-11-02&sr=c&sig=xMMghFpq3HcVjrZe%2Fmxj0EQg0eWeWBArzhj%2B5u%2BFlO8%3D"
}

YUN_PIAN_API_KEY = 'bf047e472e968533341071df6139b0e2'
NEXMO_API_KEY = "********"
NEXMO_API_SECRET = "7631c71501264406"
NEXMO_FROM_TEXT = "[CoinEx]"
PARS_GREEN_API_KEY = "75D0B03E-FE04-4151-9466-92EA564D9A44"
PARS_GREEN_SEND_NUMBER = "**************"
XINDACLOUD_USER = ""  # no test account
XINDACLOUD_PASSWORD = ""
TELESIGN_API_KEY = ""
TELESIGN_CUSTOM_ID = ""

GEETEST3 = {
    "default": {
        "id": "35f192a0cf579ec0e5288976e5ae9327",
        "key": "7e60cd999639db21f75ea9dc3f7554db",
    },
    "zh_Hans_CN": {
        "id": "8b1969931b69209e7d17be24ebac36f0",
        "key": "017355275ff7b5ab7e1314079e85c808",
    },
    "zh_Hant_HK": {
        "id": "d7e26e587c2d3e9ae4e88c6f5706b067",
        "key": "b43d941ec13a5a9785ab3fe83ac4a886",
    },
}

GEETEST4 = {
    # captcha_id: captcha_key
    '5ea42101b7c8744d10751a5d91699d73': '678575a2d068548ddb0799709d30b4e9',  # easy
    '3c425320ba6a5fb88110eafb1606d1bc': 'd5e47622cc35fdf0b524f256b066b92e',  # hard
}

JUHE = {
    'exchange_rate': '2008b4577b2dae0f1944141b23f8d61f',
    'ip_check': 'e26e5011c79a17747a549431a7197dfb'
}

POLYGON = {
    'api_key': '********************************',
    'key_id': '00edf82c-c71e-47fa-9c8a-b16f631b7971'
}

OPEN_EXCHANGE = "dfc4c7f214e7485a80e1fff1c518623a"

FIXER = {
    'api_key': '********************************',
}

ZENDESK_CONFIG = {}
INNER_MOBILE_PUSH_CONFIG = {
    "url": "http://**********:8087",
    "app_key": "c9b1575fc13e440bbf403e2f01ea5ea7",
    "api_version": "v1",
}

# fiat
SIMPLEX_CONFIG = {
    'url': 'https://sandbox.test-simplexcc.com',
    'sell_url': 'https://api.sandbox.test-simplexcc.com',
    'payment_url': 'https://sandbox.test-simplexcc.com/payments/new',
    'apikey': "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************.-ZZm61Vy-xhQeueJ5nRXLWmscSPD7mbbUdtJZmuDQuI",
    'sell_apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXJ0bmVyTmFtZSI6ImNvaW5leCIsImlhdCI6MTYzMDQwNzk2MH0.5JqDiHtf0xO9d-v_9sFKFjkJTmAgXw1Jj5a5N6CN2iI',
    'public_key': 'pk_8f7b02d4-9898-4e34-a435-2fb2c0ed4353'
}

MERCURYO_CONFIG = {
    'url': 'https://sandbox-api.mrcr.io',
    'payment_url': 'https://sandbox-exchange.mrcr.io',
    'widget_id': '60b69ef8-9287-49d7-8164-94d87d8982c4',
    'partner_token': '20:0733b0811e38e1814eWcFVMeP_ldI6rlgEcXcrU8X8uV0QujjnoVRwBvCZHtzRnn',
    'secret': ''
}

MOONPAY_CONFIG = {
    'url': 'https://api.moonpay.io',
    'payment_url': {
        'buy': 'https://buy-staging.moonpay.io',
        'sell': 'https://sell-sandbox.moonpay.com',
    },
    'apikey': 'pk_test_d0ewUySX1vFc8EdPuElJGr1GVBJbFK',
    'secret': 'sk_test_nbX76a09TNr9PaTncFBPUVPx3EN8b'
}

PAXFUL_CONFIG = {
    'url': "https://paxful.com/api",
    "payment_url": "https://paxful.com/roots/buy-bitcoin",
    "apikey": "TgSl233E2dIJ3Pm4zQqSXpr3DnKTsqMh",
    "secret": "sEZbztWOoqZmncHnnHV4GLZYjCntUq0x",
    "kiosk": "oNDwvjLowJQ"
}

VOLET_CONFIG = {
    'url': "https://wallet.advcash.com/wsm",
    "payment_url": "https://wallet.advcash.com/sci/",
    "apiName": "coinex",
    "accountEmail": "*******",
    "apiPassword": "QOe2.26ZUr",
    "sciName": "coinex",
    "sciPassword": ".98Ne6oz0w"
}

XANPOOL_CONFIG = {
    'url': "https://sandbox.xanpool.com/api",
    "payment_url": "https://checkout.sandbox.xanpool.com",
    'apiKey': '********************************',
    'apiSecret': '96b05714fc7b72ffa9c5c837a669e1f5b079fca1fb15846229317e1e723df690',
    "redirectUrl": ""
}

BANXA_CONFIG = {
    'url': 'https://coinex.banxa-sandbox.com',
    'key': 'coinex@2021test',
    'secret': b'********************************'
}

GUARDARIAN_CONFIG = {
    'url': 'https://api-payments.guardarian.com',
    'x-api-key': 'e8601dd0-8545-4443-8277-ac5a4379f65a',
}

REMITANO_CONFIG = {
    'url': 'https://remistag2.info/api/v1',
    'access_key': 'd537f278345ddbcdf8f58028ec12d811a1a2e3a97fc5418bf10b45e6da765a5e',
    'secret_key': 'FjiK9J0skzwQ/b+BhboWg9Ruh9IxsV4pMCx1Tn5/4uY37bdd2BNxfaZS+NxIR8TIYqo4Fc0aFdDIgSkCp//iig==',
}

LOOPIPAY_CONFIG = {
    'url': 'https://api.loopipay.com',
    'secret': 'AIzaSyAchDfwVXch3NrCsLd0lwu73RlAURLKW7M',
    'client_id': 'coinex',
}

# sandbox
ONRAMP_CONFIG = {
    'url': 'https://api.onramp.money/onramp/api',
    'payment_url': {
        'buy': 'https://onramp.money/app',
        'sell': 'https://onramp.money/main/sell',
    },
    'app_id': '326537',
    'api_key': '******************************',
    'api_secret': '********************************'
}

BTCDIRECT_CONFIG = {
    'url': 'https://api-sandbox.btcdirect.eu',
    'client': 'coinex',
    'password': '2423dacaa418a4a46627bc8768d912262e2f403f',
    'api_key': 'b6d601f95c049bba334e1fb65642543f241b1335813b7e1b14a95ca313a07c82'
}

ONMETA_CONFIG = {
    'url': 'https://api.platform.onmeta.in',
    'widget_url': 'https://platform.onmeta.in',
    'client_id': '56565940-0c16-4c20-bc01-8ee72bf42331',
    'client_secret': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRJZCI6IjY1NTFjMDliODZmYTYxZTA2M2U1NWJiNyIsImFwaUtleSI6ImYyNTA1YzcyLWY2MjctNDE5Mi05MTY4LTBhMDFhZjk2NDg4NyIsInVzZXJJZCI6IiIsImV4cCI6MTczMTQ3ODkzOSwiaWF0IjoxNjk5ODU2NTM5fQ.UxcT1x4Fb0yFXwbAlrHD6XAoFUqwBs-7m591dejK1og',
    'environment': 'staging'
}

ALCHEMYPAY_CONFIG = {
    'url': 'https://openapi-test.alchemypay.org/open/api',
    "payment_url": "https://ramptest.alchemypay.org",
    'app_id': 'f83Is2y7L425rxl8',
    'secret_key': '5Zp9SmtLWQ4Fh2a1',
}

TELEGRAM_BOT = {
    'name': 'TypeZeroBot',
    'token': '1221717609:AAH2QLGko6rFiFz-jGVGljIgPqPQ_Hah-Pk'
}

REFINITIV_WC1_CONFIG = {
    "url": "https://api-worldcheck.refinitiv.com",
    "api_key": "76e86c36-30d0-4003-b1ed-cbf46ddbea58",
    "api_secret": "sQVNxCyan12nv7Y5QDv/LcKOoXhuBrRpgk+0gSCtnY5edC6+W88ru7nuZA/wCPtatNiYuFFj4xlfCbDOfpt5Kg==",
}

SUMSUB_CONFIG = {
    "url": "https://api.sumsub.com",
    "secret_key": "O56EduRfT1nEEfADTFRMWXIHGlKUdBfM",
    "app_token": "sbx:E9Z3lqcHXH3C1ZfhfPvUXa1f.qXGezGk5f4nzkr8rvRUytkxlXfqhXsYq",
    "level_name": "testing-level",
    "poa_level_name": "Test-AddressVerification",
    "non_doc_level_name": "non-doc-level",
    "non_doc_source_key": "test-non-doc-level",
    "liveness_level_name": "liveness-check",
    "liveness_action_level_name": "liveness-check-action",
    "liveness_source_key": "liveness-check-level",
    "webhook_secret_key": "LaRCOr4VYWqYoGgGabZqmEz-adn",
}

DOWJONES_CONFIG = {
    "auth_url": 'https://eu.accounts.dowjones.com',
    "url": "https://eu.api.dowjones.com",
    "username": "*******",
    "password": "XE9zGLJXu2vUb5Yj",
    "client_id": "TBdWKgSa0W2nFzSJLYZL5iWUtBEKJ5x2I6GmxWtt",
}

QUOTES_CLIENT_CONFIG = {
    "cmc": {
        "url": "https://pro-api.coinmarketcap.com",
        "api_key": "9b4bc9cc-6b19-44ab-80f4-8b3f0b0b7cd8",
    },
    "coingecko": {
        "url": "https://api.coingecko.com/",
    }
}

TELEGRAM_GROUP_URL = 'https://t.me/joinchat/ZEY6vWTuyqxmNjE1'
EVENT_MONITOR_SIGN_SECRET = 'oDQWgTCDUs3/lHmvxelHEbW12J85PIsAjM8p8Qkia7k='
EVENT_TRACE_SIGN_SECRET = 'eHgxODMydTkxc3Nqc3Nzc3NzaioyMTMzMzMzMw='
QRCODE_URL_SIGN_SECRET = 'Sija$CazQd8Ks1MKusP@r^g8VbR8y6QX'
WALLACY_SIGN_SECRET = """
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

REDSHIFT_DB = {
    'host': 'vpce-05c79c9ea20df2fb8-scc7ivdk-ap-east-1a.vpce-svc-0c0a402c54abdee0e.ap-east-1.vpce.amazonaws.com',
    'port': 5439,
    'user': 'awsuser',
    'password': 'Shit0000',
    'database': f'trade_history{"_2" if _ENV_NUM == 2 else ""}',
    'client_protocol_version': 1
}

PERPETUAL_REDSHIFT_DB = {
    'host': 'vpce-05c79c9ea20df2fb8-scc7ivdk-ap-east-1a.vpce-svc-0c0a402c54abdee0e.ap-east-1.vpce.amazonaws.com',
    'port': 5439,
    'user': 'awsuser',
    'password': 'Shit0000',
    'database': f'perpetual_history{"_2" if _ENV_NUM == 2 else ""}',
    'client_protocol_version': 1
}

GOOGLE_OAUTH_CONFIG = {
    'client_config': {
        "web": **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    },
    'scopes': ["https://www.googleapis.com/auth/userinfo.profile",
               "https://www.googleapis.com/auth/userinfo.email",
               "openid"],
    'redirect_uri': f'{SITE_URL}/assets/google.html',
    # 'auth_uri': "https://accounts.google.com/o/oauth2/auth"
}

APPLE_OAUTH_CONFIG = {
    'client_id': 'com.viabtcdev.coinex-web-sign-in-with-apple',
    'ios_client_id': 'com.viabtcdev.coinex',
    'iss': 'Y9XQZ45U6M',
    'private_key': {
        'kid': 'X4J22V642B',
        'alg': 'ES256',
        'key': \
"""
******************************************************************************************************************************************************************************************************************************************************************"""
    },
    'base_url': 'https://appleid.apple.com',
}

GOOGLE_ANALYTIC_OAUTH_CONFIG = {
    'client_id': '',
    'coinex_view_id': '',
    'client_secret': '',
    'auth_uri': 'https://accounts.google.com/o/oauth2/auth',
    'token_uri': 'https://oauth2.googleapis.com/token',
    'api_uri': 'https://analyticsdata.googleapis.com/v1beta/properties/{view_id}:runReport',
    'redirect_uri': 'https://www.coinex.com',
    'scopes': ['https://www.googleapis.com/auth/analytics.readonly']}

GOOGLE_BIGQUERY_CONFIG = {
    "credentials": \
    {
        "type": "service_account",
        "project_id": "coinex-c6529",
        "private_key_id": "cde488519e0428ad361b70f6aa07cb7b2ee56fea",
        "private_key": "",
        "client_email": "*******",
        "client_id": "110487830193683174012",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-8z7n1%40coinex-c6529.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
        },
    "fcm_table": "coinex-c6529.firebase_messaging.data"
}

APPLE_ADS_URL = "https://api-adservices.apple.com/api/v1/"

DEPOSIT_SIGN_PUB_KEY = {
    0: b'-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqgl94caxnI8m0MSpbdgc\ntoysDZHxgdJ07wIe/wbR0/BpFdK6H1LiH2RJNc3IaOPLdPMb9je5NFCgm5X/I6cM\nLvZGKlYkYf9UooYWldmsNbZWComb0q6nu1EBlRg2Wt6reSnHI3fGZAmLnDN4X7HE\nby0OJBx1L62nu7qj3o+MNj0lgnM8318h8uKnva0QB93I9SLk/3PR1IUJuepOco0T\nABNkSH1m8vNvbeU96nOXnERi6EuzL+2omagyK738mj629AolrNd5pKPSaLqfEVuG\nHjfbYY7/GG+gYvAJIIU8lejTRcxkxSeBkfp7R4/929N1CsT4v/gA0bfCuausxAKe\n5wIDAQAB\n-----END PUBLIC KEY-----',
    1: b'-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqgl94caxnI8m0MSpbdgc\ntoysDZHxgdJ07wIe/wbR0/BpFdK6H1LiH2RJNc3IaOPLdPMb9je5NFCgm5X/I6cM\nLvZGKlYkYf9UooYWldmsNbZWComb0q6nu1EBlRg2Wt6reSnHI3fGZAmLnDN4X7HE\nby0OJBx1L62nu7qj3o+MNj0lgnM8318h8uKnva0QB93I9SLk/3PR1IUJuepOco0T\nABNkSH1m8vNvbeU96nOXnERi6EuzL+2omagyK738mj629AolrNd5pKPSaLqfEVuG\nHjfbYY7/GG+gYvAJIIU8lejTRcxkxSeBkfp7R4/929N1CsT4v/gA0bfCuausxAKe\n5wIDAQAB\n-----END PUBLIC KEY-----',
    2: b'-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqgl94caxnI8m0MSpbdgc\ntoysDZHxgdJ07wIe/wbR0/BpFdK6H1LiH2RJNc3IaOPLdPMb9je5NFCgm5X/I6cM\nLvZGKlYkYf9UooYWldmsNbZWComb0q6nu1EBlRg2Wt6reSnHI3fGZAmLnDN4X7HE\nby0OJBx1L62nu7qj3o+MNj0lgnM8318h8uKnva0QB93I9SLk/3PR1IUJuepOco0T\nABNkSH1m8vNvbeU96nOXnERi6EuzL+2omagyK738mj629AolrNd5pKPSaLqfEVuG\nHjfbYY7/GG+gYvAJIIU8lejTRcxkxSeBkfp7R4/929N1CsT4v/gA0bfCuausxAKe\n5wIDAQAB\n-----END PUBLIC KEY-----',
    3: b'-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqgl94caxnI8m0MSpbdgc\ntoysDZHxgdJ07wIe/wbR0/BpFdK6H1LiH2RJNc3IaOPLdPMb9je5NFCgm5X/I6cM\nLvZGKlYkYf9UooYWldmsNbZWComb0q6nu1EBlRg2Wt6reSnHI3fGZAmLnDN4X7HE\nby0OJBx1L62nu7qj3o+MNj0lgnM8318h8uKnva0QB93I9SLk/3PR1IUJuepOco0T\nABNkSH1m8vNvbeU96nOXnERi6EuzL+2omagyK738mj629AolrNd5pKPSaLqfEVuG\nHjfbYY7/GG+gYvAJIIU8lejTRcxkxSeBkfp7R4/929N1CsT4v/gA0bfCuausxAKe\n5wIDAQAB\n-----END PUBLIC KEY-----',
}

WALLET_WHITELIST_ADDRESS = {}

WALLET_GAS_ADDRESS = {}

WALLET_TEMP_ADDRESS = {}

GOOGLE_TRANSLATION_CONFIG = {
    'api_key': 'AIzaSyBDW4OVxGjFkyJY7U5NXblmRHPJPxPkqKY',
}

INFORMATION_PUSH = {
    'group_id': 263,
    'news_jump_id': 0,
    'article_jump_id': 0,
    'insight_jump_id': 0,
}

ACTIVITY_PUSH = {
    'trade_rank_jump_id': 225,
    'airdrop_jump_id': 226,
    'discount_jump_id': 227,
    'launch_pool_jump_id': 227,
}

APPSFLYER_CONFIG = {
    "android": {
        "name": "com.coinex.trade.dev",
        "dev_key": "5JiHu4cmMsXgKAMPaNGz3D"
    },
    "ios": {
        "name": "id1111786863",
        "dev_key": "qcX7KZcHGpqGcas9Mi4bW3"
    },
    "url": "https://api2.appsflyer.com",
    "pull_url": "https://hq1.appsflyer.com/api/raw-data/export/app",
    "api_token": "eyJhbGciOiJBMjU2S1ciLCJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwidHlwIjoiSldUIiwiemlwIjoiREVGIn0"
                 ".cFMu9kPywwNmaZrJE7Tzivi2SU0CuXRsE5aXGFSVcGpDSKbO39xxsw.T-WNiwS8B_xcihvr.gfVBl-jG2kmziZL70B"
                 "9yXi6Xird2-EbItgbxVBhNKyrV0DDmWtjSRa7yeL-_zYRwCAl75VTLO3J1uh2LcVUSKONHvEbwxxaBMzqrWKz89A_b3"
                 "Qb-AmRyu4cyWhcTBX6bWeOpDBengSclRX43X0lFELdAdbGUPHxwDmSmRLcP4obqSd3oLhmQUBmpCgWMgngFeh3BDS6B"
                 "tnWYrSLb5-aUDHZPfXuKSQA6kpqPvgDqe-cDlZJylu8_wEVPX4zCZvLriYjnivLzF2bAwhg8BILCef9rCi8ERoboL_y"
                 "xbe3ILRuS_aET30773iY68aoIjkOlyCa5L683zWoB95Tf3kCQJD7qcpQG9NFletohLe4W368jUYMbLhFyjMehHvUyPq"
                 "dU1JZkm_nyfBepjP24StWqWv-iACUfHeCXBqvv3uWFOmCRpwZiVScIze7NXPaJjqBGeNhOCxaSaewXDeO3DXZ817hLN"
                 "J9L9GutUgyor89X1gzh2glpKkeCuy7JUteZckWhU__k69NB-4OInI57hJrSwQz_W8xj.IruP4aAafZuNtGmDzTXQZQ"
}

ANDROID_WEBAUTHN_CONFIG = {
    "dev_key": "aMukTU2TbEifuVJI_gDDudw_F742G80Njn0d-supJs8",
    "prod_key": "wRYaU19BU7cTWPcRR8SvzaBgX43fN7Hp1ZrVI5VhufA",
}

PYROSCOPE_CONFIG = {
    "server_address": "",
}

AWS_KMS = {
    "region_name": "ap-east-1",
    "aws_access_key_id": "********************",
    "aws_secret_access_key": "ZlgeM96qk7ZrHFukTcT80dXBa+O+Lq9HeLGpvZK0",
    "timeout": 5,
    "key_id": "arn:aws:kms:ap-east-1:204070612040:alias/kms-secure-test",
}

_TRANSLATE_SERVERS = ['***********', '***********', '***********']

_TRANSLATE_SERVER_IP = _TRANSLATE_SERVERS[_ENV_NUM-1]

AI_TRANSLATION_CONFIG = {
    'url': f'http://{_TRANSLATE_SERVER_IP}:12311/v1/translate',
}

AI_ADMIN_CONFIG = {
    'url': f'http://{_TRANSLATE_SERVER_IP}:12311/admin/ai-analytics',
}

AI_KLINE_SERVICE_CONFIG = {
    'url': f'http://{_TRANSLATE_SERVER_IP}:12311/v1/kline',
}


WHATSAPP_MESSAGE = {
    "api_key": "8679f49c7c21b6c84ae952e73ff43ae7",
    "from_number": "+85296783326",
    "template": "test",
    "webhook_secret": "whsec_3e6ebfc10f434d77808c9d2d23550cf5",
}

TELEGRAM_GATEWAY_API = {
    "api_key": "AAHIBwAADEIoyughbNsg7KlUpMKU2mdrXFLXTXesSIHyng",
    "sender": "CoinEx_Announcement",
    "sender_phone": "",
    "callback_url": "https://callback-testing-only.org/callback/telegram",
}

ZENDESK_BOT_CONFIG = {
    "shared_secret": "DbjjDdEP2I-SuFcbfIWFHmutExxfZC2XlS6gWgeaLmNz6yLS4R-Cob3xdLqWSxULa_Fsl7V6igbbNgNDCascog",
    "secret_id": "app_675a92010ff9fdee2d51e479",
    "token": "554d9523c66f70d3d94fba0707a4d7e3",
    "id_prefix": "test"
}

KYT_CONFIG = {
    "chainalysis": {
        "token": "31fbb9e1b0915db01960452ad1b86ac47a39b1b47383f52ab7cac56dbe944931"
    },
    "coinfirm": {
        "token": "7QYQ1w7cahk3BQ7I5t3wrRSoCYM7c9dyTheZqo7EH6mG8qV2aCusllPL9pzV9Hu4"
    }
}

SIGN_EMAIL_TEMPLATE_ID = 374

TEE_SIGN_CHAINS = []

INSIGHT_SHORT_LINK = ''

TRUSTPILOT_BCC_ADDRESS = ''  # 国外评分网站Trustpilot, 给用户推送邮件时需要密送该地址, 合作截止2026-3-31
INTERACTION_MESSAGE_URL = "https://coinex6.test.viadeploy.com/interactive-message"

