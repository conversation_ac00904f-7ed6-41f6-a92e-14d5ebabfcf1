<style>
    .btn-link {
        color: #0ead98;
        text-decoration: none;
    }
</style>
{% extends "email/index.j2" %}
{% block title %}
<div>
  <span style="font-size: 24px; vertical-align: middle;">{{ _("%(project_name)s 申购已中签", project_name=project_name) }}</span>
</div>{% endblock %}
{% block body %}

<div style="margin-top: 10px">
  <div>{{ _("恭喜您在CoinEx Dock成功登船！") }}</div>
  <div>{{ _("项目名称：%(project_name)s", project_name=project_name) }}</div>
  <div>{{ _("中签份数：%(count)s", count=count) }}</div>
  <div>{{ _("中签数量：%(amount)s %(asset)s", amount=amount, asset=asset) }}</div>
  {% if unlocked_at %}
    <div>{{ _("中签资产已发放至你的现货账户，并于%(unlocked_at)s 解冻。", unlocked_at=unlocked_at) }}</div>
  {% else %}
    <div>{{ _("中签资产已发放至你的现货账户。") }}</div>
  {% endif %}
</div>
{% if rule_url %}
<div style="margin-top: 20px">{{ _("注：该项目代币有特殊释放机制 <a class='btn-link' href='%(rule_url)s'>查看详情</a>", rule_url=rule_url)|safe }}</div>
{% endif %}
<div style="margin-top: 30px; color: #7d7f81">
  <div>{{ _("*提示") }}</div>
  <p>{{ _("质押的%(pledge_asset)s已解冻并退回您的现货账户", pledge_asset=pledge_asset) }}</p>
  <div>{{ _("未中签的申购资金已退回您的现货账户") }}</div>
</div>

<div style="margin-top: 30px">
  <a href="{{ order_url }}" style="display: inline-block; padding: 14px 34px; border: 1px solid #0ead98; color: #0ead98; border-radius: 25px; text-decoration: none">{{ _("查看Dock订单") }}</a>
</div>
{% endblock %}

