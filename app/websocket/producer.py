import time
import json
import traceback
from gevent.lock import RLock
from app import config

from flask import current_app
from websockets.sync.client import connect, ClientConnection

from app.websocket.consumer import SpotStateUpdateMsgConsumer, PerpetualStateUpdateMsgConsumer

lock = RLock()


class WebSocketMsgProducer:
    PING_EVENT_ID = 1
    STATE_SUBSCRIBE_ID = 2

    def __init__(self, url: str):
        self.URL = url
        self.consumer_event_map = dict()

    def add_consumer(self, consumer_cls):
        consumer = consumer_cls()
        self.consumer_event_map[consumer] = consumer.SUBSCRIBE_MSG_SET

    @staticmethod
    def handle_event_error(error: dict):
        code, msg = error["code"], error["message"]
        current_app.logger.error(f"websocket resp err, code: {code}, msg: {msg}")

    def handle_receive_event(self, event: dict):
        _id = event["id"]
        if result := event.get("result"):
            # 处理响应的 resp
            if error := event["error"]:
                self.handle_event_error(error)
            else:
                self.handle_event_resp(_id, result)
        else:
            # 处理订阅事件
            for consumer, event_set in self.consumer_event_map.items():
                if event["method"] in event_set:
                    consumer.handle_event(event)

    def handle_event_resp(self, _id, result):
        if _id == self.PING_EVENT_ID:
            if result != "pong":
                current_app.logger.error(f"websocket ping err, id: {_id}, result: {result}")
        elif _id == self.STATE_SUBSCRIBE_ID:
            if result.get("status") != "success":
                current_app.logger.error(f"websocket state subscribe err, id: {_id}, result: {result}")

    @property
    def state_subscribe_event(self):
        """
            订阅以后返回的 state.update
            server 第一次返回全量数据，后续返回增量数据
        """
        event = {
            "method": "state.subscribe",
            "params": [],
            "id": self.STATE_SUBSCRIBE_ID
        }
        return json.dumps(event)

    @property
    def ping_event(self):
        event = {
            "method": "server.ping",
            "params": [],
            "id": self.PING_EVENT_ID
        }
        return json.dumps(event)

    @staticmethod
    def is_pong_event(event):
        return event.get("result") == "pong"

    def send_ping(self, conn: ClientConnection):
        current_app.logger.info(f"send ping")
        conn.send(self.ping_event)

    def run_ws_client(self):
        with connect(self.URL) as conn:
            # 订阅 state.subscribe ，state.update 第一次推送全量数据，后续推送增量的市场变更数据
            conn.send(self.state_subscribe_event)
            current_app.logger.info(f"start receive websocket")
            st = time.time()
            while True:
                # 一分钟一次心跳
                _now = time.time()
                if _now - st > 60:
                    self.send_ping(conn)
                    st = _now
                message_str = conn.recv()
                event = json.loads(message_str)
                self.handle_receive_event(event)

    def run_forever(self):
        while True:
            try:
                self.run_ws_client()
            except Exception as e:
                current_app.logger.error(f"subscribe websocket failed: {e} \n {traceback.format_exc()}")
                time.sleep(60)

    def run_local(self):
        event = {'method': 'state.update', 'params':
            [{
                'SEERTUSD': {
                    'last': '0', 'open': '0', 'close': '0', 'high': '0', 'low': '0', 'volume': '0',
                    'sell_total': '0', 'buy_total': '0', 'period': 86400, 'deal': '0'}
            }],
                 'id': None}
        while True:
            self.handle_receive_event(event)
            time.sleep(1)


class SpotMsgProducer(WebSocketMsgProducer):
    URL = config["CLIENT_CONFIGS"]["SPOT_WEBSOCKET"]["url"]

    def __init__(self):
        super().__init__(self.URL)


class PerpetualMsgProducer(WebSocketMsgProducer):
    URL = config["CLIENT_CONFIGS"]["PERPETUAL_WEBSOCKET"]["url"]

    def __init__(self):
        super().__init__(self.URL)


def spot_run():
    producer = SpotMsgProducer()
    producer.add_consumer(SpotStateUpdateMsgConsumer)
    producer.run_forever()


def perpetual_run():
    producer = PerpetualMsgProducer()
    producer.add_consumer(PerpetualStateUpdateMsgConsumer)
    producer.run_forever()
