# -*- coding: utf-8 -*-

from typing import List, Tuple, Union

from ..utils import (CashAddressType, cashaddr_decode, cashaddr_encode,
                     cashaddr_encode_full)


class AssetUtils:

    TOP_ASSETS = (
        'CET',
        'USDT',
        'USDC',
        'BTC',
        'ETH',
    )

    TOP_CHAINS = (
        'CSC',
        'BTC',
        'BCH',
        'ETH'
    )

    _asset_indices = {name: idx for idx, name in enumerate(TOP_ASSETS)}
    _chain_indices = {name: idx for idx, name in enumerate(TOP_CHAINS)}


    @classmethod
    def sort_asset_codes(cls, assets: List[str]):
        indices = cls._asset_indices
        default_idx = len(indices)
        assets.sort(key=lambda a: (indices.get(a, default_idx), a))


    @classmethod
    def sort_chain_names(cls, chains: List[str]):
        indices = cls._chain_indices
        default_idx = len(indices)
        chains.sort(key=lambda c: (indices.get(c, default_idx), c))


    @classmethod
    def encode_cash_address(cls,
                            prefix: str,
                            type_: Union[int, CashAddressType],
                            payload: bytes,
                            *,
                            with_prefix: bool = True
                            ) -> str:
        return (cashaddr_encode_full if with_prefix else cashaddr_encode
                )(prefix, type_, payload)

    @classmethod
    def decode_cash_address(cls, address: str, prefix: str = ''
                            ) -> Tuple[str, int, bytes]:
        if ':' in address:
            _prefix, payload = address.split(':', 1)
            if prefix and _prefix != prefix:
                raise ValueError(
                    f'prefix should be {prefix!r}, got {_prefix!r} instead')
        elif not prefix:
            raise ValueError(
                'prefix is not contained in address and not specified either')
        else:
            address = ':'.join((prefix, address))
        return cashaddr_decode(address)
