# -*- coding: utf-8 -*-
import datetime
import re
from collections import defaultdict
from decimal import Decimal
from typing import Dict, List, NamedTuple, Optional, Union

from ..caches import AssetCache, ChainCache, MarketCache
from ..caches.assets import ChainDisplayMappingCache
from ..exceptions import (AssetNotFound, ChainNotFound, InvalidAssetCode,
                          InvalidChainName)
from ..models import AssetPrice
from ..utils import ConfigMode
from ..utils.date_ import date_to_datetime
from .configs import AssetChainConfig, AssetConfig
from .utils import AssetUtils

_RE_ASSET_CODE = re.compile(r'[A-Za-z0-9_]{1,32}')


class Asset(NamedTuple):

    code: str
    name: str
    # json serializable or ''
    # e.g:'{"original_code": "AIDOGE", "magnitude": {"name": "K", "order": 3, "decimal": 1000}}'
    magnification_info: str

    def as_dict(self):
        """return value must be redis serializable"""
        return dict(
            code=self.code,
            name=self.name,
            magnification_info=self.magnification_info,
        )


class Chain(NamedTuple):

    name: str
    display_name: str
    network_name: str
    main_asset: Optional[str]

    def as_dict(self):
        """return value must be redis serializable"""
        return dict(
            name=self.name,
            display_name=self.display_name,
            network_name=self.network_name,
            main_asset=self.main_asset or ''
        )


def normalise_asset_code(code: str) -> str:
    if not code or not _RE_ASSET_CODE.fullmatch(code):
        raise InvalidAssetCode
    return code.upper()


def normalise_chain_name(name: str) -> str:
    if not name or not _RE_ASSET_CODE.fullmatch(name):
        raise InvalidChainName
    return name.upper()


def get_asset(asset: str) -> Asset:
    asset = normalise_asset_code(asset)
    if not (values := AssetCache(asset).hgetall()):
        raise AssetNotFound(asset)
    return Asset(
        code=values['code'],
        name=values['name'],
        magnification_info=values['magnification_info'],
    )


def get_chain(chain: str) -> Chain:
    chain = normalise_chain_name(chain)
    if not (values := ChainCache(chain).hgetall()):
        raise ChainNotFound(chain)
    return Chain(
        name=values['name'],
        display_name=values['display_name'],
        network_name=values['network_name'],
        main_asset=values['main_asset'] or None
    )


def get_chain_names() -> Dict[str, str]:
    return ChainDisplayMappingCache().hgetall() or {}


def try_get_asset_chain_config(
        asset: str,
        chain: str,
        *,
        mode: ConfigMode = ConfigMode.SNAPSHOT
) -> Optional[AssetChainConfig]:
    try:
        cfg = get_asset_chain_config(asset, chain, mode=mode)
    except AssetNotFound:
        return
    else:
        return cfg


def get_asset_chain_config(asset: str, chain: str, *,
                           mode: ConfigMode = ConfigMode.SNAPSHOT) -> AssetChainConfig:
    asset = normalise_asset_code(asset)
    chain = normalise_asset_code(chain)
    if not AssetCache.has_asset(asset, chain):
        raise AssetNotFound(asset, chain)
    return AssetChainConfig(asset, chain, mode)


def get_asset_config(asset: str, *, mode: ConfigMode = ConfigMode.SNAPSHOT) -> AssetConfig:
    asset = normalise_asset_code(asset)
    if not AssetCache.has_asset(asset):
        raise ValueError(asset)
    return AssetConfig(asset, mode)


def try_get_asset_config(asset: str, *, mode: ConfigMode = ConfigMode.SNAPSHOT) -> Optional[AssetConfig]:
    asset = normalise_asset_code(asset)
    if not AssetCache.has_asset(asset):
        return None
    return AssetConfig(asset, mode)


def list_all_assets() -> List[str]:
    assets = AssetCache.list_all_assets()
    AssetUtils.sort_asset_codes(assets)
    return assets


def list_pre_assets() -> List[str]:
    return chain_to_assets("PRE_MARKET")


def is_pre_asset(asset: str) -> bool:
    return has_asset(asset, "PRE_MARKET")


def list_all_chains() -> List[str]:
    chains = AssetCache.list_all_chains()
    AssetUtils.sort_chain_names(chains)
    return chains


def has_asset(asset: str, chain: str = None) -> bool:
    asset = normalise_asset_code(asset)
    if chain:
        chain = normalise_chain_name(chain)
    return AssetCache.has_asset(asset, chain)


def has_chain(chain: str) -> bool:
    chain = normalise_chain_name(chain)
    return AssetCache.has_chain(chain)


def asset_to_chains(asset: str = None) -> Union[List[str], Dict[str, List[str]]]:
    if asset:
        asset = normalise_asset_code(asset)
    result = AssetCache.asset_to_chains(asset)
    if asset:
        return result
    assets = list(result.keys())
    AssetUtils.sort_asset_codes(assets)
    return {a: result[a] for a in assets}


def chain_to_assets(chain: str = None) -> Union[List[str], Dict[str, List[str]]]:
    if chain:
        chain = normalise_chain_name(chain)
    result = AssetCache.chain_to_assets(chain)
    if chain:
        return result
    chains = list(result.keys())
    AssetUtils.sort_chain_names(chains)
    return {c: result[c] for c in chains}


def is_multi_chain(asset: str) -> bool:
    return len(asset_to_chains(asset)) > 1


def asset_to_default_chain(asset: str) -> str:
    chains = asset_to_chains(asset)
    if not chains:
        raise AssetNotFound(asset)
    return chains[0]


def get_assets_min_order_amounts():
    from app.business import cached

    @cached(60 * 15)
    def _get_assets_min_order_amounts():
        ret = dict()
        for asset in list_all_assets():
            amount = get_asset_config(asset).min_order_amount
            ret[asset] = amount
        ret['USDT'] = Decimal('1')
        return ret
    result = _get_assets_min_order_amounts()
    res = {k: Decimal(v) for k, v in result.items() if v}
    return res


class AssetToMarketsHelper:
    """币币交易 币种-市场转换相关操作类"""

    @classmethod
    def asset_to_markets(cls, asset) -> List[str]:
        """给定币种作为交易币种的所有市场"""
        res = []
        markets_data = MarketCache.online_markets_detail()
        for market, detail in markets_data.items():
            if asset == detail['base_asset']:
                res.append(market)
        return res

    @classmethod
    def assets_markets_mapping(cls) -> Dict[str, List[str]]:
        """所有 交易币种-市场列表 映射"""
        assets_markets_mapping = defaultdict(list)
        markets_data = MarketCache.online_markets_detail()
        for market, detail in markets_data.items():
            assets_markets_mapping[detail['base_asset']].append(market)
        return assets_markets_mapping

    @classmethod
    def get_base_assets_deal_map(cls, start: int, end: int, period: int) -> Dict[str, Decimal]:
        """指定周期内所有交易币种的交易量（单位：交易币种）"""
        res = {}
        assets_markets_mapping = cls.assets_markets_mapping()
        for asset, markets in assets_markets_mapping.items():
            deal = cls.get_markets_deal(markets, start, end, period)
            res.update({asset: deal})
        return res

    @classmethod
    def get_asset_deal(cls, asset: str, start: int, end: int, period: int) -> Decimal:
        """给定币种作为交易币种时的交易量（单位：交易币种）"""
        markets = cls.asset_to_markets(asset)
        return cls.get_markets_deal(markets, start, end, period)

    @classmethod
    def get_markets_deal(cls, markets: List[str], start: int,
                                end: int, period: int) -> Decimal:
        """交易币种相同的市场列表成交的数量"""
        from ..business import ServerClient
        server_client = ServerClient()
        res = Decimal()
        for market in markets:
            ret = server_client.market_kline(
                market=market, start_time=start, end_time=end, interval=period)
            res += sum([Decimal(i[-2]) for i in ret])
        return res

    @classmethod
    def get_usdt_deal(cls, start, end, period):
        from ..business import ServerClient
        usdt_markets = cls.get_usdt_zone_markets()
        server_client = ServerClient()
        res = Decimal()
        for market in usdt_markets:
            ret = server_client.market_kline(
                market=market, start_time=start, end_time=end, interval=period)
            res += sum([Decimal(i[-1]) for i in ret])
        return res

    @classmethod
    def get_usdt_zone_markets(cls):
        """USDT作为定价币种的市场集合"""
        res = []
        markets_data = MarketCache.online_markets_detail()
        for market, detail in markets_data.items():
            if detail['quote_asset'] == 'USDT':
                res.append(market)
        return res

    @classmethod
    def get_base_assets_deal_usd_map(cls, report_date: datetime.date) -> Dict[str, Decimal]:
        """指定某天内所有交易币种的交易额（单位：usd）"""
        start = int(date_to_datetime(report_date).timestamp())
        end = (start + 86400) - 1
        period = 86400
        res = {}
        coin_rate = AssetPrice.get_close_price_map(report_date)
        markets_data = MarketCache.online_markets_detail()
        assets_markets_mapping = cls.assets_markets_mapping()
        for asset, markets in assets_markets_mapping.items():
            deal_usd = cls.get_markets_deal_usd(markets, start, end, period, markets_data,
                                                coin_rate)
            res.update({asset: deal_usd})
        return res

    @classmethod
    def get_markets_deal_usd(cls, markets: List[str], start: int,
                             end: int, period: int, markets_data: Dict,
                             coin_rate: Dict) -> Decimal:
        """交易币种相同的市场列表成交的数额（usd）"""
        from ..business import ServerClient
        server_client = ServerClient()
        asset_deal_usd = Decimal()
        for market in markets:
            quote_asset = markets_data[market]['quote_asset']
            ret = server_client.market_kline(
                market=market, start_time=start, end_time=end, interval=period)
            quote_amount = sum([Decimal(i[-1]) for i in ret])
            asset_deal_usd += quote_amount * coin_rate.get(quote_asset, 0)
        return asset_deal_usd
