# -*- coding: utf-8 -*-

import re
import json
import time
from collections import defaultdict
from datetime import timedelta, date
from decimal import Decimal
from typing import Literal
from itertools import chain

from celery.schedules import crontab
from flask import current_app
from dateutil.relativedelta import relativedelta

from app.business.market_maker import MarketMakerHelper
from app.business import lock_call, PriceManager, PerpetualServerClient
from app.business.external_dbs import PerpetualLogDB, PerpetualHistoryDB, PerpetualSummaryDB
from app.business.prop_trading import PropTradingBusiness
from app.caches.perpetual import PropTradingUsersCache, PerpetualMarketCache, PropTradingOpUsersCache
from app.caches.report import PropTradingHistoryProfitCache, PropTradingEvaluationCache, PropTradingPositionCursorCache
from app.common import CeleryQueues, PositionSide
from app.config import config
from app.models import MarketMaker, SubAccount, User, db
from app.models.prop_trading import (PropTradingSlice, DailyPropTradingReport,
                                     MonthlyPropTradingReport, PropTradingUser,
                                     PropTradingUserStat, PropTradingStatAggregation)
from app.utils import (route_module_to_celery_queue, scheduled, datetime_to_time,
                       batch_iter, today, timestamp_to_date, quantize_amount, celery_task)
from app.utils.parser import JsonEncoder
from app.utils.date_ import this_month

route_module_to_celery_queue(__name__, CeleryQueues.PERPETUAL)

# 下面的代码目前只支持正向合约市场

SLICE_INTERVAL = 600

@scheduled(crontab(minute='45', hour='1-3'))
@lock_call()
def prop_users_evaluation_schedule():
    end_date = today() - timedelta(days=1)
    cache = PropTradingEvaluationCache()
    last_time = cache.get_last_evaluation()
    if last_time and timestamp_to_date(last_time) >= end_date:
        return
    config = PropTradingBusiness.get_config()
    days = max(config.whitelist_deal_days, 1)
    start_date = today() - timedelta(days=days)
    if not prop_users_evaluation(config, start_date, end_date):
        return
    cache.save_evaluation_time(datetime_to_time(end_date))
    PropTradingBusiness.update_prop_config()


def prop_users_evaluation(config, start_date, end_date):
    if not PerpetualSummaryDB.is_data_completed(end_date):
        return False
    user_deals = PerpetualData.get_user_trade_amount(start_date, end_date)
    prop_users = list(iter_all_prop_users())
    new_users = set(user_deals.keys()) - {x.user_id for x in prop_users}
    whitelist_users = [x.user_id for x in prop_users if x.status == PropTradingUser.Status.W]
    blacklist_users = {x.user_id: x.reason for x in prop_users if x.status == PropTradingUser.Status.B}
    makers = list_black_users('maker')
    systems = list_black_users('system')
    copy_traders = list_black_users('copy-trading')
    sub_to_main = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
    sub_to_main = dict(sub_to_main)

    def get_register_dates(_users):
        rs = {}
        main_ids = [sub_to_main.get(x, x) for x in _users]
        for uids in batch_iter(main_ids, 500):
            rows = User.query.filter(User.id.in_(uids)).with_entities(User.id, User.created_at).all()
            rs.update({row.id: date(row.created_at.year, row.created_at.month, row.created_at.day) for row in rows})
        return rs

    register_dates = get_register_dates(new_users)

    # 评估新用户
    for user_id in new_users:
        reason = None
        if user_id in systems:
            reason = PropTradingUser.Reason.SYSTEM
        elif user_id in makers:
            reason = PropTradingUser.Reason.MAKER
        elif user_id in copy_traders:
            reason = PropTradingUser.Reason.COPY_TRADING
        # 如果主账户在黑名单中，那么子账号也应该进黑名单
        elif (_main_id := sub_to_main.get(user_id, user_id)) in blacklist_users:
            reason = blacklist_users[_main_id]
        if reason:
            db.session.add(PropTradingUser(
                user_id=user_id,
                evaluation_time=end_date,
                reason=reason,
                detail='',
                first_deal_time=None,
                status=PropTradingUser.Status.B
            ))
        else:
            # 最近成交量大于x，并且是x天之前注册才进入白名单
            if user_deals[user_id] < config.whitelist_deal_amount:
                continue
            register_start = end_date - timedelta(days=config.whitelist_deal_days)
            if register_dates[sub_to_main.get(user_id, user_id)] >= register_start:
                continue
            db.session.add(PropTradingUser(
                user_id=user_id,
                evaluation_time=end_date,
                reason=PropTradingUser.Reason.NO,
                detail='',
                first_deal_time=None,
                status=PropTradingUser.Status.W
            ))
    # 评估旧用户
    for user_id in whitelist_users:
        reason = None
        if user_id in systems:
            reason = PropTradingUser.Reason.SYSTEM
        elif user_id in makers:
            reason = PropTradingUser.Reason.MAKER
        elif user_id in copy_traders:
            reason = PropTradingUser.Reason.COPY_TRADING
        elif (_main_id := sub_to_main.get(user_id, user_id)) in blacklist_users:
            reason = blacklist_users[_main_id]
        if reason:
            PropTradingUser.query.filter(PropTradingUser.user_id == user_id).update({
                PropTradingUser.evaluation_time: end_date,
                PropTradingUser.reason: reason,
                PropTradingUser.status: PropTradingUser.Status.B
            }, synchronize_session=False)
    db.session.commit()
    return True


def iter_all_prop_users():
    limit = 10000
    last = 0
    while True:
        rows = PropTradingUser.query.filter(PropTradingUser.id > last).order_by(PropTradingUser.id) \
                                    .limit(limit).with_entities(
                                        PropTradingUser.id, PropTradingUser.user_id,
                                        PropTradingUser.reason, PropTradingUser.status,
                                        PropTradingUser.first_deal_time
                                    ).all()
        yield from rows
        if len(rows) != limit:
            break
        last = rows[-1].id

def list_black_users(scope: Literal['all', 'maker', 'system', 'copy-trading']):
    blacklist = set()
    if scope == 'all' or scope =='maker':
        # 做市商账户
        rows = MarketMaker.query.with_entities(MarketMaker.user_id).distinct().all()
        blacklist.update(row[0] for row in rows)
        blacklist.update(MarketMakerHelper.list_inner_maker_ids(include_sub_account=False))

    if scope == 'all' or scope =='system':
        # 系统账户
        for k, v in config.items():
            if k.endswith('USER_ID') and isinstance(v, int) and v > 0:
                blacklist.add(v)

    if scope == 'all' or scope == 'copy-trading':
        # 跟单用户只加入该子账号，单带员所有账户都应该进入黑名单
        rows = SubAccount.query.filter(SubAccount.type == SubAccount.Type.COPY_TRADER).with_entities(SubAccount.main_user_id).all()
        blacklist.update(row[0] for row in rows)
        rows = SubAccount.query.filter(SubAccount.type == SubAccount.Type.COPY_FOLLOWER).with_entities(SubAccount.user_id).all()
        blacklist.update(row[0] for row in rows)

    rows = SubAccount.query.with_entities(SubAccount.main_user_id, SubAccount.user_id, ).all()
    subs = {}
    for main_id, user_id in rows:
        subs.setdefault(main_id, []).append(user_id)
    result = set()
    for user_id in blacklist:
        result.add(user_id)
        if _subs :=subs.get(user_id):
            result.update(_subs)
    return result

def percent(a, b):
    if b == 0:
        return 0
    return quantize_amount(a / b, 4)


@scheduled(crontab(minute='0,10,20,30,40,50'))
@lock_call()
def make_prop_trading_slice_schedule():
    end_time = int(time.time())
    end_time -= end_time % SLICE_INTERVAL
    row = PropTradingSlice.query.order_by(PropTradingSlice.slice_time.desc()).first()
    if not row:
        slice_time = end_time
    else:
        slice_time = row.slice_time + SLICE_INTERVAL

    while slice_time <= end_time:
        try:
            make_prop_trading_slice(slice_time)
        except Exception as e:
            current_app.logger.warning('make prop slice %s failed: %s. use fake instead', slice_time, e)
            _make_fake_slice(slice_time)
        slice_time += SLICE_INTERVAL


def _make_fake_slice(slice_time):
    rows = PropTradingSlice.query.filter(PropTradingSlice.slice_time == slice_time - SLICE_INTERVAL).all()
    if not rows:
        raise RuntimeError('no last slices')
    for row in rows:
        db.session.add(PropTradingSlice(
            slice_time=slice_time,
            market=row.market,
            equity=row.equity,
            total_profit=row.total_profit,
            position_amount=row.position_amount,
            position_abs_amount=row.position_abs_amount,
            op_position_abs_amount=row.op_position_abs_amount,
            op_position_long_amount=row.op_position_long_amount,
            op_position_short_amount=row.op_position_short_amount,
            op_position_long_user_count=row.op_position_long_user_count,
            op_position_short_user_count=row.op_position_short_user_count,
            position_change=0,
            margin_amount=row.margin_amount,
            op_user_count=row.op_user_count,
            tbr_user_count=row.tbr_user_count,
            deal_amount_10m=0,
            deal_amount_24h=0,
            available_amount=row.available_amount,
            curr_position_profit=row.curr_position_profit,
            profit_24h=row.profit_24h,
            whitelist_margin_amount=row.whitelist_margin_amount,
            price=row.price,
        ))
    db.session.commit()


def make_prop_trading_slice(slice_time):
    user_id = config['PROP_TRADING_USER_ID']
    history_profits = PerpetualData.get_history_total_profit(user_id)
    # 确保server已经快照
    for _ in range(10):
        if PerpetualLogDB.get_slice_history_timestamp(slice_time, interval=SLICE_INTERVAL):
            break
        time.sleep(30)
    else:
        raise RuntimeError("no slice history data")
    # 异步更新缓存
    update_op_users_cache_task.delay()

    equity, balance, available, margins, unreal_profits = PerpetualData.get_balances(user_id, slice_time)
    positions = PerpetualData.get_positions(user_id, slice_time)
    deals_10m = PerpetualData.get_deal_amount(user_id, slice_time - SLICE_INTERVAL, slice_time)
    deals_24h = PerpetualData.get_deal_amount(user_id, slice_time - 86400, slice_time)

    def get_24h_befor_slices():
        row = PropTradingSlice.query.filter(PropTradingSlice.slice_time <= slice_time - 86400) \
                .order_by(PropTradingSlice.slice_time.desc()).first()
        if not row:
            return {}
        rows = PropTradingSlice.query.filter(PropTradingSlice.slice_time == row.slice_time).all()
        return {x.market: x for x in rows}
    befor_24h_slices = get_24h_befor_slices()

    # maybe_op_users: 已经跟自营用户做对手盘，以及后面会做对手盘的用户
    # tbr_users: 已经跟自营用户做对手盘，但不在白名单中的用户
    # op_users: 已经跟自营用户做对手盘的用户。
    m_maybe_op_users, whitelist = PerpetualData.get_prop_users(slice_time)
    m_op_l_users, m_op_l_positions, m_op_s_users, m_op_s_positions = \
            PerpetualData.get_op_users_of_position_side(m_maybe_op_users, slice_time)
    m_op_users, m_op_positions = PerpetualData.merge_op_users_of_position(m_op_l_users, m_op_l_positions, m_op_s_users, m_op_s_positions)
    op_users = set().union(*m_op_users.values())
    m_tbr_users = {k: set(v) - whitelist for k, v in m_op_users.items()}
    tbr_users = set().union(*m_tbr_users.values())
    whitelist_margins, whitelist_total = PerpetualData.get_users_balances(whitelist, slice_time)

    position_usd = 0
    position_abs_usd = 0
    position_change_usd = 0
    op_position_abs_usd = 0
    op_position_long_usd = 0
    op_position_short_usd = 0
    prices = PriceManager.assets_to_usd()
    m_info = PerpetualMarketCache().read_aside()

    markets = PropTradingBusiness.get_supported_markets()
    for market in markets:
        position = positions.get(market)
        # 多仓正值，空仓负值
        position_amount = 0
        # 当前持仓盈亏(已实现+未实现)
        curr_profit = 0
        if position:
            curr_profit += position['profit_real']
            curr_profit += unreal_profits.get(market, 0)
            if position['side'] == PositionSide.LONG:
                position_amount = position['amount']
            else:
                position_amount = -position['amount']
        position_change = position_amount
        last_slice = PropTradingSlice.query.filter(PropTradingSlice.market == market) \
                                     .order_by(PropTradingSlice.slice_time.desc()).first()
        if last_slice:
            position_change -= last_slice.position_amount

        price = prices.get(m_info[market]['stock'], 0)
        position_usd += position_amount * price
        position_abs_usd += abs(position_amount * price)
        position_change_usd += position_change * price
        op_position_abs_usd += m_op_positions.get(market, 0) * price
        op_position_long_usd += m_op_l_positions.get(market, 0) * price
        op_position_short_usd += m_op_s_positions.get(market, 0) * price
        # 累计盈亏 = 历史仓位盈亏 + 当前仓位盈亏
        total_profit = history_profits.get(market, 0) + curr_profit
        befor_24h_slice = befor_24h_slices.get(market)

        db.session.add(PropTradingSlice(
            slice_time=slice_time,
            market=market,
            equity=None,
            total_profit=total_profit,
            position_amount=position_amount,
            position_abs_amount=abs(position_amount),
            op_position_abs_amount=m_op_positions.get(market, 0),
            op_position_long_amount=m_op_l_positions.get(market, 0),
            op_position_short_amount=m_op_s_positions.get(market, 0),
            op_position_long_user_count=len(m_op_l_users.get(market, [])),
            op_position_short_user_count=len(m_op_s_users.get(market, [])),
            position_change=position_change,
            margin_amount=margins.get(market, 0),
            op_user_count=len(m_op_users.get(market, [])),
            tbr_user_count=len(m_tbr_users.get(market, [])),
            deal_amount_10m=deals_10m.get(market, 0),
            deal_amount_24h=deals_24h.get(market, 0),
            available_amount=None,
            curr_position_profit=curr_profit,
            profit_24h=total_profit - befor_24h_slice.total_profit if befor_24h_slice else 0,
            whitelist_margin_amount=whitelist_margins.get(market, 0),
            deal_amount_rate=0, # 后面再计算
            price=price
        ))

    market = 'ALL'
    curr_profit = sum(x['profit_real'] for x in positions.values()) \
                   + sum(unreal_profits.values())
    total_profit = sum(history_profits.values()) + curr_profit
    befor_24h_slice = befor_24h_slices.get(market)

    db.session.add(PropTradingSlice(
        slice_time=slice_time,
        market=market,
        equity=equity,
        total_profit=total_profit,
        position_amount=quantize_amount(position_usd, 8),
        position_abs_amount=quantize_amount(position_abs_usd, 8),
        op_position_abs_amount=op_position_abs_usd,
        op_position_long_amount=op_position_long_usd,
        op_position_short_amount=op_position_short_usd,
        op_position_long_user_count=len(set().union(*m_op_l_users.values())),
        op_position_short_user_count=len(set().union(*m_op_s_users.values())),
        position_change=quantize_amount(position_change_usd, 8),
        margin_amount=balance,  # 全部市场时，所有资产，不含未实现盈亏
        op_user_count=len(op_users),
        tbr_user_count=len(tbr_users),
        deal_amount_10m=sum(deals_10m.values()),
        deal_amount_24h=sum(deals_24h.values()),
        available_amount=available,
        curr_position_profit=sum(unreal_profits.values()) + sum(p['profit_real'] for p in positions.values()),
        profit_24h=total_profit - befor_24h_slice.total_profit if befor_24h_slice else 0,
        whitelist_margin_amount=whitelist_total,
        deal_amount_rate=0,
        price=1
    ))
    db.session.commit()
    update_slice_ex_field(slice_time)


def update_slice_ex_field(slice_time):
    """更新切片的其他字段，较慢的字段在这里处理"""
    rows = PropTradingSlice.query.filter(PropTradingSlice.slice_time == slice_time).all()
    if not rows:
        return
    # 最近24h全站成交数据
    market_deals = PerpetualData.get_deal_amount_without_makers(slice_time - 86400, slice_time)
    for row in rows:
        if row.market == 'ALL':
            _site_deal = sum(market_deals.values())
        else:
            _site_deal = market_deals.get(row.market, 0)
        row.deal_amount_rate = percent(row.deal_amount_24h, _site_deal)
    db.session.commit()


@celery_task
@lock_call()
def update_op_users_cache_task():
    table = PerpetualLogDB.table('slice_history')
    data = table.select('prop_user_list', order_by="id desc", limit=1)[0][0]
    data = json.loads(data)
    PropTradingOpUsersCache.update(data)


@scheduled(crontab(minute='34', hour="0-3"))
@lock_call()
def update_daily_prop_trading_report_schedule():
    _today = today()
    last_record = DailyPropTradingReport.query.order_by(
        DailyPropTradingReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = _today - timedelta(days=1)
    while start_date < _today:
        update_daily_prop_trading_report(start_date)
        start_date += timedelta(days=1)


def update_daily_prop_trading_report(report_date):
    if not PerpetualSummaryDB.is_data_completed(report_date):
        return
    # 检查0点附近是否有slice
    report_time = datetime_to_time(report_date)
    row = PropTradingSlice.query.filter(
        PropTradingSlice.slice_time <= report_time + 86400
    ).order_by(PropTradingSlice.slice_time.desc()).first()
    if not row or report_time - row.slice_time > 6 * SLICE_INTERVAL:
        current_app.logger.warning("no prop trading slice")
        return
    # 取0点(或接近0点)的快照，作为报表部分字段来源
    slice_time = row.slice_time
    slices = PropTradingSlice.query.filter(PropTradingSlice.slice_time == slice_time).all()
    slices = {x.market: x for x in slices}
    last_reports = DailyPropTradingReport.query.filter(
        DailyPropTradingReport.report_date == report_date - timedelta(days=1)
    ).all()
    last_reports = {x.market: x for x in last_reports}
    # 先获取昨日交易对手盘用户，更新用户首次交易时间
    user_id = config['PROP_TRADING_USER_ID']
    m_op_deal_users = PerpetualData.get_op_users_of_deal(
        user_id,
        report_time, report_time + 86400)
    op_deal_users = set().union(*m_op_deal_users.values())
    first_deal_users = update_first_deal_time(op_deal_users, report_date)
    # 当前对手盘用户列表
    m_maybe_op_users, whitelist = PerpetualData.get_prop_users(slice_time)
    m_op_users, _ = PerpetualData.get_op_users_of_position(m_maybe_op_users, slice_time)
    op_users = set().union(*m_op_users.values())
    # 昨日全站成交数据
    market_deals = PerpetualData.get_user_trade_amount_without_makers(report_date)
    sys_deal, site_deal, site_deal_users = 0, 0, set()
    m_sys_deal, m_site_deal, m_site_deal_users = defaultdict(Decimal), defaultdict(Decimal), defaultdict(set)
    for (_uid, _m), _amount in market_deals.items():
        if _uid == user_id:
            sys_deal += _amount
            m_sys_deal[_m] += _amount
        site_deal += _amount
        m_site_deal[_m] += _amount
        site_deal_users.add(_uid)
        m_site_deal_users[_m].add(_uid)

    markets = PropTradingBusiness.get_supported_markets()
    for market in markets:
        slice_data = slices.get(market)
        if not slice_data:
            continue
        last_report = last_reports.get(market)
        day_profit = slice_data.total_profit - last_report.total_profit if last_report else slice_data.total_profit
        _sys_deal = m_sys_deal.get(market, 0)
        _site_deal = m_site_deal.get(market, 0)
        _op_users = m_op_users.get(market, set())
        _op_deal_users = m_op_deal_users.get(market, set())
        _site_deal_users = m_site_deal_users.get(market, set())
        db.session.add(DailyPropTradingReport(
            report_date=report_date,
            market=market,
            equity=slice_data.equity,
            total_profit=slice_data.total_profit,
            day_profit=day_profit,
            whitelist_user_count=len(whitelist),
            op_user_count=len(_op_users),  # 当前市场下存在的已跟踪用户数
            new_op_user_count=len(_op_deal_users & first_deal_users),  # 新增的首次交易跟踪用户数
            deal_user_count=len(_op_deal_users),
            deal_amount=_sys_deal,
            deal_amount_rate=percent(_sys_deal, _site_deal),  # 成交量是双边的
            deal_user_rate=percent(len(_op_deal_users), len(_site_deal_users)),
            profit_rate=percent(day_profit, _sys_deal)
        ))
    market = 'ALL'
    slice_data = slices.get(market)
    if not slice_data:
        return
    last_report = last_reports.get(market)
    day_profit = slice_data.total_profit - last_report.total_profit if last_report else slice_data.total_profit
    db.session.add(DailyPropTradingReport(
        report_date=report_date,
        market=market,
        equity=slice_data.equity,
        total_profit=slice_data.total_profit,
        day_profit=day_profit,
        whitelist_user_count=len(whitelist),
        op_user_count=len(op_users),
        new_op_user_count=len(op_deal_users & first_deal_users),
        deal_user_count=len(op_deal_users),
        deal_amount=sys_deal,
        deal_amount_rate=percent(sys_deal, site_deal),
        deal_user_rate=percent(len(op_deal_users), len(site_deal_users)),
        profit_rate=percent(day_profit, sys_deal)
    ))
    db.session.commit()


@scheduled(crontab(minute='54', hour="1-4", day_of_month="1"))
@lock_call()
def update_montyly_prop_trading_report_schedule():
    _today = this_month()
    last_record = MonthlyPropTradingReport.query.order_by(
        MonthlyPropTradingReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + relativedelta(months=1)
    else:
        report = DailyPropTradingReport.query.order_by(DailyPropTradingReport.report_date).first()
        if not report:
            return
        start_date = date(year=report.report_date.year, month=report.report_date.month, day=1)
    while start_date < _today:
        update_montyly_prop_trading_report(start_date)
        start_date += relativedelta(months=1)


def update_montyly_prop_trading_report(report_date):
    end_date = report_date + relativedelta(months=1)
    end_date = min(end_date, today())
    end_date -= timedelta(days=1)
    slice_reports = DailyPropTradingReport.query.filter(DailyPropTradingReport.report_date == end_date).all()
    if not slice_reports:
        return
    slice_reports = {x.market: x for x in slice_reports}
    last_reports = DailyPropTradingReport.query.filter(DailyPropTradingReport.report_date == report_date - timedelta(days=1)).all()
    last_reports = {x.market: x for x in last_reports}

    user_id = config['PROP_TRADING_USER_ID']
    # 月内首次交易用户
    first_deal_users = set()
    for row in iter_all_prop_users():
        if row.first_deal_time and report_date <= row.first_deal_time.date() <= end_date:
            first_deal_users.add(row.user_id)
    # 对手盘交易用户
    m_op_deal_users = PerpetualData.get_op_users_of_deal(
        user_id,
        datetime_to_time(report_date), datetime_to_time(end_date) + 86400)
    op_deal_users = set().union(*m_op_deal_users.values())
    # 月全站成交数据
    market_deals = PerpetualData.get_user_trade_amount_without_makers(report_date, end_date)
    sys_deal, site_deal, site_deal_users = 0, 0, set()
    m_sys_deal, m_site_deal, m_site_deal_users = defaultdict(Decimal), defaultdict(Decimal), defaultdict(set)
    for (_uid, _m), _amount in market_deals.items():
        if _uid == user_id:
            sys_deal += _amount
            m_sys_deal[_m] += _amount
        site_deal += _amount
        m_site_deal[_m] += _amount
        site_deal_users.add(_uid)
        m_site_deal_users[_m].add(_uid)

    markets = PropTradingBusiness.get_supported_markets()
    for market in markets:
        slice_report = slice_reports.get(market)
        if not slice_report:
            continue
        last_report = last_reports.get(market)
        day_profit = slice_report.total_profit - last_report.total_profit if last_report else slice_report.total_profit
        _sys_deal = m_sys_deal.get(market, 0)
        _site_deal = m_site_deal.get(market, 0)
        _op_deal_users = m_op_deal_users.get(market, set())
        _site_deal_users = m_site_deal_users.get(market, set())
        db.session.add(MonthlyPropTradingReport(
            report_date=report_date,
            market=market,
            equity=slice_report.equity,
            total_profit=slice_report.total_profit,
            day_profit=day_profit,
            whitelist_user_count=slice_report.whitelist_user_count,
            op_user_count=slice_report.op_user_count,
            new_op_user_count=len(_op_deal_users & first_deal_users),
            deal_user_count=len(_op_deal_users),
            deal_amount=_sys_deal,
            deal_amount_rate=percent(_sys_deal, _site_deal),  # 成交量是双边的
            deal_user_rate=percent(len(_op_deal_users), len(_site_deal_users)),
            profit_rate=percent(day_profit, _sys_deal)
        ))
    market = 'ALL'
    slice_report = slice_reports.get(market)
    if not slice_report:
        return
    last_report = last_reports.get(market)
    day_profit = slice_report.total_profit - last_report.total_profit if last_report else slice_report.total_profit
    db.session.add(MonthlyPropTradingReport(
        report_date=report_date,
        market=market,
        equity=slice_report.equity,
        total_profit=slice_report.total_profit,
        day_profit=day_profit,
        whitelist_user_count=slice_report.whitelist_user_count,
        op_user_count=slice_report.op_user_count,
        new_op_user_count=len(op_deal_users & first_deal_users),
        deal_user_count=len(op_deal_users),
        deal_amount=sys_deal,
        deal_amount_rate=percent(sys_deal, site_deal),
        deal_user_rate=percent(len(op_deal_users), len(site_deal_users)),
        profit_rate=percent(day_profit, sys_deal)
    ))
    db.session.commit()


def update_first_deal_time(deal_users, report_date):
    rs = set()
    for uids in batch_iter(deal_users, 500):
        rows = PropTradingUser.query.filter(PropTradingUser.user_id.in_(uids),
                                            PropTradingUser.first_deal_time.is_(None)).all()
        for row in rows:
            row.first_deal_time = report_date
            rs.add(row.user_id)
    db.session.commit()
    return rs


@scheduled(crontab(minute='*/5'))
@lock_call()
def prop_trading_risk_control_schedule():
    end = int(time.time())
    start = end - 86400
    prices = PriceManager.assets_to_usd()
    client = PerpetualServerClient()
    conf = PropTradingBusiness.get_config()
    markets = PropTradingBusiness.get_supported_markets()
    risk_users = {}
    for market in markets:
        # 找出每个市场下成交量超过阈值，且成交量占比也超过阈值的用户
        m_info = PerpetualMarketCache().read_aside()
        price = prices.get(m_info[market]['stock'], 0)
        rs = defaultdict(Decimal)
        data = client.query_amount_rank(market, start, end)
        total_deal_amount = (Decimal(data['total_buy_amount']) + Decimal(data['total_sell_amount'])) / 2 # 单边
        for item in chain(data['buy'], data['sell']):
            rs[item['user_id']] += Decimal(item['amount'])
        for user_id, amount in sorted(rs.items(), key=lambda x: x[1], reverse=True):
            if amount * price < conf.risk_deal_amount or amount / total_deal_amount < conf.risk_deal_amount_rate:
                break
            if user_id not in risk_users:
                risk_users[user_id] = {'market': market,
                                       'deal_amount': amount,
                                       'total_deal_amount': total_deal_amount,
                                       'deal_rate': quantize_amount(amount / total_deal_amount, 4)
                                       }
    if not risk_users:
        return
    # 这些风险用户如果是自营账户的对手盘，那么直接进入黑名单
    op_users = PerpetualData.get_op_users_of_deal(config['PROP_TRADING_USER_ID'], start, end)
    op_users = set().union(*op_users.values())
    risk_users = {k: v for k, v in risk_users.items() if k in op_users}
    if not risk_users:
        return
    new_users = []
    for user_id, detail in risk_users.items():
        row = PropTradingUser.query.filter(PropTradingUser.user_id == user_id).first()
        if row:
            if row.status == PropTradingUser.Status.W:
                row.status = PropTradingUser.Status.B
                row.evaluation_time = timestamp_to_date(end)
                row.reason = PropTradingUser.Reason.RISK_CONTROL
                row.detail = json.dumps(detail, cls=JsonEncoder)
                new_users.append(user_id)
        else:
            db.session.add(PropTradingUser(
                user_id=user_id,
                evaluation_time=timestamp_to_date(end),
                reason=PropTradingUser.Reason.RISK_CONTROL,
                detail=json.dumps(detail, cls=JsonEncoder),
                first_deal_time=None,
                status=PropTradingUser.Status.B,
            ))
            new_users.append(user_id)
        db.session.commit()

    if new_users:
        PropTradingBusiness.update_prop_config()
        client.remove_prop_user(*new_users)


@scheduled(crontab(minute='*/5'))
@lock_call()
def prop_trading_market_risk_control_schedule():
    prop_user_id = config['PROP_TRADING_USER_ID']
    end = int(time.time())
    start = end - 86400
    prices = PriceManager.assets_to_usd()
    client = PerpetualServerClient()
    conf = PropTradingBusiness.get_config()
    markets = PropTradingBusiness.get_supported_markets()
    blacklist = []
    risk_users = {}
    for market in markets:
        m_info = PerpetualMarketCache().read_aside()
        price = prices.get(m_info[market]['stock'], 0)
        data = client.query_amount_rank(market, start, end)
        total_deal_amount = (Decimal(data['total_buy_amount']) + Decimal(data['total_sell_amount'])) / 2 # 单边
        if not total_deal_amount:
            continue
        deal_amount = 0
        for item in chain(data['buy'], data['sell']):
            if item['user_id'] == prop_user_id:
                deal_amount += Decimal(item['amount'])
        deal_rate = deal_amount / total_deal_amount
        if deal_amount * price >= conf.prop_risk_deal_amount and deal_rate >= conf.prop_risk_deal_amount_rate:
            current_app.logger.warning(f"prop market {market} prop user deal rate {deal_rate}, add to blacklist")
            # 找出市场下成交量占比超过10%的用户
            rs = defaultdict(Decimal)
            for item in chain(data['buy'], data['sell']):
                rs[item['user_id']] += Decimal(item['amount'])
            for user_id, amount in sorted(rs.items(), key=lambda x: x[1], reverse=True):
                _rate = amount / total_deal_amount
                if _rate < Decimal('0.1'):
                    break
                if user_id not in risk_users:
                    risk_users[user_id] = {'market': market,
                                           'deal_rate': quantize_amount(_rate, 4),
                                           'reason': 'market risk control'
                                           }
            raw = []
            if conf.blacklist_markets:
                raw = json.loads(conf.blacklist_markets)
            raw.append(market)
            conf.blacklist_markets = json.dumps(raw)
            db.session.commit()
            blacklist.append(market)
    # 取对手盘用户，不是对手盘用户无需处理
    new_users = []
    if risk_users:
        op_users = PerpetualData.get_op_users_of_deal(config['PROP_TRADING_USER_ID'], start, end)
        op_users = set().union(*op_users.values())
        risk_users = {k: v for k, v in risk_users.items() if k in op_users}
        for user_id, detail in risk_users.items():
            row = PropTradingUser.query.filter(PropTradingUser.user_id == user_id).first()
            if row:
                if row.status == PropTradingUser.Status.W:
                    row.status = PropTradingUser.Status.B
                    row.evaluation_time = timestamp_to_date(end)
                    row.reason = PropTradingUser.Reason.RISK_CONTROL
                    row.detail = json.dumps(detail, cls=JsonEncoder)
                    new_users.append(user_id)
            else:
                db.session.add(PropTradingUser(
                    user_id=user_id,
                    evaluation_time=timestamp_to_date(end),
                    reason=PropTradingUser.Reason.RISK_CONTROL,
                    detail=json.dumps(detail, cls=JsonEncoder),
                    first_deal_time=None,
                    status=PropTradingUser.Status.B,
                ))
                new_users.append(user_id)
            db.session.commit()

    if blacklist:
        client.remove_prop_market(*blacklist)
    if new_users:
        PropTradingBusiness.update_prop_config()
        client.remove_prop_user(*new_users)


@scheduled(crontab(minute='*/30'))
@lock_call()
def prop_trading_user_stat_schedule():
    cursors = PropTradingPositionCursorCache().get_cursors()
    start = None
    if not cursors:
        current_app.logger.warning("prop trading user stat: no cursors, reset data")
        PropTradingUserStat.query.delete(synchronize_session=False)
        db.session.commit()
        start = today() - timedelta(max(PropTradingStatAggregation.PERIODS) - 1)
        start = datetime_to_time(start)

    positions, cursors = PerpetualData.iter_op_position_history(config['PROP_TRADING_USER_ID'], cursors, start)
    stat = {}
    for pos in positions:
        user_id = pos['user_id']
        t = pos['create_time']
        profit = pos['profit_real']
        # 按天分组统计
        t = t - t % 86400
        if t in stat:
            s = stat[t]
        else:
            s = stat[t] = {}
        if user_id in s:
            s[user_id]['position_count'] += 1
            if profit > 0:
                s[user_id]['win_count'] += 1
            s[user_id]['win_amount'] += profit
        else:
            s[user_id] = {
                'position_count': 1,
                'win_count': 1 if profit > 0 else 0,
                'win_amount': profit
            }

    for t, s in stat.items():
        rows = PropTradingUserStat.query.filter(PropTradingUserStat.date == timestamp_to_date(t)).all()
        rows = {x.user_id: x for x in rows}
        for user_id, data in s.items():
            if user_id in rows:
                row = rows[user_id]
                row.position_count += data['position_count']
                row.win_count += data['win_count']
                row.win_amount += data['win_amount']
            else:
                db.session.add(PropTradingUserStat(
                    user_id=user_id,
                    date=timestamp_to_date(t),
                    position_count=data['position_count'],
                    win_count=data['win_count'],
                    win_amount=data['win_amount']
                ))
    db.session.commit()
    PropTradingPositionCursorCache().save_cursors(cursors)
    aggregate_user_stat()


def aggregate_user_stat():
    """聚合用户统计结果"""
    _today = today()
    start = _today - timedelta(days=max(PropTradingStatAggregation.PERIODS) - 1)
    stat = {}
    while start <= _today:
        rows = PropTradingUserStat.query.filter(PropTradingUserStat.date == start).all()
        periods = [n for n in PropTradingStatAggregation.PERIODS if start >= _today - timedelta(days=n)]
        for row in rows:
            for n in periods:
                if n in stat:
                    s = stat[n]
                else:
                    s = stat[n] = {}
                if row.user_id in s:
                    s[row.user_id]['position_count'] += row.position_count
                    s[row.user_id]['win_count'] += row.win_count
                    s[row.user_id]['win_amount'] += row.win_amount
                else:
                    s[row.user_id] = {
                        'position_count': row.position_count,
                        'win_count': row.win_count,
                        'win_amount': row.win_amount
                    }
        start += relativedelta(days=1)

    PropTradingStatAggregation.query.delete(synchronize_session=False)
    rows = []
    for p, s in stat.items():
        for user_id, data in s.items():
            rows.append(PropTradingStatAggregation(
                user_id=user_id,
                period=p,
                position_count=data['position_count'],
                win_count=data['win_count'],
                win_amount=data['win_amount'],
                win_rate=percent(data['win_count'], data['position_count']),
            ))
            if len(rows) >= 10000:
                db.session.bulk_save_objects(rows)
                db.session.flush()
                rows.clear()
    if rows:
        db.session.bulk_save_objects(rows)
    db.session.commit()


class PerpetualData:

    @classmethod
    def get_prop_users(cls, slice_time) -> tuple[dict[str, set], set[int]]:
        """获取已经/可以跟自营账户做对手盘的用户(按市场分组)，以及全局白名单用户"""
        ts = PerpetualLogDB.get_slice_history_timestamp(slice_time, interval=SLICE_INTERVAL)
        table = PerpetualLogDB.table('slice_history')
        data = table.select('prop_user_list', where=f"time={ts}", order_by="id desc", limit=1)[0][0]
        data = json.loads(data)
        # server将当前市场的用户交集存储到common这个key中，将已移除的市场用户(待失效)存储在blacklist这个key中
        common = data.pop('common', set())
        blacklist = data.pop('blacklist', {})
        m_op_users = {k: set(v) for k, v in data.items()}
        if common:
            for v in m_op_users.values():
                v.update(common)
        if blacklist:
            for k, v in blacklist.items():
                m_op_users[k] = set(v)
        if data := PropTradingUsersCache().read():
            whitelist = set(json.loads(data))
        else:
            whitelist = set(PropTradingBusiness.sample_prop_users())
        return m_op_users, whitelist

    @classmethod
    def get_balances(cls, user_id, slice_time):
        """返回总账户权益, 总资产(不含未实现盈亏), 可用资产，保证金(按市场分组), 未实现盈亏(按市场分组)"""
        table = PerpetualLogDB.slice_balance_table(slice_time, interval=SLICE_INTERVAL)
        rows = table.select("market, type, balance", where=f"user_id={user_id}")
        margins = {}
        unreal_profits = {}
        for market, typ, balance in rows:
            if market.endswith('USD'):
                continue
            if typ == 2:
                margins[market] = balance
            elif typ == 5:
                unreal_profits[market] = balance
        equity = sum(x[2] for x in rows)
        balance = sum(x[2] for x in rows if x[1] != 5)
        available = sum(x[2] for x in rows if x[1] == 1)
        return equity, balance, available, margins, unreal_profits

    @classmethod
    def get_users_balances(cls, user_ids, slice_time):
        """获取一批用户汇总的保证金(按市场分组)，总资产(不含未实现盈亏)"""
        table = PerpetualLogDB.slice_balance_table(slice_time, interval=SLICE_INTERVAL)
        rows = table.select("market, user_id, type, balance")
        user_ids = set(user_ids)
        margins = defaultdict(Decimal)
        total = 0
        for market, user_id, typ, balance in rows:
            if market.endswith('USD'):
                continue
            if user_id not in user_ids:
                continue
            if typ == 2:
                margins[market] += balance
            total += balance
        return margins, total

    @classmethod
    def get_positions(cls, user_id, slice_time):
        table = PerpetualLogDB.slice_position_table(slice_time, interval=SLICE_INTERVAL)
        fields = ['side', 'market', 'amount', 'profit_real']
        rows = table.select(*fields, where=f"user_id={user_id}")
        return {x[1]: dict(zip(fields, x)) for x in rows}

    @classmethod
    def get_history_total_profit(cls, user_id):
        """获取自营用户所有历史仓位的盈亏，按市场分组"""
        cache = PropTradingHistoryProfitCache()
        data = cache.read()
        if not data:
            total_profits, last_id = cls._sum_history_position_profit(user_id, 0)
        else:
            data = json.loads(data)
            last_profits = {k: Decimal(v) for k, v in data['total_profits'].items()}
            last_id = data['last_id']
            delta_profits, last_id = cls._sum_history_position_profit(user_id, last_id)
            total_profits = defaultdict(Decimal)
            total_profits.update(last_profits)
            for k, v in delta_profits.items():
                total_profits[k] += v

        total_profits = {k: quantize_amount(v, 8) for k, v in total_profits.items()}
        data = {'total_profits': total_profits, 'last_id': last_id}
        cache.set(json.dumps(data, cls=JsonEncoder))
        return total_profits

    @classmethod
    def _sum_history_position_profit(cls, user_id, last_id=0):
        _db, table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'position_history')
        last = last_id
        limit = 10000
        result = defaultdict(Decimal)
        while True:
            rows = _db.table(table).select(
                "id,market,profit_real",
                where=f"user_id={user_id} and id > {last}",
                order_by="id", limit=limit)
            for _, market, profit_real in rows:
                result[market] += profit_real
            if rows:
                last = rows[-1][0]
            if len(rows) != limit:
                break
        return result, last

    @classmethod
    def get_deal_amount(cls, user_id, start, end):
        """获取自营用户成交量，按市场分组"""
        _db, table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'deal_history')
        rows = _db.table(table).select("market, amount, price", where=f"user_id={user_id} and time >= {start} and time < {end}")
        result = defaultdict(Decimal)
        for market, amount, price in rows:
            result[market] += amount * price
        return {k: quantize_amount(v, 8) for k, v in result.items()}

    @classmethod
    def get_op_users_of_deal(cls, user_id, start, end) -> dict[str, set]:
        """获取自营用户的对手盘成交用户，按市场分组"""
        _db, table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'deal_history')
        last = start
        limit = 10000
        result = {}
        while True:
            rows = _db.table(table).select("time,market,deal_user_id",
                                           where=f"user_id={user_id} and time >= {last} and time < {end}",
                                           order_by="time", limit=limit)
            for _, market, deal_user_id in rows:
                result.setdefault(market, set()).add(deal_user_id)
            if len(rows) != limit:
                break
            last = rows[-1][0]
        return result

    @classmethod
    def get_op_users_of_position_side(cls, maybe_op_users, slice_time) -> tuple[dict[str, set], dict[str, Decimal], \
                                                                                dict[str, set], dict[str, Decimal]]:
        """获取自营用户的对手盘持仓用户及持仓数量，按市场和多空方向分组"""
        table = PerpetualLogDB.slice_position_table(slice_time, interval=SLICE_INTERVAL)
        long_users = defaultdict(set)
        long_postions = defaultdict(Decimal)
        short_users = defaultdict(set)
        short_postions = defaultdict(Decimal)
        last = 0
        limit = 10000
        while True:
            rows = table.select("id,market,user_id,side,amount", where=f"id>{last}", order_by="id", limit=limit)
            for _, market, user_id, side, amount in rows:
                if market not in maybe_op_users:
                    continue
                if user_id not in maybe_op_users[market]:
                    continue
                if side == PositionSide.LONG:
                    long_users[market].add(user_id)
                    long_postions[market] += Decimal(amount)
                else:
                    short_users[market].add(user_id)
                    short_postions[market] += Decimal(amount)
            if len(rows) != limit:
                break
            last = rows[-1][0]
        return long_users, long_postions, short_users, short_postions
    
    @classmethod
    def merge_op_users_of_position(cls, long_users, long_postions, short_users, short_postions) -> tuple[dict[str, set], \
                                                                                                         dict[str, Decimal]]:
        """合并多空方向的持仓用户及持仓数量"""
        users = defaultdict(set)
        postions = defaultdict(Decimal)
        for k, v in chain(long_users.items(), short_users.items()):
            users[k].update(v)
        for k, v in chain(long_postions.items(), short_postions.items()):
            postions[k] += v
        return users, postions

    @classmethod
    def get_op_users_of_position(cls, maybe_op_users, slice_time) -> tuple[dict[str, set], dict[str, Decimal]]:
        """获取自营用户的对手盘持仓用户及持仓数量，按市场分组"""
        return cls.merge_op_users_of_position(*cls.get_op_users_of_position_side(maybe_op_users, slice_time))
    
    @classmethod
    def get_deal_amount_without_makers(cls, start: int, end: int):
        """获取全站成交量，排除做市商，按市场分组"""
        makers = list_black_users('maker')
        makers.discard(config['PROP_TRADING_USER_ID'])

        def _iter_table(table):
            last = None
            limit = 10000
            while True:
                where = f"id<{last}" if last else None
                rows = table.select("id,time,user_id,market,amount,price", where=where, order_by="id desc", limit=limit)
                for _, time_, user_id, market, amount, price in rows:
                    if time_ > end:
                        continue
                    if time_ < start:
                        return
                    if market.endswith('USD'):
                        continue
                    if user_id in makers:
                        continue
                    yield user_id, market, amount, price
                if len(rows) != limit:
                    break
                last = rows[-1][0]

        result = defaultdict(Decimal)
        for _db, table in PerpetualHistoryDB.iter_db_and_table('deal_history'):
            for _, market, amount, price in _iter_table(_db.table(table)):
                result[market] += amount * price
        return {k: quantize_amount(v, 8) for k, v in result.items()}

    @classmethod
    def get_user_trade_amount_without_makers(cls, start_date, end_date=None):
        """获取全站成交量，排除做市商，按用户-市场分组"""
        if end_date is not None and (start_date.year != end_date.year or start_date.month != end_date.month):
            raise ValueError("start_date and end_date must be in the same month")
        makers = list_black_users('maker')
        makers.discard(config['PROP_TRADING_USER_ID'])
        date_str, month_str = PerpetualSummaryDB.convert_date(start_date)
        table = PerpetualSummaryDB.table(f"user_trade_summary_{month_str}")
        if end_date is None:
            where = f"trade_date='{date_str}' and deal_volume>0"
        else:
            end_date_str = end_date.strftime('%Y-%m-%d')
            where = f"trade_date>='{date_str}' and trade_date<='{end_date_str}' and deal_volume>0"
        rows = table.select("user_id,market,sum(deal_volume)", where=where, group_by="user_id,market")
        result = {}
        for user_id, market, amount in rows:
            if market.endswith('USD'):
                continue
            if user_id in makers:
                continue
            result[(user_id, market)] = amount
        return result

    @classmethod
    def get_user_trade_amount(cls, start_date, end_date):
        """获取用户成交量"""
        start_month = date(start_date.year, start_date.month, 1)
        end_month = date(end_date.year, end_date.month, 1)
        start_date_str, _ = PerpetualSummaryDB.convert_date(start_date)
        end_date_str, _ = PerpetualSummaryDB.convert_date(end_date)
        result = defaultdict(Decimal)
        while start_month <= end_month:
            _, month_str = PerpetualSummaryDB.convert_date(start_month)
            table = PerpetualSummaryDB.table(f"user_trade_summary_{month_str}")
            rows = table.select("user_id, market, sum(deal_volume)",
                         where=f"trade_date>='{start_date_str}' and trade_date<='{end_date_str}' and deal_volume>0",
                         group_by="user_id, market")
            for user_id, market, amount in rows:
                if market.endswith('USD'):
                    continue
                if user_id < 0:
                    continue
                if user_id == config['PROP_TRADING_USER_ID']:
                    continue
                result[user_id] += amount
            start_month += relativedelta(months=1)
        return result

    @classmethod
    def iter_op_position_history(cls, prop_user_id: int, cursors: dict = None,
                                 start: float = None, ) -> tuple[list, dict]:
        """获取对手盘用户的仓位历史，返回上次的游标后面的记录。如果无游标，应指定一个开始时间，避免数据量过大"""
        if not cursors and start is None:
            raise ValueError("must specify cursors or start")
        positions = []
        new_cursors = {}
        limit = 2000
        num = re.compile(r'\d+')
        for DB, table in PerpetualHistoryDB.iter_db_and_table('position_history'):
            key = "{}.{}".format(num.search(DB.config).group(0), num.search(table).group(0))
            cursor = cursors[key] if cursors else None
            last_id = None
            end = False
            while True:
                where = f"id<{last_id}" if last_id else None
                rows = DB.table(table).select("id,position_id,market,user_id,update_time,profit_real",
                                              where=where, order_by="id desc", limit=limit)
                for row in rows:
                    if (cursor is not None and row[0] <= cursor) or (start is not None and row[4] < start):
                        end = True
                        break
                    if row[2].endswith('USD'):
                        continue
                    positions.append(row)
                if key not in new_cursors:
                    new_cursors[key] = rows[0][0] if rows else 0
                if len(rows) != limit or end:
                    break
                last_id = rows[-1][0]
        
        if not positions:
            return positions, new_cursors

        # 遍历成交历史，关联仓位和成交
        start = min(x[4] for x in positions) - 60
        deals = []
        limit = 5000
        for DB, table in PerpetualHistoryDB.iter_db_and_table('deal_history'):
            last_id = None
            while True:
                where = f"id<{last_id}" if last_id else None
                rows = DB.table(table).select("id,time,deal_user_id,position_id",
                                              where=where, order_by="id desc", limit=limit)
                for row in rows:
                    deals.append(row)
                if len(rows) != limit:
                    break
                if rows:
                    last_id = rows[-1][0]
                    if rows[-1][1] < start:
                        break

        # 对手盘用户的某个仓位要么全部与自营用户成交，要么全部不成交，这里取任意一条成交记录的deal_user_id即可
        deal_op_users = {x[3]: x[2] for x in deals}
        result = []
        for _, position_id, market, user_id, create_time, profit_real in positions:
            op_user_id = deal_op_users[position_id]
            if op_user_id != prop_user_id:
                continue
            result.append({
                'position_id': position_id,
                'create_time': create_time,
                'market': market,
                'user_id': user_id,
                'profit_real': profit_real,
            })
        return result, new_cursors
