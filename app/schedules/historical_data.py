import csv
import io
import zipfile

from flask import current_app
from app.business.redshift import PerpetualRedShiftDB, TradeRedShiftDB
from app.caches.perpetual import PerpetualMarketCache
from app.caches.spot import MarketCache
from app.common.constants import CeleryQueues, TradeIntType
from app.utils.celery_ import route_module_to_celery_queue
from app.utils.date_ import date_to_datetime, next_month, this_month, timestamp_to_date, today
from app.utils.files import AWSBucketPublic
from app.business import lock_call
from celery.schedules import crontab
from app.utils import scheduled, last_month


route_module_to_celery_queue(__name__, CeleryQueues.STATISTIC)


def _upload(all_data, headers, file_name):
    zip_buf = io.BytesIO()
    with zipfile.ZipFile(zip_buf, 'w', compression=zipfile.ZIP_DEFLATED) as zip_file:
        csv_buf = io.StringIO()
        csv_handler = csv.writer(csv_buf)
        csv_handler.writerow(headers)
        csv_handler.writerows(all_data)
        content = csv_buf.getvalue()
        zip_file.writestr(f'{file_name.split(".")[0]}.csv', content)
    zip_data = zip_buf.getvalue()
    if not AWSBucketPublic.put_file(file_name, zip_data):
        return ''
    return AWSBucketPublic.get_file_url(file_name)


@scheduled(crontab(day_of_month=1, hour=1, minute='10,50'))
@lock_call()
def upload_kline_schedule(start_timestamp=None):
    def _get_file_name(kline_type, date_, market, market_type, interval):
        if kline_type in (TradeRedShiftDB.KLINE_TYPE.INDEX_PRICE,
                          PerpetualRedShiftDB.KLINE_TYPE.INDEX_PRICE):
            file_type = 'Index'
        elif kline_type == PerpetualRedShiftDB.KLINE_TYPE.SIGN_PRICE:
            file_type = 'Mark'
        else:
            file_type = 'Kline'
        return AWSBucketPublic.new_file_key(
            key=f'{market}-{file_type}-{interval}-{market_type}-{date_.strftime("%Y-%m")}.zip')

    if not start_timestamp:
        today_ = today()
        start_date = last_month(today_.year, today_.month)
        end_date = this_month()
    else:
        start_date = timestamp_to_date(start_timestamp)
        end_date = next_month(start_date.year, start_date.month)
    start_ts = int(date_to_datetime(start_date).timestamp())
    end_ts = int(date_to_datetime(end_date).timestamp())
    kline_headers = ['timestamp', 'open', 'close', 'high', 'low']

    infos = (
        {
            'db': TradeRedShiftDB,
            'type': 'Spot',
            'markets': MarketCache.list_online_markets()
        },
        {
            'db': PerpetualRedShiftDB,
            'type': 'Perpetual',
            'markets': PerpetualMarketCache().get_market_list()
        },
    )
    for info in infos:
        markets = info['markets']
        db_ = info['db']
        kline_types = db_.KLINE_TYPE
        kline_intervals = db_.KLINE_INTERVAL
        for market in markets:
            default_file_name = _get_file_name(db_.KLINE_TYPE.DEAL_PRICE, 
                                               start_date, 
                                               market, 
                                               info['type'], db_.KLINE_INTERVAL.MINUTE.name)
            if AWSBucketPublic.check_exists(default_file_name):
                continue
            for kline_type in kline_types:
                headers = list(kline_headers)
                if kline_type in (TradeRedShiftDB.KLINE_TYPE.DEAL_PRICE, 
                                  PerpetualRedShiftDB.KLINE_TYPE.DEAL_PRICE):
                    headers += ['volume', 'deal']
                for interval in kline_intervals:
                    data = db_.get_klines(market, interval, kline_type, 
                                          start_ts, end_ts)
                    if not data:
                        continue
                    result = []
                    for item in data:
                        result.append([item[field] for field in headers])
                    file_name = _get_file_name(kline_type, start_date, market, info['type'], interval.name)
                    current_app.logger.info(f'upload {file_name}')
                    _upload(result, headers, file_name)


@scheduled(crontab(day_of_month=1, hour=1, minute='10,50'))
@lock_call()
def upload_deal_history_schedule(start_timestamp=None):
    if not start_timestamp:
        today_ = today()
        start_date = last_month(today_.year, today_.month)
        end_date = this_month()
    else:
        start_date = timestamp_to_date(start_timestamp)
        end_date = next_month(start_date.year, start_date.month)

    start_ts = int(date_to_datetime(start_date).timestamp())
    end_ts = int(date_to_datetime(end_date).timestamp())
    
    headers = ['time', 'deal_id', 'price', 'amount', 'side']
    infos = (
        {
            'db': TradeRedShiftDB,
            'type': 'Spot',
            'markets': MarketCache.list_online_markets()
        },
        {
            'db': PerpetualRedShiftDB,
            'type': 'Perpetual',
            'markets': PerpetualMarketCache().get_market_list()
        },
    )
    
    for info in infos:
        db_ = info['db']
        markets = info['markets']
        for market in markets:
            file_name = AWSBucketPublic.new_file_key(
                key=f'{market}-Trades-{info["type"]}-{start_date.strftime("%Y-%m")}.zip'
            )
            if AWSBucketPublic.check_exists(file_name):
                continue
            data = db_.get_trade_history(market, start_ts, end_ts)
            if not data:
                continue
            result = []
            for item in data:
                if item['ask_role'] == TradeIntType.TAKER:
                    item['side'] = 'buy'
                else:
                    item['side'] = 'sell'
                result.append([item[field] for field in headers])
            current_app.logger.info(f'upload {file_name}')
            _upload(result, headers, file_name)
