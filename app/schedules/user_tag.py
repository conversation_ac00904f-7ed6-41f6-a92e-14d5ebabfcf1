# -*- coding: utf-8 -*-
import json
import time
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from functools import partial
from typing import List

import pymysql
from celery.schedules import crontab
from flask import current_app

from .. import config, Language
from ..business import (
    lock_call, CacheLock, LockKeys, Locked, ExchangeLogDB, PriceManager, TradeLogDB, PerpetualLogDB,
    BusinessSettings, BalanceManager, yield_query_records_by_time_range,
)
from ..business.alert import send_alert_notice
from ..business.amm import get_all_user_amm_assets
from ..business.margin.helper import MarginHelper
from ..business.market_maker import MarketMakerHelper
from ..business.pledge.helper import group_pledge_unflat_debt_interest
from ..business.user import UserRepository
from ..business.user_tag import (
    get_all_handler_names, get_handler_by_name, get_supported_tags, get_default_priority,
    get_handler_priority_mapping,
)
from ..business.user_tag.helper import check_user_tag_finished
from ..business.user_tag.reader import TagReader
from ..caches.user_tag import NeedUpdateMaxPositionUsersCache, UserMaxPositionUpdatedFinishedCache
from ..common import CeleryQueues, PrecisionEnum
from ..exceptions import InvalidArgument
from ..models import db, User, CreditBalance, SubAccount, Deposit
from ..models.user_tag import UserTagGroup, UserTagInfo, UserTag, UserTagGroupPortrait
from ..utils import (
    route_module_to_celery_queue, scheduled, celery_task, now, batch_iter, current_timestamp, quantize_amount,
    today_timestamp_utc, amount_to_str, today,
)
from ..utils.export import export_xlsx_with_sheet

route_module_to_celery_queue(__name__, CeleryQueues.USER_TAG)


@scheduled(crontab(minute='30-58/4', hour='2-5'))
@lock_call()
def update_user_tag_schedule():
    names = get_all_handler_names()
    report_date_str = datetime.utcnow().date().strftime('%Y-%m-%d')
    for name in names:
        update_tag_data_by_handler_name.delay(name, report_date_str)


@celery_task
def update_tag_data_by_handler_name(name, report_date_str):
    report_date = datetime.strptime(report_date_str, "%Y-%m-%d").date()
    handler = get_handler_by_name(name)
    if hasattr(handler, "tag_relations") and handler.tag_relations:
        if not handler().check_relations_finished(report_date):
            current_app.logger.warning(f"handler {name} relations not finished")
            return
    if handler().check_finished(report_date):
        if hasattr(handler, "impl_tags") and handler.impl_tags:
            current_app.logger.info(f"tag handler {name} has finished")
        if hasattr(handler, "impl_requires") and handler.impl_requires:
            current_app.logger.info(f"require handler {name} has finished")
        return
    handler_priority_mapping = get_handler_priority_mapping()
    priority_handlers = [
        (_pri, _name) for _name, _pri in handler_priority_mapping.items()
    ]
    high_priority_handlers = [
        _name
        for (_pri, _name) in priority_handlers
        if _pri != get_default_priority()
    ]
    high = False
    concurrency = 6
    if name in high_priority_handlers:
        high = True
        concurrency = 2

    for index in range(concurrency):
        try:
            with CacheLock(LockKeys.running_user_tag(report_date_str, index, high),
                           ttl=3600 * 4
                           ), \
                    CacheLock(LockKeys.running_user_tag_handler(name),
                              ttl=3600 * 4
                              ):
                db.session.rollback()
                start_ts = current_timestamp(to_int=True)
                handler().save(report_date)
                end_ts = current_timestamp(to_int=True)
                current_app.logger.warning(
                    f"user_tag:{name}:finished, used: {end_ts - start_ts}s"
                )
                return
        except Locked:
            continue
    current_app.logger.info(f"user tag {name} running {concurrency} count is full")


@celery_task
@lock_call(with_args=True)
def user_tag_group_statistic(id_: int):
    """
    rules: [
        [
            {tag: FAVORITE_ASSETS, op: EQ, values: [CET]},
            {tag: CET_DISCOUNT, op: EQ, values: [True]},
        ],
        [
            {tag: LANGUAGE, op: IN, values: [EN_US, FR_FR]},
        ]
    ]
    """
    model = UserTagGroup
    row = model.query.get(id_)
    if not row or row.status is model.Status.DELETED:
        return

    rules = row.get_rules()
    parsed_rules = TagReader.parse_rules(rules)

    try:
        user_ids = TagReader.aggregate(groups=parsed_rules)
    except Exception:
        row.calc_status = model.CalcStatus.FAILED
        row.last_updated_at = now()
        raise
    else:
        row.set_user_ids(list(user_ids))
        row.user_count = len(user_ids)
        row.last_updated_at = now()
        row.calc_status = model.CalcStatus.FINISHED
    db.session.commit()
    return row


@scheduled(crontab(minute=15, hour='15-16'))
@lock_call()
def check_user_tag_finished_schedules():
    from app.business.user_tag.handlers.base import BaseHandlerMeta
    if not config.get('MONITOR_ENABLED'):
        return
    report_date = datetime.utcnow().date()
    not_finished_handlers = {
        BaseHandlerMeta.get_tag_handler(v)
        for v in check_user_tag_finished(get_supported_tags(), report_date)[1]}
    if not_finished_handlers:
        names = [handler.__name__ for handler in not_finished_handlers]
        send_text = f"未完成的Handlers有:\n{','.join(names)}\n"
        send_alert_notice(send_text, config["ADMIN_CONTACTS"]["web_notice"])


@scheduled(crontab(minute="*/10", hour="13-15"))
@lock_call()
def retry_user_tag_schedules():
    unfinished_tag_count = len(check_user_tag_finished(get_supported_tags(), now().date())[1])
    if unfinished_tag_count > 0:
        update_user_tag_schedule()


class GroupPortraitDataMixin:
    model = UserTagGroupPortrait

    @classmethod
    def _get_row(cls, id_):
        model = cls.model
        row = model.query.get(id_)
        if not row:
            raise InvalidArgument
        if row.status is model.Status.DELETED:
            raise InvalidArgument(message=f'{id_}已被删除')
        return row

    @classmethod
    def get_tags(cls, tag_ids):
        model = UserTagInfo
        rows = model.query.filter(
            model.id.in_(tag_ids),
            model.status == model.Status.PASSED
        ).all()
        if not rows:
            raise InvalidArgument(message='用户标签不存在(可能已被删除)')
        return {row.id: row.tag for row in rows}

    @classmethod
    def _get_tag_group_users(cls, ids):
        model = UserTagGroup
        rows = model.query.filter(
            model.status == model.Status.PASSED,
            model.id.in_(ids)
        ).all()
        if not rows:
            raise InvalidArgument(message='用户分群不存在(可能已被删除)')
        user_ids = set()
        for row in rows:
            if row.calc_status is not model.CalcStatus.FINISHED:
                raise InvalidArgument(message=f'用户分群({row.id}), 还未统计出用户数')
            user_ids |= set(row.get_user_ids())
        return list(user_ids)

    @classmethod
    def _get_ret(cls, user_ids, user_data, tags: List[UserTag]):
        ret = []
        for user_id, tag_items in user_data.items():
            info = dict(
                id=user_id,
            )
            item_tags = set()
            for tag_item in tag_items:
                tag = tag_item['tag']
                item_tags.add(tag)

                tag_prop = TagReader.get_tag_property(tag)
                value = tag_prop.get_display_option_value(tag_item['value'])
                info.update({tag.value: value})

            miss_tags = set(tags) - item_tags
            for miss_tag in miss_tags:
                info.update({miss_tag.value: ''})
            ret.append(info)

        # 对应画像的用户中，可能没有标签数据，需补足
        for miss_user in (set(user_ids) - set(user_data.keys())):
            _info = dict(id=miss_user)
            for tag in tags:
                _info.update({tag.value: ''})
            ret.append(_info)

        return ret


@celery_task
@lock_call(with_args=True)
def send_group_portrait_to_research_team(id_: int, export_user_id: int, include_email: bool):
    cls = GroupPortraitDataMixin
    row = cls._get_row(id_)
    user = User.query.get(export_user_id)
    if not user:
        return
    tag_ids = row.get_tag_ids()
    tags = list(cls.get_tags(tag_ids).values())
    if not include_email:
        tags = [t for t in tags if t != UserTag.USER_EMAIL]
    elif include_email and UserTag.USER_EMAIL not in tags:
        tags.append(UserTag.USER_EMAIL)
    tag_names = [tag.name for tag in tags]
    tag_group_ids = row.get_tag_group_ids()
    user_ids = cls._get_tag_group_users(tag_group_ids)
    user_data = TagReader.get_user_tag_data_by(tag_names, user_ids)
    data_list = cls._get_ret(user_ids, user_data, tags)

    header_mapping = {'id': 'ID'}
    for tag_name in [tag.value for tag in tags]:
        header_mapping.update({tag_name: tag_name})

    sheet_datas = {}
    i = 0
    for chunk_data in batch_iter(data_list, 250000):
        sheet_datas.update({
            f'sheet{i}': {
                'header_mapper': header_mapping,
                'data': chunk_data
            }
        })
        i += 1
    file_url = export_xlsx_with_sheet(
        sheet_data=sheet_datas
    )
    email_content = f'''群体画像数据 excel 下载链接：
    {file_url}'''
    from app.business.email import send_internal_user_email
    send_internal_user_email.delay(
        email=user.email,
        email_content=email_content,
        subject=f'群体画像数据-{row.name}',
        lang=Language.ZH_HANS_CN.value
    )


def sum_all_user_current_balance_data() -> dict:
    prices = PriceManager.assets_to_usd()
    quantize = partial(quantize_amount, decimals=PrecisionEnum.COIN_PLACES)
    timestamp = today_timestamp_utc()
    sub_main_dic = UserRepository.get_sub_main_acc_dic()
    user_asset_data = defaultdict(lambda: defaultdict(Decimal))
    margin_due_assets = MarginHelper.group_margin_loan_order()
    unflat_debt_interests = group_pledge_unflat_debt_interest()

    for key, value in unflat_debt_interests.items():
        # 质押
        user_id, account_id, loan_asset = key
        debt_amount, interest_amount = value
        total_amount = debt_amount + interest_amount
        main_user_id = sub_main_dic.get(user_id, user_id)
        user_asset_data[main_user_id][loan_asset] = -quantize(total_amount)
    for key, amount in margin_due_assets.items():
        # 杠杆
        account_id, user_id, asset = key
        main_user_id = sub_main_dic.get(user_id, user_id)
        user_asset_data[main_user_id][asset] = -quantize(amount)

    credit_query = CreditBalance.query.with_entities(
        CreditBalance.user_id,
        CreditBalance.asset,
        CreditBalance.unflat_amount
    ).all()
    for credit_balance in credit_query:
        # 授信
        main_user_id = sub_main_dic.get(credit_balance.user_id, credit_balance.user_id)
        user_asset_data[main_user_id][credit_balance.asset] = -quantize(credit_balance.unflat_amount)

    def get_user_amm_assets():
        retry_time = 5
        for i in range(retry_time):
            try:
                return get_all_user_amm_assets()
            except Exception:
                pass
        raise RuntimeError("cannot get amm assets")

    amm_data = get_user_amm_assets()

    for (user_id,
         asset,
         account,
         balance) in TradeLogDB.get_user_balances(timestamp):
        # 现货+杠杆+质押+理财
        main_user_id = sub_main_dic.get(user_id, user_id)
        user_asset_data[main_user_id][asset] += quantize(balance)

    for user_id, _detail in amm_data.items():
        # AMM
        main_user_id = sub_main_dic.get(user_id, user_id)
        for _asset, _amount in _detail.items():
            user_asset_data[main_user_id][_asset] += quantize(_amount)

    for (user_id, asset, balance) in PerpetualLogDB.get_user_balances(timestamp):
        # 合约
        main_user_id = sub_main_dic.get(user_id, user_id)
        user_asset_data[main_user_id][asset] += quantize(balance)

    def get_tmp_assets() -> list[str]:
        raw_str = BusinessSettings.tmp_assets_config
        tmp_assets_config = json.loads(raw_str)
        current_ts = current_timestamp(to_int=True)
        assets = []
        for (__asset, start_ts, end_ts) in tmp_assets_config:
            if start_ts < current_ts < end_ts:
                assets.append(__asset)
        return assets

    tmp_assets = get_tmp_assets()

    def sum_to_usd(asset_dict: dict[str, Decimal]) -> Decimal:
        __usd = Decimal()
        for u_asset, u_amount in asset_dict.items():
            # todo 更名币种忽略,后续有好的解决方案再处理
            if u_asset in tmp_assets:
                continue
            __usd += prices.get(u_asset, Decimal()) * u_amount
        return quantize_amount(__usd, PrecisionEnum.CASH_PLACES)

    user_usd_data = defaultdict(Decimal)
    for _uid, _asset_dict in user_asset_data.items():
        # 系统用户
        if _uid == 0:
            continue
        _usd = sum_to_usd(_asset_dict)
        if _usd < Decimal('1'):
            # 小于某个阈值不更新
            continue
        user_usd_data[_uid] = sum_to_usd(_asset_dict)
    return user_usd_data


def batch_update(data: list[tuple[int, Decimal]], batch_size: int = 5000):
    """
    分批更新数据库，处理大字典。
    Args:
        table: 表
        data: 列表，(user_id，max_usd)
        batch_size: 每批处理的记录数，默认为 5000
    """
    table = ExchangeLogDB.user_max_position_table()
    table_name = table.name
    # 构造 SQL 模板

    sql_template = f"""
    INSERT INTO {table_name} (user_id, max_usd, update_ts)
    VALUES {{}}
    ON DUPLICATE KEY UPDATE
        max_usd = VALUES(max_usd),
        update_ts = UNIX_TIMESTAMP()
    """

    # 分批处理
    total_rows = len(data)
    for start in range(0, total_rows, batch_size):
        batch = data[start:start + batch_size]
        placeholders = ', '.join(['(%s, %s, UNIX_TIMESTAMP())'] * len(batch))
        flat_values = [item for sublist in batch for item in sublist]
        sql = sql_template.format(placeholders)

        # 重试机制处理死锁
        for attempt in range(3):
            try:
                table.execute(sql, flat_values)
                current_app.logger.info(f"Batch {start // batch_size + 1} processed")
                break
            except pymysql.err.OperationalError as e:
                current_app.logger.warning(f"Batch {start // batch_size + 1} error, error sample data {batch[:10]}, error {e!r}")
                time.sleep(0.1 * (attempt + 1))
                continue


def single_update(data: tuple[int, str]):
    table = ExchangeLogDB.user_max_position_table()
    table_name = table.name
    sql_template = f"""
        INSERT INTO {table_name} (user_id, max_usd, update_ts)
        VALUES {{}}
        ON DUPLICATE KEY UPDATE
            update_ts = IF(VALUES(max_usd) > max_usd, UNIX_TIMESTAMP(), update_ts),
            max_usd = GREATEST(VALUES(max_usd), max_usd);
    """
    placeholders = ', '.join(['(%s, %s, UNIX_TIMESTAMP())'])
    sql = sql_template.format(placeholders)

    # 重试机制处理死锁
    for attempt in range(3):
        try:
            table.execute(sql, data)
            break
        except pymysql.err.OperationalError as e:
            current_app.logger.warning(f"update {data} error, error {e!r}")
            time.sleep(0.1 * (attempt + 1))
            continue


def update_table_data(new_data: dict[int, Decimal], old_data: dict[int, Decimal]):
    need_update_dict = defaultdict(Decimal)
    for _uid, _usd in new_data.items():
        if _uid not in old_data:
            need_update_dict[_uid] = _usd
            continue
        if old_data[_uid] < _usd:
            need_update_dict[_uid] = _usd
    need_update_data = sorted(need_update_dict.items(), key=lambda x: -x[0])
    batch_update(need_update_data)


@celery_task
@lock_call(with_args=True)
def update_single_user_max_balance(user_id: int):
    mm_main_user_ids = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
    main_user_id = User.query.get(user_id).main_user_id
    if main_user_id in mm_main_user_ids:
        return
    price_map = PriceManager.assets_to_usd()
    sub_user_ids = [i.user_id for i in SubAccount.query.filter(
        SubAccount.main_user_id == user_id, SubAccount.status == SubAccount.Status.VALID).all()]
    bm = BalanceManager(user_id, sub_user_ids, price_map)
    current_balance_usd = bm.get_current_balance_usd()
    single_update((main_user_id, amount_to_str(current_balance_usd)))


@scheduled(crontab(minute='*/3'))
@lock_call()
def auto_update_deposit_user_max_balance_schedule():
    _today = today()
    ts = current_timestamp(to_int=True)
    end_dt = now()
    start_dt = now() - timedelta(hours=1.5)
    query = yield_query_records_by_time_range(
        Deposit,
        start_time=start_dt,
        end_time=end_dt,
        select_fields=(Deposit.id, Deposit.user_id, Deposit.status),
        limit=500
    )
    finish_cache = UserMaxPositionUpdatedFinishedCache(_today)
    if not finish_cache.exists():
        # 主要防止更新过程冲突
        current_app.logger.warning(f"update_user_max_balance_usd_schedule 定时任务执行中,自动监听任务暂停")
        return

    cache = NeedUpdateMaxPositionUsersCache(_today)
    for deposit in query:
        if deposit.status not in (
                Deposit.Status.CONFIRMING,
                Deposit.Status.TO_HOT,
                Deposit.Status.FINISHED
        ):
            continue
        user_id = deposit.user_id
        if ts - cache.get_user_last_update_ts(user_id) < 180:
            # 这里加个判断减少重复更新逻辑
            continue
        if cache.check_and_add(user_id, deposit.id):
            update_single_user_max_balance.delay(deposit.user_id)


@scheduled(crontab(minute='10', hour='0,1'))
@lock_call()
def update_user_max_balance_usd_schedule():
    _today = today()
    cache = UserMaxPositionUpdatedFinishedCache(_today)
    if cache.exists():
        current_app.logger.info(f'update_user_max_balance_usd_schedule {_today} finished')
        return
    table = ExchangeLogDB.user_max_position_table()
    if not table.exists():
        current_app.logger.warning(f'{table.name} is not exists, stop schedule')
        return
    if table.count() == 0:
        current_app.logger.warning(f'{table.name} is running task, stop schedule')
        return

    user_usd_data = sum_all_user_current_balance_data()
    table_data = ExchangeLogDB.get_user_max_balance_table_data()
    update_table_data(user_usd_data, table_data)
    cache.set_finished()
