#!/usr/bin/env python3
import datetime
from collections import defaultdict
from decimal import Decimal

from celery.schedules import crontab

from ..business import lock_call, ServerClient
from ..business.clients.biz_monitor import biz_monitor
from ..business.push import (
    send_user_asset_notice_push,
    send_popular_market_level_broadcast,
    send_new_asset_price_rise_notice,
)
from ..business.push_base import BroadcastBusinessHandler, PushLimiterBase
from ..caches.prices import AssetRealTimeRateCache
from ..caches.push import MarketPriceNoticeCache, MarketRateLevelNoticeCache, AssetUserHoldCache, \
    AssetUserFavoriteCache, \
    NewAssetPriceRiseNoticeCache
from ..common import CeleryQueues, PrecisionEnum, AppPushEvent
from ..common.push import AppPushBusiness, AppBroadcastBusiness
from ..models import Market, MarketPriceNotice, CoinInformation, AppPushBusinessHistory, db
from ..utils import route_module_to_celery_queue, scheduled, amount_to_str, current_timestamp, now, format_percent
from ..utils.amount import calculate_ratio

route_module_to_celery_queue(__name__, CeleryQueues.PRICE_NOTICE)


def get_market_level(market_data, last_level: Decimal, new_price: Decimal) -> Decimal:
    """返回值为Decimal()时表明当前价格计算出来的档位与上次相同，或者超出价格区间"""
    new_price_step = get_step_by_price(new_price)
    last_level_step = get_step_by_price(last_level)
    step = min(new_price_step, last_level_step)
    new_level = new_price // step * step
    if new_level < market_data['start'] or new_level > market_data['end']:
        return Decimal()
    if not last_level:
        return new_level
    if last_level - step <= new_price <= last_level + step:   # 如果最新价格的波动区间没有超出 +- step，则无需推送
        return Decimal()
    if new_level < last_level:  # 新计算出来的档位低于原来推送的档位，则是从较高的位置下跌下来的，因此要 + step
        return new_level + step
    else:
        return new_level  # 新计算出来的档位低高于原来推送的档位，则是从较低的位置涨上去的，无需 + step


def get_step_by_price(price: Decimal):
    """
    使用价格的第二位有效数字所在位置计算步长
    66024.04 -> 1000
    3372 -> 100
    """
    p = price.as_tuple()
    exponent = p.exponent
    decimals = len(p.digits) + exponent - 1 - 1
    return 10 ** decimals


@scheduled(crontab(minute='*/1'))
@lock_call()
def market_price_up_down_alert_schedule():
    """
    热门市场-价格档位广播推送
    只有当前价格计算出来的档位与上次推送档位不同时（无论当前价格相对于上次推送档位是涨还是跌），才需要推送
    """
    base_asset_market_map = dict(BTC='BTCUSDT', ETH='ETHUSDT')
    market_depth_map = dict(
        BTCUSDT=dict(start=5000, end=200000),
        ETHUSDT=dict(start=500, end=10000),
    )

    mobile_ttl = 300
    client = ServerClient()

    for asset, market in base_asset_market_map.items():
        change_rate = AssetRealTimeRateCache().get_asset_real_time_rate(asset)
        if not change_rate:
            continue
        latest_price = client.market_last(market)
        cache = MarketRateLevelNoticeCache(market)
        last_level = cache.get_level()  # 上次推送时的价格档位
        level = get_market_level(market_depth_map[market], last_level, latest_price)
        if not level:   # 最新价格还是围绕上次推送档位变化，则无需推送
            continue
        if not last_level:  # 如果此前没有缓存价格突破推送，直接设置缓存，做为初始化
            set_market_level_changed(cache, str(level))
            continue
        if not market_level_changed(cache, str(level)):
            continue
        if popular_market_business_can_push():
            level_str = amount_to_str(level)
            latest_price_str = amount_to_str(latest_price, PrecisionEnum.PRICE_PLACES)
            if latest_price >= level:   # 最新价格高于计算出来的档位，此时是上涨趋势
                direction = MarketPriceNotice.Direction.RISE.name
            else:   # 最新价格低于计算出来的档位，此时是下跌趋势
                direction = MarketPriceNotice.Direction.FALL.name
            send_popular_market_level_broadcast.delay(asset, direction,
                                                      latest_price_str,
                                                      market,
                                                      level_str,
                                                      mobile_ttl,
                                                      extra=dict(
                                                        asset=asset,
                                                        market=market,
                                                        type=direction,
                                                        threshold=level_str
                                                      ),
                                                      created_at=current_timestamp(to_int=True))
            set_popular_market_business_pushed()
        set_market_level_changed(cache, str(level))


def market_level_changed(business_cache: MarketRateLevelNoticeCache, level):
    return business_cache.can_push(level)


def popular_market_business_can_push():
    business = AppBroadcastBusiness.PoplarMarketLevelBreakThrough
    handler = BroadcastBusinessHandler(business)
    return handler.can_push()


def set_popular_market_business_pushed():
    business = AppBroadcastBusiness.PoplarMarketLevelBreakThrough
    handler = BroadcastBusinessHandler(business)
    handler.set_pushed()


def set_market_level_changed(business_cache: MarketRateLevelNoticeCache, level):
    business_cache.set_pushed(level)


@scheduled(crontab(hour='*/1', minute='19'))
def new_asset_price_rise_notice_schedule():
    """新币上涨"""
    mobile_ttl = 600
    new_asset_up_threshold = Decimal('0.5')  # 新币达到上涨幅度推送阈值
    now_ = now()
    query_start = now_ - datetime.timedelta(days=1)
    records = CoinInformation.query.filter(
        CoinInformation.online_time >= query_start,
        CoinInformation.online_time <= now_
    ).with_entities(
        CoinInformation.code
    ).all()
    assets = [i.code for i in records]
    if not assets:
        return
    change_rate_dict = AssetRealTimeRateCache().get_asset_real_time_rates(assets)
    to_send_assets = [asset for asset, change_rate in change_rate_dict.items() if change_rate >= new_asset_up_threshold]
    extra = {'threshold': str(new_asset_up_threshold)}
    created_at = current_timestamp(to_int=True)
    for asset in to_send_assets:
        change_rate = change_rate_dict[asset]
        market = f'{asset}USDT'
        cache = NewAssetPriceRiseNoticeCache(asset)
        if new_asset_rise_can_push(cache):
            send_new_asset_price_rise_notice.delay(asset, market, format_percent(change_rate),
                                                   mobile_ttl, extra, created_at)
            set_new_asset_rise_pushed(cache)


def new_asset_rise_can_push(business_cache):
    business = AppBroadcastBusiness.NewAssetPriceRise
    handler = BroadcastBusinessHandler(business)
    if not handler.can_push():
        return False
    if not business_cache.can_push():
        return False
    return True


def set_new_asset_rise_pushed(business_cache):
    business = AppBroadcastBusiness.NewAssetPriceRise
    handler = BroadcastBusinessHandler(business)
    handler.set_pushed()
    business_cache.set_pushed()


def get_notice_market_user_map(market_list):
    market_notice_dict = defaultdict(set)
    # sql:3s
    market_price_notice_query = MarketPriceNotice.query.filter(
        MarketPriceNotice.market.in_(market_list),
        MarketPriceNotice.state == MarketPriceNotice.State.OPEN,
        MarketPriceNotice.status == MarketPriceNotice.Status.VALID,
        MarketPriceNotice.trade_type == MarketPriceNotice.TradeType.SPOT,
    ).all()

    for market_notice in market_price_notice_query:
        market_notice_dict[market_notice.market].add(market_notice.user_id)
    return market_notice_dict


@scheduled(crontab(minute='*/1'))
@lock_call()
def user_asset_price_notice():
    """用户持仓、自选币种推送"""
    market_list = Market.query.filter(Market.status == Market.Status.ONLINE,
                                      Market.quote_asset == Market.TradingArea.USDT.value).all()
    level_list = [10, 20, 50, 100, 200]
    min_level = level_list[0]
    interval = 86400
    client = ServerClient()
    now_ = int(current_timestamp())
    start_time = now_ - now_ % interval
    end_time = start_time + interval
    push_list = []
    for row in market_list:
        market = row.name
        asset = row.base_asset
        klines = client.market_kline(
            market=market,
            start_time=start_time,
            end_time=end_time,
            interval=interval,
        )   # 查询日线级别的当日k线
        if not klines:
            continue
        open_price, last_price = Decimal(klines[0][1]), Decimal(klines[0][2])
        change_rate = calculate_ratio(open_price, last_price)
        rate_level = abs(change_rate * 100)
        if rate_level < min_level:
            continue

        level = 0
        for _level in level_list:
            if rate_level >= _level:
                level = _level
            else:
                break
        if change_rate >= 0:
            change_type = MarketPriceNotice.Direction.RISE
        else:
            change_type = MarketPriceNotice.Direction.FALL
        cache = MarketPriceNoticeCache(market, change_type.name)
        if not cache.can_push(level):
            continue
        cache.set_pushed(str(level))

        price_str = amount_to_str(last_price, PrecisionEnum.PRICE_PLACES)
        change_rate_percent = amount_to_str(abs(change_rate) * 100, 2)

        push_list.append(dict(
            market=market,
            asset=asset,
            direction=change_type.name,
            price_str=price_str,
            change_rate_percent=change_rate_percent,
            level=level
        ))

    if not push_list:
        return

    asset_list = {i['asset'] for i in push_list}
    biz_monitor.increase_uniq_counter(AppPushEvent.PUSH_ASSET_NOTICE_CUSTOM_NUM,
                                      asset_list)

    for market_data in push_list:
        asset = market_data['asset']
        market = market_data['market']
        direction = market_data['direction']
        price_str = market_data['price_str']
        change_rate_percent = market_data['change_rate_percent']
        asset_user_fav_map = AssetUserFavoriteCache().read()
        notice_fav_user_ids = asset_user_fav_map.get(asset, set())
        send_user_asset_notice_push.delay(AppPushBusiness.UserFavoriteAssetPrice.name, list(notice_fav_user_ids), asset,
                                          market, direction, price_str, change_rate_percent,
                                          current_timestamp(to_int=True))

        asset_user_hold_map = AssetUserHoldCache().read()
        hold_user_ids = asset_user_hold_map.get(asset, set())
        notice_hold_user_ids = hold_user_ids - notice_fav_user_ids
        send_user_asset_notice_push.delay(
            AppPushBusiness.UserHoldAssetPrice.name, list(notice_hold_user_ids), asset,
            market, direction, price_str, change_rate_percent, current_timestamp(to_int=True))


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_asset_user_hold_fav_cache():
    AssetUserHoldCache().reload()
    AssetUserFavoriteCache().reload()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_push_limit():
    """更新用户push限制"""
    for limiter in PushLimiterBase.__subclasses__():
        limiter.reload()


@scheduled(crontab(hour='9', minute='27'))
@lock_call()
def clear_expire_push_records():
    """清除过期的push发送记录"""
    deadline_delta = 2
    now_ = int(now().timestamp())
    start = now_ - deadline_delta * 86400
    AppPushBusinessHistory.query.filter(
        AppPushBusinessHistory.expire_at <= start
    ).delete(synchronize_session=False)
    db.session.commit()
