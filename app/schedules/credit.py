# -*- coding: utf-8 -*-
from decimal import Decimal
from celery.schedules import crontab
from flask import current_app

from ..common import CeleryQueues
from ..models import db, CreditBalance, CreditUser
from ..business import <PERSON><PERSON><PERSON>ock, Lock<PERSON><PERSON>s, lock_call
from ..business.credit import <PERSON>Interest<PERSON>el<PERSON>, update_credit_user_risk_record_task
from ..utils import (
    scheduled,
    route_module_to_celery_queue,
    now,
    quantize_amount,
    celery_task,
)
from ..utils.date_ import convert_datetime, current_datetime


route_module_to_celery_queue(__name__, queue=CeleryQueues.CREDIT)


@celery_task
@lock_call(with_args=["user_id"])
def update_user_credit_interest(user_id):
    user_credit_balance_records = CreditBalance.query.filter(
        CreditBalance.user_id == user_id
    ).all()
    for record in user_credit_balance_records:
        with CacheLock(
            LockKeys.credit_update_balance(record.user_id, record.asset),
            wait=False,
        ):
            db.session.rollback()  # re-query first row
            day_rate = CreditInterestHelper.get_asset_day_rate(
                asset=record.asset, user_id=record.user_id
            )
            if not day_rate:
                current_app.logger.warning(f"update_user_credit_interest empty_day_rate {record.user_id} {record.asset}")
                day_rate = Decimal()

            # 计算授信记录的增量利息
            _now = now()
            if record.interest_at is not None:
                current_hour_dt = current_datetime("hour")
                interest_hour_dt = convert_datetime(record.interest_at, "hour")
                delta = current_hour_dt - interest_hour_dt
                total_seconds = delta.total_seconds()
                delta_hour = (
                    total_seconds // 3600 if total_seconds > 0 else Decimal()
                )
                hour = Decimal(delta_hour)
            else:
                # 历史授信记录-首次计息
                hour = Decimal("1")
            interest_delta_amount = quantize_amount(
                (hour * record.unflat_amount * day_rate) / Decimal("24"), 8
            )

            if interest_delta_amount != Decimal():
                record.interest_amount += interest_delta_amount
                CreditInterestHelper.insert_interest_receivable_history(
                    record, interest_delta_amount, day_rate
                )
            else:
                current_app.logger.warning(f"update_user_credit_interest zero {record.user_id} {record.asset} {interest_delta_amount}")
            record.interest_at = _now
            db.session.commit()


@scheduled(crontab(hour="*/1", minute=0))
@lock_call()
def update_credit_interest_schedule():
    credit_users = CreditBalance.query.filter(
        CreditBalance.unflat_amount > 0
    ).with_entities(
        CreditBalance.user_id.distinct()
    )
    user_ids = {x for x, in credit_users}
    for user_id in user_ids:
        update_user_credit_interest.delay(user_id)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_credit_risk_schedule():
    credit_user_query = CreditUser.query.filter(
            CreditUser.status == CreditUser.StatusType.PASS
            )
    for record in credit_user_query:
        update_credit_user_risk_record_task.delay(record.user_id)
