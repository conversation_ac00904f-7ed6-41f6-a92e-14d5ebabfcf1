import time
from decimal import Decimal
from collections import defaultdict
from itertools import chain
from datetime import timedelta

from flask import current_app
from celery.schedules import crontab

from app.common import BalanceBusiness, CeleryQueues
from app.business.perpetual import perpetual_transfer_out
from app.business.external_dbs import PerpetualLogDB, PerpetualHistoryDB, TradeLogDB, TradeHistoryDB
from app.business import PriceManager, PerpetualServerClient, lock_call, ServerClient
from app.business.utils import yield_query_records_by_time_range
from app.business.fee_constant import DEFAULT_MIN_CONTRACT_TAKER_FEE, DEFAULT_MIN_CONTRACT_MAKER_FEE
from app.business import CacheLock, LockKeys
from app.business.user import filter_active_users
from app.models.amm import UserLiquidity
from app.models.user import CleanedBalanceTransferHistory
from app.models import BalanceUpdateBusiness, SubAccount, MarginLoanOrder, db
from app.caches.perpetual import PerpetualMarketCache
from app.utils import (scheduled, amount_to_str, quantize_amount, batch_iter, 
                       today_timestamp_utc, timestamp_to_date, now)
from app.utils.celery_ import route_module_to_celery_queue


route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


class PerpetualPositionCleaner:
    """平掉较早开仓且用户最近未活跃的小额合约仓位"""

    def __init__(self):
        self.min_position_value = Decimal(100)
        self.start_time = int(time.time()) - 86400 * 180
        self.prices = PriceManager.assets_to_usd()
        self.prices["USD"] = Decimal(1)
        self._amount_assets = {}

    def get_amount_asset(self, market):
        if not (v := self._amount_assets.get(market)):
            self._amount_assets[market] = v = PerpetualMarketCache.get_amount_asset(market)
        return v

    def iter_positions(self):
        table = PerpetualLogDB.slice_position_table()
        last = 0
        fields = ("id", "create_time", "user_id", "market", "amount")
        while True:
            rows = table.select(*fields, where=f"id > {last}", order_by="id", limit=10000)
            if not rows:
                break
            for row in rows:
                yield dict(zip(fields, row))
            last = rows[-1][0]

    def should_clean_position(self, position) -> bool:
        if position['create_time'] > self.start_time:
            return False
        asset = self.get_amount_asset(position['market'])
        usd = self.prices[asset] * position['amount']
        return usd < self.min_position_value

    def is_perpetual_active(self, user_id: int) -> bool:
        # 1. 没有主动交易记录
        # target: 1-开仓, 2-加仓, 3-减仓, 4-平仓
        db_, table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'order_history')
        table = db_.table(table)
        rows = table.select(
            'create_time',
            where=f'user_id={user_id} and create_time > {self.start_time} and target in (1,2,3,4)',
            order_by='create_time desc', limit=1)
        if len(rows) > 0:
            return True
        # 2. 没有保证金变动
        # #type: 1-增加保证金, 2-减少保证金, 3-调整杠杆, 4-调整仓位类型(逐仓/全仓)
        db_, table = PerpetualHistoryDB.user_to_db_and_table(user_id, 'position_margin_history')
        table = db_.table(table)
        rows = table.select(
            'time',
            where=f'user_id={user_id} and time > {self.start_time} and `type` in (1,2,3,4)',
            order_by='time desc', limit=1)
        if len(rows) > 0:
            return True
        return False

    def get_need_clean_positions(self):
        postions = []
        for pos in self.iter_positions():
            if not self.should_clean_position(pos):
                continue
            user_id = pos['user_id']
            if self.is_perpetual_active(user_id):
                continue
            postions.append(pos)
        return postions

    def clean_positions(self):
        postions = self.get_need_clean_positions()
        current_app.logger.warning(f"clean {len(postions)} perpetual positions")
        client = PerpetualServerClient()
        taker_fee = max(DEFAULT_MIN_CONTRACT_TAKER_FEE, 0)
        maker_fee = max(DEFAULT_MIN_CONTRACT_MAKER_FEE, 0)
        for pos in postions:
            current_app.logger.warning(f"close position {pos['id']} of user {pos['user_id']} in market {pos['market']}")
            client.market_close_all(
                user_id=pos['user_id'],
                market=pos['market'],
                position_id=pos['id'],
                taker_fee_rate=amount_to_str(taker_fee),
                maker_fee_rate=amount_to_str(maker_fee),
                source='system'
            )
            time.sleep(5)


class PerpetualBalanceCleaner:
    """将最近未活跃的用户小额合约资产划转至现货"""

    def __init__(self):
        self.start_time = int(time.time()) - 86400 * 30
        self.prices = PriceManager.assets_to_usd()
        self.min_amount = Decimal(1)
    
    def get_active_users(self, user_ids):
        # 需要判断的用户都是未持仓的，因此只需要判断是否有挂单即可
        result = set()
        for DB, tables in PerpetualHistoryDB.users_to_dbs_and_tables(user_ids, 'order_history'):
            for table, _uids in tables.items():
                for s in batch_iter(_uids, 100):
                    _in = '(' + ','.join(map(str, s)) + ')'
                    rows = DB.table(table).select("DISTINCT user_id", where=f"user_id IN {_in} AND create_time>{self.start_time}",
                                                  force_index="idx_user_time")
                    for uid, in rows:
                        result.add(uid)
        return result

    def get_need_cleared_balances(self):
        rows = PerpetualLogDB.slice_balance_table().select("user_id,asset,`type`,balance")
        # 用户未挂单且未持仓，且单个资产小于1U的记录
        result = defaultdict(list)
        blacklist = set()
        for user_id, asset, _type, balance in rows:
            balance = quantize_amount(balance, 8)
            if not balance: # 存在精度小于8位的记录，无法划转，server会定期清理
                continue
            if user_id <= 0:
                continue
            if _type == 1:
                if self.prices[asset] * balance < self.min_amount:
                    result[user_id].append((asset, balance))
            else:
                blacklist.add(user_id)
        
        result = {k: v for k, v in result.items() if k not in blacklist}
        active_users = self.get_active_users(list(result.keys()))

        records = []
        for user_id, assets in result.items():
            if user_id in active_users:
                continue
            for asset, balance in assets:
                records.append((user_id, asset, balance))
        return records

    def clean_balances(self):
        records = self.get_need_cleared_balances()
        current_app.logger.warning(f"clean {len(records)} perpetual small balances")
        client = PerpetualServerClient()
        for user_id, asset, balance in records:
            rs = client.get_user_balances(user_id, asset)
            if quantize_amount(rs[asset]['available'], 8) != balance:
                continue
            try:
                perpetual_transfer_out(user_id, asset, balance)
            except Exception:
                continue


class SpotBalanceCleaner:
    """清空现货极小额资产，目前只清理现货账户，已弃用"""

    def __init__(self):
        self.min_amount = Decimal('0.01')
        self.prices = PriceManager.assets_to_usd()

    def get_need_cleared_balances(self):
        rows = TradeLogDB.slice_balance_table().select("user_id,asset,`t`,balance", where="account=0")
        result = defaultdict(list)
        blacklist = set()
        for user_id, asset, t, balance in rows:
            balance = quantize_amount(balance, 8)
            if not balance:
                continue
            if user_id <= 0:
                continue
            if t == 1:
                price = self.prices.get(asset)
                if not price:  # 找不到价格的币种不处理，避免误清除
                    continue
                if balance * price < self.min_amount:
                    result[user_id].append((asset, balance))
            else: # 如果用户有冻结/锁定资产，则不处理这个资产
                blacklist.add((user_id, asset))

        records = []
        for user_id, assets in result.items():
            for asset, balance in assets:
                if (user_id, asset) in blacklist:
                    continue
                records.append((user_id, asset, balance))
        return records


    def clean_balances(self):
        records = self.get_need_cleared_balances()
        current_app.logger.warning(f"clean {len(records)} spot small balances")
        client = ServerClient()
        for user_id, asset, balance in records:
            rs = client.get_user_balances(user_id, asset)
            if quantize_amount(rs[asset]['available'], 8) != balance:
                continue
            bid = BalanceUpdateBusiness.new_id(user_id, asset, -balance)
            try:
                client.silent_add_user_balance(user_id, asset, -balance, BalanceBusiness.SYSTEM, bid)
            except Exception:
                continue


class AccountBalanceCleaner:
    """不活跃小额账户资产清理，目前只清理现货账户"""

    def __init__(self):
        self.prices = PriceManager.assets_to_usd()
        self.min_amount = Decimal('1')
        self.start_time = today_timestamp_utc() - 86400 * 180

    def get_small_spot_balance_users(self) -> tuple[dict[str, Decimal], set]:
        """返回现货小额资产用户，以及黑名单(有非可用资产、资产数量不是小额的用户)"""
        rows = TradeLogDB.slice_balance_table().select("user_id,asset,`t`,balance")
        blacklist = set()
        user_balances = defaultdict(Decimal)
        for user_id, asset, t, balance in rows:
            balance = quantize_amount(balance, 8)
            if not balance:
                continue
            if user_id <= 0:
                continue
            if t == 1:
                price = self.prices.get(asset) # 币价未知，直接不处理该用户
                if not price: 
                    blacklist.add(user_id)
                    continue
                user_balances[user_id] += balance * price 
            else:
                blacklist.add(user_id)

        result = {}
        for user_id, balance in user_balances.items():
            if balance > self.min_amount:
                blacklist.add(user_id)
                continue
            if user_id in blacklist:
                continue
            result[user_id] = balance
        return result, blacklist

    def get_small_perpetual_balance_users(self) -> tuple[dict[str, Decimal], set]:
        """返回合约小额资产用户，以及黑名单(有非可用资产、资产数量不是小额的用户)"""
        rows = PerpetualLogDB.slice_balance_table().select("user_id,asset,`type`,balance")
        blacklist = set()
        user_balances = defaultdict(Decimal)
        for user_id, asset, t, balance in rows:
            balance = quantize_amount(balance, 8)
            if not balance:
                continue
            if user_id <= 0:
                continue
            if t == 1:
                price = self.prices.get(asset)
                if not price: 
                    blacklist.add(user_id)
                    continue
                user_balances[user_id] += balance * price 
            else:
                blacklist.add(user_id)

        result = {}
        for user_id, balance in user_balances.items():
            if balance > self.min_amount:
                blacklist.add(user_id)
                continue
            if user_id in blacklist:
                continue
            result[user_id] = balance
        return result, blacklist

    def get_amm_users(self) -> set[int]:
        rows = UserLiquidity.query.filter(UserLiquidity.liquidity > 0).with_entities(UserLiquidity.user_id).all()
        return {x for x, in rows}

    def get_margin_users(self) -> set[int]:
        rows = MarginLoanOrder.query.filter(MarginLoanOrder.status.in_([
                MarginLoanOrder.StatusType.ARREARS,
                MarginLoanOrder.StatusType.BURST,
                MarginLoanOrder.StatusType.PASS,
            ])).with_entities(MarginLoanOrder.user_id.distinct()).all()
        return {x for x, in rows}

    def get_trade_active_users(self, history_db: type[TradeHistoryDB] | type[PerpetualHistoryDB]) -> set[int]:
        result = set()
        for DB, table in history_db.iter_db_and_table('order_history'):
            rows = DB.table(table).select("DISTINCT user_id", where=f"create_time>{self.start_time}",
                                          force_index="idx_user_time")
            for row in rows:
                result.add(row[0])
        return result

    def get_web_active_users(self) -> set[int]:
        start = timestamp_to_date(self.start_time)
        return filter_active_users(start)

    def get_active_users(self) -> set[int]:
        r1 = self.get_trade_active_users(TradeHistoryDB)
        r2 = self.get_trade_active_users(PerpetualHistoryDB)
        r3 = self.get_web_active_users()
        r = r1 | r2 | r3
        return r  | self.get_main_and_sub_accounts(r)

    def get_main_and_sub_accounts(self, user_ids: set[int]) -> set[int]:
        rows = SubAccount.query.with_entities(SubAccount.main_user_id, SubAccount.user_id).all()
        main_to_subs = defaultdict(list)
        sub_to_main = {}
        for main_id, user_id in rows:
            main_to_subs[main_id].append(user_id)
            sub_to_main[user_id] = main_id

        result = set()
        for user_id in user_ids:
            uids = main_to_subs.get(user_id)
            if uids:
                result.update(uids)
            if uid := sub_to_main.get(user_id):
                result.add(uid)
        return result

    def get_need_cleared_users(self) -> set[int]:
        spot_balances, blacklist1 = self.get_small_spot_balance_users()
        perpetual_balances, blacklist2 = self.get_small_perpetual_balance_users()
        amm_users = self.get_amm_users()
        margin_users = self.get_margin_users()
        active_users = self.get_active_users()

        blacklist = blacklist1 | blacklist2
        user_balances = defaultdict(Decimal)
        for user_id, balance in chain(spot_balances.items(), perpetual_balances.items()):
            if user_id in blacklist:
                continue
            user_balances[user_id] += balance

        result = set()
        for user_id, balance in user_balances.items():
            if user_id in amm_users or user_id in margin_users:
                continue
            if user_id in active_users:
                continue
            if balance > self.min_amount:
                continue
            result.add(user_id)
        return result

    def clean_balances(self):
        user_ids = self.get_need_cleared_users()
        current_app.logger.warning(f"clean {len(user_ids)} small account balances")
        client = ServerClient()
        for user_id in user_ids:
            result = client.get_user_accounts_balances(user_id)
            # 再确认一遍资产数量
            usd = 0
            for _, rs in result.items():
                for asset, balances in rs.items():
                    balance = quantize_amount(balances['available'], 8)
                    if not balance:
                        continue
                    price = self.prices.get(asset)
                    if not price:
                        usd = Decimal('inf')
                        break
                    usd += balances['available'] * price

            if usd > self.min_amount:
                continue

            for account, rs in result.items():
                for asset, balances in rs.items():
                    balance = quantize_amount(balances['available'], 8)
                    if not balance:
                        continue
                    db.session.add(CleanedBalanceTransferHistory(
                        user_id=user_id,
                        asset=asset,
                        amount=balance,
                        account=account))
            db.session.commit()

    @classmethod
    def do_transfer(cls):
        end_time = now()
        start_time = end_time - timedelta(days=1)
        rows = yield_query_records_by_time_range(table=CleanedBalanceTransferHistory,
                                                 start_time=start_time,
                                                 end_time=end_time,
                                                 select_fields=[CleanedBalanceTransferHistory.id])
        for row in rows:
            cls.transfer_one(row.id)

    @classmethod
    def transfer_one(cls, id_):
        with CacheLock(LockKeys.transfer_cleaned_balance(id_)):
            db.session.rollback()
            row = CleanedBalanceTransferHistory.query.get(id_)
            client=  ServerClient()
            if row.status == CleanedBalanceTransferHistory.Status.CREATED:
                try:
                    client.silent_add_user_balance(row.user_id, row.asset, -row.amount,
                                                   BalanceBusiness.CLEANED_BALANCE_TO_ADMIN_TRANSFER, row.id,
                                                   account_id=row.account)
                except ServerClient.BadResponse as e:
                    if e.code == ServerClient.ResponseCode.INSUFFICIENT_BALANCE:
                        row.status = CleanedBalanceTransferHistory.Status.CANCELLED
                        db.session.commit()
                    return
                row.status = CleanedBalanceTransferHistory.Status.FINISHED
                row.finished_at = now()
                db.session.commit()


@scheduled(crontab(minute=23, hour=3, day_of_month=1))
@lock_call()
def close_small_positions_schedule():
    cc = PerpetualPositionCleaner()
    cc.clean_positions()


@scheduled(crontab(minute=33, hour=3))
@lock_call()
def clean_perpetual_small_balances_schedule():
    cc = PerpetualBalanceCleaner()
    cc.clean_balances()


@scheduled(crontab(minute=13, hour=5))
@lock_call()
def clean_account_small_balances_schedule():
    cc = AccountBalanceCleaner()
    cc.clean_balances()
    transfer_cleaned_balance_schedule.delay()


@scheduled(crontab(minute=23, hour='6-8'))
@lock_call()
def transfer_cleaned_balance_schedule():
    AccountBalanceCleaner.do_transfer()
