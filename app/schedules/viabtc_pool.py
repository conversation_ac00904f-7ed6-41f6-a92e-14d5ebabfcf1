# -*- coding: utf-8 -*-

from celery.schedules import crontab

from ..business import (add_viabtc_pool_transfer_orders,
                        do_viabtc_pool_local_transfer,
                        notify_viabtc_pool_orders,
                        fix_pool_order_status,
                        lock_call)
from ..common import CeleryQueues
from ..utils import scheduled, route_module_to_celery_queue

route_module_to_celery_queue(__name__, CeleryQueues.VIABTC_POOL)


@scheduled(crontab(minute='*/2'))
@lock_call()
def add_viabtc_pool_transfer_orders_schedule():
    add_viabtc_pool_transfer_orders()


@scheduled(crontab(minute='*/1'))
@lock_call()
def quick_local_transfer_schedule():
    do_viabtc_pool_local_transfer()


@scheduled(crontab(minute='*/30'))
@lock_call()
def local_transfer_schedule():
    do_viabtc_pool_local_transfer(quickly=False)


@scheduled(crontab(minute='*/1'))
@lock_call()
def notify_order_schedule():
    notify_viabtc_pool_orders()


@scheduled(crontab(minute='*/50'))
@lock_call()
def fix_pool_order_status_schedule():
    fix_pool_order_status()
