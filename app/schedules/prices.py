# -*- coding: utf-8 -*-
import itertools
import json
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal, ROUND_DOWN
from functools import partial
from itertools import chain as chain_iter
import time

from celery.schedules import crontab
from flask import current_app
from sqlalchemy import func

from app.common.constants import FiatPriceCurrency
from .. import config

from ..assets import list_all_assets, get_asset_config, asset_to_chains, get_asset_chain_config
from ..business import (
    PolygonClient, ServerClient, PerpetualServerClient,
    lock_call, PriceManager, WalletClient, send_alert_notice, BusinessSettings,
)
from ..business.clients.fiat_rate import OpenExchangeClient, FixerClient
from ..business.clients.quotes import CoinGeckoClient, CMCAPIClient
from ..business.kline import (
    get_all_asset_quotes_data, KlineGenerator, aggregate_price_kline_data,
    AggregatePeriodType, TopDealRankDataHelper,
)
from ..business.operation import TipBarHelper
from ..caches import (AssetUSDPricesCache, AssetUSDPriceHistoryCache,
                      FiatUSDPricesCache, MarketCache)
from ..caches.activity import NewAssetRecentCache, NewAssetSoonCache, ToOnlineAssetCache
from ..caches.alarms import FiatPriceAlertCache
from ..caches.kline import (
    AssetQuotesDataCache, AssetPeriodPriceCache, AssetKlineCache,
    AssetRankCache, AssetRecentChangeRateCache, TagAssetsCache, TagInfoCache, TagQuoteDataCache,
    AssetInformationCache, AssetTagsCache, AssetTopicsCache, AssetTotalCirculationCache, SearchClickCache,
    AssetTrendingSearchCache, MarketTrendingSearchCache, DepositWithdrawalAssetSearchCache,
    AssetRecent30dChangeRateCache, AssetRecent7dChangeRateCache, AssetRankEntryRuleCache, AssetRankEntryRuleItem1Cache,
    AssetRankEntryRuleItem2Cache, AssetRankEntryRuleItem3Cache
)
from ..caches.prices import SpotMarketRealTimePriceCache, AssetRealTimePriceCache, AssetRealTimeRateCache, CMCAssetRateInfoCache
from ..caches.report import CMCSyntheticCache
from ..caches.spot import CetCirculationCache, MarketLastPriceCache
from ..caches.assets import CoinIncomeRateCache
from ..caches.amm import LiquidityPoolCache
from ..caches.statistics import StakingStatisticsCache
from ..caches.investment import Investment7DaysEARCache
from ..caches.system import AssetChangeEventCache
from ..common import Currency, FIXED_ASSET_PRICES, CeleryQueues, SMALL_CURRENCY_SET, PrecisionEnum, CommonCurrency
from ..exceptions import InvalidArgument
from ..models import AssetPrice as AssetPriceModel, db
from ..models.mongo.assets import AssetApiSource, AssetCirculationSourceMySQL
from ..models.quotes import AssetPriceKline, PeriodType, CoinInformation, \
    AssetCirculationHistory
from ..models.staking import StakingAccount
from ..utils import (
    scheduled, g_map, current_timestamp,
    amount_to_str, timestamp_to_datetime,
    route_module_to_celery_queue, now, celery_task, quantize_amount, datetime_to_utc8_str
)
from ..utils.amount import calculate_ratio
from ..utils.parser import JsonEncoder

route_module_to_celery_queue(__name__, CeleryQueues.PRICES)


def quantize_asset_price(value: Decimal) -> Decimal:
    # 至少保留小数点后8位，最多保留小数点后12位，至少保留4位有效数字
    exp = value.adjusted()
    exp = max(-PrecisionEnum.PRICE_PLACES, min(exp - 3, -PrecisionEnum.COIN_PLACES))
    return value.quantize(Decimal(10) ** exp, ROUND_DOWN)


@scheduled(crontab(minute=0, hour='*/1'))
@lock_call()
def update_fiat_usd_prices():
    def fetch_polygon_prices(base_currency):
        client = PolygonClient()

        get_price = partial(client.get_exchange_rate,
                            to_currency=base_currency.name)

        _prices = {}

        def update_price(_currency: FiatPriceCurrency):
            # noinspection PyBroadException
            try:
                _price = get_price(_currency.name)
            except Exception:
                return
            if not _price:
                current_app.logger.error(f"Polygon.io Not Return {_currency.name} Fiat Rate Price")
            if _price > 0:
                _prices[_currency.name] = str(_price)
        update_currency = [getattr(FiatPriceCurrency, i) for i in {i.name for i in FiatPriceCurrency}]
        g_map(update_price,
              (_c for _c in update_currency
               if _c is not base_currency),
              size=10)
        return _prices

    def fetch_open_exchange_prices():
        client = OpenExchangeClient()
        currency_mapper = client.get_exchange_rates()
        if not currency_mapper:
            current_app.logger.error("Open Exchange Rates Error Not Return Data")
            return {}
        _prices = {}
        for _currency in SMALL_CURRENCY_SET:
            api_price = currency_mapper.get(_currency, Decimal())
            # 防止返回值变化, 汇率长期不更新
            if not api_price:
                current_app.logger.error(f"Open Exchange Not Return {_currency} Rate Data")
            if api_price > 0:
                _prices[_currency] = amount_to_str(Decimal("1") / api_price, 6)
        return _prices

    def fetch_fixer_prices(base_currency):
        client = FixerClient()
        currency_mapper = client.get_exchange_rates()
        if not currency_mapper:
            current_app.logger.error("Fixer Rates Error Not Return Data")
            return {}
        base_price = currency_mapper.get(base_currency.name)
        if not base_price:
            current_app.logger.error(f"Fixer Rates Error Not Return {base_currency.name} Data")
            return {}
        _prices = {}
        for i in Currency:
            _currency = i.name
            api_price = currency_mapper.get(_currency, Decimal())
            if not api_price:
                current_app.logger.error(f"Fixer Not Return {_currency} Rate Data")
            if api_price > 0:
                _prices[_currency] = amount_to_str(base_price / api_price, 6)
        return _prices

    base = Currency.USD
    prices = {base.name: '1'}
    common_fiats = {c.name for c in CommonCurrency}
    polygon_prices = fetch_polygon_prices(base)
    if polygon_prices:
        prices.update(polygon_prices)
    open_exchange_prices = fetch_open_exchange_prices()
    if open_exchange_prices:
        prices.update(open_exchange_prices)
    missing_fiats = common_fiats - set(prices.keys())
    if missing_fiats:
        fixer_prices = fetch_fixer_prices(base)
        add_prices = {f: fixer_prices.get(f) for f in missing_fiats if fixer_prices.get(f)}
        prices.update(add_prices)
    if prices:
        FiatUSDPricesCache().hmset(prices)

    missing_fiats = common_fiats - set(prices.keys())
    alert_fiats = missing_fiats - set(FiatPriceAlertCache().get_fiats())
    if alert_fiats:
        FiatPriceAlertCache().add_fiats(alert_fiats)
        fiats_str = ', '.join(alert_fiats)
        time_str = datetime_to_utc8_str(now())
        alert_msg = f'{fiats_str}法币汇率在{time_str}(UTC+8)时间已不在更新，请及时处理'
        send_alert_notice(alert_msg,
                          config["ADMIN_CONTACTS"]["web_notice"],
                          at=config["ADMIN_CONTACTS"]['slack_at'].get("fiat_price"))

@scheduled(crontab(minute="*/5"))
@lock_call()
def update_search_treading_schedule():
    AssetTrendingSearchCache.reload()
    MarketTrendingSearchCache.reload()
    DepositWithdrawalAssetSearchCache.reload()


@scheduled(crontab(minute="*/15"))
@lock_call()
def update_top_deal_rank_data_cache_schedule():
    TopDealRankDataHelper.update()


@scheduled(crontab(minute="*/30"))
def update_search_click_cache_schedule():
    SearchClickCache.clear()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_usdt_prices():
    server_client = ServerClient()
    perpetual_client = PerpetualServerClient()

    btc_usd_market_info = perpetual_client.get_market_status('BTCUSD', 86400)
    btc_usd_price = Decimal(btc_usd_market_info['index_price'])
    btc_usdt_price = server_client.get_market_index('BTCUSDT')
    usdt_usd_price = amount_to_str(btc_usd_price / btc_usdt_price, 8)
    AssetUSDPricesCache().hset('USDT', usdt_usd_price)
    AssetUSDPriceHistoryCache('USDT').add_value(usdt_usd_price)
    current_app.logger.info(f"BTCUSD {btc_usd_price}, BTCUSDT {btc_usdt_price}, USDTUSD {usdt_usd_price}")


@scheduled(20)
@lock_call()
def update_asset_usd_prices():
    asset_usd_prices_cache = AssetUSDPricesCache()
    usdt_usd_price = asset_usd_prices_cache.get_price('USDT')
    if usdt_usd_price <= 0:
        raise RuntimeError('USDT-USD price is empty')

    tickers = ServerClient().get_all_market_tickers()
    online_markets = set(MarketCache.list_online_markets())
    offline_markets = set(MarketCache.list_offline_markets())
    # 置换过程中的币种
    tmp_assets = AssetChangeEventCache().get_assets()
    last_prices = MarketLastPriceCache().read()

    quotes = 'USDT', 'BTC', 'BCH', 'ETH', 'CET'
    prices = {}
    for asset in quotes:
        if asset == 'USDT':
            continue
        market = f'{asset}USDT'
        if market not in online_markets:
            continue
        if ticker := tickers.get(market):
            prices[asset] = Decimal(ticker['last']) * usdt_usd_price
    if not prices:
        raise RuntimeError(f'none of {quotes} has a market to USDT')

    prices['USDT'] = usdt_usd_price
    for asset, price in FIXED_ASSET_PRICES.items():
        prices[asset] = price

    for asset in chain_iter(quotes, list_all_assets()):
        if asset in prices:
            continue
        for quote in quotes:
            market = f'{asset}{quote}'
            if market not in online_markets:
                continue
            if ticker := tickers.get(market):
                if (last_price := Decimal(ticker['last'])) > 0:
                    prices[asset] = last_price * prices[quote]
                    break
            elif last_price := last_prices.get(market):
                last_price = Decimal(last_price)
                if last_price > 0:
                    prices[asset] = last_price * prices[quote]
                    break
        else:
            market_name = f"{asset}USDT"
            last_price = None
            if market_name in offline_markets:
                last_price = last_prices.get(market_name)
            if last_price:
                prices[asset] = Decimal(last_price) * usdt_usd_price
            else:
                prices[asset] = get_asset_config(asset).initial_price
            # 改名的币种上架市场前取初始配置价格
            if asset in tmp_assets:
                prices[asset] = get_asset_config(asset).initial_price

    prices = {asset: amount_to_str(quantize_asset_price(price))
              for asset, price in prices.items()}
    asset_usd_prices_cache.hmset(prices)
    if delisted := set(asset_usd_prices_cache.value
                       ).difference(set(list_all_assets())):
        asset_usd_prices_cache.hdel(*delisted)
    for asset, price in prices.items():
        AssetUSDPriceHistoryCache(asset).add_value(price)


@scheduled(10)
@lock_call()
def update_asset_real_time_price_rate_cache():
    cache = SpotMarketRealTimePriceCache()
    if not cache.check_stamp():
        raise RuntimeError('MarketRealTimePriceStampCache was delayed by 30s')
    usdt_usd_price = AssetUSDPricesCache().get_price('USDT')
    if not usdt_usd_price:
        raise RuntimeError('USDT-USD price is empty')
    online_markets = set(MarketCache.list_online_markets())

    old_rate = AssetRecentChangeRateCache().get_rates()
    market_price_dict = cache.read()
    prices = {"USDT":  usdt_usd_price}
    rates = {"USDT": old_rate.get("USDT", Decimal(0))}
    quotes = ['USDT', 'BTC', 'BCH', 'ETH', 'CET']
    for quote in quotes[1:]:
        market = f"{quote}USDT"
        if (price_tuple := market_price_dict.get(market)) and market in online_markets:
            _open, _last = price_tuple
            prices[quote] = _last * usdt_usd_price
            rates[quote] = calculate_ratio(_open, _last)

    for asset, price in FIXED_ASSET_PRICES.items():
        prices[asset] = price * usdt_usd_price
        rates[asset] = old_rate.get(asset, Decimal(0))

    old_price = PriceManager.assets_to_usd()
    for asset in itertools.chain(quotes, list_all_assets()):
        if asset in prices:
            continue
        for quote in quotes:
            market = f'{asset}{quote}'
            if (price_tuple := market_price_dict.get(market)) \
                    and market in online_markets:
                _open, _last = Decimal(price_tuple[0]), Decimal(price_tuple[1])
                # 一些新币上线没有交易数据
                if _last > Decimal(0):
                    prices[asset] = _last * prices[quote]
                    rates[asset] = calculate_ratio(_open, _last)
                    break
        else:
            prices[asset] = old_price.get(asset, Decimal(0))
            rates[asset] = old_rate.get(asset, Decimal(0))
    to_save_prices = {k: amount_to_str(quantize_asset_price(v)) for k, v in prices.items()}
    to_save_rates = {k: amount_to_str(v, 4) for k, v in rates.items()}
    AssetRealTimePriceCache().save(to_save_prices)
    AssetRealTimeRateCache().save(to_save_rates)


@scheduled(crontab(minute='0-5', hour='*/1'))
@lock_call()
def save_asset_prices():
    _now = current_timestamp(to_int=True)
    _now -= _now % 3600
    now_dt = timestamp_to_datetime(_now)

    if AssetPriceModel.query \
            .filter(AssetPriceModel.date == now_dt) \
            .first():
        return

    for asset, price in AssetUSDPricesCache().value.items():
        cache = AssetUSDPriceHistoryCache(asset)
        prices = cache.list_values()
        avg_price = sum(Decimal(x) for x in prices) / len(prices) if prices else 0
        db.session.add(AssetPriceModel(
            date=now_dt,
            asset=asset,
            price=price,
            avg_price=quantize_asset_price(avg_price)
        ))
    db.session.commit()


@scheduled(crontab(minute='1-59/3'))
@lock_call()
def save_quote_asset_prices():
    # for trade mining activity.
    quote_assets = ["BTC", "BCH", "ETH", "CET", "USDT", "USDC", "TUSD", "PAX"]
    dt = now()
    for asset in quote_assets:
        price = AssetUSDPricesCache().get_price(asset)
        if price == Decimal():
            continue
        db.session.add(
            AssetPriceModel(
                date=dt,
                asset=asset,
                price=price,
                avg_price=price,
            )
        )
    db.session.commit()


def update_asset_rank_cache(assets_list_data):
    for sort_type in AssetRankCache.sort_types:
        cache = AssetRankCache(sort_type)
        reverse = not sort_type.endswith("_asc")
        # 币种名称
        if sort_type in ("asset_name_asc", "asset_name_desc"):
            assets_list_data.sort(key=lambda x: x[0], reverse=reverse)
            assets = [v[0] for v in assets_list_data]
            cache.save_assets(assets)
        if sort_type in ("real_circulation_usd", "real_circulation_usd_asc"):
            assets_list_data.sort(key=lambda x: -x[1]["circulation_usd_rank"], reverse=reverse)
            assets = [v[0] for v in assets_list_data]
            cache.save_assets(assets)
        if sort_type in ("circulation_usd", "circulation_usd_asc"):
            assets_list_data.sort(key=lambda x: (x[0] == "CET", -x[1]["circulation_usd_rank"]),
                                  reverse=reverse)
            assets = [v[0] for v in assets_list_data]
            cache.save_assets(assets)
        if sort_type in ["volume_usd", "price_usd", "online_time",
                         "volume_usd_asc", "price_usd_asc", "online_time_asc"]:
            sort_type_key = sort_type.replace("_asc", "")
            assets_list_data.sort(key=lambda x: x[1][sort_type_key], reverse=reverse)
            assets = [v[0] for v in assets_list_data]
            cache.save_assets(assets)
        if sort_type == "new_recent":
            assets = {v[0] for v in assets_list_data}
            assets = [i.code for i in NewAssetRecentCache.get_new_coin_rows() if i.code in assets]
            cache.save_assets(assets)
        if sort_type == "trending":
            assets = {v[0] for v in assets_list_data}
            assets = [i for i in AssetTrendingSearchCache().get_top_assets() if i in assets]
            cache.save_assets(assets)


def update_new_asset_recent_cache():
    NewAssetRecentCache.reload()


def update_new_asset_soon_cache():
    NewAssetSoonCache.reload()


@scheduled(crontab(minute='*/2'))
@lock_call()
def update_asset_quotes_schedule():
    # 稍微等待一下市场的状态变更
    time.sleep(3)
    assets_data = get_all_asset_quotes_data()
    assets_list_data = [(asset, v) for asset, v in assets_data.items()]
    update_asset_rank_cache(assets_list_data)
    # 新币专区-最新上线币种
    update_new_asset_recent_cache()
    # 新币专区-即将上线币种
    update_new_asset_soon_cache()
    # 倒计时-竞价中币种
    ToOnlineAssetCache.reload()
    cache = AssetQuotesDataCache()
    for asset, asset_data in assets_data.items():
        ts = int(now().timestamp())
        asset_data['ts'] = ts
        start_ts = ts - 86400*7
        kline_data = [v for v in aggregate_price_kline_data(asset, start_ts, ts,
                                                            AggregatePeriodType.HOUR).items()]
        kline_data.sort(key=lambda x: x[0], reverse=True)
        asset_data["klines"] = kline_data
        json_str = json.dumps(asset_data, cls=JsonEncoder)
        cache.hset(asset, json_str)
    cache.delete_old_assets_data()
    TagInfoCache.reload()
    TagQuoteDataCache.reload()  # 标签统计数据要与AssetQuotesDataCache更新频率保持一致


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_coin_information_cache():
    TagAssetsCache.reload()
    AssetInformationCache.reload()
    AssetTagsCache.reload()
    AssetTopicsCache.reload()


@scheduled(crontab(minute='*/1'))
@lock_call()
def update_asset_kline_schedule():
    assets = list_all_assets()

    for asset in assets:
        if asset not in KlineGenerator.SPECIAL_ASSETS and KlineGenerator.get_market(asset) is None:
            continue
        update_asset_kline.delay(asset)


@scheduled(crontab(minute='*/3'))
@lock_call()
def update_asset_top_data_schedule():
    assets = list_all_assets()
    for asset in assets:
        update_asset_top_data.delay(asset)


@scheduled(30)
@lock_call()
def update_assets_24h_change_rate():
    _update_change_rate(PeriodType.MINUTE)


@scheduled(60 * 10)
@lock_call()
def update_assets_30d_change_rate():
    _update_change_rate(PeriodType.HOUR, interval=7)
    _update_change_rate(PeriodType.HOUR, interval=30)


def _update_change_rate(period_type: PeriodType, interval=None):
    current_ts = current_timestamp(to_int=True)
    one_day = 86400

    if period_type is PeriodType.HOUR:
        interval_dict = {
            7: AssetRecent7dChangeRateCache(),
            30: AssetRecent30dChangeRateCache()
        }
        one_hour = 60 * 60
        start_ts = current_ts - one_day * interval
        fix_start_ts = start_ts - start_ts % one_hour
        fix_end_ts = current_ts - current_ts % one_hour
        ts_points = [str(fix_start_ts + one_hour * v) for v in range((fix_end_ts - fix_start_ts) // one_hour)]

        asc_key = f'change_rate_{interval}d_asc'
        desc_key = f'change_rate_{interval}d_desc'
        recent_cache = interval_dict[interval]
    else:
        one_minute = 60
        start_ts = current_ts - one_day
        fix_start_ts = start_ts - start_ts % one_minute
        fix_end_ts = current_ts - current_ts % one_minute
        ts_points = [str(fix_start_ts + one_minute * v) for v in range((fix_end_ts - fix_start_ts) // one_minute)]

        asc_key = 'change_rate_asc'
        desc_key = 'change_rate_desc'
        recent_cache = AssetRecentChangeRateCache()
    online_ts_map = CoinInformation.get_all_assets_online_ts()
    cache_assets = set(AssetQuotesDataCache().hkeys())
    online_assets = set(list_all_assets())
    assets = list(cache_assets & online_assets)
    assets_prices = PriceManager.assets_to_usd(assets)
    assets_price_data = {
        asset: dict(
            before_price=Decimal(),
            change_rate=Decimal(),
            current_price=assets_prices.get(asset, Decimal())
        )
        for asset in assets
    }

    for asset in assets:
        _online_ts = online_ts_map.get(asset, 0)
        if _online_ts > 0:
            _ts_points = [v for v in ts_points if int(v) > _online_ts]
        points_result: dict[int, Decimal] = dict(map(
            lambda x: (int(x[0]), Decimal(x[1])),
            AssetKlineCache(asset, period_type).hmget_with_keys(_ts_points)))
        if not points_result:
            continue

        before_price = points_result[min(points_result.keys())]
        current_price = assets_price_data[asset]["current_price"]
        change_rate = (current_price - before_price) / before_price if before_price != 0 else Decimal()
        change_rate = quantize_amount(change_rate, 4)
        assets_price_data[asset]['before_price'] = before_price
        assets_price_data[asset]["current_price"] = current_price
        assets_price_data[asset]['change_rate'] = change_rate

    asc_cache = AssetRankCache(asc_key)
    assets_list_data = list(assets_price_data.items())
    assets_list_data.sort(key=lambda x: x[1]['change_rate'])
    sorted_assets = [v[0] for v in assets_list_data]
    asc_cache.save_assets(sorted_assets)

    desc_cache = AssetRankCache(desc_key)
    desc_cache.save_assets(list(reversed(sorted_assets)))

    recent_cache.hmset({asset: str(d['change_rate']) for asset, d in assets_price_data.items()})


@celery_task
@lock_call(with_args=True)
def update_asset_kline(asset):
    try:
        tool = KlineGenerator(asset)
        tool.generate()
    except InvalidArgument:
        current_app.logger.info(f"{asset} not find market for update asset kline")


@celery_task
@lock_call(with_args=True)
def update_asset_top_data(asset):
    online_ts = CoinInformation.get_online_ts(asset)

    deltas = [
        ("four_hour", timedelta(hours=4), PeriodType.MINUTE),
        ("one_day", timedelta(days=1), PeriodType.MINUTE),
        ("seven_day", timedelta(days=7), PeriodType.HOUR),
        ("fifteen_day", timedelta(days=15), PeriodType.HOUR),
        ("one_month", timedelta(days=30), PeriodType.DAY),
        ("three_month", timedelta(days=90), PeriodType.DAY),
        ("six_month", timedelta(days=180), PeriodType.DAY),
        ("one_year", timedelta(days=365), PeriodType.DAY),
        ("all", None, PeriodType.DAY),
    ]
    ts = current_timestamp(to_int=True)
    current_price = PriceManager.asset_to_usd(asset)
    result = defaultdict(lambda: {'min_price': Decimal(),
                                  'max_price': Decimal(),
                                  'earliest_price': Decimal(),
                                  'change_rate': Decimal(),
                                  'earliest_ts': ts})

    all_in_cache_points = dict(list(
        itertools.chain(*[map(lambda x: (int(x[0]), Decimal(x[1])),
                              AssetKlineCache(asset, period_type).hgetall().items())
                          for period_type in PeriodType])))
    if online_ts:
        all_in_cache_points = {
            key: v
            for key, v in all_in_cache_points.items() if key > online_ts
        }
    if not all_in_cache_points:
        return result
    earliest_ts = min(all_in_cache_points.keys())
    for delta in deltas:
        result[delta[0]]['earliest_ts'] = earliest_ts
        if delta[1]:
            period_seconds = delta[2].to_seconds()
            start_ts = ts - int(delta[1].total_seconds())
            fix_start_ts = start_ts - start_ts % period_seconds
            points_result = {
                key: v for key, v in all_in_cache_points.items() if key >= fix_start_ts
            }
            if not points_result:
                continue
        else:
            points_result = all_in_cache_points
        result[delta[0]]["min_price"] = min(min(points_result.values()), current_price)
        result[delta[0]]["max_price"] = max(max(points_result.values()), current_price)
        result[delta[0]]["earliest_price"] = points_result[min(points_result.keys())]
        result[delta[0]]["change_rate"] = \
            (current_price - result[delta[0]]["earliest_price"]) \
            / result[delta[0]]["earliest_price"] if result[delta[0]]["earliest_price"] != 0 \
                else Decimal()
        result[delta[0]]["change_rate"] = quantize_amount(result[delta[0]]["change_rate"], 4)
    json_string = json.dumps(result, cls=JsonEncoder)
    cache = AssetPeriodPriceCache()
    cache.hset(asset, json_string)


@scheduled(crontab(hour='*/1', minute='0'))
@lock_call()
def update_asset_circulation_schedule():
    from app.assets import list_pre_assets
    q = CoinInformation.query.filter(
        CoinInformation.status == CoinInformation.Status.VALID,
        CoinInformation.circulation_type != CoinInformation.CirculationType.MANUAL_EDIT
    ).all()

    assets_mapping = {v.code: v for v in q if v.code not in list_pre_assets()}
    wallet_asset_circulation_data = WalletClient().get_assets_circulation()

    special_mapping = dict()
    for v in AssetCirculationSourceMySQL.query.all():
        special_mapping[(v.asset, v.source)] = v.source.parse_id(v.source_id)
    cmc_ids = sorted([
        v
        for k, v in special_mapping.items() if k[1] == AssetApiSource.CMC
    ])
    cgc_ids = sorted([
        v
        for k, v in special_mapping.items() if k[1] == AssetApiSource.COINGECKO
    ])
    cmc_id_data = {}
    cgc_id_data = {}
    try:
        cmc_id_data = CMCAPIClient().get_ids_circulation(cmc_ids)
    except Exception as e:
        current_app.logger.error(f"get cmc_asset_circulation data error, {e!r}")

    try:
        cgc_id_data = CoinGeckoClient().get_ids_circulation(cgc_ids)
    except Exception as e:
        current_app.logger.error(f"get cgc_asset_circulation_data data error, {e!r}")

    def get_circulation_by_id(parse_asset: str) -> Decimal | None:
        __id = special_mapping.get((parse_asset, AssetApiSource.CMC), '')
        __circulation = Decimal()
        if __id and __id in cmc_id_data:
            # 优先匹配特殊配置的id
            __circulation = cmc_id_data[__id]["circulation"]
            return __circulation
        __circulation = Decimal()
        __id = special_mapping.get((parse_asset, AssetApiSource.COINGECKO), '')
        if __id and __id in cgc_id_data:
            __circulation = cgc_id_data[__id]["circulation"]
            return __circulation
        return None

    alert_circulation_update_data = []
    max_rate = BusinessSettings.asset_circulation_alert_limit_max_rate
    min_rate = BusinessSettings.asset_circulation_alert_limit_min_rate
    for _asset, _v in assets_mapping.items():
        if _v.circulation_type == CoinInformation.CirculationType.MANUAL_EDIT:
            continue
        if _asset == "CET":
            data = CetCirculationCache().read_aside()
            circulation = Decimal(data['total_supply'])
            _v.circulation_type = CoinInformation.CirculationType.AUTO_TRACK
        elif _asset in wallet_asset_circulation_data:
            circulation = wallet_asset_circulation_data[_asset]
            _v.circulation_type = CoinInformation.CirculationType.AUTO_TRACK
        else:
            circulation = get_circulation_by_id(_asset) or Decimal()
            if circulation > 0:
                _v.circulation_type = CoinInformation.CirculationType.THIRD_PARTY
        if not circulation:
            continue
        old_circulation = _v.circulation
        change_rate = circulation / old_circulation if old_circulation else Decimal('1')

        if change_rate < min_rate or change_rate > max_rate:
            _v.circulation_type = CoinInformation.CirculationType.MANUAL_EDIT
            db.session.commit()
            alert_circulation_update_data.append([_asset, old_circulation, circulation, change_rate])
            continue

        if circulation > Decimal():
            _v.circulation = circulation
            try:
                total_supply = Decimal(_v.total_supply)
                if total_supply < circulation:
                    _v.total_supply = amount_to_str(circulation)
            except Exception as e:
                current_app.logger.error(f"{_asset} convert {_v.total_supply} error {e!r}")
            db.session.commit()

    text_template = "系统检测到币种 {_asset} 最新获取的流通量 {new} 数值异常，已超过上一次流通量数值 {old} 的 {rate} 倍，请及时检查更新\n"
    build_text = ""
    for data in alert_circulation_update_data:
        build_text += text_template.format(
            _asset=data[0],
            old=amount_to_str(data[1], PrecisionEnum.RATE_PLACES),
            new=data[2],
            rate=amount_to_str(data[3], PrecisionEnum.RATE_PLACES))
    send_alert_notice(
        build_text,
        config["ADMIN_CONTACTS"]["customer_service"],
        at=config['ADMIN_CONTACTS']['slack_at'].get('asset_circulation_notices', [])
    )
    assets = list_all_assets()
    info_query = CoinInformation.query.filter(
        CoinInformation.code.in_(assets),
        CoinInformation.status == CoinInformation.Status.VALID
    ).with_entities(CoinInformation.circulation, CoinInformation.code).all()
    circulation_data = {v.code: v.circulation for v in info_query if v.circulation > Decimal('0')}
    q = AssetCirculationHistory.query.with_entities(
        func.max(AssetCirculationHistory.time).label('max_time'),
        AssetCirculationHistory.asset,
    ).group_by(AssetCirculationHistory.asset).all()
    asset_time_dict = {v.asset: v.max_time for v in q}
    # 时间戳取整
    ts = current_timestamp(to_int=True)
    current_fix_ts = ts - ts % 3600
    records = []
    for asset, circulation in circulation_data.items():
        circulation_usd = quantize_amount(PriceManager.asset_to_usd(asset) * circulation, 2)
        if asset not in asset_time_dict:
            records.append(
                AssetCirculationHistory(
                    asset=asset,
                    time=current_fix_ts,
                    circulation=circulation,
                    circulation_usd=circulation_usd
                )
            )
            continue
        last_ts = asset_time_dict[asset]
        last_fix_ts = last_ts - last_ts % 3600
        # 十天以上则只添加最新时间点的流通量数据
        if current_fix_ts - last_fix_ts > 10 * 86400:
            records.append(
                AssetCirculationHistory(
                    asset=asset,
                    time=current_fix_ts,
                    circulation=circulation,
                    circulation_usd=circulation_usd
                )
            )
            continue
        ts_points = [last_fix_ts+(i+1)*3600 for i in range((current_fix_ts - last_fix_ts) // 3600)]
        for point in ts_points:
            records.append(
                AssetCirculationHistory(
                    asset=asset,
                    time=point,
                    circulation=circulation,
                    circulation_usd=circulation_usd
                )
            )
    db.session.add_all(records)
    db.session.commit()
    AssetTotalCirculationCache.reload()


@scheduled(crontab(minute="*/5"))
@lock_call()
def pre_asset_circulation_schedule():
    from app.models import PreTradingUserAsset
    q = PreTradingUserAsset.query.with_entities(
        PreTradingUserAsset.asset,
        func.sum(PreTradingUserAsset.issue_amount).label('total_amount')
    ).group_by(PreTradingUserAsset.asset).all()
    asset_data = {v.asset: v.total_amount for v in q}
    info_q = CoinInformation.query.filter(
        CoinInformation.code.in_(asset_data.keys())
    )
    for v in info_q:
        v: CoinInformation
        v.circulation = asset_data.get(v.code, Decimal())
        v.total_supply = amount_to_str(v.circulation)
    db.session.commit()


@scheduled(crontab(hour='*/1', minute=0))
@lock_call()
def clean_outdated_asset_kline_data_schedule():
    assets = list_all_assets()
    for asset in assets:
        clean_outdated_asset_kline_data.delay(asset)


@celery_task
@lock_call(with_args=True)
def clean_outdated_asset_kline_data(asset):
    # clean cache.
    """
    clean cache:
            分钟k线 7天 近1天保留全部，1-7每15分钟保留一个点
            小时k线 7月 近1个月保留全部，1-7月每三小时保留一个点
            天k线 永久
    clean db:
            分钟k线 7天
            小时k线 1年
            天k线 永久
    """
    current_ts = current_timestamp(to_int=True)

    def get_points(p_type: PeriodType, rules: list[tuple[int, int, int]]):
        p_seconds = p_type.to_seconds()
        _points = set()
        for rule in rules:
            _start_delta, _end_delta, period_count = rule
            rule_period_secs = p_seconds * period_count
            current_period_ts = current_ts - current_ts % rule_period_secs
            _start = _start_delta + current_period_ts
            _end = _end_delta + current_period_ts
            while _start < _end:
                _points.add(_start)
                _start += rule_period_secs
        return _points

    _DAY_DELTA = 3600 * 24
    cache_maps = {
        # 近1天保留全部，1-7间隔15分钟保留一个数据点
        PeriodType.MINUTE: get_points(
            PeriodType.MINUTE,
            [
                (- 1 * _DAY_DELTA, 0, 1),
                (- 7 * _DAY_DELTA, -_DAY_DELTA, 15),
            ]
        ),
        # 近1个月保留全部，1-7月间隔三小时保留一个数据点
        PeriodType.HOUR: get_points(
            PeriodType.HOUR,
            [
                (- 30 * _DAY_DELTA, 0, 1),
                (- 210 * _DAY_DELTA, -_DAY_DELTA, 3),
            ]
        )
    }
    for period_type, points in cache_maps.items():
        cache = AssetKlineCache(asset, period_type)
        exists_points = [int(v) for v in cache.hkeys()]
        del_points = [str(v) for v in exists_points if v not in points]
        if del_points:
            cache.hdel(*del_points)
    # clean db
    db_maps = {
        PeriodType.MINUTE: 3600 * 24 * 7,
        PeriodType.HOUR: 3600 * 24 * 365,
    }
    for period_type, delta in db_maps.items():
        start = current_ts - delta
        AssetPriceKline.query.filter(
            AssetPriceKline.period == period_type,
            AssetPriceKline.asset == asset,
            AssetPriceKline.time < start,
        ).delete(synchronize_session=False)
        db.session.commit()


@scheduled(crontab(minute='*/2'))
@lock_call()
def check_invisible_assets_schedule():
    from app.assets import get_asset_chain_config, asset_to_chains
    from app.caches.prices import InvisibleAssetsCache
    asset_chains_data = asset_to_chains()
    invisible_assets = set(asset_chains_data.keys())
    for asset, chains in asset_chains_data.items():
        for chain in chains:
            ac_conf = get_asset_chain_config(asset, chain)
            if ac_conf.is_visible:
                invisible_assets.remove(asset)
                break

    cache = InvisibleAssetsCache()
    cache.save(invisible_assets)


@scheduled(60 * 5)
@lock_call()
def update_asset_rank_entry_rule_cache():
    assets_1 = AssetRankEntryRuleItem1Cache().read()
    assets_2 = AssetRankEntryRuleItem2Cache().read()
    exclude_assets = set(assets_1) & set(assets_2)

    assets_3 = AssetRankEntryRuleItem3Cache().read()
    assets_4 = AssetRankCache('volume_usd').read_assets()  # 成交榜
    assets_4 = assets_4[:20]
    whitelist = set(assets_3) | set(assets_4)
    exclude_assets -= whitelist
    cache_cls = AssetRankEntryRuleCache
    cache_cls().save(list(exclude_assets))
    TipBarHelper.try_auto_operation_tip_bar_by_business(exclude_assets)


@scheduled(60 * 10)
@lock_call()
def update_asset_off_site_premium_rule_cache():
    cmc_data = CMCSyntheticCache().read()
    for key, value in cmc_data.items():
        cmc_data[key] = json.loads(value)
    price_mapping = PriceManager.assets_to_usd()
    assets = list_all_assets()
    premium = Decimal('0.5')
    ret = []
    for asset in assets:
        price = price_mapping.get(asset) or Decimal()
        if not price:
            continue
        cmc = cmc_data.get(asset, {})
        cmc_price = Decimal(cmc.get('price') or Decimal())
        if not cmc_price:
            continue
        calc_premium = (price - cmc_price) / cmc_price
        if calc_premium > premium:
            ret.append(asset)

    cache_cls = AssetRankEntryRuleItem1Cache
    cache_cls().save(ret)


@scheduled(60 * 10)
@lock_call()
def update_asset_abnormal_deposit_rule_cache():
    ret = []
    threshold_minutes = 180
    for asset, chains in asset_to_chains().items():
        d_enabled = []
        d_dm_gt180 = []
        for chain in chains:
            ac_conf = get_asset_chain_config(asset, chain)
            d_enabled.append(ac_conf.deposits_all_enabled)
            d_dm_gt180.append(ac_conf.deposits_delay_minutes > threshold_minutes)
        if not any(d_enabled) or all(d_dm_gt180):
            ret.append(asset)
    cache_cls = AssetRankEntryRuleItem2Cache
    cache_cls().save(ret)


@scheduled(60 * 10)
@lock_call()
def update_asset_activity_rule_cache():

    def _get_new_online_assets() -> set:
        model = CoinInformation
        two_weeks_ago = now() - timedelta(days=7 * 2)
        rows = model.query.with_entities(
            model.code
        ).filter(
            model.status == model.Status.VALID,
            model.online_time > two_weeks_ago
        ).all()
        return {row.code for row in rows}

    def _get_ieo_activity_asset() -> set:  # Dock
        from app.models import IeoActivity

        model = IeoActivity
        rows = model.query.filter(
            model.status == model.StatusType.ONLINE,
        ).all()
        assets = set()
        for row in rows:
            if row.active_status == model.ActiveStatus.STARTED:
                assets.add(row.subscribe_asset)
        return assets

    def _get_discount_activity_asset() -> set:  # Dibs
        from app.models import DiscountActivity

        model = DiscountActivity
        rows = model.query.filter(
            model.status == model.StatusType.ONLINE,
        ).all()
        assets = set()
        for row in rows:
            if row.active_status == model.ActiveStatus.STARTED:
                assets.add(row.asset)
        return assets

    def _get_airdrop_activity_asset() -> set:  # 空投
        from app.models import AirdropActivity
        from app.business.activity.airdrop import get_airdrop_activity_rewards

        model = AirdropActivity
        rows = model.query.filter(
            model.status == model.StatusType.ONLINE,
        ).all()
        processing = []
        for row in rows:
            if row.active_status == model.ActiveStatus.STARTED:
                processing.append(row)
        assets = set()
        for row in processing:
            asset_rewards, _ = get_airdrop_activity_rewards(row.id)
            assets |= {item['asset'] for item in asset_rewards}
        return assets

    def _get_trade_rank_activity_asset() -> set:  # 交易赛
        from app.models import TradeRankActivity

        model = TradeRankActivity
        rows = model.query.with_entities(
            model.gift_asset
        ).filter(
            model.status == model.Status.ONLINE,
            model.ended_at > now(),
        ).all()
        assets = set()
        for row in rows:
            assets.add(row.gift_asset)
        return assets

    new_assets = _get_new_online_assets()
    activity_assets = set()
    activity_assets |= _get_ieo_activity_asset()
    activity_assets |= _get_discount_activity_asset()
    activity_assets |= _get_airdrop_activity_asset()
    activity_assets |= _get_trade_rank_activity_asset()
    ret = list(new_assets & activity_assets)
    cache_cls = AssetRankEntryRuleItem3Cache
    cache_cls().save(ret)


@scheduled(crontab(hour='*/1', minute='5'))
@lock_call()
def update_cmc_asset_rate_info_cache():
    """ CMC币种USD汇率信息 """
    CMCAssetRateInfoCache.reload()


@scheduled(60 * 2)
@lock_call()
def update_coin_income_rate_cache():
    data = defaultdict(list)

    for liquidity in json.loads(LiquidityPoolCache().read()):
        base_asset, quote_asset = liquidity['base_asset'], liquidity['quote_asset']
        if quote_asset == 'USDT':
            income_type = CoinIncomeRateCache.IncomeType.AMM_USDT
        elif quote_asset == 'USDC':
            income_type = CoinIncomeRateCache.IncomeType.AMM_USDC
        elif quote_asset == 'BTC':
            income_type = CoinIncomeRateCache.IncomeType.AMM_BTC
        else:
            continue
        data[base_asset].append(
            CoinIncomeRateCache.get_item(income_type, days_7_income_rate=amount_to_str(liquidity['profit_rate']))
        )

    for staking_account in StakingAccount.query.filter(StakingAccount.status == StakingAccount.Status.OPEN).all():
        staking_asset = staking_account.asset
        if staking_data := StakingStatisticsCache(staking_asset).read():
            staking_data = json.loads(staking_data)
            data[staking_account.asset].append(
                CoinIncomeRateCache.get_item(
                    CoinIncomeRateCache.IncomeType.STAKING,
                    year_income_rate=amount_to_str(staking_data['income_rate']),
                )
            )

    for asset, invest_rate in Investment7DaysEARCache().read_data().items():
        data[asset].append(
            CoinIncomeRateCache.get_item(
                CoinIncomeRateCache.IncomeType.INVEST,
                days_7_income_rate=amount_to_str(invest_rate),
            )
        )

    CoinIncomeRateCache().save({asset: json.dumps(data) for asset, data in data.items()})
