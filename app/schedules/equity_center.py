# -*- coding: utf-8 -*-
from datetime import timedelta

from celery.schedules import crontab
from flask import current_app

from app.common import CeleryQueues, Language
from app.models.equity_center import db, EquityType, EquitySendApply
from app.business import lock_call
from app.business.email import send_internal_user_email
from app.business.equity_center.helper import check_sys_balance_enough, EquitySettings, EquityCenterService
from app.business.equity_center.airdrop import send_user_airdrop_equity_task
from app.business.equity_center.cashback import (
    CashbackEquityHelper, CashbackTradeFeeCollector, CashbackSettleHelper, retry_cashback_equity_settle_transfer,
)
from app.business.equity_center.send import create_user_equity_by_send_apply
from app.business.equity_center.notice import clean_up_equity_notice_send_cache_task, send_equity_expiring_notice_task
from app.utils import (
    scheduled,
    route_module_to_celery_queue,
    now,
)


route_module_to_celery_queue(__name__, CeleryQueues.REWARD_CENTER)


@scheduled(crontab(hour="*/1", minute='25'))
@lock_call()
def cashback_manual_collect_trade_fee_schedule():
    """ 返现权益：手动补偿收集交易手续费 """
    now_ = now()
    end_time = now_ - timedelta(minutes=10)
    start_time = end_time - timedelta(days=1)
    c = CashbackTradeFeeCollector(start_time, end_time)
    c.collect()


@scheduled(crontab(hour="0,23", minute='4'))
@lock_call()
def cashback_equity_update_biz_tag_schedule():
    """ 返现权益：更新标签 """
    CashbackEquityHelper.update_biz_tag()


@scheduled(crontab(minute="*/5"))
@lock_call()
def cashback_equity_expire_schedule():
    """ 返现权益：过期 """
    CashbackEquityHelper.update_equities_to_finished_status()


@scheduled(crontab(hour="*/1", minute='1'))
@lock_call()
def cashback_equity_settle_schedule():
    """ 返现权益：结算 """
    helper = CashbackSettleHelper()
    helper.generate_settle_history()
    if EquitySettings.cet_transfer_enabled:
        helper.execute_cashback()


@scheduled(crontab(hour="*/1", minute='41'))
@lock_call()
def retry_cashback_equity_settle_transfer_schedule():
    """ 返现权益的划转重试 """
    if EquitySettings.cet_transfer_enabled:
        retry_cashback_equity_settle_transfer()


@scheduled(crontab(hour="4", minute='11'))
@lock_call()
def cashback_equity_update_settle_his_to_expired_schedule():
    """ 返现权益：已过期的结算记录更新（结算中->已过期） """
    CashbackSettleHelper.update_settle_his_to_expired()


@scheduled(crontab(hour="4", minute='21'))
@lock_call()
def cashback_equity_update_settle_fees_to_invalid_schedule():
    """ 返现权益：无效手续费记录更新 """
    CashbackSettleHelper.update_settle_fees_to_invalid()


@scheduled(crontab(hour="*/1", minute='11'))
@lock_call()
def airdrop_equity_send_schedule():
    """ 空投权益：发放 """
    send_user_airdrop_equity_task()


@scheduled(crontab(hour="*/1", minute='5'))
@lock_call()
def send_equity_notice_schedule():
    send_equity_expiring_notice_task.delay()


@scheduled(crontab(hour="4", minute='45'))
@lock_call()
def clean_up_equity_notice_send_cache_schedule():
    clean_up_equity_notice_send_cache_task.delay()


@scheduled(crontab(hour="*/1", minute='9'))
@lock_call()
def notice_equity_sys_balance_not_enough():
    if not check_sys_balance_enough('CET', EquitySettings.cet_balance_warning_amount):
        title = "【奖励中心】系统账户资金不足，请补充资金"
        content = "系统账户资金不足，无法发放任务奖励，请尽快补充！"
        for email in EquitySettings.cet_balance_warning_emails:
            send_internal_user_email(
                email=email,
                email_content=content,
                subject=title,
                lang=Language.ZH_HANS_CN.value,
            )


@scheduled(crontab(minute="*/10"))
@lock_call()
def equity_send_apply_execute_send_schedule():
    """返现发放申请：执行发放"""
    send_pending_statuses = [
        EquitySendApply.Status.PASSED,
        EquitySendApply.Status.SENDING,
    ]
    apply_rows: list[EquitySendApply] = EquitySendApply.query.filter(
        EquitySendApply.status.in_(send_pending_statuses),
        EquitySendApply.send_at <= now(),
    ).with_entities(
        EquitySendApply.id,
        EquitySendApply.equity_type,
    ).order_by(
        EquitySendApply.id.asc(),
    ).all()
    has_airdrop = False
    for apply in apply_rows:
        try:
            create_user_equity_by_send_apply(apply.id)
            if apply.equity_type == EquityType.AIRDROP:
                has_airdrop = True
        except Exception as _e:
            db.session.rollback()
            current_app.logger.exception(f"create_user_equity_by_send_apply {apply.id} error: {_e!r}")

    if has_airdrop:
        EquityCenterService.async_send_airdrop_equity()
