#!/usr/bin/env python3

import json
from collections import defaultdict
from decimal import Decimal

from celery.schedules import crontab

from app import Language, config
from app.assets import try_get_asset_config
from app.business import PriceManager, lock_call
from app.business.market_maker import MarketMakerHelper
from app.business.statistic import get_user_account_balances
from app.business.user import UserRepository
from app.caches.statistics import AssetUsersStatisticCache, AssetUsersStatisticForLanguageCache
from app.common import CeleryQueues
from app.models import User
from app.models.user import SubAccount
from app.utils import batch_iter, current_timestamp
from app.utils import route_module_to_celery_queue, scheduled, amount_to_str

route_module_to_celery_queue(__name__, CeleryQueues.STATISTIC)


@scheduled(crontab(minute='0', hour='*/1'))
@lock_call()
def update_asset_users_report_schedule():
    market_maker_ids = set(MarketMakerHelper.list_all_maker_ids())
    excluded_user_ids = market_maker_ids | set(UserRepository.merge_sub_account_into_main_ids(config['ASSET_REPORT_IGNORE_USERS'], 
                                                                                              sub_account_type=SubAccount.Type.NORMAL))
    asset_user_balance, account_asset_user_balance = get_user_account_balances(excluded_user_ids)
    account_asset_user_balance['ALL'] = asset_user_balance
    all_user_ids = list(set(sum((list(v.keys()) for v in asset_user_balance.values()), [])))
    user_result = []
    for ids in batch_iter(all_user_ids, 5000):
        user_result.extend(query_users(ids))
    user_locations = {item.id: item.location_code for item in user_result}
    user_kyc_map = {item.id: item.kyc_status for item in user_result}
    update_asset_users_by(
        account_asset_user_balance,
        user_id2key=user_locations,
        cache_cls=AssetUsersStatisticCache,
        user_id2kyc_status=user_kyc_map
    )


def query_users(user_ids):
    from app.models import User
    return User.query.with_entities(User.id, 
                                    User.location_code, 
                                    User.kyc_status).filter(User.id.in_(user_ids)).all()


@scheduled(crontab(minute='0', hour='*/1'))
@lock_call()
def update_asset_users_group_by_language_schedule():
    market_maker_ids = set(MarketMakerHelper.list_all_maker_ids())
    excluded_user_ids = market_maker_ids | set(UserRepository.merge_sub_account_into_main_ids(config['ASSET_REPORT_IGNORE_USERS'], 
                                                                                              sub_account_type=SubAccount.Type.NORMAL))
    asset_user_balance, account_asset_user_balance = get_user_account_balances(excluded_user_ids)
    account_asset_user_balance['ALL'] = asset_user_balance
    all_user_ids = list(set(sum((list(v.keys()) for v in asset_user_balance.values()), [])))
    user_id2language = dict(query_user_preferences(all_user_ids))
    for user_id in set(all_user_ids) - set(user_id2language.keys()):
        user_id2language.update({user_id: Language.EN_US.name})
    update_asset_users_by(
        account_asset_user_balance,
        user_id2key=user_id2language,
        cache_cls=AssetUsersStatisticForLanguageCache
    )


def query_user_preferences(user_ids):
    from app.models import UserPreference as UserPreferenceModel

    for chunk_user_ids in batch_iter(user_ids, 300):
        yield from UserPreferenceModel.query.with_entities(
            UserPreferenceModel.user_id,
            UserPreferenceModel.value,
        ).filter(
            UserPreferenceModel.user_id.in_(chunk_user_ids),
            UserPreferenceModel.key == 'language',
            UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
        ).all()


def _get_balances_exclude(balances, market_maker_ids: set):
    ret = defaultdict(lambda: defaultdict(Decimal))
    for asset, user_balances in balances.items():
        for user_id, balance in user_balances.items():
            if user_id in market_maker_ids:
                continue
            ret[asset][user_id] = balance
    return ret


def update_asset_users_by(balances, user_id2key, cache_cls, user_id2kyc_status=None):
    user_id2kyc_status = user_id2kyc_status or {}
    kyc_user_ids = {user_id for user_id, status in user_id2kyc_status.items() if status == User.KYCStatus.PASSED}
    assets = list(balances['ALL'].keys())
    threshold_map = _get_threshold_map(assets)
    prices = PriceManager.assets_to_usd(assets)
    for account, asset_user_balances in balances.items():
        result = defaultdict(lambda: defaultdict(lambda: dict(
            user_count=0, amount=Decimal(), balance=Decimal(),
            kyc_user_count=0, kyc_user_amount=Decimal(),
            kyc_user_balance=Decimal())))
        alls = defaultdict(lambda: dict(users=set(), amount=Decimal(), kyc_users=set(), kyc_user_amount=Decimal()))
        asset_balances = defaultdict(lambda: defaultdict(Decimal))
        for asset, users in asset_user_balances.items():
            threshold = threshold_map.get(asset, Decimal())
            price = prices.get(asset, 0)
            for user_id, balance in users.items():
                balance_usd = price * balance
                key = user_id2key.get(user_id) or ''  # key is one of (location, language)
                if balance >= threshold:
                    asset_balances[asset][user_id] = balance
                    result[asset][key]['user_count'] += 1
                    alls[key]['users'].add(user_id)
                # 资产直接按用户累计
                result[asset][key]['amount'] += balance_usd
                result[asset][key]['balance'] += balance
                alls[key]['amount'] += balance_usd
                kyc_status = user_id2kyc_status.get(user_id)
                if kyc_status and kyc_status == User.KYCStatus.PASSED:
                    if balance >= threshold:
                        result[asset][key]['kyc_user_count'] += 1
                        alls[key]['kyc_users'].add(user_id)
                    result[asset][key]['kyc_user_amount'] += balance_usd
                    result[asset][key]['kyc_user_balance'] += balance
                    alls[key]['kyc_user_amount'] += balance_usd

        alls_ret = defaultdict(lambda: dict(user_count=set(), amount=Decimal(),
                                            kyc_user_count=set(), kyc_user_amount=Decimal()))
        for key, _all in alls.items():
            alls_ret[key] = {'user_count': len(_all['users']),
                             'amount': _all['amount'],
                             'kyc_user_count': len(_all.get('kyc_users', 0)),
                             'kyc_user_amount': _all.get('kyc_user_amount', 0)}

        alls_ret['total']['user_count'] = len(list(set(sum((list(v.keys()) for v in asset_balances.values()), []))))
        alls_ret['total']['kyc_user_count'] = len(list(set(sum((list(set(v.keys()) & kyc_user_ids) for v in asset_balances.values()), []))))
        alls_ret['total']['amount'] = amount_to_str(sum(v["amount"] for v in alls_ret.values()), 8)
        alls_ret['total']['kyc_user_amount'] = amount_to_str(sum(v.get("kyc_user_amount", 0) for v in alls_ret.values()), 8)
        result['ALL'] = alls_ret
        for data in result.values():
            for v in data.values():
                v['amount'] = amount_to_str(v['amount'], 8)
                if (balance := v.get('balance')) is not None:
                    v['balance'] = amount_to_str(balance, 8)
                if (kyc_user_amount := v.get('kyc_user_amount')) is not None:
                    v['kyc_user_amount'] = amount_to_str(kyc_user_amount, 8)
                if (kyc_user_balance := v.get('kyc_user_balance')) is not None:
                    v['kyc_user_balance'] = amount_to_str(kyc_user_balance, 8)
        cache = cache_cls(account)
        cache.delete()
        result['update_ts'] = current_timestamp(to_int=True)
        cache.hmset({k: json.dumps(v) for k, v in result.items()})


def _get_threshold_map(assets):
    min_amount_map = defaultdict(Decimal)
    for asset in assets:
        if (config := try_get_asset_config(asset)) is None:
            min_amount_map[asset] = Decimal()
        else:
            min_amount_map[asset] = config.asset_user_min_amount or Decimal()
    return min_amount_map
