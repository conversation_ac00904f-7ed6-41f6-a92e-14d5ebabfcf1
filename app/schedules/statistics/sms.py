# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import date, datetime, timedelta
from decimal import Decimal
from celery.schedules import crontab
from app.business.external_dbs import ExchangeLogDB
from app.business.lock import lock_call
from app.common.constants import CeleryQueues
from app.models.mongo.sms import MobileMessageRecordMySQL, MobileMessageSummaryMySQL
from app.models import db
from app.utils import (
    route_module_to_celery_queue, scheduled,
)
from app.utils.date_ import current_timestamp, date_to_datetime, now, timestamp_to_datetime, today
from app.utils.iterable import batch_iter

route_module_to_celery_queue(__name__, CeleryQueues.STATISTIC)


def _update_summary(report_date: date, start: datetime, end: datetime):
    def _add(country, business, field):
        result[(country, business)][field] += 1
        result[('ALL', business)][field] += 1
        result[(country, 'ALL')][field] += 1
        result[('ALL', 'ALL')][field] += 1

    records = (
        MobileMessageRecordMySQL.query
        .filter(
            MobileMessageRecordMySQL.created_at >= start,
            MobileMessageRecordMySQL.created_at < end
        )
        .order_by(MobileMessageRecordMySQL.created_at.desc())
        .all()
    )
    result = defaultdict(lambda: defaultdict(int))
    user_set = set()
    binded_mobile_user_set = set()
    non_binded_mobile_user_set = set()
    records = list(records)
    records.sort(key=lambda x: x.created_at, reverse=True)
    for record in records:
        record: MobileMessageRecordMySQL
        user_id = record.user_id
        _add(record.country, record.business, 'message_count')
        if user_id:
            _add(record.country, record.business, 'login_message_count')
        else:
            _add(record.country, record.business, 'non_login_message_count')
        if user_id and user_id not in user_set:
            user_set.add(user_id)
            _add(record.country, record.business, 'user_count')
        
        if user_id and record.has_user_binded_mobile \
            and (user_id not in binded_mobile_user_set and user_id not in non_binded_mobile_user_set):
            binded_mobile_user_set.add(user_id)
            _add(record.country, record.business, 'mobile_user_count')
        
        if user_id and not record.has_user_binded_mobile \
            and (user_id not in binded_mobile_user_set and user_id not in non_binded_mobile_user_set):
            non_binded_mobile_user_set.add(user_id)
            _add(record.country, record.business, 'non_mobile_user_count')
    
    # Delete existing records
    MobileMessageSummaryMySQL.query.filter(
        MobileMessageSummaryMySQL.report_date == date_to_datetime(report_date)
    ).delete()

    # Insert new records
    for k, v in result.items():
        country, business = k
        summary = MobileMessageSummaryMySQL(
            report_date=date_to_datetime(report_date),
            last_report_at=end,
            country=country,
            business=business,
            message_count=v['message_count'],
            login_message_count=v['login_message_count'],
            non_login_message_count=v['non_login_message_count'],
            user_count=v['user_count'],
            mobile_user_count=v['mobile_user_count'],
            non_mobile_user_count=v['non_mobile_user_count']
        )
        db.session.add(summary)
    db.session.commit()


@scheduled(crontab(minute='20,40', hour='*/1'))
@lock_call()
def update_mobile_message_summary_schedule():
    current_ts = current_timestamp(to_int=True)
    current_ts -= current_ts % 3600
    report_at = timestamp_to_datetime(current_ts)
    # 0点更新昨天的
    if report_at.hour == 0:
        report_date = today() - timedelta(days=1)
    else:
        report_date = today()
    r = MobileMessageSummaryMySQL.query.filter(
        MobileMessageSummaryMySQL.report_date == report_date
    ).first()
    if r and r.last_report_at == report_at:
        return
    start = date_to_datetime(report_date)
    end = report_at
    _update_summary(report_date, start, end)


def _get_user_balance_usd(user_ids):
        mapping = {}
        ts = int(now().timestamp())
        last_ts = ts - ts % 86400
        table = ExchangeLogDB.user_account_balance_sum_table(last_ts)
        for chunk_user_ids in batch_iter(user_ids, 5000):
            user_id_str = ','.join(map(str, chunk_user_ids))
            rows = table.select(
                'user_id', 'balance_usd',
                where=f' user_id in ({user_id_str})'
            )
            for (user_id, balance_usd) in rows:
                mapping.update({user_id: balance_usd})
        return mapping


@scheduled(crontab(minute='10,40', hour='*/1'))
@lock_call()
def update_mobile_message_user_balance_schedule():
    current_ts = current_timestamp(to_int=True)
    current_ts -= current_ts % 3600
    end = timestamp_to_datetime(current_ts)
    # 零点重试昨天的
    if now().hour == 0:
        start = end - timedelta(days=1)
    else:
        start = end - timedelta(hours=1)
    records = MobileMessageRecordMySQL.query.filter(
        MobileMessageRecordMySQL.created_at >= start,
        MobileMessageRecordMySQL.created_at < end,
        MobileMessageRecordMySQL.balance.is_(None)
    ).all()
    user_ids = {item.user_id for item in records if item.user_id}
    id_user_id_map = {item.id: item.user_id for item in records if item.user_id}
    balance_map = _get_user_balance_usd(user_ids)
    id_balance_map = {}
    for k, v in id_user_id_map.items():
        id_balance_map[k] = balance_map.get(v, Decimal())
    # Update records with balance
    for record in records:
        if record.id in id_balance_map:
            record.balance = id_balance_map[record.id]
    db.session.commit()
