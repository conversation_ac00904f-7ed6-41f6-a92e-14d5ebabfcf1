# -*- coding: utf-8 -*-

from collections import defaultdict
from decimal import Decimal
import json
from celery.schedules import crontab
from sqlalchemy import func
from app.business import lock_call
from app.business.external_dbs import TradeLogDB
from app.business.pledge.helper import USDT_ASSET, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>el<PERSON>, get_cached_market_index_prices, get_loan_asset_info
from app.business.prices import PriceManager
from app.caches.statistics import PledgeStatisticsCache, PledgeStatisticsSumamryCache
from app.common import CeleryQueues
from app.common.constants import PrecisionEnum, ServerBalanceType
from app.models.pledge import LoanAsset, PledgeAsset, PledgePosition
from app.utils import scheduled, route_module_to_celery_queue
from app.utils.amount import amount_to_str
from app.utils.date_ import current_timestamp

route_module_to_celery_queue(__name__, CeleryQueues.STATISTIC)

batch_size = 5000

_ALL_ASSETS = 'ALL_ASSETS'

def _get_snapshot_balance(ts, account_id):
    table = TradeLogDB.slice_balance_table(ts)
    if not table:
        return
    fields = ('user_id', 'asset', 'balance')
    results = table.select(
            "user_id", "asset", 'SUM(`balance`) `balance`',
            where=f"account={account_id} "
                    f"and t = {ServerBalanceType.AVAILABLE.value}",
            group_by='`user_id`, `asset`'
            )
    results = [dict(zip(fields, r)) for r in results]
    return results

def _loan_asset_sum_value(loan_asset, value_map):
    return sum([v for k, v in value_map.items() if k[0] == loan_asset])


@scheduled(crontab(minute='20,50', hour='*/1'))
@lock_call()
def update_pledge_position_schedule():
    user_count_items = PledgePosition.query.filter(
        PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES)
    ).group_by(PledgePosition.loan_asset).with_entities(
        PledgePosition.loan_asset,
        func.count(PledgePosition.user_id.distinct())
    ).all()
    user_count_map = dict(user_count_items)
    price_map = get_cached_market_index_prices()
    price_map = {k: Decimal(v) for k, v in price_map.items()}
    assets = list(user_count_map.keys())

    ts = current_timestamp(to_int=True)
    ts -= ts % 3600
    amount_map, value_map, valid_value_map, user_ids_map = defaultdict(Decimal), \
        defaultdict(Decimal), defaultdict(Decimal), defaultdict(set)
    
    config_map = {}
    summary_map, detail_map = dict(), defaultdict(list)

    _usdt_price = PriceManager.asset_to_usd(USDT_ASSET)
    for asset in assets:
        # all_cache = PledgeStatisticsCache(asset)
        # if data:= all_cache.read():
            # if json.loads(data)['updated_at'] == ts: FIXME: add this for production
            #     continue
        
        account_id = get_loan_asset_info(asset).account_id
        balance_records = _get_snapshot_balance(ts, account_id)

        config_map.update(PledgeValueHelper.get_pledge_asset_info_dict({item['asset'] for item in balance_records}))
        all_user_ids = set()
        pledge_assets = set()
        for item in balance_records:
            conf = config_map.get(item['asset'])
            if not conf:
                continue
            pledge_assets.add(item['asset'])
            key = (asset, item['asset'])
            
            index_price = PledgeValueHelper.get_asset_index_price(item['asset'], price_map)
            amount = item['balance']
            value = amount * index_price * _usdt_price
            valid_value = min(item['balance'] * index_price * conf['collateral_ratio'] * _usdt_price, 
                              conf['max_pledge_usd'])
            amount_map[key] += amount
            value_map[key] += value
            valid_value_map[key] += valid_value
            
            all_key = (_ALL_ASSETS, item['asset'])
            amount_map[all_key] += amount
            value_map[all_key] += value
            valid_value_map[all_key] += valid_value

            user_ids_map[key].add(item['user_id'])
            user_ids_map[all_key].add(item['user_id'])
        for pledge_asset in pledge_assets:
            key = (asset, pledge_asset)
            detail_map[asset].append(dict(
                pledge_asset=pledge_asset,
                user_count=len(user_ids_map[key]),
                amount=amount_to_str(amount_map[key], PrecisionEnum.COIN_PLACES),
                value=amount_to_str(value_map[key], PrecisionEnum.CASH_PLACES),
                valid_value=amount_to_str(valid_value_map[key], PrecisionEnum.CASH_PLACES),
            ))
            summary_map[key] = dict(
                pledge_asset=pledge_asset,
                user_count=len(user_ids_map[key]),
                amount=amount_to_str(amount_map[key], PrecisionEnum.COIN_PLACES),
                value=amount_to_str(value_map[key], PrecisionEnum.CASH_PLACES),
                valid_value=amount_to_str(valid_value_map[key], PrecisionEnum.CASH_PLACES),
            )
        asset_user_ids = set()
        for k, v in user_ids_map.items():
            if k[0] == asset:
                asset_user_ids.update(v)
            all_user_ids.update(v)
        summary_map[(asset, _ALL_ASSETS)] = dict(
            user_count=len(asset_user_ids),
            amount='0', # 多币种不需要总数量
            value=amount_to_str(_loan_asset_sum_value(asset, value_map), PrecisionEnum.CASH_PLACES),
            valid_value=amount_to_str(_loan_asset_sum_value(asset, valid_value_map), PrecisionEnum.CASH_PLACES),
        )
    all_value = sum(value_map.values())
    all_user_count = len(all_user_ids)
    all_valid_value = sum(valid_value_map.values())
    summary_map[(_ALL_ASSETS, _ALL_ASSETS)] = dict(
        user_count=all_user_count,
        amount='0',
        value=amount_to_str(all_value, PrecisionEnum.CASH_PLACES),
        valid_value=amount_to_str(all_valid_value, PrecisionEnum.CASH_PLACES),
    )
    all_pledge_assets = {k[1] for k in amount_map.keys()}
    
    for asset in all_pledge_assets:
        key = (_ALL_ASSETS, asset) # 选择质押币种，借币币种为ALL
        detail_map[_ALL_ASSETS].append(dict(
            pledge_asset=asset,
            user_count=len(user_ids_map[key]),
            amount=amount_to_str(amount_map[key], PrecisionEnum.COIN_PLACES),
            value=amount_to_str(value_map[key], PrecisionEnum.CASH_PLACES),
            valid_value=amount_to_str(valid_value_map[key], PrecisionEnum.CASH_PLACES),
        ))

        summary_map[(_ALL_ASSETS, asset)] = dict(
            pledge_asset=asset,
            user_count=len(user_ids_map[key]),
            amount=amount_to_str(amount_map[key], PrecisionEnum.COIN_PLACES),
            value=amount_to_str(value_map[key], PrecisionEnum.CASH_PLACES),
            valid_value=amount_to_str(valid_value_map[key], PrecisionEnum.CASH_PLACES),
        )
    
    pledge_assets = PledgeAsset.query.all()
    pledge_assets = {asset.asset for asset in pledge_assets}

    loan_assets = LoanAsset.query.all()
    loan_assets = {asset.asset for asset in loan_assets}
    # 补上0记录
    for loan_asset in [_ALL_ASSETS, *loan_assets]:
        for pledge_asset in [_ALL_ASSETS, *pledge_assets]:
            key = (loan_asset, pledge_asset)
            if key not in summary_map:
                summary_map[key] = dict(
                    pledge_asset=pledge_asset,
                    user_count=0,
                    amount='0',
                    value='0',
                    valid_value='0',
                )
    assets = list(loan_assets | set(assets))
    all_pledge_assets = list(pledge_assets | set(all_pledge_assets))
    for loan_asset in [_ALL_ASSETS, *assets]:
        detail = detail_map.get(loan_asset, [])
        if detail:
            cache = PledgeStatisticsCache(loan_asset)
            cache.set(json.dumps(dict(updated_at=ts, detail=detail)))
        for pledge_asset in [_ALL_ASSETS, *all_pledge_assets]:
            summary = summary_map.get((loan_asset, pledge_asset), {})
            if summary:
                summary_cache = PledgeStatisticsSumamryCache(loan_asset, pledge_asset)
                summary_cache.set(json.dumps(dict(updated_at=ts, summary=summary)))