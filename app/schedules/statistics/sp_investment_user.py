# -*- coding: utf-8 -*-
from datetime import date, timedelta
from decimal import Decimal

from celery.schedules import crontab
from collections import defaultdict
from flask import current_app

from app.business import lock_call, ExchangeLogDB, send_alert_notice, PriceManager
from app.business.amm import LiquidityService
from app.config import config
from app.common import CeleryQueues
from app.models import db, InvestmentUserAMMSlice, InvestmentUserSpotSlice, AssetPrice, \
    LiquidityHistory, CoinInformation, UserLiquidity
from app.utils import (
    route_module_to_celery_queue, scheduled, quantize_amount
)
from app.utils.date_ import today, datetime_to_time, date_to_datetime

route_module_to_celery_queue(__name__, CeleryQueues.STATISTIC)


def _get_asset_online_time_map():
    rows = CoinInformation.query.with_entities(
        CoinInformation.code,
        CoinInformation.online_time
    ).all()
    return {row.code: row.online_time for row in rows}


def _get_asset_online_days(asset, asset_online_time_map, report_date):
    online_dt = asset_online_time_map.get(asset)
    if not online_dt:
        return 0
    return (report_date - online_dt.date()).days


def asset_amounts_to_usd(base_asset, quote_asset, base_amount, quote_amount, prices):
    usd = base_amount * prices.get(base_asset, 0) + quote_amount * prices.get(quote_asset, 0)
    return quantize_amount(usd, 8)


def get_pool_profit_rate(srv: LiquidityService, pool_base: Decimal, pool_quote: Decimal, days: int):
    deal_info = srv.get_recent_deal_sum(days=days)
    if (deal_days := deal_info['days']) == 0:
        profit_rate = Decimal()
    else:
        profit_rate = srv.calc_profit_rate(
            deal_info['fee_base_amount'] / deal_days,
            deal_info['fee_quote_amount'] / deal_days,
            pool_base,
            pool_quote
        )
    return profit_rate


def update_sp_investment_user_amm_slice(report_date: date):
    investment_user_id = config['INVESTMENT_USER_ID']
    rows = UserLiquidity.query.filter(
        UserLiquidity.user_id == investment_user_id,
        UserLiquidity.liquidity > 0
    ).all()
    prices = PriceManager.assets_to_usd()
    market_liquidity_cost_map = _get_market_liquidity_cost_map()
    asset_online_time_map = _get_asset_online_time_map()
    alert_markets = set()
    new_rows = []
    for r in rows:
        market = r.market
        srv = LiquidityService(r.market)
        base_asset, quote_asset = srv.market['base_asset'], srv.market['quote_asset']
        pool_base, pool_quote = srv.get_assets_amount()
        pool_liquidity = srv.pool.liquidity
        liquidity = r.liquidity
        base_amount, quote_amount = srv.liquidity_to_asset_amounts(liquidity, pool_base, pool_quote)
        liquidity_usd = asset_amounts_to_usd(base_asset, quote_asset, base_amount, quote_amount, prices)
        if liquidity_usd < Decimal(10):
            continue
        liquidity_cost = market_liquidity_cost_map.get(market, Decimal(0))
        liquidity_usd_profit_rate = ((liquidity_usd - liquidity_cost) / liquidity_cost) \
            if liquidity_cost else Decimal(0)
        if liquidity_usd_profit_rate > Decimal(1):
            alert_markets.add(market)

        new_rows.append(InvestmentUserAMMSlice(
            report_date=report_date,
            market=market,
            base_asset=base_asset,
            quote_asset=quote_asset,
            online_days=_get_asset_online_days(base_asset, asset_online_time_map, report_date),
            base_amount=base_amount,
            quote_amount=quote_amount,
            liquidity_cost=liquidity_cost,
            liquidity=liquidity,
            liquidity_ratio=liquidity / pool_liquidity if pool_liquidity else Decimal(0),
            liquidity_usd=liquidity_usd,
            liquidity_usd_profit_rate=liquidity_usd_profit_rate,
            profit_rate_7d=get_pool_profit_rate(srv, pool_base, pool_quote, 7),
            profit_rate_30d=get_pool_profit_rate(srv, pool_base, pool_quote, 30),
        ))
    db.session.bulk_save_objects(new_rows)
    db.session.commit()

    if alert_markets:
        send_amm_alert(alert_markets)


def send_amm_alert(markets):
    market_str = ', '.join(markets)
    content = f"【流动性市值盈利率告警】\n市场{market_str}盈利率超过100%"
    send_alert_notice(
        content,
        config["ADMIN_CONTACTS"]["sp_investment_notice"]
    )


def _get_market_liquidity_cost_map():
    investment_user_id = config['INVESTMENT_USER_ID']
    rows = LiquidityHistory.query.filter(
        LiquidityHistory.user_id == investment_user_id,
        LiquidityHistory.status == LiquidityHistory.Status.FINISHED
    ).with_entities(
        LiquidityHistory.market,
        LiquidityHistory.business,
        LiquidityHistory.liquidity_usd,
    ).order_by(LiquidityHistory.id).all()
    result = defaultdict(Decimal)
    for r in rows:
        if r.business == LiquidityHistory.Business.ADD:
            result[r.market] += r.liquidity_usd
        else:
            result[r.market] = Decimal(0)
    return result


@scheduled(crontab(minute="5", hour='1-3'))
@lock_call()
def sp_investment_user_amm_slice_schedule():
    today_ = today()
    last_record = InvestmentUserAMMSlice.query.order_by(
        InvestmentUserAMMSlice.report_date.desc()
    ).first()
    if last_record and today_ == last_record.report_date:
        return

    update_sp_investment_user_amm_slice(today_)


def update_sp_investment_user_spot_slice(report_date: date):
    ts = datetime_to_time(date_to_datetime(report_date))
    table = ExchangeLogDB.user_balance_table(ts)
    if not table or not table.exists():
        current_app.logger.warning(f'update_sp_investment_user_spot_slice pre data not ready, ts: {ts}')
        return

    asset_balance_map = defaultdict(Decimal)
    investment_user_id = config['INVESTMENT_USER_ID']
    where = f"`user_id` = {investment_user_id} AND `account` = 0"
    rows = table.select('asset', 'balance', where=where)
    for r in rows:
        asset_balance_map[r[0]] += r[1]
    asset_price_map = AssetPrice.get_close_price_map(report_date - timedelta(days=1))
    asset_online_time_map = _get_asset_online_time_map()
    avg_price_map = _get_avg_price_map(report_date - timedelta(days=1))

    new_rows = []
    for asset, balance in asset_balance_map.items():
        price = asset_price_map.get(asset, Decimal())
        usd = balance * price
        if usd < Decimal(10):
            continue
        avg_price = avg_price_map.get(asset, Decimal())
        profit_rate = (price - avg_price) / avg_price if avg_price else Decimal(0)
        new_rows.append(InvestmentUserSpotSlice(
            report_date=report_date,
            asset=asset,
            online_days=_get_asset_online_days(asset, asset_online_time_map, report_date),
            price=price,
            avg_price=avg_price,
            amount=balance,
            usd=usd,
            profit_rate=profit_rate,
        ))
    db.session.bulk_save_objects(new_rows)
    db.session.commit()


def _get_avg_price_map(report_date: date):
    rows = InvestmentUserSpotSlice.query.filter(
        InvestmentUserSpotSlice.report_date == report_date
    ).with_entities(
        InvestmentUserSpotSlice.asset,
        InvestmentUserSpotSlice.avg_price,
    )
    return {r.asset: r.avg_price for r in rows}


@scheduled(crontab(minute="5", hour='1-3'))
@lock_call()
def sp_investment_user_spot_slice_schedule():
    today_ = today()
    last_record = InvestmentUserSpotSlice.query.order_by(
        InvestmentUserSpotSlice.report_date.desc()
    ).first()
    if last_record and today_ == last_record.report_date:
        return

    update_sp_investment_user_spot_slice(today_)
