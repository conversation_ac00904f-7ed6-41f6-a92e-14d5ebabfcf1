import json
from collections import defaultdict
import datetime
from decimal import Decimal
from typing import Dict, Set, Any

from celery.schedules import crontab
from flask import current_app

from app import config
from app.business import PerpetualServerClient, PerpetualSysHistoryDB, lock_call, PriceManager, cached
from app.business.alert import send_alert_notice
from app.business.risk_control.base import RiskControlGroupConfig
from app.business.third_exchange import GateAPIClient, \
    BybitAPIClient, OKXAPIClient, BinanceAPIClient, KucoinAPIClient
from app.business.perpetual.position import get_adl_liq_positions
from app.caches.admin import RealTimePerpetualMonitorCache, \
    PerpetualMonitorSpecialFieldCache, SignPriceBasisRateCache, RealTimePerpetualLiquidationCache
from app.caches.alarms import PerpetualMonitorAlertCache
from app.common import CeleryQueues, PerpetualMarketType, PositionSide
from app.models import PerpetualMarket, TipBar
from app.schedules.statistics.depth_snapshot import get_perpetual_market_order_depth_data
from app.schedules.statistics.market_monitor.base import update_depth_data, save_monitor_cache, save_series_cache, \
    filter_create_params, gen_auto_tip_bars, get_basis_rate_trigger_count_map
from app.schedules.statistics.realtime_asset import PerpetualRealTimeDepthCalculator
from app.utils import route_module_to_celery_queue, scheduled, \
    quantize_amount, current_timestamp, format_percent, amount_to_str, now
from app.utils.parser import JsonEncoder

route_module_to_celery_queue(__name__, CeleryQueues.STATISTIC)


@scheduled(crontab(minute='*/1'))
@lock_call()
def perpetual_monitor_schedule():
    """合约市场数据监控"""
    res = defaultdict(lambda: {
        'market': '',
        'funding_rate': Decimal(),
        'funding_rate_predict': Decimal(),
        'funding_interval': 8,
        'bid_ask_diff': Decimal(),
        'bid_ask_diff_rate': Decimal(),
        'last_price': Decimal(),
        'sign_price': Decimal(),
        'index_price': Decimal(),
        'sign_price_basis_rate': Decimal(),
        'index_price_basis_rate': Decimal(),
        'depth1_neg': Decimal(),
        'depth1_pos': Decimal(),
        'depth4_neg': Decimal(),
        'depth4_pos': Decimal(),
        'depth_1_long_short_rate': Decimal(),
        'depth_4_long_short_rate': Decimal(),
        'insurance_loss': Decimal(),
        'third_exchange': defaultdict(lambda: {
            'exchange': '',
            'last_price': Decimal(),
            'index_price': Decimal(),
            'sign_price': Decimal(),
            'index_price_basis_rate': Decimal(),
            'funding_rate': Decimal(),
            'funding_interval': 8,
        }),
    })
    perpetual_client = PerpetualServerClient()
    market_prec_dic, market_asset_dic = update_market_data(res, perpetual_client)
    update_price_and_bid_ask_diff(res, perpetual_client, market_prec_dic)
    update_depth_data(
        res,
        PerpetualRealTimeDepthCalculator,
        get_perpetual_market_order_depth_data,
        fields=("depth", "market", "money_rate", "stock_rate", "data", "snapshot_ts"),
    )
    update_insurance_loss(res)
    update_third_exchange_data(res)
    save_monitor_cache(RealTimePerpetualMonitorCache, res)
    save_series_cache(PerpetualMonitorSpecialFieldCache, res)


def update_market_data(res, perpetual_client):
    records = PerpetualMarket.query.filter(
        PerpetualMarket.status == PerpetualMarket.StatusType.OPEN
    ).with_entities(
        PerpetualMarket.name,
        PerpetualMarket.quote_asset_precision,
        PerpetualMarket.funding_interval,
        PerpetualMarket.quote_asset,
        PerpetualMarket.base_asset,
        PerpetualMarket.market_type,
    ).all()
    market_prec_dic = {}
    market_asset_dic = {}
    for record in records:
        market = record.name
        quote_asset_precision = record.quote_asset_precision
        funding_interval = record.funding_interval
        market_type = record.market_type
        asset = record.quote_asset if market_type == PerpetualMarketType.DIRECT else record.base_asset
        res[market]['market'] = market
        res[market]['funding_interval'] = funding_interval
        market_prec_dic[market] = quote_asset_precision
        market_asset_dic[market] = asset
    ticker_records = perpetual_client.get_market_status()
    for market, item in ticker_records.items():
        if market not in res:
            continue
        res[market]['funding_rate'] = item['funding_rate_next']
        res[market]['funding_rate_predict'] = item['funding_rate_predict']

    return market_prec_dic, market_asset_dic


def update_price_and_bid_ask_diff(res, perpetual_client, market_prec_dic):

    for market, item in res.items():
        orders = perpetual_client.market_order_depth(market=market, limit=1, interval='0')
        bid_price = ask_price = Decimal()
        if orders['asks']:
            ask_price = Decimal(orders['asks'][0][0])
        if orders['bids']:
            bid_price = Decimal(orders['bids'][0][0])
        prec = market_prec_dic[market]
        bid_ask_diff = quantize_amount(ask_price - bid_price, prec)
        last_price = Decimal(orders['last'])
        sign_price = Decimal(orders['sign_price'])
        index_price = Decimal(orders['index_price'])
        sign_price_basis_rate = quantize_amount((last_price-sign_price)*10000/sign_price, 2) if sign_price and last_price else 0
        index_price_basis_rate = quantize_amount((last_price-index_price)*10000/index_price, 2) if index_price and last_price else 0
        bid_ask_diff_rate = quantize_amount(bid_ask_diff * 10000/ last_price, 2) if last_price else 0
        item['bid_ask_diff'] = bid_ask_diff
        item['bid_ask_diff_rate'] = bid_ask_diff_rate
        item['last_price'] = last_price
        item['sign_price'] = sign_price
        item['index_price'] = index_price
        item['sign_price_basis_rate'] = sign_price_basis_rate
        item['index_price_basis_rate'] = index_price_basis_rate

def update_insurance_loss(res):
    """穿仓损失"""
    end = current_timestamp(to_int=True)
    start = end - 3600
    market_loss_dic = PerpetualSysHistoryDB.get_markets_insurance_summary(2, start, end)
    for market, item in res.items():
        insurance_loss = market_loss_dic.get(market, 0)
        item['insurance_loss'] = insurance_loss


def update_third_exchange_data(res):
    ordered_third_exchange_handlers = [
        BinancePerpetualHandler,
        KucoinPerpetualHandler,
        OkxPerpetualHandler,
        BybitPerpetualHandler,
        GatePerpetualInfoHandler,
    ]
    updated_markets = set()
    for handler in ordered_third_exchange_handlers:
        handler().update_third_exchange_data(res, updated_markets)


class BasePerpetualHandler:
    tag: str
    client: Any

    def update_third_exchange_data(self, res: Dict, updated_markets: Set):
        try:
            self.update(res, updated_markets)
        except Exception as e:
            current_app.logger.error(f'{self.__class__.__name__}更新失败，msg：{e}')

    def update(self, res: Dict, updated_markets: Set):
        raise NotImplementedError


class BinancePerpetualHandler(BasePerpetualHandler):
    tag = 'Binance'

    def __init__(self):
        self.client = BinanceAPIClient()

    def update(self, res: Dict, updated_markets: Set):
        for market_type in ['u', 'c']:
            self._update(res, updated_markets, market_type)

    def _update(self, res, updated_markets, market_type):
        if market_type == 'u':
            last_price_dic = self.client.fetch_u_last_prices()
            index_sign_price_funding_rate_dic = self.client.fetch_u_funding_rate_index_sign_prices()
            funding_interval_dic = self.get_u_funding_interval_dic()
        else:
            last_price_dic = self.client.fetch_c_last_prices()
            index_sign_price_funding_rate_dic = self.client.fetch_c_funding_rate_index_sign_prices()
            # 因币本位合约无批量获取资金费率配置接口，因此需要每个市场单独获取
            c_markets = {i for i in last_price_dic if '_PERP' in i}
            funding_interval_dic = self.get_c_funding_interval_dic(c_markets)
        for symbol, last_price in last_price_dic.items():
            market = self.trans_to_our_market(symbol, market_type)
            if market not in res or market in updated_markets:
                continue
            if not (info := index_sign_price_funding_rate_dic.get(symbol)):
                continue
            index_price = info['index_price']
            index_price_basis_rate = quantize_amount((last_price - index_price) * 10000 / index_price, 2)
            funding_interval = funding_interval_dic.get(symbol)
            item = dict()
            item['last_price'] = last_price
            item.update(info)
            item.update({
                'exchange': self.tag,
                'funding_interval': funding_interval,
                'index_price_basis_rate': index_price_basis_rate,
            })
            res[market]['third_exchange'] = item
            updated_markets.add(market)

    @cached(ttl=3600)
    def get_u_funding_interval_dic(self):
        return self.client.get_u_funding_intervals()

    @cached(ttl=3600)
    def get_c_funding_interval_dic(self, c_markets: set):
        res = dict()
        for symbol in c_markets:
            interval = self.client.fetch_c_funding_interval(symbol)
            res[symbol] = interval
        return res

    @staticmethod
    def trans_to_our_market(symbol, market_type):
        if market_type == 'u':
            return symbol
        else:
            return symbol.replace('_PERP', '')


class KucoinPerpetualHandler(BasePerpetualHandler):
    tag = 'Kucoin'

    def __init__(self):
        self.client = KucoinAPIClient()

    def update(self, res: Dict, updated_markets: Set):
        symbol_info_dic = self.client.fetch_price_funding_infos()
        for symbol, item in symbol_info_dic.items():
            market = self.trans_to_our_market(symbol, item['base_asset'])
            if market not in res or market in updated_markets:
                continue
            item.pop('base_asset')
            item['exchange'] = self.tag
            res[market]['third_exchange'] = item

    @staticmethod
    def trans_to_our_market(symbol, base_asset):
        """ETHUSDTM -> ETHUSDT; XBTUSDTM -> BTCUSDT"""
        market = symbol[:-1]
        if base_asset == 'XBT':
            market = market.replace('XBT', 'BTC')
        return market


class OkxPerpetualHandler(BasePerpetualHandler):
    tag = 'OKX'

    def __init__(self):
        self.client = OKXAPIClient()

    def update(self, res: Dict, updated_markets: Set):
        last_prices = self.client.fetch_last_prices()
        sign_prices = self.client.fetch_sign_prices()
        index_prices = self._merge_index_prices()
        for inst_id, last_price in last_prices.items():
            market = self.trans_to_our_market(inst_id)
            if market not in res or market in updated_markets:
                continue
            funding_rate_info = self.client.fetch_funding_rate(inst_id)
            sign_price = sign_prices[inst_id]
            index_market = self.trans_to_index_market(inst_id)
            index_price = index_prices.get(index_market, 0)
            index_price_basis_rate = quantize_amount((last_price - index_price) * 10000 / index_price, 2)
            item = {'exchange': self.tag,
                    'last_price': last_price,
                    'index_price': index_price,
                    'sign_price': sign_price,
                    'index_price_basis_rate': index_price_basis_rate,
                    }
            item.update(funding_rate_info)
            res[market]['third_exchange'] = item
            updated_markets.add(market)

    @staticmethod
    def trans_to_our_market(symbol):
        return symbol.replace('-SWAP', '').replace('-', '')

    @staticmethod
    def trans_to_index_market(symbol):
        return symbol.replace('-SWAP', '')

    def _merge_index_prices(self):
        usdt_index_prices = self.client.fetch_usdt_index_prices()
        usdc_index_prices = self.client.fetch_usdc_index_prices()
        usd_index_prices = self.client.fetch_usd_index_prices()
        btc_index_prices = self.client.fetch_btc_index_prices()
        return {**usdt_index_prices, **usdc_index_prices, **usd_index_prices, **btc_index_prices}


class BybitPerpetualHandler(BasePerpetualHandler):
    tag = 'Bybit'

    def __init__(self):
        self.client = BybitAPIClient()

    def update(self, res: Dict, updated_markets: Set):
        ticker_data = self.client.fetch_ticker_data()
        funding_interval_dic = self.client.fetch_funding_interval()
        for symbol, item in ticker_data.items():
            market = self.trans_to_our_market(symbol)
            if market not in res or market in updated_markets:
                continue
            funding_interval = funding_interval_dic[market]
            item.update({
                'exchange': self.tag,
                'funding_interval': funding_interval
            })
            res[market]['third_exchange'] = item
            updated_markets.add(market)

    @staticmethod
    def trans_to_our_market(symbol):
        return symbol


class GatePerpetualInfoHandler(BasePerpetualHandler):
    tag = 'Gate'

    def __init__(self):
        self.client = GateAPIClient()

    def update(self, res: Dict, updated_markets: Set):
        query_markets_dic = self._merge_markets_data()
        for symbol, item in query_markets_dic.items():
            market = self.trans_to_our_market(symbol)
            if market not in res or market in updated_markets:
                continue
            item.update({'exchange': self.tag})
            res[market]['third_exchange'] = item
            updated_markets.add(market)

    def _merge_markets_data(self):
        u_data = self.client.fetch_u_price_and_funding_info()
        c_data = self.client.fetch_c_price_and_funding_info()
        return {**c_data, **u_data}

    @staticmethod
    def trans_to_our_market(symbol):
        return symbol.replace('_', '')


@scheduled(crontab(minute='*/1'))
@lock_call()
def perpetual_unusual_alert_schedule():
    """合约市场数据异常告警"""
    now_ = current_timestamp(to_int=True)
    cache = PerpetualMonitorAlertCache()
    undisturbed_market_dic = cache.get_undisturbed_markets()
    sign_price_basis_alert_markets = check_sign_price_basis_rate_alert(undisturbed_market_dic['sign_price_basis_rate'], now_)
    funding_rate_diff_alert_markets = check_funding_rate_diff_alert(undisturbed_market_dic['funding_rate_diff'], now_)
    save_to_cache(cache, undisturbed_market_dic, sign_price_basis_alert_markets, funding_rate_diff_alert_markets)


def check_sign_price_basis_rate_alert(undisturbed_markets, now_):
    cache = SignPriceBasisRateCache()
    latest_data_num = 60
    records_dic = cache.get_latest_markets_data(latest_data_num)
    threshold = RiskControlGroupConfig().sign_price_basis_rate
    before_records_dic = {market: items[:latest_data_num-1] for market, items in records_dic.items()}
    before_trigger_count_map = get_basis_rate_trigger_count_map(before_records_dic, threshold)
    alert_market_dic = dict()
    trigger_market_dic = dict()
    for market, basis_items in records_dic.items():
        flag = True
        for item in basis_items[-5:]:
            if abs(Decimal(item[1])) < threshold:
                flag = False
                break
        if flag:
            basis_rate = Decimal(basis_items[-1][1])
            trigger_market_dic[market] = basis_rate
            if market not in undisturbed_markets:
                alert_market_dic[market] = basis_rate
            elif now_ > undisturbed_markets[market]['ts'] + PerpetualMonitorAlertCache.ttl_field_mapping['sign_price_basis_rate']:
                if before_trigger_count_map.get(market, 0) <= 0 \
                        or abs(basis_rate) >= abs(Decimal(undisturbed_markets[market]['value'])) * Decimal(1.5):
                    alert_market_dic[market] = basis_rate
    if alert_market_dic:
        content = gen_basis_alert_msg(alert_market_dic, threshold)
        send_alert_notice(content, config["ADMIN_CONTACTS"]["market_notice"])
    if trigger_market_dic:
        check_sign_price_basis_rate_tip_bar_alert(trigger_market_dic)

    return {k: {'ts': now_, 'value': v} for k, v in alert_market_dic.items()}


def gen_basis_alert_msg(alert_market_dic, threshold):
    reminder_slack_ids = []
    remind_str = ''
    for reminder in reminder_slack_ids:
        remind_str += f'<@{reminder}>'
    content = f"""【合约标记价格基差率告警】"""
    for market, basis in alert_market_dic.items():
        line = f"""
        市场：{market}
        该市场「标记价格基差率」为{basis}（阈值为{threshold}），请及时查看处理
        """
        content += line
    content += remind_str
    return content


def check_sign_price_basis_rate_tip_bar_alert(market_dic):
    markets = list(market_dic.keys())
    risk_config = RiskControlGroupConfig().perpetual_basis_rate_auto_tip
    basis_rate_threshold = Decimal(risk_config['perpetual_tip_sign_price_basis_rate'])
    trigger_count_threshold = int(risk_config['perpetual_tip_trigger_count'])
    tip_duration_hours = int(risk_config['perpetual_tip_duration_hours'])

    trigger_count_map = get_perpetual_basis_rate_trigger_count_map(180)
    alert_market_set = set()
    for market, basis_rate in market_dic.items():
        if abs(Decimal(basis_rate)) >= basis_rate_threshold:
            alert_market_set.add(market)
        if trigger_count_map.get(market, 0) >= trigger_count_threshold:
            alert_market_set.add(market)

    now_ = now()
    started_at = now_
    ended_at = now_ + datetime.timedelta(hours=tip_duration_hours)
    to_create_params = [
        dict(
            market=market,
            started_at=started_at,
            ended_at=ended_at,
            trigger_pages=[{
                "trigger_page": TipBar.TriggerPage.PERPETUAL_MARKET.name,
                "param_type": TipBar.TriggerPageParamType.MARKET.name,
                "page_op": TipBar.TriggerPageOp.IN.name,
                "trigger_page_params": [market],
            }],
        ) for market in markets
    ]
    create_params = filter_create_params(to_create_params=to_create_params)
    if create_params:
        gen_auto_tip_bars(create_params)
        create_markets = [v["market"] for v in create_params]
        content = gen_basis_tip_bar_alert_msg(create_markets)
        send_alert_notice(content, config["ADMIN_CONTACTS"]["market_notice"])


def gen_basis_tip_bar_alert_msg(alert_market_set):
    content = f"""【合约标记价格基差率告警提示条】"""
    for market in alert_market_set:
        line = f"""
        {market}合约市场当前标记价格基差率存在风险，前端已自动配置风险提示；
        """
        content += line
    return content


def get_perpetual_basis_rate_trigger_count_map(latest_data_num: int):
    latest_data_dic = SignPriceBasisRateCache().get_latest_markets_data(latest_data_num)
    threshold = RiskControlGroupConfig().sign_price_basis_rate
    return get_basis_rate_trigger_count_map(latest_data_dic, threshold)


def check_funding_rate_diff_alert(undisturbed_markets, now_):
    records_dic = RealTimePerpetualMonitorCache().read_aside()
    threshold = RiskControlGroupConfig().funding_rate_diff
    alert_market_dic = dict()

    for market, item in records_dic.items():
        if market in undisturbed_markets and \
                now_ <= undisturbed_markets[market]['ts'] + PerpetualMonitorAlertCache.ttl_field_mapping['funding_rate_diff']:
            continue
        our_funding_rate = Decimal(item['funding_rate'])
        third_exchange_info = item.get('third_exchange')
        if not third_exchange_info:
            continue
        third_funding_rate = Decimal(third_exchange_info['funding_rate'])
        exchange = third_exchange_info['exchange']
        diff = abs(our_funding_rate - third_funding_rate)
        if diff*100 >= threshold:
            alert_market_dic[market] = {
                'our_funding_rate': our_funding_rate,
                'third_funding_rate': third_funding_rate,
                'diff': diff,
                'exchange': exchange,
            }
    if alert_market_dic:
        content = gen_funding_alert_msg(alert_market_dic, threshold)
        send_alert_notice(content, config["ADMIN_CONTACTS"]["market_notice"])
    return {k: {'ts': now_, 'value': v} for k, v in alert_market_dic.items()}


def gen_funding_alert_msg(alert_market_dic, threshold):
    reminder_slack_ids = []
    remind_str = ''
    for reminder in reminder_slack_ids:
        remind_str += f'<@{reminder}>'
    content = f"""【合约资金费率偏差告警】"""
    for market, info in alert_market_dic.items():
        line = f"""
        市场：{market}
        该市场「资金费率」与竞品交易所差值为{format_percent(info['diff'])}（阈值为{threshold}%），请及时查看处理
        CoinEx资金费率：{format_percent(info['our_funding_rate'])}，竞品交易所（{info['exchange']}）资金费率：{format_percent(info['third_funding_rate'])}
        """
        content += line
    content += remind_str
    return content


def save_to_cache(cache, undisturbed_market_dic, sign_price_basis_alert_markets, funding_rate_diff_alert_markets):
    for k, v in undisturbed_market_dic.items():
        if k == 'sign_price_basis_rate':
            v.update(sign_price_basis_alert_markets)
        elif k == 'funding_rate_diff':
            v.update(funding_rate_diff_alert_markets)
        else:
            continue
    cache.save(json.dumps(undisturbed_market_dic, cls=JsonEncoder))


@scheduled(crontab(minute='*/15'))
@lock_call()
def update_real_time_perpetual_liquidation_schedule():
    """合约市场实时爆仓数据"""
    now_ts = current_timestamp(to_int=True)
    start_time_map = {
        '15m': now_ts - 15 * 60,
        '1h': now_ts - 1 * 3600,
        '6h': now_ts - 6 * 3600,
        '1d': now_ts - 1 * 86400,
        '3d': now_ts - 3 * 86400,
    }
    start_ts = start_time_map['3d']
    end_ts = now_ts

    total_stats = defaultdict(lambda: {
        'liq_users': set(),
        'liq_positions': set(),
        'liq_usd': Decimal(),
        'long_liq_users': set(),
        'long_liq_positions': set(),
        'long_liq_usd': Decimal(),
        'long_liq_users_ratio': str,
        'long_liq_positions_ratio': str,
        'long_liq_usd_ratio': str,
        'cross_liq_users': set(),
        'cross_liq_positions': set(),
        'cross_liq_usd': Decimal(),
        'cross_liq_users_ratio': str,
        'cross_liq_positions_ratio': str,
        'cross_liq_usd_ratio': str,
        'adl_liq_users': set(),
        'adl_liq_positions': set(),
        'adl_liq_usd': Decimal(),
        'adl_liq_users_ratio': str,
        'adl_liq_positions_ratio': str,
        'adl_liq_usd_ratio': str,
        'insurance_increase_usd': Decimal(),
        'insurance_decrease_usd': Decimal(),
        'insurance_increase_usd_ratio': Decimal(),
        'insurance_decrease_usd_ratio': Decimal(),
    })
    data = defaultdict(lambda: defaultdict(lambda: {
        'market': str,
        'liq_users': set(),
        'liq_positions': set(),
        'liq_amount': Decimal(),
        'liq_usd': Decimal(),
        'long_liq_users': set(),
        'long_liq_positions': set(),
        'long_liq_amount': Decimal(),
        'long_liq_usd': Decimal(),
        'long_liq_users_ratio': str,
        'long_liq_positions_ratio': str,
        'long_liq_usd_ratio': str,
        'cross_liq_users': set(),
        'cross_liq_users_ratio': str,
        'cross_liq_positions': set(),
        'cross_liq_positions_ratio': str,
        'cross_liq_amount': Decimal(),
        'cross_liq_usd': Decimal(),
        'cross_liq_usd_ratio': str,
        'adl_liq_users': set(),
        'adl_liq_users_ratio': str,
        'adl_liq_positions': set(),
        'adl_liq_positions_ratio': str,
        'adl_liq_amount': Decimal(),
        'adl_liq_usd': Decimal(),
        'adl_liq_usd_ratio': str,
        'insurance_increase_amount': Decimal(),
        'insurance_decrease_amount': Decimal(),
        'insurance_increase_usd': Decimal(),
        'insurance_decrease_usd': Decimal(),
        'insurance_increase_usd_ratio': str,
        'insurance_decrease_usd_ratio': str,
    }))

    position_liq_histories = get_position_liq_history(start_ts, end_ts)
    user_position_map = defaultdict(list)
    for item in position_liq_histories:
        user_position_map[item['user_id']].append(item['position_id'])
    adl_liq_positions = get_adl_liq_positions(start_ts, end_ts, user_position_map)

    for item in position_liq_histories:
        for time_type, type_start_time in start_time_map.items():
            if item['update_time'] < type_start_time:
                continue
            market = item['market']
            market_info = data[time_type][market]
            market_info['liq_users'].add(item['user_id'])
            market_info['liq_positions'].add(item['position_id'])
            market_info['liq_amount'] += item['liq_amount']
            stats_info = total_stats[time_type]
            stats_info['liq_users'].add(item['user_id'])
            stats_info['liq_positions'].add(item['position_id'])
            if item['insurance'] < 0:   # 穿仓
                market_info['cross_liq_users'].add(item['user_id'])
                market_info['cross_liq_positions'].add(item['position_id'])
                market_info['cross_liq_amount'] += item['liq_amount']
                stats_info['cross_liq_users'].add(item['user_id'])
                stats_info['cross_liq_positions'].add(item['position_id'])
            if item['position_id'] in adl_liq_positions:
                data[time_type][item['market']]['adl_liq_positions'].add(item['position_id'])
                data[time_type][item['market']]['adl_liq_users'].add(item['user_id'])
                data[time_type][item['market']]['adl_liq_amount'] += item['liq_amount']
                total_stats[time_type]['adl_liq_positions'].add(item['position_id'])
                total_stats[time_type]['adl_liq_users'].add(item['user_id'])
            if item['side'] == PositionSide.LONG:
                market_info['long_liq_users'].add(item['user_id'])
                market_info['long_liq_amount'] += item['liq_amount']
                market_info['long_liq_positions'].add(item['position_id'])
                stats_info['long_liq_users'].add(item['user_id'])
                stats_info['long_liq_positions'].add(item['position_id'])

    insurance_histories = get_insurance_history(start_ts, end_ts)
    for item in insurance_histories:
        for time_type, type_start_time in start_time_map.items():
            if item['time'] < type_start_time:
                continue
            market = item['market']
            if int(item['type']) == 2:
                data[time_type][market]['insurance_decrease_amount'] += item['change']
            elif int(item['type']) == 1:
                data[time_type][market]['insurance_increase_amount'] += item['change']

    def _update_count(info_: dict):
        info_['liq_users'] = len(info_['liq_users'])
        info_['long_liq_users'] = len(info_['long_liq_users'])
        info_['liq_positions'] = len(info_['liq_positions'])
        info_['long_liq_positions'] = len(info_['long_liq_positions'])
        info_['cross_liq_users'] = len(info_['cross_liq_users'])
        info_['cross_liq_positions'] = len(info_['cross_liq_positions'])
        info_['adl_liq_users'] = len(info_['adl_liq_users'])
        info_['adl_liq_positions'] = len(info_['adl_liq_positions'])

    def _calc_ratio(a, b):
        return amount_to_str((a / b) * 100, 2) + "%" if b else "0%"

    def _update_ratio(info_: dict):
        info_['cross_liq_users_ratio'] = _calc_ratio(info_['cross_liq_users'], info_['liq_users'])
        info_['cross_liq_positions_ratio'] = _calc_ratio(info_['cross_liq_positions'], info_['liq_positions'])
        info_['cross_liq_usd_ratio'] = _calc_ratio(info_['cross_liq_usd'], info_['liq_usd'])
        info_['adl_liq_users_ratio'] = _calc_ratio(info_['adl_liq_users'], info_['liq_users'])
        info_['adl_liq_positions_ratio'] = _calc_ratio(info_['adl_liq_positions'], info_['liq_positions'])
        info_['adl_liq_usd_ratio'] = _calc_ratio(info_['adl_liq_usd'], info_['liq_usd'])
        info_['insurance_increase_usd_ratio'] = _calc_ratio(info_['insurance_increase_usd'], info_['liq_usd'] - info_['cross_liq_usd'])
        info_['insurance_decrease_usd_ratio'] = _calc_ratio(info_['insurance_decrease_usd'], info_['cross_liq_usd'])
        info_['long_liq_users_ratio'] = _calc_ratio(info_['long_liq_users'], info_['liq_users'])
        info_['long_liq_positions_ratio'] = _calc_ratio(info_['long_liq_positions'], info_['liq_positions'])
        info_['long_liq_usd_ratio'] = _calc_ratio(info_['long_liq_usd'], info_['liq_usd'])

    asset_price = PriceManager.assets_to_usd()
    asset_price['USD'] = Decimal(1)
    market_asset_map = {i.name: dict(base_asset=i.base_asset, quote_asset=i.quote_asset, market_type=i.market_type)
                        for i in PerpetualMarket.query.all()}
    result = {}
    for time_type in start_time_map.keys():
        market_data = data[time_type]
        stats_info = total_stats[time_type]
        markets = []
        for market, info in market_data.items():
            info['market'] = market
            info['base_asset'] = base_asset = market_asset_map[market]['base_asset']
            info['quote_asset'] = quote_asset = market_asset_map[market]['quote_asset']
            info['market_type'] = market_type = market_asset_map[market]['market_type']

            _update_count(info)
            amount_asset = quote_asset if market_type == PerpetualMarketType.INVERSE else base_asset
            amount_asset_price = asset_price.get(amount_asset, 0)
            info['liq_usd'] = info['liq_amount'] * amount_asset_price
            info['long_liq_usd'] = info['long_liq_amount'] * amount_asset_price
            info['cross_liq_usd'] = info['cross_liq_amount'] * amount_asset_price
            info['adl_liq_usd'] = info['adl_liq_amount'] * amount_asset_price
            balance_asset = base_asset if market_type == PerpetualMarketType.INVERSE else quote_asset
            balance_asset_price = asset_price.get(balance_asset, 0)
            info['insurance_increase_usd'] = info['insurance_increase_amount'] * balance_asset_price
            info['insurance_decrease_usd'] = info['insurance_decrease_amount'] * balance_asset_price
            _update_ratio(info)
            markets.append(info)

            stats_info['liq_usd'] += info['liq_usd']
            stats_info['long_liq_usd'] += info['long_liq_usd']
            stats_info['cross_liq_usd'] += info['cross_liq_usd']
            stats_info['adl_liq_usd'] += info['adl_liq_usd']
            stats_info['insurance_increase_usd'] += info['insurance_increase_usd']
            stats_info['insurance_decrease_usd'] += info['insurance_decrease_usd']

        markets.sort(key=lambda x: x["liq_usd"], reverse=True)

        _update_count(stats_info)
        _update_ratio(stats_info)

        result.update({time_type: {'markets': markets, 'statistics': stats_info}})

    cache = RealTimePerpetualLiquidationCache()
    cache.hmset({
        'timestamp': now_ts,
        'data': json.dumps(result, cls=JsonEncoder)
    })


def get_position_liq_history(start_time: int, end_time: int):
    columns = (
        'market',
        'user_id',
        'position_id',
        'liq_amount',
        'liq_price',
        'insurance',
        'update_time',
        'side',
    )
    where = f' update_time >= {start_time} AND update_time < {end_time} '
    records = PerpetualSysHistoryDB.table('positionliq_history').select(
        *columns,
        where=where,
    )
    return list(dict(zip(columns, record)) for record in records)


def get_insurance_history(start_time: int, end_time: int):
    columns = ('type', 'market', 'change', 'time')
    where = f' time >= {start_time} AND time < {end_time} '
    records = PerpetualSysHistoryDB.table('insurance_history').select(
        *columns,
        where=where,
    )
    return list(dict(zip(columns, record)) for record in records)
