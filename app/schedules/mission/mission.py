from datetime import timedelta

from celery.schedules import crontab

from app.business import lock_call
from app.business.equity_center.helper import EquityCenterService
from app.business.mission_center.group import MissionGroupBiz, LogicGroupValidator
from app.business.mission_center.message import MissionMessageBiz
from app.business.mission_center.mission import MissionRewardManager
from app.business.mission_center.plan import MissionPlanBiz
from app.business.mission_center.statistics import MissionStatisticsBiz
from app.business.mission_center.utils import MissionUtils
from app.business.user import UserRepository
from app.business.utils import query_records_by_time_range
from app.caches.mission import MissionCache, MissionContentCache
from app.common import CeleryQueues
from app.models import User, db
from app.models.equity_center import EquityType
from app.models.mission_center import SceneType, MissionPlan, UserMission
from app.utils import route_module_to_celery_queue, scheduled, now, celery_task, today

route_module_to_celery_queue(__name__, CeleryQueues.REWARD_CENTER)


@scheduled(crontab(minute="*/10"))
@lock_call()
def filter_newbie_mission_users_schedules():
    filter_newbie_mission_users_task.delay()


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_mission_plan_status_schedules():
    MissionPlanBiz.plan_expired()
    MissionPlanBiz.plan_effective()
    MissionCache.reload()
    MissionContentCache.reload()


@scheduled(crontab(minute="*/10"))
def send_send_mission_expiring_notice_schedules():
    MissionMessageBiz.send_mission_expiring_notice()


@celery_task
@lock_call()
def mission_send_reward_task():
    """发送奖励异步任务"""
    to_completed_query = UserMission.query.filter(
        UserMission.status == UserMission.Status.SETTLING
    ).all()
    if not to_completed_query:
        return
    mission_ids = [i.mission_id for i in to_completed_query]
    mission_data_mapper = MissionCache.get_cache_data_by_ids(mission_ids)
    params = []
    has_airdrop = False
    abnormal_users = UserRepository.get_abnormal_users()
    for user_mission in to_completed_query:
        mission_data = mission_data_mapper[user_mission.mission_id]
        equity_status = EquityCenterService.UserEquityStatus.CREATED
        user_mission.status = UserMission.Status.FINISHED
        if user_mission.user_id in abnormal_users:
            user_mission.fail_reason = UserMission.FailReason.RISK_USER
            equity_status = EquityCenterService.UserEquityStatus.FAILED
        params.append((user_mission, mission_data, equity_status))
        if (equity_status == EquityCenterService.UserEquityStatus.CREATED and
                EquityType.AIRDROP.name == mission_data['reward']['reward_type']):
            has_airdrop = True
    success_user_missions = MissionRewardManager.send_reward(params)
    db.session.commit()
    MissionMessageBiz.send_reward_sent_notice(success_user_missions)
    if has_airdrop:
        EquityCenterService.async_send_airdrop_equity()


@scheduled(crontab(hour="*/1", minute="1"))
@lock_call()
def user_mission_statistics_schedules():
    """用户任务统计"""
    plan_ids = {i.id for i in MissionPlan.query.filter(
        MissionPlan.statistics_status == MissionPlan.StatisticsStatus.PROCESSING
    ).with_entities(
        MissionPlan.id,
    ).all()}
    if not plan_ids:
        return
    for plan_id in plan_ids:
        update_user_mission_statistics_task.delay(plan_id)


@scheduled(crontab(minute="*/5"))
@lock_call()
def user_mission_expired_schedules():
    now_ = now()
    UserMission.query.filter(
        UserMission.status == UserMission.Status.PENDING,
        UserMission.used_at > UserMission.MIN_UTC_DATETIME,
        UserMission.expired_at < now_
    ).update({
        UserMission.status: UserMission.Status.EXPIRED,
        UserMission.last_updated_at: now_
    }, synchronize_session=False)
    db.session.commit()


@celery_task
@lock_call(with_args=True)
def update_user_mission_statistics_task(plan_id: int):
    today_ = today()
    plan = MissionPlan.query.get(plan_id)
    if not plan:
        return
    plan_start_date = plan.start_at.date()
    last_date = MissionStatisticsBiz.get_plan_statistics_last_date(plan_id) or plan_start_date
    while last_date <= today_:
        MissionStatisticsBiz(plan_id, last_date).statistics()
        last_date += timedelta(days=1)
    if plan.status in [MissionPlan.Status.FINISHED, MissionPlan.Status.STOPPED] and \
            plan.statistics_status == MissionPlan.StatisticsStatus.PROCESSING:
        biz_status_mapper = {id_: status for id_, status in UserMission.query.filter(
            UserMission.plan_id == plan_id
        ).with_entities(
            UserMission.id,
            UserMission.status
        ).all()}
        biz_ids = set(biz_status_mapper.keys())
        is_finished = EquityCenterService.check_biz_equity_finished(
            EquityCenterService.BizTypes.MISSION, biz_ids
        )
        if_running = (set(biz_status_mapper.values()) -
                      {UserMission.Status.FINISHED, UserMission.Status.FAILED, UserMission.Status.EXPIRED})
        if is_finished and not if_running:
            plan.statistics_status = MissionPlan.StatisticsStatus.COMPLETED
            db.session.commit()
            MissionUtils.clear_cache(biz_ids)


@celery_task
@lock_call(with_args=True)
def filter_newbie_mission_users_task(new_user_id: int = None):
    min_time = MissionGroupBiz.query_min_register_time()
    if not min_time:
        return
    # 向后多查询5分钟
    now_ = now() + timedelta(minutes=5)
    if new_user_id:
        users = [User.query.get(new_user_id)]
    else:
        users = query_records_by_time_range(User, min_time, now_)
    normal_users = [i for i in users if i and i.user_type == User.UserType.NORMAL]
    if not normal_users:
        return
    LogicGroupValidator(SceneType.NEWBIE).domain(normal_users)