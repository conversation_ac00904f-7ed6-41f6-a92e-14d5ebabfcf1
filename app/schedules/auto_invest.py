# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import timedelta, datetime, date
from decimal import Decimal
from typing import List, Dict

from celery.schedules import crontab

from app.business import lock_call, ServerClient
from app.business.auto_invest import process_auto_invest_order_task, process_auto_invest_plan_task, \
    send_auto_invest_profit_amount_notice, auto_invest_plan_terminated_task, update_plan_statistic_task
from app.business.utils import query_records_by_time_range
from app.models import db
from app.models.auto_invest import AutoInvestPlan, AutoInvestTask, AutoInvestSystemOrder, AutoInvestSlice, \
    AutoInvestMarket, AutoInvestPlanNoticeConfig, AutoInvestMarketDepthSnapshot, AutoInvestPlanStatistic
from app.schedules.exchange import calc_market_depth_usd, query_market_depth_snapshot_rows
from app.utils import route_module_to_celery_queue, batch_iter, quantize_amount, amount_to_str
from app.common import CeleryQueues
from app.caches.spot import AutoInvestMarketDepthCache, AutoInvestMarketCache
from app.utils import now, scheduled
from app.utils.date_ import convert_datetime, current_timestamp, timestamp_to_datetime, today_datetime, today, \
    date_to_datetime

route_module_to_celery_queue(__name__, CeleryQueues.STRATEGY)


@scheduled(crontab(minute="*/5"))
@lock_call()
def save_market_depth_usd_snapshot_schedule():
    """ 保存定投市场的深度快照 """
    current_ts = current_timestamp(to_int=True)
    snapshot_ts = current_ts - current_ts % 300 - 300  # 查5分钟前的深度快照数据
    snapshot_dt = convert_datetime(timestamp_to_datetime(snapshot_ts), "minute")
    table_ts = snapshot_ts - snapshot_ts % 86400
    market_depth_rows_map = query_market_depth_snapshot_rows(table_ts, snapshot_ts)
    market_depth_usd_map = {}
    for market, rows in market_depth_rows_map.items():
        depth_usd_map = calc_market_depth_usd(rows, Decimal("0.05"))
        if sum(depth_usd_map.values()) > Decimal():
            market_depth_usd_map[market] = depth_usd_map

    for market, usd_map in market_depth_usd_map.items():
        row: AutoInvestMarketDepthSnapshot = AutoInvestMarketDepthSnapshot.get_or_create(
            market=market,
            snapshot_at=snapshot_dt,
        )
        row.buy_usd = usd_map["buy"]
        row.sell_usd = usd_map["sell"]
        db.session.add(row)
    db.session.commit()


@scheduled(crontab(hour="*/4"))
@lock_call()
def delete_market_depth_usd_snapshot_schedule():
    """ 删除 定投市场的深度快照 """
    now_ = now()
    delete_at = now_ - timedelta(days=7)
    delete_at = convert_datetime(delete_at, "minute")
    AutoInvestMarketDepthSnapshot.query.filter(
        AutoInvestMarketDepthSnapshot.snapshot_at < delete_at,
    ).delete()
    db.session.commit()


def query_markets_depth_usd_map(start_dt: datetime, end_dt: datetime) -> Dict[str, Decimal]:
    # 获取累积深度均值（买盘和卖盘中更小的）
    market_usd_map = {}
    zero = Decimal()
    auto_invest_markets = AutoInvestMarket.query.filter(AutoInvestMarket.status == AutoInvestMarket.Status.OPEN).all()
    markets = {i.market for i in auto_invest_markets}
    for market in markets:
        rows = AutoInvestMarketDepthSnapshot.query.filter(
            AutoInvestMarketDepthSnapshot.market == market,
            AutoInvestMarketDepthSnapshot.snapshot_at >= start_dt,
            AutoInvestMarketDepthSnapshot.snapshot_at <= end_dt,
        ).all()
        sum_usd = zero
        count = 0
        for row in rows:
            if row.sell_usd > zero and row.buy_usd > zero:
                min_usd = min(row.sell_usd, row.buy_usd)
                sum_usd += min_usd
                count += 1

        avg_usd = sum_usd / count if count else zero
        if avg_usd > zero:
            market_usd_map[market] = quantize_amount(avg_usd, 2)
    return market_usd_map


@scheduled(crontab(minute="*/10"))
@lock_call()
def update_auto_invest_market_cache_schedule():
    """ 更新支持定投的市场 """
    now_ = now()
    end_dt = convert_datetime(now_, "minute")
    start_dt = end_dt - timedelta(days=1)
    market_depth_usd_map = query_markets_depth_usd_map(start_dt, end_dt)
    AutoInvestMarketCache.save_to_cache(market_depth_usd_map)
    AutoInvestMarketDepthCache.save_to_cache(market_depth_usd_map)


@scheduled(crontab(minute="*/1"))
@lock_call()
def process_auto_invest_plan_schedule():
    """ 处理一段时间内的定投计划 """

    now_ = now()
    start_dt = now_ - timedelta(minutes=120)
    pending_plans = AutoInvestPlan.query.filter(
        AutoInvestPlan.next_effected_at < now_,
        AutoInvestPlan.next_effected_at >= start_dt,
        AutoInvestPlan.status == AutoInvestPlan.Status.RUNNING,
    ).with_entities(
        AutoInvestPlan.id,
        AutoInvestPlan.market,
        AutoInvestPlan.status,
    ).all()
    market_query = AutoInvestMarket.query.filter(AutoInvestMarket.status == AutoInvestMarket.Status.OPEN).all()
    opened_market_set = {i.market for i in market_query}
    for plan in pending_plans:
        if plan.market not in opened_market_set:
            continue
        process_auto_invest_plan_task.delay(plan.id)


@scheduled(crontab(minute="*/1"))
@lock_call()
def process_auto_invest_order_schedule():
    """ 处理一段时间内的定投订单 """
    now_ = now()
    start_dt = now_ - timedelta(minutes=120)
    pending_statues = [
        AutoInvestTask.Status.CREATED,
        AutoInvestTask.Status.TRADING,
        AutoInvestTask.Status.TRADED,
    ]
    # 这里极端情况下，会有部分的任务一直不执行
    for status in pending_statues:
        pending_tasks: List[AutoInvestTask] = query_records_by_time_range(
            table=AutoInvestTask,
            start_time=start_dt,
            end_time=now_,
            filters=dict(status=status),
            limit=5000,
        )
        for order in pending_tasks:
            process_auto_invest_order_task.delay(order.id)


@scheduled(10)
@lock_call()
def process_sys_asset_auto_invest_order_schedule():
    """ 定时挂单 """
    now_ = now()
    start_dt = now_ - timedelta(minutes=120)
    pending_sys_orders = query_records_by_time_range(
        table=AutoInvestSystemOrder,
        start_time=start_dt,
        end_time=now_,
        filters=dict(status=AutoInvestSystemOrder.Status.PROCESSING),
        limit=5000,
    )
    max_timeout_dt = now_ - timedelta(minutes=15)
    for sys_order in pending_sys_orders:
        if sys_order.timeout_at and sys_order.timeout_at <= max_timeout_dt:
            # 超时的 不再处理
            continue
        process_auto_invest_order_task.delay(sys_order.order_id)


@scheduled(crontab(minute="*/30"))
@lock_call()
def process_auto_invest_task_statistic_schedule():
    """ 重试一段时间内可能遗漏的定投订单统计任务 """
    now_ = now()
    start_dt = now_ - timedelta(minutes=120)
    pending_tasks: List[AutoInvestTask] = query_records_by_time_range(
        table=AutoInvestTask,
        start_time=start_dt,
        end_time=now_,
        limit=5000,
    )
    plans_ids = {i.plan_id for i in pending_tasks}
    for plan_id in plans_ids:
        update_plan_statistic_task.delay(plan_id)


@scheduled(crontab(hour='1-3', minute="0"))
@lock_call()
def generate_plan_profit_slice_schedule():
    """ 生成定投计划盈利快照 """

    today_ = today()
    model_ = AutoInvestSlice
    rec = model_.query.order_by(model_.id.desc()).first()
    if not rec:
        start_date = date(2023, 1, 1)
    else:
        start_date = rec.date + timedelta(days=1)
    while start_date < today_:
        end_date = start_date + timedelta(days=1)
        generate_plan_profit_slice(start_date)
        start_date = end_date


def generate_plan_profit_slice(report_date):
    if AutoInvestSlice.query.filter(
        AutoInvestSlice.date == report_date
    ).first():
        return
    end_date = report_date
    end_ = date_to_datetime(report_date)
    end_timestamp = int(end_.timestamp())
    client = ServerClient()
    orders_query = AutoInvestTask.query.all()
    orders = [i for i in orders_query if
              i.effected_at.date() <= end_date and i.status == AutoInvestTask.Status.FINISHED]
    plan_data_map = defaultdict(lambda: {
        "market": '',
        "total_source_traded_amount": Decimal(),
        "total_target_traded_amount": Decimal(),
        "total_target_fee_amount": Decimal(),
    })
    for order in orders:
        plan_data_map[order.plan_id]['total_source_traded_amount'] += order.source_asset_traded_amount
        plan_data_map[order.plan_id]['total_target_traded_amount'] += order.target_asset_traded_amount
        plan_data_map[order.plan_id]['market'] = order.market

    objects = []
    market_price_map = dict()

    for plan_ids_ in batch_iter(plan_data_map.keys(), 2000):
        plan_query = AutoInvestPlan.query.filter(AutoInvestPlan.id.in_(plan_ids_)).all()
        for plan in plan_query:
            plan_id = plan.id
            plan_data = plan_data_map[plan_id]
            market = plan_data['market']
            price = market_price_map.get(market)
            if not price:
                klines = client.market_kline(
                    market=market, start_time=end_timestamp,
                    end_time=end_timestamp, interval=86400)
                if not klines or not klines[0]:
                    continue
                price = Decimal(klines[-1][2])  # 收盘价
                market_price_map[market] = price

            if plan.status == AutoInvestPlan.Status.TERMINATED:
                if plan.stop_at.date() < report_date:
                    # 终止后不在更新了
                    continue
                price = plan.statistic.target_end_price if plan.stop_at.date() == report_date else price

            total_source_traded_amount = plan_data['total_source_traded_amount']
            total_target_traded_amount = plan_data['total_target_traded_amount']
            profit_amount = total_target_traded_amount * price - total_source_traded_amount
            row = AutoInvestSlice(
                plan_id=plan_id,
                date=report_date,
                market=market,
                total_source_amount=total_source_traded_amount,
                total_target_amount=total_target_traded_amount,
                profit_amount=profit_amount,
            )
            objects.append(row)
    for objs in batch_iter(objects, 2000):
        db.session.bulk_save_objects(objs)
    db.session.commit()


@scheduled(crontab(minute="*/10"))
@lock_call()
def auto_invest_plan_stop_notice_schedule():
    """ 用户定投计划达到盈利通知目标 """
    today_ = today_datetime()
    now_ = now()
    plan_notice_config_list = AutoInvestPlanNoticeConfig.query.filter(
        AutoInvestPlanNoticeConfig.notice_type != AutoInvestPlanNoticeConfig.NoticeType.NONE,
    ).all()
    pending_plan_notice = [i for i in plan_notice_config_list if (not i.last_noticed_at or i.last_noticed_at < today_)]
    notice_map = {i.plan_id: i for i in pending_plan_notice}
    plan_ids = [i.plan_id for i in pending_plan_notice]
    tickers = ServerClient().get_all_market_tickers()
    pending_plans = []
    for plan_ids_ in batch_iter(plan_ids, 2000):
        plan_query = AutoInvestPlan.query.filter(
            AutoInvestPlan.id.in_(plan_ids_),
            AutoInvestPlan.status != AutoInvestPlan.Status.TERMINATED,
        ).all()
        statistic_query = AutoInvestPlanStatistic.query.filter(AutoInvestPlanStatistic.plan_id.in_(plan_ids_)).all()
        statistic_map = {i.plan_id: i for i in statistic_query}
        for plan in plan_query:
            notice_config = notice_map[plan.id]
            if (
                plan.status == AutoInvestPlan.Status.PAUSED
                and notice_config.last_noticed_at
                and plan.stop_at < notice_config.last_noticed_at
            ):
                # 暂停且通知过的不需要通知
                continue
            if notice_config.notice_value == 0:
                continue
            ticker = tickers.get(plan.market)
            if not ticker:
                continue
            last_price = Decimal(ticker['last'])
            statistic = statistic_map[plan.id]
            profit_amount, profit_rate, need_notice = notice_config.get_profit_data(last_price,
                                                                                    statistic.total_source_amount,
                                                                                    statistic.total_target_amount,
                                                                                    )

            if need_notice:
                notice_config.last_noticed_at = now_
                pending_plans.append(dict(
                    plan_id=plan.id,
                    user_id=plan.user_id,
                    source_asset=plan.source_asset,
                    target_asset=plan.target_asset,
                    profit_amount=amount_to_str(profit_amount, 8),
                    profit_rate=amount_to_str(profit_rate * 100, 2),
                    notice_type=notice_config.notice_type.name,
                ))
            db.session.commit()

    for notice_plan in pending_plans:
        send_auto_invest_profit_amount_notice.delay(
            notice_plan['plan_id'],
            notice_plan['user_id'],
            notice_plan['source_asset'],
            notice_plan['target_asset'],
            notice_plan['profit_amount'],
            notice_plan['profit_rate'],
            notice_plan['notice_type'])


@scheduled(crontab(minute="*/5"))
@lock_call()
def auto_invest_market_terminated_schedule():
    """ 撤销处于关闭中状态的市场定投计划 """
    market_list = AutoInvestMarket.query.filter(
        AutoInvestMarket.status == AutoInvestMarket.Status.CLOSING
    ).all()
    for row in market_list:
        pending_plans = AutoInvestPlan.query.filter(
            AutoInvestPlan.market == row.market,
            AutoInvestPlan.status != AutoInvestPlan.Status.TERMINATED,
        ).all()
        for plan in pending_plans:
            auto_invest_plan_terminated_task.delay(plan.id)
