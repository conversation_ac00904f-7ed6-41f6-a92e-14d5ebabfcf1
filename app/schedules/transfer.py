# -*- coding: utf-8 -*-
from datetime import timedelta, datetime
from decimal import Decimal
from typing import Type

from celery.schedules import crontab
from flask import current_app
from app.business.staking import StakingOperation
from app.models.comment import CommentTipTransfer
from app.models.staking import StakingHistory, StakingPool, StakingUserSummary

from ..business import PerpetualServerClient, ServerClient, SPOT_ACCOUNT_ID, \
    lock_call, LockKeys, CacheLock
from ..business.lendable import LendableAmountProcessor
from ..business.account import AccountTransferLogHelper
from ..common import CeleryQueues, BalanceBusiness
from ..models import db, PerpetualBalanceTransfer, InvestmentBalanceHistory, \
    CreditAssetHistory, \
    LendableAssetChangeHistory, \
    InvestmentAccount, MarginTransferHistory, SubAccountAssetTransfer
from ..models.base import Model
from ..utils import scheduled, route_module_to_celery_queue, amount_to_str, celery_task
from ..utils.date_ import now

route_module_to_celery_queue(__name__, CeleryQueues.WALLET)


def _query_by_time_range(table: Type[Model], start_time: datetime, end_time: datetime):
    limit = 1000
    result = []
    last = None
    while last is None or last.created_at > start_time:
        query = table.query
        if last:
            query = query.filter(table.id < last.id)
        rows = query.order_by(table.id.desc()).limit(limit).all()
        for row in rows:
            if start_time < row.created_at < end_time:
                result.append(row)
        if len(rows) != limit:
            break
        last = rows[-1]
    return result


@scheduled(90)
@lock_call()
def retry_transfer_perpetual_balance():
    """
    更新合约划转失败的用户资产, 时间间隔改为90秒
    """
    _now = now()
    end_time = _now - timedelta(seconds=90)
    start_time = _now - timedelta(hours=12)

    need_transfer_logs = _query_by_time_range(PerpetualBalanceTransfer, start_time, end_time)
    need_transfer_logs = [
        x for x in need_transfer_logs if x.status in [PerpetualBalanceTransfer.Status.CREATED, PerpetualBalanceTransfer.Status.DEDUCTED]
    ]

    for _log in need_transfer_logs:
        retry_transfer_perpetual_balance_task(_log.id)


@celery_task
@lock_call(with_args=True, wait=False)
def retry_transfer_perpetual_balance_task(transfer_id: int):
    db.session.rollback()
    _log = PerpetualBalanceTransfer.query.get(transfer_id)
    if _log.status not in [PerpetualBalanceTransfer.Status.CREATED, PerpetualBalanceTransfer.Status.DEDUCTED]:
        return

    perpetual_client = PerpetualServerClient(current_app.logger)
    client = ServerClient(current_app.logger)
    # CREATED先检查是否已经划转成功
    if _log.status == PerpetualBalanceTransfer.Status.CREATED:
        try:
            if _log.transfer_type == PerpetualBalanceTransfer.TransferType.TRANSFER_OUT:
                result = perpetual_client.asset_query_business(
                    user_id=_log.user_id,
                    asset=_log.coin_type,
                    business=BalanceBusiness.PERPETUAL_TRANSFER_OUT,
                    business_id=_log.id,
                )
            else:
                result = client.asset_query_business(
                    user_id=_log.user_id,
                    asset=_log.coin_type,
                    business=BalanceBusiness.PERPETUAL_TRANSFER,
                    business_id=_log.id,
                )
            if not result:
                _log.status = PerpetualBalanceTransfer.Status.FAILED
                db.session.commit()
                return
            else:
                _log.deducted_at = _log.created_at
                _log.status = PerpetualBalanceTransfer.Status.DEDUCTED
                db.session.commit()
        except Exception as e:
            current_app.logger.error(
                f'fail to update user perpetual balance. {_log.id}: {e!r}')
            return
    # DEDUCTED
    if _log.transfer_type == PerpetualBalanceTransfer.TransferType.TRANSFER_OUT:
        try:
            client.add_user_balance(
                user_id=_log.user_id,
                asset=_log.coin_type,
                amount=amount_to_str(_log.amount, 8),
                business=BalanceBusiness.PERPETUAL_TRANSFER,
                business_id=_log.id,
                detail={'remark': 'contract transfer out'}
            )
        except Exception as e:
            current_app.logger.error(
                f'fail to update user perpetual balance. {_log.id}: {e!r}')
            return
    else:
        try:
            perpetual_client.add_user_balance(
                user_id=_log.user_id,
                asset=_log.coin_type,
                amount=amount_to_str(_log.amount, 8),
                business=BalanceBusiness.PERPETUAL_TRANSFER_IN,
                business_id=_log.id,
                detail={'remark': 'contract transfer in'}
            )
        except Exception as e:
            current_app.logger.error(
                f'fail to update user perpetual balance. {_log.id}: {e!r}')
            return
    
    _log.finished_at = now()
    _log.status = PerpetualBalanceTransfer.Status.FINISHED
    db.session.commit()
    AccountTransferLogHelper.add_log_by_transfer(_log)
    current_app.logger.info(f'retry transfer perpetual balance success. {_log.id}')


@scheduled(crontab(minute='*/3'))
@lock_call()
def retry_transfer_investment_balance():
    """
    更新理财划转失败的用户资产
    """
    _now = now()
    end_time = _now - timedelta(minutes=3)
    start_time = _now - timedelta(hours=12)

    need_transfer_logs = _query_by_time_range(InvestmentBalanceHistory, start_time, end_time)
    need_transfer_logs = [
        x for x in need_transfer_logs if x.status in [
            InvestmentBalanceHistory.StatusType.CREATE,
            InvestmentBalanceHistory.StatusType.DEDUCTED,
        ]
    ]

    client = ServerClient(current_app.logger)
    for _log in need_transfer_logs:
        if _log.opt_type == InvestmentBalanceHistory.OptType.IN:
            transfer_from = SPOT_ACCOUNT_ID
            transfer_to = InvestmentAccount.ACCOUNT_ID
        elif _log.opt_type == InvestmentBalanceHistory.OptType.OUT:
            transfer_from = InvestmentAccount.ACCOUNT_ID
            transfer_to = SPOT_ACCOUNT_ID
        else:
            continue

        if _log.status == InvestmentBalanceHistory.StatusType.CREATE:
            result = client.asset_query_business(
                user_id=_log.user_id,
                asset=_log.asset,
                business=BalanceBusiness.INVESTMENT_TRANSFER,
                business_id=_log.id,
                account_id=transfer_from,
            )
            if not result:
                _log.status = InvestmentBalanceHistory.StatusType.FAIL
                db.session.commit()
                continue
            else:
                _log.status = InvestmentBalanceHistory.StatusType.DEDUCTED
                db.session.commit()

        try:
            client.add_user_balance(
                user_id=_log.user_id,
                asset=_log.asset,
                amount=amount_to_str(Decimal(abs(_log.amount)), 8),
                business=BalanceBusiness.INVESTMENT_TRANSFER,
                business_id=_log.id,
                detail={"remark": "investment for transfer"},
                account_id=transfer_to,
            )
        except Exception as e:
            current_app.logger.info(f'fail to update user investment balance. {_log.id} {e!r}')
            continue

        _log.status = InvestmentBalanceHistory.StatusType.SUCCESS
        _log.success_at = now()
        db.session.commit()
        AccountTransferLogHelper.add_log_by_transfer(_log)
        current_app.logger.info(f'retry transfer investment balance success. {_log.id}')


@scheduled(crontab(minute='*/3'))
@lock_call()
def retry_transfer_margin_balance():
    """
    更新杠杆划转失败的用户资产
    """
    _now = now()
    end_time = _now - timedelta(minutes=3)
    start_time = _now - timedelta(hours=12)

    need_transfer_logs = _query_by_time_range(MarginTransferHistory, start_time, end_time)
    need_transfer_logs = [
        x for x in need_transfer_logs if x.status in [
            MarginTransferHistory.StatusType.CREATE,
            MarginTransferHistory.StatusType.DEDUCTED,
        ]
    ]

    client = ServerClient(current_app.logger)
    for _log in need_transfer_logs:
        if _log.status == MarginTransferHistory.StatusType.CREATE:
            result = client.asset_query_business(
                user_id=_log.user_id,
                asset=_log.asset,
                business=BalanceBusiness.MARGIN_TRANSFER,
                business_id=_log.id,
                account_id=_log.from_account_id,
            )
            if not result:
                _log.status = MarginTransferHistory.StatusType.FAIL
                db.session.commit()
                continue
            else:
                _log.status = MarginTransferHistory.StatusType.DEDUCTED
                db.session.commit()

        try:
            client.add_user_balance(
                user_id=_log.user_id,
                asset=_log.asset,
                amount=amount_to_str(_log.amount, 8),
                business=BalanceBusiness.MARGIN_TRANSFER,
                business_id=_log.id,
                detail={"remark": "margin for transfer"},
                account_id=_log.to_account_id,
            )
        except Exception as e:
            current_app.logger.info(f'fail to update user margin balance. {_log.id} {e!r}')
            continue

        _log.status = MarginTransferHistory.StatusType.SUCCESS
        _log.balance += _log.amount if _log.from_account_id == 0 else -_log.amount
        db.session.commit()
        AccountTransferLogHelper.add_log_by_transfer(_log)
        current_app.logger.info(f'retry transfer margin balance success. {_log.id}')


@scheduled(crontab(minute='*/3'))
@lock_call()
def retry_transfer_credit_balance():
    from app.business.credit import update_credit_user_risk_record_task
    """
    更新授信划转失败的用户资产
    """
    _now = now()
    end_time = _now - timedelta(minutes=3)
    start_time = _now - timedelta(hours=12)

    need_transfer_logs = _query_by_time_range(CreditAssetHistory, start_time, end_time)
    # 只有AUDIT并且记录过了check_user_id才代表可能划转中失败了
    need_transfer_logs = [x for x in need_transfer_logs if
                          x.status == CreditAssetHistory.StatusType.FAIL and
                          x.credit_type == CreditAssetHistory.CreditType.CREDIT and
                          x.check_user_id]

    client = ServerClient(current_app.logger)

    for _log in need_transfer_logs:
        try:
            client.add_user_balance(
                user_id=_log.user_id,
                asset=_log.asset,
                business=BalanceBusiness.CREDIT,
                business_id=_log.id,
                amount=str(_log.amount),
                detail={
                    'remark': f"add credit 'credit_asset_history_id':{_log.id}"
                }
            )
        except client.BadResponse as e:
            current_app.logger.error(
                f"add credit id:{_log.id} user_id {_log.user_id} asset {_log.asset} add amount {_log.amount} error: {e!r}"
            )
            continue
        _log.finished_at = now()
        _log.status = CreditAssetHistory.StatusType.FINISH
        db.session.commit()
        LendableAmountProcessor(
            _log.asset, -_log.amount,
            LendableAssetChangeHistory.BusinessType.CREDIT_LOAN
        ).process_new_record()
        update_credit_user_risk_record_task.delay(_log.user_id)
        current_app.logger.info(f'retry transfer credit balance success. {_log.id}')


@scheduled(crontab(minute='*/3'))
@lock_call()
def retry_transfer_sub_account_balance():
    """
    更新子账号划转失败的用户资产
    """
    _now = now()
    end_time = _now - timedelta(minutes=3)
    start_time = _now - timedelta(hours=1)

    need_transfer_logs = _query_by_time_range(SubAccountAssetTransfer, start_time, end_time)
    need_transfer_logs = [x for x in need_transfer_logs if x.status in (
        SubAccountAssetTransfer.Status.DEDUCTED,
        SubAccountAssetTransfer.Status.CREATED
    )]
    client = ServerClient(current_app.logger)
    p_client = PerpetualServerClient(current_app.logger)
    for _log in need_transfer_logs:  # type: SubAccountAssetTransfer

        if _log.status == SubAccountAssetTransfer.Status.CREATED:
            try:
                if _log.source_account_type == SubAccountAssetTransfer.AccountType.SPOT:
                    result = client.asset_query_business(
                        user_id=_log.source,
                        asset=_log.asset,
                        business=BalanceBusiness.SUB_ACCOUNT_TRANSFER,
                        business_id=_log.id,
                    )
                elif _log.source_account_type == SubAccountAssetTransfer.AccountType.PERPETUAL:
                    result = p_client.asset_query_business(
                        user_id=_log.source,
                        asset=_log.asset,
                        business=BalanceBusiness.SUB_ACCOUNT_TRANSFER,
                        business_id=_log.id,
                    )
                else:
                    continue
                if not result:
                    _log.status = SubAccountAssetTransfer.Status.FAILED
                    db.session.commit()
                    continue
                else:
                    _log.status = SubAccountAssetTransfer.Status.DEDUCTED
                    db.session.commit()
            except Exception as e:
                current_app.logger.info(f"fail to query user sub account transfer business. {_log.id}:{e!r}")
                continue
        try:
            if _log.target_account_type == SubAccountAssetTransfer.AccountType.SPOT:
                client.add_user_balance(
                    user_id=_log.target,
                    asset=_log.asset,
                    amount=_log.amount,
                    business=BalanceBusiness.SUB_ACCOUNT_TRANSFER,
                    business_id=_log.id,
                    detail={"remark": "sub account for transfer"}
                )
            elif _log.target_account_type == SubAccountAssetTransfer.AccountType.PERPETUAL:
                p_client.add_user_balance(
                    user_id=_log.target,
                    asset=_log.asset,
                    amount=_log.amount,
                    business=BalanceBusiness.SUB_ACCOUNT_TRANSFER,
                    business_id=_log.id,
                    detail={"remark": "sub account for transfer"}
                )
            else:
                continue
        except Exception as e:
            current_app.logger.info(f"fail to update user sub account balance. {_log.id}: {e!r}")
            continue
        _log.status = SubAccountAssetTransfer.Status.FINISHED
        db.session.commit()
        current_app.logger.info(f'retry transfer sub account balance success. {_log.id}')



@scheduled(crontab(minute='*/3'))
@lock_call()
def retry_transfer_staking_balance():
    """
    更新链上质押账户划转失败的用户资产(此任务只对质押记录进行重试)
    """
    _now = now()
    end_time = _now - timedelta(minutes=3)
    start_time = _now - timedelta(hours=12)
    need_transfer_logs = _query_by_time_range(StakingHistory, start_time, end_time)
    need_transfer_logs = [
        x for x in need_transfer_logs if x.transfer_status in [
            StakingHistory.TransferStatus.CREATED,
            StakingHistory.TransferStatus.DEDUCTED,
        ]
        and x.type == StakingHistory.Type.STAKE
    ]
    client = ServerClient(current_app.logger)
    for _log in need_transfer_logs:
        _log: StakingHistory
        with CacheLock(LockKeys.staking_operation(_log.asset), wait=False):
            db.session.rollback()
            _log = StakingHistory.query.get(_log.id)
            if _log.transfer_status not in [StakingHistory.TransferStatus.CREATED, StakingHistory.TransferStatus.DEDUCTED]:
                continue
            pool = StakingPool.query.filter(StakingPool.asset == _log.asset).first()
            summary = StakingUserSummary.query.filter(StakingUserSummary.user_id == _log.user_id,
                                                    StakingUserSummary.asset == _log.asset).first()
            
            if _log.transfer_status == StakingHistory.TransferStatus.CREATED:
                result = client.asset_query_business(
                    user_id=_log.user_id,
                    asset=_log.asset,
                    business=BalanceBusiness.STAKING_ADD,
                    business_id=_log.id,
                    account_id=SPOT_ACCOUNT_ID,
                )
                if not result:
                    current_app.logger.error(f'Staking Transfer {_log.id} retry failed')
                    _log.status = StakingHistory.Status.FAILED
                    _log.transfer_status = StakingHistory.TransferStatus.FAILED
                    db.session.commit()
                    continue
                else:
                    summary.staking_amount += _log.amount
                    _log.transfer_status = StakingHistory.TransferStatus.DEDUCTED
                    db.session.commit()
            try:
                StakingOperation(_log.asset).do_transfer_in(_log, pool, summary)
            except Exception as e:
                current_app.logger.error(f'Staking Transfer {_log.id} retry failed: {e!r}')


@scheduled(crontab(minute='*/3'))
@lock_call()
def retry_comment_tip_transfer_balance():
    """
    评论打赏划转补偿任务
    """
    _now = now()
    end_time = _now - timedelta(minutes=3)
    start_time = _now - timedelta(hours=12)

    need_transfer_logs = _query_by_time_range(CommentTipTransfer, start_time, end_time)

    need_transfer_logs = [
        x for x in need_transfer_logs if x.status in [
            CommentTipTransfer.Status.CREATED,
            CommentTipTransfer.Status.DEDUCTED,
        ]
    ]
    client = ServerClient(current_app.logger)
    for _log in need_transfer_logs:
        _log: CommentTipTransfer
        if _log.status == CommentTipTransfer.Status.CREATED:
            try:
                result = client.asset_query_business(
                    user_id=_log.source_user_id,
                    asset=_log.asset,
                    business=BalanceBusiness.COMMENT_TIP_OUT,
                    business_id=_log.id,
                )
                if not result:
                    _log.status = CommentTipTransfer.Status.FAILED
                    db.session.commit()
                    continue
                else:
                    _log.status = CommentTipTransfer.Status.DEDUCTED
                    db.session.commit()
            except Exception as e:
                current_app.logger.info(f"fail to query comment tip transfer business. {_log.id}:{e!r}")
                continue
        if _log.status != CommentTipTransfer.Status.DEDUCTED:
            continue
        try:
            client.add_user_balance(
                user_id=_log.target_user_id,
                asset=_log.asset,
                amount=_log.amount,
                business=BalanceBusiness.COMMENT_TIP_IN,
                business_id=_log.id,
                detail={'send_user_id': _log.source_user_id}
            )
        except Exception as e:
            current_app.logger.info(f"fail to add comment tip transfer business. {_log.id}: {e!r}")
            continue
        _log.status = CommentTipTransfer.Status.FINISHED
        db.session.commit()

