# -*- coding: utf-8 -*-
import datetime
import json
import time
from collections import defaultdict
from datetime import timedel<PERSON>
from decimal import Decimal
from typing import Dict, List, NamedTuple, Set

from celery.schedules import crontab
from pyroaring import BitMap
from sqlalchemy import func

from app.common.constants import MessageContent, MessageTitle, MessageWebLink
from app.models.amm import AmmMarket
from app.models.exchange import AssetExchangeOrder, AssetExchangeOrderFee, SysAssetExchangeOrder
from ..business import PerpetualSummaryDB, TradeSummaryDB, lock_call
from ..business.ambassador import InvalidAmbassadorSource, PotentialUserSource, QualityUserSource, \
    RejectedAmbassadorSource
from ..business.market_maker import MarketMakerHelper
from ..business.broker import get_broker_id_by_client_id
from ..business.coupon.utils import CouponTool
from ..business.coupon.base import BaseCouponService
from ..business.coupon.pool import update_coupon_dynamic_user_task
from ..business.coupon.user_coupon import CashBackFeeCouponStatus
from ..business.referral import ReferralBusiness, AmbassadorBusiness, ViaBtcPoolAmbBusiness
from ..business.bus_referral import BusRelationUserQuerier
from ..business.user import UserRepository
from ..business.bus_referral import BusAgentReferHelper, BusUserReferHelper
from ..caches import ReferralRankCache
from ..caches.activity import DynamicUserCouponCache
from ..caches.admin import UserFirstNormalReferLastIDCache, UserFirstNormalReferTimeCache
from ..caches.report import DailyIncomeProcessResultCache
from ..common import CeleryQueues, TradeBusinessType
from ..models import (
    Activity,
    Ambassador,
    AmbassadorAgent,
    DailyUserReferralSlice,
    AmbassadorAgentAssetDetail,
    AmbassadorAgentHistory,
    AssetPrice,
    GiftHistory,
    Referral,
    ReferralAssetDetail,
    ReferralAssetHistory,
    ReferralAssetSummary,
    ReferralHistory,
    SubAccount,
    User,
    db,
    Message,
    DailyAmbassadorAgentAssetReport,
    ReferralCodeAssetDetail,
    PotentialAmbassador, AmbassadorBusinessTrace, AmbassadorApplication, BusinessTraceChangeHistory, LoginHistory,
    IncomeType,
    DailyUserReferralTypeSlice,
    BusinessAmbassador,
    UserApiFrequencyLimitRecord, SpecialReferreeUserRate,
)
from ..models.activity import CouponApply, Coupon, CouponPool, CouponRisk
from ..models.broker import Broker, BrokerReferralAssetHistory, BrokerReferralAssetDetail
from ..models.api_resource import DailyApiRequest
from ..models.user import UserBizTag
from ..utils import (
    amount_to_str,
    batch_iter,
    celery_task,
    now,
    quantize_amount,
    route_module_to_celery_queue,
    scheduled,
    today, datetime_to_str, group_by,
)
from ..utils.date_ import last_month


route_module_to_celery_queue(__name__, CeleryQueues.GIFT)


class _Agent(NamedTuple):
    # 代理返佣
    user_id: int
    ref_ambassadors: List[int]  # 只存大使user_id
    asset: str
    referral_rate: Decimal
    type: ReferralAssetHistory.Type


class _ReferralUser(NamedTuple):
    # 被邀请用户
    referral_id: int
    user_id: int


class _Referral(NamedTuple):
    # 普通返佣、大使返佣
    user_id: int
    ref_users: List[_ReferralUser]
    asset: str
    spot_rate: Decimal
    perpetual_rate: Decimal
    type: ReferralAssetHistory.Type


class _Share(NamedTuple):
    # 普通返佣、大使返佣 分成对象
    user_id: int  # 普通用户、大使用户
    rate: Decimal


class ReferralAssetProcessor:
    """ 普通返佣、大使返佣 """
    @classmethod
    def get_exclude_referree_ids(cls) -> set[int]:
        """ 当天不执行返佣的被邀请人 """
        exclude_ee_ids = UserBizTag.query_tag_user_ids(biz_tag=UserBizTag.BizTag.EE_NOT_REFERRAL)
        return exclude_ee_ids

    @classmethod
    def send_referral_reward(cls, date) -> bool:
        """ 执行当日的普通返佣、大使返佣的发放 """
        if ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == date,
            ReferralAssetHistory.type.in_([ReferralAssetHistory.Type.REFERRAL, ReferralAssetHistory.Type.AMBASSADOR]),
        ).first():
            return False
        if not TradeSummaryDB.is_data_completed(date) or not PerpetualSummaryDB.is_data_completed(date):
            return False
        if not CashBackFeeCouponStatus.check_daily_cashback_status(date):
            return False
        cls.make_referral_asset_detail(date)
        cls.summary_referral_asset_history(date)

        ref_asset_records = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == date,
            ReferralAssetHistory.type.in_([ReferralAssetHistory.Type.REFERRAL, ReferralAssetHistory.Type.AMBASSADOR]),
            ReferralAssetHistory.status == ReferralAssetHistory.Status.CREATED,
        ).all()
        created_at = date + timedelta(days=1)
        gift_rows = []
        for row in ref_asset_records:
            gift = GiftHistory(
                created_at=created_at,
                user_id=row.user_id,
                activity_id=Activity.REFERRAL_ID,
                asset=row.asset,
                amount=row.amount,
                remark='gift for refer',
                status=GiftHistory.Status.CREATED
            )
            gift_rows.append(gift)
        for rows in batch_iter(gift_rows, 1000):
            db.session.bulk_save_objects(rows)
        ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == date,
            ReferralAssetHistory.type.in_([ReferralAssetHistory.Type.REFERRAL, ReferralAssetHistory.Type.AMBASSADOR]),
        ).update({ReferralAssetHistory.status: ReferralAssetHistory.Status.FINISHED}, synchronize_session=False)
        db.session.commit()
        return True

    @classmethod
    def summary_referral_asset_history(cls, date):
        """从返佣明细汇总待发放的返佣流水"""
        ref_details = (
            ReferralAssetDetail.query.filter(ReferralAssetDetail.date == date)
            .group_by(
                ReferralAssetDetail.user_id,
                ReferralAssetDetail.type,
                ReferralAssetDetail.asset,
            )
            .with_entities(
                ReferralAssetDetail.user_id,
                ReferralAssetDetail.type,
                ReferralAssetDetail.asset,
                func.sum(ReferralAssetDetail.spot_amount + ReferralAssetDetail.perpetual_amount),
                func.sum(ReferralAssetDetail.spot_amount),
                func.sum(ReferralAssetDetail.perpetual_amount),
            )
            .all()
        )

        ref_rows = []
        for user_id, type_, asset, amount, spot_amount, perpetual_amount in ref_details:
            ref_rows.append(
                ReferralAssetHistory(
                    date=date,
                    user_id=user_id,
                    type=type_,
                    asset=asset,
                    amount=amount,
                    spot_amount=spot_amount,
                    perpetual_amount=perpetual_amount,
                )
            )
        for rows in batch_iter(ref_rows, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()

    @classmethod
    def make_referral_asset_detail(cls, date):
        """生成当天待发放的返佣明细"""
        asset_rates = AssetPrice.get_close_price_map(date)
        fees = cls.sum_deal_fees(date, asset_rates)
        spot_fees, perpetual_fees = fees[TradeBusinessType.SPOT], fees[TradeBusinessType.PERPETUAL]

        expense = cls.sum_expense(date, asset_rates)
        spot_expense, perpetual_expense = expense[TradeBusinessType.SPOT], expense[TradeBusinessType.PERPETUAL]
        cleared_users = UserRepository.get_cleared_user_ids()
        refs = cls.build_referral_history(set(spot_fees.keys()) | set(perpetual_fees.keys()))
        shares = cls.build_referral_share(refs)
        ee_special_ref_rate_map = cls.get_all_ee_special_ref_rate()
        exclude_ee_ids = cls.get_exclude_referree_ids()

        ref_rows = []
        for ref in refs:
            total_spot_fee = total_perpetual_fee = 0
            for ref_user in ref.ref_users:
                if ref_user.user_id in exclude_ee_ids:
                    continue
                spot_fee = spot_fees.get(ref_user.user_id, 0)
                perpetual_fee = perpetual_fees.get(ref_user.user_id, 0)

                spot_fee -= spot_expense.get(ref_user.user_id, 0)
                perpetual_fee -= perpetual_expense.get(ref_user.user_id, 0)
                if spot_fee < 0:
                    spot_fee = 0
                if perpetual_fee < 0:
                    perpetual_fee = 0
                total_spot_fee += spot_fee
                total_perpetual_fee += perpetual_fee
                if ref_user.user_id in ee_special_ref_rate_map:
                    spot_rate = perpetual_rate = ee_special_ref_rate_map[ref_user.user_id]
                    spot_rate = min(ref.spot_rate, spot_rate)
                    perpetual_rate = min(ref.perpetual_rate, perpetual_rate)
                else:
                    spot_rate = ref.spot_rate
                    perpetual_rate = ref.perpetual_rate
                spot_amount = quantize_amount(spot_fee * spot_rate / asset_rates[ref.asset], 8)
                perpetual_amount = quantize_amount(perpetual_fee * perpetual_rate / asset_rates[ref.asset], 8)

                if spot_amount == 0 and perpetual_amount == 0:
                    continue
                # B -> A and B -> B
                for share in shares[ref_user.referral_id][ref_user.user_id]:
                    share_spot_amount = quantize_amount(spot_amount * share.rate, 8)
                    share_perpetual_amount = quantize_amount(perpetual_amount * share.rate, 8)
                    if share_spot_amount > 0 or share_perpetual_amount > 0:
                        user_id = share.user_id
                        if user_id in cleared_users:
                            continue

                        ref_rows.append(ReferralAssetDetail(
                            date=date,
                            user_id=user_id,
                            referree_id=ref_user.user_id,
                            referral_id=ref_user.referral_id,
                            type=ref.type,
                            asset=ref.asset,
                            spot_amount=share_spot_amount,
                            perpetual_amount=share_perpetual_amount,
                            spot_fee_usd=spot_fee,
                            perpetual_fee_usd=perpetual_fee,
                        ))
        for rows in batch_iter(ref_rows, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()

    @classmethod
    def build_referral_share(cls, ref_data: List[_Referral]) -> Dict[int, Dict[int, List[_Share]]]:
        """ 根据邀请码设置的分成比例rate，构建返佣分成, { 邀请码ID: { 被邀请用户ID : [被邀请用户比例，邀请用户比例 ] } } """
        rows = Referral.query.all()
        rows = {x.id: x for x in rows}
        result = defaultdict(lambda: defaultdict(list))  # { code_id: {referree_id: [share_rate...] } }
        for item in ref_data:
            for ref_user in item.ref_users:
                ref = rows[ref_user.referral_id]
                if ref.rate > 0:
                    result[ref_user.referral_id][ref_user.user_id].append(_Share(
                        user_id=item.user_id,
                        rate=ref.rate
                    ))
                if (rate := 1 - ref.rate) > 0:
                    result[ref_user.referral_id][ref_user.user_id].append(_Share(
                        user_id=ref_user.user_id,
                        rate=rate
                    ))
        return result

    @classmethod
    def sum_deal_fees(cls, date, asset_rates) -> Dict[TradeBusinessType, Dict[int, Decimal]]:
        """手续费收入:获取现货(含兑换)、合约交易手续费，按主账户汇总，排除做市商, 经纪商"""
        sub_users = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        sub_users = dict(sub_users)
        
        market_makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
        market_makers = set(market_makers)

        # 兑换，异常订单会没有Fee记录
        exchange_fee_map = defaultdict(Decimal)
        exchanges = AssetExchangeOrder.query.select_from(AssetExchangeOrder).join(
            AssetExchangeOrderFee, isouter=False,
        ).filter(
            AssetExchangeOrder.updated_at > date,
            AssetExchangeOrder.updated_at <= date + timedelta(days=1),
            AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED
        ).with_entities(
            AssetExchangeOrderFee.user_id,
            AssetExchangeOrderFee.asset,
            AssetExchangeOrderFee.amount
        ).all()
        for item in exchanges:
            user_id = sub_users.get(item.user_id, item.user_id)
            exchange_fee_map[user_id] += item.amount * asset_rates.get(item.asset, 0)
        exchange_fee_map = {k: quantize_amount(v, 8) for k, v in exchange_fee_map.items()}
        result = {}
        for _type, _db in ((TradeBusinessType.SPOT, TradeSummaryDB), (TradeBusinessType.PERPETUAL, PerpetualSummaryDB)):
            data = _db.group_by_user_fee_by_date(date)
            fees = defaultdict(Decimal)
            for row in data:
                main_user_id = sub_users.get(row['user_id'], row['user_id'])
                if main_user_id in market_makers:
                    continue
                fees[main_user_id] += row['fee'] * asset_rates.get(row['asset'], 0)
            result[_type] = {k: quantize_amount(v, 8) for k, v in fees.items()}
        # 兑换汇总到现货
        for k, v in exchange_fee_map.items():
            if k in result[TradeBusinessType.SPOT]:
                result[TradeBusinessType.SPOT][k] += v
            else:
                result[TradeBusinessType.SPOT][k] = v
        return result

    @classmethod
    def sum_expense(cls, date, asset_rates) -> Dict[TradeBusinessType, Dict[int, Decimal]]:
        """支出(AMM分红, 手续费返现券)，汇总到主账户"""
        sub_users = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        sub_users = dict(sub_users)
        amm_markets = AmmMarket.query.filter(AmmMarket.status == \
                                      AmmMarket.Status.ONLINE).with_entities(
            AmmMarket.name,
            AmmMarket.fee_refund_rate).all()
        amm_market_map = dict(amm_markets)
        orders = AssetExchangeOrder.query.filter(
            AssetExchangeOrder.created_at >= date,
            AssetExchangeOrder.created_at < date + timedelta(days=1),
            AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED,
        ).with_entities(AssetExchangeOrder.id, AssetExchangeOrder.user_id).all()
        order_user_map = dict(orders)
        order_ids = list(order_user_map)
        spot_result, perpetual_result = defaultdict(Decimal), defaultdict(Decimal)
        for ids in batch_iter(order_ids, 5000):
            sys_orders = SysAssetExchangeOrder.query.filter(
                SysAssetExchangeOrder.exchange_order_id.in_(ids),
                SysAssetExchangeOrder.fee_amount > 0
            ).with_entities(
                SysAssetExchangeOrder.exchange_order_id,
                SysAssetExchangeOrder.market,
                SysAssetExchangeOrder.fee_amount,
                SysAssetExchangeOrder.fee_asset,
                SysAssetExchangeOrder.fee_type,
            )
            for item in sys_orders:
                if item.market not in amm_market_map:
                    continue
                user_id = order_user_map[item.exchange_order_id]
                user_id = sub_users.get(user_id, user_id)
                spot_result[user_id] += item.fee_amount * asset_rates.get(item.fee_asset, 0)\
                      * amm_market_map[item.market]
        data = TradeSummaryDB.daily_trade_fee_list(date)
        for row in data:
            if row['market'] not in amm_market_map:
                continue
            main_user_id = sub_users.get(row['user_id'], row['user_id'])
            spot_result[main_user_id] += row['fee'] * asset_rates.get(row['asset'], 0)\
                  * amm_market_map[row['market']]

        broker_refer_expense_query = BrokerReferralAssetDetail.query.filter(
            BrokerReferralAssetDetail.date == date,
        ).with_entities(
            BrokerReferralAssetDetail.user_id,
            func.sum(BrokerReferralAssetDetail.spot_fee_usd * BrokerReferralAssetDetail.refer_fee_usd_rate).label("spot_fee_usd"),
            func.sum(BrokerReferralAssetDetail.perpetual_fee_usd * BrokerReferralAssetDetail.refer_fee_usd_rate).label("perpetual_fee_usd"),
        ).group_by(
            BrokerReferralAssetDetail.user_id,
        ).all()

        for ref_row in broker_refer_expense_query:
            user_id = ref_row.user_id
            spot_result[user_id] += ref_row.spot_fee_usd
            perpetual_result[user_id] += ref_row.perpetual_fee_usd

        # 手续费返现券
        cashback_map = CashBackFeeCouponStatus.get_daily_cashback_result(date, asset_rates)
        for user_id, v in cashback_map['spot'].items():
            main_user_id = sub_users.get(user_id, user_id)
            spot_result[main_user_id] += v
        for user_id, v in cashback_map['exchange'].items():
            main_user_id = sub_users.get(user_id, user_id)
            spot_result[main_user_id] += v
        for user_id, v in cashback_map['perpetual'].items():
            main_user_id = sub_users.get(user_id, user_id)
            perpetual_result[main_user_id] += v
        spot_result = {k: quantize_amount(v, 8) for k, v in spot_result.items()}
        perpetual_result = {k: quantize_amount(v, 8) for k, v in perpetual_result.items()}
        return {
            TradeBusinessType.SPOT: spot_result,
            TradeBusinessType.PERPETUAL: perpetual_result
        }

    @classmethod
    def build_referral_history(cls, deal_user_ids: Set[int]) -> List[_Referral]:
        """ 构建交易用户的邀请关系 """

        _now = now()
        referrals = ReferralHistory.query.filter(
            ReferralHistory.status == ReferralHistory.Status.VALID
        ).all()
        ambassador_rows = (
            Ambassador.query.filter(Ambassador.status == Ambassador.Status.VALID)
            .with_entities(Ambassador.user_id, Ambassador.effected_at)
            .all()
        )
        ambassador_effected_at_map = {user_id: effected_at for user_id, effected_at in ambassador_rows}
        bus_amb_rows = BusRelationUserQuerier.get_all_valid_bus_ambassadors()
        for r in bus_amb_rows:
            if r.user_id not in ambassador_effected_at_map:
                ambassador_effected_at_map[r.user_id] = r.effected_at

        half_at = _now - ReferralBusiness.HALF_DURATION
        result = {}
        for row in referrals:
            user_id = row.referrer_id   # 邀请人
            ref_user_id = row.referree_id   # 被邀请人
            if ref_user_id not in deal_user_ids:
                continue

            # 判断返佣类型：如果是大使有效期间注册则按大使返佣，如果不是大使有效期间注册，则按普通返佣(旧逻辑)
            # 判断返佣类型：只要大使身份有效，邀请关系就认为是大使邀请(新逻辑)
            ref_type = "referral"
            if user_id in ambassador_effected_at_map:
                ref_type = "ambassador"

            result_key = (user_id, ref_type)
            if ref_type == "ambassador":  # 大使有效期间的邀请
                if not (ref := result.get(result_key)):
                    ref_rates = ReferralBusiness.get_ambassador_referral_rate(user_id)  # 返佣比例按大使等级
                    t = ReferralAssetHistory.Type.AMBASSADOR
                    result[result_key] = _Referral(
                        user_id=user_id,
                        ref_users=[_ReferralUser(
                            referral_id=row.referral_id,
                            user_id=ref_user_id
                        )],
                        asset=ReferralBusiness.GIFT_ASSETS[t],
                        spot_rate=ref_rates[TradeBusinessType.SPOT],
                        perpetual_rate=ref_rates[TradeBusinessType.PERPETUAL],
                        type=t
                    )
                else:
                    ref.ref_users.append(_ReferralUser(
                        referral_id=row.referral_id,
                        user_id=ref_user_id
                    ))

            else:      # 普通邀请 or 不是大使有效期间的邀请
                if not (ref := result.get(result_key)):
                    ref_rates = ReferralBusiness.get_vip_referral_rate(user_id)  # 返佣比例按VIP等级
                    if row.effected_at < half_at:   # 返佣减半
                        for k in ref_rates:
                            ref_rates[k] *= Decimal('0.5')
                    t = ReferralAssetHistory.Type.REFERRAL
                    result[result_key] = _Referral(
                        user_id=user_id,
                        ref_users=[_ReferralUser(
                            referral_id=row.referral_id,
                            user_id=ref_user_id
                        )],
                        asset=ReferralBusiness.GIFT_ASSETS[t],
                        spot_rate=ref_rates[TradeBusinessType.SPOT],
                        perpetual_rate=ref_rates[TradeBusinessType.PERPETUAL],
                        type=t
                    )
                else:
                    ref.ref_users.append(_ReferralUser(
                        referral_id=row.referral_id,
                        user_id=ref_user_id
                    ))

        return list(result.values())

    @classmethod
    def get_all_ee_special_ref_rate(cls) -> dict[int, Decimal]:
        """ 被邀请人的特殊返佣比例 """
        rows = SpecialReferreeUserRate.query.filter(
            SpecialReferreeUserRate.status == SpecialReferreeUserRate.Status.VALID,
        ).with_entities(
            SpecialReferreeUserRate.referree_id,
            SpecialReferreeUserRate.referrer_id,
            SpecialReferreeUserRate.rate,
        ).all()
        bus_amb_rows = BusRelationUserQuerier.get_all_valid_bus_ambassadors()
        bus_amb_shares_map = BusRelationUserQuerier.get_bus_ambassador_share_users_map()
        bus_amb_map = {i.user_id: i for i in bus_amb_rows}
        result = {}
        for r in rows:
            if r.referrer_id in bus_amb_map:
                # 被邀请人设置的新的返佣比例 对于 商务大使来说 是<商务大使+商务代理>整体的返佣比例
                # 此处返回值只算商务大使，还需乘以商务大使的返佣系数
                bus_amb = bus_amb_map[r.referrer_id]
                _shares = bus_amb_shares_map.get(r.referrer_id, [])
                _percent = BusinessAmbassador.calc_percent(bus_amb.rate, [i.rate for i in _shares])
                new_rate = quantize_amount(r.rate * _percent, 8)
            else:
                new_rate = r.rate
            result[r.referree_id] = new_rate
        return result


class AgentReferralAssetProcessor:
    """ 大使向它的代理返佣，大使代理的返佣可分为多份 """

    @classmethod
    def send_referral_reward(cls, date) -> bool:
        """ 执行当日的大使代理返佣的发放 """
        if ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == date,
            ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR_AGENT,
        ).first():
            return False
        if not TradeSummaryDB.is_data_completed(date) or not PerpetualSummaryDB.is_data_completed(date):
            return False
        if not CashBackFeeCouponStatus.check_daily_cashback_status(date):
            return False

        cls.make_referral_asset_detail(date)
        cls.summary_referral_asset_history(date)

        ref_asset_records = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == date,
            ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR_AGENT,
            ReferralAssetHistory.status == ReferralAssetHistory.Status.CREATED,
        ).all()
        created_at = date + timedelta(days=1)
        gift_rows = []
        for row in ref_asset_records:
            gift = GiftHistory(
                created_at=created_at,
                user_id=row.user_id,
                activity_id=Activity.REFERRAL_ID,
                asset=row.asset,
                amount=row.amount,
                remark="gift for refer agent",
                status=GiftHistory.Status.CREATED,
            )
            gift_rows.append(gift)
        for rows in batch_iter(gift_rows, 1000):
            db.session.bulk_save_objects(rows)
        ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == date,
            ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR_AGENT,
        ).update({ReferralAssetHistory.status: ReferralAssetHistory.Status.FINISHED}, synchronize_session=False)
        db.session.commit()
        return True

    @classmethod
    def get_ee_ref_to_broker_fees(cls, date_) -> tuple[dict[int, Decimal], dict[int, Decimal]]:
        broker_details: list[BrokerReferralAssetDetail] = BrokerReferralAssetDetail.query.filter(
            BrokerReferralAssetDetail.date == date_,
        ).all()
        ee_bro_spot_fee_map = defaultdict(Decimal)
        ee_bro_per_fee_map = defaultdict(Decimal)
        for r in broker_details:
            # 被邀请人可能同时给多个broker返佣了
            ee_bro_spot_fee_map[r.user_id] += r.spot_fee_usd * r.refer_fee_usd_rate
            ee_bro_per_fee_map[r.user_id] += r.perpetual_fee_usd * r.refer_fee_usd_rate
        return ee_bro_spot_fee_map, ee_bro_per_fee_map

    @classmethod
    def make_referral_asset_detail(cls, date):
        """ 生成当天待发放的（代理）返佣明细 """
        asset_rates = AssetPrice.get_close_price_map(date)
        fees = ReferralAssetProcessor.sum_deal_fees(date, asset_rates)
        spot_fees, perpetual_fees = fees[TradeBusinessType.SPOT], fees[TradeBusinessType.PERPETUAL]
        ee_bro_spot_fee_map, ee_bro_per_fee_map = cls.get_ee_ref_to_broker_fees(date)
        expense = ReferralAssetProcessor.sum_expense(date, asset_rates)
        spot_expense, perpetual_expense = expense[TradeBusinessType.SPOT], expense[TradeBusinessType.PERPETUAL]

        refs = ReferralAssetProcessor.build_referral_history(set(spot_fees.keys()) | set(perpetual_fees.keys()))
        exclude_ee_ids = ReferralAssetProcessor.get_exclude_referree_ids()

        ambassador_refs = [i for i in refs if i.type == ReferralAssetHistory.Type.AMBASSADOR]  # 大使邀请的关系
        agent_ref_dict = cls.build_agent_referral_history_dict(ambassador_refs)
        ambassador_id_agent_id_dict: Dict[int, int] = {
            amb_uid: agent_ref.user_id for agent_ref in agent_ref_dict.values() for amb_uid in agent_ref.ref_ambassadors
        }
        cleared_users = UserRepository.get_cleared_user_ids()
        agent_refer_detail_rows = []
        reports = []
        for amb_ref in ambassador_refs:
            if amb_ref.user_id not in ambassador_id_agent_id_dict:
                # 大使没绑定代理
                continue

            total_spot_fee = total_perpetual_fee = 0
            for ref_user in amb_ref.ref_users:
                if ref_user.user_id in exclude_ee_ids:
                    continue
                # 用于代理计算返佣数的手续费USD，邀请人是大使时：还要包括 给经纪商返佣的那部分手续费
                spot_fee = spot_fees.get(ref_user.user_id, 0)
                spot_fee += ee_bro_spot_fee_map.get(ref_user.user_id, 0)
                perpetual_fee = perpetual_fees.get(ref_user.user_id, 0)
                perpetual_fee += ee_bro_per_fee_map.get(ref_user.user_id, 0)

                spot_fee -= spot_expense.get(ref_user.user_id, 0)
                perpetual_fee -= perpetual_expense.get(ref_user.user_id, 0)

                if spot_fee < 0:
                    spot_fee = 0
                if perpetual_fee < 0:
                    perpetual_fee = 0
                total_spot_fee += spot_fee
                total_perpetual_fee += perpetual_fee
            agent_id = ambassador_id_agent_id_dict[amb_ref.user_id]
            if agent_id in cleared_users:
                continue
            agent_ref = agent_ref_dict[agent_id]
            gift_amount = total_spot_fee * agent_ref.referral_rate + total_perpetual_fee * agent_ref.referral_rate
            gift_amount = quantize_amount(gift_amount / asset_rates[agent_ref.asset], 8)
            if gift_amount == 0:
                continue

            ambassador_id = amb_ref.user_id
            agent_refer_detail_rows.append(
                AmbassadorAgentAssetDetail(
                    date=date,
                    user_id=agent_id,
                    agent_id=agent_id,
                    ambassador_id=ambassador_id,
                    asset=agent_ref.asset,
                    amount=gift_amount,
                )
            )

            deal_user_ids = [ref_user.user_id for ref_user in amb_ref.ref_users]
            fee_amount = total_spot_fee + total_perpetual_fee
            reports.append(
                DailyAmbassadorAgentAssetReport(
                    report_date=date,
                    user_id=ambassador_id,
                    agent_id=agent_id,
                    deal_user_count=len(deal_user_ids),
                    deal_user_bit_map=BitMap(deal_user_ids).serialize(),
                    fee_amount=fee_amount,
                    referral_amount=gift_amount,
                )
            )

        for rows in batch_iter(agent_refer_detail_rows, 1000):
            db.session.bulk_save_objects(rows)
        for rows in batch_iter(reports, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()

    @classmethod
    def summary_referral_asset_history(cls, date):
        agent_details = (
            AmbassadorAgentAssetDetail.query.filter(
                AmbassadorAgentAssetDetail.date == date,
            )
            .group_by(
                AmbassadorAgentAssetDetail.user_id,
                AmbassadorAgentAssetDetail.asset,
            )
            .with_entities(
                AmbassadorAgentAssetDetail.user_id,
                AmbassadorAgentAssetDetail.asset,
                func.sum(AmbassadorAgentAssetDetail.amount),
            )
            .all()
        )
        ref_history_rows = []
        for user_id, asset, amount in agent_details:
            # 大使代理返佣没区分spot_amount、perpetual_amount，存null
            ref_history_rows.append(
                ReferralAssetHistory(
                    date=date,
                    user_id=user_id,
                    type=ReferralAssetHistory.Type.AMBASSADOR_AGENT,
                    asset=asset,
                    amount=amount,
                )
            )
        for rows in batch_iter(ref_history_rows, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()

    @classmethod
    def build_agent_referral_history_dict(cls, ref_data: List[_Referral]) -> Dict[int, _Agent]:
        """ 根据已有大使的邀请关系，构建代理的邀请关系 """
        agent_rows = AmbassadorAgent.query.filter(
            AmbassadorAgent.status == AmbassadorAgent.Status.VALID,
        ).all()
        agent_row_dict = {x.user_id: x for x in agent_rows}  # 所有有效的代理dict

        agent_his_rows = AmbassadorAgentHistory.query.filter(
            AmbassadorAgentHistory.status == AmbassadorAgentHistory.Status.VALID,
        ).all()
        ambassador_id_agent_dict: Dict[int, AmbassadorAgent] = {}  # 大使ID -> 大使代理
        for row in agent_his_rows:
            agent_row = agent_row_dict.get(row.user_id)
            if agent_row:
                ambassador_id_agent_dict[row.ambassador_id] = agent_row

        result_agent_ref_dict: Dict[int, _Agent] = {}  # 有返佣的代理dict（字典去重：多个大使同一个代理）
        _type = ReferralAssetHistory.Type.AMBASSADOR_AGENT
        for ref in ref_data:
            assert ref.type == ReferralAssetHistory.Type.AMBASSADOR
            ambassador_user_id = ref.user_id  # 大使、邀请人
            agent_row = ambassador_id_agent_dict.get(ambassador_user_id)
            if not agent_row:
                continue

            # 大使有代理
            agent_ref = result_agent_ref_dict.get(agent_row.user_id)
            if not agent_ref:
                agent_ref = _Agent(
                    user_id=agent_row.user_id,
                    ref_ambassadors=[ambassador_user_id],
                    asset=ReferralBusiness.GIFT_ASSETS[_type],
                    referral_rate=agent_row.referral_rate,
                    type=_type,
                )
                result_agent_ref_dict[agent_row.user_id] = agent_ref
            else:
                agent_ref.ref_ambassadors.append(ambassador_user_id)
        return result_agent_ref_dict


class BrokerAssetProcessor:
    """ 经纪商返佣 """

    # 当某个用户是大使邀请时，同时将API提供给经纪商去交易，产生的手续费返佣，既要返给大使又要返给经纪商，
    # 但对于该用户，大使和经纪商的返佣比例都减半。
    # 即：当某个用户是大使邀请时，在对broker返佣，该用户通过broker产生的手续费，只用某个比例的值来计算返佣数目
    #     剩余比例的手续费后面算到大使返佣那里去（broker的expense变少）
    BROKER_SPECIAL_FEE_USD_RATE = Decimal("0.5")

    @classmethod
    def send_referral_reward(cls, date) -> bool:
        """ 执行当日的经纪商的发放 """
        if BrokerReferralAssetHistory.query.filter(
                BrokerReferralAssetHistory.date == date,
        ).first():
            return False
        if not TradeSummaryDB.is_data_completed(date) or not PerpetualSummaryDB.is_data_completed(date):
            return False

        cls.make_referral_asset_detail(date)
        cls.summary_referral_asset_history(date)

        ref_asset_records = BrokerReferralAssetHistory.query.filter(
            BrokerReferralAssetHistory.date == date,
            BrokerReferralAssetHistory.status == BrokerReferralAssetHistory.Status.CREATED,
        ).all()
        created_at = date + timedelta(days=1)
        gift_rows = []
        for row in ref_asset_records:
            gift = GiftHistory(
                created_at=created_at,
                user_id=row.user_id,
                activity_id=Activity.get_or_create_broker_referral_id(),
                asset=row.asset,
                amount=row.amount,
                remark="gift for broker",
                status=GiftHistory.Status.CREATED,
            )
            gift_rows.append(gift)
        for rows in batch_iter(gift_rows, 1000):
            db.session.bulk_save_objects(rows)
        BrokerReferralAssetHistory.query.filter(
            BrokerReferralAssetHistory.date == date,
        ).update({BrokerReferralAssetHistory.status: BrokerReferralAssetHistory.Status.FINISHED},
                 synchronize_session=False)
        db.session.commit()

        DailyIncomeProcessResultCache(date).set_bit(
            IncomeType.BROKER_PAY.value, True
        )

        return True

    @classmethod
    def sum_expense(cls, date, asset_rates) -> Dict[TradeBusinessType, Dict[int, Decimal]]:
        """支出(AMM分红)，汇总到主账户"""
        sub_users = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        sub_users = dict(sub_users)
        amm_markets = AmmMarket.query.filter(
            AmmMarket.status == AmmMarket.Status.ONLINE
        ).with_entities(
            AmmMarket.name,
            AmmMarket.fee_refund_rate).all()
        amm_market_map = dict(amm_markets)
        client_query = Broker.query.filter(
            Broker.status == Broker.Status.VALID
        ).all()
        broker_id_map = {i.broker_id: i.user_id for i in client_query}

        client_data = TradeSummaryDB.daily_client_trade_fee_list(date)
        spot_result = defaultdict(lambda: defaultdict(Decimal))

        for row in client_data:
            if row['market'] not in amm_market_map:
                continue
            main_user_id = sub_users.get(row['user_id'], row['user_id'])
            broker_id = get_broker_id_by_client_id(row['client_id'])
            if broker_id not in broker_id_map:
                continue
            broker_user_id = broker_id_map[broker_id]
            spot_result[main_user_id][broker_user_id] += row['fee'] * asset_rates.get(row['asset'], 0) \
                                         * amm_market_map[row['market']]

        for user_trade_data in spot_result.values():
            for broker_user_id in user_trade_data.keys():
                user_trade_data[broker_user_id] = quantize_amount(user_trade_data[broker_user_id], 8)

        return {
            TradeBusinessType.SPOT: spot_result,
            TradeBusinessType.PERPETUAL: dict()
        }

    @classmethod
    def sum_deal_fees(cls, date, asset_rates):
        sub_users = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        sub_users = dict(sub_users)
        market_makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
        market_makers = set(market_makers)

        client_query = Broker.query.filter(
            Broker.status == Broker.Status.VALID
        ).all()
        broker_id_map = {i.broker_id: i.user_id for i in client_query}
        result = dict()
        result[TradeBusinessType.SPOT] = defaultdict(lambda: defaultdict(Decimal))
        result[TradeBusinessType.PERPETUAL] = defaultdict(lambda: defaultdict(Decimal))
        for _type, _db in ((TradeBusinessType.SPOT, TradeSummaryDB), (TradeBusinessType.PERPETUAL, PerpetualSummaryDB)):
            data = _db.group_by_client_fee(date)
            for row in data:
                main_user_id = sub_users.get(row['user_id'], row['user_id'])
                if main_user_id in market_makers:
                    continue
                broker_id = get_broker_id_by_client_id(row['client_id'])
                if broker_id not in broker_id_map:
                    continue
                broker_user_id = broker_id_map[broker_id]
                result[_type][main_user_id][broker_user_id] += row['fee'] * asset_rates.get(row['asset'], 0)

        for broker_data in result.values():
            for user_trade_data in broker_data.values():
                for broker_user_id in user_trade_data.keys():
                    user_trade_data[broker_user_id] = quantize_amount(user_trade_data[broker_user_id], 8)

        return result

    @classmethod
    def get_ambassador_referree_ids(cls, ee_ids: list[int]) -> set[int]:
        """ 返回是大使邀请的user_ids """
        referrals = []
        for ch_ids in batch_iter(ee_ids, 2000):
            ch_refs = ReferralHistory.query.filter(
                ReferralHistory.status == ReferralHistory.Status.VALID,
                ReferralHistory.referree_id.in_(ch_ids),
            ).all()
            referrals.extend(ch_refs)

        ambassador_rows = (
            Ambassador.query.filter(Ambassador.status == Ambassador.Status.VALID)
            .with_entities(Ambassador.user_id, Ambassador.effected_at)
            .all()
        )
        ambassador_effected_at_map = {user_id: effected_at for user_id, effected_at in ambassador_rows}
        bus_amb_rows = BusRelationUserQuerier.get_all_valid_bus_ambassadors()
        for r in bus_amb_rows:
            if r.user_id not in ambassador_effected_at_map:
                ambassador_effected_at_map[r.user_id] = r.effected_at

        amb_ref_ee_ids = set()
        for row in referrals:
            user_id = row.referrer_id   # 邀请人
            ref_user_id = row.referree_id   # 被邀请人
            if user_id in ambassador_effected_at_map:
                amb_ref_ee_ids.add(ref_user_id)
        return amb_ref_ee_ids

    @classmethod
    def make_referral_asset_detail(cls, date):
        """ 生成当天待发放的经纪商返佣明细 """
        asset_rates = AssetPrice.get_close_price_map(date)
        fees = BrokerAssetProcessor.sum_deal_fees(date, asset_rates)
        spot_fees, perpetual_fees = fees[TradeBusinessType.SPOT], fees[TradeBusinessType.PERPETUAL]
        expense = BrokerAssetProcessor.sum_expense(date, asset_rates)
        spot_expense, _ = expense[TradeBusinessType.SPOT], expense[TradeBusinessType.PERPETUAL]

        user_trade_map = defaultdict(lambda: defaultdict(lambda: {
            "spot_fee_usd": Decimal(),
            "perpetual_fee_usd": Decimal(),
        }))
        for user_id, client_data in spot_fees.items():
            for broker_id, fee_amount in client_data.items():
                user_trade_map[user_id][broker_id]['spot_fee_usd'] += fee_amount
        for user_id, client_data in perpetual_fees.items():
            for broker_id, fee_amount in client_data.items():
                user_trade_map[user_id][broker_id]['perpetual_fee_usd'] += fee_amount
        for user_id, client_data in spot_expense.items():
            for broker_id, fee_amount in client_data.items():
                user_trade_map[user_id][broker_id]['spot_fee_usd'] -= fee_amount
                if user_trade_map[user_id][broker_id]['spot_fee_usd'] < 0:
                    user_trade_map[user_id][broker_id]['spot_fee_usd'] = 0

        broker_rate_map = {i.user_id: i.rate for i in Broker.query.all()}
        amb_ref_ee_ids = cls.get_ambassador_referree_ids(list(user_trade_map))
        exclude_ee_ids = ReferralAssetProcessor.get_exclude_referree_ids()

        broker_refer_detail_rows = []
        broker_asset = BrokerReferralAssetDetail.REFERRAL_ASSET
        broker_asset_rate = asset_rates[broker_asset]
        for user_id, client_data in user_trade_map.items():
            if user_id in exclude_ee_ids:
                continue
            if user_id in amb_ref_ee_ids:
                fee_usd_rate = cls.BROKER_SPECIAL_FEE_USD_RATE
            else:
                fee_usd_rate = Decimal(1)
            for broker_user_id, trade_data in client_data.items():
                if broker_user_id not in broker_rate_map:
                    continue
                broker_rate = broker_rate_map[broker_user_id]
                ref_amount = (trade_data['spot_fee_usd'] + trade_data['perpetual_fee_usd']) * broker_rate * fee_usd_rate
                gift_amount = quantize_amount(ref_amount / broker_asset_rate, 8)
                if gift_amount == 0:
                    continue

                broker_refer_detail_rows.append(
                    BrokerReferralAssetDetail(
                        date=date,
                        user_id=user_id,
                        broker_user_id=broker_user_id,
                        asset=BrokerReferralAssetDetail.REFERRAL_ASSET,
                        amount=gift_amount,
                        spot_fee_usd=trade_data['spot_fee_usd'],
                        perpetual_fee_usd=trade_data['perpetual_fee_usd'],
                        refer_fee_usd_rate=fee_usd_rate,
                    )
                )
        for rows in batch_iter(broker_refer_detail_rows, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()

    @classmethod
    def summary_referral_asset_history(cls, date):
        """从返佣明细汇总待发放的返佣流水"""

        ref_details = (
            BrokerReferralAssetDetail.query.filter(BrokerReferralAssetDetail.date == date)
                .group_by(
                BrokerReferralAssetDetail.broker_user_id,
                BrokerReferralAssetDetail.asset,
            )
                .with_entities(
                BrokerReferralAssetDetail.broker_user_id,
                BrokerReferralAssetDetail.asset,
                func.sum(BrokerReferralAssetDetail.amount),
            ).all()
        )

        ref_rows = []
        for user_id, asset, amount in ref_details:
            ref_rows.append(
                BrokerReferralAssetHistory(
                    date=date,
                    user_id=user_id,
                    asset=asset,
                    amount=amount,
                )
            )
        for rows in batch_iter(ref_rows, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()


@scheduled(crontab(minute=25, hour='0-3'))
@lock_call()
def send_referral_reward_schedule():
    from app.schedules.ambassador import update_amb_dashboard_schedule

    _today = today()
    _yesterday = _today - timedelta(days=1)
    _start_date = _today - timedelta(days=3)

    if not TradeSummaryDB.is_data_completed(_yesterday) or not PerpetualSummaryDB.is_data_completed(_yesterday):
        # 避免经纪商返佣未生成但其他返佣时生成
        return

    # 经纪商返佣
    broker_last = (
        BrokerReferralAssetHistory.query.order_by(
            BrokerReferralAssetHistory.date.desc()).first()
    )
    if not broker_last:
        broker_refer_start = _yesterday
    else:
        broker_refer_start = max(_start_date, broker_last.date+timedelta(days=1))
    while broker_refer_start < _today:
        if not BrokerAssetProcessor.send_referral_reward(broker_refer_start):
            break
        broker_refer_start += timedelta(days=1)

    # 普通发佣、大使返佣
    refer_last = (
        ReferralAssetHistory.query.filter(
            ReferralAssetHistory.type.in_([ReferralAssetHistory.Type.REFERRAL, ReferralAssetHistory.Type.AMBASSADOR]),
        )
        .order_by(ReferralAssetHistory.date.desc())
        .first()
    )
    if not refer_last:
        refer_start = _yesterday
    else:
        refer_start = max(_start_date, refer_last.date+timedelta(days=1))
    while refer_start < _today:
        if not ReferralAssetProcessor.send_referral_reward(refer_start):
            break
        start_str = refer_start.strftime("%Y-%m-%d")
        update_referral_rank_task.delay(start_str)
        refer_start += timedelta(days=1)

    # 代理返佣
    agent_last = (
        ReferralAssetHistory.query.filter(
            ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR_AGENT,
        )
        .order_by(ReferralAssetHistory.date.desc())
        .first()
    )
    if not agent_last:
        agent_start = _yesterday
    else:
        agent_start = max(_start_date, agent_last.date+timedelta(days=1))
    while agent_start < _today:
        if not AgentReferralAssetProcessor.send_referral_reward(agent_start):
            break
        agent_start += timedelta(days=1)

    # 商务相关的返佣
    BusAgentReferHelper.send_referral_reward(_yesterday)
    if _today.day == 1:
        _last_month = last_month(_today.year, _today.month)
        BusUserReferHelper.send_referral_reward(_last_month)

    # 大使面板更新任务
    update_amb_dashboard_schedule.delay()


@scheduled(crontab(minute=15, hour=0))
def update_referral_history_status_schedule():
    """更新邀请关系状态"""
    now_ = now()
    earliest = now_ - ReferralBusiness.EXPIRE_DURATION
    amb_earliest = now_ - ReferralBusiness.Ambassador_EXPIRE_DURATION

    ambassador_rows = (
        Ambassador.query.filter(Ambassador.status == Ambassador.Status.VALID)
        .with_entities(Ambassador.user_id, Ambassador.effected_at)
        .all()
    )
    ambassador_effected_at_map = {user_id: effected_at for user_id, effected_at in ambassador_rows}

    bus_amb_rows = BusinessAmbassador.query.filter(
        BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
    ).with_entities(
        BusinessAmbassador.user_id,
        BusinessAmbassador.effected_at,
    ).all()
    bus_amb_effected_at_map = {user_id: effected_at for user_id, effected_at in bus_amb_rows}

    refs = ReferralHistory.query.filter(
        ReferralHistory.status == ReferralHistory.Status.VALID
    ).all()
    for ref in refs:
        if ref.referrer_id in ambassador_effected_at_map:
            if ref.effected_at < amb_earliest:
                ref.status = ReferralHistory.Status.EXPIRED
            continue
        if ref.referrer_id in bus_amb_effected_at_map:
            if ref.effected_at < amb_earliest:
                ref.status = ReferralHistory.Status.EXPIRED
            continue

        # 不是大使 or 不在大使有效期间邀请, 走下面的普通返佣失效判断
        if ref.effected_at < earliest:
            ref.status = ReferralHistory.Status.EXPIRED
    db.session.commit()


@scheduled(crontab(minute=5, hour=0))
def update_ambassador_agent_history_schedule():
    """更新大使-大使代理关系"""
    history = AmbassadorAgentHistory.query.filter(
        AmbassadorAgentHistory.status == AmbassadorAgentHistory.Status.VALID
    ).all()
    duration = ReferralBusiness.AMBASSADOR_AGENT_EXPIRE_DURATION
    today_ = today()
    for item in history:
        item: AmbassadorAgentHistory
        start_date = item.effected_at.date()
        if today_ - start_date > duration:
            item.status = AmbassadorAgentHistory.Status.DELETED
    db.session.commit()


@celery_task
def update_referral_rank_task(report_date_str):
    """更新推荐返佣排名"""
    report_date = datetime.datetime.strptime(report_date_str, "%Y-%m-%d").date()
    rows = ReferralAssetHistory.query.filter(
        ReferralAssetHistory.type.in_((
            ReferralAssetHistory.Type.REFERRAL,
            ReferralAssetHistory.Type.AMBASSADOR
        ))
    ).group_by(
        ReferralAssetHistory.user_id,
        ReferralAssetHistory.asset
    ).with_entities(
        ReferralAssetHistory.user_id,
        ReferralAssetHistory.asset,
        func.sum(ReferralAssetHistory.amount)
    ).all()

    data = defaultdict(Decimal)
    exclude_amb_user_ids = [656367]  # 某些大使不想展示在`返佣排行榜页面`
    prices = AssetPrice.get_close_price_map(report_date)
    for user_id, asset, amount in rows:
        if user_id in exclude_amb_user_ids:
            continue
        data[user_id] += prices[asset] * amount

    result = []
    rank = 1
    for user_id, amount in sorted(data.items(), key=lambda x: x[1], reverse=True):
        result.append(dict(
            user_id=user_id,
            asset='USD',
            amount=amount_to_str(amount, 2),
            rank=rank
        ))
        rank += 1
        if rank == 4:
            break

    ReferralRankCache().set(json.dumps(result))


@scheduled(crontab(minute=41, hour="1-4"))
def update_referral_code_asset_detail_schedule():
    """ 汇总ReferralAssetDetail, 更新用户邀请码的返佣, user_id和referral_id纬度 """
    _today = today()
    last = ReferralCodeAssetDetail.query.order_by(ReferralCodeAssetDetail.date.desc()).first()
    if not last:
        report_date = _today - timedelta(days=1)
    else:
        report_date = last.date + timedelta(days=1)
    while report_date < _today:
        if not _update_referral_code_asset_detail(report_date, max_check_date=_today):
            break
        report_date += timedelta(days=1)


def _update_referral_code_asset_detail(query_date, max_check_date) -> bool:
    check_row = ReferralAssetDetail.query.filter(
        ReferralAssetDetail.date >= query_date, ReferralAssetDetail.date < max_check_date
    ).first()  # 大于等于query_date, 小于max_check_date（当天）, 某天的detail表可能没数据
    if not check_row:
        return False

    ref_asset_details = (
        ReferralAssetDetail.query.filter(ReferralAssetDetail.date == query_date)
        .group_by(
            ReferralAssetDetail.user_id,
            ReferralAssetDetail.referral_id,
            ReferralAssetDetail.type,
            ReferralAssetDetail.asset,
        )
        .with_entities(
            ReferralAssetDetail.user_id,
            ReferralAssetDetail.referral_id,
            ReferralAssetDetail.type,
            ReferralAssetDetail.asset,
            func.sum(ReferralAssetDetail.spot_amount).label("total_spot_amount"),
            func.sum(ReferralAssetDetail.perpetual_amount).label("total_perpetual_amount"),
            func.sum(ReferralAssetDetail.spot_fee_usd).label("total_spot_fee_usd"),
            func.sum(ReferralAssetDetail.perpetual_fee_usd).label("total_perpetual_fee_usd"),
        )
        .all()
    )

    all_referral_data_map = defaultdict(
        lambda: {
            "total_spot_amount": Decimal(),
            "total_perpetual_amount": Decimal(),
            "total_spot_fee_usd": Decimal(),
            "total_perpetual_fee_usd": Decimal(),
        }
    )  # summary all referral_id
    new_code_details = []
    for row in ref_asset_details:
        new_code_details.append(
            ReferralCodeAssetDetail(
                date=query_date,
                user_id=row.user_id,
                referral_id=row.referral_id,
                type=row.type,
                asset=row.asset,
                spot_amount=row.total_spot_amount,
                perpetual_amount=row.total_perpetual_amount,
                spot_fee_usd=row.total_spot_fee_usd,
                perpetual_fee_usd=row.total_perpetual_fee_usd,
            )
        )
        data = all_referral_data_map[(row.user_id, row.type, row.asset)]
        data["total_spot_amount"] += row.total_spot_amount
        data["total_perpetual_amount"] += row.total_perpetual_amount
        data["total_spot_fee_usd"] += row.total_spot_fee_usd
        data["total_perpetual_fee_usd"] += row.total_perpetual_fee_usd

    for key_, data in all_referral_data_map.items():
        user_id, type_, asset = key_
        new_code_details.append(
            ReferralCodeAssetDetail(
                date=query_date,
                user_id=user_id,
                referral_id=None,
                type=type_,
                asset=asset,
                spot_amount=data["total_spot_amount"],
                perpetual_amount=data["total_perpetual_amount"],
                spot_fee_usd=data["total_spot_fee_usd"],
                perpetual_fee_usd=data["total_perpetual_fee_usd"],
            )
        )

    for rows in batch_iter(new_code_details, 1000):
        db.session.bulk_save_objects(rows)
    db.session.commit()
    return True


@scheduled(crontab(minute=42, hour='1-4'))
def update_referral_asset_summary_schedule():
    """ 汇总ReferralAssetDetail, 更新用户返佣累计 """
    _today = today()
    last_summary = ReferralAssetSummary.query.order_by(ReferralAssetSummary.summary_date.desc()).first()
    if not last_summary:
        # 从ReferralAssetDetail最旧的开始
        first_detail = ReferralAssetDetail.query.order_by(ReferralAssetDetail.date.asc()).first()
        if not first_detail:
            return
        start = first_detail.date
    else:
        start = last_summary.summary_date + timedelta(days=1)

    while start < _today:
        if not _update_referral_asset_summary(start, max_check_date=_today):
            break
        start += timedelta(days=1)


def _update_referral_asset_summary(query_date, max_check_date) -> bool:
    # 不能直接查询等于query_date的数据, 如果某天没detail数据会一直无法汇总
    check_row = ReferralAssetDetail.query.filter(
        ReferralAssetDetail.date >= query_date, ReferralAssetDetail.date < max_check_date
    ).first()  # 大于等于query_date, 小于max_check_date（当天）, 某天的detail表可能没数据
    if not check_row:
        return False

    ref_details = ReferralAssetDetail.query.filter(ReferralAssetDetail.date == query_date).all()
    to_add_summary_list = []
    summary_query = ReferralAssetSummary.query.all()
    summary_info_map = {(x.user_id, x.referree_id, x.type, x.asset): x for x in summary_query}
    for detail in ref_details:
        summary = summary_info_map.get((detail.user_id, detail.referree_id, detail.type, detail.asset))
        if summary:
            summary.spot_amount += detail.spot_amount
            summary.perpetual_amount += detail.perpetual_amount
            summary.summary_date = query_date
        else:
            new_summary = ReferralAssetSummary(
                user_id=detail.user_id,
                referral_id=detail.referral_id,
                referree_id=detail.referree_id,
                type=detail.type,
                asset=detail.asset,
                spot_amount=detail.spot_amount,
                perpetual_amount=detail.perpetual_amount,
                summary_date=query_date,
            )
            to_add_summary_list.append(new_summary)

    for summary in summary_info_map.values():
        # 某天的detail可能没数据
        summary.summary_date = query_date

    for rows in batch_iter(to_add_summary_list, 1000):
        db.session.bulk_save_objects(rows)
    db.session.commit()
    return True


@scheduled(crontab(minute='43', hour='1-4'))
def update_user_referral_slice_schedule():
    """统计用户每日邀请数据"""
    _today = today()
    last = DailyUserReferralSlice.query.order_by(
        DailyUserReferralSlice.date.desc()
    ).first()
    if not last:
        report_date = _today - timedelta(days=1)
    else:
        report_date = last.date + timedelta(days=1)
    while report_date < _today:
        if not _update_user_referral_slice(report_date, max_check_date=_today):
            break
        report_date += timedelta(days=1)


def _update_user_referral_slice(date, max_check_date) -> bool:
    check_row = ReferralAssetDetail.query.filter(
        ReferralAssetDetail.date >= date, ReferralAssetDetail.date < max_check_date
    ).first()  # 大于等于query_date, 小于max_check_date（当天）, 某天的detail表可能没数据
    if not check_row:
        return False

    ref_counts = ReferralHistory.query.filter(
        ReferralHistory.created_at >= date,
        ReferralHistory.created_at < date + timedelta(days=1)
    ).group_by(
        ReferralHistory.referrer_id,
        ReferralHistory.referral_id,
    ).with_entities(
        ReferralHistory.referrer_id,
        ReferralHistory.referral_id,
        func.count('*')
    ).all()
    ref_counts = {(x, y): z for x, y, z in ref_counts}

    ref_assets = ReferralAssetDetail.query.filter(
        ReferralAssetDetail.date == date
    ).group_by(
        ReferralAssetDetail.user_id,
        ReferralAssetDetail.referral_id,
        ReferralAssetDetail.asset
    ).with_entities(
        ReferralAssetDetail.user_id,
        ReferralAssetDetail.referral_id,
        ReferralAssetDetail.asset,
        func.sum(ReferralAssetDetail.spot_amount + ReferralAssetDetail.perpetual_amount).label('amount')
    ).all()

    prices = AssetPrice.get_close_price_map(date)
    result = []
    to_insert_refer_keys = set()  # item: (user_id, referral_id)
    for row in ref_assets:
        # 有返佣的，不管是否邀请
        usd = quantize_amount(row.amount * prices[row.asset], 2)
        ref_key = (row.user_id, row.referral_id)
        to_insert_refer_keys.add(ref_key)
        result.append(
            DailyUserReferralSlice(
                date=date,
                user_id=row.user_id,
                referral_id=row.referral_id,
                referral_count=ref_counts.get(ref_key, 0),
                amount_usd=usd,
            )
        )
    for ref_key_, ref_count in ref_counts.items():
        # 没返佣 但是有新邀请的
        if ref_key_ not in to_insert_refer_keys:
            result.append(
                DailyUserReferralSlice(
                    date=date,
                    user_id=ref_key_[0],
                    referral_id=ref_key_[1],
                    referral_count=ref_count,
                    amount_usd=Decimal(),
                )
            )

    # summary
    summary = {}
    for row in result:
        if row.user_id not in summary:
            summary[row.user_id] = DailyUserReferralSlice(
                date=date,
                user_id=row.user_id,
                referral_id=None,
                referral_count=row.referral_count,
                amount_usd=row.amount_usd
            )
        else:
            r = summary[row.user_id]
            r.referral_count += row.referral_count
            r.amount_usd += row.amount_usd
    for row in summary.values():
        result.append(row)

    for rows in batch_iter(result, 1000):
        db.session.bulk_save_objects(rows)
    db.session.commit()
    return True


@scheduled(crontab(minute='13', hour='1-4'))
def update_user_referral_type_slice_schedule():
    """统计用户每日邀请数据(用户 + 邀请类型 + referral_id 维度)"""
    _today = today()
    last = DailyUserReferralTypeSlice.query.order_by(
        DailyUserReferralTypeSlice.date.desc()
    ).first()
    if not last:
        report_date = _today - timedelta(days=1)
    else:
        report_date = last.date + timedelta(days=1)
    while report_date < _today:
        if not _update_user_referral_type_slice(report_date, max_check_date=_today):
            break
        report_date += timedelta(days=1)


def _update_user_referral_type_slice(date, max_check_date) -> bool:
    check_row = ReferralAssetDetail.query.filter(
        ReferralAssetDetail.date >= date, ReferralAssetDetail.date < max_check_date
    ).first()  # 大于等于query_date, 小于max_check_date（当天）, 某天的detail表可能没数据
    if not check_row:
        return False

    ref_counts = ReferralHistory.query.filter(
        ReferralHistory.created_at >= date,
        ReferralHistory.created_at < date + timedelta(days=1)
    ).group_by(
        ReferralHistory.referrer_id,
        ReferralHistory.referral_id,
        ReferralHistory.referral_type,
    ).with_entities(
        ReferralHistory.referrer_id,
        ReferralHistory.referral_id,
        ReferralHistory.referral_type,
        func.count('*')
    ).all()
    ref_counts = {(w, x, y): z for w, x, y, z in ref_counts}

    ref_assets = ReferralAssetDetail.query.filter(
        ReferralAssetDetail.date == date
    ).group_by(
        ReferralAssetDetail.user_id,
        ReferralAssetDetail.referral_id,
        ReferralAssetDetail.asset,
        ReferralAssetDetail.type,
    ).with_entities(
        ReferralAssetDetail.user_id,
        ReferralAssetDetail.referral_id,
        ReferralAssetDetail.asset,
        ReferralAssetDetail.type,
        func.sum(ReferralAssetDetail.spot_amount + ReferralAssetDetail.perpetual_amount).label('amount')
    ).all()

    prices = AssetPrice.get_close_price_map(date)
    result = []
    to_insert_refer_keys = set()  # item: (user_id, referral_id, type)
    for row in ref_assets:
        # 有返佣的，不管是否邀请
        usd = quantize_amount(row.amount * prices[row.asset], 2)
        if row.type is ReferralAssetHistory.Type.REFERRAL:
            type_ = ReferralHistory.ReferralType.NORMAL
        else:
            type_ = ReferralHistory.ReferralType.AMBASSADOR
        ref_key = (row.user_id, row.referral_id, type_)
        to_insert_refer_keys.add((row.user_id, row.referral_id, row.type))
        result.append(
            DailyUserReferralTypeSlice(
                date=date,
                user_id=row.user_id,
                referral_id=row.referral_id,
                type=row.type,
                referral_count=ref_counts.get(ref_key, 0),
                amount=row.amount,
                amount_usd=usd,
            )
        )
    for ref_key_, ref_count in ref_counts.items():
        # 没返佣 但是有新邀请的
        if ref_key_[2] is ReferralHistory.ReferralType.NORMAL:
            _type = ReferralAssetHistory.Type.REFERRAL
        else:
            _type = ReferralAssetHistory.Type.AMBASSADOR
        new_ref_key_ = (ref_key_[0], ref_key_[1], _type)
        if new_ref_key_ not in to_insert_refer_keys:
            result.append(
                DailyUserReferralTypeSlice(
                    date=date,
                    user_id=new_ref_key_[0],
                    referral_id=new_ref_key_[1],
                    type=new_ref_key_[2],
                    referral_count=ref_count,
                    amount=Decimal(),
                    amount_usd=Decimal(),
                )
            )

    # summary
    summary = {}
    for row in result:
        key = (row.user_id, row.type)
        if key not in summary:
            summary[key] = DailyUserReferralTypeSlice(
                date=date,
                user_id=row.user_id,
                referral_id=None,
                type=row.type,
                referral_count=row.referral_count,
                amount=row.amount,
                amount_usd=row.amount_usd
            )
        else:
            r = summary[key]
            r.referral_count += row.referral_count
            r.amount += row.amount
            r.amount_usd += row.amount_usd
    for row in summary.values():
        result.append(row)

    for rows in batch_iter(result, 1000):
        db.session.bulk_save_objects(rows)
    db.session.commit()
    return True


@scheduled(crontab(hour="2,4", minute='5'))
@lock_call()
def filter_user_insert_to_potential_clue():
    """根据不同配置的线索筛选用户插入线索库中"""
    for source_cls in [
        InvalidAmbassadorSource,
        RejectedAmbassadorSource,
        PotentialUserSource,
        QualityUserSource
    ]:
        source_cls().save_potential_users()


@scheduled(crontab(minute=10, hour="*/1"))
@lock_call()
def check_potential_clue_user():
    _now = now()
    potentials = PotentialAmbassador.query.filter(
        PotentialAmbassador.status == PotentialAmbassador.Status.CREATED
    ).all()
    user_mapper = {p.user_id: p for p in potentials}
    ambassadors = Ambassador.query.filter(
        Ambassador.user_id.in_(user_mapper.keys()),
        Ambassador.status == Ambassador.Status.VALID
    ).with_entities(
        Ambassador.id,
        Ambassador.user_id,
        Ambassador.effected_at
    ).all()
    
    bus_amb_user_ids = set()  # 商务大使列表
    for item in BusinessAmbassador.query.filter(
        BusinessAmbassador.user_id.in_(user_mapper.keys()),
        BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
        BusinessAmbassador.effected_at < _now,
    ).with_entities(
        BusinessAmbassador.user_id,
        BusinessAmbassador.effected_at,
        BusinessAmbassador.status,
    ).all():
        bus_amb_user_ids.add(item.user_id)
    
    # 已经成为了大使，如果在商务大使列表中的就是商务转化，其他是平台转化
    ambassador_user_set = set()
    for ambassador_id, user_id, _ in ambassadors:
        ambassador_user_set.add(user_id)
        potential = user_mapper[user_id]
        if user_id in bus_amb_user_ids:
            status = PotentialAmbassador.Status.BUSINESS
        else:
            status = PotentialAmbassador.Status.PUSH
        potential.status = status
        potential.ambassador_id = ambassador_id

    # 成为了商务大使，全部都是商务转化
    for user_id in bus_amb_user_ids:
        potential = user_mapper.get(user_id, None)
        if not potential:
            continue
        potential.status = PotentialAmbassador.Status.BUSINESS

    trace_user_set = {i for i, in AmbassadorBusinessTrace.query.with_entities(AmbassadorBusinessTrace.user_id).all()}
    need_add_business_potentials = {
        user_id
        for user_id, p in user_mapper.items()
        if p.created_at + timedelta(days=PotentialAmbassador.CHANGE_STATUS_DAYS) < _now
    }
    # 需要添加到商务线索库的记录 = 所有高潜用户 - 已经转化用户 - 已经加入商务线索库用户
    business_trace_set = need_add_business_potentials - ambassador_user_set - trace_user_set
    mobile_mapper = {
        u.id: u for u in User.query.filter(
            User.id.in_(business_trace_set)
        ).all()
    }
    telegram_mapper = {
        user_id: telegram for user_id, telegram in AmbassadorApplication.query.filter(
            AmbassadorApplication.user_id.in_(business_trace_set)
        ).with_entities(
            AmbassadorApplication.user_id,
            AmbassadorApplication.telegram
        ).all()
    }
    for user_id in business_trace_set:
        potential = user_mapper[user_id]
        trace = AmbassadorBusinessTrace(
            potential_id=potential.id,
            user_id=user_id,
            mobile=mobile_mapper[user_id].mobile if user_id in mobile_mapper else '',
            telegram=telegram_mapper.get(user_id)
        )
        db.session.add(trace)
        db.session.flush()
        db.session.add(BusinessTraceChangeHistory(
            business_trade_id=trace.id,
            type=BusinessTraceChangeHistory.Type.STATUS,
            value=trace.status.value
        ))
        db.session.add(
            Message(
                user_id=user_id,
                title=MessageTitle.POTENTIAL_USER.name if potential.source == PotentialAmbassador.Source.POTENTIAL_USER
                else MessageTitle.AMBASSADOR_BUSINESS_TRACE.name,
                content=MessageContent.POTENTIAL_USER.name if potential.source == PotentialAmbassador.Source.POTENTIAL_USER
                else MessageContent.AMBASSADOR_BUSINESS_TRACE.name,
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.APPLY_AMBASSADOR.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.POPUP_WINDOW,
                expired_at=now() + timedelta(days=7),
                channel=Message.Channel.ACTIVITY,
            )
        )
    db.session.commit()


@scheduled(crontab(minute=30, hour="*/1"))
@lock_call()
def check_business_clue_user():
    # 商务线索库里，待跟进和正在跟进的, 检查是否已经成为大使
    traces = AmbassadorBusinessTrace.query.filter(
        AmbassadorBusinessTrace.status.in_((
            AmbassadorBusinessTrace.Status.CREATED,
            AmbassadorBusinessTrace.Status.PROGRESS
        ))
    ).all()
    potential_mapper = {t.potential_id: t for t in traces}

    # 已经成为大使的高潜线索
    pot_query = PotentialAmbassador.query.join(Ambassador).filter(
        PotentialAmbassador.id.in_(potential_mapper.keys()),
        Ambassador.status == Ambassador.Status.VALID
    ).with_entities(
        PotentialAmbassador.id,
        Ambassador.id
    ).all()

    _now, bus_amb_user_ids = now(), set()  # 商务大使列表
    for item in BusinessAmbassador.query.filter(
            BusinessAmbassador.effected_at < _now,
    ).with_entities(
        BusinessAmbassador.user_id,
        BusinessAmbassador.effected_at,
        BusinessAmbassador.status,
    ).all():
        if item.status == BusinessAmbassador.Status.VALID:
            bus_amb_user_ids.add(item.user_id)

    # 已经成为了大使，如果是商务大使，则是商务转化，如果不是商务大使，则是平台转化
    for pot_id, ambassador_id in pot_query:
        trace = potential_mapper[pot_id]
        if trace.user_id in bus_amb_user_ids:
            trace.status = AmbassadorBusinessTrace.Status.BUSINESS
        else:
            trace.status = AmbassadorBusinessTrace.Status.PUSH
    
    potential_user_mapper = {t.user_id: t for t in traces}
    # 已经成为了商务大使，全部都是商务转化
    for user_id in bus_amb_user_ids:
        trace = potential_user_mapper.get(user_id, None)
        if not trace:
            continue
        trace.status = AmbassadorBusinessTrace.Status.BUSINESS
    db.session.commit()


@scheduled(crontab(minute=30, hour="*/1"), queue=CeleryQueues.REAL_TIME)
def update_user_first_normal_refer_time_cache():
    # 第一次生成数据需要扫描全表，速度较慢，使用脚本先生成
    model = ReferralHistory
    id_cache = UserFirstNormalReferLastIDCache()
    last_id = id_cache.read()
    batch = 3000
    while True:
        query = model.query
        if last_id:
            query = query.filter(model.id > last_id)
        data = query.order_by(model.id).limit(batch).with_entities(
            model.created_at,
            model.id,
            model.referrer_id
        ).all()
        if not data:
            return

        user_id_set = set(i.referrer_id for i in data)
        data_cache = UserFirstNormalReferTimeCache()
        cache_dict = dict(data_cache.hmget_with_keys(list(user_id_set)))

        reverse_sorted_data = sorted(data, key=lambda x: x.created_at, reverse=True)
        # 使用倒序排序数据生成的字典，相同 key 赋值时，时间小的 value 会覆盖大的
        user_first_time_dict = {
            i.referrer_id: datetime_to_str(i.created_at) for i in reverse_sorted_data if i.referrer_id not in cache_dict
        }
        if user_first_time_dict:
            data_cache.hmset(user_first_time_dict)
        last_id = data[-1].id
        id_cache.set(last_id)
        time.sleep(0.1)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_friend_gift_referral():
    coupon_and_apply_list, _ = CouponTool.get_friend_gift_coupon_and_apply_list()
    if not coupon_and_apply_list:
        return

    once_limit = 1000
    start_at = min([i[1].send_at for i in coupon_and_apply_list])

    def filter_referral_history():
        records = []
        while True:
            chunk_rows = ReferralHistory.query.order_by(
                ReferralHistory.id.desc()
            ).with_entities(
                ReferralHistory.referrer_id,
                ReferralHistory.referree_id,
                ReferralHistory.effected_at,
                ReferralHistory.status
            ).offset(len(records)).limit(once_limit).all()
            for r in chunk_rows:
                records.append({
                    "referrer_id": r.referrer_id,
                    "referree_id": r.referree_id,
                    "effected_at": r.effected_at,
                    "status": r.status
                })
                if r.effected_at < start_at:
                    return records
            if len(chunk_rows) != once_limit:
                return records

    ref_histories = filter_referral_history()
    success_dynamic_types = set()
    for coupon, apply in coupon_and_apply_list:
        if _update_ref_gift_coupon_referral_users(apply, coupon, ref_histories):
            success_dynamic_types.add(apply.dynamic_user_type)
    for _dy_type in success_dynamic_types:
        update_coupon_dynamic_user_task.delay(_dy_type.name)


def _update_ref_gift_coupon_referral_users(coupon_apply: CouponApply, coupon: Coupon, ref_histories: List[Dict]):
    pool = CouponPool.query.filter(
        CouponPool.apply_coupon_id == coupon_apply.id
    ).first()
    if not pool:
        raise

    current_pool_users = pool.get_send_user_ids()

    def get_coupon_security_users(ref_user_ids):
        add_users = set(ref_user_ids) - set(current_pool_users)
        log_id_rows = LoginHistory.query.filter(
            LoginHistory.user_id.in_(add_users),
        ).group_by(LoginHistory.user_id).with_entities(
            LoginHistory.user_id,
            func.max(LoginHistory.id).label("log_id"),
        ).all()
        log_ids = [i.log_id for i in log_id_rows]
        log_rows = LoginHistory.query.filter(
            LoginHistory.id.in_(log_ids),
        ).with_entities(
            LoginHistory.user_id,
            LoginHistory.ip,
            LoginHistory.device_id,
        ).all()
        log_user_ids = set()
        risk_users_mapper = defaultdict(set)
        for user_id, ip, device_id in log_rows:
            log_user_ids.add(user_id)
            ip_risk_cache = BaseCouponService.get_risk_cache(
                pool,
                user_id,
                limit_type=CouponRisk.LimitType.IP,
                value=ip
            )
            device_risk_cache = BaseCouponService.get_risk_cache(
                pool,
                user_id,
                limit_type=CouponRisk.LimitType.DEVICE_ID,
                value=device_id
            )
            if ip_risk_cache:
                ip_limit_count = ip_risk_cache.get_limit_count()
                if ip_risk_cache.count() >= ip_limit_count:
                    risk_users_mapper[(pool.id, CouponRisk.LimitType.IP.name, ip)].add(user_id)
            if device_risk_cache:
                device_limit_count = device_risk_cache.get_limit_count()
                if device_risk_cache.count() >= device_limit_count:
                    risk_users_mapper[(pool.id, CouponRisk.LimitType.DEVICE_ID.name, device_id)].add(user_id)
            if ip_risk_cache:
                ip_risk_cache.add_value(ip)
            if device_risk_cache:
                device_risk_cache.add_value(device_id)
        risk_user_set = {id_ for type_set in risk_users_mapper.values() for id_ in type_set}
        security_users = log_user_ids - risk_user_set
        not_login_users = set(add_users) - log_user_ids

        for key, users in risk_users_mapper.items():
            pool_id, limit_type, value = key
            BaseCouponService.insert_coupon_risk_users(
                pool_id=pool_id,
                limit_type=limit_type,
                value=value,
                users=users
            )

        return not_login_users | security_users

    if coupon_apply.dynamic_user_type == CouponApply.DynamicUser.REFERRAL_GIFT:
        is_single = True
    elif coupon_apply.dynamic_user_type == CouponApply.DynamicUser.REFERRAL_GIFT_MUL:
        is_single = False
    else:
        raise

    start_at = coupon_apply.send_at
    end_at = start_at + timedelta(days=coupon.receivable_days)
    referral_ids = coupon_apply.get_users()
    histories = []
    for h in ref_histories:
        if h["status"] != ReferralHistory.Status.VALID:
            continue
        if h["referrer_id"] not in referral_ids:
            continue
        if h["effected_at"] < start_at or h["effected_at"] > end_at:
            continue
        histories.append(h)

    referree_ids = []
    if is_single:
        for _, ref_data in group_by(lambda x: x["referrer_id"], histories).items():
            first_history = min(ref_data, key=lambda x: x["effected_at"])
            referree_ids.append(first_history["referree_id"])
    else:
        add_pool_users = get_coupon_security_users([r["referree_id"] for r in histories])
        referree_ids.extend(add_pool_users | set(current_pool_users))
    if referree_ids:
        dynamic_user_type = coupon_apply.dynamic_user_type
        DynamicUserCouponCache(
            dynamic_user_type.name,
            coupon.coupon_type.name,
            coupon_apply.id,
        ).set_users(referree_ids)
        return referree_ids


@scheduled(crontab(hour="*/1", minute=0))
@lock_call()
def insert_special_code_ambassador_application():
    special_codes = ["mg2sa", "xprogram"]
    referral_list = Referral.query.filter(
        Referral.code.in_(special_codes),
    ).with_entities(
        Referral.id
    ).all()
    if not referral_list:
        return

    started_at = now() - timedelta(days=1)
    history_list = ReferralHistory.query.filter(
        ReferralHistory.referral_id.in_([i.id for i in referral_list]),
        ReferralHistory.created_at >= started_at,
    ).with_entities(
        ReferralHistory.referree_id.distinct().label("user_id")
    ).all()
    ref_user_ids = {i.user_id for i in history_list}
    application_list = AmbassadorApplication.query.filter(
        AmbassadorApplication.user_id.in_(ref_user_ids)
    ).with_entities(
        AmbassadorApplication.user_id.distinct().label("user_id")
    ).all()
    application_user_ids = {i.user_id for i in application_list}
    insert_user_ids = (ref_user_ids - application_user_ids)
    default_value = ''
    insert_list = [
        AmbassadorApplication(
            user_id=user_id,
            type=AmbassadorApplication.Type.AMBASSADOR,
            name=default_value,
            mobile_num=default_value,
            city=default_value,
            profession=default_value,
            proficient_langs=default_value,
            community_scale=AmbassadorApplication.CommunityScale.BELOW_200,
            reason=AmbassadorApplication.Reason.OTHER,
            plan=default_value,
            plan_detail=default_value,
            referral_code=default_value,
        ) for user_id in insert_user_ids]

    for rows in batch_iter(insert_list, 1000):
        db.session.bulk_save_objects(rows)
    db.session.commit()


@celery_task()
@lock_call(with_args=True)
def audit_viabtc_pool_ambassador_application_task(user_id: int):
    """ 自动审核通过矿池大使 """
    user_: User = User.query.get(user_id)
    if user_.is_sub_account or not user_.email:
        return
    if BusRelationUserQuerier.get_bus_ambassador(user_id, need_valid=True):
        raise ValueError(f"用户{user_id}存在生效中的商务大使")
    if AmbassadorBusiness.get_ambassador(user_id):
        raise ValueError(f"用户{user_id}存在生效中的普通大使")

    AmbassadorApplication.query.filter(
        AmbassadorApplication.user_id == user_id,
        AmbassadorApplication.type == AmbassadorApplication.Type.AMBASSADOR,
        AmbassadorApplication.status == AmbassadorApplication.Status.CREATED,
    ).update(dict(status=AmbassadorApplication.Status.AUDITED), synchronize_session=False)

    ViaBtcPoolAmbBusiness.add_special_ref_rates(user_id, admin_user_id=0)
    AmbassadorBusiness.become_ambassador(
        user_id=user_id,
        source=Ambassador.Source.VIABTC_POOL,
    )
    db.session.commit()


@scheduled(crontab(hour="0,21,22,23", minute='5'))
@lock_call()
def scan_special_api_referree_user_schedule():
    """ 扫描被邀请人是API交易用户，加到特殊返佣配置表 """
    last_r = DailyApiRequest.query.order_by(DailyApiRequest.time.desc()).first()
    if last_r:
        last_dt = last_r.time
    else:
        last_dt = today() - timedelta(days=1)

    o_group_names = {
        UserApiFrequencyLimitRecord.ApiGroups.ORDER.name,
        UserApiFrequencyLimitRecord.ApiGroups.ORDERS.name,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDER.name,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDERS.name,
    }
    req_rows = DailyApiRequest.query.filter(
        DailyApiRequest.time == last_dt,
        DailyApiRequest.group.in_(o_group_names),
    ).with_entities(
        DailyApiRequest.user_id.distinct().label('user_id'),
    ).all()
    api_user_ids = {i.user_id for i in req_rows if i.user_id}

    api_sub_main_map = dict()
    for ch_ids in batch_iter(api_user_ids, 5000):
        ch_subs = SubAccount.query.filter(
            SubAccount.user_id.in_(ch_ids)
        ).with_entities(
            SubAccount.main_user_id,
            SubAccount.user_id,
        ).all()
        for r in ch_subs:
            api_sub_main_map[r.user_id] = r.main_user_id

    # 开通api 且 某个主子账号存在最后一笔成交为api下单
    last_api_trade_main_users = {api_sub_main_map.get(i, i) for i in api_user_ids}
    ee_ref_info_map = dict()  # 被邀请人&使用了API的user_id：邀请人user_id
    for ch_mids in batch_iter(last_api_trade_main_users, 5000):
        ch_ref_rows = ReferralHistory.query.filter(
            ReferralHistory.referree_id.in_(ch_mids),
            ReferralHistory.status == ReferralHistory.Status.VALID,
        ).with_entities(
            ReferralHistory.id,
            ReferralHistory.referree_id,
            ReferralHistory.referrer_id,
        ).all()
        ee_ref_info_map.update({i.referree_id: [i.referrer_id, i.id] for i in ch_ref_rows})

    sp_rows = SpecialReferreeUserRate.query.all()
    sp_ref_user_row_map = {}
    for r in sp_rows:
        sp_ref_user_row_map[r.referree_id] = r
        ee_ref_info_map[r.referree_id] = [r.referrer_id, r.referral_id]

    now_ = now()
    all_er_ids = set(i[0] for i in ee_ref_info_map.values())
    er_ref_rates_map = ReferralBusiness.batch_get_users_referral_rate(all_er_ids)
    ref_type_map = {
        "AMB": SpecialReferreeUserRate.ReferrerType.AMB,
        "BUS_AMB": SpecialReferreeUserRate.ReferrerType.BUS_AMB,
        "VIP": SpecialReferreeUserRate.ReferrerType.NORMAL,
    }
    for ee_id, ref_info in ee_ref_info_map.items():
        er_id = ref_info[0]
        referral_id = ref_info[1]
        er_rate_info = er_ref_rates_map.get(er_id)
        if not er_rate_info:
            continue

        if ee_id in sp_ref_user_row_map:
            sp_ref_rate_r = sp_ref_user_row_map[ee_id]
        else:
            sp_ref_rate_r = SpecialReferreeUserRate(
                referree_id=ee_id,
                referrer_id=er_id,
                referral_id=referral_id,
                trigger_time=now_,
            )
        referrer_type = ref_type_map[er_rate_info[2]]
        sp_ref_rate_r.referrer_type = referrer_type
        if referrer_type == SpecialReferreeUserRate.ReferrerType.BUS_AMB:
            threshold_rate = SpecialReferreeUserRate.BUS_AMB_THRESHOLD_RATE
        else:
            threshold_rate = SpecialReferreeUserRate.NORMAL_THRESHOLD_RATE
        er_ref_rate = max(er_rate_info[0], er_rate_info[1])
        sp_ref_rate_r.origin_referrer_rate = er_ref_rate
        if er_ref_rate > threshold_rate:
            if not sp_ref_rate_r.rate_manual_edit_at:
                sp_ref_rate_r.rate = threshold_rate
            if sp_ref_rate_r.rate > er_ref_rate:
                # 当新返佣比例＞邀请人返佣比例时，新返佣比例无效
                sp_ref_rate_r.status = SpecialReferreeUserRate.Status.INVALID
            else:
                sp_ref_rate_r.status = SpecialReferreeUserRate.Status.VALID
        else:
            sp_ref_rate_r.status = SpecialReferreeUserRate.Status.INVALID
            sp_ref_rate_r.rate = er_ref_rate
        sp_ref_rate_r.remark = "api trade referral user"
        db.session.add(sp_ref_rate_r)
    db.session.commit()


@scheduled(crontab(hour="*/1", minute='0'))
@lock_call()
def update_referral_reward_schedule():
    from app.caches.user import NormalReferralRewardsCache

    _today = today()
    _yesterday = _today - timedelta(days=1)
    model = ReferralAssetHistory
    last_row = model.query.with_entities(
        model.date
    ).filter(
        model.type == model.Type.REFERRAL,
    ).order_by(
        model.date.desc()
    ).first()
    last_date = last_row.date if last_row else _yesterday
    rows = model.query.group_by(
        model.asset,
        model.user_id
    ).with_entities(
        model.user_id,
        model.asset,
        func.sum(model.amount).label('total_amount'),
    ).filter(
        model.type == model.Type.REFERRAL,
        model.date == last_date
    ).order_by(
        func.sum(model.amount).desc()
    ).limit(200).all()
    user_ids = {row.user_id for row in rows}
    user_mapping = {x.id: x for x in User.query.filter(
        User.id.in_(user_ids)
    ).all()}
    ret = []
    for row in rows:
        user = user_mapping.get(row.user_id)
        account = user.hidden_complete_name if user else ''
        ret.append({'account': account, 'asset': row.asset, 'amount': amount_to_str(row.total_amount)})
    NormalReferralRewardsCache().save(json.dumps(ret))

