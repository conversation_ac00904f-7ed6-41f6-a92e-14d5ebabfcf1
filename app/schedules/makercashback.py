# -*- coding: utf-8 -*-

from datetime import timedelta

from celery.schedules import crontab

from app.common import CeleryQueues
from app.utils.date_ import now
from ..business import lock_call
from ..utils import scheduled, route_module_to_celery_queue
from ..business.makercashback import (
    RewardsSender,
)

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


@scheduled(crontab(minute=30, hour="0-3"))
@lock_call()
def send_maker_cashback_user_reward():
    """ 现货maker返佣用户-返佣每日结算 """
    today_ = now().date()
    yesterday = today_ + timedelta(days=-1)
    RewardsSender.send(yesterday)
