import json
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal

from celery.schedules import crontab
from flask import current_app

from app import config
from app.business import lock_call, PerpetualServerClient, CacheLock, LockKeys, send_alert_notice
from app.business.account import AccountTransferLogHelper
from app.business.copy_trading.follower import CopyFollowerManager, retry_or_stop_zero_fund_follow_his
from app.business.copy_trading.message import TraderMessageSender
from app.business.copy_trading.order import AddPositionOperation, \
    ReducePositionOperation, ClosePositionOperation
from app.business.copy_trading.position import market_close_user_all_position, \
    is_position_record_completed
from app.business.copy_trading.statistics import CopyStatistician
from app.business.copy_trading.trader import (
    CopyTraderManager,
    update_trader_open_position_count_cache,
    execute_trader_market_operation_task,
    InactiveTraderHelper,
)
from app.business.copy_trading.transfer import CopyTransferHelper
from app.business.utils import query_records_by_time_range
from app.caches.copy_trading import CopyTradingMarketsCache
from app.common import CeleryQueues, PrecisionEnum, PositionEventType
from app.exceptions import InvalidArgument, PerpetualResponseCode
from app.models import db
from app.models.copy_trading import (
    CopyTraderPositionChangeRecord,
    CopyFollowerOrderOperation,
    CopyFollowerHistory,
    CopyTraderProfitShareDetail,
    CopyTraderUser,
    CopyTransferHistory,
    CopyTraderHistory,
    CopyTradingRunUserStatus,
    CopyTraderMarketOperation,
)
from app.business.coupon.trace import CopyTradingExperienceFeeCouponTrace
from app.utils import route_module_to_celery_queue, scheduled, today_datetime, \
    celery_task, batch_iter, quantize_amount
from app.utils.date_ import today, now


route_module_to_celery_queue(__name__, CeleryQueues.COPY_TRADING)


@scheduled(crontab(minute="*/10"))
@lock_call()
def update_copy_trading_market_cache_schedule():
    """ 更新带单市场缓存 """
    CopyTradingMarketsCache.reload()


@celery_task
@lock_call(with_args=True)
def update_one_trader_statics(trader_sub_id: int, force_update: bool = False):
    """ 更新单个带单人的统计数据 """
    trader_his: CopyTraderHistory = CopyTraderHistory.query.filter(
        CopyTraderHistory.sub_user_id == trader_sub_id,
    ).order_by(CopyTraderHistory.id.desc()).first()
    if not trader_his:
        return
    if trader_his.status != CopyTraderHistory.Status.RUNNING and not force_update:
        return
    follower_list = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.copy_trader_sub_user_id == trader_sub_id,
        CopyFollowerHistory.status != CopyFollowerHistory.Status.FINISHED,
    ).all()
    st_mg = CopyStatistician(today())
    st_mg.update_daily_trader_statistics([trader_his], follower_list)
    st_mg.update_trader_summary([trader_his.user_id])


@celery_task
@lock_call(with_args=True)
def update_one_follower_statics(follower_sub_id: int, force_update: bool = False):
    """ 更新单个跟单人的统计数据 """
    follow_his: CopyFollowerHistory = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.sub_user_id == follower_sub_id,
    ).order_by(CopyFollowerHistory.id.desc()).first()
    if not follow_his:
        return
    if follow_his.status != CopyFollowerHistory.Status.FOLLOWING and not force_update:
        return
    st_mg = CopyStatistician(today())
    st_mg.update_daily_follower_statistics([follow_his])
    st_mg.update_one_follower_summary(follow_his)


@scheduled(crontab(minute="*/10"))
@lock_call()
def update_copy_daily_statics_schedule():
    """ 每日统计，当天更新 """
    today_ = today()
    st_mg = CopyStatistician(today_)
    st_mg.update_daily_statistics()
    st_mg.update_summary()


@scheduled(crontab(hour="0", minute="1"))
@lock_call()
def update_copy_statics_summary_schedule():
    """ 累计统计，次日更新 """
    yes_day = today() - timedelta(days=1)
    st_mg = CopyStatistician(yes_day)
    st_mg.update_daily_statistics()  # 次日0点再更新一次前一天的数据
    st_mg.update_summary()


@scheduled(crontab(hour="0", minute="10,20"))
@lock_call()
def generate_trader_daily_market_statics_schedule():
    """ 带单人交易市场维度的每日统计，次日更新 """
    yes_day = today() - timedelta(days=1)
    st_mg = CopyStatistician(yes_day)
    st_mg.gen_daily_trader_market_statistics()


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_trader_open_position_count_cache_schedule():
    """ 更新带单人每日开仓数缓存 """
    update_trader_open_position_count_cache()


@scheduled(crontab(hour="0", minute="30"))
@lock_call()
def find_and_mark_inactive_trader_schedule():
    """ 标记不活跃带单人 """
    InactiveTraderHelper.find_and_mark_inactive_traders()


@scheduled(crontab(hour="0", minute="40"))
@lock_call()
def stop_inactive_trader_schedule():
    """ 结束不活跃带单人的带单记录 """
    InactiveTraderHelper.system_stop_inactive_traders()
    InactiveTraderHelper.notice_pre_stop_inactive_traders()


@scheduled(crontab(minute="*/20"))
@lock_call()
def update_follower_expected_profit_share_amount_schedule():
    """ 更新跟单人预计分润数目 """
    fol_his_rows: list[CopyFollowerHistory] = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.status == CopyFollowerHistory.Status.FOLLOWING,
    ).all()
    for fol_his in fol_his_rows:
        try:
            end_time = fol_his.next_profit_shared_at
            start_time = fol_his.last_profit_shared_at
            _res = CopyFollowerManager.get_follower_profit_data(fol_his, start_time, end_time)
            fol_his.expected_profit_share_amount = _res[1]
            db.session.commit()
        except Exception as e:
            current_app.logger.exception(
                f"update_follower_expected_profit_share_amount {fol_his.user_id} {fol_his.sub_user_id} "
                f"error {e!r}")


@scheduled(crontab(minute='*/10'))
@lock_call()
def retry_or_stop_zero_fund_follow_his_schedule():
    """ 重试或结束`跟单资金为0的记录` """
    fol_rows: list[CopyFollowerHistory] = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.status == CopyFollowerHistory.Status.FOLLOWING,
        CopyFollowerHistory.fund_amount == 0,
    ).with_entities(
        CopyFollowerHistory.id,
    ).all()
    for r in fol_rows:
        retry_or_stop_zero_fund_follow_his.delay(r.id)


@scheduled(crontab(minute='*/10'))
@lock_call()
def retry_copy_transfer_schedule():
    """ 重试失败的划转记录 """
    _now = now()
    end_time = _now - timedelta(minutes=3)
    start_time = _now - timedelta(hours=24)

    pending_statues = [CopyTransferHistory.Status.CREATED, CopyTransferHistory.Status.DEDUCTED]
    for status in pending_statues:
        pending_rows: list[CopyTransferHistory] = query_records_by_time_range(
            table=CopyTransferHistory,
            start_time=start_time,
            end_time=end_time,
            filters=dict(status=status),
            limit=1000,
        )
        for r in pending_rows[::-1]:
            if r.type in [
                CopyTransferHistory.Type.TRADER_TRANSFER_IN,
                CopyTransferHistory.Type.TRADER_TRANSFER_OUT,
            ]:
                k = LockKeys.copy_trader_transfer(r.main_user_id)
            elif r.type in [
                CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN,
            ]:
                k = LockKeys.copy_follower_sub(r.to_user_id)
            else:
                continue
            try:
                with CacheLock(k):
                    db.session.rollback()
                    if r.type == CopyTransferHistory.Type.FOLLOWER_TRANSFER_IN:
                        CopyFollowerManager.retry_add_margin_amount(r)
                    elif r.type in [
                        CopyTransferHistory.Type.TRADER_TRANSFER_IN,
                        CopyTransferHistory.Type.TRADER_TRANSFER_OUT,
                    ]:
                        trade_his: CopyTraderHistory = CopyTraderHistory.query.get(r.history_id)
                        if trade_his.status != CopyTraderHistory.Status.RUNNING:
                            continue
                        CopyTransferHelper.transfer_by_history(r, on_finished_commit=True)
            except Exception as e:
                db.session.rollback()
                current_app.logger.exception(f"retry_copy_transfer_schedule {r.id} error {e!r}")


@scheduled(crontab(minute="*/5"))
@lock_call()
def execute_trader_market_operation_schedule():
    """ 带单人的一些操作，同步给跟单人 """
    op_rows = CopyTraderMarketOperation.query.filter(
        CopyTraderMarketOperation.status == CopyTraderMarketOperation.Status.CREATED,
    ).with_entities(CopyTraderMarketOperation.id).all()
    for op_row in op_rows:
        execute_trader_market_operation_task.delay(op_row.id)


@scheduled(5)
@lock_call()
def generate_copy_follower_op_schedule():
    """ 定时处理交易员操作信息 """
    trader_op_list = CopyTraderPositionChangeRecord.query.filter(
        CopyTraderPositionChangeRecord.status == CopyTraderPositionChangeRecord.Status.CREATED
    ).order_by(
        CopyTraderPositionChangeRecord.id.asc()
    ).all()
    trader_user_ids = {i.user_id for i in trader_op_list}
    follower_list = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.copy_trader_user_id.in_(trader_user_ids),
        CopyFollowerHistory.status == CopyFollowerHistory.Status.FOLLOWING,
    ).with_entities(
        CopyFollowerHistory.id,
        CopyFollowerHistory.sub_user_id,
        CopyFollowerHistory.copy_trader_sub_user_id,
    ).all()
    trader_follower_user_map = defaultdict(list)
    for follower in follower_list:
        trader_follower_user_map[follower.copy_trader_sub_user_id].append(dict(
            sub_user_id=follower.sub_user_id,
            id=follower.id,
        ))
    sub_user_map = {
        i.user_id: i.main_user_id
        for i in CopyTradingRunUserStatus.query.with_entities(
            CopyTradingRunUserStatus.user_id,
            CopyTradingRunUserStatus.main_user_id
        ).all()}
    record_ids = []

    change_delta_data_map = dict()
    trade_position_id_map = dict()
    for row in trader_op_list:
        if row.position_id not in trade_position_id_map:
            trade_position_id_map[row.position_id] = row
            change_delta_data = row.change_delta_data
            change_delta_data_map[row.id] = dict(
                amount_delta=change_delta_data['amount_delta'],
                settle_val_delta=change_delta_data['settle_val_delta'],
            )
        else:  # 根据顺序计算仓位变化代替使用change_delta_data查询
            amount_delta = row.amount - trade_position_id_map[row.position_id].amount
            settle_val_delta = row.settle_val - trade_position_id_map[row.position_id].settle_val
            change_delta_data_map[row.id] = dict(
                amount_delta=amount_delta,
                settle_val_delta=settle_val_delta,
            )
            trade_position_id_map[row.position_id] = row

    for trader_op_chunk in batch_iter(trader_op_list, 200):
        object_count = 0
        for copy_op_row in trader_op_chunk:
            change_delta_data = change_delta_data_map[copy_op_row.id]
            if not change_delta_data['amount_delta'] and copy_op_row.event != PositionEventType.CLOSE_ALL:
                copy_op_row.status = CopyTraderPositionChangeRecord.Status.FINISHED
                continue
            copy_op_row.status = CopyTraderPositionChangeRecord.Status.PROCESSED
            copy_op_row.amount_delta = change_delta_data['amount_delta']
            copy_op_row.settle_val_delta = change_delta_data['settle_val_delta']
            follower_his_list = trader_follower_user_map[copy_op_row.sub_user_id]
            objects = [CopyFollowerOrderOperation(
                change_record_id=copy_op_row.id,
                user_id=sub_user_map[follower_his['sub_user_id']],
                sub_user_id=follower_his['sub_user_id'],
                follow_history_id=follower_his['id'],
                copy_trader_user_id=copy_op_row.user_id,
                operation_type=copy_op_row.operation_type.name,
                market=copy_op_row.market,
                amount=Decimal(0),
                deal_stock=Decimal(0),
            ) for follower_his in follower_his_list]

            db.session.bulk_save_objects(objects)
            object_count += len(objects)
            record_ids.append(copy_op_row.id)
            if object_count >= 500:
                db.session.commit()
                object_count = 0
        db.session.commit()
    if record_ids:
        process_copy_follower_op_schedule.delay()
        send_copy_trader_op_notice.delay(record_ids)


@scheduled(5)
@lock_call()
def process_copy_follower_op_schedule():
    op_list = CopyTraderPositionChangeRecord.query.filter(
        CopyTraderPositionChangeRecord.status == CopyTraderPositionChangeRecord.Status.PROCESSED
    ).order_by(
        CopyTraderPositionChangeRecord.id.asc()
    ).all()
    follower_op_list = CopyFollowerOrderOperation.query.filter(
        CopyFollowerOrderOperation.change_record_id.in_(
            [i.id for i in op_list]),
        CopyFollowerOrderOperation.status == CopyFollowerOrderOperation.Status.CREATED,
    ).order_by(
        CopyFollowerOrderOperation.id.asc()
    ).all()
    for follower_op in follower_op_list:
        process_follower_order.delay(follower_op.follow_history_id)
    # 实际完成 = 理论未完成 - 实际未完成
    finished_op_list = {i.id for i in op_list} - {i.change_record_id for i in
                                                  follower_op_list}
    if finished_op_list:
        CopyTraderPositionChangeRecord.query.filter(
            CopyTraderPositionChangeRecord.id.in_(finished_op_list)
        ).update(
            {'status': CopyTraderPositionChangeRecord.Status.FINISHED},
            synchronize_session=False
        )
        db.session.commit()


@scheduled(30)
@lock_call()
def retry_follower_stop_schedule():
    follower_list = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.status == CopyFollowerHistory.Status.ENDING
    ).with_entities(
        CopyFollowerHistory.id,
    ).all()
    for row in follower_list:
        finish_copy_trading_follower_status.delay(row.id)


@scheduled(30)
@lock_call()
def retry_trader_stop_schedule():
    trader_list = CopyTraderHistory.query.filter(
        CopyTraderHistory.status == CopyTraderHistory.Status.ENDING
    ).with_entities(
        CopyTraderHistory.sub_user_id,
    ).all()
    for row in trader_list:
        finish_copy_trading_trader_status.delay(row.sub_user_id)


@celery_task
@lock_call(with_args=True)
def process_follower_order(follow_history_id):
    op_query = CopyFollowerOrderOperation.query.filter(
        CopyFollowerOrderOperation.follow_history_id == follow_history_id,
        CopyFollowerOrderOperation.status == CopyFollowerOrderOperation.Status.CREATED,
    )
    op_row = op_query.order_by(
        CopyFollowerOrderOperation.id.asc()
    ).with_entities(
        CopyFollowerOrderOperation.id,
        CopyFollowerOrderOperation.sub_user_id,
        CopyFollowerOrderOperation.operation_type,
    ).first()
    operation_class_map = {
        CopyTraderPositionChangeRecord.OperationType.REDUCE_POSITION.name: ReducePositionOperation,
        CopyTraderPositionChangeRecord.OperationType.CLOSE_POSITION.name: ClosePositionOperation,
        CopyTraderPositionChangeRecord.OperationType.ADD_POSITION.name: AddPositionOperation,
    }
    if not op_row:
        return
    op_class = operation_class_map[op_row.operation_type]
    op_class(op_row.id).process()
    has_next_op = op_query.with_entities(
        CopyFollowerOrderOperation.id,
    ).first()
    if has_next_op:
        process_follower_order.delay(follow_history_id)


@scheduled(crontab(minute=50, hour='*/1'))
@lock_call()
def generate_profit_shared_schedule():
    today_ = today_datetime()
    end_time = today_-timedelta(days=CopyFollowerHistory.PERIODIC_SETTLEMENT_DAYS)
    follower_his_query = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.status == CopyFollowerHistory.Status.FOLLOWING,
        CopyFollowerHistory.last_profit_shared_at <= end_time,
    ).all()
    exist_profit_list = CopyTraderProfitShareDetail.query.filter(
        CopyTraderProfitShareDetail.date == today_,
        CopyTraderProfitShareDetail.profit_share_type == CopyTraderProfitShareDetail.ProfitShareType.PERIODIC_SETTLEMENT,
    ).with_entities(
        CopyTraderProfitShareDetail.follow_history_id
    ).all()
    exist_profit_history_ids = {i.follow_history_id for i in exist_profit_list}
    for follower_his_list in batch_iter(follower_his_query, 200):
        objects = []
        for follower in follower_his_list:
            if follower.id in exist_profit_history_ids:
                continue
            share_row = CopyTraderProfitShareDetail(
                date=today_,
                trader_history_id=follower.trader_history_id,
                follow_history_id=follower.id,
                copy_trader_user_id=follower.copy_trader_user_id,
                copy_trader_sub_user_id=follower.copy_trader_sub_user_id,
                user_id=follower.user_id,
                sub_user_id=follower.sub_user_id,
                asset=CopyTraderProfitShareDetail.PROFIT_ASSET,
                profit_share_type=CopyTraderProfitShareDetail.ProfitShareType.PERIODIC_SETTLEMENT,
            )
            objects.append(share_row)
        db.session.bulk_save_objects(objects)
        db.session.commit()
    profit_share_list = CopyTraderProfitShareDetail.query.filter(
        CopyTraderProfitShareDetail.status == CopyTraderProfitShareDetail.Status.CREATED
    ).with_entities(
        CopyTraderProfitShareDetail.follow_history_id
    ).all()
    follow_history_ids = {i.follow_history_id for i in profit_share_list}
    follower_query = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.id.in_(follow_history_ids)
    ).all()
    follower_map = {i.id: i for i in follower_query}
    for row in profit_share_list:
        follower = follower_map[row.follow_history_id]
        with CacheLock(LockKeys.copy_follower_sub(follower.sub_user_id)):
            try:
                db.session.rollback()
                CopyFollowerManager.process_settlement_profit(follower)
            except Exception as _e:
                current_app.logger.warning(f"schedule_process_settlement_profit {follower.id}-{follower.sub_user_id} error: {_e}")
    if profit_share_list:
        CopyStatistician.update_summary_profit_share_amount()


@scheduled(5)
@lock_call()
def stop_loss_take_profit_schedule():
    client = PerpetualServerClient()
    asset = CopyTraderProfitShareDetail.PROFIT_ASSET
    follower_list = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.status == CopyFollowerHistory.Status.FOLLOWING,
    ).with_entities(
        CopyFollowerHistory.id,
        CopyFollowerHistory.user_id,
        CopyFollowerHistory.sub_user_id,
        CopyFollowerHistory.profit_trigger_rate,
        CopyFollowerHistory.loss_trigger_rate,
    ).all()
    follower_list = [i for i in follower_list if (i.profit_trigger_rate or i.loss_trigger_rate)]
    cost_map = {}
    balance_map = {}
    for follower_chunk in batch_iter(follower_list, 50):
        cost_chunk_map = CopyStatistician.get_follow_his_tran_in_amount_data({i.id for i in follower_chunk})
        try:
            balance_map_chunk = client.get_users_balances(asset, [i.sub_user_id for i in follower_chunk])
        except Exception as e:
            if e.code == PerpetualResponseCode.CONTRACT_FUNDING_ERROR:
                # 资金费率计算期间无法查询
                return
            raise e

        cost_map.update(cost_chunk_map)
        balance_map.update(balance_map_chunk)
    for row in follower_list:
        user_balance = balance_map[row.sub_user_id][asset]
        total_user_balance = user_balance['available'] + \
                                    user_balance['margin'] + \
                                    user_balance['profit_unreal']
        total_cost_amount = cost_map.get(row.id, 0)
        rate = (total_user_balance-total_cost_amount)/total_cost_amount if total_cost_amount else 0
        if row.profit_trigger_rate and rate > row.profit_trigger_rate:
            finish_copy_trading_follower_status.delay(row.id, CopyTraderProfitShareDetail.ProfitShareType.TAKE_PROFIT_TRIGGER.name)
        if row.loss_trigger_rate and abs(rate) > row.loss_trigger_rate and rate < 0:
            finish_copy_trading_follower_status.delay(row.id, CopyTraderProfitShareDetail.ProfitShareType.STOP_LOSS_TRIGGER.name)


@celery_task
@lock_call(with_args='follow_history_id')
def finish_copy_trading_follower_status(follow_history_id, profit_share_type=None):

    follower_his: CopyFollowerHistory = CopyFollowerHistory.query.get(follow_history_id)
    if follower_his.status == CopyFollowerHistory.Status.FINISHED:
        return

    sub_user_id = follower_his.sub_user_id
    with CacheLock(LockKeys.copy_follower_sub(sub_user_id)):
        db.session.rollback()
        if follower_his.status == CopyFollowerHistory.Status.FINISHED:
            return
        if follower_his.status == CopyFollowerHistory.Status.FOLLOWING:
            follower_his.status = CopyFollowerHistory.Status.ENDING
            profit_share_type = CopyTraderProfitShareDetail.ProfitShareType[profit_share_type]
            share_row = CopyTraderProfitShareDetail(
                date=today(),
                trader_history_id=follower_his.trader_history_id,
                follow_history_id=follower_his.id,
                user_id=follower_his.user_id,
                sub_user_id=follower_his.sub_user_id,
                copy_trader_user_id=follower_his.copy_trader_user_id,
                copy_trader_sub_user_id=follower_his.copy_trader_sub_user_id,
                asset=CopyTraderProfitShareDetail.PROFIT_ASSET,
                profit_share_type=profit_share_type,
            )
            db.session.add(share_row)
            db.session.commit()
        # 待平仓
        if follower_his.ending_status == CopyFollowerHistory.EndingStatusType.PENDING_CLOSE_POSITION:
            if not market_close_user_all_position(follower_his.sub_user_id):
                return
            if not is_position_record_completed(follower_his.sub_user_id,
                                                follower_his.copy_trader_user_id,
                                                follower_his.created_at):
                # 平仓的仓位记录可能还没写入server的position_history,等待重试
                raise InvalidArgument(message=f'position_record not completed,'
                                              f'sub_user_id:{follower_his.sub_user_id}')

            follower_his.ending_status = CopyFollowerHistory.EndingStatusType.PENDING_PROFIT
            db.session.commit()

        if follower_his.ending_status == CopyFollowerHistory.EndingStatusType.PENDING_PROFIT:
            CopyFollowerManager.process_follower_profit(follower_his)
            follower_his.ending_status = CopyFollowerHistory.EndingStatusType.PENDING_TRANSFER_OUT
            db.session.commit()
        # 待转出
        retry_transfer_by_history(follower_his.user_id, follower_his.id)
        CopyTradingExperienceFeeCouponTrace.early_finish_follower_coupon(
            follower_his.user_id, follower_his.sub_user_id, follower_his.id
        )
        client = PerpetualServerClient()
        asset = CopyTraderProfitShareDetail.PROFIT_ASSET
        user_balance = client.get_user_balances(follower_his.sub_user_id)[asset]
        available_amount = quantize_amount(user_balance['transfer'], PrecisionEnum.COIN_PLACES)
        if available_amount > Decimal(0):
            CopyFollowerManager.transfer_out_margin_amount(follower_his, available_amount)
        with CacheLock(LockKeys.copy_trader(follower_his.copy_trader_user_id), wait=3):
            db.session.rollback()
            # 会修改交易员的数据，加上交易员的锁
            CopyFollowerManager.set_follow_finished(follower_his)

    st_mg = CopyStatistician(today())
    st_mg.update_daily_follower_statistics([follower_his])
    st_mg.update_follower_summary()


@celery_task
@lock_call(with_args=True)
def finish_copy_trading_trader_status(sub_user_id):
    trader_his: CopyTraderHistory = CopyTraderHistory.query.filter(
        CopyTraderHistory.sub_user_id == sub_user_id,
    ).order_by(CopyTraderHistory.id.desc()).first()
    if not trader_his:
        return
    with CacheLock(LockKeys.copy_trader(trader_his.user_id)):
        db.session.rollback()
        if trader_his.status == CopyTraderHistory.Status.FINISHED:
            return
        if not market_close_user_all_position(trader_his.sub_user_id):
            return
        follower_list = CopyFollowerHistory.query.filter(
            CopyFollowerHistory.copy_trader_sub_user_id == trader_his.sub_user_id,
            CopyFollowerHistory.status != CopyFollowerHistory.Status.FINISHED,
        ).all()
        if follower_list:
            for follower in follower_list:
                finish_copy_trading_follower_status.delay(
                    follower.id,
                    CopyTraderProfitShareDetail.ProfitShareType.END_BY_TRADER.name
                )
            return
        retry_transfer_by_history(trader_his.user_id, trader_his.id)
        CopyTradingExperienceFeeCouponTrace.early_finish_trader_coupon(
            trader_his.user_id, trader_his.sub_user_id, trader_his.id
        )
        from_user_id = trader_his.sub_user_id
        asset = CopyTraderProfitShareDetail.PROFIT_ASSET
        transfer_type = CopyTransferHistory.Type.TRADER_TRANSFER_OUT
        balances = PerpetualServerClient().get_user_balances(from_user_id, asset)
        transfer_amount = Decimal(balances.get(asset, {}).get("transfer", "0"))
        transfer_amount = quantize_amount(transfer_amount, PrecisionEnum.COIN_PLACES)
        trade_his = CopyTraderManager.get_last_active_trade_his(trader_his.user_id)
        if transfer_amount > Decimal(0):
            tran_row = CopyTransferHelper.transfer(
                main_user_id=trader_his.user_id,
                sub_id=trader_his.sub_user_id,
                history_id=trade_his.id,
                transfer_type=transfer_type,
                asset=asset,
                amount=transfer_amount,
            )
            AccountTransferLogHelper.add_log_by_transfer(tran_row)
        trader_user = CopyTraderManager.get_trader(trader_his.user_id)
        CopyTraderManager.set_trade_finished(trader_user)

    st_mg = CopyStatistician(today())
    st_mg.update_daily_follower_statistics(follower_list)
    st_mg.update_follower_summary()
    st_mg.update_daily_trader_statistics([trader_his], follower_list)
    st_mg.update_trader_summary([trader_his.user_id])


@celery_task
def send_copy_trader_op_notice(record_ids):
    trader_op_list = CopyTraderPositionChangeRecord.query.filter(
        CopyTraderPositionChangeRecord.id.in_(record_ids)
    ).all()

    send_list = []
    for copy_op_row in trader_op_list:
        change_delta_data = copy_op_row.change_delta_data
        position = json.loads(copy_op_row.position_detail)['position']
        position['avg_price'] = copy_op_row.avg_price
        if change_delta_data['is_first']:
            send_list.append(dict(user_id=copy_op_row.user_id,
                                  position=position,
                                  type='open_position',
                                  ))
        elif copy_op_row.operation_type == CopyTraderPositionChangeRecord.OperationType.CLOSE_POSITION:
            send_data = dict(user_id=copy_op_row.user_id,
                             position=position)
            if position["sys"] == PositionEventType.STOP_LOSS:
                send_data['type'] = 'stop_loss'
            elif position["sys"] == PositionEventType.TAKE_PROFIT:
                send_data['type'] = 'take_profit'
            elif copy_op_row.event == PositionEventType.CLOSE_ALL:
                send_data['type'] = 'liquidation'
            else:
                send_data['type'] = 'close_position'
            send_list.append(send_data)

    func_map = {
        'stop_loss': TraderMessageSender.send_stop_loss,
        'take_profit': TraderMessageSender.send_take_profit,
        'liquidation': TraderMessageSender.send_liquidation,
        # 'close_position': TraderMessageSender.send_close_position,  # 按产品要去暂时去除
        # 'open_position': TraderMessageSender.send_open_position,
    }
    trader_user_ids = {i['user_id'] for i in send_list}
    trader_user_query = CopyTraderUser.query.filter(
        CopyTraderUser.user_id.in_(trader_user_ids)
    ).all()
    trader_map = {i.user_id: i for i in trader_user_query}
    for send_row in send_list:
        _func = func_map.get(send_row['type'])
        if not _func:
            continue
        trader = trader_map[send_row['user_id']]
        _func(trader, send_row['position'])


@scheduled(crontab(minute="*/20"))
@lock_call()
def alert_expired_copy_trading_operation():
    """ 对过期未处理（如有）的跟单操作告警 """
    _now = now()
    start_time = _now - timedelta(hours=2)
    end_time = _now - timedelta(minutes=5)
    follower_op_list = query_records_by_time_range(
        table=CopyFollowerOrderOperation,
        start_time=start_time,
        end_time=end_time,
        filters=dict(status=CopyFollowerOrderOperation.Status.CREATED),
        filter_in_query=False,
    )
    if follower_op_list:
        res = [i.id for i in follower_op_list]
        msg = f'存在过期未处理的跟单交易操作,ids:{res}，请及时处理'
        send_alert_notice(msg, config["ADMIN_CONTACTS"].get('copy_trading', ''))


def retry_transfer_by_history(main_user_id: int, history_id: int):
    """ 按跟单｜带单记录ID重试失败的划转记录 """
    _now = now()
    start_time = _now - timedelta(hours=24)

    pending_rows = CopyTransferHistory.query.filter(
        CopyTransferHistory.main_user_id == main_user_id,
        CopyTransferHistory.history_id == history_id,
        CopyTransferHistory.created_at >= start_time,
        CopyTransferHistory.status.in_([CopyTransferHistory.Status.CREATED, CopyTransferHistory.Status.DEDUCTED]),
    ).order_by(CopyTransferHistory.id.asc()).all()
    for r in pending_rows:
        CopyTransferHelper.transfer_by_history(r, on_finished_commit=True)
