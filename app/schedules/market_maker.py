# -*- coding: utf-8 -*-
import calendar
import json
from collections import defaultdict, Counter
from datetime import date, timedelta
from decimal import Decimal
from functools import cached_property
from itertools import chain
from typing import DefaultDict, Dict, Iterable, List, Tuple
import math
from flask import current_app

from celery.schedules import crontab
from dateutil.relativedelta import relativedelta
from app.business import (
    route_module_to_celery_queue,
    CeleryQueues, lock_call,
)
from ..caches.report import DailyIncomeProcessResultCache
from ..config import config
from app.business.alert import send_alert_notice
from app.business.appraisal import AppraisalHelper
from app.models import (
    MarketMaker, SubAccount, UserTradeSummary,
    AppraisalHistory, UserStatusChangeHistory, User, IncomeType,
)
from app.models import db
from app.utils.date_ import now, last_month
from ..business.market_maker import SPOT_MARKET_MAKER_APPRAISAL_MAP, \
    MIN_SPOT_MARKET_MAKER_RANKING_AMOUNT, \
    PERPETUAL_MARKET_MAKER_APPRAISAL_MAP, MARKET_MAKER_HIGH_LEVEL_MAP, \
    MIN_PERPETUAL_MARKET_MAKER_RANKING_AMOUNT

from ..business.email import send_appraisal_email
from ..business.market_maker import MarketMakerHelper
from ..business.fee_constant import (MARKET_MAKER_DICT,
                                    PERPETUAL_MARKET_MAKER_DICT)
from ..business.fee import update_user_fee_task
from ..business.user_status import MarketMakerChangeType
from ..utils import scheduled

route_module_to_celery_queue(__name__, CeleryQueues.MARKET_MAKER)


class Checker(object):

    def __init__(self, maker_type: MarketMaker.MakerType):
        self.maker_type = maker_type
        self.helper = MarketMakerHelper(self.maker_type)

    def get_trade_level_list(self):
        if self.maker_type == MarketMaker.MakerType.SPOT:
            fee_dict = MARKET_MAKER_DICT
        else:
            fee_dict = PERPETUAL_MARKET_MAKER_DICT
        level_list = sorted(
            [
                dict(
                    value,
                    level=level,
                )
                for level, value in fee_dict.items()
            ],
            key=lambda x: x["level"]
        )
        level_list.sort(key=lambda x: x['level'], reverse=True)
        return level_list

    @cached_property
    def all_appraisal_mmer_user_ids(self):
        mmer = MarketMaker.query.filter(
            MarketMaker.status == MarketMaker.StatusType.PASS,
            MarketMaker.maker_type == self.maker_type,
            MarketMaker.appraisal_type == MarketMaker.AppraisalType.APPRAISAL
        )
        user_ids = [item.user_id for item in mmer]
        return user_ids

    @cached_property
    def all_mmer_user_ids(self):
        mmer = MarketMaker.query.filter(
            MarketMaker.status == MarketMaker.StatusType.PASS,
            MarketMaker.maker_type == self.maker_type
        )
        user_ids = [item.user_id for item in mmer]
        return user_ids

    @classmethod
    def get_sub_user_map(cls, user_ids: Iterable) -> DefaultDict[int, List]:
        sub_query = SubAccount.query.filter(
            SubAccount.main_user_id.in_(user_ids),
        ).with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id,
        )
        data = defaultdict(list)
        for r in sub_query:
            data[r.main_user_id].append(r.user_id)
        return data

    def get_market_maker_level_map(self,
                                   trade_amount_map: Dict[int, Decimal]) -> Dict:
        def _topn(array: List[Tuple[int, Decimal]], n: int):
            c = Counter([v[1] for v in array])
            sorted_ = sorted(
                [(k, _count) for k, _count in c.items()], key=lambda x: x[0], reverse=True)
            top_n_count = sum([v[1] for v in sorted_[:n]])
            return top_n_count
        
        if self.maker_type == MarketMaker.MakerType.SPOT:
            mm_map = SPOT_MARKET_MAKER_APPRAISAL_MAP
            min_amount = MIN_SPOT_MARKET_MAKER_RANKING_AMOUNT
        else:
            mm_map = PERPETUAL_MARKET_MAKER_APPRAISAL_MAP
            min_amount = MIN_PERPETUAL_MARKET_MAKER_RANKING_AMOUNT
        level_list = sorted([(k, v) for k, v in mm_map.items()], 
                            key=lambda x: x[0], 
                            reverse=True)
        trade_amount_ranking_list = sorted([(k, v) for k, v in trade_amount_map.items() if v >= min_amount],
                                           key=lambda x: x[1], reverse=True)
        result = dict()
        total_user_count = len(trade_amount_ranking_list)
        handled_count = 0
        for item in level_list:
            level, x = item
            ratio = x['ratio']
            count = max(0, math.ceil(total_user_count * ratio) - handled_count)
            if level in MARKET_MAKER_HIGH_LEVEL_MAP:
                real_count = max(MARKET_MAKER_HIGH_LEVEL_MAP[level], count)
            else:
                real_count = count
            if real_count == 0:
                continue
            # 处理交易量并列的情形
            real_count = _topn(trade_amount_ranking_list, real_count)
            user_list = trade_amount_ranking_list[:real_count]
            
            for i in user_list:
                user_id, _ = i
                result[user_id] = level
            trade_amount_ranking_list = trade_amount_ranking_list[real_count:]
            handled_count += real_count
        return result

    def get_market_maker_ranking_map(self, trade_amount_map: Dict[int, Decimal]) -> Dict[int, int]:
        trade_amount_ranking_list = sorted([(k, v) for k, v in trade_amount_map.items()],
                                           key=lambda x: x[1], reverse=True)
        result = dict()
        for index, item in enumerate(trade_amount_ranking_list, start=1):
            user_id, _ = item
            result[user_id] = index
        return result


    def check_market_maker_new_level(self, user_ids: Iterable, write_status_history: bool = False):
        today = now().date()
        last_month_first_day = last_month(today.year, today.month, 1)
        this_month_first_day = date(today.year, today.month, 1)
        days = calendar.monthrange(
            last_month_first_day.year, last_month_first_day.month)[1]
        last_month_end_day = date(
            last_month_first_day.year, last_month_first_day.month, days)
        buffer_check_month_date = last_month_first_day + \
            relativedelta(months=-1)
        mmer = MarketMaker.query.filter(
            MarketMaker.status == MarketMaker.StatusType.PASS,
            MarketMaker.user_id.in_(user_ids),
            MarketMaker.maker_type == self.maker_type,
            MarketMaker.appraisal_type == MarketMaker.AppraisalType.APPRAISAL
        )
        main_user_id_map = self.get_sub_user_map(user_ids)
        trade_user_id_list = list(set(list(user_ids) +
                                  list(main_user_id_map.keys()) +
                                  list(chain(*list(main_user_id_map.values())))
                                      ))
        if self.maker_type == MarketMaker.MakerType.SPOT:
            system = UserTradeSummary.System.SPOT
            business_type = AppraisalHistory.BusinessType.SPOT
        else:
            system = UserTradeSummary.System.PERPETUAL
            business_type = AppraisalHistory.BusinessType.PERPETUAL
        mmer_trade_amount_dict = AppraisalHelper.list_trade_amount(
            trade_user_id_list, last_month_first_day, last_month_end_day, system)
        lock_user_dict = self.helper.list_lock_maker()
        lock_level_expired_maker_set = self.helper.list_lock_level_expired_maker()

        type_ = User.UserType.EXTERNAL_SPOT_MAKER \
            if self.maker_type == MarketMaker.MakerType.SPOT \
            else User.UserType.EXTERNAL_CONTRACT_MAKER

        # 计算交易量
        main_user_trade_amount_map = dict()
        for maker in mmer:
            trade_amount = mmer_trade_amount_dict.get(maker.user_id, Decimal())
            # add sub account amount
            for user_id in main_user_id_map[maker.user_id]:
                trade_amount += mmer_trade_amount_dict.get(user_id, Decimal())
            main_user_trade_amount_map[maker.user_id] = trade_amount

        user_level_map = self.get_market_maker_level_map(main_user_trade_amount_map)
        user_ranking_map = self.get_market_maker_ranking_map(main_user_trade_amount_map)
        for maker in mmer:
            trade_amount = main_user_trade_amount_map.get(maker.user_id, Decimal())
            old_level = maker.level
            trade_level = user_level_map.get(maker.user_id, 0)
            level = trade_level
            maker.real_level = level
            new_level = max(level, lock_user_dict.get(maker.user_id, 0))

            month_count = AppraisalHelper.check_buffer_protection_month(
                maker.user_id,
                buffer_check_month_date,
                last_month_end_day,
                business_type
            )

            appraisal_record = AppraisalHelper.add_appraisal_history(
                maker.user_id,
                this_month_first_day,
                business_type,
                trade_amount,
                old_level,
                new_level,
                lock_user_dict.get(maker.user_id, 0),
                month_count,
                ranking=user_ranking_map.get(maker.user_id)
            )
            month_count = json.loads(appraisal_record.result)['month_count']
            if month_count >= 3:
                self.helper.delete(maker.user_id)

                db.session.add(UserStatusChangeHistory(
                    user_id=maker.user_id,
                    type=type_.name,
                    action=MarketMakerChangeType.REMOVE.name,
                    detail=json.dumps(dict(
                        maker_type=type_.name,
                        old_level=old_level,
                        new_level=None,
                        expired_time=None
                    ))
                ))
                # noinspection PyBroadException
                try:
                    update_user_fee_task(maker.user_id)
                except Exception:
                    send_alert_notice(f'做市商费率更新失败，用户id: {maker.user_id}', 
                                      config["ADMIN_CONTACTS"]["web_notice"])
                    current_app.logger.error(f'update market maker fee error user_id:{maker.user_id}')
            else:
                maker.level = max(new_level, 1)
                db.session.commit()
                if old_level != maker.level:
                    # noinspection PyBroadException
                    try:
                        update_user_fee_task(maker.user_id)
                    except Exception:
                        send_alert_notice(f'做市商费率更新失败，用户id: {maker.user_id}', 
                                          config["ADMIN_CONTACTS"]["web_notice"])
                        current_app.logger.error(f'update market maker fee error user_id:{maker.user_id}')
            if write_status_history:
                if maker.user_id in lock_level_expired_maker_set:
                    maker.is_lock = False
                    db.session.add(UserStatusChangeHistory(
                        user_id=maker.user_id,
                        type=type_.name,
                        action=MarketMakerChangeType.LOCK_LEVEL_EXPIRED.name,
                        detail=json.dumps(dict(
                            maker_type=type_.name,
                            old_level=None,
                            new_level=maker.lock_level,
                            expired_time=None
                        ))
                    ))
                if old_level != maker.level:
                    db.session.add(UserStatusChangeHistory(
                        user_id=maker.user_id,
                        type=type_.name,
                        action=MarketMakerChangeType.LEVEL_CHANGE.name,
                        detail=json.dumps(dict(
                            maker_type=type_.name,
                            old_level=old_level,
                            new_level=maker.level,
                            expired_time=None
                        ))
                    ))

            send_appraisal_email.delay(maker.user_id, appraisal_record.id)
            db.session.commit()


@scheduled(crontab(minute=20, hour='1-3', day_of_month=1))
@lock_call()
def update_market_maker_level_schedule():
    today = now().date()
    this_month_first_day = date(today.year, today.month, 1)
    yesterday = today - timedelta(days=1)
    # 做市商考核改为返现之后
    is_finish = DailyIncomeProcessResultCache(yesterday).get_result(
        IncomeType.MAKER_CASHBACK_PAY)
    if not is_finish:
        return
    r = AppraisalHistory.query.filter(
        AppraisalHistory.business_type == AppraisalHistory.BusinessType.SPOT,
        AppraisalHistory.report_date == this_month_first_day
    ).first()
    # 考核过则不再考核
    if r:
        return
    last_month_end_day = this_month_first_day - timedelta(days=1)
    if not UserTradeSummary.check_data_ready(last_month_end_day, 
                                             UserTradeSummary.System.SPOT):
        return

    checker = Checker(MarketMaker.MakerType.SPOT)
    checker.check_market_maker_new_level(
        checker.all_appraisal_mmer_user_ids, write_status_history=True
    )


@scheduled(crontab(minute=20, hour='1-3', day_of_month=1))
def update_perpetual_market_maker_level_schedule():
    today = now().date()
    this_month_first_day = date(today.year, today.month, 1)
    r = AppraisalHistory.query.filter(
        AppraisalHistory.business_type == AppraisalHistory.BusinessType.PERPETUAL,
        AppraisalHistory.report_date == this_month_first_day
    ).first()
    # 考核过则不再考核
    if r:
        return
    last_month_end_day = this_month_first_day - timedelta(days=1)
    if not UserTradeSummary.check_data_ready(last_month_end_day, 
                                             UserTradeSummary.System.PERPETUAL):
        return
    checker = Checker(MarketMaker.MakerType.PERPETUAL)
    checker.check_market_maker_new_level(
        checker.all_appraisal_mmer_user_ids, write_status_history=True
    )
