from collections import Counter
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal
from math import ceil

from celery.schedules import crontab

from app.business import SiteSettings, PriceManager
from app.business.alert import send_alert_notice
from app.business.lock import lock_call
from app.business.risk_control.base import WithdrawalFuseConfig, add_risk_event_log, \
    RiskControlGroupConfig, DepositFuseConfig, get_deposit_withdrawal_rc_whitelist
from app.business.utils import yield_query_records_by_time_range
from app.business.voice import send_withdrawal_disabled_notice, send_deposit_disabled_notice, \
    send_withdrawal_cancel_notice
from app.caches.statistics import WithdrawalFuseStatisticsCache, \
    RiskControlHappendCache, NewDepositFuseStatisticsCache
from app.common import CeleryQueues, PrecisionEnum
from app.config import config
from app.models import (
    Withdrawal, RiskEventLog,
    WithdrawalSignature, Deposit, RiskControlMobileNoticeConfig,
)
from app.utils import now, route_module_to_celery_queue, amount_to_str, quantize_amount, batch_iter
from app.utils.celery_ import scheduled
from app.utils.date_ import date_to_datetime, current_timestamp, timestamp_to_datetime, today
from ...business.risk_control.deposit import is_temp_asset, get_temp_assets
from ...business.risk_control.withdrawal import AccumulatedWithdrawalHelper

route_module_to_celery_queue(__name__, CeleryQueues.RISK_CONTROL)


@scheduled(crontab(minute='10-50/10', hour="1-3"))
@lock_call()
def get_average_withdrawal_fuse_data_schedule():

    whitelist = AccumulatedWithdrawalHelper.get_whitelist_users()
    prices = PriceManager.assets_to_usd()

    end_date = now().date()
    start_date = end_date - timedelta(days=7)
    start_time = date_to_datetime(start_date)
    end_time = date_to_datetime(end_date)
    cache = WithdrawalFuseStatisticsCache()
    last_average_update_ts = int(cache.hget("last_average_update_ts") or 0)
    #  当日更新过之后不再更新
    if (last_average_update_ts > 0 and
            timestamp_to_datetime(last_average_update_ts).date() == today()):
        return

    query = yield_query_records_by_time_range(
        table=Withdrawal, start_time=start_time, end_time=end_time,
        select_fields=(
            Withdrawal.user_id,
            Withdrawal.type,
            Withdrawal.asset,
            Withdrawal.amount,
            Withdrawal.status)
    )
    total_usd = Decimal()
    total_count = 0

    for row in query:
        if row.type == Withdrawal.Type.LOCAL:
            continue
        if row.user_id in whitelist:
            continue
        if row.status not in (
                Withdrawal.Status.CONFIRMING,
                Withdrawal.Status.FINISHED):
            continue

        total_usd += abs(prices.get(row.asset, 0) * row.amount)
        total_count += 1
    last_7d_average_usd = quantize_amount(total_usd / 7, PrecisionEnum.CASH_PLACES)
    last_7d_average_count = ceil(Decimal(total_count / 7))
    last_average_update_ts = current_timestamp(to_int=True)
    result = dict(
        last_7d_average_usd=amount_to_str(last_7d_average_usd),
        last_7d_average_count=amount_to_str(last_7d_average_count),
        last_average_update_ts=last_average_update_ts
    )
    cache.hmset(result)


@scheduled(crontab(minute='10-50/10', hour="1-3"))
@lock_call()
def get_average_deposit_fuse_data_schedule():

    whitelist = get_deposit_withdrawal_rc_whitelist()
    prices = PriceManager.assets_to_usd()

    end_date = now().date()
    start_date = end_date - timedelta(days=7)
    start_time = date_to_datetime(start_date)
    end_time = date_to_datetime(end_date)
    cache = NewDepositFuseStatisticsCache()
    last_average_update_ts = int(cache.hget("last_average_update_ts") or 0)
    #  当日更新过之后不再更新
    if (last_average_update_ts > 0 and
            timestamp_to_datetime(last_average_update_ts).date() == today()):
        return

    query = yield_query_records_by_time_range(
        table=Deposit, start_time=start_time, end_time=end_time,
        select_fields=(
            Deposit.user_id,
            Deposit.type,
            Deposit.asset,
            Deposit.amount,
            Deposit.status)
    )
    total_usd = Decimal()
    total_count = 0

    for row in query:
        if row.type == Deposit.Type.LOCAL:
            continue
        if row.user_id in whitelist:
            continue
        if row.status == Deposit.Status.CANCELLED:
            continue

        total_usd += abs(prices.get(row.asset, 0) * row.amount)
        total_count += 1
    last_7d_average_usd = quantize_amount(total_usd / 7, PrecisionEnum.CASH_PLACES)
    last_7d_average_count = ceil(Decimal(total_count / 7))
    last_average_update_ts = current_timestamp(to_int=True)
    result = dict(
        last_7d_average_usd=amount_to_str(last_7d_average_usd),
        last_7d_average_count=amount_to_str(last_7d_average_count),
        last_average_update_ts=last_average_update_ts
    )
    cache.hmset(result)


def close_p2p_trading_enabled():
    from app.schedules.p2p.order import batch_cancel_not_confirmed_order_task

    if not SiteSettings.p2p_trading_enabled:
        return
    SiteSettings.p2p_trading_enabled = False
    batch_cancel_not_confirmed_order_task.delay()


@scheduled(120)
@lock_call()
def check_site_withdrawal_fuse_schedule():
    whitelist = AccumulatedWithdrawalHelper.get_whitelist_users()
    prices = PriceManager.assets_to_usd()

    end_time = now()
    start_time = now() - timedelta(days=1)

    query = yield_query_records_by_time_range(
        table=Withdrawal, start_time=start_time, end_time=end_time,
        select_fields=(
            Withdrawal.user_id,
            Withdrawal.type,
            Withdrawal.asset,
            Withdrawal.amount,
            Withdrawal.status)
    )
    user_usd_data = Counter()
    user_count_data = Counter()
    latest_24h_usd = Decimal()
    latest_24h_count = 0
    cache = WithdrawalFuseStatisticsCache()
    tmp_exclude_assets = get_temp_assets()
    for row in query:
        if is_temp_asset(row.asset, tmp_exclude_assets):
            continue
        if row.type == Withdrawal.Type.LOCAL:
            continue
        if row.user_id in whitelist:
            continue
        if row.status not in (
                Withdrawal.Status.CONFIRMING,
                Withdrawal.Status.FINISHED
        ):
            continue
        usd = abs(prices.get(row.asset, 0) * row.amount)
        latest_24h_usd += usd
        latest_24h_count += 1
        user_usd_data[row.user_id] += usd
        user_count_data[row.user_id] += 1
    cache_data = cache.hgetall()
    cache.hmset(
        dict(
            latest_24h_usd=amount_to_str(latest_24h_usd, PrecisionEnum.CASH_PLACES),
            latest_24h_count=int(latest_24h_count),
            latest_24h_update_ts=current_timestamp(to_int=True)
        )
    )

    last_7d_average_usd = quantize_amount(
        cache_data.get("last_7d_average_usd", Decimal()),
        PrecisionEnum.CASH_PLACES)
    last_7d_average_count = Decimal(cache_data.get("last_7d_average_count", Decimal()))

    if last_7d_average_count == Decimal() or last_7d_average_usd == Decimal():
        return

    if WithdrawalFuseConfig.tmp_withdrawal_fuse_count_threshold > Decimal():
        count_threshold = WithdrawalFuseConfig.tmp_withdrawal_fuse_count_threshold
    else:
        count_threshold = WithdrawalFuseConfig.withdrawal_fuse_count_threshold

    if WithdrawalFuseConfig.tmp_withdrawal_fuse_usd_threshold > Decimal():
        usd_threshold = WithdrawalFuseConfig.tmp_withdrawal_fuse_usd_threshold
    else:
        usd_threshold = WithdrawalFuseConfig.withdrawal_fuse_usd_threshold

    current_count_threshold = quantize_amount(Decimal(latest_24h_count / last_7d_average_count),
                                              PrecisionEnum.RATE_PLACES)
    current_usd_threshold = quantize_amount(latest_24h_usd / last_7d_average_usd,
                                            PrecisionEnum.RATE_PLACES)

    if (current_count_threshold > count_threshold or
            current_usd_threshold > usd_threshold):
        count_warning = "已达到阈值" if current_count_threshold > count_threshold else "未达到阈值"
        usd_warning = "已达到阈值" if current_usd_threshold > usd_threshold else "未达到阈值"
        msg = f"""
【全站提现熔断告警】
告警详情：
最近24H提现市值/7日均值：{latest_24h_usd}/{last_7d_average_usd}≈{current_usd_threshold}，阈值为{usd_threshold}，{usd_warning}
最近24H提现笔数/7日均值：{latest_24h_count}/{last_7d_average_count}≈{current_count_threshold}，阈值为{count_threshold}，{count_warning}
注：已关闭全站提现和全站P2P交易，请即使查看处理.
        """
        SiteSettings.withdrawals_disabled_by_risk_control = True
        close_p2p_trading_enabled()
        send_alert_notice(msg, config["ADMIN_CONTACTS"].get("web_notice"),
                          at=config["ADMIN_CONTACTS"]["slack_at"].get("web_notice"))
        send_alert_notice(msg, config["ADMIN_CONTACTS"]["customer_service"],
                          at=config["ADMIN_CONTACTS"]["slack_at"].get("withdrawal_fuse")
                          )
        mobiles = RiskControlMobileNoticeConfig.get_mobiles(
            RiskControlMobileNoticeConfig.MobileNoticeEventType.SITE_WITHDRAWAL_FUSE
        )
        for mobile in mobiles:
            send_withdrawal_disabled_notice.delay(mobile)
        extra = dict(
            latest_24h_usd=latest_24h_usd,
            latest_24h_count=latest_24h_count,
            last_7d_average_usd=last_7d_average_usd,
            last_7d_average_count=last_7d_average_count,
            current_usd_threshold=current_usd_threshold,
            current_count_threshold=current_count_threshold,
            usd_threshold=usd_threshold,
            count_threshold=count_threshold,
            count_warning=count_warning,
            usd_warning=usd_warning,
            usd_top_10_data={v[0]: amount_to_str(v[1], PrecisionEnum.CASH_PLACES)
                             for v in
                             user_usd_data.most_common(10)},
            count_top_10_data={v[0]: v[1] for v in user_count_data.most_common(10)},
        )
        add_risk_event_log(
            "system",
            RiskEventLog.Reason.WITHDRAWAL_FUSE,
            current_timestamp(to_int=True),
            0,
            extra,
            0
        )


@scheduled(120)
@lock_call()
def check_signed_withdrawals_cancel_schedule():
    """已经签名过的提现被取消风控"""
    key = 'signed_withdrawals_cancel'
    cache = RiskControlHappendCache(key)
    if cache.exists():
        return

    whitelist = AccumulatedWithdrawalHelper.get_whitelist_users()

    prices = PriceManager.assets_to_usd()
    end_time = now()
    start_time = now() - timedelta(days=1)

    query = yield_query_records_by_time_range(
        table=WithdrawalSignature, start_time=start_time, end_time=end_time,
        select_fields=(WithdrawalSignature.withdrawal_id, WithdrawalSignature.status)
    )
    wids = []
    for row in query:
        if row.status == WithdrawalSignature.Status.CANCELLED:
            continue
        wids.append(row.withdrawal_id)
    withdrawals = []
    for ids in batch_iter(wids, 1000):
        rows = Withdrawal.query.filter(Withdrawal.id.in_(ids),
                                       Withdrawal.status == Withdrawal.Status.CANCELLED) \
                         .with_entities(Withdrawal.user_id, Withdrawal.asset, Withdrawal.amount).all()
        withdrawals.extend(rows)

    total = 0
    user_amounts = defaultdict(Decimal)
    for w in withdrawals:
        if w.user_id in whitelist:
            continue
        usd = abs(prices.get(w.asset, 0) * w.amount)
        user_amounts[w.user_id] += usd
        total += usd
    threshold = RiskControlGroupConfig().signed_withdrawals_cancel_threshold
    if total >= threshold:
        SiteSettings.withdrawals_disabled_by_risk_control = True
        cache.set(1, ex=86400)
        msg = (f"【提现取消风控告警】最近24H全站已签名但提现取消的提现市值：{amount_to_str(total, 2)}，"
               f"阈值为{amount_to_str(threshold, 2)}，已达到阈值。\n"
                "注：已关闭全站提现，请及时查看处理。")
        send_alert_notice(msg, config["ADMIN_CONTACTS"]["customer_service"],
                          at=config["ADMIN_CONTACTS"]["slack_at"].get("withdrawal_fuse")
                          )
        top10 = sorted(user_amounts.items(), key=lambda x: x[1], reverse=True)[:10]
        extra = dict(
            latest_24h_usd=quantize_amount(total, 2),
            threshold=quantize_amount(threshold, 2),
            top10_usd_data={v[0]: amount_to_str(v[1], 2) for v in top10}
        )
        mobiles = RiskControlMobileNoticeConfig.get_mobiles(
            RiskControlMobileNoticeConfig.MobileNoticeEventType.WITHDRAWAL_CANCELLED
        )
        for mobile in mobiles:
            send_withdrawal_cancel_notice.delay(mobile)
        add_risk_event_log(
            "system",
            RiskEventLog.Reason.SIGNED_WITHDRAWALS_CANCEL.name,
            current_timestamp(to_int=True),
            0,
            extra,
            0
        )


@scheduled(120)
@lock_call()
def check_site_deposit_fuse_schedule():
    key = 'deposit_fuse'

    whitelist = get_deposit_withdrawal_rc_whitelist()
    prices = PriceManager.assets_to_usd()
    end_time = now()
    start_time = now() - timedelta(days=1)

    def query_latest_deposits(start, end):
        query = yield_query_records_by_time_range(
            table=Deposit, start_time=start, end_time=end,
            select_fields=(Deposit.user_id, Deposit.type, Deposit.asset, Deposit.amount, Deposit.status)
        )
        for row in query:
            if row.type == Deposit.Type.LOCAL:
                continue
            if row.user_id in whitelist:
                continue
            if row.status not in [
                Deposit.Status.FINISHED,
                Deposit.Status.TO_HOT,
            ]:
                continue
            yield row

    total = 0
    user_amounts = defaultdict(Decimal)
    latest_24h_count = 0
    tmp_exclude_assets = get_temp_assets()
    for row in query_latest_deposits(start_time, end_time):
        if is_temp_asset(row.asset, tmp_exclude_assets):
            continue
        usd = abs(prices.get(row.asset, 0) * row.amount)
        user_amounts[row.user_id] += usd
        total += usd
        latest_24h_count += 1
    statistic_cache = NewDepositFuseStatisticsCache()
    cache_data = statistic_cache.hgetall()
    statistic_cache.hmset(
        dict(
            latest_24h_usd=amount_to_str(total, PrecisionEnum.CASH_PLACES),
            latest_24h_count=int(latest_24h_count),
            latest_24h_update_ts=current_timestamp(to_int=True)
        )
    )

    cache = RiskControlHappendCache(key)
    if cache.exists():
        return

    if avg := cache_data.get("last_7d_average_usd", 0):
        avg = Decimal(avg)
    else:
        _sum = 0
        end = date_to_datetime(today())
        for row in query_latest_deposits(end - timedelta(days=7), end):
            _sum += abs(prices.get(row.asset, 0) * row.amount)
        avg = quantize_amount(_sum / 7, 2)

    if avg == Decimal():
        return

    if DepositFuseConfig.tmp_deposits_fuse_threshold > Decimal():
        threshold = DepositFuseConfig.tmp_deposits_fuse_threshold
    else:
        threshold = DepositFuseConfig.deposits_fuse_threshold

    target = avg * threshold
    if total >= target:
        SiteSettings.deposits_disabled_by_risk_control = True
        cache.set(1, ex=86400)
        total_value = amount_to_str(total, 2)
        avg_value = amount_to_str(avg, 2)
        rate_value = amount_to_str(total / avg if avg else 0, 2)
        threshold_value = amount_to_str(threshold, 2)
        msg = (f"【全站充值风控告警】最近24H全站充值市值/7日均值：{total_value}/{avg_value}"
               f"≈{rate_value}，"
               f"阈值为{threshold_value}，已达到阈值。\n"
                "注：已关闭全站充值，请及时查看处理。")
        send_alert_notice(msg, config["ADMIN_CONTACTS"]["customer_service"],
                          at=config["ADMIN_CONTACTS"]["slack_at"].get("accumulated_deposit_notice")
                          )
        mobiles = RiskControlMobileNoticeConfig.get_mobiles(
            RiskControlMobileNoticeConfig.MobileNoticeEventType.SITE_DEPOSIT_FUSE
        )
        for mobile in mobiles:
            send_deposit_disabled_notice.delay(mobile)
        top10 = sorted(user_amounts.items(), key=lambda x: x[1], reverse=True)[:10]
        extra = dict(
            latest_24h_usd=total_value,
            avg=avg_value,
            rate=rate_value,
            threshold=threshold_value,
            top10_usd_data={v[0]: amount_to_str(v[1], 2) for v in top10}
        )
        add_risk_event_log(
            "system",
            RiskEventLog.Reason.DEPOSITS_FUSE.name,
            current_timestamp(to_int=True),
            0,
            extra,
            0
        )
