# -*- coding: utf-8 -*-
import json
from decimal import <PERSON>ima<PERSON>
from typing import List, Dict, Tu<PERSON>
from datetime import <PERSON><PERSON><PERSON>
from collections import defaultdict

from celery.schedules import crontab
from sqlalchemy import func
from flask import current_app

from ...business.clients.wallet import CoinexWalletClient
from ...config import config
from ...caches.assets import HotWalletBalanceCache, WalletNodeAbnormalStatusCache, CoinexWalletChainSupportCache, \
    CoinexWalletChainMapCache
from ...common import CeleryQueues
from ...assets import (asset_to_chains, get_asset_chain_config, get_asset_config,
                       Asset, Chain)
from ...models import Deposit, Withdrawal, AssetChainSignConfig, db
from ...caches import AssetCache, AssetChainsRankCache, CustomWithdrawalAssetPriceCache
from ...business import lock_call, WalletClient, ServerClient, send_alert_notice
from ...utils import (scheduled, now, route_module_to_celery_queue)
from ...utils.helper import Struct


route_module_to_celery_queue(__name__, CeleryQueues.WALLET)


@scheduled(180)
@lock_call()
def sync_assets():
    result = WalletClient().get_assets()
    assets: List[Asset] = []
    chains: Dict[str, Chain] = {}
    asset_chains: Dict[str, List[str]] = {}
    ac_confs: Dict[Tuple[str, str], dict] = {}

    main_asset_of_chains: Dict[str, str] = {}
    privacy_assets: set = set()
    for a in result:
        for c in a['chains']:
            if c['is_main_asset_of_chain']:
                main_asset_of_chains[c['chain_name']] = a['code']

    coinex_wallet_support_map = CoinexWalletChainSupportCache().read_data()

    for a in result:
        a_code = a['code']
        magnification_info = a.get('magnification_info')
        assets.append(Asset(
            code=a_code,
            name=a['name'],
            magnification_info=json.dumps(magnification_info) if magnification_info else '',
        ))

        asset_chains[a_code] = []
        for c in a['chains']:
            c_name = c['chain_name']
            if c_name not in chains:
                chains[c_name] = Chain(
                    name=c_name,
                    network_name=c['chain_network_name'],
                    display_name=c['chain_name_displayed'],
                    main_asset=main_asset_of_chains.get(c_name)
                )
            asset_chains[a_code].append(c_name)
            # must convert value type
            supported_by_via_wallet = is_coinex_wallet_support(c_name, c['is_dynamic'], coinex_wallet_support_map)
            ac_confs[(a_code, c_name)] = dict(
                is_dynamic=c['is_dynamic'],
                is_visible=c['is_visible'],
                deposits_visible=c['deposits_visible'],
                withdrawals_visible=c['withdrawals_visible'],
                deposits_enabled=c['deposits_enabled'],
                withdrawals_enabled=c['withdrawals_enabled'],
                min_deposit_amount=Decimal(c['min_deposit_amount']),
                min_withdrawal_amount=Decimal(c['min_withdrawal_amount']),
                withdrawal_precision=c['withdrawal_precision'],
                withdrawal_fee=Decimal(c['withdrawal_fee']),
                deflation_rate=Decimal(c['deflation_rate']),
                supports_memo=c['supports_memo'],
                memo_name=c['memo_name'],
                requires_memo_for_deposits=c['requires_memo_for_deposits'],
                identity=c['identity'] or '',
                explorer_asset_url=c['explorer_url'],
                safe_confirmations=c['safe_confirmations'],
                irreversible_confirmations=c['irreversible_confirmations'],
                deposits_delay_minutes=int(c['deposits_delay'].get('delay_minutes', 0) if c['deposits_delay'] else 0),
                block_time=int(c['block_time']),
                supported_by_via_wallet=supported_by_via_wallet,
                deposit_suspension_reasons=list(c['deposit_suspension_reasons']),
                withdrawal_suspension_reasons=list(c['withdrawal_suspension_reasons']),
            )
            if c.get("is_privacy_asset"):
                privacy_assets.add(a_code)  # 有一个链认为是隐私币，就是隐私币

    # sort
    for a, cs in asset_chains.items():
        _sort_asset_chains(a, cs, main_asset_of_chains)

    chain_assets = _chain_to_assets(asset_chains)
    for c, as_ in chain_assets.items():
        _sort_chain_assets(c, as_, main_asset_of_chains)

    new_assets = AssetCache.update_asset_chains(assets, list(chains.values()), asset_chains, chain_assets)

    now_ = now()
    for (asset, chain), values in ac_confs.items():
        ac_conf = get_asset_chain_config(asset, chain)
        for k, v in values.items():
            vv = getattr(ac_conf, k)
            if v != vv:
                current_app.logger.info(f"set {asset}-{chain} {k} to {v}")
                setattr(ac_conf, k, v)
                if k == "deposits_enabled":
                    ac_conf.deposits_updated_at = now_
                    current_app.logger.info(f"set {asset}-{chain} deposits_updated_at to {now_}")
                elif k == "withdrawals_enabled":
                    ac_conf.withdrawals_updated_at = now_
                    current_app.logger.info(f"set {asset}-{chain} withdrawals_updated_at to {now_}")

    for asset in asset_chains.keys():
        asset_cfg = get_asset_config(asset)
        _is_privacy = asset in privacy_assets
        if asset_cfg.is_privacy != _is_privacy:
            asset_cfg.is_privacy = _is_privacy
            current_app.logger.info(f"set {asset} is_privacy to {_is_privacy}")

    if new_assets:
        current_app.logger.info("sync new assets: %s", new_assets)
        ServerClient().update_assets()

    sync_asset_chain_sign_config(result)


def is_coinex_wallet_support(chain, is_dynamic, support_map):
    if not is_dynamic and chain in support_map:
        return True
    return support_map.get(chain, False)


def _chain_to_assets(asset_chains: Dict[str, List[str]]) -> Dict[str, List[str]]:
    chain_assets = {}
    for a, cs in asset_chains.items():
        for c in cs:
            chain_assets.setdefault(c, []).append(a)
    return chain_assets


def _sort_asset_chains(asset: str, chains: List[str], main_asset_of_chains: Dict[str, str]):
    rank = AssetChainsRankCache(asset).get_chains()
    def sorter(c):
        i, j = 0, len(rank)
        if main_asset_of_chains.get(c) == asset: # asset_to_default_chain: main chain first.
            i = -1
        if c in rank:
            j = rank.index(c)
        return (i, j, c)

    chains.sort(key=sorter)


def _sort_chain_assets(chain: str, assets: List[str], main_asset_of_chains: Dict[str, str]):
    def sorter(a):
        if a == main_asset_of_chains.get(chain): # main asset first.
            return (0, a)
        return (1, a)
    assets.sort(key=sorter)


def sync_asset_chain_sign_config(assets):
    """此处假设钱包是不可信的，只处理新增币种的信息，这些信息用于充值以及提现时的签名校验。
    不处理变更、下架币种的信息(需要人工确认处理)，避免攻击者通过修改币种信息绕过签名校验"""
    infos = []
    for a in assets:
        asset = a['code']
        for c in a['chains']:
            chain = c['chain_name']
            identity = c['identity']
            precision = c['precision']
            group = c['group']
            infos.append(dict(
                asset=asset,
                chain=chain,
                identity=identity or '',
                precision=precision,
                group=group
            ))
    db.session.rollback()
    rows = AssetChainSignConfig.query.all()
    old_assets = {(x.asset, x.chain) for x in rows}
    # chain-identity是唯一的，需要检查
    chain_identities = {(x.chain, x.identity) for x in rows}
    for info in infos:
        if (info['asset'], info['chain']) in old_assets:
            continue
        if (info['chain'], info['identity']) in chain_identities:
            current_app.logger.error(f"asset {info['asset']} of chain {info['chain']} has duplicate identity")
            continue
        chain_identities.add((info['chain'], info['identity']))
        db.session.add(AssetChainSignConfig(
            **info
        ))
        current_app.logger.info(f"add new asset {info['asset']} of chain {info['chain']} info")
    db.session.commit()


@scheduled(crontab(minute="7", hour="2"))
@lock_call()
def diff_asset_chain_sign_config():
    """对比配置变更，提醒手动确认更新"""
    assets = WalletClient().get_assets()
    wallet_config = []
    for a in assets:
        asset = a['code']
        for c in a['chains']:
            chain = c['chain_name']
            identity = c['identity']
            precision = c['precision']
            group = c['group']
            wallet_config.append(Struct(
                asset=asset,
                chain=chain,
                identity=identity or '',
                precision=precision,
                group=group
            ))
    local_config = AssetChainSignConfig.query.all()
    # 先对比asset-identity相同的
    ai_local = {(x.asset, x.chain, x.identity): x for x in local_config}
    ai_wallet = {(x.asset, x.chain, x.identity): x for x in wallet_config}
    diffs = []
    for k, v in ai_local.items():
        if k not in ai_wallet:
            continue
        vv = ai_wallet[k]
        desc = []
        if v.precision != vv.precision:
            desc.append(f"precision '{v.precision}' to '{vv.precision}'")
        if v.group != vv.group:
            desc.append(f"group '{v.group}' to '{vv.group}'")
        if desc:
            diffs.append(f"{v.asset}-{v.chain}: " + ', '.join(desc))
    # asset-identity不同的，是改了asset或identity
    a_local = {(x.asset, x.chain): x for x in local_config}
    i_local = {(x.identity, x.chain): x for x in local_config}
    not_founds = set(ai_wallet.keys()) - set(ai_local.keys())
    for asset, chain, identity in not_founds:
        v1 = a_local.get((asset, chain))
        v2 = i_local.get((identity, chain))
        if v2:
            desc = f"asset '{v2.asset}' to '{asset}' (identity: {identity})"
        elif v1:
            desc = f"identity '{v1.identity}' to '{identity}'"
        else:  # 这种情况是还没来得及同步新增
            continue
        diffs.append(f"{asset}-{chain}: {desc}")
    if diffs:
        send_alert_notice('\n'.join(diffs), config["ADMIN_CONTACTS"]["web_notice"])
    # 应该删除的
    a_wallet = {(x.asset, x.chain): x for x in wallet_config}
    deleted = a_local.keys() - a_wallet.keys()
    if deleted:
        send_alert_notice("deleted: " + ', '.join(f"{a}-{c}" for a, c in deleted), config["ADMIN_CONTACTS"]["web_notice"])


@scheduled(crontab(minute="30", hour="0"))
@lock_call()
def update_asset_chains_rank():
    start = now() - timedelta(days=30)
    counts = defaultdict(lambda: defaultdict(int))
    for asset, chain, count in Deposit.query \
        .filter(Deposit.type == Deposit.Type.ON_CHAIN,
                Deposit.created_at >= start) \
        .group_by(Deposit.asset, Deposit.chain) \
        .with_entities(Deposit.asset,
                       Deposit.chain,
                       func.count(Deposit.id).label('count')):
        counts[asset][chain] += count
    for asset, chain, count in Withdrawal.query \
        .filter(Withdrawal.type == Withdrawal.Type.ON_CHAIN,
                Withdrawal.created_at >= start) \
        .group_by(Withdrawal.asset, Withdrawal.chain) \
        .with_entities(Withdrawal.asset,
                       Withdrawal.chain,
                       func.count(Withdrawal.id).label('count')):
        counts[asset][chain] += count

    for asset, chains in asset_to_chains().items():
        chain_counts = counts[asset]
        chains = sorted(chains, key=lambda x: chain_counts[x], reverse=True)
        AssetChainsRankCache(asset).set_chains(chains)


@scheduled(crontab(minute='5', hour='*/1'))
@lock_call()
def update_asset_chain_custom_withdrawal_fee_schedule():
    """ 更新 币种-链 自定义提现币种的手续费数目 """
    CustomWithdrawalAssetPriceCache.reload()


@scheduled(180)
@lock_call()
def sync_balances_and_node_statuses():
    client = WalletClient()
    balances = client.get_account_balance()
    for balance in balances:
        _asset, _chain, hot_balance = balance["asset"], balance["chain"], balance["balances"]["HOT"]["total"]
        HotWalletBalanceCache(_asset, _chain).save_data(hot_balance)
    node_statuses = client.get_node_statuses()
    for _chain, node_status in node_statuses.items():
        if not node_status['is_healthy']:
            WalletNodeAbnormalStatusCache(_chain).set_status()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_coinex_wallet_support_info():
    # 获取 coinex wallet 支持的公链 和 token 信息
    wallet_support_map = {i["chain"]: i["support_token"] for i in CoinexWalletClient().get_chain_info()}
    # 获取 admin 配置的映射链
    data = {}
    chain_map = CoinexWalletChainMapCache().read()
    for web_chain, wallet_chain in chain_map.items():
        if wallet_chain in wallet_support_map:
            data[web_chain] = wallet_support_map[wallet_chain]
    CoinexWalletChainSupportCache().save_data(data)
    return data
