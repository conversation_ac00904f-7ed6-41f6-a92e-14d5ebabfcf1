from datetime import timedelta
from decimal import Decimal

from celery.schedules import crontab

from app import config
from app.business import update_gift_history_task, lock_call, LockAssetHelper
from app.business.p2p.advertising import P2pAdvListSnapBiz
from app.business.p2p.mer_act import P2pMerActBiz
from app.business.p2p.message import P2pMerActRewardSuccess
from app.caches.p2p import P2pMerActCache
from app.common import BalanceBusiness, CeleryQueues
from app.models import P2pMerActStatisticTime, db, P2pMerActRewardHistory, P2pMerActUserReward, \
    GiftHistory
from app.models.mongo.p2p.mer_act import P2pMerActMySQL as P2pMerAct
from app.models.wallet import LockedAssetBalance
from app.utils import today_datetime, now, route_module_to_celery_queue, scheduled, group_by

route_module_to_celery_queue(__name__, CeleryQueues.P2P)


@scheduled(crontab(hour='*/1', minute='5'))
@lock_call()
def update_mer_act_hour_point():
    """更新每小时积分"""
    acts = P2pMerActBiz.get_statistic_acts(today_datetime())
    for act in acts:
        sta_row = P2pMerActStatisticTime.get_or_create(act_id=act.act_id)
        cur = sta_row.hour_point_time
        if not cur:
            cur = act.start_at.replace(minute=0, second=0, microsecond=0)
        else:
            cur += timedelta(hours=1)
        end_hour = P2pMerActBiz.get_statistic_end_hour(act)
        # while 循环是先执行再加一小时，数据库加1小时等于最新时间表示已经执行过。
        if cur > end_hour:
            continue
        while cur <= end_hour:
            valid_hour = cur.hour
            if act.valid_start_hour <= valid_hour < act.valid_end_hour:
                P2pMerActBiz.save_act_hour_point_history(act, cur)
            sta_row.hour_point_time = cur
            db.session_add_and_commit(sta_row)
            cur += timedelta(hours=1)


@scheduled(crontab(minute='20', hour='0-1'))
@lock_call()
def update_mer_act_day_point():
    """更新每日积分"""
    today_ = today_datetime()
    acts = P2pMerActBiz.get_statistic_acts(today_)
    model = P2pMerActStatisticTime
    for act in acts:
        sta_row = model.query.filter(model.act_id == act.act_id).first()
        # 等待小时积分计算完成
        if not sta_row:
            continue
        cur = sta_row.day_point_time
        if not cur:
            cur = P2pMerActBiz.get_act_default_day_dt(act)
        else:
            cur += timedelta(days=1)
        end_statistic_day = P2pMerActBiz.get_statistic_end_day(act)
        # 已经统计过最新一天了
        if cur > end_statistic_day:
            continue
        while cur <= end_statistic_day:
            next_day_dt = cur + timedelta(days=1)
            end_hour = min(next_day_dt - timedelta(hours=1), P2pMerActBiz.get_statistic_end_hour(act))
            # 小时积分计算完成才能计算每日积分
            if not sta_row.hour_point_time or sta_row.hour_point_time < end_hour:
                break
            P2pMerActBiz.save_act_day_point_history(act, cur)
            sta_row.day_point_time = cur
            db.session.commit()
            cur = next_day_dt


@scheduled(crontab(minute='30', hour='0-1'))
@lock_call()
def update_mer_act_reward():
    """发放每日奖励"""
    today_ = today_datetime()
    acts = P2pMerActBiz.get_statistic_acts(today_)
    model = P2pMerActStatisticTime
    for act in acts:
        sta_row = model.query.filter(model.act_id == act.act_id).first()
        if not sta_row:
            continue
        cur = sta_row.day_reward_time
        if not cur:
            cur = P2pMerActBiz.get_act_default_day_dt(act)
        else:
            cur += timedelta(days=1)
        # 已经统计过最新一天了
        end_statistic_day = P2pMerActBiz.get_statistic_end_day(act)
        if cur > end_statistic_day:
            continue
        while cur <= end_statistic_day:
            # 每日积分计算完才能发放每日奖励
            if not sta_row.day_point_time or sta_row.day_point_time < cur:
                break
            P2pMerActBiz.save_act_day_reward_history(act, cur)
            sta_row.day_reward_time = cur
            db.session.commit()
            cur += timedelta(days=1)


@scheduled(crontab(minute="*/5"))
@lock_call()
def send_mer_act_reward_schedule():
    """实际发放奖励"""
    model = P2pMerActRewardHistory
    rows = model.query.filter(
        model.release_status == model.ReleaseStatus.CREATED,
    ).all()
    if not rows:
        return
    act_ids = {row.act_id for row in rows}
    acts = (
        P2pMerAct.query.filter(P2pMerAct.act_id.in_(act_ids))
        .with_entities(P2pMerAct.act_id, P2pMerAct.activity_id)
        .all()
    )
    act_map = {act.act_id: act.activity_id for act in acts}
    for row in rows:
        user_id = row.user_id
        asset = row.asset
        amount = row.amount
        activity_id = act_map[row.act_id]
        gift_record = GiftHistory(
            user_id=user_id,
            activity_id=activity_id,
            asset=asset,
            amount=amount,
            lock_time=row.reward_lock_day * 86400,
            status=GiftHistory.Status.CREATED,
            remark=f"p2p商家活动 {row.act_id}",
        )
        db.session_add_and_flush(gift_record)
        row.gift_id = gift_record.id
        row.release_status = model.ReleaseStatus.RELEASED
    db.session.commit()

    activity_map = {activity_id: act_id for act_id, activity_id in act_map.items()}
    for activity_id, act_id in activity_map.items():
        update_gift_history_task.delay(
            activity_id,
            BalanceBusiness.GIFT.value,
            wait=False,
            pay_from_admin_user_id=config['P2P_MER_ACT_USER_ID'],
            is_lock=True,
        )

    for row in rows:
        P2pMerActRewardSuccess(row.act_id).send_message(
            row.user_id, reward_date=row.reward_date.strftime("%Y-%m-%d")
        )


@scheduled(crontab(minute="*/1"))
@lock_call()
def update_mer_act_gift_status():
    """更新奖励状态"""
    unlock_minuter = 3
    # 获取所有活动的 activity_ids
    a_model = P2pMerAct
    activity_ids = [i.activity_id for i in a_model.query.with_entities(a_model.activity_id).all()]
    if not activity_ids:
        return
    g_model = GiftHistory
    gift_rows = g_model.query.filter(
        g_model.activity_id.in_(activity_ids),
        g_model.status == g_model.Status.LOCKED,
    ).all()
    if not gift_rows:
        return
    user_ids = {row.user_id for row in gift_rows}
    abnormal_user_ids = P2pMerActBiz.get_abnormal_user_ids(user_ids)
    # 1. 检查商家状态，异常将记录改为冻结
    # 2. 检查记录是否已经解锁
    l_model = LockedAssetBalance
    l_rows = l_model.query.filter(
        l_model.business == l_model.Business.GIFT,
        l_model.business_id.in_([row.id for row in gift_rows]),
    ).all()
    l_row_map = {row.business_id: row for row in l_rows}

    for gift_row in gift_rows:
        l_row: l_model = l_row_map.get(gift_row.id)
        if not l_row:
            continue
        # 查看资金记录是否已经解锁
        if l_row.status == l_row.Status.UNLOCKED:
            if l_row.unlock_type == l_row.OpType.UNLOCK_AND_SUB:
                gift_row.status = g_model.Status.REVOKED
            else:
                gift_row.status = g_model.Status.FINISHED
            db.session.commit()
        elif l_row.status == l_row.Status.LOCKED:
            if gift_row.user_id in abnormal_user_ids:
                # 如果快要解锁，不冻结
                if now() + timedelta(minutes=unlock_minuter) >= l_row.unlocked_at:
                    continue
                LockAssetHelper.freeze(l_row, is_commit=False)
                gift_row.status = g_model.Status.REAL_FROZEN
                db.session.commit()


@scheduled(crontab(minute="*/3"))
@lock_call()
def update_mer_act_user_reward_statistic():
    r_model = P2pMerActUserReward
    # 存在未解锁的用户记录
    acts = P2pMerActBiz.get_statistic_acts(today_datetime())
    act_ids = [i.act_id for i in acts]
    reward_rows = r_model.query.filter(r_model.act_id.in_(act_ids)).all()
    act_ids = list({row.act_id for row in reward_rows})
    user_ids = list({row.user_id for row in reward_rows})

    # 获取对应的礼物记录
    h_model = P2pMerActRewardHistory
    history_rows = h_model.query.filter(
        h_model.act_id.in_(act_ids),
        h_model.user_id.in_(user_ids),
        h_model.release_status == h_model.ReleaseStatus.RELEASED,
    ).all()
    history_group_map = group_by(lambda x: (x.act_id, x.side, x.user_id), history_rows)

    g_ids = [row.gift_id for row in history_rows]
    g_model = GiftHistory
    gift_rows = g_model.query.filter(
        g_model.id.in_(g_ids)
    ).all()
    gift_map = {g.id: g for g in gift_rows}

    for row in reward_rows:
        row.lock_amount = Decimal()
        row.release_amount = Decimal()
        his_rows = history_group_map.get((row.act_id, row.side, row.user_id), [])
        # 找到对应的礼物记录
        for his_row in his_rows:
            gift_row = gift_map[his_row.gift_id]
            if gift_row.status == g_model.Status.FINISHED:
                row.release_amount += gift_row.amount
            elif gift_row.status in {
                g_model.Status.FROZEN,
                g_model.Status.LOCKED,
                g_model.Status.REAL_FROZEN,
                g_model.Status.CREATED,
            }:
                row.lock_amount += gift_row.amount
    db.session.commit()


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_mer_act_status():
    """更新活动状态"""
    model = P2pMerAct
    now_ = now()
    rows = model.query.filter(
        model.status == model.Status.ONLINE,
        model.end_at <= now_
    ).all()
    for row in rows:
        row.status = model.Status.OFFLINE
        db.session.commit()
    if rows:
        P2pMerActCache.reload()


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_mer_act_cache():
    """更新活动缓存"""
    P2pMerActCache.reload()


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_mer_act_statistic():
    """更新活动统计数据"""
    P2pMerActBiz.update_act_statistic()


@scheduled(crontab(minute="*/10"))
@lock_call()
def update_before_act_start_snap():
    P2pAdvListSnapBiz.before_act_start_save()


@scheduled(crontab(minute="*/1"))
@lock_call()
def save_act_start_snap():
    P2pAdvListSnapBiz.act_start_save()
