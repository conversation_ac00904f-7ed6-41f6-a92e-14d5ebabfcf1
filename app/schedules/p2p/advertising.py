from collections import defaultdict
from datetime import timedelta

from celery.schedules import crontab
from sqlalchemy import func

from app.business import lock_call
from app.business.p2p.advertising import P2pAdvertisingBiz
from app.business.p2p.config import p2p_setting
from app.business.p2p.user import P2pUserManger
from app.business.p2p.utils import P2pUtils
from app.caches.p2p import AdvertisingBookCache, UniqueOptionCache
from app.common import CeleryQueues
from app.models import db, P2pOrder, P2pUser, P2pOrderComplaint
from app.models.mongo.p2p.advertising import P2pAdvertisingMySQL, AutoOfflineAdvReason, TradeStatisticsModel
from app.models.mongo import UserPayChannelMySQL
from app.utils import route_module_to_celery_queue, scheduled, celery_task, today_datetime

route_module_to_celery_queue(__name__, CeleryQueues.P2P)


@scheduled(crontab(minute="*/1"))
@lock_call()
def update_advertising_book_cache_schedules():
    AdvertisingBookCache.reload()


@celery_task
@lock_call(with_args=True)
def offline_advertising_by_asset_invalid_task(asset: str):
    P2pAdvertisingBiz.offline_advertising_by_settings(
        reason=AutoOfflineAdvReason.ASSET_INVALID,
        asset=asset
    )


@celery_task
@lock_call(with_args=True)
def offline_advertising_by_fiat_valid_task(fiat: str):
    P2pAdvertisingBiz.offline_advertising_by_settings(
        reason=AutoOfflineAdvReason.FIAT_INVALID,
        fiat=fiat
    )


@scheduled(crontab(minute="*/10"))
@lock_call()
def offline_advertising_by_cancel_order():
    """取消订单下架广告补偿任务"""
    model = P2pOrder
    status = model.Status
    # 3. 检查用户取消订单
    today = today_datetime()
    rows = model.query.filter(
        model.status == status.CANCELED,
        model.cancel_type.in_(model.cancel_count_types()),
        model.created_at >= today,
        model.created_at < today + timedelta(days=1),
    ).group_by(
        model.cancel_user_id,
    ).with_entities(
        model.cancel_user_id,
        func.count().label("cancel_count")
    ).all()
    user_ids = [row.cancel_user_id for row in rows
                if row.cancel_count >= p2p_setting.daily_merchant_cancel_order_limit]
    p_model = P2pUser
    p2p_mc_users = p_model.query.filter(
        p_model.user_id.in_(user_ids),
        p_model.merchant_id.isnot(None)
    ).with_entities(p_model.user_id)
    for user in p2p_mc_users:
        P2pUtils.offline_advertising_by_merchant(
            user.user_id,
            reason=AutoOfflineAdvReason.CANCELED_ORDER_LIMIT,
        )


@scheduled(crontab(minute="*/10"))
@lock_call()
def offline_advertising_by_order_complaint():
    """进行中申诉配置值，下架广告补偿任务"""
    model = P2pOrderComplaint
    # 3. 检查用户取消订单
    rows = model.query.filter(
        model.complaint_status.in_([model.Status.CREATED, model.Status.PENDING]),
    ).with_entities(
        model.plaintiff_id,
        model.defendant_id,
    ).all()
    complaint_count_map = defaultdict(int)
    for row in rows:
        complaint_count_map[row.plaintiff_id] += 1
        complaint_count_map[row.defendant_id] += 1

    filtered_map = {k: v for k, v in complaint_count_map.items() if v >= p2p_setting.complaint_offline_adv_limit}
    p_model = P2pUser
    p2p_mc_users = p_model.query.filter(
        p_model.user_id.in_(list(filtered_map.keys())),
        p_model.merchant_id.isnot(None)
    ).with_entities(p_model.user_id)
    for user in p2p_mc_users:
        P2pUtils.offline_advertising_by_merchant(
            user.user_id,
            reason=AutoOfflineAdvReason.ORDER_COMPLAINT_LIMIT,
        )


@scheduled(crontab(hour="*/1", minute="3"))
@lock_call()
def delete_unique_option_cache_keys():
    """删除过期的唯一操作的缓存key"""
    UniqueOptionCache.delete_expired_keys()


@celery_task
@lock_call(with_args=True)
def update_advertising_by_pay_channel_invalid_task(pay_channel_id: str):
    P2pAdvertisingBiz.pay_channel_invalid_update_advertising(
        pay_channel_id=pay_channel_id
    )


@celery_task
@lock_call(with_args=True)
def update_advertising_by_user_pay_channel_invalid_task(user_pay_channel_id: str):
    user_pay_channel: UserPayChannelMySQL = UserPayChannelMySQL.query.filter(UserPayChannelMySQL.mongo_id == user_pay_channel_id).first()
    if not user_pay_channel:
        return
    P2pAdvertisingBiz.user_pay_channel_delete_update_advertising(
        user_id=user_pay_channel.user_id,
        user_pay_channel_id=user_pay_channel.id,
        pay_channel_id=user_pay_channel.pay_channel_id
    )


@celery_task
@lock_call(with_args=True)
def update_merchant_advertising_task(merchant_user_id: int):
    stats = P2pUserManger(merchant_user_id).get_merchant_trade_statistics()
    new_extr = TradeStatisticsModel(**stats).model_dump()
    P2pAdvertisingMySQL.query.filter_by(user_id=merchant_user_id).update({
        P2pAdvertisingMySQL.extra: new_extr,
        P2pAdvertisingMySQL.updated_at: P2pAdvertisingMySQL.__table__.c.updated_at # 绕过updated_at字段的自动更新触发器
    })
    db.session.commit()


@celery_task
@lock_call()
def update_merchant_adv_trade_task():
    user_ids = P2pAdvertisingBiz.get_online_advertising_merchants()
    for user_id in user_ids:
        update_merchant_advertising_task.delay(user_id)
