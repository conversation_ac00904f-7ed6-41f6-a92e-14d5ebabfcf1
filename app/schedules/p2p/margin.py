import time
import traceback
from datetime import timedelta

from celery.schedules import crontab
from flask import current_app

from app.business import lock_call, CacheLock, LockKeys
from app.business.p2p.margin import P2pUserMarginHistoryBiz, update_require_margin
from app.business.p2p.message import P2pMarginPaymentMessage, send_p2p_margin_change_message
from app.business.p2p.utils import send_p2p_alert
from app.business.p2p.user import update_merchant_margin_status, no_margin_merchant_operate, p2p_user_update_merchant
from app.caches.p2p import P2pMarginGraceEmailCache
from app.common import CeleryQueues
from app.models import P2pMerchant, P2pUserMargin, db, P2pMarginHistory, KycVerification, P2pMarginCountry, P2pUser
from app.models.user import SignOffUser
from app.utils import route_module_to_celery_queue, scheduled, now, today_datetime

route_module_to_celery_queue(__name__, CeleryQueues.P2P)


def update_margin_status_by_rows(margin_rows):
    margin_map = {i.user_id: i for i in margin_rows}
    # 获取对应商家
    mer_model = P2pMerchant
    mer_rows = mer_model.query.filter(
        mer_model.user_id.in_([i.user_id for i in margin_rows]),
    )
    # 更新商家状态
    invalid_ids = set()
    for mer in mer_rows:
        margin = margin_map[mer.user_id]
        ret = update_merchant_margin_status(mer, margin.margin_pass)
        if ret and not margin.margin_pass:
            invalid_ids.add(mer.user_id)
    return invalid_ids


@scheduled(crontab(hour="*/1", minute="5"))
@lock_call()
def update_grace_margin_status():
    # 检查最近x天内宽限期过期的数据，超过宽限期的商家修改状态
    days = 10
    now_ = now()

    model = P2pUserMargin
    rows = model.query.filter(
        model.paid_margin < model.require_margin,
        model.grace_deadline >= now_ - timedelta(days=days),
        model.grace_deadline <= now_,
    ).all()
    invalid_ids = update_margin_status_by_rows(rows)
    db.session.commit()
    margin_map = {i.user_id: i for i in rows}
    for user_id in invalid_ids:
        no_margin_merchant_operate(user_id, margin_map[user_id].grace_deadline)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_user_margin_status():
    # 检查最近更新时间一小时内的数据，补偿可能出现的并发问题
    model = P2pUserMargin
    now_ = now()
    hour = 1
    minute = 3
    margin_rows = model.query.filter(
        model.updated_at >= now_ - timedelta(hours=hour),
        model.updated_at <= now_ - timedelta(minutes=minute),
    ).all()

    mer_model = P2pMerchant
    mer_rows = mer_model.query.filter(
        mer_model.user_id.in_([i.user_id for i in margin_rows]),
    )
    mer_map = {mer.user_id: mer for mer in mer_rows}
    # 单条数据处理
    for margin in margin_rows:
        user_id = margin.user_id
        mer = mer_map.get(user_id)
        if not mer and not margin.margin_pass:
            continue
        if not mer:
            mer = mer_model.get_or_create(user_id=user_id)
            db.session_add_and_flush(mer)
            p2p_user = P2pUser.query.filter(P2pUser.user_id == user_id).first()
            p2p_user_update_merchant(p2p_user, mer)
        ret = update_merchant_margin_status(mer, margin.margin_pass)
        db.session.commit()
        if ret and not margin.margin_pass:
            no_margin_merchant_operate(user_id, margin.grace_deadline)


@scheduled(crontab(minute='10', hour="*/1"))
@lock_call()
def send_margin_payment_message():
    # 商家在补充限期前30/7/1天的触达
    model = P2pUserMargin
    td = today_datetime()
    for days in [30, 7, 1]:
        end_time = td + timedelta(days=days)
        rows = model.query.filter(
            model.paid_margin < model.require_margin,
            model.grace_deadline >= end_time,
            model.grace_deadline < end_time + timedelta(days=1),
        ).all()
        cache = P2pMarginGraceEmailCache()
        for row in rows:
            user_id = row.user_id
            if not cache.has_user_id(user_id):
                P2pMarginPaymentMessage().send_message(user_id, row.grace_deadline)
                cache.add_user_id(user_id)


@scheduled(crontab(minute="*/2"))
@lock_call()
def merchant_canceling_refund():
    # 保证金赎回中状态超过48小时的商家，退还保证金
    hours = 48
    model = P2pMerchant
    rows = model.query.filter(
        model.canceling_time <= now() - timedelta(hours=hours),
        model.status == model.Status.CANCELING,
    ).all()
    user_ids = [i.user_id for i in rows]
    for user_id in user_ids:
        try:
            with CacheLock(LockKeys.update_p2p_user(user_id)):
                db.session.rollback()
                row = model.query.filter(model.user_id == user_id).first()
                if row.status != model.Status.CANCELING:
                    continue
                if SignOffUser.is_signoff_user(user_id):
                    row.status = model.Status.CANCELED
                    db.session.commit()
                    continue
                P2pUserMarginHistoryBiz.refund(user_id=user_id)
        except Exception:
            err_content = f"merchant {user_id} canceling refund error: {traceback.format_exc()}"
            current_app.logger.error(f"{err_content}")
            send_p2p_alert(err_content)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_kyc_country_require_margin():
    model = P2pUserMargin
    k_model = KycVerification
    m_model = P2pMerchant
    c_model = P2pMarginCountry

    MINUTES = 60
    # 获取最近一小时kyc通过且更新的商家
    kyc_rows = k_model.query.filter(
        k_model.updated_at >= now() - timedelta(minutes=MINUTES),
        k_model.status == k_model.Status.PASSED,
    ).all()
    kyc_country_map = {i.user_id: i.country for i in kyc_rows}

    # 检查这些用户是否为 p2p 有效商家
    m_rows = m_model.query.filter(
        m_model.user_id.in_(list(kyc_country_map.keys())),
        m_model.status.in_([m_model.Status.ACTIVE, m_model.Status.INACTIVE]),
    ).all()

    country_amount_map = {i.country_code: i.amount for i in c_model.query.all()}

    # 获取这些商家的保证金信息
    margin_rows = model.query.filter(
        model.user_id.in_([i.user_id for i in m_rows]),
        model.margin_type == model.MarginType.COUNTRY,
    ).all()

    update_info = []
    for margin in margin_rows:
        kyc_country = kyc_country_map[margin.user_id]
        old_amount = margin.require_margin
        new_amount = country_amount_map[kyc_country]
        if old_amount != new_amount:
            update_require_margin(margin, new_amount, P2pUserMargin.GraceSource.COUNTRY)
            update_info.append((margin.user_id, new_amount, old_amount))
    db.session.commit()
    for user_id, amount, old_amount in update_info:
        send_p2p_margin_change_message.delay(user_id, str(amount), str(old_amount))


@scheduled(crontab(minute="*/3"))
@lock_call()
def fixup_margin_trans_process():
    P2pUserMarginHistoryBiz.fixup_mer_payment_refund_pending()
    P2pUserMarginHistoryBiz.fixup_margin_trans_process()


@scheduled(crontab(minute="*/3"))
@lock_call()
def alter_margin_timeout():
    model = P2pMarginHistory
    minutes = P2pUserMarginHistoryBiz.MARGIN_FIX_END_MIN
    rows = model.query.filter(
        model.updated_at < now() - timedelta(minutes=minutes),
        model.status == model.Status.PROCESS
    ).all()
    if rows:
        err_content = (f"当前共有 {len(rows)} 条保证金划转记录超过 {minutes} 分钟未补偿，请人工处理, "
                       f"记录ID 分别是 {','.join([str(i.id) for i in rows])}")
        current_app.logger.error(err_content)
        send_p2p_alert(err_content)


@scheduled(crontab(minute="*/1"))
@lock_call()
def margin_balance_check_task():
    # 防止和退款的任务并发，导致误报，稍微错开一下时间
    time.sleep(5)
    P2pUserMarginHistoryBiz.margin_balance_check()


@scheduled(crontab(minute="*/1"))
@lock_call()
def user_margin_negative_check_task():
    P2pUserMarginHistoryBiz.user_margin_negative_check()


