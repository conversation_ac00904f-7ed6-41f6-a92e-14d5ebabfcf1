import json
from datetime import timedelta
from decimal import Decimal, ROUND_UP

from celery.schedules import crontab
from flask import current_app

from app import config
from app.assets import get_asset_config
from app.business import lock_call, ServerClient, SPOT_ACCOUNT_ID, update_gift_history_task, UserPreferences
from app.business.activity.discount import process_discount_activity_order
from app.business.alert import send_alert_notice
from app.business.email import send_dibs_reword_email
from app.business.utils import yield_query_records_by_time_range
from app.caches.activity import DiscountActivityDetailCache
from app.exceptions import InvalidArgument
from app.models import db, DiscountActivity, DiscountActivitySystemOrder, DiscountActivityOrder, \
    DiscountActivityLotteryHistory, GiftHistory, DiscountActivityDetail, Message, DiscountActivityRewardHistory
from app.schedules.activity import update_discount_activity_cache_schedule
from app.utils import route_module_to_celery_queue, quantize_amount, amount_to_str, celery_task
from app.common import CeleryQueues, OrderSideType, BalanceBusiness, MessageTitle, MessageContent, MessageWebLink
from app.utils import now, scheduled

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


@scheduled(crontab(minute="*/5"))
@lock_call()
def process_user_discount_order_schedule():
    """ 处理用户可能存在扣款失败的申购订单 """
    end_time = now() - timedelta(minutes=5)
    start_time = end_time - timedelta(hours=2)
    for row in yield_query_records_by_time_range(
        DiscountActivityOrder,
        start_time,
        end_time,
        [
            DiscountActivityOrder.asset_status,
            DiscountActivityOrder.id,
        ],
            None,
            1000,
    ):
        if row.asset_status != DiscountActivityOrder.AssetStatusType.DEDUCTED:
            continue
        # 只处理用户被扣款但是server没有收到的情况
        process_discount_activity_order(row.id)


def put_activity_order(activity_id):
    now_ = now()
    origin_delta = Decimal('0.99')  # 初始挂单为最新成交单*99%
    step = Decimal('0.002')
    activity = DiscountActivity.query.get(activity_id)
    sys_order = DiscountActivitySystemOrder.query.filter(
        DiscountActivitySystemOrder.discount_activity_id == activity.id,
        DiscountActivitySystemOrder.status == DiscountActivitySystemOrder.Status.PROCESSING,
    ).first()
    if not sys_order:
        his_orders = DiscountActivitySystemOrder.query.filter(
            DiscountActivitySystemOrder.discount_activity_id == activity.id,
            DiscountActivitySystemOrder.status == DiscountActivitySystemOrder.Status.FINISHED,
        ).all()
        client = ServerClient()
        balances = client.get_user_balances(config['DISCOUNT_ADMIN_USER_ID'])
        target_asset = activity.asset
        delta = min(len(his_orders) * step + origin_delta, 1)  # 不超过100%
        last_price = client.market_last(activity.market)
        target_price = last_price * delta
        total_target_asset_traded_amount = sum([i.target_asset_traded_amount for i in his_orders])
        remain_amount = activity.total_amount - total_target_asset_traded_amount
        if remain_amount <= 0:
            return

        remain_amount = max([get_asset_config(target_asset).min_order_amount,
                             remain_amount])  # 小于最小下单数取最小下单数挂单
        source_asset_amount = remain_amount * target_price
        if balances[DiscountActivity.PAY_ASSET]['available'] < source_asset_amount:
            send_alert_notice(
                f"ADMIN账号余额不足，无法满足Dibs活动购币所需的数量，预计花费{source_asset_amount} {DiscountActivity.PAY_ASSET},"
                f"请尽快向账号{config['DISCOUNT_ADMIN_USER_ID']}充值",
                config["ADMIN_CONTACTS"]["discount_activity_order_notice"]
            )
            return
        try:
            order_info = client.put_limit_order(
                user_id=config['DISCOUNT_ADMIN_USER_ID'],
                account_id=SPOT_ACCOUNT_ID,
                market=activity.market,
                side=OrderSideType.BUY.value,
                amount=amount_to_str(remain_amount),
                price=amount_to_str(target_price),
                taker_fee_rate=str(0),
                maker_fee_rate=str(0),
                source='system',
                fee_asset=activity.PAY_ASSET,
                fee_discount=str(0),
            )
        except Exception as e:
            current_app.logger.error("discount activity put order error: %s", e)
            return

        sys_order = DiscountActivitySystemOrder(
            discount_activity_id=activity.id,
            market=activity.market,
            source_asset=activity.PAY_ASSET,
            source_asset_amount=source_asset_amount,
            target_asset=activity.asset,
            target_asset_amount=remain_amount,
            source_asset_traded_amount=0,
            target_asset_traded_amount=0,
            price=target_price,
            timeout_at=now_ + timedelta(seconds=50),
            order_id=order_info["id"],
        )
        db.session.add(sys_order)
        db.session.commit()


def cancel_activity_order(order_id):
    sys_order = DiscountActivitySystemOrder.query.filter(
        DiscountActivitySystemOrder.order_id == order_id,
        DiscountActivitySystemOrder.status == DiscountActivitySystemOrder.Status.PROCESSING,
    ).first()
    if not sys_order:
        return
    client = ServerClient()
    order_info = client.finished_order_detail(
        user_id=config['DISCOUNT_ADMIN_USER_ID'],
        order_id=sys_order.order_id,
    )
    if order_info:  # 完成了
        sys_order.source_asset_traded_amount = quantize_amount(
            Decimal(order_info["deal_money"]), 8, ROUND_UP)
        sys_order.target_asset_traded_amount = quantize_amount(
            Decimal(order_info["deal_stock"]), 8, ROUND_UP)
        sys_order.result = DiscountActivitySystemOrder.Result.ALL
        sys_order.status = DiscountActivitySystemOrder.Status.FINISHED
    else:
        if not sys_order.is_timeout:
            return
        send_alert_notice(
            f"Dibs购币订单超时，已取消订单并在一分钟后尝试再次挂单，"
              f"超时订单买入价：{sys_order.price} {sys_order.source_asset}，"
              f"数量{sys_order.target_asset_amount} {sys_order.target_asset}",
            config["ADMIN_CONTACTS"]["discount_activity_order_notice"]
        )
        order_info = client.cancel_user_order(
            user_id=config['DISCOUNT_ADMIN_USER_ID'],
            market=sys_order.market,
            order_id=sys_order.order_id,
        )
        sys_order.source_asset_traded_amount = quantize_amount(
            Decimal(order_info["deal_money"]), 8, ROUND_UP)
        sys_order.target_asset_traded_amount = quantize_amount(
            Decimal(order_info["deal_stock"]), 8, ROUND_UP)
        if sys_order.source_asset_traded_amount >= sys_order.source_asset_amount:
            sys_order.result = DiscountActivitySystemOrder.Result.ALL
        elif sys_order.source_asset_traded_amount == 0:
            sys_order.result = DiscountActivitySystemOrder.Result.FAILED
        else:
            sys_order.result = DiscountActivitySystemOrder.Result.PARTIAL
        sys_order.status = DiscountActivitySystemOrder.Status.FINISHED
    db.session.commit()


@scheduled(crontab(minute="*/1"))
@lock_call()
def put_activity_balance_order_schedule():
    """ Dibs活动开始前10分钟自动在admin账户购买对应数量的币种 """
    now_ = now()
    activity_query = DiscountActivity.query.filter(
        DiscountActivity.status == DiscountActivity.StatusType.ONLINE,
    ).all()
    client = ServerClient()
    balances = client.get_user_balances(config['DISCOUNT_ADMIN_USER_ID'])
    available_balance_map = {k: v['available'] for k, v in balances.items()}
    processing_list = []
    for activity in activity_query:
        # 只考虑活动开始10分钟前到活动结束的活动
        if not (activity.start_time-timedelta(minutes=10) <= now_ <= activity.end_time):
            continue
        # 如果余额足够扣款就不挂单
        if available_balance_map[activity.asset] >= activity.total_amount:
            available_balance_map[activity.asset] -= activity.total_amount
            continue
        processing_list.append(activity.id)
        sys_order = DiscountActivitySystemOrder.query.filter(
            DiscountActivitySystemOrder.discount_activity_id == activity.id,
            DiscountActivitySystemOrder.status == DiscountActivitySystemOrder.Status.PROCESSING,
        ).first()
        if not sys_order:
            put_activity_order(activity.id)
        else:
            cancel_activity_order(sys_order.order_id)
            put_activity_order(activity.id)

    # 关闭下架或者完成时可能存在未取消的订单
    need_cancel_order_list = DiscountActivitySystemOrder.query.filter(
        DiscountActivitySystemOrder.discount_activity_id.notin_(processing_list),
        DiscountActivitySystemOrder.status == DiscountActivitySystemOrder.Status.PROCESSING,
    ).all()
    for order in need_cancel_order_list:
        cancel_activity_order(order.order_id)


@scheduled(crontab(minute="*/1"))
@lock_call()
def update_discount_activity_price_schedule():
    # 活动10分钟前以第10分钟1分钟维度k线收盘价作为价格
    now_ = now()
    start_time = now_
    end_time = start_time + timedelta(minutes=10)
    activity_query = DiscountActivity.query.filter(
        DiscountActivity.status == DiscountActivity.StatusType.ONLINE,
        DiscountActivity.start_time >= start_time,
        DiscountActivity.start_time <= end_time,
    ).all()
    client = ServerClient()
    need_flash = False
    for row in activity_query:
        _datetime = row.start_time - timedelta(minutes=10)
        ts = int(_datetime.timestamp())
        klines = client.market_kline(
                                    market=row.market, start_time=ts,
                                    end_time=ts, interval=60)
        if not klines and not row.price:  # 如果无k线数据，用最新成交价代替
            price = client.market_last(row.market)
        else:
            price = Decimal(klines[-1][2])
        if row.price != price:  # 存在k线还没完全生成，收盘价非最终收盘价的情况
            row.price = price
            db.session.commit()
            need_flash = True
    if need_flash:
        DiscountActivityDetailCache.reload()


@celery_task
@lock_call(with_args='activity_id')
def send_discount_activity_reward_schedule(activity_id):
    success_user_list = []
    fail_user_list = []

    activity = DiscountActivity.query.filter(
        DiscountActivity.id == activity_id,
    ).first()

    if activity.active_status != DiscountActivity.ActiveStatus.PROCESSED:
        raise InvalidArgument(message=f"仅抽签中的活动可操作")

    lottery_query = DiscountActivityLotteryHistory.query.filter(
        DiscountActivityLotteryHistory.discount_activity_id == activity_id,
        DiscountActivityLotteryHistory.status != DiscountActivityLotteryHistory.StatusType.CREATED,
    ).all()

    order_list = DiscountActivityOrder.query.filter(
        DiscountActivityOrder.discount_activity_id == activity_id,
        DiscountActivityOrder.status == DiscountActivityOrder.StatusType.VALID,
    ).all()
    lottery_user_ids = {i.user_id for i in lottery_query if
                        i.actual_status == DiscountActivityLotteryHistory.StatusType.SUCCEED}

    if not lottery_query:
        raise InvalidArgument(message=f"没完成hash抽奖")
    for order in order_list:
        if order.user_id in lottery_user_ids:
            db.session.add(
                GiftHistory(
                    user_id=order.user_id,
                    activity_id=activity.activity_id,
                    asset=activity.asset,
                    amount=activity.amount,
                    remark='dibs activity lottery',
                    status=GiftHistory.Status.CREATED
                ))
            db.session.add(
                DiscountActivityRewardHistory(
                    user_id=order.user_id,
                    discount_activity_id=activity_id,
                    asset=activity.asset,
                    amount=activity.amount
                ))
            success_user_list.append(order.user_id)
        order.status = DiscountActivityOrder.StatusType.FINISHED
    db.session.commit()
    print('update_gift_history_task')
    update_gift_history_task(activity.activity_id, BalanceBusiness.DIBS_ACTIVITY_LOTTERY.value,
                             wait=False, pay_from_admin_user_id=config['DISCOUNT_ADMIN_USER_ID'])
    print('process_refund_order')
    client = ServerClient(current_app.logger)
    # 处理部分中签的退款逻辑
    for order in order_list:
        pay_asset = DiscountActivity.PAY_ASSET
        if order.user_id not in lottery_user_ids:
            total_pay_amount = activity.pay_amount
            client.add_user_balance(
                user_id=config['DISCOUNT_ADMIN_USER_ID'],
                asset=pay_asset,
                amount=amount_to_str(-total_pay_amount, 8),
                business=BalanceBusiness.DIBS_ACTIVITY_CANCEL,
                business_id=order.id,
            )
            client.add_user_balance(
                user_id=order.user_id,
                asset=pay_asset,
                amount=amount_to_str(total_pay_amount, 8),
                business=BalanceBusiness.DIBS_ACTIVITY_CANCEL,
                business_id=order.id,
            )
            fail_user_list.append(order.user_id)
    activity.status = DiscountActivity.StatusType.FINISHED
    db.session.commit()
    update_discount_activity_cache_schedule.delay()
    send_discount_activity_notice(activity_id, success_user_list, fail_user_list)


def send_discount_activity_notice(activity_id, success_user_list, fail_user_list):
    SITE_URL = "https://support.coinex.com/hc/articles/28475039680665"
    activity = DiscountActivity.query.filter(
        DiscountActivity.id == activity_id,
    ).first()

    lang_title_mapper = {
        lang.value: title for lang, title in DiscountActivityDetail.query.filter(
            DiscountActivityDetail.discount_activity_id == activity_id
        ).with_entities(
            DiscountActivityDetail.lang,
            DiscountActivityDetail.title
        ).all()
    }
    message_popup_expired_at = now() + timedelta(days=3)

    def get_send_message_title(lang_title_mapper, lang):
        from app.common import Language
        if lang in lang_title_mapper:
            title = lang_title_mapper[lang]
        else:
            title = lang_title_mapper[Language.DEFAULT.value]
        return title

    for user_id in success_user_list:
        pref = UserPreferences(user_id)
        lang = pref.language.value
        title = get_send_message_title(lang_title_mapper, lang)
        message = Message(
            user_id=user_id,
            title=MessageTitle.DIBS_REWORDS_SUCCESS.name,
            content=MessageContent.ACTIVITY_DIBS_LOTTERY_SUCCESS.name,
            params=json.dumps({
                'amount': amount_to_str(activity.amount, 8),
                'asset': activity.asset,
                'title': title,
            }),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.DIBS_ACTIVITY_DETAIL_PAGE.value.format(activity_id=activity_id),
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            expired_at=message_popup_expired_at,
            channel=Message.Channel.ACTIVITY,
        )
        db.session.add(message)
        send_dibs_reword_email(
            user_id, amount_to_str(activity.amount, 8), activity.asset, title, SITE_URL,
            'dibs_rewords_success', lang)
    for user_id in fail_user_list:
        pref = UserPreferences(user_id)
        lang = pref.language.value
        title = get_send_message_title(lang_title_mapper, lang)
        message = Message(
            user_id=user_id,
            title=MessageTitle.DIBS_REWORDS_FAIL.name,
            content=MessageContent.ACTIVITY_DIBS_REWARDS_FAIL.name,
            params=json.dumps({
                'title': title,
                'site_url': SITE_URL,
            }),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.DIBS_ACTIVITY_DETAIL_PAGE.value.format(activity_id=activity_id),
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            expired_at=message_popup_expired_at,
            channel=Message.Channel.ACTIVITY,
        )
        db.session.add(message)
        send_dibs_reword_email(
            user_id, activity.amount, activity.asset, title, SITE_URL, 'dibs_rewords_fail',
            lang)
    db.session.commit()
