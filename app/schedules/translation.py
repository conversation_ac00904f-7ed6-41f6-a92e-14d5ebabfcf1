from collections import defaultdict
import json
from datetime import timedelta
from typing import List
from celery.schedules import crontab
from flask import current_app
from app.business.clients.ai_translate import AITranslateClient
from app.business.lock import lock_call
from app.common.constants import CeleryQueues, Language
from app.models.mongo.insight import (
    CoinExInsightContentMySQL as CoinExInsightContent,
    ArticleType,
)
from app.models.mongo.translation import TranslationTaskMySQL
from app.models.quotes import CoinInformationTrans
from ..business.academy import AcademyBusiness
from ..business.insight import get_participles
from ..models import db, KlineAnalysisContent, AirdropActivityDetail, AirdropActivityQuestionBank, Blog, BlogCategory
from ..models.academy import AcademyArticleContent
from ..utils import scheduled, route_module_to_celery_queue, celery_task, now

route_module_to_celery_queue(__name__, CeleryQueues.TRANSLATION)


@scheduled(crontab(minute='*/5'))
@lock_call()
def check_translation():
    records: List[TranslationTaskMySQL] = list(TranslationTaskMySQL.query.filter_by(
        status=TranslationTaskMySQL.Status.CREATED
    ).order_by(TranslationTaskMySQL.created_at.desc()).limit(1000))

    translator = AITranslateClient()

    if len(records) == 0:
        return

    current_app.logger.warning(f"Handle translation task start: {len(records)}, {records[0].id},"
                               f"oldest record date: {records[-1].created_at}")

    try:
        # 记录翻译结果
        completed_tasks = []
        for task in reversed(records):
            Status = TranslationTaskMySQL.Status
            task: TranslationTaskMySQL
            data = translator.get_async_result(task.task_id)
            if not data:
                continue
            if data.status == Status.CREATED.name or data.status == Status.PROCESSING.name:
                continue

            if data.status == Status.FAILED.name:
                task.status = TranslationTaskMySQL.Status.FAILED
                db.session.commit()
                continue

            task.status = TranslationTaskMySQL.Status.FINISHED
            task.input_tokens = data.input_tokens
            task.output_tokens = data.output_tokens
            task.content = data.content
            db.session.commit()
            try:
                process_business_task(task)
                completed_tasks.append(task)
            except Exception as e:
                task.status = TranslationTaskMySQL.Status.FAILED
                db.session.commit()
                current_app.logger.error(f"Failed to process task {task.id}: {e}")

        current_app.logger.warning(f"Handle translation task complete: "
                                   f"{len(completed_tasks)}, {completed_tasks[0].id if len(completed_tasks) > 0 else ''}")
    except Exception as e:
        current_app.logger.exception(f"Handle translation task error: {e}")


# 业务处理
def process_business_task(task):
    business = task.business
    task_id = str(task.mongo_id)
    if business == TranslationTaskMySQL.Business.INSIGHT:
        insight_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.KLINE_ANALYSIS:
        kline_analysis_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.AIRDROP:
        airdrop_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.AIRDROP_QUESTION:
        airdrop_question_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.ACADEMY:
        academy_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.COIN_INFO:
        coin_info_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.BLOG:
        blog_translated.delay(task_id)
    else:
        current_app.logger.warning(f"Handle translation task warning unknown business: {business}")


def _extract_task_with_content(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()

    business_info = task.business_info
    if business_info:
        raise ValueError(f"Old async trans task, with business_info: {business_info}")

    try:
        return task, json.loads(task.content)
    except json.JSONDecodeError:
        raise ValueError(f"Failed to parse task content in JSON format: {task.content}")


def handle_translation_error(func):
    """
    捕获翻译业务处理中的异常，并将任务状态更新为失败
    """
    from functools import wraps

    @wraps(func)
    def wrapper(task_id, *args, **kwargs):
        try:
            return func(task_id, *args, **kwargs)
        except Exception as e:
            # 获取任务更新为失败
            task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
            if task:
                task.status = TranslationTaskMySQL.Status.FAILED
                task.save()

            current_app.logger.error(f"{func.__name__} 处理失败，task_id: {task_id}, 错误: {str(e)}")

            raise

    return wrapper


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def insight_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)

    business_record = CoinExInsightContent.get_or_create(task.business_id, task.target)

    # 一些字段如果为空，需要从英文版拷贝过来
    need_copy_fields = ['cover']
    if business_record.article_type == ArticleType.REPORT:
        need_copy_fields.append('report')
    source_content = None
    for need_copy_field in need_copy_fields:
        if getattr(business_record, need_copy_field):
            continue
        if not source_content:
            source_content = CoinExInsightContent.query.filter_by(
                insight_id=task.business_id,
                lang=task.source
            ).first()  # 在循环内获取英文版内容，是因为正常情况下，应该不需要复制
        if not source_content:
            # 英文版内容如果不存在，说明数据有问题
            current_app.logger.error(f"Failed to find {task.source} content for insight: {task.business_id}")
            continue
        setattr(business_record, need_copy_field, getattr(source_content, need_copy_field))

    translatedFields = ['title', 'abstract', 'content_html']
    for key, value in task_content.items():
        if key in translatedFields:
            setattr(business_record, key, value)

    participle = get_participles(Language[task.target], business_record.title, business_record.content_html)
    business_record.content_text = participle['text']
    business_record.participles = participle['participles']
    business_record.title_participles = participle['title_participles']
    business_record.content_participles = participle['content_participles']

    db.session.commit()
    current_app.logger.warning(f'insight task complete: {task_id}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def academy_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)
    current_app.logger.warning(f'academy task start: {task_id}, {task_content}')

    business_record: AcademyArticleContent = AcademyArticleContent.get_or_create_by_article(task.business_id,
                                                                                            task.target)

    AcademyBusiness.update_parsed_result(business_record, Language[task.target], task_content)
    db.session.commit()
    current_app.logger.warning(f'academy task complete: {task_id}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def airdrop_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)

    business_record = AirdropActivityDetail.get_or_create(
        auto_commit=True,
        airdrop_activity_id=task.business_id,
        lang=task.target
    )

    for key, value in task_content.items():
        if key in ['title', 'summary', 'introductions']:
            setattr(business_record, key, value)
        else:
            current_app.logger.error(f"Unknown key in task content: {key}. task.content: {task.content}")
            continue

    db.session.commit()
    current_app.logger.warning(f'airdrop task complete: {task_id}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def airdrop_question_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)

    business_record = AirdropActivityQuestionBank.query.get(task.business_id)

    for key, value in task_content.items():
        if key in ['question', 'answer', 'answer_analysis', 'A', 'B', 'C']:
            if key in ['A', 'B', 'C']:
                options = json.loads(business_record.options) or {}
                options[key] = value
                business_record.options = json.dumps(options)
            elif key in ['question', 'answer', 'answer_analysis']:
                setattr(business_record, key, value)
            else:
                current_app.logger.error(f"Unknown key in task content: {key}. task.content: {task.content}")
                continue

    db.session.commit()
    current_app.logger.warning(f'airdrop_question task complete: {task_id}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def kline_analysis_translated(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
    content = task.content
    analysis_id = task.business_id
    lang = task.target
    content = KlineAnalysisContent(
        analysis_id=analysis_id,
        lang=lang,
        text=content,
        input_token_count=task.input_tokens,
        output_token_count=task.output_tokens,
    )
    db.session.add(content)
    db.session.commit()


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def coin_info_translated(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
    content = task.content
    if not content:
        return
    content = json.loads(content)

    business_id = task.business_id
    lang = task.target
    record = CoinInformationTrans.get_or_create(
        coin_information_id=business_id,
        lang=lang
    )
    record.description = content.get('description', '')
    record.is_auto_translation = True
    introduce_map = defaultdict(lambda: defaultdict(str))

    # 发送给翻译服务的字段content是平铺的，而存储的字段introduces是一个列表，在此处把字典还原成列表
    for k, v in content.items():
        if k.startswith('transIntroduces'):
            _, field, index = k.split(':')
            introduce_map[int(index)][field] = v
    max_index = max(introduce_map)
    introduces = []
    for i in range(max_index + 1):
        introduces.append(introduce_map[i])
    record.introduces = json.dumps(introduces)
    db.session_add_and_commit(record)


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def blog_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)
    current_app.logger.warning(f'blog translation task start: {task_id}')

    # 获取原始博客
    source_blog = Blog.query.get(task.business_id)
    if not source_blog:
        current_app.logger.error(f"Source blog not found: {task.business_id}")
        return

    # 获取源博客的分类信息
    source_category = BlogCategory.query.get(source_blog.category_id) if source_blog.category_id else None

    # 查找目标语言中与源分类备注相同的分类
    target_category_id = None  # 默认设置为无分类
    if source_category:
        target_category = BlogCategory.query.filter(
            BlogCategory.status != BlogCategory.Status.DELETED,
            BlogCategory.lang == task.target,
            BlogCategory.remark == source_category.remark
        ).first()
        if target_category:
            target_category_id = target_category.id

    # 检查目标语言的博客是否已存在
    existing_blog = Blog.query.filter_by(
        blueprint_id=source_blog.id if not source_blog.blueprint_id else source_blog.blueprint_id,
        lang=task.target,
    ).first()

    if existing_blog:
        # 如果已存在，则更新内容
        blog = existing_blog
        blog.status = Blog.Status.DRAFT
        blog.category_id = target_category_id  # 更新为找到的对应分类
        # 只有在目标博客封面为空时才继承源博客封面
        if not existing_blog.cover:
            existing_blog.cover = source_blog.cover if source_blog.cover else ""
        if not existing_blog.app_cover:
            existing_blog.app_cover = source_blog.app_cover if source_blog.app_cover else ""
        current_app.logger.warning(f'Updating existing blog: {blog.id}')
    else:
        # 创建新博客
        blog = Blog(
            remark=source_blog.remark,
            lang=task.target,
            category_id=target_category_id,  # 使用找到的对应分类
            published_at=source_blog.published_at,
            status=Blog.Status.DRAFT,  # 翻译后的博客默认为草稿状态
            seo_url_keyword=source_blog.seo_url_keyword,
            seo_title=source_blog.seo_title,
            title="",
            content="",
            abstract="",
            draft_title="",
            draft_content="",
            draft_abstract="",
            cover=source_blog.cover if source_blog.cover else "",
            app_cover=source_blog.app_cover if source_blog.app_cover else "",
            is_blueprint=False,  # 翻译后的博客不是主文章
            blueprint_id=source_blog.id if not source_blog.blueprint_id else source_blog.blueprint_id,
            is_top=source_blog.is_top,
            last_updated_at=now(),
        )
        db.session.add(blog)
        current_app.logger.warning(f'Creating new blog for translation')

    # 根据原博客的状态更新翻译后的博客
    if source_blog.status == Blog.Status.DRAFT:
        # 如果原博客是草稿，更新草稿字段
        blog.draft_title = task_content.get('title', '')
        blog.draft_abstract = task_content.get('abstract', '')
        blog.draft_content = task_content.get('content', '')
    else:
        # 如果原博客已发布，同时更新正式字段和草稿字段
        blog.draft_title = task_content.get('title', '')
        blog.draft_abstract = task_content.get('abstract', '')
        blog.draft_content = task_content.get('content', '')

        # 已发布的博客也需要更新正式字段，但保持草稿状态
        blog.title = task_content.get('title', '')
        blog.abstract = task_content.get('abstract', '')
        blog.content = task_content.get('content', '')

    # 更新最后更新时间
    blog.last_updated_at = now()

    db.session.commit()
    current_app.logger.warning(f'blog translation task complete: {task_id}, blog_id: {blog.id}')


@scheduled(crontab(minute=20, hour='7'))
@lock_call(with_args=False, wait=True)
def clear_old_data_schedule():
    cutoff_date = now() - timedelta(days=7)
    delete_count = TranslationTaskMySQL.query.filter(
        TranslationTaskMySQL.created_at <= cutoff_date
    ).delete()

    db.session.commit()
    current_app.logger.warning(f"Clear old translation tasks: [{delete_count}]")
