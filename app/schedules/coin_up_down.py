import datetime
from decimal import Decimal
from enum import Enum
from functools import cached_property
from typing import List

from app import Language
from app.business.site import BusinessSettings
from app.caches import MarketCache
from app.business.email import send_internal_user_email, \
    send_new_coin_up_down_notice_email
from app.business import route_module_to_celery_queue, CeleryQueues, scheduled, \
    crontab, PriceManager, ServerClient, lock_call
from app.models import CoinInformation, AssetPriceKline, PeriodType
from app.utils import last_month, format_percent, amount_to_str

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


@scheduled(crontab(minute=30, hour=1, day_of_month=1))
@lock_call()
def push_coin_up_down_monthly():
    end_dt = datetime.datetime.now()
    last_month_date = last_month(end_dt.year, end_dt.month)
    start_dt = datetime.datetime(last_month_date.year, last_month_date.month, 1)
    CoinUpDownInPeriod(start_dt, end_dt).push_to_users(CoinUpDownInPeriod.PushPeriod.MONTHLY)


@scheduled(crontab(minute=30, hour=1, day_of_week=1))
@lock_call()
def push_coin_up_down_weekly():
    end_dt = datetime.datetime.now()
    start_dt = end_dt - datetime.timedelta(days=7)
    CoinUpDownInPeriod(start_dt, end_dt).push_to_users(CoinUpDownInPeriod.PushPeriod.WEEKLY)


@scheduled(crontab(day_of_month=1, hour=0, minute=30))
@lock_call()
def push_new_coin_up_down_monthly():
    end_dt = datetime.datetime.now()
    last_month_date = last_month(end_dt.year, end_dt.month)
    start_dt = datetime.datetime(last_month_date.year, last_month_date.month, 1)
    data = CoinUpDownInPeriod(start_dt, end_dt).get_new_coin_in_period_data()
    emails = BusinessSettings.new_coin_monthly_emails
    for email in emails:
        send_new_coin_up_down_notice_email.delay(
            email=email,
            subject=f'CoinEx 新币涨幅榜({start_dt.date()}~{end_dt.date()})',
            lang=Language.ZH_HANS_CN.value,
            items=data,
            month=start_dt.strftime('%Y-%m')
        )


class CoinUpDownInPeriod:

    class PushPeriod(Enum):
        WEEKLY = '每周'
        MONTHLY = '每月'

    def __init__(self, start_dt: datetime.datetime, end_dt: datetime.datetime):
        start_ts = int(start_dt.timestamp())
        end_ts = int(end_dt.timestamp())
        self.start_ts = start_ts - start_ts % (60 * 60 * 24)
        self.end_ts = end_ts - end_ts % (60 * 60 * 24)  # 今日零点

        self.start_dt = datetime.datetime.fromtimestamp(self.start_ts)
        self.end_dt = datetime.datetime.fromtimestamp(self.end_ts)

        self.push_period = None

    def push_to_users(self, push_period: PushPeriod):
        self.set_push_period(push_period)
        emails = BusinessSettings.coin_rate_emails
        for email in emails:
            send_internal_user_email.delay(
                email=email,
                email_content=self.email_content,
                subject=f'CoinEx 币种涨跌幅数据报告({self.start_dt.date()}~{self.end_dt.date()})',
                lang=Language.ZH_HANS_CN.value
            )

    def set_push_period(self, push_period: PushPeriod):
        self.push_period = push_period

    @cached_property
    def email_content(self) -> str:
        if self.push_period == self.PushPeriod.WEEKLY:
            period_desc = f'上周'
        elif self.push_period == self.PushPeriod.MONTHLY:
            period_desc = f'上月'
        else:
            period_desc = ''

        content = f"""
        <p>Dear all：</p>
        <p>以下是CoinEx {period_desc} ({self.start_dt.date()}~{self.end_dt.date()}) 币种数据</p>
        <p>1.涨幅前十币种</p>
        {self.content_top10()}
        <p>2.上线币种</p>
        {self.content_online()}
        """
        return content

    def content_top10(self):
        return self._assemble_up_down_content(self.top(top_count=10))

    def content_online(self):
        return self._assemble_up_down_content(self.online_in_period())

    def top(self, top_count: int) -> dict:
        kline_objs = AssetPriceKline.query.with_entities(
            AssetPriceKline.time,
            AssetPriceKline.asset,
            AssetPriceKline.price,
        ).filter(
            AssetPriceKline.period == PeriodType.DAY,
            AssetPriceKline.time >= self.start_ts,
            AssetPriceKline.time <= self.end_ts,
        )
        asset_to_rate_mapping = self._get_asset_to_rate_mapping(kline_objs)
        top10_assets = sorted(
            list(asset_to_rate_mapping.keys()),
            key=lambda k: asset_to_rate_mapping[k],
            reverse=True
        )[:top_count]
        return {asset: asset_to_rate_mapping[asset] for asset in top10_assets}

    def online_in_period_daily_kline(self) -> List[AssetPriceKline]:
        objs = CoinInformation.query.with_entities(
            CoinInformation.code
        ).filter(
            CoinInformation.online_time >= self.start_dt,
            CoinInformation.online_time <= self.end_dt,
        ).all()
        codes = [obj.code for obj in objs]
        kline_objs = AssetPriceKline.query.with_entities(
            AssetPriceKline.time,
            AssetPriceKline.asset,
            AssetPriceKline.price,
        ).filter(
            AssetPriceKline.period == PeriodType.DAY,
            AssetPriceKline.asset.in_(codes),
            AssetPriceKline.time >= self.start_ts,
            AssetPriceKline.time <= self.end_ts,
        ).all()
        return kline_objs

    def online_in_period(self) -> dict:
        kline_objs = self.online_in_period_daily_kline()
        return self._get_asset_to_rate_mapping(kline_objs)

    def get_new_coin_in_period_data(self) -> List[dict]:
        info = CoinInformation.query.filter(
            CoinInformation.online_time >= self.start_dt,
            CoinInformation.online_time < self.end_dt
        ).with_entities(
            CoinInformation.code,
            CoinInformation.online_time
        ).all()
        client = ServerClient()
        res = []
        online_markets = set(MarketCache.list_online_markets())
        for item in info:
            market = f'{item.code}USDT'
            if market not in online_markets:
                continue
            klines = client.market_kline(market=f'{item.code}USDT',
                                         start_time=self.start_ts,
                                         end_time=self.end_ts, interval=86400)
            if not klines or not klines[0]:
                continue
            klines.sort(key=lambda t: t[0])
            open_price = Decimal(klines[0][1])
            close_price = Decimal(klines[-1][2])
            max_price = Decimal()
            for i in klines:
                max_price = max(max_price, Decimal(i[3]))
            rate = Decimal()
            if open_price != 0:
                rate = (max_price - open_price) / open_price
            res.append(dict(
                asset=item.code,
                open_price=amount_to_str(open_price, 8),
                close_price=amount_to_str(close_price, 8),
                max_price=amount_to_str(max_price, 8),
                max_rate=format_percent(rate, 4),
                date=item.online_time.strftime('%m-%d')
            ))
        res.sort(key=lambda x: x['date'], reverse=True)
        return res

    @staticmethod
    def _get_asset_to_rate_mapping(kline_objs):
        group_by_asset = {}
        for kline in kline_objs:
            group_by_asset.setdefault(kline.asset, []).append((kline.time, kline.price))

        asset_to_rate_mapping = {}
        for asset, time_prices in group_by_asset.items():
            price_now = PriceManager.asset_to_usd(asset)
            time_prices.sort(key=lambda t: t[0])
            _, price0 = time_prices[0]
            if len(time_prices) == 1:
                price1 = price_now
            else:
                _, price1 = time_prices[-1]

            rate = Decimal('0')
            if price0 != 0:
                rate = (price1 - price0) / price0
            asset_to_rate_mapping[asset] = rate
        return asset_to_rate_mapping

    @staticmethod
    def _assemble_up_down_content(coin_up_down_mapping: dict):
        # Done: add color style
        up_down_content = ''
        for coin, up_down in coin_up_down_mapping.items():
            if up_down >= 0:
                color = 'color:red'
            else:
                color = 'color:green'
            rate = '{:+.2%}'.format(up_down)
            up_down_content += f'<p style="width: 150px;"><span style="float:left;">{coin}</span>' \
                               f'&nbsp;&nbsp;&nbsp;&nbsp;' \
                               f'<span style="{color}; float:right;">{rate}</span></p>'

        up_down_content = up_down_content or '<p>暂无</p>'
        return up_down_content