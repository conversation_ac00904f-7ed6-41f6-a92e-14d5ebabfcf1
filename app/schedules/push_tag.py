from typing import Dict
from typing import List
from typing import Iterable

from collections import defaultdict

from flask import current_app

from celery.schedules import crontab

from app.common import CeleryQueues

from app.caches.push_tag import UpdatePushTagCache

from app.utils import scheduled
from app.utils import batch_iter
from app.utils import MobilePusher
from app.utils import route_module_to_celery_queue
from app.utils.push import PushTag, PushTagMutexDic

from app.business import lock_call
from app.business.user import UserRepository

route_module_to_celery_queue(__name__, CeleryQueues.PUSH_TAG)


def get_push_tag_data(tag: PushTag, channel_push_id_map: Dict, user_ids: Iterable[int]) -> List[Dict]:
    current_app.logger.info(f'get information push tag list...')

    user_tag_notice_dic = UserRepository.get_user_tag_notice_dic(user_ids, tag)

    add_list_key = 'add_push_ids'
    remove_list_key = 'remove_push_ids'
    lang_channel_map = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
    for (channel, push_id), data in channel_push_id_map.items():
        user_id = data['user_id']
        event = data['event']
        lang = data['lang']

        notice_enable = user_tag_notice_dic.get(user_id, '1') == '1'
        tag_name = '{}_{}'.format(tag.value, lang.value)
        list_key = add_list_key if event == UpdatePushTagCache.Event.REPORT and notice_enable else remove_list_key

        lang_channel_map[tag_name][channel][list_key].append(push_id)

    return [{
        'tag_name': tag_name,
        'channel': channel,
        'mutex': PushTagMutexDic[tag],
        'add_push_ids': list_item[add_list_key],
        'remove_push_ids': list_item[remove_list_key],
    } for tag_name, channel_item in lang_channel_map.items() for channel, list_item in channel_item.items()]


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_push_tag():
    # 从redis队列中读取等待更新推送Tag的用户信息(同一个用户重复的信息旧的会被新的覆盖)
    cache = UpdatePushTagCache()
    update_list = cache.lrange_all()
    for tag in PushTag:
        _update_push_tag(tag, update_list)
    current_app.logger.info(f'ltrim update queue...')
    cache.ltrim(len(update_list), -1)


def _update_push_tag(tag: PushTag, update_list: List):
    current_app.logger.info(f'loading {tag.value} update queue...')
    channel_push_id_map = {}
    user_ids = set()
    for item in update_list:
        channel_push_id_map[(item['channel'], item['push_id'])] = item
        if (user_id := item['user_id']) != 0:
            user_ids.add(user_id)
    if not channel_push_id_map:
        current_app.logger.info(f'empty queue.')
        return

    current_app.logger.info(f'get tag push tag list...')
    update_push_tag_list = []
    update_push_tag_list.extend(get_push_tag_data(tag, channel_push_id_map, user_ids))

    current_app.logger.info(f'update tag push ids...')
    mp = MobilePusher()
    for item in update_push_tag_list:
        tag_name = item['tag_name']
        channel = item['channel']
        mutex = item['mutex']
        for pids in batch_iter(item['add_push_ids'], 1000):
            mp.update_tag_push_ids(tag_name, channel, mutex, pids, [])
        for pids in batch_iter(item['remove_push_ids'], 1000):
            mp.update_tag_push_ids(tag_name, channel, mutex, [], pids)

