import json
import datetime
from collections import defaultdict
from decimal import Decimal

from app.business import TradeSummaryDB, db, \
    route_module_to_celery_queue, CeleryQueues, scheduled, crontab, lock_call
from app.business.market_maker import MarketMakerHelper
from app.caches.spot import MarketCache
from app.models import DailySpotTradeCoinReport, AssetPrice, \
    MonthlySpotTradeCoinReport
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import next_month

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def get_trade_summary_db():
    return TradeSummaryDB.db


def list_coin_trade_summary(date, trade_summary_db=None):
    sql = "SELECT stock_asset, money_asset, deal_amount, deal_user_list, deal_count, deal_volume, " \
          "taker_buy_amount, taker_sell_amount, taker_buy_count, taker_sell_count " \
          "FROM coin_trade_summary " \
          "WHERE trade_date = '{}' ".format(date)
    if not trade_summary_db:
        trade_summary_db = get_trade_summary_db()
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    return cursor.fetchall()


def list_user_trade_summary(date):
    date_str = date.strftime('%Y-%m-%d')
    month_str = date.strftime('%Y%m')
    sql = "SELECT user_id, stock_asset, money_asset, deal_volume " \
          "FROM user_trade_summary_{} " \
          "WHERE trade_date = '{}' ".format(month_str, date_str)
    trade_summary_db = TradeSummaryDB.db
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    return cursor.fetchall()


def update_daily_spot_trade_coin_report(report_date, force_update=False):
    if not TradeSummaryDB.is_data_completed(report_date):
        raise Exception('{} dump_history 数据还未同步完成'.format(report_date))
    coin_rate = AssetPrice.get_close_price_map(report_date)
    trade_summary_db = get_trade_summary_db()
    deal_count_map, user_map, deal_amount_map, deal_vol_usd_map = \
        defaultdict(int), defaultdict(set), defaultdict(Decimal), defaultdict(Decimal)
    taker_buy_amount_map, taker_sell_amount_map, taker_buy_count_map, taker_sell_count_map = \
        defaultdict(Decimal), defaultdict(Decimal), defaultdict(int), defaultdict(int)
    normal_user_deal_volumes = defaultdict(Decimal)
    user_trade_summary_list = list_user_trade_summary(report_date)
    maker_ids = MarketMakerHelper.list_all_maker_ids()
    for item in user_trade_summary_list:
        user_id, stock_asset, money_asset, deal_volume = item
        if deal_volume <= 0:
            continue
        if user_id in maker_ids:
            continue
        normal_user_deal_volumes[stock_asset] += deal_volume * coin_rate.get(money_asset, 0)
    user_fee_data = TradeSummaryDB.daily_trade_fee_list(report_date)
    market_map = dict()
    markets = {item['market'] for item in user_fee_data}
    for market in markets:
        market_map[market] = MarketCache(market).dict
    asset_fee_map = defaultdict(Decimal)
    normal_user_deal_fees = defaultdict(Decimal)
    for fee_ in user_fee_data:
        market_data = market_map[fee_['market']]
        asset_ = market_data['base_asset']
        asset_fee_map[asset_] += fee_['fee'] * coin_rate.get(fee_['asset'], 0)
        if fee_['user_id'] not in maker_ids:
            normal_user_deal_fees[asset_] += fee_['fee'] * coin_rate.get(fee_['asset'], 0)

    trade_summary_list = list_coin_trade_summary(report_date, trade_summary_db)
    for item in trade_summary_list:
        stock_asset, money_asset, deal_amount, deal_user_list, deal_count, deal_volume, \
            taker_buy_amount, taker_sell_amount, taker_buy_count, taker_sell_count = item
        deal_count_map[stock_asset] += deal_count
        user_map[stock_asset] |= set(json.loads(deal_user_list))
        deal_amount_map[stock_asset] += deal_amount
        deal_vol_usd_map[stock_asset] += deal_volume * coin_rate.get(money_asset, 0)
        taker_buy_amount_map[stock_asset] += taker_buy_amount
        taker_sell_amount_map[stock_asset] += taker_sell_amount
        taker_buy_count_map[stock_asset] += taker_buy_count
        taker_sell_count_map[stock_asset] += taker_sell_count

    for coin, deal_amount in deal_amount_map.items():
        record = DailySpotTradeCoinReport.get_or_create(report_date, coin)
        if not record.trade_amount or force_update:
            normal_volume_usd = normal_user_deal_volumes[coin]
            deal_volume_usd = deal_vol_usd_map[coin]
            normal_deal_rate = (normal_volume_usd / (deal_volume_usd * 2)) if deal_volume_usd else Decimal()
            normal_fee_usd = normal_user_deal_fees[coin]
            fee_usd = asset_fee_map[coin]
            normal_fee_usd_rate = (normal_fee_usd / fee_usd) if fee_usd else Decimal()
            record.trade_amount = deal_amount
            record.trade_usd = deal_vol_usd_map[coin]
            record.fee_usd = fee_usd
            record.deal_user_list = json.dumps(list(user_map[coin]))
            record.deal_user_count = len(user_map[coin])
            record.deal_count = deal_count_map[coin]
            record.taker_buy_amount = taker_buy_amount_map[coin]
            record.taker_sell_amount = taker_sell_amount_map[coin]
            record.taker_buy_count = taker_buy_count_map[coin]
            record.taker_sell_count = taker_sell_count_map[coin]
            record.normal_deal_volume_usd = normal_volume_usd
            record.normal_deal_rate = normal_deal_rate
            record.normal_fee_usd = normal_fee_usd
            record.normal_fee_usd_rate = normal_fee_usd_rate
            db.session.add(record)

    db.session.commit()


def update_monthly_spot_trade_coin_report(start_month, end_month, force_update=False):
    query = DailySpotTradeCoinReport.query.filter(
        DailySpotTradeCoinReport.report_date >= start_month,
        DailySpotTradeCoinReport.report_date < end_month
    )
    trade_amount_map, trade_usd_map, fee_usd_map, deal_user_map, deal_count_map = \
        defaultdict(Decimal), defaultdict(Decimal), defaultdict(Decimal), defaultdict(set), defaultdict(int)
    taker_buy_amount_map, taker_buy_count_map, taker_sell_amount_map, taker_sell_count_map = \
        defaultdict(Decimal), defaultdict(Decimal), defaultdict(Decimal), defaultdict(Decimal)
    normal_fee_usd_map, normal_deal_volume = defaultdict(Decimal), defaultdict(Decimal)
    for item in query:
        trade_amount_map[item.coin] += item.trade_amount
        trade_usd_map[item.coin] += item.trade_usd
        fee_usd_map[item.coin] += item.fee_usd
        deal_user_map[item.coin] |= set(json.loads(item.deal_user_list))
        deal_count_map[item.coin] += item.deal_count
        taker_buy_amount_map[item.coin] += item.taker_buy_amount
        taker_buy_count_map[item.coin] += item.taker_buy_count
        taker_sell_amount_map[item.coin] += item.taker_sell_amount
        taker_sell_count_map[item.coin] += item.taker_sell_count
        normal_deal_volume[item.coin] += item.normal_deal_volume_usd
        normal_fee_usd_map[item.coin] += item.normal_fee_usd

    for coin in trade_amount_map:
        record = MonthlySpotTradeCoinReport.get_or_create(start_month, coin)
        if not record.trade_usd or force_update:
            normal_deal_rate = (normal_deal_volume[coin] / (trade_usd_map[coin] * 2)) if (
                trade_usd_map)[coin] else Decimal()
            normal_fee_usd_rate = (normal_fee_usd_map[coin] / fee_usd_map[coin]) if (
                fee_usd_map)[coin] else Decimal()
            record.trade_amount = trade_amount_map[coin]
            record.trade_usd = trade_usd_map[coin]
            record.fee_usd = fee_usd_map[coin]
            record.deal_count = deal_count_map[coin]
            record.taker_buy_amount = taker_buy_amount_map[coin]
            record.taker_buy_count = taker_buy_count_map[coin]
            record.taker_sell_amount = taker_sell_amount_map[coin]
            record.taker_sell_count = taker_sell_count_map[coin]
            record.deal_user_list = json.dumps(list(deal_user_map[coin]))
            record.deal_user_count = len(deal_user_map[coin])
            record.normal_deal_volume_usd = normal_deal_volume[coin]
            record.normal_deal_rate = normal_deal_rate
            record.normal_fee_usd = normal_fee_usd_map[coin]
            record.normal_fee_usd_rate = normal_fee_usd_rate
            db.session.add(record)
    db.session.commit()


@scheduled(crontab(minute=15, hour='0-4'))
@lock_call()
def update_daily_spot_trade_coin_schedule(force_update=False):
    today = datetime.datetime.utcnow().date()
    last_record = DailySpotTradeCoinReport.query.order_by(
        DailySpotTradeCoinReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today + datetime.timedelta(days=-90)
    while start_date < today:
        update_daily_spot_trade_coin_report(start_date, force_update)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute=30, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_spot_trade_coin_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(
        MonthlySpotTradeCoinReport, DailySpotTradeCoinReport)
    if not start_month:
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_spot_trade_coin_report(start_month, end_month)
        start_month = end_month
