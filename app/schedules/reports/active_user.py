# -*- coding: utf-8 -*-
from datetime import timedelta

from celery.schedules import crontab
from sqlalchemy import func

from app.common import CeleryQueues
from app.business import lock_call, filter_active_users
from app.models import UserTradeSummary, \
    UserActivenessReport, db
from app.schedules.reports.utils import get_active_user_set
from app.utils import scheduled, today, route_module_to_celery_queue

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)

time_range = (1, 7, 30)


def update_user_activeness_report(end_date, force_update=True):
    record_list = []
    for range_ in UserActivenessReport.Range:
        start_date = end_date + timedelta(days=-range_.value)
        # active users
        curr_date = start_date
        active_users = set()
        active_record = UserActivenessReport.get_or_create(
            report_date=end_date, type=UserActivenessReport.Type.active, time_range=range_)

        while curr_date < end_date:
            user_ids = filter_active_users(curr_date, curr_date)
            active_users |= set(user_ids)
            active_users |= get_active_user_set(curr_date, curr_date+timedelta(days=1))
            curr_date += timedelta(days=1)
        active_record.count = len(active_users)

        # trade users
        trade_record = UserActivenessReport.get_or_create(
            report_date=end_date, type=UserActivenessReport.Type.trade, time_range=range_)
        trade_user_count = UserTradeSummary.query.filter(
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date < end_date
        ).with_entities(
            func.count(UserTradeSummary.user_id.distinct())
        ).scalar() or 0
        trade_record.count = trade_user_count
        # spot users
        spot_record = UserActivenessReport.get_or_create(
            report_date=end_date, type=UserActivenessReport.Type.spot, time_range=range_)
        spot_user_count = UserTradeSummary.query.filter(
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date < end_date,
            UserTradeSummary.system == UserTradeSummary.System.SPOT
        ).with_entities(
            func.count(UserTradeSummary.user_id.distinct())
        ).scalar() or 0
        spot_record.count = spot_user_count
        # perpetual users
        perpetual_record = UserActivenessReport.get_or_create(
            report_date=end_date, type=UserActivenessReport.Type.perpetual, time_range=range_)
        perpetual_user_count = UserTradeSummary.query.filter(
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date < end_date,
            UserTradeSummary.system == UserTradeSummary.System.PERPETUAL
        ).with_entities(
            func.count(UserTradeSummary.user_id.distinct())
        ).scalar() or 0
        perpetual_record.count = perpetual_user_count
        record_list.extend(
            [active_record, trade_record, spot_record, perpetual_record])
    if force_update:
        db.session.add_all(record_list)
        db.session.commit()


@scheduled(crontab(minute='*/15', hour='3-4'))
@lock_call()
def update_user_activeness_report_schedule(force_update=True):
    today_ = today()
    last_record = UserActivenessReport.query.order_by(
        UserActivenessReport.report_date.desc()
    ).first()
    if last_record:
        end_date = last_record.report_date + timedelta(days=1)
    else:
        end_date = today_

    curr_date = end_date
    while curr_date <= today_:
        update_user_activeness_report(curr_date, force_update)
        curr_date += timedelta(days=1)
