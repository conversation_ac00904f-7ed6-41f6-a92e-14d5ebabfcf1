#!/usr/bin/env python3
import datetime
import json
from collections import defaultdict
from decimal import Decimal

from celery.schedules import crontab
from flask import current_app

from dateutil.tz import UTC

from app.business import PerpetualSysHistoryDB, lock_call
from app.business.perpetual.position import get_adl_liq_positions
from app.caches import PerpetualMarketCache
from app.common import CeleryQueues, PositionSide
from app.models import db, MonthlyPerpetualMarginBurstReport, \
    DailyPerpetualMarginBurstReport, AssetPrice
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import route_module_to_celery_queue, scheduled, next_month

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def get_position_liquidation_summary(start_time: int, end_time: int):
    columns = ('market', 'liq_amount', 'user_id', 'side', 'insurance', 'liq_price', 'position_id')
    where = f' update_time >= {start_time} AND update_time < {end_time} '
    records = PerpetualSysHistoryDB.table('positionliq_history').select(
        *columns,
        where=where,
    )
    res = list(dict(zip(columns, record)) for record in records)
    for record in res:
        side = record['side']   # 仓位方向，1空仓 2多仓
        amount = record['liq_amount']
        short_burst_amount = long_burst_amount = 0
        if side == PositionSide.SHORT:   # 买入平空
            short_burst_amount += amount
        else:   # 卖出平多
            long_burst_amount += amount
        record['short_burst_amount'] = short_burst_amount
        record['long_burst_amount'] = long_burst_amount
        record['insurance'] = record['insurance']
        record['liq_price'] = record['liq_price']
        record['position_id'] = record['position_id']
    return res


def get_insurance_dict(start_time: int, end_time: int):

    columns = ('type', 'market', 'SUM(`change`)')
    where = f' time >= {start_time} AND time < {end_time} '
    records = PerpetualSysHistoryDB.table('insurance_history').select(
        *columns,
        where=where,
        group_by='`type`, `market`'
    )
    stash_dict = defaultdict(lambda: defaultdict(Decimal))
    for item in records:
        if int(item[0]) == 2:
            stash_dict[item[1]]['decrease_amount'] += item[2]
        elif int(item[0]) == 1:
            stash_dict[item[1]]['increase_amount'] += item[2]

    market_list = set(PerpetualMarketCache().get_market_list())
    return [dict(
            market_type=market,
            asset=PerpetualMarketCache.get_balance_asset(market),
            decrease_amount=stash_dict[market]['decrease_amount'],
            increase_amount=stash_dict[market]['increase_amount']
            ) for market in market_list]


def update_perpetual_burst_report(start_time, end_time, model_type='daily'):

    report_model = DailyPerpetualMarginBurstReport if model_type == 'daily' else MonthlyPerpetualMarginBurstReport

    _start_time = int(datetime.datetime(
        start_time.year, start_time.month, start_time.day, tzinfo=UTC
                 ).timestamp())

    _end_time = int(datetime.datetime(
        end_time.year, end_time.month, end_time.day, tzinfo=UTC
    ).timestamp())

    insurance_results = get_insurance_dict(
        _start_time, _end_time)

    liquidation_market_map = defaultdict(lambda: {
        'user_list': list(),
        'long_user_list': list(),
        'liq_position_list': set(),
        'cross_liq_position_list': set(),
        'cross_liq_amount': Decimal(),
        'auto_deleverage_list': set(),
        'auto_deleverage_amount': Decimal(),
        'liq_amount': Decimal(),
        'short_burst_amount': Decimal(),
        'long_burst_amount': Decimal(),
        'short_burst_count': Decimal(),
        'long_burst_count': Decimal(),
    })

    summary_list = get_position_liquidation_summary(
        _start_time, _end_time)

    user_position_map = defaultdict(list)
    for row in summary_list:
        user_position_map[row['user_id']].append(row['position_id'])

    adl_liq_positions = get_adl_liq_positions(_start_time, _end_time, user_position_map)

    for item in summary_list:
        liquidation_market_map[item['market']]['user_list'].append(item['user_id'])
        if item['side'] == PositionSide.LONG:
            liquidation_market_map[item['market']]['long_user_list'].append(item['user_id'])
            liquidation_market_map[item['market']]['long_burst_count'] += 1
        else:
            liquidation_market_map[item['market']]['short_burst_count'] += 1
        liquidation_market_map[item['market']]['liq_amount'] += item['liq_amount']
        liquidation_market_map[item['market']]['short_burst_amount'] += item[
            'short_burst_amount']
        liquidation_market_map[item['market']]['long_burst_amount'] += item[
            'long_burst_amount']
        liquidation_market_map[item['market']]['liq_position_list'].add(item['position_id'])
        if item['insurance'] < 0:  # insurance < 0表示穿仓
            liquidation_market_map[item['market']]['cross_liq_amount'] += item['liq_amount']
            liquidation_market_map[item['market']]['cross_liq_position_list'].add(item['position_id'])
        if item['position_id'] in adl_liq_positions:
            liquidation_market_map[item['market']]['auto_deleverage_amount'] += item['liq_amount']
            liquidation_market_map[item['market']]['auto_deleverage_list'].add(item['position_id'])

    asset_price = AssetPrice.get_close_price_map(start_time)

    asset_price['USD'] = 1

    for item in insurance_results:
        market = item['market_type']
        _user_set = set(liquidation_market_map[market]['user_list'])
        _long_user_set = set(liquidation_market_map[market]['long_user_list'])
        record = report_model.query.filter(
            report_model.report_date == start_time,
            report_model.market == market,
        ).first()
        if not record:
            record = report_model()
            record.report_date = start_time
            record.market = market
        asset = PerpetualMarketCache.get_amount_asset(market)
        record.interest_insurance_amount = item['increase_amount']
        record.interest_insurance_usd = item['increase_amount'] * asset_price.get(item['asset'], 0)
        record.liquidation_insurance_amount = item['decrease_amount']
        record.liquidation_insurance_usd = item['decrease_amount'] * asset_price.get(item['asset'], 0)
        record.user_count = len(_user_set)
        record.user_list = json.dumps(list(_user_set))
        record.long_user_count = len(_long_user_set)
        record.long_user_list = json.dumps(list(_long_user_set))
        record.burst_count = len(liquidation_market_map[market]['user_list']) # 总的爆仓笔数
        record.burst_amount = liquidation_market_map[market]['liq_amount']
        record.burst_usd = liquidation_market_map[market]['liq_amount'] * asset_price.get(asset, 0)
        record.short_burst_amount = liquidation_market_map[market]['short_burst_amount']
        record.short_burst_usd = liquidation_market_map[market]['short_burst_amount'] * asset_price.get(asset, 0)
        record.short_burst_count = liquidation_market_map[market]['short_burst_count']  # 空单爆仓笔数
        record.long_burst_amount = liquidation_market_map[market]['long_burst_amount']
        record.long_burst_usd = liquidation_market_map[market]['long_burst_amount'] * asset_price.get(asset, 0)
        record.long_burst_count = liquidation_market_map[market]['long_burst_count']    # 多单爆仓笔数

        # 有穿仓的爆仓市值cross_liq_amount

        record.cross_liq_amount = liquidation_market_map[market]['cross_liq_amount']
        record.cross_liq_usd = liquidation_market_map[market]['cross_liq_amount'] * asset_price.get(asset, 0)
        record.cross_liq_count = len(liquidation_market_map[market]['cross_liq_position_list'])
        record.auto_deleverage_amount = liquidation_market_map[market]['auto_deleverage_amount']
        record.auto_deleverage_usd = liquidation_market_map[market]['auto_deleverage_amount'] * asset_price.get(asset, 0)
        record.auto_deleverage_count = len(liquidation_market_map[market]['auto_deleverage_list'])

        db.session.add(record)

    db.session.commit()


@scheduled(crontab(minute=20, hour='0-4'))
@lock_call()
def update_daily_margin_schedule():
    today = datetime.datetime.utcnow().date()
    last_record = DailyPerpetualMarginBurstReport.query.order_by(
        DailyPerpetualMarginBurstReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = datetime.date(2019, 6, 1)
    while start_date < today:
        try:
            end_date = start_date + datetime.timedelta(days=1)
            update_perpetual_burst_report(start_date, end_date)
        except Exception as ex:
            db.session.rollback()
            current_app.logger.exception(ex)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute=30, hour='0-4', day_of_month=1))
@lock_call()
def update_monthly_margin_schedule():

    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(
        MonthlyPerpetualMarginBurstReport, DailyPerpetualMarginBurstReport)

    if not start_month:
        return

    try:
        while start_month < cur_month:
            end_month = next_month(start_month.year, start_month.month)
            update_perpetual_burst_report(start_month, end_month, 'monthly')
            start_month = end_month
    except Exception as ex:
        db.session.rollback()
        current_app.logger.exception(ex)
