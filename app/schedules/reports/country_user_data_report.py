import datetime
from collections import defaultdict
from decimal import Decimal
from typing import Union, Set, Dict, Iterable, List

from dateutil.relativedelta import relativedelta
from flask import current_app

from app import config
from app.business.market_maker import MarketMakerHelper
from app.business.user import UserRepository
from app.business.user_tag.helper import get_disabled_user_ids
from app.caches.flow_control import EmailCountryCountCache, SmsCountryCountCache
from app.common.countries import list_country_codes_3, AREAS_MAPPING
from pyroaring import BitMap
from sqlalchemy import func
from app.common import Language, get_country
from app.business import (
    route_module_to_celery_queue,
    CeleryQueues,
    scheduled,
    crontab,
    lock_call,
    filter_active_users, PriceManager,
)
from app.business.statistic import group_asset_users, get_user_balances
from app.models import (
    db,
    User,
    DailyCountryUserReport,
    UserTradeSummary,
    MonthlyCountryUserReport,
    SubAccount,
    DailyLanguageUserReport,
    MonthlyLanguageUserReport,
    Ambassador,
    BusinessAmbassador,
    DailyCountryReachReport,
    MonthlyCountryReachReport,
    DailyChannelReachReport,
    MonthlyChannelReachReport,
    ReferralHistory, MarginReceivableInterestHistory, UserLiquidity,
    DailyAssetUserHoldingReport, DailyCountryTradeReport, DailyLanguageTradeReport,
    MonthlyCountryTradeReport, MonthlyLanguageTradeReport, UserTradeFeeSummary, DailyAreaUserReport,
    MonthlyAreaUserReport, DailyAreaTradeReport, MonthlyAreaTradeReport,
    UserExchangeSummary, QuarterlyCountryTradeReport, QuarterlyLanguageTradeReport,
    QuarterlyCountryUserReport, QuarterlyLanguageUserReport, QuarterlyAreaTradeReport,
    QuarterlyAreaUserReport
)
from app.models import UserPreference as UserPreferenceModel
from app.caches import EmailSendCountCache, SmsSendHourCountCache, SmsCountryResultCache, SmsCountryResultSetCache
from app.schedules.reports.utils import get_monthly_report_date, get_active_user_set
from app.utils import batch_iter, group_by, mobile_country_code_to_countries
from app.utils.date_ import date_to_datetime, datetime_to_time, last_month, next_month, today
from app.utils.sms import SMSProvider

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)

MAX_BALANCE = Decimal(999999999999999999)


def get_trade_user_set(start_time, end_time, search_type=None):
    _query = UserTradeSummary.query.filter(
        UserTradeSummary.report_date >= start_time,
        UserTradeSummary.report_date < end_time,
    ).with_entities(
        UserTradeSummary.user_id.distinct().label('user_id')
    )
    if search_type == 'spot':
        _query = _query.filter(
            UserTradeSummary.system == UserTradeSummary.System.SPOT)
    elif search_type == 'perpetual':
        _query = _query.filter(
            UserTradeSummary.system == UserTradeSummary.System.PERPETUAL)
    elif search_type == 'exchange':
        _query = UserExchangeSummary.query.filter(
            UserExchangeSummary.report_date >= start_time,
            UserExchangeSummary.report_date < end_time,
        ).with_entities(
            UserExchangeSummary.user_id.distinct().label('user_id')
        )
    return {item.user_id for item in _query}


def _get_user_items(start_time, end_time):
    user_items = User.query.filter(
        User.created_at < end_time,
        User.created_at >= start_time,
        User.user_type != User.UserType.SUB_ACCOUNT,
    ).with_entities(
        User.id
    ).all()
    return {item.id for item in user_items}


def get_user_location_code(user_ids):
    user_data = []
    for ids_ in batch_iter(user_ids, 1000):
        data = User.query.filter(
            User.id.in_(ids_)
        ).with_entities(User.id, User.location_code).all()
        user_data.extend(data)
    return user_data


def get_cet_new_user_set(start_time, end_time):
    increase_cet_user_bit = DailyAssetUserHoldingReport.query.filter(
        DailyAssetUserHoldingReport.report_date >= start_time,
        DailyAssetUserHoldingReport.report_date < end_time, DailyAssetUserHoldingReport.asset == 'CET'
    ).all()
    res = set()
    for users in increase_cet_user_bit:
        res |= set(BitMap.deserialize(users.user_bit_increase))
    return res


def get_referree_user_referral_history_rows(user_ids: Iterable[int]) -> List[ReferralHistory]:
    # 获取新注册(被邀请)用户的邀请关系
    history_rows = []
    for chunk_user_ids in batch_iter(user_ids, 1000):
        chunk_rows = ReferralHistory.query.filter(
            ReferralHistory.referree_id.in_(chunk_user_ids),
            ReferralHistory.status == ReferralHistory.Status.VALID,
        ).all()
        history_rows.extend(chunk_rows)
    return history_rows


def _get_margin_user_set(start_time, end_time):
    margin_user_items = MarginReceivableInterestHistory.query.filter(
        MarginReceivableInterestHistory.created_at >= start_time,
        MarginReceivableInterestHistory.created_at < end_time
    ).with_entities(
        MarginReceivableInterestHistory.user_id.distinct().label("user_id"))

    return {item.user_id for item in margin_user_items}


def _get_amm_user_set():
    amm_user_query = UserLiquidity.query.filter(UserLiquidity.liquidity > 0)\
        .with_entities(UserLiquidity.user_id.distinct().label('user_id'))
    return {item.user_id for item in amm_user_query}


def _get_invalid_user_set():
    disabled_user_ids = get_disabled_user_ids()
    sub_user_ids = {r.user_id for r in SubAccount.query.with_entities(SubAccount.user_id).all()}
    return disabled_user_ids | sub_user_ids

def update_country_user_report(start_time, end_time, report_model):
    if not UserTradeSummary.check_data_ready(start_time):
        current_app.logger.warning("{} update_country_user_report-UserTradeSummary 数据未就绪".format(start_time))
        return

    spot_trade_user_set = get_trade_user_set(start_time, end_time, 'spot')
    perpetual_trade_user_set = get_trade_user_set(start_time, end_time, 'perpetual')
    exchange_trade_user_set = get_trade_user_set(start_time, end_time, 'exchange')
    trade_user_set = spot_trade_user_set | perpetual_trade_user_set | exchange_trade_user_set
    if not trade_user_set:
        # server快照出现问题则不生成对应数据
        return
    market_maker_ids = MarketMakerHelper.list_all_maker_ids()
    market_maker_ids = list(set(market_maker_ids) | set(UserRepository.merge_sub_account_into_main_ids(config['ASSET_REPORT_IGNORE_USERS'],
                                                                                                       sub_account_type=SubAccount.Type.NORMAL)))
    spot_trade_user_set -= set(market_maker_ids)
    perpetual_trade_user_set -= set(market_maker_ids)
    exchange_trade_user_set -= set(market_maker_ids)
    trade_user_set -= set(market_maker_ids)
    margin_user_set = _get_margin_user_set(start_time, end_time)
    margin_user_set -= set(market_maker_ids)
    amm_user_set = _get_amm_user_set()
    amm_user_set -= set(market_maker_ids)
    new_user_set = _get_user_items(start_time, end_time)
    new_user_set -= set(market_maker_ids)
    invalid_user_set = _get_invalid_user_set()
    exclude_user_set = invalid_user_set | set(market_maker_ids)
    daily_active_user = filter_active_users(start_time, end_time - datetime.timedelta(days=1))
    daily_active_user = set(daily_active_user) | get_active_user_set(start_time, end_time)
    daily_active_user -= set(exclude_user_set)

    r = group_asset_users(datetime_to_time(end_time))
    asset_user_set = set(r['ALL'])
    asset_user_set -= set(market_maker_ids)

    # 截止到当天/月的总用户数
    location_user_count_map = defaultdict(int)
    user_items = User.query.filter(
        User.created_at < end_time,
        User.user_type != User.UserType.SUB_ACCOUNT,
    ).with_entities(
        User.id,
        User.location_code,
    ).all()
    for i in user_items:
        if i.id not in exclude_user_set:
            location_user_count_map[i.location_code] += 1

    # 截止到当天/月的新增交易用户
    chunk_size = 1000
    before_trade_user_id_set = set()
    for chunk_user_ids in batch_iter(trade_user_set, chunk_size):
        chunk_before_trade_user_ids = (
            UserTradeSummary.query.filter(
                UserTradeSummary.report_date < start_time,
                UserTradeSummary.user_id.in_(chunk_user_ids),
            )
            .with_entities(UserTradeSummary.user_id.distinct().label("user_id"))
            .all()
        )
        before_trade_user_id_set.update({item.user_id for item in chunk_before_trade_user_ids})
    increase_trade_user_id_set = trade_user_set - before_trade_user_id_set

    # 截止到当天/月的大使用户数
    ambassador_list = Ambassador.query.filter(
        Ambassador.status == Ambassador.Status.VALID,
        Ambassador.created_at < end_time,
    ).all()
    ambassador_dict = {i.user_id: i for i in ambassador_list}
    ambassador_user_id_set = {i.user_id for i in ambassador_list}
    bus_amb_rows = BusinessAmbassador.query.filter(
        BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
        BusinessAmbassador.created_at < end_time,
    ).all()
    bus_amb_dict = {i.user_id: i for i in bus_amb_rows}
    ambassador_user_id_set |= set(bus_amb_dict)
    ambassador_user_id_set -= set(market_maker_ids)

    # 大使邀请的注册用户,普通邀请注册用户
    amb_new_user_id_set, norm_new_user_id_set = set(), set()
    referral_history_rows = get_referree_user_referral_history_rows(new_user_set)
    for ref_row in referral_history_rows:
        if ref_row.referrer_id in ambassador_dict:
            ambassador_info = ambassador_dict[ref_row.referrer_id]
            if ref_row.effected_at >= ambassador_info.effected_at:
                amb_new_user_id_set.add(ref_row.referree_id)
        elif ref_row.referrer_id in bus_amb_dict:
            bus_amb = bus_amb_dict[ref_row.referrer_id]
            if ref_row.effected_at >= bus_amb.effected_at:
                amb_new_user_id_set.add(ref_row.referree_id)
        else:
            norm_new_user_id_set.add(ref_row.referree_id)
    # 新增CET用户
    cet_new_user_set = get_cet_new_user_set(start_time, end_time)
    cet_new_user_set -= set(market_maker_ids)

    user_location_map = defaultdict(
        lambda: {
            "active_user_count": 0,
            "trade_user_count": 0,
            "new_user_count": 0,
            "asset_user_count": 0,
            "total_user_count": 0,
            "increase_trade_user_count": 0,
            "ambassador_user_count": 0,
            "ambassador_refer_new_user_count": 0,
            "normal_refer_new_user_count": 0,
            "self_reg_new_user_count": 0,
            "spot_trade_user_count": 0,
            "perpetual_trade_user_count": 0,
            "exchange_trade_user_count": 0,
            "margin_user_count": 0,
            "amm_user_count": 0,
            "cet_new_user_count": 0,
            "balance": 0,
        }
    )
    new_user_data = get_user_location_code(new_user_set)
    for _user in new_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        if _user.id not in invalid_user_set:
            user_location_map[location]['new_user_count'] += 1
        if _user.id in amb_new_user_id_set:
            user_location_map[location]["ambassador_refer_new_user_count"] += 1
        elif _user.id in norm_new_user_id_set:
            user_location_map[location]['normal_refer_new_user_count'] += 1
        else:
            user_location_map[location]['self_reg_new_user_count'] += 1
    active_user_data = get_user_location_code(daily_active_user)
    for _user in active_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['active_user_count'] += 1

    spot_trade_user_data = get_user_location_code(spot_trade_user_set)
    for _user in spot_trade_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['spot_trade_user_count'] += 1
    perpetual_trade_user_data = get_user_location_code(perpetual_trade_user_set)
    for _user in perpetual_trade_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['perpetual_trade_user_count'] += 1
    exchange_trade_user_data = get_user_location_code(exchange_trade_user_set)
    for _user in exchange_trade_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['exchange_trade_user_count'] += 1
    trade_user_data = get_user_location_code(trade_user_set)
    for _user in trade_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['trade_user_count'] += 1

    cet_new_user_data = get_user_location_code(cet_new_user_set)
    for _user in cet_new_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['cet_new_user_count'] += 1
    margin_user_data = get_user_location_code(margin_user_set)
    for _user in margin_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['margin_user_count'] += 1
    amm_user_data = get_user_location_code(amm_user_set)
    for _user in amm_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['amm_user_count'] += 1
    asset_user_data = get_user_location_code(asset_user_set)
    for _user in asset_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['asset_user_count'] += 1
    for _location, _count in location_user_count_map.items():
        location = c.cn_name if (c := get_country(_location)) else "其他"
        user_location_map[location]["total_user_count"] += _count
    increase_trade_user_data = get_user_location_code(increase_trade_user_id_set)
    for _user in increase_trade_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else "其他"
        user_location_map[location]["increase_trade_user_count"] += 1
    ambassador_user_data = get_user_location_code(ambassador_user_id_set)
    for _user in ambassador_user_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else "其他"
        user_location_map[location]["ambassador_user_count"] += 1
    user_sum_balances = _get_user_balance_usd_map(end_time, market_maker_ids)
    user_balance_data = get_user_location_code(user_sum_balances.keys())
    for _user in user_balance_data:
        location = c.cn_name if (c := get_country(_user.location_code)) else '其他'
        user_location_map[location]['balance'] += user_sum_balances.get(_user.id, 0)

    for _country, _data in user_location_map.items():
        record = report_model.get_or_create(report_date=start_time, country=_country)
        record.active_user_count = _data['active_user_count']
        record.trade_user_count = _data['trade_user_count']
        record.new_user_count = _data['new_user_count']
        record.asset_user_count = _data['asset_user_count']
        record.total_user_count = _data["total_user_count"]
        record.increase_trade_user_count = _data["increase_trade_user_count"]
        record.ambassador_user_count = _data["ambassador_user_count"]
        record.ambassador_refer_new_user_count = _data["ambassador_refer_new_user_count"]
        record.normal_refer_new_user_count = _data['normal_refer_new_user_count']
        record.self_reg_new_user_count = _data['self_reg_new_user_count']
        record.spot_trade_user_count = _data['spot_trade_user_count']
        record.perpetual_trade_user_count = _data['perpetual_trade_user_count']
        record.exchange_trade_user_count = _data['exchange_trade_user_count']
        record.margin_user_count = _data['margin_user_count']
        record.amm_user_count = _data['amm_user_count']
        record.cet_new_user_count = _data['cet_new_user_count']
        record.balance = min(_data['balance'], MAX_BALANCE)
        db.session.add(record)
    if report_model is DailyCountryUserReport:
        area_report_model = DailyAreaUserReport
    elif report_model is MonthlyCountryUserReport:
        area_report_model = MonthlyAreaUserReport
    elif report_model is QuarterlyCountryUserReport:
        area_report_model = QuarterlyAreaUserReport
    country_area_mapping = {
        m.info.cn_name: k
        for k, v in AREAS_MAPPING.items() for m in v
    }
    area_map = defaultdict(lambda: defaultdict(Decimal))
    columns = ['active_user_count',
               'trade_user_count',
               'new_user_count',
               'asset_user_count',
               'total_user_count',
               'increase_trade_user_count',
               'ambassador_user_count',
               'ambassador_refer_new_user_count',
               'self_reg_new_user_count',
               'normal_refer_new_user_count',
               'spot_trade_user_count',
               'perpetual_trade_user_count',
               'exchange_trade_user_count',
               'margin_user_count',
               'amm_user_count',
               'cet_new_user_count',
               'balance',
               ]
    for _country, _data in user_location_map.items():
        if _country not in country_area_mapping:
            continue
        _area = country_area_mapping[_country]
        for column in columns:
            _add_value = _data.get(column, Decimal())
            area_map[_area][column] += _add_value
    for area_row, area_data in area_map.items():
        record = area_report_model.get_or_create(report_date=start_time,
                                                 area=area_row.name)
        for column in columns:
            setattr(record, column, area_data[column])
        if record.balance > MAX_BALANCE:
            record.balance = MAX_BALANCE
        db.session.add(record)
    db.session.commit()


def _get_user_balance_usd_map(end_time, market_maker_ids):
    market_maker_ids_set = set(market_maker_ids)
    ret = defaultdict(Decimal)
    user_balances = get_user_balances(datetime_to_time(end_time))
    prices = PriceManager.assets_to_usd()
    for asset, users in user_balances.items():
        for user_id, balance in users.items():
            if user_id in market_maker_ids_set:
                continue
            ret[user_id] += prices.get(asset, 0) * balance
    return ret


@scheduled(crontab(minute=30, hour='1-5'))
@lock_call()
def update_daily_country_user_data_schedule():
    """ 报表-国际化报表-国家分布日报, 地区分布日报 """
    today_ = datetime.datetime.utcnow().date()
    last_record = DailyCountryUserReport.query.order_by(
        DailyCountryUserReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today_ + datetime.timedelta(days=-7)
    while start_date < today_:
        end_date = start_date + datetime.timedelta(days=1)
        update_country_user_report(start_date, end_date, DailyCountryUserReport)
        start_date = end_date


@scheduled(crontab(minute=20, hour='2-3'))
@lock_call()
def update_monthly_country_user_data_schedule():
    """ 报表-国际化报表-国家分布月报 """
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(MonthlyCountryUserReport, DailyCountryUserReport, True)
    if not start_month:
        return

    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_country_user_report(start_month, end_month, MonthlyCountryUserReport)
        start_month = end_month


@scheduled(crontab(minute=20, hour='2-3'))
@lock_call()
def update_quarterly_country_user_data_schedule():
    """ 报表-国际化报表-国家分布季报 """
    last_record = QuarterlyCountryUserReport.query.order_by(
        QuarterlyCountryUserReport.report_date.desc()
    ).first()
    if last_record:
        start_month = last_record.report_date
    else:
        start_month = datetime.date(2023, 1, 1)
    today_ = today()
    cur_month = datetime.date(today_.year, today_.month, 1)

    while start_month <= cur_month:
        end_month = datetime.date(start_month.year, start_month.month, 1) + relativedelta(months=3)
        update_country_user_report(start_month, end_month, QuarterlyCountryUserReport)
        start_month = end_month


def get_user_language_map(user_ids: Set[int]) -> Dict[int, str]:
    user_language_map = {}
    for ids in batch_iter(user_ids, 1000):
        # some user_id maybe not existed
        users = (
            User.query.filter(User.id.in_(ids)).with_entities(User.id).all()
        )
        ids = [i.id for i in users]
        preferences = (
            UserPreferenceModel.query.filter(
                UserPreferenceModel.user_id.in_(ids),
                UserPreferenceModel.key == "language",
                UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
            )
            .with_entities(
                UserPreferenceModel.user_id, UserPreferenceModel.value
            )
            .all()
        )
        user_language_map.update({i.user_id: i.value for i in preferences})
    return user_language_map


def update_language_user_report(
    start_date: datetime.date,
    end_date: datetime.date,
    report_model: Union[
        DailyLanguageUserReport.__class__, MonthlyLanguageUserReport.__class__
    ],
) -> None:
    if not UserTradeSummary.check_data_ready(start_date):
        current_app.logger.warning("{} update_language_user_report-UserTradeSummary 数据未就绪".format(start_date))
        return
    spot_trade_user_set = get_trade_user_set(start_date, end_date, 'spot')
    perpetual_trade_user_set = get_trade_user_set(start_date, end_date, 'perpetual')
    exchange_trade_user_set = get_trade_user_set(start_date, end_date, 'exchange')
    trade_user_id_set = spot_trade_user_set | perpetual_trade_user_set | exchange_trade_user_set
    if not trade_user_id_set:
        # server快照出现问题则不生成对应数据
        return
    market_maker_ids = MarketMakerHelper.list_all_maker_ids()
    market_maker_ids = list(set(market_maker_ids) | set(UserRepository.merge_sub_account_into_main_ids(config['ASSET_REPORT_IGNORE_USERS'],
                                                                                                       sub_account_type=SubAccount.Type.NORMAL)))
    spot_trade_user_set -= set(market_maker_ids)
    perpetual_trade_user_set -= set(market_maker_ids)
    exchange_trade_user_set -= set(market_maker_ids)
    trade_user_id_set -= set(market_maker_ids)
    invalid_user_set = _get_invalid_user_set()
    exclude_user_set = invalid_user_set | set(market_maker_ids)
    new_user_id_set = _get_user_items(start_date, end_date)
    new_user_id_set -= set(exclude_user_set)
    daily_active_user_id_set = filter_active_users(start_date, end_date - datetime.timedelta(days=1))
    daily_active_user_id_set = set(daily_active_user_id_set) | get_active_user_set(start_date, end_date)
    daily_active_user_id_set -= set(exclude_user_set)
    asset_user_dict = group_asset_users(datetime_to_time(end_date))
    asset_user_id_set = set(asset_user_dict["ALL"])
    asset_user_id_set -= set(market_maker_ids)

    margin_user_set = _get_margin_user_set(start_date, end_date)
    amm_user_set = _get_amm_user_set()
    margin_user_set -= set(market_maker_ids)
    amm_user_set -= set(market_maker_ids)

    # 截止到当天/月的总用户数
    user_count = 0
    user_items = User.query.filter(
        User.created_at < end_date,
        User.user_type != User.UserType.SUB_ACCOUNT,
    ).with_entities(
        User.id,
    ).all()
    for i in user_items:
        if i.id not in exclude_user_set:
            user_count += 1

    user_max_id = (
        User.query.filter(
            User.created_at < end_date,
        )
        .with_entities(func.max(User.id))
        .scalar()
    )
    language_total_users = (
        UserPreferenceModel.query.filter(
            UserPreferenceModel.user_id <= user_max_id,
            UserPreferenceModel.key == "language",
            UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
        )
        .with_entities(
            UserPreferenceModel.value,
            UserPreferenceModel.user_id
        )
        .all()
    )
    language_total_user_count_map = defaultdict(int)
    for i in language_total_users:
        if i.user_id not in exclude_user_set:
            language_total_user_count_map[i.value] += 1
    other_lang_count = user_count - sum(language_total_user_count_map.values())
    if other_lang_count > 0:
        language_total_user_count_map[Language.EN_US.name] += other_lang_count

    # 截止到当天/月的新增交易用户
    chunk_size = 1000
    before_trade_user_id_set = set()
    for chunk_user_ids in batch_iter(trade_user_id_set, chunk_size):
        chunk_before_trade_user_ids = (
            UserTradeSummary.query.filter(
                UserTradeSummary.report_date < start_date,
                UserTradeSummary.user_id.in_(chunk_user_ids),
            )
            .with_entities(UserTradeSummary.user_id.distinct().label("user_id"))
            .all()
        )
        before_trade_user_id_set.update({item.user_id for item in chunk_before_trade_user_ids})
    increase_trade_user_id_set = trade_user_id_set - before_trade_user_id_set

    # 截止到当天/月的大使用户数
    ambassador_list = Ambassador.query.filter(
        Ambassador.status == Ambassador.Status.VALID,
        Ambassador.created_at < end_date,
    ).all()
    ambassador_dict = {i.user_id: i for i in ambassador_list}
    ambassador_user_id_set = {i.user_id for i in ambassador_list}
    bus_amb_rows = BusinessAmbassador.query.filter(
        BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
        BusinessAmbassador.created_at < end_date,
    ).all()
    bus_amb_dict = {i.user_id: i for i in bus_amb_rows}
    ambassador_user_id_set |= set(bus_amb_dict)
    ambassador_user_id_set -= set(market_maker_ids)

    # 大使邀请的注册用户
    amb_new_user_id_set, norm_new_user_id_set = set(), set()
    referral_history_rows = get_referree_user_referral_history_rows(new_user_id_set)
    for ref_row in referral_history_rows:
        if ref_row.referrer_id in ambassador_dict:
            ambassador_info = ambassador_dict[ref_row.referrer_id]
            if ref_row.effected_at >= ambassador_info.effected_at:
                amb_new_user_id_set.add(ref_row.referree_id)
        elif ref_row.referrer_id in bus_amb_dict:
            bus_amb = bus_amb_dict[ref_row.referrer_id]
            if ref_row.effected_at >= bus_amb.effected_at:
                amb_new_user_id_set.add(ref_row.referree_id)
        else:
            norm_new_user_id_set.add(ref_row.referree_id)

    self_reg_new_user_set = new_user_id_set - (amb_new_user_id_set | norm_new_user_id_set)
    # 新增CET用户
    cet_new_user_set = get_cet_new_user_set(start_date, end_date)
    cet_new_user_set -= set(market_maker_ids)

    filed_name_user_ids_map = {
        "active_user_count": daily_active_user_id_set,
        "trade_user_count": trade_user_id_set,
        "new_user_count": new_user_id_set,
        "asset_user_count": asset_user_id_set,
        "increase_trade_user_count": increase_trade_user_id_set,
        "ambassador_user_count": ambassador_user_id_set,
        "ambassador_refer_new_user_count": amb_new_user_id_set,
        "normal_refer_new_user_count": norm_new_user_id_set,
        "self_reg_new_user_count": self_reg_new_user_set,
        "spot_trade_user_count": spot_trade_user_set,
        "perpetual_trade_user_count": perpetual_trade_user_set,
        "exchange_trade_user_count": exchange_trade_user_set,
        "margin_user_count": margin_user_set,
        "amm_user_count": amm_user_set,
        "cet_new_user_count": cet_new_user_set,
    }
    valid_languages = [i.name for i in Language]
    language_user_count_map = {lang: {
        "active_user_count": 0,
        "trade_user_count": 0,
        "new_user_count": 0,
        "asset_user_count": 0,
        "total_user_count": 0,
        "increase_trade_user_count": 0,
        "ambassador_user_count": 0,
        "ambassador_refer_new_user_count": 0,
        "normal_refer_new_user_count": 0,
        "self_reg_new_user_count": 0,
        "spot_trade_user_count": 0,
        "perpetual_trade_user_count": 0,
        "exchange_trade_user_count": 0,
        "margin_user_count": 0,
        "amm_user_count": 0,
        "cet_new_user_count": 0,
        "balance": 0,
    } for lang in valid_languages}

    all_user_ids = set()
    for user_set in filed_name_user_ids_map.values():
        all_user_ids.update(user_set)
    user_language_map = get_user_language_map(all_user_ids)

    for key, user_ids_set in filed_name_user_ids_map.items():
        for user_id in user_ids_set:
            language = user_language_map.get(user_id)
            if not language:
                language = Language.EN_US.name
            language_user_count_map[language][key] += 1
    for lang, count in language_total_user_count_map.items():
        language_user_count_map[lang]["total_user_count"] += count
    user_sum_balances = _get_user_balance_usd_map(end_date, market_maker_ids)
    for user_id, balance_usd in user_sum_balances.items():
        language = user_language_map.get(user_id) or Language.EN_US.name
        language_user_count_map[language]['balance'] += balance_usd
    for language, count_data in language_user_count_map.items():
        record = report_model.get_or_create(report_date=start_date, language=language)
        record.active_user_count = count_data["active_user_count"]
        record.trade_user_count = count_data["trade_user_count"]
        record.new_user_count = count_data["new_user_count"]
        record.asset_user_count = count_data["asset_user_count"]
        record.total_user_count = count_data["total_user_count"]
        record.increase_trade_user_count = count_data["increase_trade_user_count"]
        record.ambassador_user_count = count_data["ambassador_user_count"]
        record.ambassador_refer_new_user_count = count_data["ambassador_refer_new_user_count"]
        record.normal_refer_new_user_count = count_data['normal_refer_new_user_count']
        record.self_reg_new_user_count = count_data['self_reg_new_user_count']
        record.spot_trade_user_count = count_data['spot_trade_user_count']
        record.perpetual_trade_user_count = count_data['perpetual_trade_user_count']
        record.exchange_trade_user_count = count_data['exchange_trade_user_count']
        record.margin_user_count = count_data['margin_user_count']
        record.amm_user_count = count_data['amm_user_count']
        record.cet_new_user_count = count_data['cet_new_user_count']
        record.balance = min(count_data['balance'], MAX_BALANCE)
        db.session.add(record)
    db.session.commit()


@scheduled(crontab(minute=30, hour="1-5"))
@lock_call()
def update_daily_language_user_data_schedule():
    """ 报表-国际化报表-语言分布日报 """
    today_ = today()
    last_record = DailyLanguageUserReport.query.order_by(
        DailyLanguageUserReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today_ + datetime.timedelta(days=-7)
    while start_date < today_:
        end_date = start_date + datetime.timedelta(days=1)
        update_language_user_report(start_date, end_date, DailyLanguageUserReport)
        start_date = end_date


@scheduled(crontab(minute=50, hour="4-5"))
@lock_call()
def update_monthly_language_user_data_schedule():
    """ 报表-国际化报表-语言分布月报 """
    today_ = today()
    cur_month = datetime.date(today_.year, today_.month, 1)
    start_month = get_monthly_report_date(MonthlyLanguageUserReport, DailyLanguageUserReport, True)
    if not start_month:
        return
    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_language_user_report(start_month, end_month, MonthlyLanguageUserReport)
        start_month = end_month


@scheduled(crontab(minute=50, hour="4-5"))
@lock_call()
def update_quarterly_language_user_data_schedule():
    """ 报表-国际化报表-语言分布季报 """
    last_record = QuarterlyLanguageUserReport.query.order_by(
        QuarterlyLanguageUserReport.report_date.desc()
    ).first()
    if last_record:
        start_month = last_record.report_date
    else:
        start_month = datetime.date(2023, 1, 1)
    today_ = today()
    cur_month = datetime.date(today_.year, today_.month, 1)
    while start_month <= cur_month:
        end_month = datetime.date(start_month.year, start_month.month, 1) + relativedelta(months=3)
        update_language_user_report(start_month, end_month, QuarterlyLanguageUserReport)
        start_month = end_month


def update_daily_country_notice_report(start_date: datetime.date, end_date: datetime.date):
    start_ts = datetime_to_time(start_date)
    end_ts = datetime_to_time(end_date)

    # email send count
    email_count_map = defaultdict(int)
    cur_ts = start_ts
    while cur_ts < end_ts:
        email_send_cache = EmailSendCountCache(cur_ts)
        for email, count in email_send_cache.hgetall().items():
            email_count_map[email] += int(count)
        cur_ts += EmailSendCountCache.INTERVAL

    email_country_map = defaultdict(int)
    for chunk_emails in batch_iter(email_count_map.keys(), 1000):
        rows_ = (
            User.query.filter(
                User.email.in_(chunk_emails),
            )
            .with_entities(
                User.email,
                User.location_code,
            )
            .all()
        )
        email_country_map.update(dict(rows_))

    country_email_count_map, user_email_count_map = defaultdict(int), defaultdict(int)
    
    this_month_ = start_date.replace(day=1)
    for email, count in email_count_map.items():
        country = email_country_map.get(email) or "其他"
        if country == "CHN":
            # 排除国内的邮箱
            continue
        country_email_count_map[country] += count
        user_email_count_map[country] += 1
        EmailCountryCountCache(country, int(date_to_datetime(this_month_).timestamp())).pfadd(email)
    
    # sms send count
    user_sms_count_map = defaultdict(set)
    country_sms_count_map = defaultdict(int)
    temp_ts = start_ts
    for _ in range(24):
        sms_send_cache = SmsSendHourCountCache(temp_ts)
        for full_mobile, count in sms_send_cache.hgetall().items():
            country_code, mobile_number = full_mobile.split(":")
            country_code = int(country_code)
            # 排除国内短信
            if country_code == 86:
                continue

            if c := mobile_country_code_to_countries(country_code):
                country_sms_count_map[c[0].iso_3] += int(count)
                user_sms_count_map[c[0].iso_3].add(mobile_number)
                SmsCountryCountCache(c[0].iso_3, int(date_to_datetime(this_month_).timestamp())).pfadd(full_mobile)
        temp_ts += 3600

    countries = set(country_email_count_map) | set(country_sms_count_map)
    for c in countries:
        c_str = c or "其他"
        row = DailyCountryReachReport.get_or_create(report_date=start_date, country=c_str)
        row.email_send_count = country_email_count_map[c]
        row.sms_send_count = country_sms_count_map[c]
        row.email_user_count = user_email_count_map[c]
        row.sms_user_count = len(user_sms_count_map[c])
        db.session.add(row)

    all_row = DailyCountryReachReport.get_or_create(report_date=start_date, country="")
    all_row.email_send_count = sum(country_email_count_map.values())
    all_row.sms_send_count = sum(country_sms_count_map.values())
    all_row.email_user_count = sum(user_email_count_map.values())
    all_row.sms_user_count = sum([len(item) for item in user_sms_count_map.values()])
    db.session.add(all_row)
    db.session.commit()


@scheduled(crontab(minute="20,30,40", hour="1"))
@lock_call()
def update_daily_country_notice_report_schedule():
    """ 报表-短信、邮件通知次数-日报 """
    today_ = today()
    last_record = DailyCountryReachReport.query.order_by(DailyCountryReachReport.report_date.desc()).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today_ - datetime.timedelta(days=1)

    while start_date < today_:
        end_date = start_date + datetime.timedelta(days=1)
        update_daily_country_notice_report(start_date, end_date)
        start_date = end_date


def update_monthly_country_notice_report(start_date: datetime.date, end_date: datetime.date):
    daily_rows: List[DailyCountryReachReport] = DailyCountryReachReport.query.filter(
        DailyCountryReachReport.report_date >= start_date,
        DailyCountryReachReport.report_date < end_date,
    ).all()

    data_template = {
        "email_send_count": 0,
        "sms_send_count": 0,
    }
    month_data_map = defaultdict(lambda: dict(data_template))
    for row in daily_rows:
        data = month_data_map[row.country]
        data["email_send_count"] += row.email_send_count
        data["sms_send_count"] += row.sms_send_count
    ts = int(date_to_datetime(start_date).timestamp())

    email_user_count_map, sms_user_count_map = dict(), dict()
    for country in month_data_map:
        email_user_count_map[country] = EmailCountryCountCache(country, ts).pfcount()
        sms_user_count_map[country] = SmsCountryCountCache(country, ts).pfcount()
    email_user_count_map[''] = sum(email_user_count_map.values())
    sms_user_count_map[''] = sum(sms_user_count_map.values())
    for country, data in month_data_map.items():
        row = MonthlyCountryReachReport.get_or_create(report_date=start_date, country=country)
        row.email_send_count = data["email_send_count"]
        row.sms_send_count = data["sms_send_count"]
        row.email_user_count = email_user_count_map[country]
        row.sms_user_count = sms_user_count_map[country]
        db.session.add(row)
    db.session.commit()
    # 删除上月缓存
    if start_date.day == 1:
        last_month_ = last_month(start_date.year, start_date.month)
        last_ts = int(date_to_datetime(last_month_).timestamp())
        for country in list_country_codes_3():
            EmailCountryCountCache(country, last_ts).delete()
            SmsCountryCountCache(country, last_ts).delete()


@scheduled(crontab(minute="50,55", hour="1"))
@lock_call()
def update_monthly_country_notice_report_schedule():
    """ 报表-短信、邮件通知次数-月报 """
    today_ = today()
    last_record = MonthlyCountryReachReport.query.order_by(MonthlyCountryReachReport.report_date.desc()).first()
    if last_record and last_record.updated_at.date() >= today_:
        return

    cur_month = datetime.date(today_.year, today_.month, 1)
    start_month = get_monthly_report_date(MonthlyCountryReachReport, DailyCountryReachReport)
    if not start_month and last_record:
        start_month = cur_month
    if start_month > cur_month:
        # 重复更新当月的数据
        start_month = cur_month

    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_country_notice_report(start_month, end_month)
        start_month = end_month


def update_daily_channel_notice_report(start_date: datetime.date, end_date: datetime.date):
    start_ts = datetime_to_time(start_date)
    end_ts = datetime_to_time(end_date)

    # sms send count
    channel_sms_count_map = defaultdict(int)

    # 根据短信发送情况来计算各渠道的成功发送次数
    for provider in SMSProvider:
        temp_ts = start_ts
        while temp_ts < end_ts:
            cache = SmsCountryResultCache(provider=provider.name, ts=temp_ts)
            if cache.exists():
                for key, value in cache.hgetall().items():
                    #只统计成功的数据
                    if cache.Type.SUCCESS not in key:
                        continue
                
                    country_code, _ = key.split(":")
                    country_code = int(country_code)
                    # 排除国内短信
                    if country_code == 86:
                        continue
                
                    channel_sms_count_map[provider.name] += int(value)
            # 15min 一个缓存
            temp_ts += 15 * 60
    quarter_keys = defaultdict(set)
    for provider in SMSProvider:
        temp_ts = start_ts
        while temp_ts < end_ts:
            cache = SmsCountryResultSetCache(provider=provider.name, ts=temp_ts)
            if cache.exists():
                quarter_keys[provider].add(cache.key)
            # 15min 一个缓存
            temp_ts += 15 * 60

    temp_ts = start_ts
    daily_keys, user_sms_count_map = set(), defaultdict(int)
    for provider, keys in quarter_keys.items():
        daily_cache = SmsCountryResultSetCache(provider=provider.name, ts=temp_ts, _type=SmsCountryResultSetCache.Type.DAILY)
        daily_cache.merge(list(keys))
        daily_cache.expire(SmsCountryResultSetCache.TTL_MAP["daily"])
        daily_keys.add(daily_cache.key)
        
        user_sms_count_map[provider.name] = daily_cache.pfcount()

    channels = set(channel_sms_count_map)
    for c in channels:
        c_str = c or "其他"
        row = DailyChannelReachReport.get_or_create(report_date=start_date, channel=c_str)
        row.sms_send_count = channel_sms_count_map[c]
        row.sms_user_count = user_sms_count_map[c]
        db.session.add(row)

    all_daily_cache = SmsCountryResultSetCache(provider="ALL", ts=start_ts, _type=SmsCountryResultSetCache.Type.DAILY)
    all_daily_cache.merge(list(daily_keys))
    all_daily_cache.expire(SmsCountryResultSetCache.TTL_MAP["daily"])
    all_row = DailyChannelReachReport.get_or_create(report_date=start_date, channel="")
    all_row.sms_send_count = sum(channel_sms_count_map.values())
    all_row.sms_user_count = all_daily_cache.pfcount()
    db.session.add(all_row)
    db.session.commit()


@scheduled(crontab(minute="20,30,40", hour="1"))
@lock_call()
def update_daily_channel_notice_report_schedule():
    """ 报表-短信渠道统计表-日报 """
    today_ = today()
    last_record = DailyChannelReachReport.query.order_by(DailyChannelReachReport.report_date.desc()).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today_ - datetime.timedelta(days=1)

    while start_date < today_:
        end_date = start_date + datetime.timedelta(days=1)
        update_daily_channel_notice_report(start_date, end_date)
        start_date = end_date


def update_monthly_channel_notice_report(start_date: datetime.date, end_date: datetime.date):
    daily_rows: List[DailyChannelReachReport] = DailyChannelReachReport.query.filter(
        DailyChannelReachReport.report_date >= start_date,
        DailyChannelReachReport.report_date < end_date,
    ).all()

    data_template = {
        "sms_send_count": 0,
    }
    month_data_map = defaultdict(lambda: dict(data_template))
    for row in daily_rows:
        data = month_data_map[row.channel]
        data["sms_send_count"] += row.sms_send_count

    start_ts = datetime_to_time(start_date)
    end_ts = datetime_to_time(end_date)

    daily_keys = defaultdict(set)
    for channel in month_data_map:
        temp_ts = start_ts
        while temp_ts < end_ts:
            cache = SmsCountryResultSetCache(provider=channel, ts=temp_ts, _type=SmsCountryResultSetCache.Type.DAILY)
            if cache.exists():
                daily_keys[channel].add(cache.key)
            # 1d 一个缓存
            temp_ts += 24 * 60 * 60

    temp_ts = start_ts
    monthly_keys, sms_user_count_map = set(), dict()
    for provider, keys in daily_keys.items():
        monthly_cache = SmsCountryResultSetCache(provider=provider, ts=temp_ts, _type=SmsCountryResultSetCache.Type.MONTHLY)
        monthly_cache.merge(list(keys))
        monthly_cache.expire(SmsCountryResultSetCache.TTL_MAP["monthly"])
        monthly_keys.add(monthly_cache.key)

        sms_user_count_map[provider] = monthly_cache.pfcount()

    all_monthly_cache = SmsCountryResultSetCache(provider="ALL", ts=start_ts, _type=SmsCountryResultSetCache.Type.MONTHLY)
    all_monthly_cache.merge(list(monthly_keys))
    all_monthly_cache.expire(SmsCountryResultSetCache.TTL_MAP["monthly"])
    sms_user_count_map[''] = all_monthly_cache.pfcount()
    for channel, data in month_data_map.items():
        row = MonthlyChannelReachReport.get_or_create(report_date=start_date, channel=channel)
        row.sms_send_count = data["sms_send_count"]
        row.sms_user_count = sms_user_count_map.get(channel, 0)
        db.session.add(row)
    db.session.commit()
    # 删除上月缓存
    if start_date.day == 1:
        last_month_ = last_month(start_date.year, start_date.month)
        last_ts = int(date_to_datetime(last_month_).timestamp())
        for provider in SMSProvider:
            # 删除每月缓存
            cache = SmsCountryResultSetCache(provider=provider.name, ts=last_ts, _type=SmsCountryResultSetCache.Type.MONTHLY)
            cache.delete()

            # 删除每日缓存
            temp_ts = last_ts
            while temp_ts < end_ts:
                cache = SmsCountryResultSetCache(provider=provider.name, ts=temp_ts, _type=SmsCountryResultSetCache.Type.DAILY)
                cache.delete()
                temp_ts += 24 * 60 * 60


@scheduled(crontab(minute="50,55", hour="1"))
@lock_call()
def update_monthly_channel_notice_report_schedule():
    """ 报表-短信渠道统计表-月报 """
    today_ = today()
    last_record = MonthlyChannelReachReport.query.order_by(MonthlyChannelReachReport.report_date.desc()).first()
    if last_record and last_record.updated_at.date() >= today_:
        return

    cur_month = datetime.date(today_.year, today_.month, 1)
    start_month = get_monthly_report_date(MonthlyChannelReachReport, DailyChannelReachReport)
    if not start_month and last_record:
        start_month = cur_month
    if start_month > cur_month:
        # 重复更新当月的数据
        start_month = cur_month

    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_channel_notice_report(start_month, end_month)
        start_month = end_month


def update_trade_report(start_time, end_time, report_type):

    if not UserTradeSummary.check_data_ready(start_time):
        current_app.logger.warning("{} update_trade_report-UserTradeSummary 数据未就绪".format(start_time))
        return

    trade_query = TradeHandlerMixin.get_trade_res(start_time, end_time)
    fee_query = TradeHandlerMixin.get_fee_res(start_time, end_time)
    market_maker_ids = MarketMakerHelper.list_all_maker_ids()
    for report_category, report_model in TradeHandlerMixin.REPORT_MAPPING[report_type]:
        uids = {i[0] for i in trade_query} | {i[0] for i in fee_query}
        uids -= set(market_maker_ids)
        user_mapping = getattr(TradeHandlerMixin, f'_get_user_{report_category}_map')(uids)
        fmt_trade_data = [(i[0], i[1], i[2], user_mapping[i[0]]) for i in trade_query if i[0] in uids]
        fmt_fee_data = [(i[0], i[1], i[2], user_mapping[i[0]]) for i in fee_query if i[0] in uids]

        all_country_or_language = set()
        grouped_trade_data = group_by(lambda x: x[3], fmt_trade_data)
        spot_trade_user_count = defaultdict(set)
        perpetual_trade_user_count = defaultdict(set)
        exchange_trade_user_count = defaultdict(set)
        trade_user_count = defaultdict(set)
        spot_trade_amount = defaultdict(Decimal)
        perpetual_trade_amount = defaultdict(Decimal)
        exchange_trade_amount = defaultdict(Decimal)
        for country_or_language, rows in grouped_trade_data.items():
            all_country_or_language.add(country_or_language)
            for row in rows:
                user_id, system, trade_amount, _ = row
                if system == UserTradeSummary.System.SPOT:
                    spot_trade_user_count[country_or_language].add(user_id)
                    spot_trade_amount[country_or_language] += trade_amount
                elif system == UserTradeSummary.System.PERPETUAL:
                    perpetual_trade_user_count[country_or_language].add(user_id)
                    perpetual_trade_amount[country_or_language] += trade_amount
                else:
                    exchange_trade_user_count[country_or_language].add(user_id)
                    exchange_trade_amount[country_or_language] += trade_amount

            trade_user_count[country_or_language] = spot_trade_user_count[country_or_language] | \
                    perpetual_trade_user_count[country_or_language] | \
                    exchange_trade_user_count[country_or_language]

        grouped_fee_data = group_by(lambda x: x[3], fmt_fee_data)
        spot_fee_amount = defaultdict(Decimal)
        perpetual_fee_amount = defaultdict(Decimal)
        exchange_fee_amount = defaultdict(Decimal)
        for country_or_language, rows in grouped_fee_data.items():
            all_country_or_language.add(country_or_language)
            for row in rows:
                user_id, system, fee_amount, _ = row
                if system == UserTradeFeeSummary.System.SPOT:
                    spot_fee_amount[country_or_language] += fee_amount
                elif system == UserTradeFeeSummary.System.PERPETUAL:
                    perpetual_fee_amount[country_or_language] += fee_amount
                else:
                    exchange_fee_amount[country_or_language] += fee_amount
        if report_category == 'language':
            all_country_or_language.update([i.name for i in Language])
        key_to_item = {}
        for country_or_language in all_country_or_language:
            spot_trade_users = len(spot_trade_user_count[country_or_language])
            perpetual_trade_users = len(perpetual_trade_user_count[country_or_language])
            exchange_trade_users = len(exchange_trade_user_count[country_or_language])
            spot_fee = spot_fee_amount[country_or_language]
            perpetual_fee = perpetual_fee_amount[country_or_language]
            exchange_fee = exchange_fee_amount[country_or_language]
            avg_spot_fee_amount = spot_fee / spot_trade_users if spot_trade_users else 0
            avg_perpetual_fee_amount = perpetual_fee / perpetual_trade_users if perpetual_trade_users else 0
            avg_exchange_fee_amount = exchange_fee / exchange_trade_users if exchange_trade_users else 0
            item = report_model.get_or_create(report_date=start_time,
                                              **{report_category: country_or_language})
            item.trade_user_count = len(trade_user_count[country_or_language])
            item.spot_trade_user_count = spot_trade_users
            item.perpetual_trade_user_count = perpetual_trade_users
            item.exchange_trade_user_count = exchange_trade_users
            item.spot_trade_amount = spot_trade_amount[country_or_language]
            item.perpetual_trade_amount = perpetual_trade_amount[country_or_language]
            item.exchange_trade_amount = exchange_trade_amount[country_or_language]
            item.spot_fee_amount = spot_fee
            item.perpetual_fee_amount = perpetual_fee
            item.exchange_fee_amount = exchange_fee
            item.avg_spot_fee_amount = avg_spot_fee_amount
            item.avg_perpetual_fee_amount = avg_perpetual_fee_amount
            item.avg_exchange_fee_amount = avg_exchange_fee_amount
            db.session.add(item)
            key_to_item[country_or_language] = item
        area_report_model = None
        if report_model is DailyCountryTradeReport:
            area_report_model = DailyAreaTradeReport
        elif report_model is MonthlyCountryTradeReport:
            area_report_model = MonthlyAreaTradeReport
        elif report_model is QuarterlyCountryTradeReport:
            area_report_model = QuarterlyAreaTradeReport
        if not area_report_model:
            continue
        country_area_mapping = {
            m.info.cn_name: k
            for k, v in AREAS_MAPPING.items() for m in v
        }
        area_map = defaultdict(lambda: defaultdict(Decimal))
        columns = ['trade_user_count',
                   'spot_trade_user_count',
                   'perpetual_trade_user_count',
                   'exchange_trade_user_count',
                   'spot_trade_amount',
                   'perpetual_trade_amount',
                   'exchange_trade_amount',
                   'spot_fee_amount',
                   'perpetual_fee_amount',
                   'exchange_fee_amount',
                   ]
        for country in all_country_or_language:
            if country not in country_area_mapping:
                continue
            _area = country_area_mapping[country]
            item = key_to_item[country]
            for column in columns:
                _add_value = getattr(item, column, Decimal())
                area_map[_area][column] += _add_value

        for area_row, area_data in area_map.items():
            record = area_report_model.get_or_create(report_date=start_time,
                                                     area=area_row.name)
            for column in columns:
                setattr(record, column, area_data[column])
            record.avg_spot_fee_amount = 0
            if record.spot_trade_user_count:
                record.avg_spot_fee_amount = record.spot_fee_amount / record.spot_trade_user_count
            record.avg_perpetual_fee_amount = 0
            if record.perpetual_trade_user_count:
                record.avg_perpetual_fee_amount = record.perpetual_fee_amount / record.perpetual_trade_user_count
            record.avg_exchange_fee_amount = 0
            if record.exchange_trade_user_count:
                record.avg_exchange_fee_amount = record.exchange_fee_amount / record.exchange_trade_user_count
            db.session.add(record)
    db.session.commit()


class TradeHandlerMixin:
    REPORT_MAPPING = {
        'daily': (('country', DailyCountryTradeReport), ('language', DailyLanguageTradeReport)),
        'monthly': (('country', MonthlyCountryTradeReport),('language', MonthlyLanguageTradeReport)),
        'quarterly': (('country', QuarterlyCountryTradeReport),('language', QuarterlyLanguageTradeReport)),
    }

    @classmethod
    def _get_user_language_map(cls, user_ids: Set[int]) -> Dict[int, str]:
        user_language_map = {}
        for ids in batch_iter(user_ids, 1000):
            preferences = (
                UserPreferenceModel.query.filter(
                    UserPreferenceModel.user_id.in_(ids),
                    UserPreferenceModel.key == "language",
                    UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
                )
                .with_entities(
                    UserPreferenceModel.user_id, UserPreferenceModel.value
                )
                .all()
            )
            user_language_map.update({i.user_id: i.value for i in preferences})
            for uid in set(ids) - set(user_language_map.keys()):
                user_language_map.update({uid: Language.EN_US.name})
        return user_language_map

    @classmethod
    def _get_user_country_map(cls, user_ids):
        user_country_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            data = User.query.filter(
                User.id.in_(ids_)
            ).with_entities(User.id, User.location_code).all()
            for uid, code in data:
                country = c.cn_name if (c := get_country(code)) else '其他'
                user_country_map.update({uid: country})
        return user_country_map

    @classmethod
    def get_trade_res(cls, start_time, end_time):
        trade_res = []
        offset = 0
        while 1:
            trade_query = UserTradeSummary.query.filter(
                UserTradeSummary.report_date >= start_time,
                UserTradeSummary.report_date < end_time,
            ).with_entities(
                UserTradeSummary.user_id,
                UserTradeSummary.system,
                UserTradeSummary.trade_amount
            ).order_by(UserTradeSummary.id).offset(offset).limit(200000).all()
            if not trade_query:
                break
            trade_res.extend(trade_query)
            offset += len(trade_query)

        offset = 0
        while 1:
            trade_query = UserExchangeSummary.query.filter(
                UserExchangeSummary.report_date >= start_time,
                UserExchangeSummary.report_date < end_time,
            ).with_entities(
                UserExchangeSummary.user_id,
                UserExchangeSummary.trade_amount
            ).order_by(UserExchangeSummary.id).offset(offset).limit(200000).all()
            if not trade_query:
                break
            trade_res.extend([[item.user_id, "exchange", item.trade_amount] for item in trade_query])
            offset += len(trade_query)
        return trade_res

    @classmethod
    def get_fee_res(cls, start_time, end_time):
        fee_res = []
        offset = 0
        while 1:
            fee_query = UserTradeFeeSummary.query.filter(
                UserTradeFeeSummary.report_date >= start_time,
                UserTradeFeeSummary.report_date < end_time,
            ).with_entities(
                UserTradeFeeSummary.user_id,
                UserTradeFeeSummary.system,
                UserTradeFeeSummary.trade_fee_amount
            ).order_by(UserTradeFeeSummary.id).offset(offset).limit(200000).all()
            if not fee_query:
                break
            fee_res.extend(fee_query)
            offset += len(fee_query)
            
        offset = 0
        while 1:
            trade_query = UserExchangeSummary.query.filter(
                UserExchangeSummary.report_date >= start_time,
                UserExchangeSummary.report_date < end_time,
            ).with_entities(
                UserExchangeSummary.user_id,
                UserExchangeSummary.web_fee_amount,
                UserExchangeSummary.server_fee_amount
            ).order_by(UserExchangeSummary.id).offset(offset).limit(200000).all()
            if not trade_query:
                break
            fee_res.extend([[item.user_id, "exchange", item.web_fee_amount+item.server_fee_amount] for item in trade_query])
            offset += len(trade_query)
        return fee_res


@scheduled(crontab(minute=36, hour='1-3'))
@lock_call()
def update_daily_trade_data_schedule():
    """ 报表-国际化报表-国家/语言交易分布日报 """
    today_ = datetime.datetime.utcnow().date()
    last_record = DailyCountryTradeReport.query.order_by(
        DailyCountryTradeReport.report_date.desc()
    ).first()   # 由于国家报表和语言报表同时生成，只取国家报表的值即可代表两个报表
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = datetime.date(today_.year-1, 1, 1)
    while start_date < today_:
        end_date = start_date + datetime.timedelta(days=1)
        update_trade_report(start_date, end_date, 'daily')
        start_date = end_date


@scheduled(crontab(minute=52, hour='4-5'))
@lock_call()
def update_monthly_trade_data_schedule():
    """ 报表-国际化报表-国家/语言交易分布月报 """
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    # 由于国家报表和语言报表同时生成，只取国家报表的值即可代表两个报表
    start_month = get_monthly_report_date(MonthlyCountryTradeReport,
                                          DailyCountryTradeReport, True)
    if not start_month:
        return
    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_trade_report(start_month, end_month, 'monthly')
        start_month = end_month


@scheduled(crontab(minute=52, hour='4-5'))
@lock_call()
def update_quarterly_trade_data_schedule():
    """ 报表-国际化报表-国家/语言交易分布季报 """
    last_record = QuarterlyCountryTradeReport.query.order_by(
        QuarterlyCountryTradeReport.report_date.desc()
    ).first()
    if last_record:
        start_month = last_record.report_date
    else:
        start_month = datetime.date(2023, 1, 1)
    today_ = today()
    cur_month = datetime.date(today_.year, today_.month, 1)
    while start_month <= cur_month:
        end_month = datetime.date(start_month.year, start_month.month, 1) + relativedelta(months=3)
        update_trade_report(start_month, end_month, 'quarterly')
        start_month = end_month
