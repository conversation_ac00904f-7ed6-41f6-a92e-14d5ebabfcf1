# -*- coding: utf-8 -*-
from collections import defaultdict
import datetime
import json

from celery.schedules import crontab
from flask import current_app

from app.business import lock_call
from app.common import CeleryQueues
from app.models import (
    db,
    MakerCashBackHistory,
    DailyMakerCashBackReport,
    MonthlyMakerCashBackReport,
)
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import scheduled, next_month, route_module_to_celery_queue


route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def _update_daily_maker_cashback_report(start_date, end_date):
    cashback_history_list = MakerCashBackHistory.query.filter(
        MakerCashBackHistory.report_date >= start_date,
        MakerCashBackHistory.report_date < end_date,
        MakerCashBackHistory.status == MakerCashBackHistory.Status.FINISHED,
    ).all()

    asset_users_map = defaultdict(set)
    asset_daily_summary_info_map = defaultdict(
        lambda: dict(total_amount=0, total_usd=0, total_deal_usd=0)
    )
    for h in cashback_history_list:
        asset_users_map[h.asset].add(h.user_id)
        asset_daily_summary_info_map[h.asset]["total_amount"] += h.amount
        asset_daily_summary_info_map[h.asset]["total_usd"] += h.cashback_usd
        asset_daily_summary_info_map[h.asset]["total_deal_usd"] += h.deal_usd

    for asset, asset_info in asset_daily_summary_info_map.items():
        _user_ids = asset_users_map[asset]
        record = DailyMakerCashBackReport(
            report_date=start_date,
            asset=asset,
            amount=asset_info["total_amount"],
            usd=asset_info["total_usd"],
            deal_usd=asset_info["total_deal_usd"],
            user_count=len(_user_ids),
            user_list=json.dumps(list(_user_ids)),
        )
        db.session.add(record)
    db.session.commit()


def _update_monthly_maker_cashback_report(start_date, end_date):
    daily_reports = DailyMakerCashBackReport.query.filter(
        DailyMakerCashBackReport.report_date >= start_date,
        DailyMakerCashBackReport.report_date < end_date,
    ).all()

    asset_users_map = defaultdict(set)
    asset_monthly_summary_info_map = defaultdict(
        lambda: dict(total_amount=0, total_usd=0, total_deal_usd=0)
    )
    for d in daily_reports:
        asset_users_map[d.asset].update(json.loads(d.user_list))
        asset_monthly_summary_info_map[d.asset]["total_amount"] += d.amount
        asset_monthly_summary_info_map[d.asset]["total_usd"] += d.usd
        asset_monthly_summary_info_map[d.asset]["total_deal_usd"] += d.deal_usd

    for asset, asset_info in asset_monthly_summary_info_map.items():
        _user_ids = asset_users_map[asset]
        record = MonthlyMakerCashBackReport(
            report_date=start_date,
            asset=asset,
            amount=asset_info["total_amount"],
            usd=asset_info["total_usd"],
            deal_usd=asset_info["total_deal_usd"],
            user_count=len(_user_ids),
            user_list=json.dumps(list(_user_ids)),
        )
        db.session.add(record)
    db.session.commit()


@scheduled(crontab(minute=30, hour="1-2"))
@lock_call()
def update_daily_maker_cashback_report_schedule():
    today = datetime.datetime.utcnow().date()
    last_record = DailyMakerCashBackReport.query.order_by(
        DailyMakerCashBackReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today + datetime.timedelta(days=-1)
    while start_date < today:
        try:
            end_date = start_date + datetime.timedelta(days=1)
            _update_daily_maker_cashback_report(start_date, end_date)
        except Exception as ex:
            db.session.rollback()
            current_app.logger.exception(ex)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute="30", hour="3-4", day_of_month=1))
@lock_call()
def update_monthly_maker_cashback_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(
        MonthlyMakerCashBackReport, DailyMakerCashBackReport
    )
    if not start_month:
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        _update_monthly_maker_cashback_report(start_month, end_month)
        start_month = end_month
