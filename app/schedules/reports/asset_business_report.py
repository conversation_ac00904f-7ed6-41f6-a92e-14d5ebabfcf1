#!/usr/bin/env python3

from collections import defaultdict
from datetime import datetime, date, timedelta
from enum import Enum
from functools import reduce

from pyroaring import BitMap
from sqlalchemy import func

from app.assets.asset import list_all_assets
from app.business import lock_call, crontab
from app.business.external_dbs import PerpetualSummaryDB, TradeSummaryDB, TradeHistoryDB
from app.business.report.asset_biz import AssetUserVisitBiz, AssetUserExposureBiz
from app.caches.perpetual import PerpetualMarketCache
from app.caches.report import DailyAssetVisitCache, MonthlyAssetVisitCache
from app.caches.spot import MarketCache
from app.common.constants import CeleryQueues, ReportType
from app.models import Market
from app.models.amm import LiquidityHistory, UserLiquiditySlice
from app.models.base import db
from app.models.daily import DailyAssetBusinessReport, DailyAssetVisitReport, \
    DailyBalanceReport, DailyAssetTradeUserReport, DailyAssetExposureReport, DailyAssetUserVisitReport
from app.models.exchange import AssetExchangeOrder, AssetExchangeSysUser
from app.models.investment import InvestmentBalanceHistory
from app.models.margin import MarginLoanOrder
from app.models.mongo.user_business import AssetTradeBehaviorMySQL
from app.models.monthly import MonthlyAssetBusinessReport, \
    MonthlyAssetTradeUserReport, MonthlyBalanceReport, MonthlyAssetVisitReport
from app.models.quotes import UserFavoriteAsset
from app.models.staking import StakingRewardHistory
from app.models.user import SubAccount, UserFollowMarket
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import scheduled
from app.utils.amount import quantize_amount
from app.utils.celery_ import route_module_to_celery_queue
from app.utils.date_ import date_to_datetime, next_month, this_month, today, last_month
from app.utils.iterable import batch_iter

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def _get_sub_user_map(user_ids):
    result = dict()
    for ids in batch_iter(user_ids, 5000):
        tmp = SubAccount.query.filter(
            SubAccount.user_id.in_(ids),
        ).with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        result.update(dict(tmp))
    return result


def update_asset_business_report(report_date: date, report_type: ReportType):
    start_dt = date_to_datetime(report_date)
    if report_type == ReportType.DAILY:
        end_dt = start_dt + timedelta(days=1)
        model = DailyAssetBusinessReport
        balance_data_model = DailyBalanceReport
        trade_data_model = DailyAssetTradeUserReport
    else:
        end_dt = next_month(report_date.year, report_date.month)
        model = MonthlyAssetBusinessReport
        balance_data_model = MonthlyBalanceReport
        trade_data_model = MonthlyAssetTradeUserReport
    balance_reports = balance_data_model.query.filter(
        balance_data_model.report_date == report_date,
        balance_data_model.account_type.is_(None),
    ).all()
    trade_reports = trade_data_model.query.filter(
        trade_data_model.report_date == report_date
    ).all()
    if not balance_reports or not trade_reports:
        return
    balance_report_map = dict()
    asset_balance_increase_user_map = dict()
    up_threshold_asset_balance_increase_user_map = dict()
    for item in balance_reports:
        asset = item.asset
        balance_report_map[asset] = (item.count_0, item.user_count, item.total_usd)
        asset_balance_increase_user_map[asset] = item.increase_asset_user_count
        up_threshold_asset_balance_increase_user_map[asset] = item.up_threshold_increase_asset_user_count
    trade_report_map = dict()
    for item in trade_reports:
        trade_report_map[item.asset] = dict(
            spot_user_count=item.spot_user_count,
            perpetual_user_count=item.perpetual_user_count,
            exchange_user_count=item.exchange_user_count,
            increase_trade_user_count=item.increase_trade_user_count,
        )
    margin_loans = MarginLoanOrder.query.filter(
        MarginLoanOrder.created_at >= start_dt,
        MarginLoanOrder.created_at < end_dt,
    ).with_entities(
            MarginLoanOrder.asset,
            MarginLoanOrder.user_id,
    ).all()
    sub_user_map = _get_sub_user_map({item.user_id for item in margin_loans})
    margin_map = defaultdict(set)
    for item in margin_loans:
        user_id = sub_user_map.get(item.user_id, item.user_id)
        margin_map[item.asset].add(user_id)

    if report_type == ReportType.DAILY:
        records = UserLiquiditySlice.query.filter(
            UserLiquiditySlice.date == report_date
        ).with_entities(
            UserLiquiditySlice.market,
            UserLiquiditySlice.user_id,
        )
    else:
        records = LiquidityHistory.query.filter(
            LiquidityHistory.created_at >= start_dt,
            LiquidityHistory.created_at < end_dt,
            LiquidityHistory.status == LiquidityHistory.Status.FINISHED,
            LiquidityHistory.business == LiquidityHistory.Business.ADD,
        ).with_entities(
            LiquidityHistory.market,
            LiquidityHistory.user_id,
        )
    tmp = _get_sub_user_map({item.user_id for item in records} - set(sub_user_map.values()))
    sub_user_map.update(tmp)
    market_map = MarketCache.online_markets_detail()
    amm_map = defaultdict(set)
    for item in records:
        info = market_map.get(item.market)
        if not info:
            continue
        user_id = sub_user_map.get(item.user_id, item.user_id)
        amm_map[info['base_asset']].add(user_id)
        amm_map[info['quote_asset']].add(user_id)

    records = InvestmentBalanceHistory.query.filter(
        InvestmentBalanceHistory.created_at >= start_dt,
        InvestmentBalanceHistory.created_at < end_dt,
        InvestmentBalanceHistory.opt_type == InvestmentBalanceHistory.OptType.INTEREST,
    ).with_entities(
        InvestmentBalanceHistory.asset,
        InvestmentBalanceHistory.user_id,
    )
    tmp = _get_sub_user_map({item.user_id for item in records} - set(sub_user_map.values()))
    sub_user_map.update(tmp)
    investment_map = defaultdict(set)
    for item in records:
        user_id = sub_user_map.get(item.user_id, item.user_id)
        investment_map[item.asset].add(user_id)
    
    staking_map = defaultdict(set)
    if report_type == ReportType.DAILY:
        staking_records = StakingRewardHistory.query.filter(
            StakingRewardHistory.reward_at == end_dt,
            StakingRewardHistory.status == StakingRewardHistory.Status.FINISHED,
        ).with_entities(
            StakingRewardHistory.asset,
            StakingRewardHistory.user_id,
        ).all()
    else:
        staking_records = StakingRewardHistory.query.filter(
            StakingRewardHistory.reward_at >= start_dt,
            StakingRewardHistory.reward_at <= end_dt,
            StakingRewardHistory.status == StakingRewardHistory.Status.FINISHED,
        ).with_entities(
            StakingRewardHistory.asset,
            StakingRewardHistory.user_id,
        ).all()
    for item in staking_records:
        staking_map[item.asset].add(item.user_id)

    total_record = model.get_or_create(
        report_date=report_date,
        asset=''
    )
    count_0, count, total_usd = balance_report_map.get('', (0, 0, 0))
    total_trade_record_dic = trade_report_map.get('', {})
    total_record.user_count = count_0 + count
    total_record.asset_user_count = count
    total_record.total_usd = quantize_amount(total_usd, 2)
    total_record.spot_user_count = total_trade_record_dic.get('spot_user_count', 0)
    total_record.perpetual_user_count = total_trade_record_dic.get('perpetual_user_count', 0)
    total_record.exchange_user_count = total_trade_record_dic.get('exchange_user_count', 0)
    total_record.increase_trade_user_count = total_trade_record_dic.get('increase_trade_user_count', 0)
    total_record.margin_user_count = len(reduce(lambda x, y: x | y, margin_map.values(), set()))
    total_record.amm_user_count = len(reduce(lambda x, y: x | y, amm_map.values(), set()))
    total_record.investment_user_count = len(reduce(lambda x, y: x | y, investment_map.values(), set()))
    total_record.staking_user_count = len(reduce(lambda x, y: x | y, staking_map.values(), set()))
    total_record.increase_asset_user_count = asset_balance_increase_user_map.get('', 0)
    total_record.up_threshold_increase_asset_user_count = up_threshold_asset_balance_increase_user_map.get('', 0)
    db.session.add(total_record)
    assets = list_all_assets()
    for asset in assets:
        record = model.get_or_create(
            report_date=report_date,
            asset=asset
        )
        count_0, asset_count, usd = balance_report_map.get(asset, (0, 0, 0))
        asset_trade_record_dic = trade_report_map.get(asset, {})
        record.user_count = count_0 + asset_count
        record.asset_user_count = asset_count
        record.total_usd = quantize_amount(usd, 2)
        record.spot_user_count = asset_trade_record_dic.get('spot_user_count', 0)
        record.perpetual_user_count = asset_trade_record_dic.get('perpetual_user_count', 0)
        record.exchange_user_count = asset_trade_record_dic.get('exchange_user_count', 0)
        record.increase_trade_user_count = asset_trade_record_dic.get('increase_trade_user_count', 0)
        record.margin_user_count = len(margin_map[asset])
        record.amm_user_count = len(amm_map[asset])
        record.investment_user_count = len(investment_map[asset])
        record.staking_user_count = len(staking_map[asset])
        record.increase_asset_user_count = asset_balance_increase_user_map.get(asset, 0)
        record.up_threshold_increase_asset_user_count = up_threshold_asset_balance_increase_user_map.get(asset, 0)
        db.session.add(record)
    db.session.commit()


@scheduled(crontab(hour=1, minute='17, 34, 51'))
@lock_call()
def update_daily_asset_trade_report_schedule():
    today_ = today()
    last_report = DailyAssetTradeUserReport.query.order_by(
        DailyAssetTradeUserReport.report_date.desc()).first()
    if last_report:
        report_date = last_report.report_date + timedelta(days=1)
    else:
        report_date = datetime(2022, 10, 1).date()
    while report_date < today_:
        update_asset_trade_report(report_date, ReportType.DAILY)
        report_date += timedelta(days=1)


@scheduled(crontab(day_of_month=1, hour='2-4', minute='*/20'))
@lock_call()
def update_monthly_asset_trade_report_schedule():
    cur_month = this_month()

    start_month = get_monthly_report_date(MonthlyAssetTradeUserReport,
                                          DailyAssetTradeUserReport)
    if not start_month:
        start_month = datetime(2022, 10, 1).date()

    while start_month < cur_month:
        update_asset_trade_report(start_month, ReportType.MONTHLY)
        start_month = next_month(start_month.year, start_month.month)


def update_asset_trade_report(report_date: date, report_type: ReportType):
    start_dt = date_to_datetime(report_date)
    if report_type == ReportType.DAILY:
        end_dt = start_dt + timedelta(days=1)
        model = DailyAssetTradeUserReport
    else:
        end_dt = next_month(report_date.year, report_date.month)
        model = MonthlyAssetTradeUserReport
    table_name = start_dt.strftime('%Y%m')
    start_str = start_dt.strftime('%Y-%m-%d')
    end_str = end_dt.strftime('%Y-%m-%d')

    spot_records = TradeSummaryDB.table(f'user_trade_summary_{table_name}').select(
        'money_asset', 'stock_asset', 'user_id',
        where=f'trade_date >= "{start_str}" AND trade_date < "{end_str}" and deal_amount > 0'
    )
    sub_user_map = _get_sub_user_map({item[2] for item in spot_records})
    spot_map = defaultdict(set)
    for item in spot_records:
        user_id = sub_user_map.get(item[2], item[2])
        spot_map[item[0]].add(user_id)
        spot_map[item[1]].add(user_id)

    perpetual_records = PerpetualSummaryDB.table(
        f'user_trade_summary_{table_name}').select(
        'money_asset', 'stock_asset', 'user_id',
        where=f'trade_date >= "{start_str}" AND trade_date < "{end_str}" and deal_amount > 0'
    )
    sub_user_map.update(_get_sub_user_map({item[2] for item in perpetual_records}))
    perpetual_map = defaultdict(set)
    for item in perpetual_records:
        user_id = sub_user_map.get(item[2], item[2])
        perpetual_map[item[0]].add(user_id)
        perpetual_map[item[1]].add(user_id)

    exchange_map = defaultdict(set)
    orders = AssetExchangeOrder.query.filter(
        AssetExchangeOrder.created_at >= start_dt,
        AssetExchangeOrder.created_at < end_dt,
    ).with_entities(
        AssetExchangeOrder.user_id,
        AssetExchangeOrder.source_asset,
        AssetExchangeOrder.target_asset,
    ).all()
    user_ids = {item.user_id for item in orders}
    sub_user_map.update(_get_sub_user_map(user_ids))
    for item in orders:
        user_id = sub_user_map.get(item.user_id, item.user_id)
        exchange_map[item.source_asset].add(user_id)
        exchange_map[item.target_asset].add(user_id)

    total_record = model.get_or_create(
        report_date=report_date,
        asset=''
    )
    all_history_trade_users = _get_history_trade_users_bit_map(report_date, '')
    total_spot_users = reduce(lambda x, y: x | y, spot_map.values(), set())
    total_perpetual_users = reduce(lambda x, y: x | y, perpetual_map.values(), set())
    total_exchange_users = reduce(lambda x, y: x | y, exchange_map.values(), set())
    total_users = total_spot_users | total_perpetual_users | total_exchange_users
    total_increase_trade_user_count = len(total_users-set(all_history_trade_users))
    all_history_trade_users.update(total_users)
    total_record.spot_user_count = len(total_spot_users)
    total_record.perpetual_user_count = len(total_perpetual_users)
    total_record.exchange_user_count = len(total_exchange_users)
    total_record.increase_trade_user_count = total_increase_trade_user_count
    total_record.history_trade_users = all_history_trade_users.serialize()
    db.session.add(total_record)
    assets = list_all_assets()
    for asset in assets:
        record = model.get_or_create(
            report_date=report_date,
            asset=asset
        )
        history_trade_users = _get_history_trade_users_bit_map(report_date, asset)
        spot_users = spot_map[asset]
        perpetual_users = perpetual_map[asset]
        exchange_users = exchange_map[asset]
        trade_users = spot_users | perpetual_users | exchange_users
        increase_trade_user_count = len(trade_users-set(history_trade_users))
        history_trade_users.update(trade_users)
        record.spot_user_count = len(spot_users)
        record.perpetual_user_count = len(perpetual_users)
        record.exchange_user_count = len(exchange_users)
        record.increase_trade_user_count = increase_trade_user_count
        record.history_trade_users = history_trade_users.serialize()
        db.session.add(record)
    db.session.commit()


def _get_history_trade_users_bit_map(end_date, asset):
    last_report = DailyAssetTradeUserReport.query.filter(
        DailyAssetTradeUserReport.report_date < end_date,
        DailyAssetTradeUserReport.asset == asset,
    ).order_by(DailyAssetTradeUserReport.report_date.desc()).first()
    if not last_report or not last_report.history_trade_users:
        history_trade_users = BitMap()
    else:
        history_trade_users = BitMap.deserialize(last_report.history_trade_users)
    return history_trade_users


@scheduled(crontab(hour='2-3', minute='*/20'))
@lock_call()
def update_daily_asset_business_report_schedule():
    today_ = today()
    last_report = DailyAssetBusinessReport.query.order_by(DailyAssetBusinessReport.report_date.desc()).first()
    if last_report:
        report_date = last_report.report_date + timedelta(days=1)
    else:
        report_date = today_ - timedelta(days=30)
    while report_date < today_:
        update_asset_business_report(report_date, ReportType.DAILY)
        report_date += timedelta(days=1)


@scheduled(crontab(day_of_month=1, hour='4-5', minute='*/20'))
@lock_call()
def update_monthly_asset_business_report_schedule():
    cur_month = this_month()

    start_month = get_monthly_report_date(MonthlyAssetBusinessReport, DailyAssetBusinessReport)
    if not start_month:
        return

    while start_month < cur_month:
        update_asset_business_report(start_month, ReportType.MONTHLY)
        start_month = next_month(start_month.year, start_month.month)


@scheduled(crontab(hour='2-3', minute='*/20'))
@lock_call()
def update_daily_user_visit_report_schedule():
    today_ = today()
    report_date = today_ - timedelta(days=1)
    if DailyAssetVisitReport.query.filter(
        DailyAssetVisitReport.report_date == report_date
    ).first():
        return
    update_daily_user_visit_report(report_date)


class VisitType(Enum):
    SPOT = 'spot'
    PERPETUAL = 'perpetual'
    INFO = 'info'


def update_daily_user_visit_report(report_date: date):
    model = DailyAssetVisitReport
    spot_market_map = MarketCache.online_markets_detail()
    perpetual_market_map = PerpetualMarketCache().read_aside()
    records = UserFollowMarket.query.with_entities(
        UserFollowMarket.market_type,
        UserFollowMarket.user_id,
        UserFollowMarket.trade_type
    ).all()
    spot_follow_map, perpetual_follow_map = defaultdict(set), defaultdict(set)
    for item in records:
        if item.trade_type == UserFollowMarket.TradeType.SPOT:
            if info:= spot_market_map.get(item.market_type):
                spot_follow_map[info['base_asset']].add(item.user_id)
                spot_follow_map[info['quote_asset']].add(item.user_id)
        else:
            if info:= perpetual_market_map.get(item.market_type):
                perpetual_follow_map[info['stock']].add(item.user_id)
                if item.trade_type == UserFollowMarket.TradeType.DIRECT_PERPETUAL:
                    perpetual_follow_map[info['money']].add(item.user_id)
    favorite_assets = UserFavoriteAsset.query.filter(
        UserFavoriteAsset.status == UserFavoriteAsset.StatusType.PASSED,
    ).group_by(UserFavoriteAsset.asset).with_entities(
        UserFavoriteAsset.asset,
        func.count(UserFavoriteAsset.user_id),
    )
    favorite_map = dict(favorite_assets)

    total_record = model(
            report_date=report_date,
            asset=''
        )
    total_spot_count = UserFollowMarket.query.filter(
        UserFollowMarket.trade_type == UserFollowMarket.TradeType.SPOT,
    ).with_entities(func.count(UserFollowMarket.user_id.distinct())).scalar() or 0
    total_record.spot_follow_count = total_spot_count

    total_perpetual_count = UserFollowMarket.query.filter(
        UserFollowMarket.trade_type.in_((UserFollowMarket.TradeType.INVERSE_PERPETUAL,
                                         UserFollowMarket.TradeType.DIRECT_PERPETUAL)),
    ).with_entities(func.count(UserFollowMarket.user_id.distinct())).scalar() or 0
    total_record.perpetual_follow_count = total_perpetual_count

    total_favorite_count = UserFavoriteAsset.query.filter(
        UserFavoriteAsset.status == UserFavoriteAsset.StatusType.PASSED,
    ).with_entities(func.count(UserFavoriteAsset.user_id.distinct())).scalar() or 0
    total_record.favorite_count = total_favorite_count
    for t in VisitType:
        cache = DailyAssetVisitCache(report_date, t.name, '')
        setattr(total_record, f'{t.name.lower()}_view_count', cache.pfcount())
    db.session.add(total_record)
    assets = list_all_assets()
    for asset in assets:
        record = model(
            report_date=report_date,
            asset=asset
        )
        record.spot_follow_count = len(spot_follow_map[asset])
        record.perpetual_follow_count = len(perpetual_follow_map[asset])
        record.favorite_count = favorite_map.get(asset, 0)
        for t in VisitType:
            cache = DailyAssetVisitCache(report_date, t.name, asset)
            setattr(record, f'{t.name.lower()}_view_count', cache.pfcount())
        db.session.add(record)
    db.session.commit()
    yday = report_date - timedelta(days=1)
    last_assets = model.query.filter(
        model.report_date == yday
    ).with_entities(model.asset).all()
    for asset in last_assets:
        for t in VisitType:
            cache = DailyAssetVisitCache(yday, t.name, asset)
            cache.delete()


@scheduled(crontab(hour='2', minute='30'))
@lock_call()
def update_monthly_user_visit_report_schedule():
    today_ = today()
    yday = today_ - timedelta(days=1)
    daily_report = DailyAssetVisitReport.query.filter(
        DailyAssetVisitReport.report_date == yday
    ).first()
    if not daily_report:
        return
    update_monthly_user_visit_report(today_)


def update_monthly_user_visit_report(report_date: date):
    this_month_ = report_date.replace(day=1)
    if report_date.day == 1:
        assets = list_all_assets()
        assets.append('')
        records = []
        for asset in assets:
            record = MonthlyAssetVisitReport(
                report_date=this_month_,
                asset=asset
            )
            records.append(record)
        db.session.add_all(records)
    else:
        records = MonthlyAssetVisitReport.query.filter(
            MonthlyAssetVisitReport.report_date == this_month_
        ).all()
    daily_reports = DailyAssetVisitReport.query.filter(
        DailyAssetVisitReport.report_date == report_date - timedelta(days=1)
    ).all()
    daily_report_map = {r.asset: r for r in daily_reports}
    for record in records:
        daily_report = daily_report_map.get(record.asset)
        if not daily_report:
            continue
        if daily_report:
            record.favorite_count = daily_report.favorite_count
            record.spot_follow_count = daily_report.spot_follow_count
            record.perpetual_follow_count = daily_report.perpetual_follow_count
        for t in VisitType:
            count = MonthlyAssetVisitCache(this_month_, t.name, record.asset).pfcount()
            setattr(record, f'{t.name.lower()}_view_count', count)

    if report_date.day == 1:
        yday = last_month(report_date.year, report_date.month)
        last_assets = MonthlyAssetVisitReport.query.filter(
            MonthlyAssetVisitReport.report_date == yday
        ).with_entities(MonthlyAssetVisitReport.asset).all()
        for asset in last_assets:
            for t in VisitType:
                cache = MonthlyAssetVisitCache(yday, t.name, asset)
                cache.delete()
    db.session.commit()


@scheduled(crontab(hour='2-3', minute='*/20'))
@lock_call()
def update_daily_asset_user_visit_report_schedule():
    """（lang + user_type 维度）"""
    today_ = today()
    model = DailyAssetUserVisitReport
    last_report = model.query.order_by(model.report_date.desc()).first()
    if last_report:
        report_date = last_report.report_date + timedelta(days=1)
    else:
        report_date = today_ - timedelta(days=30)
    while report_date < today_:
        AssetUserVisitBiz.run_report(report_date)
        report_date += timedelta(days=1)


@scheduled(crontab(hour='2-3', minute='*/40'))
@lock_call()
def update_daily_user_exposure_report_schedule():
    today_ = today()
    model = DailyAssetExposureReport
    last_report = model.query.order_by(model.report_date.desc()).first()
    if last_report:
        report_date = last_report.report_date + timedelta(days=1)
    else:
        report_date = today_ - timedelta(days=30)
    while report_date < today_:
        AssetUserExposureBiz.run_report(report_date)
        report_date += timedelta(days=1)


@scheduled(crontab(hour='1-2', minute='0'))
@lock_call()
def update_user_first_asset_trade_behavior_schedule():
    today_ = today()
    model = AssetTradeBehaviorMySQL
    last_report = model.query.order_by(model.report_at.desc()).first()
    if last_report:
        report_date = last_report.report_at + timedelta(days=1)
    else:
        report_date = today_ - timedelta(days=30)
    while report_date < today_:
        update_first_asset_trade_behavior_report(report_date)
        report_date += timedelta(days=1)


def update_first_asset_trade_behavior_report(report_date: date):

    def _get_markets():
        model = Market
        rows = model.query.with_entities(
            model.name,
            model.base_asset
        ).all()
        return {row.name: row.base_asset for row in rows}

    def _find_spot_trade_records():
        start_ts = int(date_to_datetime(report_date).timestamp())
        end_ts = start_ts + 60 * 60 * 24
        ret = defaultdict(lambda: {})
        exchange_sys_users = {
            row.user_id for row in AssetExchangeSysUser.query.with_entities(
                AssetExchangeSysUser.user_id
            ).all()
        }
        for _db, _table_name in TradeHistoryDB.iter_db_and_table(table_name='user_deal_history'):
            _table = _db.table(_table_name)
            rows = _table.select(
                *['user_id', 'market', 'MIN(time)'],
                group_by=f'user_id, market',
                where=f' time >= {start_ts} AND time < {end_ts}'
            )
            for row in rows:
                user_id, market, ts = row
                if user_id in exchange_sys_users:
                    continue
                base_asset = market_to_base.get(market)
                if not base_asset:
                    continue
                ret[user_id][base_asset] = ts
        return ret

    def _update_or_create_objs():
        user_ids = set(spot_ret.keys())
        model = AssetTradeBehaviorMySQL
        exists = {}
        for chunk_ids in batch_iter(user_ids, 10000):
            rows = model.query.filter(
                model.user_id.in_(chunk_ids)
            ).all()
            for row in rows:
                exists[row.user_id] = row

        new_objs = []
        for user_id in user_ids:
            asset_to_ts = spot_ret[user_id]
            assets = set(asset_to_ts.keys())
            if user_id in exists:
                exist_row = exists[user_id]
                exist_data = exist_row.data
                exist_keys = set(exist_data.keys())
                new_keys = assets - exist_keys
                new_data = {key: asset_to_ts[key] for key in new_keys}
                data = {**new_data, **exist_data}
                if exist_data == data:
                    continue
                exist_row.data = data
                exist_row.report_at = report_date
                db.session.add(exist_row)
            else:
                new_obj = model(
                    user_id=user_id,
                    report_at=report_date,
                    data=asset_to_ts,
                )
                new_objs.append(new_obj)
        
        if new_objs:
            db.session.add_all(new_objs)
        db.session.commit()

    market_to_base = _get_markets()
    spot_ret = _find_spot_trade_records()
    _update_or_create_objs()
