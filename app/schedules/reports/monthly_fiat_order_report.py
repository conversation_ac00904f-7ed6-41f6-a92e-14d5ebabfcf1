import datetime

from flask import current_app
from sqlalchemy import func

from app.business import route_module_to_celery_queue, \
    CeleryQueues, scheduled, crontab, lock_call
from app.business.fiat.base import SupportType
from app.models import db, FiatOrder, \
    DailySiteFiatOrderReport, MonthlySiteFiatOrderReport, \
    DailyAssetFiatOrderReport, MonthlyAssetFiatOrderReport
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import next_month

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def _update_monthly_fiat_report(start_month, end_month, support_type: SupportType = None, force_update=False):
    q = DailySiteFiatOrderReport.query.filter(
        DailySiteFiatOrderReport.report_date >= start_month,
        DailySiteFiatOrderReport.report_date < end_month,
    )
    if support_type == SupportType.BUY:
        daily_report_type = DailySiteFiatOrderReport.ReportType.BUY
        monthly_report_type = MonthlySiteFiatOrderReport.ReportType.BUY
    elif support_type == SupportType.SELL:
        daily_report_type = DailySiteFiatOrderReport.ReportType.SELL
        monthly_report_type = MonthlySiteFiatOrderReport.ReportType.SELL
    else:
        daily_report_type = DailySiteFiatOrderReport.ReportType.ALL
        monthly_report_type = MonthlySiteFiatOrderReport.ReportType.ALL

    report_data = q.filter(
        DailySiteFiatOrderReport.report_type == daily_report_type
    ).with_entities(
        func.sum(DailySiteFiatOrderReport.deal_count).label('deal_count'),
        func.sum(DailySiteFiatOrderReport.apply_count).label('apply_count'),
        func.sum(DailySiteFiatOrderReport.deal_usd).label('deal_usd'),
        func.sum(DailySiteFiatOrderReport.new_user_count).label('new_user_count'),
    ).first()

    last_row = q.filter(
        DailySiteFiatOrderReport.report_type == daily_report_type
    ).order_by(DailySiteFiatOrderReport.report_date.desc()).first()
    user_count = last_row.user_count if last_row else 0

    apply_user_set, deal_user_set = set(), set()
    query = FiatOrder.query.filter(
        FiatOrder.created_at >= start_month,
        FiatOrder.created_at < end_month,
    )
    for record in query:
        if support_type == SupportType.BUY:
            if record.order_type == FiatOrder.OrderType.BUY:
                if record.status == FiatOrder.StatusType.APPROVED:
                    deal_user_set.add(record.user_id)
                apply_user_set.add(record.user_id)
        elif support_type == SupportType.SELL:
            if record.order_type == FiatOrder.OrderType.SELL:
                if record.status == FiatOrder.StatusType.APPROVED:
                    deal_user_set.add(record.user_id)
                apply_user_set.add(record.user_id)
        else:
            if record.status == FiatOrder.StatusType.APPROVED:
                deal_user_set.add(record.user_id)
            apply_user_set.add(record.user_id)

    record = MonthlySiteFiatOrderReport.get_or_create(
        report_date=start_month,
        report_type=monthly_report_type
    )
    if not record.deal_usd or force_update:
        record.deal_usd = report_data.deal_usd
        record.deal_count = report_data.deal_count
        record.apply_count = report_data.apply_count
        record.apply_user_count = len(apply_user_set)
        record.deal_user_count = len(deal_user_set)
        record.user_count = user_count
        record.new_user_count = report_data.new_user_count
        db.session.add(record)
        db.session.flush()


def update_monthly_fiat_report(start_month, end_month, force_update=False):
    _update_monthly_fiat_report(start_month, end_month, None, force_update)
    _update_monthly_fiat_report(start_month, end_month, SupportType.BUY, force_update)
    _update_monthly_fiat_report(start_month, end_month, SupportType.SELL, force_update)
    db.session.commit()


def update_monthly_asset_fiat_report(start_month, end_month, force_update=False):

    daily_report_query = DailyAssetFiatOrderReport.query.filter(
        DailyAssetFiatOrderReport.report_date >= start_month,
        DailyAssetFiatOrderReport.report_date < end_month,
    ).group_by(
        DailyAssetFiatOrderReport.asset
    ).with_entities(
        DailyAssetFiatOrderReport.asset,
        func.sum(DailyAssetFiatOrderReport.deal_count).label('deal_count'),
        func.sum(DailyAssetFiatOrderReport.apply_count).label('apply_count'),
        func.sum(DailyAssetFiatOrderReport.deal_usd).label('deal_usd'),
    )

    apply_order_query = FiatOrder.query.filter(
        FiatOrder.created_at >= start_month,
        FiatOrder.created_at < end_month,
    ).group_by(
        FiatOrder.asset
    ).with_entities(
        FiatOrder.asset,
        func.count(FiatOrder.user_id.distinct()).label('user_count')
    )

    deal_order_query = FiatOrder.query.filter(
        FiatOrder.created_at >= start_month,
        FiatOrder.created_at < end_month,
        FiatOrder.status == FiatOrder.StatusType.APPROVED
    ).group_by(
        FiatOrder.asset
    ).with_entities(
        FiatOrder.asset,
        func.count(FiatOrder.user_id.distinct()).label('user_count')
    )

    daily_asset_report_map = {item.asset: {
        'deal_count': item.deal_count,
        'apply_count': item.apply_count,
        'deal_usd': item.deal_usd,
    } for item in daily_report_query}

    apply_order_map = {item.asset: item.user_count for item in apply_order_query}
    deal_order_map = {item.asset: item.user_count for item in deal_order_query}

    for asset, item in daily_asset_report_map.items():
        record = MonthlyAssetFiatOrderReport.get_or_create(
            report_date=start_month, asset=asset)
        if not record.deal_usd or force_update:
            record.deal_usd = item['deal_usd']
            record.apply_count = item['apply_count']
            record.deal_count = item['deal_count']
            record.apply_user_count = apply_order_map[asset] if asset in apply_order_map else 0
            record.deal_user_count = deal_order_map[asset] if asset in deal_order_map else 0
            db.session.add(record)
    db.session.commit()


@scheduled(crontab(minute=20, hour=0))
@lock_call()
def update_monthly_fiat_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    last_record = MonthlySiteFiatOrderReport.query.order_by(
        MonthlySiteFiatOrderReport.report_date.desc()
    ).first()

    if last_record:
        start_month = last_record.report_date
    else:
        start_month = cur_month

    try:
        while start_month <= cur_month:
            end_month = next_month(start_month.year, start_month.month)
            update_monthly_fiat_report(start_month, end_month, force_update=True)
            start_month = end_month
    except Exception as ex:
        db.session.rollback()
        current_app.logger.exception(ex)


@scheduled(crontab(minute=30, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_asset_fiat_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(
        MonthlyAssetFiatOrderReport, DailyAssetFiatOrderReport)

    if not start_month:
        return

    try:
        while start_month < cur_month:
            end_month = next_month(start_month.year, start_month.month)
            update_monthly_asset_fiat_report(start_month, end_month)
            start_month = end_month
    except Exception as ex:
        db.session.rollback()
        current_app.logger.exception(ex)
