import datetime

from flask import current_app
from sqlalchemy import func

from app.business import route_module_to_celery_queue, \
    CeleryQueues, scheduled, crontab, lock_call
from app.models import db, DailyLossProcessHistory as DailyModel, MonthlyLossProcessHistory as MonthlyModel
from app.schedules.reports.utils import get_monthly_report_date, check_report_date_exists
from app.utils import next_month

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def update_monthly_loss_process_report(start_date, end_date, force_update=False):
    query_data = DailyModel.query.filter(
        DailyModel.report_date >= start_date,
        DailyModel.report_date < end_date
    ).group_by(
        DailyModel.asset,
        DailyModel.process_type
    ).with_entities(
        DailyModel.asset,
        DailyModel.process_type,
        func.sum(DailyModel.amount).label('total_amount'),
        func.sum(DailyModel.usd).label('total_usd')
    ).all()
    for item in query_data:
        record = MonthlyModel.get_or_create(report_date=start_date, asset=item.asset)
        if not record.usd or force_update:
            record.usd = item.total_usd
            record.amount = item.total_amount
            record.process_type = MonthlyModel.ProcessType(item.process_type.name)
            db.session.add(record)
    db.session.commit()


@scheduled(crontab(minute=39, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_loss_process_report_schedule():
    today_ = datetime.date.today()
    cur_month = datetime.date(today_.year, today_.month, 1)
    start_month = get_monthly_report_date(MonthlyModel, DailyModel)
    if not start_month:
        return 
    yesterday = cur_month - datetime.timedelta(days=1)
    if not check_report_date_exists(DailyModel, yesterday):
        return 
    try:
        while start_month < cur_month:
            end_month = next_month(start_month.year, start_month.month)
            update_monthly_loss_process_report(start_month, end_month)
            start_month = end_month
    except Exception as ex:
        db.session.rollback()
        current_app.logger.exception(ex)
