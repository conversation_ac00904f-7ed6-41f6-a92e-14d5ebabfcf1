import datetime
from collections import defaultdict
from itertools import chain
from typing import Dict

from celery.schedules import crontab

from app.business import lock_call, route_module_to_celery_queue, CeleryQueues
from app.models import TradeRankActivity, db, \
    TradeRankActivityUserInfo, TradeRankActivityJoinUser, PublicityMixin
from app.models.daily import DailyPerpetualRankReport
from app.models.mongo.analytics import EventTraceLogMySQL
from app.utils import scheduled
from app.utils.date_ import today, date_to_datetime, str_to_datetime

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


@scheduled(crontab(minute='1,11', hour='0'))
@lock_call()
def update_daily_perpetual_rank_schedule():
    """更新合约交易赛日报表"""
    today_ = today()
    rec = DailyPerpetualRankReport.query.filter(
        DailyPerpetualRankReport.report_date != 'ALL').order_by(
        DailyPerpetualRankReport.report_date.desc()
    ).first()
    if not rec:
        report_date = _get_first_date()
        if not report_date:
            return
    else:
        report_date = str_to_datetime(rec.report_date).date()
    while report_date < today_:
        update_daily_perpetual_rank(report_date)
        report_date += datetime.timedelta(days=1)


def _get_first_date():
    log = EventTraceLogMySQL.query.filter(
        EventTraceLogMySQL.event.in_([
            EventTraceLogMySQL.Event.PERPETUAL_TRADE_RANK_CLICK,
            EventTraceLogMySQL.Event.PERPETUAL_TRADE_RANK_APPLY
        ])
    ).order_by(EventTraceLogMySQL.created_at).first()
    if not log:
        return None
    return log.created_at.date()


def update_daily_perpetual_rank(report_date):
    start = date_to_datetime(report_date)
    end = start + datetime.timedelta(days=1)
    report_date_str = datetime.datetime.strftime(report_date, '%Y-%m-%d')
    activities_logs_dic = get_activities_logs(start, end)
    if not activities_logs_dic:
        return
    for activity_id, event_logs in activities_logs_dic.items():
        total_trade_users = get_total_trade_users(activity_id)
        daily_event_logs = filter_daily_event_logs(event_logs, start, end)
        d_click_channel_res, d_click_all_users, d_apply_channel_res, d_apply_all_users = aggregate_event_logs(
            daily_event_logs)
        save_data(report_date_str, activity_id, d_click_channel_res, d_click_all_users,
                  d_apply_channel_res, d_apply_all_users, total_trade_users)
        update_trade_rank_users(activity_id, d_apply_channel_res)
        a_click_channel_res, a_click_all_users, a_apply_channel_res, a_apply_all_users = aggregate_event_logs(
            event_logs)
        save_data('ALL', activity_id, a_click_channel_res,
                  a_click_all_users, a_apply_channel_res, a_apply_all_users, total_trade_users)
        update_history_trade_data(report_date_str, activity_id, total_trade_users)


def get_activities_logs(start, end):
    activity_ids, query_start = get_valid_activities_and_query_start(start)
    if not activity_ids:
        return {}
    activities_logs = search_and_format_logs(activity_ids, query_start, end)
    return activities_logs


def get_valid_activities_and_query_start(start):
    activities = TradeRankActivity.query.filter(
        TradeRankActivity.type.in_((TradeRankActivity.Type.PERPETUAL_TRADE,
                                    TradeRankActivity.Type.PERPETUAL_INCOME,
                                    TradeRankActivity.Type.PERPETUAL_INCOME_RATE,
                                    )),
        TradeRankActivity.status != TradeRankActivity.Status.DELETED,
        TradeRankActivity.ended_at >= start
    ).all()
    if not activities:
        return [], None
    query_start = min([activity.started_at for activity in activities])
    safe_query_start = query_start - datetime.timedelta(days=10)  # 需要获取活动期间及活动预热期的数据,一般在活动开始前1-3天预热
    return [i.id for i in activities], safe_query_start


def search_and_format_logs(activity_ids, query_start, query_end):
    logs = get_logs(query_start, query_end)
    shor_url_publicity_dic = PublicityMixin.get_short_url_publicity_channel_fmt_name_dic()
    res = defaultdict(lambda: defaultdict(list))
    for log in logs:
        data = log.data
        if not data:
            continue
        activity_id = data.get('x_activity_id')
        if not activity_id or int(activity_id) not in activity_ids:
            continue
        event = log.event
        user_id = log.user_id
        if event == EventTraceLogMySQL.Event.PERPETUAL_TRADE_RANK_APPLY and not user_id:
            continue
        short_url = data.get('x_short_link')
        if not short_url:
            publicity_channel = '其他渠道'
        elif channel := shor_url_publicity_dic.get(short_url):
            publicity_channel = channel
        else:
            continue

        res[int(activity_id)][event].append({
            'publicity_channel': publicity_channel,
            'token': log.token,
            'user_id': user_id,
            'created_at': log.created_at,
        })
    return res


def get_logs(query_start, query_end):
    logs = EventTraceLogMySQL.query.filter(
        EventTraceLogMySQL.event.in_([
            EventTraceLogMySQL.Event.PERPETUAL_TRADE_RANK_CLICK,
            EventTraceLogMySQL.Event.PERPETUAL_TRADE_RANK_APPLY
        ]),
        EventTraceLogMySQL.created_at >= query_start,
        EventTraceLogMySQL.created_at < query_end
    ).order_by(EventTraceLogMySQL.created_at).all()
    return logs


def filter_daily_event_logs(event_logs, start, end):
    daily_res = defaultdict(list)
    for event, logs in event_logs.items():
        for log in logs:
            if start <= log['created_at'] < end:
                daily_res[event].append(log)
    return daily_res


def aggregate_event_logs(event_logs: Dict):
    click_channel_res, click_all_users, apply_channel_res, apply_all_users = defaultdict(
        set), set(), defaultdict(set), set()
    for event, logs in event_logs.items():
        if event == EventTraceLogMySQL.Event.PERPETUAL_TRADE_RANK_CLICK:
            click_channel_res, click_all_users = aggregate_logs(logs)
        elif event == EventTraceLogMySQL.Event.PERPETUAL_TRADE_RANK_APPLY:
            apply_channel_res, apply_all_users = aggregate_logs(logs)
    return click_channel_res, click_all_users, apply_channel_res, apply_all_users


def aggregate_logs(logs):
    """"用户登录态->非登录态后，会销毁当前缓存的token，非登录态->登录态仍然沿用以前的token"""
    all_users = set()
    user_id_grouped_items = defaultdict(set)
    non_user_id_grouped_items = defaultdict(set)
    for log in logs:
        user_id = log['user_id']
        token = log['token']
        publicity_channel = log['publicity_channel']
        if user_id:
            user_id_grouped_items[user_id].add(publicity_channel)
            if token in non_user_id_grouped_items:  # 表明这条记录此前为非登录态，后面又新增了登录态，需要将原来非登录态的数据平移过来
                channels_set = non_user_id_grouped_items.pop(token)
                user_id_grouped_items[user_id].update(channels_set)
                all_users -= {token}
            all_users.add(user_id)
        else:
            non_user_id_grouped_items[token].add(publicity_channel)
            all_users.add(token)

    channel_res = defaultdict(set)
    for user, channel_set in chain(user_id_grouped_items.items(),
                                   non_user_id_grouped_items.items()):
        for channel in channel_set:
            channel_res[channel].add(user)
    return channel_res, all_users


def save_data(report_date_str, activity_id, click_channel_res, click_all_users,
              apply_channel_res, apply_all_users, total_trade_users):
    save_channel_data(report_date_str, activity_id, click_channel_res,
                      apply_channel_res, total_trade_users)
    save_all_data(report_date_str, activity_id, click_all_users,
                  apply_all_users, total_trade_users)
    db.session.commit()


def save_channel_data(report_date_str, activity_id, click_channel_res,
                      apply_channel_res, total_trade_users):
    channels = set(click_channel_res.keys()) | set(apply_channel_res.keys())
    for channel in channels:
        click_count = len(click_channel_res[channel])
        apply_users = apply_channel_res[channel]
        apply_count = len(apply_users)
        trade_users = apply_users & total_trade_users
        trade_count = len(trade_users)
        row = DailyPerpetualRankReport.query.filter(
            DailyPerpetualRankReport.activity_id == activity_id,
            DailyPerpetualRankReport.channel == channel,
            DailyPerpetualRankReport.report_date == report_date_str
        ).first()
        if row:
            row.click_count = click_count
            row.apply_count = apply_count
            row.trade_count = trade_count
        else:
            row = DailyPerpetualRankReport(
                report_date=report_date_str,
                activity_id=activity_id,
                channel=channel,
                click_count=click_count,
                apply_count=apply_count,
                trade_count=trade_count
            )
            db.session.add(row)


def save_all_data(report_date_str, activity_id, click_all_users,
                  apply_all_users, total_trade_users):
    all_row = DailyPerpetualRankReport.query.filter(
        DailyPerpetualRankReport.activity_id == activity_id,
        DailyPerpetualRankReport.channel == 'ALL',
        DailyPerpetualRankReport.report_date == report_date_str
    ).first()
    click_all_count = len(click_all_users)
    apply_all_count = len(apply_all_users)
    trade_all_users = apply_all_users & total_trade_users
    trade_all_count = len(trade_all_users)
    if all_row:
        all_row.click_count = click_all_count
        all_row.apply_count = apply_all_count
        all_row.trade_count = trade_all_count
    else:
        all_row = DailyPerpetualRankReport(
            report_date=report_date_str,
            activity_id=activity_id,
            channel='ALL',
            click_count=click_all_count,
            apply_count=apply_all_count,
            trade_count=trade_all_count
        )
        db.session.add(all_row)


def get_total_trade_users(activity_id):
    user_infos = TradeRankActivityUserInfo.query.filter(
        TradeRankActivityUserInfo.trade_activity_id == activity_id,
        TradeRankActivityUserInfo.trade_amount > 0
    ).with_entities(
        TradeRankActivityUserInfo.user_id,
    ).all()
    return {i.user_id for i in user_infos}


def update_trade_rank_users(activity_id, apply_channel_res):
    user_id_channel_dic = dict()
    for channel, users in apply_channel_res.items():
        for user_id in users:
            user_id_channel_dic[user_id] = channel
    records = TradeRankActivityJoinUser.query.filter(
        TradeRankActivityJoinUser.trade_activity_id == activity_id
    ).all()
    for record in records:
        if record.user_id in user_id_channel_dic:
            record.channel = user_id_channel_dic[record.user_id]
    db.session.commit()


def update_history_trade_data(report_date_str, activity_id, total_trade_users):
    apply_dic = get_apply_channel_dic(activity_id)
    rows = DailyPerpetualRankReport.query.filter(
        DailyPerpetualRankReport.activity_id == activity_id,
        DailyPerpetualRankReport.report_date < report_date_str,
    ).all()
    for row in rows:
        report_date_str = row.report_date
        channels_dic = apply_dic.get(report_date_str)
        if not channels_dic:
            continue
        channel = row.channel
        apply_users = channels_dic[channel]
        trade_count = len(apply_users & total_trade_users)
        row.trade_count = trade_count
    db.session.commit()


def get_apply_channel_dic(activity_id):
    records = TradeRankActivityJoinUser.query.filter(
        TradeRankActivityJoinUser.trade_activity_id == activity_id,
        TradeRankActivityJoinUser.channel.isnot(None)
    ).all()
    res = defaultdict(lambda: defaultdict(set))
    for record in records:
        report_date_str = datetime.datetime.strftime(record.created_at, '%Y-%m-%d')
        res[report_date_str][record.channel].add(record.user_id)
        res[report_date_str]['ALL'].add(record.user_id)
    return res
