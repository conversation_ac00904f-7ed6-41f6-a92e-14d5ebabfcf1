#!/usr/bin/env python3
import datetime
import json
from collections import defaultdict
from decimal import Decimal

from celery.schedules import crontab
from sqlalchemy import func

from app.business import lock_call
from app.caches import TimedHashCache
from app.common import CeleryQueues
from app.models import db, MarginReceivableInterestHistory, MarginFlatHistory, MarginLoanOrder, AssetPrice, \
    DailyMarginAssetReport, MonthlyMarginAssetReport
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import route_module_to_celery_queue, scheduled, next_month

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def update_margin_report(start_time, end_time):

    report_model = DailyMarginAssetReport
    # 还币人数
    margin_flat_result = MarginFlatHistory.query.filter(
        MarginFlatHistory.created_at >= start_time,
        MarginFlatHistory.created_at < end_time,
    ).group_by(
        MarginFlatHistory.asset,
        MarginFlatHistory.user_id,
    ).with_entities(
            MarginFlatHistory.asset,
            MarginFlatHistory.user_id,
            func.count('*').label('flat_order_count'),
            func.sum(MarginFlatHistory.amount).label("sum_amount"),
    ).all()
    # 借币人数
    margin_loan_result = MarginLoanOrder.query.filter(
        MarginLoanOrder.created_at >= start_time,
        MarginLoanOrder.created_at < end_time,
    ).group_by(
        MarginLoanOrder.asset,
        MarginLoanOrder.user_id,
    ).with_entities(
            MarginLoanOrder.asset,
            MarginLoanOrder.user_id,
            func.count('*').label('loan_order_count'),
            func.sum(MarginLoanOrder.loan_amount).label("sum_amount"),
    ).all()

    # 产生利息
    margin_interest_result = MarginReceivableInterestHistory.query.filter(
        MarginReceivableInterestHistory.created_at >= start_time,
        MarginReceivableInterestHistory.created_at < end_time,
    ).group_by(
        MarginReceivableInterestHistory.asset,
        MarginReceivableInterestHistory.user_id,
    ).with_entities(
            MarginReceivableInterestHistory.asset,
            MarginReceivableInterestHistory.user_id,
            func.sum(MarginReceivableInterestHistory.amount).label("sum_amount"),
    ).all()

    margin_map = defaultdict(lambda: {
                "loan_amount": Decimal(),
                "flat_amount": Decimal(),
                "interest_amount": Decimal(),
                "loan_order_count": int(),
                "flat_order_count": int(),
                "loan_usd": Decimal(),
                "flat_usd": Decimal(),
                "interest_usd": Decimal(),
                "active_user_set": set(),
                "loan_user_set": set(),
                "flat_user_set": set(),
            })
    price_map = AssetPrice.get_close_price_map(start_time)
    for item in margin_flat_result:
        margin_map[item.asset]['flat_amount'] += item.sum_amount
        margin_map[item.asset]['flat_order_count'] += item.flat_order_count
        margin_map[item.asset]['flat_usd'] += item.sum_amount * price_map[item.asset]
        margin_map[item.asset]['flat_user_set'].add(item.user_id)

    for item in margin_loan_result:
        margin_map[item.asset]['loan_amount'] += item.sum_amount
        margin_map[item.asset]['loan_order_count'] += item.loan_order_count
        margin_map[item.asset]['loan_usd'] += item.sum_amount * price_map[item.asset]
        margin_map[item.asset]['loan_user_set'].add(item.user_id)
        margin_map[item.asset]['active_user_set'].add(item.user_id)

    for item in margin_interest_result:
        margin_map[item.asset]['interest_amount'] += item.sum_amount
        margin_map[item.asset]['interest_usd'] += item.sum_amount * price_map[item.asset]
        margin_map[item.asset]['active_user_set'].add(item.user_id)

    for asset, margin_data in margin_map.items():
        record = report_model.query.filter(
            report_model.report_date == start_time,
            report_model.asset == asset,
        ).first()
        if not record:
            record = report_model()
            record.report_date = start_time
            record.asset = asset
        record.active_user_list = json.dumps(list(margin_data['active_user_set']))
        record.loan_user_list = json.dumps(list(margin_data['loan_user_set']))
        record.flat_user_list = json.dumps(list(margin_data['flat_user_set']))

        record.active_user_count = len(margin_data['active_user_set'])
        record.loan_user_count = len(margin_data['loan_user_set'])
        record.flat_user_count = len(margin_data['flat_user_set'])

        record.loan_amount = margin_data['loan_amount']
        record.flat_amount = margin_data['flat_amount']
        record.interest_amount = margin_data['interest_amount']

        record.loan_order_count = margin_data['loan_order_count']
        record.flat_order_count = margin_data['flat_order_count']

        record.loan_usd = margin_data['loan_usd']
        record.flat_usd = margin_data['flat_usd']
        record.interest_usd = margin_data['interest_usd']

        # 平均借贷余额 和 平均利率
        timestamp_ = str(int(datetime.datetime(
            start_time.year, start_time.month, start_time.day).timestamp()))
        key_ = 'margin_report_unflat'
        margin_key = f'{key_}_{asset}_{timestamp_}'
        interval = 86400 * 3
        unflat_amount_list = list(map(Decimal, TimedHashCache(margin_key, interval=interval).list_values()))
        avg_loan_balance_amount = sum(unflat_amount_list) / len(unflat_amount_list) if unflat_amount_list else 0
        avg_loan_balance_usd = avg_loan_balance_amount * price_map[asset]

        record.average_loan_balance_amount = avg_loan_balance_amount
        record.average_loan_balance_usd = avg_loan_balance_usd
        record.average_interest_rate = record.interest_usd / avg_loan_balance_usd \
            if avg_loan_balance_usd > Decimal() else Decimal()

        db.session.add(record)

    db.session.commit()


def update_monthly_margin_report(start_time, end_time):

    report_model = MonthlyMarginAssetReport

    margin_report_data = DailyMarginAssetReport.query.filter(
        DailyMarginAssetReport.report_date >= start_time,
        DailyMarginAssetReport.report_date < end_time,
    ).all()
    margin_map = defaultdict(lambda: {
        "loan_amount": Decimal(),
        "flat_amount": Decimal(),
        "interest_amount": Decimal(),
        "loan_order_count": int(),
        "flat_order_count": int(),
        "loan_usd": Decimal(),
        "flat_usd": Decimal(),
        "interest_usd": Decimal(),
        "active_user_set": set(),
        "loan_user_set": set(),
        "flat_user_set": set(),
        "loan_balance_amount": Decimal(),
        "loan_balance_usd": Decimal()
    })
    for item in margin_report_data:
        margin_map[item.asset]['loan_amount'] += item.loan_amount
        margin_map[item.asset]['flat_amount'] += item.flat_amount
        margin_map[item.asset]['interest_amount'] += item.interest_amount
        margin_map[item.asset]['loan_order_count'] += item.loan_order_count
        margin_map[item.asset]['flat_order_count'] += item.flat_order_count
        margin_map[item.asset]['loan_usd'] += item.loan_usd
        margin_map[item.asset]['flat_usd'] += item.flat_usd
        margin_map[item.asset]['interest_usd'] += item.interest_usd
        margin_map[item.asset]['loan_balance_amount'] += item.average_loan_balance_amount
        margin_map[item.asset]['loan_balance_usd'] += item.average_loan_balance_usd
        margin_map[item.asset]['active_user_set'].update(json.loads(item.active_user_list))
        margin_map[item.asset]['loan_user_set'].update(json.loads(item.loan_user_list))
        margin_map[item.asset]['flat_user_set'].update(json.loads(item.flat_user_list))

    days_in_month = (end_time - start_time).days
    for asset, margin_data in margin_map.items():
        record = report_model.query.filter(
            report_model.report_date == start_time,
            report_model.asset == asset,
        ).first()
        if not record:
            record = report_model()
            record.report_date = start_time
            record.asset = asset

        record.active_user_count = len(margin_data['active_user_set'])
        record.loan_user_count = len(margin_data['loan_user_set'])
        record.flat_user_count = len(margin_data['flat_user_set'])

        record.loan_amount = margin_data['loan_amount']
        record.flat_amount = margin_data['flat_amount']
        record.interest_amount = margin_data['interest_amount']

        record.loan_order_count = margin_data['loan_order_count']
        record.flat_order_count = margin_data['flat_order_count']

        record.loan_usd = margin_data['loan_usd']
        record.flat_usd = margin_data['flat_usd']
        record.interest_usd = margin_data['interest_usd']

        avg_loan_balance_amount = margin_data['loan_balance_amount'] / Decimal(f'{days_in_month}')
        avg_loan_balance_usd = margin_data['loan_balance_usd'] / Decimal(f'{days_in_month}')

        record.average_loan_balance_amount = avg_loan_balance_amount
        record.average_loan_balance_usd = avg_loan_balance_usd
        record.average_interest_rate = record.interest_usd / margin_data['loan_balance_usd'] \
            if avg_loan_balance_usd > Decimal() else Decimal()

        db.session.add(record)

        db.session.commit()


@scheduled(crontab(minute=20, hour='0-4'))
@lock_call()
def update_daily_margin_schedule():
    today = datetime.datetime.utcnow().date()
    last_record = DailyMarginAssetReport.query.order_by(
        DailyMarginAssetReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = datetime.date(2019, 6, 1)
    while start_date < today:
        end_date = start_date + datetime.timedelta(days=1)
        update_margin_report(start_date, end_date)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute=30, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_margin_schedule():

    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(
        MonthlyMarginAssetReport, DailyMarginAssetReport)

    if not start_month:
        return

    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_margin_report(start_month, end_month)
        start_month = end_month
