import datetime
from decimal import Decimal
from typing import List, Dict
from openpyxl import Workbook
import io

from flask.templating import render_template_string

from app.business.market_maker import MarketMakerHelper
from app.business import (CeleryQueues, PerpetualSummaryDB,
                          SubAccount, TradeSummaryDB, User, UserPreferences,
                          amount_to_str, crontab, defaultdict, lock_call,
                          route_module_to_celery_queue, scheduled)
from app.business.email import send_user_trade_summary_email
from app.business.market_maker import SPOT_MARKET_MAKER_APPRAISAL_MAP, \
    PERPETUAL_MARKET_MAKER_APPRAISAL_MAP, MIN_SPOT_MARKET_MAKER_RANKING_AMOUNT, MIN_PERPETUAL_MARKET_MAKER_RANKING_AMOUNT
from app.caches.report import DailyMarketMakerReportCache
from app.common import PerpetualMarketType
from app.common.constants import Language, ReportType
from app.models import AssetPrice, MarketMaker, MarketMakerReportSendEmail
from app.schedules.market_maker import Checker
from app.utils import str_to_datetime
from flask import current_app
from flask_babel import force_locale, gettext as _
from app.utils import AWSBucketTmp, batch_iter


route_module_to_celery_queue(__name__, CeleryQueues.DAILY)

MARKET_MAKER_RANKING_FIELDS = (
    ('date', _('日期')),
    ('type', _('排位类型')),
    ('trade_usd', _('当月累计交易量')),
    ('ranking', _('最新排名')),
    ('level', _('预估下月等级')),
    ('next_level_trade_amount_delta_usd', _('距离上一等级交易量相差')),
    ('previous_level_trade_amount_delta_usd', _('距离下一等级交易量相差'))
)

SPOT_USER_TRADE_FIELDS = (
    ('date', _('日期')),
    ('market', _('市场')),
    ('str_taker_value_usd', _('Taker交易量')),
    ('str_maker_value_usd', _('Maker交易量')),
    ('value_usd', _('总交易量')),
    ('p_all_user_deal_value_usd', _('账户比例')),
    ('p_all_deal_value_usd', _('全站比例')),
    ('fee', _('手续费')),
    ('str_monthly_taker_value_usd', _('当月Taker交易量')),
    ('str_monthly_maker_value_usd', _('当月Maker交易量')),
    ('str_monthly_deal_value_usd', _('当月总交易量'))
)

SPOT_USER_TRADE_TYPE_FIELDS = (
    ('date', _('日期')),
    ('type', _('交易类型')),
    ('value_usd', _('交易量')),
    ('proportion', _('账户比例')),
    ('site_proportion', _('全站比例')),
    ('fee', _('手续费')),
    ('monthly_value_usd', _('当月总交易量'))
)

PERPETUAL_USER_TRADE_FIELDS = (
    ('date', _('日期')),
    ('market', _('市场')),
    ('str_taker_value_usd', _('Taker交易量')),
    ('str_maker_value_usd', _('Maker交易量')),
    ('value_usd', _('总交易量')),
    ('p_all_user_deal_value_usd', _('账户比例')),
    ('p_all_deal_value_usd', _('全站比例')),
    ('fee', _('手续费')),
    ('str_monthly_taker_value_usd', _('当月Taker交易量')),
    ('str_monthly_maker_value_usd', _('当月Maker交易量')),
    ('str_monthly_deal_value_usd', _('当月总交易量'))
)

PERPETUAL_USER_TRADE_TYPE_FIELDS = (
    ('date', _('日期')),
    ('type', _('交易类型')),
    ('value_usd', _('交易量')),
    ('proportion', _('账户比例')),
    ('site_proportion', _('全站比例')),
    ('fee', _('手续费')),
    ('monthly_value_usd', _('当月总交易量'))
)

def get_export_url(data, lang):
    """
    sheet_info: 
    {
        "sheet_name1": {
            "headers": [],
            "fields": [],
            "data": []
        }
    }
    """
    sheet_info = []
    with force_locale(lang if lang in (Language.EN_US.value, Language.ZH_HANS_CN.value) else Language.DEFAULT.value):
        if market_maker_ranking_data:= data.get('market_maker_ranking_data'):
            sheet_info.append((_("当月【做市商等级】排位情况"), market_maker_ranking_data, MARKET_MAKER_RANKING_FIELDS))
        if spot_user_trade_data:= data.get('spot_user_trade_data'):
            sheet_info.append((_("【现货-市场】统计日报"), spot_user_trade_data, SPOT_USER_TRADE_FIELDS))
        if spot_user_trade_type_data:= data.get('spot_user_trade_type_data'):
            sheet_info.append((_("【现货-交易类型】统计日报"), spot_user_trade_type_data, SPOT_USER_TRADE_TYPE_FIELDS))
        if perpetual_user_trade_data:= data.get('perpetual_user_trade_data'):
            sheet_info.append((_("【合约-市场】统计日报"), perpetual_user_trade_data, PERPETUAL_USER_TRADE_FIELDS))
        if perpetual_user_trade_type_data:= data.get('perpetual_user_trade_type_data'):
            sheet_info.append((_("【合约-交易类型】统计日报"), perpetual_user_trade_type_data, PERPETUAL_USER_TRADE_TYPE_FIELDS))
    wb = Workbook()
    del wb['Sheet']
    for item in sheet_info:
        name, records, header_data = item
        with force_locale(lang):
            headers = [_(item[1]) for item in header_data]
        fields = [item[0] for item in header_data]
        ws = wb.create_sheet(name)
        ws.append(headers)
        for _t in records:
            ws.append([_t[k] for k in fields])
    stream = io.BytesIO()
    wb.save(stream)
    stream.seek(0)
    file_key = AWSBucketTmp.new_file_key(suffix='xlsx')
    if not AWSBucketTmp.put_file(file_key, stream):
        return ''
    return AWSBucketTmp.get_file_url(file_key, ttl=60 * 60 * 24 * 7)

def list_coin_trade_summary(date_str, trade_summary_db):
    columns = (
        'market', 'stock_asset', 'money_asset', 'deal_amount', 'deal_volume',
    )
    where = f' trade_date = "{date_str}"'

    s = trade_summary_db.table('coin_trade_summary').select(
        *columns,
        where=where,
    )
    records = list(dict(zip(columns, r)) for r in s)

    trade_summary_map = {}
    for record in records:
        trade_summary_map[record['market']] = {
            'stock_asset': record['stock_asset'],
            'money_asset': record['money_asset'],
            'deal_amount': record['deal_amount'],
            'deal_volume': record['deal_volume'],
        }
    return trade_summary_map


def list_perpetual_coin_trade_summary(date_str, trade_summary_db):
    columns = (
        'market', 'stock_asset', 'money_asset', 'deal_amount', 'deal_volume',
        'market_type',
    )
    where = f' trade_date = "{date_str}"'

    s = trade_summary_db.table('coin_trade_summary').select(
        *columns,
        where=where,
    )
    records = list(dict(zip(columns, r)) for r in s)

    trade_summary_map = {}
    for record in records:
        trade_summary_map[record['market']] = {
            'stock_asset': record['stock_asset'],
            'money_asset': record['money_asset'],
            'deal_amount': record['deal_amount'],
            'market_type': record['market_type'],
        }

    return trade_summary_map


def get_user_data():
    query = MarketMaker.query.join(
        User
    ).filter(
        MarketMaker.user_id == User.id,
        MarketMaker.status == MarketMaker.StatusType.PASS)

    query = query.with_entities(
        MarketMaker.user_id,
        User.name,
        User.email,
        MarketMaker.maker_type,
    )

    inner_maker_ids = MarketMakerHelper.list_inner_maker_ids(include_sub_account=False)
    inner_market_maker_query = User.query.filter(
        User.id.in_(inner_maker_ids)
    ).with_entities(
        User.id,
        User.email,
    ).all()

    user_data = query.all()

    spot_market_maker_list = [user.user_id for user in user_data
                              if user.maker_type == MarketMaker.MakerType.SPOT]
    perpetual_market_maker_list = [user.user_id for user in user_data
                                   if user.maker_type == MarketMaker.MakerType.PERPETUAL]
    user_email_map = {user.user_id: user.email for user in user_data}

    inner_market_market_list = [i.id for i in inner_market_maker_query]
    user_email_map.update({i.id: i.email for i in inner_market_maker_query})

    return spot_market_maker_list, perpetual_market_maker_list, inner_market_market_list, user_email_map


def get_site_spot_trade_type_summary(trade_summary_db, date_str, month_str, coin_rate):
    str_date_filter = f'trade_date = "{date_str}"'
    columns = ('money_asset', 'taker_volume', 'maker_volume',)
    select_fields = (
        'money_asset',
        'SUM(`taker_volume`) AS taker_volume',
        'SUM(`maker_volume`) AS maker_volume',
    )
    where = f' {str_date_filter}'
    group_by = "`money_asset`"
    rows = trade_summary_db.table(f'user_trade_summary_{month_str}').select(
        *select_fields,
        where=where,
        group_by=group_by,
    )
    records = list(dict(zip(columns, r)) for r in rows)
    site_taker_volume_usd = site_maker_volume_usd = Decimal()
    for record in records:
        asset = record['money_asset']
        site_taker_volume_usd += record['taker_volume'] * coin_rate.get(asset, 0)
        site_maker_volume_usd += record['maker_volume'] * coin_rate.get(asset, 0)
    return site_taker_volume_usd, site_maker_volume_usd


def get_user_spot_trade_summary(trade_summary_db, user_ids, date_str, month_str, report_type=ReportType.DAILY):
    if report_type == ReportType.DAILY:
        str_date_filter = f'trade_date = "{date_str}"'
        columns = ('user_id', 'market', 'stock_asset', 'money_asset', 'deal_volume',
        'taker_volume', 'maker_volume',)
        select_fields = (
                'user_id',
                'market',
                'stock_asset',
                'money_asset',
                'SUM(`deal_volume`) AS deal_volume',
                'SUM(`taker_volume`) AS taker_volume',
                'SUM(`maker_volume`) AS maker_volume',
            )
        group_by = "`user_id`, `market`, `stock_asset`, `money_asset`"
    else:
        report_date = datetime.datetime.strptime(date_str, '%Y-%m-%d')
        str_month_start_date, _ = trade_summary_db.convert_date(
            datetime.date(report_date.year, report_date.month, 1))
        str_date_filter = f'trade_date >= "{str_month_start_date}" AND trade_date <= "{date_str}"'
        columns = ('user_id', 'trade_date', 'market', 'stock_asset', 'money_asset', 'deal_volume',
        'taker_volume', 'maker_volume',)
        select_fields = (
                        'user_id',
                        'trade_date',
                        'market',
                        'stock_asset',
                        'money_asset',
                        'deal_volume',
                        'taker_volume',
                        'maker_volume',
                    )
        group_by = None

    str_user_ids = ','.join([str(user_id) for user_id in user_ids])
    where = f' {str_date_filter} AND user_id IN ({str_user_ids})' \
        ' AND deal_amount > 0'
    s = trade_summary_db.table(f'user_trade_summary_{month_str}').select(
        *select_fields,
        where=where,
        group_by=group_by
    )
    records = list(dict(zip(columns, r)) for r in s)
    return records


def get_site_perpetual_trade_type_summary(trade_summary_db, date_str, month_str, asset_price_map):
    direct_trade_columns = ('money_asset', 'maker_amount', 'taker_amount')
    direct_trade_select_fields = (
        'money_asset',
        'SUM(maker_volume) AS maker_amount',
        'SUM(taker_volume) AS taker_amount',
    )
    inverse_trade_columns = ('stock_asset', 'maker_amount', 'taker_amount')
    inverse_trade_select_fields = (
        'stock_asset',
        'SUM(maker_amount) AS maker_amount',
        'SUM(taker_amount) AS taker_amount',
    )
    str_date_filter = f'trade_date = "{date_str}"'

    where = f' {str_date_filter} '
    # 正向合约交易
    s = trade_summary_db.table(f'user_trade_summary_{month_str}').select(
        *direct_trade_select_fields,
        where=where + f' AND market_type = {PerpetualMarketType.DIRECT.value}',
        group_by="`money_asset`"
    )
    direct_records = list(dict(zip(direct_trade_columns, r)) for r in s)
    site_taker_volume_usd = site_maker_volume_usd = Decimal()
    for r in direct_records:
        site_taker_volume_usd += asset_price_map[r['money_asset']] * r['taker_amount']
        site_maker_volume_usd += asset_price_map[r['money_asset']] * r['maker_amount']
    del direct_records

    # 反向合约交易
    s = trade_summary_db.table(f'user_trade_summary_{month_str}').select(
        *inverse_trade_select_fields,
        where=where + f' AND market_type = {PerpetualMarketType.INVERSE.value}',
        group_by="`stock_asset`"
    )
    inverse_records = list(dict(zip(inverse_trade_columns, r)) for r in s)
    for r in inverse_records:
        site_taker_volume_usd += r['taker_amount']
        site_maker_volume_usd += r['maker_amount']
    del inverse_records
    return site_taker_volume_usd, site_maker_volume_usd


def get_user_perpetual_trade_summary(trade_summary_db, user_ids, date_str, month_str, asset_price_map):
    direct_trade_columns = (
        'user_id', 'market', 'stock_asset', 'money_asset', 'deal_amount', 'maker_amount',
        'taker_amount')
    direct_trade_select_fields = (
        'user_id',
        'market',
        'stock_asset',
        'money_asset',
        'SUM(deal_volume) AS deal_amount',
        'SUM(maker_volume) AS maker_amount',
        'SUM(taker_volume) AS taker_amount',
    )
    inverse_trade_columns = (
        'user_id', 'market', 'stock_asset', 'deal_amount', 'maker_amount',
        'taker_amount')
    inverse_trade_select_fields = (
        'user_id',
        'market',
        'stock_asset',
        'SUM(deal_amount) AS deal_amount',
        'SUM(maker_amount) AS maker_amount',
        'SUM(taker_amount) AS taker_amount',
    )
    str_date_filter = f'trade_date = "{date_str}"'
    str_user_ids = ','.join([str(user_id) for user_id in user_ids])

    where = f' {str_date_filter} AND user_id IN ({str_user_ids})' \
        ' AND deal_amount > 0'
    # 正向合约交易
    s = trade_summary_db.table(f'user_trade_summary_{month_str}').select(
        *direct_trade_select_fields,
        where=where + f' AND market_type = {PerpetualMarketType.DIRECT.value}',
        group_by="`user_id`, `market`, `stock_asset`, `money_asset`"
    )
    direct_records = list(dict(zip(direct_trade_columns, r)) for r in s)
    for r in direct_records:
        r['deal_amount'] = asset_price_map[r['money_asset']] * r['deal_amount']
        r['taker_amount'] = asset_price_map[r['money_asset']] * r['taker_amount']
        r['maker_amount'] = asset_price_map[r['money_asset']] * r['maker_amount']
        del r['money_asset']

    # 反向合约交易
    s = trade_summary_db.table(f'user_trade_summary_{month_str}').select(
        *inverse_trade_select_fields,
        where=where +
        f' AND market_type = {PerpetualMarketType.INVERSE.value}',
        group_by="`user_id`, `market`, `stock_asset`"
    )
    inverse_records = list(dict(zip(inverse_trade_columns, r)) for r in s)
    return direct_records + inverse_records


def get_user_trade_fee(trade_summary_db, user_ids, date_str, month_str):
    columns = ('user_id', 'market', 'asset', 'fee', 'taker_fee', 'maker_fee')
    select_fields = (
        'user_id',
        'market',
        'asset',
        'fee',
        'taker_fee',
        'maker_fee'
    )
    str_user_ids = ','.join([str(user_id) for user_id in user_ids])
    where = f' trade_date = "{date_str}" AND user_id IN ({str_user_ids})'
    s = trade_summary_db.table(f'user_fee_summary_{month_str}').select(
        *select_fields,
        where=where,
    )
    records = list(dict(zip(columns, r)) for r in s)
    return records


def get_trade_summary_data(report_date, user_ids, trade_summary_db,
                           sub_user_map):
    coin_rate = AssetPrice.get_close_price_map(report_date)
    date_str, month_str = trade_summary_db.convert_date(report_date)

    monthly_start_date = datetime.date(report_date.year, report_date.month, 1)
    daily_coin_rate_map = AssetPrice.get_close_price_range_map(monthly_start_date, report_date)

    if trade_summary_db == TradeSummaryDB:
        trade_summary_map = list_coin_trade_summary(
            report_date, trade_summary_db)
    elif trade_summary_db == PerpetualSummaryDB:
        trade_summary_map = list_perpetual_coin_trade_summary(
            report_date, trade_summary_db)
    all_value_usd = 0
    for item in trade_summary_map.values():
        if trade_summary_db == TradeSummaryDB:
            item['value_usd'] = item['deal_volume'] * \
                coin_rate.get(item['money_asset'], 0)
        elif trade_summary_db == PerpetualSummaryDB:
            if item['market_type'] == PerpetualMarketType.INVERSE:
                item['value_usd'] = item['deal_amount']
            else:
                item['value_usd'] = item['deal_amount'] * \
                    coin_rate.get(item['stock_asset'], 0)
        all_value_usd += item['value_usd']  # 当日全站总交易市值

    if trade_summary_db == TradeSummaryDB:
        site_taker_value_usd, site_maker_value_usd = get_site_spot_trade_type_summary(
            trade_summary_db, date_str, month_str, coin_rate)
        trade_records = get_user_spot_trade_summary(trade_summary_db, user_ids,
                                                    date_str, month_str)
        monthly_trade_records = get_user_spot_trade_summary(trade_summary_db, user_ids,
                                                            date_str, month_str, ReportType.MONTHLY)
    else:
        site_taker_value_usd, site_maker_value_usd = get_site_perpetual_trade_type_summary(
            trade_summary_db, date_str, month_str, coin_rate)
        trade_records = get_user_perpetual_trade_summary(trade_summary_db, user_ids,
                                                         date_str, month_str, coin_rate)
        monthly_trade_records = []
        start, end = monthly_start_date, report_date
        while start <= end:
            tmp = get_user_perpetual_trade_summary(trade_summary_db, 
                                                   user_ids, 
                                                   start.strftime('%Y-%m-%d'), 
                                                   month_str, 
                                                   daily_coin_rate_map[start])
            start += datetime.timedelta(days=1)
            monthly_trade_records.extend(tmp)
    site_taker_maker_value_usd = site_maker_value_usd + site_taker_value_usd
    fee_records = get_user_trade_fee(trade_summary_db, user_ids, date_str,
                                     month_str)
    user_fee_map = defaultdict(dict)
    for record in fee_records:
        rate = coin_rate.get(record['asset'], 0)
        if record['market'] in user_fee_map[record['user_id']]:
            user_fee_map[record['user_id']][record['market']]['fee_usd'] += \
                record['fee'] * rate
            user_fee_map[record['user_id']][record['market']]['taker_fee_usd'] \
                += record['taker_fee'] * rate
            user_fee_map[record['user_id']][record['market']]['maker_fee_usd'] \
                += record['maker_fee'] * rate
        else:
            user_fee_map[record['user_id']][record['market']] = {
                'market': record['market'],
                'fee_usd': record['fee'] * rate,
                'taker_fee_usd': record['taker_fee'] * rate,
                'maker_fee_usd': record['maker_fee'] * rate,
            }

    user_market_map = defaultdict(dict)

    monthly_user_market_map = defaultdict(lambda: defaultdict(dict))
    for record in trade_records:
        deal_value_usd = record['deal_volume'] * coin_rate.get(record['money_asset'], 0)\
            if trade_summary_db == TradeSummaryDB else record['deal_amount']
        maker_value_usd = record['maker_volume'] * coin_rate.get(record['money_asset'], 0)\
            if trade_summary_db == TradeSummaryDB else record['maker_amount']
        taker_value_usd = record['taker_volume'] * coin_rate.get(record['money_asset'], 0)\
            if trade_summary_db == TradeSummaryDB else record['taker_amount']

        user_market_map[record['user_id']][record['market']] = {
            'market': record['market'],
            'deal_value_usd': deal_value_usd,
            'maker_value_usd': maker_value_usd,
            'taker_value_usd': taker_value_usd,
            'monthly_deal_value_usd': 0,
            'monthly_maker_value_usd': 0,
            'monthly_taker_value_usd': 0,
            'date': date_str,
        }

    for record in monthly_trade_records:
        user_id, market = record['user_id'], record['market']
        if trade_summary_db == TradeSummaryDB:
            rate = coin_rate.get(record['money_asset'], 0)
            coin_rate = daily_coin_rate_map[record['trade_date']]
            deal_value_usd = record['deal_volume'] * rate
            maker_value_usd = record['maker_volume'] * rate
            taker_value_usd = record['taker_volume'] * rate
        else:
            deal_value_usd, maker_value_usd, taker_value_usd = \
                record['deal_amount'], record['maker_amount'], record['taker_amount']
        if not monthly_user_market_map[user_id][market]:
            monthly_user_market_map[user_id][market] = dict()
            monthly_user_market_map[user_id][market]['market'] = market
            monthly_user_market_map[user_id][market]['deal_value_usd'] = deal_value_usd
            monthly_user_market_map[user_id][market]['maker_value_usd'] = maker_value_usd
            monthly_user_market_map[user_id][market]['taker_value_usd'] = taker_value_usd
        else:
            monthly_user_market_map[user_id][market]['deal_value_usd'] += deal_value_usd
            monthly_user_market_map[user_id][market]['maker_value_usd'] += maker_value_usd
            monthly_user_market_map[user_id][market]['taker_value_usd'] += taker_value_usd
    # 当月taker交易量, maker交易量, 总交易量
    for user_id in monthly_user_market_map:        
        # 当天该账号无交易
        if not user_market_map.get(user_id):
            user_market_map[user_id] = dict()
            for market in monthly_user_market_map[user_id].keys():
                user_market_map[user_id][market] = dict()
                user_market_map[user_id][market]['date'] = date_str
                user_market_map[user_id][market]['market'] = market
                user_market_map[user_id][market]['deal_value_usd'] = \
                    user_market_map[user_id][market]['maker_value_usd'] = \
                        user_market_map[user_id][market]['taker_value_usd'] = Decimal()
        
        for market, item in monthly_user_market_map[user_id].items():
            if market not in user_market_map[user_id]:
                user_market_map[user_id][market] = dict(
                    market=market,
                    date=date_str,
                    deal_value_usd=Decimal(),
                    maker_value_usd=Decimal(),
                    taker_value_usd=Decimal()
                )
            user_market_map[user_id][market]['monthly_deal_value_usd'] = \
                item.get('deal_value_usd', 0)
            user_market_map[user_id][market]['monthly_maker_value_usd'] = \
                item.get('maker_value_usd', 0)
            user_market_map[user_id][market]['monthly_taker_value_usd'] = \
                item.get('taker_value_usd', 0)
    
    for sub_user_id, main_user_id in sub_user_map.items():
        # 整合到主账号
        if sub_user_id in user_market_map:
            sub_user_data = user_market_map.pop(sub_user_id)
            for sub_market, sub_trade_data in sub_user_data.items():
                if sub_market in user_market_map[main_user_id]:
                    user_market_map[main_user_id][sub_market]['deal_value_usd'] += \
                        sub_trade_data['deal_value_usd']
                    user_market_map[main_user_id][sub_market]['maker_value_usd'] += \
                        sub_trade_data['maker_value_usd']
                    user_market_map[main_user_id][sub_market]['taker_value_usd'] += \
                        sub_trade_data['taker_value_usd']
                    if 'monthly_deal_value_usd' not in user_market_map[main_user_id][sub_market]:
                        user_market_map[main_user_id][sub_market]['monthly_deal_value_usd'] = 0
                    if 'monthly_maker_value_usd' not in user_market_map[main_user_id][sub_market]:
                        user_market_map[main_user_id][sub_market]['monthly_maker_value_usd'] = 0
                    if 'monthly_taker_value_usd' not in user_market_map[main_user_id][sub_market]:
                        user_market_map[main_user_id][sub_market]['monthly_taker_value_usd'] = 0
                    user_market_map[main_user_id][sub_market]['monthly_deal_value_usd'] += \
                        sub_trade_data.get('monthly_deal_value_usd', 0)
                    user_market_map[main_user_id][sub_market]['monthly_maker_value_usd'] += \
                        sub_trade_data.get('monthly_maker_value_usd', 0)
                    user_market_map[main_user_id][sub_market]['monthly_taker_value_usd'] += \
                        sub_trade_data.get('monthly_taker_value_usd', 0)
                else:
                    user_market_map[main_user_id][sub_market] = sub_trade_data

        if sub_user_id in user_fee_map:
            sub_user_fee_data = user_fee_map.pop(sub_user_id)
            for sub_market, sub_trade_fee_data in sub_user_fee_data.items():
                if sub_market in user_fee_map[main_user_id]:
                    user_fee_map[main_user_id][sub_market]['fee_usd'] += \
                        sub_trade_fee_data['fee_usd']
                    user_fee_map[main_user_id][sub_market]['taker_fee_usd'] += \
                        sub_trade_fee_data['taker_fee_usd']
                    user_fee_map[main_user_id][sub_market]['maker_fee_usd'] += \
                        sub_trade_fee_data['maker_fee_usd']
                else:
                    user_fee_map[main_user_id][sub_market] = sub_trade_fee_data

    for user_id, item in user_market_map.items():
        all_user_value_usd = sum(
            [item['deal_value_usd'] for item in item.values()])
        for market, market_data in item.items():
            market_data['fee'] = amount_to_str(
                user_fee_map[user_id][market]['fee_usd']
                if market in user_fee_map[user_id] else 0, 2)
            market_data['p_all_user_deal_value_usd'] = amount_to_str(
                market_data['deal_value_usd'] / all_user_value_usd * 100
                if all_user_value_usd else 0, 2) + '%'
            market_data['p_all_deal_value_usd'] = amount_to_str(
                market_data['deal_value_usd'] / trade_summary_map[market]['value_usd'] * 100 \
                    if market in trade_summary_map and trade_summary_map[market]['value_usd'] > 0 else 0, 2) + '%'
            market_data['value_usd'] = amount_to_str(
                market_data['deal_value_usd'], 2)
            market_data['str_maker_value_usd'] = amount_to_str(
                market_data['maker_value_usd'], 2)
            market_data['str_taker_value_usd'] = amount_to_str(
                market_data['taker_value_usd'], 2)
            market_data['str_monthly_deal_value_usd'] = amount_to_str(
                market_data['monthly_deal_value_usd'], 2)
            market_data['str_monthly_maker_value_usd'] = amount_to_str(
                market_data['monthly_maker_value_usd'], 2)
            market_data['str_monthly_taker_value_usd'] = amount_to_str(
                market_data['monthly_taker_value_usd'], 2)

    user_trade_all_value_map = {}
    for user_id, item in user_market_map.items():
        all_deal_value_usd = sum([item['deal_value_usd']
                                 for item in item.values()])
        all_maker_value_usd = sum([item['maker_value_usd']
                                  for item in item.values()])
        all_taker_value_usd = sum([item['taker_value_usd']
                                  for item in item.values()])
        all_monthly_deal_value_usd = sum(
            x['monthly_deal_value_usd'] for x in item.values())
        all_monthly_maker_value_usd = sum(
            x['monthly_maker_value_usd'] for x in item.values())
        all_monthly_taker_value_usd = sum(
            x['monthly_taker_value_usd'] for x in item.values())
        all_deal_fee_usd = sum([item['fee_usd']
                               for item in user_fee_map[user_id].values()])
        all_maker_fee_usd = sum([item['maker_fee_usd']
                                for item in user_fee_map[user_id].values()])
        all_taker_fee_usd = sum([item['taker_fee_usd']
                                for item in user_fee_map[user_id].values()])
        p_maker_value_usd = amount_to_str(
            all_maker_value_usd / all_deal_value_usd * 100 if all_deal_value_usd else 0, 2)
        p_all_maker_value_usd = amount_to_str(
            all_maker_value_usd / all_value_usd * 100 if all_value_usd else 0, 2)
        p_all_deal_value_usd = amount_to_str(
            all_deal_value_usd / all_value_usd * 100 if all_value_usd else 0, 2)
        p_site_taker_maker_value_usd = amount_to_str(
            (all_maker_value_usd + all_taker_value_usd) / site_taker_maker_value_usd * 100
            if site_taker_maker_value_usd else 0, 2)
        p_site_taker_value_usd = amount_to_str(
            all_taker_value_usd / site_taker_value_usd * 100
            if site_taker_value_usd else 0, 2)
        p_site_maker_value_usd = amount_to_str(
            all_maker_value_usd / site_maker_value_usd * 100
            if site_maker_value_usd else 0, 2)

        user_trade_all_value_map[user_id] = {
            'all_deal_value_usd': amount_to_str(all_deal_value_usd, 2),
            'all_maker_value_usd': amount_to_str(all_maker_value_usd, 2),
            'all_taker_value_usd': amount_to_str(all_taker_value_usd, 2),
            'all_monthly_deal_value_usd': amount_to_str(all_monthly_deal_value_usd, 2),
            'all_monthly_maker_value_usd': amount_to_str(all_monthly_maker_value_usd, 2),
            'all_monthly_taker_value_usd': amount_to_str(all_monthly_taker_value_usd, 2),
            'all_deal_fee_usd': amount_to_str(all_deal_fee_usd, 2),
            'all_maker_fee_usd': amount_to_str(all_maker_fee_usd, 2),
            'all_taker_fee_usd': amount_to_str(all_taker_fee_usd, 2),
            'p_all_deal_value_usd': p_all_deal_value_usd + '%',
            # 相减处理精度问题
            'p_all_taker_value_usd': str(round(float(p_all_deal_value_usd) - float(p_all_maker_value_usd), 2)) + '%',
            'p_all_maker_value_usd': p_all_maker_value_usd + '%',
            'p_deal_value_usd': '100%',  # 账户比例
            'p_maker_value_usd': p_maker_value_usd + '%',  # 账户比例
            # 账户比例
            'p_taker_value_usd': str(round(100 - float(p_maker_value_usd), 2)) + '%',
            'p_site_taker_maker_value_usd': p_site_taker_maker_value_usd + '%',  # 全站比例
            'p_site_taker_value_usd': p_site_taker_value_usd + '%',  # 全站比例
            'p_site_maker_value_usd': p_site_maker_value_usd + '%',  # 全站比例
        }
    format_user_trade_all_value_map = defaultdict(list)
    for user_id, item in user_trade_all_value_map.items():
        format_user_trade_all_value_map[user_id] = [
            dict(type='ALL',
                 value_usd=item['all_deal_value_usd'],
                 monthly_value_usd=item['all_monthly_deal_value_usd'],
                 proportion=item['p_deal_value_usd'],
                 all_proportion=item['p_all_deal_value_usd'],
                 site_proportion=item['p_site_taker_maker_value_usd'],
                 date=date_str,
                 fee=item['all_deal_fee_usd'],
                 ),
            dict(type='Taker',
                 value_usd=item['all_taker_value_usd'],
                 monthly_value_usd=item['all_monthly_taker_value_usd'],
                 proportion=item['p_taker_value_usd'],
                 all_proportion=item['p_all_taker_value_usd'],
                 site_proportion=item['p_site_taker_value_usd'],
                 date=date_str,
                 fee=item['all_taker_fee_usd'],
                 ),
            dict(type='Maker',
                 value_usd=item['all_maker_value_usd'],
                 monthly_value_usd=item['all_monthly_maker_value_usd'],
                 proportion=item['p_maker_value_usd'],
                 all_proportion=item['p_all_maker_value_usd'],
                 site_proportion=item['p_site_maker_value_usd'],
                 date=date_str,
                 fee=item['all_maker_fee_usd'],
                 ),
        ]

    return user_market_map, format_user_trade_all_value_map


def get_market_maker_ranking_data(spot_trade_map, perpetual_trade_map, report_date):

    result = defaultdict(list)
    # 用for循环处理spot, perpetual
    for type_, trade_amount_map, min_amount in ((MarketMaker.MakerType.SPOT, spot_trade_map, MIN_SPOT_MARKET_MAKER_RANKING_AMOUNT),
                             (MarketMaker.MakerType.PERPETUAL, perpetual_trade_map, MIN_PERPETUAL_MARKET_MAKER_RANKING_AMOUNT)):
        mm_checker = Checker(type_)
        
        appraisal_user_ids = set(mm_checker.all_appraisal_mmer_user_ids)
        
        user_level_map = \
            mm_checker.get_market_maker_level_map({k: v for k, v in trade_amount_map.items() if k in appraisal_user_ids})

        level_last_amount_map = defaultdict(Decimal)  # 每个级别最后一名用户的交易量
        level_first_amount_map = defaultdict(Decimal)  # 每个级别第一名用户的交易量
        mm_dict = SPOT_MARKET_MAKER_APPRAISAL_MAP if type_ == MarketMaker.MakerType.SPOT \
            else PERPETUAL_MARKET_MAKER_APPRAISAL_MAP
        for user_id, level in user_level_map.items():
            user_amount = trade_amount_map.get(user_id, Decimal())
            level_last_amount_map[level] = min(level_last_amount_map.get(level, user_amount), user_amount)
            level_first_amount_map[level] = max(level_first_amount_map.get(level, user_amount), user_amount)
        for user_id, level in user_level_map.items():
            next_level = level + 1
            previous_level = level - 1
            next_level_least_amount = min([v for k, v in level_last_amount_map.items() \
                if k >= next_level] or [0])
            user_amount = trade_amount_map.get(user_id, Decimal())
            next_level_amount_delta = amount_to_str(max(0, next_level_least_amount - user_amount), 2)
            if previous_level <= 0:
                previous_level_amount_delta = '/'
            else:
                previous_level_most_amount = max([v for k, v in level_first_amount_map.items() if k <= previous_level] or [0])
                previous_level_amount_delta = amount_to_str(
                    max(0, user_amount - previous_level_most_amount), 2)
            lang = UserPreferences(user_id).language.value
            with force_locale(lang):
                type_str = _('现货做市') if type_ == MarketMaker.MakerType.SPOT else _('合约做市')
                ranking_str = _(mm_dict[level]['ranking'])
            result[user_id].append(dict(
                date=report_date.strftime('%Y-%m-%d'),
                type=type_str,
                trade_usd=amount_to_str(user_amount, 2),
                ranking=ranking_str,
                level=f'LV{level}',
                next_level_trade_amount_delta_usd=next_level_amount_delta,
                previous_level_trade_amount_delta_usd=previous_level_amount_delta
            ))
    
        # 处理未达最低要求的用户
        for user_id, user_amount in trade_amount_map.items():
            if user_id in user_level_map or user_id not in appraisal_user_ids:
                continue
            lang = UserPreferences(user_id).language.value
            with force_locale(lang):
                type_str = _('现货做市') if type_ == MarketMaker.MakerType.SPOT else _('合约做市')
            next_level_amount_delta = min_amount - user_amount
            result[user_id].append(dict(
                date=report_date.strftime('%Y-%m-%d'),
                type=type_str,
                trade_usd=amount_to_str(user_amount, 2),
                ranking='/',
                level='/',
                next_level_trade_amount_delta_usd=amount_to_str(next_level_amount_delta, 2),
                previous_level_trade_amount_delta_usd='/'
            ))
    
        # 处理不参与考核的特殊用户
        no_appraisal_user_ids = set(mm_checker.all_mmer_user_ids) - appraisal_user_ids
        for user_id in no_appraisal_user_ids:
            lang = UserPreferences(user_id).language.value
            with force_locale(lang):
                type_str = _('现货做市') if type_ == MarketMaker.MakerType.SPOT else _('合约做市')
            result[user_id].append(dict(
                date=report_date.strftime('%Y-%m-%d'),
                type=type_str,
                trade_usd=amount_to_str(trade_amount_map.get(user_id, 0), 2),
                ranking='/',
                level='/',
                next_level_trade_amount_delta_usd='/',
                previous_level_trade_amount_delta_usd='/'
            ))
    return result


def _send_trade_summary_by_emails(emails: List[str], **kwargs: dict):
    for email in emails:
        send_user_trade_summary_email(email=email, **kwargs)


def push_market_maker_trade_report(report_date, 
                                   send_user_ids: List[int] = None,
                                   email_map: Dict[int, str] = None):
    if not (TradeSummaryDB.is_data_completed(
            report_date) and PerpetualSummaryDB.is_data_completed(report_date)):
        raise Exception('{} dump_history 数据还未同步完成'.format(report_date))
    spot_market_maker_list, perpetual_market_maker_list, inner_market_market_list, user_email_map = get_user_data()
    user_ids = list(user_email_map.keys())
    sub_user_map = {user.user_id: user.main_user_id
                    for user in SubAccount.query.filter(SubAccount.main_user_id.in_(user_ids)).all()}
    user_ids.extend(sub_user_map.keys())

    perpetual_format_user_market_map, perpetual_format_user_trade_all_value_map = get_trade_summary_data(
        report_date, user_ids, PerpetualSummaryDB, sub_user_map)
    spot_format_user_market_map, spot_format_user_trade_all_value_map = get_trade_summary_data(
        report_date, user_ids, TradeSummaryDB, sub_user_map)

    spot_market_maker_user_set = set(spot_market_maker_list + inner_market_market_list)
    perpetual_market_maker_user_set = set(perpetual_market_maker_list + inner_market_market_list)

    spot_value_map = {k: Decimal(v[0]['monthly_value_usd']) \
        for k, v in spot_format_user_trade_all_value_map.items() if k in spot_market_maker_user_set}
    for id_ in spot_market_maker_user_set:
        if id_ not in spot_value_map:
            spot_value_map[id_] = Decimal(0)

    perpetual_value_map = {k: Decimal(v[0]['monthly_value_usd']) \
        for k, v in perpetual_format_user_trade_all_value_map.items() if k in perpetual_market_maker_user_set}
    for id_ in perpetual_market_maker_user_set:
        if id_ not in perpetual_value_map:
            perpetual_value_map[id_] = Decimal(0)
    market_maker_ranking_map = get_market_maker_ranking_data(  # 考核过滤内部做市商
        {k: v for k, v in spot_value_map.items() if k not in inner_market_market_list},
        {k: v for k, v in perpetual_value_map.items() if k not in inner_market_market_list},
        report_date)

    user_extra_send_emails_map = defaultdict(list)
    for ch_user_ids in batch_iter(user_email_map.keys(), 1000):
        ch_report_emails = MarketMakerReportSendEmail.query.filter(
            MarketMakerReportSendEmail.user_id.in_(ch_user_ids),
            MarketMakerReportSendEmail.status == MarketMakerReportSendEmail.Status.VALID,
        ).all()
        for r in ch_report_emails:
            user_extra_send_emails_map[r.user_id].append(r.email)

    str_report_date = report_date.strftime('%Y-%m-%d')
    for user_id, email in user_email_map.items():
        if send_user_ids:
            if user_id not in send_user_ids:
                continue
        user = User.query.get(user_id)
        if email_map:
            email = email_map.get(user_id, email)
        current_app.logger.info(
            f'{str_report_date} daily market maker trade report {user_id} {email}')
        data = dict()
        if market_maker_ranking_data := market_maker_ranking_map.get(user_id):
            data['market_maker_ranking_data'] = market_maker_ranking_data
        # 现货市场
        if user_id in spot_market_maker_user_set and \
            (spot_user_trade_data := spot_format_user_market_map.get(user_id)):
            spot_user_trade_data = {k: v for k, v in spot_user_trade_data.items() if Decimal(v['value_usd']) > 0}
            if spot_user_trade_data:
                data['spot_user_trade_data'] = list(spot_user_trade_data.values())
        # 现货交易类型
        if user_id in spot_market_maker_user_set and \
            (spot_user_trade_type_data := spot_format_user_trade_all_value_map.get(user_id)):
            if Decimal(spot_user_trade_type_data[0]['value_usd']) > Decimal():
                data['spot_user_trade_type_data'] = spot_user_trade_type_data
        # 合约市场
        if user_id in perpetual_market_maker_user_set and \
            (perpetual_user_trade_data := perpetual_format_user_market_map.get(user_id)):
            perpetual_user_trade_data = {k: v for k, v in perpetual_user_trade_data.items() if Decimal(v['value_usd']) > 0}
            if perpetual_user_trade_data:
                data['perpetual_user_trade_data'] = list(perpetual_user_trade_data.values())
        # 合约交易类型
        if user_id in perpetual_market_maker_user_set and \
            (perpetual_user_trade_type_data := perpetual_format_user_trade_all_value_map.get(user_id)):
            if Decimal(perpetual_user_trade_type_data[0]['value_usd']) > Decimal():
                data['perpetual_user_trade_type_data'] = perpetual_user_trade_type_data
        lang = UserPreferences(user_id).language.value
        with force_locale(lang):
            title = render_template_string(_("【CoinEx】做市商日报（{{ report_date }}）"), report_date=str_report_date)
        if not data:
            continue

        to_send_emails = [email]
        to_send_emails.extend(user_extra_send_emails_map[user_id])
        data['export_url'] = get_export_url(data, lang)
        if user_id in spot_market_maker_list and user_id in perpetual_market_maker_list:
            # 没有交易过的话就不发送了
            if user_id in spot_format_user_market_map or user_id in perpetual_format_user_market_map:
                _send_trade_summary_by_emails(
                    emails=to_send_emails,
                    title=title,
                    report_date=str_report_date,
                    lang=lang,
                    name=user.name_displayed,
                    **data
                )
                # 特殊配置写死，两个账号是合约现货做市商
                if email in ['<EMAIL>', '<EMAIL>']:
                    send_user_trade_summary_email(
                        email='<EMAIL>',
                        title=f'{title}, email:{email}',
                        report_date=str_report_date,
                        lang=lang,
                        name=user.name_displayed,
                        **data
                    )
        elif user_id in spot_market_maker_list:
            if user_id in spot_format_user_market_map:
                _send_trade_summary_by_emails(
                    emails=to_send_emails,
                    lang=lang,
                    title=title,
                    report_date=str_report_date,
                    name=user.name_displayed,
                    **data
                )
        elif user_id in perpetual_market_maker_list:
            if user_id in perpetual_format_user_market_map:
                _send_trade_summary_by_emails(
                    emails=to_send_emails,
                    title=title,
                    report_date=str_report_date,
                    lang=lang,
                    name=user.name_displayed,
                    **data
                )
                # 特殊配置写死
                if email in ['<EMAIL>']:
                    for extra_email in ['<EMAIL>', '<EMAIL>', '<EMAIL>']:
                        send_user_trade_summary_email(
                            email=extra_email,
                            title=f'{title}, email:{email}',
                            report_date=str_report_date,
                            lang=lang,
                            name=user.name_displayed,
                            **data
                        )
        elif user_id in inner_market_market_list:
            _send_trade_summary_by_emails(
                emails=to_send_emails,
                lang=lang,
                title=title,
                report_date=str_report_date,
                name=user.name_displayed,
                **data
            )
        else:
            current_app.logger.info(
                f'{str_report_date} NOT SENDING daily market maker trade report {user_id} {email}')


@scheduled(crontab(minute='30', hour='4-6'))
@lock_call()
def push_market_maker_trade_report_schedule():

    # 以下用户发送到指定邮箱
    SPECIAL_EMAIL_MAP = {
        29102: '<EMAIL>'
    }

    today = datetime.datetime.utcnow().date()
    report_date = today - datetime.timedelta(days=1)
    cache = DailyMarketMakerReportCache()
    last_date = cache.read()
    if last_date and report_date <= str_to_datetime(last_date).date():
        return

    push_market_maker_trade_report(report_date, email_map=SPECIAL_EMAIL_MAP)
    cache.set(report_date.strftime('%Y-%m-%d'))
