# -*- coding: utf-8 -*-

import datetime
from collections import defaultdict
from decimal import Decimal

from celery.schedules import crontab
from sqlalchemy import func

from app.business import lock_call
from app.business.summary import (
    get_period_spot_trade_mapping,
    get_period_perp_trade_mapping,
    get_period_spot_trade_fee_mapping,
    get_period_perp_trade_fee_mapping,
)
from app.common import CeleryQueues
from app.common.constants import PrecisionEnum
from app.models import (
    Deposit,
    KolReferralExport,
    KolReferralExportReport,
    Referral,
    ReferralHistory,
    User,
    db,
)
from app.models.referral import ReferralAssetHistory
from app.models.wallet import AssetPrice
from app.schedules.reports.admin_async_download import async_download_kol_referral_report
from app.utils import batch_iter, route_module_to_celery_queue, scheduled
from app.utils.amount import quantize_amount

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def export_kol_referral_report(start_time, end_time, ref_id, user_ids, type_):
    # 兼容历史数据
    if not type_:
        type_ = KolReferralExport.Type.NEW_USER

    start_date, end_date = start_time.date(), end_time.date() + datetime.timedelta(days=1)
    # 用户的邀请数据
    if type_ == KolReferralExport.Type.NEW_USER:
        invitee_user_map = get_invitee_user(start_date, end_date, user_ids)
    elif type_ == KolReferralExport.Type.ALL_USER:
        invitee_user_map = get_invitee_user(None, None, user_ids)
    else:
        pass

    user_email_map = {}
    for ids_ in batch_iter(user_ids, 1000):
        chunk_users = (
            User.query.filter(User.id.in_(ids_))
            .with_entities(User.id, User.email)
            .all()
        )
        user_email_map.update(dict(chunk_users))

    user_refer_code_map = {}
    for ids_ in batch_iter(user_ids, 1000):
        chunk_referrals = (
            Referral.query.filter(Referral.user_id.in_(ids_))
            .with_entities(Referral.user_id, Referral.code)
            .all()
        )
        user_refer_code_map.update(dict(chunk_referrals))
    
    refer_reward_data = query_refer_reward(start_date, end_date, user_ids)
    for user_id in user_ids:
        invitee_users = invitee_user_map.get(user_id, [])
            
        trade_data = query_referree_trade_data(
            start_date, end_date, list(invitee_users)
        )
        deposit_data = query_referree_deposit_data(
            start_date, end_date, list(invitee_users)
        )
        report = KolReferralExportReport.get_or_create(ref_id=ref_id, user_id=user_id)
        report.email = user_email_map.get(user_id, "") or ""
        report.referral_code = user_refer_code_map.get(user_id, "") or ""

        report.referral_user_count = len(invitee_users)
        report.referral_trading_user_count = trade_data["referral_trading_user_count"]
        report.referral_depositing_user_count = len(deposit_data)
        report.referral_deposit_amount = sum(deposit_data.values())
        report.referral_spot_trading_volume = trade_data["referral_spot_trading_volume"]
        report.referral_spot_trading_fee = trade_data["referral_spot_trading_fee"]
        report.referral_perpetual_trading_volume = trade_data[
            "referral_perpetual_trading_volume"
        ]
        report.referral_perpetual_trading_fee = trade_data[
            "referral_perpetual_trading_fee"
        ]
        report.referral_reward = refer_reward_data[user_id]
        db.session.add(report)
        db.session.commit()


def query_referree_trade_data(start_date, end_date, inivitee_users):

    spot_data, perp_data = 0, 0
    spot_trade_user_ids, perp_trade_user_ids = set(), set()
    for _ids in batch_iter(inivitee_users, 1000):
        spot_trade_map = get_period_spot_trade_mapping(start_date, end_date, _ids)
        spot_data += sum(spot_trade_map.values())
        perp_trade_map = get_period_perp_trade_mapping(start_date, end_date, _ids)
        perp_data += sum(perp_trade_map.values())

        spot_trade_user_ids |= set(spot_trade_map.keys())
        perp_trade_user_ids |= set(perp_trade_map.keys())

    spot_fee_data, perp_fee_data = 0, 0
    for _ids in batch_iter(inivitee_users, 1000):
        spot_trade_fee_map = get_period_spot_trade_fee_mapping(start_date, end_date, _ids)
        spot_fee_data += sum(spot_trade_fee_map.values())
        perp_trade_fee_map = get_period_perp_trade_fee_mapping(start_date, end_date, _ids)
        perp_fee_data += sum(perp_trade_fee_map.values())

    trade_user_ids = spot_trade_user_ids | perp_trade_user_ids
    _new_trade_users = trade_user_ids
    return {
        "referral_trading_user_count": len(_new_trade_users),
        "referral_spot_trading_volume": spot_data,
        "referral_spot_trading_fee": spot_fee_data,
        "referral_perpetual_trading_volume": perp_data,
        "referral_perpetual_trading_fee": perp_fee_data,
    }


def query_referree_deposit_data(start_date, end_date, ambassador_ids):
    deposits = (
        Deposit.query.filter(
            Deposit.created_at >= start_date,
            Deposit.created_at < end_date,
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.user_id.in_(ambassador_ids),
        )
        .with_entities(
            Deposit.user_id,
            Deposit.asset,
            func.date(Deposit.created_at).label("create_date"),
            func.sum(Deposit.amount).label("total_amount"),
        )
        .group_by(Deposit.user_id, Deposit.asset, func.date(Deposit.created_at))
        .all()
    )
    result = defaultdict(Decimal)

    asset_rates = AssetPrice.get_close_price_range_map(start_date, end_date)
    for v in deposits:
        map_ = asset_rates.get(v.create_date)
        if not map_:
            continue
        result[v.user_id] += v.total_amount * map_.get(v.asset, Decimal())
    return result

def query_refer_reward(start_date, end_date, user_ids):
    records = ReferralAssetHistory.query.filter(
        ReferralAssetHistory.date >= start_date,
        ReferralAssetHistory.date < end_date,
        ReferralAssetHistory.user_id.in_(user_ids),
        ReferralAssetHistory.status == ReferralAssetHistory.Status.FINISHED,
    ).with_entities(ReferralAssetHistory.date,
                    ReferralAssetHistory.user_id,
                    ReferralAssetHistory.asset, 
                    ReferralAssetHistory.amount).all()

    price_range_map = AssetPrice.get_close_price_range_map(start_date, end_date)

    result = defaultdict(Decimal)
    for r in records:
        price = price_range_map.get(r.date, {}).get(r.asset, 0)
        result[r.user_id] += quantize_amount(price * r.amount, PrecisionEnum.CASH_PLACES)
    return result


def get_invitee_user(start_date, end_date, ambassador_ids):
    q = ReferralHistory.query.filter(
            ReferralHistory.referrer_id.in_(ambassador_ids),
            ReferralHistory.status == ReferralHistory.Status.VALID,
        ).with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
        )
    
    if start_date:
        q = q.filter(ReferralHistory.created_at >= start_date)
    if end_date:
        q = q.filter(ReferralHistory.created_at < end_date)

    referrer_data = q.all()
    result = defaultdict(set)
    for referrer_id, referree_id in referrer_data:
        result[referrer_id].add(referree_id)
    return result


@scheduled(crontab(minute="*/5"))
@lock_call()
def export_kol_referral_report_task():
    records = KolReferralExport.query.filter(
        KolReferralExport.status == KolReferralExport.Status.PROCESSING
    ).order_by(KolReferralExport.id.asc())

    for record in records:
        try:
            export_kol_referral_report(
                record.start_time, record.end_time, record.id, record.cached_user_ids, record.type
            )
            record.status = KolReferralExport.Status.FINISHED
        except Exception:
            raise

        user_emails = (
            User.query.filter(User.id == record.admin_user_id)
            .with_entities(User.id, User.email)
            .all()
        )
        user_email_dict = dict(user_emails)

        if user_email_dict:
            async_download_kol_referral_report.delay(
                user_email_dict[record.admin_user_id],
                record.id,
            )
        db.session.commit()
