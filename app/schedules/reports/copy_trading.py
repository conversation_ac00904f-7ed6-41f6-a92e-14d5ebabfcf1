# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from datetime import date, datetime, timedelta
from typing import List, Dict

from pyroaring import BitMap
from sqlalchemy import func, or_

from app.business import (
    route_module_to_celery_queue,
    CeleryQueues,
    scheduled,
    crontab,
    lock_call,
)
from app.models import db, AssetPrice, DailyCopyTradingSiteReport, MonthlyCopyTradingSiteReport
from app.models.copy_trading import (
    CopyTraderUser, CopyFollowerHistory, CopyTraderProfitShareDetail,
    DailyCopyTraderStatistics,
)
from app.business import PerpetualLogDB, PerpetualHistoryDB
from app.utils import today, quantize_amount, next_month, batch_iter
from app.utils.date_ import date_to_datetime
from app.schedules.reports.utils import get_monthly_report_date


route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def get_users_per_trade_map(
    user_ids: set[int],
    start_time: datetime,
    end_time: datetime,
) -> tuple[dict[int, int], dict[int, Decimal]]:
    user_deal_usd_map = defaultdict(Decimal)
    user_deal_count_map = defaultdict(int)
    start_ts = int(start_time.timestamp())
    end_ts = int(end_time.timestamp())
    dbs_tables = PerpetualHistoryDB.users_to_dbs_and_tables(user_ids, 'deal_history')
    for db_tables in dbs_tables:
        _db = db_tables[0]
        for _table, _table_user_ids in db_tables[1].items():
            for ch_user_ids in batch_iter(_table_user_ids, 5000):
                user_ids_str = ','.join(map(str, ch_user_ids))
                where = f' `user_id` in ({user_ids_str})  AND `time` >= {start_ts} AND `time` < {end_ts} '
                records = _db.table(_table).select(
                    *["user_id", "SUM(`deal_stock`) as `deal_usd`"],
                    where=where,
                    group_by="user_id"
                )
                for row in records:
                    user_deal_usd_map[row[0]] = quantize_amount(row[1], 2)
                    user_deal_count_map[row[0]] += 1
    return user_deal_count_map, user_deal_usd_map


def get_has_pending_position_user_ids(user_ids: set[int], slice_time: datetime) -> set[int]:
    slice_ts = int(slice_time.timestamp())
    table = PerpetualLogDB.slice_position_table(slice_ts, interval=600)
    fields = ['DISTINCT(`user_id`)']
    rows = []
    for ch_user_ids in batch_iter(user_ids, 5000):
        user_ids_str = ','.join(map(str, ch_user_ids))
        where = f' `user_id` in ({user_ids_str}) '
        ch_rows = table.select(*fields, where=where)
        rows.extend(ch_rows)
    return {x[0] for x in rows}


def build_daily_copy_trading_report_data(start_date: date, end_date: date) -> Dict[str, Dict]:
    zero = Decimal()
    end_time = date_to_datetime(end_date)

    trader_rows = CopyTraderUser.query.filter(
        CopyTraderUser.status.in_([CopyTraderUser.Status.ACTIVE, CopyTraderUser.Status.INACTIVE]),
        CopyTraderUser.created_at < end_date,
    ).with_entities(
        CopyTraderUser.user_id,
    ).all()

    follow_his_rows = CopyFollowerHistory.query.filter(
        CopyFollowerHistory.created_at < end_date,
        or_(
            CopyFollowerHistory.finished_at > start_date,
            CopyFollowerHistory.finished_at.is_(None),
        ),
    ).all()

    share_summary_row = CopyTraderProfitShareDetail.query.filter(
        CopyTraderProfitShareDetail.date == start_date,
    ).group_by(
        CopyTraderProfitShareDetail.asset,
    ).with_entities(
        CopyTraderProfitShareDetail.asset,
        func.sum(CopyTraderProfitShareDetail.amount).label("amount"),
    ).all()
    price_map = AssetPrice.get_close_price_map(start_date)
    profit_share_amount = zero
    for r in share_summary_row:
        profit_share_amount += price_map.get(r.asset, zero) * r.amount

    daily_trader_st_summary = DailyCopyTraderStatistics.query.filter(
        DailyCopyTraderStatistics.date == start_date
    ).with_entities(
        func.sum(DailyCopyTraderStatistics.aum).label("aum"),
    ).first()
    aum_usd = daily_trader_st_summary.aum or zero

    trader_user_ids = {i.user_id for i in trader_rows}
    follower_user_ids = set()
    cur_follower_user_ids = set()
    trader_sub_trader_map = {}
    follow_sub_follower_map = {}
    for r in follow_his_rows:
        r: CopyFollowerHistory
        follower_user_ids.add(r.user_id)
        if r.status != CopyFollowerHistory.Status.FINISHED:
            cur_follower_user_ids.add(r.user_id)
        trader_sub_trader_map[r.copy_trader_sub_user_id] = r.copy_trader_user_id
        follow_sub_follower_map[r.sub_user_id] = r.user_id

    all_sub_ids = set(trader_sub_trader_map) | set(follow_sub_follower_map)
    sub_deal_count_map, sub_trade_amount_map = get_users_per_trade_map(
        all_sub_ids, date_to_datetime(start_date), end_time
    )
    deal_count = sum(sub_deal_count_map.values())
    deal_user_ids = set()
    cur_position_sub_ids = get_has_pending_position_user_ids(all_sub_ids, end_time)
    cur_position_main_ids = set()
    trader_trade_usd = follower_trade_usd = zero
    for sub_id, trader_id in trader_sub_trader_map.items():
        _sub_trade_amount = sub_trade_amount_map[sub_id]
        trader_trade_usd += _sub_trade_amount
        if _sub_trade_amount > zero:
            deal_user_ids.add(trader_id)
        if sub_id in cur_position_sub_ids:
            cur_position_main_ids.add(sub_id)
    for sub_id, follower_id in follow_sub_follower_map.items():
        _sub_trade_amount = sub_trade_amount_map[sub_id]
        follower_trade_usd += _sub_trade_amount
        if _sub_trade_amount > zero:
            deal_user_ids.add(follower_id)
        if sub_id in cur_position_sub_ids:
            cur_position_main_ids.add(sub_id)

    result = {
        "trader_user_ids": trader_user_ids,
        "follower_user_ids": follower_user_ids,
        "cur_follower_user_ids": cur_follower_user_ids,
        "cur_position_user_ids": cur_position_main_ids,
        "deal_user_ids": deal_user_ids,
        "deal_count": deal_count,
        "aum_usd": aum_usd,
        "trader_trade_usd": trader_trade_usd,
        "follower_trade_usd": follower_trade_usd,
        "profit_share_amount": profit_share_amount,
    }
    return result


def update_daily_copy_trading_report(start_date: date, end_date: date):
    data = build_daily_copy_trading_report_data(start_date, end_date)

    model = DailyCopyTradingSiteReport
    last_row: model = model.query.filter(
        model.report_date < start_date,
    ).order_by(model.report_date.desc()).first()
    if last_row:
        history_trader_ubm: BitMap = BitMap.deserialize(last_row.history_trader_user_bitmap)
        history_follower_ubm: BitMap = BitMap.deserialize(last_row.history_follower_user_bitmap)
    else:
        history_trader_ubm = BitMap()
        history_follower_ubm = BitMap()

    trader_user_ids = data["trader_user_ids"]
    cur_trader_ubm = BitMap()
    cur_trader_ubm.update(trader_user_ids)
    new_trader_ubm = cur_trader_ubm.difference(history_trader_ubm)
    history_trader_ubm.update(trader_user_ids)
    follower_user_ids = data["follower_user_ids"]
    cur_follower_ubm = BitMap()
    cur_follower_ubm.update(follower_user_ids)
    new_follower_ubm = cur_follower_ubm.difference(history_follower_ubm)
    history_follower_ubm.update(follower_user_ids)
    cur_follower_user_ids = data["cur_follower_user_ids"]
    cur_position_user_ids = data["cur_position_user_ids"]
    deal_user_ids = data["deal_user_ids"]

    row: model = model.get_or_create(report_date=start_date)
    row.trader_user_count = len(trader_user_ids)
    row.new_trader_user_count = len(new_trader_ubm)
    row.follower_user_count = len(follower_user_ids)
    row.new_follower_user_count = len(new_follower_ubm)
    row.cur_follower_user_count = len(cur_follower_user_ids)
    row.cur_position_user_count = len(cur_position_user_ids)
    row.deal_user_count = len(deal_user_ids)
    row.deal_count = data['deal_count']
    row.aum_usd = data['aum_usd']
    row.trader_trade_usd = data['trader_trade_usd']
    row.follower_trade_usd = data['follower_trade_usd']
    row.profit_share_amount = data['profit_share_amount']
    row.trader_user_bitmap = cur_trader_ubm.serialize()  # noqa
    row.history_trader_user_bitmap = history_trader_ubm.serialize()  # noqa
    row.follower_user_bitmap = cur_follower_ubm.serialize()  # noqa
    row.history_follower_user_bitmap = history_follower_ubm.serialize()  # noqa
    pos_ubm = BitMap()
    pos_ubm.update(cur_position_user_ids)
    row.position_user_bitmap = pos_ubm.serialize()  # noqa
    deal_ubm = BitMap()
    deal_ubm.update(deal_user_ids)
    row.deal_user_bitmap = deal_ubm.serialize()  # noqa
    db.session.add(row)
    db.session.commit()


@scheduled(crontab(minute="40,50", hour="0"))
@lock_call()
def update_daily_copy_trading_report_schedule():
    """ 跟单日报 """
    today_ = today()
    last_record = DailyCopyTradingSiteReport.query.order_by(DailyCopyTradingSiteReport.report_date.desc()).first()
    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        trader: CopyTraderUser = CopyTraderUser.query.order_by(CopyTraderUser.id.asc()).first()
        start_date = trader.created_at.date()

    while start_date < today_:
        end_date = start_date + timedelta(days=1)
        update_daily_copy_trading_report(start_date, end_date)
        start_date = end_date


def update_monthly_copy_trading_report(start_date: date, end_date: date):
    aum_usd = trader_trade_usd = follower_trade_usd = profit_share_amount = Decimal()
    deal_count = 0
    trader_user_ids = set()
    follower_user_ids = set()
    position_user_ids = set()
    deal_user_ids = set()
    daily_rows: List[DailyCopyTradingSiteReport] = DailyCopyTradingSiteReport.query.filter(
        DailyCopyTradingSiteReport.report_date >= start_date,
        DailyCopyTradingSiteReport.report_date < end_date,
    ).order_by(DailyCopyTradingSiteReport.report_date.asc()).all()
    for row in daily_rows:
        deal_count += row.deal_count
        aum_usd += row.aum_usd
        trader_trade_usd += row.trader_trade_usd
        follower_trade_usd += row.follower_trade_usd
        profit_share_amount += row.profit_share_amount

        trader_user_ids.update(list(BitMap.deserialize(row.trader_user_bitmap)))
        follower_user_ids.update(list(BitMap.deserialize(row.follower_user_bitmap)))
        position_user_ids.update(list(BitMap.deserialize(row.position_user_bitmap)))
        deal_user_ids.update(list(BitMap.deserialize(row.deal_user_bitmap)))

    model = MonthlyCopyTradingSiteReport
    last_row: model = model.query.filter(
        model.report_date < start_date,
    ).order_by(model.report_date.desc()).first()
    if last_row:
        his_trader_ubm: BitMap = BitMap.deserialize(last_row.history_trader_user_bitmap)
        new_trader_ids = trader_user_ids - set(his_trader_ubm)
        his_follower_ubm: BitMap = BitMap.deserialize(last_row.history_follower_user_bitmap)
        new_follower_ids = follower_user_ids - set(his_follower_ubm)
    else:
        his_trader_ubm = BitMap()
        new_trader_ids = trader_user_ids
        his_follower_ubm = BitMap()
        new_follower_ids = follower_user_ids

    his_trader_ubm.update(trader_user_ids)
    his_follower_ubm.update(follower_user_ids)

    row: model = model.get_or_create(report_date=start_date)
    row.trader_user_count = len(trader_user_ids)
    row.new_trader_user_count = len(new_trader_ids)
    row.follower_user_count = len(follower_user_ids)
    row.new_follower_user_count = len(new_follower_ids)
    row.cur_follower_user_count = daily_rows[-1].cur_follower_user_count if daily_rows else 0
    row.cur_position_user_count = len(position_user_ids)
    row.deal_user_count = len(deal_user_ids)

    row.deal_count = deal_count
    row.aum_usd = aum_usd
    row.trader_trade_usd = trader_trade_usd
    row.follower_trade_usd = follower_trade_usd
    row.profit_share_amount = profit_share_amount

    row.history_trader_user_bitmap = his_trader_ubm.serialize()  # noqa
    row.history_follower_user_bitmap = his_follower_ubm.serialize()  # noqa
    db.session.add(row)
    db.session.commit()


@scheduled(crontab(minute="45,55", hour="0"))
@lock_call()
def update_monthly_copy_trading_report_schedule():
    """ 跟单月报 """
    today_ = today()
    cur_month = date(today_.year, today_.month, 1)
    start_month = get_monthly_report_date(MonthlyCopyTradingSiteReport, DailyCopyTradingSiteReport)
    if not start_month:
        start_month = cur_month

    if start_month > cur_month:
        # 重复更新当月的数据
        start_month = cur_month

    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_copy_trading_report(start_month, end_month)
        start_month = end_month
