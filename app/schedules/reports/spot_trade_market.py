import json
import datetime
from collections import defaultdict
from decimal import Decimal


from app.business import TradeSummaryDB, route_module_to_celery_queue, \
    CeleryQueues, scheduled, crontab, lock_call
from app.business.market_maker import MarketMakerHelper
from app.caches.spot import SpotMarketUpDownCache, SpotBuySellDistributionCache
from app.models import db, DailySpotTradeMarketReport, AssetPrice, \
    MonthlySpotTradeMarketReport
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import next_month, current_timestamp

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def get_trade_summary_db():
    return TradeSummaryDB.db


def list_coin_trade_summary(date, trade_summary_db=None):
    sql = "SELECT market, stock_asset, money_asset, deal_amount, deal_user_list, deal_count, deal_volume, " \
          "taker_buy_amount, taker_sell_amount, taker_buy_count, taker_sell_count " \
          "FROM coin_trade_summary " \
          "WHERE trade_date = '{}' ".format(date)
    if not trade_summary_db:
        trade_summary_db = get_trade_summary_db()
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    return cursor.fetchall()


def list_user_trade_summary(date):
    date_str = date.strftime('%Y-%m-%d')
    month_str = date.strftime('%Y%m')
    sql = "SELECT user_id, market, deal_volume " \
          "FROM user_trade_summary_{} " \
          "WHERE trade_date = '{}' ".format(month_str, date_str)
    trade_summary_db = TradeSummaryDB.db
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    return cursor.fetchall()


def update_daily_spot_trade_market_report(report_date, force_update=False):
    if not TradeSummaryDB.is_data_completed(report_date):
        raise Exception('{} dump_history 数据还未同步完成'.format(report_date))
    coin_rate = AssetPrice.get_close_price_map(report_date)
    trade_summary_db = get_trade_summary_db()

    normal_user_deal_volumes = defaultdict(Decimal)
    user_trade_summary_list = list_user_trade_summary(report_date)
    maker_ids = MarketMakerHelper.list_all_maker_ids()
    for item in user_trade_summary_list:
        user_id, market, deal_volume = item
        if deal_volume <= 0:
            continue
        if user_id in maker_ids:
            continue
        normal_user_deal_volumes[market] += deal_volume
    user_fee_data = TradeSummaryDB.daily_trade_fee_list(report_date)
    market_fee_map = defaultdict(Decimal)
    normal_user_deal_fees = defaultdict(Decimal)
    for fee_ in user_fee_data:
        market_fee_map[fee_['market']] += fee_['fee'] * coin_rate.get(fee_['asset'], 0)
        if fee_['user_id'] not in maker_ids:
            normal_user_deal_fees[fee_['market']] += fee_['fee'] * coin_rate.get(fee_['asset'], 0)

    trade_summary_list = list_coin_trade_summary(report_date, trade_summary_db)
    for item in trade_summary_list:
        market, stock_asset, money_asset, deal_amount, deal_user_list, deal_count, deal_volume, \
            taker_buy_amount, taker_sell_amount, taker_buy_count, taker_sell_count = item
        record = DailySpotTradeMarketReport.get_or_create(report_date, market)
        if not record.trade_amount or force_update:
            normal_volume = normal_user_deal_volumes[market]
            normal_deal_rate = (normal_volume / (deal_volume * 2)) if deal_volume else Decimal()
            normal_fee_usd = normal_user_deal_fees[market]
            fee_usd = market_fee_map[market]
            normal_fee_usd_rate = (normal_fee_usd / fee_usd) if fee_usd else Decimal()
            record.stock_asset = stock_asset
            record.trade_amount = deal_amount
            record.trade_volume = deal_volume
            record.trade_usd = deal_volume * coin_rate.get(money_asset, 0)
            record.fee_usd = fee_usd
            user_list = json.loads(deal_user_list)
            record.deal_user_list = json.dumps(user_list)
            record.deal_user_count = len(user_list)
            record.deal_count = deal_count
            record.taker_buy_amount = taker_buy_amount
            record.taker_sell_amount = taker_sell_amount
            record.taker_buy_count = taker_buy_count
            record.taker_sell_count = taker_sell_count
            record.normal_deal_volume = normal_volume
            record.normal_deal_rate = normal_deal_rate
            record.normal_fee_usd = normal_fee_usd
            record.normal_fee_usd_rate = normal_fee_usd_rate
            db.session.add(record)
    db.session.commit()


def update_monthly_spot_trade_market_report(start_month, end_month, force_update=False):
    query = DailySpotTradeMarketReport.query.filter(
        DailySpotTradeMarketReport.report_date >= start_month,
        DailySpotTradeMarketReport.report_date < end_month
    )
    trade_amount_map, trade_volume_map, trade_usd_map, fee_usd_map, deal_user_map, deal_count_map = \
        defaultdict(Decimal), defaultdict(Decimal), defaultdict(Decimal), defaultdict(Decimal), \
        defaultdict(set), defaultdict(Decimal)
    taker_buy_amount_map, taker_buy_count_map, taker_sell_amount_map, taker_sell_count_map = \
        defaultdict(Decimal), defaultdict(Decimal), defaultdict(Decimal), defaultdict(Decimal)
    market_stock_asset_map = {}
    normal_fee_usd_map, normal_deal_volume = defaultdict(Decimal), defaultdict(Decimal)
    for item in query:
        market_stock_asset_map[item.market] = item.stock_asset
        trade_amount_map[item.market] += item.trade_amount
        trade_volume_map[item.market] += item.trade_volume
        trade_usd_map[item.market] += item.trade_usd
        fee_usd_map[item.market] += item.fee_usd
        deal_user_map[item.market] |= set(json.loads(item.deal_user_list))
        deal_count_map[item.market] += item.deal_count
        taker_buy_amount_map[item.market] += item.taker_buy_amount
        taker_buy_count_map[item.market] += item.taker_buy_count
        taker_sell_amount_map[item.market] += item.taker_sell_amount
        taker_sell_count_map[item.market] += item.taker_sell_count
        normal_deal_volume[item.market] += item.normal_deal_volume
        normal_fee_usd_map[item.market] += item.normal_fee_usd

    for market in trade_amount_map:
        record = MonthlySpotTradeMarketReport.get_or_create(start_month, market)
        if not record.trade_usd or force_update:
            normal_deal_rate = (normal_deal_volume[market] / (trade_volume_map[market] * 2)) if (
                trade_volume_map)[market] else Decimal()
            normal_fee_usd_rate = (normal_fee_usd_map[market] / fee_usd_map[market]) if (
                fee_usd_map)[market] else Decimal()
            record.stock_asset = market_stock_asset_map[market]
            record.trade_amount = trade_amount_map[market]
            record.trade_volume = trade_volume_map[market]
            record.trade_usd = trade_usd_map[market]
            record.fee_usd = fee_usd_map[market]
            record.deal_count = deal_count_map[market]
            record.taker_buy_amount = taker_buy_amount_map[market]
            record.taker_buy_count = taker_buy_count_map[market]
            record.taker_sell_amount = taker_sell_amount_map[market]
            record.taker_sell_count = taker_sell_count_map[market]
            record.deal_user_list = json.dumps(list(deal_user_map[market]))
            record.deal_user_count = len(deal_user_map[market])
            record.normal_deal_volume = normal_deal_volume[market]
            record.normal_deal_rate = normal_deal_rate
            record.normal_fee_usd = normal_fee_usd_map[market]
            record.normal_fee_usd_rate = normal_fee_usd_rate
            db.session.add(record)
    db.session.commit()


@scheduled(crontab(minute=15, hour='0-4'))
@lock_call()
def update_daily_spot_trade_market_schedule(force_update=False):
    today = datetime.datetime.utcnow().date()
    last_record = DailySpotTradeMarketReport.query.order_by(
        DailySpotTradeMarketReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today + datetime.timedelta(days=-90)
    while start_date < today:
        update_daily_spot_trade_market_report(start_date, force_update)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute=30, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_spot_trade_market_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(
        MonthlySpotTradeMarketReport, DailySpotTradeMarketReport)
    if not start_month:
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_spot_trade_market_report(start_month, end_month)
        start_month = end_month


@scheduled(crontab(minute='*/10'))
@lock_call()
def update_spot_market_up_down_cache():
    SpotMarketUpDownCache.reload()


@scheduled(crontab(minute='*/10'))
@lock_call()
def update_spot_buy_sell_distribution_cache():
    # 历史实时成交
    ts = current_timestamp(to_int=True)
    hour = 3600
    hour_ts = ts - ts % hour
    if TradeSummaryDB.is_hour_data_completed(hour_ts):
        SpotBuySellDistributionCache.reload(hour_ts)

