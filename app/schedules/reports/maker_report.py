import json
from collections import defaultdict
from datetime import date, datetime, timedelta
from decimal import Decimal

from flask import current_app
from sqlalchemy import func

from celery.schedules import crontab

from app.business.market_maker import MarketMakerHelper
from app.business.redshift import PerpetualRedShiftDB
from app.business.user import UserRepository
from app.caches import AmmMarketCache, PerpetualMarketCache
from app.common import CeleryQueues, PerpetualMarketType
from app.business import PerpetualSummaryDB, TradeSummaryDB, lock_call
from app.models import (DailyMakerTradeReport, MarketMaker,
                        UserTradeFeeSummary, User, UserTradeSummary,
                        db, MonthlyMakerTradeReport, SubAccount, AssetPrice,
                        DailyMakerMarketTradeReport,
                        MonthlyMakerMarketTradeReport,
                        DailyMakerTradeDetailReport,
                        MonthlyMakerTradeDetailReport, DailyMakerTradeMarketDetailReport,
                        MonthlyMakerTradeMarketDetailReport, DailyPerpetualMarketMakerReport, MakerCashBackHistory)
from app.schedules.reports.utils import get_monthly_report_date, \
    check_report_date_exists
from app.utils import route_module_to_celery_queue, scheduled, batch_iter, quantize_amount
from app.utils.date_ import next_month, date_to_datetime, datetime_to_time

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def get_maker_user_set():
    q = MarketMaker.query.filter(
        MarketMaker.status == MarketMaker.StatusType.PASS
    ).with_entities(MarketMaker.user_id).all()
    return {v.user_id for v in q}


def update_maker_report(start_date, end_date, system, report_model):
    _check_date = start_date  # 日报、月报：等于start_date
    if not UserTradeSummary.check_data_ready(_check_date):
        current_app.logger.warning("{} update_maker_report-UserTradeSummary 数据未就绪".format(_check_date))
        return
    if not UserTradeFeeSummary.check_data_ready(_check_date):
        current_app.logger.warning("{} update_maker_report-UserTradeFeeSummary 数据未就绪".format(_check_date))
        return

    outer_market_maker_ids = get_maker_user_set()
    inner_market_maker_ids = set(MarketMakerHelper.list_inner_maker_ids(include_sub_account=False))
    trade_summary = UserTradeSummary.query.filter(
        UserTradeSummary.report_date >= start_date,
        UserTradeSummary.report_date < end_date,
        UserTradeSummary.system == system,
    ).with_entities(
        UserTradeSummary.user_id,
        func.sum(UserTradeSummary.trade_amount).label('trade_amount')
    ).group_by(
        UserTradeSummary.user_id,
    ).all()
    fee_summary = UserTradeFeeSummary.query.filter(
        UserTradeFeeSummary.report_date >= start_date,
        UserTradeFeeSummary.report_date < end_date,
        UserTradeFeeSummary.system == system,
    ).with_entities(
        UserTradeFeeSummary.user_id,
        func.sum(UserTradeFeeSummary.trade_fee_amount).label('trade_fee_amount')
    ).group_by(
        UserTradeFeeSummary.user_id,
    ).all()

    total_trade_value = Decimal()
    outer_marker_trade_value = Decimal()
    inner_marker_trade_value = Decimal()
    total_fee_value = Decimal()
    outer_marker_fee_value = Decimal()
    inner_marker_fee_value = Decimal()
    outer_deal_count = int()
    inner_deal_count = int()

    for item in trade_summary:
        if item.user_id in outer_market_maker_ids:
            outer_deal_count += 1
            outer_marker_trade_value += item.trade_amount

        elif item.user_id in inner_market_maker_ids:
            inner_deal_count += 1
            inner_marker_trade_value += item.trade_amount
        total_trade_value += item.trade_amount

    for item in fee_summary:
        if item.user_id in outer_market_maker_ids:
            outer_marker_fee_value += item.trade_fee_amount

        elif item.user_id in inner_market_maker_ids:
            inner_marker_fee_value += item.trade_fee_amount
        total_fee_value += item.trade_fee_amount

    for maker_type in report_model.MakerType:
        if maker_type.name == 'OUTER':
            deal_count = outer_deal_count
            marker_trade_value = outer_marker_trade_value
            marker_fee_value = outer_marker_fee_value

        elif maker_type.name == 'INNER':
            deal_count = inner_deal_count
            marker_trade_value = inner_marker_trade_value
            marker_fee_value = inner_marker_fee_value

        else:
            deal_count = outer_deal_count + inner_deal_count
            marker_trade_value = outer_marker_trade_value + inner_marker_trade_value
            marker_fee_value = outer_marker_fee_value + inner_marker_fee_value

        record = report_model.get_or_create(report_date=start_date, system=system, maker_type=maker_type)
        record.deal_count = deal_count
        record.deal_usd = marker_trade_value
        record.fee_usd = marker_fee_value
        record.avg_fee_rate = marker_fee_value / marker_trade_value if marker_trade_value else 0
        record.deal_percent = marker_trade_value / total_trade_value if marker_trade_value else 0
        record.fee_percent = marker_fee_value / total_fee_value if total_fee_value else 0
        db.session.add(record)
    db.session.commit()


def update_daily_maker_market_report(start_date, end_date, system):
    market_maker_ids = get_maker_user_set()
    sub_map = {}
    for chunk_user_ids in batch_iter(market_maker_ids, 1000):
        chunk_sub_account = SubAccount.query.filter(
            SubAccount.main_user_id.in_(chunk_user_ids)).all()
        sub_map.update({i.user_id: i.main_user_id for i in chunk_sub_account})
    if system == 'SPOT':
        trade_summary_data = TradeSummaryDB.group_by_user_asset_by_date(start_date)
        fee_data = TradeSummaryDB.daily_trade_fee_list(start_date)
    else:
        trade_summary_data = PerpetualSummaryDB.group_by_user_asset_by_date(start_date)
        fee_data = PerpetualSummaryDB.daily_trade_fee_list(start_date)
    coin_rate = AssetPrice.get_close_price_map(start_date)
    market_trade_map = defaultdict(Decimal)
    total_market_trade_map = defaultdict(Decimal)
    market_trade_user_map = defaultdict(set)
    market_fee_map = defaultdict(Decimal)
    total_market_fee_map = defaultdict(Decimal)

    for item in trade_summary_data:
        main_user_id = sub_map.get(item['user_id'], item['user_id'])
        if main_user_id in market_maker_ids:
            if system == 'SPOT':
                market_trade_map[item['market']] += item['deal_volume'] * coin_rate.get(item['money_asset'], 0)
            else:
                market_trade_map[item['market']] += item['deal_amount']
            market_trade_user_map[item['market']].add(main_user_id)

        if system == 'SPOT':
            total_market_trade_map[item['market']] += item['deal_volume'] * coin_rate[item['money_asset']]
            total_market_trade_map[item['market']] += item['deal_volume'] * coin_rate.get(item['money_asset'], 0)
        else:
            total_market_trade_map[item['market']] += item['deal_amount']

    for item in fee_data:
        main_user_id = sub_map.get(item['user_id'], item['user_id'])
        if main_user_id in market_maker_ids:
            market_fee_map[item['market']] += coin_rate.get(item['asset'], 0) * item['fee']
        total_market_fee_map[item['market']] += coin_rate.get(item['asset'], 0) * item['fee']

    for market, trade_value in market_trade_map.items():
        fee_value = market_fee_map[market]
        total_deal_usd = total_market_trade_map[market]
        total_fee_usd = total_market_fee_map[market]
        deal_user_list = list(market_trade_user_map[market])

        record = DailyMakerMarketTradeReport.get_or_create(
            market=market, system=system, report_date=start_date)
        record.fee_usd = fee_value
        record.total_fee_usd = total_fee_usd
        record.fee_percent = fee_value / total_fee_usd if total_fee_usd else 0
        record.deal_usd = trade_value
        record.total_deal_usd = total_deal_usd
        record.deal_percent = trade_value / total_deal_usd if total_deal_usd else 0
        record.avg_fee_rate = fee_value / trade_value if trade_value else 0
        record.deal_count = len(deal_user_list)
        record.deal_user_list = json.dumps(deal_user_list)
        db.session.add(record)
    db.session.commit()


def update_monthly_maker_market_report(start_date, end_date, system):
    daily_maker_market_data = DailyMakerMarketTradeReport.query.filter(
        DailyMakerMarketTradeReport.report_date >= start_date,
        DailyMakerMarketTradeReport.report_date < end_date,
        DailyMakerMarketTradeReport.system == system,
    ).all()
    maker_market_map = defaultdict(lambda: {
        'deal_user_list': set(),
        'deal_usd': Decimal(),
        'total_deal_usd': Decimal(),
        'fee_usd': Decimal(),
        'total_fee_usd': Decimal(),
    })
    for item in daily_maker_market_data:
        market = item.market
        maker_market_map[market]['deal_usd'] += item.deal_usd
        maker_market_map[market]['total_deal_usd'] += item.total_deal_usd
        maker_market_map[market]['fee_usd'] += item.fee_usd
        maker_market_map[market]['total_fee_usd'] += item.total_fee_usd
        maker_market_map[market]['deal_user_list'].update(json.loads(item.deal_user_list))

    for market, market_data in maker_market_map.items():
        record = MonthlyMakerMarketTradeReport.get_or_create(
            report_date=start_date,
            market=market,
            system=system,
        )
        deal_usd = market_data['deal_usd']
        total_deal_usd = market_data['total_deal_usd']
        fee_usd = market_data['fee_usd']
        total_fee_usd = market_data['total_fee_usd']
        deal_user_list = list(market_data['deal_user_list'])
        record.deal_count = len(deal_user_list)
        record.deal_user_list = json.dumps(deal_user_list)
        record.deal_usd = deal_usd
        record.total_deal_usd = total_deal_usd
        record.fee_usd = fee_usd
        record.total_fee_usd = total_fee_usd
        record.deal_percent = deal_usd / total_deal_usd if total_deal_usd else 0
        record.fee_percent = fee_usd / total_fee_usd if total_fee_usd else 0
        record.avg_fee_rate = fee_usd / deal_usd if deal_usd else 0
        db.session.add(record)
    db.session.commit()


def update_daily_maker_trade_detail_report(start_date, system):
    if not UserTradeSummary.check_data_ready(start_date):
        current_app.logger.warning("{} update_daily_maker_trade_detail_report-UserTradeSummary 数据未就绪".format(start_date))
        return
    if not UserTradeFeeSummary.check_data_ready(start_date):
        current_app.logger.warning("{} update_daily_maker_trade_detail_report-UserTradeFeeSummary 数据未就绪".format(start_date))
        return

    if system == DailyMakerTradeDetailReport.System.SPOT.name:
        user_types = [
                User.UserType.INTERNAL_MAKER,
                User.UserType.EXTERNAL_MAKER,
                User.UserType.EXTERNAL_SPOT_MAKER,
            ]
        _db = TradeSummaryDB
    else:
        user_types = [
            User.UserType.INTERNAL_MAKER,
            User.UserType.EXTERNAL_MAKER,
            User.UserType.EXTERNAL_CONTRACT_MAKER,
        ]
        _db = PerpetualSummaryDB

    users = User.query.with_entities(User.id, User.user_type).filter(User.user_type.in_(user_types)).all()
    user_mapping = {user.id: user.user_type for user in users}
    user_ids = set(user_mapping.keys())

    merged_sub_user_dic = UserRepository.merge_sub_account_into_main_ids(user_ids)

    user_fee_query = []
    user_trade_query = []
    for chunk_user_ids in batch_iter(user_ids, 1000):
        chunk_user_fee_query = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.user_id.in_(chunk_user_ids),
            UserTradeFeeSummary.report_date == start_date,
            UserTradeFeeSummary.system == system,
        ).all()
        user_fee_query.extend(chunk_user_fee_query)

        chunk_user_trade_query = UserTradeSummary.query.filter(
            UserTradeSummary.user_id.in_(chunk_user_ids),
            UserTradeSummary.report_date == start_date,
            UserTradeSummary.system == system,
        ).all()
        user_trade_query.extend(chunk_user_trade_query)

    sum_fee_query = UserTradeFeeSummary.query.filter(
        UserTradeFeeSummary.report_date == start_date,
        UserTradeFeeSummary.system == system,
    ).with_entities(
        func.sum(UserTradeFeeSummary.trade_fee_amount).label('trade_fee_amount')
    ).first()

    sum_trade_query = UserTradeSummary.query.filter(
        UserTradeSummary.report_date == start_date,
        UserTradeSummary.system == system,
    ).with_entities(
        func.sum(UserTradeSummary.trade_amount).label('trade_amount'),
        func.sum(UserTradeSummary.taker_amount).label('taker_amount'),
        func.sum(UserTradeSummary.maker_amount).label('maker_amount'),
    ).first()
    user_trade_map = {i.user_id: i for i in user_trade_query}
    user_fee_amount_map = {i.user_id: i.trade_fee_amount for i in user_fee_query}
    total_fee_amount = sum_fee_query.trade_fee_amount
    total_trade_amount = sum_trade_query.trade_amount
    total_taker_amount = sum_trade_query.taker_amount
    total_maker_amount = sum_trade_query.maker_amount

    coin_rate = AssetPrice.get_close_price_map(start_date)
    fee_list = _db.daily_trade_fee_list(report_date=start_date)
    maker_fee_map = defaultdict(Decimal)
    taker_fee_map = defaultdict(Decimal)
    for fee_ in fee_list:
        user_id = fee_["user_id"]
        if main_id := merged_sub_user_dic.get(user_id):
            rate = coin_rate.get(fee_['asset'], 0)
            maker_fee_map[main_id] += fee_['maker_fee'] * rate
            taker_fee_map[main_id] += fee_['taker_fee'] * rate

    cashback_map = defaultdict(Decimal)
    if system == DailyMakerTradeDetailReport.System.SPOT.name:
        c_model = MakerCashBackHistory
        cashback_row = c_model.query.filter(
            c_model.report_date == start_date,
            c_model.user_id.in_(list(merged_sub_user_dic.keys())),
        ).with_entities(
            c_model.user_id,
            c_model.cashback_usd
        ).all()
        for row in cashback_row:
            if main_id := merged_sub_user_dic.get(row.user_id):
                cashback_map[main_id] += row.cashback_usd

    for user_id, item in user_trade_map.items():
        row = DailyMakerTradeDetailReport.get_or_create(
            user_id=user_id, system=system, report_date=start_date)

        user_type = user_mapping[user_id]
        maker_type = DailyMakerTradeDetailReport.MakerType.OUTER
        if user_type is User.UserType.INTERNAL_MAKER:
            maker_type = DailyMakerTradeDetailReport.MakerType.INNER
        row.maker_type = maker_type

        fee_amount = user_fee_amount_map.get(user_id, 0)
        row.trade_amount = item.trade_amount
        row.taker_amount = item.taker_amount
        row.maker_amount = item.maker_amount
        row.fee_amount = fee_amount
        row.trade_amount_percent = item.trade_amount / total_trade_amount if total_trade_amount else 0
        row.taker_amount_percent = item.taker_amount / total_taker_amount if total_taker_amount else 0
        row.maker_amount_percent = item.maker_amount / total_maker_amount if total_maker_amount else 0
        row.fee_amount_percent = fee_amount / total_fee_amount if total_fee_amount else 0
        row.maker_fee_amount = maker_fee_map.get(user_id, 0)
        row.taker_fee_amount = taker_fee_map.get(user_id, 0)
        row.cashback_usd = cashback_map.get(user_id, 0)
        db.session.add(row)
    db.session.commit()


def update_monthly_maker_trade_detail_report(start_date, system):

    end_date = next_month(start_date.year, start_date.month)
    daily_records = DailyMakerTradeDetailReport.query.filter(
        DailyMakerTradeDetailReport.report_date >= start_date,
        DailyMakerTradeDetailReport.report_date < end_date,
        DailyMakerTradeDetailReport.system == getattr(DailyMakerTradeDetailReport.System, system),
    ).all()

    daily_report_map = defaultdict(lambda: defaultdict(Decimal))
    latest_user_types = {}
    sorted_map = {}
    for record in daily_records:
        sorted_map.setdefault(record.user_id, []).append(record)
    for user_id, records in sorted_map.items():
        latest_user_types[user_id] = sorted(records, key=lambda obj: obj.report_date)[-1]
    for record in daily_records:
        daily_report_map[(record.user_id, record.system)]['trade_amount'] += record.trade_amount
        daily_report_map[(record.user_id, record.system)]['taker_amount'] += record.taker_amount
        daily_report_map[(record.user_id, record.system)]['maker_amount'] += record.maker_amount
        daily_report_map[(record.user_id, record.system)]['fee_amount'] += record.fee_amount
        daily_report_map[(record.user_id, record.system)]['maker_fee_amount'] += record.maker_fee_amount
        daily_report_map[(record.user_id, record.system)]['taker_fee_amount'] += record.taker_fee_amount
        daily_report_map[(record.user_id, record.system)]['cashback_usd'] += record.cashback_usd

    sum_trade_record = UserTradeSummary.query.filter(
        UserTradeSummary.report_date >= start_date,
        UserTradeSummary.report_date < end_date,
        UserTradeSummary.system == system,
    ).with_entities(
        func.sum(UserTradeSummary.trade_amount).label('trade_amount'),
        func.sum(UserTradeSummary.taker_amount).label('taker_amount'),
        func.sum(UserTradeSummary.maker_amount).label('maker_amount'),
    ).first()

    sum_fee_record = UserTradeFeeSummary.query.filter(
        UserTradeFeeSummary.report_date >= start_date,
        UserTradeFeeSummary.report_date < end_date,
        UserTradeFeeSummary.system == system,
    ).with_entities(
        func.sum(UserTradeFeeSummary.trade_fee_amount).label('trade_fee_amount')
    ).first()

    total_fee_amount = sum_fee_record.trade_fee_amount
    total_trade_amount = sum_trade_record.trade_amount
    total_taker_amount = sum_trade_record.taker_amount
    total_maker_amount = sum_trade_record.maker_amount

    commit_records = []
    for k, v in daily_report_map.items():
        user_id, system = k
        maker_type = latest_user_types[user_id].maker_type
        system = getattr(MonthlyMakerTradeDetailReport.System, system.name, None)
        maker_type = getattr(MonthlyMakerTradeDetailReport.MakerType, maker_type.name, None)
        if not system or not maker_type:
            continue
        record = MonthlyMakerTradeDetailReport.get_or_create(
            user_id=user_id, system=system, maker_type=maker_type, report_date=start_date
        )
        record.trade_amount = v['trade_amount']
        record.maker_amount = v['maker_amount']
        record.taker_amount = v['taker_amount']
        record.fee_amount = v['fee_amount']
        record.maker_fee_amount = v['maker_fee_amount']
        record.taker_fee_amount = v['taker_fee_amount']
        record.cashback_usd = v['cashback_usd']

        record.trade_amount_percent = quantize_amount(
            v['trade_amount'] / total_trade_amount if total_trade_amount else 0,
            8
        )
        record.maker_amount_percent = quantize_amount(
            v['maker_amount'] / total_maker_amount if total_maker_amount else 0,
            8
        )
        record.taker_amount_percent = quantize_amount(
            v['taker_amount'] / total_taker_amount if total_taker_amount else 0,
            8
        )
        record.fee_amount_percent = quantize_amount(
            v['fee_amount'] / total_fee_amount if total_fee_amount else 0,
            8
        )
        commit_records.append(record)
    if commit_records:
        db.session.bulk_save_objects(commit_records)
        db.session.commit()


@scheduled(crontab(minute=40, hour='2-4'))
@lock_call()
def update_daily_maker_report_schedule():
    today = datetime.utcnow().date()
    last_record = DailyMakerTradeReport.query.order_by(
        DailyMakerTradeReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = date(2021, 1, 1)
    while start_date < today:
        end_date = start_date + timedelta(days=1)
        update_maker_report(start_date, end_date,
                            DailyMakerTradeReport.System.SPOT.name,
                            DailyMakerTradeReport)
        update_maker_report(start_date, end_date,
                            DailyMakerTradeReport.System.PERPETUAL.name,
                            DailyMakerTradeReport)
        start_date = end_date


@scheduled(crontab(minute=30, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_maker_report_schedule():
    cur_year_num = date.today().year
    cur_month_num = date.today().month
    cur_month = date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(MonthlyMakerTradeReport,
                                          DailyMakerTradeReport)
    if not start_month:
        return
    yesterday = cur_month - timedelta(days=1)
    if not check_report_date_exists(DailyMakerTradeReport, yesterday):
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_maker_report(start_month, end_month,
                            MonthlyMakerTradeReport.System.SPOT.name,
                            MonthlyMakerTradeReport)
        update_maker_report(start_month, end_month,
                            MonthlyMakerTradeReport.System.PERPETUAL.name,
                            MonthlyMakerTradeReport)
        start_month = end_month


@scheduled(crontab(minute=40, hour='2-4'))
@lock_call()
def update_daily_maker_market_report_schedule():
    today = datetime.utcnow().date()
    last_record = DailyMakerMarketTradeReport.query.order_by(
        DailyMakerMarketTradeReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = date(2021, 1, 1)
    while start_date < today:
        end_date = start_date + timedelta(days=1)
        update_daily_maker_market_report(start_date, end_date,
                                         DailyMakerMarketTradeReport.System.SPOT.name)
        update_daily_maker_market_report(start_date, end_date,
                                         DailyMakerMarketTradeReport.System.PERPETUAL.name)
        start_date = end_date


@scheduled(crontab(minute=30, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_maker_market_report_schedule():
    cur_year_num = date.today().year
    cur_month_num = date.today().month
    cur_month = date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(MonthlyMakerMarketTradeReport,
                                          DailyMakerMarketTradeReport)
    if not start_month:
        return
    yesterday = cur_month - timedelta(days=1)
    if not check_report_date_exists(DailyMakerMarketTradeReport, yesterday):
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_maker_market_report(start_month, end_month,
                            MonthlyMakerMarketTradeReport.System.SPOT.name)
        update_monthly_maker_market_report(start_month, end_month,
                            MonthlyMakerMarketTradeReport.System.PERPETUAL.name)
        start_month = end_month


@scheduled(crontab(minute=40, hour='2-4'))
@lock_call()
def update_daily_maker_detail_report_schedule():
    today = datetime.utcnow().date()
    last_record = DailyMakerTradeDetailReport.query.order_by(
        DailyMakerTradeDetailReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = date(2021, 1, 1)
    while start_date < today:
        end_date = start_date + timedelta(days=1)
        update_daily_maker_trade_detail_report(start_date,
                                         DailyMakerTradeDetailReport.System.SPOT.name)
        update_daily_maker_trade_detail_report(start_date,
                                         DailyMakerTradeDetailReport.System.PERPETUAL.name)
        start_date = end_date


@scheduled(crontab(minute=20, hour='3-4', day_of_month=1))
@lock_call()
def update_monthly_maker_detail_report_schedule():
    cur_year_num = date.today().year
    cur_month_num = date.today().month
    cur_month = date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(MonthlyMakerTradeDetailReport,
                                          DailyMakerTradeDetailReport)
    if not start_month:
        return
    # yesterday = cur_month - timedelta(days=1)
    # if not check_report_date_exists(DailyMakerTradeDetailReport, yesterday):
    #     return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_maker_trade_detail_report(start_month,
                            MonthlyMakerTradeDetailReport.System.SPOT.name)
        update_monthly_maker_trade_detail_report(start_month,
                            MonthlyMakerTradeDetailReport.System.PERPETUAL.name)
        start_month = end_month


@scheduled(crontab(minute=28, hour='2-4'))
@lock_call()
def update_daily_maker_market_detail_report_schedule():
    """更新做市商在各个市场做市报表"""
    today = datetime.utcnow().date()
    last_record = DailyMakerTradeMarketDetailReport.query.order_by(
        DailyMakerTradeMarketDetailReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = date(2023, 1, 1)
    while start_date < today:
        end_date = start_date + timedelta(days=1)
        if not check_data_completed(start_date):
            return
        update_maker_market_trade_detail_report(
            start_date, end_date, DailyMakerTradeMarketDetailReport,
            DailyMakerTradeMarketDetailReport.System.SPOT)
        update_maker_market_trade_detail_report(
            start_date, end_date, DailyMakerTradeMarketDetailReport,
            DailyMakerTradeMarketDetailReport.System.PERPETUAL)
        start_date = end_date


def check_data_completed(report_date):
    if not TradeSummaryDB.is_data_completed(report_date):
        current_app.logger.warning(
            "{} update_daily_maker_market_trade_detail_report-TradeSummaryDB 数据未就绪".format(
                report_date))
        return False
    if not PerpetualSummaryDB.is_data_completed(report_date):
        current_app.logger.warning(
            "{} update_daily_maker_market_trade_detail_report-PerpetualTradeSummaryDB 数据未就绪".format(
                report_date))
        return False
    return True


@scheduled(crontab(minute=49, hour='5-7', day_of_month=1))
@lock_call()
def update_monthly_maker_market_detail_report_schedule():
    cur_year_num = date.today().year
    cur_month_num = date.today().month
    cur_month = date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(MonthlyMakerTradeMarketDetailReport,
                                          DailyMakerTradeMarketDetailReport)
    if not start_month:
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_maker_market_trade_detail_report(
            start_month, end_month, MonthlyMakerTradeMarketDetailReport,
            MonthlyMakerTradeMarketDetailReport.System.SPOT)
        update_maker_market_trade_detail_report(
            start_month, end_month, MonthlyMakerTradeMarketDetailReport,
            MonthlyMakerTradeMarketDetailReport.System.PERPETUAL)

        start_month = end_month


def update_maker_market_trade_detail_report(start_date, end_date, report_model, system):
    if system == report_model.System.SPOT:
        db_ = TradeSummaryDB
        users = MarketMaker.query.filter(
            MarketMaker.maker_type == MarketMaker.MakerType.SPOT,
            MarketMaker.status == MarketMaker.StatusType.PASS
        ).with_entities(MarketMaker.user_id).all()
    else:
        db_ = PerpetualSummaryDB
        users = MarketMaker.query.filter(
            MarketMaker.maker_type == MarketMaker.MakerType.PERPETUAL,
            MarketMaker.status == MarketMaker.StatusType.PASS
        ).with_entities(MarketMaker.user_id).all()

    user_ids = {i.user_id for i in users}
    merged_sub_user_dic = UserRepository.merge_sub_account_into_main_ids(user_ids)
    total_trade_dic = defaultdict(lambda: {
        'trade_amount': Decimal(),
        'taker_amount': Decimal(),
        'maker_amount': Decimal(),
        'fee_amount': Decimal(),
    })
    total_fee_dict = defaultdict(Decimal)
    market_user_fee_dict = defaultdict(lambda: defaultdict(Decimal))
    market_user_trade_dict = defaultdict(lambda: defaultdict(lambda: {
        'trade_amount': Decimal(),
        'taker_amount': Decimal(),
        'maker_amount': Decimal(),

    }))
    report_date = start_date
    while report_date < end_date:
        summary_trade_and_fee_data(db_, report_date, merged_sub_user_dic, total_trade_dic,
                            total_fee_dict, market_user_trade_dict, market_user_fee_dict)
        report_date += timedelta(days=1)

    is_amm = False
    amm_markets = []
    if system == report_model.System.SPOT:
        amm_markets = AmmMarketCache.list_amm_markets()
    for market, user_trade_dic in market_user_trade_dict.items():
        if system == report_model.System.SPOT:
            is_amm = market in amm_markets
        total_info = total_trade_dic[market]
        total_trade_amount = total_info['trade_amount']
        total_maker_amount = total_info['maker_amount']
        total_taker_amount = total_info['taker_amount']
        total_fee_amount = total_fee_dict.get(market, 0)
        for user_id, trade_dic in user_trade_dic.items():
            trade_amount = trade_dic['trade_amount']
            maker_amount = trade_dic['maker_amount']
            taker_amount = trade_dic['taker_amount']
            user_fee_dic = market_user_fee_dict[market]
            fee_amount = user_fee_dic.get(user_id, 0)
            row = report_model(
                report_date=start_date,
                system=system,
                user_id=user_id,
                market=market,
                trade_amount=trade_amount,
                total_trade_amount=total_trade_amount,
                taker_amount=taker_amount,
                total_taker_amount=total_taker_amount,
                maker_amount=maker_amount,
                total_maker_amount=total_maker_amount,
                fee_amount=fee_amount,
                total_fee_amount=total_fee_amount,
                is_amm=is_amm,
            )
            db.session.add(row)
        db.session.commit()


def summary_trade_and_fee_data(db_, report_date, merged_sub_user_dic, total_trade_dict,
                               total_fee_dict, market_user_trade_dict, market_user_fee_dict):
    asset_rates = AssetPrice.get_close_price_map(report_date)
    user_fee_list = db_.daily_trade_fee_list(report_date)
    user_trade_list = db_.group_by_user_asset_by_date(report_date)
    for fee_dic in user_fee_list:
        user_id = fee_dic['user_id']
        market = fee_dic['market']
        fee = fee_dic['fee']
        asset = fee_dic['asset']
        fee_usd = asset_rates.get(asset, 0) * fee
        main_id = merged_sub_user_dic.get(user_id)
        if main_id:
            market_user_fee_dict[market][main_id] += fee_usd
        total_fee_dict[market] += fee_usd
    for trade_dic in user_trade_list:
        user_id = trade_dic['user_id']
        market = trade_dic['market']
        money_asset = trade_dic['money_asset']
        if db_ == TradeSummaryDB:
            rate = asset_rates.get(money_asset, 0)
            trade_amount = trade_dic['deal_volume'] * rate
            maker_amount = trade_dic['maker_volume'] * rate
            taker_amount = trade_dic['taker_volume'] * rate
        else:
            trade_amount = trade_dic['deal_amount']
            maker_amount = trade_dic['maker_amount']
            taker_amount = trade_dic['taker_amount']
        main_id = merged_sub_user_dic.get(user_id)
        if main_id:
            info = market_user_trade_dict[market][main_id]
            info['trade_amount'] += trade_amount
            info['maker_amount'] += maker_amount
            info['taker_amount'] += taker_amount
        total_info = total_trade_dict[market]
        total_info['trade_amount'] += trade_amount
        total_info['maker_amount'] += maker_amount
        total_info['taker_amount'] += taker_amount


@scheduled(crontab(minute='40', hour='2-4'))
@lock_call()
def update_daily_market_maker_report_schedule():
    """交易市场维度做市商成交额日报"""
    today = datetime.utcnow().date()
    last_record = DailyPerpetualMarketMakerReport.query.order_by(
        DailyPerpetualMarketMakerReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = today - timedelta(days=90)
    while start_date < today:
        end_date = start_date + timedelta(days=1)
        _update_market_maker_report(start_date, end_date)
        start_date = end_date


def _update_market_maker_report(start_date, end_date):
    market_maker_ids = MarketMakerHelper.list_all_maker_ids()
    rs_db = PerpetualRedShiftDB
    rows = rs_db.get_trade_history_by_users(
        user_ids=market_maker_ids,
        start_time=datetime_to_time(date_to_datetime(start_date)),
        end_time=datetime_to_time(date_to_datetime(end_date)),
    )
    prices = AssetPrice.get_close_price_map(start_date)
    ret = defaultdict(Decimal)
    infos = PerpetualMarketCache().read_aside()
    for row in rows:
        market = row['market']
        v = infos.get(market)
        if not v:
            continue
        price = prices.get(v['money']) or Decimal()
        if v['type'] == PerpetualMarketType.INVERSE:
            deal = Decimal(row['amount'])
        else:
            deal = price * Decimal(row['amount']) * Decimal(row['price'])
        ret[market] += deal
    model = DailyPerpetualMarketMakerReport
    for market, deal in ret.items():
        record = model.get_or_create(report_date=start_date, market=market)
        record.deal_usd = deal
        db.session.add(record)
    db.session.commit()
