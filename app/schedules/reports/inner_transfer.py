#!/usr/bin/env python3
import datetime
from collections import defaultdict
from decimal import Decimal
from typing import Set

from celery.schedules import crontab
from sqlalchemy import and_

from app import config
from app.business import lock_call
from app.business.user import UserRepository
from app.business.utils import yield_query_records_by_time_range
from app.common import CeleryQueues, PrecisionEnum
from app.models import Deposit, RedPacket, RedPacketHistory, \
    DailyInnerTransferReport, db, MonthlyInnerTransferReport, AssetPrice, Withdrawal
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import route_module_to_celery_queue, scheduled, \
    next_month, quantize_amount, batch_iter
from app.utils.date_ import date_to_datetime

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def update_inner_transfer_report(start_time, end_time, report_model):
    # 内部转账 排除 ViaBTC内部充值
    pool_user_id = config["CLIENT_CONFIGS"]["viabtc_pool"]["user_id"]

    ignore_user_ids = config["DEPOSIT_WITHDRAWAL_REPORT_IGNORE_USERS"]
    inner_deposit_rows = []
    for row in yield_query_records_by_time_range(
            Deposit,
            date_to_datetime(start_time),
            date_to_datetime(end_time),
            [
                Deposit.asset,
                Deposit.amount,
                Deposit.user_id,
            ],
            and_(
                Deposit.type == Deposit.Type.LOCAL,
                Deposit.sender_user_id != pool_user_id
            ),
    ):
        if row.user_id in ignore_user_ids:
            continue
        inner_deposit_rows.append(row)

    inner_withdrawal_rows = []
    for row in yield_query_records_by_time_range(
            Withdrawal,
            date_to_datetime(start_time),
            date_to_datetime(end_time),
            [
                Withdrawal.asset,
                Withdrawal.amount,
                Withdrawal.user_id,
            ],
            and_(
                Withdrawal.type == Withdrawal.Type.LOCAL,
                Withdrawal.user_id != pool_user_id
            ),
    ):
        if row.user_id in ignore_user_ids:
            continue
        inner_withdrawal_rows.append(row)

    asset_inner_transfers_dict = defaultdict(list)
    for row in inner_deposit_rows + inner_withdrawal_rows:
        asset_inner_transfers_dict[row.asset].append(row)

    red_packet_rows = RedPacket.query.filter(
        RedPacket.effective_at >= start_time,
        RedPacket.effective_at < end_time,
    ).all()
    asset_red_packets_dict = defaultdict(list)
    for row in red_packet_rows:
        if row.user_id in ignore_user_ids:
            continue
        asset_red_packets_dict[row.asset].append(row)

    red_packet_history_rows = RedPacketHistory.query.filter(
        RedPacketHistory.grab_at >= start_time,
        RedPacketHistory.grab_at < end_time,
    ).all()
    asset_red_packet_histories_dict = defaultdict(list)
    email_min_grab_at_dict = {}
    for row in red_packet_history_rows:
        if row.user_id in ignore_user_ids:
            continue
        asset_red_packet_histories_dict[row.asset].append(row)
        if not email_min_grab_at_dict.get(row.email) or email_min_grab_at_dict[row.email] >= row.grab_at:
            email_min_grab_at_dict[row.email] = row.grab_at

    db.session.expunge_all()
    assets = set(asset_inner_transfers_dict) | set(asset_red_packets_dict) | set(asset_red_packet_histories_dict)
    assets_rate = AssetPrice.get_close_price_map(start_time)

    new_records = []
    zero = Decimal()
    all_site_statistics_data_dict = dict(
        inner_transfer_user_count=0,
        inner_transfer_count=0,
        inner_transfer_amount=zero,
        inner_transfer_usd=zero,
        give_red_packet_user_count=0,
        give_red_packet_count=0,
        give_red_packet_amount=zero,
        give_red_packet_usd=zero,
        receive_red_packet_user_count=0,
        receive_red_packet_count=0,
        receive_red_packet_amount=zero,
        receive_red_packet_usd=zero,
        give_red_packet_new_user_count=0,
        receive_red_packet_new_user_count=0,
        new_register_user_count=0,
    )
    all_inner_transfer_user_set, all_give_rp_user_set, all_receive_rp_user_set = set(), set(), set()
    all_send_cb_new_user_set, all_receive_cb_new_user_set, all_new_register_users = set(), set(), set()
    for asset in assets:
        asset_price = assets_rate.get(asset, Decimal())

        inner_transfers = asset_inner_transfers_dict[asset]
        inner_transfer_count = len(inner_transfers)
        inner_transfer_amount = sum(r.amount for r in inner_transfers)
        inner_transfer_usd = inner_transfer_amount * asset_price
        inner_transfer_user_set = {r.user_id for r in inner_transfers}
        inner_transfer_user_count = len(inner_transfer_user_set)

        red_packets = asset_red_packets_dict[asset]
        give_red_packet_count = len(red_packets)
        give_red_packet_amount = sum(r.total_amount for r in red_packets)
        give_red_packet_usd = give_red_packet_amount * asset_price
        give_red_packet_user_set = {r.user_id for r in red_packets}
        give_red_packet_user_count = len(give_red_packet_user_set)
        give_red_packet_new_user_set = _get_send_red_packet_new_users(start_time,
                                                            give_red_packet_user_set)
        give_red_packet_new_user_count = len(give_red_packet_new_user_set)

        red_packet_histories = asset_red_packet_histories_dict[asset]
        receive_red_packet_count = len(red_packet_histories)
        receive_red_packet_amount = sum(r.amount for r in red_packet_histories)
        receive_red_packet_usd = receive_red_packet_amount * asset_price
        receive_c_box_email_dic = {r.email: r.grab_at for r in red_packet_histories}
        receive_c_box_email_set = set(receive_c_box_email_dic.keys())
        receive_red_packet_user_count = len(receive_c_box_email_set)
        receive_c_box_new_user_set = _get_receive_red_packet_new_users(start_time,
                                                                  receive_c_box_email_set)
        receive_red_packet_new_user_count = len(receive_c_box_new_user_set)
        new_register_user_count = 0
        record = report_model.query.filter(
            report_model.report_date == start_time,
            report_model.asset == asset,
        ).first()
        if not record:
            record = report_model()
            record.report_date = start_time
            record.asset = asset
            new_records.append(record)
        record.inner_transfer_user_count = inner_transfer_user_count
        record.inner_transfer_count = inner_transfer_count
        record.inner_transfer_amount = quantize_amount(inner_transfer_amount, PrecisionEnum.COIN_PLACES)
        record.inner_transfer_usd = quantize_amount(inner_transfer_usd, PrecisionEnum.COIN_PLACES)
        record.give_red_packet_user_count = give_red_packet_user_count
        record.give_red_packet_count = give_red_packet_count
        record.give_red_packet_amount = quantize_amount(give_red_packet_amount, PrecisionEnum.COIN_PLACES)
        record.give_red_packet_usd = quantize_amount(give_red_packet_usd, PrecisionEnum.COIN_PLACES)
        record.receive_red_packet_user_count = receive_red_packet_user_count
        record.receive_red_packet_count = receive_red_packet_count
        record.receive_red_packet_amount = quantize_amount(receive_red_packet_amount, PrecisionEnum.COIN_PLACES)
        record.receive_red_packet_usd = quantize_amount(receive_red_packet_usd, PrecisionEnum.COIN_PLACES)

        record.give_red_packet_new_user_count = give_red_packet_new_user_count
        record.receive_red_packet_new_user_count = receive_red_packet_new_user_count
        record.new_register_user_count = new_register_user_count
        # 全站数据
        for key_ in all_site_statistics_data_dict:
            all_site_statistics_data_dict[key_] += getattr(record, key_)
        all_inner_transfer_user_set.update(inner_transfer_user_set)
        all_give_rp_user_set.update(give_red_packet_user_set)
        all_receive_rp_user_set.update(receive_c_box_email_set)
        all_send_cb_new_user_set.update(give_red_packet_new_user_set)
        all_receive_cb_new_user_set.update(receive_c_box_new_user_set)
    #
    if new_records:
        db.session.add_all(new_records)
    all_site_statistics_data_dict["inner_transfer_user_count"] = len(all_inner_transfer_user_set)
    all_site_statistics_data_dict["give_red_packet_user_count"] = len(all_give_rp_user_set)
    all_site_statistics_data_dict["receive_red_packet_user_count"] = len(all_receive_rp_user_set)
    all_site_statistics_data_dict["give_red_packet_new_user_count"] = len(
        all_send_cb_new_user_set)
    all_site_statistics_data_dict["receive_red_packet_new_user_count"] = len(
        all_receive_cb_new_user_set)
    all_new_register_users = _get_c_box_new_register_users(email_min_grab_at_dict, start_time, end_time)
    all_site_statistics_data_dict["new_register_user_count"] = len(all_new_register_users)
    decimal_keys = [
        "inner_transfer_amount",
        "inner_transfer_usd",
        "give_red_packet_amount",
        "give_red_packet_usd",
        "receive_red_packet_amount",
        "receive_red_packet_usd",
    ]
    for k in decimal_keys:
        all_site_statistics_data_dict[k] = quantize_amount(all_site_statistics_data_dict[k], PrecisionEnum.COIN_PLACES)
    all_site_record = report_model.query.filter(
        report_model.report_date == start_time,
        report_model.asset == "",
    ).first()
    if not all_site_record:
        db.session.add(report_model(report_date=start_time, asset="", **all_site_statistics_data_dict))
    else:
        for key_, val_ in all_site_statistics_data_dict.items():
            setattr(all_site_record, key_, val_)
    db.session.commit()


def _get_send_red_packet_new_users(end_time, send_red_packet_user_set) -> Set:
    records = RedPacket.query.filter(
        RedPacket.effective_at < end_time,
        RedPacket.user_id.in_(send_red_packet_user_set)
    ).with_entities(RedPacket.user_id).all()
    return send_red_packet_user_set - {i.user_id for i in records}


def _get_receive_red_packet_new_users(end_time, receive_red_packet_email_set) -> Set:
    ret = set()
    for emails in batch_iter(receive_red_packet_email_set, 1000):
        records = RedPacketHistory.query.filter(
            RedPacketHistory.grab_at < end_time,
            RedPacketHistory.email.in_(emails)
        ).with_entities(RedPacketHistory.email).all()
        ret.update({i.email for i in records})
    return receive_red_packet_email_set - ret


def _get_c_box_new_register_users(email_min_grab_at_dict, start_time, end_time) -> Set:
    res = set()
    users = UserRepository.get_register_users(start_time, end_time)
    new_user_dic = {i.email: i.created_at for i in users}
    for email, grab_at in email_min_grab_at_dict.items():
        if created := new_user_dic.get(email):
            if created >= grab_at:
                res.add(email)
    checking_users = set(new_user_dic.keys()) - set(email_min_grab_at_dict.keys())
    for emails in batch_iter(checking_users, 1000):
        c_box_hist_ret = RedPacketHistory.query.filter(
            RedPacketHistory.email.in_(emails),
            RedPacketHistory.status == RedPacketHistory.Status.FINISHED
        ).with_entities(RedPacketHistory.email).all()
        res.update({i.email for i in c_box_hist_ret})
    return res


@scheduled(crontab(minute=20, hour=0))
@lock_call()
def update_daily_inner_transfer_schedule():
    today = datetime.datetime.utcnow().date()
    last_record = DailyInnerTransferReport.query.order_by(
        DailyInnerTransferReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today + datetime.timedelta(days=-30)
    while start_date < today:
        end_date = start_date + datetime.timedelta(days=1)
        update_inner_transfer_report(start_date, end_date, DailyInnerTransferReport)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute=30, hour=0, day_of_month=1))
@lock_call()
def update_monthly_inner_transfer_schedule():

    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(
        MonthlyInnerTransferReport, DailyInnerTransferReport)

    if not start_month:
        return

    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_inner_transfer_report(start_month, end_month,
                                     MonthlyInnerTransferReport)
        start_month = end_month
