import json
import datetime
from collections import defaultdict
from decimal import Decimal
from itertools import chain
from typing import Dict, List, Union
from sqlalchemy import func

from app import Language
from app.business import route_module_to_celery_queue, CeleryQueues, lock_call, ExchangeLogDB
from app.business import TradeHistoryDB, PerpetualHistoryDB, ServerClient, ALL_RECORD_ACCOUNT_ID
from app.common import PrecisionEnum, language_cn_names
from app.common.countries import get_country
from app.models import DailySpotTradeCoinReport, MonthlySpotTradeCoinReport, \
    DailyDepositWithdrawalReport, MonthlyDepositWithdrawalReport, DailyInnerTransferReport, \
    MonthlyInnerTransferReport, DailyIncomeReport, MonthlyIncomeReport, DailyLiquidityReport, MonthlyLiquidityReport, \
    LiquiditySlice, DailyAmmMarketReport, DailyChannelIncreaseUserReport, DailyMarginAssetReport, \
    MonthlyMarginAssetReport, DailyUserActiveRetainedReport, MonthlyUserActiveRetainedReport, \
    MonthlyUserActiveRetainedDetail, DailyUserHierarchicalReport, DailyBalanceReport, DailyAssetUserVisitReport, \
    DailyAssetExposureReport, KolReferralExportReport, \
    LawEnforcementExport, LawEnforcementExportReport, \
    LoginHistory, Deposit, Withdrawal
from app.models.mongo.sms import MobileMessageRecordMySQL
from app.models.user import User
from app.models.exchange import AssetExchangeOrder
from app.utils import celery_task, batch_iter, amount_to_str, format_percent, quantize_amount, today_timestamp_utc
from app.utils.date_ import (
    timestamp_to_date, last_month, next_month, timestamp_to_datetime, current_timestamp,
    datetime_to_time, datetime_to_str,
)
from app.utils.export import export_xlsx_with_sheet
from app.utils.ip import GeoIP

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


@celery_task
@lock_call(with_args=['email', 'is_daily'])
def async_download_spot_coin_report(
        email: str,
        is_daily: bool,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    if is_daily:
        model = DailySpotTradeCoinReport
    else:
        model = MonthlySpotTradeCoinReport
    rows = _get_rows(model, start_ts, end_ts)
    ret = []
    for row in rows:
        report_date = row.report_date.strftime('%Y-%m-%d') if is_daily else row.report_date.strftime('%Y-%m')
        record = {
            'report_date': report_date,
            'coin': row.coin,
            'trade_usd': amount_to_str(row.trade_usd, 2),
            'fee_usd': amount_to_str(row.fee_usd, 2),
            'trade_amount': '{} {}'.format(
                amount_to_str(row.trade_amount, 2), row.coin),
            'deal_user_count': row.deal_user_count,
            'deal_count': row.deal_count,
            'taker_buy_amount': '{} {}'.format(
                amount_to_str(row.taker_buy_amount, 2), row.coin),
            'taker_buy_count': row.taker_buy_count,
            'taker_sell_amount': '{} {}'.format(
                amount_to_str(row.taker_sell_amount, 2), row.coin),
            'taker_sell_count': row.taker_sell_count,
            'normal_deal_rate': format_percent(row.normal_deal_rate, 2),
            'normal_fee_usd_rate': format_percent(row.normal_fee_usd_rate, 2),
        }
        ret.append(record)

    header_mapping = {
        "report_date": "日期",
        "coin": "币种",
        "trade_amount": "成交量",
        "trade_usd": "成交市值(USD)",
        "normal_deal_rate": "普通成交比例",
        "fee_usd": "手续费市值(USD)",
        "normal_fee_usd_rate": "普通手续费比例",
        "deal_user_count": "成交人数",
        "deal_count": "成交笔数",
        "taker_buy_amount": "主动买入量",
        "taker_buy_count": "主动买入笔数",
        "taker_sell_amount": "主动卖出量",
        "taker_sell_count": "主动卖出笔数",
    }
    desc = '报表-现货交易-交易币种-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', 'is_daily'])
def async_download_balance_visit_report(
        email: str,
        is_daily: bool,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    model = DailyAssetUserVisitReport
    rows = _get_rows(model, start_ts, end_ts)
    start_date = timestamp_to_date(start_ts)

    def _get_prev_date(_date):
        if is_daily:
            prev_date_ = _date - datetime.timedelta(days=1)
        else:
            prev_date_ = last_month(_date.year, _date.month)
        return prev_date_

    def _get_ratio(curr, total):
        """占比"""
        if total == 0:
            return 0
        return format_percent(curr / total)

    def _format_column(field, prev_val):
        delta = item[field] - prev_val
        prefix = ''
        if delta >= 0:
            prefix = '+'
        return f'{item[field]}（{prefix}{delta}）'

    prev_date = _get_prev_date(start_date)
    prev_records = model.query.filter(
        model.report_date == prev_date,
    ).all()
    record_map = defaultdict(lambda: defaultdict(dict))
    for record in chain(prev_records, rows):
        record_map[record.report_date][record.asset] = record
    ret = []
    langs = {lang.name: lang_value for lang, lang_value in language_cn_names().items()}
    user_types = {e.name: e.value for e in model.UserType}
    for row in rows:
        item = row.to_dict()
        date_ = item['report_date']
        report_date_str = date_.strftime('%Y-%m-%d') if is_daily else date_.strftime('%Y-%m')
        record = {
            'report_date': report_date_str,
            'asset': item['asset'],
            'lang': langs[item['lang']] if item['lang'] else '',
            'user_type': user_types[item['user_type']] if item['user_type'] else '',
            'duration': item['duration'],
            'favorite_count': item['favorite_count'],
            'spot_follow_count': item['spot_follow_count'],
            'perpetual_follow_count': item['perpetual_follow_count'],
            'asset_click_count': item['asset_click_count'],
            'spot_click_count': item['spot_click_count'],
            'perpetual_click_count': item['perpetual_click_count'],
            'asset_search_count': item['asset_search_count'],
            'spot_search_count': item['spot_search_count'],
            'perpetual_search_count': item['perpetual_search_count'],
        }

        ret.append(record)

    header_mapping = {
        "report_date": "日期",
        "asset": "币种",
        "lang": "语区",
        "user_type": "用户类型",
        "duration": "币种上线时长",
        "favorite_count": "行情自选人数",
        "spot_follow_count": "币币自选人数",
        "perpetual_follow_count": "合约自选人数",
        "asset_click_count": "币种搜索点击人数",
        "spot_click_count": "币币搜索点击人数",
        "perpetual_click_count": "合约搜索点击人数",
        "asset_search_count": "app币种搜索点击人数",
        "spot_search_count": "app币币搜索点击人数",
        "perpetual_search_count": "app合约搜索点击人数",
    }

    desc = '报表-资产报表-用户关注报表-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email'])
def async_download_balance_exposure_report(
        email: str,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    model = DailyAssetExposureReport
    rows = _get_rows(model, start_ts, end_ts)
    ret = []
    langs = {lang.name: lang_value for lang, lang_value in language_cn_names().items()}
    user_types = {e.name: e.value for e in model.UserType}
    for row in rows:
        item = row.to_dict()
        date_ = item['report_date']
        report_date_str = date_.strftime('%Y-%m-%d')
        record = {
            'report_date': report_date_str,
            'asset': item['asset'] or 'ALL',
            'lang': langs[item['lang']] if item['lang'] else 'ALL',
            'user_type': user_types[item['user_type']] if item['user_type'] else 'ALL',
            'duration': item['duration'],
            'view_count': item['view_count'],
            'detail_view_count': item['detail_view_count'],
            'spot_view_count': item['spot_view_count'],
            'perpetual_view_count': item['perpetual_view_count'],
            'normal_trade_count': item['normal_trade_count'],
            'normal_spot_trade_count': item['normal_spot_trade_count'],
            'normal_spot_trade_usd': amount_to_str(item['normal_spot_trade_usd'], 2),
            'normal_perpetual_trade_count': item['normal_perpetual_trade_count'],
            'normal_perpetual_trade_usd': amount_to_str(item['normal_perpetual_trade_usd'], 2),
        }

        ret.append(record)

    header_mapping = {
        "report_date": "日期",
        "asset": "币种",
        "lang": "语区",
        "user_type": "用户类型",
        "duration": "币种上线时长",
        "view_count": "币种浏览用户数",
        "detail_view_count": "币种详情页浏览用户数",
        "spot_view_count": "币币交易页浏览用户数",
        "perpetual_view_count": "合约交易页浏览用户数",
        "normal_trade_count": "交易普通用户数",
        "normal_spot_trade_count": "币币普通交易用户数",
        "normal_spot_trade_usd": "币币普通用户交易额（USD）",
        "normal_perpetual_trade_count": "合约普通交易用户数",
        "normal_perpetual_trade_usd": "合约普通用户交易额（USD）",
    }

    desc = '报表-资产报表-币种曝光报表-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', 'is_daily', 'is_viabtc'])
def async_download_deposit_withdrawal_report(
        email: str,
        is_daily: bool,
        is_viabtc: bool,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    if is_daily:
        model = DailyDepositWithdrawalReport
    else:
        model = MonthlyDepositWithdrawalReport
    rows = _get_rows(model, start_ts, end_ts)
    ret = []
    for item in rows:
        report_date = item.report_date.strftime('%Y-%m-%d') if is_daily else item.report_date.strftime('%Y-%m')
        if is_viabtc is True:
            record = {
                'report_date': report_date,
                'asset': item.asset,
                'local_deposit_user_count': item.local_deposit_user_count,
                'local_deposit_count': item.local_deposit_count,
                'local_deposit_amount': amount_to_str(item.local_deposit_amount, 8),
                'local_deposit_usd': amount_to_str(item.local_deposit_usd, 2),
            }
        else:
            record = {
                'report_date': report_date,
                'asset': item.asset,
                'deposit_user_count': item.deposit_user_count,
                'deposit_count': item.deposit_count,
                'deposit_amount': item.deposit_amount,
                'deposit_usd': amount_to_str(item.deposit_usd, 2),
                'withdrawal_user_count': item.withdrawal_user_count,
                'withdrawal_count': item.withdrawal_count,
                'withdrawal_amount': item.withdrawal_amount,
                'withdrawal_usd': amount_to_str(item.withdrawal_usd, 2),
                'net_amount': amount_to_str(item.deposit_amount - item.withdrawal_amount, 8),
                'net_usd': amount_to_str(item.deposit_usd - item.withdrawal_usd, 2),
            }
        ret.append(record)

    if is_viabtc is True:
        header_mapping = {
            "report_date": "日期",
            "asset": "币种",
            "local_deposit_user_count": "充值人数",
            "local_deposit_count": "充值笔数",
            "local_deposit_amount": "充值数量",
            "local_deposit_usd": "充值市值",
        }
        desc = '报表-现货交易-Viabtc充值报表-币种-异步下载'
    else:
        header_mapping = {
            "report_date": "日期",
            "asset": "币种",
            "deposit_user_count": "充值人数",
            "deposit_count": "充值笔数",
            "deposit_amount": "充值数量",
            "deposit_usd": "充值市值",
            "withdrawal_user_count": "提现人数",
            "withdrawal_count": "提现笔数",
            "withdrawal_amount": "提现数量",
            "withdrawal_usd": "提现市值",
            "net_amount": "净留存数量",
            "net_usd": "净留存市值",
        }
        desc = '报表-现货交易-充提报表-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', 'is_daily'])
def async_download_inner_transfer_report(
        email: str,
        is_daily: bool,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    if is_daily:
        model = DailyInnerTransferReport
    else:
        model = MonthlyInnerTransferReport
    rows = _get_rows(model, start_ts, end_ts)
    ret = []
    for row in rows:
        report_date = row.report_date.strftime('%Y-%m-%d') if is_daily else row.report_date.strftime('%Y-%m')
        record = {
            'report_date': report_date,
            'asset': row.asset,
            'inner_transfer_user_count': row.inner_transfer_user_count,
            'inner_transfer_count': row.inner_transfer_count,
            'inner_transfer_amount': row.inner_transfer_amount,
            'give_red_packet_user_count': row.give_red_packet_user_count,
            'give_red_packet_new_user_count': row.give_red_packet_new_user_count,
            'give_red_packet_count': row.give_red_packet_count,
            'give_red_packet_amount': row.give_red_packet_amount,
            'receive_red_packet_user_count': row.receive_red_packet_user_count,
            'receive_red_packet_new_user_count': row.receive_red_packet_new_user_count,
            'receive_red_packet_count': row.receive_red_packet_count,
            'receive_red_packet_amount': row.receive_red_packet_amount,
        }
        ret.append(record)

    header_mapping = {
        "report_date": "日期",
        "asset": "币种",
        "inner_transfer_user_count": "内部转账人数",
        "inner_transfer_count": "内部转账笔数",
        "inner_transfer_amount": "内部转账数量",
        "give_red_packet_user_count": "发C-Box人数",
        "give_red_packet_new_user_count": "新增发C-Box人数",
        "give_red_packet_count": "发C-Box次数",
        "give_red_packet_amount": "发C-Box金额",
        "receive_red_packet_user_count": "收C-Box人数",
        "receive_red_packet_new_user_count": "新增收C-Box人数",
        "receive_red_packet_count": "收C-Box次数",
        "receive_red_packet_amount": "收C-Box金额",
    }
    desc = '报表-内部转账-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', 'is_daily'])
def async_download_asset_income_report(
        email: str,
        is_daily: bool,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    if is_daily:
        model = DailyIncomeReport
    else:
        model = MonthlyIncomeReport
    rows = _get_rows(model, start_ts, end_ts)
    ret = []
    total_usd = Decimal()
    for row in rows:
        report_date = row.report_date.strftime('%Y-%m-%d') if is_daily else row.report_date.strftime('%Y-%m')
        total_amount = amount_to_str(row.total_amount, PrecisionEnum.COIN_PLACES)
        total_amount_str = f'{total_amount} {row.asset}'
        pay_amount = amount_to_str(abs(row.pay_amount), PrecisionEnum.COIN_PLACES)
        pay_amount_str = f'{pay_amount} {row.asset}'
        net_amount = amount_to_str(abs(row.net_amount), PrecisionEnum.COIN_PLACES)
        net_amount_str = f'{net_amount} {row.asset}'
        net_usd = amount_to_str(abs(row.net_usd), PrecisionEnum.CASH_PLACES)
        net_usd_str = f'{net_usd} USD'
        ret.append(dict(
            report_date=report_date,
            asset=row.asset,
            total_amount=total_amount_str,
            pay_amount=pay_amount_str,
            net_amount=net_amount_str,
            net_usd=net_usd_str
        ))
        total_usd += row.net_usd
    # ret.sort(key=lambda x: x['date'], reverse=True)
    # total_usd = amount_to_str(total_usd, PrecisionEnum.CASH_PLACES)
    # ret = [dict(asset='ALL', date='ALL', total_amount='-',
    #             pay_amount='-', net_amount='-', net_usd=f'{total_usd} USD'), ] + ret

    header_mapping = {
        "report_date": "日期",
        "asset": "币种",
        "total_amount": "总收入",
        "pay_amount": "总支出",
        "net_amount": "实收",
        "net_usd": "实收市值",
    }
    desc = '报表-收入报表-币种收入报表-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', 'is_daily'])
def async_download_amm_market_report(
        email: str,
        is_daily: bool,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    if is_daily:
        model = DailyLiquidityReport
    else:
        model = MonthlyLiquidityReport

    def _get_market_map():
        _market_map = defaultdict(lambda: {
            'liquidity_usd': Decimal(),
            'refund_fee_usd': Decimal(),
            'fee_usd': Decimal(),
            'deal_usd': Decimal(),
            'market_count': int()
        })
        liquidity_slice_query = LiquiditySlice.query.filter(
            LiquiditySlice.date >= start_date,
            LiquiditySlice.date <= end_date
        ).group_by(
            LiquiditySlice.date,
            LiquiditySlice.market,
        ).with_entities(
            LiquiditySlice.date,
            func.sum(LiquiditySlice.liquidity_usd).label('liquidity_usd'),
            func.sum(LiquiditySlice.fee_usd).label('fee_usd')
        )
        liquidity_slices = liquidity_slice_query.all()
        counts = model.query.filter(
            model.market != '',
            model.report_date >= start_date,
            model.report_date <= end_date
        ).group_by(model.report_date).with_entities(
            model.report_date,
            func.count(model.market),
        ).all()
        market_count_map = dict(counts)
        for slice in liquidity_slices:
            if is_daily:
                d_ = slice.date
            else:
                d_ = datetime.date(slice.date.year, slice.date.month, 1)
            _market_map[d_]['liquidity_usd'] += slice.liquidity_usd
            _market_map[d_]['refund_fee_usd'] += slice.fee_usd
            _market_map[d_]['market_count'] = market_count_map.get(d_, 0)
        amm_reports = DailyAmmMarketReport.query.filter(
            DailyAmmMarketReport.report_date >= start_date,
            DailyAmmMarketReport.report_date <= end_date
        ).group_by(
            DailyAmmMarketReport.report_date,
            DailyAmmMarketReport.market,
        ).with_entities(
            DailyAmmMarketReport.report_date,
            func.sum(DailyAmmMarketReport.fee_usd).label('fee_usd'),
            func.sum(DailyAmmMarketReport.deal_usd).label('deal_usd')
        ).all()
        for item in amm_reports:
            item: DailyAmmMarketReport
            if is_daily:
                d_ = item.report_date
            else:
                d_ = datetime.date(item.report_date.year, item.report_date.month, 1)
            _market_map[d_]['fee_usd'] += item.fee_usd
            _market_map[d_]['deal_usd'] += item.deal_usd
        return _market_map

    start_date = timestamp_to_date(start_ts)
    end_date = timestamp_to_date(end_ts)
    rows = _get_rows(model, start_ts, end_ts)
    market_map = _get_market_map()
    ret = []
    for row in rows:
        if not row.market:
            continue
        date_amm_map = market_map[row.report_date]
        report_date = row.report_date.strftime('%Y-%m-%d') if is_daily else row.report_date.strftime('%Y-%m')
        liquidity_usd = date_amm_map['liquidity_usd']
        if liquidity_usd > 0:
            interest_rate = date_amm_map['refund_fee_usd'] / liquidity_usd * 365
        else:
            interest_rate = 0
        if not is_daily:
            next_month_ = next_month(row.report_date.year, row.report_date.month)
            days_in_month = (next_month_ - row.report_date).days
            liquidity_usd /= days_in_month
        deal_usd = date_amm_map['deal_usd']
        if deal_usd > 0:
            deal_rate = row.amm_user_deal_usd / deal_usd
        else:
            deal_rate = 0
        added_liquidity_real_usd = row.added_liquidity_usd - row.removed_liquidity_usd
        if added_liquidity_real_usd > 0:
            added_liquidity_real_usd = f"+{amount_to_str(added_liquidity_real_usd, 2)}"
        else:
            added_liquidity_real_usd = f"{amount_to_str(added_liquidity_real_usd, 2)}"
        added_user_count_and_added_count = f'{row.added_user_count}/{row.added_count}'
        removed_user_count_and_removed_count = f'{row.removed_user_count}/{row.removed_count}'
        ret.append(dict(
            report_date=report_date,
            market=row.market,
            liquidity_user_count=row.liquidity_user_count,
            trade_user_count=row.trade_user_count,
            liquidity_usd=amount_to_str(liquidity_usd, 2),
            deal_usd=amount_to_str(deal_usd, 2),
            deal_rate=format_percent(deal_rate, 4),
            interest_rate=format_percent(interest_rate, 4),
            refund_fee_usd=amount_to_str(date_amm_map['refund_fee_usd'], 2),
            added_liquidity_real_usd=added_liquidity_real_usd,
            added_user_count_and_added_count=added_user_count_and_added_count,
            removed_user_count_and_removed_count=removed_user_count_and_removed_count,
        ))
    header_mapping = {
        "report_date": "日期",
        "market": "市场名称",
        "liquidity_user_count": "做市用户数",
        "trade_user_count": "交易用户数",
        "liquidity_usd": "总流动性市值(USD)",
        "deal_usd": "总交易额(USD)",
        "deal_rate": "成交比例",
        "refund_fee_usd": "手续费分红(USD)",
        "interest_rate": "当日年化收益率",
        "added_liquidity_real_usd": "流动性净增加市值",
        "added_user_count_and_added_count": "增加人数/次数",
        "removed_user_count_and_removed_count": "提取人数/次数",
    }
    desc = '报表-AMM市场报表-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', ])
def async_download_increase_channel_report(
        email: str,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    model = DailyChannelIncreaseUserReport
    rows = _get_rows(model, start_ts, end_ts)

    def _add_channel_percent_field(record_data):
        """为各种漏斗报表添加百分比"""
        percent_fields = [
            "deposit_on_chain_count_7_days",
            "deposit_on_chain_trade_count_7_days",
            "deposit_local_count_7_days",
            "deposit_local_trade_count_7_days",
            "deposit_count_7_days",
            "trade_count_7_days",
            "spot_trade_count_30_days",
            "perpetual_trade_count_30_days",
            "trade_users_30_days",
            "exchange_count_30_days",
            "margin_count_30_days",
            "investment_count_30_days",
            "amm_count_30_days",
        ]
        for record in record_data:
            for field in percent_fields:
                p_field = f"{field}_percent"
                base = record['register_count']
                if field == 'deposit_on_chain_trade_count_7_days':
                    base = record['deposit_on_chain_count_7_days']
                if field == 'deposit_local_trade_count_7_days':
                    base = record['deposit_local_count_7_days']

                if base == 0:
                    record[p_field] = 0
                else:
                    record[p_field] = quantize_amount(
                        Decimal(record[field]) / Decimal(base), 4)
        return record_data

    def _fmt_channel_export_data(_records):
        """格式化各种漏斗报表导出格式"""
        for record in _records:
            for k, v in record.items():
                if '_percent' in k:
                    continue
                percent_k = f'{k}_percent'
                if percent_k not in record:
                    continue
                fmt_percent_v = format_percent(record[percent_k], 2)
                fmt_data = f'{v} ({fmt_percent_v})'
                record[k] = fmt_data

        return _records

    records = _add_channel_percent_field([row.to_dict() for row in rows])
    records = _fmt_channel_export_data(records)
    ret = []
    channel_type_option = {
        DailyChannelIncreaseUserReport.ChannelType.ALL.name: '全部',
        DailyChannelIncreaseUserReport.ChannelType.NONE.name: '自然注册',
        DailyChannelIncreaseUserReport.ChannelType.NORMAL.name: '普通推荐',
        DailyChannelIncreaseUserReport.ChannelType.AMBASSADOR.name: '大使推荐',
    }
    for record in records:
        report_date = record['report_date'].strftime('%Y-%m-%d')
        ret.append(dict(
            report_date=report_date,
            channel_type=channel_type_option[record['channel_type'].name],
            register_count=record['register_count'],
            deposit_on_chain_count_7_days=record['deposit_on_chain_count_7_days'],
            deposit_on_chain_trade_count_7_days=record['deposit_on_chain_trade_count_7_days'],
            deposit_local_count_7_days=record['deposit_local_count_7_days'],
            deposit_local_trade_count_7_days=record['deposit_local_trade_count_7_days'],
            deposit_count_7_days=record['deposit_count_7_days'],
            trade_count_7_days=record['trade_count_7_days'],
            spot_trade_count_30_days=record['spot_trade_count_30_days'],
            trade_users_30_days=record['trade_users_30_days'],
            perpetual_trade_count_30_days=record['perpetual_trade_count_30_days'],
            exchange_count_30_days=record['exchange_count_30_days'],
            margin_count_30_days=record['margin_count_30_days'],
            investment_count_30_days=record['investment_count_30_days'],
            amm_count_30_days=record['amm_count_30_days'],
        ))

    header_mapping = {
        "report_date": "日期",
        "channel_type": "渠道",
        "register_count": "注册人数",
        "deposit_on_chain_count_7_days": "7天内链上充值人数",
        "deposit_on_chain_trade_count_7_days": "7天内链上充值交易人数",
        "deposit_local_count_7_days": "7天内非链上充值人数",
        "deposit_local_trade_count_7_days": "7天内非链上充值交易人数",
        "deposit_count_7_days": "7天内总充值人数",
        "trade_count_7_days": "7天内总交易人数",
        "spot_trade_count_30_days": "30天内币币交易人数",
        "trade_users_30_days": "30天内总交易人数",
        "perpetual_trade_count_30_days": "30天内合约交易人数",
        "exchange_count_30_days": "30天内兑换人数",
        "margin_count_30_days": "30天内杠杆借币人数",
        "investment_count_30_days": "30天内理财人数",
        "amm_count_30_days": "30天内AMM人数",
    }
    desc = '报表-用户报表-渠道漏斗报表-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', 'is_daily'])
def async_download_margin_asset_report(
        email: str,
        is_daily: bool,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    if is_daily:
        model = DailyMarginAssetReport
    else:
        model = MonthlyMarginAssetReport
    rows = _get_rows(model, start_ts, end_ts)
    ret = []
    for row in rows:
        item = row
        report_date = row.report_date.strftime('%Y-%m-%d') if is_daily else row.report_date.strftime('%Y-%m')
        record = {
            "report_date": report_date,
            "asset": item.asset,
            "loan_order_count": amount_to_str(item.loan_order_count, 2),
            "flat_order_count": amount_to_str(item.flat_order_count, 2),
            "interest_amount": amount_to_str(item.interest_amount, 2),
            "loan_usd": amount_to_str(item.loan_usd, 2),
            "flat_usd": amount_to_str(item.flat_usd, 2),
            "interest_usd": amount_to_str(item.interest_usd, 2),
            "interest_real_usd": amount_to_str(item.interest_usd * Decimal('0.7'), 2),
            "interest_fund_usd": amount_to_str(item.interest_usd * Decimal('0.3'), 2),
            "active_user_count": item.active_user_count,
            "loan_user_count": item.loan_user_count,
            "flat_user_count": item.flat_user_count,
            "average_loan_balance_amount": amount_to_str(item.average_loan_balance_amount, 8),
            "average_loan_balance_usd": amount_to_str(item.average_loan_balance_usd, 2),
            "average_interest_rate": format_percent(item.average_interest_rate, 4),
        }
        ret.append(record)

    header_mapping = {
        "report_date": "日期",
        "asset": "币种",
        "active_user_count": "活跃用户数",
        "loan_user_count": "借币用户数",
        "loan_order_count": "借币笔数",
        "loan_usd": "借币市值(USD)",
        "flat_user_count": "还币用户数",
        "flat_order_count": "还币笔数",
        "flat_usd": "还币市值(USD)",
        "interest_real_usd": "利息收入(USD)",
        "interest_fund_usd": "基金收入(USD)",
        "average_loan_balance_amount": "平均借贷余额数量",
        "average_loan_balance_usd": "平均借贷余额市值(USD)",
        "average_interest_rate": "平均利率",
    }
    desc = '报表-杠杆-币种-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', 'is_daily'])
def async_download_active_retained_report(
        email: str,
        is_daily: bool,
        start_ts: int,
        end_ts: int,
) -> None:
    def _dump_daily_report(reports):
        result = []
        for report in reports:
            result.append({
                "report_date": report.report_date,
                "id": report.id,
                "origin_type": report.origin_type.value,
                "region_count": report.region_count,
                "next_day_retained_count": report.next_day_retained_count,
                "next_weekly_retained_count": report.next_weekly_retained_count,
                "next_month_retained_count": report.next_month_retained_count
            })
        return result

    def _dump_monthly_report(reports):
        report_ids = {i.id for i in reports}
        report_detail_mapper = defaultdict(list)
        for d in MonthlyUserActiveRetainedDetail.query.filter(
                MonthlyUserActiveRetainedDetail.monthly_report_id.in_(report_ids)
        ).all():
            report_detail_mapper[d.monthly_report_id].append(d)
        result = []
        for report in reports:
            report_info = {
                "report_date": report.report_date.strftime('%Y-%m'),
                "origin_type": report.origin_type.value,
                "region_count": report.region_count,
            }
            details = report_detail_mapper.get(report.id, [])
            date_count_mapper = {}
            for d in details:
                if report.region_count:
                    fmt_str = f'{d.retained_count}({format_percent(Decimal(d.retained_count / report.region_count), 2)})'
                else:
                    fmt_str = f'{d.retained_count}(0%)'
                date_count_mapper[d.report_date.strftime("%Y-%m-%d")] = fmt_str
            for _date in report_dates:
                _date_str = _date.strftime("%Y-%m-%d")
                if _date_str not in date_count_mapper:
                    date_count_mapper[_date_str] = None
            report_info.update(date_count_mapper)
            result.append(report_info)
        return result

    def _get_headers():
        if is_daily:
            headers = {
                "report_date": "日期",
                "origin_type": "注册来源",
                "region_count": "注册人数",
                "next_day_retained_count": "次日留存",
                "next_weekly_retained_count": "7日留存",
                "next_month_retained_count": "30日留存",
            }
        else:
            min_date = min(report_dates)
            report_dates.remove(min_date)
            columns = {
                i.strftime("%Y-%m-%d"): f'{i.strftime("%Y-%m")}留存'
                for i in sorted(report_dates, reverse=True)
            }
            headers = {
                "report_date": "日期",
                "origin_type": "注册来源",
                "region_count": "注册人数",
                **columns
            }
        return headers

    if not email:
        return

    if is_daily:
        model = DailyUserActiveRetainedReport
        dump_func = _dump_daily_report
    else:
        model = MonthlyUserActiveRetainedReport
        dump_func = _dump_monthly_report
    rows = _get_rows(model, start_ts, end_ts)
    report_dates = {i.report_date for i in rows}
    ret = dump_func(rows)
    header_mapping = _get_headers()
    desc = '用户报表-留存报表-活跃留存-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', ])
def async_download_user_hierarchical_report(
        email: str,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    model = DailyUserHierarchicalReport
    rows = _get_rows(model, start_ts, end_ts)
    ret = []
    for row in rows:
        item = row
        report_date = item.report_date.strftime('%Y-%m-%d')
        user_count_str = f'{item.user_count} ({format_percent(item.user_rate, 2)})'
        active_user_rate = format_percent(item.active_user_count / item.user_count if item.user_count else 0, 2)
        active_user_count_str = f'{item.active_user_count} ({active_user_rate})'
        cet_trading_user_rate = format_percent(item.cet_trading_user_count / item.user_count if item.user_count else 0,
                                               2)
        cet_trading_user_count_str = f'{item.cet_trading_user_count} ({cet_trading_user_rate})'
        spot_trading_user_rate = format_percent(
            item.spot_trading_user_count / item.user_count if item.user_count else 0,
            2)
        spot_trading_user_count_str = f'{item.spot_trading_user_count} ({spot_trading_user_rate})'
        perpetual_trading_user_rate = format_percent(
            item.perpetual_trading_user_count / item.user_count if item.user_count else 0,
            2)
        perpetual_trading_user_count_str = f'{item.perpetual_trading_user_count} ({perpetual_trading_user_rate})'
        margin_trading_user_rate = format_percent(
            item.margin_trading_user_count / item.user_count if item.user_count else 0,
            2)
        margin_trading_user_count_str = f'{item.margin_trading_user_count} ({margin_trading_user_rate})'
        investment_trading_user_rate = format_percent(
            item.investment_trading_user_count / item.user_count if item.user_count else 0,
            2)
        investment_trading_user_count_str = f'{item.investment_trading_user_count} ({investment_trading_user_rate})'
        amm_trading_user_rate = format_percent(
            item.amm_trading_user_count / item.user_count if item.user_count else 0,
            2)
        amm_trading_user_count_str = f'{item.amm_trading_user_count} ({amm_trading_user_rate})'
        ret.append(dict(
            report_date=report_date,
            vip_level="全部" if item.vip_level == model.VipLevel.ALL else item.vip_level.name,
            user_count=user_count_str,
            active_user_count=active_user_count_str,
            avg_cet_position_amount=amount_to_str(item.avg_cet_position_amount, 2),
            cet_trading_user_count=cet_trading_user_count_str,
            spot_trading_user_count=spot_trading_user_count_str,
            median_spot_balance_usd=amount_to_str(item.median_spot_balance_usd, 2),
            perpetual_trading_user_count=perpetual_trading_user_count_str,
            median_perpetual_balance_usd=amount_to_str(item.median_perpetual_balance_usd, 2),
            margin_trading_user_count=margin_trading_user_count_str,
            median_margin_balance_usd=amount_to_str(item.median_margin_balance_usd, 2),
            investment_trading_user_count=investment_trading_user_count_str,
            median_investment_balance_usd=amount_to_str(item.median_investment_balance_usd, 2),
            amm_trading_user_count=amm_trading_user_count_str,
            median_amm_balance_usd=amount_to_str(item.median_amm_balance_usd, 2),
        ))

    header_mapping = {
        "report_date": "日期",
        "vip_level": "用户等级",
        "user_count": "用户数",
        "active_user_count": "近30天活跃用户",
        "avg_cet_position_amount": "平均CET持仓量",
        "cet_trading_user_count": "CET交易渗透用户",
        "spot_trading_user_count": "现货渗透用户",
        "median_spot_balance_usd": "现货资产中位数(USD)",
        "perpetual_trading_user_count": "合约渗透用户",
        "median_perpetual_balance_usd": "合约资产中位数(USD)",
        "margin_trading_user_count": "杠杆渗透用户",
        "median_margin_balance_usd": "杠杆资产中位数(USD)",
        "investment_trading_user_count": "理财渗透用户",
        "median_investment_balance_usd": "理财资产中位数(USD)",
        "amm_trading_user_count": "AMM渗透用户",
        "median_amm_balance_usd": "做市资产中位数(USD)",
    }
    desc = '报表-用户报表-用户分层报表-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email', ])
def async_download_balance_asset_report(
        email: str,
        start_ts: int,
        end_ts: int,
) -> None:
    if not email:
        return
    model = DailyBalanceReport
    rows = _get_rows(model, start_ts, end_ts)
    ret = []
    for row in rows:
        report_date = row.report_date.strftime('%Y-%m-%d')
        ret.append(
            {
                "report_date": report_date,
                "count_0": row.count_0,
                "count_1": row.count_1,
                "count_2": row.count_2,
                "count_3": row.count_3,
                "count_4": row.count_4,
                "count_5": row.count_5,
                "count_6": row.count_6,
                "count_7": row.count_7,
                "threshold": row.threshold,
                "asset": row.asset,
                "account_type": row.account_type.name if row.account_type else 'ALL',
                "total_usd": quantize_amount(row.total_usd, 2),
                "total_amount": quantize_amount(row.total_amount, 8),
                # "total_count": row.user_count,
                "user_count": sum([
                    row.count_1,
                    row.count_2,
                    row.count_3,
                    row.count_4,
                    row.count_5,
                    row.count_6,
                    row.count_7,
                ]),
            }
        )

    header_mapping = {
        "report_date": "时间",
        "asset": "币种",
        "account_type": "账户类型",
        "total_amount": "总量",
        "total_usd": "总市值（USD）",
        "threshold": "资产阈值",
        "user_count": "资产用户数(> 阈值)",
        "count_0": "（0~1）* 阈值",
        "count_1": "（1~10）* 阈值",
        "count_2": "（10~100）* 阈值",
        "count_3": "（100~1000）* 阈值",
        "count_4": "（1000~10000）* 阈值",
        "count_5": "（10000~100000）* 阈值",
        "count_6": "（100000~1000000）* 阈值",
        "count_7": "> 1000000 * 阈值",
    }
    desc = '报表-资产报表-异步下载'
    _send_async_download_email(email, ret, header_mapping, desc)


@celery_task
@lock_call(with_args=['email'])
def async_download_exchanges_assets_trade_report(email: str, start_ts: int, end_ts: int):
    """ 竞品币种-交易额-异步下载 """
    from app.api.admin.report.exchanges_assets import AssetTradeReportAsyncDownloadResource

    desc, header_map, items = AssetTradeReportAsyncDownloadResource.build_download_data(start_ts, end_ts)
    _send_async_download_email(email, items, header_map, desc)


@celery_task
@lock_call(with_args=['email'])
def async_download_deposit_bonus_history_report(
        activity_id: int,
        config_id: int,
        user_id: int,
        status: str | None,
        email: str
):
    """ 运营-活动-充值福利-参与详情-异步下载 """
    from app.api.admin.activity.deposit_bonus import DepositBonusActivityHistoryReportAsyncDownloadResource

    desc, header_map, items = DepositBonusActivityHistoryReportAsyncDownloadResource.build_download_data(
        activity_id, config_id, user_id, status)
    _send_async_download_email(email, items, header_map, desc)


def _get_rows(model, start_ts: int, end_ts: int) -> list:
    start_date = timestamp_to_date(start_ts)
    end_date = timestamp_to_date(end_ts)
    rows = model.query.filter(
        model.report_date >= start_date,
        model.report_date < end_date
    ).order_by(model.id.desc()).all()
    return rows


def _send_async_download_email(email: str, ret: list, header_mapping: dict, desc: str) -> None:
    sheet_datas = {}
    i = 0
    for chunk_data in batch_iter(ret, 250000):
        sheet_datas.update({
            f'sheet{i}': {
                'header_mapper': header_mapping,
                'data': chunk_data
            }
        })
        i += 1
    if not sheet_datas:
        return
    file_url = export_xlsx_with_sheet(
        sheet_data=sheet_datas
    )
    email_content = f'''{desc}链接（请及时下载，避免超时失效）：
        {file_url}'''

    from app.business.email import send_internal_user_email
    send_internal_user_email.delay(
        email=email,
        email_content=email_content,
        subject=f'{desc}',
        lang=Language.ZH_HANS_CN.value
    )


@celery_task
@lock_call(with_args=['email'])
def async_download_sms_detail_report(
        email: str,
        start: int,
        end: int,
        country: str | None,
        business: str | None,
        order: str | None,
        sort: str | None,
        business_translation_map: Dict[str, str],
) -> None:
    start = timestamp_to_datetime(start)
    end = timestamp_to_datetime(end)

    params = {
        "created_at__gte": start,
        "created_at__lt": end + datetime.timedelta(days=1),
    }
    if country:
        params["country"] = country
    if business:
        params["business"] = business
    query = MobileMessageRecordMySQL.query.filter(
        MobileMessageRecordMySQL.created_at >= start,
        MobileMessageRecordMySQL.created_at < end + datetime.timedelta(days=1)
    )
    if country:
        query = query.filter(MobileMessageRecordMySQL.country == country)
    if business:
        query = query.filter(MobileMessageRecordMySQL.business == business)
    if order:
        if sort == 'asc':
            query = query.order_by(getattr(MobileMessageRecordMySQL, order))
        else:
            query = query.order_by(getattr(MobileMessageRecordMySQL, order).desc())
    records = query.all()
    result = []
    user_ids = {item.user_id for item in records if item.user_id}
    user_email_map = dict()
    for ids in batch_iter(user_ids, 2000):
        tmp = User.query.filter(
            User.id.in_(ids)
        ).with_entities(User.id, User.email).all()
        user_email_map.update(dict(tmp))
    spot_res = TradeHistoryDB.get_balance_history_users(user_ids, 0, current_timestamp(to_int=True))
    perpetual_res = PerpetualHistoryDB.get_balance_history_users(user_ids, 0, current_timestamp(to_int=True))
    balance_history_user_ids = spot_res | perpetual_res
    for item in records:
        country_str = ''
        c = get_country(item.country)
        if c:
            country_str = c.cn_name
        result.append(dict(
            report_date=item.created_at.strftime('%Y-%m-%d'),
            id=str(item.id),
            user_id=item.user_id or '',
            email=user_email_map.get(item.user_id, ''),
            balance=item.balance,
            created_at=item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            user_registered_at=item.user_registered_at.strftime('%Y-%m-%d %H:%M:%S') if item.user_registered_at else '',
            has_user_binded_mobile='是' if item.has_user_binded_mobile else '否',
            country=country_str,
            mobile_num=f'+{item.mobile_country_code}-{item.mobile_num}',
            business=business_translation_map.get(item.business, item.business),
            has_balance_history='是' if item.user_id in balance_history_user_ids else '否'
        ))
    header_map = {
        'report_date': '日期',
        'id': '短信ID',
        'created_at': '短信发送时间',
        'user_id': '用户ID',
        'email': '邮箱',
        'balance': '用户资产(USD)',
        'has_balance_history': '是否有资产流水',
        'user_registered_at': '用户注册时间',
        'has_user_binded_mobile': '用户是否绑定手机',
        'country': '国家',
        'mobile_num': '手机号',
        'business': '短信业务类型',
    }
    desc = '报表-短信明细报表-异步下载'
    _send_async_download_email(email, result, header_map, desc)


@celery_task
@lock_call(with_args=['email'])
def async_download_kol_referral_report(
    email: str,
    ref_id: int,
) -> None:
    records = KolReferralExportReport.query.filter(
        KolReferralExportReport.ref_id == ref_id,
    ).order_by(KolReferralExportReport.id.asc()).all()

    result = []
    for item in records:
        result.append(
            {
                'user_id': item.user_id,
                'email': item.email,
                'referral_code': item.referral_code,
                'referral_user_count': item.referral_user_count,
                'referral_trading_user_count': item.referral_trading_user_count,
                'referral_depositing_user_count': item.referral_depositing_user_count,
                'referral_deposit_amount': item.referral_deposit_amount,
                'referral_spot_trading_volume': item.referral_spot_trading_volume,
                'referral_spot_trading_fee': item.referral_spot_trading_fee,
                'referral_perpetual_trading_volume': item.referral_perpetual_trading_volume,
                'referral_perpetual_trading_fee': item.referral_perpetual_trading_fee,
                'referral_reward': item.referral_reward
            }
        )

    header_map = {
        'user_id': '用户ID',
        'email': '邮箱',
        'referral_code': '邀请码',
        'referral_user_count': '邀请人数',
        'referral_trading_user_count': '邀请交易人数',
        'referral_depositing_user_count': '邀请充值人数',
        'referral_deposit_amount': '邀请充值金额',
        'referral_spot_trading_volume': '邀请现货交易额',
        'referral_spot_trading_fee': '邀请现货手续费',
        'referral_perpetual_trading_volume': '邀请合约交易额',
        'referral_perpetual_trading_fee': '邀请合约手续费',
        'referral_reward': '返佣金额'
    }
    desc = '用户-KOL邀请数据导出-异步下载'
    _send_async_download_email(email, result, header_map, desc)
    
    
law_enforcement_header = {
    LawEnforcementExportReport.ExportType.INFO.value: {
        "email": "Registered email",
        "register_time": "Date of Registration (UTC+0)",
        "register_ip": "Registration IP",
        "mobile": "Phone Number",
        "is_kyc_passed": "ID Verification (KYC)",
        "balance_usd": "Account balance (USD)",
        "sub_accounts_count": "Sub Account"
        },
    LawEnforcementExportReport.ExportType.KYC.value: {
        "country": "Nation",
        "user_name": "Name",
        "id_type": "ID Type",
        "id_number": "ID Number",
        "passed_at": "KYC Date (UTC+0)",
    },
    "login_histroy": {
        "login_at": "Login Time (UTC+0)",
        "login_addr": "Location",
        "login_ip": "IP Address",
        "login_device": "Device",
        },
    "deposit_history": {
        "created_at": "Time (UTC+0)",
        "deposit_type": "Type",
        "asset": "Token",
        "chain": "Network",
        "amount": "Amount",
        "from_address": "Deposit Address",
        "memo": "Memo",
        "tx_id": "TXID",
        },
    "withdrawal_history": {
        "created_at": "Time (UTC+0)",
        "withdrawal_type": "Type",
        "asset": "Token",
        "chain": "Network",
        "amount": "Amount",
        "to_address": "Destination Address",
        "memo": "Memo",
        "tx_id": "TXID",
        },
    "spot_order_history": {
        "created_at": "Time (UTC+0)",
        "is_margin": "Margin Trading (YES/NO)",
        "market": "Market (spot trading)",
        "direction": "Buy/Sell",
        "amount": "Execution Amount",
        "avg_price": "Execution Price",
        "value": "Execution Value",
        },
    "exchange_order_history": {
        "created_at": "Time (UTC+0)",
        "from_asset": "Exchange Currency",
        "from_amount": "Exchange Amount",
        "to_asset": "Received Currency",
        "to_amount": "Received Amount",
        },
}

def _export_law_enforcement_login_history(user_id, start_time, end_time) -> List[Dict[str, Union[str, int]]]:
    query = LoginHistory.query \
        .filter(LoginHistory.user_id == user_id,
                LoginHistory.successful.is_(True),
                LoginHistory.created_at <= end_time) \
        .order_by(LoginHistory.id.desc())

    res = []
    for item in query:
        res.append({
            "login_at": item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            "login_addr": GeoIP(item.ip).location or 'Other',
            "login_ip": item.ip,
            "login_device": item.device,
        })
    return res

def _export_law_enforcement_deposit_history(user_id, start_time, end_time) -> List[Dict[str, Union[str, int]]]:
    query = Deposit.query \
        .filter(Deposit.user_id == user_id,
                Deposit.status != Deposit.Status.CANCELLED,
                Deposit.created_at <= end_time) \
        .with_entities(
            Deposit.created_at,
            Deposit.user_id,
            Deposit.sender_user_id,
            Deposit.type,
            Deposit.asset,
            Deposit.chain,
            Deposit.amount,
            Deposit.address,
            Deposit.memo,
            Deposit.tx_id,
        )\
        .order_by(Deposit.id.desc())

    res = []
    for item in query:
        res.append({
            "created_at": item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            "deposit_type": item.type.name.lower() if item.type != Deposit.Type.LOCAL else "inter-user transfer",
            "asset": item.asset,
            "chain": item.chain,
            "amount": item.amount,
            "from_address": item.sender_user_id if item.type == Deposit.Type.LOCAL else item.address,
            "memo": item.memo,
            "tx_id": item.tx_id,
        })
    return res

def _export_law_enforcement_withdrawal_history(user_id, start_time, end_time) -> List[Dict[str, Union[str, int]]]:
    query = Withdrawal.query \
        .filter(Withdrawal.user_id == user_id,
                Withdrawal.status != Withdrawal.Status.CANCELLED,
                Withdrawal.created_at <= end_time) \
        .with_entities(
            Withdrawal.created_at,
            Withdrawal.user_id,
            Withdrawal.recipient_user_id,
            Withdrawal.type,
            Withdrawal.asset,
            Withdrawal.chain,
            Withdrawal.amount,
            Withdrawal.address,
            Withdrawal.memo,
            Withdrawal.tx_id,
        )\
        .order_by(Withdrawal.id.desc())

    res = []
    for item in query:
        res.append({
            "created_at": item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            "withdrawal_type": item.type.name.lower() if item.type != Withdrawal.Type.LOCAL else "inter-user transfer",
            "asset": item.asset,
            "chain": item.chain,
            "amount": item.amount,
            "to_address": item.recipient_user_id if item.type == Withdrawal.Type.LOCAL else item.address,
            "memo": item.memo,
            "tx_id": item.tx_id,
        })
    return res

def _export_law_enforcement_spot_order_history(user_id, start_time, end_time) -> List[Dict[str, Union[str, int]]]:
    client = ServerClient()
    page, res = 1, []

    while True:
        page_orders = client.user_finished_orders(
            user_id=user_id,
            market='',
            account_id=ALL_RECORD_ACCOUNT_ID,
            start_time=0,
            end_time=datetime_to_time(end_time),
            stop_order_id=None,
            side=0,
            page=page,
            limit=100
        )  

        for order in page_orders:
            res.append({
                "created_at": timestamp_to_datetime(int(order["ctime"])).strftime('%Y-%m-%d %H:%M:%S'),
                "is_margin": "YES" if order["account"] != 0 else "NO",
                "market": order["market"],
                "direction": "BUY" if order["side"] == 2 else "SELL",
                "amount": order["deal_stock"],
                "avg_price": Decimal(order["deal_money"]) / Decimal(order["deal_stock"]) if Decimal(order["deal_stock"]) > 0 else 0,
                "value": order["deal_money"] ,
            })

        if page_orders.has_next:
            page += 1
        else:
            break
    return res

def _expprt_law_enforcement_exchange_order_history(user_id, start_time, end_time) -> List[Dict[str, Union[str, int]]]:
    query = AssetExchangeOrder.query.filter(
            AssetExchangeOrder.user_id == user_id,
            AssetExchangeOrder.created_at <= end_time,
            AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED,
        ).with_entities(
            AssetExchangeOrder.created_at,
            AssetExchangeOrder.source_asset,
            AssetExchangeOrder.source_asset_amount,
            AssetExchangeOrder.target_asset,
            AssetExchangeOrder.target_asset_exchanged_amount,
        )
    res = []
    for item in query:
        res.append({
            "created_at":   item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            "from_asset": item.source_asset,
            "from_amount": item.source_asset_amount,
            "to_asset": item.target_asset,
            "to_amount": item.target_asset_exchanged_amount,
        })
    return res
    
law_enforcement_export_method = {
    "login_histroy": _export_law_enforcement_login_history,
    "deposit_history": _export_law_enforcement_deposit_history,
    "withdrawal_history": _export_law_enforcement_withdrawal_history,
    "spot_order_history": _export_law_enforcement_spot_order_history,
    "exchange_order_history": _expprt_law_enforcement_exchange_order_history,
}

law_enforcement_title = {
    LawEnforcementExportReport.ExportType.INFO.value: "Basic Info",
    LawEnforcementExportReport.ExportType.KYC.value: "KYC",
    "login_histroy": "IP Address",
    "deposit_history": "Deposit Record",
    "withdrawal_history": "Withdrawal Record",
    "spot_order_history": "Trading Record",
    "exchange_order_history": "Exchange Record",
}    

@celery_task
@lock_call(with_args=['email'])
def async_download_law_enforcement_report(
    ref_id: int,
    email: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    if not email:
        return
    
    law_enforcement = LawEnforcementExport.query.filter(
        LawEnforcementExport.id == ref_id,
    ).first()
    if not law_enforcement:
        return
    
    records = LawEnforcementExportReport.query.filter(
        LawEnforcementExportReport.ref_id == ref_id,
    ).order_by(LawEnforcementExportReport.id.asc()).all()

    i, sheet_datas = 1, {}
    for item in records:
        sheet_name = law_enforcement_title[item.type.value]
        sheet_datas.update({
            sheet_name: {
                'header_mapper': law_enforcement_header.get(item.type.value, {}),
                'data': json.loads(item.data)
            }
        })
        i += 1

    for export_type, method in law_enforcement_export_method.items():
        sheet_name = law_enforcement_title[export_type]
        data = method(law_enforcement.user_id, start_time, end_time)
        sheet_datas.update({
            sheet_name: {
                'header_mapper': law_enforcement_header.get(export_type, {}),
                'data': data
            }
        })
        i += 1

    if not sheet_datas:
        return
    file_url = export_xlsx_with_sheet(
        sheet_data=sheet_datas
    )
    desc = '风控-执法用户数据导出-异步下载'
    email_content = f'''{desc}链接（请及时下载，避免超时失效）：
        {file_url}'''

    from app.business.email import send_internal_user_email
    send_internal_user_email.delay(
        email=email,
        email_content=email_content,
        subject=f'{desc}',
        lang=Language.ZH_HANS_CN.value
    )


@celery_task(queue=CeleryQueues.REPORT)
@lock_call(with_args=["user_id", "asset", "ts"])
def async_send_asset_balances_email(user_id: int, asset: str, ts: int | None = None):
    email = User.query.get(user_id).email
    if not email:
        return
    _ts = ts - ts % 86400 if ts else today_timestamp_utc()
    table = ExchangeLogDB.user_account_balance_table(timestamp=_ts)
    date_str = datetime_to_str(timestamp_to_datetime(_ts))
    if not table.exists():
        record = []
        desc = f'统计-资产排名-币种资产全量导出-{asset}-{date_str}(无当日快照)'
    else:
        record = table.select("user_id", "sum(balance)", where=f"asset='{asset}'",
                              group_by="user_id", having="sum(balance) > 0")
        desc = f'统计-资产排名-币种资产全量导出-{asset}-{date_str}'

    result = []
    for v in record:
        uid, amount = v
        if uid == 0:
            continue
        if amount == Decimal():
            continue
        if quantize_amount(amount, 8) == Decimal():
            continue
        _user: User = User.query.get(uid)
        if not _user:
            continue
        is_sub_account = True if _user.user_type == User.UserType.SUB_ACCOUNT else False
        main_user_email = _user.main_user_email
        row = {
            'user_id': uid,
            'amount': amount_to_str(amount, 8),
            'is_sub_account': is_sub_account,
            'main_user_email': main_user_email,
        }
        result.append(row)
    header_map = {
        'user_id': '用户ID',
        'is_sub_account': '是否子账号',
        'main_user_email': '主账号邮箱',
        'amount': '币种数量',
    }
    _send_async_download_email(email, result, header_map, desc)
