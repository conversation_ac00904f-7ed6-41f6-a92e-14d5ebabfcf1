import datetime
from collections import defaultdict
from decimal import Decimal

from flask import current_app
from sqlalchemy import func

from app.business import db, \
    route_module_to_celery_queue, CeleryQueues, scheduled, crontab, lock_call
from app.models import DailySiteIncomeBusinessReport, IncomeType, \
    DailyIncomeHistory, AssetPrice, MonthlySiteIncomeBusinessReport
from app.models import DailyRealTimeIncomeTransferHistory
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import next_month

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def update_site_income_business_report(start_date):
    report_map = defaultdict(Decimal)
    daily_income = DailyIncomeHistory.query.filter(
        DailyIncomeHistory.report_date == start_date
    ).all()

    if not daily_income:
        return

    if not DailyRealTimeIncomeTransferHistory.query.filter(
            DailyRealTimeIncomeTransferHistory.report_date == start_date
    ).first():
        current_app.logger.warning("income process is not finished, stop generate DailySiteIncomeBusinessReport")
        return

    coin_rate = AssetPrice.get_close_price_map(start_date)

    for item in daily_income:
        report_map[item.income_type] += item.amount * coin_rate.get(item.asset, 0)

    row = DailySiteIncomeBusinessReport.get_or_create(report_date=start_date)
    row.spot_trade_fee = report_map[IncomeType.SPOT_TRADE_FEE]
    row.perpetual_trade_fee = report_map[IncomeType.PERPETUAL_TRADE_FEE]
    row.withdraw_fee = report_map[IncomeType.WITHDRAW_FEE]
    row.margin_interest = report_map[IncomeType.MARGIN_INTEREST]
    row.credit_interest = report_map[IncomeType.CREDIT_INTEREST]
    row.pledge_interest = report_map[IncomeType.PLEDGE_INTEREST]
    row.p2p_order_fee = report_map[IncomeType.P2P_ORDER_FEE]
    row.staking_interest = report_map[IncomeType.STAKING_INTEREST]
    row.pre_trading_fee = report_map[IncomeType.PRE_TRADING_FEE]
    row.cleaned_balance = report_map[IncomeType.CLEANED_BALANCE]
    row.withdraw_pay_fee = report_map[IncomeType.WITHDRAW_PAY_FEE]
    row.refer_pay = report_map[IncomeType.REFER_PAY] + report_map[IncomeType.BROKER_PAY]
    row.investment_pay = report_map[IncomeType.INVESTMENT_PAY]
    row.staking_pay = report_map[IncomeType.STAKING_PAY]
    row.amm_trade_pay_fee = report_map[IncomeType.AMM_TRADE_PAY_FEE]
    row.maker_cashback_pay = report_map[IncomeType.MAKER_CASHBACK_PAY]
    row.exchange_fee = report_map[IncomeType.EXCHANGE_FEE]
    row.sign_off_user_balance = report_map[IncomeType.SIGN_OFF_USER_BALANCE]
    db.session.add(row)
    db.session.commit()


def update_monthly_site_income_business_report(start_date, end_date):
    query_data = DailySiteIncomeBusinessReport.query.filter(
        DailySiteIncomeBusinessReport.report_date >= start_date,
        DailySiteIncomeBusinessReport.report_date < end_date,
    ).with_entities(
        func.sum(DailySiteIncomeBusinessReport.spot_trade_fee).label('spot_trade_fee'),
        func.sum(DailySiteIncomeBusinessReport.perpetual_trade_fee).label('perpetual_trade_fee'),
        func.sum(DailySiteIncomeBusinessReport.withdraw_fee).label('withdraw_fee'),
        func.sum(DailySiteIncomeBusinessReport.margin_interest).label('margin_interest'),
        func.sum(DailySiteIncomeBusinessReport.credit_interest).label('credit_interest'),
        func.sum(DailySiteIncomeBusinessReport.pledge_interest).label('pledge_interest'),
        func.sum(DailySiteIncomeBusinessReport.p2p_order_fee).label('p2p_order_fee'),
        func.sum(DailySiteIncomeBusinessReport.staking_interest).label('staking_interest'),
        func.sum(DailySiteIncomeBusinessReport.cleaned_balance).label('cleaned_balance'),
        func.sum(DailySiteIncomeBusinessReport.pre_trading_fee).label('pre_trading_fee'),
        func.sum(DailySiteIncomeBusinessReport.withdraw_pay_fee).label('withdraw_pay_fee'),
        func.sum(DailySiteIncomeBusinessReport.refer_pay).label('refer_pay'),
        func.sum(DailySiteIncomeBusinessReport.investment_pay).label('investment_pay'),
        func.sum(DailySiteIncomeBusinessReport.staking_pay).label('staking_pay'),
        func.sum(DailySiteIncomeBusinessReport.amm_trade_pay_fee).label('amm_trade_pay_fee'),
        func.sum(DailySiteIncomeBusinessReport.maker_cashback_pay).label('maker_cashback_pay'),
        func.sum(DailySiteIncomeBusinessReport.exchange_fee).label('exchange_fee'),
        func.sum(DailySiteIncomeBusinessReport.sign_off_user_balance).label('sign_off_user_balance'),
    ).first()

    record = MonthlySiteIncomeBusinessReport.get_or_create(report_date=start_date)
    record.spot_trade_fee = query_data.spot_trade_fee
    record.perpetual_trade_fee = query_data.perpetual_trade_fee
    record.withdraw_fee = query_data.withdraw_fee
    record.margin_interest = query_data.margin_interest
    record.credit_interest = query_data.credit_interest
    record.pledge_interest = query_data.pledge_interest
    record.p2p_order_fee = query_data.p2p_order_fee
    record.staking_interest = query_data.staking_interest
    record.pre_trading_fee = query_data.pre_trading_fee
    record.withdraw_pay_fee = query_data.withdraw_pay_fee
    record.refer_pay = query_data.refer_pay
    record.investment_pay = query_data.investment_pay
    record.staking_pay = query_data.staking_pay
    record.amm_trade_pay_fee = query_data.amm_trade_pay_fee
    record.maker_cashback_pay = query_data.maker_cashback_pay
    record.exchange_fee = query_data.exchange_fee
    record.sign_off_user_balance = query_data.sign_off_user_balance
    record.cleaned_balance = query_data.cleaned_balance
    db.session.add(record)
    db.session.commit()


@scheduled(crontab(minute=0, hour='2,4'))
@lock_call()
def update_daily_site_income_business_report_schedule():
    today = datetime.datetime.utcnow().date()
    last_record = DailySiteIncomeBusinessReport.query.order_by(
        DailySiteIncomeBusinessReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date - datetime.timedelta(days=1)
    else:
        first_data = DailyIncomeHistory.query.order_by(DailyIncomeHistory.report_date.asc()).first()
        start_date = first_data.report_date
    while start_date < today:
        update_site_income_business_report(start_date)
        start_date += datetime.timedelta(days=1)

    cur_month = datetime.date(today.year, today.month, 1)
    start_month = get_monthly_report_date(MonthlySiteIncomeBusinessReport,
                                          DailySiteIncomeBusinessReport,
                                          include_curr_month=True)
    if not start_month:
        return
    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_site_income_business_report(start_month, end_month)
        start_month = end_month
