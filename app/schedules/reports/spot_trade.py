import datetime
import json
from decimal import Decimal

from flask import current_app

from app.business import TradeSummaryDB, scheduled, crontab, \
    route_module_to_celery_queue, CeleryQueues, lock_call
from app.business.market import MarketRepository
from app.business.market_maker import MarketMakerHelper
from app.business.summary import check_data_ready
from app.models import db, DailySpotTradeReport, AssetPrice, \
    MonthlySpotTradeReport, UserTradeSummary, UserApiFrequencyLimitRecord
from app.schedules.reports.user_report import get_api_deal_users
from app.utils import next_month, batch_iter

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def get_trade_summary_db():
    return TradeSummaryDB.db


def get_year_month_str(report_date):
    return report_date.strftime('%Y%m')


def get_total_fee_map(report_date, trade_summary_db=None):
    """
    total 费率 map
    """
    fee_table = 'user_fee_summary_{}'.format(get_year_month_str(report_date))
    sql = "SELECT asset, SUM(fee) FROM {table_name} WHERE trade_date='{trade_date}' GROUP BY asset;".format(
        table_name=fee_table,
        trade_date=report_date
    )
    if not trade_summary_db:
        trade_summary_db = get_trade_summary_db()
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    records = cursor.fetchall()
    fee_map = {}
    for r in records:
        fee_map[r[0]] = Decimal(r[1])
    return fee_map


def get_mm_fee_map(user_ids, report_date, trade_summary_db=None):
    """
    做市商费率 map
    """
    fee_table = 'user_fee_summary_{}'.format(get_year_month_str(report_date))
    sql = "SELECT asset, SUM(fee) FROM {table_name} WHERE trade_date='{trade_date}' AND user_id in ({user_ids})" \
          " GROUP BY asset;".format(
            table_name=fee_table,
            trade_date=report_date,
            user_ids=",".join([str(_v) for _v in user_ids])
            )
    if not trade_summary_db:
        trade_summary_db = get_trade_summary_db()
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    records = cursor.fetchall()
    fee_map = {}
    for r in records:
        fee_map[r[0]] = Decimal(r[1])
    return fee_map


def get_users_trade_map(user_ids, report_date, trade_summary_db=None, user_id_in=True):
    """
    指定用户交易额 map
    """
    trade_table = 'user_trade_summary_{}'.format(get_year_month_str(report_date))
    if user_id_in:
        in_str = ' in '
    else:
        in_str = ' not in '
    sql = "SELECT money_asset, SUM(deal_volume) FROM {table_name} WHERE trade_date='{trade_date}' AND " \
          "deal_volume > 0 AND user_id {in_str} ({user_ids}) GROUP BY money_asset;".format(
            table_name=trade_table,
            trade_date=report_date,
            in_str=in_str,
            user_ids=",".join([str(_v) for _v in user_ids])
            )
    if not trade_summary_db:
        trade_summary_db = get_trade_summary_db()
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    records = cursor.fetchall()
    trade_map = {}
    for r in records:
        trade_map[r[0]] = Decimal(r[1])
    return trade_map


def list_coin_trade_summary(date, trade_summary_db=None):
    sql = "SELECT deal_count, money_asset, deal_volume, deal_user_list " \
          "FROM coin_trade_summary " \
          "WHERE trade_date = '{}' ".format(date)
    if not trade_summary_db:
        trade_summary_db = get_trade_summary_db()
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    return cursor.fetchall()


def _get_trade_user_set(start_time, end_time):
    _query = UserTradeSummary.query.filter(
        UserTradeSummary.report_date >= start_time,
        UserTradeSummary.report_date < end_time,
    ).with_entities(
        UserTradeSummary.user_id.distinct().label('user_id')
    )
    _query = _query.filter(
        UserTradeSummary.system == UserTradeSummary.System.SPOT)

    return {item.user_id for item in _query}


def update_daily_spot_trade_report(report_date, force_update=False):

    if not TradeSummaryDB.is_data_completed(report_date):
        raise Exception('{} dump_history 数据还未同步完成'.format(report_date))
    if not check_data_ready(report_date, UserTradeSummary.System.SPOT, with_fee=False):
        current_app.logger.warning("{} update_daily_spot_trade_report-UserTradeSummary SPOT 数据未就绪".format(report_date))
        return

    coin_rate = AssetPrice.get_close_price_map(report_date)

    # 总交易额、总交易用户
    trade_summary_db = get_trade_summary_db()
    total_user_set, total_trade_usd = set(), Decimal()
    total_deal_count = 0
    trade_summary_list = list_coin_trade_summary(report_date, trade_summary_db)
    for item in trade_summary_list:
        deal_count, money_asset, deal_volume, deal_user_list = item
        if deal_volume == 0:
            continue
        total_trade_usd += Decimal(deal_volume) * coin_rate.get(
            money_asset, Decimal())
        total_deal_count += deal_count
        total_user_set |= set(json.loads(deal_user_list))

    if total_trade_usd == 0:
        raise Exception('{}统计交易额为0'.format(report_date))

    # 总手续费
    fee_map = get_total_fee_map(report_date, trade_summary_db)
    total_fee_usd, total_cet_fee_usd = Decimal(), Decimal()
    for asset, amount in fee_map.items():
        fee_usd = amount * coin_rate.get(asset, Decimal())
        total_fee_usd += fee_usd
        if asset == 'CET':
            total_cet_fee_usd = fee_usd

    if total_fee_usd == 0:
        raise Exception('{}统计手续费为0'.format(report_date))

    # 做市商手续费
    mm_ids = MarketMakerHelper.list_all_maker_ids()
    mm_fee_map = get_mm_fee_map(mm_ids, report_date, trade_summary_db)
    mm_fee_usd = Decimal()
    for asset, amount in mm_fee_map.items():
        mm_fee_usd += amount * coin_rate.get(asset, Decimal())

    # 普通用户手续费
    nm_fee_usd = total_fee_usd - mm_fee_usd

    # 做市商交易额
    mm_trade_map = get_users_trade_map(mm_ids, report_date, trade_summary_db)
    mm_trade_usd = Decimal()
    for asset, amount in mm_trade_map.items():
        mm_trade_usd += amount * coin_rate.get(asset, Decimal())

    # 普通用户交易额
    nm_trade_usd = total_trade_usd * 2 - mm_trade_usd

    # 使用CET折扣用户占比
    table_name = 'user_fee_summary_{}'.format(get_year_month_str(report_date))

    # 使用CET的用户数
    sql = "SELECT COUNT(DISTINCT(user_id)) FROM {table_name} WHERE trade_date='{trade_date}' AND asset='CET';".format(
            table_name=table_name,
            trade_date=report_date,
            )
    if not trade_summary_db:
        trade_summary_db = get_trade_summary_db()
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    records = cursor.fetchall()
    cet_trade_user_count = 0
    for count, in records:
        cet_trade_user_count += count

    # 所有用户数
    sql = "SELECT COUNT(DISTINCT(user_id)) FROM {table_name} WHERE trade_date='{trade_date}';".format(
            table_name=table_name,
            trade_date=report_date,
            )
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    records = cursor.fetchall()
    total_trade_user_count = 0
    for count, in records:
        total_trade_user_count += count

    # 新增交易用户数
    active_trade_user_set = \
        _get_trade_user_set(report_date, report_date + datetime.timedelta(days=1))
    traded_before_user_ids = []
    for chunk_user_ids in batch_iter(active_trade_user_set, 1000):
        chunk_traded_before_user_ids = UserTradeSummary.query.filter(
            UserTradeSummary.report_date < report_date,
            UserTradeSummary.user_id.in_(chunk_user_ids)
        ).with_entities(
            UserTradeSummary.user_id.distinct().label('user_id')
        ).all()
        traded_before_user_ids.extend(chunk_traded_before_user_ids)
    traded_before_user_id_set = {item.user_id
                                 for item in traded_before_user_ids}
    increase_trade_user_count = len(
        active_trade_user_set - traded_before_user_id_set)
    market_count = MarketRepository.get_online_market_count()
    api_users = get_api_deal_users({
        UserApiFrequencyLimitRecord.ApiGroups.ORDER.name,
        UserApiFrequencyLimitRecord.ApiGroups.ORDERS.name,
    }, report_date, report_date + datetime.timedelta(days=1))

    record = DailySpotTradeReport.get_or_create(report_date)
    if not record.trade_usd or force_update:
        record.trade_usd = total_trade_usd
        record.fee_usd = total_fee_usd
        record.deal_user_list = json.dumps(list(total_user_set))
        record.deal_user_count = len(total_user_set)
        record.deal_count = total_deal_count
        record.market_maker_trade_usd = mm_trade_usd
        record.market_maker_fee_usd = mm_fee_usd
        record.cet_fee_usd = total_cet_fee_usd
        record.avg_fee_rate = total_fee_usd / (total_trade_usd * 2) if total_trade_usd else Decimal()
        record.avg_mm_fee_rate = mm_fee_usd / mm_trade_usd if mm_trade_usd else Decimal()
        record.avg_normal_fee_rate = nm_fee_usd / nm_trade_usd if nm_trade_usd else Decimal()
        record.normal_fee_rate = nm_fee_usd / total_fee_usd if total_fee_usd else Decimal()
        record.normal_trade_rate = nm_trade_usd / (total_trade_usd * 2) if total_trade_usd else Decimal()
        record.cet_fee_rate = total_cet_fee_usd / total_fee_usd if total_fee_usd else Decimal()
        record.cet_user_rate = (cet_trade_user_count / total_trade_user_count) \
            if total_trade_user_count else Decimal()
        record.increase_trade_user_count = increase_trade_user_count
        record.market_count = market_count
        record.api_user_count = len(api_users)
        db.session.add(record)
        db.session.commit()


def update_monthly_spot_trade(start_month, end_month, force_update=False):
    if not UserTradeSummary.check_data_ready(start_month, UserTradeSummary.System.SPOT):
        current_app.logger.warning(
            "{} update_daily_spot_trade_report-UserTradeSummary SPOT 数据未就绪".format(start_month)
        )
        return

    query = DailySpotTradeReport.query.filter(
        DailySpotTradeReport.report_date >= start_month,
        DailySpotTradeReport.report_date < end_month
    ).order_by(DailySpotTradeReport.report_date.asc()).all()
    trade_usd, fee_usd, market_maker_trade_usd, market_maker_fee_usd, cet_fee_usd, deal_user_set = \
        Decimal(), Decimal(), Decimal(), Decimal(), Decimal(), set()
    total_deal_count = 0
    for item in query:
        trade_usd += item.trade_usd
        fee_usd += item.fee_usd
        market_maker_trade_usd += item.market_maker_trade_usd
        market_maker_fee_usd += item.market_maker_fee_usd
        cet_fee_usd += item.cet_fee_usd
        deal_user_set |= set(json.loads(item.deal_user_list))
        total_deal_count += item.deal_count

    table_name = 'user_fee_summary_{}'.format(get_year_month_str(start_month))
    # 使用CET的用户数
    sql = f"SELECT COUNT(DISTINCT(user_id)) FROM {table_name} WHERE asset='CET';"
    trade_summary_db = get_trade_summary_db()
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    records = cursor.fetchall()
    cet_trade_user_count = 0
    for count, in records:
        cet_trade_user_count += count
    # 所有用户数
    sql = f"SELECT COUNT(DISTINCT(user_id)) FROM {table_name}"
    cursor = trade_summary_db.cursor()
    cursor.execute(sql)
    records = cursor.fetchall()
    total_trade_user_count = 0
    for count, in records:
        total_trade_user_count += count

    # 新增交易用户
    active_trade_user_set = \
        _get_trade_user_set(start_month, end_month)
    traded_before_user_ids = []
    for chunk_user_ids in batch_iter(active_trade_user_set, 1000):
        chunk_traded_before_user_ids = UserTradeSummary.query.filter(
            UserTradeSummary.report_date < start_month,
            UserTradeSummary.user_id.in_(chunk_user_ids)
        ).with_entities(
            UserTradeSummary.user_id.distinct().label('user_id')
        ).all()
        traded_before_user_ids.extend(chunk_traded_before_user_ids)
    traded_before_user_id_set = {item.user_id
                                 for item in traded_before_user_ids}
    increase_trade_user_count = len(
        active_trade_user_set - traded_before_user_id_set)
    market_count = query[-1].market_count if query else 0
    api_users = get_api_deal_users({
        UserApiFrequencyLimitRecord.ApiGroups.ORDER.name,
        UserApiFrequencyLimitRecord.ApiGroups.ORDERS.name,
    }, start_month, end_month)

    record = MonthlySpotTradeReport.get_or_create(start_month)
    if not record.trade_usd or force_update:
        record.trade_usd = trade_usd
        record.fee_usd = fee_usd
        record.market_maker_trade_usd = market_maker_trade_usd
        record.market_maker_fee_usd = market_maker_fee_usd
        record.cet_fee_usd = cet_fee_usd
        record.deal_user_list = json.dumps(list(deal_user_set))
        record.deal_user_count = len(deal_user_set)
        record.deal_count = total_deal_count
        record.avg_fee_rate = fee_usd / (trade_usd * 2) if trade_usd else Decimal()
        record.avg_mm_fee_rate = market_maker_fee_usd / market_maker_trade_usd if market_maker_trade_usd else Decimal()
        nm_fee_usd = fee_usd - market_maker_fee_usd
        nm_trade_usd = trade_usd * 2 - market_maker_trade_usd
        record.avg_normal_fee_rate = nm_fee_usd / nm_trade_usd if nm_trade_usd else Decimal()
        record.normal_fee_rate = nm_fee_usd / fee_usd if fee_usd else Decimal()
        record.normal_trade_rate = nm_trade_usd / (trade_usd * 2) if trade_usd else Decimal()
        record.cet_fee_rate = cet_fee_usd / fee_usd if fee_usd else Decimal()
        record.cet_user_rate = (cet_trade_user_count / total_trade_user_count) \
            if total_trade_user_count else Decimal()
        record.increase_trade_user_count = increase_trade_user_count
        record.market_count = market_count
        record.api_user_count = len(api_users)
        db.session.add(record)
        db.session.commit()


@scheduled(crontab(minute=45, hour='0-4'))
@lock_call()
def update_daily_spot_trade_schedule(force_update=False):
    today = datetime.datetime.utcnow().date()
    last_record = DailySpotTradeReport.query.order_by(
        DailySpotTradeReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today + datetime.timedelta(days=-90)
    while start_date < today:
        try:
            update_daily_spot_trade_report(start_date, force_update)
        except Exception as ex:
            db.session.rollback()
            current_app.logger.exception(ex)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute=50, hour='0-1'))
@lock_call()
def update_monthly_spot_trade_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    last_record = MonthlySpotTradeReport.query.order_by(
        MonthlySpotTradeReport.report_date.desc()
    ).first()

    if last_record:
        start_month = last_record.report_date
    else:
        start_month = cur_month

    try:
        while start_month <= cur_month:
            end_month = next_month(start_month.year, start_month.month)
            update_monthly_spot_trade(start_month, end_month, force_update=True)
            start_month = end_month
    except Exception as ex:
        db.session.rollback()
        current_app.logger.exception(ex)
