# -*- coding: utf-8 -*-

import json
from decimal import Decimal

from celery.schedules import crontab

from app.business import lock_call
from app.business.balance.helper import UserTotalBalanceHelper
from app.common import CeleryQueues
from app.common.countries import get_country
from app.models import (
    KycVerification,
    LawEnforcementExport,
    LawEnforcementExportReport,
    User,
    SubAccount,
    func,
    db,
)
from app.utils import route_module_to_celery_queue, scheduled, GeoIP
from app.utils.amount import amount_to_str

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def _get_law_enforcement_info_report(record):
    user = User.query.filter(User.id == record.user_id).first()
    if not user:
        return []

    balance_dict = UserTotalBalanceHelper([record.user_id]).get_user_balances()
    sub_accounts_cnt = SubAccount.query.filter(
            SubAccount.main_user_id == user.id,
            SubAccount.status == SubAccount.Status.VALID,
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
        ).with_entities(func.count()).scalar() or 0
    data = {
        "email": user.email,
        "register_time": user.created_at.strftime("%Y-%m-%d %H:%M:%S"),
        "register_ip": user.registration_ip + "(%s)" % GeoIP(user.registration_ip, lang="en").location,
        "mobile": user.mobile if user.mobile else "NO",
        "is_kyc_passed": "YES" if user.kyc_status == User.KYCStatus.PASSED else "NO",
        "balance_usd": amount_to_str(balance_dict.get(record.user_id, Decimal()), 2),
        "sub_accounts_count": sub_accounts_cnt,
    }
    return [data]


def _get_law_enforcement_kyc_report(record):
    last_kyc = (
        KycVerification.query.filter(
            KycVerification.user_id == record.user_id,
            KycVerification.status == KycVerification.Status.PASSED,
        )
        .order_by(KycVerification.id.desc())
        .first()
    )
    if not last_kyc:
        return []

    country = get_country(last_kyc.country)
    # 产品要求，如果没有通过时间取创建时间
    audit_at = last_kyc.last_passed_at if last_kyc.last_passed_at else last_kyc.created_at
    data = {
        "front_img": (
            file.private_url if (file := last_kyc.front_img_file) is not None else ""
        ),
        "face_img": (
            file.private_url if (file := last_kyc.face_img_file) is not None else ""
        ),
        "country": country.en_name if country else last_kyc.country,
        "user_name": last_kyc.name,
        "id_type": last_kyc.id_type.name,
        "id_number": last_kyc.id_number,
        "passed_at":  audit_at.strftime("%Y-%m-%d") if audit_at else "",
    }
    return [data]


law_enforcement_export_method = {
    LawEnforcementExportReport.ExportType.INFO.value: _get_law_enforcement_info_report,
    LawEnforcementExportReport.ExportType.KYC.value: _get_law_enforcement_kyc_report,
}


def export_law_enforcement_report(record):
    for _type in list(LawEnforcementExportReport.ExportType):
        method = law_enforcement_export_method.get(_type.value, None)
        if not method:
            continue
        data = method(record)
        report = LawEnforcementExportReport(
            ref_id=record.id,
            type=_type,
            user_id=record.user_id,
            data=json.dumps(data),
        )
        db.session.add(report)

    db.session.commit()


@scheduled(crontab(minute="*/5"))
@lock_call()
def export_low_enforcement_report_task():
    """
    执法数据导出分两个部分，一部分是状态数据，一部分是历史数据。
        其中状态数据是在 LawEnforcementExportReport 中定义的 ExportType
        历史数据需要在具体导出任务的时候去查表，因为这些数据保存了也是需要去查表，且这些数据因为是历史数据，不会再变了。
    """
    records = LawEnforcementExport.query.filter(
        LawEnforcementExport.status == LawEnforcementExport.Status.PROCESSING
    ).order_by(LawEnforcementExport.id.asc())

    for record in records:
        try:
            export_law_enforcement_report(record)
            record.status = LawEnforcementExport.Status.FINISHED
        except Exception:
            raise
        db.session.commit()
