# -*- coding: utf-8 -*-
import datetime
from flask import current_app
from typing import Optional, Set
from app.business.external_dbs import ExchangeLogDB, PerpetualLogDB
from app.business.user import filter_active_users
from app.business.user_tag.helper import get_disabled_user_ids, get_sub_account_mapping
from app.common import CeleryQueues
from app.models import db
from app.models.daily import DailyLossUserReport, DailyUserReport
from app.models.exchange import AssetExchangeOrder
from app.models.monthly import MonthlyLossUserReport, MonthlyUserReport
from app.models.summary import UserTradeSummary
from app.models.user import SubAccount
from app.models.user_tag import UserTag
from app.business.user_tag import get_tag_read_table
from app.schedules.reports.utils import get_active_user_set, get_monthly_report_date
from app.utils import scheduled, route_module_to_celery_queue, batch_iter
from app.business import lock_call, crontab
from app.utils.date_ import date_to_datetime, next_month, this_month, today
from app.models import User
from pyroaring import BitMap


route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


class SystemLossUserReport:

    @staticmethod
    def _get_active_user_ids(start_date: Optional[datetime.date],
                             end_date: Optional[datetime.date],
                             exclude_user_set: Set[int]) -> Set[int]:
        user_ids = filter_active_users(start_date, end_date)
        user_ids |= get_active_user_set(start_date, end_date + datetime.timedelta(days=1))
        user_ids -= exclude_user_set
        return user_ids

    @staticmethod
    def get_exclude_user_ids():
        return get_disabled_user_ids() | set(get_sub_account_mapping())

    @classmethod
    def get_new_data(cls, start_date, end_date):
        user_report = DailyUserReport.query.filter(
            DailyUserReport.report_date == start_date
        ).first()
        exclude_user_ids = cls.get_exclude_user_ids()
        one_eighty_days_ago = start_date - datetime.timedelta(days=180)
        ninety_days_ago = start_date - datetime.timedelta(days=90)
        ninety_days_active_user_ids = cls._get_active_user_ids(ninety_days_ago, start_date, exclude_user_ids)
        one_eighty_days_active_user_ids = cls._get_active_user_ids(one_eighty_days_ago, start_date, exclude_user_ids)
        to_be_loss_user_ids = one_eighty_days_active_user_ids - ninety_days_active_user_ids
        all_user_ids = User.query.filter(User.user_type != User.UserType.SUB_ACCOUNT,
                                         User.created_at < date_to_datetime(end_date)).with_entities(User.id).all()
        all_user_ids = {item.id for item in all_user_ids}
        return dict(
            total_user_count=user_report.total_user,
            new_user_count=user_report.increase_user,
            active_user_ids=cls._get_active_user_ids(start_date, end_date, exclude_user_ids),
            loss_user_ids=all_user_ids - one_eighty_days_active_user_ids,
            to_be_loss_user_ids=to_be_loss_user_ids,
        )

    @classmethod
    def get_last_data(cls, report_date: datetime.date):
        exclude_user_ids = cls.get_exclude_user_ids()
        to_be_loss_user_ids, active_user_ids = set(), set()
        last_report = DailyLossUserReport.query.filter(
            DailyLossUserReport.report_date < report_date,
            DailyLossUserReport.type == DailyLossUserReport.Type.SYSTEM
        ).order_by(DailyLossUserReport.report_date.desc()).first()
        if last_report:
            to_be_loss_user_ids = set(BitMap.deserialize(last_report.to_be_loss_users))
            active_user_ids = set(BitMap.deserialize(last_report.active_users))
        all_user_ids = User.query.filter(User.user_type != User.UserType.SUB_ACCOUNT,
                                         User.created_at < date_to_datetime(report_date)).with_entities(User.id).all()
        all_user_ids = {item.id for item in all_user_ids}
        yday = report_date - datetime.timedelta(days=1)
        one_eighty_days_ago = yday - datetime.timedelta(days=180)
        return dict(
            active_user_ids=active_user_ids,
            loss_user_ids=all_user_ids - cls._get_active_user_ids(one_eighty_days_ago, yday, exclude_user_ids),
            to_be_loss_user_ids=to_be_loss_user_ids,
        )


class TradeLossUserReport:

    @staticmethod
    def _get_active_user_ids(start_date: Optional[datetime.date],
                             end_date: Optional[datetime.date],
                             exclude_user_set: Set[int]) -> Set[int]:
        query, exchange_query = UserTradeSummary.query, AssetExchangeOrder.query
        if start_date:
            query = query.filter(UserTradeSummary.report_date >= start_date)
            exchange_query = exchange_query.filter(AssetExchangeOrder.created_at >= start_date)
        if end_date:
            query = query.filter(UserTradeSummary.report_date < end_date)
            exchange_query = exchange_query.filter(AssetExchangeOrder.created_at < end_date)

        user_ids = query.with_entities(UserTradeSummary.user_id.distinct()).all()
        user_ids = {item[0] for item in user_ids}

        exchange_user_ids = exchange_query.with_entities(AssetExchangeOrder.user_id.distinct()).all()
        exchange_user_ids = {item[0] for item in exchange_user_ids}
        exchange_main_user_ids = set()
        for ids in batch_iter(exchange_user_ids - user_ids, 5000):
            tmp = SubAccount.query.filter(
                SubAccount.user_id.in_(ids)
            ).with_entities(SubAccount.main_user_id).all()
            exchange_main_user_ids |= {item.main_user_id for item in tmp}

        user_ids |= exchange_main_user_ids
        return user_ids

    @staticmethod
    def get_exclude_user_ids():
        return set()

    @classmethod
    def get_new_data(cls, start_date, end_date):
        user_ids = set()
        for tag in (
                UserTag.FIRST_SPOT_TRADE_TIME,
                UserTag.FIRST_PERPETUAL_TRADE_TIME,
                UserTag.FIRST_EXCHANGE_TIME
        ):
            read_model = get_tag_read_table(tag)
            deal_users = read_model.query.filter(
                read_model.tag == tag.name
            ).with_entities(read_model.user_id, read_model.value).all()
            start = int(date_to_datetime(start_date).timestamp())
            for item in deal_users:
                ts = int(item.value)
                if ts < start:
                    user_ids.add(item.user_id)

        slice_position_table = PerpetualLogDB.slice_position_table(start) or \
            PerpetualLogDB.slice_position_table(start - 3600)
        if slice_position_table:
            records = slice_position_table.select(
                "distinct user_id",
            )
            perpetual_user_ids = {item[0] for item in records}
        else:
            perpetual_user_ids = set()
        main_ids = set()
        for ids_ in batch_iter(perpetual_user_ids - user_ids, 5000):
            tmp = SubAccount.query.filter(
                SubAccount.user_id.in_(ids_)
            ).with_entities(SubAccount.main_user_id).all()
            main_ids |= {item.main_user_id for item in tmp}
        perpetual_user_ids |= main_ids
        one_eighty_days_ago = start_date - datetime.timedelta(days=180)
        ninety_days_ago = start_date - datetime.timedelta(days=90)
        # 90天内有交易用户
        ninety_days_trade_user_ids = cls._get_active_user_ids(ninety_days_ago, end_date, set())
        # 180天内有交易用户
        one_eighty_trade_user_ids = cls._get_active_user_ids(one_eighty_days_ago, end_date, set())
        to_be_loss_user_ids = one_eighty_trade_user_ids - ninety_days_trade_user_ids - perpetual_user_ids
        loss_user_ids = user_ids - one_eighty_trade_user_ids - perpetual_user_ids
        active_user_ids = cls._get_active_user_ids(start_date, end_date, set())

        report = DailyUserReport.query.filter(
            DailyUserReport.report_date == start_date,
        ).first()
        return dict(
            total_user_count=len(user_ids),
            new_user_count=report.increase_trade_user,
            active_user_ids=active_user_ids,
            loss_user_ids=loss_user_ids,
            to_be_loss_user_ids=to_be_loss_user_ids,
        )

    @classmethod
    def get_last_data(cls, report_date: datetime.date):
        to_be_loss_user_ids, loss_user_ids, active_user_ids = set(), set(), set()

        last_report = DailyLossUserReport.query.filter(
            DailyLossUserReport.report_date < report_date,
            DailyLossUserReport.type == DailyLossUserReport.Type.TRADE
        ).order_by(DailyLossUserReport.report_date.desc()).first()
        if last_report:
            to_be_loss_user_ids = set(BitMap.deserialize(last_report.to_be_loss_users))
            loss_user_ids = set(BitMap.deserialize(last_report.loss_users))
            active_user_ids = set(BitMap.deserialize(last_report.active_users))
        return dict(
            loss_user_ids=loss_user_ids,
            to_be_loss_user_ids=to_be_loss_user_ids,
            active_user_ids=active_user_ids
        )

class LossUserReporter:

    handler_mapping = dict(
        trade=TradeLossUserReport,
        system=SystemLossUserReport,
    )

    def __init__(self, type_: str) -> None:
        self.handler = self.handler_mapping[type_.lower()]()
        self.type_ = type_

    def update_daily_report(self, report_date: datetime.date):

        new_data = self.handler.get_new_data(report_date, report_date + datetime.timedelta(days=1))
        if not new_data:
            return
        history_data = self.handler.get_last_data(report_date)
        report = DailyLossUserReport.get_or_create(report_date=report_date,
                                     type=getattr(DailyLossUserReport.Type, self.type_))
        new_to_be_loss_user_count = len(new_data['to_be_loss_user_ids'] - history_data['to_be_loss_user_ids'])
        new_loss_user_count = len(new_data['loss_user_ids'] - history_data['loss_user_ids'])

        active_user_ids = new_data['active_user_ids']
        to_be_loss_return_count = len(history_data['to_be_loss_user_ids'] & active_user_ids)
        low_balance_user_ids = set()
        # noinspection PyBroadException
        try:
            ts = int(date_to_datetime(report_date).timestamp())
            t = ExchangeLogDB.user_account_balance_sum_table(ts)
            records = t.select("user_id",
                        where=f"balance_usd < 10")
            low_balance_user_ids = {item[0] for item in records}
        except Exception:
            pass
        to_be_loss_balance_user_count = len(new_data['to_be_loss_user_ids'] & low_balance_user_ids)

        loss_return_count = len(history_data['loss_user_ids'] & active_user_ids)
        loss_balance_user_count = len(new_data['loss_user_ids'] & low_balance_user_ids)

        new_user_count = new_data['new_user_count']
        net_new_user_count = new_user_count + loss_return_count - new_loss_user_count

        report.user_count = new_data['total_user_count']
        report.to_be_loss_users = BitMap(new_data['to_be_loss_user_ids']).serialize()
        report.to_be_loss_user_count = len(new_data['to_be_loss_user_ids'])
        report.new_to_be_loss_user_count = new_to_be_loss_user_count
        report.to_be_loss_return_count = to_be_loss_return_count
        report.to_be_loss_balance_user_count = to_be_loss_balance_user_count
        report.loss_balance_user_count = loss_balance_user_count
        report.loss_users = BitMap(new_data['loss_user_ids']).serialize()
        report.active_users = BitMap(new_data['active_user_ids']).serialize()
        report.loss_user_count = len(new_data['loss_user_ids'])
        report.new_loss_user_count = new_loss_user_count
        report.loss_return_count = loss_return_count

        report.new_user_count = new_user_count
        report.net_new_user_count = net_new_user_count
        db.session_add_and_commit(report)

    def update_monthly_report(self, report_date: datetime.date):
        # 当月最后一天
        end_date = next_month(report_date.year, report_date.month) - datetime.timedelta(days=1)
        daily_report = DailyLossUserReport.query.filter(
            DailyLossUserReport.report_date == end_date,
            DailyLossUserReport.type == getattr(DailyLossUserReport.Type, self.type_)
        ).first()
        if not daily_report:
            daily_report = DailyLossUserReport()
        report = MonthlyLossUserReport.get_or_create(report_date=report_date,
                                       type=getattr(MonthlyLossUserReport.Type, self.type_))
        report.user_count = daily_report.user_count
        report.to_be_loss_users = daily_report.to_be_loss_users
        report.to_be_loss_user_count = daily_report.to_be_loss_user_count

        # 上个月最后一天的日报数据
        last_data = self.handler.get_last_data(report_date)
        last_to_be_loss_user_ids, last_loss_user_ids = \
            last_data['to_be_loss_user_ids'], last_data['loss_user_ids']
        to_be_loss_user_set = set()
        if report.to_be_loss_users:
            to_be_loss_user_set = set(BitMap.deserialize(report.to_be_loss_users))
        report.new_to_be_loss_user_count = \
            len(to_be_loss_user_set - last_to_be_loss_user_ids)
        report.to_be_loss_balance_user_count = daily_report.to_be_loss_balance_user_count
        report.active_users = daily_report.active_users
        report.loss_users = daily_report.loss_users
        report.loss_user_count = daily_report.loss_user_count

        loss_user_set = set()
        if report.loss_users:
            loss_user_set = set(BitMap.deserialize(report.loss_users))
        report.new_loss_user_count = len(loss_user_set - last_loss_user_ids)
        report.loss_balance_user_count = daily_report.loss_balance_user_count
        exclude_user_ids = self.handler.get_exclude_user_ids()
        active_user_ids = self.handler._get_active_user_ids(report_date, end_date, exclude_user_ids)
        report.to_be_loss_return_count = len(last_to_be_loss_user_ids & active_user_ids)
        report.loss_return_count = len(last_loss_user_ids & active_user_ids)
        user_report = MonthlyUserReport.query.filter(
            MonthlyUserReport.report_date == report_date,
        ).first()
        if self.type_ == MonthlyLossUserReport.Type.SYSTEM.name:
            report.new_user_count = user_report.increase_user
        else:
            report.new_user_count = user_report.increase_trade_user
        report.net_new_user_count = report.new_user_count + report.loss_return_count - report.new_loss_user_count
        db.session_add_and_commit(report)



@scheduled(crontab(hour='2-3', minute='*/20'))
@lock_call()
def update_daily_trade_loss_user_report_schedule():
    today_ = today()
    last_report = DailyLossUserReport.query.order_by(DailyLossUserReport.report_date.desc()).first()
    if last_report:
        report_date = last_report.report_date + datetime.timedelta(days=1)
    else:
        report_date = today_ - datetime.timedelta(days=90)

    while report_date < today_:
        last_report_date = report_date - datetime.timedelta(days=1)
        for type_ in DailyLossUserReport.Type:
            last_report = DailyLossUserReport.query.filter(
                DailyLossUserReport.report_date == last_report_date,
                DailyLossUserReport.type == type_).first()
            if not last_report:
                current_app.logger.info(f'{last_report} {type_.name} daily loss user report missing')
            if not DailyUserReport.query.filter(
                DailyUserReport.report_date == report_date).first():
                current_app.logger.info(f'{last_report} daily user report missing')
                break

            report = DailyLossUserReport.query.filter(
                DailyLossUserReport.report_date == report_date,
                DailyLossUserReport.type == type_
            ).first()
            if report:
                continue
            LossUserReporter(type_.name).update_daily_report(report_date)
        report_date += datetime.timedelta(days=1)


@scheduled(crontab(day_of_month=1, hour='4-5', minute='*/20'))
@lock_call()
def update_monthly_loss_user_report_schedule():
    cur_month = this_month()

    start_month = get_monthly_report_date(MonthlyLossUserReport, DailyLossUserReport)
    if not start_month:
        start_month = datetime.date(2022, 10, 1)
    
    while start_month < cur_month:
        if MonthlyLossUserReport.query.filter(
            MonthlyLossUserReport.report_date == start_month).first():
            start_month = next_month(start_month.year, start_month.month)
            continue
        end_date = next_month(start_month.year, start_month.month) - datetime.timedelta(days=1)
        if not UserTradeSummary.check_data_ready(end_date):
            current_app.logger.warning("{} update_monthly_loss_user_report-UserTradeSummary 数据未就绪".format(end_date))
            start_month = next_month(start_month.year, start_month.month)
        if not MonthlyUserReport.query.filter(
            MonthlyUserReport.report_date == start_month).first():
            start_month = next_month(start_month.year, start_month.month)
            continue
        for type_ in MonthlyLossUserReport.Type:
            LossUserReporter(type_.name).update_monthly_report(start_month)
        start_month = next_month(start_month.year, start_month.month)