# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from datetime import date, timedelta
from itertools import chain
from typing import List, Dict
import json

from dateutil.relativedelta import relativedelta
from pyroaring import BitMap

from app.business import (
    route_module_to_celery_queue,
    CeleryQueues,
    scheduled,
    crontab,
    lock_call,
    AssetPrice,
    SPOT_ACCOUNT_ID,
    ExchangeLogDB,
)
from app.models import (
    db,
    DailySpotGridSiteReport,
    MonthlySpotGridSiteReport, QuarterlySpotGridSiteReport,
)
from app.models.strategy import (
    SpotGridMarket,
    UserStrategy,
    SpotGridStrategy,
    SpotGridStrategyDetail,
    SpotGridStrategyMatchOrder,
)
from app.caches import MarketCache
from app.utils import today, quantize_amount, next_month, batch_iter
from app.utils.date_ import date_to_datetime, datetime_to_time
from app.schedules.reports.utils import get_monthly_report_date


route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def _get_user_spot_usd_map(ts: int, user_ids: List[int], price_map: Dict[str, Decimal]) -> Dict[int, Decimal]:
    user_usd_map = defaultdict(Decimal)
    table_ = ExchangeLogDB.user_balance_table(ts)
    for ch_user_ids in batch_iter(user_ids, 5000):
        where = f" account={SPOT_ACCOUNT_ID} AND user_id in ({','.join(map(str, ch_user_ids))}) "
        rows = table_.select('balance', 'user_id', 'asset', where=where)
        for r in rows:
            user_usd_map[r[1]] += r[0] * price_map.get(r[2], Decimal())
    return user_usd_map


def update_spot_grid_daily_report(start_date: date, end_date: date):
    _today = today()
    if _today > end_date:
        # 报表日期晚于昨天，查老数据
        running_basic_rows: List[UserStrategy] = UserStrategy.query.filter(
            UserStrategy.type == UserStrategy.Type.SPOT_GRID,
            UserStrategy.status == UserStrategy.Status.RUNNING,
            UserStrategy.started_at < end_date,
        ).with_entities(
            UserStrategy.id,
            UserStrategy.user_id,
            UserStrategy.run_user_id,
        ).all()
        ready_basic_rows: List[UserStrategy] = UserStrategy.query.filter(
            UserStrategy.type == UserStrategy.Type.SPOT_GRID,
            UserStrategy.status == UserStrategy.Status.CREATED,
            UserStrategy.created_at < end_date,
        ).with_entities(
            UserStrategy.id,
            UserStrategy.user_id,
            UserStrategy.run_user_id,
        ).all()  # 待运行的策略
        terminated_basic_rows = UserStrategy.query.filter(
            UserStrategy.type == UserStrategy.Type.SPOT_GRID,
            UserStrategy.status == UserStrategy.Status.TERMINATED,
            UserStrategy.created_at < end_date,
            UserStrategy.terminated_at > end_date,
        ).with_entities(
            UserStrategy.id,
            UserStrategy.user_id,
            UserStrategy.run_user_id,
        ).all()
        running_sty_count = len(terminated_basic_rows) + len(running_basic_rows) + len(ready_basic_rows)
        running_user_ids = set()
        balance_user_ids = set()
        for r in chain(terminated_basic_rows, running_basic_rows, ready_basic_rows):
            running_user_ids.add(r.user_id)
            balance_user_ids.add(r.run_user_id)
        running_sty_rows: List[SpotGridStrategy] = []
        for ch_basic_rows in batch_iter(running_basic_rows, 5000):
            ch_sty_rows = SpotGridStrategy.query.filter(
                SpotGridStrategy.strategy_id.in_([i.id for i in ch_basic_rows])
            ).with_entities(
                SpotGridStrategy.strategy_id,
                SpotGridStrategy.user_id,
                SpotGridStrategy.market,
                SpotGridStrategy.grid_count,
            ).all()
            running_sty_rows.extend(ch_sty_rows)
    else:
        running_sty_rows: List[SpotGridStrategy] = SpotGridStrategy.query.filter(
            SpotGridStrategy.status == SpotGridStrategy.Status.RUNNING,
        ).with_entities(
            SpotGridStrategy.strategy_id,
            SpotGridStrategy.user_id,
            SpotGridStrategy.market,
            SpotGridStrategy.grid_count,
        ).all()  # 运行中的策略
        ready_sty_rows: List[SpotGridStrategy] = SpotGridStrategy.query.filter(
            SpotGridStrategy.status == SpotGridStrategy.Status.TRANSFERRED_IN,
        ).with_entities(
            SpotGridStrategy.strategy_id,
            SpotGridStrategy.user_id,
        ).all()  # 待运行的策略
        running_sty_count = len(running_sty_rows) + len(ready_sty_rows)
        running_user_ids = set(i.user_id for i in chain(running_sty_rows, ready_sty_rows))

        balance_user_ids = set()
        for ch_sty_rows in batch_iter(chain(running_sty_rows, ready_sty_rows), 5000):
            basic_rows = UserStrategy.query.filter(
                UserStrategy.id.in_([i.strategy_id for i in ch_sty_rows]),
            ).with_entities(UserStrategy.run_user_id).all()
            balance_user_ids.update([i.run_user_id for i in basic_rows])

    running_user_count = len(running_user_ids)

    zero = Decimal()
    start_ts = datetime_to_time(date_to_datetime(start_date))
    end_ts = datetime_to_time(date_to_datetime(end_date))
    asset_price_map = AssetPrice.get_close_price_map(start_date)
    grid_markets = [i.name for i in SpotGridMarket.query.with_entities(SpotGridMarket.name).all()]
    market_info_map = {}
    for m in grid_markets:
        market_info_map[m] = MarketCache(m).dict

    match_rows: List[SpotGridStrategyMatchOrder] = SpotGridStrategyMatchOrder.query.filter(
        SpotGridStrategyMatchOrder.match_at >= start_date,
    ).all()
    total_deal_usd = total_fee_usd = zero
    deal_order_count = 0
    for match_row in match_rows:
        orders = []
        if match_row.sell_order_id:
            sell_order = json.loads(match_row.sell_order_info)
            if start_ts <= int(sell_order["finish_time"]) < end_ts:
                orders.append(sell_order)
        if match_row.buy_order_id:
            buy_order = json.loads(match_row.buy_order_info)
            if start_ts <= int(buy_order["finish_time"]) < end_ts:
                orders.append(buy_order)
        deal_order_count += len(orders)
        for o in orders:
            market_info = market_info_map[o["market"]]
            quote_asset, base_asset = market_info["quote_asset"], market_info["base_asset"]
            deal_usd = Decimal(o["deal_money"]) * asset_price_map.get(quote_asset, zero)
            if money_fee := Decimal(o["money_fee"]):
                fee_usd = money_fee * asset_price_map.get(quote_asset, zero)
            else:
                fee_usd = Decimal(o["stock_fee"]) * asset_price_map.get(base_asset, zero)
            total_deal_usd += deal_usd
            total_fee_usd += fee_usd

    user_spot_usd_map = _get_user_spot_usd_map(start_ts, list(balance_user_ids), asset_price_map)
    total_sty_usd = sum(user_spot_usd_map.values())

    grid_profit_usd = zero
    for ch_sty_rows in batch_iter(running_sty_rows, 5000):
        sty_market_map = {i.strategy_id: i.market for i in ch_sty_rows}
        ch_detail_rows = SpotGridStrategyDetail.query.filter(
            SpotGridStrategyDetail.strategy_id.in_(list(sty_market_map))
        ).with_entities(
            SpotGridStrategyDetail.strategy_id,
            SpotGridStrategyDetail.grid_profit,
        ).all()
        for detail in ch_detail_rows:
            market_info = market_info_map[sty_market_map[detail.strategy_id]]
            quote_asset = market_info["quote_asset"]
            grid_profit_usd += detail.grid_profit * asset_price_map.get(quote_asset, zero)

    created_sty_rows: List[SpotGridStrategy] = SpotGridStrategy.query.filter(
        SpotGridStrategy.created_at >= start_date,
        SpotGridStrategy.created_at < end_date,
    ).with_entities(
        SpotGridStrategy.strategy_id,
        SpotGridStrategy.user_id,
    ).all()  # 当天创建的策略
    inc_sty_count = len(created_sty_rows)
    inc_user_ids = {i.user_id for i in created_sty_rows}
    inc_user_count = len(inc_user_ids)

    site_user_bitmap = BitMap()
    site_user_bitmap.update(inc_user_ids)
    last_side_row: DailySpotGridSiteReport = DailySpotGridSiteReport.query.filter(
        DailySpotGridSiteReport.report_date < start_date,
    ).order_by(DailySpotGridSiteReport.report_date.desc()).first()
    if last_side_row:
        site_history_user_bitmap: BitMap = BitMap.deserialize(last_side_row.history_user_bitmap)
        new_user_bitmap = site_user_bitmap.difference(site_history_user_bitmap)  # 删除老的
        new_user_count = len(new_user_bitmap)
    else:
        site_history_user_bitmap = BitMap()
        new_user_count = len(inc_user_ids)
    site_history_user_bitmap.update(inc_user_ids)

    site_row: DailySpotGridSiteReport = DailySpotGridSiteReport.get_or_create(report_date=start_date)
    site_row.total_deal_usd = quantize_amount(total_deal_usd, 2)
    site_row.total_fee_usd = quantize_amount(total_fee_usd, 2)
    site_row.grid_profit_usd = quantize_amount(grid_profit_usd, 2)
    site_row.total_sty_usd = quantize_amount(total_sty_usd, 2)
    site_row.running_sty_count = running_sty_count
    site_row.running_user_count = running_user_count
    site_row.inc_sty_count = inc_sty_count
    site_row.inc_user_count = inc_user_count
    site_row.new_user_count = new_user_count
    site_row.deal_order_count = deal_order_count
    site_row.user_bitmap = site_user_bitmap.serialize()
    site_row.history_user_bitmap = site_history_user_bitmap.serialize()
    db.session.add(site_row)
    db.session.commit()


def update_spot_grid_monthly_report(start_date: date, end_date: date, report_type='monthly'):
    zero = Decimal()
    total_deal_usd = total_fee_usd = grid_profit_usd = total_sty_usd = zero
    running_sty_count = running_user_count = inc_sty_count = 0
    deal_order_count = 0
    user_bitmap = BitMap()  # 当月创建策略的用户
    site_daily_rows: List[DailySpotGridSiteReport] = DailySpotGridSiteReport.query.filter(
        DailySpotGridSiteReport.report_date >= start_date,
        DailySpotGridSiteReport.report_date < end_date,
    ).order_by(DailySpotGridSiteReport.report_date.asc()).all()
    for row in site_daily_rows:
        total_deal_usd += row.total_deal_usd
        total_fee_usd += row.total_fee_usd
        inc_sty_count += row.inc_sty_count
        deal_order_count += row.deal_order_count
        user_bitmap = user_bitmap.union(BitMap.deserialize(row.user_bitmap))
    if site_daily_rows:
        last_daily_row = site_daily_rows[-1]
        grid_profit_usd = last_daily_row.grid_profit_usd
        total_sty_usd = last_daily_row.total_sty_usd
        running_sty_count = last_daily_row.running_sty_count
        running_user_count = last_daily_row.running_user_count

    model = MonthlySpotGridSiteReport if report_type == 'monthly' else QuarterlySpotGridSiteReport

    last_side_row = model.query.filter(
        model.report_date < start_date,
    ).order_by(model.report_date.desc()).first()
    if last_side_row:
        site_history_user_bitmap: BitMap = BitMap.deserialize(last_side_row.history_user_bitmap)
        new_user_bitmap = user_bitmap.difference(site_history_user_bitmap)  # 删除老的
        new_user_count = len(new_user_bitmap)
    else:
        site_history_user_bitmap = BitMap()
        new_user_count = len(user_bitmap)
    site_history_user_bitmap = site_history_user_bitmap.union(user_bitmap)

    site_row = model.get_or_create(report_date=start_date)
    site_row.total_deal_usd = quantize_amount(total_deal_usd, 2)
    site_row.total_fee_usd = quantize_amount(total_fee_usd, 2)
    site_row.grid_profit_usd = quantize_amount(grid_profit_usd, 2)
    site_row.total_sty_usd = quantize_amount(total_sty_usd, 2)
    site_row.running_sty_count = running_sty_count
    site_row.running_user_count = running_user_count
    site_row.inc_sty_count = inc_sty_count
    site_row.inc_user_count = len(user_bitmap)
    site_row.new_user_count = new_user_count
    site_row.deal_order_count = deal_order_count
    site_row.user_bitmap = user_bitmap.serialize()
    site_row.history_user_bitmap = site_history_user_bitmap.serialize()
    db.session.add(site_row)
    db.session.commit()


@scheduled(crontab(minute="8,18", hour="0"))
@lock_call()
def update_spot_grid_daily_report_schedule():
    """ 现货网格日报 """
    today_ = today()
    last_record = DailySpotGridSiteReport.query.order_by(DailySpotGridSiteReport.report_date.desc()).first()
    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        first_sty: SpotGridStrategy = SpotGridStrategy.query.order_by(SpotGridStrategy.id.asc()).first()
        start_date = first_sty.created_at.date()

    while start_date < today_:
        end_date = start_date + timedelta(days=1)
        update_spot_grid_daily_report(start_date, end_date)
        start_date = end_date


@scheduled(crontab(minute="28,38", hour="0"))
@lock_call()
def update_spot_grid_monthly_report_schedule():
    """ 现货网格月报 """
    today_ = today()
    cur_month = date(today_.year, today_.month, 1)
    start_month = get_monthly_report_date(MonthlySpotGridSiteReport, DailySpotGridSiteReport)
    if not start_month:
        start_month = cur_month
    if start_month > cur_month:
        # 重复更新当月的数据
        start_month = cur_month
    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_spot_grid_monthly_report(start_month, end_month)
        start_month = end_month


@scheduled(crontab(minute="25,35", hour="2"))
@lock_call()
def update_spot_grid_quarterly_report_schedule():
    # 定投季报
    last_record = QuarterlySpotGridSiteReport.query.order_by(
        QuarterlySpotGridSiteReport.report_date.desc()
    ).first()
    if last_record:
        start_month = last_record.report_date
    else:
        start_month = date(2022, 1, 1)
    today_ = today()
    cur_month = date(today_.year, today_.month, 1)

    while start_month <= cur_month:
        end_month = date(start_month.year, start_month.month, 1) + relativedelta(months=3)
        update_spot_grid_monthly_report(start_month, end_month, report_type='quarterly')
        start_month = end_month
