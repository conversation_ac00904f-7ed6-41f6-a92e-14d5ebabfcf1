from collections import defaultdict
import datetime
from decimal import Decimal
from typing import Union
import json
from pyroaring import BitMap
from flask import current_app

from app.models import DailyPerpetualPositionReport, MonthlyPerpetualPositionReport, db
from app.business import PerpetualHistoryDB, ExchangeLogDB, lock_call
from app.business.market_maker import MarketMakerHelper
from app.utils import scheduled, next_month, amount_to_str, last_month
from celery.schedules import crontab
from app.utils.date_ import date_to_datetime
from app.caches import PerpetualUserAnalysisCache
from app.caches.perpetual import PerpetualMarketCache, PerpetualOfflineMarketCache
from app.common.constants import PerpetualMarketType
from app.common import CeleryQueues
from app.models import User, SubAccount
from app.models.wallet import AssetPrice
from app.utils.date_ import timestamp_to_date

from app.utils.format import format_percent
from app.utils import quantize_amount, now, route_module_to_celery_queue

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def get_history_positions(user_ids, start_date, end_date):

    start_ts = date_to_datetime(start_date).timestamp()
    end_ts = date_to_datetime(end_date).timestamp()

    tables = set()
    for user_id in user_ids:
        _db, _table = PerpetualHistoryDB.user_to_db_and_table(
            user_id, 'position_history')
        tables.add((_db, _table))

    result = []
    columns = ('create_time', 'update_time', 'market', 'user_id', 'position_id', 'leverage', 'deal_all')
    current_app.logger.info(f'history positions total {len(tables)} tables.')
    for item in tables:
        _db, _table = item
        where = f' update_time >= {start_ts} and update_time < {end_ts}'
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id DESC',
        )
        result.extend(list(dict(zip(columns, item)) for item in records))
    return result


def get_user_balances(user_ids, report_date):
    report_date_str = report_date.strftime('%Y-%m-%d')
    conf_set = set()
    for user_id in user_ids:
        conf = ExchangeLogDB.user_slice_perpetual_balance_hash(user_id)
        conf_set.add(conf)
    tables = set()
    for conf in conf_set:
        table = ExchangeLogDB.table_from_config(
            f'user_slice_perpetual_balance_{conf}', 'user_slice_perpetual_balance')
        tables.add(table)
    current_app.logger.info(f'user balance total {len(tables)} tables.')
    res = dict()
    for table in tables:
        where = f' report_date="{report_date_str}"'
        records = table.select(
            *['user_id', 'sum(balance_usd)'],
            where=where,
            group_by='user_id'
        )
        res.update(dict(records))
    res = {k: v for k, v in res.items() if k in user_ids}
    return res


def update_perpetual_position_report(start_date: datetime.date,
                                     end_date: datetime.date,
                                     report_model: Union[DailyPerpetualPositionReport,
                                                         MonthlyPerpetualPositionReport]):
    current_app.logger.info(f'gettting {start_date} data.')
    position_ids = []
    user_ids = User.query.filter(
        User.user_type.in_([
            User.UserType.NORMAL,
            User.UserType.SUB_ACCOUNT,
            User.UserType.EXTERNAL,
        ])
    ).with_entities(User.id).all()

    user_ids = {item.id for item in user_ids}
    market_maker_ids = MarketMakerHelper.list_all_maker_ids()
    mm_sub_user_ids = SubAccount.query.filter(
        SubAccount.main_user_id.in_(market_maker_ids)).with_entities(SubAccount.user_id).all()
    mm_sub_user_ids = [item.user_id for item in mm_sub_user_ids]
    market_maker_ids.extend(mm_sub_user_ids)
    user_ids = user_ids - set(market_maker_ids)

    position_set_map = defaultdict(set)
    position_user_map = dict()
    position_time_map = defaultdict(float)
    position_leverage_map = dict()
    position_time_range_list = sorted([(item, item.value) for item in report_model.TimeRange],
                                      key=lambda x: x[1])
    current_app.logger.info('getting history positions.')
    records = get_history_positions(user_ids, start_date, end_date)
    current_app.logger.info('get history positions done.')
    excluded_user_ids = set(market_maker_ids)
    markets_data = PerpetualMarketCache().read_aside()
    offline_markets_data = PerpetualOfflineMarketCache().read_aside()
    markets_data.update(offline_markets_data)

    price_map = AssetPrice.get_close_price_map(start_date)
    position_deal_amount_map = defaultdict(Decimal)
    for record in records:
        user_id, position_id = record['user_id'], record['position_id']
        delta_ts = record['update_time'] - record['create_time']
        time_range = None
        for range_, value in position_time_range_list:
            if delta_ts <= value:
                time_range = range_
                break
        position_set_map[time_range].add(position_id)
        position_leverage_map[position_id] = record['leverage']
        position_user_map[position_id] = user_id
        position_time_map[position_id] = delta_ts

        info = markets_data.get(record['market'])
        if not info:
            continue
        if info['type'] == PerpetualMarketType.DIRECT:
            deal_amount = Decimal(record['deal_all']) * price_map.get(info['money'], 0)
        else:
            deal_amount = Decimal(record['deal_all'])  # USD
        position_deal_amount_map[position_id] = deal_amount

    deal_users = {item['user_id'] for item in records}
    
    current_app.logger.info('processing deal data.')
    group_position_deal_amount_map = defaultdict(Decimal)
    for group, position_ids in position_set_map.items():
        for position_id in position_ids:
            group_position_deal_amount_map[group] += position_deal_amount_map[position_id]

    # 仓位维度的数据
    total_trade_user_count = len(deal_users)
    current_app.logger.info('getting final data.')
    for item in report_model.TimeRange:
        position_ids = position_set_map[item]
        user_set = set()

        total_time = total_leverage = 0
        for position_id in position_ids:
            user_set.add(position_user_map[position_id])
            total_time += position_time_map[position_id]
            total_leverage += position_leverage_map[position_id]

        bm = BitMap()
        bm.update(excluded_user_ids & user_set)
        position_count = len(position_set_map[item])
        trade_amount = group_position_deal_amount_map[item]
        position_user_count = len(user_set)

        new_record = report_model(report_date=start_date, time_range=item)
        new_record.total_trade_user_count = total_trade_user_count
        new_record.position_count = position_count
        new_record.position_user_count = position_user_count
        new_record.trade_amount = quantize_amount(trade_amount, 2)
        new_record.average_position_time = quantize_amount(total_time / position_count if position_count else 0, 2)
        new_record.average_leverage = quantize_amount(total_leverage / position_count if position_user_count else 0, 2)
        new_record.excluded_trade_user_bitmap = bm.serialize()
        bm = BitMap()
        bm.update(list(user_set))
        new_record.close_position_user_bitmap = bm.serialize()
        db.session.add(new_record)

    db.session.commit()
    current_app.logger.info('everything done.')


def get_perpetual_user_statistics(start_date, end_date, time_range):    
    current_app.logger.info(f'starting to get user data: {start_date} - {end_date}')
    user_ids = User.query.filter(
        User.user_type.in_([
            User.UserType.NORMAL,
            User.UserType.SUB_ACCOUNT,
            User.UserType.EXTERNAL,
        ])
    ).with_entities(User.id).all()

    user_ids = {item.id for item in user_ids}
    market_maker_ids = MarketMakerHelper.list_all_maker_ids()
    mm_sub_user_ids = SubAccount.query.filter(
        SubAccount.main_user_id.in_(market_maker_ids)).with_entities(SubAccount.user_id).all()
    mm_sub_user_ids = [item.user_id for item in mm_sub_user_ids]
    market_maker_ids.extend(mm_sub_user_ids)
    user_ids -= set(market_maker_ids)
    user_set_map = defaultdict(set)
    user_position_list_map = defaultdict(list)
    user_position_time_list_map = defaultdict(list)
    user_leverage_list_map = defaultdict(list)
    position_time_range_list = sorted([(item, item.value) for item in time_range],
                                      key=lambda x: x[1])
    current_app.logger.info('getting history positions.')
    records = get_history_positions(user_ids, start_date, end_date)
    current_app.logger.info('get history position done.')
    user_ids = {item['user_id'] for item in records}
    current_app.logger.info('getting user balances.')
    user_balance_map = get_user_balances(user_ids, end_date)
    current_app.logger.info('get user balances done.')
    markets_data = PerpetualMarketCache().read_aside()
    offline_markets_data = PerpetualOfflineMarketCache().read_aside()
    markets_data.update(offline_markets_data)

    price_map = AssetPrice.get_close_price_map(start_date)
    user_deal_map = defaultdict(Decimal)
    for record in records:
        user_id, position_id = record['user_id'], record['position_id']
        delta_ts = record['update_time'] - record['create_time']
        user_position_time_list_map[user_id].append(delta_ts)
        user_position_list_map[user_id].append(position_id)
        user_leverage_list_map[user_id].append(record['leverage'])

        info = markets_data.get(record['market'])
        if not info:
            continue
        if info['type'] == PerpetualMarketType.DIRECT:
            deal_amount = Decimal(record['deal_all']) * price_map.get(info['money'], 0)
        else:
            deal_amount = Decimal(record['deal_all']) # USD
        user_deal_map[record['user_id']] += deal_amount

    user_position_average_time_map = defaultdict(float)
    for user_id, times in user_position_time_list_map.items():
        avg_time = sum(times) / len(times)
        user_position_average_time_map[user_id] = avg_time

    for user_id, avg_ts in user_position_average_time_map.items():
        user_group = None
        for group_, t in position_time_range_list:
            if avg_ts <= t:
                user_group = group_
                break
        user_set_map[user_group].add(user_id)

    group_deal_map = defaultdict(Decimal)
    for group, user_ids in user_set_map.items():
        for user_id in user_ids:
            group_deal_map[group] += user_deal_map.get(user_id, Decimal())

    #### 用户维度的数据
    result = []
    total_user_count = sum(len(v) for v in user_set_map.values())
    total_amount = sum(group_deal_map.values())
    total_position_count = sum(len(v) for v in user_position_list_map.values())
    total_user_balance = sum(user_balance_map.values())
    for item in time_range:
        user_count = len(user_set_map[item])
        trade_amount = group_deal_map[item]
        position_count, user_balance = 0, Decimal()
        total_position_time = total_leverage = 0
        for user_id in user_set_map[item]:
            position_count += len(user_position_list_map.get(user_id, [])) 
            user_balance += user_balance_map.get(user_id, Decimal())
            total_position_time += sum(user_position_time_list_map[user_id]) / len(user_position_time_list_map[user_id])
            total_leverage += sum(user_leverage_list_map[user_id]) / len(user_leverage_list_map[user_id])
        result.append(
            dict(
                duration=item.name,
                user_count=user_count,
                user_percent=format_percent(user_count / total_user_count, 2) if total_user_count > 0 else 0,
                trade_amount=amount_to_str(trade_amount, 2),
                trade_amount_percent=format_percent(trade_amount / total_amount, 2) if total_amount > 0 else 0,
                user_balance=amount_to_str(user_balance, 2),
                user_balance_percent=format_percent(user_balance / total_user_balance if total_user_balance > 0 else 0, 2),
                position_count=position_count,
                position_count_percent=format_percent(position_count / total_position_count if total_position_count > 0 else 0, 2),
                average_position_count=amount_to_str(position_count / user_count if user_count > 0 else 0, 2),
                average_position_time=total_position_time / user_count if user_count > 0 else 0,
                average_leverage=amount_to_str(total_leverage / user_count if user_count > 0 else 0, 2),
                average_trade_amount=amount_to_str(trade_amount / user_count if user_count > 0 else 0, 2),
            )
        )
    all_total_position_time = sum(sum(v) / len(v) for v in user_position_time_list_map.values())
    all_total_leverage = sum(sum(v) / len(v) for v in user_leverage_list_map.values())
    result.append(
        dict(
            duration='合计',
            user_count=total_user_count,
            user_percent='100%',
            trade_amount=amount_to_str(total_amount, 2),
            trade_amount_percent='100%',
            user_balance=amount_to_str(total_user_balance, 2),
            user_balance_percent='100%',
            position_count=total_position_count,
            position_count_percent='100%',
            average_position_count=amount_to_str(total_position_count / total_user_count if total_user_count > 0 else 0, 2),
            average_position_time=all_total_position_time / total_user_count if total_user_count > 0 else 0,
            average_leverage=amount_to_str(all_total_leverage / total_user_count if total_user_count > 0 else 0, 2),
            average_trade_amount=amount_to_str(total_amount / total_user_count if total_user_count > 0 else 0, 2),
        )
    )
    current_app.logger.info('everything done.')
    return result
    

@scheduled(crontab(minute="0", hour='1-5'))
@lock_call(ttl=3600*2)
def update_daily_perpetual_position_report_schedule():
    today = datetime.datetime.utcnow().date()
    last_record = DailyPerpetualPositionReport.query.order_by(
        DailyPerpetualPositionReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today + datetime.timedelta(days=-1)
    while start_date < today:
        update_perpetual_position_report(start_date, start_date + datetime.timedelta(days=1), DailyPerpetualPositionReport)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute=0, hour='1-5', day_of_month=1))
@lock_call()
def update_monthly_perpetual_position_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    last_record = MonthlyPerpetualPositionReport.query.order_by(
        MonthlyPerpetualPositionReport.report_date.desc()
    ).first()
    if last_record:
        start_month = next_month(last_record.report_date.year, last_record.report_date.month)
    else:
        start_month = last_month(cur_month.year, cur_month.month)
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_perpetual_position_report(start_month, end_month, MonthlyPerpetualPositionReport)
        start_month = end_month


@scheduled(crontab(minute="0", hour='1-5'))
@lock_call(ttl=3600*3)
def update_perpetual_user_analysis_schedule():
    now_ = now()
    ts = now_.timestamp() - now_.timestamp() % 3600
    history = PerpetualUserAnalysisCache().read()
    if history:
        data = json.loads(history)
        previous_updated_at = timestamp_to_date(data['updated_at'])
        if previous_updated_at == now_.date():
            return
    end_date = now_.date()
    start_date = end_date - datetime.timedelta(days=30)
    result = get_perpetual_user_statistics(start_date, end_date, DailyPerpetualPositionReport.TimeRange)

    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    data = dict(items=result, 
                updated_at=ts,
                period=f'{start_date_str} - {end_date_str}')
    PerpetualUserAnalysisCache().set(json.dumps(data))