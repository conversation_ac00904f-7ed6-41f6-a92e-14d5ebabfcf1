# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import date, timedelta
from functools import partial
from typing import Dict, List

from celery.schedules import crontab
from dateutil.relativedelta import relativedelta
from flask import current_app
from pyroaring import BitMap
from sqlalchemy import or_, func

from app.business.clients.biz_monitor import biz_monitor
from app.business.clients.server import SPOT_ACCOUNT_ID, PerpetualServerClient, ServerClient
from app.business.investment import BalanceTransferOperation
from app.business.margin.helper import MarginUserAccountInfo
from app.business.margin.transfer import MarginTransferOperation
from app.business.perpetual.balance import perpetual_transfer_out
from app.business.sub_account import SubAccountManager
from app.business.user import process_user_api_permission, filter_active_users, check_and_update_user_special_location, \
    UserRepository, UserSettings
from app.business.utils import yield_query_records_by_time_range
from app.common.constants import BalanceBusiness, Language
from app.common.countries import list_country_codes_3_admin
from app.config import config
from app.models.exchange import AssetExchangeSysUser
from app.models.investment import InvestmentAccount
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from app.models.user import ClearedUser, SubAccount, ApiAuth, UserLoginState
from app.models.user import SignOffUserBalanceTransferHistory, UserPreference, SubAccountAssetTransfer
from app.models.base import read_only_session
from app.utils.push import MobilePusher
from ..api.common.request import RequestPlatform
from ..business import UserPreferences
from ..business import lock_call, ExchangeLogDB
from ..business.email import send_api_expiration_extend_email, EmailSender
from ..business.operation import AutoPushHelper
from ..business.p2p.user import P2pUserManger
from ..business.risk_control.base import get_user_setting_risk_query
from ..business.summary import get_period_perp_trade_users, get_period_spot_trade_users
from ..caches import UserActivenessCache, MarketCache, PerpetualMarketCache
from ..caches.auth import UserLoginTokenDeleteCache
from ..caches.operation import event_cache_mapping, event_user_cache_mapping
from ..caches.user import (
    SubMainUserCache,
    UserVisitPermissionCache, SubAccountBalanceCache, UserConfigKeyCache
)
from ..caches.user import UserOnlineCache, ApiExpirationCache, \
    AbnormalUserCache, UserStateUpdateCache, InnerMakersCache
from ..common import CeleryQueues, AccountEvent, EventTag, ProductEvent
from ..models import (
    UserActivenessHistory, db, User,
    UserTradeSummary, UserBusinessRecord, Deposit,
    UserSetting, RiskUserSource, RiskUser, SignOffUser, OperationLog,
    EmailPush,
)
from ..utils import (
    scheduled, route_module_to_celery_queue, today,
    now, today_datetime, batch_iter, ConfigMode, celery_task
)
from ..utils.date_ import date_to_datetime, convert_datetime, current_timestamp

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


@scheduled(crontab(minute=0, hour='0-4'))
@lock_call()
def save_active_users():
    yesterday = date.today() - timedelta(days=1)
    cache = UserActivenessCache(yesterday)
    users = cache.get_users()
    if not users:
        cache.delete()
        return

    set_active = partial(UserActivenessHistory.set_user_active,
                         date_=yesterday, commit=False)
    for user_id in users:
        set_active(user_id)
    db.session.commit()
    cache.delete()


@scheduled(crontab(minute='5,10,15,20', hour='0-1'))
@lock_call(ttl=3600*2, wait=False)
def sync_user_balances():
    today = int(today_datetime().timestamp())
    ExchangeLogDB.sync_user_balances(today)
    ExchangeLogDB.sync_user_slice_balances(today)


@scheduled(crontab(minute='5,10,15,20', hour='0-1'))
@lock_call(ttl=3600*2, wait=False)
def sync_user_perpetual_balances():
    today = int(today_datetime().timestamp())
    ExchangeLogDB.sync_user_slice_perpetual_balances(today)


@scheduled(crontab(minute='5,10,15,20', hour='0-1'))
@lock_call(ttl=3600*2, wait=False)
def sync_user_account_balances():
    today = int(today_datetime().timestamp())
    current_app.logger.info("sync_user_account_balances started")
    ExchangeLogDB.sync_user_account_balances(today)
    current_app.logger.info("sync_user_account_balances finished")
    # 依赖 sync_user_account_balances
    ExchangeLogDB.sync_user_account_balance_sum(today)
    ExchangeLogDB.sync_user_top_balance_rank(today)


@scheduled(crontab(minute='5,10,15,20', hour='0-1'))
@lock_call(ttl=3600*2, wait=False)
def sync_web_user_balances():
    today = int(today_datetime().timestamp())
    ExchangeLogDB.sync_user_margin_balances(today)
    ExchangeLogDB.sync_user_amm_balances(today)
    ExchangeLogDB.sync_user_pledge_balances(today)
    ExchangeLogDB.sync_user_pre_trading_balances(today)
    ExchangeLogDB.sync_user_account_type_balance_sum_table(today)
    ExchangeLogDB.sync_user_slice_account_balance_sum(today)


@scheduled(crontab(hour=1, minute="*/20"))
@lock_call(wait=False)
def update_has_trade_user_schedule():
    def _update(system: UserTradeSummary.System):

        if system == UserTradeSummary.System.SPOT:
            business = UserBusinessRecord.Business.SPOT_TRADE
            trade_user_ids = get_period_spot_trade_users(curr_date, curr_date + timedelta(days=1))
        else:
            trade_user_ids = get_period_perp_trade_users(curr_date, curr_date + timedelta(days=1))
            business = UserBusinessRecord.Business.PERPETUAL_TRADE
        user_ids = list(trade_user_ids)
        bm = BitMap()
        bm.update(user_ids)
        history_record = UserBusinessRecord.query.filter(
            UserBusinessRecord.business == business
        ).order_by(UserBusinessRecord.report_at.desc()).first()
        if history_record:
            history_bit_map: BitMap = BitMap.deserialize(
                history_record.history_user_bit_map)
        else:
            history_bit_map = BitMap()
        history_bit_map.update(user_ids)
        record = UserBusinessRecord(business=business,
                                    report_at=date_to_datetime(curr_date))
        record.new_user_bit_map = bm.serialize()
        record.history_user_bit_map = history_bit_map.serialize()
        db.session.add(record)

    last_record = UserBusinessRecord.query.filter(
        UserBusinessRecord.business == UserBusinessRecord.Business.SPOT_TRADE
    ).order_by(UserBusinessRecord.report_at.desc()).first()

    if last_record and last_record.report_at.date() + timedelta(days=1) == today():
        return
    today_ = today()
    if last_record:
        curr_date = last_record.report_at.date() + timedelta(days=1)
    else:
        curr_date = today_ - timedelta(days=1)
    while curr_date < today_:
        for system in UserTradeSummary.System:
            _update(system)
        db.session.commit()
        curr_date += timedelta(days=1)


@scheduled(crontab(hour=1, minute="*/20"))
@lock_call(wait=False)
def update_has_deposit_user_schedule():
    last_record = UserBusinessRecord.query.filter(
        UserBusinessRecord.business == UserBusinessRecord.Business.DEPOSIT
    ).order_by(UserBusinessRecord.report_at.desc()).first()
    if last_record and last_record.report_at.date() + timedelta(days=1) == today():
        return
    today_ = today()
    if last_record:
        curr_date = last_record.report_at.date() + timedelta(days=1)
    else:
        curr_date = today_ - timedelta(days=1)
    
    while curr_date < today_:
        query = yield_query_records_by_time_range(Deposit,
                                                  start_time=date_to_datetime(curr_date),
                                                  end_time=date_to_datetime(curr_date + timedelta(days=1)),
                                                  select_fields=[Deposit.user_id, Deposit.status])
        user_ids = set()
        for row in query:
            if row.status not in [Deposit.Status.CONFIRMING, Deposit.Status.FINISHED, Deposit.Status.TO_HOT]:
                continue
            user_ids.add(row.user_id)

        bm = BitMap()
        bm.update(user_ids)
        history_record = UserBusinessRecord.query.filter(
            UserBusinessRecord.business == UserBusinessRecord.Business.DEPOSIT
        ).order_by(UserBusinessRecord.report_at.desc()).first()
        if history_record:
            history_bit_map: BitMap = BitMap.deserialize(history_record.history_user_bit_map)
        else:
            history_bit_map = BitMap()
        history_bit_map.update(user_ids)
        record = UserBusinessRecord(business=UserBusinessRecord.Business.DEPOSIT,
                                    report_at=date_to_datetime(curr_date))
        record.new_user_bit_map = bm.serialize()
        record.history_user_bit_map = history_bit_map.serialize()
        db.session_add_and_commit(record)
        curr_date += timedelta(days=1)


@scheduled(crontab(hour="*/1", minute=10))
@lock_call()
def refresh_user_visit_permission_cache():
    UserVisitPermissionCache.reload_all()


@scheduled(crontab(hour='*/1', minute=34))
def check_api_key_expiration():
    """APIKEY到期前72、48、24小时发送邮件提醒"""
    now_ = now()
    max_delta = max(ApiAuth.SEND_EMAIL_DELTA_HOURS)
    records = ApiAuth.query.filter(ApiAuth.status == ApiAuth.Status.VALID,
                                   ApiAuth.expired_at > now_,
                                   ApiAuth.expired_at <= now_ + timedelta(hours=max_delta)
                                   ).all()
    for record in records:
        api_auth_id = record.id
        for delta_ in ApiAuth.SEND_EMAIL_DELTA_HOURS:
            if record.expired_at <= now_ + timedelta(hours=delta_):
                _check_and_send_email(api_auth_id, delta_)
                break


def _check_and_send_email(api_auth_id, delta_):
    cache = ApiExpirationCache(api_auth_id, delta_)
    if cache.exists():
        return
    cache.gen()
    send_api_expiration_extend_email.delay(api_auth_id)


@scheduled(crontab(minute="*/1"))
def report_online_user_event():
    """上报在线人数"""
    current_datetime = convert_datetime(now(), date_field="minute")
    online_timestamps = [int((current_datetime - timedelta(minutes=i)).timestamp()) for i in range(1, 6)]
    tag_keys_mapper = defaultdict(list)
    for timestamp in online_timestamps:
        for platform in RequestPlatform:
            evnet_tag = biz_monitor.platform_to_event_tag(platform)
            cache = UserOnlineCache(platform.name, timestamp)
            tag_keys_mapper[evnet_tag].append(cache.key)

    for tag, keys in tag_keys_mapper.items():
        counter = UserOnlineCache.redis.pfcount(*keys)
        if not counter:
            continue
        biz_monitor.increase_guage(
            AccountEvent.ONLINE_NUM,
            value=counter,
            tag=tag,
        )


@scheduled(crontab(minute='*/1'), queue=CeleryQueues.REAL_TIME)
@lock_call()
def report_notification_available_device_event():
    """上报开启各种push权限的设备数"""
    for cache_class in event_cache_mapping.values():
        for tag in EventTag:
            cache = cache_class(tag.name)
            count = cache.get_count() or 0
            biz_monitor.increase_guage(
                cache.event,
                value=count,
                tag=tag
            )


@scheduled(crontab(minute='*/1'), queue=CeleryQueues.REAL_TIME)
@lock_call()
def report_notification_available_user_event():
    """上报开启各种push权限的用户数"""
    for cache_class in event_user_cache_mapping.values():
        for tag in EventTag:
            cache = cache_class(tag.name)
            if not cache.exists():
                continue
            count = cache.get_count() or 0
            biz_monitor.increase_guage(
                cache.event,
                value=count,
                tag=tag
            )



@scheduled(crontab(day_of_week=0, hour='*/2', minute=45))
@lock_call()
def transfer_sign_off_users_asset_schedule():
    """
    转移注销用户资产到Admin
    """
    sign_off_users = SignOffUser.query.filter(
        SignOffUser.has_asset_cleared_off.is_(False)
    ).all()
    record_map = {item.user_id: item for item in sign_off_users}
    main_user_ids = [user.user_id for user in sign_off_users]
    sub_user_map = dict()
    for ids in batch_iter(main_user_ids, 1000):
        tmp = SubAccount.query.filter(SubAccount.main_user_id.in_(ids)
        ).with_entities(SubAccount.user_id,
                        SubAccount.main_user_id).all()
        sub_user_map.update(dict(tmp))
    sub_user_ids = list(sub_user_map.keys())
    all_user_ids = sub_user_ids + main_user_ids
    client, perpetual_client = ServerClient(), PerpetualServerClient()

    skip_user_ids = set()
    # 杠杆/理财/合约划转
    for user_id in all_user_ids:
        balance_map = client.get_user_accounts_balances(user_id)
        margin_account_ids = set(MarginUserAccountInfo(user_id).all_account_ids)
        # 理财和杠杆账户资产
        for account_id, asset_map in balance_map.items():
            account_id = int(account_id)
            for asset, balance in asset_map.items():
                available = balance['available']
                if available <= 0:
                    continue
                if account_id in margin_account_ids:
                    operation = MarginTransferOperation(
                        user_id=user_id,
                        transfer_from=account_id,
                        transfer_to=SPOT_ACCOUNT_ID,
                        asset=asset,
                        amount=available)
                    operation.transfer()
                elif account_id == InvestmentAccount.ACCOUNT_ID:
                    operation = BalanceTransferOperation(
                        user=User.query.get(user_id),
                        transfer_from=account_id,
                        transfer_to=SPOT_ACCOUNT_ID,
                        asset=asset,
                        amount=available)
                    operation.transfer()
                    skip_user_ids.add(sub_user_map.get(user_id, user_id))
        
        perpetual_balance_map = perpetual_client.get_user_balances(user_id)
        for asset, balance in perpetual_balance_map.items():
            available = balance['available']
            if available <= 0:
                continue
            perpetual_transfer_out(user_id, asset, available)

    # 子账户划转
    for user_id in sub_user_ids:
        _main_user_id = sub_user_map[user_id]
        balances = client.get_user_balances(user_id)
        for asset, balance in balances.items():
            amount = balance['available']
            if amount <= 0:
                continue
            SubAccountManager.transfer_asset(
                main_user_id=_main_user_id,
                source_user_id=user_id,
                source_account_type=SubAccountAssetTransfer.AccountType.SPOT,
                target_user_id=_main_user_id,
                target_account_type=SubAccountAssetTransfer.AccountType.SPOT,
                asset=asset,
                amount=amount,
            )

    # 主账户资产扣减
    for user_id in main_user_ids:
        if user_id in skip_user_ids:
            continue
        balances = client.get_user_balances(user_id)
        for asset, balance in balances.items():
            transfer_amount = balance['available']
            if transfer_amount <= 0:
                continue

            record = SignOffUserBalanceTransferHistory.get_or_create(
                user_id=user_id,
                asset=asset,
            )
            record.amount = transfer_amount
            db.session_add_and_commit(record)
            if record.status == SignOffUserBalanceTransferHistory.Status.CREATED:
                deduct_result = client.asset_query_business(
                    user_id=user_id,
                    asset=record.asset,
                    business=BalanceBusiness.SIGNED_OFF_USER_TO_ADMIN_TRANSFER,
                    business_id=record.id,
                )
                if deduct_result:
                    record.status = SignOffUserBalanceTransferHistory.Status.DEDUCTED
                else:
                    remark = f"transfer_sign_off_users_asset_task {record.asset} {record.id}"
                    client.add_user_balance(
                        user_id=user_id,
                        asset=record.asset,
                        amount=-record.amount,
                        business=BalanceBusiness.SIGNED_OFF_USER_TO_ADMIN_TRANSFER,
                        business_id=record.id,
                        detail={"remark": remark},
                    )
                    record.status = SignOffUserBalanceTransferHistory.Status.DEDUCTED
                db.session.commit()
            if record.status == SignOffUserBalanceTransferHistory.Status.DEDUCTED:
                # 扣减资产后，作为收入处理
                record.status = SignOffUserBalanceTransferHistory.Status.FINISHED
                record.finished_at = now()
                db.session.commit()

        record_map[user_id].has_asset_cleared_off = True
        db.session.commit()


@scheduled(crontab(hour='2,3', minute=45))
@lock_call()
def sign_off_user_device_log_out_schedule():
    """
    注销账户设备登出定时任务
    """
    sign_off_users = SignOffUser.query.filter(
        SignOffUser.has_device_logged_out.is_(False),
    ).all()
    for item in sign_off_users:
        # noinspection PyBroadException
        try:
            MobilePusher().delete_device_report(item.user_id)
            item.has_device_logged_out = True
        except Exception:
            current_app.logger.error(f'delete_device_report failed user_id:{item.user_id}')
    db.session.commit()


@scheduled(crontab(minute="*/5"), queue=CeleryQueues.REAL_TIME)
@lock_call()
def report_user_pref_open_count():
    # 上报各种个人设置开启总人数
    event, pref = ProductEvent, UserPreferences
    pref_event_map = {
        pref.opening_account_profit_loss.name: event.PROFIT_LOSS_COUNT,
        pref.opening_withdraw_password.name: event.WITHDRAWAL_PASSWORD_ENABLE_COUNT
    }

    # 每分钟一次的统计逻辑，在不改动表结构设计的前提下，暂时只能使用读库来降低性能消耗
    with read_only_session() as ro_session:
        model = UserPreference
        rows = ro_session.query(model).filter(
            model.key.in_(list(pref_event_map.keys())),
            model.status == model.Status.VALID,
            model.value == "1",
        ).group_by(
            model.key
        ).with_entities(
            model.key,
            func.count("*").label("count")
        ).all()

        for row in rows:
            event = pref_event_map[row.key]
            biz_monitor.increase_guage(
                event,
                value=row.count,
            )


@scheduled(crontab(minute=10, hour="2,12"))
@lock_call()
def update_risk_user_source_schedule():
    """ 更新风险用户来源 """
    now_ = now()
    updated_start_at = now_ - timedelta(days=2)
    query = get_user_setting_risk_query()
    query = query.filter(
        UserSetting.updated_at >= updated_start_at,  # 最近N天更新的
    )
    rows = query.with_entities(UserSetting.user_id.distinct().label('user_id')).all()
    user_ids = [i.user_id for i in rows]
    for ch_user_ids in batch_iter(user_ids, 5000):
        user_risk_source_map = get_user_risk_source_map(ch_user_ids)
        for user_id, source in user_risk_source_map.items():
            if source == RiskUserSource.Source.RISK_CONTROL_FORBID:
                # 忽略变动频繁的风控
                continue
            row = RiskUserSource.get_or_create(user_id=user_id)
            
            if row.source == RiskUserSource.Source.RISK_CONTROL_BATCH_FORBID:
                continue
            row.source = source
            db.session.add(row)
        db.session.commit()

    # 更新已失效的
    start_id = 0
    limit = 5000
    while 1:
        ch_rows = RiskUserSource.query.filter(
            RiskUserSource.id > start_id,
            RiskUserSource.status == RiskUserSource.Status.VALID,
        ).order_by(RiskUserSource.id.asc()).limit(limit).all()
        if not ch_rows:
            break

        ch_user_ids = [i.user_id for i in ch_rows]
        query = get_user_setting_risk_query()
        _risk_user_ids = query.filter(
            UserSetting.user_id.in_(ch_user_ids),
        ).with_entities(UserSetting.user_id.distinct().label('user_id')).all()
        _risk_user_ids = {i.user_id for i in _risk_user_ids}
        _no_risk_user_ids = set(ch_user_ids) - _risk_user_ids
        if _no_risk_user_ids:
            user_row_map = {i.user_id: i for i in ch_rows}
            for uid in _no_risk_user_ids:
                row = user_row_map[uid]
                row.status = RiskUserSource.Status.DELETED
        db.session.commit()
        start_id = ch_rows[-1].id


def get_user_risk_source_map(user_ids: List[int]) -> Dict:
    """ 获取用户冻结来源 """
    from app.api.common.request import RequestPlatform

    user_source_map = {}
    remain_user_ids = set(user_ids)
    clear_rows = ClearedUser.query.filter(
        ClearedUser.user_id.in_(remain_user_ids),
        ClearedUser.valid.is_(True),
    ).with_entities(ClearedUser.user_id).all()
    # 大陆清退：归类为研发手动冻结
    user_source_map.update({i.user_id: RiskUserSource.Source.MANUAL_BATCH_FORBID for i in clear_rows})

    remain_user_ids = remain_user_ids - set(user_source_map)
    if not remain_user_ids:
        return user_source_map
    sign_off_rows = SignOffUser.query.filter(
        SignOffUser.user_id.in_(remain_user_ids),
    ).with_entities(SignOffUser.user_id).all()
    user_source_map.update({i.user_id: RiskUserSource.Source.USER_SIGN_OFF for i in sign_off_rows})

    remain_user_ids = remain_user_ids - set(user_source_map)
    if not remain_user_ids:
        return user_source_map
    risk_rows = RiskUser.query.filter(
        RiskUser.user_id.in_(remain_user_ids),
        RiskUser.status.in_((RiskUser.Status.AUDIT_REQUIRED, RiskUser.Status.AUDIT_REJECTED))
    ).with_entities(RiskUser.user_id.distinct().label('user_id')).all()
    user_source_map.update({i.user_id: RiskUserSource.Source.RISK_CONTROL_FORBID for i in risk_rows})

    remain_user_ids = remain_user_ids - set(user_source_map)
    if not remain_user_ids:
        return user_source_map
    now_ = now()
    setting_rows = UserSetting.query.filter(
        UserSetting.user_id.in_(remain_user_ids),
        or_(UserSetting.valid_till.is_(None), UserSetting.valid_till > now_),
        UserSetting.key.in_([
            UserSettings.withdrawals_disabled_after_withdraw_password_editing.name,
            UserSettings.withdrawals_disabled_after_security_editing.name
        ]),
        UserSetting.value == UserSettings.withdrawals_disabled_after_security_editing.db_value(True),
    ).with_entities(UserSetting.user_id.distinct().label('user_id')).all()
    user_source_map.update({i.user_id: RiskUserSource.Source.RESET_SECURITY for i in setting_rows})

    remain_user_ids = remain_user_ids - set(user_source_map)
    if not remain_user_ids:
        return user_source_map
    op_rows = OperationLog.query.filter(
        OperationLog.user_id.in_(remain_user_ids),
        OperationLog.operation.in_([OperationLog.Operation.FORBID_ACCOUNT.name, OperationLog.Operation.SIGN_OFF.name]),
    ).with_entities(
        OperationLog.user_id, OperationLog.operation, OperationLog.platform
    ).order_by(OperationLog.id.desc()).all()
    platforms = {i.value for i in RequestPlatform}
    for r in op_rows:
        if r.user_id in user_source_map:
            continue
        if r.platform not in platforms:
            user_source_map[r.user_id] = RiskUserSource.Source.MANUAL_BATCH_FORBID
        elif r.operation == OperationLog.Operation.SIGN_OFF.name:
            user_source_map[r.user_id] = RiskUserSource.Source.USER_SIGN_OFF
        else:
            if UserPreferences(r.user_id).self_forbid:
                user_source_map[r.user_id] = RiskUserSource.Source.USER_FREEZE

    remain_user_ids = remain_user_ids - set(user_source_map)
    if not remain_user_ids:
        return user_source_map
    admin_op_ids = AdminOperationLog.query.filter(
        AdminOperationLog.target_user_id.in_(remain_user_ids),
        AdminOperationLog.namespace == OPNamespaceObjectUser.Setting.namespace.name,
        AdminOperationLog.object == OPNamespaceObjectUser.Setting.object.name,
        AdminOperationLog.operation == AdminOperationLog.Operation.EDIT,
    ).with_entities(
        AdminOperationLog.target_user_id
    ).distinct().all()
    admin_op_ids = [x.target_user_id for x in admin_op_ids]
    # 有操作记录的归类于客服admin冻结
    user_source_map.update({i: RiskUserSource.Source.ADMIN_FORBID for i in admin_op_ids})

    # 其他无法归类的数据：都归类于研发手动冻结
    remain_user_ids = remain_user_ids - set(user_source_map)
    user_source_map.update({i: RiskUserSource.Source.MANUAL_BATCH_FORBID for i in remain_user_ids})
    return user_source_map


@scheduled(crontab(hour='*/1', minute=0))
@lock_call()
def process_cleared_user_api_permissions_schedules():
    uids = ClearedUser.get_user_ids()
    q = SubAccount.query.with_entities(SubAccount.main_user_id, SubAccount.user_id).all()
    m = {v.user_id: v.main_user_id for v in q}
    total_user_ids = set()
    for uid in uids:
        total_user_ids.add(uid)
    for v in q:
        if v.main_user_id in total_user_ids:
            total_user_ids.add(v.user_id)

    final_user_ids = set()
    for uids in batch_iter(total_user_ids, 5000):
        q = ApiAuth.query.filter(
            ApiAuth.user_id.in_(uids),
            ApiAuth.status == ApiAuth.Status.VALID,
            or_(
                ApiAuth.withdrawals_enabled.is_(True),
                ApiAuth.trading_enabled.is_(True)
            )
        ).with_entities(ApiAuth.user_id).all()
        for v in q:
            _uid = m.get(v.user_id, v.user_id)
            final_user_ids.add(_uid)
    current_app.logger.info(f"process user count: {len(final_user_ids)}")
    for uid in sorted(list(final_user_ids)):
        process_user_api_permission(uid)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_coupon_exclude_user_cache_schedule():
    AbnormalUserCache.reload()


@scheduled(crontab(hour="*/1", minute="15"))
@lock_call()
def update_sub_account_balance_cache_schedule():
    """ 定时更新子帐号市值缓存 """
    subs = SubAccount.query.filter(
        SubAccount.is_visible.is_(True),
    ).with_entities(
        SubAccount.main_user_id.distinct().label('main_user_id'),
    ).all()
    ignore_main_ids = {config["AMM_DERIVE_USER_ID"], AssetExchangeSysUser.get_derive_user_id()}  # 忽略AMM、兑换等主帐号
    main_user_ids = {i.main_user_id for i in subs if i.main_user_id not in ignore_main_ids}
    cur_ts = current_timestamp(to_int=True)
    for mid in main_user_ids:
        _cache = SubAccountBalanceCache(mid)
        last_ts = _cache.get_ts()
        if cur_ts - last_ts > 3600 * 8:
            try:
                SubAccountBalanceCache(mid).reload()
            except:  # noqa
                pass


# @scheduled(crontab(hour="1", minute="20"))
# @lock_call()
def send_top_search_basic_data_to_email():
    from app.business.kline import TrendingSearchExportWrapper
    from app.caches.kline import MarketTrendingSearchCache, AssetTrendingSearchCache
    from app.business.email import send_internal_user_email
    from app.utils.export import export_xlsx_with_sheet

    from app.business import BusinessSettings

    emails = BusinessSettings.top_search_emails
    if not emails:
        return

    cls = TrendingSearchExportWrapper
    types = [
        cls.SearchType.SPOT,
        cls.SearchType.PERPETUAL,
        cls.SearchType.ASSET,
    ]
    sheet_datas = {}
    for type_ in types:
        instance = cls(type_)
        if type_ is cls.SearchType.ASSET:
            cache = AssetTrendingSearchCache()
            keys = cache.get_top_assets()
        else:
            cache = MarketTrendingSearchCache(type_.value)
            keys = cache.lrange(0, instance.top_size)
        ret = instance.get_export_data(keys)
        sheet_datas.update({
            f'{type_.value}': {
                'header_mapper': ret['headers'],
                'data': ret['data']
            }
        })
    file_url = export_xlsx_with_sheet(
        sheet_data=sheet_datas
    )
    title = '热搜币种数据用于热搜算法优化分析'
    email_content = f'''{title}（请及时下载，避免超时失效）：
            {file_url}'''
    for email in emails:
        send_internal_user_email.delay(
            email=email,
            email_content=email_content,
            subject=f'{title}',
            lang=Language.ZH_HANS_CN.value
        )


@scheduled(crontab(hour="1", minute="30"))
@lock_call()
def send_n_anniversary_emails():
    assets = set()
    for market_name in MarketCache.list_online_markets():
        cache = MarketCache(market_name).dict
        assets |= {cache["quote_asset"], cache["base_asset"]}
    asset_count = len(assets)
    spot_market_count = len(MarketCache.list_online_markets())
    perpetual_market_count = len(PerpetualMarketCache().get_market_list())
    market_count = spot_market_count + perpetual_market_count
    country_count = len(list(list_country_codes_3_admin()))
    params = {
        'asset_count': asset_count,
        'market_count': market_count,
        'country_count': country_count
    }
    for i in range(1, 6):
        _send_n_anniversary_emails(i, params)


def _send_n_anniversary_emails(years, params):
    today_ = today()
    register_date = today_ - relativedelta(years=years)
    start = register_date
    end = register_date + timedelta(days=1)
    users = User.query.filter(
        User.created_at >= start,
        User.created_at < end,
        User.user_type != User.UserType.SUB_ACCOUNT,
    ).with_entities(
        User.id,
    ).all()
    user_ids = [row.id for row in users]
    site_url_map = {
        1: 'https://www.coinex.com/s/4K2E',
        2: 'https://www.coinex.com/s/4KG4',
        3: 'https://www.coinex.com/s/4KG8',
        4: 'https://www.coinex.com/s/4KGZ',
        5: 'https://www.coinex.com/s/4KGQ',
    }

    email_type_map = {
        1: 'anniversary_of_registration',
        2: 'second_anniversary_of_registration',
        3: 'third_anniversary_of_registration',
        4: 'fourth_anniversary_of_registration',
        5: 'fifth_anniversary_of_registration',
    }
    filled_params = {
        'years': years,
        'site_url': site_url_map[years],
        **params
    }
    email_type = email_type_map[years]
    title = EmailSender.EMAIL_SUBJECT_MAPPING['notice'][email_type]
    push_name = 'anniversary email auto push'
    AutoPushHelper.create_email_push(user_ids,
                                     title,
                                     'notice',
                                     email_type,
                                     filled_params,
                                     name=push_name,
                                     push_type=EmailPush.PushType.ACTIVITY,
                                     )


@scheduled(crontab(hour="1", minute="0"))
@lock_call()
def update_user_special_location():
    _yesterday = date.today() - timedelta(days=1)
    active_users = filter_active_users(_yesterday, _yesterday)

    for user_id in active_users:
        check_and_update_user_special_location(user_id)


@scheduled(crontab(minute="*/1"))
@lock_call()
def update_user_state():
    raw_data = UserStateUpdateCache().lrange_all()
    current_app.logger.info(f'loading user state:{len(raw_data)} from queue')

    pending_update_data = {}
    for item in raw_data:
        if "user_id" not in item:
            continue
        # remove dumplicate data
        pending_update_data[item["user_id"]] = item
    if not pending_update_data:
        current_app.logger.info(f'empty queue.')
        return

    for user_id, item in pending_update_data.items():
        # refresh token
        if token_dict := item.get("token_dict", {}):
            for token, expires_at in token_dict.items():
                UserLoginState.renew_to(token, expires_at)
        # update user preferences
        if pref_dict := item.get("pref_dict", {}):
            pref = UserPreferences(user_id, ConfigMode.REAL_TIME)
            for pref_name, value in pref_dict.items():
                if pref_name.endswith("language"):
                    setattr(pref, pref_name, Language(value))
                else:
                    setattr(pref, pref_name, value)

    current_app.logger.info(f'ltrim user state: {len(raw_data)}')
    UserStateUpdateCache().ltrim(len(raw_data), -1)


@scheduled(crontab(minute="*/1"))
@lock_call()
def offline_user_cache():
    raw_data = UserLoginTokenDeleteCache().lrange_all()
    current_app.logger.info(f'loading user pending remove token:{len(raw_data)} from queue')

    pending_delete_data = {}
    for item in raw_data:
        if "token" not in item or not item["token"]:
            continue
        # remove dumplicate data
        pending_delete_data[item["token"]] = item["token"]
    if not pending_delete_data:
        current_app.logger.info(f'empty queue.')
        return

    for login_token in pending_delete_data:
        login_state = UserLoginState.query.filter(
            UserLoginState.token == login_token,
        ).first()
        if not login_token:
            continue

        login_state.status = UserLoginState.Status.OFFLINE
        login_state.updated_at = now()
        db.session.commit()

    current_app.logger.info(f'ltrim user state: {len(raw_data)}')
    UserLoginTokenDeleteCache().ltrim(len(raw_data), -1)


@celery_task
@lock_call(with_args=True)
def reset_user_nickname_task(user_id: int):
    """重置用户昵称"""
    user = User.query.get(user_id)
    if not user:
        return
    nickname = user.nickname
    if UserRepository.check_user_is_had_block_word(user, nickname):
        nickname = P2pUserManger.generate_nickname()
    user.name = nickname
    db.session.commit()


@celery_task
@lock_call(with_args=True)
def reset_user_account_name_task(user_id: int):
    """重置用户账户名"""
    user: User = User.query.get(user_id)
    if not user:
        return
    user.set_default_account_name()


@scheduled(crontab(hour=22, minute=47))
@lock_call()
def update_inner_makers_cache_schedule():
    InnerMakersCache().reload()


@scheduled(crontab(minute='*/10'))
@lock_call()
def update_user_config_key_cache_schedule():
    UserConfigKeyCache.update_model_field_val_cache()


@scheduled(crontab(minute="*/10"))
@lock_call()
def update_sub_mian_user_cache():
    SubMainUserCache.reload()