# -*- coding: utf-8 -*-
from enum import Enum
from flask_babel import gettext as _

from types import MappingProxyType
from typing import NamedTuple, Mapping, Tuple, List, Union, Optional, Dict

# START
_COUNTRIES_DATA = (
     ('Afghanistan', _("阿富汗"), 'AF', 'AFG', 4, True),
     ('Åland Islands', _("奥兰"), 'AX', 'ALA', 248, False),
     ('Albania', _("阿尔巴尼亚"), 'AL', 'ALB', 8, True),
     ('Algeria', _("阿尔及利亚"), 'DZ', 'DZA', 12, True),
     ('American Samoa', _("美属萨摩亚"), 'AS', 'ASM', 16, False),
     ('Andorra', _("安道尔"), 'AD', 'AND', 20, True),
     ('Angola', _("安哥拉"), 'AO', 'AGO', 24, True),
     ('<PERSON><PERSON><PERSON>', _("安圭拉"), 'AI', 'AIA', 660, False),
     ('Antarctica', _("南极洲"), 'AQ', 'ATA', 10, False),
     ('Antigua and Barbuda', _("安提瓜和巴布达"), 'AG', 'ATG', 28, True),
     ('Argentina', _("阿根廷"), 'AR', 'ARG', 32, True),
     ('Armenia', _("亚美尼亚"), 'AM', 'ARM', 51, True),
     ('Aruba', _("阿鲁巴"), 'AW', 'ABW', 533, False),
     ('Australia', _("澳大利亚"), 'AU', 'AUS', 36, True),
     ('Austria', _("奥地利"), 'AT', 'AUT', 40, True),
     ('Azerbaijan', _("阿塞拜疆"), 'AZ', 'AZE', 31, True),
     ('Bahamas', _("巴哈马"), 'BS', 'BHS', 44, True),
     ('Bahrain', _("巴林"), 'BH', 'BHR', 48, True),
     ('Bangladesh', _("孟加拉国"), 'BD', 'BGD', 50, True),
     ('Barbados', _("巴巴多斯"), 'BB', 'BRB', 52, True),
     ('Belarus', _("白俄罗斯"), 'BY', 'BLR', 112, True),
     ('Belgium', _("比利时"), 'BE', 'BEL', 56, True),
     ('Belize', _("伯利兹"), 'BZ', 'BLZ', 84, True),
     ('Benin', _("贝宁"), 'BJ', 'BEN', 204, True),
     ('Bermuda', _("百慕大"), 'BM', 'BMU', 60, False),
     ('Bhutan', _("不丹"), 'BT', 'BTN', 64, True),
     ('Bolivia (Plurinational State of)', _("玻利维亚"), 'BO', 'BOL', 68, True),
     ('Bonaire, Sint Eustatius and Saba', _("荷兰加勒比区"), 'BQ', 'BES', 535, False),
     ('Bosnia and Herzegovina', _("波黑"), 'BA', 'BIH', 70, True),
     ('Botswana', _("博茨瓦纳"), 'BW', 'BWA', 72, True),
     ('Bouvet Island', _("布韦岛"), 'BV', 'BVT', 74, False),
     ('Brazil', _("巴西"), 'BR', 'BRA', 76, True),
     ('British Indian Ocean Territory', _("英属印度洋领地"), 'IO', 'IOT', 86, False),
     ('Brunei Darussalam', _("文莱"), 'BN', 'BRN', 96, True),
     ('Bulgaria', _("保加利亚"), 'BG', 'BGR', 100, True),
     ('Burkina Faso', _("布基纳法索"), 'BF', 'BFA', 854, True),
     ('Burundi', _("布隆迪"), 'BI', 'BDI', 108, True),
     ('Cabo Verde', _("佛得角"), 'CV', 'CPV', 132, True),
     ('Cambodia', _("柬埔寨"), 'KH', 'KHM', 116, True),
     ('Cameroon', _("喀麦隆"), 'CM', 'CMR', 120, True),
     ('Canada', _("加拿大"), 'CA', 'CAN', 124, True),
     ('Cayman Islands', _("开曼群岛"), 'KY', 'CYM', 136, False),
     ('Central African Republic', _("中非"), 'CF', 'CAF', 140, True),
     ('Chad', _("乍得"), 'TD', 'TCD', 148, True),
     ('Chile', _("智利"), 'CL', 'CHL', 152, True),
     ('China', _("中国"), 'CN', 'CHN', 156, True),
     ('Christmas Island', _("圣诞岛"), 'CX', 'CXR', 162, False),
     ('Cocos (Keeling) Islands', _("科科斯（基林）群岛"), 'CC', 'CCK', 166, False),
     ('Colombia', _("哥伦比亚"), 'CO', 'COL', 170, True),
     ('Comoros', _("科摩罗"), 'KM', 'COM', 174, True),
     ('Congo', _("刚果共和国"), 'CG', 'COG', 178, True),
     ('Congo (Democratic Republic of the)',
      _("刚果民主共和国"), 'CD', 'COD', 180, True),
     ('Cook Islands', _("库克群岛"), 'CK', 'COK', 184, False),
     ('Costa Rica', _("哥斯达黎加"), 'CR', 'CRI', 188, True),
     ("Côte d'Ivoire", _("科特迪瓦"), 'CI', 'CIV', 384, True),
     ('Croatia', _("克罗地亚"), 'HR', 'HRV', 191, True),
     ('Cuba', _("古巴"), 'CU', 'CUB', 192, True),
     ('Curaçao', _("库拉索"), 'CW', 'CUW', 531, False),
     ('Cyprus', _("塞浦路斯"), 'CY', 'CYP', 196, True),
     ('Czechia', _("捷克"), 'CZ', 'CZE', 203, True),
     ('Denmark', _("丹麦"), 'DK', 'DNK', 208, True),
     ('Djibouti', _("吉布提"), 'DJ', 'DJI', 262, True),
     ('Dominica', _("多米尼克"), 'DM', 'DMA', 212, True),
     ('Dominican Republic', _("多米尼加"), 'DO', 'DOM', 214, True),
     ('Ecuador', _("厄瓜多尔"), 'EC', 'ECU', 218, True),
     ('Egypt', _("埃及"), 'EG', 'EGY', 818, True),
     ('El Salvador', _("萨尔瓦多"), 'SV', 'SLV', 222, True),
     ('Equatorial Guinea', _("赤道几内亚"), 'GQ', 'GNQ', 226, True),
     ('Eritrea', _("厄立特里亚"), 'ER', 'ERI', 232, True),
     ('Estonia', _("爱沙尼亚"), 'EE', 'EST', 233, True),
     ('Eswatini', _("斯威士兰"), 'SZ', 'SWZ', 748, True),
     ('Ethiopia', _("埃塞俄比亚"), 'ET', 'ETH', 231, True),
     ('Falkland Islands (Malvinas)', _("福克兰群岛"), 'FK', 'FLK', 238, False),
     ('Faroe Islands', _("法罗群岛"), 'FO', 'FRO', 234, False),
     ('Fiji', _("斐济"), 'FJ', 'FJI', 242, True),
     ('Finland', _("芬兰"), 'FI', 'FIN', 246, True),
     ('France', _("法国"), 'FR', 'FRA', 250, True),
     ('French Guiana', _("法属圭亚那"), 'GF', 'GUF', 254, False),
     ('French Polynesia', _("法属波利尼西亚"), 'PF', 'PYF', 258, False),
     ('French Southern Territories', _("法属南部和南极领地"), 'TF', 'ATF', 260, False),
     ('Gabon', _("加蓬"), 'GA', 'GAB', 266, True),
     ('Gambia', _("冈比亚"), 'GM', 'GMB', 270, True),
     ('Georgia', _("格鲁吉亚"), 'GE', 'GEO', 268, True),
     ('Germany', _("德国"), 'DE', 'DEU', 276, True),
     ('Ghana', _("加纳"), 'GH', 'GHA', 288, True),
     ('Gibraltar', _("直布罗陀"), 'GI', 'GIB', 292, False),
     ('Greece', _("希腊"), 'GR', 'GRC', 300, True),
     ('Greenland', _("格陵兰"), 'GL', 'GRL', 304, False),
     ('Grenada', _("格林纳达"), 'GD', 'GRD', 308, True),
     ('Guadeloupe', _("瓜德罗普"), 'GP', 'GLP', 312, False),
     ('Guam', _("关岛"), 'GU', 'GUM', 316, False),
     ('Guatemala', _("危地马拉"), 'GT', 'GTM', 320, True),
     ('Guernsey', _("根西"), 'GG', 'GGY', 831, False),
     ('Guinea', _("几内亚"), 'GN', 'GIN', 324, True),
     ('Guinea-Bissau', _("几内亚比绍"), 'GW', 'GNB', 624, True),
     ('Guyana', _("圭亚那"), 'GY', 'GUY', 328, True),
     ('Haiti', _("海地"), 'HT', 'HTI', 332, True),
     ('Heard Island and McDonald Islands', _("赫德岛和麦克唐纳群岛"), 'HM', 'HMD', 334, False),
     ('Holy See', _("梵蒂冈"), 'VA', 'VAT', 336, True),
     ('Honduras', _("洪都拉斯"), 'HN', 'HND', 340, True),
     ('Hong Kong, China', _("中国香港"), 'HK', 'HKG', 344, False),
     ('Hungary', _("匈牙利"), 'HU', 'HUN', 348, True),
     ('Iceland', _("冰岛"), 'IS', 'ISL', 352, True),
     ('India', _("印度"), 'IN', 'IND', 356, True),
     ('Indonesia', _("印尼"), 'ID', 'IDN', 360, True),
     ('Iran (Islamic Republic of Iran)', _("伊朗"), 'IR', 'IRN', 364, True),
     ('Iraq', _("伊拉克"), 'IQ', 'IRQ', 368, True),
     ('Ireland', _("爱尔兰"), 'IE', 'IRL', 372, True),
     ('Isle of Man', _("马恩岛"), 'IM', 'IMN', 833, False),
     ('Israel', _("以色列"), 'IL', 'ISR', 376, True),
     ('Italy', _("意大利"), 'IT', 'ITA', 380, True),
     ('Jamaica', _("牙买加"), 'JM', 'JAM', 388, True),
     ('Japan', _("日本"), 'JP', 'JPN', 392, True),
     ('Jersey', _("泽西"), 'JE', 'JEY', 832, False),
     ('Jordan', _("约旦"), 'JO', 'JOR', 400, True),
     ('Kazakhstan', _("哈萨克斯坦"), 'KZ', 'KAZ', 398, True),
     ('Kenya', _("肯尼亚"), 'KE', 'KEN', 404, True),
     ('Kiribati', _("基里巴斯"), 'KI', 'KIR', 296, True),
     ("Korea (Democratic People's Republic of)", _("朝鲜"), 'KP', 'PRK', 408, True),
     ('Korea (Republic of)', _("韩国"), 'KR', 'KOR', 410, True),
     ('Kosovo', _("科索沃"), 'XK', 'XKX', 0, True),
     ('Kuwait', _("科威特"), 'KW', 'KWT', 414, True),
     ('Kyrgyzstan', _("吉尔吉斯斯坦"), 'KG', 'KGZ', 417, True),
     ("Lao People's Democratic Republic", _("老挝"), 'LA', 'LAO', 418, True),
     ('Latvia', _("拉脱维亚"), 'LV', 'LVA', 428, True),
     ('Lebanon', _("黎巴嫩"), 'LB', 'LBN', 422, True),
     ('Lesotho', _("莱索托"), 'LS', 'LSO', 426, True),
     ('Liberia', _("利比里亚"), 'LR', 'LBR', 430, True),
     ('Libya', _("利比亚"), 'LY', 'LBY', 434, True),
     ('Liechtenstein', _("列支敦士登"), 'LI', 'LIE', 438, True),
     ('Lithuania', _("立陶宛"), 'LT', 'LTU', 440, True),
     ('Luxembourg', _("卢森堡"), 'LU', 'LUX', 442, True),
     ('Macao, China', _("中国澳门"), 'MO', 'MAC', 446, False),
     ('Madagascar', _("马达加斯加"), 'MG', 'MDG', 450, True),
     ('Malawi', _("马拉维"), 'MW', 'MWI', 454, True),
     ('Malaysia', _("马来西亚"), 'MY', 'MYS', 458, True),
     ('Maldives', _("马尔代夫"), 'MV', 'MDV', 462, True),
     ('Mali', _("马里"), 'ML', 'MLI', 466, True),
     ('Malta', _("马耳他"), 'MT', 'MLT', 470, True),
     ('Marshall Islands', _("马绍尔群岛"), 'MH', 'MHL', 584, True),
     ('Martinique', _("马提尼克"), 'MQ', 'MTQ', 474, False),
     ('Mauritania', _("毛里塔尼亚"), 'MR', 'MRT', 478, True),
     ('Mauritius', _("毛里求斯"), 'MU', 'MUS', 480, True),
     ('Mayotte', _("马约特"), 'YT', 'MYT', 175, False),
     ('Mexico', _("墨西哥"), 'MX', 'MEX', 484, True),
     ('Micronesia (Federated States of)', _("密克罗尼西亚联邦"), 'FM', 'FSM', 583, True),
     ('Moldova (Republic of)', _("摩尔多瓦"), 'MD', 'MDA', 498, True),
     ('Monaco', _("摩纳哥"), 'MC', 'MCO', 492, True),
     ('Mongolia', _("蒙古国"), 'MN', 'MNG', 496, True),
     ('Montenegro', _("黑山"), 'ME', 'MNE', 499, True),
     ('Montserrat', _("蒙特塞拉特"), 'MS', 'MSR', 500, False),
     ('Morocco', _("摩洛哥"), 'MA', 'MAR', 504, True),
     ('Mozambique', _("莫桑比克"), 'MZ', 'MOZ', 508, True),
     ('Myanmar', _("缅甸"), 'MM', 'MMR', 104, True),
     ('Namibia', _("纳米比亚"), 'NA', 'NAM', 516, True),
     ('Nauru', _("瑙鲁"), 'NR', 'NRU', 520, True),
     ('Nepal', _("尼泊尔"), 'NP', 'NPL', 524, True),
     ('Netherlands', _("荷兰"), 'NL', 'NLD', 528, True),
     ('New Caledonia', _("新喀里多尼亚"), 'NC', 'NCL', 540, False),
     ('New Zealand', _("新西兰"), 'NZ', 'NZL', 554, True),
     ('Nicaragua', _("尼加拉瓜"), 'NI', 'NIC', 558, True),
     ('Niger', _("尼日尔"), 'NE', 'NER', 562, True),
     ('Nigeria', _("尼日利亚"), 'NG', 'NGA', 566, True),
     ('Niue', _("纽埃"), 'NU', 'NIU', 570, False),
     ('Norfolk Island', _("诺福克岛"), 'NF', 'NFK', 574, False),
     ('North Macedonia', _("北马其顿"), 'MK', 'MKD', 807, True),
     ('Northern Mariana Islands', _("北马里亚纳群岛"), 'MP', 'MNP', 580, False),
     ('Norway', _("挪威"), 'NO', 'NOR', 578, True),
     ('Oman', _("阿曼"), 'OM', 'OMN', 512, True),
     ('Pakistan', _("巴基斯坦"), 'PK', 'PAK', 586, True),
     ('Palau', _("帕劳"), 'PW', 'PLW', 585, True),
     ('Palestine, State of', _("巴勒斯坦"), 'PS', 'PSE', 275, False),
     ('Panama', _("巴拿马"), 'PA', 'PAN', 591, True),
     ('Papua New Guinea', _("巴布亚新几内亚"), 'PG', 'PNG', 598, True),
     ('Paraguay', _("巴拉圭"), 'PY', 'PRY', 600, True),
     ('Peru', _("秘鲁"), 'PE', 'PER', 604, True),
     ('Philippines', _("菲律宾"), 'PH', 'PHL', 608, True),
     ('Pitcairn', _("皮特凯恩群岛"), 'PN', 'PCN', 612, False),
     ('Poland', _("波兰"), 'PL', 'POL', 616, True),
     ('Portugal', _("葡萄牙"), 'PT', 'PRT', 620, True),
     ('Puerto Rico', _("波多黎各"), 'PR', 'PRI', 630, False),
     ('Qatar', _("卡塔尔"), 'QA', 'QAT', 634, True),
     ('Réunion', _("留尼汪"), 'RE', 'REU', 638, False),
     ('Romania', _("罗马尼亚"), 'RO', 'ROU', 642, True),
     ('Russian Federation', _("俄罗斯"), 'RU', 'RUS', 643, True),
     ('Rwanda', _("卢旺达"), 'RW', 'RWA', 646, True),
     ('Saint Barthélemy', _("圣巴泰勒米"), 'BL', 'BLM', 652, False),
     ('Saint Helena, Ascension and Tristan da Cunha', _("圣赫勒拿、阿森松和特里斯坦-达库尼亚"), 'SH', 'SHN', 654, False),
     ('Saint Kitts and Nevis', _("圣基茨和尼维斯"), 'KN', 'KNA', 659, True),
     ('Saint Lucia', _("圣卢西亚"), 'LC', 'LCA', 662, True),
     ('Saint Martin (French part)', _("法属圣马丁"), 'MF', 'MAF', 663, False),
     ('Saint Pierre and Miquelon', _("圣皮埃尔和密克隆"), 'PM', 'SPM', 666, False),
     ('Saint Vincent and the Grenadines', _("圣文森特和格林纳丁斯"), 'VC', 'VCT', 670, True),
     ('Samoa', _("萨摩亚"), 'WS', 'WSM', 882, True),
     ('San Marino', _("圣马力诺"), 'SM', 'SMR', 674, True),
     ('Sao Tome and Principe', _("圣多美和普林西比"), 'ST', 'STP', 678, True),
     ('Saudi Arabia', _("沙特阿拉伯"), 'SA', 'SAU', 682, True),
     ('Senegal', _("塞内加尔"), 'SN', 'SEN', 686, True),
     ('Serbia', _("塞尔维亚"), 'RS', 'SRB', 688, True),
     ('Seychelles', _("塞舌尔"), 'SC', 'SYC', 690, True),
     ('Sierra Leone', _("塞拉利昂"), 'SL', 'SLE', 694, True),
     ('Singapore', _("新加坡"), 'SG', 'SGP', 702, True),
     ('Sint Maarten (Dutch part)', _("荷属圣马丁"), 'SX', 'SXM', 534, False),
     ('Slovakia', _("斯洛伐克"), 'SK', 'SVK', 703, True),
     ('Slovenia', _("斯洛文尼亚"), 'SI', 'SVN', 705, True),
     ('Solomon Islands', _("所罗门群岛"), 'SB', 'SLB', 90, True),
     ('Somalia', _("索马里"), 'SO', 'SOM', 706, True),
     ('South Africa', _("南非"), 'ZA', 'ZAF', 710, True),
     ('South Georgia and the South Sandwich Islands', _("南乔治亚和南桑威奇群岛"), 'GS', 'SGS', 239, False),
     ('South Sudan', _("南苏丹"), 'SS', 'SSD', 728, True),
     ('Spain', _("西班牙"), 'ES', 'ESP', 724, True),
     ('Sri Lanka', _("斯里兰卡"), 'LK', 'LKA', 144, True),
     ('Sudan', _("苏丹"), 'SD', 'SDN', 729, True),
     ('Suriname', _("苏里南"), 'SR', 'SUR', 740, True),
     ('Svalbard and Jan Mayen', _("斯瓦尔巴和扬马延"), 'SJ', 'SJM', 744, False),
     ('Sweden', _("瑞典"), 'SE', 'SWE', 752, True),
     ('Switzerland', _("瑞士"), 'CH', 'CHE', 756, True),
     ('Syrian Arab Republic', _("叙利亚"), 'SY', 'SYR', 760, True),
     ('Taiwan, Province of China', _("台湾 中国台湾省"), 'TW', 'TWN', 158, False),
     ('Tajikistan', _("塔吉克斯坦"), 'TJ', 'TJK', 762, True),
     ('Tanzania, United Republic of', _("坦桑尼亚"), 'TZ', 'TZA', 834, True),
     ('Thailand', _("泰国"), 'TH', 'THA', 764, True),
     ('Timor-Leste', _("东帝汶"), 'TL', 'TLS', 626, True),
     ('Togo', _("多哥"), 'TG', 'TGO', 768, True),
     ('Tokelau', _("托克劳"), 'TK', 'TKL', 772, False),
     ('Tonga', _("汤加"), 'TO', 'TON', 776, True),
     ('Trinidad and Tobago', _("特立尼达和多巴哥"), 'TT', 'TTO', 780, True),
     ('Tunisia', _("突尼斯"), 'TN', 'TUN', 788, True),
     ('Turkey', _("土耳其"), 'TR', 'TUR', 792, True),
     ('Turkmenistan', _("土库曼斯坦"), 'TM', 'TKM', 795, True),
     ('Turks and Caicos Islands', _("特克斯和凯科斯群岛"), 'TC', 'TCA', 796, False),
     ('Tuvalu', _("图瓦卢"), 'TV', 'TUV', 798, True),
     ('Uganda', _("乌干达"), 'UG', 'UGA', 800, True),
     ('Ukraine', _("乌克兰"), 'UA', 'UKR', 804, True),
     ('United Arab Emirates', _("阿联酋"), 'AE', 'ARE', 784, True),
     ('United Kingdom of Great Britain and Northern Ireland', _("英国"), 'GB', 'GBR', 826, True),
     ('United States of America', _("美国"), 'US', 'USA', 840, True),
     ('United States Minor Outlying Islands', _("美国本土外小岛屿"), 'UM', 'UMI', 581, False),
     ('Uruguay', _("乌拉圭"), 'UY', 'URY', 858, True),
     ('Uzbekistan', _("乌兹别克斯坦"), 'UZ', 'UZB', 860, True),
     ('Vanuatu', _("瓦努阿图"), 'VU', 'VUT', 548, True),
     ('Venezuela (Bolivarian Republic of)', _("委内瑞拉"), 'VE', 'VEN', 862, True),
     ('Viet Nam', _("越南"), 'VN', 'VNM', 704, True),
     ('Virgin Islands (British)', _("英属维尔京群岛"), 'VG', 'VGB', 92, False),
     ('Virgin Islands (U.S.)', _("美属维尔京群岛"), 'VI', 'VIR', 850, False),
     ('Wallis and Futuna', _("瓦利斯和富图纳"), 'WF', 'WLF', 876, False),
     ('Western Sahara', _("阿拉伯撒哈拉民主共和国"), 'EH', 'ESH', 732, False),
     ('Yemen', _("也门"), 'YE', 'YEM', 887, True),
     ('Zambia', _("赞比亚"), 'ZM', 'ZMB', 894, True),
     ('Zimbabwe', _("津巴布韦"), 'ZW', 'ZWE', 716, True),
     ('Kazakhstan', _("哈萨克斯坦"), 'KZ', 'KAZ', 997, True),
     ('Antigua and Barbuda', _("安提瓜和巴布达"), 'AG', 'ATG', 1268, True),
     ('Guam', _("关岛"), 'GU', 'GUM', 1671, True),
     ('Dominican Republic', _("多米尼加共和国"), 'DO', 'DOM', 1809, True),
)
# END

class CountryInfo(NamedTuple):

    en_name: str
    cn_name: str
    iso_2: str
    iso_3: str
    iso_no: int
    is_independent: bool


_EN_NAME_TO_COUNTRY: Mapping[str, CountryInfo]
_CN_NAME_TO_COUNTRY: Mapping[str, CountryInfo]
_ISO2_TO_COUNTRY: Mapping[str, CountryInfo]
_ISO3_TO_COUNTRY: Mapping[str, CountryInfo]
_ISO_NO_TO_COUNTRY: Mapping[int, CountryInfo]
_ISO2_TUPLE = None
_ISO3_TUPLE = None
_ISO_NO_TUPLE = None
_CN_NAME_TUPLE = None


def _generate_countries_data():
    iso2_to_country = {}
    iso3_to_country = {}
    iso_no_to_country = {}
    en_name_to_country = {}
    cn_name_to_country = {}

    for (en_name,
         cn_name,
         iso_2,
         iso_3,
         iso_no,
         is_independent) in _COUNTRIES_DATA:
        c = CountryInfo(en_name, cn_name, iso_2, iso_3, iso_no, is_independent)
        en_name_to_country[en_name] \
            = cn_name_to_country[cn_name] \
            = iso2_to_country[iso_2] \
            = iso3_to_country[iso_3] \
            = iso_no_to_country[iso_no] \
            = c

    global _EN_NAME_TO_COUNTRY, _CN_NAME_TO_COUNTRY, \
        _ISO2_TO_COUNTRY, _ISO3_TO_COUNTRY, _ISO_NO_TO_COUNTRY
    _EN_NAME_TO_COUNTRY = en_name_to_country
    _CN_NAME_TO_COUNTRY = cn_name_to_country
    _ISO2_TO_COUNTRY = MappingProxyType(iso2_to_country)
    _ISO3_TO_COUNTRY = MappingProxyType(iso3_to_country)
    _ISO_NO_TO_COUNTRY = MappingProxyType(iso_no_to_country)


_generate_countries_data()


def list_country_codes_2() -> Tuple[str]:
    global _ISO2_TUPLE
    if _ISO2_TUPLE is None:
        _ISO2_TUPLE = tuple(sorted(_ISO2_TO_COUNTRY))
    return _ISO2_TUPLE


def list_country_codes_3() -> Tuple[str]:
    global _ISO3_TUPLE
    if _ISO3_TUPLE is None:
        _ISO3_TUPLE = tuple(sorted(_ISO3_TO_COUNTRY))
    return _ISO3_TUPLE


def list_country_codes_3_admin() -> Tuple[str]:
    # return tuple([x for x in list_country_codes_3() if x not in ('CHN', 'HKG', 'MAC', 'TWN')])
    return tuple([x for x in list_country_codes_3()])


def list_country_cn_name() -> Tuple[str]:
    global _CN_NAME_TUPLE
    if _CN_NAME_TUPLE is None:
        _CN_NAME_TUPLE = tuple(sorted(_CN_NAME_TO_COUNTRY.keys()))
    return _CN_NAME_TUPLE


def get_country(iso_code: Union[str, int]) -> Optional[CountryInfo]:
    if isinstance(iso_code, str):
        iso_code = iso_code.upper()
        code_len = len(iso_code)
        if code_len == 2:
            return _ISO2_TO_COUNTRY.get(iso_code)
        if code_len == 3:
            return _ISO3_TO_COUNTRY.get(iso_code)
    return _ISO_NO_TO_COUNTRY.get(iso_code)


def get_country_code_cn_name_dic() -> Dict[str, str]:
    countries = get_code_to_cn_name()
    countries.update(UNSPECIFIED='不区分')   # 兼容推广渠道语区-国家对应的情况
    countries.pop('CHN')
    return countries


def get_cn_name_to_code() -> dict:
    return {cn_name: code for code, cn_name in get_code_to_cn_name().items()}


def get_code_to_cn_name() -> dict:
    return {code: get_country(code).cn_name for code in list_country_codes_3()}


def search_for_countries(keyword: str) -> List[CountryInfo]:
    keyword = keyword.upper()

    countries = {}
    if keyword.isdigit():
        if (country := get_country(int(keyword))) is not None:
            countries[country.iso_3] = 1.
    if keyword.encode('utf-8').isalpha() and len(keyword) <= 3:
        for mapping in _ISO2_TO_COUNTRY, _ISO3_TO_COUNTRY:
            for name, country in mapping.items():
                if keyword not in name:
                    continue
                countries[country.iso_3] = (
                    1. if keyword == name
                    else ((0.5 + 0.5 * name.startswith(keyword))
                          * len(keyword) / len(name))
                )
    for mapping in _EN_NAME_TO_COUNTRY, _CN_NAME_TO_COUNTRY:
        for name, country in mapping.items():
            name = name.upper()
            if (keyword.encode('utf-8').isalpha() and len(keyword) <= 3
                    and not name.startswith(keyword)
                    or keyword not in name):
                continue
            countries[country.iso_3] = (
                1. if keyword == name
                else ((0.2 + 0.8 * name.startswith(keyword))
                      * len(keyword) / len(name))
            )

    return [get_country(code)
            for code, score in sorted(countries.items(),
                                      key=lambda x: (-x[1], x[0]))]


COUNTRY_CODE_CN_NAME_DIC = get_country_code_cn_name_dic()


class AreaInfo(Enum):
    EAST_ASIA = "东亚"
    SOUTHEAST_ASIA = "东南亚"
    SOUTH_ASIA = "南亚"
    CIS = "CIS"
    WEST_ASIA = "西亚"
    MIDDLE_EAST = "中东"
    ARAB = "阿拉伯"
    EUROPE = "欧洲"
    SOUTH_AFRICA = "南非"
    NORTH_AFRICA = "北非"
    NORTH_AMERICA = "北美"
    LATIN_AMERICA = "拉美"


_AREA_MAPPING = {
    AreaInfo.EAST_ASIA: ['中国', '中国香港', '中国澳门', '中国台湾', '日本', '韩国', '朝鲜', '新加坡'],
    AreaInfo.SOUTHEAST_ASIA: ['文莱', '柬埔寨', '印尼', '老挝', '马来西亚', '缅甸', '泰国', '越南',
                              '东帝汶'],
    AreaInfo.SOUTH_ASIA: ['孟加拉国', '不丹', '印度', '马尔代夫', '尼泊尔', '巴基斯坦', '斯里兰卡', '英属印度洋领地', '菲律宾'],
    AreaInfo.CIS: ['俄罗斯', '哈萨克斯坦', '吉尔吉斯斯坦', '塔吉克斯坦', '土库曼斯坦', '乌兹别克斯坦', '蒙古',
                   '阿富汗', '白俄罗斯', '乌克兰'],
    AreaInfo.WEST_ASIA: ['以色列', '阿塞拜疆', '格鲁吉亚', '亚美尼亚', '土耳其', ],
    AreaInfo.MIDDLE_EAST: ['伊朗',],
    AreaInfo.ARAB: ['伊拉克', '约旦', '黎巴嫩', '阿曼', '巴勒斯坦', '卡塔尔', '沙特阿拉伯', '叙利亚',
                    '阿联酋', '也门', '科威特', '巴林', '吉布提', '毛里塔尼亚', '科摩罗', '索马里'],
    AreaInfo.NORTH_AFRICA: ['埃及', '利比亚', '阿尔及利亚', '突尼斯', '摩洛哥', '苏丹'],
    AreaInfo.EUROPE: ['阿尔巴尼亚', '爱尔兰', '爱沙尼亚', '安道尔', '奥地利', '保加利亚', '北马其顿', '比利时',
                      '冰岛', '波黑', '波兰', '丹麦', '德国', '法国', '梵蒂冈', '芬兰', '荷兰', '黑山', '捷克', '克罗地亚',
                      '拉脱维亚',
                      '立陶宛', '列支敦士登', '卢森堡', '罗马尼亚', '马耳他', '摩尔多瓦', '摩纳哥', '挪威', '葡萄牙', '瑞典',
                      '瑞士',
                      '塞尔维亚', '塞浦路斯', '圣马力诺', '斯洛伐克', '斯洛文尼亚', '西班牙', '希腊', '匈牙利',
                      '意大利',
                      '英国', '科索沃', '直布罗陀', '奥兰', '圣赫勒拿、阿森松和特里斯坦-达库尼亚', '马恩岛', '法罗群岛', '泽西',
                      '根西',
                      '马约特', '斯瓦尔巴和扬马延'],
    AreaInfo.SOUTH_AFRICA: ['埃塞俄比亚', '安哥拉', '贝宁', '博茨瓦纳', '布基纳法索', '布隆迪', '赤道几内亚', '多哥', '厄立特里亚',
                      '佛得角', '冈比亚', '刚果', '刚果民主', '几内亚', '几内亚比绍', '加纳', '加蓬', '津巴布韦', '喀麦隆', '科特迪瓦',
                      '肯尼亚',
                      '莱索托', '利比里亚', '卢旺达', '马达加斯加', '马拉维', '马里', '毛里求斯', '莫桑比克', '纳米比亚', '南非',
                      '南苏丹', '尼日尔', '尼日利亚', '塞拉利昂', '塞内加尔', '塞舌尔', '圣多美和普林西比', '斯威士兰',
                      '坦桑尼亚',
                      '乌干达', '赞比亚', '乍得', '中非', '留尼汪', '法属南部和南极领地'],
    AreaInfo.NORTH_AMERICA: ['加拿大', '美国', '澳大利亚', '新西兰', '马提尼克', '格陵兰', '巴布亚新几内亚', '斐济', '基里巴斯',
                             '库克群岛', '马绍尔群岛', '密克罗尼西亚联邦', '瑙鲁', '帕劳', '萨摩亚', '所罗门群岛', '汤加',
                             '图瓦卢', '瓦努阿图',
                             '关岛', '法属波利尼西亚', '新喀里多尼亚', '北马里亚纳群岛', '美属萨摩亚', '诺福克岛',
                             '圣皮埃尔和密克隆', '托克劳',
                             '美国本土外小岛屿', '纽埃', '瓦利斯和富图纳', '圣诞岛', '赫德岛和麦克唐纳群岛'],
    AreaInfo.LATIN_AMERICA: ['墨西哥', '阿根廷', '玻利维亚', '巴西', '哥伦比亚', '智利', '厄瓜多尔', '巴拉圭', '秘鲁', '乌拉圭',
                             '委内瑞拉', '圭亚那', '苏里南', '开曼群岛', '英属维尔京群岛', '库拉索', '阿鲁巴', '法属圭亚那',
                             '荷兰加勒比区',
                             '福克兰群岛', '布韦岛', '安圭拉', '安提瓜和巴布达', '巴哈马', '巴巴多斯', '伯利兹', '百慕大',
                             '哥斯达黎加', '古巴',
                             '多米尼克', '多米尼加', '萨尔瓦多', '格林纳达', '瓜德罗普', '危地马拉', '海地', '洪都拉斯',
                             '牙买加', '蒙特塞拉特',
                             '尼加拉瓜', '巴拿马', '波多黎各', '圣基茨和尼维斯', '圣卢西亚', '法属圣马丁',
                             '圣文森特和格林纳丁斯',
                             '特立尼达和多巴哥', '特克斯和凯科斯群岛', '美属维尔京群岛', '荷属圣马丁']
}


class AreaCountryInfo(NamedTuple):
    area: AreaInfo
    info: CountryInfo


AREAS_MAPPING = {
    area_info: [
        AreaCountryInfo(area=area_info, info=info[0])
        for _name in _country_names_list
        if (info := search_for_countries(_name))]
    for area_info, _country_names_list in _AREA_MAPPING.items()
}


def list_country_codes_3_to_area() -> dict:
    return {
        area_country.info.iso_3: area_info
        for area_info, area_countries in AREAS_MAPPING.items() for area_country in area_countries
    }


_CONTINENT_CODE_TO_CONTINENT_CN_NAME = {
    'AS': '亚洲',
    'EU': '欧洲',
    'NA': '北美洲',
    'SA': '南美洲',
    'AF': '非洲',
    'OC': '大洋洲',
    'AN': '南极洲'
}

_COUNTRY_ALPHA2_TO_CONTINENT_CODE = {
    'AB': 'AS', 'AD': 'EU', 'AE': 'AS', 'AF': 'AS', 'AG': 'NA', 'AI': 'NA', 'AL': 'EU', 'AM': 'AS', 'AO': 'AF', 'AR': 'SA',
    'AS': 'OC', 'AT': 'EU', 'AU': 'OC', 'AW': 'NA', 'AX': 'EU', 'AZ': 'AS', 'BA': 'EU', 'BB': 'NA', 'BD': 'AS', 'BE': 'EU',
    'BF': 'AF', 'BG': 'EU', 'BH': 'AS', 'BI': 'AF', 'BJ': 'AF', 'BL': 'NA', 'BM': 'NA', 'BN': 'AS', 'BO': 'SA', 'BQ': 'NA',
    'BR': 'SA', 'BS': 'NA', 'BT': 'AS', 'BV': 'AN', 'BW': 'AF', 'BY': 'EU', 'BZ': 'NA', 'CA': 'NA', 'CC': 'AS', 'CD': 'AF',
    'CF': 'AF', 'CG': 'AF', 'CH': 'EU', 'CI': 'AF', 'CK': 'OC', 'CL': 'SA', 'CM': 'AF', 'CN': 'AS', 'CO': 'SA', 'CR': 'NA',
    'CU': 'NA', 'CV': 'AF', 'CW': 'NA', 'CX': 'AS', 'CY': 'AS', 'CZ': 'EU', 'DE': 'EU', 'DJ': 'AF', 'DK': 'EU', 'DM': 'NA',
    'DO': 'NA', 'DZ': 'AF', 'EC': 'SA', 'EE': 'EU', 'EG': 'AF', 'ER': 'AF', 'ES': 'EU', 'ET': 'AF', 'FI': 'EU', 'FJ': 'OC',
    'FK': 'SA', 'FM': 'OC', 'FO': 'EU', 'FR': 'EU', 'GA': 'AF', 'GB': 'EU', 'GD': 'NA', 'GE': 'AS', 'GF': 'SA', 'GG': 'EU',
    'GH': 'AF', 'GI': 'EU', 'GL': 'NA', 'GM': 'AF', 'GN': 'AF', 'GP': 'NA', 'GQ': 'AF', 'GR': 'EU', 'GS': 'SA', 'GT': 'NA',
    'GU': 'OC', 'GW': 'AF', 'GY': 'SA', 'HK': 'AS', 'HM': 'AN', 'HN': 'NA', 'HR': 'EU', 'HT': 'NA', 'HU': 'EU', 'ID': 'AS',
    'IE': 'EU', 'IL': 'AS', 'IM': 'EU', 'IN': 'AS', 'IO': 'AS', 'IQ': 'AS', 'IR': 'AS', 'IS': 'EU', 'IT': 'EU', 'JE': 'EU',
    'JM': 'NA', 'JO': 'AS', 'JP': 'AS', 'KE': 'AF', 'KG': 'AS', 'KH': 'AS', 'KI': 'OC', 'KM': 'AF', 'KN': 'NA', 'KP': 'AS',
    'KR': 'AS', 'KW': 'AS', 'KY': 'NA', 'KZ': 'AS', 'LA': 'AS', 'LB': 'AS', 'LC': 'NA', 'LI': 'EU', 'LK': 'AS', 'LR': 'AF',
    'LS': 'AF', 'LT': 'EU', 'LU': 'EU', 'LV': 'EU', 'LY': 'AF', 'MA': 'AF', 'MC': 'EU', 'MD': 'EU', 'ME': 'EU', 'MF': 'NA',
    'MG': 'AF', 'MH': 'OC', 'MK': 'EU', 'ML': 'AF', 'MM': 'AS', 'MN': 'AS', 'MO': 'AS', 'MP': 'OC', 'MQ': 'NA', 'MR': 'AF',
    'MS': 'NA', 'MT': 'EU', 'MU': 'AF', 'MV': 'AS', 'MW': 'AF', 'MX': 'NA', 'MY': 'AS', 'MZ': 'AF', 'NA': 'AF', 'NC': 'OC',
    'NE': 'AF', 'NF': 'OC', 'NG': 'AF', 'NI': 'NA', 'NL': 'EU', 'NO': 'EU', 'NP': 'AS', 'NR': 'OC', 'NU': 'OC', 'NZ': 'OC',
    'OM': 'AS', 'OS': 'AS', 'PA': 'NA', 'PE': 'SA', 'PF': 'OC', 'PG': 'OC', 'PH': 'AS', 'PK': 'AS', 'PL': 'EU', 'PM': 'NA',
    'PR': 'NA', 'PS': 'AS', 'PT': 'EU', 'PW': 'OC', 'PY': 'SA', 'QA': 'AS', 'RE': 'AF', 'RO': 'EU', 'RS': 'EU', 'RU': 'EU',
    'RW': 'AF', 'SA': 'AS', 'SB': 'OC', 'SC': 'AF', 'SD': 'AF', 'SE': 'EU', 'SG': 'AS', 'SH': 'AF', 'SI': 'EU', 'SJ': 'EU',
    'SK': 'EU', 'SL': 'AF', 'SM': 'EU', 'SN': 'AF', 'SO': 'AF', 'SR': 'SA', 'SS': 'AF', 'ST': 'AF', 'SV': 'NA', 'SY': 'AS',
    'SZ': 'AF', 'TC': 'NA', 'TD': 'AF', 'TG': 'AF', 'TH': 'AS', 'TJ': 'AS', 'TK': 'OC', 'TM': 'AS', 'TN': 'AF', 'TO': 'OC',
    'TP': 'AS', 'TR': 'AS', 'TT': 'NA', 'TV': 'OC', 'TW': 'AS', 'TZ': 'AF', 'UA': 'EU', 'UG': 'AF', 'US': 'NA', 'UY': 'SA',
    'UZ': 'AS', 'VC': 'NA', 'VE': 'SA', 'VG': 'NA', 'VI': 'NA', 'VN': 'AS', 'VU': 'OC', 'WF': 'OC', 'WS': 'OC', 'XK': 'EU',
    'YE': 'AS', 'YT': 'AF', 'ZA': 'AF', 'ZM': 'AF', 'ZW': 'AF'
}

def country_alpha2_to_continent_code(country_2_code):
    """Convert country code to continent.
    """
    if country_2_code is None or len(country_2_code) != 2:
        raise KeyError("Invalid Country Alpha-2 code: '{0}'".format(country_2_code))

    if country_2_code not in _COUNTRY_ALPHA2_TO_CONTINENT_CODE:
        raise KeyError("Invalid Country Alpha-2 code: '{0}'".format(country_2_code))

    return _COUNTRY_ALPHA2_TO_CONTINENT_CODE[country_2_code]

def list_country_codes_3_to_continent() -> dict:
    country_codes = list_country_codes_2()
    
    result = {}
    # Get all countries
    for country in country_codes:
        try:
            continent_code = country_alpha2_to_continent_code(country)
        except Exception:
            continue
        k = get_country(country).iso_3
        result[k] = continent_code
    return result

def list_continent_cn_names() -> Dict[str, str]:
    return _CONTINENT_CODE_TO_CONTINENT_CN_NAME
