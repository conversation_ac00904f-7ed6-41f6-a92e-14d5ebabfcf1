

import json
from datetime import timed<PERSON><PERSON>, datetime
from decimal import Decimal
from pprint import pprint

import pytest
from flask import g

from app import Language
from app.caches.activity import AirdropActivityDetailCache, AirdropActivityCache
from app.models import User, ActivityCondition, AirdropActivityCondition
from app.schedules.activity import get_active_total_trade_map, update_airdrop_statistic_schedule, \
    update_coinex_wallet_traffic_schedule
from app.utils import today_datetime


class TestApi:

    def test_push_statistic(self, tcontext):

        with tcontext:
            client = tcontext.app.test_client()
            for p in ["Android", "iOS", "web"]:
                for k in ["popup_page_view", "popup_click_count"]:
                    resp = client.post(
                        "/res/analytics/push-statistic",
                        json={
                            "report_type": k,
                            "id": 397,
                        },
                        headers={
                            "PLATFORM": p,
                        }
                    )
                    print(resp.text)
                    assert resp.json["code"] == 0



