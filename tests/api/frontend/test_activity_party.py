from pprint import pprint

import pytest
from flask import g

from app import Language
from app.models import User, ActivityPartyUser, MaskUser, db

USER_ID = 22430


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.auth_user = g.user = User.query.get(USER_ID)
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
class TestActivityParty:
    model = ActivityPartyUser

    def test_vertus_user(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get(
                f"/res/user/activity-party",
                query_string={
                    "activity_party": "VERTUS"
                }
            )
            pprint(resp.json)
            assert resp.json["code"] == 0


    def test_add_user(self, tcontext):
        with tcontext:
            obj = ActivityPartyUser(
                user_id=USER_ID,
                activity_party=self.model.ActivityParty.VERTUS,
                mask_id=MaskUser.get_or_create(USER_ID).mask_id
            )
            db.session_add_and_commit(obj)

    def test_vertus(self, tcontext):
        from app.business.activity.activity_party import VertusActivityMixin

        with tcontext:
            code = self.model.query.filter(self.model.user_id == USER_ID).first().mask_id
            client = tcontext.app.test_client()
            resp = client.get(
                f"/res/activity/vertus",
                query_string={
                    "code": code
                },
                headers={
                    "Cf-Connecting-IP": VertusActivityMixin.IP_WHITELIST[0]
                }
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_short_link_reload(self, tcontext):

        with tcontext:
            from app.caches.operation import ShortLinkInfoCache
            ShortLinkInfoCache.reload()