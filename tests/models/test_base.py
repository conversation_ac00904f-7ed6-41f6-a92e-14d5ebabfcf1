#!/usr/bin/python
# -*- coding: utf-8 -*-

import pytest
from sqlalchemy import func, literal_column

from app.models import db
from app.models.activity import CouponBalanceHistory
from app.models.broker import DailyBrokerUserAssetReport
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis

USER_ID = 2


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestQueryCount:
    """测试自定义的 Query.count 实现"""

    @staticmethod
    def prepare_data():
        CouponBalanceHistory.query.filter_by(user_id=USER_ID).delete()
        for i in range(10):
            user_coupon_id = 140 + i
            CouponBalanceHistory.query.filter_by(user_coupon_id=user_coupon_id).delete()
            record = CouponBalanceHistory()
            record.user_id = USER_ID
            record.system_user_id = 993
            record.user_coupon_id = user_coupon_id
            record.asset = 'USDT' if i < 3 else 'ETH'
            record.amount = 10
            record.status = CouponBalanceHistory.Status.FINISHED.name
            record.business_type = CouponBalanceHistory.BusinessType.SEND.name
            db.session.add(record)
        db.session.commit()

    def test_normal_query(self, tcontext):
        with tcontext:
            self.prepare_data()
            query = CouponBalanceHistory.query.filter_by(user_id=USER_ID)
            assert query.count() == 10

    def test_group_by(self, tcontext):
        with (tcontext):
            self.prepare_data()
            query = CouponBalanceHistory.query.filter_by(user_id=USER_ID) \
                .with_entities(func.sum(CouponBalanceHistory.amount).label('amount')) \
                .group_by(CouponBalanceHistory.asset)
            assert query.count() == 2

    def test_query_without_filter(self, tcontext):
        with (tcontext):
            self.prepare_data()
            query = CouponBalanceHistory.query
            assert query.count() >= 10

    def test_distinct(self, tcontext):
        with (tcontext):
            self.prepare_data()
            query = CouponBalanceHistory.query.filter_by(user_id=USER_ID) \
                .with_entities(CouponBalanceHistory.asset.distinct())
            assert query.count() == 2

    def test_group_by_with_having(self, tcontext):
        with (tcontext):
            self.prepare_data()
            query = CouponBalanceHistory.query.filter_by(user_id=USER_ID) \
                .group_by(CouponBalanceHistory.asset) \
                .with_entities(func.sum(CouponBalanceHistory.amount).label('amount')) \
                .having(literal_column('amount') == 30)
            assert query.count() == 1

    def test_group_by_multi_col(self, tcontext):
        with tcontext:
            query = DailyBrokerUserAssetReport.query
            query = query.filter(DailyBrokerUserAssetReport.user_id == 1)
            query = query.filter(DailyBrokerUserAssetReport.broker_user_id == 1)
            query = query.with_entities(
                DailyBrokerUserAssetReport.broker_user_id,
                DailyBrokerUserAssetReport.user_id,
                func.sum(DailyBrokerUserAssetReport.amount).label("amount"),
                func.sum(DailyBrokerUserAssetReport.spot_fee_usd).label("spot_fee_usd"),
                func.sum(DailyBrokerUserAssetReport.perpetual_fee_usd).label("perpetual_fee_usd"),
                func.sum(DailyBrokerUserAssetReport.spot_trade_usd).label("spot_trade_usd"),
                func.sum(DailyBrokerUserAssetReport.perpetual_trade_usd).label("perpetual_trade_usd"),
            ).group_by(DailyBrokerUserAssetReport.broker_user_id,
                       DailyBrokerUserAssetReport.user_id)
            paginate = query.paginate(page=1, per_page=50, error_out=False)
            assert paginate.total is not None
