import pytest
from tests.common.mock_redis import patch_redis

ZSET_KEY = 'test_mock_zset'
MEMBER_NAME = 'a'


@pytest.mark.usefixtures('patch_redis')
class TestMockOrderedSet:
    """测试有序集合 mock"""

    def test_zdd(self, tcontext):
        """测试添加"""
        from app.caches import SmsBlackListCache
        with tcontext:
            zset_cache = SmsBlackListCache(ZSET_KEY)
            zset_cache.add(MEMBER_NAME)

            assert zset_cache.redis \
                .origin_redis.zscore(zset_cache._key, MEMBER_NAME) is None
            assert zset_cache.redis \
                .fake_redis.zscore(zset_cache._key, MEMBER_NAME) is not None

    def test_zscore(self, tcontext):
        """测试 zscore"""
        from app.caches import SmsBlackListCache
        with tcontext:
            zset_cache = SmsBlackListCache(ZSET_KEY)
            zset_cache.add(MEMBER_NAME)

            assert zset_cache.zscore(MEMBER_NAME) > 0

    def test_has(self, tcontext):
        """测试 has"""
        from app.caches import SmsBlackListCache
        with tcontext:
            zset_cache = SmsBlackListCache(ZSET_KEY)
            zset_cache.add(MEMBER_NAME)

            assert zset_cache.has(MEMBER_NAME)

    def test_zrem(self, tcontext):
        """测试删除"""
        from app.caches import SmsBlackListCache
        with tcontext:
            zset_cache = SmsBlackListCache(ZSET_KEY)
            zset_cache.add(MEMBER_NAME)

            zset_cache.zrem(MEMBER_NAME)

            assert zset_cache.redis \
                .origin_redis.zscore(zset_cache._key, MEMBER_NAME) is None
            assert zset_cache.redis \
                .fake_redis.zscore(zset_cache._key, MEMBER_NAME) is None

    def test_zremrangebyrank(self, tcontext):
        """测试按排名删除"""
        from app.caches import SmsBlackListCache
        with tcontext:
            zset_cache = SmsBlackListCache(ZSET_KEY)
            zset_cache.add(MEMBER_NAME)

            print(zset_cache.redis.zrank(zset_cache._key, MEMBER_NAME))

            # zset_cache.zrem(MEMBER_NAME)
            #
            # assert zset_cache.redis \
            #     .origin_redis.zscore(zset_cache._key, MEMBER_NAME) is None
            # assert zset_cache.redis \
            #     .fake_redis.zscore(zset_cache._key, MEMBER_NAME) is None

