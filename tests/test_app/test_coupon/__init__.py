# -*- coding: utf-8 -*-
from flask import g
from app.models import db, User, Message
from app.models.activity import Coupon, ExperienceFeeCoupon, CouponApply, CouponApplyDraft, CouponPool, \
    CashBackFeeCoupon
from app.utils import now, RESTClient, new_hex_token
from tests.base import TestCase


class TestCoupon(TestCase):

    def set_default_data(self):
        self.system_user = User(
            id=1,
            name="993",
        )
        db.session.add(self.system_user)
        self.coupon_user_1 = User(
            id=1260,
            name="pawn",
            email="<EMAIL>"
        )
        db.session.add(self.coupon_user_1)
        self.ex_coupon = Coupon(
            coupon_type=Coupon.CouponType.EXPERIENCE_FEE,
            value=10,
            value_type="USDT",
            receivable_days=3,
            usable_days=15
        )
        db.session.add(self.ex_coupon)
        db.session.flush()
        self.ex_coupon_detail = ExperienceFeeCoupon(
            coupon_id=self.ex_coupon.id,
            qualified_trade_amount=2000
        )
        db.session.add(self.ex_coupon_detail)
        self.cf_coupon = Coupon(
            coupon_type=Coupon.CouponType.CASHBACK_FEE,
            value=10,
            value_type="USDT",
            receivable_days=3,
            activation_days=3,
            usable_days=15
        )
        db.session.add(self.cf_coupon)
        db.session.flush()
        self.cf_coupon_detail = CashBackFeeCoupon(
            coupon_id=self.cf_coupon.id,
            trade_type=CashBackFeeCoupon.TradeType.ALL
        )
        db.session.add(self.cf_coupon_detail)
        self.apply_draft = CouponApplyDraft(
            title="test apply draft",
            creat_user_id=self.coupon_user_1.id,
            auditor_id=self.system_user.id,
        )
        db.session.add(self.apply_draft)
        db.session.flush()
        self.origin_draft_apply = CouponApply(
            coupon_id=self.ex_coupon.id,
            origin_type=CouponApply.OriginType.DRAFT,
            origin_id=self.apply_draft.id,
            send_at=now(),
            status=CouponApply.Status.CREATED,
            source=CouponApply.Source.SYSTEM,
            total_count=2
        )
        self.origin_draft_apply.set_user_ids([1260])
        db.session.add(self.origin_draft_apply)
        db.session.commit()

    def down_default_data(self):
        CouponApplyDraft.query.delete()
        CouponApply.query.delete()
        ExperienceFeeCoupon.query.delete()
        CashBackFeeCoupon.query.delete()
        Coupon.query.delete()
        Message.query.delete()
        User.query.delete()
        db.session.commit()

    @classmethod
    def _set_login_user(cls, user_id):
        from app.caches import UserLoginTokenCache
        from app.common import LOGIN_TOKEN_SIZE, LOGIN_STATE_DEFAULT_TTL
        token = new_hex_token(LOGIN_TOKEN_SIZE)
        UserLoginTokenCache(user_id).add_token(token, LOGIN_STATE_DEFAULT_TTL)
        return token

    def setUp(self):
        super().setUp()
        self.set_default_data()
        token = self._set_login_user(self.coupon_user_1.id)
        self.client = RESTClient("http://localhost:5000/res/activity", headers={
            "AUTHORIZATION": token
        })

    def assert_api_success(self, response):
        self.assertEqual(response['code'], 0)

    def assert_api_fail(self, response):
        self.assertNotEqual(response['code'], 0)

    def tearDown(self):
        self.down_default_data()
        super().tearDown()
        db.session.remove()
