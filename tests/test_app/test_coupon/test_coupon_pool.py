# -*- coding: utf-8 -*-

from app.business.coupon.pool import create_coupon_pool_by_apply_task, get_user_available_pool, \
    update_coupon_dynamic_user_task, get_user_coupon_pools, send_delivery_coupon_task
from app.caches import CouponCache
from app.caches.activity import AvailableCouponPoolUserCache, CouponPoolCache, DynamicUserCouponCache, \
    PushAvailablePoolCache
from app.models import db
from app.models.activity import CouponPool, CouponApply, UserCoupon, CashBackFeeUserCoupon
from app.utils import amount_to_str, now
from tests.test_app.test_coupon import TestCoupon


class TestCouponCache(TestCoupon):

    def test_coupon_cache(self):
        CouponCache.reload_all()
        cache = CouponCache()
        self.assertEqual(cache.hlen(), 1)
        cache_data = cache.get_coupon_mapper()[self.ex_coupon.id]
        self.assertEqual(cache_data['coupon_type'], self.ex_coupon.coupon_type.name)
        self.assertEqual(
            cache_data['extra']["qualified_trade_amount"],
            amount_to_str(self.ex_coupon_detail.qualified_trade_amount)
        )


class TestCouponPool(TestCoupon):

    def tearDown(self):
        CashBackFeeUserCoupon.query.delete()
        UserCoupon.query.delete()
        CouponPool.query.delete()
        super(TestCouponPool, self).tearDown()
        PushAvailablePoolCache.delete_all()

    def update_cache(self):
        AvailableCouponPoolUserCache.reload()
        CouponPoolCache.reload()

    def test_get_system_pool_by_system(self):
        create_coupon_pool_by_apply_task()
        self.update_cache()
        cache_pool_data = get_user_available_pool(self.coupon_user_1.id)
        system_pools = cache_pool_data[CouponApply.Source.SYSTEM.name]
        self.assertEqual(len(system_pools), 1)

    def test_show_activity_pool(self):
        self.origin_draft_apply = CouponApply(
            coupon_id=self.ex_coupon.id,
            origin_type=CouponApply.OriginType.DRAFT,
            origin_id=self.apply_draft.id,
            send_at=now(),
            dynamic_user_type=CouponApply.DynamicUser.NOVICE_PREFECTURE,
            status=CouponApply.Status.CREATED,
            source=CouponApply.Source.ACTIVITY,
            total_count=2
        )
        self.origin_draft_apply.set_user_ids([self.coupon_user_1.id])
        db.session.add(self.origin_draft_apply)
        db.session.commit()
        create_coupon_pool_by_apply_task()
        self.update_cache()
        cache_pool_data = get_user_available_pool(self.coupon_user_1.id)
        system_pools = cache_pool_data[CouponApply.Source.SYSTEM.name]
        self.assertEqual(len(system_pools), 1)
        pools = CouponPool.query.all()
        self.assertEqual(len(pools), 2)
        other_user_pool_data = get_user_available_pool(self.system_user.id)
        self.assertEqual(other_user_pool_data, {})

    def test_show_public_activity_pools(self):
        dynamic_user_type = CouponApply.DynamicUser.REFERRAL_GIFT_MUL
        user_id = self.coupon_user_1.id
        self.referral_gift_apply = CouponApply(
            coupon_id=self.ex_coupon.id,
            origin_type=CouponApply.OriginType.DRAFT,
            origin_id=self.apply_draft.id,
            send_at=now(),
            send_user_type=CouponApply.SendUserType.DYNAMIC,
            dynamic_user_type=dynamic_user_type,
            status=CouponApply.Status.CREATED,
            source=CouponApply.Source.ACTIVITY,
            total_count=2
        )
        db.session.add(self.referral_gift_apply)
        db.session.commit()
        create_coupon_pool_by_apply_task()

        DynamicUserCouponCache(
            dynamic_user_type.name,
            self.ex_coupon.coupon_type.name,
            self.referral_gift_apply.id,
        ).set_users([user_id])
        update_coupon_dynamic_user_task(dynamic_user_type.name)
        self.update_cache()
        pools = get_user_coupon_pools(user_id)
        self.assertEqual(len(pools), 2)
        pools = CouponPool.query.all()
        self.assertEqual(len(pools), 2)
        other_user_pools = get_user_coupon_pools(self.system_user.id)
        self.assertEqual(len(other_user_pools), 0)

    def test_show_delivery_pool(self):
        user_id = self.coupon_user_1.id
        self.delivery_apply = CouponApply(
            coupon_id=self.cf_coupon.id,
            origin_type=CouponApply.OriginType.DRAFT,
            origin_id=self.apply_draft.id,
            send_at=now(),
            send_user_type=CouponApply.SendUserType.SOME,
            status=CouponApply.Status.CREATED,
            source=CouponApply.Source.DELIVERY,
            total_count=2
        )
        db.session.add(self.delivery_apply)
        self.delivery_apply.set_user_ids([user_id, self.system_user.id])
        db.session.commit()
        create_coupon_pool_by_apply_task()
        send_delivery_coupon_task(self.delivery_apply.id)
        self.update_cache()
        pools = get_user_available_pool(self.coupon_user_1.id)
        delivery_pools = pools.get(CouponApply.Source.DELIVERY.name)
        self.assertEqual(len(delivery_pools), 1)
        response = self.client.post("/coupons/popup/report", json=dict(
            pool_ids=[delivery_pools[0]["id"]]
        ))
        self.assert_api_success(response)
        next_pools = get_user_available_pool(self.coupon_user_1.id)
        watched_delivery_pools = next_pools.get(CouponApply.Source.DELIVERY.name)
        self.assertEqual(len(watched_delivery_pools), 0)
