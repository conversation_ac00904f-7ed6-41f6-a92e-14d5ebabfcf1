# -*- coding: utf-8 -*-
import json
import unittest
from unittest.mock import patch

from app.business.coupon.base import get_coupon_service
from app.business.coupon.message import get_coupon_message_server, SendTiming
from app.business.coupon.pool import create_coupon_pool_by_apply_task, get_user_available_pool, \
    update_coupon_dynamic_user_task, get_user_coupon_pools, send_delivery_coupon_task
from app.business.push_statistic import CouponType
from app.caches import CouponCache
from app.caches.activity import AvailableCouponPoolUserCache, CouponPoolCache, DynamicUserCouponCache, \
    PushAvailablePoolCache
from app.common import MessageTitle
from app.models import db, User, Message
from app.models.activity import CouponPool, CouponApply, UserCoupon, CashBackFeeUserCoupon, ExperienceFeeUserCoupon, \
    Coupon, TradingGiftUserCoupon, InvestmentIncRateUserCoupon
from app.utils import amount_to_str, now
from tests.test_app.test_coupon import TestCoupon


class TestExperienceFeeCouponMessage(TestCoupon):

    coupon_type = Coupon.CouponType.EXPERIENCE_FEE

    def tearDown(self):
        TradingGiftUserCoupon.query.delete()
        InvestmentIncRateUserCoupon.query.delete()
        CashBackFeeUserCoupon.query.delete()
        ExperienceFeeUserCoupon.query.delete()
        UserCoupon.query.delete()
        CouponPool.query.delete()
        super(TestExperienceFeeCouponMessage, self).tearDown()
        PushAvailablePoolCache.delete_all()

    def update_cache(self):
        AvailableCouponPoolUserCache.reload()
        CouponPoolCache.reload()

    def _mock_send_email(self):
        patcher = patch("app.business.coupon.message.EmailMixin.send_single_email")
        self.addCleanup(patcher.stop)
        patcher.start()
        return patcher

    def _mock_send_push(self):
        pass

    def get_message(self):
        return Message.query.filter(
            Message.user_id == self.coupon_user_1.id,
        ).order_by(Message.id.desc()).first()

    def test_send_coupon_message(self):
        create_coupon_pool_by_apply_task()
        self.update_cache()
        pool = CouponPool.query.first()
        timing = SendTiming.SEND_COUPON
        message_server = get_coupon_message_server(timing, self.coupon_type)
        message_server.send_message(self.origin_draft_apply, self.ex_coupon, pool)

    def test_use_coupon_message(self):
        create_coupon_pool_by_apply_task()
        self.update_cache()
        timing = SendTiming.USE_COUPON
        message_server = get_coupon_message_server(timing, self.coupon_type)
        pool = CouponPool.query.first()
        service = get_coupon_service(self.coupon_type)
        user_coupon = service.receive(
            self.ex_coupon,
            pool,
            self.coupon_user_1.id
        )
        message_server.send_message(user_coupon)
        message = self.get_message()
        self.assertIsNotNone(message)
        self.assertEqual(message.title, MessageTitle.COUPON_QUALIFIED)
        message_params = json.loads(message.params)
        self.assertEqual(message_params['value'], amount_to_str(self.ex_coupon.value))
        self.assertEqual(message_params['value_type'], self.ex_coupon.value_type)

    def test_coupon_early_maturity(self):
        create_coupon_pool_by_apply_task()
        self.update_cache()
        timing = SendTiming.EARLY_MATURITY
        message_server = get_coupon_message_server(timing, self.coupon_type)
        pool = CouponPool.query.first()
        service = get_coupon_service(self.coupon_type)
        user_coupon = service.receive(
            self.ex_coupon,
            pool,
            self.coupon_user_1.id
        )
        user_coupon.status = UserCoupon.Status.USED
        user_coupon_detail = ExperienceFeeUserCoupon.query.filter(
            ExperienceFeeUserCoupon.user_coupon_id == user_coupon.id
        ).first()
        user_coupon_detail.reason = ExperienceFeeUserCoupon.RecycledReason.QUALIFIED
        db.session.commit()
        message_server.send_message(pool)
        message = self.get_message()
        self.assertIsNotNone(message)
        self.assertEqual(message.title, MessageTitle.COUPON_UNLOCK)
        self.assertEqual(message.display_type, Message.DisplayType.POPUP_WINDOW)

    @unittest.skip("email push testcase temporarily unavailable")
    def test_coupon_dead_line(self):
        pass