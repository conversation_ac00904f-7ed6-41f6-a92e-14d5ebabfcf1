# -*- coding: utf-8 -*-

import os
from unittest.mock import patch
from decimal import Decimal

from flask import g
from flask_babel import refresh

from app.common.constants import Language
from tests.base import TestCase


@patch("app.caches.AssetCache")
@patch("rediscluster.RedisCluster")
@patch("redis.StrictRedis")
@patch("app.utils.auto_import", autospec=True)
class EmailTemplateRenderMigrationTestCase(TestCase):

    LanguageSets = [member.value for _, member in Language.__members__.items()]

    maxDiff = None

    def _mock_email_send(self):
        patcher = patch("app.business.email.EmailSender.send")
        self.addCleanup(patcher.stop)
        patcher.start()
        return patcher

    def _assertRenderedSame(self, params):
        from app.business.email import EmailSender

        patcher = self._mock_email_send()

        for lang in self.LanguageSets:
            # manual switch language cause of avoid flask_babel caches
            params["lang"] = g.lang = lang
            refresh()

            EmailSender.send_from_template_legacy(**params)
            legacy_subject = patcher.target.send.call_args.args[1]
            legacy_text = patcher.target.send.call_args.args[2]

            EmailSender.send_from_template_new(**params)
            new_subject = patcher.target.send.call_args.args[1]
            new_text = patcher.target.send.call_args.args[2]

            self.assertMultiLineEqual(legacy_text, new_text)
            self.assertEqual(legacy_subject, new_subject)

    def test_render_2fa_reset_fail(self, *args):
        params = dict(
            template_category='notice',
            template_type="2fa_reset_fail",
            template_args=dict(
                site_url="https://www.coinex.com/",
                support_url="https://www.coninex.com",
                name="test_name",
            ),
            recipient="test_recipient",
        )
        self._assertRenderedSame(params)

    def test_render_ambassador_agent_appraisal(self, *args):
        params = dict(
            template_category='notice',
            template_type="ambassador_agent_appraisal",
            template_args=dict(
                name="test_name",
                support_url="test_support_url",
                deal_amount="test_deal_amount",
                fail_count=10,
                referral_rate="test_amount" + "%"
            ),
            recipient="test_recipient",
        )
        for template in (
            "ambassador_agent_appraisal",
            "ambassador_agent_appraisal_update",
        ):
            params["template_type"] = template
            self._assertRenderedSame(params)

    def test_render_ambassador_level_appraisal(self, *args):
        params = dict(
            template_category='notice',
            template_type="ambassador_level_appraisal",
            template_args=dict(
                name="test_name",
                support_url="test_support_url",
                result_status="test_result_status",
                level_name="test_level",
                old_level_name="test_old_level",
                referral_rate="test_referral_rate",
                referral_asset="USDT",
                least_referral_rate=Decimal('0.4'),
                least_deal_amount=Decimal(500000),
                deal_amount=Decimal(500000),
                month_count=3,
            ),
            recipient="test_recipient",
        )
        for template in (
            "ambassador_agent_appraisal",
            "ambassador_agent_appraisal_update",
        ):
            params["template_type"] = template
            self._assertRenderedSame(params)

    def test_render_ambassador_train_book(self, *args):
        params = dict(
            template_category='notice',
            template_type="ambassador_train_book",
            template_args=dict(
                name="test_name",
                site_url="https://www.coinex.com/",
                support_url="test_support_url",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    # todo: subject text not exists
    # def test_render_announcement_notice(self, *args):
    #     params = dict(
    #         template_category='notice',
    #         template_type="announcement_notice",
    #         template_args=dict(
    #             site_url="test_site_url",
    #             content="test_content",
    #             manager_id="test_manager_id",
    #             unsubscribe_uri="test_unsubscribe_uri"
    #         ),
    #         recipient="test_email",
    #     )
    #
    #     self._assertRenderedSame(params)

    def test_render_deposit_amount_too_small(self, *args):
        params = dict(
            template_category='notice',
            template_type="deposit_amount_too_small",
            template_args=dict(
                name="test_name",
                diff_amount="test_diff_amount",
                amount="test_amount",
                min_amount="test_min_amount",
                deposit_time_str="test_deposit_time_str",
                asset_chain_str="test_asset_chain_str",
                url="test_url",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_deposit_pass_notice(self, *args):
        params = dict(
            template_category='notice',
            template_type="deposit_pass_notice",
            template_args=dict(
                name="test_name",
                amount="test_amount",
                create_time="test_create_time",
                anti_phishing_code="test_anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_deposit_resumed_notice(self, *args):
        params = dict(
            template_category='notice',
            template_type="deposit_resumed_notice",
            template_args=dict(
                name="test_name",
                asset_chain_str="test_asset_chain_str",
                url="test_url"
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_deposit_to_receive(self, *args):
        params = dict(
            template_category='notice',
            template_type="deposit_to_receive",
            template_args=dict(
                name="test_name",
                amount="test_amount",
                confirmations=0,
                freeze_confirmations="test_freeze_confirmations",
                tx_id="test_tx_id",
                tx_id_url="test_tx_id_url",
                anti_phishing_code="test_anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_edit_login_password(self, *args):
        params = dict(
            template_category='notice',
            template_type="edit_login_password",
            template_args=dict(
                create_time="create_time",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_edit_trade_password(self, *args):
        params = dict(
            template_category='notice',
            template_type="edit_trade_password",
            template_args=dict(
                create_time="create_time",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_security_notice_email(self, *args):
        params = dict(
            template_category='notice',
            template_type="email_reset_pass",
            template_args=dict(
                site_url="https://www.coinex.com/",
                support_url="test_support_url",
                name="test_name",
            ),
            recipient="test_email",
        )

        for template in (
            "totp_reset_pass",
            "mobile_reset_pass",
            "email_reset_pass",
            "2fa_reset_fail",
        ):
            params["template_type"] = template
            self._assertRenderedSame(params)

    def test_render_get_promotion_gift(self, *args):
        params = dict(
            template_category='notice',
            template_type="get_promotion_gift",
            template_args=dict(
                name="test_name",
                amount="test_amount",
                coin_type="test_coin_type",
                anti_phishing_code="test_anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_kyc_fail(self, *args):
        params = dict(
            template_category='notice',
            template_type="kyc_fail",
            template_args=dict(
                name="test_name",
                verify_id_result="test_id_result",
                account_link="test_account_link",
                support_url="test_support_url",
                anti_phishing_code="test_anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_kyc_pass(self, *args):
        params = dict(
            template_category='notice',
            template_type="kyc_pass",
            template_args=dict(
                name="test_name",
                home_link="test_home_link",
                support_url="test_support_url",
                anti_phishing_code="test_anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_maker_cashback_level_appraisal(self, *args):
        params = dict(
            template_category='notice',
            template_type="maker_cashback_level_appraisal",
            template_args=dict(
                result_status="test_result_value",
                name="test_name",
                new_level="test_new_level",
                user_trade_amount="user_trade_amount",
                month_count="test_month_count",
                limit_trade_amount="test_limit_trade_amount",
                rate="12%",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_maker_cashback_user_daily_report(self, *args):
        params = dict(
            template_category='notice',
            template_type="maker_cashback_user_daily_report",
            template_args=dict(
                name="test_name",
                report_date="report_date_str",
                level="test_level",
                rate="12%",
                cashback_data=dict(CET=Decimal("0")),
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_margin_liquidation(self, *args):
        params = dict(
            template_category='notice',
            template_type="margin_liquidation",
            template_args=dict(
                market_type="market_type",
                anti_phishing_code="anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_margin_liquidation_warning(self, *args):
        params = dict(
            template_category='notice',
            template_type="margin_liquidation_warning",
            template_args=dict(
                market_type="market_type",
                risk_limit="test_risk_limit",
                anti_phishing_code="anti_phishing_code"
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_margin_renew_notice_email(self, *args):
        params = dict(
            template_category='notice',
            template_args=dict(
                coin_type="asset",
                expired_time="expired_time",
                anti_phishing_code="anti_phishing_code",
            ),
            recipient="test_email",
        )
        for template in (
            "margin_loan_order_expired",
            "margin_loan_order_force_flat",
            "margin_renew_failed"
        ):
            params["template_type"] = template
            self._assertRenderedSame(params)

    def test_render_mining_rewords(self, *args):
        params = dict(
            template_category='notice',
            template_type="mining_rewords",
            template_args=dict(
                name="email",
                amount="amount",
                asset="asset",
                activity_name="activity_name",
                show_spot="show_spot",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_perpetual_adl(self, *args):
        params = dict(
            template_category='notice',
            template_type="perpetual_adl",
            template_args=dict(
                market="market",
                amount="amount",
                price="price",
                deal_type="deal_type",
                anti_phishing_code="anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_perpetual_liquidation(self, *args):
        params = dict(
            template_category='notice',
            template_type="perpetual_liquidation",
            template_args=dict(
                market="market",
                side="side_type",
                leverage="leverage",
                amount="amount",
                direction="direction_type",
                liq_price="liq_price",
                sign_price="sign_price",
                bkr_price="bkr_price",
                anti_phishing_code="anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_perpetual_liquidation_warning(self, *args):
        params = dict(
            template_category='notice',
            template_type="perpetual_liquidation_warning",
            template_args=dict(
                market="market",
                risk="risk",
                anti_phishing_code="anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_perpetual_market_maker_level_appraisal(self, *args):
        params = dict(
            template_category='notice',
            template_type="perpetual_market_maker_level_appraisal",
            template_args=dict(
                name="user_name",
                user_trade_amount='trade_amount',
                month_count='month_count',
                support_url="support_url",
                new_level="new_level",
                limit_trade_amount="limit_trade_amount",
                result_status="result_status",
                perpetual_maker_fee_rate='maker_fee_rate',
                perpetual_taker_fee_rate='taker_fee_rate',
                cet_perpetual_maker_fee_rate=Decimal("0"),
                cet_perpetual_taker_fee_rate=Decimal("0"),
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_red_packet_register_notice(self, *args):
        params = dict(
            template_category='notice',
            template_type="red_packet_register_notice",
            template_args=dict(
                name="test_name",
                grab_time="xxxx-xx-xx",
                expired_time=f"xxxx-xx-xx",
                amount="test_amount",
                coin_type="CET",
                register_url="test_url",
                anti_phishing_code="anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_refer(self, *args):
        params = dict(
            template_category='notice',
            template_type="refer",
            template_args=dict(
                refer_name="refer_name",
                code="refer_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_refer_success_notice(self, *args):
        params = dict(
            template_category='notice',
            template_type="refer_success_notice",
            template_args=dict(
                refer_email="refer_email",
                time="xxxx-xx-xx",
                anti_phishing_code="two_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_send_coin_withdraw_notice(self, *args):
        params = dict(
            template_category='notice',
            template_type="send_coin_withdraw_notice",
            template_args=dict(
                name="test_name",
                amount="test_amount",
                create_time="xxxx-xx-xx",
                anti_phishing_code="anti_phishing_code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_sign_in(self, *args):
        params = dict(
            template_category='notice',
            template_type="sign_in",
            template_args=dict(
                create="test_create_time",
                ip="test_ip",
                location="test_location"
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_sign_in_fail(self, *args):
        params = dict(
            template_category='notice',
            template_type="sign_in_fail",
            template_args=dict(
                name="test_name",
                create_time="xxxx-xx-xx",
                site_url="https://www.coinex.com",
                support_url="test_support_url",
                anti_phishing_code="code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_sign_in_unusual(self, *args):
        params = dict(
            template_category='notice',
            template_type="sign_in_unusual",
            template_args=dict(
                name="test_name",
                create_time="xxxx-xx-xx",
                ip="ip",
                location="location",
                site_url="https://www.coinex.com/",
                support_url="test_support_url",
                anti_phishing_code="code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_sign_up_success(self, *args):
        params = dict(
            template_category='notice',
            template_type="sign_up_success",
            template_args=dict(
                name="test_name",
                anti_phishing_code="code",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_spot_market_maker_level_appraisal(self, *args):
        params = dict(
            template_category='notice',
            template_type="spot_market_maker_level_appraisal",
            template_args=dict(
                name="test_name",
                user_trade_amount="amount",
                month_count=0,
                support_url="support_url",
                new_level="level",
                limit_trade_amount="limit_trade_amount",
                result_status="result_status",
                cet_taker_fee_rate=1.2,
                cet_maker_fee_rate=2.2,

            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_sub_account_register_notice(self, *args):
        params = dict(
            template_category='notice',
            template_type="sub_account_register_notice",
            template_args=dict(
                start_time="xxxx-xx-xx",
                user_name="name",
                password="password",
                domain="domain",
                name="name",
                anti_phishing_code="code",

            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    # todo: subject text not exists
    # def test_render_user_trade_summary(self, *args):
    #     params = dict(
    #         template_category='notice',
    #         template_type="user_trade_summary",
    #         template_args=dict(
    #             report_date="xxxx-xx-xx",
    #             name="email",
    #             spot_user_trade_data={},
    #             spot_user_trade_type_data={},
    #             perpetual_user_trade_data={},
    #             perpetual_user_trade_type_data={},
    #         ),
    #         recipient="test_email",
    #     )
    #
    #     self._assertRenderedSame(params)

    def test_render_withdrawal_cancelled_notice(self, *args):
        params = dict(
            template_category='notice',
            template_type="withdrawal_cancelled_notice",
            template_args=dict(
                name="test_name",
                amount="amount",
                withdrawal_time_str="xxxx-xx-xx",
                asset_chain_str="xxx",
                address="address",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_withdrawal_expired_notice(self, *args):
        params = dict(
            template_category='notice',
            template_type="withdrawal_expired_notice",
            template_args=dict(
                name="test_name",
                amount="amount",
                withdrawal_time_str="xxxx-xx-xx",
                asset_chain_str="xxx",
                address="address",
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)

    def test_render_withdrawal_resumed_notice(self, *args):
        params = dict(
            template_category='notice',
            template_type="withdrawal_resumed_notice",
            template_args=dict(
                name="test_name",
                asset_chain_str="test_asset_chain_str",
                url="test_url"
            ),
            recipient="test_email",
        )

        self._assertRenderedSame(params)
