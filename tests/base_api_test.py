# -*- coding: utf-8 -*-
# only for python 3.7 above.
import datetime
import decimal
import json
from hashlib import sha256
import hmac
import time
from urllib.parse import urlencode, urlparse
from collections import namedtuple
from collections.abc import Iterable, Mapping
from typing import NamedTuple, Optional, Dict
from requests_futures.sessions import FuturesSession


class BaseTester(NamedTuple):
    url: str
    method: str = "GET"
    params: Optional[Dict] = None
    json: Optional[Dict] = None



class AccessIdConfig(NamedTuple):
    ENV_URL: str
    ACCESS_ID: str
    SECRET_KEY: str


class RequestFactory(object):

    def __init__(self,
                 future_session: FuturesSession,
                 tester: BaseTester,
                 access_config: AccessIdConfig):
        self.session = future_session
        self.tester = tester
        self.access_config = access_config
        self.access_id = self.access_config.ACCESS_ID
        self.tonce = self.get_tonce()

    def get_sign(self):
        method = self.tester.method
        qs = urlencode(self.tester.params or {})
        parsed_url = urlparse(self.url)
        path = parsed_url.path
        build_path = f'{path}?{qs}' if qs else path
        body = json.dumps(self.tester.json) if self.tester.json else ''
        build_str = f'{method}{build_path}{body}{self.tonce}'
        # return sha256(f"{build_str}{self.access_config.SECRET_KEY}".encode()).hexdigest().upper()
        hmac_sign = hmac.new(
                self.access_config.SECRET_KEY.encode(),
                msg=build_str.encode(),
                digestmod=sha256
            ).hexdigest().upper()
        return hmac_sign

    @property
    def url(self):
        return f'{self.access_config.ENV_URL}{self.tester.url}'

    @classmethod
    def get_tonce(cls):
        return int(time.time() * 1000)

    def build_request(self):
        headers = {
            'X-COINEX-SIGN': self.get_sign(),
            'X-COINEX-KEY': self.access_config.ACCESS_ID,
            'X-COINEX-TIMESTAMP': str(self.tonce),
        }
        resp = self.session.request(
            self.tester.method,
            url=self.url,
            headers=headers,
            params=self.tester.params,
            json=self.tester.json)
        return resp

