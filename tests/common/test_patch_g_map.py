import random
import time

import pytest
from flask import g, current_app
from gevent import monkey

from app.models.user import User
from app.utils import g_map
from t_common import default_lang

from mock_redis import patch_redis
from patch_g_map import patch_g_map

monkey.patch_all()
USER_ID = 1428


@pytest.fixture(scope='class')
def class_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('class_setup')
@pytest.mark.usefixtures('patch_redis')
class TestGMap:

    def add(self):
        from app.caches.operation import RiskUserNoticeCache
        cache = RiskUserNoticeCache()
        c = cache.get()
        c = str(int(c) + 1)
        cache.set(c)

    def test_g_map_normal(self, tcontext):
        with tcontext:
            from app.caches.operation import RiskUserNoticeCache
            cache = RiskUserNoticeCache()
            count = cache.get()

            def inner(n):
                self.add()

            g_map(inner, range(10000), size=100)

            assert int(cache.get()) < int(count) + 100000

    @pytest.mark.usefixtures('patch_g_map')
    def test_patch_g_map(self, tcontext):
        with tcontext:
            from app.caches.operation import RiskUserNoticeCache
            cache = RiskUserNoticeCache()
            count = cache.get()

            def inner(n):
                self.add()

            g_map(inner, range(100000), size=100)

            assert int(cache.get()) == int(count) + 100000
