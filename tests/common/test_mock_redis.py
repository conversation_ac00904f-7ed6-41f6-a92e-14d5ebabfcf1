import json
from datetime import date
from time import sleep, time

import pytest
from flask import g, current_app

from app.models.user import User
from t_common import default_lang

from mock_redis import patch_redis

USER_ID = 1428

@pytest.fixture(scope='class')
def class_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('class_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.skip
class TestMockRedis:

    def test_init(self, tcontext):
        pass
    
    def test_base(self, tcontext):
        with tcontext:
            from app.caches.operation import RiskUserNoticeCache

            cache = RiskUserNoticeCache()

            assert cache.exists()
            new_ttl = 5

            # test ttl
            cache.expire(new_ttl)
            assert cache.exists()
            sleep(new_ttl)
            assert not cache.exists()
            cache.redis.write_keys.remove(cache._key)

            cache.expireat(int(time()) + new_ttl)
            assert cache.exists()
            sleep(new_ttl)
            assert not cache.exists()
            cache.redis.write_keys.remove(cache._key)

            # test delete
            cache.delete()
            assert not cache.exists()
            assert cache.redis.origin_redis.exists(cache._key)
            assert not cache.redis.fake_redis.exists(cache._key)
            cache.redis.write_keys.remove(cache._key)

    def test_string(self, tcontext):
        with tcontext:
            from app.caches.operation import RiskUserNoticeCache

            cache = RiskUserNoticeCache()

            origin_data = cache.get()
            new_data = "haha"

            # test incr
            cache.incr(1)
            assert cache.get() == str(int(origin_data) + 1)

            # test set and get
            cache.set(new_data)
            assert cache.get() == new_data
            assert cache.redis.origin_redis.get(cache._key) == origin_data.encode()
            assert cache.redis.fake_redis.get(cache._key) != origin_data.encode()
            assert cache.redis.origin_redis.get(cache._key) != new_data.encode()
            assert cache.redis.fake_redis.get(cache._key) == new_data.encode()

    def test_bits(self, tcontext):
        with tcontext:
            from app.caches.report import DailyIncomeCache

            cache = DailyIncomeCache(date(2023, 8, 1))

            data_offset = 0
            origin_data = cache.get_bit(data_offset)
            new_data = not origin_data
            origin_count = cache.bit_count()

            # test get_bit and set_bit
            cache.set_bit(data_offset, new_data)
            assert cache.get_bit(data_offset) == new_data
            assert cache.redis.origin_redis.getbit(cache._key, data_offset) == origin_data
            assert cache.redis.fake_redis.getbit(cache._key, data_offset) != origin_data
            assert cache.redis.origin_redis.getbit(cache._key, data_offset) != new_data
            assert cache.redis.fake_redis.getbit(cache._key, data_offset) == new_data

            # test bit_count
            cache.set_bit(data_offset + 10, new_data)
            assert cache.bit_count() == origin_count + (-1 if origin_data else 1)

    def test_hash(self, tcontext):
        with tcontext:
            from app.caches import AssetUSDPricesCache

            cache = AssetUSDPricesCache()

            origin_len = cache.hlen()
            origin_data = cache.hgetall()
            origin_keys = cache.hkeys()
            first_origin_key = origin_keys[0]
            first_origin_value = cache.hget(first_origin_key)
            new_key, new_value = "haha", "hahaha"

            # test exists
            assert cache.hexists(first_origin_key)
            assert not cache.hexists(new_key)

            # test set exists key
            cache.hset(first_origin_key, new_value)
            assert cache.hget(first_origin_key) == new_value
            assert cache.redis.origin_redis.hget(cache._key, first_origin_key) == first_origin_value.encode()
            assert cache.redis.fake_redis.hget(cache._key, first_origin_key) == new_value.encode()
            assert cache.redis.origin_redis.hget(cache._key, first_origin_key) != new_value.encode()
            assert cache.redis.fake_redis.hget(cache._key, first_origin_key) != first_origin_value.encode()

            # test set not exists key
            assert cache.hget(new_key) is None

            cache.hset(new_key, new_value)
            assert cache.hget(new_key) == new_value
            assert cache.redis.origin_redis.hget(cache._key, new_key) is None
            assert cache.redis.fake_redis.hget(cache._key, new_key) == new_value.encode()
            assert cache.redis.fake_redis.hget(cache._key, new_key) is not None
            assert cache.hlen() == origin_len + 1

            # test hmget and hmset
            datas = cache.hmget([first_origin_key, new_key])
            for d in datas:
                assert d == new_value

            new_value *= 2
            cache.hmset(mapping={first_origin_key: first_origin_value, new_key: new_value})
            datas = cache.hmget([first_origin_key, new_key])
            assert datas[0] == first_origin_value
            assert datas[1] == new_value
            assert cache.hlen() == origin_len + 1
            assert cache.redis.origin_redis.hget(cache._key, new_key) is None
            assert cache.redis.origin_redis.hget(cache._key, first_origin_key) == first_origin_value.encode()

            # test hdel
            cache.hdel(first_origin_key)
            assert cache.hlen() == origin_len
            assert cache.redis.origin_redis.hget(cache._key, first_origin_key) == first_origin_value.encode()
            assert cache.redis.fake_redis.hget(cache._key, first_origin_key) is None

            # test hincr
            cache.hset(new_key, "0")
            cache.hincrby(new_key, 5)
            assert cache.hget(new_key) == "5"

    def test_set(self, tcontext):
        with tcontext:
            from app.caches.spot import OnlineMarketAssetCache

            cache = OnlineMarketAssetCache()

            origin_data = cache.smembers()

            new_data = "haha"
            first_origin_data = next(iter(origin_data))

            # test data copy and sadd and read
            cache.sadd(new_data)

            assert new_data in cache.smembers()
            assert new_data.encode() in cache.redis.fake_redis.smembers(cache._key)
            assert new_data.encode() not in cache.redis.origin_redis.smembers(cache._key)
            assert 4 == cache.scard()

            # test sismember
            assert cache.sismember(new_data)

            # test srem
            cache.srem(first_origin_data)
            assert first_origin_data not in cache.smembers()
            assert first_origin_data.encode() not in cache.redis.fake_redis.smembers(cache._key)
            assert first_origin_data.encode() in cache.redis.origin_redis.smembers(cache._key)
            assert 3 == cache.scard()

    def test_list(self, tcontext):
        with tcontext:
            from app.caches.operation import AnnouncementCache

            cache = AnnouncementCache(str(default_lang))
            origin_len = cache.llen()
            origin_data = cache.lrange(0, -1)

            # test push and pop
            cache.rpush("1", "2", "3")
            assert cache.llen() == origin_len + 3
            assert cache.redis.origin_redis.llen(cache._key) == origin_len
            assert cache.redis.fake_redis.llen(cache._key) == origin_len + 3

            cache.lpop()
            assert cache.llen() == origin_len + 2
            assert cache.redis.origin_redis.llen(cache._key) == origin_len
            assert cache.redis.fake_redis.llen(cache._key) == origin_len + 2

    def test_hyperloglog(self, tcontext):
        with tcontext:
            from app.caches.flow_control import EmailCountryCountCache

            cache = EmailCountryCountCache("其他", 1688169600)
            origin_count = cache.pfcount()

            # test pfadd
            cache.pfadd("haha", "asddd")
            assert cache.redis.origin_redis.pfcount(cache._key) == origin_count
            assert cache.pfcount() == origin_count + 2


