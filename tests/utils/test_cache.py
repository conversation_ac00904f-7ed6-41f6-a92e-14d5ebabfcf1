from gevent import monkey
monkey.patch_all()

import random
import time

import pytest
from flask import g, current_app

from app.utils import g_map, now
from app.utils.cache import BucketTTLCache


@pytest.fixture(scope='class')
def class_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = 'zh_Hant_HK'
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('class_setup')
class TestBucketTTLCache:
    test_key_perfix = "test_"

    def get_cache_key(self, value):
        return self.test_key_perfix + str(value)

    def test_bucket_ttl_cache_normal(self, tcontext):
        value = 1
        # 测试读写
        cache = BucketTTLCache(bucket_size=5, interval=2)
        cache.set(self.get_cache_key(value), value)

        assert cache.get(self.get_cache_key(value)) == value

        # 测试超时淘汰
        cache = BucketTTLCache(bucket_size=5, interval=2)
        for value in range(12):
            cache.set(self.get_cache_key(value + 1), value + 1)
            time.sleep(1)

        for dead_value in range(2):
            assert cache.get(self.get_cache_key(dead_value + 1)) is None

        for alive_value in range(9):
            assert cache.get(self.get_cache_key(alive_value + 4)) == alive_value + 4

        # 测试超时未淘汰
        cache = BucketTTLCache(bucket_size=5, interval=2)
        cache.set(self.get_cache_key(1), 1)
        time.sleep(5)
        cache.set(self.get_cache_key(2), 2)
        time.sleep(5)
        cache.set(self.get_cache_key(3), 3)
        time.sleep(2)
        cache.set(self.get_cache_key(4), 4)

        assert cache.get(self.get_cache_key(1)) == 1
        assert cache.get(self.get_cache_key(2)) == 2
        assert cache.get(self.get_cache_key(3)) == 3
        assert cache.get(self.get_cache_key(4)) == 4

        time.sleep(2)
        cache.set(self.get_cache_key(4), 4)
        time.sleep(2)
        cache.set(self.get_cache_key(4), 4)

        assert cache.get(self.get_cache_key(1)) is None
        assert cache.get(self.get_cache_key(2)) == 2
        assert cache.get(self.get_cache_key(3)) == 3
        assert cache.get(self.get_cache_key(4)) == 4

    def test_bucket_ttl_cache_concurrent(self, tcontext):
        with tcontext:

            # init test data
            test_data = {}
            for i in range(10000):
                test_data[i] = random.random()

            # 并发写后并发读
            cache = BucketTTLCache(bucket_size=5, interval=10)

            def write(n):
                cache.set(self.get_cache_key(n), n)

            def read(n):
                assert cache.get(self.get_cache_key(n)) == n

            print("time before {}".format(now()))
            g_map(write, range(100000), size=100)
            g_map(read, range(100000), size=100)
            print("time after {}".format(now()))

            # 同时并发读写
            cache = BucketTTLCache(bucket_size=5, interval=10)

            def write_and_read(n):
                if (n // 10) % 2:
                    assert cache.get(self.get_cache_key(n - 10)) == n - 10
                else:
                    cache.set(self.get_cache_key(n), n)

            print("time before {}".format(now()))
            g_map(write_and_read, range(200000), size=10, ordered=True)
            print("time after {}".format(now()))
