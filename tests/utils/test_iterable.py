#!/usr/bin/python
# -*- coding: utf-8 -*-
import queue
import time
import random
from time import sleep

import gevent
import pytest
from flask import g

from app.models.user import User
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang

USER_ID = 2

@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


def gevent_sleep(seq, sleep_time):
    print(f'    gevent_sleep:{seq} start')
    gevent.sleep(sleep_time)
    return f'{seq}:{sleep_time}'


def traditional_sleep(seq, sleep_time):
    print(f'    traditional_sleep:{seq} start')
    sleep(sleep_time)
    return f'{seq}:{sleep_time}'


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
class TestSpawn:
    @classmethod
    def spawn_with_sleep(cls, tcontext, sleep_fun):
        from app.utils import spawn_greenlet
        green_queue = queue.Queue()
        greenlets = []

        print(f'test "{sleep_fun.__name__}" start')
        with tcontext:
            time_start = time.time()
            print('greenlets run')
            for i in range(5):
                sleep_time = random.uniform(0.6, 1.5)
                greenlet = spawn_greenlet(sleep_fun, i, sleep_time)
                greenlet.link(green_queue.put)
                greenlets.append(greenlet)

            gevent.joinall(greenlets, timeout=2)
            print('all greenlets done, get return sequence:')
            for i in range(len(greenlets)):
                print(f'    {green_queue.get().value}')

            time_end = time.time()
            duration = time_end - time_start
            print(f'test "{sleep_fun.__name__}" end, duration: {duration}')
            return duration

    """测试创建greenlet执行方法"""
    def test_spawn_gevent_sleep(self, tcontext):
        """测试 gevent_sleep"""
        duration = self.spawn_with_sleep(tcontext, gevent_sleep)
        assert duration < 2

    def test_spawn_sleep(self, tcontext):
        """测试传统的sleep"""
        duration = self.spawn_with_sleep(tcontext, traditional_sleep)
        assert duration > 3


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
class TestGmap:
    pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestSpawn:
    @classmethod
    def one_func_sleep(cls, *args, **kwargs):
        gevent.sleep(0.5)
        print(f'one_func_sleep:{args[0]}')
        # from flask.globals import _cv_app
        # _app_ctx = _cv_app.get()
        # return _app_ctx
        from app.models import db
        return db.session

    """测试创建greenlet执行方法"""
    def test_spawn_sleep(self, tcontext):
        """测试获取费率-来自用户特殊费率"""
        from app.utils import spawn_greenlet
        # from gevent.monkey import patch_all
        # patch_all()

        with tcontext:
            time_start = time.time()
            greenlets = []

            for i in range(3):
                greenlets.append(spawn_greenlet(self.one_func_sleep, i))

            _ = gevent.joinall(greenlets, timeout=2)

            for greenlet in greenlets:
                print(greenlet.value)

            time_end = time.time()
            assert time_end - time_start < 1.5

