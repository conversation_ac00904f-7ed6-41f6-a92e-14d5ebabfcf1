from datetime import datetime, timed<PERSON><PERSON>
from pprint import pprint
from unittest.mock import patch

import pytest
from croniter import croniter
from flask import g
from pprint import pprint

from sqlalchemy.orm import load_only

from app import Language
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from app.utils.llm import PlatformLLM
from app.utils import current_timestamp


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestTranslation:
    def test_translation(self, tcontext):
        from app.schedules.translation import check_translation

        with tcontext:
            check_translation()

    def test_insight(self, tcontext):
        from app.schedules.translation import insight_translated

        with tcontext:
            insight_translated('670cd70f7afc0d950dd2eaf9')