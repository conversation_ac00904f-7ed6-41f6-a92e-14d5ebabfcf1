from datetime import datetime, <PERSON><PERSON><PERSON>
from pprint import pprint
from unittest.mock import patch

import pytest
from croniter import croniter
from flask import g
from pprint import pprint

from sqlalchemy.orm import load_only

from app import Language
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from app.utils.llm import PlatformLLM
from app.utils import current_timestamp


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
# @pytest.mark.usefixtures('patch_redis')
class TestInsightPageCache:
    def test_reload(self, tcontext):
        from app.caches.insight import InsightPageCache

        with tcontext:
            res = InsightPageCache.reload()
            pprint(res)

