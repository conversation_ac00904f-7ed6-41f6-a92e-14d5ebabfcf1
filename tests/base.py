# -*- coding: utf-8 -*-
import logging
import os
import unittest

from flask import current_app, Flask
# noinspection PyProtectedMember
from app import (_init_babel, _init_jinja, _init_config, _init_logging, _init_db)


def create_test_app() -> Flask:
    template_folder = os.path.join(os.getcwd(), "app/templates")
    translations_folder = os.path.join(os.getcwd(), "app/translations")
    app = Flask(__name__, template_folder=template_folder)
    app.config["BABEL_TRANSLATION_DIRECTORIES"] = translations_folder
    _init_logging()
    _init_config(app)
    _init_babel(app)
    _init_db(app)
    _init_jinja(app)
    return app


class TestMixin(object):

    app = None
    app_context = None

    def setUp(self):
        try:
            assert current_app.top
        except RuntimeError:
            self.app = create_test_app()
            pass
        except AttributeError:
            self.app = current_app

        self.app_context = self.app.app_context()
        self.app_context.push()

    def tearDown(self):
        self.app_context.pop()


class TestCase(TestMixin, unittest.TestCase):
    """ Base Test case with flask context """
    pass
