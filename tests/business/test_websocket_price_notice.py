import contextlib
import random
import sys
import time
from datetime import timedelta
from functools import wraps
from unittest.mock import patch

from app.models import MarketPriceNotice
from app.utils import today, now
from app.websocket.price_notice_util import build_notice_price_engine, get_price_push_list, build_notice_ttl_map, \
    update_notice_map_by_rule

MAX_NUM = 10000 * 10
MODEL = MarketPriceNotice


@contextlib.contextmanager
def calc_time(name):
    start = time.time()
    yield
    print(f"{name} use time {round(time.time() - start, 5)}")


def get_random_market():
    market = ['CETUSDT', 'CETUSDC', 'CETBCH', 'CETTUSD', 'USDTUSDC', 'USDTETH', 'USDCUSDT', 'BTCUSDT', 'BTCUSDC',
              'BTCBCH',
              'BTCTUSD', 'ETHUSDC', 'ETHBTC', 'ETHTUSD', 'ACMUSDT', 'ACMUSDC', 'ACMBTC', 'ACMETH', 'ACMBCH', 'ACMTUSD',
              'ADAUSDT', 'ADABTC', 'ADABCH', 'ADATUSD', 'ALGOUSDT', 'ARDRUSDT', 'ARDRUSDC', 'ARDRBTC', 'ARDRTUSD',
              'AYAUSDT', 'AYABTC', 'AYAETH', 'BABYDOGEUSDT', 'BANUSDC', 'BCHUSDT', 'BCHUSDC', 'BCHBTC', 'BCHTUSD',
              'BSVBTC',
              'BSVETH', 'BSVTUSD', 'BTMCET', 'BTMETH', 'BTMBCH', 'CHIUSDC', 'CHIBTC', 'CHIETH', 'CKBUSDT', 'CKBUSDC',
              'CKBBTC', 'CKBBCH', 'CKBTUSD', 'CMTBTC', 'CMTBCH', 'CTXCBTC', 'CTXCBCH', 'DASHBTC', 'DASHBCH', 'DCRUSDT',
              'DCRBTC', 'DCRETH', 'DCRBCH', 'DGTXBTC', 'DOGEBTC', 'DOGEBCH', 'EGTBCH', 'ELABTC', 'ELFUSDT', 'EMCBTC',
              'EOSUSDT', 'EOSUSDC', 'EOSBTC', 'EOSBCH', 'EOSTUSD', 'ETCUSDT', 'ETCBTC', 'ETCBCH', 'FCHUSDT', 'FCHBTC',
              'FIROUSDT', 'FIROBTC', 'FIROBCH', 'FIROTUSD', 'GNTUSDT', 'GNTBCH', 'HNSBTC', 'HOTUSDC', 'HYDROBTC',
              'HYDROBCH', 'ICXUSDT', 'ICXBTC', 'ICXETH', 'ICXBCH', 'KANUSDC', 'KANBTC', 'KANETH', 'KANBCH', 'KUNUSDT',
              'KUNBTC', 'KUNETH', 'KUNBCH', 'KUNTUSD', 'LBCUSDT', 'LBCBCH', 'LFTUSDT', 'LFTUSDC', 'LFTBTC', 'LFTETH',
              'LFTBCH', 'LFTTUSD', 'LOOMUSDC', 'LOOMBTC', 'LOOMBCH', 'LTCUSDT', 'LTCUSDC', 'LTCBTC', 'LTCBCH',
              'LTCTUSD',
              'LUNAUSDT', 'MDXUSDT', 'MTUSDT', 'MTBTC', 'MTETH', 'MTBCH', 'NEOBTC', 'NEOBCH', 'NMCBTC', 'NOBSUSDT',
              'NOBSUSDC', 'NOBSBTC', 'NOBSBCH', 'ONGTUSD', 'ONTBCH', 'QTUMUSDT', 'QTUMUSDC', 'QTUMBTC', 'QTUMETH',
              'QTUMBCH', 'RHOCUSDC', 'RHOCBTC', 'RHOCETH', 'RHOCBCH', 'RVNUSDT', 'RVNETH', 'RVNBCH', 'RVNTUSD', 'SCBTC',
              'SCBCH', 'SCTUSD', 'SEERUSDT', 'SEERUSDC', 'SEERBTC', 'SEERETH', 'SEERBCH', 'SEERTUSD', 'SNXUSDT',
              'SNXBTC',
              'SNXETH', 'SNXBCH', 'SOPBTC', 'SOPBCH', 'SYSUSDT', 'SYSUSDC', 'SYSBTC', 'SYSETH', 'SYSTUSD', 'TRXUSDT',
              'TRXUSDC', 'TRXBTC', 'TRXETH', 'TRXBCH', 'TUSDUSDT', 'WESTBTC', 'WINGSUSDT', 'WINGSBTC', 'WINGSBCH',
              'WOOUSDT', 'XMRUSDC', 'XMRBTC', 'XMRBCH', 'XRPUSDT', 'XRPUSDC', 'XRPBTC', 'XRPETH', 'XRPBCH', 'ZECUSDT',
              'ZECUSDC', 'ZECBTC', 'ZECBCH', 'ZENUSDT', 'ZENBTC', 'ZXX2USDT', 'ZXX2USDC', 'ZXX2BTC', 'ZXX2BCH']
    return market[random.randint(0, len(market) - 1)]


def build_fake_data(max_num=None):
    rule_type = [i for i in MODEL.RuleType]
    ttl_type = [i for i in MODEL.TTLType]
    market_data = []
    max_num = max_num or MAX_NUM
    for i in range(max_num):
        rule = rule_type[random.randint(0, len(rule_type) - 1)]
        ttl = ttl_type[random.randint(0, len(ttl_type) - 1)]
        if rule in [MODEL.RuleType.RATE_RISE, MODEL.RuleType.RATE_FALL]:
            value = random.randint(0, 100)
        else:
            value = random.randint(0, max_num)
        # valid = True if random.randint(0, 10) < 8 else False
        market_data.append(
            MODEL(
                id=random.randint(0, max_num),
                market=get_random_market(),
                trade_type=random.choice(list(MODEL.TradeType)),
                rule=rule,
                ttl_type=ttl,
                value=value,
            )
        )

    return market_data


class TestSortList:

    def test_build_notice_data_benchmark(self, tcontext):
        fake_data = build_fake_data()
        with calc_time(sys._getframe().f_code.co_name):
            data = build_notice_price_engine(fake_data)
            build_notice_ttl_map(fake_data)
        count = 0
        for rule, market_data_map in data.items():
            for market, data_list in market_data_map.items():
                count += len(data_list)
        assert count == len(fake_data)

    def test_sorted_irange_key_benchmark(self, tcontext):
        with tcontext:
            fake_data = build_fake_data()
            notice_data = build_notice_price_engine(fake_data)
            with patch("app.business.push.send_price_and_rate_alter_push") as mock_func:
                mock_func.return_value = None
                with calc_time(sys._getframe().f_code.co_name):
                    for i in range(1000):
                        market = get_random_market()
                        open_price, last_price, pre_price = random.randint(0, MAX_NUM), random.randint(0, MAX_NUM), random.randint(0, MAX_NUM)
                        get_price_push_list(market, open_price, last_price, pre_price, notice_data)

    def test_build_notice_data_time(self, tcontext):
        fake_data = build_fake_data()
        build_notice_price_engine(fake_data)
        build_notice_ttl_map(fake_data)

    def test_build_notice_ttl_map(self):
        _today = today()
        notices = [
            MODEL(
                id=1,
                ttl_type=MODEL.TTLType.DAILY,
                noticed_at=_today
            ),
            MODEL(
                id=2,
                ttl_type=MODEL.TTLType.ONCE,
                noticed_at=_today
            ),
            MODEL(
                id=3,
                ttl_type=MODEL.TTLType.DAILY,
                noticed_at=_today - timedelta(days=1)
            ),
        ]
        ttl_map = build_notice_ttl_map(notices)
        ret = {
            1: MODEL.TTLType.DAILY,
            2: MODEL.TTLType.ONCE,
            3: MODEL.TTLType.DAILY
        }
        assert ttl_map == ret

    def test_update_notice_map_by_rule(self):
        test_list = [
            [MODEL(
                id=1,
                ttl_type=MODEL.TTLType.DAILY,
                updated_at=now().replace(hour=20)
            )],
            [MODEL(
                id=2,
                ttl_type=MODEL.TTLType.ONCE,
            )]
        ]
        ret = [
            {
                1: MODEL.TTLType.DAILY
            },
            {
                2: MODEL.TTLType.ONCE
            },
        ]
        for idx, data in enumerate(test_list):
            ttl_map = update_notice_map_by_rule(data, {})
            assert ttl_map == ret[idx]

    def test_to_push_notice(self, tcontext):
        RuleType = MODEL.RuleType
        market = "CETUSDT"
        notices = [
            MODEL(
                id=1,
                market="CETUSDT",
                rule=RuleType.PRICE_RISE,
                value=100
            ),
            MODEL(
                id=2,
                market="CETUSDT",
                rule=RuleType.PRICE_FALL,
                value=1000
            ),
            MODEL(
                id=3,
                market="CETUSDT",
                rule=RuleType.RATE_RISE,
                value=10
            ),
            MODEL(
                id=4,
                market="CETUSDT",
                rule=RuleType.RATE_FALL,
                value=9
            ),
        ]
        test_data = [
            (99, 105, 100),
            (100, 109, 120),
            (50, 55, 50),
            (10, 9, 8),
        ]
        ret = [[(i.id, i.value)] for i in notices]
        with tcontext:
            notice_engine = build_notice_price_engine(notices)
            for idx, (open_price, last_price, pre_price) in enumerate(test_data):
                push_list = get_price_push_list(market, open_price, last_price, pre_price, notice_engine)
                assert push_list == ret[idx]

