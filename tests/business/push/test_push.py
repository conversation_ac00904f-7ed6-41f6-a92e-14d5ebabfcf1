from decimal import Decimal

from app.schedules.price_notice import get_market_level


class TestPush:

    def test_get_market_level(self):
        market_depth =dict(start=5000, end=200000, step=1000)
        test_data1 = [
            [
                1492358400,  # time
                "70000.00",  # open
                "80000.0",  # close
            ]
        ]
        ret1 = get_market_level(market_depth, test_data1)
        assert ret1 == 0

        test_data2 = [
            [
                1492358280,  # time
                "70000.00",  # open
                "81000.0",  # close
            ],
            [
                1492358400,  # time
                "70000.00",  # open
                "81500.0",  # close
            ]
        ]
        ret2 = get_market_level(market_depth, test_data2)
        assert ret2 == Decimal(0)

        test_data3 = [
            [
                1492358280,  # time
                "70000.00",  # open
                "80000.0",  # close
            ],
            [
                1492358340,  # time
                "70000.00",  # open
                "81000.0",  # close
            ],
            [
                1492358400,  # time
                "70000.00",  # open
                "81500.0",  # close
            ]
        ]
        ret3 = get_market_level(market_depth, test_data3)
        assert ret3 == Decimal(81000)

        test_data4 = [
            [
                1492358280,  # time
                "70000.00",  # open
                "82000.0",  # close
            ],
            [
                1492358340,  # time
                "70000.00",  # open
                "81500.0",  # close
            ],
            [
                1492358400,  # time
                "70000.00",  # open
                "81000.0",  # close
            ]
        ]
        ret4 = get_market_level(market_depth, test_data4)
        assert ret4 == Decimal(82000)



