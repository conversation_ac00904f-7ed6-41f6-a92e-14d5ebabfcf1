from datetime import datetime


class TestUtils(object):
    def test_check_value_unique(self):
        from app.business.mission_center.utils import MissionUtils
        data = [[], [1, 2, 3], [], [2, 3, 4]]
        result = MissionUtils.check_value_conflict(data)
        assert result is True
        data2 = [[], []]
        result2 = MissionUtils.check_value_conflict(data2)
        assert result2 is True
        data3 = [[], [1]]
        result3 = MissionUtils.check_value_conflict(data3)
        assert result3 is False
        data4 = [[1, 2], [1]]
        result4 = MissionUtils.check_value_conflict(data4)
        assert result4 is True
        data5 = [[1, 2], [3, 4]]
        result5 = MissionUtils.check_value_conflict(data5)
        assert result5 is False
        data6 = [[1, 2]]
        result6 = MissionUtils.check_value_conflict(data6)
        assert result6 is False
        data7 = [[1], [2], [1], [3]]
        result7 = MissionUtils.check_value_conflict(data7)
        assert result7 is True

    def test_check_intervals_has_overlap(self):
        from app.business.mission_center.utils import MissionUtils
        data = [
            (datetime(2025, 4, 10, 1), None),
            (datetime(2025, 4, 10, 1), datetime(2025, 4, 30, 1)),
            (datetime(2025, 5, 30, 1), None)
        ]
        result = MissionUtils.check_intervals_has_overlap(data)
        assert result is True
        data2 = [
            (datetime(2025, 4, 10, 1), datetime(2025, 4, 11, 1)),
            (datetime(2025, 4, 10, 1), datetime(2025, 4, 30, 1)),
            (datetime(2025, 5, 30, 1), None)
        ]
        result2 = MissionUtils.check_intervals_has_overlap(data2)
        assert result2 is True
        data3 = [
            (datetime(2025, 3, 10, 1), datetime(2025, 4, 10, 1)),
            (datetime(2025, 4, 10, 1), datetime(2025, 4, 30, 1)),
            (datetime(2025, 5, 30, 1), None)
        ]
        result3 = MissionUtils.check_intervals_has_overlap(data3)
        assert result3 is False
        data4 = [
            (datetime(2025, 3, 10, 1), datetime(2025, 4, 10, 2)),
            (datetime(2025, 4, 10, 1), datetime(2025, 4, 30, 1)),
            (datetime(2025, 5, 30, 1), datetime(2025, 5, 31, 1))
        ]
        result4 = MissionUtils.check_intervals_has_overlap(data4)
        assert result4 is True
        data5 = [
            (datetime(2025, 3, 10, 1), datetime(2025, 4, 9, 2)),
            (datetime(2025, 4, 10, 1), datetime(2025, 4, 30, 1)),
            (datetime(2025, 5, 30, 1), datetime(2025, 5, 31, 1))
        ]
        result5 = MissionUtils.check_intervals_has_overlap(data5)
        assert result5 is False
        data6 = [
            (datetime(2025, 3, 10, 1), None)
        ]
        result6 = MissionUtils.check_intervals_has_overlap(data6)
        assert result6 is False

        data7 = [
            (datetime(2025, 3, 10, 1), datetime(2025, 4, 30, 1))
        ]
        result7 = MissionUtils.check_intervals_has_overlap(data7)
        assert result7 is False

        data8 = [
            (None, None),
            (datetime(2025, 3, 10, 1), datetime(2025, 4, 30, 1))
        ]
        result8 = MissionUtils.check_intervals_has_overlap(data8)
        assert result8 is False
