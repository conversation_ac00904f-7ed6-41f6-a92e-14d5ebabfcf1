import pytest
from flask import g

from app import Language
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis

from app.models import User

USER_ID = 1260


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.auth_user = g.user = User.query.get(USER_ID)
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestMission(object):

    def test_get_user_mission_by_user_data(self, tcontext, module_setup):
        from app.business.mission_center.mission import UserMissionBiz, MissionBiz
        from app.business.mission_center.plan import MissionPlanBiz
        from app.models.mission_center import LogicTemplate
        with tcontext:
            user_mission = UserMissionBiz.get_user_missions(
                user_data={
                    LogicTemplate.CHANNEL_ID_EQ.name: MissionPlanBiz.POOL_CHANNEL
                }
            )
            print(f"矿池注册: {user_mission}")
            assert len(user_mission) == 1
            user_mission2 = UserMissionBiz.get_user_missions(
                user_data={
                    LogicTemplate.REFERER_ID_IN.name: 1260
                }
            )
            print(f"邀请注册: {user_mission2}")

    def test_get_user_mission_by_user_id(self, tcontext, module_setup):
        with tcontext:
            from app.business.mission_center.mission import UserMissionBiz, MissionBiz
            from app.business.mission_center.plan import MissionPlanBiz
            from app.models.mission_center import LogicTemplate
            user = User.query.get(1260)
            user_mission_data = UserMissionBiz.get_user_missions(user)
            print(user_mission_data)
