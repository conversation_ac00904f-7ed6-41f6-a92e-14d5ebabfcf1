#!/usr/bin/python
# -*- coding: utf-8 -*-
import json
from _decimal import Decimal

import pytest
from flask import g

from app.business.fee import FeeFetcher
from app.caches import MarketCache
from app.caches.user import UserSpecialFeeCache
from app.common import TradeType, TradeBusinessType
from app.models.user import User
from app.utils import current_timestamp
# from common import TradeBusinessType
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang

USER_ID = 2
MARKET = 'BTCUSDT'


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestUserFeeParser:

    @staticmethod
    def set_fee(market_fee, user_fee):
        """保存当前费率缓存，并使用给定的费率替代"""
        market_cache = MarketCache(MARKET)
        row = market_cache.object

        row.taker_fee_rate = market_fee[TradeType.TAKER]
        row.maker_fee_rate = market_fee[TradeType.MAKER]
        market_cache.object = row

        user_cache = UserSpecialFeeCache(USER_ID)
        taker_key = user_cache.compose_key(TradeType.TAKER, MARKET)
        maker_key = user_cache.compose_key(TradeType.MAKER, MARKET)

        user_cache_taker = user_cache.hget(taker_key)
        user_cache_maker = user_cache.hget(maker_key)
        expire_time = current_timestamp() + 86400

        user_cache.update(taker_key,
                          user_fee[TradeType.TAKER],
                          json.loads(user_cache_taker)['expired_time'] if user_cache_taker else expire_time
                          )
        user_cache.update(maker_key,
                          user_fee[TradeType.MAKER],
                          json.loads(user_cache_maker)['expired_time'] if user_cache_maker else expire_time
                          )

    def test_get_fee_user(self, tcontext):
        """测试获取费率-来自用户特殊费率"""
        with tcontext:
            # self.set_fee(
            #     market_fee={TradeType.TAKER: Decimal('0.002'), TradeType.MAKER: Decimal('0.002')},
            #     user_fee={TradeType.TAKER: Decimal('0.001'), TradeType.MAKER: Decimal('0.0015')},
            # )
            fetcher = FeeFetcher(USER_ID)
            result = fetcher.fetch_business_type_display(TradeBusinessType.SPOT)
            assert result[TradeType.TAKER] == Decimal('0.001')
            assert result[TradeType.MAKER] == Decimal('0.0015')


