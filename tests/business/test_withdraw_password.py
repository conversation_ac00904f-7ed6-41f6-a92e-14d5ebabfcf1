import pytest
from flask import g

from app import Language
from app.models import User

USER_ID = 20073


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.auth_user = g.user = User.query.get(USER_ID)
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
class TestWithdrawPassword:

    def test_report_local_deposit_event(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = "/res/user/withdraw-password/reset/token"
            resp = client.get(url)
            print(resp.json)
            assert resp.json["code"] == 0
