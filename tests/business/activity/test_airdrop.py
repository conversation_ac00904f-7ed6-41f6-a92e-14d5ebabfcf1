import json
from datetime import timedelta, datetime
from decimal import Decimal
from pprint import pprint

import pytest
from flask import g

from app import Language
from app.caches.activity import AirdropActivityDetailCache, AirdropActivityCache
from app.models import User, ActivityCondition, AirdropActivityCondition
from app.schedules.activity import get_active_total_trade_map, update_airdrop_statistic_schedule
from app.utils import today_datetime

USER_ID = 20073


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.auth_user = g.user = User.query.get(USER_ID)
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
class TestApi:
    pass


@pytest.mark.usefixtures('module_setup')
class TestFunc:

    def test_get_active_total_trade_map(self, tcontext):
        with tcontext:
            _type = ActivityCondition.TradeType
            trade_type_range = [i.name for i in _type]
            et = today_datetime()
            st = et - timedelta(days=10)
            ret = get_active_total_trade_map(trade_type_range, st, et)
            print(ret)

    def test_get_trade_value(self, tcontext):
        from app.api.frontend.activity import ActivityUserConditionMixin

        with tcontext:
            _type = ActivityCondition.TradeType
            trade_type_range = [i.name for i in _type]
            days = 10
            ret = ActivityUserConditionMixin.get_trade_value(USER_ID, days, trade_type_range)
            print(ret)

    def test_ActivityUserConditionMixin(self, tcontext):
        from app.api.frontend.activity import AirdropActivityConditionMixin

        with tcontext:
            activity_id = 185
            user_id = 10000391
            conditions_query = {
                i.key: i.value for i in AirdropActivityCondition.query.filter(
                    AirdropActivityCondition.airdrop_activity_id == activity_id
                ).all()
            }

            info = AirdropActivityConditionMixin.get_condition_info(user_id, activity_id, conditions_query)
            pprint(info)

    def test_DiscountActivityConditionMixin(self, tcontext):
        from app.api.frontend.activity import DiscountActivityConditionMixin

        with tcontext:
            activity_id = 185
            user_id = 10000391
            conditions_query = {
                i.key: i.value for i in AirdropActivityCondition.query.filter(
                    AirdropActivityCondition.airdrop_activity_id == activity_id
                ).all()
            }

            info = DiscountActivityConditionMixin.get_condition_info(user_id, activity_id, conditions_query)
            pprint(info)

    def test_airdrop_reload(self, tcontext):
        with tcontext:
            AirdropActivityCache.reload()
            AirdropActivityDetailCache.reload()

    def test_update_airdrop_statistic_schedule(self, tcontext):
        with tcontext:
            update_airdrop_statistic_schedule()




@pytest.mark.usefixtures('module_setup')
class TestApi:

    def test_novice_api(self, tcontext):
        with tcontext:
            from app.caches.activity import NoviceActivityCache
            NoviceActivityCache.reload()
            from app.caches.activity import NoviceRiskUserCache
            NoviceRiskUserCache(555594).reload()
            client = tcontext.app.test_client()
            resp = client.get(
                "/res/activity/newbie-zone/activity"
            )
            print(resp.json)
            assert resp.json["code"] == 0

    def test_airdrop(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get(
                "/res/activity/airdrop/177"
            )
            resp_json = resp.json
            pprint(resp_json)
            assert 'TRADE_TYPE_RANGE' in resp.json["data"]["conditions"]
            assert resp.json["code"] == 0

    def test_airdrop_user_info(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get(
                "/res/activity/airdrop/user-info",
                query_string={'activity_id': 177}
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_get_spot_grid_trade_map(self, tcontext):
        from app.business.trade import get_spot_grid_trade_map
        from app.business.trade import get_user_spot_grid_trade_amount
        with tcontext:
            st = datetime(2024, 7, 27)
            et = datetime(2025, 1, 16)
            ret1 = get_spot_grid_trade_map([166, 161, 154, 144, 143], st, et)
            ret1_sum = sum(ret1.values())
            pprint(ret1_sum)

            ret2 = get_user_spot_grid_trade_amount(10000576, st, et)
            pprint(ret2)

            assert ret1_sum == ret2
