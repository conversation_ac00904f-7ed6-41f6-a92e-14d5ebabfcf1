import pytest
from flask import g

from app import Language
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from app.utils import current_timestamp
from undecorated import undecorated


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestKlineBacktestAgent:
    def test_single_batch(self, tcontext):
        from app.business.kline_ai.kline_backtest import KlineBacktestAgent
        from app.business.kline_ai.kline_backtest_constant import BACKTEST_WINDOW
        from app.models.kline_ai import KlineAnalyst, KlineAnalysisBatch, KlineAnalysis
        from app.models.base import db
        from sqlalchemy.orm import load_only
        from sqlalchemy.sql import func

        with (tcontext):
            analyst = KlineAnalyst.query.first()
            agent = KlineBacktestAgent(analyst)
            earlier_than = current_timestamp(to_int=True) - analyst.interval * BACKTEST_WINDOW
            recently_untested = KlineAnalysisBatch.get_recently_untested(
                analyst.id, earlier_than,options=[load_only('id','analyst_id', 'kline_time_end')]
            )
            for batch in recently_untested:
                count_query = db.session.query(
                    func.count(KlineAnalysis.id)
                ).filter(KlineAnalysis.batch_id==batch.id).scalar()
                if count_query == 0:
                    print(f'No analysis in batch {batch.id}')
                    db.session.delete(batch)
                    continue
                else:
                    print(f'Found one batch {batch.id}!!!!')
                    agent._backtest_one_batch(batch)
            db.session.commit()

    def test_single_analysis(self, tcontext):
        from app.business.kline_ai.kline_backtest import KlineBacktestAgent
        from app.models.kline_ai import KlineAnalyst, KlineAnalysis
        from app.models.base import db
        from app.common import TradeBusinessType

        with (tcontext):
            current = current_timestamp(to_int=True)
            analyst_id = 1
            analyst = KlineAnalyst.query.get(analyst_id)
            interval = analyst.interval
            kline_time_end = current - interval*3 - (current % analyst.interval)
            analysis = KlineAnalysis(
                batch_id=1,
                analyst_id=analyst_id,
                bp=85,
                market='btcusdt',
                business_type=TradeBusinessType.SPOT,
                kline_time_end=kline_time_end,
                interval=interval,
            )
            db.session.add(analysis)
            db.session.flush()
            result = KlineBacktestAgent._backtest_single_analysis(analysis, kline_time_end, current, interval)
            print(result)
            assert result

    def test_open_sim_trade(self, tcontext):
        from app.schedules.kline_ai import kline_open_sim_trade
        from app.models.kline_ai import KlineAnalyst, KlineAnalysis, KlineAnalysisSimTrade
        from app.models.base import db
        from app.common import TradeBusinessType

        with (tcontext):
            current = current_timestamp(to_int=True)
            analyst_id = 1
            analyst = KlineAnalyst.query.get(analyst_id)
            interval = analyst.interval
            kline_time_end = current - interval*3 - (current % analyst.interval)
            analysis = KlineAnalysis(
                batch_id=1,
                analyst_id=analyst_id,
                bp=85,
                market='btcusdt',
                business_type=TradeBusinessType.SPOT,
                kline_time_end=kline_time_end,
                interval=interval,
            )
            db.session.add(analysis)
            db.session.flush()
            result = kline_open_sim_trade(analysis.id)

            print(result)
            assert isinstance(result, KlineAnalysisSimTrade)
            return result

    def test_close_sim_trade(self, tcontext):
        from app.schedules.kline_ai import kline_close_sim_trade
        from app.models.kline_ai import KlineAnalysisSimTrade
        from decimal import Decimal

        with (tcontext):
            sim_trade = self.test_open_sim_trade(tcontext)
            sim_trade.open_price = str(Decimal(sim_trade.open_price) * Decimal(0.98))
            result = kline_close_sim_trade(sim_trade.id)
            print(
                f"Sim trade closed: ({sim_trade.id}, "
                f"price:{Decimal(sim_trade.open_price):.2f}-{sim_trade.close_price:.2f}, "
                f"amount:{sim_trade.open_amount:.2f}-{sim_trade.close_amount:.2f}), "
                f"point: {sim_trade.open_point}-{sim_trade.close_point}"
            )
            assert isinstance(result, KlineAnalysisSimTrade)
