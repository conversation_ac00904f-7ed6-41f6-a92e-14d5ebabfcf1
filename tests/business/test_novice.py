import datetime
from datetime import <PERSON><PERSON><PERSON>
from unittest.mock import patch, <PERSON><PERSON>, MagicMock

import pytest
from dateutil.tz import tzutc
from flask import g

from app import Language
from app.utils import now
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from app.models import db, LoginRelationHistory, User
from app.models.activity import NovicePrefectureActivity


USER_ID = 10001001
NOVICE_ID = 555618


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
class TestNovice:

    def test_novice_package_trans(self, tcontext):
        from app.business.user_group import add_condition_content
        condition_list = [
            {"condition": [{'key': 'PACKAGE_REGISTER', 'value': ['YES']}]},
            {"condition": [{'key': 'PACKAGE_FIRST_OPERATE', 'value': ['STRATEGY']}]},

            {"condition": [{'key': 'PACKAGE_FIRST_OPERATE', 'value': ['SPOT']},
                           {'key': 'PACKAGE_ACTIVATE_DAY', 'value': [15]}]},
            {"condition": [{'key': 'PACKAGE_FIRST_OPERATE', 'value': ['ASSET_SPOT']},
                           {'key': 'PACKAGE_REGISTER_DAY', 'value': [15]}]},

            {"condition": [{'key': 'PACKAGE_DEPOSIT_AMOUNT', 'value': ['GE', 100]},
                           {'key': 'PACKAGE_ACTIVATE_DAY', 'value': [15]}]},
            {"condition": [{'key': 'PACKAGE_ASSET_SPOT_AMOUNT', 'value': ['GE', 100]},
                           {'key': 'PACKAGE_REGISTER_DAY', 'value': [15]}]},

            {"condition": [{'key': 'PACKAGE_PERPETUAL_AMOUNT', 'value': ['GE', 1000]}]},
            {"condition": [{'key': 'PACKAGE_FIRST_OPERATE', 'value': ['STRATEGY']},
                           {'key': 'PACKAGE_PERPETUAL_AMOUNT', 'value': ['GE', 1000]}]},

            {"condition": [{'key': 'PACKAGE_ASSET_SPOT_AMOUNT', 'value': ['GE', 100]},
                           {'key': 'PACKAGE_FIRST_OPERATE', 'value': ['SPOT']},
                           {'key': 'PACKAGE_ACTIVATE_DAY', 'value': [15]}]},

            {"condition": [{'key': 'PACKAGE_PERPETUAL_AMOUNT', 'value': ['GE', 100]},
                           {'key': 'PACKAGE_FIRST_OPERATE', 'value': ['PERPETUAL']},
                           {'key': 'PACKAGE_REGISTER_DAY', 'value': [15]}]},
        ]
        ret_list = [
            "完成注册即可领取奖励",
            "完成首次策略交易即可领取奖励",

            "活动上线15天内，完成首次现货交易，即可领取奖励",
            "注册15天内，完成首次币币交易，即可领取奖励",

            "活动上线15天内，累计充值金额 >= 100 USDT，即可领取奖励",
            "注册15天内，累计币币交易额 >= 100 USDT，即可领取奖励",

            "累计合约交易额 >= 1000 USDT，即可领取奖励",
            "完成首次策略交易，且累计合约交易额 >= 1000 USDT，即可领取奖励",

            "活动上线15天内，完成首次现货交易，且累计币币交易额 >= 100 USDT，即可领取奖励",
            "注册15天内，完成首次合约交易，且累计合约交易额 >= 100 USDT，即可领取奖励",
        ]
        for idx, condition in enumerate(condition_list):
            ret = add_condition_content(condition)
            assert ret == ret_list[idx]

    def insert_test_data(self, tcontext):
        test_novice_id = 169
        novice_model = NovicePrefectureActivity
        st = datetime.datetime(2099, 12, 1, tzinfo=tzutc())
        novice = novice_model(
            **{
                "id": test_novice_id,
                "start_at": "2099-01-01 00:00:00.000000",
                "end_at": "2099-12-29 00:00:00.000000",
                "user_group_condition": '[{"key": "PACKAGE_REGISTRATION_TIME", "value": ["EQ", 4070880000000, 4100688000000]}]',
                "created_user_id": 123,
                "activity_type": novice_model.ActivityType.PACKAGE,
            }
        )
        db.session_add_and_commit(novice)

        test_data_list = [
            {"id": 50000, "user_id": 23888, "device_id": "10000001", "created_at": st - timedelta(days=1)},
            {"id": 50001, "user_id": 23889, "device_id": "10000001", "created_at": st},
            {"id": 50002, "user_id": 23890, "device_id": "10000003", "created_at": st - timedelta(days=1)},
        ]
        login_model = LoginRelationHistory
        user_model = User
        for test_data in test_data_list:
            user_id = test_data["user_id"]
            user_row = user_model(
                id=user_id,
                name="test",
                user_type=user_model.UserType.NORMAL,
                created_at=test_data["created_at"],
            )
            db.session.add(user_row)
        db.session.commit()

        for test_data in test_data_list:
            user_id = test_data["user_id"]
            login_row = login_model(
                login_history_id=test_data["id"],
                user_id=user_id,
                is_registration=True,
                device_id=test_data["device_id"],
                created_at=test_data["created_at"],
            )
            db.session.add(login_row)
        db.session.commit()
        return test_novice_id, test_data_list

    def test_novice_risk_user_cache_reload(self, tcontext):
        from app.caches.activity import NoviceRiskUserCache
        with tcontext:
            test_novice_id, test_data_list = self.insert_test_data(tcontext)
            ret = NoviceRiskUserCache(test_novice_id).reload()
            assert ret == test_data_list[1]["user_id"]

    def test_get_novice_risk_user(self, tcontext):
        from app.caches.activity import NoviceRiskUserCache
        from app.business.activity.novice import get_novice_risk_user
        with tcontext:
            test_novice_id, test_data_list = self.insert_test_data(tcontext)
            NoviceRiskUserCache(test_novice_id).reload()
            expect_item = test_data_list[1]
            expect = ([
                {
                    "id": 1,
                    "user_id": expect_item["user_id"],
                    "email": None,
                    "device_id": expect_item["device_id"],
                    "risk_type": "相同设备",
                    "status": "生效中",
                    "risk_time": expect_item["created_at"],
                }
            ], 1)
            test_data = [
                {"args": (test_novice_id, ), "expect": expect},
                {"args": (test_novice_id, expect_item["user_id"]), "expect": expect},
                {"args": (test_novice_id, 132432), "expect": ([], 0)},
                {"args": (test_novice_id, None, 1, 5), "expect": expect},
            ]
            for data in test_data:
                ret = get_novice_risk_user(*data["args"])
                assert ret == data["expect"]

    def test_check_new_device_user(self, tcontext):
        from app.business.risk_control.user import check_new_device_user
        with tcontext:
            test_novice_id, test_data_list = self.insert_test_data(tcontext)
            test_data = [
                {"args": (test_data_list[1]["user_id"],), "expect": False},
                {"args": (test_data_list[2]["user_id"],), "expect": True},
            ]
            for data in test_data:
                ret = check_new_device_user(*data["args"])
                assert ret == data["expect"]

    def test_get_first_p2p_map(self, tcontext):
        with tcontext:
            from app.business.report.novice import NoviceStatisticBase
            user_ids = [USER_ID]
            ret = NoviceStatisticBase(NOVICE_ID).get_first_p2p_map(user_ids)
            print(ret)

    def test_first_deposit(self, tcontext):
        with tcontext:
            from app.business.report.novice import NoviceStatisticBase
            user_ids = [USER_ID]
            st = datetime.datetime(2022, 1, 1, tzinfo=tzutc())
            et = now()
            ret = NoviceStatisticBase(NOVICE_ID).first_deposit(set(user_ids), st, et)
            print(ret)

    def test_get_p2p_trade_map(self, tcontext):
        with tcontext:
            from app.business.trade import get_p2p_trade_map
            user_ids = [USER_ID]
            st = datetime.datetime(2024, 1, 1, tzinfo=tzutc())
            et = now()
            ret = get_p2p_trade_map(user_ids, st, et)
            print(ret)

    def test_Novice_Package_User_Cache(self, tcontext):
        with tcontext:
            from app.caches.activity import NovicePackageUserCache
            from app.caches.activity import NoviceRiskUserCache
            # NoviceRiskUserCache(NOVICE_ID).reload()
            NovicePackageUserCache(NOVICE_ID).reload()
            ret = NovicePackageUserCache(NOVICE_ID).read_one_user(USER_ID)
            print(ret)

    def test_novice_statistic(self, tcontext):
        with tcontext:
            from app.business.report.novice import NoviceTaskConditionReportStatistic
            from app.business.report.novice import NoviceConvertReportStatistic
            from app.business.report.novice import NoviceUserReportStatistic
            from app.business.report.novice import NoviceChannelReportStatistic
            NoviceTaskConditionReportStatistic(NOVICE_ID).gen_report()
            NoviceConvertReportStatistic(NOVICE_ID).gen_report()
            NoviceUserReportStatistic(NOVICE_ID).gen_report()
            NoviceChannelReportStatistic(NOVICE_ID).gen_report()

    def test_novice_first_deposit(self, tcontext):
        with tcontext:
            from app.business.user_group import UserGroupValidator, PackageOperateType
            g = UserGroupValidator(
                USER_ID,
                [],
            )
            ret = g._UserGroupValidator__first_deposit()
            assert ret == True

