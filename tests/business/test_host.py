import pytest


class TestHost:

    def test_replace_host(self, tcontext):
        from app.api.common.decorators import _replace_host_url

        with tcontext:
            host = "https://127.0.0.1:5000"
            source = {
                "jump_url": f"{host}/zh-hans/activity/apply?type=1",
            }
            new_host = "https://coinex.com/"
            _replace_host_url(source, host, new_host)
            assert source['jump_url'] == f"{new_host}zh-hans/activity/apply?type=1"
