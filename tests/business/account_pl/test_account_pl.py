#!/usr/bin/python
# -*- coding: utf-8 -*-
from collections import defaultdict
from _decimal import Decimal
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from pprint import pprint
from app.common.constants import AccountBalanceType, BalanceBusiness
from app.models.referral import ReferralAssetHistory
from app.schedules.profit_loss import update_user_realtime_pl_task
from app.utils.date_ import date_to_datetime, today
import pytest

from flask import g

from app.models.user import User
from app.utils import current_timestamp
# from common import TradeBusinessType
from tests.common.t_common import default_lang

USER_ID = 20639



@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestUserFeeParser:

    def test_profit_loss_referral(self, tcontext):
        """测试现货账户实时盈亏"""
        from app.business.external_dbs import TradeHistoryDB
        from app.business.account_pl import RealtimeAccountProfitLossProcessor, _query_balance_history_by_time_range, _get_account_type

        with tcontext:
            refer_businesses = (
                BalanceBusiness.REFERRAL,
                BalanceBusiness.MAKER_CASH_BACK,
                BalanceBusiness.BROKER_REFERRAL,
                BalanceBusiness.NORMAL_REFERRAL,
                BalanceBusiness.AMBASSADOR_REFERRAL,
                BalanceBusiness.BUS_USER_REFER)
            
            today_ = today()
            referral_history = ReferralAssetHistory.query.filter(
                ReferralAssetHistory.created_at >= date_to_datetime(today_),
            ).with_entities(
                ReferralAssetHistory.user_id,
                ReferralAssetHistory.asset,
                ReferralAssetHistory.amount,
            ).all()
            assert referral_history, '没有推荐返佣记录'

            start_ts = date_to_datetime(today_).timestamp()
            end_ts = current_timestamp(to_int=True)

            user_referral_map = defaultdict(lambda: defaultdict(Decimal))
            for r in referral_history:
                user_referral_map[r.user_id][r.asset] += r.amount

            refer_user_ids = {r.user_id for r in referral_history}
            server_referral_map = defaultdict(lambda: defaultdict(Decimal))
            for user_id in refer_user_ids:
                records = _query_balance_history_by_time_range(TradeHistoryDB, user_id, start_ts, end_ts)
                records = [r for r in records if _get_account_type(r['account'], r['business']) == AccountBalanceType.SPOT]
                
                refer_records = [r for r in records if BalanceBusiness(r['business']) in refer_businesses]
                for item in refer_records:
                    server_referral_map[item['user_id']][item['asset']] += item['change']
            
            for id_, asset_map in user_referral_map.items():
                for asset, amount in asset_map.items():
                    server_amount = server_referral_map[id_][asset]
                    assert amount == server_amount, f'用户{id_}的{asset}的推荐返佣金额不一致，数据库为{amount}，server为{server_amount}'

            for user_id in refer_user_ids:
                spot_result = RealtimeAccountProfitLossProcessor.get_spot_data(user_id, start_ts, end_ts)
                pprint('>>>>>>>>', user_id)
                pprint(spot_result)
                pprint('>>>>>>>>')

                update_user_realtime_pl_task(user_id, AccountBalanceType.SPOT.name)
