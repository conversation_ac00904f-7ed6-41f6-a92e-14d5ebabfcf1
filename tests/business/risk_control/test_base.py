import pytest
from flask import g

from app.models.user import User
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang

USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestBase:
    def test_format_risk_event_log(self, tcontext):
        from app.business.risk_control.base import format_risk_event_log
        with tcontext:
            format_risk_event_log(6161)

    def test_enum_in(self, tcontext):
        from app.models.risk_control import RiskUser, RiskEventLog
        with tcontext:
            reason = RiskUser.Reason.MARKET_VOLATILITY
            reason = RiskEventLog.trans_reason(reason)
            assert reason in [RiskEventLog.Reason.MARKET_VOLATILITY,
                              RiskEventLog.Reason.PERPETUAL_MARKET_VOLATILITY,
                              RiskEventLog.Reason.MARGIN_LIQUIDATION,
                              RiskEventLog.Reason.PERPETUAL_LIQUIDATION,
                              ]
