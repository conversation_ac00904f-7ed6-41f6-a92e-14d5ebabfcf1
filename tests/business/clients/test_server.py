#!/usr/bin/python
# -*- coding: utf-8 -*-
from datetime import date

import pytest
from flask import g

from app.models.user import User
from app.schedules.amm import make_liquidity_slice, amm_market_daily_report
# from common import TradeBusinessType
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang

USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestServerClient:
    def test_trade_net_rank(self, tcontext):
        from app.business.clients.server import ServerClient
        with tcontext:
            client = ServerClient()
            result = client.trade_net_rank(market=['ETHUSDT'],
                                           start_time=1699924680,
                                           end_time=1699951620)
            assert isinstance(result, dict)

    def test_market_kline(self, tcontext):
        from app.business.clients.server import ServerClient
        with tcontext:
            client = ServerClient()
            result = client.market_kline(market='BTCUSDT', start_time=1722813859, end_time=1722843859, interval=900)
            assert isinstance(result, dict)
