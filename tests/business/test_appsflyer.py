import datetime

import pytest
from dateutil.relativedelta import relativedelta
from flask import g

from app import Language
from app.models import User
from app.schedules.reports.appsflyer import report_local_deposit_event, report_chain_deposit_event, \
    report_p2p_buy_event, report_trade_event, report_trade_fee_event
from app.utils import today, next_month

USER_ID = 20073


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.auth_user = g.user = User.query.get(USER_ID)
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
class TestAppsflyer:

    def test_report_local_deposit_event(self, tcontext):
        with tcontext:
            report_local_deposit_event()
            report_chain_deposit_event()
            report_p2p_buy_event()
            report_trade_event()
            report_trade_fee_event()

    def test_af_id(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.post(
                "/res/app/appsflyer",
                json={"af_id": "123", "register_af_id": "123"},
                headers={"PLATFORM": "iOS"}
            )
            print(resp.json)
            assert resp.json["code"] == 0

    def test_pull_event(self, tcontext):
        from app.business.appsflyer import AfPullEventManager
        with tcontext:
            AfPullEventManager.run()

    def test_update_spot_user_trade_summary_schedule(self, tcontext):
        from app.schedules.account import update_spot_user_trade_summary_schedule
        with tcontext:
            update_spot_user_trade_summary_schedule()

    def test_app_report(self, tcontext):
        with tcontext:
            from app.schedules.reports.user_report import UserAppPublicityReportHelper
            from app.schedules.reports.user_report import UserPublicityReportHelper
            base_st = datetime.date(2024, 9, 4)
            daily_st = base_st
            while True:
                if daily_st >= today():
                    break
                UserAppPublicityReportHelper(daily_st, UserPublicityReportHelper.ReportType.DAILY).create_report()
                daily_st += datetime.timedelta(days=1)

            weekly_st = base_st
            while True:
                if weekly_st >= today():
                    break
                UserAppPublicityReportHelper(weekly_st, UserPublicityReportHelper.ReportType.WEEKLY).create_report()
                weekly_st += datetime.timedelta(weeks=1)

            month_st = base_st
            while True:
                if month_st >= today():
                    break
                UserAppPublicityReportHelper(month_st, UserPublicityReportHelper.ReportType.MONTHLY).create_report()
                month_st = next_month(month_st.year, month_st.month)

            quar_st = base_st
            while True:
                if quar_st >= today():
                    break
                UserAppPublicityReportHelper(quar_st, UserPublicityReportHelper.ReportType.QUARTERLY).create_report()
                quar_st = datetime.date(quar_st.year, quar_st.month, 1) + relativedelta(months=3)

    def test_user_report(self, tcontext):
        with tcontext:
            from app.schedules.reports.user_report import UserPublicityReportHelper
            base_st = datetime.date(2024, 9, 25)
            et = today() + datetime.timedelta(days=1)
            daily_st = base_st
            while True:
                print(f"开始生成日报 {daily_st}")
                if daily_st >= et:
                    break
                UserPublicityReportHelper(daily_st, UserPublicityReportHelper.ReportType.DAILY).create_report()
                daily_st += datetime.timedelta(days=1)

            weekly_st = base_st
            while True:
                print(f"开始生成周报 {weekly_st}")
                if weekly_st >= et:
                    break
                UserPublicityReportHelper(weekly_st, UserPublicityReportHelper.ReportType.WEEKLY).create_report()
                weekly_st += datetime.timedelta(weeks=1)

            month_st = base_st
            while True:
                print(f"开始生成月报 {month_st}")
                if month_st >= et:
                    break
                UserPublicityReportHelper(month_st, UserPublicityReportHelper.ReportType.MONTHLY).create_report()
                month_st = next_month(month_st.year, month_st.month)

            quar_st = base_st
            while True:
                print(f"开始生成季报 {quar_st}")
                if quar_st >= et:
                    break
                UserPublicityReportHelper(quar_st, UserPublicityReportHelper.ReportType.QUARTERLY).create_report()
                quar_st = datetime.date(quar_st.year, quar_st.month, 1) + relativedelta(months=3)

    def test_funnel_report(self, tcontext):
        with tcontext:
            from app.schedules.reports.increase_user import UserFunnelReportHelper
            start_date = datetime.date(2024, 8, 8)
            while True:
                if start_date >= today():
                    return
                UserFunnelReportHelper(start_date).create_report()
                start_date += datetime.timedelta(days=1)

    def test_channel_statistics_detail(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get(
                "/admin/report/user/app-publicity-channel-statistics-detail",
                query_string={
                    "report_type": "DAILY",
                    "period": "HISTORY",
                    "page": 1,
                    "limit": 100
                },
            )
            print(resp.json)
            assert resp.json["code"] == 0

    def test_channel_statistics(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get(
                "/admin/report/user/app-publicity-channel-statistics",
                query_string={
                    "report_type": "DAILY",
                    "period": "HISTORY",
                    "sort_type": "register_user_count",
                    "report_date": "2024-07-19",
                    "page": 1,
                    "limit": 100
                },
            )
            print(resp.json)
            assert resp.json["code"] == 0
